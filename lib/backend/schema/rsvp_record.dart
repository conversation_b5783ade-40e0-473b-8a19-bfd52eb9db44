import 'dart:async';

import '/backend/schema/util/firestore_util.dart';
import '/backend/schema/util/schema_util.dart';

import 'index.dart';

class RsvpRecord extends FirestoreRecord {
  RsvpRecord._(
    DocumentReference reference,
    Map<String, dynamic> data,
  ) : super(reference, data) {
    _initializeFields();
  }

  // "eventAssociation" field.
  DocumentReference? _eventAssociation;
  DocumentReference? get eventAssociation => _eventAssociation;
  bool hasEventAssociation() => _eventAssociation != null;

  // "user" field.
  DocumentReference? _user;
  DocumentReference? get user => _user;
  bool hasUser() => _user != null;

  // "timeOfRSVP" field.
  DateTime? _timeOfRSVP;
  DateTime? get timeOfRSVP => _timeOfRSVP;
  bool hasTimeOfRSVP() => _timeOfRSVP != null;

  // "numberAttending" field.
  int? _numberAttending;
  int get numberAttending => _numberAttending ?? 0;
  bool hasNumberAttending() => _numberAttending != null;

  // "noteToOrganizer" field.
  String? _noteToOrganizer;
  String get noteToOrganizer => _noteToOrganizer ?? '';
  bool hasNoteToOrganizer() => _noteToOrganizer != null;

  void _initializeFields() {
    _eventAssociation = snapshotData['eventAssociation'] as DocumentReference?;
    _user = snapshotData['user'] as DocumentReference?;
    _timeOfRSVP = snapshotData['timeOfRSVP'] as DateTime?;
    _numberAttending = snapshotData['numberAttending'] as int?;
    _noteToOrganizer = snapshotData['noteToOrganizer'] as String?;
  }

  static CollectionReference get collection => FirebaseFirestore.instance.collection('rsvp');

  static Stream<RsvpRecord> getDocument(DocumentReference ref) =>
      ref.snapshots().map((s) => RsvpRecord.fromSnapshot(s));

  static Future<RsvpRecord> getDocumentOnce(DocumentReference ref) => ref.get().then((s) => RsvpRecord.fromSnapshot(s));

  static RsvpRecord fromSnapshot(DocumentSnapshot snapshot) => RsvpRecord._(
        snapshot.reference,
        mapFromFirestore(snapshot.data() as Map<String, dynamic>),
      );

  static RsvpRecord getDocumentFromData(
    Map<String, dynamic> data,
    DocumentReference reference,
  ) =>
      RsvpRecord._(reference, mapFromFirestore(data));

  @override
  String toString() => 'RsvpRecord(reference: ${reference.path}, data: $snapshotData)';
}

Map<String, dynamic> createRsvpRecordData({
  DocumentReference? eventAssociation,
  DocumentReference? user,
  DateTime? timeOfRSVP,
  int? numberAttending,
  String? noteToOrganizer,
}) {
  final firestoreData = mapToFirestore(
    <String, dynamic>{
      'eventAssociation': eventAssociation,
      'user': user,
      'timeOfRSVP': timeOfRSVP,
      'numberAttending': numberAttending,
      'noteToOrganizer': noteToOrganizer,
    }.withoutNulls,
  );

  return firestoreData;
}

import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';

import 'VideoPlayerWidget.dart';

class CarouselWithIndicator extends StatefulWidget {
  const CarouselWithIndicator({Key? key, this.mediaList, this.width, this.height}) : super(key: key);
  final List<String>? mediaList;
  final double? width;
  final double? height;

  @override
  _CarouselWithIndicatorState createState() => _CarouselWithIndicatorState();
}

class _CarouselWithIndicatorState extends State<CarouselWithIndicator> {
  int _current = 0;
  final CarouselSliderController _controller = CarouselSliderController();

  Widget _buildMedia(String item, BuildContext context) {
    bool isVideo = RegExp(r'\.(mp4|mov)(\?|$)', caseSensitive: false).hasMatch(item);
    return GestureDetector(
      child: Container(
        width: widget.width ?? double.infinity,
        height: widget.height ?? double.infinity,
        child: isVideo
            ? VideoPlayerWidget(videoUrl: item)
            : CachedNetworkImage(
                imageUrl: item,
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
                errorWidget: (context, url, error) => Center(child: Icon(Icons.error)),
                placeholder: (context, url) => Center(
                  child: CircularProgressIndicator(),
                ),
              ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final List<Widget> mediaSliders = widget.mediaList!
        .map((item) => Container(
              margin: EdgeInsets.symmetric(horizontal: 5),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: _buildMedia(item, context),
              ),
            ))
        .toList();

    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Expanded(
            child: CarouselSlider(
              items: mediaSliders,
              carouselController: _controller,
              options: CarouselOptions(
                height: widget.height ?? 400.0,
                aspectRatio: 16 / 9,
                viewportFraction: 1,
                enlargeCenterPage: false,
                enableInfiniteScroll: widget.mediaList!.length > 1,
                onPageChanged: (index, reason) {
                  setState(() {
                    _current = index;
                  });
                },
              ),
            ),
          ),
          if (widget.mediaList!.length > 1)
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: widget.mediaList!.asMap().entries.map((entry) {
                return GestureDetector(
                  onTap: () => _controller.animateToPage(entry.key),
                  child: Container(
                    width: 12.0,
                    height: 12.0,
                    margin: EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: (_current == entry.key) ? Color(0xFF015E41) : Color(0xFF8A8A8A),
                    ),
                  ),
                );
              }).toList(),
            ),
        ],
      ),
    );
  }
}

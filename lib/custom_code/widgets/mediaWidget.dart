import 'package:flutter/material.dart';

import 'VideoPlayerWidget.dart';

Widget mediaWidget(List<String> urls, double width, double height) {
  if (urls.isEmpty) return SizedBox();

  final isVideo = urls.map((url) => RegExp(r'\.mp4($|\?)', caseSensitive: false).hasMatch(url)).toList();

  // If only one url, no need for a SingleChildScrollView
  if (urls.length == 1) {
    return mediaItem(urls[0], isVideo[0], width, height);
  }

  return SingleChildScrollView(
    scrollDirection: Axis.horizontal,
    child: Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: List.generate(
        urls.length,
        (index) => Padding(
          padding: EdgeInsets.symmetric(horizontal: 1.0),
          child: mediaItem(urls[index], isVideo[index], width, height),
        ),
      ),
    ),
  );
}

Widget mediaItem(String url, bool isVideo, double width, double height) {
  return Container(
    width: width,
    height: height,
    child: isVideo
        ? AspectRatio(
            aspectRatio: 16 / 9,
            child: VideoPlayerWidget(
              videoUrl: url,
            ),
          )
        : Image.network(
            url,
            fit: BoxFit.cover,
            width: width,
            height: height,
            errorBuilder: (context, error, stackTrace) => Icon(Icons.error),
          ),
  );
}

// Automatic FlutterFlow imports
// Imports other custom actions
// Imports custom functions
// Begin custom action code
// DO NOT REMOVE OR MODIFY THE CODE ABOVE!

/// Renames the given image URL to include a "_500x500" suffix before the file type suffix.
///
/// This function takes in an image URL as a string and returns a modified version of that URL
/// with a "_500x500" suffix inserted before the file type suffix. For example, if the input URL
/// is "https://example.com/image.jpg", the output URL will be "https://example.com/image_500x500.jpg"

Future<String> renameImageUrl(String? imageUrl) async {
  String s = imageUrl.toString();
  String suffix = s.substring(s.lastIndexOf('.')); // extract the file type suffix
  String renamedUrl = s.replaceFirst(suffix, '_500x500.png');
  return renamedUrl;
}

// Set your action name, define your arguments and return parameter,
// and then add the boilerplate code using the button on the right!

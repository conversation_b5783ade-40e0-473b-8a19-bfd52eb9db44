import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_linkify/flutter_linkify.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:litter_pic/pages/user/view_profile_page_other/view_profile_info_section.dart'
    show ViewProfileInfoSection;
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '/auth/firebase_auth/auth_util.dart';
import '/backend/backend.dart';
import '/components/cached_profile_image.dart';
import '/custom_code/widgets/index.dart' as custom_widgets;
import '/flutter_flow/custom_functions.dart' as functions;
import '/flutter_flow/flutter_flow_expanded_image_view.dart';
import '/flutter_flow/flutter_flow_icon_button.dart';
import '/flutter_flow/flutter_flow_theme.dart';
import '/flutter_flow/flutter_flow_toggle_icon.dart';
import '/flutter_flow/flutter_flow_util.dart';
import '/pages/post/post_details_new/post_details_new_widget.dart';
import '/pages/post/post_location_detail/post_location_detail_widget.dart';
import '/utils/analytics_service.dart';
import '../../../backend/cache/user_cache_manager.dart';
import '../followers_members_widget.dart';
import '../following_members_widget.dart';
import '../login/login_widget.dart';
import 'view_profile_page_other_model.dart';

export 'view_profile_page_other_model.dart';

class ViewProfilePageOtherWidget extends StatefulWidget {
  const ViewProfilePageOtherWidget({
    Key? key,
    this.userDetails,
    this.imgList,
  }) : super(key: key);

  final UsersRecord? userDetails;
  final List<String>? imgList;

  @override
  _ViewProfilePageOtherWidgetState createState() => _ViewProfilePageOtherWidgetState();
}

class _ViewProfilePageOtherWidgetState extends State<ViewProfilePageOtherWidget> {
  late ViewProfilePageOtherModel _model;
  late TextEditingController followersController;
  late TextEditingController followingController;
  final ScrollController _scrollController = ScrollController();

  final scaffoldKey = GlobalKey<ScaffoldState>();
  bool isFollowing = false;
  final analyticsService = AnalyticsService();

  @override
  void initState() {
    super.initState();
    UserCacheManager().init();
    _model = createModel(context, () => ViewProfilePageOtherModel());
    _model.userNameController ??= TextEditingController(text: widget.userDetails!.displayName);
    _model.organizationController ??= TextEditingController(text: widget.userDetails!.organization);
    followersController = TextEditingController();
    followingController = TextEditingController();
    _fetchFollowersCount();
    _fetchFollowingCount();

    _checkIfFollowing();
    analyticsService.logScreenView(screenName: 'ViewProfilePageOther', screenClass: 'ViewProfilePageOtherWidget');
  }

  String? _getPhotoUrl(UsersRecord userRecord) {
    final cached = UserCacheManager().getCachedPhotoUrl(userRecord.reference.id);
    if (cached != null) return cached;

    UserCacheManager().cacheUser(userRecord);
    return userRecord.photoUrl;
  }

  Future<void> _fetchFollowersCount() async {
    final count = await functions.numberOfFollowers(widget.userDetails!.uid);
    setState(() {
      followersController.text = count.toString();
    });
  }

  Future<void> _fetchFollowingCount() async {
    final count = await functions.numberFollowing(widget.userDetails!.uid);
    setState(() {
      followingController.text = count.toString();
    });
  }

  // Check if the current user is following this profile user
  Future<void> _checkIfFollowing() async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser != null) {
      final doc = await FirebaseFirestore.instance
          .collection('following')
          .doc(currentUser.uid)
          .collection('userFollowing')
          .doc(widget.userDetails!.reference.id)
          .get();
      setState(() {
        isFollowing = doc.exists;
      });
    }
  }

  @override
  void dispose() {
    _model.dispose();
    followersController.dispose();
    followingController.dispose();
    _scrollController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return StreamBuilder<UsersRecord>(
      stream: UsersRecord.getDocument(widget.userDetails!.reference),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return Center(
            child: SizedBox(
              width: 35.0,
              height: 35.0,
              child: SpinKitCircle(
                color: FlutterFlowTheme.of(context).primary,
                size: 35.0,
              ),
            ),
          );
        }
        final viewProfilePageOtherUsersRecord = snapshot.data!;
        return Scaffold(
          key: scaffoldKey,
          backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
          appBar: AppBar(
            backgroundColor: FlutterFlowTheme.of(context).secondaryBackground,
            iconTheme: IconThemeData(color: Color(0xFF015E41)),
            automaticallyImplyLeading: false,
            leading: Padding(
              padding: EdgeInsetsDirectional.fromSTEB(5.0, 5.0, 5.0, 5.0),
              child: FlutterFlowIconButton(
                borderColor: Colors.transparent,
                borderRadius: 30.0,
                borderWidth: 1.0,
                buttonSize: 60.0,
                fillColor: Color(0xFF015E41),
                icon: FaIcon(
                  FontAwesomeIcons.arrowLeft,
                  color: Colors.white,
                  size: 20.0,
                ),
                onPressed: () async {
                  Navigator.pop(context, isFollowing);
                },
              ),
            ),
            actions: [],
            centerTitle: false,
            elevation: 0.0,
          ),
          body: SafeArea(
            top: true,
            child: Column(
              children: [
                ViewProfileInfoSection(
                  userDetails: viewProfilePageOtherUsersRecord,
                  followers: followersController.text,
                  following: followingController.text,
                  onFollowersPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => FollowersMembersWidget(
                          userId: viewProfilePageOtherUsersRecord.reference.id,
                        ),
                      ),
                    );
                  },
                  onFollowingPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => FollowingMembersWidget(
                          userId: viewProfilePageOtherUsersRecord.reference.id,
                        ),
                      ),
                    );
                  },
                  isCurrentUser: currentUserUid == viewProfilePageOtherUsersRecord.uid,
                ),
                Expanded(
                  child: DefaultTabController(
                    length: 1,
                    initialIndex: 0,
                    child: Column(
                      children: [
                        Align(
                          alignment: Alignment(0.0, 0),
                          child: TabBar(
                            labelColor: Color(0xFF015E41),
                            unselectedLabelColor: FlutterFlowTheme.of(context).grayIcon,
                            labelStyle: FlutterFlowTheme.of(context).titleMedium.override(
                                  fontFamily: FlutterFlowTheme.of(context).titleMediumFamily,
                                  fontSize: 12.0,
                                  useGoogleFonts:
                                      GoogleFonts.asMap().containsKey(FlutterFlowTheme.of(context).titleMediumFamily),
                                ),
                            indicatorColor: FlutterFlowTheme.of(context).primary,
                            indicatorWeight: 2.0,
                            tabs: [
                              Tab(
                                text: '${valueOrDefault<String>(
                                  viewProfilePageOtherUsersRecord.displayName.trim(),
                                  'User',
                                )}\'s Posts',
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          child: TabBarView(
                            children: [
                              StreamBuilder<List<UserPostsRecord>>(
                                stream: queryUserPostsRecord(
                                  queryBuilder: (userPostsRecord) => userPostsRecord
                                      .where('postUser', isEqualTo: viewProfilePageOtherUsersRecord.reference)
                                      .orderBy('timePosted', descending: true),
                                ),
                                builder: (context, snapshot) {
                                  // Customize what your widget looks like when it's loading.
                                  if (!snapshot.hasData) {
                                    return Center(
                                      child: SizedBox(
                                        width: 35.0,
                                        height: 35.0,
                                        child: SpinKitCircle(
                                          color: FlutterFlowTheme.of(context).primary,
                                          size: 35.0,
                                        ),
                                      ),
                                    );
                                  }
                                  List<UserPostsRecord> socialFeedUserPostsRecordList = snapshot.data!;
                                  if (socialFeedUserPostsRecordList.isEmpty) {
                                    return Center(
                                      child: Image.asset(
                                        'assets/images/<EMAIL>',
                                        width: MediaQuery.of(context).size.width * 0.5,
                                        height: 400.0,
                                      ),
                                    );
                                  }
                                  return SingleChildScrollView(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.max,
                                      children: List.generate(socialFeedUserPostsRecordList.length, (socialFeedIndex) {
                                        final socialFeedUserPostsRecord =
                                            socialFeedUserPostsRecordList[socialFeedIndex];
                                        return Padding(
                                          padding: EdgeInsetsDirectional.fromSTEB(0.0, 4.0, 0.0, 8.0),
                                          child: Container(
                                            width: MediaQuery.of(context).size.width * 1.0,
                                            decoration: BoxDecoration(
                                              color: FlutterFlowTheme.of(context).tertiary,
                                              boxShadow: [
                                                BoxShadow(
                                                  blurRadius: 4.0,
                                                  color: Color(0x32000000),
                                                  offset: Offset(0.0, 2.0),
                                                )
                                              ],
                                              borderRadius: BorderRadius.circular(0.0),
                                            ),
                                            child: InkWell(
                                              splashColor: Colors.transparent,
                                              focusColor: Colors.transparent,
                                              hoverColor: Colors.transparent,
                                              highlightColor: Colors.transparent,
                                              onTap: () async {
                                                await Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                    builder: (context) => PostDetailsNewWidget(
                                                      userDocRef: viewProfilePageOtherUsersRecord.reference,
                                                      postDocRef: socialFeedUserPostsRecord.reference,
                                                      mediaList: socialFeedUserPostsRecord.postPhotos,
                                                    ),
                                                  ),
                                                );
                                              },
                                              child: Column(
                                                mainAxisSize: MainAxisSize.max,
                                                children: [
                                                  Padding(
                                                    padding: EdgeInsetsDirectional.fromSTEB(0.0, 8.0, 0.0, 4.0),
                                                    child: Row(
                                                      mainAxisSize: MainAxisSize.max,
                                                      mainAxisAlignment: MainAxisAlignment.center,
                                                      children: [
                                                        Padding(
                                                          padding: EdgeInsetsDirectional.fromSTEB(8.0, 0.0, 0.0, 0.0),
                                                          child: Card(
                                                            clipBehavior: Clip.antiAliasWithSaveLayer,
                                                            color: Color(0xFF4B39EF),
                                                            shape: RoundedRectangleBorder(
                                                              borderRadius: BorderRadius.circular(20.0),
                                                            ),
                                                            child: Padding(
                                                              padding: EdgeInsetsDirectional.fromSTEB(0, 0, 0, 0),
                                                              child: InkWell(
                                                                splashColor: Colors.transparent,
                                                                focusColor: Colors.transparent,
                                                                hoverColor: Colors.transparent,
                                                                highlightColor: Colors.transparent,
                                                                onTap: () async {
                                                                  await Navigator.push(
                                                                    context,
                                                                    PageTransition(
                                                                      type: PageTransitionType.fade,
                                                                      child: FlutterFlowExpandedImageView(
                                                                        image: CachedProfileImage(
                                                                          imageUrl: valueOrDefault<String>(
                                                                            _getPhotoUrl(
                                                                                viewProfilePageOtherUsersRecord),
                                                                            'https://w7.pngwing.com/pngs/505/761/png-transparent-login-computer-icons-avatar-icon-monochrome-black-silhouette.png',
                                                                          ),
                                                                          fit: BoxFit.cover,
                                                                          width: 200,
                                                                          height: 200,
                                                                        ),
                                                                        allowRotation: false,
                                                                        tag: viewProfilePageOtherUsersRecord.photoUrl,
                                                                        useHeroAnimation: true,
                                                                      ),
                                                                    ),
                                                                  );
                                                                },
                                                                child: Container(
                                                                  color: Colors.transparent,
                                                                  width: 40.0,
                                                                  height: 40.0,
                                                                  clipBehavior: Clip.none,
                                                                  child: CachedProfileImage(
                                                                    imageUrl: valueOrDefault<String>(
                                                                      _getPhotoUrl(viewProfilePageOtherUsersRecord),
                                                                      'https://w7.pngwing.com/pngs/505/761/png-transparent-login-computer-icons-avatar-icon-monochrome-black-silhouette.png',
                                                                    ),
                                                                    width: 40.0,
                                                                    height: 40.0,
                                                                    fit: BoxFit.cover,
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                        Expanded(
                                                          child: Padding(
                                                            padding: EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 8.0, 0.0),
                                                            child: SingleChildScrollView(
                                                              scrollDirection: Axis.horizontal,
                                                              child: Row(
                                                                mainAxisSize: MainAxisSize.max,
                                                                mainAxisAlignment: MainAxisAlignment.end,
                                                                children: [
                                                                  InkWell(
                                                                    splashColor: Colors.transparent,
                                                                    focusColor: Colors.transparent,
                                                                    hoverColor: Colors.transparent,
                                                                    highlightColor: Colors.transparent,
                                                                    onTap: () async {
                                                                      await Navigator.push(
                                                                        context,
                                                                        MaterialPageRoute(
                                                                          builder: (context) =>
                                                                              PostLocationDetailWidget(
                                                                            mapLocation:
                                                                                socialFeedUserPostsRecord.latLng,
                                                                          ),
                                                                        ),
                                                                      );
                                                                    },
                                                                    child: Text(
                                                                      socialFeedUserPostsRecord.location,
                                                                      maxLines: 3,
                                                                      style: FlutterFlowTheme.of(context)
                                                                          .bodyMedium
                                                                          .override(
                                                                            fontFamily: FlutterFlowTheme.of(context)
                                                                                .bodyMediumFamily,
                                                                            fontSize: 10.0,
                                                                            fontWeight: FontWeight.normal,
                                                                            useGoogleFonts: GoogleFonts.asMap()
                                                                                .containsKey(
                                                                                    FlutterFlowTheme.of(context)
                                                                                        .bodyMediumFamily),
                                                                          ),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  if (socialFeedUserPostsRecord.postPhotos.length >= 1)
                                                    Container(
                                                      width: 400.0,
                                                      height: 400.0,
                                                      child: custom_widgets.CarouselWithIndicator(
                                                        width: 400.0,
                                                        height: 400.0,
                                                        mediaList: socialFeedUserPostsRecord.postPhotos,
                                                      ),
                                                    ),
                                                  if (socialFeedUserPostsRecord.litterWeight > 0)
                                                    Row(
                                                      mainAxisSize: MainAxisSize.max,
                                                      mainAxisAlignment: MainAxisAlignment.center,
                                                      children: [
                                                        Padding(
                                                          padding: EdgeInsetsDirectional.fromSTEB(10.0, 0.0, 0.0, 0.0),
                                                          child: Text(
                                                            socialFeedUserPostsRecord.litterWeight == 1
                                                                ? '1 pound of litter picked up!'
                                                                : '${socialFeedUserPostsRecord.litterWeight} pounds of litter picked up!',
                                                            style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                  fontFamily:
                                                                      FlutterFlowTheme.of(context).bodyMediumFamily,
                                                                  color: FlutterFlowTheme.of(context).primaryDark,
                                                                  fontWeight: FontWeight.w800,
                                                                  useGoogleFonts: GoogleFonts.asMap().containsKey(
                                                                      FlutterFlowTheme.of(context).bodyMediumFamily),
                                                                ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  Row(
                                                    mainAxisSize: MainAxisSize.max,
                                                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                                    children: [
                                                      Row(
                                                        mainAxisSize: MainAxisSize.max,
                                                        children: [
                                                          Text(
                                                            valueOrDefault<String>(
                                                              functions.likes(socialFeedUserPostsRecord).toString(),
                                                              '0',
                                                            ),
                                                            style: FlutterFlowTheme.of(context).bodySmall.override(
                                                                  fontFamily: 'Noto Serif',
                                                                  color: Color(0xFF8B97A2),
                                                                  fontSize: 14.0,
                                                                  fontWeight: FontWeight.normal,
                                                                  useGoogleFonts: GoogleFonts.asMap().containsKey(
                                                                      FlutterFlowTheme.of(context).bodySmallFamily),
                                                                ),
                                                          ),
                                                          ToggleIcon(
                                                            onPressed: () async {
                                                              final user = FirebaseAuth.instance.currentUser;

                                                              if (user == null || user.isAnonymous) {
                                                                // Navigate to the login page
                                                                Navigator.push(
                                                                  context,
                                                                  MaterialPageRoute(
                                                                      builder: (context) => LoginWidget()),
                                                                );
                                                                return; // Exit the function early
                                                              }

                                                              final likesElement = currentUserReference;
                                                              final likesUpdate =
                                                                  socialFeedUserPostsRecord.likes.contains(likesElement)
                                                                      ? FieldValue.arrayRemove([likesElement])
                                                                      : FieldValue.arrayUnion([likesElement]);
                                                              final userPostsUpdateData = {
                                                                'likes': likesUpdate,
                                                              };
                                                              await socialFeedUserPostsRecord.reference
                                                                  .update(userPostsUpdateData);
                                                            },
                                                            value: socialFeedUserPostsRecord.likes
                                                                .contains(currentUserReference),
                                                            onIcon: FaIcon(
                                                              FontAwesomeIcons.solidHeart,
                                                              color: Color(0xFFFF0003),
                                                            ),
                                                            offIcon: FaIcon(
                                                              FontAwesomeIcons.heart,
                                                              color: FlutterFlowTheme.of(context).secondaryText,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      InkWell(
                                                        splashColor: Colors.transparent,
                                                        focusColor: Colors.transparent,
                                                        hoverColor: Colors.transparent,
                                                        highlightColor: Colors.transparent,
                                                        onTap: () async {
                                                          await Navigator.push(
                                                            context,
                                                            MaterialPageRoute(
                                                              builder: (context) => PostDetailsNewWidget(
                                                                postDocRef: socialFeedUserPostsRecord.reference,
                                                                userDocRef: viewProfilePageOtherUsersRecord.reference,
                                                                mediaList: socialFeedUserPostsRecord.postPhotos,
                                                              ),
                                                            ),
                                                          );
                                                        },
                                                        child: Row(
                                                          mainAxisSize: MainAxisSize.max,
                                                          children: [
                                                            Padding(
                                                              padding:
                                                                  EdgeInsetsDirectional.fromSTEB(0.0, 0.0, 6.0, 0.0),
                                                              child: Text(
                                                                socialFeedUserPostsRecord.numComments.toString(),
                                                                style: FlutterFlowTheme.of(context).bodySmall.override(
                                                                      fontFamily: 'Noto Serif',
                                                                      color: Color(0xFF8B97A2),
                                                                      fontSize: 14.0,
                                                                      fontWeight: FontWeight.normal,
                                                                      useGoogleFonts: GoogleFonts.asMap().containsKey(
                                                                          FlutterFlowTheme.of(context).bodySmallFamily),
                                                                    ),
                                                              ),
                                                            ),
                                                            Icon(
                                                              Icons.mode_comment_outlined,
                                                              color: FlutterFlowTheme.of(context).secondaryText,
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  Padding(
                                                    padding: EdgeInsetsDirectional.fromSTEB(20.0, 10.0, 20.0, 8.0),
                                                    child: Row(
                                                      mainAxisSize: MainAxisSize.max,
                                                      children: [
                                                        Expanded(
                                                          child: Linkify(
                                                            onOpen: (link) async {
                                                              final uri = Uri.parse(link.url);
                                                              if (await canLaunchUrl(uri)) {
                                                                await launchUrl(uri,
                                                                    mode: LaunchMode.externalApplication);
                                                              } else {
                                                                throw 'Could not launch $uri';
                                                              }
                                                            },
                                                            text: socialFeedUserPostsRecord.postDescription,
                                                            textAlign: TextAlign.start,
                                                            maxLines: null,
                                                            style: FlutterFlowTheme.of(context).bodyMedium.override(
                                                                  fontFamily:
                                                                      FlutterFlowTheme.of(context).bodyMediumFamily,
                                                                  fontSize: 12.0,
                                                                  useGoogleFonts: GoogleFonts.asMap().containsKey(
                                                                      FlutterFlowTheme.of(context).bodyMediumFamily),
                                                                ),
                                                            linkStyle: TextStyle(
                                                              color: Colors.blue,
                                                              decoration: TextDecoration.none,
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        );
                                      }),
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

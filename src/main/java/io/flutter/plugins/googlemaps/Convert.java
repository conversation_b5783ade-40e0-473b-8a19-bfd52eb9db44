// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

package io.flutter.plugins.googlemaps;

import static com.google.android.gms.maps.GoogleMap.MAP_TYPE_HYBRID;
import static com.google.android.gms.maps.GoogleMap.MAP_TYPE_NONE;
import static com.google.android.gms.maps.GoogleMap.MAP_TYPE_NORMAL;
import static com.google.android.gms.maps.GoogleMap.MAP_TYPE_SATELLITE;
import static com.google.android.gms.maps.GoogleMap.MAP_TYPE_TERRAIN;

import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Point;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.VisibleForTesting;
import com.google.android.gms.maps.CameraUpdate;
import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.MapsInitializer;
import com.google.android.gms.maps.model.BitmapDescriptor;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.google.android.gms.maps.model.ButtCap;
import com.google.android.gms.maps.model.CameraPosition;
import com.google.android.gms.maps.model.Cap;
import com.google.android.gms.maps.model.CustomCap;
import com.google.android.gms.maps.model.Dash;
import com.google.android.gms.maps.model.Dot;
import com.google.android.gms.maps.model.Gap;
import com.google.android.gms.maps.model.GroundOverlay;
import com.google.android.gms.maps.model.JointType;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.LatLngBounds;
import com.google.android.gms.maps.model.PatternItem;
import com.google.android.gms.maps.model.RoundCap;
import com.google.android.gms.maps.model.SquareCap;
import com.google.android.gms.maps.model.Tile;
import com.google.maps.android.clustering.Cluster;
import com.google.maps.android.heatmaps.Gradient;
import com.google.maps.android.heatmaps.WeightedLatLng;
import io.flutter.FlutterInjector;
import io.flutter.plugins.googlemaps.Messages.FlutterError;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/** Conversions between JSON-like values and GoogleMaps data types. */
class Convert {
  // These constants must match the corresponding constants in serialization.dart
  public static final String HEATMAP_ID_KEY = "heatmapId";
  public static final String HEATMAP_DATA_KEY = "data";
  public static final String HEATMAP_GRADIENT_KEY = "gradient";
  public static final String HEATMAP_MAX_INTENSITY_KEY = "maxIntensity";
  public static final String HEATMAP_OPACITY_KEY = "opacity";
  public static final String HEATMAP_RADIUS_KEY = "radius";
  public static final String HEATMAP_GRADIENT_COLORS_KEY = "colors";
  public static final String HEATMAP_GRADIENT_START_POINTS_KEY = "startPoints";
  public static final String HEATMAP_GRADIENT_COLOR_MAP_SIZE_KEY = "colorMapSize";

  private static BitmapDescriptor toBitmapDescriptor(
      Messages.PlatformBitmap platformBitmap, AssetManager assetManager, float density) {
    return toBitmapDescriptor(
        platformBitmap, assetManager, density, new BitmapDescriptorFactoryWrapper());
  }

  private static BitmapDescriptor toBitmapDescriptor(
      Messages.PlatformBitmap platformBitmap,
      AssetManager assetManager,
      float density,
      BitmapDescriptorFactoryWrapper wrapper) {
    Object bitmap = platformBitmap.getBitmap();
    if (bitmap instanceof Messages.PlatformBitmapDefaultMarker) {
      Messages.PlatformBitmapDefaultMarker typedBitmap =
          (Messages.PlatformBitmapDefaultMarker) bitmap;
      if (typedBitmap.getHue() == null) {
        return BitmapDescriptorFactory.defaultMarker();
      } else {
        final float hue = typedBitmap.getHue().floatValue();
        return BitmapDescriptorFactory.defaultMarker(hue);
      }
    }
    if (bitmap instanceof Messages.PlatformBitmapAsset) {
      Messages.PlatformBitmapAsset typedBitmap = (Messages.PlatformBitmapAsset) bitmap;
      final String assetPath = typedBitmap.getName();
      final String assetPackage = typedBitmap.getPkg();
      if (assetPackage == null) {
        return BitmapDescriptorFactory.fromAsset(
            FlutterInjector.instance().flutterLoader().getLookupKeyForAsset(assetPath));
      } else {
        return BitmapDescriptorFactory.fromAsset(
            FlutterInjector.instance()
                .flutterLoader()
                .getLookupKeyForAsset(assetPath, assetPackage));
      }
    }
    if (bitmap instanceof Messages.PlatformBitmapAssetImage) {
      Messages.PlatformBitmapAssetImage typedBitmap = (Messages.PlatformBitmapAssetImage) bitmap;
      final String assetImagePath = typedBitmap.getName();
      return BitmapDescriptorFactory.fromAsset(
          FlutterInjector.instance().flutterLoader().getLookupKeyForAsset(assetImagePath));
    }
    if (bitmap instanceof Messages.PlatformBitmapBytes) {
      Messages.PlatformBitmapBytes typedBitmap = (Messages.PlatformBitmapBytes) bitmap;
      return getBitmapFromBytesLegacy(typedBitmap);
    }
    if (bitmap instanceof Messages.PlatformBitmapAssetMap) {
      Messages.PlatformBitmapAssetMap typedBitmap = (Messages.PlatformBitmapAssetMap) bitmap;
      return getBitmapFromAsset(
          typedBitmap, assetManager, density, wrapper, new FlutterInjectorWrapper());
    }
    if (bitmap instanceof Messages.PlatformBitmapBytesMap) {
      Messages.PlatformBitmapBytesMap typedBitmap = (Messages.PlatformBitmapBytesMap) bitmap;
      return getBitmapFromBytes(typedBitmap, density, wrapper);
    }
    throw new IllegalArgumentException("PlatformBitmap did not contain a supported subtype.");
  }

  // Used for deprecated fromBytes bitmap descriptor.
  // Can be removed after support for "fromBytes" bitmap descriptor type is
  // removed.
  private static BitmapDescriptor getBitmapFromBytesLegacy(
      Messages.PlatformBitmapBytes bitmapBytes) {
    try {
      Bitmap bitmap = toBitmap(bitmapBytes.getByteData());
      return BitmapDescriptorFactory.fromBitmap(bitmap);
    } catch (Exception e) {
      throw new IllegalArgumentException("Unable to interpret bytes as a valid image.", e);
    }
  }

  /**
   * Creates a BitmapDescriptor object from bytes data.
   *
   * @param bytesMap a [PlatformBitmapBytesMap] containing the byte data from which to construct a
   *     [BitmapDescriptor] and a bitmap scaling mode. The optional `width` affects scaling when
   *     `height` is `null`, and the optional `height` affects scaling when `width` is `null.
   * @param density the density of the display, used to calculate pixel dimensions.
   * @param bitmapDescriptorFactory is an instance of the BitmapDescriptorFactoryWrapper.
   * @return BitmapDescriptor object from bytes data.
   * @throws IllegalArgumentException if any required keys are missing in `byteData` or if the byte
   *     data cannot be interpreted as a valid image.
   */
  @VisibleForTesting
  public static BitmapDescriptor getBitmapFromBytes(
      Messages.PlatformBitmapBytesMap bytesMap,
      float density,
      BitmapDescriptorFactoryWrapper bitmapDescriptorFactory) {
    try {
      Bitmap bitmap = toBitmap(bytesMap.getByteData());
      Messages.PlatformMapBitmapScaling scalingMode = bytesMap.getBitmapScaling();
      switch (scalingMode) {
        case AUTO:
          final Double width = bytesMap.getWidth();
          final Double height = bytesMap.getHeight();

          if (width != null || height != null) {
            int targetWidth = width != null ? toInt(width * density) : bitmap.getWidth();
            int targetHeight = height != null ? toInt(height * density) : bitmap.getHeight();

            if (width != null && height == null) {
              // If only width is provided, calculate height based on aspect ratio.
              double aspectRatio = (double) bitmap.getHeight() / bitmap.getWidth();
              targetHeight = (int) (targetWidth * aspectRatio);
            } else if (height != null && width == null) {
              // If only height is provided, calculate width based on aspect ratio.
              double aspectRatio = (double) bitmap.getWidth() / bitmap.getHeight();
              targetWidth = (int) (targetHeight * aspectRatio);
            }
            return bitmapDescriptorFactory.fromBitmap(
                toScaledBitmap(bitmap, targetWidth, targetHeight));
          } else {
            // Scale image using given scale ratio
            final float scale = density / bytesMap.getImagePixelRatio().floatValue();
            return bitmapDescriptorFactory.fromBitmap(toScaledBitmap(bitmap, scale));
          }
        case NONE:
          break;
      }
      return bitmapDescriptorFactory.fromBitmap(bitmap);
    } catch (Exception e) {
      throw new IllegalArgumentException("Unable to interpret bytes as a valid image.", e);
    }
  }

  /**
   * Creates a BitmapDescriptor object from asset, using given details and density.
   *
   * <p>This method processes an asset specified by name and applies scaling based on the provided
   * parameters. The `assetMap` object provides the asset name, bitmap scaling mode, and image pixel
   * ratio, and may optionally include 'width' and/or 'height' to explicitly set the dimensions of
   * the output image.
   *
   * @param assetMap a [PlatformBitmapAssetMap] containing the asset name from which to construct a
   *     [BitmapDescriptor] and a bitmap scaling mode. The optional `width` affects scaling when
   *     `height` is `null`, and the optional `height` affects scaling when `width` is `null.
   * @param assetManager assetManager An instance of Android's AssetManager, which provides access
   *     to any raw asset files stored in the application's assets directory.
   * @param density density the density of the display, used to calculate pixel dimensions.
   * @param bitmapDescriptorFactory is an instance of the BitmapDescriptorFactoryWrapper.
   * @param flutterInjector An instance of the FlutterInjectorWrapper class.
   * @return BitmapDescriptor object from asset.
   * @throws IllegalArgumentException if any required keys are missing in `assetDetails` or if the
   *     asset cannot be opened or processed as a valid image.
   */
  @VisibleForTesting
  public static BitmapDescriptor getBitmapFromAsset(
      Messages.PlatformBitmapAssetMap assetMap,
      AssetManager assetManager,
      float density,
      BitmapDescriptorFactoryWrapper bitmapDescriptorFactory,
      FlutterInjectorWrapper flutterInjector) {
    final String assetName = assetMap.getAssetName();
    final String assetKey = flutterInjector.getLookupKeyForAsset(assetName);

    Messages.PlatformMapBitmapScaling scalingMode = assetMap.getBitmapScaling();
    switch (scalingMode) {
      case AUTO:
        final Double width = assetMap.getWidth();
        final Double height = assetMap.getHeight();
        InputStream inputStream = null;
        try {
          inputStream = assetManager.open(assetKey);
          Bitmap bitmap = BitmapFactory.decodeStream(inputStream);

          if (width != null || height != null) {
            int targetWidth = width != null ? toInt(width * density) : bitmap.getWidth();
            int targetHeight = height != null ? toInt(height * density) : bitmap.getHeight();

            if (width != null && height == null) {
              // If only width is provided, calculate height based on aspect ratio.
              double aspectRatio = (double) bitmap.getHeight() / bitmap.getWidth();
              targetHeight = (int) (targetWidth * aspectRatio);
            } else if (height != null && width == null) {
              // If only height is provided, calculate width based on aspect ratio.
              double aspectRatio = (double) bitmap.getWidth() / bitmap.getHeight();
              targetWidth = (int) (targetHeight * aspectRatio);
            }
            return bitmapDescriptorFactory.fromBitmap(
                toScaledBitmap(bitmap, targetWidth, targetHeight));
          } else {
            // Scale image using given scale.
            final float scale = density / assetMap.getImagePixelRatio().floatValue();
            return bitmapDescriptorFactory.fromBitmap(toScaledBitmap(bitmap, scale));
          }
        } catch (Exception e) {
          throw new IllegalArgumentException("'asset' cannot open asset: " + assetName, e);
        } finally {
          if (inputStream != null) {
            try {
              inputStream.close();
            } catch (IOException e) {
              e.printStackTrace();
            }
          }
        }
      case NONE:
        break;
    }

    return bitmapDescriptorFactory.fromAsset(assetKey);
  }

  static @NonNull CameraPosition cameraPositionFromPigeon(
      @NonNull Messages.PlatformCameraPosition position) {
    final CameraPosition.Builder builder = CameraPosition.builder();
    builder.bearing(position.getBearing().floatValue());
    builder.target(latLngFromPigeon(position.getTarget()));
    builder.tilt(position.getTilt().floatValue());
    builder.zoom(position.getZoom().floatValue());
    return builder.build();
  }

  static CameraUpdate cameraUpdateFromPigeon(Messages.PlatformCameraUpdate update, float density) {
    Object cameraUpdate = update.getCameraUpdate();
    if (cameraUpdate instanceof Messages.PlatformCameraUpdateNewCameraPosition) {
      Messages.PlatformCameraUpdateNewCameraPosition newCameraPosition =
          (Messages.PlatformCameraUpdateNewCameraPosition) cameraUpdate;
      return CameraUpdateFactory.newCameraPosition(
          cameraPositionFromPigeon(newCameraPosition.getCameraPosition()));
    }
    if (cameraUpdate instanceof Messages.PlatformCameraUpdateNewLatLng) {
      Messages.PlatformCameraUpdateNewLatLng newLatLng =
          (Messages.PlatformCameraUpdateNewLatLng) cameraUpdate;
      return CameraUpdateFactory.newLatLng(latLngFromPigeon(newLatLng.getLatLng()));
    }
    if (cameraUpdate instanceof Messages.PlatformCameraUpdateNewLatLngZoom) {
      Messages.PlatformCameraUpdateNewLatLngZoom newLatLngZoom =
          (Messages.PlatformCameraUpdateNewLatLngZoom) cameraUpdate;
      return CameraUpdateFactory.newLatLngZoom(
          latLngFromPigeon(newLatLngZoom.getLatLng()), newLatLngZoom.getZoom().floatValue());
    }
    if (cameraUpdate instanceof Messages.PlatformCameraUpdateNewLatLngBounds) {
      Messages.PlatformCameraUpdateNewLatLngBounds newLatLngBounds =
          (Messages.PlatformCameraUpdateNewLatLngBounds) cameraUpdate;
      return CameraUpdateFactory.newLatLngBounds(
          latLngBoundsFromPigeon(newLatLngBounds.getBounds()),
          (int) (newLatLngBounds.getPadding() * density));
    }
    if (cameraUpdate instanceof Messages.PlatformCameraUpdateScrollBy) {
      Messages.PlatformCameraUpdateScrollBy scrollBy =
          (Messages.PlatformCameraUpdateScrollBy) cameraUpdate;
      return CameraUpdateFactory.scrollBy(
          scrollBy.getDx().floatValue() * density, scrollBy.getDy().floatValue() * density);
    }
    if (cameraUpdate instanceof Messages.PlatformCameraUpdateZoomBy) {
      Messages.PlatformCameraUpdateZoomBy zoomBy =
          (Messages.PlatformCameraUpdateZoomBy) cameraUpdate;
      final Point focus = pointFromPigeon(zoomBy.getFocus(), density);
      return (focus != null)
          ? CameraUpdateFactory.zoomBy(zoomBy.getAmount().floatValue(), focus)
          : CameraUpdateFactory.zoomBy(zoomBy.getAmount().floatValue());
    }
    if (cameraUpdate instanceof Messages.PlatformCameraUpdateZoomTo) {
      Messages.PlatformCameraUpdateZoomTo zoomTo =
          (Messages.PlatformCameraUpdateZoomTo) cameraUpdate;
      return CameraUpdateFactory.zoomTo(zoomTo.getZoom().floatValue());
    }
    if (cameraUpdate instanceof Messages.PlatformCameraUpdateZoom) {
      Messages.PlatformCameraUpdateZoom zoom = (Messages.PlatformCameraUpdateZoom) cameraUpdate;
      return (zoom.getOut()) ? CameraUpdateFactory.zoomOut() : CameraUpdateFactory.zoomIn();
    }
    throw new IllegalArgumentException(
        "PlatformCameraUpdate's cameraUpdate field must be one of the PlatformCameraUpdate... case classes.");
  }

  private static double toDouble(Object o) {
    return ((Number) o).doubleValue();
  }

  private static float toFloat(Object o) {
    return ((Number) o).floatValue();
  }

  private static @Nullable Float nullableDoubleToFloat(@Nullable Double d) {
    return (d == null) ? null : d.floatValue();
  }

  private static int toInt(Object o) {
    return ((Number) o).intValue();
  }

  static int toMapType(@NonNull Messages.PlatformMapType type) {
    switch (type) {
      case NONE:
        return MAP_TYPE_NONE;
      case NORMAL:
        return MAP_TYPE_NORMAL;
      case SATELLITE:
        return MAP_TYPE_SATELLITE;
      case TERRAIN:
        return MAP_TYPE_TERRAIN;
      case HYBRID:
        return MAP_TYPE_HYBRID;
    }
    return MAP_TYPE_NORMAL;
  }

  static @Nullable MapsInitializer.Renderer toMapRendererType(
      @Nullable Messages.PlatformRendererType type) {
    if (type == null) {
      return null;
    }
    switch (type) {
      case LATEST:
        return MapsInitializer.Renderer.LATEST;
      case LEGACY:
        return MapsInitializer.Renderer.LEGACY;
    }
    return null;
  }

  static @NonNull Messages.PlatformCameraPosition cameraPositionToPigeon(
      @NonNull CameraPosition position) {
    return new Messages.PlatformCameraPosition.Builder()
        .setBearing((double) position.bearing)
        .setTarget(latLngToPigeon(position.target))
        .setTilt((double) position.tilt)
        .setZoom((double) position.zoom)
        .build();
  }

  static Messages.PlatformLatLngBounds latLngBoundsToPigeon(LatLngBounds latLngBounds) {
    return new Messages.PlatformLatLngBounds.Builder()
        .setNortheast(latLngToPigeon(latLngBounds.northeast))
        .setSouthwest(latLngToPigeon(latLngBounds.southwest))
        .build();
  }

  static @NonNull LatLngBounds latLngBoundsFromPigeon(
      @NonNull Messages.PlatformLatLngBounds bounds) {
    return new LatLngBounds(
        latLngFromPigeon(bounds.getSouthwest()), latLngFromPigeon(bounds.getNortheast()));
  }

  static Messages.PlatformLatLng latLngToPigeon(LatLng latLng) {
    return new Messages.PlatformLatLng.Builder()
        .setLatitude(latLng.latitude)
        .setLongitude(latLng.longitude)
        .build();
  }

  static LatLng latLngFromPigeon(Messages.PlatformLatLng latLng) {
    return new LatLng(latLng.getLatitude(), latLng.getLongitude());
  }

  static Messages.PlatformCluster clusterToPigeon(
      String clusterManagerId, Cluster<MarkerBuilder> cluster) {
    int clusterSize = cluster.getSize();
    String[] markerIds = new String[clusterSize];
    MarkerBuilder[] markerBuilders = cluster.getItems().toArray(new MarkerBuilder[clusterSize]);

    LatLngBounds.Builder latLngBoundsBuilder = LatLngBounds.builder();
    for (int i = 0; i < clusterSize; i++) {
      MarkerBuilder markerBuilder = markerBuilders[i];
      latLngBoundsBuilder.include(markerBuilder.getPosition());
      markerIds[i] = markerBuilder.markerId();
    }

    return new Messages.PlatformCluster.Builder()
        .setClusterManagerId(clusterManagerId)
        .setPosition(latLngToPigeon(cluster.getPosition()))
        .setBounds(latLngBoundsToPigeon(latLngBoundsBuilder.build()))
        .setMarkerIds(Arrays.asList(markerIds))
        .build();
  }

  static LatLng toLatLng(Object o) {
    final List<?> data = toList(o);
    return new LatLng(toDouble(data.get(0)), toDouble(data.get(1)));
  }

  /**
   * Converts a list of serialized weighted lat/lng to a list of WeightedLatLng.
   *
   * @param o The serialized list of weighted lat/lng.
   * @return The list of WeightedLatLng.
   */
  static WeightedLatLng toWeightedLatLng(Object o) {
    final List<?> data = toList(o);
    return new WeightedLatLng(toLatLng(data.get(0)), toDouble(data.get(1)));
  }

  static Point pointFromPigeon(Messages.PlatformPoint point) {
    return new Point(point.getX().intValue(), point.getY().intValue());
  }

  @Nullable
  static Point pointFromPigeon(@Nullable Messages.PlatformDoublePair point, float density) {
    if (point == null) {
      return null;
    }
    return new Point((int) (point.getX() * density), (int) (point.getY() * density));
  }

  static Messages.PlatformPoint pointToPigeon(Point point) {
    return new Messages.PlatformPoint.Builder().setX((long) point.x).setY((long) point.y).build();
  }

  private static List<?> toList(Object o) {
    return (List<?>) o;
  }

  private static Map<?, ?> toMap(Object o) {
    return (Map<?, ?>) o;
  }

  private static Bitmap toBitmap(byte[] bmpData) {
    Bitmap bitmap = BitmapFactory.decodeByteArray(bmpData, 0, bmpData.length);
    if (bitmap == null) {
      throw new IllegalArgumentException("Unable to decode bytes as a valid bitmap.");
    } else {
      return bitmap;
    }
  }

  private static Bitmap toScaledBitmap(Bitmap bitmap, float scale) {
    // Threshold to check if scaling is necessary.
    final float scalingThreshold = 0.001f;

    if (Math.abs(scale - 1) > scalingThreshold && scale > 0) {
      final int newWidth = (int) (bitmap.getWidth() * scale);
      final int newHeight = (int) (bitmap.getHeight() * scale);
      return toScaledBitmap(bitmap, newWidth, newHeight);
    }
    return bitmap;
  }

  private static Bitmap toScaledBitmap(Bitmap bitmap, int width, int height) {
    if (width > 0 && height > 0 && (bitmap.getWidth() != width || bitmap.getHeight() != height)) {
      return Bitmap.createScaledBitmap(bitmap, width, height, true);
    }
    return bitmap;
  }

  static void interpretMapConfiguration(
      @NonNull Messages.PlatformMapConfiguration config, @NonNull GoogleMapOptionsSink sink) {
    final Messages.PlatformCameraTargetBounds cameraTargetBounds = config.getCameraTargetBounds();
    if (cameraTargetBounds != null) {
      final @Nullable Messages.PlatformLatLngBounds bounds = cameraTargetBounds.getBounds();
      sink.setCameraTargetBounds(bounds == null ? null : latLngBoundsFromPigeon(bounds));
    }
    final Boolean compassEnabled = config.getCompassEnabled();
    if (compassEnabled != null) {
      sink.setCompassEnabled(compassEnabled);
    }
    final Boolean mapToolbarEnabled = config.getMapToolbarEnabled();
    if (mapToolbarEnabled != null) {
      sink.setMapToolbarEnabled(mapToolbarEnabled);
    }
    final Messages.PlatformMapType mapType = config.getMapType();
    if (mapType != null) {
      sink.setMapType(toMapType(mapType));
    }
    final Messages.PlatformZoomRange minMaxZoomPreference = config.getMinMaxZoomPreference();
    if (minMaxZoomPreference != null) {
      sink.setMinMaxZoomPreference(
          nullableDoubleToFloat(minMaxZoomPreference.getMin()),
          nullableDoubleToFloat(minMaxZoomPreference.getMax()));
    }
    final Messages.PlatformEdgeInsets padding = config.getPadding();
    if (padding != null) {
      sink.setPadding(
          padding.getTop().floatValue(),
          padding.getLeft().floatValue(),
          padding.getBottom().floatValue(),
          padding.getRight().floatValue());
    }
    final Boolean rotateGesturesEnabled = config.getRotateGesturesEnabled();
    if (rotateGesturesEnabled != null) {
      sink.setRotateGesturesEnabled(rotateGesturesEnabled);
    }
    final Boolean scrollGesturesEnabled = config.getScrollGesturesEnabled();
    if (scrollGesturesEnabled != null) {
      sink.setScrollGesturesEnabled(scrollGesturesEnabled);
    }
    final Boolean tiltGesturesEnabled = config.getTiltGesturesEnabled();
    if (tiltGesturesEnabled != null) {
      sink.setTiltGesturesEnabled(tiltGesturesEnabled);
    }
    final Boolean trackCameraPosition = config.getTrackCameraPosition();
    if (trackCameraPosition != null) {
      sink.setTrackCameraPosition(trackCameraPosition);
    }
    final Boolean zoomGesturesEnabled = config.getZoomGesturesEnabled();
    if (zoomGesturesEnabled != null) {
      sink.setZoomGesturesEnabled(zoomGesturesEnabled);
    }
    final Boolean liteModeEnabled = config.getLiteModeEnabled();
    if (liteModeEnabled != null) {
      sink.setLiteModeEnabled(liteModeEnabled);
    }
    final Boolean myLocationEnabled = config.getMyLocationEnabled();
    if (myLocationEnabled != null) {
      sink.setMyLocationEnabled(myLocationEnabled);
    }
    final Boolean zoomControlsEnabled = config.getZoomControlsEnabled();
    if (zoomControlsEnabled != null) {
      sink.setZoomControlsEnabled(zoomControlsEnabled);
    }
    final Boolean myLocationButtonEnabled = config.getMyLocationButtonEnabled();
    if (myLocationButtonEnabled != null) {
      sink.setMyLocationButtonEnabled(myLocationButtonEnabled);
    }
    final Boolean indoorEnabled = config.getIndoorViewEnabled();
    if (indoorEnabled != null) {
      sink.setIndoorEnabled(indoorEnabled);
    }
    final Boolean trafficEnabled = config.getTrafficEnabled();
    if (trafficEnabled != null) {
      sink.setTrafficEnabled(trafficEnabled);
    }
    final Boolean buildingsEnabled = config.getBuildingsEnabled();
    if (buildingsEnabled != null) {
      sink.setBuildingsEnabled(buildingsEnabled);
    }
    final String style = config.getStyle();
    if (style != null) {
      sink.setMapStyle(style);
    }
  }

  /** Set the options in the given object to marker options sink. */
  static void interpretMarkerOptions(
      Messages.PlatformMarker marker,
      MarkerOptionsSink sink,
      AssetManager assetManager,
      float density,
      BitmapDescriptorFactoryWrapper wrapper) {
    sink.setAlpha(marker.getAlpha().floatValue());
    sink.setAnchor(marker.getAnchor().getX().floatValue(), marker.getAnchor().getY().floatValue());
    sink.setConsumeTapEvents(marker.getConsumeTapEvents());
    sink.setDraggable(marker.getDraggable());
    sink.setFlat(marker.getFlat());
    sink.setIcon(toBitmapDescriptor(marker.getIcon(), assetManager, density, wrapper));
    interpretInfoWindowOptions(sink, marker.getInfoWindow());
    sink.setPosition(toLatLng(marker.getPosition().toList()));
    sink.setRotation(marker.getRotation().floatValue());
    sink.setVisible(marker.getVisible());
    sink.setZIndex(marker.getZIndex().floatValue());
  }

  private static void interpretInfoWindowOptions(
      MarkerOptionsSink sink, Messages.PlatformInfoWindow infoWindow) {
    String title = infoWindow.getTitle();
    if (title != null) {
      sink.setInfoWindowText(title, infoWindow.getSnippet());
    }
    Messages.PlatformDoublePair infoWindowAnchor = infoWindow.getAnchor();
    sink.setInfoWindowAnchor(
        infoWindowAnchor.getX().floatValue(), infoWindowAnchor.getY().floatValue());
  }

  static String interpretPolygonOptions(Messages.PlatformPolygon polygon, PolygonOptionsSink sink) {
    sink.setConsumeTapEvents(polygon.getConsumesTapEvents());
    sink.setGeodesic(polygon.getGeodesic());
    sink.setVisible(polygon.getVisible());
    sink.setFillColor(polygon.getFillColor().intValue());
    sink.setStrokeColor(polygon.getStrokeColor().intValue());
    sink.setStrokeWidth(polygon.getStrokeWidth());
    sink.setZIndex(polygon.getZIndex());
    sink.setPoints(pointsFromPigeon(polygon.getPoints()));
    sink.setHoles(toHoles(polygon.getHoles()));
    return polygon.getPolygonId();
  }

  static int jointTypeFromPigeon(Messages.PlatformJointType jointType) {
    switch (jointType) {
      case MITERED:
        return JointType.DEFAULT;
      case BEVEL:
        return JointType.BEVEL;
      case ROUND:
        return JointType.ROUND;
    }
    return JointType.DEFAULT;
  }

  static String interpretPolylineOptions(
      Messages.PlatformPolyline polyline,
      PolylineOptionsSink sink,
      AssetManager assetManager,
      float density) {
    sink.setConsumeTapEvents(polyline.getConsumesTapEvents());
    sink.setColor(polyline.getColor().intValue());
    sink.setEndCap(capFromPigeon(polyline.getEndCap(), assetManager, density));
    sink.setStartCap(capFromPigeon(polyline.getStartCap(), assetManager, density));
    sink.setGeodesic(polyline.getGeodesic());
    sink.setJointType(jointTypeFromPigeon(polyline.getJointType()));
    sink.setVisible(polyline.getVisible());
    sink.setWidth(polyline.getWidth());
    sink.setZIndex(polyline.getZIndex());
    sink.setPoints(pointsFromPigeon(polyline.getPoints()));
    sink.setPattern(patternFromPigeon(polyline.getPatterns()));
    return polyline.getPolylineId();
  }

  static String interpretCircleOptions(Messages.PlatformCircle circle, CircleOptionsSink sink) {
    sink.setConsumeTapEvents(circle.getConsumeTapEvents());
    sink.setFillColor(circle.getFillColor().intValue());
    sink.setStrokeColor(circle.getStrokeColor().intValue());
    sink.setStrokeWidth(circle.getStrokeWidth());
    sink.setZIndex(circle.getZIndex().floatValue());
    sink.setCenter(toLatLng(circle.getCenter().toList()));
    sink.setRadius(circle.getRadius());
    sink.setVisible(circle.getVisible());
    return circle.getCircleId();
  }

  /**
   * Set the options in the given heatmap object to the given sink.
   *
   * @param data the object expected to be a Map containing the heatmap options. The options map is
   *     expected to have the following structure:
   *     <pre>{@code
   * {
   *   "heatmapId": String,
   *   "data": List, // List of serialized weighted lat/lng
   *   "gradient": Map, // Serialized heatmap gradient
   *   "maxIntensity": Double,
   *   "opacity": Double,
   *   "radius": Integer
   * }
   * }</pre>
   *
   * @param sink the HeatmapOptionsSink where the options will be set.
   * @return the heatmapId.
   * @throws IllegalArgumentException if heatmapId is null.
   */
  static String interpretHeatmapOptions(Map<String, ?> data, HeatmapOptionsSink sink) {
    final Object rawWeightedData = data.get(HEATMAP_DATA_KEY);
    if (rawWeightedData != null) {
      sink.setWeightedData(toWeightedData(rawWeightedData));
    }
    final Object gradient = data.get(HEATMAP_GRADIENT_KEY);
    if (gradient != null) {
      sink.setGradient(toGradient(gradient));
    }
    final Object maxIntensity = data.get(HEATMAP_MAX_INTENSITY_KEY);
    if (maxIntensity != null) {
      sink.setMaxIntensity(toDouble(maxIntensity));
    }
    final Object opacity = data.get(HEATMAP_OPACITY_KEY);
    if (opacity != null) {
      sink.setOpacity(toDouble(opacity));
    }
    final Object radius = data.get(HEATMAP_RADIUS_KEY);
    if (radius != null) {
      sink.setRadius(toInt(radius));
    }
    final String heatmapId = (String) data.get(HEATMAP_ID_KEY);
    if (heatmapId == null) {
      throw new IllegalArgumentException("heatmapId was null");
    } else {
      return heatmapId;
    }
  }

  static List<LatLng> pointsFromPigeon(List<Messages.PlatformLatLng> data) {
    final List<LatLng> points = new ArrayList<>(data.size());

    for (Messages.PlatformLatLng rawPoint : data) {
      points.add(new LatLng(rawPoint.getLatitude(), rawPoint.getLongitude()));
    }
    return points;
  }

  /**
   * Converts the given object to a list of WeightedLatLng objects.
   *
   * @param o the object to convert. The object is expected to be a List of serialized weighted
   *     lat/lng.
   * @return a list of WeightedLatLng objects.
   */
  @VisibleForTesting
  static List<WeightedLatLng> toWeightedData(Object o) {
    final List<?> data = toList(o);
    final List<WeightedLatLng> weightedData = new ArrayList<>(data.size());

    for (Object rawWeightedPoint : data) {
      weightedData.add(toWeightedLatLng(rawWeightedPoint));
    }
    return weightedData;
  }

  /**
   * Converts the given object to a Gradient object.
   *
   * @param o the object to convert. The object is expected to be a Map containing the gradient
   *     options. The gradient map is expected to have the following structure:
   *     <pre>{@code
   * {
   *   "colors": List<Integer>,
   *   "startPoints": List<Float>,
   *   "colorMapSize": Integer
   * }
   * }</pre>
   *
   * @return a Gradient object.
   */
  @VisibleForTesting
  static Gradient toGradient(Object o) {
    final Map<?, ?> data = toMap(o);

    final List<?> colorData = toList(data.get(HEATMAP_GRADIENT_COLORS_KEY));
    assert colorData != null;
    final int[] colors = new int[colorData.size()];
    for (int i = 0; i < colorData.size(); i++) {
      colors[i] = toInt(colorData.get(i));
    }

    final List<?> startPointData = toList(data.get(HEATMAP_GRADIENT_START_POINTS_KEY));
    assert startPointData != null;
    final float[] startPoints = new float[startPointData.size()];
    for (int i = 0; i < startPointData.size(); i++) {
      startPoints[i] = toFloat(startPointData.get(i));
    }

    final int colorMapSize = toInt(data.get(HEATMAP_GRADIENT_COLOR_MAP_SIZE_KEY));

    return new Gradient(colors, startPoints, colorMapSize);
  }

  private static List<List<LatLng>> toHoles(List<List<Messages.PlatformLatLng>> data) {
    final List<List<LatLng>> holes = new ArrayList<>(data.size());

    for (List<Messages.PlatformLatLng> hole : data) {
      holes.add(pointsFromPigeon(hole));
    }
    return holes;
  }

  private static List<PatternItem> patternFromPigeon(
      List<Messages.PlatformPatternItem> patternItems) {
    if (patternItems.isEmpty()) {
      return null;
    }
    final List<PatternItem> pattern = new ArrayList<>();
    for (Messages.PlatformPatternItem patternItem : patternItems) {
      switch (patternItem.getType()) {
        case DOT:
          pattern.add(new Dot());
          break;
        case DASH:
          assert patternItem.getLength() != null;
          pattern.add(new Dash(patternItem.getLength().floatValue()));
          break;
        case GAP:
          assert patternItem.getLength() != null;
          pattern.add(new Gap(patternItem.getLength().floatValue()));
          break;
      }
    }
    return pattern;
  }

  private static Cap capFromPigeon(
      Messages.PlatformCap cap, AssetManager assetManager, float density) {
    switch (cap.getType()) {
      case BUTT_CAP:
        return new ButtCap();
      case ROUND_CAP:
        return new RoundCap();
      case SQUARE_CAP:
        return new SquareCap();
      case CUSTOM_CAP:
        if (cap.getRefWidth() == null) {
          throw new IllegalArgumentException("A Custom Cap must specify a refWidth value.");
        }
        return new CustomCap(
            toBitmapDescriptor(cap.getBitmapDescriptor(), assetManager, density),
            cap.getRefWidth().floatValue());
    }
    throw new IllegalArgumentException("Unrecognized PlatformCap type: " + cap.getType());
  }

  static String interpretTileOverlayOptions(
      Messages.PlatformTileOverlay tileOverlay, TileOverlaySink sink) {
    sink.setFadeIn(tileOverlay.getFadeIn());
    sink.setTransparency(tileOverlay.getTransparency().floatValue());
    sink.setZIndex(tileOverlay.getZIndex());
    sink.setVisible(tileOverlay.getVisible());
    return tileOverlay.getTileOverlayId();
  }

  static Tile tileFromPigeon(Messages.PlatformTile tile) {
    return new Tile(tile.getWidth().intValue(), tile.getHeight().intValue(), tile.getData());
  }

  /**
   * Set the options in the given ground overlay object to the given sink.
   *
   * @param groundOverlay the object expected to be a PlatformGroundOverlay containing the ground
   *     overlay options.
   * @param sink the GroundOverlaySink where the options will be set.
   * @param assetManager An instance of Android's AssetManager, which provides access to any raw
   *     asset files stored in the application's assets directory.
   * @param density the density of the display, used to calculate pixel dimensions.
   * @param wrapper the BitmapDescriptorFactoryWrapper to create BitmapDescriptor.
   * @return the identifier of the ground overlay. The identifier is valid as long as the ground
   *     overlay exists.
   * @throws IllegalArgumentException if required fields are missing or invalid.
   */
  static @NonNull String interpretGroundOverlayOptions(
      @NonNull Messages.PlatformGroundOverlay groundOverlay,
      @NonNull GroundOverlaySink sink,
      @NonNull AssetManager assetManager,
      float density,
      @NonNull BitmapDescriptorFactoryWrapper wrapper) {
    sink.setTransparency(groundOverlay.getTransparency().floatValue());
    sink.setZIndex(groundOverlay.getZIndex().floatValue());
    sink.setVisible(groundOverlay.getVisible());
    if (groundOverlay.getAnchor() != null) {
      sink.setAnchor(
          groundOverlay.getAnchor().getX().floatValue(),
          groundOverlay.getAnchor().getY().floatValue());
    }
    sink.setBearing(groundOverlay.getBearing().floatValue());
    sink.setClickable(groundOverlay.getClickable());
    sink.setImage(toBitmapDescriptor(groundOverlay.getImage(), assetManager, density, wrapper));
    if (groundOverlay.getPosition() != null) {
      if (groundOverlay.getWidth() == null) {
        throw new FlutterError(
            "Invalid GroundOverlay",
            "Width is required when using a ground overlay with a position.",
            null);
      }
      sink.setPosition(
          latLngFromPigeon(groundOverlay.getPosition()),
          groundOverlay.getWidth().floatValue(),
          groundOverlay.getHeight() != null ? groundOverlay.getHeight().floatValue() : null);
    } else if (groundOverlay.getBounds() != null) {
      sink.setPositionFromBounds(latLngBoundsFromPigeon(groundOverlay.getBounds()));
    }
    return groundOverlay.getGroundOverlayId();
  }

  /**
   * Converts a GroundOverlay object to a PlatformGroundOverlay Pigeon object.
   *
   * @param groundOverlay the GroundOverlay object to convert.
   * @param groundOverlayId the identifier of the GroundOverlay.
   * @param isCreatedWithBounds indicates if the GroundOverlay was created with bounds.
   * @return the converted PlatformGroundOverlay object.
   */
  static @NonNull Messages.PlatformGroundOverlay groundOverlayToPigeon(
      @NonNull GroundOverlay groundOverlay,
      @NonNull String groundOverlayId,
      boolean isCreatedWithBounds) {

    // Image is mandatory field on PlatformGroundOverlay (and it should be kept
    // non-nullable), therefore image must be set for the object. The image is
    // description either contains set of bytes, or path to asset. This info is
    // converted to format google maps uses (BitmapDescription), and the original
    // data is not stored on native code. Therefore placeholder image is used for
    // the image field.
    Messages.PlatformBitmap dummyImage =
        new Messages.PlatformBitmap.Builder()
            .setBitmap(
                new Messages.PlatformBitmapBytesMap.Builder()
                    .setByteData(new byte[] {0})
                    .setImagePixelRatio(1.0)
                    .setBitmapScaling(Messages.PlatformMapBitmapScaling.NONE)
                    .build())
            .build();

    Messages.PlatformGroundOverlay.Builder builder =
        new Messages.PlatformGroundOverlay.Builder()
            .setGroundOverlayId(groundOverlayId)
            .setImage(dummyImage)
            .setWidth((double) groundOverlay.getWidth())
            .setHeight((double) groundOverlay.getHeight())
            .setBearing((double) groundOverlay.getBearing())
            .setTransparency((double) groundOverlay.getTransparency())
            .setZIndex((long) groundOverlay.getZIndex())
            .setVisible(groundOverlay.isVisible())
            .setClickable(groundOverlay.isClickable());

    if (isCreatedWithBounds) {
      builder.setBounds(Convert.latLngBoundsToPigeon(groundOverlay.getBounds()));
    } else {
      builder.setPosition(Convert.latLngToPigeon(groundOverlay.getPosition()));
    }

    builder.setAnchor(Convert.buildGroundOverlayAnchorForPigeon(groundOverlay));
    return builder.build();
  }

  /**
   * Builds a PlatformDoublePair representing the anchor point for a GroundOverlay.
   *
   * @param groundOverlay the GroundOverlay object.
   * @return the PlatformDoublePair representing the anchor point.
   */
  @VisibleForTesting
  public static @NonNull Messages.PlatformDoublePair buildGroundOverlayAnchorForPigeon(
      @NonNull GroundOverlay groundOverlay) {
    Messages.PlatformDoublePair.Builder anchorBuilder = new Messages.PlatformDoublePair.Builder();

    // Position is overlays anchor point. Calculate normalized anchor point based on position and bounds.
    LatLng position = groundOverlay.getPosition();
    LatLngBounds bounds = groundOverlay.getBounds();

    // Calculate normalized latitude.
    double height = bounds.northeast.latitude - bounds.southwest.latitude;
    double normalizedLatitude = 1.0 - ((position.latitude - bounds.southwest.latitude) / height);

    // Constant for full circle degrees.
    final double FULL_CIRCLE_DEGREES = 360.0;

    // Calculate normalized longitude.
    // For longitude, if the bounds cross the antimeridian (west > east),
    // adjust the width accordingly.
    double west = bounds.southwest.longitude;
    double east = bounds.northeast.longitude;
    double width = (west <= east) ? (east - west) : (FULL_CIRCLE_DEGREES - (west - east));

    // Normalize the longitude of the anchor position relative to the western boundary.
    // Handles cases where the ground overlay crosses the antimeridian.
    double normalizedLongitude =
        ((position.longitude < west ? position.longitude + FULL_CIRCLE_DEGREES : position.longitude)
                - west)
            / width;

    anchorBuilder.setX(normalizedLongitude);
    anchorBuilder.setY(normalizedLatitude);
    return anchorBuilder.build();
  }

  static class BitmapDescriptorFactoryWrapper {
    /**
     * Creates a BitmapDescriptor from the provided asset key using the {@link
     * BitmapDescriptorFactory}.
     *
     * <p>This method is visible for testing purposes only and should never be used outside Convert
     * class.
     *
     * @param assetKey the key of the asset.
     * @return a new instance of the {@link BitmapDescriptor}.
     */
    @VisibleForTesting
    public BitmapDescriptor fromAsset(String assetKey) {
      return BitmapDescriptorFactory.fromAsset(assetKey);
    }

    /**
     * Creates a BitmapDescriptor from the provided bitmap using the {@link
     * BitmapDescriptorFactory}.
     *
     * <p>This method is visible for testing purposes only and should never be used outside Convert
     * class.
     *
     * @param bitmap the bitmap to convert.
     * @return a new instance of the {@link BitmapDescriptor}.
     */
    @VisibleForTesting
    public BitmapDescriptor fromBitmap(Bitmap bitmap) {
      return BitmapDescriptorFactory.fromBitmap(bitmap);
    }
  }

  @VisibleForTesting
  static class FlutterInjectorWrapper {
    /**
     * Retrieves the lookup key for a given asset name using the {@link FlutterInjector}.
     *
     * <p>This method is visible for testing purposes only and should never be used outside Convert
     * class.
     *
     * @param assetName the name of the asset.
     * @return the lookup key for the asset.
     */
    @VisibleForTesting
    public String getLookupKeyForAsset(@NonNull String assetName) {
      return FlutterInjector.instance().flutterLoader().getLookupKeyForAsset(assetName);
    }
  }
}

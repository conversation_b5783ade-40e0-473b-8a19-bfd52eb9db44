{"components": [{"data": {"contents_checksum": "1bc60befc4644930ef06ab113ecdfe5bf1a5bc8c3f2f0060d75bb7a7fa2fc8e2", "source": "", "type": "tar"}, "dependencies": ["gcloud-crc32c"], "details": {"description": "Command line tool that calculates CRC32C hashes on local files.", "display_name": "Google Cloud CRC32C Hash Tool"}, "id": "gcloud-crc32c-darwin-arm", "is_configuration": false, "is_hidden": true, "is_required": false, "platform": {"architectures": ["arm"], "operating_systems": ["MACOSX"]}, "platform_required": false, "version": {"build_number": 20230901141909, "version_string": "1.0.0"}}], "revision": 20230901141909, "schema_version": {"no_update": false, "url": "https://dl.google.com/dl/cloudsdk/channels/rapid/google-cloud-sdk.tar.gz", "version": 3}, "version": "445.0.0"}
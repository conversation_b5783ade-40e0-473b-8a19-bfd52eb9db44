{"kind": "discovery#restDescription", "discoveryVersion": "v1", "id": "bigquery:v2", "name": "big<PERSON>y", "canonicalName": "Bigquery", "version": "v2", "revision": "0", "title": "BigQuery API", "description": "A data platform for customers to create, manage, share and query data.", "ownerDomain": "google.com", "ownerName": "Google", "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "documentationLink": "https://cloud.google.com/bigquery/", "protocol": "rest", "rootUrl": "https://bigquery.googleapis.com/", "mtlsRootUrl": "https://bigquery.mtls.googleapis.com/", "servicePath": "", "baseUrl": "https://bigquery.googleapis.com/", "batchPath": "batch", "fullyEncodeReservedExpansion": true, "parameters": {"access_token": {"type": "string", "description": "OAuth access token.", "location": "query"}, "alt": {"type": "string", "description": "Data format for response.", "default": "json", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query"}, "callback": {"type": "string", "description": "JSONP", "location": "query"}, "fields": {"type": "string", "description": "Selector specifying which fields to include in a partial response.", "location": "query"}, "key": {"type": "string", "description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query"}, "oauth_token": {"type": "string", "description": "OAuth 2.0 token for the current user.", "location": "query"}, "prettyPrint": {"type": "boolean", "description": "Returns response with indentations and line breaks.", "default": "true", "location": "query"}, "quotaUser": {"type": "string", "description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query"}, "upload_protocol": {"type": "string", "description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query"}, "uploadType": {"type": "string", "description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query"}, "$.xgafv": {"type": "string", "description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query"}}, "auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/bigquery": {"description": "View and manage your data in Google BigQuery and see the email address for your Google Account"}, "https://www.googleapis.com/auth/bigquery.insertdata": {"description": "Insert data into Google BigQuery"}, "https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}, "https://www.googleapis.com/auth/cloud-platform.read-only": {"description": "View your data across Google Cloud services and see the email address of your Google Account"}, "https://www.googleapis.com/auth/devstorage.full_control": {"description": "Manage your data and permissions in Cloud Storage and see the email address for your Google Account"}, "https://www.googleapis.com/auth/devstorage.read_only": {"description": "View your data in Google Cloud Storage"}, "https://www.googleapis.com/auth/devstorage.read_write": {"description": "Manage your data in Cloud Storage and see the email address of your Google Account"}}}}, "schemas": {"Dataset": {"id": "Dataset", "type": "object", "properties": {"kind": {"description": "Output only. The resource type.", "default": "bigquery#dataset", "readOnly": true, "type": "string"}, "etag": {"description": "Output only. A hash of the resource.", "readOnly": true, "type": "string"}, "id": {"description": "Output only. The fully-qualified unique name of the dataset in the format projectId:datasetId. The dataset name without the project name is given in the datasetId field. When creating a new dataset, leave this field blank, and instead specify the datasetId field.", "readOnly": true, "type": "string"}, "selfLink": {"description": "Output only. A URL that can be used to access the resource again. You can use this URL in Get or Update requests to the resource.", "readOnly": true, "type": "string"}, "datasetReference": {"description": "Required. A reference that identifies the dataset.", "$ref": "DatasetReference"}, "friendlyName": {"description": "Optional. A descriptive name for the dataset.", "type": "string"}, "description": {"description": "Optional. A user-friendly description of the dataset.", "type": "string"}, "defaultTableExpirationMs": {"description": "Optional. The default lifetime of all tables in the dataset, in milliseconds. The minimum lifetime value is 3600000 milliseconds (one hour). To clear an existing default expiration with a PATCH request, set to 0. Once this property is set, all newly-created tables in the dataset will have an expirationTime property set to the creation time plus the value in this property, and changing the value will only affect new tables, not existing ones. When the expirationTime for a given table is reached, that table will be deleted automatically. If a table's expirationTime is modified or removed before the table expires, or if you provide an explicit expirationTime when creating a table, that value takes precedence over the default expiration time indicated by this property.", "type": "string", "format": "int64"}, "defaultPartitionExpirationMs": {"description": "This default partition expiration, expressed in milliseconds. When new time-partitioned tables are created in a dataset where this property is set, the table will inherit this value, propagated as the `TimePartitioning.expirationMs` property on the new table. If you set `TimePartitioning.expirationMs` explicitly when creating a table, the `defaultPartitionExpirationMs` of the containing dataset is ignored. When creating a partitioned table, if `defaultPartitionExpirationMs` is set, the `defaultTableExpirationMs` value is ignored and the table will not be inherit a table expiration deadline.", "type": "string", "format": "int64"}, "labels": {"description": "The labels associated with this dataset. You can use these to organize and group your datasets. You can set this property when inserting or updating a dataset. See Creating and Updating Dataset Labels for more information.", "type": "object", "additionalProperties": {"type": "string"}}, "access": {"description": "Optional. An array of objects that define dataset access for one or more entities. You can set this property when inserting or updating a dataset in order to control who is allowed to access the data. If unspecified at dataset creation time, BigQuery adds default dataset access for the following entities: access.specialGroup: projectReaders; access.role: READER; access.specialGroup: projectWriters; access.role: WRITER; access.specialGroup: projectOwners; access.role: OWNER; access.userByEmail: [dataset creator email]; access.role: OWNER;", "type": "array", "items": {"description": "An object that defines dataset access for an entity.", "type": "object", "properties": {"role": {"description": "An IAM role ID that should be granted to the user, group, or domain specified in this access entry. The following legacy mappings will be applied: OWNER <=> roles/bigquery.dataOwner WRITER <=> roles/bigquery.dataEditor READER <=> roles/bigquery.dataViewer This field will accept any of the above formats, but will return only the legacy format. For example, if you set this field to \"roles/bigquery.dataOwner\", it will be returned back as \"OWNER\".", "type": "string"}, "userByEmail": {"description": "[Pick one] An email address of a user to grant access to. For example: <EMAIL>. Maps to IAM policy member \"user:EMAIL\" or \"serviceAccount:EMAIL\".", "type": "string"}, "groupByEmail": {"description": "[Pick one] An email address of a Google Group to grant access to. Maps to IAM policy member \"group:GROUP\".", "type": "string"}, "domain": {"description": "[Pick one] A domain to grant access to. Any users signed in with the domain specified will be granted the specified access. Example: \"example.com\". Maps to IAM policy member \"domain:DOMAIN\".", "type": "string"}, "specialGroup": {"description": "[Pick one] A special group to grant access to. Possible values include: projectOwners: Owners of the enclosing project. projectReaders: Readers of the enclosing project. projectWriters: Writers of the enclosing project. allAuthenticatedUsers: All authenticated BigQuery users. Maps to similarly-named IAM members.", "type": "string"}, "iamMember": {"description": "[Pick one] Some other type of member that appears in the IAM Policy but isn't a user, group, domain, or special group.", "type": "string"}, "view": {"description": "[Pick one] A view from a different dataset to grant access to. Queries executed against that view will have read access to views/tables/routines in this dataset. The role field is not required when this field is set. If that view is updated by any user, access to the view needs to be granted again via an update operation.", "$ref": "TableReference"}, "routine": {"description": "[Pick one] A routine from a different dataset to grant access to. Queries executed against that routine will have read access to views/tables/routines in this dataset. Only UDF is supported for now. The role field is not required when this field is set. If that routine is updated by any user, access to the routine needs to be granted again via an update operation.", "$ref": "RoutineReference"}, "dataset": {"description": "[Pick one] A grant authorizing all resources of a particular type in a particular dataset access to this dataset. Only views are supported for now. The role field is not required when this field is set. If that dataset is deleted and re-created, its access needs to be granted again via an update operation.", "$ref": "DatasetAccessEntry"}}}}, "creationTime": {"description": "Output only. The time when this dataset was created, in milliseconds since the epoch.", "readOnly": true, "type": "string", "format": "int64"}, "lastModifiedTime": {"description": "Output only. The date when this dataset was last modified, in milliseconds since the epoch.", "readOnly": true, "type": "string", "format": "int64"}, "location": {"description": "The geographic location where the dataset should reside. See https://cloud.google.com/bigquery/docs/locations for supported locations.", "type": "string"}, "defaultEncryptionConfiguration": {"description": "The default encryption key for all tables in the dataset. Once this property is set, all newly-created partitioned tables in the dataset will have encryption key set to this value, unless table creation request (or query) overrides the key.", "$ref": "EncryptionConfiguration"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "type": {"description": "Output only. Same as `type` in `ListFormatDataset`. The type of the dataset, one of: * DEFAULT - only accessible by owner and authorized accounts, * PUBLIC - accessible by everyone, * LINKED - linked dataset, * EXTERNAL - dataset with definition in external metadata catalog. -- *BIGLAKE_METASTORE - dataset that references a database created in BigLakeMetastore service. --", "readOnly": true, "type": "string"}, "linkedDatasetSource": {"description": "Optional. The source dataset reference when the dataset is of type LINKED. For all other dataset types it is not set. This field cannot be updated once it is set. Any attempt to update this field using Update and Patch API Operations will be ignored.", "$ref": "LinkedDatasetSource"}, "externalDatasetReference": {"description": "Optional. Information about the external metadata storage where the dataset is defined. Filled out when the dataset type is EXTERNAL.", "$ref": "ExternalDatasetReference"}, "isCaseInsensitive": {"description": "Optional. TRUE if the dataset and its table names are case-insensitive, otherwise FALSE. By default, this is FALSE, which means the dataset and its table names are case-sensitive. This field does not affect routine references.", "type": "boolean"}, "defaultCollation": {"description": "Optional. Defines the default collation specification of future tables created in the dataset. If a table is created in this dataset without table-level default collation, then the table inherits the dataset default collation, which is applied to the string fields that do not have explicit collation specified. A change to this field affects only tables created afterwards, and does not alter the existing tables. The following values are supported: * 'und:ci': undetermined locale, case insensitive. * '': empty string. Default to case-sensitive behavior.", "type": "string"}, "defaultRoundingMode": {"description": "Optional. Defines the default rounding mode specification of new tables created within this dataset. During table creation, if this field is specified, the table within this dataset will inherit the default rounding mode of the dataset. Setting the default rounding mode on a table overrides this option. Existing tables in the dataset are unaffected. If columns are defined during that table creation, they will immediately inherit the table's default rounding mode, unless otherwise specified.", "type": "string", "enumDescriptions": ["Unspecified will default to using ROUND_HALF_AWAY_FROM_ZERO.", "ROUND_HALF_AWAY_FROM_ZERO rounds half values away from zero when applying precision and scale upon writing of NUMERIC and BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1 1.5, 1.6, 1.7, 1.8, 1.9 => 2", "ROUND_HALF_EVEN rounds half values to the nearest even value when applying precision and scale upon writing of NUMERIC and BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1 1.5 => 2 1.6, 1.7, 1.8, 1.9 => 2 2.5 => 2"], "enum": ["ROUNDING_MODE_UNSPECIFIED", "ROUND_HALF_AWAY_FROM_ZERO", "ROUND_HALF_EVEN"]}, "maxTimeTravelHours": {"description": "Optional. Defines the time travel window in hours. The value can be from 48 to 168 hours (2 to 7 days). The default value is 168 hours if this is not set.", "type": "string", "format": "int64"}, "tags": {"description": "Output only. Tags for the Dataset.", "readOnly": true, "type": "array", "items": {"$ref": "GcpTag"}}, "storageBillingModel": {"description": "Optional. Updates storage_billing_model for the dataset.", "type": "string", "enumDescriptions": ["Value not set.", "Billing for logical bytes.", "Billing for physical bytes."], "enum": ["STORAGE_BILLING_MODEL_UNSPECIFIED", "LOGICAL", "PHYSICAL"]}}}, "DatasetReference": {"id": "DatasetReference", "type": "object", "properties": {"datasetId": {"description": "Required. A unique ID for this dataset, without the project name. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 1,024 characters.", "type": "string"}, "projectId": {"description": "Optional. The ID of the project containing this dataset.", "type": "string"}}}, "TableReference": {"id": "TableReference", "type": "object", "properties": {"projectId": {"description": "Required. The ID of the project containing this table.", "type": "string"}, "datasetId": {"description": "Required. The ID of the dataset containing this table.", "type": "string"}, "tableId": {"description": "Required. The ID of the table. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 1,024 characters. Certain operations allow suffixing of the table ID with a partition decorator, such as `sample_table$20190123`.", "type": "string"}}}, "RoutineReference": {"id": "RoutineReference", "description": "Id path of a routine.", "type": "object", "properties": {"projectId": {"description": "Required. The ID of the project containing this routine.", "type": "string"}, "datasetId": {"description": "Required. The ID of the dataset containing this routine.", "type": "string"}, "routineId": {"description": "Required. The ID of the routine. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 256 characters.", "type": "string"}}}, "DatasetAccessEntry": {"id": "DatasetAccessEntry", "description": "Grants all resources of particular types in a particular dataset read access to the current dataset. Similar to how individually authorized views work, updates to any resource granted through its dataset (including creation of new resources) requires read permission to referenced resources, plus write permission to the authorizing dataset.", "type": "object", "properties": {"dataset": {"description": "The dataset this entry applies to", "$ref": "DatasetReference"}, "targetTypes": {"description": "Which resources in the dataset this entry applies to. Currently, only views are supported, but additional target types may be added in the future.", "type": "array", "items": {"type": "string", "enumDescriptions": ["Do not use. You must set a target type explicitly.", "This entry applies to views in the dataset.", "This entry applies to routines in the dataset."], "enum": ["TARGET_TYPE_UNSPECIFIED", "VIEWS", "ROUTINES"]}}}}, "EncryptionConfiguration": {"id": "EncryptionConfiguration", "type": "object", "properties": {"kmsKeyName": {"description": "Optional. Describes the Cloud KMS encryption key that will be used to protect destination BigQuery table. The BigQuery Service Account associated with your project requires access to this encryption key.", "type": "string"}}}, "LinkedDatasetSource": {"id": "LinkedDatasetSource", "description": "A dataset source type which refers to another BigQuery dataset.", "type": "object", "properties": {"sourceDataset": {"description": "The source dataset reference contains project numbers and not project ids.", "$ref": "DatasetReference"}}}, "ExternalDatasetReference": {"id": "ExternalDatasetReference", "description": "Configures the access a dataset defined in an external metadata storage.", "type": "object", "properties": {"externalSource": {"description": "Required. External source that backs this dataset.", "type": "string"}, "connection": {"description": "Required. The connection id that is used to access the external_source. Format: projects/{project_id}/locations/{location_id}/connections/{connection_id}", "type": "string"}}}, "GcpTag": {"id": "GcpTag", "description": "A global tag managed by Resource Manager. https://cloud.google.com/iam/docs/tags-access-control#definitions", "type": "object", "properties": {"tagKey": {"description": "Required. The namespaced friendly name of the tag key, e.g. \"12345/environment\" where 12345 is org id.", "type": "string"}, "tagValue": {"description": "Required. The friendly short name of the tag value, e.g. \"production\".", "type": "string"}}}, "DatasetList": {"id": "DatasetList", "type": "object", "properties": {"kind": {"description": "Output only. The resource type. This property always returns the value \"bigquery#datasetList\"", "default": "bigquery#datasetList", "readOnly": true, "type": "string"}, "etag": {"description": "Output only. A hash value of the results page. You can use this property to determine if the page has changed since the last request.", "readOnly": true, "type": "string"}, "nextPageToken": {"description": "A token that can be used to request the next results page. This property is omitted on the final results page.", "type": "string"}, "datasets": {"description": "An array of the dataset resources in the project. Each resource contains basic information. For full information about a particular dataset resource, use the Datasets: get method. This property is omitted when there are no datasets in the project.", "type": "array", "items": {"description": "A dataset resource with only a subset of fields, to be returned in a list of datasets.", "type": "object", "properties": {"kind": {"description": "The resource type. This property always returns the value \"bigquery#dataset\"", "type": "string"}, "id": {"description": "The fully-qualified, unique, opaque ID of the dataset.", "type": "string"}, "datasetReference": {"description": "The dataset reference. Use this property to access specific parts of the dataset's ID, such as project ID or dataset ID.", "$ref": "DatasetReference"}, "labels": {"description": "The labels associated with this dataset. You can use these to organize and group your datasets.", "type": "object", "additionalProperties": {"type": "string"}}, "friendlyName": {"description": "An alternate name for the dataset. The friendly name is purely decorative in nature.", "type": "string"}, "location": {"description": "The geographic location where the dataset resides.", "type": "string"}}}}}}, "Model": {"id": "Model", "type": "object", "properties": {"etag": {"description": "Output only. A hash of this resource.", "readOnly": true, "type": "string"}, "modelReference": {"description": "Required. Unique identifier for this model.", "$ref": "ModelReference"}, "creationTime": {"description": "Output only. The time when this model was created, in millisecs since the epoch.", "readOnly": true, "type": "string", "format": "int64"}, "lastModifiedTime": {"description": "Output only. The time when this model was last modified, in millisecs since the epoch.", "readOnly": true, "type": "string", "format": "int64"}, "description": {"description": "Optional. A user-friendly description of this model.", "type": "string"}, "friendlyName": {"description": "Optional. A descriptive name for this model.", "type": "string"}, "labels": {"description": "The labels associated with this model. You can use these to organize and group your models. Label keys and values can be no longer than 63 characters, can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter and each label in the list must have a different key.", "type": "object", "additionalProperties": {"type": "string"}}, "expirationTime": {"description": "Optional. The time when this model expires, in milliseconds since the epoch. If not present, the model will persist indefinitely. Expired models will be deleted and their storage reclaimed. The defaultTableExpirationMs property of the encapsulating dataset can be used to set a default expirationTime on newly created models.", "type": "string", "format": "int64"}, "location": {"description": "Output only. The geographic location where the model resides. This value is inherited from the dataset.", "readOnly": true, "type": "string"}, "encryptionConfiguration": {"description": "Custom encryption configuration (e.g., Cloud KMS keys). This shows the encryption configuration of the model data while stored in BigQuery storage. This field can be used with PatchModel to update encryption key for an already encrypted model.", "$ref": "EncryptionConfiguration"}, "modelType": {"description": "Output only. Type of the model resource.", "readOnly": true, "type": "string", "enumDescriptions": ["", "Linear regression model.", "Logistic regression based classification model.", "K-means clustering model.", "Matrix factorization model.", "DNN classifier model.", "An imported TensorFlow model.", "DNN regressor model.", "An imported XGBoost model.", "Boosted tree regressor model.", "Boosted tree classifier model.", "ARIMA model.", "AutoML Tables regression model.", "AutoML Tables classification model.", "Prinpical Component Analysis model.", "Wide-and-deep classifier model.", "Wide-and-deep regressor model.", "Autoencoder model.", "New name for the ARIMA model.", "ARIMA with external regressors.", "Random forest regressor model.", "Random forest classifier model.", "An imported TensorFlow Lite model.", "An imported ONNX model."], "enum": ["MODEL_TYPE_UNSPECIFIED", "LINEAR_REGRESSION", "LOGISTIC_REGRESSION", "KMEANS", "MATRIX_FACTORIZATION", "DNN_CLASSIFIER", "TENSORFLOW", "DNN_REGRESSOR", "XGBOOST", "BOOSTED_TREE_REGRESSOR", "BOOSTED_TREE_CLASSIFIER", "ARIMA", "AUTOML_REGRESSOR", "AUTOML_CLASSIFIER", "PCA", "DNN_LINEAR_COMBINED_CLASSIFIER", "DNN_LINEAR_COMBINED_REGRESSOR", "AUTOENCODER", "ARIMA_PLUS", "ARIMA_PLUS_XREG", "RANDOM_FOREST_REGRESSOR", "RANDOM_FOREST_CLASSIFIER", "TENSORFLOW_LITE", "ONNX"]}, "trainingRuns": {"description": "Information for all training runs in increasing order of start_time.", "type": "array", "items": {"$ref": "TrainingRun"}}, "featureColumns": {"description": "Output only. Input feature columns for the model inference. If the model is trained with TRANSFORM clause, these are the input of the TRANSFORM clause.", "readOnly": true, "type": "array", "items": {"$ref": "StandardSqlField"}}, "labelColumns": {"description": "Output only. Label columns that were used to train this model. The output of the model will have a \"predicted_\" prefix to these columns.", "readOnly": true, "type": "array", "items": {"$ref": "StandardSqlField"}}, "transformColumns": {"description": "Output only. This field will be populated if a TRANSFORM clause was used to train a model. TRANSFORM clause (if used) takes feature_columns as input and outputs transform_columns. transform_columns then are used to train the model.", "readOnly": true, "type": "array", "items": {"$ref": "TransformColumn"}}, "hparamSearchSpaces": {"description": "Output only. All hyperparameter search spaces in this model.", "readOnly": true, "$ref": "HparamSearchSpaces"}, "bestTrialId": {"description": "The best trial_id across all training runs.", "deprecated": true, "type": "string", "format": "int64"}, "defaultTrialId": {"description": "Output only. The default trial_id to use in TVFs when the trial_id is not passed in. For single-objective [hyperparameter tuning](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) models, this is the best trial ID. For multi-objective [hyperparameter tuning](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) models, this is the smallest trial ID among all Pareto optimal trials.", "readOnly": true, "type": "string", "format": "int64"}, "hparamTrials": {"description": "Output only. Trials of a [hyperparameter tuning](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) model sorted by trial_id.", "readOnly": true, "type": "array", "items": {"$ref": "HparamTuningTrial"}}, "optimalTrialIds": {"description": "Output only. For single-objective [hyperparameter tuning](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) models, it only contains the best trial. For multi-objective [hyperparameter tuning](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) models, it contains all Pareto optimal trials sorted by trial_id.", "readOnly": true, "type": "array", "items": {"type": "string", "format": "int64"}}, "remoteModelInfo": {"description": "Output only. Remote model info", "readOnly": true, "$ref": "RemoteModelInfo"}}}, "ModelReference": {"id": "ModelReference", "description": "Id path of a model.", "type": "object", "properties": {"projectId": {"description": "Required. The ID of the project containing this model.", "type": "string"}, "datasetId": {"description": "Required. The ID of the dataset containing this model.", "type": "string"}, "modelId": {"description": "Required. The ID of the model. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 1,024 characters.", "type": "string"}}}, "TrainingRun": {"id": "TrainingRun", "description": "Information about a single training query run for the model.", "type": "object", "properties": {"trainingOptions": {"description": "Output only. Options that were used for this training run, includes user specified and default options that were used.", "readOnly": true, "$ref": "TrainingOptions"}, "trainingStartTime": {"description": "Output only. The start time of this training run, in milliseconds since epoch.", "readOnly": true, "deprecated": true, "type": "string", "format": "int64"}, "startTime": {"description": "Output only. The start time of this training run.", "readOnly": true, "type": "string", "format": "google-datetime"}, "results": {"description": "Output only. Output of each iteration run, results.size() <= max_iterations.", "readOnly": true, "type": "array", "items": {"$ref": "IterationResult"}}, "evaluationMetrics": {"description": "Output only. The evaluation metrics over training/eval data that were computed at the end of training.", "readOnly": true, "$ref": "EvaluationMetrics"}, "dataSplitResult": {"description": "Output only. Data split result of the training run. Only set when the input data is actually split.", "readOnly": true, "$ref": "DataSplitResult"}, "modelLevelGlobalExplanation": {"description": "Output only. Global explanation contains the explanation of top features on the model level. Applies to both regression and classification models.", "readOnly": true, "$ref": "GlobalExplanation"}, "classLevelGlobalExplanations": {"description": "Output only. Global explanation contains the explanation of top features on the class level. Applies to classification models only.", "readOnly": true, "type": "array", "items": {"$ref": "GlobalExplanation"}}, "vertexAiModelId": {"description": "The model id in the [Vertex AI Model Registry](https://cloud.google.com/vertex-ai/docs/model-registry/introduction) for this training run.", "type": "string"}, "vertexAiModelVersion": {"description": "Output only. The model version in the [Vertex AI Model Registry](https://cloud.google.com/vertex-ai/docs/model-registry/introduction) for this training run.", "readOnly": true, "type": "string"}}}, "TrainingOptions": {"id": "TrainingOptions", "description": "Options used in model training.", "type": "object", "properties": {"maxIterations": {"description": "The maximum number of iterations in training. Used only for iterative training algorithms.", "type": "string", "format": "int64"}, "lossType": {"description": "Type of loss function used during training run.", "type": "string", "enumDescriptions": ["", "Mean squared loss, used for linear regression.", "Mean log loss, used for logistic regression."], "enum": ["LOSS_TYPE_UNSPECIFIED", "MEAN_SQUARED_LOSS", "MEAN_LOG_LOSS"]}, "learnRate": {"description": "Learning rate in training. Used only for iterative training algorithms.", "type": "number", "format": "double"}, "l1Regularization": {"description": "L1 regularization coefficient.", "type": "number", "format": "double"}, "l2Regularization": {"description": "L2 regularization coefficient.", "type": "number", "format": "double"}, "minRelativeProgress": {"description": "When early_stop is true, stops training when accuracy improvement is less than 'min_relative_progress'. Used only for iterative training algorithms.", "type": "number", "format": "double"}, "warmStart": {"description": "Whether to train a model from the last checkpoint.", "type": "boolean"}, "earlyStop": {"description": "Whether to stop early when the loss doesn't improve significantly any more (compared to min_relative_progress). Used only for iterative training algorithms.", "type": "boolean"}, "inputLabelColumns": {"description": "Name of input label columns in training data.", "type": "array", "items": {"type": "string"}}, "dataSplitMethod": {"description": "The data split type for training and evaluation, e.g. RANDOM.", "type": "string", "enumDescriptions": ["", "Splits data randomly.", "Splits data with the user provided tags.", "Splits data sequentially.", "Data split will be skipped.", "Splits data automatically: Uses NO_SPLIT if the data size is small. Otherwise uses RANDOM."], "enum": ["DATA_SPLIT_METHOD_UNSPECIFIED", "RANDOM", "CUSTOM", "SEQUENTIAL", "NO_SPLIT", "AUTO_SPLIT"]}, "dataSplitEvalFraction": {"description": "The fraction of evaluation data over the whole input data. The rest of data will be used as training data. The format should be double. Accurate to two decimal places. Default value is 0.2.", "type": "number", "format": "double"}, "dataSplitColumn": {"description": "The column to split data with. This column won't be used as a feature. 1. When data_split_method is CUSTOM, the corresponding column should be boolean. The rows with true value tag are eval data, and the false are training data. 2. When data_split_method is SEQ, the first DATA_SPLIT_EVAL_FRACTION rows (from smallest to largest) in the corresponding column are used as training data, and the rest are eval data. It respects the order in Orderable data types: https://cloud.google.com/bigquery/docs/reference/standard-sql/data-types#data-type-properties", "type": "string"}, "learnRateStrategy": {"description": "The strategy to determine learn rate for the current iteration.", "type": "string", "enumDescriptions": ["", "Use line search to determine learning rate.", "Use a constant learning rate."], "enum": ["LEARN_RATE_STRATEGY_UNSPECIFIED", "LINE_SEARCH", "CONSTANT"]}, "initialLearnRate": {"description": "Specifies the initial learning rate for the line search learn rate strategy.", "type": "number", "format": "double"}, "labelClassWeights": {"description": "Weights associated with each label class, for rebalancing the training data. Only applicable for classification models.", "type": "object", "additionalProperties": {"type": "number", "format": "double"}}, "userColumn": {"description": "User column specified for matrix factorization models.", "type": "string"}, "itemColumn": {"description": "Item column specified for matrix factorization models.", "type": "string"}, "distanceType": {"description": "Distance type for clustering models.", "type": "string", "enumDescriptions": ["", "Eculidean distance.", "Cosine distance."], "enum": ["DISTANCE_TYPE_UNSPECIFIED", "EUCLIDEAN", "COSINE"]}, "numClusters": {"description": "Number of clusters for clustering models.", "type": "string", "format": "int64"}, "modelUri": {"description": "Google Cloud Storage URI from which the model was imported. Only applicable for imported models.", "type": "string"}, "optimizationStrategy": {"description": "Optimization strategy for training linear regression models.", "type": "string", "enumDescriptions": ["", "Uses an iterative batch gradient descent algorithm.", "Uses a normal equation to solve linear regression problem."], "enum": ["OPTIMIZATION_STRATEGY_UNSPECIFIED", "BATCH_GRADIENT_DESCENT", "NORMAL_EQUATION"]}, "hiddenUnits": {"description": "Hidden units for dnn models.", "type": "array", "items": {"type": "string", "format": "int64"}}, "batchSize": {"description": "Batch size for dnn models.", "type": "string", "format": "int64"}, "dropout": {"description": "Dropout probability for dnn models.", "type": "number", "format": "double"}, "maxTreeDepth": {"description": "Maximum depth of a tree for boosted tree models.", "type": "string", "format": "int64"}, "subsample": {"description": "Subsample fraction of the training data to grow tree to prevent overfitting for boosted tree models.", "type": "number", "format": "double"}, "minSplitLoss": {"description": "Minimum split loss for boosted tree models.", "type": "number", "format": "double"}, "boosterType": {"description": "Booster type for boosted tree models.", "type": "string", "enumDescriptions": ["Unspecified booster type.", "Gbtree booster.", "Dart booster."], "enum": ["BOOSTER_TYPE_UNSPECIFIED", "GBTREE", "DART"]}, "numParallelTree": {"description": "Number of parallel trees constructed during each iteration for boosted tree models.", "type": "string", "format": "int64"}, "dartNormalizeType": {"description": "Type of normalization algorithm for boosted tree models using dart booster.", "type": "string", "enumDescriptions": ["Unspecified dart normalize type.", "New trees have the same weight of each of dropped trees.", "New trees have the same weight of sum of dropped trees."], "enum": ["DART_NORMALIZE_TYPE_UNSPECIFIED", "TREE", "FOREST"]}, "treeMethod": {"description": "Tree construction algorithm for boosted tree models.", "type": "string", "enumDescriptions": ["Unspecified tree method.", "Use heuristic to choose the fastest method.", "Exact greedy algorithm.", "Approximate greedy algorithm using quantile sketch and gradient histogram.", "Fast histogram optimized approximate greedy algorithm."], "enum": ["TREE_METHOD_UNSPECIFIED", "AUTO", "EXACT", "APPROX", "HIST"]}, "minTreeChildWeight": {"description": "Minimum sum of instance weight needed in a child for boosted tree models.", "type": "string", "format": "int64"}, "colsampleBytree": {"description": "Subsample ratio of columns when constructing each tree for boosted tree models.", "type": "number", "format": "double"}, "colsampleBylevel": {"description": "Subsample ratio of columns for each level for boosted tree models.", "type": "number", "format": "double"}, "colsampleBynode": {"description": "Subsample ratio of columns for each node(split) for boosted tree models.", "type": "number", "format": "double"}, "numFactors": {"description": "Num factors specified for matrix factorization models.", "type": "string", "format": "int64"}, "feedbackType": {"description": "Feedback type that specifies which algorithm to run for matrix factorization.", "type": "string", "enumDescriptions": ["", "Use weighted-als for implicit feedback problems.", "Use nonweighted-als for explicit feedback problems."], "enum": ["FEEDBACK_TYPE_UNSPECIFIED", "IMPLICIT", "EXPLICIT"]}, "walsAlpha": {"description": "Hyperparameter for matrix factoration when implicit feedback type is specified.", "type": "number", "format": "double"}, "kmeansInitializationMethod": {"description": "The method used to initialize the centroids for kmeans algorithm.", "type": "string", "enumDescriptions": ["Unspecified initialization method.", "Initializes the centroids randomly.", "Initializes the centroids using data specified in kmeans_initialization_column.", "Initializes with kmeans++."], "enum": ["KMEANS_INITIALIZATION_METHOD_UNSPECIFIED", "RANDOM", "CUSTOM", "KMEANS_PLUS_PLUS"]}, "kmeansInitializationColumn": {"description": "The column used to provide the initial centroids for kmeans algorithm when kmeans_initialization_method is CUSTOM.", "type": "string"}, "timeSeriesTimestampColumn": {"description": "Column to be designated as time series timestamp for ARIMA model.", "type": "string"}, "timeSeriesDataColumn": {"description": "Column to be designated as time series data for ARIMA model.", "type": "string"}, "autoArima": {"description": "Whether to enable auto ARIMA or not.", "type": "boolean"}, "nonSeasonalOrder": {"description": "A specification of the non-seasonal part of the ARIMA model: the three components (p, d, q) are the AR order, the degree of differencing, and the MA order.", "$ref": "ArimaOrder"}, "dataFrequency": {"description": "The data frequency of a time series.", "type": "string", "enumDescriptions": ["", "Automatically inferred from timestamps.", "Yearly data.", "Quarterly data.", "Monthly data.", "Weekly data.", "Daily data.", "Hourly data.", "Per-minute data."], "enum": ["DATA_FREQUENCY_UNSPECIFIED", "AUTO_FREQUENCY", "YEARLY", "QUARTERLY", "MONTHLY", "WEEKLY", "DAILY", "HOURLY", "PER_MINUTE"]}, "calculatePValues": {"description": "Whether or not p-value test should be computed for this model. Only available for linear and logistic regression models.", "type": "boolean"}, "includeDrift": {"description": "Include drift when fitting an ARIMA model.", "type": "boolean"}, "holidayRegion": {"description": "The geographical region based on which the holidays are considered in time series modeling. If a valid value is specified, then holiday effects modeling is enabled.", "type": "string", "enumDescriptions": ["Holiday region unspecified.", "Global.", "North America.", "Japan and Asia Pacific: Korea, Greater China, India, Australia, and New Zealand.", "Europe, the Middle East and Africa.", "Latin America and the Caribbean.", "United Arab Emirates", "Argentina", "Austria", "Australia", "Belgium", "Brazil", "Canada", "Switzerland", "Chile", "China", "Colombia", "Czechoslovakia", "Czech Republic", "Germany", "Denmark", "Algeria", "Ecuador", "Estonia", "Egypt", "Spain", "Finland", "France", "Great Britain (United Kingdom)", "Greece", "Hong Kong", "Hungary", "Indonesia", "Ireland", "Israel", "India", "Iran", "Italy", "Japan", "Korea (South)", "Latvia", "Morocco", "Mexico", "Malaysia", "Nigeria", "Netherlands", "Norway", "New Zealand", "Peru", "Philippines", "Pakistan", "Poland", "Portugal", "Romania", "Serbia", "Russian Federation", "Saudi Arabia", "Sweden", "Singapore", "Slovenia", "Slovakia", "Thailand", "Turkey", "Taiwan", "Ukraine", "United States", "Venezuela", "Viet Nam", "South Africa"], "enum": ["HOLIDAY_REGION_UNSPECIFIED", "GLOBAL", "NA", "JAPAC", "EMEA", "LAC", "AE", "AR", "AT", "AU", "BE", "BR", "CA", "CH", "CL", "CN", "CO", "CS", "CZ", "DE", "DK", "DZ", "EC", "EE", "EG", "ES", "FI", "FR", "GB", "GR", "HK", "HU", "ID", "IE", "IL", "IN", "IR", "IT", "JP", "KR", "LV", "MA", "MX", "MY", "NG", "NL", "NO", "NZ", "PE", "PH", "PK", "PL", "PT", "RO", "RS", "RU", "SA", "SE", "SG", "SI", "SK", "TH", "TR", "TW", "UA", "US", "VE", "VN", "ZA"]}, "holidayRegions": {"description": "A list of geographical regions that are used for time series modeling.", "type": "array", "items": {"type": "string", "enumDescriptions": ["Holiday region unspecified.", "Global.", "North America.", "Japan and Asia Pacific: Korea, Greater China, India, Australia, and New Zealand.", "Europe, the Middle East and Africa.", "Latin America and the Caribbean.", "United Arab Emirates", "Argentina", "Austria", "Australia", "Belgium", "Brazil", "Canada", "Switzerland", "Chile", "China", "Colombia", "Czechoslovakia", "Czech Republic", "Germany", "Denmark", "Algeria", "Ecuador", "Estonia", "Egypt", "Spain", "Finland", "France", "Great Britain (United Kingdom)", "Greece", "Hong Kong", "Hungary", "Indonesia", "Ireland", "Israel", "India", "Iran", "Italy", "Japan", "Korea (South)", "Latvia", "Morocco", "Mexico", "Malaysia", "Nigeria", "Netherlands", "Norway", "New Zealand", "Peru", "Philippines", "Pakistan", "Poland", "Portugal", "Romania", "Serbia", "Russian Federation", "Saudi Arabia", "Sweden", "Singapore", "Slovenia", "Slovakia", "Thailand", "Turkey", "Taiwan", "Ukraine", "United States", "Venezuela", "Viet Nam", "South Africa"], "enum": ["HOLIDAY_REGION_UNSPECIFIED", "GLOBAL", "NA", "JAPAC", "EMEA", "LAC", "AE", "AR", "AT", "AU", "BE", "BR", "CA", "CH", "CL", "CN", "CO", "CS", "CZ", "DE", "DK", "DZ", "EC", "EE", "EG", "ES", "FI", "FR", "GB", "GR", "HK", "HU", "ID", "IE", "IL", "IN", "IR", "IT", "JP", "KR", "LV", "MA", "MX", "MY", "NG", "NL", "NO", "NZ", "PE", "PH", "PK", "PL", "PT", "RO", "RS", "RU", "SA", "SE", "SG", "SI", "SK", "TH", "TR", "TW", "UA", "US", "VE", "VN", "ZA"]}}, "timeSeriesIdColumn": {"description": "The time series id column that was used during ARIMA model training.", "type": "string"}, "timeSeriesIdColumns": {"description": "The time series id columns that were used during ARIMA model training.", "type": "array", "items": {"type": "string"}}, "horizon": {"description": "The number of periods ahead that need to be forecasted.", "type": "string", "format": "int64"}, "autoArimaMaxOrder": {"description": "The max value of the sum of non-seasonal p and q.", "type": "string", "format": "int64"}, "autoArimaMinOrder": {"description": "The min value of the sum of non-seasonal p and q.", "type": "string", "format": "int64"}, "numTrials": {"description": "Number of trials to run this hyperparameter tuning job.", "type": "string", "format": "int64"}, "maxParallelTrials": {"description": "Maximum number of trials to run in parallel.", "type": "string", "format": "int64"}, "hparamTuningObjectives": {"description": "The target evaluation metrics to optimize the hyperparameters for.", "type": "array", "items": {"type": "string", "enumDescriptions": ["Unspecified evaluation metric.", "Mean absolute error. mean_absolute_error = AVG(ABS(label - predicted))", "Mean squared error. mean_squared_error = AVG(POW(label - predicted, 2))", "Mean squared log error. mean_squared_log_error = AVG(POW(LN(1 + label) - LN(1 + predicted), 2))", "Mean absolute error. median_absolute_error = APPROX_QUANTILES(absolute_error, 2)[OFFSET(1)]", "R^2 score. This corresponds to r2_score in ML.EVALUATE. r_squared = 1 - SUM(squared_error)/(COUNT(label)*VAR_POP(label))", "Explained variance. explained_variance = 1 - VAR_POP(label_error)/VAR_POP(label)", "Precision is the fraction of actual positive predictions that had positive actual labels. For multiclass this is a macro-averaged metric treating each class as a binary classifier.", "Recall is the fraction of actual positive labels that were given a positive prediction. For multiclass this is a macro-averaged metric.", "Accuracy is the fraction of predictions given the correct label. For multiclass this is a globally micro-averaged metric.", "The F1 score is an average of recall and precision. For multiclass this is a macro-averaged metric.", "Logorithmic Loss. For multiclass this is a macro-averaged metric.", "Area Under an ROC Curve. For multiclass this is a macro-averaged metric.", "Davies-Bouldin Index.", "Mean Average Precision.", "Normalized Discounted Cumulative Gain.", "Average Rank."], "enum": ["HPARAM_TUNING_OBJECTIVE_UNSPECIFIED", "MEAN_ABSOLUTE_ERROR", "MEAN_SQUARED_ERROR", "MEAN_SQUARED_LOG_ERROR", "MEDIAN_ABSOLUTE_ERROR", "R_SQUARED", "EXPLAINED_VARIANCE", "PRECISION", "RECALL", "ACCURACY", "F1_SCORE", "LOG_LOSS", "ROC_AUC", "DAVIES_BOULDIN_INDEX", "MEAN_AVERAGE_PRECISION", "NORMALIZED_DISCOUNTED_CUMULATIVE_GAIN", "AVERAGE_RANK"]}}, "decomposeTimeSeries": {"description": "If true, perform decompose time series and save the results.", "type": "boolean"}, "cleanSpikesAndDips": {"description": "If true, clean spikes and dips in the input time series.", "type": "boolean"}, "adjustStepChanges": {"description": "If true, detect step changes and make data adjustment in the input time series.", "type": "boolean"}, "enableGlobalExplain": {"description": "If true, enable global explanation during training.", "type": "boolean"}, "sampledShapleyNumPaths": {"description": "Number of paths for the sampled <PERSON><PERSON><PERSON><PERSON> explain method.", "type": "string", "format": "int64"}, "integratedGradientsNumSteps": {"description": "Number of integral steps for the integrated gradients explain method.", "type": "string", "format": "int64"}, "categoryEncodingMethod": {"description": "Categorical feature encoding method.", "type": "string", "enumDescriptions": ["Unspecified encoding method.", "Applies one-hot encoding.", "Applies label encoding.", "Applies dummy encoding."], "enum": ["ENCODING_METHOD_UNSPECIFIED", "ONE_HOT_ENCODING", "LABEL_ENCODING", "DUMMY_ENCODING"]}, "tfVersion": {"description": "Based on the selected TF version, the corresponding docker image is used to train external models.", "type": "string"}, "colorSpace": {"description": "Enums for color space, used for processing images in Object Table. See more details at https://www.tensorflow.org/io/tutorials/colorspace.", "type": "string", "enumDescriptions": ["Unspecified color space", "RGB", "HSV", "YIQ", "YUV", "GRAYSCALE"], "enum": ["COLOR_SPACE_UNSPECIFIED", "RGB", "HSV", "YIQ", "YUV", "GRAYSCALE"]}, "instanceWeightColumn": {"description": "Name of the instance weight column for training data. This column isn't be used as a feature.", "type": "string"}, "trendSmoothingWindowSize": {"description": "Smoothing window size for the trend component. When a positive value is specified, a center moving average smoothing is applied on the history trend. When the smoothing window is out of the boundary at the beginning or the end of the trend, the first element or the last element is padded to fill the smoothing window before the average is applied.", "type": "string", "format": "int64"}, "timeSeriesLengthFraction": {"description": "The fraction of the interpolated length of the time series that's used to model the time series trend component. All of the time points of the time series are used to model the non-trend component. This training option accelerates modeling training without sacrificing much forecasting accuracy. You can use this option with `minTimeSeriesLength` but not with `maxTimeSeriesLength`.", "type": "number", "format": "double"}, "minTimeSeriesLength": {"description": "The minimum number of time points in a time series that are used in modeling the trend component of the time series. If you use this option you must also set the `timeSeriesLengthFraction` option. This training option ensures that enough time points are available when you use `timeSeriesLengthFraction` in trend modeling. This is particularly important when forecasting multiple time series in a single query using `timeSeriesIdColumn`. If the total number of time points is less than the `minTimeSeriesLength` value, then the query uses all available time points.", "type": "string", "format": "int64"}, "maxTimeSeriesLength": {"description": "The maximum number of time points in a time series that can be used in modeling the trend component of the time series. Don't use this option with the `timeSeriesLengthFraction` or `minTimeSeriesLength` options.", "type": "string", "format": "int64"}, "xgboostVersion": {"description": "User-selected XGBoost versions for training of XGBoost models.", "type": "string"}, "approxGlobalFeatureContrib": {"description": "Whether to use approximate feature contribution method in XGBoost model explanation for global explain.", "type": "boolean"}, "fitIntercept": {"description": "Whether the model should include intercept during model training.", "type": "boolean"}, "numPrincipalComponents": {"description": "Number of principal components to keep in the PCA model. Must be <= the number of features.", "type": "string", "format": "int64"}, "pcaExplainedVarianceRatio": {"description": "The minimum ratio of cumulative explained variance that needs to be given by the PCA model.", "type": "number", "format": "double"}, "scaleFeatures": {"description": "If true, scale the feature values by dividing the feature standard deviation. Currently only apply to PCA.", "type": "boolean"}, "pcaSolver": {"description": "The solver for PCA.", "type": "string", "enumDescriptions": ["", "Full eigen-decoposition.", "Randomized SVD.", "Auto."], "enum": ["UNSPECIFIED", "FULL", "RANDOMIZED", "AUTO"]}, "autoClassWeights": {"description": "Whether to calculate class weights automatically based on the popularity of each label.", "type": "boolean"}, "activationFn": {"description": "Activation function of the neural nets.", "type": "string"}, "optimizer": {"description": "Optimizer used for training the neural nets.", "type": "string"}, "budgetHours": {"description": "Budget in hours for AutoML training.", "type": "number", "format": "double"}, "standardizeFeatures": {"description": "Whether to standardize numerical features. Default to true.", "type": "boolean"}, "l1RegActivation": {"description": "L1 regularization coefficient to activations.", "type": "number", "format": "double"}, "modelRegistry": {"description": "The model registry.", "type": "string", "enumDescriptions": ["", "Vertex AI."], "enum": ["MODEL_REGISTRY_UNSPECIFIED", "VERTEX_AI"]}, "vertexAiModelVersionAliases": {"description": "The version aliases to apply in Vertex AI model registry. Always overwrite if the version aliases exists in a existing model.", "type": "array", "items": {"type": "string"}}}}, "ArimaOrder": {"id": "ArimaOrder", "description": "Arima order, can be used for both non-seasonal and seasonal parts.", "type": "object", "properties": {"p": {"description": "Order of the autoregressive part.", "type": "string", "format": "int64"}, "d": {"description": "Order of the differencing part.", "type": "string", "format": "int64"}, "q": {"description": "Order of the moving-average part.", "type": "string", "format": "int64"}}}, "IterationResult": {"id": "IterationResult", "description": "Information about a single iteration of the training run.", "type": "object", "properties": {"index": {"description": "Index of the iteration, 0 based.", "type": "integer", "format": "int32"}, "durationMs": {"description": "Time taken to run the iteration in milliseconds.", "type": "string", "format": "int64"}, "trainingLoss": {"description": "Loss computed on the training data at the end of iteration.", "type": "number", "format": "double"}, "evalLoss": {"description": "Loss computed on the eval data at the end of iteration.", "type": "number", "format": "double"}, "learnRate": {"description": "Learn rate used for this iteration.", "type": "number", "format": "double"}, "clusterInfos": {"description": "Information about top clusters for clustering models.", "type": "array", "items": {"$ref": "ClusterInfo"}}, "arimaResult": {"$ref": "ArimaResult"}, "principalComponentInfos": {"description": "The information of the principal components.", "type": "array", "items": {"$ref": "PrincipalComponentInfo"}}}}, "ClusterInfo": {"id": "ClusterInfo", "description": "Information about a single cluster for clustering model.", "type": "object", "properties": {"centroidId": {"description": "Centroid id.", "type": "string", "format": "int64"}, "clusterRadius": {"description": "Cluster radius, the average distance from centroid to each point assigned to the cluster.", "type": "number", "format": "double"}, "clusterSize": {"description": "Cluster size, the total number of points assigned to the cluster.", "type": "string", "format": "int64"}}}, "ArimaResult": {"id": "ArimaResult", "description": "(Auto-)arima fitting result. Wrap everything in ArimaResult for easier refactoring if we want to use model-specific iteration results.", "type": "object", "properties": {"arimaModelInfo": {"description": "This message is repeated because there are multiple arima models fitted in auto-arima. For non-auto-arima model, its size is one.", "type": "array", "items": {"$ref": "ArimaModelInfo"}}, "seasonalPeriods": {"description": "Seasonal periods. Repeated because multiple periods are supported for one time series.", "type": "array", "items": {"type": "string", "enumDescriptions": ["Unspecified seasonal period.", "No seasonality", "Daily period, 24 hours.", "Weekly period, 7 days.", "Monthly period, 30 days or irregular.", "Quarterly period, 90 days or irregular.", "Yearly period, 365 days or irregular."], "enum": ["SEASONAL_PERIOD_TYPE_UNSPECIFIED", "NO_SEASONALITY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"]}}}}, "ArimaModelInfo": {"id": "ArimaModelInfo", "description": "Arima model information.", "type": "object", "properties": {"nonSeasonalOrder": {"description": "Non-seasonal order.", "$ref": "ArimaOrder"}, "arimaCoefficients": {"description": "Arima coefficients.", "$ref": "ArimaCoefficients"}, "arimaFittingMetrics": {"description": "Arima fitting metrics.", "$ref": "ArimaFittingMetrics"}, "hasDrift": {"description": "Whether Arima model fitted with drift or not. It is always false when d is not 1.", "type": "boolean"}, "timeSeriesId": {"description": "The time_series_id value for this time series. It will be one of the unique values from the time_series_id_column specified during ARIMA model training. Only present when time_series_id_column training option was used.", "type": "string"}, "timeSeriesIds": {"description": "The tuple of time_series_ids identifying this time series. It will be one of the unique tuples of values present in the time_series_id_columns specified during ARIMA model training. Only present when time_series_id_columns training option was used and the order of values here are same as the order of time_series_id_columns.", "type": "array", "items": {"type": "string"}}, "seasonalPeriods": {"description": "Seasonal periods. Repeated because multiple periods are supported for one time series.", "type": "array", "items": {"type": "string", "enumDescriptions": ["Unspecified seasonal period.", "No seasonality", "Daily period, 24 hours.", "Weekly period, 7 days.", "Monthly period, 30 days or irregular.", "Quarterly period, 90 days or irregular.", "Yearly period, 365 days or irregular."], "enum": ["SEASONAL_PERIOD_TYPE_UNSPECIFIED", "NO_SEASONALITY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"]}}, "hasHolidayEffect": {"description": "If true, holiday_effect is a part of time series decomposition result.", "type": "boolean"}, "hasSpikesAndDips": {"description": "If true, spikes_and_dips is a part of time series decomposition result.", "type": "boolean"}, "hasStepChanges": {"description": "If true, step_changes is a part of time series decomposition result.", "type": "boolean"}}}, "ArimaCoefficients": {"id": "ArimaCoefficients", "description": "Arima coefficients.", "type": "object", "properties": {"autoRegressiveCoefficients": {"description": "Auto-regressive coefficients, an array of double.", "type": "array", "items": {"type": "number", "format": "double"}}, "movingAverageCoefficients": {"description": "Moving-average coefficients, an array of double.", "type": "array", "items": {"type": "number", "format": "double"}}, "interceptCoefficient": {"description": "Intercept coefficient, just a double not an array.", "type": "number", "format": "double"}}}, "ArimaFittingMetrics": {"id": "ArimaFittingMetrics", "description": "ARIMA model fitting metrics.", "type": "object", "properties": {"logLikelihood": {"description": "Log-likelihood.", "type": "number", "format": "double"}, "aic": {"description": "AIC.", "type": "number", "format": "double"}, "variance": {"description": "Varian<PERSON>.", "type": "number", "format": "double"}}}, "PrincipalComponentInfo": {"id": "PrincipalComponentInfo", "description": "Principal component infos, used only for eigen decomposition based models, e.g., PCA. Ordered by explained_variance in the descending order.", "type": "object", "properties": {"principalComponentId": {"description": "Id of the principal component.", "type": "string", "format": "int64"}, "explainedVariance": {"description": "Explained variance by this principal component, which is simply the eigenvalue.", "type": "number", "format": "double"}, "explainedVarianceRatio": {"description": "Explained_variance over the total explained variance.", "type": "number", "format": "double"}, "cumulativeExplainedVarianceRatio": {"description": "The explained_variance is pre-ordered in the descending order to compute the cumulative explained variance ratio.", "type": "number", "format": "double"}}}, "EvaluationMetrics": {"id": "EvaluationMetrics", "description": "Evaluation metrics of a model. These are either computed on all training data or just the eval data based on whether eval data was used during training. These are not present for imported models.", "type": "object", "properties": {"regressionMetrics": {"description": "Populated for regression models and explicit feedback type matrix factorization models.", "$ref": "RegressionMetrics"}, "binaryClassificationMetrics": {"description": "Populated for binary classification/classifier models.", "$ref": "BinaryClassificationMetrics"}, "multiClassClassificationMetrics": {"description": "Populated for multi-class classification/classifier models.", "$ref": "MultiClassClassificationMetrics"}, "clusteringMetrics": {"description": "Populated for clustering models.", "$ref": "ClusteringMetrics"}, "rankingMetrics": {"description": "Populated for implicit feedback type matrix factorization models.", "$ref": "RankingMetrics"}, "arimaForecastingMetrics": {"description": "Populated for ARIMA models.", "$ref": "ArimaForecastingMetrics"}, "dimensionalityReductionMetrics": {"description": "Evaluation metrics when the model is a dimensionality reduction model, which currently includes PCA.", "$ref": "DimensionalityReductionMetrics"}}}, "RegressionMetrics": {"id": "RegressionMetrics", "description": "Evaluation metrics for regression and explicit feedback type matrix factorization models.", "type": "object", "properties": {"meanAbsoluteError": {"description": "Mean absolute error.", "type": "number", "format": "double"}, "meanSquaredError": {"description": "Mean squared error.", "type": "number", "format": "double"}, "meanSquaredLogError": {"description": "Mean squared log error.", "type": "number", "format": "double"}, "medianAbsoluteError": {"description": "Median absolute error.", "type": "number", "format": "double"}, "rSquared": {"description": "R^2 score. This corresponds to r2_score in ML.EVALUATE.", "type": "number", "format": "double"}}}, "BinaryClassificationMetrics": {"id": "BinaryClassificationMetrics", "description": "Evaluation metrics for binary classification/classifier models.", "type": "object", "properties": {"aggregateClassificationMetrics": {"description": "Aggregate classification metrics.", "$ref": "AggregateClassificationMetrics"}, "binaryConfusionMatrixList": {"description": "Binary confusion matrix at multiple thresholds.", "type": "array", "items": {"$ref": "BinaryConfusionMatrix"}}, "positiveLabel": {"description": "Label representing the positive class.", "type": "string"}, "negativeLabel": {"description": "Label representing the negative class.", "type": "string"}}}, "AggregateClassificationMetrics": {"id": "AggregateClassificationMetrics", "description": "Aggregate metrics for classification/classifier models. For multi-class models, the metrics are either macro-averaged or micro-averaged. When macro-averaged, the metrics are calculated for each label and then an unweighted average is taken of those values. When micro-averaged, the metric is calculated globally by counting the total number of correctly predicted rows.", "type": "object", "properties": {"precision": {"description": "Precision is the fraction of actual positive predictions that had positive actual labels. For multiclass this is a macro-averaged metric treating each class as a binary classifier.", "type": "number", "format": "double"}, "recall": {"description": "Recall is the fraction of actual positive labels that were given a positive prediction. For multiclass this is a macro-averaged metric.", "type": "number", "format": "double"}, "accuracy": {"description": "Accuracy is the fraction of predictions given the correct label. For multiclass this is a micro-averaged metric.", "type": "number", "format": "double"}, "threshold": {"description": "Threshold at which the metrics are computed. For binary classification models this is the positive class threshold. For multi-class classfication models this is the confidence threshold.", "type": "number", "format": "double"}, "f1Score": {"description": "The F1 score is an average of recall and precision. For multiclass this is a macro-averaged metric.", "type": "number", "format": "double"}, "logLoss": {"description": "Logarithmic Loss. For multiclass this is a macro-averaged metric.", "type": "number", "format": "double"}, "rocAuc": {"description": "Area Under a ROC Curve. For multiclass this is a macro-averaged metric.", "type": "number", "format": "double"}}}, "BinaryConfusionMatrix": {"id": "BinaryConfusionMatrix", "description": "Confusion matrix for binary classification models.", "type": "object", "properties": {"positiveClassThreshold": {"description": "Threshold value used when computing each of the following metric.", "type": "number", "format": "double"}, "truePositives": {"description": "Number of true samples predicted as true.", "type": "string", "format": "int64"}, "falsePositives": {"description": "Number of false samples predicted as true.", "type": "string", "format": "int64"}, "trueNegatives": {"description": "Number of true samples predicted as false.", "type": "string", "format": "int64"}, "falseNegatives": {"description": "Number of false samples predicted as false.", "type": "string", "format": "int64"}, "precision": {"description": "The fraction of actual positive predictions that had positive actual labels.", "type": "number", "format": "double"}, "recall": {"description": "The fraction of actual positive labels that were given a positive prediction.", "type": "number", "format": "double"}, "f1Score": {"description": "The equally weighted average of recall and precision.", "type": "number", "format": "double"}, "accuracy": {"description": "The fraction of predictions given the correct label.", "type": "number", "format": "double"}}}, "MultiClassClassificationMetrics": {"id": "MultiClassClassificationMetrics", "description": "Evaluation metrics for multi-class classification/classifier models.", "type": "object", "properties": {"aggregateClassificationMetrics": {"description": "Aggregate classification metrics.", "$ref": "AggregateClassificationMetrics"}, "confusionMatrixList": {"description": "Confusion matrix at different thresholds.", "type": "array", "items": {"$ref": "ConfusionMatrix"}}}}, "ConfusionMatrix": {"id": "ConfusionMatrix", "description": "Confusion matrix for multi-class classification models.", "type": "object", "properties": {"confidenceThreshold": {"description": "Confidence threshold used when computing the entries of the confusion matrix.", "type": "number", "format": "double"}, "rows": {"description": "One row per actual label.", "type": "array", "items": {"$ref": "Row"}}}}, "Row": {"id": "Row", "description": "A single row in the confusion matrix.", "type": "object", "properties": {"actualLabel": {"description": "The original label of this row.", "type": "string"}, "entries": {"description": "Info describing predicted label distribution.", "type": "array", "items": {"$ref": "Entry"}}}}, "Entry": {"id": "Entry", "description": "A single entry in the confusion matrix.", "type": "object", "properties": {"predictedLabel": {"description": "The predicted label. For confidence_threshold > 0, we will also add an entry indicating the number of items under the confidence threshold.", "type": "string"}, "itemCount": {"description": "Number of items being predicted as this label.", "type": "string", "format": "int64"}}}, "ClusteringMetrics": {"id": "ClusteringMetrics", "description": "Evaluation metrics for clustering models.", "type": "object", "properties": {"daviesBouldinIndex": {"description": "Davies-Bouldin index.", "type": "number", "format": "double"}, "meanSquaredDistance": {"description": "Mean of squared distances between each sample to its cluster centroid.", "type": "number", "format": "double"}, "clusters": {"description": "Information for all clusters.", "type": "array", "items": {"$ref": "Cluster"}}}}, "Cluster": {"id": "Cluster", "description": "Message containing the information about one cluster.", "type": "object", "properties": {"centroidId": {"description": "Centroid id.", "type": "string", "format": "int64"}, "featureValues": {"description": "Values of highly variant features for this cluster.", "type": "array", "items": {"$ref": "FeatureValue"}}, "count": {"description": "Count of training data rows that were assigned to this cluster.", "type": "string", "format": "int64"}}}, "FeatureValue": {"id": "FeatureValue", "description": "Representative value of a single feature within the cluster.", "type": "object", "properties": {"featureColumn": {"description": "The feature column name.", "type": "string"}, "numericalValue": {"description": "The numerical feature value. This is the centroid value for this feature.", "type": "number", "format": "double"}, "categoricalValue": {"description": "The categorical feature value.", "$ref": "CategoricalValue"}}}, "CategoricalValue": {"id": "CategoricalValue", "description": "Representative value of a categorical feature.", "type": "object", "properties": {"categoryCounts": {"description": "Counts of all categories for the categorical feature. If there are more than ten categories, we return top ten (by count) and return one more CategoryCount with category \"_OTHER_\" and count as aggregate counts of remaining categories.", "type": "array", "items": {"$ref": "CategoryCount"}}}}, "CategoryCount": {"id": "CategoryCount", "description": "Represents the count of a single category within the cluster.", "type": "object", "properties": {"category": {"description": "The name of category.", "type": "string"}, "count": {"description": "The count of training samples matching the category within the cluster.", "type": "string", "format": "int64"}}}, "RankingMetrics": {"id": "RankingMetrics", "description": "Evaluation metrics used by weighted-ALS models specified by feedback_type=implicit.", "type": "object", "properties": {"meanAveragePrecision": {"description": "Calculates a precision per user for all the items by ranking them and then averages all the precisions across all the users.", "type": "number", "format": "double"}, "meanSquaredError": {"description": "Similar to the mean squared error computed in regression and explicit recommendation models except instead of computing the rating directly, the output from evaluate is computed against a preference which is 1 or 0 depending on if the rating exists or not.", "type": "number", "format": "double"}, "normalizedDiscountedCumulativeGain": {"description": "A metric to determine the goodness of a ranking calculated from the predicted confidence by comparing it to an ideal rank measured by the original ratings.", "type": "number", "format": "double"}, "averageRank": {"description": "Determines the goodness of a ranking by computing the percentile rank from the predicted confidence and dividing it by the original rank.", "type": "number", "format": "double"}}}, "ArimaForecastingMetrics": {"id": "ArimaForecastingMetrics", "description": "Model evaluation metrics for ARIMA forecasting models.", "type": "object", "properties": {"nonSeasonalOrder": {"description": "Non-seasonal order.", "deprecated": true, "type": "array", "items": {"$ref": "ArimaOrder"}}, "arimaFittingMetrics": {"description": "Arima model fitting metrics.", "deprecated": true, "type": "array", "items": {"$ref": "ArimaFittingMetrics"}}, "seasonalPeriods": {"description": "Seasonal periods. Repeated because multiple periods are supported for one time series.", "deprecated": true, "type": "array", "items": {"type": "string", "enumDescriptions": ["Unspecified seasonal period.", "No seasonality", "Daily period, 24 hours.", "Weekly period, 7 days.", "Monthly period, 30 days or irregular.", "Quarterly period, 90 days or irregular.", "Yearly period, 365 days or irregular."], "enum": ["SEASONAL_PERIOD_TYPE_UNSPECIFIED", "NO_SEASONALITY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"]}}, "hasDrift": {"description": "Whether Arima model fitted with drift or not. It is always false when d is not 1.", "deprecated": true, "type": "array", "items": {"type": "boolean"}}, "timeSeriesId": {"description": "Id to differentiate different time series for the large-scale case.", "deprecated": true, "type": "array", "items": {"type": "string"}}, "arimaSingleModelForecastingMetrics": {"description": "Repeated as there can be many metric sets (one for each model) in auto-arima and the large-scale case.", "type": "array", "items": {"$ref": "ArimaSingleModelForecastingMetrics"}}}}, "ArimaSingleModelForecastingMetrics": {"id": "ArimaSingleModelForecastingMetrics", "description": "Model evaluation metrics for a single ARIMA forecasting model.", "type": "object", "properties": {"nonSeasonalOrder": {"description": "Non-seasonal order.", "$ref": "ArimaOrder"}, "arimaFittingMetrics": {"description": "Arima fitting metrics.", "$ref": "ArimaFittingMetrics"}, "hasDrift": {"description": "Is arima model fitted with drift or not. It is always false when d is not 1.", "type": "boolean"}, "timeSeriesId": {"description": "The time_series_id value for this time series. It will be one of the unique values from the time_series_id_column specified during ARIMA model training. Only present when time_series_id_column training option was used.", "type": "string"}, "timeSeriesIds": {"description": "The tuple of time_series_ids identifying this time series. It will be one of the unique tuples of values present in the time_series_id_columns specified during ARIMA model training. Only present when time_series_id_columns training option was used and the order of values here are same as the order of time_series_id_columns.", "type": "array", "items": {"type": "string"}}, "seasonalPeriods": {"description": "Seasonal periods. Repeated because multiple periods are supported for one time series.", "type": "array", "items": {"type": "string", "enumDescriptions": ["Unspecified seasonal period.", "No seasonality", "Daily period, 24 hours.", "Weekly period, 7 days.", "Monthly period, 30 days or irregular.", "Quarterly period, 90 days or irregular.", "Yearly period, 365 days or irregular."], "enum": ["SEASONAL_PERIOD_TYPE_UNSPECIFIED", "NO_SEASONALITY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"]}}, "hasHolidayEffect": {"description": "If true, holiday_effect is a part of time series decomposition result.", "type": "boolean"}, "hasSpikesAndDips": {"description": "If true, spikes_and_dips is a part of time series decomposition result.", "type": "boolean"}, "hasStepChanges": {"description": "If true, step_changes is a part of time series decomposition result.", "type": "boolean"}}}, "DimensionalityReductionMetrics": {"id": "DimensionalityReductionMetrics", "description": "Model evaluation metrics for dimensionality reduction models.", "type": "object", "properties": {"totalExplainedVarianceRatio": {"description": "Total percentage of variance explained by the selected principal components.", "type": "number", "format": "double"}}}, "DataSplitResult": {"id": "DataSplitResult", "description": "Data split result. This contains references to the training and evaluation data tables that were used to train the model.", "type": "object", "properties": {"trainingTable": {"description": "Table reference of the training data after split.", "$ref": "TableReference"}, "evaluationTable": {"description": "Table reference of the evaluation data after split.", "$ref": "TableReference"}, "testTable": {"description": "Table reference of the test data after split.", "$ref": "TableReference"}}}, "GlobalExplanation": {"id": "GlobalExplanation", "description": "Global explanations containing the top most important features after training.", "type": "object", "properties": {"explanations": {"description": "A list of the top global explanations. Sorted by absolute value of attribution in descending order.", "type": "array", "items": {"$ref": "Explanation"}}, "classLabel": {"description": "Class label for this set of global explanations. Will be empty/null for binary logistic and linear regression models. Sorted alphabetically in descending order.", "type": "string"}}}, "Explanation": {"id": "Explanation", "description": "Explanation for a single feature.", "type": "object", "properties": {"featureName": {"description": "The full feature name. For non-numerical features, will be formatted like `.`. Overall size of feature name will always be truncated to first 120 characters.", "type": "string"}, "attribution": {"description": "Attribution of feature.", "type": "number", "format": "double"}}}, "StandardSqlField": {"id": "StandardSqlField", "description": "A field or a column.", "type": "object", "properties": {"name": {"description": "Optional. The name of this field. Can be absent for struct fields.", "type": "string"}, "type": {"description": "Optional. The type of this parameter. Absent if not explicitly specified (e.g., CREATE FUNCTION statement can omit the return type; in this case the output parameter does not have this \"type\" field).", "$ref": "StandardSqlDataType"}}}, "StandardSqlDataType": {"id": "StandardSqlDataType", "description": "The data type of a variable such as a function argument. Examples include: * INT64: `{\"typeKind\": \"INT64\"}` * ARRAY: { \"typeKind\": \"ARRAY\", \"arrayElementType\": {\"typeKind\": \"STRING\"} } * STRUCT>: { \"typeKind\": \"STRUCT\", \"structType\": { \"fields\": [ { \"name\": \"x\", \"type\": {\"typeKind\": \"STRING\"} }, { \"name\": \"y\", \"type\": { \"typeKind\": \"ARRAY\", \"arrayElementType\": {\"typeKind\": \"DATE\"} } } ] } }", "type": "object", "properties": {"typeKind": {"description": "Required. The top level type of this field. Can be any GoogleSQL data type (e.g., \"INT64\", \"DATE\", \"ARRAY\").", "type": "string", "enumDescriptions": ["Invalid type.", "Encoded as a string in decimal format.", "Encoded as a boolean \"false\" or \"true\".", "Encoded as a number, or string \"NaN\", \"Infinity\" or \"-Infinity\".", "Encoded as a string value.", "Encoded as a base64 string per RFC 4648, section 4.", "Encoded as an RFC 3339 timestamp with mandatory \"Z\" time zone string: 1985-04-12T23:20:50.52Z", "Encoded as RFC 3339 full-date format string: 1985-04-12", "Encoded as RFC 3339 partial-time format string: 23:20:50.52", "Encoded as RFC 3339 full-date \"T\" partial-time: 1985-04-12T23:20:50.52", "Encoded as fully qualified 3 part: 0-5 15 2:30:45.6", "Encoded as WKT", "Encoded as a decimal string.", "Encoded as a decimal string.", "Encoded as a string.", "Encoded as a list with types matching Type.array_type.", "Encoded as a list with fields of type Type.struct_type[i]. List is used because a JSON object cannot have duplicate field names.", "Encoded as a pair with types matching range_element_type. Pairs must begin with \"[\", end with \")\", and be separated by \", \"."], "enum": ["TYPE_KIND_UNSPECIFIED", "INT64", "BOOL", "FLOAT64", "STRING", "BYTES", "TIMESTAMP", "DATE", "TIME", "DATETIME", "INTERVAL", "GEOGRAPHY", "NUMERIC", "BIGNUMERIC", "JSON", "ARRAY", "STRUCT", "RANGE"]}, "arrayElementType": {"description": "The type of the array's elements, if type_kind = \"ARRAY\".", "$ref": "StandardSqlDataType"}, "structType": {"description": "The fields of this struct, in order, if type_kind = \"STRUCT\".", "$ref": "StandardSqlStructType"}, "rangeElementType": {"description": "The type of the range's elements, if type_kind = \"RANGE\".", "$ref": "StandardSqlDataType"}}}, "StandardSqlStructType": {"id": "StandardSqlStructType", "type": "object", "properties": {"fields": {"type": "array", "items": {"$ref": "StandardSqlField"}}}}, "TransformColumn": {"id": "TransformColumn", "description": "Information about a single transform column.", "type": "object", "properties": {"name": {"description": "Output only. Name of the column.", "readOnly": true, "type": "string"}, "type": {"description": "Output only. Data type of the column after the transform.", "readOnly": true, "$ref": "StandardSqlDataType"}, "transformSql": {"description": "Output only. The SQL expression used in the column transform.", "readOnly": true, "type": "string"}}}, "HparamSearchSpaces": {"id": "HparamSearchSpaces", "description": "Hyperparameter search spaces. These should be a subset of training_options.", "type": "object", "properties": {"learnRate": {"description": "Learning rate of training jobs.", "$ref": "DoubleHparamSearchSpace"}, "l1Reg": {"description": "L1 regularization coefficient.", "$ref": "DoubleHparamSearchSpace"}, "l2Reg": {"description": "L2 regularization coefficient.", "$ref": "DoubleHparamSearchSpace"}, "numClusters": {"description": "Number of clusters for k-means.", "$ref": "IntHparamSearchSpace"}, "numFactors": {"description": "Number of latent factors to train on.", "$ref": "IntHparamSearchSpace"}, "hiddenUnits": {"description": "Hidden units for neural network models.", "$ref": "IntArrayHparamSearchSpace"}, "batchSize": {"description": "Mini batch sample size.", "$ref": "IntHparamSearchSpace"}, "dropout": {"description": "Dropout probability for dnn model training and boosted tree models using dart booster.", "$ref": "DoubleHparamSearchSpace"}, "maxTreeDepth": {"description": "Maximum depth of a tree for boosted tree models.", "$ref": "IntHparamSearchSpace"}, "subsample": {"description": "Subsample the training data to grow tree to prevent overfitting for boosted tree models.", "$ref": "DoubleHparamSearchSpace"}, "minSplitLoss": {"description": "Minimum split loss for boosted tree models.", "$ref": "DoubleHparamSearchSpace"}, "walsAlpha": {"description": "Hyperparameter for matrix factoration when implicit feedback type is specified.", "$ref": "DoubleHparamSearchSpace"}, "boosterType": {"description": "Booster type for boosted tree models.", "$ref": "StringHparamSearchSpace"}, "numParallelTree": {"description": "Number of parallel trees for boosted tree models.", "$ref": "IntHparamSearchSpace"}, "dartNormalizeType": {"description": "Dart normalization type for boosted tree models.", "$ref": "StringHparamSearchSpace"}, "treeMethod": {"description": "Tree construction algorithm for boosted tree models.", "$ref": "StringHparamSearchSpace"}, "minTreeChildWeight": {"description": "Minimum sum of instance weight needed in a child for boosted tree models.", "$ref": "IntHparamSearchSpace"}, "colsampleBytree": {"description": "Subsample ratio of columns when constructing each tree for boosted tree models.", "$ref": "DoubleHparamSearchSpace"}, "colsampleBylevel": {"description": "Subsample ratio of columns for each level for boosted tree models.", "$ref": "DoubleHparamSearchSpace"}, "colsampleBynode": {"description": "Subsample ratio of columns for each node(split) for boosted tree models.", "$ref": "DoubleHparamSearchSpace"}, "activationFn": {"description": "Activation functions of neural network models.", "$ref": "StringHparamSearchSpace"}, "optimizer": {"description": "Optimizer of TF models.", "$ref": "StringHparamSearchSpace"}}}, "DoubleHparamSearchSpace": {"id": "DoubleHparamSearchSpace", "description": "Search space for a double hyperparameter.", "type": "object", "properties": {"range": {"description": "Range of the double hyperparameter.", "$ref": "DoubleRange"}, "candidates": {"description": "Candidates of the double hyperparameter.", "$ref": "DoubleCandidates"}}}, "DoubleRange": {"id": "DoubleRange", "description": "Range of a double hyperparameter.", "type": "object", "properties": {"min": {"description": "Min value of the double parameter.", "type": "number", "format": "double"}, "max": {"description": "Max value of the double parameter.", "type": "number", "format": "double"}}}, "DoubleCandidates": {"id": "DoubleCandidates", "description": "Discrete candidates of a double hyperparameter.", "type": "object", "properties": {"candidates": {"description": "Candidates for the double parameter in increasing order.", "type": "array", "items": {"type": "number", "format": "double"}}}}, "IntHparamSearchSpace": {"id": "IntHparamSearchSpace", "description": "Search space for an int hyperparameter.", "type": "object", "properties": {"range": {"description": "Range of the int hyperparameter.", "$ref": "IntRange"}, "candidates": {"description": "Candidates of the int hyperparameter.", "$ref": "IntCandidates"}}}, "IntRange": {"id": "IntRange", "description": "Range of an int hyperparameter.", "type": "object", "properties": {"min": {"description": "Min value of the int parameter.", "type": "string", "format": "int64"}, "max": {"description": "Max value of the int parameter.", "type": "string", "format": "int64"}}}, "IntCandidates": {"id": "IntCandidates", "description": "Discrete candidates of an int hyperparameter.", "type": "object", "properties": {"candidates": {"description": "Candidates for the int parameter in increasing order.", "type": "array", "items": {"type": "string", "format": "int64"}}}}, "IntArrayHparamSearchSpace": {"id": "IntArrayHparamSearchSpace", "description": "Search space for int array.", "type": "object", "properties": {"candidates": {"description": "Candidates for the int array parameter.", "type": "array", "items": {"$ref": "IntArray"}}}}, "IntArray": {"id": "IntArray", "description": "An array of int.", "type": "object", "properties": {"elements": {"description": "Elements in the int array.", "type": "array", "items": {"type": "string", "format": "int64"}}}}, "StringHparamSearchSpace": {"id": "StringHparamSearchSpace", "description": "Search space for string and enum.", "type": "object", "properties": {"candidates": {"description": "Canididates for the string or enum parameter in lower case.", "type": "array", "items": {"type": "string"}}}}, "HparamTuningTrial": {"id": "HparamTuningTrial", "description": "Training info of a trial in [hyperparameter tuning](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) models.", "type": "object", "properties": {"trialId": {"description": "1-based index of the trial.", "type": "string", "format": "int64"}, "startTimeMs": {"description": "Starting time of the trial.", "type": "string", "format": "int64"}, "endTimeMs": {"description": "Ending time of the trial.", "type": "string", "format": "int64"}, "hparams": {"description": "The hyperprameters selected for this trial.", "$ref": "TrainingOptions"}, "evaluationMetrics": {"description": "Evaluation metrics of this trial calculated on the test data. Empty in Job API.", "$ref": "EvaluationMetrics"}, "status": {"description": "The status of the trial.", "type": "string", "enumDescriptions": ["", "Scheduled but not started.", "Running state.", "The trial succeeded.", "The trial failed.", "The trial is infeasible due to the invalid params.", "Trial stopped early because it's not promising."], "enum": ["TRIAL_STATUS_UNSPECIFIED", "NOT_STARTED", "RUNNING", "SUCCEEDED", "FAILED", "INFEASIBLE", "STOPPED_EARLY"]}, "errorMessage": {"description": "Error message for FAILED and INFEASIBLE trial.", "type": "string"}, "trainingLoss": {"description": "Loss computed on the training data at the end of trial.", "type": "number", "format": "double"}, "evalLoss": {"description": "Loss computed on the eval data at the end of trial.", "type": "number", "format": "double"}, "hparamTuningEvaluationMetrics": {"description": "Hyperparameter tuning evaluation metrics of this trial calculated on the eval data. Unlike evaluation_metrics, only the fields corresponding to the hparam_tuning_objectives are set.", "$ref": "EvaluationMetrics"}}}, "RemoteModelInfo": {"id": "RemoteModelInfo", "description": "Remote Model Info", "type": "object", "properties": {"endpoint": {"description": "Output only. The endpoint for remote model.", "readOnly": true, "type": "string"}, "remoteServiceType": {"description": "Output only. The remote service type for remote model.", "readOnly": true, "type": "string", "enumDescriptions": ["Unspecified remote service type.", "V3 Cloud AI Translation API. See more details at [Cloud Translation API] (https://cloud.google.com/translate/docs/reference/rest).", "V1 Cloud AI Vision API See more details at [Cloud Vision API] (https://cloud.google.com/vision/docs/reference/rest).", "V1 Cloud AI Natural Language API. See more details at [REST Resource: documents](https://cloud.google.com/natural-language/docs/reference/rest/v1/documents)."], "enum": ["REMOTE_SERVICE_TYPE_UNSPECIFIED", "CLOUD_AI_TRANSLATE_V3", "CLOUD_AI_VISION_V1", "CLOUD_AI_NATURAL_LANGUAGE_V1"]}, "connection": {"description": "Output only. Fully qualified name of the user-provided connection object of the remote model. Format: ```\"projects/{project_id}/locations/{location_id}/connections/{connection_id}\"```", "readOnly": true, "type": "string"}, "maxBatchingRows": {"description": "Output only. Max number of rows in each batch sent to the remote service. If unset, the number of rows in each batch is set dynamically.", "readOnly": true, "type": "string", "format": "int64"}, "remoteModelVersion": {"description": "Output only. The model version for LLM.", "readOnly": true, "type": "string"}}}, "ListModelsResponse": {"id": "ListModelsResponse", "type": "object", "properties": {"models": {"description": "Models in the requested dataset. Only the following fields are populated: model_reference, model_type, creation_time, last_modified_time and labels.", "type": "array", "items": {"$ref": "Model"}}, "nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}}}, "JobCancelResponse": {"id": "JobCancelResponse", "type": "object", "properties": {"kind": {"description": "The resource type of the response.", "default": "bigquery#jobCancelResponse", "type": "string"}, "job": {"description": "The final state of the job.", "$ref": "Job"}}}, "Job": {"id": "Job", "type": "object", "properties": {"kind": {"description": "Output only. The type of the resource.", "default": "bigquery#job", "readOnly": true, "type": "string"}, "etag": {"description": "Output only. A hash of this resource.", "readOnly": true, "type": "string"}, "id": {"description": "Output only. Opaque ID field of the job.", "readOnly": true, "type": "string"}, "selfLink": {"description": "Output only. A URL that can be used to access the resource again.", "readOnly": true, "type": "string"}, "user_email": {"description": "Output only. Email address of the user who ran the job.", "readOnly": true, "type": "string"}, "configuration": {"description": "Required. Describes the job configuration.", "$ref": "JobConfiguration"}, "jobReference": {"description": "Optional. Reference describing the unique-per-user name of the job.", "$ref": "JobReference"}, "statistics": {"description": "Output only. Information about the job, including starting time and ending time of the job.", "readOnly": true, "$ref": "JobStatistics"}, "status": {"description": "Output only. The status of this job. Examine this value when polling an asynchronous job to see if the job is complete.", "readOnly": true, "$ref": "JobStatus"}, "principal_subject": {"description": "Output only. [Full-projection-only] String representation of identity of requesting party. Populated for both first- and third-party identities. Only present for APIs that support third-party identities.", "readOnly": true, "type": "string"}}}, "JobConfiguration": {"id": "JobConfiguration", "type": "object", "properties": {"jobType": {"description": "Output only. The type of the job. Can be QUERY, LOAD, EXTRACT, COPY or UNKNOWN.", "type": "string"}, "query": {"description": "[Pick one] Configures a query job.", "$ref": "JobConfigurationQuery"}, "load": {"description": "[Pick one] Configures a load job.", "$ref": "JobConfigurationLoad"}, "copy": {"description": "[Pick one] Copies a table.", "$ref": "JobConfigurationTableCopy"}, "extract": {"description": "[Pick one] Configures an extract job.", "$ref": "JobConfigurationExtract"}, "dryRun": {"description": "Optional. If set, don't actually run this job. A valid query will return a mostly empty response with some processing statistics, while an invalid query will return the same error it would if it wasn't a dry run. Behavior of non-query jobs is undefined.", "type": "boolean"}, "jobTimeoutMs": {"description": "Optional. Job timeout in milliseconds. If this time limit is exceeded, <PERSON><PERSON><PERSON><PERSON> might attempt to stop the job.", "type": "string", "format": "int64"}, "labels": {"description": "The labels associated with this job. You can use these to organize and group your jobs. Label keys and values can be no longer than 63 characters, can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter and each label in the list must have a different key.", "type": "object", "additionalProperties": {"type": "string"}}}}, "JobConfigurationQuery": {"id": "JobConfigurationQuery", "description": "JobConfigurationQuery configures a BigQuery query job.", "type": "object", "properties": {"query": {"description": "[Required] SQL query text to execute. The useLegacySql field can be used to indicate whether the query uses legacy SQL or GoogleSQL.", "type": "string"}, "destinationTable": {"description": "Optional. Describes the table where the query results should be stored. This property must be set for large results that exceed the maximum response size. For queries that produce anonymous (cached) results, this field will be populated by BigQuery.", "$ref": "TableReference"}, "tableDefinitions": {"description": "Optional. You can specify external table definitions, which operate as ephemeral tables that can be queried. These definitions are configured using a JSON map, where the string key represents the table identifier, and the value is the corresponding external data configuration object.", "type": "object", "additionalProperties": {"$ref": "ExternalDataConfiguration"}}, "userDefinedFunctionResources": {"description": "Describes user-defined function resources used in the query.", "type": "array", "items": {"$ref": "UserDefinedFunctionResource"}}, "createDisposition": {"description": "Optional. Specifies whether the job is allowed to create new tables. The following values are supported: * CREATE_IF_NEEDED: If the table does not exist, BigQuery creates the table. * CREATE_NEVER: The table must already exist. If it does not, a 'notFound' error is returned in the job result. The default value is CREATE_IF_NEEDED. Creation, truncation and append actions occur as one atomic update upon job completion.", "type": "string"}, "writeDisposition": {"description": "Optional. Specifies the action that occurs if the destination table already exists. The following values are supported: * WRITE_TRUNCATE: If the table already exists, <PERSON><PERSON><PERSON><PERSON> overwrites the data, removes the constraints, and uses the schema from the query result. * WRITE_APPEND: If the table already exists, <PERSON><PERSON><PERSON><PERSON> appends the data to the table. * WRITE_EMPTY: If the table already exists and contains data, a 'duplicate' error is returned in the job result. The default value is WRITE_EMPTY. Each action is atomic and only occurs if <PERSON><PERSON><PERSON><PERSON> is able to complete the job successfully. Creation, truncation and append actions occur as one atomic update upon job completion.", "type": "string"}, "defaultDataset": {"description": "Optional. Specifies the default dataset to use for unqualified table names in the query. This setting does not alter behavior of unqualified dataset names. Setting the system variable @@dataset_id achieves the same behavior.", "$ref": "DatasetReference"}, "priority": {"description": "Optional. Specifies a priority for the query. Possible values include INTERACTIVE and BATCH. The default value is INTERACTIVE.", "type": "string"}, "preserveNulls": {"description": "[Deprecated] This property is deprecated.", "type": "boolean"}, "allowLargeResults": {"description": "Optional. If true and query uses legacy SQL dialect, allows the query to produce arbitrarily large result tables at a slight cost in performance. Requires destinationTable to be set. For GoogleSQL queries, this flag is ignored and large results are always allowed. However, you must still set destinationTable when result size exceeds the allowed maximum response size.", "default": "false", "type": "boolean"}, "useQueryCache": {"description": "Optional. Whether to look for the result in the query cache. The query cache is a best-effort cache that will be flushed whenever tables in the query are modified. Moreover, the query cache is only available when a query does not have a destination table specified. The default value is true.", "default": "true", "type": "boolean"}, "flattenResults": {"description": "Optional. If true and query uses legacy SQL dialect, flattens all nested and repeated fields in the query results. allowLargeResults must be true if this is set to false. For GoogleSQL queries, this flag is ignored and results are never flattened.", "default": "true", "type": "boolean"}, "maximumBillingTier": {"description": "Optional. [Deprecated] Maximum billing tier allowed for this query. The billing tier controls the amount of compute resources allotted to the query, and multiplies the on-demand cost of the query accordingly. A query that runs within its allotted resources will succeed and indicate its billing tier in statistics.query.billingTier, but if the query exceeds its allotted resources, it will fail with billingTierLimitExceeded. WARNING: The billed byte amount can be multiplied by an amount up to this number! Most users should not need to alter this setting, and we recommend that you avoid introducing new uses of it.", "default": "1", "type": "integer", "format": "int32"}, "maximumBytesBilled": {"description": "Limits the bytes billed for this job. Queries that will have bytes billed beyond this limit will fail (without incurring a charge). If unspecified, this will be set to your project default.", "type": "string", "format": "int64"}, "useLegacySql": {"description": "Optional. Specifies whether to use BigQuery's legacy SQL dialect for this query. The default value is true. If set to false, the query will use BigQuery's GoogleSQL: https://cloud.google.com/bigquery/sql-reference/ When useLegacySql is set to false, the value of flattenResults is ignored; query will be run as if flattenResults is false.", "default": "true", "type": "boolean"}, "parameterMode": {"description": "GoogleSQL only. Set to POSITIONAL to use positional (?) query parameters or to NAMED to use named (@myparam) query parameters in this query.", "type": "string"}, "queryParameters": {"description": "Query parameters for GoogleSQL queries.", "type": "array", "items": {"$ref": "QueryParameter"}}, "systemVariables": {"description": "Output only. System variables for GoogleSQL queries. A system variable is output if the variable is settable and its value differs from the system default. \"@@\" prefix is not included in the name of the System variables.", "readOnly": true, "$ref": "SystemVariables"}, "schemaUpdateOptions": {"description": "Allows the schema of the destination table to be updated as a side effect of the query job. Schema update options are supported in two cases: when writeDisposition is WRITE_APPEND; when writeDisposition is WRITE_TRUNCATE and the destination table is a partition of a table, specified by partition decorators. For normal tables, WRITE_TRUNCATE will always overwrite the schema. One or more of the following values are specified: * ALLOW_FIELD_ADDITION: allow adding a nullable field to the schema. * ALLOW_FIELD_RELAXATION: allow relaxing a required field in the original schema to nullable.", "type": "array", "items": {"type": "string"}}, "timePartitioning": {"description": "Time-based partitioning specification for the destination table. Only one of timePartitioning and rangePartitioning should be specified.", "$ref": "TimePartitioning"}, "rangePartitioning": {"description": "Range partitioning specification for the destination table. Only one of timePartitioning and rangePartitioning should be specified.", "$ref": "RangePartitioning"}, "clustering": {"description": "Clustering specification for the destination table.", "$ref": "Clustering"}, "destinationEncryptionConfiguration": {"description": "Custom encryption configuration (e.g., Cloud KMS keys)", "$ref": "EncryptionConfiguration"}, "scriptOptions": {"description": "Options controlling the execution of scripts.", "$ref": "ScriptOptions"}, "connectionProperties": {"description": "Connection properties which can modify the query behavior.", "type": "array", "items": {"$ref": "ConnectionProperty"}}, "createSession": {"description": "If this property is true, the job creates a new session using a randomly generated session_id. To continue using a created session with subsequent queries, pass the existing session identifier as a `ConnectionProperty` value. The session identifier is returned as part of the `SessionInfo` message within the query statistics. The new session's location will be set to `Job.JobReference.location` if it is present, otherwise it's set to the default location based on existing routing logic.", "type": "boolean"}}}, "ExternalDataConfiguration": {"id": "ExternalDataConfiguration", "type": "object", "properties": {"sourceUris": {"description": "[Required] The fully-qualified URIs that point to your data in Google Cloud. For Google Cloud Storage URIs: Each URI can contain one '*' wildcard character and it must come after the 'bucket' name. Size limits related to load jobs apply to external data sources. For Google Cloud Bigtable URIs: Exactly one URI can be specified and it has be a fully specified and valid HTTPS URL for a Google Cloud Bigtable table. For Google Cloud Datastore backups, exactly one URI can be specified. Also, the '*' wildcard character is not allowed.", "type": "array", "items": {"type": "string"}}, "fileSetSpecType": {"description": "Optional. Specifies how source URIs are interpreted for constructing the file set to load. By default source URIs are expanded against the underlying storage. Other options include specifying manifest files. Only applicable to object storage systems.", "type": "string", "enumDescriptions": ["This option expands source URIs by listing files from the object store. It is the default behavior if FileSetSpecType is not set.", "This option indicates that the provided URIs are newline-delimited manifest files, with one URI per line. Wildcard URIs are not supported."], "enum": ["FILE_SET_SPEC_TYPE_FILE_SYSTEM_MATCH", "FILE_SET_SPEC_TYPE_NEW_LINE_DELIMITED_MANIFEST"]}, "schema": {"description": "Optional. The schema for the data. Schema is required for CSV and JSON formats if autodetect is not on. Schema is disallowed for Google Cloud Bigtable, Cloud Datastore backups, Avro, ORC and Parquet formats.", "$ref": "TableSchema"}, "sourceFormat": {"description": "[Required] The data format. For CSV files, specify \"CSV\". For Google sheets, specify \"GOOGLE_SHEETS\". For newline-delimited JSON, specify \"NEWLINE_DELIMITED_JSON\". For Avro files, specify \"AVRO\". For Google Cloud Datastore backups, specify \"DATASTORE_BACKUP\". For Apache Iceberg tables, specify \"ICEBERG\". For ORC files, specify \"ORC\". For Parquet files, specify \"PARQUET\". [Beta] For Google Cloud Bigtable, specify \"BIGTABLE\".", "type": "string"}, "maxBadRecords": {"description": "Optional. The maximum number of bad records that BigQuery can ignore when reading data. If the number of bad records exceeds this value, an invalid error is returned in the job result. The default value is 0, which requires that all records are valid. This setting is ignored for Google Cloud Bigtable, Google Cloud Datastore backups, Avro, ORC and Parquet formats.", "type": "integer", "format": "int32"}, "autodetect": {"description": "Try to detect schema and format options automatically. Any option specified explicitly will be honored.", "type": "boolean"}, "ignoreUnknownValues": {"description": "Optional. Indicates if BigQuery should allow extra values that are not represented in the table schema. If true, the extra values are ignored. If false, records with extra columns are treated as bad records, and if there are too many bad records, an invalid error is returned in the job result. The default value is false. The sourceFormat property determines what BigQuery treats as an extra value: CSV: Trailing columns JSON: Named values that don't match any column names Google Cloud Bigtable: This setting is ignored. Google Cloud Datastore backups: This setting is ignored. Avro: This setting is ignored. ORC: This setting is ignored. <PERSON><PERSON><PERSON>: This setting is ignored.", "type": "boolean"}, "compression": {"description": "Optional. The compression type of the data source. Possible values include GZIP and NONE. The default value is NONE. This setting is ignored for Google Cloud Bigtable, Google Cloud Datastore backups, Avro, ORC and Parquet formats. An empty string is an invalid value.", "type": "string"}, "csvOptions": {"description": "Optional. Additional properties to set if sourceFormat is set to CSV.", "$ref": "CsvOptions"}, "jsonOptions": {"description": "Optional. Additional properties to set if sourceFormat is set to JSON.", "$ref": "JsonOptions"}, "bigtableOptions": {"description": "Optional. Additional options if sourceFormat is set to BIGTABLE.", "$ref": "BigtableOptions"}, "googleSheetsOptions": {"description": "Optional. Additional options if sourceFormat is set to GOOGLE_SHEETS.", "$ref": "GoogleSheetsOptions"}, "hivePartitioningOptions": {"description": "Optional. When set, configures hive partitioning support. Not all storage formats support hive partitioning -- requesting hive partitioning on an unsupported format will lead to an error, as will providing an invalid specification.", "$ref": "HivePartitioningOptions"}, "connectionId": {"description": "Optional. The connection specifying the credentials to be used to read external storage, such as Azure Blob, Cloud Storage, or S3. The connection_id can have the form \"<project\\_id>.<location\\_id>.<connection\\_id>\" or \"projects/<project\\_id>/locations/<location\\_id>/connections/<connection\\_id>\".", "type": "string"}, "decimalTargetTypes": {"description": "Defines the list of possible SQL data types to which the source decimal values are converted. This list and the precision and the scale parameters of the decimal field determine the target type. In the order of NUMERIC, BIGNUMERIC, and STRING, a type is picked if it is in the specified list and if it supports the precision and the scale. STRING supports all precision and scale values. If none of the listed types supports the precision and the scale, the type supporting the widest range in the specified list is picked, and if a value exceeds the supported range when reading the data, an error will be thrown. Example: Suppose the value of this field is [\"NUMER<PERSON>\", \"BIGNUMERI<PERSON>\"]. If (precision,scale) is: * (38,9) -> NUMERIC; * (39,9) -> BIGNUMERIC (NUMERIC cannot hold 30 integer digits); * (38,10) -> BIGNUMERIC (NUMERIC cannot hold 10 fractional digits); * (76,38) -> BIGNUMERIC; * (77,38) -> BIGNUMERI<PERSON> (error if value exeeds supported range). This field cannot contain duplicate types. The order of the types in this field is ignored. For example, [\"<PERSON>IG<PERSON><PERSON><PERSON><PERSON>\", \"NUMER<PERSON>\"] is the same as [\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON>IGNUME<PERSON><PERSON>\"] and NUMERIC always takes precedence over BIGNUMERIC. Defaults to [\"<PERSON><PERSON><PERSON><PERSON>\", \"STRING\"] for ORC and [\"N<PERSON>ER<PERSON>\"] for the other file formats.", "type": "array", "items": {"type": "string", "enumDescriptions": ["Invalid type.", "Decimal values could be converted to NUMERIC type.", "Decimal values could be converted to BIGNUMERIC type.", "Decimal values could be converted to STRING type."], "enum": ["DECIMAL_TARGET_TYPE_UNSPECIFIED", "NUMERIC", "BIGNUMERIC", "STRING"]}}, "avroOptions": {"description": "Optional. Additional properties to set if sourceFormat is set to AVRO.", "$ref": "AvroOptions"}, "jsonExtension": {"description": "Optional. Load option to be used together with source_format newline-delimited JSON to indicate that a variant of JSON is being loaded. To load newline-delimited GeoJSON, specify GEOJSON (and source_format must be set to NEWLINE_DELIMITED_JSON).", "type": "string", "enumDescriptions": ["The default if provided value is not one included in the enum, or the value is not specified. The source formate is parsed without any modification.", "Use GeoJSON variant of JSON. See https://tools.ietf.org/html/rfc7946."], "enum": ["JSON_EXTENSION_UNSPECIFIED", "GEOJSON"]}, "parquetOptions": {"description": "Optional. Additional properties to set if sourceFormat is set to PARQUET.", "$ref": "ParquetOptions"}, "objectMetadata": {"description": "Optional. ObjectMetadata is used to create Object Tables. Object Tables contain a listing of objects (with their metadata) found at the source_uris. If ObjectMetadata is set, source_format should be omitted. Currently SIMPLE is the only supported Object Metadata type.", "type": "string", "enumDescriptions": ["Unspecified by default.", "A synonym for `SIMPLE`.", "Directory listing of objects."], "enum": ["OBJECT_METADATA_UNSPECIFIED", "DIRECTORY", "SIMPLE"]}, "referenceFileSchemaUri": {"description": "Optional. When creating an external table, the user can provide a reference file with the table schema. This is enabled for the following formats: AVRO, PARQUET, ORC.", "type": "string"}, "metadataCacheMode": {"description": "Optional. Metadata Cache Mode for the table. Set this to enable caching of metadata from external data source.", "type": "string", "enumDescriptions": ["Unspecified metadata cache mode.", "Set this mode to trigger automatic background refresh of metadata cache from the external source. Queries will use the latest available cache version within the table's maxStaleness interval.", "Set this mode to enable triggering manual refresh of the metadata cache from external source. Queries will use the latest manually triggered cache version within the table's maxStaleness interval."], "enum": ["METADATA_CACHE_MODE_UNSPECIFIED", "AUTOMATIC", "MANUAL"]}}}, "TableSchema": {"id": "TableSchema", "description": "Schema of a table", "type": "object", "properties": {"fields": {"description": "Describes the fields in a table.", "type": "array", "items": {"$ref": "TableFieldSchema"}}}}, "TableFieldSchema": {"id": "TableFieldSchema", "description": "A field in TableSchema", "type": "object", "properties": {"name": {"description": "Required. The field name. The name must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_), and must start with a letter or underscore. The maximum length is 300 characters.", "type": "string"}, "type": {"description": "Required. The field data type. Possible values include: * STRING * BYTES * INTEGER (or INT64) * FLOAT (or FLOAT64) * BOOLEAN (or BOOL) * TIMESTAMP * DATE * TIME * DATETIME * GEOGRAPHY * NUMERIC * BIGNUMERIC * JSON * RECORD (or STRUCT) Use of RECORD/STRUCT indicates that the field contains a nested schema.", "type": "string"}, "mode": {"description": "Optional. The field mode. Possible values include NULL<PERSON>LE, REQUIRED and REPEATED. The default value is NULLABLE.", "type": "string"}, "fields": {"description": "Optional. Describes the nested schema fields if the type property is set to RECORD.", "type": "array", "items": {"$ref": "TableFieldSchema"}}, "description": {"description": "Optional. The field description. The maximum length is 1,024 characters.", "type": "string"}, "policyTags": {"description": "Optional. The policy tags attached to this field, used for field-level access control. If not set, defaults to empty policy_tags.", "type": "object", "properties": {"names": {"description": "A list of policy tag resource names. For example, \"projects/1/locations/eu/taxonomies/2/policyTags/3\". At most 1 policy tag is currently allowed.", "type": "array", "items": {"type": "string"}}}}, "maxLength": {"description": "Optional. Maximum length of values of this field for STRINGS or BYTES. If max_length is not specified, no maximum length constraint is imposed on this field. If type = \"STRING\", then max_length represents the maximum UTF-8 length of strings in this field. If type = \"BYTES\", then max_length represents the maximum number of bytes in this field. It is invalid to set this field if type ≠ \"STRING\" and ≠ \"BYTES\".", "type": "string", "format": "int64"}, "precision": {"description": "Optional. Precision (maximum number of total digits in base 10) and scale (maximum number of digits in the fractional part in base 10) constraints for values of this field for NUMERIC or BIGNUMERIC. It is invalid to set precision or scale if type ≠ \"NUMERIC\" and ≠ \"BIG<PERSON>MERI<PERSON>\". If precision and scale are not specified, no value range constraint is imposed on this field insofar as values are permitted by the type. Values of this NUMERIC or BIGNUMERIC field must be in this range when: * Precision (P) and scale (S) are specified: [-10P-S + 10-S, 10P-S - 10-S] * Precision (P) is specified but not scale (and thus scale is interpreted to be equal to zero): [-10P + 1, 10P - 1]. Acceptable values for precision and scale if both are specified: * If type = \"NUMERIC\": 1 ≤ precision - scale ≤ 29 and 0 ≤ scale ≤ 9. * If type = \"BIGNUMERIC\": 1 ≤ precision - scale ≤ 38 and 0 ≤ scale ≤ 38. Acceptable values for precision if only precision is specified but not scale (and thus scale is interpreted to be equal to zero): * If type = \"NUMERIC\": 1 ≤ precision ≤ 29. * If type = \"BIG<PERSON><PERSON>RI<PERSON>\": 1 ≤ precision ≤ 38. If scale is specified but not precision, then it is invalid.", "type": "string", "format": "int64"}, "scale": {"description": "Optional. See documentation for precision.", "type": "string", "format": "int64"}, "roundingMode": {"description": "Optional. Specifies the rounding mode to be used when storing values of NUMERIC and BIGNUMERIC type.", "type": "string", "enumDescriptions": ["Unspecified will default to using ROUND_HALF_AWAY_FROM_ZERO.", "ROUND_HALF_AWAY_FROM_ZERO rounds half values away from zero when applying precision and scale upon writing of NUMERIC and BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1 1.5, 1.6, 1.7, 1.8, 1.9 => 2", "ROUND_HALF_EVEN rounds half values to the nearest even value when applying precision and scale upon writing of NUMERIC and BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1 1.5 => 2 1.6, 1.7, 1.8, 1.9 => 2 2.5 => 2"], "enum": ["ROUNDING_MODE_UNSPECIFIED", "ROUND_HALF_AWAY_FROM_ZERO", "ROUND_HALF_EVEN"]}, "collation": {"description": "Optional. Field collation can be set only when the type of field is STRING. The following values are supported: * 'und:ci': undetermined locale, case insensitive. * '': empty string. Default to case-sensitive behavior.", "type": "string"}, "defaultValueExpression": {"description": "Optional. A SQL expression to specify the [default value] (https://cloud.google.com/bigquery/docs/default-values) for this field.", "type": "string"}, "rangeElementType": {"description": "Optional. The subtype of the RANGE, if the type of this field is RANGE. If the type is RANGE, this field is required. Possible values for the field element type of a RANGE include: * DATE * DATETIME * TIMESTAMP", "$ref": "FieldElementType"}}}, "FieldElementType": {"id": "FieldElementType", "description": "Represents the type of a field element.", "type": "object", "properties": {"type": {"description": "Required. The type of a field element. See TableFieldSchema.type.", "type": "string"}}}, "CsvOptions": {"id": "CsvOptions", "type": "object", "properties": {"fieldDelimiter": {"description": "Optional. The separator character for fields in a CSV file. The separator is interpreted as a single byte. For files encoded in ISO-8859-1, any single character can be used as a separator. For files encoded in UTF-8, characters represented in decimal range 1-127 (U+0001-U+007F) can be used without any modification. UTF-8 characters encoded with multiple bytes (i.e. U+0080 and above) will have only the first byte used for separating fields. The remaining bytes will be treated as a part of the field. BigQuery also supports the escape sequence \"\\t\" (U+0009) to specify a tab separator. The default value is comma (\",\", U+002C).", "type": "string"}, "skipLeadingRows": {"description": "Optional. The number of rows at the top of a CSV file that BigQuery will skip when reading the data. The default value is 0. This property is useful if you have header rows in the file that should be skipped. When autodetect is on, the behavior is the following: * skipLeadingRows unspecified - Autodetect tries to detect headers in the first row. If they are not detected, the row is read as data. Otherwise data is read starting from the second row. * skipLeadingRows is 0 - Instructs autodetect that there are no headers and data should be read starting from the first row. * skipLeadingRows = N > 0 - Autodetect skips N-1 rows and tries to detect headers in row N. If headers are not detected, row N is just skipped. Otherwise row N is used to extract column names for the detected schema.", "type": "string", "format": "int64"}, "quote": {"description": "Optional. The value that is used to quote data sections in a CSV file. BigQuery converts the string to ISO-8859-1 encoding, and then uses the first byte of the encoded string to split the data in its raw, binary state. The default value is a double-quote (\"). If your data does not contain quoted sections, set the property value to an empty string. If your data contains quoted newline characters, you must also set the allowQuotedNewlines property to true. To include the specific quote character within a quoted value, precede it with an additional matching quote character. For example, if you want to escape the default character ' \" ', use ' \"\" '.", "pattern": ".?", "default": "\"", "type": "string"}, "allowQuotedNewlines": {"description": "Optional. Indicates if BigQuery should allow quoted data sections that contain newline characters in a CSV file. The default value is false.", "type": "boolean"}, "allowJaggedRows": {"description": "Optional. Indicates if BigQuery should accept rows that are missing trailing optional columns. If true, BigQuery treats missing trailing columns as null values. If false, records with missing trailing columns are treated as bad records, and if there are too many bad records, an invalid error is returned in the job result. The default value is false.", "type": "boolean"}, "encoding": {"description": "Optional. The character encoding of the data. The supported values are UTF-8, ISO-8859-1, UTF-16BE, UTF-16LE, UTF-32BE, and UTF-32LE. The default value is UTF-8. BigQuery decodes the data after the raw, binary data has been split using the values of the quote and fieldDelimiter properties.", "type": "string"}, "preserveAsciiControlCharacters": {"description": "Optional. Indicates if the embedded ASCII control characters (the first 32 characters in the ASCII-table, from '\\x00' to '\\x1F') are preserved.", "type": "boolean"}}}, "JsonOptions": {"id": "JsonOptions", "description": "Json Options for load and make external tables.", "type": "object", "properties": {"encoding": {"description": "Optional. The character encoding of the data. The supported values are UTF-8, UTF-16BE, UTF-16LE, UTF-32BE, and UTF-32LE. The default value is UTF-8.", "type": "string"}}}, "BigtableOptions": {"id": "BigtableOptions", "type": "object", "properties": {"columnFamilies": {"description": "Optional. List of column families to expose in the table schema along with their types. This list restricts the column families that can be referenced in queries and specifies their value types. You can use this list to do type conversions - see the 'type' field for more details. If you leave this list empty, all column families are present in the table schema and their values are read as BYTES. During a query only the column families referenced in that query are read from Bigtable.", "type": "array", "items": {"$ref": "BigtableColumnFamily"}}, "ignoreUnspecifiedColumnFamilies": {"description": "Optional. If field is true, then the column families that are not specified in columnFamilies list are not exposed in the table schema. Otherwise, they are read with BYTES type values. The default value is false.", "type": "boolean"}, "readRowkeyAsString": {"description": "Optional. If field is true, then the rowkey column families will be read and converted to string. Otherwise they are read with BYTES type values and users need to manually cast them with CAST if necessary. The default value is false.", "type": "boolean"}}}, "BigtableColumnFamily": {"id": "BigtableColumnFamily", "type": "object", "properties": {"familyId": {"description": "Identifier of the column family.", "type": "string"}, "type": {"description": "Optional. The type to convert the value in cells of this column family. The values are expected to be encoded using HBase Bytes.toBytes function when using the BINARY encoding value. Following BigQuery types are allowed (case-sensitive): * BYTES * STRING * INTEGER * FLOAT * BOOLEAN * JSO<PERSON> Default type is BYTES. This can be overridden for a specific column by listing that column in 'columns' and specifying a type for it.", "type": "string"}, "encoding": {"description": "Optional. The encoding of the values when the type is not STRING. Acceptable encoding values are: TEXT - indicates values are alphanumeric text strings. BINARY - indicates values are encoded using HBase Bytes.toBytes family of functions. This can be overridden for a specific column by listing that column in 'columns' and specifying an encoding for it.", "type": "string"}, "columns": {"description": "Optional. Lists of columns that should be exposed as individual fields as opposed to a list of (column name, value) pairs. All columns whose qualifier matches a qualifier in this list can be accessed as .. Other columns can be accessed as a list through .Column field.", "type": "array", "items": {"$ref": "BigtableColumn"}}, "onlyReadLatest": {"description": "Optional. If this is set only the latest version of value are exposed for all columns in this column family. This can be overridden for a specific column by listing that column in 'columns' and specifying a different setting for that column.", "type": "boolean"}}}, "BigtableColumn": {"id": "BigtableColumn", "type": "object", "properties": {"qualifierEncoded": {"description": "[Required] Qualifier of the column. Columns in the parent column family that has this exact qualifier are exposed as . field. If the qualifier is valid UTF-8 string, it can be specified in the qualifier_string field. Otherwise, a base-64 encoded value must be set to qualifier_encoded. The column field name is the same as the column qualifier. However, if the qualifier is not a valid BigQuery field identifier i.e. does not match a-zA-Z*, a valid identifier must be provided as field_name.", "type": "string", "format": "byte"}, "qualifierString": {"type": "string"}, "fieldName": {"description": "Optional. If the qualifier is not a valid BigQuery field identifier i.e. does not match a-zA-Z*, a valid identifier must be provided as the column field name and is used as field name in queries.", "type": "string"}, "type": {"description": "Optional. The type to convert the value in cells of this column. The values are expected to be encoded using HBase Bytes.toBytes function when using the BINARY encoding value. Following BigQuery types are allowed (case-sensitive): * BYTES * STRING * INTEGER * FLOAT * BOOLEAN * JSO<PERSON> Default type is BYTES. 'type' can also be set at the column family level. However, the setting at this level takes precedence if 'type' is set at both levels.", "type": "string"}, "encoding": {"description": "Optional. The encoding of the values when the type is not STRING. Acceptable encoding values are: TEXT - indicates values are alphanumeric text strings. BINARY - indicates values are encoded using HBase Bytes.toBytes family of functions. 'encoding' can also be set at the column family level. However, the setting at this level takes precedence if 'encoding' is set at both levels.", "type": "string"}, "onlyReadLatest": {"description": "Optional. If this is set, only the latest version of value in this column are exposed. 'onlyReadLatest' can also be set at the column family level. However, the setting at this level takes precedence if 'onlyReadLatest' is set at both levels.", "type": "boolean"}}}, "GoogleSheetsOptions": {"id": "GoogleSheetsOptions", "type": "object", "properties": {"skipLeadingRows": {"description": "Optional. The number of rows at the top of a sheet that BigQuery will skip when reading the data. The default value is 0. This property is useful if you have header rows that should be skipped. When autodetect is on, the behavior is the following: * skipLeadingRows unspecified - Autodetect tries to detect headers in the first row. If they are not detected, the row is read as data. Otherwise data is read starting from the second row. * skipLeadingRows is 0 - Instructs autodetect that there are no headers and data should be read starting from the first row. * skipLeadingRows = N > 0 - Autodetect skips N-1 rows and tries to detect headers in row N. If headers are not detected, row N is just skipped. Otherwise row N is used to extract column names for the detected schema.", "type": "string", "format": "int64"}, "range": {"description": "Optional. Range of a sheet to query from. Only used when non-empty. Typical format: sheet_name!top_left_cell_id:bottom_right_cell_id For example: sheet1!A1:B20", "type": "string"}}}, "HivePartitioningOptions": {"id": "HivePartitioningOptions", "description": "Options for configuring hive partitioning detect.", "type": "object", "properties": {"mode": {"description": "Optional. When set, what mode of hive partitioning to use when reading data. The following modes are supported: * AUTO: automatically infer partition key name(s) and type(s). * STRINGS: automatically infer partition key name(s). All types are strings. * CUSTOM: partition key schema is encoded in the source URI prefix. Not all storage formats support hive partitioning. Requesting hive partitioning on an unsupported format will lead to an error. Currently supported formats are: JSON, CSV, ORC, Avro and Parquet.", "type": "string"}, "sourceUriPrefix": {"description": "Optional. When hive partition detection is requested, a common prefix for all source uris must be required. The prefix must end immediately before the partition key encoding begins. For example, consider files following this data layout: gs://bucket/path_to_table/dt=2019-06-01/country=USA/id=7/file.avro gs://bucket/path_to_table/dt=2019-05-31/country=CA/id=3/file.avro When hive partitioning is requested with either AUTO or STRINGS detection, the common prefix can be either of gs://bucket/path_to_table or gs://bucket/path_to_table/. CUSTOM detection requires encoding the partitioning schema immediately after the common prefix. For CUSTOM, any of * gs://bucket/path_to_table/{dt:DATE}/{country:STRING}/{id:INTEGER} * gs://bucket/path_to_table/{dt:STRING}/{country:STRING}/{id:INTEGER} * gs://bucket/path_to_table/{dt:DATE}/{country:STRING}/{id:STRING} would all be valid source URI prefixes.", "type": "string"}, "requirePartitionFilter": {"description": "Optional. If set to true, queries over this table require a partition filter that can be used for partition elimination to be specified. Note that this field should only be true when creating a permanent external table or querying a temporary external table. Hive-partitioned loads with require_partition_filter explicitly set to true will fail.", "default": "false", "type": "boolean"}, "fields": {"description": "Output only. For permanent external tables, this field is populated with the hive partition keys in the order they were inferred. The types of the partition keys can be deduced by checking the table schema (which will include the partition keys). Not every API will populate this field in the output. For example, Tables.Get will populate it, but Tables.List will not contain this field.", "readOnly": true, "type": "array", "items": {"type": "string"}}}}, "AvroOptions": {"id": "AvroOptions", "description": "Options for external data sources.", "type": "object", "properties": {"useAvroLogicalTypes": {"description": "Optional. If sourceFormat is set to \"AVRO\", indicates whether to interpret logical types as the corresponding BigQuery data type (for example, TIMESTAMP), instead of using the raw type (for example, INTEGER).", "type": "boolean"}}}, "ParquetOptions": {"id": "ParquetOptions", "description": "Parquet Options for load and make external tables.", "type": "object", "properties": {"enumAsString": {"description": "Optional. Indicates whether to infer Parquet ENUM logical type as STRING instead of BYTES by default.", "type": "boolean"}, "enableListInference": {"description": "Optional. Indicates whether to use schema inference specifically for Parquet LIST logical type.", "type": "boolean"}}}, "UserDefinedFunctionResource": {"id": "UserDefinedFunctionResource", "description": " This is used for defining User Defined Function (UDF) resources only when using legacy SQL. Users of GoogleSQL should leverage either DDL (e.g. CREATE [TEMPORARY] FUNCTION ... ) or the Routines API to define UDF resources. For additional information on migrating, see: https://cloud.google.com/bigquery/docs/reference/standard-sql/migrating-from-legacy-sql#differences_in_user-defined_javascript_functions", "type": "object", "properties": {"resourceUri": {"description": "[Pick one] A code resource to load from a Google Cloud Storage URI (gs://bucket/path).", "type": "string"}, "inlineCode": {"description": "[Pick one] An inline resource that contains code for a user-defined function (UDF). Providing a inline code resource is equivalent to providing a URI for a file containing the same code.", "type": "string"}}}, "QueryParameter": {"id": "QueryParameter", "description": "A parameter given to a query.", "type": "object", "properties": {"name": {"description": "Optional. If unset, this is a positional parameter. Otherwise, should be unique within a query.", "type": "string"}, "parameterType": {"description": "Required. The type of this parameter.", "$ref": "QueryParameterType"}, "parameterValue": {"description": "Required. The value of this parameter.", "$ref": "QueryParameterValue"}}}, "QueryParameterType": {"id": "QueryParameterType", "description": "The type of a query parameter.", "type": "object", "properties": {"type": {"description": "Required. The top level type of this field.", "type": "string"}, "arrayType": {"description": "Optional. The type of the array's elements, if this is an array.", "$ref": "QueryParameterType"}, "structTypes": {"description": "Optional. The types of the fields of this struct, in order, if this is a struct.", "type": "array", "items": {"description": "The type of a struct parameter.", "type": "object", "properties": {"name": {"description": "Optional. The name of this field.", "type": "string"}, "type": {"description": "Required. The type of this field.", "$ref": "QueryParameterType"}, "description": {"description": "Optional. Human-oriented description of the field.", "type": "string"}}}}}}, "QueryParameterValue": {"id": "QueryParameterValue", "description": "The value of a query parameter.", "type": "object", "properties": {"value": {"description": "Optional. The value of this value, if a simple scalar type.", "type": "string"}, "arrayValues": {"description": "Optional. The array values, if this is an array type.", "type": "array", "items": {"$ref": "QueryParameterValue"}}, "structValues": {"description": "The struct field values.", "type": "object", "additionalProperties": {"$ref": "QueryParameterValue"}}}}, "SystemVariables": {"id": "SystemVariables", "description": "System variables given to a query.", "type": "object", "properties": {"types": {"description": "Output only. Data type for each system variable.", "readOnly": true, "type": "object", "additionalProperties": {"$ref": "StandardSqlDataType"}}, "values": {"description": "Output only. Value for each system variable.", "readOnly": true, "type": "object", "additionalProperties": {"type": "any", "description": "Properties of the object."}}}}, "TimePartitioning": {"id": "TimePartitioning", "type": "object", "properties": {"type": {"description": "Required. The supported types are DAY, HOUR, MONTH, and YEAR, which will generate one partition per day, hour, month, and year, respectively.", "type": "string"}, "expirationMs": {"description": "Optional. Number of milliseconds for which to keep the storage for a partition. A wrapper is used here because 0 is an invalid value.", "type": "string", "format": "int64"}, "field": {"description": "Optional. If not set, the table is partitioned by pseudo column '_PARTITIONTIME'; if set, the table is partitioned by this field. The field must be a top-level TIMESTAMP or DATE field. Its mode must be NULLABLE or REQUIRED. A wrapper is used here because an empty string is an invalid value.", "type": "string"}, "requirePartitionFilter": {"description": "If set to true, queries over this table require a partition filter that can be used for partition elimination to be specified. This field is deprecated; please set the field with the same name on the table itself instead. This field needs a wrapper because we want to output the default value, false, if the user explicitly set it.", "default": "false", "deprecated": true, "type": "boolean"}}}, "RangePartitioning": {"id": "RangePartitioning", "type": "object", "properties": {"field": {"description": "Required. [Experimental] The table is partitioned by this field. The field must be a top-level NULLABLE/REQUIRED field. The only supported type is INTEGER/INT64.", "type": "string"}, "range": {"description": "[Experimental] Defines the ranges for range partitioning.", "type": "object", "properties": {"start": {"description": "[Experimental] The start of range partitioning, inclusive.", "type": "string", "format": "int64"}, "end": {"description": "[Experimental] The end of range partitioning, exclusive.", "type": "string", "format": "int64"}, "interval": {"description": "[Experimental] The width of each interval.", "type": "string", "format": "int64"}}}}}, "Clustering": {"id": "Clustering", "description": "Configures table clustering.", "type": "object", "properties": {"fields": {"description": "One or more fields on which data should be clustered. Only top-level, non-repeated, simple-type fields are supported. The ordering of the clustering fields should be prioritized from most to least important for filtering purposes. Additional information on limitations can be found here: https://cloud.google.com/bigquery/docs/creating-clustered-tables#limitations", "type": "array", "items": {"type": "string"}}}}, "ScriptOptions": {"id": "ScriptOptions", "type": "object", "properties": {"statementTimeoutMs": {"description": "Timeout period for each statement in a script.", "type": "string", "format": "int64"}, "statementByteBudget": {"description": "Limit on the number of bytes billed per statement. Exceeding this budget results in an error.", "type": "string", "format": "int64"}, "keyResultStatement": {"description": "Determines which statement in the script represents the \"key result\", used to populate the schema and query results of the script job. Default is LAST.", "type": "string", "enumDescriptions": ["", "", ""], "enum": ["KEY_RESULT_STATEMENT_KIND_UNSPECIFIED", "LAST", "FIRST_SELECT"]}}}, "ConnectionProperty": {"id": "ConnectionProperty", "description": "A connection-level property to customize query behavior. Under JDBC, these correspond directly to connection properties passed to the DriverManager. Under ODBC, these correspond to properties in the connection string. Currently supported connection properties: * **dataset_project_id**: represents the default project for datasets that are used in the query. Setting the system variable `@@dataset_project_id` achieves the same behavior. * **time_zone**: represents the default timezone used to run the query. * **session_id**: associates the query with a given session. * **query_label**: associates the query with a given job label. If set, all subsequent queries in a script or session will have this label. For the format in which a you can specify a query label, see labels in the JobConfiguration resource type. Additional properties are allowed, but ignored. Specifying multiple connection properties with the same key returns an error.", "type": "object", "properties": {"key": {"description": "The key of the property to set.", "type": "string"}, "value": {"description": "The value of the property to set.", "type": "string"}}}, "JobConfigurationLoad": {"id": "JobConfigurationLoad", "description": "JobConfigurationLoad contains the configuration properties for loading data into a destination table.", "type": "object", "properties": {"sourceUris": {"description": "[Required] The fully-qualified URIs that point to your data in Google Cloud. For Google Cloud Storage URIs: Each URI can contain one '*' wildcard character and it must come after the 'bucket' name. Size limits related to load jobs apply to external data sources. For Google Cloud Bigtable URIs: Exactly one URI can be specified and it has be a fully specified and valid HTTPS URL for a Google Cloud Bigtable table. For Google Cloud Datastore backups: Exactly one URI can be specified. Also, the '*' wildcard character is not allowed.", "type": "array", "items": {"type": "string"}}, "fileSetSpecType": {"description": "Optional. Specifies how source URIs are interpreted for constructing the file set to load. By default, source URIs are expanded against the underlying storage. You can also specify manifest files to control how the file set is constructed. This option is only applicable to object storage systems.", "type": "string", "enumDescriptions": ["This option expands source URIs by listing files from the object store. It is the default behavior if FileSetSpecType is not set.", "This option indicates that the provided URIs are newline-delimited manifest files, with one URI per line. Wildcard URIs are not supported."], "enum": ["FILE_SET_SPEC_TYPE_FILE_SYSTEM_MATCH", "FILE_SET_SPEC_TYPE_NEW_LINE_DELIMITED_MANIFEST"]}, "schema": {"description": "Optional. The schema for the destination table. The schema can be omitted if the destination table already exists, or if you're loading data from Google Cloud Datastore.", "$ref": "TableSchema"}, "destinationTable": {"description": "[Required] The destination table to load the data into.", "$ref": "TableReference"}, "destinationTableProperties": {"description": "Optional. [Experimental] Properties with which to create the destination table if it is new.", "$ref": "DestinationTableProperties"}, "createDisposition": {"description": "Optional. Specifies whether the job is allowed to create new tables. The following values are supported: * CREATE_IF_NEEDED: If the table does not exist, BigQuery creates the table. * CREATE_NEVER: The table must already exist. If it does not, a 'notFound' error is returned in the job result. The default value is CREATE_IF_NEEDED. Creation, truncation and append actions occur as one atomic update upon job completion.", "type": "string"}, "writeDisposition": {"description": "Optional. Specifies the action that occurs if the destination table already exists. The following values are supported: * WRITE_TRUNCATE: If the table already exists, <PERSON><PERSON><PERSON><PERSON> overwrites the data, removes the constraints and uses the schema from the load job. * WRITE_APPEND: If the table already exists, <PERSON>Query appends the data to the table. * WRITE_EMPTY: If the table already exists and contains data, a 'duplicate' error is returned in the job result. The default value is WRITE_APPEND. Each action is atomic and only occurs if <PERSON><PERSON><PERSON><PERSON> is able to complete the job successfully. Creation, truncation and append actions occur as one atomic update upon job completion.", "type": "string"}, "nullMarker": {"description": "Optional. Specifies a string that represents a null value in a CSV file. For example, if you specify \"\\N\", BigQuery interprets \"\\N\" as a null value when loading a CSV file. The default value is the empty string. If you set this property to a custom value, BigQuery throws an error if an empty string is present for all data types except for STRING and BYTE. For STRING and BYTE columns, BigQuery interprets the empty string as an empty value.", "type": "string"}, "fieldDelimiter": {"description": "Optional. The separator character for fields in a CSV file. The separator is interpreted as a single byte. For files encoded in ISO-8859-1, any single character can be used as a separator. For files encoded in UTF-8, characters represented in decimal range 1-127 (U+0001-U+007F) can be used without any modification. UTF-8 characters encoded with multiple bytes (i.e. U+0080 and above) will have only the first byte used for separating fields. The remaining bytes will be treated as a part of the field. BigQuery also supports the escape sequence \"\\t\" (U+0009) to specify a tab separator. The default value is comma (\",\", U+002C).", "type": "string"}, "skipLeadingRows": {"description": "Optional. The number of rows at the top of a CSV file that BigQuery will skip when loading the data. The default value is 0. This property is useful if you have header rows in the file that should be skipped. When autodetect is on, the behavior is the following: * skipLeadingRows unspecified - Autodetect tries to detect headers in the first row. If they are not detected, the row is read as data. Otherwise data is read starting from the second row. * skipLeadingRows is 0 - Instructs autodetect that there are no headers and data should be read starting from the first row. * skipLeadingRows = N > 0 - Autodetect skips N-1 rows and tries to detect headers in row N. If headers are not detected, row N is just skipped. Otherwise row N is used to extract column names for the detected schema.", "type": "integer", "format": "int32"}, "encoding": {"description": "Optional. The character encoding of the data. The supported values are UTF-8, ISO-8859-1, UTF-16BE, UTF-16LE, UTF-32BE, and UTF-32LE. The default value is UTF-8. BigQuery decodes the data after the raw, binary data has been split using the values of the `quote` and `fieldDelimiter` properties. If you don't specify an encoding, or if you specify a UTF-8 encoding when the CSV file is not UTF-8 encoded, BigQuery attempts to convert the data to UTF-8. Generally, your data loads successfully, but it may not match byte-for-byte what you expect. To avoid this, specify the correct encoding by using the `--encoding` flag. If BigQuery can't convert a character other than the ASCII `0` character, BigQuery converts the character to the standard Unicode replacement character: �.", "type": "string"}, "quote": {"description": "Optional. The value that is used to quote data sections in a CSV file. BigQuery converts the string to ISO-8859-1 encoding, and then uses the first byte of the encoded string to split the data in its raw, binary state. The default value is a double-quote ('\"'). If your data does not contain quoted sections, set the property value to an empty string. If your data contains quoted newline characters, you must also set the allowQuotedNewlines property to true. To include the specific quote character within a quoted value, precede it with an additional matching quote character. For example, if you want to escape the default character ' \" ', use ' \"\" '. @default \"", "pattern": ".?", "default": "\"", "type": "string"}, "maxBadRecords": {"description": "Optional. The maximum number of bad records that <PERSON><PERSON><PERSON><PERSON> can ignore when running the job. If the number of bad records exceeds this value, an invalid error is returned in the job result. The default value is 0, which requires that all records are valid. This is only supported for CSV and NEWLINE_DELIMITED_JSON file formats.", "type": "integer", "format": "int32"}, "schemaInlineFormat": {"description": "[Deprecated] The format of the schemaInline property.", "type": "string"}, "schemaInline": {"description": "[Deprecated] The inline schema. For CSV schemas, specify as \"Field1:Type1[,Field2:Type2]*\". For example, \"foo:STRING, bar:INTEGER, baz:FLOAT\".", "type": "string"}, "allowQuotedNewlines": {"description": "Indicates if BigQuery should allow quoted data sections that contain newline characters in a CSV file. The default value is false.", "type": "boolean"}, "sourceFormat": {"description": "Optional. The format of the data files. For CSV files, specify \"CSV\". For datastore backups, specify \"DATASTORE_BACKUP\". For newline-delimited JSON, specify \"NEWLINE_DELIMITED_JSON\". For Avro, specify \"AVRO\". For parquet, specify \"PARQUET\". For orc, specify \"ORC\". The default value is CSV.", "type": "string"}, "allowJaggedRows": {"description": "Optional. Accept rows that are missing trailing optional columns. The missing values are treated as nulls. If false, records with missing trailing columns are treated as bad records, and if there are too many bad records, an invalid error is returned in the job result. The default value is false. Only applicable to CSV, ignored for other formats.", "type": "boolean"}, "ignoreUnknownValues": {"description": "Optional. Indicates if BigQuery should allow extra values that are not represented in the table schema. If true, the extra values are ignored. If false, records with extra columns are treated as bad records, and if there are too many bad records, an invalid error is returned in the job result. The default value is false. The sourceFormat property determines what BigQuery treats as an extra value: CSV: Trailing columns JSON: Named values that don't match any column names in the table schema Avro, Parquet, ORC: Fields in the file schema that don't exist in the table schema.", "type": "boolean"}, "projectionFields": {"description": "If sourceFormat is set to \"DATASTORE_BACKUP\", indicates which entity properties to load into BigQuery from a Cloud Datastore backup. Property names are case sensitive and must be top-level properties. If no properties are specified, BigQuery loads all properties. If any named property isn't found in the Cloud Datastore backup, an invalid error is returned in the job result.", "type": "array", "items": {"type": "string"}}, "autodetect": {"description": "Optional. Indicates if we should automatically infer the options and schema for CSV and JSON sources.", "type": "boolean"}, "schemaUpdateOptions": {"description": "Allows the schema of the destination table to be updated as a side effect of the load job if a schema is autodetected or supplied in the job configuration. Schema update options are supported in two cases: when writeDisposition is WRITE_APPEND; when writeDisposition is WRITE_TRUNCATE and the destination table is a partition of a table, specified by partition decorators. For normal tables, WRITE_TRUNCATE will always overwrite the schema. One or more of the following values are specified: * ALLOW_FIELD_ADDITION: allow adding a nullable field to the schema. * ALLOW_FIELD_RELAXATION: allow relaxing a required field in the original schema to nullable.", "type": "array", "items": {"type": "string"}}, "timePartitioning": {"description": "Time-based partitioning specification for the destination table. Only one of timePartitioning and rangePartitioning should be specified.", "$ref": "TimePartitioning"}, "rangePartitioning": {"description": "Range partitioning specification for the destination table. Only one of timePartitioning and rangePartitioning should be specified.", "$ref": "RangePartitioning"}, "clustering": {"description": "Clustering specification for the destination table.", "$ref": "Clustering"}, "destinationEncryptionConfiguration": {"description": "Custom encryption configuration (e.g., Cloud KMS keys)", "$ref": "EncryptionConfiguration"}, "useAvroLogicalTypes": {"description": "Optional. If sourceFormat is set to \"AVRO\", indicates whether to interpret logical types as the corresponding BigQuery data type (for example, TIMESTAMP), instead of using the raw type (for example, INTEGER).", "type": "boolean"}, "referenceFileSchemaUri": {"description": "Optional. The user can provide a reference file with the reader schema. This file is only loaded if it is part of source URIs, but is not loaded otherwise. It is enabled for the following formats: AVRO, PARQUET, ORC.", "type": "string"}, "hivePartitioningOptions": {"description": "Optional. When set, configures hive partitioning support. Not all storage formats support hive partitioning -- requesting hive partitioning on an unsupported format will lead to an error, as will providing an invalid specification.", "$ref": "HivePartitioningOptions"}, "decimalTargetTypes": {"description": "Defines the list of possible SQL data types to which the source decimal values are converted. This list and the precision and the scale parameters of the decimal field determine the target type. In the order of NUMERIC, BIGNUMERIC, and STRING, a type is picked if it is in the specified list and if it supports the precision and the scale. STRING supports all precision and scale values. If none of the listed types supports the precision and the scale, the type supporting the widest range in the specified list is picked, and if a value exceeds the supported range when reading the data, an error will be thrown. Example: Suppose the value of this field is [\"NUMER<PERSON>\", \"BIGNUMERI<PERSON>\"]. If (precision,scale) is: * (38,9) -> NUMERIC; * (39,9) -> BIGNUMERIC (NUMERIC cannot hold 30 integer digits); * (38,10) -> BIGNUMERIC (NUMERIC cannot hold 10 fractional digits); * (76,38) -> BIGNUMERIC; * (77,38) -> BIGNUMERI<PERSON> (error if value exeeds supported range). This field cannot contain duplicate types. The order of the types in this field is ignored. For example, [\"<PERSON>IG<PERSON><PERSON><PERSON><PERSON>\", \"NUMER<PERSON>\"] is the same as [\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON>IGNUME<PERSON><PERSON>\"] and NUMERIC always takes precedence over BIGNUMERIC. Defaults to [\"<PERSON><PERSON><PERSON><PERSON>\", \"STRING\"] for ORC and [\"N<PERSON>ER<PERSON>\"] for the other file formats.", "type": "array", "items": {"type": "string", "enumDescriptions": ["Invalid type.", "Decimal values could be converted to NUMERIC type.", "Decimal values could be converted to BIGNUMERIC type.", "Decimal values could be converted to STRING type."], "enum": ["DECIMAL_TARGET_TYPE_UNSPECIFIED", "NUMERIC", "BIGNUMERIC", "STRING"]}}, "thriftOptions": {"description": "Optional. [Experimental] The load options for Apache Thrift serialized data. It defines the source of IDL bundle that should be used to be parsed as the schema and deserialization options to parse Thrift data.", "$ref": "ThriftOptions"}, "jsonExtension": {"description": "Optional. Load option to be used together with source_format newline-delimited JSON to indicate that a variant of JSON is being loaded. To load newline-delimited GeoJSON, specify GEOJSON (and source_format must be set to NEWLINE_DELIMITED_JSON).", "type": "string", "enumDescriptions": ["The default if provided value is not one included in the enum, or the value is not specified. The source formate is parsed without any modification.", "Use GeoJSON variant of JSON. See https://tools.ietf.org/html/rfc7946."], "enum": ["JSON_EXTENSION_UNSPECIFIED", "GEOJSON"]}, "parquetOptions": {"description": "Optional. Additional properties to set if sourceFormat is set to PARQUET.", "$ref": "ParquetOptions"}, "preserveAsciiControlCharacters": {"description": "Optional. When sourceFormat is set to \"CSV\", this indicates whether the embedded ASCII control characters (the first 32 characters in the ASCII-table, from '\\x00' to '\\x1F') are preserved.", "type": "boolean"}, "connectionProperties": {"description": "Optional. Connection properties which can modify the load job behavior. Currently, only the 'session_id' connection property is supported, and is used to resolve _SESSION appearing as the dataset id.", "type": "array", "items": {"$ref": "ConnectionProperty"}}, "createSession": {"description": "Optional. If this property is true, the job creates a new session using a randomly generated session_id. To continue using a created session with subsequent queries, pass the existing session identifier as a `ConnectionProperty` value. The session identifier is returned as part of the `SessionInfo` message within the query statistics. The new session's location will be set to `Job.JobReference.location` if it is present, otherwise it's set to the default location based on existing routing logic.", "type": "boolean"}}}, "DestinationTableProperties": {"id": "DestinationTableProperties", "type": "object", "properties": {"friendlyName": {"description": "Optional. Friendly name for the destination table. If the table already exists, it should be same as the existing friendly name.", "type": "string"}, "description": {"description": "Optional. The description for the destination table. This will only be used if the destination table is newly created. If the table already exists and a value different than the current description is provided, the job will fail.", "type": "string"}, "labels": {"description": "Optional. The labels associated with this table. You can use these to organize and group your tables. This will only be used if the destination table is newly created. If the table already exists and labels are different than the current labels are provided, the job will fail.", "type": "object", "additionalProperties": {"type": "string"}}}}, "ThriftOptions": {"id": "ThriftOptions", "description": "Options for configuring loading Thrift serialized data.", "type": "object", "properties": {"schemaIdlRootDir": {"description": "Required. The root directory of the IDL file bundle defining the schema. All IDL files that are used to parse the schema should be in this directory. This directory should be different from the source_uris.", "type": "string"}, "schemaIdlUri": {"description": "Required. The Thrift IDL file in the `schema_idl_root_dir` that should be used as the root file to parse the schema. All included idl files in the `schema_idl_uri` should also be in the `schema_idl_root_dir` or its sub-directory.", "type": "string"}, "schemaStruct": {"description": "Required. The root struct specified in `schema_idl_uri` that should be used to parse the schema.", "type": "string"}, "deserializationOption": {"description": "Optional. `deserialization_option` sets how the serialized Thrift should be deserialized. The following options are supported: * THRIFT_BINARY_PROTOCOL_OPTION: using TBinaryProtocol to deserialize the data.", "type": "string", "enumDescriptions": ["Default value. This value is unused.", "Use `TBinaryProtocol` to deserialize the data.."], "enum": ["DESERIALIZATION_OPTION_UNSPECIFIED", "THRIFT_BINARY_PROTOCOL_OPTION"]}, "framingOption": {"description": "Optional. Framing in Thrift means 4 bytes slipped in front of the serialized record or data block to inidicate the size of the followed record or data block. The following options are support: * NOT_FRAMED: Serialized Thrift records or data blocks are not framed, there are no 4-byte record size in front of the record. * FRAMED_WITH_BIG_ENDIAN: Serialized Thrift records or data blocks are framed with the 4-byte record size in big endian. * FRAMED_WITH_LITTLE_ENDIAN: Serialized Thrift records or data blocks are framed with the 4-byte record size in little endian. One option to frame Thrift record at serialization time is using `TFramedTransport`, which writes the 4-byte record or data block size in big endian. By default `framing_option` is set to \"NOT_FRAMED\".", "type": "string", "enumDescriptions": ["Default value. This value is unused.", "Records or data blocks are not framed.", "Records or data blocks are framed with a 4-byte record size in big endian.", "Records or data blocks are framed with a 4-byte record size in little endian."], "enum": ["FRAMING_OPTION_UNSPECIFIED", "NOT_FRAMED", "FRAMED_WITH_BIG_ENDIAN", "FRAMED_WITH_LITTLE_ENDIAN"]}, "boundaryBytes": {"description": "Optional. Sequence of bytes used to separate two serialized Thrift data blocks. When it's used with `framing_option`, the `boundary_bytes` are expected to be in front of the framed block.", "type": "string", "format": "byte"}}}, "JobConfigurationTableCopy": {"id": "JobConfigurationTableCopy", "description": "JobConfigurationTableCopy configures a job that copies data from one table to another. For more information on copying tables, see [Copy a table](https://cloud.google.com/bigquery/docs/managing-tables#copy-table).", "type": "object", "properties": {"sourceTable": {"description": "[Pick one] Source table to copy.", "$ref": "TableReference"}, "sourceTables": {"description": "[Pick one] Source tables to copy.", "type": "array", "items": {"$ref": "TableReference"}}, "destinationTable": {"description": "[Required] The destination table.", "$ref": "TableReference"}, "createDisposition": {"description": "Optional. Specifies whether the job is allowed to create new tables. The following values are supported: * CREATE_IF_NEEDED: If the table does not exist, BigQuery creates the table. * CREATE_NEVER: The table must already exist. If it does not, a 'notFound' error is returned in the job result. The default value is CREATE_IF_NEEDED. Creation, truncation and append actions occur as one atomic update upon job completion.", "type": "string"}, "writeDisposition": {"description": "Optional. Specifies the action that occurs if the destination table already exists. The following values are supported: * WRITE_TRUNCATE: If the table already exists, <PERSON><PERSON><PERSON><PERSON> overwrites the table data and uses the schema and table constraints from the source table. * WRITE_APPEND: If the table already exists, <PERSON>Query appends the data to the table. * WRITE_EMPTY: If the table already exists and contains data, a 'duplicate' error is returned in the job result. The default value is WRITE_EMPTY. Each action is atomic and only occurs if <PERSON><PERSON><PERSON><PERSON> is able to complete the job successfully. Creation, truncation and append actions occur as one atomic update upon job completion.", "type": "string"}, "destinationEncryptionConfiguration": {"description": "Custom encryption configuration (e.g., Cloud KMS keys).", "$ref": "EncryptionConfiguration"}, "operationType": {"description": "Optional. Supported operation types in table copy job.", "type": "string", "enumDescriptions": ["Unspecified operation type.", "The source and destination table have the same table type.", "The source table type is TABLE and the destination table type is SNAPSHOT.", "The source table type is SNAPSHOT and the destination table type is TABLE.", "The source and destination table have the same table type, but only bill for unique data."], "enum": ["OPERATION_TYPE_UNSPECIFIED", "COPY", "SNAPSHOT", "RESTORE", "CLONE"]}, "destinationExpirationTime": {"description": "Optional. The time when the destination table expires. Expired tables will be deleted and their storage reclaimed.", "type": "string", "format": "google-datetime"}}}, "JobConfigurationExtract": {"id": "JobConfigurationExtract", "description": "JobConfigurationExtract configures a job that exports data from a BigQuery table into Google Cloud Storage.", "type": "object", "properties": {"sourceTable": {"description": "A reference to the table being exported.", "$ref": "TableReference"}, "sourceModel": {"description": "A reference to the model being exported.", "$ref": "ModelReference"}, "destinationUri": {"description": "[Pick one] DEPRECATED: Use destinationUris instead, passing only one URI as necessary. The fully-qualified Google Cloud Storage URI where the extracted table should be written.", "type": "string"}, "destinationUris": {"description": "[Pick one] A list of fully-qualified Google Cloud Storage URIs where the extracted table should be written.", "type": "array", "items": {"type": "string"}}, "printHeader": {"description": "Optional. Whether to print out a header row in the results. Default is true. Not applicable when extracting models.", "default": "true", "type": "boolean"}, "fieldDelimiter": {"description": "Optional. When extracting data in CSV format, this defines the delimiter to use between fields in the exported data. Default is ','. Not applicable when extracting models.", "type": "string"}, "destinationFormat": {"description": "Optional. The exported file format. Possible values include CSV, NEWLINE_DELIMITED_JSON, PARQUET, or AVRO for tables and ML_TF_SAVED_MODEL or ML_XGBOOST_BOOSTER for models. The default value for tables is CSV. Tables with nested or repeated fields cannot be exported as CSV. The default value for models is ML_TF_SAVED_MODEL.", "type": "string"}, "compression": {"description": "Optional. The compression type to use for exported files. Possible values include DEFLATE, GZIP, NONE, SNAPPY, and ZSTD. The default value is NONE. Not all compression formats are support for all file formats. DEFLATE is only supported for Avro. ZSTD is only supported for Parquet. Not applicable when extracting models.", "type": "string"}, "useAvroLogicalTypes": {"description": "Whether to use logical types when extracting to AVRO format. Not applicable when extracting models.", "type": "boolean"}, "modelExtractOptions": {"description": "Optional. Model extract options only applicable when extracting models.", "$ref": "ModelExtractOptions"}}}, "ModelExtractOptions": {"id": "ModelExtractOptions", "description": "Options related to model extraction.", "type": "object", "properties": {"trialId": {"description": "The 1-based ID of the trial to be exported from a hyperparameter tuning model. If not specified, the trial with id = [Model](/bigquery/docs/reference/rest/v2/models#resource:-model).defaultTrialId is exported. This field is ignored for models not trained with hyperparameter tuning.", "type": "string", "format": "int64"}}}, "JobReference": {"id": "JobReference", "description": "A job reference is a fully qualified identifier for referring to a job.", "type": "object", "properties": {"projectId": {"description": "Required. The ID of the project containing this job.", "type": "string"}, "jobId": {"description": "Required. The ID of the job. The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), or dashes (-). The maximum length is 1,024 characters.", "type": "string"}, "location": {"description": "Optional. The geographic location of the job. The default value is US.", "type": "string"}}}, "JobStatistics": {"id": "JobStatistics", "description": "Statistics for a single job execution.", "type": "object", "properties": {"creationTime": {"description": "Output only. Creation time of this job, in milliseconds since the epoch. This field will be present on all jobs.", "readOnly": true, "type": "string", "format": "int64"}, "startTime": {"description": "Output only. Start time of this job, in milliseconds since the epoch. This field will be present when the job transitions from the PENDING state to either RUNNING or DONE.", "readOnly": true, "type": "string", "format": "int64"}, "endTime": {"description": "Output only. End time of this job, in milliseconds since the epoch. This field will be present whenever a job is in the DONE state.", "readOnly": true, "type": "string", "format": "int64"}, "totalBytesProcessed": {"description": "Output only. Total bytes processed for the job.", "readOnly": true, "type": "string", "format": "int64"}, "completionRatio": {"description": "Output only. [TrustedTester] Job progress (0.0 -> 1.0) for LOAD and EXTRACT jobs.", "readOnly": true, "type": "number", "format": "double"}, "quotaDeferments": {"description": "Output only. Quotas which delayed this job's start time.", "readOnly": true, "type": "array", "items": {"type": "string"}}, "query": {"description": "Output only. Statistics for a query job.", "readOnly": true, "$ref": "JobStatistics2"}, "load": {"description": "Output only. Statistics for a load job.", "readOnly": true, "$ref": "JobStatistics3"}, "extract": {"description": "Output only. Statistics for an extract job.", "readOnly": true, "$ref": "JobStatistics4"}, "copy": {"description": "Output only. Statistics for a copy job.", "readOnly": true, "$ref": "CopyJobStatistics"}, "totalSlotMs": {"description": "Output only. Slot-milliseconds for the job.", "readOnly": true, "type": "string", "format": "int64"}, "reservationUsage": {"description": "Output only. Job resource usage breakdown by reservation. This field reported misleading information and will no longer be populated.", "readOnly": true, "deprecated": true, "type": "array", "items": {"description": "Job resource usage breakdown by reservation.", "type": "object", "properties": {"name": {"description": "Reservation name or \"unreserved\" for on-demand resources usage.", "type": "string"}, "slotMs": {"description": "Total slot milliseconds used by the reservation for a particular job.", "type": "string", "format": "int64"}}}}, "reservation_id": {"description": "Output only. Name of the primary reservation assigned to this job. Note that this could be different than reservations reported in the reservation usage field if parent reservations were used to execute this job.", "readOnly": true, "type": "string"}, "numChildJobs": {"description": "Output only. Number of child jobs executed.", "readOnly": true, "type": "string", "format": "int64"}, "parentJobId": {"description": "Output only. If this is a child job, specifies the job ID of the parent.", "readOnly": true, "type": "string"}, "scriptStatistics": {"description": "Output only. If this a child job of a script, specifies information about the context of this job within the script.", "readOnly": true, "$ref": "ScriptStatistics"}, "rowLevelSecurityStatistics": {"description": "Output only. Statistics for row-level security. Present only for query and extract jobs.", "readOnly": true, "$ref": "RowLevelSecurityStatistics"}, "dataMaskingStatistics": {"description": "Output only. Statistics for data-masking. Present only for query and extract jobs.", "readOnly": true, "$ref": "DataMaskingStatistics"}, "transactionInfo": {"description": "Output only. [Alpha] Information of the multi-statement transaction if this job is part of one. This property is only expected on a child job or a job that is in a session. A script parent job is not part of the transaction started in the script.", "readOnly": true, "$ref": "TransactionInfo"}, "sessionInfo": {"description": "Output only. Information of the session if this job is part of one.", "readOnly": true, "$ref": "SessionInfo"}, "finalExecutionDurationMs": {"description": "Output only. The duration in milliseconds of the execution of the final attempt of this job, as BigQuery may internally re-attempt to execute the job.", "readOnly": true, "type": "string", "format": "int64"}}}, "JobStatistics2": {"id": "JobStatistics2", "description": "Statistics for a query job.", "type": "object", "properties": {"queryPlan": {"description": "Output only. Describes execution plan for the query.", "readOnly": true, "type": "array", "items": {"$ref": "ExplainQueryStage"}}, "estimatedBytesProcessed": {"description": "Output only. The original estimate of bytes processed for the job.", "readOnly": true, "type": "string", "format": "int64"}, "timeline": {"description": "Output only. Describes a timeline of job execution.", "readOnly": true, "type": "array", "items": {"$ref": "QueryTimelineSample"}}, "totalPartitionsProcessed": {"description": "Output only. Total number of partitions processed from all partitioned tables referenced in the job.", "readOnly": true, "type": "string", "format": "int64"}, "totalBytesProcessed": {"description": "Output only. Total bytes processed for the job.", "readOnly": true, "type": "string", "format": "int64"}, "totalBytesProcessedAccuracy": {"description": "Output only. For dry-run jobs, totalBytesProcessed is an estimate and this field specifies the accuracy of the estimate. Possible values can be: UNKNOWN: accuracy of the estimate is unknown. PRECISE: estimate is precise. LOWER_BOUND: estimate is lower bound of what the query would cost. UPPER_BOUND: estimate is upper bound of what the query would cost.", "readOnly": true, "type": "string"}, "totalBytesBilled": {"description": "Output only. If the project is configured to use on-demand pricing, then this field contains the total bytes billed for the job. If the project is configured to use flat-rate pricing, then you are not billed for bytes and this field is informational only.", "readOnly": true, "type": "string", "format": "int64"}, "billingTier": {"description": "Output only. Billing tier for the job. This is a BigQuery-specific concept which is not related to the GCP notion of \"free tier\". The value here is a measure of the query's resource consumption relative to the amount of data scanned. For on-demand queries, the limit is 100, and all queries within this limit are billed at the standard on-demand rates. On-demand queries that exceed this limit will fail with a billingTierLimitExceeded error.", "readOnly": true, "type": "integer", "format": "int32"}, "totalSlotMs": {"description": "Output only. Slot-milliseconds for the job.", "readOnly": true, "type": "string", "format": "int64"}, "reservationUsage": {"description": "Output only. Job resource usage breakdown by reservation. This field reported misleading information and will no longer be populated.", "readOnly": true, "deprecated": true, "type": "array", "items": {"description": "Job resource usage breakdown by reservation.", "type": "object", "properties": {"name": {"description": "Reservation name or \"unreserved\" for on-demand resources usage.", "type": "string"}, "slotMs": {"description": "Total slot milliseconds used by the reservation for a particular job.", "type": "string", "format": "int64"}}}}, "cacheHit": {"description": "Output only. Whether the query result was fetched from the query cache.", "readOnly": true, "type": "boolean"}, "referencedTables": {"description": "Output only. Referenced tables for the job. Queries that reference more than 50 tables will not have a complete list.", "readOnly": true, "type": "array", "items": {"$ref": "TableReference"}}, "referencedRoutines": {"description": "Output only. Referenced routines for the job.", "readOnly": true, "type": "array", "items": {"$ref": "RoutineReference"}}, "schema": {"description": "Output only. The schema of the results. Present only for successful dry run of non-legacy SQL queries.", "readOnly": true, "$ref": "TableSchema"}, "numDmlAffectedRows": {"description": "Output only. The number of rows affected by a DML statement. Present only for DML statements INSERT, UPDATE or DELETE.", "readOnly": true, "type": "string", "format": "int64"}, "dmlStats": {"description": "Output only. Detailed statistics for DML statements INSERT, UPDATE, DELETE, MERGE or TRUNCATE.", "readOnly": true, "$ref": "DmlStats"}, "undeclaredQueryParameters": {"description": "Output only. GoogleSQL only: list of undeclared query parameters detected during a dry run validation.", "readOnly": true, "type": "array", "items": {"$ref": "QueryParameter"}}, "statementType": {"description": "Output only. The type of query statement, if valid. Possible values: * `SELECT`: [`SELECT`](/bigquery/docs/reference/standard-sql/query-syntax#select_list) statement. * `ASSERT`: [`ASSERT`](/bigquery/docs/reference/standard-sql/debugging-statements#assert) statement. * `INSERT`: [`INSERT`](/bigquery/docs/reference/standard-sql/dml-syntax#insert_statement) statement. * `UPDATE`: [`UPDATE`](/bigquery/docs/reference/standard-sql/query-syntax#update_statement) statement. * `DELETE`: [`DELETE`](/bigquery/docs/reference/standard-sql/data-manipulation-language) statement. * `MERGE`: [`MERGE`](/bigquery/docs/reference/standard-sql/data-manipulation-language) statement. * `CREATE_TABLE`: [`CREATE TABLE`](/bigquery/docs/reference/standard-sql/data-definition-language#create_table_statement) statement, without `AS SELECT`. * `CREATE_TABLE_AS_SELECT`: [`CREATE TABLE AS SELECT`](/bigquery/docs/reference/standard-sql/data-definition-language#query_statement) statement. * `CREATE_VIEW`: [`CREATE VIEW`](/bigquery/docs/reference/standard-sql/data-definition-language#create_view_statement) statement. * `CREATE_MODEL`: [`CREATE MODEL`](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-create#create_model_statement) statement. * `CREATE_MATERIALIZED_VIEW`: [`CREATE MATERIALIZED VIEW`](/bigquery/docs/reference/standard-sql/data-definition-language#create_materialized_view_statement) statement. * `CREATE_FUNCTION`: [`CREATE FUNCTION`](/bigquery/docs/reference/standard-sql/data-definition-language#create_function_statement) statement. * `CREATE_TABLE_FUNCTION`: [`CREATE TABLE FUNCTION`](/bigquery/docs/reference/standard-sql/data-definition-language#create_table_function_statement) statement. * `CREATE_PROCEDURE`: [`CREATE PROCEDURE`](/bigquery/docs/reference/standard-sql/data-definition-language#create_procedure) statement. * `CREATE_ROW_ACCESS_POLICY`: [`CREATE ROW ACCESS POLICY`](/bigquery/docs/reference/standard-sql/data-definition-language#create_row_access_policy_statement) statement. * `CREATE_SCHEMA`: [`CREATE SCHEMA`](/bigquery/docs/reference/standard-sql/data-definition-language#create_schema_statement) statement. * `CREATE_SNAPSHOT_TABLE`: [`CREATE SNAPSHOT TABLE`](/bigquery/docs/reference/standard-sql/data-definition-language#create_snapshot_table_statement) statement. * `CREATE_SEARCH_INDEX`: [`CREATE SEARCH INDEX`](/bigquery/docs/reference/standard-sql/data-definition-language#create_search_index_statement) statement. * `DROP_TABLE`: [`DROP TABLE`](/bigquery/docs/reference/standard-sql/data-definition-language#drop_table_statement) statement. * `DROP_EXTERNAL_TABLE`: [`DROP EXTERNAL TABLE`](/bigquery/docs/reference/standard-sql/data-definition-language#drop_external_table_statement) statement. * `DROP_VIEW`: [`DROP VIEW`](/bigquery/docs/reference/standard-sql/data-definition-language#drop_view_statement) statement. * `DROP_MODEL`: [`DROP MODEL`](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-drop-model) statement. * `DROP_MATERIALIZED_VIEW`: [`DROP MATERIALIZED VIEW`](/bigquery/docs/reference/standard-sql/data-definition-language#drop_materialized_view_statement) statement. * `DROP_FUNCTION` : [`DROP FUNCTION`](/bigquery/docs/reference/standard-sql/data-definition-language#drop_function_statement) statement. * `DROP_TABLE_FUNCTION` : [`DROP TABLE FUNCTION`](/bigquery/docs/reference/standard-sql/data-definition-language#drop_table_function) statement. * `DROP_PROCEDURE`: [`DROP PROCEDURE`](/bigquery/docs/reference/standard-sql/data-definition-language#drop_procedure_statement) statement. * `DROP_SEARCH_INDEX`: [`DROP SEARCH INDEX`](/bigquery/docs/reference/standard-sql/data-definition-language#drop_search_index) statement. * `DROP_SCHEMA`: [`DROP SCHEMA`](/bigquery/docs/reference/standard-sql/data-definition-language#drop_schema_statement) statement. * `DROP_SNAPSHOT_TABLE`: [`DROP SNAPSHOT TABLE`](/bigquery/docs/reference/standard-sql/data-definition-language#drop_snapshot_table_statement) statement. * `DROP_ROW_ACCESS_POLICY`: [`DROP [ALL] ROW ACCESS POLICY|POLICIES`](/bigquery/docs/reference/standard-sql/data-definition-language#drop_row_access_policy_statement) statement. * `ALTER_TABLE`: [`ALTER TABLE`](/bigquery/docs/reference/standard-sql/data-definition-language#alter_table_set_options_statement) statement. * `ALTER_VIEW`: [`ALTER VIEW`](/bigquery/docs/reference/standard-sql/data-definition-language#alter_view_set_options_statement) statement. * `ALTER_MATERIALIZED_VIEW`: [`ALTER MATERIALIZED VIEW`](/bigquery/docs/reference/standard-sql/data-definition-language#alter_materialized_view_set_options_statement) statement. * `ALTER_SCHEMA`: [`ALTER SCHEMA`](/bigquery/docs/reference/standard-sql/data-definition-language#aalter_schema_set_options_statement) statement. * `SCRIPT`: [`SCRIPT`](/bigquery/docs/reference/standard-sql/procedural-language). * `TRUNCATE_TABLE`: [`TRUNCATE TABLE`](/bigquery/docs/reference/standard-sql/dml-syntax#truncate_table_statement) statement. * `CREATE_EXTERNAL_TABLE`: [`CREATE EXTERNAL TABLE`](/bigquery/docs/reference/standard-sql/data-definition-language#create_external_table_statement) statement. * `EXPORT_DATA`: [`EXPORT DATA`](/bigquery/docs/reference/standard-sql/other-statements#export_data_statement) statement. * `EXPORT_MODEL`: [`EXPORT MODEL`](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-export-model) statement. * `LOAD_DATA`: [`LOAD DATA`](/bigquery/docs/reference/standard-sql/other-statements#load_data_statement) statement. * `CALL`: [`CALL`](/bigquery/docs/reference/standard-sql/procedural-language#call) statement.", "readOnly": true, "type": "string"}, "ddlOperationPerformed": {"description": "Output only. The DDL operation performed, possibly dependent on the pre-existence of the DDL target.", "readOnly": true, "type": "string"}, "ddlTargetTable": {"description": "Output only. The DDL target table. Present only for CREATE/DROP TABLE/VIEW and DROP ALL ROW ACCESS POLICIES queries.", "readOnly": true, "$ref": "TableReference"}, "ddlTargetRowAccessPolicy": {"description": "Output only. The DDL target row access policy. Present only for CREATE/DROP ROW ACCESS POLICY queries.", "readOnly": true, "$ref": "RowAccessPolicyReference"}, "ddlAffectedRowAccessPolicyCount": {"description": "Output only. The number of row access policies affected by a DDL statement. Present only for DROP ALL ROW ACCESS POLICIES queries.", "readOnly": true, "type": "string", "format": "int64"}, "ddlTargetRoutine": {"description": "Output only. [Beta] The DDL target routine. Present only for CREATE/DROP FUNCTION/PROCEDURE queries.", "readOnly": true, "$ref": "RoutineReference"}, "ddlTargetDataset": {"description": "Output only. The DDL target dataset. Present only for CREATE/ALTER/DROP SCHEMA(dataset) queries.", "readOnly": true, "$ref": "DatasetReference"}, "mlStatistics": {"description": "Output only. Statistics of a BigQuery ML training job.", "readOnly": true, "$ref": "MlStatistics"}, "exportDataStatistics": {"description": "Output only. Stats for EXPORT DATA statement.", "readOnly": true, "$ref": "ExportDataStatistics"}, "externalServiceCosts": {"description": "Output only. Job cost breakdown as bigquery internal cost and external service costs.", "readOnly": true, "type": "array", "items": {"$ref": "ExternalServiceCost"}}, "biEngineStatistics": {"description": "Output only. BI Engine specific Statistics.", "readOnly": true, "$ref": "BiEngineStatistics"}, "loadQueryStatistics": {"description": "Output only. Statistics for a LOAD query.", "readOnly": true, "$ref": "LoadQueryStatistics"}, "dclTargetTable": {"description": "Output only. Referenced table for DCL statement.", "readOnly": true, "$ref": "TableReference"}, "dclTargetView": {"description": "Output only. Referenced view for DCL statement.", "readOnly": true, "$ref": "TableReference"}, "dclTargetDataset": {"description": "Output only. Referenced dataset for DCL statement.", "readOnly": true, "$ref": "DatasetReference"}, "searchStatistics": {"description": "Output only. Search query specific statistics.", "readOnly": true, "$ref": "SearchStatistics"}, "performanceInsights": {"description": "Output only. Performance insights.", "readOnly": true, "$ref": "PerformanceInsights"}, "queryInfo": {"description": "Output only. Query optimization information for a QUERY job.", "readOnly": true, "$ref": "QueryInfo"}, "sparkStatistics": {"description": "Output only. Statistics of a Spark procedure job.", "readOnly": true, "$ref": "SparkStatistics"}, "transferredBytes": {"description": "Output only. Total bytes transferred for cross-cloud queries such as Cross Cloud Transfer and CREATE TABLE AS SELECT (CTAS).", "readOnly": true, "type": "string", "format": "int64"}, "materializedViewStatistics": {"description": "Output only. Statistics of materialized views of a query job.", "readOnly": true, "$ref": "MaterializedViewStatistics"}, "metadataCacheStatistics": {"description": "Output only. Statistics of metadata cache usage in a query for BigLake tables.", "readOnly": true, "$ref": "MetadataCacheStatistics"}}}, "ExplainQueryStage": {"id": "ExplainQueryStage", "description": "A single stage of query execution.", "type": "object", "properties": {"name": {"description": "Human-readable name for the stage.", "type": "string"}, "id": {"description": "Unique ID for the stage within the plan.", "type": "string", "format": "int64"}, "startMs": {"description": "Stage start time represented as milliseconds since the epoch.", "type": "string", "format": "int64"}, "endMs": {"description": "Stage end time represented as milliseconds since the epoch.", "type": "string", "format": "int64"}, "inputStages": {"description": "IDs for stages that are inputs to this stage.", "type": "array", "items": {"type": "string", "format": "int64"}}, "waitRatioAvg": {"description": "Relative amount of time the average shard spent waiting to be scheduled.", "type": "number", "format": "double"}, "waitMsAvg": {"description": "Milliseconds the average shard spent waiting to be scheduled.", "type": "string", "format": "int64"}, "waitRatioMax": {"description": "Relative amount of time the slowest shard spent waiting to be scheduled.", "type": "number", "format": "double"}, "waitMsMax": {"description": "Milliseconds the slowest shard spent waiting to be scheduled.", "type": "string", "format": "int64"}, "readRatioAvg": {"description": "Relative amount of time the average shard spent reading input.", "type": "number", "format": "double"}, "readMsAvg": {"description": "Milliseconds the average shard spent reading input.", "type": "string", "format": "int64"}, "readRatioMax": {"description": "Relative amount of time the slowest shard spent reading input.", "type": "number", "format": "double"}, "readMsMax": {"description": "Milliseconds the slowest shard spent reading input.", "type": "string", "format": "int64"}, "computeRatioAvg": {"description": "Relative amount of time the average shard spent on CPU-bound tasks.", "type": "number", "format": "double"}, "computeMsAvg": {"description": "Milliseconds the average shard spent on CPU-bound tasks.", "type": "string", "format": "int64"}, "computeRatioMax": {"description": "Relative amount of time the slowest shard spent on CPU-bound tasks.", "type": "number", "format": "double"}, "computeMsMax": {"description": "Milliseconds the slowest shard spent on CPU-bound tasks.", "type": "string", "format": "int64"}, "writeRatioAvg": {"description": "Relative amount of time the average shard spent on writing output.", "type": "number", "format": "double"}, "writeMsAvg": {"description": "Milliseconds the average shard spent on writing output.", "type": "string", "format": "int64"}, "writeRatioMax": {"description": "Relative amount of time the slowest shard spent on writing output.", "type": "number", "format": "double"}, "writeMsMax": {"description": "Milliseconds the slowest shard spent on writing output.", "type": "string", "format": "int64"}, "shuffleOutputBytes": {"description": "Total number of bytes written to shuffle.", "type": "string", "format": "int64"}, "shuffleOutputBytesSpilled": {"description": "Total number of bytes written to shuffle and spilled to disk.", "type": "string", "format": "int64"}, "recordsRead": {"description": "Number of records read into the stage.", "type": "string", "format": "int64"}, "recordsWritten": {"description": "Number of records written by the stage.", "type": "string", "format": "int64"}, "parallelInputs": {"description": "Number of parallel input segments to be processed", "type": "string", "format": "int64"}, "completedParallelInputs": {"description": "Number of parallel input segments completed.", "type": "string", "format": "int64"}, "status": {"description": "Current status for this stage.", "type": "string"}, "steps": {"description": "List of operations within the stage in dependency order (approximately chronological).", "type": "array", "items": {"$ref": "ExplainQueryStep"}}, "slotMs": {"description": "Slot-milliseconds used by the stage.", "type": "string", "format": "int64"}, "computeMode": {"description": "Output only. Compute mode for this stage.", "readOnly": true, "type": "string", "enumDescriptions": ["ComputeMode type not specified.", "This stage was processed using BigQuery slots.", "This stage was processed using BI Engine compute."], "enum": ["COMPUTE_MODE_UNSPECIFIED", "BIGQUERY", "BI_ENGINE"]}}}, "ExplainQueryStep": {"id": "ExplainQueryStep", "description": "An operation within a stage.", "type": "object", "properties": {"kind": {"description": "Machine-readable operation type.", "type": "string"}, "substeps": {"description": "Human-readable description of the step(s).", "type": "array", "items": {"type": "string"}}}}, "QueryTimelineSample": {"id": "QueryTimelineSample", "description": "Summary of the state of query execution at a given time.", "type": "object", "properties": {"elapsedMs": {"description": "Milliseconds elapsed since the start of query execution.", "type": "string", "format": "int64"}, "totalSlotMs": {"description": "Cumulative slot-ms consumed by the query.", "type": "string", "format": "int64"}, "pendingUnits": {"description": "Total units of work remaining for the query. This number can be revised (increased or decreased) while the query is running.", "type": "string", "format": "int64"}, "completedUnits": {"description": "Total parallel units of work completed by this query.", "type": "string", "format": "int64"}, "activeUnits": {"description": "Total number of active workers. This does not correspond directly to slot usage. This is the largest value observed since the last sample.", "type": "string", "format": "int64"}, "estimatedRunnableUnits": {"description": "Units of work that can be scheduled immediately. Providing additional slots for these units of work will accelerate the query, if no other query in the reservation needs additional slots.", "type": "string", "format": "int64"}}}, "DmlStats": {"id": "DmlStats", "description": "Detailed statistics for DML statements", "type": "object", "properties": {"insertedRowCount": {"description": "Output only. Number of inserted Rows. Populated by DML INSERT and MERGE statements", "readOnly": true, "type": "string", "format": "int64"}, "deletedRowCount": {"description": "Output only. Number of deleted Rows. populated by DML DELETE, MERGE and TRUNCATE statements.", "readOnly": true, "type": "string", "format": "int64"}, "updatedRowCount": {"description": "Output only. Number of updated Rows. Populated by DML UPDATE and MERGE statements.", "readOnly": true, "type": "string", "format": "int64"}}}, "RowAccessPolicyReference": {"id": "RowAccessPolicyReference", "description": "Id path of a row access policy.", "type": "object", "properties": {"projectId": {"description": "Required. The ID of the project containing this row access policy.", "type": "string"}, "datasetId": {"description": "Required. The ID of the dataset containing this row access policy.", "type": "string"}, "tableId": {"description": "Required. The ID of the table containing this row access policy.", "type": "string"}, "policyId": {"description": "Required. The ID of the row access policy. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 256 characters.", "type": "string"}}}, "MlStatistics": {"id": "MlStatistics", "description": "Job statistics specific to a BigQuery ML training job.", "type": "object", "properties": {"maxIterations": {"description": "Output only. Maximum number of iterations specified as max_iterations in the 'CREATE MODEL' query. The actual number of iterations may be less than this number due to early stop.", "readOnly": true, "type": "string", "format": "int64"}, "iterationResults": {"description": "Results for all completed iterations. Empty for [hyperparameter tuning jobs](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview).", "type": "array", "items": {"$ref": "IterationResult"}}, "modelType": {"description": "Output only. The type of the model that is being trained.", "readOnly": true, "type": "string", "enumDescriptions": ["", "Linear regression model.", "Logistic regression based classification model.", "K-means clustering model.", "Matrix factorization model.", "DNN classifier model.", "An imported TensorFlow model.", "DNN regressor model.", "An imported XGBoost model.", "Boosted tree regressor model.", "Boosted tree classifier model.", "ARIMA model.", "AutoML Tables regression model.", "AutoML Tables classification model.", "Prinpical Component Analysis model.", "Wide-and-deep classifier model.", "Wide-and-deep regressor model.", "Autoencoder model.", "New name for the ARIMA model.", "ARIMA with external regressors.", "Random forest regressor model.", "Random forest classifier model.", "An imported TensorFlow Lite model.", "An imported ONNX model."], "enum": ["MODEL_TYPE_UNSPECIFIED", "LINEAR_REGRESSION", "LOGISTIC_REGRESSION", "KMEANS", "MATRIX_FACTORIZATION", "DNN_CLASSIFIER", "TENSORFLOW", "DNN_REGRESSOR", "XGBOOST", "BOOSTED_TREE_REGRESSOR", "BOOSTED_TREE_CLASSIFIER", "ARIMA", "AUTOML_REGRESSOR", "AUTOML_CLASSIFIER", "PCA", "DNN_LINEAR_COMBINED_CLASSIFIER", "DNN_LINEAR_COMBINED_REGRESSOR", "AUTOENCODER", "ARIMA_PLUS", "ARIMA_PLUS_XREG", "RANDOM_FOREST_REGRESSOR", "RANDOM_FOREST_CLASSIFIER", "TENSORFLOW_LITE", "ONNX"]}, "trainingType": {"description": "Output only. Training type of the job.", "readOnly": true, "type": "string", "enumDescriptions": ["Unspecified training type.", "Single training with fixed parameter space.", "[Hyperparameter tuning training](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview)."], "enum": ["TRAINING_TYPE_UNSPECIFIED", "SINGLE_TRAINING", "HPARAM_TUNING"]}, "hparamTrials": {"description": "Output only. Trials of a [hyperparameter tuning job](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) sorted by trial_id.", "readOnly": true, "type": "array", "items": {"$ref": "HparamTuningTrial"}}}}, "ExportDataStatistics": {"id": "ExportDataStatistics", "description": "Statistics for the EXPORT DATA statement as part of Query Job. EXTRACT JOB statistics are populated in JobStatistics4.", "type": "object", "properties": {"fileCount": {"description": "Number of destination files generated in case of EXPORT DATA statement only.", "type": "string", "format": "int64"}, "rowCount": {"description": "[Alpha] Number of destination rows generated in case of EXPORT DATA statement only.", "type": "string", "format": "int64"}}}, "ExternalServiceCost": {"id": "ExternalServiceCost", "description": "The external service cost is a portion of the total cost, these costs are not additive with total_bytes_billed. Moreover, this field only track external service costs that will show up as BigQuery costs (e.g. training BigQuery ML job with google cloud CAIP or Automl Tables services), not other costs which may be accrued by running the query (e.g. reading from Bigtable or Cloud Storage). The external service costs with different billing sku (e.g. CAIP job is charged based on VM usage) are converted to BigQuery billed_bytes and slot_ms with equivalent amount of US dollars. Services may not directly correlate to these metrics, but these are the equivalents for billing purposes. Output only.", "type": "object", "properties": {"externalService": {"description": "External service name.", "type": "string"}, "bytesProcessed": {"description": "External service cost in terms of bigquery bytes processed.", "type": "string", "format": "int64"}, "bytesBilled": {"description": "External service cost in terms of bigquery bytes billed.", "type": "string", "format": "int64"}, "slotMs": {"description": "External service cost in terms of bigquery slot milliseconds.", "type": "string", "format": "int64"}, "reservedSlotCount": {"description": "Non-preemptable reserved slots used for external job. For example, reserved slots for Cloua AI Platform job are the VM usages converted to BigQuery slot with equivalent mount of price.", "type": "string", "format": "int64"}}}, "BiEngineStatistics": {"id": "BiEngineStatistics", "description": "Statistics for a BI Engine specific query. Populated as part of JobStatistics2", "type": "object", "properties": {"biEngineMode": {"description": "Output only. Specifies which mode of BI Engine acceleration was performed (if any).", "readOnly": true, "type": "string", "enumDescriptions": ["BiEngineMode type not specified.", "BI Engine disabled the acceleration. bi_engine_reasons specifies a more detailed reason.", "Part of the query was accelerated using BI Engine. See bi_engine_reasons for why parts of the query were not accelerated.", "All of the query was accelerated using BI Engine."], "enum": ["ACCELERATION_MODE_UNSPECIFIED", "DISABLED", "PARTIAL", "FULL"]}, "accelerationMode": {"description": "Output only. Specifies which mode of BI Engine acceleration was performed (if any).", "readOnly": true, "type": "string", "enumDescriptions": ["BiEngineMode type not specified.", "BI Engine acceleration was attempted but disabled. bi_engine_reasons specifies a more detailed reason.", "Some inputs were accelerated using BI Engine. See bi_engine_reasons for why parts of the query were not accelerated.", "All of the query inputs were accelerated using BI Engine.", "All of the query was accelerated using BI Engine."], "enum": ["BI_ENGINE_ACCELERATION_MODE_UNSPECIFIED", "BI_ENGINE_DISABLED", "PARTIAL_INPUT", "FULL_INPUT", "FULL_QUERY"]}, "biEngineReasons": {"description": "In case of DISABLED or PARTIAL bi_engine_mode, these contain the explanatory reasons as to why BI Engine could not accelerate. In case the full query was accelerated, this field is not populated.", "type": "array", "items": {"$ref": "BiEngineReason"}}}}, "BiEngineReason": {"id": "BiEngineReason", "description": "Reason why BI Engine didn't accelerate the query (or sub-query).", "type": "object", "properties": {"code": {"description": "Output only. High-level BI Engine reason for partial or disabled acceleration", "readOnly": true, "type": "string", "enumDescriptions": ["BiEngineReason not specified.", "No reservation available for BI Engine acceleration.", "Not enough memory available for BI Engine acceleration.", "This particular SQL text is not supported for acceleration by BI Engine.", "Input too large for acceleration by BI Engine.", "Catch-all code for all other cases for partial or disabled acceleration.", "One or more tables were not eligible for BI Engine acceleration."], "enum": ["CODE_UNSPECIFIED", "NO_RESERVATION", "INSUFFICIENT_RESERVATION", "UNSUPPORTED_SQL_TEXT", "INPUT_TOO_LARGE", "OTHER_REASON", "TABLE_EXCLUDED"]}, "message": {"description": "Output only. Free form human-readable reason for partial or disabled acceleration.", "readOnly": true, "type": "string"}}}, "LoadQueryStatistics": {"id": "LoadQueryStatistics", "description": "Statistics for a LOAD query.", "type": "object", "properties": {"inputFiles": {"description": "Output only. Number of source files in a LOAD query.", "readOnly": true, "type": "string", "format": "int64"}, "inputFileBytes": {"description": "Output only. Number of bytes of source data in a LOAD query.", "readOnly": true, "type": "string", "format": "int64"}, "outputRows": {"description": "Output only. Number of rows imported in a LOAD query. Note that while a LOAD query is in the running state, this value may change.", "readOnly": true, "type": "string", "format": "int64"}, "outputBytes": {"description": "Output only. Size of the loaded data in bytes. Note that while a LOAD query is in the running state, this value may change.", "readOnly": true, "type": "string", "format": "int64"}, "badRecords": {"description": "Output only. The number of bad records encountered while processing a LOAD query. Note that if the job has failed because of more bad records encountered than the maximum allowed in the load job configuration, then this number can be less than the total number of bad records present in the input data.", "readOnly": true, "type": "string", "format": "int64"}, "bytesTransferred": {"description": "Output only. This field is deprecated. The number of bytes of source data copied over the network for a `LOAD` query. `transferred_bytes` has the canonical value for physical transferred bytes, which is used for BigQuery Omni billing.", "readOnly": true, "deprecated": true, "type": "string", "format": "int64"}}}, "SearchStatistics": {"id": "SearchStatistics", "description": "Statistics for a search query. Populated as part of JobStatistics2.", "type": "object", "properties": {"indexUsageMode": {"description": "Specifies the index usage mode for the query.", "type": "string", "enumDescriptions": ["Index usage mode not specified.", "No search indexes were used in the search query. See [`indexUnusedReasons`] (/bigquery/docs/reference/rest/v2/Job#IndexUnusedReason) for detailed reasons.", "Part of the search query used search indexes. See [`indexUnusedReasons`] (/bigquery/docs/reference/rest/v2/Job#IndexUnusedReason) for why other parts of the query did not use search indexes.", "The entire search query used search indexes."], "enum": ["INDEX_USAGE_MODE_UNSPECIFIED", "UNUSED", "PARTIALLY_USED", "FULLY_USED"]}, "indexUnusedReasons": {"description": "When `indexUsageMode` is `UNUSED` or `PARTIALLY_USED`, this field explains why indexes were not used in all or part of the search query. If `indexUsageMode` is `FULLY_USED`, this field is not populated.", "type": "array", "items": {"$ref": "IndexUnusedReason"}}}}, "IndexUnusedReason": {"id": "IndexUnusedReason", "description": "Reason about why no search index was used in the search query (or sub-query).", "type": "object", "properties": {"code": {"description": "Specifies the high-level reason for the scenario when no search index was used.", "type": "string", "enumDescriptions": ["Code not specified.", "Indicates the search index configuration has not been created.", "Indicates the search index creation has not been completed.", "Indicates the base table has been truncated (rows have been removed from table with TRUNCATE TABLE statement) since the last time the search index was refreshed.", "Indicates the search index configuration has been changed since the last time the search index was refreshed.", "Indicates the search query accesses data at a timestamp before the last time the search index was refreshed.", "Indicates the usage of search index will not contribute to any pruning improvement for the search function, e.g. when the search predicate is in a disjunction with other non-search predicates.", "Indicates the search index does not cover all fields in the search function.", "Indicates the search index does not support the given search query pattern.", "Indicates the query has been optimized by using a materialized view.", "Indicates the query has been secured by data masking, and thus search indexes are not applicable.", "Indicates that the search index and the search function call do not have the same text analyzer.", "Indicates the base table is too small (below a certain threshold). The index does not provide noticeable search performance gains when the base table is too small.", "Indicates that the total size of indexed base tables in your organization exceeds your region's limit and the index is not used in the query. To index larger base tables, you can use your own reservation for index-management jobs.", "Indicates that the esitmated performance gain from using the search index is too low for the given search query.", "Indicates that search indexes can not be used for search query with STANDARD edition.", "Indicates an internal error that causes the search index to be unused.", "Indicates that the reason search indexes cannot be used in the query is not covered by any of the other IndexUnusedReason options."], "enum": ["CODE_UNSPECIFIED", "INDEX_CONFIG_NOT_AVAILABLE", "PENDING_INDEX_CREATION", "BASE_TABLE_TRUNCATED", "INDEX_CONFIG_MODIFIED", "TIME_TRAVEL_QUERY", "NO_PRUNING_POWER", "UNINDEXED_SEARCH_FIELDS", "UNSUPPORTED_SEARCH_PATTERN", "OPTIMIZED_WITH_MATERIALIZED_VIEW", "SECURED_BY_DATA_MASKING", "MISMATCHED_TEXT_ANALYZER", "BASE_TABLE_TOO_SMALL", "BASE_TABLE_TOO_LARGE", "ESTIMATED_PERFORMANCE_GAIN_TOO_LOW", "NOT_SUPPORTED_IN_STANDARD_EDITION", "INTERNAL_ERROR", "OTHER_REASON"]}, "message": {"description": "Free form human-readable reason for the scenario when no search index was used.", "type": "string"}, "baseTable": {"description": "Specifies the base table involved in the reason that no search index was used.", "$ref": "TableReference"}, "indexName": {"description": "Specifies the name of the unused search index, if available.", "type": "string"}}}, "PerformanceInsights": {"id": "PerformanceInsights", "description": "Performance insights for the job.", "type": "object", "properties": {"avgPreviousExecutionMs": {"description": "Output only. Average execution ms of previous runs. Indicates the job ran slow compared to previous executions. To find previous executions, use INFORMATION_SCHEMA tables and filter jobs with same query hash.", "readOnly": true, "type": "string", "format": "int64"}, "stagePerformanceStandaloneInsights": {"description": "Output only. Standalone query stage performance insights, for exploring potential improvements.", "readOnly": true, "type": "array", "items": {"$ref": "StagePerformanceStandaloneInsight"}}, "stagePerformanceChangeInsights": {"description": "Output only. Query stage performance insights compared to previous runs, for diagnosing performance regression.", "readOnly": true, "type": "array", "items": {"$ref": "StagePerformanceChangeInsight"}}}}, "StagePerformanceStandaloneInsight": {"id": "StagePerformanceStandaloneInsight", "description": "Standalone performance insights for a specific stage.", "type": "object", "properties": {"stageId": {"description": "Output only. The stage id that the insight mapped to.", "readOnly": true, "type": "string", "format": "int64"}, "slotContention": {"description": "Output only. True if the stage has a slot contention issue.", "readOnly": true, "type": "boolean"}, "insufficientShuffleQuota": {"description": "Output only. True if the stage has insufficient shuffle quota.", "readOnly": true, "type": "boolean"}, "highCardinalityJoins": {"description": "Output only. High cardinality joins in the stage.", "readOnly": true, "type": "array", "items": {"$ref": "HighCardinalityJoin"}}}}, "HighCardinalityJoin": {"id": "HighCardinalityJoin", "description": "High cardinality join detailed information.", "type": "object", "properties": {"leftRows": {"description": "Output only. Count of left input rows.", "readOnly": true, "type": "string", "format": "int64"}, "rightRows": {"description": "Output only. Count of right input rows.", "readOnly": true, "type": "string", "format": "int64"}, "outputRows": {"description": "Output only. Count of the output rows.", "readOnly": true, "type": "string", "format": "int64"}, "stepIndex": {"description": "Output only. The index of the join operator in the ExplainQueryStep lists.", "readOnly": true, "type": "integer", "format": "int32"}}}, "StagePerformanceChangeInsight": {"id": "StagePerformanceChangeInsight", "description": "Performance insights compared to the previous executions for a specific stage.", "type": "object", "properties": {"stageId": {"description": "Output only. The stage id that the insight mapped to.", "readOnly": true, "type": "string", "format": "int64"}, "inputDataChange": {"description": "Output only. Input data change insight of the query stage.", "readOnly": true, "$ref": "InputDataChange"}}}, "InputDataChange": {"id": "InputDataChange", "description": "Details about the input data change insight.", "type": "object", "properties": {"recordsReadDiffPercentage": {"description": "Output only. Records read difference percentage compared to a previous run.", "readOnly": true, "type": "number", "format": "float"}}}, "QueryInfo": {"id": "QueryInfo", "description": "Query optimization information for a QUERY job.", "type": "object", "properties": {"optimizationDetails": {"description": "Output only. Information about query optimizations.", "readOnly": true, "type": "object", "additionalProperties": {"type": "any", "description": "Properties of the object."}}}}, "SparkStatistics": {"id": "SparkStatistics", "description": "Statistics for a BigSpark query. Populated as part of JobStatistics2", "type": "object", "properties": {"sparkJobId": {"description": "Output only. Spark job ID if a Spark job is created successfully.", "readOnly": true, "type": "string"}, "sparkJobLocation": {"description": "Output only. Location where the Spark job is executed. A location is selected by BigQueury for jobs configured to run in a multi-region.", "readOnly": true, "type": "string"}, "endpoints": {"description": "Output only. Endpoints returned from Dataproc. Key list: - history_server_endpoint: A link to Spark job UI.", "readOnly": true, "type": "object", "additionalProperties": {"type": "string"}}, "loggingInfo": {"description": "Output only. Logging info is used to generate a link to Cloud Logging.", "readOnly": true, "$ref": "LoggingInfo"}}}, "LoggingInfo": {"id": "LoggingInfo", "description": "Spark job logs can be filtered by these fields in Cloud Logging.", "type": "object", "properties": {"resourceType": {"description": "Output only. Resource type used for logging.", "readOnly": true, "type": "string"}, "projectId": {"description": "Output only. Project ID where the Spark logs were written.", "readOnly": true, "type": "string"}}}, "MaterializedViewStatistics": {"id": "MaterializedViewStatistics", "description": "Statistics of materialized views considered in a query job.", "type": "object", "properties": {"materializedView": {"description": "Materialized views considered for the query job. Only certain materialized views are used. For a detailed list, see the child message. If many materialized views are considered, then the list might be incomplete.", "type": "array", "items": {"$ref": "MaterializedView"}}}}, "MaterializedView": {"id": "MaterializedView", "description": "A materialized view considered for a query job.", "type": "object", "properties": {"tableReference": {"description": "The candidate materialized view.", "$ref": "TableReference"}, "chosen": {"description": "Whether the materialized view is chosen for the query. A materialized view can be chosen to rewrite multiple parts of the same query. If a materialized view is chosen to rewrite any part of the query, then this field is true, even if the materialized view was not chosen to rewrite others parts.", "type": "boolean"}, "estimatedBytesSaved": {"description": "If present, specifies a best-effort estimation of the bytes saved by using the materialized view rather than its base tables.", "type": "string", "format": "int64"}, "rejectedReason": {"description": "If present, specifies the reason why the materialized view was not chosen for the query.", "type": "string", "enumDescriptions": ["Default unspecified value.", "View has no cached data because it has not refreshed yet.", "The estimated cost of the view is more expensive than another view or the base table. Note: The estimate cost might not match the billed cost.", "View has no cached data because a base table is truncated.", "View is invalidated because of a data change in one or more base tables.", "View is invalidated because a base table's partition expiration has changed.", "View is invalidated because a base table's partition has expired.", "View is invalidated because a base table has an incompatible metadata change.", "View is invalidated because it was refreshed with a time zone other than that of the current job.", "View is outside the time travel window."], "enum": ["REJECTED_REASON_UNSPECIFIED", "NO_DATA", "COST", "BASE_TABLE_TRUNCATED", "BASE_TABLE_DATA_CHANGE", "BASE_TABLE_PARTITION_EXPIRATION_CHANGE", "BASE_TABLE_EXPIRED_PARTITION", "BASE_TABLE_INCOMPATIBLE_METADATA_CHANGE", "TIME_ZONE", "OUT_OF_TIME_TRAVEL_WINDOW"]}}}, "MetadataCacheStatistics": {"id": "MetadataCacheStatistics", "description": "Statistics for metadata caching in BigLake tables.", "type": "object", "properties": {"tableMetadataCacheUsage": {"description": "Set for the Metadata caching eligible tables referenced in the query.", "type": "array", "items": {"$ref": "TableMetadataCacheUsage"}}}}, "TableMetadataCacheUsage": {"id": "TableMetadataCacheUsage", "description": "Table level detail on the usage of metadata caching. Only set for Metadata caching eligible tables referenced in the query.", "type": "object", "properties": {"tableReference": {"description": "Metadata caching eligible table referenced in the query.", "$ref": "TableReference"}, "unusedReason": {"description": "Reason for not using metadata caching for the table.", "type": "string", "enumDescriptions": ["Unused reasons not specified.", "Metadata cache was outside the table's maxStaleness.", "Other unknown reason."], "enum": ["UNUSED_REASON_UNSPECIFIED", "EXCEEDED_MAX_STALENESS", "OTHER_REASON"]}, "explanation": {"description": "Free form human-readable reason metadata caching was unused for the job.", "type": "string"}}}, "JobStatistics3": {"id": "JobStatistics3", "description": "Statistics for a load job.", "type": "object", "properties": {"inputFiles": {"description": "Output only. Number of source files in a load job.", "readOnly": true, "type": "string", "format": "int64"}, "inputFileBytes": {"description": "Output only. Number of bytes of source data in a load job.", "readOnly": true, "type": "string", "format": "int64"}, "outputRows": {"description": "Output only. Number of rows imported in a load job. Note that while an import job is in the running state, this value may change.", "readOnly": true, "type": "string", "format": "int64"}, "outputBytes": {"description": "Output only. Size of the loaded data in bytes. Note that while a load job is in the running state, this value may change.", "readOnly": true, "type": "string", "format": "int64"}, "badRecords": {"description": "Output only. The number of bad records encountered. Note that if the job has failed because of more bad records encountered than the maximum allowed in the load job configuration, then this number can be less than the total number of bad records present in the input data.", "readOnly": true, "type": "string", "format": "int64"}, "timeline": {"description": "Output only. Describes a timeline of job execution.", "readOnly": true, "type": "array", "items": {"$ref": "QueryTimelineSample"}}}}, "JobStatistics4": {"id": "JobStatistics4", "description": "Statistics for an extract job.", "type": "object", "properties": {"destinationUriFileCounts": {"description": "Output only. Number of files per destination URI or URI pattern specified in the extract configuration. These values will be in the same order as the URIs specified in the 'destinationUris' field.", "readOnly": true, "type": "array", "items": {"type": "string", "format": "int64"}}, "inputBytes": {"description": "Output only. Number of user bytes extracted into the result. This is the byte count as computed by BigQuery for billing purposes and doesn't have any relationship with the number of actual result bytes extracted in the desired format.", "readOnly": true, "type": "string", "format": "int64"}, "timeline": {"description": "Output only. Describes a timeline of job execution.", "readOnly": true, "type": "array", "items": {"$ref": "QueryTimelineSample"}}}}, "CopyJobStatistics": {"id": "CopyJobStatistics", "description": "Statistics for a copy job.", "type": "object", "properties": {"copiedRows": {"description": "Output only. Number of rows copied to the destination table.", "readOnly": true, "type": "string", "format": "int64"}, "copiedLogicalBytes": {"description": "Output only. Number of logical bytes copied to the destination table.", "readOnly": true, "type": "string", "format": "int64"}}}, "ScriptStatistics": {"id": "ScriptStatistics", "description": "Job statistics specific to the child job of a script.", "type": "object", "properties": {"evaluationKind": {"description": "Whether this child job was a statement or expression.", "type": "string", "enumDescriptions": ["", "The statement appears directly in the script.", "The statement evaluates an expression that appears in the script."], "enum": ["EVALUATION_KIND_UNSPECIFIED", "STATEMENT", "EXPRESSION"]}, "stackFrames": {"description": "Stack trace showing the line/column/procedure name of each frame on the stack at the point where the current evaluation happened. The leaf frame is first, the primary script is last. Never empty.", "type": "array", "items": {"$ref": "ScriptStackFrame"}}}}, "ScriptStackFrame": {"id": "ScriptStackFrame", "description": "Represents the location of the statement/expression being evaluated. Line and column numbers are defined as follows: - Line and column numbers start with one. That is, line 1 column 1 denotes the start of the script. - When inside a stored procedure, all line/column numbers are relative to the procedure body, not the script in which the procedure was defined. - Start/end positions exclude leading/trailing comments and whitespace. The end position always ends with a \";\", when present. - Multi-byte Unicode characters are treated as just one column. - If the original script (or procedure definition) contains TAB characters, a tab \"snaps\" the indentation forward to the nearest multiple of 8 characters, plus 1. For example, a TAB on column 1, 2, 3, 4, 5, 6 , or 8 will advance the next character to column 9. A TAB on column 9, 10, 11, 12, 13, 14, 15, or 16 will advance the next character to column 17.", "type": "object", "properties": {"startLine": {"description": "Output only. One-based start line.", "readOnly": true, "type": "integer", "format": "int32"}, "startColumn": {"description": "Output only. One-based start column.", "readOnly": true, "type": "integer", "format": "int32"}, "endLine": {"description": "Output only. One-based end line.", "readOnly": true, "type": "integer", "format": "int32"}, "endColumn": {"description": "Output only. One-based end column.", "readOnly": true, "type": "integer", "format": "int32"}, "procedureId": {"description": "Output only. Name of the active procedure, empty if in a top-level script.", "readOnly": true, "type": "string"}, "text": {"description": "Output only. Text of the current statement/expression.", "readOnly": true, "type": "string"}}}, "RowLevelSecurityStatistics": {"id": "RowLevelSecurityStatistics", "description": "Statistics for row-level security.", "type": "object", "properties": {"rowLevelSecurityApplied": {"description": "Whether any accessed data was protected by row access policies.", "type": "boolean"}}}, "DataMaskingStatistics": {"id": "DataMaskingStatistics", "description": "Statistics for data-masking.", "type": "object", "properties": {"dataMaskingApplied": {"description": "Whether any accessed data was protected by the data masking.", "type": "boolean"}}}, "TransactionInfo": {"id": "TransactionInfo", "description": "[Alpha] Information of a multi-statement transaction.", "type": "object", "properties": {"transactionId": {"description": "Output only. [Alpha] Id of the transaction.", "readOnly": true, "type": "string"}}}, "SessionInfo": {"id": "SessionInfo", "description": "[Preview] Information related to sessions.", "type": "object", "properties": {"sessionId": {"description": "Output only. The id of the session.", "readOnly": true, "type": "string"}}}, "JobStatus": {"id": "JobStatus", "type": "object", "properties": {"errorResult": {"description": "Output only. Final error result of the job. If present, indicates that the job has completed and was unsuccessful.", "readOnly": true, "$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}, "errors": {"description": "Output only. The first errors encountered during the running of the job. The final message includes the number of errors that caused the process to stop. Errors here do not necessarily mean that the job has not completed or was unsuccessful.", "readOnly": true, "type": "array", "items": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}}, "state": {"description": "Output only. Running state of the job. Valid states include 'PENDING', 'RUNNING', and 'DONE'.", "readOnly": true, "type": "string"}}}, "ErrorProto": {"id": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "description": "Error details.", "type": "object", "properties": {"reason": {"description": "A short error code that summarizes the error.", "type": "string"}, "location": {"description": "Specifies where the error occurred, if present.", "type": "string"}, "debugInfo": {"description": "Debugging information. This property is internal to Google and should not be used.", "type": "string"}, "message": {"description": "A human-readable description of the error.", "type": "string"}}}, "JobList": {"id": "JobList", "type": "object", "properties": {"etag": {"description": "A hash of this page of results.", "type": "string"}, "kind": {"description": "The resource type of the response.", "default": "bigquery#jobList", "type": "string"}, "nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}, "jobs": {"description": "List of jobs that were requested.", "type": "array", "items": {"type": "object", "properties": {"id": {"description": "Unique opaque ID of the job.", "type": "string"}, "kind": {"description": "The resource type.", "type": "string"}, "jobReference": {"description": "Unique opaque ID of the job.", "$ref": "JobReference"}, "state": {"description": "Running state of the job. When the state is DONE, errorResult can be checked to determine whether the job succeeded or failed.", "type": "string"}, "errorResult": {"description": "A result object that will be present only if the job has failed.", "$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}, "statistics": {"description": "Output only. Information about the job, including starting time and ending time of the job.", "readOnly": true, "$ref": "JobStatistics"}, "configuration": {"description": "Required. Describes the job configuration.", "$ref": "JobConfiguration"}, "status": {"description": "[Full-projection-only] Describes the status of this job.", "$ref": "JobStatus"}, "user_email": {"description": "[Full-projection-only] Email address of the user who ran the job.", "type": "string"}, "principal_subject": {"description": "[Full-projection-only] String representation of identity of requesting party. Populated for both first- and third-party identities. Only present for APIs that support third-party identities.", "type": "string"}}}}}}, "GetQueryResultsResponse": {"id": "GetQueryResultsResponse", "description": "Response object of GetQueryResults.", "type": "object", "properties": {"kind": {"description": "The resource type of the response.", "default": "bigquery#getQueryResultsResponse", "type": "string"}, "etag": {"description": "A hash of this response.", "type": "string"}, "schema": {"description": "The schema of the results. Present only when the query completes successfully.", "$ref": "TableSchema"}, "jobReference": {"description": "Reference to the BigQuery Job that was created to run the query. This field will be present even if the original request timed out, in which case GetQueryResults can be used to read the results once the query has completed. Since this API only returns the first page of results, subsequent pages can be fetched via the same mechanism (GetQueryResults).", "$ref": "JobReference"}, "totalRows": {"description": "The total number of rows in the complete query result set, which can be more than the number of rows in this single page of results. Present only when the query completes successfully.", "type": "string", "format": "uint64"}, "pageToken": {"description": "A token used for paging results. When this token is non-empty, it indicates additional results are available.", "type": "string"}, "rows": {"description": "An object with as many results as can be contained within the maximum permitted reply size. To get any additional rows, you can call GetQueryResults and specify the jobReference returned above. Present only when the query completes successfully. The REST-based representation of this data leverages a series of JSON f,v objects for indicating fields and values.", "type": "array", "items": {"$ref": "TableRow"}}, "totalBytesProcessed": {"description": "The total number of bytes processed for this query.", "type": "string", "format": "int64"}, "jobComplete": {"description": "Whether the query has completed or not. If rows or totalRows are present, this will always be true. If this is false, totalRows will not be available.", "type": "boolean"}, "errors": {"description": "Output only. The first errors or warnings encountered during the running of the job. The final message includes the number of errors that caused the process to stop. Errors here do not necessarily mean that the job has completed or was unsuccessful. For more information about error messages, see [Error messages](https://cloud.google.com/bigquery/docs/error-messages).", "readOnly": true, "type": "array", "items": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}}, "cacheHit": {"description": "Whether the query result was fetched from the query cache.", "type": "boolean"}, "numDmlAffectedRows": {"description": "Output only. The number of rows affected by a DML statement. Present only for DML statements INSERT, UPDATE or DELETE.", "readOnly": true, "type": "string", "format": "int64"}}}, "QueryRequest": {"id": "QueryRequest", "type": "object", "properties": {"kind": {"description": "The resource type of the request.", "default": "bigquery#queryRequest", "type": "string"}, "query": {"description": "Required. A query string to execute, using Google Standard SQL or legacy SQL syntax. Example: \"SELECT COUNT(f1) FROM myProjectId.myDatasetId.myTableId\".", "type": "string"}, "maxResults": {"description": "Optional. The maximum number of rows of data to return per page of results. Setting this flag to a small value such as 1000 and then paging through results might improve reliability when the query result set is large. In addition to this limit, responses are also limited to 10 MB. By default, there is no maximum row count, and only the byte limit applies.", "type": "integer", "format": "uint32"}, "defaultDataset": {"description": "Optional. Specifies the default datasetId and projectId to assume for any unqualified table names in the query. If not set, all table names in the query string must be qualified in the format 'datasetId.tableId'.", "$ref": "DatasetReference"}, "timeoutMs": {"description": "Optional. Optional: Specifies the maximum amount of time, in milliseconds, that the client is willing to wait for the query to complete. By default, this limit is 10 seconds (10,000 milliseconds). If the query is complete, the jobComplete field in the response is true. If the query has not yet completed, jobComplete is false. You can request a longer timeout period in the timeoutMs field. However, the call is not guaranteed to wait for the specified timeout; it typically returns after around 200 seconds (200,000 milliseconds), even if the query is not complete. If jobComplete is false, you can continue to wait for the query to complete by calling the getQueryResults method until the jobComplete field in the getQueryResults response is true.", "type": "integer", "format": "uint32"}, "dryRun": {"description": "Optional. If set to true, <PERSON>Query doesn't run the job. Instead, if the query is valid, BigQuery returns statistics about the job such as how many bytes would be processed. If the query is invalid, an error returns. The default value is false.", "type": "boolean"}, "preserveNulls": {"description": "This property is deprecated.", "deprecated": true, "type": "boolean"}, "useQueryCache": {"description": "Optional. Whether to look for the result in the query cache. The query cache is a best-effort cache that will be flushed whenever tables in the query are modified. The default value is true.", "default": "true", "type": "boolean"}, "useLegacySql": {"description": "Specifies whether to use BigQuery's legacy SQL dialect for this query. The default value is true. If set to false, the query will use BigQuery's GoogleSQL: https://cloud.google.com/bigquery/sql-reference/ When useLegacySql is set to false, the value of flattenResults is ignored; query will be run as if flattenResults is false.", "default": "true", "type": "boolean"}, "parameterMode": {"description": "GoogleSQL only. Set to POSITIONAL to use positional (?) query parameters or to NAMED to use named (@myparam) query parameters in this query.", "type": "string"}, "queryParameters": {"description": "Query parameters for GoogleSQL queries.", "type": "array", "items": {"$ref": "QueryParameter"}}, "location": {"description": "The geographic location where the job should run. See details at https://cloud.google.com/bigquery/docs/locations#specifying_your_location.", "type": "string"}, "formatOptions": {"description": "Optional. Output format adjustments.", "$ref": "DataFormatOptions"}, "connectionProperties": {"description": "Optional. Connection properties which can modify the query behavior.", "type": "array", "items": {"$ref": "ConnectionProperty"}}, "labels": {"description": "Optional. The labels associated with this query. Labels can be used to organize and group query jobs. Label keys and values can be no longer than 63 characters, can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label keys must start with a letter and each label in the list must have a different key.", "type": "object", "additionalProperties": {"type": "string"}}, "maximumBytesBilled": {"description": "Optional. Limits the bytes billed for this query. Queries with bytes billed above this limit will fail (without incurring a charge). If unspecified, the project default is used.", "type": "string", "format": "int64"}, "requestId": {"description": "Optional. A unique user provided identifier to ensure idempotent behavior for queries. Note that this is different from the job_id. It has the following properties: 1. It is case-sensitive, limited to up to 36 ASCII characters. A UUID is recommended. 2. Read only queries can ignore this token since they are nullipotent by definition. 3. For the purposes of idempotency ensured by the request_id, a request is considered duplicate of another only if they have the same request_id and are actually duplicates. When determining whether a request is a duplicate of another request, all parameters in the request that may affect the result are considered. For example, query, connection_properties, query_parameters, use_legacy_sql are parameters that affect the result and are considered when determining whether a request is a duplicate, but properties like timeout_ms don't affect the result and are thus not considered. Dry run query requests are never considered duplicate of another request. 4. When a duplicate mutating query request is detected, it returns: a. the results of the mutation if it completes successfully within the timeout. b. the running operation if it is still in progress at the end of the timeout. 5. Its lifetime is limited to 15 minutes. In other words, if two requests are sent with the same request_id, but more than 15 minutes apart, idempotency is not guaranteed.", "type": "string"}, "createSession": {"description": "Optional. If true, creates a new session using a randomly generated session_id. If false, runs query with an existing session_id passed in ConnectionProperty, otherwise runs query in non-session mode. The session location will be set to QueryRequest.location if it is present, otherwise it's set to the default location based on existing routing logic.", "type": "boolean"}}}, "DataFormatOptions": {"id": "DataFormatOptions", "description": "Options for data format adjustments.", "type": "object", "properties": {"useInt64Timestamp": {"description": "Optional. Output timestamp as usec int64. Default is false.", "type": "boolean"}}}, "QueryResponse": {"id": "QueryResponse", "type": "object", "properties": {"kind": {"description": "The resource type.", "default": "bigquery#queryResponse", "type": "string"}, "schema": {"description": "The schema of the results. Present only when the query completes successfully.", "$ref": "TableSchema"}, "jobReference": {"description": "Reference to the Job that was created to run the query. This field will be present even if the original request timed out, in which case GetQueryResults can be used to read the results once the query has completed. Since this API only returns the first page of results, subsequent pages can be fetched via the same mechanism (GetQueryResults).", "$ref": "JobReference"}, "totalRows": {"description": "The total number of rows in the complete query result set, which can be more than the number of rows in this single page of results.", "type": "string", "format": "uint64"}, "pageToken": {"description": "A token used for paging results. A non-empty token indicates that additional results are available. To see additional results, query the [`jobs.getQueryResults`](https://cloud.google.com/bigquery/docs/reference/rest/v2/jobs/getQueryResults) method. For more information, see [Paging through table data](https://cloud.google.com/bigquery/docs/paging-results).", "type": "string"}, "rows": {"description": "An object with as many results as can be contained within the maximum permitted reply size. To get any additional rows, you can call GetQueryResults and specify the jobReference returned above.", "type": "array", "items": {"$ref": "TableRow"}}, "totalBytesProcessed": {"description": "The total number of bytes processed for this query. If this query was a dry run, this is the number of bytes that would be processed if the query were run.", "type": "string", "format": "int64"}, "jobComplete": {"description": "Whether the query has completed or not. If rows or totalRows are present, this will always be true. If this is false, totalRows will not be available.", "type": "boolean"}, "errors": {"description": "Output only. The first errors or warnings encountered during the running of the job. The final message includes the number of errors that caused the process to stop. Errors here do not necessarily mean that the job has completed or was unsuccessful. For more information about error messages, see [Error messages](https://cloud.google.com/bigquery/docs/error-messages).", "readOnly": true, "type": "array", "items": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}}, "cacheHit": {"description": "Whether the query result was fetched from the query cache.", "type": "boolean"}, "numDmlAffectedRows": {"description": "Output only. The number of rows affected by a DML statement. Present only for DML statements INSERT, UPDATE or DELETE.", "readOnly": true, "type": "string", "format": "int64"}, "sessionInfo": {"description": "Output only. Information of the session if this job is part of one.", "readOnly": true, "$ref": "SessionInfo"}, "dmlStats": {"description": "Output only. Detailed statistics for DML statements INSERT, UPDATE, DELETE, MERGE or TRUNCATE.", "readOnly": true, "$ref": "DmlStats"}}}, "ProjectList": {"id": "ProjectList", "description": "Response object of ListProjects", "type": "object", "properties": {"kind": {"description": "The resource type of the response.", "default": "bigquery#projectList", "type": "string"}, "etag": {"description": "A hash of the page of results.", "type": "string"}, "nextPageToken": {"description": "Use this token to request the next page of results.", "type": "string"}, "projects": {"description": "Projects to which the user has at least READ access.", "type": "array", "items": {"description": "Information about a single project.", "type": "object", "properties": {"kind": {"description": "The resource type.", "type": "string"}, "id": {"description": "An opaque ID of this project.", "type": "string"}, "numericId": {"description": "The numeric ID of this project.", "type": "string", "format": "uint64"}, "projectReference": {"description": "A unique reference to this project.", "$ref": "ProjectReference"}, "friendlyName": {"description": "A descriptive name for this project. A wrapper is used here because friendlyName can be set to the empty string.", "type": "string"}}}}, "totalItems": {"description": "The total number of projects in the page. A wrapper is used here because the field should still be in the response when the value is 0.", "type": "integer", "format": "int32"}}}, "ProjectReference": {"id": "ProjectReference", "description": "A unique reference to a project.", "type": "object", "properties": {"projectId": {"description": "Required. ID of the project. Can be either the numeric ID or the assigned ID of the project.", "type": "string"}}}, "GetServiceAccountResponse": {"id": "GetServiceAccountResponse", "description": "Response object of GetServiceAccount", "type": "object", "properties": {"kind": {"description": "The resource type of the response.", "default": "bigquery#getServiceAccountResponse", "type": "string"}, "email": {"description": "The service account email address.", "type": "string"}}}, "Routine": {"id": "Routine", "description": "A user-defined function or a stored procedure.", "type": "object", "properties": {"etag": {"description": "Output only. A hash of this resource.", "readOnly": true, "type": "string"}, "routineReference": {"description": "Required. Reference describing the ID of this routine.", "$ref": "RoutineReference"}, "routineType": {"description": "Required. The type of routine.", "type": "string", "enumDescriptions": ["", "Non-built-in persistent scalar function.", "Stored procedure.", "Non-built-in persistent TVF.", "Non-built-in persistent aggregate function."], "enum": ["ROUTINE_TYPE_UNSPECIFIED", "SCALAR_FUNCTION", "PROCEDURE", "TABLE_VALUED_FUNCTION", "AGGREGATE_FUNCTION"]}, "creationTime": {"description": "Output only. The time when this routine was created, in milliseconds since the epoch.", "readOnly": true, "type": "string", "format": "int64"}, "lastModifiedTime": {"description": "Output only. The time when this routine was last modified, in milliseconds since the epoch.", "readOnly": true, "type": "string", "format": "int64"}, "language": {"description": "Optional. Defaults to \"SQL\" if remote_function_options field is absent, not set otherwise.", "type": "string", "enumDescriptions": ["", "SQL language.", "JavaScript language.", "Python language.", "Java language.", "Scala language."], "enum": ["LANGUAGE_UNSPECIFIED", "SQL", "JAVASCRIPT", "PYTHON", "JAVA", "SCALA"]}, "arguments": {"description": "Optional.", "type": "array", "items": {"$ref": "Argument"}}, "returnType": {"description": "Optional if language = \"SQL\"; required otherwise. Cannot be set if routine_type = \"TABLE_VALUED_FUNCTION\". If absent, the return type is inferred from definition_body at query time in each query that references this routine. If present, then the evaluated result will be cast to the specified returned type at query time. For example, for the functions created with the following statements: * `CREATE FUNCTION Add(x FLOAT64, y FLOAT64) RETURNS FLOAT64 AS (x + y);` * `CREATE FUNCTION Increment(x FLOAT64) AS (Add(x, 1));` * `CREATE FUNCTION Decrement(x FLOAT64) RETURNS FLOAT64 AS (Add(x, -1));` The return_type is `{type_kind: \"FLOAT64\"}` for `Add` and `Decrement`, and is absent for `Increment` (inferred as FLOAT64 at query time). Suppose the function `Add` is replaced by `CREATE OR REPLACE FUNCTION Add(x INT64, y INT64) AS (x + y);` Then the inferred return type of `Increment` is automatically changed to INT64 at query time, while the return type of `Decrement` remains FLOAT64.", "$ref": "StandardSqlDataType"}, "returnTableType": {"description": "Optional. Can be set only if routine_type = \"TABLE_VALUED_FUNCTION\". If absent, the return table type is inferred from definition_body at query time in each query that references this routine. If present, then the columns in the evaluated table result will be cast to match the column types specified in return table type, at query time.", "$ref": "StandardSqlTableType"}, "importedLibraries": {"description": "Optional. If language = \"JAVASCRIPT\", this field stores the path of the imported JAVASCRIPT libraries.", "type": "array", "items": {"type": "string"}}, "definitionBody": {"description": "Required. The body of the routine. For functions, this is the expression in the AS clause. If language=SQL, it is the substring inside (but excluding) the parentheses. For example, for the function created with the following statement: `CREATE FUNCTION JoinLines(x string, y string) as (concat(x, \"\\n\", y))` The definition_body is `concat(x, \"\\n\", y)` (\\n is not replaced with linebreak). If language=JAVASCRIPT, it is the evaluated string in the AS clause. For example, for the function created with the following statement: `CREATE FUNCTION f() RETURNS STRING LANGUAGE js AS 'return \"\\n\";\\n'` The definition_body is `return \"\\n\";\\n` Note that both \\n are replaced with linebreaks.", "type": "string"}, "description": {"description": "Optional. The description of the routine, if defined.", "type": "string"}, "determinismLevel": {"description": "Optional. The determinism level of the JavaScript UDF, if defined.", "type": "string", "enumDescriptions": ["The determinism of the UDF is unspecified.", "The UDF is deterministic, meaning that 2 function calls with the same inputs always produce the same result, even across 2 query runs.", "The UDF is not deterministic."], "enum": ["DETERMINISM_LEVEL_UNSPECIFIED", "DETERMINISTIC", "NOT_DETERMINISTIC"]}, "strictMode": {"description": "Optional. Can be set for procedures only. If true (default), the definition body will be validated in the creation and the updates of the procedure. For procedures with an argument of ANY TYPE, the definition body validtion is not supported at creation/update time, and thus this field must be set to false explicitly.", "type": "boolean"}, "remoteFunctionOptions": {"description": "Optional. Remote function specific options.", "$ref": "RemoteFunctionOptions"}, "sparkOptions": {"description": "Optional. Spark specific options.", "$ref": "SparkOptions"}, "dataGovernanceType": {"description": "Optional. Data governance specific option, if the value is DATA_MASKING, the function will be validated as masking functions.", "type": "string", "enumDescriptions": ["Unspecified data governance type.", "The data governance type is data masking."], "enum": ["DATA_GOVERNANCE_TYPE_UNSPECIFIED", "DATA_MASKING"]}}}, "Argument": {"id": "Argument", "description": "Input/output argument of a function or a stored procedure.", "type": "object", "properties": {"name": {"description": "Optional. The name of this argument. Can be absent for function return argument.", "type": "string"}, "argumentKind": {"description": "Optional. Defaults to FIXED_TYPE.", "type": "string", "enumDescriptions": ["", "The argument is a variable with fully specified type, which can be a struct or an array, but not a table.", "The argument is any type, including struct or array, but not a table. To be added: FIXED_TABLE, ANY_TABLE"], "enum": ["ARGUMENT_KIND_UNSPECIFIED", "FIXED_TYPE", "ANY_TYPE"]}, "mode": {"description": "Optional. Specifies whether the argument is input or output. Can be set for procedures only.", "type": "string", "enumDescriptions": ["", "The argument is input-only.", "The argument is output-only.", "The argument is both an input and an output."], "enum": ["MODE_UNSPECIFIED", "IN", "OUT", "INOUT"]}, "dataType": {"description": "Required unless argument_kind = ANY_TYPE.", "$ref": "StandardSqlDataType"}}}, "StandardSqlTableType": {"id": "StandardSqlTableType", "description": "A table type", "type": "object", "properties": {"columns": {"description": "The columns in this table type", "type": "array", "items": {"$ref": "StandardSqlField"}}}}, "RemoteFunctionOptions": {"id": "RemoteFunctionOptions", "description": "Options for a remote user-defined function.", "type": "object", "properties": {"endpoint": {"description": "Endpoint of the user-provided remote service, e.g. ```https://us-east1-my_gcf_project.cloudfunctions.net/remote_add```", "type": "string"}, "connection": {"description": "Fully qualified name of the user-provided connection object which holds the authentication information to send requests to the remote service. Format: ```\"projects/{projectId}/locations/{locationId}/connections/{connectionId}\"```", "type": "string"}, "userDefinedContext": {"description": "User-defined context as a set of key/value pairs, which will be sent as function invocation context together with batched arguments in the requests to the remote service. The total number of bytes of keys and values must be less than 8KB.", "type": "object", "additionalProperties": {"type": "string"}}, "maxBatchingRows": {"description": "Max number of rows in each batch sent to the remote service. If absent or if 0, BigQuery dynamically decides the number of rows in a batch.", "type": "string", "format": "int64"}}}, "SparkOptions": {"id": "SparkOptions", "description": "Options for a user-defined Spark routine.", "type": "object", "properties": {"connection": {"description": "Fully qualified name of the user-provided Spark connection object. Format: ```\"projects/{project_id}/locations/{location_id}/connections/{connection_id}\"```", "type": "string"}, "runtimeVersion": {"description": "Runtime version. If not specified, the default runtime version is used.", "type": "string"}, "containerImage": {"description": "Custom container image for the runtime environment.", "type": "string"}, "properties": {"description": "Configuration properties as a set of key/value pairs, which will be passed on to the Spark application. For more information, see [Apache Spark](https://spark.apache.org/docs/latest/index.html) and the [procedure option list](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#procedure_option_list).", "type": "object", "additionalProperties": {"type": "string"}}, "mainFileUri": {"description": "The main file/jar URI of the Spark application. Exactly one of the definition_body field and the main_file_uri field must be set for Python. Exactly one of main_class and main_file_uri field should be set for Java/Scala language type.", "type": "string"}, "pyFileUris": {"description": "Python files to be placed on the PYTHONPATH for PySpark application. Supported file types: `.py`, `.egg`, and `.zip`. For more information about Apache Spark, see [Apache Spark](https://spark.apache.org/docs/latest/index.html).", "type": "array", "items": {"type": "string"}}, "jarUris": {"description": "JARs to include on the driver and executor CLASSPATH. For more information about Apache Spark, see [Apache Spark](https://spark.apache.org/docs/latest/index.html).", "type": "array", "items": {"type": "string"}}, "fileUris": {"description": "Files to be placed in the working directory of each executor. For more information about Apache Spark, see [Apache Spark](https://spark.apache.org/docs/latest/index.html).", "type": "array", "items": {"type": "string"}}, "archiveUris": {"description": "Archive files to be extracted into the working directory of each executor. For more information about Apache Spark, see [Apache Spark](https://spark.apache.org/docs/latest/index.html).", "type": "array", "items": {"type": "string"}}, "mainClass": {"description": "The fully qualified name of a class in jar_uris, for example, com.example.wordcount. Exactly one of main_class and main_jar_uri field should be set for Java/Scala language type.", "type": "string"}}}, "ListRoutinesResponse": {"id": "ListRoutinesResponse", "type": "object", "properties": {"routines": {"description": "Routines in the requested dataset. Unless read_mask is set in the request, only the following fields are populated: etag, project_id, dataset_id, routine_id, routine_type, creation_time, last_modified_time, language, and remote_function_options.", "type": "array", "items": {"$ref": "Routine"}}, "nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}}}, "ListRowAccessPoliciesResponse": {"id": "ListRowAccessPoliciesResponse", "description": "Response message for the ListRowAccessPolicies method.", "type": "object", "properties": {"rowAccessPolicies": {"description": "Row access policies on the requested table.", "type": "array", "items": {"$ref": "RowAccessPolicy"}}, "nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}}}, "RowAccessPolicy": {"id": "RowAccessPolicy", "description": "Represents access on a subset of rows on the specified table, defined by its filter predicate. Access to the subset of rows is controlled by its IAM policy.", "type": "object", "properties": {"etag": {"description": "Output only. A hash of this resource.", "readOnly": true, "type": "string"}, "rowAccessPolicyReference": {"description": "Required. Reference describing the ID of this row access policy.", "$ref": "RowAccessPolicyReference"}, "filterPredicate": {"description": "Required. A SQL boolean expression that represents the rows defined by this row access policy, similar to the boolean expression in a WHERE clause of a SELECT query on a table. References to other tables, routines, and temporary functions are not supported. Examples: region=\"EU\" date_field = CAST('2019-9-27' as DATE) nullable_field is not NULL numeric_field BETWEEN 1.0 AND 5.0", "type": "string"}, "creationTime": {"description": "Output only. The time when this row access policy was created, in milliseconds since the epoch.", "readOnly": true, "type": "string", "format": "google-datetime"}, "lastModifiedTime": {"description": "Output only. The time when this row access policy was last modified, in milliseconds since the epoch.", "readOnly": true, "type": "string", "format": "google-datetime"}}}, "Table": {"id": "Table", "type": "object", "properties": {"kind": {"description": "The type of resource ID.", "default": "bigquery#table", "type": "string"}, "etag": {"description": "Output only. A hash of this resource.", "readOnly": true, "type": "string"}, "id": {"description": "Output only. An opaque ID uniquely identifying the table.", "readOnly": true, "type": "string"}, "selfLink": {"description": "Output only. A URL that can be used to access this resource again.", "readOnly": true, "type": "string"}, "tableReference": {"description": "Required. Reference describing the ID of this table.", "$ref": "TableReference"}, "friendlyName": {"description": "Optional. A descriptive name for this table.", "type": "string"}, "description": {"description": "Optional. A user-friendly description of this table.", "type": "string"}, "labels": {"description": "The labels associated with this table. You can use these to organize and group your tables. Label keys and values can be no longer than 63 characters, can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter and each label in the list must have a different key.", "type": "object", "additionalProperties": {"type": "string"}}, "schema": {"description": "Optional. Describes the schema of this table.", "$ref": "TableSchema"}, "timePartitioning": {"description": "If specified, configures time-based partitioning for this table.", "$ref": "TimePartitioning"}, "rangePartitioning": {"description": "If specified, configures range partitioning for this table.", "$ref": "RangePartitioning"}, "clustering": {"description": "Clustering specification for the table. Must be specified with time-based partitioning, data in the table will be first partitioned and subsequently clustered.", "$ref": "Clustering"}, "requirePartitionFilter": {"description": "Optional. If set to true, queries over this table require a partition filter that can be used for partition elimination to be specified.", "default": "false", "type": "boolean"}, "numBytes": {"description": "Output only. The size of this table in logical bytes, excluding any data in the streaming buffer.", "readOnly": true, "type": "string", "format": "int64"}, "numPhysicalBytes": {"description": "Output only. The physical size of this table in bytes. This includes storage used for time travel.", "readOnly": true, "type": "string", "format": "int64"}, "numLongTermBytes": {"description": "Output only. The number of logical bytes in the table that are considered \"long-term storage\".", "readOnly": true, "type": "string", "format": "int64"}, "numRows": {"description": "Output only. The number of rows of data in this table, excluding any data in the streaming buffer.", "readOnly": true, "type": "string", "format": "uint64"}, "creationTime": {"description": "Output only. The time when this table was created, in milliseconds since the epoch.", "readOnly": true, "type": "string", "format": "int64"}, "expirationTime": {"description": "Optional. The time when this table expires, in milliseconds since the epoch. If not present, the table will persist indefinitely. Expired tables will be deleted and their storage reclaimed. The defaultTableExpirationMs property of the encapsulating dataset can be used to set a default expirationTime on newly created tables.", "type": "string", "format": "int64"}, "lastModifiedTime": {"description": "Output only. The time when this table was last modified, in milliseconds since the epoch.", "readOnly": true, "type": "string", "format": "uint64"}, "type": {"description": "Output only. Describes the table type. The following values are supported: * `TABLE`: A normal BigQuery table. * `VIEW`: A virtual table defined by a SQL query. * `EXTERNAL`: A table that references data stored in an external storage system, such as Google Cloud Storage. * `MATERIALIZED_VIEW`: A precomputed view defined by a SQL query. * `SNAPSHOT`: An immutable BigQuery table that preserves the contents of a base table at a particular time. See additional information on [table snapshots](/bigquery/docs/table-snapshots-intro). The default value is `TABLE`.", "readOnly": true, "type": "string"}, "view": {"description": "Optional. The view definition.", "$ref": "ViewDefinition"}, "materializedView": {"description": "Optional. The materialized view definition.", "$ref": "MaterializedViewDefinition"}, "materializedViewStatus": {"description": "Output only. The materialized view status.", "readOnly": true, "$ref": "MaterializedViewStatus"}, "externalDataConfiguration": {"description": "Optional. Describes the data format, location, and other properties of a table stored outside of BigQuery. By defining these properties, the data source can then be queried as if it were a standard BigQuery table.", "$ref": "ExternalDataConfiguration"}, "biglakeConfiguration": {"description": "Optional. Specifies the configuration of a BigLake managed table.", "$ref": "BigLakeConfiguration"}, "location": {"description": "Output only. The geographic location where the table resides. This value is inherited from the dataset.", "readOnly": true, "type": "string"}, "streamingBuffer": {"description": "Output only. Contains information regarding this table's streaming buffer, if one is present. This field will be absent if the table is not being streamed to or if there is no data in the streaming buffer.", "readOnly": true, "$ref": "Streamingbuffer"}, "encryptionConfiguration": {"description": "Custom encryption configuration (e.g., Cloud KMS keys).", "$ref": "EncryptionConfiguration"}, "snapshotDefinition": {"description": "Output only. Contains information about the snapshot. This value is set via snapshot creation.", "readOnly": true, "$ref": "SnapshotDefinition"}, "defaultCollation": {"description": "Optional. Defines the default collation specification of new STRING fields in the table. During table creation or update, if a STRING field is added to this table without explicit collation specified, then the table inherits the table default collation. A change to this field affects only fields added afterwards, and does not alter the existing fields. The following values are supported: * 'und:ci': undetermined locale, case insensitive. * '': empty string. Default to case-sensitive behavior.", "type": "string"}, "defaultRoundingMode": {"description": "Optional. Defines the default rounding mode specification of new decimal fields (NUMERIC OR BIGNUMERIC) in the table. During table creation or update, if a decimal field is added to this table without an explicit rounding mode specified, then the field inherits the table default rounding mode. Changing this field doesn't affect existing fields.", "type": "string", "enumDescriptions": ["Unspecified will default to using ROUND_HALF_AWAY_FROM_ZERO.", "ROUND_HALF_AWAY_FROM_ZERO rounds half values away from zero when applying precision and scale upon writing of NUMERIC and BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1 1.5, 1.6, 1.7, 1.8, 1.9 => 2", "ROUND_HALF_EVEN rounds half values to the nearest even value when applying precision and scale upon writing of NUMERIC and BIGNUMERIC values. For Scale: 0 1.1, 1.2, 1.3, 1.4 => 1 1.5 => 2 1.6, 1.7, 1.8, 1.9 => 2 2.5 => 2"], "enum": ["ROUNDING_MODE_UNSPECIFIED", "ROUND_HALF_AWAY_FROM_ZERO", "ROUND_HALF_EVEN"]}, "cloneDefinition": {"description": "Output only. Contains information about the clone. This value is set via the clone operation.", "readOnly": true, "$ref": "CloneDefinition"}, "numTimeTravelPhysicalBytes": {"description": "Output only. Number of physical bytes used by time travel storage (deleted or changed data). This data is not kept in real time, and might be delayed by a few seconds to a few minutes.", "readOnly": true, "type": "string", "format": "int64"}, "numTotalLogicalBytes": {"description": "Output only. Total number of logical bytes in the table or materialized view.", "readOnly": true, "type": "string", "format": "int64"}, "numActiveLogicalBytes": {"description": "Output only. Number of logical bytes that are less than 90 days old.", "readOnly": true, "type": "string", "format": "int64"}, "numLongTermLogicalBytes": {"description": "Output only. Number of logical bytes that are more than 90 days old.", "readOnly": true, "type": "string", "format": "int64"}, "numTotalPhysicalBytes": {"description": "Output only. The physical size of this table in bytes. This also includes storage used for time travel. This data is not kept in real time, and might be delayed by a few seconds to a few minutes.", "readOnly": true, "type": "string", "format": "int64"}, "numActivePhysicalBytes": {"description": "Output only. Number of physical bytes less than 90 days old. This data is not kept in real time, and might be delayed by a few seconds to a few minutes.", "readOnly": true, "type": "string", "format": "int64"}, "numLongTermPhysicalBytes": {"description": "Output only. Number of physical bytes more than 90 days old. This data is not kept in real time, and might be delayed by a few seconds to a few minutes.", "readOnly": true, "type": "string", "format": "int64"}, "numPartitions": {"description": "Output only. The number of partitions present in the table or materialized view. This data is not kept in real time, and might be delayed by a few seconds to a few minutes.", "readOnly": true, "type": "string", "format": "int64"}, "maxStaleness": {"description": "Optional. The maximum staleness of data that could be returned when the table (or stale MV) is queried. Staleness encoded as a string encoding of sql IntervalValue type.", "type": "string"}, "tableConstraints": {"description": "Optional. Tables Primary Key and Foreign Key information", "$ref": "TableConstraints"}, "tableReplicationInfo": {"description": "Optional. Table replication info for table created `AS REPLICA` DDL like: `CREATE MATERIALIZED VIEW mv1 AS REPLICA OF src_mv`", "$ref": "TableReplicationInfo"}}}, "ViewDefinition": {"id": "ViewDefinition", "type": "object", "properties": {"query": {"description": "Required. A query that Big<PERSON><PERSON>y executes when the view is referenced.", "type": "string"}, "userDefinedFunctionResources": {"description": "Describes user-defined function resources used in the query.", "type": "array", "items": {"$ref": "UserDefinedFunctionResource"}}, "useLegacySql": {"description": "Specifies whether to use BigQuery's legacy SQL for this view. The default value is true. If set to false, the view will use BigQuery's GoogleSQL: https://cloud.google.com/bigquery/sql-reference/ Queries and views that reference this view must use the same flag value. A wrapper is used here because the default value is True.", "type": "boolean"}, "useExplicitColumnNames": {"description": "True if the column names are explicitly specified. For example by using the 'CREATE VIEW v(c1, c2) AS ...' syntax. Can only be set for GoogleSQL views.", "type": "boolean"}}}, "MaterializedViewDefinition": {"id": "MaterializedViewDefinition", "description": "Definition and configuration of a materialized view.", "type": "object", "properties": {"query": {"description": "Required. A query whose results are persisted.", "type": "string"}, "lastRefreshTime": {"description": "Output only. The time when this materialized view was last refreshed, in milliseconds since the epoch.", "readOnly": true, "type": "string", "format": "int64"}, "enableRefresh": {"description": "Optional. Enable automatic refresh of the materialized view when the base table is updated. The default value is \"true\".", "type": "boolean"}, "refreshIntervalMs": {"description": "Optional. The maximum frequency at which this materialized view will be refreshed. The default value is \"1800000\" (30 minutes).", "type": "string", "format": "uint64"}, "allowNonIncrementalDefinition": {"description": "Optional. This option declares authors intention to construct a materialized view that will not be refreshed incrementally.", "type": "boolean"}}}, "MaterializedViewStatus": {"id": "MaterializedViewStatus", "description": "Status of a materialized view. The last refresh timestamp status is omitted here, but is present in the MaterializedViewDefinition message.", "type": "object", "properties": {"refreshWatermark": {"description": "Output only. Refresh watermark of materialized view. The base tables' data were collected into the materialized view cache until this time.", "readOnly": true, "type": "string", "format": "google-datetime"}, "lastRefreshStatus": {"description": "Output only. Error result of the last automatic refresh. If present, indicates that the last automatic refresh was unsuccessful.", "readOnly": true, "$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}}}, "BigLakeConfiguration": {"id": "BigLakeConfiguration", "description": "Configuration for BigLake managed tables.", "type": "object", "properties": {"connectionId": {"description": "Required. The connection specifying the credentials to be used to read and write to external storage, such as Cloud Storage. The connection_id can have the form \"<project\\_id>.<location\\_id>.<connection\\_id>\" or \"projects/<project\\_id>/locations/<location\\_id>/connections/<connection\\_id>\".", "type": "string"}, "storageUri": {"description": "Required. The fully qualified location prefix of the external folder where table data is stored. The '*' wildcard character is not allowed. The URI should be in the format \"gs://bucket/path_to_table/\"", "type": "string"}, "fileFormat": {"description": "Required. The file format the table data is stored in.", "type": "string", "enumDescriptions": ["", ""], "enum": ["FILE_FORMAT_UNSPECIFIED", "PARQUET"]}, "tableFormat": {"description": "Required. The table format the metadata only snapshots are stored in.", "type": "string", "enumDescriptions": ["", ""], "enum": ["TABLE_FORMAT_UNSPECIFIED", "ICEBERG"]}}}, "Streamingbuffer": {"id": "Streamingbuffer", "type": "object", "properties": {"estimatedBytes": {"description": "Output only. A lower-bound estimate of the number of bytes currently in the streaming buffer.", "readOnly": true, "type": "string", "format": "uint64"}, "estimatedRows": {"description": "Output only. A lower-bound estimate of the number of rows currently in the streaming buffer.", "readOnly": true, "type": "string", "format": "uint64"}, "oldestEntryTime": {"description": "Output only. Contains the timestamp of the oldest entry in the streaming buffer, in milliseconds since the epoch, if the streaming buffer is available.", "readOnly": true, "type": "string", "format": "uint64"}}}, "SnapshotDefinition": {"id": "SnapshotDefinition", "description": "Information about base table and snapshot time of the snapshot.", "type": "object", "properties": {"baseTableReference": {"description": "Required. Reference describing the ID of the table that was snapshot.", "$ref": "TableReference"}, "snapshotTime": {"description": "Required. The time at which the base table was snapshot. This value is reported in the JSON response using RFC3339 format.", "type": "string", "format": "google-datetime"}}}, "CloneDefinition": {"id": "CloneDefinition", "description": "Information about base table and clone time of a table clone.", "type": "object", "properties": {"baseTableReference": {"description": "Required. Reference describing the ID of the table that was cloned.", "$ref": "TableReference"}, "cloneTime": {"description": "Required. The time at which the base table was cloned. This value is reported in the JSON response using RFC3339 format.", "type": "string", "format": "google-datetime"}}}, "TableConstraints": {"id": "TableConstraints", "description": "The TableConstraints defines the primary key and foreign key.", "type": "object", "properties": {"primaryKey": {"description": "Optional. Represents a primary key constraint on a table's columns. Present only if the table has a primary key. The primary key is not enforced.", "$ref": "<PERSON><PERSON><PERSON>"}, "foreignKeys": {"description": "Optional. Present only if the table has a foreign key. The foreign key is not enforced.", "type": "array", "items": {"$ref": "ForeignKey"}}}}, "PrimaryKey": {"id": "<PERSON><PERSON><PERSON>", "description": "Represents the primary key constraint on a table's columns.", "type": "object", "properties": {"columns": {"description": "Required. The columns that are composed of the primary key constraint.", "type": "array", "items": {"type": "string"}}}}, "ForeignKey": {"id": "ForeignKey", "description": "Represents a foreign key constraint on a table's columns.", "type": "object", "properties": {"name": {"description": "Optional. Set only if the foreign key constraint is named.", "type": "string"}, "referencedTable": {"description": "Required. The table that holds the primary key and is referenced by this foreign key.", "$ref": "TableReference"}, "columnReferences": {"description": "Required. The columns that compose the foreign key.", "type": "array", "items": {"$ref": "ColumnReference"}}}}, "ColumnReference": {"id": "ColumnReference", "description": "The pair of the foreign key column and primary key column.", "type": "object", "properties": {"referencingColumn": {"description": "Required. The column that composes the foreign key.", "type": "string"}, "referencedColumn": {"description": "Required. The column in the primary key that are referenced by the referencing_column.", "type": "string"}}}, "TableReplicationInfo": {"id": "TableReplicationInfo", "description": "Replication info of a table created using `AS REPLICA` DDL like: `CREATE MATERIALIZED VIEW mv1 AS REPLICA OF src_mv`", "type": "object", "properties": {"sourceTable": {"description": "Required. Source table reference that is replicated.", "$ref": "TableReference"}, "replicationIntervalMs": {"description": "Required. Specifies the interval at which the source table is polled for updates.", "type": "string", "format": "int64"}, "replicatedSourceLastRefreshTime": {"description": "Optional. Output only. If source is a materialized view, this field signifies the last refresh time of the source.", "readOnly": true, "type": "string", "format": "int64"}, "replicationStatus": {"description": "Optional. Output only. Replication status of configured replication.", "readOnly": true, "type": "string", "enumDescriptions": ["", "Replication is Active with no errors.", "Source object is deleted.", "Source revoked replication permissions.", "Source configuration doesn’t allow replication."], "enum": ["REPLICATION_STATUS_UNSPECIFIED", "ACTIVE", "SOURCE_DELETED", "PERMISSION_DENIED", "UNSUPPORTED_CONFIGURATION"]}, "replicationError": {"description": "Optional. Output only. Replication error that will permanently stopped table replication.", "readOnly": true, "$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}}}, "TableList": {"id": "TableList", "type": "object", "properties": {"kind": {"description": "The type of list.", "default": "bigquery#tableList", "type": "string"}, "etag": {"description": "A hash of this page of results.", "type": "string"}, "nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}, "tables": {"description": "Tables in the requested dataset.", "type": "array", "items": {"type": "object", "properties": {"kind": {"description": "The resource type.", "type": "string"}, "id": {"description": "An opaque ID of the table.", "type": "string"}, "tableReference": {"description": "A reference uniquely identifying table.", "$ref": "TableReference"}, "friendlyName": {"description": "The user-friendly name for this table.", "type": "string"}, "type": {"description": "The type of table.", "type": "string"}, "timePartitioning": {"description": "The time-based partitioning for this table.", "$ref": "TimePartitioning"}, "rangePartitioning": {"description": "The range partitioning for this table.", "$ref": "RangePartitioning"}, "clustering": {"description": "Clustering specification for this table, if configured.", "$ref": "Clustering"}, "hivePartitioningOptions": {"description": "The hive partitioning configuration for this table, when applicable.", "$ref": "HivePartitioningOptions"}, "labels": {"description": "The labels associated with this table. You can use these to organize and group your tables.", "type": "object", "additionalProperties": {"type": "string"}}, "view": {"description": "Additional details for a view.", "type": "object", "properties": {"useLegacySql": {"description": "True if view is defined in legacy SQL dialect, false if in GoogleSQL.", "type": "boolean"}}}, "creationTime": {"description": "Output only. The time when this table was created, in milliseconds since the epoch.", "readOnly": true, "type": "string", "format": "int64"}, "expirationTime": {"description": "The time when this table expires, in milliseconds since the epoch. If not present, the table will persist indefinitely. Expired tables will be deleted and their storage reclaimed.", "type": "string", "format": "int64"}}}}, "totalItems": {"description": "The total number of tables in the dataset.", "type": "integer", "format": "int32"}}}, "TableDataInsertAllRequest": {"id": "TableDataInsertAllRequest", "type": "object", "properties": {"kind": {"description": "Optional. The resource type of the response. The value is not checked at the backend. Historically, it has been set to \"bigquery#tableDataInsertAllRequest\" but you are not required to set it.", "default": "bigquery#tableDataInsertAllRequest", "type": "string"}, "skipInvalidRows": {"description": "Optional. Insert all valid rows of a request, even if invalid rows exist. The default value is false, which causes the entire request to fail if any invalid rows exist.", "type": "boolean"}, "ignoreUnknownValues": {"description": "Optional. Accept rows that contain values that do not match the schema. The unknown values are ignored. Default is false, which treats unknown values as errors.", "type": "boolean"}, "templateSuffix": {"description": "Optional. If specified, treats the destination table as a base template, and inserts the rows into an instance table named \"{destination}{templateSuffix}\". BigQuery will manage creation of the instance table, using the schema of the base template table. See https://cloud.google.com/bigquery/streaming-data-into-bigquery#template-tables for considerations when working with templates tables.", "type": "string"}, "rows": {"type": "array", "items": {"type": "object", "properties": {"insertId": {"type": "string"}, "json": {"$ref": "JsonObject"}}}}, "traceId": {"description": "Optional. Unique request trace id. Used for debugging purposes only. It is case-sensitive, limited to up to 36 ASCII characters. A UUID is recommended.", "type": "string"}}}, "TableDataInsertAllResponse": {"id": "TableDataInsertAllResponse", "type": "object", "properties": {"kind": {"description": "Returns \"bigquery#tableDataInsertAllResponse\".", "default": "bigquery#tableDataInsertAllResponse", "type": "string"}, "insertErrors": {"type": "array", "items": {"type": "object", "properties": {"index": {"type": "integer", "format": "uint32"}, "errors": {"type": "array", "items": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}}}}}}}, "SetIamPolicyRequest": {"id": "SetIamPolicyRequest", "description": "Request message for `SetIamPolicy` method.", "type": "object", "properties": {"policy": {"description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.", "$ref": "Policy"}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "type": "string", "format": "google-fieldmask"}}}, "Policy": {"id": "Policy", "description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "type": "object", "properties": {"version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "type": "integer", "format": "int32"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "type": "array", "items": {"$ref": "Binding"}}, "auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "type": "array", "items": {"$ref": "AuditConfig"}}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "type": "string", "format": "byte"}}}, "Binding": {"id": "Binding", "description": "Associates `members`, or principals, with a `role`.", "type": "object", "properties": {"role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`.", "type": "string"}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding.", "type": "array", "items": {"type": "string"}}, "condition": {"description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "$ref": "Expr"}}}, "Expr": {"id": "Expr", "description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "type": "object", "properties": {"expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}, "description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}}}, "AuditConfig": {"id": "AuditConfig", "description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "type": "object", "properties": {"service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}, "auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "type": "array", "items": {"$ref": "AuditLogConfig"}}}}, "AuditLogConfig": {"id": "AuditLogConfig", "description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "type": "object", "properties": {"logType": {"description": "The log type that this config enables.", "type": "string", "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"]}, "exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "type": "array", "items": {"type": "string"}}}}, "GetIamPolicyRequest": {"id": "GetIamPolicyRequest", "description": "Request message for `GetIamPolicy` method.", "type": "object", "properties": {"options": {"description": "OPTIONAL: A `GetPolicyOptions` object for specifying options to `GetIamPolicy`.", "$ref": "GetPolicyOptions"}}}, "GetPolicyOptions": {"id": "GetPolicyOptions", "description": "Encapsulates settings provided to GetIamPolicy.", "type": "object", "properties": {"requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "type": "integer", "format": "int32"}}}, "TestIamPermissionsRequest": {"id": "TestIamPermissionsRequest", "description": "Request message for `TestIamPermissions` method.", "type": "object", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "type": "array", "items": {"type": "string"}}}}, "TestIamPermissionsResponse": {"id": "TestIamPermissionsResponse", "description": "Response message for `TestIamPermissions` method.", "type": "object", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "type": "array", "items": {"type": "string"}}}}, "LocationMetadata": {"id": "LocationMetadata", "description": "BigQuery-specific metadata about a location. This will be set on google.cloud.location.Location.metadata in Cloud Location API responses.", "type": "object", "properties": {"legacyLocationId": {"description": "The legacy BigQuery location ID, e.g. “EU” for the “europe” location. This is for any API consumers that need the legacy “US” and “EU” locations.", "type": "string"}}}, "TableRow": {"id": "TableRow", "type": "object", "properties": {"f": {"description": "Represents a single row in the result set, consisting of one or more fields.", "type": "array", "items": {"$ref": "TableCell"}}}}, "TableCell": {"id": "TableCell", "type": "object", "properties": {"v": {"type": "any"}}}, "JsonObject": {"id": "JsonObject", "description": "Represents a single JSON object.", "type": "object", "additionalProperties": {"$ref": "JsonValue"}}, "JsonValue": {"id": "JsonValue", "type": "any"}, "TableDataList": {"id": "TableDataList", "type": "object", "properties": {"pageToken": {"description": "A token used for paging results. Providing this token instead of the startIndex parameter can help you retrieve stable results when an underlying table is changing.", "type": "string"}, "kind": {"description": "The resource type of the response.", "default": "bigquery#tableDataList", "type": "string"}, "rows": {"description": "Rows of results.", "type": "array", "items": {"$ref": "TableRow"}}, "totalRows": {"description": "Total rows of the entire table. In order to show default value 0 we have to present it as string.", "type": "string", "format": "int64"}, "etag": {"description": "A hash of this page of results.", "type": "string"}}}}, "resources": {"datasets": {"methods": {"get": {"id": "bigquery.datasets.get", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. Project ID of the requested dataset", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the requested dataset", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetView": {"description": "Optional. Specifies the view that determines which dataset information is returned. By default, metadata and ACL information are returned.", "location": "query", "type": "string", "enumDescriptions": ["The default value. Default to the FULL view.", "Includes metadata information for the dataset, such as location, etag, lastModifiedTime, etc.", "Includes ACL information for the dataset, which defines dataset access for one or more entities.", "Includes both dataset metadata and ACL information."], "enum": ["DATASET_VIEW_UNSPECIFIED", "METADATA", "ACL", "FULL"]}}, "parameterOrder": ["projectId", "datasetId"], "response": {"$ref": "Dataset"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Returns the dataset specified by datasetID."}, "insert": {"id": "bigquery.datasets.insert", "path": "bigquery/v2/projects/{+projectId}/datasets", "flatPath": "bigquery/v2/projects/{projectsId}/datasets", "httpMethod": "POST", "parameters": {"projectId": {"description": "Required. Project ID of the new dataset", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId"], "request": {"$ref": "Dataset"}, "response": {"$ref": "Dataset"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Creates a new empty dataset."}, "patch": {"id": "bigquery.datasets.patch", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}", "httpMethod": "PATCH", "parameters": {"projectId": {"description": "Required. Project ID of the dataset being updated", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the dataset being updated", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId", "datasetId"], "request": {"$ref": "Dataset"}, "response": {"$ref": "Dataset"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Updates information in an existing dataset. The update method replaces the entire dataset resource, whereas the patch method only replaces fields that are provided in the submitted dataset resource. This method supports RFC5789 patch semantics."}, "update": {"id": "bigquery.datasets.update", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}", "httpMethod": "PUT", "parameters": {"projectId": {"description": "Required. Project ID of the dataset being updated", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the dataset being updated", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId", "datasetId"], "request": {"$ref": "Dataset"}, "response": {"$ref": "Dataset"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Updates information in an existing dataset. The update method replaces the entire dataset resource, whereas the patch method only replaces fields that are provided in the submitted dataset resource."}, "delete": {"id": "bigquery.datasets.delete", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}", "httpMethod": "DELETE", "parameters": {"projectId": {"description": "Required. Project ID of the dataset being deleted", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of dataset being deleted", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "deleteContents": {"description": "If True, delete all the tables in the dataset. If False and the dataset contains tables, the request will fail. Default is False", "location": "query", "type": "boolean"}}, "parameterOrder": ["projectId", "datasetId"], "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Deletes the dataset specified by the datasetId value. Before you can delete a dataset, you must delete all its tables, either manually or by specifying deleteContents. Immediately after deletion, you can create another dataset with the same name."}, "list": {"id": "bigquery.datasets.list", "path": "bigquery/v2/projects/{+projectId}/datasets", "flatPath": "bigquery/v2/projects/{projectsId}/datasets", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. Project ID of the datasets to be listed", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "location": "query", "type": "integer", "format": "uint32"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results", "location": "query", "type": "string"}, "all": {"description": "Whether to list all datasets, including hidden ones", "location": "query", "type": "boolean"}, "filter": {"description": "An expression for filtering the results of the request by label. The syntax is \\\"labels.<name>[:<value>]\\\". Multiple filters can be ANDed together by connecting with a space. Example: \\\"labels.department:receiving labels.active\\\". See [Filtering datasets using labels](/bigquery/docs/labeling-datasets#filtering_datasets_using_labels) for details.", "location": "query", "type": "string"}}, "parameterOrder": ["projectId"], "response": {"$ref": "DatasetList"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Lists all datasets in the specified project to which the user has been granted the READER dataset role."}, "setIamPolicy": {"id": "bigquery.datasets.setIamPolicy", "path": "bigquery/v2/{+resource}:setIamPolicy", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}:setIamPolicy", "httpMethod": "POST", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/datasets/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["resource"], "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors."}, "getIamPolicy": {"id": "bigquery.datasets.getIamPolicy", "path": "bigquery/v2/{+resource}:getIamPolicy", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}:getIamPolicy", "httpMethod": "POST", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/datasets/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["resource"], "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set."}, "testIamPermissions": {"id": "bigquery.datasets.testIamPermissions", "path": "bigquery/v2/{+resource}:testIamPermissions", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}:testIamPermissions", "httpMethod": "POST", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/datasets/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["resource"], "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning."}}}, "models": {"methods": {"get": {"id": "bigquery.models.get", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/models/{+modelId}", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/models/{modelsId}", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. Project ID of the requested model.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the requested model.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "modelId": {"description": "Required. Model ID of the requested model.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId", "datasetId", "modelId"], "response": {"$ref": "Model"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Gets the specified model resource by model ID."}, "list": {"id": "bigquery.models.list", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/models", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/models", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. Project ID of the models to list.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the models to list.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "location": "query", "type": "integer", "format": "uint32"}, "pageToken": {"description": "Page token, returned by a previous call to request the next page of results", "location": "query", "type": "string"}}, "parameterOrder": ["projectId", "datasetId"], "response": {"$ref": "ListModelsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Lists all models in the specified dataset. Requires the READER dataset role. After retrieving the list of models, you can get information about a particular model by calling the models.get method."}, "patch": {"id": "bigquery.models.patch", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/models/{+modelId}", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/models/{modelsId}", "httpMethod": "PATCH", "parameters": {"projectId": {"description": "Required. Project ID of the model to patch.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the model to patch.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "modelId": {"description": "Required. Model ID of the model to patch.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId", "datasetId", "modelId"], "request": {"$ref": "Model"}, "response": {"$ref": "Model"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Patch specific fields in the specified model."}, "delete": {"id": "bigquery.models.delete", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/models/{+modelId}", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/models/{modelsId}", "httpMethod": "DELETE", "parameters": {"projectId": {"description": "Required. Project ID of the model to delete.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the model to delete.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "modelId": {"description": "Required. Model ID of the model to delete.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId", "datasetId", "modelId"], "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Deletes the model specified by modelId from the dataset."}}}, "jobs": {"methods": {"cancel": {"id": "bigquery.jobs.cancel", "path": "bigquery/v2/projects/{+projectId}/jobs/{+jobId}/cancel", "flatPath": "bigquery/v2/projects/{projectsId}/jobs/{jobsId}/cancel", "httpMethod": "POST", "parameters": {"projectId": {"description": "Required. Project ID of the job to cancel", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "jobId": {"description": "Required. Job ID of the job to cancel", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "location": {"description": "The geographic location of the job. You must specify the location to run the job for the following scenarios: - If the location to run a job is not in the `us` or the `eu` multi-regional location - If the job's location is in a single region (for example, `us-central1`) For more information, see https://cloud.google.com/bigquery/docs/locations#specifying_your_location.", "location": "query", "type": "string"}}, "parameterOrder": ["projectId", "jobId"], "response": {"$ref": "JobCancelResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Requests that a job be cancelled. This call will return immediately, and the client will need to poll for the job status to see if the cancel completed successfully. Cancelled jobs may still incur costs."}, "get": {"id": "bigquery.jobs.get", "path": "bigquery/v2/projects/{+projectId}/jobs/{+jobId}", "flatPath": "bigquery/v2/projects/{projectsId}/jobs/{jobsId}", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. Project ID of the requested job.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "jobId": {"description": "Required. Job ID of the requested job.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "location": {"description": "The geographic location of the job. You must specify the location to run the job for the following scenarios: - If the location to run a job is not in the `us` or the `eu` multi-regional location - If the job's location is in a single region (for example, `us-central1`) For more information, see https://cloud.google.com/bigquery/docs/locations#specifying_your_location.", "location": "query", "type": "string"}}, "parameterOrder": ["projectId", "jobId"], "response": {"$ref": "Job"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Returns information about a specific job. Job information is available for a six month period after creation. Requires that you're the person who ran the job, or have the Is Owner project role."}, "insert": {"id": "bigquery.jobs.insert", "path": "bigquery/v2/projects/{+projectId}/jobs", "flatPath": "bigquery/v2/projects/{projectsId}/jobs", "httpMethod": "POST", "parameters": {"projectId": {"description": "Project ID of project that will be billed for the job.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId"], "supportsMediaUpload": true, "mediaUpload": {"accept": ["*/*"], "protocols": {"simple": {"multipart": true, "path": "/upload/bigquery/v2/projects/{+projectId}/jobs"}}}, "request": {"$ref": "Job"}, "response": {"$ref": "Job"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"], "description": "Starts a new asynchronous job. This API has two different kinds of endpoint URIs, as this method supports a variety of use cases. * The *Metadata* URI is used for most interactions, as it accepts the job configuration directly. * The *Upload* URI is ONLY for the case when you're sending both a load job configuration and a data stream together. In this case, the Upload URI accepts the job configuration and the data as two distinct multipart MIME parts."}, "delete": {"id": "bigquery.jobs.delete", "path": "bigquery/v2/projects/{+projectId}/jobs/{+jobId}/delete", "flatPath": "bigquery/v2/projects/{projectsId}/jobs/{jobsId}/delete", "httpMethod": "DELETE", "parameters": {"projectId": {"description": "Required. Project ID of the job for which metadata is to be deleted.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "jobId": {"description": "Required. Job ID of the job for which metadata is to be deleted. If this is a parent job which has child jobs, the metadata from all child jobs will be deleted as well. Direct deletion of the metadata of child jobs is not allowed.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "location": {"description": "The geographic location of the job. Required. See details at: https://cloud.google.com/bigquery/docs/locations#specifying_your_location.", "location": "query", "type": "string"}}, "parameterOrder": ["projectId", "jobId"], "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Requests the deletion of the metadata of a job. This call returns when the job's metadata is deleted."}, "list": {"id": "bigquery.jobs.list", "path": "bigquery/v2/projects/{+projectId}/jobs", "flatPath": "bigquery/v2/projects/{projectsId}/jobs", "httpMethod": "GET", "parameters": {"projectId": {"description": "Project ID of the jobs to list.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "allUsers": {"description": "Whether to display jobs owned by all users in the project. Default False.", "location": "query", "type": "boolean"}, "maxResults": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "location": "query", "type": "integer", "format": "int32"}, "minCreationTime": {"description": "Min value for job creation time, in milliseconds since the POSIX epoch. If set, only jobs created after or at this timestamp are returned.", "location": "query", "type": "string", "format": "uint64"}, "maxCreationTime": {"description": "Max value for job creation time, in milliseconds since the POSIX epoch. If set, only jobs created before or at this timestamp are returned.", "location": "query", "type": "string", "format": "uint64"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results.", "location": "query", "type": "string"}, "projection": {"description": "Restrict information returned to a set of selected fields", "location": "query", "type": "string", "enumDescriptions": ["", ""], "enum": ["MINIMAL", "FULL"]}, "stateFilter": {"description": "Filter for job state", "location": "query", "repeated": true, "type": "string", "enumDescriptions": ["", "", ""], "enum": ["DONE", "PENDING", "RUNNING"]}, "parentJobId": {"description": "If set, show only child jobs of the specified parent. Otherwise, show all top-level jobs.", "location": "query", "type": "string"}}, "parameterOrder": ["projectId"], "response": {"$ref": "JobList"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Lists all jobs that you started in the specified project. Job information is available for a six month period after creation. The job list is sorted in reverse chronological order, by job creation time. Requires the Can View project role, or the Is Owner project role if you set the allUsers property."}, "getQueryResults": {"id": "bigquery.jobs.getQueryResults", "path": "bigquery/v2/projects/{+projectId}/queries/{+jobId}", "flatPath": "bigquery/v2/projects/{projectsId}/queries/{queriesId}", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. Project ID of the query job.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "jobId": {"description": "Required. Job ID of the query job.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "startIndex": {"description": "Zero-based index of the starting row.", "location": "query", "type": "string", "format": "uint64"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results.", "location": "query", "type": "string"}, "maxResults": {"description": "Maximum number of results to read.", "location": "query", "type": "integer", "format": "uint32"}, "timeoutMs": {"description": "Optional: Specifies the maximum amount of time, in milliseconds, that the client is willing to wait for the query to complete. By default, this limit is 10 seconds (10,000 milliseconds). If the query is complete, the jobComplete field in the response is true. If the query has not yet completed, jobComplete is false. You can request a longer timeout period in the timeoutMs field. However, the call is not guaranteed to wait for the specified timeout; it typically returns after around 200 seconds (200,000 milliseconds), even if the query is not complete. If jobComplete is false, you can continue to wait for the query to complete by calling the getQueryResults method until the jobComplete field in the getQueryResults response is true.", "location": "query", "type": "integer", "format": "uint32"}, "location": {"description": "The geographic location of the job. You must specify the location to run the job for the following scenarios: - If the location to run a job is not in the `us` or the `eu` multi-regional location - If the job's location is in a single region (for example, `us-central1`) For more information, see https://cloud.google.com/bigquery/docs/locations#specifying_your_location.", "location": "query", "type": "string"}, "formatOptions.useInt64Timestamp": {"description": "Optional. Output timestamp as usec int64. Default is false.", "location": "query", "type": "boolean"}}, "parameterOrder": ["projectId", "jobId"], "response": {"$ref": "GetQueryResultsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "RPC to get the results of a query job."}, "query": {"id": "bigquery.jobs.query", "path": "bigquery/v2/projects/{+projectId}/queries", "flatPath": "bigquery/v2/projects/{projectsId}/queries", "httpMethod": "POST", "parameters": {"projectId": {"description": "Required. Project ID of the query request.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId"], "request": {"$ref": "QueryRequest"}, "response": {"$ref": "QueryResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Runs a BigQuery SQL query synchronously and returns query results if the query completes within a specified timeout."}}}, "projects": {"methods": {"list": {"id": "bigquery.projects.list", "path": "bigquery/v2/projects", "flatPath": "bigquery/v2/projects", "httpMethod": "GET", "parameters": {"maxResults": {"description": "`maxResults` unset returns all results, up to 50 per page. Additionally, the number of projects in a page may be fewer than `maxResults` because projects are retrieved and then filtered to only projects with the BigQuery API enabled.", "location": "query", "type": "integer", "format": "uint32"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results. If not present, no further pages are present.", "location": "query", "type": "string"}}, "parameterOrder": [], "response": {"$ref": "ProjectList"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "RPC to list projects to which the user has been granted any project role. Users of this method are encouraged to consider the [Resource Manager](https://cloud.google.com/resource-manager/docs/) API, which provides the underlying data for this method and has more capabilities."}, "getServiceAccount": {"id": "bigquery.projects.getServiceAccount", "path": "bigquery/v2/projects/{+projectId}/serviceAccount", "flatPath": "bigquery/v2/projects/{projectsId}/serviceAccount", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. ID of the project.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId"], "response": {"$ref": "GetServiceAccountResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "RPC to get the service account for a project used for interactions with Google Cloud KMS"}}}, "routines": {"methods": {"get": {"id": "bigquery.routines.get", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/routines/{+routineId}", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. Project ID of the requested routine", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the requested routine", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "routineId": {"description": "Required. Routine ID of the requested routine", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "readMask": {"description": "If set, only the Routine fields in the field mask are returned in the response. If unset, all Routine fields are returned.", "location": "query", "type": "string", "format": "google-fieldmask"}}, "parameterOrder": ["projectId", "datasetId", "routineId"], "response": {"$ref": "Routine"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Gets the specified routine resource by routine ID."}, "insert": {"id": "bigquery.routines.insert", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/routines", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/routines", "httpMethod": "POST", "parameters": {"projectId": {"description": "Required. Project ID of the new routine", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the new routine", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId", "datasetId"], "request": {"$ref": "Routine"}, "response": {"$ref": "Routine"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Creates a new routine in the dataset."}, "update": {"id": "bigquery.routines.update", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/routines/{+routineId}", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}", "httpMethod": "PUT", "parameters": {"projectId": {"description": "Required. Project ID of the routine to update", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the routine to update", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "routineId": {"description": "Required. Routine ID of the routine to update", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId", "datasetId", "routineId"], "request": {"$ref": "Routine"}, "response": {"$ref": "Routine"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Updates information in an existing routine. The update method replaces the entire Routine resource."}, "delete": {"id": "bigquery.routines.delete", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/routines/{+routineId}", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}", "httpMethod": "DELETE", "parameters": {"projectId": {"description": "Required. Project ID of the routine to delete", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the routine to delete", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "routineId": {"description": "Required. Routine ID of the routine to delete", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId", "datasetId", "routineId"], "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Deletes the routine specified by routineId from the dataset."}, "list": {"id": "bigquery.routines.list", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/routines", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/routines", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. Project ID of the routines to list", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the routines to list", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "location": "query", "type": "integer", "format": "uint32"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results", "location": "query", "type": "string"}, "readMask": {"description": "If set, then only the Routine fields in the field mask, as well as project_id, dataset_id and routine_id, are returned in the response. If unset, then the following Routine fields are returned: etag, project_id, dataset_id, routine_id, routine_type, creation_time, last_modified_time, and language.", "location": "query", "type": "string", "format": "google-fieldmask"}, "filter": {"description": "If set, then only the Routines matching this filter are returned. The supported format is `routineType:{RoutineType}`, where `{RoutineType}` is a RoutineType enum. For example: `routineType:SCALAR_FUNCTION`.", "location": "query", "type": "string"}}, "parameterOrder": ["projectId", "datasetId"], "response": {"$ref": "ListRoutinesResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Lists all routines in the specified dataset. Requires the READER dataset role."}}}, "rowAccessPolicies": {"methods": {"list": {"id": "bigquery.rowAccessPolicies.list", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/rowAccessPolicies", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. Project ID of the row access policies to list.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of row access policies to list.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "tableId": {"description": "Required. Table ID of the table to list row access policies.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "location": "query", "type": "integer", "format": "int32"}}, "parameterOrder": ["projectId", "datasetId", "tableId"], "response": {"$ref": "ListRowAccessPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Lists all row access policies on the specified table."}, "getIamPolicy": {"id": "bigquery.rowAccessPolicies.getIamPolicy", "path": "bigquery/v2/{+resource}:getIamPolicy", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies/{rowAccessPoliciesId}:getIamPolicy", "httpMethod": "POST", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+/rowAccessPolicies/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["resource"], "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set."}, "testIamPermissions": {"id": "bigquery.rowAccessPolicies.testIamPermissions", "path": "bigquery/v2/{+resource}:testIamPermissions", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies/{rowAccessPoliciesId}:testIamPermissions", "httpMethod": "POST", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+/rowAccessPolicies/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["resource"], "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning."}}}, "tables": {"methods": {"get": {"id": "bigquery.tables.get", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. Project ID of the requested table", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the requested table", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "tableId": {"description": "Required. Table ID of the requested table", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "selectedFields": {"description": "List of table schema fields to return (comma-separated). If unspecified, all fields are returned. A fieldMask cannot be used here because the fields will automatically be converted from camelCase to snake_case and the conversion will fail if there are underscores. Since these are fields in BigQuery table schemas, underscores are allowed.", "location": "query", "type": "string"}, "view": {"description": "Optional. Specifies the view that determines which table information is returned. By default, basic table information and storage statistics (STORAGE_STATS) are returned.", "location": "query", "type": "string", "enumDescriptions": ["The default value. Default to the STORAGE_STATS view.", "Includes basic table information including schema and partitioning specification. This view does not include storage statistics such as numRows or numBytes. This view is significantly more efficient and should be used to support high query rates.", "Includes all information in the BASIC view as well as storage statistics (numBytes, numLongTermBytes, numRows and lastModifiedTime).", "Includes all table information, including storage statistics. It returns same information as STORAGE_STATS view, but may contain additional information in the future."], "enum": ["TABLE_METADATA_VIEW_UNSPECIFIED", "BASIC", "STORAGE_STATS", "FULL"]}}, "parameterOrder": ["projectId", "datasetId", "tableId"], "response": {"$ref": "Table"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Gets the specified table resource by table ID. This method does not return the data in the table, it only returns the table resource, which describes the structure of this table."}, "insert": {"id": "bigquery.tables.insert", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/tables", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/tables", "httpMethod": "POST", "parameters": {"projectId": {"description": "Required. Project ID of the new table", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the new table", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId", "datasetId"], "request": {"$ref": "Table"}, "response": {"$ref": "Table"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Creates a new, empty table in the dataset."}, "patch": {"id": "bigquery.tables.patch", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}", "httpMethod": "PATCH", "parameters": {"projectId": {"description": "Required. Project ID of the table to update", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the table to update", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "tableId": {"description": "Required. Table ID of the table to update", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "autodetectSchema": {"description": "Optional. When true will autodetect schema, else will keep original schema.", "location": "query", "type": "boolean"}}, "parameterOrder": ["projectId", "datasetId", "tableId"], "request": {"$ref": "Table"}, "response": {"$ref": "Table"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Updates information in an existing table. The update method replaces the entire table resource, whereas the patch method only replaces fields that are provided in the submitted table resource. This method supports RFC5789 patch semantics."}, "update": {"id": "bigquery.tables.update", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}", "httpMethod": "PUT", "parameters": {"projectId": {"description": "Required. Project ID of the table to update", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the table to update", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "tableId": {"description": "Required. Table ID of the table to update", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "autodetectSchema": {"description": "Optional. When true will autodetect schema, else will keep original schema.", "location": "query", "type": "boolean"}}, "parameterOrder": ["projectId", "datasetId", "tableId"], "request": {"$ref": "Table"}, "response": {"$ref": "Table"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Updates information in an existing table. The update method replaces the entire Table resource, whereas the patch method only replaces fields that are provided in the submitted Table resource."}, "delete": {"id": "bigquery.tables.delete", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}", "httpMethod": "DELETE", "parameters": {"projectId": {"description": "Required. Project ID of the table to delete", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the table to delete", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "tableId": {"description": "Required. Table ID of the table to delete", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId", "datasetId", "tableId"], "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Deletes the table specified by tableId from the dataset. If the table contains data, all the data will be deleted."}, "list": {"id": "bigquery.tables.list", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/tables", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/tables", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. Project ID of the tables to list", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the tables to list", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "location": "query", "type": "integer", "format": "uint32"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results", "location": "query", "type": "string"}}, "parameterOrder": ["projectId", "datasetId"], "response": {"$ref": "TableList"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Lists all tables in the specified dataset. Requires the READER dataset role."}, "setIamPolicy": {"id": "bigquery.tables.setIamPolicy", "path": "bigquery/v2/{+resource}:setIamPolicy", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}:setIamPolicy", "httpMethod": "POST", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["resource"], "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors."}, "getIamPolicy": {"id": "bigquery.tables.getIamPolicy", "path": "bigquery/v2/{+resource}:getIamPolicy", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}:getIamPolicy", "httpMethod": "POST", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["resource"], "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set."}, "testIamPermissions": {"id": "bigquery.tables.testIamPermissions", "path": "bigquery/v2/{+resource}:testIamPermissions", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}:testIamPermissions", "httpMethod": "POST", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["resource"], "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning."}}}, "tabledata": {"methods": {"insertAll": {"id": "bigquery.tabledata.insertAll", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/insertAll", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/insertAll", "httpMethod": "POST", "parameters": {"projectId": {"pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "tableId": {"pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId", "datasetId", "tableId"], "request": {"$ref": "TableDataInsertAllRequest"}, "response": {"$ref": "TableDataInsertAllResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.insertdata", "https://www.googleapis.com/auth/cloud-platform"], "description": "Streams data into BigQuery one record at a time without needing to run a load job."}, "list": {"id": "bigquery.tabledata.list", "path": "bigquery/v2/projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/data", "flatPath": "bigquery/v2/projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/data", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. Project id of the table to list.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset id of the table to list.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "tableId": {"description": "Required. Table id of the table to list.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "startIndex": {"description": "Start row index of the table.", "location": "query", "type": "string", "format": "uint64"}, "maxResults": {"description": "Row limit of the table.", "location": "query", "type": "integer", "format": "uint32"}, "pageToken": {"description": "To retrieve the next page of table data, set this field to the string provided in the pageToken field of the response body from your previous call to tabledata.list.", "location": "query", "type": "string"}, "selectedFields": {"description": "Subset of fields to return, supports select into sub fields. Example: selected_fields = \"a,e.d.f\";", "location": "query", "type": "string"}, "formatOptions.useInt64Timestamp": {"description": "Optional. Output timestamp as usec int64. Default is false.", "location": "query", "type": "boolean"}}, "parameterOrder": ["projectId", "datasetId", "tableId"], "response": {"$ref": "TableDataList"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "List the content of a table in rows."}}}}, "basePath": ""}
{"kind": "discovery#restDescription", "discoveryVersion": "v1", "id": "bigquery:v2", "name": "big<PERSON>y", "version": "v2", "revision": "20230815", "title": "BigQuery API", "description": "A data platform for customers to create, manage, share and query data.", "ownerDomain": "google.com", "ownerName": "Google", "icons": {"x16": "https://www.google.com/images/icons/product/search-16.gif", "x32": "https://www.google.com/images/icons/product/search-32.gif"}, "documentationLink": "https://cloud.google.com/bigquery/", "protocol": "rest", "baseUrl": "https://bigquery.googleapis.com/bigquery/v2/", "basePath": "/bigquery/v2/", "rootUrl": "https://bigquery.googleapis.com/", "servicePath": "bigquery/v2/", "batchPath": "batch/bigquery/v2", "parameters": {"alt": {"type": "string", "description": "Data format for the response.", "default": "json", "enum": ["json"], "enumDescriptions": ["Responses with Content-Type of application/json"], "location": "query"}, "fields": {"type": "string", "description": "Selector specifying which fields to include in a partial response.", "location": "query"}, "key": {"type": "string", "description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query"}, "oauth_token": {"type": "string", "description": "OAuth 2.0 token for the current user.", "location": "query"}, "prettyPrint": {"type": "boolean", "description": "Returns response with indentations and line breaks.", "default": "true", "location": "query"}, "quotaUser": {"type": "string", "description": "An opaque string that represents a user for quota purposes. Must not exceed 40 characters.", "location": "query"}, "userIp": {"type": "string", "description": "Deprecated. Please use quotaUser instead.", "location": "query"}}, "auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/bigquery": {"description": "View and manage your data in Google BigQuery and see the email address for your Google Account"}, "https://www.googleapis.com/auth/bigquery.insertdata": {"description": "Insert data into Google BigQuery"}, "https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}, "https://www.googleapis.com/auth/cloud-platform.read-only": {"description": "View your data across Google Cloud services and see the email address of your Google Account"}, "https://www.googleapis.com/auth/devstorage.full_control": {"description": "Manage your data and permissions in Cloud Storage and see the email address for your Google Account"}, "https://www.googleapis.com/auth/devstorage.read_only": {"description": "View your data in Google Cloud Storage"}, "https://www.googleapis.com/auth/devstorage.read_write": {"description": "Manage your data in Cloud Storage and see the email address of your Google Account"}}}}, "schemas": {"AvroOptions": {"id": "AvroOptions", "type": "object", "properties": {"useAvroLogicalTypes": {"type": "boolean", "description": "[Optional] If sourceFormat is set to \"AVRO\", indicates whether to interpret logical types as the corresponding BigQuery data type (for example, TIMESTAMP), instead of using the raw type (for example, INTEGER)."}}}, "BiEngineReason": {"id": "BiEngineReason", "type": "object", "properties": {"code": {"type": "string", "description": "[Output-only] High-level BI Engine reason for partial or disabled acceleration.", "default": ""}, "message": {"type": "string", "description": "[Output-only] Free form human-readable reason for partial or disabled acceleration.", "default": ""}}}, "BiEngineStatistics": {"id": "BiEngineStatistics", "type": "object", "properties": {"accelerationMode": {"type": "string", "description": "[Output-only] Specifies which mode of BI Engine acceleration was performed (if any).", "default": ""}, "biEngineMode": {"type": "string", "description": "[Output-only] Specifies which mode of BI Engine acceleration was performed (if any).", "default": ""}, "biEngineReasons": {"type": "array", "description": "In case of DISABLED or PARTIAL bi_engine_mode, these contain the explanatory reasons as to why BI Engine could not accelerate. In case the full query was accelerated, this field is not populated.", "items": {"$ref": "BiEngineReason"}}}}, "BigLakeConfiguration": {"id": "BigLakeConfiguration", "type": "object", "properties": {"connectionId": {"type": "string", "description": "[Required] Required and immutable. Credential reference for accessing external storage system. Normalized as project_id.location_id.connection_id."}, "fileFormat": {"type": "string", "description": "[Required] Required and immutable. Open source file format that the table data is stored in. Currently only PARQUET is supported."}, "storageUri": {"type": "string", "description": "[Required] Required and immutable. Fully qualified location prefix of the external folder where data is stored. Normalized to standard format: \"gs:////\". Starts with \"gs://\" rather than \"/bigstore/\". Ends with \"/\". Does not contain \"*\". See also BigLakeStorageMetadata on how it is used."}, "tableFormat": {"type": "string", "description": "[Required] Required and immutable. Open source file format that the table data is stored in. Currently only PARQUET is supported."}}}, "BigQueryModelTraining": {"id": "BigQueryModelTraining", "type": "object", "properties": {"currentIteration": {"type": "integer", "description": "[Output-only, Beta] Index of current ML training iteration. Updated during create model query job to show job progress.", "format": "int32"}, "expectedTotalIterations": {"type": "string", "description": "[Output-only, Beta] Expected number of iterations for the create model query job specified as num_iterations in the input query. The actual total number of iterations may be less than this number due to early stop.", "format": "int64"}}}, "BigtableColumn": {"id": "BigtableColumn", "type": "object", "properties": {"encoding": {"type": "string", "description": "[Optional] The encoding of the values when the type is not STRING. Acceptable encoding values are: TEXT - indicates values are alphanumeric text strings. BINARY - indicates values are encoded using HBase Bytes.toBytes family of functions. 'encoding' can also be set at the column family level. However, the setting at this level takes precedence if 'encoding' is set at both levels."}, "fieldName": {"type": "string", "description": "[Optional] If the qualifier is not a valid BigQuery field identifier i.e. does not match [a-zA-Z][a-zA-Z0-9_]*, a valid identifier must be provided as the column field name and is used as field name in queries."}, "onlyReadLatest": {"type": "boolean", "description": "[Optional] If this is set, only the latest version of value in this column are exposed. 'onlyReadLatest' can also be set at the column family level. However, the setting at this level takes precedence if 'onlyReadLatest' is set at both levels."}, "qualifierEncoded": {"type": "string", "description": "[Required] Qualifier of the column. Columns in the parent column family that has this exact qualifier are exposed as . field. If the qualifier is valid UTF-8 string, it can be specified in the qualifier_string field. Otherwise, a base-64 encoded value must be set to qualifier_encoded. The column field name is the same as the column qualifier. However, if the qualifier is not a valid BigQuery field identifier i.e. does not match [a-zA-Z][a-zA-Z0-9_]*, a valid identifier must be provided as field_name.", "format": "byte"}, "qualifierString": {"type": "string"}, "type": {"type": "string", "description": "[Optional] The type to convert the value in cells of this column. The values are expected to be encoded using HBase Bytes.toBytes function when using the BINARY encoding value. Following BigQuery types are allowed (case-sensitive) - BYTES STRING INTEGER FLOAT BOOLEAN Default type is BYTES. 'type' can also be set at the column family level. However, the setting at this level takes precedence if 'type' is set at both levels."}}}, "BigtableColumnFamily": {"id": "BigtableColumnFamily", "type": "object", "properties": {"columns": {"type": "array", "description": "[Optional] Lists of columns that should be exposed as individual fields as opposed to a list of (column name, value) pairs. All columns whose qualifier matches a qualifier in this list can be accessed as .. Other columns can be accessed as a list through .Column field.", "items": {"$ref": "BigtableColumn"}}, "encoding": {"type": "string", "description": "[Optional] The encoding of the values when the type is not STRING. Acceptable encoding values are: TEXT - indicates values are alphanumeric text strings. BINARY - indicates values are encoded using HBase Bytes.toBytes family of functions. This can be overridden for a specific column by listing that column in 'columns' and specifying an encoding for it."}, "familyId": {"type": "string", "description": "Identifier of the column family."}, "onlyReadLatest": {"type": "boolean", "description": "[Optional] If this is set only the latest version of value are exposed for all columns in this column family. This can be overridden for a specific column by listing that column in 'columns' and specifying a different setting for that column."}, "type": {"type": "string", "description": "[Optional] The type to convert the value in cells of this column family. The values are expected to be encoded using HBase Bytes.toBytes function when using the BINARY encoding value. Following BigQuery types are allowed (case-sensitive) - BYTES STRING INTEGER FLOAT BOOLEAN Default type is BYTES. This can be overridden for a specific column by listing that column in 'columns' and specifying a type for it."}}}, "BigtableOptions": {"id": "BigtableOptions", "type": "object", "properties": {"columnFamilies": {"type": "array", "description": "[Optional] List of column families to expose in the table schema along with their types. This list restricts the column families that can be referenced in queries and specifies their value types. You can use this list to do type conversions - see the 'type' field for more details. If you leave this list empty, all column families are present in the table schema and their values are read as BYTES. During a query only the column families referenced in that query are read from Bigtable.", "items": {"$ref": "BigtableColumnFamily"}}, "ignoreUnspecifiedColumnFamilies": {"type": "boolean", "description": "[Optional] If field is true, then the column families that are not specified in columnFamilies list are not exposed in the table schema. Otherwise, they are read with BYTES type values. The default value is false."}, "readRowkeyAsString": {"type": "boolean", "description": "[Optional] If field is true, then the rowkey column families will be read and converted to string. Otherwise they are read with BYTES type values and users need to manually cast them with CAST if necessary. The default value is false."}}}, "BqmlIterationResult": {"id": "BqmlIterationResult", "type": "object", "properties": {"durationMs": {"type": "string", "description": "[Output-only, Beta] Time taken to run the training iteration in milliseconds.", "format": "int64"}, "evalLoss": {"type": "number", "description": "[Output-only, Beta] Eval loss computed on the eval data at the end of the iteration. The eval loss is used for early stopping to avoid overfitting. No eval loss if eval_split_method option is specified as no_split or auto_split with input data size less than 500 rows.", "format": "double"}, "index": {"type": "integer", "description": "[Output-only, Beta] Index of the ML training iteration, starting from zero for each training run.", "format": "int32"}, "learnRate": {"type": "number", "description": "[Output-only, Beta] Learning rate used for this iteration, it varies for different training iterations if learn_rate_strategy option is not constant.", "format": "double"}, "trainingLoss": {"type": "number", "description": "[Output-only, Beta] Training loss computed on the training data at the end of the iteration. The training loss function is defined by model type.", "format": "double"}}}, "BqmlTrainingRun": {"id": "BqmlTrainingRun", "type": "object", "properties": {"iterationResults": {"type": "array", "description": "[Output-only, Beta] List of each iteration results.", "items": {"$ref": "BqmlIterationResult"}}, "startTime": {"type": "string", "description": "[Output-only, Beta] Training run start time in milliseconds since the epoch.", "format": "date-time"}, "state": {"type": "string", "description": "[Output-only, Beta] Different state applicable for a training run. IN PROGRESS: Training run is in progress. FAILED: Training run ended due to a non-retryable failure. SUCCEEDED: Training run successfully completed. CANCELLED: Training run cancelled by the user."}, "trainingOptions": {"type": "object", "description": "[Output-only, Beta] Training options used by this training run. These options are mutable for subsequent training runs. Default values are explicitly stored for options not specified in the input query of the first training run. For subsequent training runs, any option not explicitly specified in the input query will be copied from the previous training run.", "properties": {"earlyStop": {"type": "boolean"}, "l1Reg": {"type": "number", "format": "double"}, "l2Reg": {"type": "number", "format": "double"}, "learnRate": {"type": "number", "format": "double"}, "learnRateStrategy": {"type": "string"}, "lineSearchInitLearnRate": {"type": "number", "format": "double"}, "maxIteration": {"type": "string", "format": "int64"}, "minRelProgress": {"type": "number", "format": "double"}, "warmStart": {"type": "boolean"}}}}}, "CloneDefinition": {"id": "CloneDefinition", "type": "object", "properties": {"baseTableReference": {"$ref": "TableReference", "description": "[Required] Reference describing the ID of the table that was cloned."}, "cloneTime": {"type": "string", "description": "[Required] The time at which the base table was cloned. This value is reported in the JSON response using RFC3339 format.", "format": "date-time"}}}, "Clustering": {"id": "Clustering", "type": "object", "properties": {"fields": {"type": "array", "description": "[Repeated] One or more fields on which data should be clustered. Only top-level, non-repeated, simple-type fields are supported. When you cluster a table using multiple columns, the order of columns you specify is important. The order of the specified columns determines the sort order of the data.", "items": {"type": "string"}}}}, "ConnectionProperty": {"id": "ConnectionProperty", "type": "object", "properties": {"key": {"type": "string", "description": "[Required] Name of the connection property to set."}, "value": {"type": "string", "description": "[Required] Value of the connection property."}}}, "CsvOptions": {"id": "CsvOptions", "type": "object", "properties": {"allowJaggedRows": {"type": "boolean", "description": "[Optional] Indicates if BigQuery should accept rows that are missing trailing optional columns. If true, BigQuery treats missing trailing columns as null values. If false, records with missing trailing columns are treated as bad records, and if there are too many bad records, an invalid error is returned in the job result. The default value is false."}, "allowQuotedNewlines": {"type": "boolean", "description": "[Optional] Indicates if BigQuery should allow quoted data sections that contain newline characters in a CSV file. The default value is false."}, "encoding": {"type": "string", "description": "[Optional] The character encoding of the data. The supported values are UTF-8 or ISO-8859-1. The default value is UTF-8. BigQuery decodes the data after the raw, binary data has been split using the values of the quote and fieldDelimiter properties."}, "fieldDelimiter": {"type": "string", "description": "[Optional] The separator for fields in a CSV file. BigQuery converts the string to ISO-8859-1 encoding, and then uses the first byte of the encoded string to split the data in its raw, binary state. BigQuery also supports the escape sequence \"\\t\" to specify a tab separator. The default value is a comma (',')."}, "null_marker": {"type": "string", "description": "[Optional] An custom string that will represent a NULL value in CSV import data."}, "preserveAsciiControlCharacters": {"type": "boolean", "description": "[Optional] Preserves the embedded ASCII control characters (the first 32 characters in the ASCII-table, from '\\x00' to '\\x1F') when loading from CSV. Only applicable to CSV, ignored for other formats."}, "quote": {"type": "string", "description": "[Optional] The value that is used to quote data sections in a CSV file. BigQuery converts the string to ISO-8859-1 encoding, and then uses the first byte of the encoded string to split the data in its raw, binary state. The default value is a double-quote ('\"'). If your data does not contain quoted sections, set the property value to an empty string. If your data contains quoted newline characters, you must also set the allowQuotedNewlines property to true.", "default": "\"", "pattern": ".?"}, "skipLeadingRows": {"type": "string", "description": "[Optional] The number of rows at the top of a CSV file that BigQuery will skip when reading the data. The default value is 0. This property is useful if you have header rows in the file that should be skipped. When autodetect is on, the behavior is the following: * skipLeadingRows unspecified - Autodetect tries to detect headers in the first row. If they are not detected, the row is read as data. Otherwise data is read starting from the second row. * skipLeadingRows is 0 - Instructs autodetect that there are no headers and data should be read starting from the first row. * skipLeadingRows = N > 0 - Autodetect skips N-1 rows and tries to detect headers in row N. If headers are not detected, row N is just skipped. Otherwise row N is used to extract column names for the detected schema.", "format": "int64"}}}, "DataMaskingStatistics": {"id": "DataMaskingStatistics", "type": "object", "properties": {"dataMaskingApplied": {"type": "boolean", "description": "[Output-only] [Preview] Whether any accessed data was protected by data masking. The actual evaluation is done by accessStats.masked_field_count > 0. Since this is only used for the discovery_doc generation purpose, as long as the type (boolean) matches, client library can leverage this. The actual evaluation of the variable is done else-where.", "default": "false"}}}, "Dataset": {"id": "Dataset", "type": "object", "properties": {"access": {"type": "array", "description": "[Optional] An array of objects that define dataset access for one or more entities. You can set this property when inserting or updating a dataset in order to control who is allowed to access the data. If unspecified at dataset creation time, BigQuery adds default dataset access for the following entities: access.specialGroup: projectReaders; access.role: READER; access.specialGroup: projectWriters; access.role: WRITER; access.specialGroup: projectOwners; access.role: OWNER; access.userByEmail: [dataset creator email]; access.role: OWNER;", "items": {"type": "object", "properties": {"dataset": {"$ref": "DatasetAccessEntry", "description": "[Pick one] A grant authorizing all resources of a particular type in a particular dataset access to this dataset. Only views are supported for now. The role field is not required when this field is set. If that dataset is deleted and re-created, its access needs to be granted again via an update operation."}, "domain": {"type": "string", "description": "[Pick one] A domain to grant access to. Any users signed in with the domain specified will be granted the specified access. Example: \"example.com\". Maps to IAM policy member \"domain:DOMAIN\"."}, "groupByEmail": {"type": "string", "description": "[Pick one] An email address of a Google Group to grant access to. Maps to IAM policy member \"group:GROUP\"."}, "iamMember": {"type": "string", "description": "[Pick one] Some other type of member that appears in the IAM Policy but isn't a user, group, domain, or special group."}, "role": {"type": "string", "description": "[Required] An IAM role ID that should be granted to the user, group, or domain specified in this access entry. The following legacy mappings will be applied: OWNER  roles/bigquery.dataOwner WRITER  roles/bigquery.dataEditor READER  roles/bigquery.dataViewer This field will accept any of the above formats, but will return only the legacy format. For example, if you set this field to \"roles/bigquery.dataOwner\", it will be returned back as \"OWNER\"."}, "routine": {"$ref": "RoutineReference", "description": "[Pick one] A routine from a different dataset to grant access to. Queries executed against that routine will have read access to views/tables/routines in this dataset. Only UDF is supported for now. The role field is not required when this field is set. If that routine is updated by any user, access to the routine needs to be granted again via an update operation."}, "specialGroup": {"type": "string", "description": "[Pick one] A special group to grant access to. Possible values include: projectOwners: Owners of the enclosing project. projectReaders: Readers of the enclosing project. projectWriters: Writers of the enclosing project. allAuthenticatedUsers: All authenticated BigQuery users. Maps to similarly-named IAM members."}, "userByEmail": {"type": "string", "description": "[Pick one] An email address of a user to grant access to. For example: <EMAIL>. Maps to IAM policy member \"user:EMAIL\" or \"serviceAccount:EMAIL\"."}, "view": {"$ref": "TableReference", "description": "[Pick one] A view from a different dataset to grant access to. Queries executed against that view will have read access to tables in this dataset. The role field is not required when this field is set. If that view is updated by any user, access to the view needs to be granted again via an update operation."}}}}, "creationTime": {"type": "string", "description": "[Output-only] The time when this dataset was created, in milliseconds since the epoch.", "format": "int64"}, "datasetReference": {"$ref": "DatasetReference", "description": "[Required] A reference that identifies the dataset."}, "defaultCollation": {"type": "string", "description": "[Output-only] The default collation of the dataset."}, "defaultEncryptionConfiguration": {"$ref": "EncryptionConfiguration"}, "defaultPartitionExpirationMs": {"type": "string", "description": "[Optional] The default partition expiration for all partitioned tables in the dataset, in milliseconds. Once this property is set, all newly-created partitioned tables in the dataset will have an expirationMs property in the timePartitioning settings set to this value, and changing the value will only affect new tables, not existing ones. The storage in a partition will have an expiration time of its partition time plus this value. Setting this property overrides the use of defaultTableExpirationMs for partitioned tables: only one of defaultTableExpirationMs and defaultPartitionExpirationMs will be used for any new partitioned table. If you provide an explicit timePartitioning.expirationMs when creating or updating a partitioned table, that value takes precedence over the default partition expiration time indicated by this property.", "format": "int64"}, "defaultRoundingMode": {"type": "string", "description": "[Output-only] The default rounding mode of the dataset."}, "defaultTableExpirationMs": {"type": "string", "description": "[Optional] The default lifetime of all tables in the dataset, in milliseconds. The minimum value is 3600000 milliseconds (one hour). Once this property is set, all newly-created tables in the dataset will have an expirationTime property set to the creation time plus the value in this property, and changing the value will only affect new tables, not existing ones. When the expirationTime for a given table is reached, that table will be deleted automatically. If a table's expirationTime is modified or removed before the table expires, or if you provide an explicit expirationTime when creating a table, that value takes precedence over the default expiration time indicated by this property.", "format": "int64"}, "description": {"type": "string", "description": "[Optional] A user-friendly description of the dataset."}, "etag": {"type": "string", "description": "[Output-only] A hash of the resource."}, "externalDatasetReference": {"$ref": "ExternalDatasetReference", "description": "[Optional] Information about the external metadata storage where the dataset is defined. Filled out when the dataset type is EXTERNAL."}, "friendlyName": {"type": "string", "description": "[Optional] A descriptive name for the dataset."}, "id": {"type": "string", "description": "[Output-only] The fully-qualified unique name of the dataset in the format projectId:datasetId. The dataset name without the project name is given in the datasetId field. When creating a new dataset, leave this field blank, and instead specify the datasetId field."}, "isCaseInsensitive": {"type": "boolean", "description": "[Optional] Indicates if table names are case insensitive in the dataset."}, "kind": {"type": "string", "description": "[Output-only] The resource type.", "default": "bigquery#dataset"}, "labels": {"type": "object", "description": "The labels associated with this dataset. You can use these to organize and group your datasets. You can set this property when inserting or updating a dataset. See Creating and Updating Dataset Labels for more information.", "additionalProperties": {"type": "string"}}, "lastModifiedTime": {"type": "string", "description": "[Output-only] The date when this dataset or any of its tables was last modified, in milliseconds since the epoch.", "format": "int64"}, "location": {"type": "string", "description": "The geographic location where the dataset should reside. The default value is US. See details at https://cloud.google.com/bigquery/docs/locations."}, "maxTimeTravelHours": {"type": "string", "description": "[Optional] Number of hours for the max time travel for all tables in the dataset.", "format": "int64"}, "satisfiesPzs": {"type": "boolean", "description": "[Output-only] Reserved for future use."}, "selfLink": {"type": "string", "description": "[Output-only] A URL that can be used to access the resource again. You can use this URL in Get or Update requests to the resource."}, "storageBillingModel": {"type": "string", "description": "[Optional] Storage billing model to be used for all tables in the dataset. Can be set to PHYSICAL. Default is LOGICAL."}, "tags": {"type": "array", "description": "[Optional]The tags associated with this dataset. Tag keys are globally unique.", "items": {"type": "object", "properties": {"tagKey": {"type": "string", "description": "[Required] The namespaced friendly name of the tag key, e.g. \"12345/environment\" where 12345 is org id."}, "tagValue": {"type": "string", "description": "[Required] Friendly short name of the tag value, e.g. \"production\"."}}}}}}, "DatasetAccessEntry": {"id": "DatasetAccessEntry", "type": "object", "properties": {"dataset": {"$ref": "DatasetReference", "description": "[Required] The dataset this entry applies to."}, "targetTypes": {"type": "array", "items": {"type": "string", "enum": ["TARGET_TYPE_UNSPECIFIED", "VIEWS", "ROUTINES"], "enumDescriptions": ["Do not use. You must set a target type explicitly.", "This entry applies to views in the dataset.", "This entry applies to routines in the dataset."]}}}}, "DatasetList": {"id": "DatasetList", "type": "object", "properties": {"datasets": {"type": "array", "description": "An array of the dataset resources in the project. Each resource contains basic information. For full information about a particular dataset resource, use the Datasets: get method. This property is omitted when there are no datasets in the project.", "items": {"type": "object", "properties": {"datasetReference": {"$ref": "DatasetReference", "description": "The dataset reference. Use this property to access specific parts of the dataset's ID, such as project ID or dataset ID."}, "friendlyName": {"type": "string", "description": "A descriptive name for the dataset, if one exists."}, "id": {"type": "string", "description": "The fully-qualified, unique, opaque ID of the dataset."}, "kind": {"type": "string", "description": "The resource type. This property always returns the value \"bigquery#dataset\".", "default": "bigquery#dataset"}, "labels": {"type": "object", "description": "The labels associated with this dataset. You can use these to organize and group your datasets.", "additionalProperties": {"type": "string"}}, "location": {"type": "string", "description": "The geographic location where the data resides."}}}}, "etag": {"type": "string", "description": "A hash value of the results page. You can use this property to determine if the page has changed since the last request."}, "kind": {"type": "string", "description": "The list type. This property always returns the value \"bigquery#datasetList\".", "default": "bigquery#datasetList"}, "nextPageToken": {"type": "string", "description": "A token that can be used to request the next results page. This property is omitted on the final results page."}}}, "DatasetReference": {"id": "DatasetReference", "type": "object", "properties": {"datasetId": {"type": "string", "description": "[Required] A unique ID for this dataset, without the project name. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 1,024 characters.", "annotations": {"required": ["bigquery.datasets.update"]}}, "projectId": {"type": "string", "description": "[Optional] The ID of the project containing this dataset.", "annotations": {"required": ["bigquery.datasets.update"]}}}}, "DestinationTableProperties": {"id": "DestinationTableProperties", "type": "object", "properties": {"description": {"type": "string", "description": "[Optional] The description for the destination table. This will only be used if the destination table is newly created. If the table already exists and a value different than the current description is provided, the job will fail."}, "expirationTime": {"type": "string", "description": "[Internal] This field is for Google internal use only.", "format": "date-time"}, "friendlyName": {"type": "string", "description": "[Optional] The friendly name for the destination table. This will only be used if the destination table is newly created. If the table already exists and a value different than the current friendly name is provided, the job will fail."}, "labels": {"type": "object", "description": "[Optional] The labels associated with this table. You can use these to organize and group your tables. This will only be used if the destination table is newly created. If the table already exists and labels are different than the current labels are provided, the job will fail.", "additionalProperties": {"type": "string"}}}}, "DmlStatistics": {"id": "DmlStatistics", "type": "object", "properties": {"deletedRowCount": {"type": "string", "description": "Number of deleted Rows. populated by DML DELETE, MERGE and TRUNCATE statements.", "format": "int64"}, "insertedRowCount": {"type": "string", "description": "Number of inserted Rows. Populated by DML INSERT and MERGE statements.", "format": "int64"}, "updatedRowCount": {"type": "string", "description": "Number of updated Rows. Populated by DML UPDATE and MERGE statements.", "format": "int64"}}}, "EncryptionConfiguration": {"id": "EncryptionConfiguration", "type": "object", "properties": {"kmsKeyName": {"type": "string", "description": "Optional. Describes the Cloud KMS encryption key that will be used to protect destination BigQuery table. The BigQuery Service Account associated with your project requires access to this encryption key."}}}, "ErrorProto": {"id": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "type": "object", "properties": {"debugInfo": {"type": "string", "description": "Debugging information. This property is internal to Google and should not be used."}, "location": {"type": "string", "description": "Specifies where the error occurred, if present."}, "message": {"type": "string", "description": "A human-readable description of the error."}, "reason": {"type": "string", "description": "A short error code that summarizes the error."}}}, "ExplainQueryStage": {"id": "ExplainQueryStage", "type": "object", "properties": {"completedParallelInputs": {"type": "string", "description": "Number of parallel input segments completed.", "format": "int64"}, "computeMsAvg": {"type": "string", "description": "Milliseconds the average shard spent on CPU-bound tasks.", "format": "int64"}, "computeMsMax": {"type": "string", "description": "Milliseconds the slowest shard spent on CPU-bound tasks.", "format": "int64"}, "computeRatioAvg": {"type": "number", "description": "Relative amount of time the average shard spent on CPU-bound tasks.", "format": "double"}, "computeRatioMax": {"type": "number", "description": "Relative amount of time the slowest shard spent on CPU-bound tasks.", "format": "double"}, "endMs": {"type": "string", "description": "Stage end time represented as milliseconds since epoch.", "format": "int64"}, "id": {"type": "string", "description": "Unique ID for stage within plan.", "format": "int64"}, "inputStages": {"type": "array", "description": "IDs for stages that are inputs to this stage.", "items": {"type": "string", "format": "int64"}}, "name": {"type": "string", "description": "Human-readable name for stage."}, "parallelInputs": {"type": "string", "description": "Number of parallel input segments to be processed.", "format": "int64"}, "readMsAvg": {"type": "string", "description": "Milliseconds the average shard spent reading input.", "format": "int64"}, "readMsMax": {"type": "string", "description": "Milliseconds the slowest shard spent reading input.", "format": "int64"}, "readRatioAvg": {"type": "number", "description": "Relative amount of time the average shard spent reading input.", "format": "double"}, "readRatioMax": {"type": "number", "description": "Relative amount of time the slowest shard spent reading input.", "format": "double"}, "recordsRead": {"type": "string", "description": "Number of records read into the stage.", "format": "int64"}, "recordsWritten": {"type": "string", "description": "Number of records written by the stage.", "format": "int64"}, "shuffleOutputBytes": {"type": "string", "description": "Total number of bytes written to shuffle.", "format": "int64"}, "shuffleOutputBytesSpilled": {"type": "string", "description": "Total number of bytes written to shuffle and spilled to disk.", "format": "int64"}, "slotMs": {"type": "string", "description": "Slot-milliseconds used by the stage.", "format": "int64"}, "startMs": {"type": "string", "description": "Stage start time represented as milliseconds since epoch.", "format": "int64"}, "status": {"type": "string", "description": "Current status for the stage."}, "steps": {"type": "array", "description": "List of operations within the stage in dependency order (approximately chronological).", "items": {"$ref": "ExplainQueryStep"}}, "waitMsAvg": {"type": "string", "description": "Milliseconds the average shard spent waiting to be scheduled.", "format": "int64"}, "waitMsMax": {"type": "string", "description": "Milliseconds the slowest shard spent waiting to be scheduled.", "format": "int64"}, "waitRatioAvg": {"type": "number", "description": "Relative amount of time the average shard spent waiting to be scheduled.", "format": "double"}, "waitRatioMax": {"type": "number", "description": "Relative amount of time the slowest shard spent waiting to be scheduled.", "format": "double"}, "writeMsAvg": {"type": "string", "description": "Milliseconds the average shard spent on writing output.", "format": "int64"}, "writeMsMax": {"type": "string", "description": "Milliseconds the slowest shard spent on writing output.", "format": "int64"}, "writeRatioAvg": {"type": "number", "description": "Relative amount of time the average shard spent on writing output.", "format": "double"}, "writeRatioMax": {"type": "number", "description": "Relative amount of time the slowest shard spent on writing output.", "format": "double"}}}, "ExplainQueryStep": {"id": "ExplainQueryStep", "type": "object", "properties": {"kind": {"type": "string", "description": "Machine-readable operation type."}, "substeps": {"type": "array", "description": "Human-readable stage descriptions.", "items": {"type": "string"}}}}, "ExternalDataConfiguration": {"id": "ExternalDataConfiguration", "type": "object", "properties": {"autodetect": {"type": "boolean", "description": "Try to detect schema and format options automatically. Any option specified explicitly will be honored."}, "avroOptions": {"$ref": "AvroOptions", "description": "Additional properties to set if sourceFormat is set to Avro."}, "bigtableOptions": {"$ref": "BigtableOptions", "description": "[Optional] Additional options if sourceFormat is set to BIGTABLE."}, "compression": {"type": "string", "description": "[Optional] The compression type of the data source. Possible values include GZIP and NONE. The default value is NONE. This setting is ignored for Google Cloud Bigtable, Google Cloud Datastore backups and Avro formats."}, "connectionId": {"type": "string", "description": "[Optional, Trusted Tester] Connection for external data source."}, "csvOptions": {"$ref": "CsvOptions", "description": "Additional properties to set if sourceFormat is set to CSV."}, "decimalTargetTypes": {"type": "array", "description": "[Optional] Defines the list of possible SQL data types to which the source decimal values are converted. This list and the precision and the scale parameters of the decimal field determine the target type. In the order of NUMERIC, BIGNUMERIC, and STRING, a type is picked if it is in the specified list and if it supports the precision and the scale. STRING supports all precision and scale values. If none of the listed types supports the precision and the scale, the type supporting the widest range in the specified list is picked, and if a value exceeds the supported range when reading the data, an error will be thrown. Example: Suppose the value of this field is [\"NUMER<PERSON>\", \"BIGNUMERI<PERSON>\"]. If (precision,scale) is: (38,9) -> NUMERIC; (39,9) -> BIGNUMERIC (NUMERIC cannot hold 30 integer digits); (38,10) -> BIGNUMERIC (NUMERIC cannot hold 10 fractional digits); (76,38) -> BIGNUMERIC; (77,38) -> BIGNUMERIC (error if value exeeds supported range). This field cannot contain duplicate types. The order of the types in this field is ignored. For example, [\"<PERSON>IG<PERSON><PERSON><PERSON><PERSON>\", \"NUMER<PERSON>\"] is the same as [\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON>IGNUME<PERSON><PERSON>\"] and NUMERIC always takes precedence over <PERSON><PERSON>NUMERI<PERSON>. Defaults to [\"<PERSON><PERSON>ER<PERSON>\", \"STRING\"] for ORC and [\"NUMER<PERSON>\"] for the other file formats.", "items": {"type": "string"}}, "fileSetSpecType": {"type": "string", "description": "[Optional] Specifies how source URIs are interpreted for constructing the file set to load. By default source URIs are expanded against the underlying storage. Other options include specifying manifest files. Only applicable to object storage systems."}, "googleSheetsOptions": {"$ref": "GoogleSheetsOptions", "description": "[Optional] Additional options if sourceFormat is set to GOOGLE_SHEETS."}, "hivePartitioningOptions": {"$ref": "HivePartitioningOptions", "description": "[Optional] Options to configure hive partitioning support."}, "ignoreUnknownValues": {"type": "boolean", "description": "[Optional] Indicates if BigQuery should allow extra values that are not represented in the table schema. If true, the extra values are ignored. If false, records with extra columns are treated as bad records, and if there are too many bad records, an invalid error is returned in the job result. The default value is false. The sourceFormat property determines what BigQuery treats as an extra value: CSV: Trailing columns JSON: Named values that don't match any column names Google Cloud Bigtable: This setting is ignored. Google Cloud Datastore backups: This setting is ignored. Avro: This setting is ignored."}, "jsonOptions": {"$ref": "JsonOptions", "description": "Additional properties to set if `sourceFormat` is set to `NEWLINE_DELIMITED_JSON`."}, "maxBadRecords": {"type": "integer", "description": "[Optional] The maximum number of bad records that BigQuery can ignore when reading data. If the number of bad records exceeds this value, an invalid error is returned in the job result. This is only valid for CSV, JSON, and Google Sheets. The default value is 0, which requires that all records are valid. This setting is ignored for Google Cloud Bigtable, Google Cloud Datastore backups and Avro formats.", "format": "int32"}, "metadataCacheMode": {"type": "string", "description": "[Optional] Metadata Cache Mode for the table. Set this to enable caching of metadata from external data source."}, "objectMetadata": {"type": "string", "description": "ObjectMetadata is used to create Object Tables. Object Tables contain a listing of objects (with their metadata) found at the source_uris. If ObjectMetadata is set, source_format should be omitted. Currently SIMPLE is the only supported Object Metadata type."}, "parquetOptions": {"$ref": "ParquetOptions", "description": "Additional properties to set if sourceFormat is set to Parquet."}, "referenceFileSchemaUri": {"type": "string", "description": "[Optional] Provide a referencing file with the expected table schema. Enabled for the format: AVRO, PARQUET, ORC."}, "schema": {"$ref": "TableSchema", "description": "[Optional] The schema for the data. Schema is required for CSV and JSON formats. Schema is disallowed for Google Cloud Bigtable, Cloud Datastore backups, and Avro formats."}, "sourceFormat": {"type": "string", "description": "[Required] The data format. For CSV files, specify \"CSV\". For Google sheets, specify \"GOOGLE_SHEETS\". For newline-delimited JSON, specify \"NEWLINE_DELIMITED_JSON\". For Avro files, specify \"AVRO\". For Google Cloud Datastore backups, specify \"DATASTORE_BACKUP\". [Beta] For Google Cloud Bigtable, specify \"BIGTABLE\"."}, "sourceUris": {"type": "array", "description": "[Required] The fully-qualified URIs that point to your data in Google Cloud. For Google Cloud Storage URIs: Each URI can contain one '*' wildcard character and it must come after the 'bucket' name. Size limits related to load jobs apply to external data sources. For Google Cloud Bigtable URIs: Exactly one URI can be specified and it has be a fully specified and valid HTTPS URL for a Google Cloud Bigtable table. For Google Cloud Datastore backups, exactly one URI can be specified. Also, the '*' wildcard character is not allowed.", "items": {"type": "string"}}}}, "ExternalDatasetReference": {"id": "ExternalDatasetReference", "type": "object", "properties": {"connection": {"type": "string", "description": "[Required] The connection id that is used to access the external_source. Format: projects/{project_id}/locations/{location_id}/connections/{connection_id}"}, "externalSource": {"type": "string", "description": "[Required] External source that backs this dataset."}}}, "GetQueryResultsResponse": {"id": "GetQueryResultsResponse", "type": "object", "properties": {"cacheHit": {"type": "boolean", "description": "Whether the query result was fetched from the query cache."}, "errors": {"type": "array", "description": "[Output-only] The first errors or warnings encountered during the running of the job. The final message includes the number of errors that caused the process to stop. Errors here do not necessarily mean that the job has completed or was unsuccessful.", "items": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}}, "etag": {"type": "string", "description": "A hash of this response."}, "jobComplete": {"type": "boolean", "description": "Whether the query has completed or not. If rows or totalRows are present, this will always be true. If this is false, totalRows will not be available."}, "jobReference": {"$ref": "JobReference", "description": "Reference to the BigQuery Job that was created to run the query. This field will be present even if the original request timed out, in which case GetQueryResults can be used to read the results once the query has completed. Since this API only returns the first page of results, subsequent pages can be fetched via the same mechanism (GetQueryResults)."}, "kind": {"type": "string", "description": "The resource type of the response.", "default": "bigquery#getQueryResultsResponse"}, "numDmlAffectedRows": {"type": "string", "description": "[Output-only] The number of rows affected by a DML statement. Present only for DML statements INSERT, UPDATE or DELETE.", "format": "int64"}, "pageToken": {"type": "string", "description": "A token used for paging results."}, "rows": {"type": "array", "description": "An object with as many results as can be contained within the maximum permitted reply size. To get any additional rows, you can call GetQueryResults and specify the jobReference returned above. Present only when the query completes successfully.", "items": {"$ref": "TableRow"}}, "schema": {"$ref": "TableSchema", "description": "The schema of the results. Present only when the query completes successfully."}, "totalBytesProcessed": {"type": "string", "description": "The total number of bytes processed for this query.", "format": "int64"}, "totalRows": {"type": "string", "description": "The total number of rows in the complete query result set, which can be more than the number of rows in this single page of results. Present only when the query completes successfully.", "format": "uint64"}}}, "GetServiceAccountResponse": {"id": "GetServiceAccountResponse", "type": "object", "properties": {"email": {"type": "string", "description": "The service account email address."}, "kind": {"type": "string", "description": "The resource type of the response.", "default": "bigquery#getServiceAccountResponse"}}}, "GoogleSheetsOptions": {"id": "GoogleSheetsOptions", "type": "object", "properties": {"range": {"type": "string", "description": "[Optional] Range of a sheet to query from. Only used when non-empty. Typical format: sheet_name!top_left_cell_id:bottom_right_cell_id For example: sheet1!A1:B20"}, "skipLeadingRows": {"type": "string", "description": "[Optional] The number of rows at the top of a sheet that BigQuery will skip when reading the data. The default value is 0. This property is useful if you have header rows that should be skipped. When autodetect is on, behavior is the following: * skipLeadingRows unspecified - Autodetect tries to detect headers in the first row. If they are not detected, the row is read as data. Otherwise data is read starting from the second row. * skipLeadingRows is 0 - Instructs autodetect that there are no headers and data should be read starting from the first row. * skipLeadingRows = N > 0 - Autodetect skips N-1 rows and tries to detect headers in row N. If headers are not detected, row N is just skipped. Otherwise row N is used to extract column names for the detected schema.", "format": "int64"}}}, "HivePartitioningOptions": {"id": "HivePartitioningOptions", "type": "object", "properties": {"fields": {"type": "array", "description": "[Output-only] For permanent external tables, this field is populated with the hive partition keys in the order they were inferred. The types of the partition keys can be deduced by checking the table schema (which will include the partition keys). Not every API will populate this field in the output. For example, Tables.Get will populate it, but Tables.List will not contain this field.", "items": {"type": "string"}}, "mode": {"type": "string", "description": "[Optional] When set, what mode of hive partitioning to use when reading data. The following modes are supported. (1) AUTO: automatically infer partition key name(s) and type(s). (2) STRINGS: automatically infer partition key name(s). All types are interpreted as strings. (3) CUSTOM: partition key schema is encoded in the source URI prefix. Not all storage formats support hive partitioning. Requesting hive partitioning on an unsupported format will lead to an error. Currently supported types include: AVRO, CSV, JSON, ORC and Parquet."}, "requirePartitionFilter": {"type": "boolean", "description": "[Optional] If set to true, queries over this table require a partition filter that can be used for partition elimination to be specified. Note that this field should only be true when creating a permanent external table or querying a temporary external table. Hive-partitioned loads with requirePartitionFilter explicitly set to true will fail."}, "sourceUriPrefix": {"type": "string", "description": "[Optional] When hive partition detection is requested, a common prefix for all source uris should be supplied. The prefix must end immediately before the partition key encoding begins. For example, consider files following this data layout. gs://bucket/path_to_table/dt=2019-01-01/country=BR/id=7/file.avro gs://bucket/path_to_table/dt=2018-12-31/country=CA/id=3/file.avro When hive partitioning is requested with either AUTO or STRINGS detection, the common prefix can be either of gs://bucket/path_to_table or gs://bucket/path_to_table/ (trailing slash does not matter)."}}}, "IndexUnusedReason": {"id": "IndexUnusedReason", "type": "object", "properties": {"baseTable": {"$ref": "TableReference", "description": "[Output-only] Specifies the base table involved in the reason that no search index was used."}, "code": {"type": "string", "description": "[Output-only] Specifies the high-level reason for the scenario when no search index was used.", "default": "$(reason.code)"}, "indexName": {"type": "string", "description": "[Output-only] Specifies the name of the unused search index, if available.", "default": "$(reason.index_name)"}, "message": {"type": "string", "description": "[Output-only] Free form human-readable reason for the scenario when no search index was used.", "default": "$(reason.message)"}}}, "IterationResult": {"id": "IterationResult", "type": "object", "properties": {"durationMs": {"type": "string", "description": "Time taken to run the iteration in milliseconds.", "format": "int64"}, "evalLoss": {"type": "number", "description": "Loss computed on the eval data at the end of iteration.", "format": "double"}, "index": {"type": "integer", "description": "Index of the iteration, 0 based.", "format": "int32"}, "learnRate": {"type": "number", "description": "Learn rate used for this iteration.", "format": "double"}, "trainingLoss": {"type": "number", "description": "Loss computed on the training data at the end of iteration.", "format": "double"}}}, "Job": {"id": "Job", "type": "object", "properties": {"configuration": {"$ref": "JobConfiguration", "description": "[Required] Describes the job configuration."}, "etag": {"type": "string", "description": "[Output-only] A hash of this resource."}, "id": {"type": "string", "description": "[Output-only] Opaque ID field of the job"}, "jobReference": {"$ref": "JobReference", "description": "[Optional] Reference describing the unique-per-user name of the job."}, "kind": {"type": "string", "description": "[Output-only] The type of the resource.", "default": "bigquery#job"}, "selfLink": {"type": "string", "description": "[Output-only] A URL that can be used to access this resource again."}, "statistics": {"$ref": "JobStatistics", "description": "[Output-only] Information about the job, including starting time and ending time of the job."}, "status": {"$ref": "JobStatus", "description": "[Output-only] The status of this job. Examine this value when polling an asynchronous job to see if the job is complete."}, "user_email": {"type": "string", "description": "[Output-only] Email address of the user who ran the job."}}}, "JobCancelResponse": {"id": "JobCancelResponse", "type": "object", "properties": {"job": {"$ref": "Job", "description": "The final state of the job."}, "kind": {"type": "string", "description": "The resource type of the response.", "default": "bigquery#jobCancelResponse"}}}, "JobConfiguration": {"id": "JobConfiguration", "type": "object", "properties": {"copy": {"$ref": "JobConfigurationTableCopy", "description": "[Pick one] Copies a table."}, "dryRun": {"type": "boolean", "description": "[Optional] If set, don't actually run this job. A valid query will return a mostly empty response with some processing statistics, while an invalid query will return the same error it would if it wasn't a dry run. Behavior of non-query jobs is undefined."}, "extract": {"$ref": "JobConfigurationExtract", "description": "[Pick one] Configures an extract job."}, "jobTimeoutMs": {"type": "string", "description": "[Optional] Job timeout in milliseconds. If this time limit is exceeded, <PERSON><PERSON><PERSON><PERSON> may attempt to terminate the job.", "format": "int64"}, "jobType": {"type": "string", "description": "[Output-only] The type of the job. Can be QUERY, LOAD, EXTRACT, COPY or UNKNOWN."}, "labels": {"type": "object", "description": "The labels associated with this job. You can use these to organize and group your jobs. Label keys and values can be no longer than 63 characters, can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter and each label in the list must have a different key.", "additionalProperties": {"type": "string"}}, "load": {"$ref": "JobConfigurationLoad", "description": "[Pick one] Configures a load job."}, "query": {"$ref": "JobConfigurationQuery", "description": "[Pick one] Configures a query job."}}}, "JobConfigurationExtract": {"id": "JobConfigurationExtract", "type": "object", "properties": {"compression": {"type": "string", "description": "[Optional] The compression type to use for exported files. Possible values include GZIP, DEFLATE, SNAPPY, and NONE. The default value is NONE. DEFLATE and SNAPPY are only supported for Avro. Not applicable when extracting models."}, "destinationFormat": {"type": "string", "description": "[Optional] The exported file format. Possible values include CSV, NEWLINE_DELIMITED_JSON, PARQUET or AVRO for tables and ML_TF_SAVED_MODEL or ML_XGBOOST_BOOSTER for models. The default value for tables is CSV. Tables with nested or repeated fields cannot be exported as CSV. The default value for models is ML_TF_SAVED_MODEL."}, "destinationUri": {"type": "string", "description": "[Pick one] DEPRECATED: Use destinationUris instead, passing only one URI as necessary. The fully-qualified Google Cloud Storage URI where the extracted table should be written."}, "destinationUris": {"type": "array", "description": "[Pick one] A list of fully-qualified Google Cloud Storage URIs where the extracted table should be written.", "items": {"type": "string"}}, "fieldDelimiter": {"type": "string", "description": "[Optional] Delimiter to use between fields in the exported data. Default is ','. Not applicable when extracting models."}, "printHeader": {"type": "boolean", "description": "[Optional] Whether to print out a header row in the results. Default is true. Not applicable when extracting models.", "default": "true"}, "sourceModel": {"$ref": "ModelReference", "description": "A reference to the model being exported."}, "sourceTable": {"$ref": "TableReference", "description": "A reference to the table being exported."}, "useAvroLogicalTypes": {"type": "boolean", "description": "[Optional] If destinationFormat is set to \"AVRO\", this flag indicates whether to enable extracting applicable column types (such as TIMESTAMP) to their corresponding AVRO logical types (timestamp-micros), instead of only using their raw types (avro-long). Not applicable when extracting models."}}}, "JobConfigurationLoad": {"id": "JobConfigurationLoad", "type": "object", "properties": {"allowJaggedRows": {"type": "boolean", "description": "[Optional] Accept rows that are missing trailing optional columns. The missing values are treated as nulls. If false, records with missing trailing columns are treated as bad records, and if there are too many bad records, an invalid error is returned in the job result. The default value is false. Only applicable to CSV, ignored for other formats."}, "allowQuotedNewlines": {"type": "boolean", "description": "Indicates if BigQuery should allow quoted data sections that contain newline characters in a CSV file. The default value is false."}, "autodetect": {"type": "boolean", "description": "[Optional] Indicates if we should automatically infer the options and schema for CSV and JSON sources."}, "clustering": {"$ref": "Clustering", "description": "[Beta] Clustering specification for the destination table. Must be specified with time-based partitioning, data in the table will be first partitioned and subsequently clustered."}, "connectionProperties": {"type": "array", "description": "Connection properties.", "items": {"$ref": "ConnectionProperty"}}, "createDisposition": {"type": "string", "description": "[Optional] Specifies whether the job is allowed to create new tables. The following values are supported: CREATE_IF_NEEDED: If the table does not exist, BigQuery creates the table. CREATE_NEVER: The table must already exist. If it does not, a 'notFound' error is returned in the job result. The default value is CREATE_IF_NEEDED. Creation, truncation and append actions occur as one atomic update upon job completion."}, "createSession": {"type": "boolean", "description": "If true, creates a new session, where session id will be a server generated random id. If false, runs query with an existing session_id passed in ConnectionProperty, otherwise runs the load job in non-session mode."}, "decimalTargetTypes": {"type": "array", "description": "[Optional] Defines the list of possible SQL data types to which the source decimal values are converted. This list and the precision and the scale parameters of the decimal field determine the target type. In the order of NUMERIC, BIGNUMERIC, and STRING, a type is picked if it is in the specified list and if it supports the precision and the scale. STRING supports all precision and scale values. If none of the listed types supports the precision and the scale, the type supporting the widest range in the specified list is picked, and if a value exceeds the supported range when reading the data, an error will be thrown. Example: Suppose the value of this field is [\"NUMER<PERSON>\", \"BIGNUMERI<PERSON>\"]. If (precision,scale) is: (38,9) -> NUMERIC; (39,9) -> BIGNUMERIC (NUMERIC cannot hold 30 integer digits); (38,10) -> BIGNUMERIC (NUMERIC cannot hold 10 fractional digits); (76,38) -> BIGNUMERIC; (77,38) -> BIGNUMERIC (error if value exeeds supported range). This field cannot contain duplicate types. The order of the types in this field is ignored. For example, [\"<PERSON>IG<PERSON><PERSON><PERSON><PERSON>\", \"NUMER<PERSON>\"] is the same as [\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON>IGNUME<PERSON><PERSON>\"] and NUMERIC always takes precedence over <PERSON><PERSON>NUMERI<PERSON>. Defaults to [\"<PERSON><PERSON>ER<PERSON>\", \"STRING\"] for ORC and [\"NUMER<PERSON>\"] for the other file formats.", "items": {"type": "string"}}, "destinationEncryptionConfiguration": {"$ref": "EncryptionConfiguration", "description": "Custom encryption configuration (e.g., Cloud KMS keys)."}, "destinationTable": {"$ref": "TableReference", "description": "[Required] The destination table to load the data into."}, "destinationTableProperties": {"$ref": "DestinationTableProperties", "description": "[Beta] [Optional] Properties with which to create the destination table if it is new."}, "encoding": {"type": "string", "description": "[Optional] The character encoding of the data. The supported values are UTF-8 or ISO-8859-1. The default value is UTF-8. BigQuery decodes the data after the raw, binary data has been split using the values of the quote and fieldDelimiter properties."}, "fieldDelimiter": {"type": "string", "description": "[Optional] The separator for fields in a CSV file. The separator can be any ISO-8859-1 single-byte character. To use a character in the range 128-255, you must encode the character as UTF8. BigQuery converts the string to ISO-8859-1 encoding, and then uses the first byte of the encoded string to split the data in its raw, binary state. BigQuery also supports the escape sequence \"\\t\" to specify a tab separator. The default value is a comma (',')."}, "fileSetSpecType": {"type": "string", "description": "[Optional] Specifies how source URIs are interpreted for constructing the file set to load. By default source URIs are expanded against the underlying storage. Other options include specifying manifest files. Only applicable to object storage systems."}, "hivePartitioningOptions": {"$ref": "HivePartitioningOptions", "description": "[Optional] Options to configure hive partitioning support."}, "ignoreUnknownValues": {"type": "boolean", "description": "[Optional] Indicates if BigQuery should allow extra values that are not represented in the table schema. If true, the extra values are ignored. If false, records with extra columns are treated as bad records, and if there are too many bad records, an invalid error is returned in the job result. The default value is false. The sourceFormat property determines what BigQuery treats as an extra value: CSV: Trailing columns JSON: Named values that don't match any column names"}, "jsonExtension": {"type": "string", "description": "[Optional] If sourceFormat is set to newline-delimited JSON, indicates whether it should be processed as a JSON variant such as GeoJSON. For a sourceFormat other than JSON, omit this field. If the sourceFormat is newline-delimited JSON: - for newline-delimited GeoJSON: set to GEOJSON."}, "maxBadRecords": {"type": "integer", "description": "[Optional] The maximum number of bad records that BigQuery can ignore when running the job. If the number of bad records exceeds this value, an invalid error is returned in the job result. This is only valid for CSV and JSON. The default value is 0, which requires that all records are valid.", "format": "int32"}, "nullMarker": {"type": "string", "description": "[Optional] Specifies a string that represents a null value in a CSV file. For example, if you specify \"\\N\", BigQuery interprets \"\\N\" as a null value when loading a CSV file. The default value is the empty string. If you set this property to a custom value, BigQuery throws an error if an empty string is present for all data types except for STRING and BYTE. For STRING and BYTE columns, BigQuery interprets the empty string as an empty value."}, "parquetOptions": {"$ref": "ParquetOptions", "description": "[Optional] Options to configure parquet support."}, "preserveAsciiControlCharacters": {"type": "boolean", "description": "[Optional] Preserves the embedded ASCII control characters (the first 32 characters in the ASCII-table, from '\\x00' to '\\x1F') when loading from CSV. Only applicable to CSV, ignored for other formats."}, "projectionFields": {"type": "array", "description": "If sourceFormat is set to \"DATASTORE_BACKUP\", indicates which entity properties to load into BigQuery from a Cloud Datastore backup. Property names are case sensitive and must be top-level properties. If no properties are specified, BigQuery loads all properties. If any named property isn't found in the Cloud Datastore backup, an invalid error is returned in the job result.", "items": {"type": "string"}}, "quote": {"type": "string", "description": "[Optional] The value that is used to quote data sections in a CSV file. BigQuery converts the string to ISO-8859-1 encoding, and then uses the first byte of the encoded string to split the data in its raw, binary state. The default value is a double-quote ('\"'). If your data does not contain quoted sections, set the property value to an empty string. If your data contains quoted newline characters, you must also set the allowQuotedNewlines property to true.", "default": "\"", "pattern": ".?"}, "rangePartitioning": {"$ref": "RangePartitioning", "description": "[TrustedTester] Range partitioning specification for this table. Only one of timePartitioning and rangePartitioning should be specified."}, "referenceFileSchemaUri": {"type": "string", "description": "User provided referencing file with the expected reader schema, Available for the format: AVRO, PARQUET, ORC."}, "schema": {"$ref": "TableSchema", "description": "[Optional] The schema for the destination table. The schema can be omitted if the destination table already exists, or if you're loading data from Google Cloud Datastore."}, "schemaInline": {"type": "string", "description": "[Deprecated] The inline schema. For CSV schemas, specify as \"Field1:Type1[,Field2:Type2]*\". For example, \"foo:STRING, bar:INTEGER, baz:FLOAT\"."}, "schemaInlineFormat": {"type": "string", "description": "[Deprecated] The format of the schemaInline property."}, "schemaUpdateOptions": {"type": "array", "description": "Allows the schema of the destination table to be updated as a side effect of the load job if a schema is autodetected or supplied in the job configuration. Schema update options are supported in two cases: when writeDisposition is WRITE_APPEND; when writeDisposition is WRITE_TRUNCATE and the destination table is a partition of a table, specified by partition decorators. For normal tables, WRITE_TRUNCATE will always overwrite the schema. One or more of the following values are specified: ALLOW_FIELD_ADDITION: allow adding a nullable field to the schema. ALLOW_FIELD_RELAXATION: allow relaxing a required field in the original schema to nullable.", "items": {"type": "string"}}, "skipLeadingRows": {"type": "integer", "description": "[Optional] The number of rows at the top of a CSV file that BigQuery will skip when loading the data. The default value is 0. This property is useful if you have header rows in the file that should be skipped.", "format": "int32"}, "sourceFormat": {"type": "string", "description": "[Optional] The format of the data files. For CSV files, specify \"CSV\". For datastore backups, specify \"DATASTORE_BACKUP\". For newline-delimited JSON, specify \"NEWLINE_DELIMITED_JSON\". For Avro, specify \"AVRO\". For parquet, specify \"PARQUET\". For orc, specify \"ORC\". The default value is CSV."}, "sourceUris": {"type": "array", "description": "[Required] The fully-qualified URIs that point to your data in Google Cloud. For Google Cloud Storage URIs: Each URI can contain one '*' wildcard character and it must come after the 'bucket' name. Size limits related to load jobs apply to external data sources. For Google Cloud Bigtable URIs: Exactly one URI can be specified and it has be a fully specified and valid HTTPS URL for a Google Cloud Bigtable table. For Google Cloud Datastore backups: Exactly one URI can be specified. Also, the '*' wildcard character is not allowed.", "items": {"type": "string"}}, "timePartitioning": {"$ref": "TimePartitioning", "description": "Time-based partitioning specification for the destination table. Only one of timePartitioning and rangePartitioning should be specified."}, "useAvroLogicalTypes": {"type": "boolean", "description": "[Optional] If sourceFormat is set to \"AVRO\", indicates whether to interpret logical types as the corresponding BigQuery data type (for example, TIMESTAMP), instead of using the raw type (for example, INTEGER)."}, "writeDisposition": {"type": "string", "description": "[Optional] Specifies the action that occurs if the destination table already exists. The following values are supported: WRITE_TRUNCATE: If the table already exists, <PERSON><PERSON><PERSON><PERSON> overwrites the table data. WRITE_APPEND: If the table already exists, <PERSON>Query appends the data to the table. WRITE_EMPTY: If the table already exists and contains data, a 'duplicate' error is returned in the job result. The default value is WRITE_APPEND. Each action is atomic and only occurs if <PERSON><PERSON><PERSON><PERSON> is able to complete the job successfully. Creation, truncation and append actions occur as one atomic update upon job completion."}}}, "JobConfigurationQuery": {"id": "JobConfigurationQuery", "type": "object", "properties": {"allowLargeResults": {"type": "boolean", "description": "[Optional] If true and query uses legacy SQL dialect, allows the query to produce arbitrarily large result tables at a slight cost in performance. Requires destinationTable to be set. For standard SQL queries, this flag is ignored and large results are always allowed. However, you must still set destinationTable when result size exceeds the allowed maximum response size.", "default": "false"}, "clustering": {"$ref": "Clustering", "description": "[Beta] Clustering specification for the destination table. Must be specified with time-based partitioning, data in the table will be first partitioned and subsequently clustered."}, "connectionProperties": {"type": "array", "description": "Connection properties.", "items": {"$ref": "ConnectionProperty"}}, "continuous": {"type": "boolean", "description": "[Optional] Specifies whether the query should be executed as a continuous query. The default value is false."}, "createDisposition": {"type": "string", "description": "[Optional] Specifies whether the job is allowed to create new tables. The following values are supported: CREATE_IF_NEEDED: If the table does not exist, BigQuery creates the table. CREATE_NEVER: The table must already exist. If it does not, a 'notFound' error is returned in the job result. The default value is CREATE_IF_NEEDED. Creation, truncation and append actions occur as one atomic update upon job completion."}, "createSession": {"type": "boolean", "description": "If true, creates a new session, where session id will be a server generated random id. If false, runs query with an existing session_id passed in ConnectionProperty, otherwise runs query in non-session mode."}, "defaultDataset": {"$ref": "DatasetReference", "description": "[Optional] Specifies the default dataset to use for unqualified table names in the query. Note that this does not alter behavior of unqualified dataset names."}, "destinationEncryptionConfiguration": {"$ref": "EncryptionConfiguration", "description": "Custom encryption configuration (e.g., Cloud KMS keys)."}, "destinationTable": {"$ref": "TableReference", "description": "[Optional] Describes the table where the query results should be stored. If not present, a new table will be created to store the results. This property must be set for large results that exceed the maximum response size."}, "flattenResults": {"type": "boolean", "description": "[Optional] If true and query uses legacy SQL dialect, flattens all nested and repeated fields in the query results. allowLargeResults must be true if this is set to false. For standard SQL queries, this flag is ignored and results are never flattened.", "default": "true"}, "maximumBillingTier": {"type": "integer", "description": "[Optional] Limits the billing tier for this job. Queries that have resource usage beyond this tier will fail (without incurring a charge). If unspecified, this will be set to your project default.", "default": "1", "format": "int32"}, "maximumBytesBilled": {"type": "string", "description": "[Optional] Limits the bytes billed for this job. Queries that will have bytes billed beyond this limit will fail (without incurring a charge). If unspecified, this will be set to your project default.", "format": "int64"}, "parameterMode": {"type": "string", "description": "Standard SQL only. Set to POSITIONAL to use positional (?) query parameters or to NAMED to use named (@myparam) query parameters in this query."}, "preserveNulls": {"type": "boolean", "description": "[Deprecated] This property is deprecated."}, "priority": {"type": "string", "description": "[Optional] Specifies a priority for the query. Possible values include INTERACTIVE and BATCH. The default value is INTERACTIVE."}, "query": {"type": "string", "description": "[Required] SQL query text to execute. The useLegacySql field can be used to indicate whether the query uses legacy SQL or standard SQL."}, "queryParameters": {"type": "array", "description": "Query parameters for standard SQL queries.", "items": {"$ref": "QueryParameter"}}, "rangePartitioning": {"$ref": "RangePartitioning", "description": "[TrustedTester] Range partitioning specification for this table. Only one of timePartitioning and rangePartitioning should be specified."}, "schemaUpdateOptions": {"type": "array", "description": "Allows the schema of the destination table to be updated as a side effect of the query job. Schema update options are supported in two cases: when writeDisposition is WRITE_APPEND; when writeDisposition is WRITE_TRUNCATE and the destination table is a partition of a table, specified by partition decorators. For normal tables, WRITE_TRUNCATE will always overwrite the schema. One or more of the following values are specified: ALLOW_FIELD_ADDITION: allow adding a nullable field to the schema. ALLOW_FIELD_RELAXATION: allow relaxing a required field in the original schema to nullable.", "items": {"type": "string"}}, "tableDefinitions": {"type": "object", "description": "[Optional] If querying an external data source outside of BigQuery, describes the data format, location and other properties of the data source. By defining these properties, the data source can then be queried as if it were a standard BigQuery table.", "additionalProperties": {"$ref": "ExternalDataConfiguration"}}, "timePartitioning": {"$ref": "TimePartitioning", "description": "Time-based partitioning specification for the destination table. Only one of timePartitioning and rangePartitioning should be specified."}, "useLegacySql": {"type": "boolean", "description": "Specifies whether to use BigQuery's legacy SQL dialect for this query. The default value is true. If set to false, the query will use BigQuery's standard SQL: https://cloud.google.com/bigquery/sql-reference/ When useLegacySql is set to false, the value of flattenResults is ignored; query will be run as if flattenResults is false.", "default": "true"}, "useQueryCache": {"type": "boolean", "description": "[Optional] Whether to look for the result in the query cache. The query cache is a best-effort cache that will be flushed whenever tables in the query are modified. Moreover, the query cache is only available when a query does not have a destination table specified. The default value is true.", "default": "true"}, "userDefinedFunctionResources": {"type": "array", "description": "Describes user-defined function resources used in the query.", "items": {"$ref": "UserDefinedFunctionResource"}}, "writeDisposition": {"type": "string", "description": "[Optional] Specifies the action that occurs if the destination table already exists. The following values are supported: WRITE_TRUNCATE: If the table already exists, <PERSON><PERSON>uery overwrites the table data and uses the schema from the query result. WRITE_APPEND: If the table already exists, <PERSON>Query appends the data to the table. WRITE_EMPTY: If the table already exists and contains data, a 'duplicate' error is returned in the job result. The default value is WRITE_EMPTY. Each action is atomic and only occurs if <PERSON>Quer<PERSON> is able to complete the job successfully. Creation, truncation and append actions occur as one atomic update upon job completion."}}}, "JobConfigurationTableCopy": {"id": "JobConfigurationTableCopy", "type": "object", "properties": {"createDisposition": {"type": "string", "description": "[Optional] Specifies whether the job is allowed to create new tables. The following values are supported: CREATE_IF_NEEDED: If the table does not exist, BigQuery creates the table. CREATE_NEVER: The table must already exist. If it does not, a 'notFound' error is returned in the job result. The default value is CREATE_IF_NEEDED. Creation, truncation and append actions occur as one atomic update upon job completion."}, "destinationEncryptionConfiguration": {"$ref": "EncryptionConfiguration", "description": "Custom encryption configuration (e.g., Cloud KMS keys)."}, "destinationExpirationTime": {"type": "any", "description": "[Optional] The time when the destination table expires. Expired tables will be deleted and their storage reclaimed."}, "destinationTable": {"$ref": "TableReference", "description": "[Required] The destination table"}, "operationType": {"type": "string", "description": "[Optional] Supported operation types in table copy job."}, "sourceTable": {"$ref": "TableReference", "description": "[Pick one] Source table to copy."}, "sourceTables": {"type": "array", "description": "[Pick one] Source tables to copy.", "items": {"$ref": "TableReference"}}, "writeDisposition": {"type": "string", "description": "[Optional] Specifies the action that occurs if the destination table already exists. The following values are supported: WRITE_TRUNCATE: If the table already exists, <PERSON><PERSON><PERSON><PERSON> overwrites the table data. WRITE_APPEND: If the table already exists, <PERSON>Query appends the data to the table. WRITE_EMPTY: If the table already exists and contains data, a 'duplicate' error is returned in the job result. The default value is WRITE_EMPTY. Each action is atomic and only occurs if <PERSON><PERSON><PERSON><PERSON> is able to complete the job successfully. Creation, truncation and append actions occur as one atomic update upon job completion."}}}, "JobList": {"id": "JobList", "type": "object", "properties": {"etag": {"type": "string", "description": "A hash of this page of results."}, "jobs": {"type": "array", "description": "List of jobs that were requested.", "items": {"type": "object", "properties": {"configuration": {"$ref": "JobConfiguration", "description": "[Full-projection-only] Specifies the job configuration."}, "errorResult": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "description": "A result object that will be present only if the job has failed."}, "id": {"type": "string", "description": "Unique opaque ID of the job."}, "jobReference": {"$ref": "JobReference", "description": "Job reference uniquely identifying the job."}, "kind": {"type": "string", "description": "The resource type.", "default": "bigquery#job"}, "state": {"type": "string", "description": "Running state of the job. When the state is DONE, errorResult can be checked to determine whether the job succeeded or failed."}, "statistics": {"$ref": "JobStatistics", "description": "[Output-only] Information about the job, including starting time and ending time of the job."}, "status": {"$ref": "JobStatus", "description": "[Full-projection-only] Describes the state of the job."}, "user_email": {"type": "string", "description": "[Full-projection-only] Email address of the user who ran the job."}}}}, "kind": {"type": "string", "description": "The resource type of the response.", "default": "bigquery#jobList"}, "nextPageToken": {"type": "string", "description": "A token to request the next page of results."}}}, "JobReference": {"id": "JobReference", "type": "object", "properties": {"jobId": {"type": "string", "description": "[Required] The ID of the job. The ID must contain only letters (a-z, A-Z), numbers (0-9), underscores (_), or dashes (-). The maximum length is 1,024 characters.", "annotations": {"required": ["bigquery.jobs.getQueryResults"]}}, "location": {"type": "string", "description": "The geographic location of the job. See details at https://cloud.google.com/bigquery/docs/locations#specifying_your_location."}, "projectId": {"type": "string", "description": "[Required] The ID of the project containing this job.", "annotations": {"required": ["bigquery.jobs.getQueryResults"]}}}}, "JobStatistics": {"id": "JobStatistics", "type": "object", "properties": {"completionRatio": {"type": "number", "description": "[TrustedTester] [Output-only] Job progress (0.0 -> 1.0) for LOAD and EXTRACT jobs.", "format": "double"}, "copy": {"$ref": "JobStatistics5", "description": "[Output-only] Statistics for a copy job."}, "creationTime": {"type": "string", "description": "[Output-only] Creation time of this job, in milliseconds since the epoch. This field will be present on all jobs.", "format": "int64"}, "dataMaskingStatistics": {"$ref": "DataMaskingStatistics", "description": "[Output-only] Statistics for data masking. Present only for query and extract jobs."}, "endTime": {"type": "string", "description": "[Output-only] End time of this job, in milliseconds since the epoch. This field will be present whenever a job is in the DONE state.", "format": "int64"}, "extract": {"$ref": "JobStatistics4", "description": "[Output-only] Statistics for an extract job."}, "load": {"$ref": "JobStatistics3", "description": "[Output-only] Statistics for a load job."}, "numChildJobs": {"type": "string", "description": "[Output-only] Number of child jobs executed.", "format": "int64"}, "parentJobId": {"type": "string", "description": "[Output-only] If this is a child job, the id of the parent."}, "query": {"$ref": "JobStatistics2", "description": "[Output-only] Statistics for a query job."}, "quotaDeferments": {"type": "array", "description": "[Output-only] Quotas which delayed this job's start time.", "items": {"type": "string"}}, "reservationUsage": {"type": "array", "description": "[Output-only] Job resource usage breakdown by reservation.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "[Output-only] Reservation name or \"unreserved\" for on-demand resources usage."}, "slotMs": {"type": "string", "description": "[Output-only] Slot-milliseconds the job spent in the given reservation.", "format": "int64"}}}}, "reservation_id": {"type": "string", "description": "[Output-only] Name of the primary reservation assigned to this job. Note that this could be different than reservations reported in the reservation usage field if parent reservations were used to execute this job."}, "rowLevelSecurityStatistics": {"$ref": "RowLevelSecurityStatistics", "description": "[Output-only] [Preview] Statistics for row-level security. Present only for query and extract jobs."}, "scriptStatistics": {"$ref": "ScriptStatistics", "description": "[Output-only] Statistics for a child job of a script."}, "sessionInfo": {"$ref": "SessionInfo", "description": "[Output-only] [Preview] Information of the session if this job is part of one."}, "startTime": {"type": "string", "description": "[Output-only] Start time of this job, in milliseconds since the epoch. This field will be present when the job transitions from the PENDING state to either RUNNING or DONE.", "format": "int64"}, "totalBytesProcessed": {"type": "string", "description": "[Output-only] [Deprecated] Use the bytes processed in the query statistics instead.", "format": "int64"}, "totalSlotMs": {"type": "string", "description": "[Output-only] Slot-milliseconds for the job.", "format": "int64"}, "transactionInfo": {"$ref": "TransactionInfo", "description": "[Output-only] [Alpha] Information of the multi-statement transaction if this job is part of one."}}}, "JobStatistics2": {"id": "JobStatistics2", "type": "object", "properties": {"biEngineStatistics": {"$ref": "BiEngineStatistics", "description": "BI Engine specific Statistics. [Output only] BI Engine specific Statistics."}, "billingTier": {"type": "integer", "description": "[Output only] Billing tier for the job.", "format": "int32"}, "cacheHit": {"type": "boolean", "description": "[Output only] Whether the query result was fetched from the query cache."}, "ddlAffectedRowAccessPolicyCount": {"type": "string", "description": "[Output only] [Preview] The number of row access policies affected by a DDL statement. Present only for DROP ALL ROW ACCESS POLICIES queries.", "format": "int64"}, "ddlDestinationTable": {"$ref": "TableReference", "description": "[Output only] The DDL destination table. Present only for ALTER TABLE RENAME TO queries. Note that ddl_target_table is used just for its type information."}, "ddlOperationPerformed": {"type": "string", "description": "The DDL operation performed, possibly dependent on the pre-existence of the DDL target. Possible values (new values might be added in the future): \"CREATE\": The query created the DDL target. \"SKIP\": No-op. Example cases: the query is CREATE TABLE IF NOT EXISTS while the table already exists, or the query is DROP TABLE IF EXISTS while the table does not exist. \"REPLACE\": The query replaced the DDL target. Example case: the query is CREATE OR REPLACE TABLE, and the table already exists. \"DROP\": The query deleted the DDL target."}, "ddlTargetDataset": {"$ref": "DatasetReference", "description": "[Output only] The DDL target dataset. Present only for CREATE/ALTER/DROP SCHEMA queries."}, "ddlTargetRoutine": {"$ref": "RoutineReference", "description": "The DDL target routine. Present only for CREATE/DROP FUNCTION/PROCEDURE queries."}, "ddlTargetRowAccessPolicy": {"$ref": "RowAccessPolicyReference", "description": "[Output only] [Preview] The DDL target row access policy. Present only for CREATE/DROP ROW ACCESS POLICY queries."}, "ddlTargetTable": {"$ref": "TableReference", "description": "[Output only] The DDL target table. Present only for CREATE/DROP TABLE/VIEW and DROP ALL ROW ACCESS POLICIES queries."}, "dmlStats": {"$ref": "DmlStatistics", "description": "[Output only] Detailed statistics for DML statements Present only for DML statements INSERT, UPDATE, DELETE or TRUNCATE."}, "estimatedBytesProcessed": {"type": "string", "description": "[Output only] The original estimate of bytes processed for the job.", "format": "int64"}, "mlStatistics": {"$ref": "MlStatistics", "description": "[Output only] Statistics of a BigQuery ML training job."}, "modelTraining": {"$ref": "BigQueryModelTraining", "description": "[Output only, Beta] Information about create model query job progress."}, "modelTrainingCurrentIteration": {"type": "integer", "description": "[Output only, Beta] Deprecated; do not use.", "format": "int32"}, "modelTrainingExpectedTotalIteration": {"type": "string", "description": "[Output only, Beta] Deprecated; do not use.", "format": "int64"}, "numDmlAffectedRows": {"type": "string", "description": "[Output only] The number of rows affected by a DML statement. Present only for DML statements INSERT, UPDATE or DELETE.", "format": "int64"}, "queryPlan": {"type": "array", "description": "[Output only] Describes execution plan for the query.", "items": {"$ref": "ExplainQueryStage"}}, "referencedRoutines": {"type": "array", "description": "[Output only] Referenced routines (persistent user-defined functions and stored procedures) for the job.", "items": {"$ref": "RoutineReference"}}, "referencedTables": {"type": "array", "description": "[Output only] Referenced tables for the job. Queries that reference more than 50 tables will not have a complete list.", "items": {"$ref": "TableReference"}}, "reservationUsage": {"type": "array", "description": "[Output only] Job resource usage breakdown by reservation.", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "[Output only] Reservation name or \"unreserved\" for on-demand resources usage."}, "slotMs": {"type": "string", "description": "[Output only] Slot-milliseconds the job spent in the given reservation.", "format": "int64"}}}}, "schema": {"$ref": "TableSchema", "description": "[Output only] The schema of the results. Present only for successful dry run of non-legacy SQL queries."}, "searchStatistics": {"$ref": "SearchStatistics", "description": "[Output only] Search query specific statistics."}, "sparkStatistics": {"$ref": "SparkStatistics", "description": "[Output only] Statistics of a Spark procedure job."}, "statementType": {"type": "string", "description": "The type of query statement, if valid. Possible values (new values might be added in the future): \"SELECT\": SELECT query. \"INSERT\": INSERT query; see https://cloud.google.com/bigquery/docs/reference/standard-sql/data-manipulation-language. \"UPDATE\": UPDATE query; see https://cloud.google.com/bigquery/docs/reference/standard-sql/data-manipulation-language. \"DELETE\": DELETE query; see https://cloud.google.com/bigquery/docs/reference/standard-sql/data-manipulation-language. \"MERGE\": MERGE query; see https://cloud.google.com/bigquery/docs/reference/standard-sql/data-manipulation-language. \"ALTER_TABLE\": ALTER TABLE query. \"ALTER_VIEW\": ALTER VIEW query. \"ASSERT\": ASSERT condition AS 'description'. \"CREATE_FUNCTION\": CREATE FUNCTION query. \"CREATE_MODEL\": CREATE [OR REPLACE] MODEL ... AS SELECT ... . \"CREATE_PROCEDURE\": CREATE PROCEDURE query. \"CREATE_TABLE\": CREATE [OR REPLACE] TABLE without AS SELECT. \"CREATE_TABLE_AS_SELECT\": CREATE [OR REPLACE] TABLE ... AS SELECT ... . \"CREATE_VIEW\": CREATE [OR REPLACE] VIEW ... AS SELECT ... . \"DROP_FUNCTION\" : DROP FUNCTION query. \"DROP_PROCEDURE\": DROP PROCEDURE query. \"DROP_TABLE\": DROP TABLE query. \"DROP_VIEW\": DROP VIEW query."}, "timeline": {"type": "array", "description": "[Output only] [Beta] Describes a timeline of job execution.", "items": {"$ref": "QueryTimelineSample"}}, "totalBytesBilled": {"type": "string", "description": "[Output only] Total bytes billed for the job.", "format": "int64"}, "totalBytesProcessed": {"type": "string", "description": "[Output only] Total bytes processed for the job.", "format": "int64"}, "totalBytesProcessedAccuracy": {"type": "string", "description": "[Output only] For dry-run jobs, totalBytesProcessed is an estimate and this field specifies the accuracy of the estimate. Possible values can be: UNKNOWN: accuracy of the estimate is unknown. PRECISE: estimate is precise. LOWER_BOUND: estimate is lower bound of what the query would cost. UPPER_BOUND: estimate is upper bound of what the query would cost."}, "totalPartitionsProcessed": {"type": "string", "description": "[Output only] Total number of partitions processed from all partitioned tables referenced in the job.", "format": "int64"}, "totalSlotMs": {"type": "string", "description": "[Output only] Slot-milliseconds for the job.", "format": "int64"}, "transferredBytes": {"type": "string", "description": "[Output-only] Total bytes transferred for cross-cloud queries such as Cross Cloud Transfer and CREATE TABLE AS SELECT (CTAS).", "format": "int64"}, "undeclaredQueryParameters": {"type": "array", "description": "Standard SQL only: list of undeclared query parameters detected during a dry run validation.", "items": {"$ref": "QueryParameter"}}}}, "JobStatistics3": {"id": "JobStatistics3", "type": "object", "properties": {"badRecords": {"type": "string", "description": "[Output-only] The number of bad records encountered. Note that if the job has failed because of more bad records encountered than the maximum allowed in the load job configuration, then this number can be less than the total number of bad records present in the input data.", "format": "int64"}, "inputFileBytes": {"type": "string", "description": "[Output-only] Number of bytes of source data in a load job.", "format": "int64"}, "inputFiles": {"type": "string", "description": "[Output-only] Number of source files in a load job.", "format": "int64"}, "outputBytes": {"type": "string", "description": "[Output-only] Size of the loaded data in bytes. Note that while a load job is in the running state, this value may change.", "format": "int64"}, "outputRows": {"type": "string", "description": "[Output-only] Number of rows imported in a load job. Note that while an import job is in the running state, this value may change.", "format": "int64"}}}, "JobStatistics4": {"id": "JobStatistics4", "type": "object", "properties": {"destinationUriFileCounts": {"type": "array", "description": "[Output-only] Number of files per destination URI or URI pattern specified in the extract configuration. These values will be in the same order as the URIs specified in the 'destinationUris' field.", "items": {"type": "string", "format": "int64"}}, "inputBytes": {"type": "string", "description": "[Output-only] Number of user bytes extracted into the result. This is the byte count as computed by BigQuery for billing purposes.", "format": "int64"}}}, "JobStatistics5": {"id": "JobStatistics5", "type": "object", "properties": {"copiedLogicalBytes": {"type": "string", "description": "[Output-only] Number of logical bytes copied to the destination table.", "format": "int64"}, "copiedRows": {"type": "string", "description": "[Output-only] Number of rows copied to the destination table.", "format": "int64"}}}, "JobStatus": {"id": "JobStatus", "type": "object", "properties": {"errorResult": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "description": "[Output-only] Final error result of the job. If present, indicates that the job has completed and was unsuccessful."}, "errors": {"type": "array", "description": "[Output-only] The first errors encountered during the running of the job. The final message includes the number of errors that caused the process to stop. Errors here do not necessarily mean that the job has completed or was unsuccessful.", "items": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}}, "state": {"type": "string", "description": "[Output-only] Running state of the job."}}}, "JsonObject": {"id": "JsonObject", "type": "object", "description": "Represents a single JSON object.", "additionalProperties": {"$ref": "JsonValue"}}, "JsonOptions": {"id": "JsonOptions", "type": "object", "properties": {"encoding": {"type": "string", "description": "[Optional] The character encoding of the data. The supported values are UTF-8, UTF-16BE, UTF-16LE, UTF-32BE, and UTF-32LE. The default value is UTF-8."}}}, "JsonValue": {"id": "JsonValue", "type": "any"}, "MaterializedViewDefinition": {"id": "MaterializedViewDefinition", "type": "object", "properties": {"allowNonIncrementalDefinition": {"type": "boolean", "description": "[Optional] Allow non incremental materialized view definition. The default value is \"false\"."}, "enableRefresh": {"type": "boolean", "description": "[Optional] [TrustedTester] Enable automatic refresh of the materialized view when the base table is updated. The default value is \"true\"."}, "lastRefreshTime": {"type": "string", "description": "[Output-only] [TrustedTester] The time when this materialized view was last modified, in milliseconds since the epoch.", "format": "int64"}, "maxStaleness": {"type": "string", "description": "[Optional] Max staleness of data that could be returned when materizlized view is queried (formatted as Google SQL Interval type).", "format": "byte"}, "query": {"type": "string", "description": "[Required] A query whose result is persisted."}, "refreshIntervalMs": {"type": "string", "description": "[Optional] [TrustedTester] The maximum frequency at which this materialized view will be refreshed. The default value is \"1800000\" (30 minutes).", "format": "int64"}}}, "MlStatistics": {"id": "MlStatistics", "type": "object", "properties": {"iterationResults": {"type": "array", "description": "Results for all completed iterations.", "items": {"$ref": "IterationResult"}}, "maxIterations": {"type": "string", "description": "Maximum number of iterations specified as max_iterations in the 'CREATE MODEL' query. The actual number of iterations may be less than this number due to early stop.", "format": "int64"}}}, "ModelDefinition": {"id": "ModelDefinition", "type": "object", "properties": {"modelOptions": {"type": "object", "description": "[Output-only, Beta] Model options used for the first training run. These options are immutable for subsequent training runs. Default values are used for any options not specified in the input query.", "properties": {"labels": {"type": "array", "items": {"type": "string"}}, "lossType": {"type": "string"}, "modelType": {"type": "string"}}}, "trainingRuns": {"type": "array", "description": "[Output-only, Beta] Information about ml training runs, each training run comprises of multiple iterations and there may be multiple training runs for the model if warm start is used or if a user decides to continue a previously cancelled query.", "items": {"$ref": "BqmlTrainingRun"}}}}, "ModelReference": {"id": "ModelReference", "type": "object", "properties": {"datasetId": {"type": "string", "description": "Required. The ID of the dataset containing this model."}, "modelId": {"type": "string", "description": "Required. The ID of the model. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 1,024 characters."}, "projectId": {"type": "string", "description": "Required. The ID of the project containing this model."}}}, "ParquetOptions": {"id": "ParquetOptions", "type": "object", "properties": {"enableListInference": {"type": "boolean", "description": "[Optional] Indicates whether to use schema inference specifically for Parquet LIST logical type."}, "enumAsString": {"type": "boolean", "description": "[Optional] Indicates whether to infer Parquet ENUM logical type as STRING instead of BYTES by default."}}}, "ProjectList": {"id": "ProjectList", "type": "object", "properties": {"etag": {"type": "string", "description": "A hash of the page of results"}, "kind": {"type": "string", "description": "The type of list.", "default": "bigquery#projectList"}, "nextPageToken": {"type": "string", "description": "A token to request the next page of results."}, "projects": {"type": "array", "description": "Projects to which you have at least READ access.", "items": {"type": "object", "properties": {"friendlyName": {"type": "string", "description": "A descriptive name for this project."}, "id": {"type": "string", "description": "An opaque ID of this project."}, "kind": {"type": "string", "description": "The resource type.", "default": "bigquery#project"}, "numericId": {"type": "string", "description": "The numeric ID of this project.", "format": "uint64"}, "projectReference": {"$ref": "ProjectReference", "description": "A unique reference to this project."}}}}, "totalItems": {"type": "integer", "description": "The total number of projects in the list.", "format": "int32"}}}, "ProjectReference": {"id": "ProjectReference", "type": "object", "properties": {"projectId": {"type": "string", "description": "[Required] ID of the project. Can be either the numeric ID or the assigned ID of the project."}}}, "QueryParameter": {"id": "QueryParameter", "type": "object", "properties": {"name": {"type": "string", "description": "[Optional] If unset, this is a positional parameter. Otherwise, should be unique within a query."}, "parameterType": {"$ref": "QueryParameterType", "description": "[Required] The type of this parameter."}, "parameterValue": {"$ref": "QueryParameterValue", "description": "[Required] The value of this parameter."}}}, "QueryParameterType": {"id": "QueryParameterType", "type": "object", "properties": {"arrayType": {"$ref": "QueryParameterType", "description": "[Optional] The type of the array's elements, if this is an array."}, "structTypes": {"type": "array", "description": "[Optional] The types of the fields of this struct, in order, if this is a struct.", "items": {"type": "object", "properties": {"description": {"type": "string", "description": "[Optional] Human-oriented description of the field."}, "name": {"type": "string", "description": "[Optional] The name of this field."}, "type": {"$ref": "QueryParameterType", "description": "[Required] The type of this field."}}}}, "type": {"type": "string", "description": "[Required] The top level type of this field."}}}, "QueryParameterValue": {"id": "QueryParameterValue", "type": "object", "properties": {"arrayValues": {"type": "array", "description": "[Optional] The array values, if this is an array type.", "items": {"$ref": "QueryParameterValue"}}, "structValues": {"type": "object", "description": "[Optional] The struct field values, in order of the struct type's declaration.", "additionalProperties": {"$ref": "QueryParameterValue"}}, "value": {"type": "string", "description": "[Optional] The value of this value, if a simple scalar type."}}}, "QueryRequest": {"id": "QueryRequest", "type": "object", "properties": {"connectionProperties": {"type": "array", "description": "Connection properties.", "items": {"$ref": "ConnectionProperty"}}, "continuous": {"type": "boolean", "description": "[Optional] Specifies whether the query should be executed as a continuous query. The default value is false."}, "createSession": {"type": "boolean", "description": "If true, creates a new session, where session id will be a server generated random id. If false, runs query with an existing session_id passed in ConnectionProperty, otherwise runs query in non-session mode."}, "defaultDataset": {"$ref": "DatasetReference", "description": "[Optional] Specifies the default datasetId and projectId to assume for any unqualified table names in the query. If not set, all table names in the query string must be qualified in the format 'datasetId.tableId'."}, "dryRun": {"type": "boolean", "description": "[Optional] If set to true, BigQuery doesn't run the job. Instead, if the query is valid, BigQuery returns statistics about the job such as how many bytes would be processed. If the query is invalid, an error returns. The default value is false."}, "kind": {"type": "string", "description": "The resource type of the request.", "default": "bigquery#queryRequest"}, "labels": {"type": "object", "description": "The labels associated with this job. You can use these to organize and group your jobs. Label keys and values can be no longer than 63 characters, can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter and each label in the list must have a different key.", "additionalProperties": {"type": "string"}}, "location": {"type": "string", "description": "The geographic location where the job should run. See details at https://cloud.google.com/bigquery/docs/locations#specifying_your_location."}, "maxResults": {"type": "integer", "description": "[Optional] The maximum number of rows of data to return per page of results. Setting this flag to a small value such as 1000 and then paging through results might improve reliability when the query result set is large. In addition to this limit, responses are also limited to 10 MB. By default, there is no maximum row count, and only the byte limit applies.", "format": "uint32"}, "maximumBytesBilled": {"type": "string", "description": "[Optional] Limits the bytes billed for this job. Queries that will have bytes billed beyond this limit will fail (without incurring a charge). If unspecified, this will be set to your project default.", "format": "int64"}, "parameterMode": {"type": "string", "description": "Standard SQL only. Set to POSITIONAL to use positional (?) query parameters or to NAMED to use named (@myparam) query parameters in this query."}, "preserveNulls": {"type": "boolean", "description": "[Deprecated] This property is deprecated."}, "query": {"type": "string", "description": "[Required] A query string, following the BigQuery query syntax, of the query to execute. Example: \"SELECT count(f1) FROM [myProjectId:myDatasetId.myTableId]\".", "annotations": {"required": ["bigquery.jobs.query"]}}, "queryParameters": {"type": "array", "description": "Query parameters for Standard SQL queries.", "items": {"$ref": "QueryParameter"}}, "requestId": {"type": "string", "description": "A unique user provided identifier to ensure idempotent behavior for queries. Note that this is different from the job_id. It has the following properties: 1. It is case-sensitive, limited to up to 36 ASCII characters. A UUID is recommended. 2. Read only queries can ignore this token since they are nullipotent by definition. 3. For the purposes of idempotency ensured by the request_id, a request is considered duplicate of another only if they have the same request_id and are actually duplicates. When determining whether a request is a duplicate of the previous request, all parameters in the request that may affect the behavior are considered. For example, query, connection_properties, query_parameters, use_legacy_sql are parameters that affect the result and are considered when determining whether a request is a duplicate, but properties like timeout_ms don't affect the result and are thus not considered. Dry run query requests are never considered duplicate of another request. 4. When a duplicate mutating query request is detected, it returns: a. the results of the mutation if it completes successfully within the timeout. b. the running operation if it is still in progress at the end of the timeout. 5. Its lifetime is limited to 15 minutes. In other words, if two requests are sent with the same request_id, but more than 15 minutes apart, idempotency is not guaranteed."}, "timeoutMs": {"type": "integer", "description": "[Optional] How long to wait for the query to complete, in milliseconds, before the request times out and returns. Note that this is only a timeout for the request, not the query. If the query takes longer to run than the timeout value, the call returns without any results and with the 'jobComplete' flag set to false. You can call GetQueryResults() to wait for the query to complete and read the results. The default value is 10000 milliseconds (10 seconds).", "format": "uint32"}, "useLegacySql": {"type": "boolean", "description": "Specifies whether to use BigQuery's legacy SQL dialect for this query. The default value is true. If set to false, the query will use BigQuery's standard SQL: https://cloud.google.com/bigquery/sql-reference/ When useLegacySql is set to false, the value of flattenResults is ignored; query will be run as if flattenResults is false.", "default": "true"}, "useQueryCache": {"type": "boolean", "description": "[Optional] Whether to look for the result in the query cache. The query cache is a best-effort cache that will be flushed whenever tables in the query are modified. The default value is true.", "default": "true"}}}, "QueryResponse": {"id": "QueryResponse", "type": "object", "properties": {"cacheHit": {"type": "boolean", "description": "Whether the query result was fetched from the query cache."}, "dmlStats": {"$ref": "DmlStatistics", "description": "[Output-only] Detailed statistics for DML statements Present only for DML statements INSERT, UPDATE, DELETE or TRUNCATE."}, "errors": {"type": "array", "description": "[Output-only] The first errors or warnings encountered during the running of the job. The final message includes the number of errors that caused the process to stop. Errors here do not necessarily mean that the job has completed or was unsuccessful.", "items": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}}, "jobComplete": {"type": "boolean", "description": "Whether the query has completed or not. If rows or totalRows are present, this will always be true. If this is false, totalRows will not be available."}, "jobReference": {"$ref": "JobReference", "description": "Reference to the Job that was created to run the query. This field will be present even if the original request timed out, in which case GetQueryResults can be used to read the results once the query has completed. Since this API only returns the first page of results, subsequent pages can be fetched via the same mechanism (GetQueryResults)."}, "kind": {"type": "string", "description": "The resource type.", "default": "bigquery#queryResponse"}, "numDmlAffectedRows": {"type": "string", "description": "[Output-only] The number of rows affected by a DML statement. Present only for DML statements INSERT, UPDATE or DELETE.", "format": "int64"}, "pageToken": {"type": "string", "description": "A token used for paging results."}, "rows": {"type": "array", "description": "An object with as many results as can be contained within the maximum permitted reply size. To get any additional rows, you can call GetQueryResults and specify the jobReference returned above.", "items": {"$ref": "TableRow"}}, "schema": {"$ref": "TableSchema", "description": "The schema of the results. Present only when the query completes successfully."}, "sessionInfo": {"$ref": "SessionInfo", "description": "[Output-only] [Preview] Information of the session if this job is part of one."}, "totalBytesProcessed": {"type": "string", "description": "The total number of bytes processed for this query. If this query was a dry run, this is the number of bytes that would be processed if the query were run.", "format": "int64"}, "totalRows": {"type": "string", "description": "The total number of rows in the complete query result set, which can be more than the number of rows in this single page of results.", "format": "uint64"}}}, "QueryTimelineSample": {"id": "QueryTimelineSample", "type": "object", "properties": {"activeUnits": {"type": "string", "description": "Total number of units currently being processed by workers. This does not correspond directly to slot usage. This is the largest value observed since the last sample.", "format": "int64"}, "completedUnits": {"type": "string", "description": "Total parallel units of work completed by this query.", "format": "int64"}, "elapsedMs": {"type": "string", "description": "Milliseconds elapsed since the start of query execution.", "format": "int64"}, "estimatedRunnableUnits": {"type": "string", "description": "Units of work that can be scheduled immediately. Providing additional slots for these units of work will speed up the query, provided no other query in the reservation needs additional slots.", "format": "int64"}, "pendingUnits": {"type": "string", "description": "Total units of work remaining for the query. This number can be revised (increased or decreased) while the query is running.", "format": "int64"}, "totalSlotMs": {"type": "string", "description": "Cumulative slot-ms consumed by the query.", "format": "int64"}}}, "RangePartitioning": {"id": "RangePartitioning", "type": "object", "properties": {"field": {"type": "string", "description": "[TrustedTester] [Required] The table is partitioned by this field. The field must be a top-level NULLABLE/REQUIRED field. The only supported type is INTEGER/INT64."}, "range": {"type": "object", "description": "[TrustedTester] [Required] Defines the ranges for range partitioning.", "properties": {"end": {"type": "string", "description": "[TrustedTester] [Required] The end of range partitioning, exclusive.", "format": "int64"}, "interval": {"type": "string", "description": "[TrustedTester] [Required] The width of each interval.", "format": "int64"}, "start": {"type": "string", "description": "[TrustedTester] [Required] The start of range partitioning, inclusive.", "format": "int64"}}}}}, "RoutineReference": {"id": "RoutineReference", "type": "object", "properties": {"datasetId": {"type": "string", "description": "Required. The ID of the dataset containing this routine."}, "projectId": {"type": "string", "description": "Required. The ID of the project containing this routine."}, "routineId": {"type": "string", "description": "Required. The ID of the routine. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 256 characters."}}}, "RowAccessPolicyReference": {"id": "RowAccessPolicyReference", "type": "object", "properties": {"datasetId": {"type": "string", "description": "Required. The ID of the dataset containing this row access policy."}, "policyId": {"type": "string", "description": "Required. The ID of the row access policy. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 256 characters."}, "projectId": {"type": "string", "description": "Required. The ID of the project containing this row access policy."}, "tableId": {"type": "string", "description": "Required. The ID of the table containing this row access policy."}}}, "RowLevelSecurityStatistics": {"id": "RowLevelSecurityStatistics", "type": "object", "properties": {"rowLevelSecurityApplied": {"type": "boolean", "description": "[Output-only] [Preview] Whether any accessed data was protected by row access policies."}}}, "ScriptStackFrame": {"id": "ScriptStackFrame", "type": "object", "properties": {"endColumn": {"type": "integer", "description": "[Output-only] One-based end column.", "format": "int32"}, "endLine": {"type": "integer", "description": "[Output-only] One-based end line.", "format": "int32"}, "procedureId": {"type": "string", "description": "[Output-only] Name of the active procedure, empty if in a top-level script."}, "startColumn": {"type": "integer", "description": "[Output-only] One-based start column.", "format": "int32"}, "startLine": {"type": "integer", "description": "[Output-only] One-based start line.", "format": "int32"}, "text": {"type": "string", "description": "[Output-only] Text of the current statement/expression."}}}, "ScriptStatistics": {"id": "ScriptStatistics", "type": "object", "properties": {"evaluationKind": {"type": "string", "description": "[Output-only] Whether this child job was a statement or expression."}, "stackFrames": {"type": "array", "description": "Stack trace showing the line/column/procedure name of each frame on the stack at the point where the current evaluation happened. The leaf frame is first, the primary script is last. Never empty.", "items": {"$ref": "ScriptStackFrame"}}}}, "SearchStatistics": {"id": "SearchStatistics", "type": "object", "properties": {"indexUnusedReasons": {"type": "array", "description": "When index_usage_mode is UNUSED or PARTIALLY_USED, this field explains why index was not used in all or part of the search query. If index_usage_mode is FULLLY_USED, this field is not populated.", "items": {"$ref": "IndexUnusedReason"}}, "indexUsageMode": {"type": "string", "description": "Specifies index usage mode for the query."}}}, "SessionInfo": {"id": "SessionInfo", "type": "object", "properties": {"sessionId": {"type": "string", "description": "[Output-only] // [Preview] Id of the session."}}}, "SnapshotDefinition": {"id": "SnapshotDefinition", "type": "object", "properties": {"baseTableReference": {"$ref": "TableReference", "description": "[Required] Reference describing the ID of the table that was snapshot."}, "snapshotTime": {"type": "string", "description": "[Required] The time at which the base table was snapshot. This value is reported in the JSON response using RFC3339 format.", "format": "date-time"}}}, "SparkLoggingInfo": {"id": "SparkLoggingInfo", "type": "object", "properties": {"project_id": {"type": "string", "description": "[Output-only] Project ID used for logging"}, "resource_type": {"type": "string", "description": "[Output-only] Resource type used for logging"}}}, "SparkStatistics": {"id": "SparkStatistics", "type": "object", "properties": {"endpoints": {"type": "object", "description": "[Output-only] Endpoints generated for the Spark job.", "additionalProperties": {"type": "string"}}, "loggingInfo": {"$ref": "SparkLoggingInfo", "description": "[Output-only] Logging info is used to generate a link to Cloud Logging."}, "sparkJobId": {"type": "string", "description": "[Output-only] Spark job id if a Spark job is created successfully."}, "sparkJobLocation": {"type": "string", "description": "[Output-only] Location where the Spark job is executed."}}}, "Streamingbuffer": {"id": "Streamingbuffer", "type": "object", "properties": {"estimatedBytes": {"type": "string", "description": "[Output-only] A lower-bound estimate of the number of bytes currently in the streaming buffer.", "format": "uint64"}, "estimatedRows": {"type": "string", "description": "[Output-only] A lower-bound estimate of the number of rows currently in the streaming buffer.", "format": "uint64"}, "oldestEntryTime": {"type": "string", "description": "[Output-only] Contains the timestamp of the oldest entry in the streaming buffer, in milliseconds since the epoch, if the streaming buffer is available.", "format": "uint64"}}}, "Table": {"id": "Table", "type": "object", "properties": {"biglakeConfiguration": {"$ref": "BigLakeConfiguration", "description": "[Optional] Specifies the configuration of a BigLake managed table."}, "cloneDefinition": {"$ref": "CloneDefinition", "description": "[Output-only] Clone definition."}, "clustering": {"$ref": "Clustering", "description": "[Beta] Clustering specification for the table. Must be specified with partitioning, data in the table will be first partitioned and subsequently clustered."}, "creationTime": {"type": "string", "description": "[Output-only] The time when this table was created, in milliseconds since the epoch.", "format": "int64"}, "defaultCollation": {"type": "string", "description": "[Output-only] The default collation of the table."}, "defaultRoundingMode": {"type": "string", "description": "[Output-only] The default rounding mode of the table."}, "description": {"type": "string", "description": "[Optional] A user-friendly description of this table."}, "encryptionConfiguration": {"$ref": "EncryptionConfiguration", "description": "Custom encryption configuration (e.g., Cloud KMS keys)."}, "etag": {"type": "string", "description": "[Output-only] A hash of the table metadata. Used to ensure there were no concurrent modifications to the resource when attempting an update. Not guaranteed to change when the table contents or the fields numRows, numBytes, numLongTermBytes or lastModifiedTime change."}, "expirationTime": {"type": "string", "description": "[Optional] The time when this table expires, in milliseconds since the epoch. If not present, the table will persist indefinitely. Expired tables will be deleted and their storage reclaimed. The defaultTableExpirationMs property of the encapsulating dataset can be used to set a default expirationTime on newly created tables.", "format": "int64"}, "externalDataConfiguration": {"$ref": "ExternalDataConfiguration", "description": "[Optional] Describes the data format, location, and other properties of a table stored outside of BigQuery. By defining these properties, the data source can then be queried as if it were a standard BigQuery table."}, "friendlyName": {"type": "string", "description": "[Optional] A descriptive name for this table."}, "id": {"type": "string", "description": "[Output-only] An opaque ID uniquely identifying the table."}, "kind": {"type": "string", "description": "[Output-only] The type of the resource.", "default": "bigquery#table"}, "labels": {"type": "object", "description": "The labels associated with this table. You can use these to organize and group your tables. Label keys and values can be no longer than 63 characters, can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter and each label in the list must have a different key.", "additionalProperties": {"type": "string"}}, "lastModifiedTime": {"type": "string", "description": "[Output-only] The time when this table was last modified, in milliseconds since the epoch.", "format": "uint64"}, "location": {"type": "string", "description": "[Output-only] The geographic location where the table resides. This value is inherited from the dataset."}, "materializedView": {"$ref": "MaterializedViewDefinition", "description": "[Optional] Materialized view definition."}, "maxStaleness": {"type": "string", "description": "[Optional] Max staleness of data that could be returned when table or materialized view is queried (formatted as Google SQL Interval type).", "format": "byte"}, "model": {"$ref": "ModelDefinition", "description": "[Output-only, Beta] Present iff this table represents a ML model. Describes the training information for the model, and it is required to run 'PREDICT' queries."}, "numActiveLogicalBytes": {"type": "string", "description": "[Output-only] Number of logical bytes that are less than 90 days old.", "format": "int64"}, "numActivePhysicalBytes": {"type": "string", "description": "[Output-only] Number of physical bytes less than 90 days old. This data is not kept in real time, and might be delayed by a few seconds to a few minutes.", "format": "int64"}, "numBytes": {"type": "string", "description": "[Output-only] The size of this table in bytes, excluding any data in the streaming buffer.", "format": "int64"}, "numLongTermBytes": {"type": "string", "description": "[Output-only] The number of bytes in the table that are considered \"long-term storage\".", "format": "int64"}, "numLongTermLogicalBytes": {"type": "string", "description": "[Output-only] Number of logical bytes that are more than 90 days old.", "format": "int64"}, "numLongTermPhysicalBytes": {"type": "string", "description": "[Output-only] Number of physical bytes more than 90 days old. This data is not kept in real time, and might be delayed by a few seconds to a few minutes.", "format": "int64"}, "numPartitions": {"type": "string", "description": "[Output-only] The number of partitions present in the table or materialized view. This data is not kept in real time, and might be delayed by a few seconds to a few minutes.", "format": "int64"}, "numPhysicalBytes": {"type": "string", "description": "[Output-only] [TrustedTester] The physical size of this table in bytes, excluding any data in the streaming buffer. This includes compression and storage used for time travel.", "format": "int64"}, "numRows": {"type": "string", "description": "[Output-only] The number of rows of data in this table, excluding any data in the streaming buffer.", "format": "uint64"}, "numTimeTravelPhysicalBytes": {"type": "string", "description": "[Output-only] Number of physical bytes used by time travel storage (deleted or changed data). This data is not kept in real time, and might be delayed by a few seconds to a few minutes.", "format": "int64"}, "numTotalLogicalBytes": {"type": "string", "description": "[Output-only] Total number of logical bytes in the table or materialized view.", "format": "int64"}, "numTotalPhysicalBytes": {"type": "string", "description": "[Output-only] The physical size of this table in bytes. This also includes storage used for time travel. This data is not kept in real time, and might be delayed by a few seconds to a few minutes.", "format": "int64"}, "rangePartitioning": {"$ref": "RangePartitioning", "description": "[TrustedTester] Range partitioning specification for this table. Only one of timePartitioning and rangePartitioning should be specified."}, "requirePartitionFilter": {"type": "boolean", "description": "[Optional] If set to true, queries over this table require a partition filter that can be used for partition elimination to be specified.", "default": "false"}, "schema": {"$ref": "TableSchema", "description": "[Optional] Describes the schema of this table."}, "selfLink": {"type": "string", "description": "[Output-only] A URL that can be used to access this resource again."}, "snapshotDefinition": {"$ref": "SnapshotDefinition", "description": "[Output-only] Snapshot definition."}, "streamingBuffer": {"$ref": "Streamingbuffer", "description": "[Output-only] Contains information regarding this table's streaming buffer, if one is present. This field will be absent if the table is not being streamed to or if there is no data in the streaming buffer."}, "tableConstraints": {"$ref": "TableConstraints", "description": "[Optional] The table constraints on the table."}, "tableReference": {"$ref": "TableReference", "description": "[Required] Reference describing the ID of this table."}, "timePartitioning": {"$ref": "TimePartitioning", "description": "Time-based partitioning specification for this table. Only one of timePartitioning and rangePartitioning should be specified."}, "type": {"type": "string", "description": "[Output-only] Describes the table type. The following values are supported: TABLE: A normal BigQuery table. VIEW: A virtual table defined by a SQL query. SNAPSHOT: An immutable, read-only table that is a copy of another table. [TrustedTester] MATERIALIZED_VIEW: SQL query whose result is persisted. EXTERNAL: A table that references data stored in an external storage system, such as Google Cloud Storage. The default value is TABLE."}, "view": {"$ref": "ViewDefinition", "description": "[Optional] The view definition."}}}, "TableCell": {"id": "TableCell", "type": "object", "properties": {"v": {"type": "any"}}}, "TableConstraints": {"id": "TableConstraints", "type": "object", "properties": {"foreignKeys": {"type": "array", "description": "[Optional] The foreign keys of the tables.", "items": {"type": "object", "properties": {"columnReferences": {"type": "array", "items": {"type": "object", "properties": {"referencedColumn": {"type": "string"}, "referencingColumn": {"type": "string"}}}}, "name": {"type": "string"}, "referencedTable": {"type": "object", "properties": {"datasetId": {"type": "string"}, "projectId": {"type": "string"}, "tableId": {"type": "string"}}}}}}, "primaryKey": {"type": "object", "description": "[Optional] The primary key of the table.", "properties": {"columns": {"type": "array", "items": {"type": "string"}}}}}}, "TableDataInsertAllRequest": {"id": "TableDataInsertAllRequest", "type": "object", "properties": {"ignoreUnknownValues": {"type": "boolean", "description": "[Optional] Accept rows that contain values that do not match the schema. The unknown values are ignored. Default is false, which treats unknown values as errors."}, "kind": {"type": "string", "description": "The resource type of the response.", "default": "bigquery#tableDataInsertAllRequest"}, "rows": {"type": "array", "description": "The rows to insert.", "items": {"type": "object", "properties": {"insertId": {"type": "string", "description": "[Optional] A unique ID for each row. BigQuery uses this property to detect duplicate insertion requests on a best-effort basis."}, "json": {"$ref": "JsonObject", "description": "[Required] A JSON object that contains a row of data. The object's properties and values must match the destination table's schema."}}}}, "skipInvalidRows": {"type": "boolean", "description": "[Optional] Insert all valid rows of a request, even if invalid rows exist. The default value is false, which causes the entire request to fail if any invalid rows exist."}, "templateSuffix": {"type": "string", "description": "If specified, treats the destination table as a base template, and inserts the rows into an instance table named \"{destination}{templateSuffix}\". BigQuery will manage creation of the instance table, using the schema of the base template table. See https://cloud.google.com/bigquery/streaming-data-into-bigquery#template-tables for considerations when working with templates tables."}}}, "TableDataInsertAllResponse": {"id": "TableDataInsertAllResponse", "type": "object", "properties": {"insertErrors": {"type": "array", "description": "An array of errors for rows that were not inserted.", "items": {"type": "object", "properties": {"errors": {"type": "array", "description": "Error information for the row indicated by the index property.", "items": {"$ref": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>"}}, "index": {"type": "integer", "description": "The index of the row that error applies to.", "format": "uint32"}}}}, "kind": {"type": "string", "description": "The resource type of the response.", "default": "bigquery#tableDataInsertAllResponse"}}}, "TableDataList": {"id": "TableDataList", "type": "object", "properties": {"etag": {"type": "string", "description": "A hash of this page of results."}, "kind": {"type": "string", "description": "The resource type of the response.", "default": "bigquery#tableDataList"}, "pageToken": {"type": "string", "description": "A token used for paging results. Providing this token instead of the startIndex parameter can help you retrieve stable results when an underlying table is changing."}, "rows": {"type": "array", "description": "Rows of results.", "items": {"$ref": "TableRow"}}, "totalRows": {"type": "string", "description": "The total number of rows in the complete table.", "format": "int64"}}}, "TableFieldSchema": {"id": "TableFieldSchema", "type": "object", "properties": {"categories": {"type": "object", "description": "[Optional] The categories attached to this field, used for field-level access control.", "properties": {"names": {"type": "array", "description": "A list of category resource names. For example, \"projects/1/taxonomies/2/categories/3\". At most 5 categories are allowed.", "items": {"type": "string"}}}}, "collation": {"type": "string", "description": "Optional. Collation specification of the field. It only can be set on string type field."}, "defaultValueExpression": {"type": "string", "description": "Optional. A SQL expression to specify the default value for this field. It can only be set for top level fields (columns). You can use struct or array expression to specify default value for the entire struct or array. The valid SQL expressions are: - Literals for all data types, including STRUCT and ARRAY. - Following functions: - CURRENT_TIMESTAMP - CURRENT_TIME - CURRENT_DATE - CURRENT_DATETIME - GENERATE_UUID - RAND - SESSION_USER - ST_GEOGPOINT - Struct or array composed with the above allowed functions, for example, [CURRENT_DATE(), DATE '2020-01-01']"}, "description": {"type": "string", "description": "[Optional] The field description. The maximum length is 1,024 characters."}, "fields": {"type": "array", "description": "[Optional] Describes the nested schema fields if the type property is set to RECORD.", "items": {"$ref": "TableFieldSchema"}}, "maxLength": {"type": "string", "description": "[Optional] Maximum length of values of this field for STRINGS or BYTES. If max_length is not specified, no maximum length constraint is imposed on this field. If type = \"STRING\", then max_length represents the maximum UTF-8 length of strings in this field. If type = \"BYTES\", then max_length represents the maximum number of bytes in this field. It is invalid to set this field if type ≠ \"STRING\" and ≠ \"BYTES\".", "format": "int64"}, "mode": {"type": "string", "description": "[Optional] The field mode. Possible values include NULL<PERSON>LE, REQUIRED and REPEATED. The default value is NULLABLE."}, "name": {"type": "string", "description": "[Required] The field name. The name must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_), and must start with a letter or underscore. The maximum length is 300 characters."}, "policyTags": {"type": "object", "properties": {"names": {"type": "array", "description": "A list of category resource names. For example, \"projects/1/location/eu/taxonomies/2/policyTags/3\". At most 1 policy tag is allowed.", "items": {"type": "string"}}}}, "precision": {"type": "string", "description": "[Optional] Precision (maximum number of total digits in base 10) and scale (maximum number of digits in the fractional part in base 10) constraints for values of this field for NUMERIC or BIGNUMERIC. It is invalid to set precision or scale if type ≠ \"NUMERIC\" and ≠ \"BIG<PERSON>MERI<PERSON>\". If precision and scale are not specified, no value range constraint is imposed on this field insofar as values are permitted by the type. Values of this NUMERIC or BIGNUMERIC field must be in this range when: - Precision (P) and scale (S) are specified: [-10P-S + 10-S, 10P-S - 10-S] - Precision (P) is specified but not scale (and thus scale is interpreted to be equal to zero): [-10P + 1, 10P - 1]. Acceptable values for precision and scale if both are specified: - If type = \"NUMERIC\": 1 ≤ precision - scale ≤ 29 and 0 ≤ scale ≤ 9. - If type = \"BIGNUMERIC\": 1 ≤ precision - scale ≤ 38 and 0 ≤ scale ≤ 38. Acceptable values for precision if only precision is specified but not scale (and thus scale is interpreted to be equal to zero): - If type = \"NUMERIC\": 1 ≤ precision ≤ 29. - If type = \"BIG<PERSON><PERSON>RI<PERSON>\": 1 ≤ precision ≤ 38. If scale is specified but not precision, then it is invalid.", "format": "int64"}, "rangeElementType": {"type": "object", "description": "Optional. The subtype of the RANGE, if the type of this field is RANGE. If the type is RANGE, this field is required. Possible values for the field element type of a RANGE include: - DATE - DATETIME - TIMESTAMP", "properties": {"type": {"type": "string", "description": "The field element type of a RANGE"}}}, "roundingMode": {"type": "string", "description": "Optional. Rounding Mode specification of the field. It only can be set on NUMERIC or BIGNUMERIC type fields."}, "scale": {"type": "string", "description": "[Optional] See documentation for precision.", "format": "int64"}, "type": {"type": "string", "description": "[Required] The field data type. Possible values include STRING, BYTES, INTEGER, INT64 (same as INTEGER), FLOAT, FLOAT64 (same as FLOAT), NUMERIC, BIGNUMERIC, BOOLEAN, BOOL (same as BOOLEAN), TIM<PERSON><PERSON>MP, DATE, TIME, DATETIM<PERSON>, INTERVAL, RECORD (where RECORD indicates that the field contains a nested schema) or STRUCT (same as RECORD)."}}}, "TableList": {"id": "TableList", "type": "object", "properties": {"etag": {"type": "string", "description": "A hash of this page of results."}, "kind": {"type": "string", "description": "The type of list.", "default": "bigquery#tableList"}, "nextPageToken": {"type": "string", "description": "A token to request the next page of results."}, "tables": {"type": "array", "description": "Tables in the requested dataset.", "items": {"type": "object", "properties": {"clustering": {"$ref": "Clustering", "description": "[Beta] Clustering specification for this table, if configured."}, "creationTime": {"type": "string", "description": "The time when this table was created, in milliseconds since the epoch.", "format": "int64"}, "expirationTime": {"type": "string", "description": "[Optional] The time when this table expires, in milliseconds since the epoch. If not present, the table will persist indefinitely. Expired tables will be deleted and their storage reclaimed.", "format": "int64"}, "friendlyName": {"type": "string", "description": "The user-friendly name for this table."}, "id": {"type": "string", "description": "An opaque ID of the table"}, "kind": {"type": "string", "description": "The resource type.", "default": "bigquery#table"}, "labels": {"type": "object", "description": "The labels associated with this table. You can use these to organize and group your tables.", "additionalProperties": {"type": "string"}}, "rangePartitioning": {"$ref": "RangePartitioning", "description": "The range partitioning specification for this table, if configured."}, "tableReference": {"$ref": "TableReference", "description": "A reference uniquely identifying the table."}, "timePartitioning": {"$ref": "TimePartitioning", "description": "The time-based partitioning specification for this table, if configured."}, "type": {"type": "string", "description": "The type of table. Possible values are: TABLE, VIEW."}, "view": {"type": "object", "description": "Additional details for a view.", "properties": {"useLegacySql": {"type": "boolean", "description": "True if view is defined in legacy SQL dialect, false if in standard SQL."}}}}}}, "totalItems": {"type": "integer", "description": "The total number of tables in the dataset.", "format": "int32"}}}, "TableReference": {"id": "TableReference", "type": "object", "properties": {"datasetId": {"type": "string", "description": "[Required] The ID of the dataset containing this table.", "annotations": {"required": ["bigquery.tables.update"]}}, "projectId": {"type": "string", "description": "[Required] The ID of the project containing this table.", "annotations": {"required": ["bigquery.tables.update"]}}, "tableId": {"type": "string", "description": "[Required] The ID of the table. The ID must contain only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is 1,024 characters.", "annotations": {"required": ["bigquery.tables.update"]}}}}, "TableRow": {"id": "TableRow", "type": "object", "properties": {"f": {"type": "array", "description": "Represents a single row in the result set, consisting of one or more fields.", "items": {"$ref": "TableCell"}}}}, "TableSchema": {"id": "TableSchema", "type": "object", "properties": {"fields": {"type": "array", "description": "Describes the fields in a table.", "items": {"$ref": "TableFieldSchema"}}}}, "TimePartitioning": {"id": "TimePartitioning", "type": "object", "properties": {"expirationMs": {"type": "string", "description": "[Optional] Number of milliseconds for which to keep the storage for partitions in the table. The storage in a partition will have an expiration time of its partition time plus this value.", "format": "int64"}, "field": {"type": "string", "description": "[Beta] [Optional] If not set, the table is partitioned by pseudo column, referenced via either '_PARTITIONTIME' as TIMESTAMP type, or '_PARTITIONDATE' as DATE type. If field is specified, the table is instead partitioned by this field. The field must be a top-level TIMESTAMP or DATE field. Its mode must be NULLABLE or REQUIRED."}, "requirePartitionFilter": {"type": "boolean"}, "type": {"type": "string", "description": "[Required] The supported types are DAY, HOUR, MONTH, and YEAR, which will generate one partition per day, hour, month, and year, respectively. When the type is not specified, the default behavior is DAY."}}}, "TransactionInfo": {"id": "TransactionInfo", "type": "object", "properties": {"transactionId": {"type": "string", "description": "[Output-only] // [Alpha] Id of the transaction."}}}, "UserDefinedFunctionResource": {"id": "UserDefinedFunctionResource", "type": "object", "description": "This is used for defining User Defined Function (UDF) resources only when using legacy SQL. Users of Standard SQL should leverage either DDL (e.g. CREATE [TEMPORARY] FUNCTION ... ) or the Routines API to define UDF resources. For additional information on migrating, see: https://cloud.google.com/bigquery/docs/reference/standard-sql/migrating-from-legacy-sql#differences_in_user-defined_javascript_functions", "properties": {"inlineCode": {"type": "string", "description": "[Pick one] An inline resource that contains code for a user-defined function (UDF). Providing a inline code resource is equivalent to providing a URI for a file containing the same code."}, "resourceUri": {"type": "string", "description": "[Pick one] A code resource to load from a Google Cloud Storage URI (gs://bucket/path)."}}}, "ViewDefinition": {"id": "ViewDefinition", "type": "object", "properties": {"query": {"type": "string", "description": "[Required] A query that BigQuery executes when the view is referenced."}, "useExplicitColumnNames": {"type": "boolean", "description": "True if the column names are explicitly specified. For example by using the 'CREATE VIEW v(c1, c2) AS ...' syntax. Can only be set using BigQuery's standard SQL: https://cloud.google.com/bigquery/sql-reference/"}, "useLegacySql": {"type": "boolean", "description": "Specifies whether to use BigQuery's legacy SQL for this view. The default value is true. If set to false, the view will use BigQuery's standard SQL: https://cloud.google.com/bigquery/sql-reference/ Queries and views that reference this view must use the same flag value."}, "userDefinedFunctionResources": {"type": "array", "description": "Describes user-defined function resources used in the query.", "items": {"$ref": "UserDefinedFunctionResource"}}}}, "Model": {"id": "Model", "type": "object", "properties": {"etag": {"description": "Output only. A hash of this resource.", "readOnly": true, "type": "string"}, "modelReference": {"description": "Required. Unique identifier for this model.", "$ref": "ModelReference"}, "creationTime": {"description": "Output only. The time when this model was created, in millisecs since the epoch.", "readOnly": true, "type": "string", "format": "int64"}, "lastModifiedTime": {"description": "Output only. The time when this model was last modified, in millisecs since the epoch.", "readOnly": true, "type": "string", "format": "int64"}, "description": {"description": "Optional. A user-friendly description of this model.", "type": "string"}, "friendlyName": {"description": "Optional. A descriptive name for this model.", "type": "string"}, "labels": {"description": "The labels associated with this model. You can use these to organize and group your models. Label keys and values can be no longer than 63 characters, can only contain lowercase letters, numeric characters, underscores and dashes. International characters are allowed. Label values are optional. Label keys must start with a letter and each label in the list must have a different key.", "type": "object", "additionalProperties": {"type": "string"}}, "expirationTime": {"description": "Optional. The time when this model expires, in milliseconds since the epoch. If not present, the model will persist indefinitely. Expired models will be deleted and their storage reclaimed. The defaultTableExpirationMs property of the encapsulating dataset can be used to set a default expirationTime on newly created models.", "type": "string", "format": "int64"}, "location": {"description": "Output only. The geographic location where the model resides. This value is inherited from the dataset.", "readOnly": true, "type": "string"}, "encryptionConfiguration": {"description": "Custom encryption configuration (e.g., Cloud KMS keys). This shows the encryption configuration of the model data while stored in BigQuery storage. This field can be used with PatchModel to update encryption key for an already encrypted model.", "$ref": "EncryptionConfiguration"}, "modelType": {"description": "Output only. Type of the model resource.", "readOnly": true, "type": "string", "enumDescriptions": ["", "Linear regression model.", "Logistic regression based classification model.", "K-means clustering model.", "Matrix factorization model.", "DNN classifier model.", "An imported TensorFlow model.", "DNN regressor model.", "An imported XGBoost model.", "Boosted tree regressor model.", "Boosted tree classifier model.", "ARIMA model.", "AutoML Tables regression model.", "AutoML Tables classification model.", "Prinpical Component Analysis model.", "Wide-and-deep classifier model.", "Wide-and-deep regressor model.", "Autoencoder model.", "New name for the ARIMA model.", "ARIMA with external regressors.", "Random forest regressor model.", "Random forest classifier model.", "An imported TensorFlow Lite model.", "An imported ONNX model."], "enum": ["MODEL_TYPE_UNSPECIFIED", "LINEAR_REGRESSION", "LOGISTIC_REGRESSION", "KMEANS", "MATRIX_FACTORIZATION", "DNN_CLASSIFIER", "TENSORFLOW", "DNN_REGRESSOR", "XGBOOST", "BOOSTED_TREE_REGRESSOR", "BOOSTED_TREE_CLASSIFIER", "ARIMA", "AUTOML_REGRESSOR", "AUTOML_CLASSIFIER", "PCA", "DNN_LINEAR_COMBINED_CLASSIFIER", "DNN_LINEAR_COMBINED_REGRESSOR", "AUTOENCODER", "ARIMA_PLUS", "ARIMA_PLUS_XREG", "RANDOM_FOREST_REGRESSOR", "RANDOM_FOREST_CLASSIFIER", "TENSORFLOW_LITE", "ONNX"]}, "trainingRuns": {"description": "Information for all training runs in increasing order of start_time.", "type": "array", "items": {"$ref": "TrainingRun"}}, "featureColumns": {"description": "Output only. Input feature columns for the model inference. If the model is trained with TRANSFORM clause, these are the input of the TRANSFORM clause.", "readOnly": true, "type": "array", "items": {"$ref": "StandardSqlField"}}, "labelColumns": {"description": "Output only. Label columns that were used to train this model. The output of the model will have a \"predicted_\" prefix to these columns.", "readOnly": true, "type": "array", "items": {"$ref": "StandardSqlField"}}, "transformColumns": {"description": "Output only. This field will be populated if a TRANSFORM clause was used to train a model. TRANSFORM clause (if used) takes feature_columns as input and outputs transform_columns. transform_columns then are used to train the model.", "readOnly": true, "type": "array", "items": {"$ref": "TransformColumn"}}, "hparamSearchSpaces": {"description": "Output only. All hyperparameter search spaces in this model.", "readOnly": true, "$ref": "HparamSearchSpaces"}, "bestTrialId": {"description": "The best trial_id across all training runs.", "deprecated": true, "type": "string", "format": "int64"}, "defaultTrialId": {"description": "Output only. The default trial_id to use in TVFs when the trial_id is not passed in. For single-objective [hyperparameter tuning](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) models, this is the best trial ID. For multi-objective [hyperparameter tuning](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) models, this is the smallest trial ID among all Pareto optimal trials.", "readOnly": true, "type": "string", "format": "int64"}, "hparamTrials": {"description": "Output only. Trials of a [hyperparameter tuning](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) model sorted by trial_id.", "readOnly": true, "type": "array", "items": {"$ref": "HparamTuningTrial"}}, "optimalTrialIds": {"description": "Output only. For single-objective [hyperparameter tuning](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) models, it only contains the best trial. For multi-objective [hyperparameter tuning](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) models, it contains all Pareto optimal trials sorted by trial_id.", "readOnly": true, "type": "array", "items": {"type": "string", "format": "int64"}}, "remoteModelInfo": {"description": "Output only. Remote model info", "readOnly": true, "$ref": "RemoteModelInfo"}}}, "TrainingRun": {"id": "TrainingRun", "description": "Information about a single training query run for the model.", "type": "object", "properties": {"trainingOptions": {"description": "Output only. Options that were used for this training run, includes user specified and default options that were used.", "readOnly": true, "$ref": "TrainingOptions"}, "trainingStartTime": {"description": "Output only. The start time of this training run, in milliseconds since epoch.", "readOnly": true, "deprecated": true, "type": "string", "format": "int64"}, "startTime": {"description": "Output only. The start time of this training run.", "readOnly": true, "type": "string", "format": "google-datetime"}, "results": {"description": "Output only. Output of each iteration run, results.size() <= max_iterations.", "readOnly": true, "type": "array", "items": {"$ref": "IterationResult"}}, "evaluationMetrics": {"description": "Output only. The evaluation metrics over training/eval data that were computed at the end of training.", "readOnly": true, "$ref": "EvaluationMetrics"}, "dataSplitResult": {"description": "Output only. Data split result of the training run. Only set when the input data is actually split.", "readOnly": true, "$ref": "DataSplitResult"}, "modelLevelGlobalExplanation": {"description": "Output only. Global explanation contains the explanation of top features on the model level. Applies to both regression and classification models.", "readOnly": true, "$ref": "GlobalExplanation"}, "classLevelGlobalExplanations": {"description": "Output only. Global explanation contains the explanation of top features on the class level. Applies to classification models only.", "readOnly": true, "type": "array", "items": {"$ref": "GlobalExplanation"}}, "vertexAiModelId": {"description": "The model id in the [Vertex AI Model Registry](https://cloud.google.com/vertex-ai/docs/model-registry/introduction) for this training run.", "type": "string"}, "vertexAiModelVersion": {"description": "Output only. The model version in the [Vertex AI Model Registry](https://cloud.google.com/vertex-ai/docs/model-registry/introduction) for this training run.", "readOnly": true, "type": "string"}}}, "TrainingOptions": {"id": "TrainingOptions", "description": "Options used in model training.", "type": "object", "properties": {"maxIterations": {"description": "The maximum number of iterations in training. Used only for iterative training algorithms.", "type": "string", "format": "int64"}, "lossType": {"description": "Type of loss function used during training run.", "type": "string", "enumDescriptions": ["", "Mean squared loss, used for linear regression.", "Mean log loss, used for logistic regression."], "enum": ["LOSS_TYPE_UNSPECIFIED", "MEAN_SQUARED_LOSS", "MEAN_LOG_LOSS"]}, "learnRate": {"description": "Learning rate in training. Used only for iterative training algorithms.", "type": "number", "format": "double"}, "l1Regularization": {"description": "L1 regularization coefficient.", "type": "number", "format": "double"}, "l2Regularization": {"description": "L2 regularization coefficient.", "type": "number", "format": "double"}, "minRelativeProgress": {"description": "When early_stop is true, stops training when accuracy improvement is less than 'min_relative_progress'. Used only for iterative training algorithms.", "type": "number", "format": "double"}, "warmStart": {"description": "Whether to train a model from the last checkpoint.", "type": "boolean"}, "earlyStop": {"description": "Whether to stop early when the loss doesn't improve significantly any more (compared to min_relative_progress). Used only for iterative training algorithms.", "type": "boolean"}, "inputLabelColumns": {"description": "Name of input label columns in training data.", "type": "array", "items": {"type": "string"}}, "dataSplitMethod": {"description": "The data split type for training and evaluation, e.g. RANDOM.", "type": "string", "enumDescriptions": ["", "Splits data randomly.", "Splits data with the user provided tags.", "Splits data sequentially.", "Data split will be skipped.", "Splits data automatically: Uses NO_SPLIT if the data size is small. Otherwise uses RANDOM."], "enum": ["DATA_SPLIT_METHOD_UNSPECIFIED", "RANDOM", "CUSTOM", "SEQUENTIAL", "NO_SPLIT", "AUTO_SPLIT"]}, "dataSplitEvalFraction": {"description": "The fraction of evaluation data over the whole input data. The rest of data will be used as training data. The format should be double. Accurate to two decimal places. Default value is 0.2.", "type": "number", "format": "double"}, "dataSplitColumn": {"description": "The column to split data with. This column won't be used as a feature. 1. When data_split_method is CUSTOM, the corresponding column should be boolean. The rows with true value tag are eval data, and the false are training data. 2. When data_split_method is SEQ, the first DATA_SPLIT_EVAL_FRACTION rows (from smallest to largest) in the corresponding column are used as training data, and the rest are eval data. It respects the order in Orderable data types: https://cloud.google.com/bigquery/docs/reference/standard-sql/data-types#data-type-properties", "type": "string"}, "learnRateStrategy": {"description": "The strategy to determine learn rate for the current iteration.", "type": "string", "enumDescriptions": ["", "Use line search to determine learning rate.", "Use a constant learning rate."], "enum": ["LEARN_RATE_STRATEGY_UNSPECIFIED", "LINE_SEARCH", "CONSTANT"]}, "initialLearnRate": {"description": "Specifies the initial learning rate for the line search learn rate strategy.", "type": "number", "format": "double"}, "labelClassWeights": {"description": "Weights associated with each label class, for rebalancing the training data. Only applicable for classification models.", "type": "object", "additionalProperties": {"type": "number", "format": "double"}}, "userColumn": {"description": "User column specified for matrix factorization models.", "type": "string"}, "itemColumn": {"description": "Item column specified for matrix factorization models.", "type": "string"}, "distanceType": {"description": "Distance type for clustering models.", "type": "string", "enumDescriptions": ["", "Eculidean distance.", "Cosine distance."], "enum": ["DISTANCE_TYPE_UNSPECIFIED", "EUCLIDEAN", "COSINE"]}, "numClusters": {"description": "Number of clusters for clustering models.", "type": "string", "format": "int64"}, "modelUri": {"description": "Google Cloud Storage URI from which the model was imported. Only applicable for imported models.", "type": "string"}, "optimizationStrategy": {"description": "Optimization strategy for training linear regression models.", "type": "string", "enumDescriptions": ["", "Uses an iterative batch gradient descent algorithm.", "Uses a normal equation to solve linear regression problem."], "enum": ["OPTIMIZATION_STRATEGY_UNSPECIFIED", "BATCH_GRADIENT_DESCENT", "NORMAL_EQUATION"]}, "hiddenUnits": {"description": "Hidden units for dnn models.", "type": "array", "items": {"type": "string", "format": "int64"}}, "batchSize": {"description": "Batch size for dnn models.", "type": "string", "format": "int64"}, "dropout": {"description": "Dropout probability for dnn models.", "type": "number", "format": "double"}, "maxTreeDepth": {"description": "Maximum depth of a tree for boosted tree models.", "type": "string", "format": "int64"}, "subsample": {"description": "Subsample fraction of the training data to grow tree to prevent overfitting for boosted tree models.", "type": "number", "format": "double"}, "minSplitLoss": {"description": "Minimum split loss for boosted tree models.", "type": "number", "format": "double"}, "boosterType": {"description": "Booster type for boosted tree models.", "type": "string", "enumDescriptions": ["Unspecified booster type.", "Gbtree booster.", "Dart booster."], "enum": ["BOOSTER_TYPE_UNSPECIFIED", "GBTREE", "DART"]}, "numParallelTree": {"description": "Number of parallel trees constructed during each iteration for boosted tree models.", "type": "string", "format": "int64"}, "dartNormalizeType": {"description": "Type of normalization algorithm for boosted tree models using dart booster.", "type": "string", "enumDescriptions": ["Unspecified dart normalize type.", "New trees have the same weight of each of dropped trees.", "New trees have the same weight of sum of dropped trees."], "enum": ["DART_NORMALIZE_TYPE_UNSPECIFIED", "TREE", "FOREST"]}, "treeMethod": {"description": "Tree construction algorithm for boosted tree models.", "type": "string", "enumDescriptions": ["Unspecified tree method.", "Use heuristic to choose the fastest method.", "Exact greedy algorithm.", "Approximate greedy algorithm using quantile sketch and gradient histogram.", "Fast histogram optimized approximate greedy algorithm."], "enum": ["TREE_METHOD_UNSPECIFIED", "AUTO", "EXACT", "APPROX", "HIST"]}, "minTreeChildWeight": {"description": "Minimum sum of instance weight needed in a child for boosted tree models.", "type": "string", "format": "int64"}, "colsampleBytree": {"description": "Subsample ratio of columns when constructing each tree for boosted tree models.", "type": "number", "format": "double"}, "colsampleBylevel": {"description": "Subsample ratio of columns for each level for boosted tree models.", "type": "number", "format": "double"}, "colsampleBynode": {"description": "Subsample ratio of columns for each node(split) for boosted tree models.", "type": "number", "format": "double"}, "numFactors": {"description": "Num factors specified for matrix factorization models.", "type": "string", "format": "int64"}, "feedbackType": {"description": "Feedback type that specifies which algorithm to run for matrix factorization.", "type": "string", "enumDescriptions": ["", "Use weighted-als for implicit feedback problems.", "Use nonweighted-als for explicit feedback problems."], "enum": ["FEEDBACK_TYPE_UNSPECIFIED", "IMPLICIT", "EXPLICIT"]}, "walsAlpha": {"description": "Hyperparameter for matrix factoration when implicit feedback type is specified.", "type": "number", "format": "double"}, "kmeansInitializationMethod": {"description": "The method used to initialize the centroids for kmeans algorithm.", "type": "string", "enumDescriptions": ["Unspecified initialization method.", "Initializes the centroids randomly.", "Initializes the centroids using data specified in kmeans_initialization_column.", "Initializes with kmeans++."], "enum": ["KMEANS_INITIALIZATION_METHOD_UNSPECIFIED", "RANDOM", "CUSTOM", "KMEANS_PLUS_PLUS"]}, "kmeansInitializationColumn": {"description": "The column used to provide the initial centroids for kmeans algorithm when kmeans_initialization_method is CUSTOM.", "type": "string"}, "timeSeriesTimestampColumn": {"description": "Column to be designated as time series timestamp for ARIMA model.", "type": "string"}, "timeSeriesDataColumn": {"description": "Column to be designated as time series data for ARIMA model.", "type": "string"}, "autoArima": {"description": "Whether to enable auto ARIMA or not.", "type": "boolean"}, "nonSeasonalOrder": {"description": "A specification of the non-seasonal part of the ARIMA model: the three components (p, d, q) are the AR order, the degree of differencing, and the MA order.", "$ref": "ArimaOrder"}, "dataFrequency": {"description": "The data frequency of a time series.", "type": "string", "enumDescriptions": ["", "Automatically inferred from timestamps.", "Yearly data.", "Quarterly data.", "Monthly data.", "Weekly data.", "Daily data.", "Hourly data.", "Per-minute data."], "enum": ["DATA_FREQUENCY_UNSPECIFIED", "AUTO_FREQUENCY", "YEARLY", "QUARTERLY", "MONTHLY", "WEEKLY", "DAILY", "HOURLY", "PER_MINUTE"]}, "calculatePValues": {"description": "Whether or not p-value test should be computed for this model. Only available for linear and logistic regression models.", "type": "boolean"}, "includeDrift": {"description": "Include drift when fitting an ARIMA model.", "type": "boolean"}, "holidayRegion": {"description": "The geographical region based on which the holidays are considered in time series modeling. If a valid value is specified, then holiday effects modeling is enabled.", "type": "string", "enumDescriptions": ["Holiday region unspecified.", "Global.", "North America.", "Japan and Asia Pacific: Korea, Greater China, India, Australia, and New Zealand.", "Europe, the Middle East and Africa.", "Latin America and the Caribbean.", "United Arab Emirates", "Argentina", "Austria", "Australia", "Belgium", "Brazil", "Canada", "Switzerland", "Chile", "China", "Colombia", "Czechoslovakia", "Czech Republic", "Germany", "Denmark", "Algeria", "Ecuador", "Estonia", "Egypt", "Spain", "Finland", "France", "Great Britain (United Kingdom)", "Greece", "Hong Kong", "Hungary", "Indonesia", "Ireland", "Israel", "India", "Iran", "Italy", "Japan", "Korea (South)", "Latvia", "Morocco", "Mexico", "Malaysia", "Nigeria", "Netherlands", "Norway", "New Zealand", "Peru", "Philippines", "Pakistan", "Poland", "Portugal", "Romania", "Serbia", "Russian Federation", "Saudi Arabia", "Sweden", "Singapore", "Slovenia", "Slovakia", "Thailand", "Turkey", "Taiwan", "Ukraine", "United States", "Venezuela", "Viet Nam", "South Africa"], "enum": ["HOLIDAY_REGION_UNSPECIFIED", "GLOBAL", "NA", "JAPAC", "EMEA", "LAC", "AE", "AR", "AT", "AU", "BE", "BR", "CA", "CH", "CL", "CN", "CO", "CS", "CZ", "DE", "DK", "DZ", "EC", "EE", "EG", "ES", "FI", "FR", "GB", "GR", "HK", "HU", "ID", "IE", "IL", "IN", "IR", "IT", "JP", "KR", "LV", "MA", "MX", "MY", "NG", "NL", "NO", "NZ", "PE", "PH", "PK", "PL", "PT", "RO", "RS", "RU", "SA", "SE", "SG", "SI", "SK", "TH", "TR", "TW", "UA", "US", "VE", "VN", "ZA"]}, "holidayRegions": {"description": "A list of geographical regions that are used for time series modeling.", "type": "array", "items": {"type": "string", "enumDescriptions": ["Holiday region unspecified.", "Global.", "North America.", "Japan and Asia Pacific: Korea, Greater China, India, Australia, and New Zealand.", "Europe, the Middle East and Africa.", "Latin America and the Caribbean.", "United Arab Emirates", "Argentina", "Austria", "Australia", "Belgium", "Brazil", "Canada", "Switzerland", "Chile", "China", "Colombia", "Czechoslovakia", "Czech Republic", "Germany", "Denmark", "Algeria", "Ecuador", "Estonia", "Egypt", "Spain", "Finland", "France", "Great Britain (United Kingdom)", "Greece", "Hong Kong", "Hungary", "Indonesia", "Ireland", "Israel", "India", "Iran", "Italy", "Japan", "Korea (South)", "Latvia", "Morocco", "Mexico", "Malaysia", "Nigeria", "Netherlands", "Norway", "New Zealand", "Peru", "Philippines", "Pakistan", "Poland", "Portugal", "Romania", "Serbia", "Russian Federation", "Saudi Arabia", "Sweden", "Singapore", "Slovenia", "Slovakia", "Thailand", "Turkey", "Taiwan", "Ukraine", "United States", "Venezuela", "Viet Nam", "South Africa"], "enum": ["HOLIDAY_REGION_UNSPECIFIED", "GLOBAL", "NA", "JAPAC", "EMEA", "LAC", "AE", "AR", "AT", "AU", "BE", "BR", "CA", "CH", "CL", "CN", "CO", "CS", "CZ", "DE", "DK", "DZ", "EC", "EE", "EG", "ES", "FI", "FR", "GB", "GR", "HK", "HU", "ID", "IE", "IL", "IN", "IR", "IT", "JP", "KR", "LV", "MA", "MX", "MY", "NG", "NL", "NO", "NZ", "PE", "PH", "PK", "PL", "PT", "RO", "RS", "RU", "SA", "SE", "SG", "SI", "SK", "TH", "TR", "TW", "UA", "US", "VE", "VN", "ZA"]}}, "timeSeriesIdColumn": {"description": "The time series id column that was used during ARIMA model training.", "type": "string"}, "timeSeriesIdColumns": {"description": "The time series id columns that were used during ARIMA model training.", "type": "array", "items": {"type": "string"}}, "horizon": {"description": "The number of periods ahead that need to be forecasted.", "type": "string", "format": "int64"}, "autoArimaMaxOrder": {"description": "The max value of the sum of non-seasonal p and q.", "type": "string", "format": "int64"}, "autoArimaMinOrder": {"description": "The min value of the sum of non-seasonal p and q.", "type": "string", "format": "int64"}, "numTrials": {"description": "Number of trials to run this hyperparameter tuning job.", "type": "string", "format": "int64"}, "maxParallelTrials": {"description": "Maximum number of trials to run in parallel.", "type": "string", "format": "int64"}, "hparamTuningObjectives": {"description": "The target evaluation metrics to optimize the hyperparameters for.", "type": "array", "items": {"type": "string", "enumDescriptions": ["Unspecified evaluation metric.", "Mean absolute error. mean_absolute_error = AVG(ABS(label - predicted))", "Mean squared error. mean_squared_error = AVG(POW(label - predicted, 2))", "Mean squared log error. mean_squared_log_error = AVG(POW(LN(1 + label) - LN(1 + predicted), 2))", "Mean absolute error. median_absolute_error = APPROX_QUANTILES(absolute_error, 2)[OFFSET(1)]", "R^2 score. This corresponds to r2_score in ML.EVALUATE. r_squared = 1 - SUM(squared_error)/(COUNT(label)*VAR_POP(label))", "Explained variance. explained_variance = 1 - VAR_POP(label_error)/VAR_POP(label)", "Precision is the fraction of actual positive predictions that had positive actual labels. For multiclass this is a macro-averaged metric treating each class as a binary classifier.", "Recall is the fraction of actual positive labels that were given a positive prediction. For multiclass this is a macro-averaged metric.", "Accuracy is the fraction of predictions given the correct label. For multiclass this is a globally micro-averaged metric.", "The F1 score is an average of recall and precision. For multiclass this is a macro-averaged metric.", "Logorithmic Loss. For multiclass this is a macro-averaged metric.", "Area Under an ROC Curve. For multiclass this is a macro-averaged metric.", "Davies-Bouldin Index.", "Mean Average Precision.", "Normalized Discounted Cumulative Gain.", "Average Rank."], "enum": ["HPARAM_TUNING_OBJECTIVE_UNSPECIFIED", "MEAN_ABSOLUTE_ERROR", "MEAN_SQUARED_ERROR", "MEAN_SQUARED_LOG_ERROR", "MEDIAN_ABSOLUTE_ERROR", "R_SQUARED", "EXPLAINED_VARIANCE", "PRECISION", "RECALL", "ACCURACY", "F1_SCORE", "LOG_LOSS", "ROC_AUC", "DAVIES_BOULDIN_INDEX", "MEAN_AVERAGE_PRECISION", "NORMALIZED_DISCOUNTED_CUMULATIVE_GAIN", "AVERAGE_RANK"]}}, "decomposeTimeSeries": {"description": "If true, perform decompose time series and save the results.", "type": "boolean"}, "cleanSpikesAndDips": {"description": "If true, clean spikes and dips in the input time series.", "type": "boolean"}, "adjustStepChanges": {"description": "If true, detect step changes and make data adjustment in the input time series.", "type": "boolean"}, "enableGlobalExplain": {"description": "If true, enable global explanation during training.", "type": "boolean"}, "sampledShapleyNumPaths": {"description": "Number of paths for the sampled <PERSON><PERSON><PERSON><PERSON> explain method.", "type": "string", "format": "int64"}, "integratedGradientsNumSteps": {"description": "Number of integral steps for the integrated gradients explain method.", "type": "string", "format": "int64"}, "categoryEncodingMethod": {"description": "Categorical feature encoding method.", "type": "string", "enumDescriptions": ["Unspecified encoding method.", "Applies one-hot encoding.", "Applies label encoding.", "Applies dummy encoding."], "enum": ["ENCODING_METHOD_UNSPECIFIED", "ONE_HOT_ENCODING", "LABEL_ENCODING", "DUMMY_ENCODING"]}, "tfVersion": {"description": "Based on the selected TF version, the corresponding docker image is used to train external models.", "type": "string"}, "colorSpace": {"description": "Enums for color space, used for processing images in Object Table. See more details at https://www.tensorflow.org/io/tutorials/colorspace.", "type": "string", "enumDescriptions": ["Unspecified color space", "RGB", "HSV", "YIQ", "YUV", "GRAYSCALE"], "enum": ["COLOR_SPACE_UNSPECIFIED", "RGB", "HSV", "YIQ", "YUV", "GRAYSCALE"]}, "instanceWeightColumn": {"description": "Name of the instance weight column for training data. This column isn't be used as a feature.", "type": "string"}, "trendSmoothingWindowSize": {"description": "Smoothing window size for the trend component. When a positive value is specified, a center moving average smoothing is applied on the history trend. When the smoothing window is out of the boundary at the beginning or the end of the trend, the first element or the last element is padded to fill the smoothing window before the average is applied.", "type": "string", "format": "int64"}, "timeSeriesLengthFraction": {"description": "The fraction of the interpolated length of the time series that's used to model the time series trend component. All of the time points of the time series are used to model the non-trend component. This training option accelerates modeling training without sacrificing much forecasting accuracy. You can use this option with `minTimeSeriesLength` but not with `maxTimeSeriesLength`.", "type": "number", "format": "double"}, "minTimeSeriesLength": {"description": "The minimum number of time points in a time series that are used in modeling the trend component of the time series. If you use this option you must also set the `timeSeriesLengthFraction` option. This training option ensures that enough time points are available when you use `timeSeriesLengthFraction` in trend modeling. This is particularly important when forecasting multiple time series in a single query using `timeSeriesIdColumn`. If the total number of time points is less than the `minTimeSeriesLength` value, then the query uses all available time points.", "type": "string", "format": "int64"}, "maxTimeSeriesLength": {"description": "The maximum number of time points in a time series that can be used in modeling the trend component of the time series. Don't use this option with the `timeSeriesLengthFraction` or `minTimeSeriesLength` options.", "type": "string", "format": "int64"}, "xgboostVersion": {"description": "User-selected XGBoost versions for training of XGBoost models.", "type": "string"}, "approxGlobalFeatureContrib": {"description": "Whether to use approximate feature contribution method in XGBoost model explanation for global explain.", "type": "boolean"}, "fitIntercept": {"description": "Whether the model should include intercept during model training.", "type": "boolean"}, "numPrincipalComponents": {"description": "Number of principal components to keep in the PCA model. Must be <= the number of features.", "type": "string", "format": "int64"}, "pcaExplainedVarianceRatio": {"description": "The minimum ratio of cumulative explained variance that needs to be given by the PCA model.", "type": "number", "format": "double"}, "scaleFeatures": {"description": "If true, scale the feature values by dividing the feature standard deviation. Currently only apply to PCA.", "type": "boolean"}, "pcaSolver": {"description": "The solver for PCA.", "type": "string", "enumDescriptions": ["", "Full eigen-decoposition.", "Randomized SVD.", "Auto."], "enum": ["UNSPECIFIED", "FULL", "RANDOMIZED", "AUTO"]}, "autoClassWeights": {"description": "Whether to calculate class weights automatically based on the popularity of each label.", "type": "boolean"}, "activationFn": {"description": "Activation function of the neural nets.", "type": "string"}, "optimizer": {"description": "Optimizer used for training the neural nets.", "type": "string"}, "budgetHours": {"description": "Budget in hours for AutoML training.", "type": "number", "format": "double"}, "standardizeFeatures": {"description": "Whether to standardize numerical features. Default to true.", "type": "boolean"}, "l1RegActivation": {"description": "L1 regularization coefficient to activations.", "type": "number", "format": "double"}, "modelRegistry": {"description": "The model registry.", "type": "string", "enumDescriptions": ["", "Vertex AI."], "enum": ["MODEL_REGISTRY_UNSPECIFIED", "VERTEX_AI"]}, "vertexAiModelVersionAliases": {"description": "The version aliases to apply in Vertex AI model registry. Always overwrite if the version aliases exists in a existing model.", "type": "array", "items": {"type": "string"}}}}, "ArimaOrder": {"id": "ArimaOrder", "description": "Arima order, can be used for both non-seasonal and seasonal parts.", "type": "object", "properties": {"p": {"description": "Order of the autoregressive part.", "type": "string", "format": "int64"}, "d": {"description": "Order of the differencing part.", "type": "string", "format": "int64"}, "q": {"description": "Order of the moving-average part.", "type": "string", "format": "int64"}}}, "ClusterInfo": {"id": "ClusterInfo", "description": "Information about a single cluster for clustering model.", "type": "object", "properties": {"centroidId": {"description": "Centroid id.", "type": "string", "format": "int64"}, "clusterRadius": {"description": "Cluster radius, the average distance from centroid to each point assigned to the cluster.", "type": "number", "format": "double"}, "clusterSize": {"description": "Cluster size, the total number of points assigned to the cluster.", "type": "string", "format": "int64"}}}, "ArimaResult": {"id": "ArimaResult", "description": "(Auto-)arima fitting result. Wrap everything in ArimaResult for easier refactoring if we want to use model-specific iteration results.", "type": "object", "properties": {"arimaModelInfo": {"description": "This message is repeated because there are multiple arima models fitted in auto-arima. For non-auto-arima model, its size is one.", "type": "array", "items": {"$ref": "ArimaModelInfo"}}, "seasonalPeriods": {"description": "Seasonal periods. Repeated because multiple periods are supported for one time series.", "type": "array", "items": {"type": "string", "enumDescriptions": ["Unspecified seasonal period.", "No seasonality", "Daily period, 24 hours.", "Weekly period, 7 days.", "Monthly period, 30 days or irregular.", "Quarterly period, 90 days or irregular.", "Yearly period, 365 days or irregular."], "enum": ["SEASONAL_PERIOD_TYPE_UNSPECIFIED", "NO_SEASONALITY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"]}}}}, "ArimaModelInfo": {"id": "ArimaModelInfo", "description": "Arima model information.", "type": "object", "properties": {"nonSeasonalOrder": {"description": "Non-seasonal order.", "$ref": "ArimaOrder"}, "arimaCoefficients": {"description": "Arima coefficients.", "$ref": "ArimaCoefficients"}, "arimaFittingMetrics": {"description": "Arima fitting metrics.", "$ref": "ArimaFittingMetrics"}, "hasDrift": {"description": "Whether Arima model fitted with drift or not. It is always false when d is not 1.", "type": "boolean"}, "timeSeriesId": {"description": "The time_series_id value for this time series. It will be one of the unique values from the time_series_id_column specified during ARIMA model training. Only present when time_series_id_column training option was used.", "type": "string"}, "timeSeriesIds": {"description": "The tuple of time_series_ids identifying this time series. It will be one of the unique tuples of values present in the time_series_id_columns specified during ARIMA model training. Only present when time_series_id_columns training option was used and the order of values here are same as the order of time_series_id_columns.", "type": "array", "items": {"type": "string"}}, "seasonalPeriods": {"description": "Seasonal periods. Repeated because multiple periods are supported for one time series.", "type": "array", "items": {"type": "string", "enumDescriptions": ["Unspecified seasonal period.", "No seasonality", "Daily period, 24 hours.", "Weekly period, 7 days.", "Monthly period, 30 days or irregular.", "Quarterly period, 90 days or irregular.", "Yearly period, 365 days or irregular."], "enum": ["SEASONAL_PERIOD_TYPE_UNSPECIFIED", "NO_SEASONALITY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"]}}, "hasHolidayEffect": {"description": "If true, holiday_effect is a part of time series decomposition result.", "type": "boolean"}, "hasSpikesAndDips": {"description": "If true, spikes_and_dips is a part of time series decomposition result.", "type": "boolean"}, "hasStepChanges": {"description": "If true, step_changes is a part of time series decomposition result.", "type": "boolean"}}}, "ArimaCoefficients": {"id": "ArimaCoefficients", "description": "Arima coefficients.", "type": "object", "properties": {"autoRegressiveCoefficients": {"description": "Auto-regressive coefficients, an array of double.", "type": "array", "items": {"type": "number", "format": "double"}}, "movingAverageCoefficients": {"description": "Moving-average coefficients, an array of double.", "type": "array", "items": {"type": "number", "format": "double"}}, "interceptCoefficient": {"description": "Intercept coefficient, just a double not an array.", "type": "number", "format": "double"}}}, "ArimaFittingMetrics": {"id": "ArimaFittingMetrics", "description": "ARIMA model fitting metrics.", "type": "object", "properties": {"logLikelihood": {"description": "Log-likelihood.", "type": "number", "format": "double"}, "aic": {"description": "AIC.", "type": "number", "format": "double"}, "variance": {"description": "Varian<PERSON>.", "type": "number", "format": "double"}}}, "PrincipalComponentInfo": {"id": "PrincipalComponentInfo", "description": "Principal component infos, used only for eigen decomposition based models, e.g., PCA. Ordered by explained_variance in the descending order.", "type": "object", "properties": {"principalComponentId": {"description": "Id of the principal component.", "type": "string", "format": "int64"}, "explainedVariance": {"description": "Explained variance by this principal component, which is simply the eigenvalue.", "type": "number", "format": "double"}, "explainedVarianceRatio": {"description": "Explained_variance over the total explained variance.", "type": "number", "format": "double"}, "cumulativeExplainedVarianceRatio": {"description": "The explained_variance is pre-ordered in the descending order to compute the cumulative explained variance ratio.", "type": "number", "format": "double"}}}, "EvaluationMetrics": {"id": "EvaluationMetrics", "description": "Evaluation metrics of a model. These are either computed on all training data or just the eval data based on whether eval data was used during training. These are not present for imported models.", "type": "object", "properties": {"regressionMetrics": {"description": "Populated for regression models and explicit feedback type matrix factorization models.", "$ref": "RegressionMetrics"}, "binaryClassificationMetrics": {"description": "Populated for binary classification/classifier models.", "$ref": "BinaryClassificationMetrics"}, "multiClassClassificationMetrics": {"description": "Populated for multi-class classification/classifier models.", "$ref": "MultiClassClassificationMetrics"}, "clusteringMetrics": {"description": "Populated for clustering models.", "$ref": "ClusteringMetrics"}, "rankingMetrics": {"description": "Populated for implicit feedback type matrix factorization models.", "$ref": "RankingMetrics"}, "arimaForecastingMetrics": {"description": "Populated for ARIMA models.", "$ref": "ArimaForecastingMetrics"}, "dimensionalityReductionMetrics": {"description": "Evaluation metrics when the model is a dimensionality reduction model, which currently includes PCA.", "$ref": "DimensionalityReductionMetrics"}}}, "RegressionMetrics": {"id": "RegressionMetrics", "description": "Evaluation metrics for regression and explicit feedback type matrix factorization models.", "type": "object", "properties": {"meanAbsoluteError": {"description": "Mean absolute error.", "type": "number", "format": "double"}, "meanSquaredError": {"description": "Mean squared error.", "type": "number", "format": "double"}, "meanSquaredLogError": {"description": "Mean squared log error.", "type": "number", "format": "double"}, "medianAbsoluteError": {"description": "Median absolute error.", "type": "number", "format": "double"}, "rSquared": {"description": "R^2 score. This corresponds to r2_score in ML.EVALUATE.", "type": "number", "format": "double"}}}, "BinaryClassificationMetrics": {"id": "BinaryClassificationMetrics", "description": "Evaluation metrics for binary classification/classifier models.", "type": "object", "properties": {"aggregateClassificationMetrics": {"description": "Aggregate classification metrics.", "$ref": "AggregateClassificationMetrics"}, "binaryConfusionMatrixList": {"description": "Binary confusion matrix at multiple thresholds.", "type": "array", "items": {"$ref": "BinaryConfusionMatrix"}}, "positiveLabel": {"description": "Label representing the positive class.", "type": "string"}, "negativeLabel": {"description": "Label representing the negative class.", "type": "string"}}}, "AggregateClassificationMetrics": {"id": "AggregateClassificationMetrics", "description": "Aggregate metrics for classification/classifier models. For multi-class models, the metrics are either macro-averaged or micro-averaged. When macro-averaged, the metrics are calculated for each label and then an unweighted average is taken of those values. When micro-averaged, the metric is calculated globally by counting the total number of correctly predicted rows.", "type": "object", "properties": {"precision": {"description": "Precision is the fraction of actual positive predictions that had positive actual labels. For multiclass this is a macro-averaged metric treating each class as a binary classifier.", "type": "number", "format": "double"}, "recall": {"description": "Recall is the fraction of actual positive labels that were given a positive prediction. For multiclass this is a macro-averaged metric.", "type": "number", "format": "double"}, "accuracy": {"description": "Accuracy is the fraction of predictions given the correct label. For multiclass this is a micro-averaged metric.", "type": "number", "format": "double"}, "threshold": {"description": "Threshold at which the metrics are computed. For binary classification models this is the positive class threshold. For multi-class classfication models this is the confidence threshold.", "type": "number", "format": "double"}, "f1Score": {"description": "The F1 score is an average of recall and precision. For multiclass this is a macro-averaged metric.", "type": "number", "format": "double"}, "logLoss": {"description": "Logarithmic Loss. For multiclass this is a macro-averaged metric.", "type": "number", "format": "double"}, "rocAuc": {"description": "Area Under a ROC Curve. For multiclass this is a macro-averaged metric.", "type": "number", "format": "double"}}}, "BinaryConfusionMatrix": {"id": "BinaryConfusionMatrix", "description": "Confusion matrix for binary classification models.", "type": "object", "properties": {"positiveClassThreshold": {"description": "Threshold value used when computing each of the following metric.", "type": "number", "format": "double"}, "truePositives": {"description": "Number of true samples predicted as true.", "type": "string", "format": "int64"}, "falsePositives": {"description": "Number of false samples predicted as true.", "type": "string", "format": "int64"}, "trueNegatives": {"description": "Number of true samples predicted as false.", "type": "string", "format": "int64"}, "falseNegatives": {"description": "Number of false samples predicted as false.", "type": "string", "format": "int64"}, "precision": {"description": "The fraction of actual positive predictions that had positive actual labels.", "type": "number", "format": "double"}, "recall": {"description": "The fraction of actual positive labels that were given a positive prediction.", "type": "number", "format": "double"}, "f1Score": {"description": "The equally weighted average of recall and precision.", "type": "number", "format": "double"}, "accuracy": {"description": "The fraction of predictions given the correct label.", "type": "number", "format": "double"}}}, "MultiClassClassificationMetrics": {"id": "MultiClassClassificationMetrics", "description": "Evaluation metrics for multi-class classification/classifier models.", "type": "object", "properties": {"aggregateClassificationMetrics": {"description": "Aggregate classification metrics.", "$ref": "AggregateClassificationMetrics"}, "confusionMatrixList": {"description": "Confusion matrix at different thresholds.", "type": "array", "items": {"$ref": "ConfusionMatrix"}}}}, "ConfusionMatrix": {"id": "ConfusionMatrix", "description": "Confusion matrix for multi-class classification models.", "type": "object", "properties": {"confidenceThreshold": {"description": "Confidence threshold used when computing the entries of the confusion matrix.", "type": "number", "format": "double"}, "rows": {"description": "One row per actual label.", "type": "array", "items": {"$ref": "Row"}}}}, "Row": {"id": "Row", "description": "A single row in the confusion matrix.", "type": "object", "properties": {"actualLabel": {"description": "The original label of this row.", "type": "string"}, "entries": {"description": "Info describing predicted label distribution.", "type": "array", "items": {"$ref": "Entry"}}}}, "Entry": {"id": "Entry", "description": "A single entry in the confusion matrix.", "type": "object", "properties": {"predictedLabel": {"description": "The predicted label. For confidence_threshold > 0, we will also add an entry indicating the number of items under the confidence threshold.", "type": "string"}, "itemCount": {"description": "Number of items being predicted as this label.", "type": "string", "format": "int64"}}}, "ClusteringMetrics": {"id": "ClusteringMetrics", "description": "Evaluation metrics for clustering models.", "type": "object", "properties": {"daviesBouldinIndex": {"description": "Davies-Bouldin index.", "type": "number", "format": "double"}, "meanSquaredDistance": {"description": "Mean of squared distances between each sample to its cluster centroid.", "type": "number", "format": "double"}, "clusters": {"description": "Information for all clusters.", "type": "array", "items": {"$ref": "Cluster"}}}}, "Cluster": {"id": "Cluster", "description": "Message containing the information about one cluster.", "type": "object", "properties": {"centroidId": {"description": "Centroid id.", "type": "string", "format": "int64"}, "featureValues": {"description": "Values of highly variant features for this cluster.", "type": "array", "items": {"$ref": "FeatureValue"}}, "count": {"description": "Count of training data rows that were assigned to this cluster.", "type": "string", "format": "int64"}}}, "FeatureValue": {"id": "FeatureValue", "description": "Representative value of a single feature within the cluster.", "type": "object", "properties": {"featureColumn": {"description": "The feature column name.", "type": "string"}, "numericalValue": {"description": "The numerical feature value. This is the centroid value for this feature.", "type": "number", "format": "double"}, "categoricalValue": {"description": "The categorical feature value.", "$ref": "CategoricalValue"}}}, "CategoricalValue": {"id": "CategoricalValue", "description": "Representative value of a categorical feature.", "type": "object", "properties": {"categoryCounts": {"description": "Counts of all categories for the categorical feature. If there are more than ten categories, we return top ten (by count) and return one more CategoryCount with category \"_OTHER_\" and count as aggregate counts of remaining categories.", "type": "array", "items": {"$ref": "CategoryCount"}}}}, "CategoryCount": {"id": "CategoryCount", "description": "Represents the count of a single category within the cluster.", "type": "object", "properties": {"category": {"description": "The name of category.", "type": "string"}, "count": {"description": "The count of training samples matching the category within the cluster.", "type": "string", "format": "int64"}}}, "RankingMetrics": {"id": "RankingMetrics", "description": "Evaluation metrics used by weighted-ALS models specified by feedback_type=implicit.", "type": "object", "properties": {"meanAveragePrecision": {"description": "Calculates a precision per user for all the items by ranking them and then averages all the precisions across all the users.", "type": "number", "format": "double"}, "meanSquaredError": {"description": "Similar to the mean squared error computed in regression and explicit recommendation models except instead of computing the rating directly, the output from evaluate is computed against a preference which is 1 or 0 depending on if the rating exists or not.", "type": "number", "format": "double"}, "normalizedDiscountedCumulativeGain": {"description": "A metric to determine the goodness of a ranking calculated from the predicted confidence by comparing it to an ideal rank measured by the original ratings.", "type": "number", "format": "double"}, "averageRank": {"description": "Determines the goodness of a ranking by computing the percentile rank from the predicted confidence and dividing it by the original rank.", "type": "number", "format": "double"}}}, "ArimaForecastingMetrics": {"id": "ArimaForecastingMetrics", "description": "Model evaluation metrics for ARIMA forecasting models.", "type": "object", "properties": {"nonSeasonalOrder": {"description": "Non-seasonal order.", "deprecated": true, "type": "array", "items": {"$ref": "ArimaOrder"}}, "arimaFittingMetrics": {"description": "Arima model fitting metrics.", "deprecated": true, "type": "array", "items": {"$ref": "ArimaFittingMetrics"}}, "seasonalPeriods": {"description": "Seasonal periods. Repeated because multiple periods are supported for one time series.", "deprecated": true, "type": "array", "items": {"type": "string", "enumDescriptions": ["Unspecified seasonal period.", "No seasonality", "Daily period, 24 hours.", "Weekly period, 7 days.", "Monthly period, 30 days or irregular.", "Quarterly period, 90 days or irregular.", "Yearly period, 365 days or irregular."], "enum": ["SEASONAL_PERIOD_TYPE_UNSPECIFIED", "NO_SEASONALITY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"]}}, "hasDrift": {"description": "Whether Arima model fitted with drift or not. It is always false when d is not 1.", "deprecated": true, "type": "array", "items": {"type": "boolean"}}, "timeSeriesId": {"description": "Id to differentiate different time series for the large-scale case.", "deprecated": true, "type": "array", "items": {"type": "string"}}, "arimaSingleModelForecastingMetrics": {"description": "Repeated as there can be many metric sets (one for each model) in auto-arima and the large-scale case.", "type": "array", "items": {"$ref": "ArimaSingleModelForecastingMetrics"}}}}, "ArimaSingleModelForecastingMetrics": {"id": "ArimaSingleModelForecastingMetrics", "description": "Model evaluation metrics for a single ARIMA forecasting model.", "type": "object", "properties": {"nonSeasonalOrder": {"description": "Non-seasonal order.", "$ref": "ArimaOrder"}, "arimaFittingMetrics": {"description": "Arima fitting metrics.", "$ref": "ArimaFittingMetrics"}, "hasDrift": {"description": "Is arima model fitted with drift or not. It is always false when d is not 1.", "type": "boolean"}, "timeSeriesId": {"description": "The time_series_id value for this time series. It will be one of the unique values from the time_series_id_column specified during ARIMA model training. Only present when time_series_id_column training option was used.", "type": "string"}, "timeSeriesIds": {"description": "The tuple of time_series_ids identifying this time series. It will be one of the unique tuples of values present in the time_series_id_columns specified during ARIMA model training. Only present when time_series_id_columns training option was used and the order of values here are same as the order of time_series_id_columns.", "type": "array", "items": {"type": "string"}}, "seasonalPeriods": {"description": "Seasonal periods. Repeated because multiple periods are supported for one time series.", "type": "array", "items": {"type": "string", "enumDescriptions": ["Unspecified seasonal period.", "No seasonality", "Daily period, 24 hours.", "Weekly period, 7 days.", "Monthly period, 30 days or irregular.", "Quarterly period, 90 days or irregular.", "Yearly period, 365 days or irregular."], "enum": ["SEASONAL_PERIOD_TYPE_UNSPECIFIED", "NO_SEASONALITY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"]}}, "hasHolidayEffect": {"description": "If true, holiday_effect is a part of time series decomposition result.", "type": "boolean"}, "hasSpikesAndDips": {"description": "If true, spikes_and_dips is a part of time series decomposition result.", "type": "boolean"}, "hasStepChanges": {"description": "If true, step_changes is a part of time series decomposition result.", "type": "boolean"}}}, "DimensionalityReductionMetrics": {"id": "DimensionalityReductionMetrics", "description": "Model evaluation metrics for dimensionality reduction models.", "type": "object", "properties": {"totalExplainedVarianceRatio": {"description": "Total percentage of variance explained by the selected principal components.", "type": "number", "format": "double"}}}, "DataSplitResult": {"id": "DataSplitResult", "description": "Data split result. This contains references to the training and evaluation data tables that were used to train the model.", "type": "object", "properties": {"trainingTable": {"description": "Table reference of the training data after split.", "$ref": "TableReference"}, "evaluationTable": {"description": "Table reference of the evaluation data after split.", "$ref": "TableReference"}, "testTable": {"description": "Table reference of the test data after split.", "$ref": "TableReference"}}}, "GlobalExplanation": {"id": "GlobalExplanation", "description": "Global explanations containing the top most important features after training.", "type": "object", "properties": {"explanations": {"description": "A list of the top global explanations. Sorted by absolute value of attribution in descending order.", "type": "array", "items": {"$ref": "Explanation"}}, "classLabel": {"description": "Class label for this set of global explanations. Will be empty/null for binary logistic and linear regression models. Sorted alphabetically in descending order.", "type": "string"}}}, "Explanation": {"id": "Explanation", "description": "Explanation for a single feature.", "type": "object", "properties": {"featureName": {"description": "The full feature name. For non-numerical features, will be formatted like `.`. Overall size of feature name will always be truncated to first 120 characters.", "type": "string"}, "attribution": {"description": "Attribution of feature.", "type": "number", "format": "double"}}}, "StandardSqlField": {"id": "StandardSqlField", "description": "A field or a column.", "type": "object", "properties": {"name": {"description": "Optional. The name of this field. Can be absent for struct fields.", "type": "string"}, "type": {"description": "Optional. The type of this parameter. Absent if not explicitly specified (e.g., CREATE FUNCTION statement can omit the return type; in this case the output parameter does not have this \"type\" field).", "$ref": "StandardSqlDataType"}}}, "StandardSqlDataType": {"id": "StandardSqlDataType", "description": "The data type of a variable such as a function argument. Examples include: * INT64: `{\"typeKind\": \"INT64\"}` * ARRAY: { \"typeKind\": \"ARRAY\", \"arrayElementType\": {\"typeKind\": \"STRING\"} } * STRUCT>: { \"typeKind\": \"STRUCT\", \"structType\": { \"fields\": [ { \"name\": \"x\", \"type\": {\"typeKind\": \"STRING\"} }, { \"name\": \"y\", \"type\": { \"typeKind\": \"ARRAY\", \"arrayElementType\": {\"typeKind\": \"DATE\"} } } ] } }", "type": "object", "properties": {"typeKind": {"description": "Required. The top level type of this field. Can be any GoogleSQL data type (e.g., \"INT64\", \"DATE\", \"ARRAY\").", "type": "string", "enumDescriptions": ["Invalid type.", "Encoded as a string in decimal format.", "Encoded as a boolean \"false\" or \"true\".", "Encoded as a number, or string \"NaN\", \"Infinity\" or \"-Infinity\".", "Encoded as a string value.", "Encoded as a base64 string per RFC 4648, section 4.", "Encoded as an RFC 3339 timestamp with mandatory \"Z\" time zone string: 1985-04-12T23:20:50.52Z", "Encoded as RFC 3339 full-date format string: 1985-04-12", "Encoded as RFC 3339 partial-time format string: 23:20:50.52", "Encoded as RFC 3339 full-date \"T\" partial-time: 1985-04-12T23:20:50.52", "Encoded as fully qualified 3 part: 0-5 15 2:30:45.6", "Encoded as WKT", "Encoded as a decimal string.", "Encoded as a decimal string.", "Encoded as a string.", "Encoded as a list with types matching Type.array_type.", "Encoded as a list with fields of type Type.struct_type[i]. List is used because a JSON object cannot have duplicate field names.", "Encoded as a pair with types matching range_element_type. Pairs must begin with \"[\", end with \")\", and be separated by \", \"."], "enum": ["TYPE_KIND_UNSPECIFIED", "INT64", "BOOL", "FLOAT64", "STRING", "BYTES", "TIMESTAMP", "DATE", "TIME", "DATETIME", "INTERVAL", "GEOGRAPHY", "NUMERIC", "BIGNUMERIC", "JSON", "ARRAY", "STRUCT", "RANGE"]}, "arrayElementType": {"description": "The type of the array's elements, if type_kind = \"ARRAY\".", "$ref": "StandardSqlDataType"}, "structType": {"description": "The fields of this struct, in order, if type_kind = \"STRUCT\".", "$ref": "StandardSqlStructType"}, "rangeElementType": {"description": "The type of the range's elements, if type_kind = \"RANGE\".", "$ref": "StandardSqlDataType"}}}, "StandardSqlStructType": {"id": "StandardSqlStructType", "type": "object", "properties": {"fields": {"type": "array", "items": {"$ref": "StandardSqlField"}}}}, "TransformColumn": {"id": "TransformColumn", "description": "Information about a single transform column.", "type": "object", "properties": {"name": {"description": "Output only. Name of the column.", "readOnly": true, "type": "string"}, "type": {"description": "Output only. Data type of the column after the transform.", "readOnly": true, "$ref": "StandardSqlDataType"}, "transformSql": {"description": "Output only. The SQL expression used in the column transform.", "readOnly": true, "type": "string"}}}, "HparamSearchSpaces": {"id": "HparamSearchSpaces", "description": "Hyperparameter search spaces. These should be a subset of training_options.", "type": "object", "properties": {"learnRate": {"description": "Learning rate of training jobs.", "$ref": "DoubleHparamSearchSpace"}, "l1Reg": {"description": "L1 regularization coefficient.", "$ref": "DoubleHparamSearchSpace"}, "l2Reg": {"description": "L2 regularization coefficient.", "$ref": "DoubleHparamSearchSpace"}, "numClusters": {"description": "Number of clusters for k-means.", "$ref": "IntHparamSearchSpace"}, "numFactors": {"description": "Number of latent factors to train on.", "$ref": "IntHparamSearchSpace"}, "hiddenUnits": {"description": "Hidden units for neural network models.", "$ref": "IntArrayHparamSearchSpace"}, "batchSize": {"description": "Mini batch sample size.", "$ref": "IntHparamSearchSpace"}, "dropout": {"description": "Dropout probability for dnn model training and boosted tree models using dart booster.", "$ref": "DoubleHparamSearchSpace"}, "maxTreeDepth": {"description": "Maximum depth of a tree for boosted tree models.", "$ref": "IntHparamSearchSpace"}, "subsample": {"description": "Subsample the training data to grow tree to prevent overfitting for boosted tree models.", "$ref": "DoubleHparamSearchSpace"}, "minSplitLoss": {"description": "Minimum split loss for boosted tree models.", "$ref": "DoubleHparamSearchSpace"}, "walsAlpha": {"description": "Hyperparameter for matrix factoration when implicit feedback type is specified.", "$ref": "DoubleHparamSearchSpace"}, "boosterType": {"description": "Booster type for boosted tree models.", "$ref": "StringHparamSearchSpace"}, "numParallelTree": {"description": "Number of parallel trees for boosted tree models.", "$ref": "IntHparamSearchSpace"}, "dartNormalizeType": {"description": "Dart normalization type for boosted tree models.", "$ref": "StringHparamSearchSpace"}, "treeMethod": {"description": "Tree construction algorithm for boosted tree models.", "$ref": "StringHparamSearchSpace"}, "minTreeChildWeight": {"description": "Minimum sum of instance weight needed in a child for boosted tree models.", "$ref": "IntHparamSearchSpace"}, "colsampleBytree": {"description": "Subsample ratio of columns when constructing each tree for boosted tree models.", "$ref": "DoubleHparamSearchSpace"}, "colsampleBylevel": {"description": "Subsample ratio of columns for each level for boosted tree models.", "$ref": "DoubleHparamSearchSpace"}, "colsampleBynode": {"description": "Subsample ratio of columns for each node(split) for boosted tree models.", "$ref": "DoubleHparamSearchSpace"}, "activationFn": {"description": "Activation functions of neural network models.", "$ref": "StringHparamSearchSpace"}, "optimizer": {"description": "Optimizer of TF models.", "$ref": "StringHparamSearchSpace"}}}, "DoubleHparamSearchSpace": {"id": "DoubleHparamSearchSpace", "description": "Search space for a double hyperparameter.", "type": "object", "properties": {"range": {"description": "Range of the double hyperparameter.", "$ref": "DoubleRange"}, "candidates": {"description": "Candidates of the double hyperparameter.", "$ref": "DoubleCandidates"}}}, "DoubleRange": {"id": "DoubleRange", "description": "Range of a double hyperparameter.", "type": "object", "properties": {"min": {"description": "Min value of the double parameter.", "type": "number", "format": "double"}, "max": {"description": "Max value of the double parameter.", "type": "number", "format": "double"}}}, "DoubleCandidates": {"id": "DoubleCandidates", "description": "Discrete candidates of a double hyperparameter.", "type": "object", "properties": {"candidates": {"description": "Candidates for the double parameter in increasing order.", "type": "array", "items": {"type": "number", "format": "double"}}}}, "IntHparamSearchSpace": {"id": "IntHparamSearchSpace", "description": "Search space for an int hyperparameter.", "type": "object", "properties": {"range": {"description": "Range of the int hyperparameter.", "$ref": "IntRange"}, "candidates": {"description": "Candidates of the int hyperparameter.", "$ref": "IntCandidates"}}}, "IntRange": {"id": "IntRange", "description": "Range of an int hyperparameter.", "type": "object", "properties": {"min": {"description": "Min value of the int parameter.", "type": "string", "format": "int64"}, "max": {"description": "Max value of the int parameter.", "type": "string", "format": "int64"}}}, "IntCandidates": {"id": "IntCandidates", "description": "Discrete candidates of an int hyperparameter.", "type": "object", "properties": {"candidates": {"description": "Candidates for the int parameter in increasing order.", "type": "array", "items": {"type": "string", "format": "int64"}}}}, "IntArrayHparamSearchSpace": {"id": "IntArrayHparamSearchSpace", "description": "Search space for int array.", "type": "object", "properties": {"candidates": {"description": "Candidates for the int array parameter.", "type": "array", "items": {"$ref": "IntArray"}}}}, "IntArray": {"id": "IntArray", "description": "An array of int.", "type": "object", "properties": {"elements": {"description": "Elements in the int array.", "type": "array", "items": {"type": "string", "format": "int64"}}}}, "StringHparamSearchSpace": {"id": "StringHparamSearchSpace", "description": "Search space for string and enum.", "type": "object", "properties": {"candidates": {"description": "Canididates for the string or enum parameter in lower case.", "type": "array", "items": {"type": "string"}}}}, "HparamTuningTrial": {"id": "HparamTuningTrial", "description": "Training info of a trial in [hyperparameter tuning](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) models.", "type": "object", "properties": {"trialId": {"description": "1-based index of the trial.", "type": "string", "format": "int64"}, "startTimeMs": {"description": "Starting time of the trial.", "type": "string", "format": "int64"}, "endTimeMs": {"description": "Ending time of the trial.", "type": "string", "format": "int64"}, "hparams": {"description": "The hyperprameters selected for this trial.", "$ref": "TrainingOptions"}, "evaluationMetrics": {"description": "Evaluation metrics of this trial calculated on the test data. Empty in Job API.", "$ref": "EvaluationMetrics"}, "status": {"description": "The status of the trial.", "type": "string", "enumDescriptions": ["", "Scheduled but not started.", "Running state.", "The trial succeeded.", "The trial failed.", "The trial is infeasible due to the invalid params.", "Trial stopped early because it's not promising."], "enum": ["TRIAL_STATUS_UNSPECIFIED", "NOT_STARTED", "RUNNING", "SUCCEEDED", "FAILED", "INFEASIBLE", "STOPPED_EARLY"]}, "errorMessage": {"description": "Error message for FAILED and INFEASIBLE trial.", "type": "string"}, "trainingLoss": {"description": "Loss computed on the training data at the end of trial.", "type": "number", "format": "double"}, "evalLoss": {"description": "Loss computed on the eval data at the end of trial.", "type": "number", "format": "double"}, "hparamTuningEvaluationMetrics": {"description": "Hyperparameter tuning evaluation metrics of this trial calculated on the eval data. Unlike evaluation_metrics, only the fields corresponding to the hparam_tuning_objectives are set.", "$ref": "EvaluationMetrics"}}}, "RemoteModelInfo": {"id": "RemoteModelInfo", "description": "Remote Model Info", "type": "object", "properties": {"endpoint": {"description": "Output only. The endpoint for remote model.", "readOnly": true, "type": "string"}, "remoteServiceType": {"description": "Output only. The remote service type for remote model.", "readOnly": true, "type": "string", "enumDescriptions": ["Unspecified remote service type.", "V3 Cloud AI Translation API. See more details at [Cloud Translation API] (https://cloud.google.com/translate/docs/reference/rest).", "V1 Cloud AI Vision API See more details at [Cloud Vision API] (https://cloud.google.com/vision/docs/reference/rest).", "V1 Cloud AI Natural Language API. See more details at [REST Resource: documents](https://cloud.google.com/natural-language/docs/reference/rest/v1/documents)."], "enum": ["REMOTE_SERVICE_TYPE_UNSPECIFIED", "CLOUD_AI_TRANSLATE_V3", "CLOUD_AI_VISION_V1", "CLOUD_AI_NATURAL_LANGUAGE_V1"]}, "connection": {"description": "Output only. Fully qualified name of the user-provided connection object of the remote model. Format: ```\"projects/{project_id}/locations/{location_id}/connections/{connection_id}\"```", "readOnly": true, "type": "string"}, "maxBatchingRows": {"description": "Output only. Max number of rows in each batch sent to the remote service. If unset, the number of rows in each batch is set dynamically.", "readOnly": true, "type": "string", "format": "int64"}, "remoteModelVersion": {"description": "Output only. The model version for LLM.", "readOnly": true, "type": "string"}}}, "ListModelsResponse": {"id": "ListModelsResponse", "type": "object", "properties": {"models": {"description": "Models in the requested dataset. Only the following fields are populated: model_reference, model_type, creation_time, last_modified_time and labels.", "type": "array", "items": {"$ref": "Model"}}, "nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}}}, "Routine": {"id": "Routine", "description": "A user-defined function or a stored procedure.", "type": "object", "properties": {"etag": {"description": "Output only. A hash of this resource.", "readOnly": true, "type": "string"}, "routineReference": {"description": "Required. Reference describing the ID of this routine.", "$ref": "RoutineReference"}, "routineType": {"description": "Required. The type of routine.", "type": "string", "enumDescriptions": ["", "Non-built-in persistent scalar function.", "Stored procedure.", "Non-built-in persistent TVF.", "Non-built-in persistent aggregate function."], "enum": ["ROUTINE_TYPE_UNSPECIFIED", "SCALAR_FUNCTION", "PROCEDURE", "TABLE_VALUED_FUNCTION", "AGGREGATE_FUNCTION"]}, "creationTime": {"description": "Output only. The time when this routine was created, in milliseconds since the epoch.", "readOnly": true, "type": "string", "format": "int64"}, "lastModifiedTime": {"description": "Output only. The time when this routine was last modified, in milliseconds since the epoch.", "readOnly": true, "type": "string", "format": "int64"}, "language": {"description": "Optional. Defaults to \"SQL\" if remote_function_options field is absent, not set otherwise.", "type": "string", "enumDescriptions": ["", "SQL language.", "JavaScript language.", "Python language.", "Java language.", "Scala language."], "enum": ["LANGUAGE_UNSPECIFIED", "SQL", "JAVASCRIPT", "PYTHON", "JAVA", "SCALA"]}, "arguments": {"description": "Optional.", "type": "array", "items": {"$ref": "Argument"}}, "returnType": {"description": "Optional if language = \"SQL\"; required otherwise. Cannot be set if routine_type = \"TABLE_VALUED_FUNCTION\". If absent, the return type is inferred from definition_body at query time in each query that references this routine. If present, then the evaluated result will be cast to the specified returned type at query time. For example, for the functions created with the following statements: * `CREATE FUNCTION Add(x FLOAT64, y FLOAT64) RETURNS FLOAT64 AS (x + y);` * `CREATE FUNCTION Increment(x FLOAT64) AS (Add(x, 1));` * `CREATE FUNCTION Decrement(x FLOAT64) RETURNS FLOAT64 AS (Add(x, -1));` The return_type is `{type_kind: \"FLOAT64\"}` for `Add` and `Decrement`, and is absent for `Increment` (inferred as FLOAT64 at query time). Suppose the function `Add` is replaced by `CREATE OR REPLACE FUNCTION Add(x INT64, y INT64) AS (x + y);` Then the inferred return type of `Increment` is automatically changed to INT64 at query time, while the return type of `Decrement` remains FLOAT64.", "$ref": "StandardSqlDataType"}, "returnTableType": {"description": "Optional. Can be set only if routine_type = \"TABLE_VALUED_FUNCTION\". If absent, the return table type is inferred from definition_body at query time in each query that references this routine. If present, then the columns in the evaluated table result will be cast to match the column types specified in return table type, at query time.", "$ref": "StandardSqlTableType"}, "importedLibraries": {"description": "Optional. If language = \"JAVASCRIPT\", this field stores the path of the imported JAVASCRIPT libraries.", "type": "array", "items": {"type": "string"}}, "definitionBody": {"description": "Required. The body of the routine. For functions, this is the expression in the AS clause. If language=SQL, it is the substring inside (but excluding) the parentheses. For example, for the function created with the following statement: `CREATE FUNCTION JoinLines(x string, y string) as (concat(x, \"\\n\", y))` The definition_body is `concat(x, \"\\n\", y)` (\\n is not replaced with linebreak). If language=JAVASCRIPT, it is the evaluated string in the AS clause. For example, for the function created with the following statement: `CREATE FUNCTION f() RETURNS STRING LANGUAGE js AS 'return \"\\n\";\\n'` The definition_body is `return \"\\n\";\\n` Note that both \\n are replaced with linebreaks.", "type": "string"}, "description": {"description": "Optional. The description of the routine, if defined.", "type": "string"}, "determinismLevel": {"description": "Optional. The determinism level of the JavaScript UDF, if defined.", "type": "string", "enumDescriptions": ["The determinism of the UDF is unspecified.", "The UDF is deterministic, meaning that 2 function calls with the same inputs always produce the same result, even across 2 query runs.", "The UDF is not deterministic."], "enum": ["DETERMINISM_LEVEL_UNSPECIFIED", "DETERMINISTIC", "NOT_DETERMINISTIC"]}, "strictMode": {"description": "Optional. Can be set for procedures only. If true (default), the definition body will be validated in the creation and the updates of the procedure. For procedures with an argument of ANY TYPE, the definition body validtion is not supported at creation/update time, and thus this field must be set to false explicitly.", "type": "boolean"}, "remoteFunctionOptions": {"description": "Optional. Remote function specific options.", "$ref": "RemoteFunctionOptions"}, "sparkOptions": {"description": "Optional. Spark specific options.", "$ref": "SparkOptions"}, "dataGovernanceType": {"description": "Optional. Data governance specific option, if the value is DATA_MASKING, the function will be validated as masking functions.", "type": "string", "enumDescriptions": ["Unspecified data governance type.", "The data governance type is data masking."], "enum": ["DATA_GOVERNANCE_TYPE_UNSPECIFIED", "DATA_MASKING"]}}}, "Argument": {"id": "Argument", "description": "Input/output argument of a function or a stored procedure.", "type": "object", "properties": {"name": {"description": "Optional. The name of this argument. Can be absent for function return argument.", "type": "string"}, "argumentKind": {"description": "Optional. Defaults to FIXED_TYPE.", "type": "string", "enumDescriptions": ["", "The argument is a variable with fully specified type, which can be a struct or an array, but not a table.", "The argument is any type, including struct or array, but not a table. To be added: FIXED_TABLE, ANY_TABLE"], "enum": ["ARGUMENT_KIND_UNSPECIFIED", "FIXED_TYPE", "ANY_TYPE"]}, "mode": {"description": "Optional. Specifies whether the argument is input or output. Can be set for procedures only.", "type": "string", "enumDescriptions": ["", "The argument is input-only.", "The argument is output-only.", "The argument is both an input and an output."], "enum": ["MODE_UNSPECIFIED", "IN", "OUT", "INOUT"]}, "dataType": {"description": "Required unless argument_kind = ANY_TYPE.", "$ref": "StandardSqlDataType"}}}, "StandardSqlTableType": {"id": "StandardSqlTableType", "description": "A table type", "type": "object", "properties": {"columns": {"description": "The columns in this table type", "type": "array", "items": {"$ref": "StandardSqlField"}}}}, "RemoteFunctionOptions": {"id": "RemoteFunctionOptions", "description": "Options for a remote user-defined function.", "type": "object", "properties": {"endpoint": {"description": "Endpoint of the user-provided remote service, e.g. ```https://us-east1-my_gcf_project.cloudfunctions.net/remote_add```", "type": "string"}, "connection": {"description": "Fully qualified name of the user-provided connection object which holds the authentication information to send requests to the remote service. Format: ```\"projects/{projectId}/locations/{locationId}/connections/{connectionId}\"```", "type": "string"}, "userDefinedContext": {"description": "User-defined context as a set of key/value pairs, which will be sent as function invocation context together with batched arguments in the requests to the remote service. The total number of bytes of keys and values must be less than 8KB.", "type": "object", "additionalProperties": {"type": "string"}}, "maxBatchingRows": {"description": "Max number of rows in each batch sent to the remote service. If absent or if 0, BigQuery dynamically decides the number of rows in a batch.", "type": "string", "format": "int64"}}}, "SparkOptions": {"id": "SparkOptions", "description": "Options for a user-defined Spark routine.", "type": "object", "properties": {"connection": {"description": "Fully qualified name of the user-provided Spark connection object. Format: ```\"projects/{project_id}/locations/{location_id}/connections/{connection_id}\"```", "type": "string"}, "runtimeVersion": {"description": "Runtime version. If not specified, the default runtime version is used.", "type": "string"}, "containerImage": {"description": "Custom container image for the runtime environment.", "type": "string"}, "properties": {"description": "Configuration properties as a set of key/value pairs, which will be passed on to the Spark application. For more information, see [Apache Spark](https://spark.apache.org/docs/latest/index.html) and the [procedure option list](https://cloud.google.com/bigquery/docs/reference/standard-sql/data-definition-language#procedure_option_list).", "type": "object", "additionalProperties": {"type": "string"}}, "mainFileUri": {"description": "The main file/jar URI of the Spark application. Exactly one of the definition_body field and the main_file_uri field must be set for Python. Exactly one of main_class and main_file_uri field should be set for Java/Scala language type.", "type": "string"}, "pyFileUris": {"description": "Python files to be placed on the PYTHONPATH for PySpark application. Supported file types: `.py`, `.egg`, and `.zip`. For more information about Apache Spark, see [Apache Spark](https://spark.apache.org/docs/latest/index.html).", "type": "array", "items": {"type": "string"}}, "jarUris": {"description": "JARs to include on the driver and executor CLASSPATH. For more information about Apache Spark, see [Apache Spark](https://spark.apache.org/docs/latest/index.html).", "type": "array", "items": {"type": "string"}}, "fileUris": {"description": "Files to be placed in the working directory of each executor. For more information about Apache Spark, see [Apache Spark](https://spark.apache.org/docs/latest/index.html).", "type": "array", "items": {"type": "string"}}, "archiveUris": {"description": "Archive files to be extracted into the working directory of each executor. For more information about Apache Spark, see [Apache Spark](https://spark.apache.org/docs/latest/index.html).", "type": "array", "items": {"type": "string"}}, "mainClass": {"description": "The fully qualified name of a class in jar_uris, for example, com.example.wordcount. Exactly one of main_class and main_jar_uri field should be set for Java/Scala language type.", "type": "string"}}}, "ListRoutinesResponse": {"id": "ListRoutinesResponse", "type": "object", "properties": {"routines": {"description": "Routines in the requested dataset. Unless read_mask is set in the request, only the following fields are populated: etag, project_id, dataset_id, routine_id, routine_type, creation_time, last_modified_time, language, and remote_function_options.", "type": "array", "items": {"$ref": "Routine"}}, "nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}}}, "ListRowAccessPoliciesResponse": {"id": "ListRowAccessPoliciesResponse", "description": "Response message for the ListRowAccessPolicies method.", "type": "object", "properties": {"rowAccessPolicies": {"description": "Row access policies on the requested table.", "type": "array", "items": {"$ref": "RowAccessPolicy"}}, "nextPageToken": {"description": "A token to request the next page of results.", "type": "string"}}}, "RowAccessPolicy": {"id": "RowAccessPolicy", "description": "Represents access on a subset of rows on the specified table, defined by its filter predicate. Access to the subset of rows is controlled by its IAM policy.", "type": "object", "properties": {"etag": {"description": "Output only. A hash of this resource.", "readOnly": true, "type": "string"}, "rowAccessPolicyReference": {"description": "Required. Reference describing the ID of this row access policy.", "$ref": "RowAccessPolicyReference"}, "filterPredicate": {"description": "Required. A SQL boolean expression that represents the rows defined by this row access policy, similar to the boolean expression in a WHERE clause of a SELECT query on a table. References to other tables, routines, and temporary functions are not supported. Examples: region=\"EU\" date_field = CAST('2019-9-27' as DATE) nullable_field is not NULL numeric_field BETWEEN 1.0 AND 5.0", "type": "string"}, "creationTime": {"description": "Output only. The time when this row access policy was created, in milliseconds since the epoch.", "readOnly": true, "type": "string", "format": "google-datetime"}, "lastModifiedTime": {"description": "Output only. The time when this row access policy was last modified, in milliseconds since the epoch.", "readOnly": true, "type": "string", "format": "google-datetime"}}}, "SetIamPolicyRequest": {"id": "SetIamPolicyRequest", "description": "Request message for `SetIamPolicy` method.", "type": "object", "properties": {"policy": {"description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them.", "$ref": "Policy"}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "type": "string", "format": "google-fieldmask"}}}, "Policy": {"id": "Policy", "description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "type": "object", "properties": {"version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "type": "integer", "format": "int32"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "type": "array", "items": {"$ref": "Binding"}}, "auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "type": "array", "items": {"$ref": "AuditConfig"}}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "type": "string", "format": "byte"}}}, "Binding": {"id": "Binding", "description": "Associates `members`, or principals, with a `role`.", "type": "object", "properties": {"role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`.", "type": "string"}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding.", "type": "array", "items": {"type": "string"}}, "condition": {"description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "$ref": "Expr"}}}, "Expr": {"id": "Expr", "description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "type": "object", "properties": {"expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}, "description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}}}, "AuditConfig": {"id": "AuditConfig", "description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "type": "object", "properties": {"service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}, "auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "type": "array", "items": {"$ref": "AuditLogConfig"}}}}, "AuditLogConfig": {"id": "AuditLogConfig", "description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "type": "object", "properties": {"logType": {"description": "The log type that this config enables.", "type": "string", "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"]}, "exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "type": "array", "items": {"type": "string"}}}}, "GetIamPolicyRequest": {"id": "GetIamPolicyRequest", "description": "Request message for `GetIamPolicy` method.", "type": "object", "properties": {"options": {"description": "OPTIONAL: A `GetPolicyOptions` object for specifying options to `GetIamPolicy`.", "$ref": "GetPolicyOptions"}}}, "GetPolicyOptions": {"id": "GetPolicyOptions", "description": "Encapsulates settings provided to GetIamPolicy.", "type": "object", "properties": {"requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "type": "integer", "format": "int32"}}}, "TestIamPermissionsRequest": {"id": "TestIamPermissionsRequest", "description": "Request message for `TestIamPermissions` method.", "type": "object", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "type": "array", "items": {"type": "string"}}}}, "TestIamPermissionsResponse": {"id": "TestIamPermissionsResponse", "description": "Response message for `TestIamPermissions` method.", "type": "object", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "type": "array", "items": {"type": "string"}}}}, "LocationMetadata": {"id": "LocationMetadata", "description": "BigQuery-specific metadata about a location. This will be set on google.cloud.location.Location.metadata in Cloud Location API responses.", "type": "object", "properties": {"legacyLocationId": {"description": "The legacy BigQuery location ID, e.g. “EU” for the “europe” location. This is for any API consumers that need the legacy “US” and “EU” locations.", "type": "string"}}}}, "resources": {"datasets": {"methods": {"delete": {"id": "bigquery.datasets.delete", "path": "projects/{projectId}/datasets/{datasetId}", "httpMethod": "DELETE", "description": "Deletes the dataset specified by the datasetId value. Before you can delete a dataset, you must delete all its tables, either manually or by specifying deleteContents. Immediately after deletion, you can create another dataset with the same name.", "parameters": {"datasetId": {"type": "string", "description": "Dataset ID of dataset being deleted", "required": true, "location": "path"}, "deleteContents": {"type": "boolean", "description": "If True, delete all the tables in the dataset. If False and the dataset contains tables, the request will fail. Default is False", "location": "query"}, "projectId": {"type": "string", "description": "Project ID of the dataset being deleted", "required": true, "location": "path"}}, "parameterOrder": ["projectId", "datasetId"], "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"id": "bigquery.datasets.get", "path": "projects/{projectId}/datasets/{datasetId}", "httpMethod": "GET", "description": "Returns the dataset specified by datasetID.", "parameters": {"datasetId": {"type": "string", "description": "Dataset ID of the requested dataset", "required": true, "location": "path"}, "projectId": {"type": "string", "description": "Project ID of the requested dataset", "required": true, "location": "path"}}, "parameterOrder": ["projectId", "datasetId"], "response": {"$ref": "Dataset"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "insert": {"id": "bigquery.datasets.insert", "path": "projects/{projectId}/datasets", "httpMethod": "POST", "description": "Creates a new empty dataset.", "parameters": {"projectId": {"type": "string", "description": "Project ID of the new dataset", "required": true, "location": "path"}}, "parameterOrder": ["projectId"], "request": {"$ref": "Dataset"}, "response": {"$ref": "Dataset"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"id": "bigquery.datasets.list", "path": "projects/{projectId}/datasets", "httpMethod": "GET", "description": "Lists all datasets in the specified project to which you have been granted the READER dataset role.", "parameters": {"all": {"type": "boolean", "description": "Whether to list all datasets, including hidden ones", "location": "query"}, "filter": {"type": "string", "description": "An expression for filtering the results of the request by label. The syntax is \"labels.<name>[:<value>]\". Multiple filters can be ANDed together by connecting with a space. Example: \"labels.department:receiving labels.active\". See Filtering datasets using labels for details.", "location": "query"}, "maxResults": {"type": "integer", "description": "The maximum number of results to return", "format": "uint32", "location": "query"}, "pageToken": {"type": "string", "description": "Page token, returned by a previous call, to request the next page of results", "location": "query"}, "projectId": {"type": "string", "description": "Project ID of the datasets to be listed", "required": true, "location": "path"}}, "parameterOrder": ["projectId"], "response": {"$ref": "DatasetList"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "patch": {"id": "bigquery.datasets.patch", "path": "projects/{projectId}/datasets/{datasetId}", "httpMethod": "PATCH", "description": "Updates information in an existing dataset. The update method replaces the entire dataset resource, whereas the patch method only replaces fields that are provided in the submitted dataset resource. This method supports patch semantics.", "parameters": {"datasetId": {"type": "string", "description": "Dataset ID of the dataset being updated", "required": true, "location": "path"}, "projectId": {"type": "string", "description": "Project ID of the dataset being updated", "required": true, "location": "path"}}, "parameterOrder": ["projectId", "datasetId"], "request": {"$ref": "Dataset"}, "response": {"$ref": "Dataset"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "update": {"id": "bigquery.datasets.update", "path": "projects/{projectId}/datasets/{datasetId}", "httpMethod": "PUT", "description": "Updates information in an existing dataset. The update method replaces the entire dataset resource, whereas the patch method only replaces fields that are provided in the submitted dataset resource.", "parameters": {"datasetId": {"type": "string", "description": "Dataset ID of the dataset being updated", "required": true, "location": "path"}, "projectId": {"type": "string", "description": "Project ID of the dataset being updated", "required": true, "location": "path"}}, "parameterOrder": ["projectId", "datasetId"], "request": {"$ref": "Dataset"}, "response": {"$ref": "Dataset"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}}}, "jobs": {"methods": {"cancel": {"id": "bigquery.jobs.cancel", "path": "projects/{projectId}/jobs/{jobId}/cancel", "httpMethod": "POST", "description": "Requests that a job be cancelled. This call will return immediately, and the client will need to poll for the job status to see if the cancel completed successfully. Cancelled jobs may still incur costs.", "parameters": {"jobId": {"type": "string", "description": "[Required] Job ID of the job to cancel", "required": true, "location": "path"}, "location": {"type": "string", "description": "The geographic location of the job. Required except for US and EU. See details at https://cloud.google.com/bigquery/docs/locations#specifying_your_location.", "location": "query"}, "projectId": {"type": "string", "description": "[Required] Project ID of the job to cancel", "required": true, "location": "path"}}, "parameterOrder": ["projectId", "jobId"], "response": {"$ref": "JobCancelResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"id": "bigquery.jobs.get", "path": "projects/{projectId}/jobs/{jobId}", "httpMethod": "GET", "description": "Returns information about a specific job. Job information is available for a six month period after creation. Requires that you're the person who ran the job, or have the Is Owner project role.", "parameters": {"jobId": {"type": "string", "description": "[Required] Job ID of the requested job", "required": true, "location": "path"}, "location": {"type": "string", "description": "The geographic location of the job. Required except for US and EU. See details at https://cloud.google.com/bigquery/docs/locations#specifying_your_location.", "location": "query"}, "projectId": {"type": "string", "description": "[Required] Project ID of the requested job", "required": true, "location": "path"}}, "parameterOrder": ["projectId", "jobId"], "response": {"$ref": "Job"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "getQueryResults": {"id": "bigquery.jobs.getQueryResults", "path": "projects/{projectId}/queries/{jobId}", "httpMethod": "GET", "description": "Retrieves the results of a query job.", "parameters": {"jobId": {"type": "string", "description": "[Required] Job ID of the query job", "required": true, "location": "path"}, "location": {"type": "string", "description": "The geographic location where the job should run. Required except for US and EU. See details at https://cloud.google.com/bigquery/docs/locations#specifying_your_location.", "location": "query"}, "maxResults": {"type": "integer", "description": "Maximum number of results to read", "format": "uint32", "location": "query"}, "pageToken": {"type": "string", "description": "Page token, returned by a previous call, to request the next page of results", "location": "query"}, "projectId": {"type": "string", "description": "[Required] Project ID of the query job", "required": true, "location": "path"}, "startIndex": {"type": "string", "description": "Zero-based index of the starting row", "format": "uint64", "location": "query"}, "timeoutMs": {"type": "integer", "description": "How long to wait for the query to complete, in milliseconds, before returning. Default is 10 seconds. If the timeout passes before the job completes, the 'jobComplete' field in the response will be false", "format": "uint32", "location": "query"}}, "parameterOrder": ["projectId", "jobId"], "response": {"$ref": "GetQueryResultsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "insert": {"id": "bigquery.jobs.insert", "path": "projects/{projectId}/jobs", "httpMethod": "POST", "description": "Starts a new asynchronous job. Requires the Can View project role.", "parameters": {"projectId": {"type": "string", "description": "Project ID of the project that will be billed for the job", "required": true, "location": "path"}}, "parameterOrder": ["projectId"], "request": {"$ref": "Job"}, "response": {"$ref": "Job"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/devstorage.full_control", "https://www.googleapis.com/auth/devstorage.read_only", "https://www.googleapis.com/auth/devstorage.read_write"], "supportsMediaUpload": true, "mediaUpload": {"accept": ["*/*"], "protocols": {"simple": {"multipart": true, "path": "/upload/bigquery/v2/projects/{projectId}/jobs"}, "resumable": {"multipart": true, "path": "/resumable/upload/bigquery/v2/projects/{projectId}/jobs"}}}}, "list": {"id": "bigquery.jobs.list", "path": "projects/{projectId}/jobs", "httpMethod": "GET", "description": "Lists all jobs that you started in the specified project. Job information is available for a six month period after creation. The job list is sorted in reverse chronological order, by job creation time. Requires the Can View project role, or the Is Owner project role if you set the allUsers property.", "parameters": {"allUsers": {"type": "boolean", "description": "Whether to display jobs owned by all users in the project. Default false", "location": "query"}, "maxCreationTime": {"type": "string", "description": "Max value for job creation time, in milliseconds since the POSIX epoch. If set, only jobs created before or at this timestamp are returned", "format": "uint64", "location": "query"}, "maxResults": {"type": "integer", "description": "Maximum number of results to return", "format": "uint32", "location": "query"}, "minCreationTime": {"type": "string", "description": "Min value for job creation time, in milliseconds since the POSIX epoch. If set, only jobs created after or at this timestamp are returned", "format": "uint64", "location": "query"}, "pageToken": {"type": "string", "description": "Page token, returned by a previous call, to request the next page of results", "location": "query"}, "parentJobId": {"type": "string", "description": "If set, retrieves only jobs whose parent is this job. Otherwise, retrieves only jobs which have no parent", "location": "query"}, "projectId": {"type": "string", "description": "Project ID of the jobs to list", "required": true, "location": "path"}, "projection": {"type": "string", "description": "Restrict information returned to a set of selected fields", "enum": ["full", "minimal"], "enumDescriptions": ["Includes all job data", "Does not include the job configuration"], "location": "query"}, "stateFilter": {"type": "string", "description": "Filter for job state", "enum": ["done", "pending", "running"], "enumDescriptions": ["Finished jobs", "Pending jobs", "Running jobs"], "repeated": true, "location": "query"}}, "parameterOrder": ["projectId"], "response": {"$ref": "JobList"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "query": {"id": "bigquery.jobs.query", "path": "projects/{projectId}/queries", "httpMethod": "POST", "description": "Runs a BigQuery SQL query synchronously and returns query results if the query completes within a specified timeout.", "parameters": {"projectId": {"type": "string", "description": "Project ID of the project billed for the query", "required": true, "location": "path"}}, "parameterOrder": ["projectId"], "request": {"$ref": "QueryRequest"}, "response": {"$ref": "QueryResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "delete": {"id": "bigquery.jobs.delete", "path": "projects/{+projectId}/jobs/{+jobId}/delete", "flatPath": "projects/{projectsId}/jobs/{jobsId}/delete", "httpMethod": "DELETE", "parameters": {"projectId": {"description": "Required. Project ID of the job for which metadata is to be deleted.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "jobId": {"description": "Required. Job ID of the job for which metadata is to be deleted. If this is a parent job which has child jobs, the metadata from all child jobs will be deleted as well. Direct deletion of the metadata of child jobs is not allowed.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "location": {"description": "The geographic location of the job. Required. See details at: https://cloud.google.com/bigquery/docs/locations#specifying_your_location.", "location": "query", "type": "string"}}, "parameterOrder": ["projectId", "jobId"], "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Requests the deletion of the metadata of a job. This call returns when the job's metadata is deleted."}}}, "projects": {"methods": {"getServiceAccount": {"id": "bigquery.projects.getServiceAccount", "path": "projects/{projectId}/serviceAccount", "httpMethod": "GET", "description": "Returns the email address of the service account for your project used for interactions with Google Cloud KMS.", "parameters": {"projectId": {"type": "string", "description": "Project ID for which the service account is requested.", "required": true, "location": "path"}}, "parameterOrder": ["projectId"], "response": {"$ref": "GetServiceAccountResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "list": {"id": "bigquery.projects.list", "path": "projects", "httpMethod": "GET", "description": "Lists all projects to which you have been granted any project role.", "parameters": {"maxResults": {"type": "integer", "description": "Maximum number of results to return", "format": "uint32", "location": "query"}, "pageToken": {"type": "string", "description": "Page token, returned by a previous call, to request the next page of results", "location": "query"}}, "response": {"$ref": "ProjectList"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}}}, "tabledata": {"methods": {"insertAll": {"id": "bigquery.tabledata.insertAll", "path": "projects/{projectId}/datasets/{datasetId}/tables/{tableId}/insertAll", "httpMethod": "POST", "description": "Streams data into BigQuery one record at a time without needing to run a load job. Requires the WRITER dataset role.", "parameters": {"datasetId": {"type": "string", "description": "Dataset ID of the destination table.", "required": true, "location": "path"}, "projectId": {"type": "string", "description": "Project ID of the destination table.", "required": true, "location": "path"}, "tableId": {"type": "string", "description": "Table ID of the destination table.", "required": true, "location": "path"}}, "parameterOrder": ["projectId", "datasetId", "tableId"], "request": {"$ref": "TableDataInsertAllRequest"}, "response": {"$ref": "TableDataInsertAllResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/bigquery.insertdata", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"id": "bigquery.tabledata.list", "path": "projects/{projectId}/datasets/{datasetId}/tables/{tableId}/data", "httpMethod": "GET", "description": "Retrieves table data from a specified set of rows. Requires the READER dataset role.", "parameters": {"datasetId": {"type": "string", "description": "Dataset ID of the table to read", "required": true, "location": "path"}, "maxResults": {"type": "integer", "description": "Maximum number of results to return", "format": "uint32", "location": "query"}, "pageToken": {"type": "string", "description": "Page token, returned by a previous call, identifying the result set", "location": "query"}, "projectId": {"type": "string", "description": "Project ID of the table to read", "required": true, "location": "path"}, "selectedFields": {"type": "string", "description": "List of fields to return (comma-separated). If unspecified, all fields are returned", "location": "query"}, "startIndex": {"type": "string", "description": "Zero-based index of the starting row to read", "format": "uint64", "location": "query"}, "tableId": {"type": "string", "description": "Table ID of the table to read", "required": true, "location": "path"}}, "parameterOrder": ["projectId", "datasetId", "tableId"], "response": {"$ref": "TableDataList"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}}}, "tables": {"methods": {"delete": {"id": "bigquery.tables.delete", "path": "projects/{projectId}/datasets/{datasetId}/tables/{tableId}", "httpMethod": "DELETE", "description": "Deletes the table specified by tableId from the dataset. If the table contains data, all the data will be deleted.", "parameters": {"datasetId": {"type": "string", "description": "Dataset ID of the table to delete", "required": true, "location": "path"}, "projectId": {"type": "string", "description": "Project ID of the table to delete", "required": true, "location": "path"}, "tableId": {"type": "string", "description": "Table ID of the table to delete", "required": true, "location": "path"}}, "parameterOrder": ["projectId", "datasetId", "tableId"], "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "get": {"id": "bigquery.tables.get", "path": "projects/{projectId}/datasets/{datasetId}/tables/{tableId}", "httpMethod": "GET", "description": "Gets the specified table resource by table ID. This method does not return the data in the table, it only returns the table resource, which describes the structure of this table.", "parameters": {"datasetId": {"type": "string", "description": "Dataset ID of the requested table", "required": true, "location": "path"}, "projectId": {"type": "string", "description": "Project ID of the requested table", "required": true, "location": "path"}, "selectedFields": {"type": "string", "description": "List of fields to return (comma-separated). If unspecified, all fields are returned", "location": "query"}, "tableId": {"type": "string", "description": "Table ID of the requested table", "required": true, "location": "path"}, "view": {"type": "string", "description": "Specifies the view that determines which table information is returned. By default, basic table information and storage statistics (STORAGE_STATS) are returned.", "enum": ["BASIC", "FULL", "STORAGE_STATS", "TABLE_METADATA_VIEW_UNSPECIFIED"], "enumDescriptions": ["Includes basic table information including schema and partitioning specification. This view does not include storage statistics such as numRows or numBytes. This view is significantly more efficient and should be used to support high query rates.", "Includes all table information, including storage statistics. It returns same information as STORAGE_STATS view, but may contain additional information in the future.", "Includes all information in the BASIC view as well as storage statistics (numBytes, numLongTermBytes, numRows and lastModifiedTime).", "The default value. Default to the STORAGE_STATS view."], "location": "query"}}, "parameterOrder": ["projectId", "datasetId", "tableId"], "response": {"$ref": "Table"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "insert": {"id": "bigquery.tables.insert", "path": "projects/{projectId}/datasets/{datasetId}/tables", "httpMethod": "POST", "description": "Creates a new, empty table in the dataset.", "parameters": {"datasetId": {"type": "string", "description": "Dataset ID of the new table", "required": true, "location": "path"}, "projectId": {"type": "string", "description": "Project ID of the new table", "required": true, "location": "path"}}, "parameterOrder": ["projectId", "datasetId"], "request": {"$ref": "Table"}, "response": {"$ref": "Table"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "list": {"id": "bigquery.tables.list", "path": "projects/{projectId}/datasets/{datasetId}/tables", "httpMethod": "GET", "description": "Lists all tables in the specified dataset. Requires the READER dataset role.", "parameters": {"datasetId": {"type": "string", "description": "Dataset ID of the tables to list", "required": true, "location": "path"}, "maxResults": {"type": "integer", "description": "Maximum number of results to return", "format": "uint32", "location": "query"}, "pageToken": {"type": "string", "description": "Page token, returned by a previous call, to request the next page of results", "location": "query"}, "projectId": {"type": "string", "description": "Project ID of the tables to list", "required": true, "location": "path"}}, "parameterOrder": ["projectId", "datasetId"], "response": {"$ref": "TableList"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"]}, "patch": {"id": "bigquery.tables.patch", "path": "projects/{projectId}/datasets/{datasetId}/tables/{tableId}", "httpMethod": "PATCH", "description": "Updates information in an existing table. The update method replaces the entire table resource, whereas the patch method only replaces fields that are provided in the submitted table resource. This method supports patch semantics.", "parameters": {"autodetect_schema": {"type": "boolean", "description": "When true will autodetect schema, else will keep original schema", "location": "query"}, "datasetId": {"type": "string", "description": "Dataset ID of the table to update", "required": true, "location": "path"}, "projectId": {"type": "string", "description": "Project ID of the table to update", "required": true, "location": "path"}, "tableId": {"type": "string", "description": "Table ID of the table to update", "required": true, "location": "path"}}, "parameterOrder": ["projectId", "datasetId", "tableId"], "request": {"$ref": "Table"}, "response": {"$ref": "Table"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "update": {"id": "bigquery.tables.update", "path": "projects/{projectId}/datasets/{datasetId}/tables/{tableId}", "httpMethod": "PUT", "description": "Updates information in an existing table. The update method replaces the entire table resource, whereas the patch method only replaces fields that are provided in the submitted table resource.", "parameters": {"autodetect_schema": {"type": "boolean", "description": "When true will autodetect schema, else will keep original schema", "location": "query"}, "datasetId": {"type": "string", "description": "Dataset ID of the table to update", "required": true, "location": "path"}, "projectId": {"type": "string", "description": "Project ID of the table to update", "required": true, "location": "path"}, "tableId": {"type": "string", "description": "Table ID of the table to update", "required": true, "location": "path"}}, "parameterOrder": ["projectId", "datasetId", "tableId"], "request": {"$ref": "Table"}, "response": {"$ref": "Table"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"id": "bigquery.tables.setIamPolicy", "path": "{+resource}:setIamPolicy", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}:setIamPolicy", "httpMethod": "POST", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["resource"], "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors."}, "getIamPolicy": {"id": "bigquery.tables.getIamPolicy", "path": "{+resource}:getIamPolicy", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}:getIamPolicy", "httpMethod": "POST", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["resource"], "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set."}, "testIamPermissions": {"id": "bigquery.tables.testIamPermissions", "path": "{+resource}:testIamPermissions", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}:testIamPermissions", "httpMethod": "POST", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["resource"], "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning."}}}, "models": {"methods": {"get": {"id": "bigquery.models.get", "path": "projects/{+projectId}/datasets/{+datasetId}/models/{+modelId}", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/models/{modelsId}", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. Project ID of the requested model.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the requested model.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "modelId": {"description": "Required. Model ID of the requested model.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId", "datasetId", "modelId"], "response": {"$ref": "Model"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Gets the specified model resource by model ID."}, "list": {"id": "bigquery.models.list", "path": "projects/{+projectId}/datasets/{+datasetId}/models", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/models", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. Project ID of the models to list.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the models to list.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "location": "query", "type": "integer", "format": "uint32"}, "pageToken": {"description": "Page token, returned by a previous call to request the next page of results", "location": "query", "type": "string"}}, "parameterOrder": ["projectId", "datasetId"], "response": {"$ref": "ListModelsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Lists all models in the specified dataset. Requires the READER dataset role. After retrieving the list of models, you can get information about a particular model by calling the models.get method."}, "patch": {"id": "bigquery.models.patch", "path": "projects/{+projectId}/datasets/{+datasetId}/models/{+modelId}", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/models/{modelsId}", "httpMethod": "PATCH", "parameters": {"projectId": {"description": "Required. Project ID of the model to patch.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the model to patch.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "modelId": {"description": "Required. Model ID of the model to patch.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId", "datasetId", "modelId"], "request": {"$ref": "Model"}, "response": {"$ref": "Model"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Patch specific fields in the specified model."}, "delete": {"id": "bigquery.models.delete", "path": "projects/{+projectId}/datasets/{+datasetId}/models/{+modelId}", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/models/{modelsId}", "httpMethod": "DELETE", "parameters": {"projectId": {"description": "Required. Project ID of the model to delete.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the model to delete.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "modelId": {"description": "Required. Model ID of the model to delete.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId", "datasetId", "modelId"], "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Deletes the model specified by modelId from the dataset."}}}, "routines": {"methods": {"get": {"id": "bigquery.routines.get", "path": "projects/{+projectId}/datasets/{+datasetId}/routines/{+routineId}", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. Project ID of the requested routine", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the requested routine", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "routineId": {"description": "Required. Routine ID of the requested routine", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "readMask": {"description": "If set, only the Routine fields in the field mask are returned in the response. If unset, all Routine fields are returned.", "location": "query", "type": "string", "format": "google-fieldmask"}}, "parameterOrder": ["projectId", "datasetId", "routineId"], "response": {"$ref": "Routine"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Gets the specified routine resource by routine ID."}, "insert": {"id": "bigquery.routines.insert", "path": "projects/{+projectId}/datasets/{+datasetId}/routines", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/routines", "httpMethod": "POST", "parameters": {"projectId": {"description": "Required. Project ID of the new routine", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the new routine", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId", "datasetId"], "request": {"$ref": "Routine"}, "response": {"$ref": "Routine"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Creates a new routine in the dataset."}, "update": {"id": "bigquery.routines.update", "path": "projects/{+projectId}/datasets/{+datasetId}/routines/{+routineId}", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}", "httpMethod": "PUT", "parameters": {"projectId": {"description": "Required. Project ID of the routine to update", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the routine to update", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "routineId": {"description": "Required. Routine ID of the routine to update", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId", "datasetId", "routineId"], "request": {"$ref": "Routine"}, "response": {"$ref": "Routine"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Updates information in an existing routine. The update method replaces the entire Routine resource."}, "delete": {"id": "bigquery.routines.delete", "path": "projects/{+projectId}/datasets/{+datasetId}/routines/{+routineId}", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/routines/{routinesId}", "httpMethod": "DELETE", "parameters": {"projectId": {"description": "Required. Project ID of the routine to delete", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the routine to delete", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "routineId": {"description": "Required. Routine ID of the routine to delete", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["projectId", "datasetId", "routineId"], "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform"], "description": "Deletes the routine specified by routineId from the dataset."}, "list": {"id": "bigquery.routines.list", "path": "projects/{+projectId}/datasets/{+datasetId}/routines", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/routines", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. Project ID of the routines to list", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of the routines to list", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "maxResults": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "location": "query", "type": "integer", "format": "uint32"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results", "location": "query", "type": "string"}, "readMask": {"description": "If set, then only the Routine fields in the field mask, as well as project_id, dataset_id and routine_id, are returned in the response. If unset, then the following Routine fields are returned: etag, project_id, dataset_id, routine_id, routine_type, creation_time, last_modified_time, and language.", "location": "query", "type": "string", "format": "google-fieldmask"}, "filter": {"description": "If set, then only the Routines matching this filter are returned. The supported format is `routineType:{RoutineType}`, where `{RoutineType}` is a RoutineType enum. For example: `routineType:SCALAR_FUNCTION`.", "location": "query", "type": "string"}}, "parameterOrder": ["projectId", "datasetId"], "response": {"$ref": "ListRoutinesResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Lists all routines in the specified dataset. Requires the READER dataset role."}}}, "rowAccessPolicies": {"methods": {"list": {"id": "bigquery.rowAccessPolicies.list", "path": "projects/{+projectId}/datasets/{+datasetId}/tables/{+tableId}/rowAccessPolicies", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies", "httpMethod": "GET", "parameters": {"projectId": {"description": "Required. Project ID of the row access policies to list.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "datasetId": {"description": "Required. Dataset ID of row access policies to list.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "tableId": {"description": "Required. Table ID of the table to list row access policies.", "pattern": "^[^/]+$", "location": "path", "required": true, "type": "string"}, "pageToken": {"description": "Page token, returned by a previous call, to request the next page of results.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of results to return in a single response page. Leverage the page tokens to iterate through the entire collection.", "location": "query", "type": "integer", "format": "int32"}}, "parameterOrder": ["projectId", "datasetId", "tableId"], "response": {"$ref": "ListRowAccessPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Lists all row access policies on the specified table."}, "getIamPolicy": {"id": "bigquery.rowAccessPolicies.getIamPolicy", "path": "{+resource}:getIamPolicy", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies/{rowAccessPoliciesId}:getIamPolicy", "httpMethod": "POST", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+/rowAccessPolicies/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["resource"], "request": {"$ref": "GetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set."}, "testIamPermissions": {"id": "bigquery.rowAccessPolicies.testIamPermissions", "path": "{+resource}:testIamPermissions", "flatPath": "projects/{projectsId}/datasets/{datasetsId}/tables/{tablesId}/rowAccessPolicies/{rowAccessPoliciesId}:testIamPermissions", "httpMethod": "POST", "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "pattern": "^projects/[^/]+/datasets/[^/]+/tables/[^/]+/rowAccessPolicies/[^/]+$", "location": "path", "required": true, "type": "string"}}, "parameterOrder": ["resource"], "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/bigquery", "https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/cloud-platform.read-only"], "description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning."}}}}, "mtlsRootUrl": "https://bigquery.mtls.googleapis.com/"}
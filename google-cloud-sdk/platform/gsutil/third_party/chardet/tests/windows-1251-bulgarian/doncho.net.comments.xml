<?xml version="1.0" encoding="windows-1251"?>
<!--
Source: http://blog.doncho.net/wp-commentsrss2.php
Expect: windows-1251
-->
<!-- generator="wordpress/1.5.2" -->
<rss version="2.0" 
	xmlns:content="http://purl.org/rss/1.0/modules/content/">
<channel>
	<title>blog.doncho.net Comments</title>
	<link>http://blog.doncho.net</link>
	<description>Късчета живот</description>
	<pubDate>Wed, 04 Jan 2006 16:15:10 +0000</pubDate>
	<generator>http://wordpress.org/?v=1.5.2</generator>

	<item>
 		<title>Comment on Да лоукостираме или да не лоукостираме? by: Владо</title>
		<link>http://blog.doncho.net/?p=396#comment-10614</link>
		<pubDate>Wed, 04 Jan 2006 16:03:21 +0000</pubDate>
		<guid>http://blog.doncho.net/?p=396#comment-10614</guid>
					<description>&quot;You get what you pay for&quot; :-) Не винаги парите са най-важният критерий. Но човек трябва да изпробва всички опции.
Добре дошли обратно в Дания.</description>
		<content:encoded><![CDATA[	<p>&#8220;You get what you pay for&#8221; <img src='http://blog.doncho.net/wp-images/smilies/icon_smile.gif' alt=':-)' class='wp-smiley' />  Не винаги парите са най-важният критерий. Но човек трябва да изпробва всички опции.<br />
Добре дошли обратно в Дания.</p>
]]></content:encoded>
				</item>
	<item>
 		<title>Comment on Secure Image: краят на коментарния спам? by: Velizar</title>
		<link>http://blog.doncho.net/?p=347#comment-10602</link>
		<pubDate>Mon, 02 Jan 2006 06:51:23 +0000</pubDate>
		<guid>http://blog.doncho.net/?p=347#comment-10602</guid>
					<description>Значи по отношение на SecurityImage Code, един въпрос - за какво ти е hidden параметъра
&quot;securityhash&quot;? До колкото схващам това е MD5 хеша на secirity code, който трябва да се въведе и според това, което е въвел юзера го сравняваш дали е вярното... Но защо трябва да го показваш на сайта?? По принцип не е ли по-добре този параметър да го &quot;държиш&quot; скрит в сесията, т.е. вместо това hidden поле мисля, че е по-добре да ползваш $_SERVER['securityhash'] = ... Само идея де, не го приемай като критика, може и да не съм го разбрал правилно:) 
Сайтът е готин, поздравления! И разбира се Честита нова година! 
Успех!</description>
		<content:encoded><![CDATA[	<p>Значи по отношение на SecurityImage Code, един въпрос - за какво ти е hidden параметъра<br />
&#8220;securityhash&#8221;? До колкото схващам това е MD5 хеша на secirity code, който трябва да се въведе и според това, което е въвел юзера го сравняваш дали е вярното&#8230; Но защо трябва да го показваш на сайта?? По принцип не е ли по-добре този параметър да го &#8220;държиш&#8221; скрит в сесията, т.е. вместо това hidden поле мисля, че е по-добре да ползваш $_SERVER[&#8217;securityhash&#8217;] = &#8230; Само идея де, не го приемай като критика, може и да не съм го разбрал правилно:)<br />
Сайтът е готин, поздравления! И разбира се Честита нова година!<br />
Успех!</p>
]]></content:encoded>
				</item>
	<item>
 		<title>Comment on Можеше by: donangel</title>
		<link>http://blog.doncho.net/?p=390#comment-10528</link>
		<pubDate>Sat, 17 Dec 2005 11:05:30 +0000</pubDate>
		<guid>http://blog.doncho.net/?p=390#comment-10528</guid>
					<description>Анонимни тролове много... и всякакви...</description>
		<content:encoded><![CDATA[	<p>Анонимни тролове много&#8230; и всякакви&#8230;</p>
]]></content:encoded>
				</item>
	<item>
 		<title>Comment on Можеше by: Георги Генов</title>
		<link>http://blog.doncho.net/?p=390#comment-10527</link>
		<pubDate>Sat, 17 Dec 2005 09:46:50 +0000</pubDate>
		<guid>http://blog.doncho.net/?p=390#comment-10527</guid>
					<description>Цитат: &quot;.. президентът и другия политически боклук можеха да си возят миризливите гъзове в нови Шкоди или Хюндай..&quot;
Като гледам и ти си за съд. Аз ако бях на мястото на президента бих ти съдрал задника :)</description>
		<content:encoded><![CDATA[	<p>Цитат: &#8220;.. президентът и другия политически боклук можеха да си возят миризливите гъзове в нови Шкоди или Хюндай..&#8221;<br />
Като гледам и ти си за съд. Аз ако бях на мястото на президента бих ти съдрал задника <img src='http://blog.doncho.net/wp-images/smilies/icon_smile.gif' alt=':)' class='wp-smiley' /> </p>
]]></content:encoded>
				</item>
	<item>
 		<title>Comment on Купон &#8220;МЕРЦЕДЕС&#8221; by: Калин</title>
		<link>http://blog.doncho.net/?p=395#comment-10503</link>
		<pubDate>Tue, 13 Dec 2005 20:40:42 +0000</pubDate>
		<guid>http://blog.doncho.net/?p=395#comment-10503</guid>
					<description>И от мен благодарско!

За един купоничен купон,
Кал:)н</description>
		<content:encoded><![CDATA[	<p>И от мен благодарско!</p>
	<p>За един купоничен купон,<br />
Кал:)н</p>
]]></content:encoded>
				</item>
	<item>
 		<title>Comment on Можеше by: blog.doncho.net &#187; Blog Archive &#187; Купон &#8220;МЕРЦЕДЕС&#8221;</title>
		<link>http://blog.doncho.net/?p=390#comment-10500</link>
		<pubDate>Tue, 13 Dec 2005 16:27:11 +0000</pubDate>
		<guid>http://blog.doncho.net/?p=390#comment-10500</guid>
					<description>[...] Преди време писах за депутатски и президентски мерцедеси. И за това как тези пари можеше да нахранят/спасят хора. [...]</description>
		<content:encoded><![CDATA[	<p>[&#8230;] Преди време писах за депутатски и президентски мерцедеси. И за това как тези пари можеше да нахранят/спасят хора. [&#8230;]</p>
]]></content:encoded>
				</item>
	<item>
 		<title>Comment on Българско Държавно Порно by: Atanas</title>
		<link>http://blog.doncho.net/?p=387#comment-10489</link>
		<pubDate>Sun, 11 Dec 2005 14:22:11 +0000</pubDate>
		<guid>http://blog.doncho.net/?p=387#comment-10489</guid>
					<description>“Информацияонно обслужване” е ЕАД на Държавата. И като такова хич не мога да го приема за частна фирма. Ама хич. Защото то не е. То е държавна фирма - управлява се от (обикновено - политически) държавни чиновници.

Само да добавя, че тази структура е фактически олигополист на пазара за издаване на електронни подписи, които струват скромните 72 лв на година. Реалната цена на услугата не ми е известна, но не вярвам да е по-висока от 10-15 лв.</description>
		<content:encoded><![CDATA[	<p>“Информацияонно обслужване” е ЕАД на Държавата. И като такова хич не мога да го приема за частна фирма. Ама хич. Защото то не е. То е държавна фирма - управлява се от (обикновено - политически) държавни чиновници.</p>
	<p>Само да добавя, че тази структура е фактически олигополист на пазара за издаване на електронни подписи, които струват скромните 72 лв на година. Реалната цена на услугата не ми е известна, но не вярвам да е по-висока от 10-15 лв.</p>
]]></content:encoded>
				</item>
	<item>
 		<title>Comment on Половинките в България by: Палавров</title>
		<link>http://blog.doncho.net/?p=391#comment-10487</link>
		<pubDate>Sun, 11 Dec 2005 00:34:45 +0000</pubDate>
		<guid>http://blog.doncho.net/?p=391#comment-10487</guid>
					<description>Не, не съм съгласен - погледнато отстрани историята ни е весела и поучителна. Самоиронията си е право на всеки човек ;)</description>
		<content:encoded><![CDATA[	<p>Не, не съм съгласен - погледнато отстрани историята ни е весела и поучителна. Самоиронията си е право на всеки човек <img src='http://blog.doncho.net/wp-images/smilies/icon_wink.gif' alt=';)' class='wp-smiley' /> </p>
]]></content:encoded>
				</item>
	<item>
 		<title>Comment on Visual&#8217;s 2005 безплатно by: donangel</title>
		<link>http://blog.doncho.net/?p=394#comment-10485</link>
		<pubDate>Sat, 10 Dec 2005 22:30:29 +0000</pubDate>
		<guid>http://blog.doncho.net/?p=394#comment-10485</guid>
					<description>@&lt;b&gt;Artem&lt;/b&gt;: Thanks a lot for this offtopic, but extremely important comment. I have taken measures to ensure that the users of KeePass PPC can download the files, while I'm trying to resolve my hosting provider's issue with sending mails from PHP scripts.
Needless to say, I am quite pissed off - this is second quite serious flaw in their service (for two years of usage).</description>
		<content:encoded><![CDATA[	<p>@<b>Artem</b>: Thanks a lot for this offtopic, but extremely important comment. I have taken measures to ensure that the users of KeePass PPC can download the files, while I&#8217;m trying to resolve my hosting provider&#8217;s issue with sending mails from PHP scripts.<br />
Needless to say, I am quite pissed off - this is second quite serious flaw in their service (for two years of usage).</p>
]]></content:encoded>
				</item>
	<item>
 		<title>Comment on Visual&#8217;s 2005 безплатно by: Artem Matevosyan</title>
		<link>http://blog.doncho.net/?p=394#comment-10484</link>
		<pubDate>Sat, 10 Dec 2005 20:56:34 +0000</pubDate>
		<guid>http://blog.doncho.net/?p=394#comment-10484</guid>
					<description>Hello! I'm sorry for the offtopic. I don't understand bulgarian and i'm posting this of I didn't find your mail to contact. I want to download the keepass for PocketPC and I can't the mailer doesn't send me a download link, though it says it hove done so. So could you contact me at Email or ICQ and explain how can I download the POcketPC port of my favourite password manager! Thanks a lot!</description>
		<content:encoded><![CDATA[	<p>Hello! I&#8217;m sorry for the offtopic. I don&#8217;t understand bulgarian and i&#8217;m posting this of I didn&#8217;t find your mail to contact. I want to download the keepass for PocketPC and I can&#8217;t the mailer doesn&#8217;t send me a download link, though it says it hove done so. So could you contact me at Email or ICQ and explain how can I download the POcketPC port of my favourite password manager! Thanks a lot!</p>
]]></content:encoded>
				</item>
</channel>
</rss>

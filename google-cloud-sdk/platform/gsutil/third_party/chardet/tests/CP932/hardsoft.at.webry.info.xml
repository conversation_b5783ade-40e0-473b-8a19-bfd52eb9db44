<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<base href="http://webryblog.biglobe.ne.jp" />
<meta http-equiv="Content-Type" content="text/html; charset=Shift_JIS" />
<meta http-equiv="Content-Script-Type" content="text/javascript" />
<meta http-equiv="Content-Style-Type" content="text/css" />
<meta name="description" content="�n�[�h�ȃ\�t�g�̘b��Perl 5.8.x �� shiftjis �ACP932�AMacJapanese �̈Ⴂ�Ɋւ���ڍ׋L���B�iPowered by BIGLOBE�E�F�u���u���O�jperldoc �� Encode::JP ��ǂނƁAShift JIS�ɂ�3��ނ���Bshiftjis - MS KanjiCP932 - CODE PAGE 932 = Shift JIS + MS/IBM vendor mappingsMacJapanese -  Shift JIS + Apple vendor mappings��������Ƃ킩���Ă��邯��ǁA�C���������̂Œ��ׂĂ݂��BWikipedia �ɏڂ���������������Bshiftjis - http://ja.w..." />
<meta name="robots" content="index,follow" />
<meta name="robots" content="NOYDIR,NOODP" />
<meta name="keywords" content="Shift JIS,Perl,���{��,�u���O,�u���O ����" />
<title>Perl 5.8.x �� shiftjis �ACP932�AMacJapanese �̈Ⴂ �n�[�h�ȃ\�t�g�̘b/�E�F�u���u���O</title>
<script type="text/javascript" src="/bblog.js"></script>
<script type="text/javascript" src="/include_logic.js"></script>
<script type="text/javascript" src="/footerlink/footerlink.js"></script>
<script type="text/javascript" src="/validator.js"></script>
<script type="text/javascript" src="http://hardsoft.at.webry.info/index_css.js"></script>
<script type="text/javascript" src="/articles_jss/blank.js"></script>
<script type="text/javascript" src="/snsMenu.js"></script>
<link rel="stylesheet" type="text/css" href="/css/articles.new/common.css" />
<link rel="alternate" type="application/rss+xml" title="RSS" href="http://hardsoft.at.webry.info/rss/index.rdf" />
<link rel="stylesheet" type="text/css" href="/css/articles.new/plain_basic.css" />
<link rel="alternate" media="handheld" type="application/xhtml+xml" href="http://m.webry.info/at/hardsoft/200802/article_1.htm" />
<script language="JavaScript">
<!--
  include_article_css("article");
// -->
</script>
<style type="text/css">
table.preview { border: 1px solid #000; }
td.preview { border-width: 0; }
</style>
</head>

<body>

	<!-- free space E07 -->

<!-- RDF area start -->
<!--
<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
         xmlns:dc="http://purl.org/dc/elements/1.1/"
         xmlns:trackback="http://madskills.com/public/xml/rss/module/trackback/">
<rdf:Description
    rdf:about="http://hardsoft.at.webry.info/200802/article_1.html"
    dc:identifier="http://hardsoft.at.webry.info/200802/article_1.html"
    dc:title="Perl 5.8.x �� shiftjis �ACP932�AMacJapanese �̈Ⴂ"
    dc:date="2008-02-05T00:14:00+09:00"
    trackback:ping="http://tb.bblog.biglobe.ne.jp/ap/tb/049eb1ab85" />
</rdf:RDF>
-->
<!-- RDF area end -->

<!-- main area start -->
<div id="container">
	<script type="text/javascript" src="/searchengine.js"></script>
	<script type="text/javascript" charset="Shift_JIS" src="/searchbox.js"></script>


<table id="link-bar" cellpadding="3" cellspacing="0" border="0">
	<!--colgroup>
	<col width="20%" />
	<col width="50%" />
	<col width="30%" />
	</colgroup-->
<script type="text/javascript">
<!--
  dispSpChangeButton();
// -->
</script>
    <tr>
      <td><a href="http://webryblog.biglobe.ne.jp/">�u���O ���O�C��</a></td>
      <td id="linkunit" align="center">
      <script type="text/javascript">
      <!--
		var firstView = true;
		function google_ad_request_done(ads) {}
		function google_radlink_request_done(radlinks) {
			if (firstView) {
				firstView = false;
				if (radlinks.length < 1) {
					document.write(' <a href="http://webryblog.biglobe.ne.jp/campaign/">�E�F�u���u���O�̃L�����y�[���ɉ��債�悤�I</a> ');
					return;
				}
				document.write("<h4>Ads by Google</h4>\n");
				for(i=0; i < radlinks.length; ++i) {
					document.write(' <a target="_top" href="http://map.bblog.biglobe.ne.jp/ap/tool/show_ads.jsp?ch=t&google_kw=' + radlinks[i].url_escaped_term + '&google_rt=' + radlinks[i].radlink_token + '&google_page_url=' + encodeURIComponent(window.location) + '">' + radlinks[i].term + '</a>\n');
				}
			}
		}
      // -->
      </script>
    <script type="text/javascript">
      <!--
        google_ad_client = "ca-nec_radlinks_js";
        google_ad_channel = 'top';
        google_page_url = 'http://hardsoft.at.webry.info/200802/article_1.html';
        google_language = 'ja';
        google_encoding = 'sjis';
        google_ad_output = 'js';
        google_safe = 'high';
        google_num_radlinks = '3';
        google_max_radlink_len = '20';
        google_max_num_ads = '0';
        google_rl_mode = 'default';
        google_ad_section = 'section1 section2';
      // -->
      </script>
      [ <script type="text/javascript" src="http://pagead2.googlesyndication.com/pagead/show_ads.js"></script> ]
      </td>
      <td align="right">
      <a href="http://hardsoft.at.webry.info/">�g�b�v</a>&nbsp;
      <a href="http://hardsoft.at.webry.info/profile.html">�v���t</a>&nbsp;
      <a href="http://hardsoft.at.webry.info/friendlist.html">�t�����h</a>&nbsp;
      <a href="http://hardsoft.at.webry.info/joincirclelist.html">�T�[�N��</a>
      </td></tr>
</table>

  <!-- title banner start -->
  <div id="subbanner">
    <h1><a href="http://hardsoft.at.webry.info/" title="���̃u���O�̃g�b�v��">�n�[�h�ȃ\�t�g�̘b</a></h1>

    <!-- top links start -->
    <script type="text/javascript">
    <!--
      topLinks();
    // -->
    </script>
      <!-- top links end -->

<script language="JavaScript">
<!--
  loginLink("bblog.sso.biglobe.ne.jp/ap");
// -->
</script>

  </div>
  <!-- title banner end -->

  <!-- counter start -->
  <p id="counter"><img id="counterimg" src="http://webryblog.biglobe.ne.jp/cgi-bin/Count.cgi?trgb=ffffff&ft=0&df=h/a/hardsoft.at.webry.info/NEWS/049eb1ab85.dat" height="9" alt="�A�N�Z�X�J�E���^" /></p>
  <!-- counter end -->

	<!-- free space E08 -->

  <!-- utilities2 start -->
  <div id="utilities2" class="utiloff">
    <script type="text/javascript"><!--
        forced_disp_ad2();
    // --></script>
    <script type="text/javascript" src="/sidebar.js"></script>
    <script type="text/javascript">
    <!--
      load_sidebar_info('hardsoft.at.webry.info');
    // -->
    </script>

    <script type="text/javascript"><!--
        include_compo_left(1);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(2);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(3);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(4);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(5);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(6);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(7);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(8);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(9);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(10);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(11);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(12);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(13);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(14);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(15);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(16);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(17);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(18);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(19);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo_left(20);
    // --></script>
    </div>
  </div>
  <!-- utilities2 end -->

  <!-- blog start -->
  <div id="blog">

	<script type="text/javascript" src="http://hardsoft.at.webry.info/themetab_data.js"></script>
	<script type="text/javascript">
	<!--
		insertThemeTabNews('hardsoft.at.webry.info');
	// -->
	</script>

	<!-- free space E09 -->

    <!-- blog-head start -->
    <div id="blog-head">
      <h2>
    <a href="javascript:void(0)" onclick="helpWin('general_cont.html#rss')">
    <img width="30" height="14" border="0" hspace="1" align="right" src="/images/img/help.gif" alt="help" /></a>
    <a href="http://hardsoft.at.webry.info/rss/index.rdf">
    <img width="30" height="14" border="0" hspace="1" align="right" src="/images/img/rss.gif" alt="RSS" /></a>
  <!-- google_ad_section_start(name=section1) -->
    Perl 5.8.x �� shiftjis �ACP932�AMacJapanese �̈Ⴂ
  <!-- google_ad_section_end(name=section1) -->
      </h2>

      <p class="date">
        <a href="http://hardsoft.at.webry.info/200801/article_6.html" title="�O�L��">&lt;&lt;</a>
        &nbsp;
        �쐬���� �F 2008/02/05 00:14
        &nbsp;
        <a href="http://hardsoft.at.webry.info/200802/article_2.html" title="��L��">&gt;&gt;</a>
      </p>
      <p class="response" style="text-align:right">
            <a href="http://hardsoft.at.webry.info/200802/article_1.html#kimochi">
            <img src="/images/article/kimochi_aha.png" width="24" height="24" border="0" alt="�Ȃ�قǁi�[���A�Q�l�ɂȂ����A�w�[�j" title="�Ȃ�قǁi�[���A�Q�l�ɂȂ����A�w�[�j" />
            �u���O�C���� 4</a> /
            <a href="http://hardsoft.at.webry.info/200802/article_1.html#trackback">�g���b�N�o�b�N 1</a> /
      <a href="http://hardsoft.at.webry.info/200802/article_1.html#comment">�R�����g 0</a></p>
    </div>
    <!-- blog-head end -->

    <!-- blog-body start -->
    <table id="blog-body-container" cellpadding="0" cellspacing="0" border="0">
    <tr><td id="blog-body">
    <!-- google_ad_section_start(name=section2) -->
      perldoc �� Encode::JP ��ǂނƁAShift JIS�ɂ�3��ނ���B<br />
<ul><li>shiftjis - MS Kanji</li><li>CP932 - CODE PAGE 932 = Shift JIS + MS/IBM vendor mappings</li><li>MacJapanese -  Shift JIS + Apple vendor mappings</li></ul><br />
��������Ƃ킩���Ă��邯��ǁA�C���������̂Œ��ׂĂ݂��B<br />
Wikipedia �ɏڂ���������������B<br />
<ul><li>shiftjis - <A HREF="http://ja.wikipedia.org/wiki/Shift_JIS" TARGET="_blank">http://ja.wikipedia.org/wiki/Shift_JIS</A></li><li>CP932 - <A HREF="http://ja.wikipedia.org/wiki/CP932" TARGET="_blank">http://ja.wikipedia.org/wiki/CP932</A></li><li>MacJapanese - <A HREF="http://ja.wikipedia.org/wiki/MacJapanese" TARGET="_blank">http://ja.wikipedia.org/wiki/MacJapanese</A></li></ul><br />
����3�� encoding �ɂ��ċ�̓I�ɔc��������@���l���Ă݂��B3��ނ�encoding �Ɋ܂܂�镶����Unicode�e�L�X�g�t�@�C���ɕۑ����� Perl �ł��ꂼ��� encoding ��Shift JIS�e�L�X�g�t�@�C�����쐬���āA�ǂ̕������G���[�ɂȂ邩���ׂĂ݂�΂����B<br />
����ȃv���O����������Ă݂��B<br />
<br />
<blockquote><code>use&nbsp;utf8;<br />
use&nbsp;strict;<br />
use&nbsp;warnings;<br />
<br />
open&nbsp;my&nbsp;$fin,&nbsp;"&lt;:encoding(utf16BE)",&nbsp;"test_u2s_utf16be.txt"&nbsp;or&nbsp;die;<br />
my&nbsp;@lines&nbsp;=&nbsp;&lt;$fin&gt;;<br />
close&nbsp;$fin;<br />
$lines[0]&nbsp;=~&nbsp;s/\x{feff}//;<br />
exit;<br />
foreach&nbsp;my&nbsp;$name&nbsp;(&nbsp;qw/shiftjis&nbsp;cp932&nbsp;MacJapanese/&nbsp;){<br />
&nbsp;&nbsp;open&nbsp;&nbsp;my&nbsp;$fout,&nbsp;"&gt;:encoding($name)","test_u2s_$name.txt"&nbsp;or&nbsp;die;<br />
&nbsp;&nbsp;print&nbsp;$fout&nbsp;@lines;<br />
&nbsp;&nbsp;close&nbsp;$fout;<br />
}<br />
</code></blockquote><br />
<br />
���̃v���O���������s����ƁAUnicode �̃t�@�C����ǂݍ����3��ނ̃e�L�X�g�t�@�C�����o�͂���B<br />
<br />
���̂悤�ȃe�L�X�g�t�@�C�����쐬����UTF-16BE�Ńt�@�C������test_u2s_utf16be.txt�Ƃ��ĕۑ������B<br />
<blockquote><br />
NEC: JIS X 0212-1990<br />
&#8470;<br />
NEC: JIS X 0213:2004<br />
&#9312; &#9313; &#9314; &#9315; &#9316; &#9317; &#9318; &#9319; &#9320; &#9321; &#9322; &#9323; &#9324; &#9325; &#9326; &#9327; &#9328; &#9329; &#9330; &#9331; &#8544; &#8545; &#8546; &#8547; &#8548; &#8549; &#8550; &#8551; &#8552; &#8553; &#13129; &#13076; &#13090; &#13133; &#13080; &#13095; &#13059; &#13110; &#13137; &#13143; &#13069; &#13094; &#13091; &#13099; &#13130; &#13115; &#13212; &#13213; &#13214; &#13198; &#13199; &#13252; &#13217; &#13179; &#12317; &#12319; &#8470; &#13261; &#8481; &#12964; &#12965; &#12966; &#12967; &#12968; &#12849; &#12850; &#12857; &#13182; &#13181; &#13180; &#8786; &#8801; &#8747; &#8750; &#8730; &#8869; &#8736; &#8735; &#8895; &#8757; &#8745; &#8746;<br />
<br />
IBM: &#20154;&#21517;&#29992;&#28450;&#23383;&#65288;2004&#24180;&#25913;&#27491;&#65289;<br />
&#23532; &#34224; &#28712; &#22686; &#24503; &#36084; &#63785; &#27243; &#40657; &#32214; &#64034; &#64026; &#64025; &#64038; &#64027; &#37086;<br />
<br />
IBM: JIS X 0212-1990<br />
&#8470; &#20008; &#20193; &#20224; &#20227; &#20281; &#20310; &#20362; &#20370; &#20372; &#20378; &#20425; &#20429; &#20479; &#20510; &#20514; &#20544; &#20546; &#20550; &#20592; &#20628; &#20696; &#20810; &#20836; &#20893; &#20926; &#21013; &#21148; &#21158; &#21167; &#21184; &#21211; &#21248; &#21362; &#21395; &#21426; &#21469; &#21660; &#21673; &#21759; &#21894; &#22373; &#22444; &#22471; &#22472; &#22706; &#22795; &#22867; &#22875; &#22877; &#22883; &#22948; &#22970; &#23382; &#23488; &#23512; &#23582; &#23718; &#23738; &#23847; &#23874; &#23917; &#23992; &#23993; &#24016; &#24353; &#24372; &#24389; &#24423; &#24542; &#24669; &#24714; &#24789; &#24798; &#24818; &#24849; &#24880; &#24887; &#24984; &#25107; &#25254; &#25589; &#25696; &#25757; &#25806; &#26112; &#26121; &#26133; &#26142; &#26148; &#26158; &#26161; &#26199; &#26201; &#26363; &#26227; &#26265; &#26272; &#26290; &#26303; &#26362; &#26382; &#26470; &#26555; &#26560; &#26706; &#26692; &#26824; &#26831; &#26984; &#27032; &#27106; &#27184; &#27206; &#27251; &#27262; &#27364; &#27606; &#27711; &#27740; &#27759; &#27782; &#27866; &#27908; &#28015; &#28039; &#28054; &#28076; &#28111; &#28156; &#28199; &#28217; &#28220; &#28252; &#28351; &#28552; &#28597; &#28661; &#28677; &#28679; &#28805; &#28843; &#28932; &#28943; &#28998; &#28999; &#29020; &#29121; &#29182; &#29361; &#29374; &#29476; &#29559; &#29629; &#29641; &#29650; &#29654; &#29667; &#29685; &#29703; &#29734; &#29737; &#29738; &#29742; &#29794; &#29833; &#29855; &#29999; &#30063; &#30363; &#30364; &#30374; &#30534; &#30753; &#30798; &#30820; &#30842; &#31124; &#31131; &#31441; &#31467; &#31646; &#32072; &#32092; &#32183; &#32338; &#32394; &#32583; &#32673; &#33537; &#33663; &#33735; &#33782; &#33864; &#33972; &#34012; &#34131; &#34137; &#34155; &#35061; &#35100; &#35346; &#35383; &#35449; &#35495; &#35518; &#35551; &#35574; &#35667; &#36080; &#36114; &#36559; &#36967; &#37159; &#37335; &#37338; &#37342; &#37348; &#37349; &#37357; &#37358; &#37382; &#37386; &#37392; &#37433; &#37434; &#37436; &#37440; &#37454; &#37457; &#37465; &#37479; &#37495; &#37496; &#37512; &#37543; &#37584; &#37587; &#37589; &#37591; &#37593; &#37600; &#37607; &#37625; &#37627; &#37631; &#37634; &#37661; &#37662; &#37665; &#37669; &#37704; &#37719; &#37744; &#37796; &#37830; &#37854; &#37880; &#37937; &#37957; &#37960; &#38557; &#38575; &#38707; &#38723; &#38735; &#38741; &#38999; &#39013; &#39207; &#39502; &#39644; &#39797; &#39823; &#39857; &#39867; &#39936; &#40299; &#40304; &#40473;<br />
<br />
IBM JIS X 0213:2004<br />
&#8560; &#8561; &#8562; &#8563; &#8564; &#8565; &#8566; &#8567; &#8568; &#8569; &#8544; &#8545; &#8546; &#8547; &#8548; &#8549; &#8550; &#8551; &#8552; &#8553; &#65506; &#65508; &#65287; &#65282; &#12849; &#8470; &#8481; &#8757; &#20008; &#20193; &#20227; &#20310; &#20362; &#20372; &#20425; &#20544; &#20628; &#20810; &#20893; &#21013; &#21211; &#21248; &#21255; &#21395; &#21426; &#21660; &#21673; &#21759; &#21894; &#64015; &#22471; &#22472; &#64016; &#22686; &#22877; &#22948; &#23382; &#23512; &#23532; &#23582; &#23738; &#23847; &#64017; &#23891; &#23917; &#23992; &#24372; &#24389; &#24423; &#24503; &#24542; &#24789; &#24880; &#24887; &#25589; &#25696; &#25806; &#26112; &#26121; &#26133; &#26142; &#26148; &#26161; &#26363; &#26201; &#26213; &#26227; &#26272; &#26290; &#26362; &#63785; &#26470; &#26555; &#26560; &#26625; &#26706; &#26824; &#26984; &#64020; &#27106; &#27243; &#27251; &#27364; &#27606; &#27740; &#27782; &#27908; &#28039; &#28076; &#28156; &#28252; &#28552; &#28661; &#28677; &#28712; &#28805; &#28843; &#28943; &#28998; &#28999; &#29020; &#64021; &#29121; &#29182; &#29374; &#64022; &#29641; &#29654; &#29667; &#29703; &#29734; &#29738; &#29742; &#29794; &#29833; &#29855; &#29953; &#29999; &#30063; &#30363; &#30366; &#30374; &#30534; &#30798; &#64025; &#64026; &#64027; &#31467; &#31646; &#32072; &#32092; &#32160; &#32214; &#32338; &#32394; &#32583; &#33537; &#33634; &#33735; &#33864; &#33972; &#34137; &#64031; &#34224; &#35061; &#35100; &#35346; &#35383; &#35449; &#35495; &#35551; &#35574; &#64034; &#36084; &#36114; &#37086; &#64038; &#37141; &#37159; &#37335; &#37348; &#37349; &#37357; &#37392; &#37433; &#37434; &#37436; &#37440; &#37496; &#37512; &#37587; &#37600; &#37631; &#37665; &#37669; &#37704; &#37744; &#37830; &#37854; &#63964; &#38557; &#38707; &#38715; &#38733; &#38735; &#38999; &#39013; &#39326; &#39502; &#39794; &#39797; &#39823; &#39936; &#40304; &#40657; &#20224; &#20370; &#20378; &#20510; &#20514; &#20546; &#20550; &#20592; &#21158; &#21167; &#21284; &#21642; &#22444; &#22795; &#22875; &#23488; &#23718; &#23874; &#23993; &#24714; &#24818; &#24984; &#25254; &#25757; &#26199; &#26265; &#26303; &#26692; &#64019; &#26831; &#27032; &#27184; &#27206; &#27262; &#27711; &#27866; &#28111; &#28199; &#28220; &#28351; &#28597; &#28679; &#28859; &#28932; &#29361; &#29559; &#30364; &#30753; &#30820; &#31024; &#31124; &#31131; &#31441; &#31463; &#32183; &#33663; &#34012; &#34131; &#64032; &#64033; &#35518; &#35711; &#36080; &#64036; &#37338; &#37358; &#37386; &#37454; &#37457; &#37465; &#37479; &#37543; &#37584; &#37591; &#37593; &#37607; &#37625; &#37627; &#37634; &#37661; &#37662; &#37719; &#37957; &#38575; &#38741; &#39644; &#39857; &#40299; &#40473;<br />
<br />
MAC:<br />
&#9312; &#9313; &#9314; &#9315; &#9316; &#9317; &#9318; &#9319; &#9320; &#9321; &#9322; &#9323; &#9324; &#9325; &#9326; &#9327; &#9328; &#9329; &#9330; &#9331;<br />
&#9332; &#9333; &#9334; &#9335; &#9336; &#9337; &#9338; &#9339; &#9340; &#10121; &#9342; &#9343; &#9344; &#9345; &#9346; &#9347; &#9348; &#9349; &#9350; &#9351;<br />
&#10102; &#10123; &#10104; &#10105; &#10106; &#10107; &#10108; &#10109; &#10110; &#10111; <br />
&#9352; &#9353; &#9354; &#9355; &#9356; &#9357; &#9358; &#9359; &#9360; &#9361; &#9362; &#9363; &#9364; &#9365; &#9366; &#9367; &#9368; &#9369; &#9370; &#9371;<br />
&#8544; &#8545; &#8546; &#8547; &#8548; &#8549; &#8550; &#8551; &#8552; &#8553;<br />
&#13198; &#13199; &#13212; &#13213; &#13217; &#13252;<br />
&#8470; &#8481;<br />
&#9824;  &#9828;  &#9829;  &#9825;  &#9830;  &#9826;  &#9827;  &#9831; <br />
&#12320; &#9742; &#12292; <br />
&#9757; &#9759; &#9758; &#9756; &#8679;  &#8681;  &#8680;  &#8678;<br />
&#12857; &#12849; &#12850;<br />
&#12964; &#12966; &#12968; &#12967; <br />
&#13090; &#13059; &#13069; &#13076; &#13080; &#13090; &#13183;<br />
&#8750;  <br />
&#12436; &#12535; &#12536; &#12537; &#12538; <br />
</blockquote><br />
<br />
�ϊ��ł��Ȃ����������� \x{0123}�Ƃ���������16�i���ŕ\�������B<br />
<br />
test_u2s_shiftjis.txt �̌��ʂ͎��̒ʂ�B<br />
<blockquote><br />
NEC: JIS X 0212-1990<br />
\x{2116}<br />
NEC: JIS X 0213:2004<br />
\x{2460} \x{2461} \x{2462} \x{2463} \x{2464} \x{2465} \x{2466} \x{2467} \x{2468} \x{2469} \x{246a} \x{246b} \x{246c} \x{246d} \x{246e} \x{246f} \x{2470} \x{2471} \x{2472} \x{2473} \x{2160} \x{2161} \x{2162} \x{2163} \x{2164} \x{2165} \x{2166} \x{2167} \x{2168} \x{2169} \x{3349} \x{3314} \x{3322} \x{334d} \x{3318} \x{3327} \x{3303} \x{3336} \x{3351} \x{3357} \x{330d} \x{3326} \x{3323} \x{332b} \x{334a} \x{333b} \x{339c} \x{339d} \x{339e} \x{338e} \x{338f} \x{33c4} \x{33a1} \x{337b} \x{301d} \x{301f} \x{2116} \x{33cd} \x{2121} \x{32a4} \x{32a5} \x{32a6} \x{32a7} \x{32a8} \x{3231} \x{3232} \x{3239} \x{337e} \x{337d} \x{337c} �� �� �� \x{222e} �� �� �� \x{221f} \x{22bf} �� �� ��<br />
(...�ȉ��ȗ�...) <br />
</blockquote><br />
�قځA�ϊ��ł��Ȃ������B�g�������͂قڕϊ����Ȃ��B���Ɉˑ�����g�����������i�Ƀ`�F�b�N���������Ɏg����B<br />
<br />
HP-UX�Ȃ񂩂Ƀt�@�C���������Ă��Ƃ��͂�����������Ȃ��B<br />
<br />
test_u2s_cp932.txt�̌��ʂ͎��̒ʂ�B<br />
<br />
<blockquote><br />
NEC: JIS X 0212-1990<br />
��<br />
NEC: JIS X 0213:2004<br />
�@ �A �B �C �D �E �F �G �H �I �J �K �L �M �N �O �P �Q �R �S �T �U �V �W �X �Y �Z �[ �\ �] �_ �` �a �b �c �d �e �f �g �h �i �j �k �l �m �n �o �p �q �r �s �t �u �~ �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� ��<br />
<br />
IBM: �l���p�����i2004�N�����j<br />
�� �� �P �� �� �� �� �� �K �� �� �� �~ �� �� ��<br />
<br />
IBM: JIS X 0212-1990<br />
�� �h �i �k �l �m �n �p �o �r �q �a �s �v �w �u �t �z �x �y �{ �} �~ �� �� �� �� �� �� �w �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �g �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �c �� �� �f �� �� �� �� �� �� �� �� �� �� �� �� �d �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �@ �A �B �E �H �F �I �G �J �K �L �M �N �O �Q �R �T �S �V �W �U �Y �Z �[ �\ �] �_ �` �a �d �b �c �f �e �g �i �h �j �k �l �m �� �o �s �q �t �v �x �y �z �{ �� �� �� �� �� �� �� �� �� �\ �� �� �� �� �� �� �� �� �` �� �� �� �� �] �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �_ �� �� �� �� �� �� �� �� �e �� �� �� �� �� �� �� �^ �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �@ �A �C �D �E �F �H �G �J<br />
<br />
IBM JIS X 0213:2004<br />
�@ �A �B �C �D �E �F �G �H �I �T �U �V �W �X �Y �Z �[ �\ �] �� �U �V �W �� �� �� �� �h �i �l �n �p �r �a �t �{ �~ �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �g �� �� �� �� �� �� �� �� �� �� �� �� �� �� �c �f �� �� �� �� �� �� �� �� �� �� �� �� �d �� �� �� �� �� �� �� �� �� �� �� �A �E �G �K �M �N �P �Q �R �S �V �W �U �X �Y �Z �\ �^ �a �b �c �e �g �h �j �k �l �m �n �� �o �s �r �t �v �y �~ �� �� �� �� �� �� �� �� �� �\ �� �� �� �� �� �� �� �� �� �� �] �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �_ �� �� �� �� �� �^ �� �� �� �� �� �� �� �� �� �� �� �� �� �B �A �C �F �G �K �k �o �q �w �u �z �x �y �� �w �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �B �H �I �J �L �O �b �T �[ �_ �q �x �z �| �� �� �� �� �� �� �` �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �� �e �� �� �� �� �� �� �� �� �@ �D �H �J<br />
<br />
MAC:<br />
�@ �A �B �C �D �E �F �G �H �I �J �K �L �M �N �O �P �Q �R �S<br />
\x{2474} \x{2475} \x{2476} \x{2477} \x{2478} \x{2479} \x{247a} \x{247b} \x{247c} \x{2789} \x{247e} \x{247f} \x{2480} \x{2481} \x{2482} \x{2483} \x{2484} \x{2485} \x{2486} \x{2487}<br />
\x{2776} \x{278b} \x{2778} \x{2779} \x{277a} \x{277b} \x{277c} \x{277d} \x{277e} \x{277f} <br />
\x{2488} \x{2489} \x{248a} \x{248b} \x{248c} \x{248d} \x{248e} \x{248f} \x{2490} \x{2491} \x{2492} \x{2493} \x{2494} \x{2495} \x{2496} \x{2497} \x{2498} \x{2499} \x{249a} \x{249b}<br />
�T �U �V �W �X �Y �Z �[ �\ �]<br />
�r �s �o �p �u �t<br />
�� ��<br />
\x{2660}  \x{2664}  \x{2665}  \x{2661}  \x{2666}  \x{2662}  \x{2663}  \x{2667} <br />
\x{3020} \x{260e} \x{3004} <br />
\x{261d} \x{261f} \x{261e} \x{261c} \x{21e7}  \x{21e9}  \x{21e8}  \x{21e6}<br />
�� �� ��<br />
�� �� �� �� <br />
�a �e �i �` �c �a \x{337f}<br />
��  <br />
�� \x{30f7} \x{30f8} \x{30f9} \x{30fa} <br />
</blockquote><br />
<br />
Windows �͈̔͂ŕ����R�[�h����������ɂ͈�Ԃ����Ǝv����B<br />
�g�ѓd�b�p��CGI��CP932���g���l�����邯��ǋ@��ʑΉ������Ȃ��̂Ȃ� shiftjis�ɂ��� encoding �G���[��������Ə��������ق���������������Ȃ��B���̌��́A���������ڂ������ׂȂ��Ƃ킩��Ȃ��Ȃ��c�B<br />
<br />
test_u2s_MacJapanese.txt �̌��ʂ́AMac���Ȃ��̂ŕ\���ł��Ȃ��B<br />
IBM�g�������͑S�ł������B<br />
<br />
�Ȃ񂾂��񂾂�Shift JIS�̓g���u�����[�J�[�Ȃ̂� Shift JIS�̃e�L�X�g�t�@�C�����������瑁�߂� Unicode�ɕϊ����Ďg�����ق����_��������Ǝv����B<br />

    <!-- google_ad_section_end(name=section2) -->
      </td></tr>
    </table>
    <!-- blog-body end -->

	<!-- free space E04 -->

    <!-- blog-foot start -->
    <div id="blog-foot">

      <!-- theme start -->
    <script type="text/javascript" src="http://webryblog.biglobe.ne.jp/beacon/theme/blogentrycount/themeid/275a986d99/url/hardsoft.at.webry.info/200802/article_1.html"></script>
    <script type="text/javascript" src="http://webryblog.biglobe.ne.jp/beacon/theme/blogentrycount/themeid/2d5396c6b9/url/hardsoft.at.webry.info/200802/article_1.html"></script>
    <script type="text/javascript" src="http://webryblog.biglobe.ne.jp/beacon/theme/blogentrycount/themeid/75575704c2/url/hardsoft.at.webry.info/200802/article_1.html"></script>
      <form name="themeradar" method="post" action="http://map.bblog.biglobe.ne.jp/ap/themeradar.do">
      <input type="hidden" name="newsid" value="049eb1ab85" />
      <table id="theme">
      <tr valign="top">
        <td>
        <h2>�e�[�}</h2>
        <ul>
        <li><a href="http://hardsoft.at.webry.info/theme/275a986d99.html"><strong>Shift JIS</strong></a></li>
        <li><a href="http://hardsoft.at.webry.info/theme/2d5396c6b9.html"><strong>Perl</strong></a></li>
        <li><a href="http://hardsoft.at.webry.info/theme/75575704c2.html"><strong>���{��</strong></a></li>
        </ul>
        </td>
        <td>
        <h2>�֘A�e�[�} <a href="javascript:document.themeradar.submit()">�ꗗ</a></h2>
        <ul>
        <li><a href="http://webryblog.biglobe.ne.jp/3/3/331f1b774e.html"><strong>�N�C�Y</strong></a></li>
        <li><a href="http://webryblog.biglobe.ne.jp/e/0/e067f2104c.html"><strong>����</strong></a></li>
        <li><a href="http://webryblog.biglobe.ne.jp/2/7/27dd3423e6.html"><strong>�p��</strong></a></li>
        </ul>
        </td>
        </tr>
      </table>
      </form>
      <!-- theme end -->

	<!-- free space E05 -->


      <script type="text/javascript">
      <!--
      google_ad_client = 'ca-nec-cgm_js';
      google_ad_output = 'js';
      google_ad_type = 'text';
      google_max_num_ads = 4;
      google_safe = 'high';
      google_language = 'ja';
      google_encoding = 'sjis';
      google_ad_section = 'section1 section2';

      google_ad_channel = 'ad4';
      // -->
      </script>

      <script type="text/javascript" src="/searchengine.js"></script>
      <script type="text/javascript">
      <!--
      if (searchEngineFlag) {
          google_max_num_ads = '6';
          google_ad_type = 'text,image,flash,html';
          google_image_size = '336x280';
      }
      if(! (navigator.appVersion.charAt(0) <= 4 && navigator.appName.charAt(0) == "N"))
        document.write('<script type="text/javascript" src="http://pagead2.googlesyndication.com/pagead/show_ads.js"></script\>');
      function google_ad_request_done(google_ads) {
        var i;
        if( google_ads[0] ){
document.write("<div class=\"g-ads\">");
document.write("<h2><a href=\""+google_info.feedback_url+"\" target=\"_blank\">Ads by Google</a></h2>");
document.write("<ul>");
        }

        // GoogleAdsense
		for(i = 0; i < google_ads.length; ++i) {
          if (! google_ads[i] ) { break ; }
            if ( google_ads[i].type == 'image' ) {
              document.write("<li><a href=\"" +google_ads[i].url+ "\"><img src=\"" +google_ads[i].image_url+ "\" width=\"" +google_ads[i].image_width+ "\" height=\"" +google_ads[i].image_height+ "\" border=\"0\" /></a><br /><a href=\"" +google_ads[i].url+ "\">" + google_ads[i].visible_url + "</a></li>");
            } else if ( google_ads[i].type == 'flash' ) {
              document.write( '<li><OBJECT classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000" codebase="http://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=6,0,0,0" WIDTH="'+google_ads[i].image_width+'" HEIGHT="'+google_ads[i].image_height+'"><PARAM NAME=movie VALUE="'+google_ads[i].image_url+'"><PARAM NAME=quality VALUE="high"><PARAM NAME="AllowScriptAccess" VALUE="never"><EMBED src="'+google_ads[i].image_url+'" WIDTH="'+google_ads[i].image_width+'" HEIGHT="'+google_ads[i].image_height+'" TYPE="application/x-shockwave-flash" AllowScriptAccess="never" PLUGINSPAGE="http://www.macromedia.com/go/getflashplayer"></EMBED></OBJECT><br /><a href="' +google_ads[i].url+ '">' + google_ads[i].visible_url + '</a></li>' );
            } else if ( google_ads[i].type == 'html' ) {
              document.write( google_ads[i].snippet + '<br /><a href="' +google_ads[i].url+ '">' + google_ads[i].visible_url + '</a>' );
            } else {
              document.write("<li><a href=\"" + google_ads[i].url + "\">");
              document.write("<span class=\"line1\">" + google_ads[i].line1 + "</span></a><br />");
              document.write("<span class=\"line2\">" + google_ads[i].line2 + "&nbsp;" + google_ads[i].line3 + "</span><br />");
              document.write("<span class=\"url\"><a href=\"" + google_ads[i].url + "\">" + google_ads[i].visible_url + "</a></span></li>");
            }
        }

        if( google_ads[0] ){
          document.write("</ul>");
          document.write("</div>");
        }
      }
      // -->
      </script>


      <!-- related theme start -->
<div id="related">
      <h2>�����e�[�}�̃u���O�L��</h2>


		<h3>
            �uShift JIS�v
			<p class="links" style="float:right">��&nbsp;<a
			href="http://hardsoft.at.webry.info/theme/275a986d99.html">�����ƌ���</a>&nbsp;			</p>
		</h3>
        <ul>
            <script type="text/javascript" src="http://hardsoft.at.webry.info/theme/275a986d99.js"></script>
            <script type="text/javascript">
          <!--
            include_relatednews_list2(3, "http://hardsoft.at.webry.info/200802/article_1.html");
          // -->
          </script>
        </ul>

		<h3>
            �uPerl�v
			<p class="links" style="float:right">��&nbsp;<a
			href="http://hardsoft.at.webry.info/theme/2d5396c6b9.html">�����ƌ���</a>&nbsp;			<a href="http://webryblog.biglobe.ne.jp/2/d/2d5396c6b9.html">�݂�Ȃ́uPerl�v�u���O</a>			</p>
		</h3>
        <ul>
            <script type="text/javascript" src="http://hardsoft.at.webry.info/theme/2d5396c6b9.js"></script>
            <script type="text/javascript">
          <!--
            include_relatednews_list2(3, "http://hardsoft.at.webry.info/200802/article_1.html");
          // -->
          </script>
        </ul>

		<h3>
            �u���{��v
			<p class="links" style="float:right">��&nbsp;<a
			href="http://hardsoft.at.webry.info/theme/75575704c2.html">�����ƌ���</a>&nbsp;			<a href="http://webryblog.biglobe.ne.jp/7/5/75575704c2.html">�݂�Ȃ́u���{��v�u���O</a>			</p>
		</h3>
        <ul>
            <script type="text/javascript" src="http://hardsoft.at.webry.info/theme/75575704c2.js"></script>
            <script type="text/javascript">
          <!--
            include_relatednews_list2(3, "http://hardsoft.at.webry.info/200802/article_1.html");
          // -->
          </script>
        </ul>
</div>
      <!-- related theme end -->

    </div>
    <!-- blog-foot end -->

    <!-- navigation start -->
    <table class="navi">
    <tr><td align="right" >
      <a href="http://hardsoft.at.webry.info/200801/article_6.html" title="�u���O�̑O�L��">&lt;&lt; �O�L��(2008/01/31)</a>
      </td>
      <td align="center"><a href="http://hardsoft.at.webry.info/" title="���̃u���O�̃g�b�v��">�u���O�̃g�b�v��</a></td>
      <td align="left"  >
      <a href="http://hardsoft.at.webry.info/200802/article_2.html" title="�u���O�̌�L��">��L��(2008/02/08) &gt;&gt;</a>
      </td>
    </tr></table>
    <!-- navigation end -->

    <!-- archives start -->
    <div id="archives">
      <h2>���ʃ����N</h2>
      <ul>
    <script type="text/javascript" src="http://hardsoft.at.webry.info/monthly_link.js"></script>
    <script type="text/javascript">
    <!--
      include_monthly_link_top(12);
    // -->
    </script>
      </ul>
    </div>
    <!-- archives end -->

    		<!-- kimochidama start -->
		    <div id="kimochi" class="box-unit">
			        <h2 class="h2-general">
				            �u���O�C����
				            <a href="javascript:void(0)" onclick="helpWin('general_cont.html#blog_feeling')"><img src="../images/img/help.gif" width="30" height="14" border="0" align="absmiddle" /></a>
			        </h2>

			        <div class="box">
								            <strong>�N���b�N���ċC������`���悤�I</strong><br />
            ���O�C�����ăN���b�N����΁A�����̃u���O�ւ̃����N���t���܂��B<br /><a href="http://webryblog.biglobe.ne.jp/">�����O�C����</a>
            <script type="text/javascript">
                <!--
                  var blogtop_tooldomain = "bblog.sso.biglobe.ne.jp/ap";
                  include_feeling_btn('049eb1ab85');
                // -->
            </script>
									            <div id="k-list">
					                �C���ʐ� �F 4
										                <div class="k-unit">
											                    <img src="/images/article/kimochi_aha.png"	 width="24" height="24" alt="�Ȃ�قǁi�[���A�Q�l�ɂȂ����A�w�[�j" />
																		                    <img src="/images/article/kimochi_aha.png"	 width="24" height="24" alt="�Ȃ�قǁi�[���A�Q�l�ɂȂ����A�w�[�j" />
																		                    <img src="/images/article/kimochi_aha.png"	 width="24" height="24" alt="�Ȃ�قǁi�[���A�Q�l�ɂȂ����A�w�[�j" />
																		                    <div class="ns-bg"></div>
					                </div>
										                <div class="k-unit">
											                    <img src="/images/article/kimochi_funny.png"	 width="24" height="24" alt="�ʔ���" />
																		                    <div class="ns-bg"></div>
					                </div>
					            </div>
							        </div>
    </div>
	    	<!-- kimochidama end -->

    <!-- trackback start -->
    <div id="trackback">
      <h2>�g���b�N�o�b�N<span class="supple">�i1���j</span></h2>
      <table cellpadding="0" cellspacing="0" border="0">
      <tr><th class="cont">�^�C�g�� �i�{���j</th>
        <th class="sub">�u���O���^����</th></tr>

      <tr><td class="cont" style="vertical-align:top"><a href="http://hardsoft.at.webry.info/200505/article_5.html">ActivePerl 5.8.X�@�œ��{�ꏈ��������</a>
        <blockquote style="margin-top:8px">
        Windows �� ActivePerl 5.8.X �ȍ~���g���ꍇ�A���̂悤�ɐ錾����ƁA�W�����o�͂�Perl/Tk�����{��Ŏg����悤�ɂȂ�B�\�[�X�v���O������ Shift-JIS�R�[�h�ŕۑ��ł���悤�ɂȂ��ĕ֗��B
        <span class="more"><a href="http://hardsoft.at.webry.info/200505/article_5.html">...����������</a></span>
        </blockquote></td>
        <td class="sub">�n�[�h�ȃ\�t�g�̘b<br />2008/02/06 22:24</td></tr>
      </table>
      <form name="atcl1" method="post" action="https://bblog.sso.biglobe.ne.jp/ap/tool/bblogwrite.do" onsubmit="javascript:if(articlePageValueCheck1() == false){return false;}">
      <div id="trackback_form">
      <div class="tbkurl">
        <h2>�g���b�N�o�b�N�pURL
        <a href="javascript:void(0)" onclick="helpWin('general_cont.html#trackback_url')"><img src="/images/img/help.gif" width="30" height="14" border="0" align="absmiddle" alt="help" /></a></h2>
        <input type="text" class="urlstring" value="http://tb.bblog.biglobe.ne.jp/ap/tb/049eb1ab85" />
      </div><br />

      <h2>�����̃u���O�Ƀg���b�N�o�b�N�L���쐬<span class="supple">�i����p�j</span>
      <a href="javascript:void(0)" onclick="helpWin('general_cont.html#boomcomment')"><img src="/images/img/help.gif" width="30" height="14" border="0" align="absmiddle" alt="help" /></a>
      </h2>
      <input type="hidden" name="newsid" value="049eb1ab85" />
      <input name="themename" type="hidden" value="Shift JIS" />
      <input name="themename" type="hidden" value="Perl" />
      <input name="themename" type="hidden" value="���{��" />
      <table cellspacing="5" cellpadding="0" border="0">
      <tr><td align="right" width="18%">�^�C�g��</td>
        <td><input name="title" type="text" class="txt" size="50" maxlength="50" value="�uPerl 5.8.x �� shiftjis �ACP932�AMacJapanese �̈Ⴂ�v�ɂ���" /></td></tr>
      <tr><td align="right">�{�@��</td>
        <td><textarea name="textbody" cols="100" rows="9"></textarea></td></tr>
      <tr><td colspan="2" align="right"><input type="button" class="btn" value="�L���쐬�i����p�j" onclick="javascript:if(articlePageValueCheck1()){onMySubmit(document.atcl1,'https://bblog.sso.biglobe.ne.jp/ap/tool/bblogwrite.do')}" /></td></tr>
      </table>
      </div>
      </form>
    </div>
    <!-- trackback end -->
    <!-- comment start -->
    <div id="comment">
      <h2>�R�����g<span class="supple">�i0���j</span></h2>
      <table cellpadding="0" cellspacing="0" border="0">
      <tr><th class="cont">�� �e</th>
        <th class="sub">�j�b�N�l�[���^����</th></tr>


      </table>
      <form name="atcl2" method="post" action="http://cmt.bblog.biglobe.ne.jp/ap/commentwrite.do" onsubmit="javascript:if(articlePageValueCheck3()){onMySubmit(document.atcl2, 'http://cmt.bblog.biglobe.ne.jp/ap/commentwrite.do')}else{return false}">
      <div id="comment_form">
      <h2>�R�����g����
  <a href="javascript:void(0)" onclick="helpWin('general_cont.html#comment')"><img src="/images/img/help.gif" width="30" height="14" border="0" align="absmiddle" alt="help" /></a>
      </h2>
      <input type="hidden" name="newsid" value="049eb1ab85" />
      <input type="hidden" name="srcurl" value="http://hardsoft.at.webry.info/200802/article_1.html" />
      <input type="hidden" name="lastcmtdate" value="" />


      <table cellspacing="5" cellpadding="0" border="0">
      <tr><td align="right" width="18%">�j�b�N�l�[��</td>
        <td><input name="nickname" type="text" class="txt" value="" size="50" maxlength="25" /></td></tr>
	 <tr><td></td>
	 <script type="text/javascript">
      <!--
        insertEmoji('hardsoft.at.webry.info', 'commentbody');
      // -->
	 </script>
	 </tr>

      <tr><td align="right">�{�@��</td>
        <td><textarea name="comment" id="commentbody" cols="100" rows="5"></textarea></td></tr>
      <tr>

      <td colspan="2" align="right">
	<!-- free space E06 -->
        <input type="button" class="btn" value="�R�����g�i����p�j" onclick="javascript:if(articlePageValueCheck2(1)){onMySubmit(document.atcl2, 'https://bblog.sso.biglobe.ne.jp/ap/tool/authcommentwrite.do')}" />
        <input type="button" class="btn" value="�@�@�@�R�����g�@�@�@" onclick="javascript:if(articlePageValueCheck3()){onMySubmit(document.atcl2, 'http://cmt.bblog.biglobe.ne.jp/ap/commentwrite.do')}" /></td>
	  </tr>
      </table>

      </div>
      </form>
    </div>
    <!-- comment link area end -->

    <!-- navigation start -->
    <table class="navi"><tr>
      <td align="right" >
      <a href="http://hardsoft.at.webry.info/200801/article_6.html" title="�u���O�̑O�L��">&lt;&lt; �O�L��(2008/01/31)</a>
      </td>
      <td align="center"><a href="http://hardsoft.at.webry.info/" title="���̃u���O�̃g�b�v��">�u���O�̃g�b�v��</a></td>
      <td align="left"  >
      <a href="http://hardsoft.at.webry.info/200802/article_2.html" title="�u���O�̌�L��">��L��(2008/02/08) &gt;&gt;</a>
      </td>
    </tr></table>
    <!-- navigation end -->

	<!-- free space E10 -->

  </div>
  <!-- contents end -->

  <!-- utilities start -->
  <script type="text/javascript"><!--
    forced_disp_ad3();
  // --></script>
  <div id="utilities" class="utiloff">


    <div id="sidenews"></div>
    <script type="text/javascript"><!--
      forced_disp_ad();
    // --></script>
    <script type="text/javascript" src="/sidebar.js"></script>
    <script type="text/javascript">
    <!--
      load_sidebar_info('hardsoft.at.webry.info');
    // -->
    </script>
    <script type="text/javascript"><!--
        include_compo(1);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(2);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(3);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(4);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(5);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(6);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(7);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(8);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(9);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(10);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(11);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(12);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(13);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(14);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(15);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(16);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(17);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(18);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(19);
    // --></script>
    </div>
    <script type="text/javascript"><!--
        include_compo(20);
    // --></script>
    </div>

    <div id="side-image"></div>

  </div>
  <!-- utilities end -->
  <div class="ns-bg"></div>

	<!-- free space E11 -->

<div align="center">Perl 5.8.x �� shiftjis �ACP932�AMacJapanese �̈Ⴂ&nbsp;�n�[�h�ȃ\�t�g�̘b/BIGLOBE�E�F�u���u���O</div>

  <div id="linkunit" align="center">
      <script type="text/javascript">
      <!--
		var firstView = true;
		function google_ad_request_done(ads) {}
		function google_radlink_request_done(radlinks) {
			if (firstView) {
				firstView = false;
				if (radlinks.length < 1) {
					document.write(' <a href="http://webryblog.biglobe.ne.jp/campaign/">�E�F�u���u���O�̃L�����y�[���ɉ��債�悤�I</a> ');
					return;
				}
				document.write("<h4>Ads by Google</h4>\n");
				for(i=0; i < radlinks.length; ++i) {
					document.write(' <a target="_top" href="http://map.bblog.biglobe.ne.jp/ap/tool/show_ads.jsp?ch=b&google_kw=' + radlinks[i].url_escaped_term + '&google_rt=' + radlinks[i].radlink_token + '&google_page_url=' + encodeURIComponent(window.location) + '">' + radlinks[i].term + '</a>\n');
				}
			}
		}
      // -->
      </script>
    <script type="text/javascript">
      <!--
        google_ad_client = "ca-nec_radlinks_js";
        google_ad_channel = 'bottom';
        google_page_url = 'http://hardsoft.at.webry.info/200802/article_1.html';
        google_language = 'ja';
        google_encoding = 'sjis';
        google_ad_output = 'js';
        google_safe = 'high';
        google_num_radlinks = '3';
        google_max_radlink_len = '20';
        google_max_num_ads = '0';
        google_rl_mode = 'default';
        google_ad_section = 'section1 section2';
      // -->
    </script>
      [ <script type="text/javascript" src="http://pagead2.googlesyndication.com/pagead/show_ads.js"></script> ]
  </div>


</div>
<!-- main area end -->

<!-- footer start -->
<div id="footer" style="display:block;visibility:visible">
  <!-- links start -->
  <div id="links" style="display:block;visibility:visible">
    <a href="http://webryblog.biglobe.ne.jp/">�E�F�u���u���O(�u���O ����)</a> �b
    <a href="http://www.biglobe.ne.jp/">BIGLOBE�g�b�v</a> �b
    <a href="http://news.biglobe.ne.jp/">�j���[�X</a> �b
    <a href="http://house.biglobe.ne.jp/">�Z����</a> �b
    <a href="http://webryblog.biglobe.ne.jp/nicotto/"><img width="20" height="20" align="absmiddle" style="margin-right: 3px; border: 0pt none;" src="/images/img/nicotto_20x20.gif">�j�R�b�ƃ^�E��</a> �b
    <a href="http://join.biglobe.ne.jp/index.html">����ē�</a>
    <script type="text/javascript">
    <!--
      footerLink();
    // -->
    </script>
  </div>
  <!-- links end -->

  <!-- powered start -->
  <div id="sbanner" style="display:block;visibility:visible">
    <!-- copyright start -->
    <script type="text/javascript">
    <!--
      footCopyright();
      insert_SocialButton();
    // -->
    </script>
    <!-- copyright end -->

    <a href="http://webryblog.biglobe.ne.jp/" style="display:block;visibility:visible;width:190px;height:30px"><img src="/images/article/sbanner-m.gif" width="190" height="30" alt="�E�F�u���u���O�F���@�\�u���O�𖳗��ŁI" style="display:block;visibility:visible;width:190px;height:30px" /></a>
  </div>
  <!-- powered end -->
</div>
<!-- footer end -->

	<!-- free space E12 -->

<!-- beacon start -->
 <!-- beacon standard_ad contents  -->
<img src="http://webryblog.biglobe.ne.jp/beacon_img/hardsoft.at.webry.info/200802/article_1.html" height="1" width="1" />
<!-- beacon end -->

</body>
</html>

<?xml version="1.0" encoding="EUC-JP"?>
<!--
Source: http://www.arclamp.jp/index.rdf
Expect: EUC-JP
-->
<rdf:RDF
xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
xmlns:dc="http://purl.org/dc/elements/1.1/"
xmlns:sy="http://purl.org/rss/1.0/modules/syndication/"
xmlns:admin="http://webns.net/mvcb/"
xmlns:cc="http://web.resource.org/cc/"
xmlns="http://purl.org/rss/1.0/">

<channel rdf:about="http://www.arclamp.jp/">
<title>arclamp.jp アークランプ</title>
<link>http://www.arclamp.jp/</link>
<description></description>
<dc:language>en_us</dc:language>
<dc:creator></dc:creator>
<dc:date>2005-12-30T19:07:00+09:00</dc:date>
<admin:generatorAgent rdf:resource="http://www.movabletype.org/?v=3.15-ja" />


<items>
<rdf:Seq><rdf:li rdf:resource="http://www.arclamp.jp/blog/archives/000755.html" />
<rdf:li rdf:resource="http://www.arclamp.jp/blog/archives/000754.html" />
<rdf:li rdf:resource="http://www.arclamp.jp/blog/archives/000752.html" />
<rdf:li rdf:resource="http://www.arclamp.jp/blog/archives/000750.html" />
<rdf:li rdf:resource="http://www.arclamp.jp/blog/archives/000749.html" />
<rdf:li rdf:resource="http://www.arclamp.jp/blog/archives/000747.html" />
<rdf:li rdf:resource="http://www.arclamp.jp/blog/archives/000741.html" />
<rdf:li rdf:resource="http://www.arclamp.jp/blog/archives/000745.html" />
<rdf:li rdf:resource="http://www.arclamp.jp/blog/archives/000743.html" />
<rdf:li rdf:resource="http://www.arclamp.jp/blog/archives/000740.html" />
<rdf:li rdf:resource="http://www.arclamp.jp/blog/archives/000739.html" />
<rdf:li rdf:resource="http://www.arclamp.jp/blog/archives/000738.html" />
<rdf:li rdf:resource="http://www.arclamp.jp/blog/archives/000737.html" />
<rdf:li rdf:resource="http://www.arclamp.jp/blog/archives/000736.html" />
<rdf:li rdf:resource="http://www.arclamp.jp/blog/archives/000734.html" />
</rdf:Seq>
</items>

</channel>

<item rdf:about="http://www.arclamp.jp/blog/archives/000755.html">
<title>あなたには選択権がある（フリーランスのススメ2）</title>
<link>http://www.arclamp.jp/blog/archives/000755.html</link>
<description><![CDATA[<p>　眠る開発屋blogのdevさんに<a href="http://dev.blogtribe.org/entry-35af2b9a30fecbcfd13cd1e7f4a19a6a.html">フリーランスは「手段」であり「目的」ではない</a>というトラックバックをいただいて、ちょっと考えてみました。</p>

<p>　僕自身、チームやコミュニティにはコミットしています。知識を持った個人が協力しあうことで新しい価値が生まれるというのには大賛成。</p>

<p>　でも、それは社員でなくてもできるはずです。</p>

<p>　「自社のコア技術は社内」って言い方、前は僕もしていたけど、それって何？って思うようになりました。結局、人って事ではないのでしょうか。人やチームがいなくなれば失われてしまうのでは？それをプロダクトや特許という形にすることで価値が生まれるだけです。それは会社自身の価値ではないと思います（固定資産や不動産が大量に必要な場合、会社という形態は法律上都合がいいわけですが、IT業界では意味ないですよね）。</p>

<p>　もちろん会社という形態はチームを作るうえで便利な入れ物です。しかし多様性が必要とされている現在、会社という垣根に関係なく人材を登用していかないと勝てないようになっているのではないでしょうか。</p>

<p>　インテルは素晴らしい会社です。彼らは会社という組織をうまく利用しています。子会社を作り、お金を投資し、成功したところだけを買収してきました。Celeronだって、無線技術だって、インテル本体が作ったわけではありません。インテルは才能をうまく統合する方法として会社を利用したに過ぎません。それこそ、会社は手段であり、目的ではないわけです。</p>

<p><br />
　そもそも、社員とフリーランスなんて仕事上の違いはないはずです。機密保持が必要ならNDAを結べばよい。ある期間いて欲しいなら専属契約すればよい。もちろん必要であれば会社員になってもいい。どれもすべて契約です（むしろ社員だからといういって暗黙的なルールがあるほうが変です）。</p>

<p>　「会社員にならないほうがいい」といっているのではないです。「1人で生きていけ」といっているわけではないです。</p>

<p><br />
　会社や社員というくくりに、そんなに意味なんてない、って言いたいだけです。</p>

<p><br />
　で、僕のように会社や社員ということ自体に意味を見出せないならフリーランスは有効な選択肢です。</p>

<p>　だから僕は皆様にオススメしたい。やってみると意外に楽しいですよ。それが、この1年の結論です。分かりやすいモチベーションとしては年収の増加であり、個人活動の自由でしょう。</p>

<p>　あ、もちろん社員のままでやりたいことがやれるなら、それも良い選択です。僕は会社を変えようとして働きかけたのですが、会社が変わると僕以外の人が不幸になりそうだったので、僕が出て行っただけです。</p>

<p><br />
　考えてみてください。あなたには選択権があるという事を。</p>]]></description>
<dc:subject>ライフ</dc:subject>
<dc:creator>yusuke</dc:creator>
<dc:date>2005-12-30T19:07:00+09:00</dc:date>
</item>
<item rdf:about="http://www.arclamp.jp/blog/archives/000754.html">
<title>フリーランスのススメ</title>
<link>http://www.arclamp.jp/blog/archives/000754.html</link>
<description><![CDATA[<p>　昨年12/20に個人事業主になり1年が過ぎました。思ったよりも楽だったし、なにより楽しかったというのが率直な感想です。</p>

<p>　楽だ、と感じるのは、良い出会いがあったため安定した収入を得られたためです。楽しかったのはブログ、雑誌、講演、案件を通じて多くの方と知り合い、いろいろな話ができたことです（そして脂肪が溜まりましたｗ）。</p>

<p><br />
<strong>フリーランスは<s>企業</s>起業家ではない</strong><br />
　「雇われない生き方」を選んだ理由。たぶん、仕事を出す側と対等の立場になりたかったから。つまり、やりたくない仕事をやらない権利が欲しかったのだと思います。</p>

<p>　すごく重要なことですが<u>フリーランスは<s>企業</s>起業家ではありません</u>。あくまでも自分の能力を提供し対価を得る人間です。ここを勘違いしないでください。</p>

<p>　僕は案件に組み込まれ、知識を提供し対価を得ています。なにかビジネスをやっているわけではありません。だから独立するときにも大したリスクを感じませんでした。「お前が望めば雇ってやる」という人もいたし、別にそこらの会社にでも雇ってもらえると思っていたからです（いやだけど生きていくための選択としてはアリです）。</p>

<p><br />
<strong>知識は個人のもの</strong><br />
　<a href="http://www.works-i.com/special/independence_01.html">『フリーエージェント社会の到来』著者　ダニエル・ピンク氏インタビュー</a>では、企業側の視点でフリーランスを論じています。「外の人に任せて社内に知識が蓄積されないリスクがある」という問いかけに対して、</p>

<blockquote>知識、ナレッジが蓄積されないという問題ですが、これは企業側にとって新しく出てきた常識です。専門知識は会社に属するものではなく、本来個人に所有権があるという常識を受け入れるべきだと思います。プロジェクトで契約をするということは、そのとき必要な専門知識を、そのときだけ借りる、そのことにお金を払うという考え方に、今後企業は慣れなければならないと考えています。</blockquote>

<p>と答えています。IT業界では強く実感する点です。</p>

<p>　これを悩んでいる会社員も多いのではないでしょうか？自分の暗黙知を会社の形式知にする、つまり誰にでもできるようにすることが使命だと感じてはいませんか？（ナレッジを秘密にする必要はありません。オープンが、自分をより高めてくれることはもはや常識だからです）。<br />
　あなたの中に知識があるのは正常な状態です。どんどん溜め込んで、どんどん出していきましょう。</p>

<p><br />
<strong>企業とフリーランスの緊張関係が良い仕事を生む</strong><br />
　もう1つ、企業側から見たフリーランス化の問題点として</p>

<blockquote>フリーエージェント化の流れで歓迎できない点としては、企業が引き留めたい非常に優れた能力を持つ人が、容易に企業を離れてしまうことが挙げられます。その人の能力に対し、ほかに仕事のニーズがあれば、その企業に必ずしもとどまろうとしません。そういった人たちは、会社がその人たちを必要とするほど、その会社を必要としていないという状況になっています。非常に優れた能力を持った人たちは、ほかにも仕事があるので、その会社にはこだわらないのです。ベストな人たちを引き留めておいて、自分たちがその人たちの能力を活用したいときにどう備えるのか。自由に働く人たちの自主性が今、企業の悩みの種になってきました。</blockquote>

<p>　ともあげていますが、それが良い緊張感を生むと思っています。良い仕事をしたいから自分を磨く、良い人材が欲しいから良い仕事（お金だけじゃない）を用意するという双方の努力があってこそ、本当に良い仕事ができるのではないでしょうか。</p>

<p><br />
<strong>フリーランスのススメ</strong><br />
　日本の企業には優秀な人材が腐るほどいて、実際に腐っている気がします。彼らが能力を発揮するためにはフリーランスという選択肢が一番良いと思います。</p>

<p>　どんどんフリーランスになればよいと思います。別に仕事を変える必要性はありません。立場が変わることが重要なんです。例えば、いまの会社を退社して、すぐに人月契約してもらうのも良いはずです。年収は増えますし、個人活動も自由にできます。会社としては様々な経費が削減できるはずです。</p>

<p>　特に30歳前後の人はそうです。IT業界は常に人手不足ですから、雇ってくれるところは絶対にあります。日本にいれば死ぬこともありません。そんなにリスクはそんなに大きくないはずなんです。</p>

<p>　もしかしたら、こういう状況で一番変わるべきなのは会社なのかもしれません。フリーランスをうまく使える会社が優位になるのは間違いないでしょう。やめていく人に仕事を頼める会社が強いのです。</p>

<p>　<br />
　やってみなくては何も変わりません。誰かが言ってましたが、今は動くリスクよりも動かないリスクのほうが高いのです。フリーランスになる側も、フリーランスと契約する側もです。<br />
　お正月にじっくり考えてみてください（ｗ。来年はより多くのフリーランスと、フリーランスを契約する企業に出会えることを祈っています。</p>

<p>TO　フリーランスと契約する企業様<br />
こんな僕に仕事があるときは呼んでください。なお、対等な立場なので、良い仕事ではなければ文句も言いますし断りもします（ｗ。</p>

<p>TO フリーランス＆フリーランス予備軍様<br />
優秀な方と一緒に仕事したいです。業務案件が好きで、技術も好きで、なるべくなら語れる人。</p>

<p>いずれも yusukeあっとarclamp.jp まで。</p>]]></description>
<dc:subject>ライフ</dc:subject>
<dc:creator>yusuke</dc:creator>
<dc:date>2005-12-29T14:01:15+09:00</dc:date>
</item>
<item rdf:about="http://www.arclamp.jp/blog/archives/000752.html">
<title>プロジェクトマネジメントとプロジェクトコントロール</title>
<link>http://www.arclamp.jp/blog/archives/000752.html</link>
<description><![CDATA[<p>　ちょっと前の記事ですがnikkeibp.jpの"<a href="http://nikkeibp.jp/style/biz/special/indomitable/051212_14th/">マネジメントとコントロールは違う</a>"が面白かったです。</p>

<p><br />
<strong>プロジェクトコントロールとは失敗しないようにすること</strong><br />
企業のリスクマネジメントの話なのですが、次の</p>

<blockquote>　マネジメントの意味は、do right things、正しいことをする、である。これに対し、コントロールは、do things right、事を正しくする、である。

<p>　話を思い切り単純にして進めれば、マネジメントは企業が成長していくための活動であり、コントロールは企業が失敗しないための活動である。アイデアをうまく育て、売れる商品を作り出した企業は、マネジメントされていたことになる。逆に、製造現場をうまくコントロールし、高品質の商品を作ったとしても、それが顧客に受け入れられなかったら、正しいことをしたとは言えない。</blockquote></p>

<p>というのが非常に良い指摘だなと思いました。</p>

<p>　これをSI業界に置き換えるとプロジェクトを成功させるのがマネジメントであり、プロジェクトを失敗させないのがコントロールになるかと思います。</p>

<p><br />
<strong>コントロールされていない動かないコンピュータが増えている</strong><br />
　さて本題。僕はコントロールされていないプロジェクトが増えているように感じています。つまり最低限、失敗しないようにしておかないままマネジメントをしようとして失敗してしまうプロジェクトが多いのです。<br />
　失敗プロジェクトとは「動かないコンピュータ」のことです。多少、予算がオーバーしようが、リリースが遅れようが、まともなシステムができていれば失敗とは呼ばない気がします。逆に、予算内でちゃんとリリースしても使えないシステムであれば、それは失敗でしょう。ソフトウェア開発においてはQCD（品質・コスト・期間）のうち、品質がもっとも重要であるのです。</p>

<p>　品質を確保する方法がない（＝コントロールできていない）ままマネジメントをしようとするとどうなるでしょう。きっと表面的なコストや納期のつじつまだけを合わせるだけに終始し、品質が劣化しやすくなってしまうでしょう。しかも、品質の劣化を隠されたまま。</p>

<p>　昨今、構造設計書の偽造が騒がれていますが、これは決して他人事ではありません。建築士は「他にも建築士はいると言われた。鉄骨を減らせというのは法律を犯せということだと思った」と言い、発注側は「法律を犯してまで鉄骨を減らすとは思わなかった」と言いました。<br />
　これをSI業界で言えばSIerは「他にもSIerはいると言われた。コストと納期を守れというのは品質を落とせということだと思った」と言い、発注側は「品質を落としてまでコストを納期を守るとは思わなかった（守れてないけど）」と言うわけです。<br />
　もちろん建築業界の発注者には悪意があり、SIerでは双方に悪意なんかないわけで、業界の未熟さゆえに起こった錯誤でしょう。</p>

<p>　ちゃんとシステムが作れていれば、後々にはなりますがコストや納期の問題を明確に把握できます。その上でコストを増やすなり、納期を遅らせるなりという判断が行えます。まともなものができていないのに、ずるずるとコストや納期をかけるのは意味がありません。明確な方法論がないのであれば、時間とコストをかけても良いシステムは*絶対に*できません。</p>

<p><br />
<strong>品質を確保するためにアーキテクトがすべきこと</strong><br />
　プロジェクトコントロールとはソフトウェアの品質を確保する方法が確立されているということです。それに必要なのはしっかりしたアーキテクチャと開発環境のことです。</p>

<p>　単体テストを可能にし、ソースコードを検証し、常に統合され、自動生成をなるべく行い、変更に対応でき、プログラマの責任範囲が明確になるようにするわけです。<br />
　UnitText、静的メトリクス解析、ソースコードリポジトリとデイリービルド、O/Rマッピングツール、DIコンテナなどの手法を組み合わせ、さらに業務に沿ったフレームワークを構築することで実現できます。</p>

<p>　多くの人員が関わるプロジェクトではソフトウェア化されたフレームワークが存在しないと品質確保することはできません。どんな立派なコード規約や実装手順書があっても、人間が完全に守れるわけがありません。</p>

<p>　逆に言えばプロジェクトマネジメントができていることはコントロールができるということのはずです。COBOL時代は、COBOL自体が固いし業務も簡単でした。コントロールを意識しなくても大丈夫だったのです。ところが現在、Javaや.Netは柔軟であり業務も複雑です。コントロールを意識しないではおられないのです。</p>

<p><br />
　もちろん、これらはプロジェクトマネージャの責務ではありません。できないですから。これはITアーキテクトが背負うべきものです。プロジェクトマネージャとITアーキテクトが連携してこそ、初めてプロジェクトが正しくマネジメントできるのだと思います。</p>

<p>　プロジェクトマネジメントうんぬん言う前に、あなたのプロジェクトはコントロールできていますか？</p>]]></description>
<dc:subject>システム開発手法</dc:subject>
<dc:creator>yusuke</dc:creator>
<dc:date>2005-12-28T20:30:00+09:00</dc:date>
</item>
<item rdf:about="http://www.arclamp.jp/blog/archives/000750.html">
<title>写真撮影</title>
<link>http://www.arclamp.jp/blog/archives/000750.html</link>
<description><![CDATA[<p>　<a href="http://www.arclamp.jp/blog/archives/000721.html">以前エントリしましたが</a>、写真家の<a href="http://www.amy.hi-ho.ne.jp/takanori-chiba/">千葉先生</a>（<a href="http://love.ap.teacup.com/loveppo/">ブログ</a>）に撮影していただきました。</p>

<p>　本来、僕なんか撮っていただけるような方ではないのですが、嫁の知り合いということでお願いさせていただきました。しかも「男なんか撮ったことない」というのを説得して（ｗ。</p>

<p>　場所は新宿近辺です。とりあえず右側の写真だけ変えました。<a href="http://www.arclamp.jp/about/archives/2004/12/eo.html">バイオグラフィー</a>にいくと、もうちょい拡大した写真があります。不思議な空間ですよね。</p>

<p>　僕はポラで撮ってもらったこれがお気に入りかも。ポラで寒い日に撮ると斑が出やすいのですが、これも見事に上が金色に。<br />
　<br />
<img src="/img/yusuke3.jpg" width="240" height="246" border="0" /></p>

<p>　こちらは嫁が選んだ一枚。こちらもポラですが「真面目そうに見えるから」とのこと。まるで本人が真面目でないかのような意見です。</p>

<p><img src="/img/yusuke4.jpg" width="240" height="246" border="0" /></p>

<p>　ポラで撮ると味わいがあるというか、細部がぼやかされるというか（ｗ。撮影につかっていたポラロイドの写真機は1974年製。その分の重みもあるんでしょうね。</p>

<p>　もう何枚かありますので、折を見て出していきます。千葉先生、本当にありがとうございました。</p>]]></description>
<dc:subject>その他の話題</dc:subject>
<dc:creator>yusuke</dc:creator>
<dc:date>2005-12-23T14:18:13+09:00</dc:date>
</item>
<item rdf:about="http://www.arclamp.jp/blog/archives/000749.html">
<title>ApacheにAJAX Toolkit Frameworkが提案</title>
<link>http://www.arclamp.jp/blog/archives/000749.html</link>
<description><![CDATA[<p>　TuscanyはエントリしないくせにAJAX Toolkit Frameworkはエントリします（だってSCAが理解できなかったんだもん）。</p>

<p>　日本時間20日深夜にApache Incubatorあてに"AJAX Toolkit Framework"が提案されました（<a href="http://mail-archives.apache.org/mod_mbox/incubator-general/200512.mbox/%<EMAIL>%3e">提案書のメール</a>）。</p>

<p>　これ<a href="http://www.zimbra.com/">Zimbra</a>とIBMのエンジニアが中心というから驚きました。<a href="http://www.zimbra.com/">Zimbra</a>といえばBEA Systemsの元CTOスコット・ディッゼン氏を引き抜き、カレンダー、メールなどを含む<a href="http://www.zimbra.com/products/index.html">Zimbra Collaboration Suite</a>をリリースしたことで知られるベンチャー。</p>

<p>　しかも、そこらのAJAXフレームワークとは考えていることのでかさが違います。EclipseプラグインとしてAJAX/DHTMLのIDEを提供がメインになるようです。</p>

<blockquote>a JavaScript editor with edit-time syntax checking; Mozilla web browser; integrated DOM browser; integrated JavaScript debugger; and wizards and development aides tuned to specific libraries and toolkits.

<p>（シンタックスチェック付きJavaScriptエディタ、Mozillaブラウザ、DOMブラウザ、JavaScriptデバッガ、そしてウィザードやライブラリ）</blockquote></p>

<p>　これは</p>

<blockquote>Mozilla XULRunner and JavaConnect, and Eclipse WTP</blockquote>

<p>　に依存するそうです。それから利用するオープンソースプロジェクトとしてはJavaによるJavaScript（というかECMAScript）実行環境の<a href="http://www.mozilla.org/rhino/">Rhino</a>、それからJSのライブラリとしてJavaScript検証の<a href="http://www.crockford.com/jslint/lint.html">JSLint</a>、UIライブラリの<a href="http://openrico.org/">Rico</a>、そして<a href="http://www.zimbra.com/products/index.html">Zimbra Collaboration Suite</a>をあげています。</p>

<p>　で、これをベースにZimbraのコンポーネントを乗せてくるみたいです。つまり、ベースになる開発環境はエディタやデバッガだけにして、その上にのるコンポーネントやウィザードは取替え可能にしておくようです。<br />
　すでにイニシャルコードはあるみたいですね。興味がある方はメールにリンクがあるのでたどってみてください。</p>

<p><br />
　これは来るMustang（JavaSE　6）への動きでしょう。Mustangには<a href="http://jcp.org/en/jsr/detail?id=223">JSR-223 Scripting for Java Platform</a>で議論されているスクリプトが搭載されます（実質的にはRhinoそのものではないかといわれています）。サーバサイドでJavaScriptが動くと何ができるのかというのは楽しみで、こうなるとJava開発も変わってくることが予想されます。言語的なライトウェイトでありながら、基幹システムとも統合できるというスケーラビリティは大きなアドバンテージではないでしょうか？</p>

<p>　そうなるとJavaScriptエディタ＆開発環境は必要なはず。これをIBMはAJAXブームにのってオープンソース・コミュニティと作っていくつもりなのではないでしょうか？今からはじめればMustangの頃にはいい感じになっていることでしょう。もし、そうだとしたらIBM手堅いなぁ。</p>

<p><br />
　というわけで、AJAXという意味では実用性に不安を感じるものの、Java環境でスクリプトが動作するということには非常に興味があるわけで、こっち方面にアンテナを向けていこうかなと思っています。</p>]]></description>
<dc:subject>Java</dc:subject>
<dc:creator>yusuke</dc:creator>
<dc:date>2005-12-21T11:45:26+09:00</dc:date>
</item>
<item rdf:about="http://www.arclamp.jp/blog/archives/000747.html">
<title>WEB+DB PRESS「DI時代のアーキテクチャ設計入門/第1章、第2章」を寄稿</title>
<link>http://www.arclamp.jp/blog/archives/000747.html</link>
<description><![CDATA[<p><a href="http://www.amazon.co.jp/exec/obidos/ASIN/4774126187/arclampjp-22/" target="_top">WEB+DB PRESS Vol30</a>（2005/12/22発売）の特集1「DI時代のアーキテクチャ設計入門」において、第1章と第2章を寄稿しました。共著はギガプライズ田中さんです（ブログ：<a href="http://blog.so-net.ne.jp/yoichiro/">天使やカイザーと呼ばれて</a>）。</p>

<p>　第1章は「DI時代のアーキテクト - ソフトウェアアーキテクチャを見据えた設計とは」。僕はアイデアのみで田中さんが執筆。アーキテクトに焦点を当て、プロジェクト開発での実装を実現するための開発のフレームワークが重要だと書かれています。具体的には規約をどのように運用していくべきかということですかね。ま、DIには一切関係のない内容のですが（ｗ、DIであればこうした運用に柔軟性が出るという指摘を行っています。</p>

<p>　第2章は「DI時代のJava EE（J2EE）アーキテクチャ - DIの本質とその効果的な導入とは」。僕が執筆。個人的には良い感じに書けたと思っています（某記事での反省をいかしてありますので）。<br />
　「DIはアーキテクチャを柔らかくする」という視点から、EJB2とEJB3の比較、およびDI対応アプリがどんどん柔らかくなっていく様子をテスタビリティを中心に記述しました。ポイントは、既存の（ちゃんとした）アーキテクチャを変えることなくDIは導入できるところでしょうか。一般的な3層アーキテクチャにおいて、ロジックがどんどん分解されている過程は、けっこう面白いと思います。<br />
　サンプルアプリもダウンロードできるようになっているので、これは試していただけるとありがたいです（<a href="http://www.gihyo.co.jp/magazines/wdpress/">たぶんココから</a>）。<br />
　あと「EJB3は柔らかくなっただけで軽くはなってねぇ」という指摘に、もっと毒を込めても良かったのですが自粛しました。はい。</p>

<p><br />
　フィードバックがありましたら、トラバやコメントやメールでお願いします。</p>

<table  border="0" cellpadding="5"><tr><td valign="top"><a href="http://www.amazon.co.jp/exec/obidos/ASIN/4774126187/arclampjp-22/" target="_top"><img src="http://images.amazon.com/images/P/4774126187.09._SCMZZZZZZZ_.jpg" border="0" alt="4774126187" /></a></td><td valign="top"><font size="-1"><a href="http://www.amazon.co.jp/exec/obidos/ASIN/4774126187/arclampjp-22/" target="_top">WEB+DB PRESS Vol.30</a><br />WEB+DB PRESS編集部 <br /><br /><iframe scrolling="no" frameborder="0" width="200" height="40" hspace="0" vspace="0" marginheight="0" marginwidth="0" src="http://webservices.amazon.co.jp/onca/xml?Service=AWSECommerceService&SubscriptionId=0G91FPYVW6ZGWBH4Y9G2&AssociateTag=goodpic-22&Operation=ItemLookup&IdType=ASIN&ContentType=text/html&Version=2004-10-04&Page=1&ResponseGroup=Offers&ItemId=4774126187&Style=http://www.g-tools.net/xsl/priceFFFFFF.xsl"></iframe><br /><a href="http://www.amazon.co.jp/exec/obidos/ASIN/4774126187/arclampjp-22/" target="_top">Amazonで詳しく見る</a></font><font size="-2"> by <a href="http://www.goodpic.com/mt/aws/index.html" >G-Tools</a></font></td></tr></table>
]]></description>
<dc:subject>その他の話題</dc:subject>
<dc:creator>yusuke</dc:creator>
<dc:date>2005-12-19T16:40:26+09:00</dc:date>
</item>
<item rdf:about="http://www.arclamp.jp/blog/archives/000741.html">
<title>なぜWeb2.0は楽しいのか</title>
<link>http://www.arclamp.jp/blog/archives/000741.html</link>
<description><![CDATA[<p>　セス・ゴードウィン氏によればWeb2.0は技術ではなく態度です（<a href="http://sethgodin.typepad.com/seths_blog/2005/10/tim_o_on_web_20.html">Seth's Blog : Tim O. on Web 2.0</a>）。では、なぜWeb2.0的であることは楽しいのか？</p>

<p>　僕が思うに、それはWeb2.0がアートだから。</p>

<p>　Web2.0を示す"Mush-up"という言葉は、元々ヒップホップのアーティスト達が、いろんな曲をコラージュして新しい曲を作り出すことをさします。</p>

<p>　AJAXの楽しさはHTMLに従うのではなくHTMLをコントロールし、データを出力するのではなくデータを表現することです。<br />
　とくにハックは最高のアートです。僕がはてなマップをハックして<a href="http://www.arclamp.jp/blog/archives/000667.html">ブックマークレットを作った</a>時、いけないことをしているという感覚とともに、不思議な快感を得ることが出来ました。元々表示されていたHTMLをinnerHTML = ''でぶち壊し、同じデータを使って全く違う表現をする。そこには新しい表現があるわけです。</p>

<p>　この快感はアートに他なりません。</p>

<p>　もちろんソフトウェアをアートとして利用できた人間もいました。しかし、それはごく少数の天才・秀才に過ぎなかった訳です。しかしWeb2.0の技術的なリーチが非常に長く、そして、ソフトウェアが最も得意とする視覚に訴えられた（っていうかPCがそれ以外に訴えかけるのが難しいから）。</p>

<p>　これまでアートを禁じられてきた何百万という人々が一斉にWeb2.0に飛びついたのは何の不思議もありません。</p>

<p>　これまでのソフトウェア開発は、情報を入力し出力することが目的でした。しかし、Web2.0は情報を集め、切り取り、混ぜ、つなぎ直し、"情報を表現する"ことが目的です。</p>

<p>　表現とは、<a href="http://dictionary.goo.ne.jp/search.php?MT=%C9%BD%B8%BD&kind=jn">辞書によれば</a>次のような意味を持ちます。</p>

<blockquote>内面的・精神的・主体的な思想や感情などを、外面的・客観的な形あるものとして表すこと。また、その表れた形である表情・身振り・記号・言語など。特に、芸術的形象たる文学作品（詩・小説など）・音楽・絵画・造形など。</blockquote>

<p>　Web2.0の楽しさ。それは技術やデータをソース（素材）として、その上に付け加えられた新たな"表現"そのものなわけです。</p>

<p><br />
　ただアートが抱えている実用性という問題を引きずっているのも正しいでしょう。僕もAjaxを業務システムに使うのはどうかと思います。</p>

<p>　でも僕は、システム開発はアートになりうると信じています。</p>

<p>　多くの建築家が高い価値を提供し、評価される。まだシステム開発には最低限の安全性もルールもありません（だから偽造なんてしようもないわけですよ）。でも、いつの日か、そこに楽しみが産まれることと信じています。</p>

<p><br />
　<a href="http://www.arclamp.jp/blog/archives/cat_servicemix.html">ServiceMixによってGoogle MapとJBIを組み合わせる</a>。SOAの技術をWeb2.0的に使うというのは、業務システムの未来をしめす僕なりのアートだったのかもしれません。</p>]]></description>
<dc:subject>ソフトウェアデザイン</dc:subject>
<dc:creator>yusuke</dc:creator>
<dc:date>2005-12-19T01:00:11+09:00</dc:date>
</item>
<item rdf:about="http://www.arclamp.jp/blog/archives/000745.html">
<title>ServiceMix (5) InfoProviderの作り方</title>
<link>http://www.arclamp.jp/blog/archives/000745.html</link>
<description><![CDATA[<p>　今回はServiceMixにデプロイするServiceEngine（SE）の作り方を考えていきたいと思います。特にビジネスロジックであるInfoProviderです。</p>

<p>　何度も書いていますがInfoProviderは条件に応じて情報を返す仕組みです。しかし、いかにServiceMixが簡単とはいえJBIの仕様にそったメッセージ送信を書くのは大変です。そこで、InfoProviderのコアであるロジック部分と、それをServiceMix（JBI）上で動かすためのコードを分離してしまう方法を考えましょう。参考にするのはServiceMix自身がアクティベートで使っているテクニックです。</p>

<p>　まず、InfoProviderをServiceMixに依存しないように考えると、次のようなインターフェースになります。<br />
<div style="background-color:#FFCC99; padding:2px"><br />
package demo.info;</p>

<p>import java.util.Map;</p>

<p>public interface InfoProvider {<br />
&nbsp;&nbsp;Info[] getInfos(Map condition);<br />
}<br />
</div></p>

<p>　また、クラスInfoは次のようになります。</p>

<div style="background-color:#FFCC99; padding:2px">
package demo.info;

<p>public class Info {<br />
&nbsp;&nbsp;private String id;<br />
&nbsp;&nbsp;private String lat;<br />
&nbsp;&nbsp;private String lng;<br />
&nbsp;&nbsp;private String imgSrc;<br />
&nbsp;&nbsp;private String title;<br />
&nbsp;&nbsp;private String message;</p>

<p>&nbsp;&nbsp;//各GetterとSetter<br />
</div></p>

<p>　この状態ではパラメタをクラスMap、返却をクラスInfoによって行っており、ServiceMixに否依存であり、単体テストも非常に簡単に行えることがわかります。実装例は次のとおりです。</p>

<div style="background-color:#FFCC99; padding:2px">
package demo.info.impl;

<p>import java.util.Map;</p>

<p>import demo.info.Info;<br />
import demo.info.InfoProvider;</p>

<p>public class InfoProviderImpl1 implements InfoProvider {</p>

<p>&nbsp;&nbsp;public Info[] getInfos(Map condition) {<br />
&nbsp;&nbsp;&nbsp;&nbsp;Info[] infos = new Info[2];<br />
&nbsp;&nbsp;&nbsp;&nbsp;infos[0] = new Info("simple1_1", "34.991305", "135.750579", "", "本願寺（西本願寺）", "...");<br />
&nbsp;&nbsp;&nbsp;&nbsp;if ( "family".equals(condition.get("group")) ) {<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;infos[1] = new Info("simple1_2", "35.010271", "135.768378", "", "本能寺（家族向き）", "...");<br />
&nbsp;&nbsp;&nbsp;&nbsp;}<br />
&nbsp;&nbsp;&nbsp;&nbsp;else if ( "friends".equals(condition.get("group")) ) {<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;....<br />
&nbsp;&nbsp;&nbsp;&nbsp;}<br />
&nbsp;&nbsp;&nbsp;&nbsp;return infos;<br />
&nbsp;&nbsp;}<br />
}<br />
</div></p>

<p><br />
　しかし、このままではServiceMix上にデプロイすることはできません。そこで、これにServiceMix用のアダプタをつけて処理を行わせることにします。アダプタの役割は、<br />
 1.JBIのメッセージで届けられたパラメタのXMLをクラスMapに変換<br />
 2.InfoProviderを呼び出し<br />
 3.実行結果のクラスInfoをXMLにしてメッセージを返却<br />
です。</p>

<p>　アダプタを利用するのはDIを利用します。アダプタクラス側にInfoProviderを属性として用意し、そこに実行したいInfoProviderの実装をインジェクトするのです。前エントリで書いたservicemix.xmlを見ると、クラスdemo.servicemix.se.Adaptorの属性infoProviderに対してInfoProviderの実装クラスdemo.info.impl.InfoProviderImpl1がインジェクとされています（赤い部分）。</p>

<div style="background-color:#FFCC99; padding:2px"><pre>
&nbsp;&nbsp;&lt;sm:activationSpec&nbsp;componentName="provider1"&nbsp;&nbsp;&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;service="foo:provider1"
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;destinationService="foo:findAggregator"&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;sm:component&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;bean&nbsp;xmlns="http://xbean.org/schemas/spring/1.0"
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;class="demo.servicemix.se.Adaptor"&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font style="color:red; font-weight:bold">&lt;property&nbsp;name="infoProvider"&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;bean&nbsp;class="demo.info.impl.InfoProviderImpl1"&nbsp;/&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/property&gt;</font>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/bean&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/sm:component&gt;
&nbsp;&nbsp;&lt;/sm:activationSpec&gt;
</pre></div>

<p>　このXMLをみるとactivationSpecによってJBIへの登録を行い、componentによってコンポーネントの初期化を行い、クラスAdaptorによってJBIの処理を隠蔽していることになります。<br />
　このように機能の合成をインジェクトによって簡単に表現できるのがDIの特徴です。これまでであれば継承による機能の合成をしていたものが、インジェクトによって実現しているようにも感じています。</p>

<p>　では、アダプタの実装を見ながら、ServiceMixでの実装方法を見ていきましょう。</p>

<div style="background-color:#FFCC99; padding:2px">
package demo.servicemix.se;

<p>import //略</p>

<p>public class Adaptor extends ComponentSupport implements<br />
&nbsp;&nbsp;&nbsp;&nbsp;MessageExchangeListener { ...[1]</p>

<p>&nbsp;&nbsp;private InfoProvider infoProvider; ...[2]<br />
&nbsp;&nbsp;public void setInfoProvider(InfoProvider infoProvider) {<br />
&nbsp;&nbsp;&nbsp;&nbsp;this.infoProvider = infoProvider;<br />
&nbsp;&nbsp;}</p>

<p>&nbsp;&nbsp;private SourceTransformer transformer = new SourceTransformer(); ...[3]<br />
&nbsp;&nbsp;public void setTransformer(SourceTransformer transformer) {<br />
&nbsp;&nbsp;&nbsp;&nbsp;this.transformer = transformer;<br />
&nbsp;&nbsp;}</p>

<p>&nbsp;&nbsp;private boolean encode = false; ...[4]<br />
&nbsp;&nbsp;public void setEncode(boolean encode) {<br />
&nbsp;&nbsp;&nbsp;&nbsp;this.encode = encode;<br />
&nbsp;&nbsp;}</p>

<p>&nbsp;&nbsp;public void onMessageExchange(MessageExchange exchange)<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;throws MessagingException { ...[5]</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;if (exchange.getStatus() == ExchangeStatus.DONE) { ...[6]<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return;<br />
&nbsp;&nbsp;&nbsp;&nbsp;} else if (exchange.getStatus() == ExchangeStatus.ERROR) {<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;done(exchange);<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;return;<br />
&nbsp;&nbsp;&nbsp;&nbsp;}</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;NormalizedMessage in = getInMessage(exchange); ...[7]</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;Node docNode = null;<br />
&nbsp;&nbsp;&nbsp;&nbsp;try {<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;docNode = transformer.toDOMNode(in.getContent()); ...[8]<br />
&nbsp;&nbsp;&nbsp;&nbsp;} catch (Exception e) {<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;e.printStackTrace();<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;fail(exchange, e);<br />
&nbsp;&nbsp;&nbsp;&nbsp;}</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;Map queryParam = Util.createQueryMap((Element) docNode.getChildNodes().item(0)); ...[9]</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;Info infos[] = infoProvider.getInfos(queryParam); ...[10]</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;Element resultElem = Util.toElement(infos, encode); ...[11]</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;InOnly inOnly = getExchangeFactory().createInOnlyExchange(); ...[12]<br />
&nbsp;&nbsp;&nbsp;&nbsp;NormalizedMessage out = inOnly.createMessage(); ...[13]<br />
&nbsp;&nbsp;&nbsp;&nbsp;out.setContent(new DOMSource(resultElem));<br />
&nbsp;&nbsp;&nbsp;&nbsp;inOnly.setInMessage(out);</p>

<p>&nbsp;&nbsp;&nbsp;&nbsp;send(inOnly); ...[14]</p>

<p>&nbsp;&nbsp;}</p>

<p>}<br />
</div></p>

<p><br />
[1]まず、ServiceMix用のSEを実装するにはインターフェースMessageExchangeListenerを実装します。このメソッドonMessageExchangeを実装することでJBIのアクセプトイベントをハンドリングすることができます。また継承しているクラスComponentSupportは、その名のとおりJBI上のコンポーネントを実装するための基底クラスとして利用することができます。</p>

<p>[2]実際に実行するInfoProviderのインスタンスをインジェクとしてもらうために属性InfoProviderを用意します。</p>

<p>[3]SourceTransformerはJBIで用意されたJAXPのユーティリティクラスです。</p>

<p>[4]これは本質的ではないのですが、URLエンコードを行うかどうかをしめすためのフラグです。デモでは別サーバインスタンスのServiceMixにREST経由で処理を投げる場合に日本語のままだと文字化けしてしまいます。そこでURLエンコードするわけです。</p>

<p>[5]メソッドonMessageExchangeがインターフェースMessageExchangeListenerで定義されたものです。引数のクラスMessageExchangeはJBIにおけるメッセージ交換ためのオブジェクトです。</p>

<p>[6]JBIでは、メッセージが相手に届いて処理が終了したことをコールバックしてくれます。MessageExchangeのメソッドgetStatusによって状態を知ることができます。DONEは正常終了、ERRORは異常終了を示しますが、どちらも特には処理を行いません。</p>

<p>[7]ComponentSupportのメソッドgetInMessageによって、MessageExchangeから入力（In）のメッセージNormalizedMessageを取り出します。MessageExchangeが行き先やステータスをのあらわすのに対して、NormalizedMessageはメッセージそのものを示し、XML、バイナリの添付、任意のプロパティなどを持ちます。この丸山先生の絵がわかりやすいでしょうか。<br />
<img src="/blog/archives/images/jbi_messageexchange.jpg" width="420" height="158" border="0" /></p>

<p>[8]NormalizedMessageから、さらに生のXMLを取り出してDOMに変換しています。</p>

<p>[9]DOMを操作して、パラメタ用のMapを作ります。やっていることは単純でエレメントの名前とノード値をMapにいれているだけです。次のようなXMLであれば、numberOfPerson:4と、group:familyということになります。</p>

<div style="background-color:#FFCC99; padding:2px"><pre>
&lt;request&gt;
&nbsp;&nbsp;&lt;numberOfPerson&gt;4&lt;/numberOfPerson&gt;
&nbsp;&nbsp;&lt;group&gt;family&lt;/group&gt;
&lt;request&gt;
</pre></div>

<p>[10]いよいよInfoProviderを呼び出します。</p>

<p>[11]結果としてInfoの配列が取得できたので、今度はそれをDOMに変換します。こちらもやっていることは単純で、次のようなXMLを作っているだけです。</p>

<div style="background-color:#FFCC99; padding:2px"><pre>
&lt;infos&gt;
&nbsp;&nbsp;&lt;info id="simple1_1" lat="34.991305" lng="... /&gt;
&nbsp;&nbsp;...
&lt;/infos&gt;
</pre></div>

<p>[12]では、メッセージの返事を行います。まずメソッドgetExchangeFactoryを利用して、このSEに設定されたクラスMessageExchangeFactoryを取得し、非同期（InOnly）の返信用MessageEchangeを作ります。ちなみにメソッドcreateExchangeを使えば明示的にサービスに対応したMessageExchangeを作ることもできます。</p>

<p>[13]次に返信用MessageExchangeのメソッドcreateMessageを利用してNormalizedMessageを作ります。そこに先ほどのInfoをXML化したものを流し込みます。</p>

<p>[14]最後に返信用MessageExchangeをNMRに送信して終了です。内部的にはデリバリーチャンネルを取得して、そこに送信を行っています。</p>

<p><br />
　こうしてみてもらうと難しそうな、簡単そうな、微妙な感じだと思います（ｗ。ただ、メインのビジネスロジック自体は、このアダプタを使うことによってシンプルに保たれていることがわかりますから、JBIに依存する部分と、しない部分を分離していくという戦略自体に問題はないでしょう。</p>

<p>　僕がServiceMixの課題と書いたのはアダプタにあたる部分のサポートです。StrutsがHTTPリクエストのパラメタをActionFormにしたように、JBIのメッセージをもっと簡単にハンドリングする機構があると便利なはずです。たとえば、ESBメッセージング・フレームワークの<a href="http://mule.codehaus.org/">Mule</a>であれば、BindingComponentにあたる層にトランスフォーマー機構が用意されていて、ある程度は自動的にメッセージをオブジェクトにバインドしてくれます。</p>

<p>　なお、今回のデモではクラスAdaptor以外にも、いくつかのクラスを作りましたが、汎用的な機能です。今後はライブラリとして用意されていくか、あるいはBPELやXSLTのようなプロセス・エンジンが使われるようになると思います。</p>

<p><br />
　なんにせよServiceMixはまだまだ発展途上です。なので複雑なことをさせるとか、業務アプリに使うといった場合には十分に注意してください。バージョン2.0といっても、ベータぐらいの気持ちで見てあげると良いでしょう。今後、広く普及していくにしたがって使い方が検討されていくと思っています。</p>

<p><br />
　これで一通りのデモアプリ解説は終了です。なにかあればエントリを追加します。もしくは解説して欲しいことがあればメール（yusukeあっとarclamp.jp）かコメントをくださいませ。</p>

<p><br />
<a href="http://www.arclamp.jp/blog/archives/cat_servicemix.html">このServiceMix連載の一覧</a></p>]]></description>
<dc:subject>servicemix</dc:subject>
<dc:creator>yusuke</dc:creator>
<dc:date>2005-12-16T12:12:52+09:00</dc:date>
</item>
<item rdf:about="http://www.arclamp.jp/blog/archives/000743.html">
<title>ServiceMix (4) デモアプリのservicemix.xml</title>
<link>http://www.arclamp.jp/blog/archives/000743.html</link>
<description><![CDATA[<p>　いよいよデモアプリケーションの説明です（デモのダウンロードは<a href="http://www.arclamp.jp/blog/archives/000737.html">ServiceMix (1) JBIとServiceMix</a> から）。</p>

<p>　<a href="http://www.arclamp.jp/blog/archives/000739.html">ServiceMix (2) デモアプリケーション概要</a>の「ServiceMix内の概要」に描いた図を、もう一度載せます（クリックで拡大）。</p>

<p><a href="/blog/archives/images/servicemix_demo2_l.gif" target="_blank"><img src="/blog/archives/images/servicemix_demo2.gif" width="420" height="238" border="0" /></a></p>

<p>　デモでは2つのSeriviceMixが起動しています。ここではメインになる画面を表示する側（左側）のServiceMixを見て行きます。設定ファイルであるservicemix.xmlは、src\webapp\WEB-INF\servicemix.xmlです。</p>

<p>　servicemix上の流れを見るためには、要素sm:activationSpecの属性destinationServiceに定義されたサービス名を見ていけばよいことになります。</p>

<p><strong>httpBinding（HTTPのリクエストをJBIに流し込むためのBC）</strong><br />
受け取ったら、そのままfoo:asyncForwarderに流します。<br />
<div style="background-color:#FFCC99; padding:2px">&nbsp;&nbsp;&lt;sm:activationSpec&nbsp;componentName="httpBinding"&nbsp;&nbsp;&nbsp;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;service="foo:httpBinding"<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font style="color:red; font-weight:bold">destinationService="foo:asyncForwarder"</font>&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&lt;sm:component&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;bean&nbsp;xmlns="http://xbean.org/schemas/spring/1.0"<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;class="org.servicemix.components.http.HttpInOutBinding"&nbsp;/&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/sm:component&gt;<br />
&nbsp;&nbsp;&lt;/sm:activationSpec&gt;<br />
</div></p>

<p><br />
<strong>asyncForwarder（HTTPの同期メッセージを非同期に切り替える）</strong><br />
foo:findAggregatorに対して非同期でメッセージを送ります。foo:httpBindingからは同期処理で呼ばれているので、帰りの処理はメッセージに返信すれば自然に戻っていきます。<br />
<div style="background-color:#FFCC99; padding:2px">&nbsp;&nbsp;&lt;sm:activationSpec&nbsp;componentName="asyncForwarder"&nbsp;&nbsp;&nbsp;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;service="foo:asyncForwarder"<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font style="color:red; font-weight:bold">destinationService="foo:findAggregator"</font>&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&lt;sm:component&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;bean&nbsp;xmlns="http://xbean.org/schemas/spring/1.0"<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;class="demo.servicemix.se.AsyncForwarder"&nbsp;/&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/sm:component&gt;<br />
&nbsp;&nbsp;&lt;/sm:activationSpec&gt;<br />
</div></p>

<p><br />
<strong>findAggregator（指定されたサービスを同時・非同期に呼び出し、結果を集積する）</strong><br />
　ここではtargetsに指定されたInfoProvider達に情報を非同期に同時実行するため、順次、実行結果が戻ってきます。その数を数えて、全て集まったら結果を１つのXMLファイルにして戻します。そのためdestinationServiceとは戻り先であるfoo:asyncForwarderになります。<br />
<div style="background-color:#FFCC99; padding:2px">&nbsp;&nbsp;&lt;sm:activationSpec&nbsp;componentName="findAggregator"&nbsp;&nbsp;&nbsp;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;service="foo:findAggregator"<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font style="color:red; font-weight:bold">destinationService="foo:asyncForwarder"</font>&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&lt;sm:component&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;bean&nbsp;xmlns="http://xbean.org/schemas/spring/1.0"<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;class="demo.servicemix.se.FindAggregator"&nbsp;&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;property&nbsp;name="targets"&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;list&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font style="color:red; font-weight:bold">&lt;value&gt;provider1&lt;/value&gt;</font><br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font style="color:red; font-weight:bold">&lt;value&gt;provider2&lt;/value&gt;</font><br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font style="color:red; font-weight:bold">&lt;value&gt;exProvider&lt;/value&gt;</font><br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/list&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/property&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/bean&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/sm:component&gt;<br />
&nbsp;&nbsp;&lt;/sm:activationSpec&gt;<br />
</div></p>

<p><br />
<strong>provider1（実際のプロバイダ）</strong><br />
　それぞれのInfoProviderは、処理後foo:findAggregatorに戻ります。<br />
<div style="background-color:#FFCC99; padding:2px">&nbsp;&nbsp;&lt;sm:activationSpec&nbsp;componentName="provider1"&nbsp;&nbsp;&nbsp;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;service="foo:provider1"<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font style="color:red; font-weight:bold">destinationService="foo:findAggregator"</font>&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&lt;sm:component&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;bean&nbsp;xmlns="http://xbean.org/schemas/spring/1.0"<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;class="demo.servicemix.se.Adaptor"&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;property&nbsp;name="infoProvider"&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;bean&nbsp;class="demo.info.impl.InfoProviderImpl1"&nbsp;/&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/property&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/bean&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/sm:component&gt;<br />
&nbsp;&nbsp;&lt;/sm:activationSpec&gt;<br />
</div></p>

<p><br />
<strong>exProvider（別サーバ・インスタンスのInfoProviderを呼び出す）</strong><br />
　これも戻り先自体はfoo:findAggregatorです。<br />
<div style="background-color:#FFCC99; padding:2px">&nbsp;&nbsp;&lt;sm:activationSpec&nbsp;componentName="exProvider"&nbsp;&nbsp;&nbsp;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;service="foo:exProvider"<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<font style="color:red; font-weight:bold">destinationService="foo:findAggregator"</font>&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&lt;sm:component&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;bean&nbsp;xmlns="http://xbean.org/schemas/spring/1.0"<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;class="demo.servicemix.se.ExHttp"&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;property&nbsp;name="outBoundName"&nbsp;value="exServiceOutBound"&nbsp;/&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/bean&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/sm:component&gt;<br />
&nbsp;&nbsp;&lt;/sm:activationSpec&gt;</p>

<p>&nbsp;&nbsp;&lt;sm:activationSpec&nbsp;componentName="exServiceOutBound"&nbsp;&nbsp;&nbsp;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;service="foo:exServiceOutBound"&nbsp;&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&lt;sm:component&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;bean&nbsp;xmlns="http://xbean.org/schemas/spring/1.0"<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;class="org.servicemix.components.http.HttpInvoker"&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;property&nbsp;name="url"&nbsp;value="http://localhost:8912"/&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/bean&gt;<br />
&nbsp;&nbsp;&nbsp;&nbsp;&lt;/sm:component&gt;<br />
&nbsp;&nbsp;&lt;/sm:activationSpec&gt;<br />
</div></p>

<p><br />
　というわけで、非常に直感的ではないでしょうか。もちろん単純だからということもありますが、コンポーネントのフローがよく分かります。</p>

<p>　つまり、このデモアプリケーションというのは、ServiceMixによってコンポーネントとメッセージ交換で組み上げられたものなわけです。しかも、その組み合わせ方が「同期で呼ばれていたものを、内部的には非同期同時でビジネスロジックを処理する」という、それなりに複雑なものです。<br />
　そのためにInfoProvider以外はビジネスロジックではなくコントロールとして機能しています。asyncForwarderは同期・非同期の切り替えであり、findAggregatorは非同期実行と実行結果の集約というコントロールなのです。<br />
　「こんなに組み合わせるなんてめんどうだなぁ」と感じられるかもしれません。しかし、このようにロジックとコントロールが明確に分離されることで、アプリケーションとして変化に適応できる可能性があることに気づきます。BCを変更すればJMSやWebサービスからも呼び出し可能でしょうし、findAgregatorの機能を上げればタイムアウトなども実現することもできます。もちろんビジネスロジックであるInfoProviderを変更する必要性はありません。</p>

<p>　なお、こういったアイデアはEAIやBPM（ビジネス・プロセス・マネージメント）と変わるないように感じるかもしれません。しかし、これまでのEIAやBPMはIFなどの単純な処理しかできなかったのに比べると、BPELやJBIはそのよりも遥かに細かい上の処理が可能になります（思い出してください。BPELはBusiness Process Execution <strong>Language</strong> for Web Servicesの略です）。</p>

<p>　ここで感じていただきたいのは、ServiceMixが実現しているものが、きっと皆さんが思うSOAとは一線を画しているということです。ServiceMixによってSOAの本質である「メッセージ交換による疎結合」というのがDIコンテナに持ち込まれました。その結果、SOA的アプローチは普通のアプリケーションの構築においても十分に利用可能になったわけです。<br />
　僕はこれを「コンテナベース・サービス・インテグレーション」と呼んでみたわけですが「コンテナベース・コンポーネント・リミックス（マッシュアップ）」という感じの方が正確かもしれません（IBMやBEAを主導にSCA（サービス・コンポーネント・アーキテクチャ）という取り組みもはじまっているのですが、その話は別の機会に）。</p>

<p>　丸山先生はこのservicemix.xmlを見て</p>

<blockquote>- ただ、ServiceMixのXbeanの記述を見ていると、DIコンテナの単なるコンフィグ・ファイルとして以上の意味を、それが潜在的に持っていることに気づく。

<p>- それが表現しているものを、単なるInjectされるべきオブジェクトの宣言としてではなく、ネットワーク上の複数のコンポーネントの結合の青写真を与えるものと考えると面白い。</p>

<p>- Grid風に言えば、それはVirtual Organizationの見取り図を与えているのである。</p>

<p>- BPELも似たような特徴をもつのだが、我々は、ネットワーク上のサービスをプログラムする、新しいメタ・プログラム言語が誕生する時代の入り口に立っているのかもしれない。</blockquote></p>

<p>と指摘されています。若干、大げさだとは思いますが意図している感覚は伝わるのではないでしょうか。</p>

<p><br />
　で、なんか結論らしいものがないのですが、僕もはっきりといえるほどに使い込んでいないのが現状です。ただ、こうした手法を業務アプリケーションに組み込んでいくことで、ロジックとコントロールの分離が明確になり、よりアプリケーションの構造が柔軟になることは間違いないと思います（と、信じたいｗ）。<br />
　来年は、こうした考え方（もはやSOAという印象からは離れているかもしれませんが）によるアプリケーション構築が大きな課題になることでしょう。<br />
　EIA的なSOAに取り組み必要性はまったくないのですが、こういう小さなSOAには、どんどん手を出していただきたいと思うわけです（安いし、手の届く範囲でできるし）。</p>

<p><br />
　では、次回は視点を変えてInfoProviderの仕組みを見てみます。これによって、現状のServiceMixの課題が見えてくるはずです。</p>

<p><br />
<a href="http://www.arclamp.jp/blog/archives/cat_servicemix.html">このServiceMix連載の一覧</a></p>]]></description>
<dc:subject>servicemix</dc:subject>
<dc:creator>yusuke</dc:creator>
<dc:date>2005-12-14T01:09:57+09:00</dc:date>
</item>
<item rdf:about="http://www.arclamp.jp/blog/archives/000740.html">
<title>ServiceMix (3) servicemix.xmlの概要</title>
<link>http://www.arclamp.jp/blog/archives/000740.html</link>
<description><![CDATA[<p>　では、いよいよServiceMix本体の話になってきます（デモのダウンロードは<a href="http://www.arclamp.jp/blog/archives/000737.html">ServiceMix (1) JBIとServiceMix</a> から）。</p>

<p>　まずServiceMixそのものの作りを簡単に説明しておきましょう。ServiceMixはSpring上で動きます。現状ではSpringにしか対応していない部分もありますが、比較的簡単に他のDIコンテナ上で動かすことも出来るはずです（ServiceMix1はGeronimo上で動くようにしている人がいました）。</p>

<p>　はじめに書いた通りJBIではコンテナにBCやSEをデプロイします。そのためのデプロイメント・ディスクリプタは非常に煩雑になっています。あとで説明しますが、ServiceMixはSpringを利用してBC、SEをデプロイするので、デプロイを単純にしており、さらにBCやSEをSpringのBeanと同じようにDIの対象することができます。</p>

<p>　このためにServiceMix2の定義ファイルに利用されているのが<a href="<br />
http://xbean.codehaus.org/">xbean</a>です。xbeanはGeronimoプロジェクトからの派生なのですが、設定ファイルを便利に書くためのライブラリで、Springもサポートしています。"Spring in Action" のCraig Walls氏がブログに書いた<a href="http://jroller.com/page/habuma/20051011">Spring Simplified with XBean</a>にまとまっていますが、Springの記述を単純にするための1つの解決策として注目されています。<br />
　上記のエントリからの抜粋ですが、</p>

<div style="background-color:#FFCC99; padding:2px"><pre>
&lt;beans&gt;
  &lt;bean id="knight"
      class="....KnightOfTheRoundTable"&gt;
    &lt;property name="moniker"&gt;
      &lt;value&gt;Bedivere&lt;/value&gt;
    &lt;/property&gt;
    &lt;property name="quest"&gt;
      &lt;ref bean="quest"/&gt;
    &lt;/property&gt;
  &lt;/bean&gt;
&lt;/beans&gt;
</pre></div>

<p>　と書くところを</p>

<div style="background-color:#FFCC99; padding:2px"><pre>
&lt;beans&gt;
  &lt;knight id="knight" moniker="Bedivere"
      xmlns="http://springinaction.com/schemas/knight"&gt;
    &lt;myQuest&gt;&lt;quest/&gt;&lt;/myQuest&gt;
  &lt;/knight&gt;　
&lt;beans&gt;
</pre></div>

<p>　って感じで書けちゃいます。つまり特定のクラスをBeanとして登録するのに専用のタグが使えるようになっているのです。</p>

<p>　コンポーネント・コンポジットをXMLファイルで表現しようとすると、エクテンション・ポイントの形式を明示するために専用のXMLの要素（タグ名）や属性を追加したくなります。<a href="http://jakarta.apache.org/tapestry/">Tapestry</a>の<a href="http://jakarta.apache.org/hivemind/">HiveMind</a>ではいきなり要素を追加してしまうのですが、そうするとXMLのバリデーションが利用できなくなります。xbeanがスマートなのは名前空間によってXMLのバリデーションを生かしたままで良い点です。この手法は流行ると思います。</p>

<p>　ServiceMixではJBIのサービス名やエンドポイント名などを指定するために利用しています。まじめにSpringで記述していると非常に見にくくなってしまうのですが、xbeanのおかげでかなり直感的にすることができるのです。</p>

<p><br />
　では、servicemix.xmlの見方を説明します。まずサンプルを見てください。</p>

<div style="background-color:#FFCC99; padding:2px"><pre>
&lt;beans&nbsp;xmlns="http://xbean.org/schemas/spring/1.0"
&nbsp;&nbsp;&nbsp;&nbsp;xmlns:spring="http://xbean.org/schemas/spring/1.0"
&nbsp;&nbsp;&nbsp;&nbsp;xmlns:sm="http://servicemix.org/config/1.0"
&nbsp;&nbsp;&nbsp;&nbsp;xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
&nbsp;&nbsp;&nbsp;&nbsp;xsi:schemaLocation="
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;http://xbean.org/schemas/spring/1.0&nbsp;spring-beans.xsd
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;http://servicemix.org/config/1.0&nbsp;servicemix.xsd"
&nbsp;&nbsp;&nbsp;&nbsp;xmlns:foo="http://servicemix.org/demo/"&gt; ...[1]
&nbsp;
&nbsp;&nbsp;&lt;sm:container&nbsp;spring:id="jbi"
&nbsp;&nbsp;&nbsp;&nbsp;useMBeanServer="false"
&nbsp;&nbsp;&nbsp;&nbsp;createMBeanServer="false"
&nbsp;&nbsp;&nbsp;&nbsp;dumpStats="true"
&nbsp;&nbsp;&nbsp;&nbsp;statsInterval="10"&gt; ...[2]
&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;sm:activationSpecs&gt;
&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;sm:activationSpec&nbsp;id="hoge"
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;service="foo:hoge"
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;destinationService="foo:bar"&gt;  ...[3]
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;sm:component&gt; ...[4]
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;bean&nbsp;xmlns="http://xbean.org/schemas/spring/1.0"
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;class="foo.Hoge"&nbsp;/&gt; ...[5]
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/sm:component&gt;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/sm:activationSpec&gt;  
&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;sm:activationSpec&nbsp;...
&nbsp;
&nbsp;&nbsp;&nbsp;&nbsp;&lt;sm:activationSpecs&gt;
&nbsp;
&nbsp;&nbsp;&lt;/sm:container&gt;
&nbsp;
&lt;beans&gt;
</pre></div>

<p>　要素beans[1]はxbeanのためにXMLスキーマを設定しています。属性xmlns:fooは、あとで出てきますがサービスのための名前空間fooを定義しています。</p>

<p>　内側の要素sm:container[2]がJBIコンテナになります。JBIはJMXでの管理を行うことができるので、その設定を行います。今回のデモは1台の端末に2つのServiceMixを立ち上げる都合上、ポート番号がぶつかってしまうために属性useMBeanServerをfalseにしています（本当はポート番号をかえてあげるべきですが）。<br />
　ServiceMixは、このJBIコンテナだけをSpring上にデプロイしているだけです。ですからSpring内包しているわけではなく、BCやSEをJBIコンテナにデプロイするためだけにDIコンテナをうまくつかっています。そのため、他のDIコンテナへの移行も単純に行える訳です。</p>

<p>　要素sm:activationSpec[3]がJBIのコンポーネント、つまりBCやSEを"アクティベート"するために使われています。ここの説明がややこしいのですが、JBIのコンポーネントはロジックを書くだけではなく、JBI上で機能するために様々な情報を登録する必要性があります。具体的にはサービス名やエンドポイント名をNMRに登録するのですが、これをアクティベートと呼びます（ここらへんはWSDL2の話なので、なんとなく名前を登録するんだ程度の理解で良いです）。<br />
　ServiceMixではJBIへのデプロイを単純にするためにアクティベート処理を行ってくれるコンポーネントをアダプタとして用意し、そのコンポーネントにビジネスロジックをインジェクトするように設計されています。こうすることでアクティベート処理とビジネスロジックを完全に切り離しているのです。ここらへんもDIをうまく利用しているなぁと感じます。<br />
　属性id（もしくはcomponentName）が、実コンポーネントを呼び出す名前です。次の属性serviceがJBI上でのサービス名になります。JBIでは名前空間を付けることでサービス名を定義します。Javaのパッケージ名と同じですが、こうすることで複数のサービスを名前をかぶることなく利用できるようになるわけです。なおエンドポイント名は指定しなければサービス名と同じになります。最後の要素destinationServiceが、次にルーティングするサービスを示します。<br />
　<br />
　その内側の要素sm:component[4]が、これまたアダプタになっています。JBIではメッセージ交換するために様々なコンポーネントを利用します。それらを全部扱うのは大変なのでベースクラスを提供し、これを継承する形でロジックを記述します。そのベースクラスに、いろいろなものをインジェクトする必要性があるので、それを行うためのアダプタになります。</p>

<p>　そして要素bean[5]が、ようやく普通のSpringの設定になります。属性xmlnsを指定することで、その内側については名前空間の指定が不要になります。</p>

<p><br />
では、ここからは実際の各コンポーネントを見ていきましょう、、と思ったのですが、長くなったのでまた次回ということで（w。</p>

<p><a href="http://www.arclamp.jp/blog/archives/cat_servicemix.html">ServiceMixの一覧</a></p>]]></description>
<dc:subject>servicemix</dc:subject>
<dc:creator>yusuke</dc:creator>
<dc:date>2005-12-11T22:50:24+09:00</dc:date>
</item>
<item rdf:about="http://www.arclamp.jp/blog/archives/000739.html">
<title>ServiceMix (2) デモアプリケーション概要</title>
<link>http://www.arclamp.jp/blog/archives/000739.html</link>
<description><![CDATA[<p>　さて、今回はデモの概要です（デモのダウンロードは<a href="http://www.arclamp.jp/blog/archives/000737.html">ServiceMix (1) JBIとServiceMix</a> から）。</p>

<p><strong>ビジネス概要</strong><br />
　ダウンロードできるアプリケーションでは京都を題材にしてあります。京都を訪れたい人が、人数とグループ（家族／友達／男のみ／女のみ）を入力すると、地図上にその条件にあった情報を表示しします。あとは、情報の詳細をみたり、それらを任意に選び経路を表示させることができます。表示される情報は寺院の情報、イベントの情報、そして百貨店の情報です。</p>

<p><img src="/blog/archives/images/servicemix_demo1.gif" width="420" height="168" /></p>

<p>　WindowsでJDK1.4.2以降が入っているなら、<a href="http://www.arclamp.jp/blog/archives/000737.html">ダウンロード</a>して解凍するだけですぐに試せるようになっています（Macはmaven経由でいけました。Linuxは文字化けしたらごめんなさい）。お試しください。</p>

<p><br />
<strong>Web層の概要</strong><br />
　Web層はいわゆるAjax（Asynchronous JavaScript + XML）です。地図はGoogle Mapsから読み込み、京都の情報はServiceMixからXML形式で取得しています。わかりやすいWeb2.0的Mush-upサイトでしょう。<br />
　Ajaxの作りを簡単に。ダウンロードしたフォルダのsrc\webappにあるmymap.js（UTF-8）を見てください。最下部のメソッドcreateMyMapは<a href="http://www.google.com/apis/maps/documentation/">基本的なGoogle Mapsの実装</a>をしています。つまりサーバがServiceMixであることは意識していません。ちょっと特別なのはリクエストでXMLをPOSTしている点だけです。</p>

<div style="background-color:#FFCC99; padding:2px"><pre>
  var request = GXmlHttp.create();
  request.open('POST', '/exec/jbi/', true);
  request.onreadystatechange = function() {
    if (request.readyState == 4) {
      //受信後の処理（マーカーをおいて、リストに追加）
    }
  }
  request.setRequestHeader("content-type", 
      "application/x-www-form-urlencoded;charset=UTF-8");
  request.send(DemoUtil.serializeRequestInfos(document.
      getElementById('entryform')));
</pre></div>

<p><br />
<strong>サーバロジックの概要</strong><br />
　サーバには複数のInfoProviderというサービスがデプロイします。InfoProviderはユーザーが入力した条件をキーにして情報を返却します。今回は寺院の情報、イベントの情報、百貨店の情報を提供する3つのInfoProviderが存在します。ServiceMixはデプロイされた3つのInfoProviderを呼び出し、それらの情報を集積しています。</p>

<p><strong><br />
ServiceMix内の概要</strong><br />
　ServiceMixで実行すべきことは、以下の2つです。<br />
1.クライアントからのリクエストをJBIに流し込み<br />
2.InfoProviderを呼び出し、その結果をまとめてレスポンス</p>

<p>　まずは次の図を見てください（クリックで拡大図を表示）。</p>

<p><a href="/blog/archives/images/servicemix_demo2_l.gif" target="_blank"><img src="/blog/archives/images/servicemix_demo2.gif" width="420" height="238" border="0" /></a></p>

<p>　左側がブラウザになります。オレンジの丸がServletになっており、HTTPリクエストのストリームをそのままhttpBinding（Hb）に流し込みます。</p>

<p>　httpBinding（Hb）はBindingComponentの役割を果たしておりMeesageExchangeに変換後、asyncForwarder（As）に渡します。</p>

<p>　asyncForwarder（As）は、同期（InOut）から非同期（InOnly）への変換を行います。ご存知の通りHTTPは同期メッセージですが、InfoProviderを非同期実行させるために変換をする必要性があります。内部的には、受け取った同期メッセージを取っておきつつ、その内容をコピーして、findAggregator（Ag）に新しい非同期メッセージをしています。</p>

<p>　findAggregator（Ag）は、指定された複数のService Engineにメッセージを非同期で送信し、それらの結果（XML）を全て集めて返却します。</p>

<p>　今回の設定ではP1、P2がInfoProviderになります。そしてexP最後の1つだけに仕掛けがしてあります。exPは、そのままexServiceOutBound（OB)に処理を流しています。</p>

<p>　exServiceOutBound（OB）は、HTTPでアウトバウンドを行うBindingComponentです。アウトバンド先は別サーバインスタンスのServiceMixになります。</p>

<p>　別サーバインスタンスのServiceMixの中は、これまでの説明どおりの仕組みになっています。そして3つ目のInfoProvider（Pex）がデプロイされています。</p>

<p><br />
　つまりInfoProviderは全部で3つではありますが、2つが同インスタンス内に元々デプロイされているものですが、最後の1つだけは別サーバインスタンス内にあり、そことはHTTP（REST）を通じて処理を依頼しているのです。3つのInfoProviderを動かすためではありますが、必要な処理に分解していくと、このような形で実現することになります。</p>

<p>　めんどうだなぁと感じるかもしれませんが、実はInfoProvider以外は全て汎用的なコンポーネントです。後でInfoProviderの説明をしますが中身はPOJOです。POJOを作成するだけで、あとは処理手順を組み合わせるだけでアプリケーションができていると考えると面白くありませんか？</p>

<p><br />
　これをServiceMix上で設定するためのservicemix.xmlを説明しようと思っていたのですが、長くなったので切ります。次回はservicemix.xmlです。</p>

<p><a href="http://www.arclamp.jp/blog/archives/cat_servicemix.html">ServiceMixの一覧</a></p>]]></description>
<dc:subject>servicemix</dc:subject>
<dc:creator>yusuke</dc:creator>
<dc:date>2005-12-10T21:00:42+09:00</dc:date>
</item>
<item rdf:about="http://www.arclamp.jp/blog/archives/000738.html">
<title>デブサミ2006登壇します＆受付開始</title>
<link>http://www.arclamp.jp/blog/archives/000738.html</link>
<description><![CDATA[<p><a href="http://www.seshop.com/event/dev/">デブサミ2006</a>（2006年2月9日、10日）に登壇いたします。</p>

<blockquote>2006/2/10　15:25〜16:15

<p><strong>【10-D-5】Web2.0的SOA Ajax+DI+JBIで実現する疎結合アーキテクチャ</strong></p>

<p>世間で騒がれてはいるものの接点の少なそうなWeb2.0とSOA。セッションでは両者を比較しながらWeb2.0的SOAを考えてきます。そしてデモを通じてAjax、DI、JBIを組み合わせた疎結合アーキテクチャを知れば、ワクワクするような可能性を感じられるはずです。</blockquote></p>

<p><br />
　デブサミ2006のテーマは「Developer 2.0」だそうです。</p>

<blockquote>Developers 2.0は、より高い志を持ち、より広い視野で仕事を捉え、より深い洞察で未来を見据えることのできる人たちであると定義することができます。</blockquote>

<p>　ということなので、未来を予測できる内容にしたいと考えています。このブログで何回も書いていますが、SOAを単にBuzzワードとしてとらえるのではなく、そこで使われている技術の本質（おいしいところ）だけを取り出すことで、アプリケーションの作り方がまったく変わると思っています。それをいち早く実現しているのがWeb2.0の流れでしょう。Javaの世界のWeb2.0、つまりWeb2.0的SOAというのが次世代のアプケーション開発手法ではないか、という提唱が講演の骨子になります。</p>

<p>　内容としてはServiceMix周りになります。JavaOne、丸山先生レクチャーシリーズと基本的には同じではありますが、この２回を経て、伝えたいポイントも明確になってきましたし、今までの２回を聞いた方にも違う視点を提供できるようにがんばりたいと思います（あと、なんかサプライズを用意したいとは思っています）。</p>

<p><br />
　翔泳社のご担当者が「デブサミは年１回のデベロッパーの皆様への還元です」といわれていました。もちろん無料ですし、それだけに多くのDevelopers 2.0な人々が協力をしています。<br />
　僕としては、20代後半から30歳代で「開発者としての自分に悩みを持っている現場の人」に来ていただきたいです。たぶん、そういう方は日々の業務がとっても忙しくて、それどころじゃないって思われるかもしれません。でも、そういう方こそDevelopers 2.0に触れていただきたいと心から思います。この２日間はプロジェクトが止まったっていいじゃないですか（おい！）。この２日間で学んだことが未来のプロジェクトの、なにより自分の役に立つはずです。ぜひ、参加をご検討ください。</p>

<p><a href="http://www.seshop.com/event/dev/2006/limited/2days/touroku/index.html">デブサミ2006　参加登録</a><br />
</p>]]></description>
<dc:subject>その他の話題</dc:subject>
<dc:creator>yusuke</dc:creator>
<dc:date>2005-12-10T12:10:15+09:00</dc:date>
</item>
<item rdf:about="http://www.arclamp.jp/blog/archives/000737.html">
<title>ServiceMix (1) JBIとServiceMix</title>
<link>http://www.arclamp.jp/blog/archives/000737.html</link>
<description><![CDATA[<p>　2005/12/9に行われた丸山先生レクチャー内で行ったデモをダウンロードできるようにしました。この際なので、ちゃんとまとめてみました。連載で解説を行いたいと思います。</p>

<p><a href="http://www.c-sq.com/modules/article/article66.html">資料のダウンロード</a>（最下部の20051209_demo_1.pdfか20051209_demo_6.pdf）</p>

<p><a href="/blog/archives/resources/meegle_20051210.zip">デモアプリのダウンロード</a>（meegle_20051210.zip 13.2MB）</p>

<p>1.適当なところで解凍する<br />
2.servicemix.batを実行する<br />
3.servicemixEx.batを実行する<br />
4.<a href="http://localhost:8080/exec/">http://localhost:8080/exec/</a>にアクセスする<br />
5.なんとなく試す</p>

<p>注：JDK1.4.2以降。8080ポート使うのでTomcatなどは落としておくこと。Firefox推奨。IEで経路を表示したい場合は<a href="http://msdn.microsoft.com/workshop/author/VML/ref/appendix.asp">VMLの設定</a>をおこなう必要があります。Mac OS Xで動確しました（maven servicemix, maven servicemixExでいけます）。</p>

<hr />

<p><strong>JBIとは</strong><br />
　丸山先生の資料からの抜粋になります（<a href="http://www.c-sq.com/modules/article/article66.html">ダウンロード</a>）。ようは、メッセージ交換を通じたサービスのコンポジットによるアプリケーション構築手法の標準仕様です（意味不明？）。</p>

<p>　JBIの特徴はJBIコンテナと呼ばれるコンテナを用意し、その上にコンポーネントをデプロイしたうえで、それらのメッセージ交換手法だけを標準化していることです。ですから、コンテナの外側はなんでもよいことになります。その、なんでも良いプロトコルをJBIコンテナ内部に入れる際には、JBI手順に変換する必要性があります。その役目を負うコンポーネントをBinding Component（バインディング・コンポーネント。略称BC）と称します。その他のコンポーネントはService Engine（サービス・エンジン。略称SE）と呼びます。これらは特定の実装があるわけではなく、単に役割として考えてもらえばいいです。</p>

<p><img src="/blog/archives/images/maruyama_jbi_1.gif" height="311" width="414" /><br />
(丸山先生の資料から抜粋）</p>

<p>　メッセージ交換はバス（ルータ）を通じて行います。これをNormalized Message Router（ノーマライズド・メッセージ・ルータ。略称NMR）と呼びます。出発点であるコンシューマーはメッセージや行き先情報をMessageExchangeというモノに詰め込みNMRに投げ込みます（Send）。一方、サービスを提供するプロバイダは、NMRに対してAcceptを行うことで自分宛のMessageExchangeを取得して処理を行うわけです。このようにしてサービスの利用者と提供者が、メッセージ交換によって疎結合の関係にあるわけです。ここらへんがSOA的なところでしょう。<br />
　なお、プロバイダは、一番最初に自分自身をNMRに登録します。それをアクティベーションと呼びます。</p>

<p><img src="/blog/archives/images/maruyama_jbi_2.gif" height="311" width="414" /><br />
(丸山先生の資料から抜粋）</p>

<p>　整理すると、JBIコンテナ内には"外部とやり取りするBC"と"ロジックを担当するSE"がデプロイされています。そいつらはメッセージ（MessageExchange）を交換することで協調して動作します。サービス利用者（コンシューマ）がメッセージをNMRに送ると、サービス利用者（プロバイダ）がメッセージを受け取り処理を行うわけです。</p>

<p>　また、メッセージ交換には種類があります。簡単にいえば同期と非同期です。同期は返事を待つまで処理を止めるもの。非同期は返事を待ちません。例えばHTTPは同期メッセージの代表格です。サーバが処理している間、ブラウザはじっと待っています。</p>

<p>　基本的にJBIで重要なのはこれだけです。あとはServiceとEndPointとかServiceUnitとデプロイとか、いろいろありますが、そんなに大切ではありません。</p>

<p><br />
<strong>ServiceMixとは</strong><br />
　ここまで書いて、すでに疲れました（ｗ。ServiceMixは、JBIのいいとこ取りをして、めんどうなところを省いてしまったものです。ですからJBI"準拠"です。丸山先生に言い方を借りれば、JBIとServiceMixの関係はEJB2とEJB3みたいなものです。彼らは、"bending the JBI specification some, but keeping to the APIs（APIは守るけど仕様はちょい曲げる）"と明記しています（<a href="http://servicemix.org/Does+JBI+depend+on+XML+and+WSDL">http://servicemix.org/Does+JBI+depend+on+XML+and+WSDL</a>）。<br />
（正確に言えば、EJB3はEJB2をラップしただけで本質的には変わらないわけですが、ServiceMixは、そういうしがらみもなく、必要のないものは本当にばっさりと無視しています）。</p>

<p>　ですから、ServiceMixをやってもJBIを完全に理解したとはいえないのですが、JBIの本質は理解することができます。しかも、JBIよりも遥かに直感的に理解することができるのです。</p>

<p>　ServiceMixはコンポジット・アプリケーションの構築手法です。SOAというBuzzワードとして理解する必要はありません。僕が熱くなって書いてしまいましたが<a href="http://www.arclamp.jp/blog/archives/000728.html">コンテナベース・サービス・インテグレーション</a>というものです（たぶん）。</p>

<p>　丸山先生もJBIの仕様書を読んでいるだけでは分からなかったことが、ServiceMixを通じることで理解することができたと言われています。この感覚を知るためにも、ぜひコードを見て、感じてみてください。それぞれの感じ方だとは思いますが、SOAというアイデアが非常に身近であることに気づいていただければと思います。</p>

<p>　さて、次回はデモアプリの概要とservicemix.xmlの説明を書いてみたいと思います。</p>

<p><a href="http://www.arclamp.jp/blog/archives/cat_servicemix.html">ServiceMixの一覧</a></p>]]></description>
<dc:subject>servicemix</dc:subject>
<dc:creator>yusuke</dc:creator>
<dc:date>2005-12-09T23:59:00+09:00</dc:date>
</item>
<item rdf:about="http://www.arclamp.jp/blog/archives/000736.html">
<title>好きだから</title>
<link>http://www.arclamp.jp/blog/archives/000736.html</link>
<description><![CDATA[<p>　今日は友人の紹介でファシリテーターの鬼（というか仙人？）<a href="http://blog.livedoor.jp/facilitators/">田村洋一さん</a>と忘年会。</p>

<p>　その中で心に残った言葉が「好きだから」という話です。<br />
　例えば考古学者がなぜ歴史を調べるのかといえば、「未来知るため」「過去から学ぶため」いろいろ理由を答えるけど、聞かれたから答えただけ。きっとそれは、好きだから。</p>

<p>　「好きだから」とは２つのことしか求めない。それは生きることと死ぬこと。その全てが好きであること。なにも理由なんかいらない。<br />
　理由があるってことは、うそ。好きってことにロジカルな理由なんてない。<br />
　すべてを突き抜けて、それこそ生死に関係なく好きだと言えること。それは「好きだから」という以上のことはないのです。</p>

<p>　僕が「好きだから」という言葉にであったのは就職活動の時。社長面接で「なんで入りたいの？」と聞かれ、うだうだと理由を述べてみる。それが、あまりにだめだめだったのでしょう、人事の人が助け舟を出してくれました。</p>

<p>「ようは好きだってことだよね」</p>

<p>　その時始めて気づいたんです。「好きだから」っていう理由がありなんだって。そう「好きだ」ってことにそれ以上の言葉なんていらないって。</p>

<p>　「好きだから」。この言葉をいえる人はすごく強い。きっと、自分の感覚を信じるってことだから。直感ほど重要なものはないのです。</p>

<p>　空気のように、それがなかったら死んだも同然と思えること。でも、すごく自然にそこに存在するもの。っていうか、なくなるなんてことが想像すら出来ない。それが「好き」。（だから、失って初めて気づくんだと思うよ）</p>

<p>　「好きだから」。ま、大なり小なりはあっても、この言葉を、まっすぐにいえるように生きていたいと思いました。</p>

<p><br />
　って、ファシリテーター関係ないじゃん（w。また、いっぱいお話しさせてくださいませ。</p>]]></description>
<dc:subject>ライフ</dc:subject>
<dc:creator>yusuke</dc:creator>
<dc:date>2005-12-09T00:17:56+09:00</dc:date>
</item>
<item rdf:about="http://www.arclamp.jp/blog/archives/000734.html">
<title>丸山先生レクチャーシリーズ第2回目に登壇</title>
<link>http://www.arclamp.jp/blog/archives/000734.html</link>
<description><![CDATA[<p>　今週金曜日（12/9）の<a href="http://www.c-sq.com/modules/article/article66.html">丸山先生レクチャーシリーズ第２回</a>で登壇することになりました。13:40〜15:00の丸山先生が講演する時間内でServiceMixのデモ＆ショートプレゼンを担当させていただきます。</p>

<p>　デモはJavaOneでやってものをシンプルにしたものになります。でもServiseMixの2.0.2対応するだけで異様に時間がかかってしまいました。なにやらXMLパーサーが変な動きをするんですよねぇ。なんなのでしょうか。<br />
　ついでにJavaOneで気になっていた点を修正してみました。ほんと、よせばいいのに。こういうことをプロジェクトでもやって、はまるタイプです。ええ、そうですとも。</p>

<p>　っていうかですよ、講演まで１週間を切ってからから依頼する先生も先生です。そのときの言葉が、</p>

<blockquote>鈴木さん、お願いがあります。

<p>ランプをなでています。jiniが現れますように。</blockquote></p>

<p>でした（w。先生のメールは本当に面白いです。もちろん快諾させていただきました。</p>

<p>　こちらとしてはJavaOneで展示会場のNEC休憩場所にいた先生を捕まえてプレゼンし、レクチャーシリーズに行くというアピールもし、想定内の出来事ではありますが、いかんせん急です。ただ追いつめられないと本気をだせないタイプなのでちょうどいいのかもしれません。</p>

<p>　そんなわけで各方面に若干の滞りがありますが、なんとかしますので、お許しを。<br />
　</p>

<p>　残念ながら、レクチャーシリーズの参加は締め切られてしまっていますが、当日参加される方はお声かけくださいませ。当日の心配は先生の資料が100枚を超える中で、僕の話す時間がどれくらいとれるかということです。</p>

<p><br />
2005年12月9日（金） 13:30〜18:00（受付13:00〜13:30）<br />
日立ソフトウェアエンジニアリング株式会社　4F講堂</p>]]></description>
<dc:subject>Java</dc:subject>
<dc:creator>yusuke</dc:creator>
<dc:date>2005-12-07T01:52:31+09:00</dc:date>
</item>


</rdf:RDF>
<?xml version="1.0" encoding="gb2312" ?>
<!--
Source: http://www.w3cn.org/article/rss.xml
Expect: GB18030
-->
<rss version="2.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:trackback="http://madskills.com/public/xml/rss/module/trackback/" xmlns:wfw="http://wellformedweb.org/CommentAPI/">
	<channel>
		<title>网页设计师 -  文章教程</title>
		<link>http://www.w3cn.org</link>
		  <description><![CDATA[新网页设计师,web标准的教程站点,推动web标准在中国的应用]]></description>
		<language>zh</language>
		<generator>RSS Generated by ActiveContent 3.5</generator>


		<item>
			<author>阿宏</author>
		<title>如何以及何时使用sIFR</title>
	<link>http://www.w3cn.org/article/translate/2005/117.html</link>
	<pubDate>2005-5-28 16:05:36</pubDate>
	<guid>http://www.w3cn.org/article/translate/2005/117.html</guid>
  </item>

		<item>
			<author>阿捷</author>
		<title>HTML4标签的默认样式列表</title>
	<link>http://www.w3cn.org/article/tips/2005/116.html</link>
	<pubDate>2005-5-25 9:51:45</pubDate>
	<guid>http://www.w3cn.org/article/tips/2005/116.html</guid>
  </item>

		<item>
			<author>JunChen</author>
		<title>在IE中为abbr标签加样式</title>
	<link>http://www.w3cn.org/article/translate/2005/115.html</link>
	<pubDate>2005-5-24 9:56:57</pubDate>
	<guid>http://www.w3cn.org/article/translate/2005/115.html</guid>
  </item>

		<item>
			<author>JunChen</author>
		<title>alt属性和title属性</title>
	<link>http://www.w3cn.org/article/translate/2005/114.html</link>
	<pubDate>2005-5-23 12:00:35</pubDate>
	<guid>http://www.w3cn.org/article/translate/2005/114.html</guid>
  </item>

		<item>
			<author>dodo</author>
		<title>左中右3栏布局中最先显示中栏内容的方法 </title>
	<link>http://www.w3cn.org/article/tips/2005/113.html</link>
	<pubDate>2005-5-17 14:39:50</pubDate>
	<guid>http://www.w3cn.org/article/tips/2005/113.html</guid>
  </item>

		<item>
			<author>nobita</author>
		<title>CSS中的滑动门技术</title>
	<link>http://www.w3cn.org/article/translate/2005/112.html</link>
	<pubDate>2005-5-8 9:39:31</pubDate>
	<guid>http://www.w3cn.org/article/translate/2005/112.html</guid>
  </item>

		<item>
			<author>阿宏</author>
		<title>用CSS定义标题的几个实例</title>
	<link>http://www.w3cn.org/article/translate/2005/111.html</link>
	<pubDate>2005-4-21 18:21:15</pubDate>
	<guid>http://www.w3cn.org/article/translate/2005/111.html</guid>
  </item>

		<item>
			<author>阿宏</author>
		<title>定义标题的最好方法</title>
	<link>http://www.w3cn.org/article/translate/2005/110.html</link>
	<pubDate>2005-4-15 20:45:18</pubDate>
	<guid>http://www.w3cn.org/article/translate/2005/110.html</guid>
  </item>

		<item>
			<author>阿捷</author>
		<title>捷足先登学用CSS：HTML结构化</title>
	<link>http://www.w3cn.org/article/translate/2005/109.html</link>
	<pubDate>2005-4-15 12:29:43</pubDate>
	<guid>http://www.w3cn.org/article/translate/2005/109.html</guid>
  </item>

		<item>
			<author>阿捷</author>
		<title>_blank开新窗口不符合标准?</title>
	<link>http://www.w3cn.org/article/tips/2005/107.html</link>
	<pubDate>2005-4-13 10:05:32</pubDate>
	<guid>http://www.w3cn.org/article/tips/2005/107.html</guid>
  </item>

		<item>
			<author>阿捷</author>
		<title>如何用CSS制作横向菜单?</title>
	<link>http://www.w3cn.org/article/tips/2005/105.html</link>
	<pubDate>2005-4-11 16:54:52</pubDate>
	<guid>http://www.w3cn.org/article/tips/2005/105.html</guid>
  </item>

		<item>
			<author>翻译：阿捷</author>
		<title>CSS的十八般技巧</title>
	<link>http://www.w3cn.org/article/translate/2005/104.html</link>
	<pubDate>2005-4-10 22:03:25</pubDate>
	<guid>http://www.w3cn.org/article/translate/2005/104.html</guid>
  </item>

		<item>
			<author>阿捷</author>
		<title>常用CSS缩写语法总结</title>
	<link>http://www.w3cn.org/article/tips/2005/103.html</link>
	<pubDate>2005-4-8 15:28:00</pubDate>
	<guid>http://www.w3cn.org/article/tips/2005/103.html</guid>
  </item>

		<item>
			<author>阿捷</author>
		<title>同一个页面用多个id有什么影响</title>
	<link>http://www.w3cn.org/article/tips/2005/102.html</link>
	<pubDate>2005-4-8 9:51:20</pubDate>
	<guid>http://www.w3cn.org/article/tips/2005/102.html</guid>
  </item>

		<item>
			<author>x5</author>
		<title>伴随着Web标准发展</title>
	<link>http://www.w3cn.org/article/translate/2004/101.html</link>
	<pubDate>2004-11-24 10:31:24</pubDate>
	<guid>http://www.w3cn.org/article/translate/2004/101.html</guid>
  </item>

		<item>
			<author>allan</author>
		<title>使用DIV之后,什么时候使用TABLE</title>
	<link>http://www.w3cn.org/article/tips/2004/100.html</link>
	<pubDate>2004-11-9 15:47:29</pubDate>
	<guid>http://www.w3cn.org/article/tips/2004/100.html</guid>
  </item>

		<item>
			<author>阿捷</author>
		<title>经典样式表大赛中web标准应用存在的问题</title>
	<link>http://www.w3cn.org/article/tips/2004/99.html</link>
	<pubDate>2004-11-8 11:36:30</pubDate>
	<guid>http://www.w3cn.org/article/tips/2004/99.html</guid>
  </item>

		<item>
			<author>阿捷</author>
		<title>用!important解决IE和Mozilla的布局差别</title>
	<link>http://www.w3cn.org/article/tips/2004/91.html</link>
	<pubDate>2004-7-24 14:56:36</pubDate>
	<guid>http://www.w3cn.org/article/tips/2004/91.html</guid>
  </item>

		<item>
			<author>阿捷</author>
		<title>盒模型bug的解决方法</title>
	<link>http://www.w3cn.org/article/tips/2004/90.html</link>
	<pubDate>2004-7-22 22:37:07</pubDate>
	<guid>http://www.w3cn.org/article/tips/2004/90.html</guid>
  </item>

		<item>
			<author>阿捷</author>
		<title>表格对决CSS--一场生死之战</title>
	<link>http://www.w3cn.org/article/translate/2004/89.html</link>
	<pubDate>2004-7-19 21:00:54</pubDate>
	<guid>http://www.w3cn.org/article/translate/2004/89.html</guid>
  </item>

  </channel>
  </rss>
<?xml version="1.0" encoding="ISO-8859-5"?>
<!--
Source: http://forum.template-toolkit.ru/rss/forum_6.rss
Expect: ISO-8859-5
-->
<rss version="2.0">
	<channel>
		<title>Форумы Template Toolkit : Новости Template Toolkit</title>
		<link>http://forum.template-toolkit.ru/view_forum/forum_id-6.html?rss</link>
		<description>Template Toolkit - быстрая, мощная и расширяемая система обработки шаблонов. На сайте работает форум, в котором обсуждаются вопросы использования библиотеки по работе с шаблонами. Анонсы и новости, связанные с Template Toolkit.</description>
		<language>ru</language>
		<copyright><PERSON><PERSON><PERSON></copyright>
		<webMaster><PERSON><PERSON><PERSON> &lt;<EMAIL>&gt;</webMaster>
		<pubDate>Mon, 26 Dec 2005 16:28:41 GMT</pubDate>
		<lastBuildDate>Wed,  4 Jan 2006 02:29:01 GMT</lastBuildDate>
		<managingEditor>Perl script, Template Toolkit</managingEditor>
		<image>
			<url>http://forum.template-toolkit.ru/images/tt2powered.gif</url>
			<title>Template Toolkit - шаблоны в perl</title>
			<link>http://forum.template-toolkit.ru/</link>
			<width>88</width>
			<height>31</height>
		</image>
		<docs>http://blogs.law.harvard.edu/tech/rss</docs>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-122.html?rss</guid>
			<pubDate>Mon, 19 Dec 2005 14:49:49 GMT</pubDate>
			<title>Template::Provider::FromDATA - загрузка шаблонов из секции __DATA__</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-122.html?rss</link>
			<description>На CPAN опубликована версия 0.04 &lt;a target="_blank" href="http://search.cpan.org/~bricas/Template-Provider-FromDATA/"&gt;модуля Template::Provider::FromDATA&lt;/a&gt;, который позволяет хранить шаблоны вместе с кодом в одном файле (секция __DATA__).&lt;br /&gt;&lt;br /&gt;Для использования данного модуля необходимо вначале создать экземпляр провайдера Template::Provider::FromDATA, а затем указать его в качестве поставщика шаблонов в конструкторе Template:&lt;br /&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;use Template;
use Template::Provider::FromDATA;
    
# Создаём объект поставщика
my $provider = Template::Provider::FromDATA-&amp;gt;new( {
    CLASSES =&amp;gt; __PACKAGE__
} );
    
# Указываем объект поставщика в опция конструктора Template
my $template = Template-&amp;gt;new( {
    # ...
    LOAD_TEMPLATES =&amp;gt; [ $provider ]
} );

# ...сами шаблоны в секции __DATA__
    
__DATA__
    
__mytemplate__
Foo [% bar %]
    
__myothertemplate__
Baz, [% qux %]?&lt;/pre&gt;&lt;/div&gt;
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-122.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-122.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-121.html?rss</guid>
			<pubDate>Mon, 19 Dec 2005 14:10:59 GMT</pubDate>
			<title>Template::Plugin::HTML::Strip - удаление HTML-тегов из текста</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-121.html?rss</link>
			<description>На CPAN опубликована версия 0.01 модуля &lt;a target="_blank" href="http://search.cpan.org/~gsimmons/Template-Plugin-HTML-Strip/"&gt;модуля Template::Plugin::HTML::Strip&lt;/a&gt;. Модуль предоставляет плагин, который позволяет удалять из текста HTML-теги.&lt;br /&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;[% USE HTML.Strip %]

[% FILTER html_strip %]
&amp;lt;title&amp;gt;People for the Preservation of Presentational Markup&amp;lt;/title&amp;gt;
&amp;lt;h1&amp;gt;HTML::Strip - A cause for concern?&amp;lt;/h1&amp;gt;
[% END %]

[% USE HTML.Strip 'strip'
    striptags   = [ 'script' 'iframe' ]
    emit_spaces = 0
%]

[% FILTER strip %]
&amp;lt;p&amp;gt;A call to arms against the removal of our elements!&amp;lt;/p&amp;gt;
[% END %]&lt;/pre&gt;&lt;/div&gt;
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-121.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-121.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-115.html?rss</guid>
			<pubDate>Sun,  6 Nov 2005 08:46:17 GMT</pubDate>
			<title>Template::Plugin::Subst - замена текста с помощью регулярных выражений</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-115.html?rss</link>
			<description>На CPAN опубликован &lt;a target="_blank" href="http://search.cpan.org/~nikc/Template-Plugin-Subst/"&gt;модуль Template::Plugin::Subst&lt;/a&gt;, который позволяет выполнять замены в тексте с использованием регулярных выражений.&lt;br /&gt;&lt;br /&gt;Использование виртуального метода &lt;span style="font-weight:bold"&gt;subst&lt;/span&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;[% USE Subst %]
[% str = 'шило на мыло' %]
[% str.subst('(шило|мыло) на (мыло|шило)', '$2 на $1', 1) %]&lt;/pre&gt;&lt;/div&gt;&lt;br /&gt;&lt;br /&gt;Использование фильтра&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;[% USE filt = Subst
    pattern = '(шило|мыло) на (мыло|шило)'
    replacement = '$2 на $1'
    global = 1 %]
[% FILTER $filt %]
шило на мыло
[% END %]&lt;/pre&gt;&lt;/div&gt;
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-115.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-115.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-111.html?rss</guid>
			<pubDate>Sat,  1 Oct 2005 06:52:16 GMT</pubDate>
			<title>Template::Plugin::ASCIITable - вывод текстовых таблиц</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-111.html?rss</link>
			<description>На CPAN опубликована версия 0.1 &lt;a target="_blank" href="http://search.cpan.org/~dakkar/Template-Plugin-ASCIITable/"&gt;модуля Template::Plugin::ASCIITable&lt;/a&gt;, который позволяет рисовать текстовые таблицы.&lt;br /&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;[%
  USE ASCIITable;
  ASCIITable.cols('a', 'b', 'c');
  ASCIITable.rows([1,2,3],['one','two','three']);
  ASCIITable.draw()
%]&lt;/pre&gt;&lt;/div&gt;&lt;br /&gt;Вывод:&lt;br /&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;
.=----+-----+------=.
|  a  |  b  |   c   |
|=----+-----+------=|
|  1  |  2  |   3   |
| one | two | three |
'=----+-----+------='&lt;/pre&gt;&lt;/div&gt;&lt;br /&gt;&lt;br /&gt;Плагин позволяет настраивать параметры таблицы, а также создавать собственные стили рамок.
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-111.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-111.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-102.html?rss</guid>
			<pubDate>Thu, 21 Jul 2005 05:47:32 GMT</pubDate>
			<title>Template::Plugin::Perl - экспорт ряда функций Perl в шаблоны</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-102.html?rss</link>
			<description>На CPAN опубликован &lt;a target="_blank" href="http://search.cpan.org/~agent/Template-Plugin-Perl/"&gt;модуль Template::Plugin::Perl&lt;/a&gt;, предоставляющий ряд полезных функций Perl.&lt;br /&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;[% USE Perl %]
[% Perl.log(100) %]
[% Perl.rand(1) %]
[% Perl.exp(2) %]
[% Perl.sprintf(&amp;quot;%.0f&amp;quot;, 3.5) %]
[% Perl.pow(2, 3) %]   # 2 ** 3;
[% Perl.eval('2**3') %]
[% Perl.sin(3.14) %]
[% Perl.cos(0) %]
[% Perl.join(',', 'a', 'b', 'c') %]
[% list = ['a','b','c'];
   Perl.join(',' list) %]&lt;/pre&gt;&lt;/div&gt;
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-102.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-102.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-101.html?rss</guid>
			<pubDate>Thu, 21 Jul 2005 04:37:32 GMT</pubDate>
			<title>Template::Plugin::POSIX - POSIX функции в шаблонах</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-101.html?rss</link>
			<description>На CPAN опубликован &lt;a target="_blank" href="http://search.cpan.org/~agent/Template-Plugin-POSIX/"&gt;модуль Template::Plugin::POSIX&lt;/a&gt;. Модуль позволяет использовать в шаблонах функции POSIX.&lt;br /&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;[% USE POSIX %]
[% POSIX.log(100) %]
[% POSIX.rand(1) %]
[% POSIX.exp(2) %]
[% POSIX.sprintf(&amp;quot;%.0f&amp;quot;, 3.5) %]
[% POSIX.pow(2, 3) %]
[% POSIX.ceil(3.8) %]
[% POSIX.floor(3.8) %]
[% POSIX.sin(3.14) %]
[% POSIX.cos(0) %]&lt;/pre&gt;&lt;/div&gt;
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-101.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-101.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-96.html?rss</guid>
			<pubDate>Mon, 11 Jul 2005 04:05:37 GMT</pubDate>
			<title>Template::Plugin::Decode - преобразование вывода в Unicode</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-96.html?rss</link>
			<description>На CPAN опубликован &lt;a target="_blank" href="http://search.cpan.org/~lyokato/Template-Plugin-Decode/"&gt;модуль Template::Plugin::Decode&lt;/a&gt;, который предоставляет способ преодолеть сложности с использованием в шаблонах UTF-8.&lt;br /&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;Вначале [% USE Decode %].
Затем можно использовать фильтр 'decode'.
Этот фильтр преобразует строку из UTF-8 во внутренний unicode-формат Perl:
[% multibyte_str | decode %]&lt;/pre&gt;&lt;/div&gt;&lt;br /&gt;Автор модуля предлагает следующий подход. В качестве кодировки шаблонов используется UTF-8. Файлы должны содержать BOM-запись. Все входные данные декодируются фильтром decode.
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-96.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-96.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-94.html?rss</guid>
			<pubDate>Sat,  2 Jul 2005 06:41:54 GMT</pubDate>
			<title>Template::Plugin::XSLT - XSLT-преобразование XML</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-94.html?rss</link>
			<description>На CPAN опубликована версия 1.2 &lt;a target="_blank" href="http://search.cpan.org/~scott/Template-Plugin-XSLT/"&gt;модуля Template::Plugin::XSLT&lt;/a&gt;, в котором реализован фильтр, позволяющий производить XSLT-преобразования фрагмента XML.&lt;br /&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;[% USE transform = XSLT(&amp;quot;stylesheet.xsl&amp;quot;); %]
    ...
[% foo.as_xml | $transform foo = '&amp;quot;bar&amp;quot;' baz = 123 %]&lt;/pre&gt;&lt;/div&gt;
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-94.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-94.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-88.html?rss</guid>
			<pubDate>Tue, 24 May 2005 11:28:04 GMT</pubDate>
			<title>Template::Plugin::NakedBody - выделение BODY-составляющей HTML-документа</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-88.html?rss</link>
			<description>На CPAN опубликована версия 0.02 &lt;a target="_blank" href="http://search.cpan.org/~adamk/Template-Plugin-NakedBody/"&gt;модуля Template::Plugin::NakedBody&lt;/a&gt;. Данный модуль реализует фильтр, который выделяет содержимое HTML-документа между тегами &amp;lt;BODY&amp;gt; и &amp;lt;/BODY&amp;gt;. Как написано в документации это оказывается удобно при работе с дизайнерами. При включении подготовленных ими документов нет необходимости дополнительно редактировать документ.&lt;br /&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;# included.html
# В html-редакторе удобно работать с полным документом
&amp;lt;html&amp;gt;
&amp;lt;head&amp;gt;
&amp;lt;style ...&amp;gt;
&amp;lt;/head&amp;gt;
&amp;lt;body&amp;gt;
Чтобы видеть правильно этот контент нужны таблицы стилей.
&amp;lt;/body&amp;gt;
&amp;lt;/html&amp;gt;
  
# mypage.html
[% USE NakedBody %]
&amp;lt;html&amp;gt;
&amp;lt;head&amp;gt;
&amp;lt;style ...&amp;gt;
&amp;lt;/head&amp;gt;
&amp;lt;body&amp;gt;
Контент исходного документа.
Затем включается документ, подготовленный дизайнером.
[% INCLUDE included.html | NakedBody %]
Продолжение исходного документа.
&amp;lt;/body&amp;gt;
&amp;lt;/html&amp;gt;&lt;/pre&gt;&lt;/div&gt;
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-88.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-88.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-87.html?rss</guid>
			<pubDate>Sat, 21 May 2005 08:04:46 GMT</pubDate>
			<title>CGI::Application::Plugin::AnyTemplate - интерфейс к основным системам шаблонов</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-87.html?rss</link>
			<description>На CPAN опубликована &lt;a target="_blank" href="http://backpan.cpan.org/authors/id/M/MG/MGRAHAM/CGI-Application-Plugin-AnyTemplate-0.04.tar.gz"&gt;версия 0.04 модуля CGI::Application::Plugin::AnyTemplate&lt;/a&gt;. Данный модуль содержит обобщенный интерфейс для работы с любой известной системой шаблонов Perl (в настоящий момент поддерживаются &lt;span style="font-weight:bold"&gt;HTML::Template&lt;/span&gt;, &lt;span style="font-weight:bold"&gt;HTML::Template::Expr&lt;/span&gt;, &lt;span style="font-weight:bold"&gt;Petal&lt;/span&gt; и &lt;span style="font-weight:bold"&gt;Template Toolkit&lt;/span&gt;) из приложений, использующих в качестве контроллера CGI::Application.
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-87.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-87.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-84.html?rss</guid>
			<pubDate>Sun, 15 May 2005 14:37:34 GMT</pubDate>
			<title>Inline::TT - включение шаблонов в скрипт</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-84.html?rss</link>
			<description>На CPAN опубликована версия 0.04 &lt;a target="_blank" href="http://search.cpan.org/~philcrow/Inline-TT/"&gt;модуля Inline::TT&lt;/a&gt;, который позволяет включать шаблоны в скрипт с сохранением возможности кеширования откомпилированных шаблонов.&lt;br /&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;
use Inline TT =&amp;gt; 'DATA';

# имена блоков экспортируются как функции
print hello( { name =&amp;gt; 'Rob' } ), &amp;quot;\n&amp;quot;;
print goodbye( { name =&amp;gt; 'Rob' } ), &amp;quot;\n&amp;quot;;

__DATA__
__TT__
[% BLOCK hello %]
&amp;lt;H1&amp;gt; Hello [% name %], how are you? &amp;lt;/H1&amp;gt;
[% END %]
[% BLOCK goodbye %]
&amp;lt;H1&amp;gt; Goodbye [% name %], have a nice day. &amp;lt;/H1&amp;gt;
[% END %]&lt;/pre&gt;&lt;/div&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;use Inline TT =&amp;gt; &amp;lt;&amp;lt; EO_TEMPLATE
[% BLOCK hello %]
&amp;lt;H1&amp;gt; Hello [% name %], how are you? &amp;lt;/H1&amp;gt;
[% END %]
[% BLOCK goodbye %]
&amp;lt;H1&amp;gt; Goodbye [% name %], have a nice day. &amp;lt;/H1&amp;gt;
[% END %]
EO_TEMPLATE

print hello( { name =&amp;gt; 'Rob' } ), &amp;quot;\n&amp;quot;;
print goodbye( { name =&amp;gt; 'Rob' } ), &amp;quot;\n&amp;quot;;&lt;/pre&gt;&lt;/div&gt;
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-84.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-84.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-81.html?rss</guid>
			<pubDate>Sat,  7 May 2005 12:41:26 GMT</pubDate>
			<title>Template::Plugin::HTML::Prototype - генерация Ajax-кода в шаблонах</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-81.html?rss</link>
			<description>На CPAN опубликована &lt;a target="_blank" href="http://backpan.cpan.org/authors/id/B/BA/BAUERB/Template-Plugin-HTML-Prototype-0.01.tar.gz"&gt;версия 0.01 модуля Template::Plugin::HTML::Prototype&lt;/a&gt;. Данный модуль предоставляет интерфейс к модулю HTML::Prototype, который содержит функции для генерации кода &lt;a target="_blank" href="http://prototype.conio.net/"&gt;Javascript-библиотеки Prototype&lt;/a&gt;. Prototype используется в частности в &lt;span style="font-weight:bold"&gt;Ruby on Rails&lt;/span&gt; и содержит объектно-ориентированную реализацию &lt;span style="font-weight:bold"&gt;Ajax&lt;/span&gt;.&lt;br /&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;    [% USE proto = HTML::Prototype %]

    [% proto.define_javascript_functions %]
    [% proto.form_remote_tag(...) %]
    [% proto.link_to_function(...) %]
    [% proto.link_to_remote(...) %]
    [% proto.observe_field(...) %]
    [% proto.observe_form(...) %]
    [% proto.periodically_call_remote(...) %]
    [% proto.submit_to_remote(...) %]&lt;/pre&gt;&lt;/div&gt;
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-81.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-81.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-75.html?rss</guid>
			<pubDate>Tue, 12 Apr 2005 12:23:30 GMT</pubDate>
			<title>Template::Multilingual - поддержка нескольких языков в шаблонах</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-75.html?rss</link>
			<description>На CPAN опубликована &lt;a target="_blank" href="http://backpan.cpan.org/authors/id/C/CH/CHOLET/Template-Multilingual-0.02.tar.gz"&gt;версия 0.02 модуля Template::Multilingual&lt;/a&gt;. Этот модуль является производным от Template и предлагает реализацию поддержки нескольких языков в шаблонах.&lt;br /&gt;&lt;br /&gt;Код:&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;use Template::Multilingual;
my $template = Template::Multilingual-&amp;gt;new();
$template-&amp;gt;language('en');
$template-&amp;gt;process('example.ttml');&lt;/pre&gt;&lt;/div&gt;&lt;br /&gt;Шаблон:&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;
&amp;lt;t&amp;gt;
  &amp;lt;en&amp;gt;Hello!&amp;lt;/en&amp;gt;
  &amp;lt;fr&amp;gt;Bonjour!&amp;lt;/fr&amp;gt;
  &amp;lt;ru&amp;gt;Привет!&amp;lt;/ru&amp;gt;
&amp;lt;/t&amp;gt;&lt;/pre&gt;&lt;/div&gt;
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-75.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-75.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-74.html?rss</guid>
			<pubDate>Mon,  4 Apr 2005 06:03:55 GMT</pubDate>
			<title>Template::Plugin::TagRescue - плагин для выделения тегов html с исключением</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-74.html?rss</link>
			<description>Опубликована &lt;a target="_blank" href="http://backpan.cpan.org/authors/id/T/TA/TANIMOTO/Template-Plugin-TagRescue-0.06.tar.gz"&gt;версия 0.06 модуля Template::Plugin::TagRescue&lt;/a&gt;. Модуль выделяет  при выводе (т.е. заменяет &amp;lt; &amp;gt; &amp;amp; &amp;quot; на соответсвующие entities) теги html за исключением указанных.&lt;br /&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;[% USE TagRescue %]

[% FILTER html_except_for('b') -%]
&amp;lt;B&amp;gt;Жирный!&amp;lt;/B&amp;gt; and &amp;lt;I&amp;gt;Курсив!&amp;lt;/I&amp;gt;&amp;lt;BR&amp;gt;
[%- END %]

# Вывод:
# &amp;lt;B&amp;gt;Жирный!&amp;lt;/B&amp;gt; and &amp;amp;lt;I&amp;amp;gt;Italic!&amp;amp;lt;/I&amp;amp;gt;&amp;amp;lt;BR&amp;amp;gt;

[% '&amp;lt;B&amp;gt;Жирный!&amp;lt;/B&amp;gt; and &amp;lt;I&amp;gt;Курсив!&amp;lt;/I&amp;gt;&amp;lt;BR&amp;gt;' | html_except_for('i','br') %]

# Вывод:
# &amp;amp;lt;B&amp;amp;gt;Bold!&amp;amp;lt;/B&amp;amp;gt; and &amp;lt;I&amp;gt;Italic!&amp;lt;/I&amp;gt;&amp;lt;BR&amp;gt;

[% taglist = ['b', 'br']; '&amp;lt;B&amp;gt;Жирный!&amp;lt;/B&amp;gt; и &amp;lt;I&amp;gt;Курсив!&amp;lt;/I&amp;gt;&amp;lt;BR&amp;gt;' | html_except_for(taglist) %]

# Вывод:
# &amp;lt;B&amp;gt;Жирный!&amp;lt;/B&amp;gt; и &amp;amp;lt;I&amp;amp;gt;Курсив!&amp;amp;lt;/I&amp;amp;gt;&amp;lt;BR&amp;gt;&lt;/pre&gt;&lt;/div&gt;
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-74.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-74.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-72.html?rss</guid>
			<pubDate>Tue, 29 Mar 2005 12:46:04 GMT</pubDate>
			<title>Template::Plugin::HTML::BBCode - преобразование BBCode в HTML</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-72.html?rss</link>
			<description>На CPAN опубликована &lt;a target="_blank" href="http://backpan.cpan.org/authors/id/L/LI/LIOL/Template-Plugin-HTML-BBCode-0.01.tar.gz"&gt;версия 0.01 модуля Template::Plugin::HTML::BBCode&lt;/a&gt;. Модуль является оберткой вокруг модуля HTML::BBCode и предоставляет фильтр bbcode, который заменяет теги разметки BBCode на соответсвтующие теги HTML.&lt;br /&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;[%- USE HTML::BBCode -%]
[% FILTER bbcode %]
&amp;#91;b]BBCode&amp;#91;/b] - is simple &amp;#91;i]markup language&amp;#91;/i] used in
&amp;#91;url=http://www.phpbb.com/]phpBB&amp;#91;/url].
[% END %]

[%- USE HTML::BBCode( 'bbcode_limited'
  allowed_tags = [ 'b', 'i', 'u' ]
) -%]
[% FILTER bbcode_limited %]
&amp;#91;b]BBCode&amp;#91;/b] - is simple &amp;#91;i]markup language&amp;#91;/i] used in
&amp;#91;url=http://www.phpbb.com/]phpBB&amp;#91;/url].
[% END %]&lt;/pre&gt;&lt;/div&gt;
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-72.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-72.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-71.html?rss</guid>
			<pubDate>Sun, 27 Mar 2005 11:07:13 GMT</pubDate>
			<title>Template::Plugin::DBM::Deep - интерфейс к pure perl DBM</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-71.html?rss</link>
			<description>На CPAN опубликована &lt;a target="_blank" href="http://backpan.cpan.org/authors/id/M/ME/MERLYN/Template-Plugin-DBM-Deep-0.02.tar.gz"&gt;версия 0.02 модуля Template::Plugin::DBM::Deep&lt;/a&gt;. Модуль предоставляет интерфейс к модулю DBM::Deep.&lt;br /&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;
  [% USE db = DBM.Deep(file = &amp;quot;my.db&amp;quot; locking = 1 autoflush = 1);
     db.lock;
     db.flintstones = { &amp;quot;flintstone&amp;quot; = [&amp;quot;fred&amp;quot; &amp;quot;wilma&amp;quot;]
                        &amp;quot;rubble&amp;quot; = [&amp;quot;barney&amp;quot; &amp;quot;betty&amp;quot;] };
     db.castaways = [&amp;quot;gilligan&amp;quot; &amp;quot;skipper&amp;quot; &amp;quot;professor&amp;quot; &amp;quot;and the rest&amp;quot; ];
     db.unlock;
  -%]
  ...
  [% db.flintstones.rubble.0; %] -- barney
  [% db.castaways.3; %] -- and the rest
&lt;/pre&gt;&lt;/div&gt;
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-71.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-71.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-68.html?rss</guid>
			<pubDate>Mon, 14 Mar 2005 12:35:02 GMT</pubDate>
			<title>ExtUtils::ModuleMaker::TT - подготовка модуля к дистрибуции из TT-шаблонов</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-68.html?rss</link>
			<description>На CPAN опубликована &lt;a target="_blank" href="http://backpan.cpan.org/authors/id/D/DA/DAGOLDEN/ExtUtils-ModuleMaker-TT-0.74.tar.gz"&gt;версия 0.74 модуля ExtUtils::ModuleMaker::TT&lt;/a&gt;. Модуль позволяет строить стандартные файлы, входящие в состав дистрибутива модуля (README, Changes, Todo, Build.PL, Makefile.PL, Proxy_Makefile.PL, MANIFEST.SKIP, test.t, module.pm), из шаблонов TT.
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-68.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-68.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-67.html?rss</guid>
			<pubDate>Mon, 14 Mar 2005 12:21:55 GMT</pubDate>
			<title>Template::Plugin::String::Compare - сравнение строк</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-67.html?rss</link>
			<description>На CPAN опубликована &lt;a target="_blank" href="http://backpan.cpan.org/authors/id/T/TA/TANIMOTO/Template-Plugin-String-Compare-0.01.tar.gz"&gt;версия 0.01 плагина Template::Plugin::String::Compare&lt;/a&gt;, который позволяет сравнивать строки в строковом контексте.&lt;br /&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;
[% IF '2005-03-01' &amp;lt; '2005-04-01' %]
Такое сравнение проводится в числовом контексте.
[% END %]

[% USE String.Compare %]
[% IF String.Compare.new('2005-03-01') &amp;lt; '2005-04-01' %]
Это сравнение происходит в строковом контексте.
[% END %]
&lt;/pre&gt;&lt;/div&gt;
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-67.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-67.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-63.html?rss</guid>
			<pubDate>Sat, 19 Feb 2005 08:23:15 GMT</pubDate>
			<title>Среда для разработки eCommerece приложений Handel</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-63.html?rss</link>
			<description>На CPAN опубликован &lt;a target="_blank" href="http://backpan.cpan.org/authors/id/C/CL/CLACO/Handel-0.04.tar.gz"&gt;модуль Handel (версия 0.04)&lt;/a&gt;. Этот модуль предоставляет среду для разработки eCommerece приложений с поддержкой AxKit и Template Toolkit.
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-63.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-63.html?rss</source>
		</item>
		<item>
			<guid isPermaLink="true">http://forum.template-toolkit.ru/view_topic/topic_id-62.html?rss</guid>
			<pubDate>Thu, 17 Feb 2005 07:27:20 GMT</pubDate>
			<title>Template::Plugin::SSI - использование SSI в шаблонах</title>
			<link>http://forum.template-toolkit.ru/view_topic/topic_id-62.html?rss</link>
			<description>На CPAN опубликована &lt;a target="_blank" href="http://backpan.cpan.org/authors/id/C/CO/COREY/Template-Plugin-SSI-0.11.tar.gz"&gt;версия 0.11 Template::Plugin::SSI&lt;/a&gt;. Модуль является оберткой вокруг CGI::SSI и позволяет использовать в шаблонах SSI-директивы.&lt;br /&gt;&lt;br /&gt;&lt;div class="code"&gt;&lt;pre&gt;[% USE SSI %]

# включение (virtual) файла /foo/bar.inc.html
[% SSI.include('virtual', '/foo/bar.inc.html') %]

# включение файла /foo/bar.inc.html
[% SSI.include('file', '/var/www/html/foo/bar.inc.html') %]

# выполнить команду
[% SSI.exec('cmd', 'ls') %]

# выполнить cgi скрипт
[% SSI.exec('cgi', '/cgi-bin/foo.cgi') %]

# установить переменную конфигурации ('sizefmt', 'timefmt', или 'errmsg')
[% SSI.config('timefmt', &amp;quot;%Y&amp;quot;) %]

# вывести набор переменную окружения
[% SSI.echo('DATE_LOCAL') %]

# установить локальную переменную ($name = 'Corey')
[% SSI.set('name', 'Corey') %]

# вывести дату последней модификации 'index.html'
[% SSI.flastmod('file', 'index.html') %]

# вывести размер файла 'index.html'
[% SSI.fsize('file', 'index.html') %]&lt;/pre&gt;&lt;/div&gt;
			</description>
			<author>Лобанов Игорь &lt;<EMAIL>&gt;</author>
			<comments>http://forum.template-toolkit.ru/view_topic/topic_id-62.html</comments>
			<category>Template Toolkit - библиотека для работы с шаблонами в Perl</category>
			<source url="http://forum.template-toolkit.ru/rss/forum_id-6.rss">http://forum.template-toolkit.ru/view_topic/topic_id-62.html?rss</source>
		</item>
	</channel>
</rss>

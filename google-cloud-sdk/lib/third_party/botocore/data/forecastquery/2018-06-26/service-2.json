{"version": "2.0", "metadata": {"apiVersion": "2018-06-26", "endpointPrefix": "forecastquery", "jsonVersion": "1.1", "protocol": "json", "serviceFullName": "Amazon Forecast Query Service", "serviceId": "forecastquery", "signatureVersion": "v4", "signingName": "forecast", "targetPrefix": "AmazonForecastRuntime", "uid": "forecastquery-2018-06-26"}, "operations": {"QueryForecast": {"name": "QueryForecast", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "QueryForecastRequest"}, "output": {"shape": "QueryForecastResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidInputException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidNextTokenException"}], "documentation": "<p>Retrieves a forecast for a single item, filtered by the supplied criteria.</p> <p>The criteria is a key-value pair. The key is either <code>item_id</code> (or the equivalent non-timestamp, non-target field) from the <code>TARGET_TIME_SERIES</code> dataset, or one of the forecast dimensions specified as part of the <code>FeaturizationConfig</code> object.</p> <p>By default, <code>QueryForecast</code> returns the complete date range for the filtered forecast. You can request a specific date range.</p> <p>To get the full forecast, use the <a href=\"https://docs.aws.amazon.com/en_us/forecast/latest/dg/API_CreateForecastExportJob.html\">CreateForecastExportJob</a> operation.</p> <note> <p>The forecasts generated by Amazon Forecast are in the same timezone as the dataset that was used to create the predictor.</p> </note>"}}, "shapes": {"Arn": {"type": "string", "max": 256, "pattern": "arn:([a-z\\d-]+):forecast:.*:.*:.+"}, "AttributeName": {"type": "string", "max": 256, "pattern": "^[a-zA-Z0-9\\_\\-]+$"}, "AttributeValue": {"type": "string", "max": 256}, "DataPoint": {"type": "structure", "members": {"Timestamp": {"shape": "Timestamp", "documentation": "<p>The timestamp of the specific forecast.</p>"}, "Value": {"shape": "Double", "documentation": "<p>The forecast value.</p>"}}, "documentation": "<p>The forecast value for a specific date. Part of the <a>Forecast</a> object.</p>"}, "DateTime": {"type": "string"}, "Double": {"type": "double"}, "ErrorMessage": {"type": "string"}, "Filters": {"type": "map", "key": {"shape": "AttributeName"}, "value": {"shape": "AttributeValue"}, "max": 50, "min": 1}, "Forecast": {"type": "structure", "members": {"Predictions": {"shape": "Predictions", "documentation": "<p>The forecast.</p> <p>The <i>string</i> of the string-to-array map is one of the following values:</p> <ul> <li> <p>p10</p> </li> <li> <p>p50</p> </li> <li> <p>p90</p> </li> </ul>"}}, "documentation": "<p>Provides information about a forecast. Returned as part of the <a>QueryForecast</a> response.</p>"}, "InvalidInputException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The value is invalid or is too long.</p>", "exception": true}, "InvalidNextTokenException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The token is not valid. Tokens expire after 24 hours.</p>", "exception": true}, "LimitExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The limit on the number of requests per second has been exceeded.</p>", "exception": true}, "NextToken": {"type": "string", "max": 3000, "min": 1}, "Predictions": {"type": "map", "key": {"shape": "Statistic"}, "value": {"shape": "TimeSeries"}}, "QueryForecastRequest": {"type": "structure", "required": ["ForecastArn", "Filters"], "members": {"ForecastArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the forecast to query.</p>"}, "StartDate": {"shape": "DateTime", "documentation": "<p>The start date for the forecast. Specify the date using this format: yyyy-MM-dd'T'HH:mm:ss (ISO 8601 format). For example, 2015-01-01T08:00:00.</p>"}, "EndDate": {"shape": "DateTime", "documentation": "<p>The end date for the forecast. Specify the date using this format: yyyy-MM-dd'T'HH:mm:ss (ISO 8601 format). For example, 2015-01-01T20:00:00. </p>"}, "Filters": {"shape": "Filters", "documentation": "<p>The filtering criteria to apply when retrieving the forecast. For example, to get the forecast for <code>client_21</code> in the electricity usage dataset, specify the following:</p> <p> <code>{\"item_id\" : \"client_21\"}</code> </p> <p>To get the full forecast, use the <a href=\"https://docs.aws.amazon.com/en_us/forecast/latest/dg/API_CreateForecastExportJob.html\">CreateForecastExportJob</a> operation.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>If the result of the previous request was truncated, the response includes a <code>NextToken</code>. To retrieve the next set of results, use the token in the next request. Tokens expire after 24 hours.</p>"}}}, "QueryForecastResponse": {"type": "structure", "members": {"Forecast": {"shape": "Forecast", "documentation": "<p>The forecast.</p>"}}}, "ResourceInUseException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified resource is in use.</p>", "exception": true}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>We can't find that resource. Check the information that you've provided and try again.</p>", "exception": true}, "Statistic": {"type": "string", "max": 4}, "TimeSeries": {"type": "list", "member": {"shape": "DataPoint"}}, "Timestamp": {"type": "string"}}, "documentation": "<p>Provides APIs for creating and managing Amazon Forecast resources.</p>"}
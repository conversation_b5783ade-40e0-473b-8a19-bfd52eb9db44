{"version": "2.0", "metadata": {"apiVersion": "2019-11-01", "endpointPrefix": "access-analyzer", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Access Analyzer", "serviceId": "AccessAnalyzer", "signatureVersion": "v4", "signingName": "access-analyzer", "uid": "accessanalyzer-2019-11-01"}, "operations": {"CreateAnalyzer": {"name": "CreateAnalyzer", "http": {"method": "PUT", "requestUri": "/analyzer", "responseCode": 200}, "input": {"shape": "CreateAnalyzerRequest"}, "output": {"shape": "CreateAnalyzerResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates an analyzer for your account.</p>", "idempotent": true}, "CreateArchiveRule": {"name": "CreateArchiveRule", "http": {"method": "PUT", "requestUri": "/analyzer/{analyzerName}/archive-rule", "responseCode": 200}, "input": {"shape": "CreateArchiveRuleRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates an archive rule for the specified analyzer. Archive rules automatically archive findings that meet the criteria you define when you create the rule.</p>", "idempotent": true}, "DeleteAnalyzer": {"name": "DeleteAnalyzer", "http": {"method": "DELETE", "requestUri": "/analyzer/{analyzerName}", "responseCode": 200}, "input": {"shape": "DeleteAnalyzerRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes the specified analyzer. When you delete an analyzer, Access Analyzer is disabled for the account in the current or specific Region. All findings that were generated by the analyzer are deleted. You cannot undo this action.</p>", "idempotent": true}, "DeleteArchiveRule": {"name": "DeleteArchiveRule", "http": {"method": "DELETE", "requestUri": "/analyzer/{analyzerName}/archive-rule/{ruleName}", "responseCode": 200}, "input": {"shape": "DeleteArchiveRuleRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes the specified archive rule.</p>", "idempotent": true}, "GetAnalyzedResource": {"name": "GetAnalyzedResource", "http": {"method": "GET", "requestUri": "/analyzed-resource", "responseCode": 200}, "input": {"shape": "GetAnalyzedResourceRequest"}, "output": {"shape": "GetAnalyzedResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves information about a resource that was analyzed.</p>"}, "GetAnalyzer": {"name": "GetAnalyzer", "http": {"method": "GET", "requestUri": "/analyzer/{analyzerName}", "responseCode": 200}, "input": {"shape": "GetAnalyzerRequest"}, "output": {"shape": "GetAnalyzerResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves information about the specified analyzer.</p>"}, "GetArchiveRule": {"name": "GetArchiveRule", "http": {"method": "GET", "requestUri": "/analyzer/{analyzerName}/archive-rule/{ruleName}", "responseCode": 200}, "input": {"shape": "GetArchiveRuleRequest"}, "output": {"shape": "GetArchiveRuleResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves information about an archive rule.</p>"}, "GetFinding": {"name": "GetFinding", "http": {"method": "GET", "requestUri": "/finding/{id}", "responseCode": 200}, "input": {"shape": "GetFindingRequest"}, "output": {"shape": "GetFindingResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves information about the specified finding.</p>"}, "ListAnalyzedResources": {"name": "ListAnalyzedResources", "http": {"method": "POST", "requestUri": "/analyzed-resource", "responseCode": 200}, "input": {"shape": "ListAnalyzedResourcesRequest"}, "output": {"shape": "ListAnalyzedResourcesResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of resources of the specified type that have been analyzed by the specified analyzer..</p>"}, "ListAnalyzers": {"name": "ListAnalyzers", "http": {"method": "GET", "requestUri": "/analyzer", "responseCode": 200}, "input": {"shape": "ListAnalyzersRequest"}, "output": {"shape": "ListAnalyzersResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of analyzers.</p>"}, "ListArchiveRules": {"name": "ListArchiveRules", "http": {"method": "GET", "requestUri": "/analyzer/{analyzerName}/archive-rule", "responseCode": 200}, "input": {"shape": "ListArchiveRulesRequest"}, "output": {"shape": "ListArchiveRulesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of archive rules created for the specified analyzer.</p>"}, "ListFindings": {"name": "ListFindings", "http": {"method": "POST", "requestUri": "/finding", "responseCode": 200}, "input": {"shape": "ListFindingsRequest"}, "output": {"shape": "ListFindingsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of findings generated by the specified analyzer.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a list of tags applied to the specified resource.</p>"}, "StartResourceScan": {"name": "StartResourceScan", "http": {"method": "POST", "requestUri": "/resource/scan", "responseCode": 200}, "input": {"shape": "StartResourceScanRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Immediately starts a scan of the policies applied to the specified resource.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Adds a tag to the specified resource.</p>", "idempotent": true}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Removes a tag from the specified resource.</p>", "idempotent": true}, "UpdateArchiveRule": {"name": "UpdateArchiveRule", "http": {"method": "PUT", "requestUri": "/analyzer/{analyzerName}/archive-rule/{ruleName}", "responseCode": 200}, "input": {"shape": "UpdateArchiveRuleRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates the criteria and values for the specified archive rule.</p>", "idempotent": true}, "UpdateFindings": {"name": "UpdateF<PERSON>ings", "http": {"method": "PUT", "requestUri": "/finding", "responseCode": 200}, "input": {"shape": "UpdateFindingsRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates the status for the specified findings.</p>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "ActionList": {"type": "list", "member": {"shape": "String"}}, "AnalyzedResource": {"type": "structure", "required": ["analyzedAt", "createdAt", "isPublic", "resourceArn", "resourceOwnerAccount", "resourceType", "updatedAt"], "members": {"actions": {"shape": "ActionList", "documentation": "<p>The actions that an external principal is granted permission to use by the policy that generated the finding.</p>"}, "analyzedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the resource was analyzed.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time at which the finding was created.</p>"}, "error": {"shape": "String", "documentation": "<p>An error message.</p>"}, "isPublic": {"shape": "Boolean", "documentation": "<p>Indicates whether the policy that generated the finding grants public access to the resource.</p>"}, "resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource that was analyzed.</p>"}, "resourceOwnerAccount": {"shape": "String", "documentation": "<p>The AWS account ID that owns the resource.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource that was analyzed.</p>"}, "sharedVia": {"shape": "SharedViaList", "documentation": "<p>Indicates how the access that generated the finding is granted. This is populated for Amazon S3 bucket findings.</p>"}, "status": {"shape": "FindingStatus", "documentation": "<p>The current status of the finding generated from the analyzed resource.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the finding was updated.</p>"}}, "documentation": "<p>Contains details about the analyzed resource.</p>"}, "AnalyzedResourceSummary": {"type": "structure", "required": ["resourceArn", "resourceOwnerAccount", "resourceType"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the analyzed resource.</p>"}, "resourceOwnerAccount": {"shape": "String", "documentation": "<p>The AWS account ID that owns the resource.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of resource that was analyzed.</p>"}}, "documentation": "<p>Contains the ARN of the analyzed resource.</p>"}, "AnalyzedResourcesList": {"type": "list", "member": {"shape": "AnalyzedResourceSummary"}}, "AnalyzerArn": {"type": "string", "pattern": "^[^:]*:[^:]*:[^:]*:[^:]*:[^:]*:analyzer/.{1,255}$"}, "AnalyzerStatus": {"type": "string", "enum": ["ACTIVE", "CREATING", "DISABLED", "FAILED"]}, "AnalyzerSummary": {"type": "structure", "required": ["arn", "createdAt", "name", "status", "type"], "members": {"arn": {"shape": "AnalyzerArn", "documentation": "<p>The ARN of the analyzer.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>A timestamp for the time at which the analyzer was created.</p>"}, "lastResourceAnalyzed": {"shape": "String", "documentation": "<p>The resource that was most recently analyzed by the analyzer.</p>"}, "lastResourceAnalyzedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the most recently analyzed resource was analyzed.</p>"}, "name": {"shape": "Name", "documentation": "<p>The name of the analyzer.</p>"}, "status": {"shape": "AnalyzerStatus", "documentation": "<p>The status of the analyzer. An <code>Active</code> analyzer successfully monitors supported resources and generates new findings. The analyzer is <code>Disabled</code> when a user action, such as removing trusted access for IAM Access Analyzer from AWS Organizations, causes the analyzer to stop generating new findings. The status is <code>Creating</code> when the analyzer creation is in progress and <code>Failed</code> when the analyzer creation has failed. </p>"}, "statusReason": {"shape": "StatusReason", "documentation": "<p>The <code>statusReason</code> provides more details about the current status of the analyzer. For example, if the creation for the analyzer fails, a <code>Failed</code> status is displayed. For an analyzer with organization as the type, this failure can be due to an issue with creating the service-linked roles required in the member accounts of the AWS organization.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>The tags added to the analyzer.</p>"}, "type": {"shape": "Type", "documentation": "<p>The type of analyzer, which corresponds to the zone of trust chosen for the analyzer.</p>"}}, "documentation": "<p>Contains information about the analyzer.</p>"}, "AnalyzersList": {"type": "list", "member": {"shape": "AnalyzerSummary"}}, "ArchiveRuleSummary": {"type": "structure", "required": ["createdAt", "filter", "ruleName", "updatedAt"], "members": {"createdAt": {"shape": "Timestamp", "documentation": "<p>The time at which the archive rule was created.</p>"}, "filter": {"shape": "FilterCriteriaMap", "documentation": "<p>A filter used to define the archive rule.</p>"}, "ruleName": {"shape": "Name", "documentation": "<p>The name of the archive rule.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the archive rule was last updated.</p>"}}, "documentation": "<p>Contains information about an archive rule.</p>"}, "ArchiveRulesList": {"type": "list", "member": {"shape": "ArchiveRuleSummary"}}, "Boolean": {"type": "boolean", "box": true}, "ConditionKeyMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "ConflictException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>The ID of the resource.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>The resource type.</p>"}}, "documentation": "<p>A conflict exception error.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "CreateAnalyzerRequest": {"type": "structure", "required": ["analyzerName", "type"], "members": {"analyzerName": {"shape": "Name", "documentation": "<p>The name of the analyzer to create.</p>"}, "archiveRules": {"shape": "InlineArchiveRulesList", "documentation": "<p>Specifies the archive rules to add for the analyzer. Archive rules automatically archive findings that meet the criteria you define for the rule.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>A client token.</p>", "idempotencyToken": true}, "tags": {"shape": "TagsMap", "documentation": "<p>The tags to apply to the analyzer.</p>"}, "type": {"shape": "Type", "documentation": "<p>The type of analyzer to create. Only ACCOUNT analyzers are supported. You can create only one analyzer per account per Region.</p>"}}, "documentation": "<p>Creates an analyzer.</p>"}, "CreateAnalyzerResponse": {"type": "structure", "members": {"arn": {"shape": "AnalyzerArn", "documentation": "<p>The ARN of the analyzer that was created by the request.</p>"}}, "documentation": "<p>The response to the request to create an analyzer.</p>"}, "CreateArchiveRuleRequest": {"type": "structure", "required": ["analyzerName", "filter", "ruleName"], "members": {"analyzerName": {"shape": "Name", "documentation": "<p>The name of the created analyzer.</p>", "location": "uri", "locationName": "analyzerName"}, "clientToken": {"shape": "String", "documentation": "<p>A client token.</p>", "idempotencyToken": true}, "filter": {"shape": "FilterCriteriaMap", "documentation": "<p>The criteria for the rule.</p>"}, "ruleName": {"shape": "Name", "documentation": "<p>The name of the rule to create.</p>"}}, "documentation": "<p>Creates an archive rule.</p>"}, "Criterion": {"type": "structure", "members": {"contains": {"shape": "ValueList", "documentation": "<p>A \"contains\" operator to match for the filter used to create the rule.</p>"}, "eq": {"shape": "ValueList", "documentation": "<p>An \"equals\" operator to match for the filter used to create the rule.</p>"}, "exists": {"shape": "Boolean", "documentation": "<p>An \"exists\" operator to match for the filter used to create the rule. </p>"}, "neq": {"shape": "ValueList", "documentation": "<p>A \"not equals\" operator to match for the filter used to create the rule.</p>"}}, "documentation": "<p>The criteria to use in the filter that defines the archive rule.</p>"}, "DeleteAnalyzerRequest": {"type": "structure", "required": ["analyzerName"], "members": {"analyzerName": {"shape": "Name", "documentation": "<p>The name of the analyzer to delete.</p>", "location": "uri", "locationName": "analyzerName"}, "clientToken": {"shape": "String", "documentation": "<p>A client token.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}}, "documentation": "<p>Deletes an analyzer.</p>"}, "DeleteArchiveRuleRequest": {"type": "structure", "required": ["analyzerName", "ruleName"], "members": {"analyzerName": {"shape": "Name", "documentation": "<p>The name of the analyzer that associated with the archive rule to delete.</p>", "location": "uri", "locationName": "analyzerName"}, "clientToken": {"shape": "String", "documentation": "<p>A client token.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}, "ruleName": {"shape": "Name", "documentation": "<p>The name of the rule to delete.</p>", "location": "uri", "locationName": "ruleName"}}, "documentation": "<p>Deletes an archive rule.</p>"}, "FilterCriteriaMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "Criterion"}}, "Finding": {"type": "structure", "required": ["analyzedAt", "condition", "createdAt", "id", "resourceOwnerAccount", "resourceType", "status", "updatedAt"], "members": {"action": {"shape": "ActionList", "documentation": "<p>The action in the analyzed policy statement that an external principal has permission to use.</p>"}, "analyzedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the resource was analyzed.</p>"}, "condition": {"shape": "ConditionKeyMap", "documentation": "<p>The condition in the analyzed policy statement that resulted in a finding.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time at which the finding was generated.</p>"}, "error": {"shape": "String", "documentation": "<p>An error.</p>"}, "id": {"shape": "FindingId", "documentation": "<p>The ID of the finding.</p>"}, "isPublic": {"shape": "Boolean", "documentation": "<p>Indicates whether the policy that generated the finding allows public access to the resource.</p>"}, "principal": {"shape": "PrincipalMap", "documentation": "<p>The external principal that access to a resource within the zone of trust.</p>"}, "resource": {"shape": "String", "documentation": "<p>The resource that an external principal has access to.</p>"}, "resourceOwnerAccount": {"shape": "String", "documentation": "<p>The AWS account ID that owns the resource.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource reported in the finding.</p>"}, "sources": {"shape": "FindingSourceList", "documentation": "<p>The sources of the finding. This indicates how the access that generated the finding is granted. It is populated for Amazon S3 bucket findings.</p>"}, "status": {"shape": "FindingStatus", "documentation": "<p>The current status of the finding.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the finding was updated.</p>"}}, "documentation": "<p>Contains information about a finding.</p>"}, "FindingId": {"type": "string"}, "FindingIdList": {"type": "list", "member": {"shape": "FindingId"}}, "FindingSource": {"type": "structure", "required": ["type"], "members": {"detail": {"shape": "FindingSourceDetail", "documentation": "<p>Includes details about how the access that generated the finding is granted. This is populated for Amazon S3 bucket findings.</p>"}, "type": {"shape": "FindingSourceType", "documentation": "<p>Indicates the type of access that generated the finding.</p>"}}, "documentation": "<p>The source of the finding. This indicates how the access that generated the finding is granted. It is populated for Amazon S3 bucket findings.</p>"}, "FindingSourceDetail": {"type": "structure", "members": {"accessPointArn": {"shape": "String", "documentation": "<p>The ARN of the access point that generated the finding.</p>"}}, "documentation": "<p>Includes details about how the access that generated the finding is granted. This is populated for Amazon S3 bucket findings.</p>"}, "FindingSourceList": {"type": "list", "member": {"shape": "FindingSource"}}, "FindingSourceType": {"type": "string", "enum": ["BUCKET_ACL", "POLICY", "S3_ACCESS_POINT"]}, "FindingStatus": {"type": "string", "enum": ["ACTIVE", "ARCHIVED", "RESOLVED"]}, "FindingStatusUpdate": {"type": "string", "enum": ["ACTIVE", "ARCHIVED"]}, "FindingSummary": {"type": "structure", "required": ["analyzedAt", "condition", "createdAt", "id", "resourceOwnerAccount", "resourceType", "status", "updatedAt"], "members": {"action": {"shape": "ActionList", "documentation": "<p>The action in the analyzed policy statement that an external principal has permission to use.</p>"}, "analyzedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the resource-based policy that generated the finding was analyzed.</p>"}, "condition": {"shape": "ConditionKeyMap", "documentation": "<p>The condition in the analyzed policy statement that resulted in a finding.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time at which the finding was created.</p>"}, "error": {"shape": "String", "documentation": "<p>The error that resulted in an Error finding.</p>"}, "id": {"shape": "FindingId", "documentation": "<p>The ID of the finding.</p>"}, "isPublic": {"shape": "Boolean", "documentation": "<p>Indicates whether the finding reports a resource that has a policy that allows public access.</p>"}, "principal": {"shape": "PrincipalMap", "documentation": "<p>The external principal that has access to a resource within the zone of trust.</p>"}, "resource": {"shape": "String", "documentation": "<p>The resource that the external principal has access to.</p>"}, "resourceOwnerAccount": {"shape": "String", "documentation": "<p>The AWS account ID that owns the resource.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of the resource that the external principal has access to.</p>"}, "sources": {"shape": "FindingSourceList", "documentation": "<p>The sources of the finding. This indicates how the access that generated the finding is granted. It is populated for Amazon S3 bucket findings.</p>"}, "status": {"shape": "FindingStatus", "documentation": "<p>The status of the finding.</p>"}, "updatedAt": {"shape": "Timestamp", "documentation": "<p>The time at which the finding was most recently updated.</p>"}}, "documentation": "<p>Contains information about a finding.</p>"}, "FindingsList": {"type": "list", "member": {"shape": "FindingSummary"}}, "GetAnalyzedResourceRequest": {"type": "structure", "required": ["analyzerArn", "resourceArn"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The ARN of the analyzer to retrieve information from.</p>", "location": "querystring", "locationName": "analyzerArn"}, "resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource to retrieve information about.</p>", "location": "querystring", "locationName": "resourceArn"}}, "documentation": "<p>Retrieves an analyzed resource.</p>"}, "GetAnalyzedResourceResponse": {"type": "structure", "members": {"resource": {"shape": "AnalyzedResource", "documentation": "<p>An <code>AnalyedResource</code> object that contains information that Access Analyzer found when it analyzed the resource.</p>"}}, "documentation": "<p>The response to the request.</p>"}, "GetAnalyzerRequest": {"type": "structure", "required": ["analyzerName"], "members": {"analyzerName": {"shape": "Name", "documentation": "<p>The name of the analyzer retrieved.</p>", "location": "uri", "locationName": "analyzerName"}}, "documentation": "<p>Retrieves an analyzer.</p>"}, "GetAnalyzerResponse": {"type": "structure", "required": ["analyzer"], "members": {"analyzer": {"shape": "AnalyzerSummary", "documentation": "<p>An <code>AnalyzerSummary</code> object that contains information about the analyzer.</p>"}}, "documentation": "<p>The response to the request.</p>"}, "GetArchiveRuleRequest": {"type": "structure", "required": ["analyzerName", "ruleName"], "members": {"analyzerName": {"shape": "Name", "documentation": "<p>The name of the analyzer to retrieve rules from.</p>", "location": "uri", "locationName": "analyzerName"}, "ruleName": {"shape": "Name", "documentation": "<p>The name of the rule to retrieve.</p>", "location": "uri", "locationName": "ruleName"}}, "documentation": "<p>Retrieves an archive rule.</p>"}, "GetArchiveRuleResponse": {"type": "structure", "required": ["archiveRule"], "members": {"archiveRule": {"shape": "ArchiveRuleSummary"}}, "documentation": "<p>The response to the request.</p>"}, "GetFindingRequest": {"type": "structure", "required": ["analyzerArn", "id"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The ARN of the analyzer that generated the finding.</p>", "location": "querystring", "locationName": "analyzerArn"}, "id": {"shape": "FindingId", "documentation": "<p>The ID of the finding to retrieve.</p>", "location": "uri", "locationName": "id"}}, "documentation": "<p>Retrieves a finding.</p>"}, "GetFindingResponse": {"type": "structure", "members": {"finding": {"shape": "Finding", "documentation": "<p>A <code>finding</code> object that contains finding details.</p>"}}, "documentation": "<p>The response to the request.</p>"}, "InlineArchiveRule": {"type": "structure", "required": ["filter", "ruleName"], "members": {"filter": {"shape": "FilterCriteriaMap", "documentation": "<p>The condition and values for a criterion.</p>"}, "ruleName": {"shape": "Name", "documentation": "<p>The name of the rule.</p>"}}, "documentation": "<p>An criterion statement in an archive rule. Each archive rule may have multiple criteria.</p>"}, "InlineArchiveRulesList": {"type": "list", "member": {"shape": "InlineArchiveRule"}}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "retryAfterSeconds": {"shape": "Integer", "documentation": "<p>The seconds to wait to retry.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>Internal server error.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "ListAnalyzedResourcesRequest": {"type": "structure", "required": ["analyzerArn"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The ARN of the analyzer to retrieve a list of analyzed resources from.</p>"}, "maxResults": {"shape": "Integer", "documentation": "<p>The maximum number of results to return in the response.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of resource.</p>"}}, "documentation": "<p>Retrieves a list of resources that have been analyzed.</p>"}, "ListAnalyzedResourcesResponse": {"type": "structure", "required": ["analyzedResources"], "members": {"analyzedResources": {"shape": "AnalyzedResourcesList", "documentation": "<p>A list of resources that were analyzed.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}}, "documentation": "<p>The response to the request.</p>"}, "ListAnalyzersRequest": {"type": "structure", "members": {"maxResults": {"shape": "Integer", "documentation": "<p>The maximum number of results to return in the response.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>", "location": "querystring", "locationName": "nextToken"}, "type": {"shape": "Type", "documentation": "<p>The type of analyzer.</p>", "location": "querystring", "locationName": "type"}}, "documentation": "<p>Retrieves a list of analyzers.</p>"}, "ListAnalyzersResponse": {"type": "structure", "required": ["analyzers"], "members": {"analyzers": {"shape": "AnalyzersList", "documentation": "<p>The analyzers retrieved.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}}, "documentation": "<p>The response to the request.</p>"}, "ListArchiveRulesRequest": {"type": "structure", "required": ["analyzerName"], "members": {"analyzerName": {"shape": "Name", "documentation": "<p>The name of the analyzer to retrieve rules from.</p>", "location": "uri", "locationName": "analyzerName"}, "maxResults": {"shape": "Integer", "documentation": "<p>The maximum number of results to return in the request.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>", "location": "querystring", "locationName": "nextToken"}}, "documentation": "<p>Retrieves a list of archive rules created for the specified analyzer.</p>"}, "ListArchiveRulesResponse": {"type": "structure", "required": ["archiveRules"], "members": {"archiveRules": {"shape": "ArchiveRulesList", "documentation": "<p>A list of archive rules created for the specified analyzer.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}}, "documentation": "<p>The response to the request.</p>"}, "ListFindingsRequest": {"type": "structure", "required": ["analyzerArn"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The ARN of the analyzer to retrieve findings from.</p>"}, "filter": {"shape": "FilterCriteriaMap", "documentation": "<p>A filter to match for the findings to return.</p>"}, "maxResults": {"shape": "Integer", "documentation": "<p>The maximum number of results to return in the response.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}, "sort": {"shape": "SortCriteria", "documentation": "<p>The sort order for the findings returned.</p>"}}, "documentation": "<p>Retrieves a list of findings generated by the specified analyzer.</p>"}, "ListFindingsResponse": {"type": "structure", "required": ["findings"], "members": {"findings": {"shape": "FindingsList", "documentation": "<p>A list of findings retrieved from the analyzer that match the filter criteria specified, if any.</p>"}, "nextToken": {"shape": "Token", "documentation": "<p>A token used for pagination of results returned.</p>"}}, "documentation": "<p>The response to the request.</p>"}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The ARN of the resource to retrieve tags from.</p>", "location": "uri", "locationName": "resourceArn"}}, "documentation": "<p>Retrieves a list of tags applied to the specified resource.</p>"}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagsMap", "documentation": "<p>The tags that are applied to the specified resource.</p>"}}, "documentation": "<p>The response to the request.</p>"}, "Name": {"type": "string", "max": 255, "min": 1, "pattern": "^[A-Za-z][A-Za-z0-9_.-]*$"}, "OrderBy": {"type": "string", "enum": ["ASC", "DESC"]}, "PrincipalMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "ReasonCode": {"type": "string", "enum": ["AWS_SERVICE_ACCESS_DISABLED", "DELEGATED_ADMINISTRATOR_DEREGISTERED", "ORGANIZATION_DELETED", "SERVICE_LINKED_ROLE_CREATION_FAILED"]}, "ResourceArn": {"type": "string", "pattern": "arn:[^:]*:[^:]*:[^:]*:[^:]*:.*$"}, "ResourceNotFoundException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>The ID of the resource.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>The type of the resource.</p>"}}, "documentation": "<p>The specified resource could not be found.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ResourceType": {"type": "string", "enum": ["AWS::IAM::Role", "AWS::KMS::Key", "AWS::Lambda::Function", "AWS::Lambda::LayerVersion", "AWS::S3::<PERSON><PERSON>", "AWS::SQS::Queue"]}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>The resource ID.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>The resource type.</p>"}}, "documentation": "<p>Service quote met error.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "SharedViaList": {"type": "list", "member": {"shape": "String"}}, "SortCriteria": {"type": "structure", "members": {"attributeName": {"shape": "String", "documentation": "<p>The name of the attribute to sort on.</p>"}, "orderBy": {"shape": "OrderBy", "documentation": "<p>The sort order, ascending or descending.</p>"}}, "documentation": "<p>The criteria used to sort.</p>"}, "StartResourceScanRequest": {"type": "structure", "required": ["analyzerArn", "resourceArn"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The ARN of the analyzer to use to scan the policies applied to the specified resource.</p>"}, "resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource to scan.</p>"}}, "documentation": "<p>Starts a scan of the policies applied to the specified resource.</p>"}, "StatusReason": {"type": "structure", "required": ["code"], "members": {"code": {"shape": "ReasonCode", "documentation": "<p>The reason code for the current status of the analyzer.</p>"}}, "documentation": "<p>Provides more details about the current status of the analyzer. For example, if the creation for the analyzer fails, a <code>Failed</code> status is displayed. For an analyzer with organization as the type, this failure can be due to an issue with creating the service-linked roles required in the member accounts of the AWS organization.</p>"}, "String": {"type": "string"}, "TagKeys": {"type": "list", "member": {"shape": "String"}}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The ARN of the resource to add the tag to.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagsMap", "documentation": "<p>The tags to add to the resource.</p>"}}, "documentation": "<p>Adds a tag to the specified resource.</p>"}, "TagResourceResponse": {"type": "structure", "members": {}, "documentation": "<p>The response to the request.</p>"}, "TagsMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "retryAfterSeconds": {"shape": "Integer", "documentation": "<p>The seconds to wait to retry.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>Throttling limit exceeded error.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true}, "Timestamp": {"type": "timestamp", "timestampFormat": "iso8601"}, "Token": {"type": "string"}, "Type": {"type": "string", "enum": ["ACCOUNT", "ORGANIZATION"]}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The ARN of the resource to remove the tag from.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeys", "documentation": "<p>The key for the tag to add.</p>", "location": "querystring", "locationName": "tagKeys"}}, "documentation": "<p>Removes a tag from the specified resource.</p>"}, "UntagResourceResponse": {"type": "structure", "members": {}, "documentation": "<p>The response to the request.</p>"}, "UpdateArchiveRuleRequest": {"type": "structure", "required": ["analyzerName", "filter", "ruleName"], "members": {"analyzerName": {"shape": "Name", "documentation": "<p>The name of the analyzer to update the archive rules for.</p>", "location": "uri", "locationName": "analyzerName"}, "clientToken": {"shape": "String", "documentation": "<p>A client token.</p>", "idempotencyToken": true}, "filter": {"shape": "FilterCriteriaMap", "documentation": "<p>A filter to match for the rules to update. Only rules that match the filter are updated.</p>"}, "ruleName": {"shape": "Name", "documentation": "<p>The name of the rule to update.</p>", "location": "uri", "locationName": "ruleName"}}, "documentation": "<p>Updates the specified archive rule.</p>"}, "UpdateFindingsRequest": {"type": "structure", "required": ["analyzerArn", "status"], "members": {"analyzerArn": {"shape": "AnalyzerArn", "documentation": "<p>The ARN of the analyzer that generated the findings to update.</p>"}, "clientToken": {"shape": "String", "documentation": "<p>A client token.</p>", "idempotencyToken": true}, "ids": {"shape": "FindingIdList", "documentation": "<p>The IDs of the findings to update.</p>"}, "resourceArn": {"shape": "ResourceArn", "documentation": "<p>The ARN of the resource identified in the finding.</p>"}, "status": {"shape": "FindingStatusUpdate", "documentation": "<p>The state represents the action to take to update the finding Status. Use <code>ARCHIVE</code> to change an Active finding to an Archived finding. Use <code>ACTIVE</code> to change an Archived finding to an Active finding.</p>"}}, "documentation": "<p>Updates findings with the new values provided in the request.</p>"}, "ValidationException": {"type": "structure", "required": ["message", "reason"], "members": {"fieldList": {"shape": "ValidationExceptionFieldList", "documentation": "<p>A list of fields that didn't validate.</p>"}, "message": {"shape": "String"}, "reason": {"shape": "ValidationExceptionReason", "documentation": "<p>The reason for the exception.</p>"}}, "documentation": "<p>Validation exception error.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["message", "name"], "members": {"message": {"shape": "String", "documentation": "<p>A message about the validation exception.</p>"}, "name": {"shape": "String", "documentation": "<p>The name of the validation exception.</p>"}}, "documentation": "<p>Contains information about a validation exception.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationExceptionReason": {"type": "string", "enum": ["<PERSON><PERSON><PERSON><PERSON>", "fieldValidationFailed", "other", "unknownOperation"]}, "ValueList": {"type": "list", "member": {"shape": "String"}, "max": 20, "min": 1}}, "documentation": "<p>AWS IAM Access Analyzer helps identify potential resource-access risks by enabling you to identify any policies that grant access to an external principal. It does this by using logic-based reasoning to analyze resource-based policies in your AWS environment. An external principal can be another AWS account, a root user, an IAM user or role, a federated user, an AWS service, or an anonymous user. This guide describes the AWS IAM Access Analyzer operations that you can call programmatically. For general information about Access Analyzer, see the <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/what-is-access-analyzer.html\">AWS IAM Access Analyzer section of the IAM User Guide</a>.</p> <p>To start using Access Analyzer, you first need to create an analyzer.</p>"}
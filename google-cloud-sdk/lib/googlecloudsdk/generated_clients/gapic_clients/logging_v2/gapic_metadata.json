{"comment": "This file maps proto services/RPCs to the corresponding library clients/methods", "language": "python", "libraryPackage": "googlecloudsdk.generated_clients.gapic_clients.logging_v2", "protoPackage": "google.logging.v2", "schema": "1.0", "services": {"ConfigServiceV2": {"clients": {"grpc": {"libraryClient": "ConfigServiceV2Client", "rpcs": {"CopyLogEntries": {"methods": ["copy_log_entries"]}, "CreateBucket": {"methods": ["create_bucket"]}, "CreateBucketAsync": {"methods": ["create_bucket_async"]}, "CreateExclusion": {"methods": ["create_exclusion"]}, "CreateLink": {"methods": ["create_link"]}, "CreateSink": {"methods": ["create_sink"]}, "CreateView": {"methods": ["create_view"]}, "DeleteBucket": {"methods": ["delete_bucket"]}, "DeleteExclusion": {"methods": ["delete_exclusion"]}, "DeleteLink": {"methods": ["delete_link"]}, "DeleteSink": {"methods": ["delete_sink"]}, "DeleteView": {"methods": ["delete_view"]}, "GetBucket": {"methods": ["get_bucket"]}, "GetCmekSettings": {"methods": ["get_cmek_settings"]}, "GetExclusion": {"methods": ["get_exclusion"]}, "GetLink": {"methods": ["get_link"]}, "GetSettings": {"methods": ["get_settings"]}, "GetSink": {"methods": ["get_sink"]}, "GetView": {"methods": ["get_view"]}, "ListBuckets": {"methods": ["list_buckets"]}, "ListExclusions": {"methods": ["list_exclusions"]}, "ListLinks": {"methods": ["list_links"]}, "ListSinks": {"methods": ["list_sinks"]}, "ListViews": {"methods": ["list_views"]}, "UndeleteBucket": {"methods": ["undelete_bucket"]}, "UpdateBucket": {"methods": ["update_bucket"]}, "UpdateBucketAsync": {"methods": ["update_bucket_async"]}, "UpdateCmekSettings": {"methods": ["update_cmek_settings"]}, "UpdateExclusion": {"methods": ["update_exclusion"]}, "UpdateSettings": {"methods": ["update_settings"]}, "UpdateSink": {"methods": ["update_sink"]}, "UpdateView": {"methods": ["update_view"]}}}, "grpc-async": {"libraryClient": "ConfigServiceV2AsyncClient", "rpcs": {"CopyLogEntries": {"methods": ["copy_log_entries"]}, "CreateBucket": {"methods": ["create_bucket"]}, "CreateBucketAsync": {"methods": ["create_bucket_async"]}, "CreateExclusion": {"methods": ["create_exclusion"]}, "CreateLink": {"methods": ["create_link"]}, "CreateSink": {"methods": ["create_sink"]}, "CreateView": {"methods": ["create_view"]}, "DeleteBucket": {"methods": ["delete_bucket"]}, "DeleteExclusion": {"methods": ["delete_exclusion"]}, "DeleteLink": {"methods": ["delete_link"]}, "DeleteSink": {"methods": ["delete_sink"]}, "DeleteView": {"methods": ["delete_view"]}, "GetBucket": {"methods": ["get_bucket"]}, "GetCmekSettings": {"methods": ["get_cmek_settings"]}, "GetExclusion": {"methods": ["get_exclusion"]}, "GetLink": {"methods": ["get_link"]}, "GetSettings": {"methods": ["get_settings"]}, "GetSink": {"methods": ["get_sink"]}, "GetView": {"methods": ["get_view"]}, "ListBuckets": {"methods": ["list_buckets"]}, "ListExclusions": {"methods": ["list_exclusions"]}, "ListLinks": {"methods": ["list_links"]}, "ListSinks": {"methods": ["list_sinks"]}, "ListViews": {"methods": ["list_views"]}, "UndeleteBucket": {"methods": ["undelete_bucket"]}, "UpdateBucket": {"methods": ["update_bucket"]}, "UpdateBucketAsync": {"methods": ["update_bucket_async"]}, "UpdateCmekSettings": {"methods": ["update_cmek_settings"]}, "UpdateExclusion": {"methods": ["update_exclusion"]}, "UpdateSettings": {"methods": ["update_settings"]}, "UpdateSink": {"methods": ["update_sink"]}, "UpdateView": {"methods": ["update_view"]}}}, "rest": {"libraryClient": "ConfigServiceV2Client", "rpcs": {"CopyLogEntries": {"methods": ["copy_log_entries"]}, "CreateBucket": {"methods": ["create_bucket"]}, "CreateBucketAsync": {"methods": ["create_bucket_async"]}, "CreateExclusion": {"methods": ["create_exclusion"]}, "CreateLink": {"methods": ["create_link"]}, "CreateSink": {"methods": ["create_sink"]}, "CreateView": {"methods": ["create_view"]}, "DeleteBucket": {"methods": ["delete_bucket"]}, "DeleteExclusion": {"methods": ["delete_exclusion"]}, "DeleteLink": {"methods": ["delete_link"]}, "DeleteSink": {"methods": ["delete_sink"]}, "DeleteView": {"methods": ["delete_view"]}, "GetBucket": {"methods": ["get_bucket"]}, "GetCmekSettings": {"methods": ["get_cmek_settings"]}, "GetExclusion": {"methods": ["get_exclusion"]}, "GetLink": {"methods": ["get_link"]}, "GetSettings": {"methods": ["get_settings"]}, "GetSink": {"methods": ["get_sink"]}, "GetView": {"methods": ["get_view"]}, "ListBuckets": {"methods": ["list_buckets"]}, "ListExclusions": {"methods": ["list_exclusions"]}, "ListLinks": {"methods": ["list_links"]}, "ListSinks": {"methods": ["list_sinks"]}, "ListViews": {"methods": ["list_views"]}, "UndeleteBucket": {"methods": ["undelete_bucket"]}, "UpdateBucket": {"methods": ["update_bucket"]}, "UpdateBucketAsync": {"methods": ["update_bucket_async"]}, "UpdateCmekSettings": {"methods": ["update_cmek_settings"]}, "UpdateExclusion": {"methods": ["update_exclusion"]}, "UpdateSettings": {"methods": ["update_settings"]}, "UpdateSink": {"methods": ["update_sink"]}, "UpdateView": {"methods": ["update_view"]}}}}}, "LoggingServiceV2": {"clients": {"grpc": {"libraryClient": "LoggingServiceV2Client", "rpcs": {"DeleteLog": {"methods": ["delete_log"]}, "ListLogEntries": {"methods": ["list_log_entries"]}, "ListLogs": {"methods": ["list_logs"]}, "ListMonitoredResourceDescriptors": {"methods": ["list_monitored_resource_descriptors"]}, "TailLogEntries": {"methods": ["tail_log_entries"]}, "WriteLogEntries": {"methods": ["write_log_entries"]}}}, "grpc-async": {"libraryClient": "LoggingServiceV2AsyncClient", "rpcs": {"DeleteLog": {"methods": ["delete_log"]}, "ListLogEntries": {"methods": ["list_log_entries"]}, "ListLogs": {"methods": ["list_logs"]}, "ListMonitoredResourceDescriptors": {"methods": ["list_monitored_resource_descriptors"]}, "TailLogEntries": {"methods": ["tail_log_entries"]}, "WriteLogEntries": {"methods": ["write_log_entries"]}}}, "rest": {"libraryClient": "LoggingServiceV2Client", "rpcs": {"DeleteLog": {"methods": ["delete_log"]}, "ListLogEntries": {"methods": ["list_log_entries"]}, "ListLogs": {"methods": ["list_logs"]}, "ListMonitoredResourceDescriptors": {"methods": ["list_monitored_resource_descriptors"]}, "TailLogEntries": {"methods": ["tail_log_entries"]}, "WriteLogEntries": {"methods": ["write_log_entries"]}}}}}, "MetricsServiceV2": {"clients": {"grpc": {"libraryClient": "MetricsServiceV2Client", "rpcs": {"CreateLogMetric": {"methods": ["create_log_metric"]}, "DeleteLogMetric": {"methods": ["delete_log_metric"]}, "GetLogMetric": {"methods": ["get_log_metric"]}, "ListLogMetrics": {"methods": ["list_log_metrics"]}, "UpdateLogMetric": {"methods": ["update_log_metric"]}}}, "grpc-async": {"libraryClient": "MetricsServiceV2AsyncClient", "rpcs": {"CreateLogMetric": {"methods": ["create_log_metric"]}, "DeleteLogMetric": {"methods": ["delete_log_metric"]}, "GetLogMetric": {"methods": ["get_log_metric"]}, "ListLogMetrics": {"methods": ["list_log_metrics"]}, "UpdateLogMetric": {"methods": ["update_log_metric"]}}}, "rest": {"libraryClient": "MetricsServiceV2Client", "rpcs": {"CreateLogMetric": {"methods": ["create_log_metric"]}, "DeleteLogMetric": {"methods": ["delete_log_metric"]}, "GetLogMetric": {"methods": ["get_log_metric"]}, "ListLogMetrics": {"methods": ["list_log_metrics"]}, "UpdateLogMetric": {"methods": ["update_log_metric"]}}}}}}}
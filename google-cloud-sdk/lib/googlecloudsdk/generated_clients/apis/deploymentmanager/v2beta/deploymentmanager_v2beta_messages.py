"""Generated message classes for deploymentmanager version v2beta.

The Google Cloud Deployment Manager v2 API provides services for configuring,
deploying, and viewing Google Cloud services and APIs via templates which
specify deployments of Cloud resources.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'deploymentmanager'


class AsyncOptions(_messages.Message):
  r"""Async options that determine when a resource should finish.

  Fields:
    methodMatch: Method regex where this policy will apply.
    pollingOptions: Deployment manager will poll instances for this API
      resource setting a RUNNING state, and blocking until polling conditions
      tell whether the resource is completed or failed.
  """

  methodMatch = _messages.StringField(1)
  pollingOptions = _messages.MessageField('PollingOptions', 2)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class BaseType(_messages.Message):
  r"""BaseType that describes a service-backed Type.

  Fields:
    collectionOverrides: Allows resource handling overrides for specific
      collections
    credential: Credential used when interacting with this type.
    descriptorUrl: Descriptor Url for the this type.
    options: Options to apply when handling any resources in this service.
  """

  collectionOverrides = _messages.MessageField('CollectionOverride', 1, repeated=True)
  credential = _messages.MessageField('Credential', 2)
  descriptorUrl = _messages.StringField(3)
  options = _messages.MessageField('Options', 4)


class BasicAuth(_messages.Message):
  r"""Basic Auth used as a credential.

  Fields:
    password: A string attribute.
    user: A string attribute.
  """

  password = _messages.StringField(1)
  user = _messages.StringField(2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class BulkInsertOperationStatus(_messages.Message):
  r"""A BulkInsertOperationStatus object.

  Enums:
    StatusValueValuesEnum: [Output Only] Creation status of BulkInsert
      operation - information if the flow is rolling forward or rolling back.

  Fields:
    createdVmCount: [Output Only] Count of VMs successfully created so far.
    deletedVmCount: [Output Only] Count of VMs that got deleted during
      rollback.
    failedToCreateVmCount: [Output Only] Count of VMs that started creating
      but encountered an error.
    status: [Output Only] Creation status of BulkInsert operation -
      information if the flow is rolling forward or rolling back.
    targetVmCount: [Output Only] Count of VMs originally planned to be
      created.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""[Output Only] Creation status of BulkInsert operation - information if
    the flow is rolling forward or rolling back.

    Values:
      STATUS_UNSPECIFIED: <no description>
      CREATING: Rolling forward - creating VMs.
      ROLLING_BACK: Rolling back - cleaning up after an error.
      DONE: Done
    """
    STATUS_UNSPECIFIED = 0
    CREATING = 1
    ROLLING_BACK = 2
    DONE = 3

  createdVmCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  deletedVmCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  failedToCreateVmCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  status = _messages.EnumField('StatusValueValuesEnum', 4)
  targetVmCount = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class CollectionOverride(_messages.Message):
  r"""CollectionOverride allows resource handling overrides for specific
  resources within a BaseType

  Fields:
    collection: The collection that identifies this resource within its
      service.
    options: The options to apply to this resource-level override
  """

  collection = _messages.StringField(1)
  options = _messages.MessageField('Options', 2)


class CompositeType(_messages.Message):
  r"""Holds the composite type.

  Enums:
    StatusValueValuesEnum:

  Fields:
    description: An optional textual description of the resource; provided by
      the client when the resource is created.
    id: A string attribute.
    insertTime: Output only. Creation timestamp in RFC3339 text format.
    labels: Map of labels; provided by the client when the resource is created
      or updated. Specifically: Label keys must be between 1 and 63 characters
      long and must conform to the following regular expression:
      `[a-z]([-a-z0-9]*[a-z0-9])?` Label values must be between 0 and 63
      characters long and must conform to the regular expression
      `([a-z]([-a-z0-9]*[a-z0-9])?)?`.
    name: Name of the composite type, must follow the expression:
      `[a-z]([-a-z0-9_.]{0,61}[a-z0-9])?`.
    operation: Output only. The Operation that most recently ran, or is
      currently running, on this composite type.
    selfLink: Output only. Server defined URL for the resource.
    status: A StatusValueValuesEnum attribute.
    templateContents: Files for the template type.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""StatusValueValuesEnum enum type.

    Values:
      UNKNOWN_STATUS: <no description>
      DEPRECATED: <no description>
      EXPERIMENTAL: <no description>
      SUPPORTED: <no description>
    """
    UNKNOWN_STATUS = 0
    DEPRECATED = 1
    EXPERIMENTAL = 2
    SUPPORTED = 3

  description = _messages.StringField(1)
  id = _messages.IntegerField(2, variant=_messages.Variant.UINT64)
  insertTime = _messages.StringField(3)
  labels = _messages.MessageField('CompositeTypeLabelEntry', 4, repeated=True)
  name = _messages.StringField(5)
  operation = _messages.MessageField('Operation', 6)
  selfLink = _messages.StringField(7)
  status = _messages.EnumField('StatusValueValuesEnum', 8)
  templateContents = _messages.MessageField('TemplateContents', 9)


class CompositeTypeLabelEntry(_messages.Message):
  r"""Label object for CompositeTypes

  Fields:
    key: Key of the label
    value: Value of the label
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class CompositeTypesListResponse(_messages.Message):
  r"""A response that returns all Composite Types supported by Deployment
  Manager

  Fields:
    compositeTypes: Output only. A list of resource composite types supported
      by Deployment Manager.
    nextPageToken: A token used to continue a truncated list request.
  """

  compositeTypes = _messages.MessageField('CompositeType', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ConfigFile(_messages.Message):
  r"""A ConfigFile object.

  Fields:
    content: The contents of the file.
  """

  content = _messages.StringField(1)


class Credential(_messages.Message):
  r"""The credential used by Deployment Manager and TypeProvider. Only one of
  the options is permitted.

  Fields:
    basicAuth: Basic Auth Credential, only used by TypeProvider.
    serviceAccount: Service Account Credential, only used by Deployment.
    useProjectDefault: Specify to use the project default credential, only
      supported by Deployment.
  """

  basicAuth = _messages.MessageField('BasicAuth', 1)
  serviceAccount = _messages.MessageField('ServiceAccount', 2)
  useProjectDefault = _messages.BooleanField(3)


class Deployment(_messages.Message):
  r"""A Deployment object.

  Fields:
    description: An optional user-provided description of the deployment.
    fingerprint: Provides a fingerprint to use in requests to modify a
      deployment, such as `update()`, `stop()`, and `cancelPreview()`
      requests. A fingerprint is a randomly generated value that must be
      provided with `update()`, `stop()`, and `cancelPreview()` requests to
      perform optimistic locking. This ensures optimistic concurrency so that
      only one request happens at a time. The fingerprint is initially
      generated by Deployment Manager and changes after every request to
      modify data. To get the latest fingerprint value, perform a `get()`
      request to a deployment.
    id: A string attribute.
    insertTime: Output only. Creation timestamp in RFC3339 text format.
    labels: Map of One Platform labels; provided by the client when the
      resource is created or updated. Specifically: Label keys must be between
      1 and 63 characters long and must conform to the following regular
      expression: `[a-z]([-a-z0-9]*[a-z0-9])?` Label values must be between 0
      and 63 characters long and must conform to the regular expression
      `([a-z]([-a-z0-9]*[a-z0-9])?)?`.
    manifest: Output only. URL of the manifest representing the last manifest
      that was successfully deployed. If no manifest has been successfully
      deployed, this field will be absent.
    name: Name of the resource; provided by the client when the resource is
      created. The name must be 1-63 characters long, and comply with RFC1035.
      Specifically, the name must be 1-63 characters long and match the
      regular expression `[a-z]([-a-z0-9]*[a-z0-9])?` which means the first
      character must be a lowercase letter, and all following characters must
      be a dash, lowercase letter, or digit, except the last character, which
      cannot be a dash.
    operation: Output only. The Operation that most recently ran, or is
      currently running, on this deployment.
    selfLink: Output only. Server defined URL for the resource.
    target: [Input Only] The parameters that define your deployment, including
      the deployment configuration and relevant templates.
    update: Output only. If Deployment Manager is currently updating or
      previewing an update to this deployment, the updated configuration
      appears here.
    updateTime: Output only. Update timestamp in RFC3339 text format.
  """

  description = _messages.StringField(1)
  fingerprint = _messages.BytesField(2)
  id = _messages.IntegerField(3, variant=_messages.Variant.UINT64)
  insertTime = _messages.StringField(4)
  labels = _messages.MessageField('DeploymentLabelEntry', 5, repeated=True)
  manifest = _messages.StringField(6)
  name = _messages.StringField(7)
  operation = _messages.MessageField('Operation', 8)
  selfLink = _messages.StringField(9)
  target = _messages.MessageField('TargetConfiguration', 10)
  update = _messages.MessageField('DeploymentUpdate', 11)
  updateTime = _messages.StringField(12)


class DeploymentLabelEntry(_messages.Message):
  r"""Label object for Deployments

  Fields:
    key: Key of the label
    value: Value of the label
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class DeploymentUpdate(_messages.Message):
  r"""A DeploymentUpdate object.

  Fields:
    description: Output only. An optional user-provided description of the
      deployment after the current update has been applied.
    labels: Map of One Platform labels; provided by the client when the
      resource is created or updated. Specifically: Label keys must be between
      1 and 63 characters long and must conform to the following regular
      expression: `[a-z]([-a-z0-9]*[a-z0-9])?` Label values must be between 0
      and 63 characters long and must conform to the regular expression
      `([a-z]([-a-z0-9]*[a-z0-9])?)?`.
    manifest: Output only. URL of the manifest representing the update
      configuration of this deployment.
  """

  description = _messages.StringField(1)
  labels = _messages.MessageField('DeploymentUpdateLabelEntry', 2, repeated=True)
  manifest = _messages.StringField(3)


class DeploymentUpdateLabelEntry(_messages.Message):
  r"""Label object for DeploymentUpdate

  Fields:
    key: Key of the label
    value: Value of the label
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class DeploymentmanagerCompositeTypesDeleteRequest(_messages.Message):
  r"""A DeploymentmanagerCompositeTypesDeleteRequest object.

  Fields:
    compositeType: The name of the type for this request.
    project: The project ID for this request.
  """

  compositeType = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class DeploymentmanagerCompositeTypesGetRequest(_messages.Message):
  r"""A DeploymentmanagerCompositeTypesGetRequest object.

  Fields:
    compositeType: The name of the composite type for this request.
    project: The project ID for this request.
  """

  compositeType = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class DeploymentmanagerCompositeTypesInsertRequest(_messages.Message):
  r"""A DeploymentmanagerCompositeTypesInsertRequest object.

  Fields:
    compositeType: A CompositeType resource to be passed as the request body.
    project: The project ID for this request.
  """

  compositeType = _messages.MessageField('CompositeType', 1)
  project = _messages.StringField(2, required=True)


class DeploymentmanagerCompositeTypesListRequest(_messages.Message):
  r"""A DeploymentmanagerCompositeTypesListRequest object.

  Fields:
    filter: A filter expression that filters resources listed in the response.
      Most Compute resources support two types of filter expressions:
      expressions that support regular expressions and expressions that follow
      API improvement proposal AIP-160. If you want to use AIP-160, your
      expression must specify the field name, an operator, and the value that
      you want to use for filtering. The value must be a string, a number, or
      a boolean. The operator must be either `=`, `!=`, `>`, `<`, `<=`, `>=`
      or `:`. For example, if you are filtering Compute Engine instances, you
      can exclude instances named `example-instance` by specifying `name !=
      example-instance`. The `:` operator can be used with string fields to
      match substrings. For non-string fields it is equivalent to the `=`
      operator. The `:*` comparison can be used to test whether a key has been
      defined. For example, to find all objects with `owner` label use: ```
      labels.owner:* ``` You can also filter nested fields. For example, you
      could specify `scheduling.automaticRestart = false` to include instances
      only if they are not scheduled for automatic restarts. You can use
      filtering on nested fields to filter based on resource labels. To filter
      on multiple expressions, provide each separate expression within
      parentheses. For example: ``` (scheduling.automaticRestart = true)
      (cpuPlatform = "Intel Skylake") ``` By default, each expression is an
      `AND` expression. However, you can include `AND` and `OR` expressions
      explicitly. For example: ``` (cpuPlatform = "Intel Skylake") OR
      (cpuPlatform = "Intel Broadwell") AND (scheduling.automaticRestart =
      true) ``` If you want to use a regular expression, use the `eq` (equal)
      or `ne` (not equal) operator against a single un-parenthesized
      expression with or without quotes or against multiple parenthesized
      expressions. Examples: `fieldname eq unquoted literal` `fieldname eq
      'single quoted literal'` `fieldname eq "double quoted literal"`
      `(fieldname1 eq literal) (fieldname2 ne "literal")` The literal value is
      interpreted as a regular expression using Google RE2 library syntax. The
      literal value must match the entire field. For example, to filter for
      instances that do not end with name "instance", you would use `name ne
      .*instance`.
    maxResults: The maximum number of results per page that should be
      returned. If the number of available results is larger than
      `maxResults`, Compute Engine returns a `nextPageToken` that can be used
      to get the next page of results in subsequent list requests. Acceptable
      values are `0` to `500`, inclusive. (Default: `500`)
    orderBy: Sorts list results by a certain order. By default, results are
      returned in alphanumerical order based on the resource name. You can
      also sort results in descending order based on the creation timestamp
      using `orderBy="creationTimestamp desc"`. This sorts results based on
      the `creationTimestamp` field in reverse chronological order (newest
      result first). Use this to sort resources like operations so that the
      newest operation is returned first. Currently, only sorting by `name` or
      `creationTimestamp desc` is supported.
    pageToken: Specifies a page token to use. Set `pageToken` to the
      `nextPageToken` returned by a previous list request to get the next page
      of results.
    project: The project ID for this request.
  """

  filter = _messages.StringField(1)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.UINT32, default=500)
  orderBy = _messages.StringField(3)
  pageToken = _messages.StringField(4)
  project = _messages.StringField(5, required=True)


class DeploymentmanagerCompositeTypesPatchRequest(_messages.Message):
  r"""A DeploymentmanagerCompositeTypesPatchRequest object.

  Fields:
    compositeType: The name of the composite type for this request.
    compositeTypeResource: A CompositeType resource to be passed as the
      request body.
    project: The project ID for this request.
  """

  compositeType = _messages.StringField(1, required=True)
  compositeTypeResource = _messages.MessageField('CompositeType', 2)
  project = _messages.StringField(3, required=True)


class DeploymentmanagerCompositeTypesUpdateRequest(_messages.Message):
  r"""A DeploymentmanagerCompositeTypesUpdateRequest object.

  Fields:
    compositeType: The name of the composite type for this request.
    compositeTypeResource: A CompositeType resource to be passed as the
      request body.
    project: The project ID for this request.
  """

  compositeType = _messages.StringField(1, required=True)
  compositeTypeResource = _messages.MessageField('CompositeType', 2)
  project = _messages.StringField(3, required=True)


class DeploymentmanagerDeploymentsCancelPreviewRequest(_messages.Message):
  r"""A DeploymentmanagerDeploymentsCancelPreviewRequest object.

  Fields:
    deployment: The name of the deployment for this request.
    deploymentsCancelPreviewRequest: A DeploymentsCancelPreviewRequest
      resource to be passed as the request body.
    project: The project ID for this request.
  """

  deployment = _messages.StringField(1, required=True)
  deploymentsCancelPreviewRequest = _messages.MessageField('DeploymentsCancelPreviewRequest', 2)
  project = _messages.StringField(3, required=True)


class DeploymentmanagerDeploymentsDeleteRequest(_messages.Message):
  r"""A DeploymentmanagerDeploymentsDeleteRequest object.

  Enums:
    DeletePolicyValueValuesEnum: Sets the policy to use for deleting
      resources.

  Fields:
    deletePolicy: Sets the policy to use for deleting resources.
    deployment: The name of the deployment for this request.
    project: The project ID for this request.
  """

  class DeletePolicyValueValuesEnum(_messages.Enum):
    r"""Sets the policy to use for deleting resources.

    Values:
      DELETE: <no description>
      ABANDON: <no description>
    """
    DELETE = 0
    ABANDON = 1

  deletePolicy = _messages.EnumField('DeletePolicyValueValuesEnum', 1, default='DELETE')
  deployment = _messages.StringField(2, required=True)
  project = _messages.StringField(3, required=True)


class DeploymentmanagerDeploymentsGetIamPolicyRequest(_messages.Message):
  r"""A DeploymentmanagerDeploymentsGetIamPolicyRequest object.

  Fields:
    optionsRequestedPolicyVersion: Requested IAM Policy version.
    project: Project ID for this request.
    resource: Name or id of the resource for this request.
  """

  optionsRequestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  project = _messages.StringField(2, required=True)
  resource = _messages.StringField(3, required=True)


class DeploymentmanagerDeploymentsGetRequest(_messages.Message):
  r"""A DeploymentmanagerDeploymentsGetRequest object.

  Fields:
    deployment: The name of the deployment for this request.
    project: The project ID for this request.
  """

  deployment = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class DeploymentmanagerDeploymentsInsertRequest(_messages.Message):
  r"""A DeploymentmanagerDeploymentsInsertRequest object.

  Enums:
    CreatePolicyValueValuesEnum: Sets the policy to use for creating new
      resources.

  Fields:
    createPolicy: Sets the policy to use for creating new resources.
    deployment: A Deployment resource to be passed as the request body.
    preview: If set to true, creates a deployment and creates "shell"
      resources but does not actually instantiate these resources. This allows
      you to preview what your deployment looks like. After previewing a
      deployment, you can deploy your resources by making a request with the
      `update()` method or you can use the `cancelPreview()` method to cancel
      the preview altogether. Note that the deployment will still exist after
      you cancel the preview and you must separately delete this deployment if
      you want to remove it.
    project: The project ID for this request.
  """

  class CreatePolicyValueValuesEnum(_messages.Enum):
    r"""Sets the policy to use for creating new resources.

    Values:
      CREATE_OR_ACQUIRE: <no description>
      ACQUIRE: <no description>
      CREATE: <no description>
    """
    CREATE_OR_ACQUIRE = 0
    ACQUIRE = 1
    CREATE = 2

  createPolicy = _messages.EnumField('CreatePolicyValueValuesEnum', 1, default='CREATE_OR_ACQUIRE')
  deployment = _messages.MessageField('Deployment', 2)
  preview = _messages.BooleanField(3)
  project = _messages.StringField(4, required=True)


class DeploymentmanagerDeploymentsListRequest(_messages.Message):
  r"""A DeploymentmanagerDeploymentsListRequest object.

  Fields:
    filter: A filter expression that filters resources listed in the response.
      Most Compute resources support two types of filter expressions:
      expressions that support regular expressions and expressions that follow
      API improvement proposal AIP-160. If you want to use AIP-160, your
      expression must specify the field name, an operator, and the value that
      you want to use for filtering. The value must be a string, a number, or
      a boolean. The operator must be either `=`, `!=`, `>`, `<`, `<=`, `>=`
      or `:`. For example, if you are filtering Compute Engine instances, you
      can exclude instances named `example-instance` by specifying `name !=
      example-instance`. The `:` operator can be used with string fields to
      match substrings. For non-string fields it is equivalent to the `=`
      operator. The `:*` comparison can be used to test whether a key has been
      defined. For example, to find all objects with `owner` label use: ```
      labels.owner:* ``` You can also filter nested fields. For example, you
      could specify `scheduling.automaticRestart = false` to include instances
      only if they are not scheduled for automatic restarts. You can use
      filtering on nested fields to filter based on resource labels. To filter
      on multiple expressions, provide each separate expression within
      parentheses. For example: ``` (scheduling.automaticRestart = true)
      (cpuPlatform = "Intel Skylake") ``` By default, each expression is an
      `AND` expression. However, you can include `AND` and `OR` expressions
      explicitly. For example: ``` (cpuPlatform = "Intel Skylake") OR
      (cpuPlatform = "Intel Broadwell") AND (scheduling.automaticRestart =
      true) ``` If you want to use a regular expression, use the `eq` (equal)
      or `ne` (not equal) operator against a single un-parenthesized
      expression with or without quotes or against multiple parenthesized
      expressions. Examples: `fieldname eq unquoted literal` `fieldname eq
      'single quoted literal'` `fieldname eq "double quoted literal"`
      `(fieldname1 eq literal) (fieldname2 ne "literal")` The literal value is
      interpreted as a regular expression using Google RE2 library syntax. The
      literal value must match the entire field. For example, to filter for
      instances that do not end with name "instance", you would use `name ne
      .*instance`.
    maxResults: The maximum number of results per page that should be
      returned. If the number of available results is larger than
      `maxResults`, Compute Engine returns a `nextPageToken` that can be used
      to get the next page of results in subsequent list requests. Acceptable
      values are `0` to `500`, inclusive. (Default: `500`)
    orderBy: Sorts list results by a certain order. By default, results are
      returned in alphanumerical order based on the resource name. You can
      also sort results in descending order based on the creation timestamp
      using `orderBy="creationTimestamp desc"`. This sorts results based on
      the `creationTimestamp` field in reverse chronological order (newest
      result first). Use this to sort resources like operations so that the
      newest operation is returned first. Currently, only sorting by `name` or
      `creationTimestamp desc` is supported.
    pageToken: Specifies a page token to use. Set `pageToken` to the
      `nextPageToken` returned by a previous list request to get the next page
      of results.
    project: The project ID for this request.
  """

  filter = _messages.StringField(1)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.UINT32, default=500)
  orderBy = _messages.StringField(3)
  pageToken = _messages.StringField(4)
  project = _messages.StringField(5, required=True)


class DeploymentmanagerDeploymentsPatchRequest(_messages.Message):
  r"""A DeploymentmanagerDeploymentsPatchRequest object.

  Enums:
    CreatePolicyValueValuesEnum: Sets the policy to use for creating new
      resources.
    DeletePolicyValueValuesEnum: Sets the policy to use for deleting
      resources.

  Fields:
    createPolicy: Sets the policy to use for creating new resources.
    deletePolicy: Sets the policy to use for deleting resources.
    deployment: The name of the deployment for this request.
    deploymentResource: A Deployment resource to be passed as the request
      body.
    preview: If set to true, updates the deployment and creates and updates
      the "shell" resources but does not actually alter or instantiate these
      resources. This allows you to preview what your deployment will look
      like. You can use this intent to preview how an update would affect your
      deployment. You must provide a `target.config` with a configuration if
      this is set to true. After previewing a deployment, you can deploy your
      resources by making a request with the `update()` or you can
      `cancelPreview()` to remove the preview altogether. Note that the
      deployment will still exist after you cancel the preview and you must
      separately delete this deployment if you want to remove it.
    project: The project ID for this request.
  """

  class CreatePolicyValueValuesEnum(_messages.Enum):
    r"""Sets the policy to use for creating new resources.

    Values:
      CREATE_OR_ACQUIRE: <no description>
      ACQUIRE: <no description>
      CREATE: <no description>
    """
    CREATE_OR_ACQUIRE = 0
    ACQUIRE = 1
    CREATE = 2

  class DeletePolicyValueValuesEnum(_messages.Enum):
    r"""Sets the policy to use for deleting resources.

    Values:
      DELETE: <no description>
      ABANDON: <no description>
    """
    DELETE = 0
    ABANDON = 1

  createPolicy = _messages.EnumField('CreatePolicyValueValuesEnum', 1, default='CREATE_OR_ACQUIRE')
  deletePolicy = _messages.EnumField('DeletePolicyValueValuesEnum', 2, default='DELETE')
  deployment = _messages.StringField(3, required=True)
  deploymentResource = _messages.MessageField('Deployment', 4)
  preview = _messages.BooleanField(5, default=False)
  project = _messages.StringField(6, required=True)


class DeploymentmanagerDeploymentsSetIamPolicyRequest(_messages.Message):
  r"""A DeploymentmanagerDeploymentsSetIamPolicyRequest object.

  Fields:
    globalSetPolicyRequest: A GlobalSetPolicyRequest resource to be passed as
      the request body.
    project: Project ID for this request.
    resource: Name or id of the resource for this request.
  """

  globalSetPolicyRequest = _messages.MessageField('GlobalSetPolicyRequest', 1)
  project = _messages.StringField(2, required=True)
  resource = _messages.StringField(3, required=True)


class DeploymentmanagerDeploymentsStopRequest(_messages.Message):
  r"""A DeploymentmanagerDeploymentsStopRequest object.

  Fields:
    deployment: The name of the deployment for this request.
    deploymentsStopRequest: A DeploymentsStopRequest resource to be passed as
      the request body.
    project: The project ID for this request.
  """

  deployment = _messages.StringField(1, required=True)
  deploymentsStopRequest = _messages.MessageField('DeploymentsStopRequest', 2)
  project = _messages.StringField(3, required=True)


class DeploymentmanagerDeploymentsTestIamPermissionsRequest(_messages.Message):
  r"""A DeploymentmanagerDeploymentsTestIamPermissionsRequest object.

  Fields:
    project: Project ID for this request.
    resource: Name or id of the resource for this request.
    testPermissionsRequest: A TestPermissionsRequest resource to be passed as
      the request body.
  """

  project = _messages.StringField(1, required=True)
  resource = _messages.StringField(2, required=True)
  testPermissionsRequest = _messages.MessageField('TestPermissionsRequest', 3)


class DeploymentmanagerDeploymentsUpdateRequest(_messages.Message):
  r"""A DeploymentmanagerDeploymentsUpdateRequest object.

  Enums:
    CreatePolicyValueValuesEnum: Sets the policy to use for creating new
      resources.
    DeletePolicyValueValuesEnum: Sets the policy to use for deleting
      resources.

  Fields:
    createPolicy: Sets the policy to use for creating new resources.
    deletePolicy: Sets the policy to use for deleting resources.
    deployment: The name of the deployment for this request.
    deploymentResource: A Deployment resource to be passed as the request
      body.
    preview: If set to true, updates the deployment and creates and updates
      the "shell" resources but does not actually alter or instantiate these
      resources. This allows you to preview what your deployment will look
      like. You can use this intent to preview how an update would affect your
      deployment. You must provide a `target.config` with a configuration if
      this is set to true. After previewing a deployment, you can deploy your
      resources by making a request with the `update()` or you can
      `cancelPreview()` to remove the preview altogether. Note that the
      deployment will still exist after you cancel the preview and you must
      separately delete this deployment if you want to remove it.
    project: The project ID for this request.
  """

  class CreatePolicyValueValuesEnum(_messages.Enum):
    r"""Sets the policy to use for creating new resources.

    Values:
      CREATE_OR_ACQUIRE: <no description>
      ACQUIRE: <no description>
      CREATE: <no description>
    """
    CREATE_OR_ACQUIRE = 0
    ACQUIRE = 1
    CREATE = 2

  class DeletePolicyValueValuesEnum(_messages.Enum):
    r"""Sets the policy to use for deleting resources.

    Values:
      DELETE: <no description>
      ABANDON: <no description>
    """
    DELETE = 0
    ABANDON = 1

  createPolicy = _messages.EnumField('CreatePolicyValueValuesEnum', 1, default='CREATE_OR_ACQUIRE')
  deletePolicy = _messages.EnumField('DeletePolicyValueValuesEnum', 2, default='DELETE')
  deployment = _messages.StringField(3, required=True)
  deploymentResource = _messages.MessageField('Deployment', 4)
  preview = _messages.BooleanField(5, default=False)
  project = _messages.StringField(6, required=True)


class DeploymentmanagerManifestsGetRequest(_messages.Message):
  r"""A DeploymentmanagerManifestsGetRequest object.

  Fields:
    deployment: The name of the deployment for this request.
    manifest: The name of the manifest for this request.
    project: The project ID for this request.
  """

  deployment = _messages.StringField(1, required=True)
  manifest = _messages.StringField(2, required=True)
  project = _messages.StringField(3, required=True)


class DeploymentmanagerManifestsListRequest(_messages.Message):
  r"""A DeploymentmanagerManifestsListRequest object.

  Fields:
    deployment: The name of the deployment for this request.
    filter: A filter expression that filters resources listed in the response.
      Most Compute resources support two types of filter expressions:
      expressions that support regular expressions and expressions that follow
      API improvement proposal AIP-160. If you want to use AIP-160, your
      expression must specify the field name, an operator, and the value that
      you want to use for filtering. The value must be a string, a number, or
      a boolean. The operator must be either `=`, `!=`, `>`, `<`, `<=`, `>=`
      or `:`. For example, if you are filtering Compute Engine instances, you
      can exclude instances named `example-instance` by specifying `name !=
      example-instance`. The `:` operator can be used with string fields to
      match substrings. For non-string fields it is equivalent to the `=`
      operator. The `:*` comparison can be used to test whether a key has been
      defined. For example, to find all objects with `owner` label use: ```
      labels.owner:* ``` You can also filter nested fields. For example, you
      could specify `scheduling.automaticRestart = false` to include instances
      only if they are not scheduled for automatic restarts. You can use
      filtering on nested fields to filter based on resource labels. To filter
      on multiple expressions, provide each separate expression within
      parentheses. For example: ``` (scheduling.automaticRestart = true)
      (cpuPlatform = "Intel Skylake") ``` By default, each expression is an
      `AND` expression. However, you can include `AND` and `OR` expressions
      explicitly. For example: ``` (cpuPlatform = "Intel Skylake") OR
      (cpuPlatform = "Intel Broadwell") AND (scheduling.automaticRestart =
      true) ``` If you want to use a regular expression, use the `eq` (equal)
      or `ne` (not equal) operator against a single un-parenthesized
      expression with or without quotes or against multiple parenthesized
      expressions. Examples: `fieldname eq unquoted literal` `fieldname eq
      'single quoted literal'` `fieldname eq "double quoted literal"`
      `(fieldname1 eq literal) (fieldname2 ne "literal")` The literal value is
      interpreted as a regular expression using Google RE2 library syntax. The
      literal value must match the entire field. For example, to filter for
      instances that do not end with name "instance", you would use `name ne
      .*instance`.
    maxResults: The maximum number of results per page that should be
      returned. If the number of available results is larger than
      `maxResults`, Compute Engine returns a `nextPageToken` that can be used
      to get the next page of results in subsequent list requests. Acceptable
      values are `0` to `500`, inclusive. (Default: `500`)
    orderBy: Sorts list results by a certain order. By default, results are
      returned in alphanumerical order based on the resource name. You can
      also sort results in descending order based on the creation timestamp
      using `orderBy="creationTimestamp desc"`. This sorts results based on
      the `creationTimestamp` field in reverse chronological order (newest
      result first). Use this to sort resources like operations so that the
      newest operation is returned first. Currently, only sorting by `name` or
      `creationTimestamp desc` is supported.
    pageToken: Specifies a page token to use. Set `pageToken` to the
      `nextPageToken` returned by a previous list request to get the next page
      of results.
    project: The project ID for this request.
  """

  deployment = _messages.StringField(1, required=True)
  filter = _messages.StringField(2)
  maxResults = _messages.IntegerField(3, variant=_messages.Variant.UINT32, default=500)
  orderBy = _messages.StringField(4)
  pageToken = _messages.StringField(5)
  project = _messages.StringField(6, required=True)


class DeploymentmanagerOperationsGetRequest(_messages.Message):
  r"""A DeploymentmanagerOperationsGetRequest object.

  Fields:
    operation: The name of the operation for this request.
    project: The project ID for this request.
  """

  operation = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class DeploymentmanagerOperationsListRequest(_messages.Message):
  r"""A DeploymentmanagerOperationsListRequest object.

  Fields:
    filter: A filter expression that filters resources listed in the response.
      Most Compute resources support two types of filter expressions:
      expressions that support regular expressions and expressions that follow
      API improvement proposal AIP-160. If you want to use AIP-160, your
      expression must specify the field name, an operator, and the value that
      you want to use for filtering. The value must be a string, a number, or
      a boolean. The operator must be either `=`, `!=`, `>`, `<`, `<=`, `>=`
      or `:`. For example, if you are filtering Compute Engine instances, you
      can exclude instances named `example-instance` by specifying `name !=
      example-instance`. The `:` operator can be used with string fields to
      match substrings. For non-string fields it is equivalent to the `=`
      operator. The `:*` comparison can be used to test whether a key has been
      defined. For example, to find all objects with `owner` label use: ```
      labels.owner:* ``` You can also filter nested fields. For example, you
      could specify `scheduling.automaticRestart = false` to include instances
      only if they are not scheduled for automatic restarts. You can use
      filtering on nested fields to filter based on resource labels. To filter
      on multiple expressions, provide each separate expression within
      parentheses. For example: ``` (scheduling.automaticRestart = true)
      (cpuPlatform = "Intel Skylake") ``` By default, each expression is an
      `AND` expression. However, you can include `AND` and `OR` expressions
      explicitly. For example: ``` (cpuPlatform = "Intel Skylake") OR
      (cpuPlatform = "Intel Broadwell") AND (scheduling.automaticRestart =
      true) ``` If you want to use a regular expression, use the `eq` (equal)
      or `ne` (not equal) operator against a single un-parenthesized
      expression with or without quotes or against multiple parenthesized
      expressions. Examples: `fieldname eq unquoted literal` `fieldname eq
      'single quoted literal'` `fieldname eq "double quoted literal"`
      `(fieldname1 eq literal) (fieldname2 ne "literal")` The literal value is
      interpreted as a regular expression using Google RE2 library syntax. The
      literal value must match the entire field. For example, to filter for
      instances that do not end with name "instance", you would use `name ne
      .*instance`.
    maxResults: The maximum number of results per page that should be
      returned. If the number of available results is larger than
      `maxResults`, Compute Engine returns a `nextPageToken` that can be used
      to get the next page of results in subsequent list requests. Acceptable
      values are `0` to `500`, inclusive. (Default: `500`)
    orderBy: Sorts list results by a certain order. By default, results are
      returned in alphanumerical order based on the resource name. You can
      also sort results in descending order based on the creation timestamp
      using `orderBy="creationTimestamp desc"`. This sorts results based on
      the `creationTimestamp` field in reverse chronological order (newest
      result first). Use this to sort resources like operations so that the
      newest operation is returned first. Currently, only sorting by `name` or
      `creationTimestamp desc` is supported.
    pageToken: Specifies a page token to use. Set `pageToken` to the
      `nextPageToken` returned by a previous list request to get the next page
      of results.
    project: The project ID for this request.
  """

  filter = _messages.StringField(1)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.UINT32, default=500)
  orderBy = _messages.StringField(3)
  pageToken = _messages.StringField(4)
  project = _messages.StringField(5, required=True)


class DeploymentmanagerResourcesGetRequest(_messages.Message):
  r"""A DeploymentmanagerResourcesGetRequest object.

  Fields:
    deployment: The name of the deployment for this request.
    project: The project ID for this request.
    resource: The name of the resource for this request.
  """

  deployment = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)
  resource = _messages.StringField(3, required=True)


class DeploymentmanagerResourcesListRequest(_messages.Message):
  r"""A DeploymentmanagerResourcesListRequest object.

  Fields:
    deployment: The name of the deployment for this request.
    filter: A filter expression that filters resources listed in the response.
      Most Compute resources support two types of filter expressions:
      expressions that support regular expressions and expressions that follow
      API improvement proposal AIP-160. If you want to use AIP-160, your
      expression must specify the field name, an operator, and the value that
      you want to use for filtering. The value must be a string, a number, or
      a boolean. The operator must be either `=`, `!=`, `>`, `<`, `<=`, `>=`
      or `:`. For example, if you are filtering Compute Engine instances, you
      can exclude instances named `example-instance` by specifying `name !=
      example-instance`. The `:` operator can be used with string fields to
      match substrings. For non-string fields it is equivalent to the `=`
      operator. The `:*` comparison can be used to test whether a key has been
      defined. For example, to find all objects with `owner` label use: ```
      labels.owner:* ``` You can also filter nested fields. For example, you
      could specify `scheduling.automaticRestart = false` to include instances
      only if they are not scheduled for automatic restarts. You can use
      filtering on nested fields to filter based on resource labels. To filter
      on multiple expressions, provide each separate expression within
      parentheses. For example: ``` (scheduling.automaticRestart = true)
      (cpuPlatform = "Intel Skylake") ``` By default, each expression is an
      `AND` expression. However, you can include `AND` and `OR` expressions
      explicitly. For example: ``` (cpuPlatform = "Intel Skylake") OR
      (cpuPlatform = "Intel Broadwell") AND (scheduling.automaticRestart =
      true) ``` If you want to use a regular expression, use the `eq` (equal)
      or `ne` (not equal) operator against a single un-parenthesized
      expression with or without quotes or against multiple parenthesized
      expressions. Examples: `fieldname eq unquoted literal` `fieldname eq
      'single quoted literal'` `fieldname eq "double quoted literal"`
      `(fieldname1 eq literal) (fieldname2 ne "literal")` The literal value is
      interpreted as a regular expression using Google RE2 library syntax. The
      literal value must match the entire field. For example, to filter for
      instances that do not end with name "instance", you would use `name ne
      .*instance`.
    maxResults: The maximum number of results per page that should be
      returned. If the number of available results is larger than
      `maxResults`, Compute Engine returns a `nextPageToken` that can be used
      to get the next page of results in subsequent list requests. Acceptable
      values are `0` to `500`, inclusive. (Default: `500`)
    orderBy: Sorts list results by a certain order. By default, results are
      returned in alphanumerical order based on the resource name. You can
      also sort results in descending order based on the creation timestamp
      using `orderBy="creationTimestamp desc"`. This sorts results based on
      the `creationTimestamp` field in reverse chronological order (newest
      result first). Use this to sort resources like operations so that the
      newest operation is returned first. Currently, only sorting by `name` or
      `creationTimestamp desc` is supported.
    pageToken: Specifies a page token to use. Set `pageToken` to the
      `nextPageToken` returned by a previous list request to get the next page
      of results.
    project: The project ID for this request.
  """

  deployment = _messages.StringField(1, required=True)
  filter = _messages.StringField(2)
  maxResults = _messages.IntegerField(3, variant=_messages.Variant.UINT32, default=500)
  orderBy = _messages.StringField(4)
  pageToken = _messages.StringField(5)
  project = _messages.StringField(6, required=True)


class DeploymentmanagerTypeProvidersDeleteRequest(_messages.Message):
  r"""A DeploymentmanagerTypeProvidersDeleteRequest object.

  Fields:
    project: The project ID for this request.
    typeProvider: The name of the type provider for this request.
  """

  project = _messages.StringField(1, required=True)
  typeProvider = _messages.StringField(2, required=True)


class DeploymentmanagerTypeProvidersGetRequest(_messages.Message):
  r"""A DeploymentmanagerTypeProvidersGetRequest object.

  Fields:
    project: The project ID for this request.
    typeProvider: The name of the type provider for this request.
  """

  project = _messages.StringField(1, required=True)
  typeProvider = _messages.StringField(2, required=True)


class DeploymentmanagerTypeProvidersGetTypeRequest(_messages.Message):
  r"""A DeploymentmanagerTypeProvidersGetTypeRequest object.

  Fields:
    project: The project ID for this request.
    type: The name of the type provider type for this request.
    typeProvider: The name of the type provider for this request.
  """

  project = _messages.StringField(1, required=True)
  type = _messages.StringField(2, required=True)
  typeProvider = _messages.StringField(3, required=True)


class DeploymentmanagerTypeProvidersInsertRequest(_messages.Message):
  r"""A DeploymentmanagerTypeProvidersInsertRequest object.

  Fields:
    project: The project ID for this request.
    typeProvider: A TypeProvider resource to be passed as the request body.
  """

  project = _messages.StringField(1, required=True)
  typeProvider = _messages.MessageField('TypeProvider', 2)


class DeploymentmanagerTypeProvidersListRequest(_messages.Message):
  r"""A DeploymentmanagerTypeProvidersListRequest object.

  Fields:
    filter: A filter expression that filters resources listed in the response.
      Most Compute resources support two types of filter expressions:
      expressions that support regular expressions and expressions that follow
      API improvement proposal AIP-160. If you want to use AIP-160, your
      expression must specify the field name, an operator, and the value that
      you want to use for filtering. The value must be a string, a number, or
      a boolean. The operator must be either `=`, `!=`, `>`, `<`, `<=`, `>=`
      or `:`. For example, if you are filtering Compute Engine instances, you
      can exclude instances named `example-instance` by specifying `name !=
      example-instance`. The `:` operator can be used with string fields to
      match substrings. For non-string fields it is equivalent to the `=`
      operator. The `:*` comparison can be used to test whether a key has been
      defined. For example, to find all objects with `owner` label use: ```
      labels.owner:* ``` You can also filter nested fields. For example, you
      could specify `scheduling.automaticRestart = false` to include instances
      only if they are not scheduled for automatic restarts. You can use
      filtering on nested fields to filter based on resource labels. To filter
      on multiple expressions, provide each separate expression within
      parentheses. For example: ``` (scheduling.automaticRestart = true)
      (cpuPlatform = "Intel Skylake") ``` By default, each expression is an
      `AND` expression. However, you can include `AND` and `OR` expressions
      explicitly. For example: ``` (cpuPlatform = "Intel Skylake") OR
      (cpuPlatform = "Intel Broadwell") AND (scheduling.automaticRestart =
      true) ``` If you want to use a regular expression, use the `eq` (equal)
      or `ne` (not equal) operator against a single un-parenthesized
      expression with or without quotes or against multiple parenthesized
      expressions. Examples: `fieldname eq unquoted literal` `fieldname eq
      'single quoted literal'` `fieldname eq "double quoted literal"`
      `(fieldname1 eq literal) (fieldname2 ne "literal")` The literal value is
      interpreted as a regular expression using Google RE2 library syntax. The
      literal value must match the entire field. For example, to filter for
      instances that do not end with name "instance", you would use `name ne
      .*instance`.
    maxResults: The maximum number of results per page that should be
      returned. If the number of available results is larger than
      `maxResults`, Compute Engine returns a `nextPageToken` that can be used
      to get the next page of results in subsequent list requests. Acceptable
      values are `0` to `500`, inclusive. (Default: `500`)
    orderBy: Sorts list results by a certain order. By default, results are
      returned in alphanumerical order based on the resource name. You can
      also sort results in descending order based on the creation timestamp
      using `orderBy="creationTimestamp desc"`. This sorts results based on
      the `creationTimestamp` field in reverse chronological order (newest
      result first). Use this to sort resources like operations so that the
      newest operation is returned first. Currently, only sorting by `name` or
      `creationTimestamp desc` is supported.
    pageToken: Specifies a page token to use. Set `pageToken` to the
      `nextPageToken` returned by a previous list request to get the next page
      of results.
    project: The project ID for this request.
  """

  filter = _messages.StringField(1)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.UINT32, default=500)
  orderBy = _messages.StringField(3)
  pageToken = _messages.StringField(4)
  project = _messages.StringField(5, required=True)


class DeploymentmanagerTypeProvidersListTypesRequest(_messages.Message):
  r"""A DeploymentmanagerTypeProvidersListTypesRequest object.

  Fields:
    filter: A filter expression that filters resources listed in the response.
      Most Compute resources support two types of filter expressions:
      expressions that support regular expressions and expressions that follow
      API improvement proposal AIP-160. If you want to use AIP-160, your
      expression must specify the field name, an operator, and the value that
      you want to use for filtering. The value must be a string, a number, or
      a boolean. The operator must be either `=`, `!=`, `>`, `<`, `<=`, `>=`
      or `:`. For example, if you are filtering Compute Engine instances, you
      can exclude instances named `example-instance` by specifying `name !=
      example-instance`. The `:` operator can be used with string fields to
      match substrings. For non-string fields it is equivalent to the `=`
      operator. The `:*` comparison can be used to test whether a key has been
      defined. For example, to find all objects with `owner` label use: ```
      labels.owner:* ``` You can also filter nested fields. For example, you
      could specify `scheduling.automaticRestart = false` to include instances
      only if they are not scheduled for automatic restarts. You can use
      filtering on nested fields to filter based on resource labels. To filter
      on multiple expressions, provide each separate expression within
      parentheses. For example: ``` (scheduling.automaticRestart = true)
      (cpuPlatform = "Intel Skylake") ``` By default, each expression is an
      `AND` expression. However, you can include `AND` and `OR` expressions
      explicitly. For example: ``` (cpuPlatform = "Intel Skylake") OR
      (cpuPlatform = "Intel Broadwell") AND (scheduling.automaticRestart =
      true) ``` If you want to use a regular expression, use the `eq` (equal)
      or `ne` (not equal) operator against a single un-parenthesized
      expression with or without quotes or against multiple parenthesized
      expressions. Examples: `fieldname eq unquoted literal` `fieldname eq
      'single quoted literal'` `fieldname eq "double quoted literal"`
      `(fieldname1 eq literal) (fieldname2 ne "literal")` The literal value is
      interpreted as a regular expression using Google RE2 library syntax. The
      literal value must match the entire field. For example, to filter for
      instances that do not end with name "instance", you would use `name ne
      .*instance`.
    maxResults: The maximum number of results per page that should be
      returned. If the number of available results is larger than
      `maxResults`, Compute Engine returns a `nextPageToken` that can be used
      to get the next page of results in subsequent list requests. Acceptable
      values are `0` to `500`, inclusive. (Default: `500`)
    orderBy: Sorts list results by a certain order. By default, results are
      returned in alphanumerical order based on the resource name. You can
      also sort results in descending order based on the creation timestamp
      using `orderBy="creationTimestamp desc"`. This sorts results based on
      the `creationTimestamp` field in reverse chronological order (newest
      result first). Use this to sort resources like operations so that the
      newest operation is returned first. Currently, only sorting by `name` or
      `creationTimestamp desc` is supported.
    pageToken: Specifies a page token to use. Set `pageToken` to the
      `nextPageToken` returned by a previous list request to get the next page
      of results.
    project: The project ID for this request.
    typeProvider: The name of the type provider for this request.
  """

  filter = _messages.StringField(1)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.UINT32, default=500)
  orderBy = _messages.StringField(3)
  pageToken = _messages.StringField(4)
  project = _messages.StringField(5, required=True)
  typeProvider = _messages.StringField(6, required=True)


class DeploymentmanagerTypeProvidersPatchRequest(_messages.Message):
  r"""A DeploymentmanagerTypeProvidersPatchRequest object.

  Fields:
    project: The project ID for this request.
    typeProvider: The name of the type provider for this request.
    typeProviderResource: A TypeProvider resource to be passed as the request
      body.
  """

  project = _messages.StringField(1, required=True)
  typeProvider = _messages.StringField(2, required=True)
  typeProviderResource = _messages.MessageField('TypeProvider', 3)


class DeploymentmanagerTypeProvidersUpdateRequest(_messages.Message):
  r"""A DeploymentmanagerTypeProvidersUpdateRequest object.

  Fields:
    project: The project ID for this request.
    typeProvider: The name of the type provider for this request.
    typeProviderResource: A TypeProvider resource to be passed as the request
      body.
  """

  project = _messages.StringField(1, required=True)
  typeProvider = _messages.StringField(2, required=True)
  typeProviderResource = _messages.MessageField('TypeProvider', 3)


class DeploymentmanagerTypesListRequest(_messages.Message):
  r"""A DeploymentmanagerTypesListRequest object.

  Fields:
    filter: A filter expression that filters resources listed in the response.
      Most Compute resources support two types of filter expressions:
      expressions that support regular expressions and expressions that follow
      API improvement proposal AIP-160. If you want to use AIP-160, your
      expression must specify the field name, an operator, and the value that
      you want to use for filtering. The value must be a string, a number, or
      a boolean. The operator must be either `=`, `!=`, `>`, `<`, `<=`, `>=`
      or `:`. For example, if you are filtering Compute Engine instances, you
      can exclude instances named `example-instance` by specifying `name !=
      example-instance`. The `:` operator can be used with string fields to
      match substrings. For non-string fields it is equivalent to the `=`
      operator. The `:*` comparison can be used to test whether a key has been
      defined. For example, to find all objects with `owner` label use: ```
      labels.owner:* ``` You can also filter nested fields. For example, you
      could specify `scheduling.automaticRestart = false` to include instances
      only if they are not scheduled for automatic restarts. You can use
      filtering on nested fields to filter based on resource labels. To filter
      on multiple expressions, provide each separate expression within
      parentheses. For example: ``` (scheduling.automaticRestart = true)
      (cpuPlatform = "Intel Skylake") ``` By default, each expression is an
      `AND` expression. However, you can include `AND` and `OR` expressions
      explicitly. For example: ``` (cpuPlatform = "Intel Skylake") OR
      (cpuPlatform = "Intel Broadwell") AND (scheduling.automaticRestart =
      true) ``` If you want to use a regular expression, use the `eq` (equal)
      or `ne` (not equal) operator against a single un-parenthesized
      expression with or without quotes or against multiple parenthesized
      expressions. Examples: `fieldname eq unquoted literal` `fieldname eq
      'single quoted literal'` `fieldname eq "double quoted literal"`
      `(fieldname1 eq literal) (fieldname2 ne "literal")` The literal value is
      interpreted as a regular expression using Google RE2 library syntax. The
      literal value must match the entire field. For example, to filter for
      instances that do not end with name "instance", you would use `name ne
      .*instance`.
    maxResults: The maximum number of results per page that should be
      returned. If the number of available results is larger than
      `maxResults`, Compute Engine returns a `nextPageToken` that can be used
      to get the next page of results in subsequent list requests. Acceptable
      values are `0` to `500`, inclusive. (Default: `500`)
    orderBy: Sorts list results by a certain order. By default, results are
      returned in alphanumerical order based on the resource name. You can
      also sort results in descending order based on the creation timestamp
      using `orderBy="creationTimestamp desc"`. This sorts results based on
      the `creationTimestamp` field in reverse chronological order (newest
      result first). Use this to sort resources like operations so that the
      newest operation is returned first. Currently, only sorting by `name` or
      `creationTimestamp desc` is supported.
    pageToken: Specifies a page token to use. Set `pageToken` to the
      `nextPageToken` returned by a previous list request to get the next page
      of results.
    project: The project ID for this request.
  """

  filter = _messages.StringField(1)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.UINT32, default=500)
  orderBy = _messages.StringField(3)
  pageToken = _messages.StringField(4)
  project = _messages.StringField(5, required=True)


class DeploymentsCancelPreviewRequest(_messages.Message):
  r"""A DeploymentsCancelPreviewRequest object.

  Fields:
    fingerprint: Specifies a fingerprint for `cancelPreview()` requests. A
      fingerprint is a randomly generated value that must be provided in
      `cancelPreview()` requests to perform optimistic locking. This ensures
      optimistic concurrency so that the deployment does not have conflicting
      requests (e.g. if someone attempts to make a new update request while
      another user attempts to cancel a preview, this would prevent one of the
      requests). The fingerprint is initially generated by Deployment Manager
      and changes after every request to modify a deployment. To get the
      latest fingerprint value, perform a `get()` request on the deployment.
  """

  fingerprint = _messages.BytesField(1)


class DeploymentsListResponse(_messages.Message):
  r"""A response containing a partial list of deployments and a page token
  used to build the next request if the request has been truncated.

  Fields:
    deployments: Output only. The deployments contained in this response.
    nextPageToken: Output only. A token used to continue a truncated list
      request.
  """

  deployments = _messages.MessageField('Deployment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class DeploymentsStopRequest(_messages.Message):
  r"""A DeploymentsStopRequest object.

  Fields:
    fingerprint: Specifies a fingerprint for `stop()` requests. A fingerprint
      is a randomly generated value that must be provided in `stop()` requests
      to perform optimistic locking. This ensures optimistic concurrency so
      that the deployment does not have conflicting requests (e.g. if someone
      attempts to make a new update request while another user attempts to
      stop an ongoing update request, this would prevent a collision). The
      fingerprint is initially generated by Deployment Manager and changes
      after every request to modify a deployment. To get the latest
      fingerprint value, perform a `get()` request on the deployment.
  """

  fingerprint = _messages.BytesField(1)


class Diagnostic(_messages.Message):
  r"""A Diagnostic object.

  Enums:
    LevelValueValuesEnum: Level to record this diagnostic.

  Fields:
    field: JsonPath expression on the resource that if non empty, indicates
      that this field needs to be extracted as a diagnostic.
    level: Level to record this diagnostic.
  """

  class LevelValueValuesEnum(_messages.Enum):
    r"""Level to record this diagnostic.

    Values:
      UNKNOWN: <no description>
      INFORMATION: If level is informational, it only gets displayed as part
        of the resource.
      WARNING: If level is warning, will end up in the resource as a warning.
      ERROR: If level is error, it will indicate an error occurred after
        finishCondition is set, and this field will populate resource errors
        and operation errors.
    """
    UNKNOWN = 0
    INFORMATION = 1
    WARNING = 2
    ERROR = 3

  field = _messages.StringField(1)
  level = _messages.EnumField('LevelValueValuesEnum', 2)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GlobalSetPolicyRequest(_messages.Message):
  r"""A GlobalSetPolicyRequest object.

  Fields:
    bindings: Flatten Policy to create a backward compatible wire-format.
      Deprecated. Use 'policy' to specify bindings.
    etag: Flatten Policy to create a backward compatible wire-format.
      Deprecated. Use 'policy' to specify the etag.
    policy: REQUIRED: The complete policy to be applied to the 'resource'. The
      size of the policy is limited to a few 10s of KB. An empty policy is in
      general a valid policy but certain services (like Projects) might reject
      them.
  """

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  etag = _messages.BytesField(2)
  policy = _messages.MessageField('Policy', 3)


class ImportFile(_messages.Message):
  r"""A ImportFile object.

  Fields:
    content: The contents of the file.
    name: The name of the file.
  """

  content = _messages.StringField(1)
  name = _messages.StringField(2)


class InputMapping(_messages.Message):
  r"""InputMapping creates a 'virtual' property that will be injected into the
  properties before sending the request to the underlying API.

  Enums:
    LocationValueValuesEnum: The location where this mapping applies.

  Fields:
    fieldName: The name of the field that is going to be injected.
    location: The location where this mapping applies.
    methodMatch: Regex to evaluate on method to decide if input applies.
    value: A jsonPath expression to select an element.
  """

  class LocationValueValuesEnum(_messages.Enum):
    r"""The location where this mapping applies.

    Values:
      UNKNOWN: <no description>
      PATH: <no description>
      QUERY: <no description>
      BODY: <no description>
      HEADER: <no description>
    """
    UNKNOWN = 0
    PATH = 1
    QUERY = 2
    BODY = 3
    HEADER = 4

  fieldName = _messages.StringField(1)
  location = _messages.EnumField('LocationValueValuesEnum', 2)
  methodMatch = _messages.StringField(3)
  value = _messages.StringField(4)


class InstancesBulkInsertOperationMetadata(_messages.Message):
  r"""A InstancesBulkInsertOperationMetadata object.

  Messages:
    PerLocationStatusValue: Status information per location (location name is
      key). Example key: zones/us-central1-a

  Fields:
    perLocationStatus: Status information per location (location name is key).
      Example key: zones/us-central1-a
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PerLocationStatusValue(_messages.Message):
    r"""Status information per location (location name is key). Example key:
    zones/us-central1-a

    Messages:
      AdditionalProperty: An additional property for a PerLocationStatusValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        PerLocationStatusValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PerLocationStatusValue object.

      Fields:
        key: Name of the additional property.
        value: A BulkInsertOperationStatus attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('BulkInsertOperationStatus', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  perLocationStatus = _messages.MessageField('PerLocationStatusValue', 1)


class Manifest(_messages.Message):
  r"""A Manifest object.

  Fields:
    config: Output only. The YAML configuration for this manifest.
    expandedConfig: Output only. The fully-expanded configuration file,
      including any templates and references.
    id: A string attribute.
    imports: Output only. The imported files for this manifest.
    insertTime: Output only. Creation timestamp in RFC3339 text format.
    layout: Output only. The YAML layout for this manifest.
    manifestSizeBytes: Output only. The computed size of the fully expanded
      manifest.
    manifestSizeLimitBytes: Output only. The size limit for expanded manifests
      in the project.
    name: Output only. The name of the manifest.
    selfLink: Output only. Self link for the manifest.
  """

  config = _messages.MessageField('ConfigFile', 1)
  expandedConfig = _messages.StringField(2)
  id = _messages.IntegerField(3, variant=_messages.Variant.UINT64)
  imports = _messages.MessageField('ImportFile', 4, repeated=True)
  insertTime = _messages.StringField(5)
  layout = _messages.StringField(6)
  manifestSizeBytes = _messages.IntegerField(7)
  manifestSizeLimitBytes = _messages.IntegerField(8)
  name = _messages.StringField(9)
  selfLink = _messages.StringField(10)


class ManifestsListResponse(_messages.Message):
  r"""A response containing a partial list of manifests and a page token used
  to build the next request if the request has been truncated.

  Fields:
    manifests: Output only. Manifests contained in this list response.
    nextPageToken: Output only. A token used to continue a truncated list
      request.
  """

  manifests = _messages.MessageField('Manifest', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class Operation(_messages.Message):
  r"""Represents an Operation resource. Google Compute Engine has three
  Operation resources: *
  [Global](/compute/docs/reference/rest/{$api_version}/globalOperations) *
  [Regional](/compute/docs/reference/rest/{$api_version}/regionOperations) *
  [Zonal](/compute/docs/reference/rest/{$api_version}/zoneOperations) You can
  use an operation resource to manage asynchronous API requests. For more
  information, read Handling API responses. Operations can be global, regional
  or zonal. - For global operations, use the `globalOperations` resource. -
  For regional operations, use the `regionOperations` resource. - For zonal
  operations, use the `zoneOperations` resource. For more information, read
  Global, Regional, and Zonal Resources.

  Enums:
    StatusValueValuesEnum: [Output Only] The status of the operation, which
      can be one of the following: `PENDING`, `RUNNING`, or `DONE`.

  Messages:
    ErrorValue: [Output Only] If errors are generated during processing of the
      operation, this field will be populated.
    WarningsValueListEntry: A WarningsValueListEntry object.

  Fields:
    clientOperationId: [Output Only] The value of `requestId` if you provided
      it in the request. Not present otherwise.
    creationTimestamp: [Deprecated] This field is deprecated.
    description: [Output Only] A textual description of the operation, which
      is set when the operation is created.
    endTime: [Output Only] The time that this operation was completed. This
      value is in RFC3339 text format.
    error: [Output Only] If errors are generated during processing of the
      operation, this field will be populated.
    httpErrorMessage: [Output Only] If the operation fails, this field
      contains the HTTP error message that was returned, such as `NOT FOUND`.
    httpErrorStatusCode: [Output Only] If the operation fails, this field
      contains the HTTP error status code that was returned. For example, a
      `404` means the resource was not found.
    id: [Output Only] The unique identifier for the operation. This identifier
      is defined by the server.
    insertTime: [Output Only] The time that this operation was requested. This
      value is in RFC3339 text format.
    instancesBulkInsertOperationMetadata: A
      InstancesBulkInsertOperationMetadata attribute.
    kind: [Output Only] Type of the resource. Always `compute#operation` for
      Operation resources.
    name: [Output Only] Name of the operation.
    operationGroupId: [Output Only] An ID that represents a group of
      operations, such as when a group of operations results from a
      `bulkInsert` API request.
    operationType: [Output Only] The type of operation, such as `insert`,
      `update`, or `delete`, and so on.
    progress: [Output Only] An optional progress indicator that ranges from 0
      to 100. There is no requirement that this be linear or support any
      granularity of operations. This should not be used to guess when the
      operation will be complete. This number should monotonically increase as
      the operation progresses.
    region: [Output Only] The URL of the region where the operation resides.
      Only applicable when performing regional operations.
    selfLink: [Output Only] Server-defined URL for the resource.
    setCommonInstanceMetadataOperationMetadata: [Output Only] If the operation
      is for projects.setCommonInstanceMetadata, this field will contain
      information on all underlying zonal actions and their state.
    startTime: [Output Only] The time that this operation was started by the
      server. This value is in RFC3339 text format.
    status: [Output Only] The status of the operation, which can be one of the
      following: `PENDING`, `RUNNING`, or `DONE`.
    statusMessage: [Output Only] An optional textual description of the
      current status of the operation.
    targetId: [Output Only] The unique target ID, which identifies a specific
      incarnation of the target resource.
    targetLink: [Output Only] The URL of the resource that the operation
      modifies. For operations related to creating a snapshot, this points to
      the persistent disk that the snapshot was created from.
    user: [Output Only] User who requested the operation, for example:
      `<EMAIL>` or `alice_smith_identifier
      (global/workforcePools/example-com-us-employees)`.
    warnings: [Output Only] If warning messages are generated during
      processing of the operation, this field will be populated.
    zone: [Output Only] The URL of the zone where the operation resides. Only
      applicable when performing per-zone operations.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""[Output Only] The status of the operation, which can be one of the
    following: `PENDING`, `RUNNING`, or `DONE`.

    Values:
      PENDING: <no description>
      RUNNING: <no description>
      DONE: <no description>
    """
    PENDING = 0
    RUNNING = 1
    DONE = 2

  class ErrorValue(_messages.Message):
    r"""[Output Only] If errors are generated during processing of the
    operation, this field will be populated.

    Messages:
      ErrorsValueListEntry: A ErrorsValueListEntry object.

    Fields:
      errors: [Output Only] The array of errors encountered while processing
        this operation.
    """

    class ErrorsValueListEntry(_messages.Message):
      r"""A ErrorsValueListEntry object.

      Fields:
        code: [Output Only] The error type identifier for this error.
        location: [Output Only] Indicates the field in the request that caused
          the error. This property is optional.
        message: [Output Only] An optional, human-readable error message.
      """

      code = _messages.StringField(1)
      location = _messages.StringField(2)
      message = _messages.StringField(3)

    errors = _messages.MessageField('ErrorsValueListEntry', 1, repeated=True)

  class WarningsValueListEntry(_messages.Message):
    r"""A WarningsValueListEntry object.

    Enums:
      CodeValueValuesEnum: [Output Only] A warning code, if applicable. For
        example, Compute Engine returns NO_RESULTS_ON_PAGE if there are no
        results in the response.

    Messages:
      DataValueListEntry: A DataValueListEntry object.

    Fields:
      code: [Output Only] A warning code, if applicable. For example, Compute
        Engine returns NO_RESULTS_ON_PAGE if there are no results in the
        response.
      data: [Output Only] Metadata about this warning in key: value format.
        For example: "data": [ { "key": "scope", "value": "zones/us-east1-d" }
      message: [Output Only] A human-readable description of the warning code.
    """

    class CodeValueValuesEnum(_messages.Enum):
      r"""[Output Only] A warning code, if applicable. For example, Compute
      Engine returns NO_RESULTS_ON_PAGE if there are no results in the
      response.

      Values:
        DEPRECATED_RESOURCE_USED: A link to a deprecated resource was created.
        NO_RESULTS_ON_PAGE: No results are present on a particular list page.
        UNREACHABLE: A given scope cannot be reached.
        NEXT_HOP_ADDRESS_NOT_ASSIGNED: The route's nextHopIp address is not
          assigned to an instance on the network.
        NEXT_HOP_INSTANCE_NOT_FOUND: The route's nextHopInstance URL refers to
          an instance that does not exist.
        NEXT_HOP_INSTANCE_NOT_ON_NETWORK: The route's nextHopInstance URL
          refers to an instance that is not on the same network as the route.
        NEXT_HOP_CANNOT_IP_FORWARD: The route's next hop instance cannot ip
          forward.
        NEXT_HOP_NOT_RUNNING: The route's next hop instance does not have a
          status of RUNNING.
        INJECTED_KERNELS_DEPRECATED: The operation involved use of an injected
          kernel, which is deprecated.
        REQUIRED_TOS_AGREEMENT: The user attempted to use a resource that
          requires a TOS they have not accepted.
        DISK_SIZE_LARGER_THAN_IMAGE_SIZE: The user created a boot disk that is
          larger than image size.
        RESOURCE_NOT_DELETED: One or more of the resources set to auto-delete
          could not be deleted because they were in use.
        SINGLE_INSTANCE_PROPERTY_TEMPLATE: Instance template used in instance
          group manager is valid as such, but its application does not make a
          lot of sense, because it allows only single instance in instance
          group.
        NOT_CRITICAL_ERROR: Error which is not critical. We decided to
          continue the process despite the mentioned error.
        CLEANUP_FAILED: Warning about failed cleanup of transient changes made
          by a failed operation.
        FIELD_VALUE_OVERRIDEN: Warning that value of a field has been
          overridden. Deprecated unused field.
        RESOURCE_IN_USE_BY_OTHER_RESOURCE_WARNING: Warning that a resource is
          in use.
        MISSING_TYPE_DEPENDENCY: A resource depends on a missing type
        EXTERNAL_API_WARNING: Warning that is present in an external api call
        SCHEMA_VALIDATION_IGNORED: When a resource schema validation is
          ignored.
        UNDECLARED_PROPERTIES: When undeclared properties in the schema are
          present
        EXPERIMENTAL_TYPE_USED: When deploying and at least one of the
          resources has a type marked as experimental
        DEPRECATED_TYPE_USED: When deploying and at least one of the resources
          has a type marked as deprecated
        PARTIAL_SUCCESS: Success is reported, but some results may be missing
          due to errors
        LARGE_DEPLOYMENT_WARNING: When deploying a deployment with a
          exceedingly large number of resources
        NEXT_HOP_INSTANCE_HAS_NO_IPV6_INTERFACE: The route's nextHopInstance
          URL refers to an instance that does not have an ipv6 interface on
          the same network as the route.
        INVALID_HEALTH_CHECK_FOR_DYNAMIC_WIEGHTED_LB: A WEIGHTED_MAGLEV
          backend service is associated with a health check that is not of
          type HTTP/HTTPS/HTTP2.
        LIST_OVERHEAD_QUOTA_EXCEED: Resource can't be retrieved due to list
          overhead quota exceed which captures the amount of resources
          filtered out by user-defined list filter.
      """
      DEPRECATED_RESOURCE_USED = 0
      NO_RESULTS_ON_PAGE = 1
      UNREACHABLE = 2
      NEXT_HOP_ADDRESS_NOT_ASSIGNED = 3
      NEXT_HOP_INSTANCE_NOT_FOUND = 4
      NEXT_HOP_INSTANCE_NOT_ON_NETWORK = 5
      NEXT_HOP_CANNOT_IP_FORWARD = 6
      NEXT_HOP_NOT_RUNNING = 7
      INJECTED_KERNELS_DEPRECATED = 8
      REQUIRED_TOS_AGREEMENT = 9
      DISK_SIZE_LARGER_THAN_IMAGE_SIZE = 10
      RESOURCE_NOT_DELETED = 11
      SINGLE_INSTANCE_PROPERTY_TEMPLATE = 12
      NOT_CRITICAL_ERROR = 13
      CLEANUP_FAILED = 14
      FIELD_VALUE_OVERRIDEN = 15
      RESOURCE_IN_USE_BY_OTHER_RESOURCE_WARNING = 16
      MISSING_TYPE_DEPENDENCY = 17
      EXTERNAL_API_WARNING = 18
      SCHEMA_VALIDATION_IGNORED = 19
      UNDECLARED_PROPERTIES = 20
      EXPERIMENTAL_TYPE_USED = 21
      DEPRECATED_TYPE_USED = 22
      PARTIAL_SUCCESS = 23
      LARGE_DEPLOYMENT_WARNING = 24
      NEXT_HOP_INSTANCE_HAS_NO_IPV6_INTERFACE = 25
      INVALID_HEALTH_CHECK_FOR_DYNAMIC_WIEGHTED_LB = 26
      LIST_OVERHEAD_QUOTA_EXCEED = 27

    class DataValueListEntry(_messages.Message):
      r"""A DataValueListEntry object.

      Fields:
        key: [Output Only] A key that provides more detail on the warning
          being returned. For example, for warnings where there are no results
          in a list request for a particular zone, this key might be scope and
          the key value might be the zone name. Other examples might be a key
          indicating a deprecated resource and a suggested replacement, or a
          warning about invalid network settings (for example, if an instance
          attempts to perform IP forwarding but is not enabled for IP
          forwarding).
        value: [Output Only] A warning data value corresponding to the key.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    code = _messages.EnumField('CodeValueValuesEnum', 1)
    data = _messages.MessageField('DataValueListEntry', 2, repeated=True)
    message = _messages.StringField(3)

  clientOperationId = _messages.StringField(1)
  creationTimestamp = _messages.StringField(2)
  description = _messages.StringField(3)
  endTime = _messages.StringField(4)
  error = _messages.MessageField('ErrorValue', 5)
  httpErrorMessage = _messages.StringField(6)
  httpErrorStatusCode = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  id = _messages.IntegerField(8, variant=_messages.Variant.UINT64)
  insertTime = _messages.StringField(9)
  instancesBulkInsertOperationMetadata = _messages.MessageField('InstancesBulkInsertOperationMetadata', 10)
  kind = _messages.StringField(11, default='compute#operation')
  name = _messages.StringField(12)
  operationGroupId = _messages.StringField(13)
  operationType = _messages.StringField(14)
  progress = _messages.IntegerField(15, variant=_messages.Variant.INT32)
  region = _messages.StringField(16)
  selfLink = _messages.StringField(17)
  setCommonInstanceMetadataOperationMetadata = _messages.MessageField('SetCommonInstanceMetadataOperationMetadata', 18)
  startTime = _messages.StringField(19)
  status = _messages.EnumField('StatusValueValuesEnum', 20)
  statusMessage = _messages.StringField(21)
  targetId = _messages.IntegerField(22, variant=_messages.Variant.UINT64)
  targetLink = _messages.StringField(23)
  user = _messages.StringField(24)
  warnings = _messages.MessageField('WarningsValueListEntry', 25, repeated=True)
  zone = _messages.StringField(26)


class OperationsListResponse(_messages.Message):
  r"""A response containing a partial list of operations and a page token used
  to build the next request if the request has been truncated.

  Fields:
    nextPageToken: Output only. A token used to continue a truncated list
      request.
    operations: Output only. Operations contained in this list response.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Options(_messages.Message):
  r"""Options allows customized resource handling by Deployment Manager.

  Fields:
    asyncOptions: Options regarding how to thread async requests.
    inputMappings: The mappings that apply for requests.
    validationOptions: Options for how to validate and process properties on a
      resource.
    virtualProperties: Additional properties block described as a jsonSchema,
      these properties will never be part of the json payload, but they can be
      consumed by InputMappings, this must be a valid json schema draft-04.
      The properties specified here will be decouple in a different section.
      This schema will be merged to the schema validation, and properties here
      will be extracted From the payload and consumed explicitly by
      InputMappings. ex: field1: type: string field2: type: number
  """

  asyncOptions = _messages.MessageField('AsyncOptions', 1, repeated=True)
  inputMappings = _messages.MessageField('InputMapping', 2, repeated=True)
  validationOptions = _messages.MessageField('ValidationOptions', 3)
  virtualProperties = _messages.StringField(4)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class PollingOptions(_messages.Message):
  r"""A PollingOptions object.

  Fields:
    diagnostics: An array of diagnostics to be collected by Deployment
      Manager, these diagnostics will be displayed to the user.
    failCondition: JsonPath expression that determines if the request failed.
    finishCondition: JsonPath expression that determines if the request is
      completed.
    pollingLink: JsonPath expression that evaluates to string, it indicates
      where to poll.
    targetLink: JsonPath expression, after polling is completed, indicates
      where to fetch the resource.
  """

  diagnostics = _messages.MessageField('Diagnostic', 1, repeated=True)
  failCondition = _messages.StringField(2)
  finishCondition = _messages.StringField(3)
  pollingLink = _messages.StringField(4)
  targetLink = _messages.StringField(5)


class Resource(_messages.Message):
  r"""A Resource object.

  Messages:
    WarningsValueListEntry: A WarningsValueListEntry object.

  Fields:
    accessControl: The Access Control Policy set on this resource.
    finalProperties: Output only. The evaluated properties of the resource
      with references expanded. Returned as serialized YAML.
    id: A string attribute.
    insertTime: Output only. Creation timestamp in RFC3339 text format.
    manifest: Output only. URL of the manifest representing the current
      configuration of this resource.
    name: Output only. The name of the resource as it appears in the YAML
      config.
    properties: Output only. The current properties of the resource before any
      references have been filled in. Returned as serialized YAML.
    type: Output only. The type of the resource, for example
      `compute.v1.instance`, or `cloudfunctions.v1beta1.function`.
    update: Output only. If Deployment Manager is currently updating or
      previewing an update to this resource, the updated configuration appears
      here.
    updateTime: Output only. Update timestamp in RFC3339 text format.
    url: Output only. The URL of the actual resource.
    warnings: Output only. If warning messages are generated during processing
      of this resource, this field will be populated.
  """

  class WarningsValueListEntry(_messages.Message):
    r"""A WarningsValueListEntry object.

    Enums:
      CodeValueValuesEnum: [Output Only] A warning code, if applicable. For
        example, Compute Engine returns NO_RESULTS_ON_PAGE if there are no
        results in the response.

    Messages:
      DataValueListEntry: A DataValueListEntry object.

    Fields:
      code: [Output Only] A warning code, if applicable. For example, Compute
        Engine returns NO_RESULTS_ON_PAGE if there are no results in the
        response.
      data: [Output Only] Metadata about this warning in key: value format.
        For example: "data": [ { "key": "scope", "value": "zones/us-east1-d" }
      message: [Output Only] A human-readable description of the warning code.
    """

    class CodeValueValuesEnum(_messages.Enum):
      r"""[Output Only] A warning code, if applicable. For example, Compute
      Engine returns NO_RESULTS_ON_PAGE if there are no results in the
      response.

      Values:
        DEPRECATED_RESOURCE_USED: A link to a deprecated resource was created.
        NO_RESULTS_ON_PAGE: No results are present on a particular list page.
        UNREACHABLE: A given scope cannot be reached.
        NEXT_HOP_ADDRESS_NOT_ASSIGNED: The route's nextHopIp address is not
          assigned to an instance on the network.
        NEXT_HOP_INSTANCE_NOT_FOUND: The route's nextHopInstance URL refers to
          an instance that does not exist.
        NEXT_HOP_INSTANCE_NOT_ON_NETWORK: The route's nextHopInstance URL
          refers to an instance that is not on the same network as the route.
        NEXT_HOP_CANNOT_IP_FORWARD: The route's next hop instance cannot ip
          forward.
        NEXT_HOP_NOT_RUNNING: The route's next hop instance does not have a
          status of RUNNING.
        INJECTED_KERNELS_DEPRECATED: The operation involved use of an injected
          kernel, which is deprecated.
        REQUIRED_TOS_AGREEMENT: The user attempted to use a resource that
          requires a TOS they have not accepted.
        DISK_SIZE_LARGER_THAN_IMAGE_SIZE: The user created a boot disk that is
          larger than image size.
        RESOURCE_NOT_DELETED: One or more of the resources set to auto-delete
          could not be deleted because they were in use.
        SINGLE_INSTANCE_PROPERTY_TEMPLATE: Instance template used in instance
          group manager is valid as such, but its application does not make a
          lot of sense, because it allows only single instance in instance
          group.
        NOT_CRITICAL_ERROR: Error which is not critical. We decided to
          continue the process despite the mentioned error.
        CLEANUP_FAILED: Warning about failed cleanup of transient changes made
          by a failed operation.
        FIELD_VALUE_OVERRIDEN: Warning that value of a field has been
          overridden. Deprecated unused field.
        RESOURCE_IN_USE_BY_OTHER_RESOURCE_WARNING: Warning that a resource is
          in use.
        MISSING_TYPE_DEPENDENCY: A resource depends on a missing type
        EXTERNAL_API_WARNING: Warning that is present in an external api call
        SCHEMA_VALIDATION_IGNORED: When a resource schema validation is
          ignored.
        UNDECLARED_PROPERTIES: When undeclared properties in the schema are
          present
        EXPERIMENTAL_TYPE_USED: When deploying and at least one of the
          resources has a type marked as experimental
        DEPRECATED_TYPE_USED: When deploying and at least one of the resources
          has a type marked as deprecated
        PARTIAL_SUCCESS: Success is reported, but some results may be missing
          due to errors
        LARGE_DEPLOYMENT_WARNING: When deploying a deployment with a
          exceedingly large number of resources
        NEXT_HOP_INSTANCE_HAS_NO_IPV6_INTERFACE: The route's nextHopInstance
          URL refers to an instance that does not have an ipv6 interface on
          the same network as the route.
        INVALID_HEALTH_CHECK_FOR_DYNAMIC_WIEGHTED_LB: A WEIGHTED_MAGLEV
          backend service is associated with a health check that is not of
          type HTTP/HTTPS/HTTP2.
        LIST_OVERHEAD_QUOTA_EXCEED: Resource can't be retrieved due to list
          overhead quota exceed which captures the amount of resources
          filtered out by user-defined list filter.
      """
      DEPRECATED_RESOURCE_USED = 0
      NO_RESULTS_ON_PAGE = 1
      UNREACHABLE = 2
      NEXT_HOP_ADDRESS_NOT_ASSIGNED = 3
      NEXT_HOP_INSTANCE_NOT_FOUND = 4
      NEXT_HOP_INSTANCE_NOT_ON_NETWORK = 5
      NEXT_HOP_CANNOT_IP_FORWARD = 6
      NEXT_HOP_NOT_RUNNING = 7
      INJECTED_KERNELS_DEPRECATED = 8
      REQUIRED_TOS_AGREEMENT = 9
      DISK_SIZE_LARGER_THAN_IMAGE_SIZE = 10
      RESOURCE_NOT_DELETED = 11
      SINGLE_INSTANCE_PROPERTY_TEMPLATE = 12
      NOT_CRITICAL_ERROR = 13
      CLEANUP_FAILED = 14
      FIELD_VALUE_OVERRIDEN = 15
      RESOURCE_IN_USE_BY_OTHER_RESOURCE_WARNING = 16
      MISSING_TYPE_DEPENDENCY = 17
      EXTERNAL_API_WARNING = 18
      SCHEMA_VALIDATION_IGNORED = 19
      UNDECLARED_PROPERTIES = 20
      EXPERIMENTAL_TYPE_USED = 21
      DEPRECATED_TYPE_USED = 22
      PARTIAL_SUCCESS = 23
      LARGE_DEPLOYMENT_WARNING = 24
      NEXT_HOP_INSTANCE_HAS_NO_IPV6_INTERFACE = 25
      INVALID_HEALTH_CHECK_FOR_DYNAMIC_WIEGHTED_LB = 26
      LIST_OVERHEAD_QUOTA_EXCEED = 27

    class DataValueListEntry(_messages.Message):
      r"""A DataValueListEntry object.

      Fields:
        key: [Output Only] A key that provides more detail on the warning
          being returned. For example, for warnings where there are no results
          in a list request for a particular zone, this key might be scope and
          the key value might be the zone name. Other examples might be a key
          indicating a deprecated resource and a suggested replacement, or a
          warning about invalid network settings (for example, if an instance
          attempts to perform IP forwarding but is not enabled for IP
          forwarding).
        value: [Output Only] A warning data value corresponding to the key.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    code = _messages.EnumField('CodeValueValuesEnum', 1)
    data = _messages.MessageField('DataValueListEntry', 2, repeated=True)
    message = _messages.StringField(3)

  accessControl = _messages.MessageField('ResourceAccessControl', 1)
  finalProperties = _messages.StringField(2)
  id = _messages.IntegerField(3, variant=_messages.Variant.UINT64)
  insertTime = _messages.StringField(4)
  manifest = _messages.StringField(5)
  name = _messages.StringField(6)
  properties = _messages.StringField(7)
  type = _messages.StringField(8)
  update = _messages.MessageField('ResourceUpdate', 9)
  updateTime = _messages.StringField(10)
  url = _messages.StringField(11)
  warnings = _messages.MessageField('WarningsValueListEntry', 12, repeated=True)


class ResourceAccessControl(_messages.Message):
  r"""The access controls set on the resource.

  Fields:
    gcpIamPolicy: The GCP IAM Policy to set on the resource.
  """

  gcpIamPolicy = _messages.StringField(1)


class ResourceUpdate(_messages.Message):
  r"""A ResourceUpdate object.

  Enums:
    IntentValueValuesEnum: Output only. The intent of the resource: `PREVIEW`,
      `UPDATE`, or `CANCEL`.
    StateValueValuesEnum: Output only. The state of the resource.

  Messages:
    ErrorValue: Output only. If errors are generated during update of the
      resource, this field will be populated.
    WarningsValueListEntry: A WarningsValueListEntry object.

  Fields:
    accessControl: The Access Control Policy to set on this resource after
      updating the resource itself.
    error: Output only. If errors are generated during update of the resource,
      this field will be populated.
    finalProperties: Output only. The expanded properties of the resource with
      reference values expanded. Returned as serialized YAML.
    intent: Output only. The intent of the resource: `PREVIEW`, `UPDATE`, or
      `CANCEL`.
    manifest: Output only. URL of the manifest representing the update
      configuration of this resource.
    properties: Output only. The set of updated properties for this resource,
      before references are expanded. Returned as serialized YAML.
    state: Output only. The state of the resource.
    warnings: Output only. If warning messages are generated during processing
      of this resource, this field will be populated.
  """

  class IntentValueValuesEnum(_messages.Enum):
    r"""Output only. The intent of the resource: `PREVIEW`, `UPDATE`, or
    `CANCEL`.

    Values:
      CREATE_OR_ACQUIRE: The resource is scheduled to be created, or if it
        already exists, acquired.
      DELETE: The resource is scheduled to be deleted.
      ACQUIRE: The resource is scheduled to be acquired.
      UPDATE: The resource is scheduled to be updated via the UPDATE method.
      ABANDON: The resource is scheduled to be abandoned.
      CREATE: The resource is scheduled to be created.
    """
    CREATE_OR_ACQUIRE = 0
    DELETE = 1
    ACQUIRE = 2
    UPDATE = 3
    ABANDON = 4
    CREATE = 5

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the resource.

    Values:
      PENDING: There are changes pending for this resource.
      IN_PROGRESS: The service is executing changes on the resource.
      IN_PREVIEW: The service is previewing changes on the resource.
      FAILED: The service has failed to change the resource.
      ABORTED: The service has aborted trying to change the resource.
    """
    PENDING = 0
    IN_PROGRESS = 1
    IN_PREVIEW = 2
    FAILED = 3
    ABORTED = 4

  class ErrorValue(_messages.Message):
    r"""Output only. If errors are generated during update of the resource,
    this field will be populated.

    Messages:
      ErrorsValueListEntry: A ErrorsValueListEntry object.

    Fields:
      errors: [Output Only] The array of errors encountered while processing
        this operation.
    """

    class ErrorsValueListEntry(_messages.Message):
      r"""A ErrorsValueListEntry object.

      Fields:
        code: [Output Only] The error type identifier for this error.
        location: [Output Only] Indicates the field in the request that caused
          the error. This property is optional.
        message: [Output Only] An optional, human-readable error message.
      """

      code = _messages.StringField(1)
      location = _messages.StringField(2)
      message = _messages.StringField(3)

    errors = _messages.MessageField('ErrorsValueListEntry', 1, repeated=True)

  class WarningsValueListEntry(_messages.Message):
    r"""A WarningsValueListEntry object.

    Enums:
      CodeValueValuesEnum: [Output Only] A warning code, if applicable. For
        example, Compute Engine returns NO_RESULTS_ON_PAGE if there are no
        results in the response.

    Messages:
      DataValueListEntry: A DataValueListEntry object.

    Fields:
      code: [Output Only] A warning code, if applicable. For example, Compute
        Engine returns NO_RESULTS_ON_PAGE if there are no results in the
        response.
      data: [Output Only] Metadata about this warning in key: value format.
        For example: "data": [ { "key": "scope", "value": "zones/us-east1-d" }
      message: [Output Only] A human-readable description of the warning code.
    """

    class CodeValueValuesEnum(_messages.Enum):
      r"""[Output Only] A warning code, if applicable. For example, Compute
      Engine returns NO_RESULTS_ON_PAGE if there are no results in the
      response.

      Values:
        DEPRECATED_RESOURCE_USED: A link to a deprecated resource was created.
        NO_RESULTS_ON_PAGE: No results are present on a particular list page.
        UNREACHABLE: A given scope cannot be reached.
        NEXT_HOP_ADDRESS_NOT_ASSIGNED: The route's nextHopIp address is not
          assigned to an instance on the network.
        NEXT_HOP_INSTANCE_NOT_FOUND: The route's nextHopInstance URL refers to
          an instance that does not exist.
        NEXT_HOP_INSTANCE_NOT_ON_NETWORK: The route's nextHopInstance URL
          refers to an instance that is not on the same network as the route.
        NEXT_HOP_CANNOT_IP_FORWARD: The route's next hop instance cannot ip
          forward.
        NEXT_HOP_NOT_RUNNING: The route's next hop instance does not have a
          status of RUNNING.
        INJECTED_KERNELS_DEPRECATED: The operation involved use of an injected
          kernel, which is deprecated.
        REQUIRED_TOS_AGREEMENT: The user attempted to use a resource that
          requires a TOS they have not accepted.
        DISK_SIZE_LARGER_THAN_IMAGE_SIZE: The user created a boot disk that is
          larger than image size.
        RESOURCE_NOT_DELETED: One or more of the resources set to auto-delete
          could not be deleted because they were in use.
        SINGLE_INSTANCE_PROPERTY_TEMPLATE: Instance template used in instance
          group manager is valid as such, but its application does not make a
          lot of sense, because it allows only single instance in instance
          group.
        NOT_CRITICAL_ERROR: Error which is not critical. We decided to
          continue the process despite the mentioned error.
        CLEANUP_FAILED: Warning about failed cleanup of transient changes made
          by a failed operation.
        FIELD_VALUE_OVERRIDEN: Warning that value of a field has been
          overridden. Deprecated unused field.
        RESOURCE_IN_USE_BY_OTHER_RESOURCE_WARNING: Warning that a resource is
          in use.
        MISSING_TYPE_DEPENDENCY: A resource depends on a missing type
        EXTERNAL_API_WARNING: Warning that is present in an external api call
        SCHEMA_VALIDATION_IGNORED: When a resource schema validation is
          ignored.
        UNDECLARED_PROPERTIES: When undeclared properties in the schema are
          present
        EXPERIMENTAL_TYPE_USED: When deploying and at least one of the
          resources has a type marked as experimental
        DEPRECATED_TYPE_USED: When deploying and at least one of the resources
          has a type marked as deprecated
        PARTIAL_SUCCESS: Success is reported, but some results may be missing
          due to errors
        LARGE_DEPLOYMENT_WARNING: When deploying a deployment with a
          exceedingly large number of resources
        NEXT_HOP_INSTANCE_HAS_NO_IPV6_INTERFACE: The route's nextHopInstance
          URL refers to an instance that does not have an ipv6 interface on
          the same network as the route.
        INVALID_HEALTH_CHECK_FOR_DYNAMIC_WIEGHTED_LB: A WEIGHTED_MAGLEV
          backend service is associated with a health check that is not of
          type HTTP/HTTPS/HTTP2.
        LIST_OVERHEAD_QUOTA_EXCEED: Resource can't be retrieved due to list
          overhead quota exceed which captures the amount of resources
          filtered out by user-defined list filter.
      """
      DEPRECATED_RESOURCE_USED = 0
      NO_RESULTS_ON_PAGE = 1
      UNREACHABLE = 2
      NEXT_HOP_ADDRESS_NOT_ASSIGNED = 3
      NEXT_HOP_INSTANCE_NOT_FOUND = 4
      NEXT_HOP_INSTANCE_NOT_ON_NETWORK = 5
      NEXT_HOP_CANNOT_IP_FORWARD = 6
      NEXT_HOP_NOT_RUNNING = 7
      INJECTED_KERNELS_DEPRECATED = 8
      REQUIRED_TOS_AGREEMENT = 9
      DISK_SIZE_LARGER_THAN_IMAGE_SIZE = 10
      RESOURCE_NOT_DELETED = 11
      SINGLE_INSTANCE_PROPERTY_TEMPLATE = 12
      NOT_CRITICAL_ERROR = 13
      CLEANUP_FAILED = 14
      FIELD_VALUE_OVERRIDEN = 15
      RESOURCE_IN_USE_BY_OTHER_RESOURCE_WARNING = 16
      MISSING_TYPE_DEPENDENCY = 17
      EXTERNAL_API_WARNING = 18
      SCHEMA_VALIDATION_IGNORED = 19
      UNDECLARED_PROPERTIES = 20
      EXPERIMENTAL_TYPE_USED = 21
      DEPRECATED_TYPE_USED = 22
      PARTIAL_SUCCESS = 23
      LARGE_DEPLOYMENT_WARNING = 24
      NEXT_HOP_INSTANCE_HAS_NO_IPV6_INTERFACE = 25
      INVALID_HEALTH_CHECK_FOR_DYNAMIC_WIEGHTED_LB = 26
      LIST_OVERHEAD_QUOTA_EXCEED = 27

    class DataValueListEntry(_messages.Message):
      r"""A DataValueListEntry object.

      Fields:
        key: [Output Only] A key that provides more detail on the warning
          being returned. For example, for warnings where there are no results
          in a list request for a particular zone, this key might be scope and
          the key value might be the zone name. Other examples might be a key
          indicating a deprecated resource and a suggested replacement, or a
          warning about invalid network settings (for example, if an instance
          attempts to perform IP forwarding but is not enabled for IP
          forwarding).
        value: [Output Only] A warning data value corresponding to the key.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    code = _messages.EnumField('CodeValueValuesEnum', 1)
    data = _messages.MessageField('DataValueListEntry', 2, repeated=True)
    message = _messages.StringField(3)

  accessControl = _messages.MessageField('ResourceAccessControl', 1)
  error = _messages.MessageField('ErrorValue', 2)
  finalProperties = _messages.StringField(3)
  intent = _messages.EnumField('IntentValueValuesEnum', 4)
  manifest = _messages.StringField(5)
  properties = _messages.StringField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  warnings = _messages.MessageField('WarningsValueListEntry', 8, repeated=True)


class ResourcesListResponse(_messages.Message):
  r"""A response containing a partial list of resources and a page token used
  to build the next request if the request has been truncated.

  Fields:
    nextPageToken: A token used to continue a truncated list request.
    resources: Resources contained in this list response.
  """

  nextPageToken = _messages.StringField(1)
  resources = _messages.MessageField('Resource', 2, repeated=True)


class ServiceAccount(_messages.Message):
  r"""Service Account used as a credential.

  Fields:
    email: The IAM service account email address like
      <EMAIL>
  """

  email = _messages.StringField(1)


class SetCommonInstanceMetadataOperationMetadata(_messages.Message):
  r"""A SetCommonInstanceMetadataOperationMetadata object.

  Messages:
    PerLocationOperationsValue: [Output Only] Status information per location
      (location name is key). Example key: zones/us-central1-a

  Fields:
    clientOperationId: [Output Only] The client operation id.
    perLocationOperations: [Output Only] Status information per location
      (location name is key). Example key: zones/us-central1-a
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PerLocationOperationsValue(_messages.Message):
    r"""[Output Only] Status information per location (location name is key).
    Example key: zones/us-central1-a

    Messages:
      AdditionalProperty: An additional property for a
        PerLocationOperationsValue object.

    Fields:
      additionalProperties: Additional properties of type
        PerLocationOperationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PerLocationOperationsValue object.

      Fields:
        key: Name of the additional property.
        value: A
          SetCommonInstanceMetadataOperationMetadataPerLocationOperationInfo
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('SetCommonInstanceMetadataOperationMetadataPerLocationOperationInfo', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  clientOperationId = _messages.StringField(1)
  perLocationOperations = _messages.MessageField('PerLocationOperationsValue', 2)


class SetCommonInstanceMetadataOperationMetadataPerLocationOperationInfo(_messages.Message):
  r"""A SetCommonInstanceMetadataOperationMetadataPerLocationOperationInfo
  object.

  Enums:
    StateValueValuesEnum: [Output Only] Status of the action, which can be one
      of the following: `PROPAGATING`, `PROPAGATED`, `ABANDONED`, `FAILED`, or
      `DONE`.

  Fields:
    error: [Output Only] If state is `ABANDONED` or `FAILED`, this field is
      populated.
    state: [Output Only] Status of the action, which can be one of the
      following: `PROPAGATING`, `PROPAGATED`, `ABANDONED`, `FAILED`, or
      `DONE`.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""[Output Only] Status of the action, which can be one of the following:
    `PROPAGATING`, `PROPAGATED`, `ABANDONED`, `FAILED`, or `DONE`.

    Values:
      UNSPECIFIED: <no description>
      PROPAGATING: Operation is not yet confirmed to have been created in the
        location.
      PROPAGATED: Operation is confirmed to be in the location.
      ABANDONED: Operation not tracked in this location e.g. zone is marked as
        DOWN.
      FAILED: Operation is in an error state.
      DONE: Operation has completed successfully.
    """
    UNSPECIFIED = 0
    PROPAGATING = 1
    PROPAGATED = 2
    ABANDONED = 3
    FAILED = 4
    DONE = 5

  error = _messages.MessageField('Status', 1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TargetConfiguration(_messages.Message):
  r"""A TargetConfiguration object.

  Fields:
    config: The configuration to use for this deployment.
    imports: Specifies any files to import for this configuration. This can be
      used to import templates or other files. For example, you might import a
      text file in order to use the file in a template.
  """

  config = _messages.MessageField('ConfigFile', 1)
  imports = _messages.MessageField('ImportFile', 2, repeated=True)


class TemplateContents(_messages.Message):
  r"""Files that make up the template contents of a template type.

  Enums:
    InterpreterValueValuesEnum: Which interpreter (python or jinja) should be
      used during expansion.

  Fields:
    imports: Import files referenced by the main template.
    interpreter: Which interpreter (python or jinja) should be used during
      expansion.
    mainTemplate: The filename of the mainTemplate
    schema: The contents of the template schema.
    template: The contents of the main template file.
  """

  class InterpreterValueValuesEnum(_messages.Enum):
    r"""Which interpreter (python or jinja) should be used during expansion.

    Values:
      UNKNOWN_INTERPRETER: <no description>
      PYTHON: <no description>
      JINJA: <no description>
    """
    UNKNOWN_INTERPRETER = 0
    PYTHON = 1
    JINJA = 2

  imports = _messages.MessageField('ImportFile', 1, repeated=True)
  interpreter = _messages.EnumField('InterpreterValueValuesEnum', 2)
  mainTemplate = _messages.StringField(3)
  schema = _messages.StringField(4)
  template = _messages.StringField(5)


class TestPermissionsRequest(_messages.Message):
  r"""A TestPermissionsRequest object.

  Fields:
    permissions: The set of permissions to check for the 'resource'.
      Permissions with wildcards (such as '*' or 'storage.*') are not allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TestPermissionsResponse(_messages.Message):
  r"""A TestPermissionsResponse object.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class Type(_messages.Message):
  r"""A resource type supported by Deployment Manager.

  Fields:
    base: Base Type (configurable service) that backs this Type.
    description: An optional textual description of the resource; provided by
      the client when the resource is created.
    id: A string attribute.
    insertTime: Output only. Creation timestamp in RFC3339 text format.
    labels: Map of One Platform labels; provided by the client when the
      resource is created or updated. Specifically: Label keys must be between
      1 and 63 characters long and must conform to the following regular
      expression: `[a-z]([-a-z0-9]*[a-z0-9])?` Label values must be between 0
      and 63 characters long and must conform to the regular expression
      `([a-z]([-a-z0-9]*[a-z0-9])?)?`.
    name: Name of the type.
    operation: Output only. The Operation that most recently ran, or is
      currently running, on this type.
    selfLink: Output only. Server defined URL for the resource.
  """

  base = _messages.MessageField('BaseType', 1)
  description = _messages.StringField(2)
  id = _messages.IntegerField(3, variant=_messages.Variant.UINT64)
  insertTime = _messages.StringField(4)
  labels = _messages.MessageField('TypeLabelEntry', 5, repeated=True)
  name = _messages.StringField(6)
  operation = _messages.MessageField('Operation', 7)
  selfLink = _messages.StringField(8)


class TypeInfo(_messages.Message):
  r"""Type Information. Contains detailed information about a composite type,
  base type, or base type with specific collection.

  Fields:
    description: The description of the type.
    documentationLink: For swagger 2.0 externalDocs field will be used. For
      swagger 1.2 this field will be empty.
    kind: Output only. Type of the output. Always `deploymentManager#TypeInfo`
      for TypeInfo.
    name: The base type or composite type name.
    schema: For base types with a collection, we return a schema and
      documentation link For template types, we return only a schema
    selfLink: Output only. Self link for the type provider.
    title: The title on the API descriptor URL provided.
  """

  description = _messages.StringField(1)
  documentationLink = _messages.StringField(2)
  kind = _messages.StringField(3)
  name = _messages.StringField(4)
  schema = _messages.MessageField('TypeInfoSchemaInfo', 5)
  selfLink = _messages.StringField(6)
  title = _messages.StringField(7)


class TypeInfoSchemaInfo(_messages.Message):
  r"""A TypeInfoSchemaInfo object.

  Fields:
    input: The properties that this composite type or base type collection
      accept as input, represented as a json blob, format is: JSON Schema
      Draft V4
    output: The properties that this composite type or base type collection
      exposes as output, these properties can be used for references,
      represented as json blob, format is: JSON Schema Draft V4
  """

  input = _messages.StringField(1)
  output = _messages.StringField(2)


class TypeLabelEntry(_messages.Message):
  r"""Label object for Types

  Fields:
    key: Key of the label
    value: Value of the label
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class TypeProvider(_messages.Message):
  r"""A type provider that describes a service-backed Type.

  Fields:
    collectionOverrides: Allows resource handling overrides for specific
      collections
    credential: Credential used when interacting with this type.
    customCertificateAuthorityRoots: List of up to 2 custom certificate
      authority roots to use for TLS authentication when making calls on
      behalf of this type provider. If set, TLS authentication will
      exclusively use these roots instead of relying on publicly trusted
      certificate authorities when validating TLS certificate authenticity.
      The certificates must be in base64-encoded PEM format. The maximum size
      of each certificate must not exceed 10KB.
    description: An optional textual description of the resource; provided by
      the client when the resource is created.
    descriptorUrl: Descriptor Url for the this type provider.
    id: Output only. Unique identifier for the resource defined by the server.
    insertTime: Output only. Creation timestamp in RFC3339 text format.
    labels: Map of One Platform labels; provided by the client when the
      resource is created or updated. Specifically: Label keys must be between
      1 and 63 characters long and must conform to the following regular
      expression: `[a-z]([-a-z0-9]*[a-z0-9])?` Label values must be between 0
      and 63 characters long and must conform to the regular expression
      `([a-z]([-a-z0-9]*[a-z0-9])?)?`
    name: Name of the resource; provided by the client when the resource is
      created. The name must be 1-63 characters long, and comply with RFC1035.
      Specifically, the name must be 1-63 characters long and match the
      regular expression `[a-z]([-a-z0-9]*[a-z0-9])?` which means the first
      character must be a lowercase letter, and all following characters must
      be a dash, lowercase letter, or digit, except the last character, which
      cannot be a dash.
    operation: Output only. The Operation that most recently ran, or is
      currently running, on this type provider.
    options: Options to apply when handling any resources in this service.
    selfLink: Output only. Self link for the type provider.
  """

  collectionOverrides = _messages.MessageField('CollectionOverride', 1, repeated=True)
  credential = _messages.MessageField('Credential', 2)
  customCertificateAuthorityRoots = _messages.StringField(3, repeated=True)
  description = _messages.StringField(4)
  descriptorUrl = _messages.StringField(5)
  id = _messages.IntegerField(6, variant=_messages.Variant.UINT64)
  insertTime = _messages.StringField(7)
  labels = _messages.MessageField('TypeProviderLabelEntry', 8, repeated=True)
  name = _messages.StringField(9)
  operation = _messages.MessageField('Operation', 10)
  options = _messages.MessageField('Options', 11)
  selfLink = _messages.StringField(12)


class TypeProviderLabelEntry(_messages.Message):
  r"""Label object for TypeProviders

  Fields:
    key: Key of the label
    value: Value of the label
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class TypeProvidersListResponse(_messages.Message):
  r"""A response that returns all Type Providers supported by Deployment
  Manager

  Fields:
    nextPageToken: A token used to continue a truncated list request.
    typeProviders: Output only. A list of resource type providers supported by
      Deployment Manager.
  """

  nextPageToken = _messages.StringField(1)
  typeProviders = _messages.MessageField('TypeProvider', 2, repeated=True)


class TypeProvidersListTypesResponse(_messages.Message):
  r"""A TypeProvidersListTypesResponse object.

  Fields:
    nextPageToken: A token used to continue a truncated list request.
    types: Output only. A list of resource type info.
  """

  nextPageToken = _messages.StringField(1)
  types = _messages.MessageField('TypeInfo', 2, repeated=True)


class TypesListResponse(_messages.Message):
  r"""A response that returns all Types supported by Deployment Manager

  Fields:
    nextPageToken: A token used to continue a truncated list request.
    types: Output only. A list of resource types supported by Deployment
      Manager.
  """

  nextPageToken = _messages.StringField(1)
  types = _messages.MessageField('Type', 2, repeated=True)


class ValidationOptions(_messages.Message):
  r"""Options for how to validate and process properties on a resource.

  Enums:
    SchemaValidationValueValuesEnum: Customize how deployment manager will
      validate the resource against schema errors.
    UndeclaredPropertiesValueValuesEnum: Specify what to do with extra
      properties when executing a request.

  Fields:
    schemaValidation: Customize how deployment manager will validate the
      resource against schema errors.
    undeclaredProperties: Specify what to do with extra properties when
      executing a request.
  """

  class SchemaValidationValueValuesEnum(_messages.Enum):
    r"""Customize how deployment manager will validate the resource against
    schema errors.

    Values:
      UNKNOWN: <no description>
      IGNORE: Ignore schema failures.
      IGNORE_WITH_WARNINGS: Ignore schema failures but display them as
        warnings.
      FAIL: Fail the resource if the schema is not valid, this is the default
        behavior.
    """
    UNKNOWN = 0
    IGNORE = 1
    IGNORE_WITH_WARNINGS = 2
    FAIL = 3

  class UndeclaredPropertiesValueValuesEnum(_messages.Enum):
    r"""Specify what to do with extra properties when executing a request.

    Values:
      UNKNOWN: <no description>
      INCLUDE: Always include even if not present on discovery doc.
      IGNORE: Always ignore if not present on discovery doc.
      INCLUDE_WITH_WARNINGS: Include on request, but emit a warning.
      IGNORE_WITH_WARNINGS: Ignore properties, but emit a warning.
      FAIL: Always fail if undeclared properties are present.
    """
    UNKNOWN = 0
    INCLUDE = 1
    IGNORE = 2
    INCLUDE_WITH_WARNINGS = 3
    IGNORE_WITH_WARNINGS = 4
    FAIL = 5

  schemaValidation = _messages.EnumField('SchemaValidationValueValuesEnum', 1)
  undeclaredProperties = _messages.EnumField('UndeclaredPropertiesValueValuesEnum', 2)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

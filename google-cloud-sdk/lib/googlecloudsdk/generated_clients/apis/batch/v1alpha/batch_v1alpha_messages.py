"""Generated message classes for batch version v1alpha.

An API to manage the running of batch jobs on Google Cloud Platform.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'batch'


class Accelerator(_messages.Message):
  r"""Accelerator describes Compute Engine accelerators to be attached to the
  VM.

  Fields:
    count: The number of accelerators of this type.
    driverVersion: Optional. The NVIDIA GPU driver version that should be
      installed for this type. You can define the specific driver version such
      as "470.103.01", following the driver version requirements in
      https://cloud.google.com/compute/docs/gpus/install-drivers-gpu#minimum-
      driver. Batch will install the specific accelerator driver if qualified.
    installGpuDrivers: Deprecated: please use instances[0].install_gpu_drivers
      instead.
    type: The accelerator type. For example, "nvidia-tesla-t4". See `gcloud
      compute accelerator-types list`.
  """

  count = _messages.IntegerField(1)
  driverVersion = _messages.StringField(2)
  installGpuDrivers = _messages.BooleanField(3)
  type = _messages.StringField(4)


class ActionCondition(_messages.Message):
  r"""Conditions for actions to deal with task failures.

  Fields:
    exitCodes: Exit codes of a task execution. If there are more than 1 exit
      codes, when task executes with any of the exit code in the list, the
      condition is met and the action will be executed.
  """

  exitCodes = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)


class AllocationPolicy(_messages.Message):
  r"""A Job's resource allocation policy describes when, where, and how
  compute resources should be allocated for the Job.

  Enums:
    ProvisioningModelsValueListEntryValuesEnum:

  Messages:
    LabelsValue: Labels applied to all VM instances and other resources
      created by AllocationPolicy. Labels could be user provided or system
      generated. You can assign up to 64 labels. [Google Compute Engine label
      restrictions](https://cloud.google.com/compute/docs/labeling-
      resources#restrictions) apply. Label names that start with "goog-" or
      "google-" are reserved.

  Fields:
    instance: Deprecated: please use instances[0].policy instead.
    instanceTemplates: Deprecated: please use instances[0].template instead.
    instances: Describe instances that can be created by this
      AllocationPolicy. Only instances[0] is supported now.
    labels: Labels applied to all VM instances and other resources created by
      AllocationPolicy. Labels could be user provided or system generated. You
      can assign up to 64 labels. [Google Compute Engine label
      restrictions](https://cloud.google.com/compute/docs/labeling-
      resources#restrictions) apply. Label names that start with "goog-" or
      "google-" are reserved.
    location: Location where compute resources should be allocated for the
      Job.
    network: The network policy.
    placement: The placement policy.
    provisioningModels: Deprecated: please use
      instances[0].policy.provisioning_model instead.
    serviceAccount: Service account that VMs will run as.
    serviceAccountEmail: Deprecated: please use service_account instead.
  """

  class ProvisioningModelsValueListEntryValuesEnum(_messages.Enum):
    r"""ProvisioningModelsValueListEntryValuesEnum enum type.

    Values:
      PROVISIONING_MODEL_UNSPECIFIED: Unspecified.
      STANDARD: Standard VM.
      SPOT: SPOT VM.
      PREEMPTIBLE: Preemptible VM (PVM). Above SPOT VM is the preferable model
        for preemptible VM instances: the old preemptible VM model (indicated
        by this field) is the older model, and has been migrated to use the
        SPOT model as the underlying technology. This old model will still be
        supported.
    """
    PROVISIONING_MODEL_UNSPECIFIED = 0
    STANDARD = 1
    SPOT = 2
    PREEMPTIBLE = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels applied to all VM instances and other resources created by
    AllocationPolicy. Labels could be user provided or system generated. You
    can assign up to 64 labels. [Google Compute Engine label
    restrictions](https://cloud.google.com/compute/docs/labeling-
    resources#restrictions) apply. Label names that start with "goog-" or
    "google-" are reserved.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  instance = _messages.MessageField('InstancePolicy', 1)
  instanceTemplates = _messages.StringField(2, repeated=True)
  instances = _messages.MessageField('InstancePolicyOrTemplate', 3, repeated=True)
  labels = _messages.MessageField('LabelsValue', 4)
  location = _messages.MessageField('LocationPolicy', 5)
  network = _messages.MessageField('NetworkPolicy', 6)
  placement = _messages.MessageField('PlacementPolicy', 7)
  provisioningModels = _messages.EnumField('ProvisioningModelsValueListEntryValuesEnum', 8, repeated=True)
  serviceAccount = _messages.MessageField('ServiceAccount', 9)
  serviceAccountEmail = _messages.StringField(10)


class AttachedDisk(_messages.Message):
  r"""A new or an existing persistent disk (PD) or a local ssd attached to a
  VM instance.

  Fields:
    deviceName: Device name that the guest operating system will see. It is
      used by Runnable.volumes field to mount disks. So please specify the
      device_name if you want Batch to help mount the disk, and it should
      match the device_name field in volumes.
    existingDisk: Name of an existing PD.
    newDisk: A Disk attribute.
  """

  deviceName = _messages.StringField(1)
  existingDisk = _messages.StringField(2)
  newDisk = _messages.MessageField('Disk', 3)


class Barrier(_messages.Message):
  r"""Barrier runnable blocks until all tasks in a taskgroup reach it.

  Fields:
    name: Barriers are identified by their index in runnable list. Names are
      not required, but if present should be an identifier.
  """

  name = _messages.StringField(1)


class BatchProjectsLocationsGetRequest(_messages.Message):
  r"""A BatchProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class BatchProjectsLocationsJobsCreateRequest(_messages.Message):
  r"""A BatchProjectsLocationsJobsCreateRequest object.

  Fields:
    job: A Job resource to be passed as the request body.
    jobId: ID used to uniquely identify the Job within its parent scope. This
      field should contain at most 63 characters and must start with lowercase
      characters. Only lowercase characters, numbers and '-' are accepted. The
      '-' character cannot be the first or the last one. A system generated ID
      will be used if the field is not set. The job.name field in the request
      will be ignored and the created resource name of the Job will be
      "{parent}/jobs/{job_id}".
    parent: Required. The parent resource name where the Job will be created.
      Pattern: "projects/{project}/locations/{location}"
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  job = _messages.MessageField('Job', 1)
  jobId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class BatchProjectsLocationsJobsDeleteRequest(_messages.Message):
  r"""A BatchProjectsLocationsJobsDeleteRequest object.

  Fields:
    name: Job name.
    reason: Optional. Reason for this deletion.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  reason = _messages.StringField(2)
  requestId = _messages.StringField(3)


class BatchProjectsLocationsJobsGetRequest(_messages.Message):
  r"""A BatchProjectsLocationsJobsGetRequest object.

  Fields:
    name: Required. Job name.
  """

  name = _messages.StringField(1, required=True)


class BatchProjectsLocationsJobsListRequest(_messages.Message):
  r"""A BatchProjectsLocationsJobsListRequest object.

  Fields:
    filter: List filter.
    orderBy: Optional. Sort results. Supported are "name", "name desc",
      "create_time", and "create_time desc".
    pageSize: Page size.
    pageToken: Page token.
    parent: Parent path.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BatchProjectsLocationsJobsTaskGroupsTasksGetRequest(_messages.Message):
  r"""A BatchProjectsLocationsJobsTaskGroupsTasksGetRequest object.

  Fields:
    name: Required. Task name.
  """

  name = _messages.StringField(1, required=True)


class BatchProjectsLocationsJobsTaskGroupsTasksListRequest(_messages.Message):
  r"""A BatchProjectsLocationsJobsTaskGroupsTasksListRequest object.

  Fields:
    filter: Task filter, null filter matches all Tasks. Filter string should
      be of the format State=TaskStatus.State e.g. State=RUNNING
    orderBy: Not implemented.
    pageSize: Page size.
    pageToken: Page token.
    parent: Required. Name of a TaskGroup from which Tasks are being
      requested. Pattern: "projects/{project}/locations/{location}/jobs/{job}/
      taskGroups/{task_group}"
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BatchProjectsLocationsListRequest(_messages.Message):
  r"""A BatchProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class BatchProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A BatchProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class BatchProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A BatchProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class BatchProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A BatchProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class BatchProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A BatchProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class ComputeResource(_messages.Message):
  r"""Compute resource requirements. ComputeResource defines the amount of
  resources required for each task. Make sure your tasks have enough resources
  to successfully run. If you also define the types of resources for a job to
  use with the [InstancePolicyOrTemplate](https://cloud.google.com/batch/docs/
  reference/rest/v1/projects.locations.jobs#instancepolicyortemplate) field,
  make sure both fields are compatible with each other.

  Fields:
    bootDiskMib: Extra boot disk size in MiB for each task.
    cpuMilli: The milliCPU count. `cpuMilli` defines the amount of CPU
      resources per task in milliCPU units. For example, `1000` corresponds to
      1 vCPU per task. If undefined, the default value is `2000`. If you also
      define the VM's machine type using the `machineType` in [InstancePolicy]
      (https://cloud.google.com/batch/docs/reference/rest/v1/projects.location
      s.jobs#instancepolicy) field or inside the `instanceTemplate` in the [In
      stancePolicyOrTemplate](https://cloud.google.com/batch/docs/reference/re
      st/v1/projects.locations.jobs#instancepolicyortemplate) field, make sure
      the CPU resources for both fields are compatible with each other and
      with how many tasks you want to allow to run on the same VM at the same
      time. For example, if you specify the `n2-standard-2` machine type,
      which has 2 vCPUs each, you are recommended to set `cpuMilli` no more
      than `2000`, or you are recommended to run two tasks on the same VM if
      you set `cpuMilli` to `1000` or less.
    gpuCount: The GPU count. Not yet implemented.
    memoryMib: Memory in MiB. `memoryMib` defines the amount of memory per
      task in MiB units. If undefined, the default value is `2000`. If you
      also define the VM's machine type using the `machineType` in [InstancePo
      licy](https://cloud.google.com/batch/docs/reference/rest/v1/projects.loc
      ations.jobs#instancepolicy) field or inside the `instanceTemplate` in
      the [InstancePolicyOrTemplate](https://cloud.google.com/batch/docs/refer
      ence/rest/v1/projects.locations.jobs#instancepolicyortemplate) field,
      make sure the memory resources for both fields are compatible with each
      other and with how many tasks you want to allow to run on the same VM at
      the same time. For example, if you specify the `n2-standard-2` machine
      type, which has 8 GiB each, you are recommended to set `memoryMib` to no
      more than `8192`, or you are recommended to run two tasks on the same VM
      if you set `memoryMib` to `4096` or less.
  """

  bootDiskMib = _messages.IntegerField(1)
  cpuMilli = _messages.IntegerField(2)
  gpuCount = _messages.IntegerField(3)
  memoryMib = _messages.IntegerField(4)


class Container(_messages.Message):
  r"""Container runnable.

  Fields:
    blockExternalNetwork: If set to true, external network access to and from
      container will be blocked, containers that are with
      block_external_network as true can still communicate with each other,
      network cannot be specified in the `container.options` field.
    commands: Overrides the `CMD` specified in the container. If there is an
      ENTRYPOINT (either in the container image or with the entrypoint field
      below) then commands are appended as arguments to the ENTRYPOINT.
    enableImageStreaming: Optional. Not yet implemented. If set to true,
      container will run with Image streaming. The container runtime will be
      changed to containerd instead of docker. Currently, only imageUri,
      commands, entrypoint and volumes are supported and any other fields will
      be ignored.
    entrypoint: Overrides the `ENTRYPOINT` specified in the container.
    imageUri: The URI to pull the container image from.
    options: Arbitrary additional options to include in the "docker run"
      command when running this container, e.g. "--network host".
    password: Optional password for logging in to a docker registry. If
      password matches `projects/*/secrets/*/versions/*` then Batch will read
      the password from the Secret Manager;
    username: Optional username for logging in to a docker registry. If
      username matches `projects/*/secrets/*/versions/*` then Batch will read
      the username from the Secret Manager.
    volumes: Volumes to mount (bind mount) from the host machine files or
      directories into the container, formatted to match docker run's --volume
      option, e.g. /foo:/bar, or /foo:/bar:ro
  """

  blockExternalNetwork = _messages.BooleanField(1)
  commands = _messages.StringField(2, repeated=True)
  enableImageStreaming = _messages.BooleanField(3)
  entrypoint = _messages.StringField(4)
  imageUri = _messages.StringField(5)
  options = _messages.StringField(6)
  password = _messages.StringField(7)
  username = _messages.StringField(8)
  volumes = _messages.StringField(9, repeated=True)


class Disk(_messages.Message):
  r"""A new persistent disk or a local ssd. A VM can only have one local SSD
  setting but multiple local SSD partitions. See
  https://cloud.google.com/compute/docs/disks#pdspecs and
  https://cloud.google.com/compute/docs/disks#localssds.

  Fields:
    diskInterface: Local SSDs are available through both "SCSI" and "NVMe"
      interfaces. If not indicated, "NVMe" will be the default one for local
      ssds. We only support "SCSI" for persistent disks now.
    image: URL for a VM image to use as the data source for this disk. For
      example, the following are all valid URLs: * Specify the image by its
      family name: projects/{project}/global/images/family/{image_family} *
      Specify the image version:
      projects/{project}/global/images/{image_version} You can also use Batch
      customized image in short names. The following image values are
      supported for a boot disk: * `batch-debian`: use Batch Debian images. *
      `batch-centos`: use Batch CentOS images. * `batch-cos`: use Batch
      Container-Optimized images. * `batch-hpc-centos`: use Batch HPC CentOS
      images.
    sizeGb: Disk size in GB. For persistent disk, this field is ignored if
      `data_source` is `image` or `snapshot`. For local SSD, size_gb should be
      a multiple of 375GB, otherwise, the final size will be the next greater
      multiple of 375 GB. For boot disk, Batch will calculate the boot disk
      size based on source image and task requirements if you do not speicify
      the size. If both this field and the boot_disk_mib field in task spec's
      compute_resource are defined, Batch will only honor this field.
    snapshot: Name of a snapshot used as the data source. Snapshot is not
      supported as boot disk now.
    type: Disk type as shown in `gcloud compute disk-types list`. For example,
      local SSD uses type "local-ssd". Persistent disks and boot disks use
      "pd-balanced", "pd-extreme", "pd-ssd" or "pd-standard".
  """

  diskInterface = _messages.StringField(1)
  image = _messages.StringField(2)
  sizeGb = _messages.IntegerField(3)
  snapshot = _messages.StringField(4)
  type = _messages.StringField(5)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Environment(_messages.Message):
  r"""An Environment describes a collection of environment variables to set
  when executing Tasks.

  Messages:
    SecretVariablesValue: A map of environment variable names to Secret
      Manager secret names. The VM will access the named secrets to set the
      value of each environment variable.
    VariablesValue: A map of environment variable names to values.

  Fields:
    encryptedVariables: An encrypted JSON dictionary where the key/value pairs
      correspond to environment variable names and their values.
    secretVariables: A map of environment variable names to Secret Manager
      secret names. The VM will access the named secrets to set the value of
      each environment variable.
    variables: A map of environment variable names to values.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SecretVariablesValue(_messages.Message):
    r"""A map of environment variable names to Secret Manager secret names.
    The VM will access the named secrets to set the value of each environment
    variable.

    Messages:
      AdditionalProperty: An additional property for a SecretVariablesValue
        object.

    Fields:
      additionalProperties: Additional properties of type SecretVariablesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SecretVariablesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class VariablesValue(_messages.Message):
    r"""A map of environment variable names to values.

    Messages:
      AdditionalProperty: An additional property for a VariablesValue object.

    Fields:
      additionalProperties: Additional properties of type VariablesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a VariablesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  encryptedVariables = _messages.MessageField('KMSEnvMap', 1)
  secretVariables = _messages.MessageField('SecretVariablesValue', 2)
  variables = _messages.MessageField('VariablesValue', 3)


class GCS(_messages.Message):
  r"""Represents a Google Cloud Storage volume.

  Fields:
    remotePath: Remote path, either a bucket name or a subdirectory of a
      bucket, e.g.: bucket_name, bucket_name/subdirectory/
  """

  remotePath = _messages.StringField(1)


class InstancePolicy(_messages.Message):
  r"""InstancePolicy describes an instance type and resources attached to each
  VM created by this InstancePolicy.

  Enums:
    ProvisioningModelValueValuesEnum: The provisioning model.

  Fields:
    accelerators: The accelerators attached to each VM instance.
    allowedMachineTypes: Deprecated: please use machine_type instead.
    bootDisk: Boot disk to be created and attached to each VM by this
      InstancePolicy. Boot disk will be deleted when the VM is deleted. Batch
      API now only supports booting from image.
    disks: Non-boot disks to be attached for each VM created by this
      InstancePolicy. New disks will be deleted when the VM is deleted. A non-
      boot disk is a disk that can be of a device with a file system or a raw
      storage drive that is not ready for data storage and accessing.
    machineType: The Compute Engine machine type.
    minCpuPlatform: The minimum CPU platform. See
      https://cloud.google.com/compute/docs/instances/specify-min-cpu-
      platform.
    provisioningModel: The provisioning model.
    reservation: If specified, VMs will consume only the specified
      reservation. If not specified (default), VMs will consume any applicable
      reservation.
  """

  class ProvisioningModelValueValuesEnum(_messages.Enum):
    r"""The provisioning model.

    Values:
      PROVISIONING_MODEL_UNSPECIFIED: Unspecified.
      STANDARD: Standard VM.
      SPOT: SPOT VM.
      PREEMPTIBLE: Preemptible VM (PVM). Above SPOT VM is the preferable model
        for preemptible VM instances: the old preemptible VM model (indicated
        by this field) is the older model, and has been migrated to use the
        SPOT model as the underlying technology. This old model will still be
        supported.
    """
    PROVISIONING_MODEL_UNSPECIFIED = 0
    STANDARD = 1
    SPOT = 2
    PREEMPTIBLE = 3

  accelerators = _messages.MessageField('Accelerator', 1, repeated=True)
  allowedMachineTypes = _messages.StringField(2, repeated=True)
  bootDisk = _messages.MessageField('Disk', 3)
  disks = _messages.MessageField('AttachedDisk', 4, repeated=True)
  machineType = _messages.StringField(5)
  minCpuPlatform = _messages.StringField(6)
  provisioningModel = _messages.EnumField('ProvisioningModelValueValuesEnum', 7)
  reservation = _messages.StringField(8)


class InstancePolicyOrTemplate(_messages.Message):
  r"""InstancePolicyOrTemplate lets you define the type of resources to use
  for this job either with an InstancePolicy or an instance template. If
  undefined, Batch picks the type of VM to use and doesn't include optional VM
  resources such as GPUs and extra disks.

  Fields:
    installGpuDrivers: Set this field true if users want Batch to help fetch
      drivers from a third party location and install them for GPUs specified
      in policy.accelerators or instance_template on their behalf. Default is
      false. For Container-Optimized Image cases, Batch will install the
      accelerator driver following milestones of
      https://cloud.google.com/container-optimized-os/docs/release-notes. For
      non Container-Optimized Image cases, following
      https://github.com/GoogleCloudPlatform/compute-gpu-
      installation/blob/main/linux/install_gpu_driver.py.
    instanceTemplate: Name of an instance template used to create VMs. Named
      the field as 'instance_template' instead of 'template' to avoid c++
      keyword conflict.
    policy: InstancePolicy.
  """

  installGpuDrivers = _messages.BooleanField(1)
  instanceTemplate = _messages.StringField(2)
  policy = _messages.MessageField('InstancePolicy', 3)


class InstanceStatus(_messages.Message):
  r"""VM instance status.

  Enums:
    ProvisioningModelValueValuesEnum: The VM instance provisioning model.

  Fields:
    bootDisk: The VM boot disk.
    machineType: The Compute Engine machine type.
    provisioningModel: The VM instance provisioning model.
    taskPack: The max number of tasks can be assigned to this instance type.
  """

  class ProvisioningModelValueValuesEnum(_messages.Enum):
    r"""The VM instance provisioning model.

    Values:
      PROVISIONING_MODEL_UNSPECIFIED: Unspecified.
      STANDARD: Standard VM.
      SPOT: SPOT VM.
      PREEMPTIBLE: Preemptible VM (PVM). Above SPOT VM is the preferable model
        for preemptible VM instances: the old preemptible VM model (indicated
        by this field) is the older model, and has been migrated to use the
        SPOT model as the underlying technology. This old model will still be
        supported.
    """
    PROVISIONING_MODEL_UNSPECIFIED = 0
    STANDARD = 1
    SPOT = 2
    PREEMPTIBLE = 3

  bootDisk = _messages.MessageField('Disk', 1)
  machineType = _messages.StringField(2)
  provisioningModel = _messages.EnumField('ProvisioningModelValueValuesEnum', 3)
  taskPack = _messages.IntegerField(4)


class Job(_messages.Message):
  r"""The Cloud Batch Job description.

  Enums:
    SchedulingPolicyValueValuesEnum: Scheduling policy for TaskGroups in the
      job.

  Messages:
    LabelsValue: Labels for the Job. Labels could be user provided or system
      generated. For example, "labels": { "department": "finance",
      "environment": "test" } You can assign up to 64 labels. [Google Compute
      Engine label
      restrictions](https://cloud.google.com/compute/docs/labeling-
      resources#restrictions) apply. Label names that start with "goog-" or
      "google-" are reserved.

  Fields:
    allocationPolicy: Compute resource allocation for all TaskGroups in the
      Job.
    createTime: Output only. When the Job was created.
    dependencies: At least one of the dependencies must be satisfied before
      the Job is scheduled to run. Only one JobDependency is supported now.
      Not yet implemented.
    labels: Labels for the Job. Labels could be user provided or system
      generated. For example, "labels": { "department": "finance",
      "environment": "test" } You can assign up to 64 labels. [Google Compute
      Engine label
      restrictions](https://cloud.google.com/compute/docs/labeling-
      resources#restrictions) apply. Label names that start with "goog-" or
      "google-" are reserved.
    logsPolicy: Log preservation policy for the Job.
    name: Output only. Job name. For example: "projects/123456/locations/us-
      central1/jobs/job01".
    notification: Deprecated: please use notifications instead.
    notifications: Notification configurations.
    priority: Priority of the Job. The valid value range is [0, 100). Default
      value is 0. Higher value indicates higher priority. A job with higher
      priority value is more likely to run earlier if all other requirements
      are satisfied.
    schedulingPolicy: Scheduling policy for TaskGroups in the job.
    status: Output only. Job status. It is read only for users.
    taskGroups: Required. TaskGroups in the Job. Only one TaskGroup is
      supported now.
    uid: Output only. A system generated unique ID (in UUID4 format) for the
      Job.
    updateTime: Output only. The last time the Job was updated.
  """

  class SchedulingPolicyValueValuesEnum(_messages.Enum):
    r"""Scheduling policy for TaskGroups in the job.

    Values:
      SCHEDULING_POLICY_UNSPECIFIED: Unspecified.
      AS_SOON_AS_POSSIBLE: Run all TaskGroups as soon as possible.
    """
    SCHEDULING_POLICY_UNSPECIFIED = 0
    AS_SOON_AS_POSSIBLE = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels for the Job. Labels could be user provided or system generated.
    For example, "labels": { "department": "finance", "environment": "test" }
    You can assign up to 64 labels. [Google Compute Engine label
    restrictions](https://cloud.google.com/compute/docs/labeling-
    resources#restrictions) apply. Label names that start with "goog-" or
    "google-" are reserved.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allocationPolicy = _messages.MessageField('AllocationPolicy', 1)
  createTime = _messages.StringField(2)
  dependencies = _messages.MessageField('JobDependency', 3, repeated=True)
  labels = _messages.MessageField('LabelsValue', 4)
  logsPolicy = _messages.MessageField('LogsPolicy', 5)
  name = _messages.StringField(6)
  notification = _messages.MessageField('JobNotification', 7)
  notifications = _messages.MessageField('JobNotification', 8, repeated=True)
  priority = _messages.IntegerField(9)
  schedulingPolicy = _messages.EnumField('SchedulingPolicyValueValuesEnum', 10)
  status = _messages.MessageField('JobStatus', 11)
  taskGroups = _messages.MessageField('TaskGroup', 12, repeated=True)
  uid = _messages.StringField(13)
  updateTime = _messages.StringField(14)


class JobDependency(_messages.Message):
  r"""JobDependency describes the state of other Jobs that the start of this
  Job depends on. All dependent Jobs must have been submitted in the same
  region.

  Messages:
    ItemsValue: Each item maps a Job name to a Type. All items must be
      satisfied for the JobDependency to be satisfied (the AND operation).
      Once a condition for one item becomes true, it won't go back to false
      even the dependent Job state changes again.

  Fields:
    items: Each item maps a Job name to a Type. All items must be satisfied
      for the JobDependency to be satisfied (the AND operation). Once a
      condition for one item becomes true, it won't go back to false even the
      dependent Job state changes again.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ItemsValue(_messages.Message):
    r"""Each item maps a Job name to a Type. All items must be satisfied for
    the JobDependency to be satisfied (the AND operation). Once a condition
    for one item becomes true, it won't go back to false even the dependent
    Job state changes again.

    Messages:
      AdditionalProperty: An additional property for a ItemsValue object.

    Fields:
      additionalProperties: Additional properties of type ItemsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ItemsValue object.

      Enums:
        ValueValueValuesEnum:

      Fields:
        key: Name of the additional property.
        value: A ValueValueValuesEnum attribute.
      """

      class ValueValueValuesEnum(_messages.Enum):
        r"""ValueValueValuesEnum enum type.

        Values:
          TYPE_UNSPECIFIED: Unspecified.
          SUCCEEDED: The dependent Job has succeeded.
          FAILED: The dependent Job has failed.
          FINISHED: SUCCEEDED or FAILED.
        """
        TYPE_UNSPECIFIED = 0
        SUCCEEDED = 1
        FAILED = 2
        FINISHED = 3

      key = _messages.StringField(1)
      value = _messages.EnumField('ValueValueValuesEnum', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  items = _messages.MessageField('ItemsValue', 1)


class JobNotification(_messages.Message):
  r"""Notification configurations.

  Fields:
    message: The attribute requirements of messages to be sent to this Pub/Sub
      topic. Without this field, no message will be sent.
    pubsubTopic: The Pub/Sub topic where notifications like the job state
      changes will be published. The topic must exist in the same project as
      the job and billings will be charged to this project. If not specified,
      no Pub/Sub messages will be sent. Topic format:
      `projects/{project}/topics/{topic}`.
  """

  message = _messages.MessageField('Message', 1)
  pubsubTopic = _messages.StringField(2)


class JobStatus(_messages.Message):
  r"""Job status.

  Enums:
    StateValueValuesEnum: Job state

  Messages:
    TaskGroupsValue: Aggregated task status for each TaskGroup in the Job. The
      map key is TaskGroup ID.

  Fields:
    resourceUsage: The resource usage of the job.
    runDuration: The duration of time that the Job spent in status RUNNING.
    state: Job state
    statusEvents: Job status events
    taskGroups: Aggregated task status for each TaskGroup in the Job. The map
      key is TaskGroup ID.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Job state

    Values:
      STATE_UNSPECIFIED: Job state unspecified.
      QUEUED: Job is admitted (validated and persisted) and waiting for
        resources.
      SCHEDULED: Job is scheduled to run as soon as resource allocation is
        ready. The resource allocation may happen at a later time but with a
        high chance to succeed.
      RUNNING: Resource allocation has been successful. At least one Task in
        the Job is RUNNING.
      SUCCEEDED: All Tasks in the Job have finished successfully.
      FAILED: At least one Task in the Job has failed.
      DELETION_IN_PROGRESS: The Job will be deleted, but has not been deleted
        yet. Typically this is because resources used by the Job are still
        being cleaned up.
    """
    STATE_UNSPECIFIED = 0
    QUEUED = 1
    SCHEDULED = 2
    RUNNING = 3
    SUCCEEDED = 4
    FAILED = 5
    DELETION_IN_PROGRESS = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TaskGroupsValue(_messages.Message):
    r"""Aggregated task status for each TaskGroup in the Job. The map key is
    TaskGroup ID.

    Messages:
      AdditionalProperty: An additional property for a TaskGroupsValue object.

    Fields:
      additionalProperties: Additional properties of type TaskGroupsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TaskGroupsValue object.

      Fields:
        key: Name of the additional property.
        value: A TaskGroupStatus attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('TaskGroupStatus', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  resourceUsage = _messages.MessageField('ResourceUsage', 1)
  runDuration = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  statusEvents = _messages.MessageField('StatusEvent', 4, repeated=True)
  taskGroups = _messages.MessageField('TaskGroupsValue', 5)


class KMSEnvMap(_messages.Message):
  r"""A KMSEnvMap object.

  Fields:
    cipherText: The value of the cipherText response from the `encrypt`
      method.
    keyName: The name of the KMS key that will be used to decrypt the cipher
      text.
  """

  cipherText = _messages.StringField(1)
  keyName = _messages.StringField(2)


class LifecyclePolicy(_messages.Message):
  r"""LifecyclePolicy describes how to deal with task failures based on
  different conditions.

  Enums:
    ActionValueValuesEnum: Action to execute when ActionCondition is true.
      When RETRY_TASK is specified, we will retry failed tasks if we notice
      any exit code match and fail tasks if no match is found. Likewise, when
      FAIL_TASK is specified, we will fail tasks if we notice any exit code
      match and retry tasks if no match is found.

  Fields:
    action: Action to execute when ActionCondition is true. When RETRY_TASK is
      specified, we will retry failed tasks if we notice any exit code match
      and fail tasks if no match is found. Likewise, when FAIL_TASK is
      specified, we will fail tasks if we notice any exit code match and retry
      tasks if no match is found.
    actionCondition: Conditions that decide why a task failure is dealt with a
      specific action.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Action to execute when ActionCondition is true. When RETRY_TASK is
    specified, we will retry failed tasks if we notice any exit code match and
    fail tasks if no match is found. Likewise, when FAIL_TASK is specified, we
    will fail tasks if we notice any exit code match and retry tasks if no
    match is found.

    Values:
      ACTION_UNSPECIFIED: Action unspecified.
      RETRY_TASK: Action that tasks in the group will be scheduled to re-
        execute.
      FAIL_TASK: Action that tasks in the group will be stopped immediately.
    """
    ACTION_UNSPECIFIED = 0
    RETRY_TASK = 1
    FAIL_TASK = 2

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  actionCondition = _messages.MessageField('ActionCondition', 2)


class ListJobsResponse(_messages.Message):
  r"""ListJob Response.

  Fields:
    jobs: Jobs.
    nextPageToken: Next page token.
    unreachable: Locations that could not be reached.
  """

  jobs = _messages.MessageField('Job', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListTasksResponse(_messages.Message):
  r"""ListTasks Response.

  Fields:
    nextPageToken: Next page token.
    tasks: Tasks.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  tasks = _messages.MessageField('Task', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LocationPolicy(_messages.Message):
  r"""A LocationPolicy object.

  Fields:
    allowedLocations: A list of allowed location names represented by internal
      URLs. Each location can be a region or a zone. Only one region or
      multiple zones in one region is supported now. For example,
      ["regions/us-central1"] allow VMs in any zones in region us-central1.
      ["zones/us-central1-a", "zones/us-central1-c"] only allow VMs in zones
      us-central1-a and us-central1-c. All locations end up in different
      regions would cause errors. For example, ["regions/us-central1",
      "zones/us-central1-a", "zones/us-central1-b", "zones/us-west1-a"]
      contains 2 regions "us-central1" and "us-west1". An error is expected in
      this case.
    deniedLocations: A list of denied location names. Not yet implemented.
  """

  allowedLocations = _messages.StringField(1, repeated=True)
  deniedLocations = _messages.StringField(2, repeated=True)


class LogsPolicy(_messages.Message):
  r"""LogsPolicy describes how outputs from a Job's Tasks (stdout/stderr) will
  be preserved.

  Enums:
    DestinationValueValuesEnum: Where logs should be saved.

  Fields:
    destination: Where logs should be saved.
    logsPath: The path to which logs are saved when the destination = PATH.
      This can be a local file path on the VM, or under the mount point of a
      Persistent Disk or Filestore, or a Cloud Storage path.
  """

  class DestinationValueValuesEnum(_messages.Enum):
    r"""Where logs should be saved.

    Values:
      DESTINATION_UNSPECIFIED: Logs are not preserved.
      CLOUD_LOGGING: Logs are streamed to Cloud Logging.
      PATH: Logs are saved to a file path.
    """
    DESTINATION_UNSPECIFIED = 0
    CLOUD_LOGGING = 1
    PATH = 2

  destination = _messages.EnumField('DestinationValueValuesEnum', 1)
  logsPath = _messages.StringField(2)


class Message(_messages.Message):
  r"""Message details. Describe the conditions under which messages will be
  sent. If no attribute is defined, no message will be sent by default. One
  message should specify either the job or the task level attributes, but not
  both. For example, job level: JOB_STATE_CHANGED and/or a specified
  new_job_state; task level: TASK_STATE_CHANGED and/or a specified
  new_task_state.

  Enums:
    NewJobStateValueValuesEnum: The new job state.
    NewTaskStateValueValuesEnum: The new task state.
    TypeValueValuesEnum: The message type.

  Fields:
    newJobState: The new job state.
    newTaskState: The new task state.
    type: The message type.
  """

  class NewJobStateValueValuesEnum(_messages.Enum):
    r"""The new job state.

    Values:
      STATE_UNSPECIFIED: Job state unspecified.
      QUEUED: Job is admitted (validated and persisted) and waiting for
        resources.
      SCHEDULED: Job is scheduled to run as soon as resource allocation is
        ready. The resource allocation may happen at a later time but with a
        high chance to succeed.
      RUNNING: Resource allocation has been successful. At least one Task in
        the Job is RUNNING.
      SUCCEEDED: All Tasks in the Job have finished successfully.
      FAILED: At least one Task in the Job has failed.
      DELETION_IN_PROGRESS: The Job will be deleted, but has not been deleted
        yet. Typically this is because resources used by the Job are still
        being cleaned up.
    """
    STATE_UNSPECIFIED = 0
    QUEUED = 1
    SCHEDULED = 2
    RUNNING = 3
    SUCCEEDED = 4
    FAILED = 5
    DELETION_IN_PROGRESS = 6

  class NewTaskStateValueValuesEnum(_messages.Enum):
    r"""The new task state.

    Values:
      STATE_UNSPECIFIED: Unknown state.
      PENDING: The Task is created and waiting for resources.
      ASSIGNED: The Task is assigned to at least one VM.
      RUNNING: The Task is running.
      FAILED: The Task has failed.
      SUCCEEDED: The Task has succeeded.
      UNEXECUTED: The Task has not been executed when the Job finishes.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    ASSIGNED = 2
    RUNNING = 3
    FAILED = 4
    SUCCEEDED = 5
    UNEXECUTED = 6

  class TypeValueValuesEnum(_messages.Enum):
    r"""The message type.

    Values:
      TYPE_UNSPECIFIED: Unspecified.
      JOB_STATE_CHANGED: Notify users that the job state has changed.
      TASK_STATE_CHANGED: Notify users that the task state has changed.
    """
    TYPE_UNSPECIFIED = 0
    JOB_STATE_CHANGED = 1
    TASK_STATE_CHANGED = 2

  newJobState = _messages.EnumField('NewJobStateValueValuesEnum', 1)
  newTaskState = _messages.EnumField('NewTaskStateValueValuesEnum', 2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class NFS(_messages.Message):
  r"""Represents an NFS volume.

  Fields:
    remotePath: Remote source path exported from the NFS, e.g., "/share".
    server: The IP address of the NFS.
  """

  remotePath = _messages.StringField(1)
  server = _messages.StringField(2)


class NetworkInterface(_messages.Message):
  r"""A network interface.

  Fields:
    network: The URL of an existing network resource. You can specify the
      network as a full or partial URL. For example, the following are all
      valid URLs: * https://www.googleapis.com/compute/v1/projects/{project}/g
      lobal/networks/{network} * projects/{project}/global/networks/{network}
      * global/networks/{network}
    noExternalIpAddress: Default is false (with an external IP address).
      Required if no external public IP address is attached to the VM. If no
      external public IP address, additional configuration is required to
      allow the VM to access Google Services. See
      https://cloud.google.com/vpc/docs/configure-private-google-access and
      https://cloud.google.com/nat/docs/gce-example#create-nat for more
      information.
    subnetwork: The URL of an existing subnetwork resource in the network. You
      can specify the subnetwork as a full or partial URL. For example, the
      following are all valid URLs: * https://www.googleapis.com/compute/v1/pr
      ojects/{project}/regions/{region}/subnetworks/{subnetwork} *
      projects/{project}/regions/{region}/subnetworks/{subnetwork} *
      regions/{region}/subnetworks/{subnetwork}
  """

  network = _messages.StringField(1)
  noExternalIpAddress = _messages.BooleanField(2)
  subnetwork = _messages.StringField(3)


class NetworkPolicy(_messages.Message):
  r"""NetworkPolicy describes VM instance network configurations.

  Fields:
    networkInterfaces: Network configurations.
  """

  networkInterfaces = _messages.MessageField('NetworkInterface', 1, repeated=True)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class PD(_messages.Message):
  r"""Deprecated: please use device_name instead.

  Fields:
    device: PD device name, e.g. persistent-disk-1.
    disk: PD disk name, e.g. pd-1.
    existing: Whether this is an existing PD. Default is false. If false,
      i.e., new PD, we will format it into ext4 and mount to the given path.
      If true, i.e., existing PD, it should be in ext4 format and we will
      mount it to the given path.
  """

  device = _messages.StringField(1)
  disk = _messages.StringField(2)
  existing = _messages.BooleanField(3)


class PlacementPolicy(_messages.Message):
  r"""PlacementPolicy describes a group placement policy for the VMs
  controlled by this AllocationPolicy.

  Fields:
    collocation: UNSPECIFIED vs. COLLOCATED (default UNSPECIFIED). Use
      COLLOCATED when you want VMs to be located close to each other for low
      network latency between the VMs. No placement policy will be generated
      when collocation is UNSPECIFIED.
    maxDistance: When specified, causes the job to fail if more than
      max_distance logical switches are required between VMs. Batch uses the
      most compact possible placement of VMs even when max_distance is not
      specified. An explicit max_distance makes that level of compactness a
      strict requirement. Not yet implemented
  """

  collocation = _messages.StringField(1)
  maxDistance = _messages.IntegerField(2)


class ResourceUsage(_messages.Message):
  r"""ResourceUsage describes the resource usage of the job.

  Fields:
    coreHours: The CPU core hours that the job consumes.
  """

  coreHours = _messages.FloatField(1)


class Runnable(_messages.Message):
  r"""Runnable describes instructions for executing a specific script or
  container as part of a Task.

  Messages:
    LabelsValue: Labels for this Runnable.

  Fields:
    alwaysRun: By default, after a Runnable fails, no further Runnable are
      executed. This flag indicates that this Runnable must be run even if the
      Task has already failed. This is useful for Runnables that copy output
      files off of the VM or for debugging. The always_run flag does not
      override the Task's overall max_run_duration. If the max_run_duration
      has expired then no further Runnables will execute, not even always_run
      Runnables.
    background: This flag allows a Runnable to continue running in the
      background while the Task executes subsequent Runnables. This is useful
      to provide services to other Runnables (or to provide debugging support
      tools like SSH servers).
    barrier: Barrier runnable.
    container: Container runnable.
    displayName: Optional. DisplayName is an optional field that can be
      provided by the caller. If provided, it will be used in logs and other
      outputs to identify the script, making it easier for users to understand
      the logs. If not provided the index of the runnable will be used for
      outputs.
    environment: Environment variables for this Runnable (overrides variables
      set for the whole Task or TaskGroup).
    ignoreExitStatus: Normally, a non-zero exit status causes the Task to
      fail. This flag allows execution of other Runnables to continue instead.
    labels: Labels for this Runnable.
    script: Script runnable.
    timeout: Timeout for this Runnable.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels for this Runnable.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  alwaysRun = _messages.BooleanField(1)
  background = _messages.BooleanField(2)
  barrier = _messages.MessageField('Barrier', 3)
  container = _messages.MessageField('Container', 4)
  displayName = _messages.StringField(5)
  environment = _messages.MessageField('Environment', 6)
  ignoreExitStatus = _messages.BooleanField(7)
  labels = _messages.MessageField('LabelsValue', 8)
  script = _messages.MessageField('Script', 9)
  timeout = _messages.StringField(10)


class Script(_messages.Message):
  r"""Script runnable.

  Fields:
    path: Script file path on the host VM. To specify an interpreter, please
      add a `#!`(also known as [shebang
      line](https://en.wikipedia.org/wiki/Shebang_(Unix))) as the first line
      of the file.(For example, to execute the script using bash,
      `#!/bin/bash` should be the first line of the file. To execute the
      script using`Python3`, `#!/usr/bin/env python3` should be the first line
      of the file.) Otherwise, the file will by default be excuted by
      `/bin/sh`.
    text: Shell script text. To specify an interpreter, please add a `#!\n` at
      the beginning of the text.(For example, to execute the script using
      bash, `#!/bin/bash\n` should be added. To execute the script
      using`Python3`, `#!/usr/bin/env python3\n` should be added.) Otherwise,
      the script will by default be excuted by `/bin/sh`.
  """

  path = _messages.StringField(1)
  text = _messages.StringField(2)


class ServiceAccount(_messages.Message):
  r"""Carries information about a Google Cloud service account.

  Fields:
    email: Email address of the service account. If not specified, the default
      Compute Engine service account for the project will be used. If instance
      template is being used, the service account has to be specified in the
      instance template and it has to match the email field here.
    scopes: List of scopes to be enabled for this service account on the VM,
      in addition to the cloud-platform API scope that will be added by
      default.
  """

  email = _messages.StringField(1)
  scopes = _messages.StringField(2, repeated=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StatusEvent(_messages.Message):
  r"""Status event

  Enums:
    TaskStateValueValuesEnum: Task State

  Fields:
    description: Description of the event.
    eventTime: The time this event occurred.
    taskExecution: Task Execution
    taskState: Task State
    type: Type of the event.
  """

  class TaskStateValueValuesEnum(_messages.Enum):
    r"""Task State

    Values:
      STATE_UNSPECIFIED: Unknown state.
      PENDING: The Task is created and waiting for resources.
      ASSIGNED: The Task is assigned to at least one VM.
      RUNNING: The Task is running.
      FAILED: The Task has failed.
      SUCCEEDED: The Task has succeeded.
      UNEXECUTED: The Task has not been executed when the Job finishes.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    ASSIGNED = 2
    RUNNING = 3
    FAILED = 4
    SUCCEEDED = 5
    UNEXECUTED = 6

  description = _messages.StringField(1)
  eventTime = _messages.StringField(2)
  taskExecution = _messages.MessageField('TaskExecution', 3)
  taskState = _messages.EnumField('TaskStateValueValuesEnum', 4)
  type = _messages.StringField(5)


class Task(_messages.Message):
  r"""A Cloud Batch task.

  Fields:
    name: Task name. The name is generated from the parent TaskGroup name and
      'id' field. For example: "projects/123456/locations/us-
      west1/jobs/job01/taskGroups/group01/tasks/task01".
    status: Task Status.
  """

  name = _messages.StringField(1)
  status = _messages.MessageField('TaskStatus', 2)


class TaskExecution(_messages.Message):
  r"""This Task Execution field includes detail information for task execution
  procedures, based on StatusEvent types.

  Fields:
    exitCode: When task is completed as the status of FAILED or SUCCEEDED,
      exit code is for one task execution result, default is 0 as success.
    stderrSnippet: Optional. The tail end of any content written to standard
      error by the task execution. This field will be populated only when the
      execution failed.
  """

  exitCode = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  stderrSnippet = _messages.StringField(2)


class TaskGroup(_messages.Message):
  r"""A TaskGroup defines one or more Tasks that all share the same TaskSpec.

  Enums:
    SchedulingPolicyValueValuesEnum: Scheduling policy for Tasks in the
      TaskGroup. The default value is AS_SOON_AS_POSSIBLE.

  Messages:
    LabelsValue: Labels for the TaskGroup. Labels could be user provided or
      system generated. You can assign up to 64 labels. [Google Compute Engine
      label restrictions](https://cloud.google.com/compute/docs/labeling-
      resources#restrictions) apply. Label names that start with "goog-" or
      "google-" are reserved.

  Fields:
    allocationPolicy: Compute resource allocation for the TaskGroup. If
      specified, it overrides resources in Job.
    labels: Labels for the TaskGroup. Labels could be user provided or system
      generated. You can assign up to 64 labels. [Google Compute Engine label
      restrictions](https://cloud.google.com/compute/docs/labeling-
      resources#restrictions) apply. Label names that start with "goog-" or
      "google-" are reserved.
    name: Output only. TaskGroup name. The system generates this field based
      on parent Job name. For example: "projects/123456/locations/us-
      west1/jobs/job01/taskGroups/group01".
    parallelism: Max number of tasks that can run in parallel. Default to
      min(task_count, 1000). Field parallelism must be 1 if the
      scheduling_policy is IN_ORDER.
    permissiveSsh: When true, Batch will configure SSH to allow passwordless
      login between VMs running the Batch tasks in the same TaskGroup.
    requireHostsFile: When true, Batch will populate a file with a list of all
      VMs assigned to the TaskGroup and set the BATCH_HOSTS_FILE environment
      variable to the path of that file. Defaults to false.
    schedulingPolicy: Scheduling policy for Tasks in the TaskGroup. The
      default value is AS_SOON_AS_POSSIBLE.
    taskCount: Number of Tasks in the TaskGroup. Default is 1.
    taskCountPerNode: Max number of tasks that can be run on a VM at the same
      time. If not specified, the system will decide a value based on
      available compute resources on a VM and task requirements.
    taskEnvironments: An array of environment variable mappings, which are
      passed to Tasks with matching indices. If task_environments is used then
      task_count should not be specified in the request (and will be ignored).
      Task count will be the length of task_environments. Tasks get a
      BATCH_TASK_INDEX and BATCH_TASK_COUNT environment variable, in addition
      to any environment variables set in task_environments, specifying the
      number of Tasks in the Task's parent TaskGroup, and the specific Task's
      index in the TaskGroup (0 through BATCH_TASK_COUNT - 1).
    taskSpec: Required. Tasks in the group share the same task spec.
  """

  class SchedulingPolicyValueValuesEnum(_messages.Enum):
    r"""Scheduling policy for Tasks in the TaskGroup. The default value is
    AS_SOON_AS_POSSIBLE.

    Values:
      SCHEDULING_POLICY_UNSPECIFIED: Unspecified.
      AS_SOON_AS_POSSIBLE: Run Tasks as soon as resources are available. Tasks
        might be executed in parallel depending on parallelism and task_count
        values.
      IN_ORDER: Run Tasks sequentially with increased task index.
    """
    SCHEDULING_POLICY_UNSPECIFIED = 0
    AS_SOON_AS_POSSIBLE = 1
    IN_ORDER = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels for the TaskGroup. Labels could be user provided or system
    generated. You can assign up to 64 labels. [Google Compute Engine label
    restrictions](https://cloud.google.com/compute/docs/labeling-
    resources#restrictions) apply. Label names that start with "goog-" or
    "google-" are reserved.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allocationPolicy = _messages.MessageField('AllocationPolicy', 1)
  labels = _messages.MessageField('LabelsValue', 2)
  name = _messages.StringField(3)
  parallelism = _messages.IntegerField(4)
  permissiveSsh = _messages.BooleanField(5)
  requireHostsFile = _messages.BooleanField(6)
  schedulingPolicy = _messages.EnumField('SchedulingPolicyValueValuesEnum', 7)
  taskCount = _messages.IntegerField(8)
  taskCountPerNode = _messages.IntegerField(9)
  taskEnvironments = _messages.MessageField('Environment', 10, repeated=True)
  taskSpec = _messages.MessageField('TaskSpec', 11)


class TaskGroupStatus(_messages.Message):
  r"""Aggregated task status for a TaskGroup.

  Messages:
    CountsValue: Count of task in each state in the TaskGroup. The map key is
      task state name.

  Fields:
    counts: Count of task in each state in the TaskGroup. The map key is task
      state name.
    instances: Status of instances allocated for the TaskGroup.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CountsValue(_messages.Message):
    r"""Count of task in each state in the TaskGroup. The map key is task
    state name.

    Messages:
      AdditionalProperty: An additional property for a CountsValue object.

    Fields:
      additionalProperties: Additional properties of type CountsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CountsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  counts = _messages.MessageField('CountsValue', 1)
  instances = _messages.MessageField('InstanceStatus', 2, repeated=True)


class TaskResourceUsage(_messages.Message):
  r"""TaskResourceUsage describes the resource usage of the task.

  Fields:
    coreHours: The CPU core hours the task consumes based on task requirement
      and run time.
  """

  coreHours = _messages.FloatField(1)


class TaskSpec(_messages.Message):
  r"""Spec of a task

  Messages:
    EnvironmentsValue: Deprecated: please use environment(non-plural) instead.

  Fields:
    computeResource: ComputeResource requirements.
    environment: Environment variables to set before running the Task.
    environments: Deprecated: please use environment(non-plural) instead.
    lifecyclePolicies: Lifecycle management schema when any task in a task
      group is failed. Currently we only support one lifecycle policy. When
      the lifecycle policy condition is met, the action in the policy will
      execute. If task execution result does not meet with the defined
      lifecycle policy, we consider it as the default policy. Default policy
      means if the exit code is 0, exit task. If task ends with non-zero exit
      code, retry the task with max_retry_count.
    maxRetryCount: Maximum number of retries on failures. The default, 0,
      which means never retry. The valid value range is [0, 10].
    maxRunDuration: Maximum duration the task should run. The task will be
      killed and marked as FAILED if over this limit.
    runnables: The sequence of scripts or containers to run for this Task.
      Each Task using this TaskSpec executes its list of runnables in order.
      The Task succeeds if all of its runnables either exit with a zero status
      or any that exit with a non-zero status have the ignore_exit_status
      flag. Background runnables are killed automatically (if they have not
      already exited) a short time after all foreground runnables have
      completed. Even though this is likely to result in a non-zero exit
      status for the background runnable, these automatic kills are not
      treated as Task failures.
    volumes: Volumes to mount before running Tasks using this TaskSpec.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvironmentsValue(_messages.Message):
    r"""Deprecated: please use environment(non-plural) instead.

    Messages:
      AdditionalProperty: An additional property for a EnvironmentsValue
        object.

    Fields:
      additionalProperties: Additional properties of type EnvironmentsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvironmentsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  computeResource = _messages.MessageField('ComputeResource', 1)
  environment = _messages.MessageField('Environment', 2)
  environments = _messages.MessageField('EnvironmentsValue', 3)
  lifecyclePolicies = _messages.MessageField('LifecyclePolicy', 4, repeated=True)
  maxRetryCount = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  maxRunDuration = _messages.StringField(6)
  runnables = _messages.MessageField('Runnable', 7, repeated=True)
  volumes = _messages.MessageField('Volume', 8, repeated=True)


class TaskStatus(_messages.Message):
  r"""Status of a task

  Enums:
    StateValueValuesEnum: Task state

  Fields:
    resourceUsage: The resource usage of the task.
    state: Task state
    statusEvents: Detailed info about why the state is reached.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Task state

    Values:
      STATE_UNSPECIFIED: Unknown state.
      PENDING: The Task is created and waiting for resources.
      ASSIGNED: The Task is assigned to at least one VM.
      RUNNING: The Task is running.
      FAILED: The Task has failed.
      SUCCEEDED: The Task has succeeded.
      UNEXECUTED: The Task has not been executed when the Job finishes.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    ASSIGNED = 2
    RUNNING = 3
    FAILED = 4
    SUCCEEDED = 5
    UNEXECUTED = 6

  resourceUsage = _messages.MessageField('TaskResourceUsage', 1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  statusEvents = _messages.MessageField('StatusEvent', 3, repeated=True)


class Volume(_messages.Message):
  r"""Volume describes a volume and parameters for it to be mounted to a VM.

  Fields:
    deviceName: Device name of an attached disk volume, which should align
      with a device_name specified by
      job.allocation_policy.instances[0].policy.disks[i].device_name or
      defined by the given instance template in
      job.allocation_policy.instances[0].instance_template.
    gcs: A Google Cloud Storage (GCS) volume.
    mountOptions: For Google Cloud Storage (GCS), mount options are the
      options supported by the gcsfuse tool
      (https://github.com/GoogleCloudPlatform/gcsfuse). For existing
      persistent disks, mount options provided by the mount command
      (https://man7.org/linux/man-pages/man8/mount.8.html) except writing are
      supported. This is due to restrictions of multi-writer mode
      (https://cloud.google.com/compute/docs/disks/sharing-disks-between-vms).
      For other attached disks and Network File System (NFS), mount options
      are these supported by the mount command (https://man7.org/linux/man-
      pages/man8/mount.8.html).
    mountPath: The mount path for the volume, e.g. /mnt/disks/share.
    nfs: A Network File System (NFS) volume. For example, a Filestore file
      share.
    pd: Deprecated: please use device_name instead.
  """

  deviceName = _messages.StringField(1)
  gcs = _messages.MessageField('GCS', 2)
  mountOptions = _messages.StringField(3, repeated=True)
  mountPath = _messages.StringField(4)
  nfs = _messages.MessageField('NFS', 5)
  pd = _messages.MessageField('PD', 6)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

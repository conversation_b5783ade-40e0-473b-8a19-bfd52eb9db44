"""Generated message classes for servicenetworking version v1.

Provides automatic management of network configurations necessary for certain
services.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'servicenetworking'


class AddDnsRecordSetMetadata(_messages.Message):
  r"""Metadata provided through GetOperation request for the LRO generated by
  AddDnsRecordSet API
  """



class AddDnsRecordSetRequest(_messages.Message):
  r"""Request to add a record set to a private managed DNS zone in the shared
  producer host project.

  Fields:
    consumerNetwork: Required. The network that the consumer is using to
      connect with services. Must be in the form of
      projects/{project}/global/networks/{network} {project} is the project
      number, as in '12345' {network} is the network name.
    dnsRecordSet: Required. The DNS record set to add.
    zone: Required. The name of the private DNS zone in the shared producer
      host project to which the record set will be added.
  """

  consumerNetwork = _messages.StringField(1)
  dnsRecordSet = _messages.MessageField('DnsRecordSet', 2)
  zone = _messages.StringField(3)


class AddDnsZoneMetadata(_messages.Message):
  r"""Metadata provided through GetOperation request for the LRO generated by
  AddDnsZone API
  """



class AddDnsZoneRequest(_messages.Message):
  r"""Request to add a private managed DNS zone in the shared producer host
  project and a matching DNS peering zone in the consumer project.

  Fields:
    consumerNetwork: Required. The network that the consumer is using to
      connect with services. Must be in the form of
      projects/{project}/global/networks/{network} {project} is the project
      number, as in '12345' {network} is the network name.
    dnsSuffix: Required. The DNS name suffix for the zones e.g.
      `example.com.`. Cloud DNS requires that a DNS suffix ends with a
      trailing dot.
    name: Required. The name for both the private zone in the shared producer
      host project and the peering zone in the consumer project. Must be
      unique within both projects. The name must be 1-63 characters long, must
      begin with a letter, end with a letter or digit, and only contain
      lowercase letters, digits or dashes.
  """

  consumerNetwork = _messages.StringField(1)
  dnsSuffix = _messages.StringField(2)
  name = _messages.StringField(3)


class AddDnsZoneResponse(_messages.Message):
  r"""Represents managed DNS zones created in the shared producer host and
  consumer projects.

  Fields:
    consumerPeeringZone: The DNS peering zone created in the consumer project.
    producerPrivateZone: The private DNS zone created in the shared producer
      host project.
  """

  consumerPeeringZone = _messages.MessageField('DnsZone', 1)
  producerPrivateZone = _messages.MessageField('DnsZone', 2)


class AddRolesMetadata(_messages.Message):
  r"""Metadata provided through GetOperation request for the LRO generated by
  AddRoles API
  """



class AddRolesRequest(_messages.Message):
  r"""Request for AddRoles to allow Service Producers to add roles in the
  shared VPC host project for them to use.

  Fields:
    consumerNetwork: Required. The network that the consumer is using to
      connect with services. Must be in the form of
      projects/{project}/global/networks/{network} {project} is a project
      number, as in '12345' {network} is a network name.
    policyBinding: Required. List of policy bindings to add to shared VPC host
      project.
  """

  consumerNetwork = _messages.StringField(1)
  policyBinding = _messages.MessageField('PolicyBinding', 2, repeated=True)


class AddRolesResponse(_messages.Message):
  r"""Represents IAM roles added to the shared VPC host project.

  Fields:
    policyBinding: Required. List of policy bindings that were added to the
      shared VPC host project.
  """

  policyBinding = _messages.MessageField('PolicyBinding', 1, repeated=True)


class AddSubnetworkRequest(_messages.Message):
  r"""Request to create a subnetwork in a previously peered service network.

  Fields:
    allowSubnetCidrRoutesOverlap: Optional. Defines the
      allowSubnetCidrRoutesOverlap field of the subnet, e.g. Available in
      alpha and beta according to [Compute API documentation](https://cloud.go
      ogle.com/compute/docs/reference/rest/beta/subnetworks/insert)
    checkServiceNetworkingUsePermission: Optional. The IAM permission check
      determines whether the consumer project has
      'servicenetworking.services.use' permission or not.
    computeIdempotencyWindow: Optional. Specifies a custom time bucket for
      Arcus subnetwork request idempotency. If two equivalent concurrent
      requests are made, Arcus will know to ignore the request if it has
      already been completed or is in progress. Only requests with matching
      compute_idempotency_window have guaranteed idempotency. Changing this
      time window between requests results in undefined behavior. Zero (or
      empty) value with custom_compute_idempotency_window=true specifies no
      idempotency (i.e. no request ID is provided to Arcus). Maximum value of
      14 days (enforced by Arcus limit). For more information on how to use,
      see: go/revisit-sn-idempotency-window
    consumer: Required. A resource that represents the service consumer, such
      as `projects/123456`. The project number can be different from the value
      in the consumer network parameter. For example, the network might be
      part of a Shared VPC network. In those cases, Service Networking
      validates that this resource belongs to that Shared VPC.
    consumerNetwork: Required. The name of the service consumer's VPC network.
      The network must have an existing private connection that was
      provisioned through the connections.create method. The name must be in
      the following format: `projects/{project}/global/networks/{network}`,
      where {project} is a project number, such as `12345`. {network} is the
      name of a VPC network in the project.
    description: Optional. Description of the subnet.
    ipPrefixLength: Required. The prefix length of the subnet's IP address
      range. Use CIDR range notation, such as `29` to provision a subnet with
      an `x.x.x.x/29` CIDR range. The IP address range is drawn from a pool of
      available ranges in the service consumer's allocated range. GCE
      disallows subnets with prefix_length > 29
    outsideAllocationPublicIpRange: Optional. Enable outside allocation using
      public IP addresses. Any public IP range may be specified. If this field
      is provided, we will not use customer reserved ranges for this primary
      IP range.
    privateIpv6GoogleAccess: Optional. The private IPv6 google access type for
      the VMs in this subnet. For information about the access types that can
      be set using this field, see [subnetwork](https://cloud.google.com/compu
      te/docs/reference/rest/v1/subnetworks) in the Compute API documentation.
    purpose: Optional. Defines the purpose field of the subnet, e.g.
      'PRIVATE_SERVICE_CONNECT'. For information about the purposes that can
      be set using this field, see [subnetwork](https://cloud.google.com/compu
      te/docs/reference/rest/v1/subnetworks) in the Compute API documentation.
    region: Required. The name of a [region](/compute/docs/regions-zones) for
      the subnet, such `europe-west1`.
    requestedAddress: Optional. The starting address of a range. The address
      must be a valid IPv4 address in the x.x.x.x format. This value combined
      with the IP prefix range is the CIDR range for the subnet. The range
      must be within the allocated range that is assigned to the private
      connection. If the CIDR range isn't available, the call fails.
    requestedRanges: Optional. The name of one or more allocated IP address
      ranges associated with this private service access connection. If no
      range names are provided all ranges associated with this connection will
      be considered. If a CIDR range with the specified IP prefix length is
      not available within these ranges, the call fails.
    role: Optional. Defines the role field of the subnet, e.g. 'ACTIVE'. For
      information about the roles that can be set using this field, see [subne
      twork](https://cloud.google.com/compute/docs/reference/rest/v1/subnetwor
      ks) in the Compute API documentation.
    secondaryIpRangeSpecs: Optional. A list of secondary IP ranges to be
      created within the new subnetwork.
    subnetwork: Required. A name for the new subnet. For information about the
      naming requirements, see
      [subnetwork](/compute/docs/reference/rest/v1/subnetworks) in the Compute
      API documentation.
    subnetworkUsers: A list of members that are granted the
      `roles/servicenetworking.subnetworkAdmin` role on the subnet.
    useCustomComputeIdempotencyWindow: Optional. Specifies if Service
      Networking should use a custom time bucket for Arcus idempotency. If
      false, Service Networking uses a 300 second (5 minute) Arcus idempotency
      window. If true, Service Networking uses a custom idempotency window
      provided by the user in field compute_idempotency_window. For more
      information on how to use, see: go/revisit-sn-idempotency-window
  """

  allowSubnetCidrRoutesOverlap = _messages.BooleanField(1)
  checkServiceNetworkingUsePermission = _messages.BooleanField(2)
  computeIdempotencyWindow = _messages.StringField(3)
  consumer = _messages.StringField(4)
  consumerNetwork = _messages.StringField(5)
  description = _messages.StringField(6)
  ipPrefixLength = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  outsideAllocationPublicIpRange = _messages.StringField(8)
  privateIpv6GoogleAccess = _messages.StringField(9)
  purpose = _messages.StringField(10)
  region = _messages.StringField(11)
  requestedAddress = _messages.StringField(12)
  requestedRanges = _messages.StringField(13, repeated=True)
  role = _messages.StringField(14)
  secondaryIpRangeSpecs = _messages.MessageField('SecondaryIpRangeSpec', 15, repeated=True)
  subnetwork = _messages.StringField(16)
  subnetworkUsers = _messages.StringField(17, repeated=True)
  useCustomComputeIdempotencyWindow = _messages.BooleanField(18)


class Api(_messages.Message):
  r"""Api is a light-weight descriptor for an API Interface. Interfaces are
  also described as "protocol buffer services" in some contexts, such as by
  the "service" keyword in a .proto file, but they are different from API
  Services, which represent a concrete implementation of an interface as
  opposed to simply a description of methods and bindings. They are also
  sometimes simply referred to as "APIs" in other contexts, such as the name
  of this message itself. See https://cloud.google.com/apis/design/glossary
  for detailed terminology.

  Enums:
    SyntaxValueValuesEnum: The source syntax of the service.

  Fields:
    methods: The methods of this interface, in unspecified order.
    mixins: Included interfaces. See Mixin.
    name: The fully qualified name of this interface, including package name
      followed by the interface's simple name.
    options: Any metadata attached to the interface.
    sourceContext: Source context for the protocol buffer service represented
      by this message.
    syntax: The source syntax of the service.
    version: A version string for this interface. If specified, must have the
      form `major-version.minor-version`, as in `1.10`. If the minor version
      is omitted, it defaults to zero. If the entire version field is empty,
      the major version is derived from the package name, as outlined below.
      If the field is not empty, the version in the package name will be
      verified to be consistent with what is provided here. The versioning
      schema uses [semantic versioning](http://semver.org) where the major
      version number indicates a breaking change and the minor version an
      additive, non-breaking change. Both version numbers are signals to users
      what to expect from different versions, and should be carefully chosen
      based on the product plan. The major version is also reflected in the
      package name of the interface, which must end in `v`, as in
      `google.feature.v1`. For major versions 0 and 1, the suffix can be
      omitted. Zero major versions must only be used for experimental, non-GA
      interfaces.
  """

  class SyntaxValueValuesEnum(_messages.Enum):
    r"""The source syntax of the service.

    Values:
      SYNTAX_PROTO2: Syntax `proto2`.
      SYNTAX_PROTO3: Syntax `proto3`.
      SYNTAX_EDITIONS: Syntax `editions`.
    """
    SYNTAX_PROTO2 = 0
    SYNTAX_PROTO3 = 1
    SYNTAX_EDITIONS = 2

  methods = _messages.MessageField('Method', 1, repeated=True)
  mixins = _messages.MessageField('Mixin', 2, repeated=True)
  name = _messages.StringField(3)
  options = _messages.MessageField('Option', 4, repeated=True)
  sourceContext = _messages.MessageField('SourceContext', 5)
  syntax = _messages.EnumField('SyntaxValueValuesEnum', 6)
  version = _messages.StringField(7)


class AuthProvider(_messages.Message):
  r"""Configuration for an authentication provider, including support for
  [JSON Web Token (JWT)](https://tools.ietf.org/html/draft-ietf-oauth-json-
  web-token-32).

  Fields:
    audiences: The list of JWT [audiences](https://tools.ietf.org/html/draft-
      ietf-oauth-json-web-token-32#section-4.1.3). that are allowed to access.
      A JWT containing any of these audiences will be accepted. When this
      setting is absent, JWTs with audiences: -
      "https://[service.name]/[google.protobuf.Api.name]" -
      "https://[service.name]/" will be accepted. For example, if no audiences
      are in the setting, LibraryService API will accept JWTs with the
      following audiences: - https://library-
      example.googleapis.com/google.example.library.v1.LibraryService -
      https://library-example.googleapis.com/ Example: audiences:
      bookstore_android.apps.googleusercontent.com,
      bookstore_web.apps.googleusercontent.com
    authorizationUrl: Redirect URL if JWT token is required but not present or
      is expired. Implement authorizationUrl of securityDefinitions in OpenAPI
      spec.
    id: The unique identifier of the auth provider. It will be referred to by
      `AuthRequirement.provider_id`. Example: "bookstore_auth".
    issuer: Identifies the principal that issued the JWT. See
      https://tools.ietf.org/html/draft-ietf-oauth-json-web-
      token-32#section-4.1.1 Usually a URL or an email address. Example:
      https://securetoken.google.com Example:
      <EMAIL>
    jwksUri: URL of the provider's public key set to validate signature of the
      JWT. See [OpenID Discovery](https://openid.net/specs/openid-connect-
      discovery-1_0.html#ProviderMetadata). Optional if the key set document:
      - can be retrieved from [OpenID
      Discovery](https://openid.net/specs/openid-connect-discovery-1_0.html)
      of the issuer. - can be inferred from the email domain of the issuer
      (e.g. a Google service account). Example:
      https://www.googleapis.com/oauth2/v1/certs
    jwtLocations: Defines the locations to extract the JWT. For now it is only
      used by the Cloud Endpoints to store the OpenAPI extension [x-google-
      jwt-locations] (https://cloud.google.com/endpoints/docs/openapi/openapi-
      extensions#x-google-jwt-locations) JWT locations can be one of HTTP
      headers, URL query parameters or cookies. The rule is that the first
      match wins. If not specified, default to use following 3 locations: 1)
      Authorization: Bearer 2) x-goog-iap-jwt-assertion 3) access_token query
      parameter Default locations can be specified as followings:
      jwt_locations: - header: Authorization value_prefix: "Bearer " - header:
      x-goog-iap-jwt-assertion - query: access_token
  """

  audiences = _messages.StringField(1)
  authorizationUrl = _messages.StringField(2)
  id = _messages.StringField(3)
  issuer = _messages.StringField(4)
  jwksUri = _messages.StringField(5)
  jwtLocations = _messages.MessageField('JwtLocation', 6, repeated=True)


class AuthRequirement(_messages.Message):
  r"""User-defined authentication requirements, including support for [JSON
  Web Token (JWT)](https://tools.ietf.org/html/draft-ietf-oauth-json-web-
  token-32).

  Fields:
    audiences: NOTE: This will be deprecated soon, once AuthProvider.audiences
      is implemented and accepted in all the runtime components. The list of
      JWT [audiences](https://tools.ietf.org/html/draft-ietf-oauth-json-web-
      token-32#section-4.1.3). that are allowed to access. A JWT containing
      any of these audiences will be accepted. When this setting is absent,
      only JWTs with audience "https://Service_name/API_name" will be
      accepted. For example, if no audiences are in the setting,
      LibraryService API will only accept JWTs with the following audience
      "https://library-
      example.googleapis.com/google.example.library.v1.LibraryService".
      Example: audiences: bookstore_android.apps.googleusercontent.com,
      bookstore_web.apps.googleusercontent.com
    providerId: id from authentication provider. Example: provider_id:
      bookstore_auth
  """

  audiences = _messages.StringField(1)
  providerId = _messages.StringField(2)


class Authentication(_messages.Message):
  r"""`Authentication` defines the authentication configuration for API
  methods provided by an API service. Example: name: calendar.googleapis.com
  authentication: providers: - id: google_calendar_auth jwks_uri:
  https://www.googleapis.com/oauth2/v1/certs issuer:
  https://securetoken.google.com rules: - selector: "*" requirements:
  provider_id: google_calendar_auth - selector: google.calendar.Delegate
  oauth: canonical_scopes: https://www.googleapis.com/auth/calendar.read

  Fields:
    providers: Defines a set of authentication providers that a service
      supports.
    rules: A list of authentication rules that apply to individual API
      methods. **NOTE:** All service configuration rules follow "last one
      wins" order.
  """

  providers = _messages.MessageField('AuthProvider', 1, repeated=True)
  rules = _messages.MessageField('AuthenticationRule', 2, repeated=True)


class AuthenticationRule(_messages.Message):
  r"""Authentication rules for the service. By default, if a method has any
  authentication requirements, every request must include a valid credential
  matching one of the requirements. It's an error to include more than one
  kind of credential in a single request. If a method doesn't have any auth
  requirements, request credentials will be ignored.

  Fields:
    allowWithoutCredential: If true, the service accepts API keys without any
      other credential. This flag only applies to HTTP and gRPC requests.
    oauth: The requirements for OAuth credentials.
    requirements: Requirements for additional authentication providers.
    selector: Selects the methods to which this rule applies. Refer to
      selector for syntax details.
  """

  allowWithoutCredential = _messages.BooleanField(1)
  oauth = _messages.MessageField('OAuthRequirements', 2)
  requirements = _messages.MessageField('AuthRequirement', 3, repeated=True)
  selector = _messages.StringField(4)


class Backend(_messages.Message):
  r"""`Backend` defines the backend configuration for a service.

  Fields:
    rules: A list of API backend rules that apply to individual API methods.
      **NOTE:** All service configuration rules follow "last one wins" order.
  """

  rules = _messages.MessageField('BackendRule', 1, repeated=True)


class BackendRule(_messages.Message):
  r"""A backend rule provides configuration for an individual API element.

  Enums:
    PathTranslationValueValuesEnum:

  Messages:
    OverridesByRequestProtocolValue: The map between request protocol and the
      backend address.

  Fields:
    address: The address of the API backend. The scheme is used to determine
      the backend protocol and security. The following schemes are accepted:
      SCHEME PROTOCOL SECURITY http:// HTTP None https:// HTTP TLS grpc://
      gRPC None grpcs:// gRPC TLS It is recommended to explicitly include a
      scheme. Leaving out the scheme may cause constrasting behaviors across
      platforms. If the port is unspecified, the default is: - 80 for schemes
      without TLS - 443 for schemes with TLS For HTTP backends, use protocol
      to specify the protocol version.
    deadline: The number of seconds to wait for a response from a request. The
      default varies based on the request protocol and deployment environment.
    disableAuth: When disable_auth is true, a JWT ID token won't be generated
      and the original "Authorization" HTTP header will be preserved. If the
      header is used to carry the original token and is expected by the
      backend, this field must be set to true to preserve the header.
    jwtAudience: The JWT audience is used when generating a JWT ID token for
      the backend. This ID token will be added in the HTTP "authorization"
      header, and sent to the backend.
    minDeadline: Deprecated, do not use.
    operationDeadline: The number of seconds to wait for the completion of a
      long running operation. The default is no deadline.
    overridesByRequestProtocol: The map between request protocol and the
      backend address.
    pathTranslation: A PathTranslationValueValuesEnum attribute.
    protocol: The protocol used for sending a request to the backend. The
      supported values are "http/1.1" and "h2". The default value is inferred
      from the scheme in the address field: SCHEME PROTOCOL http:// http/1.1
      https:// http/1.1 grpc:// h2 grpcs:// h2 For secure HTTP backends
      (https://) that support HTTP/2, set this field to "h2" for improved
      performance. Configuring this field to non-default values is only
      supported for secure HTTP backends. This field will be ignored for all
      other backends. See https://www.iana.org/assignments/tls-extensiontype-
      values/tls-extensiontype-values.xhtml#alpn-protocol-ids for more details
      on the supported values.
    selector: Selects the methods to which this rule applies. Refer to
      selector for syntax details.
  """

  class PathTranslationValueValuesEnum(_messages.Enum):
    r"""PathTranslationValueValuesEnum enum type.

    Values:
      PATH_TRANSLATION_UNSPECIFIED: <no description>
      CONSTANT_ADDRESS: Use the backend address as-is, with no modification to
        the path. If the URL pattern contains variables, the variable names
        and values will be appended to the query string. If a query string
        parameter and a URL pattern variable have the same name, this may
        result in duplicate keys in the query string. # Examples Given the
        following operation config: Method path: /api/company/{cid}/user/{uid}
        Backend address: https://example.cloudfunctions.net/getUser Requests
        to the following request paths will call the backend at the translated
        path: Request path: /api/company/widgetworks/user/johndoe Translated:
        https://example.cloudfunctions.net/getUser?cid=widgetworks&uid=johndoe
        Request path: /api/company/widgetworks/user/johndoe?timezone=EST
        Translated: https://example.cloudfunctions.net/getUser?timezone=EST&ci
        d=widgetworks&uid=johndoe
      APPEND_PATH_TO_ADDRESS: The request path will be appended to the backend
        address. # Examples Given the following operation config: Method path:
        /api/company/{cid}/user/{uid} Backend address:
        https://example.appspot.com Requests to the following request paths
        will call the backend at the translated path: Request path:
        /api/company/widgetworks/user/johndoe Translated:
        https://example.appspot.com/api/company/widgetworks/user/johndoe
        Request path: /api/company/widgetworks/user/johndoe?timezone=EST
        Translated: https://example.appspot.com/api/company/widgetworks/user/j
        ohndoe?timezone=EST
    """
    PATH_TRANSLATION_UNSPECIFIED = 0
    CONSTANT_ADDRESS = 1
    APPEND_PATH_TO_ADDRESS = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class OverridesByRequestProtocolValue(_messages.Message):
    r"""The map between request protocol and the backend address.

    Messages:
      AdditionalProperty: An additional property for a
        OverridesByRequestProtocolValue object.

    Fields:
      additionalProperties: Additional properties of type
        OverridesByRequestProtocolValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a OverridesByRequestProtocolValue object.

      Fields:
        key: Name of the additional property.
        value: A BackendRule attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('BackendRule', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  address = _messages.StringField(1)
  deadline = _messages.FloatField(2)
  disableAuth = _messages.BooleanField(3)
  jwtAudience = _messages.StringField(4)
  minDeadline = _messages.FloatField(5)
  operationDeadline = _messages.FloatField(6)
  overridesByRequestProtocol = _messages.MessageField('OverridesByRequestProtocolValue', 7)
  pathTranslation = _messages.EnumField('PathTranslationValueValuesEnum', 8)
  protocol = _messages.StringField(9)
  selector = _messages.StringField(10)


class Billing(_messages.Message):
  r"""Billing related configuration of the service. The following example
  shows how to configure monitored resources and metrics for billing,
  `consumer_destinations` is the only supported destination and the monitored
  resources need at least one label key `cloud.googleapis.com/location` to
  indicate the location of the billing usage, using different monitored
  resources between monitoring and billing is recommended so they can be
  evolved independently: monitored_resources: - type:
  library.googleapis.com/billing_branch labels: - key:
  cloud.googleapis.com/location description: | Predefined label to support
  billing location restriction. - key: city description: | Custom label to
  define the city where the library branch is located in. - key: name
  description: Custom label to define the name of the library branch. metrics:
  - name: library.googleapis.com/book/borrowed_count metric_kind: DELTA
  value_type: INT64 unit: "1" billing: consumer_destinations: -
  monitored_resource: library.googleapis.com/billing_branch metrics: -
  library.googleapis.com/book/borrowed_count

  Fields:
    consumerDestinations: Billing configurations for sending metrics to the
      consumer project. There can be multiple consumer destinations per
      service, each one must have a different monitored resource type. A
      metric can be used in at most one consumer destination.
  """

  consumerDestinations = _messages.MessageField('BillingDestination', 1, repeated=True)


class BillingDestination(_messages.Message):
  r"""Configuration of a specific billing destination (Currently only support
  bill against consumer project).

  Fields:
    metrics: Names of the metrics to report to this billing destination. Each
      name must be defined in Service.metrics section.
    monitoredResource: The monitored resource type. The type must be defined
      in Service.monitored_resources section.
  """

  metrics = _messages.StringField(1, repeated=True)
  monitoredResource = _messages.StringField(2)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class ClientLibrarySettings(_messages.Message):
  r"""Details about how and where to publish client libraries.

  Enums:
    LaunchStageValueValuesEnum: Launch stage of this version of the API.

  Fields:
    cppSettings: Settings for C++ client libraries.
    dotnetSettings: Settings for .NET client libraries.
    goSettings: Settings for Go client libraries.
    javaSettings: Settings for legacy Java features, supported in the Service
      YAML.
    launchStage: Launch stage of this version of the API.
    nodeSettings: Settings for Node client libraries.
    phpSettings: Settings for PHP client libraries.
    pythonSettings: Settings for Python client libraries.
    restNumericEnums: When using transport=rest, the client request will
      encode enums as numbers rather than strings.
    rubySettings: Settings for Ruby client libraries.
    version: Version of the API to apply these settings to. This is the full
      protobuf package for the API, ending in the version element. Examples:
      "google.cloud.speech.v1" and "google.spanner.admin.database.v1".
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Launch stage of this version of the API.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our [Terms of Service](https://cloud.google.com/terms/) and the
        [Google Cloud Platform Subject to the Deprecation
        Policy](https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  cppSettings = _messages.MessageField('CppSettings', 1)
  dotnetSettings = _messages.MessageField('DotnetSettings', 2)
  goSettings = _messages.MessageField('GoSettings', 3)
  javaSettings = _messages.MessageField('JavaSettings', 4)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 5)
  nodeSettings = _messages.MessageField('NodeSettings', 6)
  phpSettings = _messages.MessageField('PhpSettings', 7)
  pythonSettings = _messages.MessageField('PythonSettings', 8)
  restNumericEnums = _messages.BooleanField(9)
  rubySettings = _messages.MessageField('RubySettings', 10)
  version = _messages.StringField(11)


class CloudSQLConfig(_messages.Message):
  r"""Cloud SQL configuration.

  Fields:
    service: Peering service used for peering with the Cloud SQL project.
    umbrellaNetwork: The name of the umbrella network in the Cloud SQL
      umbrella project.
    umbrellaProject: The project number of the Cloud SQL umbrella project.
  """

  service = _messages.StringField(1)
  umbrellaNetwork = _messages.StringField(2)
  umbrellaProject = _messages.IntegerField(3)


class CommonLanguageSettings(_messages.Message):
  r"""Required information for every language.

  Enums:
    DestinationsValueListEntryValuesEnum:

  Fields:
    destinations: The destination where API teams want this client library to
      be published.
    referenceDocsUri: Link to automatically generated reference documentation.
      Example: https://cloud.google.com/nodejs/docs/reference/asset/latest
  """

  class DestinationsValueListEntryValuesEnum(_messages.Enum):
    r"""DestinationsValueListEntryValuesEnum enum type.

    Values:
      CLIENT_LIBRARY_DESTINATION_UNSPECIFIED: Client libraries will neither be
        generated nor published to package managers.
      GITHUB: Generate the client library in a repo under
        github.com/googleapis, but don't publish it to package managers.
      PACKAGE_MANAGER: Publish the library to package managers like nuget.org
        and npmjs.com.
    """
    CLIENT_LIBRARY_DESTINATION_UNSPECIFIED = 0
    GITHUB = 1
    PACKAGE_MANAGER = 2

  destinations = _messages.EnumField('DestinationsValueListEntryValuesEnum', 1, repeated=True)
  referenceDocsUri = _messages.StringField(2)


class Connection(_messages.Message):
  r"""Represents a private connection resource. A private connection is
  implemented as a VPC Network Peering connection between a service producer's
  VPC network and a service consumer's VPC network.

  Fields:
    network: The name of service consumer's VPC network that's connected with
      service producer network, in the following format:
      `projects/{project}/global/networks/{network}`. `{project}` is a project
      number, such as in `12345` that includes the VPC service consumer's VPC
      network. `{network}` is the name of the service consumer's VPC network.
    peering: Output only. The name of the VPC Network Peering connection that
      was created by the service producer.
    reservedPeeringRanges: The name of one or more allocated IP address ranges
      for this service producer of type `PEERING`. Note that invoking
      CreateConnection method with a different range when connection is
      already established will not modify already provisioned service producer
      subnetworks. If CreateConnection method is invoked repeatedly to
      reconnect when peering connection had been disconnected on the consumer
      side, leaving this field empty will restore previously allocated IP
      ranges.
    service: Output only. The name of the peering service that's associated
      with this connection, in the following format: `services/{service
      name}`.
  """

  network = _messages.StringField(1)
  peering = _messages.StringField(2)
  reservedPeeringRanges = _messages.StringField(3, repeated=True)
  service = _messages.StringField(4)


class ConsumerConfig(_messages.Message):
  r"""Configuration information for a private service access connection.

  Fields:
    cloudsqlConfigs: Represents one or multiple Cloud SQL configurations.
    consumerExportCustomRoutes: Export custom routes flag value for peering
      from consumer to producer.
    consumerExportSubnetRoutesWithPublicIp: Export subnet routes with public
      ip flag value for peering from consumer to producer.
    consumerImportCustomRoutes: Import custom routes flag value for peering
      from consumer to producer.
    consumerImportSubnetRoutesWithPublicIp: Import subnet routes with public
      ip flag value for peering from consumer to producer.
    producerExportCustomRoutes: Export custom routes flag value for peering
      from producer to consumer.
    producerExportSubnetRoutesWithPublicIp: Export subnet routes with public
      ip flag value for peering from producer to consumer.
    producerImportCustomRoutes: Import custom routes flag value for peering
      from producer to consumer.
    producerImportSubnetRoutesWithPublicIp: Import subnet routes with public
      ip flag value for peering from producer to consumer.
    producerNetwork: Output only. The VPC host network that is used to host
      managed service instances. In the format,
      projects/{project}/global/networks/{network} where {project} is the
      project number e.g. '12345' and {network} is the network name.
    reservedRanges: Output only. The reserved ranges associated with this
      private service access connection.
    usedIpRanges: Output only. The IP ranges already in use by consumer or
      producer
    vpcScReferenceArchitectureEnabled: Output only. Indicates whether the VPC
      Service Controls reference architecture is configured for the producer
      VPC host network.
  """

  cloudsqlConfigs = _messages.MessageField('CloudSQLConfig', 1, repeated=True)
  consumerExportCustomRoutes = _messages.BooleanField(2)
  consumerExportSubnetRoutesWithPublicIp = _messages.BooleanField(3)
  consumerImportCustomRoutes = _messages.BooleanField(4)
  consumerImportSubnetRoutesWithPublicIp = _messages.BooleanField(5)
  producerExportCustomRoutes = _messages.BooleanField(6)
  producerExportSubnetRoutesWithPublicIp = _messages.BooleanField(7)
  producerImportCustomRoutes = _messages.BooleanField(8)
  producerImportSubnetRoutesWithPublicIp = _messages.BooleanField(9)
  producerNetwork = _messages.StringField(10)
  reservedRanges = _messages.MessageField('GoogleCloudServicenetworkingV1ConsumerConfigReservedRange', 11, repeated=True)
  usedIpRanges = _messages.StringField(12, repeated=True)
  vpcScReferenceArchitectureEnabled = _messages.BooleanField(13)


class ConsumerConfigMetadata(_messages.Message):
  r"""Metadata provided through GetOperation request for the LRO generated by
  UpdateConsumerConfig API.
  """



class ConsumerProject(_messages.Message):
  r"""Represents a consumer project.

  Fields:
    projectNum: Required. Project number of the consumer that is launching the
      service instance. It can own the network that is peered with Google or,
      be a service project in an XPN where the host project has the network.
  """

  projectNum = _messages.IntegerField(1)


class Context(_messages.Message):
  r"""`Context` defines which contexts an API requests. Example: context:
  rules: - selector: "*" requested: - google.rpc.context.ProjectContext -
  google.rpc.context.OriginContext The above specifies that all methods in the
  API request `google.rpc.context.ProjectContext` and
  `google.rpc.context.OriginContext`. Available context types are defined in
  package `google.rpc.context`. This also provides mechanism to allowlist any
  protobuf message extension that can be sent in grpc metadata using "x-goog-
  ext--bin" and "x-goog-ext--jspb" format. For example, list any service
  specific protobuf types that can appear in grpc metadata as follows in your
  yaml file: Example: context: rules: - selector:
  "google.example.library.v1.LibraryService.CreateBook"
  allowed_request_extensions: - google.foo.v1.NewExtension
  allowed_response_extensions: - google.foo.v1.NewExtension You can also
  specify extension ID instead of fully qualified extension name here.

  Fields:
    rules: A list of RPC context rules that apply to individual API methods.
      **NOTE:** All service configuration rules follow "last one wins" order.
  """

  rules = _messages.MessageField('ContextRule', 1, repeated=True)


class ContextRule(_messages.Message):
  r"""A context rule provides information about the context for an individual
  API element.

  Fields:
    allowedRequestExtensions: A list of full type names or extension IDs of
      extensions allowed in grpc side channel from client to backend.
    allowedResponseExtensions: A list of full type names or extension IDs of
      extensions allowed in grpc side channel from backend to client.
    provided: A list of full type names of provided contexts.
    requested: A list of full type names of requested contexts.
    selector: Selects the methods to which this rule applies. Refer to
      selector for syntax details.
  """

  allowedRequestExtensions = _messages.StringField(1, repeated=True)
  allowedResponseExtensions = _messages.StringField(2, repeated=True)
  provided = _messages.StringField(3, repeated=True)
  requested = _messages.StringField(4, repeated=True)
  selector = _messages.StringField(5)


class Control(_messages.Message):
  r"""Selects and configures the service controller used by the service.
  Example: control: environment: servicecontrol.googleapis.com

  Fields:
    environment: The service controller environment to use. If empty, no
      control plane feature (like quota and billing) will be enabled. The
      recommended value for most services is servicecontrol.googleapis.com
    methodPolicies: Defines policies applying to the API methods of the
      service.
  """

  environment = _messages.StringField(1)
  methodPolicies = _messages.MessageField('MethodPolicy', 2, repeated=True)


class CppSettings(_messages.Message):
  r"""Settings for C++ client libraries.

  Fields:
    common: Some settings.
  """

  common = _messages.MessageField('CommonLanguageSettings', 1)


class CustomError(_messages.Message):
  r"""Customize service error responses. For example, list any service
  specific protobuf types that can appear in error detail lists of error
  responses. Example: custom_error: types: - google.foo.v1.CustomError -
  google.foo.v1.AnotherError

  Fields:
    rules: The list of custom error rules that apply to individual API
      messages. **NOTE:** All service configuration rules follow "last one
      wins" order.
    types: The list of custom error detail types, e.g.
      'google.foo.v1.CustomError'.
  """

  rules = _messages.MessageField('CustomErrorRule', 1, repeated=True)
  types = _messages.StringField(2, repeated=True)


class CustomErrorRule(_messages.Message):
  r"""A custom error rule.

  Fields:
    isErrorType: Mark this message as possible payload in error response.
      Otherwise, objects of this type will be filtered when they appear in
      error payload.
    selector: Selects messages to which this rule applies. Refer to selector
      for syntax details.
  """

  isErrorType = _messages.BooleanField(1)
  selector = _messages.StringField(2)


class CustomHttpPattern(_messages.Message):
  r"""A custom pattern is used for defining custom HTTP verb.

  Fields:
    kind: The name of this custom HTTP verb.
    path: The path matched by this custom verb.
  """

  kind = _messages.StringField(1)
  path = _messages.StringField(2)


class DeleteConnectionMetadata(_messages.Message):
  r"""Metadata provided through GetOperation request for the LRO generated by
  Delete Connection API
  """



class DeleteConnectionRequest(_messages.Message):
  r"""Request to delete a private service access connection. The call will
  fail if there are any managed service instances using this connection.

  Fields:
    consumerNetwork: Required. The network that the consumer is using to
      connect with services. Must be in the form of
      projects/{project}/global/networks/{network} {project} is a project
      number, as in '12345' {network} is a network name.
  """

  consumerNetwork = _messages.StringField(1)


class DeletePeeredDnsDomainMetadata(_messages.Message):
  r"""Metadata provided through GetOperation request for the LRO generated by
  DeletePeeredDnsDomain API.
  """



class DisableVpcServiceControlsRequest(_messages.Message):
  r"""Request to disable VPC service controls.

  Fields:
    consumerNetwork: Required. The network that the consumer is using to
      connect with services. Must be in the form of
      projects/{project}/global/networks/{network} {project} is a project
      number, as in '12345' {network} is network name.
  """

  consumerNetwork = _messages.StringField(1)


class DnsRecordSet(_messages.Message):
  r"""Represents a DNS record set resource.

  Fields:
    data: Required. As defined in RFC 1035 (section 5) and RFC 1034 (section
      3.6.1) for examples see https://cloud.google.com/dns/records/json-
      record.
    domain: Required. The DNS or domain name of the record set, e.g.
      `test.example.com`. Cloud DNS requires that a DNS suffix ends with a
      trailing dot.
    ttl: Required. The period of time for which this RecordSet can be cached
      by resolvers.
    type: Required. The identifier of a supported record type.
  """

  data = _messages.StringField(1, repeated=True)
  domain = _messages.StringField(2)
  ttl = _messages.StringField(3)
  type = _messages.StringField(4)


class DnsZone(_messages.Message):
  r"""Represents a DNS zone resource.

  Fields:
    dnsSuffix: The DNS name suffix of this zone e.g. `example.com.`. Cloud DNS
      requires that a DNS suffix ends with a trailing dot.
    name: User assigned name for this resource. Must be unique within the
      project. The name must be 1-63 characters long, must begin with a
      letter, end with a letter or digit, and only contain lowercase letters,
      digits or dashes.
  """

  dnsSuffix = _messages.StringField(1)
  name = _messages.StringField(2)


class DnsZonePair(_messages.Message):
  r"""* Represents a pair of private and peering DNS zone resources. *

  Fields:
    consumerPeeringZone: The DNS peering zone in the consumer project.
    producerPrivateZone: The private DNS zone in the shared producer host
      project.
  """

  consumerPeeringZone = _messages.MessageField('DnsZone', 1)
  producerPrivateZone = _messages.MessageField('DnsZone', 2)


class Documentation(_messages.Message):
  r"""`Documentation` provides the information for describing a service.
  Example: documentation: summary: > The Google Calendar API gives access to
  most calendar features. pages: - name: Overview content: (== include
  google/foo/overview.md ==) - name: Tutorial content: (== include
  google/foo/tutorial.md ==) subpages: - name: Java content: (== include
  google/foo/tutorial_java.md ==) rules: - selector:
  google.calendar.Calendar.Get description: > ... - selector:
  google.calendar.Calendar.Put description: > ... Documentation is provided in
  markdown syntax. In addition to standard markdown features, definition
  lists, tables and fenced code blocks are supported. Section headers can be
  provided and are interpreted relative to the section nesting of the context
  where a documentation fragment is embedded. Documentation from the IDL is
  merged with documentation defined via the config at normalization time,
  where documentation provided by config rules overrides IDL provided. A
  number of constructs specific to the API platform are supported in
  documentation text. In order to reference a proto element, the following
  notation can be used: [fully.qualified.proto.name][] To override the display
  text used for the link, this can be used: [display
  text][fully.qualified.proto.name] Text can be excluded from doc using the
  following notation: (-- internal comment --) A few directives are available
  in documentation. Note that directives must appear on a single line to be
  properly identified. The `include` directive includes a markdown file from
  an external source: (== include path/to/file ==) The `resource_for`
  directive marks a message to be the resource of a collection in REST view.
  If it is not specified, tools attempt to infer the resource from the
  operations in a collection: (== resource_for v1.shelves.books ==) The
  directive `suppress_warning` does not directly affect documentation and is
  documented together with service config validation.

  Fields:
    documentationRootUrl: The URL to the root of documentation.
    overview: Declares a single overview page. For example: documentation:
      summary: ... overview: (== include overview.md ==) This is a shortcut
      for the following declaration (using pages style): documentation:
      summary: ... pages: - name: Overview content: (== include overview.md
      ==) Note: you cannot specify both `overview` field and `pages` field.
    pages: The top level pages for the documentation set.
    rules: A list of documentation rules that apply to individual API
      elements. **NOTE:** All service configuration rules follow "last one
      wins" order.
    sectionOverrides: Specifies section and content to override boilerplate
      content provided by go/api-docgen. Currently overrides following
      sections: 1. rest.service.client_libraries
    serviceRootUrl: Specifies the service root url if the default one (the
      service name from the yaml file) is not suitable. This can be seen in
      any fully specified service urls as well as sections that show a base
      that other urls are relative to.
    summary: A short description of what the service does. The summary must be
      plain text. It becomes the overview of the service displayed in Google
      Cloud Console. NOTE: This field is equivalent to the standard field
      `description`.
  """

  documentationRootUrl = _messages.StringField(1)
  overview = _messages.StringField(2)
  pages = _messages.MessageField('Page', 3, repeated=True)
  rules = _messages.MessageField('DocumentationRule', 4, repeated=True)
  sectionOverrides = _messages.MessageField('Page', 5, repeated=True)
  serviceRootUrl = _messages.StringField(6)
  summary = _messages.StringField(7)


class DocumentationRule(_messages.Message):
  r"""A documentation rule provides information about individual API elements.

  Fields:
    deprecationDescription: Deprecation description of the selected
      element(s). It can be provided if an element is marked as `deprecated`.
    description: Description of the selected proto element (e.g. a message, a
      method, a 'service' definition, or a field). Defaults to leading &
      trailing comments taken from the proto source definition of the proto
      element.
    disableReplacementWords: String of comma or space separated case-sensitive
      words for which method/field name replacement will be disabled by
      go/api-docgen.
    selector: The selector is a comma-separated list of patterns for any
      element such as a method, a field, an enum value. Each pattern is a
      qualified name of the element which may end in "*", indicating a
      wildcard. Wildcards are only allowed at the end and for a whole
      component of the qualified name, i.e. "foo.*" is ok, but not "foo.b*" or
      "foo.*.bar". A wildcard will match one or more components. To specify a
      default for all applicable elements, the whole pattern "*" is used.
  """

  deprecationDescription = _messages.StringField(1)
  description = _messages.StringField(2)
  disableReplacementWords = _messages.StringField(3)
  selector = _messages.StringField(4)


class DotnetSettings(_messages.Message):
  r"""Settings for Dotnet client libraries.

  Messages:
    RenamedResourcesValue: Map from full resource types to the effective short
      name for the resource. This is used when otherwise resource named from
      different services would cause naming collisions. Example entry:
      "datalabeling.googleapis.com/Dataset": "DataLabelingDataset"
    RenamedServicesValue: Map from original service names to renamed versions.
      This is used when the default generated types would cause a naming
      conflict. (Neither name is fully-qualified.) Example: Subscriber to
      SubscriberServiceApi.

  Fields:
    common: Some settings.
    forcedNamespaceAliases: Namespaces which must be aliased in snippets due
      to a known (but non-generator-predictable) naming collision
    handwrittenSignatures: Method signatures (in the form
      "service.method(signature)") which are provided separately, so shouldn't
      be generated. Snippets *calling* these methods are still generated,
      however.
    ignoredResources: List of full resource types to ignore during generation.
      This is typically used for API-specific Location resources, which should
      be handled by the generator as if they were actually the common Location
      resources. Example entry: "documentai.googleapis.com/Location"
    renamedResources: Map from full resource types to the effective short name
      for the resource. This is used when otherwise resource named from
      different services would cause naming collisions. Example entry:
      "datalabeling.googleapis.com/Dataset": "DataLabelingDataset"
    renamedServices: Map from original service names to renamed versions. This
      is used when the default generated types would cause a naming conflict.
      (Neither name is fully-qualified.) Example: Subscriber to
      SubscriberServiceApi.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RenamedResourcesValue(_messages.Message):
    r"""Map from full resource types to the effective short name for the
    resource. This is used when otherwise resource named from different
    services would cause naming collisions. Example entry:
    "datalabeling.googleapis.com/Dataset": "DataLabelingDataset"

    Messages:
      AdditionalProperty: An additional property for a RenamedResourcesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        RenamedResourcesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RenamedResourcesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RenamedServicesValue(_messages.Message):
    r"""Map from original service names to renamed versions. This is used when
    the default generated types would cause a naming conflict. (Neither name
    is fully-qualified.) Example: Subscriber to SubscriberServiceApi.

    Messages:
      AdditionalProperty: An additional property for a RenamedServicesValue
        object.

    Fields:
      additionalProperties: Additional properties of type RenamedServicesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RenamedServicesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  common = _messages.MessageField('CommonLanguageSettings', 1)
  forcedNamespaceAliases = _messages.StringField(2, repeated=True)
  handwrittenSignatures = _messages.StringField(3, repeated=True)
  ignoredResources = _messages.StringField(4, repeated=True)
  renamedResources = _messages.MessageField('RenamedResourcesValue', 5)
  renamedServices = _messages.MessageField('RenamedServicesValue', 6)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EnableVpcServiceControlsRequest(_messages.Message):
  r"""Request to enable VPC service controls.

  Fields:
    consumerNetwork: Required. The network that the consumer is using to
      connect with services. Must be in the form of
      projects/{project}/global/networks/{network} {project} is a project
      number, as in '12345' {network} is network name.
  """

  consumerNetwork = _messages.StringField(1)


class Endpoint(_messages.Message):
  r"""`Endpoint` describes a network address of a service that serves a set of
  APIs. It is commonly known as a service endpoint. A service may expose any
  number of service endpoints, and all service endpoints share the same
  service definition, such as quota limits and monitoring metrics. Example:
  type: google.api.Service name: library-example.googleapis.com endpoints: #
  Declares network address `https://library-example.googleapis.com` # for
  service `library-example.googleapis.com`. The `https` scheme # is implicit
  for all service endpoints. Other schemes may be # supported in the future. -
  name: library-example.googleapis.com allow_cors: false - name: content-
  staging-library-example.googleapis.com # Allows HTTP OPTIONS calls to be
  passed to the API frontend, for it # to decide whether the subsequent cross-
  origin request is allowed # to proceed. allow_cors: true

  Fields:
    aliases: Unimplemented. Dot not use. DEPRECATED: This field is no longer
      supported. Instead of using aliases, please specify multiple
      google.api.Endpoint for each of the intended aliases. Additional names
      that this endpoint will be hosted on.
    allowCors: Allowing [CORS](https://en.wikipedia.org/wiki/Cross-
      origin_resource_sharing), aka cross-domain traffic, would allow the
      backends served from this endpoint to receive and respond to HTTP
      OPTIONS requests. The response will be used by the browser to determine
      whether the subsequent cross-origin request is allowed to proceed.
    name: The canonical name of this endpoint.
    target: The specification of an Internet routable address of API frontend
      that will handle requests to this [API
      Endpoint](https://cloud.google.com/apis/design/glossary). It should be
      either a valid IPv4 address or a fully-qualified domain name. For
      example, "*******" or "myservice.appspot.com".
  """

  aliases = _messages.StringField(1, repeated=True)
  allowCors = _messages.BooleanField(2)
  name = _messages.StringField(3)
  target = _messages.StringField(4)


class Enum(_messages.Message):
  r"""Enum type definition.

  Enums:
    SyntaxValueValuesEnum: The source syntax.

  Fields:
    edition: The source edition string, only valid when syntax is
      SYNTAX_EDITIONS.
    enumvalue: Enum value definitions.
    name: Enum type name.
    options: Protocol buffer options.
    sourceContext: The source context.
    syntax: The source syntax.
  """

  class SyntaxValueValuesEnum(_messages.Enum):
    r"""The source syntax.

    Values:
      SYNTAX_PROTO2: Syntax `proto2`.
      SYNTAX_PROTO3: Syntax `proto3`.
      SYNTAX_EDITIONS: Syntax `editions`.
    """
    SYNTAX_PROTO2 = 0
    SYNTAX_PROTO3 = 1
    SYNTAX_EDITIONS = 2

  edition = _messages.StringField(1)
  enumvalue = _messages.MessageField('EnumValue', 2, repeated=True)
  name = _messages.StringField(3)
  options = _messages.MessageField('Option', 4, repeated=True)
  sourceContext = _messages.MessageField('SourceContext', 5)
  syntax = _messages.EnumField('SyntaxValueValuesEnum', 6)


class EnumValue(_messages.Message):
  r"""Enum value definition.

  Fields:
    name: Enum value name.
    number: Enum value number.
    options: Protocol buffer options.
  """

  name = _messages.StringField(1)
  number = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  options = _messages.MessageField('Option', 3, repeated=True)


class Field(_messages.Message):
  r"""A single field of a message type.

  Enums:
    CardinalityValueValuesEnum: The field cardinality.
    KindValueValuesEnum: The field type.

  Fields:
    cardinality: The field cardinality.
    defaultValue: The string value of the default value of this field. Proto2
      syntax only.
    jsonName: The field JSON name.
    kind: The field type.
    name: The field name.
    number: The field number.
    oneofIndex: The index of the field type in `Type.oneofs`, for message or
      enumeration types. The first type has index 1; zero means the type is
      not in the list.
    options: The protocol buffer options.
    packed: Whether to use alternative packed wire representation.
    typeUrl: The field type URL, without the scheme, for message or
      enumeration types. Example:
      `"type.googleapis.com/google.protobuf.Timestamp"`.
  """

  class CardinalityValueValuesEnum(_messages.Enum):
    r"""The field cardinality.

    Values:
      CARDINALITY_UNKNOWN: For fields with unknown cardinality.
      CARDINALITY_OPTIONAL: For optional fields.
      CARDINALITY_REQUIRED: For required fields. Proto2 syntax only.
      CARDINALITY_REPEATED: For repeated fields.
    """
    CARDINALITY_UNKNOWN = 0
    CARDINALITY_OPTIONAL = 1
    CARDINALITY_REQUIRED = 2
    CARDINALITY_REPEATED = 3

  class KindValueValuesEnum(_messages.Enum):
    r"""The field type.

    Values:
      TYPE_UNKNOWN: Field type unknown.
      TYPE_DOUBLE: Field type double.
      TYPE_FLOAT: Field type float.
      TYPE_INT64: Field type int64.
      TYPE_UINT64: Field type uint64.
      TYPE_INT32: Field type int32.
      TYPE_FIXED64: Field type fixed64.
      TYPE_FIXED32: Field type fixed32.
      TYPE_BOOL: Field type bool.
      TYPE_STRING: Field type string.
      TYPE_GROUP: Field type group. Proto2 syntax only, and deprecated.
      TYPE_MESSAGE: Field type message.
      TYPE_BYTES: Field type bytes.
      TYPE_UINT32: Field type uint32.
      TYPE_ENUM: Field type enum.
      TYPE_SFIXED32: Field type sfixed32.
      TYPE_SFIXED64: Field type sfixed64.
      TYPE_SINT32: Field type sint32.
      TYPE_SINT64: Field type sint64.
    """
    TYPE_UNKNOWN = 0
    TYPE_DOUBLE = 1
    TYPE_FLOAT = 2
    TYPE_INT64 = 3
    TYPE_UINT64 = 4
    TYPE_INT32 = 5
    TYPE_FIXED64 = 6
    TYPE_FIXED32 = 7
    TYPE_BOOL = 8
    TYPE_STRING = 9
    TYPE_GROUP = 10
    TYPE_MESSAGE = 11
    TYPE_BYTES = 12
    TYPE_UINT32 = 13
    TYPE_ENUM = 14
    TYPE_SFIXED32 = 15
    TYPE_SFIXED64 = 16
    TYPE_SINT32 = 17
    TYPE_SINT64 = 18

  cardinality = _messages.EnumField('CardinalityValueValuesEnum', 1)
  defaultValue = _messages.StringField(2)
  jsonName = _messages.StringField(3)
  kind = _messages.EnumField('KindValueValuesEnum', 4)
  name = _messages.StringField(5)
  number = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  oneofIndex = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  options = _messages.MessageField('Option', 8, repeated=True)
  packed = _messages.BooleanField(9)
  typeUrl = _messages.StringField(10)


class FieldPolicy(_messages.Message):
  r"""Google API Policy Annotation This message defines a simple API policy
  annotation that can be used to annotate API request and response message
  fields with applicable policies. One field may have multiple applicable
  policies that must all be satisfied before a request can be processed. This
  policy annotation is used to generate the overall policy that will be used
  for automatic runtime policy enforcement and documentation generation.

  Fields:
    resourcePermission: Specifies the required permission(s) for the resource
      referred to by the field. It requires the field contains a valid
      resource reference, and the request must pass the permission checks to
      proceed. For example, "resourcemanager.projects.get".
    resourceType: Specifies the resource type for the resource referred to by
      the field.
    selector: Selects one or more request or response message fields to apply
      this `FieldPolicy`. When a `FieldPolicy` is used in proto annotation,
      the selector must be left as empty. The service config generator will
      automatically fill the correct value. When a `FieldPolicy` is used in
      service config, the selector must be a comma-separated string with valid
      request or response field paths, such as "foo.bar" or "foo.bar,foo.baz".
  """

  resourcePermission = _messages.StringField(1)
  resourceType = _messages.StringField(2)
  selector = _messages.StringField(3)


class GetDnsZoneResponse(_messages.Message):
  r"""Represents managed DNS zones created in the shared Producer host and
  consumer projects.

  Fields:
    consumerPeeringZone: The DNS peering zone created in the consumer project.
    producerPrivateZone: The private DNS zone created in the shared producer
      host project.
  """

  consumerPeeringZone = _messages.MessageField('DnsZone', 1)
  producerPrivateZone = _messages.MessageField('DnsZone', 2)


class GoSettings(_messages.Message):
  r"""Settings for Go client libraries.

  Fields:
    common: Some settings.
  """

  common = _messages.MessageField('CommonLanguageSettings', 1)


class GoogleCloudServicenetworkingV1ConsumerConfigReservedRange(_messages.Message):
  r"""Allocated IP address ranges for this private service access connection.

  Fields:
    address: The starting address of the reserved range. The address must be a
      valid IPv4 address in the x.x.x.x format. This value combined with the
      IP prefix length is the CIDR range for the reserved range.
    ipPrefixLength: The prefix length of the reserved range.
    name: The name of the reserved range.
  """

  address = _messages.StringField(1)
  ipPrefixLength = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  name = _messages.StringField(3)


class GoogleCloudServicenetworkingV1betaConnection(_messages.Message):
  r"""Represents a private connection resource. A private connection is
  implemented as a VPC Network Peering connection between a service producer's
  VPC network and a service consumer's VPC network.

  Fields:
    network: The name of service consumer's VPC network that's connected with
      service producer network, in the following format:
      `projects/{project}/global/networks/{network}`. `{project}` is a project
      number, such as in `12345` that includes the VPC service consumer's VPC
      network. `{network}` is the name of the service consumer's VPC network.
    peering: Output only. The name of the VPC Network Peering connection that
      was created by the service producer.
    reservedPeeringRanges: The name of one or more allocated IP address ranges
      for this service producer of type `PEERING`. Note that invoking this
      method with a different range when connection is already established
      will not modify already provisioned service producer subnetworks.
    service: Output only. The name of the peering service that's associated
      with this connection, in the following format: `services/{service
      name}`.
  """

  network = _messages.StringField(1)
  peering = _messages.StringField(2)
  reservedPeeringRanges = _messages.StringField(3, repeated=True)
  service = _messages.StringField(4)


class GoogleCloudServicenetworkingV1betaSubnetwork(_messages.Message):
  r"""Represents a subnet that was created or discovered by a private access
  management service.

  Fields:
    ipCidrRange: Subnetwork CIDR range in `10.x.x.x/y` format.
    name: Subnetwork name. See https://cloud.google.com/compute/docs/vpc/
    network: In the Shared VPC host project, the VPC network that's peered
      with the consumer network. For example:
      `projects/1234321/global/networks/host-network`
    outsideAllocation: This is a discovered subnet that is not within the
      current consumer allocated ranges.
  """

  ipCidrRange = _messages.StringField(1)
  name = _messages.StringField(2)
  network = _messages.StringField(3)
  outsideAllocation = _messages.BooleanField(4)


class Http(_messages.Message):
  r"""Defines the HTTP configuration for an API service. It contains a list of
  HttpRule, each specifying the mapping of an RPC method to one or more HTTP
  REST API methods.

  Fields:
    fullyDecodeReservedExpansion: When set to true, URL path parameters will
      be fully URI-decoded except in cases of single segment matches in
      reserved expansion, where "%2F" will be left encoded. The default
      behavior is to not decode RFC 6570 reserved characters in multi segment
      matches.
    rules: A list of HTTP configuration rules that apply to individual API
      methods. **NOTE:** All service configuration rules follow "last one
      wins" order.
  """

  fullyDecodeReservedExpansion = _messages.BooleanField(1)
  rules = _messages.MessageField('HttpRule', 2, repeated=True)


class HttpRule(_messages.Message):
  r"""# gRPC Transcoding gRPC Transcoding is a feature for mapping between a
  gRPC method and one or more HTTP REST endpoints. It allows developers to
  build a single API service that supports both gRPC APIs and REST APIs. Many
  systems, including [Google APIs](https://github.com/googleapis/googleapis),
  [Cloud Endpoints](https://cloud.google.com/endpoints), [gRPC
  Gateway](https://github.com/grpc-ecosystem/grpc-gateway), and
  [Envoy](https://github.com/envoyproxy/envoy) proxy support this feature and
  use it for large scale production services. `HttpRule` defines the schema of
  the gRPC/REST mapping. The mapping specifies how different portions of the
  gRPC request message are mapped to the URL path, URL query parameters, and
  HTTP request body. It also controls how the gRPC response message is mapped
  to the HTTP response body. `HttpRule` is typically specified as an
  `google.api.http` annotation on the gRPC method. Each mapping specifies a
  URL path template and an HTTP method. The path template may refer to one or
  more fields in the gRPC request message, as long as each field is a non-
  repeated field with a primitive (non-message) type. The path template
  controls how fields of the request message are mapped to the URL path.
  Example: service Messaging { rpc GetMessage(GetMessageRequest) returns
  (Message) { option (google.api.http) = { get: "/v1/{name=messages/*}" }; } }
  message GetMessageRequest { string name = 1; // Mapped to URL path. }
  message Message { string text = 1; // The resource content. } This enables
  an HTTP REST to gRPC mapping as below: HTTP | gRPC -----|----- `GET
  /v1/messages/123456` | `GetMessage(name: "messages/123456")` Any fields in
  the request message which are not bound by the path template automatically
  become HTTP query parameters if there is no HTTP request body. For example:
  service Messaging { rpc GetMessage(GetMessageRequest) returns (Message) {
  option (google.api.http) = { get:"/v1/messages/{message_id}" }; } } message
  GetMessageRequest { message SubMessage { string subfield = 1; } string
  message_id = 1; // Mapped to URL path. int64 revision = 2; // Mapped to URL
  query parameter `revision`. SubMessage sub = 3; // Mapped to URL query
  parameter `sub.subfield`. } This enables a HTTP JSON to RPC mapping as
  below: HTTP | gRPC -----|----- `GET
  /v1/messages/123456?revision=2&sub.subfield=foo` | `GetMessage(message_id:
  "123456" revision: 2 sub: SubMessage(subfield: "foo"))` Note that fields
  which are mapped to URL query parameters must have a primitive type or a
  repeated primitive type or a non-repeated message type. In the case of a
  repeated type, the parameter can be repeated in the URL as
  `...?param=A&param=B`. In the case of a message type, each field of the
  message is mapped to a separate parameter, such as
  `...?foo.a=A&foo.b=B&foo.c=C`. For HTTP methods that allow a request body,
  the `body` field specifies the mapping. Consider a REST update method on the
  message resource collection: service Messaging { rpc
  UpdateMessage(UpdateMessageRequest) returns (Message) { option
  (google.api.http) = { patch: "/v1/messages/{message_id}" body: "message" };
  } } message UpdateMessageRequest { string message_id = 1; // mapped to the
  URL Message message = 2; // mapped to the body } The following HTTP JSON to
  RPC mapping is enabled, where the representation of the JSON in the request
  body is determined by protos JSON encoding: HTTP | gRPC -----|----- `PATCH
  /v1/messages/123456 { "text": "Hi!" }` | `UpdateMessage(message_id: "123456"
  message { text: "Hi!" })` The special name `*` can be used in the body
  mapping to define that every field not bound by the path template should be
  mapped to the request body. This enables the following alternative
  definition of the update method: service Messaging { rpc
  UpdateMessage(Message) returns (Message) { option (google.api.http) = {
  patch: "/v1/messages/{message_id}" body: "*" }; } } message Message { string
  message_id = 1; string text = 2; } The following HTTP JSON to RPC mapping is
  enabled: HTTP | gRPC -----|----- `PATCH /v1/messages/123456 { "text": "Hi!"
  }` | `UpdateMessage(message_id: "123456" text: "Hi!")` Note that when using
  `*` in the body mapping, it is not possible to have HTTP parameters, as all
  fields not bound by the path end in the body. This makes this option more
  rarely used in practice when defining REST APIs. The common usage of `*` is
  in custom methods which don't use the URL at all for transferring data. It
  is possible to define multiple HTTP methods for one RPC by using the
  `additional_bindings` option. Example: service Messaging { rpc
  GetMessage(GetMessageRequest) returns (Message) { option (google.api.http) =
  { get: "/v1/messages/{message_id}" additional_bindings { get:
  "/v1/users/{user_id}/messages/{message_id}" } }; } } message
  GetMessageRequest { string message_id = 1; string user_id = 2; } This
  enables the following two alternative HTTP JSON to RPC mappings: HTTP | gRPC
  -----|----- `GET /v1/messages/123456` | `GetMessage(message_id: "123456")`
  `GET /v1/users/me/messages/123456` | `GetMessage(user_id: "me" message_id:
  "123456")` ## Rules for HTTP mapping 1. Leaf request fields (recursive
  expansion nested messages in the request message) are classified into three
  categories: - Fields referred by the path template. They are passed via the
  URL path. - Fields referred by the HttpRule.body. They are passed via the
  HTTP request body. - All other fields are passed via the URL query
  parameters, and the parameter name is the field path in the request message.
  A repeated field can be represented as multiple query parameters under the
  same name. 2. If HttpRule.body is "*", there is no URL query parameter, all
  fields are passed via URL path and HTTP request body. 3. If HttpRule.body is
  omitted, there is no HTTP request body, all fields are passed via URL path
  and URL query parameters. ### Path template syntax Template = "/" Segments [
  Verb ] ; Segments = Segment { "/" Segment } ; Segment = "*" | "**" | LITERAL
  | Variable ; Variable = "{" FieldPath [ "=" Segments ] "}" ; FieldPath =
  IDENT { "." IDENT } ; Verb = ":" LITERAL ; The syntax `*` matches a single
  URL path segment. The syntax `**` matches zero or more URL path segments,
  which must be the last part of the URL path except the `Verb`. The syntax
  `Variable` matches part of the URL path as specified by its template. A
  variable template must not contain other variables. If a variable matches a
  single path segment, its template may be omitted, e.g. `{var}` is equivalent
  to `{var=*}`. The syntax `LITERAL` matches literal text in the URL path. If
  the `LITERAL` contains any reserved character, such characters should be
  percent-encoded before the matching. If a variable contains exactly one path
  segment, such as `"{var}"` or `"{var=*}"`, when such a variable is expanded
  into a URL path on the client side, all characters except `[-_.~0-9a-zA-Z]`
  are percent-encoded. The server side does the reverse decoding. Such
  variables show up in the [Discovery
  Document](https://developers.google.com/discovery/v1/reference/apis) as
  `{var}`. If a variable contains multiple path segments, such as
  `"{var=foo/*}"` or `"{var=**}"`, when such a variable is expanded into a URL
  path on the client side, all characters except `[-_.~/0-9a-zA-Z]` are
  percent-encoded. The server side does the reverse decoding, except "%2F" and
  "%2f" are left unchanged. Such variables show up in the [Discovery
  Document](https://developers.google.com/discovery/v1/reference/apis) as
  `{+var}`. ## Using gRPC API Service Configuration gRPC API Service
  Configuration (service config) is a configuration language for configuring a
  gRPC service to become a user-facing product. The service config is simply
  the YAML representation of the `google.api.Service` proto message. As an
  alternative to annotating your proto file, you can configure gRPC
  transcoding in your service config YAML files. You do this by specifying a
  `HttpRule` that maps the gRPC method to a REST endpoint, achieving the same
  effect as the proto annotation. This can be particularly useful if you have
  a proto that is reused in multiple services. Note that any transcoding
  specified in the service config will override any matching transcoding
  configuration in the proto. Example: http: rules: # Selects a gRPC method
  and applies HttpRule to it. - selector: example.v1.Messaging.GetMessage get:
  /v1/messages/{message_id}/{sub.subfield} ## Special notes When gRPC
  Transcoding is used to map a gRPC to JSON REST endpoints, the proto to JSON
  conversion must follow the [proto3
  specification](https://developers.google.com/protocol-
  buffers/docs/proto3#json). While the single segment variable follows the
  semantics of [RFC 6570](https://tools.ietf.org/html/rfc6570) Section 3.2.2
  Simple String Expansion, the multi segment variable **does not** follow RFC
  6570 Section 3.2.3 Reserved Expansion. The reason is that the Reserved
  Expansion does not expand special characters like `?` and `#`, which would
  lead to invalid URLs. As the result, gRPC Transcoding uses a custom encoding
  for multi segment variables. The path variables **must not** refer to any
  repeated or mapped field, because client libraries are not capable of
  handling such variable expansion. The path variables **must not** capture
  the leading "/" character. The reason is that the most common use case
  "{var}" does not capture the leading "/" character. For consistency, all
  path variables must share the same behavior. Repeated message fields must
  not be mapped to URL query parameters, because no client library can support
  such complicated mapping. If an API needs to use a JSON array for request or
  response body, it can map the request or response body to a repeated field.
  However, some gRPC Transcoding implementations may not support this feature.

  Fields:
    additionalBindings: Additional HTTP bindings for the selector. Nested
      bindings must not contain an `additional_bindings` field themselves
      (that is, the nesting may only be one level deep).
    body: The name of the request field whose value is mapped to the HTTP
      request body, or `*` for mapping all request fields not captured by the
      path pattern to the HTTP body, or omitted for not having any HTTP
      request body. NOTE: the referred field must be present at the top-level
      of the request message type.
    custom: The custom pattern is used for specifying an HTTP method that is
      not included in the `pattern` field, such as HEAD, or "*" to leave the
      HTTP method unspecified for this rule. The wild-card rule is useful for
      services that provide content to Web (HTML) clients.
    delete: Maps to HTTP DELETE. Used for deleting a resource.
    get: Maps to HTTP GET. Used for listing and getting information about
      resources.
    patch: Maps to HTTP PATCH. Used for updating a resource.
    post: Maps to HTTP POST. Used for creating a resource or performing an
      action.
    put: Maps to HTTP PUT. Used for replacing a resource.
    responseBody: Optional. The name of the response field whose value is
      mapped to the HTTP response body. When omitted, the entire response
      message will be used as the HTTP response body. NOTE: The referred field
      must be present at the top-level of the response message type.
    selector: Selects a method to which this rule applies. Refer to selector
      for syntax details.
  """

  additionalBindings = _messages.MessageField('HttpRule', 1, repeated=True)
  body = _messages.StringField(2)
  custom = _messages.MessageField('CustomHttpPattern', 3)
  delete = _messages.StringField(4)
  get = _messages.StringField(5)
  patch = _messages.StringField(6)
  post = _messages.StringField(7)
  put = _messages.StringField(8)
  responseBody = _messages.StringField(9)
  selector = _messages.StringField(10)


class JavaSettings(_messages.Message):
  r"""Settings for Java client libraries.

  Messages:
    ServiceClassNamesValue: Configure the Java class name to use instead of
      the service's for its corresponding generated GAPIC client. Keys are
      fully-qualified service names as they appear in the protobuf (including
      the full the language_settings.java.interface_names" field in
      gapic.yaml. API teams should otherwise use the service name as it
      appears in the protobuf. Example of a YAML configuration:: publishing:
      java_settings: service_class_names: - google.pubsub.v1.Publisher:
      TopicAdmin - google.pubsub.v1.Subscriber: SubscriptionAdmin

  Fields:
    common: Some settings.
    libraryPackage: The package name to use in Java. Clobbers the java_package
      option set in the protobuf. This should be used **only** by APIs who
      have already set the language_settings.java.package_name" field in
      gapic.yaml. API teams should use the protobuf java_package option where
      possible. Example of a YAML configuration:: publishing: java_settings:
      library_package: com.google.cloud.pubsub.v1
    serviceClassNames: Configure the Java class name to use instead of the
      service's for its corresponding generated GAPIC client. Keys are fully-
      qualified service names as they appear in the protobuf (including the
      full the language_settings.java.interface_names" field in gapic.yaml.
      API teams should otherwise use the service name as it appears in the
      protobuf. Example of a YAML configuration:: publishing: java_settings:
      service_class_names: - google.pubsub.v1.Publisher: TopicAdmin -
      google.pubsub.v1.Subscriber: SubscriptionAdmin
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ServiceClassNamesValue(_messages.Message):
    r"""Configure the Java class name to use instead of the service's for its
    corresponding generated GAPIC client. Keys are fully-qualified service
    names as they appear in the protobuf (including the full the
    language_settings.java.interface_names" field in gapic.yaml. API teams
    should otherwise use the service name as it appears in the protobuf.
    Example of a YAML configuration:: publishing: java_settings:
    service_class_names: - google.pubsub.v1.Publisher: TopicAdmin -
    google.pubsub.v1.Subscriber: SubscriptionAdmin

    Messages:
      AdditionalProperty: An additional property for a ServiceClassNamesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        ServiceClassNamesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ServiceClassNamesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  common = _messages.MessageField('CommonLanguageSettings', 1)
  libraryPackage = _messages.StringField(2)
  serviceClassNames = _messages.MessageField('ServiceClassNamesValue', 3)


class JwtLocation(_messages.Message):
  r"""Specifies a location to extract JWT from an API request.

  Fields:
    cookie: Specifies cookie name to extract JWT token.
    header: Specifies HTTP header name to extract JWT token.
    query: Specifies URL query parameter name to extract JWT token.
    valuePrefix: The value prefix. The value format is "value_prefix{token}"
      Only applies to "in" header type. Must be empty for "in" query type. If
      not empty, the header value has to match (case sensitive) this prefix.
      If not matched, JWT will not be extracted. If matched, JWT will be
      extracted after the prefix is removed. For example, for "Authorization:
      Bearer {JWT}", value_prefix="Bearer " with a space at the end.
  """

  cookie = _messages.StringField(1)
  header = _messages.StringField(2)
  query = _messages.StringField(3)
  valuePrefix = _messages.StringField(4)


class LabelDescriptor(_messages.Message):
  r"""A description of a label.

  Enums:
    ValueTypeValueValuesEnum: The type of data that can be assigned to the
      label.

  Fields:
    description: A human-readable description for the label.
    key: The label key.
    valueType: The type of data that can be assigned to the label.
  """

  class ValueTypeValueValuesEnum(_messages.Enum):
    r"""The type of data that can be assigned to the label.

    Values:
      STRING: A variable-length string. This is the default.
      BOOL: Boolean; true or false.
      INT64: A 64-bit signed integer.
    """
    STRING = 0
    BOOL = 1
    INT64 = 2

  description = _messages.StringField(1)
  key = _messages.StringField(2)
  valueType = _messages.EnumField('ValueTypeValueValuesEnum', 3)


class ListConnectionsResponse(_messages.Message):
  r"""ListConnectionsResponse is the response to list peering states for the
  given service and consumer project.

  Fields:
    connections: The list of Connections.
  """

  connections = _messages.MessageField('Connection', 1, repeated=True)


class ListDnsRecordSetsResponse(_messages.Message):
  r"""Represents all DNS RecordSets associated with the producer network

  Fields:
    dnsRecordSets: DNS record Set Resource
  """

  dnsRecordSets = _messages.MessageField('DnsRecordSet', 1, repeated=True)


class ListDnsZonesResponse(_messages.Message):
  r"""Represents all DNS zones in the shared producer host project and the
  matching peering zones in the consumer project.

  Fields:
    dnsZonePairs: All pairs of private DNS zones in the shared producer host
      project and the matching peering zones in the consumer project..
  """

  dnsZonePairs = _messages.MessageField('DnsZonePair', 1, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListPeeredDnsDomainsResponse(_messages.Message):
  r"""Response to list peered DNS domains for a given connection.

  Fields:
    peeredDnsDomains: The list of peered DNS domains.
  """

  peeredDnsDomains = _messages.MessageField('PeeredDnsDomain', 1, repeated=True)


class LogDescriptor(_messages.Message):
  r"""A description of a log type. Example in YAML format: - name:
  library.googleapis.com/activity_history description: The history of
  borrowing and returning library items. display_name: Activity labels: - key:
  /customer_id description: Identifier of a library customer

  Fields:
    description: A human-readable description of this log. This information
      appears in the documentation and can contain details.
    displayName: The human-readable name for this log. This information
      appears on the user interface and should be concise.
    labels: The set of labels that are available to describe a specific log
      entry. Runtime requests that contain labels not specified here are
      considered invalid.
    name: The name of the log. It must be less than 512 characters long and
      can include the following characters: upper- and lower-case alphanumeric
      characters [A-Za-z0-9], and punctuation characters including slash,
      underscore, hyphen, period [/_-.].
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  labels = _messages.MessageField('LabelDescriptor', 3, repeated=True)
  name = _messages.StringField(4)


class Logging(_messages.Message):
  r"""Logging configuration of the service. The following example shows how to
  configure logs to be sent to the producer and consumer projects. In the
  example, the `activity_history` log is sent to both the producer and
  consumer projects, whereas the `purchase_history` log is only sent to the
  producer project. monitored_resources: - type: library.googleapis.com/branch
  labels: - key: /city description: The city where the library branch is
  located in. - key: /name description: The name of the branch. logs: - name:
  activity_history labels: - key: /customer_id - name: purchase_history
  logging: producer_destinations: - monitored_resource:
  library.googleapis.com/branch logs: - activity_history - purchase_history
  consumer_destinations: - monitored_resource: library.googleapis.com/branch
  logs: - activity_history

  Fields:
    consumerDestinations: Logging configurations for sending logs to the
      consumer project. There can be multiple consumer destinations, each one
      must have a different monitored resource type. A log can be used in at
      most one consumer destination.
    producerDestinations: Logging configurations for sending logs to the
      producer project. There can be multiple producer destinations, each one
      must have a different monitored resource type. A log can be used in at
      most one producer destination.
  """

  consumerDestinations = _messages.MessageField('LoggingDestination', 1, repeated=True)
  producerDestinations = _messages.MessageField('LoggingDestination', 2, repeated=True)


class LoggingDestination(_messages.Message):
  r"""Configuration of a specific logging destination (the producer project or
  the consumer project).

  Fields:
    logs: Names of the logs to be sent to this destination. Each name must be
      defined in the Service.logs section. If the log name is not a domain
      scoped name, it will be automatically prefixed with the service name
      followed by "/".
    monitoredResource: The monitored resource type. The type must be defined
      in the Service.monitored_resources section.
  """

  logs = _messages.StringField(1, repeated=True)
  monitoredResource = _messages.StringField(2)


class LongRunning(_messages.Message):
  r"""Describes settings to use when generating API methods that use the long-
  running operation pattern. All default values below are from those used in
  the client library generators (e.g.
  [Java](https://github.com/googleapis/gapic-generator-java/blob/04c2faa191a9b
  5a10b92392fe8482279c4404803/src/main/java/com/google/api/generator/gapic/com
  poser/common/RetrySettingsComposer.java)).

  Fields:
    initialPollDelay: Initial delay after which the first poll request will be
      made. Default value: 5 seconds.
    maxPollDelay: Maximum time between two subsequent poll requests. Default
      value: 45 seconds.
    pollDelayMultiplier: Multiplier to gradually increase delay between
      subsequent polls until it reaches max_poll_delay. Default value: 1.5.
    totalPollTimeout: Total polling timeout. Default value: 5 minutes.
  """

  initialPollDelay = _messages.StringField(1)
  maxPollDelay = _messages.StringField(2)
  pollDelayMultiplier = _messages.FloatField(3, variant=_messages.Variant.FLOAT)
  totalPollTimeout = _messages.StringField(4)


class Method(_messages.Message):
  r"""Method represents a method of an API interface.

  Enums:
    SyntaxValueValuesEnum: The source syntax of this method.

  Fields:
    name: The simple name of this method.
    options: Any metadata attached to the method.
    requestStreaming: If true, the request is streamed.
    requestTypeUrl: A URL of the input message type.
    responseStreaming: If true, the response is streamed.
    responseTypeUrl: The URL of the output message type.
    syntax: The source syntax of this method.
  """

  class SyntaxValueValuesEnum(_messages.Enum):
    r"""The source syntax of this method.

    Values:
      SYNTAX_PROTO2: Syntax `proto2`.
      SYNTAX_PROTO3: Syntax `proto3`.
      SYNTAX_EDITIONS: Syntax `editions`.
    """
    SYNTAX_PROTO2 = 0
    SYNTAX_PROTO3 = 1
    SYNTAX_EDITIONS = 2

  name = _messages.StringField(1)
  options = _messages.MessageField('Option', 2, repeated=True)
  requestStreaming = _messages.BooleanField(3)
  requestTypeUrl = _messages.StringField(4)
  responseStreaming = _messages.BooleanField(5)
  responseTypeUrl = _messages.StringField(6)
  syntax = _messages.EnumField('SyntaxValueValuesEnum', 7)


class MethodPolicy(_messages.Message):
  r"""Defines policies applying to an RPC method.

  Fields:
    requestPolicies: Policies that are applicable to the request message.
    selector: Selects a method to which these policies should be enforced, for
      example, "google.pubsub.v1.Subscriber.CreateSubscription". Refer to
      selector for syntax details. NOTE: This field must not be set in the
      proto annotation. It will be automatically filled by the service config
      compiler .
  """

  requestPolicies = _messages.MessageField('FieldPolicy', 1, repeated=True)
  selector = _messages.StringField(2)


class MethodSettings(_messages.Message):
  r"""Describes the generator configuration for a method.

  Fields:
    longRunning: Describes settings to use for long-running operations when
      generating API methods for RPCs. Complements RPCs that use the
      annotations in google/longrunning/operations.proto. Example of a YAML
      configuration:: publishing: method_settings: - selector:
      google.cloud.speech.v2.Speech.BatchRecognize long_running:
      initial_poll_delay: seconds: 60 # 1 minute poll_delay_multiplier: 1.5
      max_poll_delay: seconds: 360 # 6 minutes total_poll_timeout: seconds:
      54000 # 90 minutes
    selector: The fully qualified name of the method, for which the options
      below apply. This is used to find the method to apply the options.
  """

  longRunning = _messages.MessageField('LongRunning', 1)
  selector = _messages.StringField(2)


class MetricDescriptor(_messages.Message):
  r"""Defines a metric type and its schema. Once a metric descriptor is
  created, deleting or altering it stops data collection and makes the metric
  type's existing data unusable.

  Enums:
    LaunchStageValueValuesEnum: Optional. The launch stage of the metric
      definition.
    MetricKindValueValuesEnum: Whether the metric records instantaneous
      values, changes to a value, etc. Some combinations of `metric_kind` and
      `value_type` might not be supported.
    ValueTypeValueValuesEnum: Whether the measurement is an integer, a
      floating-point number, etc. Some combinations of `metric_kind` and
      `value_type` might not be supported.

  Fields:
    description: A detailed description of the metric, which can be used in
      documentation.
    displayName: A concise name for the metric, which can be displayed in user
      interfaces. Use sentence case without an ending period, for example
      "Request count". This field is optional but it is recommended to be set
      for any metrics associated with user-visible concepts, such as Quota.
    labels: The set of labels that can be used to describe a specific instance
      of this metric type. For example, the
      `appengine.googleapis.com/http/server/response_latencies` metric type
      has a label for the HTTP response code, `response_code`, so you can look
      at latencies for successful responses or just for responses that failed.
    launchStage: Optional. The launch stage of the metric definition.
    metadata: Optional. Metadata which can be used to guide usage of the
      metric.
    metricKind: Whether the metric records instantaneous values, changes to a
      value, etc. Some combinations of `metric_kind` and `value_type` might
      not be supported.
    monitoredResourceTypes: Read-only. If present, then a time series, which
      is identified partially by a metric type and a
      MonitoredResourceDescriptor, that is associated with this metric type
      can only be associated with one of the monitored resource types listed
      here.
    name: The resource name of the metric descriptor.
    type: The metric type, including its DNS name prefix. The type is not URL-
      encoded. All user-defined metric types have the DNS name
      `custom.googleapis.com` or `external.googleapis.com`. Metric types
      should use a natural hierarchical grouping. For example:
      "custom.googleapis.com/invoice/paid/amount"
      "external.googleapis.com/prometheus/up"
      "appengine.googleapis.com/http/server/response_latencies"
    unit: The units in which the metric value is reported. It is only
      applicable if the `value_type` is `INT64`, `DOUBLE`, or `DISTRIBUTION`.
      The `unit` defines the representation of the stored metric values.
      Different systems might scale the values to be more easily displayed (so
      a value of `0.02kBy` _might_ be displayed as `20By`, and a value of
      `3523kBy` _might_ be displayed as `3.5MBy`). However, if the `unit` is
      `kBy`, then the value of the metric is always in thousands of bytes, no
      matter how it might be displayed. If you want a custom metric to record
      the exact number of CPU-seconds used by a job, you can create an `INT64
      CUMULATIVE` metric whose `unit` is `s{CPU}` (or equivalently `1s{CPU}`
      or just `s`). If the job uses 12,005 CPU-seconds, then the value is
      written as `12005`. Alternatively, if you want a custom metric to record
      data in a more granular way, you can create a `DOUBLE CUMULATIVE` metric
      whose `unit` is `ks{CPU}`, and then write the value `12.005` (which is
      `12005/1000`), or use `Kis{CPU}` and write `11.723` (which is
      `12005/1024`). The supported units are a subset of [The Unified Code for
      Units of Measure](https://unitsofmeasure.org/ucum.html) standard:
      **Basic units (UNIT)** * `bit` bit * `By` byte * `s` second * `min`
      minute * `h` hour * `d` day * `1` dimensionless **Prefixes (PREFIX)** *
      `k` kilo (10^3) * `M` mega (10^6) * `G` giga (10^9) * `T` tera (10^12) *
      `P` peta (10^15) * `E` exa (10^18) * `Z` zetta (10^21) * `Y` yotta
      (10^24) * `m` milli (10^-3) * `u` micro (10^-6) * `n` nano (10^-9) * `p`
      pico (10^-12) * `f` femto (10^-15) * `a` atto (10^-18) * `z` zepto
      (10^-21) * `y` yocto (10^-24) * `Ki` kibi (2^10) * `Mi` mebi (2^20) *
      `Gi` gibi (2^30) * `Ti` tebi (2^40) * `Pi` pebi (2^50) **Grammar** The
      grammar also includes these connectors: * `/` division or ratio (as an
      infix operator). For examples, `kBy/{email}` or `MiBy/10ms` (although
      you should almost never have `/s` in a metric `unit`; rates should
      always be computed at query time from the underlying cumulative or delta
      value). * `.` multiplication or composition (as an infix operator). For
      examples, `GBy.d` or `k{watt}.h`. The grammar for a unit is as follows:
      Expression = Component { "." Component } { "/" Component } ; Component =
      ( [ PREFIX ] UNIT | "%" ) [ Annotation ] | Annotation | "1" ; Annotation
      = "{" NAME "}" ; Notes: * `Annotation` is just a comment if it follows a
      `UNIT`. If the annotation is used alone, then the unit is equivalent to
      `1`. For examples, `{request}/s == 1/s`, `By{transmitted}/s == By/s`. *
      `NAME` is a sequence of non-blank printable ASCII characters not
      containing `{` or `}`. * `1` represents a unitary [dimensionless
      unit](https://en.wikipedia.org/wiki/Dimensionless_quantity) of 1, such
      as in `1/s`. It is typically used when none of the basic units are
      appropriate. For example, "new users per day" can be represented as
      `1/d` or `{new-users}/d` (and a metric value `5` would mean "5 new
      users). Alternatively, "thousands of page views per day" would be
      represented as `1000/d` or `k1/d` or `k{page_views}/d` (and a metric
      value of `5.3` would mean "5300 page views per day"). * `%` represents
      dimensionless value of 1/100, and annotates values giving a percentage
      (so the metric values are typically in the range of 0..100, and a metric
      value `3` means "3 percent"). * `10^2.%` indicates a metric contains a
      ratio, typically in the range 0..1, that will be multiplied by 100 and
      displayed as a percentage (so a metric value `0.03` means "3 percent").
    valueType: Whether the measurement is an integer, a floating-point number,
      etc. Some combinations of `metric_kind` and `value_type` might not be
      supported.
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Optional. The launch stage of the metric definition.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our [Terms of Service](https://cloud.google.com/terms/) and the
        [Google Cloud Platform Subject to the Deprecation
        Policy](https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  class MetricKindValueValuesEnum(_messages.Enum):
    r"""Whether the metric records instantaneous values, changes to a value,
    etc. Some combinations of `metric_kind` and `value_type` might not be
    supported.

    Values:
      METRIC_KIND_UNSPECIFIED: Do not use this default value.
      GAUGE: An instantaneous measurement of a value.
      DELTA: The change in a value during a time interval.
      CUMULATIVE: A value accumulated over a time interval. Cumulative
        measurements in a time series should have the same start time and
        increasing end times, until an event resets the cumulative value to
        zero and sets a new start time for the following points.
    """
    METRIC_KIND_UNSPECIFIED = 0
    GAUGE = 1
    DELTA = 2
    CUMULATIVE = 3

  class ValueTypeValueValuesEnum(_messages.Enum):
    r"""Whether the measurement is an integer, a floating-point number, etc.
    Some combinations of `metric_kind` and `value_type` might not be
    supported.

    Values:
      VALUE_TYPE_UNSPECIFIED: Do not use this default value.
      BOOL: The value is a boolean. This value type can be used only if the
        metric kind is `GAUGE`.
      INT64: The value is a signed 64-bit integer.
      DOUBLE: The value is a double precision floating point number.
      STRING: The value is a text string. This value type can be used only if
        the metric kind is `GAUGE`.
      DISTRIBUTION: The value is a `Distribution`.
      MONEY: The value is money.
    """
    VALUE_TYPE_UNSPECIFIED = 0
    BOOL = 1
    INT64 = 2
    DOUBLE = 3
    STRING = 4
    DISTRIBUTION = 5
    MONEY = 6

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  labels = _messages.MessageField('LabelDescriptor', 3, repeated=True)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 4)
  metadata = _messages.MessageField('MetricDescriptorMetadata', 5)
  metricKind = _messages.EnumField('MetricKindValueValuesEnum', 6)
  monitoredResourceTypes = _messages.StringField(7, repeated=True)
  name = _messages.StringField(8)
  type = _messages.StringField(9)
  unit = _messages.StringField(10)
  valueType = _messages.EnumField('ValueTypeValueValuesEnum', 11)


class MetricDescriptorMetadata(_messages.Message):
  r"""Additional annotations that can be used to guide the usage of a metric.

  Enums:
    LaunchStageValueValuesEnum: Deprecated. Must use the
      MetricDescriptor.launch_stage instead.

  Fields:
    ingestDelay: The delay of data points caused by ingestion. Data points
      older than this age are guaranteed to be ingested and available to be
      read, excluding data loss due to errors.
    launchStage: Deprecated. Must use the MetricDescriptor.launch_stage
      instead.
    samplePeriod: The sampling period of metric data points. For metrics which
      are written periodically, consecutive data points are stored at this
      time interval, excluding data loss due to errors. Metrics with a higher
      granularity have a smaller sampling period.
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Deprecated. Must use the MetricDescriptor.launch_stage instead.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our [Terms of Service](https://cloud.google.com/terms/) and the
        [Google Cloud Platform Subject to the Deprecation
        Policy](https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  ingestDelay = _messages.StringField(1)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 2)
  samplePeriod = _messages.StringField(3)


class MetricRule(_messages.Message):
  r"""Bind API methods to metrics. Binding a method to a metric causes that
  metric's configured quota behaviors to apply to the method call.

  Messages:
    MetricCostsValue: Metrics to update when the selected methods are called,
      and the associated cost applied to each metric. The key of the map is
      the metric name, and the values are the amount increased for the metric
      against which the quota limits are defined. The value must not be
      negative.

  Fields:
    metricCosts: Metrics to update when the selected methods are called, and
      the associated cost applied to each metric. The key of the map is the
      metric name, and the values are the amount increased for the metric
      against which the quota limits are defined. The value must not be
      negative.
    selector: Selects the methods to which this rule applies. Refer to
      selector for syntax details.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetricCostsValue(_messages.Message):
    r"""Metrics to update when the selected methods are called, and the
    associated cost applied to each metric. The key of the map is the metric
    name, and the values are the amount increased for the metric against which
    the quota limits are defined. The value must not be negative.

    Messages:
      AdditionalProperty: An additional property for a MetricCostsValue
        object.

    Fields:
      additionalProperties: Additional properties of type MetricCostsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetricCostsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  metricCosts = _messages.MessageField('MetricCostsValue', 1)
  selector = _messages.StringField(2)


class Mixin(_messages.Message):
  r"""Declares an API Interface to be included in this interface. The
  including interface must redeclare all the methods from the included
  interface, but documentation and options are inherited as follows: - If
  after comment and whitespace stripping, the documentation string of the
  redeclared method is empty, it will be inherited from the original method. -
  Each annotation belonging to the service config (http, visibility) which is
  not set in the redeclared method will be inherited. - If an http annotation
  is inherited, the path pattern will be modified as follows. Any version
  prefix will be replaced by the version of the including interface plus the
  root path if specified. Example of a simple mixin: package google.acl.v1;
  service AccessControl { // Get the underlying ACL object. rpc
  GetAcl(GetAclRequest) returns (Acl) { option (google.api.http).get =
  "/v1/{resource=**}:getAcl"; } } package google.storage.v2; service Storage {
  // rpc GetAcl(GetAclRequest) returns (Acl); // Get a data record. rpc
  GetData(GetDataRequest) returns (Data) { option (google.api.http).get =
  "/v2/{resource=**}"; } } Example of a mixin configuration: apis: - name:
  google.storage.v2.Storage mixins: - name: google.acl.v1.AccessControl The
  mixin construct implies that all methods in `AccessControl` are also
  declared with same name and request/response types in `Storage`. A
  documentation generator or annotation processor will see the effective
  `Storage.GetAcl` method after inherting documentation and annotations as
  follows: service Storage { // Get the underlying ACL object. rpc
  GetAcl(GetAclRequest) returns (Acl) { option (google.api.http).get =
  "/v2/{resource=**}:getAcl"; } ... } Note how the version in the path pattern
  changed from `v1` to `v2`. If the `root` field in the mixin is specified, it
  should be a relative path under which inherited HTTP paths are placed.
  Example: apis: - name: google.storage.v2.Storage mixins: - name:
  google.acl.v1.AccessControl root: acls This implies the following inherited
  HTTP annotation: service Storage { // Get the underlying ACL object. rpc
  GetAcl(GetAclRequest) returns (Acl) { option (google.api.http).get =
  "/v2/acls/{resource=**}:getAcl"; } ... }

  Fields:
    name: The fully qualified name of the interface which is included.
    root: If non-empty specifies a path under which inherited HTTP paths are
      rooted.
  """

  name = _messages.StringField(1)
  root = _messages.StringField(2)


class MonitoredResourceDescriptor(_messages.Message):
  r"""An object that describes the schema of a MonitoredResource object using
  a type name and a set of labels. For example, the monitored resource
  descriptor for Google Compute Engine VM instances has a type of
  `"gce_instance"` and specifies the use of the labels `"instance_id"` and
  `"zone"` to identify particular VM instances. Different APIs can support
  different monitored resource types. APIs generally provide a `list` method
  that returns the monitored resource descriptors used by the API.

  Enums:
    LaunchStageValueValuesEnum: Optional. The launch stage of the monitored
      resource definition.

  Fields:
    description: Optional. A detailed description of the monitored resource
      type that might be used in documentation.
    displayName: Optional. A concise name for the monitored resource type that
      might be displayed in user interfaces. It should be a Title Cased Noun
      Phrase, without any article or other determiners. For example, `"Google
      Cloud SQL Database"`.
    labels: Required. A set of labels used to describe instances of this
      monitored resource type. For example, an individual Google Cloud SQL
      database is identified by values for the labels `"database_id"` and
      `"zone"`.
    launchStage: Optional. The launch stage of the monitored resource
      definition.
    name: Optional. The resource name of the monitored resource descriptor:
      `"projects/{project_id}/monitoredResourceDescriptors/{type}"` where
      {type} is the value of the `type` field in this object and {project_id}
      is a project ID that provides API-specific context for accessing the
      type. APIs that do not use project information can use the resource name
      format `"monitoredResourceDescriptors/{type}"`.
    type: Required. The monitored resource type. For example, the type
      `"cloudsql_database"` represents databases in Google Cloud SQL. For a
      list of types, see [Monitoring resource
      types](https://cloud.google.com/monitoring/api/resources) and [Logging
      resource types](https://cloud.google.com/logging/docs/api/v2/resource-
      list).
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Optional. The launch stage of the monitored resource definition.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our [Terms of Service](https://cloud.google.com/terms/) and the
        [Google Cloud Platform Subject to the Deprecation
        Policy](https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  labels = _messages.MessageField('LabelDescriptor', 3, repeated=True)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 4)
  name = _messages.StringField(5)
  type = _messages.StringField(6)


class Monitoring(_messages.Message):
  r"""Monitoring configuration of the service. The example below shows how to
  configure monitored resources and metrics for monitoring. In the example, a
  monitored resource and two metrics are defined. The
  `library.googleapis.com/book/returned_count` metric is sent to both producer
  and consumer projects, whereas the `library.googleapis.com/book/num_overdue`
  metric is only sent to the consumer project. monitored_resources: - type:
  library.googleapis.com/Branch display_name: "Library Branch" description: "A
  branch of a library." launch_stage: GA labels: - key: resource_container
  description: "The Cloud container (ie. project id) for the Branch." - key:
  location description: "The location of the library branch." - key: branch_id
  description: "The id of the branch." metrics: - name:
  library.googleapis.com/book/returned_count display_name: "Books Returned"
  description: "The count of books that have been returned." launch_stage: GA
  metric_kind: DELTA value_type: INT64 unit: "1" labels: - key: customer_id
  description: "The id of the customer." - name:
  library.googleapis.com/book/num_overdue display_name: "Books Overdue"
  description: "The current number of overdue books." launch_stage: GA
  metric_kind: GAUGE value_type: INT64 unit: "1" labels: - key: customer_id
  description: "The id of the customer." monitoring: producer_destinations: -
  monitored_resource: library.googleapis.com/Branch metrics: -
  library.googleapis.com/book/returned_count consumer_destinations: -
  monitored_resource: library.googleapis.com/Branch metrics: -
  library.googleapis.com/book/returned_count -
  library.googleapis.com/book/num_overdue

  Fields:
    consumerDestinations: Monitoring configurations for sending metrics to the
      consumer project. There can be multiple consumer destinations. A
      monitored resource type may appear in multiple monitoring destinations
      if different aggregations are needed for different sets of metrics
      associated with that monitored resource type. A monitored resource and
      metric pair may only be used once in the Monitoring configuration.
    producerDestinations: Monitoring configurations for sending metrics to the
      producer project. There can be multiple producer destinations. A
      monitored resource type may appear in multiple monitoring destinations
      if different aggregations are needed for different sets of metrics
      associated with that monitored resource type. A monitored resource and
      metric pair may only be used once in the Monitoring configuration.
  """

  consumerDestinations = _messages.MessageField('MonitoringDestination', 1, repeated=True)
  producerDestinations = _messages.MessageField('MonitoringDestination', 2, repeated=True)


class MonitoringDestination(_messages.Message):
  r"""Configuration of a specific monitoring destination (the producer project
  or the consumer project).

  Fields:
    metrics: Types of the metrics to report to this monitoring destination.
      Each type must be defined in Service.metrics section.
    monitoredResource: The monitored resource type. The type must be defined
      in Service.monitored_resources section.
  """

  metrics = _messages.StringField(1, repeated=True)
  monitoredResource = _messages.StringField(2)


class NodeSettings(_messages.Message):
  r"""Settings for Node client libraries.

  Fields:
    common: Some settings.
  """

  common = _messages.MessageField('CommonLanguageSettings', 1)


class OAuthRequirements(_messages.Message):
  r"""OAuth scopes are a way to define data and permissions on data. For
  example, there are scopes defined for "Read-only access to Google Calendar"
  and "Access to Cloud Platform". Users can consent to a scope for an
  application, giving it permission to access that data on their behalf. OAuth
  scope specifications should be fairly coarse grained; a user will need to
  see and understand the text description of what your scope means. In most
  cases: use one or at most two OAuth scopes for an entire family of products.
  If your product has multiple APIs, you should probably be sharing the OAuth
  scope across all of those APIs. When you need finer grained OAuth consent
  screens: talk with your product management about how developers will use
  them in practice. Please note that even though each of the canonical scopes
  is enough for a request to be accepted and passed to the backend, a request
  can still fail due to the backend requiring additional scopes or
  permissions.

  Fields:
    canonicalScopes: The list of publicly documented OAuth scopes that are
      allowed access. An OAuth token containing any of these scopes will be
      accepted. Example: canonical_scopes:
      https://www.googleapis.com/auth/calendar,
      https://www.googleapis.com/auth/calendar.read
  """

  canonicalScopes = _messages.StringField(1)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class Option(_messages.Message):
  r"""A protocol buffer option, which can be attached to a message, field,
  enumeration, etc.

  Messages:
    ValueValue: The option's value packed in an Any message. If the value is a
      primitive, the corresponding wrapper type defined in
      google/protobuf/wrappers.proto should be used. If the value is an enum,
      it should be stored as an int32 value using the
      google.protobuf.Int32Value type.

  Fields:
    name: The option's name. For protobuf built-in options (options defined in
      descriptor.proto), this is the short name. For example, `"map_entry"`.
      For custom options, it should be the fully-qualified name. For example,
      `"google.api.http"`.
    value: The option's value packed in an Any message. If the value is a
      primitive, the corresponding wrapper type defined in
      google/protobuf/wrappers.proto should be used. If the value is an enum,
      it should be stored as an int32 value using the
      google.protobuf.Int32Value type.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ValueValue(_messages.Message):
    r"""The option's value packed in an Any message. If the value is a
    primitive, the corresponding wrapper type defined in
    google/protobuf/wrappers.proto should be used. If the value is an enum, it
    should be stored as an int32 value using the google.protobuf.Int32Value
    type.

    Messages:
      AdditionalProperty: An additional property for a ValueValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ValueValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  name = _messages.StringField(1)
  value = _messages.MessageField('ValueValue', 2)


class Page(_messages.Message):
  r"""Represents a documentation page. A page can contain subpages to
  represent nested documentation set structure.

  Fields:
    content: The Markdown content of the page. You can use (== include {path}
      ==) to include content from a Markdown file. The content can be used to
      produce the documentation page such as HTML format page.
    name: The name of the page. It will be used as an identity of the page to
      generate URI of the page, text of the link to this page in navigation,
      etc. The full page name (start from the root page name to this page
      concatenated with `.`) can be used as reference to the page in your
      documentation. For example: pages: - name: Tutorial content: (== include
      tutorial.md ==) subpages: - name: Java content: (== include
      tutorial_java.md ==) You can reference `Java` page using Markdown
      reference link syntax: `Java`.
    subpages: Subpages of this page. The order of subpages specified here will
      be honored in the generated docset.
  """

  content = _messages.StringField(1)
  name = _messages.StringField(2)
  subpages = _messages.MessageField('Page', 3, repeated=True)


class PartialDeleteConnectionMetadata(_messages.Message):
  r"""Metadata provided through GetOperation request for the LRO generated by
  Partial Delete Connection API
  """



class PeeredDnsDomain(_messages.Message):
  r"""DNS domain suffix for which requests originating in the producer VPC
  network are resolved in the associated consumer VPC network.

  Fields:
    dnsSuffix: The DNS domain name suffix e.g. `example.com.`. Cloud DNS
      requires that a DNS suffix ends with a trailing dot.
    name: User assigned name for this resource. Must be unique within the
      consumer network. The name must be 1-63 characters long, must begin with
      a letter, end with a letter or digit, and only contain lowercase
      letters, digits or dashes.
  """

  dnsSuffix = _messages.StringField(1)
  name = _messages.StringField(2)


class PeeredDnsDomainMetadata(_messages.Message):
  r"""Metadata provided through GetOperation request for the LRO generated by
  CreatePeeredDnsDomain API.
  """



class PhpSettings(_messages.Message):
  r"""Settings for Php client libraries.

  Fields:
    common: Some settings.
  """

  common = _messages.MessageField('CommonLanguageSettings', 1)


class PolicyBinding(_messages.Message):
  r"""Grouping of IAM role and IAM member.

  Fields:
    member: Required. Member to bind the role with. See
      /iam/docs/reference/rest/v1/Policy#Binding for how to format each
      member. Eg. - user:<EMAIL> - serviceAccount:my-service-
      <EMAIL>
    role: Required. Role to apply. Only allowlisted roles can be used at the
      specified granularity. The role must be one of the following: -
      'roles/container.hostServiceAgentUser' applied on the shared VPC host
      project - 'roles/compute.securityAdmin' applied on the shared VPC host
      project - 'roles/compute.networkAdmin' applied on the shared VPC host
      project - 'roles/compute.xpnAdmin' applied on the shared VPC host
      project - 'roles/dns.admin' applied on the shared VPC host project
  """

  member = _messages.StringField(1)
  role = _messages.StringField(2)


class Publishing(_messages.Message):
  r"""This message configures the settings for publishing [Google Cloud Client
  libraries](https://cloud.google.com/apis/docs/cloud-client-libraries)
  generated from the service config.

  Enums:
    OrganizationValueValuesEnum: For whom the client library is being
      published.

  Fields:
    apiShortName: Used as a tracking tag when collecting data about the APIs
      developer relations artifacts like docs, packages delivered to package
      managers, etc. Example: "speech".
    codeownerGithubTeams: GitHub teams to be added to CODEOWNERS in the
      directory in GitHub containing source code for the client libraries for
      this API.
    docTagPrefix: A prefix used in sample code when demarking regions to be
      included in documentation.
    documentationUri: Link to product home page. Example:
      https://cloud.google.com/asset-inventory/docs/overview
    githubLabel: GitHub label to apply to issues and pull requests opened for
      this API.
    librarySettings: Client library settings. If the same version string
      appears multiple times in this list, then the last one wins. Settings
      from earlier settings with the same version string are discarded.
    methodSettings: A list of API method settings, e.g. the behavior for
      methods that use the long-running operation pattern.
    newIssueUri: Link to a *public* URI where users can report issues.
      Example: https://issuetracker.google.com/issues/new?component=190865&tem
      plate=1161103
    organization: For whom the client library is being published.
    protoReferenceDocumentationUri: Optional link to proto reference
      documentation. Example:
      https://cloud.google.com/pubsub/lite/docs/reference/rpc
  """

  class OrganizationValueValuesEnum(_messages.Enum):
    r"""For whom the client library is being published.

    Values:
      CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED: Not useful.
      CLOUD: Google Cloud Platform Org.
      ADS: Ads (Advertising) Org.
      PHOTOS: Photos Org.
      STREET_VIEW: Street View Org.
      SHOPPING: Shopping Org.
      GEO: Geo Org.
      GENERATIVE_AI: Generative AI - https://developers.generativeai.google
    """
    CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED = 0
    CLOUD = 1
    ADS = 2
    PHOTOS = 3
    STREET_VIEW = 4
    SHOPPING = 5
    GEO = 6
    GENERATIVE_AI = 7

  apiShortName = _messages.StringField(1)
  codeownerGithubTeams = _messages.StringField(2, repeated=True)
  docTagPrefix = _messages.StringField(3)
  documentationUri = _messages.StringField(4)
  githubLabel = _messages.StringField(5)
  librarySettings = _messages.MessageField('ClientLibrarySettings', 6, repeated=True)
  methodSettings = _messages.MessageField('MethodSettings', 7, repeated=True)
  newIssueUri = _messages.StringField(8)
  organization = _messages.EnumField('OrganizationValueValuesEnum', 9)
  protoReferenceDocumentationUri = _messages.StringField(10)


class PythonSettings(_messages.Message):
  r"""Settings for Python client libraries.

  Fields:
    common: Some settings.
  """

  common = _messages.MessageField('CommonLanguageSettings', 1)


class Quota(_messages.Message):
  r"""Quota configuration helps to achieve fairness and budgeting in service
  usage. The metric based quota configuration works this way: - The service
  configuration defines a set of metrics. - For API calls, the
  quota.metric_rules maps methods to metrics with corresponding costs. - The
  quota.limits defines limits on the metrics, which will be used for quota
  checks at runtime. An example quota configuration in yaml format: quota:
  limits: - name: apiWriteQpsPerProject metric:
  library.googleapis.com/write_calls unit: "1/min/{project}" # rate limit for
  consumer projects values: STANDARD: 10000 (The metric rules bind all methods
  to the read_calls metric, except for the UpdateBook and DeleteBook methods.
  These two methods are mapped to the write_calls metric, with the UpdateBook
  method consuming at twice rate as the DeleteBook method.) metric_rules: -
  selector: "*" metric_costs: library.googleapis.com/read_calls: 1 - selector:
  google.example.library.v1.LibraryService.UpdateBook metric_costs:
  library.googleapis.com/write_calls: 2 - selector:
  google.example.library.v1.LibraryService.DeleteBook metric_costs:
  library.googleapis.com/write_calls: 1 Corresponding Metric definition:
  metrics: - name: library.googleapis.com/read_calls display_name: Read
  requests metric_kind: DELTA value_type: INT64 - name:
  library.googleapis.com/write_calls display_name: Write requests metric_kind:
  DELTA value_type: INT64

  Fields:
    limits: List of QuotaLimit definitions for the service.
    metricRules: List of MetricRule definitions, each one mapping a selected
      method to one or more metrics.
  """

  limits = _messages.MessageField('QuotaLimit', 1, repeated=True)
  metricRules = _messages.MessageField('MetricRule', 2, repeated=True)


class QuotaLimit(_messages.Message):
  r"""`QuotaLimit` defines a specific limit that applies over a specified
  duration for a limit type. There can be at most one limit for a duration and
  limit type combination defined within a `QuotaGroup`.

  Messages:
    ValuesValue: Tiered limit values. You must specify this as a key:value
      pair, with an integer value that is the maximum number of requests
      allowed for the specified unit. Currently only STANDARD is supported.

  Fields:
    defaultLimit: Default number of tokens that can be consumed during the
      specified duration. This is the number of tokens assigned when a client
      application developer activates the service for his/her project.
      Specifying a value of 0 will block all requests. This can be used if you
      are provisioning quota to selected consumers and blocking others.
      Similarly, a value of -1 will indicate an unlimited quota. No other
      negative values are allowed. Used by group-based quotas only.
    description: Optional. User-visible, extended description for this quota
      limit. Should be used only when more context is needed to understand
      this limit than provided by the limit's display name (see:
      `display_name`).
    displayName: User-visible display name for this limit. Optional. If not
      set, the UI will provide a default display name based on the quota
      configuration. This field can be used to override the default display
      name generated from the configuration.
    duration: Duration of this limit in textual notation. Must be "100s" or
      "1d". Used by group-based quotas only.
    freeTier: Free tier value displayed in the Developers Console for this
      limit. The free tier is the number of tokens that will be subtracted
      from the billed amount when billing is enabled. This field can only be
      set on a limit with duration "1d", in a billable group; it is invalid on
      any other limit. If this field is not set, it defaults to 0, indicating
      that there is no free tier for this service. Used by group-based quotas
      only.
    maxLimit: Maximum number of tokens that can be consumed during the
      specified duration. Client application developers can override the
      default limit up to this maximum. If specified, this value cannot be set
      to a value less than the default limit. If not specified, it is set to
      the default limit. To allow clients to apply overrides with no upper
      bound, set this to -1, indicating unlimited maximum quota. Used by
      group-based quotas only.
    metric: The name of the metric this quota limit applies to. The quota
      limits with the same metric will be checked together during runtime. The
      metric must be defined within the service config.
    name: Name of the quota limit. The name must be provided, and it must be
      unique within the service. The name can only include alphanumeric
      characters as well as '-'. The maximum length of the limit name is 64
      characters.
    unit: Specify the unit of the quota limit. It uses the same syntax as
      Metric.unit. The supported unit kinds are determined by the quota
      backend system. Here are some examples: * "1/min/{project}" for quota
      per minute per project. Note: the order of unit components is
      insignificant. The "1" at the beginning is required to follow the metric
      unit syntax.
    values: Tiered limit values. You must specify this as a key:value pair,
      with an integer value that is the maximum number of requests allowed for
      the specified unit. Currently only STANDARD is supported.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ValuesValue(_messages.Message):
    r"""Tiered limit values. You must specify this as a key:value pair, with
    an integer value that is the maximum number of requests allowed for the
    specified unit. Currently only STANDARD is supported.

    Messages:
      AdditionalProperty: An additional property for a ValuesValue object.

    Fields:
      additionalProperties: Additional properties of type ValuesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ValuesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.IntegerField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  defaultLimit = _messages.IntegerField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  duration = _messages.StringField(4)
  freeTier = _messages.IntegerField(5)
  maxLimit = _messages.IntegerField(6)
  metric = _messages.StringField(7)
  name = _messages.StringField(8)
  unit = _messages.StringField(9)
  values = _messages.MessageField('ValuesValue', 10)


class Range(_messages.Message):
  r"""Represents a found unused range.

  Fields:
    ipCidrRange: CIDR range in "10.x.x.x/y" format that is within the
      allocated ranges and currently unused.
    network: In the Shared VPC host project, the VPC network that's peered
      with the consumer network. For example:
      `projects/1234321/global/networks/host-network`
  """

  ipCidrRange = _messages.StringField(1)
  network = _messages.StringField(2)


class RangeReservation(_messages.Message):
  r"""Represents a range reservation.

  Fields:
    ipPrefixLength: Required. The size of the desired subnet. Use usual CIDR
      range notation. For example, '29' to find unused x.x.x.x/29 CIDR range.
      The goal is to determine if one of the allocated ranges has enough free
      space for a subnet of the requested size. GCE disallows subnets with
      prefix_length > 29
    requestedRanges: Optional. The name of one or more allocated IP address
      ranges associated with this private service access connection. If no
      range names are provided all ranges associated with this connection will
      be considered. If a CIDR range with the specified IP prefix length is
      not available within these ranges the validation fails.
    secondaryRangeIpPrefixLengths: Optional. The size of the desired secondary
      ranges for the subnet. Use usual CIDR range notation. For example, '29'
      to find unused x.x.x.x/29 CIDR range. The goal is to determine that the
      allocated ranges have enough free space for all the requested secondary
      ranges. GCE disallows subnets with prefix_length > 29
    subnetworkCandidates: Optional. List of subnetwork candidates to validate.
      The required input fields are `name`, `network`, and `region`.
      Subnetworks from this list which exist will be returned in the response
      with the `ip_cidr_range`, `secondary_ip_cider_ranges`, and
      `outside_allocation` fields set.
  """

  ipPrefixLength = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  requestedRanges = _messages.StringField(2, repeated=True)
  secondaryRangeIpPrefixLengths = _messages.IntegerField(3, repeated=True, variant=_messages.Variant.INT32)
  subnetworkCandidates = _messages.MessageField('Subnetwork', 4, repeated=True)


class RemoveDnsRecordSetMetadata(_messages.Message):
  r"""Metadata provided through GetOperation request for the LRO generated by
  RemoveDnsRecordSet API
  """



class RemoveDnsRecordSetRequest(_messages.Message):
  r"""Request to remove a record set from a private managed DNS zone in the
  shared producer host project. The name, type, ttl, and data values must all
  exactly match an existing record set in the specified zone.

  Fields:
    consumerNetwork: Required. The network that the consumer is using to
      connect with services. Must be in the form of
      projects/{project}/global/networks/{network} {project} is the project
      number, as in '12345' {network} is the network name.
    dnsRecordSet: Required. The DNS record set to remove.
    zone: Required. The name of the private DNS zone in the shared producer
      host project from which the record set will be removed.
  """

  consumerNetwork = _messages.StringField(1)
  dnsRecordSet = _messages.MessageField('DnsRecordSet', 2)
  zone = _messages.StringField(3)


class RemoveDnsRecordSetResponse(_messages.Message):
  r"""Blank message response type for RemoveDnsRecordSet API"""


class RemoveDnsZoneMetadata(_messages.Message):
  r"""Metadata provided through GetOperation request for the LRO generated by
  RemoveDnsZone API
  """



class RemoveDnsZoneRequest(_messages.Message):
  r"""Request to remove a private managed DNS zone in the shared producer host
  project and a matching DNS peering zone in the consumer project.

  Fields:
    consumerNetwork: Required. The network that the consumer is using to
      connect with services. Must be in the form of
      projects/{project}/global/networks/{network} {project} is the project
      number, as in '12345' {network} is the network name.
    name: Required. The name for both the private zone in the shared producer
      host project and the peering zone in the consumer project.
  """

  consumerNetwork = _messages.StringField(1)
  name = _messages.StringField(2)


class RemoveDnsZoneResponse(_messages.Message):
  r"""Blank message response type for RemoveDnsZone API"""


class Route(_messages.Message):
  r"""Represents a route that was created or discovered by a private access
  management service.

  Fields:
    destRange: Destination CIDR range that this route applies to.
    name: Route name. See https://cloud.google.com/vpc/docs/routes
    network: Fully-qualified URL of the VPC network in the producer host
      tenant project that this route applies to. For example:
      `projects/123456/global/networks/host-network`
    nextHopGateway: Fully-qualified URL of the gateway that should handle
      matching packets that this route applies to. For example:
      `projects/123456/global/gateways/default-internet-gateway`
  """

  destRange = _messages.StringField(1)
  name = _messages.StringField(2)
  network = _messages.StringField(3)
  nextHopGateway = _messages.StringField(4)


class RubySettings(_messages.Message):
  r"""Settings for Ruby client libraries.

  Fields:
    common: Some settings.
  """

  common = _messages.MessageField('CommonLanguageSettings', 1)


class SearchRangeRequest(_messages.Message):
  r"""Request to search for an unused range within allocated ranges.

  Fields:
    ipPrefixLength: Required. The prefix length of the IP range. Use usual
      CIDR range notation. For example, '30' to find unused x.x.x.x/30 CIDR
      range. Actual range will be determined using allocated range for the
      consumer peered network and returned in the result.
    network: Network name in the consumer project. This network must have been
      already peered with a shared VPC network using CreateConnection method.
      Must be in a form 'projects/{project}/global/networks/{network}'.
      {project} is a project number, as in '12345' {network} is network name.
  """

  ipPrefixLength = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  network = _messages.StringField(2)


class SecondaryIpRange(_messages.Message):
  r"""A SecondaryIpRange object.

  Fields:
    ipCidrRange: Secondary IP CIDR range in `x.x.x.x/y` format.
    rangeName: Name of the secondary IP range.
  """

  ipCidrRange = _messages.StringField(1)
  rangeName = _messages.StringField(2)


class SecondaryIpRangeSpec(_messages.Message):
  r"""A SecondaryIpRangeSpec object.

  Fields:
    ipPrefixLength: Required. The prefix length of the secondary IP range. Use
      CIDR range notation, such as `30` to provision a secondary IP range with
      an `x.x.x.x/30` CIDR range. The IP address range is drawn from a pool of
      available ranges in the service consumer's allocated range.
    outsideAllocationPublicIpRange: Optional. Enable outside allocation using
      public IP addresses. Any public IP range may be specified. If this field
      is provided, we will not use customer reserved ranges for this secondary
      IP range.
    rangeName: Required. A name for the secondary IP range. The name must be
      1-63 characters long, and comply with RFC1035. The name must be unique
      within the subnetwork.
    requestedAddress: Optional. The starting address of a range. The address
      must be a valid IPv4 address in the x.x.x.x format. This value combined
      with the IP prefix range is the CIDR range for the secondary IP range.
      The range must be within the allocated range that is assigned to the
      private connection. If the CIDR range isn't available, the call fails.
  """

  ipPrefixLength = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  outsideAllocationPublicIpRange = _messages.StringField(2)
  rangeName = _messages.StringField(3)
  requestedAddress = _messages.StringField(4)


class Service(_messages.Message):
  r"""`Service` is the root object of Google API service configuration
  (service config). It describes the basic information about a logical
  service, such as the service name and the user-facing title, and delegates
  other aspects to sub-sections. Each sub-section is either a proto message or
  a repeated proto message that configures a specific aspect, such as auth.
  For more information, see each proto message definition. Example: type:
  google.api.Service name: calendar.googleapis.com title: Google Calendar API
  apis: - name: google.calendar.v3.Calendar visibility: rules: - selector:
  "google.calendar.v3.*" restriction: PREVIEW backend: rules: - selector:
  "google.calendar.v3.*" address: calendar.example.com authentication:
  providers: - id: google_calendar_auth jwks_uri:
  https://www.googleapis.com/oauth2/v1/certs issuer:
  https://securetoken.google.com rules: - selector: "*" requirements:
  provider_id: google_calendar_auth

  Fields:
    apis: A list of API interfaces exported by this service. Only the `name`
      field of the google.protobuf.Api needs to be provided by the
      configuration author, as the remaining fields will be derived from the
      IDL during the normalization process. It is an error to specify an API
      interface here which cannot be resolved against the associated IDL
      files.
    authentication: Auth configuration.
    backend: API backend configuration.
    billing: Billing configuration.
    configVersion: Obsolete. Do not use. This field has no semantic meaning.
      The service config compiler always sets this field to `3`.
    context: Context configuration.
    control: Configuration for the service control plane.
    customError: Custom error configuration.
    documentation: Additional API documentation.
    endpoints: Configuration for network endpoints. If this is empty, then an
      endpoint with the same name as the service is automatically generated to
      service all defined APIs.
    enums: A list of all enum types included in this API service. Enums
      referenced directly or indirectly by the `apis` are automatically
      included. Enums which are not referenced but shall be included should be
      listed here by name by the configuration author. Example: enums: - name:
      google.someapi.v1.SomeEnum
    http: HTTP configuration.
    id: A unique ID for a specific instance of this message, typically
      assigned by the client for tracking purpose. Must be no longer than 63
      characters and only lower case letters, digits, '.', '_' and '-' are
      allowed. If empty, the server may choose to generate one instead.
    logging: Logging configuration.
    logs: Defines the logs used by this service.
    metrics: Defines the metrics used by this service.
    monitoredResources: Defines the monitored resources used by this service.
      This is required by the Service.monitoring and Service.logging
      configurations.
    monitoring: Monitoring configuration.
    name: The service name, which is a DNS-like logical identifier for the
      service, such as `calendar.googleapis.com`. The service name typically
      goes through DNS verification to make sure the owner of the service also
      owns the DNS name.
    producerProjectId: The Google project that owns this service.
    publishing: Settings for [Google Cloud Client
      libraries](https://cloud.google.com/apis/docs/cloud-client-libraries)
      generated from APIs defined as protocol buffers.
    quota: Quota configuration.
    sourceInfo: Output only. The source information for this configuration if
      available.
    systemParameters: System parameter configuration.
    systemTypes: A list of all proto message types included in this API
      service. It serves similar purpose as [google.api.Service.types], except
      that these types are not needed by user-defined APIs. Therefore, they
      will not show up in the generated discovery doc. This field should only
      be used to define system APIs in ESF.
    title: The product title for this service, it is the name displayed in
      Google Cloud Console.
    types: A list of all proto message types included in this API service.
      Types referenced directly or indirectly by the `apis` are automatically
      included. Messages which are not referenced but shall be included, such
      as types used by the `google.protobuf.Any` type, should be listed here
      by name by the configuration author. Example: types: - name:
      google.protobuf.Int32
    usage: Configuration controlling usage of this service.
  """

  apis = _messages.MessageField('Api', 1, repeated=True)
  authentication = _messages.MessageField('Authentication', 2)
  backend = _messages.MessageField('Backend', 3)
  billing = _messages.MessageField('Billing', 4)
  configVersion = _messages.IntegerField(5, variant=_messages.Variant.UINT32)
  context = _messages.MessageField('Context', 6)
  control = _messages.MessageField('Control', 7)
  customError = _messages.MessageField('CustomError', 8)
  documentation = _messages.MessageField('Documentation', 9)
  endpoints = _messages.MessageField('Endpoint', 10, repeated=True)
  enums = _messages.MessageField('Enum', 11, repeated=True)
  http = _messages.MessageField('Http', 12)
  id = _messages.StringField(13)
  logging = _messages.MessageField('Logging', 14)
  logs = _messages.MessageField('LogDescriptor', 15, repeated=True)
  metrics = _messages.MessageField('MetricDescriptor', 16, repeated=True)
  monitoredResources = _messages.MessageField('MonitoredResourceDescriptor', 17, repeated=True)
  monitoring = _messages.MessageField('Monitoring', 18)
  name = _messages.StringField(19)
  producerProjectId = _messages.StringField(20)
  publishing = _messages.MessageField('Publishing', 21)
  quota = _messages.MessageField('Quota', 22)
  sourceInfo = _messages.MessageField('SourceInfo', 23)
  systemParameters = _messages.MessageField('SystemParameters', 24)
  systemTypes = _messages.MessageField('Type', 25, repeated=True)
  title = _messages.StringField(26)
  types = _messages.MessageField('Type', 27, repeated=True)
  usage = _messages.MessageField('Usage', 28)


class ServicenetworkingOperationsCancelRequest(_messages.Message):
  r"""A ServicenetworkingOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class ServicenetworkingOperationsDeleteRequest(_messages.Message):
  r"""A ServicenetworkingOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class ServicenetworkingOperationsGetRequest(_messages.Message):
  r"""A ServicenetworkingOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class ServicenetworkingOperationsListRequest(_messages.Message):
  r"""A ServicenetworkingOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ServicenetworkingServicesAddSubnetworkRequest(_messages.Message):
  r"""A ServicenetworkingServicesAddSubnetworkRequest object.

  Fields:
    addSubnetworkRequest: A AddSubnetworkRequest resource to be passed as the
      request body.
    parent: Required. A tenant project in the service producer organization,
      in the following format: services/{service}/{collection-id}/{resource-
      id}. {collection-id} is the cloud resource collection type that
      represents the tenant project. Only `projects` are supported. {resource-
      id} is the tenant project numeric id, such as `123456`. {service} the
      name of the peering service, such as `service-peering.example.com`. This
      service must already be enabled in the service consumer's project.
  """

  addSubnetworkRequest = _messages.MessageField('AddSubnetworkRequest', 1)
  parent = _messages.StringField(2, required=True)


class ServicenetworkingServicesConnectionsCreateRequest(_messages.Message):
  r"""A ServicenetworkingServicesConnectionsCreateRequest object.

  Fields:
    connection: A Connection resource to be passed as the request body.
    parent: The service that is managing peering connectivity for a service
      producer's organization. For Google services that support this
      functionality, this value is
      `services/servicenetworking.googleapis.com`.
  """

  connection = _messages.MessageField('Connection', 1)
  parent = _messages.StringField(2, required=True)


class ServicenetworkingServicesConnectionsDeleteConnectionRequest(_messages.Message):
  r"""A ServicenetworkingServicesConnectionsDeleteConnectionRequest object.

  Fields:
    deleteConnectionRequest: A DeleteConnectionRequest resource to be passed
      as the request body.
    name: Required. The private service connection that connects to a service
      producer organization. The name includes both the private service name
      and the VPC network peering name in the format of
      `services/{peering_service_name}/connections/{vpc_peering_name}`. For
      Google services that support this functionality, this is `services/servi
      cenetworking.googleapis.com/connections/servicenetworking-googleapis-
      com`.
  """

  deleteConnectionRequest = _messages.MessageField('DeleteConnectionRequest', 1)
  name = _messages.StringField(2, required=True)


class ServicenetworkingServicesConnectionsListRequest(_messages.Message):
  r"""A ServicenetworkingServicesConnectionsListRequest object.

  Fields:
    network: The name of service consumer's VPC network that's connected with
      service producer network through a private connection. The network name
      must be in the following format:
      `projects/{project}/global/networks/{network}`. {project} is a project
      number, such as in `12345` that includes the VPC service consumer's VPC
      network. {network} is the name of the service consumer's VPC network.
    parent: The service that is managing peering connectivity for a service
      producer's organization. For Google services that support this
      functionality, this value is
      `services/servicenetworking.googleapis.com`. If you specify `services/-`
      as the parameter value, all configured peering services are listed.
  """

  network = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)


class ServicenetworkingServicesConnectionsPatchRequest(_messages.Message):
  r"""A ServicenetworkingServicesConnectionsPatchRequest object.

  Fields:
    connection: A Connection resource to be passed as the request body.
    force: If a previously defined allocated range is removed, force flag must
      be set to true.
    name: The private service connection that connects to a service producer
      organization. The name includes both the private service name and the
      VPC network peering name in the format of
      `services/{peering_service_name}/connections/{vpc_peering_name}`. For
      Google services that support this functionality, this is `services/servi
      cenetworking.googleapis.com/connections/servicenetworking-googleapis-
      com`.
    updateMask: The update mask. If this is omitted, it defaults to "*". You
      can only update the listed peering ranges.
  """

  connection = _messages.MessageField('Connection', 1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  updateMask = _messages.StringField(4)


class ServicenetworkingServicesDisableVpcServiceControlsRequest(_messages.Message):
  r"""A ServicenetworkingServicesDisableVpcServiceControlsRequest object.

  Fields:
    disableVpcServiceControlsRequest: A DisableVpcServiceControlsRequest
      resource to be passed as the request body.
    parent: The service that is managing peering connectivity for a service
      producer's organization. For Google services that support this
      functionality, this value is
      `services/servicenetworking.googleapis.com`.
  """

  disableVpcServiceControlsRequest = _messages.MessageField('DisableVpcServiceControlsRequest', 1)
  parent = _messages.StringField(2, required=True)


class ServicenetworkingServicesDnsRecordSetsAddRequest(_messages.Message):
  r"""A ServicenetworkingServicesDnsRecordSetsAddRequest object.

  Fields:
    addDnsRecordSetRequest: A AddDnsRecordSetRequest resource to be passed as
      the request body.
    parent: Required. The service that is managing peering connectivity for a
      service producer's organization. For Google services that support this
      functionality, this value is
      `services/servicenetworking.googleapis.com`.
  """

  addDnsRecordSetRequest = _messages.MessageField('AddDnsRecordSetRequest', 1)
  parent = _messages.StringField(2, required=True)


class ServicenetworkingServicesDnsRecordSetsGetRequest(_messages.Message):
  r"""A ServicenetworkingServicesDnsRecordSetsGetRequest object.

  Fields:
    consumerNetwork: Required. The consumer network containing the record set.
      Must be in the form of projects/{project}/global/networks/{network}
    domain: Required. The domain name of the zone containing the recordset.
    parent: Required. Parent resource identifying the connection which owns
      this collection of DNS zones in the format services/{service}.
    type: Required. RecordSet Type eg. type='A'. See the list of [Supported
      DNS Types](https://cloud.google.com/dns/records/json-record).
    zone: Required. The name of the zone containing the record set.
  """

  consumerNetwork = _messages.StringField(1)
  domain = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  type = _messages.StringField(4)
  zone = _messages.StringField(5)


class ServicenetworkingServicesDnsRecordSetsListRequest(_messages.Message):
  r"""A ServicenetworkingServicesDnsRecordSetsListRequest object.

  Fields:
    consumerNetwork: Required. The network that the consumer is using to
      connect with services. Must be in the form of
      projects/{project}/global/networks/{network} {project} is the project
      number, as in '12345' {network} is the network name.
    parent: Required. The service that is managing peering connectivity for a
      service producer's organization. For Google services that support this
      functionality, this value is
      `services/servicenetworking.googleapis.com`.
    zone: Required. The name of the private DNS zone in the shared producer
      host project from which the record set will be removed.
  """

  consumerNetwork = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)
  zone = _messages.StringField(3)


class ServicenetworkingServicesDnsRecordSetsRemoveRequest(_messages.Message):
  r"""A ServicenetworkingServicesDnsRecordSetsRemoveRequest object.

  Fields:
    parent: Required. The service that is managing peering connectivity for a
      service producer's organization. For Google services that support this
      functionality, this value is
      `services/servicenetworking.googleapis.com`.
    removeDnsRecordSetRequest: A RemoveDnsRecordSetRequest resource to be
      passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  removeDnsRecordSetRequest = _messages.MessageField('RemoveDnsRecordSetRequest', 2)


class ServicenetworkingServicesDnsRecordSetsUpdateRequest(_messages.Message):
  r"""A ServicenetworkingServicesDnsRecordSetsUpdateRequest object.

  Fields:
    parent: Required. The service that is managing peering connectivity for a
      service producer's organization. For Google services that support this
      functionality, this value is
      `services/servicenetworking.googleapis.com`.
    updateDnsRecordSetRequest: A UpdateDnsRecordSetRequest resource to be
      passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  updateDnsRecordSetRequest = _messages.MessageField('UpdateDnsRecordSetRequest', 2)


class ServicenetworkingServicesDnsZonesAddRequest(_messages.Message):
  r"""A ServicenetworkingServicesDnsZonesAddRequest object.

  Fields:
    addDnsZoneRequest: A AddDnsZoneRequest resource to be passed as the
      request body.
    parent: Required. The service that is managing peering connectivity for a
      service producer's organization. For Google services that support this
      functionality, this value is
      `services/servicenetworking.googleapis.com`.
  """

  addDnsZoneRequest = _messages.MessageField('AddDnsZoneRequest', 1)
  parent = _messages.StringField(2, required=True)


class ServicenetworkingServicesDnsZonesRemoveRequest(_messages.Message):
  r"""A ServicenetworkingServicesDnsZonesRemoveRequest object.

  Fields:
    parent: Required. The service that is managing peering connectivity for a
      service producer's organization. For Google services that support this
      functionality, this value is
      `services/servicenetworking.googleapis.com`.
    removeDnsZoneRequest: A RemoveDnsZoneRequest resource to be passed as the
      request body.
  """

  parent = _messages.StringField(1, required=True)
  removeDnsZoneRequest = _messages.MessageField('RemoveDnsZoneRequest', 2)


class ServicenetworkingServicesEnableVpcServiceControlsRequest(_messages.Message):
  r"""A ServicenetworkingServicesEnableVpcServiceControlsRequest object.

  Fields:
    enableVpcServiceControlsRequest: A EnableVpcServiceControlsRequest
      resource to be passed as the request body.
    parent: The service that is managing peering connectivity for a service
      producer's organization. For Google services that support this
      functionality, this value is
      `services/servicenetworking.googleapis.com`.
  """

  enableVpcServiceControlsRequest = _messages.MessageField('EnableVpcServiceControlsRequest', 1)
  parent = _messages.StringField(2, required=True)


class ServicenetworkingServicesProjectsGlobalNetworksDnsZonesGetRequest(_messages.Message):
  r"""A ServicenetworkingServicesProjectsGlobalNetworksDnsZonesGetRequest
  object.

  Fields:
    name: Required. The network that the consumer is using to connect with
      services. Must be in the form of services/{service}/projects/{project}/g
      lobal/networks/{network}/zones/{zoneName} Where {service} is the peering
      service that is managing connectivity for the service producer's
      organization. For Google services that support this {project} is the
      project number, as in '12345' {network} is the network name. {zoneName}
      is the DNS zone name
  """

  name = _messages.StringField(1, required=True)


class ServicenetworkingServicesProjectsGlobalNetworksDnsZonesListRequest(_messages.Message):
  r"""A ServicenetworkingServicesProjectsGlobalNetworksDnsZonesListRequest
  object.

  Fields:
    parent: Required. Parent resource identifying the connection which owns
      this collection of DNS zones in the format
      services/{service}/projects/{project}/global/networks/{network} Service:
      The service that is managing connectivity for the service producer's
      organization. For Google services that support this functionality, this
      value is `servicenetworking.googleapis.com`. Projects: the consumer
      project containing the consumer network. Network: The consumer network
      accessible from the tenant project.
  """

  parent = _messages.StringField(1, required=True)


class ServicenetworkingServicesProjectsGlobalNetworksGetRequest(_messages.Message):
  r"""A ServicenetworkingServicesProjectsGlobalNetworksGetRequest object.

  Fields:
    includeUsedIpRanges: Optional. When true, include the used IP ranges as
      part of the GetConsumerConfig output. This includes routes created
      inside the service networking network, consumer network, peers of the
      consumer network, and reserved ranges inside the service networking
      network. By default, this is false
    name: Required. Name of the consumer config to retrieve in the format:
      `services/{service}/projects/{project}/global/networks/{network}`.
      {service} is the peering service that is managing connectivity for the
      service producer's organization. For Google services that support this
      functionality, this value is `servicenetworking.googleapis.com`.
      {project} is a project number e.g. `12345` that contains the service
      consumer's VPC network. {network} is the name of the service consumer's
      VPC network.
  """

  includeUsedIpRanges = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class ServicenetworkingServicesProjectsGlobalNetworksPeeredDnsDomainsCreateRequest(_messages.Message):
  r"""A
  ServicenetworkingServicesProjectsGlobalNetworksPeeredDnsDomainsCreateRequest
  object.

  Fields:
    parent: Required. Parent resource identifying the connection for which the
      peered DNS domain will be created in the format:
      `services/{service}/projects/{project}/global/networks/{network}`
      {service} is the peering service that is managing connectivity for the
      service producer's organization. For Google services that support this
      functionality, this value is `servicenetworking.googleapis.com`.
      {project} is the number of the project that contains the service
      consumer's VPC network e.g. `12345`. {network} is the name of the
      service consumer's VPC network.
    peeredDnsDomain: A PeeredDnsDomain resource to be passed as the request
      body.
  """

  parent = _messages.StringField(1, required=True)
  peeredDnsDomain = _messages.MessageField('PeeredDnsDomain', 2)


class ServicenetworkingServicesProjectsGlobalNetworksPeeredDnsDomainsDeleteRequest(_messages.Message):
  r"""A
  ServicenetworkingServicesProjectsGlobalNetworksPeeredDnsDomainsDeleteRequest
  object.

  Fields:
    name: Required. The name of the peered DNS domain to delete in the format:
      `services/{service}/projects/{project}/global/networks/{network}/peeredD
      nsDomains/{name}`. {service} is the peering service that is managing
      connectivity for the service producer's organization. For Google
      services that support this functionality, this value is
      `servicenetworking.googleapis.com`. {project} is the number of the
      project that contains the service consumer's VPC network e.g. `12345`.
      {network} is the name of the service consumer's VPC network. {name} is
      the name of the peered DNS domain.
  """

  name = _messages.StringField(1, required=True)


class ServicenetworkingServicesProjectsGlobalNetworksPeeredDnsDomainsListRequest(_messages.Message):
  r"""A
  ServicenetworkingServicesProjectsGlobalNetworksPeeredDnsDomainsListRequest
  object.

  Fields:
    parent: Required. Parent resource identifying the connection which owns
      this collection of peered DNS domains in the format:
      `services/{service}/projects/{project}/global/networks/{network}`.
      {service} is the peering service that is managing connectivity for the
      service producer's organization. For Google services that support this
      functionality, this value is `servicenetworking.googleapis.com`.
      {project} is a project number e.g. `12345` that contains the service
      consumer's VPC network. {network} is the name of the service consumer's
      VPC network.
  """

  parent = _messages.StringField(1, required=True)


class ServicenetworkingServicesProjectsGlobalNetworksUpdateConsumerConfigRequest(_messages.Message):
  r"""A
  ServicenetworkingServicesProjectsGlobalNetworksUpdateConsumerConfigRequest
  object.

  Fields:
    parent: Required. Parent resource identifying the connection for which the
      consumer config is being updated in the format:
      `services/{service}/projects/{project}/global/networks/{network}`
      {service} is the peering service that is managing connectivity for the
      service producer's organization. For Google services that support this
      functionality, this value is `servicenetworking.googleapis.com`.
      {project} is the number of the project that contains the service
      consumer's VPC network e.g. `12345`. {network} is the name of the
      service consumer's VPC network.
    updateConsumerConfigRequest: A UpdateConsumerConfigRequest resource to be
      passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  updateConsumerConfigRequest = _messages.MessageField('UpdateConsumerConfigRequest', 2)


class ServicenetworkingServicesRolesAddRequest(_messages.Message):
  r"""A ServicenetworkingServicesRolesAddRequest object.

  Fields:
    addRolesRequest: A AddRolesRequest resource to be passed as the request
      body.
    parent: Required. This is in a form services/{service} where {service} is
      the name of the private access management service. For example 'service-
      peering.example.com'.
  """

  addRolesRequest = _messages.MessageField('AddRolesRequest', 1)
  parent = _messages.StringField(2, required=True)


class ServicenetworkingServicesSearchRangeRequest(_messages.Message):
  r"""A ServicenetworkingServicesSearchRangeRequest object.

  Fields:
    parent: Required. This is in a form services/{service}. {service} the name
      of the private access management service, for example 'service-
      peering.example.com'.
    searchRangeRequest: A SearchRangeRequest resource to be passed as the
      request body.
  """

  parent = _messages.StringField(1, required=True)
  searchRangeRequest = _messages.MessageField('SearchRangeRequest', 2)


class ServicenetworkingServicesValidateRequest(_messages.Message):
  r"""A ServicenetworkingServicesValidateRequest object.

  Fields:
    parent: Required. This is in a form services/{service} where {service} is
      the name of the private access management service. For example 'service-
      peering.example.com'.
    validateConsumerConfigRequest: A ValidateConsumerConfigRequest resource to
      be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  validateConsumerConfigRequest = _messages.MessageField('ValidateConsumerConfigRequest', 2)


class SourceContext(_messages.Message):
  r"""`SourceContext` represents information about the source of a protobuf
  element, like the file in which it is defined.

  Fields:
    fileName: The path-qualified name of the .proto file that contained the
      associated protobuf element. For example:
      `"google/protobuf/source_context.proto"`.
  """

  fileName = _messages.StringField(1)


class SourceInfo(_messages.Message):
  r"""Source information used to create a Service Config

  Messages:
    SourceFilesValueListEntry: A SourceFilesValueListEntry object.

  Fields:
    sourceFiles: All files used during config generation.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SourceFilesValueListEntry(_messages.Message):
    r"""A SourceFilesValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a
        SourceFilesValueListEntry object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SourceFilesValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  sourceFiles = _messages.MessageField('SourceFilesValueListEntry', 1, repeated=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Subnetwork(_messages.Message):
  r"""Represents a subnet that was created or discovered by a private access
  management service.

  Fields:
    ipCidrRange: Subnetwork CIDR range in `10.x.x.x/y` format.
    name: Subnetwork name. See https://cloud.google.com/compute/docs/vpc/
    network: In the Shared VPC host project, the VPC network that's peered
      with the consumer network. For example:
      `projects/1234321/global/networks/host-network`
    outsideAllocation: This is a discovered subnet that is not within the
      current consumer allocated ranges.
    region: GCP region where the subnetwork is located.
    secondaryIpRanges: List of secondary IP ranges in this subnetwork.
  """

  ipCidrRange = _messages.StringField(1)
  name = _messages.StringField(2)
  network = _messages.StringField(3)
  outsideAllocation = _messages.BooleanField(4)
  region = _messages.StringField(5)
  secondaryIpRanges = _messages.MessageField('SecondaryIpRange', 6, repeated=True)


class SystemParameter(_messages.Message):
  r"""Define a parameter's name and location. The parameter may be passed as
  either an HTTP header or a URL query parameter, and if both are passed the
  behavior is implementation-dependent.

  Fields:
    httpHeader: Define the HTTP header name to use for the parameter. It is
      case insensitive.
    name: Define the name of the parameter, such as "api_key" . It is case
      sensitive.
    urlQueryParameter: Define the URL query parameter name to use for the
      parameter. It is case sensitive.
  """

  httpHeader = _messages.StringField(1)
  name = _messages.StringField(2)
  urlQueryParameter = _messages.StringField(3)


class SystemParameterRule(_messages.Message):
  r"""Define a system parameter rule mapping system parameter definitions to
  methods.

  Fields:
    parameters: Define parameters. Multiple names may be defined for a
      parameter. For a given method call, only one of them should be used. If
      multiple names are used the behavior is implementation-dependent. If
      none of the specified names are present the behavior is parameter-
      dependent.
    selector: Selects the methods to which this rule applies. Use '*' to
      indicate all methods in all APIs. Refer to selector for syntax details.
  """

  parameters = _messages.MessageField('SystemParameter', 1, repeated=True)
  selector = _messages.StringField(2)


class SystemParameters(_messages.Message):
  r"""### System parameter configuration A system parameter is a special kind
  of parameter defined by the API system, not by an individual API. It is
  typically mapped to an HTTP header and/or a URL query parameter. This
  configuration specifies which methods change the names of the system
  parameters.

  Fields:
    rules: Define system parameters. The parameters defined here will override
      the default parameters implemented by the system. If this field is
      missing from the service config, default system parameters will be used.
      Default system parameters and names is implementation-dependent.
      Example: define api key for all methods system_parameters rules: -
      selector: "*" parameters: - name: api_key url_query_parameter: api_key
      Example: define 2 api key names for a specific method. system_parameters
      rules: - selector: "/ListShelves" parameters: - name: api_key
      http_header: Api-Key1 - name: api_key http_header: Api-Key2 **NOTE:**
      All service configuration rules follow "last one wins" order.
  """

  rules = _messages.MessageField('SystemParameterRule', 1, repeated=True)


class Type(_messages.Message):
  r"""A protocol buffer message type.

  Enums:
    SyntaxValueValuesEnum: The source syntax.

  Fields:
    edition: The source edition string, only valid when syntax is
      SYNTAX_EDITIONS.
    fields: The list of fields.
    name: The fully qualified message name.
    oneofs: The list of types appearing in `oneof` definitions in this type.
    options: The protocol buffer options.
    sourceContext: The source context.
    syntax: The source syntax.
  """

  class SyntaxValueValuesEnum(_messages.Enum):
    r"""The source syntax.

    Values:
      SYNTAX_PROTO2: Syntax `proto2`.
      SYNTAX_PROTO3: Syntax `proto3`.
      SYNTAX_EDITIONS: Syntax `editions`.
    """
    SYNTAX_PROTO2 = 0
    SYNTAX_PROTO3 = 1
    SYNTAX_EDITIONS = 2

  edition = _messages.StringField(1)
  fields = _messages.MessageField('Field', 2, repeated=True)
  name = _messages.StringField(3)
  oneofs = _messages.StringField(4, repeated=True)
  options = _messages.MessageField('Option', 5, repeated=True)
  sourceContext = _messages.MessageField('SourceContext', 6)
  syntax = _messages.EnumField('SyntaxValueValuesEnum', 7)


class UpdateConsumerConfigRequest(_messages.Message):
  r"""Request to update the configuration of a service networking connection
  including the import/export of custom routes and subnetwork routes with
  public IP.

  Fields:
    consumerConfig: Required. The updated peering config.
  """

  consumerConfig = _messages.MessageField('ConsumerConfig', 1)


class UpdateDnsRecordSetMetadata(_messages.Message):
  r"""Metadata provided through GetOperation request for the LRO generated by
  UpdateDnsRecordSet API
  """



class UpdateDnsRecordSetRequest(_messages.Message):
  r"""Request to update a record set from a private managed DNS zone in the
  shared producer host project. The name, type, ttl, and data values of the
  existing record set must all exactly match an existing record set in the
  specified zone.

  Fields:
    consumerNetwork: Required. The network that the consumer is using to
      connect with services. Must be in the form of
      projects/{project}/global/networks/{network} {project} is the project
      number, as in '12345' {network} is the network name.
    existingDnsRecordSet: Required. The existing DNS record set to update.
    newDnsRecordSet: Required. The new values that the DNS record set should
      be updated to hold.
    zone: Required. The name of the private DNS zone in the shared producer
      host project from which the record set will be removed.
  """

  consumerNetwork = _messages.StringField(1)
  existingDnsRecordSet = _messages.MessageField('DnsRecordSet', 2)
  newDnsRecordSet = _messages.MessageField('DnsRecordSet', 3)
  zone = _messages.StringField(4)


class Usage(_messages.Message):
  r"""Configuration controlling usage of a service.

  Fields:
    producerNotificationChannel: The full resource name of a channel used for
      sending notifications to the service producer. Google Service Management
      currently only supports [Google Cloud
      Pub/Sub](https://cloud.google.com/pubsub) as a notification channel. To
      use Google Cloud Pub/Sub as the channel, this must be the name of a
      Cloud Pub/Sub topic that uses the Cloud Pub/Sub topic name format
      documented in https://cloud.google.com/pubsub/docs/overview.
    requirements: Requirements that must be satisfied before a consumer
      project can use the service. Each requirement is of the form /; for
      example 'serviceusage.googleapis.com/billing-enabled'. For Google APIs,
      a Terms of Service requirement must be included here. Google Cloud APIs
      must include "serviceusage.googleapis.com/tos/cloud". Other Google APIs
      should include "serviceusage.googleapis.com/tos/universal". Additional
      ToS can be included based on the business needs.
    rules: A list of usage rules that apply to individual API methods.
      **NOTE:** All service configuration rules follow "last one wins" order.
  """

  producerNotificationChannel = _messages.StringField(1)
  requirements = _messages.StringField(2, repeated=True)
  rules = _messages.MessageField('UsageRule', 3, repeated=True)


class UsageRule(_messages.Message):
  r"""Usage configuration rules for the service. NOTE: Under development. Use
  this rule to configure unregistered calls for the service. Unregistered
  calls are calls that do not contain consumer project identity. (Example:
  calls that do not contain an API key). By default, API methods do not allow
  unregistered calls, and each method call must be identified by a consumer
  project identity. Use this rule to allow/disallow unregistered calls.
  Example of an API that wants to allow unregistered calls for entire service.
  usage: rules: - selector: "*" allow_unregistered_calls: true Example of a
  method that wants to allow unregistered calls. usage: rules: - selector:
  "google.example.library.v1.LibraryService.CreateBook"
  allow_unregistered_calls: true

  Fields:
    allowUnregisteredCalls: If true, the selected method allows unregistered
      calls, e.g. calls that don't identify any user or application.
    selector: Selects the methods to which this rule applies. Use '*' to
      indicate all methods in all APIs. Refer to selector for syntax details.
    skipServiceControl: If true, the selected method should skip service
      control and the control plane features, such as quota and billing, will
      not be available. This flag is used by Google Cloud Endpoints to bypass
      checks for internal methods, such as service health check methods.
  """

  allowUnregisteredCalls = _messages.BooleanField(1)
  selector = _messages.StringField(2)
  skipServiceControl = _messages.BooleanField(3)


class ValidateConsumerConfigRequest(_messages.Message):
  r"""A ValidateConsumerConfigRequest object.

  Fields:
    checkServiceNetworkingUsePermission: Optional. The IAM permission check
      determines whether the consumer project has
      'servicenetworking.services.use' permission or not.
    consumerNetwork: Required. The network that the consumer is using to
      connect with services. Must be in the form of
      projects/{project}/global/networks/{network} {project} is a project
      number, as in '12345' {network} is network name.
    consumerProject: NETWORK_NOT_IN_CONSUMERS_PROJECT,
      NETWORK_NOT_IN_CONSUMERS_HOST_PROJECT, and HOST_PROJECT_NOT_FOUND are
      done when consumer_project is provided.
    rangeReservation: RANGES_EXHAUSTED, RANGES_EXHAUSTED, and
      RANGES_DELETED_LATER are done when range_reservation is provided.
    validateNetwork: The validations will be performed in the order listed in
      the ValidationError enum. The first failure will return. If a validation
      is not requested, then the next one will be performed.
      SERVICE_NETWORKING_NOT_ENABLED and NETWORK_NOT_PEERED checks are
      performed for all requests where validation is requested.
      NETWORK_NOT_FOUND and NETWORK_DISCONNECTED checks are done for requests
      that have validate_network set to true.
  """

  checkServiceNetworkingUsePermission = _messages.BooleanField(1)
  consumerNetwork = _messages.StringField(2)
  consumerProject = _messages.MessageField('ConsumerProject', 3)
  rangeReservation = _messages.MessageField('RangeReservation', 4)
  validateNetwork = _messages.BooleanField(5)


class ValidateConsumerConfigResponse(_messages.Message):
  r"""A ValidateConsumerConfigResponse object.

  Enums:
    ValidationErrorValueValuesEnum: The first validation which failed.

  Fields:
    existingSubnetworkCandidates: List of subnetwork candidates from the
      request which exist with the `ip_cidr_range`,
      `secondary_ip_cider_ranges`, and `outside_allocation` fields set.
    isValid: Indicates whether all the requested validations passed.
    validationError: The first validation which failed.
  """

  class ValidationErrorValueValuesEnum(_messages.Enum):
    r"""The first validation which failed.

    Values:
      VALIDATION_ERROR_UNSPECIFIED: <no description>
      VALIDATION_NOT_REQUESTED: In case none of the validations are requested.
      SERVICE_NETWORKING_NOT_ENABLED: <no description>
      NETWORK_NOT_FOUND: The network provided by the consumer does not exist.
      NETWORK_NOT_PEERED: The network has not been peered with the producer
        org.
      NETWORK_PEERING_DELETED: The peering was created and later deleted.
      NETWORK_NOT_IN_CONSUMERS_PROJECT: The network is a regular VPC but the
        network is not in the consumer's project.
      NETWORK_NOT_IN_CONSUMERS_HOST_PROJECT: The consumer project is a service
        project, and network is a shared VPC, but the network is not in the
        host project of this consumer project.
      HOST_PROJECT_NOT_FOUND: The host project associated with the consumer
        project was not found.
      CONSUMER_PROJECT_NOT_SERVICE_PROJECT: The consumer project is not a
        service project for the specified host project.
      RANGES_EXHAUSTED: The reserved IP ranges do not have enough space to
        create a subnet of desired size.
      RANGES_NOT_RESERVED: The IP ranges were not reserved.
      RANGES_DELETED_LATER: The IP ranges were reserved but deleted later.
      COMPUTE_API_NOT_ENABLED: The consumer project does not have the compute
        api enabled.
      USE_PERMISSION_NOT_FOUND: The consumer project does not have the
        permission from the host project.
    """
    VALIDATION_ERROR_UNSPECIFIED = 0
    VALIDATION_NOT_REQUESTED = 1
    SERVICE_NETWORKING_NOT_ENABLED = 2
    NETWORK_NOT_FOUND = 3
    NETWORK_NOT_PEERED = 4
    NETWORK_PEERING_DELETED = 5
    NETWORK_NOT_IN_CONSUMERS_PROJECT = 6
    NETWORK_NOT_IN_CONSUMERS_HOST_PROJECT = 7
    HOST_PROJECT_NOT_FOUND = 8
    CONSUMER_PROJECT_NOT_SERVICE_PROJECT = 9
    RANGES_EXHAUSTED = 10
    RANGES_NOT_RESERVED = 11
    RANGES_DELETED_LATER = 12
    COMPUTE_API_NOT_ENABLED = 13
    USE_PERMISSION_NOT_FOUND = 14

  existingSubnetworkCandidates = _messages.MessageField('Subnetwork', 1, repeated=True)
  isValid = _messages.BooleanField(2)
  validationError = _messages.EnumField('ValidationErrorValueValuesEnum', 3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

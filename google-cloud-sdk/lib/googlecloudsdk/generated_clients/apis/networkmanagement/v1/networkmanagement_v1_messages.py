"""Generated message classes for networkmanagement version v1.

The Network Management API provides a collection of network performance
monitoring and diagnostic capabilities.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'networkmanagement'


class AbortInfo(_messages.Message):
  r"""Details of the final state "abort" and associated resource.

  Enums:
    CauseValueValuesEnum: Causes that the analysis is aborted.

  Fields:
    cause: Causes that the analysis is aborted.
    projectsMissingPermission: List of project IDs that the user has specified
      in the request but does not have permission to access network configs.
      Analysis is aborted in this case with the PERMISSION_DENIED cause.
    resourceUri: URI of the resource that caused the abort.
  """

  class CauseValueValuesEnum(_messages.Enum):
    r"""Causes that the analysis is aborted.

    Values:
      CAUSE_UNSPECIFIED: Cause is unspecified.
      UNKNOWN_NETWORK: Aborted due to unknown network. The reachability
        analysis cannot proceed because the user does not have access to the
        host project's network configurations, including firewall rules and
        routes. This happens when the project is a service project and the
        endpoints being traced are in the host project's network.
      UNKNOWN_IP: Aborted because the IP address(es) are unknown.
      UNKNOWN_PROJECT: Aborted because no project information can be derived
        from the test input.
      PERMISSION_DENIED: Aborted because the user lacks the permission to
        access all or part of the network configurations required to run the
        test.
      NO_SOURCE_LOCATION: Aborted because no valid source endpoint is derived
        from the input test request.
      INVALID_ARGUMENT: Aborted because the source and/or destination endpoint
        specified in the test are invalid. The possible reasons that an
        endpoint is invalid include: malformed IP address; nonexistent
        instance or network URI; IP address not in the range of specified
        network URI; and instance not owning the network interface in the
        specified network.
      NO_EXTERNAL_IP: Aborted because traffic is sent from a public IP to an
        instance without an external IP.
      UNINTENDED_DESTINATION: Aborted because none of the traces matches
        destination information specified in the input test request.
      TRACE_TOO_LONG: Aborted because the number of steps in the trace
        exceeding a certain limit which may be caused by routing loop.
      INTERNAL_ERROR: Aborted due to internal server error.
      SOURCE_ENDPOINT_NOT_FOUND: Aborted because the source endpoint could not
        be found.
      MISMATCHED_SOURCE_NETWORK: Aborted because the source network does not
        match the source endpoint.
      DESTINATION_ENDPOINT_NOT_FOUND: Aborted because the destination endpoint
        could not be found.
      MISMATCHED_DESTINATION_NETWORK: Aborted because the destination network
        does not match the destination endpoint.
      UNSUPPORTED: Aborted because the test scenario is not supported.
      MISMATCHED_IP_VERSION: Aborted because the source and destination
        resources have no common IP version.
      GKE_KONNECTIVITY_PROXY_UNSUPPORTED: Aborted because the connection
        between the control plane and the node of the source cluster is
        initiated by the node and managed by the Konnectivity proxy.
      RESOURCE_CONFIG_NOT_FOUND: Aborted because expected resource
        configuration was missing.
      GOOGLE_MANAGED_SERVICE_AMBIGUOUS_PSC_ENDPOINT: Aborted because a PSC
        endpoint selection for the Google-managed service is ambiguous
        (several PSC endpoints satisfy test input).
      SOURCE_PSC_CLOUD_SQL_UNSUPPORTED: Aborted because tests with a PSC-based
        Cloud SQL instance as a source are not supported.
      SOURCE_FORWARDING_RULE_UNSUPPORTED: Aborted because tests with a
        forwarding rule as a source are not supported.
    """
    CAUSE_UNSPECIFIED = 0
    UNKNOWN_NETWORK = 1
    UNKNOWN_IP = 2
    UNKNOWN_PROJECT = 3
    PERMISSION_DENIED = 4
    NO_SOURCE_LOCATION = 5
    INVALID_ARGUMENT = 6
    NO_EXTERNAL_IP = 7
    UNINTENDED_DESTINATION = 8
    TRACE_TOO_LONG = 9
    INTERNAL_ERROR = 10
    SOURCE_ENDPOINT_NOT_FOUND = 11
    MISMATCHED_SOURCE_NETWORK = 12
    DESTINATION_ENDPOINT_NOT_FOUND = 13
    MISMATCHED_DESTINATION_NETWORK = 14
    UNSUPPORTED = 15
    MISMATCHED_IP_VERSION = 16
    GKE_KONNECTIVITY_PROXY_UNSUPPORTED = 17
    RESOURCE_CONFIG_NOT_FOUND = 18
    GOOGLE_MANAGED_SERVICE_AMBIGUOUS_PSC_ENDPOINT = 19
    SOURCE_PSC_CLOUD_SQL_UNSUPPORTED = 20
    SOURCE_FORWARDING_RULE_UNSUPPORTED = 21

  cause = _messages.EnumField('CauseValueValuesEnum', 1)
  projectsMissingPermission = _messages.StringField(2, repeated=True)
  resourceUri = _messages.StringField(3)


class AppEngineVersionEndpoint(_messages.Message):
  r"""Wrapper for the App Engine service version attributes.

  Fields:
    uri: An [App Engine](https://cloud.google.com/appengine) [service
      version](https://cloud.google.com/appengine/docs/admin-
      api/reference/rest/v1/apps.services.versions) name.
  """

  uri = _messages.StringField(1)


class AppEngineVersionInfo(_messages.Message):
  r"""For display only. Metadata associated with an App Engine version.

  Fields:
    displayName: Name of an App Engine version.
    environment: App Engine execution environment for a version.
    runtime: Runtime of the App Engine version.
    uri: URI of an App Engine version.
  """

  displayName = _messages.StringField(1)
  environment = _messages.StringField(2)
  runtime = _messages.StringField(3)
  uri = _messages.StringField(4)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CloudFunctionEndpoint(_messages.Message):
  r"""Wrapper for Cloud Function attributes.

  Fields:
    uri: A [Cloud Function](https://cloud.google.com/functions) name.
  """

  uri = _messages.StringField(1)


class CloudFunctionInfo(_messages.Message):
  r"""For display only. Metadata associated with a Cloud Function.

  Fields:
    displayName: Name of a Cloud Function.
    location: Location in which the Cloud Function is deployed.
    uri: URI of a Cloud Function.
    versionId: Latest successfully deployed version id of the Cloud Function.
  """

  displayName = _messages.StringField(1)
  location = _messages.StringField(2)
  uri = _messages.StringField(3)
  versionId = _messages.IntegerField(4)


class CloudRunRevisionEndpoint(_messages.Message):
  r"""Wrapper for Cloud Run revision attributes.

  Fields:
    uri: A [Cloud Run](https://cloud.google.com/run) [revision](https://cloud.
      google.com/run/docs/reference/rest/v1/namespaces.revisions/get) URI. The
      format is: projects/{project}/locations/{location}/revisions/{revision}
  """

  uri = _messages.StringField(1)


class CloudRunRevisionInfo(_messages.Message):
  r"""For display only. Metadata associated with a Cloud Run revision.

  Fields:
    displayName: Name of a Cloud Run revision.
    location: Location in which this revision is deployed.
    serviceUri: URI of Cloud Run service this revision belongs to.
    uri: URI of a Cloud Run revision.
  """

  displayName = _messages.StringField(1)
  location = _messages.StringField(2)
  serviceUri = _messages.StringField(3)
  uri = _messages.StringField(4)


class CloudSQLInstanceInfo(_messages.Message):
  r"""For display only. Metadata associated with a Cloud SQL instance.

  Fields:
    displayName: Name of a Cloud SQL instance.
    externalIp: External IP address of a Cloud SQL instance.
    internalIp: Internal IP address of a Cloud SQL instance.
    networkUri: URI of a Cloud SQL instance network or empty string if the
      instance does not have one.
    region: Region in which the Cloud SQL instance is running.
    uri: URI of a Cloud SQL instance.
  """

  displayName = _messages.StringField(1)
  externalIp = _messages.StringField(2)
  internalIp = _messages.StringField(3)
  networkUri = _messages.StringField(4)
  region = _messages.StringField(5)
  uri = _messages.StringField(6)


class ConnectivityTest(_messages.Message):
  r"""A Connectivity Test for a network reachability analysis.

  Messages:
    LabelsValue: Resource labels to represent user-provided metadata.

  Fields:
    createTime: Output only. The time the test was created.
    description: The user-supplied description of the Connectivity Test.
      Maximum of 512 characters.
    destination: Required. Destination specification of the Connectivity Test.
      You can use a combination of destination IP address, Compute Engine VM
      instance, or VPC network to uniquely identify the destination location.
      Even if the destination IP address is not unique, the source IP location
      is unique. Usually, the analysis can infer the destination endpoint from
      route information. If the destination you specify is a VM instance and
      the instance has multiple network interfaces, then you must also specify
      either a destination IP address or VPC network to identify the
      destination interface. A reachability analysis proceeds even if the
      destination location is ambiguous. However, the result can include
      endpoints that you don't intend to test.
    displayName: Output only. The display name of a Connectivity Test.
    labels: Resource labels to represent user-provided metadata.
    name: Required. Unique name of the resource using the form:
      `projects/{project_id}/locations/global/connectivityTests/{test_id}`
    protocol: IP Protocol of the test. When not provided, "TCP" is assumed.
    reachabilityDetails: Output only. The reachability details of this test
      from the latest run. The details are updated when creating a new test,
      updating an existing test, or triggering a one-time rerun of an existing
      test.
    relatedProjects: Other projects that may be relevant for reachability
      analysis. This is applicable to scenarios where a test can cross project
      boundaries.
    source: Required. Source specification of the Connectivity Test. You can
      use a combination of source IP address, virtual machine (VM) instance,
      or Compute Engine network to uniquely identify the source location.
      Examples: If the source IP address is an internal IP address within a
      Google Cloud Virtual Private Cloud (VPC) network, then you must also
      specify the VPC network. Otherwise, specify the VM instance, which
      already contains its internal IP address and VPC network information. If
      the source of the test is within an on-premises network, then you must
      provide the destination VPC network. If the source endpoint is a Compute
      Engine VM instance with multiple network interfaces, the instance itself
      is not sufficient to identify the endpoint. So, you must also specify
      the source IP address or VPC network. A reachability analysis proceeds
      even if the source location is ambiguous. However, the test result may
      include endpoints that you don't intend to test.
    updateTime: Output only. The time the test's configuration was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Resource labels to represent user-provided metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  destination = _messages.MessageField('Endpoint', 3)
  displayName = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  protocol = _messages.StringField(7)
  reachabilityDetails = _messages.MessageField('ReachabilityDetails', 8)
  relatedProjects = _messages.StringField(9, repeated=True)
  source = _messages.MessageField('Endpoint', 10)
  updateTime = _messages.StringField(11)


class DeliverInfo(_messages.Message):
  r"""Details of the final state "deliver" and associated resource.

  Enums:
    TargetValueValuesEnum: Target type where the packet is delivered to.

  Fields:
    resourceUri: URI of the resource that the packet is delivered to.
    target: Target type where the packet is delivered to.
  """

  class TargetValueValuesEnum(_messages.Enum):
    r"""Target type where the packet is delivered to.

    Values:
      TARGET_UNSPECIFIED: Target not specified.
      INSTANCE: Target is a Compute Engine instance.
      INTERNET: Target is the internet.
      GOOGLE_API: Target is a Google API.
      GKE_MASTER: Target is a Google Kubernetes Engine cluster master.
      CLOUD_SQL_INSTANCE: Target is a Cloud SQL instance.
      PSC_PUBLISHED_SERVICE: Target is a published service that uses [Private
        Service Connect](https://cloud.google.com/vpc/docs/configure-private-
        service-connect-services).
      PSC_GOOGLE_API: Target is all Google APIs that use [Private Service
        Connect](https://cloud.google.com/vpc/docs/configure-private-service-
        connect-apis).
      PSC_VPC_SC: Target is a VPC-SC that uses [Private Service
        Connect](https://cloud.google.com/vpc/docs/configure-private-service-
        connect-apis).
    """
    TARGET_UNSPECIFIED = 0
    INSTANCE = 1
    INTERNET = 2
    GOOGLE_API = 3
    GKE_MASTER = 4
    CLOUD_SQL_INSTANCE = 5
    PSC_PUBLISHED_SERVICE = 6
    PSC_GOOGLE_API = 7
    PSC_VPC_SC = 8

  resourceUri = _messages.StringField(1)
  target = _messages.EnumField('TargetValueValuesEnum', 2)


class DropInfo(_messages.Message):
  r"""Details of the final state "drop" and associated resource.

  Enums:
    CauseValueValuesEnum: Cause that the packet is dropped.

  Fields:
    cause: Cause that the packet is dropped.
    resourceUri: URI of the resource that caused the drop.
  """

  class CauseValueValuesEnum(_messages.Enum):
    r"""Cause that the packet is dropped.

    Values:
      CAUSE_UNSPECIFIED: Cause is unspecified.
      UNKNOWN_EXTERNAL_ADDRESS: Destination external address cannot be
        resolved to a known target. If the address is used in a Google Cloud
        project, provide the project ID as test input.
      FOREIGN_IP_DISALLOWED: A Compute Engine instance can only send or
        receive a packet with a foreign IP address if ip_forward is enabled.
      FIREWALL_RULE: Dropped due to a firewall rule, unless allowed due to
        connection tracking.
      NO_ROUTE: Dropped due to no routes.
      ROUTE_BLACKHOLE: Dropped due to invalid route. Route's next hop is a
        blackhole.
      ROUTE_WRONG_NETWORK: Packet is sent to a wrong (unintended) network.
        Example: you trace a packet from VM1:Network1 to VM2:Network2,
        however, the route configured in Network1 sends the packet destined
        for VM2's IP addresss to Network3.
      PRIVATE_TRAFFIC_TO_INTERNET: Packet with internal destination address
        sent to the internet gateway.
      PRIVATE_GOOGLE_ACCESS_DISALLOWED: Instance with only an internal IP
        address tries to access Google API and services, but private Google
        access is not enabled.
      NO_EXTERNAL_ADDRESS: Instance with only an internal IP address tries to
        access external hosts, but Cloud NAT is not enabled in the subnet,
        unless special configurations on a VM allow this connection.
      UNKNOWN_INTERNAL_ADDRESS: Destination internal address cannot be
        resolved to a known target. If this is a shared VPC scenario, verify
        if the service project ID is provided as test input. Otherwise, verify
        if the IP address is being used in the project.
      FORWARDING_RULE_MISMATCH: Forwarding rule's protocol and ports do not
        match the packet header.
      FORWARDING_RULE_REGION_MISMATCH: Packet could be dropped because it was
        sent from a different region to a regional forwarding without global
        access.
      FORWARDING_RULE_NO_INSTANCES: Forwarding rule does not have backends
        configured.
      FIREWALL_BLOCKING_LOAD_BALANCER_BACKEND_HEALTH_CHECK: Firewalls block
        the health check probes to the backends and cause the backends to be
        unavailable for traffic from the load balancer. For more details, see
        [Health check firewall rules](https://cloud.google.com/load-
        balancing/docs/health-checks#firewall_rules).
      INSTANCE_NOT_RUNNING: Packet is sent from or to a Compute Engine
        instance that is not in a running state.
      GKE_CLUSTER_NOT_RUNNING: Packet sent from or to a GKE cluster that is
        not in running state.
      CLOUD_SQL_INSTANCE_NOT_RUNNING: Packet sent from or to a Cloud SQL
        instance that is not in running state.
      TRAFFIC_TYPE_BLOCKED: The type of traffic is blocked and the user cannot
        configure a firewall rule to enable it. See [Always blocked
        traffic](https://cloud.google.com/vpc/docs/firewalls#blockedtraffic)
        for more details.
      GKE_MASTER_UNAUTHORIZED_ACCESS: Access to Google Kubernetes Engine
        cluster master's endpoint is not authorized. See [Access to the
        cluster endpoints](https://cloud.google.com/kubernetes-
        engine/docs/how-to/private-clusters#access_to_the_cluster_endpoints)
        for more details.
      CLOUD_SQL_INSTANCE_UNAUTHORIZED_ACCESS: Access to the Cloud SQL instance
        endpoint is not authorized. See [Authorizing with authorized
        networks](https://cloud.google.com/sql/docs/mysql/authorize-networks)
        for more details.
      DROPPED_INSIDE_GKE_SERVICE: Packet was dropped inside Google Kubernetes
        Engine Service.
      DROPPED_INSIDE_CLOUD_SQL_SERVICE: Packet was dropped inside Cloud SQL
        Service.
      GOOGLE_MANAGED_SERVICE_NO_PEERING: Packet was dropped because there is
        no peering between the originating network and the Google Managed
        Services Network.
      GOOGLE_MANAGED_SERVICE_NO_PSC_ENDPOINT: Packet was dropped because the
        Google-managed service uses Private Service Connect (PSC), but the PSC
        endpoint is not found in the project.
      GKE_PSC_ENDPOINT_MISSING: Packet was dropped because the GKE cluster
        uses Private Service Connect (PSC), but the PSC endpoint is not found
        in the project.
      CLOUD_SQL_INSTANCE_NO_IP_ADDRESS: Packet was dropped because the Cloud
        SQL instance has neither a private nor a public IP address.
      GKE_CONTROL_PLANE_REGION_MISMATCH: Packet was dropped because a GKE
        cluster private endpoint is unreachable from a region different from
        the cluster's region.
      PUBLIC_GKE_CONTROL_PLANE_TO_PRIVATE_DESTINATION: Packet sent from a
        public GKE cluster control plane to a private IP address.
      GKE_CONTROL_PLANE_NO_ROUTE: Packet was dropped because there is no route
        from a GKE cluster control plane to a destination network.
      CLOUD_SQL_INSTANCE_NOT_CONFIGURED_FOR_EXTERNAL_TRAFFIC: Packet sent from
        a Cloud SQL instance to an external IP address is not allowed. The
        Cloud SQL instance is not configured to send packets to external IP
        addresses.
      PUBLIC_CLOUD_SQL_INSTANCE_TO_PRIVATE_DESTINATION: Packet sent from a
        Cloud SQL instance with only a public IP address to a private IP
        address.
      CLOUD_SQL_INSTANCE_NO_ROUTE: Packet was dropped because there is no
        route from a Cloud SQL instance to a destination network.
      CLOUD_FUNCTION_NOT_ACTIVE: Packet could be dropped because the Cloud
        Function is not in an active status.
      VPC_CONNECTOR_NOT_SET: Packet could be dropped because no VPC connector
        is set.
      VPC_CONNECTOR_NOT_RUNNING: Packet could be dropped because the VPC
        connector is not in a running state.
      PSC_CONNECTION_NOT_ACCEPTED: The Private Service Connect endpoint is in
        a project that is not approved to connect to the service.
      CLOUD_RUN_REVISION_NOT_READY: Packet sent from a Cloud Run revision that
        is not ready.
      DROPPED_INSIDE_PSC_SERVICE_PRODUCER: Packet was dropped inside Private
        Service Connect service producer.
      LOAD_BALANCER_HAS_NO_PROXY_SUBNET: Packet sent to a load balancer, which
        requires a proxy-only subnet and the subnet is not found.
    """
    CAUSE_UNSPECIFIED = 0
    UNKNOWN_EXTERNAL_ADDRESS = 1
    FOREIGN_IP_DISALLOWED = 2
    FIREWALL_RULE = 3
    NO_ROUTE = 4
    ROUTE_BLACKHOLE = 5
    ROUTE_WRONG_NETWORK = 6
    PRIVATE_TRAFFIC_TO_INTERNET = 7
    PRIVATE_GOOGLE_ACCESS_DISALLOWED = 8
    NO_EXTERNAL_ADDRESS = 9
    UNKNOWN_INTERNAL_ADDRESS = 10
    FORWARDING_RULE_MISMATCH = 11
    FORWARDING_RULE_REGION_MISMATCH = 12
    FORWARDING_RULE_NO_INSTANCES = 13
    FIREWALL_BLOCKING_LOAD_BALANCER_BACKEND_HEALTH_CHECK = 14
    INSTANCE_NOT_RUNNING = 15
    GKE_CLUSTER_NOT_RUNNING = 16
    CLOUD_SQL_INSTANCE_NOT_RUNNING = 17
    TRAFFIC_TYPE_BLOCKED = 18
    GKE_MASTER_UNAUTHORIZED_ACCESS = 19
    CLOUD_SQL_INSTANCE_UNAUTHORIZED_ACCESS = 20
    DROPPED_INSIDE_GKE_SERVICE = 21
    DROPPED_INSIDE_CLOUD_SQL_SERVICE = 22
    GOOGLE_MANAGED_SERVICE_NO_PEERING = 23
    GOOGLE_MANAGED_SERVICE_NO_PSC_ENDPOINT = 24
    GKE_PSC_ENDPOINT_MISSING = 25
    CLOUD_SQL_INSTANCE_NO_IP_ADDRESS = 26
    GKE_CONTROL_PLANE_REGION_MISMATCH = 27
    PUBLIC_GKE_CONTROL_PLANE_TO_PRIVATE_DESTINATION = 28
    GKE_CONTROL_PLANE_NO_ROUTE = 29
    CLOUD_SQL_INSTANCE_NOT_CONFIGURED_FOR_EXTERNAL_TRAFFIC = 30
    PUBLIC_CLOUD_SQL_INSTANCE_TO_PRIVATE_DESTINATION = 31
    CLOUD_SQL_INSTANCE_NO_ROUTE = 32
    CLOUD_FUNCTION_NOT_ACTIVE = 33
    VPC_CONNECTOR_NOT_SET = 34
    VPC_CONNECTOR_NOT_RUNNING = 35
    PSC_CONNECTION_NOT_ACCEPTED = 36
    CLOUD_RUN_REVISION_NOT_READY = 37
    DROPPED_INSIDE_PSC_SERVICE_PRODUCER = 38
    LOAD_BALANCER_HAS_NO_PROXY_SUBNET = 39

  cause = _messages.EnumField('CauseValueValuesEnum', 1)
  resourceUri = _messages.StringField(2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Endpoint(_messages.Message):
  r"""Source or destination of the Connectivity Test.

  Enums:
    NetworkTypeValueValuesEnum: Type of the network where the endpoint is
      located. Applicable only to source endpoint, as destination network type
      can be inferred from the source.

  Fields:
    appEngineVersion: An [App Engine](https://cloud.google.com/appengine)
      [service version](https://cloud.google.com/appengine/docs/admin-
      api/reference/rest/v1/apps.services.versions).
    cloudFunction: A [Cloud Function](https://cloud.google.com/functions).
    cloudRunRevision: A [Cloud Run](https://cloud.google.com/run) [revision](h
      ttps://cloud.google.com/run/docs/reference/rest/v1/namespaces.revisions/
      get)
    cloudSqlInstance: A [Cloud SQL](https://cloud.google.com/sql) instance
      URI.
    forwardingRule: A forwarding rule and its corresponding IP address
      represent the frontend configuration of a Google Cloud load balancer.
      Forwarding rules are also used for protocol forwarding, Private Service
      Connect and other network services to provide forwarding information in
      the control plane. Format:
      projects/{project}/global/forwardingRules/{id} or
      projects/{project}/regions/{region}/forwardingRules/{id}
    gkeMasterCluster: A cluster URI for [Google Kubernetes Engine
      master](https://cloud.google.com/kubernetes-
      engine/docs/concepts/cluster-architecture).
    instance: A Compute Engine instance URI.
    ipAddress: The IP address of the endpoint, which can be an external or
      internal IP. An IPv6 address is only allowed when the test's destination
      is a [global load balancer VIP](/load-balancing/docs/load-balancing-
      overview).
    network: A Compute Engine network URI.
    networkType: Type of the network where the endpoint is located. Applicable
      only to source endpoint, as destination network type can be inferred
      from the source.
    port: The IP protocol port of the endpoint. Only applicable when protocol
      is TCP or UDP.
    projectId: Project ID where the endpoint is located. The Project ID can be
      derived from the URI if you provide a VM instance or network URI. The
      following are two cases where you must provide the project ID: 1. Only
      the IP address is specified, and the IP address is within a Google Cloud
      project. 2. When you are using Shared VPC and the IP address that you
      provide is from the service project. In this case, the network that the
      IP address resides in is defined in the host project.
  """

  class NetworkTypeValueValuesEnum(_messages.Enum):
    r"""Type of the network where the endpoint is located. Applicable only to
    source endpoint, as destination network type can be inferred from the
    source.

    Values:
      NETWORK_TYPE_UNSPECIFIED: Default type if unspecified.
      GCP_NETWORK: A network hosted within Google Cloud. To receive more
        detailed output, specify the URI for the source or destination
        network.
      NON_GCP_NETWORK: A network hosted outside of Google Cloud. This can be
        an on-premises network, or a network hosted by another cloud provider.
    """
    NETWORK_TYPE_UNSPECIFIED = 0
    GCP_NETWORK = 1
    NON_GCP_NETWORK = 2

  appEngineVersion = _messages.MessageField('AppEngineVersionEndpoint', 1)
  cloudFunction = _messages.MessageField('CloudFunctionEndpoint', 2)
  cloudRunRevision = _messages.MessageField('CloudRunRevisionEndpoint', 3)
  cloudSqlInstance = _messages.StringField(4)
  forwardingRule = _messages.StringField(5)
  gkeMasterCluster = _messages.StringField(6)
  instance = _messages.StringField(7)
  ipAddress = _messages.StringField(8)
  network = _messages.StringField(9)
  networkType = _messages.EnumField('NetworkTypeValueValuesEnum', 10)
  port = _messages.IntegerField(11, variant=_messages.Variant.INT32)
  projectId = _messages.StringField(12)


class EndpointInfo(_messages.Message):
  r"""For display only. The specification of the endpoints for the test.
  EndpointInfo is derived from source and destination Endpoint and validated
  by the backend data plane model.

  Fields:
    destinationIp: Destination IP address.
    destinationNetworkUri: URI of the network where this packet is sent to.
    destinationPort: Destination port. Only valid when protocol is TCP or UDP.
    protocol: IP protocol in string format, for example: "TCP", "UDP", "ICMP".
    sourceIp: Source IP address.
    sourceNetworkUri: URI of the network where this packet originates from.
    sourcePort: Source port. Only valid when protocol is TCP or UDP.
  """

  destinationIp = _messages.StringField(1)
  destinationNetworkUri = _messages.StringField(2)
  destinationPort = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  protocol = _messages.StringField(4)
  sourceIp = _messages.StringField(5)
  sourceNetworkUri = _messages.StringField(6)
  sourcePort = _messages.IntegerField(7, variant=_messages.Variant.INT32)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class FirewallInfo(_messages.Message):
  r"""For display only. Metadata associated with a VPC firewall rule, an
  implied VPC firewall rule, or a hierarchical firewall policy rule.

  Enums:
    FirewallRuleTypeValueValuesEnum: The firewall rule's type.

  Fields:
    action: Possible values: ALLOW, DENY
    direction: Possible values: INGRESS, EGRESS
    displayName: The display name of the VPC firewall rule. This field is not
      applicable to hierarchical firewall policy rules.
    firewallRuleType: The firewall rule's type.
    networkUri: The URI of the VPC network that the firewall rule is
      associated with. This field is not applicable to hierarchical firewall
      policy rules.
    policy: The hierarchical firewall policy that this rule is associated
      with. This field is not applicable to VPC firewall rules.
    priority: The priority of the firewall rule.
    targetServiceAccounts: The target service accounts specified by the
      firewall rule.
    targetTags: The target tags defined by the VPC firewall rule. This field
      is not applicable to hierarchical firewall policy rules.
    uri: The URI of the VPC firewall rule. This field is not applicable to
      implied firewall rules or hierarchical firewall policy rules.
  """

  class FirewallRuleTypeValueValuesEnum(_messages.Enum):
    r"""The firewall rule's type.

    Values:
      FIREWALL_RULE_TYPE_UNSPECIFIED: Unspecified type.
      HIERARCHICAL_FIREWALL_POLICY_RULE: Hierarchical firewall policy rule.
        For details, see [Hierarchical firewall policies
        overview](https://cloud.google.com/vpc/docs/firewall-policies).
      VPC_FIREWALL_RULE: VPC firewall rule. For details, see [VPC firewall
        rules overview](https://cloud.google.com/vpc/docs/firewalls).
      IMPLIED_VPC_FIREWALL_RULE: Implied VPC firewall rule. For details, see
        [Implied rules](https://cloud.google.com/vpc/docs/firewalls#default_fi
        rewall_rules).
      SERVERLESS_VPC_ACCESS_MANAGED_FIREWALL_RULE: Implicit firewall rules
        that are managed by serverless VPC access to allow ingress access.
        They are not visible in the Google Cloud console. For details, see
        [VPC connector's implicit
        rules](https://cloud.google.com/functions/docs/networking/connecting-
        vpc#restrict-access).
      NETWORK_FIREWALL_POLICY_RULE: Global network firewall policy rule. For
        details, see [Network firewall
        policies](https://cloud.google.com/vpc/docs/network-firewall-
        policies).
    """
    FIREWALL_RULE_TYPE_UNSPECIFIED = 0
    HIERARCHICAL_FIREWALL_POLICY_RULE = 1
    VPC_FIREWALL_RULE = 2
    IMPLIED_VPC_FIREWALL_RULE = 3
    SERVERLESS_VPC_ACCESS_MANAGED_FIREWALL_RULE = 4
    NETWORK_FIREWALL_POLICY_RULE = 5

  action = _messages.StringField(1)
  direction = _messages.StringField(2)
  displayName = _messages.StringField(3)
  firewallRuleType = _messages.EnumField('FirewallRuleTypeValueValuesEnum', 4)
  networkUri = _messages.StringField(5)
  policy = _messages.StringField(6)
  priority = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  targetServiceAccounts = _messages.StringField(8, repeated=True)
  targetTags = _messages.StringField(9, repeated=True)
  uri = _messages.StringField(10)


class ForwardInfo(_messages.Message):
  r"""Details of the final state "forward" and associated resource.

  Enums:
    TargetValueValuesEnum: Target type where this packet is forwarded to.

  Fields:
    resourceUri: URI of the resource that the packet is forwarded to.
    target: Target type where this packet is forwarded to.
  """

  class TargetValueValuesEnum(_messages.Enum):
    r"""Target type where this packet is forwarded to.

    Values:
      TARGET_UNSPECIFIED: Target not specified.
      PEERING_VPC: Forwarded to a VPC peering network.
      VPN_GATEWAY: Forwarded to a Cloud VPN gateway.
      INTERCONNECT: Forwarded to a Cloud Interconnect connection.
      GKE_MASTER: Forwarded to a Google Kubernetes Engine Container cluster
        master.
      IMPORTED_CUSTOM_ROUTE_NEXT_HOP: Forwarded to the next hop of a custom
        route imported from a peering VPC.
      CLOUD_SQL_INSTANCE: Forwarded to a Cloud SQL instance.
      ANOTHER_PROJECT: Forwarded to a VPC network in another project.
      NCC_HUB: Forwarded to an NCC Hub.
    """
    TARGET_UNSPECIFIED = 0
    PEERING_VPC = 1
    VPN_GATEWAY = 2
    INTERCONNECT = 3
    GKE_MASTER = 4
    IMPORTED_CUSTOM_ROUTE_NEXT_HOP = 5
    CLOUD_SQL_INSTANCE = 6
    ANOTHER_PROJECT = 7
    NCC_HUB = 8

  resourceUri = _messages.StringField(1)
  target = _messages.EnumField('TargetValueValuesEnum', 2)


class ForwardingRuleInfo(_messages.Message):
  r"""For display only. Metadata associated with a Compute Engine forwarding
  rule.

  Fields:
    displayName: Name of a Compute Engine forwarding rule.
    matchedPortRange: Port range defined in the forwarding rule that matches
      the test.
    matchedProtocol: Protocol defined in the forwarding rule that matches the
      test.
    networkUri: Network URI. Only valid for Internal Load Balancer.
    target: Target type of the forwarding rule.
    uri: URI of a Compute Engine forwarding rule.
    vip: VIP of the forwarding rule.
  """

  displayName = _messages.StringField(1)
  matchedPortRange = _messages.StringField(2)
  matchedProtocol = _messages.StringField(3)
  networkUri = _messages.StringField(4)
  target = _messages.StringField(5)
  uri = _messages.StringField(6)
  vip = _messages.StringField(7)


class GKEMasterInfo(_messages.Message):
  r"""For display only. Metadata associated with a Google Kubernetes Engine
  (GKE) cluster master.

  Fields:
    clusterNetworkUri: URI of a GKE cluster network.
    clusterUri: URI of a GKE cluster.
    externalIp: External IP address of a GKE cluster master.
    internalIp: Internal IP address of a GKE cluster master.
  """

  clusterNetworkUri = _messages.StringField(1)
  clusterUri = _messages.StringField(2)
  externalIp = _messages.StringField(3)
  internalIp = _messages.StringField(4)


class GoogleServiceInfo(_messages.Message):
  r"""For display only. Details of a Google Service sending packets to a VPC
  network. Although the source IP might be a publicly routable address, some
  Google Services use special routes within Google production infrastructure
  to reach Compute Engine Instances.
  https://cloud.google.com/vpc/docs/routes#special_return_paths

  Enums:
    GoogleServiceTypeValueValuesEnum: Recognized type of a Google Service.

  Fields:
    googleServiceType: Recognized type of a Google Service.
    sourceIp: Source IP address.
  """

  class GoogleServiceTypeValueValuesEnum(_messages.Enum):
    r"""Recognized type of a Google Service.

    Values:
      GOOGLE_SERVICE_TYPE_UNSPECIFIED: Unspecified Google Service. Includes
        most of Google APIs and services.
      IAP: Identity aware proxy. https://cloud.google.com/iap/docs/using-tcp-
        forwarding
      GFE_PROXY_OR_HEALTH_CHECK_PROBER: One of two services sharing IP ranges:
        * Load Balancer proxy * Centralized Health Check prober
        https://cloud.google.com/load-balancing/docs/firewall-rules
      CLOUD_DNS: Connectivity from Cloud DNS to forwarding targets or
        alternate name servers that use private routing.
        https://cloud.google.com/dns/docs/zones/forwarding-zones#firewall-
        rules https://cloud.google.com/dns/docs/policies#firewall-rules
    """
    GOOGLE_SERVICE_TYPE_UNSPECIFIED = 0
    IAP = 1
    GFE_PROXY_OR_HEALTH_CHECK_PROBER = 2
    CLOUD_DNS = 3

  googleServiceType = _messages.EnumField('GoogleServiceTypeValueValuesEnum', 1)
  sourceIp = _messages.StringField(2)


class InstanceInfo(_messages.Message):
  r"""For display only. Metadata associated with a Compute Engine instance.

  Fields:
    displayName: Name of a Compute Engine instance.
    externalIp: External IP address of the network interface.
    interface: Name of the network interface of a Compute Engine instance.
    internalIp: Internal IP address of the network interface.
    networkTags: Network tags configured on the instance.
    networkUri: URI of a Compute Engine network.
    serviceAccount: Service account authorized for the instance.
    uri: URI of a Compute Engine instance.
  """

  displayName = _messages.StringField(1)
  externalIp = _messages.StringField(2)
  interface = _messages.StringField(3)
  internalIp = _messages.StringField(4)
  networkTags = _messages.StringField(5, repeated=True)
  networkUri = _messages.StringField(6)
  serviceAccount = _messages.StringField(7)
  uri = _messages.StringField(8)


class ListConnectivityTestsResponse(_messages.Message):
  r"""Response for the `ListConnectivityTests` method.

  Fields:
    nextPageToken: Page token to fetch the next set of Connectivity Tests.
    resources: List of Connectivity Tests.
    unreachable: Locations that could not be reached (when querying all
      locations with `-`).
  """

  nextPageToken = _messages.StringField(1)
  resources = _messages.MessageField('ConnectivityTest', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class LoadBalancerBackend(_messages.Message):
  r"""For display only. Metadata associated with a specific load balancer
  backend.

  Enums:
    HealthCheckFirewallStateValueValuesEnum: State of the health check
      firewall configuration.

  Fields:
    displayName: Name of a Compute Engine instance or network endpoint.
    healthCheckAllowingFirewallRules: A list of firewall rule URIs allowing
      probes from health check IP ranges.
    healthCheckBlockingFirewallRules: A list of firewall rule URIs blocking
      probes from health check IP ranges.
    healthCheckFirewallState: State of the health check firewall
      configuration.
    uri: URI of a Compute Engine instance or network endpoint.
  """

  class HealthCheckFirewallStateValueValuesEnum(_messages.Enum):
    r"""State of the health check firewall configuration.

    Values:
      HEALTH_CHECK_FIREWALL_STATE_UNSPECIFIED: State is unspecified. Default
        state if not populated.
      CONFIGURED: There are configured firewall rules to allow health check
        probes to the backend.
      MISCONFIGURED: There are firewall rules configured to allow partial
        health check ranges or block all health check ranges. If a health
        check probe is sent from denied IP ranges, the health check to the
        backend will fail. Then, the backend will be marked unhealthy and will
        not receive traffic sent to the load balancer.
    """
    HEALTH_CHECK_FIREWALL_STATE_UNSPECIFIED = 0
    CONFIGURED = 1
    MISCONFIGURED = 2

  displayName = _messages.StringField(1)
  healthCheckAllowingFirewallRules = _messages.StringField(2, repeated=True)
  healthCheckBlockingFirewallRules = _messages.StringField(3, repeated=True)
  healthCheckFirewallState = _messages.EnumField('HealthCheckFirewallStateValueValuesEnum', 4)
  uri = _messages.StringField(5)


class LoadBalancerInfo(_messages.Message):
  r"""For display only. Metadata associated with a load balancer.

  Enums:
    BackendTypeValueValuesEnum: Type of load balancer's backend configuration.
    LoadBalancerTypeValueValuesEnum: Type of the load balancer.

  Fields:
    backendType: Type of load balancer's backend configuration.
    backendUri: Backend configuration URI.
    backends: Information for the loadbalancer backends.
    healthCheckUri: URI of the health check for the load balancer.
    loadBalancerType: Type of the load balancer.
  """

  class BackendTypeValueValuesEnum(_messages.Enum):
    r"""Type of load balancer's backend configuration.

    Values:
      BACKEND_TYPE_UNSPECIFIED: Type is unspecified.
      BACKEND_SERVICE: Backend Service as the load balancer's backend.
      TARGET_POOL: Target Pool as the load balancer's backend.
      TARGET_INSTANCE: Target Instance as the load balancer's backend.
    """
    BACKEND_TYPE_UNSPECIFIED = 0
    BACKEND_SERVICE = 1
    TARGET_POOL = 2
    TARGET_INSTANCE = 3

  class LoadBalancerTypeValueValuesEnum(_messages.Enum):
    r"""Type of the load balancer.

    Values:
      LOAD_BALANCER_TYPE_UNSPECIFIED: Type is unspecified.
      INTERNAL_TCP_UDP: Internal TCP/UDP load balancer.
      NETWORK_TCP_UDP: Network TCP/UDP load balancer.
      HTTP_PROXY: HTTP(S) proxy load balancer.
      TCP_PROXY: TCP proxy load balancer.
      SSL_PROXY: SSL proxy load balancer.
    """
    LOAD_BALANCER_TYPE_UNSPECIFIED = 0
    INTERNAL_TCP_UDP = 1
    NETWORK_TCP_UDP = 2
    HTTP_PROXY = 3
    TCP_PROXY = 4
    SSL_PROXY = 5

  backendType = _messages.EnumField('BackendTypeValueValuesEnum', 1)
  backendUri = _messages.StringField(2)
  backends = _messages.MessageField('LoadBalancerBackend', 3, repeated=True)
  healthCheckUri = _messages.StringField(4)
  loadBalancerType = _messages.EnumField('LoadBalancerTypeValueValuesEnum', 5)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class NetworkInfo(_messages.Message):
  r"""For display only. Metadata associated with a Compute Engine network.

  Fields:
    displayName: Name of a Compute Engine network.
    matchedIpRange: The IP range that matches the test.
    uri: URI of a Compute Engine network.
  """

  displayName = _messages.StringField(1)
  matchedIpRange = _messages.StringField(2)
  uri = _messages.StringField(3)


class NetworkmanagementProjectsLocationsGetRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsCreateRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalConnectivityTestsCreateRequest
  object.

  Fields:
    connectivityTest: A ConnectivityTest resource to be passed as the request
      body.
    parent: Required. The parent resource of the Connectivity Test to create:
      `projects/{project_id}/locations/global`
    testId: Required. The logical name of the Connectivity Test in your
      project with the following restrictions: * Must contain only lowercase
      letters, numbers, and hyphens. * Must start with a letter. * Must be
      between 1-40 characters. * Must end with a number or a letter. * Must be
      unique within the customer project
  """

  connectivityTest = _messages.MessageField('ConnectivityTest', 1)
  parent = _messages.StringField(2, required=True)
  testId = _messages.StringField(3)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsDeleteRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalConnectivityTestsDeleteRequest
  object.

  Fields:
    name: Required. Connectivity Test resource name using the form:
      `projects/{project_id}/locations/global/connectivityTests/{test_id}`
  """

  name = _messages.StringField(1, required=True)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsGetIamPolicyRequest(_messages.Message):
  r"""A
  NetworkmanagementProjectsLocationsGlobalConnectivityTestsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsGetRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalConnectivityTestsGetRequest
  object.

  Fields:
    name: Required. `ConnectivityTest` resource name using the form:
      `projects/{project_id}/locations/global/connectivityTests/{test_id}`
  """

  name = _messages.StringField(1, required=True)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsListRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalConnectivityTestsListRequest
  object.

  Fields:
    filter: Lists the `ConnectivityTests` that match the filter expression. A
      filter expression filters the resources listed in the response. The
      expression must be of the form ` ` where operators: `<`, `>`, `<=`,
      `>=`, `!=`, `=`, `:` are supported (colon `:` represents a HAS operator
      which is roughly synonymous with equality). can refer to a proto or JSON
      field, or a synthetic field. Field names can be camelCase or snake_case.
      Examples: - Filter by name: name =
      "projects/proj-1/locations/global/connectivityTests/test-1 - Filter by
      labels: - Resources that have a key called `foo` labels.foo:* -
      Resources that have a key called `foo` whose value is `bar` labels.foo =
      bar
    orderBy: Field to use to sort the list.
    pageSize: Number of `ConnectivityTests` to return.
    pageToken: Page token from an earlier query, as returned in
      `next_page_token`.
    parent: Required. The parent resource of the Connectivity Tests:
      `projects/{project_id}/locations/global`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsPatchRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalConnectivityTestsPatchRequest
  object.

  Fields:
    connectivityTest: A ConnectivityTest resource to be passed as the request
      body.
    name: Required. Unique name of the resource using the form:
      `projects/{project_id}/locations/global/connectivityTests/{test_id}`
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field.
  """

  connectivityTest = _messages.MessageField('ConnectivityTest', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsRerunRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalConnectivityTestsRerunRequest
  object.

  Fields:
    name: Required. Connectivity Test resource name using the form:
      `projects/{project_id}/locations/global/connectivityTests/{test_id}`
    rerunConnectivityTestRequest: A RerunConnectivityTestRequest resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  rerunConnectivityTestRequest = _messages.MessageField('RerunConnectivityTestRequest', 2)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsSetIamPolicyRequest(_messages.Message):
  r"""A
  NetworkmanagementProjectsLocationsGlobalConnectivityTestsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class NetworkmanagementProjectsLocationsGlobalConnectivityTestsTestIamPermissionsRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalConnectivityTestsTestIamPermis
  sionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class NetworkmanagementProjectsLocationsGlobalOperationsCancelRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalOperationsCancelRequest
  object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class NetworkmanagementProjectsLocationsGlobalOperationsDeleteRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalOperationsDeleteRequest
  object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class NetworkmanagementProjectsLocationsGlobalOperationsGetRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class NetworkmanagementProjectsLocationsGlobalOperationsListRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsGlobalOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class NetworkmanagementProjectsLocationsListRequest(_messages.Message):
  r"""A NetworkmanagementProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Metadata describing an Operation

  Fields:
    apiVersion: API version.
    cancelRequested: Specifies if cancellation was requested for the
      operation.
    createTime: The time the operation was created.
    endTime: The time the operation finished running.
    statusDetail: Human-readable status of the operation, if any.
    target: Target of the operation - for example
      projects/project-1/locations/global/connectivityTests/test-1
    verb: Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class ReachabilityDetails(_messages.Message):
  r"""Results of the configuration analysis from the last run of the test.

  Enums:
    ResultValueValuesEnum: The overall result of the test's configuration
      analysis.

  Fields:
    error: The details of a failure or a cancellation of reachability
      analysis.
    result: The overall result of the test's configuration analysis.
    traces: Result may contain a list of traces if a test has multiple
      possible paths in the network, such as when destination endpoint is a
      load balancer with multiple backends.
    verifyTime: The time of the configuration analysis.
  """

  class ResultValueValuesEnum(_messages.Enum):
    r"""The overall result of the test's configuration analysis.

    Values:
      RESULT_UNSPECIFIED: No result was specified.
      REACHABLE: Possible scenarios are: * The configuration analysis
        determined that a packet originating from the source is expected to
        reach the destination. * The analysis didn't complete because the user
        lacks permission for some of the resources in the trace. However, at
        the time the user's permission became insufficient, the trace had been
        successful so far.
      UNREACHABLE: A packet originating from the source is expected to be
        dropped before reaching the destination.
      AMBIGUOUS: The source and destination endpoints do not uniquely identify
        the test location in the network, and the reachability result contains
        multiple traces. For some traces, a packet could be delivered, and for
        others, it would not be.
      UNDETERMINED: The configuration analysis did not complete. Possible
        reasons are: * A permissions error occurred--for example, the user
        might not have read permission for all of the resources named in the
        test. * An internal error occurred. * The analyzer received an invalid
        or unsupported argument or was unable to identify a known endpoint.
    """
    RESULT_UNSPECIFIED = 0
    REACHABLE = 1
    UNREACHABLE = 2
    AMBIGUOUS = 3
    UNDETERMINED = 4

  error = _messages.MessageField('Status', 1)
  result = _messages.EnumField('ResultValueValuesEnum', 2)
  traces = _messages.MessageField('Trace', 3, repeated=True)
  verifyTime = _messages.StringField(4)


class RerunConnectivityTestRequest(_messages.Message):
  r"""Request for the `RerunConnectivityTest` method."""


class RouteInfo(_messages.Message):
  r"""For display only. Metadata associated with a Compute Engine route.

  Enums:
    NextHopTypeValueValuesEnum: Type of next hop.
    RouteScopeValueValuesEnum: Indicates where route is applicable.
    RouteTypeValueValuesEnum: Type of route.

  Fields:
    destIpRange: Destination IP range of the route.
    destPortRanges: Destination port ranges of the route. Policy based routes
      only.
    displayName: Name of a route.
    instanceTags: Instance tags of the route.
    nccHubUri: URI of a NCC Hub. NCC_HUB routes only.
    nccSpokeUri: URI of a NCC Spoke. NCC_HUB routes only.
    networkUri: URI of a Compute Engine network. NETWORK routes only.
    nextHop: Next hop of the route.
    nextHopType: Type of next hop.
    priority: Priority of the route.
    protocols: Protocols of the route. Policy based routes only.
    routeScope: Indicates where route is applicable.
    routeType: Type of route.
    srcIpRange: Source IP address range of the route. Policy based routes
      only.
    srcPortRanges: Source port ranges of the route. Policy based routes only.
    uri: URI of a route. Dynamic, peering static and peering dynamic routes do
      not have an URI. Advertised route from Google Cloud VPC to on-premises
      network also does not have an URI.
  """

  class NextHopTypeValueValuesEnum(_messages.Enum):
    r"""Type of next hop.

    Values:
      NEXT_HOP_TYPE_UNSPECIFIED: Unspecified type. Default value.
      NEXT_HOP_IP: Next hop is an IP address.
      NEXT_HOP_INSTANCE: Next hop is a Compute Engine instance.
      NEXT_HOP_NETWORK: Next hop is a VPC network gateway.
      NEXT_HOP_PEERING: Next hop is a peering VPC.
      NEXT_HOP_INTERCONNECT: Next hop is an interconnect.
      NEXT_HOP_VPN_TUNNEL: Next hop is a VPN tunnel.
      NEXT_HOP_VPN_GATEWAY: Next hop is a VPN gateway. This scenario only
        happens when tracing connectivity from an on-premises network to
        Google Cloud through a VPN. The analysis simulates a packet departing
        from the on-premises network through a VPN tunnel and arriving at a
        Cloud VPN gateway.
      NEXT_HOP_INTERNET_GATEWAY: Next hop is an internet gateway.
      NEXT_HOP_BLACKHOLE: Next hop is blackhole; that is, the next hop either
        does not exist or is not running.
      NEXT_HOP_ILB: Next hop is the forwarding rule of an Internal Load
        Balancer.
      NEXT_HOP_ROUTER_APPLIANCE: Next hop is a [router appliance
        instance](https://cloud.google.com/network-connectivity/docs/network-
        connectivity-center/concepts/ra-overview).
      NEXT_HOP_NCC_HUB: Next hop is an NCC hub.
    """
    NEXT_HOP_TYPE_UNSPECIFIED = 0
    NEXT_HOP_IP = 1
    NEXT_HOP_INSTANCE = 2
    NEXT_HOP_NETWORK = 3
    NEXT_HOP_PEERING = 4
    NEXT_HOP_INTERCONNECT = 5
    NEXT_HOP_VPN_TUNNEL = 6
    NEXT_HOP_VPN_GATEWAY = 7
    NEXT_HOP_INTERNET_GATEWAY = 8
    NEXT_HOP_BLACKHOLE = 9
    NEXT_HOP_ILB = 10
    NEXT_HOP_ROUTER_APPLIANCE = 11
    NEXT_HOP_NCC_HUB = 12

  class RouteScopeValueValuesEnum(_messages.Enum):
    r"""Indicates where route is applicable.

    Values:
      ROUTE_SCOPE_UNSPECIFIED: Unspecified scope. Default value.
      NETWORK: Route is applicable to packets in Network.
      NCC_HUB: Route is applicable to packets using NCC Hub's routing table.
    """
    ROUTE_SCOPE_UNSPECIFIED = 0
    NETWORK = 1
    NCC_HUB = 2

  class RouteTypeValueValuesEnum(_messages.Enum):
    r"""Type of route.

    Values:
      ROUTE_TYPE_UNSPECIFIED: Unspecified type. Default value.
      SUBNET: Route is a subnet route automatically created by the system.
      STATIC: Static route created by the user, including the default route to
        the internet.
      DYNAMIC: Dynamic route exchanged between BGP peers.
      PEERING_SUBNET: A subnet route received from peering network.
      PEERING_STATIC: A static route received from peering network.
      PEERING_DYNAMIC: A dynamic route received from peering network.
      POLICY_BASED: Policy based route.
    """
    ROUTE_TYPE_UNSPECIFIED = 0
    SUBNET = 1
    STATIC = 2
    DYNAMIC = 3
    PEERING_SUBNET = 4
    PEERING_STATIC = 5
    PEERING_DYNAMIC = 6
    POLICY_BASED = 7

  destIpRange = _messages.StringField(1)
  destPortRanges = _messages.StringField(2, repeated=True)
  displayName = _messages.StringField(3)
  instanceTags = _messages.StringField(4, repeated=True)
  nccHubUri = _messages.StringField(5)
  nccSpokeUri = _messages.StringField(6)
  networkUri = _messages.StringField(7)
  nextHop = _messages.StringField(8)
  nextHopType = _messages.EnumField('NextHopTypeValueValuesEnum', 9)
  priority = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  protocols = _messages.StringField(11, repeated=True)
  routeScope = _messages.EnumField('RouteScopeValueValuesEnum', 12)
  routeType = _messages.EnumField('RouteTypeValueValuesEnum', 13)
  srcIpRange = _messages.StringField(14)
  srcPortRanges = _messages.StringField(15, repeated=True)
  uri = _messages.StringField(16)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Step(_messages.Message):
  r"""A simulated forwarding path is composed of multiple steps. Each step has
  a well-defined state and an associated configuration.

  Enums:
    StateValueValuesEnum: Each step is in one of the pre-defined states.

  Fields:
    abort: Display information of the final state "abort" and reason.
    appEngineVersion: Display information of an App Engine service version.
    causesDrop: This is a step that leads to the final state Drop.
    cloudFunction: Display information of a Cloud Function.
    cloudRunRevision: Display information of a Cloud Run revision.
    cloudSqlInstance: Display information of a Cloud SQL instance.
    deliver: Display information of the final state "deliver" and reason.
    description: A description of the step. Usually this is a summary of the
      state.
    drop: Display information of the final state "drop" and reason.
    endpoint: Display information of the source and destination under
      analysis. The endpoint information in an intermediate state may differ
      with the initial input, as it might be modified by state like NAT, or
      Connection Proxy.
    firewall: Display information of a Compute Engine firewall rule.
    forward: Display information of the final state "forward" and reason.
    forwardingRule: Display information of a Compute Engine forwarding rule.
    gkeMaster: Display information of a Google Kubernetes Engine cluster
      master.
    googleService: Display information of a Google service
    instance: Display information of a Compute Engine instance.
    loadBalancer: Display information of the load balancers.
    network: Display information of a Google Cloud network.
    projectId: Project ID that contains the configuration this step is
      validating.
    route: Display information of a Compute Engine route.
    state: Each step is in one of the pre-defined states.
    vpcConnector: Display information of a VPC connector.
    vpnGateway: Display information of a Compute Engine VPN gateway.
    vpnTunnel: Display information of a Compute Engine VPN tunnel.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Each step is in one of the pre-defined states.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      START_FROM_INSTANCE: Initial state: packet originating from a Compute
        Engine instance. An InstanceInfo is populated with starting instance
        information.
      START_FROM_INTERNET: Initial state: packet originating from the
        internet. The endpoint information is populated.
      START_FROM_GOOGLE_SERVICE: Initial state: packet originating from a
        Google service. Some Google services, such as health check probers or
        Identity Aware Proxy use special routes, outside VPC routing
        configuration to reach Compute Engine Instances.
      START_FROM_PRIVATE_NETWORK: Initial state: packet originating from a VPC
        or on-premises network with internal source IP. If the source is a VPC
        network visible to the user, a NetworkInfo is populated with details
        of the network.
      START_FROM_GKE_MASTER: Initial state: packet originating from a Google
        Kubernetes Engine cluster master. A GKEMasterInfo is populated with
        starting instance information.
      START_FROM_CLOUD_SQL_INSTANCE: Initial state: packet originating from a
        Cloud SQL instance. A CloudSQLInstanceInfo is populated with starting
        instance information.
      START_FROM_CLOUD_FUNCTION: Initial state: packet originating from a
        Cloud Function. A CloudFunctionInfo is populated with starting
        function information.
      START_FROM_APP_ENGINE_VERSION: Initial state: packet originating from an
        App Engine service version. An AppEngineVersionInfo is populated with
        starting version information.
      START_FROM_CLOUD_RUN_REVISION: Initial state: packet originating from a
        Cloud Run revision. A CloudRunRevisionInfo is populated with starting
        revision information.
      APPLY_INGRESS_FIREWALL_RULE: Config checking state: verify ingress
        firewall rule.
      APPLY_EGRESS_FIREWALL_RULE: Config checking state: verify egress
        firewall rule.
      APPLY_ROUTE: Config checking state: verify route.
      APPLY_FORWARDING_RULE: Config checking state: match forwarding rule.
      SPOOFING_APPROVED: Config checking state: packet sent or received under
        foreign IP address and allowed.
      ARRIVE_AT_INSTANCE: Forwarding state: arriving at a Compute Engine
        instance.
      ARRIVE_AT_INTERNAL_LOAD_BALANCER: Forwarding state: arriving at a
        Compute Engine internal load balancer.
      ARRIVE_AT_EXTERNAL_LOAD_BALANCER: Forwarding state: arriving at a
        Compute Engine external load balancer.
      ARRIVE_AT_VPN_GATEWAY: Forwarding state: arriving at a Cloud VPN
        gateway.
      ARRIVE_AT_VPN_TUNNEL: Forwarding state: arriving at a Cloud VPN tunnel.
      ARRIVE_AT_VPC_CONNECTOR: Forwarding state: arriving at a VPC connector.
      NAT: Transition state: packet header translated.
      PROXY_CONNECTION: Transition state: original connection is terminated
        and a new proxied connection is initiated.
      DELIVER: Final state: packet could be delivered.
      DROP: Final state: packet could be dropped.
      FORWARD: Final state: packet could be forwarded to a network with an
        unknown configuration.
      ABORT: Final state: analysis is aborted.
      VIEWER_PERMISSION_MISSING: Special state: viewer of the test result does
        not have permission to see the configuration in this step.
    """
    STATE_UNSPECIFIED = 0
    START_FROM_INSTANCE = 1
    START_FROM_INTERNET = 2
    START_FROM_GOOGLE_SERVICE = 3
    START_FROM_PRIVATE_NETWORK = 4
    START_FROM_GKE_MASTER = 5
    START_FROM_CLOUD_SQL_INSTANCE = 6
    START_FROM_CLOUD_FUNCTION = 7
    START_FROM_APP_ENGINE_VERSION = 8
    START_FROM_CLOUD_RUN_REVISION = 9
    APPLY_INGRESS_FIREWALL_RULE = 10
    APPLY_EGRESS_FIREWALL_RULE = 11
    APPLY_ROUTE = 12
    APPLY_FORWARDING_RULE = 13
    SPOOFING_APPROVED = 14
    ARRIVE_AT_INSTANCE = 15
    ARRIVE_AT_INTERNAL_LOAD_BALANCER = 16
    ARRIVE_AT_EXTERNAL_LOAD_BALANCER = 17
    ARRIVE_AT_VPN_GATEWAY = 18
    ARRIVE_AT_VPN_TUNNEL = 19
    ARRIVE_AT_VPC_CONNECTOR = 20
    NAT = 21
    PROXY_CONNECTION = 22
    DELIVER = 23
    DROP = 24
    FORWARD = 25
    ABORT = 26
    VIEWER_PERMISSION_MISSING = 27

  abort = _messages.MessageField('AbortInfo', 1)
  appEngineVersion = _messages.MessageField('AppEngineVersionInfo', 2)
  causesDrop = _messages.BooleanField(3)
  cloudFunction = _messages.MessageField('CloudFunctionInfo', 4)
  cloudRunRevision = _messages.MessageField('CloudRunRevisionInfo', 5)
  cloudSqlInstance = _messages.MessageField('CloudSQLInstanceInfo', 6)
  deliver = _messages.MessageField('DeliverInfo', 7)
  description = _messages.StringField(8)
  drop = _messages.MessageField('DropInfo', 9)
  endpoint = _messages.MessageField('EndpointInfo', 10)
  firewall = _messages.MessageField('FirewallInfo', 11)
  forward = _messages.MessageField('ForwardInfo', 12)
  forwardingRule = _messages.MessageField('ForwardingRuleInfo', 13)
  gkeMaster = _messages.MessageField('GKEMasterInfo', 14)
  googleService = _messages.MessageField('GoogleServiceInfo', 15)
  instance = _messages.MessageField('InstanceInfo', 16)
  loadBalancer = _messages.MessageField('LoadBalancerInfo', 17)
  network = _messages.MessageField('NetworkInfo', 18)
  projectId = _messages.StringField(19)
  route = _messages.MessageField('RouteInfo', 20)
  state = _messages.EnumField('StateValueValuesEnum', 21)
  vpcConnector = _messages.MessageField('VpcConnectorInfo', 22)
  vpnGateway = _messages.MessageField('VpnGatewayInfo', 23)
  vpnTunnel = _messages.MessageField('VpnTunnelInfo', 24)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class Trace(_messages.Message):
  r"""Trace represents one simulated packet forwarding path. * Each trace
  contains multiple ordered steps. * Each step is in a particular state with
  associated configuration. * State is categorized as final or non-final
  states. * Each final state has a reason associated. * Each trace must end
  with a final state (the last step). ```
  |---------------------Trace----------------------| Step1(State) Step2(State)
  --- StepN(State(final)) ```

  Fields:
    endpointInfo: Derived from the source and destination endpoints definition
      specified by user request, and validated by the data plane model. If
      there are multiple traces starting from different source locations, then
      the endpoint_info may be different between traces.
    steps: A trace of a test contains multiple steps from the initial state to
      the final state (delivered, dropped, forwarded, or aborted). The steps
      are ordered by the processing sequence within the simulated network
      state machine. It is critical to preserve the order of the steps and
      avoid reordering or sorting them.
  """

  endpointInfo = _messages.MessageField('EndpointInfo', 1)
  steps = _messages.MessageField('Step', 2, repeated=True)


class VpcConnectorInfo(_messages.Message):
  r"""For display only. Metadata associated with a VPC connector.

  Fields:
    displayName: Name of a VPC connector.
    location: Location in which the VPC connector is deployed.
    uri: URI of a VPC connector.
  """

  displayName = _messages.StringField(1)
  location = _messages.StringField(2)
  uri = _messages.StringField(3)


class VpnGatewayInfo(_messages.Message):
  r"""For display only. Metadata associated with a Compute Engine VPN gateway.

  Fields:
    displayName: Name of a VPN gateway.
    ipAddress: IP address of the VPN gateway.
    networkUri: URI of a Compute Engine network where the VPN gateway is
      configured.
    region: Name of a Google Cloud region where this VPN gateway is
      configured.
    uri: URI of a VPN gateway.
    vpnTunnelUri: A VPN tunnel that is associated with this VPN gateway. There
      may be multiple VPN tunnels configured on a VPN gateway, and only the
      one relevant to the test is displayed.
  """

  displayName = _messages.StringField(1)
  ipAddress = _messages.StringField(2)
  networkUri = _messages.StringField(3)
  region = _messages.StringField(4)
  uri = _messages.StringField(5)
  vpnTunnelUri = _messages.StringField(6)


class VpnTunnelInfo(_messages.Message):
  r"""For display only. Metadata associated with a Compute Engine VPN tunnel.

  Enums:
    RoutingTypeValueValuesEnum: Type of the routing policy.

  Fields:
    displayName: Name of a VPN tunnel.
    networkUri: URI of a Compute Engine network where the VPN tunnel is
      configured.
    region: Name of a Google Cloud region where this VPN tunnel is configured.
    remoteGateway: URI of a VPN gateway at remote end of the tunnel.
    remoteGatewayIp: Remote VPN gateway's IP address.
    routingType: Type of the routing policy.
    sourceGateway: URI of the VPN gateway at local end of the tunnel.
    sourceGatewayIp: Local VPN gateway's IP address.
    uri: URI of a VPN tunnel.
  """

  class RoutingTypeValueValuesEnum(_messages.Enum):
    r"""Type of the routing policy.

    Values:
      ROUTING_TYPE_UNSPECIFIED: Unspecified type. Default value.
      ROUTE_BASED: Route based VPN.
      POLICY_BASED: Policy based routing.
      DYNAMIC: Dynamic (BGP) routing.
    """
    ROUTING_TYPE_UNSPECIFIED = 0
    ROUTE_BASED = 1
    POLICY_BASED = 2
    DYNAMIC = 3

  displayName = _messages.StringField(1)
  networkUri = _messages.StringField(2)
  region = _messages.StringField(3)
  remoteGateway = _messages.StringField(4)
  remoteGatewayIp = _messages.StringField(5)
  routingType = _messages.EnumField('RoutingTypeValueValuesEnum', 6)
  sourceGateway = _messages.StringField(7)
  sourceGatewayIp = _messages.StringField(8)
  uri = _messages.StringField(9)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

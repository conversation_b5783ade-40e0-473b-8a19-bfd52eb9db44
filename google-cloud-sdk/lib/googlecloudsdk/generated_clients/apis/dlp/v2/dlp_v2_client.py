"""Generated client library for dlp version v2."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.dlp.v2 import dlp_v2_messages as messages


class DlpV2(base_api.BaseApiClient):
  """Generated client library for service dlp version v2."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://dlp.googleapis.com/'
  MTLS_BASE_URL = 'https://dlp.mtls.googleapis.com/'

  _PACKAGE = 'dlp'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v2'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'DlpV2'
  _URL_VERSION = 'v2'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new dlp handle."""
    url = url or self.BASE_URL
    super(DlpV2, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.infoTypes = self.InfoTypesService(self)
    self.locations_infoTypes = self.LocationsInfoTypesService(self)
    self.locations = self.LocationsService(self)
    self.organizations_deidentifyTemplates = self.OrganizationsDeidentifyTemplatesService(self)
    self.organizations_inspectTemplates = self.OrganizationsInspectTemplatesService(self)
    self.organizations_locations_deidentifyTemplates = self.OrganizationsLocationsDeidentifyTemplatesService(self)
    self.organizations_locations_dlpJobs = self.OrganizationsLocationsDlpJobsService(self)
    self.organizations_locations_inspectTemplates = self.OrganizationsLocationsInspectTemplatesService(self)
    self.organizations_locations_jobTriggers = self.OrganizationsLocationsJobTriggersService(self)
    self.organizations_locations_storedInfoTypes = self.OrganizationsLocationsStoredInfoTypesService(self)
    self.organizations_locations = self.OrganizationsLocationsService(self)
    self.organizations_storedInfoTypes = self.OrganizationsStoredInfoTypesService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_content = self.ProjectsContentService(self)
    self.projects_deidentifyTemplates = self.ProjectsDeidentifyTemplatesService(self)
    self.projects_dlpJobs = self.ProjectsDlpJobsService(self)
    self.projects_image = self.ProjectsImageService(self)
    self.projects_inspectTemplates = self.ProjectsInspectTemplatesService(self)
    self.projects_jobTriggers = self.ProjectsJobTriggersService(self)
    self.projects_locations_content = self.ProjectsLocationsContentService(self)
    self.projects_locations_deidentifyTemplates = self.ProjectsLocationsDeidentifyTemplatesService(self)
    self.projects_locations_dlpJobs = self.ProjectsLocationsDlpJobsService(self)
    self.projects_locations_image = self.ProjectsLocationsImageService(self)
    self.projects_locations_inspectTemplates = self.ProjectsLocationsInspectTemplatesService(self)
    self.projects_locations_jobTriggers = self.ProjectsLocationsJobTriggersService(self)
    self.projects_locations_storedInfoTypes = self.ProjectsLocationsStoredInfoTypesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects_storedInfoTypes = self.ProjectsStoredInfoTypesService(self)
    self.projects = self.ProjectsService(self)

  class InfoTypesService(base_api.BaseApiService):
    """Service class for the infoTypes resource."""

    _NAME = 'infoTypes'

    def __init__(self, client):
      super(DlpV2.InfoTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Returns a list of the sensitive information types that DLP API supports. See https://cloud.google.com/dlp/docs/infotypes-reference to learn more.

      Args:
        request: (DlpInfoTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListInfoTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        http_method='GET',
        method_id='dlp.infoTypes.list',
        ordered_params=[],
        path_params=[],
        query_params=['filter', 'languageCode', 'locationId', 'parent'],
        relative_path='v2/infoTypes',
        request_field='',
        request_type_name='DlpInfoTypesListRequest',
        response_type_name='GooglePrivacyDlpV2ListInfoTypesResponse',
        supports_download=False,
    )

  class LocationsInfoTypesService(base_api.BaseApiService):
    """Service class for the locations_infoTypes resource."""

    _NAME = 'locations_infoTypes'

    def __init__(self, client):
      super(DlpV2.LocationsInfoTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Returns a list of the sensitive information types that DLP API supports. See https://cloud.google.com/dlp/docs/infotypes-reference to learn more.

      Args:
        request: (DlpLocationsInfoTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListInfoTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/locations/{locationsId}/infoTypes',
        http_method='GET',
        method_id='dlp.locations.infoTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'languageCode', 'locationId'],
        relative_path='v2/{+parent}/infoTypes',
        request_field='',
        request_type_name='DlpLocationsInfoTypesListRequest',
        response_type_name='GooglePrivacyDlpV2ListInfoTypesResponse',
        supports_download=False,
    )

  class LocationsService(base_api.BaseApiService):
    """Service class for the locations resource."""

    _NAME = 'locations'

    def __init__(self, client):
      super(DlpV2.LocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsDeidentifyTemplatesService(base_api.BaseApiService):
    """Service class for the organizations_deidentifyTemplates resource."""

    _NAME = 'organizations_deidentifyTemplates'

    def __init__(self, client):
      super(DlpV2.OrganizationsDeidentifyTemplatesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a DeidentifyTemplate for reusing frequently used configuration for de-identifying content, images, and storage. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpOrganizationsDeidentifyTemplatesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DeidentifyTemplate) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/deidentifyTemplates',
        http_method='POST',
        method_id='dlp.organizations.deidentifyTemplates.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/deidentifyTemplates',
        request_field='googlePrivacyDlpV2CreateDeidentifyTemplateRequest',
        request_type_name='DlpOrganizationsDeidentifyTemplatesCreateRequest',
        response_type_name='GooglePrivacyDlpV2DeidentifyTemplate',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a DeidentifyTemplate. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpOrganizationsDeidentifyTemplatesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/deidentifyTemplates/{deidentifyTemplatesId}',
        http_method='DELETE',
        method_id='dlp.organizations.deidentifyTemplates.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpOrganizationsDeidentifyTemplatesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a DeidentifyTemplate. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpOrganizationsDeidentifyTemplatesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DeidentifyTemplate) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/deidentifyTemplates/{deidentifyTemplatesId}',
        http_method='GET',
        method_id='dlp.organizations.deidentifyTemplates.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpOrganizationsDeidentifyTemplatesGetRequest',
        response_type_name='GooglePrivacyDlpV2DeidentifyTemplate',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists DeidentifyTemplates. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpOrganizationsDeidentifyTemplatesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListDeidentifyTemplatesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/deidentifyTemplates',
        http_method='GET',
        method_id='dlp.organizations.deidentifyTemplates.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['locationId', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/deidentifyTemplates',
        request_field='',
        request_type_name='DlpOrganizationsDeidentifyTemplatesListRequest',
        response_type_name='GooglePrivacyDlpV2ListDeidentifyTemplatesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the DeidentifyTemplate. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpOrganizationsDeidentifyTemplatesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DeidentifyTemplate) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/deidentifyTemplates/{deidentifyTemplatesId}',
        http_method='PATCH',
        method_id='dlp.organizations.deidentifyTemplates.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='googlePrivacyDlpV2UpdateDeidentifyTemplateRequest',
        request_type_name='DlpOrganizationsDeidentifyTemplatesPatchRequest',
        response_type_name='GooglePrivacyDlpV2DeidentifyTemplate',
        supports_download=False,
    )

  class OrganizationsInspectTemplatesService(base_api.BaseApiService):
    """Service class for the organizations_inspectTemplates resource."""

    _NAME = 'organizations_inspectTemplates'

    def __init__(self, client):
      super(DlpV2.OrganizationsInspectTemplatesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an InspectTemplate for reusing frequently used configuration for inspecting content, images, and storage. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpOrganizationsInspectTemplatesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2InspectTemplate) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/inspectTemplates',
        http_method='POST',
        method_id='dlp.organizations.inspectTemplates.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/inspectTemplates',
        request_field='googlePrivacyDlpV2CreateInspectTemplateRequest',
        request_type_name='DlpOrganizationsInspectTemplatesCreateRequest',
        response_type_name='GooglePrivacyDlpV2InspectTemplate',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an InspectTemplate. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpOrganizationsInspectTemplatesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/inspectTemplates/{inspectTemplatesId}',
        http_method='DELETE',
        method_id='dlp.organizations.inspectTemplates.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpOrganizationsInspectTemplatesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an InspectTemplate. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpOrganizationsInspectTemplatesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2InspectTemplate) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/inspectTemplates/{inspectTemplatesId}',
        http_method='GET',
        method_id='dlp.organizations.inspectTemplates.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpOrganizationsInspectTemplatesGetRequest',
        response_type_name='GooglePrivacyDlpV2InspectTemplate',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists InspectTemplates. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpOrganizationsInspectTemplatesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListInspectTemplatesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/inspectTemplates',
        http_method='GET',
        method_id='dlp.organizations.inspectTemplates.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['locationId', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/inspectTemplates',
        request_field='',
        request_type_name='DlpOrganizationsInspectTemplatesListRequest',
        response_type_name='GooglePrivacyDlpV2ListInspectTemplatesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the InspectTemplate. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpOrganizationsInspectTemplatesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2InspectTemplate) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/inspectTemplates/{inspectTemplatesId}',
        http_method='PATCH',
        method_id='dlp.organizations.inspectTemplates.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='googlePrivacyDlpV2UpdateInspectTemplateRequest',
        request_type_name='DlpOrganizationsInspectTemplatesPatchRequest',
        response_type_name='GooglePrivacyDlpV2InspectTemplate',
        supports_download=False,
    )

  class OrganizationsLocationsDeidentifyTemplatesService(base_api.BaseApiService):
    """Service class for the organizations_locations_deidentifyTemplates resource."""

    _NAME = 'organizations_locations_deidentifyTemplates'

    def __init__(self, client):
      super(DlpV2.OrganizationsLocationsDeidentifyTemplatesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a DeidentifyTemplate for reusing frequently used configuration for de-identifying content, images, and storage. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpOrganizationsLocationsDeidentifyTemplatesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DeidentifyTemplate) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/deidentifyTemplates',
        http_method='POST',
        method_id='dlp.organizations.locations.deidentifyTemplates.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/deidentifyTemplates',
        request_field='googlePrivacyDlpV2CreateDeidentifyTemplateRequest',
        request_type_name='DlpOrganizationsLocationsDeidentifyTemplatesCreateRequest',
        response_type_name='GooglePrivacyDlpV2DeidentifyTemplate',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a DeidentifyTemplate. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpOrganizationsLocationsDeidentifyTemplatesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/deidentifyTemplates/{deidentifyTemplatesId}',
        http_method='DELETE',
        method_id='dlp.organizations.locations.deidentifyTemplates.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpOrganizationsLocationsDeidentifyTemplatesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a DeidentifyTemplate. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpOrganizationsLocationsDeidentifyTemplatesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DeidentifyTemplate) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/deidentifyTemplates/{deidentifyTemplatesId}',
        http_method='GET',
        method_id='dlp.organizations.locations.deidentifyTemplates.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpOrganizationsLocationsDeidentifyTemplatesGetRequest',
        response_type_name='GooglePrivacyDlpV2DeidentifyTemplate',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists DeidentifyTemplates. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpOrganizationsLocationsDeidentifyTemplatesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListDeidentifyTemplatesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/deidentifyTemplates',
        http_method='GET',
        method_id='dlp.organizations.locations.deidentifyTemplates.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['locationId', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/deidentifyTemplates',
        request_field='',
        request_type_name='DlpOrganizationsLocationsDeidentifyTemplatesListRequest',
        response_type_name='GooglePrivacyDlpV2ListDeidentifyTemplatesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the DeidentifyTemplate. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpOrganizationsLocationsDeidentifyTemplatesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DeidentifyTemplate) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/deidentifyTemplates/{deidentifyTemplatesId}',
        http_method='PATCH',
        method_id='dlp.organizations.locations.deidentifyTemplates.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='googlePrivacyDlpV2UpdateDeidentifyTemplateRequest',
        request_type_name='DlpOrganizationsLocationsDeidentifyTemplatesPatchRequest',
        response_type_name='GooglePrivacyDlpV2DeidentifyTemplate',
        supports_download=False,
    )

  class OrganizationsLocationsDlpJobsService(base_api.BaseApiService):
    """Service class for the organizations_locations_dlpJobs resource."""

    _NAME = 'organizations_locations_dlpJobs'

    def __init__(self, client):
      super(DlpV2.OrganizationsLocationsDlpJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists DlpJobs that match the specified filter in the request. See https://cloud.google.com/dlp/docs/inspecting-storage and https://cloud.google.com/dlp/docs/compute-risk-analysis to learn more.

      Args:
        request: (DlpOrganizationsLocationsDlpJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListDlpJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/dlpJobs',
        http_method='GET',
        method_id='dlp.organizations.locations.dlpJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'locationId', 'orderBy', 'pageSize', 'pageToken', 'type'],
        relative_path='v2/{+parent}/dlpJobs',
        request_field='',
        request_type_name='DlpOrganizationsLocationsDlpJobsListRequest',
        response_type_name='GooglePrivacyDlpV2ListDlpJobsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsInspectTemplatesService(base_api.BaseApiService):
    """Service class for the organizations_locations_inspectTemplates resource."""

    _NAME = 'organizations_locations_inspectTemplates'

    def __init__(self, client):
      super(DlpV2.OrganizationsLocationsInspectTemplatesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an InspectTemplate for reusing frequently used configuration for inspecting content, images, and storage. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpOrganizationsLocationsInspectTemplatesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2InspectTemplate) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/inspectTemplates',
        http_method='POST',
        method_id='dlp.organizations.locations.inspectTemplates.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/inspectTemplates',
        request_field='googlePrivacyDlpV2CreateInspectTemplateRequest',
        request_type_name='DlpOrganizationsLocationsInspectTemplatesCreateRequest',
        response_type_name='GooglePrivacyDlpV2InspectTemplate',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an InspectTemplate. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpOrganizationsLocationsInspectTemplatesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/inspectTemplates/{inspectTemplatesId}',
        http_method='DELETE',
        method_id='dlp.organizations.locations.inspectTemplates.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpOrganizationsLocationsInspectTemplatesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an InspectTemplate. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpOrganizationsLocationsInspectTemplatesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2InspectTemplate) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/inspectTemplates/{inspectTemplatesId}',
        http_method='GET',
        method_id='dlp.organizations.locations.inspectTemplates.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpOrganizationsLocationsInspectTemplatesGetRequest',
        response_type_name='GooglePrivacyDlpV2InspectTemplate',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists InspectTemplates. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpOrganizationsLocationsInspectTemplatesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListInspectTemplatesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/inspectTemplates',
        http_method='GET',
        method_id='dlp.organizations.locations.inspectTemplates.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['locationId', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/inspectTemplates',
        request_field='',
        request_type_name='DlpOrganizationsLocationsInspectTemplatesListRequest',
        response_type_name='GooglePrivacyDlpV2ListInspectTemplatesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the InspectTemplate. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpOrganizationsLocationsInspectTemplatesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2InspectTemplate) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/inspectTemplates/{inspectTemplatesId}',
        http_method='PATCH',
        method_id='dlp.organizations.locations.inspectTemplates.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='googlePrivacyDlpV2UpdateInspectTemplateRequest',
        request_type_name='DlpOrganizationsLocationsInspectTemplatesPatchRequest',
        response_type_name='GooglePrivacyDlpV2InspectTemplate',
        supports_download=False,
    )

  class OrganizationsLocationsJobTriggersService(base_api.BaseApiService):
    """Service class for the organizations_locations_jobTriggers resource."""

    _NAME = 'organizations_locations_jobTriggers'

    def __init__(self, client):
      super(DlpV2.OrganizationsLocationsJobTriggersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a job trigger to run DLP actions such as scanning storage for sensitive information on a set schedule. See https://cloud.google.com/dlp/docs/creating-job-triggers to learn more.

      Args:
        request: (DlpOrganizationsLocationsJobTriggersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2JobTrigger) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/jobTriggers',
        http_method='POST',
        method_id='dlp.organizations.locations.jobTriggers.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/jobTriggers',
        request_field='googlePrivacyDlpV2CreateJobTriggerRequest',
        request_type_name='DlpOrganizationsLocationsJobTriggersCreateRequest',
        response_type_name='GooglePrivacyDlpV2JobTrigger',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a job trigger. See https://cloud.google.com/dlp/docs/creating-job-triggers to learn more.

      Args:
        request: (DlpOrganizationsLocationsJobTriggersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/jobTriggers/{jobTriggersId}',
        http_method='DELETE',
        method_id='dlp.organizations.locations.jobTriggers.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpOrganizationsLocationsJobTriggersDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a job trigger. See https://cloud.google.com/dlp/docs/creating-job-triggers to learn more.

      Args:
        request: (DlpOrganizationsLocationsJobTriggersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2JobTrigger) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/jobTriggers/{jobTriggersId}',
        http_method='GET',
        method_id='dlp.organizations.locations.jobTriggers.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpOrganizationsLocationsJobTriggersGetRequest',
        response_type_name='GooglePrivacyDlpV2JobTrigger',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists job triggers. See https://cloud.google.com/dlp/docs/creating-job-triggers to learn more.

      Args:
        request: (DlpOrganizationsLocationsJobTriggersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListJobTriggersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/jobTriggers',
        http_method='GET',
        method_id='dlp.organizations.locations.jobTriggers.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'locationId', 'orderBy', 'pageSize', 'pageToken', 'type'],
        relative_path='v2/{+parent}/jobTriggers',
        request_field='',
        request_type_name='DlpOrganizationsLocationsJobTriggersListRequest',
        response_type_name='GooglePrivacyDlpV2ListJobTriggersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a job trigger. See https://cloud.google.com/dlp/docs/creating-job-triggers to learn more.

      Args:
        request: (DlpOrganizationsLocationsJobTriggersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2JobTrigger) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/jobTriggers/{jobTriggersId}',
        http_method='PATCH',
        method_id='dlp.organizations.locations.jobTriggers.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='googlePrivacyDlpV2UpdateJobTriggerRequest',
        request_type_name='DlpOrganizationsLocationsJobTriggersPatchRequest',
        response_type_name='GooglePrivacyDlpV2JobTrigger',
        supports_download=False,
    )

  class OrganizationsLocationsStoredInfoTypesService(base_api.BaseApiService):
    """Service class for the organizations_locations_storedInfoTypes resource."""

    _NAME = 'organizations_locations_storedInfoTypes'

    def __init__(self, client):
      super(DlpV2.OrganizationsLocationsStoredInfoTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a pre-built stored infoType to be used for inspection. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpOrganizationsLocationsStoredInfoTypesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2StoredInfoType) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/storedInfoTypes',
        http_method='POST',
        method_id='dlp.organizations.locations.storedInfoTypes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/storedInfoTypes',
        request_field='googlePrivacyDlpV2CreateStoredInfoTypeRequest',
        request_type_name='DlpOrganizationsLocationsStoredInfoTypesCreateRequest',
        response_type_name='GooglePrivacyDlpV2StoredInfoType',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a stored infoType. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpOrganizationsLocationsStoredInfoTypesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/storedInfoTypes/{storedInfoTypesId}',
        http_method='DELETE',
        method_id='dlp.organizations.locations.storedInfoTypes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpOrganizationsLocationsStoredInfoTypesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a stored infoType. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpOrganizationsLocationsStoredInfoTypesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2StoredInfoType) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/storedInfoTypes/{storedInfoTypesId}',
        http_method='GET',
        method_id='dlp.organizations.locations.storedInfoTypes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpOrganizationsLocationsStoredInfoTypesGetRequest',
        response_type_name='GooglePrivacyDlpV2StoredInfoType',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists stored infoTypes. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpOrganizationsLocationsStoredInfoTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListStoredInfoTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/storedInfoTypes',
        http_method='GET',
        method_id='dlp.organizations.locations.storedInfoTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['locationId', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/storedInfoTypes',
        request_field='',
        request_type_name='DlpOrganizationsLocationsStoredInfoTypesListRequest',
        response_type_name='GooglePrivacyDlpV2ListStoredInfoTypesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the stored infoType by creating a new version. The existing version will continue to be used until the new version is ready. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpOrganizationsLocationsStoredInfoTypesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2StoredInfoType) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/locations/{locationsId}/storedInfoTypes/{storedInfoTypesId}',
        http_method='PATCH',
        method_id='dlp.organizations.locations.storedInfoTypes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='googlePrivacyDlpV2UpdateStoredInfoTypeRequest',
        request_type_name='DlpOrganizationsLocationsStoredInfoTypesPatchRequest',
        response_type_name='GooglePrivacyDlpV2StoredInfoType',
        supports_download=False,
    )

  class OrganizationsLocationsService(base_api.BaseApiService):
    """Service class for the organizations_locations resource."""

    _NAME = 'organizations_locations'

    def __init__(self, client):
      super(DlpV2.OrganizationsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsStoredInfoTypesService(base_api.BaseApiService):
    """Service class for the organizations_storedInfoTypes resource."""

    _NAME = 'organizations_storedInfoTypes'

    def __init__(self, client):
      super(DlpV2.OrganizationsStoredInfoTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a pre-built stored infoType to be used for inspection. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpOrganizationsStoredInfoTypesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2StoredInfoType) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/storedInfoTypes',
        http_method='POST',
        method_id='dlp.organizations.storedInfoTypes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/storedInfoTypes',
        request_field='googlePrivacyDlpV2CreateStoredInfoTypeRequest',
        request_type_name='DlpOrganizationsStoredInfoTypesCreateRequest',
        response_type_name='GooglePrivacyDlpV2StoredInfoType',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a stored infoType. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpOrganizationsStoredInfoTypesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/storedInfoTypes/{storedInfoTypesId}',
        http_method='DELETE',
        method_id='dlp.organizations.storedInfoTypes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpOrganizationsStoredInfoTypesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a stored infoType. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpOrganizationsStoredInfoTypesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2StoredInfoType) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/storedInfoTypes/{storedInfoTypesId}',
        http_method='GET',
        method_id='dlp.organizations.storedInfoTypes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpOrganizationsStoredInfoTypesGetRequest',
        response_type_name='GooglePrivacyDlpV2StoredInfoType',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists stored infoTypes. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpOrganizationsStoredInfoTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListStoredInfoTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/storedInfoTypes',
        http_method='GET',
        method_id='dlp.organizations.storedInfoTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['locationId', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/storedInfoTypes',
        request_field='',
        request_type_name='DlpOrganizationsStoredInfoTypesListRequest',
        response_type_name='GooglePrivacyDlpV2ListStoredInfoTypesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the stored infoType by creating a new version. The existing version will continue to be used until the new version is ready. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpOrganizationsStoredInfoTypesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2StoredInfoType) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/organizations/{organizationsId}/storedInfoTypes/{storedInfoTypesId}',
        http_method='PATCH',
        method_id='dlp.organizations.storedInfoTypes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='googlePrivacyDlpV2UpdateStoredInfoTypeRequest',
        request_type_name='DlpOrganizationsStoredInfoTypesPatchRequest',
        response_type_name='GooglePrivacyDlpV2StoredInfoType',
        supports_download=False,
    )

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(DlpV2.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsContentService(base_api.BaseApiService):
    """Service class for the projects_content resource."""

    _NAME = 'projects_content'

    def __init__(self, client):
      super(DlpV2.ProjectsContentService, self).__init__(client)
      self._upload_configs = {
          }

    def Deidentify(self, request, global_params=None):
      r"""De-identifies potentially sensitive info from a ContentItem. This method has limits on input size and output size. See https://cloud.google.com/dlp/docs/deidentify-sensitive-data to learn more. When no InfoTypes or CustomInfoTypes are specified in this request, the system will automatically choose what detectors to run. By default this may be all types, but may change over time as detectors are updated.

      Args:
        request: (DlpProjectsContentDeidentifyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DeidentifyContentResponse) The response message.
      """
      config = self.GetMethodConfig('Deidentify')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deidentify.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/content:deidentify',
        http_method='POST',
        method_id='dlp.projects.content.deidentify',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/content:deidentify',
        request_field='googlePrivacyDlpV2DeidentifyContentRequest',
        request_type_name='DlpProjectsContentDeidentifyRequest',
        response_type_name='GooglePrivacyDlpV2DeidentifyContentResponse',
        supports_download=False,
    )

    def Inspect(self, request, global_params=None):
      r"""Finds potentially sensitive info in content. This method has limits on input size, processing time, and output size. When no InfoTypes or CustomInfoTypes are specified in this request, the system will automatically choose what detectors to run. By default this may be all types, but may change over time as detectors are updated. For how to guides, see https://cloud.google.com/dlp/docs/inspecting-images and https://cloud.google.com/dlp/docs/inspecting-text,.

      Args:
        request: (DlpProjectsContentInspectRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2InspectContentResponse) The response message.
      """
      config = self.GetMethodConfig('Inspect')
      return self._RunMethod(
          config, request, global_params=global_params)

    Inspect.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/content:inspect',
        http_method='POST',
        method_id='dlp.projects.content.inspect',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/content:inspect',
        request_field='googlePrivacyDlpV2InspectContentRequest',
        request_type_name='DlpProjectsContentInspectRequest',
        response_type_name='GooglePrivacyDlpV2InspectContentResponse',
        supports_download=False,
    )

    def Reidentify(self, request, global_params=None):
      r"""Re-identifies content that has been de-identified. See https://cloud.google.com/dlp/docs/pseudonymization#re-identification_in_free_text_code_example to learn more.

      Args:
        request: (DlpProjectsContentReidentifyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ReidentifyContentResponse) The response message.
      """
      config = self.GetMethodConfig('Reidentify')
      return self._RunMethod(
          config, request, global_params=global_params)

    Reidentify.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/content:reidentify',
        http_method='POST',
        method_id='dlp.projects.content.reidentify',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/content:reidentify',
        request_field='googlePrivacyDlpV2ReidentifyContentRequest',
        request_type_name='DlpProjectsContentReidentifyRequest',
        response_type_name='GooglePrivacyDlpV2ReidentifyContentResponse',
        supports_download=False,
    )

  class ProjectsDeidentifyTemplatesService(base_api.BaseApiService):
    """Service class for the projects_deidentifyTemplates resource."""

    _NAME = 'projects_deidentifyTemplates'

    def __init__(self, client):
      super(DlpV2.ProjectsDeidentifyTemplatesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a DeidentifyTemplate for reusing frequently used configuration for de-identifying content, images, and storage. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpProjectsDeidentifyTemplatesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DeidentifyTemplate) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/deidentifyTemplates',
        http_method='POST',
        method_id='dlp.projects.deidentifyTemplates.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/deidentifyTemplates',
        request_field='googlePrivacyDlpV2CreateDeidentifyTemplateRequest',
        request_type_name='DlpProjectsDeidentifyTemplatesCreateRequest',
        response_type_name='GooglePrivacyDlpV2DeidentifyTemplate',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a DeidentifyTemplate. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpProjectsDeidentifyTemplatesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/deidentifyTemplates/{deidentifyTemplatesId}',
        http_method='DELETE',
        method_id='dlp.projects.deidentifyTemplates.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsDeidentifyTemplatesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a DeidentifyTemplate. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpProjectsDeidentifyTemplatesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DeidentifyTemplate) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/deidentifyTemplates/{deidentifyTemplatesId}',
        http_method='GET',
        method_id='dlp.projects.deidentifyTemplates.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsDeidentifyTemplatesGetRequest',
        response_type_name='GooglePrivacyDlpV2DeidentifyTemplate',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists DeidentifyTemplates. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpProjectsDeidentifyTemplatesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListDeidentifyTemplatesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/deidentifyTemplates',
        http_method='GET',
        method_id='dlp.projects.deidentifyTemplates.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['locationId', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/deidentifyTemplates',
        request_field='',
        request_type_name='DlpProjectsDeidentifyTemplatesListRequest',
        response_type_name='GooglePrivacyDlpV2ListDeidentifyTemplatesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the DeidentifyTemplate. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpProjectsDeidentifyTemplatesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DeidentifyTemplate) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/deidentifyTemplates/{deidentifyTemplatesId}',
        http_method='PATCH',
        method_id='dlp.projects.deidentifyTemplates.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='googlePrivacyDlpV2UpdateDeidentifyTemplateRequest',
        request_type_name='DlpProjectsDeidentifyTemplatesPatchRequest',
        response_type_name='GooglePrivacyDlpV2DeidentifyTemplate',
        supports_download=False,
    )

  class ProjectsDlpJobsService(base_api.BaseApiService):
    """Service class for the projects_dlpJobs resource."""

    _NAME = 'projects_dlpJobs'

    def __init__(self, client):
      super(DlpV2.ProjectsDlpJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running DlpJob. The server makes a best effort to cancel the DlpJob, but success is not guaranteed. See https://cloud.google.com/dlp/docs/inspecting-storage and https://cloud.google.com/dlp/docs/compute-risk-analysis to learn more.

      Args:
        request: (DlpProjectsDlpJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/dlpJobs/{dlpJobsId}:cancel',
        http_method='POST',
        method_id='dlp.projects.dlpJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:cancel',
        request_field='googlePrivacyDlpV2CancelDlpJobRequest',
        request_type_name='DlpProjectsDlpJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new job to inspect storage or calculate risk metrics. See https://cloud.google.com/dlp/docs/inspecting-storage and https://cloud.google.com/dlp/docs/compute-risk-analysis to learn more. When no InfoTypes or CustomInfoTypes are specified in inspect jobs, the system will automatically choose what detectors to run. By default this may be all types, but may change over time as detectors are updated.

      Args:
        request: (DlpProjectsDlpJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DlpJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/dlpJobs',
        http_method='POST',
        method_id='dlp.projects.dlpJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/dlpJobs',
        request_field='googlePrivacyDlpV2CreateDlpJobRequest',
        request_type_name='DlpProjectsDlpJobsCreateRequest',
        response_type_name='GooglePrivacyDlpV2DlpJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running DlpJob. This method indicates that the client is no longer interested in the DlpJob result. The job will be canceled if possible. See https://cloud.google.com/dlp/docs/inspecting-storage and https://cloud.google.com/dlp/docs/compute-risk-analysis to learn more.

      Args:
        request: (DlpProjectsDlpJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/dlpJobs/{dlpJobsId}',
        http_method='DELETE',
        method_id='dlp.projects.dlpJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsDlpJobsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running DlpJob. See https://cloud.google.com/dlp/docs/inspecting-storage and https://cloud.google.com/dlp/docs/compute-risk-analysis to learn more.

      Args:
        request: (DlpProjectsDlpJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DlpJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/dlpJobs/{dlpJobsId}',
        http_method='GET',
        method_id='dlp.projects.dlpJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsDlpJobsGetRequest',
        response_type_name='GooglePrivacyDlpV2DlpJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists DlpJobs that match the specified filter in the request. See https://cloud.google.com/dlp/docs/inspecting-storage and https://cloud.google.com/dlp/docs/compute-risk-analysis to learn more.

      Args:
        request: (DlpProjectsDlpJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListDlpJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/dlpJobs',
        http_method='GET',
        method_id='dlp.projects.dlpJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'locationId', 'orderBy', 'pageSize', 'pageToken', 'type'],
        relative_path='v2/{+parent}/dlpJobs',
        request_field='',
        request_type_name='DlpProjectsDlpJobsListRequest',
        response_type_name='GooglePrivacyDlpV2ListDlpJobsResponse',
        supports_download=False,
    )

  class ProjectsImageService(base_api.BaseApiService):
    """Service class for the projects_image resource."""

    _NAME = 'projects_image'

    def __init__(self, client):
      super(DlpV2.ProjectsImageService, self).__init__(client)
      self._upload_configs = {
          }

    def Redact(self, request, global_params=None):
      r"""Redacts potentially sensitive info from an image. This method has limits on input size, processing time, and output size. See https://cloud.google.com/dlp/docs/redacting-sensitive-data-images to learn more. When no InfoTypes or CustomInfoTypes are specified in this request, the system will automatically choose what detectors to run. By default this may be all types, but may change over time as detectors are updated.

      Args:
        request: (DlpProjectsImageRedactRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2RedactImageResponse) The response message.
      """
      config = self.GetMethodConfig('Redact')
      return self._RunMethod(
          config, request, global_params=global_params)

    Redact.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/image:redact',
        http_method='POST',
        method_id='dlp.projects.image.redact',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/image:redact',
        request_field='googlePrivacyDlpV2RedactImageRequest',
        request_type_name='DlpProjectsImageRedactRequest',
        response_type_name='GooglePrivacyDlpV2RedactImageResponse',
        supports_download=False,
    )

  class ProjectsInspectTemplatesService(base_api.BaseApiService):
    """Service class for the projects_inspectTemplates resource."""

    _NAME = 'projects_inspectTemplates'

    def __init__(self, client):
      super(DlpV2.ProjectsInspectTemplatesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an InspectTemplate for reusing frequently used configuration for inspecting content, images, and storage. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpProjectsInspectTemplatesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2InspectTemplate) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/inspectTemplates',
        http_method='POST',
        method_id='dlp.projects.inspectTemplates.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/inspectTemplates',
        request_field='googlePrivacyDlpV2CreateInspectTemplateRequest',
        request_type_name='DlpProjectsInspectTemplatesCreateRequest',
        response_type_name='GooglePrivacyDlpV2InspectTemplate',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an InspectTemplate. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpProjectsInspectTemplatesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/inspectTemplates/{inspectTemplatesId}',
        http_method='DELETE',
        method_id='dlp.projects.inspectTemplates.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsInspectTemplatesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an InspectTemplate. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpProjectsInspectTemplatesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2InspectTemplate) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/inspectTemplates/{inspectTemplatesId}',
        http_method='GET',
        method_id='dlp.projects.inspectTemplates.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsInspectTemplatesGetRequest',
        response_type_name='GooglePrivacyDlpV2InspectTemplate',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists InspectTemplates. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpProjectsInspectTemplatesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListInspectTemplatesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/inspectTemplates',
        http_method='GET',
        method_id='dlp.projects.inspectTemplates.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['locationId', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/inspectTemplates',
        request_field='',
        request_type_name='DlpProjectsInspectTemplatesListRequest',
        response_type_name='GooglePrivacyDlpV2ListInspectTemplatesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the InspectTemplate. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpProjectsInspectTemplatesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2InspectTemplate) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/inspectTemplates/{inspectTemplatesId}',
        http_method='PATCH',
        method_id='dlp.projects.inspectTemplates.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='googlePrivacyDlpV2UpdateInspectTemplateRequest',
        request_type_name='DlpProjectsInspectTemplatesPatchRequest',
        response_type_name='GooglePrivacyDlpV2InspectTemplate',
        supports_download=False,
    )

  class ProjectsJobTriggersService(base_api.BaseApiService):
    """Service class for the projects_jobTriggers resource."""

    _NAME = 'projects_jobTriggers'

    def __init__(self, client):
      super(DlpV2.ProjectsJobTriggersService, self).__init__(client)
      self._upload_configs = {
          }

    def Activate(self, request, global_params=None):
      r"""Activate a job trigger. Causes the immediate execute of a trigger instead of waiting on the trigger event to occur.

      Args:
        request: (DlpProjectsJobTriggersActivateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DlpJob) The response message.
      """
      config = self.GetMethodConfig('Activate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Activate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/jobTriggers/{jobTriggersId}:activate',
        http_method='POST',
        method_id='dlp.projects.jobTriggers.activate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:activate',
        request_field='googlePrivacyDlpV2ActivateJobTriggerRequest',
        request_type_name='DlpProjectsJobTriggersActivateRequest',
        response_type_name='GooglePrivacyDlpV2DlpJob',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a job trigger to run DLP actions such as scanning storage for sensitive information on a set schedule. See https://cloud.google.com/dlp/docs/creating-job-triggers to learn more.

      Args:
        request: (DlpProjectsJobTriggersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2JobTrigger) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/jobTriggers',
        http_method='POST',
        method_id='dlp.projects.jobTriggers.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/jobTriggers',
        request_field='googlePrivacyDlpV2CreateJobTriggerRequest',
        request_type_name='DlpProjectsJobTriggersCreateRequest',
        response_type_name='GooglePrivacyDlpV2JobTrigger',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a job trigger. See https://cloud.google.com/dlp/docs/creating-job-triggers to learn more.

      Args:
        request: (DlpProjectsJobTriggersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/jobTriggers/{jobTriggersId}',
        http_method='DELETE',
        method_id='dlp.projects.jobTriggers.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsJobTriggersDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a job trigger. See https://cloud.google.com/dlp/docs/creating-job-triggers to learn more.

      Args:
        request: (DlpProjectsJobTriggersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2JobTrigger) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/jobTriggers/{jobTriggersId}',
        http_method='GET',
        method_id='dlp.projects.jobTriggers.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsJobTriggersGetRequest',
        response_type_name='GooglePrivacyDlpV2JobTrigger',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists job triggers. See https://cloud.google.com/dlp/docs/creating-job-triggers to learn more.

      Args:
        request: (DlpProjectsJobTriggersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListJobTriggersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/jobTriggers',
        http_method='GET',
        method_id='dlp.projects.jobTriggers.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'locationId', 'orderBy', 'pageSize', 'pageToken', 'type'],
        relative_path='v2/{+parent}/jobTriggers',
        request_field='',
        request_type_name='DlpProjectsJobTriggersListRequest',
        response_type_name='GooglePrivacyDlpV2ListJobTriggersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a job trigger. See https://cloud.google.com/dlp/docs/creating-job-triggers to learn more.

      Args:
        request: (DlpProjectsJobTriggersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2JobTrigger) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/jobTriggers/{jobTriggersId}',
        http_method='PATCH',
        method_id='dlp.projects.jobTriggers.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='googlePrivacyDlpV2UpdateJobTriggerRequest',
        request_type_name='DlpProjectsJobTriggersPatchRequest',
        response_type_name='GooglePrivacyDlpV2JobTrigger',
        supports_download=False,
    )

  class ProjectsLocationsContentService(base_api.BaseApiService):
    """Service class for the projects_locations_content resource."""

    _NAME = 'projects_locations_content'

    def __init__(self, client):
      super(DlpV2.ProjectsLocationsContentService, self).__init__(client)
      self._upload_configs = {
          }

    def Deidentify(self, request, global_params=None):
      r"""De-identifies potentially sensitive info from a ContentItem. This method has limits on input size and output size. See https://cloud.google.com/dlp/docs/deidentify-sensitive-data to learn more. When no InfoTypes or CustomInfoTypes are specified in this request, the system will automatically choose what detectors to run. By default this may be all types, but may change over time as detectors are updated.

      Args:
        request: (DlpProjectsLocationsContentDeidentifyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DeidentifyContentResponse) The response message.
      """
      config = self.GetMethodConfig('Deidentify')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deidentify.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/content:deidentify',
        http_method='POST',
        method_id='dlp.projects.locations.content.deidentify',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/content:deidentify',
        request_field='googlePrivacyDlpV2DeidentifyContentRequest',
        request_type_name='DlpProjectsLocationsContentDeidentifyRequest',
        response_type_name='GooglePrivacyDlpV2DeidentifyContentResponse',
        supports_download=False,
    )

    def Inspect(self, request, global_params=None):
      r"""Finds potentially sensitive info in content. This method has limits on input size, processing time, and output size. When no InfoTypes or CustomInfoTypes are specified in this request, the system will automatically choose what detectors to run. By default this may be all types, but may change over time as detectors are updated. For how to guides, see https://cloud.google.com/dlp/docs/inspecting-images and https://cloud.google.com/dlp/docs/inspecting-text,.

      Args:
        request: (DlpProjectsLocationsContentInspectRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2InspectContentResponse) The response message.
      """
      config = self.GetMethodConfig('Inspect')
      return self._RunMethod(
          config, request, global_params=global_params)

    Inspect.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/content:inspect',
        http_method='POST',
        method_id='dlp.projects.locations.content.inspect',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/content:inspect',
        request_field='googlePrivacyDlpV2InspectContentRequest',
        request_type_name='DlpProjectsLocationsContentInspectRequest',
        response_type_name='GooglePrivacyDlpV2InspectContentResponse',
        supports_download=False,
    )

    def Reidentify(self, request, global_params=None):
      r"""Re-identifies content that has been de-identified. See https://cloud.google.com/dlp/docs/pseudonymization#re-identification_in_free_text_code_example to learn more.

      Args:
        request: (DlpProjectsLocationsContentReidentifyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ReidentifyContentResponse) The response message.
      """
      config = self.GetMethodConfig('Reidentify')
      return self._RunMethod(
          config, request, global_params=global_params)

    Reidentify.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/content:reidentify',
        http_method='POST',
        method_id='dlp.projects.locations.content.reidentify',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/content:reidentify',
        request_field='googlePrivacyDlpV2ReidentifyContentRequest',
        request_type_name='DlpProjectsLocationsContentReidentifyRequest',
        response_type_name='GooglePrivacyDlpV2ReidentifyContentResponse',
        supports_download=False,
    )

  class ProjectsLocationsDeidentifyTemplatesService(base_api.BaseApiService):
    """Service class for the projects_locations_deidentifyTemplates resource."""

    _NAME = 'projects_locations_deidentifyTemplates'

    def __init__(self, client):
      super(DlpV2.ProjectsLocationsDeidentifyTemplatesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a DeidentifyTemplate for reusing frequently used configuration for de-identifying content, images, and storage. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpProjectsLocationsDeidentifyTemplatesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DeidentifyTemplate) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/deidentifyTemplates',
        http_method='POST',
        method_id='dlp.projects.locations.deidentifyTemplates.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/deidentifyTemplates',
        request_field='googlePrivacyDlpV2CreateDeidentifyTemplateRequest',
        request_type_name='DlpProjectsLocationsDeidentifyTemplatesCreateRequest',
        response_type_name='GooglePrivacyDlpV2DeidentifyTemplate',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a DeidentifyTemplate. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpProjectsLocationsDeidentifyTemplatesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/deidentifyTemplates/{deidentifyTemplatesId}',
        http_method='DELETE',
        method_id='dlp.projects.locations.deidentifyTemplates.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsLocationsDeidentifyTemplatesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a DeidentifyTemplate. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpProjectsLocationsDeidentifyTemplatesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DeidentifyTemplate) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/deidentifyTemplates/{deidentifyTemplatesId}',
        http_method='GET',
        method_id='dlp.projects.locations.deidentifyTemplates.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsLocationsDeidentifyTemplatesGetRequest',
        response_type_name='GooglePrivacyDlpV2DeidentifyTemplate',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists DeidentifyTemplates. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpProjectsLocationsDeidentifyTemplatesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListDeidentifyTemplatesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/deidentifyTemplates',
        http_method='GET',
        method_id='dlp.projects.locations.deidentifyTemplates.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['locationId', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/deidentifyTemplates',
        request_field='',
        request_type_name='DlpProjectsLocationsDeidentifyTemplatesListRequest',
        response_type_name='GooglePrivacyDlpV2ListDeidentifyTemplatesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the DeidentifyTemplate. See https://cloud.google.com/dlp/docs/creating-templates-deid to learn more.

      Args:
        request: (DlpProjectsLocationsDeidentifyTemplatesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DeidentifyTemplate) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/deidentifyTemplates/{deidentifyTemplatesId}',
        http_method='PATCH',
        method_id='dlp.projects.locations.deidentifyTemplates.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='googlePrivacyDlpV2UpdateDeidentifyTemplateRequest',
        request_type_name='DlpProjectsLocationsDeidentifyTemplatesPatchRequest',
        response_type_name='GooglePrivacyDlpV2DeidentifyTemplate',
        supports_download=False,
    )

  class ProjectsLocationsDlpJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_dlpJobs resource."""

    _NAME = 'projects_locations_dlpJobs'

    def __init__(self, client):
      super(DlpV2.ProjectsLocationsDlpJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running DlpJob. The server makes a best effort to cancel the DlpJob, but success is not guaranteed. See https://cloud.google.com/dlp/docs/inspecting-storage and https://cloud.google.com/dlp/docs/compute-risk-analysis to learn more.

      Args:
        request: (DlpProjectsLocationsDlpJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/dlpJobs/{dlpJobsId}:cancel',
        http_method='POST',
        method_id='dlp.projects.locations.dlpJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:cancel',
        request_field='googlePrivacyDlpV2CancelDlpJobRequest',
        request_type_name='DlpProjectsLocationsDlpJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new job to inspect storage or calculate risk metrics. See https://cloud.google.com/dlp/docs/inspecting-storage and https://cloud.google.com/dlp/docs/compute-risk-analysis to learn more. When no InfoTypes or CustomInfoTypes are specified in inspect jobs, the system will automatically choose what detectors to run. By default this may be all types, but may change over time as detectors are updated.

      Args:
        request: (DlpProjectsLocationsDlpJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DlpJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/dlpJobs',
        http_method='POST',
        method_id='dlp.projects.locations.dlpJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/dlpJobs',
        request_field='googlePrivacyDlpV2CreateDlpJobRequest',
        request_type_name='DlpProjectsLocationsDlpJobsCreateRequest',
        response_type_name='GooglePrivacyDlpV2DlpJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running DlpJob. This method indicates that the client is no longer interested in the DlpJob result. The job will be canceled if possible. See https://cloud.google.com/dlp/docs/inspecting-storage and https://cloud.google.com/dlp/docs/compute-risk-analysis to learn more.

      Args:
        request: (DlpProjectsLocationsDlpJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/dlpJobs/{dlpJobsId}',
        http_method='DELETE',
        method_id='dlp.projects.locations.dlpJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsLocationsDlpJobsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Finish(self, request, global_params=None):
      r"""Finish a running hybrid DlpJob. Triggers the finalization steps and running of any enabled actions that have not yet run.

      Args:
        request: (DlpProjectsLocationsDlpJobsFinishRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Finish')
      return self._RunMethod(
          config, request, global_params=global_params)

    Finish.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/dlpJobs/{dlpJobsId}:finish',
        http_method='POST',
        method_id='dlp.projects.locations.dlpJobs.finish',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:finish',
        request_field='googlePrivacyDlpV2FinishDlpJobRequest',
        request_type_name='DlpProjectsLocationsDlpJobsFinishRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running DlpJob. See https://cloud.google.com/dlp/docs/inspecting-storage and https://cloud.google.com/dlp/docs/compute-risk-analysis to learn more.

      Args:
        request: (DlpProjectsLocationsDlpJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DlpJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/dlpJobs/{dlpJobsId}',
        http_method='GET',
        method_id='dlp.projects.locations.dlpJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsLocationsDlpJobsGetRequest',
        response_type_name='GooglePrivacyDlpV2DlpJob',
        supports_download=False,
    )

    def HybridInspect(self, request, global_params=None):
      r"""Inspect hybrid content and store findings to a job. To review the findings, inspect the job. Inspection will occur asynchronously.

      Args:
        request: (DlpProjectsLocationsDlpJobsHybridInspectRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2HybridInspectResponse) The response message.
      """
      config = self.GetMethodConfig('HybridInspect')
      return self._RunMethod(
          config, request, global_params=global_params)

    HybridInspect.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/dlpJobs/{dlpJobsId}:hybridInspect',
        http_method='POST',
        method_id='dlp.projects.locations.dlpJobs.hybridInspect',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:hybridInspect',
        request_field='googlePrivacyDlpV2HybridInspectDlpJobRequest',
        request_type_name='DlpProjectsLocationsDlpJobsHybridInspectRequest',
        response_type_name='GooglePrivacyDlpV2HybridInspectResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists DlpJobs that match the specified filter in the request. See https://cloud.google.com/dlp/docs/inspecting-storage and https://cloud.google.com/dlp/docs/compute-risk-analysis to learn more.

      Args:
        request: (DlpProjectsLocationsDlpJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListDlpJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/dlpJobs',
        http_method='GET',
        method_id='dlp.projects.locations.dlpJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'locationId', 'orderBy', 'pageSize', 'pageToken', 'type'],
        relative_path='v2/{+parent}/dlpJobs',
        request_field='',
        request_type_name='DlpProjectsLocationsDlpJobsListRequest',
        response_type_name='GooglePrivacyDlpV2ListDlpJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsImageService(base_api.BaseApiService):
    """Service class for the projects_locations_image resource."""

    _NAME = 'projects_locations_image'

    def __init__(self, client):
      super(DlpV2.ProjectsLocationsImageService, self).__init__(client)
      self._upload_configs = {
          }

    def Redact(self, request, global_params=None):
      r"""Redacts potentially sensitive info from an image. This method has limits on input size, processing time, and output size. See https://cloud.google.com/dlp/docs/redacting-sensitive-data-images to learn more. When no InfoTypes or CustomInfoTypes are specified in this request, the system will automatically choose what detectors to run. By default this may be all types, but may change over time as detectors are updated.

      Args:
        request: (DlpProjectsLocationsImageRedactRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2RedactImageResponse) The response message.
      """
      config = self.GetMethodConfig('Redact')
      return self._RunMethod(
          config, request, global_params=global_params)

    Redact.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/image:redact',
        http_method='POST',
        method_id='dlp.projects.locations.image.redact',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/image:redact',
        request_field='googlePrivacyDlpV2RedactImageRequest',
        request_type_name='DlpProjectsLocationsImageRedactRequest',
        response_type_name='GooglePrivacyDlpV2RedactImageResponse',
        supports_download=False,
    )

  class ProjectsLocationsInspectTemplatesService(base_api.BaseApiService):
    """Service class for the projects_locations_inspectTemplates resource."""

    _NAME = 'projects_locations_inspectTemplates'

    def __init__(self, client):
      super(DlpV2.ProjectsLocationsInspectTemplatesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an InspectTemplate for reusing frequently used configuration for inspecting content, images, and storage. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpProjectsLocationsInspectTemplatesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2InspectTemplate) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/inspectTemplates',
        http_method='POST',
        method_id='dlp.projects.locations.inspectTemplates.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/inspectTemplates',
        request_field='googlePrivacyDlpV2CreateInspectTemplateRequest',
        request_type_name='DlpProjectsLocationsInspectTemplatesCreateRequest',
        response_type_name='GooglePrivacyDlpV2InspectTemplate',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an InspectTemplate. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpProjectsLocationsInspectTemplatesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/inspectTemplates/{inspectTemplatesId}',
        http_method='DELETE',
        method_id='dlp.projects.locations.inspectTemplates.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsLocationsInspectTemplatesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an InspectTemplate. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpProjectsLocationsInspectTemplatesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2InspectTemplate) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/inspectTemplates/{inspectTemplatesId}',
        http_method='GET',
        method_id='dlp.projects.locations.inspectTemplates.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsLocationsInspectTemplatesGetRequest',
        response_type_name='GooglePrivacyDlpV2InspectTemplate',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists InspectTemplates. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpProjectsLocationsInspectTemplatesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListInspectTemplatesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/inspectTemplates',
        http_method='GET',
        method_id='dlp.projects.locations.inspectTemplates.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['locationId', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/inspectTemplates',
        request_field='',
        request_type_name='DlpProjectsLocationsInspectTemplatesListRequest',
        response_type_name='GooglePrivacyDlpV2ListInspectTemplatesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the InspectTemplate. See https://cloud.google.com/dlp/docs/creating-templates to learn more.

      Args:
        request: (DlpProjectsLocationsInspectTemplatesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2InspectTemplate) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/inspectTemplates/{inspectTemplatesId}',
        http_method='PATCH',
        method_id='dlp.projects.locations.inspectTemplates.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='googlePrivacyDlpV2UpdateInspectTemplateRequest',
        request_type_name='DlpProjectsLocationsInspectTemplatesPatchRequest',
        response_type_name='GooglePrivacyDlpV2InspectTemplate',
        supports_download=False,
    )

  class ProjectsLocationsJobTriggersService(base_api.BaseApiService):
    """Service class for the projects_locations_jobTriggers resource."""

    _NAME = 'projects_locations_jobTriggers'

    def __init__(self, client):
      super(DlpV2.ProjectsLocationsJobTriggersService, self).__init__(client)
      self._upload_configs = {
          }

    def Activate(self, request, global_params=None):
      r"""Activate a job trigger. Causes the immediate execute of a trigger instead of waiting on the trigger event to occur.

      Args:
        request: (DlpProjectsLocationsJobTriggersActivateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2DlpJob) The response message.
      """
      config = self.GetMethodConfig('Activate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Activate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobTriggers/{jobTriggersId}:activate',
        http_method='POST',
        method_id='dlp.projects.locations.jobTriggers.activate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:activate',
        request_field='googlePrivacyDlpV2ActivateJobTriggerRequest',
        request_type_name='DlpProjectsLocationsJobTriggersActivateRequest',
        response_type_name='GooglePrivacyDlpV2DlpJob',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a job trigger to run DLP actions such as scanning storage for sensitive information on a set schedule. See https://cloud.google.com/dlp/docs/creating-job-triggers to learn more.

      Args:
        request: (DlpProjectsLocationsJobTriggersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2JobTrigger) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobTriggers',
        http_method='POST',
        method_id='dlp.projects.locations.jobTriggers.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/jobTriggers',
        request_field='googlePrivacyDlpV2CreateJobTriggerRequest',
        request_type_name='DlpProjectsLocationsJobTriggersCreateRequest',
        response_type_name='GooglePrivacyDlpV2JobTrigger',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a job trigger. See https://cloud.google.com/dlp/docs/creating-job-triggers to learn more.

      Args:
        request: (DlpProjectsLocationsJobTriggersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobTriggers/{jobTriggersId}',
        http_method='DELETE',
        method_id='dlp.projects.locations.jobTriggers.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsLocationsJobTriggersDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a job trigger. See https://cloud.google.com/dlp/docs/creating-job-triggers to learn more.

      Args:
        request: (DlpProjectsLocationsJobTriggersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2JobTrigger) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobTriggers/{jobTriggersId}',
        http_method='GET',
        method_id='dlp.projects.locations.jobTriggers.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsLocationsJobTriggersGetRequest',
        response_type_name='GooglePrivacyDlpV2JobTrigger',
        supports_download=False,
    )

    def HybridInspect(self, request, global_params=None):
      r"""Inspect hybrid content and store findings to a trigger. The inspection will be processed asynchronously. To review the findings monitor the jobs within the trigger.

      Args:
        request: (DlpProjectsLocationsJobTriggersHybridInspectRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2HybridInspectResponse) The response message.
      """
      config = self.GetMethodConfig('HybridInspect')
      return self._RunMethod(
          config, request, global_params=global_params)

    HybridInspect.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobTriggers/{jobTriggersId}:hybridInspect',
        http_method='POST',
        method_id='dlp.projects.locations.jobTriggers.hybridInspect',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}:hybridInspect',
        request_field='googlePrivacyDlpV2HybridInspectJobTriggerRequest',
        request_type_name='DlpProjectsLocationsJobTriggersHybridInspectRequest',
        response_type_name='GooglePrivacyDlpV2HybridInspectResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists job triggers. See https://cloud.google.com/dlp/docs/creating-job-triggers to learn more.

      Args:
        request: (DlpProjectsLocationsJobTriggersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListJobTriggersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobTriggers',
        http_method='GET',
        method_id='dlp.projects.locations.jobTriggers.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'locationId', 'orderBy', 'pageSize', 'pageToken', 'type'],
        relative_path='v2/{+parent}/jobTriggers',
        request_field='',
        request_type_name='DlpProjectsLocationsJobTriggersListRequest',
        response_type_name='GooglePrivacyDlpV2ListJobTriggersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a job trigger. See https://cloud.google.com/dlp/docs/creating-job-triggers to learn more.

      Args:
        request: (DlpProjectsLocationsJobTriggersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2JobTrigger) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/jobTriggers/{jobTriggersId}',
        http_method='PATCH',
        method_id='dlp.projects.locations.jobTriggers.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='googlePrivacyDlpV2UpdateJobTriggerRequest',
        request_type_name='DlpProjectsLocationsJobTriggersPatchRequest',
        response_type_name='GooglePrivacyDlpV2JobTrigger',
        supports_download=False,
    )

  class ProjectsLocationsStoredInfoTypesService(base_api.BaseApiService):
    """Service class for the projects_locations_storedInfoTypes resource."""

    _NAME = 'projects_locations_storedInfoTypes'

    def __init__(self, client):
      super(DlpV2.ProjectsLocationsStoredInfoTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a pre-built stored infoType to be used for inspection. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpProjectsLocationsStoredInfoTypesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2StoredInfoType) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/storedInfoTypes',
        http_method='POST',
        method_id='dlp.projects.locations.storedInfoTypes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/storedInfoTypes',
        request_field='googlePrivacyDlpV2CreateStoredInfoTypeRequest',
        request_type_name='DlpProjectsLocationsStoredInfoTypesCreateRequest',
        response_type_name='GooglePrivacyDlpV2StoredInfoType',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a stored infoType. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpProjectsLocationsStoredInfoTypesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/storedInfoTypes/{storedInfoTypesId}',
        http_method='DELETE',
        method_id='dlp.projects.locations.storedInfoTypes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsLocationsStoredInfoTypesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a stored infoType. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpProjectsLocationsStoredInfoTypesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2StoredInfoType) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/storedInfoTypes/{storedInfoTypesId}',
        http_method='GET',
        method_id='dlp.projects.locations.storedInfoTypes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsLocationsStoredInfoTypesGetRequest',
        response_type_name='GooglePrivacyDlpV2StoredInfoType',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists stored infoTypes. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpProjectsLocationsStoredInfoTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListStoredInfoTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/storedInfoTypes',
        http_method='GET',
        method_id='dlp.projects.locations.storedInfoTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['locationId', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/storedInfoTypes',
        request_field='',
        request_type_name='DlpProjectsLocationsStoredInfoTypesListRequest',
        response_type_name='GooglePrivacyDlpV2ListStoredInfoTypesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the stored infoType by creating a new version. The existing version will continue to be used until the new version is ready. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpProjectsLocationsStoredInfoTypesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2StoredInfoType) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/locations/{locationsId}/storedInfoTypes/{storedInfoTypesId}',
        http_method='PATCH',
        method_id='dlp.projects.locations.storedInfoTypes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='googlePrivacyDlpV2UpdateStoredInfoTypeRequest',
        request_type_name='DlpProjectsLocationsStoredInfoTypesPatchRequest',
        response_type_name='GooglePrivacyDlpV2StoredInfoType',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(DlpV2.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsStoredInfoTypesService(base_api.BaseApiService):
    """Service class for the projects_storedInfoTypes resource."""

    _NAME = 'projects_storedInfoTypes'

    def __init__(self, client):
      super(DlpV2.ProjectsStoredInfoTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a pre-built stored infoType to be used for inspection. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpProjectsStoredInfoTypesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2StoredInfoType) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/storedInfoTypes',
        http_method='POST',
        method_id='dlp.projects.storedInfoTypes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v2/{+parent}/storedInfoTypes',
        request_field='googlePrivacyDlpV2CreateStoredInfoTypeRequest',
        request_type_name='DlpProjectsStoredInfoTypesCreateRequest',
        response_type_name='GooglePrivacyDlpV2StoredInfoType',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a stored infoType. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpProjectsStoredInfoTypesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/storedInfoTypes/{storedInfoTypesId}',
        http_method='DELETE',
        method_id='dlp.projects.storedInfoTypes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsStoredInfoTypesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a stored infoType. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpProjectsStoredInfoTypesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2StoredInfoType) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/storedInfoTypes/{storedInfoTypesId}',
        http_method='GET',
        method_id='dlp.projects.storedInfoTypes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='',
        request_type_name='DlpProjectsStoredInfoTypesGetRequest',
        response_type_name='GooglePrivacyDlpV2StoredInfoType',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists stored infoTypes. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpProjectsStoredInfoTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2ListStoredInfoTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/storedInfoTypes',
        http_method='GET',
        method_id='dlp.projects.storedInfoTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['locationId', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v2/{+parent}/storedInfoTypes',
        request_field='',
        request_type_name='DlpProjectsStoredInfoTypesListRequest',
        response_type_name='GooglePrivacyDlpV2ListStoredInfoTypesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the stored infoType by creating a new version. The existing version will continue to be used until the new version is ready. See https://cloud.google.com/dlp/docs/creating-stored-infotypes to learn more.

      Args:
        request: (DlpProjectsStoredInfoTypesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GooglePrivacyDlpV2StoredInfoType) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v2/projects/{projectsId}/storedInfoTypes/{storedInfoTypesId}',
        http_method='PATCH',
        method_id='dlp.projects.storedInfoTypes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v2/{+name}',
        request_field='googlePrivacyDlpV2UpdateStoredInfoTypeRequest',
        request_type_name='DlpProjectsStoredInfoTypesPatchRequest',
        response_type_name='GooglePrivacyDlpV2StoredInfoType',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(DlpV2.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

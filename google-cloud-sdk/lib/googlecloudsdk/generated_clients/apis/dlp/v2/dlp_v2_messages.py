"""Generated message classes for dlp version v2.

Provides methods for detection, risk analysis, and de-identification of
privacy-sensitive fragments in text, images, and Google Cloud Platform storage
repositories.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'dlp'


class DlpInfoTypesListRequest(_messages.Message):
  r"""A DlpInfoTypesListRequest object.

  Fields:
    filter: filter to only return infoTypes supported by certain parts of the
      API. Defaults to supported_by=INSPECT.
    languageCode: BCP-47 language code for localized infoType friendly names.
      If omitted, or if localized strings are not available, en-US strings
      will be returned.
    locationId: Deprecated. This field has no effect.
    parent: The parent resource name. The format of this value is as follows:
      locations/ LOCATION_ID
  """

  filter = _messages.StringField(1)
  languageCode = _messages.StringField(2)
  locationId = _messages.StringField(3)
  parent = _messages.StringField(4)


class DlpLocationsInfoTypesListRequest(_messages.Message):
  r"""A DlpLocationsInfoTypesListRequest object.

  Fields:
    filter: filter to only return infoTypes supported by certain parts of the
      API. Defaults to supported_by=INSPECT.
    languageCode: BCP-47 language code for localized infoType friendly names.
      If omitted, or if localized strings are not available, en-US strings
      will be returned.
    locationId: Deprecated. This field has no effect.
    parent: The parent resource name. The format of this value is as follows:
      locations/ LOCATION_ID
  """

  filter = _messages.StringField(1)
  languageCode = _messages.StringField(2)
  locationId = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class DlpOrganizationsDeidentifyTemplatesCreateRequest(_messages.Message):
  r"""A DlpOrganizationsDeidentifyTemplatesCreateRequest object.

  Fields:
    googlePrivacyDlpV2CreateDeidentifyTemplateRequest: A
      GooglePrivacyDlpV2CreateDeidentifyTemplateRequest resource to be passed
      as the request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2CreateDeidentifyTemplateRequest = _messages.MessageField('GooglePrivacyDlpV2CreateDeidentifyTemplateRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpOrganizationsDeidentifyTemplatesDeleteRequest(_messages.Message):
  r"""A DlpOrganizationsDeidentifyTemplatesDeleteRequest object.

  Fields:
    name: Required. Resource name of the organization and deidentify template
      to be deleted, for example
      `organizations/433245324/deidentifyTemplates/*********` or
      projects/project-id/deidentifyTemplates/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpOrganizationsDeidentifyTemplatesGetRequest(_messages.Message):
  r"""A DlpOrganizationsDeidentifyTemplatesGetRequest object.

  Fields:
    name: Required. Resource name of the organization and deidentify template
      to be read, for example
      `organizations/433245324/deidentifyTemplates/*********` or
      projects/project-id/deidentifyTemplates/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpOrganizationsDeidentifyTemplatesListRequest(_messages.Message):
  r"""A DlpOrganizationsDeidentifyTemplatesListRequest object.

  Fields:
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of fields to order by, followed by `asc` or
      `desc` postfix. This list is case-insensitive, default sorting order is
      ascending, redundant space characters are insignificant. Example: `name
      asc,update_time, create_time desc` Supported fields are: -
      `create_time`: corresponds to the time the template was created. -
      `update_time`: corresponds to the time the template was last updated. -
      `name`: corresponds to the template's name. - `display_name`:
      corresponds to the template's display name.
    pageSize: Size of the page, can be limited by the server. If zero server
      returns a page of max size 100.
    pageToken: Page token to continue retrieval. Comes from previous call to
      `ListDeidentifyTemplates`.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  locationId = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DlpOrganizationsDeidentifyTemplatesPatchRequest(_messages.Message):
  r"""A DlpOrganizationsDeidentifyTemplatesPatchRequest object.

  Fields:
    googlePrivacyDlpV2UpdateDeidentifyTemplateRequest: A
      GooglePrivacyDlpV2UpdateDeidentifyTemplateRequest resource to be passed
      as the request body.
    name: Required. Resource name of organization and deidentify template to
      be updated, for example
      `organizations/433245324/deidentifyTemplates/*********` or
      projects/project-id/deidentifyTemplates/*********.
  """

  googlePrivacyDlpV2UpdateDeidentifyTemplateRequest = _messages.MessageField('GooglePrivacyDlpV2UpdateDeidentifyTemplateRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpOrganizationsInspectTemplatesCreateRequest(_messages.Message):
  r"""A DlpOrganizationsInspectTemplatesCreateRequest object.

  Fields:
    googlePrivacyDlpV2CreateInspectTemplateRequest: A
      GooglePrivacyDlpV2CreateInspectTemplateRequest resource to be passed as
      the request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2CreateInspectTemplateRequest = _messages.MessageField('GooglePrivacyDlpV2CreateInspectTemplateRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpOrganizationsInspectTemplatesDeleteRequest(_messages.Message):
  r"""A DlpOrganizationsInspectTemplatesDeleteRequest object.

  Fields:
    name: Required. Resource name of the organization and inspectTemplate to
      be deleted, for example
      `organizations/433245324/inspectTemplates/*********` or
      projects/project-id/inspectTemplates/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpOrganizationsInspectTemplatesGetRequest(_messages.Message):
  r"""A DlpOrganizationsInspectTemplatesGetRequest object.

  Fields:
    name: Required. Resource name of the organization and inspectTemplate to
      be read, for example
      `organizations/433245324/inspectTemplates/*********` or
      projects/project-id/inspectTemplates/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpOrganizationsInspectTemplatesListRequest(_messages.Message):
  r"""A DlpOrganizationsInspectTemplatesListRequest object.

  Fields:
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of fields to order by, followed by `asc` or
      `desc` postfix. This list is case-insensitive, default sorting order is
      ascending, redundant space characters are insignificant. Example: `name
      asc,update_time, create_time desc` Supported fields are: -
      `create_time`: corresponds to the time the template was created. -
      `update_time`: corresponds to the time the template was last updated. -
      `name`: corresponds to the template's name. - `display_name`:
      corresponds to the template's display name.
    pageSize: Size of the page, can be limited by the server. If zero server
      returns a page of max size 100.
    pageToken: Page token to continue retrieval. Comes from previous call to
      `ListInspectTemplates`.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  locationId = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DlpOrganizationsInspectTemplatesPatchRequest(_messages.Message):
  r"""A DlpOrganizationsInspectTemplatesPatchRequest object.

  Fields:
    googlePrivacyDlpV2UpdateInspectTemplateRequest: A
      GooglePrivacyDlpV2UpdateInspectTemplateRequest resource to be passed as
      the request body.
    name: Required. Resource name of organization and inspectTemplate to be
      updated, for example
      `organizations/433245324/inspectTemplates/*********` or
      projects/project-id/inspectTemplates/*********.
  """

  googlePrivacyDlpV2UpdateInspectTemplateRequest = _messages.MessageField('GooglePrivacyDlpV2UpdateInspectTemplateRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpOrganizationsLocationsDeidentifyTemplatesCreateRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsDeidentifyTemplatesCreateRequest object.

  Fields:
    googlePrivacyDlpV2CreateDeidentifyTemplateRequest: A
      GooglePrivacyDlpV2CreateDeidentifyTemplateRequest resource to be passed
      as the request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2CreateDeidentifyTemplateRequest = _messages.MessageField('GooglePrivacyDlpV2CreateDeidentifyTemplateRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpOrganizationsLocationsDeidentifyTemplatesDeleteRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsDeidentifyTemplatesDeleteRequest object.

  Fields:
    name: Required. Resource name of the organization and deidentify template
      to be deleted, for example
      `organizations/433245324/deidentifyTemplates/*********` or
      projects/project-id/deidentifyTemplates/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpOrganizationsLocationsDeidentifyTemplatesGetRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsDeidentifyTemplatesGetRequest object.

  Fields:
    name: Required. Resource name of the organization and deidentify template
      to be read, for example
      `organizations/433245324/deidentifyTemplates/*********` or
      projects/project-id/deidentifyTemplates/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpOrganizationsLocationsDeidentifyTemplatesListRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsDeidentifyTemplatesListRequest object.

  Fields:
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of fields to order by, followed by `asc` or
      `desc` postfix. This list is case-insensitive, default sorting order is
      ascending, redundant space characters are insignificant. Example: `name
      asc,update_time, create_time desc` Supported fields are: -
      `create_time`: corresponds to the time the template was created. -
      `update_time`: corresponds to the time the template was last updated. -
      `name`: corresponds to the template's name. - `display_name`:
      corresponds to the template's display name.
    pageSize: Size of the page, can be limited by the server. If zero server
      returns a page of max size 100.
    pageToken: Page token to continue retrieval. Comes from previous call to
      `ListDeidentifyTemplates`.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  locationId = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DlpOrganizationsLocationsDeidentifyTemplatesPatchRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsDeidentifyTemplatesPatchRequest object.

  Fields:
    googlePrivacyDlpV2UpdateDeidentifyTemplateRequest: A
      GooglePrivacyDlpV2UpdateDeidentifyTemplateRequest resource to be passed
      as the request body.
    name: Required. Resource name of organization and deidentify template to
      be updated, for example
      `organizations/433245324/deidentifyTemplates/*********` or
      projects/project-id/deidentifyTemplates/*********.
  """

  googlePrivacyDlpV2UpdateDeidentifyTemplateRequest = _messages.MessageField('GooglePrivacyDlpV2UpdateDeidentifyTemplateRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpOrganizationsLocationsDlpJobsListRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsDlpJobsListRequest object.

  Enums:
    TypeValueValuesEnum: The type of job. Defaults to `DlpJobType.INSPECT`

  Fields:
    filter: Allows filtering. Supported syntax: * Filter expressions are made
      up of one or more restrictions. * Restrictions can be combined by `AND`
      or `OR` logical operators. A sequence of restrictions implicitly uses
      `AND`. * A restriction has the form of `{field} {operator} {value}`. *
      Supported fields/values for inspect jobs: - `state` -
      PENDING|RUNNING|CANCELED|FINISHED|FAILED - `inspected_storage` -
      DATASTORE|CLOUD_STORAGE|BIGQUERY - `trigger_name` - The name of the
      trigger that created the job. - 'end_time` - Corresponds to the time the
      job finished. - 'start_time` - Corresponds to the time the job finished.
      * Supported fields for risk analysis jobs: - `state` -
      RUNNING|CANCELED|FINISHED|FAILED - 'end_time` - Corresponds to the time
      the job finished. - 'start_time` - Corresponds to the time the job
      finished. * The operator must be `=` or `!=`. Examples: *
      inspected_storage = cloud_storage AND state = done * inspected_storage =
      cloud_storage OR inspected_storage = bigquery * inspected_storage =
      cloud_storage AND (state = done OR state = canceled) * end_time >
      \"2017-12-12T00:00:00+00:00\" The length of this field should be no more
      than 500 characters.
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of fields to order by, followed by `asc` or
      `desc` postfix. This list is case-insensitive, default sorting order is
      ascending, redundant space characters are insignificant. Example: `name
      asc, end_time asc, create_time desc` Supported fields are: -
      `create_time`: corresponds to the time the job was created. -
      `end_time`: corresponds to the time the job ended. - `name`: corresponds
      to the job's name. - `state`: corresponds to `state`
    pageSize: The standard list page size.
    pageToken: The standard list page token.
    parent: Required. Parent resource name. The format of this value varies
      depending on whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
    type: The type of job. Defaults to `DlpJobType.INSPECT`
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of job. Defaults to `DlpJobType.INSPECT`

    Values:
      DLP_JOB_TYPE_UNSPECIFIED: Defaults to INSPECT_JOB.
      INSPECT_JOB: The job inspected Google Cloud for sensitive data.
      RISK_ANALYSIS_JOB: The job executed a Risk Analysis computation.
    """
    DLP_JOB_TYPE_UNSPECIFIED = 0
    INSPECT_JOB = 1
    RISK_ANALYSIS_JOB = 2

  filter = _messages.StringField(1)
  locationId = _messages.StringField(2)
  orderBy = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  parent = _messages.StringField(6, required=True)
  type = _messages.EnumField('TypeValueValuesEnum', 7)


class DlpOrganizationsLocationsInspectTemplatesCreateRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsInspectTemplatesCreateRequest object.

  Fields:
    googlePrivacyDlpV2CreateInspectTemplateRequest: A
      GooglePrivacyDlpV2CreateInspectTemplateRequest resource to be passed as
      the request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2CreateInspectTemplateRequest = _messages.MessageField('GooglePrivacyDlpV2CreateInspectTemplateRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpOrganizationsLocationsInspectTemplatesDeleteRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsInspectTemplatesDeleteRequest object.

  Fields:
    name: Required. Resource name of the organization and inspectTemplate to
      be deleted, for example
      `organizations/433245324/inspectTemplates/*********` or
      projects/project-id/inspectTemplates/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpOrganizationsLocationsInspectTemplatesGetRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsInspectTemplatesGetRequest object.

  Fields:
    name: Required. Resource name of the organization and inspectTemplate to
      be read, for example
      `organizations/433245324/inspectTemplates/*********` or
      projects/project-id/inspectTemplates/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpOrganizationsLocationsInspectTemplatesListRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsInspectTemplatesListRequest object.

  Fields:
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of fields to order by, followed by `asc` or
      `desc` postfix. This list is case-insensitive, default sorting order is
      ascending, redundant space characters are insignificant. Example: `name
      asc,update_time, create_time desc` Supported fields are: -
      `create_time`: corresponds to the time the template was created. -
      `update_time`: corresponds to the time the template was last updated. -
      `name`: corresponds to the template's name. - `display_name`:
      corresponds to the template's display name.
    pageSize: Size of the page, can be limited by the server. If zero server
      returns a page of max size 100.
    pageToken: Page token to continue retrieval. Comes from previous call to
      `ListInspectTemplates`.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  locationId = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DlpOrganizationsLocationsInspectTemplatesPatchRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsInspectTemplatesPatchRequest object.

  Fields:
    googlePrivacyDlpV2UpdateInspectTemplateRequest: A
      GooglePrivacyDlpV2UpdateInspectTemplateRequest resource to be passed as
      the request body.
    name: Required. Resource name of organization and inspectTemplate to be
      updated, for example
      `organizations/433245324/inspectTemplates/*********` or
      projects/project-id/inspectTemplates/*********.
  """

  googlePrivacyDlpV2UpdateInspectTemplateRequest = _messages.MessageField('GooglePrivacyDlpV2UpdateInspectTemplateRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpOrganizationsLocationsJobTriggersCreateRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsJobTriggersCreateRequest object.

  Fields:
    googlePrivacyDlpV2CreateJobTriggerRequest: A
      GooglePrivacyDlpV2CreateJobTriggerRequest resource to be passed as the
      request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2CreateJobTriggerRequest = _messages.MessageField('GooglePrivacyDlpV2CreateJobTriggerRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpOrganizationsLocationsJobTriggersDeleteRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsJobTriggersDeleteRequest object.

  Fields:
    name: Required. Resource name of the project and the triggeredJob, for
      example `projects/dlp-test-project/jobTriggers/53234423`.
  """

  name = _messages.StringField(1, required=True)


class DlpOrganizationsLocationsJobTriggersGetRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsJobTriggersGetRequest object.

  Fields:
    name: Required. Resource name of the project and the triggeredJob, for
      example `projects/dlp-test-project/jobTriggers/53234423`.
  """

  name = _messages.StringField(1, required=True)


class DlpOrganizationsLocationsJobTriggersListRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsJobTriggersListRequest object.

  Enums:
    TypeValueValuesEnum: The type of jobs. Will use `DlpJobType.INSPECT` if
      not set.

  Fields:
    filter: Allows filtering. Supported syntax: * Filter expressions are made
      up of one or more restrictions. * Restrictions can be combined by `AND`
      or `OR` logical operators. A sequence of restrictions implicitly uses
      `AND`. * A restriction has the form of `{field} {operator} {value}`. *
      Supported fields/values for inspect triggers: - `status` -
      HEALTHY|PAUSED|CANCELLED - `inspected_storage` -
      DATASTORE|CLOUD_STORAGE|BIGQUERY - 'last_run_time` - RFC 3339 formatted
      timestamp, surrounded by quotation marks. Nanoseconds are ignored. -
      'error_count' - Number of errors that have occurred while running. * The
      operator must be `=` or `!=` for status and inspected_storage. Examples:
      * inspected_storage = cloud_storage AND status = HEALTHY *
      inspected_storage = cloud_storage OR inspected_storage = bigquery *
      inspected_storage = cloud_storage AND (state = PAUSED OR state =
      HEALTHY) * last_run_time > \"2017-12-12T00:00:00+00:00\" The length of
      this field should be no more than 500 characters.
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of triggeredJob fields to order by, followed
      by `asc` or `desc` postfix. This list is case-insensitive, default
      sorting order is ascending, redundant space characters are
      insignificant. Example: `name asc,update_time, create_time desc`
      Supported fields are: - `create_time`: corresponds to the time the
      JobTrigger was created. - `update_time`: corresponds to the time the
      JobTrigger was last updated. - `last_run_time`: corresponds to the last
      time the JobTrigger ran. - `name`: corresponds to the JobTrigger's name.
      - `display_name`: corresponds to the JobTrigger's display name. -
      `status`: corresponds to JobTrigger's status.
    pageSize: Size of the page, can be limited by a server.
    pageToken: Page token to continue retrieval. Comes from previous call to
      ListJobTriggers. `order_by` field must not change for subsequent calls.
    parent: Required. Parent resource name. The format of this value varies
      depending on whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
    type: The type of jobs. Will use `DlpJobType.INSPECT` if not set.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of jobs. Will use `DlpJobType.INSPECT` if not set.

    Values:
      DLP_JOB_TYPE_UNSPECIFIED: Defaults to INSPECT_JOB.
      INSPECT_JOB: The job inspected Google Cloud for sensitive data.
      RISK_ANALYSIS_JOB: The job executed a Risk Analysis computation.
    """
    DLP_JOB_TYPE_UNSPECIFIED = 0
    INSPECT_JOB = 1
    RISK_ANALYSIS_JOB = 2

  filter = _messages.StringField(1)
  locationId = _messages.StringField(2)
  orderBy = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  parent = _messages.StringField(6, required=True)
  type = _messages.EnumField('TypeValueValuesEnum', 7)


class DlpOrganizationsLocationsJobTriggersPatchRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsJobTriggersPatchRequest object.

  Fields:
    googlePrivacyDlpV2UpdateJobTriggerRequest: A
      GooglePrivacyDlpV2UpdateJobTriggerRequest resource to be passed as the
      request body.
    name: Required. Resource name of the project and the triggeredJob, for
      example `projects/dlp-test-project/jobTriggers/53234423`.
  """

  googlePrivacyDlpV2UpdateJobTriggerRequest = _messages.MessageField('GooglePrivacyDlpV2UpdateJobTriggerRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpOrganizationsLocationsStoredInfoTypesCreateRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsStoredInfoTypesCreateRequest object.

  Fields:
    googlePrivacyDlpV2CreateStoredInfoTypeRequest: A
      GooglePrivacyDlpV2CreateStoredInfoTypeRequest resource to be passed as
      the request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2CreateStoredInfoTypeRequest = _messages.MessageField('GooglePrivacyDlpV2CreateStoredInfoTypeRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpOrganizationsLocationsStoredInfoTypesDeleteRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsStoredInfoTypesDeleteRequest object.

  Fields:
    name: Required. Resource name of the organization and storedInfoType to be
      deleted, for example `organizations/433245324/storedInfoTypes/*********`
      or projects/project-id/storedInfoTypes/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpOrganizationsLocationsStoredInfoTypesGetRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsStoredInfoTypesGetRequest object.

  Fields:
    name: Required. Resource name of the organization and storedInfoType to be
      read, for example `organizations/433245324/storedInfoTypes/*********` or
      projects/project-id/storedInfoTypes/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpOrganizationsLocationsStoredInfoTypesListRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsStoredInfoTypesListRequest object.

  Fields:
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of fields to order by, followed by `asc` or
      `desc` postfix. This list is case-insensitive, default sorting order is
      ascending, redundant space characters are insignificant. Example: `name
      asc, display_name, create_time desc` Supported fields are: -
      `create_time`: corresponds to the time the most recent version of the
      resource was created. - `state`: corresponds to the state of the
      resource. - `name`: corresponds to resource name. - `display_name`:
      corresponds to info type's display name.
    pageSize: Size of the page, can be limited by the server. If zero server
      returns a page of max size 100.
    pageToken: Page token to continue retrieval. Comes from previous call to
      `ListStoredInfoTypes`.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  locationId = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DlpOrganizationsLocationsStoredInfoTypesPatchRequest(_messages.Message):
  r"""A DlpOrganizationsLocationsStoredInfoTypesPatchRequest object.

  Fields:
    googlePrivacyDlpV2UpdateStoredInfoTypeRequest: A
      GooglePrivacyDlpV2UpdateStoredInfoTypeRequest resource to be passed as
      the request body.
    name: Required. Resource name of organization and storedInfoType to be
      updated, for example `organizations/433245324/storedInfoTypes/*********`
      or projects/project-id/storedInfoTypes/*********.
  """

  googlePrivacyDlpV2UpdateStoredInfoTypeRequest = _messages.MessageField('GooglePrivacyDlpV2UpdateStoredInfoTypeRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpOrganizationsStoredInfoTypesCreateRequest(_messages.Message):
  r"""A DlpOrganizationsStoredInfoTypesCreateRequest object.

  Fields:
    googlePrivacyDlpV2CreateStoredInfoTypeRequest: A
      GooglePrivacyDlpV2CreateStoredInfoTypeRequest resource to be passed as
      the request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2CreateStoredInfoTypeRequest = _messages.MessageField('GooglePrivacyDlpV2CreateStoredInfoTypeRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpOrganizationsStoredInfoTypesDeleteRequest(_messages.Message):
  r"""A DlpOrganizationsStoredInfoTypesDeleteRequest object.

  Fields:
    name: Required. Resource name of the organization and storedInfoType to be
      deleted, for example `organizations/433245324/storedInfoTypes/*********`
      or projects/project-id/storedInfoTypes/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpOrganizationsStoredInfoTypesGetRequest(_messages.Message):
  r"""A DlpOrganizationsStoredInfoTypesGetRequest object.

  Fields:
    name: Required. Resource name of the organization and storedInfoType to be
      read, for example `organizations/433245324/storedInfoTypes/*********` or
      projects/project-id/storedInfoTypes/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpOrganizationsStoredInfoTypesListRequest(_messages.Message):
  r"""A DlpOrganizationsStoredInfoTypesListRequest object.

  Fields:
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of fields to order by, followed by `asc` or
      `desc` postfix. This list is case-insensitive, default sorting order is
      ascending, redundant space characters are insignificant. Example: `name
      asc, display_name, create_time desc` Supported fields are: -
      `create_time`: corresponds to the time the most recent version of the
      resource was created. - `state`: corresponds to the state of the
      resource. - `name`: corresponds to resource name. - `display_name`:
      corresponds to info type's display name.
    pageSize: Size of the page, can be limited by the server. If zero server
      returns a page of max size 100.
    pageToken: Page token to continue retrieval. Comes from previous call to
      `ListStoredInfoTypes`.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  locationId = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DlpOrganizationsStoredInfoTypesPatchRequest(_messages.Message):
  r"""A DlpOrganizationsStoredInfoTypesPatchRequest object.

  Fields:
    googlePrivacyDlpV2UpdateStoredInfoTypeRequest: A
      GooglePrivacyDlpV2UpdateStoredInfoTypeRequest resource to be passed as
      the request body.
    name: Required. Resource name of organization and storedInfoType to be
      updated, for example `organizations/433245324/storedInfoTypes/*********`
      or projects/project-id/storedInfoTypes/*********.
  """

  googlePrivacyDlpV2UpdateStoredInfoTypeRequest = _messages.MessageField('GooglePrivacyDlpV2UpdateStoredInfoTypeRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpProjectsContentDeidentifyRequest(_messages.Message):
  r"""A DlpProjectsContentDeidentifyRequest object.

  Fields:
    googlePrivacyDlpV2DeidentifyContentRequest: A
      GooglePrivacyDlpV2DeidentifyContentRequest resource to be passed as the
      request body.
    parent: Parent resource name. The format of this value varies depending on
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2DeidentifyContentRequest = _messages.MessageField('GooglePrivacyDlpV2DeidentifyContentRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsContentInspectRequest(_messages.Message):
  r"""A DlpProjectsContentInspectRequest object.

  Fields:
    googlePrivacyDlpV2InspectContentRequest: A
      GooglePrivacyDlpV2InspectContentRequest resource to be passed as the
      request body.
    parent: Parent resource name. The format of this value varies depending on
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2InspectContentRequest = _messages.MessageField('GooglePrivacyDlpV2InspectContentRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsContentReidentifyRequest(_messages.Message):
  r"""A DlpProjectsContentReidentifyRequest object.

  Fields:
    googlePrivacyDlpV2ReidentifyContentRequest: A
      GooglePrivacyDlpV2ReidentifyContentRequest resource to be passed as the
      request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2ReidentifyContentRequest = _messages.MessageField('GooglePrivacyDlpV2ReidentifyContentRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsDeidentifyTemplatesCreateRequest(_messages.Message):
  r"""A DlpProjectsDeidentifyTemplatesCreateRequest object.

  Fields:
    googlePrivacyDlpV2CreateDeidentifyTemplateRequest: A
      GooglePrivacyDlpV2CreateDeidentifyTemplateRequest resource to be passed
      as the request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2CreateDeidentifyTemplateRequest = _messages.MessageField('GooglePrivacyDlpV2CreateDeidentifyTemplateRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsDeidentifyTemplatesDeleteRequest(_messages.Message):
  r"""A DlpProjectsDeidentifyTemplatesDeleteRequest object.

  Fields:
    name: Required. Resource name of the organization and deidentify template
      to be deleted, for example
      `organizations/433245324/deidentifyTemplates/*********` or
      projects/project-id/deidentifyTemplates/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsDeidentifyTemplatesGetRequest(_messages.Message):
  r"""A DlpProjectsDeidentifyTemplatesGetRequest object.

  Fields:
    name: Required. Resource name of the organization and deidentify template
      to be read, for example
      `organizations/433245324/deidentifyTemplates/*********` or
      projects/project-id/deidentifyTemplates/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsDeidentifyTemplatesListRequest(_messages.Message):
  r"""A DlpProjectsDeidentifyTemplatesListRequest object.

  Fields:
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of fields to order by, followed by `asc` or
      `desc` postfix. This list is case-insensitive, default sorting order is
      ascending, redundant space characters are insignificant. Example: `name
      asc,update_time, create_time desc` Supported fields are: -
      `create_time`: corresponds to the time the template was created. -
      `update_time`: corresponds to the time the template was last updated. -
      `name`: corresponds to the template's name. - `display_name`:
      corresponds to the template's display name.
    pageSize: Size of the page, can be limited by the server. If zero server
      returns a page of max size 100.
    pageToken: Page token to continue retrieval. Comes from previous call to
      `ListDeidentifyTemplates`.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  locationId = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DlpProjectsDeidentifyTemplatesPatchRequest(_messages.Message):
  r"""A DlpProjectsDeidentifyTemplatesPatchRequest object.

  Fields:
    googlePrivacyDlpV2UpdateDeidentifyTemplateRequest: A
      GooglePrivacyDlpV2UpdateDeidentifyTemplateRequest resource to be passed
      as the request body.
    name: Required. Resource name of organization and deidentify template to
      be updated, for example
      `organizations/433245324/deidentifyTemplates/*********` or
      projects/project-id/deidentifyTemplates/*********.
  """

  googlePrivacyDlpV2UpdateDeidentifyTemplateRequest = _messages.MessageField('GooglePrivacyDlpV2UpdateDeidentifyTemplateRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpProjectsDlpJobsCancelRequest(_messages.Message):
  r"""A DlpProjectsDlpJobsCancelRequest object.

  Fields:
    googlePrivacyDlpV2CancelDlpJobRequest: A
      GooglePrivacyDlpV2CancelDlpJobRequest resource to be passed as the
      request body.
    name: Required. The name of the DlpJob resource to be cancelled.
  """

  googlePrivacyDlpV2CancelDlpJobRequest = _messages.MessageField('GooglePrivacyDlpV2CancelDlpJobRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpProjectsDlpJobsCreateRequest(_messages.Message):
  r"""A DlpProjectsDlpJobsCreateRequest object.

  Fields:
    googlePrivacyDlpV2CreateDlpJobRequest: A
      GooglePrivacyDlpV2CreateDlpJobRequest resource to be passed as the
      request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2CreateDlpJobRequest = _messages.MessageField('GooglePrivacyDlpV2CreateDlpJobRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsDlpJobsDeleteRequest(_messages.Message):
  r"""A DlpProjectsDlpJobsDeleteRequest object.

  Fields:
    name: Required. The name of the DlpJob resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsDlpJobsGetRequest(_messages.Message):
  r"""A DlpProjectsDlpJobsGetRequest object.

  Fields:
    name: Required. The name of the DlpJob resource.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsDlpJobsListRequest(_messages.Message):
  r"""A DlpProjectsDlpJobsListRequest object.

  Enums:
    TypeValueValuesEnum: The type of job. Defaults to `DlpJobType.INSPECT`

  Fields:
    filter: Allows filtering. Supported syntax: * Filter expressions are made
      up of one or more restrictions. * Restrictions can be combined by `AND`
      or `OR` logical operators. A sequence of restrictions implicitly uses
      `AND`. * A restriction has the form of `{field} {operator} {value}`. *
      Supported fields/values for inspect jobs: - `state` -
      PENDING|RUNNING|CANCELED|FINISHED|FAILED - `inspected_storage` -
      DATASTORE|CLOUD_STORAGE|BIGQUERY - `trigger_name` - The name of the
      trigger that created the job. - 'end_time` - Corresponds to the time the
      job finished. - 'start_time` - Corresponds to the time the job finished.
      * Supported fields for risk analysis jobs: - `state` -
      RUNNING|CANCELED|FINISHED|FAILED - 'end_time` - Corresponds to the time
      the job finished. - 'start_time` - Corresponds to the time the job
      finished. * The operator must be `=` or `!=`. Examples: *
      inspected_storage = cloud_storage AND state = done * inspected_storage =
      cloud_storage OR inspected_storage = bigquery * inspected_storage =
      cloud_storage AND (state = done OR state = canceled) * end_time >
      \"2017-12-12T00:00:00+00:00\" The length of this field should be no more
      than 500 characters.
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of fields to order by, followed by `asc` or
      `desc` postfix. This list is case-insensitive, default sorting order is
      ascending, redundant space characters are insignificant. Example: `name
      asc, end_time asc, create_time desc` Supported fields are: -
      `create_time`: corresponds to the time the job was created. -
      `end_time`: corresponds to the time the job ended. - `name`: corresponds
      to the job's name. - `state`: corresponds to `state`
    pageSize: The standard list page size.
    pageToken: The standard list page token.
    parent: Required. Parent resource name. The format of this value varies
      depending on whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
    type: The type of job. Defaults to `DlpJobType.INSPECT`
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of job. Defaults to `DlpJobType.INSPECT`

    Values:
      DLP_JOB_TYPE_UNSPECIFIED: Defaults to INSPECT_JOB.
      INSPECT_JOB: The job inspected Google Cloud for sensitive data.
      RISK_ANALYSIS_JOB: The job executed a Risk Analysis computation.
    """
    DLP_JOB_TYPE_UNSPECIFIED = 0
    INSPECT_JOB = 1
    RISK_ANALYSIS_JOB = 2

  filter = _messages.StringField(1)
  locationId = _messages.StringField(2)
  orderBy = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  parent = _messages.StringField(6, required=True)
  type = _messages.EnumField('TypeValueValuesEnum', 7)


class DlpProjectsImageRedactRequest(_messages.Message):
  r"""A DlpProjectsImageRedactRequest object.

  Fields:
    googlePrivacyDlpV2RedactImageRequest: A
      GooglePrivacyDlpV2RedactImageRequest resource to be passed as the
      request body.
    parent: Parent resource name. The format of this value varies depending on
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2RedactImageRequest = _messages.MessageField('GooglePrivacyDlpV2RedactImageRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsInspectTemplatesCreateRequest(_messages.Message):
  r"""A DlpProjectsInspectTemplatesCreateRequest object.

  Fields:
    googlePrivacyDlpV2CreateInspectTemplateRequest: A
      GooglePrivacyDlpV2CreateInspectTemplateRequest resource to be passed as
      the request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2CreateInspectTemplateRequest = _messages.MessageField('GooglePrivacyDlpV2CreateInspectTemplateRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsInspectTemplatesDeleteRequest(_messages.Message):
  r"""A DlpProjectsInspectTemplatesDeleteRequest object.

  Fields:
    name: Required. Resource name of the organization and inspectTemplate to
      be deleted, for example
      `organizations/433245324/inspectTemplates/*********` or
      projects/project-id/inspectTemplates/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsInspectTemplatesGetRequest(_messages.Message):
  r"""A DlpProjectsInspectTemplatesGetRequest object.

  Fields:
    name: Required. Resource name of the organization and inspectTemplate to
      be read, for example
      `organizations/433245324/inspectTemplates/*********` or
      projects/project-id/inspectTemplates/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsInspectTemplatesListRequest(_messages.Message):
  r"""A DlpProjectsInspectTemplatesListRequest object.

  Fields:
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of fields to order by, followed by `asc` or
      `desc` postfix. This list is case-insensitive, default sorting order is
      ascending, redundant space characters are insignificant. Example: `name
      asc,update_time, create_time desc` Supported fields are: -
      `create_time`: corresponds to the time the template was created. -
      `update_time`: corresponds to the time the template was last updated. -
      `name`: corresponds to the template's name. - `display_name`:
      corresponds to the template's display name.
    pageSize: Size of the page, can be limited by the server. If zero server
      returns a page of max size 100.
    pageToken: Page token to continue retrieval. Comes from previous call to
      `ListInspectTemplates`.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  locationId = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DlpProjectsInspectTemplatesPatchRequest(_messages.Message):
  r"""A DlpProjectsInspectTemplatesPatchRequest object.

  Fields:
    googlePrivacyDlpV2UpdateInspectTemplateRequest: A
      GooglePrivacyDlpV2UpdateInspectTemplateRequest resource to be passed as
      the request body.
    name: Required. Resource name of organization and inspectTemplate to be
      updated, for example
      `organizations/433245324/inspectTemplates/*********` or
      projects/project-id/inspectTemplates/*********.
  """

  googlePrivacyDlpV2UpdateInspectTemplateRequest = _messages.MessageField('GooglePrivacyDlpV2UpdateInspectTemplateRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpProjectsJobTriggersActivateRequest(_messages.Message):
  r"""A DlpProjectsJobTriggersActivateRequest object.

  Fields:
    googlePrivacyDlpV2ActivateJobTriggerRequest: A
      GooglePrivacyDlpV2ActivateJobTriggerRequest resource to be passed as the
      request body.
    name: Required. Resource name of the trigger to activate, for example
      `projects/dlp-test-project/jobTriggers/53234423`.
  """

  googlePrivacyDlpV2ActivateJobTriggerRequest = _messages.MessageField('GooglePrivacyDlpV2ActivateJobTriggerRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpProjectsJobTriggersCreateRequest(_messages.Message):
  r"""A DlpProjectsJobTriggersCreateRequest object.

  Fields:
    googlePrivacyDlpV2CreateJobTriggerRequest: A
      GooglePrivacyDlpV2CreateJobTriggerRequest resource to be passed as the
      request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2CreateJobTriggerRequest = _messages.MessageField('GooglePrivacyDlpV2CreateJobTriggerRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsJobTriggersDeleteRequest(_messages.Message):
  r"""A DlpProjectsJobTriggersDeleteRequest object.

  Fields:
    name: Required. Resource name of the project and the triggeredJob, for
      example `projects/dlp-test-project/jobTriggers/53234423`.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsJobTriggersGetRequest(_messages.Message):
  r"""A DlpProjectsJobTriggersGetRequest object.

  Fields:
    name: Required. Resource name of the project and the triggeredJob, for
      example `projects/dlp-test-project/jobTriggers/53234423`.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsJobTriggersListRequest(_messages.Message):
  r"""A DlpProjectsJobTriggersListRequest object.

  Enums:
    TypeValueValuesEnum: The type of jobs. Will use `DlpJobType.INSPECT` if
      not set.

  Fields:
    filter: Allows filtering. Supported syntax: * Filter expressions are made
      up of one or more restrictions. * Restrictions can be combined by `AND`
      or `OR` logical operators. A sequence of restrictions implicitly uses
      `AND`. * A restriction has the form of `{field} {operator} {value}`. *
      Supported fields/values for inspect triggers: - `status` -
      HEALTHY|PAUSED|CANCELLED - `inspected_storage` -
      DATASTORE|CLOUD_STORAGE|BIGQUERY - 'last_run_time` - RFC 3339 formatted
      timestamp, surrounded by quotation marks. Nanoseconds are ignored. -
      'error_count' - Number of errors that have occurred while running. * The
      operator must be `=` or `!=` for status and inspected_storage. Examples:
      * inspected_storage = cloud_storage AND status = HEALTHY *
      inspected_storage = cloud_storage OR inspected_storage = bigquery *
      inspected_storage = cloud_storage AND (state = PAUSED OR state =
      HEALTHY) * last_run_time > \"2017-12-12T00:00:00+00:00\" The length of
      this field should be no more than 500 characters.
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of triggeredJob fields to order by, followed
      by `asc` or `desc` postfix. This list is case-insensitive, default
      sorting order is ascending, redundant space characters are
      insignificant. Example: `name asc,update_time, create_time desc`
      Supported fields are: - `create_time`: corresponds to the time the
      JobTrigger was created. - `update_time`: corresponds to the time the
      JobTrigger was last updated. - `last_run_time`: corresponds to the last
      time the JobTrigger ran. - `name`: corresponds to the JobTrigger's name.
      - `display_name`: corresponds to the JobTrigger's display name. -
      `status`: corresponds to JobTrigger's status.
    pageSize: Size of the page, can be limited by a server.
    pageToken: Page token to continue retrieval. Comes from previous call to
      ListJobTriggers. `order_by` field must not change for subsequent calls.
    parent: Required. Parent resource name. The format of this value varies
      depending on whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
    type: The type of jobs. Will use `DlpJobType.INSPECT` if not set.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of jobs. Will use `DlpJobType.INSPECT` if not set.

    Values:
      DLP_JOB_TYPE_UNSPECIFIED: Defaults to INSPECT_JOB.
      INSPECT_JOB: The job inspected Google Cloud for sensitive data.
      RISK_ANALYSIS_JOB: The job executed a Risk Analysis computation.
    """
    DLP_JOB_TYPE_UNSPECIFIED = 0
    INSPECT_JOB = 1
    RISK_ANALYSIS_JOB = 2

  filter = _messages.StringField(1)
  locationId = _messages.StringField(2)
  orderBy = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  parent = _messages.StringField(6, required=True)
  type = _messages.EnumField('TypeValueValuesEnum', 7)


class DlpProjectsJobTriggersPatchRequest(_messages.Message):
  r"""A DlpProjectsJobTriggersPatchRequest object.

  Fields:
    googlePrivacyDlpV2UpdateJobTriggerRequest: A
      GooglePrivacyDlpV2UpdateJobTriggerRequest resource to be passed as the
      request body.
    name: Required. Resource name of the project and the triggeredJob, for
      example `projects/dlp-test-project/jobTriggers/53234423`.
  """

  googlePrivacyDlpV2UpdateJobTriggerRequest = _messages.MessageField('GooglePrivacyDlpV2UpdateJobTriggerRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpProjectsLocationsContentDeidentifyRequest(_messages.Message):
  r"""A DlpProjectsLocationsContentDeidentifyRequest object.

  Fields:
    googlePrivacyDlpV2DeidentifyContentRequest: A
      GooglePrivacyDlpV2DeidentifyContentRequest resource to be passed as the
      request body.
    parent: Parent resource name. The format of this value varies depending on
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2DeidentifyContentRequest = _messages.MessageField('GooglePrivacyDlpV2DeidentifyContentRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsLocationsContentInspectRequest(_messages.Message):
  r"""A DlpProjectsLocationsContentInspectRequest object.

  Fields:
    googlePrivacyDlpV2InspectContentRequest: A
      GooglePrivacyDlpV2InspectContentRequest resource to be passed as the
      request body.
    parent: Parent resource name. The format of this value varies depending on
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2InspectContentRequest = _messages.MessageField('GooglePrivacyDlpV2InspectContentRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsLocationsContentReidentifyRequest(_messages.Message):
  r"""A DlpProjectsLocationsContentReidentifyRequest object.

  Fields:
    googlePrivacyDlpV2ReidentifyContentRequest: A
      GooglePrivacyDlpV2ReidentifyContentRequest resource to be passed as the
      request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2ReidentifyContentRequest = _messages.MessageField('GooglePrivacyDlpV2ReidentifyContentRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsLocationsDeidentifyTemplatesCreateRequest(_messages.Message):
  r"""A DlpProjectsLocationsDeidentifyTemplatesCreateRequest object.

  Fields:
    googlePrivacyDlpV2CreateDeidentifyTemplateRequest: A
      GooglePrivacyDlpV2CreateDeidentifyTemplateRequest resource to be passed
      as the request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2CreateDeidentifyTemplateRequest = _messages.MessageField('GooglePrivacyDlpV2CreateDeidentifyTemplateRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsLocationsDeidentifyTemplatesDeleteRequest(_messages.Message):
  r"""A DlpProjectsLocationsDeidentifyTemplatesDeleteRequest object.

  Fields:
    name: Required. Resource name of the organization and deidentify template
      to be deleted, for example
      `organizations/433245324/deidentifyTemplates/*********` or
      projects/project-id/deidentifyTemplates/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsLocationsDeidentifyTemplatesGetRequest(_messages.Message):
  r"""A DlpProjectsLocationsDeidentifyTemplatesGetRequest object.

  Fields:
    name: Required. Resource name of the organization and deidentify template
      to be read, for example
      `organizations/433245324/deidentifyTemplates/*********` or
      projects/project-id/deidentifyTemplates/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsLocationsDeidentifyTemplatesListRequest(_messages.Message):
  r"""A DlpProjectsLocationsDeidentifyTemplatesListRequest object.

  Fields:
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of fields to order by, followed by `asc` or
      `desc` postfix. This list is case-insensitive, default sorting order is
      ascending, redundant space characters are insignificant. Example: `name
      asc,update_time, create_time desc` Supported fields are: -
      `create_time`: corresponds to the time the template was created. -
      `update_time`: corresponds to the time the template was last updated. -
      `name`: corresponds to the template's name. - `display_name`:
      corresponds to the template's display name.
    pageSize: Size of the page, can be limited by the server. If zero server
      returns a page of max size 100.
    pageToken: Page token to continue retrieval. Comes from previous call to
      `ListDeidentifyTemplates`.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  locationId = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DlpProjectsLocationsDeidentifyTemplatesPatchRequest(_messages.Message):
  r"""A DlpProjectsLocationsDeidentifyTemplatesPatchRequest object.

  Fields:
    googlePrivacyDlpV2UpdateDeidentifyTemplateRequest: A
      GooglePrivacyDlpV2UpdateDeidentifyTemplateRequest resource to be passed
      as the request body.
    name: Required. Resource name of organization and deidentify template to
      be updated, for example
      `organizations/433245324/deidentifyTemplates/*********` or
      projects/project-id/deidentifyTemplates/*********.
  """

  googlePrivacyDlpV2UpdateDeidentifyTemplateRequest = _messages.MessageField('GooglePrivacyDlpV2UpdateDeidentifyTemplateRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpProjectsLocationsDlpJobsCancelRequest(_messages.Message):
  r"""A DlpProjectsLocationsDlpJobsCancelRequest object.

  Fields:
    googlePrivacyDlpV2CancelDlpJobRequest: A
      GooglePrivacyDlpV2CancelDlpJobRequest resource to be passed as the
      request body.
    name: Required. The name of the DlpJob resource to be cancelled.
  """

  googlePrivacyDlpV2CancelDlpJobRequest = _messages.MessageField('GooglePrivacyDlpV2CancelDlpJobRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpProjectsLocationsDlpJobsCreateRequest(_messages.Message):
  r"""A DlpProjectsLocationsDlpJobsCreateRequest object.

  Fields:
    googlePrivacyDlpV2CreateDlpJobRequest: A
      GooglePrivacyDlpV2CreateDlpJobRequest resource to be passed as the
      request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2CreateDlpJobRequest = _messages.MessageField('GooglePrivacyDlpV2CreateDlpJobRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsLocationsDlpJobsDeleteRequest(_messages.Message):
  r"""A DlpProjectsLocationsDlpJobsDeleteRequest object.

  Fields:
    name: Required. The name of the DlpJob resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsLocationsDlpJobsFinishRequest(_messages.Message):
  r"""A DlpProjectsLocationsDlpJobsFinishRequest object.

  Fields:
    googlePrivacyDlpV2FinishDlpJobRequest: A
      GooglePrivacyDlpV2FinishDlpJobRequest resource to be passed as the
      request body.
    name: Required. The name of the DlpJob resource to be cancelled.
  """

  googlePrivacyDlpV2FinishDlpJobRequest = _messages.MessageField('GooglePrivacyDlpV2FinishDlpJobRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpProjectsLocationsDlpJobsGetRequest(_messages.Message):
  r"""A DlpProjectsLocationsDlpJobsGetRequest object.

  Fields:
    name: Required. The name of the DlpJob resource.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsLocationsDlpJobsHybridInspectRequest(_messages.Message):
  r"""A DlpProjectsLocationsDlpJobsHybridInspectRequest object.

  Fields:
    googlePrivacyDlpV2HybridInspectDlpJobRequest: A
      GooglePrivacyDlpV2HybridInspectDlpJobRequest resource to be passed as
      the request body.
    name: Required. Resource name of the job to execute a hybrid inspect on,
      for example `projects/dlp-test-project/dlpJob/53234423`.
  """

  googlePrivacyDlpV2HybridInspectDlpJobRequest = _messages.MessageField('GooglePrivacyDlpV2HybridInspectDlpJobRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpProjectsLocationsDlpJobsListRequest(_messages.Message):
  r"""A DlpProjectsLocationsDlpJobsListRequest object.

  Enums:
    TypeValueValuesEnum: The type of job. Defaults to `DlpJobType.INSPECT`

  Fields:
    filter: Allows filtering. Supported syntax: * Filter expressions are made
      up of one or more restrictions. * Restrictions can be combined by `AND`
      or `OR` logical operators. A sequence of restrictions implicitly uses
      `AND`. * A restriction has the form of `{field} {operator} {value}`. *
      Supported fields/values for inspect jobs: - `state` -
      PENDING|RUNNING|CANCELED|FINISHED|FAILED - `inspected_storage` -
      DATASTORE|CLOUD_STORAGE|BIGQUERY - `trigger_name` - The name of the
      trigger that created the job. - 'end_time` - Corresponds to the time the
      job finished. - 'start_time` - Corresponds to the time the job finished.
      * Supported fields for risk analysis jobs: - `state` -
      RUNNING|CANCELED|FINISHED|FAILED - 'end_time` - Corresponds to the time
      the job finished. - 'start_time` - Corresponds to the time the job
      finished. * The operator must be `=` or `!=`. Examples: *
      inspected_storage = cloud_storage AND state = done * inspected_storage =
      cloud_storage OR inspected_storage = bigquery * inspected_storage =
      cloud_storage AND (state = done OR state = canceled) * end_time >
      \"2017-12-12T00:00:00+00:00\" The length of this field should be no more
      than 500 characters.
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of fields to order by, followed by `asc` or
      `desc` postfix. This list is case-insensitive, default sorting order is
      ascending, redundant space characters are insignificant. Example: `name
      asc, end_time asc, create_time desc` Supported fields are: -
      `create_time`: corresponds to the time the job was created. -
      `end_time`: corresponds to the time the job ended. - `name`: corresponds
      to the job's name. - `state`: corresponds to `state`
    pageSize: The standard list page size.
    pageToken: The standard list page token.
    parent: Required. Parent resource name. The format of this value varies
      depending on whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
    type: The type of job. Defaults to `DlpJobType.INSPECT`
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of job. Defaults to `DlpJobType.INSPECT`

    Values:
      DLP_JOB_TYPE_UNSPECIFIED: Defaults to INSPECT_JOB.
      INSPECT_JOB: The job inspected Google Cloud for sensitive data.
      RISK_ANALYSIS_JOB: The job executed a Risk Analysis computation.
    """
    DLP_JOB_TYPE_UNSPECIFIED = 0
    INSPECT_JOB = 1
    RISK_ANALYSIS_JOB = 2

  filter = _messages.StringField(1)
  locationId = _messages.StringField(2)
  orderBy = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  parent = _messages.StringField(6, required=True)
  type = _messages.EnumField('TypeValueValuesEnum', 7)


class DlpProjectsLocationsImageRedactRequest(_messages.Message):
  r"""A DlpProjectsLocationsImageRedactRequest object.

  Fields:
    googlePrivacyDlpV2RedactImageRequest: A
      GooglePrivacyDlpV2RedactImageRequest resource to be passed as the
      request body.
    parent: Parent resource name. The format of this value varies depending on
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2RedactImageRequest = _messages.MessageField('GooglePrivacyDlpV2RedactImageRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsLocationsInspectTemplatesCreateRequest(_messages.Message):
  r"""A DlpProjectsLocationsInspectTemplatesCreateRequest object.

  Fields:
    googlePrivacyDlpV2CreateInspectTemplateRequest: A
      GooglePrivacyDlpV2CreateInspectTemplateRequest resource to be passed as
      the request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2CreateInspectTemplateRequest = _messages.MessageField('GooglePrivacyDlpV2CreateInspectTemplateRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsLocationsInspectTemplatesDeleteRequest(_messages.Message):
  r"""A DlpProjectsLocationsInspectTemplatesDeleteRequest object.

  Fields:
    name: Required. Resource name of the organization and inspectTemplate to
      be deleted, for example
      `organizations/433245324/inspectTemplates/*********` or
      projects/project-id/inspectTemplates/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsLocationsInspectTemplatesGetRequest(_messages.Message):
  r"""A DlpProjectsLocationsInspectTemplatesGetRequest object.

  Fields:
    name: Required. Resource name of the organization and inspectTemplate to
      be read, for example
      `organizations/433245324/inspectTemplates/*********` or
      projects/project-id/inspectTemplates/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsLocationsInspectTemplatesListRequest(_messages.Message):
  r"""A DlpProjectsLocationsInspectTemplatesListRequest object.

  Fields:
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of fields to order by, followed by `asc` or
      `desc` postfix. This list is case-insensitive, default sorting order is
      ascending, redundant space characters are insignificant. Example: `name
      asc,update_time, create_time desc` Supported fields are: -
      `create_time`: corresponds to the time the template was created. -
      `update_time`: corresponds to the time the template was last updated. -
      `name`: corresponds to the template's name. - `display_name`:
      corresponds to the template's display name.
    pageSize: Size of the page, can be limited by the server. If zero server
      returns a page of max size 100.
    pageToken: Page token to continue retrieval. Comes from previous call to
      `ListInspectTemplates`.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  locationId = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DlpProjectsLocationsInspectTemplatesPatchRequest(_messages.Message):
  r"""A DlpProjectsLocationsInspectTemplatesPatchRequest object.

  Fields:
    googlePrivacyDlpV2UpdateInspectTemplateRequest: A
      GooglePrivacyDlpV2UpdateInspectTemplateRequest resource to be passed as
      the request body.
    name: Required. Resource name of organization and inspectTemplate to be
      updated, for example
      `organizations/433245324/inspectTemplates/*********` or
      projects/project-id/inspectTemplates/*********.
  """

  googlePrivacyDlpV2UpdateInspectTemplateRequest = _messages.MessageField('GooglePrivacyDlpV2UpdateInspectTemplateRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpProjectsLocationsJobTriggersActivateRequest(_messages.Message):
  r"""A DlpProjectsLocationsJobTriggersActivateRequest object.

  Fields:
    googlePrivacyDlpV2ActivateJobTriggerRequest: A
      GooglePrivacyDlpV2ActivateJobTriggerRequest resource to be passed as the
      request body.
    name: Required. Resource name of the trigger to activate, for example
      `projects/dlp-test-project/jobTriggers/53234423`.
  """

  googlePrivacyDlpV2ActivateJobTriggerRequest = _messages.MessageField('GooglePrivacyDlpV2ActivateJobTriggerRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpProjectsLocationsJobTriggersCreateRequest(_messages.Message):
  r"""A DlpProjectsLocationsJobTriggersCreateRequest object.

  Fields:
    googlePrivacyDlpV2CreateJobTriggerRequest: A
      GooglePrivacyDlpV2CreateJobTriggerRequest resource to be passed as the
      request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2CreateJobTriggerRequest = _messages.MessageField('GooglePrivacyDlpV2CreateJobTriggerRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsLocationsJobTriggersDeleteRequest(_messages.Message):
  r"""A DlpProjectsLocationsJobTriggersDeleteRequest object.

  Fields:
    name: Required. Resource name of the project and the triggeredJob, for
      example `projects/dlp-test-project/jobTriggers/53234423`.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsLocationsJobTriggersGetRequest(_messages.Message):
  r"""A DlpProjectsLocationsJobTriggersGetRequest object.

  Fields:
    name: Required. Resource name of the project and the triggeredJob, for
      example `projects/dlp-test-project/jobTriggers/53234423`.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsLocationsJobTriggersHybridInspectRequest(_messages.Message):
  r"""A DlpProjectsLocationsJobTriggersHybridInspectRequest object.

  Fields:
    googlePrivacyDlpV2HybridInspectJobTriggerRequest: A
      GooglePrivacyDlpV2HybridInspectJobTriggerRequest resource to be passed
      as the request body.
    name: Required. Resource name of the trigger to execute a hybrid inspect
      on, for example `projects/dlp-test-project/jobTriggers/53234423`.
  """

  googlePrivacyDlpV2HybridInspectJobTriggerRequest = _messages.MessageField('GooglePrivacyDlpV2HybridInspectJobTriggerRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpProjectsLocationsJobTriggersListRequest(_messages.Message):
  r"""A DlpProjectsLocationsJobTriggersListRequest object.

  Enums:
    TypeValueValuesEnum: The type of jobs. Will use `DlpJobType.INSPECT` if
      not set.

  Fields:
    filter: Allows filtering. Supported syntax: * Filter expressions are made
      up of one or more restrictions. * Restrictions can be combined by `AND`
      or `OR` logical operators. A sequence of restrictions implicitly uses
      `AND`. * A restriction has the form of `{field} {operator} {value}`. *
      Supported fields/values for inspect triggers: - `status` -
      HEALTHY|PAUSED|CANCELLED - `inspected_storage` -
      DATASTORE|CLOUD_STORAGE|BIGQUERY - 'last_run_time` - RFC 3339 formatted
      timestamp, surrounded by quotation marks. Nanoseconds are ignored. -
      'error_count' - Number of errors that have occurred while running. * The
      operator must be `=` or `!=` for status and inspected_storage. Examples:
      * inspected_storage = cloud_storage AND status = HEALTHY *
      inspected_storage = cloud_storage OR inspected_storage = bigquery *
      inspected_storage = cloud_storage AND (state = PAUSED OR state =
      HEALTHY) * last_run_time > \"2017-12-12T00:00:00+00:00\" The length of
      this field should be no more than 500 characters.
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of triggeredJob fields to order by, followed
      by `asc` or `desc` postfix. This list is case-insensitive, default
      sorting order is ascending, redundant space characters are
      insignificant. Example: `name asc,update_time, create_time desc`
      Supported fields are: - `create_time`: corresponds to the time the
      JobTrigger was created. - `update_time`: corresponds to the time the
      JobTrigger was last updated. - `last_run_time`: corresponds to the last
      time the JobTrigger ran. - `name`: corresponds to the JobTrigger's name.
      - `display_name`: corresponds to the JobTrigger's display name. -
      `status`: corresponds to JobTrigger's status.
    pageSize: Size of the page, can be limited by a server.
    pageToken: Page token to continue retrieval. Comes from previous call to
      ListJobTriggers. `order_by` field must not change for subsequent calls.
    parent: Required. Parent resource name. The format of this value varies
      depending on whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
    type: The type of jobs. Will use `DlpJobType.INSPECT` if not set.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of jobs. Will use `DlpJobType.INSPECT` if not set.

    Values:
      DLP_JOB_TYPE_UNSPECIFIED: Defaults to INSPECT_JOB.
      INSPECT_JOB: The job inspected Google Cloud for sensitive data.
      RISK_ANALYSIS_JOB: The job executed a Risk Analysis computation.
    """
    DLP_JOB_TYPE_UNSPECIFIED = 0
    INSPECT_JOB = 1
    RISK_ANALYSIS_JOB = 2

  filter = _messages.StringField(1)
  locationId = _messages.StringField(2)
  orderBy = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  parent = _messages.StringField(6, required=True)
  type = _messages.EnumField('TypeValueValuesEnum', 7)


class DlpProjectsLocationsJobTriggersPatchRequest(_messages.Message):
  r"""A DlpProjectsLocationsJobTriggersPatchRequest object.

  Fields:
    googlePrivacyDlpV2UpdateJobTriggerRequest: A
      GooglePrivacyDlpV2UpdateJobTriggerRequest resource to be passed as the
      request body.
    name: Required. Resource name of the project and the triggeredJob, for
      example `projects/dlp-test-project/jobTriggers/53234423`.
  """

  googlePrivacyDlpV2UpdateJobTriggerRequest = _messages.MessageField('GooglePrivacyDlpV2UpdateJobTriggerRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpProjectsLocationsStoredInfoTypesCreateRequest(_messages.Message):
  r"""A DlpProjectsLocationsStoredInfoTypesCreateRequest object.

  Fields:
    googlePrivacyDlpV2CreateStoredInfoTypeRequest: A
      GooglePrivacyDlpV2CreateStoredInfoTypeRequest resource to be passed as
      the request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2CreateStoredInfoTypeRequest = _messages.MessageField('GooglePrivacyDlpV2CreateStoredInfoTypeRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsLocationsStoredInfoTypesDeleteRequest(_messages.Message):
  r"""A DlpProjectsLocationsStoredInfoTypesDeleteRequest object.

  Fields:
    name: Required. Resource name of the organization and storedInfoType to be
      deleted, for example `organizations/433245324/storedInfoTypes/*********`
      or projects/project-id/storedInfoTypes/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsLocationsStoredInfoTypesGetRequest(_messages.Message):
  r"""A DlpProjectsLocationsStoredInfoTypesGetRequest object.

  Fields:
    name: Required. Resource name of the organization and storedInfoType to be
      read, for example `organizations/433245324/storedInfoTypes/*********` or
      projects/project-id/storedInfoTypes/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsLocationsStoredInfoTypesListRequest(_messages.Message):
  r"""A DlpProjectsLocationsStoredInfoTypesListRequest object.

  Fields:
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of fields to order by, followed by `asc` or
      `desc` postfix. This list is case-insensitive, default sorting order is
      ascending, redundant space characters are insignificant. Example: `name
      asc, display_name, create_time desc` Supported fields are: -
      `create_time`: corresponds to the time the most recent version of the
      resource was created. - `state`: corresponds to the state of the
      resource. - `name`: corresponds to resource name. - `display_name`:
      corresponds to info type's display name.
    pageSize: Size of the page, can be limited by the server. If zero server
      returns a page of max size 100.
    pageToken: Page token to continue retrieval. Comes from previous call to
      `ListStoredInfoTypes`.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  locationId = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DlpProjectsLocationsStoredInfoTypesPatchRequest(_messages.Message):
  r"""A DlpProjectsLocationsStoredInfoTypesPatchRequest object.

  Fields:
    googlePrivacyDlpV2UpdateStoredInfoTypeRequest: A
      GooglePrivacyDlpV2UpdateStoredInfoTypeRequest resource to be passed as
      the request body.
    name: Required. Resource name of organization and storedInfoType to be
      updated, for example `organizations/433245324/storedInfoTypes/*********`
      or projects/project-id/storedInfoTypes/*********.
  """

  googlePrivacyDlpV2UpdateStoredInfoTypeRequest = _messages.MessageField('GooglePrivacyDlpV2UpdateStoredInfoTypeRequest', 1)
  name = _messages.StringField(2, required=True)


class DlpProjectsStoredInfoTypesCreateRequest(_messages.Message):
  r"""A DlpProjectsStoredInfoTypesCreateRequest object.

  Fields:
    googlePrivacyDlpV2CreateStoredInfoTypeRequest: A
      GooglePrivacyDlpV2CreateStoredInfoTypeRequest resource to be passed as
      the request body.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID +
      Organizations scope, location specified:
      `organizations/`ORG_ID`/locations/`LOCATION_ID + Organizations scope, no
      location specified (defaults to global): `organizations/`ORG_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  googlePrivacyDlpV2CreateStoredInfoTypeRequest = _messages.MessageField('GooglePrivacyDlpV2CreateStoredInfoTypeRequest', 1)
  parent = _messages.StringField(2, required=True)


class DlpProjectsStoredInfoTypesDeleteRequest(_messages.Message):
  r"""A DlpProjectsStoredInfoTypesDeleteRequest object.

  Fields:
    name: Required. Resource name of the organization and storedInfoType to be
      deleted, for example `organizations/433245324/storedInfoTypes/*********`
      or projects/project-id/storedInfoTypes/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsStoredInfoTypesGetRequest(_messages.Message):
  r"""A DlpProjectsStoredInfoTypesGetRequest object.

  Fields:
    name: Required. Resource name of the organization and storedInfoType to be
      read, for example `organizations/433245324/storedInfoTypes/*********` or
      projects/project-id/storedInfoTypes/*********.
  """

  name = _messages.StringField(1, required=True)


class DlpProjectsStoredInfoTypesListRequest(_messages.Message):
  r"""A DlpProjectsStoredInfoTypesListRequest object.

  Fields:
    locationId: Deprecated. This field has no effect.
    orderBy: Comma separated list of fields to order by, followed by `asc` or
      `desc` postfix. This list is case-insensitive, default sorting order is
      ascending, redundant space characters are insignificant. Example: `name
      asc, display_name, create_time desc` Supported fields are: -
      `create_time`: corresponds to the time the most recent version of the
      resource was created. - `state`: corresponds to the state of the
      resource. - `name`: corresponds to resource name. - `display_name`:
      corresponds to info type's display name.
    pageSize: Size of the page, can be limited by the server. If zero server
      returns a page of max size 100.
    pageToken: Page token to continue retrieval. Comes from previous call to
      `ListStoredInfoTypes`.
    parent: Required. Parent resource name. The format of this value varies
      depending on the scope of the request (project or organization) and
      whether you have [specified a processing
      location](https://cloud.google.com/dlp/docs/specifying-location): +
      Projects scope, location specified:
      `projects/`PROJECT_ID`/locations/`LOCATION_ID + Projects scope, no
      location specified (defaults to global): `projects/`PROJECT_ID The
      following example `parent` string specifies a parent project with the
      identifier `example-project`, and specifies the `europe-west3` location
      for processing data: parent=projects/example-project/locations/europe-
      west3
  """

  locationId = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DlpProjectsStoredInfoTypesPatchRequest(_messages.Message):
  r"""A DlpProjectsStoredInfoTypesPatchRequest object.

  Fields:
    googlePrivacyDlpV2UpdateStoredInfoTypeRequest: A
      GooglePrivacyDlpV2UpdateStoredInfoTypeRequest resource to be passed as
      the request body.
    name: Required. Resource name of organization and storedInfoType to be
      updated, for example `organizations/433245324/storedInfoTypes/*********`
      or projects/project-id/storedInfoTypes/*********.
  """

  googlePrivacyDlpV2UpdateStoredInfoTypeRequest = _messages.MessageField('GooglePrivacyDlpV2UpdateStoredInfoTypeRequest', 1)
  name = _messages.StringField(2, required=True)


class GooglePrivacyDlpV2Action(_messages.Message):
  r"""A task to execute on the completion of a job. See
  https://cloud.google.com/dlp/docs/concepts-actions to learn more.

  Fields:
    deidentify: Create a de-identified copy of the input data.
    jobNotificationEmails: Sends an email when the job completes. The email
      goes to IAM project owners and technical [Essential
      Contacts](https://cloud.google.com/resource-manager/docs/managing-
      notification-contacts).
    pubSub: Publish a notification to a Pub/Sub topic.
    publishFindingsToCloudDataCatalog: Publish findings to Cloud Datahub.
    publishSummaryToCscc: Publish summary to Cloud Security Command Center
      (Alpha).
    publishToStackdriver: Enable Stackdriver metric
      dlp.googleapis.com/finding_count.
    saveFindings: Save resulting findings in a provided location.
  """

  deidentify = _messages.MessageField('GooglePrivacyDlpV2Deidentify', 1)
  jobNotificationEmails = _messages.MessageField('GooglePrivacyDlpV2JobNotificationEmails', 2)
  pubSub = _messages.MessageField('GooglePrivacyDlpV2PublishToPubSub', 3)
  publishFindingsToCloudDataCatalog = _messages.MessageField('GooglePrivacyDlpV2PublishFindingsToCloudDataCatalog', 4)
  publishSummaryToCscc = _messages.MessageField('GooglePrivacyDlpV2PublishSummaryToCscc', 5)
  publishToStackdriver = _messages.MessageField('GooglePrivacyDlpV2PublishToStackdriver', 6)
  saveFindings = _messages.MessageField('GooglePrivacyDlpV2SaveFindings', 7)


class GooglePrivacyDlpV2ActionDetails(_messages.Message):
  r"""The results of an Action.

  Fields:
    deidentifyDetails: Outcome of a de-identification action.
  """

  deidentifyDetails = _messages.MessageField('GooglePrivacyDlpV2DeidentifyDataSourceDetails', 1)


class GooglePrivacyDlpV2ActivateJobTriggerRequest(_messages.Message):
  r"""Request message for ActivateJobTrigger."""


class GooglePrivacyDlpV2AllInfoTypes(_messages.Message):
  r"""Apply transformation to all findings."""


class GooglePrivacyDlpV2AllText(_messages.Message):
  r"""Apply to all text."""


class GooglePrivacyDlpV2AnalyzeDataSourceRiskDetails(_messages.Message):
  r"""Result of a risk analysis operation request.

  Fields:
    categoricalStatsResult: Categorical stats result
    deltaPresenceEstimationResult: Delta-presence result
    kAnonymityResult: K-anonymity result
    kMapEstimationResult: K-map result
    lDiversityResult: L-divesity result
    numericalStatsResult: Numerical stats result
    requestedOptions: The configuration used for this job.
    requestedPrivacyMetric: Privacy metric to compute.
    requestedSourceTable: Input dataset to compute metrics over.
  """

  categoricalStatsResult = _messages.MessageField('GooglePrivacyDlpV2CategoricalStatsResult', 1)
  deltaPresenceEstimationResult = _messages.MessageField('GooglePrivacyDlpV2DeltaPresenceEstimationResult', 2)
  kAnonymityResult = _messages.MessageField('GooglePrivacyDlpV2KAnonymityResult', 3)
  kMapEstimationResult = _messages.MessageField('GooglePrivacyDlpV2KMapEstimationResult', 4)
  lDiversityResult = _messages.MessageField('GooglePrivacyDlpV2LDiversityResult', 5)
  numericalStatsResult = _messages.MessageField('GooglePrivacyDlpV2NumericalStatsResult', 6)
  requestedOptions = _messages.MessageField('GooglePrivacyDlpV2RequestedRiskAnalysisOptions', 7)
  requestedPrivacyMetric = _messages.MessageField('GooglePrivacyDlpV2PrivacyMetric', 8)
  requestedSourceTable = _messages.MessageField('GooglePrivacyDlpV2BigQueryTable', 9)


class GooglePrivacyDlpV2AuxiliaryTable(_messages.Message):
  r"""An auxiliary table contains statistical information on the relative
  frequency of different quasi-identifiers values. It has one or several
  quasi-identifiers columns, and one column that indicates the relative
  frequency of each quasi-identifier tuple. If a tuple is present in the data
  but not in the auxiliary table, the corresponding relative frequency is
  assumed to be zero (and thus, the tuple is highly reidentifiable).

  Fields:
    quasiIds: Required. Quasi-identifier columns.
    relativeFrequency: Required. The relative frequency column must contain a
      floating-point number between 0 and 1 (inclusive). Null values are
      assumed to be zero.
    table: Required. Auxiliary table location.
  """

  quasiIds = _messages.MessageField('GooglePrivacyDlpV2QuasiIdField', 1, repeated=True)
  relativeFrequency = _messages.MessageField('GooglePrivacyDlpV2FieldId', 2)
  table = _messages.MessageField('GooglePrivacyDlpV2BigQueryTable', 3)


class GooglePrivacyDlpV2BigQueryField(_messages.Message):
  r"""Message defining a field of a BigQuery table.

  Fields:
    field: Designated field in the BigQuery table.
    table: Source table of the field.
  """

  field = _messages.MessageField('GooglePrivacyDlpV2FieldId', 1)
  table = _messages.MessageField('GooglePrivacyDlpV2BigQueryTable', 2)


class GooglePrivacyDlpV2BigQueryKey(_messages.Message):
  r"""Row key for identifying a record in BigQuery table.

  Fields:
    rowNumber: Row number inferred at the time the table was scanned. This
      value is nondeterministic, cannot be queried, and may be null for
      inspection jobs. To locate findings within a table, specify
      `inspect_job.storage_config.big_query_options.identifying_fields` in
      `CreateDlpJobRequest`.
    tableReference: Complete BigQuery table reference.
  """

  rowNumber = _messages.IntegerField(1)
  tableReference = _messages.MessageField('GooglePrivacyDlpV2BigQueryTable', 2)


class GooglePrivacyDlpV2BigQueryOptions(_messages.Message):
  r"""Options defining BigQuery table and row identifiers.

  Enums:
    SampleMethodValueValuesEnum:

  Fields:
    excludedFields: References to fields excluded from scanning. This allows
      you to skip inspection of entire columns which you know have no
      findings. When inspecting a table, we recommend that you inspect all
      columns. Otherwise, findings might be impacted because hints from
      excluded columns will not be used.
    identifyingFields: Table fields that may uniquely identify a row within
      the table. When `actions.saveFindings.outputConfig.table` is specified,
      the values of columns specified here are available in the output table
      under `location.content_locations.record_location.record_key.id_values`.
      Nested fields such as `person.birthdate.year` are allowed.
    includedFields: Limit scanning only to these fields. When inspecting a
      table, we recommend that you inspect all columns. Otherwise, findings
      might be impacted because hints from excluded columns will not be used.
    rowsLimit: Max number of rows to scan. If the table has more rows than
      this value, the rest of the rows are omitted. If not set, or if set to
      0, all rows will be scanned. Only one of rows_limit and
      rows_limit_percent can be specified. Cannot be used in conjunction with
      TimespanConfig.
    rowsLimitPercent: Max percentage of rows to scan. The rest are omitted.
      The number of rows scanned is rounded down. Must be between 0 and 100,
      inclusively. Both 0 and 100 means no limit. Defaults to 0. Only one of
      rows_limit and rows_limit_percent can be specified. Cannot be used in
      conjunction with TimespanConfig.
    sampleMethod: A SampleMethodValueValuesEnum attribute.
    tableReference: Complete BigQuery table reference.
  """

  class SampleMethodValueValuesEnum(_messages.Enum):
    r"""SampleMethodValueValuesEnum enum type.

    Values:
      SAMPLE_METHOD_UNSPECIFIED: <no description>
      TOP: Scan groups of rows in the order BigQuery provides (default).
        Multiple groups of rows may be scanned in parallel, so results may not
        appear in the same order the rows are read.
      RANDOM_START: Randomly pick groups of rows to scan.
    """
    SAMPLE_METHOD_UNSPECIFIED = 0
    TOP = 1
    RANDOM_START = 2

  excludedFields = _messages.MessageField('GooglePrivacyDlpV2FieldId', 1, repeated=True)
  identifyingFields = _messages.MessageField('GooglePrivacyDlpV2FieldId', 2, repeated=True)
  includedFields = _messages.MessageField('GooglePrivacyDlpV2FieldId', 3, repeated=True)
  rowsLimit = _messages.IntegerField(4)
  rowsLimitPercent = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  sampleMethod = _messages.EnumField('SampleMethodValueValuesEnum', 6)
  tableReference = _messages.MessageField('GooglePrivacyDlpV2BigQueryTable', 7)


class GooglePrivacyDlpV2BigQueryTable(_messages.Message):
  r"""Message defining the location of a BigQuery table. A table is uniquely
  identified by its project_id, dataset_id, and table_name. Within a query a
  table is often referenced with a string in the format of: `:.` or `..`.

  Fields:
    datasetId: Dataset ID of the table.
    projectId: The Google Cloud Platform project ID of the project containing
      the table. If omitted, project ID is inferred from the API call.
    tableId: Name of the table.
  """

  datasetId = _messages.StringField(1)
  projectId = _messages.StringField(2)
  tableId = _messages.StringField(3)


class GooglePrivacyDlpV2BoundingBox(_messages.Message):
  r"""Bounding box encompassing detected text within an image.

  Fields:
    height: Height of the bounding box in pixels.
    left: Left coordinate of the bounding box. (0,0) is upper left.
    top: Top coordinate of the bounding box. (0,0) is upper left.
    width: Width of the bounding box in pixels.
  """

  height = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  left = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  top = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  width = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GooglePrivacyDlpV2Bucket(_messages.Message):
  r"""Bucket is represented as a range, along with replacement values.

  Fields:
    max: Upper bound of the range, exclusive; type must match min.
    min: Lower bound of the range, inclusive. Type should be the same as max
      if used.
    replacementValue: Required. Replacement value for this bucket.
  """

  max = _messages.MessageField('GooglePrivacyDlpV2Value', 1)
  min = _messages.MessageField('GooglePrivacyDlpV2Value', 2)
  replacementValue = _messages.MessageField('GooglePrivacyDlpV2Value', 3)


class GooglePrivacyDlpV2BucketingConfig(_messages.Message):
  r"""Generalization function that buckets values based on ranges. The ranges
  and replacement values are dynamically provided by the user for custom
  behavior, such as 1-30 -> LOW 31-65 -> MEDIUM 66-100 -> HIGH This can be
  used on data of type: number, long, string, timestamp. If the bound `Value`
  type differs from the type of data being transformed, we will first attempt
  converting the type of the data to be transformed to match the type of the
  bound before comparing. See https://cloud.google.com/dlp/docs/concepts-
  bucketing to learn more.

  Fields:
    buckets: Set of buckets. Ranges must be non-overlapping.
  """

  buckets = _messages.MessageField('GooglePrivacyDlpV2Bucket', 1, repeated=True)


class GooglePrivacyDlpV2ByteContentItem(_messages.Message):
  r"""Container for bytes to inspect or redact.

  Enums:
    TypeValueValuesEnum: The type of data stored in the bytes string. Default
      will be TEXT_UTF8.

  Fields:
    data: Content data to inspect or redact.
    type: The type of data stored in the bytes string. Default will be
      TEXT_UTF8.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of data stored in the bytes string. Default will be
    TEXT_UTF8.

    Values:
      BYTES_TYPE_UNSPECIFIED: Unused
      IMAGE: Any image type.
      IMAGE_JPEG: jpeg
      IMAGE_BMP: bmp
      IMAGE_PNG: png
      IMAGE_SVG: svg
      TEXT_UTF8: plain text
      WORD_DOCUMENT: docx, docm, dotx, dotm
      PDF: pdf
      POWERPOINT_DOCUMENT: pptx, pptm, potx, potm, pot
      EXCEL_DOCUMENT: xlsx, xlsm, xltx, xltm
      AVRO: avro
      CSV: csv
      TSV: tsv
    """
    BYTES_TYPE_UNSPECIFIED = 0
    IMAGE = 1
    IMAGE_JPEG = 2
    IMAGE_BMP = 3
    IMAGE_PNG = 4
    IMAGE_SVG = 5
    TEXT_UTF8 = 6
    WORD_DOCUMENT = 7
    PDF = 8
    POWERPOINT_DOCUMENT = 9
    EXCEL_DOCUMENT = 10
    AVRO = 11
    CSV = 12
    TSV = 13

  data = _messages.BytesField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class GooglePrivacyDlpV2CancelDlpJobRequest(_messages.Message):
  r"""The request message for canceling a DLP job."""


class GooglePrivacyDlpV2CategoricalStatsConfig(_messages.Message):
  r"""Compute numerical stats over an individual column, including number of
  distinct values and value count distribution.

  Fields:
    field: Field to compute categorical stats on. All column types are
      supported except for arrays and structs. However, it may be more
      informative to use NumericalStats when the field type is supported,
      depending on the data.
  """

  field = _messages.MessageField('GooglePrivacyDlpV2FieldId', 1)


class GooglePrivacyDlpV2CategoricalStatsHistogramBucket(_messages.Message):
  r"""Histogram of value frequencies in the column.

  Fields:
    bucketSize: Total number of values in this bucket.
    bucketValueCount: Total number of distinct values in this bucket.
    bucketValues: Sample of value frequencies in this bucket. The total number
      of values returned per bucket is capped at 20.
    valueFrequencyLowerBound: Lower bound on the value frequency of the values
      in this bucket.
    valueFrequencyUpperBound: Upper bound on the value frequency of the values
      in this bucket.
  """

  bucketSize = _messages.IntegerField(1)
  bucketValueCount = _messages.IntegerField(2)
  bucketValues = _messages.MessageField('GooglePrivacyDlpV2ValueFrequency', 3, repeated=True)
  valueFrequencyLowerBound = _messages.IntegerField(4)
  valueFrequencyUpperBound = _messages.IntegerField(5)


class GooglePrivacyDlpV2CategoricalStatsResult(_messages.Message):
  r"""Result of the categorical stats computation.

  Fields:
    valueFrequencyHistogramBuckets: Histogram of value frequencies in the
      column.
  """

  valueFrequencyHistogramBuckets = _messages.MessageField('GooglePrivacyDlpV2CategoricalStatsHistogramBucket', 1, repeated=True)


class GooglePrivacyDlpV2CharacterMaskConfig(_messages.Message):
  r"""Partially mask a string by replacing a given number of characters with a
  fixed character. Masking can start from the beginning or end of the string.
  This can be used on data of any type (numbers, longs, and so on) and when
  de-identifying structured data we'll attempt to preserve the original data's
  type. (This allows you to take a long like 123 and modify it to a string
  like **3.

  Fields:
    charactersToIgnore: When masking a string, items in this list will be
      skipped when replacing characters. For example, if the input string is
      `555-555-5555` and you instruct Cloud DLP to skip `-` and mask 5
      characters with `*`, Cloud DLP returns `***-**5-5555`.
    maskingCharacter: Character to use to mask the sensitive values-for
      example, `*` for an alphabetic string such as a name, or `0` for a
      numeric string such as ZIP code or credit card number. This string must
      have a length of 1. If not supplied, this value defaults to `*` for
      strings, and `0` for digits.
    numberToMask: Number of characters to mask. If not set, all matching chars
      will be masked. Skipped characters do not count towards this tally. If
      `number_to_mask` is negative, this denotes inverse masking. Cloud DLP
      masks all but a number of characters. For example, suppose you have the
      following values: - `masking_character` is `*` - `number_to_mask` is
      `-4` - `reverse_order` is `false` - `CharsToIgnore` includes `-` - Input
      string is `1234-5678-9012-3456` The resulting de-identified string is
      `****-****-****-3456`. Cloud DLP masks all but the last four characters.
      If `reverse_order` is `true`, all but the first four characters are
      masked as `1234-****-****-****`.
    reverseOrder: Mask characters in reverse order. For example, if
      `masking_character` is `0`, `number_to_mask` is `14`, and
      `reverse_order` is `false`, then the input string `1234-5678-9012-3456`
      is masked as `00000000000000-3456`. If `masking_character` is `*`,
      `number_to_mask` is `3`, and `reverse_order` is `true`, then the string
      `12345` is masked as `12***`.
  """

  charactersToIgnore = _messages.MessageField('GooglePrivacyDlpV2CharsToIgnore', 1, repeated=True)
  maskingCharacter = _messages.StringField(2)
  numberToMask = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  reverseOrder = _messages.BooleanField(4)


class GooglePrivacyDlpV2CharsToIgnore(_messages.Message):
  r"""Characters to skip when doing deidentification of a value. These will be
  left alone and skipped.

  Enums:
    CommonCharactersToIgnoreValueValuesEnum: Common characters to not
      transform when masking. Useful to avoid removing punctuation.

  Fields:
    charactersToSkip: Characters to not transform when masking.
    commonCharactersToIgnore: Common characters to not transform when masking.
      Useful to avoid removing punctuation.
  """

  class CommonCharactersToIgnoreValueValuesEnum(_messages.Enum):
    r"""Common characters to not transform when masking. Useful to avoid
    removing punctuation.

    Values:
      COMMON_CHARS_TO_IGNORE_UNSPECIFIED: Unused.
      NUMERIC: 0-9
      ALPHA_UPPER_CASE: A-Z
      ALPHA_LOWER_CASE: a-z
      PUNCTUATION: US Punctuation, one of !"#$%&'()*+,-./:;<=>?@[\]^_`{|}~
      WHITESPACE: Whitespace character, one of [ \t\n\x0B\f\r]
    """
    COMMON_CHARS_TO_IGNORE_UNSPECIFIED = 0
    NUMERIC = 1
    ALPHA_UPPER_CASE = 2
    ALPHA_LOWER_CASE = 3
    PUNCTUATION = 4
    WHITESPACE = 5

  charactersToSkip = _messages.StringField(1)
  commonCharactersToIgnore = _messages.EnumField('CommonCharactersToIgnoreValueValuesEnum', 2)


class GooglePrivacyDlpV2CloudStorageFileSet(_messages.Message):
  r"""Message representing a set of files in Cloud Storage.

  Fields:
    url: The url, in the format `gs:///`. Trailing wildcard in the path is
      allowed.
  """

  url = _messages.StringField(1)


class GooglePrivacyDlpV2CloudStorageOptions(_messages.Message):
  r"""Options defining a file or a set of files within a Cloud Storage bucket.

  Enums:
    FileTypesValueListEntryValuesEnum:
    SampleMethodValueValuesEnum:

  Fields:
    bytesLimitPerFile: Max number of bytes to scan from a file. If a scanned
      file's size is bigger than this value then the rest of the bytes are
      omitted. Only one of `bytes_limit_per_file` and
      `bytes_limit_per_file_percent` can be specified. This field can't be set
      if de-identification is requested. For certain file types, setting this
      field has no effect. For more information, see [Limits on bytes scanned
      per file](https://cloud.google.com/dlp/docs/supported-file-types#max-
      byte-size-per-file).
    bytesLimitPerFilePercent: Max percentage of bytes to scan from a file. The
      rest are omitted. The number of bytes scanned is rounded down. Must be
      between 0 and 100, inclusively. Both 0 and 100 means no limit. Defaults
      to 0. Only one of bytes_limit_per_file and bytes_limit_per_file_percent
      can be specified. This field can't be set if de-identification is
      requested. For certain file types, setting this field has no effect. For
      more information, see [Limits on bytes scanned per
      file](https://cloud.google.com/dlp/docs/supported-file-types#max-byte-
      size-per-file).
    fileSet: The set of one or more files to scan.
    fileTypes: List of file type groups to include in the scan. If empty, all
      files are scanned and available data format processors are applied. In
      addition, the binary content of the selected files is always scanned as
      well. Images are scanned only as binary if the specified region does not
      support image inspection and no file_types were specified. Image
      inspection is restricted to 'global', 'us', 'asia', and 'europe'.
    filesLimitPercent: Limits the number of files to scan to this percentage
      of the input FileSet. Number of files scanned is rounded down. Must be
      between 0 and 100, inclusively. Both 0 and 100 means no limit. Defaults
      to 0.
    sampleMethod: A SampleMethodValueValuesEnum attribute.
  """

  class FileTypesValueListEntryValuesEnum(_messages.Enum):
    r"""FileTypesValueListEntryValuesEnum enum type.

    Values:
      FILE_TYPE_UNSPECIFIED: Includes all files.
      BINARY_FILE: Includes all file extensions not covered by another entry.
        Binary scanning attempts to convert the content of the file to utf_8
        to scan the file. If you wish to avoid this fall back, specify one or
        more of the other file types in your storage scan.
      TEXT_FILE: Included file extensions: asc,asp, aspx, brf, c, cc,cfm, cgi,
        cpp, csv, cxx, c++, cs, css, dart, dat, dot, eml,, epbub, ged, go, h,
        hh, hpp, hxx, h++, hs, html, htm, mkd, markdown, m, ml, mli, perl, pl,
        plist, pm, php, phtml, pht, properties, py, pyw, rb, rbw, rs, rss, rc,
        scala, sh, sql, swift, tex, shtml, shtm, xhtml, lhs, ics, ini, java,
        js, json, kix, kml, ocaml, md, txt, text, tsv, vb, vcard, vcs, wml,
        xcodeproj, xml, xsl, xsd, yml, yaml.
      IMAGE: Included file extensions: bmp, gif, jpg, jpeg, jpe, png. Setting
        bytes_limit_per_file or bytes_limit_per_file_percent has no effect on
        image files. Image inspection is restricted to the `global`, `us`,
        `asia`, and `europe` regions.
      WORD: Microsoft Word files larger than 30 MB will be scanned as binary
        files. Included file extensions: docx, dotx, docm, dotm. Setting
        `bytes_limit_per_file` or `bytes_limit_per_file_percent` has no effect
        on Word files.
      PDF: PDF files larger than 30 MB will be scanned as binary files.
        Included file extensions: pdf. Setting `bytes_limit_per_file` or
        `bytes_limit_per_file_percent` has no effect on PDF files.
      AVRO: Included file extensions: avro
      CSV: Included file extensions: csv
      TSV: Included file extensions: tsv
      POWERPOINT: Microsoft PowerPoint files larger than 30 MB will be scanned
        as binary files. Included file extensions: pptx, pptm, potx, potm,
        pot. Setting `bytes_limit_per_file` or `bytes_limit_per_file_percent`
        has no effect on PowerPoint files.
      EXCEL: Microsoft Excel files larger than 30 MB will be scanned as binary
        files. Included file extensions: xlsx, xlsm, xltx, xltm. Setting
        `bytes_limit_per_file` or `bytes_limit_per_file_percent` has no effect
        on Excel files.
    """
    FILE_TYPE_UNSPECIFIED = 0
    BINARY_FILE = 1
    TEXT_FILE = 2
    IMAGE = 3
    WORD = 4
    PDF = 5
    AVRO = 6
    CSV = 7
    TSV = 8
    POWERPOINT = 9
    EXCEL = 10

  class SampleMethodValueValuesEnum(_messages.Enum):
    r"""SampleMethodValueValuesEnum enum type.

    Values:
      SAMPLE_METHOD_UNSPECIFIED: <no description>
      TOP: Scan from the top (default).
      RANDOM_START: For each file larger than bytes_limit_per_file, randomly
        pick the offset to start scanning. The scanned bytes are contiguous.
    """
    SAMPLE_METHOD_UNSPECIFIED = 0
    TOP = 1
    RANDOM_START = 2

  bytesLimitPerFile = _messages.IntegerField(1)
  bytesLimitPerFilePercent = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  fileSet = _messages.MessageField('GooglePrivacyDlpV2FileSet', 3)
  fileTypes = _messages.EnumField('FileTypesValueListEntryValuesEnum', 4, repeated=True)
  filesLimitPercent = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  sampleMethod = _messages.EnumField('SampleMethodValueValuesEnum', 6)


class GooglePrivacyDlpV2CloudStoragePath(_messages.Message):
  r"""Message representing a single file or path in Cloud Storage.

  Fields:
    path: A url representing a file or path (no wildcards) in Cloud Storage.
      Example: gs://[BUCKET_NAME]/dictionary.txt
  """

  path = _messages.StringField(1)


class GooglePrivacyDlpV2CloudStorageRegexFileSet(_messages.Message):
  r"""Message representing a set of files in a Cloud Storage bucket. Regular
  expressions are used to allow fine-grained control over which files in the
  bucket to include. Included files are those that match at least one item in
  `include_regex` and do not match any items in `exclude_regex`. Note that a
  file that matches items from both lists will _not_ be included. For a match
  to occur, the entire file path (i.e., everything in the url after the bucket
  name) must match the regular expression. For example, given the input
  `{bucket_name: "mybucket", include_regex: ["directory1/.*"], exclude_regex:
  ["directory1/excluded.*"]}`: * `gs://mybucket/directory1/myfile` will be
  included * `gs://mybucket/directory1/directory2/myfile` will be included
  (`.*` matches across `/`) * `gs://mybucket/directory0/directory1/myfile`
  will _not_ be included (the full path doesn't match any items in
  `include_regex`) * `gs://mybucket/directory1/excludedfile` will _not_ be
  included (the path matches an item in `exclude_regex`) If `include_regex` is
  left empty, it will match all files by default (this is equivalent to
  setting `include_regex: [".*"]`). Some other common use cases: *
  `{bucket_name: "mybucket", exclude_regex: [".*\.pdf"]}` will include all
  files in `mybucket` except for .pdf files * `{bucket_name: "mybucket",
  include_regex: ["directory/[^/]+"]}` will include all files directly under
  `gs://mybucket/directory/`, without matching across `/`

  Fields:
    bucketName: The name of a Cloud Storage bucket. Required.
    excludeRegex: A list of regular expressions matching file paths to
      exclude. All files in the bucket that match at least one of these
      regular expressions will be excluded from the scan. Regular expressions
      use RE2 [syntax](https://github.com/google/re2/wiki/Syntax); a guide can
      be found under the google/re2 repository on GitHub.
    includeRegex: A list of regular expressions matching file paths to
      include. All files in the bucket that match at least one of these
      regular expressions will be included in the set of files, except for
      those that also match an item in `exclude_regex`. Leaving this field
      empty will match all files by default (this is equivalent to including
      `.*` in the list). Regular expressions use RE2
      [syntax](https://github.com/google/re2/wiki/Syntax); a guide can be
      found under the google/re2 repository on GitHub.
  """

  bucketName = _messages.StringField(1)
  excludeRegex = _messages.StringField(2, repeated=True)
  includeRegex = _messages.StringField(3, repeated=True)


class GooglePrivacyDlpV2Color(_messages.Message):
  r"""Represents a color in the RGB color space.

  Fields:
    blue: The amount of blue in the color as a value in the interval [0, 1].
    green: The amount of green in the color as a value in the interval [0, 1].
    red: The amount of red in the color as a value in the interval [0, 1].
  """

  blue = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  green = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  red = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GooglePrivacyDlpV2ColumnDataProfile(_messages.Message):
  r"""The profile for a scanned column within a table.

  Enums:
    ColumnTypeValueValuesEnum: The data type of a given column.
    EstimatedNullPercentageValueValuesEnum: Approximate percentage of entries
      being null in the column.
    EstimatedUniquenessScoreValueValuesEnum: Approximate uniqueness of the
      column.
    PolicyStateValueValuesEnum: Indicates if a policy tag has been applied to
      the column.
    StateValueValuesEnum: State of a profile.

  Fields:
    column: The name of the column.
    columnInfoType: If it's been determined this column can be identified as a
      single type, this will be set. Otherwise the column either has
      unidentifiable content or mixed types.
    columnType: The data type of a given column.
    dataRiskLevel: The data risk level for this column.
    datasetId: The BigQuery dataset ID.
    datasetLocation: The BigQuery location where the dataset's data is stored.
      See https://cloud.google.com/bigquery/docs/locations for supported
      locations.
    datasetProjectId: The Google Cloud project ID that owns the BigQuery
      dataset.
    estimatedNullPercentage: Approximate percentage of entries being null in
      the column.
    estimatedUniquenessScore: Approximate uniqueness of the column.
    freeTextScore: The likelihood that this column contains free-form text. A
      value close to 1 may indicate the column is likely to contain free-form
      or natural language text. Range in 0-1.
    name: The name of the profile.
    otherMatches: Other types found within this column. List will be un-
      ordered.
    policyState: Indicates if a policy tag has been applied to the column.
    profileLastGenerated: The last time the profile was generated.
    profileStatus: Success or error status from the most recent profile
      generation attempt. May be empty if the profile is still being
      generated.
    sensitivityScore: The sensitivity of this column.
    state: State of a profile.
    tableDataProfile: The resource name to the table data profile.
    tableFullResource: The resource name of the table this column is within.
    tableId: The BigQuery table ID.
  """

  class ColumnTypeValueValuesEnum(_messages.Enum):
    r"""The data type of a given column.

    Values:
      COLUMN_DATA_TYPE_UNSPECIFIED: Invalid type.
      TYPE_INT64: Encoded as a string in decimal format.
      TYPE_BOOL: Encoded as a boolean "false" or "true".
      TYPE_FLOAT64: Encoded as a number, or string "NaN", "Infinity" or
        "-Infinity".
      TYPE_STRING: Encoded as a string value.
      TYPE_BYTES: Encoded as a base64 string per RFC 4648, section 4.
      TYPE_TIMESTAMP: Encoded as an RFC 3339 timestamp with mandatory "Z" time
        zone string: 1985-04-12T23:20:50.52Z
      TYPE_DATE: Encoded as RFC 3339 full-date format string: 1985-04-12
      TYPE_TIME: Encoded as RFC 3339 partial-time format string: 23:20:50.52
      TYPE_DATETIME: Encoded as RFC 3339 full-date "T" partial-time:
        1985-04-12T23:20:50.52
      TYPE_GEOGRAPHY: Encoded as WKT
      TYPE_NUMERIC: Encoded as a decimal string.
      TYPE_RECORD: Container of ordered fields, each with a type and field
        name.
      TYPE_BIGNUMERIC: Decimal type.
      TYPE_JSON: Json type.
    """
    COLUMN_DATA_TYPE_UNSPECIFIED = 0
    TYPE_INT64 = 1
    TYPE_BOOL = 2
    TYPE_FLOAT64 = 3
    TYPE_STRING = 4
    TYPE_BYTES = 5
    TYPE_TIMESTAMP = 6
    TYPE_DATE = 7
    TYPE_TIME = 8
    TYPE_DATETIME = 9
    TYPE_GEOGRAPHY = 10
    TYPE_NUMERIC = 11
    TYPE_RECORD = 12
    TYPE_BIGNUMERIC = 13
    TYPE_JSON = 14

  class EstimatedNullPercentageValueValuesEnum(_messages.Enum):
    r"""Approximate percentage of entries being null in the column.

    Values:
      NULL_PERCENTAGE_LEVEL_UNSPECIFIED: Unused.
      NULL_PERCENTAGE_VERY_LOW: Very few null entries.
      NULL_PERCENTAGE_LOW: Some null entries.
      NULL_PERCENTAGE_MEDIUM: <no description>
      NULL_PERCENTAGE_HIGH: A lot of null entries.
    """
    NULL_PERCENTAGE_LEVEL_UNSPECIFIED = 0
    NULL_PERCENTAGE_VERY_LOW = 1
    NULL_PERCENTAGE_LOW = 2
    NULL_PERCENTAGE_MEDIUM = 3
    NULL_PERCENTAGE_HIGH = 4

  class EstimatedUniquenessScoreValueValuesEnum(_messages.Enum):
    r"""Approximate uniqueness of the column.

    Values:
      UNIQUENESS_SCORE_LEVEL_UNSPECIFIED: Some columns do not have estimated
        uniqueness. Possible reasons include having too few values.
      UNIQUENESS_SCORE_LOW: Low uniqueness, possibly a boolean, enum or
        similiarly typed column.
      UNIQUENESS_SCORE_MEDIUM: Medium uniqueness.
      UNIQUENESS_SCORE_HIGH: High uniqueness, possibly a column of free text
        or unique identifiers.
    """
    UNIQUENESS_SCORE_LEVEL_UNSPECIFIED = 0
    UNIQUENESS_SCORE_LOW = 1
    UNIQUENESS_SCORE_MEDIUM = 2
    UNIQUENESS_SCORE_HIGH = 3

  class PolicyStateValueValuesEnum(_messages.Enum):
    r"""Indicates if a policy tag has been applied to the column.

    Values:
      COLUMN_POLICY_STATE_UNSPECIFIED: No policy tags.
      COLUMN_POLICY_TAGGED: Column has policy tag applied.
    """
    COLUMN_POLICY_STATE_UNSPECIFIED = 0
    COLUMN_POLICY_TAGGED = 1

  class StateValueValuesEnum(_messages.Enum):
    r"""State of a profile.

    Values:
      STATE_UNSPECIFIED: Unused.
      RUNNING: The profile is currently running. Once a profile has finished
        it will transition to DONE.
      DONE: The profile is no longer generating. If profile_status.status.code
        is 0, the profile succeeded, otherwise, it failed.
    """
    STATE_UNSPECIFIED = 0
    RUNNING = 1
    DONE = 2

  column = _messages.StringField(1)
  columnInfoType = _messages.MessageField('GooglePrivacyDlpV2InfoTypeSummary', 2)
  columnType = _messages.EnumField('ColumnTypeValueValuesEnum', 3)
  dataRiskLevel = _messages.MessageField('GooglePrivacyDlpV2DataRiskLevel', 4)
  datasetId = _messages.StringField(5)
  datasetLocation = _messages.StringField(6)
  datasetProjectId = _messages.StringField(7)
  estimatedNullPercentage = _messages.EnumField('EstimatedNullPercentageValueValuesEnum', 8)
  estimatedUniquenessScore = _messages.EnumField('EstimatedUniquenessScoreValueValuesEnum', 9)
  freeTextScore = _messages.FloatField(10)
  name = _messages.StringField(11)
  otherMatches = _messages.MessageField('GooglePrivacyDlpV2OtherInfoTypeSummary', 12, repeated=True)
  policyState = _messages.EnumField('PolicyStateValueValuesEnum', 13)
  profileLastGenerated = _messages.StringField(14)
  profileStatus = _messages.MessageField('GooglePrivacyDlpV2ProfileStatus', 15)
  sensitivityScore = _messages.MessageField('GooglePrivacyDlpV2SensitivityScore', 16)
  state = _messages.EnumField('StateValueValuesEnum', 17)
  tableDataProfile = _messages.StringField(18)
  tableFullResource = _messages.StringField(19)
  tableId = _messages.StringField(20)


class GooglePrivacyDlpV2Condition(_messages.Message):
  r"""The field type of `value` and `field` do not need to match to be
  considered equal, but not all comparisons are possible. EQUAL_TO and
  NOT_EQUAL_TO attempt to compare even with incompatible types, but all other
  comparisons are invalid with incompatible types. A `value` of type: -
  `string` can be compared against all other types - `boolean` can only be
  compared against other booleans - `integer` can be compared against doubles
  or a string if the string value can be parsed as an integer. - `double` can
  be compared against integers or a string if the string can be parsed as a
  double. - `Timestamp` can be compared against strings in RFC 3339 date
  string format. - `TimeOfDay` can be compared against timestamps and strings
  in the format of 'HH:mm:ss'. If we fail to compare do to type mismatch, a
  warning will be given and the condition will evaluate to false.

  Enums:
    OperatorValueValuesEnum: Required. Operator used to compare the field or
      infoType to the value.

  Fields:
    field: Required. Field within the record this condition is evaluated
      against.
    operator: Required. Operator used to compare the field or infoType to the
      value.
    value: Value to compare against. [Mandatory, except for `EXISTS` tests.]
  """

  class OperatorValueValuesEnum(_messages.Enum):
    r"""Required. Operator used to compare the field or infoType to the value.

    Values:
      RELATIONAL_OPERATOR_UNSPECIFIED: Unused
      EQUAL_TO: Equal. Attempts to match even with incompatible types.
      NOT_EQUAL_TO: Not equal to. Attempts to match even with incompatible
        types.
      GREATER_THAN: Greater than.
      LESS_THAN: Less than.
      GREATER_THAN_OR_EQUALS: Greater than or equals.
      LESS_THAN_OR_EQUALS: Less than or equals.
      EXISTS: Exists
    """
    RELATIONAL_OPERATOR_UNSPECIFIED = 0
    EQUAL_TO = 1
    NOT_EQUAL_TO = 2
    GREATER_THAN = 3
    LESS_THAN = 4
    GREATER_THAN_OR_EQUALS = 5
    LESS_THAN_OR_EQUALS = 6
    EXISTS = 7

  field = _messages.MessageField('GooglePrivacyDlpV2FieldId', 1)
  operator = _messages.EnumField('OperatorValueValuesEnum', 2)
  value = _messages.MessageField('GooglePrivacyDlpV2Value', 3)


class GooglePrivacyDlpV2Conditions(_messages.Message):
  r"""A collection of conditions.

  Fields:
    conditions: A collection of conditions.
  """

  conditions = _messages.MessageField('GooglePrivacyDlpV2Condition', 1, repeated=True)


class GooglePrivacyDlpV2Container(_messages.Message):
  r"""Represents a container that may contain DLP findings. Examples of a
  container include a file, table, or database record.

  Fields:
    fullPath: A string representation of the full container name. Examples: -
      BigQuery: 'Project:DataSetId.TableId' - Cloud Storage:
      'gs://Bucket/folders/filename.txt'
    projectId: Project where the finding was found. Can be different from the
      project that owns the finding.
    relativePath: The rest of the path after the root. Examples: - For
      BigQuery table `project_id:dataset_id.table_id`, the relative path is
      `table_id` - For Cloud Storage file `gs://bucket/folder/filename.txt`,
      the relative path is `folder/filename.txt`
    rootPath: The root of the container. Examples: - For BigQuery table
      `project_id:dataset_id.table_id`, the root is `dataset_id` - For Cloud
      Storage file `gs://bucket/folder/filename.txt`, the root is
      `gs://bucket`
    type: Container type, for example BigQuery or Cloud Storage.
    updateTime: Findings container modification timestamp, if applicable. For
      Cloud Storage, this field contains the last file modification timestamp.
      For a BigQuery table, this field contains the last_modified_time
      property. For Datastore, this field isn't populated.
    version: Findings container version, if available ("generation" for Cloud
      Storage).
  """

  fullPath = _messages.StringField(1)
  projectId = _messages.StringField(2)
  relativePath = _messages.StringField(3)
  rootPath = _messages.StringField(4)
  type = _messages.StringField(5)
  updateTime = _messages.StringField(6)
  version = _messages.StringField(7)


class GooglePrivacyDlpV2ContentItem(_messages.Message):
  r"""A GooglePrivacyDlpV2ContentItem object.

  Fields:
    byteItem: Content data to inspect or redact. Replaces `type` and `data`.
    table: Structured content for inspection. See
      https://cloud.google.com/dlp/docs/inspecting-text#inspecting_a_table to
      learn more.
    value: String data to inspect or redact.
  """

  byteItem = _messages.MessageField('GooglePrivacyDlpV2ByteContentItem', 1)
  table = _messages.MessageField('GooglePrivacyDlpV2Table', 2)
  value = _messages.StringField(3)


class GooglePrivacyDlpV2ContentLocation(_messages.Message):
  r"""Precise location of the finding within a document, record, image, or
  metadata container.

  Fields:
    containerName: Name of the container where the finding is located. The top
      level name is the source file name or table name. Names of some common
      storage containers are formatted as follows: * BigQuery tables:
      `{project_id}:{dataset_id}.{table_id}` * Cloud Storage files:
      `gs://{bucket}/{path}` * Datastore namespace: {namespace} Nested names
      could be absent if the embedded object has no string identifier (for
      example, an image contained within a document).
    containerTimestamp: Finding container modification timestamp, if
      applicable. For Cloud Storage, this field contains the last file
      modification timestamp. For a BigQuery table, this field contains the
      last_modified_time property. For Datastore, this field isn't populated.
    containerVersion: Finding container version, if available ("generation"
      for Cloud Storage).
    documentLocation: Location data for document files.
    imageLocation: Location within an image's pixels.
    metadataLocation: Location within the metadata for inspected content.
    recordLocation: Location within a row or record of a database table.
  """

  containerName = _messages.StringField(1)
  containerTimestamp = _messages.StringField(2)
  containerVersion = _messages.StringField(3)
  documentLocation = _messages.MessageField('GooglePrivacyDlpV2DocumentLocation', 4)
  imageLocation = _messages.MessageField('GooglePrivacyDlpV2ImageLocation', 5)
  metadataLocation = _messages.MessageField('GooglePrivacyDlpV2MetadataLocation', 6)
  recordLocation = _messages.MessageField('GooglePrivacyDlpV2RecordLocation', 7)


class GooglePrivacyDlpV2CreateDeidentifyTemplateRequest(_messages.Message):
  r"""Request message for CreateDeidentifyTemplate.

  Fields:
    deidentifyTemplate: Required. The DeidentifyTemplate to create.
    locationId: Deprecated. This field has no effect.
    templateId: The template id can contain uppercase and lowercase letters,
      numbers, and hyphens; that is, it must match the regular expression:
      `[a-zA-Z\d-_]+`. The maximum length is 100 characters. Can be empty to
      allow the system to generate one.
  """

  deidentifyTemplate = _messages.MessageField('GooglePrivacyDlpV2DeidentifyTemplate', 1)
  locationId = _messages.StringField(2)
  templateId = _messages.StringField(3)


class GooglePrivacyDlpV2CreateDlpJobRequest(_messages.Message):
  r"""Request message for CreateDlpJobRequest. Used to initiate long running
  jobs such as calculating risk metrics or inspecting Google Cloud Storage.

  Fields:
    inspectJob: An inspection job scans a storage repository for InfoTypes.
    jobId: The job id can contain uppercase and lowercase letters, numbers,
      and hyphens; that is, it must match the regular expression:
      `[a-zA-Z\d-_]+`. The maximum length is 100 characters. Can be empty to
      allow the system to generate one.
    locationId: Deprecated. This field has no effect.
    riskJob: A risk analysis job calculates re-identification risk metrics for
      a BigQuery table.
  """

  inspectJob = _messages.MessageField('GooglePrivacyDlpV2InspectJobConfig', 1)
  jobId = _messages.StringField(2)
  locationId = _messages.StringField(3)
  riskJob = _messages.MessageField('GooglePrivacyDlpV2RiskAnalysisJobConfig', 4)


class GooglePrivacyDlpV2CreateInspectTemplateRequest(_messages.Message):
  r"""Request message for CreateInspectTemplate.

  Fields:
    inspectTemplate: Required. The InspectTemplate to create.
    locationId: Deprecated. This field has no effect.
    templateId: The template id can contain uppercase and lowercase letters,
      numbers, and hyphens; that is, it must match the regular expression:
      `[a-zA-Z\d-_]+`. The maximum length is 100 characters. Can be empty to
      allow the system to generate one.
  """

  inspectTemplate = _messages.MessageField('GooglePrivacyDlpV2InspectTemplate', 1)
  locationId = _messages.StringField(2)
  templateId = _messages.StringField(3)


class GooglePrivacyDlpV2CreateJobTriggerRequest(_messages.Message):
  r"""Request message for CreateJobTrigger.

  Fields:
    jobTrigger: Required. The JobTrigger to create.
    locationId: Deprecated. This field has no effect.
    triggerId: The trigger id can contain uppercase and lowercase letters,
      numbers, and hyphens; that is, it must match the regular expression:
      `[a-zA-Z\d-_]+`. The maximum length is 100 characters. Can be empty to
      allow the system to generate one.
  """

  jobTrigger = _messages.MessageField('GooglePrivacyDlpV2JobTrigger', 1)
  locationId = _messages.StringField(2)
  triggerId = _messages.StringField(3)


class GooglePrivacyDlpV2CreateStoredInfoTypeRequest(_messages.Message):
  r"""Request message for CreateStoredInfoType.

  Fields:
    config: Required. Configuration of the storedInfoType to create.
    locationId: Deprecated. This field has no effect.
    storedInfoTypeId: The storedInfoType ID can contain uppercase and
      lowercase letters, numbers, and hyphens; that is, it must match the
      regular expression: `[a-zA-Z\d-_]+`. The maximum length is 100
      characters. Can be empty to allow the system to generate one.
  """

  config = _messages.MessageField('GooglePrivacyDlpV2StoredInfoTypeConfig', 1)
  locationId = _messages.StringField(2)
  storedInfoTypeId = _messages.StringField(3)


class GooglePrivacyDlpV2CryptoDeterministicConfig(_messages.Message):
  r"""Pseudonymization method that generates deterministic encryption for the
  given input. Outputs a base64 encoded representation of the encrypted
  output. Uses AES-SIV based on the RFC https://tools.ietf.org/html/rfc5297.

  Fields:
    context: A context may be used for higher security and maintaining
      referential integrity such that the same identifier in two different
      contexts will be given a distinct surrogate. The context is appended to
      plaintext value being encrypted. On decryption the provided context is
      validated against the value used during encryption. If a context was
      provided during encryption, same context must be provided during
      decryption as well. If the context is not set, plaintext would be used
      as is for encryption. If the context is set but: 1. there is no record
      present when transforming a given value or 2. the field is not present
      when transforming a given value, plaintext would be used as is for
      encryption. Note that case (1) is expected when an
      `InfoTypeTransformation` is applied to both structured and unstructured
      `ContentItem`s.
    cryptoKey: The key used by the encryption function. For deterministic
      encryption using AES-SIV, the provided key is internally expanded to 64
      bytes prior to use.
    surrogateInfoType: The custom info type to annotate the surrogate with.
      This annotation will be applied to the surrogate by prefixing it with
      the name of the custom info type followed by the number of characters
      comprising the surrogate. The following scheme defines the format: {info
      type name}({surrogate character count}):{surrogate} For example, if the
      name of custom info type is 'MY_TOKEN_INFO_TYPE' and the surrogate is
      'abc', the full replacement value will be: 'MY_TOKEN_INFO_TYPE(3):abc'
      This annotation identifies the surrogate when inspecting content using
      the custom info type 'Surrogate'. This facilitates reversal of the
      surrogate when it occurs in free text. Note: For record transformations
      where the entire cell in a table is being transformed, surrogates are
      not mandatory. Surrogates are used to denote the location of the token
      and are necessary for re-identification in free form text. In order for
      inspection to work properly, the name of this info type must not occur
      naturally anywhere in your data; otherwise, inspection may either -
      reverse a surrogate that does not correspond to an actual identifier -
      be unable to parse the surrogate and result in an error Therefore,
      choose your custom info type name carefully after considering what your
      data looks like. One way to select a name that has a high chance of
      yielding reliable detection is to include one or more unicode characters
      that are highly improbable to exist in your data. For example, assuming
      your data is entered from a regular ASCII keyboard, the symbol with the
      hex code point 29DD might be used like so: \u29ddMY_TOKEN_TYPE.
  """

  context = _messages.MessageField('GooglePrivacyDlpV2FieldId', 1)
  cryptoKey = _messages.MessageField('GooglePrivacyDlpV2CryptoKey', 2)
  surrogateInfoType = _messages.MessageField('GooglePrivacyDlpV2InfoType', 3)


class GooglePrivacyDlpV2CryptoHashConfig(_messages.Message):
  r"""Pseudonymization method that generates surrogates via cryptographic
  hashing. Uses SHA-256. The key size must be either 32 or 64 bytes. Outputs a
  base64 encoded representation of the hashed output (for example,
  L7k0BHmF1ha5U3NfGykjro4xWi1MPVQPjhMAZbSV9mM=). Currently, only string and
  integer values can be hashed. See
  https://cloud.google.com/dlp/docs/pseudonymization to learn more.

  Fields:
    cryptoKey: The key used by the hash function.
  """

  cryptoKey = _messages.MessageField('GooglePrivacyDlpV2CryptoKey', 1)


class GooglePrivacyDlpV2CryptoKey(_messages.Message):
  r"""This is a data encryption key (DEK) (as opposed to a key encryption key
  (KEK) stored by Cloud Key Management Service (Cloud KMS). When using Cloud
  KMS to wrap or unwrap a DEK, be sure to set an appropriate IAM policy on the
  KEK to ensure an attacker cannot unwrap the DEK.

  Fields:
    kmsWrapped: Key wrapped using Cloud KMS
    transient: Transient crypto key
    unwrapped: Unwrapped crypto key
  """

  kmsWrapped = _messages.MessageField('GooglePrivacyDlpV2KmsWrappedCryptoKey', 1)
  transient = _messages.MessageField('GooglePrivacyDlpV2TransientCryptoKey', 2)
  unwrapped = _messages.MessageField('GooglePrivacyDlpV2UnwrappedCryptoKey', 3)


class GooglePrivacyDlpV2CryptoReplaceFfxFpeConfig(_messages.Message):
  r"""Replaces an identifier with a surrogate using Format Preserving
  Encryption (FPE) with the FFX mode of operation; however when used in the
  `ReidentifyContent` API method, it serves the opposite function by reversing
  the surrogate back into the original identifier. The identifier must be
  encoded as ASCII. For a given crypto key and context, the same identifier
  will be replaced with the same surrogate. Identifiers must be at least two
  characters long. In the case that the identifier is the empty string, it
  will be skipped. See https://cloud.google.com/dlp/docs/pseudonymization to
  learn more. Note: We recommend using CryptoDeterministicConfig for all use
  cases which do not require preserving the input alphabet space and size,
  plus warrant referential integrity.

  Enums:
    CommonAlphabetValueValuesEnum: Common alphabets.

  Fields:
    commonAlphabet: Common alphabets.
    context: The 'tweak', a context may be used for higher security since the
      same identifier in two different contexts won't be given the same
      surrogate. If the context is not set, a default tweak will be used. If
      the context is set but: 1. there is no record present when transforming
      a given value or 1. the field is not present when transforming a given
      value, a default tweak will be used. Note that case (1) is expected when
      an `InfoTypeTransformation` is applied to both structured and
      unstructured `ContentItem`s. Currently, the referenced field may be of
      value type integer or string. The tweak is constructed as a sequence of
      bytes in big endian byte order such that: - a 64 bit integer is encoded
      followed by a single byte of value 1 - a string is encoded in UTF-8
      format followed by a single byte of value 2
    cryptoKey: Required. The key used by the encryption algorithm.
    customAlphabet: This is supported by mapping these to the alphanumeric
      characters that the FFX mode natively supports. This happens
      before/after encryption/decryption. Each character listed must appear
      only once. Number of characters must be in the range [2, 95]. This must
      be encoded as ASCII. The order of characters does not matter. The full
      list of allowed characters is:
      0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz
      ~`!@#$%^&*()_-+={[}]|\:;"'<,>.?/
    radix: The native way to select the alphabet. Must be in the range [2,
      95].
    surrogateInfoType: The custom infoType to annotate the surrogate with.
      This annotation will be applied to the surrogate by prefixing it with
      the name of the custom infoType followed by the number of characters
      comprising the surrogate. The following scheme defines the format:
      info_type_name(surrogate_character_count):surrogate For example, if the
      name of custom infoType is 'MY_TOKEN_INFO_TYPE' and the surrogate is
      'abc', the full replacement value will be: 'MY_TOKEN_INFO_TYPE(3):abc'
      This annotation identifies the surrogate when inspecting content using
      the custom infoType [`SurrogateType`](https://cloud.google.com/dlp/docs/
      reference/rest/v2/InspectConfig#surrogatetype). This facilitates
      reversal of the surrogate when it occurs in free text. In order for
      inspection to work properly, the name of this infoType must not occur
      naturally anywhere in your data; otherwise, inspection may find a
      surrogate that does not correspond to an actual identifier. Therefore,
      choose your custom infoType name carefully after considering what your
      data looks like. One way to select a name that has a high chance of
      yielding reliable detection is to include one or more unicode characters
      that are highly improbable to exist in your data. For example, assuming
      your data is entered from a regular ASCII keyboard, the symbol with the
      hex code point 29DD might be used like so: \u29ddMY_TOKEN_TYPE
  """

  class CommonAlphabetValueValuesEnum(_messages.Enum):
    r"""Common alphabets.

    Values:
      FFX_COMMON_NATIVE_ALPHABET_UNSPECIFIED: Unused.
      NUMERIC: `[0-9]` (radix of 10)
      HEXADECIMAL: `[0-9A-F]` (radix of 16)
      UPPER_CASE_ALPHA_NUMERIC: `[0-9A-Z]` (radix of 36)
      ALPHA_NUMERIC: `[0-9A-Za-z]` (radix of 62)
    """
    FFX_COMMON_NATIVE_ALPHABET_UNSPECIFIED = 0
    NUMERIC = 1
    HEXADECIMAL = 2
    UPPER_CASE_ALPHA_NUMERIC = 3
    ALPHA_NUMERIC = 4

  commonAlphabet = _messages.EnumField('CommonAlphabetValueValuesEnum', 1)
  context = _messages.MessageField('GooglePrivacyDlpV2FieldId', 2)
  cryptoKey = _messages.MessageField('GooglePrivacyDlpV2CryptoKey', 3)
  customAlphabet = _messages.StringField(4)
  radix = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  surrogateInfoType = _messages.MessageField('GooglePrivacyDlpV2InfoType', 6)


class GooglePrivacyDlpV2CustomInfoType(_messages.Message):
  r"""Custom information type provided by the user. Used to find domain-
  specific sensitive information configurable to the data in question.

  Enums:
    ExclusionTypeValueValuesEnum: If set to EXCLUSION_TYPE_EXCLUDE this
      infoType will not cause a finding to be returned. It still can be used
      for rules matching.
    LikelihoodValueValuesEnum: Likelihood to return for this CustomInfoType.
      This base value can be altered by a detection rule if the finding meets
      the criteria specified by the rule. Defaults to `VERY_LIKELY` if not
      specified.

  Fields:
    detectionRules: Set of detection rules to apply to all findings of this
      CustomInfoType. Rules are applied in order that they are specified. Not
      supported for the `surrogate_type` CustomInfoType.
    dictionary: A list of phrases to detect as a CustomInfoType.
    exclusionType: If set to EXCLUSION_TYPE_EXCLUDE this infoType will not
      cause a finding to be returned. It still can be used for rules matching.
    infoType: CustomInfoType can either be a new infoType, or an extension of
      built-in infoType, when the name matches one of existing infoTypes and
      that infoType is specified in `InspectContent.info_types` field.
      Specifying the latter adds findings to the one detected by the system.
      If built-in info type is not specified in `InspectContent.info_types`
      list then the name is treated as a custom info type.
    likelihood: Likelihood to return for this CustomInfoType. This base value
      can be altered by a detection rule if the finding meets the criteria
      specified by the rule. Defaults to `VERY_LIKELY` if not specified.
    regex: Regular expression based CustomInfoType.
    sensitivityScore: Sensitivity for this CustomInfoType. If this
      CustomInfoType extends an existing InfoType, the sensitivity here will
      take precedent over that of the original InfoType. If unset for a
      CustomInfoType, it will default to HIGH. This only applies to data
      profiling.
    storedType: Load an existing `StoredInfoType` resource for use in
      `InspectDataSource`. Not currently supported in `InspectContent`.
    surrogateType: Message for detecting output from deidentification
      transformations that support reversing.
  """

  class ExclusionTypeValueValuesEnum(_messages.Enum):
    r"""If set to EXCLUSION_TYPE_EXCLUDE this infoType will not cause a
    finding to be returned. It still can be used for rules matching.

    Values:
      EXCLUSION_TYPE_UNSPECIFIED: A finding of this custom info type will not
        be excluded from results.
      EXCLUSION_TYPE_EXCLUDE: A finding of this custom info type will be
        excluded from final results, but can still affect rule execution.
    """
    EXCLUSION_TYPE_UNSPECIFIED = 0
    EXCLUSION_TYPE_EXCLUDE = 1

  class LikelihoodValueValuesEnum(_messages.Enum):
    r"""Likelihood to return for this CustomInfoType. This base value can be
    altered by a detection rule if the finding meets the criteria specified by
    the rule. Defaults to `VERY_LIKELY` if not specified.

    Values:
      LIKELIHOOD_UNSPECIFIED: Default value; same as POSSIBLE.
      VERY_UNLIKELY: Highest chance of a false positive.
      UNLIKELY: High chance of a false positive.
      POSSIBLE: Some matching signals. The default value.
      LIKELY: Low chance of a false positive.
      VERY_LIKELY: Confidence level is high. Lowest chance of a false
        positive.
    """
    LIKELIHOOD_UNSPECIFIED = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  detectionRules = _messages.MessageField('GooglePrivacyDlpV2DetectionRule', 1, repeated=True)
  dictionary = _messages.MessageField('GooglePrivacyDlpV2Dictionary', 2)
  exclusionType = _messages.EnumField('ExclusionTypeValueValuesEnum', 3)
  infoType = _messages.MessageField('GooglePrivacyDlpV2InfoType', 4)
  likelihood = _messages.EnumField('LikelihoodValueValuesEnum', 5)
  regex = _messages.MessageField('GooglePrivacyDlpV2Regex', 6)
  sensitivityScore = _messages.MessageField('GooglePrivacyDlpV2SensitivityScore', 7)
  storedType = _messages.MessageField('GooglePrivacyDlpV2StoredType', 8)
  surrogateType = _messages.MessageField('GooglePrivacyDlpV2SurrogateType', 9)


class GooglePrivacyDlpV2DataProfileAction(_messages.Message):
  r"""A task to execute when a data profile has been generated.

  Fields:
    exportData: Export data profiles into a provided location.
    pubSubNotification: Publish a message into the Pub/Sub topic.
  """

  exportData = _messages.MessageField('GooglePrivacyDlpV2Export', 1)
  pubSubNotification = _messages.MessageField('GooglePrivacyDlpV2PubSubNotification', 2)


class GooglePrivacyDlpV2DataProfileBigQueryRowSchema(_messages.Message):
  r"""The schema of data to be saved to the BigQuery when the
  `DataProfileAction` is enabled.

  Fields:
    columnProfile: Column data profile column
    tableProfile: Table data profile column
  """

  columnProfile = _messages.MessageField('GooglePrivacyDlpV2ColumnDataProfile', 1)
  tableProfile = _messages.MessageField('GooglePrivacyDlpV2TableDataProfile', 2)


class GooglePrivacyDlpV2DataProfileConfigSnapshot(_messages.Message):
  r"""Snapshot of the configurations used to generate the profile.

  Fields:
    dataProfileJob: A copy of the configuration used to generate this profile.
    inspectConfig: A copy of the inspection config used to generate this
      profile. This is a copy of the inspect_template specified in
      `DataProfileJobConfig`.
  """

  dataProfileJob = _messages.MessageField('GooglePrivacyDlpV2DataProfileJobConfig', 1)
  inspectConfig = _messages.MessageField('GooglePrivacyDlpV2InspectConfig', 2)


class GooglePrivacyDlpV2DataProfileJobConfig(_messages.Message):
  r"""Configuration for setting up a job to scan resources for profile
  generation. Only one data profile configuration may exist per organization,
  folder, or project. The generated data profiles are retained according to
  the [data retention policy] (https://cloud.google.com/dlp/docs/data-
  profiles#retention).

  Fields:
    dataProfileActions: Actions to execute at the completion of the job.
    inspectTemplates: Detection logic for profile generation. Not all template
      features are used by profiles. FindingLimits, include_quote and
      exclude_info_types have no impact on data profiling. Multiple templates
      may be provided if there is data in multiple regions. At most one
      template must be specified per-region (including "global"). Each region
      is scanned using the applicable template. If no region-specific template
      is specified, but a "global" template is specified, it will be copied to
      that region and used instead. If no global or region-specific template
      is provided for a region with data, that region's data will not be
      scanned. For more information, see
      https://cloud.google.com/dlp/docs/data-profiles#data_residency.
    location: The data to scan.
    projectId: The project that will run the scan. The DLP service account
      that exists within this project must have access to all resources that
      are profiled, and the Cloud DLP API must be enabled.
  """

  dataProfileActions = _messages.MessageField('GooglePrivacyDlpV2DataProfileAction', 1, repeated=True)
  inspectTemplates = _messages.StringField(2, repeated=True)
  location = _messages.MessageField('GooglePrivacyDlpV2DataProfileLocation', 3)
  projectId = _messages.StringField(4)


class GooglePrivacyDlpV2DataProfileLocation(_messages.Message):
  r"""The data that will be profiled.

  Fields:
    folderId: The ID of the Folder within an organization to scan.
    organizationId: The ID of an organization to scan.
  """

  folderId = _messages.IntegerField(1)
  organizationId = _messages.IntegerField(2)


class GooglePrivacyDlpV2DataProfilePubSubCondition(_messages.Message):
  r"""A condition for determining whether a Pub/Sub should be triggered.

  Fields:
    expressions: An expression.
  """

  expressions = _messages.MessageField('GooglePrivacyDlpV2PubSubExpressions', 1)


class GooglePrivacyDlpV2DataProfilePubSubMessage(_messages.Message):
  r"""Pub/Sub topic message for a DataProfileAction.PubSubNotification event.
  To receive a message of protocol buffer schema type, convert the message
  data to an object of this proto class.

  Enums:
    EventValueValuesEnum: The event that caused the Pub/Sub message to be
      sent.

  Fields:
    event: The event that caused the Pub/Sub message to be sent.
    profile: If `DetailLevel` is `TABLE_PROFILE` this will be fully populated.
      Otherwise, if `DetailLevel` is `RESOURCE_NAME`, then only `name` and
      `full_resource` will be populated.
  """

  class EventValueValuesEnum(_messages.Enum):
    r"""The event that caused the Pub/Sub message to be sent.

    Values:
      EVENT_TYPE_UNSPECIFIED: Unused.
      NEW_PROFILE: New profile (not a re-profile).
      CHANGED_PROFILE: Changed one of the following profile metrics: * Table
        data risk score * Table sensitivity score * Table resource visibility
        * Table encryption type * Table predicted infoTypes * Table other
        infoTypes
      SCORE_INCREASED: Table data risk score or sensitivity score increased.
      ERROR_CHANGED: A user (non-internal) error occurred.
    """
    EVENT_TYPE_UNSPECIFIED = 0
    NEW_PROFILE = 1
    CHANGED_PROFILE = 2
    SCORE_INCREASED = 3
    ERROR_CHANGED = 4

  event = _messages.EnumField('EventValueValuesEnum', 1)
  profile = _messages.MessageField('GooglePrivacyDlpV2TableDataProfile', 2)


class GooglePrivacyDlpV2DataRiskLevel(_messages.Message):
  r"""Score is a summary of all elements in the data profile. A higher number
  means more risk.

  Enums:
    ScoreValueValuesEnum: The score applied to the resource.

  Fields:
    score: The score applied to the resource.
  """

  class ScoreValueValuesEnum(_messages.Enum):
    r"""The score applied to the resource.

    Values:
      RISK_SCORE_UNSPECIFIED: Unused.
      RISK_LOW: Low risk - Lower indication of sensitive data that appears to
        have additional access restrictions in place or no indication of
        sensitive data found.
      RISK_MODERATE: Medium risk - Sensitive data may be present but
        additional access or fine grain access restrictions appear to be
        present. Consider limiting access even further or transform data to
        mask.
      RISK_HIGH: High risk \u2013 SPII may be present. Access controls may
        include public ACLs. Exfiltration of data may lead to user data loss.
        Re-identification of users may be possible. Consider limiting usage
        and or removing SPII.
    """
    RISK_SCORE_UNSPECIFIED = 0
    RISK_LOW = 1
    RISK_MODERATE = 2
    RISK_HIGH = 3

  score = _messages.EnumField('ScoreValueValuesEnum', 1)


class GooglePrivacyDlpV2DatastoreKey(_messages.Message):
  r"""Record key for a finding in Cloud Datastore.

  Fields:
    entityKey: Datastore entity key.
  """

  entityKey = _messages.MessageField('GooglePrivacyDlpV2Key', 1)


class GooglePrivacyDlpV2DatastoreOptions(_messages.Message):
  r"""Options defining a data set within Google Cloud Datastore.

  Fields:
    kind: The kind to process.
    partitionId: A partition ID identifies a grouping of entities. The
      grouping is always by project and namespace, however the namespace ID
      may be empty.
  """

  kind = _messages.MessageField('GooglePrivacyDlpV2KindExpression', 1)
  partitionId = _messages.MessageField('GooglePrivacyDlpV2PartitionId', 2)


class GooglePrivacyDlpV2DateShiftConfig(_messages.Message):
  r"""Shifts dates by random number of days, with option to be consistent for
  the same context. See https://cloud.google.com/dlp/docs/concepts-date-
  shifting to learn more.

  Fields:
    context: Points to the field that contains the context, for example, an
      entity id. If set, must also set cryptoKey. If set, shift will be
      consistent for the given context.
    cryptoKey: Causes the shift to be computed based on this key and the
      context. This results in the same shift for the same context and
      crypto_key. If set, must also set context. Can only be applied to table
      items.
    lowerBoundDays: Required. For example, -5 means shift date to at most 5
      days back in the past.
    upperBoundDays: Required. Range of shift in days. Actual shift will be
      selected at random within this range (inclusive ends). Negative means
      shift to earlier in time. Must not be more than 365250 days (1000 years)
      each direction. For example, 3 means shift date to at most 3 days into
      the future.
  """

  context = _messages.MessageField('GooglePrivacyDlpV2FieldId', 1)
  cryptoKey = _messages.MessageField('GooglePrivacyDlpV2CryptoKey', 2)
  lowerBoundDays = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  upperBoundDays = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GooglePrivacyDlpV2DateTime(_messages.Message):
  r"""Message for a date time object. e.g. 2018-01-01, 5th August.

  Enums:
    DayOfWeekValueValuesEnum: Day of week

  Fields:
    date: One or more of the following must be set. Must be a valid date or
      time value.
    dayOfWeek: Day of week
    time: Time of day
    timeZone: Time zone
  """

  class DayOfWeekValueValuesEnum(_messages.Enum):
    r"""Day of week

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  date = _messages.MessageField('GoogleTypeDate', 1)
  dayOfWeek = _messages.EnumField('DayOfWeekValueValuesEnum', 2)
  time = _messages.MessageField('GoogleTypeTimeOfDay', 3)
  timeZone = _messages.MessageField('GooglePrivacyDlpV2TimeZone', 4)


class GooglePrivacyDlpV2Deidentify(_messages.Message):
  r"""Create a de-identified copy of the requested table or files. A
  TransformationDetail will be created for each transformation. If any rows in
  BigQuery are skipped during de-identification (transformation errors or row
  size exceeds BigQuery insert API limits) they are placed in the failure
  output table. If the original row exceeds the BigQuery insert API limit it
  will be truncated when written to the failure output table. The failure
  output table can be set in the
  action.deidentify.output.big_query_output.deidentified_failure_output_table
  field, if no table is set, a table will be automatically created in the same
  project and dataset as the original table. Compatible with: Inspect

  Enums:
    FileTypesToTransformValueListEntryValuesEnum:

  Fields:
    cloudStorageOutput: Required. User settable Cloud Storage bucket and
      folders to store de-identified files. This field must be set for cloud
      storage deidentification. The output Cloud Storage bucket must be
      different from the input bucket. De-identified files will overwrite
      files in the output path. Form of: gs://bucket/folder/ or gs://bucket
    fileTypesToTransform: List of user-specified file type groups to
      transform. If specified, only the files with these filetypes will be
      transformed. If empty, all supported files will be transformed.
      Supported types may be automatically added over time. If a file type is
      set in this field that isn't supported by the Deidentify action then the
      job will fail and will not be successfully created/started. Currently
      the only filetypes supported are: IMAGES, TEXT_FILES, CSV, TSV.
    transformationConfig: User specified deidentify templates and configs for
      structured, unstructured, and image files.
    transformationDetailsStorageConfig: Config for storing transformation
      details. This is separate from the de-identified content, and contains
      metadata about the successful transformations and/or failures that
      occurred while de-identifying. This needs to be set in order for users
      to access information about the status of each transformation (see
      TransformationDetails message for more information about what is noted).
  """

  class FileTypesToTransformValueListEntryValuesEnum(_messages.Enum):
    r"""FileTypesToTransformValueListEntryValuesEnum enum type.

    Values:
      FILE_TYPE_UNSPECIFIED: Includes all files.
      BINARY_FILE: Includes all file extensions not covered by another entry.
        Binary scanning attempts to convert the content of the file to utf_8
        to scan the file. If you wish to avoid this fall back, specify one or
        more of the other file types in your storage scan.
      TEXT_FILE: Included file extensions: asc,asp, aspx, brf, c, cc,cfm, cgi,
        cpp, csv, cxx, c++, cs, css, dart, dat, dot, eml,, epbub, ged, go, h,
        hh, hpp, hxx, h++, hs, html, htm, mkd, markdown, m, ml, mli, perl, pl,
        plist, pm, php, phtml, pht, properties, py, pyw, rb, rbw, rs, rss, rc,
        scala, sh, sql, swift, tex, shtml, shtm, xhtml, lhs, ics, ini, java,
        js, json, kix, kml, ocaml, md, txt, text, tsv, vb, vcard, vcs, wml,
        xcodeproj, xml, xsl, xsd, yml, yaml.
      IMAGE: Included file extensions: bmp, gif, jpg, jpeg, jpe, png. Setting
        bytes_limit_per_file or bytes_limit_per_file_percent has no effect on
        image files. Image inspection is restricted to the `global`, `us`,
        `asia`, and `europe` regions.
      WORD: Microsoft Word files larger than 30 MB will be scanned as binary
        files. Included file extensions: docx, dotx, docm, dotm. Setting
        `bytes_limit_per_file` or `bytes_limit_per_file_percent` has no effect
        on Word files.
      PDF: PDF files larger than 30 MB will be scanned as binary files.
        Included file extensions: pdf. Setting `bytes_limit_per_file` or
        `bytes_limit_per_file_percent` has no effect on PDF files.
      AVRO: Included file extensions: avro
      CSV: Included file extensions: csv
      TSV: Included file extensions: tsv
      POWERPOINT: Microsoft PowerPoint files larger than 30 MB will be scanned
        as binary files. Included file extensions: pptx, pptm, potx, potm,
        pot. Setting `bytes_limit_per_file` or `bytes_limit_per_file_percent`
        has no effect on PowerPoint files.
      EXCEL: Microsoft Excel files larger than 30 MB will be scanned as binary
        files. Included file extensions: xlsx, xlsm, xltx, xltm. Setting
        `bytes_limit_per_file` or `bytes_limit_per_file_percent` has no effect
        on Excel files.
    """
    FILE_TYPE_UNSPECIFIED = 0
    BINARY_FILE = 1
    TEXT_FILE = 2
    IMAGE = 3
    WORD = 4
    PDF = 5
    AVRO = 6
    CSV = 7
    TSV = 8
    POWERPOINT = 9
    EXCEL = 10

  cloudStorageOutput = _messages.StringField(1)
  fileTypesToTransform = _messages.EnumField('FileTypesToTransformValueListEntryValuesEnum', 2, repeated=True)
  transformationConfig = _messages.MessageField('GooglePrivacyDlpV2TransformationConfig', 3)
  transformationDetailsStorageConfig = _messages.MessageField('GooglePrivacyDlpV2TransformationDetailsStorageConfig', 4)


class GooglePrivacyDlpV2DeidentifyConfig(_messages.Message):
  r"""The configuration that controls how the data will change.

  Fields:
    imageTransformations: Treat the dataset as an image and redact.
    infoTypeTransformations: Treat the dataset as free-form text and apply the
      same free text transformation everywhere.
    recordTransformations: Treat the dataset as structured. Transformations
      can be applied to specific locations within structured datasets, such as
      transforming a column within a table.
    transformationErrorHandling: Mode for handling transformation errors. If
      left unspecified, the default mode is
      `TransformationErrorHandling.ThrowError`.
  """

  imageTransformations = _messages.MessageField('GooglePrivacyDlpV2ImageTransformations', 1)
  infoTypeTransformations = _messages.MessageField('GooglePrivacyDlpV2InfoTypeTransformations', 2)
  recordTransformations = _messages.MessageField('GooglePrivacyDlpV2RecordTransformations', 3)
  transformationErrorHandling = _messages.MessageField('GooglePrivacyDlpV2TransformationErrorHandling', 4)


class GooglePrivacyDlpV2DeidentifyContentRequest(_messages.Message):
  r"""Request to de-identify a ContentItem.

  Fields:
    deidentifyConfig: Configuration for the de-identification of the content
      item. Items specified here will override the template referenced by the
      deidentify_template_name argument.
    deidentifyTemplateName: Template to use. Any configuration directly
      specified in deidentify_config will override those set in the template.
      Singular fields that are set in this request will replace their
      corresponding fields in the template. Repeated fields are appended.
      Singular sub-messages and groups are recursively merged.
    inspectConfig: Configuration for the inspector. Items specified here will
      override the template referenced by the inspect_template_name argument.
    inspectTemplateName: Template to use. Any configuration directly specified
      in inspect_config will override those set in the template. Singular
      fields that are set in this request will replace their corresponding
      fields in the template. Repeated fields are appended. Singular sub-
      messages and groups are recursively merged.
    item: The item to de-identify. Will be treated as text. This value must be
      of type Table if your deidentify_config is a RecordTransformations
      object.
    locationId: Deprecated. This field has no effect.
  """

  deidentifyConfig = _messages.MessageField('GooglePrivacyDlpV2DeidentifyConfig', 1)
  deidentifyTemplateName = _messages.StringField(2)
  inspectConfig = _messages.MessageField('GooglePrivacyDlpV2InspectConfig', 3)
  inspectTemplateName = _messages.StringField(4)
  item = _messages.MessageField('GooglePrivacyDlpV2ContentItem', 5)
  locationId = _messages.StringField(6)


class GooglePrivacyDlpV2DeidentifyContentResponse(_messages.Message):
  r"""Results of de-identifying a ContentItem.

  Fields:
    item: The de-identified item.
    overview: An overview of the changes that were made on the `item`.
  """

  item = _messages.MessageField('GooglePrivacyDlpV2ContentItem', 1)
  overview = _messages.MessageField('GooglePrivacyDlpV2TransformationOverview', 2)


class GooglePrivacyDlpV2DeidentifyDataSourceDetails(_messages.Message):
  r"""The results of a Deidentify action from an inspect job.

  Fields:
    deidentifyStats: Stats about the de-identification operation.
    requestedOptions: De-identification config used for the request.
  """

  deidentifyStats = _messages.MessageField('GooglePrivacyDlpV2DeidentifyDataSourceStats', 1)
  requestedOptions = _messages.MessageField('GooglePrivacyDlpV2RequestedDeidentifyOptions', 2)


class GooglePrivacyDlpV2DeidentifyDataSourceStats(_messages.Message):
  r"""Summary of what was modified during a transformation.

  Fields:
    transformationCount: Number of successfully applied transformations.
    transformationErrorCount: Number of errors encountered while trying to
      apply transformations.
    transformedBytes: Total size in bytes that were transformed in some way.
  """

  transformationCount = _messages.IntegerField(1)
  transformationErrorCount = _messages.IntegerField(2)
  transformedBytes = _messages.IntegerField(3)


class GooglePrivacyDlpV2DeidentifyTemplate(_messages.Message):
  r"""DeidentifyTemplates contains instructions on how to de-identify content.
  See https://cloud.google.com/dlp/docs/concepts-templates to learn more.

  Fields:
    createTime: Output only. The creation timestamp of an inspectTemplate.
    deidentifyConfig: The core content of the template.
    description: Short description (max 256 chars).
    displayName: Display name (max 256 chars).
    name: Output only. The template name. The template will have one of the
      following formats: `projects/PROJECT_ID/deidentifyTemplates/TEMPLATE_ID`
      OR `organizations/ORGANIZATION_ID/deidentifyTemplates/TEMPLATE_ID`
    updateTime: Output only. The last update timestamp of an inspectTemplate.
  """

  createTime = _messages.StringField(1)
  deidentifyConfig = _messages.MessageField('GooglePrivacyDlpV2DeidentifyConfig', 2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  name = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class GooglePrivacyDlpV2DeltaPresenceEstimationConfig(_messages.Message):
  r"""\u03b4-presence metric, used to estimate how likely it is for an
  attacker to figure out that one given individual appears in a de-identified
  dataset. Similarly to the k-map metric, we cannot compute \u03b4-presence
  exactly without knowing the attack dataset, so we use a statistical model
  instead.

  Fields:
    auxiliaryTables: Several auxiliary tables can be used in the analysis.
      Each custom_tag used to tag a quasi-identifiers field must appear in
      exactly one field of one auxiliary table.
    quasiIds: Required. Fields considered to be quasi-identifiers. No two
      fields can have the same tag.
    regionCode: ISO 3166-1 alpha-2 region code to use in the statistical
      modeling. Set if no column is tagged with a region-specific InfoType
      (like US_ZIP_5) or a region code.
  """

  auxiliaryTables = _messages.MessageField('GooglePrivacyDlpV2StatisticalTable', 1, repeated=True)
  quasiIds = _messages.MessageField('GooglePrivacyDlpV2QuasiId', 2, repeated=True)
  regionCode = _messages.StringField(3)


class GooglePrivacyDlpV2DeltaPresenceEstimationHistogramBucket(_messages.Message):
  r"""A DeltaPresenceEstimationHistogramBucket message with the following
  values: min_probability: 0.1 max_probability: 0.2 frequency: 42 means that
  there are 42 records for which \u03b4 is in [0.1, 0.2). An important
  particular case is when min_probability = max_probability = 1: then, every
  individual who shares this quasi-identifier combination is in the dataset.

  Fields:
    bucketSize: Number of records within these probability bounds.
    bucketValueCount: Total number of distinct quasi-identifier tuple values
      in this bucket.
    bucketValues: Sample of quasi-identifier tuple values in this bucket. The
      total number of classes returned per bucket is capped at 20.
    maxProbability: Always greater than or equal to min_probability.
    minProbability: Between 0 and 1.
  """

  bucketSize = _messages.IntegerField(1)
  bucketValueCount = _messages.IntegerField(2)
  bucketValues = _messages.MessageField('GooglePrivacyDlpV2DeltaPresenceEstimationQuasiIdValues', 3, repeated=True)
  maxProbability = _messages.FloatField(4)
  minProbability = _messages.FloatField(5)


class GooglePrivacyDlpV2DeltaPresenceEstimationQuasiIdValues(_messages.Message):
  r"""A tuple of values for the quasi-identifier columns.

  Fields:
    estimatedProbability: The estimated probability that a given individual
      sharing these quasi-identifier values is in the dataset. This value,
      typically called \u03b4, is the ratio between the number of records in
      the dataset with these quasi-identifier values, and the total number of
      individuals (inside *and* outside the dataset) with these quasi-
      identifier values. For example, if there are 15 individuals in the
      dataset who share the same quasi-identifier values, and an estimated 100
      people in the entire population with these values, then \u03b4 is 0.15.
    quasiIdsValues: The quasi-identifier values.
  """

  estimatedProbability = _messages.FloatField(1)
  quasiIdsValues = _messages.MessageField('GooglePrivacyDlpV2Value', 2, repeated=True)


class GooglePrivacyDlpV2DeltaPresenceEstimationResult(_messages.Message):
  r"""Result of the \u03b4-presence computation. Note that these results are
  an estimation, not exact values.

  Fields:
    deltaPresenceEstimationHistogram: The intervals [min_probability,
      max_probability) do not overlap. If a value doesn't correspond to any
      such interval, the associated frequency is zero. For example, the
      following records: {min_probability: 0, max_probability: 0.1, frequency:
      17} {min_probability: 0.2, max_probability: 0.3, frequency: 42}
      {min_probability: 0.3, max_probability: 0.4, frequency: 99} mean that
      there are no record with an estimated probability in [0.1, 0.2) nor
      larger or equal to 0.4.
  """

  deltaPresenceEstimationHistogram = _messages.MessageField('GooglePrivacyDlpV2DeltaPresenceEstimationHistogramBucket', 1, repeated=True)


class GooglePrivacyDlpV2DetectionRule(_messages.Message):
  r"""Deprecated; use `InspectionRuleSet` instead. Rule for modifying a
  `CustomInfoType` to alter behavior under certain circumstances, depending on
  the specific details of the rule. Not supported for the `surrogate_type`
  custom infoType.

  Fields:
    hotwordRule: Hotword-based detection rule.
  """

  hotwordRule = _messages.MessageField('GooglePrivacyDlpV2HotwordRule', 1)


class GooglePrivacyDlpV2Dictionary(_messages.Message):
  r"""Custom information type based on a dictionary of words or phrases. This
  can be used to match sensitive information specific to the data, such as a
  list of employee IDs or job titles. Dictionary words are case-insensitive
  and all characters other than letters and digits in the unicode [Basic
  Multilingual Plane](https://en.wikipedia.org/wiki/Plane_%28Unicode%29#Basic_
  Multilingual_Plane) will be replaced with whitespace when scanning for
  matches, so the dictionary phrase "Sam Johnson" will match all three phrases
  "sam johnson", "Sam, Johnson", and "Sam (Johnson)". Additionally, the
  characters surrounding any match must be of a different type than the
  adjacent characters within the word, so letters must be next to non-letters
  and digits next to non-digits. For example, the dictionary word "jen" will
  match the first three letters of the text "jen123" but will return no
  matches for "jennifer". Dictionary words containing a large number of
  characters that are not letters or digits may result in unexpected findings
  because such characters are treated as whitespace. The
  [limits](https://cloud.google.com/dlp/limits) page contains details about
  the size limits of dictionaries. For dictionaries that do not fit within
  these constraints, consider using `LargeCustomDictionaryConfig` in the
  `StoredInfoType` API.

  Fields:
    cloudStoragePath: Newline-delimited file of words in Cloud Storage. Only a
      single file is accepted.
    wordList: List of words or phrases to search for.
  """

  cloudStoragePath = _messages.MessageField('GooglePrivacyDlpV2CloudStoragePath', 1)
  wordList = _messages.MessageField('GooglePrivacyDlpV2WordList', 2)


class GooglePrivacyDlpV2DlpJob(_messages.Message):
  r"""Combines all of the information about a DLP job.

  Enums:
    StateValueValuesEnum: State of a job.
    TypeValueValuesEnum: The type of job.

  Fields:
    actionDetails: Events that should occur after the job has completed.
    createTime: Time when the job was created.
    endTime: Time when the job finished.
    errors: A stream of errors encountered running the job.
    inspectDetails: Results from inspecting a data source.
    jobTriggerName: If created by a job trigger, the resource name of the
      trigger that instantiated the job.
    name: The server-assigned name.
    riskDetails: Results from analyzing risk of a data source.
    startTime: Time when the job started.
    state: State of a job.
    type: The type of job.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of a job.

    Values:
      JOB_STATE_UNSPECIFIED: Unused.
      PENDING: The job has not yet started.
      RUNNING: The job is currently running. Once a job has finished it will
        transition to FAILED or DONE.
      DONE: The job is no longer running.
      CANCELED: The job was canceled before it could be completed.
      FAILED: The job had an error and did not complete.
      ACTIVE: The job is currently accepting findings via hybridInspect. A
        hybrid job in ACTIVE state may continue to have findings added to it
        through the calling of hybridInspect. After the job has finished no
        more calls to hybridInspect may be made. ACTIVE jobs can transition to
        DONE.
    """
    JOB_STATE_UNSPECIFIED = 0
    PENDING = 1
    RUNNING = 2
    DONE = 3
    CANCELED = 4
    FAILED = 5
    ACTIVE = 6

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of job.

    Values:
      DLP_JOB_TYPE_UNSPECIFIED: Defaults to INSPECT_JOB.
      INSPECT_JOB: The job inspected Google Cloud for sensitive data.
      RISK_ANALYSIS_JOB: The job executed a Risk Analysis computation.
    """
    DLP_JOB_TYPE_UNSPECIFIED = 0
    INSPECT_JOB = 1
    RISK_ANALYSIS_JOB = 2

  actionDetails = _messages.MessageField('GooglePrivacyDlpV2ActionDetails', 1, repeated=True)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  errors = _messages.MessageField('GooglePrivacyDlpV2Error', 4, repeated=True)
  inspectDetails = _messages.MessageField('GooglePrivacyDlpV2InspectDataSourceDetails', 5)
  jobTriggerName = _messages.StringField(6)
  name = _messages.StringField(7)
  riskDetails = _messages.MessageField('GooglePrivacyDlpV2AnalyzeDataSourceRiskDetails', 8)
  startTime = _messages.StringField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  type = _messages.EnumField('TypeValueValuesEnum', 11)


class GooglePrivacyDlpV2DocumentLocation(_messages.Message):
  r"""Location of a finding within a document.

  Fields:
    fileOffset: Offset of the line, from the beginning of the file, where the
      finding is located.
  """

  fileOffset = _messages.IntegerField(1)


class GooglePrivacyDlpV2EntityId(_messages.Message):
  r"""An entity in a dataset is a field or set of fields that correspond to a
  single person. For example, in medical records the `EntityId` might be a
  patient identifier, or for financial records it might be an account
  identifier. This message is used when generalizations or analysis must take
  into account that multiple rows correspond to the same entity.

  Fields:
    field: Composite key indicating which field contains the entity
      identifier.
  """

  field = _messages.MessageField('GooglePrivacyDlpV2FieldId', 1)


class GooglePrivacyDlpV2Error(_messages.Message):
  r"""Details information about an error encountered during job execution or
  the results of an unsuccessful activation of the JobTrigger.

  Fields:
    details: Detailed error codes and messages.
    timestamps: The times the error occurred.
  """

  details = _messages.MessageField('GoogleRpcStatus', 1)
  timestamps = _messages.StringField(2, repeated=True)


class GooglePrivacyDlpV2ExcludeByHotword(_messages.Message):
  r"""The rule to exclude findings based on a hotword. For record inspection
  of tables, column names are considered hotwords. An example of this is to
  exclude a finding if it belongs to a BigQuery column that matches a specific
  pattern.

  Fields:
    hotwordRegex: Regular expression pattern defining what qualifies as a
      hotword.
    proximity: Range of characters within which the entire hotword must
      reside. The total length of the window cannot exceed 1000 characters.
      The windowBefore property in proximity should be set to 1 if the hotword
      needs to be included in a column header.
  """

  hotwordRegex = _messages.MessageField('GooglePrivacyDlpV2Regex', 1)
  proximity = _messages.MessageField('GooglePrivacyDlpV2Proximity', 2)


class GooglePrivacyDlpV2ExcludeInfoTypes(_messages.Message):
  r"""List of excluded infoTypes.

  Fields:
    infoTypes: InfoType list in ExclusionRule rule drops a finding when it
      overlaps or contained within with a finding of an infoType from this
      list. For example, for `InspectionRuleSet.info_types` containing
      "PHONE_NUMBER"` and `exclusion_rule` containing
      `exclude_info_types.info_types` with "EMAIL_ADDRESS" the phone number
      findings are dropped if they overlap with EMAIL_ADDRESS finding. That
      leads to "<EMAIL>" to generate only a single finding,
      namely email address.
  """

  infoTypes = _messages.MessageField('GooglePrivacyDlpV2InfoType', 1, repeated=True)


class GooglePrivacyDlpV2ExclusionRule(_messages.Message):
  r"""The rule that specifies conditions when findings of infoTypes specified
  in `InspectionRuleSet` are removed from results.

  Enums:
    MatchingTypeValueValuesEnum: How the rule is applied, see MatchingType
      documentation for details.

  Fields:
    dictionary: Dictionary which defines the rule.
    excludeByHotword: Drop if the hotword rule is contained in the proximate
      context. For tabular data, the context includes the column name.
    excludeInfoTypes: Set of infoTypes for which findings would affect this
      rule.
    matchingType: How the rule is applied, see MatchingType documentation for
      details.
    regex: Regular expression which defines the rule.
  """

  class MatchingTypeValueValuesEnum(_messages.Enum):
    r"""How the rule is applied, see MatchingType documentation for details.

    Values:
      MATCHING_TYPE_UNSPECIFIED: Invalid.
      MATCHING_TYPE_FULL_MATCH: Full match. - Dictionary: join of Dictionary
        results matched complete finding quote - Regex: all regex matches fill
        a finding quote start to end - Exclude info type: completely inside
        affecting info types findings
      MATCHING_TYPE_PARTIAL_MATCH: Partial match. - Dictionary: at least one
        of the tokens in the finding matches - Regex: substring of the finding
        matches - Exclude info type: intersects with affecting info types
        findings
      MATCHING_TYPE_INVERSE_MATCH: Inverse match. - Dictionary: no tokens in
        the finding match the dictionary - Regex: finding doesn't match the
        regex - Exclude info type: no intersection with affecting info types
        findings
    """
    MATCHING_TYPE_UNSPECIFIED = 0
    MATCHING_TYPE_FULL_MATCH = 1
    MATCHING_TYPE_PARTIAL_MATCH = 2
    MATCHING_TYPE_INVERSE_MATCH = 3

  dictionary = _messages.MessageField('GooglePrivacyDlpV2Dictionary', 1)
  excludeByHotword = _messages.MessageField('GooglePrivacyDlpV2ExcludeByHotword', 2)
  excludeInfoTypes = _messages.MessageField('GooglePrivacyDlpV2ExcludeInfoTypes', 3)
  matchingType = _messages.EnumField('MatchingTypeValueValuesEnum', 4)
  regex = _messages.MessageField('GooglePrivacyDlpV2Regex', 5)


class GooglePrivacyDlpV2Export(_messages.Message):
  r"""If set, the detailed data profiles will be persisted to the location of
  your choice whenever updated.

  Fields:
    profileTable: Store all table and column profiles in an existing table or
      a new table in an existing dataset. Each re-generation will result in a
      new row in BigQuery.
  """

  profileTable = _messages.MessageField('GooglePrivacyDlpV2BigQueryTable', 1)


class GooglePrivacyDlpV2Expressions(_messages.Message):
  r"""An expression, consisting of an operator and conditions.

  Enums:
    LogicalOperatorValueValuesEnum: The operator to apply to the result of
      conditions. Default and currently only supported value is `AND`.

  Fields:
    conditions: Conditions to apply to the expression.
    logicalOperator: The operator to apply to the result of conditions.
      Default and currently only supported value is `AND`.
  """

  class LogicalOperatorValueValuesEnum(_messages.Enum):
    r"""The operator to apply to the result of conditions. Default and
    currently only supported value is `AND`.

    Values:
      LOGICAL_OPERATOR_UNSPECIFIED: Unused
      AND: Conditional AND
    """
    LOGICAL_OPERATOR_UNSPECIFIED = 0
    AND = 1

  conditions = _messages.MessageField('GooglePrivacyDlpV2Conditions', 1)
  logicalOperator = _messages.EnumField('LogicalOperatorValueValuesEnum', 2)


class GooglePrivacyDlpV2FieldId(_messages.Message):
  r"""General identifier of a data field in a storage service.

  Fields:
    name: Name describing the field.
  """

  name = _messages.StringField(1)


class GooglePrivacyDlpV2FieldTransformation(_messages.Message):
  r"""The transformation to apply to the field.

  Fields:
    condition: Only apply the transformation if the condition evaluates to
      true for the given `RecordCondition`. The conditions are allowed to
      reference fields that are not used in the actual transformation. Example
      Use Cases: - Apply a different bucket transformation to an age column if
      the zip code column for the same record is within a specific range. -
      Redact a field if the date of birth field is greater than 85.
    fields: Required. Input field(s) to apply the transformation to. When you
      have columns that reference their position within a list, omit the index
      from the FieldId. FieldId name matching ignores the index. For example,
      instead of "contact.nums[0].type", use "contact.nums.type".
    infoTypeTransformations: Treat the contents of the field as free text, and
      selectively transform content that matches an `InfoType`.
    primitiveTransformation: Apply the transformation to the entire field.
  """

  condition = _messages.MessageField('GooglePrivacyDlpV2RecordCondition', 1)
  fields = _messages.MessageField('GooglePrivacyDlpV2FieldId', 2, repeated=True)
  infoTypeTransformations = _messages.MessageField('GooglePrivacyDlpV2InfoTypeTransformations', 3)
  primitiveTransformation = _messages.MessageField('GooglePrivacyDlpV2PrimitiveTransformation', 4)


class GooglePrivacyDlpV2FileSet(_messages.Message):
  r"""Set of files to scan.

  Fields:
    regexFileSet: The regex-filtered set of files to scan. Exactly one of
      `url` or `regex_file_set` must be set.
    url: The Cloud Storage url of the file(s) to scan, in the format `gs:///`.
      Trailing wildcard in the path is allowed. If the url ends in a trailing
      slash, the bucket or directory represented by the url will be scanned
      non-recursively (content in sub-directories will not be scanned). This
      means that `gs://mybucket/` is equivalent to `gs://mybucket/*`, and
      `gs://mybucket/directory/` is equivalent to `gs://mybucket/directory/*`.
      Exactly one of `url` or `regex_file_set` must be set.
  """

  regexFileSet = _messages.MessageField('GooglePrivacyDlpV2CloudStorageRegexFileSet', 1)
  url = _messages.StringField(2)


class GooglePrivacyDlpV2Finding(_messages.Message):
  r"""Represents a piece of potentially sensitive content.

  Enums:
    LikelihoodValueValuesEnum: Confidence of how likely it is that the
      `info_type` is correct.

  Messages:
    LabelsValue: The labels associated with this `Finding`. Label keys must be
      between 1 and 63 characters long and must conform to the following
      regular expression: `[a-z]([-a-z0-9]*[a-z0-9])?`. Label values must be
      between 0 and 63 characters long and must conform to the regular
      expression `([a-z]([-a-z0-9]*[a-z0-9])?)?`. No more than 10 labels can
      be associated with a given finding. Examples: * `"environment" :
      "production"` * `"pipeline" : "etl"`

  Fields:
    createTime: Timestamp when finding was detected.
    findingId: The unique finding id.
    infoType: The type of content that might have been found. Provided if
      `excluded_types` is false.
    jobCreateTime: Time the job started that produced this finding.
    jobName: The job that stored the finding.
    labels: The labels associated with this `Finding`. Label keys must be
      between 1 and 63 characters long and must conform to the following
      regular expression: `[a-z]([-a-z0-9]*[a-z0-9])?`. Label values must be
      between 0 and 63 characters long and must conform to the regular
      expression `([a-z]([-a-z0-9]*[a-z0-9])?)?`. No more than 10 labels can
      be associated with a given finding. Examples: * `"environment" :
      "production"` * `"pipeline" : "etl"`
    likelihood: Confidence of how likely it is that the `info_type` is
      correct.
    location: Where the content was found.
    name: Resource name in format
      projects/{project}/locations/{location}/findings/{finding} Populated
      only when viewing persisted findings.
    quote: The content that was found. Even if the content is not textual, it
      may be converted to a textual representation here. Provided if
      `include_quote` is true and the finding is less than or equal to 4096
      bytes long. If the finding exceeds 4096 bytes in length, the quote may
      be omitted.
    quoteInfo: Contains data parsed from quotes. Only populated if
      include_quote was set to true and a supported infoType was requested.
      Currently supported infoTypes: DATE, DATE_OF_BIRTH and TIME.
    resourceName: The job that stored the finding.
    triggerName: Job trigger name, if applicable, for this finding.
  """

  class LikelihoodValueValuesEnum(_messages.Enum):
    r"""Confidence of how likely it is that the `info_type` is correct.

    Values:
      LIKELIHOOD_UNSPECIFIED: Default value; same as POSSIBLE.
      VERY_UNLIKELY: Highest chance of a false positive.
      UNLIKELY: High chance of a false positive.
      POSSIBLE: Some matching signals. The default value.
      LIKELY: Low chance of a false positive.
      VERY_LIKELY: Confidence level is high. Lowest chance of a false
        positive.
    """
    LIKELIHOOD_UNSPECIFIED = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this `Finding`. Label keys must be between
    1 and 63 characters long and must conform to the following regular
    expression: `[a-z]([-a-z0-9]*[a-z0-9])?`. Label values must be between 0
    and 63 characters long and must conform to the regular expression
    `([a-z]([-a-z0-9]*[a-z0-9])?)?`. No more than 10 labels can be associated
    with a given finding. Examples: * `"environment" : "production"` *
    `"pipeline" : "etl"`

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  findingId = _messages.StringField(2)
  infoType = _messages.MessageField('GooglePrivacyDlpV2InfoType', 3)
  jobCreateTime = _messages.StringField(4)
  jobName = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  likelihood = _messages.EnumField('LikelihoodValueValuesEnum', 7)
  location = _messages.MessageField('GooglePrivacyDlpV2Location', 8)
  name = _messages.StringField(9)
  quote = _messages.StringField(10)
  quoteInfo = _messages.MessageField('GooglePrivacyDlpV2QuoteInfo', 11)
  resourceName = _messages.StringField(12)
  triggerName = _messages.StringField(13)


class GooglePrivacyDlpV2FindingLimits(_messages.Message):
  r"""Configuration to control the number of findings returned for inspection.
  This is not used for de-identification or data profiling. When redacting
  sensitive data from images, finding limits don't apply. They can cause
  unexpected or inconsistent results, where only some data is redacted. Don't
  include finding limits in RedactImage requests. Otherwise, Cloud DLP returns
  an error.

  Fields:
    maxFindingsPerInfoType: Configuration of findings limit given for
      specified infoTypes.
    maxFindingsPerItem: Max number of findings that are returned for each item
      scanned. When set within an InspectContentRequest, this field is
      ignored. This value isn't a hard limit. If the number of findings for an
      item reaches this limit, the inspection of that item ends gradually, not
      abruptly. Therefore, the actual number of findings that Cloud DLP
      returns for the item can be multiple times higher than this value.
    maxFindingsPerRequest: Max number of findings that are returned per
      request or job. If you set this field in an InspectContentRequest, the
      resulting maximum value is the value that you set or 3,000, whichever is
      lower. This value isn't a hard limit. If an inspection reaches this
      limit, the inspection ends gradually, not abruptly. Therefore, the
      actual number of findings that Cloud DLP returns can be multiple times
      higher than this value.
  """

  maxFindingsPerInfoType = _messages.MessageField('GooglePrivacyDlpV2InfoTypeLimit', 1, repeated=True)
  maxFindingsPerItem = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  maxFindingsPerRequest = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GooglePrivacyDlpV2FinishDlpJobRequest(_messages.Message):
  r"""The request message for finishing a DLP hybrid job."""


class GooglePrivacyDlpV2FixedSizeBucketingConfig(_messages.Message):
  r"""Buckets values based on fixed size ranges. The Bucketing transformation
  can provide all of this functionality, but requires more configuration. This
  message is provided as a convenience to the user for simple bucketing
  strategies. The transformed value will be a hyphenated string of
  {lower_bound}-{upper_bound}. For example, if lower_bound = 10 and
  upper_bound = 20, all values that are within this bucket will be replaced
  with "10-20". This can be used on data of type: double, long. If the bound
  Value type differs from the type of data being transformed, we will first
  attempt converting the type of the data to be transformed to match the type
  of the bound before comparing. See
  https://cloud.google.com/dlp/docs/concepts-bucketing to learn more.

  Fields:
    bucketSize: Required. Size of each bucket (except for minimum and maximum
      buckets). So if `lower_bound` = 10, `upper_bound` = 89, and
      `bucket_size` = 10, then the following buckets would be used: -10,
      10-20, 20-30, 30-40, 40-50, 50-60, 60-70, 70-80, 80-89, 89+. Precision
      up to 2 decimals works.
    lowerBound: Required. Lower bound value of buckets. All values less than
      `lower_bound` are grouped together into a single bucket; for example if
      `lower_bound` = 10, then all values less than 10 are replaced with the
      value "-10".
    upperBound: Required. Upper bound value of buckets. All values greater
      than upper_bound are grouped together into a single bucket; for example
      if `upper_bound` = 89, then all values greater than 89 are replaced with
      the value "89+".
  """

  bucketSize = _messages.FloatField(1)
  lowerBound = _messages.MessageField('GooglePrivacyDlpV2Value', 2)
  upperBound = _messages.MessageField('GooglePrivacyDlpV2Value', 3)


class GooglePrivacyDlpV2HotwordRule(_messages.Message):
  r"""The rule that adjusts the likelihood of findings within a certain
  proximity of hotwords.

  Fields:
    hotwordRegex: Regular expression pattern defining what qualifies as a
      hotword.
    likelihoodAdjustment: Likelihood adjustment to apply to all matching
      findings.
    proximity: Range of characters within which the entire hotword must
      reside. The total length of the window cannot exceed 1000 characters.
      The finding itself will be included in the window, so that hotwords can
      be used to match substrings of the finding itself. Suppose you want
      Cloud DLP to promote the likelihood of the phone number regex "\(\d{3}\)
      \d{3}-\d{4}" if the area code is known to be the area code of a
      company's office. In this case, use the hotword regex "\(xxx\)", where
      "xxx" is the area code in question. For tabular data, if you want to
      modify the likelihood of an entire column of findngs, see [Hotword
      example: Set the match likelihood of a table column]
      (https://cloud.google.com/dlp/docs/creating-custom-infotypes-
      likelihood#match-column-values).
  """

  hotwordRegex = _messages.MessageField('GooglePrivacyDlpV2Regex', 1)
  likelihoodAdjustment = _messages.MessageField('GooglePrivacyDlpV2LikelihoodAdjustment', 2)
  proximity = _messages.MessageField('GooglePrivacyDlpV2Proximity', 3)


class GooglePrivacyDlpV2HybridContentItem(_messages.Message):
  r"""An individual hybrid item to inspect. Will be stored temporarily during
  processing.

  Fields:
    findingDetails: Supplementary information that will be added to each
      finding.
    item: The item to inspect.
  """

  findingDetails = _messages.MessageField('GooglePrivacyDlpV2HybridFindingDetails', 1)
  item = _messages.MessageField('GooglePrivacyDlpV2ContentItem', 2)


class GooglePrivacyDlpV2HybridFindingDetails(_messages.Message):
  r"""Populate to associate additional data with each finding.

  Messages:
    LabelsValue: Labels to represent user provided metadata about the data
      being inspected. If configured by the job, some key values may be
      required. The labels associated with `Finding`'s produced by hybrid
      inspection. Label keys must be between 1 and 63 characters long and must
      conform to the following regular expression:
      `[a-z]([-a-z0-9]*[a-z0-9])?`. Label values must be between 0 and 63
      characters long and must conform to the regular expression
      `([a-z]([-a-z0-9]*[a-z0-9])?)?`. No more than 10 labels can be
      associated with a given finding. Examples: * `"environment" :
      "production"` * `"pipeline" : "etl"`

  Fields:
    containerDetails: Details about the container where the content being
      inspected is from.
    fileOffset: Offset in bytes of the line, from the beginning of the file,
      where the finding is located. Populate if the item being scanned is only
      part of a bigger item, such as a shard of a file and you want to track
      the absolute position of the finding.
    labels: Labels to represent user provided metadata about the data being
      inspected. If configured by the job, some key values may be required.
      The labels associated with `Finding`'s produced by hybrid inspection.
      Label keys must be between 1 and 63 characters long and must conform to
      the following regular expression: `[a-z]([-a-z0-9]*[a-z0-9])?`. Label
      values must be between 0 and 63 characters long and must conform to the
      regular expression `([a-z]([-a-z0-9]*[a-z0-9])?)?`. No more than 10
      labels can be associated with a given finding. Examples: *
      `"environment" : "production"` * `"pipeline" : "etl"`
    rowOffset: Offset of the row for tables. Populate if the row(s) being
      scanned are part of a bigger dataset and you want to keep track of their
      absolute position.
    tableOptions: If the container is a table, additional information to make
      findings meaningful such as the columns that are primary keys. If not
      known ahead of time, can also be set within each inspect hybrid call and
      the two will be merged. Note that identifying_fields will only be stored
      to BigQuery, and only if the BigQuery action has been included.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels to represent user provided metadata about the data being
    inspected. If configured by the job, some key values may be required. The
    labels associated with `Finding`'s produced by hybrid inspection. Label
    keys must be between 1 and 63 characters long and must conform to the
    following regular expression: `[a-z]([-a-z0-9]*[a-z0-9])?`. Label values
    must be between 0 and 63 characters long and must conform to the regular
    expression `([a-z]([-a-z0-9]*[a-z0-9])?)?`. No more than 10 labels can be
    associated with a given finding. Examples: * `"environment" :
    "production"` * `"pipeline" : "etl"`

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  containerDetails = _messages.MessageField('GooglePrivacyDlpV2Container', 1)
  fileOffset = _messages.IntegerField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  rowOffset = _messages.IntegerField(4)
  tableOptions = _messages.MessageField('GooglePrivacyDlpV2TableOptions', 5)


class GooglePrivacyDlpV2HybridInspectDlpJobRequest(_messages.Message):
  r"""Request to search for potentially sensitive info in a custom location.

  Fields:
    hybridItem: The item to inspect.
  """

  hybridItem = _messages.MessageField('GooglePrivacyDlpV2HybridContentItem', 1)


class GooglePrivacyDlpV2HybridInspectJobTriggerRequest(_messages.Message):
  r"""Request to search for potentially sensitive info in a custom location.

  Fields:
    hybridItem: The item to inspect.
  """

  hybridItem = _messages.MessageField('GooglePrivacyDlpV2HybridContentItem', 1)


class GooglePrivacyDlpV2HybridInspectResponse(_messages.Message):
  r"""Quota exceeded errors will be thrown once quota has been met."""


class GooglePrivacyDlpV2HybridInspectStatistics(_messages.Message):
  r"""Statistics related to processing hybrid inspect requests.

  Fields:
    abortedCount: The number of hybrid inspection requests aborted because the
      job ran out of quota or was ended before they could be processed.
    pendingCount: The number of hybrid requests currently being processed.
      Only populated when called via method `getDlpJob`. A burst of traffic
      may cause hybrid inspect requests to be enqueued. Processing will take
      place as quickly as possible, but resource limitations may impact how
      long a request is enqueued for.
    processedCount: The number of hybrid inspection requests processed within
      this job.
  """

  abortedCount = _messages.IntegerField(1)
  pendingCount = _messages.IntegerField(2)
  processedCount = _messages.IntegerField(3)


class GooglePrivacyDlpV2HybridOptions(_messages.Message):
  r"""Configuration to control jobs where the content being inspected is
  outside of Google Cloud Platform.

  Messages:
    LabelsValue: To organize findings, these labels will be added to each
      finding. Label keys must be between 1 and 63 characters long and must
      conform to the following regular expression:
      `[a-z]([-a-z0-9]*[a-z0-9])?`. Label values must be between 0 and 63
      characters long and must conform to the regular expression
      `([a-z]([-a-z0-9]*[a-z0-9])?)?`. No more than 10 labels can be
      associated with a given finding. Examples: * `"environment" :
      "production"` * `"pipeline" : "etl"`

  Fields:
    description: A short description of where the data is coming from. Will be
      stored once in the job. 256 max length.
    labels: To organize findings, these labels will be added to each finding.
      Label keys must be between 1 and 63 characters long and must conform to
      the following regular expression: `[a-z]([-a-z0-9]*[a-z0-9])?`. Label
      values must be between 0 and 63 characters long and must conform to the
      regular expression `([a-z]([-a-z0-9]*[a-z0-9])?)?`. No more than 10
      labels can be associated with a given finding. Examples: *
      `"environment" : "production"` * `"pipeline" : "etl"`
    requiredFindingLabelKeys: These are labels that each inspection request
      must include within their 'finding_labels' map. Request may contain
      others, but any missing one of these will be rejected. Label keys must
      be between 1 and 63 characters long and must conform to the following
      regular expression: `[a-z]([-a-z0-9]*[a-z0-9])?`. No more than 10 keys
      can be required.
    tableOptions: If the container is a table, additional information to make
      findings meaningful such as the columns that are primary keys.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""To organize findings, these labels will be added to each finding.
    Label keys must be between 1 and 63 characters long and must conform to
    the following regular expression: `[a-z]([-a-z0-9]*[a-z0-9])?`. Label
    values must be between 0 and 63 characters long and must conform to the
    regular expression `([a-z]([-a-z0-9]*[a-z0-9])?)?`. No more than 10 labels
    can be associated with a given finding. Examples: * `"environment" :
    "production"` * `"pipeline" : "etl"`

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  description = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  requiredFindingLabelKeys = _messages.StringField(3, repeated=True)
  tableOptions = _messages.MessageField('GooglePrivacyDlpV2TableOptions', 4)


class GooglePrivacyDlpV2ImageLocation(_messages.Message):
  r"""Location of the finding within an image.

  Fields:
    boundingBoxes: Bounding boxes locating the pixels within the image
      containing the finding.
  """

  boundingBoxes = _messages.MessageField('GooglePrivacyDlpV2BoundingBox', 1, repeated=True)


class GooglePrivacyDlpV2ImageRedactionConfig(_messages.Message):
  r"""Configuration for determining how redaction of images should occur.

  Fields:
    infoType: Only one per info_type should be provided per request. If not
      specified, and redact_all_text is false, the DLP API will redact all
      text that it matches against all info_types that are found, but not
      specified in another ImageRedactionConfig.
    redactAllText: If true, all text found in the image, regardless whether it
      matches an info_type, is redacted. Only one should be provided.
    redactionColor: The color to use when redacting content from an image. If
      not specified, the default is black.
  """

  infoType = _messages.MessageField('GooglePrivacyDlpV2InfoType', 1)
  redactAllText = _messages.BooleanField(2)
  redactionColor = _messages.MessageField('GooglePrivacyDlpV2Color', 3)


class GooglePrivacyDlpV2ImageTransformation(_messages.Message):
  r"""Configuration for determining how redaction of images should occur.

  Fields:
    allInfoTypes: Apply transformation to all findings not specified in other
      ImageTransformation's selected_info_types. Only one instance is allowed
      within the ImageTransformations message.
    allText: Apply transformation to all text that doesn't match an infoType.
      Only one instance is allowed within the ImageTransformations message.
    redactionColor: The color to use when redacting content from an image. If
      not specified, the default is black.
    selectedInfoTypes: Apply transformation to the selected info_types.
  """

  allInfoTypes = _messages.MessageField('GooglePrivacyDlpV2AllInfoTypes', 1)
  allText = _messages.MessageField('GooglePrivacyDlpV2AllText', 2)
  redactionColor = _messages.MessageField('GooglePrivacyDlpV2Color', 3)
  selectedInfoTypes = _messages.MessageField('GooglePrivacyDlpV2SelectedInfoTypes', 4)


class GooglePrivacyDlpV2ImageTransformations(_messages.Message):
  r"""A type of transformation that is applied over images.

  Fields:
    transforms: A GooglePrivacyDlpV2ImageTransformation attribute.
  """

  transforms = _messages.MessageField('GooglePrivacyDlpV2ImageTransformation', 1, repeated=True)


class GooglePrivacyDlpV2InfoType(_messages.Message):
  r"""Type of information detected by the API.

  Fields:
    name: Name of the information type. Either a name of your choosing when
      creating a CustomInfoType, or one of the names listed at
      https://cloud.google.com/dlp/docs/infotypes-reference when specifying a
      built-in type. When sending Cloud DLP results to Data Catalog, infoType
      names should conform to the pattern `[A-Za-z0-9$_-]{1,64}`.
    sensitivityScore: Optional custom sensitivity for this InfoType. This only
      applies to data profiling.
    version: Optional version name for this InfoType.
  """

  name = _messages.StringField(1)
  sensitivityScore = _messages.MessageField('GooglePrivacyDlpV2SensitivityScore', 2)
  version = _messages.StringField(3)


class GooglePrivacyDlpV2InfoTypeCategory(_messages.Message):
  r"""Classification of infoTypes to organize them according to geographic
  location, industry, and data type.

  Enums:
    IndustryCategoryValueValuesEnum: The group of relevant businesses where
      this infoType is commonly used
    LocationCategoryValueValuesEnum: The region or country that issued the ID
      or document represented by the infoType.
    TypeCategoryValueValuesEnum: The class of identifiers where this infoType
      belongs

  Fields:
    industryCategory: The group of relevant businesses where this infoType is
      commonly used
    locationCategory: The region or country that issued the ID or document
      represented by the infoType.
    typeCategory: The class of identifiers where this infoType belongs
  """

  class IndustryCategoryValueValuesEnum(_messages.Enum):
    r"""The group of relevant businesses where this infoType is commonly used

    Values:
      INDUSTRY_UNSPECIFIED: Unused industry
      FINANCE: The infoType is typically used in the finance industry.
      HEALTH: The infoType is typically used in the health industry.
      TELECOMMUNICATIONS: The infoType is typically used in the
        telecommunications industry.
    """
    INDUSTRY_UNSPECIFIED = 0
    FINANCE = 1
    HEALTH = 2
    TELECOMMUNICATIONS = 3

  class LocationCategoryValueValuesEnum(_messages.Enum):
    r"""The region or country that issued the ID or document represented by
    the infoType.

    Values:
      LOCATION_UNSPECIFIED: Unused location
      GLOBAL: The infoType is not issued by or tied to a specific region, but
        is used almost everywhere.
      ARGENTINA: The infoType is typically used in Argentina.
      AUSTRALIA: The infoType is typically used in Australia.
      BELGIUM: The infoType is typically used in Belgium.
      BRAZIL: The infoType is typically used in Brazil.
      CANADA: The infoType is typically used in Canada.
      CHILE: The infoType is typically used in Chile.
      CHINA: The infoType is typically used in China.
      COLOMBIA: The infoType is typically used in Colombia.
      CROATIA: The infoType is typically used in Croatia.
      DENMARK: The infoType is typically used in Denmark.
      FRANCE: The infoType is typically used in France.
      FINLAND: The infoType is typically used in Finland.
      GERMANY: The infoType is typically used in Germany.
      HONG_KONG: The infoType is typically used in Hong Kong.
      INDIA: The infoType is typically used in India.
      INDONESIA: The infoType is typically used in Indonesia.
      IRELAND: The infoType is typically used in Ireland.
      ISRAEL: The infoType is typically used in Israel.
      ITALY: The infoType is typically used in Italy.
      JAPAN: The infoType is typically used in Japan.
      KOREA: The infoType is typically used in Korea.
      MEXICO: The infoType is typically used in Mexico.
      NEW_ZEALAND: The infoType is typically used in New Zealand.
      THE_NETHERLANDS: The infoType is typically used in the Netherlands.
      NORWAY: The infoType is typically used in Norway.
      PARAGUAY: The infoType is typically used in Paraguay.
      PERU: The infoType is typically used in Peru.
      POLAND: The infoType is typically used in Poland.
      PORTUGAL: The infoType is typically used in Portugal.
      SINGAPORE: The infoType is typically used in Singapore.
      SOUTH_AFRICA: The infoType is typically used in South Africa.
      SPAIN: The infoType is typically used in Spain.
      SWEDEN: The infoType is typically used in Sweden.
      TAIWAN: The infoType is typically used in Taiwan.
      THAILAND: The infoType is typically used in Thailand.
      TURKEY: The infoType is typically used in Turkey.
      UNITED_KINGDOM: The infoType is typically used in the United Kingdom.
      UNITED_STATES: The infoType is typically used in the United States.
      URUGUAY: The infoType is typically used in Uruguay.
      VENEZUELA: The infoType is typically used in Venezuela.
      INTERNAL: The infoType is typically used in Google internally.
    """
    LOCATION_UNSPECIFIED = 0
    GLOBAL = 1
    ARGENTINA = 2
    AUSTRALIA = 3
    BELGIUM = 4
    BRAZIL = 5
    CANADA = 6
    CHILE = 7
    CHINA = 8
    COLOMBIA = 9
    CROATIA = 10
    DENMARK = 11
    FRANCE = 12
    FINLAND = 13
    GERMANY = 14
    HONG_KONG = 15
    INDIA = 16
    INDONESIA = 17
    IRELAND = 18
    ISRAEL = 19
    ITALY = 20
    JAPAN = 21
    KOREA = 22
    MEXICO = 23
    NEW_ZEALAND = 24
    THE_NETHERLANDS = 25
    NORWAY = 26
    PARAGUAY = 27
    PERU = 28
    POLAND = 29
    PORTUGAL = 30
    SINGAPORE = 31
    SOUTH_AFRICA = 32
    SPAIN = 33
    SWEDEN = 34
    TAIWAN = 35
    THAILAND = 36
    TURKEY = 37
    UNITED_KINGDOM = 38
    UNITED_STATES = 39
    URUGUAY = 40
    VENEZUELA = 41
    INTERNAL = 42

  class TypeCategoryValueValuesEnum(_messages.Enum):
    r"""The class of identifiers where this infoType belongs

    Values:
      TYPE_UNSPECIFIED: Unused type
      PII: Personally identifiable information, for example, a name or phone
        number
      SPII: Personally identifiable information that is especially sensitive,
        for example, a passport number.
      DEMOGRAPHIC: Attributes that can partially identify someone, especially
        in combination with other attributes, like age, height, and gender.
      CREDENTIAL: Confidential or secret information, for example, a password.
      GOVERNMENT_ID: An identification document issued by a government.
      DOCUMENT: A document, for example, a resume or source code.
      CONTEXTUAL_INFORMATION: Information that is not sensitive on its own,
        but provides details about the circumstances surrounding an entity or
        an event.
    """
    TYPE_UNSPECIFIED = 0
    PII = 1
    SPII = 2
    DEMOGRAPHIC = 3
    CREDENTIAL = 4
    GOVERNMENT_ID = 5
    DOCUMENT = 6
    CONTEXTUAL_INFORMATION = 7

  industryCategory = _messages.EnumField('IndustryCategoryValueValuesEnum', 1)
  locationCategory = _messages.EnumField('LocationCategoryValueValuesEnum', 2)
  typeCategory = _messages.EnumField('TypeCategoryValueValuesEnum', 3)


class GooglePrivacyDlpV2InfoTypeDescription(_messages.Message):
  r"""InfoType description.

  Enums:
    SupportedByValueListEntryValuesEnum:

  Fields:
    categories: The category of the infoType.
    description: Description of the infotype. Translated when language is
      provided in the request.
    displayName: Human readable form of the infoType name.
    name: Internal name of the infoType.
    sensitivityScore: The default sensitivity of the infoType.
    supportedBy: Which parts of the API supports this InfoType.
    versions: A list of available versions for the infotype.
  """

  class SupportedByValueListEntryValuesEnum(_messages.Enum):
    r"""SupportedByValueListEntryValuesEnum enum type.

    Values:
      ENUM_TYPE_UNSPECIFIED: Unused.
      INSPECT: Supported by the inspect operations.
      RISK_ANALYSIS: Supported by the risk analysis operations.
    """
    ENUM_TYPE_UNSPECIFIED = 0
    INSPECT = 1
    RISK_ANALYSIS = 2

  categories = _messages.MessageField('GooglePrivacyDlpV2InfoTypeCategory', 1, repeated=True)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  name = _messages.StringField(4)
  sensitivityScore = _messages.MessageField('GooglePrivacyDlpV2SensitivityScore', 5)
  supportedBy = _messages.EnumField('SupportedByValueListEntryValuesEnum', 6, repeated=True)
  versions = _messages.MessageField('GooglePrivacyDlpV2VersionDescription', 7, repeated=True)


class GooglePrivacyDlpV2InfoTypeLimit(_messages.Message):
  r"""Max findings configuration per infoType, per content item or long
  running DlpJob.

  Fields:
    infoType: Type of information the findings limit applies to. Only one
      limit per info_type should be provided. If InfoTypeLimit does not have
      an info_type, the DLP API applies the limit against all info_types that
      are found but not specified in another InfoTypeLimit.
    maxFindings: Max findings limit for the given infoType.
  """

  infoType = _messages.MessageField('GooglePrivacyDlpV2InfoType', 1)
  maxFindings = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GooglePrivacyDlpV2InfoTypeStats(_messages.Message):
  r"""Statistics regarding a specific InfoType.

  Fields:
    count: Number of findings for this infoType.
    infoType: The type of finding this stat is for.
  """

  count = _messages.IntegerField(1)
  infoType = _messages.MessageField('GooglePrivacyDlpV2InfoType', 2)


class GooglePrivacyDlpV2InfoTypeSummary(_messages.Message):
  r"""The infoType details for this column.

  Fields:
    estimatedPrevalence: Not populated for predicted infotypes.
    infoType: The infoType.
  """

  estimatedPrevalence = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  infoType = _messages.MessageField('GooglePrivacyDlpV2InfoType', 2)


class GooglePrivacyDlpV2InfoTypeTransformation(_messages.Message):
  r"""A transformation to apply to text that is identified as a specific
  info_type.

  Fields:
    infoTypes: InfoTypes to apply the transformation to. An empty list will
      cause this transformation to apply to all findings that correspond to
      infoTypes that were requested in `InspectConfig`.
    primitiveTransformation: Required. Primitive transformation to apply to
      the infoType.
  """

  infoTypes = _messages.MessageField('GooglePrivacyDlpV2InfoType', 1, repeated=True)
  primitiveTransformation = _messages.MessageField('GooglePrivacyDlpV2PrimitiveTransformation', 2)


class GooglePrivacyDlpV2InfoTypeTransformations(_messages.Message):
  r"""A type of transformation that will scan unstructured text and apply
  various `PrimitiveTransformation`s to each finding, where the transformation
  is applied to only values that were identified as a specific info_type.

  Fields:
    transformations: Required. Transformation for each infoType. Cannot
      specify more than one for a given infoType.
  """

  transformations = _messages.MessageField('GooglePrivacyDlpV2InfoTypeTransformation', 1, repeated=True)


class GooglePrivacyDlpV2InspectConfig(_messages.Message):
  r"""Configuration description of the scanning process. When used with
  redactContent only info_types and min_likelihood are currently used.

  Enums:
    ContentOptionsValueListEntryValuesEnum:
    MinLikelihoodValueValuesEnum: Only returns findings equal or above this
      threshold. The default is POSSIBLE. See
      https://cloud.google.com/dlp/docs/likelihood to learn more.

  Fields:
    contentOptions: Deprecated and unused.
    customInfoTypes: CustomInfoTypes provided by the user. See
      https://cloud.google.com/dlp/docs/creating-custom-infotypes to learn
      more.
    excludeInfoTypes: When true, excludes type information of the findings.
      This is not used for data profiling.
    includeQuote: When true, a contextual quote from the data that triggered a
      finding is included in the response; see Finding.quote. This is not used
      for data profiling.
    infoTypes: Restricts what info_types to look for. The values must
      correspond to InfoType values returned by ListInfoTypes or listed at
      https://cloud.google.com/dlp/docs/infotypes-reference. When no InfoTypes
      or CustomInfoTypes are specified in a request, the system may
      automatically choose what detectors to run. By default this may be all
      types, but may change over time as detectors are updated. If you need
      precise control and predictability as to what detectors are run you
      should specify specific InfoTypes listed in the reference, otherwise a
      default list will be used, which may change over time.
    limits: Configuration to control the number of findings returned. This is
      not used for data profiling. When redacting sensitive data from images,
      finding limits don't apply. They can cause unexpected or inconsistent
      results, where only some data is redacted. Don't include finding limits
      in RedactImage requests. Otherwise, Cloud DLP returns an error. When set
      within an InspectJobConfig, the specified maximum values aren't hard
      limits. If an inspection job reaches these limits, the job ends
      gradually, not abruptly. Therefore, the actual number of findings that
      Cloud DLP returns can be multiple times higher than these maximum
      values.
    minLikelihood: Only returns findings equal or above this threshold. The
      default is POSSIBLE. See https://cloud.google.com/dlp/docs/likelihood to
      learn more.
    ruleSet: Set of rules to apply to the findings for this InspectConfig.
      Exclusion rules, contained in the set are executed in the end, other
      rules are executed in the order they are specified for each info type.
  """

  class ContentOptionsValueListEntryValuesEnum(_messages.Enum):
    r"""ContentOptionsValueListEntryValuesEnum enum type.

    Values:
      CONTENT_UNSPECIFIED: Includes entire content of a file or a data stream.
      CONTENT_TEXT: Text content within the data, excluding any metadata.
      CONTENT_IMAGE: Images found in the data.
    """
    CONTENT_UNSPECIFIED = 0
    CONTENT_TEXT = 1
    CONTENT_IMAGE = 2

  class MinLikelihoodValueValuesEnum(_messages.Enum):
    r"""Only returns findings equal or above this threshold. The default is
    POSSIBLE. See https://cloud.google.com/dlp/docs/likelihood to learn more.

    Values:
      LIKELIHOOD_UNSPECIFIED: Default value; same as POSSIBLE.
      VERY_UNLIKELY: Highest chance of a false positive.
      UNLIKELY: High chance of a false positive.
      POSSIBLE: Some matching signals. The default value.
      LIKELY: Low chance of a false positive.
      VERY_LIKELY: Confidence level is high. Lowest chance of a false
        positive.
    """
    LIKELIHOOD_UNSPECIFIED = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  contentOptions = _messages.EnumField('ContentOptionsValueListEntryValuesEnum', 1, repeated=True)
  customInfoTypes = _messages.MessageField('GooglePrivacyDlpV2CustomInfoType', 2, repeated=True)
  excludeInfoTypes = _messages.BooleanField(3)
  includeQuote = _messages.BooleanField(4)
  infoTypes = _messages.MessageField('GooglePrivacyDlpV2InfoType', 5, repeated=True)
  limits = _messages.MessageField('GooglePrivacyDlpV2FindingLimits', 6)
  minLikelihood = _messages.EnumField('MinLikelihoodValueValuesEnum', 7)
  ruleSet = _messages.MessageField('GooglePrivacyDlpV2InspectionRuleSet', 8, repeated=True)


class GooglePrivacyDlpV2InspectContentRequest(_messages.Message):
  r"""Request to search for potentially sensitive info in a ContentItem.

  Fields:
    inspectConfig: Configuration for the inspector. What specified here will
      override the template referenced by the inspect_template_name argument.
    inspectTemplateName: Template to use. Any configuration directly specified
      in inspect_config will override those set in the template. Singular
      fields that are set in this request will replace their corresponding
      fields in the template. Repeated fields are appended. Singular sub-
      messages and groups are recursively merged.
    item: The item to inspect.
    locationId: Deprecated. This field has no effect.
  """

  inspectConfig = _messages.MessageField('GooglePrivacyDlpV2InspectConfig', 1)
  inspectTemplateName = _messages.StringField(2)
  item = _messages.MessageField('GooglePrivacyDlpV2ContentItem', 3)
  locationId = _messages.StringField(4)


class GooglePrivacyDlpV2InspectContentResponse(_messages.Message):
  r"""Results of inspecting an item.

  Fields:
    result: The findings.
  """

  result = _messages.MessageField('GooglePrivacyDlpV2InspectResult', 1)


class GooglePrivacyDlpV2InspectDataSourceDetails(_messages.Message):
  r"""The results of an inspect DataSource job.

  Fields:
    requestedOptions: The configuration used for this job.
    result: A summary of the outcome of this inspection job.
  """

  requestedOptions = _messages.MessageField('GooglePrivacyDlpV2RequestedOptions', 1)
  result = _messages.MessageField('GooglePrivacyDlpV2Result', 2)


class GooglePrivacyDlpV2InspectJobConfig(_messages.Message):
  r"""Controls what and how to inspect for findings.

  Fields:
    actions: Actions to execute at the completion of the job.
    inspectConfig: How and what to scan for.
    inspectTemplateName: If provided, will be used as the default for all
      values in InspectConfig. `inspect_config` will be merged into the values
      persisted as part of the template.
    storageConfig: The data to scan.
  """

  actions = _messages.MessageField('GooglePrivacyDlpV2Action', 1, repeated=True)
  inspectConfig = _messages.MessageField('GooglePrivacyDlpV2InspectConfig', 2)
  inspectTemplateName = _messages.StringField(3)
  storageConfig = _messages.MessageField('GooglePrivacyDlpV2StorageConfig', 4)


class GooglePrivacyDlpV2InspectResult(_messages.Message):
  r"""All the findings for a single scanned item.

  Fields:
    findings: List of findings for an item.
    findingsTruncated: If true, then this item might have more findings than
      were returned, and the findings returned are an arbitrary subset of all
      findings. The findings list might be truncated because the input items
      were too large, or because the server reached the maximum amount of
      resources allowed for a single API call. For best results, divide the
      input into smaller batches.
  """

  findings = _messages.MessageField('GooglePrivacyDlpV2Finding', 1, repeated=True)
  findingsTruncated = _messages.BooleanField(2)


class GooglePrivacyDlpV2InspectTemplate(_messages.Message):
  r"""The inspectTemplate contains a configuration (set of types of sensitive
  data to be detected) to be used anywhere you otherwise would normally
  specify InspectConfig. See https://cloud.google.com/dlp/docs/concepts-
  templates to learn more.

  Fields:
    createTime: Output only. The creation timestamp of an inspectTemplate.
    description: Short description (max 256 chars).
    displayName: Display name (max 256 chars).
    inspectConfig: The core content of the template. Configuration of the
      scanning process.
    name: Output only. The template name. The template will have one of the
      following formats: `projects/PROJECT_ID/inspectTemplates/TEMPLATE_ID` OR
      `organizations/ORGANIZATION_ID/inspectTemplates/TEMPLATE_ID`;
    updateTime: Output only. The last update timestamp of an inspectTemplate.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  inspectConfig = _messages.MessageField('GooglePrivacyDlpV2InspectConfig', 4)
  name = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class GooglePrivacyDlpV2InspectionRule(_messages.Message):
  r"""A single inspection rule to be applied to infoTypes, specified in
  `InspectionRuleSet`.

  Fields:
    exclusionRule: Exclusion rule.
    hotwordRule: Hotword-based detection rule.
  """

  exclusionRule = _messages.MessageField('GooglePrivacyDlpV2ExclusionRule', 1)
  hotwordRule = _messages.MessageField('GooglePrivacyDlpV2HotwordRule', 2)


class GooglePrivacyDlpV2InspectionRuleSet(_messages.Message):
  r"""Rule set for modifying a set of infoTypes to alter behavior under
  certain circumstances, depending on the specific details of the rules within
  the set.

  Fields:
    infoTypes: List of infoTypes this rule set is applied to.
    rules: Set of rules to be applied to infoTypes. The rules are applied in
      order.
  """

  infoTypes = _messages.MessageField('GooglePrivacyDlpV2InfoType', 1, repeated=True)
  rules = _messages.MessageField('GooglePrivacyDlpV2InspectionRule', 2, repeated=True)


class GooglePrivacyDlpV2JobNotificationEmails(_messages.Message):
  r"""Sends an email when the job completes. The email goes to IAM project
  owners and technical [Essential Contacts](https://cloud.google.com/resource-
  manager/docs/managing-notification-contacts).
  """



class GooglePrivacyDlpV2JobTrigger(_messages.Message):
  r"""Contains a configuration to make dlp api calls on a repeating basis. See
  https://cloud.google.com/dlp/docs/concepts-job-triggers to learn more.

  Enums:
    StatusValueValuesEnum: Required. A status for this trigger.

  Fields:
    createTime: Output only. The creation timestamp of a triggeredJob.
    description: User provided description (max 256 chars)
    displayName: Display name (max 100 chars)
    errors: Output only. A stream of errors encountered when the trigger was
      activated. Repeated errors may result in the JobTrigger automatically
      being paused. Will return the last 100 errors. Whenever the JobTrigger
      is modified this list will be cleared.
    inspectJob: For inspect jobs, a snapshot of the configuration.
    lastRunTime: Output only. The timestamp of the last time this trigger
      executed.
    name: Unique resource name for the triggeredJob, assigned by the service
      when the triggeredJob is created, for example `projects/dlp-test-
      project/jobTriggers/53234423`.
    status: Required. A status for this trigger.
    triggers: A list of triggers which will be OR'ed together. Only one in the
      list needs to trigger for a job to be started. The list may contain only
      a single Schedule trigger and must have at least one object.
    updateTime: Output only. The last update timestamp of a triggeredJob.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Required. A status for this trigger.

    Values:
      STATUS_UNSPECIFIED: Unused.
      HEALTHY: Trigger is healthy.
      PAUSED: Trigger is temporarily paused.
      CANCELLED: Trigger is cancelled and can not be resumed.
    """
    STATUS_UNSPECIFIED = 0
    HEALTHY = 1
    PAUSED = 2
    CANCELLED = 3

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  errors = _messages.MessageField('GooglePrivacyDlpV2Error', 4, repeated=True)
  inspectJob = _messages.MessageField('GooglePrivacyDlpV2InspectJobConfig', 5)
  lastRunTime = _messages.StringField(6)
  name = _messages.StringField(7)
  status = _messages.EnumField('StatusValueValuesEnum', 8)
  triggers = _messages.MessageField('GooglePrivacyDlpV2Trigger', 9, repeated=True)
  updateTime = _messages.StringField(10)


class GooglePrivacyDlpV2KAnonymityConfig(_messages.Message):
  r"""k-anonymity metric, used for analysis of reidentification risk.

  Fields:
    entityId: Message indicating that multiple rows might be associated to a
      single individual. If the same entity_id is associated to multiple
      quasi-identifier tuples over distinct rows, we consider the entire
      collection of tuples as the composite quasi-identifier. This collection
      is a multiset: the order in which the different tuples appear in the
      dataset is ignored, but their frequency is taken into account. Important
      note: a maximum of 1000 rows can be associated to a single entity ID. If
      more rows are associated with the same entity ID, some might be ignored.
    quasiIds: Set of fields to compute k-anonymity over. When multiple fields
      are specified, they are considered a single composite key. Structs and
      repeated data types are not supported; however, nested fields are
      supported so long as they are not structs themselves or nested within a
      repeated field.
  """

  entityId = _messages.MessageField('GooglePrivacyDlpV2EntityId', 1)
  quasiIds = _messages.MessageField('GooglePrivacyDlpV2FieldId', 2, repeated=True)


class GooglePrivacyDlpV2KAnonymityEquivalenceClass(_messages.Message):
  r"""The set of columns' values that share the same ldiversity value

  Fields:
    equivalenceClassSize: Size of the equivalence class, for example number of
      rows with the above set of values.
    quasiIdsValues: Set of values defining the equivalence class. One value
      per quasi-identifier column in the original KAnonymity metric message.
      The order is always the same as the original request.
  """

  equivalenceClassSize = _messages.IntegerField(1)
  quasiIdsValues = _messages.MessageField('GooglePrivacyDlpV2Value', 2, repeated=True)


class GooglePrivacyDlpV2KAnonymityHistogramBucket(_messages.Message):
  r"""Histogram of k-anonymity equivalence classes.

  Fields:
    bucketSize: Total number of equivalence classes in this bucket.
    bucketValueCount: Total number of distinct equivalence classes in this
      bucket.
    bucketValues: Sample of equivalence classes in this bucket. The total
      number of classes returned per bucket is capped at 20.
    equivalenceClassSizeLowerBound: Lower bound on the size of the equivalence
      classes in this bucket.
    equivalenceClassSizeUpperBound: Upper bound on the size of the equivalence
      classes in this bucket.
  """

  bucketSize = _messages.IntegerField(1)
  bucketValueCount = _messages.IntegerField(2)
  bucketValues = _messages.MessageField('GooglePrivacyDlpV2KAnonymityEquivalenceClass', 3, repeated=True)
  equivalenceClassSizeLowerBound = _messages.IntegerField(4)
  equivalenceClassSizeUpperBound = _messages.IntegerField(5)


class GooglePrivacyDlpV2KAnonymityResult(_messages.Message):
  r"""Result of the k-anonymity computation.

  Fields:
    equivalenceClassHistogramBuckets: Histogram of k-anonymity equivalence
      classes.
  """

  equivalenceClassHistogramBuckets = _messages.MessageField('GooglePrivacyDlpV2KAnonymityHistogramBucket', 1, repeated=True)


class GooglePrivacyDlpV2KMapEstimationConfig(_messages.Message):
  r"""Reidentifiability metric. This corresponds to a risk model similar to
  what is called "journalist risk" in the literature, except the attack
  dataset is statistically modeled instead of being perfectly known. This can
  be done using publicly available data (like the US Census), or using a
  custom statistical model (indicated as one or several BigQuery tables), or
  by extrapolating from the distribution of values in the input dataset.

  Fields:
    auxiliaryTables: Several auxiliary tables can be used in the analysis.
      Each custom_tag used to tag a quasi-identifiers column must appear in
      exactly one column of one auxiliary table.
    quasiIds: Required. Fields considered to be quasi-identifiers. No two
      columns can have the same tag.
    regionCode: ISO 3166-1 alpha-2 region code to use in the statistical
      modeling. Set if no column is tagged with a region-specific InfoType
      (like US_ZIP_5) or a region code.
  """

  auxiliaryTables = _messages.MessageField('GooglePrivacyDlpV2AuxiliaryTable', 1, repeated=True)
  quasiIds = _messages.MessageField('GooglePrivacyDlpV2TaggedField', 2, repeated=True)
  regionCode = _messages.StringField(3)


class GooglePrivacyDlpV2KMapEstimationHistogramBucket(_messages.Message):
  r"""A KMapEstimationHistogramBucket message with the following values:
  min_anonymity: 3 max_anonymity: 5 frequency: 42 means that there are 42
  records whose quasi-identifier values correspond to 3, 4 or 5 people in the
  overlying population. An important particular case is when min_anonymity =
  max_anonymity = 1: the frequency field then corresponds to the number of
  uniquely identifiable records.

  Fields:
    bucketSize: Number of records within these anonymity bounds.
    bucketValueCount: Total number of distinct quasi-identifier tuple values
      in this bucket.
    bucketValues: Sample of quasi-identifier tuple values in this bucket. The
      total number of classes returned per bucket is capped at 20.
    maxAnonymity: Always greater than or equal to min_anonymity.
    minAnonymity: Always positive.
  """

  bucketSize = _messages.IntegerField(1)
  bucketValueCount = _messages.IntegerField(2)
  bucketValues = _messages.MessageField('GooglePrivacyDlpV2KMapEstimationQuasiIdValues', 3, repeated=True)
  maxAnonymity = _messages.IntegerField(4)
  minAnonymity = _messages.IntegerField(5)


class GooglePrivacyDlpV2KMapEstimationQuasiIdValues(_messages.Message):
  r"""A tuple of values for the quasi-identifier columns.

  Fields:
    estimatedAnonymity: The estimated anonymity for these quasi-identifier
      values.
    quasiIdsValues: The quasi-identifier values.
  """

  estimatedAnonymity = _messages.IntegerField(1)
  quasiIdsValues = _messages.MessageField('GooglePrivacyDlpV2Value', 2, repeated=True)


class GooglePrivacyDlpV2KMapEstimationResult(_messages.Message):
  r"""Result of the reidentifiability analysis. Note that these results are an
  estimation, not exact values.

  Fields:
    kMapEstimationHistogram: The intervals [min_anonymity, max_anonymity] do
      not overlap. If a value doesn't correspond to any such interval, the
      associated frequency is zero. For example, the following records:
      {min_anonymity: 1, max_anonymity: 1, frequency: 17} {min_anonymity: 2,
      max_anonymity: 3, frequency: 42} {min_anonymity: 5, max_anonymity: 10,
      frequency: 99} mean that there are no record with an estimated anonymity
      of 4, 5, or larger than 10.
  """

  kMapEstimationHistogram = _messages.MessageField('GooglePrivacyDlpV2KMapEstimationHistogramBucket', 1, repeated=True)


class GooglePrivacyDlpV2Key(_messages.Message):
  r"""A unique identifier for a Datastore entity. If a key's partition ID or
  any of its path kinds or names are reserved/read-only, the key is
  reserved/read-only. A reserved/read-only key is forbidden in certain
  documented contexts.

  Fields:
    partitionId: Entities are partitioned into subsets, currently identified
      by a project ID and namespace ID. Queries are scoped to a single
      partition.
    path: The entity path. An entity path consists of one or more elements
      composed of a kind and a string or numerical identifier, which identify
      entities. The first element identifies a _root entity_, the second
      element identifies a _child_ of the root entity, the third element
      identifies a child of the second entity, and so forth. The entities
      identified by all prefixes of the path are called the element's
      _ancestors_. A path can never be empty, and a path can have at most 100
      elements.
  """

  partitionId = _messages.MessageField('GooglePrivacyDlpV2PartitionId', 1)
  path = _messages.MessageField('GooglePrivacyDlpV2PathElement', 2, repeated=True)


class GooglePrivacyDlpV2KindExpression(_messages.Message):
  r"""A representation of a Datastore kind.

  Fields:
    name: The name of the kind.
  """

  name = _messages.StringField(1)


class GooglePrivacyDlpV2KmsWrappedCryptoKey(_messages.Message):
  r"""Include to use an existing data crypto key wrapped by KMS. The wrapped
  key must be a 128-, 192-, or 256-bit key. Authorization requires the
  following IAM permissions when sending a request to perform a crypto
  transformation using a KMS-wrapped crypto key: dlp.kms.encrypt For more
  information, see [Creating a wrapped key]
  (https://cloud.google.com/dlp/docs/create-wrapped-key). Note: When you use
  Cloud KMS for cryptographic operations, [charges
  apply](https://cloud.google.com/kms/pricing).

  Fields:
    cryptoKeyName: Required. The resource name of the KMS CryptoKey to use for
      unwrapping.
    wrappedKey: Required. The wrapped data crypto key.
  """

  cryptoKeyName = _messages.StringField(1)
  wrappedKey = _messages.BytesField(2)


class GooglePrivacyDlpV2LDiversityConfig(_messages.Message):
  r"""l-diversity metric, used for analysis of reidentification risk.

  Fields:
    quasiIds: Set of quasi-identifiers indicating how equivalence classes are
      defined for the l-diversity computation. When multiple fields are
      specified, they are considered a single composite key.
    sensitiveAttribute: Sensitive field for computing the l-value.
  """

  quasiIds = _messages.MessageField('GooglePrivacyDlpV2FieldId', 1, repeated=True)
  sensitiveAttribute = _messages.MessageField('GooglePrivacyDlpV2FieldId', 2)


class GooglePrivacyDlpV2LDiversityEquivalenceClass(_messages.Message):
  r"""The set of columns' values that share the same ldiversity value.

  Fields:
    equivalenceClassSize: Size of the k-anonymity equivalence class.
    numDistinctSensitiveValues: Number of distinct sensitive values in this
      equivalence class.
    quasiIdsValues: Quasi-identifier values defining the k-anonymity
      equivalence class. The order is always the same as the original request.
    topSensitiveValues: Estimated frequencies of top sensitive values.
  """

  equivalenceClassSize = _messages.IntegerField(1)
  numDistinctSensitiveValues = _messages.IntegerField(2)
  quasiIdsValues = _messages.MessageField('GooglePrivacyDlpV2Value', 3, repeated=True)
  topSensitiveValues = _messages.MessageField('GooglePrivacyDlpV2ValueFrequency', 4, repeated=True)


class GooglePrivacyDlpV2LDiversityHistogramBucket(_messages.Message):
  r"""Histogram of l-diversity equivalence class sensitive value frequencies.

  Fields:
    bucketSize: Total number of equivalence classes in this bucket.
    bucketValueCount: Total number of distinct equivalence classes in this
      bucket.
    bucketValues: Sample of equivalence classes in this bucket. The total
      number of classes returned per bucket is capped at 20.
    sensitiveValueFrequencyLowerBound: Lower bound on the sensitive value
      frequencies of the equivalence classes in this bucket.
    sensitiveValueFrequencyUpperBound: Upper bound on the sensitive value
      frequencies of the equivalence classes in this bucket.
  """

  bucketSize = _messages.IntegerField(1)
  bucketValueCount = _messages.IntegerField(2)
  bucketValues = _messages.MessageField('GooglePrivacyDlpV2LDiversityEquivalenceClass', 3, repeated=True)
  sensitiveValueFrequencyLowerBound = _messages.IntegerField(4)
  sensitiveValueFrequencyUpperBound = _messages.IntegerField(5)


class GooglePrivacyDlpV2LDiversityResult(_messages.Message):
  r"""Result of the l-diversity computation.

  Fields:
    sensitiveValueFrequencyHistogramBuckets: Histogram of l-diversity
      equivalence class sensitive value frequencies.
  """

  sensitiveValueFrequencyHistogramBuckets = _messages.MessageField('GooglePrivacyDlpV2LDiversityHistogramBucket', 1, repeated=True)


class GooglePrivacyDlpV2LargeCustomDictionaryConfig(_messages.Message):
  r"""Configuration for a custom dictionary created from a data source of any
  size up to the maximum size defined in the
  [limits](https://cloud.google.com/dlp/limits) page. The artifacts of
  dictionary creation are stored in the specified Cloud Storage location.
  Consider using `CustomInfoType.Dictionary` for smaller dictionaries that
  satisfy the size requirements.

  Fields:
    bigQueryField: Field in a BigQuery table where each cell represents a
      dictionary phrase.
    cloudStorageFileSet: Set of files containing newline-delimited lists of
      dictionary phrases.
    outputPath: Location to store dictionary artifacts in Cloud Storage. These
      files will only be accessible by project owners and the DLP API. If any
      of these artifacts are modified, the dictionary is considered invalid
      and can no longer be used.
  """

  bigQueryField = _messages.MessageField('GooglePrivacyDlpV2BigQueryField', 1)
  cloudStorageFileSet = _messages.MessageField('GooglePrivacyDlpV2CloudStorageFileSet', 2)
  outputPath = _messages.MessageField('GooglePrivacyDlpV2CloudStoragePath', 3)


class GooglePrivacyDlpV2LargeCustomDictionaryStats(_messages.Message):
  r"""Summary statistics of a custom dictionary.

  Fields:
    approxNumPhrases: Approximate number of distinct phrases in the
      dictionary.
  """

  approxNumPhrases = _messages.IntegerField(1)


class GooglePrivacyDlpV2LeaveUntransformed(_messages.Message):
  r"""Skips the data without modifying it if the requested transformation
  would cause an error. For example, if a `DateShift` transformation were
  applied an an IP address, this mode would leave the IP address unchanged in
  the response.
  """



class GooglePrivacyDlpV2LikelihoodAdjustment(_messages.Message):
  r"""Message for specifying an adjustment to the likelihood of a finding as
  part of a detection rule.

  Enums:
    FixedLikelihoodValueValuesEnum: Set the likelihood of a finding to a fixed
      value.

  Fields:
    fixedLikelihood: Set the likelihood of a finding to a fixed value.
    relativeLikelihood: Increase or decrease the likelihood by the specified
      number of levels. For example, if a finding would be `POSSIBLE` without
      the detection rule and `relative_likelihood` is 1, then it is upgraded
      to `LIKELY`, while a value of -1 would downgrade it to `UNLIKELY`.
      Likelihood may never drop below `VERY_UNLIKELY` or exceed `VERY_LIKELY`,
      so applying an adjustment of 1 followed by an adjustment of -1 when base
      likelihood is `VERY_LIKELY` will result in a final likelihood of
      `LIKELY`.
  """

  class FixedLikelihoodValueValuesEnum(_messages.Enum):
    r"""Set the likelihood of a finding to a fixed value.

    Values:
      LIKELIHOOD_UNSPECIFIED: Default value; same as POSSIBLE.
      VERY_UNLIKELY: Highest chance of a false positive.
      UNLIKELY: High chance of a false positive.
      POSSIBLE: Some matching signals. The default value.
      LIKELY: Low chance of a false positive.
      VERY_LIKELY: Confidence level is high. Lowest chance of a false
        positive.
    """
    LIKELIHOOD_UNSPECIFIED = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  fixedLikelihood = _messages.EnumField('FixedLikelihoodValueValuesEnum', 1)
  relativeLikelihood = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GooglePrivacyDlpV2ListDeidentifyTemplatesResponse(_messages.Message):
  r"""Response message for ListDeidentifyTemplates.

  Fields:
    deidentifyTemplates: List of deidentify templates, up to page_size in
      ListDeidentifyTemplatesRequest.
    nextPageToken: If the next page is available then the next page token to
      be used in following ListDeidentifyTemplates request.
  """

  deidentifyTemplates = _messages.MessageField('GooglePrivacyDlpV2DeidentifyTemplate', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GooglePrivacyDlpV2ListDlpJobsResponse(_messages.Message):
  r"""The response message for listing DLP jobs.

  Fields:
    jobs: A list of DlpJobs that matches the specified filter in the request.
    nextPageToken: The standard List next-page token.
  """

  jobs = _messages.MessageField('GooglePrivacyDlpV2DlpJob', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GooglePrivacyDlpV2ListInfoTypesResponse(_messages.Message):
  r"""Response to the ListInfoTypes request.

  Fields:
    infoTypes: Set of sensitive infoTypes.
  """

  infoTypes = _messages.MessageField('GooglePrivacyDlpV2InfoTypeDescription', 1, repeated=True)


class GooglePrivacyDlpV2ListInspectTemplatesResponse(_messages.Message):
  r"""Response message for ListInspectTemplates.

  Fields:
    inspectTemplates: List of inspectTemplates, up to page_size in
      ListInspectTemplatesRequest.
    nextPageToken: If the next page is available then the next page token to
      be used in following ListInspectTemplates request.
  """

  inspectTemplates = _messages.MessageField('GooglePrivacyDlpV2InspectTemplate', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GooglePrivacyDlpV2ListJobTriggersResponse(_messages.Message):
  r"""Response message for ListJobTriggers.

  Fields:
    jobTriggers: List of triggeredJobs, up to page_size in
      ListJobTriggersRequest.
    nextPageToken: If the next page is available then the next page token to
      be used in following ListJobTriggers request.
  """

  jobTriggers = _messages.MessageField('GooglePrivacyDlpV2JobTrigger', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GooglePrivacyDlpV2ListStoredInfoTypesResponse(_messages.Message):
  r"""Response message for ListStoredInfoTypes.

  Fields:
    nextPageToken: If the next page is available then the next page token to
      be used in following ListStoredInfoTypes request.
    storedInfoTypes: List of storedInfoTypes, up to page_size in
      ListStoredInfoTypesRequest.
  """

  nextPageToken = _messages.StringField(1)
  storedInfoTypes = _messages.MessageField('GooglePrivacyDlpV2StoredInfoType', 2, repeated=True)


class GooglePrivacyDlpV2Location(_messages.Message):
  r"""Specifies the location of the finding.

  Fields:
    byteRange: Zero-based byte offsets delimiting the finding. These are
      relative to the finding's containing element. Note that when the content
      is not textual, this references the UTF-8 encoded textual representation
      of the content. Omitted if content is an image.
    codepointRange: Unicode character offsets delimiting the finding. These
      are relative to the finding's containing element. Provided when the
      content is text.
    container: Information about the container where this finding occurred, if
      available.
    contentLocations: List of nested objects pointing to the precise location
      of the finding within the file or record.
  """

  byteRange = _messages.MessageField('GooglePrivacyDlpV2Range', 1)
  codepointRange = _messages.MessageField('GooglePrivacyDlpV2Range', 2)
  container = _messages.MessageField('GooglePrivacyDlpV2Container', 3)
  contentLocations = _messages.MessageField('GooglePrivacyDlpV2ContentLocation', 4, repeated=True)


class GooglePrivacyDlpV2Manual(_messages.Message):
  r"""Job trigger option for hybrid jobs. Jobs must be manually created and
  finished.
  """



class GooglePrivacyDlpV2MetadataLocation(_messages.Message):
  r"""Metadata Location

  Enums:
    TypeValueValuesEnum: Type of metadata containing the finding.

  Fields:
    storageLabel: Storage metadata.
    type: Type of metadata containing the finding.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of metadata containing the finding.

    Values:
      METADATATYPE_UNSPECIFIED: Unused
      STORAGE_METADATA: General file metadata provided by Cloud Storage.
    """
    METADATATYPE_UNSPECIFIED = 0
    STORAGE_METADATA = 1

  storageLabel = _messages.MessageField('GooglePrivacyDlpV2StorageMetadataLabel', 1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class GooglePrivacyDlpV2NumericalStatsConfig(_messages.Message):
  r"""Compute numerical stats over an individual column, including min, max,
  and quantiles.

  Fields:
    field: Field to compute numerical stats on. Supported types are integer,
      float, date, datetime, timestamp, time.
  """

  field = _messages.MessageField('GooglePrivacyDlpV2FieldId', 1)


class GooglePrivacyDlpV2NumericalStatsResult(_messages.Message):
  r"""Result of the numerical stats computation.

  Fields:
    maxValue: Maximum value appearing in the column.
    minValue: Minimum value appearing in the column.
    quantileValues: List of 99 values that partition the set of field values
      into 100 equal sized buckets.
  """

  maxValue = _messages.MessageField('GooglePrivacyDlpV2Value', 1)
  minValue = _messages.MessageField('GooglePrivacyDlpV2Value', 2)
  quantileValues = _messages.MessageField('GooglePrivacyDlpV2Value', 3, repeated=True)


class GooglePrivacyDlpV2OtherInfoTypeSummary(_messages.Message):
  r"""Infotype details for other infoTypes found within a column.

  Fields:
    estimatedPrevalence: Approximate percentage of non-null rows that
      contained data detected by this infotype.
    excludedFromAnalysis: Whether this infoType was excluded from sensitivity
      and risk analysis due to factors such as low prevalence (subject to
      change).
    infoType: The other infoType.
  """

  estimatedPrevalence = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  excludedFromAnalysis = _messages.BooleanField(2)
  infoType = _messages.MessageField('GooglePrivacyDlpV2InfoType', 3)


class GooglePrivacyDlpV2OutputStorageConfig(_messages.Message):
  r"""Cloud repository for storing output.

  Enums:
    OutputSchemaValueValuesEnum: Schema used for writing the findings for
      Inspect jobs. This field is only used for Inspect and must be
      unspecified for Risk jobs. Columns are derived from the `Finding`
      object. If appending to an existing table, any columns from the
      predefined schema that are missing will be added. No columns in the
      existing table will be deleted. If unspecified, then all available
      columns will be used for a new table or an (existing) table with no
      schema, and no changes will be made to an existing table that has a
      schema. Only for use with external storage.

  Fields:
    outputSchema: Schema used for writing the findings for Inspect jobs. This
      field is only used for Inspect and must be unspecified for Risk jobs.
      Columns are derived from the `Finding` object. If appending to an
      existing table, any columns from the predefined schema that are missing
      will be added. No columns in the existing table will be deleted. If
      unspecified, then all available columns will be used for a new table or
      an (existing) table with no schema, and no changes will be made to an
      existing table that has a schema. Only for use with external storage.
    table: Store findings in an existing table or a new table in an existing
      dataset. If table_id is not set a new one will be generated for you with
      the following format: dlp_googleapis_yyyy_mm_dd_[dlp_job_id]. Pacific
      time zone will be used for generating the date details. For Inspect,
      each column in an existing output table must have the same name, type,
      and mode of a field in the `Finding` object. For Risk, an existing
      output table should be the output of a previous Risk analysis job run on
      the same source table, with the same privacy metric and quasi-
      identifiers. Risk jobs that analyze the same table but compute a
      different privacy metric, or use different sets of quasi-identifiers,
      cannot store their results in the same table.
  """

  class OutputSchemaValueValuesEnum(_messages.Enum):
    r"""Schema used for writing the findings for Inspect jobs. This field is
    only used for Inspect and must be unspecified for Risk jobs. Columns are
    derived from the `Finding` object. If appending to an existing table, any
    columns from the predefined schema that are missing will be added. No
    columns in the existing table will be deleted. If unspecified, then all
    available columns will be used for a new table or an (existing) table with
    no schema, and no changes will be made to an existing table that has a
    schema. Only for use with external storage.

    Values:
      OUTPUT_SCHEMA_UNSPECIFIED: Unused.
      BASIC_COLUMNS: Basic schema including only `info_type`, `quote`,
        `certainty`, and `timestamp`.
      GCS_COLUMNS: Schema tailored to findings from scanning Cloud Storage.
      DATASTORE_COLUMNS: Schema tailored to findings from scanning Google
        Datastore.
      BIG_QUERY_COLUMNS: Schema tailored to findings from scanning Google
        BigQuery.
      ALL_COLUMNS: Schema containing all columns.
    """
    OUTPUT_SCHEMA_UNSPECIFIED = 0
    BASIC_COLUMNS = 1
    GCS_COLUMNS = 2
    DATASTORE_COLUMNS = 3
    BIG_QUERY_COLUMNS = 4
    ALL_COLUMNS = 5

  outputSchema = _messages.EnumField('OutputSchemaValueValuesEnum', 1)
  table = _messages.MessageField('GooglePrivacyDlpV2BigQueryTable', 2)


class GooglePrivacyDlpV2PartitionId(_messages.Message):
  r"""Datastore partition ID. A partition ID identifies a grouping of
  entities. The grouping is always by project and namespace, however the
  namespace ID may be empty. A partition ID contains several dimensions:
  project ID and namespace ID.

  Fields:
    namespaceId: If not empty, the ID of the namespace to which the entities
      belong.
    projectId: The ID of the project to which the entities belong.
  """

  namespaceId = _messages.StringField(1)
  projectId = _messages.StringField(2)


class GooglePrivacyDlpV2PathElement(_messages.Message):
  r"""A (kind, ID/name) pair used to construct a key path. If either name or
  ID is set, the element is complete. If neither is set, the element is
  incomplete.

  Fields:
    id: The auto-allocated ID of the entity. Never equal to zero. Values less
      than zero are discouraged and may not be supported in the future.
    kind: The kind of the entity. A kind matching regex `__.*__` is
      reserved/read-only. A kind must not contain more than 1500 bytes when
      UTF-8 encoded. Cannot be `""`.
    name: The name of the entity. A name matching regex `__.*__` is
      reserved/read-only. A name must not be more than 1500 bytes when UTF-8
      encoded. Cannot be `""`.
  """

  id = _messages.IntegerField(1)
  kind = _messages.StringField(2)
  name = _messages.StringField(3)


class GooglePrivacyDlpV2PrimitiveTransformation(_messages.Message):
  r"""A rule for transforming a value.

  Fields:
    bucketingConfig: Bucketing
    characterMaskConfig: Mask
    cryptoDeterministicConfig: Deterministic Crypto
    cryptoHashConfig: Crypto
    cryptoReplaceFfxFpeConfig: Ffx-Fpe
    dateShiftConfig: Date Shift
    fixedSizeBucketingConfig: Fixed size bucketing
    redactConfig: Redact
    replaceConfig: Replace with a specified value.
    replaceDictionaryConfig: Replace with a value randomly drawn (with
      replacement) from a dictionary.
    replaceWithInfoTypeConfig: Replace with infotype
    timePartConfig: Time extraction
  """

  bucketingConfig = _messages.MessageField('GooglePrivacyDlpV2BucketingConfig', 1)
  characterMaskConfig = _messages.MessageField('GooglePrivacyDlpV2CharacterMaskConfig', 2)
  cryptoDeterministicConfig = _messages.MessageField('GooglePrivacyDlpV2CryptoDeterministicConfig', 3)
  cryptoHashConfig = _messages.MessageField('GooglePrivacyDlpV2CryptoHashConfig', 4)
  cryptoReplaceFfxFpeConfig = _messages.MessageField('GooglePrivacyDlpV2CryptoReplaceFfxFpeConfig', 5)
  dateShiftConfig = _messages.MessageField('GooglePrivacyDlpV2DateShiftConfig', 6)
  fixedSizeBucketingConfig = _messages.MessageField('GooglePrivacyDlpV2FixedSizeBucketingConfig', 7)
  redactConfig = _messages.MessageField('GooglePrivacyDlpV2RedactConfig', 8)
  replaceConfig = _messages.MessageField('GooglePrivacyDlpV2ReplaceValueConfig', 9)
  replaceDictionaryConfig = _messages.MessageField('GooglePrivacyDlpV2ReplaceDictionaryConfig', 10)
  replaceWithInfoTypeConfig = _messages.MessageField('GooglePrivacyDlpV2ReplaceWithInfoTypeConfig', 11)
  timePartConfig = _messages.MessageField('GooglePrivacyDlpV2TimePartConfig', 12)


class GooglePrivacyDlpV2PrivacyMetric(_messages.Message):
  r"""Privacy metric to compute for reidentification risk analysis.

  Fields:
    categoricalStatsConfig: Categorical stats
    deltaPresenceEstimationConfig: delta-presence
    kAnonymityConfig: K-anonymity
    kMapEstimationConfig: k-map
    lDiversityConfig: l-diversity
    numericalStatsConfig: Numerical stats
  """

  categoricalStatsConfig = _messages.MessageField('GooglePrivacyDlpV2CategoricalStatsConfig', 1)
  deltaPresenceEstimationConfig = _messages.MessageField('GooglePrivacyDlpV2DeltaPresenceEstimationConfig', 2)
  kAnonymityConfig = _messages.MessageField('GooglePrivacyDlpV2KAnonymityConfig', 3)
  kMapEstimationConfig = _messages.MessageField('GooglePrivacyDlpV2KMapEstimationConfig', 4)
  lDiversityConfig = _messages.MessageField('GooglePrivacyDlpV2LDiversityConfig', 5)
  numericalStatsConfig = _messages.MessageField('GooglePrivacyDlpV2NumericalStatsConfig', 6)


class GooglePrivacyDlpV2ProfileStatus(_messages.Message):
  r"""A GooglePrivacyDlpV2ProfileStatus object.

  Fields:
    status: Profiling status code and optional message. The `status.code`
      value is 0 (default value) for OK.
    timestamp: Time when the profile generation status was updated
  """

  status = _messages.MessageField('GoogleRpcStatus', 1)
  timestamp = _messages.StringField(2)


class GooglePrivacyDlpV2Proximity(_messages.Message):
  r"""Message for specifying a window around a finding to apply a detection
  rule.

  Fields:
    windowAfter: Number of characters after the finding to consider.
    windowBefore: Number of characters before the finding to consider. For
      tabular data, if you want to modify the likelihood of an entire column
      of findngs, set this to 1. For more information, see [Hotword example:
      Set the match likelihood of a table column]
      (https://cloud.google.com/dlp/docs/creating-custom-infotypes-
      likelihood#match-column-values).
  """

  windowAfter = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  windowBefore = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GooglePrivacyDlpV2PubSubCondition(_messages.Message):
  r"""A condition consisting of a value.

  Enums:
    MinimumRiskScoreValueValuesEnum: The minimum data risk score that triggers
      the condition.
    MinimumSensitivityScoreValueValuesEnum: The minimum sensitivity level that
      triggers the condition.

  Fields:
    minimumRiskScore: The minimum data risk score that triggers the condition.
    minimumSensitivityScore: The minimum sensitivity level that triggers the
      condition.
  """

  class MinimumRiskScoreValueValuesEnum(_messages.Enum):
    r"""The minimum data risk score that triggers the condition.

    Values:
      PROFILE_SCORE_BUCKET_UNSPECIFIED: Unused.
      HIGH: High risk/sensitivity detected.
      MEDIUM_OR_HIGH: Medium or high risk/sensitivity detected.
    """
    PROFILE_SCORE_BUCKET_UNSPECIFIED = 0
    HIGH = 1
    MEDIUM_OR_HIGH = 2

  class MinimumSensitivityScoreValueValuesEnum(_messages.Enum):
    r"""The minimum sensitivity level that triggers the condition.

    Values:
      PROFILE_SCORE_BUCKET_UNSPECIFIED: Unused.
      HIGH: High risk/sensitivity detected.
      MEDIUM_OR_HIGH: Medium or high risk/sensitivity detected.
    """
    PROFILE_SCORE_BUCKET_UNSPECIFIED = 0
    HIGH = 1
    MEDIUM_OR_HIGH = 2

  minimumRiskScore = _messages.EnumField('MinimumRiskScoreValueValuesEnum', 1)
  minimumSensitivityScore = _messages.EnumField('MinimumSensitivityScoreValueValuesEnum', 2)


class GooglePrivacyDlpV2PubSubExpressions(_messages.Message):
  r"""An expression, consisting of an operator and conditions.

  Enums:
    LogicalOperatorValueValuesEnum: The operator to apply to the collection of
      conditions.

  Fields:
    conditions: Conditions to apply to the expression.
    logicalOperator: The operator to apply to the collection of conditions.
  """

  class LogicalOperatorValueValuesEnum(_messages.Enum):
    r"""The operator to apply to the collection of conditions.

    Values:
      LOGICAL_OPERATOR_UNSPECIFIED: Unused.
      OR: Conditional OR.
      AND: Conditional AND.
    """
    LOGICAL_OPERATOR_UNSPECIFIED = 0
    OR = 1
    AND = 2

  conditions = _messages.MessageField('GooglePrivacyDlpV2PubSubCondition', 1, repeated=True)
  logicalOperator = _messages.EnumField('LogicalOperatorValueValuesEnum', 2)


class GooglePrivacyDlpV2PubSubNotification(_messages.Message):
  r"""Send a Pub/Sub message into the given Pub/Sub topic to connect other
  systems to data profile generation. The message payload data will be the
  byte serialization of `DataProfilePubSubMessage`.

  Enums:
    DetailOfMessageValueValuesEnum: How much data to include in the Pub/Sub
      message. If the user wishes to limit the size of the message, they can
      use resource_name and fetch the profile fields they wish to. Per table
      profile (not per column).
    EventValueValuesEnum: The type of event that triggers a Pub/Sub. At most
      one `PubSubNotification` per EventType is permitted.

  Fields:
    detailOfMessage: How much data to include in the Pub/Sub message. If the
      user wishes to limit the size of the message, they can use resource_name
      and fetch the profile fields they wish to. Per table profile (not per
      column).
    event: The type of event that triggers a Pub/Sub. At most one
      `PubSubNotification` per EventType is permitted.
    pubsubCondition: Conditions (e.g., data risk or sensitivity level) for
      triggering a Pub/Sub.
    topic: Cloud Pub/Sub topic to send notifications to. Format is
      projects/{project}/topics/{topic}.
  """

  class DetailOfMessageValueValuesEnum(_messages.Enum):
    r"""How much data to include in the Pub/Sub message. If the user wishes to
    limit the size of the message, they can use resource_name and fetch the
    profile fields they wish to. Per table profile (not per column).

    Values:
      DETAIL_LEVEL_UNSPECIFIED: Unused.
      TABLE_PROFILE: The full table data profile.
      RESOURCE_NAME: The resource name of the table.
    """
    DETAIL_LEVEL_UNSPECIFIED = 0
    TABLE_PROFILE = 1
    RESOURCE_NAME = 2

  class EventValueValuesEnum(_messages.Enum):
    r"""The type of event that triggers a Pub/Sub. At most one
    `PubSubNotification` per EventType is permitted.

    Values:
      EVENT_TYPE_UNSPECIFIED: Unused.
      NEW_PROFILE: New profile (not a re-profile).
      CHANGED_PROFILE: Changed one of the following profile metrics: * Table
        data risk score * Table sensitivity score * Table resource visibility
        * Table encryption type * Table predicted infoTypes * Table other
        infoTypes
      SCORE_INCREASED: Table data risk score or sensitivity score increased.
      ERROR_CHANGED: A user (non-internal) error occurred.
    """
    EVENT_TYPE_UNSPECIFIED = 0
    NEW_PROFILE = 1
    CHANGED_PROFILE = 2
    SCORE_INCREASED = 3
    ERROR_CHANGED = 4

  detailOfMessage = _messages.EnumField('DetailOfMessageValueValuesEnum', 1)
  event = _messages.EnumField('EventValueValuesEnum', 2)
  pubsubCondition = _messages.MessageField('GooglePrivacyDlpV2DataProfilePubSubCondition', 3)
  topic = _messages.StringField(4)


class GooglePrivacyDlpV2PublishFindingsToCloudDataCatalog(_messages.Message):
  r"""Publish findings of a DlpJob to Data Catalog. In Data Catalog, tag
  templates are applied to the resource that Cloud DLP scanned. Data Catalog
  tag templates are stored in the same project and region where the BigQuery
  table exists. For Cloud DLP to create and apply the tag template, the Cloud
  DLP service agent must have the `roles/datacatalog.tagTemplateOwner`
  permission on the project. The tag template contains fields summarizing the
  results of the DlpJob. Any field values previously written by another DlpJob
  are deleted. InfoType naming patterns are strictly enforced when using this
  feature. Findings are persisted in Data Catalog storage and are governed by
  service-specific policies for Data Catalog. For more information, see
  [Service Specific Terms](https://cloud.google.com/terms/service-terms). Only
  a single instance of this action can be specified. This action is allowed
  only if all resources being scanned are BigQuery tables. Compatible with:
  Inspect
  """



class GooglePrivacyDlpV2PublishSummaryToCscc(_messages.Message):
  r"""Publish the result summary of a DlpJob to [Security Command
  Center](https://cloud.google.com/security-command-center). This action is
  available for only projects that belong to an organization. This action
  publishes the count of finding instances and their infoTypes. The summary of
  findings are persisted in Security Command Center and are governed by
  [service-specific policies for Security Command
  Center](https://cloud.google.com/terms/service-terms). Only a single
  instance of this action can be specified. Compatible with: Inspect
  """



class GooglePrivacyDlpV2PublishToPubSub(_messages.Message):
  r"""Publish a message into a given Pub/Sub topic when DlpJob has completed.
  The message contains a single field, `DlpJobName`, which is equal to the
  finished job's [`DlpJob.name`](https://cloud.google.com/dlp/docs/reference/r
  est/v2/projects.dlpJobs#DlpJob). Compatible with: Inspect, Risk

  Fields:
    topic: Cloud Pub/Sub topic to send notifications to. The topic must have
      given publishing access rights to the DLP API service account executing
      the long running DlpJob sending the notifications. Format is
      projects/{project}/topics/{topic}.
  """

  topic = _messages.StringField(1)


class GooglePrivacyDlpV2PublishToStackdriver(_messages.Message):
  r"""Enable Stackdriver metric dlp.googleapis.com/finding_count. This will
  publish a metric to stack driver on each infotype requested and how many
  findings were found for it. CustomDetectors will be bucketed as 'Custom'
  under the Stackdriver label 'info_type'.
  """



class GooglePrivacyDlpV2QuasiId(_messages.Message):
  r"""A column with a semantic tag attached.

  Fields:
    customTag: A column can be tagged with a custom tag. In this case, the
      user must indicate an auxiliary table that contains statistical
      information on the possible values of this column (below).
    field: Required. Identifies the column.
    inferred: If no semantic tag is indicated, we infer the statistical model
      from the distribution of values in the input data
    infoType: A column can be tagged with a InfoType to use the relevant
      public dataset as a statistical model of population, if available. We
      currently support US ZIP codes, region codes, ages and genders. To
      programmatically obtain the list of supported InfoTypes, use
      ListInfoTypes with the supported_by=RISK_ANALYSIS filter.
  """

  customTag = _messages.StringField(1)
  field = _messages.MessageField('GooglePrivacyDlpV2FieldId', 2)
  inferred = _messages.MessageField('GoogleProtobufEmpty', 3)
  infoType = _messages.MessageField('GooglePrivacyDlpV2InfoType', 4)


class GooglePrivacyDlpV2QuasiIdField(_messages.Message):
  r"""A quasi-identifier column has a custom_tag, used to know which column in
  the data corresponds to which column in the statistical model.

  Fields:
    customTag: A auxiliary field.
    field: Identifies the column.
  """

  customTag = _messages.StringField(1)
  field = _messages.MessageField('GooglePrivacyDlpV2FieldId', 2)


class GooglePrivacyDlpV2QuasiIdentifierField(_messages.Message):
  r"""A quasi-identifier column has a custom_tag, used to know which column in
  the data corresponds to which column in the statistical model.

  Fields:
    customTag: A column can be tagged with a custom tag. In this case, the
      user must indicate an auxiliary table that contains statistical
      information on the possible values of this column (below).
    field: Identifies the column.
  """

  customTag = _messages.StringField(1)
  field = _messages.MessageField('GooglePrivacyDlpV2FieldId', 2)


class GooglePrivacyDlpV2QuoteInfo(_messages.Message):
  r"""Message for infoType-dependent details parsed from quote.

  Fields:
    dateTime: The date time indicated by the quote.
  """

  dateTime = _messages.MessageField('GooglePrivacyDlpV2DateTime', 1)


class GooglePrivacyDlpV2Range(_messages.Message):
  r"""Generic half-open interval [start, end)

  Fields:
    end: Index of the last character of the range (exclusive).
    start: Index of the first character of the range (inclusive).
  """

  end = _messages.IntegerField(1)
  start = _messages.IntegerField(2)


class GooglePrivacyDlpV2RecordCondition(_messages.Message):
  r"""A condition for determining whether a transformation should be applied
  to a field.

  Fields:
    expressions: An expression.
  """

  expressions = _messages.MessageField('GooglePrivacyDlpV2Expressions', 1)


class GooglePrivacyDlpV2RecordKey(_messages.Message):
  r"""Message for a unique key indicating a record that contains a finding.

  Fields:
    bigQueryKey: A GooglePrivacyDlpV2BigQueryKey attribute.
    datastoreKey: A GooglePrivacyDlpV2DatastoreKey attribute.
    idValues: Values of identifying columns in the given row. Order of values
      matches the order of `identifying_fields` specified in the scanning
      request.
  """

  bigQueryKey = _messages.MessageField('GooglePrivacyDlpV2BigQueryKey', 1)
  datastoreKey = _messages.MessageField('GooglePrivacyDlpV2DatastoreKey', 2)
  idValues = _messages.StringField(3, repeated=True)


class GooglePrivacyDlpV2RecordLocation(_messages.Message):
  r"""Location of a finding within a row or record.

  Fields:
    fieldId: Field id of the field containing the finding.
    recordKey: Key of the finding.
    tableLocation: Location within a `ContentItem.Table`.
  """

  fieldId = _messages.MessageField('GooglePrivacyDlpV2FieldId', 1)
  recordKey = _messages.MessageField('GooglePrivacyDlpV2RecordKey', 2)
  tableLocation = _messages.MessageField('GooglePrivacyDlpV2TableLocation', 3)


class GooglePrivacyDlpV2RecordSuppression(_messages.Message):
  r"""Configuration to suppress records whose suppression conditions evaluate
  to true.

  Fields:
    condition: A condition that when it evaluates to true will result in the
      record being evaluated to be suppressed from the transformed content.
  """

  condition = _messages.MessageField('GooglePrivacyDlpV2RecordCondition', 1)


class GooglePrivacyDlpV2RecordTransformation(_messages.Message):
  r"""A GooglePrivacyDlpV2RecordTransformation object.

  Fields:
    containerTimestamp: Findings container modification timestamp, if
      applicable.
    containerVersion: Container version, if available ("generation" for Cloud
      Storage).
    fieldId: For record transformations, provide a field.
  """

  containerTimestamp = _messages.StringField(1)
  containerVersion = _messages.StringField(2)
  fieldId = _messages.MessageField('GooglePrivacyDlpV2FieldId', 3)


class GooglePrivacyDlpV2RecordTransformations(_messages.Message):
  r"""A type of transformation that is applied over structured data such as a
  table.

  Fields:
    fieldTransformations: Transform the record by applying various field
      transformations.
    recordSuppressions: Configuration defining which records get suppressed
      entirely. Records that match any suppression rule are omitted from the
      output.
  """

  fieldTransformations = _messages.MessageField('GooglePrivacyDlpV2FieldTransformation', 1, repeated=True)
  recordSuppressions = _messages.MessageField('GooglePrivacyDlpV2RecordSuppression', 2, repeated=True)


class GooglePrivacyDlpV2RedactConfig(_messages.Message):
  r"""Redact a given value. For example, if used with an
  `InfoTypeTransformation` transforming PHONE_NUMBER, and input 'My phone
  number is ************', the output would be 'My phone number is '.
  """



class GooglePrivacyDlpV2RedactImageRequest(_messages.Message):
  r"""Request to search for potentially sensitive info in an image and redact
  it by covering it with a colored rectangle.

  Fields:
    byteItem: The content must be PNG, JPEG, SVG or BMP.
    imageRedactionConfigs: The configuration for specifying what content to
      redact from images.
    includeFindings: Whether the response should include findings along with
      the redacted image.
    inspectConfig: Configuration for the inspector.
    locationId: Deprecated. This field has no effect.
  """

  byteItem = _messages.MessageField('GooglePrivacyDlpV2ByteContentItem', 1)
  imageRedactionConfigs = _messages.MessageField('GooglePrivacyDlpV2ImageRedactionConfig', 2, repeated=True)
  includeFindings = _messages.BooleanField(3)
  inspectConfig = _messages.MessageField('GooglePrivacyDlpV2InspectConfig', 4)
  locationId = _messages.StringField(5)


class GooglePrivacyDlpV2RedactImageResponse(_messages.Message):
  r"""Results of redacting an image.

  Fields:
    extractedText: If an image was being inspected and the InspectConfig's
      include_quote was set to true, then this field will include all text, if
      any, that was found in the image.
    inspectResult: The findings. Populated when include_findings in the
      request is true.
    redactedImage: The redacted image. The type will be the same as the
      original image.
  """

  extractedText = _messages.StringField(1)
  inspectResult = _messages.MessageField('GooglePrivacyDlpV2InspectResult', 2)
  redactedImage = _messages.BytesField(3)


class GooglePrivacyDlpV2Regex(_messages.Message):
  r"""Message defining a custom regular expression.

  Fields:
    groupIndexes: The index of the submatch to extract as findings. When not
      specified, the entire match is returned. No more than 3 may be included.
    pattern: Pattern defining the regular expression. Its syntax
      (https://github.com/google/re2/wiki/Syntax) can be found under the
      google/re2 repository on GitHub.
  """

  groupIndexes = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)
  pattern = _messages.StringField(2)


class GooglePrivacyDlpV2ReidentifyContentRequest(_messages.Message):
  r"""Request to re-identify an item.

  Fields:
    inspectConfig: Configuration for the inspector.
    inspectTemplateName: Template to use. Any configuration directly specified
      in `inspect_config` will override those set in the template. Singular
      fields that are set in this request will replace their corresponding
      fields in the template. Repeated fields are appended. Singular sub-
      messages and groups are recursively merged.
    item: The item to re-identify. Will be treated as text.
    locationId: Deprecated. This field has no effect.
    reidentifyConfig: Configuration for the re-identification of the content
      item. This field shares the same proto message type that is used for de-
      identification, however its usage here is for the reversal of the
      previous de-identification. Re-identification is performed by examining
      the transformations used to de-identify the items and executing the
      reverse. This requires that only reversible transformations be provided
      here. The reversible transformations are: - `CryptoDeterministicConfig`
      - `CryptoReplaceFfxFpeConfig`
    reidentifyTemplateName: Template to use. References an instance of
      `DeidentifyTemplate`. Any configuration directly specified in
      `reidentify_config` or `inspect_config` will override those set in the
      template. The `DeidentifyTemplate` used must include only reversible
      transformations. Singular fields that are set in this request will
      replace their corresponding fields in the template. Repeated fields are
      appended. Singular sub-messages and groups are recursively merged.
  """

  inspectConfig = _messages.MessageField('GooglePrivacyDlpV2InspectConfig', 1)
  inspectTemplateName = _messages.StringField(2)
  item = _messages.MessageField('GooglePrivacyDlpV2ContentItem', 3)
  locationId = _messages.StringField(4)
  reidentifyConfig = _messages.MessageField('GooglePrivacyDlpV2DeidentifyConfig', 5)
  reidentifyTemplateName = _messages.StringField(6)


class GooglePrivacyDlpV2ReidentifyContentResponse(_messages.Message):
  r"""Results of re-identifying an item.

  Fields:
    item: The re-identified item.
    overview: An overview of the changes that were made to the `item`.
  """

  item = _messages.MessageField('GooglePrivacyDlpV2ContentItem', 1)
  overview = _messages.MessageField('GooglePrivacyDlpV2TransformationOverview', 2)


class GooglePrivacyDlpV2ReplaceDictionaryConfig(_messages.Message):
  r"""Replace each input value with a value randomly selected from the
  dictionary.

  Fields:
    wordList: A list of words to select from for random replacement. The
      [limits](https://cloud.google.com/dlp/limits) page contains details
      about the size limits of dictionaries.
  """

  wordList = _messages.MessageField('GooglePrivacyDlpV2WordList', 1)


class GooglePrivacyDlpV2ReplaceValueConfig(_messages.Message):
  r"""Replace each input value with a given `Value`.

  Fields:
    newValue: Value to replace it with.
  """

  newValue = _messages.MessageField('GooglePrivacyDlpV2Value', 1)


class GooglePrivacyDlpV2ReplaceWithInfoTypeConfig(_messages.Message):
  r"""Replace each matching finding with the name of the info_type."""


class GooglePrivacyDlpV2RequestedDeidentifyOptions(_messages.Message):
  r"""De-id options.

  Fields:
    snapshotDeidentifyTemplate: Snapshot of the state of the
      `DeidentifyTemplate` from the Deidentify action at the time this job was
      run.
    snapshotImageRedactTemplate: Snapshot of the state of the image
      transformation `DeidentifyTemplate` from the `Deidentify` action at the
      time this job was run.
    snapshotStructuredDeidentifyTemplate: Snapshot of the state of the
      structured `DeidentifyTemplate` from the `Deidentify` action at the time
      this job was run.
  """

  snapshotDeidentifyTemplate = _messages.MessageField('GooglePrivacyDlpV2DeidentifyTemplate', 1)
  snapshotImageRedactTemplate = _messages.MessageField('GooglePrivacyDlpV2DeidentifyTemplate', 2)
  snapshotStructuredDeidentifyTemplate = _messages.MessageField('GooglePrivacyDlpV2DeidentifyTemplate', 3)


class GooglePrivacyDlpV2RequestedOptions(_messages.Message):
  r"""Snapshot of the inspection configuration.

  Fields:
    jobConfig: Inspect config.
    snapshotInspectTemplate: If run with an InspectTemplate, a snapshot of its
      state at the time of this run.
  """

  jobConfig = _messages.MessageField('GooglePrivacyDlpV2InspectJobConfig', 1)
  snapshotInspectTemplate = _messages.MessageField('GooglePrivacyDlpV2InspectTemplate', 2)


class GooglePrivacyDlpV2RequestedRiskAnalysisOptions(_messages.Message):
  r"""Risk analysis options.

  Fields:
    jobConfig: The job config for the risk job.
  """

  jobConfig = _messages.MessageField('GooglePrivacyDlpV2RiskAnalysisJobConfig', 1)


class GooglePrivacyDlpV2Result(_messages.Message):
  r"""All result fields mentioned below are updated while the job is
  processing.

  Fields:
    hybridStats: Statistics related to the processing of hybrid inspect.
    infoTypeStats: Statistics of how many instances of each info type were
      found during inspect job.
    processedBytes: Total size in bytes that were processed.
    totalEstimatedBytes: Estimate of the number of bytes to process.
  """

  hybridStats = _messages.MessageField('GooglePrivacyDlpV2HybridInspectStatistics', 1)
  infoTypeStats = _messages.MessageField('GooglePrivacyDlpV2InfoTypeStats', 2, repeated=True)
  processedBytes = _messages.IntegerField(3)
  totalEstimatedBytes = _messages.IntegerField(4)


class GooglePrivacyDlpV2RiskAnalysisJobConfig(_messages.Message):
  r"""Configuration for a risk analysis job. See
  https://cloud.google.com/dlp/docs/concepts-risk-analysis to learn more.

  Fields:
    actions: Actions to execute at the completion of the job. Are executed in
      the order provided.
    privacyMetric: Privacy metric to compute.
    sourceTable: Input dataset to compute metrics over.
  """

  actions = _messages.MessageField('GooglePrivacyDlpV2Action', 1, repeated=True)
  privacyMetric = _messages.MessageField('GooglePrivacyDlpV2PrivacyMetric', 2)
  sourceTable = _messages.MessageField('GooglePrivacyDlpV2BigQueryTable', 3)


class GooglePrivacyDlpV2Row(_messages.Message):
  r"""Values of the row.

  Fields:
    values: Individual cells.
  """

  values = _messages.MessageField('GooglePrivacyDlpV2Value', 1, repeated=True)


class GooglePrivacyDlpV2SaveFindings(_messages.Message):
  r"""If set, the detailed findings will be persisted to the specified
  OutputStorageConfig. Only a single instance of this action can be specified.
  Compatible with: Inspect, Risk

  Fields:
    outputConfig: Location to store findings outside of DLP.
  """

  outputConfig = _messages.MessageField('GooglePrivacyDlpV2OutputStorageConfig', 1)


class GooglePrivacyDlpV2Schedule(_messages.Message):
  r"""Schedule for inspect job triggers.

  Fields:
    recurrencePeriodDuration: With this option a job is started on a regular
      periodic basis. For example: every day (86400 seconds). A scheduled
      start time will be skipped if the previous execution has not ended when
      its scheduled time occurs. This value must be set to a time duration
      greater than or equal to 1 day and can be no longer than 60 days.
  """

  recurrencePeriodDuration = _messages.StringField(1)


class GooglePrivacyDlpV2SelectedInfoTypes(_messages.Message):
  r"""Apply transformation to the selected info_types.

  Fields:
    infoTypes: Required. InfoTypes to apply the transformation to. Required.
      Provided InfoType must be unique within the ImageTransformations
      message.
  """

  infoTypes = _messages.MessageField('GooglePrivacyDlpV2InfoType', 1, repeated=True)


class GooglePrivacyDlpV2SensitivityScore(_messages.Message):
  r"""Score is calculated from of all elements in the data profile. A higher
  level means the data is more sensitive.

  Enums:
    ScoreValueValuesEnum: The sensitivity score applied to the resource.

  Fields:
    score: The sensitivity score applied to the resource.
  """

  class ScoreValueValuesEnum(_messages.Enum):
    r"""The sensitivity score applied to the resource.

    Values:
      SENSITIVITY_SCORE_UNSPECIFIED: Unused.
      SENSITIVITY_LOW: No sensitive information detected. The resource isn't
        publicly accessible.
      SENSITIVITY_MODERATE: Medium risk. Contains personally identifiable
        information (PII), potentially sensitive data, or fields with free-
        text data that are at a higher risk of having intermittent sensitive
        data. Consider limiting access.
      SENSITIVITY_HIGH: High risk. Sensitive personally identifiable
        information (SPII) can be present. Exfiltration of data can lead to
        user data loss. Re-identification of users might be possible. Consider
        limiting usage and or removing SPII.
    """
    SENSITIVITY_SCORE_UNSPECIFIED = 0
    SENSITIVITY_LOW = 1
    SENSITIVITY_MODERATE = 2
    SENSITIVITY_HIGH = 3

  score = _messages.EnumField('ScoreValueValuesEnum', 1)


class GooglePrivacyDlpV2StatisticalTable(_messages.Message):
  r"""An auxiliary table containing statistical information on the relative
  frequency of different quasi-identifiers values. It has one or several
  quasi-identifiers columns, and one column that indicates the relative
  frequency of each quasi-identifier tuple. If a tuple is present in the data
  but not in the auxiliary table, the corresponding relative frequency is
  assumed to be zero (and thus, the tuple is highly reidentifiable).

  Fields:
    quasiIds: Required. Quasi-identifier columns.
    relativeFrequency: Required. The relative frequency column must contain a
      floating-point number between 0 and 1 (inclusive). Null values are
      assumed to be zero.
    table: Required. Auxiliary table location.
  """

  quasiIds = _messages.MessageField('GooglePrivacyDlpV2QuasiIdentifierField', 1, repeated=True)
  relativeFrequency = _messages.MessageField('GooglePrivacyDlpV2FieldId', 2)
  table = _messages.MessageField('GooglePrivacyDlpV2BigQueryTable', 3)


class GooglePrivacyDlpV2StorageConfig(_messages.Message):
  r"""Shared message indicating Cloud storage type.

  Fields:
    bigQueryOptions: BigQuery options.
    cloudStorageOptions: Cloud Storage options.
    datastoreOptions: Google Cloud Datastore options.
    hybridOptions: Hybrid inspection options.
    timespanConfig: A GooglePrivacyDlpV2TimespanConfig attribute.
  """

  bigQueryOptions = _messages.MessageField('GooglePrivacyDlpV2BigQueryOptions', 1)
  cloudStorageOptions = _messages.MessageField('GooglePrivacyDlpV2CloudStorageOptions', 2)
  datastoreOptions = _messages.MessageField('GooglePrivacyDlpV2DatastoreOptions', 3)
  hybridOptions = _messages.MessageField('GooglePrivacyDlpV2HybridOptions', 4)
  timespanConfig = _messages.MessageField('GooglePrivacyDlpV2TimespanConfig', 5)


class GooglePrivacyDlpV2StorageMetadataLabel(_messages.Message):
  r"""Storage metadata label to indicate which metadata entry contains
  findings.

  Fields:
    key: A string attribute.
  """

  key = _messages.StringField(1)


class GooglePrivacyDlpV2StoredInfoType(_messages.Message):
  r"""StoredInfoType resource message that contains information about the
  current version and any pending updates.

  Fields:
    currentVersion: Current version of the stored info type.
    name: Resource name.
    pendingVersions: Pending versions of the stored info type. Empty if no
      versions are pending.
  """

  currentVersion = _messages.MessageField('GooglePrivacyDlpV2StoredInfoTypeVersion', 1)
  name = _messages.StringField(2)
  pendingVersions = _messages.MessageField('GooglePrivacyDlpV2StoredInfoTypeVersion', 3, repeated=True)


class GooglePrivacyDlpV2StoredInfoTypeConfig(_messages.Message):
  r"""Configuration for stored infoTypes. All fields and subfield are provided
  by the user. For more information, see
  https://cloud.google.com/dlp/docs/creating-custom-infotypes.

  Fields:
    description: Description of the StoredInfoType (max 256 characters).
    dictionary: Store dictionary-based CustomInfoType.
    displayName: Display name of the StoredInfoType (max 256 characters).
    largeCustomDictionary: StoredInfoType where findings are defined by a
      dictionary of phrases.
    regex: Store regular expression-based StoredInfoType.
  """

  description = _messages.StringField(1)
  dictionary = _messages.MessageField('GooglePrivacyDlpV2Dictionary', 2)
  displayName = _messages.StringField(3)
  largeCustomDictionary = _messages.MessageField('GooglePrivacyDlpV2LargeCustomDictionaryConfig', 4)
  regex = _messages.MessageField('GooglePrivacyDlpV2Regex', 5)


class GooglePrivacyDlpV2StoredInfoTypeStats(_messages.Message):
  r"""Statistics for a StoredInfoType.

  Fields:
    largeCustomDictionary: StoredInfoType where findings are defined by a
      dictionary of phrases.
  """

  largeCustomDictionary = _messages.MessageField('GooglePrivacyDlpV2LargeCustomDictionaryStats', 1)


class GooglePrivacyDlpV2StoredInfoTypeVersion(_messages.Message):
  r"""Version of a StoredInfoType, including the configuration used to build
  it, create timestamp, and current state.

  Enums:
    StateValueValuesEnum: Stored info type version state. Read-only, updated
      by the system during dictionary creation.

  Fields:
    config: StoredInfoType configuration.
    createTime: Create timestamp of the version. Read-only, determined by the
      system when the version is created.
    errors: Errors that occurred when creating this storedInfoType version, or
      anomalies detected in the storedInfoType data that render it unusable.
      Only the five most recent errors will be displayed, with the most recent
      error appearing first. For example, some of the data for stored custom
      dictionaries is put in the user's Cloud Storage bucket, and if this data
      is modified or deleted by the user or another system, the dictionary
      becomes invalid. If any errors occur, fix the problem indicated by the
      error message and use the UpdateStoredInfoType API method to create
      another version of the storedInfoType to continue using it, reusing the
      same `config` if it was not the source of the error.
    state: Stored info type version state. Read-only, updated by the system
      during dictionary creation.
    stats: Statistics about this storedInfoType version.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Stored info type version state. Read-only, updated by the system
    during dictionary creation.

    Values:
      STORED_INFO_TYPE_STATE_UNSPECIFIED: Unused
      PENDING: StoredInfoType version is being created.
      READY: StoredInfoType version is ready for use.
      FAILED: StoredInfoType creation failed. All relevant error messages are
        returned in the `StoredInfoTypeVersion` message.
      INVALID: StoredInfoType is no longer valid because artifacts stored in
        user-controlled storage were modified. To fix an invalid
        StoredInfoType, use the `UpdateStoredInfoType` method to create a new
        version.
    """
    STORED_INFO_TYPE_STATE_UNSPECIFIED = 0
    PENDING = 1
    READY = 2
    FAILED = 3
    INVALID = 4

  config = _messages.MessageField('GooglePrivacyDlpV2StoredInfoTypeConfig', 1)
  createTime = _messages.StringField(2)
  errors = _messages.MessageField('GooglePrivacyDlpV2Error', 3, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  stats = _messages.MessageField('GooglePrivacyDlpV2StoredInfoTypeStats', 5)


class GooglePrivacyDlpV2StoredType(_messages.Message):
  r"""A reference to a StoredInfoType to use with scanning.

  Fields:
    createTime: Timestamp indicating when the version of the `StoredInfoType`
      used for inspection was created. Output-only field, populated by the
      system.
    name: Resource name of the requested `StoredInfoType`, for example
      `organizations/433245324/storedInfoTypes/*********` or
      `projects/project-id/storedInfoTypes/*********`.
  """

  createTime = _messages.StringField(1)
  name = _messages.StringField(2)


class GooglePrivacyDlpV2SummaryResult(_messages.Message):
  r"""A collection that informs the user the number of times a particular
  `TransformationResultCode` and error details occurred.

  Enums:
    CodeValueValuesEnum: Outcome of the transformation.

  Fields:
    code: Outcome of the transformation.
    count: Number of transformations counted by this result.
    details: A place for warnings or errors to show up if a transformation
      didn't work as expected.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Outcome of the transformation.

    Values:
      TRANSFORMATION_RESULT_CODE_UNSPECIFIED: Unused
      SUCCESS: Transformation completed without an error.
      ERROR: Transformation had an error.
    """
    TRANSFORMATION_RESULT_CODE_UNSPECIFIED = 0
    SUCCESS = 1
    ERROR = 2

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  count = _messages.IntegerField(2)
  details = _messages.StringField(3)


class GooglePrivacyDlpV2SurrogateType(_messages.Message):
  r"""Message for detecting output from deidentification transformations such
  as [`CryptoReplaceFfxFpeConfig`](https://cloud.google.com/dlp/docs/reference
  /rest/v2/organizations.deidentifyTemplates#cryptoreplaceffxfpeconfig). These
  types of transformations are those that perform pseudonymization, thereby
  producing a "surrogate" as output. This should be used in conjunction with a
  field on the transformation such as `surrogate_info_type`. This
  CustomInfoType does not support the use of `detection_rules`.
  """



class GooglePrivacyDlpV2Table(_messages.Message):
  r"""Structured content to inspect. Up to 50,000 `Value`s per request
  allowed. See https://cloud.google.com/dlp/docs/inspecting-structured-
  text#inspecting_a_table to learn more.

  Fields:
    headers: Headers of the table.
    rows: Rows of the table.
  """

  headers = _messages.MessageField('GooglePrivacyDlpV2FieldId', 1, repeated=True)
  rows = _messages.MessageField('GooglePrivacyDlpV2Row', 2, repeated=True)


class GooglePrivacyDlpV2TableDataProfile(_messages.Message):
  r"""The profile for a scanned table.

  Enums:
    EncryptionStatusValueValuesEnum: How the table is encrypted.
    ResourceVisibilityValueValuesEnum: How broadly a resource has been shared.
    StateValueValuesEnum: State of a profile.

  Messages:
    ResourceLabelsValue: The labels applied to the resource at the time the
      profile was generated.

  Fields:
    configSnapshot: The snapshot of the configurations used to generate the
      profile.
    createTime: The time at which the table was created.
    dataRiskLevel: The data risk level of this table.
    datasetId: The BigQuery dataset ID.
    datasetLocation: The BigQuery location where the dataset's data is stored.
      See https://cloud.google.com/bigquery/docs/locations for supported
      locations.
    datasetProjectId: The Google Cloud project ID that owns the BigQuery
      dataset.
    encryptionStatus: How the table is encrypted.
    expirationTime: Optional. The time when this table expires.
    failedColumnCount: The number of columns skipped in the table because of
      an error.
    fullResource: The resource name of the table.
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    lastModifiedTime: The time when this table was last modified
    name: The name of the profile.
    otherInfoTypes: Other infoTypes found in this table's data.
    predictedInfoTypes: The infoTypes predicted from this table's data.
    profileLastGenerated: The last time the profile was generated.
    profileStatus: Success or error status from the most recent profile
      generation attempt. May be empty if the profile is still being
      generated.
    projectDataProfile: The resource name to the project data profile for this
      table.
    resourceLabels: The labels applied to the resource at the time the profile
      was generated.
    resourceVisibility: How broadly a resource has been shared.
    rowCount: Number of rows in the table when the profile was generated. This
      will not be populated for BigLake tables.
    scannedColumnCount: The number of columns profiled in the table.
    sensitivityScore: The sensitivity score of this table.
    state: State of a profile.
    tableId: The BigQuery table ID.
    tableSizeBytes: The size of the table when the profile was generated.
  """

  class EncryptionStatusValueValuesEnum(_messages.Enum):
    r"""How the table is encrypted.

    Values:
      ENCRYPTION_STATUS_UNSPECIFIED: Unused.
      ENCRYPTION_GOOGLE_MANAGED: Google manages server-side encryption keys on
        your behalf.
      ENCRYPTION_CUSTOMER_MANAGED: Customer provides the key.
    """
    ENCRYPTION_STATUS_UNSPECIFIED = 0
    ENCRYPTION_GOOGLE_MANAGED = 1
    ENCRYPTION_CUSTOMER_MANAGED = 2

  class ResourceVisibilityValueValuesEnum(_messages.Enum):
    r"""How broadly a resource has been shared.

    Values:
      RESOURCE_VISIBILITY_UNSPECIFIED: Unused.
      RESOURCE_VISIBILITY_PUBLIC: Visible to any user.
      RESOURCE_VISIBILITY_RESTRICTED: Visible only to specific users.
    """
    RESOURCE_VISIBILITY_UNSPECIFIED = 0
    RESOURCE_VISIBILITY_PUBLIC = 1
    RESOURCE_VISIBILITY_RESTRICTED = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""State of a profile.

    Values:
      STATE_UNSPECIFIED: Unused.
      RUNNING: The profile is currently running. Once a profile has finished
        it will transition to DONE.
      DONE: The profile is no longer generating. If profile_status.status.code
        is 0, the profile succeeded, otherwise, it failed.
    """
    STATE_UNSPECIFIED = 0
    RUNNING = 1
    DONE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceLabelsValue(_messages.Message):
    r"""The labels applied to the resource at the time the profile was
    generated.

    Messages:
      AdditionalProperty: An additional property for a ResourceLabelsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ResourceLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  configSnapshot = _messages.MessageField('GooglePrivacyDlpV2DataProfileConfigSnapshot', 1)
  createTime = _messages.StringField(2)
  dataRiskLevel = _messages.MessageField('GooglePrivacyDlpV2DataRiskLevel', 3)
  datasetId = _messages.StringField(4)
  datasetLocation = _messages.StringField(5)
  datasetProjectId = _messages.StringField(6)
  encryptionStatus = _messages.EnumField('EncryptionStatusValueValuesEnum', 7)
  expirationTime = _messages.StringField(8)
  failedColumnCount = _messages.IntegerField(9)
  fullResource = _messages.StringField(10)
  lastModifiedTime = _messages.StringField(11)
  name = _messages.StringField(12)
  otherInfoTypes = _messages.MessageField('GooglePrivacyDlpV2OtherInfoTypeSummary', 13, repeated=True)
  predictedInfoTypes = _messages.MessageField('GooglePrivacyDlpV2InfoTypeSummary', 14, repeated=True)
  profileLastGenerated = _messages.StringField(15)
  profileStatus = _messages.MessageField('GooglePrivacyDlpV2ProfileStatus', 16)
  projectDataProfile = _messages.StringField(17)
  resourceLabels = _messages.MessageField('ResourceLabelsValue', 18)
  resourceVisibility = _messages.EnumField('ResourceVisibilityValueValuesEnum', 19)
  rowCount = _messages.IntegerField(20)
  scannedColumnCount = _messages.IntegerField(21)
  sensitivityScore = _messages.MessageField('GooglePrivacyDlpV2SensitivityScore', 22)
  state = _messages.EnumField('StateValueValuesEnum', 23)
  tableId = _messages.StringField(24)
  tableSizeBytes = _messages.IntegerField(25)


class GooglePrivacyDlpV2TableLocation(_messages.Message):
  r"""Location of a finding within a table.

  Fields:
    rowIndex: The zero-based index of the row where the finding is located.
      Only populated for resources that have a natural ordering, not BigQuery.
      In BigQuery, to identify the row a finding came from, populate
      BigQueryOptions.identifying_fields with your primary key column names
      and when you store the findings the value of those columns will be
      stored inside of Finding.
  """

  rowIndex = _messages.IntegerField(1)


class GooglePrivacyDlpV2TableOptions(_messages.Message):
  r"""Instructions regarding the table content being inspected.

  Fields:
    identifyingFields: The columns that are the primary keys for table objects
      included in ContentItem. A copy of this cell's value will stored
      alongside alongside each finding so that the finding can be traced to
      the specific row it came from. No more than 3 may be provided.
  """

  identifyingFields = _messages.MessageField('GooglePrivacyDlpV2FieldId', 1, repeated=True)


class GooglePrivacyDlpV2TaggedField(_messages.Message):
  r"""A column with a semantic tag attached.

  Fields:
    customTag: A column can be tagged with a custom tag. In this case, the
      user must indicate an auxiliary table that contains statistical
      information on the possible values of this column (below).
    field: Required. Identifies the column.
    inferred: If no semantic tag is indicated, we infer the statistical model
      from the distribution of values in the input data
    infoType: A column can be tagged with a InfoType to use the relevant
      public dataset as a statistical model of population, if available. We
      currently support US ZIP codes, region codes, ages and genders. To
      programmatically obtain the list of supported InfoTypes, use
      ListInfoTypes with the supported_by=RISK_ANALYSIS filter.
  """

  customTag = _messages.StringField(1)
  field = _messages.MessageField('GooglePrivacyDlpV2FieldId', 2)
  inferred = _messages.MessageField('GoogleProtobufEmpty', 3)
  infoType = _messages.MessageField('GooglePrivacyDlpV2InfoType', 4)


class GooglePrivacyDlpV2ThrowError(_messages.Message):
  r"""Throw an error and fail the request when a transformation error occurs.
  """



class GooglePrivacyDlpV2TimePartConfig(_messages.Message):
  r"""For use with `Date`, `Timestamp`, and `TimeOfDay`, extract or preserve a
  portion of the value.

  Enums:
    PartToExtractValueValuesEnum: The part of the time to keep.

  Fields:
    partToExtract: The part of the time to keep.
  """

  class PartToExtractValueValuesEnum(_messages.Enum):
    r"""The part of the time to keep.

    Values:
      TIME_PART_UNSPECIFIED: Unused
      YEAR: [0-9999]
      MONTH: [1-12]
      DAY_OF_MONTH: [1-31]
      DAY_OF_WEEK: [1-7]
      WEEK_OF_YEAR: [1-53]
      HOUR_OF_DAY: [0-23]
    """
    TIME_PART_UNSPECIFIED = 0
    YEAR = 1
    MONTH = 2
    DAY_OF_MONTH = 3
    DAY_OF_WEEK = 4
    WEEK_OF_YEAR = 5
    HOUR_OF_DAY = 6

  partToExtract = _messages.EnumField('PartToExtractValueValuesEnum', 1)


class GooglePrivacyDlpV2TimeZone(_messages.Message):
  r"""Time zone of the date time object.

  Fields:
    offsetMinutes: Set only if the offset can be determined. Positive for time
      ahead of UTC. E.g. For "UTC-9", this value is -540.
  """

  offsetMinutes = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GooglePrivacyDlpV2TimespanConfig(_messages.Message):
  r"""Configuration of the timespan of the items to include in scanning.
  Currently only supported when inspecting Cloud Storage and BigQuery.

  Fields:
    enableAutoPopulationOfTimespanConfig: When the job is started by a
      JobTrigger we will automatically figure out a valid start_time to avoid
      scanning files that have not been modified since the last time the
      JobTrigger executed. This will be based on the time of the execution of
      the last run of the JobTrigger or the timespan end_time used in the last
      run of the JobTrigger.
    endTime: Exclude files, tables, or rows newer than this value. If not set,
      no upper time limit is applied.
    startTime: Exclude files, tables, or rows older than this value. If not
      set, no lower time limit is applied.
    timestampField: Specification of the field containing the timestamp of
      scanned items. Used for data sources like Datastore and BigQuery. *For
      BigQuery* If this value is not specified and the table was modified
      between the given start and end times, the entire table will be scanned.
      If this value is specified, then rows are filtered based on the given
      start and end times. Rows with a `NULL` value in the provided BigQuery
      column are skipped. Valid data types of the provided BigQuery column
      are: `INTEGER`, `DATE`, `TIMESTAMP`, and `DATETIME`. If your BigQuery
      table is [partitioned at ingestion
      time](https://cloud.google.com/bigquery/docs/partitioned-
      tables#ingestion_time), you can use any of the following pseudo-columns
      as your timestamp field. When used with Cloud DLP, these pseudo-column
      names are case sensitive. - _PARTITIONTIME - _PARTITIONDATE -
      _PARTITION_LOAD_TIME *For Datastore* If this value is specified, then
      entities are filtered based on the given start and end times. If an
      entity does not contain the provided timestamp property or contains
      empty or invalid values, then it is included. Valid data types of the
      provided timestamp property are: `TIMESTAMP`. See the [known
      issue](https://cloud.google.com/dlp/docs/known-issues#bq-timespan)
      related to this operation.
  """

  enableAutoPopulationOfTimespanConfig = _messages.BooleanField(1)
  endTime = _messages.StringField(2)
  startTime = _messages.StringField(3)
  timestampField = _messages.MessageField('GooglePrivacyDlpV2FieldId', 4)


class GooglePrivacyDlpV2TransformationConfig(_messages.Message):
  r"""User specified templates and configs for how to deidentify structured,
  unstructures, and image files. User must provide either a unstructured
  deidentify template or at least one redact image config.

  Fields:
    deidentifyTemplate: De-identify template. If this template is specified,
      it will serve as the default de-identify template. This template cannot
      contain `record_transformations` since it can be used for unstructured
      content such as free-form text files. If this template is not set, a
      default `ReplaceWithInfoTypeConfig` will be used to de-identify
      unstructured content.
    imageRedactTemplate: Image redact template. If this template is specified,
      it will serve as the de-identify template for images. If this template
      is not set, all findings in the image will be redacted with a black box.
    structuredDeidentifyTemplate: Structured de-identify template. If this
      template is specified, it will serve as the de-identify template for
      structured content such as delimited files and tables. If this template
      is not set but the `deidentify_template` is set, then
      `deidentify_template` will also apply to the structured content. If
      neither template is set, a default `ReplaceWithInfoTypeConfig` will be
      used to de-identify structured content.
  """

  deidentifyTemplate = _messages.StringField(1)
  imageRedactTemplate = _messages.StringField(2)
  structuredDeidentifyTemplate = _messages.StringField(3)


class GooglePrivacyDlpV2TransformationDescription(_messages.Message):
  r"""A flattened description of a `PrimitiveTransformation` or
  `RecordSuppression`.

  Enums:
    TypeValueValuesEnum: The transformation type.

  Fields:
    condition: A human-readable string representation of the `RecordCondition`
      corresponding to this transformation. Set if a `RecordCondition` was
      used to determine whether or not to apply this transformation. Examples:
      * (age_field > 85) * (age_field <= 18) * (zip_field exists) * (zip_field
      == 01234) && (city_field != "Springville") * (zip_field == 01234) &&
      (age_field <= 18) && (city_field exists)
    description: A description of the transformation. This is empty for a
      RECORD_SUPPRESSION, or is the output of calling toString() on the
      `PrimitiveTransformation` protocol buffer message for any other type of
      transformation.
    infoType: Set if the transformation was limited to a specific `InfoType`.
    type: The transformation type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The transformation type.

    Values:
      TRANSFORMATION_TYPE_UNSPECIFIED: Unused
      RECORD_SUPPRESSION: Record suppression
      REPLACE_VALUE: Replace value
      REPLACE_DICTIONARY: Replace value using a dictionary.
      REDACT: Redact
      CHARACTER_MASK: Character mask
      CRYPTO_REPLACE_FFX_FPE: FFX-FPE
      FIXED_SIZE_BUCKETING: Fixed size bucketing
      BUCKETING: Bucketing
      REPLACE_WITH_INFO_TYPE: Replace with info type
      TIME_PART: Time part
      CRYPTO_HASH: Crypto hash
      DATE_SHIFT: Date shift
      CRYPTO_DETERMINISTIC_CONFIG: Deterministic crypto
      REDACT_IMAGE: Redact image
    """
    TRANSFORMATION_TYPE_UNSPECIFIED = 0
    RECORD_SUPPRESSION = 1
    REPLACE_VALUE = 2
    REPLACE_DICTIONARY = 3
    REDACT = 4
    CHARACTER_MASK = 5
    CRYPTO_REPLACE_FFX_FPE = 6
    FIXED_SIZE_BUCKETING = 7
    BUCKETING = 8
    REPLACE_WITH_INFO_TYPE = 9
    TIME_PART = 10
    CRYPTO_HASH = 11
    DATE_SHIFT = 12
    CRYPTO_DETERMINISTIC_CONFIG = 13
    REDACT_IMAGE = 14

  condition = _messages.StringField(1)
  description = _messages.StringField(2)
  infoType = _messages.MessageField('GooglePrivacyDlpV2InfoType', 3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class GooglePrivacyDlpV2TransformationDetails(_messages.Message):
  r"""Details about a single transformation. This object contains a
  description of the transformation, information about whether the
  transformation was successfully applied, and the precise location where the
  transformation occurred. These details are stored in a user-specified
  BigQuery table.

  Fields:
    containerName: The top level name of the container where the
      transformation is located (this will be the source file name or table
      name).
    resourceName: The name of the job that completed the transformation.
    statusDetails: Status of the transformation, if transformation was not
      successful, this will specify what caused it to fail, otherwise it will
      show that the transformation was successful.
    transformation: Description of transformation. This would only contain
      more than one element if there were multiple matching transformations
      and which one to apply was ambiguous. Not set for states that contain no
      transformation, currently only state that contains no transformation is
      TransformationResultStateType.METADATA_UNRETRIEVABLE.
    transformationLocation: The precise location of the transformed content in
      the original container.
    transformedBytes: The number of bytes that were transformed. If
      transformation was unsuccessful or did not take place because there was
      no content to transform, this will be zero.
  """

  containerName = _messages.StringField(1)
  resourceName = _messages.StringField(2)
  statusDetails = _messages.MessageField('GooglePrivacyDlpV2TransformationResultStatus', 3)
  transformation = _messages.MessageField('GooglePrivacyDlpV2TransformationDescription', 4, repeated=True)
  transformationLocation = _messages.MessageField('GooglePrivacyDlpV2TransformationLocation', 5)
  transformedBytes = _messages.IntegerField(6)


class GooglePrivacyDlpV2TransformationDetailsStorageConfig(_messages.Message):
  r"""Config for storing transformation details.

  Fields:
    table: The BigQuery table in which to store the output. This may be an
      existing table or in a new table in an existing dataset. If table_id is
      not set a new one will be generated for you with the following format:
      dlp_googleapis_transformation_details_yyyy_mm_dd_[dlp_job_id]. Pacific
      time zone will be used for generating the date details.
  """

  table = _messages.MessageField('GooglePrivacyDlpV2BigQueryTable', 1)


class GooglePrivacyDlpV2TransformationErrorHandling(_messages.Message):
  r"""How to handle transformation errors during de-identification. A
  transformation error occurs when the requested transformation is
  incompatible with the data. For example, trying to de-identify an IP address
  using a `DateShift` transformation would result in a transformation error,
  since date info cannot be extracted from an IP address. Information about
  any incompatible transformations, and how they were handled, is returned in
  the response as part of the `TransformationOverviews`.

  Fields:
    leaveUntransformed: Ignore errors
    throwError: Throw an error
  """

  leaveUntransformed = _messages.MessageField('GooglePrivacyDlpV2LeaveUntransformed', 1)
  throwError = _messages.MessageField('GooglePrivacyDlpV2ThrowError', 2)


class GooglePrivacyDlpV2TransformationLocation(_messages.Message):
  r"""Specifies the location of a transformation.

  Enums:
    ContainerTypeValueValuesEnum: Information about the functionality of the
      container where this finding occurred, if available.

  Fields:
    containerType: Information about the functionality of the container where
      this finding occurred, if available.
    findingId: For infotype transformations, link to the corresponding
      findings ID so that location information does not need to be duplicated.
      Each findings ID correlates to an entry in the findings output table,
      this table only gets created when users specify to save findings (add
      the save findings action to the request).
    recordTransformation: For record transformations, provide a field and
      container information.
  """

  class ContainerTypeValueValuesEnum(_messages.Enum):
    r"""Information about the functionality of the container where this
    finding occurred, if available.

    Values:
      TRANSFORM_UNKNOWN_CONTAINER: <no description>
      TRANSFORM_BODY: <no description>
      TRANSFORM_METADATA: <no description>
      TRANSFORM_TABLE: <no description>
    """
    TRANSFORM_UNKNOWN_CONTAINER = 0
    TRANSFORM_BODY = 1
    TRANSFORM_METADATA = 2
    TRANSFORM_TABLE = 3

  containerType = _messages.EnumField('ContainerTypeValueValuesEnum', 1)
  findingId = _messages.StringField(2)
  recordTransformation = _messages.MessageField('GooglePrivacyDlpV2RecordTransformation', 3)


class GooglePrivacyDlpV2TransformationOverview(_messages.Message):
  r"""Overview of the modifications that occurred.

  Fields:
    transformationSummaries: Transformations applied to the dataset.
    transformedBytes: Total size in bytes that were transformed in some way.
  """

  transformationSummaries = _messages.MessageField('GooglePrivacyDlpV2TransformationSummary', 1, repeated=True)
  transformedBytes = _messages.IntegerField(2)


class GooglePrivacyDlpV2TransformationResultStatus(_messages.Message):
  r"""A GooglePrivacyDlpV2TransformationResultStatus object.

  Enums:
    ResultStatusTypeValueValuesEnum: Transformation result status type, this
      will be either SUCCESS, or it will be the reason for why the
      transformation was not completely successful.

  Fields:
    details: Detailed error codes and messages
    resultStatusType: Transformation result status type, this will be either
      SUCCESS, or it will be the reason for why the transformation was not
      completely successful.
  """

  class ResultStatusTypeValueValuesEnum(_messages.Enum):
    r"""Transformation result status type, this will be either SUCCESS, or it
    will be the reason for why the transformation was not completely
    successful.

    Values:
      STATE_TYPE_UNSPECIFIED: <no description>
      INVALID_TRANSFORM: This will be set when a finding could not be
        transformed (i.e. outside user set bucket range).
      BIGQUERY_MAX_ROW_SIZE_EXCEEDED: This will be set when a BigQuery
        transformation was successful but could not be stored back in BigQuery
        because the transformed row exceeds BigQuery's max row size.
      METADATA_UNRETRIEVABLE: This will be set when there is a finding in the
        custom metadata of a file, but at the write time of the transformed
        file, this key / value pair is unretrievable.
      SUCCESS: This will be set when the transformation and storing of it is
        successful.
    """
    STATE_TYPE_UNSPECIFIED = 0
    INVALID_TRANSFORM = 1
    BIGQUERY_MAX_ROW_SIZE_EXCEEDED = 2
    METADATA_UNRETRIEVABLE = 3
    SUCCESS = 4

  details = _messages.MessageField('GoogleRpcStatus', 1)
  resultStatusType = _messages.EnumField('ResultStatusTypeValueValuesEnum', 2)


class GooglePrivacyDlpV2TransformationSummary(_messages.Message):
  r"""Summary of a single transformation. Only one of 'transformation',
  'field_transformation', or 'record_suppress' will be set.

  Fields:
    field: Set if the transformation was limited to a specific FieldId.
    fieldTransformations: The field transformation that was applied. If
      multiple field transformations are requested for a single field, this
      list will contain all of them; otherwise, only one is supplied.
    infoType: Set if the transformation was limited to a specific InfoType.
    recordSuppress: The specific suppression option these stats apply to.
    results: Collection of all transformations that took place or had an
      error.
    transformation: The specific transformation these stats apply to.
    transformedBytes: Total size in bytes that were transformed in some way.
  """

  field = _messages.MessageField('GooglePrivacyDlpV2FieldId', 1)
  fieldTransformations = _messages.MessageField('GooglePrivacyDlpV2FieldTransformation', 2, repeated=True)
  infoType = _messages.MessageField('GooglePrivacyDlpV2InfoType', 3)
  recordSuppress = _messages.MessageField('GooglePrivacyDlpV2RecordSuppression', 4)
  results = _messages.MessageField('GooglePrivacyDlpV2SummaryResult', 5, repeated=True)
  transformation = _messages.MessageField('GooglePrivacyDlpV2PrimitiveTransformation', 6)
  transformedBytes = _messages.IntegerField(7)


class GooglePrivacyDlpV2TransientCryptoKey(_messages.Message):
  r"""Use this to have a random data crypto key generated. It will be
  discarded after the request finishes.

  Fields:
    name: Required. Name of the key. This is an arbitrary string used to
      differentiate different keys. A unique key is generated per name: two
      separate `TransientCryptoKey` protos share the same generated key if
      their names are the same. When the data crypto key is generated, this
      name is not used in any way (repeating the api call will result in a
      different key being generated).
  """

  name = _messages.StringField(1)


class GooglePrivacyDlpV2Trigger(_messages.Message):
  r"""What event needs to occur for a new job to be started.

  Fields:
    manual: For use with hybrid jobs. Jobs must be manually created and
      finished.
    schedule: Create a job on a repeating basis based on the elapse of time.
  """

  manual = _messages.MessageField('GooglePrivacyDlpV2Manual', 1)
  schedule = _messages.MessageField('GooglePrivacyDlpV2Schedule', 2)


class GooglePrivacyDlpV2UnwrappedCryptoKey(_messages.Message):
  r"""Using raw keys is prone to security risks due to accidentally leaking
  the key. Choose another type of key if possible.

  Fields:
    key: Required. A 128/192/256 bit key.
  """

  key = _messages.BytesField(1)


class GooglePrivacyDlpV2UpdateDeidentifyTemplateRequest(_messages.Message):
  r"""Request message for UpdateDeidentifyTemplate.

  Fields:
    deidentifyTemplate: New DeidentifyTemplate value.
    updateMask: Mask to control which fields get updated.
  """

  deidentifyTemplate = _messages.MessageField('GooglePrivacyDlpV2DeidentifyTemplate', 1)
  updateMask = _messages.StringField(2)


class GooglePrivacyDlpV2UpdateInspectTemplateRequest(_messages.Message):
  r"""Request message for UpdateInspectTemplate.

  Fields:
    inspectTemplate: New InspectTemplate value.
    updateMask: Mask to control which fields get updated.
  """

  inspectTemplate = _messages.MessageField('GooglePrivacyDlpV2InspectTemplate', 1)
  updateMask = _messages.StringField(2)


class GooglePrivacyDlpV2UpdateJobTriggerRequest(_messages.Message):
  r"""Request message for UpdateJobTrigger.

  Fields:
    jobTrigger: New JobTrigger value.
    updateMask: Mask to control which fields get updated.
  """

  jobTrigger = _messages.MessageField('GooglePrivacyDlpV2JobTrigger', 1)
  updateMask = _messages.StringField(2)


class GooglePrivacyDlpV2UpdateStoredInfoTypeRequest(_messages.Message):
  r"""Request message for UpdateStoredInfoType.

  Fields:
    config: Updated configuration for the storedInfoType. If not provided, a
      new version of the storedInfoType will be created with the existing
      configuration.
    updateMask: Mask to control which fields get updated.
  """

  config = _messages.MessageField('GooglePrivacyDlpV2StoredInfoTypeConfig', 1)
  updateMask = _messages.StringField(2)


class GooglePrivacyDlpV2Value(_messages.Message):
  r"""Set of primitive values supported by the system. Note that for the
  purposes of inspection or transformation, the number of bytes considered to
  comprise a 'Value' is based on its representation as a UTF-8 encoded string.
  For example, if 'integer_value' is set to 123456789, the number of bytes
  would be counted as 9, even though an int64 only holds up to 8 bytes of
  data.

  Enums:
    DayOfWeekValueValueValuesEnum: day of week

  Fields:
    booleanValue: boolean
    dateValue: date
    dayOfWeekValue: day of week
    floatValue: float
    integerValue: integer
    stringValue: string
    timeValue: time of day
    timestampValue: timestamp
  """

  class DayOfWeekValueValueValuesEnum(_messages.Enum):
    r"""day of week

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  booleanValue = _messages.BooleanField(1)
  dateValue = _messages.MessageField('GoogleTypeDate', 2)
  dayOfWeekValue = _messages.EnumField('DayOfWeekValueValueValuesEnum', 3)
  floatValue = _messages.FloatField(4)
  integerValue = _messages.IntegerField(5)
  stringValue = _messages.StringField(6)
  timeValue = _messages.MessageField('GoogleTypeTimeOfDay', 7)
  timestampValue = _messages.StringField(8)


class GooglePrivacyDlpV2ValueFrequency(_messages.Message):
  r"""A value of a field, including its frequency.

  Fields:
    count: How many times the value is contained in the field.
    value: A value contained in the field in question.
  """

  count = _messages.IntegerField(1)
  value = _messages.MessageField('GooglePrivacyDlpV2Value', 2)


class GooglePrivacyDlpV2VersionDescription(_messages.Message):
  r"""Details about each available version for an infotype.

  Fields:
    description: Description of the version.
    version: Name of the version
  """

  description = _messages.StringField(1)
  version = _messages.StringField(2)


class GooglePrivacyDlpV2WordList(_messages.Message):
  r"""Message defining a list of words or phrases to search for in the data.

  Fields:
    words: Words or phrases defining the dictionary. The dictionary must
      contain at least one phrase and every phrase must contain at least 2
      characters that are letters or digits. [required]
  """

  words = _messages.StringField(1, repeated=True)


class GoogleProtobufEmpty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class GoogleTypeDate(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleTypeTimeOfDay(_messages.Message):
  r"""Represents a time of day. The date and time zone are either not
  significant or are specified elsewhere. An API may choose to allow leap
  seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.

  Fields:
    hours: Hours of day in 24 hour format. Should be from 0 to 23. An API may
      choose to allow the value "24:00:00" for scenarios like business closing
      time.
    minutes: Minutes of hour of day. Must be from 0 to 59.
    nanos: Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999.
    seconds: Seconds of minutes of the time. Must normally be from 0 to 59. An
      API may allow the value 60 if it allows leap-seconds.
  """

  hours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

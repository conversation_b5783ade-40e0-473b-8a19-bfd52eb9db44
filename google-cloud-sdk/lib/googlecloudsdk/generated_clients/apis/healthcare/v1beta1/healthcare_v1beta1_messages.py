"""Generated message classes for healthcare version v1beta1.

Manage, store, and access healthcare data in Google Cloud Platform.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'healthcare'


class Action(_messages.Message):
  r"""Specifies a selection of tags and an `Action` to apply to each one.

  Fields:
    cleanImageTag: Inspect image and transform sensitive burnt-in text.
      Doesn't apply to elements nested in a sequence, which revert to `Keep`.
      Supported [tags](http://dicom.nema.org/medical/dicom/2018e/output/chtml/
      part06/chapter_6.html): PixelData
    cleanTextTag: Inspect text and transform sensitive text. Configurable via
      TextConfig. Supported Value Representations: AE, LO, LT, PN, SH, ST, UC,
      UT, DA, DT, AS
    deleteTag: Delete tag.
    keepTag: Keep tag unchanged.
    queries: Select all tags with the listed tag IDs, names, or Value
      Representations (VRs). Examples: ID: "00100010" Keyword: "PatientName"
      VR: "PN"
    recurseTag: Recursively apply DICOM de-id to tags nested in a sequence.
      Supported [Value Representation] (http://dicom.nema.org/medical/dicom/20
      18e/output/chtml/part05/sect_6.2.html#table_6.2-1): SQ
    regenUidTag: Replace UID with a new generated UID. Supported [Value
      Representation] (http://dicom.nema.org/medical/dicom/2018e/output/chtml/
      part05/sect_6.2.html#table_6.2-1): UI
    removeTag: Replace with empty tag.
    resetTag: Reset tag to a placeholder value.
  """

  cleanImageTag = _messages.MessageField('ImageConfig', 1)
  cleanTextTag = _messages.MessageField('CleanTextTag', 2)
  deleteTag = _messages.MessageField('DeleteTag', 3)
  keepTag = _messages.MessageField('KeepTag', 4)
  queries = _messages.StringField(5, repeated=True)
  recurseTag = _messages.MessageField('RecurseTag', 6)
  regenUidTag = _messages.MessageField('RegenUidTag', 7)
  removeTag = _messages.MessageField('RemoveTag', 8)
  resetTag = _messages.MessageField('ResetTag', 9)


class ActivateConsentRequest(_messages.Message):
  r"""Activates the latest revision of the specified Consent by committing a
  new revision with `state` updated to `ACTIVE`. If the latest revision of the
  given Consent is in the `ACTIVE` state, no new revision is committed. A
  FAILED_PRECONDITION error occurs if the latest revision of the given consent
  is in the `REJECTED` or `REVOKED` state.

  Fields:
    consentArtifact: Required. The resource name of the Consent artifact that
      contains documentation of the user's consent, of the form `projects/{pro
      ject_id}/locations/{location_id}/datasets/{dataset_id}/consentStores/{co
      nsent_store_id}/consentArtifacts/{consent_artifact_id}`. If the draft
      Consent had a Consent artifact, this Consent artifact overwrites it.
    expireTime: Timestamp in UTC of when this Consent is considered expired.
    ttl: The time to live for this Consent from when it is marked as active.
  """

  consentArtifact = _messages.StringField(1)
  expireTime = _messages.StringField(2)
  ttl = _messages.StringField(3)


class AnalyzeEntitiesRequest(_messages.Message):
  r"""The request to analyze healthcare entities in a document.

  Enums:
    AlternativeOutputFormatValueValuesEnum: Optional. Alternative output
      format to be generated based on the results of analysis.
    LicensedVocabulariesValueListEntryValuesEnum:

  Fields:
    alternativeOutputFormat: Optional. Alternative output format to be
      generated based on the results of analysis.
    documentContent: document_content is a document to be annotated.
    licensedVocabularies: A list of licensed vocabularies to use in the
      request, in addition to the default unlicensed vocabularies.
  """

  class AlternativeOutputFormatValueValuesEnum(_messages.Enum):
    r"""Optional. Alternative output format to be generated based on the
    results of analysis.

    Values:
      ALTERNATIVE_OUTPUT_FORMAT_UNSPECIFIED: No alternative output format is
        specified.
      FHIR_BUNDLE: FHIR bundle output.
    """
    ALTERNATIVE_OUTPUT_FORMAT_UNSPECIFIED = 0
    FHIR_BUNDLE = 1

  class LicensedVocabulariesValueListEntryValuesEnum(_messages.Enum):
    r"""LicensedVocabulariesValueListEntryValuesEnum enum type.

    Values:
      LICENSED_VOCABULARY_UNSPECIFIED: No licensed vocabulary specified.
      ICD10CM: ICD-10-CM vocabulary
      SNOMEDCT_US: SNOMED CT (US version) vocabulary
    """
    LICENSED_VOCABULARY_UNSPECIFIED = 0
    ICD10CM = 1
    SNOMEDCT_US = 2

  alternativeOutputFormat = _messages.EnumField('AlternativeOutputFormatValueValuesEnum', 1)
  documentContent = _messages.StringField(2)
  licensedVocabularies = _messages.EnumField('LicensedVocabulariesValueListEntryValuesEnum', 3, repeated=True)


class AnalyzeEntitiesResponse(_messages.Message):
  r"""Includes recognized entity mentions and relationships between them.

  Fields:
    entities: The union of all the candidate entities that the entity_mentions
      in this response could link to. These are UMLS concepts or normalized
      mention content.
    entityMentions: The `entity_mentions` field contains all the annotated
      medical entities that were mentioned in the provided document.
    fhirBundle: The FHIR bundle ([`R4`](http://hl7.org/fhir/R4/bundle.html))
      that includes all the entities, the entity mentions, and the
      relationships in JSON format.
    relationships: relationships contains all the binary relationships that
      were identified between entity mentions within the provided document.
  """

  entities = _messages.MessageField('Entity', 1, repeated=True)
  entityMentions = _messages.MessageField('EntityMention', 2, repeated=True)
  fhirBundle = _messages.StringField(3)
  relationships = _messages.MessageField('EntityMentionRelationship', 4, repeated=True)


class Annotation(_messages.Message):
  r"""An annotation record.

  Messages:
    CustomDataValue: Additional information for this annotation record, such
      as annotator and verifier information or study campaign.

  Fields:
    annotationSource: Details of the source.
    customData: Additional information for this annotation record, such as
      annotator and verifier information or study campaign.
    imageAnnotation: Annotations for images. For example, bounding polygons.
    name: Resource name of the Annotation, of the form `projects/{project_id}/
      locations/{location_id}/datasets/{dataset_id}/annotationStores/{annotati
      on_store_id}/annotations/{annotation_id}`.
    resourceAnnotation: Annotations for resource. For example, classification
      tags.
    textAnnotation: Annotations for sensitive texts. For example, a range that
      describes the location of sensitive text.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomDataValue(_messages.Message):
    r"""Additional information for this annotation record, such as annotator
    and verifier information or study campaign.

    Messages:
      AdditionalProperty: An additional property for a CustomDataValue object.

    Fields:
      additionalProperties: Additional properties of type CustomDataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomDataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotationSource = _messages.MessageField('AnnotationSource', 1)
  customData = _messages.MessageField('CustomDataValue', 2)
  imageAnnotation = _messages.MessageField('ImageAnnotation', 3)
  name = _messages.StringField(4)
  resourceAnnotation = _messages.MessageField('ResourceAnnotation', 5)
  textAnnotation = _messages.MessageField('SensitiveTextAnnotation', 6)


class AnnotationConfig(_messages.Message):
  r"""Specifies how to store annotations during de-identification operation.

  Fields:
    annotationStoreName: The name of the annotation store, in the form `projec
      ts/{project_id}/locations/{location_id}/datasets/{dataset_id}/annotation
      Stores/{annotation_store_id}`). * The destination annotation store must
      be in the same project as the source data. De-identifying data across
      multiple projects is not supported. * The destination annotation store
      must exist when using DeidentifyDicomStore or DeidentifyFhirStore.
      DeidentifyDataset automatically creates the destination annotation
      store.
    storeQuote: If set to true, the sensitive texts are included in
      SensitiveTextAnnotation of Annotation.
  """

  annotationStoreName = _messages.StringField(1)
  storeQuote = _messages.BooleanField(2)


class AnnotationSource(_messages.Message):
  r"""AnnotationSource holds the source information of the annotation.

  Fields:
    cloudHealthcareSource: Cloud Healthcare API resource.
  """

  cloudHealthcareSource = _messages.MessageField('CloudHealthcareSource', 1)


class AnnotationStore(_messages.Message):
  r"""An Annotation store that can store annotation resources such as labels
  and tags for text, image and audio.

  Messages:
    LabelsValue: Optional. User-supplied key-value pairs used to organize
      Annotation stores. Label keys must be between 1 and 63 characters long,
      have a UTF-8 encoding of maximum 128 bytes, and must conform to the
      following PCRE regular expression: \p{Ll}\p{Lo}{0,62} Label values must
      be between 1 and 63 characters long, have a UTF-8 encoding of maximum
      128 bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.

  Fields:
    labels: Optional. User-supplied key-value pairs used to organize
      Annotation stores. Label keys must be between 1 and 63 characters long,
      have a UTF-8 encoding of maximum 128 bytes, and must conform to the
      following PCRE regular expression: \p{Ll}\p{Lo}{0,62} Label values must
      be between 1 and 63 characters long, have a UTF-8 encoding of maximum
      128 bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.
    name: Resource name of the Annotation store, of the form `projects/{projec
      t_id}/locations/{location_id}/datasets/{dataset_id}/annotationStores/{an
      notation_store_id}`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User-supplied key-value pairs used to organize Annotation
    stores. Label keys must be between 1 and 63 characters long, have a UTF-8
    encoding of maximum 128 bytes, and must conform to the following PCRE
    regular expression: \p{Ll}\p{Lo}{0,62} Label values must be between 1 and
    63 characters long, have a UTF-8 encoding of maximum 128 bytes, and must
    conform to the following PCRE regular expression:
    [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated with
    a given store.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  name = _messages.StringField(2)


class ArchiveUserDataMappingRequest(_messages.Message):
  r"""Archives the specified User data mapping."""


class ArchiveUserDataMappingResponse(_messages.Message):
  r"""Archives the specified User data mapping."""


class Attribute(_messages.Message):
  r"""An attribute value for a Consent or User data mapping. Each Attribute
  must have a corresponding AttributeDefinition in the consent store that
  defines the default and allowed values.

  Fields:
    attributeDefinitionId: Indicates the name of an attribute defined in the
      consent store.
    values: The value of the attribute. Must be an acceptable value as defined
      in the consent store. For example, if the consent store defines "data
      type" with acceptable values "questionnaire" and "step-count", when the
      attribute name is data type, this field must contain one of those
      values.
  """

  attributeDefinitionId = _messages.StringField(1)
  values = _messages.StringField(2, repeated=True)


class AttributeDefinition(_messages.Message):
  r"""A client-defined consent attribute.

  Enums:
    CategoryValueValuesEnum: Required. The category of the attribute. The
      value of this field cannot be changed after creation.

  Fields:
    allowedValues: Required. Possible values for the attribute. The number of
      allowed values must not exceed 500. An empty list is invalid. The list
      can only be expanded after creation.
    category: Required. The category of the attribute. The value of this field
      cannot be changed after creation.
    consentDefaultValues: Optional. Default values of the attribute in
      Consents. If no default values are specified, it defaults to an empty
      value.
    dataMappingDefaultValue: Optional. Default value of the attribute in User
      data mappings. If no default value is specified, it defaults to an empty
      value. This field is only applicable to attributes of the category
      `RESOURCE`.
    description: Optional. A description of the attribute.
    name: Resource name of the Attribute definition, of the form `projects/{pr
      oject_id}/locations/{location_id}/datasets/{dataset_id}/consentStores/{c
      onsent_store_id}/attributeDefinitions/{attribute_definition_id}`. Cannot
      be changed after creation.
  """

  class CategoryValueValuesEnum(_messages.Enum):
    r"""Required. The category of the attribute. The value of this field
    cannot be changed after creation.

    Values:
      CATEGORY_UNSPECIFIED: No category specified. This option is invalid.
      RESOURCE: Specify this category when this attribute describes the
        properties of resources. For example, data anonymity or data type.
      REQUEST: Specify this category when this attribute describes the
        properties of requests. For example, requester's role or requester's
        organization.
    """
    CATEGORY_UNSPECIFIED = 0
    RESOURCE = 1
    REQUEST = 2

  allowedValues = _messages.StringField(1, repeated=True)
  category = _messages.EnumField('CategoryValueValuesEnum', 2)
  consentDefaultValues = _messages.StringField(3, repeated=True)
  dataMappingDefaultValue = _messages.StringField(4)
  description = _messages.StringField(5)
  name = _messages.StringField(6)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class BatchGetMessagesResponse(_messages.Message):
  r"""Gets multiple messages in a specified HL7v2 store.

  Fields:
    messages: The returned Messages. See `MessageView` for populated fields.
  """

  messages = _messages.MessageField('Message', 1, repeated=True)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class BoundingPoly(_messages.Message):
  r"""A bounding polygon for the detected image annotation.

  Fields:
    label: A description of this polygon.
    vertices: List of the vertices of this polygon.
  """

  label = _messages.StringField(1)
  vertices = _messages.MessageField('Vertex', 2, repeated=True)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CharacterMaskConfig(_messages.Message):
  r"""Mask a string by replacing its characters with a fixed character.

  Fields:
    maskingCharacter: Character to mask the sensitive values. If not supplied,
      defaults to "*".
  """

  maskingCharacter = _messages.StringField(1)


class CharacterMaskField(_messages.Message):
  r"""Replace field value with masking character. Supported
  [types](https://www.hl7.org/fhir/datatypes.html): Code, Decimal, HumanName,
  Id, LanguageCode, Markdown, Oid, String, Uri, Uuid, Xhtml
  """



class CheckDataAccessRequest(_messages.Message):
  r"""Checks if a particular data_id of a User data mapping in the given
  consent store is consented for a given use.

  Enums:
    ResponseViewValueValuesEnum: Optional. The view for
      CheckDataAccessResponse. If unspecified, defaults to `BASIC` and returns
      `consented` as `TRUE` or `FALSE`.

  Messages:
    RequestAttributesValue: The values of request attributes associated with
      this access request.

  Fields:
    consentList: Optional. Specific Consents to evaluate the access request
      against. These Consents must have the same `user_id` as the evaluated
      User data mapping, must exist in the current `consent_store`, and have a
      `state` of either `ACTIVE` or `DRAFT`. A maximum of 100 Consents can be
      provided here. If no selection is specified, the access request is
      evaluated against all `ACTIVE` unexpired Consents with the same
      `user_id` as the evaluated User data mapping.
    dataId: Required. The unique identifier of the resource to check access
      for. This identifier must correspond to a User data mapping in the given
      consent store.
    requestAttributes: The values of request attributes associated with this
      access request.
    responseView: Optional. The view for CheckDataAccessResponse. If
      unspecified, defaults to `BASIC` and returns `consented` as `TRUE` or
      `FALSE`.
  """

  class ResponseViewValueValuesEnum(_messages.Enum):
    r"""Optional. The view for CheckDataAccessResponse. If unspecified,
    defaults to `BASIC` and returns `consented` as `TRUE` or `FALSE`.

    Values:
      RESPONSE_VIEW_UNSPECIFIED: No response view specified. The API will
        default to the BASIC view.
      BASIC: Only the `consented` field is populated in
        CheckDataAccessResponse.
      FULL: All fields within CheckDataAccessResponse are populated. When set
        to `FULL`, all `ACTIVE` Consents are evaluated even if a matching
        policy is found during evaluation.
    """
    RESPONSE_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RequestAttributesValue(_messages.Message):
    r"""The values of request attributes associated with this access request.

    Messages:
      AdditionalProperty: An additional property for a RequestAttributesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        RequestAttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RequestAttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  consentList = _messages.MessageField('ConsentList', 1)
  dataId = _messages.StringField(2)
  requestAttributes = _messages.MessageField('RequestAttributesValue', 3)
  responseView = _messages.EnumField('ResponseViewValueValuesEnum', 4)


class CheckDataAccessResponse(_messages.Message):
  r"""Checks if a particular data_id of a User data mapping in the given
  consent store is consented for a given use.

  Messages:
    ConsentDetailsValue: The resource names of all evaluated Consents mapped
      to their evaluation.

  Fields:
    consentDetails: The resource names of all evaluated Consents mapped to
      their evaluation.
    consented: Whether the requested resource is consented for the given use.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConsentDetailsValue(_messages.Message):
    r"""The resource names of all evaluated Consents mapped to their
    evaluation.

    Messages:
      AdditionalProperty: An additional property for a ConsentDetailsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ConsentDetailsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConsentDetailsValue object.

      Fields:
        key: Name of the additional property.
        value: A ConsentEvaluation attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ConsentEvaluation', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  consentDetails = _messages.MessageField('ConsentDetailsValue', 1)
  consented = _messages.BooleanField(2)


class CleanDescriptorsOption(_messages.Message):
  r"""This option is based on the DICOM Standard's [Clean Descriptors Option](
  http://dicom.nema.org/medical/dicom/2018e/output/chtml/part15/sect_E.3.5.htm
  l), and the `CleanText` `Action` is applied to all the specified fields.
  When cleaning text, the process attempts to transform phrases matching any
  of the tags marked for removal (action codes D, Z, X, and U) in the [Basic P
  rofile](http://dicom.nema.org/medical/dicom/2018e/output/chtml/part15/chapte
  r_E.html). These contextual phrases are replaced with the token "[CTX]".
  This option uses an additional `InfoType` during inspection.
  """



class CleanTextField(_messages.Message):
  r"""Inspect text and transform sensitive text. Configure using `TextConfig`.
  Supported [types](https://www.hl7.org/fhir/datatypes.html): Code, Date,
  DateTime, Decimal, HumanName, Id, LanguageCode, Markdown, Oid, String, Uri,
  Uuid, Xhtml
  """



class CleanTextTag(_messages.Message):
  r"""Inspect text and transform sensitive text. Configurable using
  `TextConfig`. Supported [Value Representations] (http://dicom.nema.org/medic
  al/dicom/2018e/output/chtml/part05/sect_6.2.html#table_6.2-1): AE, LO, LT,
  PN, SH, ST, UC, UT, DA, DT, AS
  """



class CloudHealthcareSource(_messages.Message):
  r"""Cloud Healthcare API resource.

  Fields:
    name: Full path of a Cloud Healthcare API resource.
  """

  name = _messages.StringField(1)


class ConfigureSearchRequest(_messages.Message):
  r"""Request to configure the search parameters for the specified FHIR store.

  Fields:
    canonicalUrls: The canonical URLs of the search parameters that are
      intended to be used for the FHIR store. See
      https://www.hl7.org/fhir/references.html#canonical for explanation on
      FHIR canonical urls
    validateOnly: If `validate_only` is set to true, the method will compile
      all the search parameters without actually setting the search config for
      the store and triggering the reindex.
  """

  canonicalUrls = _messages.StringField(1, repeated=True)
  validateOnly = _messages.BooleanField(2)


class Consent(_messages.Message):
  r"""Represents a user's consent.

  Enums:
    StateValueValuesEnum: Required. Indicates the current state of this
      Consent.

  Messages:
    MetadataValue: Optional. User-supplied key-value pairs used to organize
      Consent resources. Metadata keys must: - be between 1 and 63 characters
      long - have a UTF-8 encoding of maximum 128 bytes - begin with a letter
      - consist of up to 63 characters including lowercase letters, numeric
      characters, underscores, and dashes Metadata values must be: - be
      between 1 and 63 characters long - have a UTF-8 encoding of maximum 128
      bytes - consist of up to 63 characters including lowercase letters,
      numeric characters, underscores, and dashes No more than 64 metadata
      entries can be associated with a given consent.

  Fields:
    consentArtifact: Required. The resource name of the Consent artifact that
      contains proof of the end user's consent, of the form `projects/{project
      _id}/locations/{location_id}/datasets/{dataset_id}/consentStores/{consen
      t_store_id}/consentArtifacts/{consent_artifact_id}`.
    expireTime: Timestamp in UTC of when this Consent is considered expired.
    metadata: Optional. User-supplied key-value pairs used to organize Consent
      resources. Metadata keys must: - be between 1 and 63 characters long -
      have a UTF-8 encoding of maximum 128 bytes - begin with a letter -
      consist of up to 63 characters including lowercase letters, numeric
      characters, underscores, and dashes Metadata values must be: - be
      between 1 and 63 characters long - have a UTF-8 encoding of maximum 128
      bytes - consist of up to 63 characters including lowercase letters,
      numeric characters, underscores, and dashes No more than 64 metadata
      entries can be associated with a given consent.
    name: Resource name of the Consent, of the form `projects/{project_id}/loc
      ations/{location_id}/datasets/{dataset_id}/consentStores/{consent_store_
      id}/consents/{consent_id}`. Cannot be changed after creation.
    policies: Optional. Represents a user's consent in terms of the resources
      that can be accessed and under what conditions.
    revisionCreateTime: Output only. The timestamp that the revision was
      created.
    revisionId: Output only. The revision ID of the Consent. The format is an
      8-character hexadecimal string. Refer to a specific revision of a
      Consent by appending `@{revision_id}` to the Consent's resource name.
    state: Required. Indicates the current state of this Consent.
    ttl: Input only. The time to live for this Consent from when it is
      created.
    userId: Required. User's UUID provided by the client.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Required. Indicates the current state of this Consent.

    Values:
      STATE_UNSPECIFIED: No state specified. Treated as ACTIVE only at the
        time of resource creation.
      ACTIVE: The Consent is active and is considered when evaluating a user's
        consent on resources.
      ARCHIVED: The archived state is currently not being used.
      REVOKED: A revoked Consent is not considered when evaluating a user's
        consent on resources.
      DRAFT: A draft Consent is not considered when evaluating a user's
        consent on resources unless explicitly specified.
      REJECTED: When a draft Consent is rejected by a user, it is set to a
        rejected state. A rejected Consent is not considered when evaluating a
        user's consent on resources.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    ARCHIVED = 2
    REVOKED = 3
    DRAFT = 4
    REJECTED = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Optional. User-supplied key-value pairs used to organize Consent
    resources. Metadata keys must: - be between 1 and 63 characters long -
    have a UTF-8 encoding of maximum 128 bytes - begin with a letter - consist
    of up to 63 characters including lowercase letters, numeric characters,
    underscores, and dashes Metadata values must be: - be between 1 and 63
    characters long - have a UTF-8 encoding of maximum 128 bytes - consist of
    up to 63 characters including lowercase letters, numeric characters,
    underscores, and dashes No more than 64 metadata entries can be associated
    with a given consent.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  consentArtifact = _messages.StringField(1)
  expireTime = _messages.StringField(2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  policies = _messages.MessageField('GoogleCloudHealthcareV1beta1ConsentPolicy', 5, repeated=True)
  revisionCreateTime = _messages.StringField(6)
  revisionId = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  ttl = _messages.StringField(9)
  userId = _messages.StringField(10)


class ConsentArtifact(_messages.Message):
  r"""Documentation of a user's consent.

  Messages:
    MetadataValue: Optional. Metadata associated with the Consent artifact.
      For example, the consent locale or user agent version.

  Fields:
    consentContentScreenshots: Optional. Screenshots, PDFs, or other binary
      information documenting the user's consent.
    consentContentVersion: Optional. An string indicating the version of the
      consent information shown to the user.
    guardianSignature: Optional. A signature from a guardian.
    metadata: Optional. Metadata associated with the Consent artifact. For
      example, the consent locale or user agent version.
    name: Resource name of the Consent artifact, of the form `projects/{projec
      t_id}/locations/{location_id}/datasets/{dataset_id}/consentStores/{conse
      nt_store_id}/consentArtifacts/{consent_artifact_id}`. Cannot be changed
      after creation.
    userId: Required. User's UUID provided by the client.
    userSignature: Optional. User's signature.
    witnessSignature: Optional. A signature from a witness.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Optional. Metadata associated with the Consent artifact. For example,
    the consent locale or user agent version.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  consentContentScreenshots = _messages.MessageField('Image', 1, repeated=True)
  consentContentVersion = _messages.StringField(2)
  guardianSignature = _messages.MessageField('Signature', 3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)
  userId = _messages.StringField(6)
  userSignature = _messages.MessageField('Signature', 7)
  witnessSignature = _messages.MessageField('Signature', 8)


class ConsentEvaluation(_messages.Message):
  r"""The detailed evaluation of a particular Consent.

  Enums:
    EvaluationResultValueValuesEnum: The evaluation result.

  Fields:
    evaluationResult: The evaluation result.
  """

  class EvaluationResultValueValuesEnum(_messages.Enum):
    r"""The evaluation result.

    Values:
      EVALUATION_RESULT_UNSPECIFIED: No evaluation result specified. This
        option is invalid.
      NOT_APPLICABLE: The Consent is not applicable to the requested access
        determination. For example, the Consent does not apply to the user for
        which the access determination is requested, or it has a `state` of
        `REVOKED`, or it has expired.
      NO_MATCHING_POLICY: The Consent does not have a policy that matches the
        `resource_attributes` of the evaluated resource.
      NO_SATISFIED_POLICY: The Consent has at least one policy that matches
        the `resource_attributes` of the evaluated resource, but no
        `authorization_rule` was satisfied.
      HAS_SATISFIED_POLICY: The Consent has at least one policy that matches
        the `resource_attributes` of the evaluated resource, and at least one
        `authorization_rule` was satisfied.
    """
    EVALUATION_RESULT_UNSPECIFIED = 0
    NOT_APPLICABLE = 1
    NO_MATCHING_POLICY = 2
    NO_SATISFIED_POLICY = 3
    HAS_SATISFIED_POLICY = 4

  evaluationResult = _messages.EnumField('EvaluationResultValueValuesEnum', 1)


class ConsentList(_messages.Message):
  r"""List of resource names of Consent resources.

  Fields:
    consents: The resource names of the Consents to evaluate against, of the
      form `projects/{project_id}/locations/{location_id}/datasets/{dataset_id
      }/consentStores/{consent_store_id}/consents/{consent_id}`.
  """

  consents = _messages.StringField(1, repeated=True)


class ConsentStore(_messages.Message):
  r"""Represents a consent store.

  Messages:
    LabelsValue: Optional. User-supplied key-value pairs used to organize
      consent stores. Label keys must be between 1 and 63 characters long,
      have a UTF-8 encoding of maximum 128 bytes, and must conform to the
      following PCRE regular expression: \p{Ll}\p{Lo}{0,62}. Label values must
      be between 1 and 63 characters long, have a UTF-8 encoding of maximum
      128 bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63}. No more than 64 labels can be associated
      with a given store. For more information:
      https://cloud.google.com/healthcare/docs/how-tos/labeling-resources

  Fields:
    defaultConsentTtl: Optional. Default time to live for Consents created in
      this store. Must be at least 24 hours. Updating this field will not
      affect the expiration time of existing consents.
    enableConsentCreateOnUpdate: Optional. If `true`, UpdateConsent creates
      the Consent if it does not already exist. If unspecified, defaults to
      `false`.
    labels: Optional. User-supplied key-value pairs used to organize consent
      stores. Label keys must be between 1 and 63 characters long, have a
      UTF-8 encoding of maximum 128 bytes, and must conform to the following
      PCRE regular expression: \p{Ll}\p{Lo}{0,62}. Label values must be
      between 1 and 63 characters long, have a UTF-8 encoding of maximum 128
      bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63}. No more than 64 labels can be associated
      with a given store. For more information:
      https://cloud.google.com/healthcare/docs/how-tos/labeling-resources
    name: Resource name of the consent store, of the form `projects/{project_i
      d}/locations/{location_id}/datasets/{dataset_id}/consentStores/{consent_
      store_id}`. Cannot be changed after creation.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. User-supplied key-value pairs used to organize consent
    stores. Label keys must be between 1 and 63 characters long, have a UTF-8
    encoding of maximum 128 bytes, and must conform to the following PCRE
    regular expression: \p{Ll}\p{Lo}{0,62}. Label values must be between 1 and
    63 characters long, have a UTF-8 encoding of maximum 128 bytes, and must
    conform to the following PCRE regular expression:
    [\p{Ll}\p{Lo}\p{N}_-]{0,63}. No more than 64 labels can be associated with
    a given store. For more information:
    https://cloud.google.com/healthcare/docs/how-tos/labeling-resources

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  defaultConsentTtl = _messages.StringField(1)
  enableConsentCreateOnUpdate = _messages.BooleanField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)


class ContextualDeidConfig(_messages.Message):
  r"""The fields that aren't marked `Keep` or `CleanText` in the `BASIC`
  profile are collected into a contextual phrase list. For fields marked
  `CleanText`, the process attempts to transform phrases matching these
  contextual entries. These contextual phrases are replaced with the token
  "[CTX]". This feature uses an additional InfoType during inspection.
  """



class CreateMessageRequest(_messages.Message):
  r"""Creates a new message.

  Fields:
    message: HL7v2 message.
  """

  message = _messages.MessageField('Message', 1)


class CryptoHashConfig(_messages.Message):
  r"""Pseudonymization method that generates surrogates via cryptographic
  hashing. Uses SHA-256. Outputs a base64-encoded representation of the hashed
  output. For example, `L7k0BHmF1ha5U3NfGykjro4xWi1MPVQPjhMAZbSV9mM=`.

  Fields:
    cryptoKey: An AES 128/192/256 bit key. Causes the hash to be computed
      based on this key. A default key is generated for each Deidentify
      operation and is used when neither `crypto_key` nor `kms_wrapped` is
      specified. Must not be set if `kms_wrapped` is set.
    kmsWrapped: KMS wrapped key. Must not be set if `crypto_key` is set.
  """

  cryptoKey = _messages.BytesField(1)
  kmsWrapped = _messages.MessageField('KmsWrappedCryptoKey', 2)


class CryptoHashField(_messages.Message):
  r"""Replace field value with a hash of that value. Supported
  [types](https://www.hl7.org/fhir/datatypes.html): Code, Decimal, HumanName,
  Id, LanguageCode, Markdown, Oid, String, Uri, Uuid, Xhtml
  """



class Dataset(_messages.Message):
  r"""A message representing a health dataset. A health dataset represents a
  collection of healthcare data pertaining to one or more patients. This may
  include multiple modalities of healthcare data, such as electronic medical
  records or medical imaging data.

  Fields:
    name: Resource name of the dataset, of the form
      `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}`.
    timeZone: The default timezone used by this dataset. Must be a either a
      valid IANA time zone name such as "America/New_York" or empty, which
      defaults to UTC. This is used for parsing times in resources, such as
      HL7 messages, where no explicit timezone is specified.
  """

  name = _messages.StringField(1)
  timeZone = _messages.StringField(2)


class DateShiftConfig(_messages.Message):
  r"""Shift a date forward or backward in time by a random amount which is
  consistent for a given patient and crypto key combination.

  Fields:
    cryptoKey: An AES 128/192/256 bit key. The date shift is computed based on
      this key and the patient ID. If the patient ID is empty for a DICOM
      resource, the date shift is computed based on this key and the study
      instance UID. If `crypto_key` is not set, then `kms_wrapped` is used to
      calculate the date shift. If neither is set, a default key is generated
      for each de-identify operation. Must not be set if `kms_wrapped` is set.
    kmsWrapped: KMS wrapped key. If `kms_wrapped` is not set, then
      `crypto_key` is used to calculate the date shift. If neither is set, a
      default key is generated for each de-identify operation. Must not be set
      if `crypto_key` is set.
  """

  cryptoKey = _messages.BytesField(1)
  kmsWrapped = _messages.MessageField('KmsWrappedCryptoKey', 2)


class DateShiftField(_messages.Message):
  r"""Shift the date by a randomized number of days. See [date
  shifting](https://cloud.google.com/dlp/docs/concepts-date-shifting) for more
  information. Supported [types](https://www.hl7.org/fhir/datatypes.html):
  Date, DateTime
  """



class DeidentifiedStoreDestination(_messages.Message):
  r"""Contains configuration for streaming de-identified FHIR export.

  Fields:
    config: The configuration to use when de-identifying resources that are
      added to this store.
    store: The full resource name of a Cloud Healthcare FHIR store, for
      example, `projects/{project_id}/locations/{location_id}/datasets/{datase
      t_id}/fhirStores/{fhir_store_id}`.
  """

  config = _messages.MessageField('DeidentifyConfig', 1)
  store = _messages.StringField(2)


class DeidentifyConfig(_messages.Message):
  r"""Configures de-id options specific to different types of content. Each
  submessage customizes the handling of an https://tools.ietf.org/html/rfc6838
  media type or subtype. Configs are applied in a nested manner at runtime.

  Fields:
    annotation: Configures how annotations, meaning that the location and
      infoType of sensitive information findings, are created during de-
      identification. If unspecified, no annotations are created.
    dicom: Configures de-id of application/DICOM content. Deprecated. Use
      `dicom_tag_config` instead.
    dicomTagConfig: Configures de-id of application/DICOM content.
    fhir: Configures de-id of application/FHIR content. Deprecated. Use
      `fhir_field_config` instead.
    fhirFieldConfig: Configures de-id of application/FHIR content.
    image: Configures the de-identification of image pixels in the
      source_dataset. Deprecated. Use `dicom_tag_config.options.clean_image`
      instead.
    operationMetadata: Details about the work the de-identify operation
      performed.
    text: Configures de-identification of text wherever it is found in the
      source_dataset.
    useRegionalDataProcessing: Ensures in-flight data remains in the region of
      origin during de-identification. Using this option results in a
      significant reduction of throughput, and is not compatible with
      `LOCATION` or `ORGANIZATION_NAME` infoTypes. If the deprecated
      `DicomConfig` or `FhirConfig` are used, then `LOCATION` must be excluded
      within `TextConfig`, and must also be excluded within `ImageConfig` if
      image redaction is required.
  """

  annotation = _messages.MessageField('AnnotationConfig', 1)
  dicom = _messages.MessageField('DicomConfig', 2)
  dicomTagConfig = _messages.MessageField('DicomTagConfig', 3)
  fhir = _messages.MessageField('FhirConfig', 4)
  fhirFieldConfig = _messages.MessageField('FhirFieldConfig', 5)
  image = _messages.MessageField('ImageConfig', 6)
  operationMetadata = _messages.MessageField('DeidentifyOperationMetadata', 7)
  text = _messages.MessageField('TextConfig', 8)
  useRegionalDataProcessing = _messages.BooleanField(9)


class DeidentifyDatasetRequest(_messages.Message):
  r"""Redacts identifying information from the specified dataset.

  Fields:
    config: Deidentify configuration. Only one of `config` and
      `gcs_config_uri` can be specified.
    destinationDataset: The name of the dataset resource to create and write
      the redacted data to. * The destination dataset must not exist. * The
      destination dataset must be in the same location as the source dataset.
      De-identifying data across multiple locations is not supported.
    gcsConfigUri: Cloud Storage location to read the JSON
      cloud.healthcare.deidentify.DeidentifyConfig from, overriding the
      default config. Must be of the form `gs://{bucket_id}/path/to/object`.
      The Cloud Storage location must grant the Cloud IAM role
      `roles/storage.objectViewer` to the project's Cloud Healthcare Service
      Agent service account. Only one of `config` and `gcs_config_uri` can be
      specified.
  """

  config = _messages.MessageField('DeidentifyConfig', 1)
  destinationDataset = _messages.StringField(2)
  gcsConfigUri = _messages.StringField(3)


class DeidentifyDicomStoreRequest(_messages.Message):
  r"""Creates a new DICOM store with sensitive information de-identified.

  Fields:
    config: Deidentify configuration. Only one of `config` and
      `gcs_config_uri` can be specified.
    destinationStore: The name of the DICOM store to create and write the
      redacted data to. For example, `projects/{project_id}/locations/{locatio
      n_id}/datasets/{dataset_id}/dicomStores/{dicom_store_id}`. * The
      destination dataset must exist. * The source dataset and destination
      dataset must both reside in the same location. De-identifying data
      across multiple locations is not supported. * The destination DICOM
      store must not exist. * The caller must have the necessary permissions
      to create the destination DICOM store.
    filterConfig: Filter configuration.
    gcsConfigUri: Cloud Storage location to read the JSON
      cloud.healthcare.deidentify.DeidentifyConfig from, overriding the
      default config. Must be of the form `gs://{bucket_id}/path/to/object`.
      The Cloud Storage location must grant the Cloud IAM role
      `roles/storage.objectViewer` to the project's Cloud Healthcare Service
      Agent service account. Only one of `config` and `gcs_config_uri` can be
      specified.
  """

  config = _messages.MessageField('DeidentifyConfig', 1)
  destinationStore = _messages.StringField(2)
  filterConfig = _messages.MessageField('DicomFilterConfig', 3)
  gcsConfigUri = _messages.StringField(4)


class DeidentifyFhirStoreRequest(_messages.Message):
  r"""Creates a new FHIR store with sensitive information de-identified.

  Fields:
    config: Deidentify configuration. Only one of `config` and
      `gcs_config_uri` can be specified.
    destinationStore: The name of the FHIR store to create and write the
      redacted data to. For example, `projects/{project_id}/locations/{locatio
      n_id}/datasets/{dataset_id}/fhirStores/{fhir_store_id}`. * The
      destination dataset must exist. * The source dataset and destination
      dataset must both reside in the same location. De-identifying data
      across multiple locations is not supported. * The destination FHIR store
      must exist. * The caller must have the healthcare.fhirResources.update
      permission to write to the destination FHIR store.
    gcsConfigUri: Cloud Storage location to read the JSON
      cloud.healthcare.deidentify.DeidentifyConfig from, overriding the
      default config. Must be of the form `gs://{bucket_id}/path/to/object`.
      The Cloud Storage location must grant the Cloud IAM role
      `roles/storage.objectViewer` to the project's Cloud Healthcare Service
      Agent service account. Only one of `config` and `gcs_config_uri` can be
      specified.
    resourceFilter: A filter specifying the resources to include in the
      output. If not specified, all resources are included in the output.
    skipModifiedResources: If true, skips resources that are created or
      modified after the de-identify operation is created.
  """

  config = _messages.MessageField('DeidentifyConfig', 1)
  destinationStore = _messages.StringField(2)
  gcsConfigUri = _messages.StringField(3)
  resourceFilter = _messages.MessageField('FhirFilter', 4)
  skipModifiedResources = _messages.BooleanField(5)


class DeidentifyOperationMetadata(_messages.Message):
  r"""Details about the work the de-identify operation performed.

  Fields:
    fhirOutput: Details about the FHIR store to write the output to.
  """

  fhirOutput = _messages.MessageField('FhirOutput', 1)


class DeidentifySummary(_messages.Message):
  r"""Contains a detailed summary of the Deidentify operation."""


class DeleteTag(_messages.Message):
  r"""Delete tag."""


class Detail(_messages.Message):
  r"""Contains multiple sensitive information findings for each resource
  slice.

  Fields:
    findings: A Finding attribute.
  """

  findings = _messages.MessageField('Finding', 1, repeated=True)


class DicomConfig(_messages.Message):
  r"""Specifies the parameters needed for de-identification of DICOM stores.

  Enums:
    FilterProfileValueValuesEnum: Tag filtering profile that determines which
      tags to keep/remove.

  Fields:
    filterProfile: Tag filtering profile that determines which tags to
      keep/remove.
    keepList: List of tags to keep. Remove all other tags.
    removeList: List of tags to remove. Keep all other tags.
    skipIdRedaction: If true, skip replacing StudyInstanceUID,
      SeriesInstanceUID, SOPInstanceUID, and MediaStorageSOPInstanceUID and
      leave them untouched. The Cloud Healthcare API regenerates these UIDs by
      default based on the DICOM Standard's reasoning: "Whilst these UIDs
      cannot be mapped directly to an individual out of context, given access
      to the original images, or to a database of the original images
      containing the UIDs, it would be possible to recover the individual's
      identity." http://dicom.nema.org/medical/dicom/current/output/chtml/part
      15/sect_E.3.9.html
  """

  class FilterProfileValueValuesEnum(_messages.Enum):
    r"""Tag filtering profile that determines which tags to keep/remove.

    Values:
      TAG_FILTER_PROFILE_UNSPECIFIED: No tag filtration profile provided. Same
        as KEEP_ALL_PROFILE.
      MINIMAL_KEEP_LIST_PROFILE: Keep only the tags required to produce valid
        DICOM objects.
      ATTRIBUTE_CONFIDENTIALITY_BASIC_PROFILE: Remove tags based on DICOM
        Standard's Attribute Confidentiality Basic Profile (DICOM Standard
        Edition 2018e) http://dicom.nema.org/medical/dicom/2018e/output/chtml/
        part15/chapter_E.html.
      KEEP_ALL_PROFILE: Keep all tags.
      DEIDENTIFY_TAG_CONTENTS: Inspect within tag contents and replace
        sensitive text. The process can be configured using the TextConfig.
        Applies to all tags with the following Value Representation names: AE,
        LO, LT, PN, SH, ST, UC, UT, DA, DT, AS
    """
    TAG_FILTER_PROFILE_UNSPECIFIED = 0
    MINIMAL_KEEP_LIST_PROFILE = 1
    ATTRIBUTE_CONFIDENTIALITY_BASIC_PROFILE = 2
    KEEP_ALL_PROFILE = 3
    DEIDENTIFY_TAG_CONTENTS = 4

  filterProfile = _messages.EnumField('FilterProfileValueValuesEnum', 1)
  keepList = _messages.MessageField('TagFilterList', 2)
  removeList = _messages.MessageField('TagFilterList', 3)
  skipIdRedaction = _messages.BooleanField(4)


class DicomFilterConfig(_messages.Message):
  r"""Specifies the filter configuration for DICOM resources.

  Fields:
    resourcePathsGcsUri: The Cloud Storage location of the filter
      configuration file. The `gcs_uri` must be in the format
      `gs://bucket/path/to/object`. The filter configuration file must contain
      a list of resource paths separated by newline characters (\n or \r\n).
      Each resource path must be in the format
      "/studies/{studyUID}[/series/{seriesUID}[/instances/{instanceUID}]]" The
      Cloud Healthcare API service account must have the
      `roles/storage.objectViewer` Cloud IAM role for this Cloud Storage
      location.
  """

  resourcePathsGcsUri = _messages.StringField(1)


class DicomStore(_messages.Message):
  r"""Represents a DICOM store.

  Messages:
    LabelsValue: User-supplied key-value pairs used to organize DICOM stores.
      Label keys must be between 1 and 63 characters long, have a UTF-8
      encoding of maximum 128 bytes, and must conform to the following PCRE
      regular expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must
      be between 1 and 63 characters long, have a UTF-8 encoding of maximum
      128 bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.

  Fields:
    labels: User-supplied key-value pairs used to organize DICOM stores. Label
      keys must be between 1 and 63 characters long, have a UTF-8 encoding of
      maximum 128 bytes, and must conform to the following PCRE regular
      expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must be
      between 1 and 63 characters long, have a UTF-8 encoding of maximum 128
      bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.
    name: Resource name of the DICOM store, of the form `projects/{project_id}
      /locations/{location_id}/datasets/{dataset_id}/dicomStores/{dicom_store_
      id}`.
    notificationConfig: Notification destination for new DICOM instances.
      Supplied by the client.
    streamConfigs: Optional. A list of streaming configs used to configure the
      destination of streaming exports for every DICOM instance insertion in
      this DICOM store. After a new config is added to `stream_configs`, DICOM
      instance insertions are streamed to the new destination. When a config
      is removed from `stream_configs`, the server stops streaming to that
      destination. Each config must contain a unique destination.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-supplied key-value pairs used to organize DICOM stores. Label
    keys must be between 1 and 63 characters long, have a UTF-8 encoding of
    maximum 128 bytes, and must conform to the following PCRE regular
    expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must be between
    1 and 63 characters long, have a UTF-8 encoding of maximum 128 bytes, and
    must conform to the following PCRE regular expression:
    [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated with
    a given store.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  name = _messages.StringField(2)
  notificationConfig = _messages.MessageField('NotificationConfig', 3)
  streamConfigs = _messages.MessageField('GoogleCloudHealthcareV1beta1DicomStreamConfig', 4, repeated=True)


class DicomTagConfig(_messages.Message):
  r"""Specifies the parameters needed for the de-identification of DICOM
  stores.

  Enums:
    ProfileTypeValueValuesEnum: Base profile type for handling DICOM tags.

  Fields:
    actions: Specifies custom tag selections and `Actions` to apply to them.
      Overrides `options` and `profile`. Conflicting `Actions` are applied in
      the order given.
    options: Specifies additional options to apply, overriding the base
      `profile`.
    profileType: Base profile type for handling DICOM tags.
  """

  class ProfileTypeValueValuesEnum(_messages.Enum):
    r"""Base profile type for handling DICOM tags.

    Values:
      PROFILE_TYPE_UNSPECIFIED: No profile provided. Same as
        `ATTRIBUTE_CONFIDENTIALITY_BASIC_PROFILE`.
      MINIMAL_KEEP_LIST_PROFILE: Keep only the tags required to produce valid
        DICOM objects.
      ATTRIBUTE_CONFIDENTIALITY_BASIC_PROFILE: Remove tags based on DICOM
        Standard's [Attribute Confidentiality Basic Profile (DICOM Standard
        Edition 2018e)](http://dicom.nema.org/medical/dicom/2018e/output/chtml
        /part15/chapter_E.html).
      KEEP_ALL_PROFILE: Keep all tags.
      DEIDENTIFY_TAG_CONTENTS: Inspect tag contents and replace sensitive
        text. The process can be configured using the TextConfig. Applies to
        all tags with the following [Value Representations] (http://dicom.nema
        .org/medical/dicom/2018e/output/chtml/part05/sect_6.2.html#table_6.2-
        1): AE, LO, LT, PN, SH, ST, UC, UT, DA, DT, AS
    """
    PROFILE_TYPE_UNSPECIFIED = 0
    MINIMAL_KEEP_LIST_PROFILE = 1
    ATTRIBUTE_CONFIDENTIALITY_BASIC_PROFILE = 2
    KEEP_ALL_PROFILE = 3
    DEIDENTIFY_TAG_CONTENTS = 4

  actions = _messages.MessageField('Action', 1, repeated=True)
  options = _messages.MessageField('Options', 2)
  profileType = _messages.EnumField('ProfileTypeValueValuesEnum', 3)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Entity(_messages.Message):
  r"""The candidate entities that an entity mention could link to.

  Fields:
    entityId: entity_id is a first class field entity_id uniquely identifies
      this concept and its meta-vocabulary. For example, "UMLS/********".
    preferredTerm: preferred_term is the preferred term for this concept. For
      example, "Acetaminophen". For ad hoc entities formed by normalization,
      this is the most popular unnormalized string.
    vocabularyCodes: Vocabulary codes are first-class fields and
      differentiated from the concept unique identifier (entity_id).
      vocabulary_codes contains the representation of this concept in
      particular vocabularies, such as ICD-10, SNOMED-CT and RxNORM. These are
      prefixed by the name of the vocabulary, followed by the unique code
      within that vocabulary. For example, "RXNORM/A10334543".
  """

  entityId = _messages.StringField(1)
  preferredTerm = _messages.StringField(2)
  vocabularyCodes = _messages.StringField(3, repeated=True)


class EntityMention(_messages.Message):
  r"""An entity mention in the document.

  Fields:
    additionalInfo: Additional information about the entity mention. For
      example, for an entity mention of type `DATE` this can be its more
      specific date types from the following list: `ADMISSION_DATE`,
      `CONSULTATION_DATE`, `DISCHARGE_DATE`, `SERVICE_DATE`, `VISIT_DATE`,
      `DIAGNOSIS_DATE`, `MED_STARTED_DATE`, `MED_ENDED_DATE`, `NOTE_DATE`,
      `PROCEDURE_DATE`, `RADIATION_STARTED_DATE`, `RADIATION_ENDED_DATE`,
      `STAGE_DATE`
    certaintyAssessment: The certainty assessment of the entity mention. Its
      value is one of: LIKELY, SOMEWHAT_LIKELY, UNCERTAIN, SOMEWHAT_UNLIKELY,
      UNLIKELY, CONDITIONAL
    confidence: The model's confidence in this entity mention annotation. A
      number between 0 and 1.
    linkedEntities: linked_entities are candidate ontological concepts that
      this entity mention may refer to. They are sorted by decreasing
      confidence.
    mentionId: mention_id uniquely identifies each entity mention in a single
      response.
    subject: The subject this entity mention relates to. Its value is one of:
      PATIENT, FAMILY_MEMBER, OTHER
    temporalAssessment: How this entity mention relates to the subject
      temporally. Its value is one of: CURRENT, CLINICAL_HISTORY,
      FAMILY_HISTORY, UPCOMING, ALLERGY
    text: text is the location of the entity mention in the document.
    type: The semantic type of the entity: UNKNOWN_ENTITY_TYPE, ALONE,
      ANATOMICAL_STRUCTURE, ASSISTED_LIVING, BF_RESULT, BM_RESULT, BM_UNIT,
      BM_VALUE, BODY_FUNCTION, BODY_MEASUREMENT, COMPLIANT, DOESNOT_FOLLOWUP,
      FAMILY, FOLLOWSUP, LABORATORY_DATA, LAB_RESULT, LAB_UNIT, LAB_VALUE,
      MEDICAL_DEVICE, MEDICINE, MED_DOSE, MED_DURATION, MED_FORM,
      MED_FREQUENCY, MED_ROUTE, MED_STATUS, MED_STRENGTH, MED_TOTALDOSE,
      MED_UNIT, NON_COMPLIANT, OTHER_LIVINGSTATUS, PROBLEM, PROCEDURE,
      PROCEDURE_RESULT, PROC_METHOD, REASON_FOR_NONCOMPLIANCE, SEVERITY,
      SUBSTANCE_ABUSE, UNCLEAR_FOLLOWUP.
  """

  additionalInfo = _messages.MessageField('Feature', 1, repeated=True)
  certaintyAssessment = _messages.MessageField('Feature', 2)
  confidence = _messages.FloatField(3)
  linkedEntities = _messages.MessageField('LinkedEntity', 4, repeated=True)
  mentionId = _messages.StringField(5)
  subject = _messages.MessageField('Feature', 6)
  temporalAssessment = _messages.MessageField('Feature', 7)
  text = _messages.MessageField('TextSpan', 8)
  type = _messages.StringField(9)


class EntityMentionRelationship(_messages.Message):
  r"""Defines directed relationship from one entity mention to another.

  Fields:
    confidence: The model's confidence in this annotation. A number between 0
      and 1.
    objectId: object_id is the id of the object entity mention.
    subjectId: subject_id is the id of the subject entity mention.
  """

  confidence = _messages.FloatField(1)
  objectId = _messages.StringField(2)
  subjectId = _messages.StringField(3)


class EvaluateAnnotationStoreRequest(_messages.Message):
  r"""Request to evaluate an Annotation store against a ground truth
  [Annotation store].

  Messages:
    EvalInfoTypeMappingValue: Optional. InfoType mapping for `eval_store`.
      Different resources can map to the same infoType. For example,
      `PERSON_NAME`, `PERSON`, `NAME`, and `HUMAN` are different. To map all
      of these into a single infoType (such as `PERSON_NAME`), specify the
      following mapping: ``` info_type_mapping["PERSON"] = "PERSON_NAME"
      info_type_mapping["NAME"] = "PERSON_NAME" info_type_mapping["HUMAN"] =
      "PERSON_NAME" ``` Unmentioned infoTypes, such as `DATE`, are treated as
      identity mapping. For example: ``` info_type_mapping["DATE"] = "DATE"
      ``` InfoTypes are case-insensitive.
    GoldenInfoTypeMappingValue: Optional. Similar to `eval_info_type_mapping`,
      infoType mapping for `golden_store`.

  Fields:
    bigqueryDestination: The BigQuery table where the server writes the
      output. BigQueryDestination requires the `roles/bigquery.dataEditor` and
      `roles/bigquery.jobUser` Cloud IAM roles.
    evalInfoTypeMapping: Optional. InfoType mapping for `eval_store`.
      Different resources can map to the same infoType. For example,
      `PERSON_NAME`, `PERSON`, `NAME`, and `HUMAN` are different. To map all
      of these into a single infoType (such as `PERSON_NAME`), specify the
      following mapping: ``` info_type_mapping["PERSON"] = "PERSON_NAME"
      info_type_mapping["NAME"] = "PERSON_NAME" info_type_mapping["HUMAN"] =
      "PERSON_NAME" ``` Unmentioned infoTypes, such as `DATE`, are treated as
      identity mapping. For example: ``` info_type_mapping["DATE"] = "DATE"
      ``` InfoTypes are case-insensitive.
    goldenInfoTypeMapping: Optional. Similar to `eval_info_type_mapping`,
      infoType mapping for `golden_store`.
    goldenStore: The Annotation store to use as ground truth, in the format of
      `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/ann
      otationStores/{annotation_store_id}`.
    infoTypeConfig: A InfoTypeConfig attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EvalInfoTypeMappingValue(_messages.Message):
    r"""Optional. InfoType mapping for `eval_store`. Different resources can
    map to the same infoType. For example, `PERSON_NAME`, `PERSON`, `NAME`,
    and `HUMAN` are different. To map all of these into a single infoType
    (such as `PERSON_NAME`), specify the following mapping: ```
    info_type_mapping["PERSON"] = "PERSON_NAME" info_type_mapping["NAME"] =
    "PERSON_NAME" info_type_mapping["HUMAN"] = "PERSON_NAME" ``` Unmentioned
    infoTypes, such as `DATE`, are treated as identity mapping. For example:
    ``` info_type_mapping["DATE"] = "DATE" ``` InfoTypes are case-insensitive.

    Messages:
      AdditionalProperty: An additional property for a
        EvalInfoTypeMappingValue object.

    Fields:
      additionalProperties: Additional properties of type
        EvalInfoTypeMappingValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EvalInfoTypeMappingValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class GoldenInfoTypeMappingValue(_messages.Message):
    r"""Optional. Similar to `eval_info_type_mapping`, infoType mapping for
    `golden_store`.

    Messages:
      AdditionalProperty: An additional property for a
        GoldenInfoTypeMappingValue object.

    Fields:
      additionalProperties: Additional properties of type
        GoldenInfoTypeMappingValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a GoldenInfoTypeMappingValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bigqueryDestination = _messages.MessageField('GoogleCloudHealthcareV1beta1AnnotationBigQueryDestination', 1)
  evalInfoTypeMapping = _messages.MessageField('EvalInfoTypeMappingValue', 2)
  goldenInfoTypeMapping = _messages.MessageField('GoldenInfoTypeMappingValue', 3)
  goldenStore = _messages.StringField(4)
  infoTypeConfig = _messages.MessageField('InfoTypeConfig', 5)


class EvaluateAnnotationStoreResponse(_messages.Message):
  r"""Response for successful Annotation store evaluation operations. This
  structure is included in the response upon operation completion.
  """



class EvaluateUserConsentsRequest(_messages.Message):
  r"""Evaluate a user's Consents for all matching User data mappings. Note:
  User data mappings are indexed asynchronously, causing slight delays between
  the time mappings are created or updated and when they are included in
  EvaluateUserConsents results.

  Enums:
    ResponseViewValueValuesEnum: Optional. The view for
      EvaluateUserConsentsResponse. If unspecified, defaults to `BASIC` and
      returns `consented` as `TRUE` or `FALSE`.

  Messages:
    RequestAttributesValue: Required. The values of request attributes
      associated with this access request.
    ResourceAttributesValue: Optional. The values of resource attributes
      associated with the resources being requested. If no values are
      specified, then all resources are queried.

  Fields:
    consentList: Optional. Specific Consents to evaluate the access request
      against. These Consents must have the same `user_id` as the User data
      mappings being evalauted, must exist in the current `consent_store`, and
      must have a `state` of either `ACTIVE` or `DRAFT`. A maximum of 100
      Consents can be provided here. If unspecified, all `ACTIVE` unexpired
      Consents in the current `consent_store` will be evaluated.
    pageSize: Optional. Limit on the number of User data mappings to return in
      a single response. If not specified, 100 is used. May not be larger than
      1000.
    pageToken: Optional. Token to retrieve the next page of results, or empty
      to get the first page.
    requestAttributes: Required. The values of request attributes associated
      with this access request.
    resourceAttributes: Optional. The values of resource attributes associated
      with the resources being requested. If no values are specified, then all
      resources are queried.
    responseView: Optional. The view for EvaluateUserConsentsResponse. If
      unspecified, defaults to `BASIC` and returns `consented` as `TRUE` or
      `FALSE`.
    userId: Required. User ID to evaluate consents for.
  """

  class ResponseViewValueValuesEnum(_messages.Enum):
    r"""Optional. The view for EvaluateUserConsentsResponse. If unspecified,
    defaults to `BASIC` and returns `consented` as `TRUE` or `FALSE`.

    Values:
      RESPONSE_VIEW_UNSPECIFIED: No response view specified. The API will
        default to the BASIC view.
      BASIC: Only the `data_id` and `consented` fields are populated in the
        response.
      FULL: All fields within the response are populated. When set to `FULL`,
        all `ACTIVE` Consents are evaluated even if a matching policy is found
        during evaluation.
    """
    RESPONSE_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RequestAttributesValue(_messages.Message):
    r"""Required. The values of request attributes associated with this access
    request.

    Messages:
      AdditionalProperty: An additional property for a RequestAttributesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        RequestAttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RequestAttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceAttributesValue(_messages.Message):
    r"""Optional. The values of resource attributes associated with the
    resources being requested. If no values are specified, then all resources
    are queried.

    Messages:
      AdditionalProperty: An additional property for a ResourceAttributesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        ResourceAttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceAttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  consentList = _messages.MessageField('ConsentList', 1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  requestAttributes = _messages.MessageField('RequestAttributesValue', 4)
  resourceAttributes = _messages.MessageField('ResourceAttributesValue', 5)
  responseView = _messages.EnumField('ResponseViewValueValuesEnum', 6)
  userId = _messages.StringField(7)


class EvaluateUserConsentsResponse(_messages.Message):
  r"""A EvaluateUserConsentsResponse object.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list. This token is valid for 72 hours
      after it is created.
    results: The consent evaluation result for each `data_id`.
  """

  nextPageToken = _messages.StringField(1)
  results = _messages.MessageField('Result', 2, repeated=True)


class ExportAnnotationsRequest(_messages.Message):
  r"""Request to export Annotations. The export operation is not atomic. If a
  failure occurs, any annotations already exported are not removed.

  Fields:
    bigqueryDestination: The BigQuery output destination, which requires two
      IAM roles: `roles/bigquery.dataEditor` and `roles/bigquery.jobUser`.
    gcsDestination: The Cloud Storage destination, which requires the
      `roles/storage.objectAdmin` Cloud IAM role.
  """

  bigqueryDestination = _messages.MessageField('GoogleCloudHealthcareV1beta1AnnotationBigQueryDestination', 1)
  gcsDestination = _messages.MessageField('GoogleCloudHealthcareV1beta1AnnotationGcsDestination', 2)


class ExportAnnotationsResponse(_messages.Message):
  r"""Response for successful annotation export operations. This structure is
  included in response upon operation completion.
  """



class ExportDicomDataRequest(_messages.Message):
  r"""Exports data from the specified DICOM store. If a given resource, such
  as a DICOM object with the same SOPInstance UID, already exists in the
  output, it is overwritten with the version in the source dataset. Exported
  DICOM data persists when the DICOM store from which it was exported is
  deleted.

  Fields:
    bigqueryDestination: The BigQuery output destination. You can only export
      to a BigQuery dataset that's in the same project as the DICOM store
      you're exporting from. The Cloud Healthcare Service Agent requires two
      IAM roles on the BigQuery location: `roles/bigquery.dataEditor` and
      `roles/bigquery.jobUser`.
    filterConfig: Specifies the filter configuration.
    gcsDestination: The Cloud Storage output destination. The Cloud Healthcare
      Service Agent requires the `roles/storage.objectAdmin` Cloud IAM roles
      on the Cloud Storage location.
  """

  bigqueryDestination = _messages.MessageField('GoogleCloudHealthcareV1beta1DicomBigQueryDestination', 1)
  filterConfig = _messages.MessageField('DicomFilterConfig', 2)
  gcsDestination = _messages.MessageField('GoogleCloudHealthcareV1beta1DicomGcsDestination', 3)


class ExportDicomDataResponse(_messages.Message):
  r"""Returns additional information in regards to a completed DICOM store
  export.
  """



class ExportMessagesRequest(_messages.Message):
  r"""Request to schedule an export.

  Fields:
    endTime: The end of the range in `send_time` (MSH.7, https://www.hl7.org/d
      ocumentcenter/public_temp_2E58C1F9-1C23-BA17-
      0C6126475344DA9D/wg/conf/HL7MSH.htm) to process. If not specified, the
      time when the export is scheduled is used. This value has to come after
      the `start_time` defined below. Only messages whose `send_time` lies in
      the range `start_time` (inclusive) to `end_time` (exclusive) are
      exported.
    filter: Restricts messages exported to those matching a filter, only
      applicable to PubsubDestination and GcsDestination. The following syntax
      is available: * A string field value can be written as text inside
      quotation marks, for example `"query text"`. The only valid relational
      operation for text fields is equality (`=`), where text is searched
      within the field, rather than having the field be equal to the text. For
      example, `"Comment = great"` returns messages with `great` in the
      comment field. * A number field value can be written as an integer, a
      decimal, or an exponential. The valid relational operators for number
      fields are the equality operator (`=`), along with the less than/greater
      than operators (`<`, `<=`, `>`, `>=`). Note that there is no inequality
      (`!=`) operator. You can prepend the `NOT` operator to an expression to
      negate it. * A date field value must be written in the `yyyy-mm-dd`
      format. Fields with date and time use the RFC3339 time format. Leading
      zeros are required for one-digit months and days. The valid relational
      operators for date fields are the equality operator (`=`) , along with
      the less than/greater than operators (`<`, `<=`, `>`, `>=`). Note that
      there is no inequality (`!=`) operator. You can prepend the `NOT`
      operator to an expression to negate it. * Multiple field query
      expressions can be combined in one query by adding `AND` or `OR`
      operators between the expressions. If a boolean operator appears within
      a quoted string, it is not treated as special, and is just another part
      of the character string to be matched. You can prepend the `NOT`
      operator to an expression to negate it. The following fields and
      functions are available for filtering: * `message_type`, from the
      MSH-9.1 field. For example, `NOT message_type = "ADT"`. * `send_date` or
      `sendDate`, the YYYY-MM-DD date the message was sent in the dataset's
      time_zone, from the MSH-7 segment. For example, `send_date <
      "2017-01-02"`. * `send_time`, the timestamp when the message was sent,
      using the RFC3339 time format for comparisons, from the MSH-7 segment.
      For example, `send_time < "2017-01-02T00:00:00-05:00"`. * `create_time`,
      the timestamp when the message was created in the HL7v2 store. Use the
      RFC3339 time format for comparisons. For example, `create_time <
      "2017-01-02T00:00:00-05:00"`. * `send_facility`, the care center that
      the message came from, from the MSH-4 segment. For example,
      `send_facility = "ABC"`. Note: The filter will be applied to every
      message in the HL7v2 store whose `send_time` lies in the range defined
      by the `start_time` and the `end_time`. Even if the filter only matches
      a small set of messages, the export operation can still take a long time
      to finish when a lot of messages are between the specified `start_time`
      and `end_time` range.
    gcsDestination: Export to a Cloud Storage destination.
    pubsubDestination: Export messages to a Pub/Sub topic.
    startTime: The start of the range in `send_time` (MSH.7, https://www.hl7.o
      rg/documentcenter/public_temp_2E58C1F9-1C23-BA17-
      0C6126475344DA9D/wg/conf/HL7MSH.htm) to process. If not specified, the
      UNIX epoch (1970-01-01T00:00:00Z) is used. This value has to come before
      the `end_time` defined below. Only messages whose `send_time` lies in
      the range `start_time` (inclusive) to `end_time` (exclusive) are
      exported.
  """

  endTime = _messages.StringField(1)
  filter = _messages.StringField(2)
  gcsDestination = _messages.MessageField('GcsDestination', 3)
  pubsubDestination = _messages.MessageField('PubsubDestination', 4)
  startTime = _messages.StringField(5)


class ExportMessagesResponse(_messages.Message):
  r"""Final response for the export operation. This structure is included in
  the response to describe the detailed outcome.
  """



class ExportResourcesRequest(_messages.Message):
  r"""Request to export resources.

  Fields:
    _since: If provided, only resources updated after this time are exported.
      The time uses the format YYYY-MM-DDThh:mm:ss.sss+zz:zz. For example,
      `2015-02-07T13:28:17.239+02:00` or `2017-01-01T00:00:00Z`. The time must
      be specified to the second and include a time zone.
    _type: String of comma-delimited FHIR resource types. If provided, only
      resources of the specified resource type(s) are exported.
    bigqueryDestination: The BigQuery output destination. The Cloud Healthcare
      Service Agent requires two IAM roles on the BigQuery location:
      `roles/bigquery.dataEditor` and `roles/bigquery.jobUser`. The output is
      one BigQuery table per resource type. Unlike when setting
      `BigQueryDestination` for `StreamConfig`, `ExportResources` does not
      create BigQuery views.
    gcsDestination: The Cloud Storage output destination. The Cloud Healthcare
      Service Agent requires the `roles/storage.objectAdmin` Cloud IAM roles
      on the Cloud Storage location. The exported outputs are organized by
      FHIR resource types. The server creates one object per resource type.
      Each object contains newline delimited JSON, and each line is a FHIR
      resource.
  """

  _since = _messages.StringField(1)
  _type = _messages.StringField(2)
  bigqueryDestination = _messages.MessageField('GoogleCloudHealthcareV1beta1FhirBigQueryDestination', 3)
  gcsDestination = _messages.MessageField('GoogleCloudHealthcareV1beta1FhirGcsDestination', 4)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class Feature(_messages.Message):
  r"""A feature of an entity mention.

  Fields:
    confidence: The model's confidence in this feature annotation. A number
      between 0 and 1.
    value: The value of this feature annotation. Its range depends on the type
      of the feature.
  """

  confidence = _messages.FloatField(1)
  value = _messages.StringField(2)


class FhirConfig(_messages.Message):
  r"""Specifies how to handle de-identification of a FHIR store.

  Fields:
    defaultKeepExtensions: The behaviour for handling FHIR extensions that
      aren't otherwise specified for de-identification. If true, all
      extensions are preserved during de-identification by default. If false
      or unspecified, all extensions are removed during de-identification by
      default.
    fieldMetadataList: Specifies FHIR paths to match and how to transform
      them. Any field that is not matched by a FieldMetadata is passed through
      to the output dataset unmodified. All extensions will be processed
      according to `default_keep_extensions`. If a field can be matched by
      more than one FieldMetadata, the first FieldMetadata.Action is applied.
  """

  defaultKeepExtensions = _messages.BooleanField(1)
  fieldMetadataList = _messages.MessageField('FieldMetadata', 2, repeated=True)


class FhirFieldConfig(_messages.Message):
  r"""Specifies how to handle the de-identification of a FHIR store.

  Enums:
    ProfileTypeValueValuesEnum: Base profile type for handling FHIR fields.

  Fields:
    fieldMetadataList: Specifies FHIR paths to match and how to transform
      them. Any field that is not matched by a `FieldMetadata` is passed
      through to the output dataset unmodified. All extensions will be
      processed according to `keep_extensions`. If a field can be matched by
      more than one `FieldMetadata`, the first `FieldMetadata.Action` is
      applied. Overrides `options` and `profile`.
    options: Specifies additional options, overriding the base `profile`.
    profileType: Base profile type for handling FHIR fields.
  """

  class ProfileTypeValueValuesEnum(_messages.Enum):
    r"""Base profile type for handling FHIR fields.

    Values:
      PROFILE_TYPE_UNSPECIFIED: No profile provided. Same as `BASIC`.
      KEEP_ALL: `Keep` all fields.
      BASIC: Transforms known HIPAA 18 fields and cleans known unstructured
        text fields.
      CLEAN_ALL: Cleans all supported tags. Applies to types: Code, Date,
        DateTime, Decimal, HumanName, Id, LanguageCode, Markdown, Oid, String,
        Uri, Uuid, Xhtml
    """
    PROFILE_TYPE_UNSPECIFIED = 0
    KEEP_ALL = 1
    BASIC = 2
    CLEAN_ALL = 3

  fieldMetadataList = _messages.MessageField('GoogleCloudHealthcareV1beta1DeidentifyFieldMetadata', 1, repeated=True)
  options = _messages.MessageField('GoogleCloudHealthcareV1beta1DeidentifyOptions', 2)
  profileType = _messages.EnumField('ProfileTypeValueValuesEnum', 3)


class FhirFilter(_messages.Message):
  r"""Filter configuration.

  Fields:
    resources: List of resources to include in the output. If this list is
      empty or not specified, all resources are included in the output.
  """

  resources = _messages.MessageField('Resources', 1)


class FhirNotificationConfig(_messages.Message):
  r"""Contains the configuration for FHIR notifications.

  Fields:
    pubsubTopic: The [Pub/Sub](https://cloud.google.com/pubsub/docs/) topic
      that notifications of changes are published on. Supplied by the client.
      The notification is a `PubsubMessage` with the following fields: *
      `PubsubMessage.Data` contains the resource name. *
      `PubsubMessage.MessageId` is the ID of this notification. It is
      guaranteed to be unique within the topic. * `PubsubMessage.PublishTime`
      is the time when the message was published. Note that notifications are
      only sent if the topic is non-empty. [Topic
      names](https://cloud.google.com/pubsub/docs/overview#names) must be
      scoped to a project. The Cloud Healthcare API service account,
      <EMAIL>, must have publisher
      permissions on the given Pub/Sub topic. Not having adequate permissions
      causes the calls that send notifications to fail
      (https://cloud.google.com/healthcare-api/docs/permissions-healthcare-
      api-gcp-products#dicom_fhir_and_hl7v2_store_cloud_pubsub_permissions).
      If a notification can't be published to Pub/Sub, errors are logged to
      Cloud Logging. For more information, see [Viewing error logs in Cloud
      Logging](https://cloud.google.com/healthcare-api/docs/how-tos/logging).
    sendFullResource: Whether to send full FHIR resource to this Pub/Sub topic
      for Create and Update operation. Note that setting this to true does not
      guarantee that all resources will be sent in the format of full FHIR
      resource. When a resource change is too large or during heavy traffic,
      only the resource name will be sent. Clients should always check the
      "payloadType" label from a Pub/Sub message to determine whether it needs
      to fetch the full resource as a separate operation.
    sendPreviousResourceOnDelete: Whether to send full FHIR resource to this
      Pub/Sub topic for deleting FHIR resource. Note that setting this to true
      does not guarantee that all previous resources will be sent in the
      format of full FHIR resource. When a resource change is too large or
      during heavy traffic, only the resource name will be sent. Clients
      should always check the "payloadType" label from a Pub/Sub message to
      determine whether it needs to fetch the full previous resource as a
      separate operation.
  """

  pubsubTopic = _messages.StringField(1)
  sendFullResource = _messages.BooleanField(2)
  sendPreviousResourceOnDelete = _messages.BooleanField(3)


class FhirOutput(_messages.Message):
  r"""Details about the FHIR store to write the output to.

  Fields:
    fhirStore: Name of the output FHIR store, which must already exist. You
      must grant the healthcare.fhirResources.update permission on the
      destination store to your project's **Cloud Healthcare Service Agent**
      [service account](https://cloud.google.com/healthcare/docs/how-
      tos/permissions-healthcare-api-gcp-
      products#the_cloud_healthcare_service_agent). The destination store must
      set `enable_update_create` to true. The destination store must use FHIR
      version R4. Writing these resources will consume FHIR operations quota
      from the project containing the source data. De-identify operation
      metadata is only generated for DICOM de-identification operations.
  """

  fhirStore = _messages.StringField(1)


class FhirStore(_messages.Message):
  r"""Represents a FHIR store.

  Enums:
    ComplexDataTypeReferenceParsingValueValuesEnum: Enable parsing of
      references within complex FHIR data types such as Extensions. If this
      value is set to ENABLED, then features like referential integrity and
      Bundle reference rewriting apply to all references. If this flag has not
      been specified the behavior of the FHIR store will not change,
      references in complex data types will not be parsed. New stores will
      have this value set to ENABLED after a notification period. Warning:
      turning on this flag causes processing existing resources to fail if
      they contain references to non-existent resources.
    VersionValueValuesEnum: Immutable. The FHIR specification version that
      this FHIR store supports natively. This field is immutable after store
      creation. Requests are rejected if they contain FHIR resources of a
      different version. Version is required for every FHIR store.

  Messages:
    LabelsValue: User-supplied key-value pairs used to organize FHIR stores.
      Label keys must be between 1 and 63 characters long, have a UTF-8
      encoding of maximum 128 bytes, and must conform to the following PCRE
      regular expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must
      be between 1 and 63 characters long, have a UTF-8 encoding of maximum
      128 bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.

  Fields:
    complexDataTypeReferenceParsing: Enable parsing of references within
      complex FHIR data types such as Extensions. If this value is set to
      ENABLED, then features like referential integrity and Bundle reference
      rewriting apply to all references. If this flag has not been specified
      the behavior of the FHIR store will not change, references in complex
      data types will not be parsed. New stores will have this value set to
      ENABLED after a notification period. Warning: turning on this flag
      causes processing existing resources to fail if they contain references
      to non-existent resources.
    defaultSearchHandlingStrict: If true, overrides the default search
      behavior for this FHIR store to `handling=strict` which returns an error
      for unrecognized search parameters. If false, uses the FHIR
      specification default `handling=lenient` which ignores unrecognized
      search parameters. The handling can always be changed from the default
      on an individual API call by setting the HTTP header `Prefer:
      handling=strict` or `Prefer: handling=lenient`.
    disableReferentialIntegrity: Immutable. Whether to disable referential
      integrity in this FHIR store. This field is immutable after FHIR store
      creation. The default value is false, meaning that the API enforces
      referential integrity and fails the requests that result in inconsistent
      state in the FHIR store. When this field is set to true, the API skips
      referential integrity checks. Consequently, operations that rely on
      references, such as GetPatientEverything, do not return all the results
      if broken references exist.
    disableResourceVersioning: Immutable. Whether to disable resource
      versioning for this FHIR store. This field can not be changed after the
      creation of FHIR store. If set to false, which is the default behavior,
      all write operations cause historical versions to be recorded
      automatically. The historical versions can be fetched through the
      history APIs, but cannot be updated. If set to true, no historical
      versions are kept. The server sends errors for attempts to read the
      historical versions.
    enableUpdateCreate: Whether this FHIR store has the [updateCreate
      capability](https://www.hl7.org/fhir/capabilitystatement-
      definitions.html#CapabilityStatement.rest.resource.updateCreate). This
      determines if the client can use an Update operation to create a new
      resource with a client-specified ID. If false, all IDs are server-
      assigned through the Create operation and attempts to update a non-
      existent resource return errors. It is strongly advised not to include
      or encode any sensitive data such as patient identifiers in client-
      specified resource IDs. Those IDs are part of the FHIR resource path
      recorded in Cloud audit logs and Pub/Sub notifications. Those IDs can
      also be contained in reference fields within other resources.
    labels: User-supplied key-value pairs used to organize FHIR stores. Label
      keys must be between 1 and 63 characters long, have a UTF-8 encoding of
      maximum 128 bytes, and must conform to the following PCRE regular
      expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must be
      between 1 and 63 characters long, have a UTF-8 encoding of maximum 128
      bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.
    name: Output only. Resource name of the FHIR store, of the form
      `projects/{project_id}/datasets/{dataset_id}/fhirStores/{fhir_store_id}`
      .
    notificationConfig: Deprecated. Use `notification_configs` instead. If
      non-empty, publish all resource modifications of this FHIR store to this
      destination. The Pub/Sub message attributes contain a map with a string
      describing the action that has triggered the notification. For example,
      "action":"CreateResource".
    notificationConfigs: Specifies where and whether to send notifications
      upon changes to a Fhir store.
    searchConfig: Configuration for how FHIR resources can be searched.
    streamConfigs: A list of streaming configs that configure the destinations
      of streaming export for every resource mutation in this FHIR store. Each
      store is allowed to have up to 10 streaming configs. After a new config
      is added, the next resource mutation is streamed to the new location in
      addition to the existing ones. When a location is removed from the list,
      the server stops streaming to that location. Before adding a new config,
      you must add the required
      [`bigquery.dataEditor`](https://cloud.google.com/bigquery/docs/access-
      control#bigquery.dataEditor) role to your project's **Cloud Healthcare
      Service Agent** [service
      account](https://cloud.google.com/iam/docs/service-accounts). Some lag
      (typically on the order of dozens of seconds) is expected before the
      results show up in the streaming destination.
    validationConfig: Configuration for how to validate incoming FHIR
      resources against configured profiles.
    version: Immutable. The FHIR specification version that this FHIR store
      supports natively. This field is immutable after store creation.
      Requests are rejected if they contain FHIR resources of a different
      version. Version is required for every FHIR store.
  """

  class ComplexDataTypeReferenceParsingValueValuesEnum(_messages.Enum):
    r"""Enable parsing of references within complex FHIR data types such as
    Extensions. If this value is set to ENABLED, then features like
    referential integrity and Bundle reference rewriting apply to all
    references. If this flag has not been specified the behavior of the FHIR
    store will not change, references in complex data types will not be
    parsed. New stores will have this value set to ENABLED after a
    notification period. Warning: turning on this flag causes processing
    existing resources to fail if they contain references to non-existent
    resources.

    Values:
      COMPLEX_DATA_TYPE_REFERENCE_PARSING_UNSPECIFIED: No parsing behavior
        specified. This is the same as DISABLED for backwards compatibility.
      DISABLED: References in complex data types are ignored.
      ENABLED: References in complex data types are parsed.
    """
    COMPLEX_DATA_TYPE_REFERENCE_PARSING_UNSPECIFIED = 0
    DISABLED = 1
    ENABLED = 2

  class VersionValueValuesEnum(_messages.Enum):
    r"""Immutable. The FHIR specification version that this FHIR store
    supports natively. This field is immutable after store creation. Requests
    are rejected if they contain FHIR resources of a different version.
    Version is required for every FHIR store.

    Values:
      VERSION_UNSPECIFIED: VERSION_UNSPECIFIED is treated as STU3 to
        accommodate the existing FHIR stores.
      DSTU2: Draft Standard for Trial Use, [Release
        2](https://www.hl7.org/fhir/DSTU2)
      STU3: Standard for Trial Use, [Release 3](https://www.hl7.org/fhir/STU3)
      R4: [Release 4](https://www.hl7.org/fhir/R4)
    """
    VERSION_UNSPECIFIED = 0
    DSTU2 = 1
    STU3 = 2
    R4 = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-supplied key-value pairs used to organize FHIR stores. Label keys
    must be between 1 and 63 characters long, have a UTF-8 encoding of maximum
    128 bytes, and must conform to the following PCRE regular expression:
    \p{Ll}\p{Lo}{0,62} Label values are optional, must be between 1 and 63
    characters long, have a UTF-8 encoding of maximum 128 bytes, and must
    conform to the following PCRE regular expression:
    [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated with
    a given store.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  complexDataTypeReferenceParsing = _messages.EnumField('ComplexDataTypeReferenceParsingValueValuesEnum', 1)
  defaultSearchHandlingStrict = _messages.BooleanField(2)
  disableReferentialIntegrity = _messages.BooleanField(3)
  disableResourceVersioning = _messages.BooleanField(4)
  enableUpdateCreate = _messages.BooleanField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  notificationConfig = _messages.MessageField('NotificationConfig', 8)
  notificationConfigs = _messages.MessageField('FhirNotificationConfig', 9, repeated=True)
  searchConfig = _messages.MessageField('SearchConfig', 10)
  streamConfigs = _messages.MessageField('StreamConfig', 11, repeated=True)
  validationConfig = _messages.MessageField('ValidationConfig', 12)
  version = _messages.EnumField('VersionValueValuesEnum', 13)


class FhirStoreMetric(_messages.Message):
  r"""Count of resources and total storage size by type for a given FHIR
  store.

  Fields:
    count: The total count of FHIR resources in the store of this resource
      type.
    resourceType: The FHIR resource type this metric applies to.
    structuredStorageSizeBytes: The total amount of structured storage used by
      FHIR resources of this resource type in the store.
  """

  count = _messages.IntegerField(1)
  resourceType = _messages.StringField(2)
  structuredStorageSizeBytes = _messages.IntegerField(3)


class FhirStoreMetrics(_messages.Message):
  r"""List of metrics for a given FHIR store.

  Fields:
    metrics: List of FhirStoreMetric by resource type.
    name: The resource name of the FHIR store to get metrics for, in the
      format `projects/{project_id}/datasets/{dataset_id}/fhirStores/{fhir_sto
      re_id}`.
  """

  metrics = _messages.MessageField('FhirStoreMetric', 1, repeated=True)
  name = _messages.StringField(2)


class Field(_messages.Message):
  r"""A (sub) field of a type.

  Fields:
    maxOccurs: The maximum number of times this field can be repeated. 0 or -1
      means unbounded.
    minOccurs: The minimum number of times this field must be
      present/repeated.
    name: The name of the field. For example, "PID-1" or just "1".
    table: The HL7v2 table this field refers to. For example, PID-15
      (Patient's Primary Language) usually refers to table "0296".
    type: The type of this field. A Type with this name must be defined in an
      Hl7TypesConfig.
  """

  maxOccurs = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minOccurs = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  name = _messages.StringField(3)
  table = _messages.StringField(4)
  type = _messages.StringField(5)


class FieldMetadata(_messages.Message):
  r"""Specifies FHIR paths to match, and how to handle de-identification of
  matching fields.

  Enums:
    ActionValueValuesEnum: Deidentify action for one field.

  Fields:
    action: Deidentify action for one field.
    paths: List of paths to FHIR fields to redact. Each path is a period-
      separated list where each component is either a field name or FHIR type
      name. All types begin with an upper case letter. For example, the
      resource field "Patient.Address.city", which uses a string type, can be
      matched by "Patient.Address.String". Path also supports partial
      matching. For example, "Patient.Address.city" can be matched by
      "Address.city" (Patient omitted). Partial matching and type matching can
      be combined. For example, "Patient.Address.city" can be matched by
      "Address.String". For "choice" types (those defined in the FHIR spec
      with the form: field[x]), use two separate components. For example,
      "deceasedAge.unit" is matched by "Deceased.Age.unit". Supported types
      are: AdministrativeGenderCode, Base64Binary, Boolean, Code, Date,
      DateTime, Decimal, HumanName, Id, Instant, Integer, LanguageCode,
      Markdown, Oid, PositiveInt, String, UnsignedInt, Uri, Uuid, Xhtml. The
      sub-type for HumanName(for example HumanName.given, HumanName.family)
      can be omitted.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Deidentify action for one field.

    Values:
      ACTION_UNSPECIFIED: No action specified.
      TRANSFORM: Transform the entire field based on transformations specified
        in TextConfig. When the specified transformation cannot be applied to
        a field, RedactConfig is used. For example, a Crypto Hash
        transformation can't be applied to a FHIR Date field.
      INSPECT_AND_TRANSFORM: Inspect and transform any found PHI. When
        `AnnotationConfig` is provided, annotations of PHI will be generated,
        except for Date and Datetime.
      DO_NOT_TRANSFORM: Do not transform.
    """
    ACTION_UNSPECIFIED = 0
    TRANSFORM = 1
    INSPECT_AND_TRANSFORM = 2
    DO_NOT_TRANSFORM = 3

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  paths = _messages.StringField(2, repeated=True)


class FilterList(_messages.Message):
  r"""List of infoTypes to be filtered.

  Fields:
    infoTypes: These infoTypes are based on after the `eval_info_type_mapping`
      and `golden_info_type_mapping`.
  """

  infoTypes = _messages.StringField(1, repeated=True)


class Finding(_messages.Message):
  r"""A Finding object.

  Fields:
    end: Zero-based ending index of the found text, exclusively.
    infoType: The type of information stored in this text range. For example,
      HumanName, BirthDate, or Address.
    quote: The snippet of the sensitive text. This field is only populated
      during deidentification if `store_quote` is set to true in
      DeidentifyConfig.
    start: Zero-based starting index of the found text, inclusively.
  """

  end = _messages.IntegerField(1)
  infoType = _messages.StringField(2)
  quote = _messages.StringField(3)
  start = _messages.IntegerField(4)


class GcsDestination(_messages.Message):
  r"""The Cloud Storage output destination. The Cloud Healthcare Service Agent
  requires the `roles/storage.objectAdmin` Cloud IAM roles on the Cloud
  Storage location.

  Enums:
    ContentStructureValueValuesEnum: The format of the exported HL7v2 message
      files.
    MessageViewValueValuesEnum: Specifies the parts of the Message resource to
      include in the export. If not specified, FULL is used.

  Fields:
    contentStructure: The format of the exported HL7v2 message files.
    messageView: Specifies the parts of the Message resource to include in the
      export. If not specified, FULL is used.
    uriPrefix: URI of an existing Cloud Storage directory where the server
      writes result files, in the format `gs://{bucket-
      id}/{path/to/destination/dir}`. If there is no trailing slash, the
      service appends one when composing the object path.
  """

  class ContentStructureValueValuesEnum(_messages.Enum):
    r"""The format of the exported HL7v2 message files.

    Values:
      CONTENT_STRUCTURE_UNSPECIFIED: If the content structure is not
        specified, the default value `MESSAGE_JSON` will be used.
      MESSAGE_JSON: Messages are printed using the JSON format returned from
        the `GetMessage` API. Messages are delimited with newlines.
    """
    CONTENT_STRUCTURE_UNSPECIFIED = 0
    MESSAGE_JSON = 1

  class MessageViewValueValuesEnum(_messages.Enum):
    r"""Specifies the parts of the Message resource to include in the export.
    If not specified, FULL is used.

    Values:
      MESSAGE_VIEW_UNSPECIFIED: Not specified, equivalent to FULL for
        getMessage, equivalent to BASIC for listMessages.
      RAW_ONLY: Server responses include all the message fields except
        parsed_data, and schematized_data fields.
      PARSED_ONLY: Server responses include all the message fields except data
        and schematized_data fields.
      FULL: Server responses include all the message fields.
      SCHEMATIZED_ONLY: Server responses include all the message fields except
        data and parsed_data fields.
      BASIC: Server responses include only the name field.
    """
    MESSAGE_VIEW_UNSPECIFIED = 0
    RAW_ONLY = 1
    PARSED_ONLY = 2
    FULL = 3
    SCHEMATIZED_ONLY = 4
    BASIC = 5

  contentStructure = _messages.EnumField('ContentStructureValueValuesEnum', 1)
  messageView = _messages.EnumField('MessageViewValueValuesEnum', 2)
  uriPrefix = _messages.StringField(3)


class GcsSource(_messages.Message):
  r"""Specifies the configuration for importing data from Cloud Storage.

  Fields:
    uri: Points to a Cloud Storage URI containing file(s) to import. The URI
      must be in the following format: `gs://{bucket_id}/{object_id}`. The URI
      can include wildcards in `object_id` and thus identify multiple files.
      Supported wildcards: * `*` to match 0 or more non-separator characters *
      `**` to match 0 or more characters (including separators). Must be used
      at the end of a path and with no other wildcards in the path. Can also
      be used with a file extension (such as .ndjson), which imports all files
      with the extension in the specified directory and its sub-directories.
      For example, `gs://my-bucket/my-directory/**.ndjson` imports all files
      with `.ndjson` extensions in `my-directory/` and its sub-directories. *
      `?` to match 1 character Files matching the wildcard are expected to
      contain content only, no metadata.
  """

  uri = _messages.StringField(1)


class GoogleCloudHealthcareV1beta1AnnotationBigQueryDestination(_messages.Message):
  r"""The BigQuery table for export.

  Enums:
    SchemaTypeValueValuesEnum: Specifies the schema format to export.
    WriteDispositionValueValuesEnum: Determines if existing data in the
      destination dataset is overwritten, appended to, or not written if the
      tables contain data. If a write_disposition is specified, the `force`
      parameter is ignored.

  Fields:
    force: Use `write_disposition` instead. If `write_disposition` is
      specified, this parameter is ignored. force=false is equivalent to
      write_disposition=WRITE_EMPTY and force=true is equivalent to
      write_disposition=WRITE_TRUNCATE.
    schemaType: Specifies the schema format to export.
    tableUri: BigQuery URI to a table, up to 2000 characters long, must be of
      the form bq://projectId.bqDatasetId.tableId.
    writeDisposition: Determines if existing data in the destination dataset
      is overwritten, appended to, or not written if the tables contain data.
      If a write_disposition is specified, the `force` parameter is ignored.
  """

  class SchemaTypeValueValuesEnum(_messages.Enum):
    r"""Specifies the schema format to export.

    Values:
      SCHEMA_TYPE_UNSPECIFIED: Same as SIMPLE.
      SIMPLE: A flatterned version of Annotation.
    """
    SCHEMA_TYPE_UNSPECIFIED = 0
    SIMPLE = 1

  class WriteDispositionValueValuesEnum(_messages.Enum):
    r"""Determines if existing data in the destination dataset is overwritten,
    appended to, or not written if the tables contain data. If a
    write_disposition is specified, the `force` parameter is ignored.

    Values:
      WRITE_DISPOSITION_UNSPECIFIED: Default behavior is the same as
        WRITE_EMPTY.
      WRITE_EMPTY: Only export data if the destination table is empty.
      WRITE_TRUNCATE: Erase all existing data in a table before writing the
        instances.
      WRITE_APPEND: Append data to the existing table.
    """
    WRITE_DISPOSITION_UNSPECIFIED = 0
    WRITE_EMPTY = 1
    WRITE_TRUNCATE = 2
    WRITE_APPEND = 3

  force = _messages.BooleanField(1)
  schemaType = _messages.EnumField('SchemaTypeValueValuesEnum', 2)
  tableUri = _messages.StringField(3)
  writeDisposition = _messages.EnumField('WriteDispositionValueValuesEnum', 4)


class GoogleCloudHealthcareV1beta1AnnotationGcsDestination(_messages.Message):
  r"""The Cloud Storage location for export.

  Fields:
    uriPrefix: The Cloud Storage destination to export to. URI for a Cloud
      Storage directory where the server writes result files, in the format
      `gs://{bucket-id}/{path/to/destination/dir}`. If there is no trailing
      slash, the service appends one when composing the object path. The user
      is responsible for creating the Cloud Storage bucket referenced in
      `uri_prefix`.
  """

  uriPrefix = _messages.StringField(1)


class GoogleCloudHealthcareV1beta1AnnotationGcsSource(_messages.Message):
  r"""Specifies the configuration for importing data from Cloud Storage.

  Fields:
    uri: Points to a Cloud Storage URI containing file(s) with content only.
      The URI must be in the following format: `gs://{bucket_id}/{object_id}`.
      The URI can include wildcards in `object_id` and thus identify multiple
      files. Supported wildcards: '*' to match 0 or more non-separator
      characters '**' to match 0 or more characters (including separators).
      Must be used at the end of a path and with no other wildcards in the
      path. Can also be used with a file extension (such as .dcm), which
      imports all files with the extension in the specified directory and its
      sub-directories. For example, `gs://my-bucket/my-directory/**.json`
      imports all files with .json extensions in `my-directory/` and its sub-
      directories. '?' to match 1 character All other URI formats are invalid.
      Files matching the wildcard are expected to contain content only, no
      metadata.
  """

  uri = _messages.StringField(1)


class GoogleCloudHealthcareV1beta1ConsentGcsDestination(_messages.Message):
  r"""The Cloud Storage location for export.

  Fields:
    uriPrefix: URI for a Cloud Storage directory where the server writes
      result files, in the format `gs://{bucket-
      id}/{path/to/destination/dir}`. If there is no trailing slash, the
      service appends one when composing the object path. The user is
      responsible for creating the Cloud Storage bucket and directory
      referenced in `uri_prefix`.
  """

  uriPrefix = _messages.StringField(1)


class GoogleCloudHealthcareV1beta1ConsentPolicy(_messages.Message):
  r"""Represents a user's consent in terms of the resources that can be
  accessed and under what conditions.

  Fields:
    authorizationRule: Required. The request conditions to meet to grant
      access. In addition to any supported comparison operators, authorization
      rules may have `IN` operator as well as at most 10 logical operators
      that are limited to `AND` (`&&`), `OR` (`||`).
    resourceAttributes: The resources that this policy applies to. A resource
      is a match if it matches all the attributes listed here. If empty, this
      policy applies to all User data mappings for the given user.
  """

  authorizationRule = _messages.MessageField('Expr', 1)
  resourceAttributes = _messages.MessageField('Attribute', 2, repeated=True)


class GoogleCloudHealthcareV1beta1DeidentifyDeidentifyDicomStoreSummary(_messages.Message):
  r"""Contains a summary of the DeidentifyDicomStore operation."""


class GoogleCloudHealthcareV1beta1DeidentifyDeidentifyFhirStoreSummary(_messages.Message):
  r"""Contains a summary of the DeidentifyFhirStore operation."""


class GoogleCloudHealthcareV1beta1DeidentifyFieldMetadata(_messages.Message):
  r"""Specifies the FHIR paths to match and how to handle the de-
  identification of matching fields.

  Fields:
    characterMaskField: Replace the field's value with a masking character.
      Supported [types](https://www.hl7.org/fhir/datatypes.html): Code,
      Decimal, HumanName, Id, LanguageCode, Markdown, Oid, String, Uri, Uuid,
      Xhtml
    cleanTextField: Inspect the field's text and transform sensitive text.
      Configure using `TextConfig`. Supported
      [types](https://www.hl7.org/fhir/datatypes.html): Code, Date, DateTime,
      Decimal, HumanName, Id, LanguageCode, Markdown, Oid, String, Uri, Uuid,
      Xhtml
    cryptoHashField: Replace field value with a hash of that value. Supported
      [types](https://www.hl7.org/fhir/datatypes.html): Code, Decimal,
      HumanName, Id, LanguageCode, Markdown, Oid, String, Uri, Uuid, Xhtml
    dateShiftField: Shift the date by a randomized number of days. See [date
      shifting](https://cloud.google.com/dlp/docs/concepts-date-shifting) for
      more information. Supported
      [types](https://www.hl7.org/fhir/datatypes.html): Date, DateTime
    keepField: Keep the field unchanged.
    paths: List of paths to FHIR fields to redact. Each path is a period-
      separated list where each component is either a field name or FHIR type
      name. All types begin with an upper case letter. For example, the
      resource field "Patient.Address.city", which uses a string type, can be
      matched by "Patient.Address.String". Path also supports partialkk
      matching. For example, "Patient.Address.city" can be matched by
      "Address.city" (Patient omitted). Partial matching and type matching can
      be combined, for example "Patient.Address.city" can be matched by
      "Address.String". For "choice" types (those defined in the FHIR spec
      with the form: field[x]), use two separate components. For example,
      "deceasedAge.unit" is matched by "Deceased.Age.unit". Supported
      [types](https://www.hl7.org/fhir/datatypes.html) are:
      AdministrativeGenderCode, Base64Binary, Boolean, Code, Date, DateTime,
      Decimal, HumanName, Id, Instant, Integer, LanguageCode, Markdown, Oid,
      PositiveInt, String, UnsignedInt, Uri, Uuid, Xhtml. The sub-type for
      HumanName (for example HumanName.given, HumanName.family) can be
      omitted.
    removeField: Remove the field.
  """

  characterMaskField = _messages.MessageField('CharacterMaskField', 1)
  cleanTextField = _messages.MessageField('CleanTextField', 2)
  cryptoHashField = _messages.MessageField('CryptoHashField', 3)
  dateShiftField = _messages.MessageField('DateShiftField', 4)
  keepField = _messages.MessageField('KeepField', 5)
  paths = _messages.StringField(6, repeated=True)
  removeField = _messages.MessageField('RemoveField', 7)


class GoogleCloudHealthcareV1beta1DeidentifyOptions(_messages.Message):
  r"""Specifies additional options to apply to the base `profile`.

  Fields:
    characterMaskConfig: Character mask config for `CharacterMaskField`
      `FieldMetadatas`.
    contextualDeid: Configure contextual de-id.
    cryptoHashConfig: Crypo hash config for `CharacterMaskField`
      `FieldMetadatas`.
    dateShiftConfig: Date shifting config for `CharacterMaskField`
      `FieldMetadatas`.
    keepExtensions: Configure keeping extensions by default.
  """

  characterMaskConfig = _messages.MessageField('CharacterMaskConfig', 1)
  contextualDeid = _messages.MessageField('ContextualDeidConfig', 2)
  cryptoHashConfig = _messages.MessageField('CryptoHashConfig', 3)
  dateShiftConfig = _messages.MessageField('DateShiftConfig', 4)
  keepExtensions = _messages.MessageField('KeepExtensionsConfig', 5)


class GoogleCloudHealthcareV1beta1DicomBigQueryDestination(_messages.Message):
  r"""The BigQuery table where the server writes output.

  Enums:
    WriteDispositionValueValuesEnum: Determines whether the existing table in
      the destination is to be overwritten or appended to. If a
      write_disposition is specified, the `force` parameter is ignored.

  Fields:
    force: Use `write_disposition` instead. If `write_disposition` is
      specified, this parameter is ignored. force=false is equivalent to
      write_disposition=WRITE_EMPTY and force=true is equivalent to
      write_disposition=WRITE_TRUNCATE.
    tableUri: BigQuery URI to a table, up to 2000 characters long, in the
      format `bq://projectId.bqDatasetId.tableId`
    writeDisposition: Determines whether the existing table in the destination
      is to be overwritten or appended to. If a write_disposition is
      specified, the `force` parameter is ignored.
  """

  class WriteDispositionValueValuesEnum(_messages.Enum):
    r"""Determines whether the existing table in the destination is to be
    overwritten or appended to. If a write_disposition is specified, the
    `force` parameter is ignored.

    Values:
      WRITE_DISPOSITION_UNSPECIFIED: Default behavior is the same as
        WRITE_EMPTY.
      WRITE_EMPTY: Only export data if the destination table is empty.
      WRITE_TRUNCATE: Erase all existing data in the destination table before
        writing the instances.
      WRITE_APPEND: Append data to the destination table.
    """
    WRITE_DISPOSITION_UNSPECIFIED = 0
    WRITE_EMPTY = 1
    WRITE_TRUNCATE = 2
    WRITE_APPEND = 3

  force = _messages.BooleanField(1)
  tableUri = _messages.StringField(2)
  writeDisposition = _messages.EnumField('WriteDispositionValueValuesEnum', 3)


class GoogleCloudHealthcareV1beta1DicomGcsDestination(_messages.Message):
  r"""The Cloud Storage location where the server writes the output and the
  export configuration.

  Fields:
    mimeType: MIME types supported by DICOM spec. Each file is written in the
      following format:
      `.../{study_id}/{series_id}/{instance_id}[/{frame_number}].{extension}`
      The frame_number component exists only for multi-frame instances.
      Supported MIME types are consistent with supported formats in DICOMweb:
      https://cloud.google.com/healthcare/docs/dicom#retrieve_transaction.
      Specifically, the following are supported: - application/dicom;
      transfer-syntax=1.2.840.10008.1.2.1 (uncompressed DICOM) -
      application/dicom; transfer-syntax=1.2.840.10008.******** (DICOM with
      embedded JPEG Baseline) - application/dicom; transfer-
      syntax=1.2.840.10008.1.2.4.90 (DICOM with embedded JPEG 2000 Lossless
      Only) - application/dicom; transfer-syntax=1.2.840.10008.1.2.4.91 (DICOM
      with embedded JPEG 2000)h - application/dicom; transfer-syntax=* (DICOM
      with no transcoding) - application/octet-stream; transfer-
      syntax=1.2.840.10008.1.2.1 (raw uncompressed PixelData) -
      application/octet-stream; transfer-syntax=* (raw PixelData in whatever
      format it was uploaded in) - image/jpeg; transfer-
      syntax=1.2.840.10008.******** (Consumer JPEG) - image/png The following
      extensions are used for output files: - application/dicom -> .dcm -
      image/jpeg -> .jpg - image/png -> .png - application/octet-stream -> no
      extension If unspecified, the instances are exported in the original
      DICOM format they were uploaded in.
    uriPrefix: The Cloud Storage destination to export to. URI for a Cloud
      Storage directory where the server writes the result files, in the
      format `gs://{bucket-id}/{path/to/destination/dir}`). If there is no
      trailing slash, the service appends one when composing the object path.
      The user is responsible for creating the Cloud Storage bucket referenced
      in `uri_prefix`.
  """

  mimeType = _messages.StringField(1)
  uriPrefix = _messages.StringField(2)


class GoogleCloudHealthcareV1beta1DicomGcsSource(_messages.Message):
  r"""Specifies the configuration for importing data from Cloud Storage.

  Fields:
    uri: Points to a Cloud Storage URI containing file(s) with content only.
      The URI must be in the following format: `gs://{bucket_id}/{object_id}`.
      The URI can include wildcards in `object_id` and thus identify multiple
      files. Supported wildcards: * '*' to match 0 or more non-separator
      characters * '**' to match 0 or more characters (including separators).
      Must be used at the end of a path and with no other wildcards in the
      path. Can also be used with a file extension (such as .dcm), which
      imports all files with the extension in the specified directory and its
      sub-directories. For example, `gs://my-bucket/my-directory/**.dcm`
      imports all files with .dcm extensions in `my-directory/` and its sub-
      directories. * '?' to match 1 character. All other URI formats are
      invalid. Files matching the wildcard are expected to contain content
      only, no metadata.
  """

  uri = _messages.StringField(1)


class GoogleCloudHealthcareV1beta1DicomStreamConfig(_messages.Message):
  r"""StreamConfig specifies configuration for a streaming DICOM export.

  Fields:
    bigqueryDestination: Results are appended to this table. The server
      creates a new table in the given BigQuery dataset if the specified table
      does not exist. To enable the Cloud Healthcare API to write to your
      BigQuery table, you must give the Cloud Healthcare API service account
      the bigquery.dataEditor role. The service account is:
      `service-{PROJECT_NUMBER}@gcp-sa-healthcare.iam.gserviceaccount.com`.
      The PROJECT_NUMBER identifies the project that the DICOM store resides
      in. To get the project number, go to the Cloud Console Dashboard. It is
      recommended to not have a custom schema in the destination table which
      could conflict with the schema created by the Cloud Healthcare API.
      Instance deletions are not applied to the destination table. The
      destination's table schema will be automatically updated in case a new
      instance's data is incompatible with the current schema. The schema
      should not be updated manually as this can cause incompatibilies that
      cannot be resolved automatically. One resolution in this case is to
      delete the incompatible table and let the server recreate one, though
      the newly created table only contains data after the table recreation.
      BigQuery imposes a 1 MB limit on streaming insert row size, therefore
      any instance that generates more than 1 MB of BigQuery data will not be
      streamed. If an instance cannot be streamed to BigQuery, errors will be
      logged to Cloud Logging (see [Viewing error logs in Cloud
      Logging](https://cloud.google.com/healthcare/docs/how-tos/logging)).
  """

  bigqueryDestination = _messages.MessageField('GoogleCloudHealthcareV1beta1DicomBigQueryDestination', 1)


class GoogleCloudHealthcareV1beta1FhirBigQueryDestination(_messages.Message):
  r"""The configuration for exporting to BigQuery.

  Enums:
    WriteDispositionValueValuesEnum: Determines if existing data in the
      destination dataset is overwritten, appended to, or not written if the
      tables contain data. If a write_disposition is specified, the `force`
      parameter is ignored.

  Fields:
    datasetUri: BigQuery URI to an existing dataset, up to 2000 characters
      long, in the format `bq://projectId.bqDatasetId`.
    force: Use `write_disposition` instead. If `write_disposition` is
      specified, this parameter is ignored. force=false is equivalent to
      write_disposition=WRITE_EMPTY and force=true is equivalent to
      write_disposition=WRITE_TRUNCATE.
    schemaConfig: The configuration for the exported BigQuery schema.
    writeDisposition: Determines if existing data in the destination dataset
      is overwritten, appended to, or not written if the tables contain data.
      If a write_disposition is specified, the `force` parameter is ignored.
  """

  class WriteDispositionValueValuesEnum(_messages.Enum):
    r"""Determines if existing data in the destination dataset is overwritten,
    appended to, or not written if the tables contain data. If a
    write_disposition is specified, the `force` parameter is ignored.

    Values:
      WRITE_DISPOSITION_UNSPECIFIED: Default behavior is the same as
        WRITE_EMPTY.
      WRITE_EMPTY: Only export data if the destination tables are empty.
      WRITE_TRUNCATE: Erase all existing data in the destination tables before
        writing the FHIR resources.
      WRITE_APPEND: Append data to the destination tables.
    """
    WRITE_DISPOSITION_UNSPECIFIED = 0
    WRITE_EMPTY = 1
    WRITE_TRUNCATE = 2
    WRITE_APPEND = 3

  datasetUri = _messages.StringField(1)
  force = _messages.BooleanField(2)
  schemaConfig = _messages.MessageField('SchemaConfig', 3)
  writeDisposition = _messages.EnumField('WriteDispositionValueValuesEnum', 4)


class GoogleCloudHealthcareV1beta1FhirExportResourcesResponse(_messages.Message):
  r"""Response when all resources export successfully. This structure is
  included in the response to describe the detailed outcome after the
  operation finishes successfully.
  """



class GoogleCloudHealthcareV1beta1FhirGcsDestination(_messages.Message):
  r"""The configuration for exporting to Cloud Storage.

  Fields:
    uriPrefix: URI for a Cloud Storage directory where result files should be
      written (in the format `gs://{bucket-id}/{path/to/destination/dir}`). If
      there is no trailing slash, the service appends one when composing the
      object path. The Cloud Storage bucket referenced in `uri_prefix` must
      exist or an error occurs.
  """

  uriPrefix = _messages.StringField(1)


class GoogleCloudHealthcareV1beta1FhirGcsSource(_messages.Message):
  r"""Specifies the configuration for importing data from Cloud Storage.

  Fields:
    uri: Points to a Cloud Storage URI containing file(s) to import. The URI
      must be in the following format: `gs://{bucket_id}/{object_id}`. The URI
      can include wildcards in `object_id` and thus identify multiple files.
      Supported wildcards: * `*` to match 0 or more non-separator characters *
      `**` to match 0 or more characters (including separators). Must be used
      at the end of a path and with no other wildcards in the path. Can also
      be used with a file extension (such as .ndjson), which imports all files
      with the extension in the specified directory and its sub-directories.
      For example, `gs://my-bucket/my-directory/**.ndjson` imports all files
      with `.ndjson` extensions in `my-directory/` and its sub-directories. *
      `?` to match 1 character Files matching the wildcard are expected to
      contain content only, no metadata.
  """

  uri = _messages.StringField(1)


class GoogleCloudHealthcareV1beta1FhirImportResourcesResponse(_messages.Message):
  r"""Final response of importing resources. This structure is included in the
  response to describe the detailed outcome after the operation finishes
  successfully.
  """



class GroupOrSegment(_messages.Message):
  r"""Construct representing a logical group or a segment.

  Fields:
    group: A SchemaGroup attribute.
    segment: A SchemaSegment attribute.
  """

  group = _messages.MessageField('SchemaGroup', 1)
  segment = _messages.MessageField('SchemaSegment', 2)


class HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsCreateRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsCreateRequest
  object.

  Fields:
    annotation: A Annotation resource to be passed as the request body.
    parent: The name of the Annotation store this annotation belongs to. For
      example, `projects/my-project/locations/us-
      central1/datasets/mydataset/annotationStores/myannotationstore`.
  """

  annotation = _messages.MessageField('Annotation', 1)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsDeleteRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsDeleteRequest
  object.

  Fields:
    name: The resource name of the Annotation to delete.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsGetRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsGetRequest
  object.

  Fields:
    name: The resource name of the Annotation to retrieve.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsListRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsListRequest
  object.

  Enums:
    ViewValueValuesEnum: Controls which fields are populated in the response.

  Fields:
    filter: Restricts Annotations returned to those matching a filter.
      Functions available for filtering are: -
      `matches("annotation_source.cloud_healthcare_source.name", substring)`.
      Filter on `cloud_healthcare_source.name`. For example:
      `matches("annotation_source.cloud_healthcare_source.name", "some
      source")`. - `matches("annotation", substring)`. Filter on all fields of
      annotation. For example: `matches("annotation", "some-content")`. -
      `type("text")`, `type("image")`, `type("resource")`. Filter on the type
      of annotation `data`.
    pageSize: Limit on the number of Annotations to return in a single
      response. If not specified, 100 is used. May not be larger than 1000.
    pageToken: The next_page_token value returned from the previous List
      request, if any.
    parent: Name of the Annotation store to retrieve Annotations from.
    view: Controls which fields are populated in the response.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Controls which fields are populated in the response.

    Values:
      ANNOTATION_VIEW_UNSPECIFIED: Same as BASIC.
      ANNOTATION_VIEW_BASIC: Only `name`, `annotation_source` and
        `custom_data` fields are populated.
      ANNOTATION_VIEW_FULL: All fields are populated.
    """
    ANNOTATION_VIEW_UNSPECIFIED = 0
    ANNOTATION_VIEW_BASIC = 1
    ANNOTATION_VIEW_FULL = 2

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 5)


class HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsPatchRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsPatchRequest
  object.

  Fields:
    annotation: A Annotation resource to be passed as the request body.
    name: Resource name of the Annotation, of the form `projects/{project_id}/
      locations/{location_id}/datasets/{dataset_id}/annotationStores/{annotati
      on_store_id}/annotations/{annotation_id}`.
    updateMask: The update mask applies to the resource. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  annotation = _messages.MessageField('Annotation', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class HealthcareProjectsLocationsDatasetsAnnotationStoresCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsAnnotationStoresCreateRequest
  object.

  Fields:
    annotationStore: A AnnotationStore resource to be passed as the request
      body.
    annotationStoreId: The ID of the Annotation store that is being created.
      The string must match the following regex: `[\p{L}\p{N}_\-\.]{1,256}`.
    parent: The name of the dataset this Annotation store belongs to.
  """

  annotationStore = _messages.MessageField('AnnotationStore', 1)
  annotationStoreId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsAnnotationStoresDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsAnnotationStoresDeleteRequest
  object.

  Fields:
    name: The resource name of the Annotation store to delete.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsAnnotationStoresEvaluateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsAnnotationStoresEvaluateRequest
  object.

  Fields:
    evaluateAnnotationStoreRequest: A EvaluateAnnotationStoreRequest resource
      to be passed as the request body.
    name: The Annotation store to compare against `golden_store`, in the
      format of `projects/{project_id}/locations/{location_id}/datasets/{datas
      et_id}/annotationStores/{annotation_store_id}`.
  """

  evaluateAnnotationStoreRequest = _messages.MessageField('EvaluateAnnotationStoreRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsAnnotationStoresExportRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsAnnotationStoresExportRequest
  object.

  Fields:
    exportAnnotationsRequest: A ExportAnnotationsRequest resource to be passed
      as the request body.
    name: The name of the Annotation store to export annotations to, in the
      format of `projects/{project_id}/locations/{location_id}/datasets/{datas
      et_id}/annotationStores/{annotation_store_id}`.
  """

  exportAnnotationsRequest = _messages.MessageField('ExportAnnotationsRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsAnnotationStoresGetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsAnnotationStoresGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsAnnotationStoresGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsAnnotationStoresGetRequest object.

  Fields:
    name: The resource name of the Annotation store to get.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsAnnotationStoresImportRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsAnnotationStoresImportRequest
  object.

  Fields:
    importAnnotationsRequest: A ImportAnnotationsRequest resource to be passed
      as the request body.
    name: The name of the Annotation store to which the server imports
      annotations, in the format `projects/{project_id}/locations/{location_id
      }/datasets/{dataset_id}/annotationStores/{annotation_store_id}`.
  """

  importAnnotationsRequest = _messages.MessageField('ImportAnnotationsRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsAnnotationStoresListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsAnnotationStoresListRequest object.

  Fields:
    filter: Restricts stores returned to those matching a filter. The
      following syntax is available: * A string field value can be written as
      text inside quotation marks, for example `"query text"`. The only valid
      relational operation for text fields is equality (`=`), where text is
      searched within the field, rather than having the field be equal to the
      text. For example, `"Comment = great"` returns messages with `great` in
      the comment field. * A number field value can be written as an integer,
      a decimal, or an exponential. The valid relational operators for number
      fields are the equality operator (`=`), along with the less than/greater
      than operators (`<`, `<=`, `>`, `>=`). Note that there is no inequality
      (`!=`) operator. You can prepend the `NOT` operator to an expression to
      negate it. * A date field value must be written in `yyyy-mm-dd` form.
      Fields with date and time use the RFC3339 time format. Leading zeros are
      required for one-digit months and days. The valid relational operators
      for date fields are the equality operator (`=`) , along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * Multiple field query expressions can be
      combined in one query by adding `AND` or `OR` operators between the
      expressions. If a boolean operator appears within a quoted string, it is
      not treated as special, it's just another part of the character string
      to be matched. You can prepend the `NOT` operator to an expression to
      negate it. Only filtering on labels is supported, for example
      `labels.key=value`.
    pageSize: Limit on the number of Annotation stores to return in a single
      response. If not specified, 100 is used. May not be larger than 1000.
    pageToken: The next_page_token value returned from the previous List
      request, if any.
    parent: Name of the dataset.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsAnnotationStoresPatchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsAnnotationStoresPatchRequest
  object.

  Fields:
    annotationStore: A AnnotationStore resource to be passed as the request
      body.
    name: Resource name of the Annotation store, of the form `projects/{projec
      t_id}/locations/{location_id}/datasets/{dataset_id}/annotationStores/{an
      notation_store_id}`.
    updateMask: The update mask applies to the resource. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  annotationStore = _messages.MessageField('AnnotationStore', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class HealthcareProjectsLocationsDatasetsAnnotationStoresSetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsAnnotationStoresSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class HealthcareProjectsLocationsDatasetsAnnotationStoresTestIamPermissionsRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsAnnotationStoresTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsCr
  eateRequest object.

  Fields:
    attributeDefinition: A AttributeDefinition resource to be passed as the
      request body.
    attributeDefinitionId: Required. The ID of the Attribute definition to
      create. The string must match the following regex: `_a-zA-Z{0,255}` and
      must not be a reserved keyword within the Common Expression Language as
      listed on https://github.com/google/cel-spec/blob/master/doc/langdef.md.
    parent: Required. The name of the consent store that this Attribute
      definition belongs to.
  """

  attributeDefinition = _messages.MessageField('AttributeDefinition', 1)
  attributeDefinitionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsDe
  leteRequest object.

  Fields:
    name: Required. The resource name of the Attribute definition to delete.
      To preserve referential integrity, Attribute definitions referenced by a
      User data mapping or the latest revision of a Consent cannot be deleted.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsGe
  tRequest object.

  Fields:
    name: Required. The resource name of the Attribute definition to get.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsLi
  stRequest object.

  Fields:
    filter: Optional. Restricts the attributes returned to those matching a
      filter. The following syntax is available: * A string field value can be
      written as text inside quotation marks, for example `"query text"`. The
      only valid relational operation for text fields is equality (`=`), where
      text is searched within the field, rather than having the field be equal
      to the text. For example, `"Comment = great"` returns messages with
      `great` in the comment field. * A number field value can be written as
      an integer, a decimal, or an exponential. The valid relational operators
      for number fields are the equality operator (`=`), along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * A date field value must be written in `yyyy-
      mm-dd` form. Fields with date and time use the RFC3339 time format.
      Leading zeros are required for one-digit months and days. The valid
      relational operators for date fields are the equality operator (`=`) ,
      along with the less than/greater than operators (`<`, `<=`, `>`, `>=`).
      Note that there is no inequality (`!=`) operator. You can prepend the
      `NOT` operator to an expression to negate it. * Multiple field query
      expressions can be combined in one query by adding `AND` or `OR`
      operators between the expressions. If a boolean operator appears within
      a quoted string, it is not treated as special, it's just another part of
      the character string to be matched. You can prepend the `NOT` operator
      to an expression to negate it. The only field available for filtering is
      `category`. For example, `filter=category=\"REQUEST\"`.
    pageSize: Optional. Limit on the number of Attribute definitions to return
      in a single response. If not specified, 100 is used. May not be larger
      than 1000.
    pageToken: Optional. Token to retrieve the next page of results or empty
      to get the first page.
    parent: Required. Name of the consent store to retrieve Attribute
      definitions from.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsPatchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsPa
  tchRequest object.

  Fields:
    attributeDefinition: A AttributeDefinition resource to be passed as the
      request body.
    name: Resource name of the Attribute definition, of the form `projects/{pr
      oject_id}/locations/{location_id}/datasets/{dataset_id}/consentStores/{c
      onsent_store_id}/attributeDefinitions/{attribute_definition_id}`. Cannot
      be changed after creation.
    updateMask: Required. The update mask that applies to the resource. For
      the `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask. Only the
      `description`, `allowed_values`, `consent_default_values` and
      `data_mapping_default_value` fields can be updated. The updated
      `allowed_values` must contain all values from the previous
      `allowed_values`.
  """

  attributeDefinition = _messages.MessageField('AttributeDefinition', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class HealthcareProjectsLocationsDatasetsConsentStoresCheckDataAccessRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresCheckDataAccessRequest
  object.

  Fields:
    checkDataAccessRequest: A CheckDataAccessRequest resource to be passed as
      the request body.
    consentStore: Required. Name of the consent store where the requested
      data_id is stored, of the form `projects/{project_id}/locations/{locatio
      n_id}/datasets/{dataset_id}/consentStores/{consent_store_id}`.
  """

  checkDataAccessRequest = _messages.MessageField('CheckDataAccessRequest', 1)
  consentStore = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsCreate
  Request object.

  Fields:
    consentArtifact: A ConsentArtifact resource to be passed as the request
      body.
    parent: Required. The name of the consent store this Consent artifact
      belongs to.
  """

  consentArtifact = _messages.MessageField('ConsentArtifact', 1)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsDelete
  Request object.

  Fields:
    name: Required. The resource name of the Consent artifact to delete. To
      preserve referential integrity, Consent artifacts referenced by the
      latest revision of a Consent cannot be deleted.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsGetRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsGetRequest
  object.

  Fields:
    name: Required. The resource name of the Consent artifact to retrieve.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsListRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsListRequest
  object.

  Fields:
    filter: Optional. Restricts the artifacts returned to those matching a
      filter. The following syntax is available: * A string field value can be
      written as text inside quotation marks, for example `"query text"`. The
      only valid relational operation for text fields is equality (`=`), where
      text is searched within the field, rather than having the field be equal
      to the text. For example, `"Comment = great"` returns messages with
      `great` in the comment field. * A number field value can be written as
      an integer, a decimal, or an exponential. The valid relational operators
      for number fields are the equality operator (`=`), along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * A date field value must be written in `yyyy-
      mm-dd` form. Fields with date and time use the RFC3339 time format.
      Leading zeros are required for one-digit months and days. The valid
      relational operators for date fields are the equality operator (`=`) ,
      along with the less than/greater than operators (`<`, `<=`, `>`, `>=`).
      Note that there is no inequality (`!=`) operator. You can prepend the
      `NOT` operator to an expression to negate it. * Multiple field query
      expressions can be combined in one query by adding `AND` or `OR`
      operators between the expressions. If a boolean operator appears within
      a quoted string, it is not treated as special, it's just another part of
      the character string to be matched. You can prepend the `NOT` operator
      to an expression to negate it. The fields available for filtering are: -
      user_id. For example, `filter=user_id=\"user123\"`. -
      consent_content_version - metadata. For example,
      `filter=Metadata(\"testkey\")=\"value\"` or
      `filter=HasMetadata(\"testkey\")`.
    pageSize: Optional. Limit on the number of consent artifacts to return in
      a single response. If not specified, 100 is used. May not be larger than
      1000.
    pageToken: Optional. The next_page_token value returned from the previous
      List request, if any.
    parent: Required. Name of the consent store to retrieve consent artifacts
      from.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentsActivateRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresConsentsActivateRequest
  object.

  Fields:
    activateConsentRequest: A ActivateConsentRequest resource to be passed as
      the request body.
    name: Required. The resource name of the Consent to activate, of the form
      `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/con
      sentStores/{consent_store_id}/consents/{consent_id}`. An
      INVALID_ARGUMENT error occurs if `revision_id` is specified in the name.
  """

  activateConsentRequest = _messages.MessageField('ActivateConsentRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentsCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentsCreateRequest
  object.

  Fields:
    consent: A Consent resource to be passed as the request body.
    parent: Required. Name of the consent store.
  """

  consent = _messages.MessageField('Consent', 1)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentsDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentsDeleteRequest
  object.

  Fields:
    name: Required. The resource name of the Consent to delete, of the form `p
      rojects/{project_id}/locations/{location_id}/datasets/{dataset_id}/conse
      ntStores/{consent_store_id}/consents/{consent_id}`. An INVALID_ARGUMENT
      error occurs if `revision_id` is specified in the name.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentsDeleteRevisionRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentsDeleteRevision
  Request object.

  Fields:
    name: Required. The resource name of the Consent revision to delete, of
      the form `projects/{project_id}/locations/{location_id}/datasets/{datase
      t_id}/consentStores/{consent_store_id}/consents/{consent_id}@{revision_i
      d}`. An INVALID_ARGUMENT error occurs if `revision_id` is not specified
      in the name.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentsGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentsGetRequest
  object.

  Fields:
    name: Required. The resource name of the Consent to retrieve, of the form
      `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/con
      sentStores/{consent_store_id}/consents/{consent_id}`. In order to
      retrieve a previous revision of the Consent, also provide the revision
      ID: `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}
      /consentStores/{consent_store_id}/consents/{consent_id}@{revision_id}`
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentsListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentsListRequest
  object.

  Fields:
    filter: Optional. Restricts the consents returned to those matching a
      filter. The following syntax is available: * A string field value can be
      written as text inside quotation marks, for example `"query text"`. The
      only valid relational operation for text fields is equality (`=`), where
      text is searched within the field, rather than having the field be equal
      to the text. For example, `"Comment = great"` returns messages with
      `great` in the comment field. * A number field value can be written as
      an integer, a decimal, or an exponential. The valid relational operators
      for number fields are the equality operator (`=`), along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * A date field value must be written in `yyyy-
      mm-dd` form. Fields with date and time use the RFC3339 time format.
      Leading zeros are required for one-digit months and days. The valid
      relational operators for date fields are the equality operator (`=`) ,
      along with the less than/greater than operators (`<`, `<=`, `>`, `>=`).
      Note that there is no inequality (`!=`) operator. You can prepend the
      `NOT` operator to an expression to negate it. * Multiple field query
      expressions can be combined in one query by adding `AND` or `OR`
      operators between the expressions. If a boolean operator appears within
      a quoted string, it is not treated as special, it's just another part of
      the character string to be matched. You can prepend the `NOT` operator
      to an expression to negate it. The fields available for filtering are: -
      user_id. For example, `filter='user_id="user123"'`. - consent_artifact -
      state - revision_create_time - metadata. For example,
      `filter=Metadata(\"testkey\")=\"value\"` or
      `filter=HasMetadata(\"testkey\")`.
    pageSize: Optional. Limit on the number of Consents to return in a single
      response. If not specified, 100 is used. May not be larger than 1000.
    pageToken: Optional. The next_page_token value returned from the previous
      List request, if any.
    parent: Required. Name of the consent store to retrieve Consents from.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentsListRevisionsRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresConsentsListRevisionsRequest
  object.

  Fields:
    filter: Optional. Restricts the revisions returned to those matching a
      filter. The following syntax is available: * A string field value can be
      written as text inside quotation marks, for example `"query text"`. The
      only valid relational operation for text fields is equality (`=`), where
      text is searched within the field, rather than having the field be equal
      to the text. For example, `"Comment = great"` returns messages with
      `great` in the comment field. * A number field value can be written as
      an integer, a decimal, or an exponential. The valid relational operators
      for number fields are the equality operator (`=`), along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * A date field value must be written in `yyyy-
      mm-dd` form. Fields with date and time use the RFC3339 time format.
      Leading zeros are required for one-digit months and days. The valid
      relational operators for date fields are the equality operator (`=`) ,
      along with the less than/greater than operators (`<`, `<=`, `>`, `>=`).
      Note that there is no inequality (`!=`) operator. You can prepend the
      `NOT` operator to an expression to negate it. * Multiple field query
      expressions can be combined in one query by adding `AND` or `OR`
      operators between the expressions. If a boolean operator appears within
      a quoted string, it is not treated as special, it's just another part of
      the character string to be matched. You can prepend the `NOT` operator
      to an expression to negate it. Fields/functions available for filtering
      are: - user_id. For example, `filter='user_id="user123"'`. -
      consent_artifact - state - revision_create_time - metadata. For example,
      `filter=Metadata(\"testkey\")=\"value\"` or
      `filter=HasMetadata(\"testkey\")`.
    name: Required. The resource name of the Consent to retrieve revisions
      for.
    pageSize: Optional. Limit on the number of revisions to return in a single
      response. If not specified, 100 is used. May not be larger than 1000.
    pageToken: Optional. Token to retrieve the next page of results or empty
      if there are no more results in the list.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentsPatchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentsPatchRequest
  object.

  Fields:
    consent: A Consent resource to be passed as the request body.
    name: Resource name of the Consent, of the form `projects/{project_id}/loc
      ations/{location_id}/datasets/{dataset_id}/consentStores/{consent_store_
      id}/consents/{consent_id}`. Cannot be changed after creation.
    updateMask: Required. The update mask to apply to the resource. For the
      `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask. Only the `user_id`,
      `policies`, `consent_artifact`, and `metadata` fields can be updated.
  """

  consent = _messages.MessageField('Consent', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentsRejectRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentsRejectRequest
  object.

  Fields:
    name: Required. The resource name of the Consent to reject, of the form `p
      rojects/{project_id}/locations/{location_id}/datasets/{dataset_id}/conse
      ntStores/{consent_store_id}/consents/{consent_id}`. An INVALID_ARGUMENT
      error occurs if `revision_id` is specified in the name.
    rejectConsentRequest: A RejectConsentRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  rejectConsentRequest = _messages.MessageField('RejectConsentRequest', 2)


class HealthcareProjectsLocationsDatasetsConsentStoresConsentsRevokeRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresConsentsRevokeRequest
  object.

  Fields:
    name: Required. The resource name of the Consent to revoke, of the form `p
      rojects/{project_id}/locations/{location_id}/datasets/{dataset_id}/conse
      ntStores/{consent_store_id}/consents/{consent_id}`. An INVALID_ARGUMENT
      error occurs if `revision_id` is specified in the name.
    revokeConsentRequest: A RevokeConsentRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  revokeConsentRequest = _messages.MessageField('RevokeConsentRequest', 2)


class HealthcareProjectsLocationsDatasetsConsentStoresCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresCreateRequest object.

  Fields:
    consentStore: A ConsentStore resource to be passed as the request body.
    consentStoreId: Required. The ID of the consent store to create. The
      string must match the following regex: `[\p{L}\p{N}_\-\.]{1,256}`.
      Cannot be changed after creation.
    parent: Required. The name of the dataset this consent store belongs to.
  """

  consentStore = _messages.MessageField('ConsentStore', 1)
  consentStoreId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresDeleteRequest object.

  Fields:
    name: Required. The resource name of the consent store to delete.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresEvaluateUserConsentsRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresEvaluateUserConsentsRequest
  object.

  Fields:
    consentStore: Required. Name of the consent store to retrieve User data
      mappings from.
    evaluateUserConsentsRequest: A EvaluateUserConsentsRequest resource to be
      passed as the request body.
  """

  consentStore = _messages.StringField(1, required=True)
  evaluateUserConsentsRequest = _messages.MessageField('EvaluateUserConsentsRequest', 2)


class HealthcareProjectsLocationsDatasetsConsentStoresGetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresGetRequest object.

  Fields:
    name: Required. The resource name of the consent store to get.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresListRequest object.

  Fields:
    filter: Optional. Restricts the stores returned to those matching a
      filter. The following syntax is available: * A string field value can be
      written as text inside quotation marks, for example `"query text"`. The
      only valid relational operation for text fields is equality (`=`), where
      text is searched within the field, rather than having the field be equal
      to the text. For example, `"Comment = great"` returns messages with
      `great` in the comment field. * A number field value can be written as
      an integer, a decimal, or an exponential. The valid relational operators
      for number fields are the equality operator (`=`), along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * A date field value must be written in `yyyy-
      mm-dd` form. Fields with date and time use the RFC3339 time format.
      Leading zeros are required for one-digit months and days. The valid
      relational operators for date fields are the equality operator (`=`) ,
      along with the less than/greater than operators (`<`, `<=`, `>`, `>=`).
      Note that there is no inequality (`!=`) operator. You can prepend the
      `NOT` operator to an expression to negate it. * Multiple field query
      expressions can be combined in one query by adding `AND` or `OR`
      operators between the expressions. If a boolean operator appears within
      a quoted string, it is not treated as special, it's just another part of
      the character string to be matched. You can prepend the `NOT` operator
      to an expression to negate it. Only filtering on labels is supported.
      For example, `filter=labels.key=value`.
    pageSize: Optional. Limit on the number of consent stores to return in a
      single response. If not specified, 100 is used. May not be larger than
      1000.
    pageToken: Optional. Token to retrieve the next page of results, or empty
      to get the first page.
    parent: Required. Name of the dataset.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresPatchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresPatchRequest object.

  Fields:
    consentStore: A ConsentStore resource to be passed as the request body.
    name: Resource name of the consent store, of the form `projects/{project_i
      d}/locations/{location_id}/datasets/{dataset_id}/consentStores/{consent_
      store_id}`. Cannot be changed after creation.
    updateMask: Required. The update mask that applies to the resource. For
      the `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask. Only the `labels`,
      `default_consent_ttl`, and `enable_consent_create_on_update` fields are
      allowed to be updated.
  """

  consentStore = _messages.MessageField('ConsentStore', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class HealthcareProjectsLocationsDatasetsConsentStoresQueryAccessibleDataRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresQueryAccessibleDataRequest
  object.

  Fields:
    consentStore: Required. Name of the consent store to retrieve User data
      mappings from.
    queryAccessibleDataRequest: A QueryAccessibleDataRequest resource to be
      passed as the request body.
  """

  consentStore = _messages.StringField(1, required=True)
  queryAccessibleDataRequest = _messages.MessageField('QueryAccessibleDataRequest', 2)


class HealthcareProjectsLocationsDatasetsConsentStoresSetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class HealthcareProjectsLocationsDatasetsConsentStoresTestIamPermissionsRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsArchiveRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsArchiv
  eRequest object.

  Fields:
    archiveUserDataMappingRequest: A ArchiveUserDataMappingRequest resource to
      be passed as the request body.
    name: Required. The resource name of the User data mapping to archive.
  """

  archiveUserDataMappingRequest = _messages.MessageField('ArchiveUserDataMappingRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsCreate
  Request object.

  Fields:
    parent: Required. Name of the consent store.
    userDataMapping: A UserDataMapping resource to be passed as the request
      body.
  """

  parent = _messages.StringField(1, required=True)
  userDataMapping = _messages.MessageField('UserDataMapping', 2)


class HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsDelete
  Request object.

  Fields:
    name: Required. The resource name of the User data mapping to delete.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsGetRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsGetRequest
  object.

  Fields:
    name: Required. The resource name of the User data mapping to retrieve.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsListRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsListRequest
  object.

  Fields:
    filter: Optional. Restricts the user data mappings returned to those
      matching a filter. The following syntax is available: * A string field
      value can be written as text inside quotation marks, for example `"query
      text"`. The only valid relational operation for text fields is equality
      (`=`), where text is searched within the field, rather than having the
      field be equal to the text. For example, `"Comment = great"` returns
      messages with `great` in the comment field. * A number field value can
      be written as an integer, a decimal, or an exponential. The valid
      relational operators for number fields are the equality operator (`=`),
      along with the less than/greater than operators (`<`, `<=`, `>`, `>=`).
      Note that there is no inequality (`!=`) operator. You can prepend the
      `NOT` operator to an expression to negate it. * A date field value must
      be written in `yyyy-mm-dd` form. Fields with date and time use the
      RFC3339 time format. Leading zeros are required for one-digit months and
      days. The valid relational operators for date fields are the equality
      operator (`=`) , along with the less than/greater than operators (`<`,
      `<=`, `>`, `>=`). Note that there is no inequality (`!=`) operator. You
      can prepend the `NOT` operator to an expression to negate it. * Multiple
      field query expressions can be combined in one query by adding `AND` or
      `OR` operators between the expressions. If a boolean operator appears
      within a quoted string, it is not treated as special, it's just another
      part of the character string to be matched. You can prepend the `NOT`
      operator to an expression to negate it. The fields available for
      filtering are: - data_id - user_id. For example,
      `filter=user_id=\"user123\"`. - archived - archive_time
    pageSize: Optional. Limit on the number of User data mappings to return in
      a single response. If not specified, 100 is used. May not be larger than
      1000.
    pageToken: Optional. Token to retrieve the next page of results, or empty
      to get the first page.
    parent: Required. Name of the consent store to retrieve User data mappings
      from.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsPatchRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsPatchRequest
  object.

  Fields:
    name: Resource name of the User data mapping, of the form `projects/{proje
      ct_id}/locations/{location_id}/datasets/{dataset_id}/consentStores/{cons
      ent_store_id}/userDataMappings/{user_data_mapping_id}`.
    updateMask: Required. The update mask that applies to the resource. For
      the `FieldMask` definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask. Only the `data_id`,
      `user_id` and `resource_attributes` fields can be updated.
    userDataMapping: A UserDataMapping resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  userDataMapping = _messages.MessageField('UserDataMapping', 3)


class HealthcareProjectsLocationsDatasetsCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsCreateRequest object.

  Fields:
    dataset: A Dataset resource to be passed as the request body.
    datasetId: The ID of the dataset that is being created. The string must
      match the following regex: `[\p{L}\p{N}_\-\.]{1,256}`.
    parent: The name of the project where the server creates the dataset. For
      example, `projects/{project_id}/locations/{location_id}`.
  """

  dataset = _messages.MessageField('Dataset', 1)
  datasetId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsDeidentifyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDeidentifyRequest object.

  Fields:
    deidentifyDatasetRequest: A DeidentifyDatasetRequest resource to be passed
      as the request body.
    sourceDataset: Source dataset resource name. For example,
      `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}`.
  """

  deidentifyDatasetRequest = _messages.MessageField('DeidentifyDatasetRequest', 1)
  sourceDataset = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDeleteRequest object.

  Fields:
    name: The name of the dataset to delete. For example,
      `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}`.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresCreateRequest object.

  Fields:
    dicomStore: A DicomStore resource to be passed as the request body.
    dicomStoreId: The ID of the DICOM store that is being created. Any string
      value up to 256 characters in length.
    parent: The name of the dataset this DICOM store belongs to.
  """

  dicomStore = _messages.MessageField('DicomStore', 1)
  dicomStoreId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresDeidentifyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresDeidentifyRequest
  object.

  Fields:
    deidentifyDicomStoreRequest: A DeidentifyDicomStoreRequest resource to be
      passed as the request body.
    sourceStore: Source DICOM store resource name. For example, `projects/{pro
      ject_id}/locations/{location_id}/datasets/{dataset_id}/dicomStores/{dico
      m_store_id}`.
  """

  deidentifyDicomStoreRequest = _messages.MessageField('DeidentifyDicomStoreRequest', 1)
  sourceStore = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresDeleteRequest object.

  Fields:
    name: The resource name of the DICOM store to delete.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresExportRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresExportRequest object.

  Fields:
    exportDicomDataRequest: A ExportDicomDataRequest resource to be passed as
      the request body.
    name: The DICOM store resource name from which to export the data. For
      example, `projects/{project_id}/locations/{location_id}/datasets/{datase
      t_id}/dicomStores/{dicom_store_id}`.
  """

  exportDicomDataRequest = _messages.MessageField('ExportDicomDataRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresGetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresGetRequest object.

  Fields:
    name: The resource name of the DICOM store to get.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresImportRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresImportRequest object.

  Fields:
    importDicomDataRequest: A ImportDicomDataRequest resource to be passed as
      the request body.
    name: The name of the DICOM store resource into which the data is
      imported. For example, `projects/{project_id}/locations/{location_id}/da
      tasets/{dataset_id}/dicomStores/{dicom_store_id}`.
  """

  importDicomDataRequest = _messages.MessageField('ImportDicomDataRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresListRequest object.

  Fields:
    filter: Restricts stores returned to those matching a filter. The
      following syntax is available: * A string field value can be written as
      text inside quotation marks, for example `"query text"`. The only valid
      relational operation for text fields is equality (`=`), where text is
      searched within the field, rather than having the field be equal to the
      text. For example, `"Comment = great"` returns messages with `great` in
      the comment field. * A number field value can be written as an integer,
      a decimal, or an exponential. The valid relational operators for number
      fields are the equality operator (`=`), along with the less than/greater
      than operators (`<`, `<=`, `>`, `>=`). Note that there is no inequality
      (`!=`) operator. You can prepend the `NOT` operator to an expression to
      negate it. * A date field value must be written in `yyyy-mm-dd` form.
      Fields with date and time use the RFC3339 time format. Leading zeros are
      required for one-digit months and days. The valid relational operators
      for date fields are the equality operator (`=`) , along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * Multiple field query expressions can be
      combined in one query by adding `AND` or `OR` operators between the
      expressions. If a boolean operator appears within a quoted string, it is
      not treated as special, it's just another part of the character string
      to be matched. You can prepend the `NOT` operator to an expression to
      negate it. Only filtering on labels is supported. For example,
      `labels.key=value`.
    pageSize: Limit on the number of DICOM stores to return in a single
      response. If not specified, 100 is used. May not be larger than 1000.
    pageToken: The next_page_token value returned from the previous List
      request, if any.
    parent: Name of the dataset.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresPatchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresPatchRequest object.

  Fields:
    dicomStore: A DicomStore resource to be passed as the request body.
    name: Resource name of the DICOM store, of the form `projects/{project_id}
      /locations/{location_id}/datasets/{dataset_id}/dicomStores/{dicom_store_
      id}`.
    updateMask: The update mask applies to the resource. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  dicomStore = _messages.MessageField('DicomStore', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class HealthcareProjectsLocationsDatasetsDicomStoresSearchForInstancesRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsDicomStoresSearchForInstancesRequest
  object.

  Fields:
    dicomWebPath: The path of the SearchForInstancesRequest DICOMweb request.
      For example, `instances`, `series/{series_uid}/instances`, or
      `studies/{study_uid}/instances`.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresSearchForSeriesRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresSearchForSeriesRequest
  object.

  Fields:
    dicomWebPath: The path of the SearchForSeries DICOMweb request. For
      example, `series` or `studies/{study_uid}/series`.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresSearchForStudiesRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresSearchForStudiesRequest
  object.

  Fields:
    dicomWebPath: The path of the SearchForStudies DICOMweb request. For
      example, `studies`.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresSetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class HealthcareProjectsLocationsDatasetsDicomStoresStoreInstancesRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresStoreInstancesRequest
  object.

  Fields:
    dicomWebPath: The path of the StoreInstances DICOMweb request. For
      example, `studies/[{study_uid}]`. Note that the `study_uid` is optional.
    httpBody: A HttpBody resource to be passed as the request body.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  httpBody = _messages.MessageField('HttpBody', 2)
  parent = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresStudiesDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresStudiesDeleteRequest
  object.

  Fields:
    dicomWebPath: The path of the DeleteStudy request. For example,
      `studies/{study_uid}`.
    parent: A string attribute.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresStudiesRetrieveMetadataRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsDicomStoresStudiesRetrieveMetadataRequest
  object.

  Fields:
    dicomWebPath: The path of the RetrieveStudyMetadata DICOMweb request. For
      example, `studies/{study_uid}/metadata`.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresStudiesRetrieveStudyRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsDicomStoresStudiesRetrieveStudyRequest
  object.

  Fields:
    dicomWebPath: The path of the RetrieveStudy DICOMweb request. For example,
      `studies/{study_uid}`.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresStudiesSearchForInstancesRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresStudiesSearchForInstance
  sRequest object.

  Fields:
    dicomWebPath: The path of the SearchForInstancesRequest DICOMweb request.
      For example, `instances`, `series/{series_uid}/instances`, or
      `studies/{study_uid}/instances`.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresStudiesSearchForSeriesRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsDicomStoresStudiesSearchForSeriesRequest
  object.

  Fields:
    dicomWebPath: The path of the SearchForSeries DICOMweb request. For
      example, `series` or `studies/{study_uid}/series`.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesDeleteRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesDeleteRequest
  object.

  Fields:
    dicomWebPath: The path of the DeleteSeries request. For example,
      `studies/{study_uid}/series/{series_uid}`.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesDe
  leteRequest object.

  Fields:
    dicomWebPath: The path of the DeleteInstance request. For example,
      `studies/{study_uid}/series/{series_uid}/instances/{instance_uid}`.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesFramesRetrieveFramesRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesFr
  amesRetrieveFramesRequest object.

  Fields:
    dicomWebPath: The path of the RetrieveFrames DICOMweb request. For
      example, `studies/{study_uid}/series/{series_uid}/instances/{instance_ui
      d}/frames/{frame_list}`.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesFramesRetrieveRenderedRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesFr
  amesRetrieveRenderedRequest object.

  Fields:
    dicomWebPath: The path of the RetrieveRenderedFrames DICOMweb request. For
      example, `studies/{study_uid}/series/{series_uid}/instances/{instance_ui
      d}/frames/{frame_list}/rendered`.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesRetrieveInstanceRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesRe
  trieveInstanceRequest object.

  Fields:
    dicomWebPath: The path of the RetrieveInstance DICOMweb request. For
      example,
      `studies/{study_uid}/series/{series_uid}/instances/{instance_uid}`.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesRetrieveMetadataRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesRe
  trieveMetadataRequest object.

  Fields:
    dicomWebPath: The path of the RetrieveInstanceMetadata DICOMweb request.
      For example, `studies/{study_uid}/series/{series_uid}/instances/{instanc
      e_uid}/metadata`.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesRetrieveRenderedRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesRe
  trieveRenderedRequest object.

  Fields:
    dicomWebPath: The path of the RetrieveRenderedInstance DICOMweb request.
      For example, `studies/{study_uid}/series/{series_uid}/instances/{instanc
      e_uid}/rendered`.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesRetrieveMetadataRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesRetrieveMet
  adataRequest object.

  Fields:
    dicomWebPath: The path of the RetrieveSeriesMetadata DICOMweb request. For
      example, `studies/{study_uid}/series/{series_uid}/metadata`.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesRetrieveSeriesRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesRetrieveSer
  iesRequest object.

  Fields:
    dicomWebPath: The path of the RetrieveSeries DICOMweb request. For
      example, `studies/{study_uid}/series/{series_uid}`.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesSearchForInstancesRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesSearchForIn
  stancesRequest object.

  Fields:
    dicomWebPath: The path of the SearchForInstancesRequest DICOMweb request.
      For example, `instances`, `series/{series_uid}/instances`, or
      `studies/{study_uid}/instances`.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresStudiesStoreInstancesRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsDicomStoresStudiesStoreInstancesRequest
  object.

  Fields:
    dicomWebPath: The path of the StoreInstances DICOMweb request. For
      example, `studies/[{study_uid}]`. Note that the `study_uid` is optional.
    httpBody: A HttpBody resource to be passed as the request body.
    parent: The name of the DICOM store that is being accessed. For example, `
      projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/dico
      mStores/{dicom_store_id}`.
  """

  dicomWebPath = _messages.StringField(1, required=True)
  httpBody = _messages.MessageField('HttpBody', 2)
  parent = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsDicomStoresTestIamPermissionsRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsDicomStoresTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class HealthcareProjectsLocationsDatasetsFhirStoresConfigureSearchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresConfigureSearchRequest
  object.

  Fields:
    configureSearchRequest: A ConfigureSearchRequest resource to be passed as
      the request body.
    name: The name of the FHIR store to configure, in the format `projects/{pr
      oject_id}/locations/{location_id}/datasets/{dataset_id}/fhirStores/{fhir
      _store_id}`.
  """

  configureSearchRequest = _messages.MessageField('ConfigureSearchRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresCreateRequest object.

  Fields:
    fhirStore: A FhirStore resource to be passed as the request body.
    fhirStoreId: The ID of the FHIR store that is being created. The string
      must match the following regex: `[\p{L}\p{N}_\-\.]{1,256}`.
    parent: The name of the dataset this FHIR store belongs to.
  """

  fhirStore = _messages.MessageField('FhirStore', 1)
  fhirStoreId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresDeidentifyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresDeidentifyRequest object.

  Fields:
    deidentifyFhirStoreRequest: A DeidentifyFhirStoreRequest resource to be
      passed as the request body.
    sourceStore: Source FHIR store resource name. For example, `projects/{proj
      ect_id}/locations/{location_id}/datasets/{dataset_id}/fhirStores/{fhir_s
      tore_id}`.
  """

  deidentifyFhirStoreRequest = _messages.MessageField('DeidentifyFhirStoreRequest', 1)
  sourceStore = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresDeleteRequest object.

  Fields:
    name: The resource name of the FHIR store to delete.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresExportRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresExportRequest object.

  Fields:
    exportResourcesRequest: A ExportResourcesRequest resource to be passed as
      the request body.
    name: The name of the FHIR store to export resource from, in the format of
      `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/fhi
      rStores/{fhir_store_id}`.
  """

  exportResourcesRequest = _messages.MessageField('ExportResourcesRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirCapabilitiesRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresFhirCapabilitiesRequest
  object.

  Fields:
    name: Name of the FHIR store to retrieve the capabilities for.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirConceptMapSearchTranslateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresFhirConceptMapSearchTrans
  lateRequest object.

  Fields:
    code: The code to translate.
    conceptMapVersion: The version of the concept map to use. If unset, the
      most current version is used.
    parent: The name for the FHIR store containing the concept map(s) to use
      for the translation.
    source: The source value set of the concept map to be used. If unset,
      target is used to search for concept maps.
    system: The system for the code to be translated.
    target: The target value set of the concept map to be used. If unset,
      source is used to search for concept maps.
    url: The canonical url of the concept map to use. If unset, the source and
      target is used to search for concept maps.
  """

  code = _messages.StringField(1)
  conceptMapVersion = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  source = _messages.StringField(4)
  system = _messages.StringField(5)
  target = _messages.StringField(6)
  url = _messages.StringField(7)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirConceptMapTranslateRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsFhirStoresFhirConceptMapTranslateRequest
  object.

  Fields:
    code: The code to translate.
    conceptMapVersion: The version of the concept map to use. If unset, the
      most current version is used.
    name: The URL for the concept map to use for the translation.
    system: The system for the code to be translated.
  """

  code = _messages.StringField(1)
  conceptMapVersion = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  system = _messages.StringField(4)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirConditionalDeleteRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsFhirStoresFhirConditionalDeleteRequest
  object.

  Fields:
    parent: The name of the FHIR store this resource belongs to.
    type: The FHIR resource type to delete, such as Patient or Observation.
      For a complete list, see the FHIR Resource Index ([DSTU2](https://hl7.or
      g/implement/standards/fhir/DSTU2/resourcelist.html),
      [STU3](https://hl7.org/implement/standards/fhir/STU3/resourcelist.html),
      [R4](https://hl7.org/implement/standards/fhir/R4/resourcelist.html)).
  """

  parent = _messages.StringField(1, required=True)
  type = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirConditionalPatchRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsFhirStoresFhirConditionalPatchRequest
  object.

  Fields:
    httpBody: A HttpBody resource to be passed as the request body.
    parent: The name of the FHIR store this resource belongs to.
    type: The FHIR resource type to update, such as Patient or Observation.
      For a complete list, see the FHIR Resource Index ([DSTU2](https://hl7.or
      g/implement/standards/fhir/DSTU2/resourcelist.html),
      [STU3](https://hl7.org/implement/standards/fhir/STU3/resourcelist.html),
      [R4](https://hl7.org/implement/standards/fhir/R4/resourcelist.html)).
  """

  httpBody = _messages.MessageField('HttpBody', 1)
  parent = _messages.StringField(2, required=True)
  type = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirConditionalUpdateRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsFhirStoresFhirConditionalUpdateRequest
  object.

  Fields:
    httpBody: A HttpBody resource to be passed as the request body.
    parent: The name of the FHIR store this resource belongs to.
    type: The FHIR resource type to update, such as Patient or Observation.
      For a complete list, see the FHIR Resource Index ([DSTU2](https://hl7.or
      g/implement/standards/fhir/DSTU2/resourcelist.html),
      [STU3](https://hl7.org/implement/standards/fhir/STU3/resourcelist.html),
      [R4](https://hl7.org/implement/standards/fhir/R4/resourcelist.html)).
      Must match the resource type in the provided content.
  """

  httpBody = _messages.MessageField('HttpBody', 1)
  parent = _messages.StringField(2, required=True)
  type = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresFhirCreateRequest object.

  Fields:
    httpBody: A HttpBody resource to be passed as the request body.
    parent: The name of the FHIR store this resource belongs to.
    type: The FHIR resource type to create, such as Patient or Observation.
      For a complete list, see the FHIR Resource Index ([DSTU2](https://hl7.or
      g/implement/standards/fhir/DSTU2/resourcelist.html),
      [STU3](https://hl7.org/implement/standards/fhir/STU3/resourcelist.html),
      [R4](https://hl7.org/implement/standards/fhir/R4/resourcelist.html)).
      Must match the resource type in the provided content.
  """

  httpBody = _messages.MessageField('HttpBody', 1)
  parent = _messages.StringField(2, required=True)
  type = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresFhirDeleteRequest object.

  Fields:
    name: The name of the resource to delete.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirExecuteBundleRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresFhirExecuteBundleRequest
  object.

  Fields:
    httpBody: A HttpBody resource to be passed as the request body.
    parent: Name of the FHIR store in which this bundle will be executed.
  """

  httpBody = _messages.MessageField('HttpBody', 1)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirHistoryRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresFhirHistoryRequest
  object.

  Fields:
    _at: Only include resource versions that were current at some point during
      the time period specified in the date time value. The date parameter
      format is yyyy-mm-ddThh:mm:ss[Z|(+|-)hh:mm] Clients may specify any of
      the following: * An entire year: `_at=2019` * An entire month:
      `_at=2019-01` * A specific day: `_at=2019-01-20` * A specific second:
      `_at=2018-12-31T23:59:58Z`
    _count: The maximum number of search results on a page. If not specified,
      100 is used. May not be larger than 1000.
    _page_token: Used to retrieve the first, previous, next, or last page of
      resource versions when using pagination. Value should be set to the
      value of `_page_token` set in next or previous page links' URLs. Next
      and previous page are returned in the response bundle's links field,
      where `link.relation` is "previous" or "next". Omit `_page_token` if no
      previous request has been made.
    _since: Only include resource versions that were created at or after the
      given instant in time. The instant in time uses the format YYYY-MM-
      DDThh:mm:ss.sss+zz:zz (for example 2015-02-07T13:28:17.239+02:00 or
      2017-01-01T00:00:00Z). The time must be specified to the second and
      include a time zone.
    name: The name of the resource to retrieve.
  """

  _at = _messages.StringField(1)
  _count = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  _page_token = _messages.StringField(3)
  _since = _messages.StringField(4)
  name = _messages.StringField(5, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirObservationLastnRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsFhirStoresFhirObservationLastnRequest
  object.

  Fields:
    parent: Name of the FHIR store to retrieve resources from.
  """

  parent = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirPatchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresFhirPatchRequest object.

  Fields:
    httpBody: A HttpBody resource to be passed as the request body.
    name: The name of the resource to update.
  """

  httpBody = _messages.MessageField('HttpBody', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirPatientEverythingRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsFhirStoresFhirPatientEverythingRequest
  object.

  Fields:
    _count: Maximum number of resources in a page. If not specified, 100 is
      used. May not be larger than 1000.
    _page_token: Used to retrieve the next or previous page of results when
      using pagination. Set `_page_token` to the value of _page_token set in
      next or previous page links' url. Next and previous page are returned in
      the response bundle's links field, where `link.relation` is "previous"
      or "next". Omit `_page_token` if no previous request has been made.
    _since: If provided, only resources updated after this time are returned.
      The time uses the format YYYY-MM-DDThh:mm:ss.sss+zz:zz. For example,
      `2015-02-07T13:28:17.239+02:00` or `2017-01-01T00:00:00Z`. The time must
      be specified to the second and include a time zone.
    _type: String of comma-delimited FHIR resource types. If provided, only
      resources of the specified resource type(s) are returned. Specifying
      multiple `_type` parameters isn't supported. For example, the result of
      `_type=Observation&_type=Encounter` is undefined. Use
      `_type=Observation,Encounter` instead.
    end: The response includes records prior to the end date. The date uses
      the format YYYY-MM-DD. If no end date is provided, all records
      subsequent to the start date are in scope.
    name: Name of the `Patient` resource for which the information is
      required.
    start: The response includes records subsequent to the start date. The
      date uses the format YYYY-MM-DD. If no start date is provided, all
      records prior to the end date are in scope.
  """

  _count = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  _page_token = _messages.StringField(2)
  _since = _messages.StringField(3)
  _type = _messages.StringField(4)
  end = _messages.StringField(5)
  name = _messages.StringField(6, required=True)
  start = _messages.StringField(7)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirReadRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresFhirReadRequest object.

  Fields:
    name: The name of the resource to retrieve.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirResourceIncomingReferencesRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresFhirResourceIncomingRefer
  encesRequest object.

  Fields:
    _count: Maximum number of resources in a page. If not specified, 100 is
      used. May not be larger than 1000.
    _page_token: Used to retrieve the next page of results when using
      pagination. Set `_page_token` to the value of _page_token set in next
      page links' url. Next page are returned in the response bundle's links
      field, where `link.relation` is "next". Omit `_page_token` if no
      previous request has been made.
    _summary: Used to simplify the representation of the returned resources.
      `_summary=text` returns only the `text`, `id`, and `meta` top-level
      fields. `_summary=data` removes the `text` field and returns all other
      fields. `_summary=false` returns all parts of the resource(s). Either
      not providing this parameter or providing an empty value to this
      parameter also returns all parts of the resource(s).
    _type: String of comma-delimited FHIR resource types. If provided, only
      resources of the specified resource type(s) are returned. If not
      provided or an empty value is provided, no filter on the returned
      resource type(s) is applied.
    parent: Required. The name of the FHIR store that holds the target
      resource.
    target: Required. The target whose incoming references are requested. This
      param is required and must not be empty. It uses the format
      "ResourceType/ResourceID", for example, target=ResourceType/ResourceID.
  """

  _count = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  _page_token = _messages.StringField(2)
  _summary = _messages.StringField(3)
  _type = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  target = _messages.StringField(6)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirResourcePurgeRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresFhirResourcePurgeRequest
  object.

  Fields:
    name: The name of the resource to purge.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirResourceValidateRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsFhirStoresFhirResourceValidateRequest
  object.

  Fields:
    httpBody: A HttpBody resource to be passed as the request body.
    parent: The name of the FHIR store that holds the profiles being used for
      validation.
    profile: The canonical URL of a profile that this resource should be
      validated against. For example, to validate a Patient resource against
      the US Core Patient profile this parameter would be
      `http://hl7.org/fhir/us/core/StructureDefinition/us-core-patient`. A
      StructureDefinition with this canonical URL must exist in the FHIR
      store.
    type: The FHIR resource type of the resource being validated. For a
      complete list, see the FHIR Resource Index ([DSTU2](http://hl7.org/imple
      ment/standards/fhir/DSTU2/resourcelist.html),
      [STU3](http://hl7.org/implement/standards/fhir/STU3/resourcelist.html),
      or [R4](http://hl7.org/implement/standards/fhir/R4/resourcelist.html)).
      Must match the resource type in the provided content.
  """

  httpBody = _messages.MessageField('HttpBody', 1)
  parent = _messages.StringField(2, required=True)
  profile = _messages.StringField(3)
  type = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirSearchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresFhirSearchRequest object.

  Fields:
    parent: Name of the FHIR store to retrieve resources from.
    searchResourcesRequest: A SearchResourcesRequest resource to be passed as
      the request body.
  """

  parent = _messages.StringField(1, required=True)
  searchResourcesRequest = _messages.MessageField('SearchResourcesRequest', 2)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirSearchTypeRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresFhirSearchTypeRequest
  object.

  Fields:
    parent: Name of the FHIR store to retrieve resources from.
    resourceType: The FHIR resource type to search, such as Patient or
      Observation. For a complete list, see the FHIR Resource Index ([DSTU2](h
      ttps://hl7.org/implement/standards/fhir/DSTU2/resourcelist.html),
      [STU3](https://hl7.org/implement/standards/fhir/STU3/resourcelist.html),
      [R4](https://hl7.org/implement/standards/fhir/R4/resourcelist.html)).
    searchResourcesRequest: A SearchResourcesRequest resource to be passed as
      the request body.
  """

  parent = _messages.StringField(1, required=True)
  resourceType = _messages.StringField(2, required=True)
  searchResourcesRequest = _messages.MessageField('SearchResourcesRequest', 3)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirUpdateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresFhirUpdateRequest object.

  Fields:
    httpBody: A HttpBody resource to be passed as the request body.
    name: The name of the resource to update.
  """

  httpBody = _messages.MessageField('HttpBody', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresFhirVreadRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresFhirVreadRequest object.

  Fields:
    name: The name of the resource version to retrieve.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresGetFHIRStoreMetricsRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsFhirStoresGetFHIRStoreMetricsRequest
  object.

  Fields:
    name: The resource name of the FHIR store to get metrics for.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresGetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresGetRequest object.

  Fields:
    name: The resource name of the FHIR store to get.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresImportRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresImportRequest object.

  Fields:
    importResourcesRequest: A ImportResourcesRequest resource to be passed as
      the request body.
    name: The name of the FHIR store to import FHIR resources to, in the
      format of `projects/{project_id}/locations/{location_id}/datasets/{datas
      et_id}/fhirStores/{fhir_store_id}`.
  """

  importResourcesRequest = _messages.MessageField('ImportResourcesRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresListRequest object.

  Fields:
    filter: Restricts stores returned to those matching a filter. The
      following syntax is available: * A string field value can be written as
      text inside quotation marks, for example `"query text"`. The only valid
      relational operation for text fields is equality (`=`), where text is
      searched within the field, rather than having the field be equal to the
      text. For example, `"Comment = great"` returns messages with `great` in
      the comment field. * A number field value can be written as an integer,
      a decimal, or an exponential. The valid relational operators for number
      fields are the equality operator (`=`), along with the less than/greater
      than operators (`<`, `<=`, `>`, `>=`). Note that there is no inequality
      (`!=`) operator. You can prepend the `NOT` operator to an expression to
      negate it. * A date field value must be written in `yyyy-mm-dd` form.
      Fields with date and time use the RFC3339 time format. Leading zeros are
      required for one-digit months and days. The valid relational operators
      for date fields are the equality operator (`=`) , along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * Multiple field query expressions can be
      combined in one query by adding `AND` or `OR` operators between the
      expressions. If a boolean operator appears within a quoted string, it is
      not treated as special, it's just another part of the character string
      to be matched. You can prepend the `NOT` operator to an expression to
      negate it. Only filtering on labels is supported, for example
      `labels.key=value`.
    pageSize: Limit on the number of FHIR stores to return in a single
      response. If not specified, 100 is used. May not be larger than 1000.
    pageToken: The next_page_token value returned from the previous List
      request, if any.
    parent: Name of the dataset.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsFhirStoresPatchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresPatchRequest object.

  Fields:
    fhirStore: A FhirStore resource to be passed as the request body.
    name: Output only. Resource name of the FHIR store, of the form
      `projects/{project_id}/datasets/{dataset_id}/fhirStores/{fhir_store_id}`
      .
    updateMask: The update mask applies to the resource. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  fhirStore = _messages.MessageField('FhirStore', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class HealthcareProjectsLocationsDatasetsFhirStoresSetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class HealthcareProjectsLocationsDatasetsFhirStoresTestIamPermissionsRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsFhirStoresTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class HealthcareProjectsLocationsDatasetsGetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsGetRequest object.

  Fields:
    name: The name of the dataset to read. For example,
      `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}`.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresCreateRequest object.

  Fields:
    hl7V2Store: A Hl7V2Store resource to be passed as the request body.
    hl7V2StoreId: The ID of the HL7v2 store that is being created. The string
      must match the following regex: `[\p{L}\p{N}_\-\.]{1,256}`.
    parent: The name of the dataset this HL7v2 store belongs to.
  """

  hl7V2Store = _messages.MessageField('Hl7V2Store', 1)
  hl7V2StoreId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresDeleteRequest object.

  Fields:
    name: The resource name of the HL7v2 store to delete.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresExportRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresExportRequest object.

  Fields:
    exportMessagesRequest: A ExportMessagesRequest resource to be passed as
      the request body.
    name: The name of the source HL7v2 store, in the format `projects/{project
      _id}/locations/{location_id}/datasets/{dataset_id}/hl7v2Stores/{hl7v2_st
      ore_id}`
  """

  exportMessagesRequest = _messages.MessageField('ExportMessagesRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresGetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresGetRequest object.

  Fields:
    name: The resource name of the HL7v2 store to get.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresImportRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresImportRequest object.

  Fields:
    importMessagesRequest: A ImportMessagesRequest resource to be passed as
      the request body.
    name: The name of the target HL7v2 store, in the format `projects/{project
      _id}/locations/{location_id}/datasets/{dataset_id}/hl7v2Stores/{hl7v2_st
      ore_id}`
  """

  importMessagesRequest = _messages.MessageField('ImportMessagesRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresListRequest object.

  Fields:
    filter: Restricts stores returned to those matching a filter. The
      following syntax is available: * A string field value can be written as
      text inside quotation marks, for example `"query text"`. The only valid
      relational operation for text fields is equality (`=`), where text is
      searched within the field, rather than having the field be equal to the
      text. For example, `"Comment = great"` returns messages with `great` in
      the comment field. * A number field value can be written as an integer,
      a decimal, or an exponential. The valid relational operators for number
      fields are the equality operator (`=`), along with the less than/greater
      than operators (`<`, `<=`, `>`, `>=`). Note that there is no inequality
      (`!=`) operator. You can prepend the `NOT` operator to an expression to
      negate it. * A date field value must be written in `yyyy-mm-dd` form.
      Fields with date and time use the RFC3339 time format. Leading zeros are
      required for one-digit months and days. The valid relational operators
      for date fields are the equality operator (`=`) , along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * Multiple field query expressions can be
      combined in one query by adding `AND` or `OR` operators between the
      expressions. If a boolean operator appears within a quoted string, it is
      not treated as special, it's just another part of the character string
      to be matched. You can prepend the `NOT` operator to an expression to
      negate it. Only filtering on labels is supported. For example,
      `labels.key=value`.
    pageSize: Limit on the number of HL7v2 stores to return in a single
      response. If not specified, 100 is used. May not be larger than 1000.
    pageToken: The next_page_token value returned from the previous List
      request, if any.
    parent: Name of the dataset.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesBatchGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesBatchGetRequest
  object.

  Enums:
    ViewValueValuesEnum: Specifies the parts of the Messages resource to
      return in the response. When unspecified, equivalent to BASIC.

  Fields:
    ids: The resource id of the HL7v2 messages to retrieve in the format:
      `{message_id}`, where the full resource name is
      `{parent}/messages/{message_id}` A maximum of 100 messages can be
      retrieved in a batch. All 'ids' have to be under parent.
    parent: Name of the HL7v2 store to retrieve messages from, in the format:
      `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/hl7
      v2Stores/{hl7v2_store_id}`.
    view: Specifies the parts of the Messages resource to return in the
      response. When unspecified, equivalent to BASIC.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Specifies the parts of the Messages resource to return in the
    response. When unspecified, equivalent to BASIC.

    Values:
      MESSAGE_VIEW_UNSPECIFIED: Not specified, equivalent to FULL for
        getMessage, equivalent to BASIC for listMessages.
      RAW_ONLY: Server responses include all the message fields except
        parsed_data, and schematized_data fields.
      PARSED_ONLY: Server responses include all the message fields except data
        and schematized_data fields.
      FULL: Server responses include all the message fields.
      SCHEMATIZED_ONLY: Server responses include all the message fields except
        data and parsed_data fields.
      BASIC: Server responses include only the name field.
    """
    MESSAGE_VIEW_UNSPECIFIED = 0
    RAW_ONLY = 1
    PARSED_ONLY = 2
    FULL = 3
    SCHEMATIZED_ONLY = 4
    BASIC = 5

  ids = _messages.StringField(1, repeated=True)
  parent = _messages.StringField(2, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 3)


class HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesCreateRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesCreateRequest
  object.

  Fields:
    createMessageRequest: A CreateMessageRequest resource to be passed as the
      request body.
    parent: The name of the HL7v2 store this message belongs to.
  """

  createMessageRequest = _messages.MessageField('CreateMessageRequest', 1)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesDeleteRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesDeleteRequest
  object.

  Fields:
    name: The resource name of the HL7v2 message to delete.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesGetRequest
  object.

  Enums:
    ViewValueValuesEnum: Specifies which parts of the Message resource to
      return in the response. When unspecified, equivalent to FULL.

  Fields:
    name: The resource name of the HL7v2 message to retrieve.
    view: Specifies which parts of the Message resource to return in the
      response. When unspecified, equivalent to FULL.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Specifies which parts of the Message resource to return in the
    response. When unspecified, equivalent to FULL.

    Values:
      MESSAGE_VIEW_UNSPECIFIED: Not specified, equivalent to FULL for
        getMessage, equivalent to BASIC for listMessages.
      RAW_ONLY: Server responses include all the message fields except
        parsed_data, and schematized_data fields.
      PARSED_ONLY: Server responses include all the message fields except data
        and schematized_data fields.
      FULL: Server responses include all the message fields.
      SCHEMATIZED_ONLY: Server responses include all the message fields except
        data and parsed_data fields.
      BASIC: Server responses include only the name field.
    """
    MESSAGE_VIEW_UNSPECIFIED = 0
    RAW_ONLY = 1
    PARSED_ONLY = 2
    FULL = 3
    SCHEMATIZED_ONLY = 4
    BASIC = 5

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesIngestRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesIngestRequest
  object.

  Fields:
    ingestMessageRequest: A IngestMessageRequest resource to be passed as the
      request body.
    parent: The name of the HL7v2 store this message belongs to.
  """

  ingestMessageRequest = _messages.MessageField('IngestMessageRequest', 1)
  parent = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesListRequest
  object.

  Enums:
    ViewValueValuesEnum: Specifies the parts of the Message to return in the
      response. When unspecified, equivalent to BASIC. Setting this to
      anything other than BASIC with a `page_size` larger than the default can
      generate a large response, which impacts the performance of this method.

  Fields:
    filter: Restricts messages returned to those matching a filter. The
      following syntax is available: * A string field value can be written as
      text inside quotation marks, for example `"query text"`. The only valid
      relational operation for text fields is equality (`=`), where text is
      searched within the field, rather than having the field be equal to the
      text. For example, `"Comment = great"` returns messages with `great` in
      the comment field. * A number field value can be written as an integer,
      a decimal, or an exponential. The valid relational operators for number
      fields are the equality operator (`=`), along with the less than/greater
      than operators (`<`, `<=`, `>`, `>=`). Note that there is no inequality
      (`!=`) operator. You can prepend the `NOT` operator to an expression to
      negate it. * A date field value must be written in `yyyy-mm-dd` form.
      Fields with date and time use the RFC3339 time format. Leading zeros are
      required for one-digit months and days. The valid relational operators
      for date fields are the equality operator (`=`) , along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * Multiple field query expressions can be
      combined in one query by adding `AND` or `OR` operators between the
      expressions. If a boolean operator appears within a quoted string, it is
      not treated as special, it's just another part of the character string
      to be matched. You can prepend the `NOT` operator to an expression to
      negate it. Fields/functions available for filtering are: *
      `message_type`, from the MSH-9.1 field. For example, `NOT message_type =
      "ADT"`. * `send_date` or `sendDate`, the YYYY-MM-DD date the message was
      sent in the dataset's time_zone, from the MSH-7 segment. For example,
      `send_date < "2017-01-02"`. * `send_time`, the timestamp when the
      message was sent, using the RFC3339 time format for comparisons, from
      the MSH-7 segment. For example, `send_time <
      "2017-01-02T00:00:00-05:00"`. * `create_time`, the timestamp when the
      message was created in the HL7v2 store. Use the RFC3339 time format for
      comparisons. For example, `create_time < "2017-01-02T00:00:00-05:00"`. *
      `send_facility`, the care center that the message came from, from the
      MSH-4 segment. For example, `send_facility = "ABC"`. * `PatientId(value,
      type)`, which matches if the message lists a patient having an ID of the
      given value and type in the PID-2, PID-3, or PID-4 segments. For
      example, `PatientId("123456", "MRN")`. * `labels.x`, a string value of
      the label with key `x` as set using the Message.labels map. For example,
      `labels."priority"="high"`. The operator `:*` can be used to assert the
      existence of a label. For example, `labels."priority":*`.
    orderBy: Orders messages returned by the specified order_by clause.
      Syntax:
      https://cloud.google.com/apis/design/design_patterns#sorting_order
      Fields available for ordering are: * `send_time`
    pageSize: Limit on the number of messages to return in a single response.
      If not specified, 100 is used. May not be larger than 1000.
    pageToken: The next_page_token value returned from the previous List
      request, if any.
    parent: Name of the HL7v2 store to retrieve messages from.
    view: Specifies the parts of the Message to return in the response. When
      unspecified, equivalent to BASIC. Setting this to anything other than
      BASIC with a `page_size` larger than the default can generate a large
      response, which impacts the performance of this method.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Specifies the parts of the Message to return in the response. When
    unspecified, equivalent to BASIC. Setting this to anything other than
    BASIC with a `page_size` larger than the default can generate a large
    response, which impacts the performance of this method.

    Values:
      MESSAGE_VIEW_UNSPECIFIED: Not specified, equivalent to FULL for
        getMessage, equivalent to BASIC for listMessages.
      RAW_ONLY: Server responses include all the message fields except
        parsed_data, and schematized_data fields.
      PARSED_ONLY: Server responses include all the message fields except data
        and schematized_data fields.
      FULL: Server responses include all the message fields.
      SCHEMATIZED_ONLY: Server responses include all the message fields except
        data and parsed_data fields.
      BASIC: Server responses include only the name field.
    """
    MESSAGE_VIEW_UNSPECIFIED = 0
    RAW_ONLY = 1
    PARSED_ONLY = 2
    FULL = 3
    SCHEMATIZED_ONLY = 4
    BASIC = 5

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 6)


class HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesPatchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesPatchRequest
  object.

  Fields:
    message: A Message resource to be passed as the request body.
    name: Resource name of the Message, of the form `projects/{project_id}/loc
      ations/{location_id}/datasets/{dataset_id}/hl7V2Stores/{hl7_v2_store_id}
      /messages/{message_id}`. Assigned by the server.
    updateMask: The update mask applies to the resource. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  message = _messages.MessageField('Message', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class HealthcareProjectsLocationsDatasetsHl7V2StoresPatchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresPatchRequest object.

  Fields:
    hl7V2Store: A Hl7V2Store resource to be passed as the request body.
    name: Resource name of the HL7v2 store, of the form `projects/{project_id}
      /locations/{location_id}/datasets/{dataset_id}/hl7V2Stores/{hl7v2_store_
      id}`.
    updateMask: The update mask applies to the resource. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  hl7V2Store = _messages.MessageField('Hl7V2Store', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class HealthcareProjectsLocationsDatasetsHl7V2StoresSetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsHl7V2StoresSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class HealthcareProjectsLocationsDatasetsHl7V2StoresTestIamPermissionsRequest(_messages.Message):
  r"""A
  HealthcareProjectsLocationsDatasetsHl7V2StoresTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class HealthcareProjectsLocationsDatasetsListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsListRequest object.

  Fields:
    pageSize: The maximum number of items to return. If not specified, 100 is
      used. May not be larger than 1000.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: The name of the project whose datasets should be listed. For
      example, `projects/{project_id}/locations/{location_id}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class HealthcareProjectsLocationsDatasetsOperationsCancelRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class HealthcareProjectsLocationsDatasetsOperationsGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsDatasetsOperationsListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class HealthcareProjectsLocationsDatasetsPatchRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsPatchRequest object.

  Fields:
    dataset: A Dataset resource to be passed as the request body.
    name: Resource name of the dataset, of the form
      `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}`.
    updateMask: The update mask applies to the resource. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask
  """

  dataset = _messages.MessageField('Dataset', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class HealthcareProjectsLocationsDatasetsSetIamPolicyRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class HealthcareProjectsLocationsDatasetsTestIamPermissionsRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsDatasetsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class HealthcareProjectsLocationsGetRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class HealthcareProjectsLocationsListRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class HealthcareProjectsLocationsServicesNlpAnalyzeEntitiesRequest(_messages.Message):
  r"""A HealthcareProjectsLocationsServicesNlpAnalyzeEntitiesRequest object.

  Fields:
    analyzeEntitiesRequest: A AnalyzeEntitiesRequest resource to be passed as
      the request body.
    nlpService: The resource name of the service of the form:
      "projects/{project_id}/locations/{location_id}/services/nlp".
  """

  analyzeEntitiesRequest = _messages.MessageField('AnalyzeEntitiesRequest', 1)
  nlpService = _messages.StringField(2, required=True)


class Hl7SchemaConfig(_messages.Message):
  r"""Root config message for HL7v2 schema. This contains a schema structure
  of groups and segments, and filters that determine which messages to apply
  the schema structure to.

  Messages:
    MessageSchemaConfigsValue: Map from each HL7v2 message type and trigger
      event pair, such as ADT_A04, to its schema configuration root group.

  Fields:
    messageSchemaConfigs: Map from each HL7v2 message type and trigger event
      pair, such as ADT_A04, to its schema configuration root group.
    version: Each VersionSource is tested and only if they all match is the
      schema used for the message.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MessageSchemaConfigsValue(_messages.Message):
    r"""Map from each HL7v2 message type and trigger event pair, such as
    ADT_A04, to its schema configuration root group.

    Messages:
      AdditionalProperty: An additional property for a
        MessageSchemaConfigsValue object.

    Fields:
      additionalProperties: Additional properties of type
        MessageSchemaConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MessageSchemaConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A SchemaGroup attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('SchemaGroup', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  messageSchemaConfigs = _messages.MessageField('MessageSchemaConfigsValue', 1)
  version = _messages.MessageField('VersionSource', 2, repeated=True)


class Hl7TypesConfig(_messages.Message):
  r"""Root config for HL7v2 datatype definitions for a specific HL7v2 version.

  Fields:
    type: The HL7v2 type definitions.
    version: The version selectors that this config applies to. A message must
      match ALL version sources to apply.
  """

  type = _messages.MessageField('Type', 1, repeated=True)
  version = _messages.MessageField('VersionSource', 2, repeated=True)


class Hl7V2NotificationConfig(_messages.Message):
  r"""Specifies where and whether to send notifications upon changes to a data
  store.

  Fields:
    filter: Restricts notifications sent for messages matching a filter. If
      this is empty, all messages are matched. The following syntax is
      available: * A string field value can be written as text inside
      quotation marks, for example `"query text"`. The only valid relational
      operation for text fields is equality (`=`), where text is searched
      within the field, rather than having the field be equal to the text. For
      example, `"Comment = great"` returns messages with `great` in the
      comment field. * A number field value can be written as an integer, a
      decimal, or an exponential. The valid relational operators for number
      fields are the equality operator (`=`), along with the less than/greater
      than operators (`<`, `<=`, `>`, `>=`). Note that there is no inequality
      (`!=`) operator. You can prepend the `NOT` operator to an expression to
      negate it. * A date field value must be written in `yyyy-mm-dd` form.
      Fields with date and time use the RFC3339 time format. Leading zeros are
      required for one-digit months and days. The valid relational operators
      for date fields are the equality operator (`=`) , along with the less
      than/greater than operators (`<`, `<=`, `>`, `>=`). Note that there is
      no inequality (`!=`) operator. You can prepend the `NOT` operator to an
      expression to negate it. * Multiple field query expressions can be
      combined in one query by adding `AND` or `OR` operators between the
      expressions. If a boolean operator appears within a quoted string, it is
      not treated as special, it's just another part of the character string
      to be matched. You can prepend the `NOT` operator to an expression to
      negate it. Fields/functions available for filtering are: *
      `message_type`, from the MSH-9.1 field. For example, `NOT message_type =
      "ADT"`. * `send_date` or `sendDate`, the YYYY-MM-DD date the message was
      sent in the dataset's time_zone, from the MSH-7 segment. For example,
      `send_date < "2017-01-02"`. * `send_time`, the timestamp when the
      message was sent, using the RFC3339 time format for comparisons, from
      the MSH-7 segment. For example, `send_time <
      "2017-01-02T00:00:00-05:00"`. * `create_time`, the timestamp when the
      message was created in the HL7v2 store. Use the RFC3339 time format for
      comparisons. For example, `create_time < "2017-01-02T00:00:00-05:00"`. *
      `send_facility`, the care center that the message came from, from the
      MSH-4 segment. For example, `send_facility = "ABC"`. * `PatientId(value,
      type)`, which matches if the message lists a patient having an ID of the
      given value and type in the PID-2, PID-3, or PID-4 segments. For
      example, `PatientId("123456", "MRN")`. * `labels.x`, a string value of
      the label with key `x` as set using the Message.labels map. For example,
      `labels."priority"="high"`. The operator `:*` can be used to assert the
      existence of a label. For example, `labels."priority":*`.
    pubsubTopic: The [Pub/Sub](https://cloud.google.com/pubsub/docs/) topic
      that notifications of changes are published on. Supplied by the client.
      The notification is a `PubsubMessage` with the following fields: *
      `PubsubMessage.Data` contains the resource name. *
      `PubsubMessage.MessageId` is the ID of this notification. It is
      guaranteed to be unique within the topic. * `PubsubMessage.PublishTime`
      is the time when the message was published. Note that notifications are
      only sent if the topic is non-empty. [Topic
      names](https://cloud.google.com/pubsub/docs/overview#names) must be
      scoped to a project. Cloud Healthcare API service account must have
      publisher permissions on the given Pub/Sub topic. Not having adequate
      permissions causes the calls that send notifications to fail. If a
      notification can't be published to Pub/Sub, errors are logged to Cloud
      Logging. For more information, see [Viewing error logs in Cloud
      Logging](https://cloud.google.com/healthcare/docs/how-tos/logging).
  """

  filter = _messages.StringField(1)
  pubsubTopic = _messages.StringField(2)


class Hl7V2Store(_messages.Message):
  r"""Represents an HL7v2 store.

  Messages:
    LabelsValue: User-supplied key-value pairs used to organize HL7v2 stores.
      Label keys must be between 1 and 63 characters long, have a UTF-8
      encoding of maximum 128 bytes, and must conform to the following PCRE
      regular expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must
      be between 1 and 63 characters long, have a UTF-8 encoding of maximum
      128 bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.

  Fields:
    labels: User-supplied key-value pairs used to organize HL7v2 stores. Label
      keys must be between 1 and 63 characters long, have a UTF-8 encoding of
      maximum 128 bytes, and must conform to the following PCRE regular
      expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must be
      between 1 and 63 characters long, have a UTF-8 encoding of maximum 128
      bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.
    name: Resource name of the HL7v2 store, of the form `projects/{project_id}
      /locations/{location_id}/datasets/{dataset_id}/hl7V2Stores/{hl7v2_store_
      id}`.
    notificationConfig: The notification destination all messages (both Ingest
      & Create) are published on. Only the message name is sent as part of the
      notification. If this is unset, no notifications are sent. Supplied by
      the client.
    notificationConfigs: A list of notification configs. Each configuration
      uses a filter to determine whether to publish a message (both Ingest &
      Create) on the corresponding notification destination. Only the message
      name is sent as part of the notification. Supplied by the client.
    parserConfig: The configuration for the parser. It determines how the
      server parses the messages.
    rejectDuplicateMessage: Determines whether to reject duplicate messages. A
      duplicate message is a message with the same raw bytes as a message that
      has already been ingested/created in this HL7v2 store. The default value
      is false, meaning that the store accepts the duplicate messages and it
      also returns the same ACK message in the IngestMessageResponse as has
      been returned previously. Note that only one resource is created in the
      store. When this field is set to true, CreateMessage/IngestMessage
      requests with a duplicate message will be rejected by the store, and
      IngestMessageErrorDetail returns a NACK message upon rejection.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-supplied key-value pairs used to organize HL7v2 stores. Label
    keys must be between 1 and 63 characters long, have a UTF-8 encoding of
    maximum 128 bytes, and must conform to the following PCRE regular
    expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must be between
    1 and 63 characters long, have a UTF-8 encoding of maximum 128 bytes, and
    must conform to the following PCRE regular expression:
    [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated with
    a given store.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  name = _messages.StringField(2)
  notificationConfig = _messages.MessageField('NotificationConfig', 3)
  notificationConfigs = _messages.MessageField('Hl7V2NotificationConfig', 4, repeated=True)
  parserConfig = _messages.MessageField('ParserConfig', 5)
  rejectDuplicateMessage = _messages.BooleanField(6)


class HttpBody(_messages.Message):
  r"""Message that represents an arbitrary HTTP body. It should only be used
  for payload formats that can't be represented as JSON, such as raw binary or
  an HTML page. This message can be used both in streaming and non-streaming
  API methods in the request as well as the response. It can be used as a top-
  level request field, which is convenient if one wants to extract parameters
  from either the URL or HTTP template into the request fields and also want
  access to the raw HTTP body. Example: message GetResourceRequest { // A
  unique request id. string request_id = 1; // The raw HTTP body is bound to
  this field. google.api.HttpBody http_body = 2; } service ResourceService {
  rpc GetResource(GetResourceRequest) returns (google.api.HttpBody); rpc
  UpdateResource(google.api.HttpBody) returns (google.protobuf.Empty); }
  Example with streaming methods: service CaldavService { rpc
  GetCalendar(stream google.api.HttpBody) returns (stream
  google.api.HttpBody); rpc UpdateCalendar(stream google.api.HttpBody) returns
  (stream google.api.HttpBody); } Use of this type only changes how the
  request and response bodies are handled, all other features will continue to
  work unchanged.

  Messages:
    ExtensionsValueListEntry: A ExtensionsValueListEntry object.

  Fields:
    contentType: The HTTP Content-Type header value specifying the content
      type of the body.
    data: The HTTP request/response body as raw binary.
    extensions: Application specific response metadata. Must be set in the
      first response for streaming APIs.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExtensionsValueListEntry(_messages.Message):
    r"""A ExtensionsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a
        ExtensionsValueListEntry object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExtensionsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  contentType = _messages.StringField(1)
  data = _messages.BytesField(2)
  extensions = _messages.MessageField('ExtensionsValueListEntry', 3, repeated=True)


class Image(_messages.Message):
  r"""Raw bytes representing consent artifact content.

  Fields:
    gcsUri: Input only. Points to a Cloud Storage URI containing the consent
      artifact content. The URI must be in the following format:
      `gs://{bucket_id}/{object_id}`. The Cloud Healthcare API service account
      must have the `roles/storage.objectViewer` Cloud IAM role for this Cloud
      Storage location. The consent artifact content at this URI is copied to
      a Cloud Storage location managed by the Cloud Healthcare API. Responses
      to fetching requests return the consent artifact content in raw_bytes.
    rawBytes: Consent artifact content represented as a stream of bytes. This
      field is populated when returned in GetConsentArtifact response, but not
      included in CreateConsentArtifact and ListConsentArtifact response.
  """

  gcsUri = _messages.StringField(1)
  rawBytes = _messages.BytesField(2)


class ImageAnnotation(_messages.Message):
  r"""Image annotation.

  Fields:
    boundingPolys: The list of polygons outlining the sensitive regions in the
      image.
    frameIndex: 0-based index of the image frame. For example, an image frame
      in a DICOM instance.
  """

  boundingPolys = _messages.MessageField('BoundingPoly', 1, repeated=True)
  frameIndex = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class ImageConfig(_messages.Message):
  r"""Specifies how to handle de-identification of image pixels.

  Enums:
    TextRedactionModeValueValuesEnum: Determines how to redact text from
      image.

  Fields:
    additionalInfoTypes: Additional InfoTypes to redact in the images in
      addition to those used by `text_redaction_mode`. Can only be used when
      `text_redaction_mode` is set to `REDACT_SENSITIVE_TEXT`,
      `REDACT_SENSITIVE_TEXT_CLEAN_DESCRIPTORS` or
      `TEXT_REDACTION_MODE_UNSPECIFIED`.
    excludeInfoTypes: InfoTypes to skip redacting, overriding those used by
      `text_redaction_mode`. Can only be used when `text_redaction_mode` is
      set to `REDACT_SENSITIVE_TEXT` or
      `REDACT_SENSITIVE_TEXT_CLEAN_DESCRIPTORS`.
    textRedactionMode: Determines how to redact text from image.
  """

  class TextRedactionModeValueValuesEnum(_messages.Enum):
    r"""Determines how to redact text from image.

    Values:
      TEXT_REDACTION_MODE_UNSPECIFIED: No text redaction specified. Same as
        REDACT_NO_TEXT.
      REDACT_ALL_TEXT: Redact all text.
      REDACT_SENSITIVE_TEXT: Redact sensitive text. Uses the set of [Default
        DICOM InfoTypes](https://cloud.google.com/healthcare-api/docs/how-
        tos/dicom-deidentify#default_dicom_infotypes).
      REDACT_NO_TEXT: Do not redact text.
      REDACT_SENSITIVE_TEXT_CLEAN_DESCRIPTORS: This mode is like
        `REDACT_SENSITIVE_TEXT` with the addition of the [Clean Descriptors
        Option] (https://dicom.nema.org/medical/dicom/2018e/output/chtml/part1
        5/sect_E.3.5.html) enabled: When cleaning text, the process attempts
        to transform phrases matching any of the tags marked for removal
        (action codes D, Z, X, and U) in the [Basic Profile] (https://dicom.ne
        ma.org/medical/dicom/2018e/output/chtml/part15/chapter_E.html). These
        contextual phrases are replaced with the token "[CTX]". This mode uses
        an additional InfoType during inspection.
    """
    TEXT_REDACTION_MODE_UNSPECIFIED = 0
    REDACT_ALL_TEXT = 1
    REDACT_SENSITIVE_TEXT = 2
    REDACT_NO_TEXT = 3
    REDACT_SENSITIVE_TEXT_CLEAN_DESCRIPTORS = 4

  additionalInfoTypes = _messages.StringField(1, repeated=True)
  excludeInfoTypes = _messages.StringField(2, repeated=True)
  textRedactionMode = _messages.EnumField('TextRedactionModeValueValuesEnum', 3)


class ImportAnnotationsRequest(_messages.Message):
  r"""Request to import Annotations. The Annotations to be imported must have
  client-supplied resource names which indicate the annotation resource. The
  import operation is not atomic. If a failure occurs, any annotations already
  imported are not removed.

  Fields:
    gcsSource: A GoogleCloudHealthcareV1beta1AnnotationGcsSource attribute.
  """

  gcsSource = _messages.MessageField('GoogleCloudHealthcareV1beta1AnnotationGcsSource', 1)


class ImportAnnotationsResponse(_messages.Message):
  r"""Final response of importing Annotations in successful case. This
  structure is included in the response. It is only included when the
  operation finishes.
  """



class ImportDicomDataRequest(_messages.Message):
  r"""Imports data into the specified DICOM store. Returns an error if any of
  the files to import are not DICOM files. This API accepts duplicate DICOM
  instances by ignoring the newly-pushed instance. It does not overwrite.

  Fields:
    gcsSource: Cloud Storage source data location and import configuration.
      The Cloud Healthcare Service Agent requires the
      `roles/storage.objectViewer` Cloud IAM roles on the Cloud Storage
      location.
  """

  gcsSource = _messages.MessageField('GoogleCloudHealthcareV1beta1DicomGcsSource', 1)


class ImportDicomDataResponse(_messages.Message):
  r"""Returns additional information in regards to a completed DICOM store
  import.
  """



class ImportMessagesRequest(_messages.Message):
  r"""Request to import messages.

  Fields:
    gcsSource: Cloud Storage source data location and import configuration.
      The Cloud Healthcare Service Agent requires the
      `roles/storage.objectViewer` Cloud IAM roles on the Cloud Storage
      location.
  """

  gcsSource = _messages.MessageField('GcsSource', 1)


class ImportMessagesResponse(_messages.Message):
  r"""Final response of importing messages. This structure is included in the
  response to describe the detailed outcome. It is only included when the
  operation finishes successfully.
  """



class ImportResourcesRequest(_messages.Message):
  r"""Request to import resources.

  Enums:
    ContentStructureValueValuesEnum: The content structure in the source
      location. If not specified, the server treats the input source files as
      BUNDLE.

  Fields:
    contentStructure: The content structure in the source location. If not
      specified, the server treats the input source files as BUNDLE.
    gcsSource: Cloud Storage source data location and import configuration.
      The Cloud Healthcare Service Agent requires the
      `roles/storage.objectViewer` Cloud IAM roles on the Cloud Storage
      location. The Healthcare Service Agent Each Cloud Storage object should
      be a text file that contains the format specified in ContentStructure.
  """

  class ContentStructureValueValuesEnum(_messages.Enum):
    r"""The content structure in the source location. If not specified, the
    server treats the input source files as BUNDLE.

    Values:
      CONTENT_STRUCTURE_UNSPECIFIED: If the content structure is not
        specified, the default value `BUNDLE` is used.
      BUNDLE: The source file contains one or more lines of newline-delimited
        JSON (ndjson). Each line is a bundle that contains one or more
        resources.
      RESOURCE: The source file contains one or more lines of newline-
        delimited JSON (ndjson). Each line is a single resource.
      BUNDLE_PRETTY: The entire file is one JSON bundle. The JSON can span
        multiple lines.
      RESOURCE_PRETTY: The entire file is one JSON resource. The JSON can span
        multiple lines.
    """
    CONTENT_STRUCTURE_UNSPECIFIED = 0
    BUNDLE = 1
    RESOURCE = 2
    BUNDLE_PRETTY = 3
    RESOURCE_PRETTY = 4

  contentStructure = _messages.EnumField('ContentStructureValueValuesEnum', 1)
  gcsSource = _messages.MessageField('GoogleCloudHealthcareV1beta1FhirGcsSource', 2)


class InfoTypeConfig(_messages.Message):
  r"""Specifies how to use infoTypes for evaluation. For example, a user might
  only want to evaluate `PERSON`, `LOCATION`, and `AGE`.

  Fields:
    evaluateList: A FilterList attribute.
    ignoreList: A FilterList attribute.
    strictMatching: If `TRUE`, infoTypes described by `filter` are used for
      evaluation. Otherwise, infoTypes are not considered for evaluation. For
      example: * Annotated text: "Toronto is a location" * Finding 1:
      `{"infoType": "PERSON", "quote": "Toronto", "start": 0, "end": 7}` *
      Finding 2: `{"infoType": "CITY", "quote": "Toronto", "start": 0, "end":
      7}` * Finding 3: `{}` * Ground truth: `{"infoType": "LOCATION", "quote":
      "Toronto", "start": 0, "end": 7}` When `strict_matching` is `TRUE`: *
      Finding 1: 1 false positive * Finding 2: 1 false positive * Finding 3: 1
      false negative When `strict_matching` is `FALSE`: * Finding 1: 1 true
      positive * Finding 2: 1 true positive * Finding 3: 1 false negative
  """

  evaluateList = _messages.MessageField('FilterList', 1)
  ignoreList = _messages.MessageField('FilterList', 2)
  strictMatching = _messages.BooleanField(3)


class InfoTypeTransformation(_messages.Message):
  r"""A transformation to apply to text that is identified as a specific
  info_type.

  Fields:
    characterMaskConfig: Config for character mask.
    cryptoHashConfig: Config for crypto hash.
    dateShiftConfig: Config for date shift.
    infoTypes: `InfoTypes` to apply this transformation to. If this is not
      specified, this transformation becomes the default transformation, and
      is used for any `info_type` that is not specified in another
      transformation.
    redactConfig: Config for text redaction.
    replaceWithInfoTypeConfig: Config for replace with InfoType.
  """

  characterMaskConfig = _messages.MessageField('CharacterMaskConfig', 1)
  cryptoHashConfig = _messages.MessageField('CryptoHashConfig', 2)
  dateShiftConfig = _messages.MessageField('DateShiftConfig', 3)
  infoTypes = _messages.StringField(4, repeated=True)
  redactConfig = _messages.MessageField('RedactConfig', 5)
  replaceWithInfoTypeConfig = _messages.MessageField('ReplaceWithInfoTypeConfig', 6)


class IngestMessageRequest(_messages.Message):
  r"""Ingests a message into the specified HL7v2 store.

  Fields:
    message: HL7v2 message to ingest.
  """

  message = _messages.MessageField('Message', 1)


class IngestMessageResponse(_messages.Message):
  r"""Acknowledges that a message has been ingested into the specified HL7v2
  store.

  Fields:
    hl7Ack: HL7v2 ACK message.
    message: Created message resource.
  """

  hl7Ack = _messages.BytesField(1)
  message = _messages.MessageField('Message', 2)


class KeepExtensionsConfig(_messages.Message):
  r"""The behaviour for handling FHIR extensions that aren't otherwise
  specified for de-identification. If provided, all extensions are preserved
  during de-identification by default. If unspecified, all extensions are
  removed during de-identification by default.
  """



class KeepField(_messages.Message):
  r"""Keep field unchanged."""


class KeepTag(_messages.Message):
  r"""Keep tag unchanged."""


class KmsWrappedCryptoKey(_messages.Message):
  r"""Include to use an existing data crypto key wrapped by KMS. The wrapped
  key must be a 128-, 192-, or 256-bit key. The key must grant the Cloud IAM
  permission `cloudkms.cryptoKeyVersions.useToDecrypt` to the project's Cloud
  Healthcare Service Agent service account. For more information, see
  [Creating a wrapped key] (https://cloud.google.com/dlp/docs/create-wrapped-
  key).

  Fields:
    cryptoKey: Required. The resource name of the KMS CryptoKey to use for
      unwrapping. For example, `projects/{project_id}/locations/{location_id}/
      keyRings/{keyring}/cryptoKeys/{key}`.
    wrappedKey: Required. The wrapped data crypto key.
  """

  cryptoKey = _messages.StringField(1)
  wrappedKey = _messages.BytesField(2)


class LinkedEntity(_messages.Message):
  r"""EntityMentions can be linked to multiple entities using a LinkedEntity
  message lets us add other fields, e.g. confidence.

  Fields:
    entityId: entity_id is a concept unique identifier. These are prefixed by
      a string that identifies the entity coding system, followed by the
      unique identifier within that system. For example, "UMLS/********". This
      also supports ad hoc entities, which are formed by normalizing entity
      mention content.
  """

  entityId = _messages.StringField(1)


class ListAnnotationStoresResponse(_messages.Message):
  r"""Lists the Annotation stores in the given dataset.

  Fields:
    annotationStores: The returned Annotation stores. Won't be more Annotation
      stores than the value of page_size in the request.
    nextPageToken: Token to retrieve the next page of results or empty if
      there are no more results in the list.
  """

  annotationStores = _messages.MessageField('AnnotationStore', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListAnnotationsResponse(_messages.Message):
  r"""Lists the Annotations in the specified Annotation store.

  Fields:
    annotations: The returned Annotations. Won't be more values than the value
      of page_size in the request. See `AnnotationView` in the request for
      populated fields.
    nextPageToken: Token to retrieve the next page of results or empty if
      there are no more results in the list.
  """

  annotations = _messages.MessageField('Annotation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListAttributeDefinitionsResponse(_messages.Message):
  r"""A ListAttributeDefinitionsResponse object.

  Fields:
    attributeDefinitions: The returned Attribute definitions. The maximum
      number of attributes returned is determined by the value of page_size in
      the ListAttributeDefinitionsRequest.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  attributeDefinitions = _messages.MessageField('AttributeDefinition', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListConsentArtifactsResponse(_messages.Message):
  r"""A ListConsentArtifactsResponse object.

  Fields:
    consentArtifacts: The returned Consent artifacts. The maximum number of
      artifacts returned is determined by the value of page_size in the
      ListConsentArtifactsRequest.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  consentArtifacts = _messages.MessageField('ConsentArtifact', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListConsentRevisionsResponse(_messages.Message):
  r"""A ListConsentRevisionsResponse object.

  Fields:
    consents: The returned Consent revisions. The maximum number of revisions
      returned is determined by the value of `page_size` in the
      ListConsentRevisionsRequest.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  consents = _messages.MessageField('Consent', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListConsentStoresResponse(_messages.Message):
  r"""A ListConsentStoresResponse object.

  Fields:
    consentStores: The returned consent stores. The maximum number of stores
      returned is determined by the value of page_size in the
      ListConsentStoresRequest.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  consentStores = _messages.MessageField('ConsentStore', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListConsentsResponse(_messages.Message):
  r"""A ListConsentsResponse object.

  Fields:
    consents: The returned Consents. The maximum number of Consents returned
      is determined by the value of page_size in the ListConsentsRequest.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  consents = _messages.MessageField('Consent', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDatasetsResponse(_messages.Message):
  r"""Lists the available datasets.

  Fields:
    datasets: The first page of datasets.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  datasets = _messages.MessageField('Dataset', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDicomStoresResponse(_messages.Message):
  r"""Lists the DICOM stores in the given dataset.

  Fields:
    dicomStores: The returned DICOM stores. Won't be more DICOM stores than
      the value of page_size in the request.
    nextPageToken: Token to retrieve the next page of results or empty if
      there are no more results in the list.
  """

  dicomStores = _messages.MessageField('DicomStore', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListFhirStoresResponse(_messages.Message):
  r"""Lists the FHIR stores in the given dataset.

  Fields:
    fhirStores: The returned FHIR stores. Won't be more FHIR stores than the
      value of page_size in the request.
    nextPageToken: Token to retrieve the next page of results or empty if
      there are no more results in the list.
  """

  fhirStores = _messages.MessageField('FhirStore', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListHl7V2StoresResponse(_messages.Message):
  r"""Lists the HL7v2 stores in the given dataset.

  Fields:
    hl7V2Stores: The returned HL7v2 stores. Won't be more HL7v2 stores than
      the value of page_size in the request.
    nextPageToken: Token to retrieve the next page of results or empty if
      there are no more results in the list.
  """

  hl7V2Stores = _messages.MessageField('Hl7V2Store', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMessagesResponse(_messages.Message):
  r"""Lists the messages in the specified HL7v2 store.

  Fields:
    hl7V2Messages: The returned Messages. Won't be more Messages than the
      value of page_size in the request. See view for populated fields.
    nextPageToken: Token to retrieve the next page of results or empty if
      there are no more results in the list.
  """

  hl7V2Messages = _messages.MessageField('Message', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListUserDataMappingsResponse(_messages.Message):
  r"""A ListUserDataMappingsResponse object.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    userDataMappings: The returned User data mappings. The maximum number of
      User data mappings returned is determined by the value of page_size in
      the ListUserDataMappingsRequest.
  """

  nextPageToken = _messages.StringField(1)
  userDataMappings = _messages.MessageField('UserDataMapping', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Message(_messages.Message):
  r"""A complete HL7v2 message. See [Introduction to HL7 Standards]
  (https://www.hl7.org/implement/standards/index.cfm?ref=common) for details
  on the standard.

  Messages:
    LabelsValue: User-supplied key-value pairs used to organize HL7v2 stores.
      Label keys must be between 1 and 63 characters long, have a UTF-8
      encoding of maximum 128 bytes, and must conform to the following PCRE
      regular expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must
      be between 1 and 63 characters long, have a UTF-8 encoding of maximum
      128 bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.

  Fields:
    createTime: Output only. The datetime when the message was created. Set by
      the server.
    data: Raw message bytes.
    labels: User-supplied key-value pairs used to organize HL7v2 stores. Label
      keys must be between 1 and 63 characters long, have a UTF-8 encoding of
      maximum 128 bytes, and must conform to the following PCRE regular
      expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must be
      between 1 and 63 characters long, have a UTF-8 encoding of maximum 128
      bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.
    messageType: The message type for this message. MSH-9.1.
    name: Resource name of the Message, of the form `projects/{project_id}/loc
      ations/{location_id}/datasets/{dataset_id}/hl7V2Stores/{hl7_v2_store_id}
      /messages/{message_id}`. Assigned by the server.
    parsedData: Output only. The parsed version of the raw message data.
    patientIds: All patient IDs listed in the PID-2, PID-3, and PID-4 segments
      of this message.
    schematizedData: The parsed version of the raw message data schematized
      according to this store's schemas and type definitions.
    sendFacility: The hospital that this message came from. MSH-4.
    sendTime: The datetime the sending application sent this message. MSH-7.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-supplied key-value pairs used to organize HL7v2 stores. Label
    keys must be between 1 and 63 characters long, have a UTF-8 encoding of
    maximum 128 bytes, and must conform to the following PCRE regular
    expression: \p{Ll}\p{Lo}{0,62} Label values are optional, must be between
    1 and 63 characters long, have a UTF-8 encoding of maximum 128 bytes, and
    must conform to the following PCRE regular expression:
    [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated with
    a given store.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  data = _messages.BytesField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  messageType = _messages.StringField(4)
  name = _messages.StringField(5)
  parsedData = _messages.MessageField('ParsedData', 6)
  patientIds = _messages.MessageField('PatientId', 7, repeated=True)
  schematizedData = _messages.MessageField('SchematizedData', 8)
  sendFacility = _messages.StringField(9)
  sendTime = _messages.StringField(10)


class NotificationConfig(_messages.Message):
  r"""Specifies where to send notifications upon changes to a data store.

  Fields:
    pubsubTopic: The [Pub/Sub](https://cloud.google.com/pubsub/docs/) topic
      that notifications of changes are published on. Supplied by the client.
      PubsubMessage.Data contains the resource name. PubsubMessage.MessageId
      is the ID of this message. It is guaranteed to be unique within the
      topic. PubsubMessage.PublishTime is the time at which the message was
      published. Notifications are only sent if the topic is non-empty. [Topic
      names](https://cloud.google.com/pubsub/docs/overview#names) must be
      scoped to a project. Cloud Healthcare API service account must have
      publisher permissions on the given Pub/Sub topic. Not having adequate
      permissions causes the calls that send notifications to fail. If a
      notification can't be published to Pub/Sub, errors are logged to Cloud
      Logging (see [Viewing error logs in Cloud
      Logging](https://cloud.google.com/healthcare/docs/how-tos/logging)). If
      the number of errors exceeds a certain rate, some aren't submitted. Note
      that not all operations trigger notifications, see [Configuring Pub/Sub
      notifications](https://cloud.google.com/healthcare/docs/how-tos/pubsub)
      for specific details.
    sendForBulkImport: Indicates whether or not to send Pub/Sub notifications
      on bulk import. Only supported for DICOM imports.
  """

  pubsubTopic = _messages.StringField(1)
  sendForBulkImport = _messages.BooleanField(2)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""OperationMetadata provides information about the operation execution.
  Returned in the long-running operation's metadata field.

  Fields:
    apiMethodName: The name of the API method that initiated the operation.
    cancelRequested: Specifies if cancellation was requested for the
      operation.
    counter: A ProgressCounter attribute.
    createTime: The time at which the operation was created by the API.
    endTime: The time at which execution was completed.
    logsUrl: A link to audit and error logs in the log viewer. Error logs are
      generated only by some operations, listed at [Viewing error logs in
      Cloud Logging](https://cloud.google.com/healthcare/docs/how-
      tos/logging).
  """

  apiMethodName = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  counter = _messages.MessageField('ProgressCounter', 3)
  createTime = _messages.StringField(4)
  endTime = _messages.StringField(5)
  logsUrl = _messages.StringField(6)


class Options(_messages.Message):
  r"""Specifies additional options to apply to the base profile.

  Enums:
    PrimaryIdsValueValuesEnum: Set `Action` for [`StudyInstanceUID`,
      `SeriesInstanceUID`, `SOPInstanceUID`, and `MediaStorageSOPInstanceUID`]
      (http://dicom.nema.org/medical/dicom/2018e/output/chtml/part06/chapter_6
      .html).

  Fields:
    cleanDescriptors: Set Clean Descriptors Option.
    cleanImage: Apply `Action.clean_image` to [`PixelData`](http://dicom.nema.
      org/medical/dicom/2018e/output/chtml/part06/chapter_6.html) as
      configured.
    primaryIds: Set `Action` for [`StudyInstanceUID`, `SeriesInstanceUID`,
      `SOPInstanceUID`, and `MediaStorageSOPInstanceUID`](http://dicom.nema.or
      g/medical/dicom/2018e/output/chtml/part06/chapter_6.html).
  """

  class PrimaryIdsValueValuesEnum(_messages.Enum):
    r"""Set `Action` for [`StudyInstanceUID`, `SeriesInstanceUID`,
    `SOPInstanceUID`, and `MediaStorageSOPInstanceUID`](http://dicom.nema.org/
    medical/dicom/2018e/output/chtml/part06/chapter_6.html).

    Values:
      PRIMARY_IDS_OPTION_UNSPECIFIED: No value provided. Default to the
        behavior specified by the base profile.
      KEEP: Keep primary IDs.
      REGEN: Regenerate primary IDs.
    """
    PRIMARY_IDS_OPTION_UNSPECIFIED = 0
    KEEP = 1
    REGEN = 2

  cleanDescriptors = _messages.MessageField('CleanDescriptorsOption', 1)
  cleanImage = _messages.MessageField('ImageConfig', 2)
  primaryIds = _messages.EnumField('PrimaryIdsValueValuesEnum', 3)


class ParsedData(_messages.Message):
  r"""The content of an HL7v2 message in a structured format.

  Fields:
    segments: A Segment attribute.
  """

  segments = _messages.MessageField('Segment', 1, repeated=True)


class ParserConfig(_messages.Message):
  r"""The configuration for the parser. It determines how the server parses
  the messages.

  Enums:
    VersionValueValuesEnum: Immutable. Determines the version of both the
      default parser to be used when `schema` is not given, as well as the
      schematized parser used when `schema` is specified. This field is
      immutable after HL7v2 store creation.

  Fields:
    allowNullHeader: Determines whether messages with no header are allowed.
    schema: Schemas used to parse messages in this store, if schematized
      parsing is desired.
    segmentTerminator: Byte(s) to use as the segment terminator. If this is
      unset, '\r' is used as segment terminator, matching the HL7 version 2
      specification.
    version: Immutable. Determines the version of both the default parser to
      be used when `schema` is not given, as well as the schematized parser
      used when `schema` is specified. This field is immutable after HL7v2
      store creation.
  """

  class VersionValueValuesEnum(_messages.Enum):
    r"""Immutable. Determines the version of both the default parser to be
    used when `schema` is not given, as well as the schematized parser used
    when `schema` is specified. This field is immutable after HL7v2 store
    creation.

    Values:
      PARSER_VERSION_UNSPECIFIED: Unspecified parser version, equivalent to
        V1.
      V1: The `parsed_data` includes every given non-empty message field
        except the Field Separator (MSH-1) field. As a result, the parsed MSH
        segment starts with the MSH-2 field and the field numbers are off-by-
        one with respect to the HL7 standard.
      V2: The `parsed_data` includes every given non-empty message field.
      V3: This version is the same as V2, with the following change. The
        `parsed_data` contains unescaped escaped field separators, component
        separators, sub-component separators, repetition separators, escape
        characters, and truncation characters. If `schema` is specified, the
        schematized parser uses improved parsing heuristics compared to
        previous versions.
    """
    PARSER_VERSION_UNSPECIFIED = 0
    V1 = 1
    V2 = 2
    V3 = 3

  allowNullHeader = _messages.BooleanField(1)
  schema = _messages.MessageField('SchemaPackage', 2)
  segmentTerminator = _messages.BytesField(3)
  version = _messages.EnumField('VersionValueValuesEnum', 4)


class PatientId(_messages.Message):
  r"""A patient identifier and associated type.

  Fields:
    type: ID type. For example, MRN or NHS.
    value: The patient's unique identifier.
  """

  type = _messages.StringField(1)
  value = _messages.StringField(2)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class ProgressCounter(_messages.Message):
  r"""ProgressCounter provides counters to describe an operation's progress.

  Fields:
    failure: The number of units that failed in the operation.
    pending: The number of units that are pending in the operation.
    success: The number of units that succeeded in the operation.
  """

  failure = _messages.IntegerField(1)
  pending = _messages.IntegerField(2)
  success = _messages.IntegerField(3)


class PubsubDestination(_messages.Message):
  r"""The Pub/Sub output destination. The Cloud Healthcare Service Agent
  requires the `roles/pubsub.publisher` Cloud IAM role on the Pub/Sub topic.

  Fields:
    pubsubTopic: The [Pub/Sub](https://cloud.google.com/pubsub/docs/) topic
      that Pub/Sub messages are published on. Supplied by the client. The
      `PubsubMessage` contains the following fields: * `PubsubMessage.Data`
      contains the resource name. * `PubsubMessage.MessageId` is the ID of
      this notification. It is guaranteed to be unique within the topic. *
      `PubsubMessage.PublishTime` is the time when the message was published.
      [Topic names](https://cloud.google.com/pubsub/docs/overview#names) must
      be scoped to a project. The Cloud Healthcare API service account,
      <EMAIL>, must
      have publisher permissions on the given Pub/Sub topic. Not having
      adequate permissions causes the calls that send notifications to fail.
  """

  pubsubTopic = _messages.StringField(1)


class QueryAccessibleDataRequest(_messages.Message):
  r"""Queries all data_ids that are consented for a given use in the given
  consent store and writes them to a specified destination. The returned
  Operation includes a progress counter for the number of User data mappings
  processed. Errors are logged to Cloud Logging (see [Viewing error logs in
  Cloud Logging] (https://cloud.google.com/healthcare/docs/how-tos/logging)
  and [QueryAccessibleData] for a sample log entry).

  Messages:
    RequestAttributesValue: The values of request attributes associated with
      this access request.
    ResourceAttributesValue: Optional. The values of resource attributes
      associated with the type of resources being requested. If no values are
      specified, then all resource types are included in the output.

  Fields:
    gcsDestination: The Cloud Storage destination. The Cloud Healthcare API
      service account must have the `roles/storage.objectAdmin` Cloud IAM role
      for this Cloud Storage location. The object name is in the following
      format: query-accessible-data-result-{operation_id}.txt where each line
      contains a single data_id.
    requestAttributes: The values of request attributes associated with this
      access request.
    resourceAttributes: Optional. The values of resource attributes associated
      with the type of resources being requested. If no values are specified,
      then all resource types are included in the output.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RequestAttributesValue(_messages.Message):
    r"""The values of request attributes associated with this access request.

    Messages:
      AdditionalProperty: An additional property for a RequestAttributesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        RequestAttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RequestAttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceAttributesValue(_messages.Message):
    r"""Optional. The values of resource attributes associated with the type
    of resources being requested. If no values are specified, then all
    resource types are included in the output.

    Messages:
      AdditionalProperty: An additional property for a ResourceAttributesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        ResourceAttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceAttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  gcsDestination = _messages.MessageField('GoogleCloudHealthcareV1beta1ConsentGcsDestination', 1)
  requestAttributes = _messages.MessageField('RequestAttributesValue', 2)
  resourceAttributes = _messages.MessageField('ResourceAttributesValue', 3)


class QueryAccessibleDataResponse(_messages.Message):
  r"""Response for successful QueryAccessibleData operations. This structure
  is included in the response upon operation completion.

  Fields:
    gcsUris: List of files, each of which contains a list of data_id(s) that
      are consented for a specified use in the request.
  """

  gcsUris = _messages.StringField(1, repeated=True)


class RecurseTag(_messages.Message):
  r"""Recursively apply DICOM de-id to tags nested in a sequence. Supported
  [Value Representation] (http://dicom.nema.org/medical/dicom/2018e/output/cht
  ml/part05/sect_6.2.html#table_6.2-1): SQ
  """



class RedactConfig(_messages.Message):
  r"""Define how to redact sensitive values. Default behaviour is erase. For
  example, "My name is Jane." becomes "My name is ."
  """



class RegenUidTag(_messages.Message):
  r"""Replace UID with a new generated UID. Supported [Value Representation] (
  http://dicom.nema.org/medical/dicom/2018e/output/chtml/part05/sect_6.2.html#
  table_6.2-1): UI
  """



class RejectConsentRequest(_messages.Message):
  r"""Rejects the latest revision of the specified Consent by committing a new
  revision with `state` updated to `REJECTED`. If the latest revision of the
  given Consent is in the `REJECTED` state, no new revision is committed.

  Fields:
    consentArtifact: Optional. The resource name of the Consent artifact that
      contains documentation of the user's rejection of the draft Consent, of
      the form `projects/{project_id}/locations/{location_id}/datasets/{datase
      t_id}/consentStores/{consent_store_id}/consentArtifacts/{consent_artifac
      t_id}`. If the draft Consent had a Consent artifact, this Consent
      artifact overwrites it.
  """

  consentArtifact = _messages.StringField(1)


class RemoveField(_messages.Message):
  r"""Remove field."""


class RemoveTag(_messages.Message):
  r"""Replace with empty tag."""


class ReplaceWithInfoTypeConfig(_messages.Message):
  r"""When using the INSPECT_AND_TRANSFORM action, each match is replaced with
  the name of the info_type. For example, "My name is Jane" becomes "My name
  is [PERSON_NAME]." The TRANSFORM action is equivalent to redacting.
  """



class ResetTag(_messages.Message):
  r"""Reset tag to a placeholder value."""


class ResourceAnnotation(_messages.Message):
  r"""Resource level annotation.

  Fields:
    label: A description of the annotation record.
  """

  label = _messages.StringField(1)


class Resources(_messages.Message):
  r"""A list of FHIR resources.

  Fields:
    resources: List of resources IDs. For example, "Patient/1234".
  """

  resources = _messages.StringField(1, repeated=True)


class Result(_messages.Message):
  r"""The consent evaluation result for a single `data_id`.

  Messages:
    ConsentDetailsValue: The resource names of all evaluated Consents mapped
      to their evaluation.

  Fields:
    consentDetails: The resource names of all evaluated Consents mapped to
      their evaluation.
    consented: Whether the resource is consented for the given use.
    dataId: The unique identifier of the evaluated resource.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConsentDetailsValue(_messages.Message):
    r"""The resource names of all evaluated Consents mapped to their
    evaluation.

    Messages:
      AdditionalProperty: An additional property for a ConsentDetailsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ConsentDetailsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConsentDetailsValue object.

      Fields:
        key: Name of the additional property.
        value: A ConsentEvaluation attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ConsentEvaluation', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  consentDetails = _messages.MessageField('ConsentDetailsValue', 1)
  consented = _messages.BooleanField(2)
  dataId = _messages.StringField(3)


class RevokeConsentRequest(_messages.Message):
  r"""Revokes the latest revision of the specified Consent by committing a new
  revision with `state` updated to `REVOKED`. If the latest revision of the
  given Consent is in the `REVOKED` state, no new revision is committed.

  Fields:
    consentArtifact: Optional. The resource name of the Consent artifact that
      contains proof of the user's revocation of the Consent, of the form `pro
      jects/{project_id}/locations/{location_id}/datasets/{dataset_id}/consent
      Stores/{consent_store_id}/consentArtifacts/{consent_artifact_id}`.
  """

  consentArtifact = _messages.StringField(1)


class SchemaConfig(_messages.Message):
  r"""Configuration for the FHIR BigQuery schema. Determines how the server
  generates the schema.

  Enums:
    SchemaTypeValueValuesEnum: Specifies the output schema type. Schema type
      is required.

  Fields:
    lastUpdatedPartitionConfig: The configuration for exported BigQuery tables
      to be partitioned by FHIR resource's last updated time column.
    recursiveStructureDepth: The depth for all recursive structures in the
      output analytics schema. For example, `concept` in the CodeSystem
      resource is a recursive structure; when the depth is 2, the CodeSystem
      table will have a column called `concept.concept` but not
      `concept.concept.concept`. If not specified or set to 0, the server will
      use the default value 2. The maximum depth allowed is 5.
    schemaType: Specifies the output schema type. Schema type is required.
  """

  class SchemaTypeValueValuesEnum(_messages.Enum):
    r"""Specifies the output schema type. Schema type is required.

    Values:
      SCHEMA_TYPE_UNSPECIFIED: No schema type specified. This type is
        unsupported.
      LOSSLESS: A data-driven schema generated from the fields present in the
        FHIR data being exported, with no additional simplification. This type
        cannot be used for streaming to BigQuery.
      ANALYTICS: Analytics schema defined by the FHIR community. See
        https://github.com/FHIR/sql-on-fhir/blob/master/sql-on-fhir.md.
        BigQuery only allows a maximum of 10,000 columns per table. Due to
        this limitation, the server will not generate schemas for fields of
        type `Resource`, which can hold any resource type. The affected fields
        are `Parameters.parameter.resource`, `Bundle.entry.resource`, and
        `Bundle.entry.response.outcome`. Analytics schema does not gracefully
        handle extensions with one or more occurrences, anaytics schema also
        does not handle contained resource.
      ANALYTICS_V2: Analytics V2, similar to schema defined by the FHIR
        community, with added support for extensions with one or more
        occurrences and contained resources in stringified JSON. Analytics V2
        uses more space in the destination table than Analytics V1. It is
        generally recommended to use Analytics V2 over Analytics.
    """
    SCHEMA_TYPE_UNSPECIFIED = 0
    LOSSLESS = 1
    ANALYTICS = 2
    ANALYTICS_V2 = 3

  lastUpdatedPartitionConfig = _messages.MessageField('TimePartitioning', 1)
  recursiveStructureDepth = _messages.IntegerField(2)
  schemaType = _messages.EnumField('SchemaTypeValueValuesEnum', 3)


class SchemaGroup(_messages.Message):
  r"""An HL7v2 logical group construct.

  Fields:
    choice: True indicates that this is a choice group, meaning that only one
      of its segments can exist in a given message.
    maxOccurs: The maximum number of times this group can be repeated. 0 or -1
      means unbounded.
    members: Nested groups and/or segments.
    minOccurs: The minimum number of times this group must be
      present/repeated.
    name: The name of this group. For example, "ORDER_DETAIL".
  """

  choice = _messages.BooleanField(1)
  maxOccurs = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  members = _messages.MessageField('GroupOrSegment', 3, repeated=True)
  minOccurs = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  name = _messages.StringField(5)


class SchemaPackage(_messages.Message):
  r"""A schema package contains a set of schemas and type definitions.

  Enums:
    SchematizedParsingTypeValueValuesEnum: Determines how messages that fail
      to parse are handled.
    UnexpectedSegmentHandlingValueValuesEnum: Determines how unexpected
      segments (segments not matched to the schema) are handled.

  Fields:
    ignoreMinOccurs: Flag to ignore all min_occurs restrictions in the schema.
      This means that incoming messages can omit any group, segment, field,
      component, or subcomponent.
    schemas: Schema configs that are layered based on their VersionSources
      that match the incoming message. Schema configs present in higher
      indices override those in lower indices with the same message type and
      trigger event if their VersionSources all match an incoming message.
    schematizedParsingType: Determines how messages that fail to parse are
      handled.
    types: Schema type definitions that are layered based on their
      VersionSources that match the incoming message. Type definitions present
      in higher indices override those in lower indices with the same type
      name if their VersionSources all match an incoming message.
    unexpectedSegmentHandling: Determines how unexpected segments (segments
      not matched to the schema) are handled.
  """

  class SchematizedParsingTypeValueValuesEnum(_messages.Enum):
    r"""Determines how messages that fail to parse are handled.

    Values:
      SCHEMATIZED_PARSING_TYPE_UNSPECIFIED: Unspecified schematized parsing
        type, equivalent to `SOFT_FAIL`.
      SOFT_FAIL: Messages that fail to parse are still stored and ACKed but a
        parser error is stored in place of the schematized data.
      HARD_FAIL: Messages that fail to parse are rejected from
        ingestion/insertion and return an error code.
    """
    SCHEMATIZED_PARSING_TYPE_UNSPECIFIED = 0
    SOFT_FAIL = 1
    HARD_FAIL = 2

  class UnexpectedSegmentHandlingValueValuesEnum(_messages.Enum):
    r"""Determines how unexpected segments (segments not matched to the
    schema) are handled.

    Values:
      UNEXPECTED_SEGMENT_HANDLING_MODE_UNSPECIFIED: Unspecified handling mode,
        equivalent to FAIL.
      FAIL: Unexpected segments fail to parse and return an error.
      SKIP: Unexpected segments do not fail, but are omitted from the output.
      PARSE: Unexpected segments do not fail, but are parsed in place and
        added to the current group. If a segment has a type definition, it is
        used, otherwise it is parsed as VARIES.
    """
    UNEXPECTED_SEGMENT_HANDLING_MODE_UNSPECIFIED = 0
    FAIL = 1
    SKIP = 2
    PARSE = 3

  ignoreMinOccurs = _messages.BooleanField(1)
  schemas = _messages.MessageField('Hl7SchemaConfig', 2, repeated=True)
  schematizedParsingType = _messages.EnumField('SchematizedParsingTypeValueValuesEnum', 3)
  types = _messages.MessageField('Hl7TypesConfig', 4, repeated=True)
  unexpectedSegmentHandling = _messages.EnumField('UnexpectedSegmentHandlingValueValuesEnum', 5)


class SchemaSegment(_messages.Message):
  r"""An HL7v2 Segment.

  Fields:
    maxOccurs: The maximum number of times this segment can be present in this
      group. 0 or -1 means unbounded.
    minOccurs: The minimum number of times this segment can be present in this
      group.
    type: The Segment type. For example, "PID".
  """

  maxOccurs = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minOccurs = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  type = _messages.StringField(3)


class SchematizedData(_messages.Message):
  r"""The content of an HL7v2 message in a structured format as specified by a
  schema.

  Fields:
    data: JSON output of the parser.
    error: The error output of the parser.
  """

  data = _messages.StringField(1)
  error = _messages.StringField(2)


class SearchConfig(_messages.Message):
  r"""Contains the configuration for FHIR search.

  Fields:
    searchParameters: A list of search parameters in this FHIR store that are
      used to configure this FHIR store.
  """

  searchParameters = _messages.MessageField('SearchParameter', 1, repeated=True)


class SearchParameter(_messages.Message):
  r"""Contains the versioned name and the URL for one SearchParameter.

  Fields:
    canonicalUrl: The canonical url of the search parameter resource.
    parameter: The versioned name of the search parameter resource. The format
      is projects/{project-id}/locations/{location}/datasets/{dataset-
      id}/fhirStores/{fhirStore-id}/fhir/SearchParameter/{resource-
      id}/_history/{version-id} For fhir stores with
      disable_resource_versioning=true, the format is projects/{project-
      id}/locations/{location}/datasets/{dataset-id}/fhirStores/{fhirStore-
      id}/fhir/SearchParameter/{resource-id}/
  """

  canonicalUrl = _messages.StringField(1)
  parameter = _messages.StringField(2)


class SearchResourcesRequest(_messages.Message):
  r"""Request to search the resources in the specified FHIR store.

  Fields:
    resourceType: The FHIR resource type to search, such as Patient or
      Observation. For a complete list, see the FHIR Resource Index ([DSTU2](h
      ttps://hl7.org/implement/standards/fhir/DSTU2/resourcelist.html),
      [STU3](https://hl7.org/implement/standards/fhir/STU3/resourcelist.html),
      [R4](https://hl7.org/implement/standards/fhir/R4/resourcelist.html)).
  """

  resourceType = _messages.StringField(1)


class Segment(_messages.Message):
  r"""A segment in a structured format.

  Messages:
    FieldsValue: A mapping from the positional location to the value. The key
      string uses zero-based indexes separated by dots to identify Fields,
      components and sub-components. A bracket notation is also used to
      identify different instances of a repeated field. Regex for key:
      (\d+)(\[\d+\])?(.\d+)?(.\d+)? Examples of (key, value) pairs: * (0.1,
      "hemoglobin") denotes that the first component of Field 0 has the value
      "hemoglobin". * (1.1.2, "CBC") denotes that the second sub-component of
      the first component of Field 1 has the value "CBC". * (1[0].1, "HbA1c")
      denotes that the first component of the first Instance of Field 1, which
      is repeated, has the value "HbA1c".

  Fields:
    fields: A mapping from the positional location to the value. The key
      string uses zero-based indexes separated by dots to identify Fields,
      components and sub-components. A bracket notation is also used to
      identify different instances of a repeated field. Regex for key:
      (\d+)(\[\d+\])?(.\d+)?(.\d+)? Examples of (key, value) pairs: * (0.1,
      "hemoglobin") denotes that the first component of Field 0 has the value
      "hemoglobin". * (1.1.2, "CBC") denotes that the second sub-component of
      the first component of Field 1 has the value "CBC". * (1[0].1, "HbA1c")
      denotes that the first component of the first Instance of Field 1, which
      is repeated, has the value "HbA1c".
    segmentId: A string that indicates the type of segment. For example, EVN
      or PID.
    setId: Set ID for segments that can be in a set. This can be empty if it's
      missing or isn't applicable.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FieldsValue(_messages.Message):
    r"""A mapping from the positional location to the value. The key string
    uses zero-based indexes separated by dots to identify Fields, components
    and sub-components. A bracket notation is also used to identify different
    instances of a repeated field. Regex for key:
    (\d+)(\[\d+\])?(.\d+)?(.\d+)? Examples of (key, value) pairs: * (0.1,
    "hemoglobin") denotes that the first component of Field 0 has the value
    "hemoglobin". * (1.1.2, "CBC") denotes that the second sub-component of
    the first component of Field 1 has the value "CBC". * (1[0].1, "HbA1c")
    denotes that the first component of the first Instance of Field 1, which
    is repeated, has the value "HbA1c".

    Messages:
      AdditionalProperty: An additional property for a FieldsValue object.

    Fields:
      additionalProperties: Additional properties of type FieldsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FieldsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  fields = _messages.MessageField('FieldsValue', 1)
  segmentId = _messages.StringField(2)
  setId = _messages.StringField(3)


class SensitiveTextAnnotation(_messages.Message):
  r"""A TextAnnotation specifies a text range that includes sensitive
  information.

  Messages:
    DetailsValue: Maps from a resource slice. For example, FHIR resource field
      path to a set of sensitive text findings. For example,
      Appointment.Narrative text1 --> {findings_1, findings_2, findings_3}

  Fields:
    details: Maps from a resource slice. For example, FHIR resource field path
      to a set of sensitive text findings. For example, Appointment.Narrative
      text1 --> {findings_1, findings_2, findings_3}
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValue(_messages.Message):
    r"""Maps from a resource slice. For example, FHIR resource field path to a
    set of sensitive text findings. For example, Appointment.Narrative text1
    --> {findings_1, findings_2, findings_3}

    Messages:
      AdditionalProperty: An additional property for a DetailsValue object.

    Fields:
      additionalProperties: Additional properties of type DetailsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValue object.

      Fields:
        key: Name of the additional property.
        value: A Detail attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Detail', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  details = _messages.MessageField('DetailsValue', 1)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class Signature(_messages.Message):
  r"""User signature.

  Messages:
    MetadataValue: Optional. Metadata associated with the user's signature.
      For example, the user's name or the user's title.

  Fields:
    image: Optional. An image of the user's signature.
    metadata: Optional. Metadata associated with the user's signature. For
      example, the user's name or the user's title.
    signatureTime: Optional. Timestamp of the signature.
    userId: Required. User's UUID provided by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Optional. Metadata associated with the user's signature. For example,
    the user's name or the user's title.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  image = _messages.MessageField('Image', 1)
  metadata = _messages.MessageField('MetadataValue', 2)
  signatureTime = _messages.StringField(3)
  userId = _messages.StringField(4)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StreamConfig(_messages.Message):
  r"""Contains configuration for streaming FHIR export.

  Fields:
    bigqueryDestination: The destination BigQuery structure that contains both
      the dataset location and corresponding schema config. The output is
      organized in one table per resource type. The server reuses the existing
      tables (if any) that are named after the resource types, e.g. "Patient",
      "Observation". When there is no existing table for a given resource
      type, the server attempts to create one. When a table schema doesn't
      align with the schema config, either because of existing incompatible
      schema or out of band incompatible modification, the server does not
      stream in new data. One resolution in this case is to delete the
      incompatible table and let the server recreate one, though the newly
      created table only contains data after the table recreation. BigQuery
      imposes a 1 MB limit on streaming insert row size, therefore any
      resource mutation that generates more than 1 MB of BigQuery data will
      not be streamed. Results are written to BigQuery tables according to the
      parameters in BigQueryDestination.WriteDisposition. Different versions
      of the same resource are distinguishable by the meta.versionId and
      meta.lastUpdated columns. The operation (CREATE/UPDATE/DELETE) that
      results in the new version is recorded in the meta.tag. The tables
      contain all historical resource versions since streaming was enabled.
      For query convenience, the server also creates one view per table of the
      same name containing only the current resource version. The streamed
      data in the BigQuery dataset is not guaranteed to be completely unique.
      The combination of the id and meta.versionId columns should ideally
      identify a single unique row. But in rare cases, duplicates may exist.
      At query time, users may use the SQL select statement to keep only one
      of the duplicate rows given an id and meta.versionId pair.
      Alternatively, the server created view mentioned above also filters out
      duplicates. If a resource mutation cannot be streamed to BigQuery,
      errors will be logged to Cloud Logging (see [Viewing error logs in Cloud
      Logging](https://cloud.google.com/healthcare/docs/how-tos/logging)).
    deidentifiedStoreDestination: The destination FHIR store for de-identified
      resources. After this field is added, all subsequent
      creates/updates/patches to the source store will be de-identified using
      the provided configuration and applied to the destination store.
      Importing resources to the source store will not trigger the streaming.
      If the source store already contains resources when this option is
      enabled, those resources will not be copied to the destination store
      unless they are subsequently updated. This may result in invalid
      references in the destination store. Before adding this config, you must
      grant the healthcare.fhirResources.update permission on the destination
      store to your project's **Cloud Healthcare Service Agent** [service
      account](https://cloud.google.com/healthcare/docs/how-tos/permissions-
      healthcare-api-gcp-products#the_cloud_healthcare_service_agent). The
      destination store must set enable_update_create to true. The destination
      store must have disable_referential_integrity set to true. If a resource
      cannot be de-identified, errors will be logged to Cloud Logging (see
      [Viewing error logs in Cloud
      Logging](https://cloud.google.com/healthcare/docs/how-tos/logging)).
    resourceTypes: Supply a FHIR resource type (such as "Patient" or
      "Observation"). See https://www.hl7.org/fhir/valueset-resource-
      types.html for a list of all FHIR resource types. The server treats an
      empty list as an intent to stream all the supported resource types in
      this FHIR store.
  """

  bigqueryDestination = _messages.MessageField('GoogleCloudHealthcareV1beta1FhirBigQueryDestination', 1)
  deidentifiedStoreDestination = _messages.MessageField('DeidentifiedStoreDestination', 2)
  resourceTypes = _messages.StringField(3, repeated=True)


class TagFilterList(_messages.Message):
  r"""List of tags to be filtered.

  Fields:
    tags: Tags to be filtered. Tags must be DICOM Data Elements, File Meta
      Elements, or Directory Structuring Elements, as defined at: http://dicom
      .nema.org/medical/dicom/current/output/html/part06.html#table_6-1,. They
      may be provided by "Keyword" or "Tag". For example, "PatientID",
      "00100010".
  """

  tags = _messages.StringField(1, repeated=True)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TextConfig(_messages.Message):
  r"""Configures how to transform sensitive text `InfoTypes`.

  Enums:
    ProfileTypeValueValuesEnum: Base profile type for text transformation.

  Fields:
    additionalTransformations: Additional transformations to apply to the
      detected data, overriding `profile`.
    excludeInfoTypes: InfoTypes to skip transforming, overriding `profile`.
    profileType: Base profile type for text transformation.
    transformations: The transformations to apply to the detected data.
      Deprecated. Use `additional_transformations` instead.
  """

  class ProfileTypeValueValuesEnum(_messages.Enum):
    r"""Base profile type for text transformation.

    Values:
      PROFILE_TYPE_UNSPECIFIED: Same as BASIC.
      EMPTY: Empty profile which does not perform any transformations.
      BASIC: Basic profile applies: DATE -> DateShift Default ->
        ReplaceWithInfoType
    """
    PROFILE_TYPE_UNSPECIFIED = 0
    EMPTY = 1
    BASIC = 2

  additionalTransformations = _messages.MessageField('InfoTypeTransformation', 1, repeated=True)
  excludeInfoTypes = _messages.StringField(2, repeated=True)
  profileType = _messages.EnumField('ProfileTypeValueValuesEnum', 3)
  transformations = _messages.MessageField('InfoTypeTransformation', 4, repeated=True)


class TextSpan(_messages.Message):
  r"""A span of text in the provided document.

  Fields:
    beginOffset: The unicode codepoint index of the beginning of this span.
    content: The original text contained in this span.
  """

  beginOffset = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  content = _messages.StringField(2)


class TimePartitioning(_messages.Message):
  r"""Configuration for FHIR BigQuery time-partitioned tables.

  Enums:
    TypeValueValuesEnum: Type of partitioning.

  Fields:
    expirationMs: Number of milliseconds for which to keep the storage for a
      partition.
    type: Type of partitioning.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of partitioning.

    Values:
      PARTITION_TYPE_UNSPECIFIED: Default unknown time.
      HOUR: Data partitioned by hour.
      DAY: Data partitioned by day.
      MONTH: Data partitioned by month.
      YEAR: Data partitioned by year.
    """
    PARTITION_TYPE_UNSPECIFIED = 0
    HOUR = 1
    DAY = 2
    MONTH = 3
    YEAR = 4

  expirationMs = _messages.IntegerField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class Type(_messages.Message):
  r"""A type definition for some HL7v2 type (incl. Segments and Datatypes).

  Enums:
    PrimitiveValueValuesEnum: If this is a primitive type then this field is
      the type of the primitive For example, STRING. Leave unspecified for
      composite types.

  Fields:
    fields: The (sub) fields this type has (if not primitive).
    name: The name of this type. This would be the segment or datatype name.
      For example, "PID" or "XPN".
    primitive: If this is a primitive type then this field is the type of the
      primitive For example, STRING. Leave unspecified for composite types.
  """

  class PrimitiveValueValuesEnum(_messages.Enum):
    r"""If this is a primitive type then this field is the type of the
    primitive For example, STRING. Leave unspecified for composite types.

    Values:
      PRIMITIVE_UNSPECIFIED: Not a primitive.
      STRING: String primitive.
      VARIES: Element that can have unschematized children.
      UNESCAPED_STRING: Like STRING, but all delimiters below this element are
        ignored.
    """
    PRIMITIVE_UNSPECIFIED = 0
    STRING = 1
    VARIES = 2
    UNESCAPED_STRING = 3

  fields = _messages.MessageField('Field', 1, repeated=True)
  name = _messages.StringField(2)
  primitive = _messages.EnumField('PrimitiveValueValuesEnum', 3)


class UserDataMapping(_messages.Message):
  r"""Maps a resource to the associated user and Attributes.

  Fields:
    archiveTime: Output only. Indicates the time when this mapping was
      archived.
    archived: Output only. Indicates whether this mapping is archived.
    dataId: Required. A unique identifier for the mapped resource.
    name: Resource name of the User data mapping, of the form `projects/{proje
      ct_id}/locations/{location_id}/datasets/{dataset_id}/consentStores/{cons
      ent_store_id}/userDataMappings/{user_data_mapping_id}`.
    resourceAttributes: Attributes of the resource. Only explicitly set
      attributes are displayed here. Attribute definitions with defaults set
      implicitly apply to these User data mappings. Attributes listed here
      must be single valued, that is, exactly one value is specified for the
      field "values" in each Attribute.
    userId: Required. User's UUID provided by the client.
  """

  archiveTime = _messages.StringField(1)
  archived = _messages.BooleanField(2)
  dataId = _messages.StringField(3)
  name = _messages.StringField(4)
  resourceAttributes = _messages.MessageField('Attribute', 5, repeated=True)
  userId = _messages.StringField(6)


class ValidationConfig(_messages.Message):
  r"""Contains the configuration for FHIR profiles and validation.

  Fields:
    disableFhirpathValidation: Whether to disable FHIRPath validation for
      incoming resources. Set this to true to disable checking incoming
      resources for conformance against FHIRPath requirement defined in the
      FHIR specification. This property only affects resource types that do
      not have profiles configured for them, any rules in enabled
      implementation guides will still be enforced.
    disableProfileValidation: Whether to disable profile validation for this
      FHIR store. Set this to true to disable checking incoming resources for
      conformance against StructureDefinitions in this FHIR store.
    disableReferenceTypeValidation: Whether to disable reference type
      validation for incoming resources. Set this to true to disable checking
      incoming resources for conformance against reference type requirement
      defined in the FHIR specification. This property only affects resource
      types that do not have profiles configured for them, any rules in
      enabled implementation guides will still be enforced.
    disableRequiredFieldValidation: Whether to disable required fields
      validation for incoming resources. Set this to true to disable checking
      incoming resources for conformance against required fields requirement
      defined in the FHIR specification. This property only affects resource
      types that do not have profiles configured for them, any rules in
      enabled implementation guides will still be enforced.
    enabledImplementationGuides: A list of ImplementationGuide URLs in this
      FHIR store that are used to configure the profiles to use for
      validation. For example, to use the US Core profiles for validation, set
      `enabled_implementation_guides` to
      `["http://hl7.org/fhir/us/core/ImplementationGuide/ig"]`. If
      `enabled_implementation_guides` is empty or omitted, then incoming
      resources are only required to conform to the base FHIR profiles.
      Otherwise, a resource must conform to at least one profile listed in the
      `global` property of one of the enabled ImplementationGuides. The Cloud
      Healthcare API does not currently enforce all of the rules in a
      StructureDefinition. The following rules are supported: - min/max -
      minValue/maxValue - maxLength - type - fixed[x] - pattern[x] on simple
      types - slicing, when using "value" as the discriminator type When a URL
      cannot be resolved (for example, in a type assertion), the server does
      not return an error.
  """

  disableFhirpathValidation = _messages.BooleanField(1)
  disableProfileValidation = _messages.BooleanField(2)
  disableReferenceTypeValidation = _messages.BooleanField(3)
  disableRequiredFieldValidation = _messages.BooleanField(4)
  enabledImplementationGuides = _messages.StringField(5, repeated=True)


class VersionSource(_messages.Message):
  r"""Describes a selector for extracting and matching an MSH field to a
  value.

  Fields:
    mshField: The field to extract from the MSH segment. For example, "3.1" or
      "18[1].1".
    value: The value to match with the field. For example, "My Application
      Name" or "2.3".
  """

  mshField = _messages.StringField(1)
  value = _messages.StringField(2)


class Vertex(_messages.Message):
  r"""A 2D coordinate in an image. The origin is the top-left.

  Fields:
    x: X coordinate.
    y: Y coordinate.
  """

  x = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  y = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

"""Generated client library for healthcare version v1beta1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.healthcare.v1beta1 import healthcare_v1beta1_messages as messages


class HealthcareV1beta1(base_api.BaseApiClient):
  """Generated client library for service healthcare version v1beta1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://healthcare.googleapis.com/'
  MTLS_BASE_URL = 'https://healthcare.mtls.googleapis.com/'

  _PACKAGE = 'healthcare'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1beta1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'HealthcareV1beta1'
  _URL_VERSION = 'v1beta1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new healthcare handle."""
    url = url or self.BASE_URL
    super(HealthcareV1beta1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_datasets_annotationStores_annotations = self.ProjectsLocationsDatasetsAnnotationStoresAnnotationsService(self)
    self.projects_locations_datasets_annotationStores = self.ProjectsLocationsDatasetsAnnotationStoresService(self)
    self.projects_locations_datasets_consentStores_attributeDefinitions = self.ProjectsLocationsDatasetsConsentStoresAttributeDefinitionsService(self)
    self.projects_locations_datasets_consentStores_consentArtifacts = self.ProjectsLocationsDatasetsConsentStoresConsentArtifactsService(self)
    self.projects_locations_datasets_consentStores_consents = self.ProjectsLocationsDatasetsConsentStoresConsentsService(self)
    self.projects_locations_datasets_consentStores_userDataMappings = self.ProjectsLocationsDatasetsConsentStoresUserDataMappingsService(self)
    self.projects_locations_datasets_consentStores = self.ProjectsLocationsDatasetsConsentStoresService(self)
    self.projects_locations_datasets_dicomStores_studies_series_instances_frames = self.ProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesFramesService(self)
    self.projects_locations_datasets_dicomStores_studies_series_instances = self.ProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesService(self)
    self.projects_locations_datasets_dicomStores_studies_series = self.ProjectsLocationsDatasetsDicomStoresStudiesSeriesService(self)
    self.projects_locations_datasets_dicomStores_studies = self.ProjectsLocationsDatasetsDicomStoresStudiesService(self)
    self.projects_locations_datasets_dicomStores = self.ProjectsLocationsDatasetsDicomStoresService(self)
    self.projects_locations_datasets_fhirStores_fhir = self.ProjectsLocationsDatasetsFhirStoresFhirService(self)
    self.projects_locations_datasets_fhirStores = self.ProjectsLocationsDatasetsFhirStoresService(self)
    self.projects_locations_datasets_hl7V2Stores_messages = self.ProjectsLocationsDatasetsHl7V2StoresMessagesService(self)
    self.projects_locations_datasets_hl7V2Stores = self.ProjectsLocationsDatasetsHl7V2StoresService(self)
    self.projects_locations_datasets_operations = self.ProjectsLocationsDatasetsOperationsService(self)
    self.projects_locations_datasets = self.ProjectsLocationsDatasetsService(self)
    self.projects_locations_services_nlp = self.ProjectsLocationsServicesNlpService(self)
    self.projects_locations_services = self.ProjectsLocationsServicesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsDatasetsAnnotationStoresAnnotationsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_annotationStores_annotations resource."""

    _NAME = 'projects_locations_datasets_annotationStores_annotations'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsAnnotationStoresAnnotationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Annotation record. It is valid to create Annotation objects for the same source more than once since a unique ID is assigned to each record by this service.

      Args:
        request: (HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Annotation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationStores/{annotationStoresId}/annotations',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.annotationStores.annotations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/annotations',
        request_field='annotation',
        request_type_name='HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsCreateRequest',
        response_type_name='Annotation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an Annotation or returns NOT_FOUND if it does not exist.

      Args:
        request: (HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationStores/{annotationStoresId}/annotations/{annotationsId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.annotationStores.annotations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an Annotation.

      Args:
        request: (HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Annotation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationStores/{annotationStoresId}/annotations/{annotationsId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.annotationStores.annotations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsGetRequest',
        response_type_name='Annotation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Annotations in the given Annotation store for a source resource.

      Args:
        request: (HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAnnotationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationStores/{annotationStoresId}/annotations',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.annotationStores.annotations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'view'],
        relative_path='v1beta1/{+parent}/annotations',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsListRequest',
        response_type_name='ListAnnotationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the Annotation.

      Args:
        request: (HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Annotation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationStores/{annotationStoresId}/annotations/{annotationsId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.annotationStores.annotations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='annotation',
        request_type_name='HealthcareProjectsLocationsDatasetsAnnotationStoresAnnotationsPatchRequest',
        response_type_name='Annotation',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsAnnotationStoresService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_annotationStores resource."""

    _NAME = 'projects_locations_datasets_annotationStores'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsAnnotationStoresService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Annotation store within the parent dataset.

      Args:
        request: (HealthcareProjectsLocationsDatasetsAnnotationStoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AnnotationStore) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationStores',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.annotationStores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['annotationStoreId'],
        relative_path='v1beta1/{+parent}/annotationStores',
        request_field='annotationStore',
        request_type_name='HealthcareProjectsLocationsDatasetsAnnotationStoresCreateRequest',
        response_type_name='AnnotationStore',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified Annotation store and removes all annotations that are contained within it.

      Args:
        request: (HealthcareProjectsLocationsDatasetsAnnotationStoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationStores/{annotationStoresId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.annotationStores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsAnnotationStoresDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Evaluate(self, request, global_params=None):
      r"""Evaluate an Annotation store against a ground truth Annotation store. When the operation finishes successfully, a detailed response is returned of type EvaluateAnnotationStoreResponse, contained in the response. The metadata field type is OperationMetadata. Errors are logged to Cloud Logging (see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging)).

      Args:
        request: (HealthcareProjectsLocationsDatasetsAnnotationStoresEvaluateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Evaluate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Evaluate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationStores/{annotationStoresId}:evaluate',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.annotationStores.evaluate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:evaluate',
        request_field='evaluateAnnotationStoreRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsAnnotationStoresEvaluateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Export Annotations from the Annotation store. If the request is successful, a detailed response is returned of type ExportAnnotationsResponse, contained in the response field when the operation finishes. The metadata field type is OperationMetadata. Errors are logged to Cloud Logging (see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging)).

      Args:
        request: (HealthcareProjectsLocationsDatasetsAnnotationStoresExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationStores/{annotationStoresId}:export',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.annotationStores.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:export',
        request_field='exportAnnotationsRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsAnnotationStoresExportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified Annotation store or returns NOT_FOUND if it does not exist.

      Args:
        request: (HealthcareProjectsLocationsDatasetsAnnotationStoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AnnotationStore) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationStores/{annotationStoresId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.annotationStores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsAnnotationStoresGetRequest',
        response_type_name='AnnotationStore',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (HealthcareProjectsLocationsDatasetsAnnotationStoresGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationStores/{annotationStoresId}:getIamPolicy',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.annotationStores.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1beta1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsAnnotationStoresGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Import Annotations to the Annotation store by loading data from the specified sources. If the request is successful, a detailed response is returned as of type ImportAnnotationsResponse, contained in the response field when the operation finishes. The metadata field type is OperationMetadata. Errors are logged to Cloud Logging (see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging)).

      Args:
        request: (HealthcareProjectsLocationsDatasetsAnnotationStoresImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationStores/{annotationStoresId}:import',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.annotationStores.import',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:import',
        request_field='importAnnotationsRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsAnnotationStoresImportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Annotation stores in the given dataset for a source store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsAnnotationStoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAnnotationStoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationStores',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.annotationStores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/annotationStores',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsAnnotationStoresListRequest',
        response_type_name='ListAnnotationStoresResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified Annotation store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsAnnotationStoresPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AnnotationStore) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationStores/{annotationStoresId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.annotationStores.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='annotationStore',
        request_type_name='HealthcareProjectsLocationsDatasetsAnnotationStoresPatchRequest',
        response_type_name='AnnotationStore',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (HealthcareProjectsLocationsDatasetsAnnotationStoresSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationStores/{annotationStoresId}:setIamPolicy',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.annotationStores.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsAnnotationStoresSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (HealthcareProjectsLocationsDatasetsAnnotationStoresTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationStores/{annotationStoresId}:testIamPermissions',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.annotationStores.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsAnnotationStoresTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsConsentStoresAttributeDefinitionsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_consentStores_attributeDefinitions resource."""

    _NAME = 'projects_locations_datasets_consentStores_attributeDefinitions'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsConsentStoresAttributeDefinitionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Attribute definition in the parent consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AttributeDefinition) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/attributeDefinitions',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.attributeDefinitions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['attributeDefinitionId'],
        relative_path='v1beta1/{+parent}/attributeDefinitions',
        request_field='attributeDefinition',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsCreateRequest',
        response_type_name='AttributeDefinition',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified Attribute definition. Fails if the Attribute definition is referenced by any User data mapping, or the latest revision of any Consent.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/attributeDefinitions/{attributeDefinitionsId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.consentStores.attributeDefinitions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified Attribute definition.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AttributeDefinition) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/attributeDefinitions/{attributeDefinitionsId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.attributeDefinitions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsGetRequest',
        response_type_name='AttributeDefinition',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Attribute definitions in the specified consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAttributeDefinitionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/attributeDefinitions',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.attributeDefinitions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/attributeDefinitions',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsListRequest',
        response_type_name='ListAttributeDefinitionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified Attribute definition.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AttributeDefinition) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/attributeDefinitions/{attributeDefinitionsId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.consentStores.attributeDefinitions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='attributeDefinition',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresAttributeDefinitionsPatchRequest',
        response_type_name='AttributeDefinition',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsConsentStoresConsentArtifactsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_consentStores_consentArtifacts resource."""

    _NAME = 'projects_locations_datasets_consentStores_consentArtifacts'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsConsentStoresConsentArtifactsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Consent artifact in the parent consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConsentArtifact) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consentArtifacts',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.consentArtifacts.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/consentArtifacts',
        request_field='consentArtifact',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsCreateRequest',
        response_type_name='ConsentArtifact',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified Consent artifact. Fails if the artifact is referenced by the latest revision of any Consent.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consentArtifacts/{consentArtifactsId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.consentStores.consentArtifacts.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified Consent artifact.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConsentArtifact) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consentArtifacts/{consentArtifactsId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.consentArtifacts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsGetRequest',
        response_type_name='ConsentArtifact',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Consent artifacts in the specified consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConsentArtifactsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consentArtifacts',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.consentArtifacts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/consentArtifacts',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentArtifactsListRequest',
        response_type_name='ListConsentArtifactsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsConsentStoresConsentsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_consentStores_consents resource."""

    _NAME = 'projects_locations_datasets_consentStores_consents'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsConsentStoresConsentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Activate(self, request, global_params=None):
      r"""Activates the latest revision of the specified Consent by committing a new revision with `state` updated to `ACTIVE`. If the latest revision of the specified Consent is in the `ACTIVE` state, no new revision is committed. A FAILED_PRECONDITION error occurs if the latest revision of the specified consent is in the `REJECTED` or `REVOKED` state.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentsActivateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Consent) The response message.
      """
      config = self.GetMethodConfig('Activate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Activate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consents/{consentsId}:activate',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.consents.activate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:activate',
        request_field='activateConsentRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentsActivateRequest',
        response_type_name='Consent',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new Consent in the parent consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Consent) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consents',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.consents.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/consents',
        request_field='consent',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentsCreateRequest',
        response_type_name='Consent',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the Consent and its revisions. To keep a record of the Consent but mark it inactive, see [RevokeConsent]. To delete a revision of a Consent, see [DeleteConsentRevision]. This operation does not delete the related Consent artifact.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consents/{consentsId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.consentStores.consents.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def DeleteRevision(self, request, global_params=None):
      r"""Deletes the specified revision of a Consent. An INVALID_ARGUMENT error occurs if the specified revision is the latest revision.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentsDeleteRevisionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('DeleteRevision')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteRevision.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consents/{consentsId}:deleteRevision',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.consentStores.consents.deleteRevision',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:deleteRevision',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentsDeleteRevisionRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified revision of a Consent, or the latest revision if `revision_id` is not specified in the resource name.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Consent) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consents/{consentsId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.consents.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentsGetRequest',
        response_type_name='Consent',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Consent in the given consent store, returning each Consent's latest revision.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConsentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consents',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.consents.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/consents',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentsListRequest',
        response_type_name='ListConsentsResponse',
        supports_download=False,
    )

    def ListRevisions(self, request, global_params=None):
      r"""Lists the revisions of the specified Consent in reverse chronological order.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentsListRevisionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConsentRevisionsResponse) The response message.
      """
      config = self.GetMethodConfig('ListRevisions')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListRevisions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consents/{consentsId}:listRevisions',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.consents.listRevisions',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+name}:listRevisions',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentsListRevisionsRequest',
        response_type_name='ListConsentRevisionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the latest revision of the specified Consent by committing a new revision with the changes. A FAILED_PRECONDITION error occurs if the latest revision of the specified Consent is in the `REJECTED` or `REVOKED` state.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Consent) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consents/{consentsId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.consentStores.consents.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='consent',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentsPatchRequest',
        response_type_name='Consent',
        supports_download=False,
    )

    def Reject(self, request, global_params=None):
      r"""Rejects the latest revision of the specified Consent by committing a new revision with `state` updated to `REJECTED`. If the latest revision of the specified Consent is in the `REJECTED` state, no new revision is committed. A FAILED_PRECONDITION error occurs if the latest revision of the specified Consent is in the `ACTIVE` or `REVOKED` state.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentsRejectRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Consent) The response message.
      """
      config = self.GetMethodConfig('Reject')
      return self._RunMethod(
          config, request, global_params=global_params)

    Reject.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consents/{consentsId}:reject',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.consents.reject',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:reject',
        request_field='rejectConsentRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentsRejectRequest',
        response_type_name='Consent',
        supports_download=False,
    )

    def Revoke(self, request, global_params=None):
      r"""Revokes the latest revision of the specified Consent by committing a new revision with `state` updated to `REVOKED`. If the latest revision of the specified Consent is in the `REVOKED` state, no new revision is committed. A FAILED_PRECONDITION error occurs if the latest revision of the given consent is in `DRAFT` or `REJECTED` state.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresConsentsRevokeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Consent) The response message.
      """
      config = self.GetMethodConfig('Revoke')
      return self._RunMethod(
          config, request, global_params=global_params)

    Revoke.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/consents/{consentsId}:revoke',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.consents.revoke',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:revoke',
        request_field='revokeConsentRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresConsentsRevokeRequest',
        response_type_name='Consent',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsConsentStoresUserDataMappingsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_consentStores_userDataMappings resource."""

    _NAME = 'projects_locations_datasets_consentStores_userDataMappings'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsConsentStoresUserDataMappingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Archive(self, request, global_params=None):
      r"""Archives the specified User data mapping.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsArchiveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ArchiveUserDataMappingResponse) The response message.
      """
      config = self.GetMethodConfig('Archive')
      return self._RunMethod(
          config, request, global_params=global_params)

    Archive.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/userDataMappings/{userDataMappingsId}:archive',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.userDataMappings.archive',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:archive',
        request_field='archiveUserDataMappingRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsArchiveRequest',
        response_type_name='ArchiveUserDataMappingResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new User data mapping in the parent consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UserDataMapping) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/userDataMappings',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.userDataMappings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/userDataMappings',
        request_field='userDataMapping',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsCreateRequest',
        response_type_name='UserDataMapping',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified User data mapping.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/userDataMappings/{userDataMappingsId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.consentStores.userDataMappings.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified User data mapping.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UserDataMapping) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/userDataMappings/{userDataMappingsId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.userDataMappings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsGetRequest',
        response_type_name='UserDataMapping',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the User data mappings in the specified consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUserDataMappingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/userDataMappings',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.userDataMappings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/userDataMappings',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsListRequest',
        response_type_name='ListUserDataMappingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified User data mapping.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UserDataMapping) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}/userDataMappings/{userDataMappingsId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.consentStores.userDataMappings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='userDataMapping',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresUserDataMappingsPatchRequest',
        response_type_name='UserDataMapping',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsConsentStoresService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_consentStores resource."""

    _NAME = 'projects_locations_datasets_consentStores'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsConsentStoresService, self).__init__(client)
      self._upload_configs = {
          }

    def CheckDataAccess(self, request, global_params=None):
      r"""Checks if a particular data_id of a User data mapping in the specified consent store is consented for the specified use.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresCheckDataAccessRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (CheckDataAccessResponse) The response message.
      """
      config = self.GetMethodConfig('CheckDataAccess')
      return self._RunMethod(
          config, request, global_params=global_params)

    CheckDataAccess.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}:checkDataAccess',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.checkDataAccess',
        ordered_params=['consentStore'],
        path_params=['consentStore'],
        query_params=[],
        relative_path='v1beta1/{+consentStore}:checkDataAccess',
        request_field='checkDataAccessRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresCheckDataAccessRequest',
        response_type_name='CheckDataAccessResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new consent store in the parent dataset. Attempting to create a consent store with the same ID as an existing store fails with an ALREADY_EXISTS error.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConsentStore) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['consentStoreId'],
        relative_path='v1beta1/{+parent}/consentStores',
        request_field='consentStore',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresCreateRequest',
        response_type_name='ConsentStore',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified consent store and removes all the consent store's data.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.consentStores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def EvaluateUserConsents(self, request, global_params=None):
      r"""Evaluates the user's Consents for all matching User data mappings. Note: User data mappings are indexed asynchronously, which can cause a slight delay between the time mappings are created or updated and when they are included in EvaluateUserConsents results.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresEvaluateUserConsentsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EvaluateUserConsentsResponse) The response message.
      """
      config = self.GetMethodConfig('EvaluateUserConsents')
      return self._RunMethod(
          config, request, global_params=global_params)

    EvaluateUserConsents.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}:evaluateUserConsents',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.evaluateUserConsents',
        ordered_params=['consentStore'],
        path_params=['consentStore'],
        query_params=[],
        relative_path='v1beta1/{+consentStore}:evaluateUserConsents',
        request_field='evaluateUserConsentsRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresEvaluateUserConsentsRequest',
        response_type_name='EvaluateUserConsentsResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConsentStore) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresGetRequest',
        response_type_name='ConsentStore',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}:getIamPolicy',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1beta1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the consent stores in the specified dataset.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListConsentStoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.consentStores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/consentStores',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresListRequest',
        response_type_name='ListConsentStoresResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified consent store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ConsentStore) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.consentStores.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='consentStore',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresPatchRequest',
        response_type_name='ConsentStore',
        supports_download=False,
    )

    def QueryAccessibleData(self, request, global_params=None):
      r"""Queries all data_ids that are consented for a specified use in the given consent store and writes them to a specified destination. The returned Operation includes a progress counter for the number of User data mappings processed. If the request is successful, a detailed response is returned of type QueryAccessibleDataResponse, contained in the response field when the operation finishes. The metadata field type is OperationMetadata. Errors are logged to Cloud Logging (see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging)). For example, the following sample log entry shows a `failed to evaluate consent policy` error that occurred during a QueryAccessibleData call to consent store `projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/consentStores/{consent_store_id}`. ```json jsonPayload: { @type: "type.googleapis.com/google.cloud.healthcare.logging.QueryAccessibleDataLogEntry" error: { code: 9 message: "failed to evaluate consent policy" } resourceName: "projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/consentStores/{consent_store_id}/consents/{consent_id}" } logName: "projects/{project_id}/logs/healthcare.googleapis.com%2Fquery_accessible_data" operation: { id: "projects/{project_id}/locations/{location_id}/datasets/{dataset_id}/operations/{operation_id}" producer: "healthcare.googleapis.com/QueryAccessibleData" } receiveTimestamp: "TIMESTAMP" resource: { labels: { consent_store_id: "{consent_store_id}" dataset_id: "{dataset_id}" location: "{location_id}" project_id: "{project_id}" } type: "healthcare_consent_store" } severity: "ERROR" timestamp: "TIMESTAMP" ```.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresQueryAccessibleDataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('QueryAccessibleData')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryAccessibleData.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}:queryAccessibleData',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.queryAccessibleData',
        ordered_params=['consentStore'],
        path_params=['consentStore'],
        query_params=[],
        relative_path='v1beta1/{+consentStore}:queryAccessibleData',
        request_field='queryAccessibleDataRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresQueryAccessibleDataRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}:setIamPolicy',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (HealthcareProjectsLocationsDatasetsConsentStoresTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/consentStores/{consentStoresId}:testIamPermissions',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.consentStores.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsConsentStoresTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesFramesService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_dicomStores_studies_series_instances_frames resource."""

    _NAME = 'projects_locations_datasets_dicomStores_studies_series_instances_frames'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesFramesService, self).__init__(client)
      self._upload_configs = {
          }

    def RetrieveFrames(self, request, global_params=None):
      r"""RetrieveFrames returns instances associated with the given study, series, SOP Instance UID and frame numbers. See [RetrieveTransaction](http://dicom.nema.org/medical/dicom/current/output/html/part18.html#sect_10.4). For details on the implementation of RetrieveFrames, see [DICOM frames](https://cloud.google.com/healthcare/docs/dicom#dicom_frames) in the Cloud Healthcare API conformance statement. For samples that show how to call RetrieveFrames, see [Retrieving DICOM data](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#retrieving_dicom_data).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesFramesRetrieveFramesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('RetrieveFrames')
      return self._RunMethod(
          config, request, global_params=global_params)

    RetrieveFrames.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}/series/{seriesId}/instances/{instancesId}/frames/{framesId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.studies.series.instances.frames.retrieveFrames',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesFramesRetrieveFramesRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def RetrieveRendered(self, request, global_params=None):
      r"""RetrieveRenderedFrames returns instances associated with the given study, series, SOP Instance UID and frame numbers in an acceptable Rendered Media Type. See [RetrieveTransaction](http://dicom.nema.org/medical/dicom/current/output/html/part18.html#sect_10.4). For details on the implementation of RetrieveRenderedFrames, see [Rendered resources](https://cloud.google.com/healthcare/docs/dicom#rendered_resources) in the Cloud Healthcare API conformance statement. For samples that show how to call RetrieveRenderedFrames, see [Retrieving consumer image formats](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#retrieving_consumer_image_formats).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesFramesRetrieveRenderedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('RetrieveRendered')
      return self._RunMethod(
          config, request, global_params=global_params)

    RetrieveRendered.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}/series/{seriesId}/instances/{instancesId}/frames/{framesId}/rendered',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.studies.series.instances.frames.retrieveRendered',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesFramesRetrieveRenderedRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_dicomStores_studies_series_instances resource."""

    _NAME = 'projects_locations_datasets_dicomStores_studies_series_instances'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""DeleteInstance deletes an instance associated with the given study, series, and SOP Instance UID. Delete requests are equivalent to the GET requests specified in the Retrieve transaction. Study and series search results can take a few seconds to be updated after an instance is deleted using DeleteInstance. For samples that show how to call DeleteInstance, see [Deleting a study, series, or instance](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#deleting_a_study_series_or_instance).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}/series/{seriesId}/instances/{instancesId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.dicomStores.studies.series.instances.delete',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def RetrieveInstance(self, request, global_params=None):
      r"""RetrieveInstance returns instance associated with the given study, series, and SOP Instance UID. See [RetrieveTransaction](http://dicom.nema.org/medical/dicom/current/output/html/part18.html#sect_10.4). For details on the implementation of RetrieveInstance, see [DICOM study/series/instances](https://cloud.google.com/healthcare/docs/dicom#dicom_studyseriesinstances) and [DICOM instances](https://cloud.google.com/healthcare/docs/dicom#dicom_instances) in the Cloud Healthcare API conformance statement. For samples that show how to call RetrieveInstance, see [Retrieving an instance](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#retrieving_an_instance).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesRetrieveInstanceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('RetrieveInstance')
      return self._RunMethod(
          config, request, global_params=global_params)

    RetrieveInstance.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}/series/{seriesId}/instances/{instancesId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.studies.series.instances.retrieveInstance',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesRetrieveInstanceRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def RetrieveMetadata(self, request, global_params=None):
      r"""RetrieveInstanceMetadata returns instance associated with the given study, series, and SOP Instance UID presented as metadata with the bulk data removed. See [RetrieveTransaction](http://dicom.nema.org/medical/dicom/current/output/html/part18.html#sect_10.4). For details on the implementation of RetrieveInstanceMetadata, see [Metadata resources](https://cloud.google.com/healthcare/docs/dicom#metadata_resources) in the Cloud Healthcare API conformance statement. For samples that show how to call RetrieveInstanceMetadata, see [Retrieving metadata](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#retrieving_metadata).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesRetrieveMetadataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('RetrieveMetadata')
      return self._RunMethod(
          config, request, global_params=global_params)

    RetrieveMetadata.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}/series/{seriesId}/instances/{instancesId}/metadata',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.studies.series.instances.retrieveMetadata',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesRetrieveMetadataRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def RetrieveRendered(self, request, global_params=None):
      r"""RetrieveRenderedInstance returns instance associated with the given study, series, and SOP Instance UID in an acceptable Rendered Media Type. See [RetrieveTransaction](http://dicom.nema.org/medical/dicom/current/output/html/part18.html#sect_10.4). For details on the implementation of RetrieveRenderedInstance, see [Rendered resources](https://cloud.google.com/healthcare/docs/dicom#rendered_resources) in the Cloud Healthcare API conformance statement. For samples that show how to call RetrieveRenderedInstance, see [Retrieving consumer image formats](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#retrieving_consumer_image_formats).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesRetrieveRenderedRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('RetrieveRendered')
      return self._RunMethod(
          config, request, global_params=global_params)

    RetrieveRendered.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}/series/{seriesId}/instances/{instancesId}/rendered',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.studies.series.instances.retrieveRendered',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesInstancesRetrieveRenderedRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsDicomStoresStudiesSeriesService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_dicomStores_studies_series resource."""

    _NAME = 'projects_locations_datasets_dicomStores_studies_series'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsDicomStoresStudiesSeriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""DeleteSeries deletes all instances within the given study and series using a long running operation. The method returns an Operation which will be marked successful when the deletion is complete. Warning: Instances cannot be inserted into a series that is being deleted by an operation until the operation completes. For samples that show how to call DeleteSeries, see [Deleting a study, series, or instance](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#deleting_a_study_series_or_instance).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}/series/{seriesId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.dicomStores.studies.series.delete',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RetrieveMetadata(self, request, global_params=None):
      r"""RetrieveSeriesMetadata returns instance associated with the given study and series, presented as metadata with the bulk data removed. See [RetrieveTransaction](http://dicom.nema.org/medical/dicom/current/output/html/part18.html#sect_10.4). For details on the implementation of RetrieveSeriesMetadata, see [Metadata resources](https://cloud.google.com/healthcare/docs/dicom#metadata_resources) in the Cloud Healthcare API conformance statement. For samples that show how to call RetrieveSeriesMetadata, see [Retrieving metadata](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#retrieving_metadata).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesRetrieveMetadataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('RetrieveMetadata')
      return self._RunMethod(
          config, request, global_params=global_params)

    RetrieveMetadata.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}/series/{seriesId}/metadata',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.studies.series.retrieveMetadata',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesRetrieveMetadataRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def RetrieveSeries(self, request, global_params=None):
      r"""RetrieveSeries returns all instances within the given study and series. See [RetrieveTransaction](http://dicom.nema.org/medical/dicom/current/output/html/part18.html#sect_10.4). For details on the implementation of RetrieveSeries, see [DICOM study/series/instances](https://cloud.google.com/healthcare/docs/dicom#dicom_studyseriesinstances) in the Cloud Healthcare API conformance statement. For samples that show how to call RetrieveSeries, see [Retrieving DICOM data](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#retrieving_dicom_data).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesRetrieveSeriesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('RetrieveSeries')
      return self._RunMethod(
          config, request, global_params=global_params)

    RetrieveSeries.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}/series/{seriesId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.studies.series.retrieveSeries',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesRetrieveSeriesRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def SearchForInstances(self, request, global_params=None):
      r"""SearchForInstances returns a list of matching instances. See [RetrieveTransaction](http://dicom.nema.org/medical/dicom/current/output/html/part18.html#sect_10.4). For details on the implementation of SearchForInstances, see [Search transaction](https://cloud.google.com/healthcare/docs/dicom#search_transaction) in the Cloud Healthcare API conformance statement. For samples that show how to call SearchForInstances, see [Searching for studies, series, instances, and frames](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#searching_for_studies_series_instances_and_frames).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesSearchForInstancesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('SearchForInstances')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchForInstances.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}/series/{seriesId}/instances',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.studies.series.searchForInstances',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresStudiesSeriesSearchForInstancesRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsDicomStoresStudiesService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_dicomStores_studies resource."""

    _NAME = 'projects_locations_datasets_dicomStores_studies'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsDicomStoresStudiesService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""DeleteStudy deletes all instances within the given study using a long running operation. The method returns an Operation which will be marked successful when the deletion is complete. Warning: Instances cannot be inserted into a study that is being deleted by an operation until the operation completes. For samples that show how to call DeleteStudy, see [Deleting a study, series, or instance](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#deleting_a_study_series_or_instance).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresStudiesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.dicomStores.studies.delete',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresStudiesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RetrieveMetadata(self, request, global_params=None):
      r"""RetrieveStudyMetadata returns instance associated with the given study presented as metadata with the bulk data removed. See [RetrieveTransaction](http://dicom.nema.org/medical/dicom/current/output/html/part18.html#sect_10.4). For details on the implementation of RetrieveStudyMetadata, see [Metadata resources](https://cloud.google.com/healthcare/docs/dicom#metadata_resources) in the Cloud Healthcare API conformance statement. For samples that show how to call RetrieveStudyMetadata, see [Retrieving metadata](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#retrieving_metadata).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresStudiesRetrieveMetadataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('RetrieveMetadata')
      return self._RunMethod(
          config, request, global_params=global_params)

    RetrieveMetadata.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}/metadata',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.studies.retrieveMetadata',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresStudiesRetrieveMetadataRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def RetrieveStudy(self, request, global_params=None):
      r"""RetrieveStudy returns all instances within the given study. See [RetrieveTransaction](http://dicom.nema.org/medical/dicom/current/output/html/part18.html#sect_10.4). For details on the implementation of RetrieveStudy, see [DICOM study/series/instances](https://cloud.google.com/healthcare/docs/dicom#dicom_studyseriesinstances) in the Cloud Healthcare API conformance statement. For samples that show how to call RetrieveStudy, see [Retrieving DICOM data](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#retrieving_dicom_data).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresStudiesRetrieveStudyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('RetrieveStudy')
      return self._RunMethod(
          config, request, global_params=global_params)

    RetrieveStudy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.studies.retrieveStudy',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresStudiesRetrieveStudyRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def SearchForInstances(self, request, global_params=None):
      r"""SearchForInstances returns a list of matching instances. See [RetrieveTransaction](http://dicom.nema.org/medical/dicom/current/output/html/part18.html#sect_10.4). For details on the implementation of SearchForInstances, see [Search transaction](https://cloud.google.com/healthcare/docs/dicom#search_transaction) in the Cloud Healthcare API conformance statement. For samples that show how to call SearchForInstances, see [Searching for studies, series, instances, and frames](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#searching_for_studies_series_instances_and_frames).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresStudiesSearchForInstancesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('SearchForInstances')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchForInstances.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}/instances',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.studies.searchForInstances',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresStudiesSearchForInstancesRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def SearchForSeries(self, request, global_params=None):
      r"""SearchForSeries returns a list of matching series. See [RetrieveTransaction](http://dicom.nema.org/medical/dicom/current/output/html/part18.html#sect_10.4). For details on the implementation of SearchForSeries, see [Search transaction](https://cloud.google.com/healthcare/docs/dicom#search_transaction) in the Cloud Healthcare API conformance statement. For samples that show how to call SearchForSeries, see [Searching for studies, series, instances, and frames](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#searching_for_studies_series_instances_and_frames).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresStudiesSearchForSeriesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('SearchForSeries')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchForSeries.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}/series',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.studies.searchForSeries',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresStudiesSearchForSeriesRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def StoreInstances(self, request, global_params=None):
      r"""StoreInstances stores DICOM instances associated with study instance unique identifiers (SUID). See [Store Transaction](http://dicom.nema.org/medical/dicom/current/output/html/part18.html#sect_10.5). For details on the implementation of StoreInstances, see [Store transaction](https://cloud.google.com/healthcare/docs/dicom#store_transaction) in the Cloud Healthcare API conformance statement. For samples that show how to call StoreInstances, see [Storing DICOM data](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#storing_dicom_data).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresStudiesStoreInstancesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('StoreInstances')
      return self._RunMethod(
          config, request, global_params=global_params)

    StoreInstances.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies/{studiesId}',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.dicomStores.studies.storeInstances',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='httpBody',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresStudiesStoreInstancesRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsDicomStoresService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_dicomStores resource."""

    _NAME = 'projects_locations_datasets_dicomStores'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsDicomStoresService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new DICOM store within the parent dataset.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DicomStore) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.dicomStores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['dicomStoreId'],
        relative_path='v1beta1/{+parent}/dicomStores',
        request_field='dicomStore',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresCreateRequest',
        response_type_name='DicomStore',
        supports_download=False,
    )

    def Deidentify(self, request, global_params=None):
      r"""De-identifies data from the source store and writes it to the destination store. The metadata field type is OperationMetadata. If the request is successful, the response field type is DeidentifyDicomStoreSummary. The LRO result may still be successful if de-identification fails for some DICOM instances. The output DICOM store will not contain these failed resources. The number of resources processed are tracked in Operation.metadata. Error details are logged to Cloud Logging. For more information, see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresDeidentifyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Deidentify')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deidentify.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}:deidentify',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.dicomStores.deidentify',
        ordered_params=['sourceStore'],
        path_params=['sourceStore'],
        query_params=[],
        relative_path='v1beta1/{+sourceStore}:deidentify',
        request_field='deidentifyDicomStoreRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresDeidentifyRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified DICOM store and removes all images that are contained within it.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.dicomStores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Exports data to the specified destination by copying it from the DICOM store. Errors are also logged to Cloud Logging. For more information, see [Viewing errors in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging). The metadata field type is OperationMetadata.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}:export',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.dicomStores.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:export',
        request_field='exportDicomDataRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresExportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified DICOM store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DicomStore) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresGetRequest',
        response_type_name='DicomStore',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}:getIamPolicy',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1beta1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Imports data into the DICOM store by copying it from the specified source. Errors are logged to Cloud Logging. For more information, see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging). The metadata field type is OperationMetadata.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}:import',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.dicomStores.import',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:import',
        request_field='importDicomDataRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresImportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the DICOM stores in the given dataset.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDicomStoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/dicomStores',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresListRequest',
        response_type_name='ListDicomStoresResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the specified DICOM store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DicomStore) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.dicomStores.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='dicomStore',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresPatchRequest',
        response_type_name='DicomStore',
        supports_download=False,
    )

    def SearchForInstances(self, request, global_params=None):
      r"""SearchForInstances returns a list of matching instances. See [RetrieveTransaction](http://dicom.nema.org/medical/dicom/current/output/html/part18.html#sect_10.4). For details on the implementation of SearchForInstances, see [Search transaction](https://cloud.google.com/healthcare/docs/dicom#search_transaction) in the Cloud Healthcare API conformance statement. For samples that show how to call SearchForInstances, see [Searching for studies, series, instances, and frames](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#searching_for_studies_series_instances_and_frames).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresSearchForInstancesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('SearchForInstances')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchForInstances.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/instances',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.searchForInstances',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresSearchForInstancesRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def SearchForSeries(self, request, global_params=None):
      r"""SearchForSeries returns a list of matching series. See [RetrieveTransaction](http://dicom.nema.org/medical/dicom/current/output/html/part18.html#sect_10.4). For details on the implementation of SearchForSeries, see [Search transaction](https://cloud.google.com/healthcare/docs/dicom#search_transaction) in the Cloud Healthcare API conformance statement. For samples that show how to call SearchForSeries, see [Searching for studies, series, instances, and frames](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#searching_for_studies_series_instances_and_frames).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresSearchForSeriesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('SearchForSeries')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchForSeries.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/series',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.searchForSeries',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresSearchForSeriesRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def SearchForStudies(self, request, global_params=None):
      r"""SearchForStudies returns a list of matching studies. See [RetrieveTransaction](http://dicom.nema.org/medical/dicom/current/output/html/part18.html#sect_10.4). For details on the implementation of SearchForStudies, see [Search transaction](https://cloud.google.com/healthcare/docs/dicom#search_transaction) in the Cloud Healthcare API conformance statement. For samples that show how to call SearchForStudies, see [Searching for studies, series, instances, and frames](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#searching_for_studies_series_instances_and_frames).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresSearchForStudiesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('SearchForStudies')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchForStudies.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.dicomStores.searchForStudies',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresSearchForStudiesRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}:setIamPolicy',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.dicomStores.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def StoreInstances(self, request, global_params=None):
      r"""StoreInstances stores DICOM instances associated with study instance unique identifiers (SUID). See [Store Transaction](http://dicom.nema.org/medical/dicom/current/output/html/part18.html#sect_10.5). For details on the implementation of StoreInstances, see [Store transaction](https://cloud.google.com/healthcare/docs/dicom#store_transaction) in the Cloud Healthcare API conformance statement. For samples that show how to call StoreInstances, see [Storing DICOM data](https://cloud.google.com/healthcare/docs/how-tos/dicomweb#storing_dicom_data).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresStoreInstancesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('StoreInstances')
      return self._RunMethod(
          config, request, global_params=global_params)

    StoreInstances.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}/dicomWeb/studies',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.dicomStores.storeInstances',
        ordered_params=['parent', 'dicomWebPath'],
        path_params=['dicomWebPath', 'parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/dicomWeb/{+dicomWebPath}',
        request_field='httpBody',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresStoreInstancesRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (HealthcareProjectsLocationsDatasetsDicomStoresTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dicomStores/{dicomStoresId}:testIamPermissions',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.dicomStores.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsDicomStoresTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsFhirStoresFhirService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_fhirStores_fhir resource."""

    _NAME = 'projects_locations_datasets_fhirStores_fhir'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsFhirStoresFhirService, self).__init__(client)
      self._upload_configs = {
          }

    def ConceptMap_search_translate(self, request, global_params=None):
      r"""Translates a code from one value set to another by searching for appropriate concept maps. Implements the FHIR standard $translate operation ([DSTU2](https://www.hl7.org/fhir/DSTU2/operation-conceptmap-translate.html), [STU3](https://www.hl7.org/fhir/STU3/operation-conceptmap-translate.html), [R4](https://www.hl7.org/fhir/R4/operation-conceptmap-translate.html)). On success, the response body contains a JSON-encoded representation of a FHIR Parameters resource, which includes the translation result. Errors generated by the FHIR store contain a JSON-encoded `OperationOutcome` resource describing the reason for the error. If the request cannot be mapped to a valid API method on a FHIR store, a generic GCP error might be returned instead.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirConceptMapSearchTranslateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('ConceptMap_search_translate')
      return self._RunMethod(
          config, request, global_params=global_params)

    ConceptMap_search_translate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/ConceptMap/$translate',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.ConceptMap-search-translate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['code', 'conceptMapVersion', 'source', 'system', 'target', 'url'],
        relative_path='v1beta1/{+parent}/fhir/ConceptMap/$translate',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirConceptMapSearchTranslateRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def ConceptMap_translate(self, request, global_params=None):
      r"""Translates a code from one value set to another using a concept map. You can provide your own concept maps to translate any code system to another code system. Implements the FHIR standard $translate operation ([DSTU2](https://www.hl7.org/fhir/DSTU2/operation-conceptmap-translate.html), [STU3](https://www.hl7.org/fhir/STU3/operation-conceptmap-translate.html), [R4](https://www.hl7.org/fhir/R4/operation-conceptmap-translate.html)). On success, the response body contains a JSON-encoded representation of a FHIR Parameters resource, which includes the translation result. Errors generated by the FHIR store contain a JSON-encoded `OperationOutcome` resource describing the reason for the error. If the request cannot be mapped to a valid API method on a FHIR store, a generic GCP error might be returned instead.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirConceptMapTranslateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('ConceptMap_translate')
      return self._RunMethod(
          config, request, global_params=global_params)

    ConceptMap_translate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/ConceptMap/{ConceptMapId}/$translate',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.ConceptMap-translate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['code', 'conceptMapVersion', 'system'],
        relative_path='v1beta1/{+name}/$translate',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirConceptMapTranslateRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def Observation_lastn(self, request, global_params=None):
      r"""Retrieves the N most recent `Observation` resources for a subject matching search criteria specified as query parameters, grouped by `Observation.code`, sorted from most recent to oldest. Implements the FHIR extended operation Observation-lastn ([STU3](https://hl7.org/implement/standards/fhir/STU3/observation-operations.html#lastn), [R4](https://hl7.org/implement/standards/fhir/R4/observation-operations.html#lastn)). DSTU2 doesn't define the Observation-lastn method, but the server supports it the same way it supports STU3. Search terms are provided as query parameters following the same pattern as the search method. The following search parameters must be provided: - `subject` or `patient` to specify a subject for the Observation. - `code`, `category` or any of the composite parameters that include `code`. Any other valid Observation search parameters can also be provided. This operation accepts an additional query parameter `max`, which specifies N, the maximum number of Observations to return from each group, with a default of 1. Searches with over 1000 results are rejected. Results are counted before grouping and limiting the results with `max`. To stay within the limit, constrain these searches using Observation search parameters such as `_lastUpdated` or `date`. On success, the response body contains a JSON-encoded representation of a `Bundle` resource of type `searchset`, containing the results of the operation. Errors generated by the FHIR store contain a JSON-encoded `OperationOutcome` resource describing the reason for the error. If the request cannot be mapped to a valid API method on a FHIR store, a generic GCP error might be returned instead.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirObservationLastnRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('Observation_lastn')
      return self._RunMethod(
          config, request, global_params=global_params)

    Observation_lastn.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/Observation/$lastn',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.Observation-lastn',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/fhir/Observation/$lastn',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirObservationLastnRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def Patient_everything(self, request, global_params=None):
      r"""Retrieves a Patient resource and resources related to that patient. Implements the FHIR extended operation Patient-everything ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/patient-operations.html#everything), [STU3](https://hl7.org/implement/standards/fhir/STU3/patient-operations.html#everything), [R4](https://hl7.org/implement/standards/fhir/R4/patient-operations.html#everything)). On success, the response body contains a JSON-encoded representation of a `Bundle` resource of type `searchset`, containing the results of the operation. Errors generated by the FHIR store contain a JSON-encoded `OperationOutcome` resource describing the reason for the error. If the request cannot be mapped to a valid API method on a FHIR store, a generic GCP error might be returned instead. The resources in scope for the response are: * The patient resource itself. * All the resources directly referenced by the patient resource. * Resources directly referencing the patient resource that meet the inclusion criteria. The inclusion criteria are based on the membership rules in the patient compartment definition ([DSTU2](https://hl7.org/fhir/DSTU2/compartment-patient.html), [STU3](http://www.hl7.org/fhir/stu3/compartmentdefinition-patient.html), [R4](https://hl7.org/fhir/R4/compartmentdefinition-patient.html)), which details the eligible resource types and referencing search parameters. For samples that show how to call `Patient-everything`, see [Getting all patient compartment resources](https://cloud.google.com/healthcare/docs/how-tos/fhir-resources#getting_all_patient_compartment_resources).

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirPatientEverythingRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('Patient_everything')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patient_everything.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/Patient/{PatientId}/$everything',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.Patient-everything',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['_count', '_page_token', '_since', '_type', 'end', 'start'],
        relative_path='v1beta1/{+name}/$everything',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirPatientEverythingRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def Resource_incoming_references(self, request, global_params=None):
      r"""Lists all the resources that directly refer to the given target FHIR resource. Can also support the case when the target resource doesn't exist, for example, if the target has been deleted. On success, the response body contains a Bundle with type `searchset`, where each entry in the Bundle contains the full content of the resource. If the operation fails, an `OperationOutcome` is returned describing the failure. If the request cannot be mapped to a valid API method on a FHIR store, a generic Google Cloud error might be returned instead.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirResourceIncomingReferencesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('Resource_incoming_references')
      return self._RunMethod(
          config, request, global_params=global_params)

    Resource_incoming_references.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/$references',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.Resource-incoming-references',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['_count', '_page_token', '_summary', '_type', 'target'],
        relative_path='v1beta1/{+parent}/fhir/$references',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirResourceIncomingReferencesRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def Resource_purge(self, request, global_params=None):
      r"""Deletes all the historical versions of a resource (excluding the current version) from the FHIR store. To remove all versions of a resource, first delete the current version and then call this method. This is not a FHIR standard operation. For samples that show how to call `Resource-purge`, see [Deleting historical versions of a FHIR resource](https://cloud.google.com/healthcare/docs/how-tos/fhir-resources#deleting_historical_versions_of_a_fhir_resource).

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirResourcePurgeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Resource_purge')
      return self._RunMethod(
          config, request, global_params=global_params)

    Resource_purge.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/{fhirId}/{fhirId1}/$purge',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.Resource-purge',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}/$purge',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirResourcePurgeRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Resource_validate(self, request, global_params=None):
      r"""Validates an input FHIR resource's conformance to its profiles and the profiles configured on the FHIR store. Implements the FHIR extended operation $validate ([DSTU2](http://hl7.org/implement/standards/fhir/DSTU2/resource-operations.html#validate), [STU3](http://hl7.org/implement/standards/fhir/STU3/resource-operations.html#validate), or [R4](http://hl7.org/implement/standards/fhir/R4/resource-operation-validate.html)). The request body must contain a JSON-encoded FHIR resource, and the request headers must contain `Content-Type: application/fhir+json`. The `Parameters` input syntax is not supported. The `profile` query parameter can be used to request that the resource only be validated against a specific profile. If a profile with the given URL cannot be found in the FHIR store then an error is returned. Errors generated by validation contain a JSON-encoded `OperationOutcome` resource describing the reason for the error. If the request cannot be mapped to a valid API method on a FHIR store, a generic GCP error might be returned instead.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirResourceValidateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('Resource_validate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Resource_validate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/{fhirId}/$validate',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.Resource-validate',
        ordered_params=['parent', 'type'],
        path_params=['parent', 'type'],
        query_params=['profile'],
        relative_path='v1beta1/{+parent}/fhir/{+type}/$validate',
        request_field='httpBody',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirResourceValidateRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def Capabilities(self, request, global_params=None):
      r"""Gets the FHIR capability statement ([STU3](https://hl7.org/implement/standards/fhir/STU3/capabilitystatement.html), [R4](https://hl7.org/implement/standards/fhir/R4/capabilitystatement.html)), or the [conformance statement](https://hl7.org/implement/standards/fhir/DSTU2/conformance.html) in the DSTU2 case for the store, which contains a description of functionality supported by the server. Implements the FHIR standard capabilities interaction ([STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#capabilities), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#capabilities)), or the [conformance interaction](https://hl7.org/implement/standards/fhir/DSTU2/http.html#conformance) in the DSTU2 case. On success, the response body contains a JSON-encoded representation of a `CapabilityStatement` resource.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirCapabilitiesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('Capabilities')
      return self._RunMethod(
          config, request, global_params=global_params)

    Capabilities.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/metadata',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.capabilities',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}/fhir/metadata',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirCapabilitiesRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def ConditionalDelete(self, request, global_params=None):
      r"""Deletes FHIR resources that match a search query. Implements the FHIR standard conditional delete interaction ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/http.html#********.1), [STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#*********.1), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#*******.1)). If multiple resources match, all matching resources are deleted. Search terms are provided as query parameters following the same pattern as the search method. Not all FHIR resources that match the search query might be deleted because, by default, a maximum of 100 FHIR resources can be deleted. The number of FHIR resources that can be deleted depends on the page size of the returned resources, which you can control using the `_count` query parameter. Even when using `_count`, you can delete a maximum 1,000 FHIR resources per each call of `conditionalDelete`. Note: Unless resource versioning is disabled by setting the disable_resource_versioning flag on the FHIR store, the deleted resources are moved to a history repository that can still be retrieved through vread and related methods, unless they are removed by the purge method. This method requires the`healthcare.fhirStores.searchResources` and `healthcare.fhirResources.delete` permissions on the parent FHIR store. For samples that show how to call `conditionalDelete`, see [Conditionally deleting a FHIR resource](https://cloud.google.com/healthcare/docs/how-tos/fhir-resources#conditionally_deleting_a_fhir_resource).

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirConditionalDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('ConditionalDelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    ConditionalDelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/{fhirId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.conditionalDelete',
        ordered_params=['parent', 'type'],
        path_params=['parent', 'type'],
        query_params=[],
        relative_path='v1beta1/{+parent}/fhir/{+type}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirConditionalDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def ConditionalPatch(self, request, global_params=None):
      r"""If a resource is found based on the search criteria specified in the query parameters, updates part of that resource by applying the operations specified in a [JSON Patch](http://jsonpatch.com/) document. Implements the FHIR standard conditional patch interaction ([STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#patch), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#patch)). DSTU2 doesn't define a conditional patch method, but the server supports it in the same way it supports STU3. Search terms are provided as query parameters following the same pattern as the search method. If the search criteria identify more than one match, the request returns a `412 Precondition Failed` error. The request body must contain a JSON Patch document, and the request headers must contain `Content-Type: application/json-patch+json`. On success, the response body contains a JSON-encoded representation of the updated resource, including the server-assigned version ID. Errors generated by the FHIR store contain a JSON-encoded `OperationOutcome` resource describing the reason for the error. If the request cannot be mapped to a valid API method on a FHIR store, a generic GCP error might be returned instead. This method requires the`healthcare.fhirStores.searchResources` permission on the parent FHIR store and the `healthcare.fhirResources.patch` permission on the requested FHIR store resource. For samples that show how to call `conditionalPatch`, see [Conditionally patching a FHIR resource](https://cloud.google.com/healthcare/docs/how-tos/fhir-resources#conditionally_patching_a_fhir_resource).

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirConditionalPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('ConditionalPatch')
      return self._RunMethod(
          config, request, global_params=global_params)

    ConditionalPatch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/{fhirId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.conditionalPatch',
        ordered_params=['parent', 'type'],
        path_params=['parent', 'type'],
        query_params=[],
        relative_path='v1beta1/{+parent}/fhir/{+type}',
        request_field='httpBody',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirConditionalPatchRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def ConditionalUpdate(self, request, global_params=None):
      r"""If a resource is found based on the search criteria specified in the query parameters, updates the entire contents of that resource. Implements the FHIR standard conditional update interaction ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/http.html#2.1.0.10.2), [STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#cond-update), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#cond-update)). Search terms are provided as query parameters following the same pattern as the search method. If the search criteria identify more than one match, the request returns a `412 Precondition Failed` error. If the search criteria identify zero matches, and the supplied resource body contains an `id`, and the FHIR store has enable_update_create set, creates the resource with the client-specified ID. It is strongly advised not to include or encode any sensitive data such as patient identifiers in client-specified resource IDs. Those IDs are part of the FHIR resource path recorded in Cloud Audit Logs and Pub/Sub notifications. Those IDs can also be contained in reference fields within other resources. If the search criteria identify zero matches, and the supplied resource body does not contain an `id`, the resource is created with a server-assigned ID as per the create method. The request body must contain a JSON-encoded FHIR resource, and the request headers must contain `Content-Type: application/fhir+json`. On success, the response body contains a JSON-encoded representation of the updated resource, including the server-assigned version ID. Errors generated by the FHIR store contain a JSON-encoded `OperationOutcome` resource describing the reason for the error. If the request cannot be mapped to a valid API method on a FHIR store, a generic GCP error might be returned instead. This method requires the`healthcare.fhirStores.searchResources` and `healthcare.fhirResources.update` permissions on the parent FHIR store. For samples that show how to call `conditionalUpdate`, see [Conditionally updating a FHIR resource](https://cloud.google.com/healthcare/docs/how-tos/fhir-resources#conditionally_updating_a_fhir_resource).

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirConditionalUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('ConditionalUpdate')
      return self._RunMethod(
          config, request, global_params=global_params)

    ConditionalUpdate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/{fhirId}',
        http_method='PUT',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.conditionalUpdate',
        ordered_params=['parent', 'type'],
        path_params=['parent', 'type'],
        query_params=[],
        relative_path='v1beta1/{+parent}/fhir/{+type}',
        request_field='httpBody',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirConditionalUpdateRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a FHIR resource. Implements the FHIR standard create interaction ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/http.html#create), [STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#create), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#create)), which creates a new resource with a server-assigned resource ID. Also supports the FHIR standard conditional create interaction ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/http.html#ccreate), [STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#ccreate), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#ccreate)), specified by supplying an `If-None-Exist` header containing a FHIR search query. If no resources match this search query, the server processes the create operation as normal. The request body must contain a JSON-encoded FHIR resource, and the request headers must contain `Content-Type: application/fhir+json`. On success, the response body contains a JSON-encoded representation of the resource as it was created on the server, including the server-assigned resource ID and version ID. Errors generated by the FHIR store contain a JSON-encoded `OperationOutcome` resource describing the reason for the error. If the request cannot be mapped to a valid API method on a FHIR store, a generic GCP error might be returned instead. For samples that show how to call `create`, see [Creating a FHIR resource](https://cloud.google.com/healthcare/docs/how-tos/fhir-resources#creating_a_fhir_resource).

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/{fhirId}',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.create',
        ordered_params=['parent', 'type'],
        path_params=['parent', 'type'],
        query_params=[],
        relative_path='v1beta1/{+parent}/fhir/{+type}',
        request_field='httpBody',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirCreateRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a FHIR resource. Implements the FHIR standard delete interaction ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/http.html#delete), [STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#delete), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#delete)). Note: Unless resource versioning is disabled by setting the disable_resource_versioning flag on the FHIR store, the deleted resources are moved to a history repository that can still be retrieved through vread and related methods, unless they are removed by the purge method. For samples that show how to call `delete`, see [Deleting a FHIR resource](https://cloud.google.com/healthcare/docs/how-tos/fhir-resources#deleting_a_fhir_resource).

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/{fhirId}/{fhirId1}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirDeleteRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def ExecuteBundle(self, request, global_params=None):
      r"""Executes all the requests in the given Bundle. Implements the FHIR standard batch/transaction interaction ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/http.html#transaction), [STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#transaction), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#transaction)). Supports all interactions within a bundle, except search. This method accepts Bundles of type `batch` and `transaction`, processing them according to the batch processing rules ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/http.html#2.1.0.16.1), [STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#2.21.0.17.1), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#brules)) and transaction processing rules ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/http.html#2.1.0.16.2), [STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#2.21.0.17.2), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#trules)). The request body must contain a JSON-encoded FHIR `Bundle` resource, and the request headers must contain `Content-Type: application/fhir+json`. For a batch bundle or a successful transaction, the response body contains a JSON-encoded representation of a `Bundle` resource of type `batch-response` or `transaction-response` containing one entry for each entry in the request, with the outcome of processing the entry. In the case of an error for a transaction bundle, the response body contains a JSON-encoded `OperationOutcome` resource describing the reason for the error. If the request cannot be mapped to a valid API method on a FHIR store, a generic GCP error might be returned instead. This method checks permissions for each request in the bundle. The `executeBundle` permission is required to call this method, but you must also grant sufficient permissions to execute the individual requests in the bundle. For example, if the bundle contains a request to create a FHIR resource, the caller must also have been granted the `healthcare.fhirResources.create` permission. You can use audit logs to view the permissions for `executeBundle` and each request in the bundle. For more information, see [Viewing Cloud Audit logs](https://cloud.google.com/healthcare-api/docs/how-tos/audit-logging). For samples that show how to call `executeBundle`, see [Managing FHIR resources using FHIR bundles](https://cloud.google.com/healthcare/docs/how-tos/fhir-bundles).

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirExecuteBundleRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('ExecuteBundle')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExecuteBundle.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.executeBundle',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/fhir',
        request_field='httpBody',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirExecuteBundleRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def History(self, request, global_params=None):
      r"""Lists all the versions of a resource (including the current version and deleted versions) from the FHIR store. Implements the per-resource form of the FHIR standard history interaction ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/http.html#history), [STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#history), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#history)). On success, the response body contains a JSON-encoded representation of a `Bundle` resource of type `history`, containing the version history sorted from most recent to oldest versions. Errors generated by the FHIR store contain a JSON-encoded `OperationOutcome` resource describing the reason for the error. If the request cannot be mapped to a valid API method on a FHIR store, a generic GCP error might be returned instead. For samples that show how to call `history`, see [Listing FHIR resource versions](https://cloud.google.com/healthcare/docs/how-tos/fhir-resources#listing_fhir_resource_versions).

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirHistoryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('History')
      return self._RunMethod(
          config, request, global_params=global_params)

    History.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/{fhirId}/{fhirId1}/_history',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.history',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['_at', '_count', '_page_token', '_since'],
        relative_path='v1beta1/{+name}/_history',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirHistoryRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates part of an existing resource by applying the operations specified in a [JSON Patch](http://jsonpatch.com/) document. Implements the FHIR standard patch interaction ([STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#patch), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#patch)). DSTU2 doesn't define a patch method, but the server supports it in the same way it supports STU3. The request body must contain a JSON Patch document, and the request headers must contain `Content-Type: application/json-patch+json`. On success, the response body contains a JSON-encoded representation of the updated resource, including the server-assigned version ID. Errors generated by the FHIR store contain a JSON-encoded `OperationOutcome` resource describing the reason for the error. If the request cannot be mapped to a valid API method on a FHIR store, a generic GCP error might be returned instead. For samples that show how to call `patch`, see [Patching a FHIR resource](https://cloud.google.com/healthcare/docs/how-tos/fhir-resources#patching_a_fhir_resource).

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/{fhirId}/{fhirId1}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='httpBody',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirPatchRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def Read(self, request, global_params=None):
      r"""Gets the contents of a FHIR resource. Implements the FHIR standard read interaction ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/http.html#read), [STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#read), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#read)). Also supports the FHIR standard conditional read interaction ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/http.html#cread), [STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#cread), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#cread)) specified by supplying an `If-Modified-Since` header with a date/time value or an `If-None-Match` header with an ETag value. On success, the response body contains a JSON-encoded representation of the resource. Errors generated by the FHIR store contain a JSON-encoded `OperationOutcome` resource describing the reason for the error. If the request cannot be mapped to a valid API method on a FHIR store, a generic GCP error might be returned instead. For samples that show how to call `read`, see [Getting a FHIR resource](https://cloud.google.com/healthcare/docs/how-tos/fhir-resources#getting_a_fhir_resource).

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirReadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('Read')
      return self._RunMethod(
          config, request, global_params=global_params)

    Read.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/{fhirId}/{fhirId1}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.read',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirReadRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def Search(self, request, global_params=None):
      r"""Searches for resources in the given FHIR store according to criteria specified as query parameters. Implements the FHIR standard search interaction ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/http.html#search), [STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#search), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#search)) using the search semantics described in the FHIR Search specification ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/search.html), [STU3](https://hl7.org/implement/standards/fhir/STU3/search.html), [R4](https://hl7.org/implement/standards/fhir/R4/search.html)). Supports four methods of search defined by the specification: * `GET [base]?[parameters]` to search across all resources. * `GET [base]/[type]?[parameters]` to search resources of a specified type. * `POST [base]/_search?[parameters]` as an alternate form having the same semantics as the `GET` method across all resources. * `POST [base]/[type]/_search?[parameters]` as an alternate form having the same semantics as the `GET` method for the specified type. The `GET` and `POST` methods do not support compartment searches. The `POST` method does not support `application/x-www-form-urlencoded` search parameters. On success, the response body contains a JSON-encoded representation of a `Bundle` resource of type `searchset`, containing the results of the search. Errors generated by the FHIR store contain a JSON-encoded `OperationOutcome` resource describing the reason for the error. If the request cannot be mapped to a valid API method on a FHIR store, a generic GCP error might be returned instead. The server's capability statement, retrieved through capabilities, indicates what search parameters are supported on each FHIR resource. A list of all search parameters defined by the specification can be found in the FHIR Search Parameter Registry ([STU3](https://hl7.org/implement/standards/fhir/STU3/searchparameter-registry.html), [R4](https://hl7.org/implement/standards/fhir/R4/searchparameter-registry.html)). FHIR search parameters for DSTU2 can be found on each resource's definition page. Supported search modifiers: `:missing`, `:exact`, `:contains`, `:text`, `:in`, `:not-in`, `:above`, `:below`, `:[type]`, `:not`, and `recurse` (DSTU2 and STU3) or `:iterate` (R4). Supported search result parameters: `_sort`, `_count`, `_include`, `_revinclude`, `_summary=text`, `_summary=data`, and `_elements`. The maximum number of search results returned defaults to 100, which can be overridden by the `_count` parameter up to a maximum limit of 1000. The server might return fewer resources than requested to prevent excessively large responses. If there are additional results, the returned `Bundle` contains a link of `relation` "next", which has a `_page_token` parameter for an opaque pagination token that can be used to retrieve the next page. Resources with a total size larger than 5MB or a field count larger than 50,000 might not be fully searchable as the server might trim its generated search index in those cases. Note: FHIR resources are indexed asynchronously, so there might be a slight delay between the time a resource is created or changed, and the time when the change reflects in search results. The only exception is resource identifier data, which is indexed synchronously as a special index. As a result, searching using resource identifier is not subject to indexing delay. To use the special synchronous index, the search term for identifier should be in the pattern `identifier=[system]|[value]` or `identifier=[value]`, and any of the following search result parameters can be used: * `_count` * `_include` * `_revinclude` * `_summary` * `_elements` If your query contains any other search parameters, the standard asynchronous index will be used instead. Note that searching against the special index is optimized for resolving a small number of matches. The search isn't optimized if your identifier search criteria matches a large number (i.e. more than 2,000) of resources. For a search query that will match a large number of resources, you can avoiding using the special synchronous index by including an additional `_sort` parameter in your query. Use `_sort=-_lastUpdated` if you want to keep the default sorting order. For samples and detailed information, see [Searching for FHIR resources](https://cloud.google.com/healthcare/docs/how-tos/fhir-search) and [Advanced FHIR search features](https://cloud.google.com/healthcare/docs/how-tos/fhir-advanced-search).

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/_search',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.search',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/fhir/_search',
        request_field='searchResourcesRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirSearchRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def Search_type(self, request, global_params=None):
      r"""Searches for resources in the given FHIR store according to criteria specified as query parameters. Implements the FHIR standard search interaction ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/http.html#search), [STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#search), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#search)) using the search semantics described in the FHIR Search specification ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/search.html), [STU3](https://hl7.org/implement/standards/fhir/STU3/search.html), [R4](https://hl7.org/implement/standards/fhir/R4/search.html)). Supports four methods of search defined by the specification: * `GET [base]?[parameters]` to search across all resources. * `GET [base]/[type]?[parameters]` to search resources of a specified type. * `POST [base]/_search?[parameters]` as an alternate form having the same semantics as the `GET` method across all resources. * `POST [base]/[type]/_search?[parameters]` as an alternate form having the same semantics as the `GET` method for the specified type. The `GET` and `POST` methods do not support compartment searches. The `POST` method does not support `application/x-www-form-urlencoded` search parameters. On success, the response body contains a JSON-encoded representation of a `Bundle` resource of type `searchset`, containing the results of the search. Errors generated by the FHIR store contain a JSON-encoded `OperationOutcome` resource describing the reason for the error. If the request cannot be mapped to a valid API method on a FHIR store, a generic GCP error might be returned instead. The server's capability statement, retrieved through capabilities, indicates what search parameters are supported on each FHIR resource. A list of all search parameters defined by the specification can be found in the FHIR Search Parameter Registry ([STU3](https://hl7.org/implement/standards/fhir/STU3/searchparameter-registry.html), [R4](https://hl7.org/implement/standards/fhir/R4/searchparameter-registry.html)). FHIR search parameters for DSTU2 can be found on each resource's definition page. Supported search modifiers: `:missing`, `:exact`, `:contains`, `:text`, `:in`, `:not-in`, `:above`, `:below`, `:[type]`, `:not`, and `recurse` (DSTU2 and STU3) or `:iterate` (R4). Supported search result parameters: `_sort`, `_count`, `_include`, `_revinclude`, `_summary=text`, `_summary=data`, and `_elements`. The maximum number of search results returned defaults to 100, which can be overridden by the `_count` parameter up to a maximum limit of 1000. The server might return fewer resources than requested to prevent excessively large responses. If there are additional results, the returned `Bundle` contains a link of `relation` "next", which has a `_page_token` parameter for an opaque pagination token that can be used to retrieve the next page. Resources with a total size larger than 5MB or a field count larger than 50,000 might not be fully searchable as the server might trim its generated search index in those cases. Note: FHIR resources are indexed asynchronously, so there might be a slight delay between the time a resource is created or changed, and the time when the change reflects in search results. The only exception is resource identifier data, which is indexed synchronously as a special index. As a result, searching using resource identifier is not subject to indexing delay. To use the special synchronous index, the search term for identifier should be in the pattern `identifier=[system]|[value]` or `identifier=[value]`, and any of the following search result parameters can be used: * `_count` * `_include` * `_revinclude` * `_summary` * `_elements` If your query contains any other search parameters, the standard asynchronous index will be used instead. Note that searching against the special index is optimized for resolving a small number of matches. The search isn't optimized if your identifier search criteria matches a large number (i.e. more than 2,000) of resources. For a search query that will match a large number of resources, you can avoiding using the special synchronous index by including an additional `_sort` parameter in your query. Use `_sort=-_lastUpdated` if you want to keep the default sorting order. For samples and detailed information, see [Searching for FHIR resources](https://cloud.google.com/healthcare/docs/how-tos/fhir-search) and [Advanced FHIR search features](https://cloud.google.com/healthcare/docs/how-tos/fhir-advanced-search).

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirSearchTypeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('Search_type')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search_type.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/{resourceType}/_search',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.search-type',
        ordered_params=['parent', 'resourceType'],
        path_params=['parent', 'resourceType'],
        query_params=[],
        relative_path='v1beta1/{+parent}/fhir/{resourceType}/_search',
        request_field='searchResourcesRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirSearchTypeRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates the entire contents of a resource. Implements the FHIR standard update interaction ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/http.html#update), [STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#update), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#update)). If the specified resource does not exist and the FHIR store has enable_update_create set, creates the resource with the client-specified ID. It is strongly advised not to include or encode any sensitive data such as patient identifiers in client-specified resource IDs. Those IDs are part of the FHIR resource path recorded in Cloud Audit Logs and Pub/Sub notifications. Those IDs can also be contained in reference fields within other resources. The request body must contain a JSON-encoded FHIR resource, and the request headers must contain `Content-Type: application/fhir+json`. The resource must contain an `id` element having an identical value to the ID in the REST path of the request. On success, the response body contains a JSON-encoded representation of the updated resource, including the server-assigned version ID. Errors generated by the FHIR store contain a JSON-encoded `OperationOutcome` resource describing the reason for the error. If the request cannot be mapped to a valid API method on a FHIR store, a generic GCP error might be returned instead. For samples that show how to call `update`, see [Updating a FHIR resource](https://cloud.google.com/healthcare/docs/how-tos/fhir-resources#updating_a_fhir_resource).

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/{fhirId}/{fhirId1}',
        http_method='PUT',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='httpBody',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirUpdateRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

    def Vread(self, request, global_params=None):
      r"""Gets the contents of a version (current or historical) of a FHIR resource by version ID. Implements the FHIR standard vread interaction ([DSTU2](https://hl7.org/implement/standards/fhir/DSTU2/http.html#vread), [STU3](https://hl7.org/implement/standards/fhir/STU3/http.html#vread), [R4](https://hl7.org/implement/standards/fhir/R4/http.html#vread)). On success, the response body contains a JSON-encoded representation of the resource. Errors generated by the FHIR store contain a JSON-encoded `OperationOutcome` resource describing the reason for the error. If the request cannot be mapped to a valid API method on a FHIR store, a generic GCP error might be returned instead. For samples that show how to call `vread`, see [Retrieving a FHIR resource version](https://cloud.google.com/healthcare/docs/how-tos/fhir-resources#retrieving_a_fhir_resource_version).

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresFhirVreadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpBody) The response message.
      """
      config = self.GetMethodConfig('Vread')
      return self._RunMethod(
          config, request, global_params=global_params)

    Vread.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}/fhir/{fhirId}/{fhirId1}/_history/{_historyId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.fhirStores.fhir.vread',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresFhirVreadRequest',
        response_type_name='HttpBody',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsFhirStoresService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_fhirStores resource."""

    _NAME = 'projects_locations_datasets_fhirStores'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsFhirStoresService, self).__init__(client)
      self._upload_configs = {
          }

    def ConfigureSearch(self, request, global_params=None):
      r"""Configure the search parameters for the FHIR store and reindex resources in the FHIR store according to the defined search parameters. The search parameters provided in this request will replace any previous search configuration. The target SearchParameter resources need to exist in the store before calling ConfigureSearch, otherwise an error will occur. This method returns an Operation that can be used to track the progress of the reindexing by calling GetOperation.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresConfigureSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('ConfigureSearch')
      return self._RunMethod(
          config, request, global_params=global_params)

    ConfigureSearch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}:configureSearch',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.configureSearch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:configureSearch',
        request_field='configureSearchRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresConfigureSearchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new FHIR store within the parent dataset.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FhirStore) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['fhirStoreId'],
        relative_path='v1beta1/{+parent}/fhirStores',
        request_field='fhirStore',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresCreateRequest',
        response_type_name='FhirStore',
        supports_download=False,
    )

    def Deidentify(self, request, global_params=None):
      r"""De-identifies data from the source store and writes it to the destination store. The metadata field type is OperationMetadata. If the request is successful, the response field type is DeidentifyFhirStoreSummary. The number of resources processed are tracked in Operation.metadata. Error details are logged to Cloud Logging. For more information, see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging).

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresDeidentifyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Deidentify')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deidentify.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}:deidentify',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.deidentify',
        ordered_params=['sourceStore'],
        path_params=['sourceStore'],
        query_params=[],
        relative_path='v1beta1/{+sourceStore}:deidentify',
        request_field='deidentifyFhirStoreRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresDeidentifyRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified FHIR store and removes all resources within it.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.fhirStores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Export resources from the FHIR store to the specified destination. This method returns an Operation that can be used to track the status of the export by calling GetOperation. Immediate fatal errors appear in the error field, errors are also logged to Cloud Logging (see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging)). Otherwise, when the operation finishes, a detailed response of type ExportResourcesResponse is returned in the response field. The metadata field type for this operation is OperationMetadata.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}:export',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:export',
        request_field='exportResourcesRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresExportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the configuration of the specified FHIR store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FhirStore) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.fhirStores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresGetRequest',
        response_type_name='FhirStore',
        supports_download=False,
    )

    def GetFHIRStoreMetrics(self, request, global_params=None):
      r"""Gets metrics associated with the FHIR store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresGetFHIRStoreMetricsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FhirStoreMetrics) The response message.
      """
      config = self.GetMethodConfig('GetFHIRStoreMetrics')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetFHIRStoreMetrics.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}:getFHIRStoreMetrics',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.fhirStores.getFHIRStoreMetrics',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:getFHIRStoreMetrics',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresGetFHIRStoreMetricsRequest',
        response_type_name='FhirStoreMetrics',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}:getIamPolicy',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.fhirStores.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1beta1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Import resources to the FHIR store by loading data from the specified sources. This method is optimized to load large quantities of data using import semantics that ignore some FHIR store configuration options and are not suitable for all use cases. It is primarily intended to load data into an empty FHIR store that is not being used by other clients. In cases where this method is not appropriate, consider using ExecuteBundle to load data. Every resource in the input must contain a client-supplied ID. Each resource is stored using the supplied ID regardless of the enable_update_create setting on the FHIR store. It is strongly advised not to include or encode any sensitive data such as patient identifiers in client-specified resource IDs. Those IDs are part of the FHIR resource path recorded in Cloud Audit Logs and Cloud Pub/Sub notifications. Those IDs can also be contained in reference fields within other resources. The import process does not enforce referential integrity, regardless of the disable_referential_integrity setting on the FHIR store. This allows the import of resources with arbitrary interdependencies without considering grouping or ordering, but if the input data contains invalid references or if some resources fail to be imported, the FHIR store might be left in a state that violates referential integrity. The import process does not trigger Pub/Sub notification or BigQuery streaming update, regardless of how those are configured on the FHIR store. If a resource with the specified ID already exists, the most recent version of the resource is overwritten without creating a new historical version, regardless of the disable_resource_versioning setting on the FHIR store. If transient failures occur during the import, it is possible that successfully imported resources will be overwritten more than once. The import operation is idempotent unless the input data contains multiple valid resources with the same ID but different contents. In that case, after the import completes, the store contains exactly one resource with that ID but there is no ordering guarantee on which version of the contents it will have. The operation result counters do not count duplicate IDs as an error and count one success for each resource in the input, which might result in a success count larger than the number of resources in the FHIR store. This often occurs when importing data organized in bundles produced by Patient-everything where each bundle contains its own copy of a resource such as Practitioner that might be referred to by many patients. If some resources fail to import, for example due to parsing errors, successfully imported resources are not rolled back. The location and format of the input data are specified by the parameters in ImportResourcesRequest. Note that if no format is specified, this method assumes the `BUNDLE` format. When using the `BUNDLE` format this method ignores the `Bundle.type` field, except that `history` bundles are rejected, and does not apply any of the bundle processing semantics for batch or transaction bundles. Unlike in ExecuteBundle, transaction bundles are not executed as a single transaction and bundle-internal references are not rewritten. The bundle is treated as a collection of resources to be written as provided in `Bundle.entry.resource`, ignoring `Bundle.entry.request`. As an example, this allows the import of `searchset` bundles produced by a FHIR search or Patient-everything operation. This method returns an Operation that can be used to track the status of the import by calling GetOperation. Immediate fatal errors appear in the error field, errors are also logged to Cloud Logging (see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging)). Otherwise, when the operation finishes, a detailed response of type ImportResourcesResponse is returned in the response field. The metadata field type for this operation is OperationMetadata.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}:import',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.import',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:import',
        request_field='importResourcesRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresImportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the FHIR stores in the given dataset.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFhirStoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.fhirStores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/fhirStores',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresListRequest',
        response_type_name='ListFhirStoresResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the configuration of the specified FHIR store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FhirStore) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.fhirStores.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='fhirStore',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresPatchRequest',
        response_type_name='FhirStore',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}:setIamPolicy',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (HealthcareProjectsLocationsDatasetsFhirStoresTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/fhirStores/{fhirStoresId}:testIamPermissions',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.fhirStores.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsFhirStoresTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsHl7V2StoresMessagesService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_hl7V2Stores_messages resource."""

    _NAME = 'projects_locations_datasets_hl7V2Stores_messages'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsHl7V2StoresMessagesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchGet(self, request, global_params=None):
      r"""Gets multiple messages in the given HL7v2 store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesBatchGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BatchGetMessagesResponse) The response message.
      """
      config = self.GetMethodConfig('BatchGet')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchGet.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}/messages:batchGet',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.messages.batchGet',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['ids', 'view'],
        relative_path='v1beta1/{+parent}/messages:batchGet',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesBatchGetRequest',
        response_type_name='BatchGetMessagesResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Parses and stores an HL7v2 message. This method triggers an asynchronous notification to any Pub/Sub topic configured in Hl7V2Store.Hl7V2NotificationConfig, if the filtering matches the message. If an MLLP adapter is configured to listen to a Pub/Sub topic, the adapter transmits the message when a notification is received.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Message) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}/messages',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.messages.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/messages',
        request_field='createMessageRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesCreateRequest',
        response_type_name='Message',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an HL7v2 message.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}/messages/{messagesId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.messages.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an HL7v2 message.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Message) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}/messages/{messagesId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.messages.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesGetRequest',
        response_type_name='Message',
        supports_download=False,
    )

    def Ingest(self, request, global_params=None):
      r"""Parses and stores an HL7v2 message. This method triggers an asynchronous notification to any Pub/Sub topic configured in Hl7V2Store.Hl7V2NotificationConfig, if the filtering matches the message. If an MLLP adapter is configured to listen to a Pub/Sub topic, the adapter transmits the message when a notification is received. If the method is successful, it generates a response containing an HL7v2 acknowledgment (`ACK`) message. If the method encounters an error, it returns a negative acknowledgment (`NACK`) message. This behavior is suitable for replying to HL7v2 interface systems that expect these acknowledgments.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesIngestRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (IngestMessageResponse) The response message.
      """
      config = self.GetMethodConfig('Ingest')
      return self._RunMethod(
          config, request, global_params=global_params)

    Ingest.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}/messages:ingest',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.messages.ingest',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1beta1/{+parent}/messages:ingest',
        request_field='ingestMessageRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesIngestRequest',
        response_type_name='IngestMessageResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the messages in the given HL7v2 store with support for filtering. Note: HL7v2 messages are indexed asynchronously, so there might be a slight delay between the time a message is created and when it can be found through a filter.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMessagesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}/messages',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.messages.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'view'],
        relative_path='v1beta1/{+parent}/messages',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesListRequest',
        response_type_name='ListMessagesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update the message. The contents of the message in Message.data and data extracted from the contents such as Message.create_time can't be altered. Only the Message.labels field is allowed to be updated. The labels in the request are merged with the existing set of labels. Existing labels with the same keys are updated.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Message) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}/messages/{messagesId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.messages.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='message',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresMessagesPatchRequest',
        response_type_name='Message',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsHl7V2StoresService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_hl7V2Stores resource."""

    _NAME = 'projects_locations_datasets_hl7V2Stores'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsHl7V2StoresService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new HL7v2 store within the parent dataset.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Hl7V2Store) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['hl7V2StoreId'],
        relative_path='v1beta1/{+parent}/hl7V2Stores',
        request_field='hl7V2Store',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresCreateRequest',
        response_type_name='Hl7V2Store',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified HL7v2 store and removes all messages that it contains.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Exports the messages to a destination. To filter messages to be exported, define a filter using the start and end time, relative to the message generation time (MSH.7). This API returns an Operation that can be used to track the status of the job by calling GetOperation. Immediate fatal errors appear in the error field. Otherwise, when the operation finishes, a detailed response of type ExportMessagesResponse is returned in the response field. The metadata field type for this operation is OperationMetadata.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}:export',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:export',
        request_field='exportMessagesRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresExportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified HL7v2 store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Hl7V2Store) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresGetRequest',
        response_type_name='Hl7V2Store',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}:getIamPolicy',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1beta1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Import messages to the HL7v2 store by loading data from the specified sources. This method is optimized to load large quantities of data using import semantics that ignore some HL7v2 store configuration options and are not suitable for all use cases. It is primarily intended to load data into an empty HL7v2 store that is not being used by other clients. An existing message will be overwritten if a duplicate message is imported. A duplicate message is a message with the same raw bytes as a message that already exists in this HL7v2 store. When a message is overwritten, its labels will also be overwritten. The import operation is idempotent unless the input data contains multiple valid messages with the same raw bytes but different labels. In that case, after the import completes, the store contains exactly one message with those raw bytes but there is no ordering guarantee on which version of the labels it has. The operation result counters do not count duplicated raw bytes as an error and count one success for each message in the input, which might result in a success count larger than the number of messages in the HL7v2 store. If some messages fail to import, for example due to parsing errors, successfully imported messages are not rolled back. This method returns an Operation that can be used to track the status of the import by calling GetOperation. Immediate fatal errors appear in the error field, errors are also logged to Cloud Logging (see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging)). Otherwise, when the operation finishes, a response of type ImportMessagesResponse is returned in the response field. The metadata field type for this operation is OperationMetadata.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}:import',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.import',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:import',
        request_field='importMessagesRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresImportRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the HL7v2 stores in the given dataset.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListHl7V2StoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/hl7V2Stores',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresListRequest',
        response_type_name='ListHl7V2StoresResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the HL7v2 store.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Hl7V2Store) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='hl7V2Store',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresPatchRequest',
        response_type_name='Hl7V2Store',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}:setIamPolicy',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (HealthcareProjectsLocationsDatasetsHl7V2StoresTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/hl7V2Stores/{hl7V2StoresId}:testIamPermissions',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.hl7V2Stores.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsHl7V2StoresTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_operations resource."""

    _NAME = 'projects_locations_datasets_operations'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (HealthcareProjectsLocationsDatasetsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (HealthcareProjectsLocationsDatasetsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/operations/{operationsId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (HealthcareProjectsLocationsDatasetsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/operations',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+name}/operations',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets resource."""

    _NAME = 'projects_locations_datasets'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsDatasetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new health dataset. Results are returned through the Operation interface which returns either an `Operation.response` which contains a Dataset or `Operation.error`. The metadata field type is OperationMetadata.

      Args:
        request: (HealthcareProjectsLocationsDatasetsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['datasetId'],
        relative_path='v1beta1/{+parent}/datasets',
        request_field='dataset',
        request_type_name='HealthcareProjectsLocationsDatasetsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Deidentify(self, request, global_params=None):
      r"""Creates a new dataset containing de-identified data from the source dataset. The metadata field type is OperationMetadata. If the request is successful, the response field type is DeidentifySummary. The LRO result may still be successful if de-identification fails for some resources. The new de-identified dataset will not contain these failed resources. The number of resources processed are tracked in Operation.metadata. Error details are logged to Cloud Logging. For more information, see [Viewing error logs in Cloud Logging](https://cloud.google.com/healthcare/docs/how-tos/logging).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDeidentifyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Deidentify')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deidentify.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:deidentify',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.deidentify',
        ordered_params=['sourceDataset'],
        path_params=['sourceDataset'],
        query_params=[],
        relative_path='v1beta1/{+sourceDataset}:deidentify',
        request_field='deidentifyDatasetRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsDeidentifyRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified health dataset and all data contained in the dataset. Deleting a dataset does not affect the sources from which the dataset was imported (if any).

      Args:
        request: (HealthcareProjectsLocationsDatasetsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}',
        http_method='DELETE',
        method_id='healthcare.projects.locations.datasets.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets any metadata associated with a dataset.

      Args:
        request: (HealthcareProjectsLocationsDatasetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Dataset) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsGetRequest',
        response_type_name='Dataset',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (HealthcareProjectsLocationsDatasetsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:getIamPolicy',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1beta1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the health datasets in the current project.

      Args:
        request: (HealthcareProjectsLocationsDatasetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDatasetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets',
        http_method='GET',
        method_id='healthcare.projects.locations.datasets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1beta1/{+parent}/datasets',
        request_field='',
        request_type_name='HealthcareProjectsLocationsDatasetsListRequest',
        response_type_name='ListDatasetsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates dataset metadata.

      Args:
        request: (HealthcareProjectsLocationsDatasetsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Dataset) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}',
        http_method='PATCH',
        method_id='healthcare.projects.locations.datasets.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1beta1/{+name}',
        request_field='dataset',
        request_type_name='HealthcareProjectsLocationsDatasetsPatchRequest',
        response_type_name='Dataset',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (HealthcareProjectsLocationsDatasetsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:setIamPolicy',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (HealthcareProjectsLocationsDatasetsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:testIamPermissions',
        http_method='POST',
        method_id='healthcare.projects.locations.datasets.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1beta1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='HealthcareProjectsLocationsDatasetsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsServicesNlpService(base_api.BaseApiService):
    """Service class for the projects_locations_services_nlp resource."""

    _NAME = 'projects_locations_services_nlp'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsServicesNlpService, self).__init__(client)
      self._upload_configs = {
          }

    def AnalyzeEntities(self, request, global_params=None):
      r"""Analyze heathcare entity in a document. Its response includes the recognized entity mentions and the relationships between them. AnalyzeEntities uses context aware models to detect entities. This method can only analyze documents written in English.

      Args:
        request: (HealthcareProjectsLocationsServicesNlpAnalyzeEntitiesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AnalyzeEntitiesResponse) The response message.
      """
      config = self.GetMethodConfig('AnalyzeEntities')
      return self._RunMethod(
          config, request, global_params=global_params)

    AnalyzeEntities.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}/services/nlp:analyzeEntities',
        http_method='POST',
        method_id='healthcare.projects.locations.services.nlp.analyzeEntities',
        ordered_params=['nlpService'],
        path_params=['nlpService'],
        query_params=[],
        relative_path='v1beta1/{+nlpService}:analyzeEntities',
        request_field='analyzeEntitiesRequest',
        request_type_name='HealthcareProjectsLocationsServicesNlpAnalyzeEntitiesRequest',
        response_type_name='AnalyzeEntitiesResponse',
        supports_download=False,
    )

  class ProjectsLocationsServicesService(base_api.BaseApiService):
    """Service class for the projects_locations_services resource."""

    _NAME = 'projects_locations_services'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsServicesService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (HealthcareProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='healthcare.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1beta1/{+name}',
        request_field='',
        request_type_name='HealthcareProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (HealthcareProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1beta1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='healthcare.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1beta1/{+name}/locations',
        request_field='',
        request_type_name='HealthcareProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(HealthcareV1beta1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

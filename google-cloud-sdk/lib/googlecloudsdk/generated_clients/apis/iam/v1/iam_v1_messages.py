"""Generated message classes for iam version v1.

Manages identity and access control for Google Cloud Platform resources,
including the creation of service accounts, which you can use to authenticate
to Google and make API calls.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'iam'


class AccessRestrictions(_messages.Message):
  r"""Access related restrictions on the workforce pool.

  Fields:
    allowedServices: Optional. Services allowed for web sign-in with the
      workforce pool. If not set by default there are no restrictions.
    disableProgrammaticSignin: Optional. Disable programmatic sign-in by
      disabling token issue via the Security Token API endpoint. See [Security
      Token Service API]
      (https://cloud.google.com/iam/docs/reference/sts/rest).
  """

  allowedServices = _messages.MessageField('ServiceConfig', 1, repeated=True)
  disableProgrammaticSignin = _messages.BooleanField(2)


class AdminAuditData(_messages.Message):
  r"""Audit log information specific to Cloud IAM admin APIs. This message is
  serialized as an `Any` type in the `ServiceData` message of an `AuditLog`
  message.

  Fields:
    permissionDelta: The permission_delta when when creating or updating a
      Role.
  """

  permissionDelta = _messages.MessageField('PermissionDelta', 1)


class AttributeTranslatorCEL(_messages.Message):
  r"""Specifies a list of output attribute names and the corresponding input
  attribute to use for that output attribute. Each defined output attribute is
  populated with the value of the specified input attribute.

  Messages:
    AttributesValue: Each entry specifies the desired output attribute and a
      CEL field selector expression for the corresponding input to read. This
      field supports a subset of the CEL functionality to select fields from
      the input (no boolean expressions, functions or arithmetics). Output
      attributes must match `(google.sub|a-z_*)`. The output attribute
      google.sub is interpreted to be the "identity" of the requesting user.
      For example, to copy the inbound attribute "sub" into the output
      `google.sub` add an entry `google.sub` -> `inclaim.sub` or `google.sub`
      -> `inclaim[\"sub\"]`. See https://github.com/google/cel-spec for more
      details. If the input does not exist the output attribute will be null.

  Fields:
    attributes: Each entry specifies the desired output attribute and a CEL
      field selector expression for the corresponding input to read. This
      field supports a subset of the CEL functionality to select fields from
      the input (no boolean expressions, functions or arithmetics). Output
      attributes must match `(google.sub|a-z_*)`. The output attribute
      google.sub is interpreted to be the "identity" of the requesting user.
      For example, to copy the inbound attribute "sub" into the output
      `google.sub` add an entry `google.sub` -> `inclaim.sub` or `google.sub`
      -> `inclaim[\"sub\"]`. See https://github.com/google/cel-spec for more
      details. If the input does not exist the output attribute will be null.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributesValue(_messages.Message):
    r"""Each entry specifies the desired output attribute and a CEL field
    selector expression for the corresponding input to read. This field
    supports a subset of the CEL functionality to select fields from the input
    (no boolean expressions, functions or arithmetics). Output attributes must
    match `(google.sub|a-z_*)`. The output attribute google.sub is interpreted
    to be the "identity" of the requesting user. For example, to copy the
    inbound attribute "sub" into the output `google.sub` add an entry
    `google.sub` -> `inclaim.sub` or `google.sub` -> `inclaim[\"sub\"]`. See
    https://github.com/google/cel-spec for more details. If the input does not
    exist the output attribute will be null.

    Messages:
      AdditionalProperty: An additional property for a AttributesValue object.

    Fields:
      additionalProperties: Additional properties of type AttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributes = _messages.MessageField('AttributesValue', 1)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditData(_messages.Message):
  r"""Audit log information specific to Cloud IAM. This message is serialized
  as an `Any` type in the `ServiceData` message of an `AuditLog` message.

  Fields:
    policyDelta: Policy delta between the original policy and the newly set
      policy.
  """

  policyDelta = _messages.MessageField('PolicyDelta', 1)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class AuditableService(_messages.Message):
  r"""Contains information about an auditable service.

  Fields:
    name: Public name of the service. For example, the service name for Cloud
      IAM is 'iam.googleapis.com'.
  """

  name = _messages.StringField(1)


class Aws(_messages.Message):
  r"""Represents an Amazon Web Services identity provider.

  Fields:
    accountId: Required. The AWS account ID.
  """

  accountId = _messages.StringField(1)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class BindingDelta(_messages.Message):
  r"""One delta entry for Binding. Each individual change (only one member in
  each entry) to a binding will be a separate entry.

  Enums:
    ActionValueValuesEnum: The action that was performed on a Binding.
      Required

  Fields:
    action: The action that was performed on a Binding. Required
    condition: The condition that is associated with this binding.
    member: A single identity requesting access for a Google Cloud resource.
      Follows the same format of Binding.members. Required
    role: Role that is assigned to `members`. For example, `roles/viewer`,
      `roles/editor`, or `roles/owner`. Required
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""The action that was performed on a Binding. Required

    Values:
      ACTION_UNSPECIFIED: Unspecified.
      ADD: Addition of a Binding.
      REMOVE: Removal of a Binding.
    """
    ACTION_UNSPECIFIED = 0
    ADD = 1
    REMOVE = 2

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  condition = _messages.MessageField('Expr', 2)
  member = _messages.StringField(3)
  role = _messages.StringField(4)


class CreateRoleRequest(_messages.Message):
  r"""The request to create a new role.

  Fields:
    role: The Role resource to create.
    roleId: The role ID to use for this role. A role ID may contain
      alphanumeric characters, underscores (`_`), and periods (`.`). It must
      contain a minimum of 3 characters and a maximum of 64 characters.
  """

  role = _messages.MessageField('Role', 1)
  roleId = _messages.StringField(2)


class CreateServiceAccountIdentityBindingRequest(_messages.Message):
  r"""The service account identity binding create request.

  Fields:
    acceptanceFilter: A CEL expression that is evaluated to determine whether
      a credential should be accepted. To accept any credential, specify
      "true". See: https://github.com/google/cel-spec . The input claims are
      available using "inclaim[\"attribute_name\"]". The output attributes
      calculated by the translator are available using
      "outclaim[\"attribute_name\"]"
    cel: A set of output attributes and corresponding input attribute names.
    oidc: An OIDC reference with Discovery.
  """

  acceptanceFilter = _messages.StringField(1)
  cel = _messages.MessageField('AttributeTranslatorCEL', 2)
  oidc = _messages.MessageField('IDPReferenceOIDC', 3)


class CreateServiceAccountKeyRequest(_messages.Message):
  r"""The service account key create request.

  Enums:
    KeyAlgorithmValueValuesEnum: Which type of key and algorithm to use for
      the key. The default is currently a 2K RSA key. However this may change
      in the future.
    PrivateKeyTypeValueValuesEnum: The output format of the private key. The
      default value is `TYPE_GOOGLE_CREDENTIALS_FILE`, which is the Google
      Credentials File format.

  Fields:
    keyAlgorithm: Which type of key and algorithm to use for the key. The
      default is currently a 2K RSA key. However this may change in the
      future.
    privateKeyType: The output format of the private key. The default value is
      `TYPE_GOOGLE_CREDENTIALS_FILE`, which is the Google Credentials File
      format.
  """

  class KeyAlgorithmValueValuesEnum(_messages.Enum):
    r"""Which type of key and algorithm to use for the key. The default is
    currently a 2K RSA key. However this may change in the future.

    Values:
      KEY_ALG_UNSPECIFIED: An unspecified key algorithm.
      KEY_ALG_RSA_1024: 1k RSA Key.
      KEY_ALG_RSA_2048: 2k RSA Key.
    """
    KEY_ALG_UNSPECIFIED = 0
    KEY_ALG_RSA_1024 = 1
    KEY_ALG_RSA_2048 = 2

  class PrivateKeyTypeValueValuesEnum(_messages.Enum):
    r"""The output format of the private key. The default value is
    `TYPE_GOOGLE_CREDENTIALS_FILE`, which is the Google Credentials File
    format.

    Values:
      TYPE_UNSPECIFIED: Unspecified. Equivalent to
        `TYPE_GOOGLE_CREDENTIALS_FILE`.
      TYPE_PKCS12_FILE: PKCS12 format. The password for the PKCS12 file is
        `notasecret`. For more information, see
        https://tools.ietf.org/html/rfc7292.
      TYPE_GOOGLE_CREDENTIALS_FILE: Google Credentials File format.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PKCS12_FILE = 1
    TYPE_GOOGLE_CREDENTIALS_FILE = 2

  keyAlgorithm = _messages.EnumField('KeyAlgorithmValueValuesEnum', 1)
  privateKeyType = _messages.EnumField('PrivateKeyTypeValueValuesEnum', 2)


class CreateServiceAccountRequest(_messages.Message):
  r"""The service account create request.

  Fields:
    accountId: Required. The account id that is used to generate the service
      account email address and a stable unique id. It is unique within a
      project, must be 6-30 characters long, and match the regular expression
      `[a-z]([-a-z0-9]*[a-z0-9])` to comply with RFC1035.
    serviceAccount: The ServiceAccount resource to create. Currently, only the
      following values are user assignable: `display_name` and `description`.
  """

  accountId = _messages.StringField(1)
  serviceAccount = _messages.MessageField('ServiceAccount', 2)


class DisableServiceAccountKeyRequest(_messages.Message):
  r"""The service account key disable request."""


class DisableServiceAccountRequest(_messages.Message):
  r"""The service account disable request."""


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EnableServiceAccountKeyRequest(_messages.Message):
  r"""The service account key enable request."""


class EnableServiceAccountRequest(_messages.Message):
  r"""The service account enable request."""


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GetIamPolicyRequest(_messages.Message):
  r"""Request message for `GetIamPolicy` method.

  Fields:
    options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
      `GetIamPolicy`.
  """

  options = _messages.MessageField('GetPolicyOptions', 1)


class GetPolicyOptions(_messages.Message):
  r"""Encapsulates settings provided to GetIamPolicy.

  Fields:
    requestedPolicyVersion: Optional. The maximum policy version that will be
      used to format the policy. Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected. Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset. The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1. To learn which resources support conditions in
      their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GoogleIamAdminV1WorkforcePoolProviderOidc(_messages.Message):
  r"""Represents an OpenId Connect 1.0 identity provider.

  Fields:
    clientId: Required. The client ID. Must match the audience claim of the
      JWT issued by the identity provider.
    clientSecret: The optional client secret. Required to enable Authorization
      Code flow for web sign-in.
    issuerUri: Required. The OIDC issuer URI. Must be a valid URI using the
      'https' scheme.
    jwksJson: OIDC JWKs in JSON String format. For details on the definition
      of a JWK, see https://tools.ietf.org/html/rfc7517. If not set, the
      `jwks_uri` from the discovery document(fetched from the .well-known path
      of the `issuer_uri`) will be used. Currently, RSA and EC asymmetric keys
      are supported. The JWK must use following format and include only the
      following fields: { "keys": [ { "kty": "RSA/EC", "alg": "", "use":
      "sig", "kid": "", "n": "", "e": "", "x": "", "y": "", "crv": "" } ] }
    webSsoConfig: Required. Configuration for web single sign-on for the OIDC
      provider. Here, web sign-in refers to console sign-in and gcloud sign-in
      through the browser.
  """

  clientId = _messages.StringField(1)
  clientSecret = _messages.MessageField('GoogleIamAdminV1WorkforcePoolProviderOidcClientSecret', 2)
  issuerUri = _messages.StringField(3)
  jwksJson = _messages.StringField(4)
  webSsoConfig = _messages.MessageField('GoogleIamAdminV1WorkforcePoolProviderOidcWebSsoConfig', 5)


class GoogleIamAdminV1WorkforcePoolProviderOidcClientSecret(_messages.Message):
  r"""Representation of a client secret configured for the OIDC provider.

  Fields:
    value: The value of the client secret.
  """

  value = _messages.MessageField('GoogleIamAdminV1WorkforcePoolProviderOidcClientSecretValue', 1)


class GoogleIamAdminV1WorkforcePoolProviderOidcClientSecretValue(_messages.Message):
  r"""Representation of the value of the client secret.

  Fields:
    plainText: Input only. The plain text of the client secret value. For
      security reasons, this field is only used for input and will never be
      populated in any response.
    thumbprint: Output only. A thumbprint to represent the current client
      secret value.
  """

  plainText = _messages.StringField(1)
  thumbprint = _messages.StringField(2)


class GoogleIamAdminV1WorkforcePoolProviderOidcWebSsoConfig(_messages.Message):
  r"""Configuration for web single sign-on for the OIDC provider.

  Enums:
    AssertionClaimsBehaviorValueValuesEnum: Required. The behavior for how
      OIDC Claims are included in the `assertion` object used for attribute
      mapping and attribute condition.
    ResponseTypeValueValuesEnum: Required. The Response Type to request for in
      the OIDC Authorization Request for web sign-in. The `CODE` Response Type
      is recommended to avoid the Implicit Flow, for security reasons.

  Fields:
    additionalScopes: Additional scopes to request for in the OIDC
      authentication request on top of scopes requested by default. By
      default, the `openid`, `profile` and `email` scopes that are supported
      by the identity provider are requested. Each additional scope may be at
      most 256 characters. A maximum of 10 additional scopes may be
      configured.
    assertionClaimsBehavior: Required. The behavior for how OIDC Claims are
      included in the `assertion` object used for attribute mapping and
      attribute condition.
    responseType: Required. The Response Type to request for in the OIDC
      Authorization Request for web sign-in. The `CODE` Response Type is
      recommended to avoid the Implicit Flow, for security reasons.
  """

  class AssertionClaimsBehaviorValueValuesEnum(_messages.Enum):
    r"""Required. The behavior for how OIDC Claims are included in the
    `assertion` object used for attribute mapping and attribute condition.

    Values:
      ASSERTION_CLAIMS_BEHAVIOR_UNSPECIFIED: No assertion claims behavior
        specified.
      MERGE_USER_INFO_OVER_ID_TOKEN_CLAIMS: Merge the UserInfo Endpoint Claims
        with ID Token Claims, preferring UserInfo Claim Values for the same
        Claim Name. This option is available only for the Authorization Code
        Flow.
      ONLY_ID_TOKEN_CLAIMS: Only include ID Token Claims.
    """
    ASSERTION_CLAIMS_BEHAVIOR_UNSPECIFIED = 0
    MERGE_USER_INFO_OVER_ID_TOKEN_CLAIMS = 1
    ONLY_ID_TOKEN_CLAIMS = 2

  class ResponseTypeValueValuesEnum(_messages.Enum):
    r"""Required. The Response Type to request for in the OIDC Authorization
    Request for web sign-in. The `CODE` Response Type is recommended to avoid
    the Implicit Flow, for security reasons.

    Values:
      RESPONSE_TYPE_UNSPECIFIED: No Response Type specified.
      CODE: The `response_type=code` selection uses the Authorization Code
        Flow for web sign-in. Requires a configured client secret.
      ID_TOKEN: The `response_type=id_token` selection uses the Implicit Flow
        for web sign-in.
    """
    RESPONSE_TYPE_UNSPECIFIED = 0
    CODE = 1
    ID_TOKEN = 2

  additionalScopes = _messages.StringField(1, repeated=True)
  assertionClaimsBehavior = _messages.EnumField('AssertionClaimsBehaviorValueValuesEnum', 2)
  responseType = _messages.EnumField('ResponseTypeValueValuesEnum', 3)


class GoogleIamAdminV1WorkforcePoolProviderSaml(_messages.Message):
  r"""Represents a SAML identity provider.

  Fields:
    idpMetadataXml: Required. SAML Identity provider configuration metadata
      xml doc. The xml document should comply with [SAML 2.0
      specification](https://docs.oasis-open.org/security/saml/v2.0/saml-
      metadata-2.0-os.pdf). The max size of the acceptable xml document will
      be bounded to 128k characters. The metadata xml document should satisfy
      the following constraints: 1) Must contain an Identity Provider Entity
      ID. 2) Must contain at least one non-expired signing key certificate. 3)
      For each signing key: a) Valid from should be no more than 7 days from
      now. b) Valid to should be no more than 14 years in the future. 4) Up to
      3 IdP signing keys are allowed in the metadata xml. When updating the
      provider's metadata xml, at least one non-expired signing key must
      overlap with the existing metadata. This requirement is skipped if there
      are no non-expired signing keys present in the existing metadata.
  """

  idpMetadataXml = _messages.StringField(1)


class IDPReferenceOIDC(_messages.Message):
  r"""Represents a reference to an OIDC provider.

  Fields:
    audience: Optional. The acceptable audience. Default is the unique_id of
      the Service Account.
    maxTokenLifetimeSeconds: This optional field allows enforcing a maximum
      lifetime for tokens. Using a lifetime that is as short as possible
      improves security since it prevents use of exfiltrated tokens after a
      certain amount of time. All tokens must specify both exp and iat or they
      will be rejected. If "nbf" is present we will reject tokens that are not
      yet valid. Expiration and lifetime will be enforced in the following
      way: - "exp" > "current time" is always required (expired tokens are
      rejected) - "iat" < "current time" + 300 seconds is required (tokens
      from the future . are rejected although a small amount of clock skew is
      tolerated). - If max_token_lifetime_seconds is set: "exp" - "iat" <
      max_token_lifetime_seconds will be checked - The default is otherwise to
      accept a max_token_lifetime_seconds of 3600 (1 hour)
    oidcJwks: Optional. OIDC verification keys in JWKS format (RFC 7517). It
      contains a list of OIDC verification keys that can be used to verify
      OIDC JWTs. When OIDC verification key is provided, it will be directly
      used to verify the OIDC JWT asserted by the IDP.
    url: The OpenID Connect URL. To use this Identity Binding, JWT 'iss' field
      should match this field. When URL is set, public keys will be fetched
      from the provided URL for credentials verification unless `oidc_jwks`
      field is set.
  """

  audience = _messages.StringField(1)
  maxTokenLifetimeSeconds = _messages.IntegerField(2)
  oidcJwks = _messages.BytesField(3)
  url = _messages.StringField(4)


class IamLocationsWorkforcePoolsCreateRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsCreateRequest object.

  Fields:
    location: The location of the pool to create. Format:
      `locations/{location}`.
    workforcePool: A WorkforcePool resource to be passed as the request body.
    workforcePoolId: The ID to use for the pool, which becomes the final
      component of the resource name. The IDs must be a globally unique string
      of 6 to 63 lowercase letters, digits, or hyphens. It must start with a
      letter, and cannot have a trailing hyphen. The prefix `gcp-` is reserved
      for use by Google, and may not be specified.
  """

  location = _messages.StringField(1, required=True)
  workforcePool = _messages.MessageField('WorkforcePool', 2)
  workforcePoolId = _messages.StringField(3)


class IamLocationsWorkforcePoolsDeleteRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsDeleteRequest object.

  Fields:
    name: Required. The name of the pool to delete. Format:
      `locations/{location}/workforcePools/{workforce_pool_id}`
  """

  name = _messages.StringField(1, required=True)


class IamLocationsWorkforcePoolsGetIamPolicyRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class IamLocationsWorkforcePoolsGetRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsGetRequest object.

  Fields:
    name: Required. The name of the pool to retrieve. Format:
      `locations/{location}/workforcePools/{workforce_pool_id}`
  """

  name = _messages.StringField(1, required=True)


class IamLocationsWorkforcePoolsInstalledAppsCreateRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsInstalledAppsCreateRequest object.

  Fields:
    parent: Required. The pool to create this workforce pool installed app in.
      Format: `locations/{location}/workforcePools/{workforce_pool}`
    workforcePoolInstalledApp: A WorkforcePoolInstalledApp resource to be
      passed as the request body.
    workforcePoolInstalledAppId: Required. The ID to use for the workforce
      pool installed app, which becomes the final component of the resource
      name. This value should be 4-32 characters, and may contain the
      characters [a-z0-9-]. The prefix `gcp-` is reserved for use by Google,
      and may not be specified.
  """

  parent = _messages.StringField(1, required=True)
  workforcePoolInstalledApp = _messages.MessageField('WorkforcePoolInstalledApp', 2)
  workforcePoolInstalledAppId = _messages.StringField(3)


class IamLocationsWorkforcePoolsInstalledAppsDeleteRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsInstalledAppsDeleteRequest object.

  Fields:
    name: Required. The name of the workforce pool installed app to delete.
      Format: `locations/{location}/workforcePools/{workforce_pool}/installedA
      pps/{installed_app}`
    validateOnly: Optional. If set, validate the request and preview the
      response, but do not actually post it.
  """

  name = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)


class IamLocationsWorkforcePoolsInstalledAppsGetRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsInstalledAppsGetRequest object.

  Fields:
    name: Required. The name of the workforce pool installed app to retrieve.
      Format: `locations/{location}/workforcePools/{workforce_pool}/installedA
      pps/{installed_app}`
  """

  name = _messages.StringField(1, required=True)


class IamLocationsWorkforcePoolsInstalledAppsListRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsInstalledAppsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of workforce pool installed apps to
      return. If unspecified, at most 50 workforce pool installed apps will be
      returned. The maximum value is 1000; values above 1000 are truncated to
      1000.
    pageToken: Optional. A page token, received from a previous
      `ListWorkforcePoolInstalledApps` call. Provide this to retrieve the
      subsequent page.
    parent: Required. The parent to list installed apps, format:
      'locations/{location}/workforcePools/{workforce_pool}'
    showDeleted: Optional. Whether to return soft-deleted workforce pool
      installed apps.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class IamLocationsWorkforcePoolsInstalledAppsPatchRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsInstalledAppsPatchRequest object.

  Fields:
    name: Immutable. The resource name of the workforce pool installed app.
      Format: `locations/{location}/workforcePools/{workforce_pool}/installedA
      pps/{installed_app}`
    updateMask: Required. The list of fields to update.
    workforcePoolInstalledApp: A WorkforcePoolInstalledApp resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  workforcePoolInstalledApp = _messages.MessageField('WorkforcePoolInstalledApp', 3)


class IamLocationsWorkforcePoolsInstalledAppsUndeleteRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsInstalledAppsUndeleteRequest object.

  Fields:
    name: Required. The name of the workforce pool installed app to undelete.
      Format: `locations/{location}/workforcePools/{workforce_pool}/installedA
      pps/{installed_app}`
    undeleteWorkforcePoolInstalledAppRequest: A
      UndeleteWorkforcePoolInstalledAppRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteWorkforcePoolInstalledAppRequest = _messages.MessageField('UndeleteWorkforcePoolInstalledAppRequest', 2)


class IamLocationsWorkforcePoolsListRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsListRequest object.

  Fields:
    location: The location of the pool. Format: `locations/{location}`.
    pageSize: The maximum number of pools to return. If unspecified, at most
      50 pools will be returned. The maximum value is 1000; values above 1000
      are truncated to 1000.
    pageToken: A page token, received from a previous `ListWorkforcePools`
      call. Provide this to retrieve the subsequent page.
    parent: Required. The parent resource to list pools for. Format:
      `organizations/{org-id}`.
    showDeleted: Whether to return soft-deleted pools.
  """

  location = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4)
  showDeleted = _messages.BooleanField(5)


class IamLocationsWorkforcePoolsOperationsGetRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IamLocationsWorkforcePoolsPatchRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsPatchRequest object.

  Fields:
    name: Output only. The resource name of the pool. Format:
      `locations/{location}/workforcePools/{workforce_pool_id}`
    updateMask: Required. The list of fields to update.
    workforcePool: A WorkforcePool resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  workforcePool = _messages.MessageField('WorkforcePool', 3)


class IamLocationsWorkforcePoolsProvidersCreateRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsProvidersCreateRequest object.

  Fields:
    parent: Required. The pool to create this provider in. Format:
      `locations/{location}/workforcePools/{workforce_pool_id}`
    workforcePoolProvider: A WorkforcePoolProvider resource to be passed as
      the request body.
    workforcePoolProviderId: Required. The ID for the provider, which becomes
      the final component of the resource name. This value must be 4-32
      characters, and may contain the characters [a-z0-9-]. The prefix `gcp-`
      is reserved for use by Google, and may not be specified.
  """

  parent = _messages.StringField(1, required=True)
  workforcePoolProvider = _messages.MessageField('WorkforcePoolProvider', 2)
  workforcePoolProviderId = _messages.StringField(3)


class IamLocationsWorkforcePoolsProvidersDeleteRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsProvidersDeleteRequest object.

  Fields:
    name: Required. The name of the provider to delete. Format: `locations/{lo
      cation}/workforcePools/{workforce_pool_id}/providers/{provider_id}`
  """

  name = _messages.StringField(1, required=True)


class IamLocationsWorkforcePoolsProvidersGetRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsProvidersGetRequest object.

  Fields:
    name: Required. The name of the provider to retrieve. Format: `locations/{
      location}/workforcePools/{workforce_pool_id}/providers/{provider_id}`
  """

  name = _messages.StringField(1, required=True)


class IamLocationsWorkforcePoolsProvidersKeysCreateRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsProvidersKeysCreateRequest object.

  Fields:
    parent: Required. The provider to create this key in.
    workforcePoolProviderKey: A WorkforcePoolProviderKey resource to be passed
      as the request body.
    workforcePoolProviderKeyId: Required. The ID to use for the key, which
      becomes the final component of the resource name. This value must be
      4-32 characters, and may contain the characters [a-z0-9-].
  """

  parent = _messages.StringField(1, required=True)
  workforcePoolProviderKey = _messages.MessageField('WorkforcePoolProviderKey', 2)
  workforcePoolProviderKeyId = _messages.StringField(3)


class IamLocationsWorkforcePoolsProvidersKeysDeleteRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsProvidersKeysDeleteRequest object.

  Fields:
    name: Required. The name of the key to delete.
  """

  name = _messages.StringField(1, required=True)


class IamLocationsWorkforcePoolsProvidersKeysGetRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsProvidersKeysGetRequest object.

  Fields:
    name: Required. The name of the key to retrieve.
  """

  name = _messages.StringField(1, required=True)


class IamLocationsWorkforcePoolsProvidersKeysListRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsProvidersKeysListRequest object.

  Fields:
    pageSize: The maximum number of keys to return. If unspecified, all keys
      are returned. The maximum value is 10; values above 10 are truncated to
      10.
    pageToken: A page token, received from a previous
      `ListWorkforcePoolProviderKeys` call. Provide this to retrieve the
      subsequent page.
    parent: Required. The provider resource to list encryption keys for.
      Format: `locations/{location}/workforcePools/{workforce_pool_id}/provide
      rs/{provider_id}`
    showDeleted: Whether to return soft-deleted keys.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class IamLocationsWorkforcePoolsProvidersKeysOperationsGetRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsProvidersKeysOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IamLocationsWorkforcePoolsProvidersKeysUndeleteRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsProvidersKeysUndeleteRequest object.

  Fields:
    name: Required. The name of the key to undelete.
    undeleteWorkforcePoolProviderKeyRequest: A
      UndeleteWorkforcePoolProviderKeyRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteWorkforcePoolProviderKeyRequest = _messages.MessageField('UndeleteWorkforcePoolProviderKeyRequest', 2)


class IamLocationsWorkforcePoolsProvidersListRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsProvidersListRequest object.

  Fields:
    pageSize: The maximum number of providers to return. If unspecified, at
      most 50 providers are returned. The maximum value is 100; values above
      100 are truncated to 100.
    pageToken: A page token, received from a previous
      `ListWorkforcePoolProviders` call. Provide this to retrieve the
      subsequent page.
    parent: Required. The pool to list providers for. Format:
      `locations/{location}/workforcePools/{workforce_pool_id}`
    showDeleted: Whether to return soft-deleted providers.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class IamLocationsWorkforcePoolsProvidersOperationsGetRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsProvidersOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IamLocationsWorkforcePoolsProvidersPatchRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsProvidersPatchRequest object.

  Fields:
    name: Output only. The resource name of the provider. Format: `locations/{
      location}/workforcePools/{workforce_pool_id}/providers/{provider_id}`
    updateMask: Required. The list of fields to update.
    workforcePoolProvider: A WorkforcePoolProvider resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  workforcePoolProvider = _messages.MessageField('WorkforcePoolProvider', 3)


class IamLocationsWorkforcePoolsProvidersUndeleteRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsProvidersUndeleteRequest object.

  Fields:
    name: Required. The name of the provider to undelete. Format: `locations/{
      location}/workforcePools/{workforce_pool_id}/providers/{provider_id}`
    undeleteWorkforcePoolProviderRequest: A
      UndeleteWorkforcePoolProviderRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteWorkforcePoolProviderRequest = _messages.MessageField('UndeleteWorkforcePoolProviderRequest', 2)


class IamLocationsWorkforcePoolsSetIamPolicyRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class IamLocationsWorkforcePoolsSubjectsDeleteRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsSubjectsDeleteRequest object.

  Fields:
    name: Required. The resource name of the WorkforcePoolSubject. Special
      characters, like '/' and ':', must be escaped, because all URLs need to
      conform to the "When to Escape and Unescape" section of
      [RFC3986](https://www.ietf.org/rfc/rfc2396.txt). Format: `locations/{loc
      ation}/workforcePools/{workforce_pool_id}/subjects/{subject_id}`
  """

  name = _messages.StringField(1, required=True)


class IamLocationsWorkforcePoolsSubjectsOperationsGetRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsSubjectsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IamLocationsWorkforcePoolsSubjectsUndeleteRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsSubjectsUndeleteRequest object.

  Fields:
    name: Required. The resource name of the WorkforcePoolSubject. Special
      characters, like '/' and ':', must be escaped, because all URLs need to
      conform to the "When to Escape and Unescape" section of
      [RFC3986](https://www.ietf.org/rfc/rfc2396.txt). Format: `locations/{loc
      ation}/workforcePools/{workforce_pool_id}/subjects/{subject_id}`
    undeleteWorkforcePoolSubjectRequest: A UndeleteWorkforcePoolSubjectRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteWorkforcePoolSubjectRequest = _messages.MessageField('UndeleteWorkforcePoolSubjectRequest', 2)


class IamLocationsWorkforcePoolsTestIamPermissionsRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class IamLocationsWorkforcePoolsUndeleteRequest(_messages.Message):
  r"""A IamLocationsWorkforcePoolsUndeleteRequest object.

  Fields:
    name: Required. The name of the pool to undelete. Format:
      `locations/{location}/workforcePools/{workforce_pool_id}`
    undeleteWorkforcePoolRequest: A UndeleteWorkforcePoolRequest resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteWorkforcePoolRequest = _messages.MessageField('UndeleteWorkforcePoolRequest', 2)


class IamOrganizationsRolesCreateRequest(_messages.Message):
  r"""A IamOrganizationsRolesCreateRequest object.

  Fields:
    createRoleRequest: A CreateRoleRequest resource to be passed as the
      request body.
    parent: The `parent` parameter's value depends on the target resource for
      the request, namely [`projects`](https://cloud.google.com/iam/reference/
      rest/v1/projects.roles) or [`organizations`](https://cloud.google.com/ia
      m/reference/rest/v1/organizations.roles). Each resource type's `parent`
      value format is described below: * [`projects.roles.create()`](https://c
      loud.google.com/iam/reference/rest/v1/projects.roles/create):
      `projects/{PROJECT_ID}`. This method creates project-level [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles).
      Example request URL:
      `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles` * [`organiza
      tions.roles.create()`](https://cloud.google.com/iam/reference/rest/v1/or
      ganizations.roles/create): `organizations/{ORGANIZATION_ID}`. This
      method creates organization-level [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles).
      Example request URL:
      `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles`
      Note: Wildcard (*) values are invalid; you must specify a complete
      project ID or organization ID.
  """

  createRoleRequest = _messages.MessageField('CreateRoleRequest', 1)
  parent = _messages.StringField(2, required=True)


class IamOrganizationsRolesDeleteRequest(_messages.Message):
  r"""A IamOrganizationsRolesDeleteRequest object.

  Fields:
    etag: Used to perform a consistent read-modify-write.
    name: The `name` parameter's value depends on the target resource for the
      request, namely [`projects`](https://cloud.google.com/iam/reference/rest
      /v1/projects.roles) or [`organizations`](https://cloud.google.com/iam/re
      ference/rest/v1/organizations.roles). Each resource type's `name` value
      format is described below: * [`projects.roles.delete()`](https://cloud.g
      oogle.com/iam/reference/rest/v1/projects.roles/delete):
      `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method deletes only
      [custom roles](https://cloud.google.com/iam/docs/understanding-custom-
      roles) that have been created at the project level. Example request URL:
      `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_
      ID}` * [`organizations.roles.delete()`](https://cloud.google.com/iam/ref
      erence/rest/v1/organizations.roles/delete):
      `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method
      deletes only [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles)
      that have been created at the organization level. Example request URL: `
      https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUS
      TOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a
      complete project ID or organization ID.
  """

  etag = _messages.BytesField(1)
  name = _messages.StringField(2, required=True)


class IamOrganizationsRolesGetRequest(_messages.Message):
  r"""A IamOrganizationsRolesGetRequest object.

  Fields:
    name: The `name` parameter's value depends on the target resource for the
      request, namely
      [`roles`](https://cloud.google.com/iam/reference/rest/v1/roles), [`proje
      cts`](https://cloud.google.com/iam/reference/rest/v1/projects.roles), or
      [`organizations`](https://cloud.google.com/iam/reference/rest/v1/organiz
      ations.roles). Each resource type's `name` value format is described
      below: * [`roles.get()`](https://cloud.google.com/iam/reference/rest/v1/
      roles/get): `roles/{ROLE_NAME}`. This method returns results from all
      [predefined roles](https://cloud.google.com/iam/docs/understanding-
      roles#predefined_roles) in Cloud IAM. Example request URL:
      `https://iam.googleapis.com/v1/roles/{ROLE_NAME}` * [`projects.roles.get
      ()`](https://cloud.google.com/iam/reference/rest/v1/projects.roles/get):
      `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method returns only
      [custom roles](https://cloud.google.com/iam/docs/understanding-custom-
      roles) that have been created at the project level. Example request URL:
      `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_
      ID}` * [`organizations.roles.get()`](https://cloud.google.com/iam/refere
      nce/rest/v1/organizations.roles/get):
      `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method
      returns only [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles)
      that have been created at the organization level. Example request URL: `
      https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUS
      TOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a
      complete project ID or organization ID.
  """

  name = _messages.StringField(1, required=True)


class IamOrganizationsRolesListRequest(_messages.Message):
  r"""A IamOrganizationsRolesListRequest object.

  Enums:
    ViewValueValuesEnum: Optional view for the returned Role objects. When
      `FULL` is specified, the `includedPermissions` field is returned, which
      includes a list of all permissions in the role. The default value is
      `BASIC`, which does not return the `includedPermissions` field.

  Fields:
    pageSize: Optional limit on the number of roles to include in the
      response. The default is 300, and the maximum is 1,000.
    pageToken: Optional pagination token returned in an earlier
      ListRolesResponse.
    parent: The `parent` parameter's value depends on the target resource for
      the request, namely
      [`roles`](https://cloud.google.com/iam/reference/rest/v1/roles), [`proje
      cts`](https://cloud.google.com/iam/reference/rest/v1/projects.roles), or
      [`organizations`](https://cloud.google.com/iam/reference/rest/v1/organiz
      ations.roles). Each resource type's `parent` value format is described
      below: * [`roles.list()`](https://cloud.google.com/iam/reference/rest/v1
      /roles/list): An empty string. This method doesn't require a resource;
      it simply returns all [predefined
      roles](https://cloud.google.com/iam/docs/understanding-
      roles#predefined_roles) in Cloud IAM. Example request URL:
      `https://iam.googleapis.com/v1/roles` * [`projects.roles.list()`](https:
      //cloud.google.com/iam/reference/rest/v1/projects.roles/list):
      `projects/{PROJECT_ID}`. This method lists all project-level [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles).
      Example request URL:
      `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles` * [`organiza
      tions.roles.list()`](https://cloud.google.com/iam/reference/rest/v1/orga
      nizations.roles/list): `organizations/{ORGANIZATION_ID}`. This method
      lists all organization-level [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles).
      Example request URL:
      `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles`
      Note: Wildcard (*) values are invalid; you must specify a complete
      project ID or organization ID.
    showDeleted: Include Roles that have been deleted.
    view: Optional view for the returned Role objects. When `FULL` is
      specified, the `includedPermissions` field is returned, which includes a
      list of all permissions in the role. The default value is `BASIC`, which
      does not return the `includedPermissions` field.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional view for the returned Role objects. When `FULL` is specified,
    the `includedPermissions` field is returned, which includes a list of all
    permissions in the role. The default value is `BASIC`, which does not
    return the `includedPermissions` field.

    Values:
      BASIC: Omits the `included_permissions` field. This is the default
        value.
      FULL: Returns all fields.
    """
    BASIC = 0
    FULL = 1

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)
  view = _messages.EnumField('ViewValueValuesEnum', 5)


class IamOrganizationsRolesPatchRequest(_messages.Message):
  r"""A IamOrganizationsRolesPatchRequest object.

  Fields:
    name: The `name` parameter's value depends on the target resource for the
      request, namely [`projects`](https://cloud.google.com/iam/reference/rest
      /v1/projects.roles) or [`organizations`](https://cloud.google.com/iam/re
      ference/rest/v1/organizations.roles). Each resource type's `name` value
      format is described below: * [`projects.roles.patch()`](https://cloud.go
      ogle.com/iam/reference/rest/v1/projects.roles/patch):
      `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method updates only
      [custom roles](https://cloud.google.com/iam/docs/understanding-custom-
      roles) that have been created at the project level. Example request URL:
      `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_
      ID}` * [`organizations.roles.patch()`](https://cloud.google.com/iam/refe
      rence/rest/v1/organizations.roles/patch):
      `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method
      updates only [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles)
      that have been created at the organization level. Example request URL: `
      https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUS
      TOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a
      complete project ID or organization ID.
    role: A Role resource to be passed as the request body.
    updateMask: A mask describing which fields in the Role have changed.
  """

  name = _messages.StringField(1, required=True)
  role = _messages.MessageField('Role', 2)
  updateMask = _messages.StringField(3)


class IamOrganizationsRolesUndeleteRequest(_messages.Message):
  r"""A IamOrganizationsRolesUndeleteRequest object.

  Fields:
    name: The `name` parameter's value depends on the target resource for the
      request, namely [`projects`](https://cloud.google.com/iam/reference/rest
      /v1/projects.roles) or [`organizations`](https://cloud.google.com/iam/re
      ference/rest/v1/organizations.roles). Each resource type's `name` value
      format is described below: * [`projects.roles.undelete()`](https://cloud
      .google.com/iam/reference/rest/v1/projects.roles/undelete):
      `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method undeletes
      only [custom roles](https://cloud.google.com/iam/docs/understanding-
      custom-roles) that have been created at the project level. Example
      request URL: `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/
      {CUSTOM_ROLE_ID}` * [`organizations.roles.undelete()`](https://cloud.goo
      gle.com/iam/reference/rest/v1/organizations.roles/undelete):
      `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method
      undeletes only [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles)
      that have been created at the organization level. Example request URL: `
      https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUS
      TOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a
      complete project ID or organization ID.
    undeleteRoleRequest: A UndeleteRoleRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteRoleRequest = _messages.MessageField('UndeleteRoleRequest', 2)


class IamProjectsLocationsOauthClientsCreateRequest(_messages.Message):
  r"""A IamProjectsLocationsOauthClientsCreateRequest object.

  Fields:
    oauthClient: A OauthClient resource to be passed as the request body.
    oauthClientId: Required. The ID to use for the oauth client, which becomes
      the final component of the resource name. This value should be a string
      of 6 to 63 lowercase letters, digits, or hyphens. It must start with a
      letter, and cannot have a trailing hyphen. The prefix `gcp-` is reserved
      for use by Google, and may not be specified.
    parent: Required. The parent resource to create the oauth client in. The
      only supported location is `global`.
  """

  oauthClient = _messages.MessageField('OauthClient', 1)
  oauthClientId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class IamProjectsLocationsOauthClientsCredentialsCreateRequest(_messages.Message):
  r"""A IamProjectsLocationsOauthClientsCredentialsCreateRequest object.

  Fields:
    oauthClientCredential: A OauthClientCredential resource to be passed as
      the request body.
    oauthClientCredentialId: Required. The ID to use for the oauth client
      credential, which becomes the final component of the resource name. This
      value should be 4-32 characters, and may contain the characters
      [a-z0-9-]. The prefix `gcp-` is reserved for use by Google, and may not
      be specified.
    parent: Required. The parent resource to create the oauth client
      Credential in.
  """

  oauthClientCredential = _messages.MessageField('OauthClientCredential', 1)
  oauthClientCredentialId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class IamProjectsLocationsOauthClientsCredentialsDeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsOauthClientsCredentialsDeleteRequest object.

  Fields:
    name: Required. The name of the oauth client credential to delete. Format:
      `projects/{project}/locations/{location}/oauthClients/{oauth_client}/cre
      dentials/{credential}`.
    validateOnly: Optional. If set, validate the request and preview the
      response, but do not actually post it.
  """

  name = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)


class IamProjectsLocationsOauthClientsCredentialsGetRequest(_messages.Message):
  r"""A IamProjectsLocationsOauthClientsCredentialsGetRequest object.

  Fields:
    name: Required. The name of the oauth client credential to retrieve.
      Format: `projects/{project}/locations/{location}/oauthClients/{oauth_cli
      ent}/credentials/{credential}`.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsOauthClientsCredentialsListRequest(_messages.Message):
  r"""A IamProjectsLocationsOauthClientsCredentialsListRequest object.

  Fields:
    parent: Required. The parent to list oauth client credentials for.
  """

  parent = _messages.StringField(1, required=True)


class IamProjectsLocationsOauthClientsCredentialsPatchRequest(_messages.Message):
  r"""A IamProjectsLocationsOauthClientsCredentialsPatchRequest object.

  Fields:
    name: Immutable. The resource name of the oauth client credential. Format:
      `projects/{project}/locations/{location}/oauthClients/{oauth_client}/cre
      dentials/{credential}`
    oauthClientCredential: A OauthClientCredential resource to be passed as
      the request body.
    updateMask: Required. The list of fields to update.
  """

  name = _messages.StringField(1, required=True)
  oauthClientCredential = _messages.MessageField('OauthClientCredential', 2)
  updateMask = _messages.StringField(3)


class IamProjectsLocationsOauthClientsDeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsOauthClientsDeleteRequest object.

  Fields:
    name: Required. The name of the oauth client to delete. Format:
      `projects/{project}/locations/{location}/oauthClients/{oauth_client}`.
    validateOnly: Optional. If set, validate the request and preview the
      response, but do not actually post it.
  """

  name = _messages.StringField(1, required=True)
  validateOnly = _messages.BooleanField(2)


class IamProjectsLocationsOauthClientsGetRequest(_messages.Message):
  r"""A IamProjectsLocationsOauthClientsGetRequest object.

  Fields:
    name: Required. The name of the oauth client to retrieve. Format:
      `projects/{project}/locations/{location}/oauthClients/{oauth_client}`.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsOauthClientsListRequest(_messages.Message):
  r"""A IamProjectsLocationsOauthClientsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of oauth clients to return. If
      unspecified, at most 50 oauth clients will be returned. The maximum
      value is 100; values above 100 are truncated to 100.
    pageToken: Optional. A page token, received from a previous
      `ListOauthClients` call. Provide this to retrieve the subsequent page.
    parent: Required. The parent to list oauth clients for.
    showDeleted: Optional. Whether to return soft-deleted oauth clients.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class IamProjectsLocationsOauthClientsPatchRequest(_messages.Message):
  r"""A IamProjectsLocationsOauthClientsPatchRequest object.

  Fields:
    name: Immutable. The resource name of the oauth client. Format:`projects/{
      project}/locations/{location}/oauthClients/{oauth_client}`.
    oauthClient: A OauthClient resource to be passed as the request body.
    updateMask: Required. The list of fields to update.
  """

  name = _messages.StringField(1, required=True)
  oauthClient = _messages.MessageField('OauthClient', 2)
  updateMask = _messages.StringField(3)


class IamProjectsLocationsOauthClientsUndeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsOauthClientsUndeleteRequest object.

  Fields:
    name: Required. The name of the oauth client to undelete. Format:
      `projects/{project}/locations/{location}/oauthClients/{oauth_client}`.
    undeleteOauthClientRequest: A UndeleteOauthClientRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteOauthClientRequest = _messages.MessageField('UndeleteOauthClientRequest', 2)


class IamProjectsLocationsWorkloadIdentityPoolsCreateRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsCreateRequest object.

  Fields:
    parent: Required. The parent resource to create the pool in. The only
      supported location is `global`.
    workloadIdentityPool: A WorkloadIdentityPool resource to be passed as the
      request body.
    workloadIdentityPoolId: Required. The ID to use for the pool, which
      becomes the final component of the resource name. This value should be
      4-32 characters, and may contain the characters [a-z0-9-]. The prefix
      `gcp-` is reserved for use by Google, and may not be specified.
  """

  parent = _messages.StringField(1, required=True)
  workloadIdentityPool = _messages.MessageField('WorkloadIdentityPool', 2)
  workloadIdentityPoolId = _messages.StringField(3)


class IamProjectsLocationsWorkloadIdentityPoolsDeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsDeleteRequest object.

  Fields:
    name: Required. The name of the pool to delete.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsGetRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsGetRequest object.

  Fields:
    name: Required. The name of the pool to retrieve.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsListRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsListRequest object.

  Fields:
    pageSize: The maximum number of pools to return. If unspecified, at most
      50 pools are returned. The maximum value is 1000; values above are 1000
      truncated to 1000.
    pageToken: A page token, received from a previous
      `ListWorkloadIdentityPools` call. Provide this to retrieve the
      subsequent page.
    parent: Required. The parent resource to list pools for.
    showDeleted: Whether to return soft-deleted pools.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesCreateRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesCreateRequest
  object.

  Fields:
    parent: Required. The parent resource to create the namespace in. The only
      supported location is `global`.
    workloadIdentityPoolNamespace: A WorkloadIdentityPoolNamespace resource to
      be passed as the request body.
    workloadIdentityPoolNamespaceId: Required. The ID to use for the
      namespace. Must conform to the SPIFFE spec which limits characters to
      letters, numbers, dots, dashes, and underscores [a-zA-Z0-9.-_] (it
      cannot include only periods) and sets a maximum length of the full
      canonical identifier at 2048 bytes. The prefix "gcp-" will be reserved
      for future uses.
  """

  parent = _messages.StringField(1, required=True)
  workloadIdentityPoolNamespace = _messages.MessageField('WorkloadIdentityPoolNamespace', 2)
  workloadIdentityPoolNamespaceId = _messages.StringField(3)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesDeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesDeleteRequest
  object.

  Fields:
    name: Required. The name of the namespace to delete.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesGetRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesGetRequest object.

  Fields:
    name: Required. The name of the namespace to retrieve.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesListRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesListRequest object.

  Fields:
    pageSize: The maximum number of namespaces to return. If unspecified, at
      most 50 namespaces are returned. The maximum value is 1000; values above
      are 1000 truncated to 1000.
    pageToken: A page token, received from a previous
      `ListWorkloadIdentityPoolNamespaces` call. Provide this to retrieve the
      subsequent page.
    parent: Required. The parent resource to list namespaces for.
    showDeleted: Whether to return soft-deleted namespaces.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesCreateRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesCr
  eateRequest object.

  Fields:
    parent: Required. The parent resource to create the manage identity in.
      The only supported location is `global`.
    workloadIdentityPoolManagedIdentity: A WorkloadIdentityPoolManagedIdentity
      resource to be passed as the request body.
    workloadIdentityPoolManagedIdentityId: Required. The ID to use for the
      managed identity. Must conform to the SPIFFE spec which limits
      characters to letters, numbers, dots, dashes, and underscores
      [a-zA-Z0-9.-_] (it cannot include only periods) and sets a maximum
      length of the full canonical identifier at 2048 bytes. The prefix "gcp-"
      will be reserved for future uses.
  """

  parent = _messages.StringField(1, required=True)
  workloadIdentityPoolManagedIdentity = _messages.MessageField('WorkloadIdentityPoolManagedIdentity', 2)
  workloadIdentityPoolManagedIdentityId = _messages.StringField(3)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesDeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesDe
  leteRequest object.

  Fields:
    name: Required. The name of the managed identity to delete.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesGetRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesGe
  tRequest object.

  Fields:
    name: Required. The name of the managed identity to retrieve.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesListRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesLi
  stRequest object.

  Fields:
    pageSize: The maximum number of managed identities to return. If
      unspecified, at most 50 managed identities are returned. The maximum
      value is 1000; values above are 1000 truncated to 1000.
    pageToken: A page token, received from a previous
      `ListWorkloadIdentityPoolManagedIdentities` call. Provide this to
      retrieve the subsequent page.
    parent: Required. The parent resource to list managed identities for.
    showDeleted: Whether to return soft-deleted managed identities.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesOperationsGetRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesOp
  erationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesPatchRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesPa
  tchRequest object.

  Fields:
    name: Output only. The resource name of the managed identity.
    updateMask: Required. The list of fields to update.
    workloadIdentityPoolManagedIdentity: A WorkloadIdentityPoolManagedIdentity
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  workloadIdentityPoolManagedIdentity = _messages.MessageField('WorkloadIdentityPoolManagedIdentity', 3)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesUndeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesUn
  deleteRequest object.

  Fields:
    name: Required. The name of the managed identity to undelete.
    undeleteWorkloadIdentityPoolManagedIdentityRequest: A
      UndeleteWorkloadIdentityPoolManagedIdentityRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteWorkloadIdentityPoolManagedIdentityRequest = _messages.MessageField('UndeleteWorkloadIdentityPoolManagedIdentityRequest', 2)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesCreateRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWo
  rkloadSourcesCreateRequest object.

  Fields:
    parent: Required. The parent resource to create the workload source in.
    workloadSource: A WorkloadSource resource to be passed as the request
      body.
    workloadSourceId: Required. The ID to use for the workload source, which
      becomes the final component of the resource name. This should be in the
      format of `project-`.
  """

  parent = _messages.StringField(1, required=True)
  workloadSource = _messages.MessageField('WorkloadSource', 2)
  workloadSourceId = _messages.StringField(3)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesDeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWo
  rkloadSourcesDeleteRequest object.

  Fields:
    etag: Optional. The etag for this workload source. If provided, it must
      match the server's etag.
    name: Required. The name of the workload source to delete.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesGetRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWo
  rkloadSourcesGetRequest object.

  Fields:
    name: Required. The name of the workload source to retrieve.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesListRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWo
  rkloadSourcesListRequest object.

  Fields:
    pageSize: The maximum number of workload sources to return. If
      unspecified, at most 50 workload sources are returned. The maximum value
      is 1000; values above are 1000 truncated to 1000.
    pageToken: A page token, received from a previous `ListWorkloadSources`
      call. Provide this to retrieve the subsequent page.
    parent: Required. The parent resource to list workload sources for.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesOperationsGetRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWo
  rkloadSourcesOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWorkloadSourcesPatchRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesManagedIdentitiesWo
  rkloadSourcesPatchRequest object.

  Fields:
    name: Output only. The resource name of the workload source. The format
      should be one of: * /workloadSources/ * /workloadSources/
    updateMask: Required. The list of fields to update.
    workloadSource: A WorkloadSource resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  workloadSource = _messages.MessageField('WorkloadSource', 3)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesOperationsGetRequest(_messages.Message):
  r"""A
  IamProjectsLocationsWorkloadIdentityPoolsNamespacesOperationsGetRequest
  object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesPatchRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesPatchRequest
  object.

  Fields:
    name: Output only. The resource name of the namespace.
    updateMask: Required. The list of fields to update.
    workloadIdentityPoolNamespace: A WorkloadIdentityPoolNamespace resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  workloadIdentityPoolNamespace = _messages.MessageField('WorkloadIdentityPoolNamespace', 3)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesUndeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesUndeleteRequest
  object.

  Fields:
    name: Required. The name of the namespace to undelete.
    undeleteWorkloadIdentityPoolNamespaceRequest: A
      UndeleteWorkloadIdentityPoolNamespaceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteWorkloadIdentityPoolNamespaceRequest = _messages.MessageField('UndeleteWorkloadIdentityPoolNamespaceRequest', 2)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesCreateRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesCrea
  teRequest object.

  Fields:
    parent: Required. The parent resource to create the workload source in.
    workloadSource: A WorkloadSource resource to be passed as the request
      body.
    workloadSourceId: Required. The ID to use for the workload source, which
      becomes the final component of the resource name. This should be in the
      format of `project-`.
  """

  parent = _messages.StringField(1, required=True)
  workloadSource = _messages.MessageField('WorkloadSource', 2)
  workloadSourceId = _messages.StringField(3)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesDeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesDele
  teRequest object.

  Fields:
    etag: Optional. The etag for this workload source. If provided, it must
      match the server's etag.
    name: Required. The name of the workload source to delete.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesGetRequest(_messages.Message):
  r"""A
  IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesGetRequest
  object.

  Fields:
    name: Required. The name of the workload source to retrieve.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesListRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesList
  Request object.

  Fields:
    pageSize: The maximum number of workload sources to return. If
      unspecified, at most 50 workload sources are returned. The maximum value
      is 1000; values above are 1000 truncated to 1000.
    pageToken: A page token, received from a previous `ListWorkloadSources`
      call. Provide this to retrieve the subsequent page.
    parent: Required. The parent resource to list workload sources for.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesOperationsGetRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesOper
  ationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesPatchRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsNamespacesWorkloadSourcesPatc
  hRequest object.

  Fields:
    name: Output only. The resource name of the workload source. The format
      should be one of: * /workloadSources/ * /workloadSources/
    updateMask: Required. The list of fields to update.
    workloadSource: A WorkloadSource resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  workloadSource = _messages.MessageField('WorkloadSource', 3)


class IamProjectsLocationsWorkloadIdentityPoolsOperationsGetRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsPatchRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsPatchRequest object.

  Fields:
    name: Output only. The resource name of the pool.
    updateMask: Required. The list of fields to update.
    workloadIdentityPool: A WorkloadIdentityPool resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  workloadIdentityPool = _messages.MessageField('WorkloadIdentityPool', 3)


class IamProjectsLocationsWorkloadIdentityPoolsProvidersCreateRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsProvidersCreateRequest
  object.

  Fields:
    parent: Required. The pool to create this provider in.
    workloadIdentityPoolProvider: A WorkloadIdentityPoolProvider resource to
      be passed as the request body.
    workloadIdentityPoolProviderId: Required. The ID for the provider, which
      becomes the final component of the resource name. This value must be
      4-32 characters, and may contain the characters [a-z0-9-]. The prefix
      `gcp-` is reserved for use by Google, and may not be specified.
  """

  parent = _messages.StringField(1, required=True)
  workloadIdentityPoolProvider = _messages.MessageField('WorkloadIdentityPoolProvider', 2)
  workloadIdentityPoolProviderId = _messages.StringField(3)


class IamProjectsLocationsWorkloadIdentityPoolsProvidersDeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsProvidersDeleteRequest
  object.

  Fields:
    name: Required. The name of the provider to delete.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsProvidersGetRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsProvidersGetRequest object.

  Fields:
    name: Required. The name of the provider to retrieve.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysCreateRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysCreateRequest
  object.

  Fields:
    parent: Required. The parent provider resource to create the key in.
    workloadIdentityPoolProviderKey: A WorkloadIdentityPoolProviderKey
      resource to be passed as the request body.
    workloadIdentityPoolProviderKeyId: Required. The ID to use for the key,
      which becomes the final component of the resource name. This value
      should be 4-32 characters, and may contain the characters [a-z0-9-].
  """

  parent = _messages.StringField(1, required=True)
  workloadIdentityPoolProviderKey = _messages.MessageField('WorkloadIdentityPoolProviderKey', 2)
  workloadIdentityPoolProviderKeyId = _messages.StringField(3)


class IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysDeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysDeleteRequest
  object.

  Fields:
    name: Required. The name of the encryption key to delete.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysGetRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysGetRequest
  object.

  Fields:
    name: Required. The name of the key to retrieve.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysListRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysListRequest
  object.

  Fields:
    pageSize: The maximum number of keys to return. If unspecified, all keys
      are returned. The maximum value is 10; values above 10 are truncated to
      10.
    pageToken: A page token, received from a previous
      `ListWorkloadIdentityPoolProviderKeys` call. Provide this to retrieve
      the subsequent page.
    parent: Required. The parent provider resource to list encryption keys
      for.
    showDeleted: Whether to return soft deleted resources as well.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysOperationsGetRequest(_messages.Message):
  r"""A
  IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysOperationsGetRequest
  object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysUndeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsProvidersKeysUndeleteRequest
  object.

  Fields:
    name: Required. The name of the encryption key to undelete.
    undeleteWorkloadIdentityPoolProviderKeyRequest: A
      UndeleteWorkloadIdentityPoolProviderKeyRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteWorkloadIdentityPoolProviderKeyRequest = _messages.MessageField('UndeleteWorkloadIdentityPoolProviderKeyRequest', 2)


class IamProjectsLocationsWorkloadIdentityPoolsProvidersListRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsProvidersListRequest object.

  Fields:
    pageSize: The maximum number of providers to return. If unspecified, at
      most 50 providers are returned. The maximum value is 100; values above
      100 are truncated to 100.
    pageToken: A page token, received from a previous
      `ListWorkloadIdentityPoolProviders` call. Provide this to retrieve the
      subsequent page.
    parent: Required. The pool to list providers for.
    showDeleted: Whether to return soft-deleted providers.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)


class IamProjectsLocationsWorkloadIdentityPoolsProvidersOperationsGetRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsProvidersOperationsGetRequest
  object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsLocationsWorkloadIdentityPoolsProvidersPatchRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsProvidersPatchRequest object.

  Fields:
    name: Output only. The resource name of the provider.
    updateMask: Required. The list of fields to update.
    workloadIdentityPoolProvider: A WorkloadIdentityPoolProvider resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  workloadIdentityPoolProvider = _messages.MessageField('WorkloadIdentityPoolProvider', 3)


class IamProjectsLocationsWorkloadIdentityPoolsProvidersUndeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsProvidersUndeleteRequest
  object.

  Fields:
    name: Required. The name of the provider to undelete.
    undeleteWorkloadIdentityPoolProviderRequest: A
      UndeleteWorkloadIdentityPoolProviderRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteWorkloadIdentityPoolProviderRequest = _messages.MessageField('UndeleteWorkloadIdentityPoolProviderRequest', 2)


class IamProjectsLocationsWorkloadIdentityPoolsUndeleteRequest(_messages.Message):
  r"""A IamProjectsLocationsWorkloadIdentityPoolsUndeleteRequest object.

  Fields:
    name: Required. The name of the pool to undelete.
    undeleteWorkloadIdentityPoolRequest: A UndeleteWorkloadIdentityPoolRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteWorkloadIdentityPoolRequest = _messages.MessageField('UndeleteWorkloadIdentityPoolRequest', 2)


class IamProjectsRolesCreateRequest(_messages.Message):
  r"""A IamProjectsRolesCreateRequest object.

  Fields:
    createRoleRequest: A CreateRoleRequest resource to be passed as the
      request body.
    parent: The `parent` parameter's value depends on the target resource for
      the request, namely [`projects`](https://cloud.google.com/iam/reference/
      rest/v1/projects.roles) or [`organizations`](https://cloud.google.com/ia
      m/reference/rest/v1/organizations.roles). Each resource type's `parent`
      value format is described below: * [`projects.roles.create()`](https://c
      loud.google.com/iam/reference/rest/v1/projects.roles/create):
      `projects/{PROJECT_ID}`. This method creates project-level [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles).
      Example request URL:
      `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles` * [`organiza
      tions.roles.create()`](https://cloud.google.com/iam/reference/rest/v1/or
      ganizations.roles/create): `organizations/{ORGANIZATION_ID}`. This
      method creates organization-level [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles).
      Example request URL:
      `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles`
      Note: Wildcard (*) values are invalid; you must specify a complete
      project ID or organization ID.
  """

  createRoleRequest = _messages.MessageField('CreateRoleRequest', 1)
  parent = _messages.StringField(2, required=True)


class IamProjectsRolesDeleteRequest(_messages.Message):
  r"""A IamProjectsRolesDeleteRequest object.

  Fields:
    etag: Used to perform a consistent read-modify-write.
    name: The `name` parameter's value depends on the target resource for the
      request, namely [`projects`](https://cloud.google.com/iam/reference/rest
      /v1/projects.roles) or [`organizations`](https://cloud.google.com/iam/re
      ference/rest/v1/organizations.roles). Each resource type's `name` value
      format is described below: * [`projects.roles.delete()`](https://cloud.g
      oogle.com/iam/reference/rest/v1/projects.roles/delete):
      `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method deletes only
      [custom roles](https://cloud.google.com/iam/docs/understanding-custom-
      roles) that have been created at the project level. Example request URL:
      `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_
      ID}` * [`organizations.roles.delete()`](https://cloud.google.com/iam/ref
      erence/rest/v1/organizations.roles/delete):
      `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method
      deletes only [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles)
      that have been created at the organization level. Example request URL: `
      https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUS
      TOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a
      complete project ID or organization ID.
  """

  etag = _messages.BytesField(1)
  name = _messages.StringField(2, required=True)


class IamProjectsRolesGetRequest(_messages.Message):
  r"""A IamProjectsRolesGetRequest object.

  Fields:
    name: The `name` parameter's value depends on the target resource for the
      request, namely
      [`roles`](https://cloud.google.com/iam/reference/rest/v1/roles), [`proje
      cts`](https://cloud.google.com/iam/reference/rest/v1/projects.roles), or
      [`organizations`](https://cloud.google.com/iam/reference/rest/v1/organiz
      ations.roles). Each resource type's `name` value format is described
      below: * [`roles.get()`](https://cloud.google.com/iam/reference/rest/v1/
      roles/get): `roles/{ROLE_NAME}`. This method returns results from all
      [predefined roles](https://cloud.google.com/iam/docs/understanding-
      roles#predefined_roles) in Cloud IAM. Example request URL:
      `https://iam.googleapis.com/v1/roles/{ROLE_NAME}` * [`projects.roles.get
      ()`](https://cloud.google.com/iam/reference/rest/v1/projects.roles/get):
      `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method returns only
      [custom roles](https://cloud.google.com/iam/docs/understanding-custom-
      roles) that have been created at the project level. Example request URL:
      `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_
      ID}` * [`organizations.roles.get()`](https://cloud.google.com/iam/refere
      nce/rest/v1/organizations.roles/get):
      `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method
      returns only [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles)
      that have been created at the organization level. Example request URL: `
      https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUS
      TOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a
      complete project ID or organization ID.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsRolesListRequest(_messages.Message):
  r"""A IamProjectsRolesListRequest object.

  Enums:
    ViewValueValuesEnum: Optional view for the returned Role objects. When
      `FULL` is specified, the `includedPermissions` field is returned, which
      includes a list of all permissions in the role. The default value is
      `BASIC`, which does not return the `includedPermissions` field.

  Fields:
    pageSize: Optional limit on the number of roles to include in the
      response. The default is 300, and the maximum is 1,000.
    pageToken: Optional pagination token returned in an earlier
      ListRolesResponse.
    parent: The `parent` parameter's value depends on the target resource for
      the request, namely
      [`roles`](https://cloud.google.com/iam/reference/rest/v1/roles), [`proje
      cts`](https://cloud.google.com/iam/reference/rest/v1/projects.roles), or
      [`organizations`](https://cloud.google.com/iam/reference/rest/v1/organiz
      ations.roles). Each resource type's `parent` value format is described
      below: * [`roles.list()`](https://cloud.google.com/iam/reference/rest/v1
      /roles/list): An empty string. This method doesn't require a resource;
      it simply returns all [predefined
      roles](https://cloud.google.com/iam/docs/understanding-
      roles#predefined_roles) in Cloud IAM. Example request URL:
      `https://iam.googleapis.com/v1/roles` * [`projects.roles.list()`](https:
      //cloud.google.com/iam/reference/rest/v1/projects.roles/list):
      `projects/{PROJECT_ID}`. This method lists all project-level [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles).
      Example request URL:
      `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles` * [`organiza
      tions.roles.list()`](https://cloud.google.com/iam/reference/rest/v1/orga
      nizations.roles/list): `organizations/{ORGANIZATION_ID}`. This method
      lists all organization-level [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles).
      Example request URL:
      `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles`
      Note: Wildcard (*) values are invalid; you must specify a complete
      project ID or organization ID.
    showDeleted: Include Roles that have been deleted.
    view: Optional view for the returned Role objects. When `FULL` is
      specified, the `includedPermissions` field is returned, which includes a
      list of all permissions in the role. The default value is `BASIC`, which
      does not return the `includedPermissions` field.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional view for the returned Role objects. When `FULL` is specified,
    the `includedPermissions` field is returned, which includes a list of all
    permissions in the role. The default value is `BASIC`, which does not
    return the `includedPermissions` field.

    Values:
      BASIC: Omits the `included_permissions` field. This is the default
        value.
      FULL: Returns all fields.
    """
    BASIC = 0
    FULL = 1

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  showDeleted = _messages.BooleanField(4)
  view = _messages.EnumField('ViewValueValuesEnum', 5)


class IamProjectsRolesPatchRequest(_messages.Message):
  r"""A IamProjectsRolesPatchRequest object.

  Fields:
    name: The `name` parameter's value depends on the target resource for the
      request, namely [`projects`](https://cloud.google.com/iam/reference/rest
      /v1/projects.roles) or [`organizations`](https://cloud.google.com/iam/re
      ference/rest/v1/organizations.roles). Each resource type's `name` value
      format is described below: * [`projects.roles.patch()`](https://cloud.go
      ogle.com/iam/reference/rest/v1/projects.roles/patch):
      `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method updates only
      [custom roles](https://cloud.google.com/iam/docs/understanding-custom-
      roles) that have been created at the project level. Example request URL:
      `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_
      ID}` * [`organizations.roles.patch()`](https://cloud.google.com/iam/refe
      rence/rest/v1/organizations.roles/patch):
      `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method
      updates only [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles)
      that have been created at the organization level. Example request URL: `
      https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUS
      TOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a
      complete project ID or organization ID.
    role: A Role resource to be passed as the request body.
    updateMask: A mask describing which fields in the Role have changed.
  """

  name = _messages.StringField(1, required=True)
  role = _messages.MessageField('Role', 2)
  updateMask = _messages.StringField(3)


class IamProjectsRolesUndeleteRequest(_messages.Message):
  r"""A IamProjectsRolesUndeleteRequest object.

  Fields:
    name: The `name` parameter's value depends on the target resource for the
      request, namely [`projects`](https://cloud.google.com/iam/reference/rest
      /v1/projects.roles) or [`organizations`](https://cloud.google.com/iam/re
      ference/rest/v1/organizations.roles). Each resource type's `name` value
      format is described below: * [`projects.roles.undelete()`](https://cloud
      .google.com/iam/reference/rest/v1/projects.roles/undelete):
      `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method undeletes
      only [custom roles](https://cloud.google.com/iam/docs/understanding-
      custom-roles) that have been created at the project level. Example
      request URL: `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/
      {CUSTOM_ROLE_ID}` * [`organizations.roles.undelete()`](https://cloud.goo
      gle.com/iam/reference/rest/v1/organizations.roles/undelete):
      `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method
      undeletes only [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles)
      that have been created at the organization level. Example request URL: `
      https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUS
      TOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a
      complete project ID or organization ID.
    undeleteRoleRequest: A UndeleteRoleRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteRoleRequest = _messages.MessageField('UndeleteRoleRequest', 2)


class IamProjectsServiceAccountsCreateRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsCreateRequest object.

  Fields:
    createServiceAccountRequest: A CreateServiceAccountRequest resource to be
      passed as the request body.
    name: Required. The resource name of the project associated with the
      service accounts, such as `projects/my-project-123`.
  """

  createServiceAccountRequest = _messages.MessageField('CreateServiceAccountRequest', 1)
  name = _messages.StringField(2, required=True)


class IamProjectsServiceAccountsDeleteRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsDeleteRequest object.

  Fields:
    name: Required. The resource name of the service account. Use one of the
      following formats: *
      `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative,
      you can use the `-` wildcard character instead of the project ID: *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the
      `-` wildcard character, because it can cause response messages to
      contain misleading error codes. For example, if you try to access the
      service account `projects/-/serviceAccounts/<EMAIL>`, which
      does not exist, the response contains an HTTP `403 Forbidden` error
      instead of a `404 Not Found` error.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsServiceAccountsDisableRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsDisableRequest object.

  Fields:
    disableServiceAccountRequest: A DisableServiceAccountRequest resource to
      be passed as the request body.
    name: The resource name of the service account. Use one of the following
      formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative,
      you can use the `-` wildcard character instead of the project ID: *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the
      `-` wildcard character, because it can cause response messages to
      contain misleading error codes. For example, if you try to access the
      service account `projects/-/serviceAccounts/<EMAIL>`, which
      does not exist, the response contains an HTTP `403 Forbidden` error
      instead of a `404 Not Found` error.
  """

  disableServiceAccountRequest = _messages.MessageField('DisableServiceAccountRequest', 1)
  name = _messages.StringField(2, required=True)


class IamProjectsServiceAccountsEnableRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsEnableRequest object.

  Fields:
    enableServiceAccountRequest: A EnableServiceAccountRequest resource to be
      passed as the request body.
    name: The resource name of the service account. Use one of the following
      formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative,
      you can use the `-` wildcard character instead of the project ID: *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the
      `-` wildcard character, because it can cause response messages to
      contain misleading error codes. For example, if you try to access the
      service account `projects/-/serviceAccounts/<EMAIL>`, which
      does not exist, the response contains an HTTP `403 Forbidden` error
      instead of a `404 Not Found` error.
  """

  enableServiceAccountRequest = _messages.MessageField('EnableServiceAccountRequest', 1)
  name = _messages.StringField(2, required=True)


class IamProjectsServiceAccountsGetIamPolicyRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class IamProjectsServiceAccountsGetRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsGetRequest object.

  Fields:
    name: Required. The resource name of the service account. Use one of the
      following formats: *
      `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative,
      you can use the `-` wildcard character instead of the project ID: *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the
      `-` wildcard character, because it can cause response messages to
      contain misleading error codes. For example, if you try to access the
      service account `projects/-/serviceAccounts/<EMAIL>`, which
      does not exist, the response contains an HTTP `403 Forbidden` error
      instead of a `404 Not Found` error.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsServiceAccountsIdentityBindingsCreateRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsIdentityBindingsCreateRequest object.

  Fields:
    createServiceAccountIdentityBindingRequest: A
      CreateServiceAccountIdentityBindingRequest resource to be passed as the
      request body.
    name: The resource name of the service account. Use one of the following
      formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative,
      you can use the `-` wildcard character instead of the project ID: *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the
      `-` wildcard character, because it can cause response messages to
      contain misleading error codes. For example, if you try to access the
      service account `projects/-/serviceAccounts/<EMAIL>`, which
      does not exist, the response contains an HTTP `403 Forbidden` error
      instead of a `404 Not Found` error.
  """

  createServiceAccountIdentityBindingRequest = _messages.MessageField('CreateServiceAccountIdentityBindingRequest', 1)
  name = _messages.StringField(2, required=True)


class IamProjectsServiceAccountsIdentityBindingsDeleteRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsIdentityBindingsDeleteRequest object.

  Fields:
    name: The resource name of the service account identity binding. Use one
      of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAI
      L_ADDRESS}/identityBindings/{BINDING}` * `projects/{PROJECT_ID}/serviceA
      ccounts/{UNIQUE_ID}/identityBindings/{BINDING}` As an alternative, you
      can use the `-` wildcard character instead of the project ID: *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS}/identityBindings/{BINDING}`
      * `projects/-/serviceAccounts/{UNIQUE_ID}/identityBindings/{BINDING}`
      When possible, avoid using the `-` wildcard character, because it can
      cause response messages to contain misleading error codes. For example,
      if you try to access the service account identity binding
      `projects/-/serviceAccounts/<EMAIL>/identityBindings/fake-
      binding`, which does not exist, the response contains an HTTP `403
      Forbidden` error instead of a `404 Not Found` error.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsServiceAccountsIdentityBindingsGetRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsIdentityBindingsGetRequest object.

  Fields:
    name: The resource name of the service account identity binding. Use one
      of the following formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAI
      L_ADDRESS}/identityBindings/{BINDING}` * `projects/{PROJECT_ID}/serviceA
      ccounts/{UNIQUE_ID}/identityBindings/{BINDING}` As an alternative, you
      can use the `-` wildcard character instead of the project ID: *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS}/identityBindings/{BINDING}`
      * `projects/-/serviceAccounts/{UNIQUE_ID}/identityBindings/{BINDING}`
      When possible, avoid using the `-` wildcard character, because it can
      cause response messages to contain misleading error codes. For example,
      if you try to access the service account identity binding
      `projects/-/serviceAccounts/<EMAIL>/identityBindings/fake-
      binding`, which does not exist, the response contains an HTTP `403
      Forbidden` error instead of a `404 Not Found` error.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsServiceAccountsIdentityBindingsListRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsIdentityBindingsListRequest object.

  Fields:
    name: The resource name of the service account. Use one of the following
      formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative,
      you can use the `-` wildcard character instead of the project ID: *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the
      `-` wildcard character, because it can cause response messages to
      contain misleading error codes. For example, if you try to access the
      service account `projects/-/serviceAccounts/<EMAIL>`, which
      does not exist, the response contains an HTTP `403 Forbidden` error
      instead of a `404 Not Found` error.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsServiceAccountsKeysCreateRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsKeysCreateRequest object.

  Fields:
    createServiceAccountKeyRequest: A CreateServiceAccountKeyRequest resource
      to be passed as the request body.
    name: Required. The resource name of the service account. Use one of the
      following formats: *
      `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative,
      you can use the `-` wildcard character instead of the project ID: *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the
      `-` wildcard character, because it can cause response messages to
      contain misleading error codes. For example, if you try to access the
      service account `projects/-/serviceAccounts/<EMAIL>`, which
      does not exist, the response contains an HTTP `403 Forbidden` error
      instead of a `404 Not Found` error.
  """

  createServiceAccountKeyRequest = _messages.MessageField('CreateServiceAccountKeyRequest', 1)
  name = _messages.StringField(2, required=True)


class IamProjectsServiceAccountsKeysDeleteRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsKeysDeleteRequest object.

  Fields:
    name: Required. The resource name of the service account key. Use one of
      the following formats: *
      `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}/keys/{KEY_ID}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}/keys/{KEY_ID}` As an
      alternative, you can use the `-` wildcard character instead of the
      project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}/keys/{KEY_ID}`
      * `projects/-/serviceAccounts/{UNIQUE_ID}/keys/{KEY_ID}` When possible,
      avoid using the `-` wildcard character, because it can cause response
      messages to contain misleading error codes. For example, if you try to
      access the service account key
      `projects/-/serviceAccounts/<EMAIL>/keys/fake-key`, which does
      not exist, the response contains an HTTP `403 Forbidden` error instead
      of a `404 Not Found` error.
  """

  name = _messages.StringField(1, required=True)


class IamProjectsServiceAccountsKeysDisableRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsKeysDisableRequest object.

  Fields:
    disableServiceAccountKeyRequest: A DisableServiceAccountKeyRequest
      resource to be passed as the request body.
    name: Required. The resource name of the service account key. Use one of
      the following formats: *
      `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}/keys/{KEY_ID}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}/keys/{KEY_ID}` As an
      alternative, you can use the `-` wildcard character instead of the
      project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}/keys/{KEY_ID}`
      * `projects/-/serviceAccounts/{UNIQUE_ID}/keys/{KEY_ID}` When possible,
      avoid using the `-` wildcard character, because it can cause response
      messages to contain misleading error codes. For example, if you try to
      access the service account key
      `projects/-/serviceAccounts/<EMAIL>/keys/fake-key`, which does
      not exist, the response contains an HTTP `403 Forbidden` error instead
      of a `404 Not Found` error.
  """

  disableServiceAccountKeyRequest = _messages.MessageField('DisableServiceAccountKeyRequest', 1)
  name = _messages.StringField(2, required=True)


class IamProjectsServiceAccountsKeysEnableRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsKeysEnableRequest object.

  Fields:
    enableServiceAccountKeyRequest: A EnableServiceAccountKeyRequest resource
      to be passed as the request body.
    name: Required. The resource name of the service account key. Use one of
      the following formats: *
      `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}/keys/{KEY_ID}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}/keys/{KEY_ID}` As an
      alternative, you can use the `-` wildcard character instead of the
      project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}/keys/{KEY_ID}`
      * `projects/-/serviceAccounts/{UNIQUE_ID}/keys/{KEY_ID}` When possible,
      avoid using the `-` wildcard character, because it can cause response
      messages to contain misleading error codes. For example, if you try to
      access the service account key
      `projects/-/serviceAccounts/<EMAIL>/keys/fake-key`, which does
      not exist, the response contains an HTTP `403 Forbidden` error instead
      of a `404 Not Found` error.
  """

  enableServiceAccountKeyRequest = _messages.MessageField('EnableServiceAccountKeyRequest', 1)
  name = _messages.StringField(2, required=True)


class IamProjectsServiceAccountsKeysGetRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsKeysGetRequest object.

  Enums:
    PublicKeyTypeValueValuesEnum: Optional. The output format of the public
      key. The default is `TYPE_NONE`, which means that the public key is not
      returned.

  Fields:
    name: Required. The resource name of the service account key. Use one of
      the following formats: *
      `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}/keys/{KEY_ID}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}/keys/{KEY_ID}` As an
      alternative, you can use the `-` wildcard character instead of the
      project ID: * `projects/-/serviceAccounts/{EMAIL_ADDRESS}/keys/{KEY_ID}`
      * `projects/-/serviceAccounts/{UNIQUE_ID}/keys/{KEY_ID}` When possible,
      avoid using the `-` wildcard character, because it can cause response
      messages to contain misleading error codes. For example, if you try to
      access the service account key
      `projects/-/serviceAccounts/<EMAIL>/keys/fake-key`, which does
      not exist, the response contains an HTTP `403 Forbidden` error instead
      of a `404 Not Found` error.
    publicKeyType: Optional. The output format of the public key. The default
      is `TYPE_NONE`, which means that the public key is not returned.
  """

  class PublicKeyTypeValueValuesEnum(_messages.Enum):
    r"""Optional. The output format of the public key. The default is
    `TYPE_NONE`, which means that the public key is not returned.

    Values:
      TYPE_NONE: Do not return the public key.
      TYPE_X509_PEM_FILE: X509 PEM format.
      TYPE_RAW_PUBLIC_KEY: Raw public key.
    """
    TYPE_NONE = 0
    TYPE_X509_PEM_FILE = 1
    TYPE_RAW_PUBLIC_KEY = 2

  name = _messages.StringField(1, required=True)
  publicKeyType = _messages.EnumField('PublicKeyTypeValueValuesEnum', 2)


class IamProjectsServiceAccountsKeysListRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsKeysListRequest object.

  Enums:
    KeyTypesValueValuesEnum: Filters the types of keys the user wants to
      include in the list response. Duplicate key types are not allowed. If no
      key type is provided, all keys are returned.

  Fields:
    keyTypes: Filters the types of keys the user wants to include in the list
      response. Duplicate key types are not allowed. If no key type is
      provided, all keys are returned.
    name: Required. The resource name of the service account. Use one of the
      following formats: *
      `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative,
      you can use the `-` wildcard character instead of the project ID: *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the
      `-` wildcard character, because it can cause response messages to
      contain misleading error codes. For example, if you try to access the
      service account `projects/-/serviceAccounts/<EMAIL>`, which
      does not exist, the response contains an HTTP `403 Forbidden` error
      instead of a `404 Not Found` error.
  """

  class KeyTypesValueValuesEnum(_messages.Enum):
    r"""Filters the types of keys the user wants to include in the list
    response. Duplicate key types are not allowed. If no key type is provided,
    all keys are returned.

    Values:
      KEY_TYPE_UNSPECIFIED: Unspecified key type. The presence of this in the
        message will immediately result in an error.
      USER_MANAGED: User-managed keys (managed and rotated by the user).
      SYSTEM_MANAGED: System-managed keys (managed and rotated by Google).
    """
    KEY_TYPE_UNSPECIFIED = 0
    USER_MANAGED = 1
    SYSTEM_MANAGED = 2

  keyTypes = _messages.EnumField('KeyTypesValueValuesEnum', 1, repeated=True)
  name = _messages.StringField(2, required=True)


class IamProjectsServiceAccountsKeysUploadRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsKeysUploadRequest object.

  Fields:
    name: The resource name of the service account key. Use one of the
      following formats: *
      `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative,
      you can use the `-` wildcard character instead of the project ID: *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the
      `-` wildcard character, because it can cause response messages to
      contain misleading error codes. For example, if you try to access the
      service account `projects/-/serviceAccounts/<EMAIL>`, which
      does not exist, the response contains an HTTP `403 Forbidden` error
      instead of a `404 Not Found` error.
    uploadServiceAccountKeyRequest: A UploadServiceAccountKeyRequest resource
      to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  uploadServiceAccountKeyRequest = _messages.MessageField('UploadServiceAccountKeyRequest', 2)


class IamProjectsServiceAccountsListRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsListRequest object.

  Fields:
    name: Required. The resource name of the project associated with the
      service accounts, such as `projects/my-project-123`.
    pageSize: Optional limit on the number of service accounts to include in
      the response. Further accounts can subsequently be obtained by including
      the ListServiceAccountsResponse.next_page_token in a subsequent request.
      The default is 20, and the maximum is 100.
    pageToken: Optional pagination token returned in an earlier
      ListServiceAccountsResponse.next_page_token.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class IamProjectsServiceAccountsPatchRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsPatchRequest object.

  Fields:
    name: The resource name of the service account. Use one of the following
      formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative,
      you can use the `-` wildcard character instead of the project ID: *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the
      `-` wildcard character, because it can cause response messages to
      contain misleading error codes. For example, if you try to access the
      service account `projects/-/serviceAccounts/<EMAIL>`, which
      does not exist, the response contains an HTTP `403 Forbidden` error
      instead of a `404 Not Found` error.
    patchServiceAccountRequest: A PatchServiceAccountRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  patchServiceAccountRequest = _messages.MessageField('PatchServiceAccountRequest', 2)


class IamProjectsServiceAccountsSetIamPolicyRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class IamProjectsServiceAccountsSignBlobRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsSignBlobRequest object.

  Fields:
    name: Required. Deprecated. [Migrate to Service Account Credentials
      API](https://cloud.google.com/iam/help/credentials/migrate-api). The
      resource name of the service account. Use one of the following formats:
      * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative,
      you can use the `-` wildcard character instead of the project ID: *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the
      `-` wildcard character, because it can cause response messages to
      contain misleading error codes. For example, if you try to access the
      service account `projects/-/serviceAccounts/<EMAIL>`, which
      does not exist, the response contains an HTTP `403 Forbidden` error
      instead of a `404 Not Found` error.
    signBlobRequest: A SignBlobRequest resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  signBlobRequest = _messages.MessageField('SignBlobRequest', 2)


class IamProjectsServiceAccountsSignJwtRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsSignJwtRequest object.

  Fields:
    name: Required. Deprecated. [Migrate to Service Account Credentials
      API](https://cloud.google.com/iam/help/credentials/migrate-api). The
      resource name of the service account. Use one of the following formats:
      * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative,
      you can use the `-` wildcard character instead of the project ID: *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the
      `-` wildcard character, because it can cause response messages to
      contain misleading error codes. For example, if you try to access the
      service account `projects/-/serviceAccounts/<EMAIL>`, which
      does not exist, the response contains an HTTP `403 Forbidden` error
      instead of a `404 Not Found` error.
    signJwtRequest: A SignJwtRequest resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  signJwtRequest = _messages.MessageField('SignJwtRequest', 2)


class IamProjectsServiceAccountsTestIamPermissionsRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class IamProjectsServiceAccountsUndeleteRequest(_messages.Message):
  r"""A IamProjectsServiceAccountsUndeleteRequest object.

  Fields:
    name: The resource name of the service account. Use one of the following
      formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative,
      you can use the `-` wildcard character instead of the project ID: *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the
      `-` wildcard character, because it can cause response messages to
      contain misleading error codes. For example, if you try to access the
      service account `projects/-/serviceAccounts/<EMAIL>`, which
      does not exist, the response contains an HTTP `403 Forbidden` error
      instead of a `404 Not Found` error.
    undeleteServiceAccountRequest: A UndeleteServiceAccountRequest resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteServiceAccountRequest = _messages.MessageField('UndeleteServiceAccountRequest', 2)


class IamRolesGetRequest(_messages.Message):
  r"""A IamRolesGetRequest object.

  Fields:
    name: The `name` parameter's value depends on the target resource for the
      request, namely
      [`roles`](https://cloud.google.com/iam/reference/rest/v1/roles), [`proje
      cts`](https://cloud.google.com/iam/reference/rest/v1/projects.roles), or
      [`organizations`](https://cloud.google.com/iam/reference/rest/v1/organiz
      ations.roles). Each resource type's `name` value format is described
      below: * [`roles.get()`](https://cloud.google.com/iam/reference/rest/v1/
      roles/get): `roles/{ROLE_NAME}`. This method returns results from all
      [predefined roles](https://cloud.google.com/iam/docs/understanding-
      roles#predefined_roles) in Cloud IAM. Example request URL:
      `https://iam.googleapis.com/v1/roles/{ROLE_NAME}` * [`projects.roles.get
      ()`](https://cloud.google.com/iam/reference/rest/v1/projects.roles/get):
      `projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_ID}`. This method returns only
      [custom roles](https://cloud.google.com/iam/docs/understanding-custom-
      roles) that have been created at the project level. Example request URL:
      `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles/{CUSTOM_ROLE_
      ID}` * [`organizations.roles.get()`](https://cloud.google.com/iam/refere
      nce/rest/v1/organizations.roles/get):
      `organizations/{ORGANIZATION_ID}/roles/{CUSTOM_ROLE_ID}`. This method
      returns only [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles)
      that have been created at the organization level. Example request URL: `
      https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles/{CUS
      TOM_ROLE_ID}` Note: Wildcard (*) values are invalid; you must specify a
      complete project ID or organization ID.
  """

  name = _messages.StringField(1, required=True)


class IamRolesListRequest(_messages.Message):
  r"""A IamRolesListRequest object.

  Enums:
    ViewValueValuesEnum: Optional view for the returned Role objects. When
      `FULL` is specified, the `includedPermissions` field is returned, which
      includes a list of all permissions in the role. The default value is
      `BASIC`, which does not return the `includedPermissions` field.

  Fields:
    pageSize: Optional limit on the number of roles to include in the
      response. The default is 300, and the maximum is 1,000.
    pageToken: Optional pagination token returned in an earlier
      ListRolesResponse.
    parent: The `parent` parameter's value depends on the target resource for
      the request, namely
      [`roles`](https://cloud.google.com/iam/reference/rest/v1/roles), [`proje
      cts`](https://cloud.google.com/iam/reference/rest/v1/projects.roles), or
      [`organizations`](https://cloud.google.com/iam/reference/rest/v1/organiz
      ations.roles). Each resource type's `parent` value format is described
      below: * [`roles.list()`](https://cloud.google.com/iam/reference/rest/v1
      /roles/list): An empty string. This method doesn't require a resource;
      it simply returns all [predefined
      roles](https://cloud.google.com/iam/docs/understanding-
      roles#predefined_roles) in Cloud IAM. Example request URL:
      `https://iam.googleapis.com/v1/roles` * [`projects.roles.list()`](https:
      //cloud.google.com/iam/reference/rest/v1/projects.roles/list):
      `projects/{PROJECT_ID}`. This method lists all project-level [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles).
      Example request URL:
      `https://iam.googleapis.com/v1/projects/{PROJECT_ID}/roles` * [`organiza
      tions.roles.list()`](https://cloud.google.com/iam/reference/rest/v1/orga
      nizations.roles/list): `organizations/{ORGANIZATION_ID}`. This method
      lists all organization-level [custom
      roles](https://cloud.google.com/iam/docs/understanding-custom-roles).
      Example request URL:
      `https://iam.googleapis.com/v1/organizations/{ORGANIZATION_ID}/roles`
      Note: Wildcard (*) values are invalid; you must specify a complete
      project ID or organization ID.
    showDeleted: Include Roles that have been deleted.
    view: Optional view for the returned Role objects. When `FULL` is
      specified, the `includedPermissions` field is returned, which includes a
      list of all permissions in the role. The default value is `BASIC`, which
      does not return the `includedPermissions` field.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional view for the returned Role objects. When `FULL` is specified,
    the `includedPermissions` field is returned, which includes a list of all
    permissions in the role. The default value is `BASIC`, which does not
    return the `includedPermissions` field.

    Values:
      BASIC: Omits the `included_permissions` field. This is the default
        value.
      FULL: Returns all fields.
    """
    BASIC = 0
    FULL = 1

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)
  showDeleted = _messages.BooleanField(4)
  view = _messages.EnumField('ViewValueValuesEnum', 5)


class KeyData(_messages.Message):
  r"""Represents a public key data along with its format.

  Enums:
    FormatValueValuesEnum: Output only. The format of the key.
    KeySpecValueValuesEnum: Required. The specifications for the key.

  Fields:
    format: Output only. The format of the key.
    key: Output only. The key data. The format of the key is represented by
      the format field.
    keySpec: Required. The specifications for the key.
    notAfterTime: Output only. Latest timestamp when this key is valid.
      Attempts to use this key after this time will fail. Only present if the
      key data represents a X.509 certificate.
    notBeforeTime: Output only. Earliest timestamp when this key is valid.
      Attempts to use this key before this time will fail. Only present if the
      key data represents a X.509 certificate.
  """

  class FormatValueValuesEnum(_messages.Enum):
    r"""Output only. The format of the key.

    Values:
      KEY_FORMAT_UNSPECIFIED: No format has been specified. This is an invalid
        format and must not be used.
      RSA_X509_PEM: A RSA public key wrapped in an X.509v3 certificate
        ([RFC5280] ( https://www.ietf.org/rfc/rfc5280.txt)), encoded in
        base64, and wrapped in [public certificate
        label](https://datatracker.ietf.org/doc/html/rfc7468#section-5.1).
    """
    KEY_FORMAT_UNSPECIFIED = 0
    RSA_X509_PEM = 1

  class KeySpecValueValuesEnum(_messages.Enum):
    r"""Required. The specifications for the key.

    Values:
      KEY_SPEC_UNSPECIFIED: No key specification specified.
      RSA_2048: A 2048 bit RSA key.
      RSA_3072: A 3072 bit RSA key.
      RSA_4096: A 4096 bit RSA key.
    """
    KEY_SPEC_UNSPECIFIED = 0
    RSA_2048 = 1
    RSA_3072 = 2
    RSA_4096 = 3

  format = _messages.EnumField('FormatValueValuesEnum', 1)
  key = _messages.StringField(2)
  keySpec = _messages.EnumField('KeySpecValueValuesEnum', 3)
  notAfterTime = _messages.StringField(4)
  notBeforeTime = _messages.StringField(5)


class LintPolicyRequest(_messages.Message):
  r"""The request to lint a Cloud IAM policy object.

  Fields:
    condition: google.iam.v1.Binding.condition object to be linted.
    fullResourceName: The full resource name of the policy this lint request
      is about. The name follows the Google Cloud format for full resource
      names. For example, a Cloud project with ID `my-project` will be named
      `//cloudresourcemanager.googleapis.com/projects/my-project`. The
      resource name is not used to read a policy from IAM. Only the data in
      the request object is linted.
  """

  condition = _messages.MessageField('Expr', 1)
  fullResourceName = _messages.StringField(2)


class LintPolicyResponse(_messages.Message):
  r"""The response of a lint operation. An empty response indicates the
  operation was able to fully execute and no lint issue was found.

  Fields:
    lintResults: List of lint results sorted by `severity` in descending
      order.
  """

  lintResults = _messages.MessageField('LintResult', 1, repeated=True)


class LintResult(_messages.Message):
  r"""Structured response of a single validation unit.

  Enums:
    LevelValueValuesEnum: The validation unit level.
    SeverityValueValuesEnum: The validation unit severity.

  Fields:
    debugMessage: Human readable debug message associated with the issue.
    fieldName: The name of the field for which this lint result is about. For
      nested messages `field_name` consists of names of the embedded fields
      separated by period character. The top-level qualifier is the input
      object to lint in the request. For example, the `field_name` value
      `condition.expression` identifies a lint result for the `expression`
      field of the provided condition.
    level: The validation unit level.
    locationOffset: 0-based character position of problematic construct within
      the object identified by `field_name`. Currently, this is populated only
      for condition expression.
    severity: The validation unit severity.
    validationUnitName: The validation unit name, for instance
      "lintValidationUnits/ConditionComplexityCheck".
  """

  class LevelValueValuesEnum(_messages.Enum):
    r"""The validation unit level.

    Values:
      LEVEL_UNSPECIFIED: Level is unspecified.
      CONDITION: A validation unit which operates on an individual condition
        within a binding.
    """
    LEVEL_UNSPECIFIED = 0
    CONDITION = 1

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The validation unit severity.

    Values:
      SEVERITY_UNSPECIFIED: Severity is unspecified.
      ERROR: A validation unit returns an error only for critical issues. If
        an attempt is made to set the problematic policy without rectifying
        the critical issue, it causes the `setPolicy` operation to fail.
      WARNING: Any issue which is severe enough but does not cause an error.
        For example, suspicious constructs in the input object will not
        necessarily fail `setPolicy`, but there is a high likelihood that they
        won't behave as expected during policy evaluation in `checkPolicy`.
        This includes the following common scenarios: - Unsatisfiable
        condition: Expired timestamp in date/time condition. - Ineffective
        condition: Condition on a pair which is granted unconditionally in
        another binding of the same policy.
      NOTICE: Reserved for the issues that are not severe as
        `ERROR`/`WARNING`, but need special handling. For instance, messages
        about skipped validation units are issued as `NOTICE`.
      INFO: Any informative statement which is not severe enough to raise
        `ERROR`/`WARNING`/`NOTICE`, like auto-correction recommendations on
        the input content. Note that current version of the linter does not
        utilize `INFO`.
      DEPRECATED: Deprecated severity level.
    """
    SEVERITY_UNSPECIFIED = 0
    ERROR = 1
    WARNING = 2
    NOTICE = 3
    INFO = 4
    DEPRECATED = 5

  debugMessage = _messages.StringField(1)
  fieldName = _messages.StringField(2)
  level = _messages.EnumField('LevelValueValuesEnum', 3)
  locationOffset = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  severity = _messages.EnumField('SeverityValueValuesEnum', 5)
  validationUnitName = _messages.StringField(6)


class ListOauthClientCredentialsResponse(_messages.Message):
  r"""Response message for ListOauthClientCredentials.

  Fields:
    oauthClientCredentials: A list of oauth client credentials.
  """

  oauthClientCredentials = _messages.MessageField('OauthClientCredential', 1, repeated=True)


class ListOauthClientsResponse(_messages.Message):
  r"""Response message for ListOauthClients.

  Fields:
    nextPageToken: Optional. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    oauthClients: A list of oauth clients.
  """

  nextPageToken = _messages.StringField(1)
  oauthClients = _messages.MessageField('OauthClient', 2, repeated=True)


class ListRolesResponse(_messages.Message):
  r"""The response containing the roles defined under a resource.

  Fields:
    nextPageToken: To retrieve the next page of results, set
      `ListRolesRequest.page_token` to this value.
    roles: The Roles defined on this resource.
  """

  nextPageToken = _messages.StringField(1)
  roles = _messages.MessageField('Role', 2, repeated=True)


class ListServiceAccountIdentityBindingsResponse(_messages.Message):
  r"""The service account identity bindings list response.

  Fields:
    identityBinding: The identity bindings trusted to assert the service
      account.
  """

  identityBinding = _messages.MessageField('ServiceAccountIdentityBinding', 1, repeated=True)


class ListServiceAccountKeysResponse(_messages.Message):
  r"""The service account keys list response.

  Fields:
    keys: The public keys for the service account.
  """

  keys = _messages.MessageField('ServiceAccountKey', 1, repeated=True)


class ListServiceAccountsResponse(_messages.Message):
  r"""The service account list response.

  Fields:
    accounts: The list of matching service accounts.
    nextPageToken: To retrieve the next page of results, set
      ListServiceAccountsRequest.page_token to this value.
  """

  accounts = _messages.MessageField('ServiceAccount', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListWorkforcePoolInstalledAppsResponse(_messages.Message):
  r"""Response message for ListWorkforcePoolInstalledApps.

  Fields:
    nextPageToken: Optional. A token, which can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    workforcePoolInstalledApps: Output only. A list of workforce pool
      installed apps.
  """

  nextPageToken = _messages.StringField(1)
  workforcePoolInstalledApps = _messages.MessageField('WorkforcePoolInstalledApp', 2, repeated=True)


class ListWorkforcePoolProviderKeysResponse(_messages.Message):
  r"""Response message for ListWorkforcePoolProviderKeys.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    workforcePoolProviderKeys: A list of WorkforcePoolProviderKeys.
  """

  nextPageToken = _messages.StringField(1)
  workforcePoolProviderKeys = _messages.MessageField('WorkforcePoolProviderKey', 2, repeated=True)


class ListWorkforcePoolProvidersResponse(_messages.Message):
  r"""Response message for ListWorkforcePoolProviders.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    workforcePoolProviders: A list of providers.
  """

  nextPageToken = _messages.StringField(1)
  workforcePoolProviders = _messages.MessageField('WorkforcePoolProvider', 2, repeated=True)


class ListWorkforcePoolsResponse(_messages.Message):
  r"""Response message for ListWorkforcePools.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    workforcePools: A list of pools.
  """

  nextPageToken = _messages.StringField(1)
  workforcePools = _messages.MessageField('WorkforcePool', 2, repeated=True)


class ListWorkloadIdentityPoolManagedIdentitiesResponse(_messages.Message):
  r"""Response message for ListWorkloadIdentityPoolManagedIdentities.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    workloadIdentityPoolManagedIdentities: A list of managed identities.
  """

  nextPageToken = _messages.StringField(1)
  workloadIdentityPoolManagedIdentities = _messages.MessageField('WorkloadIdentityPoolManagedIdentity', 2, repeated=True)


class ListWorkloadIdentityPoolNamespacesResponse(_messages.Message):
  r"""Response message for ListWorkloadIdentityPoolNamespaces.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    workloadIdentityPoolNamespaces: A list of namespaces.
  """

  nextPageToken = _messages.StringField(1)
  workloadIdentityPoolNamespaces = _messages.MessageField('WorkloadIdentityPoolNamespace', 2, repeated=True)


class ListWorkloadIdentityPoolProviderKeysResponse(_messages.Message):
  r"""Response message for ListWorkloadIdentityPoolProviderKeys.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    workloadIdentityPoolProviderKeys: A list of
      WorkloadIdentityPoolProviderKey
  """

  nextPageToken = _messages.StringField(1)
  workloadIdentityPoolProviderKeys = _messages.MessageField('WorkloadIdentityPoolProviderKey', 2, repeated=True)


class ListWorkloadIdentityPoolProvidersResponse(_messages.Message):
  r"""Response message for ListWorkloadIdentityPoolProviders.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    workloadIdentityPoolProviders: A list of providers.
  """

  nextPageToken = _messages.StringField(1)
  workloadIdentityPoolProviders = _messages.MessageField('WorkloadIdentityPoolProvider', 2, repeated=True)


class ListWorkloadIdentityPoolsResponse(_messages.Message):
  r"""Response message for ListWorkloadIdentityPools.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    workloadIdentityPools: A list of pools.
  """

  nextPageToken = _messages.StringField(1)
  workloadIdentityPools = _messages.MessageField('WorkloadIdentityPool', 2, repeated=True)


class ListWorkloadSourcesResponse(_messages.Message):
  r"""Response message for ListWorkloadSources.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    workloadSources: A list of workload sources.
  """

  nextPageToken = _messages.StringField(1)
  workloadSources = _messages.MessageField('WorkloadSource', 2, repeated=True)


class OauthClient(_messages.Message):
  r"""Represents an oauth client. Used to access Google Cloud resources on
  behave of a user by using OAuth2 Protocol to obtain an access token from
  Google Cloud Platform.

  Enums:
    AllowedGrantTypesValueListEntryValuesEnum:
    ClientTypeValueValuesEnum: Immutable. The type of oauth client. either
      public or private.
    StateValueValuesEnum: Output only. The state of the oauth client.

  Fields:
    allowedGrantTypes: Optional. The list of OAuth grant type is allowed for
      the oauth client.
    allowedRedirectUris: Optional. The list of redirect uris that is allowed
      to redirect back when authorization process is completed.
    allowedScopes: Optional. The list of scopes that the oauth client is
      allowed to request during OAuth flows. The following scopes are
      supported: * `https://www.googleapis.com/auth/cloud-platform`: See,
      edit, configure, and delete your Google Cloud data and see the email
      address for your Google Account. * `openid`: Associate you with your
      personal info on Google Cloud. * `email`: See your Google Cloud Account
      email address.
    clientId: Output only. The system-generated oauth client id.
    clientType: Immutable. The type of oauth client. either public or private.
    description: Optional. A user-specified description of the oauth client.
      Cannot exceed 256 characters.
    disabled: Optional. Whether the oauth client is disabled. You cannot use a
      disabled oauth client for login.
    displayName: Optional. A user-specified display name of the oauth client.
      Cannot exceed 32 characters.
    expireTime: Output only. Time after which the oauth client will be
      permanently purged and cannot be recovered.
    name: Immutable. The resource name of the oauth client. Format:`projects/{
      project}/locations/{location}/oauthClients/{oauth_client}`.
    state: Output only. The state of the oauth client.
  """

  class AllowedGrantTypesValueListEntryValuesEnum(_messages.Enum):
    r"""AllowedGrantTypesValueListEntryValuesEnum enum type.

    Values:
      GRANT_TYPE_UNSPECIFIED: should not be used
      AUTHORIZATION_CODE_GRANT: authorization code grant
      REFRESH_TOKEN_GRANT: refresh token grant
    """
    GRANT_TYPE_UNSPECIFIED = 0
    AUTHORIZATION_CODE_GRANT = 1
    REFRESH_TOKEN_GRANT = 2

  class ClientTypeValueValuesEnum(_messages.Enum):
    r"""Immutable. The type of oauth client. either public or private.

    Values:
      CLIENT_TYPE_UNSPECIFIED: should not be used
      PUBLIC_CLIENT: public client has no secret
      CONFIDENTIAL_CLIENT: private client
    """
    CLIENT_TYPE_UNSPECIFIED = 0
    PUBLIC_CLIENT = 1
    CONFIDENTIAL_CLIENT = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the oauth client.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      ACTIVE: The oauth client is active.
      DELETED: The oauth client is soft-deleted. Soft-deleted oauth client is
        permanently deleted after approximately 30 days unless restored via
        UndeleteOauthClient.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETED = 2

  allowedGrantTypes = _messages.EnumField('AllowedGrantTypesValueListEntryValuesEnum', 1, repeated=True)
  allowedRedirectUris = _messages.StringField(2, repeated=True)
  allowedScopes = _messages.StringField(3, repeated=True)
  clientId = _messages.StringField(4)
  clientType = _messages.EnumField('ClientTypeValueValuesEnum', 5)
  description = _messages.StringField(6)
  disabled = _messages.BooleanField(7)
  displayName = _messages.StringField(8)
  expireTime = _messages.StringField(9)
  name = _messages.StringField(10)
  state = _messages.EnumField('StateValueValuesEnum', 11)


class OauthClientCredential(_messages.Message):
  r"""Represents an oauth client credential. Used to authenticate an oauth
  client while accessing Google Cloud resources on behalf of a user by using
  OAuth2 Protocol.

  Fields:
    clientSecret: Output only. The system-generated oauth client secret.
    createTime: Output only. The timestamp when the oauth client credential
      was created
    disabled: Optional. Whether the oauth client credential is disabled. You
      cannot use a disabled oauth client credential for OAuth.
    displayName: Optional. A user-specified display name of the oauth client
      credential Cannot exceed 32 characters.
    name: Immutable. The resource name of the oauth client credential. Format:
      `projects/{project}/locations/{location}/oauthClients/{oauth_client}/cre
      dentials/{credential}`
    updateTime: Output only. The timestamp for the last update of the oauth
      client credential. If no updates have been made, the creation time will
      serve as the designated value.
  """

  clientSecret = _messages.StringField(1)
  createTime = _messages.StringField(2)
  disabled = _messages.BooleanField(3)
  displayName = _messages.StringField(4)
  name = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class Oidc(_messages.Message):
  r"""Represents an OpenId Connect 1.0 identity provider.

  Fields:
    allowedAudiences: Acceptable values for the `aud` field (audience) in the
      OIDC token. Token exchange requests are rejected if the token audience
      does not match one of the configured values. Each audience may be at
      most 256 characters. A maximum of 10 audiences may be configured. If
      this list is empty, the OIDC token audience must be equal to the full
      canonical resource name of the WorkloadIdentityPoolProvider, with or
      without the HTTPS prefix. For example: ``` //iam.googleapis.com/projects
      //locations//workloadIdentityPools//providers/ https://iam.googleapis.co
      m/projects//locations//workloadIdentityPools//providers/ ```
    issuerUri: Required. The OIDC issuer URL. Must be an HTTPS endpoint.
    jwksJson: Optional. OIDC JWKs in JSON String format. For details on the
      definition of a JWK, see https://tools.ietf.org/html/rfc7517. If not
      set, the `jwks_uri` from the discovery document(fetched from the .well-
      known path of the `issuer_uri`) will be used. Currently, RSA and EC
      asymmetric keys are supported. The JWK must use following format and
      include only the following fields: { "keys": [ { "kty": "RSA/EC", "alg":
      "", "use": "sig", "kid": "", "n": "", "e": "", "x": "", "y": "", "crv":
      "" } ] }
  """

  allowedAudiences = _messages.StringField(1, repeated=True)
  issuerUri = _messages.StringField(2)
  jwksJson = _messages.StringField(3)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class PatchServiceAccountRequest(_messages.Message):
  r"""The service account patch request. You can patch only the `display_name`
  and `description` fields. You must use the `update_mask` field to specify
  which of these fields you want to patch. Only the fields specified in the
  request are guaranteed to be returned in the response. Other fields may be
  empty in the response.

  Fields:
    serviceAccount: A ServiceAccount attribute.
    updateMask: A string attribute.
  """

  serviceAccount = _messages.MessageField('ServiceAccount', 1)
  updateMask = _messages.StringField(2)


class Permission(_messages.Message):
  r"""A permission which can be included by a role.

  Enums:
    CustomRolesSupportLevelValueValuesEnum: The current custom role support
      level.
    StageValueValuesEnum: The current launch stage of the permission.

  Fields:
    apiDisabled: The service API associated with the permission is not
      enabled.
    customRolesSupportLevel: The current custom role support level.
    description: A brief description of what this Permission is used for. This
      permission can ONLY be used in predefined roles.
    name: The name of this Permission.
    onlyInPredefinedRoles: A boolean attribute.
    primaryPermission: The preferred name for this permission. If present,
      then this permission is an alias of, and equivalent to, the listed
      primary_permission.
    stage: The current launch stage of the permission.
    title: The title of this Permission.
  """

  class CustomRolesSupportLevelValueValuesEnum(_messages.Enum):
    r"""The current custom role support level.

    Values:
      SUPPORTED: Default state. Permission is fully supported for custom role
        use.
      TESTING: Permission is being tested to check custom role compatibility.
      NOT_SUPPORTED: Permission is not supported for custom role use.
    """
    SUPPORTED = 0
    TESTING = 1
    NOT_SUPPORTED = 2

  class StageValueValuesEnum(_messages.Enum):
    r"""The current launch stage of the permission.

    Values:
      ALPHA: The permission is currently in an alpha phase.
      BETA: The permission is currently in a beta phase.
      GA: The permission is generally available.
      DEPRECATED: The permission is being deprecated.
    """
    ALPHA = 0
    BETA = 1
    GA = 2
    DEPRECATED = 3

  apiDisabled = _messages.BooleanField(1)
  customRolesSupportLevel = _messages.EnumField('CustomRolesSupportLevelValueValuesEnum', 2)
  description = _messages.StringField(3)
  name = _messages.StringField(4)
  onlyInPredefinedRoles = _messages.BooleanField(5)
  primaryPermission = _messages.StringField(6)
  stage = _messages.EnumField('StageValueValuesEnum', 7)
  title = _messages.StringField(8)


class PermissionDelta(_messages.Message):
  r"""A PermissionDelta message to record the added_permissions and
  removed_permissions inside a role.

  Fields:
    addedPermissions: Added permissions.
    removedPermissions: Removed permissions.
  """

  addedPermissions = _messages.StringField(1, repeated=True)
  removedPermissions = _messages.StringField(2, repeated=True)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class PolicyDelta(_messages.Message):
  r"""The difference delta between two policies.

  Fields:
    bindingDeltas: The delta for Bindings between two policies.
  """

  bindingDeltas = _messages.MessageField('BindingDelta', 1, repeated=True)


class QueryAuditableServicesRequest(_messages.Message):
  r"""A request to get the list of auditable services for a resource.

  Fields:
    fullResourceName: Required. The full resource name to query from the list
      of auditable services. The name follows the Google Cloud Platform
      resource format. For example, a Cloud Platform project with id `my-
      project` will be named
      `//cloudresourcemanager.googleapis.com/projects/my-project`.
  """

  fullResourceName = _messages.StringField(1)


class QueryAuditableServicesResponse(_messages.Message):
  r"""A response containing a list of auditable services for a resource.

  Fields:
    services: The auditable services for a resource.
  """

  services = _messages.MessageField('AuditableService', 1, repeated=True)


class QueryGrantableRolesRequest(_messages.Message):
  r"""The grantable role query request.

  Enums:
    ViewValueValuesEnum:

  Fields:
    fullResourceName: Required. The full resource name to query from the list
      of grantable roles. The name follows the Google Cloud Platform resource
      format. For example, a Cloud Platform project with id `my-project` will
      be named `//cloudresourcemanager.googleapis.com/projects/my-project`.
    pageSize: Optional limit on the number of roles to include in the
      response. The default is 300, and the maximum is 1,000.
    pageToken: Optional pagination token returned in an earlier
      QueryGrantableRolesResponse.
    view: A ViewValueValuesEnum attribute.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""ViewValueValuesEnum enum type.

    Values:
      BASIC: Omits the `included_permissions` field. This is the default
        value.
      FULL: Returns all fields.
    """
    BASIC = 0
    FULL = 1

  fullResourceName = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  view = _messages.EnumField('ViewValueValuesEnum', 4)


class QueryGrantableRolesResponse(_messages.Message):
  r"""The grantable role query response.

  Fields:
    nextPageToken: To retrieve the next page of results, set
      `QueryGrantableRolesRequest.page_token` to this value.
    roles: The list of matching roles.
  """

  nextPageToken = _messages.StringField(1)
  roles = _messages.MessageField('Role', 2, repeated=True)


class QueryTestablePermissionsRequest(_messages.Message):
  r"""A request to get permissions which can be tested on a resource.

  Fields:
    fullResourceName: Required. The full resource name to query from the list
      of testable permissions. The name follows the Google Cloud Platform
      resource format. For example, a Cloud Platform project with id `my-
      project` will be named
      `//cloudresourcemanager.googleapis.com/projects/my-project`.
    pageSize: Optional limit on the number of permissions to include in the
      response. The default is 100, and the maximum is 1,000.
    pageToken: Optional pagination token returned in an earlier
      QueryTestablePermissionsRequest.
  """

  fullResourceName = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class QueryTestablePermissionsResponse(_messages.Message):
  r"""The response containing permissions which can be tested on a resource.

  Fields:
    nextPageToken: To retrieve the next page of results, set
      `QueryTestableRolesRequest.page_token` to this value.
    permissions: The Permissions testable on the requested resource.
  """

  nextPageToken = _messages.StringField(1)
  permissions = _messages.MessageField('Permission', 2, repeated=True)


class Role(_messages.Message):
  r"""A role in the Identity and Access Management API.

  Enums:
    StageValueValuesEnum: The current launch stage of the role. If the `ALPHA`
      launch stage has been selected for a role, the `stage` field will not be
      included in the returned definition for the role.

  Fields:
    deleted: The current deleted state of the role. This field is read only.
      It will be ignored in calls to CreateRole and UpdateRole.
    description: Optional. A human-readable description for the role.
    etag: Used to perform a consistent read-modify-write.
    includedPermissions: The names of the permissions this role grants when
      bound in an IAM policy.
    name: The name of the role. When `Role` is used in `CreateRole`, the role
      name must not be set. When `Role` is used in output and other input such
      as `UpdateRole`, the role name is the complete path. For example,
      `roles/logging.viewer` for predefined roles,
      `organizations/{ORGANIZATION_ID}/roles/my-role` for organization-level
      custom roles, and `projects/{PROJECT_ID}/roles/my-role` for project-
      level custom roles.
    stage: The current launch stage of the role. If the `ALPHA` launch stage
      has been selected for a role, the `stage` field will not be included in
      the returned definition for the role.
    title: Optional. A human-readable title for the role. Typically this is
      limited to 100 UTF-8 bytes.
  """

  class StageValueValuesEnum(_messages.Enum):
    r"""The current launch stage of the role. If the `ALPHA` launch stage has
    been selected for a role, the `stage` field will not be included in the
    returned definition for the role.

    Values:
      ALPHA: The user has indicated this role is currently in an Alpha phase.
        If this launch stage is selected, the `stage` field will not be
        included when requesting the definition for a given role.
      BETA: The user has indicated this role is currently in a Beta phase.
      GA: The user has indicated this role is generally available.
      DEPRECATED: The user has indicated this role is being deprecated.
      DISABLED: This role is disabled and will not contribute permissions to
        any principals it is granted to in policies.
      EAP: The user has indicated this role is currently in an EAP phase.
    """
    ALPHA = 0
    BETA = 1
    GA = 2
    DEPRECATED = 3
    DISABLED = 4
    EAP = 5

  deleted = _messages.BooleanField(1)
  description = _messages.StringField(2)
  etag = _messages.BytesField(3)
  includedPermissions = _messages.StringField(4, repeated=True)
  name = _messages.StringField(5)
  stage = _messages.EnumField('StageValueValuesEnum', 6)
  title = _messages.StringField(7)


class Saml(_messages.Message):
  r"""Represents an SAML 2.0 identity provider.

  Fields:
    idpMetadataXml: Required. SAML Identity provider configuration metadata
      xml doc. The xml document should comply with [SAML 2.0
      specification](https://www.oasis-
      open.org/committees/download.php/56785/sstc-saml-metadata-
      errata-2.0-wd-05.pdf). The max size of the acceptable xml document will
      be bounded to 128k characters. The metadata xml document should satisfy
      the following constraints: 1) Must contain an Identity Provider Entity
      ID. 2) Must contain at least one non-expired signing key certificate. 3)
      For each signing key: a) Valid from should be no more than 7 days from
      now. b) Valid to should be no more than 14 years in the future. 4) Upto
      3 IdP signing keys are allowed in the metadata xml. When updating the
      provider's metadata xml, at lease one non-expired signing key must
      overlap with the existing metadata. This requirement is skipped if there
      are no non-expired signing keys present in the existing metadata
  """

  idpMetadataXml = _messages.StringField(1)


class ServiceAccount(_messages.Message):
  r"""An IAM service account. A service account is an account for an
  application or a virtual machine (VM) instance, not a person. You can use a
  service account to call Google APIs. To learn more, read the [overview of
  service accounts](https://cloud.google.com/iam/help/service-
  accounts/overview). When you create a service account, you specify the
  project ID that owns the service account, as well as a name that must be
  unique within the project. IAM uses these values to create an email address
  that identifies the service account. //

  Fields:
    description: Optional. A user-specified, human-readable description of the
      service account. The maximum length is 256 UTF-8 bytes.
    disabled: Output only. Whether the service account is disabled.
    displayName: Optional. A user-specified, human-readable name for the
      service account. The maximum length is 100 UTF-8 bytes.
    email: Output only. The email address of the service account.
    etag: Deprecated. Do not use.
    name: The resource name of the service account. Use one of the following
      formats: * `projects/{PROJECT_ID}/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/{PROJECT_ID}/serviceAccounts/{UNIQUE_ID}` As an alternative,
      you can use the `-` wildcard character instead of the project ID: *
      `projects/-/serviceAccounts/{EMAIL_ADDRESS}` *
      `projects/-/serviceAccounts/{UNIQUE_ID}` When possible, avoid using the
      `-` wildcard character, because it can cause response messages to
      contain misleading error codes. For example, if you try to access the
      service account `projects/-/serviceAccounts/<EMAIL>`, which
      does not exist, the response contains an HTTP `403 Forbidden` error
      instead of a `404 Not Found` error.
    oauth2ClientId: Output only. The OAuth 2.0 client ID for the service
      account.
    projectId: Output only. The ID of the project that owns the service
      account.
    uniqueId: Output only. The unique, stable numeric ID for the service
      account. Each service account retains its unique ID even if you delete
      the service account. For example, if you delete a service account, then
      create a new service account with the same name, the new service account
      has a different unique ID than the deleted service account.
  """

  description = _messages.StringField(1)
  disabled = _messages.BooleanField(2)
  displayName = _messages.StringField(3)
  email = _messages.StringField(4)
  etag = _messages.BytesField(5)
  name = _messages.StringField(6)
  oauth2ClientId = _messages.StringField(7)
  projectId = _messages.StringField(8)
  uniqueId = _messages.StringField(9)


class ServiceAccountIdentityBinding(_messages.Message):
  r"""Represents a service account identity provider reference. A service
  account has at most one identity binding for the EAP. This is an alternative
  to service account keys and enables the service account to be configured to
  trust an external IDP through the provided identity binding.

  Fields:
    acceptanceFilter: A CEL expression that is evaluated to determine whether
      a credential should be accepted. To accept any credential, specify
      "true". See: https://github.com/google/cel-spec . This field supports a
      subset of the CEL functionality to select fields and evaluate boolean
      expressions based on the input (no functions or arithmetics). The values
      for input claims are available using `inclaim.attribute_name` or
      `inclaim[\"attribute_name\"]`. The values for output attributes
      calculated by the translator are available using
      `outclaim.attribute_name` or `outclaim[\"attribute_name\"]`.
    cel: A set of output attributes and corresponding input attribute
      expressions.
    name: The resource name of the service account identity binding in the
      following format `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}/identi
      tyBindings/{BINDING}`.
    oidc: OIDC with discovery.
  """

  acceptanceFilter = _messages.StringField(1)
  cel = _messages.MessageField('AttributeTranslatorCEL', 2)
  name = _messages.StringField(3)
  oidc = _messages.MessageField('IDPReferenceOIDC', 4)


class ServiceAccountKey(_messages.Message):
  r"""Represents a service account key. A service account has two sets of key-
  pairs: user-managed, and system-managed. User-managed key-pairs can be
  created and deleted by users. Users are responsible for rotating these keys
  periodically to ensure security of their service accounts. Users retain the
  private key of these key-pairs, and Google retains ONLY the public key.
  System-managed keys are automatically rotated by Google, and are used for
  signing for a maximum of two weeks. The rotation process is probabilistic,
  and usage of the new key will gradually ramp up and down over the key's
  lifetime. If you cache the public key set for a service account, we
  recommend that you update the cache every 15 minutes. User-managed keys can
  be added and removed at any time, so it is important to update the cache
  frequently. For Google-managed keys, Google will publish a key at least 6
  hours before it is first used for signing and will keep publishing it for at
  least 6 hours after it was last used for signing. Public keys for all
  service accounts are also published at the OAuth2 Service Account API.

  Enums:
    KeyAlgorithmValueValuesEnum: Specifies the algorithm (and possibly key
      size) for the key.
    KeyOriginValueValuesEnum: The key origin.
    KeyTypeValueValuesEnum: The key type.
    PrivateKeyTypeValueValuesEnum: The output format for the private key. Only
      provided in `CreateServiceAccountKey` responses, not in
      `GetServiceAccountKey` or `ListServiceAccountKey` responses. Google
      never exposes system-managed private keys, and never retains user-
      managed private keys.

  Fields:
    disabled: The key status.
    keyAlgorithm: Specifies the algorithm (and possibly key size) for the key.
    keyOrigin: The key origin.
    keyType: The key type.
    name: The resource name of the service account key in the following format
      `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}/keys/{key}`.
    privateKeyData: The private key data. Only provided in
      `CreateServiceAccountKey` responses. Make sure to keep the private key
      data secure because it allows for the assertion of the service account
      identity. When base64 decoded, the private key data can be used to
      authenticate with Google API client libraries and with gcloud auth
      activate-service-account.
    privateKeyType: The output format for the private key. Only provided in
      `CreateServiceAccountKey` responses, not in `GetServiceAccountKey` or
      `ListServiceAccountKey` responses. Google never exposes system-managed
      private keys, and never retains user-managed private keys.
    publicKeyData: The public key data. Only provided in
      `GetServiceAccountKey` responses.
    validAfterTime: The key can be used after this timestamp.
    validBeforeTime: The key can be used before this timestamp. For system-
      managed key pairs, this timestamp is the end time for the private key
      signing operation. The public key could still be used for verification
      for a few hours after this time.
  """

  class KeyAlgorithmValueValuesEnum(_messages.Enum):
    r"""Specifies the algorithm (and possibly key size) for the key.

    Values:
      KEY_ALG_UNSPECIFIED: An unspecified key algorithm.
      KEY_ALG_RSA_1024: 1k RSA Key.
      KEY_ALG_RSA_2048: 2k RSA Key.
    """
    KEY_ALG_UNSPECIFIED = 0
    KEY_ALG_RSA_1024 = 1
    KEY_ALG_RSA_2048 = 2

  class KeyOriginValueValuesEnum(_messages.Enum):
    r"""The key origin.

    Values:
      ORIGIN_UNSPECIFIED: Unspecified key origin.
      USER_PROVIDED: Key is provided by user.
      GOOGLE_PROVIDED: Key is provided by Google.
    """
    ORIGIN_UNSPECIFIED = 0
    USER_PROVIDED = 1
    GOOGLE_PROVIDED = 2

  class KeyTypeValueValuesEnum(_messages.Enum):
    r"""The key type.

    Values:
      KEY_TYPE_UNSPECIFIED: Unspecified key type. The presence of this in the
        message will immediately result in an error.
      USER_MANAGED: User-managed keys (managed and rotated by the user).
      SYSTEM_MANAGED: System-managed keys (managed and rotated by Google).
    """
    KEY_TYPE_UNSPECIFIED = 0
    USER_MANAGED = 1
    SYSTEM_MANAGED = 2

  class PrivateKeyTypeValueValuesEnum(_messages.Enum):
    r"""The output format for the private key. Only provided in
    `CreateServiceAccountKey` responses, not in `GetServiceAccountKey` or
    `ListServiceAccountKey` responses. Google never exposes system-managed
    private keys, and never retains user-managed private keys.

    Values:
      TYPE_UNSPECIFIED: Unspecified. Equivalent to
        `TYPE_GOOGLE_CREDENTIALS_FILE`.
      TYPE_PKCS12_FILE: PKCS12 format. The password for the PKCS12 file is
        `notasecret`. For more information, see
        https://tools.ietf.org/html/rfc7292.
      TYPE_GOOGLE_CREDENTIALS_FILE: Google Credentials File format.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PKCS12_FILE = 1
    TYPE_GOOGLE_CREDENTIALS_FILE = 2

  disabled = _messages.BooleanField(1)
  keyAlgorithm = _messages.EnumField('KeyAlgorithmValueValuesEnum', 2)
  keyOrigin = _messages.EnumField('KeyOriginValueValuesEnum', 3)
  keyType = _messages.EnumField('KeyTypeValueValuesEnum', 4)
  name = _messages.StringField(5)
  privateKeyData = _messages.BytesField(6)
  privateKeyType = _messages.EnumField('PrivateKeyTypeValueValuesEnum', 7)
  publicKeyData = _messages.BytesField(8)
  validAfterTime = _messages.StringField(9)
  validBeforeTime = _messages.StringField(10)


class ServiceConfig(_messages.Message):
  r"""Configuration for a service.

  Fields:
    domain: Optional. Domain name of the service. Example:
      console.cloud.google
  """

  domain = _messages.StringField(1)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class SignBlobRequest(_messages.Message):
  r"""Deprecated. [Migrate to Service Account Credentials
  API](https://cloud.google.com/iam/help/credentials/migrate-api). The service
  account sign blob request.

  Fields:
    bytesToSign: Required. Deprecated. [Migrate to Service Account Credentials
      API](https://cloud.google.com/iam/help/credentials/migrate-api). The
      bytes to sign.
  """

  bytesToSign = _messages.BytesField(1)


class SignBlobResponse(_messages.Message):
  r"""Deprecated. [Migrate to Service Account Credentials
  API](https://cloud.google.com/iam/help/credentials/migrate-api). The service
  account sign blob response.

  Fields:
    keyId: Deprecated. [Migrate to Service Account Credentials
      API](https://cloud.google.com/iam/help/credentials/migrate-api). The id
      of the key used to sign the blob.
    signature: Deprecated. [Migrate to Service Account Credentials
      API](https://cloud.google.com/iam/help/credentials/migrate-api). The
      signed blob.
  """

  keyId = _messages.StringField(1)
  signature = _messages.BytesField(2)


class SignJwtRequest(_messages.Message):
  r"""Deprecated. [Migrate to Service Account Credentials
  API](https://cloud.google.com/iam/help/credentials/migrate-api). The service
  account sign JWT request.

  Fields:
    payload: Required. Deprecated. [Migrate to Service Account Credentials
      API](https://cloud.google.com/iam/help/credentials/migrate-api). The JWT
      payload to sign. Must be a serialized JSON object that contains a JWT
      Claims Set. For example: `{"sub": "<EMAIL>", "iat": 313435}` If
      the JWT Claims Set contains an expiration time (`exp`) claim, it must be
      an integer timestamp that is not in the past and no more than 12 hours
      in the future. If the JWT Claims Set does not contain an expiration time
      (`exp`) claim, this claim is added automatically, with a timestamp that
      is 1 hour in the future.
  """

  payload = _messages.StringField(1)


class SignJwtResponse(_messages.Message):
  r"""Deprecated. [Migrate to Service Account Credentials
  API](https://cloud.google.com/iam/help/credentials/migrate-api). The service
  account sign JWT response.

  Fields:
    keyId: Deprecated. [Migrate to Service Account Credentials
      API](https://cloud.google.com/iam/help/credentials/migrate-api). The id
      of the key used to sign the JWT.
    signedJwt: Deprecated. [Migrate to Service Account Credentials
      API](https://cloud.google.com/iam/help/credentials/migrate-api). The
      signed JWT.
  """

  keyId = _messages.StringField(1)
  signedJwt = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class UndeleteOauthClientRequest(_messages.Message):
  r"""Request message for UndeleteOauthClient.

  Fields:
    validateOnly: Optional. If set, validate the request and preview the
      response, but do not actually post it.
  """

  validateOnly = _messages.BooleanField(1)


class UndeleteRoleRequest(_messages.Message):
  r"""The request to undelete an existing role.

  Fields:
    etag: Used to perform a consistent read-modify-write.
  """

  etag = _messages.BytesField(1)


class UndeleteServiceAccountRequest(_messages.Message):
  r"""The service account undelete request."""


class UndeleteServiceAccountResponse(_messages.Message):
  r"""A UndeleteServiceAccountResponse object.

  Fields:
    restoredAccount: Metadata for the restored service account.
  """

  restoredAccount = _messages.MessageField('ServiceAccount', 1)


class UndeleteWorkforcePoolInstalledAppRequest(_messages.Message):
  r"""Request message for UndeleteWorkforcePoolInstalledApp.

  Fields:
    validateOnly: Optional. If set, validate the request and preview the
      response, but do not actually post it.
  """

  validateOnly = _messages.BooleanField(1)


class UndeleteWorkforcePoolProviderKeyRequest(_messages.Message):
  r"""Request message for UndeleteWorkforcePoolProviderKey."""


class UndeleteWorkforcePoolProviderRequest(_messages.Message):
  r"""Request message for UndeleteWorkforcePoolProvider."""


class UndeleteWorkforcePoolRequest(_messages.Message):
  r"""Request message for UndeleteWorkforcePool."""


class UndeleteWorkforcePoolSubjectRequest(_messages.Message):
  r"""Request message for UndeleteWorkforcePoolSubject."""


class UndeleteWorkloadIdentityPoolManagedIdentityRequest(_messages.Message):
  r"""Request message for UndeleteWorkloadIdentityPoolManagedIdentity."""


class UndeleteWorkloadIdentityPoolNamespaceRequest(_messages.Message):
  r"""Request message for UndeleteWorkloadIdentityPoolNamespace."""


class UndeleteWorkloadIdentityPoolProviderKeyRequest(_messages.Message):
  r"""Request message for UndeleteWorkloadIdentityPoolProviderKey."""


class UndeleteWorkloadIdentityPoolProviderRequest(_messages.Message):
  r"""Request message for UndeleteWorkloadIdentityPoolProvider."""


class UndeleteWorkloadIdentityPoolRequest(_messages.Message):
  r"""Request message for UndeleteWorkloadIdentityPool."""


class UploadServiceAccountKeyRequest(_messages.Message):
  r"""The service account key upload request.

  Fields:
    publicKeyData: The public key to associate with the service account. Must
      be an RSA public key that is wrapped in an X.509 v3 certificate. Include
      the first line, `-----BEGIN CERTIFICATE-----`, and the last line,
      `-----END CERTIFICATE-----`.
  """

  publicKeyData = _messages.BytesField(1)


class WorkforcePool(_messages.Message):
  r"""Represents a collection of external workforces. Provides namespaces for
  federated users that can be referenced in IAM policies.

  Enums:
    StateValueValuesEnum: Output only. The state of the pool.

  Fields:
    accessRestrictions: Optional. Configure access restrictions on the
      workforce pool users. This is an optional field. If specified web sign-
      in can be restricted to given set of services or programmatic sign-in
      can be disabled for pool users.
    description: A user-specified description of the pool. Cannot exceed 256
      characters.
    disabled: Disables the workforce pool. You cannot use a disabled pool to
      exchange tokens, or use existing tokens to access resources. If the pool
      is re-enabled, existing tokens grant access again.
    displayName: A user-specified display name of the pool in Google Cloud
      Console. Cannot exceed 32 characters.
    expireTime: Output only. Time after which the workforce pool will be
      permanently purged and cannot be recovered.
    name: Output only. The resource name of the pool. Format:
      `locations/{location}/workforcePools/{workforce_pool_id}`
    parent: Immutable. The resource name of the parent. Format:
      `organizations/{org-id}`.
    sessionDuration: Duration that the Google Cloud access tokens, console
      sign-in sessions, and `gcloud` sign-in sessions from this pool are
      valid. Must be greater than 15 minutes (900s) and less than 12 hours
      (43200s). If `session_duration` is not configured, minted credentials
      have a default duration of one hour (3600s). For SAML providers, the
      lifetime of the token is the minimum of the `session_duration` and the
      `SessionNotOnOrAfter` claim in the SAML assertion.
    state: Output only. The state of the pool.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the pool.

    Values:
      STATE_UNSPECIFIED: State unspecified.
      ACTIVE: The pool is active and may be used in Google Cloud policies.
      DELETED: The pool is soft-deleted. Soft-deleted pools are permanently
        deleted after approximately 30 days. You can restore a soft-deleted
        pool using UndeleteWorkforcePool. You cannot reuse the ID of a soft-
        deleted pool until it is permanently deleted. While a pool is deleted,
        you cannot use it to exchange tokens, or use existing tokens to access
        resources. If the pool is undeleted, existing tokens grant access
        again.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETED = 2

  accessRestrictions = _messages.MessageField('AccessRestrictions', 1)
  description = _messages.StringField(2)
  disabled = _messages.BooleanField(3)
  displayName = _messages.StringField(4)
  expireTime = _messages.StringField(5)
  name = _messages.StringField(6)
  parent = _messages.StringField(7)
  sessionDuration = _messages.StringField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)


class WorkforcePoolInstalledApp(_messages.Message):
  r"""Represents a workforce pool installed app. Used to indicate that a
  workforce pool administrator has completed the installation process, thereby
  giving consent for the installed app, i.e. OAuth Client, to access workforce
  pool users' information and resources.

  Enums:
    StateValueValuesEnum: Output only. The state of the workforce pool
      installed app.

  Fields:
    appId: Output only. The UUID of the app that is installed. Current only
      OAuth Client is supported. If the installed app is an OAuth client, this
      field represents the system generated OAuth client ID.
    createTime: Output only. The timestamp when the workforce pool installed
      app was created.
    deleteTime: Output only. The timestamp that the workforce pool installed
      app was soft deleted.
    description: Optional. A user-specified description of the workforce pool
      installed app. Cannot exceed 256 characters.
    displayName: Optional. A user-specified display name of the workforce pool
      installed app Cannot exceed 32 characters.
    expireTime: Output only. Time after which the workforce pool installed app
      will be permanently purged and cannot be recovered.
    name: Immutable. The resource name of the workforce pool installed app.
      Format: `locations/{location}/workforcePools/{workforce_pool}/installedA
      pps/{installed_app}`
    oauthClient: Immutable. The resource name of an OAuth client to be
      installed. Format:
      `projects/{project}/locations/{location}/oauthClients/{oauth_client}`.
    state: Output only. The state of the workforce pool installed app.
    updateTime: Output only. The timestamp for the last update of the
      workforce pool installed app.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the workforce pool installed app.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      ACTIVE: The workforce pool installed app is active.
      DELETED: The workforce pool installed app is soft-deleted. Soft-deleted
        workforce pool installed apps are permanently deleted after
        approximately 30 days unless restored via
        UndeleteWorkforcePoolInstalledApp.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETED = 2

  appId = _messages.StringField(1)
  createTime = _messages.StringField(2)
  deleteTime = _messages.StringField(3)
  description = _messages.StringField(4)
  displayName = _messages.StringField(5)
  expireTime = _messages.StringField(6)
  name = _messages.StringField(7)
  oauthClient = _messages.StringField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  updateTime = _messages.StringField(10)


class WorkforcePoolProvider(_messages.Message):
  r"""A configuration for an external identity provider.

  Enums:
    StateValueValuesEnum: Output only. The state of the provider.

  Messages:
    AttributeMappingValue: Required. Maps attributes from the authentication
      credentials issued by an external identity provider to Google Cloud
      attributes, such as `subject` and `segment`. Each key must be a string
      specifying the Google Cloud IAM attribute to map to. The following keys
      are supported: * `google.subject`: The principal IAM is authenticating.
      You can reference this value in IAM bindings. This is also the subject
      that appears in Cloud Logging logs. This is a required field and the
      mapped subject cannot exceed 127 bytes. * `google.groups`: Groups the
      authenticating user belongs to. You can grant groups access to resources
      using an IAM `principalSet` binding; access applies to all members of
      the group. * `google.display_name`: The name of the authenticated user.
      This is an optional field and the mapped display name cannot exceed 100
      bytes. If not set, `google.subject` will be displayed instead. This
      attribute cannot be referenced in IAM bindings. *
      `google.profile_photo`: The URL that specifies the authenticated user's
      thumbnail photo. This is an optional field. When set, the image will be
      visible as the user's profile picture. If not set, a generic user icon
      will be displayed instead. This attribute cannot be referenced in IAM
      bindings. You can also provide custom attributes by specifying
      `attribute.{custom_attribute}`, where {custom_attribute} is the name of
      the custom attribute to be mapped. You can define a maximum of 50 custom
      attributes. The maximum length of a mapped attribute key is 100
      characters, and the key may only contain the characters [a-z0-9_]. You
      can reference these attributes in IAM policies to define fine-grained
      access for a workforce pool to Google Cloud resources. For example: *
      `google.subject`: `principal://iam.googleapis.com/locations/global/workf
      orcePools/{pool}/subject/{value}` * `google.groups`: `principalSet://iam
      .googleapis.com/locations/global/workforcePools/{pool}/group/{value}` *
      `attribute.{custom_attribute}`: `principalSet://iam.googleapis.com/locat
      ions/global/workforcePools/{pool}/attribute.{custom_attribute}/{value}`
      Each value must be a [Common Expression Language]
      (https://opensource.google/projects/cel) function that maps an identity
      provider credential to the normalized attribute specified by the
      corresponding map key. You can use the `assertion` keyword in the
      expression to access a JSON representation of the authentication
      credential issued by the provider. The maximum length of an attribute
      mapping expression is 2048 characters. When evaluated, the total size of
      all mapped attributes must not exceed 4KB. For OIDC providers, you must
      supply a custom mapping that includes the `google.subject` attribute.
      For example, the following maps the `sub` claim of the incoming
      credential to the `subject` attribute on a Google token: ```
      {"google.subject": "assertion.sub"} ```

  Fields:
    attributeCondition: A [Common Expression
      Language](https://opensource.google/projects/cel) expression, in plain
      text, to restrict what otherwise valid authentication credentials issued
      by the provider should not be accepted. The expression must output a
      boolean representing whether to allow the federation. The following
      keywords may be referenced in the expressions: * `assertion`: JSON
      representing the authentication credential issued by the provider. *
      `google`: The Google attributes mapped from the assertion in the
      `attribute_mappings`. `google.profile_photo` and `google.display_name`
      are not supported. * `attribute`: The custom attributes mapped from the
      assertion in the `attribute_mappings`. The maximum length of the
      attribute condition expression is 4096 characters. If unspecified, all
      valid authentication credentials will be accepted. The following example
      shows how to only allow credentials with a mapped `google.groups` value
      of `admins`: ``` "'admins' in google.groups" ```
    attributeMapping: Required. Maps attributes from the authentication
      credentials issued by an external identity provider to Google Cloud
      attributes, such as `subject` and `segment`. Each key must be a string
      specifying the Google Cloud IAM attribute to map to. The following keys
      are supported: * `google.subject`: The principal IAM is authenticating.
      You can reference this value in IAM bindings. This is also the subject
      that appears in Cloud Logging logs. This is a required field and the
      mapped subject cannot exceed 127 bytes. * `google.groups`: Groups the
      authenticating user belongs to. You can grant groups access to resources
      using an IAM `principalSet` binding; access applies to all members of
      the group. * `google.display_name`: The name of the authenticated user.
      This is an optional field and the mapped display name cannot exceed 100
      bytes. If not set, `google.subject` will be displayed instead. This
      attribute cannot be referenced in IAM bindings. *
      `google.profile_photo`: The URL that specifies the authenticated user's
      thumbnail photo. This is an optional field. When set, the image will be
      visible as the user's profile picture. If not set, a generic user icon
      will be displayed instead. This attribute cannot be referenced in IAM
      bindings. You can also provide custom attributes by specifying
      `attribute.{custom_attribute}`, where {custom_attribute} is the name of
      the custom attribute to be mapped. You can define a maximum of 50 custom
      attributes. The maximum length of a mapped attribute key is 100
      characters, and the key may only contain the characters [a-z0-9_]. You
      can reference these attributes in IAM policies to define fine-grained
      access for a workforce pool to Google Cloud resources. For example: *
      `google.subject`: `principal://iam.googleapis.com/locations/global/workf
      orcePools/{pool}/subject/{value}` * `google.groups`: `principalSet://iam
      .googleapis.com/locations/global/workforcePools/{pool}/group/{value}` *
      `attribute.{custom_attribute}`: `principalSet://iam.googleapis.com/locat
      ions/global/workforcePools/{pool}/attribute.{custom_attribute}/{value}`
      Each value must be a [Common Expression Language]
      (https://opensource.google/projects/cel) function that maps an identity
      provider credential to the normalized attribute specified by the
      corresponding map key. You can use the `assertion` keyword in the
      expression to access a JSON representation of the authentication
      credential issued by the provider. The maximum length of an attribute
      mapping expression is 2048 characters. When evaluated, the total size of
      all mapped attributes must not exceed 4KB. For OIDC providers, you must
      supply a custom mapping that includes the `google.subject` attribute.
      For example, the following maps the `sub` claim of the incoming
      credential to the `subject` attribute on a Google token: ```
      {"google.subject": "assertion.sub"} ```
    description: A user-specified description of the provider. Cannot exceed
      256 characters.
    disabled: Disables the workforce pool provider. You cannot use a disabled
      provider to exchange tokens. However, existing tokens still grant
      access.
    displayName: A user-specified display name for the provider. Cannot exceed
      32 characters.
    expireTime: Output only. Time after which the workload pool provider will
      be permanently purged and cannot be recovered.
    name: Output only. The resource name of the provider. Format: `locations/{
      location}/workforcePools/{workforce_pool_id}/providers/{provider_id}`
    oidc: An OpenId Connect 1.0 identity provider configuration.
    saml: A SAML identity provider configuration.
    state: Output only. The state of the provider.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the provider.

    Values:
      STATE_UNSPECIFIED: State unspecified.
      ACTIVE: The provider is active and may be used to validate
        authentication credentials.
      DELETED: The provider is soft-deleted. Soft-deleted providers are
        permanently deleted after approximately 30 days. You can restore a
        soft-deleted provider using UndeleteWorkforcePoolProvider.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributeMappingValue(_messages.Message):
    r"""Required. Maps attributes from the authentication credentials issued
    by an external identity provider to Google Cloud attributes, such as
    `subject` and `segment`. Each key must be a string specifying the Google
    Cloud IAM attribute to map to. The following keys are supported: *
    `google.subject`: The principal IAM is authenticating. You can reference
    this value in IAM bindings. This is also the subject that appears in Cloud
    Logging logs. This is a required field and the mapped subject cannot
    exceed 127 bytes. * `google.groups`: Groups the authenticating user
    belongs to. You can grant groups access to resources using an IAM
    `principalSet` binding; access applies to all members of the group. *
    `google.display_name`: The name of the authenticated user. This is an
    optional field and the mapped display name cannot exceed 100 bytes. If not
    set, `google.subject` will be displayed instead. This attribute cannot be
    referenced in IAM bindings. * `google.profile_photo`: The URL that
    specifies the authenticated user's thumbnail photo. This is an optional
    field. When set, the image will be visible as the user's profile picture.
    If not set, a generic user icon will be displayed instead. This attribute
    cannot be referenced in IAM bindings. You can also provide custom
    attributes by specifying `attribute.{custom_attribute}`, where
    {custom_attribute} is the name of the custom attribute to be mapped. You
    can define a maximum of 50 custom attributes. The maximum length of a
    mapped attribute key is 100 characters, and the key may only contain the
    characters [a-z0-9_]. You can reference these attributes in IAM policies
    to define fine-grained access for a workforce pool to Google Cloud
    resources. For example: * `google.subject`: `principal://iam.googleapis.co
    m/locations/global/workforcePools/{pool}/subject/{value}` *
    `google.groups`: `principalSet://iam.googleapis.com/locations/global/workf
    orcePools/{pool}/group/{value}` * `attribute.{custom_attribute}`: `princip
    alSet://iam.googleapis.com/locations/global/workforcePools/{pool}/attribut
    e.{custom_attribute}/{value}` Each value must be a [Common Expression
    Language] (https://opensource.google/projects/cel) function that maps an
    identity provider credential to the normalized attribute specified by the
    corresponding map key. You can use the `assertion` keyword in the
    expression to access a JSON representation of the authentication
    credential issued by the provider. The maximum length of an attribute
    mapping expression is 2048 characters. When evaluated, the total size of
    all mapped attributes must not exceed 4KB. For OIDC providers, you must
    supply a custom mapping that includes the `google.subject` attribute. For
    example, the following maps the `sub` claim of the incoming credential to
    the `subject` attribute on a Google token: ``` {"google.subject":
    "assertion.sub"} ```

    Messages:
      AdditionalProperty: An additional property for a AttributeMappingValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        AttributeMappingValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributeMappingValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributeCondition = _messages.StringField(1)
  attributeMapping = _messages.MessageField('AttributeMappingValue', 2)
  description = _messages.StringField(3)
  disabled = _messages.BooleanField(4)
  displayName = _messages.StringField(5)
  expireTime = _messages.StringField(6)
  name = _messages.StringField(7)
  oidc = _messages.MessageField('GoogleIamAdminV1WorkforcePoolProviderOidc', 8)
  saml = _messages.MessageField('GoogleIamAdminV1WorkforcePoolProviderSaml', 9)
  state = _messages.EnumField('StateValueValuesEnum', 10)


class WorkforcePoolProviderKey(_messages.Message):
  r"""Represents a public key configuration for a Workforce Pool Provider. The
  key can be configured in your identity provider to encrypt SAML assertions.
  Google holds the corresponding private key, which it uses to decrypt
  encrypted tokens.

  Enums:
    StateValueValuesEnum: Output only. The state of the key.
    UseValueValuesEnum: Required. The purpose of the key.

  Fields:
    expireTime: Output only. The time after which the key will be permanently
      deleted and cannot be recovered. Note that the key may get purged before
      this time if the total limit of keys per provider is exceeded.
    keyData: Immutable. Public half of the asymmetric key.
    name: Output only. The resource name of the key.
    state: Output only. The state of the key.
    use: Required. The purpose of the key.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the key.

    Values:
      STATE_UNSPECIFIED: State unspecified.
      ACTIVE: The key is active.
      DELETED: The key is soft-deleted. Soft-deleted keys are permanently
        deleted after approximately 30 days. You can restore a soft-deleted
        key using UndeleteWorkforcePoolProviderKey.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETED = 2

  class UseValueValuesEnum(_messages.Enum):
    r"""Required. The purpose of the key.

    Values:
      KEY_USE_UNSPECIFIED: KeyUse unspecified.
      ENCRYPTION: The key is used for encryption.
    """
    KEY_USE_UNSPECIFIED = 0
    ENCRYPTION = 1

  expireTime = _messages.StringField(1)
  keyData = _messages.MessageField('KeyData', 2)
  name = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  use = _messages.EnumField('UseValueValuesEnum', 5)


class WorkloadIdentityPool(_messages.Message):
  r"""Represents a collection of external workload identities. You can define
  IAM policies to grant these identities access to Google Cloud resources.

  Enums:
    IdentityModeValueValuesEnum: Immutable. The identity mode of the pool.
    StateValueValuesEnum: Output only. The state of the pool.

  Fields:
    description: A description of the pool. Cannot exceed 256 characters.
    disabled: Whether the pool is disabled. You cannot use a disabled pool to
      exchange tokens, or use existing tokens to access resources. If the pool
      is re-enabled, existing tokens grant access again.
    displayName: A display name for the pool. Cannot exceed 32 characters.
    expireTime: Output only. Time after which the workload identity pool will
      be permanently purged and cannot be recovered.
    identityMode: Immutable. The identity mode of the pool.
    name: Output only. The resource name of the pool.
    sessionDuration: Overrides the lifespan of access tokens issued when
      federating using this pool. If not set, the lifespan of issued access
      tokens is computed based on the type of identity provider: - For AWS
      providers, the default access token lifespan is equal to 15 minutes. -
      For OIDC providers, the default access token lifespan is equal to the
      remaining lifespan of the exchanged OIDC ID token, with a maximum limit
      of 1 hour. If set, session duration must be between 2 minutes and 12
      hours. Organization administrators can further restrict the maximum
      allowed session_duration value using the iam-
      workloadIdentitySessionDuration Resource Setting.
    state: Output only. The state of the pool.
  """

  class IdentityModeValueValuesEnum(_messages.Enum):
    r"""Immutable. The identity mode of the pool.

    Values:
      IDENTITY_MODE_UNSPECIFIED: Existing pools will be in this mode. For
        existing worklod identity pools created through the public API, they
        will act as if they are set to FEDERATION_ONLY.
      FEDERATION_ONLY: With FEDERATION_ONLY mode, providers can be created at
        the root level within the pool. Attribute mappings must specify a
        "google.subject" claim that specifies the identity of the federation
        workload. Namespace or any sub-namespace resources is not allowed with
        this mode.
      TRUST_DOMAIN: With TRUST_DOMAIN mode, providers can be created at the
        root level within the pool. Attribute mappings must specify the
        "google.namespace" and "google.workload_identifier" claims that,
        respectively, specify the namespace and individual sub-namespace
        identifier for the workload. Namespaces and sub-Namespace resources
        are allowed.
    """
    IDENTITY_MODE_UNSPECIFIED = 0
    FEDERATION_ONLY = 1
    TRUST_DOMAIN = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the pool.

    Values:
      STATE_UNSPECIFIED: State unspecified.
      ACTIVE: The pool is active, and may be used in Google Cloud policies.
      DELETED: The pool is soft-deleted. Soft-deleted pools are permanently
        deleted after approximately 30 days. You can restore a soft-deleted
        pool using UndeleteWorkloadIdentityPool. You cannot reuse the ID of a
        soft-deleted pool until it is permanently deleted. While a pool is
        deleted, you cannot use it to exchange tokens, or use existing tokens
        to access resources. If the pool is undeleted, existing tokens grant
        access again.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETED = 2

  description = _messages.StringField(1)
  disabled = _messages.BooleanField(2)
  displayName = _messages.StringField(3)
  expireTime = _messages.StringField(4)
  identityMode = _messages.EnumField('IdentityModeValueValuesEnum', 5)
  name = _messages.StringField(6)
  sessionDuration = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)


class WorkloadIdentityPoolManagedIdentity(_messages.Message):
  r"""Represents a managed identity for a workload identity pool namespace.

  Enums:
    StateValueValuesEnum: Output only. The state of the managed identity.

  Fields:
    description: A description of the managed identity. Cannot exceed 256
      characters.
    disabled: Whether the managed identity is disabled. If disabled,
      credentials may no longer be issued for this identity and this identity
      will no longer be able to access Google Cloud APIs. Existing credentials
      may continue to be accepted by third party APIs and workloads until they
      expire.
    expireTime: Output only. Time after which the managed identity will be
      permanently purged and cannot be recovered.
    name: Output only. The resource name of the managed identity.
    state: Output only. The state of the managed identity.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the managed identity.

    Values:
      STATE_UNSPECIFIED: State unspecified.
      ACTIVE: The managed identity is active.
      DELETED: The managed identity is soft-deleted. Soft-deleted managed
        identities are permanently deleted after approximately 30 days. You
        can restore a soft-deleted managed identity using
        UndeleteWorkloadIdentityPoolManagedIdentity. You cannot reuse the ID
        of a soft-deleted managed identity until it is permanently deleted.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETED = 2

  description = _messages.StringField(1)
  disabled = _messages.BooleanField(2)
  expireTime = _messages.StringField(3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class WorkloadIdentityPoolNamespace(_messages.Message):
  r"""Represents a namespace for a workload identity pool.

  Enums:
    StateValueValuesEnum: Output only. The state of the namespace.

  Fields:
    description: A description of the namespace. Cannot exceed 256 characters.
    disabled: Whether the namespace is disabled. If disabled, credentials may
      no longer be issued for identities within this namespace (including
      federated identities) and they will no longer be able to access Google
      Cloud APIs. Existing credentials may continue to be accepted by third
      party APIs and workloads until they expire.
    expireTime: Output only. Time after which the namespace will be
      permanently purged and cannot be recovered.
    name: Output only. The resource name of the namespace.
    state: Output only. The state of the namespace.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the namespace.

    Values:
      STATE_UNSPECIFIED: State unspecified.
      ACTIVE: The namespace is active.
      DELETED: The namespace is soft-deleted. Soft-deleted namespaces are
        permanently deleted after approximately 30 days. You can restore a
        soft-deleted namespace using UndeleteWorkloadIdentityPoolNamespace.
        You cannot reuse the ID of a soft-deleted namespace until it is
        permanently deleted.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETED = 2

  description = _messages.StringField(1)
  disabled = _messages.BooleanField(2)
  expireTime = _messages.StringField(3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class WorkloadIdentityPoolOperationMetadata(_messages.Message):
  r"""Metadata for long-running WorkloadIdentityPool operations."""


class WorkloadIdentityPoolProvider(_messages.Message):
  r"""A configuration for an external identity provider.

  Enums:
    StateValueValuesEnum: Output only. The state of the provider.

  Messages:
    AttributeMappingValue: Maps attributes from authentication credentials
      issued by an external identity provider to Google Cloud attributes, such
      as `subject` and `segment`. Each key must be a string specifying the
      Google Cloud IAM attribute to map to. The following keys are supported:
      * `google.subject`: The principal IAM is authenticating. You can
      reference this value in IAM bindings. This is also the subject that
      appears in Cloud Logging logs. Cannot exceed 127 bytes. *
      `google.groups`: Groups the external identity belongs to. You can grant
      groups access to resources using an IAM `principalSet` binding; access
      applies to all members of the group. You can also provide custom
      attributes by specifying `attribute.{custom_attribute}`, where
      `{custom_attribute}` is the name of the custom attribute to be mapped.
      You can define a maximum of 50 custom attributes. The maximum length of
      a mapped attribute key is 100 characters, and the key may only contain
      the characters [a-z0-9_]. You can reference these attributes in IAM
      policies to define fine-grained access for a workload to Google Cloud
      resources. For example: * `google.subject`: `principal://iam.googleapis.
      com/projects/{project}/locations/{location}/workloadIdentityPools/{pool}
      /subject/{value}` * `google.groups`: `principalSet://iam.googleapis.com/
      projects/{project}/locations/{location}/workloadIdentityPools/{pool}/gro
      up/{value}` * `attribute.{custom_attribute}`: `principalSet://iam.google
      apis.com/projects/{project}/locations/{location}/workloadIdentityPools/{
      pool}/attribute.{custom_attribute}/{value}` Each value must be a [Common
      Expression Language] (https://opensource.google/projects/cel) function
      that maps an identity provider credential to the normalized attribute
      specified by the corresponding map key. You can use the `assertion`
      keyword in the expression to access a JSON representation of the
      authentication credential issued by the provider. The maximum length of
      an attribute mapping expression is 2048 characters. When evaluated, the
      total size of all mapped attributes must not exceed 8KB. For AWS
      providers, if no attribute mapping is defined, the following default
      mapping applies: ``` { "google.subject":"assertion.arn",
      "attribute.aws_role": "assertion.arn.contains('assumed-role')" " ?
      assertion.arn.extract('{account_arn}assumed-role/')" " + 'assumed-
      role/'" " + assertion.arn.extract('assumed-role/{role_name}/')" " :
      assertion.arn", } ``` If any custom attribute mappings are defined, they
      must include a mapping to the `google.subject` attribute. For OIDC
      providers, you must supply a custom mapping, which must include the
      `google.subject` attribute. For example, the following maps the `sub`
      claim of the incoming credential to the `subject` attribute on a Google
      token: ``` {"google.subject": "assertion.sub"} ```

  Fields:
    attributeCondition: [A Common Expression
      Language](https://opensource.google/projects/cel) expression, in plain
      text, to restrict what otherwise valid authentication credentials issued
      by the provider should not be accepted. The expression must output a
      boolean representing whether to allow the federation. The following
      keywords may be referenced in the expressions: * `assertion`: JSON
      representing the authentication credential issued by the provider. *
      `google`: The Google attributes mapped from the assertion in the
      `attribute_mappings`. * `attribute`: The custom attributes mapped from
      the assertion in the `attribute_mappings`. The maximum length of the
      attribute condition expression is 4096 characters. If unspecified, all
      valid authentication credential are accepted. The following example
      shows how to only allow credentials with a mapped `google.groups` value
      of `admins`: ``` "'admins' in google.groups" ```
    attributeMapping: Maps attributes from authentication credentials issued
      by an external identity provider to Google Cloud attributes, such as
      `subject` and `segment`. Each key must be a string specifying the Google
      Cloud IAM attribute to map to. The following keys are supported: *
      `google.subject`: The principal IAM is authenticating. You can reference
      this value in IAM bindings. This is also the subject that appears in
      Cloud Logging logs. Cannot exceed 127 bytes. * `google.groups`: Groups
      the external identity belongs to. You can grant groups access to
      resources using an IAM `principalSet` binding; access applies to all
      members of the group. You can also provide custom attributes by
      specifying `attribute.{custom_attribute}`, where `{custom_attribute}` is
      the name of the custom attribute to be mapped. You can define a maximum
      of 50 custom attributes. The maximum length of a mapped attribute key is
      100 characters, and the key may only contain the characters [a-z0-9_].
      You can reference these attributes in IAM policies to define fine-
      grained access for a workload to Google Cloud resources. For example: *
      `google.subject`: `principal://iam.googleapis.com/projects/{project}/loc
      ations/{location}/workloadIdentityPools/{pool}/subject/{value}` *
      `google.groups`: `principalSet://iam.googleapis.com/projects/{project}/l
      ocations/{location}/workloadIdentityPools/{pool}/group/{value}` *
      `attribute.{custom_attribute}`: `principalSet://iam.googleapis.com/proje
      cts/{project}/locations/{location}/workloadIdentityPools/{pool}/attribut
      e.{custom_attribute}/{value}` Each value must be a [Common Expression
      Language] (https://opensource.google/projects/cel) function that maps an
      identity provider credential to the normalized attribute specified by
      the corresponding map key. You can use the `assertion` keyword in the
      expression to access a JSON representation of the authentication
      credential issued by the provider. The maximum length of an attribute
      mapping expression is 2048 characters. When evaluated, the total size of
      all mapped attributes must not exceed 8KB. For AWS providers, if no
      attribute mapping is defined, the following default mapping applies: ```
      { "google.subject":"assertion.arn", "attribute.aws_role":
      "assertion.arn.contains('assumed-role')" " ?
      assertion.arn.extract('{account_arn}assumed-role/')" " + 'assumed-
      role/'" " + assertion.arn.extract('assumed-role/{role_name}/')" " :
      assertion.arn", } ``` If any custom attribute mappings are defined, they
      must include a mapping to the `google.subject` attribute. For OIDC
      providers, you must supply a custom mapping, which must include the
      `google.subject` attribute. For example, the following maps the `sub`
      claim of the incoming credential to the `subject` attribute on a Google
      token: ``` {"google.subject": "assertion.sub"} ```
    aws: An Amazon Web Services identity provider.
    description: A description for the provider. Cannot exceed 256 characters.
    disabled: Whether the provider is disabled. You cannot use a disabled
      provider to exchange tokens. However, existing tokens still grant
      access.
    displayName: A display name for the provider. Cannot exceed 32 characters.
    expireTime: Output only. Time after which the workload identity pool
      provider will be permanently purged and cannot be recovered.
    name: Output only. The resource name of the provider.
    oidc: An OpenId Connect 1.0 identity provider.
    saml: An SAML 2.0 identity provider.
    state: Output only. The state of the provider.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the provider.

    Values:
      STATE_UNSPECIFIED: State unspecified.
      ACTIVE: The provider is active, and may be used to validate
        authentication credentials.
      DELETED: The provider is soft-deleted. Soft-deleted providers are
        permanently deleted after approximately 30 days. You can restore a
        soft-deleted provider using UndeleteWorkloadIdentityPoolProvider. You
        cannot reuse the ID of a soft-deleted provider until it is permanently
        deleted.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributeMappingValue(_messages.Message):
    r"""Maps attributes from authentication credentials issued by an external
    identity provider to Google Cloud attributes, such as `subject` and
    `segment`. Each key must be a string specifying the Google Cloud IAM
    attribute to map to. The following keys are supported: * `google.subject`:
    The principal IAM is authenticating. You can reference this value in IAM
    bindings. This is also the subject that appears in Cloud Logging logs.
    Cannot exceed 127 bytes. * `google.groups`: Groups the external identity
    belongs to. You can grant groups access to resources using an IAM
    `principalSet` binding; access applies to all members of the group. You
    can also provide custom attributes by specifying
    `attribute.{custom_attribute}`, where `{custom_attribute}` is the name of
    the custom attribute to be mapped. You can define a maximum of 50 custom
    attributes. The maximum length of a mapped attribute key is 100
    characters, and the key may only contain the characters [a-z0-9_]. You can
    reference these attributes in IAM policies to define fine-grained access
    for a workload to Google Cloud resources. For example: * `google.subject`:
    `principal://iam.googleapis.com/projects/{project}/locations/{location}/wo
    rkloadIdentityPools/{pool}/subject/{value}` * `google.groups`: `principalS
    et://iam.googleapis.com/projects/{project}/locations/{location}/workloadId
    entityPools/{pool}/group/{value}` * `attribute.{custom_attribute}`: `princ
    ipalSet://iam.googleapis.com/projects/{project}/locations/{location}/workl
    oadIdentityPools/{pool}/attribute.{custom_attribute}/{value}` Each value
    must be a [Common Expression Language]
    (https://opensource.google/projects/cel) function that maps an identity
    provider credential to the normalized attribute specified by the
    corresponding map key. You can use the `assertion` keyword in the
    expression to access a JSON representation of the authentication
    credential issued by the provider. The maximum length of an attribute
    mapping expression is 2048 characters. When evaluated, the total size of
    all mapped attributes must not exceed 8KB. For AWS providers, if no
    attribute mapping is defined, the following default mapping applies: ``` {
    "google.subject":"assertion.arn", "attribute.aws_role":
    "assertion.arn.contains('assumed-role')" " ?
    assertion.arn.extract('{account_arn}assumed-role/')" " + 'assumed-role/'"
    " + assertion.arn.extract('assumed-role/{role_name}/')" " :
    assertion.arn", } ``` If any custom attribute mappings are defined, they
    must include a mapping to the `google.subject` attribute. For OIDC
    providers, you must supply a custom mapping, which must include the
    `google.subject` attribute. For example, the following maps the `sub`
    claim of the incoming credential to the `subject` attribute on a Google
    token: ``` {"google.subject": "assertion.sub"} ```

    Messages:
      AdditionalProperty: An additional property for a AttributeMappingValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        AttributeMappingValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributeMappingValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributeCondition = _messages.StringField(1)
  attributeMapping = _messages.MessageField('AttributeMappingValue', 2)
  aws = _messages.MessageField('Aws', 3)
  description = _messages.StringField(4)
  disabled = _messages.BooleanField(5)
  displayName = _messages.StringField(6)
  expireTime = _messages.StringField(7)
  name = _messages.StringField(8)
  oidc = _messages.MessageField('Oidc', 9)
  saml = _messages.MessageField('Saml', 10)
  state = _messages.EnumField('StateValueValuesEnum', 11)


class WorkloadIdentityPoolProviderKey(_messages.Message):
  r"""Represents a public key configuration for your workload identity pool
  provider. The key can be configured in your identity provider to encrypt the
  SAML assertions. Google holds the corresponding private key which it uses to
  decrypt encrypted tokens.

  Enums:
    StateValueValuesEnum: Output only. The state of the key.
    UseValueValuesEnum: Required. The purpose of the key.

  Fields:
    expireTime: Output only. Time after which the key will be permanently
      purged and cannot be recovered. Note that the key may get purged before
      this timestamp if the total limit of keys per provider is crossed.
    keyData: Immutable. Public half of the asymmetric key.
    name: Output only. The resource name of the key.
    state: Output only. The state of the key.
    use: Required. The purpose of the key.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the key.

    Values:
      STATE_UNSPECIFIED: State unspecified.
      ACTIVE: The key is active.
      DELETED: The key is soft-deleted. Soft-deleted keys are permanently
        deleted after approximately 30 days. You can restore a soft-deleted
        key using UndeleteWorkloadIdentityPoolProviderKey. While a key is
        deleted, you cannot use it during the federation.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETED = 2

  class UseValueValuesEnum(_messages.Enum):
    r"""Required. The purpose of the key.

    Values:
      KEY_USE_UNSPECIFIED: The key use is not known.
      ENCRYPTION: The public key is used for encryption purposes.
    """
    KEY_USE_UNSPECIFIED = 0
    ENCRYPTION = 1

  expireTime = _messages.StringField(1)
  keyData = _messages.MessageField('KeyData', 2)
  name = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  use = _messages.EnumField('UseValueValuesEnum', 5)


class WorkloadSource(_messages.Message):
  r"""Represents a workload source for a namespace or a managed identity. A
  workload source is used to determine whether a workload can attest an
  identity based on the conditions.

  Fields:
    conditionSet: Required. A set of allowlisted attribute values. Applicable
      if you are using a Google Cloud workload source, such as projects/.
    etag: Optional. The etag for this workload source. If this is provided on
      update, it must match the server's etag.
    name: Output only. The resource name of the workload source. The format
      should be one of: * /workloadSources/ * /workloadSources/
  """

  conditionSet = _messages.MessageField('WorkloadSourceConditionSet', 1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3)


class WorkloadSourceCondition(_messages.Message):
  r"""Defines the attribute-value pair that will be used as a matching
  condition for a Google Cloud workload source.

  Fields:
    attribute: Required. The attribute key that will be matched. Supported
      attributes are `resource` and `attached_service_account`.
    value: Required. The value that should exactly match the attribute of the
      workload. Supported values are: * Arbitrary string representing a
      resource or a service account * "ALL" The value "ALL" can be used to
      indicate that any workload source is allowed. However, it can only be
      used when the attribute is "resource". For example, the following
      condition would allow all workload sources: .
  """

  attribute = _messages.StringField(1)
  value = _messages.StringField(2)


class WorkloadSourceConditionSet(_messages.Message):
  r"""A set of WorkloadSourceConditions used to match the container workload
  source. The workload is considered to match the policy if at least one
  condition matches the workload. The maximum number of conditions is
  restricted to 50 WorkloadSourceConditions.

  Fields:
    conditions: Required. A list of WorkloadSourceConditions.
  """

  conditions = _messages.MessageField('WorkloadSourceCondition', 1, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

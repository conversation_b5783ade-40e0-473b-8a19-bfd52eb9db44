"""Generated message classes for networksecurity version v1beta1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'networksecurity'


class AddAddressGroupItemsRequest(_messages.Message):
  r"""Request used by the AddAddressGroupItems method.

  Fields:
    items: Required. List of items to add.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  items = _messages.StringField(1, repeated=True)
  requestId = _messages.StringField(2)


class AddressGroup(_messages.Message):
  r"""AddressGroup is a resource that specifies how a collection of IP/DNS
  used in Firewall Policy.

  Enums:
    TypeValueValuesEnum: Required. The type of the Address Group. Possible
      values are "IPv4" or "IPV6".

  Messages:
    LabelsValue: Optional. Set of label tags associated with the AddressGroup
      resource.

  Fields:
    capacity: Required. Capacity of the Address Group
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. Free-text description of the resource.
    items: Optional. List of items.
    labels: Optional. Set of label tags associated with the AddressGroup
      resource.
    name: Required. Name of the AddressGroup resource. It matches pattern
      `projects/*/locations/{location}/addressGroups/`.
    selfLink: Output only. Server-defined fully-qualified URL for this
      resource.
    type: Required. The type of the Address Group. Possible values are "IPv4"
      or "IPV6".
    updateTime: Output only. The timestamp when the resource was updated.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of the Address Group. Possible values are "IPv4" or
    "IPV6".

    Values:
      TYPE_UNSPECIFIED: Default value.
      IPV4: IP v4 ranges.
      IPV6: IP v6 ranges.
    """
    TYPE_UNSPECIFIED = 0
    IPV4 = 1
    IPV6 = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the AddressGroup resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  capacity = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  items = _messages.StringField(4, repeated=True)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  selfLink = _messages.StringField(7)
  type = _messages.EnumField('TypeValueValuesEnum', 8)
  updateTime = _messages.StringField(9)


class AuthorizationLoggingOptions(_messages.Message):
  r"""Authorization-related information used by Cloud Audit Logging.

  Enums:
    PermissionTypeValueValuesEnum: The type of the permission that was
      checked.

  Fields:
    permissionType: The type of the permission that was checked.
  """

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type of the permission that was checked.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: A read of admin (meta) data.
      ADMIN_WRITE: A write of admin (meta) data.
      DATA_READ: A read of standard data.
      DATA_WRITE: A write of standard data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 1)


class AuthorizationPolicy(_messages.Message):
  r"""AuthorizationPolicy is a resource that specifies how a server should
  authorize incoming connections. This resource in itself does not change the
  configuration unless it's attached to a target https proxy or endpoint
  config selector resource.

  Enums:
    ActionValueValuesEnum: Required. The action to take when a rule match is
      found. Possible values are "ALLOW" or "DENY".

  Messages:
    LabelsValue: Optional. Set of label tags associated with the
      AuthorizationPolicy resource.

  Fields:
    action: Required. The action to take when a rule match is found. Possible
      values are "ALLOW" or "DENY".
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. Free-text description of the resource.
    internalCaller: Optional. A flag set to identify internal controllers
      Setting this will trigger a P4SA check to validate the caller is from an
      allowlisted service's P4SA even if other optional fields are unset.
    labels: Optional. Set of label tags associated with the
      AuthorizationPolicy resource.
    name: Required. Name of the AuthorizationPolicy resource. It matches
      pattern
      `projects/{project}/locations/{location}/authorizationPolicies/`.
    rules: Optional. List of rules to match. Note that at least one of the
      rules must match in order for the action specified in the 'action' field
      to be taken. A rule is a match if there is a matching source and
      destination. If left blank, the action specified in the `action` field
      will be applied on every request.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. The action to take when a rule match is found. Possible
    values are "ALLOW" or "DENY".

    Values:
      ACTION_UNSPECIFIED: Default value.
      ALLOW: Grant access.
      DENY: Deny access. Deny rules should be avoided unless they are used to
        provide a default "deny all" fallback.
    """
    ACTION_UNSPECIFIED = 0
    ALLOW = 1
    DENY = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the AuthorizationPolicy
    resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  internalCaller = _messages.BooleanField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  rules = _messages.MessageField('Rule', 7, repeated=True)
  updateTime = _messages.StringField(8)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CertificateProviderInstance(_messages.Message):
  r"""Specification of a TLS certificate provider instance. Workloads may have
  one or more CertificateProvider instances (plugins) and one of them is
  enabled and configured by specifying this message. Workloads use the values
  from this message to locate and load the CertificateProvider instance
  configuration.

  Fields:
    pluginInstance: Required. Plugin instance name, used to locate and load
      CertificateProvider instance configuration. Set to
      "google_cloud_private_spiffe" to use Certificate Authority Service
      certificate provider instance.
  """

  pluginInstance = _messages.StringField(1)


class ClientTlsPolicy(_messages.Message):
  r"""ClientTlsPolicy is a resource that specifies how a client should
  authenticate connections to backends of a service. This resource itself does
  not affect configuration unless it is attached to a backend service
  resource.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the resource.

  Fields:
    clientCertificate: Optional. Defines a mechanism to provision client
      identity (public and private keys) for peer to peer authentication. The
      presence of this dictates mTLS.
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. Free-text description of the resource.
    internalCaller: Optional. A flag set to identify internal controllers
      Setting this will trigger a P4SA check to validate the caller is from an
      allowlisted service's P4SA even if other optional fields are unset.
    labels: Optional. Set of label tags associated with the resource.
    name: Required. Name of the ClientTlsPolicy resource. It matches the
      pattern
      `projects/*/locations/{location}/clientTlsPolicies/{client_tls_policy}`
    serverValidationCa: Optional. Defines the mechanism to obtain the
      Certificate Authority certificate to validate the server certificate. If
      empty, client does not validate the server certificate.
    sni: Optional. Server Name Indication string to present to the server
      during TLS handshake. E.g: "secure.example.com".
    subjectAltNames: Optional. A list of alternate names to verify the server
      identity in the certificate. If specified, the client will verify that
      the server certificate's subject alt name matches one of the specified
      values. If specified, this list overrides the value of subject_alt_names
      from the BackendService.securitySettings.subjectAltNames[]. The domain
      names can be either be exact match (e.g foo) or suffix matches (e.g foo*
      or foo/*)
    targets: Optional. Define a list of targets this policy should serve. A
      target can only be a BackendService and it should be the fully qualified
      name of the BackendService, e.g.:
      projects/xxx/backendServices/locations/global/xxx NOTE: ClientTlsPolicy
      and the referenced BackendServices must be present in the same project.
      This is used only for Google Service Mesh (GSM) product.
    updateTime: Output only. The timestamp when the resource was updated.
    workloadContextSelectors: Optional. Selects the workload where the policy
      should be applied to its targets. A policy without a
      WorkloadContextSelector should always be applied to its targets when
      there is no conflict. If there are multiple WorkloadContextSelectors
      then the policy will be applied to all targets if ANY of the
      WorkloadContextSelectors match. Therefore these selectors can be
      combined in an OR fashion. If there are multiple ClientTlsPolicy
      targeted to the same BackendService, There should be only one effective
      ClientTlsPolicy and the precdence is as following: 1) ClientTlsPolicy
      with workload_context_selectors will take precedence first. 2) If there
      are multiple ClientTlsPolicy with workload_context_selectors matched,
      earliest created one will take take precedence. 3) Then ClientTlsPolicy
      without workloadSelector will take precedence. Right now we don't allow
      multiple ClientTlsPolicy without workloadSelector attached the same
      backendService. NOTE: For GSM use only.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  clientCertificate = _messages.MessageField('GoogleCloudNetworksecurityV1beta1CertificateProvider', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  internalCaller = _messages.BooleanField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  serverValidationCa = _messages.MessageField('ValidationCA', 7, repeated=True)
  sni = _messages.StringField(8)
  subjectAltNames = _messages.StringField(9, repeated=True)
  targets = _messages.StringField(10, repeated=True)
  updateTime = _messages.StringField(11)
  workloadContextSelectors = _messages.MessageField('WorkloadContextSelector', 12, repeated=True)


class CloneAddressGroupItemsRequest(_messages.Message):
  r"""Request used by the CloneAddressGroupItems method.

  Fields:
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    sourceAddressGroup: Required. Source address group to clone items from.
  """

  requestId = _messages.StringField(1)
  sourceAddressGroup = _messages.StringField(2)


class CreateReferenceRequest(_messages.Message):
  r"""The CreateReferenceRequest request.

  Fields:
    parent: Required. The parent resource name (target_resource of this
      reference). For example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}`.
    reference: Required. The reference to be created.
    referenceId: The unique id of this resource. Must be unique within a scope
      of a target resource, but does not have to be globally unique. Reference
      ID is part of resource name of the reference. Resource name is generated
      in the following way: {parent}/references/{reference_id}. Reference ID
      field is currently required but id auto generation might be added in the
      future. It can be any arbitrary string, either GUID or any other string,
      however CLHs can use preprocess callbacks to perform a custom
      validation.
    requestId: Optional. Request ID is an idempotency ID of the request. It
      must be a valid UUID. Zero UUID (00000000-0000-0000-0000-000000000000)
      is not supported.
  """

  parent = _messages.StringField(1)
  reference = _messages.MessageField('Reference', 2)
  referenceId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class DeleteReferenceRequest(_messages.Message):
  r"""The DeleteReferenceRequest request.

  Fields:
    name: Required. Full resource name of the reference, in the following
      format:
      `//{targer_service}/{target_resource}/references/{reference_id}`. For
      example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}/references/{xyz}`.
    requestId: Optional. Request ID is an idempotency ID of the request. It
      must be a valid UUID. Zero UUID (00000000-0000-0000-0000-000000000000)
      is not supported.
  """

  name = _messages.StringField(1)
  requestId = _messages.StringField(2)


class Destination(_messages.Message):
  r"""Specification of traffic destination attributes.

  Fields:
    hosts: Required. List of host names to match. Matched against the
      ":authority" header in http requests. At least one host should match.
      Each host can be an exact match, or a prefix match (example
      "mydomain.*") or a suffix match (example "*.myorg.com") or a presence
      (any) match "*".
    httpHeaderMatch: Optional. Match against key:value pair in http header.
      Provides a flexible match based on HTTP headers, for potentially
      advanced use cases. At least one header should match. Avoid using header
      matches to make authorization decisions unless there is a strong
      guarantee that requests arrive through a trusted client or proxy.
    methods: Optional. A list of HTTP methods to match. At least one method
      should match. Should not be set for gRPC services.
    paths: Optional. A list of HTTP paths to match. gRPC methods must be
      presented as fully-qualified name in the form of
      "/packageName.serviceName/methodName". At least one path should match.
      Each path can be an exact match, or a prefix match (example,
      "/packageName.serviceName/*") or a suffix match (example, "*/video") or
      a presence (any) match "*".
    ports: Required. List of destination ports to match. At least one port
      should match.
  """

  hosts = _messages.StringField(1, repeated=True)
  httpHeaderMatch = _messages.MessageField('HttpHeaderMatch', 2)
  methods = _messages.StringField(3, repeated=True)
  paths = _messages.StringField(4, repeated=True)
  ports = _messages.IntegerField(5, repeated=True, variant=_messages.Variant.UINT32)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class FirewallEndpoint(_messages.Message):
  r"""Message describing Endpoint object

  Enums:
    StateValueValuesEnum: Output only. Current state of the endpoint.

  Messages:
    LabelsValue: Labels as key value pairs

  Fields:
    associatedNetworks: Output only. List of networks that are associated with
      this endpoint in the local zone. This is a projection of the
      FirewallEndpointAssociations pointing at this endpoint. A network will
      only appear in this list after traffic routing is fully configured.
      Format: projects/{project}/global/networks/{name}.
    createTime: Output only. Create time stamp
    labels: Labels as key value pairs
    name: Output only. name of resource
    reconciling: Output only. Whether reconciling is in progress, recommended
      per https://google.aip.dev/128.
    state: Output only. Current state of the endpoint.
    updateTime: Output only. Update time stamp
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the endpoint.

    Values:
      STATE_UNSPECIFIED: Not set.
      CREATING: Being created.
      ACTIVE: Processing configuration updates.
      DELETING: Being deleted.
      INACTIVE: Down or in an error state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    INACTIVE = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  associatedNetworks = _messages.StringField(1, repeated=True)
  createTime = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  reconciling = _messages.BooleanField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  updateTime = _messages.StringField(7)


class FirewallEndpointAssociation(_messages.Message):
  r"""Message describing Association object

  Enums:
    StateValueValuesEnum: Output only. Current state of the association.

  Messages:
    LabelsValue: Labels as key value pairs

  Fields:
    createTime: Output only. Create time stamp
    firewallEndpoint: Required. The URL of the FirewallEndpoint that is being
      associated.
    labels: Labels as key value pairs
    name: Output only. name of resource
    network: Required. The URL of the network that is being associated.
    reconciling: Output only. Whether reconciling is in progress, recommended
      per https://google.aip.dev/128.
    state: Output only. Current state of the association.
    tlsInspectionPolicy: Optional. The URL of the TlsInspectionPolicy that is
      being associated.
    updateTime: Output only. Update time stamp
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the association.

    Values:
      STATE_UNSPECIFIED: Not set.
      CREATING: Being created.
      ACTIVE: Active and ready for traffic.
      DELETING: Being deleted.
      INACTIVE: Down or in an error state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    INACTIVE = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  firewallEndpoint = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  network = _messages.StringField(5)
  reconciling = _messages.BooleanField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  tlsInspectionPolicy = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class GatewaySecurityPolicy(_messages.Message):
  r"""The GatewaySecurityPolicy resource contains a collection of
  GatewaySecurityPolicyRules and associated metadata.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. Free-text description of the resource.
    name: Required. Name of the resource. Name is of the form projects/{projec
      t}/locations/{location}/gatewaySecurityPolicies/{gateway_security_policy
      } gateway_security_policy should match the
      pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
    tlsInspectionPolicy: Optional. Name of a TLS Inspection Policy resource
      that defines how TLS inspection will be performed for any rule(s) which
      enables it.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  name = _messages.StringField(3)
  tlsInspectionPolicy = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class GatewaySecurityPolicyRule(_messages.Message):
  r"""The GatewaySecurityPolicyRule resource is in a nested collection within
  a GatewaySecurityPolicy and represents a traffic matching condition and
  associated action to perform.

  Enums:
    BasicProfileValueValuesEnum: Required. Profile which tells what the
      primitive action should be.

  Fields:
    applicationMatcher: Optional. CEL expression for matching on
      L7/application level criteria.
    basicProfile: Required. Profile which tells what the primitive action
      should be.
    createTime: Output only. Time when the rule was created.
    description: Optional. Free-text description of the resource.
    enabled: Required. Whether the rule is enforced.
    name: Required. Immutable. Name of the resource. ame is the full resource
      name so projects/{project}/locations/{location}/gatewaySecurityPolicies/
      {gateway_security_policy}/rules/{rule} rule should match the pattern:
      (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
    priority: Required. Priority of the rule. Lower number corresponds to
      higher precedence.
    sessionMatcher: Required. CEL expression for matching on session criteria.
    tlsInspectionEnabled: Optional. Flag to enable TLS inspection of traffic
      matching on , can only be true if the parent GatewaySecurityPolicy
      references a TLSInspectionConfig.
    updateTime: Output only. Time when the rule was updated.
  """

  class BasicProfileValueValuesEnum(_messages.Enum):
    r"""Required. Profile which tells what the primitive action should be.

    Values:
      BASIC_PROFILE_UNSPECIFIED: If there is not a mentioned action for the
        target.
      ALLOW: Allow the matched traffic.
      DENY: Deny the matched traffic.
    """
    BASIC_PROFILE_UNSPECIFIED = 0
    ALLOW = 1
    DENY = 2

  applicationMatcher = _messages.StringField(1)
  basicProfile = _messages.EnumField('BasicProfileValueValuesEnum', 2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  enabled = _messages.BooleanField(5)
  name = _messages.StringField(6)
  priority = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  sessionMatcher = _messages.StringField(8)
  tlsInspectionEnabled = _messages.BooleanField(9)
  updateTime = _messages.StringField(10)


class GetReferenceRequest(_messages.Message):
  r"""The GetReferenceRequest request.

  Fields:
    name: Required. Full resource name of the reference, in the following
      format:
      `//{target_service}/{target_resource}/references/{reference_id}`. For
      example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}/references/{xyz}`.
  """

  name = _messages.StringField(1)


class GoogleCloudNetworksecurityV1beta1CertificateProvider(_messages.Message):
  r"""Specification of certificate provider. Defines the mechanism to obtain
  the certificate and private key for peer to peer authentication.

  Fields:
    certificateProviderInstance: The certificate provider instance
      specification that will be passed to the data plane, which will be used
      to load necessary credential information.
    grpcEndpoint: gRPC specific configuration to access the gRPC server to
      obtain the cert and private key.
  """

  certificateProviderInstance = _messages.MessageField('CertificateProviderInstance', 1)
  grpcEndpoint = _messages.MessageField('GoogleCloudNetworksecurityV1beta1GrpcEndpoint', 2)


class GoogleCloudNetworksecurityV1beta1GrpcEndpoint(_messages.Message):
  r"""Specification of the GRPC Endpoint.

  Fields:
    sdsResource: Optional. sds_resource is used to set the name of the SDS
      configuration. When used in the context of GSM, the following rules
      apply If the resource name is "default" and "ROOTCA" then it implies
      ISTIO_MUTUAL tlsMode. If the resource name begins with "file-cert"
      and/or "file-root", it implies custom MUTUAL tlsMode
    targetUri: Required. The target URI of the gRPC endpoint. Only UDS path is
      supported, and should start with "unix:".
  """

  sdsResource = _messages.StringField(1)
  targetUri = _messages.StringField(2)


class GoogleIamV1AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('GoogleIamV1AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class GoogleIamV1AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    ignoreChildExemptions: A boolean attribute.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  ignoreChildExemptions = _messages.BooleanField(2)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 3)


class GoogleIamV1Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    bindingId: A string attribute.
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  bindingId = _messages.StringField(1)
  condition = _messages.MessageField('Expr', 2)
  members = _messages.StringField(3, repeated=True)
  role = _messages.StringField(4)


class GoogleIamV1CloudAuditOptions(_messages.Message):
  r"""Write a Cloud Audit log

  Enums:
    LogNameValueValuesEnum: The log_name to populate in the Cloud Audit
      Record.

  Fields:
    authorizationLoggingOptions: Information used by the Cloud Audit Logging
      pipeline.
    logName: The log_name to populate in the Cloud Audit Record.
  """

  class LogNameValueValuesEnum(_messages.Enum):
    r"""The log_name to populate in the Cloud Audit Record.

    Values:
      UNSPECIFIED_LOG_NAME: Default. Should not be used.
      ADMIN_ACTIVITY: Corresponds to "cloudaudit.googleapis.com/activity"
      DATA_ACCESS: Corresponds to "cloudaudit.googleapis.com/data_access"
    """
    UNSPECIFIED_LOG_NAME = 0
    ADMIN_ACTIVITY = 1
    DATA_ACCESS = 2

  authorizationLoggingOptions = _messages.MessageField('AuthorizationLoggingOptions', 1)
  logName = _messages.EnumField('LogNameValueValuesEnum', 2)


class GoogleIamV1Condition(_messages.Message):
  r"""A condition to be met.

  Enums:
    IamValueValuesEnum: Trusted attributes supplied by the IAM system.
    OpValueValuesEnum: An operator to apply the subject with.
    SysValueValuesEnum: Trusted attributes supplied by any service that owns
      resources and uses the IAM system for access control.

  Fields:
    iam: Trusted attributes supplied by the IAM system.
    op: An operator to apply the subject with.
    svc: Trusted attributes discharged by the service.
    sys: Trusted attributes supplied by any service that owns resources and
      uses the IAM system for access control.
    values: The objects of the condition.
  """

  class IamValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by the IAM system.

    Values:
      NO_ATTR: Default non-attribute.
      AUTHORITY: Either principal or (if present) authority selector.
      ATTRIBUTION: The principal (even if an authority selector is present),
        which must only be used for attribution, not authorization.
      SECURITY_REALM: Any of the security realms in the IAMContext
        (go/security-realms). When used with IN, the condition indicates "any
        of the request's realms match one of the given values; with NOT_IN,
        "none of the realms match any of the given values". Note that a value
        can be: - 'self:campus' (i.e., clients that are in the same campus) -
        'self:metro' (i.e., clients that are in the same metro) - 'self:cloud-
        region' (i.e., allow connections from clients that are in the same
        cloud region) - 'self:prod-region' (i.e., allow connections from
        clients that are in the same prod region) - 'guardians' (i.e., allow
        connections from its guardian realms. See go/security-realms-
        glossary#guardian for more information.) - 'self' [DEPRECATED] (i.e.,
        allow connections from clients that are in the same security realm,
        which is currently but not guaranteed to be campus-sized) - a realm
        (e.g., 'campus-abc') - a realm group (e.g., 'realms-for-borg-cell-xx',
        see: go/realm-groups) A match is determined by a realm group
        membership check performed by a RealmAclRep object (go/realm-acl-
        howto). It is not permitted to grant access based on the *absence* of
        a realm, so realm conditions can only be used in a "positive" context
        (e.g., ALLOW/IN or DENY/NOT_IN).
      APPROVER: An approver (distinct from the requester) that has authorized
        this request. When used with IN, the condition indicates that one of
        the approvers associated with the request matches the specified
        principal, or is a member of the specified group. Approvers can only
        grant additional access, and are thus only used in a strictly positive
        context (e.g. ALLOW/IN or DENY/NOT_IN).
      JUSTIFICATION_TYPE: What types of justifications have been supplied with
        this request. String values should match enum names from
        security.credentials.JustificationType, e.g. "MANUAL_STRING". It is
        not permitted to grant access based on the *absence* of a
        justification, so justification conditions can only be used in a
        "positive" context (e.g., ALLOW/IN or DENY/NOT_IN). Multiple
        justifications, e.g., a Buganizer ID and a manually-entered reason,
        are normal and supported.
      CREDENTIALS_TYPE: What type of credentials have been supplied with this
        request. String values should match enum names from
        security_loas_l2.CredentialsType - currently, only
        CREDS_TYPE_EMERGENCY is supported. It is not permitted to grant access
        based on the *absence* of a credentials type, so the conditions can
        only be used in a "positive" context (e.g., ALLOW/IN or DENY/NOT_IN).
      CREDS_ASSERTION: EXPERIMENTAL -- DO NOT USE. The conditions can only be
        used in a "positive" context (e.g., ALLOW/IN or DENY/NOT_IN).
    """
    NO_ATTR = 0
    AUTHORITY = 1
    ATTRIBUTION = 2
    SECURITY_REALM = 3
    APPROVER = 4
    JUSTIFICATION_TYPE = 5
    CREDENTIALS_TYPE = 6
    CREDS_ASSERTION = 7

  class OpValueValuesEnum(_messages.Enum):
    r"""An operator to apply the subject with.

    Values:
      NO_OP: Default no-op.
      EQUALS: DEPRECATED. Use IN instead.
      NOT_EQUALS: DEPRECATED. Use NOT_IN instead.
      IN: The condition is true if the subject (or any element of it if it is
        a set) matches any of the supplied values.
      NOT_IN: The condition is true if the subject (or every element of it if
        it is a set) matches none of the supplied values.
      DISCHARGED: Subject is discharged
    """
    NO_OP = 0
    EQUALS = 1
    NOT_EQUALS = 2
    IN = 3
    NOT_IN = 4
    DISCHARGED = 5

  class SysValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by any service that owns resources and
    uses the IAM system for access control.

    Values:
      NO_ATTR: Default non-attribute type
      REGION: Region of the resource
      SERVICE: Service name
      NAME: Resource name
      IP: IP address of the caller
    """
    NO_ATTR = 0
    REGION = 1
    SERVICE = 2
    NAME = 3
    IP = 4

  iam = _messages.EnumField('IamValueValuesEnum', 1)
  op = _messages.EnumField('OpValueValuesEnum', 2)
  svc = _messages.StringField(3)
  sys = _messages.EnumField('SysValueValuesEnum', 4)
  values = _messages.StringField(5, repeated=True)


class GoogleIamV1CounterOptions(_messages.Message):
  r"""Increment a streamz counter with the specified metric and field names.
  Metric names should start with a '/', generally be lowercase-only, and end
  in "_count". Field names should not contain an initial slash. The actual
  exported metric names will have "/iam/policy" prepended. Field names
  correspond to IAM request parameters and field values are their respective
  values. Supported field names: - "authority", which is "[token]" if
  IAMContext.token is present, otherwise the value of
  IAMContext.authority_selector if present, and otherwise a representation of
  IAMContext.principal; or - "iam_principal", a representation of
  IAMContext.principal even if a token or authority selector is present; or -
  "" (empty string), resulting in a counter with no fields. Examples: counter
  { metric: "/debug_access_count" field: "iam_principal" } ==> increment
  counter /iam/policy/debug_access_count {iam_principal=[value of
  IAMContext.principal]}

  Fields:
    customFields: Custom fields.
    field: The field value to attribute.
    metric: The metric to update.
  """

  customFields = _messages.MessageField('GoogleIamV1CustomField', 1, repeated=True)
  field = _messages.StringField(2)
  metric = _messages.StringField(3)


class GoogleIamV1CustomField(_messages.Message):
  r"""Custom fields. These can be used to create a counter with arbitrary
  field/value pairs. See: go/rpcsp-custom-fields.

  Fields:
    name: Name is the field name.
    value: Value is the field value. It is important that in contrast to the
      CounterOptions.field, the value here is a constant that is not derived
      from the IAMContext.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleIamV1DataAccessOptions(_messages.Message):
  r"""Write a Data Access (Gin) log

  Enums:
    LogModeValueValuesEnum:

  Fields:
    logMode: A LogModeValueValuesEnum attribute.
  """

  class LogModeValueValuesEnum(_messages.Enum):
    r"""LogModeValueValuesEnum enum type.

    Values:
      LOG_MODE_UNSPECIFIED: Client is not required to write a partial Gin log
        immediately after the authorization check. If client chooses to write
        one and it fails, client may either fail open (allow the operation to
        continue) or fail closed (handle as a DENY outcome).
      LOG_FAIL_CLOSED: The application's operation in the context of which
        this authorization check is being made may only be performed if it is
        successfully logged to Gin. For instance, the authorization library
        may satisfy this obligation by emitting a partial log entry at
        authorization check time and only returning ALLOW to the application
        if it succeeds. If a matching Rule has this directive, but the client
        has not indicated that it will honor such requirements, then the IAM
        check will result in authorization failure by setting
        CheckPolicyResponse.success=false.
    """
    LOG_MODE_UNSPECIFIED = 0
    LOG_FAIL_CLOSED = 1

  logMode = _messages.EnumField('LogModeValueValuesEnum', 1)


class GoogleIamV1LogConfig(_messages.Message):
  r"""Specifies what kind of log the caller must write

  Fields:
    cloudAudit: Cloud audit options.
    counter: Counter options.
    dataAccess: Data access options.
  """

  cloudAudit = _messages.MessageField('GoogleIamV1CloudAuditOptions', 1)
  counter = _messages.MessageField('GoogleIamV1CounterOptions', 2)
  dataAccess = _messages.MessageField('GoogleIamV1DataAccessOptions', 3)


class GoogleIamV1Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } **YAML example:** bindings: - members: -
  user:<EMAIL> - group:<EMAIL> - domain:google.com -
  serviceAccount:<EMAIL> role:
  roles/resourcemanager.organizationAdmin - members: - user:<EMAIL>
  role: roles/resourcemanager.organizationViewer condition: title: expirable
  access description: Does not grant access after Sep 2020 expression:
  request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA=
  version: 3 For a description of IAM and its features, see the [IAM
  documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    rules: If more than one rule is specified, the rules are applied in the
      following manner: - All matching LOG rules are always applied. - If any
      DENY/DENY_WITH_LOG rule matches, permission is denied. Logging will be
      applied if one or more matching rule requires logging. - Otherwise, if
      any ALLOW/ALLOW_WITH_LOG rule matches, permission is granted. Logging
      will be applied if one or more matching rule requires logging. -
      Otherwise, if no rule applies, permission is denied.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('GoogleIamV1AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('GoogleIamV1Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  rules = _messages.MessageField('GoogleIamV1Rule', 4, repeated=True)
  version = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class GoogleIamV1Rule(_messages.Message):
  r"""A rule to be applied in a Policy.

  Enums:
    ActionValueValuesEnum: Required

  Fields:
    action: Required
    conditions: Additional restrictions that must be met. All conditions must
      pass for the rule to match.
    description: Human-readable description of the rule.
    in_: If one or more 'in' clauses are specified, the rule matches if the
      PRINCIPAL/AUTHORITY_SELECTOR is in at least one of these entries.
    logConfig: The config returned to callers of CheckPolicy for any entries
      that match the LOG action.
    notIn: If one or more 'not_in' clauses are specified, the rule matches if
      the PRINCIPAL/AUTHORITY_SELECTOR is in none of the entries. The format
      for in and not_in entries can be found at in the Local IAM documentation
      (see go/local-iam#features).
    permissions: A permission is a string of form '..' (e.g.,
      'storage.buckets.list'). A value of '*' matches all permissions, and a
      verb part of '*' (e.g., 'storage.buckets.*') matches all verbs.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required

    Values:
      NO_ACTION: Default no action.
      ALLOW: Matching 'Entries' grant access.
      ALLOW_WITH_LOG: Matching 'Entries' grant access and the caller promises
        to log the request per the returned log_configs.
      DENY: Matching 'Entries' deny access.
      DENY_WITH_LOG: Matching 'Entries' deny access and the caller promises to
        log the request per the returned log_configs.
      LOG: Matching 'Entries' tell IAM.Check callers to generate logs.
    """
    NO_ACTION = 0
    ALLOW = 1
    ALLOW_WITH_LOG = 2
    DENY = 3
    DENY_WITH_LOG = 4
    LOG = 5

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  conditions = _messages.MessageField('GoogleIamV1Condition', 2, repeated=True)
  description = _messages.StringField(3)
  in_ = _messages.StringField(4, repeated=True)
  logConfig = _messages.MessageField('GoogleIamV1LogConfig', 5, repeated=True)
  notIn = _messages.StringField(6, repeated=True)
  permissions = _messages.StringField(7, repeated=True)


class GoogleIamV1SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('GoogleIamV1Policy', 1)
  updateMask = _messages.StringField(2)


class GoogleIamV1TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class GoogleIamV1TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class HttpHeaderMatch(_messages.Message):
  r"""Specification of HTTP header match attributes.

  Fields:
    headerName: Required. The name of the HTTP header to match. For matching
      against the HTTP request's authority, use a headerMatch with the header
      name ":authority". For matching a request's method, use the headerName
      ":method".
    regexMatch: Required. The value of the header must match the regular
      expression specified in regexMatch. For regular expression grammar,
      please see: en.cppreference.com/w/cpp/regex/ecmascript For matching
      against a port specified in the HTTP request, use a headerMatch with
      headerName set to Host and a regular expression that satisfies the
      RFC2616 Host header's port specifier.
  """

  headerName = _messages.StringField(1)
  regexMatch = _messages.StringField(2)


class ListAddressGroupReferencesResponse(_messages.Message):
  r"""Response of the ListAddressGroupReferences method.

  Fields:
    addressGroupReferences: A list of references that matches the specified
      filter in the request.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
  """

  addressGroupReferences = _messages.MessageField('ListAddressGroupReferencesResponseAddressGroupReference', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListAddressGroupReferencesResponseAddressGroupReference(_messages.Message):
  r"""The Reference of AddressGroup.

  Fields:
    firewallPolicy: FirewallPolicy that is using the Address Group.
    rulePriority: Rule priority of the FirewallPolicy that is using the
      Address Group.
  """

  firewallPolicy = _messages.StringField(1)
  rulePriority = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class ListAddressGroupsResponse(_messages.Message):
  r"""Response returned by the ListAddressGroups method.

  Fields:
    addressGroups: List of AddressGroups resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
  """

  addressGroups = _messages.MessageField('AddressGroup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListAuthorizationPoliciesResponse(_messages.Message):
  r"""Response returned by the ListAuthorizationPolicies method.

  Fields:
    authorizationPolicies: List of AuthorizationPolicies resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
  """

  authorizationPolicies = _messages.MessageField('AuthorizationPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListClientTlsPoliciesResponse(_messages.Message):
  r"""Response returned by the ListClientTlsPolicies method.

  Fields:
    clientTlsPolicies: List of ClientTlsPolicy resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
  """

  clientTlsPolicies = _messages.MessageField('ClientTlsPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListFirewallEndpointAssociationsResponse(_messages.Message):
  r"""Message for response to listing Associations

  Fields:
    firewallEndpointAssociations: The list of Association
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  firewallEndpointAssociations = _messages.MessageField('FirewallEndpointAssociation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListFirewallEndpointsResponse(_messages.Message):
  r"""Message for response to listing Endpoints

  Fields:
    firewallEndpoints: The list of Endpoint
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  firewallEndpoints = _messages.MessageField('FirewallEndpoint', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListGatewaySecurityPoliciesResponse(_messages.Message):
  r"""Response returned by the ListGatewaySecurityPolicies method.

  Fields:
    gatewaySecurityPolicies: List of GatewaySecurityPolicies resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then 'next_page_token' is included. To get the next set of
      results, call this method again using the value of 'next_page_token' as
      'page_token'.
    unreachable: Locations that could not be reached.
  """

  gatewaySecurityPolicies = _messages.MessageField('GatewaySecurityPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListGatewaySecurityPolicyRulesResponse(_messages.Message):
  r"""Response returned by the ListGatewaySecurityPolicyRules method.

  Fields:
    gatewaySecurityPolicyRules: List of GatewaySecurityPolicyRule resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then 'next_page_token' is included. To get the next set of
      results, call this method again using the value of 'next_page_token' as
      'page_token'.
    unreachable: Locations that could not be reached.
  """

  gatewaySecurityPolicyRules = _messages.MessageField('GatewaySecurityPolicyRule', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListReferencesRequest(_messages.Message):
  r"""The ListResourceMetadataRequest request.

  Fields:
    pageSize: The maximum number of items to return. If unspecified, server
      will pick an appropriate default. Server may return fewer items than
      requested. A caller should only rely on response's next_page_token to
      determine if there are more References left to be queried.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name (target_resource of this
      reference). For example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)


class ListReferencesResponse(_messages.Message):
  r"""The ListReferencesResponse response.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    references: The list of references.
  """

  nextPageToken = _messages.StringField(1)
  references = _messages.MessageField('Reference', 2, repeated=True)


class ListSecurityProfileGroupsResponse(_messages.Message):
  r"""Response returned by the ListSecurityProfileGroups method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    securityProfileGroups: List of SecurityProfileGroups resources.
  """

  nextPageToken = _messages.StringField(1)
  securityProfileGroups = _messages.MessageField('SecurityProfileGroup', 2, repeated=True)


class ListSecurityProfilesResponse(_messages.Message):
  r"""Response returned by the ListSecurityProfiles method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    securityProfiles: List of SecurityProfile resources.
  """

  nextPageToken = _messages.StringField(1)
  securityProfiles = _messages.MessageField('SecurityProfile', 2, repeated=True)


class ListServerTlsPoliciesResponse(_messages.Message):
  r"""Response returned by the ListServerTlsPolicies method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    serverTlsPolicies: List of ServerTlsPolicy resources.
  """

  nextPageToken = _messages.StringField(1)
  serverTlsPolicies = _messages.MessageField('ServerTlsPolicy', 2, repeated=True)


class ListTlsInspectionPoliciesResponse(_messages.Message):
  r"""Response returned by the ListTlsInspectionPolicies method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then 'next_page_token' is included. To get the next set of
      results, call this method again using the value of 'next_page_token' as
      'page_token'.
    tlsInspectionPolicies: List of TlsInspectionPolicies resources.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  tlsInspectionPolicies = _messages.MessageField('TlsInspectionPolicy', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListUrlListsResponse(_messages.Message):
  r"""Response returned by the ListUrlLists method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    unreachable: Locations that could not be reached.
    urlLists: List of UrlList resources.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  urlLists = _messages.MessageField('UrlList', 3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MTLSPolicy(_messages.Message):
  r"""Specification of the MTLSPolicy.

  Enums:
    ClientValidationModeValueValuesEnum: When the client presents an invalid
      certificate or no certificate to the load balancer, the
      `client_validation_mode` specifies how the client connection is handled.
      Required if the policy is to be used with the external HTTPS load
      balancing. For Traffic Director it must be empty.
    TierValueValuesEnum: Mutual TLS tier. Allowed only if the policy is to be
      used with external HTTPS load balancers.

  Fields:
    clientValidationCa: Required if the policy is to be used with Traffic
      Director. For external HTTPS load balancers it must be empty. Defines
      the mechanism to obtain the Certificate Authority certificate to
      validate the client certificate.
    clientValidationMode: When the client presents an invalid certificate or
      no certificate to the load balancer, the `client_validation_mode`
      specifies how the client connection is handled. Required if the policy
      is to be used with the external HTTPS load balancing. For Traffic
      Director it must be empty.
    clientValidationTrustConfig: Reference to the TrustConfig from
      certificatemanager.googleapis.com namespace. If specified, the chain
      validation will be performed against certificates configured in the
      given TrustConfig. Allowed only if the policy is to be used with
      external HTTPS load balancers.
    tier: Mutual TLS tier. Allowed only if the policy is to be used with
      external HTTPS load balancers.
  """

  class ClientValidationModeValueValuesEnum(_messages.Enum):
    r"""When the client presents an invalid certificate or no certificate to
    the load balancer, the `client_validation_mode` specifies how the client
    connection is handled. Required if the policy is to be used with the
    external HTTPS load balancing. For Traffic Director it must be empty.

    Values:
      CLIENT_VALIDATION_MODE_UNSPECIFIED: Not allowed.
      ALLOW_INVALID_OR_MISSING_CLIENT_CERT: Allow connection even if
        certificate chain validation of the client certificate failed or no
        client certificate was presented. The proof of possession of the
        private key is always checked if client certificate was presented.
        This mode requires the backend to implement processing of data
        extracted from a client certificate to authenticate the peer, or to
        reject connections if the client certificate fingerprint is missing.
      REJECT_INVALID: Require a client certificate and allow connection to the
        backend only if validation of the client certificate passed. If set,
        requires a reference to non-empty TrustConfig specified in
        `client_validation_trust_config`.
    """
    CLIENT_VALIDATION_MODE_UNSPECIFIED = 0
    ALLOW_INVALID_OR_MISSING_CLIENT_CERT = 1
    REJECT_INVALID = 2

  class TierValueValuesEnum(_messages.Enum):
    r"""Mutual TLS tier. Allowed only if the policy is to be used with
    external HTTPS load balancers.

    Values:
      TIER_UNSPECIFIED: If tier is unspecified in the request, the system will
        choose a default value - `STANDARD` tier at present.
      STANDARD: Default Tier. Primarily for Software Providers (service to
        service/API communication).
      ADVANCED: Advanced Tier. For customers in strongly regulated
        environments, specifying longer keys, complex certificate chains.
    """
    TIER_UNSPECIFIED = 0
    STANDARD = 1
    ADVANCED = 2

  clientValidationCa = _messages.MessageField('ValidationCA', 1, repeated=True)
  clientValidationMode = _messages.EnumField('ClientValidationModeValueValuesEnum', 2)
  clientValidationTrustConfig = _messages.StringField(3)
  tier = _messages.EnumField('TierValueValuesEnum', 4)


class NetworksecurityOrganizationsLocationsAddressGroupsAddItemsRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsAddressGroupsAddItemsRequest
  object.

  Fields:
    addAddressGroupItemsRequest: A AddAddressGroupItemsRequest resource to be
      passed as the request body.
    addressGroup: Required. A name of the AddressGroup to add items to. Must
      be in the format
      `projects|organization/*/locations/{location}/addressGroups/*`.
  """

  addAddressGroupItemsRequest = _messages.MessageField('AddAddressGroupItemsRequest', 1)
  addressGroup = _messages.StringField(2, required=True)


class NetworksecurityOrganizationsLocationsAddressGroupsCloneItemsRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsAddressGroupsCloneItemsRequest
  object.

  Fields:
    addressGroup: Required. A name of the AddressGroup to clone items to. Must
      be in the format
      `projects|organization/*/locations/{location}/addressGroups/*`.
    cloneAddressGroupItemsRequest: A CloneAddressGroupItemsRequest resource to
      be passed as the request body.
  """

  addressGroup = _messages.StringField(1, required=True)
  cloneAddressGroupItemsRequest = _messages.MessageField('CloneAddressGroupItemsRequest', 2)


class NetworksecurityOrganizationsLocationsAddressGroupsCreateRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsAddressGroupsCreateRequest
  object.

  Fields:
    addressGroup: A AddressGroup resource to be passed as the request body.
    addressGroupId: Required. Short name of the AddressGroup resource to be
      created. This value should be 1-63 characters long, containing only
      letters, numbers, hyphens, and underscores, and should not start with a
      number. E.g. "authz_policy".
    parent: Required. The parent resource of the AddressGroup. Must be in the
      format `projects/*/locations/{location}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  addressGroup = _messages.MessageField('AddressGroup', 1)
  addressGroupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityOrganizationsLocationsAddressGroupsDeleteRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsAddressGroupsDeleteRequest
  object.

  Fields:
    name: Required. A name of the AddressGroup to delete. Must be in the
      format `projects/*/locations/{location}/addressGroups/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityOrganizationsLocationsAddressGroupsGetRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsAddressGroupsGetRequest object.

  Fields:
    name: Required. A name of the AddressGroup to get. Must be in the format
      `projects/*/locations/{location}/addressGroups/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityOrganizationsLocationsAddressGroupsListReferencesRequest(_messages.Message):
  r"""A
  NetworksecurityOrganizationsLocationsAddressGroupsListReferencesRequest
  object.

  Fields:
    addressGroup: Required. A name of the AddressGroup to clone items to. Must
      be in the format
      `projects|organization/*/locations/{location}/addressGroups/*`.
    pageSize: The maximum number of references to return. If unspecified,
      server will pick an appropriate default. Server may return fewer items
      than requested. A caller should only rely on response's next_page_token
      to determine if there are more AddressGroupUsers left to be queried.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
  """

  addressGroup = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class NetworksecurityOrganizationsLocationsAddressGroupsListRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsAddressGroupsListRequest object.

  Fields:
    pageSize: Maximum number of AddressGroups to return per call.
    pageToken: The value returned by the last `ListAddressGroupsResponse`
      Indicates that this is a continuation of a prior `ListAddressGroups`
      call, and that the system should return the next page of data.
    parent: Required. The project and location from which the AddressGroups
      should be listed, specified in the format
      `projects/*/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityOrganizationsLocationsAddressGroupsPatchRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsAddressGroupsPatchRequest object.

  Fields:
    addressGroup: A AddressGroup resource to be passed as the request body.
    name: Required. Name of the AddressGroup resource. It matches pattern
      `projects/*/locations/{location}/addressGroups/`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the AddressGroup resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  addressGroup = _messages.MessageField('AddressGroup', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityOrganizationsLocationsAddressGroupsRemoveItemsRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsAddressGroupsRemoveItemsRequest
  object.

  Fields:
    addressGroup: Required. A name of the AddressGroup to remove items from.
      Must be in the format
      `projects|organization/*/locations/{location}/addressGroups/*`.
    removeAddressGroupItemsRequest: A RemoveAddressGroupItemsRequest resource
      to be passed as the request body.
  """

  addressGroup = _messages.StringField(1, required=True)
  removeAddressGroupItemsRequest = _messages.MessageField('RemoveAddressGroupItemsRequest', 2)


class NetworksecurityOrganizationsLocationsFirewallEndpointsCreateRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsFirewallEndpointsCreateRequest
  object.

  Fields:
    firewallEndpoint: A FirewallEndpoint resource to be passed as the request
      body.
    firewallEndpointId: Required. Id of the requesting object. If auto-
      generating Id server-side, remove this field and firewall_endpoint_id
      from the method_signature of Create RPC.
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  firewallEndpoint = _messages.MessageField('FirewallEndpoint', 1)
  firewallEndpointId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityOrganizationsLocationsFirewallEndpointsDeleteRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsFirewallEndpointsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityOrganizationsLocationsFirewallEndpointsGetRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsFirewallEndpointsGetRequest
  object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityOrganizationsLocationsFirewallEndpointsListRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsFirewallEndpointsListRequest
  object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListEndpointsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityOrganizationsLocationsFirewallEndpointsPatchRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsFirewallEndpointsPatchRequest
  object.

  Fields:
    firewallEndpoint: A FirewallEndpoint resource to be passed as the request
      body.
    name: Output only. name of resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Endpoint resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  firewallEndpoint = _messages.MessageField('FirewallEndpoint', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityOrganizationsLocationsOperationsCancelRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class NetworksecurityOrganizationsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityOrganizationsLocationsOperationsGetRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityOrganizationsLocationsOperationsListRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class NetworksecurityOrganizationsLocationsSecurityProfileGroupsCreateRequest(_messages.Message):
  r"""A
  NetworksecurityOrganizationsLocationsSecurityProfileGroupsCreateRequest
  object.

  Fields:
    parent: Required. The parent resource of the SecurityProfileGroup. Must be
      in the format `projects|organizations/*/locations/{location}`.
    securityProfileGroup: A SecurityProfileGroup resource to be passed as the
      request body.
    securityProfileGroupId: Required. Short name of the SecurityProfileGroup
      resource to be created. This value should be 1-63 characters long,
      containing only letters, numbers, hyphens, and underscores, and should
      not start with a number. E.g. "security_profile_group1".
  """

  parent = _messages.StringField(1, required=True)
  securityProfileGroup = _messages.MessageField('SecurityProfileGroup', 2)
  securityProfileGroupId = _messages.StringField(3)


class NetworksecurityOrganizationsLocationsSecurityProfileGroupsDeleteRequest(_messages.Message):
  r"""A
  NetworksecurityOrganizationsLocationsSecurityProfileGroupsDeleteRequest
  object.

  Fields:
    etag: Optional. If client provided etag is out of date, delete will return
      FAILED_PRECONDITION error.
    name: Required. A name of the SecurityProfileGroup to delete. Must be in
      the format `projects|organizations/*/locations/{location}/securityProfil
      eGroups/{security_profile_group}`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class NetworksecurityOrganizationsLocationsSecurityProfileGroupsGetRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsSecurityProfileGroupsGetRequest
  object.

  Fields:
    name: Required. A name of the SecurityProfileGroup to get. Must be in the
      format `projects|organizations/*/locations/{location}/securityProfileGro
      ups/{security_profile_group}`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityOrganizationsLocationsSecurityProfileGroupsListRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsSecurityProfileGroupsListRequest
  object.

  Fields:
    pageSize: Maximum number of SecurityProfileGroups to return per call.
    pageToken: The value returned by the last
      `ListSecurityProfileGroupsResponse` Indicates that this is a
      continuation of a prior `ListSecurityProfileGroups` call, and that the
      system should return the next page of data.
    parent: Required. The project or organization and location from which the
      SecurityProfileGroups should be listed, specified in the format
      `projects|organizations/*/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityOrganizationsLocationsSecurityProfileGroupsPatchRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsSecurityProfileGroupsPatchRequest
  object.

  Fields:
    name: Immutable. Name of the SecurityProfileGroup resource. It matches
      pattern `projects|organizations/*/locations/{location}/securityProfileGr
      oups/{security_profile_group}`.
    securityProfileGroup: A SecurityProfileGroup resource to be passed as the
      request body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the SecurityProfileGroup resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask.
  """

  name = _messages.StringField(1, required=True)
  securityProfileGroup = _messages.MessageField('SecurityProfileGroup', 2)
  updateMask = _messages.StringField(3)


class NetworksecurityOrganizationsLocationsSecurityProfilesCreateRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsSecurityProfilesCreateRequest
  object.

  Fields:
    parent: Required. The parent resource of the SecurityProfile. Must be in
      the format `projects|organizations/*/locations/{location}`.
    securityProfile: A SecurityProfile resource to be passed as the request
      body.
    securityProfileId: Required. Short name of the SecurityProfile resource to
      be created. This value should be 1-63 characters long, containing only
      letters, numbers, hyphens, and underscores, and should not start with a
      number. E.g. "security_profile1".
  """

  parent = _messages.StringField(1, required=True)
  securityProfile = _messages.MessageField('SecurityProfile', 2)
  securityProfileId = _messages.StringField(3)


class NetworksecurityOrganizationsLocationsSecurityProfilesDeleteRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsSecurityProfilesDeleteRequest
  object.

  Fields:
    etag: Optional. If client provided etag is out of date, delete will return
      FAILED_PRECONDITION error.
    name: Required. A name of the SecurityProfile to delete. Must be in the
      format `projects|organizations/*/locations/{location}/securityProfiles/{
      security_profile_id}`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class NetworksecurityOrganizationsLocationsSecurityProfilesGetRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsSecurityProfilesGetRequest
  object.

  Fields:
    name: Required. A name of the SecurityProfile to get. Must be in the
      format `projects|organizations/*/locations/{location}/securityProfiles/{
      security_profile_id}`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityOrganizationsLocationsSecurityProfilesListRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsSecurityProfilesListRequest
  object.

  Fields:
    pageSize: Maximum number of SecurityProfiles to return per call.
    pageToken: The value returned by the last `ListSecurityProfilesResponse`
      Indicates that this is a continuation of a prior `ListSecurityProfiles`
      call, and that the system should return the next page of data.
    parent: Required. The project or organization and location from which the
      SecurityProfiles should be listed, specified in the format
      `projects|organizations/*/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityOrganizationsLocationsSecurityProfilesPatchRequest(_messages.Message):
  r"""A NetworksecurityOrganizationsLocationsSecurityProfilesPatchRequest
  object.

  Fields:
    name: Immutable. Name of the SecurityProfile resource. It matches pattern
      `projects|organizations/*/locations/{location}/securityProfiles/{securit
      y_profile}`.
    securityProfile: A SecurityProfile resource to be passed as the request
      body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the SecurityProfile resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask.
  """

  name = _messages.StringField(1, required=True)
  securityProfile = _messages.MessageField('SecurityProfile', 2)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsAddressGroupsAddItemsRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsAddItemsRequest object.

  Fields:
    addAddressGroupItemsRequest: A AddAddressGroupItemsRequest resource to be
      passed as the request body.
    addressGroup: Required. A name of the AddressGroup to add items to. Must
      be in the format
      `projects|organization/*/locations/{location}/addressGroups/*`.
  """

  addAddressGroupItemsRequest = _messages.MessageField('AddAddressGroupItemsRequest', 1)
  addressGroup = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsAddressGroupsCloneItemsRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsCloneItemsRequest object.

  Fields:
    addressGroup: Required. A name of the AddressGroup to clone items to. Must
      be in the format
      `projects|organization/*/locations/{location}/addressGroups/*`.
    cloneAddressGroupItemsRequest: A CloneAddressGroupItemsRequest resource to
      be passed as the request body.
  """

  addressGroup = _messages.StringField(1, required=True)
  cloneAddressGroupItemsRequest = _messages.MessageField('CloneAddressGroupItemsRequest', 2)


class NetworksecurityProjectsLocationsAddressGroupsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsCreateRequest object.

  Fields:
    addressGroup: A AddressGroup resource to be passed as the request body.
    addressGroupId: Required. Short name of the AddressGroup resource to be
      created. This value should be 1-63 characters long, containing only
      letters, numbers, hyphens, and underscores, and should not start with a
      number. E.g. "authz_policy".
    parent: Required. The parent resource of the AddressGroup. Must be in the
      format `projects/*/locations/{location}`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  addressGroup = _messages.MessageField('AddressGroup', 1)
  addressGroupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsAddressGroupsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsDeleteRequest object.

  Fields:
    name: Required. A name of the AddressGroup to delete. Must be in the
      format `projects/*/locations/{location}/addressGroups/*`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsAddressGroupsGetIamPolicyRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsAddressGroupsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsGetRequest object.

  Fields:
    name: Required. A name of the AddressGroup to get. Must be in the format
      `projects/*/locations/{location}/addressGroups/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsAddressGroupsListReferencesRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsListReferencesRequest
  object.

  Fields:
    addressGroup: Required. A name of the AddressGroup to clone items to. Must
      be in the format
      `projects|organization/*/locations/{location}/addressGroups/*`.
    pageSize: The maximum number of references to return. If unspecified,
      server will pick an appropriate default. Server may return fewer items
      than requested. A caller should only rely on response's next_page_token
      to determine if there are more AddressGroupUsers left to be queried.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
  """

  addressGroup = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class NetworksecurityProjectsLocationsAddressGroupsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsListRequest object.

  Fields:
    pageSize: Maximum number of AddressGroups to return per call.
    pageToken: The value returned by the last `ListAddressGroupsResponse`
      Indicates that this is a continuation of a prior `ListAddressGroups`
      call, and that the system should return the next page of data.
    parent: Required. The project and location from which the AddressGroups
      should be listed, specified in the format
      `projects/*/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsAddressGroupsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsPatchRequest object.

  Fields:
    addressGroup: A AddressGroup resource to be passed as the request body.
    name: Required. Name of the AddressGroup resource. It matches pattern
      `projects/*/locations/{location}/addressGroups/`.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the AddressGroup resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  addressGroup = _messages.MessageField('AddressGroup', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityProjectsLocationsAddressGroupsRemoveItemsRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsRemoveItemsRequest
  object.

  Fields:
    addressGroup: Required. A name of the AddressGroup to remove items from.
      Must be in the format
      `projects|organization/*/locations/{location}/addressGroups/*`.
    removeAddressGroupItemsRequest: A RemoveAddressGroupItemsRequest resource
      to be passed as the request body.
  """

  addressGroup = _messages.StringField(1, required=True)
  removeAddressGroupItemsRequest = _messages.MessageField('RemoveAddressGroupItemsRequest', 2)


class NetworksecurityProjectsLocationsAddressGroupsSetIamPolicyRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsSetIamPolicyRequest
  object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsAddressGroupsTestIamPermissionsRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAddressGroupsTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsAuthorizationPoliciesCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthorizationPoliciesCreateRequest
  object.

  Fields:
    authorizationPolicy: A AuthorizationPolicy resource to be passed as the
      request body.
    authorizationPolicyId: Required. Short name of the AuthorizationPolicy
      resource to be created. This value should be 1-63 characters long,
      containing only letters, numbers, hyphens, and underscores, and should
      not start with a number. E.g. "authz_policy".
    parent: Required. The parent resource of the AuthorizationPolicy. Must be
      in the format `projects/{project}/locations/{location}`.
  """

  authorizationPolicy = _messages.MessageField('AuthorizationPolicy', 1)
  authorizationPolicyId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsAuthorizationPoliciesDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthorizationPoliciesDeleteRequest
  object.

  Fields:
    name: Required. A name of the AuthorizationPolicy to delete. Must be in
      the format
      `projects/{project}/locations/{location}/authorizationPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsAuthorizationPoliciesGetIamPolicyRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsAuthorizationPoliciesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsAuthorizationPoliciesGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthorizationPoliciesGetRequest
  object.

  Fields:
    name: Required. A name of the AuthorizationPolicy to get. Must be in the
      format
      `projects/{project}/locations/{location}/authorizationPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsAuthorizationPoliciesListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthorizationPoliciesListRequest
  object.

  Fields:
    pageSize: Maximum number of AuthorizationPolicies to return per call.
    pageToken: The value returned by the last
      `ListAuthorizationPoliciesResponse` Indicates that this is a
      continuation of a prior `ListAuthorizationPolicies` call, and that the
      system should return the next page of data.
    parent: Required. The project and location from which the
      AuthorizationPolicies should be listed, specified in the format
      `projects/{project}/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsAuthorizationPoliciesPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthorizationPoliciesPatchRequest
  object.

  Fields:
    authorizationPolicy: A AuthorizationPolicy resource to be passed as the
      request body.
    name: Required. Name of the AuthorizationPolicy resource. It matches
      pattern
      `projects/{project}/locations/{location}/authorizationPolicies/`.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the AuthorizationPolicy resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
  """

  authorizationPolicy = _messages.MessageField('AuthorizationPolicy', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsAuthorizationPoliciesSetIamPolicyRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsAuthorizationPoliciesSetIamPolicyRequest
  object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsAuthorizationPoliciesTestIamPermissionsRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsAuthorizationPoliciesTestIamPermission
  sRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsClientTlsPoliciesCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsClientTlsPoliciesCreateRequest object.

  Fields:
    clientTlsPolicy: A ClientTlsPolicy resource to be passed as the request
      body.
    clientTlsPolicyId: Required. Short name of the ClientTlsPolicy resource to
      be created. This value should be 1-63 characters long, containing only
      letters, numbers, hyphens, and underscores, and should not start with a
      number. E.g. "client_mtls_policy".
    parent: Required. The parent resource of the ClientTlsPolicy. Must be in
      the format `projects/*/locations/{location}`.
  """

  clientTlsPolicy = _messages.MessageField('ClientTlsPolicy', 1)
  clientTlsPolicyId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsClientTlsPoliciesDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsClientTlsPoliciesDeleteRequest object.

  Fields:
    name: Required. A name of the ClientTlsPolicy to delete. Must be in the
      format `projects/*/locations/{location}/clientTlsPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsClientTlsPoliciesGetIamPolicyRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsClientTlsPoliciesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsClientTlsPoliciesGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsClientTlsPoliciesGetRequest object.

  Fields:
    name: Required. A name of the ClientTlsPolicy to get. Must be in the
      format `projects/*/locations/{location}/clientTlsPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsClientTlsPoliciesListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsClientTlsPoliciesListRequest object.

  Fields:
    pageSize: Maximum number of ClientTlsPolicies to return per call.
    pageToken: The value returned by the last `ListClientTlsPoliciesResponse`
      Indicates that this is a continuation of a prior `ListClientTlsPolicies`
      call, and that the system should return the next page of data.
    parent: Required. The project and location from which the
      ClientTlsPolicies should be listed, specified in the format
      `projects/*/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsClientTlsPoliciesPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsClientTlsPoliciesPatchRequest object.

  Fields:
    clientTlsPolicy: A ClientTlsPolicy resource to be passed as the request
      body.
    name: Required. Name of the ClientTlsPolicy resource. It matches the
      pattern
      `projects/*/locations/{location}/clientTlsPolicies/{client_tls_policy}`
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the ClientTlsPolicy resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  clientTlsPolicy = _messages.MessageField('ClientTlsPolicy', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsClientTlsPoliciesSetIamPolicyRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsClientTlsPoliciesSetIamPolicyRequest
  object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsClientTlsPoliciesTestIamPermissionsRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsClientTlsPoliciesTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsFirewallEndpointAssociationsCreateRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsFirewallEndpointAssociationsCreateRequest
  object.

  Fields:
    firewallEndpointAssociation: A FirewallEndpointAssociation resource to be
      passed as the request body.
    firewallEndpointAssociationId: Required. Id of the requesting object. If
      auto-generating Id server-side, remove this field and
      firewall_endpoint_association_id from the method_signature of Create
      RPC.
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  firewallEndpointAssociation = _messages.MessageField('FirewallEndpointAssociation', 1)
  firewallEndpointAssociationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworksecurityProjectsLocationsFirewallEndpointAssociationsDeleteRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsFirewallEndpointAssociationsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworksecurityProjectsLocationsFirewallEndpointAssociationsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsFirewallEndpointAssociationsGetRequest
  object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsFirewallEndpointAssociationsListRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsFirewallEndpointAssociationsListRequest
  object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListAssociationsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworksecurityProjectsLocationsFirewallEndpointAssociationsPatchRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsFirewallEndpointAssociationsPatchRequest
  object.

  Fields:
    firewallEndpointAssociation: A FirewallEndpointAssociation resource to be
      passed as the request body.
    name: Output only. name of resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Association resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  firewallEndpointAssociation = _messages.MessageField('FirewallEndpointAssociation', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewaySecurityPoliciesCreateRequest
  object.

  Fields:
    gatewaySecurityPolicy: A GatewaySecurityPolicy resource to be passed as
      the request body.
    gatewaySecurityPolicyId: Required. Short name of the GatewaySecurityPolicy
      resource to be created. This value should be 1-63 characters long,
      containing only letters, numbers, hyphens, and underscores, and should
      not start with a number. E.g. "gateway_security_policy1".
    parent: Required. The parent resource of the GatewaySecurityPolicy. Must
      be in the format `projects/{project}/locations/{location}`.
  """

  gatewaySecurityPolicy = _messages.MessageField('GatewaySecurityPolicy', 1)
  gatewaySecurityPolicyId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewaySecurityPoliciesDeleteRequest
  object.

  Fields:
    name: Required. A name of the GatewaySecurityPolicy to delete. Must be in
      the format
      `projects/{project}/locations/{location}/gatewaySecurityPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewaySecurityPoliciesGetRequest
  object.

  Fields:
    name: Required. A name of the GatewaySecurityPolicy to get. Must be in the
      format
      `projects/{project}/locations/{location}/gatewaySecurityPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewaySecurityPoliciesListRequest
  object.

  Fields:
    pageSize: Maximum number of GatewaySecurityPolicies to return per call.
    pageToken: The value returned by the last
      'ListGatewaySecurityPoliciesResponse' Indicates that this is a
      continuation of a prior 'ListGatewaySecurityPolicies' call, and that the
      system should return the next page of data.
    parent: Required. The project and location from which the
      GatewaySecurityPolicies should be listed, specified in the format
      `projects/{project}/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewaySecurityPoliciesPatchRequest
  object.

  Fields:
    gatewaySecurityPolicy: A GatewaySecurityPolicy resource to be passed as
      the request body.
    name: Required. Name of the resource. Name is of the form projects/{projec
      t}/locations/{location}/gatewaySecurityPolicies/{gateway_security_policy
      } gateway_security_policy should match the
      pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the GatewaySecurityPolicy resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
  """

  gatewaySecurityPolicy = _messages.MessageField('GatewaySecurityPolicy', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesCreateRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesCreateRequest
  object.

  Fields:
    gatewaySecurityPolicyRule: A GatewaySecurityPolicyRule resource to be
      passed as the request body.
    gatewaySecurityPolicyRuleId: The ID to use for the rule, which will become
      the final component of the rule's resource name. This value should be
      4-63 characters, and valid characters are /a-z-/.
    parent: Required. The parent where this rule will be created. Format :
      projects/{project}/location/{location}/gatewaySecurityPolicies/*
  """

  gatewaySecurityPolicyRule = _messages.MessageField('GatewaySecurityPolicyRule', 1)
  gatewaySecurityPolicyRuleId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesDeleteRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesDeleteRequest
  object.

  Fields:
    name: Required. A name of the GatewaySecurityPolicyRule to delete. Must be
      in the format `projects/{project}/locations/{location}/gatewaySecurityPo
      licies/{gatewaySecurityPolicy}/rules/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesGetRequest
  object.

  Fields:
    name: Required. The name of the GatewaySecurityPolicyRule to retrieve.
      Format:
      projects/{project}/location/{location}/gatewaySecurityPolicies/*/rules/*
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesListRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesListRequest
  object.

  Fields:
    pageSize: Maximum number of GatewaySecurityPolicyRules to return per call.
    pageToken: The value returned by the last
      'ListGatewaySecurityPolicyRulesResponse' Indicates that this is a
      continuation of a prior 'ListGatewaySecurityPolicyRules' call, and that
      the system should return the next page of data.
    parent: Required. The project, location and GatewaySecurityPolicy from
      which the GatewaySecurityPolicyRules should be listed, specified in the
      format `projects/{project}/locations/{location}/gatewaySecurityPolicies/
      {gatewaySecurityPolicy}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesPatchRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesPatchRequest
  object.

  Fields:
    gatewaySecurityPolicyRule: A GatewaySecurityPolicyRule resource to be
      passed as the request body.
    name: Required. Immutable. Name of the resource. ame is the full resource
      name so projects/{project}/locations/{location}/gatewaySecurityPolicies/
      {gateway_security_policy}/rules/{rule} rule should match the pattern:
      (^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the GatewaySecurityPolicy resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
  """

  gatewaySecurityPolicyRule = _messages.MessageField('GatewaySecurityPolicyRule', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    includeUnrevealedLocations: If true, the returned list will include
      locations which are not yet revealed.
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  includeUnrevealedLocations = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class NetworksecurityProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class NetworksecurityProjectsLocationsSecurityProfileGroupsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfileGroupsCreateRequest
  object.

  Fields:
    parent: Required. The parent resource of the SecurityProfileGroup. Must be
      in the format `projects|organizations/*/locations/{location}`.
    securityProfileGroup: A SecurityProfileGroup resource to be passed as the
      request body.
    securityProfileGroupId: Required. Short name of the SecurityProfileGroup
      resource to be created. This value should be 1-63 characters long,
      containing only letters, numbers, hyphens, and underscores, and should
      not start with a number. E.g. "security_profile_group1".
  """

  parent = _messages.StringField(1, required=True)
  securityProfileGroup = _messages.MessageField('SecurityProfileGroup', 2)
  securityProfileGroupId = _messages.StringField(3)


class NetworksecurityProjectsLocationsSecurityProfileGroupsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfileGroupsDeleteRequest
  object.

  Fields:
    etag: Optional. If client provided etag is out of date, delete will return
      FAILED_PRECONDITION error.
    name: Required. A name of the SecurityProfileGroup to delete. Must be in
      the format `projects|organizations/*/locations/{location}/securityProfil
      eGroups/{security_profile_group}`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsSecurityProfileGroupsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfileGroupsGetRequest
  object.

  Fields:
    name: Required. A name of the SecurityProfileGroup to get. Must be in the
      format `projects|organizations/*/locations/{location}/securityProfileGro
      ups/{security_profile_group}`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsSecurityProfileGroupsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfileGroupsListRequest
  object.

  Fields:
    pageSize: Maximum number of SecurityProfileGroups to return per call.
    pageToken: The value returned by the last
      `ListSecurityProfileGroupsResponse` Indicates that this is a
      continuation of a prior `ListSecurityProfileGroups` call, and that the
      system should return the next page of data.
    parent: Required. The project or organization and location from which the
      SecurityProfileGroups should be listed, specified in the format
      `projects|organizations/*/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsSecurityProfileGroupsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfileGroupsPatchRequest
  object.

  Fields:
    name: Immutable. Name of the SecurityProfileGroup resource. It matches
      pattern `projects|organizations/*/locations/{location}/securityProfileGr
      oups/{security_profile_group}`.
    securityProfileGroup: A SecurityProfileGroup resource to be passed as the
      request body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the SecurityProfileGroup resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask.
  """

  name = _messages.StringField(1, required=True)
  securityProfileGroup = _messages.MessageField('SecurityProfileGroup', 2)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsSecurityProfilesCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfilesCreateRequest object.

  Fields:
    parent: Required. The parent resource of the SecurityProfile. Must be in
      the format `projects|organizations/*/locations/{location}`.
    securityProfile: A SecurityProfile resource to be passed as the request
      body.
    securityProfileId: Required. Short name of the SecurityProfile resource to
      be created. This value should be 1-63 characters long, containing only
      letters, numbers, hyphens, and underscores, and should not start with a
      number. E.g. "security_profile1".
  """

  parent = _messages.StringField(1, required=True)
  securityProfile = _messages.MessageField('SecurityProfile', 2)
  securityProfileId = _messages.StringField(3)


class NetworksecurityProjectsLocationsSecurityProfilesDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfilesDeleteRequest object.

  Fields:
    etag: Optional. If client provided etag is out of date, delete will return
      FAILED_PRECONDITION error.
    name: Required. A name of the SecurityProfile to delete. Must be in the
      format `projects|organizations/*/locations/{location}/securityProfiles/{
      security_profile_id}`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsSecurityProfilesGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfilesGetRequest object.

  Fields:
    name: Required. A name of the SecurityProfile to get. Must be in the
      format `projects|organizations/*/locations/{location}/securityProfiles/{
      security_profile_id}`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsSecurityProfilesListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfilesListRequest object.

  Fields:
    pageSize: Maximum number of SecurityProfiles to return per call.
    pageToken: The value returned by the last `ListSecurityProfilesResponse`
      Indicates that this is a continuation of a prior `ListSecurityProfiles`
      call, and that the system should return the next page of data.
    parent: Required. The project or organization and location from which the
      SecurityProfiles should be listed, specified in the format
      `projects|organizations/*/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsSecurityProfilesPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsSecurityProfilesPatchRequest object.

  Fields:
    name: Immutable. Name of the SecurityProfile resource. It matches pattern
      `projects|organizations/*/locations/{location}/securityProfiles/{securit
      y_profile}`.
    securityProfile: A SecurityProfile resource to be passed as the request
      body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the SecurityProfile resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask.
  """

  name = _messages.StringField(1, required=True)
  securityProfile = _messages.MessageField('SecurityProfile', 2)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsServerTlsPoliciesCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsServerTlsPoliciesCreateRequest object.

  Fields:
    parent: Required. The parent resource of the ServerTlsPolicy. Must be in
      the format `projects/*/locations/{location}`.
    serverTlsPolicy: A ServerTlsPolicy resource to be passed as the request
      body.
    serverTlsPolicyId: Required. Short name of the ServerTlsPolicy resource to
      be created. This value should be 1-63 characters long, containing only
      letters, numbers, hyphens, and underscores, and should not start with a
      number. E.g. "server_mtls_policy".
  """

  parent = _messages.StringField(1, required=True)
  serverTlsPolicy = _messages.MessageField('ServerTlsPolicy', 2)
  serverTlsPolicyId = _messages.StringField(3)


class NetworksecurityProjectsLocationsServerTlsPoliciesDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsServerTlsPoliciesDeleteRequest object.

  Fields:
    name: Required. A name of the ServerTlsPolicy to delete. Must be in the
      format `projects/*/locations/{location}/serverTlsPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsServerTlsPoliciesGetIamPolicyRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsServerTlsPoliciesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsServerTlsPoliciesGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsServerTlsPoliciesGetRequest object.

  Fields:
    name: Required. A name of the ServerTlsPolicy to get. Must be in the
      format `projects/*/locations/{location}/serverTlsPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsServerTlsPoliciesListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsServerTlsPoliciesListRequest object.

  Fields:
    pageSize: Maximum number of ServerTlsPolicies to return per call.
    pageToken: The value returned by the last `ListServerTlsPoliciesResponse`
      Indicates that this is a continuation of a prior `ListServerTlsPolicies`
      call, and that the system should return the next page of data.
    parent: Required. The project and location from which the
      ServerTlsPolicies should be listed, specified in the format
      `projects/*/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsServerTlsPoliciesPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsServerTlsPoliciesPatchRequest object.

  Fields:
    name: Required. Name of the ServerTlsPolicy resource. It matches the
      pattern
      `projects/*/locations/{location}/serverTlsPolicies/{server_tls_policy}`
    serverTlsPolicy: A ServerTlsPolicy resource to be passed as the request
      body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the ServerTlsPolicy resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  serverTlsPolicy = _messages.MessageField('ServerTlsPolicy', 2)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsServerTlsPoliciesSetIamPolicyRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsServerTlsPoliciesSetIamPolicyRequest
  object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsServerTlsPoliciesTestIamPermissionsRequest(_messages.Message):
  r"""A
  NetworksecurityProjectsLocationsServerTlsPoliciesTestIamPermissionsRequest
  object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsTlsInspectionPoliciesCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsTlsInspectionPoliciesCreateRequest
  object.

  Fields:
    parent: Required. The parent resource of the TlsInspectionPolicy. Must be
      in the format `projects/{project}/locations/{location}`.
    tlsInspectionPolicy: A TlsInspectionPolicy resource to be passed as the
      request body.
    tlsInspectionPolicyId: Required. Short name of the TlsInspectionPolicy
      resource to be created. This value should be 1-63 characters long,
      containing only letters, numbers, hyphens, and underscores, and should
      not start with a number. E.g. "tls_inspection_policy1".
  """

  parent = _messages.StringField(1, required=True)
  tlsInspectionPolicy = _messages.MessageField('TlsInspectionPolicy', 2)
  tlsInspectionPolicyId = _messages.StringField(3)


class NetworksecurityProjectsLocationsTlsInspectionPoliciesDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsTlsInspectionPoliciesDeleteRequest
  object.

  Fields:
    force: If set to true, any rules for this TlsInspectionPolicy will also be
      deleted. (Otherwise, the request will only work if the
      TlsInspectionPolicy has no rules.)
    name: Required. A name of the TlsInspectionPolicy to delete. Must be in
      the format `projects/{project}/locations/{location}/tlsInspectionPolicie
      s/{tls_inspection_policy}`.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class NetworksecurityProjectsLocationsTlsInspectionPoliciesGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsTlsInspectionPoliciesGetRequest
  object.

  Fields:
    name: Required. A name of the TlsInspectionPolicy to get. Must be in the
      format `projects/{project}/locations/{location}/tlsInspectionPolicies/{t
      ls_inspection_policy}`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsTlsInspectionPoliciesListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsTlsInspectionPoliciesListRequest
  object.

  Fields:
    pageSize: Maximum number of TlsInspectionPolicies to return per call.
    pageToken: The value returned by the last
      'ListTlsInspectionPoliciesResponse' Indicates that this is a
      continuation of a prior 'ListTlsInspectionPolicies' call, and that the
      system should return the next page of data.
    parent: Required. The project and location from which the
      TlsInspectionPolicies should be listed, specified in the format
      `projects/{project}/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsTlsInspectionPoliciesPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsTlsInspectionPoliciesPatchRequest
  object.

  Fields:
    name: Required. Name of the resource. Name is of the form projects/{projec
      t}/locations/{location}/tlsInspectionPolicies/{tls_inspection_policy}
      tls_inspection_policy should match the
      pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
    tlsInspectionPolicy: A TlsInspectionPolicy resource to be passed as the
      request body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the TlsInspectionPolicy resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  tlsInspectionPolicy = _messages.MessageField('TlsInspectionPolicy', 2)
  updateMask = _messages.StringField(3)


class NetworksecurityProjectsLocationsUrlListsCreateRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUrlListsCreateRequest object.

  Fields:
    parent: Required. The parent resource of the UrlList. Must be in the
      format `projects/*/locations/{location}`.
    urlList: A UrlList resource to be passed as the request body.
    urlListId: Required. Short name of the UrlList resource to be created.
      This value should be 1-63 characters long, containing only letters,
      numbers, hyphens, and underscores, and should not start with a number.
      E.g. "url_list".
  """

  parent = _messages.StringField(1, required=True)
  urlList = _messages.MessageField('UrlList', 2)
  urlListId = _messages.StringField(3)


class NetworksecurityProjectsLocationsUrlListsDeleteRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUrlListsDeleteRequest object.

  Fields:
    name: Required. A name of the UrlList to delete. Must be in the format
      `projects/*/locations/{location}/urlLists/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsUrlListsGetRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUrlListsGetRequest object.

  Fields:
    name: Required. A name of the UrlList to get. Must be in the format
      `projects/*/locations/{location}/urlLists/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworksecurityProjectsLocationsUrlListsListRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUrlListsListRequest object.

  Fields:
    pageSize: Maximum number of UrlLists to return per call.
    pageToken: The value returned by the last `ListUrlListsResponse` Indicates
      that this is a continuation of a prior `ListUrlLists` call, and that the
      system should return the next page of data.
    parent: Required. The project and location from which the UrlLists should
      be listed, specified in the format
      `projects/{project}/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworksecurityProjectsLocationsUrlListsPatchRequest(_messages.Message):
  r"""A NetworksecurityProjectsLocationsUrlListsPatchRequest object.

  Fields:
    name: Required. Name of the resource provided by the user. Name is of the
      form projects/{project}/locations/{location}/urlLists/{url_list}
      url_list should match the pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the UrlList resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
    urlList: A UrlList resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  urlList = _messages.MessageField('UrlList', 3)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal response of the operation in case of success. If
      the original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal response of the operation in case of success. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal response of the operation in case of success. If the
    original method returns no data on success, such as `Delete`, the response
    is `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Reference(_messages.Message):
  r"""Represents a reference to a resource.

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    createTime: Output only. The creation time.
    details: Details of the reference type with no implied semantics.
      Cumulative size of the field must not be more than 1KiB.
    name: Output only. Relative resource name of the reference. Includes
      target resource as a parent and reference uid
      `{target_resource}/references/{reference_id}`. For example,
      `projects/{my-project}/locations/{location}/instances/{my-
      instance}/references/{xyz}`.
    sourceResource: Required. Full resource name of the resource which refers
      the target resource. For example:
      //tpu.googleapis.com/projects/myproject/nodes/mynode
    targetUniqueId: Output only. The unique_id of the target resource. Example
      1: (For arcus resource) A-1-0-2-387420123-13-913517247483640811
      unique_id format defined in go/m11n-unique-id-as-resource-id Example 2:
      (For CCFE resource) 123e4567-e89b-12d3-a456-************
    type: Required. Type of the reference. A service might impose limits on
      number of references of a specific type. Note: It's recommended to use
      CAPITALS_WITH_UNDERSCORES style for a type name.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  name = _messages.StringField(3)
  sourceResource = _messages.StringField(4)
  targetUniqueId = _messages.StringField(5)
  type = _messages.StringField(6)


class RemoveAddressGroupItemsRequest(_messages.Message):
  r"""Request used by the RemoveAddressGroupItems method.

  Fields:
    items: Required. List of items to remove.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  items = _messages.StringField(1, repeated=True)
  requestId = _messages.StringField(2)


class Rule(_messages.Message):
  r"""Specification of rules.

  Fields:
    destinations: Optional. List of attributes for the traffic destination.
      All of the destinations must match. A destination is a match if a
      request matches all the specified hosts, ports, methods and headers. If
      not set, the action specified in the 'action' field will be applied
      without any rule checks for the destination.
    sources: Optional. List of attributes for the traffic source. All of the
      sources must match. A source is a match if both principals and ip_blocks
      match. If not set, the action specified in the 'action' field will be
      applied without any rule checks for the source.
  """

  destinations = _messages.MessageField('Destination', 1, repeated=True)
  sources = _messages.MessageField('Source', 2, repeated=True)


class SecurityProfile(_messages.Message):
  r"""SecurityProfile is a resource that defines the behavior for one of many
  ProfileTypes. Next ID: 9

  Enums:
    TypeValueValuesEnum: Immutable. The single ProfileType that the
      SecurityProfile resource configures.

  Messages:
    LabelsValue: Optional. Labels as key value pairs.

  Fields:
    createTime: Output only. Resource creation timestamp.
    description: Optional. An optional description of the profile. Max length
      512 characters.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and may be sent on update and delete requests to
      ensure the client has an up-to-date value before proceeding.
    labels: Optional. Labels as key value pairs.
    name: Immutable. Name of the SecurityProfile resource. It matches pattern
      `projects|organizations/*/locations/{location}/securityProfiles/{securit
      y_profile}`.
    threatPreventionProfile: The threat prevention configuration for the
      SecurityProfile.
    type: Immutable. The single ProfileType that the SecurityProfile resource
      configures.
    updateTime: Output only. Last resource update timestamp.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Immutable. The single ProfileType that the SecurityProfile resource
    configures.

    Values:
      PROFILE_TYPE_UNSPECIFIED: Profile type not specified.
      THREAT_PREVENTION: Profile type for threat prevention.
    """
    PROFILE_TYPE_UNSPECIFIED = 0
    THREAT_PREVENTION = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  etag = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  threatPreventionProfile = _messages.MessageField('ThreatPreventionProfile', 6)
  type = _messages.EnumField('TypeValueValuesEnum', 7)
  updateTime = _messages.StringField(8)


class SecurityProfileGroup(_messages.Message):
  r"""SecurityProfileGroup is a resource that defines the behavior for various
  ProfileTypes. Next ID: 8

  Messages:
    LabelsValue: Optional. Labels as key value pairs.

  Fields:
    createTime: Output only. Resource creation timestamp.
    description: Optional. An optional description of the profile group. Max
      length 2048 characters.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and may be sent on update and delete requests to
      ensure the client has an up-to-date value before proceeding.
    labels: Optional. Labels as key value pairs.
    name: Immutable. Name of the SecurityProfileGroup resource. It matches
      pattern `projects|organizations/*/locations/{location}/securityProfileGr
      oups/{security_profile_group}`.
    threatPreventionProfile: Optional. Reference to a SecurityProfile with the
      threat prevention configuration for the SecurityProfileGroup.
    updateTime: Output only. Last resource update timestamp.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  etag = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  threatPreventionProfile = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class ServerTlsPolicy(_messages.Message):
  r"""ServerTlsPolicy is a resource that specifies how a server should
  authenticate incoming requests. This resource itself does not affect
  configuration unless it is attached to a target HTTPS proxy or endpoint
  config selector resource. ServerTlsPolicy in the form accepted by external
  HTTPS load balancers can be attached only to TargetHttpsProxy with an
  `EXTERNAL` or `EXTERNAL_MANAGED` load balancing scheme. Traffic Director
  compatible ServerTlsPolicies can be attached to EndpointPolicy and
  TargetHttpsProxy with Traffic Director `INTERNAL_SELF_MANAGED` load
  balancing scheme.

  Messages:
    LabelsValue: Set of label tags associated with the resource.

  Fields:
    allowOpen: This field applies only for Traffic Director policies. It is
      must be set to false for external HTTPS load balancer policies.
      Determines if server allows plaintext connections. If set to true,
      server allows plain text connections. By default, it is set to false.
      This setting is not exclusive of other encryption modes. For example, if
      `allow_open` and `mtls_policy` are set, server allows both plain text
      and mTLS connections. See documentation of other encryption modes to
      confirm compatibility. Consider using it if you wish to upgrade in place
      your deployment to TLS while having mixed TLS and non-TLS traffic
      reaching port :80.
    createTime: Output only. The timestamp when the resource was created.
    description: Free-text description of the resource.
    internalCaller: Optional. A flag set to identify internal controllers
      Setting this will trigger a P4SA check to validate the caller is from an
      allowlisted service's P4SA even if other optional fields are unset.
    labels: Set of label tags associated with the resource.
    mtlsPolicy: This field is required if the policy is used with external
      HTTPS load balancers. This field can be empty for Traffic Director.
      Defines a mechanism to provision peer validation certificates for peer
      to peer authentication (Mutual TLS - mTLS). If not specified, client
      certificate will not be requested. The connection is treated as TLS and
      not mTLS. If `allow_open` and `mtls_policy` are set, server allows both
      plain text and mTLS connections.
    name: Required. Name of the ServerTlsPolicy resource. It matches the
      pattern
      `projects/*/locations/{location}/serverTlsPolicies/{server_tls_policy}`
    serverCertificate: Optional if policy is to be used with Traffic Director.
      For external HTTPS load balancer must be empty. Defines a mechanism to
      provision server identity (public and private keys). Cannot be combined
      with `allow_open` as a permissive mode that allows both plain text and
      TLS is not supported.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Set of label tags associated with the resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allowOpen = _messages.BooleanField(1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  internalCaller = _messages.BooleanField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  mtlsPolicy = _messages.MessageField('MTLSPolicy', 6)
  name = _messages.StringField(7)
  serverCertificate = _messages.MessageField('GoogleCloudNetworksecurityV1beta1CertificateProvider', 8)
  updateTime = _messages.StringField(9)


class SeverityOverride(_messages.Message):
  r"""Defines what action to take for a specific severity match.

  Enums:
    ActionValueValuesEnum: Required. Threat action override.
    SeverityValueValuesEnum: Required. Severity level to match.

  Fields:
    action: Required. Threat action override.
    severity: Required. Severity level to match.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. Threat action override.

    Values:
      THREAT_ACTION_UNSPECIFIED: Threat action not specified.
      DEFAULT_ACTION: The default action (as specified by the vendor) is
        taken.
      ALLOW: The packet matching this rule will be allowed to transmit.
      ALERT: The packet matching this rule will be allowed to transmit, but a
        threat_log entry will be sent to the consumer project.
      DENY: The packet matching this rule will be dropped, and a threat_log
        entry will be sent to the consumer project.
    """
    THREAT_ACTION_UNSPECIFIED = 0
    DEFAULT_ACTION = 1
    ALLOW = 2
    ALERT = 3
    DENY = 4

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Required. Severity level to match.

    Values:
      SEVERITY_UNSPECIFIED: Severity level not specified.
      INFORMATIONAL: Suspicious events that do not pose an immediate threat,
        but that are reported to call attention to deeper problems that could
        possibly exist.
      LOW: Warning-level threats that have very little impact on an
        organization's infrastructure. They usually require local or physical
        system access and may often result in victim privacy issues and
        information leakage.
      MEDIUM: Minor threats in which impact is minimized, that do not
        compromise the target or exploits that require an attacker to reside
        on the same local network as the victim, affect only non-standard
        configurations or obscure applications, or provide very limited
        access.
      HIGH: Threats that have the ability to become critical but have
        mitigating factors; for example, they may be difficult to exploit, do
        not result in elevated privileges, or do not have a large victim pool.
      CRITICAL: Serious threats, such as those that affect default
        installations of widely deployed software, result in root compromise
        of servers, and the exploit code is widely available to attackers. The
        attacker usually does not need any special authentication credentials
        or knowledge about the individual victims and the target does not need
        to be manipulated into performing any special functions.
    """
    SEVERITY_UNSPECIFIED = 0
    INFORMATIONAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  severity = _messages.EnumField('SeverityValueValuesEnum', 2)


class Source(_messages.Message):
  r"""Specification of traffic source attributes.

  Fields:
    ipBlocks: Optional. List of CIDR ranges to match based on source IP
      address. At least one IP block should match. Single IP (e.g., "*******")
      and CIDR (e.g., "*******/24") are supported. Authorization based on
      source IP alone should be avoided. The IP addresses of any load
      balancers or proxies should be considered untrusted.
    principals: Optional. List of peer identities to match for authorization.
      At least one principal should match. Each peer can be an exact match, or
      a prefix match (example, "namespace/*") or a suffix match (example,
      "*/service-account") or a presence match "*". Authorization based on the
      principal name without certificate validation (configured by
      ServerTlsPolicy resource) is considered insecure.
  """

  ipBlocks = _messages.StringField(1, repeated=True)
  principals = _messages.StringField(2, repeated=True)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class ThreatOverride(_messages.Message):
  r"""Defines what action to take for a specific threat_id match.

  Enums:
    ActionValueValuesEnum: Required. Threat action override. For some threat
      types, only a subset of actions applies.
    TypeValueValuesEnum: Output only. Type of the threat (read only).

  Fields:
    action: Required. Threat action override. For some threat types, only a
      subset of actions applies.
    threatId: Required. Vendor-specific ID of a threat to override.
    type: Output only. Type of the threat (read only).
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required. Threat action override. For some threat types, only a subset
    of actions applies.

    Values:
      THREAT_ACTION_UNSPECIFIED: Threat action not specified.
      DEFAULT_ACTION: The default action (as specified by the vendor) is
        taken.
      ALLOW: The packet matching this rule will be allowed to transmit.
      ALERT: The packet matching this rule will be allowed to transmit, but a
        threat_log entry will be sent to the consumer project.
      DENY: The packet matching this rule will be dropped, and a threat_log
        entry will be sent to the consumer project.
    """
    THREAT_ACTION_UNSPECIFIED = 0
    DEFAULT_ACTION = 1
    ALLOW = 2
    ALERT = 3
    DENY = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of the threat (read only).

    Values:
      THREAT_TYPE_UNSPECIFIED: Type of threat not specified.
      UNKNOWN: Type of threat is not derivable from threat ID. An override
        will be created for all types. Firewall will ignore overridden
        signature ID's that don't exist in the specific type.
      VULNERABILITY: Threats related to system flaws that an attacker might
        otherwise attempt to exploit.
      ANTIVIRUS: Threats related to viruses and malware found in executables
        and file types.
      SPYWARE: Threats related to command-and-control (C2) activity, where
        spyware on an infected client is collecting data without the user's
        consent and/or communicating with a remote attacker.
      DNS: Threats related to DNS.
    """
    THREAT_TYPE_UNSPECIFIED = 0
    UNKNOWN = 1
    VULNERABILITY = 2
    ANTIVIRUS = 3
    SPYWARE = 4
    DNS = 5

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  threatId = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class ThreatPreventionProfile(_messages.Message):
  r"""ThreatPreventionProfile defines an action for specific threat signatures
  or severity levels.

  Fields:
    severityOverrides: Optional. Configuration for overriding threats actions
      by severity match.
    threatOverrides: Optional. Configuration for overriding threats actions by
      threat_id match. If a threat is matched both by configuration provided
      in severity_overrides and threat_overrides, the threat_overrides action
      is applied.
  """

  severityOverrides = _messages.MessageField('SeverityOverride', 1, repeated=True)
  threatOverrides = _messages.MessageField('ThreatOverride', 2, repeated=True)


class TlsInspectionPolicy(_messages.Message):
  r"""The TlsInspectionPolicy resource contains references to CA pools in
  Certificate Authority Service and associated metadata.

  Enums:
    MinTlsVersionValueValuesEnum: Optional. Minimum TLS version that the
      firewall should use when negotiating connections with both clients and
      servers. If this is not set, then the default value is to allow the
      broadest set of clients and servers (TLS 1.0 or higher). Setting this to
      more restrictive values may improve security, but may also prevent the
      firewall from connecting to some clients or servers. Note that Secure
      Web Proxy does not yet honor this field.
    TlsFeatureProfileValueValuesEnum: Optional. The selected Profile. If this
      is not set, then the default value is to allow the broadest set of
      clients and servers ("PROFILE_COMPATIBLE"). Setting this to more
      restrictive values may improve security, but may also prevent the TLS
      inspection proxy from connecting to some clients or servers. Note that
      Secure Web Proxy does not yet honor this field.

  Fields:
    caPool: Required. A CA pool resource used to issue interception
      certificates. The CA pool string has a relative resource path following
      the form "projects/{project}/locations/{location}/caPools/{ca_pool}".
    createTime: Output only. The timestamp when the resource was created.
    customTlsFeatures: Optional. List of custom TLS cipher suites selected.
      This field is valid only if the selected tls_feature_profile is CUSTOM.
      The compute.SslPoliciesService.ListAvailableFeatures method returns the
      set of features that can be specified in this list. Note that Secure Web
      Proxy does not yet honor this field.
    description: Optional. Free-text description of the resource.
    excludePublicCaSet: Optional. If FALSE (the default), use our default set
      of public CAs in addition to any CAs specified in trust_config. These
      public CAs are currently based on the Mozilla Root Program and are
      subject to change over time. If TRUE, do not accept our default set of
      public CAs. Only CAs specified in trust_config will be accepted. This
      defaults to FALSE (use public CAs in addition to trust_config) for
      backwards compatibility, but trusting public root CAs is *not
      recommended* unless the traffic in question is outbound to public web
      servers. When possible, prefer setting this to "false" and explicitly
      specifying trusted CAs and certificates in a TrustConfig. Note that
      Secure Web Proxy does not yet honor this field.
    minTlsVersion: Optional. Minimum TLS version that the firewall should use
      when negotiating connections with both clients and servers. If this is
      not set, then the default value is to allow the broadest set of clients
      and servers (TLS 1.0 or higher). Setting this to more restrictive values
      may improve security, but may also prevent the firewall from connecting
      to some clients or servers. Note that Secure Web Proxy does not yet
      honor this field.
    name: Required. Name of the resource. Name is of the form projects/{projec
      t}/locations/{location}/tlsInspectionPolicies/{tls_inspection_policy}
      tls_inspection_policy should match the
      pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
    tlsFeatureProfile: Optional. The selected Profile. If this is not set,
      then the default value is to allow the broadest set of clients and
      servers ("PROFILE_COMPATIBLE"). Setting this to more restrictive values
      may improve security, but may also prevent the TLS inspection proxy from
      connecting to some clients or servers. Note that Secure Web Proxy does
      not yet honor this field.
    trustConfig: Optional. A TrustConfig resource used when making a
      connection to the TLS server. This is a relative resource path following
      the form
      "projects/{project}/locations/{location}/trustConfigs/{trust_config}".
      This is necessary to intercept TLS connections to servers with
      certificates signed by a private CA or self-signed certificates. Note
      that Secure Web Proxy does not yet honor this field.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  class MinTlsVersionValueValuesEnum(_messages.Enum):
    r"""Optional. Minimum TLS version that the firewall should use when
    negotiating connections with both clients and servers. If this is not set,
    then the default value is to allow the broadest set of clients and servers
    (TLS 1.0 or higher). Setting this to more restrictive values may improve
    security, but may also prevent the firewall from connecting to some
    clients or servers. Note that Secure Web Proxy does not yet honor this
    field.

    Values:
      TLS_VERSION_UNSPECIFIED: Indicates no TLS version was specified.
      TLS_1_0: TLS 1.0
      TLS_1_1: TLS 1.1
      TLS_1_2: TLS 1.2
      TLS_1_3: TLS 1.3
    """
    TLS_VERSION_UNSPECIFIED = 0
    TLS_1_0 = 1
    TLS_1_1 = 2
    TLS_1_2 = 3
    TLS_1_3 = 4

  class TlsFeatureProfileValueValuesEnum(_messages.Enum):
    r"""Optional. The selected Profile. If this is not set, then the default
    value is to allow the broadest set of clients and servers
    ("PROFILE_COMPATIBLE"). Setting this to more restrictive values may
    improve security, but may also prevent the TLS inspection proxy from
    connecting to some clients or servers. Note that Secure Web Proxy does not
    yet honor this field.

    Values:
      PROFILE_UNSPECIFIED: Indicates no profile was specified.
      PROFILE_COMPATIBLE: Compatible profile. Allows the broadest set of
        clients, even those which support only out-of-date SSL features to
        negotiate with the TLS inspection proxy.
      PROFILE_MODERN: Modern profile. Supports a wide set of SSL features,
        allowing modern clients to negotiate SSL with the TLS inspection
        proxy.
      PROFILE_RESTRICTED: Restricted profile. Supports a reduced set of SSL
        features, intended to meet stricter compliance requirements.
      PROFILE_CUSTOM: Custom profile. Allow only the set of allowed SSL
        features specified in the custom_features field of SslPolicy.
    """
    PROFILE_UNSPECIFIED = 0
    PROFILE_COMPATIBLE = 1
    PROFILE_MODERN = 2
    PROFILE_RESTRICTED = 3
    PROFILE_CUSTOM = 4

  caPool = _messages.StringField(1)
  createTime = _messages.StringField(2)
  customTlsFeatures = _messages.StringField(3, repeated=True)
  description = _messages.StringField(4)
  excludePublicCaSet = _messages.BooleanField(5)
  minTlsVersion = _messages.EnumField('MinTlsVersionValueValuesEnum', 6)
  name = _messages.StringField(7)
  tlsFeatureProfile = _messages.EnumField('TlsFeatureProfileValueValuesEnum', 8)
  trustConfig = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class UrlList(_messages.Message):
  r"""UrlList proto helps users to set reusable, independently manageable
  lists of hosts, host patterns, URLs, URL patterns.

  Fields:
    createTime: Output only. Time when the security policy was created.
    description: Optional. Free-text description of the resource.
    name: Required. Name of the resource provided by the user. Name is of the
      form projects/{project}/locations/{location}/urlLists/{url_list}
      url_list should match the pattern:(^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$).
    updateTime: Output only. Time when the security policy was updated.
    values: Required. FQDNs and URLs.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  name = _messages.StringField(3)
  updateTime = _messages.StringField(4)
  values = _messages.StringField(5, repeated=True)


class ValidationCA(_messages.Message):
  r"""Specification of ValidationCA. Defines the mechanism to obtain the
  Certificate Authority certificate to validate the peer certificate.

  Fields:
    certificateProviderInstance: The certificate provider instance
      specification that will be passed to the data plane, which will be used
      to load necessary credential information.
    grpcEndpoint: gRPC specific configuration to access the gRPC server to
      obtain the CA certificate.
  """

  certificateProviderInstance = _messages.MessageField('CertificateProviderInstance', 1)
  grpcEndpoint = _messages.MessageField('GoogleCloudNetworksecurityV1beta1GrpcEndpoint', 2)


class WorkloadContextSelector(_messages.Message):
  r"""Determines which workloads a policy is applicable for.

  Fields:
    metadataSelectors: Required. A map of metadata label values used to select
      workloads. If multiple MetadataSelectors are provided, all
      MetadataSelectors must match in order for the policy to be applied to
      this workload. Therefore these selectors must be combined in an AND
      fashion.
  """

  metadataSelectors = _messages.MessageField('WorkloadContextSelectorMetadataSelector', 1, repeated=True)


class WorkloadContextSelectorMetadataSelector(_messages.Message):
  r"""This message type exists as opposed to using a map to support additional
  fields in the future such as priority.

  Fields:
    key: Required. The metadata field being selected on
    value: Required. The value for this metadata field to be compared with
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    GoogleIamV1Rule, 'in_', 'in')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

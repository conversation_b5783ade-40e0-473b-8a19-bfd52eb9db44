"""Generated client library for networksecurity version v1alpha1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.networksecurity.v1alpha1 import networksecurity_v1alpha1_messages as messages


class NetworksecurityV1alpha1(base_api.BaseApiClient):
  """Generated client library for service networksecurity version v1alpha1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://networksecurity.googleapis.com/'
  MTLS_BASE_URL = 'https://networksecurity.mtls.googleapis.com/'

  _PACKAGE = 'networksecurity'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'NetworksecurityV1alpha1'
  _URL_VERSION = 'v1alpha1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new networksecurity handle."""
    url = url or self.BASE_URL
    super(NetworksecurityV1alpha1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.organizations_locations_addressGroups = self.OrganizationsLocationsAddressGroupsService(self)
    self.organizations_locations_firewallEndpoints = self.OrganizationsLocationsFirewallEndpointsService(self)
    self.organizations_locations_operations = self.OrganizationsLocationsOperationsService(self)
    self.organizations_locations_securityProfileGroups = self.OrganizationsLocationsSecurityProfileGroupsService(self)
    self.organizations_locations_securityProfiles = self.OrganizationsLocationsSecurityProfilesService(self)
    self.organizations_locations = self.OrganizationsLocationsService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_locations_addressGroups = self.ProjectsLocationsAddressGroupsService(self)
    self.projects_locations_authorizationPolicies = self.ProjectsLocationsAuthorizationPoliciesService(self)
    self.projects_locations_clientTlsPolicies = self.ProjectsLocationsClientTlsPoliciesService(self)
    self.projects_locations_firewallAttachments = self.ProjectsLocationsFirewallAttachmentsService(self)
    self.projects_locations_firewallEndpointAssociations = self.ProjectsLocationsFirewallEndpointAssociationsService(self)
    self.projects_locations_gatewaySecurityPolicies_rules = self.ProjectsLocationsGatewaySecurityPoliciesRulesService(self)
    self.projects_locations_gatewaySecurityPolicies = self.ProjectsLocationsGatewaySecurityPoliciesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_partnerSSEGateways = self.ProjectsLocationsPartnerSSEGatewaysService(self)
    self.projects_locations_partnerSSERealms = self.ProjectsLocationsPartnerSSERealmsService(self)
    self.projects_locations_securityProfileGroups = self.ProjectsLocationsSecurityProfileGroupsService(self)
    self.projects_locations_securityProfiles = self.ProjectsLocationsSecurityProfilesService(self)
    self.projects_locations_serverTlsPolicies = self.ProjectsLocationsServerTlsPoliciesService(self)
    self.projects_locations_sseGatewayReferences = self.ProjectsLocationsSseGatewayReferencesService(self)
    self.projects_locations_sseGateways = self.ProjectsLocationsSseGatewaysService(self)
    self.projects_locations_sseRealms = self.ProjectsLocationsSseRealmsService(self)
    self.projects_locations_tlsInspectionPolicies = self.ProjectsLocationsTlsInspectionPoliciesService(self)
    self.projects_locations_urlLists = self.ProjectsLocationsUrlListsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class OrganizationsLocationsAddressGroupsService(base_api.BaseApiService):
    """Service class for the organizations_locations_addressGroups resource."""

    _NAME = 'organizations_locations_addressGroups'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.OrganizationsLocationsAddressGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddItems(self, request, global_params=None):
      r"""Adds items to an address group.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsAddItemsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('AddItems')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddItems.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:addItems',
        http_method='POST',
        method_id='networksecurity.organizations.locations.addressGroups.addItems',
        ordered_params=['addressGroup'],
        path_params=['addressGroup'],
        query_params=[],
        relative_path='v1alpha1/{+addressGroup}:addItems',
        request_field='addAddressGroupItemsRequest',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsAddItemsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def CloneItems(self, request, global_params=None):
      r"""Clones items from one address group to another.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsCloneItemsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('CloneItems')
      return self._RunMethod(
          config, request, global_params=global_params)

    CloneItems.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:cloneItems',
        http_method='POST',
        method_id='networksecurity.organizations.locations.addressGroups.cloneItems',
        ordered_params=['addressGroup'],
        path_params=['addressGroup'],
        query_params=[],
        relative_path='v1alpha1/{+addressGroup}:cloneItems',
        request_field='cloneAddressGroupItemsRequest',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsCloneItemsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new address group in a given project and location.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups',
        http_method='POST',
        method_id='networksecurity.organizations.locations.addressGroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['addressGroupId', 'requestId'],
        relative_path='v1alpha1/{+parent}/addressGroups',
        request_field='addressGroup',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an address group.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}',
        http_method='DELETE',
        method_id='networksecurity.organizations.locations.addressGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single address group.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AddressGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}',
        http_method='GET',
        method_id='networksecurity.organizations.locations.addressGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsGetRequest',
        response_type_name='AddressGroup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists address groups in a given project and location.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAddressGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups',
        http_method='GET',
        method_id='networksecurity.organizations.locations.addressGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/addressGroups',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsListRequest',
        response_type_name='ListAddressGroupsResponse',
        supports_download=False,
    )

    def ListReferences(self, request, global_params=None):
      r"""Lists references of an address group.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsListReferencesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAddressGroupReferencesResponse) The response message.
      """
      config = self.GetMethodConfig('ListReferences')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListReferences.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:listReferences',
        http_method='GET',
        method_id='networksecurity.organizations.locations.addressGroups.listReferences',
        ordered_params=['addressGroup'],
        path_params=['addressGroup'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+addressGroup}:listReferences',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsListReferencesRequest',
        response_type_name='ListAddressGroupReferencesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates parameters of an address group.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}',
        http_method='PATCH',
        method_id='networksecurity.organizations.locations.addressGroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='addressGroup',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RemoveItems(self, request, global_params=None):
      r"""Removes items from an address group.

      Args:
        request: (NetworksecurityOrganizationsLocationsAddressGroupsRemoveItemsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('RemoveItems')
      return self._RunMethod(
          config, request, global_params=global_params)

    RemoveItems.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:removeItems',
        http_method='POST',
        method_id='networksecurity.organizations.locations.addressGroups.removeItems',
        ordered_params=['addressGroup'],
        path_params=['addressGroup'],
        query_params=[],
        relative_path='v1alpha1/{+addressGroup}:removeItems',
        request_field='removeAddressGroupItemsRequest',
        request_type_name='NetworksecurityOrganizationsLocationsAddressGroupsRemoveItemsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrganizationsLocationsFirewallEndpointsService(base_api.BaseApiService):
    """Service class for the organizations_locations_firewallEndpoints resource."""

    _NAME = 'organizations_locations_firewallEndpoints'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.OrganizationsLocationsFirewallEndpointsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new FirewallEndpoint in a given project and location.

      Args:
        request: (NetworksecurityOrganizationsLocationsFirewallEndpointsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/firewallEndpoints',
        http_method='POST',
        method_id='networksecurity.organizations.locations.firewallEndpoints.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['firewallEndpointId', 'requestId'],
        relative_path='v1alpha1/{+parent}/firewallEndpoints',
        request_field='firewallEndpoint',
        request_type_name='NetworksecurityOrganizationsLocationsFirewallEndpointsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Endpoint.

      Args:
        request: (NetworksecurityOrganizationsLocationsFirewallEndpointsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/firewallEndpoints/{firewallEndpointsId}',
        http_method='DELETE',
        method_id='networksecurity.organizations.locations.firewallEndpoints.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsFirewallEndpointsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Endpoint.

      Args:
        request: (NetworksecurityOrganizationsLocationsFirewallEndpointsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FirewallEndpoint) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/firewallEndpoints/{firewallEndpointsId}',
        http_method='GET',
        method_id='networksecurity.organizations.locations.firewallEndpoints.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsFirewallEndpointsGetRequest',
        response_type_name='FirewallEndpoint',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists FirewallEndpoints in a given project and location.

      Args:
        request: (NetworksecurityOrganizationsLocationsFirewallEndpointsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFirewallEndpointsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/firewallEndpoints',
        http_method='GET',
        method_id='networksecurity.organizations.locations.firewallEndpoints.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/firewallEndpoints',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsFirewallEndpointsListRequest',
        response_type_name='ListFirewallEndpointsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single Endpoint.

      Args:
        request: (NetworksecurityOrganizationsLocationsFirewallEndpointsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/firewallEndpoints/{firewallEndpointsId}',
        http_method='PATCH',
        method_id='networksecurity.organizations.locations.firewallEndpoints.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='firewallEndpoint',
        request_type_name='NetworksecurityOrganizationsLocationsFirewallEndpointsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrganizationsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the organizations_locations_operations resource."""

    _NAME = 'organizations_locations_operations'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.OrganizationsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (NetworksecurityOrganizationsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='networksecurity.organizations.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='NetworksecurityOrganizationsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (NetworksecurityOrganizationsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='networksecurity.organizations.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (NetworksecurityOrganizationsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='networksecurity.organizations.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (NetworksecurityOrganizationsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='networksecurity.organizations.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class OrganizationsLocationsSecurityProfileGroupsService(base_api.BaseApiService):
    """Service class for the organizations_locations_securityProfileGroups resource."""

    _NAME = 'organizations_locations_securityProfileGroups'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.OrganizationsLocationsSecurityProfileGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new SecurityProfileGroup in a given organization and location.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfileGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfileGroups',
        http_method='POST',
        method_id='networksecurity.organizations.locations.securityProfileGroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['securityProfileGroupId'],
        relative_path='v1alpha1/{+parent}/securityProfileGroups',
        request_field='securityProfileGroup',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfileGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single SecurityProfileGroup.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfileGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfileGroups/{securityProfileGroupsId}',
        http_method='DELETE',
        method_id='networksecurity.organizations.locations.securityProfileGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfileGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single SecurityProfileGroup.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfileGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityProfileGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfileGroups/{securityProfileGroupsId}',
        http_method='GET',
        method_id='networksecurity.organizations.locations.securityProfileGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfileGroupsGetRequest',
        response_type_name='SecurityProfileGroup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SecurityProfileGroups in a given organization and location.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfileGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSecurityProfileGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfileGroups',
        http_method='GET',
        method_id='networksecurity.organizations.locations.securityProfileGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/securityProfileGroups',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfileGroupsListRequest',
        response_type_name='ListSecurityProfileGroupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single SecurityProfileGroup.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfileGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfileGroups/{securityProfileGroupsId}',
        http_method='PATCH',
        method_id='networksecurity.organizations.locations.securityProfileGroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='securityProfileGroup',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfileGroupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrganizationsLocationsSecurityProfilesService(base_api.BaseApiService):
    """Service class for the organizations_locations_securityProfiles resource."""

    _NAME = 'organizations_locations_securityProfiles'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.OrganizationsLocationsSecurityProfilesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new SecurityProfile in a given organization and location.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfilesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfiles',
        http_method='POST',
        method_id='networksecurity.organizations.locations.securityProfiles.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['securityProfileId'],
        relative_path='v1alpha1/{+parent}/securityProfiles',
        request_field='securityProfile',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfilesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single SecurityProfile.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfilesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfiles/{securityProfilesId}',
        http_method='DELETE',
        method_id='networksecurity.organizations.locations.securityProfiles.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfilesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single SecurityProfile.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfilesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityProfile) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfiles/{securityProfilesId}',
        http_method='GET',
        method_id='networksecurity.organizations.locations.securityProfiles.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfilesGetRequest',
        response_type_name='SecurityProfile',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SecurityProfiles in a given organization and location.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfilesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSecurityProfilesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfiles',
        http_method='GET',
        method_id='networksecurity.organizations.locations.securityProfiles.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/securityProfiles',
        request_field='',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfilesListRequest',
        response_type_name='ListSecurityProfilesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single SecurityProfile.

      Args:
        request: (NetworksecurityOrganizationsLocationsSecurityProfilesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/organizations/{organizationsId}/locations/{locationsId}/securityProfiles/{securityProfilesId}',
        http_method='PATCH',
        method_id='networksecurity.organizations.locations.securityProfiles.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='securityProfile',
        request_type_name='NetworksecurityOrganizationsLocationsSecurityProfilesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrganizationsLocationsService(base_api.BaseApiService):
    """Service class for the organizations_locations resource."""

    _NAME = 'organizations_locations'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.OrganizationsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsAddressGroupsService(base_api.BaseApiService):
    """Service class for the projects_locations_addressGroups resource."""

    _NAME = 'projects_locations_addressGroups'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsAddressGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddItems(self, request, global_params=None):
      r"""Adds items to an address group.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsAddItemsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('AddItems')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddItems.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:addItems',
        http_method='POST',
        method_id='networksecurity.projects.locations.addressGroups.addItems',
        ordered_params=['addressGroup'],
        path_params=['addressGroup'],
        query_params=[],
        relative_path='v1alpha1/{+addressGroup}:addItems',
        request_field='addAddressGroupItemsRequest',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsAddItemsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def CloneItems(self, request, global_params=None):
      r"""Clones items from one address group to another.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsCloneItemsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('CloneItems')
      return self._RunMethod(
          config, request, global_params=global_params)

    CloneItems.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:cloneItems',
        http_method='POST',
        method_id='networksecurity.projects.locations.addressGroups.cloneItems',
        ordered_params=['addressGroup'],
        path_params=['addressGroup'],
        query_params=[],
        relative_path='v1alpha1/{+addressGroup}:cloneItems',
        request_field='cloneAddressGroupItemsRequest',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsCloneItemsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new address group in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups',
        http_method='POST',
        method_id='networksecurity.projects.locations.addressGroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['addressGroupId', 'requestId'],
        relative_path='v1alpha1/{+parent}/addressGroups',
        request_field='addressGroup',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single address group.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.addressGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single address group.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AddressGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.addressGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsGetRequest',
        response_type_name='AddressGroup',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:getIamPolicy',
        http_method='GET',
        method_id='networksecurity.projects.locations.addressGroups.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists address groups in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAddressGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups',
        http_method='GET',
        method_id='networksecurity.projects.locations.addressGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/addressGroups',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsListRequest',
        response_type_name='ListAddressGroupsResponse',
        supports_download=False,
    )

    def ListReferences(self, request, global_params=None):
      r"""Lists references of an address group.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsListReferencesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAddressGroupReferencesResponse) The response message.
      """
      config = self.GetMethodConfig('ListReferences')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListReferences.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:listReferences',
        http_method='GET',
        method_id='networksecurity.projects.locations.addressGroups.listReferences',
        ordered_params=['addressGroup'],
        path_params=['addressGroup'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+addressGroup}:listReferences',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsListReferencesRequest',
        response_type_name='ListAddressGroupReferencesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single address group.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.addressGroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='addressGroup',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def RemoveItems(self, request, global_params=None):
      r"""Removes items from an address group.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsRemoveItemsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('RemoveItems')
      return self._RunMethod(
          config, request, global_params=global_params)

    RemoveItems.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:removeItems',
        http_method='POST',
        method_id='networksecurity.projects.locations.addressGroups.removeItems',
        ordered_params=['addressGroup'],
        path_params=['addressGroup'],
        query_params=[],
        relative_path='v1alpha1/{+addressGroup}:removeItems',
        request_field='removeAddressGroupItemsRequest',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsRemoveItemsRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:setIamPolicy',
        http_method='POST',
        method_id='networksecurity.projects.locations.addressGroups.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworksecurityProjectsLocationsAddressGroupsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/addressGroups/{addressGroupsId}:testIamPermissions',
        http_method='POST',
        method_id='networksecurity.projects.locations.addressGroups.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='NetworksecurityProjectsLocationsAddressGroupsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsAuthorizationPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_authorizationPolicies resource."""

    _NAME = 'projects_locations_authorizationPolicies'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsAuthorizationPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new AuthorizationPolicy in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsAuthorizationPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies',
        http_method='POST',
        method_id='networksecurity.projects.locations.authorizationPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['authorizationPolicyId'],
        relative_path='v1alpha1/{+parent}/authorizationPolicies',
        request_field='authorizationPolicy',
        request_type_name='NetworksecurityProjectsLocationsAuthorizationPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single AuthorizationPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsAuthorizationPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.authorizationPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAuthorizationPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single AuthorizationPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsAuthorizationPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (AuthorizationPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.authorizationPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAuthorizationPoliciesGetRequest',
        response_type_name='AuthorizationPolicy',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworksecurityProjectsLocationsAuthorizationPoliciesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}:getIamPolicy',
        http_method='GET',
        method_id='networksecurity.projects.locations.authorizationPolicies.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAuthorizationPoliciesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists AuthorizationPolicies in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsAuthorizationPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAuthorizationPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies',
        http_method='GET',
        method_id='networksecurity.projects.locations.authorizationPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/authorizationPolicies',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsAuthorizationPoliciesListRequest',
        response_type_name='ListAuthorizationPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single AuthorizationPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsAuthorizationPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.authorizationPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='authorizationPolicy',
        request_type_name='NetworksecurityProjectsLocationsAuthorizationPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworksecurityProjectsLocationsAuthorizationPoliciesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}:setIamPolicy',
        http_method='POST',
        method_id='networksecurity.projects.locations.authorizationPolicies.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='NetworksecurityProjectsLocationsAuthorizationPoliciesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworksecurityProjectsLocationsAuthorizationPoliciesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/authorizationPolicies/{authorizationPoliciesId}:testIamPermissions',
        http_method='POST',
        method_id='networksecurity.projects.locations.authorizationPolicies.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='NetworksecurityProjectsLocationsAuthorizationPoliciesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsClientTlsPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_clientTlsPolicies resource."""

    _NAME = 'projects_locations_clientTlsPolicies'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsClientTlsPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ClientTlsPolicy in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsClientTlsPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies',
        http_method='POST',
        method_id='networksecurity.projects.locations.clientTlsPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['clientTlsPolicyId'],
        relative_path='v1alpha1/{+parent}/clientTlsPolicies',
        request_field='clientTlsPolicy',
        request_type_name='NetworksecurityProjectsLocationsClientTlsPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ClientTlsPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsClientTlsPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.clientTlsPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsClientTlsPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ClientTlsPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsClientTlsPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ClientTlsPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.clientTlsPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsClientTlsPoliciesGetRequest',
        response_type_name='ClientTlsPolicy',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworksecurityProjectsLocationsClientTlsPoliciesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}:getIamPolicy',
        http_method='GET',
        method_id='networksecurity.projects.locations.clientTlsPolicies.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsClientTlsPoliciesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ClientTlsPolicies in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsClientTlsPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListClientTlsPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies',
        http_method='GET',
        method_id='networksecurity.projects.locations.clientTlsPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/clientTlsPolicies',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsClientTlsPoliciesListRequest',
        response_type_name='ListClientTlsPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single ClientTlsPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsClientTlsPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.clientTlsPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='clientTlsPolicy',
        request_type_name='NetworksecurityProjectsLocationsClientTlsPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworksecurityProjectsLocationsClientTlsPoliciesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}:setIamPolicy',
        http_method='POST',
        method_id='networksecurity.projects.locations.clientTlsPolicies.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='NetworksecurityProjectsLocationsClientTlsPoliciesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworksecurityProjectsLocationsClientTlsPoliciesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/clientTlsPolicies/{clientTlsPoliciesId}:testIamPermissions',
        http_method='POST',
        method_id='networksecurity.projects.locations.clientTlsPolicies.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='NetworksecurityProjectsLocationsClientTlsPoliciesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsFirewallAttachmentsService(base_api.BaseApiService):
    """Service class for the projects_locations_firewallAttachments resource."""

    _NAME = 'projects_locations_firewallAttachments'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsFirewallAttachmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new FirewallAttachment in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallAttachmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallAttachments',
        http_method='POST',
        method_id='networksecurity.projects.locations.firewallAttachments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['firewallAttachmentId', 'requestId'],
        relative_path='v1alpha1/{+parent}/firewallAttachments',
        request_field='firewallAttachment',
        request_type_name='NetworksecurityProjectsLocationsFirewallAttachmentsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Attachment.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallAttachmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallAttachments/{firewallAttachmentsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.firewallAttachments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsFirewallAttachmentsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Attachment.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallAttachmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FirewallAttachment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallAttachments/{firewallAttachmentsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.firewallAttachments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsFirewallAttachmentsGetRequest',
        response_type_name='FirewallAttachment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists FirewallAttachments in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallAttachmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFirewallAttachmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallAttachments',
        http_method='GET',
        method_id='networksecurity.projects.locations.firewallAttachments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/firewallAttachments',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsFirewallAttachmentsListRequest',
        response_type_name='ListFirewallAttachmentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single FirewallAttachment.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallAttachmentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallAttachments/{firewallAttachmentsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.firewallAttachments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='firewallAttachment',
        request_type_name='NetworksecurityProjectsLocationsFirewallAttachmentsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsFirewallEndpointAssociationsService(base_api.BaseApiService):
    """Service class for the projects_locations_firewallEndpointAssociations resource."""

    _NAME = 'projects_locations_firewallEndpointAssociations'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsFirewallEndpointAssociationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new FirewallEndpointAssociation in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallEndpointAssociationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallEndpointAssociations',
        http_method='POST',
        method_id='networksecurity.projects.locations.firewallEndpointAssociations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['firewallEndpointAssociationId', 'requestId'],
        relative_path='v1alpha1/{+parent}/firewallEndpointAssociations',
        request_field='firewallEndpointAssociation',
        request_type_name='NetworksecurityProjectsLocationsFirewallEndpointAssociationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single FirewallEndpointAssociation.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallEndpointAssociationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallEndpointAssociations/{firewallEndpointAssociationsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.firewallEndpointAssociations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsFirewallEndpointAssociationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single FirewallEndpointAssociation.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallEndpointAssociationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (FirewallEndpointAssociation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallEndpointAssociations/{firewallEndpointAssociationsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.firewallEndpointAssociations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsFirewallEndpointAssociationsGetRequest',
        response_type_name='FirewallEndpointAssociation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Associations in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallEndpointAssociationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFirewallEndpointAssociationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallEndpointAssociations',
        http_method='GET',
        method_id='networksecurity.projects.locations.firewallEndpointAssociations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/firewallEndpointAssociations',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsFirewallEndpointAssociationsListRequest',
        response_type_name='ListFirewallEndpointAssociationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Update a single FirewallEndpointAssociation.

      Args:
        request: (NetworksecurityProjectsLocationsFirewallEndpointAssociationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/firewallEndpointAssociations/{firewallEndpointAssociationsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.firewallEndpointAssociations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='firewallEndpointAssociation',
        request_type_name='NetworksecurityProjectsLocationsFirewallEndpointAssociationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsGatewaySecurityPoliciesRulesService(base_api.BaseApiService):
    """Service class for the projects_locations_gatewaySecurityPolicies_rules resource."""

    _NAME = 'projects_locations_gatewaySecurityPolicies_rules'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsGatewaySecurityPoliciesRulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new GatewaySecurityPolicy in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}/rules',
        http_method='POST',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.rules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['gatewaySecurityPolicyRuleId'],
        relative_path='v1alpha1/{+parent}/rules',
        request_field='gatewaySecurityPolicyRule',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single GatewaySecurityPolicyRule.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}/rules/{rulesId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.rules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single GatewaySecurityPolicyRule.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GatewaySecurityPolicyRule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}/rules/{rulesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.rules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesGetRequest',
        response_type_name='GatewaySecurityPolicyRule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists GatewaySecurityPolicyRules in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGatewaySecurityPolicyRulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}/rules',
        http_method='GET',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.rules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/rules',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesListRequest',
        response_type_name='ListGatewaySecurityPolicyRulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single GatewaySecurityPolicyRule.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}/rules/{rulesId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.rules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='gatewaySecurityPolicyRule',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesRulesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsGatewaySecurityPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_gatewaySecurityPolicies resource."""

    _NAME = 'projects_locations_gatewaySecurityPolicies'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsGatewaySecurityPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new GatewaySecurityPolicy in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies',
        http_method='POST',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['gatewaySecurityPolicyId'],
        relative_path='v1alpha1/{+parent}/gatewaySecurityPolicies',
        request_field='gatewaySecurityPolicy',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single GatewaySecurityPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single GatewaySecurityPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GatewaySecurityPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesGetRequest',
        response_type_name='GatewaySecurityPolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists GatewaySecurityPolicies in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGatewaySecurityPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies',
        http_method='GET',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/gatewaySecurityPolicies',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesListRequest',
        response_type_name='ListGatewaySecurityPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single GatewaySecurityPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsGatewaySecurityPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gatewaySecurityPolicies/{gatewaySecurityPoliciesId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.gatewaySecurityPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='gatewaySecurityPolicy',
        request_type_name='NetworksecurityProjectsLocationsGatewaySecurityPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (NetworksecurityProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='networksecurity.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='NetworksecurityProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (NetworksecurityProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (NetworksecurityProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (NetworksecurityProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='networksecurity.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsPartnerSSEGatewaysService(base_api.BaseApiService):
    """Service class for the projects_locations_partnerSSEGateways resource."""

    _NAME = 'projects_locations_partnerSSEGateways'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsPartnerSSEGatewaysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new PartnerSSEGateway in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSEGatewaysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSEGateways',
        http_method='POST',
        method_id='networksecurity.projects.locations.partnerSSEGateways.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['partnerSseGatewayId', 'requestId'],
        relative_path='v1alpha1/{+parent}/partnerSSEGateways',
        request_field='partnerSSEGateway',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSEGatewaysCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single PartnerSSEGateway.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSEGatewaysDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSEGateways/{partnerSSEGatewaysId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.partnerSSEGateways.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSEGatewaysDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single PartnerSSEGateway.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSEGatewaysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PartnerSSEGateway) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSEGateways/{partnerSSEGatewaysId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.partnerSSEGateways.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSEGatewaysGetRequest',
        response_type_name='PartnerSSEGateway',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists PartnerSSEGateways in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSEGatewaysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPartnerSSEGatewaysResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSEGateways',
        http_method='GET',
        method_id='networksecurity.projects.locations.partnerSSEGateways.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/partnerSSEGateways',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSEGatewaysListRequest',
        response_type_name='ListPartnerSSEGatewaysResponse',
        supports_download=False,
    )

  class ProjectsLocationsPartnerSSERealmsService(base_api.BaseApiService):
    """Service class for the projects_locations_partnerSSERealms resource."""

    _NAME = 'projects_locations_partnerSSERealms'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsPartnerSSERealmsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new PartnerSSERealm in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSERealmsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSERealms',
        http_method='POST',
        method_id='networksecurity.projects.locations.partnerSSERealms.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['partnerSseRealmId', 'requestId'],
        relative_path='v1alpha1/{+parent}/partnerSSERealms',
        request_field='partnerSSERealm',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSERealmsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single PartnerSSERealm.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSERealmsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSERealms/{partnerSSERealmsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.partnerSSERealms.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSERealmsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single PartnerSSERealm.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSERealmsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (PartnerSSERealm) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSERealms/{partnerSSERealmsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.partnerSSERealms.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSERealmsGetRequest',
        response_type_name='PartnerSSERealm',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists PartnerSSERealms in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsPartnerSSERealmsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPartnerSSERealmsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/partnerSSERealms',
        http_method='GET',
        method_id='networksecurity.projects.locations.partnerSSERealms.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/partnerSSERealms',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsPartnerSSERealmsListRequest',
        response_type_name='ListPartnerSSERealmsResponse',
        supports_download=False,
    )

  class ProjectsLocationsSecurityProfileGroupsService(base_api.BaseApiService):
    """Service class for the projects_locations_securityProfileGroups resource."""

    _NAME = 'projects_locations_securityProfileGroups'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsSecurityProfileGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new SecurityProfileGroup in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfileGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfileGroups',
        http_method='POST',
        method_id='networksecurity.projects.locations.securityProfileGroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['securityProfileGroupId'],
        relative_path='v1alpha1/{+parent}/securityProfileGroups',
        request_field='securityProfileGroup',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfileGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single SecurityProfileGroup.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfileGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfileGroups/{securityProfileGroupsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.securityProfileGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfileGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single SecurityProfileGroup.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfileGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityProfileGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfileGroups/{securityProfileGroupsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.securityProfileGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfileGroupsGetRequest',
        response_type_name='SecurityProfileGroup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SecurityProfileGroups in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfileGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSecurityProfileGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfileGroups',
        http_method='GET',
        method_id='networksecurity.projects.locations.securityProfileGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/securityProfileGroups',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfileGroupsListRequest',
        response_type_name='ListSecurityProfileGroupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single SecurityProfileGroup.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfileGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfileGroups/{securityProfileGroupsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.securityProfileGroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='securityProfileGroup',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfileGroupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsSecurityProfilesService(base_api.BaseApiService):
    """Service class for the projects_locations_securityProfiles resource."""

    _NAME = 'projects_locations_securityProfiles'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsSecurityProfilesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new SecurityProfile in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfilesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfiles',
        http_method='POST',
        method_id='networksecurity.projects.locations.securityProfiles.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['securityProfileId'],
        relative_path='v1alpha1/{+parent}/securityProfiles',
        request_field='securityProfile',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfilesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single SecurityProfile.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfilesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfiles/{securityProfilesId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.securityProfiles.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfilesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single SecurityProfile.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfilesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityProfile) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfiles/{securityProfilesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.securityProfiles.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfilesGetRequest',
        response_type_name='SecurityProfile',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SecurityProfiles in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfilesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSecurityProfilesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfiles',
        http_method='GET',
        method_id='networksecurity.projects.locations.securityProfiles.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/securityProfiles',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfilesListRequest',
        response_type_name='ListSecurityProfilesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single SecurityProfile.

      Args:
        request: (NetworksecurityProjectsLocationsSecurityProfilesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/securityProfiles/{securityProfilesId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.securityProfiles.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='securityProfile',
        request_type_name='NetworksecurityProjectsLocationsSecurityProfilesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsServerTlsPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_serverTlsPolicies resource."""

    _NAME = 'projects_locations_serverTlsPolicies'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsServerTlsPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ServerTlsPolicy in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsServerTlsPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies',
        http_method='POST',
        method_id='networksecurity.projects.locations.serverTlsPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['serverTlsPolicyId'],
        relative_path='v1alpha1/{+parent}/serverTlsPolicies',
        request_field='serverTlsPolicy',
        request_type_name='NetworksecurityProjectsLocationsServerTlsPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ServerTlsPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsServerTlsPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.serverTlsPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsServerTlsPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ServerTlsPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsServerTlsPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServerTlsPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.serverTlsPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsServerTlsPoliciesGetRequest',
        response_type_name='ServerTlsPolicy',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworksecurityProjectsLocationsServerTlsPoliciesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}:getIamPolicy',
        http_method='GET',
        method_id='networksecurity.projects.locations.serverTlsPolicies.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsServerTlsPoliciesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ServerTlsPolicies in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsServerTlsPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServerTlsPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies',
        http_method='GET',
        method_id='networksecurity.projects.locations.serverTlsPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/serverTlsPolicies',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsServerTlsPoliciesListRequest',
        response_type_name='ListServerTlsPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single ServerTlsPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsServerTlsPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.serverTlsPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='serverTlsPolicy',
        request_type_name='NetworksecurityProjectsLocationsServerTlsPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworksecurityProjectsLocationsServerTlsPoliciesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}:setIamPolicy',
        http_method='POST',
        method_id='networksecurity.projects.locations.serverTlsPolicies.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='NetworksecurityProjectsLocationsServerTlsPoliciesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworksecurityProjectsLocationsServerTlsPoliciesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serverTlsPolicies/{serverTlsPoliciesId}:testIamPermissions',
        http_method='POST',
        method_id='networksecurity.projects.locations.serverTlsPolicies.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='NetworksecurityProjectsLocationsServerTlsPoliciesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsSseGatewayReferencesService(base_api.BaseApiService):
    """Service class for the projects_locations_sseGatewayReferences resource."""

    _NAME = 'projects_locations_sseGatewayReferences'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsSseGatewayReferencesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets details of a single SSEGatewayReference.

      Args:
        request: (NetworksecurityProjectsLocationsSseGatewayReferencesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SSEGatewayReference) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sseGatewayReferences/{sseGatewayReferencesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.sseGatewayReferences.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSseGatewayReferencesGetRequest',
        response_type_name='SSEGatewayReference',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SSEGatewayReferences in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsSseGatewayReferencesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSSEGatewayReferencesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sseGatewayReferences',
        http_method='GET',
        method_id='networksecurity.projects.locations.sseGatewayReferences.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/sseGatewayReferences',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSseGatewayReferencesListRequest',
        response_type_name='ListSSEGatewayReferencesResponse',
        supports_download=False,
    )

  class ProjectsLocationsSseGatewaysService(base_api.BaseApiService):
    """Service class for the projects_locations_sseGateways resource."""

    _NAME = 'projects_locations_sseGateways'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsSseGatewaysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new SSEGateway in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsSseGatewaysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sseGateways',
        http_method='POST',
        method_id='networksecurity.projects.locations.sseGateways.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'sseGatewayId'],
        relative_path='v1alpha1/{+parent}/sseGateways',
        request_field='sSEGateway',
        request_type_name='NetworksecurityProjectsLocationsSseGatewaysCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single SSEGateway.

      Args:
        request: (NetworksecurityProjectsLocationsSseGatewaysDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sseGateways/{sseGatewaysId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.sseGateways.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSseGatewaysDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single SSEGateway.

      Args:
        request: (NetworksecurityProjectsLocationsSseGatewaysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SSEGateway) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sseGateways/{sseGatewaysId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.sseGateways.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSseGatewaysGetRequest',
        response_type_name='SSEGateway',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SSEGateways in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsSseGatewaysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSSEGatewaysResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sseGateways',
        http_method='GET',
        method_id='networksecurity.projects.locations.sseGateways.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/sseGateways',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSseGatewaysListRequest',
        response_type_name='ListSSEGatewaysResponse',
        supports_download=False,
    )

  class ProjectsLocationsSseRealmsService(base_api.BaseApiService):
    """Service class for the projects_locations_sseRealms resource."""

    _NAME = 'projects_locations_sseRealms'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsSseRealmsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new SSERealm in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsSseRealmsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sseRealms',
        http_method='POST',
        method_id='networksecurity.projects.locations.sseRealms.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['requestId', 'sseRealmId'],
        relative_path='v1alpha1/{+parent}/sseRealms',
        request_field='sSERealm',
        request_type_name='NetworksecurityProjectsLocationsSseRealmsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single SSERealm.

      Args:
        request: (NetworksecurityProjectsLocationsSseRealmsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sseRealms/{sseRealmsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.sseRealms.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSseRealmsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single SSERealm.

      Args:
        request: (NetworksecurityProjectsLocationsSseRealmsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SSERealm) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sseRealms/{sseRealmsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.sseRealms.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSseRealmsGetRequest',
        response_type_name='SSERealm',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SSERealms in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsSseRealmsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSSERealmsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/sseRealms',
        http_method='GET',
        method_id='networksecurity.projects.locations.sseRealms.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/sseRealms',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsSseRealmsListRequest',
        response_type_name='ListSSERealmsResponse',
        supports_download=False,
    )

  class ProjectsLocationsTlsInspectionPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_tlsInspectionPolicies resource."""

    _NAME = 'projects_locations_tlsInspectionPolicies'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsTlsInspectionPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new TlsInspectionPolicy in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsTlsInspectionPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tlsInspectionPolicies',
        http_method='POST',
        method_id='networksecurity.projects.locations.tlsInspectionPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['tlsInspectionPolicyId'],
        relative_path='v1alpha1/{+parent}/tlsInspectionPolicies',
        request_field='tlsInspectionPolicy',
        request_type_name='NetworksecurityProjectsLocationsTlsInspectionPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single TlsInspectionPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsTlsInspectionPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tlsInspectionPolicies/{tlsInspectionPoliciesId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.tlsInspectionPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsTlsInspectionPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single TlsInspectionPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsTlsInspectionPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TlsInspectionPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tlsInspectionPolicies/{tlsInspectionPoliciesId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.tlsInspectionPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsTlsInspectionPoliciesGetRequest',
        response_type_name='TlsInspectionPolicy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TlsInspectionPolicies in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsTlsInspectionPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTlsInspectionPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tlsInspectionPolicies',
        http_method='GET',
        method_id='networksecurity.projects.locations.tlsInspectionPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/tlsInspectionPolicies',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsTlsInspectionPoliciesListRequest',
        response_type_name='ListTlsInspectionPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single TlsInspectionPolicy.

      Args:
        request: (NetworksecurityProjectsLocationsTlsInspectionPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tlsInspectionPolicies/{tlsInspectionPoliciesId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.tlsInspectionPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='tlsInspectionPolicy',
        request_type_name='NetworksecurityProjectsLocationsTlsInspectionPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsUrlListsService(base_api.BaseApiService):
    """Service class for the projects_locations_urlLists resource."""

    _NAME = 'projects_locations_urlLists'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsUrlListsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new UrlList in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsUrlListsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/urlLists',
        http_method='POST',
        method_id='networksecurity.projects.locations.urlLists.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['urlListId'],
        relative_path='v1alpha1/{+parent}/urlLists',
        request_field='urlList',
        request_type_name='NetworksecurityProjectsLocationsUrlListsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single UrlList.

      Args:
        request: (NetworksecurityProjectsLocationsUrlListsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/urlLists/{urlListsId}',
        http_method='DELETE',
        method_id='networksecurity.projects.locations.urlLists.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsUrlListsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single UrlList.

      Args:
        request: (NetworksecurityProjectsLocationsUrlListsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (UrlList) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/urlLists/{urlListsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.urlLists.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsUrlListsGetRequest',
        response_type_name='UrlList',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists UrlLists in a given project and location.

      Args:
        request: (NetworksecurityProjectsLocationsUrlListsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListUrlListsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/urlLists',
        http_method='GET',
        method_id='networksecurity.projects.locations.urlLists.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/urlLists',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsUrlListsListRequest',
        response_type_name='ListUrlListsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single UrlList.

      Args:
        request: (NetworksecurityProjectsLocationsUrlListsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/urlLists/{urlListsId}',
        http_method='PATCH',
        method_id='networksecurity.projects.locations.urlLists.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='urlList',
        request_type_name='NetworksecurityProjectsLocationsUrlListsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (NetworksecurityProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='networksecurity.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (NetworksecurityProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='networksecurity.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/locations',
        request_field='',
        request_type_name='NetworksecurityProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(NetworksecurityV1alpha1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

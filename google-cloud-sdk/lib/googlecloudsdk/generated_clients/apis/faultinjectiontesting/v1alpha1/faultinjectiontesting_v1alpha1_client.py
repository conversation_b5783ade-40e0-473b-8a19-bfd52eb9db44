"""Generated client library for faultinjectiontesting version v1alpha1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.faultinjectiontesting.v1alpha1 import faultinjectiontesting_v1alpha1_messages as messages


class FaultinjectiontestingV1alpha1(base_api.BaseApiClient):
  """Generated client library for service faultinjectiontesting version v1alpha1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://faultinjectiontesting.googleapis.com/'
  MTLS_BASE_URL = 'https://faultinjectiontesting.mtls.googleapis.com/'

  _PACKAGE = 'faultinjectiontesting'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'FaultinjectiontestingV1alpha1'
  _URL_VERSION = 'v1alpha1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new faultinjectiontesting handle."""
    url = url or self.BASE_URL
    super(FaultinjectiontestingV1alpha1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_experiments = self.ProjectsLocationsExperimentsService(self)
    self.projects_locations_faults = self.ProjectsLocationsFaultsService(self)
    self.projects_locations_jobs = self.ProjectsLocationsJobsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsExperimentsService(base_api.BaseApiService):
    """Service class for the projects_locations_experiments resource."""

    _NAME = 'projects_locations_experiments'

    def __init__(self, client):
      super(FaultinjectiontestingV1alpha1.ProjectsLocationsExperimentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Experiment in a given project and location.

      Args:
        request: (FaultinjectiontestingProjectsLocationsExperimentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Experiment) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/experiments',
        http_method='POST',
        method_id='faultinjectiontesting.projects.locations.experiments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['experimentId', 'requestId'],
        relative_path='v1alpha1/{+parent}/experiments',
        request_field='experiment',
        request_type_name='FaultinjectiontestingProjectsLocationsExperimentsCreateRequest',
        response_type_name='Experiment',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Experiment.

      Args:
        request: (FaultinjectiontestingProjectsLocationsExperimentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/experiments/{experimentsId}',
        http_method='DELETE',
        method_id='faultinjectiontesting.projects.locations.experiments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='FaultinjectiontestingProjectsLocationsExperimentsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Experiment.

      Args:
        request: (FaultinjectiontestingProjectsLocationsExperimentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Experiment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/experiments/{experimentsId}',
        http_method='GET',
        method_id='faultinjectiontesting.projects.locations.experiments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='FaultinjectiontestingProjectsLocationsExperimentsGetRequest',
        response_type_name='Experiment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Experiments in a given project and location.

      Args:
        request: (FaultinjectiontestingProjectsLocationsExperimentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListExperimentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/experiments',
        http_method='GET',
        method_id='faultinjectiontesting.projects.locations.experiments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/experiments',
        request_field='',
        request_type_name='FaultinjectiontestingProjectsLocationsExperimentsListRequest',
        response_type_name='ListExperimentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Experiment.

      Args:
        request: (FaultinjectiontestingProjectsLocationsExperimentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Experiment) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/experiments/{experimentsId}',
        http_method='PATCH',
        method_id='faultinjectiontesting.projects.locations.experiments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='experiment',
        request_type_name='FaultinjectiontestingProjectsLocationsExperimentsPatchRequest',
        response_type_name='Experiment',
        supports_download=False,
    )

  class ProjectsLocationsFaultsService(base_api.BaseApiService):
    """Service class for the projects_locations_faults resource."""

    _NAME = 'projects_locations_faults'

    def __init__(self, client):
      super(FaultinjectiontestingV1alpha1.ProjectsLocationsFaultsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Fault in a given project and location.

      Args:
        request: (FaultinjectiontestingProjectsLocationsFaultsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Fault) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/faults',
        http_method='POST',
        method_id='faultinjectiontesting.projects.locations.faults.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['faultId', 'requestId'],
        relative_path='v1alpha1/{+parent}/faults',
        request_field='fault',
        request_type_name='FaultinjectiontestingProjectsLocationsFaultsCreateRequest',
        response_type_name='Fault',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Fault.

      Args:
        request: (FaultinjectiontestingProjectsLocationsFaultsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/faults/{faultsId}',
        http_method='DELETE',
        method_id='faultinjectiontesting.projects.locations.faults.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='FaultinjectiontestingProjectsLocationsFaultsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Fault.

      Args:
        request: (FaultinjectiontestingProjectsLocationsFaultsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Fault) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/faults/{faultsId}',
        http_method='GET',
        method_id='faultinjectiontesting.projects.locations.faults.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='FaultinjectiontestingProjectsLocationsFaultsGetRequest',
        response_type_name='Fault',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Faults in a given project and location.

      Args:
        request: (FaultinjectiontestingProjectsLocationsFaultsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFaultsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/faults',
        http_method='GET',
        method_id='faultinjectiontesting.projects.locations.faults.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/faults',
        request_field='',
        request_type_name='FaultinjectiontestingProjectsLocationsFaultsListRequest',
        response_type_name='ListFaultsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Fault.

      Args:
        request: (FaultinjectiontestingProjectsLocationsFaultsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Fault) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/faults/{faultsId}',
        http_method='PATCH',
        method_id='faultinjectiontesting.projects.locations.faults.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='fault',
        request_type_name='FaultinjectiontestingProjectsLocationsFaultsPatchRequest',
        response_type_name='Fault',
        supports_download=False,
    )

  class ProjectsLocationsJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_jobs resource."""

    _NAME = 'projects_locations_jobs'

    def __init__(self, client):
      super(FaultinjectiontestingV1alpha1.ProjectsLocationsJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Job in a given project and location.

      Args:
        request: (FaultinjectiontestingProjectsLocationsJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Job) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/jobs',
        http_method='POST',
        method_id='faultinjectiontesting.projects.locations.jobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['jobId', 'requestId', 'validateOnly'],
        relative_path='v1alpha1/{+parent}/jobs',
        request_field='job',
        request_type_name='FaultinjectiontestingProjectsLocationsJobsCreateRequest',
        response_type_name='Job',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Job.

      Args:
        request: (FaultinjectiontestingProjectsLocationsJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}',
        http_method='DELETE',
        method_id='faultinjectiontesting.projects.locations.jobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='FaultinjectiontestingProjectsLocationsJobsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Job.

      Args:
        request: (FaultinjectiontestingProjectsLocationsJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Job) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/jobs/{jobsId}',
        http_method='GET',
        method_id='faultinjectiontesting.projects.locations.jobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='FaultinjectiontestingProjectsLocationsJobsGetRequest',
        response_type_name='Job',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Jobs in a given project and location.

      Args:
        request: (FaultinjectiontestingProjectsLocationsJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/jobs',
        http_method='GET',
        method_id='faultinjectiontesting.projects.locations.jobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/jobs',
        request_field='',
        request_type_name='FaultinjectiontestingProjectsLocationsJobsListRequest',
        response_type_name='ListJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(FaultinjectiontestingV1alpha1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (FaultinjectiontestingProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='faultinjectiontesting.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='FaultinjectiontestingProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (FaultinjectiontestingProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='faultinjectiontesting.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='FaultinjectiontestingProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (FaultinjectiontestingProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='faultinjectiontesting.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='FaultinjectiontestingProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (FaultinjectiontestingProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='faultinjectiontesting.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='FaultinjectiontestingProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(FaultinjectiontestingV1alpha1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (FaultinjectiontestingProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='faultinjectiontesting.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='FaultinjectiontestingProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (FaultinjectiontestingProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='faultinjectiontesting.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/locations',
        request_field='',
        request_type_name='FaultinjectiontestingProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(FaultinjectiontestingV1alpha1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

"""Generated message classes for firestore version v1beta1.

Accesses the NoSQL document database built for automatic scaling, high
performance, and ease of application development.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'firestore'


class Aggregation(_messages.Message):
  r"""Defines an aggregation that produces a single result.

  Fields:
    alias: Optional. Optional name of the field to store the result of the
      aggregation into. If not provided, Firestore will pick a default name
      following the format `field_`. For example: ``` AGGREGATE COUNT_UP_TO(1)
      AS count_up_to_1, COUNT_UP_TO(2), COUNT_UP_TO(3) AS count_up_to_3,
      COUNT(*) OVER ( ... ); ``` becomes: ``` AGGREGATE COUNT_UP_TO(1) AS
      count_up_to_1, COUNT_UP_TO(2) AS field_1, COUNT_UP_TO(3) AS
      count_up_to_3, COUNT(*) AS field_2 OVER ( ... ); ``` Requires: * Must be
      unique across all aggregation aliases. * Conform to document field name
      limitations.
    count: Count aggregator.
  """

  alias = _messages.StringField(1)
  count = _messages.MessageField('Count', 2)


class AggregationResult(_messages.Message):
  r"""The result of a single bucket from a Firestore aggregation query. The
  keys of `aggregate_fields` are the same for all results in an aggregation
  query, unlike document queries which can have different fields present for
  each result.

  Messages:
    AggregateFieldsValue: The result of the aggregation functions, ex:
      `COUNT(*) AS total_docs`. The key is the alias assigned to the
      aggregation function on input and the size of this map equals the number
      of aggregation functions in the query.

  Fields:
    aggregateFields: The result of the aggregation functions, ex: `COUNT(*) AS
      total_docs`. The key is the alias assigned to the aggregation function
      on input and the size of this map equals the number of aggregation
      functions in the query.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AggregateFieldsValue(_messages.Message):
    r"""The result of the aggregation functions, ex: `COUNT(*) AS total_docs`.
    The key is the alias assigned to the aggregation function on input and the
    size of this map equals the number of aggregation functions in the query.

    Messages:
      AdditionalProperty: An additional property for a AggregateFieldsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AggregateFieldsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AggregateFieldsValue object.

      Fields:
        key: Name of the additional property.
        value: A Value attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Value', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  aggregateFields = _messages.MessageField('AggregateFieldsValue', 1)


class ArrayValue(_messages.Message):
  r"""An array value.

  Fields:
    values: Values in the array.
  """

  values = _messages.MessageField('Value', 1, repeated=True)


class BatchGetDocumentsRequest(_messages.Message):
  r"""The request for Firestore.BatchGetDocuments.

  Fields:
    documents: The names of the documents to retrieve. In the format:
      `projects/{project_id}/databases/{database_id}/documents/{document_path}
      `. The request will fail if any of the document is not a child resource
      of the given `database`. Duplicate names will be elided.
    mask: The fields to return. If not set, returns all fields. If a document
      has a field that is not present in this mask, that field will not be
      returned in the response.
    newTransaction: Starts a new transaction and reads the documents. Defaults
      to a read-only transaction. The new transaction ID will be returned as
      the first response in the stream.
    readTime: Reads documents as they were at the given time. This may not be
      older than 270 seconds.
    transaction: Reads documents in a transaction.
  """

  documents = _messages.StringField(1, repeated=True)
  mask = _messages.MessageField('DocumentMask', 2)
  newTransaction = _messages.MessageField('TransactionOptions', 3)
  readTime = _messages.StringField(4)
  transaction = _messages.BytesField(5)


class BatchGetDocumentsResponse(_messages.Message):
  r"""The streamed response for Firestore.BatchGetDocuments.

  Fields:
    found: A document that was requested.
    missing: A document name that was requested but does not exist. In the
      format: `projects/{project_id}/databases/{database_id}/documents/{docume
      nt_path}`.
    readTime: The time at which the document was read. This may be monotically
      increasing, in this case the previous documents in the result stream are
      guaranteed not to have changed between their read_time and this one.
    transaction: The transaction that was started as part of this request.
      Will only be set in the first response, and only if
      BatchGetDocumentsRequest.new_transaction was set in the request.
  """

  found = _messages.MessageField('Document', 1)
  missing = _messages.StringField(2)
  readTime = _messages.StringField(3)
  transaction = _messages.BytesField(4)


class BatchWriteRequest(_messages.Message):
  r"""The request for Firestore.BatchWrite.

  Messages:
    LabelsValue: Labels associated with this batch write.

  Fields:
    labels: Labels associated with this batch write.
    writes: The writes to apply. Method does not apply writes atomically and
      does not guarantee ordering. Each write succeeds or fails independently.
      You cannot write to the same document more than once per request.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this batch write.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  writes = _messages.MessageField('Write', 2, repeated=True)


class BatchWriteResponse(_messages.Message):
  r"""The response from Firestore.BatchWrite.

  Fields:
    status: The status of applying the writes. This i-th write status
      corresponds to the i-th write in the request.
    writeResults: The result of applying the writes. This i-th write result
      corresponds to the i-th write in the request.
  """

  status = _messages.MessageField('Status', 1, repeated=True)
  writeResults = _messages.MessageField('WriteResult', 2, repeated=True)


class BeginTransactionRequest(_messages.Message):
  r"""The request for Firestore.BeginTransaction.

  Fields:
    options: The options for the transaction. Defaults to a read-write
      transaction.
  """

  options = _messages.MessageField('TransactionOptions', 1)


class BeginTransactionResponse(_messages.Message):
  r"""The response for Firestore.BeginTransaction.

  Fields:
    transaction: The transaction that was started.
  """

  transaction = _messages.BytesField(1)


class BitSequence(_messages.Message):
  r"""A sequence of bits, encoded in a byte array. Each byte in the `bitmap`
  byte array stores 8 bits of the sequence. The only exception is the last
  byte, which may store 8 _or fewer_ bits. The `padding` defines the number of
  bits of the last byte to be ignored as "padding". The values of these
  "padding" bits are unspecified and must be ignored. To retrieve the first
  bit, bit 0, calculate: `(bitmap[0] & 0x01) != 0`. To retrieve the second
  bit, bit 1, calculate: `(bitmap[0] & 0x02) != 0`. To retrieve the third bit,
  bit 2, calculate: `(bitmap[0] & 0x04) != 0`. To retrieve the fourth bit, bit
  3, calculate: `(bitmap[0] & 0x08) != 0`. To retrieve bit n, calculate:
  `(bitmap[n / 8] & (0x01 << (n % 8))) != 0`. The "size" of a `BitSequence`
  (the number of bits it contains) is calculated by this formula:
  `(bitmap.length * 8) - padding`.

  Fields:
    bitmap: The bytes that encode the bit sequence. May have a length of zero.
    padding: The number of bits of the last byte in `bitmap` to ignore as
      "padding". If the length of `bitmap` is zero, then this value must be
      `0`. Otherwise, this value must be between 0 and 7, inclusive.
  """

  bitmap = _messages.BytesField(1)
  padding = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class BloomFilter(_messages.Message):
  r"""A bloom filter (https://en.wikipedia.org/wiki/Bloom_filter). The bloom
  filter hashes the entries with MD5 and treats the resulting 128-bit hash as
  2 distinct 64-bit hash values, interpreted as unsigned integers using 2's
  complement encoding. These two hash values, named `h1` and `h2`, are then
  used to compute the `hash_count` hash values using the formula, starting at
  `i=0`: h(i) = h1 + (i * h2) These resulting values are then taken modulo the
  number of bits in the bloom filter to get the bits of the bloom filter to
  test for the given entry.

  Fields:
    bits: The bloom filter data.
    hashCount: The number of hashes used by the algorithm.
  """

  bits = _messages.MessageField('BitSequence', 1)
  hashCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class CollectionSelector(_messages.Message):
  r"""A selection of a collection, such as `messages as m1`.

  Fields:
    allDescendants: When false, selects only collections that are immediate
      children of the `parent` specified in the containing `RunQueryRequest`.
      When true, selects all descendant collections.
    collectionId: The collection ID. When set, selects only collections with
      this ID.
  """

  allDescendants = _messages.BooleanField(1)
  collectionId = _messages.StringField(2)


class CommitRequest(_messages.Message):
  r"""The request for Firestore.Commit.

  Fields:
    transaction: If set, applies all writes in this transaction, and commits
      it.
    writes: The writes to apply. Always executed atomically and in order.
  """

  transaction = _messages.BytesField(1)
  writes = _messages.MessageField('Write', 2, repeated=True)


class CommitResponse(_messages.Message):
  r"""The response for Firestore.Commit.

  Fields:
    commitTime: The time at which the commit occurred. Any read with an equal
      or greater `read_time` is guaranteed to see the effects of the commit.
    writeResults: The result of applying the writes. This i-th write result
      corresponds to the i-th write in the request.
  """

  commitTime = _messages.StringField(1)
  writeResults = _messages.MessageField('WriteResult', 2, repeated=True)


class CompositeFilter(_messages.Message):
  r"""A filter that merges multiple other filters using the given operator.

  Enums:
    OpValueValuesEnum: The operator for combining multiple filters.

  Fields:
    filters: The list of filters to combine. Requires: * At least one filter
      is present.
    op: The operator for combining multiple filters.
  """

  class OpValueValuesEnum(_messages.Enum):
    r"""The operator for combining multiple filters.

    Values:
      OPERATOR_UNSPECIFIED: Unspecified. This value must not be used.
      AND: Documents are required to satisfy all of the combined filters.
      OR: Documents are required to satisfy at least one of the combined
        filters.
    """
    OPERATOR_UNSPECIFIED = 0
    AND = 1
    OR = 2

  filters = _messages.MessageField('Filter', 1, repeated=True)
  op = _messages.EnumField('OpValueValuesEnum', 2)


class Count(_messages.Message):
  r"""Count of documents that match the query. The `COUNT(*)` aggregation
  function operates on the entire document so it does not require a field
  reference.

  Fields:
    upTo: Optional. Optional constraint on the maximum number of documents to
      count. This provides a way to set an upper bound on the number of
      documents to scan, limiting latency, and cost. Unspecified is
      interpreted as no bound. High-Level Example: ``` AGGREGATE
      COUNT_UP_TO(1000) OVER ( SELECT * FROM k ); ``` Requires: * Must be
      greater than zero when present.
  """

  upTo = _messages.IntegerField(1)


class Cursor(_messages.Message):
  r"""A position in a query result set.

  Fields:
    before: If the position is just before or just after the given values,
      relative to the sort order defined by the query.
    values: The values that represent a position, in the order they appear in
      the order by clause of a query. Can contain fewer values than specified
      in the order by clause.
  """

  before = _messages.BooleanField(1)
  values = _messages.MessageField('Value', 2, repeated=True)


class Document(_messages.Message):
  r"""A Firestore document. Must not exceed 1 MiB - 4 bytes.

  Messages:
    FieldsValue: The document's fields. The map keys represent field names. A
      simple field name contains only characters `a` to `z`, `A` to `Z`, `0`
      to `9`, or `_`, and must not start with `0` to `9`. For example,
      `foo_bar_17`. Field names matching the regular expression `__.*__` are
      reserved. Reserved field names are forbidden except in certain
      documented contexts. The map keys, represented as UTF-8, must not exceed
      1,500 bytes and cannot be empty. Field paths may be used in other
      contexts to refer to structured fields defined here. For `map_value`,
      the field path is represented by the simple or quoted field names of the
      containing fields, delimited by `.`. For example, the structured field
      `"foo" : { map_value: { "x&y" : { string_value: "hello" }}}` would be
      represented by the field path `foo.x&y`. Within a field path, a quoted
      field name starts and ends with `` ` `` and may contain any character.
      Some characters, including `` ` ``, must be escaped using a `\`. For
      example, `` `x&y` `` represents `x&y` and `` `bak\`tik` `` represents ``
      bak`tik ``.

  Fields:
    createTime: Output only. The time at which the document was created. This
      value increases monotonically when a document is deleted then recreated.
      It can also be compared to values from other documents and the
      `read_time` of a query.
    fields: The document's fields. The map keys represent field names. A
      simple field name contains only characters `a` to `z`, `A` to `Z`, `0`
      to `9`, or `_`, and must not start with `0` to `9`. For example,
      `foo_bar_17`. Field names matching the regular expression `__.*__` are
      reserved. Reserved field names are forbidden except in certain
      documented contexts. The map keys, represented as UTF-8, must not exceed
      1,500 bytes and cannot be empty. Field paths may be used in other
      contexts to refer to structured fields defined here. For `map_value`,
      the field path is represented by the simple or quoted field names of the
      containing fields, delimited by `.`. For example, the structured field
      `"foo" : { map_value: { "x&y" : { string_value: "hello" }}}` would be
      represented by the field path `foo.x&y`. Within a field path, a quoted
      field name starts and ends with `` ` `` and may contain any character.
      Some characters, including `` ` ``, must be escaped using a `\`. For
      example, `` `x&y` `` represents `x&y` and `` `bak\`tik` `` represents ``
      bak`tik ``.
    name: The resource name of the document, for example
      `projects/{project_id}/databases/{database_id}/documents/{document_path}
      `.
    updateTime: Output only. The time at which the document was last changed.
      This value is initially set to the `create_time` then increases
      monotonically with each change to the document. It can also be compared
      to values from other documents and the `read_time` of a query.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FieldsValue(_messages.Message):
    r"""The document's fields. The map keys represent field names. A simple
    field name contains only characters `a` to `z`, `A` to `Z`, `0` to `9`, or
    `_`, and must not start with `0` to `9`. For example, `foo_bar_17`. Field
    names matching the regular expression `__.*__` are reserved. Reserved
    field names are forbidden except in certain documented contexts. The map
    keys, represented as UTF-8, must not exceed 1,500 bytes and cannot be
    empty. Field paths may be used in other contexts to refer to structured
    fields defined here. For `map_value`, the field path is represented by the
    simple or quoted field names of the containing fields, delimited by `.`.
    For example, the structured field `"foo" : { map_value: { "x&y" : {
    string_value: "hello" }}}` would be represented by the field path
    `foo.x&y`. Within a field path, a quoted field name starts and ends with
    `` ` `` and may contain any character. Some characters, including `` ` ``,
    must be escaped using a `\`. For example, `` `x&y` `` represents `x&y` and
    `` `bak\`tik` `` represents `` bak`tik ``.

    Messages:
      AdditionalProperty: An additional property for a FieldsValue object.

    Fields:
      additionalProperties: Additional properties of type FieldsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FieldsValue object.

      Fields:
        key: Name of the additional property.
        value: A Value attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Value', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  fields = _messages.MessageField('FieldsValue', 2)
  name = _messages.StringField(3)
  updateTime = _messages.StringField(4)


class DocumentChange(_messages.Message):
  r"""A Document has changed. May be the result of multiple writes, including
  deletes, that ultimately resulted in a new value for the Document. Multiple
  DocumentChange messages may be returned for the same logical change, if
  multiple targets are affected.

  Fields:
    document: The new state of the Document. If `mask` is set, contains only
      fields that were updated or added.
    removedTargetIds: A set of target IDs for targets that no longer match
      this document.
    targetIds: A set of target IDs of targets that match this document.
  """

  document = _messages.MessageField('Document', 1)
  removedTargetIds = _messages.IntegerField(2, repeated=True, variant=_messages.Variant.INT32)
  targetIds = _messages.IntegerField(3, repeated=True, variant=_messages.Variant.INT32)


class DocumentDelete(_messages.Message):
  r"""A Document has been deleted. May be the result of multiple writes,
  including updates, the last of which deleted the Document. Multiple
  DocumentDelete messages may be returned for the same logical delete, if
  multiple targets are affected.

  Fields:
    document: The resource name of the Document that was deleted.
    readTime: The read timestamp at which the delete was observed. Greater or
      equal to the `commit_time` of the delete.
    removedTargetIds: A set of target IDs for targets that previously matched
      this entity.
  """

  document = _messages.StringField(1)
  readTime = _messages.StringField(2)
  removedTargetIds = _messages.IntegerField(3, repeated=True, variant=_messages.Variant.INT32)


class DocumentMask(_messages.Message):
  r"""A set of field paths on a document. Used to restrict a get or update
  operation on a document to a subset of its fields. This is different from
  standard field masks, as this is always scoped to a Document, and takes in
  account the dynamic nature of Value.

  Fields:
    fieldPaths: The list of field paths in the mask. See Document.fields for a
      field path syntax reference.
  """

  fieldPaths = _messages.StringField(1, repeated=True)


class DocumentRemove(_messages.Message):
  r"""A Document has been removed from the view of the targets. Sent if the
  document is no longer relevant to a target and is out of view. Can be sent
  instead of a DocumentDelete or a DocumentChange if the server can not send
  the new value of the document. Multiple DocumentRemove messages may be
  returned for the same logical write or delete, if multiple targets are
  affected.

  Fields:
    document: The resource name of the Document that has gone out of view.
    readTime: The read timestamp at which the remove was observed. Greater or
      equal to the `commit_time` of the change/delete/remove.
    removedTargetIds: A set of target IDs for targets that previously matched
      this document.
  """

  document = _messages.StringField(1)
  readTime = _messages.StringField(2)
  removedTargetIds = _messages.IntegerField(3, repeated=True, variant=_messages.Variant.INT32)


class DocumentTransform(_messages.Message):
  r"""A transformation of a document.

  Fields:
    document: The name of the document to transform.
    fieldTransforms: The list of transformations to apply to the fields of the
      document, in order. This must not be empty.
  """

  document = _messages.StringField(1)
  fieldTransforms = _messages.MessageField('FieldTransform', 2, repeated=True)


class DocumentsTarget(_messages.Message):
  r"""A target specified by a set of documents names.

  Fields:
    documents: The names of the documents to retrieve. In the format:
      `projects/{project_id}/databases/{database_id}/documents/{document_path}
      `. The request will fail if any of the document is not a child resource
      of the given `database`. Duplicate names will be elided.
  """

  documents = _messages.StringField(1, repeated=True)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ExistenceFilter(_messages.Message):
  r"""A digest of all the documents that match a given target.

  Fields:
    count: The total count of documents that match target_id. If different
      from the count of documents in the client that match, the client must
      manually determine which documents no longer match the target. The
      client can use the `unchanged_names` bloom filter to assist with this
      determination.
    targetId: The target ID to which this filter applies.
    unchangedNames: A bloom filter that contains the UTF-8 byte encodings of
      the resource names of the documents that match target_id, in the form `p
      rojects/{project_id}/databases/{database_id}/documents/{document_path}`
      that have NOT changed since the query results indicated by the resume
      token or timestamp given in `Target.resume_type`. This bloom filter may
      be omitted at the server's discretion, such as if it is deemed that the
      client will not make use of it or if it is too computationally expensive
      to calculate or transmit. Clients must gracefully handle this field
      being absent by falling back to the logic used before this field
      existed; that is, re-add the target without a resume token to figure out
      which documents in the client's cache are out of sync.
  """

  count = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  targetId = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  unchangedNames = _messages.MessageField('BloomFilter', 3)


class FieldFilter(_messages.Message):
  r"""A filter on a specific field.

  Enums:
    OpValueValuesEnum: The operator to filter by.

  Fields:
    field: The field to filter by.
    op: The operator to filter by.
    value: The value to compare to.
  """

  class OpValueValuesEnum(_messages.Enum):
    r"""The operator to filter by.

    Values:
      OPERATOR_UNSPECIFIED: Unspecified. This value must not be used.
      LESS_THAN: The given `field` is less than the given `value`. Requires: *
        That `field` come first in `order_by`.
      LESS_THAN_OR_EQUAL: The given `field` is less than or equal to the given
        `value`. Requires: * That `field` come first in `order_by`.
      GREATER_THAN: The given `field` is greater than the given `value`.
        Requires: * That `field` come first in `order_by`.
      GREATER_THAN_OR_EQUAL: The given `field` is greater than or equal to the
        given `value`. Requires: * That `field` come first in `order_by`.
      EQUAL: The given `field` is equal to the given `value`.
      NOT_EQUAL: The given `field` is not equal to the given `value`.
        Requires: * No other `NOT_EQUAL`, `NOT_IN`, `IS_NOT_NULL`, or
        `IS_NOT_NAN`. * That `field` comes first in the `order_by`.
      ARRAY_CONTAINS: The given `field` is an array that contains the given
        `value`.
      IN: The given `field` is equal to at least one value in the given array.
        Requires: * That `value` is a non-empty `ArrayValue`, subject to
        disjunction limits. * No `NOT_IN` filters in the same query.
      ARRAY_CONTAINS_ANY: The given `field` is an array that contains any of
        the values in the given array. Requires: * That `value` is a non-empty
        `ArrayValue`, subject to disjunction limits. * No other
        `ARRAY_CONTAINS_ANY` filters within the same disjunction. * No
        `NOT_IN` filters in the same query.
      NOT_IN: The value of the `field` is not in the given array. Requires: *
        That `value` is a non-empty `ArrayValue` with at most 10 values. * No
        other `OR`, `IN`, `ARRAY_CONTAINS_ANY`, `NOT_IN`, `NOT_EQUAL`,
        `IS_NOT_NULL`, or `IS_NOT_NAN`. * That `field` comes first in the
        `order_by`.
    """
    OPERATOR_UNSPECIFIED = 0
    LESS_THAN = 1
    LESS_THAN_OR_EQUAL = 2
    GREATER_THAN = 3
    GREATER_THAN_OR_EQUAL = 4
    EQUAL = 5
    NOT_EQUAL = 6
    ARRAY_CONTAINS = 7
    IN = 8
    ARRAY_CONTAINS_ANY = 9
    NOT_IN = 10

  field = _messages.MessageField('FieldReference', 1)
  op = _messages.EnumField('OpValueValuesEnum', 2)
  value = _messages.MessageField('Value', 3)


class FieldReference(_messages.Message):
  r"""A reference to a field in a document, ex: `stats.operations`.

  Fields:
    fieldPath: The relative path of the document being referenced. Requires: *
      Conform to document field name limitations.
  """

  fieldPath = _messages.StringField(1)


class FieldTransform(_messages.Message):
  r"""A transformation of a field of the document.

  Enums:
    SetToServerValueValueValuesEnum: Sets the field to the given server value.

  Fields:
    appendMissingElements: Append the given elements in order if they are not
      already present in the current field value. If the field is not an
      array, or if the field does not yet exist, it is first set to the empty
      array. Equivalent numbers of different types (e.g. 3L and 3.0) are
      considered equal when checking if a value is missing. NaN is equal to
      NaN, and Null is equal to Null. If the input contains multiple
      equivalent values, only the first will be considered. The corresponding
      transform_result will be the null value.
    fieldPath: The path of the field. See Document.fields for the field path
      syntax reference.
    increment: Adds the given value to the field's current value. This must be
      an integer or a double value. If the field is not an integer or double,
      or if the field does not yet exist, the transformation will set the
      field to the given value. If either of the given value or the current
      field value are doubles, both values will be interpreted as doubles.
      Double arithmetic and representation of double values follow IEEE 754
      semantics. If there is positive/negative integer overflow, the field is
      resolved to the largest magnitude positive/negative integer.
    maximum: Sets the field to the maximum of its current value and the given
      value. This must be an integer or a double value. If the field is not an
      integer or double, or if the field does not yet exist, the
      transformation will set the field to the given value. If a maximum
      operation is applied where the field and the input value are of mixed
      types (that is - one is an integer and one is a double) the field takes
      on the type of the larger operand. If the operands are equivalent (e.g.
      3 and 3.0), the field does not change. 0, 0.0, and -0.0 are all zero.
      The maximum of a zero stored value and zero input value is always the
      stored value. The maximum of any numeric value x and NaN is NaN.
    minimum: Sets the field to the minimum of its current value and the given
      value. This must be an integer or a double value. If the field is not an
      integer or double, or if the field does not yet exist, the
      transformation will set the field to the input value. If a minimum
      operation is applied where the field and the input value are of mixed
      types (that is - one is an integer and one is a double) the field takes
      on the type of the smaller operand. If the operands are equivalent (e.g.
      3 and 3.0), the field does not change. 0, 0.0, and -0.0 are all zero.
      The minimum of a zero stored value and zero input value is always the
      stored value. The minimum of any numeric value x and NaN is NaN.
    removeAllFromArray: Remove all of the given elements from the array in the
      field. If the field is not an array, or if the field does not yet exist,
      it is set to the empty array. Equivalent numbers of the different types
      (e.g. 3L and 3.0) are considered equal when deciding whether an element
      should be removed. NaN is equal to NaN, and Null is equal to Null. This
      will remove all equivalent values if there are duplicates. The
      corresponding transform_result will be the null value.
    setToServerValue: Sets the field to the given server value.
  """

  class SetToServerValueValueValuesEnum(_messages.Enum):
    r"""Sets the field to the given server value.

    Values:
      SERVER_VALUE_UNSPECIFIED: Unspecified. This value must not be used.
      REQUEST_TIME: The time at which the server processed the request, with
        millisecond precision. If used on multiple fields (same or different
        documents) in a transaction, all the fields will get the same server
        timestamp.
    """
    SERVER_VALUE_UNSPECIFIED = 0
    REQUEST_TIME = 1

  appendMissingElements = _messages.MessageField('ArrayValue', 1)
  fieldPath = _messages.StringField(2)
  increment = _messages.MessageField('Value', 3)
  maximum = _messages.MessageField('Value', 4)
  minimum = _messages.MessageField('Value', 5)
  removeAllFromArray = _messages.MessageField('ArrayValue', 6)
  setToServerValue = _messages.EnumField('SetToServerValueValueValuesEnum', 7)


class Filter(_messages.Message):
  r"""A filter.

  Fields:
    compositeFilter: A composite filter.
    fieldFilter: A filter on a document field.
    unaryFilter: A filter that takes exactly one argument.
  """

  compositeFilter = _messages.MessageField('CompositeFilter', 1)
  fieldFilter = _messages.MessageField('FieldFilter', 2)
  unaryFilter = _messages.MessageField('UnaryFilter', 3)


class FirestoreProjectsDatabasesDocumentsBatchGetRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsBatchGetRequest object.

  Fields:
    batchGetDocumentsRequest: A BatchGetDocumentsRequest resource to be passed
      as the request body.
    database: Required. The database name. In the format:
      `projects/{project_id}/databases/{database_id}`.
  """

  batchGetDocumentsRequest = _messages.MessageField('BatchGetDocumentsRequest', 1)
  database = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesDocumentsBatchWriteRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsBatchWriteRequest object.

  Fields:
    batchWriteRequest: A BatchWriteRequest resource to be passed as the
      request body.
    database: Required. The database name. In the format:
      `projects/{project_id}/databases/{database_id}`.
  """

  batchWriteRequest = _messages.MessageField('BatchWriteRequest', 1)
  database = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesDocumentsBeginTransactionRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsBeginTransactionRequest object.

  Fields:
    beginTransactionRequest: A BeginTransactionRequest resource to be passed
      as the request body.
    database: Required. The database name. In the format:
      `projects/{project_id}/databases/{database_id}`.
  """

  beginTransactionRequest = _messages.MessageField('BeginTransactionRequest', 1)
  database = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesDocumentsCommitRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsCommitRequest object.

  Fields:
    commitRequest: A CommitRequest resource to be passed as the request body.
    database: Required. The database name. In the format:
      `projects/{project_id}/databases/{database_id}`.
  """

  commitRequest = _messages.MessageField('CommitRequest', 1)
  database = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesDocumentsCreateDocumentRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsCreateDocumentRequest object.

  Fields:
    collectionId: Required. The collection ID, relative to `parent`, to list.
      For example: `chatrooms`.
    document: A Document resource to be passed as the request body.
    documentId: The client-assigned document ID to use for this document.
      Optional. If not specified, an ID will be assigned by the service.
    mask_fieldPaths: The list of field paths in the mask. See Document.fields
      for a field path syntax reference.
    parent: Required. The parent resource. For example:
      `projects/{project_id}/databases/{database_id}/documents` or `projects/{
      project_id}/databases/{database_id}/documents/chatrooms/{chatroom_id}`
  """

  collectionId = _messages.StringField(1, required=True)
  document = _messages.MessageField('Document', 2)
  documentId = _messages.StringField(3)
  mask_fieldPaths = _messages.StringField(4, repeated=True)
  parent = _messages.StringField(5, required=True)


class FirestoreProjectsDatabasesDocumentsDeleteRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsDeleteRequest object.

  Fields:
    currentDocument_exists: When set to `true`, the target document must
      exist. When set to `false`, the target document must not exist.
    currentDocument_updateTime: When set, the target document must exist and
      have been last updated at that time. Timestamp must be microsecond
      aligned.
    name: Required. The resource name of the Document to delete. In the
      format: `projects/{project_id}/databases/{database_id}/documents/{docume
      nt_path}`.
  """

  currentDocument_exists = _messages.BooleanField(1)
  currentDocument_updateTime = _messages.StringField(2)
  name = _messages.StringField(3, required=True)


class FirestoreProjectsDatabasesDocumentsGetRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsGetRequest object.

  Fields:
    mask_fieldPaths: The list of field paths in the mask. See Document.fields
      for a field path syntax reference.
    name: Required. The resource name of the Document to get. In the format:
      `projects/{project_id}/databases/{database_id}/documents/{document_path}
      `.
    readTime: Reads the version of the document at the given time. This may
      not be older than 270 seconds.
    transaction: Reads the document in a transaction.
  """

  mask_fieldPaths = _messages.StringField(1, repeated=True)
  name = _messages.StringField(2, required=True)
  readTime = _messages.StringField(3)
  transaction = _messages.BytesField(4)


class FirestoreProjectsDatabasesDocumentsListCollectionIdsRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsListCollectionIdsRequest object.

  Fields:
    listCollectionIdsRequest: A ListCollectionIdsRequest resource to be passed
      as the request body.
    parent: Required. The parent document. In the format:
      `projects/{project_id}/databases/{database_id}/documents/{document_path}
      `. For example: `projects/my-project/databases/my-
      database/documents/chatrooms/my-chatroom`
  """

  listCollectionIdsRequest = _messages.MessageField('ListCollectionIdsRequest', 1)
  parent = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesDocumentsListDocumentsRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsListDocumentsRequest object.

  Fields:
    collectionId: Optional. The collection ID, relative to `parent`, to list.
      For example: `chatrooms` or `messages`. This is optional, and when not
      provided, Firestore will list documents from all collections under the
      provided `parent`.
    mask_fieldPaths: The list of field paths in the mask. See Document.fields
      for a field path syntax reference.
    orderBy: Optional. The optional ordering of the documents to return. For
      example: `priority desc, __name__ desc`. This mirrors the `ORDER BY`
      used in Firestore queries but in a string representation. When absent,
      documents are ordered based on `__name__ ASC`.
    pageSize: Optional. The maximum number of documents to return in a single
      response. Firestore may return fewer than this value.
    pageToken: Optional. A page token, received from a previous
      `ListDocuments` response. Provide this to retrieve the subsequent page.
      When paginating, all other parameters (with the exception of
      `page_size`) must match the values set in the request that generated the
      page token.
    parent: Required. The parent resource name. In the format:
      `projects/{project_id}/databases/{database_id}/documents` or `projects/{
      project_id}/databases/{database_id}/documents/{document_path}`. For
      example: `projects/my-project/databases/my-database/documents` or
      `projects/my-project/databases/my-database/documents/chatrooms/my-
      chatroom`
    readTime: Perform the read at the provided time. This may not be older
      than 270 seconds.
    showMissing: If the list should show missing documents. A document is
      missing if it does not exist, but there are sub-documents nested
      underneath it. When true, such missing documents will be returned with a
      key but will not have fields, `create_time`, or `update_time` set.
      Requests with `show_missing` may not specify `where` or `order_by`.
    transaction: Perform the read as part of an already active transaction.
  """

  collectionId = _messages.StringField(1, required=True)
  mask_fieldPaths = _messages.StringField(2, repeated=True)
  orderBy = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  parent = _messages.StringField(6, required=True)
  readTime = _messages.StringField(7)
  showMissing = _messages.BooleanField(8)
  transaction = _messages.BytesField(9)


class FirestoreProjectsDatabasesDocumentsListRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsListRequest object.

  Fields:
    collectionId: Optional. The collection ID, relative to `parent`, to list.
      For example: `chatrooms` or `messages`. This is optional, and when not
      provided, Firestore will list documents from all collections under the
      provided `parent`.
    mask_fieldPaths: The list of field paths in the mask. See Document.fields
      for a field path syntax reference.
    orderBy: Optional. The optional ordering of the documents to return. For
      example: `priority desc, __name__ desc`. This mirrors the `ORDER BY`
      used in Firestore queries but in a string representation. When absent,
      documents are ordered based on `__name__ ASC`.
    pageSize: Optional. The maximum number of documents to return in a single
      response. Firestore may return fewer than this value.
    pageToken: Optional. A page token, received from a previous
      `ListDocuments` response. Provide this to retrieve the subsequent page.
      When paginating, all other parameters (with the exception of
      `page_size`) must match the values set in the request that generated the
      page token.
    parent: Required. The parent resource name. In the format:
      `projects/{project_id}/databases/{database_id}/documents` or `projects/{
      project_id}/databases/{database_id}/documents/{document_path}`. For
      example: `projects/my-project/databases/my-database/documents` or
      `projects/my-project/databases/my-database/documents/chatrooms/my-
      chatroom`
    readTime: Perform the read at the provided time. This may not be older
      than 270 seconds.
    showMissing: If the list should show missing documents. A document is
      missing if it does not exist, but there are sub-documents nested
      underneath it. When true, such missing documents will be returned with a
      key but will not have fields, `create_time`, or `update_time` set.
      Requests with `show_missing` may not specify `where` or `order_by`.
    transaction: Perform the read as part of an already active transaction.
  """

  collectionId = _messages.StringField(1, required=True)
  mask_fieldPaths = _messages.StringField(2, repeated=True)
  orderBy = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  parent = _messages.StringField(6, required=True)
  readTime = _messages.StringField(7)
  showMissing = _messages.BooleanField(8)
  transaction = _messages.BytesField(9)


class FirestoreProjectsDatabasesDocumentsListenRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsListenRequest object.

  Fields:
    database: Required. The database name. In the format:
      `projects/{project_id}/databases/{database_id}`.
    listenRequest: A ListenRequest resource to be passed as the request body.
  """

  database = _messages.StringField(1, required=True)
  listenRequest = _messages.MessageField('ListenRequest', 2)


class FirestoreProjectsDatabasesDocumentsPartitionQueryRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsPartitionQueryRequest object.

  Fields:
    parent: Required. The parent resource name. In the format:
      `projects/{project_id}/databases/{database_id}/documents`. Document
      resource names are not supported; only database resource names can be
      specified.
    partitionQueryRequest: A PartitionQueryRequest resource to be passed as
      the request body.
  """

  parent = _messages.StringField(1, required=True)
  partitionQueryRequest = _messages.MessageField('PartitionQueryRequest', 2)


class FirestoreProjectsDatabasesDocumentsPatchRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsPatchRequest object.

  Fields:
    currentDocument_exists: When set to `true`, the target document must
      exist. When set to `false`, the target document must not exist.
    currentDocument_updateTime: When set, the target document must exist and
      have been last updated at that time. Timestamp must be microsecond
      aligned.
    document: A Document resource to be passed as the request body.
    mask_fieldPaths: The list of field paths in the mask. See Document.fields
      for a field path syntax reference.
    name: The resource name of the document, for example
      `projects/{project_id}/databases/{database_id}/documents/{document_path}
      `.
    updateMask_fieldPaths: The list of field paths in the mask. See
      Document.fields for a field path syntax reference.
  """

  currentDocument_exists = _messages.BooleanField(1)
  currentDocument_updateTime = _messages.StringField(2)
  document = _messages.MessageField('Document', 3)
  mask_fieldPaths = _messages.StringField(4, repeated=True)
  name = _messages.StringField(5, required=True)
  updateMask_fieldPaths = _messages.StringField(6, repeated=True)


class FirestoreProjectsDatabasesDocumentsRollbackRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsRollbackRequest object.

  Fields:
    database: Required. The database name. In the format:
      `projects/{project_id}/databases/{database_id}`.
    rollbackRequest: A RollbackRequest resource to be passed as the request
      body.
  """

  database = _messages.StringField(1, required=True)
  rollbackRequest = _messages.MessageField('RollbackRequest', 2)


class FirestoreProjectsDatabasesDocumentsRunAggregationQueryRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsRunAggregationQueryRequest object.

  Fields:
    parent: Required. The parent resource name. In the format:
      `projects/{project_id}/databases/{database_id}/documents` or `projects/{
      project_id}/databases/{database_id}/documents/{document_path}`. For
      example: `projects/my-project/databases/my-database/documents` or
      `projects/my-project/databases/my-database/documents/chatrooms/my-
      chatroom`
    runAggregationQueryRequest: A RunAggregationQueryRequest resource to be
      passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  runAggregationQueryRequest = _messages.MessageField('RunAggregationQueryRequest', 2)


class FirestoreProjectsDatabasesDocumentsRunQueryRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsRunQueryRequest object.

  Fields:
    parent: Required. The parent resource name. In the format:
      `projects/{project_id}/databases/{database_id}/documents` or `projects/{
      project_id}/databases/{database_id}/documents/{document_path}`. For
      example: `projects/my-project/databases/my-database/documents` or
      `projects/my-project/databases/my-database/documents/chatrooms/my-
      chatroom`
    runQueryRequest: A RunQueryRequest resource to be passed as the request
      body.
  """

  parent = _messages.StringField(1, required=True)
  runQueryRequest = _messages.MessageField('RunQueryRequest', 2)


class FirestoreProjectsDatabasesDocumentsWriteRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesDocumentsWriteRequest object.

  Fields:
    database: Required. The database name. In the format:
      `projects/{project_id}/databases/{database_id}`. This is only required
      in the first message.
    writeRequest: A WriteRequest resource to be passed as the request body.
  """

  database = _messages.StringField(1, required=True)
  writeRequest = _messages.MessageField('WriteRequest', 2)


class FirestoreProjectsDatabasesExportDocumentsRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesExportDocumentsRequest object.

  Fields:
    googleFirestoreAdminV1beta1ExportDocumentsRequest: A
      GoogleFirestoreAdminV1beta1ExportDocumentsRequest resource to be passed
      as the request body.
    name: Database to export. Should be of the form:
      `projects/{project_id}/databases/{database_id}`.
  """

  googleFirestoreAdminV1beta1ExportDocumentsRequest = _messages.MessageField('GoogleFirestoreAdminV1beta1ExportDocumentsRequest', 1)
  name = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesImportDocumentsRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesImportDocumentsRequest object.

  Fields:
    googleFirestoreAdminV1beta1ImportDocumentsRequest: A
      GoogleFirestoreAdminV1beta1ImportDocumentsRequest resource to be passed
      as the request body.
    name: Database to import into. Should be of the form:
      `projects/{project_id}/databases/{database_id}`.
  """

  googleFirestoreAdminV1beta1ImportDocumentsRequest = _messages.MessageField('GoogleFirestoreAdminV1beta1ImportDocumentsRequest', 1)
  name = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesIndexesCreateRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesIndexesCreateRequest object.

  Fields:
    googleFirestoreAdminV1beta1Index: A GoogleFirestoreAdminV1beta1Index
      resource to be passed as the request body.
    parent: The name of the database this index will apply to. For example:
      `projects/{project_id}/databases/{database_id}`
  """

  googleFirestoreAdminV1beta1Index = _messages.MessageField('GoogleFirestoreAdminV1beta1Index', 1)
  parent = _messages.StringField(2, required=True)


class FirestoreProjectsDatabasesIndexesDeleteRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesIndexesDeleteRequest object.

  Fields:
    name: The index name. For example:
      `projects/{project_id}/databases/{database_id}/indexes/{index_id}`
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsDatabasesIndexesGetRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesIndexesGetRequest object.

  Fields:
    name: The name of the index. For example:
      `projects/{project_id}/databases/{database_id}/indexes/{index_id}`
  """

  name = _messages.StringField(1, required=True)


class FirestoreProjectsDatabasesIndexesListRequest(_messages.Message):
  r"""A FirestoreProjectsDatabasesIndexesListRequest object.

  Fields:
    filter: A string attribute.
    pageSize: The standard List page size.
    pageToken: The standard List page token.
    parent: The database name. For example:
      `projects/{project_id}/databases/{database_id}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class GoogleFirestoreAdminV1RestoreDatabaseMetadata(_messages.Message):
  r"""Metadata for the long-running operation from the RestoreDatabase
  request.

  Enums:
    OperationStateValueValuesEnum: The operation state of the restore.

  Fields:
    backup: The name of the backup restoring from.
    database: The name of the database being restored to.
    endTime: The time the restore finished, unset for ongoing restores.
    operationState: The operation state of the restore.
    startTime: The time the restore was started.
  """

  class OperationStateValueValuesEnum(_messages.Enum):
    r"""The operation state of the restore.

    Values:
      OPERATION_STATE_UNSPECIFIED: Unspecified.
      INITIALIZING: Request is being prepared for processing.
      PROCESSING: Request is actively being processed.
      CANCELLING: Request is in the process of being cancelled after user
        called google.longrunning.Operations.CancelOperation on the operation.
      FINALIZING: Request has been processed and is in its finalization stage.
      SUCCESSFUL: Request has completed successfully.
      FAILED: Request has finished being processed, but encountered an error.
      CANCELLED: Request has finished being cancelled after user called
        google.longrunning.Operations.CancelOperation.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    PROCESSING = 2
    CANCELLING = 3
    FINALIZING = 4
    SUCCESSFUL = 5
    FAILED = 6
    CANCELLED = 7

  backup = _messages.StringField(1)
  database = _messages.StringField(2)
  endTime = _messages.StringField(3)
  operationState = _messages.EnumField('OperationStateValueValuesEnum', 4)
  startTime = _messages.StringField(5)


class GoogleFirestoreAdminV1UpdateDatabaseMetadata(_messages.Message):
  r"""Metadata related to the update database operation."""


class GoogleFirestoreAdminV1beta1ExportDocumentsMetadata(_messages.Message):
  r"""Metadata for ExportDocuments operations.

  Enums:
    OperationStateValueValuesEnum: The state of the export operation.

  Fields:
    collectionIds: Which collection ids are being exported.
    endTime: The time the operation ended, either successfully or otherwise.
      Unset if the operation is still active.
    operationState: The state of the export operation.
    outputUriPrefix: Where the entities are being exported to.
    progressBytes: An estimate of the number of bytes processed.
    progressDocuments: An estimate of the number of documents processed.
    startTime: The time that work began on the operation.
  """

  class OperationStateValueValuesEnum(_messages.Enum):
    r"""The state of the export operation.

    Values:
      STATE_UNSPECIFIED: Unspecified.
      INITIALIZING: Request is being prepared for processing.
      PROCESSING: Request is actively being processed.
      CANCELLING: Request is in the process of being cancelled after user
        called google.longrunning.Operations.CancelOperation on the operation.
      FINALIZING: Request has been processed and is in its finalization stage.
      SUCCESSFUL: Request has completed successfully.
      FAILED: Request has finished being processed, but encountered an error.
      CANCELLED: Request has finished being cancelled after user called
        google.longrunning.Operations.CancelOperation.
    """
    STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    PROCESSING = 2
    CANCELLING = 3
    FINALIZING = 4
    SUCCESSFUL = 5
    FAILED = 6
    CANCELLED = 7

  collectionIds = _messages.StringField(1, repeated=True)
  endTime = _messages.StringField(2)
  operationState = _messages.EnumField('OperationStateValueValuesEnum', 3)
  outputUriPrefix = _messages.StringField(4)
  progressBytes = _messages.MessageField('GoogleFirestoreAdminV1beta1Progress', 5)
  progressDocuments = _messages.MessageField('GoogleFirestoreAdminV1beta1Progress', 6)
  startTime = _messages.StringField(7)


class GoogleFirestoreAdminV1beta1ExportDocumentsRequest(_messages.Message):
  r"""The request for FirestoreAdmin.ExportDocuments.

  Fields:
    collectionIds: Which collection ids to export. Unspecified means all
      collections.
    outputUriPrefix: The output URI. Currently only supports Google Cloud
      Storage URIs of the form: `gs://BUCKET_NAME[/NAMESPACE_PATH]`, where
      `BUCKET_NAME` is the name of the Google Cloud Storage bucket and
      `NAMESPACE_PATH` is an optional Google Cloud Storage namespace path.
      When choosing a name, be sure to consider Google Cloud Storage naming
      guidelines: https://cloud.google.com/storage/docs/naming. If the URI is
      a bucket (without a namespace path), a prefix will be generated based on
      the start time.
  """

  collectionIds = _messages.StringField(1, repeated=True)
  outputUriPrefix = _messages.StringField(2)


class GoogleFirestoreAdminV1beta1ExportDocumentsResponse(_messages.Message):
  r"""Returned in the google.longrunning.Operation response field.

  Fields:
    outputUriPrefix: Location of the output files. This can be used to begin
      an import into Cloud Firestore (this project or another project) after
      the operation completes successfully.
  """

  outputUriPrefix = _messages.StringField(1)


class GoogleFirestoreAdminV1beta1ImportDocumentsMetadata(_messages.Message):
  r"""Metadata for ImportDocuments operations.

  Enums:
    OperationStateValueValuesEnum: The state of the import operation.

  Fields:
    collectionIds: Which collection ids are being imported.
    endTime: The time the operation ended, either successfully or otherwise.
      Unset if the operation is still active.
    inputUriPrefix: The location of the documents being imported.
    operationState: The state of the import operation.
    progressBytes: An estimate of the number of bytes processed.
    progressDocuments: An estimate of the number of documents processed.
    startTime: The time that work began on the operation.
  """

  class OperationStateValueValuesEnum(_messages.Enum):
    r"""The state of the import operation.

    Values:
      STATE_UNSPECIFIED: Unspecified.
      INITIALIZING: Request is being prepared for processing.
      PROCESSING: Request is actively being processed.
      CANCELLING: Request is in the process of being cancelled after user
        called google.longrunning.Operations.CancelOperation on the operation.
      FINALIZING: Request has been processed and is in its finalization stage.
      SUCCESSFUL: Request has completed successfully.
      FAILED: Request has finished being processed, but encountered an error.
      CANCELLED: Request has finished being cancelled after user called
        google.longrunning.Operations.CancelOperation.
    """
    STATE_UNSPECIFIED = 0
    INITIALIZING = 1
    PROCESSING = 2
    CANCELLING = 3
    FINALIZING = 4
    SUCCESSFUL = 5
    FAILED = 6
    CANCELLED = 7

  collectionIds = _messages.StringField(1, repeated=True)
  endTime = _messages.StringField(2)
  inputUriPrefix = _messages.StringField(3)
  operationState = _messages.EnumField('OperationStateValueValuesEnum', 4)
  progressBytes = _messages.MessageField('GoogleFirestoreAdminV1beta1Progress', 5)
  progressDocuments = _messages.MessageField('GoogleFirestoreAdminV1beta1Progress', 6)
  startTime = _messages.StringField(7)


class GoogleFirestoreAdminV1beta1ImportDocumentsRequest(_messages.Message):
  r"""The request for FirestoreAdmin.ImportDocuments.

  Fields:
    collectionIds: Which collection ids to import. Unspecified means all
      collections included in the import.
    inputUriPrefix: Location of the exported files. This must match the
      output_uri_prefix of an ExportDocumentsResponse from an export that has
      completed successfully. See: google.firestore.admin.v1beta1.ExportDocume
      ntsResponse.output_uri_prefix.
  """

  collectionIds = _messages.StringField(1, repeated=True)
  inputUriPrefix = _messages.StringField(2)


class GoogleFirestoreAdminV1beta1Index(_messages.Message):
  r"""An index definition.

  Enums:
    StateValueValuesEnum: The state of the index. Output only.

  Fields:
    collectionId: The collection ID to which this index applies. Required.
    fields: The fields to index.
    name: The resource name of the index. Output only.
    state: The state of the index. Output only.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the index. Output only.

    Values:
      STATE_UNSPECIFIED: The state is unspecified.
      CREATING: The index is being created. There is an active long-running
        operation for the index. The index is updated when writing a document.
        Some index data may exist.
      READY: The index is ready to be used. The index is updated when writing
        a document. The index is fully populated from all stored documents it
        applies to.
      ERROR: The index was being created, but something went wrong. There is
        no active long-running operation for the index, and the most recently
        finished long-running operation failed. The index is not updated when
        writing a document. Some index data may exist.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    ERROR = 3

  collectionId = _messages.StringField(1)
  fields = _messages.MessageField('GoogleFirestoreAdminV1beta1IndexField', 2, repeated=True)
  name = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class GoogleFirestoreAdminV1beta1IndexField(_messages.Message):
  r"""A field of an index.

  Enums:
    ModeValueValuesEnum: The field's mode.

  Fields:
    fieldPath: The path of the field. Must match the field path specification
      described by google.firestore.v1beta1.Document.fields. Special field
      path `__name__` may be used by itself or at the end of a path.
      `__type__` may be used only at the end of path.
    mode: The field's mode.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""The field's mode.

    Values:
      MODE_UNSPECIFIED: The mode is unspecified.
      ASCENDING: The field's values are indexed so as to support sequencing in
        ascending order and also query by <, >, <=, >=, and =.
      DESCENDING: The field's values are indexed so as to support sequencing
        in descending order and also query by <, >, <=, >=, and =.
      ARRAY_CONTAINS: The field's array values are indexed so as to support
        membership using ARRAY_CONTAINS queries.
    """
    MODE_UNSPECIFIED = 0
    ASCENDING = 1
    DESCENDING = 2
    ARRAY_CONTAINS = 3

  fieldPath = _messages.StringField(1)
  mode = _messages.EnumField('ModeValueValuesEnum', 2)


class GoogleFirestoreAdminV1beta1IndexOperationMetadata(_messages.Message):
  r"""Metadata for index operations. This metadata populates the metadata
  field of google.longrunning.Operation.

  Enums:
    OperationTypeValueValuesEnum: The type of index operation.

  Fields:
    cancelled: True if the [google.longrunning.Operation] was cancelled. If
      the cancellation is in progress, cancelled will be true but
      google.longrunning.Operation.done will be false.
    documentProgress: Progress of the existing operation, measured in number
      of documents.
    endTime: The time the operation ended, either successfully or otherwise.
      Unset if the operation is still active.
    index: The index resource that this operation is acting on. For example:
      `projects/{project_id}/databases/{database_id}/indexes/{index_id}`
    operationType: The type of index operation.
    startTime: The time that work began on the operation.
  """

  class OperationTypeValueValuesEnum(_messages.Enum):
    r"""The type of index operation.

    Values:
      OPERATION_TYPE_UNSPECIFIED: Unspecified. Never set by server.
      CREATING_INDEX: The operation is creating the index. Initiated by a
        `CreateIndex` call.
    """
    OPERATION_TYPE_UNSPECIFIED = 0
    CREATING_INDEX = 1

  cancelled = _messages.BooleanField(1)
  documentProgress = _messages.MessageField('GoogleFirestoreAdminV1beta1Progress', 2)
  endTime = _messages.StringField(3)
  index = _messages.StringField(4)
  operationType = _messages.EnumField('OperationTypeValueValuesEnum', 5)
  startTime = _messages.StringField(6)


class GoogleFirestoreAdminV1beta1ListIndexesResponse(_messages.Message):
  r"""The response for FirestoreAdmin.ListIndexes.

  Fields:
    indexes: The indexes.
    nextPageToken: The standard List next-page token.
  """

  indexes = _messages.MessageField('GoogleFirestoreAdminV1beta1Index', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleFirestoreAdminV1beta1LocationMetadata(_messages.Message):
  r"""The metadata message for google.cloud.location.Location.metadata."""


class GoogleFirestoreAdminV1beta1Progress(_messages.Message):
  r"""Measures the progress of a particular metric.

  Fields:
    workCompleted: An estimate of how much work has been completed. Note that
      this may be greater than `work_estimated`.
    workEstimated: An estimate of how much work needs to be performed. Zero if
      the work estimate is unavailable. May change as work progresses.
  """

  workCompleted = _messages.IntegerField(1)
  workEstimated = _messages.IntegerField(2)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal response of the operation in case of success. If
      the original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal response of the operation in case of success. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal response of the operation in case of success. If the
    original method returns no data on success, such as `Delete`, the response
    is `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class LatLng(_messages.Message):
  r"""An object that represents a latitude/longitude pair. This is expressed
  as a pair of doubles to represent degrees latitude and degrees longitude.
  Unless specified otherwise, this object must conform to the WGS84 standard.
  Values must be within normalized ranges.

  Fields:
    latitude: The latitude in degrees. It must be in the range [-90.0, +90.0].
    longitude: The longitude in degrees. It must be in the range [-180.0,
      +180.0].
  """

  latitude = _messages.FloatField(1)
  longitude = _messages.FloatField(2)


class ListCollectionIdsRequest(_messages.Message):
  r"""The request for Firestore.ListCollectionIds.

  Fields:
    pageSize: The maximum number of results to return.
    pageToken: A page token. Must be a value from ListCollectionIdsResponse.
    readTime: Reads documents as they were at the given time. This may not be
      older than 270 seconds.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  readTime = _messages.StringField(3)


class ListCollectionIdsResponse(_messages.Message):
  r"""The response from Firestore.ListCollectionIds.

  Fields:
    collectionIds: The collection ids.
    nextPageToken: A page token that may be used to continue the list.
  """

  collectionIds = _messages.StringField(1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDocumentsResponse(_messages.Message):
  r"""The response for Firestore.ListDocuments.

  Fields:
    documents: The Documents found.
    nextPageToken: A token to retrieve the next page of documents. If this
      field is omitted, there are no subsequent pages.
  """

  documents = _messages.MessageField('Document', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListenRequest(_messages.Message):
  r"""A request for Firestore.Listen

  Messages:
    LabelsValue: Labels associated with this target change.

  Fields:
    addTarget: A target to add to this stream.
    labels: Labels associated with this target change.
    removeTarget: The ID of a target to remove from this stream.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this target change.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  addTarget = _messages.MessageField('Target', 1)
  labels = _messages.MessageField('LabelsValue', 2)
  removeTarget = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class ListenResponse(_messages.Message):
  r"""The response for Firestore.Listen.

  Fields:
    documentChange: A Document has changed.
    documentDelete: A Document has been deleted.
    documentRemove: A Document has been removed from a target (because it is
      no longer relevant to that target).
    filter: A filter to apply to the set of documents previously returned for
      the given target. Returned when documents may have been removed from the
      given target, but the exact documents are unknown.
    targetChange: Targets have changed.
  """

  documentChange = _messages.MessageField('DocumentChange', 1)
  documentDelete = _messages.MessageField('DocumentDelete', 2)
  documentRemove = _messages.MessageField('DocumentRemove', 3)
  filter = _messages.MessageField('ExistenceFilter', 4)
  targetChange = _messages.MessageField('TargetChange', 5)


class MapValue(_messages.Message):
  r"""A map value.

  Messages:
    FieldsValue: The map's fields. The map keys represent field names. Field
      names matching the regular expression `__.*__` are reserved. Reserved
      field names are forbidden except in certain documented contexts. The map
      keys, represented as UTF-8, must not exceed 1,500 bytes and cannot be
      empty.

  Fields:
    fields: The map's fields. The map keys represent field names. Field names
      matching the regular expression `__.*__` are reserved. Reserved field
      names are forbidden except in certain documented contexts. The map keys,
      represented as UTF-8, must not exceed 1,500 bytes and cannot be empty.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FieldsValue(_messages.Message):
    r"""The map's fields. The map keys represent field names. Field names
    matching the regular expression `__.*__` are reserved. Reserved field
    names are forbidden except in certain documented contexts. The map keys,
    represented as UTF-8, must not exceed 1,500 bytes and cannot be empty.

    Messages:
      AdditionalProperty: An additional property for a FieldsValue object.

    Fields:
      additionalProperties: Additional properties of type FieldsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FieldsValue object.

      Fields:
        key: Name of the additional property.
        value: A Value attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Value', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  fields = _messages.MessageField('FieldsValue', 1)


class Order(_messages.Message):
  r"""An order on a field.

  Enums:
    DirectionValueValuesEnum: The direction to order by. Defaults to
      `ASCENDING`.

  Fields:
    direction: The direction to order by. Defaults to `ASCENDING`.
    field: The field to order by.
  """

  class DirectionValueValuesEnum(_messages.Enum):
    r"""The direction to order by. Defaults to `ASCENDING`.

    Values:
      DIRECTION_UNSPECIFIED: Unspecified.
      ASCENDING: Ascending.
      DESCENDING: Descending.
    """
    DIRECTION_UNSPECIFIED = 0
    ASCENDING = 1
    DESCENDING = 2

  direction = _messages.EnumField('DirectionValueValuesEnum', 1)
  field = _messages.MessageField('FieldReference', 2)


class PartitionQueryRequest(_messages.Message):
  r"""The request for Firestore.PartitionQuery.

  Fields:
    pageSize: The maximum number of partitions to return in this call, subject
      to `partition_count`. For example, if `partition_count` = 10 and
      `page_size` = 8, the first call to PartitionQuery will return up to 8
      partitions and a `next_page_token` if more results exist. A second call
      to PartitionQuery will return up to 2 partitions, to complete the total
      of 10 specified in `partition_count`.
    pageToken: The `next_page_token` value returned from a previous call to
      PartitionQuery that may be used to get an additional set of results.
      There are no ordering guarantees between sets of results. Thus, using
      multiple sets of results will require merging the different result sets.
      For example, two subsequent calls using a page_token may return: *
      cursor B, cursor M, cursor Q * cursor A, cursor U, cursor W To obtain a
      complete result set ordered with respect to the results of the query
      supplied to PartitionQuery, the results sets should be merged: cursor A,
      cursor B, cursor M, cursor Q, cursor U, cursor W
    partitionCount: The desired maximum number of partition points. The
      partitions may be returned across multiple pages of results. The number
      must be positive. The actual number of partitions returned may be fewer.
      For example, this may be set to one fewer than the number of parallel
      queries to be run, or in running a data pipeline job, one fewer than the
      number of workers or compute instances available.
    readTime: Reads documents as they were at the given time. This may not be
      older than 270 seconds.
    structuredQuery: A structured query. Query must specify collection with
      all descendants and be ordered by name ascending. Other filters, order
      bys, limits, offsets, and start/end cursors are not supported.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  partitionCount = _messages.IntegerField(3)
  readTime = _messages.StringField(4)
  structuredQuery = _messages.MessageField('StructuredQuery', 5)


class PartitionQueryResponse(_messages.Message):
  r"""The response for Firestore.PartitionQuery.

  Fields:
    nextPageToken: A page token that may be used to request an additional set
      of results, up to the number specified by `partition_count` in the
      PartitionQuery request. If blank, there are no more results.
    partitions: Partition results. Each partition is a split point that can be
      used by RunQuery as a starting or end point for the query results. The
      RunQuery requests must be made with the same query supplied to this
      PartitionQuery request. The partition cursors will be ordered according
      to same ordering as the results of the query supplied to PartitionQuery.
      For example, if a PartitionQuery request returns partition cursors A and
      B, running the following three queries will return the entire result set
      of the original query: * query, end_at A * query, start_at A, end_at B *
      query, start_at B An empty result may indicate that the query has too
      few results to be partitioned.
  """

  nextPageToken = _messages.StringField(1)
  partitions = _messages.MessageField('Cursor', 2, repeated=True)


class Precondition(_messages.Message):
  r"""A precondition on a document, used for conditional operations.

  Fields:
    exists: When set to `true`, the target document must exist. When set to
      `false`, the target document must not exist.
    updateTime: When set, the target document must exist and have been last
      updated at that time. Timestamp must be microsecond aligned.
  """

  exists = _messages.BooleanField(1)
  updateTime = _messages.StringField(2)


class Projection(_messages.Message):
  r"""The projection of document's fields to return.

  Fields:
    fields: The fields to return. If empty, all fields are returned. To only
      return the name of the document, use `['__name__']`.
  """

  fields = _messages.MessageField('FieldReference', 1, repeated=True)


class QueryTarget(_messages.Message):
  r"""A target specified by a query.

  Fields:
    parent: The parent resource name. In the format:
      `projects/{project_id}/databases/{database_id}/documents` or `projects/{
      project_id}/databases/{database_id}/documents/{document_path}`. For
      example: `projects/my-project/databases/my-database/documents` or
      `projects/my-project/databases/my-database/documents/chatrooms/my-
      chatroom`
    structuredQuery: A structured query.
  """

  parent = _messages.StringField(1)
  structuredQuery = _messages.MessageField('StructuredQuery', 2)


class ReadOnly(_messages.Message):
  r"""Options for a transaction that can only be used to read documents.

  Fields:
    readTime: Reads documents at the given time. This may not be older than 60
      seconds.
  """

  readTime = _messages.StringField(1)


class ReadWrite(_messages.Message):
  r"""Options for a transaction that can be used to read and write documents.
  Firestore does not allow 3rd party auth requests to create read-write.
  transactions.

  Fields:
    retryTransaction: An optional transaction to retry.
  """

  retryTransaction = _messages.BytesField(1)


class RollbackRequest(_messages.Message):
  r"""The request for Firestore.Rollback.

  Fields:
    transaction: Required. The transaction to roll back.
  """

  transaction = _messages.BytesField(1)


class RunAggregationQueryRequest(_messages.Message):
  r"""The request for Firestore.RunAggregationQuery.

  Fields:
    newTransaction: Starts a new transaction as part of the query, defaulting
      to read-only. The new transaction ID will be returned as the first
      response in the stream.
    readTime: Executes the query at the given timestamp. Requires: * Cannot be
      more than 270 seconds in the past.
    structuredAggregationQuery: An aggregation query.
    transaction: Run the aggregation within an already active transaction. The
      value here is the opaque transaction ID to execute the query in.
  """

  newTransaction = _messages.MessageField('TransactionOptions', 1)
  readTime = _messages.StringField(2)
  structuredAggregationQuery = _messages.MessageField('StructuredAggregationQuery', 3)
  transaction = _messages.BytesField(4)


class RunAggregationQueryResponse(_messages.Message):
  r"""The response for Firestore.RunAggregationQuery.

  Fields:
    readTime: The time at which the aggregate result was computed. This is
      always monotonically increasing; in this case, the previous
      AggregationResult in the result stream are guaranteed not to have
      changed between their `read_time` and this one. If the query returns no
      results, a response with `read_time` and no `result` will be sent, and
      this represents the time at which the query was run.
    result: A single aggregation result. Not present when reporting partial
      progress.
    transaction: The transaction that was started as part of this request.
      Only present on the first response when the request requested to start a
      new transaction.
  """

  readTime = _messages.StringField(1)
  result = _messages.MessageField('AggregationResult', 2)
  transaction = _messages.BytesField(3)


class RunQueryRequest(_messages.Message):
  r"""The request for Firestore.RunQuery.

  Fields:
    newTransaction: Starts a new transaction and reads the documents. Defaults
      to a read-only transaction. The new transaction ID will be returned as
      the first response in the stream.
    readTime: Reads documents as they were at the given time. This may not be
      older than 270 seconds.
    structuredQuery: A structured query.
    transaction: Run the query within an already active transaction. The value
      here is the opaque transaction ID to execute the query in.
  """

  newTransaction = _messages.MessageField('TransactionOptions', 1)
  readTime = _messages.StringField(2)
  structuredQuery = _messages.MessageField('StructuredQuery', 3)
  transaction = _messages.BytesField(4)


class RunQueryResponse(_messages.Message):
  r"""The response for Firestore.RunQuery.

  Fields:
    document: A query result, not set when reporting partial progress.
    done: If present, Firestore has completely finished the request and no
      more documents will be returned.
    readTime: The time at which the document was read. This may be
      monotonically increasing; in this case, the previous documents in the
      result stream are guaranteed not to have changed between their
      `read_time` and this one. If the query returns no results, a response
      with `read_time` and no `document` will be sent, and this represents the
      time at which the query was run.
    skippedResults: The number of results that have been skipped due to an
      offset between the last response and the current response.
    transaction: The transaction that was started as part of this request. Can
      only be set in the first response, and only if
      RunQueryRequest.new_transaction was set in the request. If set, no other
      fields will be set in this response.
  """

  document = _messages.MessageField('Document', 1)
  done = _messages.BooleanField(2)
  readTime = _messages.StringField(3)
  skippedResults = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  transaction = _messages.BytesField(5)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StructuredAggregationQuery(_messages.Message):
  r"""Firestore query for running an aggregation over a StructuredQuery.

  Fields:
    aggregations: Optional. Series of aggregations to apply over the results
      of the `structured_query`. Requires: * A minimum of one and maximum of
      five aggregations per query.
    structuredQuery: Nested structured query.
  """

  aggregations = _messages.MessageField('Aggregation', 1, repeated=True)
  structuredQuery = _messages.MessageField('StructuredQuery', 2)


class StructuredQuery(_messages.Message):
  r"""A Firestore query.

  Fields:
    endAt: A potential prefix of a position in the result set to end the query
      at. This is similar to `START_AT` but with it controlling the end
      position rather than the start position. Requires: * The number of
      values cannot be greater than the number of fields specified in the
      `ORDER BY` clause.
    from_: The collections to query.
    limit: The maximum number of results to return. Applies after all other
      constraints. Requires: * The value must be greater than or equal to zero
      if specified.
    offset: The number of documents to skip before returning the first result.
      This applies after the constraints specified by the `WHERE`, `START AT`,
      & `END AT` but before the `LIMIT` clause. Requires: * The value must be
      greater than or equal to zero if specified.
    orderBy: The order to apply to the query results. Firestore allows callers
      to provide a full ordering, a partial ordering, or no ordering at all.
      In all cases, Firestore guarantees a stable ordering through the
      following rules: * The `order_by` is required to reference all fields
      used with an inequality filter. * All fields that are required to be in
      the `order_by` but are not already present are appended in
      lexicographical ordering of the field name. * If an order on `__name__`
      is not specified, it is appended by default. Fields are appended with
      the same sort direction as the last order specified, or 'ASCENDING' if
      no order was specified. For example: * `ORDER BY a` becomes `ORDER BY a
      ASC, __name__ ASC` * `ORDER BY a DESC` becomes `ORDER BY a DESC,
      __name__ DESC` * `WHERE a > 1` becomes `WHERE a > 1 ORDER BY a ASC,
      __name__ ASC` * `WHERE __name__ > ... AND a > 1` becomes `WHERE __name__
      > ... AND a > 1 ORDER BY a ASC, __name__ ASC`
    select: Optional sub-set of the fields to return. This acts as a
      DocumentMask over the documents returned from a query. When not set,
      assumes that the caller wants all fields returned.
    startAt: A potential prefix of a position in the result set to start the
      query at. The ordering of the result set is based on the `ORDER BY`
      clause of the original query. ``` SELECT * FROM k WHERE a = 1 AND b > 2
      ORDER BY b ASC, __name__ ASC; ``` This query's results are ordered by
      `(b ASC, __name__ ASC)`. Cursors can reference either the full ordering
      or a prefix of the location, though it cannot reference more fields than
      what are in the provided `ORDER BY`. Continuing off the example above,
      attaching the following start cursors will have varying impact: - `START
      BEFORE (2, /k/123)`: start the query right before `a = 1 AND b > 2 AND
      __name__ > /k/123`. - `START AFTER (10)`: start the query right after `a
      = 1 AND b > 10`. Unlike `OFFSET` which requires scanning over the first
      N results to skip, a start cursor allows the query to begin at a logical
      position. This position is not required to match an actual result, it
      will scan forward from this position to find the next document.
      Requires: * The number of values cannot be greater than the number of
      fields specified in the `ORDER BY` clause.
    where: The filter to apply.
  """

  endAt = _messages.MessageField('Cursor', 1)
  from_ = _messages.MessageField('CollectionSelector', 2, repeated=True)
  limit = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  offset = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  orderBy = _messages.MessageField('Order', 5, repeated=True)
  select = _messages.MessageField('Projection', 6)
  startAt = _messages.MessageField('Cursor', 7)
  where = _messages.MessageField('Filter', 8)


class Target(_messages.Message):
  r"""A specification of a set of documents to listen to.

  Fields:
    documents: A target specified by a set of document names.
    expectedCount: The number of documents that last matched the query at the
      resume token or read time. This value is only relevant when a
      `resume_type` is provided. This value being present and greater than
      zero signals that the client wants `ExistenceFilter.unchanged_names` to
      be included in the response.
    once: If the target should be removed once it is current and consistent.
    query: A target specified by a query.
    readTime: Start listening after a specific `read_time`. The client must
      know the state of matching documents at this time.
    resumeToken: A resume token from a prior TargetChange for an identical
      target. Using a resume token with a different target is unsupported and
      may fail.
    targetId: The target ID that identifies the target on the stream. Must be
      a positive number and non-zero.
  """

  documents = _messages.MessageField('DocumentsTarget', 1)
  expectedCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  once = _messages.BooleanField(3)
  query = _messages.MessageField('QueryTarget', 4)
  readTime = _messages.StringField(5)
  resumeToken = _messages.BytesField(6)
  targetId = _messages.IntegerField(7, variant=_messages.Variant.INT32)


class TargetChange(_messages.Message):
  r"""Targets being watched have changed.

  Enums:
    TargetChangeTypeValueValuesEnum: The type of change that occurred.

  Fields:
    cause: The error that resulted in this change, if applicable.
    readTime: The consistent `read_time` for the given `target_ids` (omitted
      when the target_ids are not at a consistent snapshot). The stream is
      guaranteed to send a `read_time` with `target_ids` empty whenever the
      entire stream reaches a new consistent snapshot. ADD, CURRENT, and RESET
      messages are guaranteed to (eventually) result in a new consistent
      snapshot (while NO_CHANGE and REMOVE messages are not). For a given
      stream, `read_time` is guaranteed to be monotonically increasing.
    resumeToken: A token that can be used to resume the stream for the given
      `target_ids`, or all targets if `target_ids` is empty. Not set on every
      target change.
    targetChangeType: The type of change that occurred.
    targetIds: The target IDs of targets that have changed. If empty, the
      change applies to all targets. The order of the target IDs is not
      defined.
  """

  class TargetChangeTypeValueValuesEnum(_messages.Enum):
    r"""The type of change that occurred.

    Values:
      NO_CHANGE: No change has occurred. Used only to send an updated
        `resume_token`.
      ADD: The targets have been added.
      REMOVE: The targets have been removed.
      CURRENT: The targets reflect all changes committed before the targets
        were added to the stream. This will be sent after or with a
        `read_time` that is greater than or equal to the time at which the
        targets were added. Listeners can wait for this change if read-after-
        write semantics are desired.
      RESET: The targets have been reset, and a new initial state for the
        targets will be returned in subsequent changes. After the initial
        state is complete, `CURRENT` will be returned even if the target was
        previously indicated to be `CURRENT`.
    """
    NO_CHANGE = 0
    ADD = 1
    REMOVE = 2
    CURRENT = 3
    RESET = 4

  cause = _messages.MessageField('Status', 1)
  readTime = _messages.StringField(2)
  resumeToken = _messages.BytesField(3)
  targetChangeType = _messages.EnumField('TargetChangeTypeValueValuesEnum', 4)
  targetIds = _messages.IntegerField(5, repeated=True, variant=_messages.Variant.INT32)


class TransactionOptions(_messages.Message):
  r"""Options for creating a new transaction.

  Fields:
    readOnly: The transaction can only be used for read operations.
    readWrite: The transaction can be used for both read and write operations.
  """

  readOnly = _messages.MessageField('ReadOnly', 1)
  readWrite = _messages.MessageField('ReadWrite', 2)


class UnaryFilter(_messages.Message):
  r"""A filter with a single operand.

  Enums:
    OpValueValuesEnum: The unary operator to apply.

  Fields:
    field: The field to which to apply the operator.
    op: The unary operator to apply.
  """

  class OpValueValuesEnum(_messages.Enum):
    r"""The unary operator to apply.

    Values:
      OPERATOR_UNSPECIFIED: Unspecified. This value must not be used.
      IS_NAN: The given `field` is equal to `NaN`.
      IS_NULL: The given `field` is equal to `NULL`.
      IS_NOT_NAN: The given `field` is not equal to `NaN`. Requires: * No
        other `NOT_EQUAL`, `NOT_IN`, `IS_NOT_NULL`, or `IS_NOT_NAN`. * That
        `field` comes first in the `order_by`.
      IS_NOT_NULL: The given `field` is not equal to `NULL`. Requires: * A
        single `NOT_EQUAL`, `NOT_IN`, `IS_NOT_NULL`, or `IS_NOT_NAN`. * That
        `field` comes first in the `order_by`.
    """
    OPERATOR_UNSPECIFIED = 0
    IS_NAN = 1
    IS_NULL = 2
    IS_NOT_NAN = 3
    IS_NOT_NULL = 4

  field = _messages.MessageField('FieldReference', 1)
  op = _messages.EnumField('OpValueValuesEnum', 2)


class Value(_messages.Message):
  r"""A message that can hold any of the supported value types.

  Enums:
    NullValueValueValuesEnum: A null value.

  Fields:
    arrayValue: An array value. Cannot directly contain another array value,
      though can contain an map which contains another array.
    booleanValue: A boolean value.
    bytesValue: A bytes value. Must not exceed 1 MiB - 89 bytes. Only the
      first 1,500 bytes are considered by queries.
    doubleValue: A double value.
    geoPointValue: A geo point value representing a point on the surface of
      Earth.
    integerValue: An integer value.
    mapValue: A map value.
    nullValue: A null value.
    referenceValue: A reference to a document. For example:
      `projects/{project_id}/databases/{database_id}/documents/{document_path}
      `.
    stringValue: A string value. The string, represented as UTF-8, must not
      exceed 1 MiB - 89 bytes. Only the first 1,500 bytes of the UTF-8
      representation are considered by queries.
    timestampValue: A timestamp value. Precise only to microseconds. When
      stored, any additional precision is rounded down.
  """

  class NullValueValueValuesEnum(_messages.Enum):
    r"""A null value.

    Values:
      NULL_VALUE: Null value.
    """
    NULL_VALUE = 0

  arrayValue = _messages.MessageField('ArrayValue', 1)
  booleanValue = _messages.BooleanField(2)
  bytesValue = _messages.BytesField(3)
  doubleValue = _messages.FloatField(4)
  geoPointValue = _messages.MessageField('LatLng', 5)
  integerValue = _messages.IntegerField(6)
  mapValue = _messages.MessageField('MapValue', 7)
  nullValue = _messages.EnumField('NullValueValueValuesEnum', 8)
  referenceValue = _messages.StringField(9)
  stringValue = _messages.StringField(10)
  timestampValue = _messages.StringField(11)


class Write(_messages.Message):
  r"""A write on a document.

  Fields:
    currentDocument: An optional precondition on the document. The write will
      fail if this is set and not met by the target document.
    delete: A document name to delete. In the format:
      `projects/{project_id}/databases/{database_id}/documents/{document_path}
      `.
    transform: Applies a transformation to a document.
    update: A document to write.
    updateMask: The fields to update in this write. This field can be set only
      when the operation is `update`. If the mask is not set for an `update`
      and the document exists, any existing data will be overwritten. If the
      mask is set and the document on the server has fields not covered by the
      mask, they are left unchanged. Fields referenced in the mask, but not
      present in the input document, are deleted from the document on the
      server. The field paths in this mask must not contain a reserved field
      name.
    updateTransforms: The transforms to perform after update. This field can
      be set only when the operation is `update`. If present, this write is
      equivalent to performing `update` and `transform` to the same document
      atomically and in order.
  """

  currentDocument = _messages.MessageField('Precondition', 1)
  delete = _messages.StringField(2)
  transform = _messages.MessageField('DocumentTransform', 3)
  update = _messages.MessageField('Document', 4)
  updateMask = _messages.MessageField('DocumentMask', 5)
  updateTransforms = _messages.MessageField('FieldTransform', 6, repeated=True)


class WriteRequest(_messages.Message):
  r"""The request for Firestore.Write. The first request creates a stream, or
  resumes an existing one from a token. When creating a new stream, the server
  replies with a response containing only an ID and a token, to use in the
  next request. When resuming a stream, the server first streams any responses
  later than the given token, then a response containing only an up-to-date
  token, to use in the next request.

  Messages:
    LabelsValue: Labels associated with this write request.

  Fields:
    labels: Labels associated with this write request.
    streamId: The ID of the write stream to resume. This may only be set in
      the first message. When left empty, a new write stream will be created.
    streamToken: A stream token that was previously sent by the server. The
      client should set this field to the token from the most recent
      WriteResponse it has received. This acknowledges that the client has
      received responses up to this token. After sending this token, earlier
      tokens may not be used anymore. The server may close the stream if there
      are too many unacknowledged responses. Leave this field unset when
      creating a new stream. To resume a stream at a specific point, set this
      field and the `stream_id` field. Leave this field unset when creating a
      new stream.
    writes: The writes to apply. Always executed atomically and in order. This
      must be empty on the first request. This may be empty on the last
      request. This must not be empty on all other requests.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels associated with this write request.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  streamId = _messages.StringField(2)
  streamToken = _messages.BytesField(3)
  writes = _messages.MessageField('Write', 4, repeated=True)


class WriteResponse(_messages.Message):
  r"""The response for Firestore.Write.

  Fields:
    commitTime: The time at which the commit occurred. Any read with an equal
      or greater `read_time` is guaranteed to see the effects of the write.
    streamId: The ID of the stream. Only set on the first message, when a new
      stream was created.
    streamToken: A token that represents the position of this response in the
      stream. This can be used by a client to resume the stream at this point.
      This field is always set.
    writeResults: The result of applying the writes. This i-th write result
      corresponds to the i-th write in the request.
  """

  commitTime = _messages.StringField(1)
  streamId = _messages.StringField(2)
  streamToken = _messages.BytesField(3)
  writeResults = _messages.MessageField('WriteResult', 4, repeated=True)


class WriteResult(_messages.Message):
  r"""The result of applying a write.

  Fields:
    transformResults: The results of applying each
      DocumentTransform.FieldTransform, in the same order.
    updateTime: The last update time of the document after applying the write.
      Not set after a `delete`. If the write did not actually change the
      document, this will be the previous update_time.
  """

  transformResults = _messages.MessageField('Value', 1, repeated=True)
  updateTime = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    StructuredQuery, 'from_', 'from')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

"""Generated message classes for containeranalysis version v1alpha1.

An implementation of the Grafeas API, which stores, and enables querying and
retrieval of critical metadata about all of your software artifacts.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'containeranalysis'


class AnalysisCompleted(_messages.Message):
  r"""Indicates which analysis completed successfully. Multiple types of
  analysis can be performed on a single resource.

  Fields:
    analysisType: type of analysis that were completed on a resource.
  """

  analysisType = _messages.StringField(1, repeated=True)


class Artifact(_messages.Message):
  r"""Artifact describes a build product.

  Fields:
    checksum: Hash or checksum value of a binary, or Docker Registry 2.0
      digest of a container.
    id: Artifact ID, if any; for container images, this will be a URL by
      digest like gcr.io/projectID/imagename@sha256:123456
    name: Name of the artifact. This may be the path to a binary or jar file,
      or in the case of a container build, the name used to push the container
      image to Google Container Registry, as presented to `docker push`. This
      field is deprecated in favor of the plural `names` field; it continues
      to exist here to allow existing BuildProvenance serialized to json in
      google.devtools.containeranalysis.v1alpha1.BuildDetails.provenance_bytes
      to deserialize back into proto.
    names: Related artifact names. This may be the path to a binary or jar
      file, or in the case of a container build, the name used to push the
      container image to Google Container Registry, as presented to `docker
      push`. Note that a single Artifact ID can have multiple names, for
      example if two tags are applied to one image.
  """

  checksum = _messages.StringField(1)
  id = _messages.StringField(2)
  name = _messages.StringField(3)
  names = _messages.StringField(4, repeated=True)


class Assessment(_messages.Message):
  r"""Assessment provides all information that is related to a single
  vulnerability for this product.

  Enums:
    StateValueValuesEnum: Provides the state of this Vulnerability assessment.

  Fields:
    cve: Holds the MITRE standard Common Vulnerabilities and Exposures (CVE)
      tracking number for the vulnerability.
    impacts: Contains information about the impact of this vulnerability, this
      will change with time.
    justification: Justification provides the justification when the state of
      the assessment if NOT_AFFECTED.
    longDescription: A detailed description of this Vex.
    relatedUris: Holds a list of references associated with this vulnerability
      item and assessment. These uris have additional information about the
      vulnerability and the assessment itself. E.g. Link to a document which
      details how this assessment concluded the state of this vulnerability.
    remediations: Specifies details on how to handle (and presumably, fix) a
      vulnerability.
    shortDescription: A one sentence description of this Vex.
    state: Provides the state of this Vulnerability assessment.
    vulnerabilityId: The vulnerability identifier for this Assessment. Will
      hold one of common identifiers e.g. CVE, GHSA etc.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Provides the state of this Vulnerability assessment.

    Values:
      STATE_UNSPECIFIED: No state is specified.
      AFFECTED: This product is known to be affected by this vulnerability.
      NOT_AFFECTED: This product is known to be not affected by this
        vulnerability.
      FIXED: This product contains a fix for this vulnerability.
      UNDER_INVESTIGATION: It is not known yet whether these versions are or
        are not affected by the vulnerability. However, it is still under
        investigation.
    """
    STATE_UNSPECIFIED = 0
    AFFECTED = 1
    NOT_AFFECTED = 2
    FIXED = 3
    UNDER_INVESTIGATION = 4

  cve = _messages.StringField(1)
  impacts = _messages.StringField(2, repeated=True)
  justification = _messages.MessageField('Justification', 3)
  longDescription = _messages.StringField(4)
  relatedUris = _messages.MessageField('URI', 5, repeated=True)
  remediations = _messages.MessageField('Remediation', 6, repeated=True)
  shortDescription = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  vulnerabilityId = _messages.StringField(9)


class Attestation(_messages.Message):
  r"""Occurrence that represents a single "attestation". The authenticity of
  an Attestation can be verified using the attached signature. If the verifier
  trusts the public key of the signer, then verifying the signature is
  sufficient to establish trust. In this circumstance, the
  AttestationAuthority to which this Attestation is attached is primarily
  useful for look-up (how to find this Attestation if you already know the
  Authority and artifact to be verified) and intent (which authority was this
  attestation intended to sign for).

  Fields:
    pgpSignedAttestation: A PgpSignedAttestation attribute.
  """

  pgpSignedAttestation = _messages.MessageField('PgpSignedAttestation', 1)


class AttestationAuthority(_messages.Message):
  r"""Note kind that represents a logical attestation "role" or "authority".
  For example, an organization might have one `AttestationAuthority` for "QA"
  and one for "build". This Note is intended to act strictly as a grouping
  mechanism for the attached Occurrences (Attestations). This grouping
  mechanism also provides a security boundary, since IAM ACLs gate the ability
  for a principle to attach an Occurrence to a given Note. It also provides a
  single point of lookup to find all attached Attestation Occurrences, even if
  they don't all live in the same project.

  Fields:
    hint: A AttestationAuthorityHint attribute.
  """

  hint = _messages.MessageField('AttestationAuthorityHint', 1)


class AttestationAuthorityHint(_messages.Message):
  r"""This submessage provides human-readable hints about the purpose of the
  AttestationAuthority. Because the name of a Note acts as its resource
  reference, it is important to disambiguate the canonical name of the Note
  (which might be a UUID for security purposes) from "readable" names more
  suitable for debug output. Note that these hints should NOT be used to look
  up AttestationAuthorities in security sensitive contexts, such as when
  looking up Attestations to verify.

  Fields:
    humanReadableName: The human readable name of this Attestation Authority,
      for example "qa".
  """

  humanReadableName = _messages.StringField(1)


class Basis(_messages.Message):
  r"""Basis describes the base image portion (Note) of the DockerImage
  relationship. Linked occurrences are derived from this or an equivalent
  image via: FROM Or an equivalent reference, e.g. a tag of the resource_url.

  Fields:
    fingerprint: The fingerprint of the base image.
    resourceUrl: The resource_url for the resource representing the basis of
      associated occurrence images.
  """

  fingerprint = _messages.MessageField('Fingerprint', 1)
  resourceUrl = _messages.StringField(2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class BuildDefinition(_messages.Message):
  r"""A BuildDefinition object.

  Messages:
    ExternalParametersValue: A ExternalParametersValue object.
    InternalParametersValue: A InternalParametersValue object.

  Fields:
    buildType: A string attribute.
    externalParameters: A ExternalParametersValue attribute.
    internalParameters: A InternalParametersValue attribute.
    resolvedDependencies: A ResourceDescriptor attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExternalParametersValue(_messages.Message):
    r"""A ExternalParametersValue object.

    Messages:
      AdditionalProperty: An additional property for a ExternalParametersValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExternalParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class InternalParametersValue(_messages.Message):
    r"""A InternalParametersValue object.

    Messages:
      AdditionalProperty: An additional property for a InternalParametersValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a InternalParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  buildType = _messages.StringField(1)
  externalParameters = _messages.MessageField('ExternalParametersValue', 2)
  internalParameters = _messages.MessageField('InternalParametersValue', 3)
  resolvedDependencies = _messages.MessageField('ResourceDescriptor', 4, repeated=True)


class BuildDetails(_messages.Message):
  r"""Message encapsulating build provenance details.

  Fields:
    inTotoSlsaProvenanceV1: In-Toto Slsa Provenance V1 represents a slsa
      provenance meeting the slsa spec, wrapped in an in-toto statement. This
      allows for direct jsonification of a to-spec in-toto slsa statement with
      a to-spec slsa provenance.
    intotoProvenance: Deprecated. See InTotoStatement for the replacement. In-
      toto Provenance representation as defined in spec.
    intotoStatement: In-toto Statement representation as defined in spec. The
      intoto_statement can contain any type of provenance. The serialized
      payload of the statement can be stored and signed in the Occurrence's
      envelope.
    provenance: The actual provenance
    provenanceBytes: Serialized JSON representation of the provenance, used in
      generating the `BuildSignature` in the corresponding Result. After
      verifying the signature, `provenance_bytes` can be unmarshalled and
      compared to the provenance to confirm that it is unchanged. A
      base64-encoded string representation of the provenance bytes is used for
      the signature in order to interoperate with openssl which expects this
      format for signature verification. The serialized form is captured both
      to avoid ambiguity in how the provenance is marshalled to json as well
      to prevent incompatibilities with future changes.
  """

  inTotoSlsaProvenanceV1 = _messages.MessageField('InTotoSlsaProvenanceV1', 1)
  intotoProvenance = _messages.MessageField('InTotoProvenance', 2)
  intotoStatement = _messages.MessageField('InTotoStatement', 3)
  provenance = _messages.MessageField('BuildProvenance', 4)
  provenanceBytes = _messages.StringField(5)


class BuildMetadata(_messages.Message):
  r"""A BuildMetadata object.

  Fields:
    finishedOn: A string attribute.
    invocationId: A string attribute.
    startedOn: A string attribute.
  """

  finishedOn = _messages.StringField(1)
  invocationId = _messages.StringField(2)
  startedOn = _messages.StringField(3)


class BuildProvenance(_messages.Message):
  r"""Provenance of a build. Contains all information needed to verify the
  full details about the build from source to completion.

  Messages:
    BuildOptionsValue: Special options applied to this build. This is a catch-
      all field where build providers can enter any desired additional
      details.

  Fields:
    buildOptions: Special options applied to this build. This is a catch-all
      field where build providers can enter any desired additional details.
    builderVersion: Version string of the builder at the time this build was
      executed.
    builtArtifacts: Output of the build.
    commands: Commands requested by the build.
    createTime: Time at which the build was created.
    creator: E-mail address of the user who initiated this build. Note that
      this was the user's e-mail address at the time the build was initiated;
      this address may not represent the same end-user for all time.
    finishTime: Time at which execution of the build was finished.
    id: Unique identifier of the build.
    logsBucket: Google Cloud Storage bucket where logs were written.
    projectId: ID of the project.
    sourceProvenance: Details of the Source input to the build.
    startTime: Time at which execution of the build was started.
    triggerId: Trigger identifier if the build was triggered automatically;
      empty if not.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class BuildOptionsValue(_messages.Message):
    r"""Special options applied to this build. This is a catch-all field where
    build providers can enter any desired additional details.

    Messages:
      AdditionalProperty: An additional property for a BuildOptionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type BuildOptionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a BuildOptionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  buildOptions = _messages.MessageField('BuildOptionsValue', 1)
  builderVersion = _messages.StringField(2)
  builtArtifacts = _messages.MessageField('Artifact', 3, repeated=True)
  commands = _messages.MessageField('Command', 4, repeated=True)
  createTime = _messages.StringField(5)
  creator = _messages.StringField(6)
  finishTime = _messages.StringField(7)
  id = _messages.StringField(8)
  logsBucket = _messages.StringField(9)
  projectId = _messages.StringField(10)
  sourceProvenance = _messages.MessageField('Source', 11)
  startTime = _messages.StringField(12)
  triggerId = _messages.StringField(13)


class BuildSignature(_messages.Message):
  r"""Message encapsulating the signature of the verified build.

  Enums:
    KeyTypeValueValuesEnum: The type of the key, either stored in `public_key`
      or referenced in `key_id`

  Fields:
    keyId: An Id for the key used to sign. This could be either an Id for the
      key stored in `public_key` (such as the Id or fingerprint for a PGP key,
      or the CN for a cert), or a reference to an external key (such as a
      reference to a key in Cloud Key Management Service).
    keyType: The type of the key, either stored in `public_key` or referenced
      in `key_id`
    publicKey: Public key of the builder which can be used to verify that the
      related findings are valid and unchanged. If `key_type` is empty, this
      defaults to PEM encoded public keys. This field may be empty if `key_id`
      references an external key. For Cloud Build based signatures, this is a
      PEM encoded public key. To verify the Cloud Build signature, place the
      contents of this field into a file (public.pem). The signature field is
      base64-decoded into its binary representation in signature.bin, and the
      provenance bytes from `BuildDetails` are base64-decoded into a binary
      representation in signed.bin. OpenSSL can then verify the signature:
      `openssl sha256 -verify public.pem -signature signature.bin signed.bin`
    signature: Signature of the related `BuildProvenance`, encoded in a base64
      string.
  """

  class KeyTypeValueValuesEnum(_messages.Enum):
    r"""The type of the key, either stored in `public_key` or referenced in
    `key_id`

    Values:
      KEY_TYPE_UNSPECIFIED: `KeyType` is not set.
      PGP_ASCII_ARMORED: `PGP ASCII Armored` public key.
      PKIX_PEM: `PKIX PEM` public key.
    """
    KEY_TYPE_UNSPECIFIED = 0
    PGP_ASCII_ARMORED = 1
    PKIX_PEM = 2

  keyId = _messages.StringField(1)
  keyType = _messages.EnumField('KeyTypeValueValuesEnum', 2)
  publicKey = _messages.StringField(3)
  signature = _messages.StringField(4)


class BuildStep(_messages.Message):
  r"""A step in the build pipeline. Next ID: 21

  Enums:
    StatusValueValuesEnum: Output only. Status of the build step. At this
      time, build step status is only updated on build completion; step status
      is not updated in real-time as the build progresses.

  Fields:
    allowExitCodes: Allow this build step to fail without failing the entire
      build if and only if the exit code is one of the specified codes. If
      allow_failure is also specified, this field will take precedence.
    allowFailure: Allow this build step to fail without failing the entire
      build. If false, the entire build will fail if this step fails.
      Otherwise, the build will succeed, but this step will still have a
      failure status. Error information will be reported in the failure_detail
      field.
    args: A list of arguments that will be presented to the step when it is
      started. If the image used to run the step's container has an
      entrypoint, the `args` are used as arguments to that entrypoint. If the
      image does not define an entrypoint, the first element in args is used
      as the entrypoint, and the remainder will be used as arguments.
    automapSubstitutions: Option to include built-in and custom substitutions
      as env variables for this build step. This option will override the
      global option in BuildOption.
    dir: Working directory to use when running this step's container. If this
      value is a relative path, it is relative to the build's working
      directory. If this value is absolute, it may be outside the build's
      working directory, in which case the contents of the path may not be
      persisted across build step executions, unless a `volume` for that path
      is specified. If the build specifies a `RepoSource` with `dir` and a
      step with a `dir`, which specifies an absolute path, the `RepoSource`
      `dir` is ignored for the step's execution.
    entrypoint: Entrypoint to be used instead of the build step image's
      default entrypoint. If unset, the image's default entrypoint is used.
    env: A list of environment variable definitions to be used when running a
      step. The elements are of the form "KEY=VALUE" for the environment
      variable "KEY" being given the value "VALUE".
    exitCode: Output only. Return code from running the step.
    id: Unique identifier for this build step, used in `wait_for` to reference
      this build step as a dependency.
    name: Required. The name of the container image that will run this
      particular build step. If the image is available in the host's Docker
      daemon's cache, it will be run directly. If not, the host will attempt
      to pull the image first, using the builder service account's credentials
      if necessary. The Docker daemon's cache will already have the latest
      versions of all of the officially supported build steps
      ([https://github.com/GoogleCloudPlatform/cloud-
      builders](https://github.com/GoogleCloudPlatform/cloud-builders)). The
      Docker daemon will also have cached many of the layers for some popular
      images, like "ubuntu", "debian", but they will be refreshed at the time
      you attempt to use them. If you built an image in a previous build step,
      it will be stored in the host's Docker daemon's cache and is available
      to use as the name for a later build step.
    pullTiming: Output only. Stores timing information for pulling this build
      step's builder image only.
    script: A shell script to be executed in the step. When script is
      provided, the user cannot specify the entrypoint or args.
    secretEnv: A list of environment variables which are encrypted using a
      Cloud Key Management Service crypto key. These values must be specified
      in the build's `Secret`.
    status: Output only. Status of the build step. At this time, build step
      status is only updated on build completion; step status is not updated
      in real-time as the build progresses.
    timeout: Time limit for executing this build step. If not defined, the
      step has no time limit and will be allowed to continue to run until
      either it completes or the build itself times out.
    timing: Output only. Stores timing information for executing this build
      step.
    volumes: List of volumes to mount into the build step. Each volume is
      created as an empty volume prior to execution of the build step. Upon
      completion of the build, volumes and their contents are discarded. Using
      a named volume in only one step is not valid as it is indicative of a
      build request with an incorrect configuration.
    waitFor: The ID(s) of the step(s) that this build step depends on. This
      build step will not start until all the build steps in `wait_for` have
      completed successfully. If `wait_for` is empty, this build step will
      start when all previous build steps in the `Build.Steps` list have
      completed successfully.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. Status of the build step. At this time, build step status
    is only updated on build completion; step status is not updated in real-
    time as the build progresses.

    Values:
      STATUS_UNKNOWN: Status of the build is unknown.
      PENDING: Build has been created and is pending execution and queuing. It
        has not been queued.
      QUEUING: Build has been received and is being queued.
      QUEUED: Build or step is queued; work has not yet begun.
      WORKING: Build or step is being executed.
      SUCCESS: Build or step finished successfully.
      FAILURE: Build or step failed to complete successfully.
      INTERNAL_ERROR: Build or step failed due to an internal cause.
      TIMEOUT: Build or step took longer than was allowed.
      CANCELLED: Build or step was canceled by a user.
      EXPIRED: Build was enqueued for longer than the value of `queue_ttl`.
    """
    STATUS_UNKNOWN = 0
    PENDING = 1
    QUEUING = 2
    QUEUED = 3
    WORKING = 4
    SUCCESS = 5
    FAILURE = 6
    INTERNAL_ERROR = 7
    TIMEOUT = 8
    CANCELLED = 9
    EXPIRED = 10

  allowExitCodes = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)
  allowFailure = _messages.BooleanField(2)
  args = _messages.StringField(3, repeated=True)
  automapSubstitutions = _messages.BooleanField(4)
  dir = _messages.StringField(5)
  entrypoint = _messages.StringField(6)
  env = _messages.StringField(7, repeated=True)
  exitCode = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  id = _messages.StringField(9)
  name = _messages.StringField(10)
  pullTiming = _messages.MessageField('TimeSpan', 11)
  script = _messages.StringField(12)
  secretEnv = _messages.StringField(13, repeated=True)
  status = _messages.EnumField('StatusValueValuesEnum', 14)
  timeout = _messages.StringField(15)
  timing = _messages.MessageField('TimeSpan', 16)
  volumes = _messages.MessageField('Volume', 17, repeated=True)
  waitFor = _messages.StringField(18, repeated=True)


class BuildType(_messages.Message):
  r"""Note holding the version of the provider's builder and the signature of
  the provenance message in linked BuildDetails.

  Fields:
    builderVersion: Version of the builder which produced this Note.
    signature: Signature of the build in Occurrences pointing to the Note
      containing this `BuilderDetails`.
  """

  builderVersion = _messages.StringField(1)
  signature = _messages.MessageField('BuildSignature', 2)


class BuilderConfig(_messages.Message):
  r"""A BuilderConfig object.

  Fields:
    id: A string attribute.
  """

  id = _messages.StringField(1)


class CVSS(_messages.Message):
  r"""Common Vulnerability Scoring System. This message is compatible with
  CVSS v2 and v3. For CVSS v2 details, see https://www.first.org/cvss/v2/guide
  CVSS v2 calculator: https://nvd.nist.gov/vuln-metrics/cvss/v2-calculator For
  CVSS v3 details, see https://www.first.org/cvss/specification-document CVSS
  v3 calculator: https://nvd.nist.gov/vuln-metrics/cvss/v3-calculator

  Enums:
    AttackComplexityValueValuesEnum: Defined in CVSS v3, CVSS v2
    AttackVectorValueValuesEnum: Base Metrics Represents the intrinsic
      characteristics of a vulnerability that are constant over time and
      across user environments. Defined in CVSS v3, CVSS v2
    AuthenticationValueValuesEnum: Defined in CVSS v2
    AvailabilityImpactValueValuesEnum: Defined in CVSS v3, CVSS v2
    ConfidentialityImpactValueValuesEnum: Defined in CVSS v3, CVSS v2
    IntegrityImpactValueValuesEnum: Defined in CVSS v3, CVSS v2
    PrivilegesRequiredValueValuesEnum: Defined in CVSS v3
    ScopeValueValuesEnum: Defined in CVSS v3
    UserInteractionValueValuesEnum: Defined in CVSS v3

  Fields:
    attackComplexity: Defined in CVSS v3, CVSS v2
    attackVector: Base Metrics Represents the intrinsic characteristics of a
      vulnerability that are constant over time and across user environments.
      Defined in CVSS v3, CVSS v2
    authentication: Defined in CVSS v2
    availabilityImpact: Defined in CVSS v3, CVSS v2
    baseScore: The base score is a function of the base metric scores.
    confidentialityImpact: Defined in CVSS v3, CVSS v2
    exploitabilityScore: A number attribute.
    impactScore: A number attribute.
    integrityImpact: Defined in CVSS v3, CVSS v2
    privilegesRequired: Defined in CVSS v3
    scope: Defined in CVSS v3
    userInteraction: Defined in CVSS v3
  """

  class AttackComplexityValueValuesEnum(_messages.Enum):
    r"""Defined in CVSS v3, CVSS v2

    Values:
      ATTACK_COMPLEXITY_UNSPECIFIED: Defined in CVSS v3, CVSS v2
      ATTACK_COMPLEXITY_LOW: Defined in CVSS v3, CVSS v2
      ATTACK_COMPLEXITY_HIGH: Defined in CVSS v3, CVSS v2
      ATTACK_COMPLEXITY_MEDIUM: Defined in CVSS v2
    """
    ATTACK_COMPLEXITY_UNSPECIFIED = 0
    ATTACK_COMPLEXITY_LOW = 1
    ATTACK_COMPLEXITY_HIGH = 2
    ATTACK_COMPLEXITY_MEDIUM = 3

  class AttackVectorValueValuesEnum(_messages.Enum):
    r"""Base Metrics Represents the intrinsic characteristics of a
    vulnerability that are constant over time and across user environments.
    Defined in CVSS v3, CVSS v2

    Values:
      ATTACK_VECTOR_UNSPECIFIED: Defined in CVSS v3, CVSS v2
      ATTACK_VECTOR_NETWORK: Defined in CVSS v3, CVSS v2
      ATTACK_VECTOR_ADJACENT: Defined in CVSS v3, CVSS v2
      ATTACK_VECTOR_LOCAL: Defined in CVSS v3, CVSS v2
      ATTACK_VECTOR_PHYSICAL: Defined in CVSS v3
    """
    ATTACK_VECTOR_UNSPECIFIED = 0
    ATTACK_VECTOR_NETWORK = 1
    ATTACK_VECTOR_ADJACENT = 2
    ATTACK_VECTOR_LOCAL = 3
    ATTACK_VECTOR_PHYSICAL = 4

  class AuthenticationValueValuesEnum(_messages.Enum):
    r"""Defined in CVSS v2

    Values:
      AUTHENTICATION_UNSPECIFIED: Defined in CVSS v2
      AUTHENTICATION_MULTIPLE: Defined in CVSS v2
      AUTHENTICATION_SINGLE: Defined in CVSS v2
      AUTHENTICATION_NONE: Defined in CVSS v2
    """
    AUTHENTICATION_UNSPECIFIED = 0
    AUTHENTICATION_MULTIPLE = 1
    AUTHENTICATION_SINGLE = 2
    AUTHENTICATION_NONE = 3

  class AvailabilityImpactValueValuesEnum(_messages.Enum):
    r"""Defined in CVSS v3, CVSS v2

    Values:
      IMPACT_UNSPECIFIED: Defined in CVSS v3, CVSS v2
      IMPACT_HIGH: Defined in CVSS v3
      IMPACT_LOW: Defined in CVSS v3
      IMPACT_NONE: Defined in CVSS v3, CVSS v2
      IMPACT_PARTIAL: Defined in CVSS v2
      IMPACT_COMPLETE: Defined in CVSS v2
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3
    IMPACT_PARTIAL = 4
    IMPACT_COMPLETE = 5

  class ConfidentialityImpactValueValuesEnum(_messages.Enum):
    r"""Defined in CVSS v3, CVSS v2

    Values:
      IMPACT_UNSPECIFIED: Defined in CVSS v3, CVSS v2
      IMPACT_HIGH: Defined in CVSS v3
      IMPACT_LOW: Defined in CVSS v3
      IMPACT_NONE: Defined in CVSS v3, CVSS v2
      IMPACT_PARTIAL: Defined in CVSS v2
      IMPACT_COMPLETE: Defined in CVSS v2
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3
    IMPACT_PARTIAL = 4
    IMPACT_COMPLETE = 5

  class IntegrityImpactValueValuesEnum(_messages.Enum):
    r"""Defined in CVSS v3, CVSS v2

    Values:
      IMPACT_UNSPECIFIED: Defined in CVSS v3, CVSS v2
      IMPACT_HIGH: Defined in CVSS v3
      IMPACT_LOW: Defined in CVSS v3
      IMPACT_NONE: Defined in CVSS v3, CVSS v2
      IMPACT_PARTIAL: Defined in CVSS v2
      IMPACT_COMPLETE: Defined in CVSS v2
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3
    IMPACT_PARTIAL = 4
    IMPACT_COMPLETE = 5

  class PrivilegesRequiredValueValuesEnum(_messages.Enum):
    r"""Defined in CVSS v3

    Values:
      PRIVILEGES_REQUIRED_UNSPECIFIED: Defined in CVSS v3
      PRIVILEGES_REQUIRED_NONE: Defined in CVSS v3
      PRIVILEGES_REQUIRED_LOW: Defined in CVSS v3
      PRIVILEGES_REQUIRED_HIGH: Defined in CVSS v3
    """
    PRIVILEGES_REQUIRED_UNSPECIFIED = 0
    PRIVILEGES_REQUIRED_NONE = 1
    PRIVILEGES_REQUIRED_LOW = 2
    PRIVILEGES_REQUIRED_HIGH = 3

  class ScopeValueValuesEnum(_messages.Enum):
    r"""Defined in CVSS v3

    Values:
      SCOPE_UNSPECIFIED: Defined in CVSS v3
      SCOPE_UNCHANGED: Defined in CVSS v3
      SCOPE_CHANGED: Defined in CVSS v3
    """
    SCOPE_UNSPECIFIED = 0
    SCOPE_UNCHANGED = 1
    SCOPE_CHANGED = 2

  class UserInteractionValueValuesEnum(_messages.Enum):
    r"""Defined in CVSS v3

    Values:
      USER_INTERACTION_UNSPECIFIED: Defined in CVSS v3
      USER_INTERACTION_NONE: Defined in CVSS v3
      USER_INTERACTION_REQUIRED: Defined in CVSS v3
    """
    USER_INTERACTION_UNSPECIFIED = 0
    USER_INTERACTION_NONE = 1
    USER_INTERACTION_REQUIRED = 2

  attackComplexity = _messages.EnumField('AttackComplexityValueValuesEnum', 1)
  attackVector = _messages.EnumField('AttackVectorValueValuesEnum', 2)
  authentication = _messages.EnumField('AuthenticationValueValuesEnum', 3)
  availabilityImpact = _messages.EnumField('AvailabilityImpactValueValuesEnum', 4)
  baseScore = _messages.FloatField(5, variant=_messages.Variant.FLOAT)
  confidentialityImpact = _messages.EnumField('ConfidentialityImpactValueValuesEnum', 6)
  exploitabilityScore = _messages.FloatField(7, variant=_messages.Variant.FLOAT)
  impactScore = _messages.FloatField(8, variant=_messages.Variant.FLOAT)
  integrityImpact = _messages.EnumField('IntegrityImpactValueValuesEnum', 9)
  privilegesRequired = _messages.EnumField('PrivilegesRequiredValueValuesEnum', 10)
  scope = _messages.EnumField('ScopeValueValuesEnum', 11)
  userInteraction = _messages.EnumField('UserInteractionValueValuesEnum', 12)


class CisBenchmark(_messages.Message):
  r"""A compliance check that is a CIS benchmark.

  Enums:
    SeverityValueValuesEnum: The severity level of this CIS benchmark check.

  Fields:
    profileLevel: The profile level of this CIS benchmark check.
    severity: The severity level of this CIS benchmark check.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity level of this CIS benchmark check.

    Values:
      SEVERITY_UNSPECIFIED: Unknown Impact
      MINIMAL: Minimal Impact
      LOW: Low Impact
      MEDIUM: Medium Impact
      HIGH: High Impact
      CRITICAL: Critical Impact
    """
    SEVERITY_UNSPECIFIED = 0
    MINIMAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5

  profileLevel = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  severity = _messages.EnumField('SeverityValueValuesEnum', 2)


class Command(_messages.Message):
  r"""Command describes a step performed as part of the build pipeline.

  Fields:
    args: Command-line arguments used when executing this Command.
    dir: Working directory (relative to project source root) used when running
      this Command.
    env: Environment variables set before running this Command.
    id: Optional unique identifier for this Command, used in wait_for to
      reference this Command as a dependency.
    name: Name of the command, as presented on the command line, or if the
      command is packaged as a Docker container, as presented to `docker
      pull`.
    waitFor: The ID(s) of the Command(s) that this Command depends on.
  """

  args = _messages.StringField(1, repeated=True)
  dir = _messages.StringField(2)
  env = _messages.StringField(3, repeated=True)
  id = _messages.StringField(4)
  name = _messages.StringField(5)
  waitFor = _messages.StringField(6, repeated=True)


class Completeness(_messages.Message):
  r"""Indicates that the builder claims certain fields in this message to be
  complete.

  Fields:
    arguments: If true, the builder claims that recipe.arguments is complete,
      meaning that all external inputs are properly captured in the recipe.
    environment: If true, the builder claims that recipe.environment is
      claimed to be complete.
    materials: If true, the builder claims that materials are complete,
      usually through some controls to prevent network access. Sometimes
      called "hermetic".
  """

  arguments = _messages.BooleanField(1)
  environment = _messages.BooleanField(2)
  materials = _messages.BooleanField(3)


class ComplianceNote(_messages.Message):
  r"""ComplianceNote encapsulates all information about a specific compliance
  check.

  Fields:
    cisBenchmark: Right now we only have one compliance type, but we may add
      additional types in the future.
    description: A description about this compliance check.
    rationale: A rationale for the existence of this compliance check.
    remediation: A description of remediation steps if the compliance check
      fails.
    scanInstructions: Serialized scan instructions with a predefined format.
    title: The title that identifies this compliance check.
    version: The OS and config versions the benchmark applies to.
  """

  cisBenchmark = _messages.MessageField('CisBenchmark', 1)
  description = _messages.StringField(2)
  rationale = _messages.StringField(3)
  remediation = _messages.StringField(4)
  scanInstructions = _messages.BytesField(5)
  title = _messages.StringField(6)
  version = _messages.MessageField('ComplianceVersion', 7, repeated=True)


class ComplianceOccurrence(_messages.Message):
  r"""An indication that the compliance checks in the associated
  ComplianceNote were not satisfied for particular resources or a specified
  reason.

  Fields:
    nonComplianceReason: The reason for non compliance of these files.
    nonCompliantFiles: A list of files which are violating compliance checks.
  """

  nonComplianceReason = _messages.StringField(1)
  nonCompliantFiles = _messages.MessageField('NonCompliantFile', 2, repeated=True)


class ComplianceVersion(_messages.Message):
  r"""Describes the CIS benchmark version that is applicable to a given OS and
  os version.

  Fields:
    benchmarkDocument: The name of the document that defines this benchmark,
      e.g. "CIS Container-Optimized OS".
    cpeUri: The CPE URI (https://cpe.mitre.org/specification/) this benchmark
      is applicable to.
    version: The version of the benchmark. This is set to the version of the
      OS-specific CIS document the benchmark is defined in.
  """

  benchmarkDocument = _messages.StringField(1)
  cpeUri = _messages.StringField(2)
  version = _messages.StringField(3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1ApprovalConfig(_messages.Message):
  r"""ApprovalConfig describes configuration for manual approval of a build.

  Fields:
    approvalRequired: Whether or not approval is needed. If this is set on a
      build, it will become pending when created, and will need to be
      explicitly approved to start.
  """

  approvalRequired = _messages.BooleanField(1)


class ContaineranalysisGoogleDevtoolsCloudbuildV1ApprovalResult(_messages.Message):
  r"""ApprovalResult describes the decision and associated metadata of a
  manual approval of a build.

  Enums:
    DecisionValueValuesEnum: Required. The decision of this manual approval.

  Fields:
    approvalTime: Output only. The time when the approval decision was made.
    approverAccount: Output only. Email of the user that called the
      ApproveBuild API to approve or reject a build at the time that the API
      was called.
    comment: Optional. An optional comment for this manual approval result.
    decision: Required. The decision of this manual approval.
    url: Optional. An optional URL tied to this manual approval result. This
      field is essentially the same as comment, except that it will be
      rendered by the UI differently. An example use case is a link to an
      external job that approved this Build.
  """

  class DecisionValueValuesEnum(_messages.Enum):
    r"""Required. The decision of this manual approval.

    Values:
      DECISION_UNSPECIFIED: Default enum type. This should not be used.
      APPROVED: Build is approved.
      REJECTED: Build is rejected.
    """
    DECISION_UNSPECIFIED = 0
    APPROVED = 1
    REJECTED = 2

  approvalTime = _messages.StringField(1)
  approverAccount = _messages.StringField(2)
  comment = _messages.StringField(3)
  decision = _messages.EnumField('DecisionValueValuesEnum', 4)
  url = _messages.StringField(5)


class ContaineranalysisGoogleDevtoolsCloudbuildV1Artifacts(_messages.Message):
  r"""Artifacts produced by a build that should be uploaded upon successful
  completion of all build steps.

  Fields:
    images: A list of images to be pushed upon the successful completion of
      all build steps. The images will be pushed using the builder service
      account's credentials. The digests of the pushed images will be stored
      in the Build resource's results field. If any of the images fail to be
      pushed, the build is marked FAILURE.
    mavenArtifacts: A list of Maven artifacts to be uploaded to Artifact
      Registry upon successful completion of all build steps. Artifacts in the
      workspace matching specified paths globs will be uploaded to the
      specified Artifact Registry repository using the builder service
      account's credentials. If any artifacts fail to be pushed, the build is
      marked FAILURE.
    npmPackages: A list of npm packages to be uploaded to Artifact Registry
      upon successful completion of all build steps. Npm packages in the
      specified paths will be uploaded to the specified Artifact Registry
      repository using the builder service account's credentials. If any
      packages fail to be pushed, the build is marked FAILURE.
    objects: A list of objects to be uploaded to Cloud Storage upon successful
      completion of all build steps. Files in the workspace matching specified
      paths globs will be uploaded to the specified Cloud Storage location
      using the builder service account's credentials. The location and
      generation of the uploaded objects will be stored in the Build
      resource's results field. If any objects fail to be pushed, the build is
      marked FAILURE.
    pythonPackages: A list of Python packages to be uploaded to Artifact
      Registry upon successful completion of all build steps. The build
      service account credentials will be used to perform the upload. If any
      objects fail to be pushed, the build is marked FAILURE.
  """

  images = _messages.StringField(1, repeated=True)
  mavenArtifacts = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsMavenArtifact', 2, repeated=True)
  npmPackages = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsNpmPackage', 3, repeated=True)
  objects = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsArtifactObjects', 4)
  pythonPackages = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsPythonPackage', 5, repeated=True)


class ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsArtifactObjects(_messages.Message):
  r"""Files in the workspace to upload to Cloud Storage upon successful
  completion of all build steps.

  Fields:
    location: Cloud Storage bucket and optional object path, in the form
      "gs://bucket/path/to/somewhere/". (see [Bucket Name
      Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)). Files in the workspace matching any path pattern
      will be uploaded to Cloud Storage with this location as a prefix.
    paths: Path globs used to match files in the build's workspace.
    timing: Output only. Stores timing information for pushing all artifact
      objects.
  """

  location = _messages.StringField(1)
  paths = _messages.StringField(2, repeated=True)
  timing = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsMavenArtifact(_messages.Message):
  r"""A Maven artifact to upload to Artifact Registry upon successful
  completion of all build steps.

  Fields:
    artifactId: Maven `artifactId` value used when uploading the artifact to
      Artifact Registry.
    groupId: Maven `groupId` value used when uploading the artifact to
      Artifact Registry.
    path: Path to an artifact in the build's workspace to be uploaded to
      Artifact Registry. This can be either an absolute path, e.g.
      /workspace/my-app/target/my-app-1.0.SNAPSHOT.jar or a relative path from
      /workspace, e.g. my-app/target/my-app-1.0.SNAPSHOT.jar.
    repository: Artifact Registry repository, in the form "https://$REGION-
      maven.pkg.dev/$PROJECT/$REPOSITORY" Artifact in the workspace specified
      by path will be uploaded to Artifact Registry with this location as a
      prefix.
    version: Maven `version` value used when uploading the artifact to
      Artifact Registry.
  """

  artifactId = _messages.StringField(1)
  groupId = _messages.StringField(2)
  path = _messages.StringField(3)
  repository = _messages.StringField(4)
  version = _messages.StringField(5)


class ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsNpmPackage(_messages.Message):
  r"""Npm package to upload to Artifact Registry upon successful completion of
  all build steps.

  Fields:
    packagePath: Path to the package.json. e.g. workspace/path/to/package
    repository: Artifact Registry repository, in the form "https://$REGION-
      npm.pkg.dev/$PROJECT/$REPOSITORY" Npm package in the workspace specified
      by path will be zipped and uploaded to Artifact Registry with this
      location as a prefix.
  """

  packagePath = _messages.StringField(1)
  repository = _messages.StringField(2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsPythonPackage(_messages.Message):
  r"""Python package to upload to Artifact Registry upon successful completion
  of all build steps. A package can encapsulate multiple objects to be
  uploaded to a single repository.

  Fields:
    paths: Path globs used to match files in the build's workspace. For
      Python/ Twine, this is usually `dist/*`, and sometimes additionally an
      `.asc` file.
    repository: Artifact Registry repository, in the form "https://$REGION-
      python.pkg.dev/$PROJECT/$REPOSITORY" Files in the workspace matching any
      path pattern will be uploaded to Artifact Registry with this location as
      a prefix.
  """

  paths = _messages.StringField(1, repeated=True)
  repository = _messages.StringField(2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1Build(_messages.Message):
  r"""A build resource in the Cloud Build API. At a high level, a `Build`
  describes where to find source code, how to build it (for example, the
  builder image to run on the source), and where to store the built artifacts.
  Fields can include the following variables, which will be expanded when the
  build is created: - $PROJECT_ID: the project ID of the build. -
  $PROJECT_NUMBER: the project number of the build. - $LOCATION: the
  location/region of the build. - $BUILD_ID: the autogenerated ID of the
  build. - $REPO_NAME: the source repository name specified by RepoSource. -
  $BRANCH_NAME: the branch name specified by RepoSource. - $TAG_NAME: the tag
  name specified by RepoSource. - $REVISION_ID or $COMMIT_SHA: the commit SHA
  specified by RepoSource or resolved from the specified branch or tag. -
  $SHORT_SHA: first 7 characters of $REVISION_ID or $COMMIT_SHA.

  Enums:
    StatusValueValuesEnum: Output only. Status of the build.

  Messages:
    SubstitutionsValue: Substitutions data for `Build` resource.
    TimingValue: Output only. Stores timing information for phases of the
      build. Valid keys are: * BUILD: time to execute all build steps. * PUSH:
      time to push all artifacts including docker images and non docker
      artifacts. * FETCHSOURCE: time to fetch source. * SETUPBUILD: time to
      set up build. If the build does not specify source or images, these keys
      will not be included.

  Fields:
    approval: Output only. Describes this build's approval configuration,
      status, and result.
    artifacts: Artifacts produced by the build that should be uploaded upon
      successful completion of all build steps.
    availableSecrets: Secrets and secret environment variables.
    buildTriggerId: Output only. The ID of the `BuildTrigger` that triggered
      this build, if it was triggered automatically.
    createTime: Output only. Time at which the request to create the build was
      received.
    failureInfo: Output only. Contains information about the build when
      status=FAILURE.
    finishTime: Output only. Time at which execution of the build was
      finished. The difference between finish_time and start_time is the
      duration of the build's execution.
    id: Output only. Unique identifier of the build.
    images: A list of images to be pushed upon the successful completion of
      all build steps. The images are pushed using the builder service
      account's credentials. The digests of the pushed images will be stored
      in the `Build` resource's results field. If any of the images fail to be
      pushed, the build status is marked `FAILURE`.
    logUrl: Output only. URL to logs for this build in Google Cloud Console.
    logsBucket: Cloud Storage bucket where logs should be written (see [Bucket
      Name Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)). Logs file names will be of the format
      `${logs_bucket}/log-${build_id}.txt`.
    name: Output only. The 'Build' name with format:
      `projects/{project}/locations/{location}/builds/{build}`, where {build}
      is a unique identifier generated by the service.
    options: Special options for this build.
    projectId: Output only. ID of the project.
    queueTtl: TTL in queue for this build. If provided and the build is
      enqueued longer than this value, the build will expire and the build
      status will be `EXPIRED`. The TTL starts ticking from create_time.
    results: Output only. Results of the build.
    secrets: Secrets to decrypt using Cloud Key Management Service. Note:
      Secret Manager is the recommended technique for managing sensitive data
      with Cloud Build. Use `available_secrets` to configure builds to access
      secrets from Secret Manager. For instructions, see:
      https://cloud.google.com/cloud-build/docs/securing-builds/use-secrets
    serviceAccount: IAM service account whose credentials will be used at
      build runtime. Must be of the format
      `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`. ACCOUNT can be email
      address or uniqueId of the service account.
    source: The location of the source files to build.
    sourceProvenance: Output only. A permanent fixed identifier for source.
    startTime: Output only. Time at which execution of the build was started.
    status: Output only. Status of the build.
    statusDetail: Output only. Customer-readable message about the current
      status.
    steps: Required. The operations to be performed on the workspace.
    substitutions: Substitutions data for `Build` resource.
    tags: Tags for annotation of a `Build`. These are not docker tags.
    timeout: Amount of time that this build should be allowed to run, to
      second granularity. If this amount of time elapses, work on the build
      will cease and the build status will be `TIMEOUT`. `timeout` starts
      ticking from `startTime`. Default time is 60 minutes.
    timing: Output only. Stores timing information for phases of the build.
      Valid keys are: * BUILD: time to execute all build steps. * PUSH: time
      to push all artifacts including docker images and non docker artifacts.
      * FETCHSOURCE: time to fetch source. * SETUPBUILD: time to set up build.
      If the build does not specify source or images, these keys will not be
      included.
    warnings: Output only. Non-fatal problems encountered during the execution
      of the build.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. Status of the build.

    Values:
      STATUS_UNKNOWN: Status of the build is unknown.
      PENDING: Build has been created and is pending execution and queuing. It
        has not been queued.
      QUEUED: Build or step is queued; work has not yet begun.
      WORKING: Build or step is being executed.
      SUCCESS: Build or step finished successfully.
      FAILURE: Build or step failed to complete successfully.
      INTERNAL_ERROR: Build or step failed due to an internal cause.
      TIMEOUT: Build or step took longer than was allowed.
      CANCELLED: Build or step was canceled by a user.
      EXPIRED: Build was enqueued for longer than the value of `queue_ttl`.
    """
    STATUS_UNKNOWN = 0
    PENDING = 1
    QUEUED = 2
    WORKING = 3
    SUCCESS = 4
    FAILURE = 5
    INTERNAL_ERROR = 6
    TIMEOUT = 7
    CANCELLED = 8
    EXPIRED = 9

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SubstitutionsValue(_messages.Message):
    r"""Substitutions data for `Build` resource.

    Messages:
      AdditionalProperty: An additional property for a SubstitutionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type SubstitutionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SubstitutionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TimingValue(_messages.Message):
    r"""Output only. Stores timing information for phases of the build. Valid
    keys are: * BUILD: time to execute all build steps. * PUSH: time to push
    all artifacts including docker images and non docker artifacts. *
    FETCHSOURCE: time to fetch source. * SETUPBUILD: time to set up build. If
    the build does not specify source or images, these keys will not be
    included.

    Messages:
      AdditionalProperty: An additional property for a TimingValue object.

    Fields:
      additionalProperties: Additional properties of type TimingValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TimingValue object.

      Fields:
        key: Name of the additional property.
        value: A ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  approval = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1BuildApproval', 1)
  artifacts = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1Artifacts', 2)
  availableSecrets = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1Secrets', 3)
  buildTriggerId = _messages.StringField(4)
  createTime = _messages.StringField(5)
  failureInfo = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1BuildFailureInfo', 6)
  finishTime = _messages.StringField(7)
  id = _messages.StringField(8)
  images = _messages.StringField(9, repeated=True)
  logUrl = _messages.StringField(10)
  logsBucket = _messages.StringField(11)
  name = _messages.StringField(12)
  options = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1BuildOptions', 13)
  projectId = _messages.StringField(14)
  queueTtl = _messages.StringField(15)
  results = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1Results', 16)
  secrets = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1Secret', 17, repeated=True)
  serviceAccount = _messages.StringField(18)
  source = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1Source', 19)
  sourceProvenance = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1SourceProvenance', 20)
  startTime = _messages.StringField(21)
  status = _messages.EnumField('StatusValueValuesEnum', 22)
  statusDetail = _messages.StringField(23)
  steps = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1BuildStep', 24, repeated=True)
  substitutions = _messages.MessageField('SubstitutionsValue', 25)
  tags = _messages.StringField(26, repeated=True)
  timeout = _messages.StringField(27)
  timing = _messages.MessageField('TimingValue', 28)
  warnings = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1BuildWarning', 29, repeated=True)


class ContaineranalysisGoogleDevtoolsCloudbuildV1BuildApproval(_messages.Message):
  r"""BuildApproval describes a build's approval configuration, state, and
  result.

  Enums:
    StateValueValuesEnum: Output only. The state of this build's approval.

  Fields:
    config: Output only. Configuration for manual approval of this build.
    result: Output only. Result of manual approval for this Build.
    state: Output only. The state of this build's approval.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of this build's approval.

    Values:
      STATE_UNSPECIFIED: Default enum type. This should not be used.
      PENDING: Build approval is pending.
      APPROVED: Build approval has been approved.
      REJECTED: Build approval has been rejected.
      CANCELLED: Build was cancelled while it was still pending approval.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    APPROVED = 2
    REJECTED = 3
    CANCELLED = 4

  config = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1ApprovalConfig', 1)
  result = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1ApprovalResult', 2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1BuildFailureInfo(_messages.Message):
  r"""A fatal problem encountered during the execution of the build.

  Enums:
    TypeValueValuesEnum: The name of the failure.

  Fields:
    detail: Explains the failure issue in more detail using hard-coded text.
    type: The name of the failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The name of the failure.

    Values:
      FAILURE_TYPE_UNSPECIFIED: Type unspecified
      PUSH_FAILED: Unable to push the image to the repository.
      PUSH_IMAGE_NOT_FOUND: Final image not found.
      PUSH_NOT_AUTHORIZED: Unauthorized push of the final image.
      LOGGING_FAILURE: Backend logging failures. Should retry.
      USER_BUILD_STEP: A build step has failed.
      FETCH_SOURCE_FAILED: The source fetching has failed.
    """
    FAILURE_TYPE_UNSPECIFIED = 0
    PUSH_FAILED = 1
    PUSH_IMAGE_NOT_FOUND = 2
    PUSH_NOT_AUTHORIZED = 3
    LOGGING_FAILURE = 4
    USER_BUILD_STEP = 5
    FETCH_SOURCE_FAILED = 6

  detail = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1BuildOptions(_messages.Message):
  r"""Optional arguments to enable specific features of builds.

  Enums:
    DefaultLogsBucketBehaviorValueValuesEnum: Optional. Option to specify how
      default logs buckets are setup.
    LogStreamingOptionValueValuesEnum: Option to define build log streaming
      behavior to Cloud Storage.
    LoggingValueValuesEnum: Option to specify the logging mode, which
      determines if and where build logs are stored.
    MachineTypeValueValuesEnum: Compute Engine machine type on which to run
      the build.
    RequestedVerifyOptionValueValuesEnum: Requested verifiability options.
    SourceProvenanceHashValueListEntryValuesEnum:
    SubstitutionOptionValueValuesEnum: Option to specify behavior when there
      is an error in the substitution checks. NOTE: this is always set to
      ALLOW_LOOSE for triggered builds and cannot be overridden in the build
      configuration file.

  Fields:
    automapSubstitutions: Option to include built-in and custom substitutions
      as env variables for all build steps.
    defaultLogsBucketBehavior: Optional. Option to specify how default logs
      buckets are setup.
    diskSizeGb: Requested disk size for the VM that runs the build. Note that
      this is *NOT* "disk free"; some of the space will be used by the
      operating system and build utilities. Also note that this is the minimum
      disk size that will be allocated for the build -- the build may run with
      a larger disk than requested. At present, the maximum disk size is
      2000GB; builds that request more than the maximum are rejected with an
      error.
    dynamicSubstitutions: Option to specify whether or not to apply bash style
      string operations to the substitutions. NOTE: this is always enabled for
      triggered builds and cannot be overridden in the build configuration
      file.
    env: A list of global environment variable definitions that will exist for
      all build steps in this build. If a variable is defined in both globally
      and in a build step, the variable will use the build step value. The
      elements are of the form "KEY=VALUE" for the environment variable "KEY"
      being given the value "VALUE".
    logStreamingOption: Option to define build log streaming behavior to Cloud
      Storage.
    logging: Option to specify the logging mode, which determines if and where
      build logs are stored.
    machineType: Compute Engine machine type on which to run the build.
    pool: Optional. Specification for execution on a `WorkerPool`. See
      [running builds in a private
      pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-
      private-pool) for more information.
    requestedVerifyOption: Requested verifiability options.
    secretEnv: A list of global environment variables, which are encrypted
      using a Cloud Key Management Service crypto key. These values must be
      specified in the build's `Secret`. These variables will be available to
      all build steps in this build.
    sourceProvenanceHash: Requested hash for SourceProvenance.
    substitutionOption: Option to specify behavior when there is an error in
      the substitution checks. NOTE: this is always set to ALLOW_LOOSE for
      triggered builds and cannot be overridden in the build configuration
      file.
    volumes: Global list of volumes to mount for ALL build steps Each volume
      is created as an empty volume prior to starting the build process. Upon
      completion of the build, volumes and their contents are discarded.
      Global volume names and paths cannot conflict with the volumes defined a
      build step. Using a global volume in a build with only one step is not
      valid as it is indicative of a build request with an incorrect
      configuration.
    workerPool: This field deprecated; please use `pool.name` instead.
  """

  class DefaultLogsBucketBehaviorValueValuesEnum(_messages.Enum):
    r"""Optional. Option to specify how default logs buckets are setup.

    Values:
      DEFAULT_LOGS_BUCKET_BEHAVIOR_UNSPECIFIED: Unspecified.
      REGIONAL_USER_OWNED_BUCKET: Bucket is located in user-owned project in
        the same region as the build. The builder service account must have
        access to create and write to Cloud Storage buckets in the build
        project.
    """
    DEFAULT_LOGS_BUCKET_BEHAVIOR_UNSPECIFIED = 0
    REGIONAL_USER_OWNED_BUCKET = 1

  class LogStreamingOptionValueValuesEnum(_messages.Enum):
    r"""Option to define build log streaming behavior to Cloud Storage.

    Values:
      STREAM_DEFAULT: Service may automatically determine build log streaming
        behavior.
      STREAM_ON: Build logs should be streamed to Cloud Storage.
      STREAM_OFF: Build logs should not be streamed to Cloud Storage; they
        will be written when the build is completed.
    """
    STREAM_DEFAULT = 0
    STREAM_ON = 1
    STREAM_OFF = 2

  class LoggingValueValuesEnum(_messages.Enum):
    r"""Option to specify the logging mode, which determines if and where
    build logs are stored.

    Values:
      LOGGING_UNSPECIFIED: The service determines the logging mode. The
        default is `LEGACY`. Do not rely on the default logging behavior as it
        may change in the future.
      LEGACY: Build logs are stored in Cloud Logging and Cloud Storage.
      GCS_ONLY: Build logs are stored in Cloud Storage.
      STACKDRIVER_ONLY: This option is the same as CLOUD_LOGGING_ONLY.
      CLOUD_LOGGING_ONLY: Build logs are stored in Cloud Logging. Selecting
        this option will not allow [logs
        streaming](https://cloud.google.com/sdk/gcloud/reference/builds/log).
      NONE: Turn off all logging. No build logs will be captured.
    """
    LOGGING_UNSPECIFIED = 0
    LEGACY = 1
    GCS_ONLY = 2
    STACKDRIVER_ONLY = 3
    CLOUD_LOGGING_ONLY = 4
    NONE = 5

  class MachineTypeValueValuesEnum(_messages.Enum):
    r"""Compute Engine machine type on which to run the build.

    Values:
      UNSPECIFIED: Standard machine type.
      N1_HIGHCPU_8: Highcpu machine with 8 CPUs.
      N1_HIGHCPU_32: Highcpu machine with 32 CPUs.
      E2_HIGHCPU_8: Highcpu e2 machine with 8 CPUs.
      E2_HIGHCPU_32: Highcpu e2 machine with 32 CPUs.
      E2_MEDIUM: E2 machine with 1 CPU.
    """
    UNSPECIFIED = 0
    N1_HIGHCPU_8 = 1
    N1_HIGHCPU_32 = 2
    E2_HIGHCPU_8 = 3
    E2_HIGHCPU_32 = 4
    E2_MEDIUM = 5

  class RequestedVerifyOptionValueValuesEnum(_messages.Enum):
    r"""Requested verifiability options.

    Values:
      NOT_VERIFIED: Not a verifiable build (the default).
      VERIFIED: Build must be verified.
    """
    NOT_VERIFIED = 0
    VERIFIED = 1

  class SourceProvenanceHashValueListEntryValuesEnum(_messages.Enum):
    r"""SourceProvenanceHashValueListEntryValuesEnum enum type.

    Values:
      NONE: No hash requested.
      SHA256: Use a sha256 hash.
      MD5: Use a md5 hash.
      SHA512: Use a sha512 hash.
    """
    NONE = 0
    SHA256 = 1
    MD5 = 2
    SHA512 = 3

  class SubstitutionOptionValueValuesEnum(_messages.Enum):
    r"""Option to specify behavior when there is an error in the substitution
    checks. NOTE: this is always set to ALLOW_LOOSE for triggered builds and
    cannot be overridden in the build configuration file.

    Values:
      MUST_MATCH: Fails the build if error in substitutions checks, like
        missing a substitution in the template or in the map.
      ALLOW_LOOSE: Do not fail the build if error in substitutions checks.
    """
    MUST_MATCH = 0
    ALLOW_LOOSE = 1

  automapSubstitutions = _messages.BooleanField(1)
  defaultLogsBucketBehavior = _messages.EnumField('DefaultLogsBucketBehaviorValueValuesEnum', 2)
  diskSizeGb = _messages.IntegerField(3)
  dynamicSubstitutions = _messages.BooleanField(4)
  env = _messages.StringField(5, repeated=True)
  logStreamingOption = _messages.EnumField('LogStreamingOptionValueValuesEnum', 6)
  logging = _messages.EnumField('LoggingValueValuesEnum', 7)
  machineType = _messages.EnumField('MachineTypeValueValuesEnum', 8)
  pool = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1BuildOptionsPoolOption', 9)
  requestedVerifyOption = _messages.EnumField('RequestedVerifyOptionValueValuesEnum', 10)
  secretEnv = _messages.StringField(11, repeated=True)
  sourceProvenanceHash = _messages.EnumField('SourceProvenanceHashValueListEntryValuesEnum', 12, repeated=True)
  substitutionOption = _messages.EnumField('SubstitutionOptionValueValuesEnum', 13)
  volumes = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1Volume', 14, repeated=True)
  workerPool = _messages.StringField(15)


class ContaineranalysisGoogleDevtoolsCloudbuildV1BuildOptionsPoolOption(_messages.Message):
  r"""Details about how a build should be executed on a `WorkerPool`. See
  [running builds in a private
  pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-
  private-pool) for more information.

  Fields:
    name: The `WorkerPool` resource to execute the build on. You must have
      `cloudbuild.workerpools.use` on the project hosting the WorkerPool.
      Format
      projects/{project}/locations/{location}/workerPools/{workerPoolId}
  """

  name = _messages.StringField(1)


class ContaineranalysisGoogleDevtoolsCloudbuildV1BuildStep(_messages.Message):
  r"""A step in the build pipeline.

  Enums:
    StatusValueValuesEnum: Output only. Status of the build step. At this
      time, build step status is only updated on build completion; step status
      is not updated in real-time as the build progresses.

  Fields:
    allowExitCodes: Allow this build step to fail without failing the entire
      build if and only if the exit code is one of the specified codes. If
      allow_failure is also specified, this field will take precedence.
    allowFailure: Allow this build step to fail without failing the entire
      build. If false, the entire build will fail if this step fails.
      Otherwise, the build will succeed, but this step will still have a
      failure status. Error information will be reported in the failure_detail
      field.
    args: A list of arguments that will be presented to the step when it is
      started. If the image used to run the step's container has an
      entrypoint, the `args` are used as arguments to that entrypoint. If the
      image does not define an entrypoint, the first element in args is used
      as the entrypoint, and the remainder will be used as arguments.
    automapSubstitutions: Option to include built-in and custom substitutions
      as env variables for this build step. This option will override the
      global option in BuildOption.
    dir: Working directory to use when running this step's container. If this
      value is a relative path, it is relative to the build's working
      directory. If this value is absolute, it may be outside the build's
      working directory, in which case the contents of the path may not be
      persisted across build step executions, unless a `volume` for that path
      is specified. If the build specifies a `RepoSource` with `dir` and a
      step with a `dir`, which specifies an absolute path, the `RepoSource`
      `dir` is ignored for the step's execution.
    entrypoint: Entrypoint to be used instead of the build step image's
      default entrypoint. If unset, the image's default entrypoint is used.
    env: A list of environment variable definitions to be used when running a
      step. The elements are of the form "KEY=VALUE" for the environment
      variable "KEY" being given the value "VALUE".
    exitCode: Output only. Return code from running the step.
    id: Unique identifier for this build step, used in `wait_for` to reference
      this build step as a dependency.
    name: Required. The name of the container image that will run this
      particular build step. If the image is available in the host's Docker
      daemon's cache, it will be run directly. If not, the host will attempt
      to pull the image first, using the builder service account's credentials
      if necessary. The Docker daemon's cache will already have the latest
      versions of all of the officially supported build steps
      ([https://github.com/GoogleCloudPlatform/cloud-
      builders](https://github.com/GoogleCloudPlatform/cloud-builders)). The
      Docker daemon will also have cached many of the layers for some popular
      images, like "ubuntu", "debian", but they will be refreshed at the time
      you attempt to use them. If you built an image in a previous build step,
      it will be stored in the host's Docker daemon's cache and is available
      to use as the name for a later build step.
    pullTiming: Output only. Stores timing information for pulling this build
      step's builder image only.
    script: A shell script to be executed in the step. When script is
      provided, the user cannot specify the entrypoint or args.
    secretEnv: A list of environment variables which are encrypted using a
      Cloud Key Management Service crypto key. These values must be specified
      in the build's `Secret`.
    status: Output only. Status of the build step. At this time, build step
      status is only updated on build completion; step status is not updated
      in real-time as the build progresses.
    timeout: Time limit for executing this build step. If not defined, the
      step has no time limit and will be allowed to continue to run until
      either it completes or the build itself times out.
    timing: Output only. Stores timing information for executing this build
      step.
    volumes: List of volumes to mount into the build step. Each volume is
      created as an empty volume prior to execution of the build step. Upon
      completion of the build, volumes and their contents are discarded. Using
      a named volume in only one step is not valid as it is indicative of a
      build request with an incorrect configuration.
    waitFor: The ID(s) of the step(s) that this build step depends on. This
      build step will not start until all the build steps in `wait_for` have
      completed successfully. If `wait_for` is empty, this build step will
      start when all previous build steps in the `Build.Steps` list have
      completed successfully.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. Status of the build step. At this time, build step status
    is only updated on build completion; step status is not updated in real-
    time as the build progresses.

    Values:
      STATUS_UNKNOWN: Status of the build is unknown.
      PENDING: Build has been created and is pending execution and queuing. It
        has not been queued.
      QUEUED: Build or step is queued; work has not yet begun.
      WORKING: Build or step is being executed.
      SUCCESS: Build or step finished successfully.
      FAILURE: Build or step failed to complete successfully.
      INTERNAL_ERROR: Build or step failed due to an internal cause.
      TIMEOUT: Build or step took longer than was allowed.
      CANCELLED: Build or step was canceled by a user.
      EXPIRED: Build was enqueued for longer than the value of `queue_ttl`.
    """
    STATUS_UNKNOWN = 0
    PENDING = 1
    QUEUED = 2
    WORKING = 3
    SUCCESS = 4
    FAILURE = 5
    INTERNAL_ERROR = 6
    TIMEOUT = 7
    CANCELLED = 8
    EXPIRED = 9

  allowExitCodes = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)
  allowFailure = _messages.BooleanField(2)
  args = _messages.StringField(3, repeated=True)
  automapSubstitutions = _messages.BooleanField(4)
  dir = _messages.StringField(5)
  entrypoint = _messages.StringField(6)
  env = _messages.StringField(7, repeated=True)
  exitCode = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  id = _messages.StringField(9)
  name = _messages.StringField(10)
  pullTiming = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 11)
  script = _messages.StringField(12)
  secretEnv = _messages.StringField(13, repeated=True)
  status = _messages.EnumField('StatusValueValuesEnum', 14)
  timeout = _messages.StringField(15)
  timing = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 16)
  volumes = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1Volume', 17, repeated=True)
  waitFor = _messages.StringField(18, repeated=True)


class ContaineranalysisGoogleDevtoolsCloudbuildV1BuildWarning(_messages.Message):
  r"""A non-fatal problem encountered during the execution of the build.

  Enums:
    PriorityValueValuesEnum: The priority for this warning.

  Fields:
    priority: The priority for this warning.
    text: Explanation of the warning generated.
  """

  class PriorityValueValuesEnum(_messages.Enum):
    r"""The priority for this warning.

    Values:
      PRIORITY_UNSPECIFIED: Should not be used.
      INFO: e.g. deprecation warnings and alternative feature highlights.
      WARNING: e.g. automated detection of possible issues with the build.
      ALERT: e.g. alerts that a feature used in the build is pending removal
    """
    PRIORITY_UNSPECIFIED = 0
    INFO = 1
    WARNING = 2
    ALERT = 3

  priority = _messages.EnumField('PriorityValueValuesEnum', 1)
  text = _messages.StringField(2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1BuiltImage(_messages.Message):
  r"""An image built by the pipeline.

  Fields:
    digest: Docker Registry 2.0 digest.
    name: Name used to push the container image to Google Container Registry,
      as presented to `docker push`.
    pushTiming: Output only. Stores timing information for pushing the
      specified image.
  """

  digest = _messages.StringField(1)
  name = _messages.StringField(2)
  pushTiming = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes(_messages.Message):
  r"""Container message for hashes of byte content of files, used in
  SourceProvenance messages to verify integrity of source input to the build.

  Fields:
    fileHash: Collection of file hashes.
  """

  fileHash = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1Hash', 1, repeated=True)


class ContaineranalysisGoogleDevtoolsCloudbuildV1GitSource(_messages.Message):
  r"""Location of the source in any accessible Git repository.

  Fields:
    dir: Directory, relative to the source root, in which to run the build.
      This must be a relative path. If a step's `dir` is specified and is an
      absolute path, this value is ignored for that step's execution.
    revision: The revision to fetch from the Git repository such as a branch,
      a tag, a commit SHA, or any Git ref. Cloud Build uses `git fetch` to
      fetch the revision from the Git repository; therefore make sure that the
      string you provide for `revision` is parsable by the command. For
      information on string values accepted by `git fetch`, see https://git-
      scm.com/docs/gitrevisions#_specifying_revisions. For information on `git
      fetch`, see https://git-scm.com/docs/git-fetch.
    url: Location of the Git repo to build. This will be used as a `git
      remote`, see https://git-scm.com/docs/git-remote.
  """

  dir = _messages.StringField(1)
  revision = _messages.StringField(2)
  url = _messages.StringField(3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1Hash(_messages.Message):
  r"""Container message for hash values.

  Enums:
    TypeValueValuesEnum: The type of hash that was performed.

  Fields:
    type: The type of hash that was performed.
    value: The hash value.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of hash that was performed.

    Values:
      NONE: No hash requested.
      SHA256: Use a sha256 hash.
      MD5: Use a md5 hash.
      SHA512: Use a sha512 hash.
    """
    NONE = 0
    SHA256 = 1
    MD5 = 2
    SHA512 = 3

  type = _messages.EnumField('TypeValueValuesEnum', 1)
  value = _messages.BytesField(2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1InlineSecret(_messages.Message):
  r"""Pairs a set of secret environment variables mapped to encrypted values
  with the Cloud KMS key to use to decrypt the value.

  Messages:
    EnvMapValue: Map of environment variable name to its encrypted value.
      Secret environment variables must be unique across all of a build's
      secrets, and must be used by at least one build step. Values can be at
      most 64 KB in size. There can be at most 100 secret values across all of
      a build's secrets.

  Fields:
    envMap: Map of environment variable name to its encrypted value. Secret
      environment variables must be unique across all of a build's secrets,
      and must be used by at least one build step. Values can be at most 64 KB
      in size. There can be at most 100 secret values across all of a build's
      secrets.
    kmsKeyName: Resource name of Cloud KMS crypto key to decrypt the encrypted
      value. In format: projects/*/locations/*/keyRings/*/cryptoKeys/*
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvMapValue(_messages.Message):
    r"""Map of environment variable name to its encrypted value. Secret
    environment variables must be unique across all of a build's secrets, and
    must be used by at least one build step. Values can be at most 64 KB in
    size. There can be at most 100 secret values across all of a build's
    secrets.

    Messages:
      AdditionalProperty: An additional property for a EnvMapValue object.

    Fields:
      additionalProperties: Additional properties of type EnvMapValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvMapValue object.

      Fields:
        key: Name of the additional property.
        value: A byte attribute.
      """

      key = _messages.StringField(1)
      value = _messages.BytesField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  envMap = _messages.MessageField('EnvMapValue', 1)
  kmsKeyName = _messages.StringField(2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1RepoSource(_messages.Message):
  r"""Location of the source in a Google Cloud Source Repository.

  Messages:
    SubstitutionsValue: Substitutions to use in a triggered build. Should only
      be used with RunBuildTrigger

  Fields:
    branchName: Regex matching branches to build. The syntax of the regular
      expressions accepted is the syntax accepted by RE2 and described at
      https://github.com/google/re2/wiki/Syntax
    commitSha: Explicit commit SHA to build.
    dir: Directory, relative to the source root, in which to run the build.
      This must be a relative path. If a step's `dir` is specified and is an
      absolute path, this value is ignored for that step's execution.
    invertRegex: Only trigger a build if the revision regex does NOT match the
      revision regex.
    projectId: ID of the project that owns the Cloud Source Repository. If
      omitted, the project ID requesting the build is assumed.
    repoName: Name of the Cloud Source Repository.
    substitutions: Substitutions to use in a triggered build. Should only be
      used with RunBuildTrigger
    tagName: Regex matching tags to build. The syntax of the regular
      expressions accepted is the syntax accepted by RE2 and described at
      https://github.com/google/re2/wiki/Syntax
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SubstitutionsValue(_messages.Message):
    r"""Substitutions to use in a triggered build. Should only be used with
    RunBuildTrigger

    Messages:
      AdditionalProperty: An additional property for a SubstitutionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type SubstitutionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SubstitutionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  branchName = _messages.StringField(1)
  commitSha = _messages.StringField(2)
  dir = _messages.StringField(3)
  invertRegex = _messages.BooleanField(4)
  projectId = _messages.StringField(5)
  repoName = _messages.StringField(6)
  substitutions = _messages.MessageField('SubstitutionsValue', 7)
  tagName = _messages.StringField(8)


class ContaineranalysisGoogleDevtoolsCloudbuildV1Results(_messages.Message):
  r"""Artifacts created by the build pipeline.

  Fields:
    artifactManifest: Path to the artifact manifest for non-container
      artifacts uploaded to Cloud Storage. Only populated when artifacts are
      uploaded to Cloud Storage.
    artifactTiming: Time to push all non-container artifacts to Cloud Storage.
    buildStepImages: List of build step digests, in the order corresponding to
      build step indices.
    buildStepOutputs: List of build step outputs, produced by builder images,
      in the order corresponding to build step indices. [Cloud
      Builders](https://cloud.google.com/cloud-build/docs/cloud-builders) can
      produce this output by writing to `$BUILDER_OUTPUT/output`. Only the
      first 4KB of data is stored.
    images: Container images that were built as a part of the build.
    mavenArtifacts: Maven artifacts uploaded to Artifact Registry at the end
      of the build.
    npmPackages: Npm packages uploaded to Artifact Registry at the end of the
      build.
    numArtifacts: Number of non-container artifacts uploaded to Cloud Storage.
      Only populated when artifacts are uploaded to Cloud Storage.
    pythonPackages: Python artifacts uploaded to Artifact Registry at the end
      of the build.
  """

  artifactManifest = _messages.StringField(1)
  artifactTiming = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 2)
  buildStepImages = _messages.StringField(3, repeated=True)
  buildStepOutputs = _messages.BytesField(4, repeated=True)
  images = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1BuiltImage', 5, repeated=True)
  mavenArtifacts = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedMavenArtifact', 6, repeated=True)
  npmPackages = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedNpmPackage', 7, repeated=True)
  numArtifacts = _messages.IntegerField(8)
  pythonPackages = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedPythonPackage', 9, repeated=True)


class ContaineranalysisGoogleDevtoolsCloudbuildV1Secret(_messages.Message):
  r"""Pairs a set of secret environment variables containing encrypted values
  with the Cloud KMS key to use to decrypt the value. Note: Use `kmsKeyName`
  with `available_secrets` instead of using `kmsKeyName` with `secret`. For
  instructions see: https://cloud.google.com/cloud-build/docs/securing-
  builds/use-encrypted-credentials.

  Messages:
    SecretEnvValue: Map of environment variable name to its encrypted value.
      Secret environment variables must be unique across all of a build's
      secrets, and must be used by at least one build step. Values can be at
      most 64 KB in size. There can be at most 100 secret values across all of
      a build's secrets.

  Fields:
    kmsKeyName: Cloud KMS key name to use to decrypt these envs.
    secretEnv: Map of environment variable name to its encrypted value. Secret
      environment variables must be unique across all of a build's secrets,
      and must be used by at least one build step. Values can be at most 64 KB
      in size. There can be at most 100 secret values across all of a build's
      secrets.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SecretEnvValue(_messages.Message):
    r"""Map of environment variable name to its encrypted value. Secret
    environment variables must be unique across all of a build's secrets, and
    must be used by at least one build step. Values can be at most 64 KB in
    size. There can be at most 100 secret values across all of a build's
    secrets.

    Messages:
      AdditionalProperty: An additional property for a SecretEnvValue object.

    Fields:
      additionalProperties: Additional properties of type SecretEnvValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SecretEnvValue object.

      Fields:
        key: Name of the additional property.
        value: A byte attribute.
      """

      key = _messages.StringField(1)
      value = _messages.BytesField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  kmsKeyName = _messages.StringField(1)
  secretEnv = _messages.MessageField('SecretEnvValue', 2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1SecretManagerSecret(_messages.Message):
  r"""Pairs a secret environment variable with a SecretVersion in Secret
  Manager.

  Fields:
    env: Environment variable name to associate with the secret. Secret
      environment variables must be unique across all of a build's secrets,
      and must be used by at least one build step.
    versionName: Resource name of the SecretVersion. In format:
      projects/*/secrets/*/versions/*
  """

  env = _messages.StringField(1)
  versionName = _messages.StringField(2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1Secrets(_messages.Message):
  r"""Secrets and secret environment variables.

  Fields:
    inline: Secrets encrypted with KMS key and the associated secret
      environment variable.
    secretManager: Secrets in Secret Manager and associated secret environment
      variable.
  """

  inline = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1InlineSecret', 1, repeated=True)
  secretManager = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1SecretManagerSecret', 2, repeated=True)


class ContaineranalysisGoogleDevtoolsCloudbuildV1Source(_messages.Message):
  r"""Location of the source in a supported storage service.

  Fields:
    gitSource: If provided, get the source from this Git repository.
    repoSource: If provided, get the source from this location in a Cloud
      Source Repository.
    storageSource: If provided, get the source from this location in Cloud
      Storage.
    storageSourceManifest: If provided, get the source from this manifest in
      Cloud Storage. This feature is in Preview; see description
      [here](https://github.com/GoogleCloudPlatform/cloud-
      builders/tree/master/gcs-fetcher).
  """

  gitSource = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1GitSource', 1)
  repoSource = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1RepoSource', 2)
  storageSource = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSource', 3)
  storageSourceManifest = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSourceManifest', 4)


class ContaineranalysisGoogleDevtoolsCloudbuildV1SourceProvenance(_messages.Message):
  r"""Provenance of the source. Ways to find the original source, or verify
  that some source was used for this build.

  Messages:
    FileHashesValue: Output only. Hash(es) of the build source, which can be
      used to verify that the original source integrity was maintained in the
      build. Note that `FileHashes` will only be populated if `BuildOptions`
      has requested a `SourceProvenanceHash`. The keys to this map are file
      paths used as build source and the values contain the hash values for
      those files. If the build source came in a single package such as a
      gzipped tarfile (`.tar.gz`), the `FileHash` will be for the single path
      to that file.

  Fields:
    fileHashes: Output only. Hash(es) of the build source, which can be used
      to verify that the original source integrity was maintained in the
      build. Note that `FileHashes` will only be populated if `BuildOptions`
      has requested a `SourceProvenanceHash`. The keys to this map are file
      paths used as build source and the values contain the hash values for
      those files. If the build source came in a single package such as a
      gzipped tarfile (`.tar.gz`), the `FileHash` will be for the single path
      to that file.
    resolvedRepoSource: A copy of the build's `source.repo_source`, if exists,
      with any revisions resolved.
    resolvedStorageSource: A copy of the build's `source.storage_source`, if
      exists, with any generations resolved.
    resolvedStorageSourceManifest: A copy of the build's
      `source.storage_source_manifest`, if exists, with any revisions
      resolved. This feature is in Preview.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FileHashesValue(_messages.Message):
    r"""Output only. Hash(es) of the build source, which can be used to verify
    that the original source integrity was maintained in the build. Note that
    `FileHashes` will only be populated if `BuildOptions` has requested a
    `SourceProvenanceHash`. The keys to this map are file paths used as build
    source and the values contain the hash values for those files. If the
    build source came in a single package such as a gzipped tarfile
    (`.tar.gz`), the `FileHash` will be for the single path to that file.

    Messages:
      AdditionalProperty: An additional property for a FileHashesValue object.

    Fields:
      additionalProperties: Additional properties of type FileHashesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FileHashesValue object.

      Fields:
        key: Name of the additional property.
        value: A ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  fileHashes = _messages.MessageField('FileHashesValue', 1)
  resolvedRepoSource = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1RepoSource', 2)
  resolvedStorageSource = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSource', 3)
  resolvedStorageSourceManifest = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSourceManifest', 4)


class ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSource(_messages.Message):
  r"""Location of the source in an archive file in Cloud Storage.

  Fields:
    bucket: Cloud Storage bucket containing the source (see [Bucket Name
      Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)).
    generation: Cloud Storage generation for the object. If the generation is
      omitted, the latest generation will be used.
    object: Cloud Storage object containing the source. This object must be a
      zipped (`.zip`) or gzipped archive file (`.tar.gz`) containing source to
      build.
  """

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSourceManifest(_messages.Message):
  r"""Location of the source manifest in Cloud Storage. This feature is in
  Preview; see description
  [here](https://github.com/GoogleCloudPlatform/cloud-
  builders/tree/master/gcs-fetcher).

  Fields:
    bucket: Cloud Storage bucket containing the source manifest (see [Bucket
      Name Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)).
    generation: Cloud Storage generation for the object. If the generation is
      omitted, the latest generation will be used.
    object: Cloud Storage object containing the source manifest. This object
      must be a JSON file.
  """

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan(_messages.Message):
  r"""Start and end times for a build execution phase.

  Fields:
    endTime: End of time span.
    startTime: Start of time span.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedMavenArtifact(_messages.Message):
  r"""A Maven artifact uploaded using the MavenArtifact directive.

  Fields:
    fileHashes: Hash types and values of the Maven Artifact.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded artifact.
  """

  fileHashes = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes', 1)
  pushTiming = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 2)
  uri = _messages.StringField(3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedNpmPackage(_messages.Message):
  r"""An npm package uploaded to Artifact Registry using the NpmPackage
  directive.

  Fields:
    fileHashes: Hash types and values of the npm package.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded npm package.
  """

  fileHashes = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes', 1)
  pushTiming = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 2)
  uri = _messages.StringField(3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedPythonPackage(_messages.Message):
  r"""Artifact uploaded using the PythonPackage directive.

  Fields:
    fileHashes: Hash types and values of the Python Artifact.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded artifact.
  """

  fileHashes = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes', 1)
  pushTiming = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 2)
  uri = _messages.StringField(3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1Volume(_messages.Message):
  r"""Volume describes a Docker container volume which is mounted into build
  steps in order to persist files across build step execution.

  Fields:
    name: Name of the volume to mount. Volume names must be unique per build
      step and must be valid names for Docker volumes. Each named volume must
      be used by at least two build steps.
    path: Path at which to mount the volume. Paths must be absolute and cannot
      conflict with other volume paths on the same build step or with certain
      reserved volume paths.
  """

  name = _messages.StringField(1)
  path = _messages.StringField(2)


class ContaineranalysisProjectsNotesCreateRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesCreateRequest object.

  Fields:
    name: The name of the project. Should be of the form
      "providers/{provider_id}". @Deprecated
    note: A Note resource to be passed as the request body.
    noteId: The ID to use for this note.
    parent: This field contains the project Id for example:
      "projects/{project_id}
  """

  name = _messages.StringField(1)
  note = _messages.MessageField('Note', 2)
  noteId = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ContaineranalysisProjectsNotesDeleteRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesDeleteRequest object.

  Fields:
    name: The name of the note in the form of
      "providers/{provider_id}/notes/{NOTE_ID}"
  """

  name = _messages.StringField(1, required=True)


class ContaineranalysisProjectsNotesGetIamPolicyRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class ContaineranalysisProjectsNotesGetRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesGetRequest object.

  Fields:
    name: The name of the note in the form of
      "providers/{provider_id}/notes/{NOTE_ID}"
  """

  name = _messages.StringField(1, required=True)


class ContaineranalysisProjectsNotesListRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesListRequest object.

  Fields:
    filter: The filter expression.
    name: The name field will contain the project Id for example:
      "providers/{provider_id} @Deprecated
    pageSize: Number of notes to return in the list.
    pageToken: Token to provide to skip to a particular spot in the list.
    parent: This field contains the project Id for example:
      "projects/{PROJECT_ID}".
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ContaineranalysisProjectsNotesOccurrencesListRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesOccurrencesListRequest object.

  Fields:
    filter: The filter expression.
    name: The name field will contain the note name for example:
      "provider/{provider_id}/notes/{note_id}"
    pageSize: Number of notes to return in the list.
    pageToken: Token to provide to skip to a particular spot in the list.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ContaineranalysisProjectsNotesPatchRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesPatchRequest object.

  Fields:
    name: The name of the note. Should be of the form
      "projects/{provider_id}/notes/{note_id}".
    note: A Note resource to be passed as the request body.
    updateMask: The fields to update.
  """

  name = _messages.StringField(1, required=True)
  note = _messages.MessageField('Note', 2)
  updateMask = _messages.StringField(3)


class ContaineranalysisProjectsNotesSetIamPolicyRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ContaineranalysisProjectsNotesTestIamPermissionsRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class ContaineranalysisProjectsOccurrencesCreateRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesCreateRequest object.

  Fields:
    name: The name of the project. Should be of the form
      "projects/{project_id}". @Deprecated
    occurrence: A Occurrence resource to be passed as the request body.
    parent: This field contains the project Id for example:
      "projects/{project_id}"
  """

  name = _messages.StringField(1)
  occurrence = _messages.MessageField('Occurrence', 2)
  parent = _messages.StringField(3, required=True)


class ContaineranalysisProjectsOccurrencesDeleteRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesDeleteRequest object.

  Fields:
    name: The name of the occurrence in the form of
      "projects/{project_id}/occurrences/{OCCURRENCE_ID}"
  """

  name = _messages.StringField(1, required=True)


class ContaineranalysisProjectsOccurrencesGetIamPolicyRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class ContaineranalysisProjectsOccurrencesGetNotesRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesGetNotesRequest object.

  Fields:
    name: The name of the occurrence in the form
      "projects/{project_id}/occurrences/{OCCURRENCE_ID}"
  """

  name = _messages.StringField(1, required=True)


class ContaineranalysisProjectsOccurrencesGetRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesGetRequest object.

  Fields:
    name: The name of the occurrence of the form
      "projects/{project_id}/occurrences/{OCCURRENCE_ID}"
  """

  name = _messages.StringField(1, required=True)


class ContaineranalysisProjectsOccurrencesGetVulnerabilitySummaryRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesGetVulnerabilitySummaryRequest
  object.

  Fields:
    filter: The filter expression.
    parent: This contains the project Id for example: projects/{project_id}
  """

  filter = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)


class ContaineranalysisProjectsOccurrencesListRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesListRequest object.

  Enums:
    KindValueValuesEnum: The kind of occurrences to filter on.

  Fields:
    filter: The filter expression.
    kind: The kind of occurrences to filter on.
    name: The name field contains the project Id. For example:
      "projects/{project_id} @Deprecated
    pageSize: Number of occurrences to return in the list.
    pageToken: Token to provide to skip to a particular spot in the list.
    parent: This contains the project Id for example: projects/{project_id}.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""The kind of occurrences to filter on.

    Values:
      KIND_UNSPECIFIED: Unknown
      PACKAGE_VULNERABILITY: The note and occurrence represent a package
        vulnerability.
      BUILD_DETAILS: The note and occurrence assert build provenance.
      IMAGE_BASIS: This represents an image basis relationship.
      PACKAGE_MANAGER: This represents a package installed via a package
        manager.
      DEPLOYABLE: The note and occurrence track deployment events.
      DISCOVERY: The note and occurrence track the initial discovery status of
        a resource.
      ATTESTATION_AUTHORITY: This represents a logical "role" that can attest
        to artifacts.
      UPGRADE: This represents an available software upgrade.
      COMPLIANCE: This represents a compliance check that can be applied to a
        resource.
      SBOM: This represents a software bill of materials.
      SPDX_PACKAGE: This represents an SPDX Package.
      SPDX_FILE: This represents an SPDX File.
      SPDX_RELATIONSHIP: This represents an SPDX Relationship.
      DSSE_ATTESTATION: This represents a DSSE attestation Note
      VULNERABILITY_ASSESSMENT: This represents a Vulnerability Assessment.
      SBOM_REFERENCE: This represents a reference to an SBOM.
    """
    KIND_UNSPECIFIED = 0
    PACKAGE_VULNERABILITY = 1
    BUILD_DETAILS = 2
    IMAGE_BASIS = 3
    PACKAGE_MANAGER = 4
    DEPLOYABLE = 5
    DISCOVERY = 6
    ATTESTATION_AUTHORITY = 7
    UPGRADE = 8
    COMPLIANCE = 9
    SBOM = 10
    SPDX_PACKAGE = 11
    SPDX_FILE = 12
    SPDX_RELATIONSHIP = 13
    DSSE_ATTESTATION = 14
    VULNERABILITY_ASSESSMENT = 15
    SBOM_REFERENCE = 16

  filter = _messages.StringField(1)
  kind = _messages.EnumField('KindValueValuesEnum', 2)
  name = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  parent = _messages.StringField(6, required=True)


class ContaineranalysisProjectsOccurrencesPatchRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesPatchRequest object.

  Fields:
    name: The name of the occurrence. Should be of the form
      "projects/{project_id}/occurrences/{OCCURRENCE_ID}".
    occurrence: A Occurrence resource to be passed as the request body.
    updateMask: The fields to update.
  """

  name = _messages.StringField(1, required=True)
  occurrence = _messages.MessageField('Occurrence', 2)
  updateMask = _messages.StringField(3)


class ContaineranalysisProjectsOccurrencesSetIamPolicyRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ContaineranalysisProjectsOccurrencesTestIamPermissionsRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class ContaineranalysisProjectsOperationsCreateRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOperationsCreateRequest object.

  Fields:
    createOperationRequest: A CreateOperationRequest resource to be passed as
      the request body.
    parent: The project Id that this operation should be created under.
  """

  createOperationRequest = _messages.MessageField('CreateOperationRequest', 1)
  parent = _messages.StringField(2, required=True)


class ContaineranalysisProjectsOperationsPatchRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOperationsPatchRequest object.

  Fields:
    name: The name of the Operation. Should be of the form
      "projects/{provider_id}/operations/{operation_id}".
    updateOperationRequest: A UpdateOperationRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  updateOperationRequest = _messages.MessageField('UpdateOperationRequest', 2)


class ContaineranalysisProjectsScanConfigsGetRequest(_messages.Message):
  r"""A ContaineranalysisProjectsScanConfigsGetRequest object.

  Fields:
    name: The name of the ScanConfig in the form
      projects/{project_id}/scanConfigs/{scan_config_id}
  """

  name = _messages.StringField(1, required=True)


class ContaineranalysisProjectsScanConfigsListRequest(_messages.Message):
  r"""A ContaineranalysisProjectsScanConfigsListRequest object.

  Fields:
    filter: The filter expression.
    pageSize: The number of items to return.
    pageToken: The page token to use for the next request.
    parent: This containers the project Id i.e.: projects/{project_id}
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ContaineranalysisProjectsScanConfigsPatchRequest(_messages.Message):
  r"""A ContaineranalysisProjectsScanConfigsPatchRequest object.

  Fields:
    name: The scan config to update of the form
      projects/{project_id}/scanConfigs/{scan_config_id}.
    scanConfig: A ScanConfig resource to be passed as the request body.
    updateMask: The fields to update.
  """

  name = _messages.StringField(1, required=True)
  scanConfig = _messages.MessageField('ScanConfig', 2)
  updateMask = _messages.StringField(3)


class ContaineranalysisProvidersNotesCreateRequest(_messages.Message):
  r"""A ContaineranalysisProvidersNotesCreateRequest object.

  Fields:
    name: The name of the project. Should be of the form
      "providers/{provider_id}". @Deprecated
    note: A Note resource to be passed as the request body.
    noteId: The ID to use for this note.
    parent: This field contains the project Id for example:
      "projects/{project_id}
  """

  name = _messages.StringField(1, required=True)
  note = _messages.MessageField('Note', 2)
  noteId = _messages.StringField(3)
  parent = _messages.StringField(4)


class ContaineranalysisProvidersNotesDeleteRequest(_messages.Message):
  r"""A ContaineranalysisProvidersNotesDeleteRequest object.

  Fields:
    name: The name of the note in the form of
      "providers/{provider_id}/notes/{NOTE_ID}"
  """

  name = _messages.StringField(1, required=True)


class ContaineranalysisProvidersNotesGetIamPolicyRequest(_messages.Message):
  r"""A ContaineranalysisProvidersNotesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class ContaineranalysisProvidersNotesGetRequest(_messages.Message):
  r"""A ContaineranalysisProvidersNotesGetRequest object.

  Fields:
    name: The name of the note in the form of
      "providers/{provider_id}/notes/{NOTE_ID}"
  """

  name = _messages.StringField(1, required=True)


class ContaineranalysisProvidersNotesListRequest(_messages.Message):
  r"""A ContaineranalysisProvidersNotesListRequest object.

  Fields:
    filter: The filter expression.
    name: The name field will contain the project Id for example:
      "providers/{provider_id} @Deprecated
    pageSize: Number of notes to return in the list.
    pageToken: Token to provide to skip to a particular spot in the list.
    parent: This field contains the project Id for example:
      "projects/{PROJECT_ID}".
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5)


class ContaineranalysisProvidersNotesOccurrencesListRequest(_messages.Message):
  r"""A ContaineranalysisProvidersNotesOccurrencesListRequest object.

  Fields:
    filter: The filter expression.
    name: The name field will contain the note name for example:
      "provider/{provider_id}/notes/{note_id}"
    pageSize: Number of notes to return in the list.
    pageToken: Token to provide to skip to a particular spot in the list.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ContaineranalysisProvidersNotesPatchRequest(_messages.Message):
  r"""A ContaineranalysisProvidersNotesPatchRequest object.

  Fields:
    name: The name of the note. Should be of the form
      "projects/{provider_id}/notes/{note_id}".
    note: A Note resource to be passed as the request body.
    updateMask: The fields to update.
  """

  name = _messages.StringField(1, required=True)
  note = _messages.MessageField('Note', 2)
  updateMask = _messages.StringField(3)


class ContaineranalysisProvidersNotesSetIamPolicyRequest(_messages.Message):
  r"""A ContaineranalysisProvidersNotesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ContaineranalysisProvidersNotesTestIamPermissionsRequest(_messages.Message):
  r"""A ContaineranalysisProvidersNotesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class CreateOperationRequest(_messages.Message):
  r"""Request for creating an operation

  Fields:
    operation: The operation to create.
    operationId: The ID to use for this operation.
  """

  operation = _messages.MessageField('Operation', 1)
  operationId = _messages.StringField(2)


class DSSEAttestationNote(_messages.Message):
  r"""A note describing an attestation

  Fields:
    hint: DSSEHint hints at the purpose of the attestation authority.
  """

  hint = _messages.MessageField('DSSEHint', 1)


class DSSEAttestationOccurrence(_messages.Message):
  r"""An occurrence describing an attestation on a resource

  Fields:
    envelope: If doing something security critical, make sure to verify the
      signatures in this metadata.
    statement: A InTotoStatement attribute.
  """

  envelope = _messages.MessageField('Envelope', 1)
  statement = _messages.MessageField('InTotoStatement', 2)


class DSSEHint(_messages.Message):
  r"""This submessage provides human-readable hints about the purpose of the
  authority. Because the name of a note acts as its resource reference, it is
  important to disambiguate the canonical name of the Note (which might be a
  UUID for security purposes) from "readable" names more suitable for debug
  output. Note that these hints should not be used to look up authorities in
  security sensitive contexts, such as when looking up attestations to verify.

  Fields:
    humanReadableName: Required. The human readable name of this attestation
      authority, for example "cloudbuild-prod".
  """

  humanReadableName = _messages.StringField(1)


class Deployable(_messages.Message):
  r"""An artifact that can be deployed in some runtime.

  Fields:
    resourceUri: Resource URI for the artifact being deployed.
  """

  resourceUri = _messages.StringField(1, repeated=True)


class Deployment(_messages.Message):
  r"""The period during which some deployable was active in a runtime.

  Enums:
    PlatformValueValuesEnum: Platform hosting this deployment.

  Fields:
    address: Address of the runtime element hosting this deployment.
    config: Configuration used to create this deployment.
    deployTime: Beginning of the lifetime of this deployment.
    platform: Platform hosting this deployment.
    resourceUri: Resource URI for the artifact being deployed taken from the
      deployable field with the same name.
    undeployTime: End of the lifetime of this deployment.
    userEmail: Identity of the user that triggered this deployment.
  """

  class PlatformValueValuesEnum(_messages.Enum):
    r"""Platform hosting this deployment.

    Values:
      PLATFORM_UNSPECIFIED: Unknown
      GKE: Google Container Engine
      FLEX: Google App Engine: Flexible Environment
      CUSTOM: Custom user-defined platform
    """
    PLATFORM_UNSPECIFIED = 0
    GKE = 1
    FLEX = 2
    CUSTOM = 3

  address = _messages.StringField(1)
  config = _messages.StringField(2)
  deployTime = _messages.StringField(3)
  platform = _messages.EnumField('PlatformValueValuesEnum', 4)
  resourceUri = _messages.StringField(5, repeated=True)
  undeployTime = _messages.StringField(6)
  userEmail = _messages.StringField(7)


class Derived(_messages.Message):
  r"""Derived describes the derived image portion (Occurrence) of the
  DockerImage relationship. This image would be produced from a Dockerfile
  with FROM .

  Fields:
    baseResourceUrl: Output only. This contains the base image URL for the
      derived image occurrence.
    distance: Output only. The number of layers by which this image differs
      from the associated image basis.
    fingerprint: The fingerprint of the derived image.
    layerInfo: This contains layer-specific metadata, if populated it has
      length "distance" and is ordered with [distance] being the layer
      immediately following the base image and [1] being the final layer.
  """

  baseResourceUrl = _messages.StringField(1)
  distance = _messages.IntegerField(2, variant=_messages.Variant.UINT32)
  fingerprint = _messages.MessageField('Fingerprint', 3)
  layerInfo = _messages.MessageField('Layer', 4, repeated=True)


class Detail(_messages.Message):
  r"""Identifies all occurrences of this vulnerability in the package for a
  specific distro/location For example: glibc in cpe:/o:debian:debian_linux:8
  for versions 2.1 - 2.2

  Fields:
    cpeUri: The cpe_uri in [cpe format] (https://cpe.mitre.org/specification/)
      in which the vulnerability manifests. Examples include distro or storage
      location for vulnerable jar. This field can be used as a filter in list
      requests.
    description: A vendor-specific description of this note.
    fixedLocation: The fix for this specific package version.
    isObsolete: Whether this Detail is obsolete. Occurrences are expected not
      to point to obsolete details.
    maxAffectedVersion: The max version of the package in which the
      vulnerability exists.
    minAffectedVersion: The min version of the package in which the
      vulnerability exists.
    package: The name of the package where the vulnerability was found. This
      field can be used as a filter in list requests.
    packageType: The type of package; whether native or non native(ruby gems,
      node.js packages etc)
    severityName: The severity (eg: distro assigned severity) for this
      vulnerability.
    source: The source from which the information in this Detail was obtained.
    vendor: The vendor of the product. e.g. "google"
  """

  cpeUri = _messages.StringField(1)
  description = _messages.StringField(2)
  fixedLocation = _messages.MessageField('VulnerabilityLocation', 3)
  isObsolete = _messages.BooleanField(4)
  maxAffectedVersion = _messages.MessageField('Version', 5)
  minAffectedVersion = _messages.MessageField('Version', 6)
  package = _messages.StringField(7)
  packageType = _messages.StringField(8)
  severityName = _messages.StringField(9)
  source = _messages.StringField(10)
  vendor = _messages.StringField(11)


class Digest(_messages.Message):
  r"""Digest information.

  Fields:
    algo: `SHA1`, `SHA512` etc.
    digestBytes: Value of the digest.
  """

  algo = _messages.StringField(1)
  digestBytes = _messages.BytesField(2)


class Discovered(_messages.Message):
  r"""Provides information about the scan status of a discovered resource.

  Enums:
    AnalysisStatusValueValuesEnum: The status of discovery for the resource.
    ContinuousAnalysisValueValuesEnum: Whether the resource is continuously
      analyzed.

  Fields:
    analysisCompleted: The list of analysis that were completed for a
      resource.
    analysisError: Indicates any errors encountered during analysis of a
      resource. There could be 0 or more of these errors.
    analysisStatus: The status of discovery for the resource.
    analysisStatusError: When an error is encountered this will contain a
      LocalizedMessage under details to show to the user. The LocalizedMessage
      output only and populated by the API.
    archiveTime: The time occurrences related to this discovery occurrence
      were archived.
    continuousAnalysis: Whether the resource is continuously analyzed.
    cpe: The CPE of the resource being scanned.
    lastScanTime: The last time this resource was scanned.
    operation: Output only. An operation that indicates the status of the
      current scan. This field is deprecated, do not use.
    sbomStatus: Output only. The status of an SBOM generation.
  """

  class AnalysisStatusValueValuesEnum(_messages.Enum):
    r"""The status of discovery for the resource.

    Values:
      ANALYSIS_STATUS_UNSPECIFIED: Unknown
      PENDING: Resource is known but no action has been taken yet.
      SCANNING: Resource is being analyzed.
      COMPLETE: Analysis has completed
      FINISHED_SUCCESS: Analysis has finished successfully.
      FINISHED_FAILED: Analysis has finished unsuccessfully, the analysis
        itself is in a bad state.
      FINISHED_UNSUPPORTED: The resource is known not to be supported.
    """
    ANALYSIS_STATUS_UNSPECIFIED = 0
    PENDING = 1
    SCANNING = 2
    COMPLETE = 3
    FINISHED_SUCCESS = 4
    FINISHED_FAILED = 5
    FINISHED_UNSUPPORTED = 6

  class ContinuousAnalysisValueValuesEnum(_messages.Enum):
    r"""Whether the resource is continuously analyzed.

    Values:
      CONTINUOUS_ANALYSIS_UNSPECIFIED: Unknown
      ACTIVE: The resource is continuously analyzed.
      INACTIVE: The resource is ignored for continuous analysis.
    """
    CONTINUOUS_ANALYSIS_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2

  analysisCompleted = _messages.MessageField('AnalysisCompleted', 1)
  analysisError = _messages.MessageField('Status', 2, repeated=True)
  analysisStatus = _messages.EnumField('AnalysisStatusValueValuesEnum', 3)
  analysisStatusError = _messages.MessageField('Status', 4)
  archiveTime = _messages.StringField(5)
  continuousAnalysis = _messages.EnumField('ContinuousAnalysisValueValuesEnum', 6)
  cpe = _messages.StringField(7)
  lastScanTime = _messages.StringField(8)
  operation = _messages.MessageField('Operation', 9)
  sbomStatus = _messages.MessageField('SBOMStatus', 10)


class Discovery(_messages.Message):
  r"""A note that indicates a type of analysis a provider would perform. This
  note exists in a provider's project. A `Discovery` occurrence is created in
  a consumer's project at the start of analysis. The occurrence's operation
  will indicate the status of the analysis. Absence of an occurrence linked to
  this note for a resource indicates that analysis hasn't started.

  Enums:
    AnalysisKindValueValuesEnum: The kind of analysis that is handled by this
      discovery.

  Fields:
    analysisKind: The kind of analysis that is handled by this discovery.
  """

  class AnalysisKindValueValuesEnum(_messages.Enum):
    r"""The kind of analysis that is handled by this discovery.

    Values:
      KIND_UNSPECIFIED: Unknown
      PACKAGE_VULNERABILITY: The note and occurrence represent a package
        vulnerability.
      BUILD_DETAILS: The note and occurrence assert build provenance.
      IMAGE_BASIS: This represents an image basis relationship.
      PACKAGE_MANAGER: This represents a package installed via a package
        manager.
      DEPLOYABLE: The note and occurrence track deployment events.
      DISCOVERY: The note and occurrence track the initial discovery status of
        a resource.
      ATTESTATION_AUTHORITY: This represents a logical "role" that can attest
        to artifacts.
      UPGRADE: This represents an available software upgrade.
      COMPLIANCE: This represents a compliance check that can be applied to a
        resource.
      SBOM: This represents a software bill of materials.
      SPDX_PACKAGE: This represents an SPDX Package.
      SPDX_FILE: This represents an SPDX File.
      SPDX_RELATIONSHIP: This represents an SPDX Relationship.
      DSSE_ATTESTATION: This represents a DSSE attestation Note
      VULNERABILITY_ASSESSMENT: This represents a Vulnerability Assessment.
      SBOM_REFERENCE: This represents a reference to an SBOM.
    """
    KIND_UNSPECIFIED = 0
    PACKAGE_VULNERABILITY = 1
    BUILD_DETAILS = 2
    IMAGE_BASIS = 3
    PACKAGE_MANAGER = 4
    DEPLOYABLE = 5
    DISCOVERY = 6
    ATTESTATION_AUTHORITY = 7
    UPGRADE = 8
    COMPLIANCE = 9
    SBOM = 10
    SPDX_PACKAGE = 11
    SPDX_FILE = 12
    SPDX_RELATIONSHIP = 13
    DSSE_ATTESTATION = 14
    VULNERABILITY_ASSESSMENT = 15
    SBOM_REFERENCE = 16

  analysisKind = _messages.EnumField('AnalysisKindValueValuesEnum', 1)


class Distribution(_messages.Message):
  r"""This represents a particular channel of distribution for a given
  package. e.g. Debian's jessie-backports dpkg mirror

  Enums:
    ArchitectureValueValuesEnum: The CPU architecture for which packages in
      this distribution channel were built

  Fields:
    architecture: The CPU architecture for which packages in this distribution
      channel were built
    cpeUri: The cpe_uri in [cpe format](https://cpe.mitre.org/specification/)
      denoting the package manager version distributing a package.
    description: The distribution channel-specific description of this
      package.
    latestVersion: The latest available version of this package in this
      distribution channel.
    maintainer: A freeform string denoting the maintainer of this package.
    url: The distribution channel-specific homepage for this package.
  """

  class ArchitectureValueValuesEnum(_messages.Enum):
    r"""The CPU architecture for which packages in this distribution channel
    were built

    Values:
      ARCHITECTURE_UNSPECIFIED: Unknown architecture
      X86: X86 architecture
      X64: X64 architecture
    """
    ARCHITECTURE_UNSPECIFIED = 0
    X86 = 1
    X64 = 2

  architecture = _messages.EnumField('ArchitectureValueValuesEnum', 1)
  cpeUri = _messages.StringField(2)
  description = _messages.StringField(3)
  latestVersion = _messages.MessageField('Version', 4)
  maintainer = _messages.StringField(5)
  url = _messages.StringField(6)


class DocumentNote(_messages.Message):
  r"""DocumentNote represents an SPDX Document Creation Infromation section:
  https://spdx.github.io/spdx-spec/2-document-creation-information/

  Fields:
    dataLicence: Compliance with the SPDX specification includes populating
      the SPDX fields therein with data related to such fields ("SPDX-
      Metadata")
    spdxVersion: Provide a reference number that can be used to understand how
      to parse and interpret the rest of the file
  """

  dataLicence = _messages.StringField(1)
  spdxVersion = _messages.StringField(2)


class DocumentOccurrence(_messages.Message):
  r"""DocumentOccurrence represents an SPDX Document Creation Information
  section: https://spdx.github.io/spdx-spec/2-document-creation-information/

  Fields:
    createTime: Identify when the SPDX file was originally created. The date
      is to be specified according to combined date and time in UTC format as
      specified in ISO 8601 standard
    creatorComment: A field for creators of the SPDX file to provide general
      comments about the creation of the SPDX file or any other relevant
      comment not included in the other fields
    creators: Identify who (or what, in the case of a tool) created the SPDX
      file. If the SPDX file was created by an individual, indicate the
      person's name
    documentComment: A field for creators of the SPDX file content to provide
      comments to the consumers of the SPDX document
    externalDocumentRefs: Identify any external SPDX documents referenced
      within this SPDX document
    id: Identify the current SPDX document which may be referenced in
      relationships by other files, packages internally and documents
      externally
    licenseListVersion: A field for creators of the SPDX file to provide the
      version of the SPDX License List used when the SPDX file was created
    namespace: Provide an SPDX document specific namespace as a unique
      absolute Uniform Resource Identifier (URI) as specified in RFC-3986,
      with the exception of the '#' delimiter
    title: Identify name of this document as designated by creator
  """

  createTime = _messages.StringField(1)
  creatorComment = _messages.StringField(2)
  creators = _messages.StringField(3, repeated=True)
  documentComment = _messages.StringField(4)
  externalDocumentRefs = _messages.StringField(5, repeated=True)
  id = _messages.StringField(6)
  licenseListVersion = _messages.StringField(7)
  namespace = _messages.StringField(8)
  title = _messages.StringField(9)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Envelope(_messages.Message):
  r"""MUST match https://github.com/secure-systems-
  lab/dsse/blob/master/envelope.proto. An authenticated message of arbitrary
  type.

  Fields:
    payload: The bytes being signed
    payloadType: The type of payload being signed
    signatures: The signatures over the payload
  """

  payload = _messages.BytesField(1)
  payloadType = _messages.StringField(2)
  signatures = _messages.MessageField('EnvelopeSignature', 3, repeated=True)


class EnvelopeSignature(_messages.Message):
  r"""A DSSE signature

  Fields:
    keyid: A reference id to the key being used for signing
    sig: The signature itself
  """

  keyid = _messages.StringField(1)
  sig = _messages.BytesField(2)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class ExternalRef(_messages.Message):
  r"""An External Reference allows a Package to reference an external source
  of additional information, metadata, enumerations, asset identifiers, or
  downloadable content believed to be relevant to the Package

  Enums:
    CategoryValueValuesEnum: An External Reference allows a Package to
      reference an external source of additional information, metadata,
      enumerations, asset identifiers, or downloadable content believed to be
      relevant to the Package

  Fields:
    category: An External Reference allows a Package to reference an external
      source of additional information, metadata, enumerations, asset
      identifiers, or downloadable content believed to be relevant to the
      Package
    comment: Human-readable information about the purpose and target of the
      reference
    locator: The unique string with no spaces necessary to access the package-
      specific information, metadata, or content within the target location
    type: Type of category (e.g. 'npm' for the PACKAGE_MANAGER category)
  """

  class CategoryValueValuesEnum(_messages.Enum):
    r"""An External Reference allows a Package to reference an external source
    of additional information, metadata, enumerations, asset identifiers, or
    downloadable content believed to be relevant to the Package

    Values:
      CATEGORY_UNSPECIFIED: Unspecified
      SECURITY: Security (e.g. cpe22Type, cpe23Type)
      PACKAGE_MANAGER: Package Manager (e.g. maven-central, npm, nuget, bower,
        purl)
      PERSISTENT_ID: Persistent-Id (e.g. swh)
      OTHER: Other
    """
    CATEGORY_UNSPECIFIED = 0
    SECURITY = 1
    PACKAGE_MANAGER = 2
    PERSISTENT_ID = 3
    OTHER = 4

  category = _messages.EnumField('CategoryValueValuesEnum', 1)
  comment = _messages.StringField(2)
  locator = _messages.StringField(3)
  type = _messages.StringField(4)


class FileHashes(_messages.Message):
  r"""Container message for hashes of byte content of files, used in Source
  messages to verify integrity of source input to the build.

  Fields:
    fileHash: Collection of file hashes.
  """

  fileHash = _messages.MessageField('Hash', 1, repeated=True)


class FileLocation(_messages.Message):
  r"""Indicates the location at which a package was found.

  Fields:
    filePath: For jars that are contained inside .war files, this filepath can
      indicate the path to war file combined with the path to jar file.
  """

  filePath = _messages.StringField(1)


class FileNote(_messages.Message):
  r"""FileNote represents an SPDX File Information section:
  https://spdx.github.io/spdx-spec/4-file-information/

  Enums:
    FileTypeValueValuesEnum: This field provides information about the type of
      file identified

  Fields:
    checksum: Provide a unique identifier to match analysis information on
      each specific file in a package
    fileType: This field provides information about the type of file
      identified
    title: Identify the full path and filename that corresponds to the file
      information in this section
  """

  class FileTypeValueValuesEnum(_messages.Enum):
    r"""This field provides information about the type of file identified

    Values:
      FILE_TYPE_UNSPECIFIED: Unspecified
      SOURCE: The file is human readable source code (.c, .html, etc.)
      BINARY: The file is a compiled object, target image or binary executable
        (.o, .a, etc.)
      ARCHIVE: The file represents an archive (.tar, .jar, etc.)
      APPLICATION: The file is associated with a specific application type
        (MIME type of application/*)
      AUDIO: The file is associated with an audio file (MIME type of audio/* ,
        e.g. .mp3)
      IMAGE: The file is associated with an picture image file (MIME type of
        image/*, e.g., .jpg, .gif)
      TEXT: The file is human readable text file (MIME type of text/*)
      VIDEO: The file is associated with a video file type (MIME type of
        video/*)
      DOCUMENTATION: The file serves as documentation
      SPDX: The file is an SPDX document
      OTHER: The file doesn't fit into the above categories (generated
        artifacts, data files, etc.)
    """
    FILE_TYPE_UNSPECIFIED = 0
    SOURCE = 1
    BINARY = 2
    ARCHIVE = 3
    APPLICATION = 4
    AUDIO = 5
    IMAGE = 6
    TEXT = 7
    VIDEO = 8
    DOCUMENTATION = 9
    SPDX = 10
    OTHER = 11

  checksum = _messages.StringField(1, repeated=True)
  fileType = _messages.EnumField('FileTypeValueValuesEnum', 2)
  title = _messages.StringField(3)


class FileOccurrence(_messages.Message):
  r"""FileOccurrence represents an SPDX File Information section:
  https://spdx.github.io/spdx-spec/4-file-information/

  Fields:
    attributions: This field provides a place for the SPDX data creator to
      record, at the file level, acknowledgements that may be needed to be
      communicated in some contexts
    comment: This field provides a place for the SPDX file creator to record
      any general comments about the file
    contributors: This field provides a place for the SPDX file creator to
      record file contributors
    copyright: Identify the copyright holder of the file, as well as any dates
      present
    filesLicenseInfo: This field contains the license information actually
      found in the file, if any
    id: Uniquely identify any element in an SPDX document which may be
      referenced by other elements
    licenseConcluded: This field contains the license the SPDX file creator
      has concluded as governing the file or alternative values if the
      governing license cannot be determined
    notice: This field provides a place for the SPDX file creator to record
      license notices or other such related notices found in the file
  """

  attributions = _messages.StringField(1, repeated=True)
  comment = _messages.StringField(2)
  contributors = _messages.StringField(3, repeated=True)
  copyright = _messages.StringField(4)
  filesLicenseInfo = _messages.StringField(5, repeated=True)
  id = _messages.StringField(6)
  licenseConcluded = _messages.MessageField('License', 7)
  notice = _messages.StringField(8)


class Fingerprint(_messages.Message):
  r"""A set of properties that uniquely identify a given Docker image.

  Fields:
    v1Name: The layer-id of the final layer in the Docker image's v1
      representation. This field can be used as a filter in list requests.
    v2Blob: The ordered list of v2 blobs that represent a given image.
    v2Name: Output only. The name of the image's v2 blobs computed via:
      [bottom] := v2_blobbottom := sha256(v2_blob[N] + " " + v2_name[N+1])
      Only the name of the final blob is kept. This field can be used as a
      filter in list requests.
  """

  v1Name = _messages.StringField(1)
  v2Blob = _messages.StringField(2, repeated=True)
  v2Name = _messages.StringField(3)


class GetIamPolicyRequest(_messages.Message):
  r"""Request message for `GetIamPolicy` method.

  Fields:
    options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
      `GetIamPolicy`.
  """

  options = _messages.MessageField('GetPolicyOptions', 1)


class GetPolicyOptions(_messages.Message):
  r"""Encapsulates settings provided to GetIamPolicy.

  Fields:
    requestedPolicyVersion: Optional. The maximum policy version that will be
      used to format the policy. Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected. Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset. The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1. To learn which resources support conditions in
      their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GetVulnzOccurrencesSummaryResponse(_messages.Message):
  r"""A summary of how many vulnz occurrences there are per severity type.
  counts by groups, or if we should have different summary messages like this.

  Fields:
    counts: A map of how many occurrences were found for each severity.
  """

  counts = _messages.MessageField('SeverityCount', 1, repeated=True)


class GoogleDevtoolsContaineranalysisV1alpha1AliasContext(_messages.Message):
  r"""An alias to a repo revision.

  Enums:
    KindValueValuesEnum: The alias kind.

  Fields:
    kind: The alias kind.
    name: The alias name.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""The alias kind.

    Values:
      KIND_UNSPECIFIED: Unknown.
      FIXED: Git tag.
      MOVABLE: Git branch.
      OTHER: Used to specify non-standard aliases. For example, if a Git repo
        has a ref named "refs/foo/bar".
    """
    KIND_UNSPECIFIED = 0
    FIXED = 1
    MOVABLE = 2
    OTHER = 3

  kind = _messages.EnumField('KindValueValuesEnum', 1)
  name = _messages.StringField(2)


class GoogleDevtoolsContaineranalysisV1alpha1CloudRepoSourceContext(_messages.Message):
  r"""A CloudRepoSourceContext denotes a particular revision in a Google Cloud
  Source Repo.

  Fields:
    aliasContext: An alias, which may be a branch or tag.
    repoId: The ID of the repo.
    revisionId: A revision ID.
  """

  aliasContext = _messages.MessageField('GoogleDevtoolsContaineranalysisV1alpha1AliasContext', 1)
  repoId = _messages.MessageField('GoogleDevtoolsContaineranalysisV1alpha1RepoId', 2)
  revisionId = _messages.StringField(3)


class GoogleDevtoolsContaineranalysisV1alpha1GerritSourceContext(_messages.Message):
  r"""A SourceContext referring to a Gerrit project.

  Fields:
    aliasContext: An alias, which may be a branch or tag.
    gerritProject: The full project name within the host. Projects may be
      nested, so "project/subproject" is a valid project name. The "repo name"
      is the hostURI/project.
    hostUri: The URI of a running Gerrit instance.
    revisionId: A revision (commit) ID.
  """

  aliasContext = _messages.MessageField('GoogleDevtoolsContaineranalysisV1alpha1AliasContext', 1)
  gerritProject = _messages.StringField(2)
  hostUri = _messages.StringField(3)
  revisionId = _messages.StringField(4)


class GoogleDevtoolsContaineranalysisV1alpha1GitSourceContext(_messages.Message):
  r"""A GitSourceContext denotes a particular revision in a third party Git
  repository (e.g., GitHub).

  Fields:
    revisionId: Required. Git commit hash.
    url: Git repository URL.
  """

  revisionId = _messages.StringField(1)
  url = _messages.StringField(2)


class GoogleDevtoolsContaineranalysisV1alpha1OperationMetadata(_messages.Message):
  r"""Metadata for all operations used and required for all operations that
  created by Container Analysis Providers

  Fields:
    createTime: Output only. The time this operation was created.
    endTime: Output only. The time that this operation was marked completed or
      failed.
  """

  createTime = _messages.StringField(1)
  endTime = _messages.StringField(2)


class GoogleDevtoolsContaineranalysisV1alpha1ProjectRepoId(_messages.Message):
  r"""Selects a repo using a Google Cloud Platform project ID (e.g., winged-
  cargo-31) and a repo name within that project.

  Fields:
    projectId: The ID of the project.
    repoName: The name of the repo. Leave empty for the default repo.
  """

  projectId = _messages.StringField(1)
  repoName = _messages.StringField(2)


class GoogleDevtoolsContaineranalysisV1alpha1RepoId(_messages.Message):
  r"""A unique identifier for a Cloud Repo.

  Fields:
    projectRepoId: A combination of a project ID and a repo name.
    uid: A server-assigned, globally unique identifier.
  """

  projectRepoId = _messages.MessageField('GoogleDevtoolsContaineranalysisV1alpha1ProjectRepoId', 1)
  uid = _messages.StringField(2)


class GoogleDevtoolsContaineranalysisV1alpha1SlsaProvenanceZeroTwoSlsaBuilder(_messages.Message):
  r"""Identifies the entity that executed the recipe, which is trusted to have
  correctly performed the operation and populated this provenance.

  Fields:
    id: URI indicating the builder's identity.
  """

  id = _messages.StringField(1)


class GoogleDevtoolsContaineranalysisV1alpha1SlsaProvenanceZeroTwoSlsaCompleteness(_messages.Message):
  r"""Indicates that the builder claims certain fields in this message to be
  complete.

  Fields:
    environment: If true, the builder claims that invocation.environment is
      complete.
    materials: If true, the builder claims that materials is complete.
    parameters: If true, the builder claims that invocation.parameters is
      complete.
  """

  environment = _messages.BooleanField(1)
  materials = _messages.BooleanField(2)
  parameters = _messages.BooleanField(3)


class GoogleDevtoolsContaineranalysisV1alpha1SlsaProvenanceZeroTwoSlsaConfigSource(_messages.Message):
  r"""Describes where the config file that kicked off the build came from.
  This is effectively a pointer to the source where buildConfig came from.

  Messages:
    DigestValue: Collection of cryptographic digests for the contents of the
      artifact specified by invocation.configSource.uri.

  Fields:
    digest: Collection of cryptographic digests for the contents of the
      artifact specified by invocation.configSource.uri.
    entryPoint: String identifying the entry point into the build.
    uri: URI indicating the identity of the source of the config.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DigestValue(_messages.Message):
    r"""Collection of cryptographic digests for the contents of the artifact
    specified by invocation.configSource.uri.

    Messages:
      AdditionalProperty: An additional property for a DigestValue object.

    Fields:
      additionalProperties: Additional properties of type DigestValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DigestValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  digest = _messages.MessageField('DigestValue', 1)
  entryPoint = _messages.StringField(2)
  uri = _messages.StringField(3)


class GoogleDevtoolsContaineranalysisV1alpha1SlsaProvenanceZeroTwoSlsaInvocation(_messages.Message):
  r"""Identifies the event that kicked off the build.

  Messages:
    EnvironmentValue: Any other builder-controlled inputs necessary for
      correctly evaluating the build.
    ParametersValue: Collection of all external inputs that influenced the
      build on top of invocation.configSource.

  Fields:
    configSource: Describes where the config file that kicked off the build
      came from.
    environment: Any other builder-controlled inputs necessary for correctly
      evaluating the build.
    parameters: Collection of all external inputs that influenced the build on
      top of invocation.configSource.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvironmentValue(_messages.Message):
    r"""Any other builder-controlled inputs necessary for correctly evaluating
    the build.

    Messages:
      AdditionalProperty: An additional property for a EnvironmentValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvironmentValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParametersValue(_messages.Message):
    r"""Collection of all external inputs that influenced the build on top of
    invocation.configSource.

    Messages:
      AdditionalProperty: An additional property for a ParametersValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  configSource = _messages.MessageField('GoogleDevtoolsContaineranalysisV1alpha1SlsaProvenanceZeroTwoSlsaConfigSource', 1)
  environment = _messages.MessageField('EnvironmentValue', 2)
  parameters = _messages.MessageField('ParametersValue', 3)


class GoogleDevtoolsContaineranalysisV1alpha1SlsaProvenanceZeroTwoSlsaMaterial(_messages.Message):
  r"""The collection of artifacts that influenced the build including sources,
  dependencies, build tools, base images, and so on.

  Messages:
    DigestValue: Collection of cryptographic digests for the contents of this
      artifact.

  Fields:
    digest: Collection of cryptographic digests for the contents of this
      artifact.
    uri: The method by which this artifact was referenced during the build.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DigestValue(_messages.Message):
    r"""Collection of cryptographic digests for the contents of this artifact.

    Messages:
      AdditionalProperty: An additional property for a DigestValue object.

    Fields:
      additionalProperties: Additional properties of type DigestValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DigestValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  digest = _messages.MessageField('DigestValue', 1)
  uri = _messages.StringField(2)


class GoogleDevtoolsContaineranalysisV1alpha1SlsaProvenanceZeroTwoSlsaMetadata(_messages.Message):
  r"""Other properties of the build.

  Fields:
    buildFinishedOn: The timestamp of when the build completed.
    buildInvocationId: Identifies this particular build invocation, which can
      be useful for finding associated logs or other ad-hoc analysis.
    buildStartedOn: The timestamp of when the build started.
    completeness: Indicates that the builder claims certain fields in this
      message to be complete.
    reproducible: If true, the builder claims that running invocation on
      materials will produce bit-for-bit identical output.
  """

  buildFinishedOn = _messages.StringField(1)
  buildInvocationId = _messages.StringField(2)
  buildStartedOn = _messages.StringField(3)
  completeness = _messages.MessageField('GoogleDevtoolsContaineranalysisV1alpha1SlsaProvenanceZeroTwoSlsaCompleteness', 4)
  reproducible = _messages.BooleanField(5)


class GoogleDevtoolsContaineranalysisV1alpha1SourceContext(_messages.Message):
  r"""A SourceContext is a reference to a tree of files. A SourceContext
  together with a path point to a unique revision of a single file or
  directory.

  Messages:
    LabelsValue: Labels with user defined metadata.

  Fields:
    cloudRepo: A SourceContext referring to a revision in a Google Cloud
      Source Repo.
    gerrit: A SourceContext referring to a Gerrit project.
    git: A SourceContext referring to any third party Git repo (e.g., GitHub).
    labels: Labels with user defined metadata.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels with user defined metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cloudRepo = _messages.MessageField('GoogleDevtoolsContaineranalysisV1alpha1CloudRepoSourceContext', 1)
  gerrit = _messages.MessageField('GoogleDevtoolsContaineranalysisV1alpha1GerritSourceContext', 2)
  git = _messages.MessageField('GoogleDevtoolsContaineranalysisV1alpha1GitSourceContext', 3)
  labels = _messages.MessageField('LabelsValue', 4)


class Hash(_messages.Message):
  r"""Container message for hash values.

  Enums:
    TypeValueValuesEnum: The type of hash that was performed.

  Fields:
    type: The type of hash that was performed.
    value: The hash value.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of hash that was performed.

    Values:
      NONE: No hash requested.
      SHA256: A sha256 hash.
    """
    NONE = 0
    SHA256 = 1

  type = _messages.EnumField('TypeValueValuesEnum', 1)
  value = _messages.BytesField(2)


class IdentifierHelper(_messages.Message):
  r"""Helps in identifying the underlying product. This should be treated like
  a one-of field. Only one field should be set in this proto. This is a
  workaround because spanner indexes on one-of fields restrict addition and
  deletion of fields.

  Enums:
    FieldValueValuesEnum: The field that is set in the API proto.

  Fields:
    field: The field that is set in the API proto.
    genericUri: Contains a URI which is vendor-specific. Example: The artifact
      repository URL of an image.
  """

  class FieldValueValuesEnum(_messages.Enum):
    r"""The field that is set in the API proto.

    Values:
      IDENTIFIER_HELPER_FIELD_UNSPECIFIED: The helper isn't set.
      GENERIC_URI: The generic_uri one-of field is set.
    """
    IDENTIFIER_HELPER_FIELD_UNSPECIFIED = 0
    GENERIC_URI = 1

  field = _messages.EnumField('FieldValueValuesEnum', 1)
  genericUri = _messages.StringField(2)


class InTotoProvenance(_messages.Message):
  r"""A InTotoProvenance object.

  Fields:
    builderConfig: required
    materials: The collection of artifacts that influenced the build including
      sources, dependencies, build tools, base images, and so on. This is
      considered to be incomplete unless metadata.completeness.materials is
      true. Unset or null is equivalent to empty.
    metadata: A Metadata attribute.
    recipe: Identifies the configuration used for the build. When combined
      with materials, this SHOULD fully describe the build, such that re-
      running this recipe results in bit-for-bit identical output (if the
      build is reproducible). required
  """

  builderConfig = _messages.MessageField('BuilderConfig', 1)
  materials = _messages.StringField(2, repeated=True)
  metadata = _messages.MessageField('Metadata', 3)
  recipe = _messages.MessageField('Recipe', 4)


class InTotoSlsaProvenanceV1(_messages.Message):
  r"""A InTotoSlsaProvenanceV1 object.

  Fields:
    _type: InToto spec defined at https://github.com/in-
      toto/attestation/tree/main/spec#statement
    predicate: A SlsaProvenanceV1 attribute.
    predicateType: A string attribute.
    subject: A Subject attribute.
  """

  _type = _messages.StringField(1)
  predicate = _messages.MessageField('SlsaProvenanceV1', 2)
  predicateType = _messages.StringField(3)
  subject = _messages.MessageField('Subject', 4, repeated=True)


class InTotoStatement(_messages.Message):
  r"""Spec defined at https://github.com/in-
  toto/attestation/tree/main/spec#statement The serialized InTotoStatement
  will be stored as Envelope.payload. Envelope.payloadType is always
  "application/vnd.in-toto+json".

  Fields:
    _type: Always "https://in-toto.io/Statement/v0.1".
    predicateType: "https://slsa.dev/provenance/v0.1" for SlsaProvenance.
    provenance: Generic Grafeas provenance.
    slsaProvenance: SLSA 0.1 provenance.
    slsaProvenanceZeroTwo: SLSA 0.2 provenance.
    subject: subject is the subjects of the intoto statement
  """

  _type = _messages.StringField(1)
  predicateType = _messages.StringField(2)
  provenance = _messages.MessageField('InTotoProvenance', 3)
  slsaProvenance = _messages.MessageField('SlsaProvenance', 4)
  slsaProvenanceZeroTwo = _messages.MessageField('SlsaProvenanceZeroTwo', 5)
  subject = _messages.MessageField('Subject', 6, repeated=True)


class Installation(_messages.Message):
  r"""This represents how a particular software package may be installed on a
  system.

  Enums:
    ArchitectureValueValuesEnum: Output only. The CPU architecture for which
      packages in this distribution channel were built. Architecture will be
      blank for language packages.

  Fields:
    architecture: Output only. The CPU architecture for which packages in this
      distribution channel were built. Architecture will be blank for language
      packages.
    cpeUri: Output only. The cpe_uri in [CPE
      format](https://cpe.mitre.org/specification/) denoting the package
      manager version distributing a package. The cpe_uri will be blank for
      language packages.
    license: Licenses that have been declared by the authors of the package.
    location: All of the places within the filesystem versions of this package
      have been found.
    name: Output only. The name of the installed package.
    packageType: Output only. The type of package; whether native or non
      native (e.g., ruby gems, node.js packages, etc.).
    version: Output only. The version of the package.
  """

  class ArchitectureValueValuesEnum(_messages.Enum):
    r"""Output only. The CPU architecture for which packages in this
    distribution channel were built. Architecture will be blank for language
    packages.

    Values:
      ARCHITECTURE_UNSPECIFIED: Unknown architecture
      X86: X86 architecture
      X64: X64 architecture
    """
    ARCHITECTURE_UNSPECIFIED = 0
    X86 = 1
    X64 = 2

  architecture = _messages.EnumField('ArchitectureValueValuesEnum', 1)
  cpeUri = _messages.StringField(2)
  license = _messages.MessageField('License', 3)
  location = _messages.MessageField('Location', 4, repeated=True)
  name = _messages.StringField(5)
  packageType = _messages.StringField(6)
  version = _messages.MessageField('Version', 7)


class Justification(_messages.Message):
  r"""Justification provides the justification when the state of the
  assessment if NOT_AFFECTED.

  Enums:
    JustificationTypeValueValuesEnum: The justification type for this
      vulnerability.

  Fields:
    details: Additional details on why this justification was chosen.
    justificationType: The justification type for this vulnerability.
  """

  class JustificationTypeValueValuesEnum(_messages.Enum):
    r"""The justification type for this vulnerability.

    Values:
      JUSTIFICATION_TYPE_UNSPECIFIED: JUSTIFICATION_TYPE_UNSPECIFIED.
      COMPONENT_NOT_PRESENT: The vulnerable component is not present in the
        product.
      VULNERABLE_CODE_NOT_PRESENT: The vulnerable code is not present.
        Typically this case occurs when source code is configured or built in
        a way that excludes the vulnerable code.
      VULNERABLE_CODE_NOT_IN_EXECUTE_PATH: The vulnerable code can not be
        executed. Typically this case occurs when the product includes the
        vulnerable code but does not call or use the vulnerable code.
      VULNERABLE_CODE_CANNOT_BE_CONTROLLED_BY_ADVERSARY: The vulnerable code
        cannot be controlled by an attacker to exploit the vulnerability.
      INLINE_MITIGATIONS_ALREADY_EXIST: The product includes built-in
        protections or features that prevent exploitation of the
        vulnerability. These built-in protections cannot be subverted by the
        attacker and cannot be configured or disabled by the user. These
        mitigations completely prevent exploitation based on known attack
        vectors.
    """
    JUSTIFICATION_TYPE_UNSPECIFIED = 0
    COMPONENT_NOT_PRESENT = 1
    VULNERABLE_CODE_NOT_PRESENT = 2
    VULNERABLE_CODE_NOT_IN_EXECUTE_PATH = 3
    VULNERABLE_CODE_CANNOT_BE_CONTROLLED_BY_ADVERSARY = 4
    INLINE_MITIGATIONS_ALREADY_EXIST = 5

  details = _messages.StringField(1)
  justificationType = _messages.EnumField('JustificationTypeValueValuesEnum', 2)


class Layer(_messages.Message):
  r"""Layer holds metadata specific to a layer of a Docker image.

  Enums:
    DirectiveValueValuesEnum: The recovered Dockerfile directive used to
      construct this layer.

  Fields:
    arguments: The recovered arguments to the Dockerfile directive.
    directive: The recovered Dockerfile directive used to construct this
      layer.
  """

  class DirectiveValueValuesEnum(_messages.Enum):
    r"""The recovered Dockerfile directive used to construct this layer.

    Values:
      DIRECTIVE_UNSPECIFIED: Default value for unsupported/missing directive
      MAINTAINER: https://docs.docker.com/engine/reference/builder/
      RUN: https://docs.docker.com/engine/reference/builder/
      CMD: https://docs.docker.com/engine/reference/builder/
      LABEL: https://docs.docker.com/engine/reference/builder/
      EXPOSE: https://docs.docker.com/engine/reference/builder/
      ENV: https://docs.docker.com/engine/reference/builder/
      ADD: https://docs.docker.com/engine/reference/builder/
      COPY: https://docs.docker.com/reference/builder/#copy
      ENTRYPOINT: https://docs.docker.com/engine/reference/builder/
      VOLUME: https://docs.docker.com/engine/reference/builder/
      USER: https://docs.docker.com/engine/reference/builder/
      WORKDIR: https://docs.docker.com/engine/reference/builder/
      ARG: https://docs.docker.com/engine/reference/builder/
      ONBUILD: https://docs.docker.com/engine/reference/builder/
      STOPSIGNAL: https://docs.docker.com/engine/reference/builder/
      HEALTHCHECK: https://docs.docker.com/engine/reference/builder/
      SHELL: https://docs.docker.com/engine/reference/builder/
    """
    DIRECTIVE_UNSPECIFIED = 0
    MAINTAINER = 1
    RUN = 2
    CMD = 3
    LABEL = 4
    EXPOSE = 5
    ENV = 6
    ADD = 7
    COPY = 8
    ENTRYPOINT = 9
    VOLUME = 10
    USER = 11
    WORKDIR = 12
    ARG = 13
    ONBUILD = 14
    STOPSIGNAL = 15
    HEALTHCHECK = 16
    SHELL = 17

  arguments = _messages.StringField(1)
  directive = _messages.EnumField('DirectiveValueValuesEnum', 2)


class License(_messages.Message):
  r"""License information.

  Fields:
    comments: Comments
    expression: Often a single license can be used to represent the licensing
      terms. Sometimes it is necessary to include a choice of one or more
      licenses or some combination of license identifiers. Examples:
      "LGPL-2.1-only OR MIT", "LGPL-2.1-only AND MIT", "GPL-2.0-or-later WITH
      Bison-exception-2.2".
  """

  comments = _messages.StringField(1)
  expression = _messages.StringField(2)


class ListNoteOccurrencesResponse(_messages.Message):
  r"""Response including listed occurrences for a note.

  Fields:
    nextPageToken: Token to receive the next page of notes.
    occurrences: The occurrences attached to the specified note.
  """

  nextPageToken = _messages.StringField(1)
  occurrences = _messages.MessageField('Occurrence', 2, repeated=True)


class ListNotesResponse(_messages.Message):
  r"""Response including listed notes.

  Fields:
    nextPageToken: The next pagination token in the list response. It should
      be used as page_token for the following request. An empty value means no
      more result.
    notes: The occurrences requested
  """

  nextPageToken = _messages.StringField(1)
  notes = _messages.MessageField('Note', 2, repeated=True)


class ListOccurrencesResponse(_messages.Message):
  r"""Response including listed active occurrences.

  Fields:
    nextPageToken: The next pagination token in the list response. It should
      be used as `page_token` for the following request. An empty value means
      no more results.
    occurrences: The occurrences requested.
  """

  nextPageToken = _messages.StringField(1)
  occurrences = _messages.MessageField('Occurrence', 2, repeated=True)


class ListScanConfigsResponse(_messages.Message):
  r"""A list of scan configs for the project.

  Fields:
    nextPageToken: A page token to pass in order to get more scan configs.
    scanConfigs: The set of scan configs.
  """

  nextPageToken = _messages.StringField(1)
  scanConfigs = _messages.MessageField('ScanConfig', 2, repeated=True)


class Location(_messages.Message):
  r"""An occurrence of a particular package installation found within a
  system's filesystem. e.g. glibc was found in /var/lib/dpkg/status

  Fields:
    cpeUri: Deprecated. The cpe_uri in [cpe
      format](https://cpe.mitre.org/specification/) denoting the package
      manager version distributing a package.
    path: The path from which we gathered that this package/version is
      installed.
    version: Deprecated. The version installed at this location.
  """

  cpeUri = _messages.StringField(1)
  path = _messages.StringField(2)
  version = _messages.MessageField('Version', 3)


class Material(_messages.Message):
  r"""Material is a material used in the generation of the provenance

  Messages:
    DigestValue: digest is a map from a hash algorithm (e.g. sha256) to the
      value in the material

  Fields:
    digest: digest is a map from a hash algorithm (e.g. sha256) to the value
      in the material
    uri: uri is the uri of the material
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DigestValue(_messages.Message):
    r"""digest is a map from a hash algorithm (e.g. sha256) to the value in
    the material

    Messages:
      AdditionalProperty: An additional property for a DigestValue object.

    Fields:
      additionalProperties: Additional properties of type DigestValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DigestValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  digest = _messages.MessageField('DigestValue', 1)
  uri = _messages.StringField(2)


class Metadata(_messages.Message):
  r"""Other properties of the build.

  Fields:
    buildFinishedOn: The timestamp of when the build completed.
    buildInvocationId: Identifies the particular build invocation, which can
      be useful for finding associated logs or other ad-hoc analysis. The
      value SHOULD be globally unique, per in-toto Provenance spec.
    buildStartedOn: The timestamp of when the build started.
    completeness: Indicates that the builder claims certain fields in this
      message to be complete.
    reproducible: If true, the builder claims that running the recipe on
      materials will produce bit-for-bit identical output.
  """

  buildFinishedOn = _messages.StringField(1)
  buildInvocationId = _messages.StringField(2)
  buildStartedOn = _messages.StringField(3)
  completeness = _messages.MessageField('Completeness', 4)
  reproducible = _messages.BooleanField(5)


class NonCompliantFile(_messages.Message):
  r"""Details about files that caused a compliance check to fail.

  Fields:
    displayCommand: Command to display the non-compliant files.
    path: display_command is a single command that can be used to display a
      list of non compliant files. When there is no such command, we can also
      iterate a list of non compliant file using 'path'. Empty if
      `display_command` is set.
    reason: Explains why a file is non compliant for a CIS check.
  """

  displayCommand = _messages.StringField(1)
  path = _messages.StringField(2)
  reason = _messages.StringField(3)


class Note(_messages.Message):
  r"""Provides a detailed description of a `Note`.

  Enums:
    KindValueValuesEnum: Output only. This explicitly denotes which kind of
      note is specified. This field can be used as a filter in list requests.

  Fields:
    attestationAuthority: A note describing an attestation role.
    baseImage: A note describing a base image.
    buildType: Build provenance type for a verifiable build.
    compliance: A note describing a compliance check.
    createTime: Output only. The time this note was created. This field can be
      used as a filter in list requests.
    deployable: A note describing something that can be deployed.
    discovery: A note describing a provider/analysis type.
    dsseAttestation: A note describing a dsse attestation note.
    expirationTime: Time of expiration for this note, null if note does not
      expire.
    kind: Output only. This explicitly denotes which kind of note is
      specified. This field can be used as a filter in list requests.
    longDescription: A detailed description of this `Note`.
    name: The name of the note in the form
      "projects/{provider_project_id}/notes/{NOTE_ID}"
    package: A note describing a package hosted by various package managers.
    relatedUrl: URLs associated with this note
    sbom: A note describing a software bill of materials.
    sbomReference: A note describing a reference to an SBOM.
    shortDescription: A one sentence description of this `Note`.
    spdxFile: A note describing an SPDX File.
    spdxPackage: A note describing an SPDX Package.
    spdxRelationship: A note describing a relationship between SPDX elements.
    updateTime: Output only. The time this note was last updated. This field
      can be used as a filter in list requests.
    upgrade: A note describing an upgrade.
    vulnerabilityAssessment: A note describing a vulnerability assessment.
    vulnerabilityType: A package vulnerability type of note.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""Output only. This explicitly denotes which kind of note is specified.
    This field can be used as a filter in list requests.

    Values:
      KIND_UNSPECIFIED: Unknown
      PACKAGE_VULNERABILITY: The note and occurrence represent a package
        vulnerability.
      BUILD_DETAILS: The note and occurrence assert build provenance.
      IMAGE_BASIS: This represents an image basis relationship.
      PACKAGE_MANAGER: This represents a package installed via a package
        manager.
      DEPLOYABLE: The note and occurrence track deployment events.
      DISCOVERY: The note and occurrence track the initial discovery status of
        a resource.
      ATTESTATION_AUTHORITY: This represents a logical "role" that can attest
        to artifacts.
      UPGRADE: This represents an available software upgrade.
      COMPLIANCE: This represents a compliance check that can be applied to a
        resource.
      SBOM: This represents a software bill of materials.
      SPDX_PACKAGE: This represents an SPDX Package.
      SPDX_FILE: This represents an SPDX File.
      SPDX_RELATIONSHIP: This represents an SPDX Relationship.
      DSSE_ATTESTATION: This represents a DSSE attestation Note
      VULNERABILITY_ASSESSMENT: This represents a Vulnerability Assessment.
      SBOM_REFERENCE: This represents a reference to an SBOM.
    """
    KIND_UNSPECIFIED = 0
    PACKAGE_VULNERABILITY = 1
    BUILD_DETAILS = 2
    IMAGE_BASIS = 3
    PACKAGE_MANAGER = 4
    DEPLOYABLE = 5
    DISCOVERY = 6
    ATTESTATION_AUTHORITY = 7
    UPGRADE = 8
    COMPLIANCE = 9
    SBOM = 10
    SPDX_PACKAGE = 11
    SPDX_FILE = 12
    SPDX_RELATIONSHIP = 13
    DSSE_ATTESTATION = 14
    VULNERABILITY_ASSESSMENT = 15
    SBOM_REFERENCE = 16

  attestationAuthority = _messages.MessageField('AttestationAuthority', 1)
  baseImage = _messages.MessageField('Basis', 2)
  buildType = _messages.MessageField('BuildType', 3)
  compliance = _messages.MessageField('ComplianceNote', 4)
  createTime = _messages.StringField(5)
  deployable = _messages.MessageField('Deployable', 6)
  discovery = _messages.MessageField('Discovery', 7)
  dsseAttestation = _messages.MessageField('DSSEAttestationNote', 8)
  expirationTime = _messages.StringField(9)
  kind = _messages.EnumField('KindValueValuesEnum', 10)
  longDescription = _messages.StringField(11)
  name = _messages.StringField(12)
  package = _messages.MessageField('Package', 13)
  relatedUrl = _messages.MessageField('RelatedUrl', 14, repeated=True)
  sbom = _messages.MessageField('DocumentNote', 15)
  sbomReference = _messages.MessageField('SBOMReferenceNote', 16)
  shortDescription = _messages.StringField(17)
  spdxFile = _messages.MessageField('FileNote', 18)
  spdxPackage = _messages.MessageField('PackageInfoNote', 19)
  spdxRelationship = _messages.MessageField('RelationshipNote', 20)
  updateTime = _messages.StringField(21)
  upgrade = _messages.MessageField('UpgradeNote', 22)
  vulnerabilityAssessment = _messages.MessageField('VulnerabilityAssessmentNote', 23)
  vulnerabilityType = _messages.MessageField('VulnerabilityType', 24)


class Occurrence(_messages.Message):
  r"""`Occurrence` includes information about analysis occurrences for an
  image.

  Enums:
    KindValueValuesEnum: Output only. This explicitly denotes which of the
      `Occurrence` details are specified. This field can be used as a filter
      in list requests.

  Fields:
    attestation: Describes an attestation of an artifact.
    buildDetails: Build details for a verifiable build.
    compliance: Describes whether or not a resource passes compliance checks.
    createTime: Output only. The time this `Occurrence` was created.
    deployment: Describes the deployment of an artifact on a runtime.
    derivedImage: Describes how this resource derives from the basis in the
      associated note.
    discovered: Describes the initial scan status for this resource.
    dsseAttestation: This represents a DSSE attestation occurrence
    envelope: https://github.com/secure-systems-lab/dsse
    installation: Describes the installation of a package on the linked
      resource.
    kind: Output only. This explicitly denotes which of the `Occurrence`
      details are specified. This field can be used as a filter in list
      requests.
    name: Output only. The name of the `Occurrence` in the form
      "projects/{project_id}/occurrences/{OCCURRENCE_ID}"
    noteName: An analysis note associated with this image, in the form
      "providers/{provider_id}/notes/{NOTE_ID}" This field can be used as a
      filter in list requests.
    remediation: A description of actions that can be taken to remedy the
      `Note`
    resource:  The resource for which the `Occurrence` applies.
    resourceUrl: The unique URL of the image or the container for which the
      `Occurrence` applies. For example,
      https://gcr.io/project/image@sha256:foo This field can be used as a
      filter in list requests.
    sbom: Describes a specific software bill of materials document.
    sbomReference: This represents an SBOM reference occurrence
    spdxFile: Describes a specific SPDX File.
    spdxPackage: Describes a specific SPDX Package.
    spdxRelationship: Describes a specific relationship between SPDX elements.
    updateTime: Output only. The time this `Occurrence` was last updated.
    upgrade: Describes an upgrade.
    vulnerabilityDetails: Details of a security vulnerability note.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""Output only. This explicitly denotes which of the `Occurrence` details
    are specified. This field can be used as a filter in list requests.

    Values:
      KIND_UNSPECIFIED: Unknown
      PACKAGE_VULNERABILITY: The note and occurrence represent a package
        vulnerability.
      BUILD_DETAILS: The note and occurrence assert build provenance.
      IMAGE_BASIS: This represents an image basis relationship.
      PACKAGE_MANAGER: This represents a package installed via a package
        manager.
      DEPLOYABLE: The note and occurrence track deployment events.
      DISCOVERY: The note and occurrence track the initial discovery status of
        a resource.
      ATTESTATION_AUTHORITY: This represents a logical "role" that can attest
        to artifacts.
      UPGRADE: This represents an available software upgrade.
      COMPLIANCE: This represents a compliance check that can be applied to a
        resource.
      SBOM: This represents a software bill of materials.
      SPDX_PACKAGE: This represents an SPDX Package.
      SPDX_FILE: This represents an SPDX File.
      SPDX_RELATIONSHIP: This represents an SPDX Relationship.
      DSSE_ATTESTATION: This represents a DSSE attestation Note
      VULNERABILITY_ASSESSMENT: This represents a Vulnerability Assessment.
      SBOM_REFERENCE: This represents a reference to an SBOM.
    """
    KIND_UNSPECIFIED = 0
    PACKAGE_VULNERABILITY = 1
    BUILD_DETAILS = 2
    IMAGE_BASIS = 3
    PACKAGE_MANAGER = 4
    DEPLOYABLE = 5
    DISCOVERY = 6
    ATTESTATION_AUTHORITY = 7
    UPGRADE = 8
    COMPLIANCE = 9
    SBOM = 10
    SPDX_PACKAGE = 11
    SPDX_FILE = 12
    SPDX_RELATIONSHIP = 13
    DSSE_ATTESTATION = 14
    VULNERABILITY_ASSESSMENT = 15
    SBOM_REFERENCE = 16

  attestation = _messages.MessageField('Attestation', 1)
  buildDetails = _messages.MessageField('BuildDetails', 2)
  compliance = _messages.MessageField('ComplianceOccurrence', 3)
  createTime = _messages.StringField(4)
  deployment = _messages.MessageField('Deployment', 5)
  derivedImage = _messages.MessageField('Derived', 6)
  discovered = _messages.MessageField('Discovered', 7)
  dsseAttestation = _messages.MessageField('DSSEAttestationOccurrence', 8)
  envelope = _messages.MessageField('Envelope', 9)
  installation = _messages.MessageField('Installation', 10)
  kind = _messages.EnumField('KindValueValuesEnum', 11)
  name = _messages.StringField(12)
  noteName = _messages.StringField(13)
  remediation = _messages.StringField(14)
  resource = _messages.MessageField('Resource', 15)
  resourceUrl = _messages.StringField(16)
  sbom = _messages.MessageField('DocumentOccurrence', 17)
  sbomReference = _messages.MessageField('SBOMReferenceOccurrence', 18)
  spdxFile = _messages.MessageField('FileOccurrence', 19)
  spdxPackage = _messages.MessageField('PackageInfoOccurrence', 20)
  spdxRelationship = _messages.MessageField('RelationshipOccurrence', 21)
  updateTime = _messages.StringField(22)
  upgrade = _messages.MessageField('UpgradeOccurrence', 23)
  vulnerabilityDetails = _messages.MessageField('VulnerabilityDetails', 24)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class Package(_messages.Message):
  r"""This represents a particular package that is distributed over various
  channels. e.g. glibc (aka libc6) is distributed by many, at various
  versions.

  Enums:
    ArchitectureValueValuesEnum: The CPU architecture for which packages in
      this distribution channel were built. Architecture will be blank for
      language packages.

  Fields:
    architecture: The CPU architecture for which packages in this distribution
      channel were built. Architecture will be blank for language packages.
    cpeUri: The cpe_uri in [CPE format](https://cpe.mitre.org/specification/)
      denoting the package manager version distributing a package. The cpe_uri
      will be blank for language packages.
    description: The description of this package.
    digest: Hash value, typically a file digest, that allows unique
      identification a specific package.
    distribution: The various channels by which a package is distributed.
    license: Licenses that have been declared by the authors of the package.
    maintainer: A freeform text denoting the maintainer of this package.
    name: The name of the package.
    packageType: The type of package; whether native or non native (e.g., ruby
      gems, node.js packages, etc.).
    url: The homepage for this package.
    version: The version of the package.
  """

  class ArchitectureValueValuesEnum(_messages.Enum):
    r"""The CPU architecture for which packages in this distribution channel
    were built. Architecture will be blank for language packages.

    Values:
      ARCHITECTURE_UNSPECIFIED: Unknown architecture
      X86: X86 architecture
      X64: X64 architecture
    """
    ARCHITECTURE_UNSPECIFIED = 0
    X86 = 1
    X64 = 2

  architecture = _messages.EnumField('ArchitectureValueValuesEnum', 1)
  cpeUri = _messages.StringField(2)
  description = _messages.StringField(3)
  digest = _messages.MessageField('Digest', 4, repeated=True)
  distribution = _messages.MessageField('Distribution', 5, repeated=True)
  license = _messages.MessageField('License', 6)
  maintainer = _messages.StringField(7)
  name = _messages.StringField(8)
  packageType = _messages.StringField(9)
  url = _messages.StringField(10)
  version = _messages.MessageField('Version', 11)


class PackageInfoNote(_messages.Message):
  r"""PackageInfoNote represents an SPDX Package Information section:
  https://spdx.github.io/spdx-spec/3-package-information/

  Fields:
    analyzed: Indicates whether the file content of this package has been
      available for or subjected to analysis when creating the SPDX document
    attribution: A place for the SPDX data creator to record, at the package
      level, acknowledgements that may be needed to be communicated in some
      contexts
    checksum: Provide an independently reproducible mechanism that permits
      unique identification of a specific package that correlates to the data
      in this SPDX file
    copyright: Identify the copyright holders of the package, as well as any
      dates present
    detailedDescription: A more detailed description of the package
    downloadLocation: This section identifies the download Universal Resource
      Locator (URL), or a specific location within a version control system
      (VCS) for the package at the time that the SPDX file was created
    externalRefs: ExternalRef
    filesLicenseInfo: Contain the license the SPDX file creator has concluded
      as governing the This field is to contain a list of all licenses found
      in the package. The relationship between licenses (i.e., conjunctive,
      disjunctive) is not specified in this field \u2013 it is simply a
      listing of all licenses found
    homePage: Provide a place for the SPDX file creator to record a web site
      that serves as the package's home page
    licenseDeclared: List the licenses that have been declared by the authors
      of the package
    originator: If the package identified in the SPDX file originated from a
      different person or organization than identified as Package Supplier,
      this field identifies from where or whom the package originally came
    packageType: The type of package: OS, MAVEN, GO, GO_STDLIB, etc.
    summaryDescription: A short description of the package
    supplier: Identify the actual distribution source for the
      package/directory identified in the SPDX file
    title: Identify the full name of the package as given by the Package
      Originator
    verificationCode: This field provides an independently reproducible
      mechanism identifying specific contents of a package based on the actual
      files (except the SPDX file itself, if it is included in the package)
      that make up each package and that correlates to the data in this SPDX
      file
    version: Identify the version of the package
  """

  analyzed = _messages.BooleanField(1)
  attribution = _messages.StringField(2)
  checksum = _messages.StringField(3)
  copyright = _messages.StringField(4)
  detailedDescription = _messages.StringField(5)
  downloadLocation = _messages.StringField(6)
  externalRefs = _messages.MessageField('ExternalRef', 7, repeated=True)
  filesLicenseInfo = _messages.StringField(8, repeated=True)
  homePage = _messages.StringField(9)
  licenseDeclared = _messages.MessageField('License', 10)
  originator = _messages.StringField(11)
  packageType = _messages.StringField(12)
  summaryDescription = _messages.StringField(13)
  supplier = _messages.StringField(14)
  title = _messages.StringField(15)
  verificationCode = _messages.StringField(16)
  version = _messages.StringField(17)


class PackageInfoOccurrence(_messages.Message):
  r"""PackageInfoOccurrence represents an SPDX Package Information section:
  https://spdx.github.io/spdx-spec/3-package-information/

  Fields:
    comment: A place for the SPDX file creator to record any general comments
      about the package being described
    filename: Provide the actual file name of the package, or path of the
      directory being treated as a package
    homePage: Output only. Provide a place for the SPDX file creator to record
      a web site that serves as the package's home page
    id: Uniquely identify any element in an SPDX document which may be
      referenced by other elements
    licenseConcluded: package or alternative values, if the governing license
      cannot be determined
    packageType: Output only. The type of package: OS, MAVEN, GO, GO_STDLIB,
      etc.
    sourceInfo: Provide a place for the SPDX file creator to record any
      relevant background information or additional comments about the origin
      of the package
    summaryDescription: Output only. A short description of the package
    title: Output only. Identify the full name of the package as given by the
      Package Originator
    version: Output only. Identify the version of the package
  """

  comment = _messages.StringField(1)
  filename = _messages.StringField(2)
  homePage = _messages.StringField(3)
  id = _messages.StringField(4)
  licenseConcluded = _messages.MessageField('License', 5)
  packageType = _messages.StringField(6)
  sourceInfo = _messages.StringField(7)
  summaryDescription = _messages.StringField(8)
  title = _messages.StringField(9)
  version = _messages.StringField(10)


class PackageIssue(_messages.Message):
  r"""This message wraps a location affected by a vulnerability and its
  associated fix (if one is available).

  Enums:
    EffectiveSeverityValueValuesEnum: Output only. The distro or language
      system assigned severity for this vulnerability when that is available
      and note provider assigned severity when distro or language system has
      not yet assigned a severity for this vulnerability.

  Fields:
    affectedLocation: The location of the vulnerability.
    effectiveSeverity: Output only. The distro or language system assigned
      severity for this vulnerability when that is available and note provider
      assigned severity when distro or language system has not yet assigned a
      severity for this vulnerability.
    fixedLocation: The location of the available fix for vulnerability.
    packageType: The type of package (e.g. OS, MAVEN, GO).
    severityName: A string attribute.
  """

  class EffectiveSeverityValueValuesEnum(_messages.Enum):
    r"""Output only. The distro or language system assigned severity for this
    vulnerability when that is available and note provider assigned severity
    when distro or language system has not yet assigned a severity for this
    vulnerability.

    Values:
      SEVERITY_UNSPECIFIED: Unknown Impact
      MINIMAL: Minimal Impact
      LOW: Low Impact
      MEDIUM: Medium Impact
      HIGH: High Impact
      CRITICAL: Critical Impact
    """
    SEVERITY_UNSPECIFIED = 0
    MINIMAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5

  affectedLocation = _messages.MessageField('VulnerabilityLocation', 1)
  effectiveSeverity = _messages.EnumField('EffectiveSeverityValueValuesEnum', 2)
  fixedLocation = _messages.MessageField('VulnerabilityLocation', 3)
  packageType = _messages.StringField(4)
  severityName = _messages.StringField(5)


class PgpSignedAttestation(_messages.Message):
  r"""An attestation wrapper with a PGP-compatible signature. This message
  only supports `ATTACHED` signatures, where the payload that is signed is
  included alongside the signature itself in the same file.

  Enums:
    ContentTypeValueValuesEnum: Type (for example schema) of the attestation
      payload that was signed. The verifier must ensure that the provided type
      is one that the verifier supports, and that the attestation payload is a
      valid instantiation of that type (for example by validating a JSON
      schema).

  Fields:
    contentType: Type (for example schema) of the attestation payload that was
      signed. The verifier must ensure that the provided type is one that the
      verifier supports, and that the attestation payload is a valid
      instantiation of that type (for example by validating a JSON schema).
    pgpKeyId: The cryptographic fingerprint of the key used to generate the
      signature, as output by, e.g. `gpg --list-keys`. This should be the
      version 4, full 160-bit fingerprint, expressed as a 40 character
      hexadecimal string. See https://tools.ietf.org/html/rfc4880#section-12.2
      for details. Implementations may choose to acknowledge "LONG", "SHORT",
      or other abbreviated key IDs, but only the full fingerprint is
      guaranteed to work. In gpg, the full fingerprint can be retrieved from
      the `fpr` field returned when calling --list-keys with --with-colons.
      For example: ``` gpg --with-colons --with-fingerprint --force-v4-certs \
      --list-keys <EMAIL> tru::1:1513631572:0:3:1:5 pub:......
      fpr:::::::::24FF6481B76AC91E66A00AC657A93A81EF3AE6FB: ``` Above, the
      fingerprint is `24FF6481B76AC91E66A00AC657A93A81EF3AE6FB`.
    signature: The raw content of the signature, as output by GNU Privacy
      Guard (GPG) or equivalent. Since this message only supports attached
      signatures, the payload that was signed must be attached. While the
      signature format supported is dependent on the verification
      implementation, currently only ASCII-armored (`--armor` to gpg), non-
      clearsigned (`--sign` rather than `--clearsign` to gpg) are supported.
      Concretely, `gpg --sign --armor --output=signature.gpg payload.json`
      will create the signature content expected in this field in
      `signature.gpg` for the `payload.json` attestation payload.
  """

  class ContentTypeValueValuesEnum(_messages.Enum):
    r"""Type (for example schema) of the attestation payload that was signed.
    The verifier must ensure that the provided type is one that the verifier
    supports, and that the attestation payload is a valid instantiation of
    that type (for example by validating a JSON schema).

    Values:
      CONTENT_TYPE_UNSPECIFIED: `ContentType` is not set.
      SIMPLE_SIGNING_JSON: Atomic format attestation signature. See https://gi
        thub.com/containers/image/blob/8a5d2f82a6e3263290c8e0276c3e0f64e77723e
        7/docs/atomic-signature.md The payload extracted from `signature` is a
        JSON blob conforming to the linked schema.
    """
    CONTENT_TYPE_UNSPECIFIED = 0
    SIMPLE_SIGNING_JSON = 1

  contentType = _messages.EnumField('ContentTypeValueValuesEnum', 1)
  pgpKeyId = _messages.StringField(2)
  signature = _messages.StringField(3)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  etag = _messages.BytesField(2)
  version = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class Product(_messages.Message):
  r"""Product contains information about a product and how to uniquely
  identify it.

  Fields:
    id: Token that identifies a product so that it can be referred to from
      other parts in the document. There is no predefined format as long as it
      uniquely identifies a group in the context of the current document.
    identifierHelper: Helps in identifying the underlying product.
    name: Name of the product.
  """

  id = _messages.StringField(1)
  identifierHelper = _messages.MessageField('IdentifierHelper', 2)
  name = _messages.StringField(3)


class ProvenanceBuilder(_messages.Message):
  r"""A ProvenanceBuilder object.

  Messages:
    VersionValue: A VersionValue object.

  Fields:
    builderDependencies: A ResourceDescriptor attribute.
    id: A string attribute.
    version: A VersionValue attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class VersionValue(_messages.Message):
    r"""A VersionValue object.

    Messages:
      AdditionalProperty: An additional property for a VersionValue object.

    Fields:
      additionalProperties: Additional properties of type VersionValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a VersionValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  builderDependencies = _messages.MessageField('ResourceDescriptor', 1, repeated=True)
  id = _messages.StringField(2)
  version = _messages.MessageField('VersionValue', 3)


class Publisher(_messages.Message):
  r"""Publisher contains information about the publisher of this Note.

  Fields:
    issuingAuthority: Provides information about the authority of the issuing
      party to release the document, in particular, the party's constituency
      and responsibilities or other obligations.
    name: Name of the publisher. Examples: 'Google', 'Google Cloud Platform'.
    publisherNamespace: The context or namespace. Contains a URL which is
      under control of the issuing party and can be used as a globally unique
      identifier for that issuing party. Example: https://csaf.io
  """

  issuingAuthority = _messages.StringField(1)
  name = _messages.StringField(2)
  publisherNamespace = _messages.StringField(3)


class Recipe(_messages.Message):
  r"""Steps taken to build the artifact. For a TaskRun, typically each
  container corresponds to one step in the recipe.

  Messages:
    ArgumentsValueListEntry: A ArgumentsValueListEntry object.
    EnvironmentValueListEntry: A EnvironmentValueListEntry object.

  Fields:
    arguments: Collection of all external inputs that influenced the build on
      top of recipe.definedInMaterial and recipe.entryPoint. For example, if
      the recipe type were "make", then this might be the flags passed to make
      aside from the target, which is captured in recipe.entryPoint.
    definedInMaterial: Index in materials containing the recipe steps that are
      not implied by recipe.type. For example, if the recipe type were "make",
      then this would point to the source containing the Makefile, not the
      make program itself. Set to -1 if the recipe doesn't come from a
      material, as zero is default unset value for int64.
    entryPoint: String identifying the entry point into the build. This is
      often a path to a configuration file and/or a target label within that
      file. The syntax and meaning are defined by recipe.type. For example, if
      the recipe type were "make", then this would reference the directory in
      which to run make as well as which target to use.
    environment: Any other builder-controlled inputs necessary for correctly
      evaluating the recipe. Usually only needed for reproducing the build but
      not evaluated as part of policy.
    type: URI indicating what type of recipe was performed. It determines the
      meaning of recipe.entryPoint, recipe.arguments, recipe.environment, and
      materials.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ArgumentsValueListEntry(_messages.Message):
    r"""A ArgumentsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a ArgumentsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ArgumentsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvironmentValueListEntry(_messages.Message):
    r"""A EnvironmentValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a
        EnvironmentValueListEntry object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvironmentValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  arguments = _messages.MessageField('ArgumentsValueListEntry', 1, repeated=True)
  definedInMaterial = _messages.IntegerField(2)
  entryPoint = _messages.StringField(3)
  environment = _messages.MessageField('EnvironmentValueListEntry', 4, repeated=True)
  type = _messages.StringField(5)


class RelatedUrl(_messages.Message):
  r"""Metadata for any related URL information

  Fields:
    label: Label to describe usage of the URL
    url: Specific URL to associate with the note
  """

  label = _messages.StringField(1)
  url = _messages.StringField(2)


class RelationshipNote(_messages.Message):
  r"""RelationshipNote represents an SPDX Relationship section:
  https://spdx.github.io/spdx-spec/7-relationships-between-SPDX-elements/

  Enums:
    TypeValueValuesEnum: The type of relationship between the source and
      target SPDX elements

  Fields:
    type: The type of relationship between the source and target SPDX elements
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of relationship between the source and target SPDX elements

    Values:
      RELATIONSHIP_TYPE_UNSPECIFIED: Unspecified
      DESCRIBES: Is to be used when SPDXRef-DOCUMENT describes SPDXRef-A
      DESCRIBED_BY: Is to be used when SPDXRef-A is described by SPDXREF-
        Document
      CONTAINS: Is to be used when SPDXRef-A contains SPDXRef-B
      CONTAINED_BY: Is to be used when SPDXRef-A is contained by SPDXRef-B
      DEPENDS_ON: Is to be used when SPDXRef-A depends on SPDXRef-B
      DEPENDENCY_OF: Is to be used when SPDXRef-A is dependency of SPDXRef-B
      DEPENDENCY_MANIFEST_OF: Is to be used when SPDXRef-A is a manifest file
        that lists a set of dependencies for SPDXRef-B
      BUILD_DEPENDENCY_OF: Is to be used when SPDXRef-A is a build dependency
        of SPDXRef-B
      DEV_DEPENDENCY_OF: Is to be used when SPDXRef-A is a development
        dependency of SPDXRef-B
      OPTIONAL_DEPENDENCY_OF: Is to be used when SPDXRef-A is an optional
        dependency of SPDXRef-B
      PROVIDED_DEPENDENCY_OF: Is to be used when SPDXRef-A is a to be provided
        dependency of SPDXRef-B
      TEST_DEPENDENCY_OF: Is to be used when SPDXRef-A is a test dependency of
        SPDXRef-B
      RUNTIME_DEPENDENCY_OF: Is to be used when SPDXRef-A is a dependency
        required for the execution of SPDXRef-B
      EXAMPLE_OF: Is to be used when SPDXRef-A is an example of SPDXRef-B
      GENERATES: Is to be used when SPDXRef-A generates SPDXRef-B
      GENERATED_FROM: Is to be used when SPDXRef-A was generated from
        SPDXRef-B
      ANCESTOR_OF: Is to be used when SPDXRef-A is an ancestor (same lineage
        but pre-dates) SPDXRef-B
      DESCENDANT_OF: Is to be used when SPDXRef-A is a descendant of (same
        lineage but postdates) SPDXRef-B
      VARIANT_OF: Is to be used when SPDXRef-A is a variant of (same lineage
        but not clear which came first) SPDXRef-B
      DISTRIBUTION_ARTIFACT: Is to be used when distributing SPDXRef-A
        requires that SPDXRef-B also be distributed
      PATCH_FOR: Is to be used when SPDXRef-A is a patch file for (to be
        applied to) SPDXRef-B
      PATCH_APPLIED: Is to be used when SPDXRef-A is a patch file that has
        been applied to SPDXRef-B
      COPY_OF: Is to be used when SPDXRef-A is an exact copy of SPDXRef-B
      FILE_ADDED: Is to be used when SPDXRef-A is a file that was added to
        SPDXRef-B
      FILE_DELETED: Is to be used when SPDXRef-A is a file that was deleted
        from SPDXRef-B
      FILE_MODIFIED: Is to be used when SPDXRef-A is a file that was modified
        from SPDXRef-B
      EXPANDED_FROM_ARCHIVE: Is to be used when SPDXRef-A is expanded from the
        archive SPDXRef-B
      DYNAMIC_LINK: Is to be used when SPDXRef-A dynamically links to
        SPDXRef-B
      STATIC_LINK: Is to be used when SPDXRef-A statically links to SPDXRef-B
      DATA_FILE_OF: Is to be used when SPDXRef-A is a data file used in
        SPDXRef-B
      TEST_CASE_OF: Is to be used when SPDXRef-A is a test case used in
        testing SPDXRef-B
      BUILD_TOOL_OF: Is to be used when SPDXRef-A is used to build SPDXRef-B
      DEV_TOOL_OF: Is to be used when SPDXRef-A is used as a development tool
        for SPDXRef-B
      TEST_OF: Is to be used when SPDXRef-A is used for testing SPDXRef-B
      TEST_TOOL_OF: Is to be used when SPDXRef-A is used as a test tool for
        SPDXRef-B
      DOCUMENTATION_OF: Is to be used when SPDXRef-A provides documentation of
        SPDXRef-B
      OPTIONAL_COMPONENT_OF: Is to be used when SPDXRef-A is an optional
        component of SPDXRef-B
      METAFILE_OF: Is to be used when SPDXRef-A is a metafile of SPDXRef-B
      PACKAGE_OF: Is to be used when SPDXRef-A is used as a package as part of
        SPDXRef-B
      AMENDS: Is to be used when (current) SPDXRef-DOCUMENT amends the SPDX
        information in SPDXRef-B
      PREREQUISITE_FOR: Is to be used when SPDXRef-A is a prerequisite for
        SPDXRef-B
      HAS_PREREQUISITE: Is to be used when SPDXRef-A has as a prerequisite
        SPDXRef-B
      OTHER: Is to be used for a relationship which has not been defined in
        the formal SPDX specification. A description of the relationship
        should be included in the Relationship comments field
    """
    RELATIONSHIP_TYPE_UNSPECIFIED = 0
    DESCRIBES = 1
    DESCRIBED_BY = 2
    CONTAINS = 3
    CONTAINED_BY = 4
    DEPENDS_ON = 5
    DEPENDENCY_OF = 6
    DEPENDENCY_MANIFEST_OF = 7
    BUILD_DEPENDENCY_OF = 8
    DEV_DEPENDENCY_OF = 9
    OPTIONAL_DEPENDENCY_OF = 10
    PROVIDED_DEPENDENCY_OF = 11
    TEST_DEPENDENCY_OF = 12
    RUNTIME_DEPENDENCY_OF = 13
    EXAMPLE_OF = 14
    GENERATES = 15
    GENERATED_FROM = 16
    ANCESTOR_OF = 17
    DESCENDANT_OF = 18
    VARIANT_OF = 19
    DISTRIBUTION_ARTIFACT = 20
    PATCH_FOR = 21
    PATCH_APPLIED = 22
    COPY_OF = 23
    FILE_ADDED = 24
    FILE_DELETED = 25
    FILE_MODIFIED = 26
    EXPANDED_FROM_ARCHIVE = 27
    DYNAMIC_LINK = 28
    STATIC_LINK = 29
    DATA_FILE_OF = 30
    TEST_CASE_OF = 31
    BUILD_TOOL_OF = 32
    DEV_TOOL_OF = 33
    TEST_OF = 34
    TEST_TOOL_OF = 35
    DOCUMENTATION_OF = 36
    OPTIONAL_COMPONENT_OF = 37
    METAFILE_OF = 38
    PACKAGE_OF = 39
    AMENDS = 40
    PREREQUISITE_FOR = 41
    HAS_PREREQUISITE = 42
    OTHER = 43

  type = _messages.EnumField('TypeValueValuesEnum', 1)


class RelationshipOccurrence(_messages.Message):
  r"""RelationshipOccurrence represents an SPDX Relationship section:
  https://spdx.github.io/spdx-spec/7-relationships-between-SPDX-elements/

  Enums:
    TypeValueValuesEnum: Output only. The type of relationship between the
      source and target SPDX elements

  Fields:
    comment: A place for the SPDX file creator to record any general comments
      about the relationship
    source: Also referred to as SPDXRef-A The source SPDX element (file,
      package, etc)
    target: Also referred to as SPDXRef-B The target SPDC element (file,
      package, etc) In cases where there are "known unknowns", the use of the
      keyword NOASSERTION can be used The keywords NONE can be used to
      indicate that an SPDX element (package/file/snippet) has no other
      elements connected by some relationship to it
    type: Output only. The type of relationship between the source and target
      SPDX elements
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of relationship between the source and target
    SPDX elements

    Values:
      RELATIONSHIP_TYPE_UNSPECIFIED: Unspecified
      DESCRIBES: Is to be used when SPDXRef-DOCUMENT describes SPDXRef-A
      DESCRIBED_BY: Is to be used when SPDXRef-A is described by SPDXREF-
        Document
      CONTAINS: Is to be used when SPDXRef-A contains SPDXRef-B
      CONTAINED_BY: Is to be used when SPDXRef-A is contained by SPDXRef-B
      DEPENDS_ON: Is to be used when SPDXRef-A depends on SPDXRef-B
      DEPENDENCY_OF: Is to be used when SPDXRef-A is dependency of SPDXRef-B
      DEPENDENCY_MANIFEST_OF: Is to be used when SPDXRef-A is a manifest file
        that lists a set of dependencies for SPDXRef-B
      BUILD_DEPENDENCY_OF: Is to be used when SPDXRef-A is a build dependency
        of SPDXRef-B
      DEV_DEPENDENCY_OF: Is to be used when SPDXRef-A is a development
        dependency of SPDXRef-B
      OPTIONAL_DEPENDENCY_OF: Is to be used when SPDXRef-A is an optional
        dependency of SPDXRef-B
      PROVIDED_DEPENDENCY_OF: Is to be used when SPDXRef-A is a to be provided
        dependency of SPDXRef-B
      TEST_DEPENDENCY_OF: Is to be used when SPDXRef-A is a test dependency of
        SPDXRef-B
      RUNTIME_DEPENDENCY_OF: Is to be used when SPDXRef-A is a dependency
        required for the execution of SPDXRef-B
      EXAMPLE_OF: Is to be used when SPDXRef-A is an example of SPDXRef-B
      GENERATES: Is to be used when SPDXRef-A generates SPDXRef-B
      GENERATED_FROM: Is to be used when SPDXRef-A was generated from
        SPDXRef-B
      ANCESTOR_OF: Is to be used when SPDXRef-A is an ancestor (same lineage
        but pre-dates) SPDXRef-B
      DESCENDANT_OF: Is to be used when SPDXRef-A is a descendant of (same
        lineage but postdates) SPDXRef-B
      VARIANT_OF: Is to be used when SPDXRef-A is a variant of (same lineage
        but not clear which came first) SPDXRef-B
      DISTRIBUTION_ARTIFACT: Is to be used when distributing SPDXRef-A
        requires that SPDXRef-B also be distributed
      PATCH_FOR: Is to be used when SPDXRef-A is a patch file for (to be
        applied to) SPDXRef-B
      PATCH_APPLIED: Is to be used when SPDXRef-A is a patch file that has
        been applied to SPDXRef-B
      COPY_OF: Is to be used when SPDXRef-A is an exact copy of SPDXRef-B
      FILE_ADDED: Is to be used when SPDXRef-A is a file that was added to
        SPDXRef-B
      FILE_DELETED: Is to be used when SPDXRef-A is a file that was deleted
        from SPDXRef-B
      FILE_MODIFIED: Is to be used when SPDXRef-A is a file that was modified
        from SPDXRef-B
      EXPANDED_FROM_ARCHIVE: Is to be used when SPDXRef-A is expanded from the
        archive SPDXRef-B
      DYNAMIC_LINK: Is to be used when SPDXRef-A dynamically links to
        SPDXRef-B
      STATIC_LINK: Is to be used when SPDXRef-A statically links to SPDXRef-B
      DATA_FILE_OF: Is to be used when SPDXRef-A is a data file used in
        SPDXRef-B
      TEST_CASE_OF: Is to be used when SPDXRef-A is a test case used in
        testing SPDXRef-B
      BUILD_TOOL_OF: Is to be used when SPDXRef-A is used to build SPDXRef-B
      DEV_TOOL_OF: Is to be used when SPDXRef-A is used as a development tool
        for SPDXRef-B
      TEST_OF: Is to be used when SPDXRef-A is used for testing SPDXRef-B
      TEST_TOOL_OF: Is to be used when SPDXRef-A is used as a test tool for
        SPDXRef-B
      DOCUMENTATION_OF: Is to be used when SPDXRef-A provides documentation of
        SPDXRef-B
      OPTIONAL_COMPONENT_OF: Is to be used when SPDXRef-A is an optional
        component of SPDXRef-B
      METAFILE_OF: Is to be used when SPDXRef-A is a metafile of SPDXRef-B
      PACKAGE_OF: Is to be used when SPDXRef-A is used as a package as part of
        SPDXRef-B
      AMENDS: Is to be used when (current) SPDXRef-DOCUMENT amends the SPDX
        information in SPDXRef-B
      PREREQUISITE_FOR: Is to be used when SPDXRef-A is a prerequisite for
        SPDXRef-B
      HAS_PREREQUISITE: Is to be used when SPDXRef-A has as a prerequisite
        SPDXRef-B
      OTHER: Is to be used for a relationship which has not been defined in
        the formal SPDX specification. A description of the relationship
        should be included in the Relationship comments field
    """
    RELATIONSHIP_TYPE_UNSPECIFIED = 0
    DESCRIBES = 1
    DESCRIBED_BY = 2
    CONTAINS = 3
    CONTAINED_BY = 4
    DEPENDS_ON = 5
    DEPENDENCY_OF = 6
    DEPENDENCY_MANIFEST_OF = 7
    BUILD_DEPENDENCY_OF = 8
    DEV_DEPENDENCY_OF = 9
    OPTIONAL_DEPENDENCY_OF = 10
    PROVIDED_DEPENDENCY_OF = 11
    TEST_DEPENDENCY_OF = 12
    RUNTIME_DEPENDENCY_OF = 13
    EXAMPLE_OF = 14
    GENERATES = 15
    GENERATED_FROM = 16
    ANCESTOR_OF = 17
    DESCENDANT_OF = 18
    VARIANT_OF = 19
    DISTRIBUTION_ARTIFACT = 20
    PATCH_FOR = 21
    PATCH_APPLIED = 22
    COPY_OF = 23
    FILE_ADDED = 24
    FILE_DELETED = 25
    FILE_MODIFIED = 26
    EXPANDED_FROM_ARCHIVE = 27
    DYNAMIC_LINK = 28
    STATIC_LINK = 29
    DATA_FILE_OF = 30
    TEST_CASE_OF = 31
    BUILD_TOOL_OF = 32
    DEV_TOOL_OF = 33
    TEST_OF = 34
    TEST_TOOL_OF = 35
    DOCUMENTATION_OF = 36
    OPTIONAL_COMPONENT_OF = 37
    METAFILE_OF = 38
    PACKAGE_OF = 39
    AMENDS = 40
    PREREQUISITE_FOR = 41
    HAS_PREREQUISITE = 42
    OTHER = 43

  comment = _messages.StringField(1)
  source = _messages.StringField(2)
  target = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class Remediation(_messages.Message):
  r"""Specifies details on how to handle (and presumably, fix) a
  vulnerability.

  Enums:
    RemediationTypeValueValuesEnum: The type of remediation that can be
      applied.

  Fields:
    details: Contains a comprehensive human-readable discussion of the
      remediation.
    remediationType: The type of remediation that can be applied.
    remediationUri: Contains the URL where to obtain the remediation.
  """

  class RemediationTypeValueValuesEnum(_messages.Enum):
    r"""The type of remediation that can be applied.

    Values:
      REMEDIATION_TYPE_UNSPECIFIED: No remediation type specified.
      MITIGATION: A MITIGATION is available.
      NO_FIX_PLANNED: No fix is planned.
      NONE_AVAILABLE: Not available.
      VENDOR_FIX: A vendor fix is available.
      WORKAROUND: A workaround is available.
    """
    REMEDIATION_TYPE_UNSPECIFIED = 0
    MITIGATION = 1
    NO_FIX_PLANNED = 2
    NONE_AVAILABLE = 3
    VENDOR_FIX = 4
    WORKAROUND = 5

  details = _messages.StringField(1)
  remediationType = _messages.EnumField('RemediationTypeValueValuesEnum', 2)
  remediationUri = _messages.MessageField('URI', 3)


class RepoSource(_messages.Message):
  r"""RepoSource describes the location of the source in a Google Cloud Source
  Repository.

  Fields:
    branchName: Name of the branch to build.
    commitSha: Explicit commit SHA to build.
    projectId: ID of the project that owns the repo.
    repoName: Name of the repo.
    tagName: Name of the tag to build.
  """

  branchName = _messages.StringField(1)
  commitSha = _messages.StringField(2)
  projectId = _messages.StringField(3)
  repoName = _messages.StringField(4)
  tagName = _messages.StringField(5)


class Resource(_messages.Message):
  r""" Resource is an entity that can have metadata. E.g., a Docker image.

  Fields:
    contentHash: The hash of the resource content. E.g., the Docker digest.
    name: The name of the resource. E.g., the name of a Docker image -
      "Debian".
    uri: The unique URI of the resource. E.g.,
      "https://gcr.io/project/image@sha256:foo" for a Docker image.
  """

  contentHash = _messages.MessageField('Hash', 1)
  name = _messages.StringField(2)
  uri = _messages.StringField(3)


class ResourceDescriptor(_messages.Message):
  r"""A ResourceDescriptor object.

  Messages:
    AnnotationsValue: A AnnotationsValue object.
    DigestValue: A DigestValue object.

  Fields:
    annotations: A AnnotationsValue attribute.
    content: A byte attribute.
    digest: A DigestValue attribute.
    downloadLocation: A string attribute.
    mediaType: A string attribute.
    name: A string attribute.
    uri: A string attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""A AnnotationsValue object.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DigestValue(_messages.Message):
    r"""A DigestValue object.

    Messages:
      AdditionalProperty: An additional property for a DigestValue object.

    Fields:
      additionalProperties: Additional properties of type DigestValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DigestValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  content = _messages.BytesField(2)
  digest = _messages.MessageField('DigestValue', 3)
  downloadLocation = _messages.StringField(4)
  mediaType = _messages.StringField(5)
  name = _messages.StringField(6)
  uri = _messages.StringField(7)


class RunDetails(_messages.Message):
  r"""A RunDetails object.

  Fields:
    builder: A ProvenanceBuilder attribute.
    byproducts: A ResourceDescriptor attribute.
    metadata: A BuildMetadata attribute.
  """

  builder = _messages.MessageField('ProvenanceBuilder', 1)
  byproducts = _messages.MessageField('ResourceDescriptor', 2, repeated=True)
  metadata = _messages.MessageField('BuildMetadata', 3)


class SBOMReferenceNote(_messages.Message):
  r"""The note representing an SBOM reference.

  Fields:
    format: The format that SBOM takes. E.g. may be spdx, cyclonedx, etc...
    version: The version of the format that the SBOM takes. E.g. if the format
      is spdx, the version may be 2.3.
  """

  format = _messages.StringField(1)
  version = _messages.StringField(2)


class SBOMReferenceOccurrence(_messages.Message):
  r"""The occurrence representing an SBOM reference as applied to a specific
  resource. The occurrence follows the DSSE specification. See
  https://github.com/secure-systems-lab/dsse/blob/master/envelope.md for more
  details.

  Fields:
    payload: The actual payload that contains the SBOM reference data.
    payloadType: The kind of payload that SbomReferenceIntotoPayload takes.
      Since it's in the intoto format, this value is expected to be
      'application/vnd.in-toto+json'.
    signatures: The signatures over the payload.
  """

  payload = _messages.MessageField('SbomReferenceIntotoPayload', 1)
  payloadType = _messages.StringField(2)
  signatures = _messages.MessageField('EnvelopeSignature', 3, repeated=True)


class SBOMStatus(_messages.Message):
  r"""The status of an SBOM generation.

  Enums:
    SbomStateValueValuesEnum: Output only. The progress of the SBOM
      generation.

  Fields:
    error: Output only. If there was an error generating an SBOM, this will
      indicate what that error was.
    sbomState: Output only. The progress of the SBOM generation.
  """

  class SbomStateValueValuesEnum(_messages.Enum):
    r"""Output only. The progress of the SBOM generation.

    Values:
      SBOM_STATE_UNSPECIFIED: Default unknown state.
      PENDING: SBOM scanning is pending.
      COMPLETE: SBOM scanning has completed.
    """
    SBOM_STATE_UNSPECIFIED = 0
    PENDING = 1
    COMPLETE = 2

  error = _messages.StringField(1)
  sbomState = _messages.EnumField('SbomStateValueValuesEnum', 2)


class SbomReferenceIntotoPayload(_messages.Message):
  r"""The actual payload that contains the SBOM Reference data. The payload
  follows the intoto statement specification. See https://github.com/in-
  toto/attestation/blob/main/spec/v1.0/statement.md for more details.

  Fields:
    _type: Identifier for the schema of the Statement.
    predicate: Additional parameters of the Predicate. Includes the actual
      data about the SBOM.
    predicateType: URI identifying the type of the Predicate.
    subject: Set of software artifacts that the attestation applies to. Each
      element represents a single software artifact.
  """

  _type = _messages.StringField(1)
  predicate = _messages.MessageField('SbomReferenceIntotoPredicate', 2)
  predicateType = _messages.StringField(3)
  subject = _messages.MessageField('Subject', 4, repeated=True)


class SbomReferenceIntotoPredicate(_messages.Message):
  r"""A predicate which describes the SBOM being referenced.

  Messages:
    DigestValue: A map of algorithm to digest of the contents of the SBOM.

  Fields:
    digest: A map of algorithm to digest of the contents of the SBOM.
    location: The location of the SBOM.
    mimeType: The mime type of the SBOM.
    referrerId: The person or system referring this predicate to the consumer.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DigestValue(_messages.Message):
    r"""A map of algorithm to digest of the contents of the SBOM.

    Messages:
      AdditionalProperty: An additional property for a DigestValue object.

    Fields:
      additionalProperties: Additional properties of type DigestValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DigestValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  digest = _messages.MessageField('DigestValue', 1)
  location = _messages.StringField(2)
  mimeType = _messages.StringField(3)
  referrerId = _messages.StringField(4)


class ScanConfig(_messages.Message):
  r"""Indicates various scans and whether they are turned on or off.

  Fields:
    createTime: Output only. The time this scan config was created.
    description: Output only. A human-readable description of what the
      `ScanConfig` does.
    enabled: Indicates whether the Scan is enabled.
    name: Output only. The name of the ScanConfig in the form
      "projects/{project_id}/scanConfigs/{scan_config_id}".
    updateTime: Output only. The time this scan config was last updated.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  enabled = _messages.BooleanField(3)
  name = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
  """

  policy = _messages.MessageField('Policy', 1)


class SeverityCount(_messages.Message):
  r"""The number of occurrences created for a specific severity.

  Enums:
    SeverityValueValuesEnum: The severity of the occurrences.

  Fields:
    count: The number of occurrences with the severity.
    severity: The severity of the occurrences.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity of the occurrences.

    Values:
      SEVERITY_UNSPECIFIED: Unknown Impact
      MINIMAL: Minimal Impact
      LOW: Low Impact
      MEDIUM: Medium Impact
      HIGH: High Impact
      CRITICAL: Critical Impact
    """
    SEVERITY_UNSPECIFIED = 0
    MINIMAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5

  count = _messages.IntegerField(1)
  severity = _messages.EnumField('SeverityValueValuesEnum', 2)


class SlsaBuilder(_messages.Message):
  r"""SlsaBuilder encapsulates the identity of the builder of this provenance.

  Fields:
    id: id is the id of the slsa provenance builder
  """

  id = _messages.StringField(1)


class SlsaCompleteness(_messages.Message):
  r"""Indicates that the builder claims certain fields in this message to be
  complete.

  Fields:
    arguments: If true, the builder claims that recipe.arguments is complete,
      meaning that all external inputs are properly captured in the recipe.
    environment: If true, the builder claims that recipe.environment is
      claimed to be complete.
    materials: If true, the builder claims that materials are complete,
      usually through some controls to prevent network access. Sometimes
      called "hermetic".
  """

  arguments = _messages.BooleanField(1)
  environment = _messages.BooleanField(2)
  materials = _messages.BooleanField(3)


class SlsaMetadata(_messages.Message):
  r"""Other properties of the build.

  Fields:
    buildFinishedOn: The timestamp of when the build completed.
    buildInvocationId: Identifies the particular build invocation, which can
      be useful for finding associated logs or other ad-hoc analysis. The
      value SHOULD be globally unique, per in-toto Provenance spec.
    buildStartedOn: The timestamp of when the build started.
    completeness: Indicates that the builder claims certain fields in this
      message to be complete.
    reproducible: If true, the builder claims that running the recipe on
      materials will produce bit-for-bit identical output.
  """

  buildFinishedOn = _messages.StringField(1)
  buildInvocationId = _messages.StringField(2)
  buildStartedOn = _messages.StringField(3)
  completeness = _messages.MessageField('SlsaCompleteness', 4)
  reproducible = _messages.BooleanField(5)


class SlsaProvenance(_messages.Message):
  r"""SlsaProvenance is the slsa provenance as defined by the slsa spec.

  Fields:
    builder: builder is the builder of this provenance
    materials: The collection of artifacts that influenced the build including
      sources, dependencies, build tools, base images, and so on. This is
      considered to be incomplete unless metadata.completeness.materials is
      true. Unset or null is equivalent to empty.
    metadata: metadata is the metadata of the provenance
    recipe: Identifies the configuration used for the build. When combined
      with materials, this SHOULD fully describe the build, such that re-
      running this recipe results in bit-for-bit identical output (if the
      build is reproducible).
  """

  builder = _messages.MessageField('SlsaBuilder', 1)
  materials = _messages.MessageField('Material', 2, repeated=True)
  metadata = _messages.MessageField('SlsaMetadata', 3)
  recipe = _messages.MessageField('SlsaRecipe', 4)


class SlsaProvenanceV1(_messages.Message):
  r"""Keep in sync with schema at https://github.com/slsa-
  framework/slsa/blob/main/docs/provenance/schema/v1/provenance.proto Builder
  renamed to ProvenanceBuilder because of Java conflicts.

  Fields:
    buildDefinition: A BuildDefinition attribute.
    runDetails: A RunDetails attribute.
  """

  buildDefinition = _messages.MessageField('BuildDefinition', 1)
  runDetails = _messages.MessageField('RunDetails', 2)


class SlsaProvenanceZeroTwo(_messages.Message):
  r"""SlsaProvenanceZeroTwo is the slsa provenance as defined by the slsa
  spec. See full explanation of fields at slsa.dev/provenance/v0.2.

  Messages:
    BuildConfigValue: Lists the steps in the build.

  Fields:
    buildConfig: Lists the steps in the build.
    buildType: URI indicating what type of build was performed.
    builder: Identifies the entity that executed the recipe, which is trusted
      to have correctly performed the operation and populated this provenance.
    invocation: Identifies the event that kicked off the build.
    materials: The collection of artifacts that influenced the build including
      sources, dependencies, build tools, base images, and so on.
    metadata: Other properties of the build.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class BuildConfigValue(_messages.Message):
    r"""Lists the steps in the build.

    Messages:
      AdditionalProperty: An additional property for a BuildConfigValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a BuildConfigValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  buildConfig = _messages.MessageField('BuildConfigValue', 1)
  buildType = _messages.StringField(2)
  builder = _messages.MessageField('GoogleDevtoolsContaineranalysisV1alpha1SlsaProvenanceZeroTwoSlsaBuilder', 3)
  invocation = _messages.MessageField('GoogleDevtoolsContaineranalysisV1alpha1SlsaProvenanceZeroTwoSlsaInvocation', 4)
  materials = _messages.MessageField('GoogleDevtoolsContaineranalysisV1alpha1SlsaProvenanceZeroTwoSlsaMaterial', 5, repeated=True)
  metadata = _messages.MessageField('GoogleDevtoolsContaineranalysisV1alpha1SlsaProvenanceZeroTwoSlsaMetadata', 6)


class SlsaRecipe(_messages.Message):
  r"""Steps taken to build the artifact. For a TaskRun, typically each
  container corresponds to one step in the recipe.

  Messages:
    ArgumentsValue: Collection of all external inputs that influenced the
      build on top of recipe.definedInMaterial and recipe.entryPoint. For
      example, if the recipe type were "make", then this might be the flags
      passed to make aside from the target, which is captured in
      recipe.entryPoint. Depending on the recipe Type, the structure may be
      different.
    EnvironmentValue: Any other builder-controlled inputs necessary for
      correctly evaluating the recipe. Usually only needed for reproducing the
      build but not evaluated as part of policy. Depending on the recipe Type,
      the structure may be different.

  Fields:
    arguments: Collection of all external inputs that influenced the build on
      top of recipe.definedInMaterial and recipe.entryPoint. For example, if
      the recipe type were "make", then this might be the flags passed to make
      aside from the target, which is captured in recipe.entryPoint. Depending
      on the recipe Type, the structure may be different.
    definedInMaterial: Index in materials containing the recipe steps that are
      not implied by recipe.type. For example, if the recipe type were "make",
      then this would point to the source containing the Makefile, not the
      make program itself. Set to -1 if the recipe doesn't come from a
      material, as zero is default unset value for int64.
    entryPoint: String identifying the entry point into the build. This is
      often a path to a configuration file and/or a target label within that
      file. The syntax and meaning are defined by recipe.type. For example, if
      the recipe type were "make", then this would reference the directory in
      which to run make as well as which target to use.
    environment: Any other builder-controlled inputs necessary for correctly
      evaluating the recipe. Usually only needed for reproducing the build but
      not evaluated as part of policy. Depending on the recipe Type, the
      structure may be different.
    type: URI indicating what type of recipe was performed. It determines the
      meaning of recipe.entryPoint, recipe.arguments, recipe.environment, and
      materials.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ArgumentsValue(_messages.Message):
    r"""Collection of all external inputs that influenced the build on top of
    recipe.definedInMaterial and recipe.entryPoint. For example, if the recipe
    type were "make", then this might be the flags passed to make aside from
    the target, which is captured in recipe.entryPoint. Depending on the
    recipe Type, the structure may be different.

    Messages:
      AdditionalProperty: An additional property for a ArgumentsValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ArgumentsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvironmentValue(_messages.Message):
    r"""Any other builder-controlled inputs necessary for correctly evaluating
    the recipe. Usually only needed for reproducing the build but not
    evaluated as part of policy. Depending on the recipe Type, the structure
    may be different.

    Messages:
      AdditionalProperty: An additional property for a EnvironmentValue
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvironmentValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  arguments = _messages.MessageField('ArgumentsValue', 1)
  definedInMaterial = _messages.IntegerField(2)
  entryPoint = _messages.StringField(3)
  environment = _messages.MessageField('EnvironmentValue', 4)
  type = _messages.StringField(5)


class Source(_messages.Message):
  r"""Source describes the location of the source used for the build.

  Messages:
    FileHashesValue: Hash(es) of the build source, which can be used to verify
      that the original source integrity was maintained in the build. The keys
      to this map are file paths used as build source and the values contain
      the hash values for those files. If the build source came in a single
      package such as a gzipped tarfile (.tar.gz), the FileHash will be for
      the single path to that file.

  Fields:
    additionalContexts: If provided, some of the source code used for the
      build may be found in these locations, in the case where the source
      repository had multiple remotes or submodules. This list will not
      include the context specified in the context field.
    artifactStorageSource: If provided, the input binary artifacts for the
      build came from this location.
    context: If provided, the source code used for the build came from this
      location.
    fileHashes: Hash(es) of the build source, which can be used to verify that
      the original source integrity was maintained in the build. The keys to
      this map are file paths used as build source and the values contain the
      hash values for those files. If the build source came in a single
      package such as a gzipped tarfile (.tar.gz), the FileHash will be for
      the single path to that file.
    repoSource: If provided, get source from this location in a Cloud Repo.
    storageSource: If provided, get the source from this location in Google
      Cloud Storage.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FileHashesValue(_messages.Message):
    r"""Hash(es) of the build source, which can be used to verify that the
    original source integrity was maintained in the build. The keys to this
    map are file paths used as build source and the values contain the hash
    values for those files. If the build source came in a single package such
    as a gzipped tarfile (.tar.gz), the FileHash will be for the single path
    to that file.

    Messages:
      AdditionalProperty: An additional property for a FileHashesValue object.

    Fields:
      additionalProperties: Additional properties of type FileHashesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FileHashesValue object.

      Fields:
        key: Name of the additional property.
        value: A FileHashes attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('FileHashes', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalContexts = _messages.MessageField('GoogleDevtoolsContaineranalysisV1alpha1SourceContext', 1, repeated=True)
  artifactStorageSource = _messages.MessageField('StorageSource', 2)
  context = _messages.MessageField('GoogleDevtoolsContaineranalysisV1alpha1SourceContext', 3)
  fileHashes = _messages.MessageField('FileHashesValue', 4)
  repoSource = _messages.MessageField('RepoSource', 5)
  storageSource = _messages.MessageField('StorageSource', 6)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StorageSource(_messages.Message):
  r"""StorageSource describes the location of the source in an archive file in
  Google Cloud Storage.

  Fields:
    bucket: Google Cloud Storage bucket containing source (see [Bucket Name
      Requirements] (https://cloud.google.com/storage/docs/bucket-
      naming#requirements)).
    generation: Google Cloud Storage generation for the object.
    object: Google Cloud Storage object containing source.
  """

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)


class Subject(_messages.Message):
  r"""Subject refers to the subject of the intoto statement

  Messages:
    DigestValue: "": "" Algorithms can be e.g. sha256, sha512 See
      https://github.com/in-
      toto/attestation/blob/main/spec/field_types.md#DigestSet

  Fields:
    digest: "": "" Algorithms can be e.g. sha256, sha512 See
      https://github.com/in-
      toto/attestation/blob/main/spec/field_types.md#DigestSet
    name: name is the name of the Subject used here
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DigestValue(_messages.Message):
    r""""": "" Algorithms can be e.g. sha256, sha512 See
    https://github.com/in-
    toto/attestation/blob/main/spec/field_types.md#DigestSet

    Messages:
      AdditionalProperty: An additional property for a DigestValue object.

    Fields:
      additionalProperties: Additional properties of type DigestValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DigestValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  digest = _messages.MessageField('DigestValue', 1)
  name = _messages.StringField(2)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TimeSpan(_messages.Message):
  r"""Start and end times for a build execution phase. Next ID: 3

  Fields:
    endTime: End of time span.
    startTime: Start of time span.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class URI(_messages.Message):
  r"""An URI message.

  Fields:
    label: A label for the URI.
    uri: The unique resource identifier.
  """

  label = _messages.StringField(1)
  uri = _messages.StringField(2)


class UpdateOperationRequest(_messages.Message):
  r"""Request for updating an existing operation

  Fields:
    operation: The operation to create.
    updateMask: The fields to update.
  """

  operation = _messages.MessageField('Operation', 1)
  updateMask = _messages.StringField(2)


class UpgradeDistribution(_messages.Message):
  r"""The Upgrade Distribution represents metadata about the Upgrade for each
  operating system (CPE). Some distributions have additional metadata around
  updates, classifying them into various categories and severities.

  Fields:
    classification: The operating system classification of this Upgrade, as
      specified by the upstream operating system upgrade feed.
    cpeUri: Required - The specific operating system this metadata applies to.
      See https://cpe.mitre.org/specification/.
    cve: The cve that would be resolved by this upgrade.
    severity: The severity as specified by the upstream operating system.
  """

  classification = _messages.StringField(1)
  cpeUri = _messages.StringField(2)
  cve = _messages.StringField(3, repeated=True)
  severity = _messages.StringField(4)


class UpgradeNote(_messages.Message):
  r"""An Upgrade Note represents a potential upgrade of a package to a given
  version. For each package version combination (i.e. bash 4.0, bash 4.1, bash
  4.1.2), there will be a Upgrade Note.

  Fields:
    distributions: Metadata about the upgrade for each specific operating
      system.
    package: Required - The package this Upgrade is for.
    version: Required - The version of the package in machine + human readable
      form.
  """

  distributions = _messages.MessageField('UpgradeDistribution', 1, repeated=True)
  package = _messages.StringField(2)
  version = _messages.MessageField('Version', 3)


class UpgradeOccurrence(_messages.Message):
  r"""An Upgrade Occurrence represents that a specific resource_url could
  install a specific upgrade. This presence is supplied via local sources
  (i.e. it is present in the mirror and the running system has noticed its
  availability).

  Fields:
    distribution: Metadata about the upgrade for available for the specific
      operating system for the resource_url. This allows efficient filtering,
      as well as making it easier to use the occurrence.
    package: Required - The package this Upgrade is for.
    parsedVersion: Required - The version of the package in a machine + human
      readable form.
  """

  distribution = _messages.MessageField('UpgradeDistribution', 1)
  package = _messages.StringField(2)
  parsedVersion = _messages.MessageField('Version', 3)


class Version(_messages.Message):
  r"""Version contains structured information about the version of the
  package. For a discussion of this in Debian/Ubuntu:
  http://serverfault.com/questions/604541/debian-packages-version-convention
  For a discussion of this in Redhat/Fedora/Centos:
  http://blog.jasonantman.com/2014/07/how-yum-and-rpm-compare-versions/

  Enums:
    KindValueValuesEnum: Distinguish between sentinel MIN/MAX versions and
      normal versions. If kind is not NORMAL, then the other fields are
      ignored.

  Fields:
    epoch: Used to correct mistakes in the version numbering scheme.
    inclusive: Whether this version is vulnerable, when defining the version
      bounds. For example, if the minimum version is 2.0, inclusive=true would
      say 2.0 is vulnerable, while inclusive=false would say it's not
    kind: Distinguish between sentinel MIN/MAX versions and normal versions.
      If kind is not NORMAL, then the other fields are ignored.
    name: The main part of the version name.
    revision: The iteration of the package build from the above version.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""Distinguish between sentinel MIN/MAX versions and normal versions. If
    kind is not NORMAL, then the other fields are ignored.

    Values:
      NORMAL: A standard package version, defined by the other fields.
      MINIMUM: A special version representing negative infinity, other fields
        are ignored.
      MAXIMUM: A special version representing positive infinity, other fields
        are ignored.
    """
    NORMAL = 0
    MINIMUM = 1
    MAXIMUM = 2

  epoch = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  inclusive = _messages.BooleanField(2)
  kind = _messages.EnumField('KindValueValuesEnum', 3)
  name = _messages.StringField(4)
  revision = _messages.StringField(5)


class VexAssessment(_messages.Message):
  r"""VexAssessment provides all publisher provided Vex information that is
  related to this vulnerability.

  Enums:
    StateValueValuesEnum: Provides the state of this Vulnerability assessment.

  Fields:
    cve: Holds the MITRE standard Common Vulnerabilities and Exposures (CVE)
      tracking number for the vulnerability.
    impacts: Contains information about the impact of this vulnerability, this
      will change with time.
    justification: Justification provides the justification when the state of
      the assessment if NOT_AFFECTED.
    noteName: The VulnerabilityAssessment note from which this VexAssessment
      was generated. This will be of the form:
      `projects/[PROJECT_ID]/notes/[NOTE_ID]`.
    relatedUris: Holds a list of references associated with this vulnerability
      item and assessment. These uris have additional information about the
      vulnerability and the assessment itself. E.g. Link to a document which
      details how this assessment concluded the state of this vulnerability.
    remediations: Specifies details on how to handle (and presumably, fix) a
      vulnerability.
    state: Provides the state of this Vulnerability assessment.
    vulnerabilityId: The vulnerability identifier for this Assessment. Will
      hold one of common identifiers e.g. CVE, GHSA etc.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Provides the state of this Vulnerability assessment.

    Values:
      STATE_UNSPECIFIED: No state is specified.
      AFFECTED: This product is known to be affected by this vulnerability.
      NOT_AFFECTED: This product is known to be not affected by this
        vulnerability.
      FIXED: This product contains a fix for this vulnerability.
      UNDER_INVESTIGATION: It is not known yet whether these versions are or
        are not affected by the vulnerability. However, it is still under
        investigation.
    """
    STATE_UNSPECIFIED = 0
    AFFECTED = 1
    NOT_AFFECTED = 2
    FIXED = 3
    UNDER_INVESTIGATION = 4

  cve = _messages.StringField(1)
  impacts = _messages.StringField(2, repeated=True)
  justification = _messages.MessageField('Justification', 3)
  noteName = _messages.StringField(4)
  relatedUris = _messages.MessageField('URI', 5, repeated=True)
  remediations = _messages.MessageField('Remediation', 6, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  vulnerabilityId = _messages.StringField(8)


class Volume(_messages.Message):
  r"""Volume describes a Docker container volume which is mounted into build
  steps in order to persist files across build step execution. Next ID: 3

  Fields:
    name: Name of the volume to mount. Volume names must be unique per build
      step and must be valid names for Docker volumes. Each named volume must
      be used by at least two build steps.
    path: Path at which to mount the volume. Paths must be absolute and cannot
      conflict with other volume paths on the same build step or with certain
      reserved volume paths.
  """

  name = _messages.StringField(1)
  path = _messages.StringField(2)


class VulnerabilityAssessmentNote(_messages.Message):
  r"""A single VulnerabilityAssessmentNote represents one particular product's
  vulnerability assessment for one CVE. Multiple VulnerabilityAssessmentNotes
  together form a Vex statement. Please go/sds-vex-example for a sample Vex
  statement in the CSAF format.

  Fields:
    assessment: Represents a vulnerability assessment for the product.
    languageCode: Identifies the language used by this document, corresponding
      to IETF BCP 47 / RFC 5646.
    longDescription: A detailed description of this Vex.
    product: The product affected by this vex.
    publisher: Publisher details of this Note.
    shortDescription: A one sentence description of this Vex.
    title: The title of the note. E.g. `Vex-Debian-11.4`
  """

  assessment = _messages.MessageField('Assessment', 1)
  languageCode = _messages.StringField(2)
  longDescription = _messages.StringField(3)
  product = _messages.MessageField('Product', 4)
  publisher = _messages.MessageField('Publisher', 5)
  shortDescription = _messages.StringField(6)
  title = _messages.StringField(7)


class VulnerabilityDetails(_messages.Message):
  r"""Used by Occurrence to point to where the vulnerability exists and how to
  fix it.

  Enums:
    CvssVersionValueValuesEnum: Output only. CVSS version used to populate
      cvss_score and severity.
    EffectiveSeverityValueValuesEnum: The distro assigned severity for this
      vulnerability when that is available and note provider assigned severity
      when distro has not yet assigned a severity for this vulnerability. When
      there are multiple package issues for this vulnerability, they can have
      different effective severities because some might come from the distro
      and some might come from installed language packs (e.g. Maven JARs or Go
      binaries). For this reason, it is advised to use the effective severity
      on the PackageIssue level, as this field may eventually be deprecated.
      In the case where multiple PackageIssues have different effective
      severities, the one set here will be the highest severity of any of the
      PackageIssues.
    SeverityValueValuesEnum: Output only. The note provider assigned Severity
      of the vulnerability.

  Fields:
    cvssScore: Output only. The CVSS score of this vulnerability. CVSS score
      is on a scale of 0-10 where 0 indicates low severity and 10 indicates
      high severity.
    cvssV2: The CVSS v2 score of this vulnerability.
    cvssV3: The CVSS v3 score of this vulnerability.
    cvssVersion: Output only. CVSS version used to populate cvss_score and
      severity.
    effectiveSeverity: The distro assigned severity for this vulnerability
      when that is available and note provider assigned severity when distro
      has not yet assigned a severity for this vulnerability. When there are
      multiple package issues for this vulnerability, they can have different
      effective severities because some might come from the distro and some
      might come from installed language packs (e.g. Maven JARs or Go
      binaries). For this reason, it is advised to use the effective severity
      on the PackageIssue level, as this field may eventually be deprecated.
      In the case where multiple PackageIssues have different effective
      severities, the one set here will be the highest severity of any of the
      PackageIssues.
    packageIssue: The set of affected locations and their fixes (if available)
      within the associated resource.
    severity: Output only. The note provider assigned Severity of the
      vulnerability.
    type: The type of package; whether native or non native(ruby gems, node.js
      packages etc). This may be deprecated in the future because we can have
      multiple PackageIssues with different package types.
    vexAssessment: VexAssessment provides all publisher provided Vex
      information that is related to this vulnerability for this resource.
  """

  class CvssVersionValueValuesEnum(_messages.Enum):
    r"""Output only. CVSS version used to populate cvss_score and severity.

    Values:
      CVSS_VERSION_UNSPECIFIED: CVSS Version unspecified.
      CVSS_VERSION_2: CVSS v2.
      CVSS_VERSION_3: CVSS v3.
    """
    CVSS_VERSION_UNSPECIFIED = 0
    CVSS_VERSION_2 = 1
    CVSS_VERSION_3 = 2

  class EffectiveSeverityValueValuesEnum(_messages.Enum):
    r"""The distro assigned severity for this vulnerability when that is
    available and note provider assigned severity when distro has not yet
    assigned a severity for this vulnerability. When there are multiple
    package issues for this vulnerability, they can have different effective
    severities because some might come from the distro and some might come
    from installed language packs (e.g. Maven JARs or Go binaries). For this
    reason, it is advised to use the effective severity on the PackageIssue
    level, as this field may eventually be deprecated. In the case where
    multiple PackageIssues have different effective severities, the one set
    here will be the highest severity of any of the PackageIssues.

    Values:
      SEVERITY_UNSPECIFIED: Unknown Impact
      MINIMAL: Minimal Impact
      LOW: Low Impact
      MEDIUM: Medium Impact
      HIGH: High Impact
      CRITICAL: Critical Impact
    """
    SEVERITY_UNSPECIFIED = 0
    MINIMAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Output only. The note provider assigned Severity of the vulnerability.

    Values:
      SEVERITY_UNSPECIFIED: Unknown Impact
      MINIMAL: Minimal Impact
      LOW: Low Impact
      MEDIUM: Medium Impact
      HIGH: High Impact
      CRITICAL: Critical Impact
    """
    SEVERITY_UNSPECIFIED = 0
    MINIMAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5

  cvssScore = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  cvssV2 = _messages.MessageField('CVSS', 2)
  cvssV3 = _messages.MessageField('CVSS', 3)
  cvssVersion = _messages.EnumField('CvssVersionValueValuesEnum', 4)
  effectiveSeverity = _messages.EnumField('EffectiveSeverityValueValuesEnum', 5)
  packageIssue = _messages.MessageField('PackageIssue', 6, repeated=True)
  severity = _messages.EnumField('SeverityValueValuesEnum', 7)
  type = _messages.StringField(8)
  vexAssessment = _messages.MessageField('VexAssessment', 9)


class VulnerabilityLocation(_messages.Message):
  r"""The location of the vulnerability

  Fields:
    cpeUri: The cpe_uri in [cpe format] (https://cpe.mitre.org/specification/)
      format. Examples include distro or storage location for vulnerable jar.
      This field can be used as a filter in list requests.
    fileLocation: The file location at which this package was found.
    package: The package being described.
    version: The version of the package being described. This field can be
      used as a filter in list requests.
  """

  cpeUri = _messages.StringField(1)
  fileLocation = _messages.MessageField('FileLocation', 2, repeated=True)
  package = _messages.StringField(3)
  version = _messages.MessageField('Version', 4)


class VulnerabilityType(_messages.Message):
  r"""VulnerabilityType provides metadata about a security vulnerability.

  Enums:
    CvssVersionValueValuesEnum: CVSS version used to populate cvss_score and
      severity.
    SeverityValueValuesEnum: Note provider assigned impact of the
      vulnerability

  Fields:
    cvssScore: The CVSS score for this Vulnerability.
    cvssV2: The full description of the CVSS for version 2.
    cvssVersion: CVSS version used to populate cvss_score and severity.
    cwe: A list of CWE for this vulnerability. For details, see:
      https://cwe.mitre.org/index.html
    details: All information about the package to specifically identify this
      vulnerability. One entry per (version range and cpe_uri) the package
      vulnerability has manifested in.
    severity: Note provider assigned impact of the vulnerability
  """

  class CvssVersionValueValuesEnum(_messages.Enum):
    r"""CVSS version used to populate cvss_score and severity.

    Values:
      CVSS_VERSION_UNSPECIFIED: CVSS Version unspecified.
      CVSS_VERSION_2: CVSS v2.
      CVSS_VERSION_3: CVSS v3.
    """
    CVSS_VERSION_UNSPECIFIED = 0
    CVSS_VERSION_2 = 1
    CVSS_VERSION_3 = 2

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Note provider assigned impact of the vulnerability

    Values:
      SEVERITY_UNSPECIFIED: Unknown Impact
      MINIMAL: Minimal Impact
      LOW: Low Impact
      MEDIUM: Medium Impact
      HIGH: High Impact
      CRITICAL: Critical Impact
    """
    SEVERITY_UNSPECIFIED = 0
    MINIMAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5

  cvssScore = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  cvssV2 = _messages.MessageField('CVSS', 2)
  cvssVersion = _messages.EnumField('CvssVersionValueValuesEnum', 3)
  cwe = _messages.StringField(4, repeated=True)
  details = _messages.MessageField('Detail', 5, repeated=True)
  severity = _messages.EnumField('SeverityValueValuesEnum', 6)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

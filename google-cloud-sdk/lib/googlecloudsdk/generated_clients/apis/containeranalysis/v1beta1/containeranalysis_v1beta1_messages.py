"""Generated message classes for containeranalysis version v1beta1.

An implementation of the Grafeas API, which stores, and enables querying and
retrieval of critical metadata about all of your software artifacts.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'containeranalysis'


class AliasContext(_messages.Message):
  r"""An alias to a repo revision.

  Enums:
    KindValueValuesEnum: The alias kind.

  Fields:
    kind: The alias kind.
    name: The alias name.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""The alias kind.

    Values:
      KIND_UNSPECIFIED: Unknown.
      FIXED: Git tag.
      MOVABLE: Git branch.
      OTHER: Used to specify non-standard aliases. For example, if a Git repo
        has a ref named "refs/foo/bar".
    """
    KIND_UNSPECIFIED = 0
    FIXED = 1
    MOVABLE = 2
    OTHER = 3

  kind = _messages.EnumField('KindValueValuesEnum', 1)
  name = _messages.StringField(2)


class AnalysisCompleted(_messages.Message):
  r"""Indicates which analysis completed successfully. Multiple types of
  analysis can be performed on a single resource.

  Fields:
    analysisType: A string attribute.
  """

  analysisType = _messages.StringField(1, repeated=True)


class Artifact(_messages.Message):
  r"""Artifact describes a build product.

  Fields:
    checksum: Hash or checksum value of a binary, or Docker Registry 2.0
      digest of a container.
    id: Artifact ID, if any; for container images, this will be a URL by
      digest like `gcr.io/projectID/imagename@sha256:123456`.
    names: Related artifact names. This may be the path to a binary or jar
      file, or in the case of a container build, the name used to push the
      container image to Google Container Registry, as presented to `docker
      push`. Note that a single Artifact ID can have multiple names, for
      example if two tags are applied to one image.
  """

  checksum = _messages.StringField(1)
  id = _messages.StringField(2)
  names = _messages.StringField(3, repeated=True)


class ArtifactHashes(_messages.Message):
  r"""Defines a hash object for use in Materials and Products.

  Fields:
    sha256: A string attribute.
  """

  sha256 = _messages.StringField(1)


class ArtifactRule(_messages.Message):
  r"""Defines an object to declare an in-toto artifact rule

  Fields:
    artifactRule: A string attribute.
  """

  artifactRule = _messages.StringField(1, repeated=True)


class Assessment(_messages.Message):
  r"""Assessment provides all information that is related to a single
  vulnerability for this product.

  Enums:
    StateValueValuesEnum: Provides the state of this Vulnerability assessment.

  Fields:
    cve: Holds the MITRE standard Common Vulnerabilities and Exposures (CVE)
      tracking number for the vulnerability. Deprecated: Use vulnerability_id
      instead to denote CVEs.
    impacts: Contains information about the impact of this vulnerability, this
      will change with time.
    justification: Justification provides the justification when the state of
      the assessment if NOT_AFFECTED.
    longDescription: A detailed description of this Vex.
    relatedUris: Holds a list of references associated with this vulnerability
      item and assessment. These uris have additional information about the
      vulnerability and the assessment itself. E.g. Link to a document which
      details how this assessment concluded the state of this vulnerability.
    remediations: Specifies details on how to handle (and presumably, fix) a
      vulnerability.
    shortDescription: A one sentence description of this Vex.
    state: Provides the state of this Vulnerability assessment.
    vulnerabilityId: The vulnerability identifier for this Assessment. Will
      hold one of common identifiers e.g. CVE, GHSA etc.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Provides the state of this Vulnerability assessment.

    Values:
      STATE_UNSPECIFIED: No state is specified.
      AFFECTED: This product is known to be affected by this vulnerability.
      NOT_AFFECTED: This product is known to be not affected by this
        vulnerability.
      FIXED: This product contains a fix for this vulnerability.
      UNDER_INVESTIGATION: It is not known yet whether these versions are or
        are not affected by the vulnerability. However, it is still under
        investigation.
    """
    STATE_UNSPECIFIED = 0
    AFFECTED = 1
    NOT_AFFECTED = 2
    FIXED = 3
    UNDER_INVESTIGATION = 4

  cve = _messages.StringField(1)
  impacts = _messages.StringField(2, repeated=True)
  justification = _messages.MessageField('Justification', 3)
  longDescription = _messages.StringField(4)
  relatedUris = _messages.MessageField('RelatedUrl', 5, repeated=True)
  remediations = _messages.MessageField('Remediation', 6, repeated=True)
  shortDescription = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  vulnerabilityId = _messages.StringField(9)


class Attestation(_messages.Message):
  r"""Occurrence that represents a single "attestation". The authenticity of
  an attestation can be verified using the attached signature. If the verifier
  trusts the public key of the signer, then verifying the signature is
  sufficient to establish trust. In this circumstance, the authority to which
  this attestation is attached is primarily useful for look-up (how to find
  this attestation if you already know the authority and artifact to be
  verified) and intent (which authority was this attestation intended to sign
  for).

  Fields:
    genericSignedAttestation: A GenericSignedAttestation attribute.
    pgpSignedAttestation: A PGP signed attestation.
  """

  genericSignedAttestation = _messages.MessageField('GenericSignedAttestation', 1)
  pgpSignedAttestation = _messages.MessageField('PgpSignedAttestation', 2)


class Authority(_messages.Message):
  r"""Note kind that represents a logical attestation "role" or "authority".
  For example, an organization might have one `Authority` for "QA" and one for
  "build". This note is intended to act strictly as a grouping mechanism for
  the attached occurrences (Attestations). This grouping mechanism also
  provides a security boundary, since IAM ACLs gate the ability for a
  principle to attach an occurrence to a given note. It also provides a single
  point of lookup to find all attached attestation occurrences, even if they
  don't all live in the same project.

  Fields:
    hint: Hint hints at the purpose of the attestation authority.
  """

  hint = _messages.MessageField('Hint', 1)


class Basis(_messages.Message):
  r"""Basis describes the base image portion (Note) of the DockerImage
  relationship. Linked occurrences are derived from this or an equivalent
  image via: FROM Or an equivalent reference, e.g. a tag of the resource_url.

  Fields:
    fingerprint: Required. Immutable. The fingerprint of the base image.
    resourceUrl: Required. Immutable. The resource_url for the resource
      representing the basis of associated occurrence images.
  """

  fingerprint = _messages.MessageField('Fingerprint', 1)
  resourceUrl = _messages.StringField(2)


class BatchCreateNotesRequest(_messages.Message):
  r"""Request to create notes in batch.

  Messages:
    NotesValue: Required. The notes to create, the key is expected to be the
      note ID. Max allowed length is 1000.

  Fields:
    notes: Required. The notes to create, the key is expected to be the note
      ID. Max allowed length is 1000.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NotesValue(_messages.Message):
    r"""Required. The notes to create, the key is expected to be the note ID.
    Max allowed length is 1000.

    Messages:
      AdditionalProperty: An additional property for a NotesValue object.

    Fields:
      additionalProperties: Additional properties of type NotesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NotesValue object.

      Fields:
        key: Name of the additional property.
        value: A Note attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Note', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  notes = _messages.MessageField('NotesValue', 1)


class BatchCreateNotesResponse(_messages.Message):
  r"""Response for creating notes in batch.

  Fields:
    notes: The notes that were created.
  """

  notes = _messages.MessageField('Note', 1, repeated=True)


class BatchCreateOccurrencesRequest(_messages.Message):
  r"""Request to create occurrences in batch.

  Fields:
    occurrences: Required. The occurrences to create. Max allowed length is
      1000.
  """

  occurrences = _messages.MessageField('Occurrence', 1, repeated=True)


class BatchCreateOccurrencesResponse(_messages.Message):
  r"""Response for creating occurrences in batch.

  Fields:
    occurrences: The occurrences that were created.
  """

  occurrences = _messages.MessageField('Occurrence', 1, repeated=True)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class Build(_messages.Message):
  r"""Note holding the version of the provider's builder and the signature of
  the provenance message in the build details occurrence.

  Fields:
    builderVersion: Required. Immutable. Version of the builder which produced
      this build.
    signature: Signature of the build in occurrences pointing to this build
      note containing build details.
  """

  builderVersion = _messages.StringField(1)
  signature = _messages.MessageField('BuildSignature', 2)


class BuildDefinition(_messages.Message):
  r"""A BuildDefinition object.

  Messages:
    ExternalParametersValue: A ExternalParametersValue object.
    InternalParametersValue: A InternalParametersValue object.

  Fields:
    buildType: A string attribute.
    externalParameters: A ExternalParametersValue attribute.
    internalParameters: A InternalParametersValue attribute.
    resolvedDependencies: A ResourceDescriptor attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExternalParametersValue(_messages.Message):
    r"""A ExternalParametersValue object.

    Messages:
      AdditionalProperty: An additional property for a ExternalParametersValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExternalParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class InternalParametersValue(_messages.Message):
    r"""A InternalParametersValue object.

    Messages:
      AdditionalProperty: An additional property for a InternalParametersValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a InternalParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  buildType = _messages.StringField(1)
  externalParameters = _messages.MessageField('ExternalParametersValue', 2)
  internalParameters = _messages.MessageField('InternalParametersValue', 3)
  resolvedDependencies = _messages.MessageField('ResourceDescriptor', 4, repeated=True)


class BuildMetadata(_messages.Message):
  r"""A BuildMetadata object.

  Fields:
    finishedOn: A string attribute.
    invocationId: A string attribute.
    startedOn: A string attribute.
  """

  finishedOn = _messages.StringField(1)
  invocationId = _messages.StringField(2)
  startedOn = _messages.StringField(3)


class BuildProvenance(_messages.Message):
  r"""Provenance of a build. Contains all information needed to verify the
  full details about the build from source to completion.

  Messages:
    BuildOptionsValue: Special options applied to this build. This is a catch-
      all field where build providers can enter any desired additional
      details.

  Fields:
    buildOptions: Special options applied to this build. This is a catch-all
      field where build providers can enter any desired additional details.
    builderVersion: Version string of the builder at the time this build was
      executed.
    builtArtifacts: Output of the build.
    commands: Commands requested by the build.
    createTime: Time at which the build was created.
    creator: E-mail address of the user who initiated this build. Note that
      this was the user's e-mail address at the time the build was initiated;
      this address may not represent the same end-user for all time.
    endTime: Time at which execution of the build was finished.
    id: Required. Unique identifier of the build.
    logsUri: URI where any logs for this provenance were written.
    projectId: ID of the project.
    sourceProvenance: Details of the Source input to the build.
    startTime: Time at which execution of the build was started.
    triggerId: Trigger identifier if the build was triggered automatically;
      empty if not.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class BuildOptionsValue(_messages.Message):
    r"""Special options applied to this build. This is a catch-all field where
    build providers can enter any desired additional details.

    Messages:
      AdditionalProperty: An additional property for a BuildOptionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type BuildOptionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a BuildOptionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  buildOptions = _messages.MessageField('BuildOptionsValue', 1)
  builderVersion = _messages.StringField(2)
  builtArtifacts = _messages.MessageField('Artifact', 3, repeated=True)
  commands = _messages.MessageField('Command', 4, repeated=True)
  createTime = _messages.StringField(5)
  creator = _messages.StringField(6)
  endTime = _messages.StringField(7)
  id = _messages.StringField(8)
  logsUri = _messages.StringField(9)
  projectId = _messages.StringField(10)
  sourceProvenance = _messages.MessageField('Source', 11)
  startTime = _messages.StringField(12)
  triggerId = _messages.StringField(13)


class BuildSignature(_messages.Message):
  r"""Message encapsulating the signature of the verified build.

  Enums:
    KeyTypeValueValuesEnum: The type of the key, either stored in `public_key`
      or referenced in `key_id`.

  Fields:
    keyId: An ID for the key used to sign. This could be either an ID for the
      key stored in `public_key` (such as the ID or fingerprint for a PGP key,
      or the CN for a cert), or a reference to an external key (such as a
      reference to a key in Cloud Key Management Service).
    keyType: The type of the key, either stored in `public_key` or referenced
      in `key_id`.
    publicKey: Public key of the builder which can be used to verify that the
      related findings are valid and unchanged. If `key_type` is empty, this
      defaults to PEM encoded public keys. This field may be empty if `key_id`
      references an external key. For Cloud Build based signatures, this is a
      PEM encoded public key. To verify the Cloud Build signature, place the
      contents of this field into a file (public.pem). The signature field is
      base64-decoded into its binary representation in signature.bin, and the
      provenance bytes from `BuildDetails` are base64-decoded into a binary
      representation in signed.bin. OpenSSL can then verify the signature:
      `openssl sha256 -verify public.pem -signature signature.bin signed.bin`
    signature: Required. Signature of the related `BuildProvenance`. In JSON,
      this is base-64 encoded.
  """

  class KeyTypeValueValuesEnum(_messages.Enum):
    r"""The type of the key, either stored in `public_key` or referenced in
    `key_id`.

    Values:
      KEY_TYPE_UNSPECIFIED: `KeyType` is not set.
      PGP_ASCII_ARMORED: `PGP ASCII Armored` public key.
      PKIX_PEM: `PKIX PEM` public key.
    """
    KEY_TYPE_UNSPECIFIED = 0
    PGP_ASCII_ARMORED = 1
    PKIX_PEM = 2

  keyId = _messages.StringField(1)
  keyType = _messages.EnumField('KeyTypeValueValuesEnum', 2)
  publicKey = _messages.StringField(3)
  signature = _messages.BytesField(4)


class BuildStep(_messages.Message):
  r"""A step in the build pipeline. Next ID: 21

  Enums:
    StatusValueValuesEnum: Output only. Status of the build step. At this
      time, build step status is only updated on build completion; step status
      is not updated in real-time as the build progresses.

  Fields:
    allowExitCodes: Allow this build step to fail without failing the entire
      build if and only if the exit code is one of the specified codes. If
      allow_failure is also specified, this field will take precedence.
    allowFailure: Allow this build step to fail without failing the entire
      build. If false, the entire build will fail if this step fails.
      Otherwise, the build will succeed, but this step will still have a
      failure status. Error information will be reported in the failure_detail
      field.
    args: A list of arguments that will be presented to the step when it is
      started. If the image used to run the step's container has an
      entrypoint, the `args` are used as arguments to that entrypoint. If the
      image does not define an entrypoint, the first element in args is used
      as the entrypoint, and the remainder will be used as arguments.
    automapSubstitutions: Option to include built-in and custom substitutions
      as env variables for this build step. This option will override the
      global option in BuildOption.
    dir: Working directory to use when running this step's container. If this
      value is a relative path, it is relative to the build's working
      directory. If this value is absolute, it may be outside the build's
      working directory, in which case the contents of the path may not be
      persisted across build step executions, unless a `volume` for that path
      is specified. If the build specifies a `RepoSource` with `dir` and a
      step with a `dir`, which specifies an absolute path, the `RepoSource`
      `dir` is ignored for the step's execution.
    entrypoint: Entrypoint to be used instead of the build step image's
      default entrypoint. If unset, the image's default entrypoint is used.
    env: A list of environment variable definitions to be used when running a
      step. The elements are of the form "KEY=VALUE" for the environment
      variable "KEY" being given the value "VALUE".
    exitCode: Output only. Return code from running the step.
    id: Unique identifier for this build step, used in `wait_for` to reference
      this build step as a dependency.
    name: Required. The name of the container image that will run this
      particular build step. If the image is available in the host's Docker
      daemon's cache, it will be run directly. If not, the host will attempt
      to pull the image first, using the builder service account's credentials
      if necessary. The Docker daemon's cache will already have the latest
      versions of all of the officially supported build steps
      ([https://github.com/GoogleCloudPlatform/cloud-
      builders](https://github.com/GoogleCloudPlatform/cloud-builders)). The
      Docker daemon will also have cached many of the layers for some popular
      images, like "ubuntu", "debian", but they will be refreshed at the time
      you attempt to use them. If you built an image in a previous build step,
      it will be stored in the host's Docker daemon's cache and is available
      to use as the name for a later build step.
    pullTiming: Output only. Stores timing information for pulling this build
      step's builder image only.
    script: A shell script to be executed in the step. When script is
      provided, the user cannot specify the entrypoint or args.
    secretEnv: A list of environment variables which are encrypted using a
      Cloud Key Management Service crypto key. These values must be specified
      in the build's `Secret`.
    status: Output only. Status of the build step. At this time, build step
      status is only updated on build completion; step status is not updated
      in real-time as the build progresses.
    timeout: Time limit for executing this build step. If not defined, the
      step has no time limit and will be allowed to continue to run until
      either it completes or the build itself times out.
    timing: Output only. Stores timing information for executing this build
      step.
    volumes: List of volumes to mount into the build step. Each volume is
      created as an empty volume prior to execution of the build step. Upon
      completion of the build, volumes and their contents are discarded. Using
      a named volume in only one step is not valid as it is indicative of a
      build request with an incorrect configuration.
    waitFor: The ID(s) of the step(s) that this build step depends on. This
      build step will not start until all the build steps in `wait_for` have
      completed successfully. If `wait_for` is empty, this build step will
      start when all previous build steps in the `Build.Steps` list have
      completed successfully.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. Status of the build step. At this time, build step status
    is only updated on build completion; step status is not updated in real-
    time as the build progresses.

    Values:
      STATUS_UNKNOWN: Status of the build is unknown.
      PENDING: Build has been created and is pending execution and queuing. It
        has not been queued.
      QUEUING: Build has been received and is being queued.
      QUEUED: Build or step is queued; work has not yet begun.
      WORKING: Build or step is being executed.
      SUCCESS: Build or step finished successfully.
      FAILURE: Build or step failed to complete successfully.
      INTERNAL_ERROR: Build or step failed due to an internal cause.
      TIMEOUT: Build or step took longer than was allowed.
      CANCELLED: Build or step was canceled by a user.
      EXPIRED: Build was enqueued for longer than the value of `queue_ttl`.
    """
    STATUS_UNKNOWN = 0
    PENDING = 1
    QUEUING = 2
    QUEUED = 3
    WORKING = 4
    SUCCESS = 5
    FAILURE = 6
    INTERNAL_ERROR = 7
    TIMEOUT = 8
    CANCELLED = 9
    EXPIRED = 10

  allowExitCodes = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)
  allowFailure = _messages.BooleanField(2)
  args = _messages.StringField(3, repeated=True)
  automapSubstitutions = _messages.BooleanField(4)
  dir = _messages.StringField(5)
  entrypoint = _messages.StringField(6)
  env = _messages.StringField(7, repeated=True)
  exitCode = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  id = _messages.StringField(9)
  name = _messages.StringField(10)
  pullTiming = _messages.MessageField('TimeSpan', 11)
  script = _messages.StringField(12)
  secretEnv = _messages.StringField(13, repeated=True)
  status = _messages.EnumField('StatusValueValuesEnum', 14)
  timeout = _messages.StringField(15)
  timing = _messages.MessageField('TimeSpan', 16)
  volumes = _messages.MessageField('Volume', 17, repeated=True)
  waitFor = _messages.StringField(18, repeated=True)


class ByProducts(_messages.Message):
  r"""Defines an object for the byproducts field in in-toto links. The
  suggested fields are "stderr", "stdout", and "return-value".

  Messages:
    CustomValuesValue: A CustomValuesValue object.

  Fields:
    customValues: A CustomValuesValue attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomValuesValue(_messages.Message):
    r"""A CustomValuesValue object.

    Messages:
      AdditionalProperty: An additional property for a CustomValuesValue
        object.

    Fields:
      additionalProperties: Additional properties of type CustomValuesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomValuesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  customValues = _messages.MessageField('CustomValuesValue', 1)


class CVSS(_messages.Message):
  r"""Common Vulnerability Scoring System. This message is compatible with
  CVSS v2 and v3. For CVSS v2 details, see https://www.first.org/cvss/v2/guide
  CVSS v2 calculator: https://nvd.nist.gov/vuln-metrics/cvss/v2-calculator For
  CVSS v3 details, see https://www.first.org/cvss/specification-document CVSS
  v3 calculator: https://nvd.nist.gov/vuln-metrics/cvss/v3-calculator

  Enums:
    AttackComplexityValueValuesEnum: Defined in CVSS v3, CVSS v2
    AttackVectorValueValuesEnum: Base Metrics Represents the intrinsic
      characteristics of a vulnerability that are constant over time and
      across user environments. Defined in CVSS v3, CVSS v2
    AuthenticationValueValuesEnum: Defined in CVSS v2
    AvailabilityImpactValueValuesEnum: Defined in CVSS v3, CVSS v2
    ConfidentialityImpactValueValuesEnum: Defined in CVSS v3, CVSS v2
    IntegrityImpactValueValuesEnum: Defined in CVSS v3, CVSS v2
    PrivilegesRequiredValueValuesEnum: Defined in CVSS v3
    ScopeValueValuesEnum: Defined in CVSS v3
    UserInteractionValueValuesEnum: Defined in CVSS v3

  Fields:
    attackComplexity: Defined in CVSS v3, CVSS v2
    attackVector: Base Metrics Represents the intrinsic characteristics of a
      vulnerability that are constant over time and across user environments.
      Defined in CVSS v3, CVSS v2
    authentication: Defined in CVSS v2
    availabilityImpact: Defined in CVSS v3, CVSS v2
    baseScore: The base score is a function of the base metric scores.
    confidentialityImpact: Defined in CVSS v3, CVSS v2
    exploitabilityScore: A number attribute.
    impactScore: A number attribute.
    integrityImpact: Defined in CVSS v3, CVSS v2
    privilegesRequired: Defined in CVSS v3
    scope: Defined in CVSS v3
    userInteraction: Defined in CVSS v3
  """

  class AttackComplexityValueValuesEnum(_messages.Enum):
    r"""Defined in CVSS v3, CVSS v2

    Values:
      ATTACK_COMPLEXITY_UNSPECIFIED: Defined in CVSS v3, CVSS v2
      ATTACK_COMPLEXITY_LOW: Defined in CVSS v3, CVSS v2
      ATTACK_COMPLEXITY_HIGH: Defined in CVSS v3, CVSS v2
      ATTACK_COMPLEXITY_MEDIUM: Defined in CVSS v2
    """
    ATTACK_COMPLEXITY_UNSPECIFIED = 0
    ATTACK_COMPLEXITY_LOW = 1
    ATTACK_COMPLEXITY_HIGH = 2
    ATTACK_COMPLEXITY_MEDIUM = 3

  class AttackVectorValueValuesEnum(_messages.Enum):
    r"""Base Metrics Represents the intrinsic characteristics of a
    vulnerability that are constant over time and across user environments.
    Defined in CVSS v3, CVSS v2

    Values:
      ATTACK_VECTOR_UNSPECIFIED: Defined in CVSS v3, CVSS v2
      ATTACK_VECTOR_NETWORK: Defined in CVSS v3, CVSS v2
      ATTACK_VECTOR_ADJACENT: Defined in CVSS v3, CVSS v2
      ATTACK_VECTOR_LOCAL: Defined in CVSS v3, CVSS v2
      ATTACK_VECTOR_PHYSICAL: Defined in CVSS v3
    """
    ATTACK_VECTOR_UNSPECIFIED = 0
    ATTACK_VECTOR_NETWORK = 1
    ATTACK_VECTOR_ADJACENT = 2
    ATTACK_VECTOR_LOCAL = 3
    ATTACK_VECTOR_PHYSICAL = 4

  class AuthenticationValueValuesEnum(_messages.Enum):
    r"""Defined in CVSS v2

    Values:
      AUTHENTICATION_UNSPECIFIED: Defined in CVSS v2
      AUTHENTICATION_MULTIPLE: Defined in CVSS v2
      AUTHENTICATION_SINGLE: Defined in CVSS v2
      AUTHENTICATION_NONE: Defined in CVSS v2
    """
    AUTHENTICATION_UNSPECIFIED = 0
    AUTHENTICATION_MULTIPLE = 1
    AUTHENTICATION_SINGLE = 2
    AUTHENTICATION_NONE = 3

  class AvailabilityImpactValueValuesEnum(_messages.Enum):
    r"""Defined in CVSS v3, CVSS v2

    Values:
      IMPACT_UNSPECIFIED: Defined in CVSS v3, CVSS v2
      IMPACT_HIGH: Defined in CVSS v3
      IMPACT_LOW: Defined in CVSS v3
      IMPACT_NONE: Defined in CVSS v3, CVSS v2
      IMPACT_PARTIAL: Defined in CVSS v2
      IMPACT_COMPLETE: Defined in CVSS v2
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3
    IMPACT_PARTIAL = 4
    IMPACT_COMPLETE = 5

  class ConfidentialityImpactValueValuesEnum(_messages.Enum):
    r"""Defined in CVSS v3, CVSS v2

    Values:
      IMPACT_UNSPECIFIED: Defined in CVSS v3, CVSS v2
      IMPACT_HIGH: Defined in CVSS v3
      IMPACT_LOW: Defined in CVSS v3
      IMPACT_NONE: Defined in CVSS v3, CVSS v2
      IMPACT_PARTIAL: Defined in CVSS v2
      IMPACT_COMPLETE: Defined in CVSS v2
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3
    IMPACT_PARTIAL = 4
    IMPACT_COMPLETE = 5

  class IntegrityImpactValueValuesEnum(_messages.Enum):
    r"""Defined in CVSS v3, CVSS v2

    Values:
      IMPACT_UNSPECIFIED: Defined in CVSS v3, CVSS v2
      IMPACT_HIGH: Defined in CVSS v3
      IMPACT_LOW: Defined in CVSS v3
      IMPACT_NONE: Defined in CVSS v3, CVSS v2
      IMPACT_PARTIAL: Defined in CVSS v2
      IMPACT_COMPLETE: Defined in CVSS v2
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3
    IMPACT_PARTIAL = 4
    IMPACT_COMPLETE = 5

  class PrivilegesRequiredValueValuesEnum(_messages.Enum):
    r"""Defined in CVSS v3

    Values:
      PRIVILEGES_REQUIRED_UNSPECIFIED: Defined in CVSS v3
      PRIVILEGES_REQUIRED_NONE: Defined in CVSS v3
      PRIVILEGES_REQUIRED_LOW: Defined in CVSS v3
      PRIVILEGES_REQUIRED_HIGH: Defined in CVSS v3
    """
    PRIVILEGES_REQUIRED_UNSPECIFIED = 0
    PRIVILEGES_REQUIRED_NONE = 1
    PRIVILEGES_REQUIRED_LOW = 2
    PRIVILEGES_REQUIRED_HIGH = 3

  class ScopeValueValuesEnum(_messages.Enum):
    r"""Defined in CVSS v3

    Values:
      SCOPE_UNSPECIFIED: Defined in CVSS v3
      SCOPE_UNCHANGED: Defined in CVSS v3
      SCOPE_CHANGED: Defined in CVSS v3
    """
    SCOPE_UNSPECIFIED = 0
    SCOPE_UNCHANGED = 1
    SCOPE_CHANGED = 2

  class UserInteractionValueValuesEnum(_messages.Enum):
    r"""Defined in CVSS v3

    Values:
      USER_INTERACTION_UNSPECIFIED: Defined in CVSS v3
      USER_INTERACTION_NONE: Defined in CVSS v3
      USER_INTERACTION_REQUIRED: Defined in CVSS v3
    """
    USER_INTERACTION_UNSPECIFIED = 0
    USER_INTERACTION_NONE = 1
    USER_INTERACTION_REQUIRED = 2

  attackComplexity = _messages.EnumField('AttackComplexityValueValuesEnum', 1)
  attackVector = _messages.EnumField('AttackVectorValueValuesEnum', 2)
  authentication = _messages.EnumField('AuthenticationValueValuesEnum', 3)
  availabilityImpact = _messages.EnumField('AvailabilityImpactValueValuesEnum', 4)
  baseScore = _messages.FloatField(5, variant=_messages.Variant.FLOAT)
  confidentialityImpact = _messages.EnumField('ConfidentialityImpactValueValuesEnum', 6)
  exploitabilityScore = _messages.FloatField(7, variant=_messages.Variant.FLOAT)
  impactScore = _messages.FloatField(8, variant=_messages.Variant.FLOAT)
  integrityImpact = _messages.EnumField('IntegrityImpactValueValuesEnum', 9)
  privilegesRequired = _messages.EnumField('PrivilegesRequiredValueValuesEnum', 10)
  scope = _messages.EnumField('ScopeValueValuesEnum', 11)
  userInteraction = _messages.EnumField('UserInteractionValueValuesEnum', 12)


class CVSSv3(_messages.Message):
  r"""Deprecated. Common Vulnerability Scoring System version 3. For details,
  see https://www.first.org/cvss/specification-document

  Enums:
    AttackComplexityValueValuesEnum:
    AttackVectorValueValuesEnum: Base Metrics Represents the intrinsic
      characteristics of a vulnerability that are constant over time and
      across user environments.
    AvailabilityImpactValueValuesEnum:
    ConfidentialityImpactValueValuesEnum:
    IntegrityImpactValueValuesEnum:
    PrivilegesRequiredValueValuesEnum:
    ScopeValueValuesEnum:
    UserInteractionValueValuesEnum:

  Fields:
    attackComplexity: A AttackComplexityValueValuesEnum attribute.
    attackVector: Base Metrics Represents the intrinsic characteristics of a
      vulnerability that are constant over time and across user environments.
    availabilityImpact: A AvailabilityImpactValueValuesEnum attribute.
    baseScore: The base score is a function of the base metric scores.
    confidentialityImpact: A ConfidentialityImpactValueValuesEnum attribute.
    exploitabilityScore: A number attribute.
    impactScore: A number attribute.
    integrityImpact: A IntegrityImpactValueValuesEnum attribute.
    privilegesRequired: A PrivilegesRequiredValueValuesEnum attribute.
    scope: A ScopeValueValuesEnum attribute.
    userInteraction: A UserInteractionValueValuesEnum attribute.
  """

  class AttackComplexityValueValuesEnum(_messages.Enum):
    r"""AttackComplexityValueValuesEnum enum type.

    Values:
      ATTACK_COMPLEXITY_UNSPECIFIED: <no description>
      ATTACK_COMPLEXITY_LOW: <no description>
      ATTACK_COMPLEXITY_HIGH: <no description>
    """
    ATTACK_COMPLEXITY_UNSPECIFIED = 0
    ATTACK_COMPLEXITY_LOW = 1
    ATTACK_COMPLEXITY_HIGH = 2

  class AttackVectorValueValuesEnum(_messages.Enum):
    r"""Base Metrics Represents the intrinsic characteristics of a
    vulnerability that are constant over time and across user environments.

    Values:
      ATTACK_VECTOR_UNSPECIFIED: <no description>
      ATTACK_VECTOR_NETWORK: <no description>
      ATTACK_VECTOR_ADJACENT: <no description>
      ATTACK_VECTOR_LOCAL: <no description>
      ATTACK_VECTOR_PHYSICAL: <no description>
    """
    ATTACK_VECTOR_UNSPECIFIED = 0
    ATTACK_VECTOR_NETWORK = 1
    ATTACK_VECTOR_ADJACENT = 2
    ATTACK_VECTOR_LOCAL = 3
    ATTACK_VECTOR_PHYSICAL = 4

  class AvailabilityImpactValueValuesEnum(_messages.Enum):
    r"""AvailabilityImpactValueValuesEnum enum type.

    Values:
      IMPACT_UNSPECIFIED: <no description>
      IMPACT_HIGH: <no description>
      IMPACT_LOW: <no description>
      IMPACT_NONE: <no description>
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class ConfidentialityImpactValueValuesEnum(_messages.Enum):
    r"""ConfidentialityImpactValueValuesEnum enum type.

    Values:
      IMPACT_UNSPECIFIED: <no description>
      IMPACT_HIGH: <no description>
      IMPACT_LOW: <no description>
      IMPACT_NONE: <no description>
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class IntegrityImpactValueValuesEnum(_messages.Enum):
    r"""IntegrityImpactValueValuesEnum enum type.

    Values:
      IMPACT_UNSPECIFIED: <no description>
      IMPACT_HIGH: <no description>
      IMPACT_LOW: <no description>
      IMPACT_NONE: <no description>
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class PrivilegesRequiredValueValuesEnum(_messages.Enum):
    r"""PrivilegesRequiredValueValuesEnum enum type.

    Values:
      PRIVILEGES_REQUIRED_UNSPECIFIED: <no description>
      PRIVILEGES_REQUIRED_NONE: <no description>
      PRIVILEGES_REQUIRED_LOW: <no description>
      PRIVILEGES_REQUIRED_HIGH: <no description>
    """
    PRIVILEGES_REQUIRED_UNSPECIFIED = 0
    PRIVILEGES_REQUIRED_NONE = 1
    PRIVILEGES_REQUIRED_LOW = 2
    PRIVILEGES_REQUIRED_HIGH = 3

  class ScopeValueValuesEnum(_messages.Enum):
    r"""ScopeValueValuesEnum enum type.

    Values:
      SCOPE_UNSPECIFIED: <no description>
      SCOPE_UNCHANGED: <no description>
      SCOPE_CHANGED: <no description>
    """
    SCOPE_UNSPECIFIED = 0
    SCOPE_UNCHANGED = 1
    SCOPE_CHANGED = 2

  class UserInteractionValueValuesEnum(_messages.Enum):
    r"""UserInteractionValueValuesEnum enum type.

    Values:
      USER_INTERACTION_UNSPECIFIED: <no description>
      USER_INTERACTION_NONE: <no description>
      USER_INTERACTION_REQUIRED: <no description>
    """
    USER_INTERACTION_UNSPECIFIED = 0
    USER_INTERACTION_NONE = 1
    USER_INTERACTION_REQUIRED = 2

  attackComplexity = _messages.EnumField('AttackComplexityValueValuesEnum', 1)
  attackVector = _messages.EnumField('AttackVectorValueValuesEnum', 2)
  availabilityImpact = _messages.EnumField('AvailabilityImpactValueValuesEnum', 3)
  baseScore = _messages.FloatField(4, variant=_messages.Variant.FLOAT)
  confidentialityImpact = _messages.EnumField('ConfidentialityImpactValueValuesEnum', 5)
  exploitabilityScore = _messages.FloatField(6, variant=_messages.Variant.FLOAT)
  impactScore = _messages.FloatField(7, variant=_messages.Variant.FLOAT)
  integrityImpact = _messages.EnumField('IntegrityImpactValueValuesEnum', 8)
  privilegesRequired = _messages.EnumField('PrivilegesRequiredValueValuesEnum', 9)
  scope = _messages.EnumField('ScopeValueValuesEnum', 10)
  userInteraction = _messages.EnumField('UserInteractionValueValuesEnum', 11)


class CloudRepoSourceContext(_messages.Message):
  r"""A CloudRepoSourceContext denotes a particular revision in a Google Cloud
  Source Repo.

  Fields:
    aliasContext: An alias, which may be a branch or tag.
    repoId: The ID of the repo.
    revisionId: A revision ID.
  """

  aliasContext = _messages.MessageField('AliasContext', 1)
  repoId = _messages.MessageField('RepoId', 2)
  revisionId = _messages.StringField(3)


class Command(_messages.Message):
  r"""Command describes a step performed as part of the build pipeline.

  Fields:
    args: Command-line arguments used when executing this command.
    dir: Working directory (relative to project source root) used when running
      this command.
    env: Environment variables set before running this command.
    id: Optional unique identifier for this command, used in wait_for to
      reference this command as a dependency.
    name: Required. Name of the command, as presented on the command line, or
      if the command is packaged as a Docker container, as presented to
      `docker pull`.
    waitFor: The ID(s) of the command(s) that this command depends on.
  """

  args = _messages.StringField(1, repeated=True)
  dir = _messages.StringField(2)
  env = _messages.StringField(3, repeated=True)
  id = _messages.StringField(4)
  name = _messages.StringField(5)
  waitFor = _messages.StringField(6, repeated=True)


class ContaineranalysisGoogleDevtoolsCloudbuildV1ApprovalConfig(_messages.Message):
  r"""ApprovalConfig describes configuration for manual approval of a build.

  Fields:
    approvalRequired: Whether or not approval is needed. If this is set on a
      build, it will become pending when created, and will need to be
      explicitly approved to start.
  """

  approvalRequired = _messages.BooleanField(1)


class ContaineranalysisGoogleDevtoolsCloudbuildV1ApprovalResult(_messages.Message):
  r"""ApprovalResult describes the decision and associated metadata of a
  manual approval of a build.

  Enums:
    DecisionValueValuesEnum: Required. The decision of this manual approval.

  Fields:
    approvalTime: Output only. The time when the approval decision was made.
    approverAccount: Output only. Email of the user that called the
      ApproveBuild API to approve or reject a build at the time that the API
      was called.
    comment: Optional. An optional comment for this manual approval result.
    decision: Required. The decision of this manual approval.
    url: Optional. An optional URL tied to this manual approval result. This
      field is essentially the same as comment, except that it will be
      rendered by the UI differently. An example use case is a link to an
      external job that approved this Build.
  """

  class DecisionValueValuesEnum(_messages.Enum):
    r"""Required. The decision of this manual approval.

    Values:
      DECISION_UNSPECIFIED: Default enum type. This should not be used.
      APPROVED: Build is approved.
      REJECTED: Build is rejected.
    """
    DECISION_UNSPECIFIED = 0
    APPROVED = 1
    REJECTED = 2

  approvalTime = _messages.StringField(1)
  approverAccount = _messages.StringField(2)
  comment = _messages.StringField(3)
  decision = _messages.EnumField('DecisionValueValuesEnum', 4)
  url = _messages.StringField(5)


class ContaineranalysisGoogleDevtoolsCloudbuildV1Artifacts(_messages.Message):
  r"""Artifacts produced by a build that should be uploaded upon successful
  completion of all build steps.

  Fields:
    images: A list of images to be pushed upon the successful completion of
      all build steps. The images will be pushed using the builder service
      account's credentials. The digests of the pushed images will be stored
      in the Build resource's results field. If any of the images fail to be
      pushed, the build is marked FAILURE.
    mavenArtifacts: A list of Maven artifacts to be uploaded to Artifact
      Registry upon successful completion of all build steps. Artifacts in the
      workspace matching specified paths globs will be uploaded to the
      specified Artifact Registry repository using the builder service
      account's credentials. If any artifacts fail to be pushed, the build is
      marked FAILURE.
    npmPackages: A list of npm packages to be uploaded to Artifact Registry
      upon successful completion of all build steps. Npm packages in the
      specified paths will be uploaded to the specified Artifact Registry
      repository using the builder service account's credentials. If any
      packages fail to be pushed, the build is marked FAILURE.
    objects: A list of objects to be uploaded to Cloud Storage upon successful
      completion of all build steps. Files in the workspace matching specified
      paths globs will be uploaded to the specified Cloud Storage location
      using the builder service account's credentials. The location and
      generation of the uploaded objects will be stored in the Build
      resource's results field. If any objects fail to be pushed, the build is
      marked FAILURE.
    pythonPackages: A list of Python packages to be uploaded to Artifact
      Registry upon successful completion of all build steps. The build
      service account credentials will be used to perform the upload. If any
      objects fail to be pushed, the build is marked FAILURE.
  """

  images = _messages.StringField(1, repeated=True)
  mavenArtifacts = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsMavenArtifact', 2, repeated=True)
  npmPackages = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsNpmPackage', 3, repeated=True)
  objects = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsArtifactObjects', 4)
  pythonPackages = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsPythonPackage', 5, repeated=True)


class ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsArtifactObjects(_messages.Message):
  r"""Files in the workspace to upload to Cloud Storage upon successful
  completion of all build steps.

  Fields:
    location: Cloud Storage bucket and optional object path, in the form
      "gs://bucket/path/to/somewhere/". (see [Bucket Name
      Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)). Files in the workspace matching any path pattern
      will be uploaded to Cloud Storage with this location as a prefix.
    paths: Path globs used to match files in the build's workspace.
    timing: Output only. Stores timing information for pushing all artifact
      objects.
  """

  location = _messages.StringField(1)
  paths = _messages.StringField(2, repeated=True)
  timing = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsMavenArtifact(_messages.Message):
  r"""A Maven artifact to upload to Artifact Registry upon successful
  completion of all build steps.

  Fields:
    artifactId: Maven `artifactId` value used when uploading the artifact to
      Artifact Registry.
    groupId: Maven `groupId` value used when uploading the artifact to
      Artifact Registry.
    path: Path to an artifact in the build's workspace to be uploaded to
      Artifact Registry. This can be either an absolute path, e.g.
      /workspace/my-app/target/my-app-1.0.SNAPSHOT.jar or a relative path from
      /workspace, e.g. my-app/target/my-app-1.0.SNAPSHOT.jar.
    repository: Artifact Registry repository, in the form "https://$REGION-
      maven.pkg.dev/$PROJECT/$REPOSITORY" Artifact in the workspace specified
      by path will be uploaded to Artifact Registry with this location as a
      prefix.
    version: Maven `version` value used when uploading the artifact to
      Artifact Registry.
  """

  artifactId = _messages.StringField(1)
  groupId = _messages.StringField(2)
  path = _messages.StringField(3)
  repository = _messages.StringField(4)
  version = _messages.StringField(5)


class ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsNpmPackage(_messages.Message):
  r"""Npm package to upload to Artifact Registry upon successful completion of
  all build steps.

  Fields:
    packagePath: Path to the package.json. e.g. workspace/path/to/package
    repository: Artifact Registry repository, in the form "https://$REGION-
      npm.pkg.dev/$PROJECT/$REPOSITORY" Npm package in the workspace specified
      by path will be zipped and uploaded to Artifact Registry with this
      location as a prefix.
  """

  packagePath = _messages.StringField(1)
  repository = _messages.StringField(2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1ArtifactsPythonPackage(_messages.Message):
  r"""Python package to upload to Artifact Registry upon successful completion
  of all build steps. A package can encapsulate multiple objects to be
  uploaded to a single repository.

  Fields:
    paths: Path globs used to match files in the build's workspace. For
      Python/ Twine, this is usually `dist/*`, and sometimes additionally an
      `.asc` file.
    repository: Artifact Registry repository, in the form "https://$REGION-
      python.pkg.dev/$PROJECT/$REPOSITORY" Files in the workspace matching any
      path pattern will be uploaded to Artifact Registry with this location as
      a prefix.
  """

  paths = _messages.StringField(1, repeated=True)
  repository = _messages.StringField(2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1Build(_messages.Message):
  r"""A build resource in the Cloud Build API. At a high level, a `Build`
  describes where to find source code, how to build it (for example, the
  builder image to run on the source), and where to store the built artifacts.
  Fields can include the following variables, which will be expanded when the
  build is created: - $PROJECT_ID: the project ID of the build. -
  $PROJECT_NUMBER: the project number of the build. - $LOCATION: the
  location/region of the build. - $BUILD_ID: the autogenerated ID of the
  build. - $REPO_NAME: the source repository name specified by RepoSource. -
  $BRANCH_NAME: the branch name specified by RepoSource. - $TAG_NAME: the tag
  name specified by RepoSource. - $REVISION_ID or $COMMIT_SHA: the commit SHA
  specified by RepoSource or resolved from the specified branch or tag. -
  $SHORT_SHA: first 7 characters of $REVISION_ID or $COMMIT_SHA.

  Enums:
    StatusValueValuesEnum: Output only. Status of the build.

  Messages:
    SubstitutionsValue: Substitutions data for `Build` resource.
    TimingValue: Output only. Stores timing information for phases of the
      build. Valid keys are: * BUILD: time to execute all build steps. * PUSH:
      time to push all artifacts including docker images and non docker
      artifacts. * FETCHSOURCE: time to fetch source. * SETUPBUILD: time to
      set up build. If the build does not specify source or images, these keys
      will not be included.

  Fields:
    approval: Output only. Describes this build's approval configuration,
      status, and result.
    artifacts: Artifacts produced by the build that should be uploaded upon
      successful completion of all build steps.
    availableSecrets: Secrets and secret environment variables.
    buildTriggerId: Output only. The ID of the `BuildTrigger` that triggered
      this build, if it was triggered automatically.
    createTime: Output only. Time at which the request to create the build was
      received.
    failureInfo: Output only. Contains information about the build when
      status=FAILURE.
    finishTime: Output only. Time at which execution of the build was
      finished. The difference between finish_time and start_time is the
      duration of the build's execution.
    id: Output only. Unique identifier of the build.
    images: A list of images to be pushed upon the successful completion of
      all build steps. The images are pushed using the builder service
      account's credentials. The digests of the pushed images will be stored
      in the `Build` resource's results field. If any of the images fail to be
      pushed, the build status is marked `FAILURE`.
    logUrl: Output only. URL to logs for this build in Google Cloud Console.
    logsBucket: Cloud Storage bucket where logs should be written (see [Bucket
      Name Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)). Logs file names will be of the format
      `${logs_bucket}/log-${build_id}.txt`.
    name: Output only. The 'Build' name with format:
      `projects/{project}/locations/{location}/builds/{build}`, where {build}
      is a unique identifier generated by the service.
    options: Special options for this build.
    projectId: Output only. ID of the project.
    queueTtl: TTL in queue for this build. If provided and the build is
      enqueued longer than this value, the build will expire and the build
      status will be `EXPIRED`. The TTL starts ticking from create_time.
    results: Output only. Results of the build.
    secrets: Secrets to decrypt using Cloud Key Management Service. Note:
      Secret Manager is the recommended technique for managing sensitive data
      with Cloud Build. Use `available_secrets` to configure builds to access
      secrets from Secret Manager. For instructions, see:
      https://cloud.google.com/cloud-build/docs/securing-builds/use-secrets
    serviceAccount: IAM service account whose credentials will be used at
      build runtime. Must be of the format
      `projects/{PROJECT_ID}/serviceAccounts/{ACCOUNT}`. ACCOUNT can be email
      address or uniqueId of the service account.
    source: The location of the source files to build.
    sourceProvenance: Output only. A permanent fixed identifier for source.
    startTime: Output only. Time at which execution of the build was started.
    status: Output only. Status of the build.
    statusDetail: Output only. Customer-readable message about the current
      status.
    steps: Required. The operations to be performed on the workspace.
    substitutions: Substitutions data for `Build` resource.
    tags: Tags for annotation of a `Build`. These are not docker tags.
    timeout: Amount of time that this build should be allowed to run, to
      second granularity. If this amount of time elapses, work on the build
      will cease and the build status will be `TIMEOUT`. `timeout` starts
      ticking from `startTime`. Default time is 60 minutes.
    timing: Output only. Stores timing information for phases of the build.
      Valid keys are: * BUILD: time to execute all build steps. * PUSH: time
      to push all artifacts including docker images and non docker artifacts.
      * FETCHSOURCE: time to fetch source. * SETUPBUILD: time to set up build.
      If the build does not specify source or images, these keys will not be
      included.
    warnings: Output only. Non-fatal problems encountered during the execution
      of the build.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. Status of the build.

    Values:
      STATUS_UNKNOWN: Status of the build is unknown.
      PENDING: Build has been created and is pending execution and queuing. It
        has not been queued.
      QUEUED: Build or step is queued; work has not yet begun.
      WORKING: Build or step is being executed.
      SUCCESS: Build or step finished successfully.
      FAILURE: Build or step failed to complete successfully.
      INTERNAL_ERROR: Build or step failed due to an internal cause.
      TIMEOUT: Build or step took longer than was allowed.
      CANCELLED: Build or step was canceled by a user.
      EXPIRED: Build was enqueued for longer than the value of `queue_ttl`.
    """
    STATUS_UNKNOWN = 0
    PENDING = 1
    QUEUED = 2
    WORKING = 3
    SUCCESS = 4
    FAILURE = 5
    INTERNAL_ERROR = 6
    TIMEOUT = 7
    CANCELLED = 8
    EXPIRED = 9

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SubstitutionsValue(_messages.Message):
    r"""Substitutions data for `Build` resource.

    Messages:
      AdditionalProperty: An additional property for a SubstitutionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type SubstitutionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SubstitutionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TimingValue(_messages.Message):
    r"""Output only. Stores timing information for phases of the build. Valid
    keys are: * BUILD: time to execute all build steps. * PUSH: time to push
    all artifacts including docker images and non docker artifacts. *
    FETCHSOURCE: time to fetch source. * SETUPBUILD: time to set up build. If
    the build does not specify source or images, these keys will not be
    included.

    Messages:
      AdditionalProperty: An additional property for a TimingValue object.

    Fields:
      additionalProperties: Additional properties of type TimingValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TimingValue object.

      Fields:
        key: Name of the additional property.
        value: A ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  approval = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1BuildApproval', 1)
  artifacts = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1Artifacts', 2)
  availableSecrets = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1Secrets', 3)
  buildTriggerId = _messages.StringField(4)
  createTime = _messages.StringField(5)
  failureInfo = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1BuildFailureInfo', 6)
  finishTime = _messages.StringField(7)
  id = _messages.StringField(8)
  images = _messages.StringField(9, repeated=True)
  logUrl = _messages.StringField(10)
  logsBucket = _messages.StringField(11)
  name = _messages.StringField(12)
  options = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1BuildOptions', 13)
  projectId = _messages.StringField(14)
  queueTtl = _messages.StringField(15)
  results = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1Results', 16)
  secrets = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1Secret', 17, repeated=True)
  serviceAccount = _messages.StringField(18)
  source = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1Source', 19)
  sourceProvenance = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1SourceProvenance', 20)
  startTime = _messages.StringField(21)
  status = _messages.EnumField('StatusValueValuesEnum', 22)
  statusDetail = _messages.StringField(23)
  steps = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1BuildStep', 24, repeated=True)
  substitutions = _messages.MessageField('SubstitutionsValue', 25)
  tags = _messages.StringField(26, repeated=True)
  timeout = _messages.StringField(27)
  timing = _messages.MessageField('TimingValue', 28)
  warnings = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1BuildWarning', 29, repeated=True)


class ContaineranalysisGoogleDevtoolsCloudbuildV1BuildApproval(_messages.Message):
  r"""BuildApproval describes a build's approval configuration, state, and
  result.

  Enums:
    StateValueValuesEnum: Output only. The state of this build's approval.

  Fields:
    config: Output only. Configuration for manual approval of this build.
    result: Output only. Result of manual approval for this Build.
    state: Output only. The state of this build's approval.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of this build's approval.

    Values:
      STATE_UNSPECIFIED: Default enum type. This should not be used.
      PENDING: Build approval is pending.
      APPROVED: Build approval has been approved.
      REJECTED: Build approval has been rejected.
      CANCELLED: Build was cancelled while it was still pending approval.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    APPROVED = 2
    REJECTED = 3
    CANCELLED = 4

  config = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1ApprovalConfig', 1)
  result = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1ApprovalResult', 2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1BuildFailureInfo(_messages.Message):
  r"""A fatal problem encountered during the execution of the build.

  Enums:
    TypeValueValuesEnum: The name of the failure.

  Fields:
    detail: Explains the failure issue in more detail using hard-coded text.
    type: The name of the failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The name of the failure.

    Values:
      FAILURE_TYPE_UNSPECIFIED: Type unspecified
      PUSH_FAILED: Unable to push the image to the repository.
      PUSH_IMAGE_NOT_FOUND: Final image not found.
      PUSH_NOT_AUTHORIZED: Unauthorized push of the final image.
      LOGGING_FAILURE: Backend logging failures. Should retry.
      USER_BUILD_STEP: A build step has failed.
      FETCH_SOURCE_FAILED: The source fetching has failed.
    """
    FAILURE_TYPE_UNSPECIFIED = 0
    PUSH_FAILED = 1
    PUSH_IMAGE_NOT_FOUND = 2
    PUSH_NOT_AUTHORIZED = 3
    LOGGING_FAILURE = 4
    USER_BUILD_STEP = 5
    FETCH_SOURCE_FAILED = 6

  detail = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1BuildOptions(_messages.Message):
  r"""Optional arguments to enable specific features of builds.

  Enums:
    DefaultLogsBucketBehaviorValueValuesEnum: Optional. Option to specify how
      default logs buckets are setup.
    LogStreamingOptionValueValuesEnum: Option to define build log streaming
      behavior to Cloud Storage.
    LoggingValueValuesEnum: Option to specify the logging mode, which
      determines if and where build logs are stored.
    MachineTypeValueValuesEnum: Compute Engine machine type on which to run
      the build.
    RequestedVerifyOptionValueValuesEnum: Requested verifiability options.
    SourceProvenanceHashValueListEntryValuesEnum:
    SubstitutionOptionValueValuesEnum: Option to specify behavior when there
      is an error in the substitution checks. NOTE: this is always set to
      ALLOW_LOOSE for triggered builds and cannot be overridden in the build
      configuration file.

  Fields:
    automapSubstitutions: Option to include built-in and custom substitutions
      as env variables for all build steps.
    defaultLogsBucketBehavior: Optional. Option to specify how default logs
      buckets are setup.
    diskSizeGb: Requested disk size for the VM that runs the build. Note that
      this is *NOT* "disk free"; some of the space will be used by the
      operating system and build utilities. Also note that this is the minimum
      disk size that will be allocated for the build -- the build may run with
      a larger disk than requested. At present, the maximum disk size is
      2000GB; builds that request more than the maximum are rejected with an
      error.
    dynamicSubstitutions: Option to specify whether or not to apply bash style
      string operations to the substitutions. NOTE: this is always enabled for
      triggered builds and cannot be overridden in the build configuration
      file.
    env: A list of global environment variable definitions that will exist for
      all build steps in this build. If a variable is defined in both globally
      and in a build step, the variable will use the build step value. The
      elements are of the form "KEY=VALUE" for the environment variable "KEY"
      being given the value "VALUE".
    logStreamingOption: Option to define build log streaming behavior to Cloud
      Storage.
    logging: Option to specify the logging mode, which determines if and where
      build logs are stored.
    machineType: Compute Engine machine type on which to run the build.
    pool: Optional. Specification for execution on a `WorkerPool`. See
      [running builds in a private
      pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-
      private-pool) for more information.
    requestedVerifyOption: Requested verifiability options.
    secretEnv: A list of global environment variables, which are encrypted
      using a Cloud Key Management Service crypto key. These values must be
      specified in the build's `Secret`. These variables will be available to
      all build steps in this build.
    sourceProvenanceHash: Requested hash for SourceProvenance.
    substitutionOption: Option to specify behavior when there is an error in
      the substitution checks. NOTE: this is always set to ALLOW_LOOSE for
      triggered builds and cannot be overridden in the build configuration
      file.
    volumes: Global list of volumes to mount for ALL build steps Each volume
      is created as an empty volume prior to starting the build process. Upon
      completion of the build, volumes and their contents are discarded.
      Global volume names and paths cannot conflict with the volumes defined a
      build step. Using a global volume in a build with only one step is not
      valid as it is indicative of a build request with an incorrect
      configuration.
    workerPool: This field deprecated; please use `pool.name` instead.
  """

  class DefaultLogsBucketBehaviorValueValuesEnum(_messages.Enum):
    r"""Optional. Option to specify how default logs buckets are setup.

    Values:
      DEFAULT_LOGS_BUCKET_BEHAVIOR_UNSPECIFIED: Unspecified.
      REGIONAL_USER_OWNED_BUCKET: Bucket is located in user-owned project in
        the same region as the build. The builder service account must have
        access to create and write to Cloud Storage buckets in the build
        project.
    """
    DEFAULT_LOGS_BUCKET_BEHAVIOR_UNSPECIFIED = 0
    REGIONAL_USER_OWNED_BUCKET = 1

  class LogStreamingOptionValueValuesEnum(_messages.Enum):
    r"""Option to define build log streaming behavior to Cloud Storage.

    Values:
      STREAM_DEFAULT: Service may automatically determine build log streaming
        behavior.
      STREAM_ON: Build logs should be streamed to Cloud Storage.
      STREAM_OFF: Build logs should not be streamed to Cloud Storage; they
        will be written when the build is completed.
    """
    STREAM_DEFAULT = 0
    STREAM_ON = 1
    STREAM_OFF = 2

  class LoggingValueValuesEnum(_messages.Enum):
    r"""Option to specify the logging mode, which determines if and where
    build logs are stored.

    Values:
      LOGGING_UNSPECIFIED: The service determines the logging mode. The
        default is `LEGACY`. Do not rely on the default logging behavior as it
        may change in the future.
      LEGACY: Build logs are stored in Cloud Logging and Cloud Storage.
      GCS_ONLY: Build logs are stored in Cloud Storage.
      STACKDRIVER_ONLY: This option is the same as CLOUD_LOGGING_ONLY.
      CLOUD_LOGGING_ONLY: Build logs are stored in Cloud Logging. Selecting
        this option will not allow [logs
        streaming](https://cloud.google.com/sdk/gcloud/reference/builds/log).
      NONE: Turn off all logging. No build logs will be captured.
    """
    LOGGING_UNSPECIFIED = 0
    LEGACY = 1
    GCS_ONLY = 2
    STACKDRIVER_ONLY = 3
    CLOUD_LOGGING_ONLY = 4
    NONE = 5

  class MachineTypeValueValuesEnum(_messages.Enum):
    r"""Compute Engine machine type on which to run the build.

    Values:
      UNSPECIFIED: Standard machine type.
      N1_HIGHCPU_8: Highcpu machine with 8 CPUs.
      N1_HIGHCPU_32: Highcpu machine with 32 CPUs.
      E2_HIGHCPU_8: Highcpu e2 machine with 8 CPUs.
      E2_HIGHCPU_32: Highcpu e2 machine with 32 CPUs.
      E2_MEDIUM: E2 machine with 1 CPU.
    """
    UNSPECIFIED = 0
    N1_HIGHCPU_8 = 1
    N1_HIGHCPU_32 = 2
    E2_HIGHCPU_8 = 3
    E2_HIGHCPU_32 = 4
    E2_MEDIUM = 5

  class RequestedVerifyOptionValueValuesEnum(_messages.Enum):
    r"""Requested verifiability options.

    Values:
      NOT_VERIFIED: Not a verifiable build (the default).
      VERIFIED: Build must be verified.
    """
    NOT_VERIFIED = 0
    VERIFIED = 1

  class SourceProvenanceHashValueListEntryValuesEnum(_messages.Enum):
    r"""SourceProvenanceHashValueListEntryValuesEnum enum type.

    Values:
      NONE: No hash requested.
      SHA256: Use a sha256 hash.
      MD5: Use a md5 hash.
      SHA512: Use a sha512 hash.
    """
    NONE = 0
    SHA256 = 1
    MD5 = 2
    SHA512 = 3

  class SubstitutionOptionValueValuesEnum(_messages.Enum):
    r"""Option to specify behavior when there is an error in the substitution
    checks. NOTE: this is always set to ALLOW_LOOSE for triggered builds and
    cannot be overridden in the build configuration file.

    Values:
      MUST_MATCH: Fails the build if error in substitutions checks, like
        missing a substitution in the template or in the map.
      ALLOW_LOOSE: Do not fail the build if error in substitutions checks.
    """
    MUST_MATCH = 0
    ALLOW_LOOSE = 1

  automapSubstitutions = _messages.BooleanField(1)
  defaultLogsBucketBehavior = _messages.EnumField('DefaultLogsBucketBehaviorValueValuesEnum', 2)
  diskSizeGb = _messages.IntegerField(3)
  dynamicSubstitutions = _messages.BooleanField(4)
  env = _messages.StringField(5, repeated=True)
  logStreamingOption = _messages.EnumField('LogStreamingOptionValueValuesEnum', 6)
  logging = _messages.EnumField('LoggingValueValuesEnum', 7)
  machineType = _messages.EnumField('MachineTypeValueValuesEnum', 8)
  pool = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1BuildOptionsPoolOption', 9)
  requestedVerifyOption = _messages.EnumField('RequestedVerifyOptionValueValuesEnum', 10)
  secretEnv = _messages.StringField(11, repeated=True)
  sourceProvenanceHash = _messages.EnumField('SourceProvenanceHashValueListEntryValuesEnum', 12, repeated=True)
  substitutionOption = _messages.EnumField('SubstitutionOptionValueValuesEnum', 13)
  volumes = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1Volume', 14, repeated=True)
  workerPool = _messages.StringField(15)


class ContaineranalysisGoogleDevtoolsCloudbuildV1BuildOptionsPoolOption(_messages.Message):
  r"""Details about how a build should be executed on a `WorkerPool`. See
  [running builds in a private
  pool](https://cloud.google.com/build/docs/private-pools/run-builds-in-
  private-pool) for more information.

  Fields:
    name: The `WorkerPool` resource to execute the build on. You must have
      `cloudbuild.workerpools.use` on the project hosting the WorkerPool.
      Format
      projects/{project}/locations/{location}/workerPools/{workerPoolId}
  """

  name = _messages.StringField(1)


class ContaineranalysisGoogleDevtoolsCloudbuildV1BuildStep(_messages.Message):
  r"""A step in the build pipeline.

  Enums:
    StatusValueValuesEnum: Output only. Status of the build step. At this
      time, build step status is only updated on build completion; step status
      is not updated in real-time as the build progresses.

  Fields:
    allowExitCodes: Allow this build step to fail without failing the entire
      build if and only if the exit code is one of the specified codes. If
      allow_failure is also specified, this field will take precedence.
    allowFailure: Allow this build step to fail without failing the entire
      build. If false, the entire build will fail if this step fails.
      Otherwise, the build will succeed, but this step will still have a
      failure status. Error information will be reported in the failure_detail
      field.
    args: A list of arguments that will be presented to the step when it is
      started. If the image used to run the step's container has an
      entrypoint, the `args` are used as arguments to that entrypoint. If the
      image does not define an entrypoint, the first element in args is used
      as the entrypoint, and the remainder will be used as arguments.
    automapSubstitutions: Option to include built-in and custom substitutions
      as env variables for this build step. This option will override the
      global option in BuildOption.
    dir: Working directory to use when running this step's container. If this
      value is a relative path, it is relative to the build's working
      directory. If this value is absolute, it may be outside the build's
      working directory, in which case the contents of the path may not be
      persisted across build step executions, unless a `volume` for that path
      is specified. If the build specifies a `RepoSource` with `dir` and a
      step with a `dir`, which specifies an absolute path, the `RepoSource`
      `dir` is ignored for the step's execution.
    entrypoint: Entrypoint to be used instead of the build step image's
      default entrypoint. If unset, the image's default entrypoint is used.
    env: A list of environment variable definitions to be used when running a
      step. The elements are of the form "KEY=VALUE" for the environment
      variable "KEY" being given the value "VALUE".
    exitCode: Output only. Return code from running the step.
    id: Unique identifier for this build step, used in `wait_for` to reference
      this build step as a dependency.
    name: Required. The name of the container image that will run this
      particular build step. If the image is available in the host's Docker
      daemon's cache, it will be run directly. If not, the host will attempt
      to pull the image first, using the builder service account's credentials
      if necessary. The Docker daemon's cache will already have the latest
      versions of all of the officially supported build steps
      ([https://github.com/GoogleCloudPlatform/cloud-
      builders](https://github.com/GoogleCloudPlatform/cloud-builders)). The
      Docker daemon will also have cached many of the layers for some popular
      images, like "ubuntu", "debian", but they will be refreshed at the time
      you attempt to use them. If you built an image in a previous build step,
      it will be stored in the host's Docker daemon's cache and is available
      to use as the name for a later build step.
    pullTiming: Output only. Stores timing information for pulling this build
      step's builder image only.
    script: A shell script to be executed in the step. When script is
      provided, the user cannot specify the entrypoint or args.
    secretEnv: A list of environment variables which are encrypted using a
      Cloud Key Management Service crypto key. These values must be specified
      in the build's `Secret`.
    status: Output only. Status of the build step. At this time, build step
      status is only updated on build completion; step status is not updated
      in real-time as the build progresses.
    timeout: Time limit for executing this build step. If not defined, the
      step has no time limit and will be allowed to continue to run until
      either it completes or the build itself times out.
    timing: Output only. Stores timing information for executing this build
      step.
    volumes: List of volumes to mount into the build step. Each volume is
      created as an empty volume prior to execution of the build step. Upon
      completion of the build, volumes and their contents are discarded. Using
      a named volume in only one step is not valid as it is indicative of a
      build request with an incorrect configuration.
    waitFor: The ID(s) of the step(s) that this build step depends on. This
      build step will not start until all the build steps in `wait_for` have
      completed successfully. If `wait_for` is empty, this build step will
      start when all previous build steps in the `Build.Steps` list have
      completed successfully.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. Status of the build step. At this time, build step status
    is only updated on build completion; step status is not updated in real-
    time as the build progresses.

    Values:
      STATUS_UNKNOWN: Status of the build is unknown.
      PENDING: Build has been created and is pending execution and queuing. It
        has not been queued.
      QUEUED: Build or step is queued; work has not yet begun.
      WORKING: Build or step is being executed.
      SUCCESS: Build or step finished successfully.
      FAILURE: Build or step failed to complete successfully.
      INTERNAL_ERROR: Build or step failed due to an internal cause.
      TIMEOUT: Build or step took longer than was allowed.
      CANCELLED: Build or step was canceled by a user.
      EXPIRED: Build was enqueued for longer than the value of `queue_ttl`.
    """
    STATUS_UNKNOWN = 0
    PENDING = 1
    QUEUED = 2
    WORKING = 3
    SUCCESS = 4
    FAILURE = 5
    INTERNAL_ERROR = 6
    TIMEOUT = 7
    CANCELLED = 8
    EXPIRED = 9

  allowExitCodes = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)
  allowFailure = _messages.BooleanField(2)
  args = _messages.StringField(3, repeated=True)
  automapSubstitutions = _messages.BooleanField(4)
  dir = _messages.StringField(5)
  entrypoint = _messages.StringField(6)
  env = _messages.StringField(7, repeated=True)
  exitCode = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  id = _messages.StringField(9)
  name = _messages.StringField(10)
  pullTiming = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 11)
  script = _messages.StringField(12)
  secretEnv = _messages.StringField(13, repeated=True)
  status = _messages.EnumField('StatusValueValuesEnum', 14)
  timeout = _messages.StringField(15)
  timing = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 16)
  volumes = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1Volume', 17, repeated=True)
  waitFor = _messages.StringField(18, repeated=True)


class ContaineranalysisGoogleDevtoolsCloudbuildV1BuildWarning(_messages.Message):
  r"""A non-fatal problem encountered during the execution of the build.

  Enums:
    PriorityValueValuesEnum: The priority for this warning.

  Fields:
    priority: The priority for this warning.
    text: Explanation of the warning generated.
  """

  class PriorityValueValuesEnum(_messages.Enum):
    r"""The priority for this warning.

    Values:
      PRIORITY_UNSPECIFIED: Should not be used.
      INFO: e.g. deprecation warnings and alternative feature highlights.
      WARNING: e.g. automated detection of possible issues with the build.
      ALERT: e.g. alerts that a feature used in the build is pending removal
    """
    PRIORITY_UNSPECIFIED = 0
    INFO = 1
    WARNING = 2
    ALERT = 3

  priority = _messages.EnumField('PriorityValueValuesEnum', 1)
  text = _messages.StringField(2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1BuiltImage(_messages.Message):
  r"""An image built by the pipeline.

  Fields:
    digest: Docker Registry 2.0 digest.
    name: Name used to push the container image to Google Container Registry,
      as presented to `docker push`.
    pushTiming: Output only. Stores timing information for pushing the
      specified image.
  """

  digest = _messages.StringField(1)
  name = _messages.StringField(2)
  pushTiming = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes(_messages.Message):
  r"""Container message for hashes of byte content of files, used in
  SourceProvenance messages to verify integrity of source input to the build.

  Fields:
    fileHash: Collection of file hashes.
  """

  fileHash = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1Hash', 1, repeated=True)


class ContaineranalysisGoogleDevtoolsCloudbuildV1GitSource(_messages.Message):
  r"""Location of the source in any accessible Git repository.

  Fields:
    dir: Directory, relative to the source root, in which to run the build.
      This must be a relative path. If a step's `dir` is specified and is an
      absolute path, this value is ignored for that step's execution.
    revision: The revision to fetch from the Git repository such as a branch,
      a tag, a commit SHA, or any Git ref. Cloud Build uses `git fetch` to
      fetch the revision from the Git repository; therefore make sure that the
      string you provide for `revision` is parsable by the command. For
      information on string values accepted by `git fetch`, see https://git-
      scm.com/docs/gitrevisions#_specifying_revisions. For information on `git
      fetch`, see https://git-scm.com/docs/git-fetch.
    url: Location of the Git repo to build. This will be used as a `git
      remote`, see https://git-scm.com/docs/git-remote.
  """

  dir = _messages.StringField(1)
  revision = _messages.StringField(2)
  url = _messages.StringField(3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1Hash(_messages.Message):
  r"""Container message for hash values.

  Enums:
    TypeValueValuesEnum: The type of hash that was performed.

  Fields:
    type: The type of hash that was performed.
    value: The hash value.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of hash that was performed.

    Values:
      NONE: No hash requested.
      SHA256: Use a sha256 hash.
      MD5: Use a md5 hash.
      SHA512: Use a sha512 hash.
    """
    NONE = 0
    SHA256 = 1
    MD5 = 2
    SHA512 = 3

  type = _messages.EnumField('TypeValueValuesEnum', 1)
  value = _messages.BytesField(2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1InlineSecret(_messages.Message):
  r"""Pairs a set of secret environment variables mapped to encrypted values
  with the Cloud KMS key to use to decrypt the value.

  Messages:
    EnvMapValue: Map of environment variable name to its encrypted value.
      Secret environment variables must be unique across all of a build's
      secrets, and must be used by at least one build step. Values can be at
      most 64 KB in size. There can be at most 100 secret values across all of
      a build's secrets.

  Fields:
    envMap: Map of environment variable name to its encrypted value. Secret
      environment variables must be unique across all of a build's secrets,
      and must be used by at least one build step. Values can be at most 64 KB
      in size. There can be at most 100 secret values across all of a build's
      secrets.
    kmsKeyName: Resource name of Cloud KMS crypto key to decrypt the encrypted
      value. In format: projects/*/locations/*/keyRings/*/cryptoKeys/*
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvMapValue(_messages.Message):
    r"""Map of environment variable name to its encrypted value. Secret
    environment variables must be unique across all of a build's secrets, and
    must be used by at least one build step. Values can be at most 64 KB in
    size. There can be at most 100 secret values across all of a build's
    secrets.

    Messages:
      AdditionalProperty: An additional property for a EnvMapValue object.

    Fields:
      additionalProperties: Additional properties of type EnvMapValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvMapValue object.

      Fields:
        key: Name of the additional property.
        value: A byte attribute.
      """

      key = _messages.StringField(1)
      value = _messages.BytesField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  envMap = _messages.MessageField('EnvMapValue', 1)
  kmsKeyName = _messages.StringField(2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1RepoSource(_messages.Message):
  r"""Location of the source in a Google Cloud Source Repository.

  Messages:
    SubstitutionsValue: Substitutions to use in a triggered build. Should only
      be used with RunBuildTrigger

  Fields:
    branchName: Regex matching branches to build. The syntax of the regular
      expressions accepted is the syntax accepted by RE2 and described at
      https://github.com/google/re2/wiki/Syntax
    commitSha: Explicit commit SHA to build.
    dir: Directory, relative to the source root, in which to run the build.
      This must be a relative path. If a step's `dir` is specified and is an
      absolute path, this value is ignored for that step's execution.
    invertRegex: Only trigger a build if the revision regex does NOT match the
      revision regex.
    projectId: ID of the project that owns the Cloud Source Repository. If
      omitted, the project ID requesting the build is assumed.
    repoName: Name of the Cloud Source Repository.
    substitutions: Substitutions to use in a triggered build. Should only be
      used with RunBuildTrigger
    tagName: Regex matching tags to build. The syntax of the regular
      expressions accepted is the syntax accepted by RE2 and described at
      https://github.com/google/re2/wiki/Syntax
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SubstitutionsValue(_messages.Message):
    r"""Substitutions to use in a triggered build. Should only be used with
    RunBuildTrigger

    Messages:
      AdditionalProperty: An additional property for a SubstitutionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type SubstitutionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SubstitutionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  branchName = _messages.StringField(1)
  commitSha = _messages.StringField(2)
  dir = _messages.StringField(3)
  invertRegex = _messages.BooleanField(4)
  projectId = _messages.StringField(5)
  repoName = _messages.StringField(6)
  substitutions = _messages.MessageField('SubstitutionsValue', 7)
  tagName = _messages.StringField(8)


class ContaineranalysisGoogleDevtoolsCloudbuildV1Results(_messages.Message):
  r"""Artifacts created by the build pipeline.

  Fields:
    artifactManifest: Path to the artifact manifest for non-container
      artifacts uploaded to Cloud Storage. Only populated when artifacts are
      uploaded to Cloud Storage.
    artifactTiming: Time to push all non-container artifacts to Cloud Storage.
    buildStepImages: List of build step digests, in the order corresponding to
      build step indices.
    buildStepOutputs: List of build step outputs, produced by builder images,
      in the order corresponding to build step indices. [Cloud
      Builders](https://cloud.google.com/cloud-build/docs/cloud-builders) can
      produce this output by writing to `$BUILDER_OUTPUT/output`. Only the
      first 4KB of data is stored.
    images: Container images that were built as a part of the build.
    mavenArtifacts: Maven artifacts uploaded to Artifact Registry at the end
      of the build.
    npmPackages: Npm packages uploaded to Artifact Registry at the end of the
      build.
    numArtifacts: Number of non-container artifacts uploaded to Cloud Storage.
      Only populated when artifacts are uploaded to Cloud Storage.
    pythonPackages: Python artifacts uploaded to Artifact Registry at the end
      of the build.
  """

  artifactManifest = _messages.StringField(1)
  artifactTiming = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 2)
  buildStepImages = _messages.StringField(3, repeated=True)
  buildStepOutputs = _messages.BytesField(4, repeated=True)
  images = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1BuiltImage', 5, repeated=True)
  mavenArtifacts = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedMavenArtifact', 6, repeated=True)
  npmPackages = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedNpmPackage', 7, repeated=True)
  numArtifacts = _messages.IntegerField(8)
  pythonPackages = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedPythonPackage', 9, repeated=True)


class ContaineranalysisGoogleDevtoolsCloudbuildV1Secret(_messages.Message):
  r"""Pairs a set of secret environment variables containing encrypted values
  with the Cloud KMS key to use to decrypt the value. Note: Use `kmsKeyName`
  with `available_secrets` instead of using `kmsKeyName` with `secret`. For
  instructions see: https://cloud.google.com/cloud-build/docs/securing-
  builds/use-encrypted-credentials.

  Messages:
    SecretEnvValue: Map of environment variable name to its encrypted value.
      Secret environment variables must be unique across all of a build's
      secrets, and must be used by at least one build step. Values can be at
      most 64 KB in size. There can be at most 100 secret values across all of
      a build's secrets.

  Fields:
    kmsKeyName: Cloud KMS key name to use to decrypt these envs.
    secretEnv: Map of environment variable name to its encrypted value. Secret
      environment variables must be unique across all of a build's secrets,
      and must be used by at least one build step. Values can be at most 64 KB
      in size. There can be at most 100 secret values across all of a build's
      secrets.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SecretEnvValue(_messages.Message):
    r"""Map of environment variable name to its encrypted value. Secret
    environment variables must be unique across all of a build's secrets, and
    must be used by at least one build step. Values can be at most 64 KB in
    size. There can be at most 100 secret values across all of a build's
    secrets.

    Messages:
      AdditionalProperty: An additional property for a SecretEnvValue object.

    Fields:
      additionalProperties: Additional properties of type SecretEnvValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SecretEnvValue object.

      Fields:
        key: Name of the additional property.
        value: A byte attribute.
      """

      key = _messages.StringField(1)
      value = _messages.BytesField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  kmsKeyName = _messages.StringField(1)
  secretEnv = _messages.MessageField('SecretEnvValue', 2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1SecretManagerSecret(_messages.Message):
  r"""Pairs a secret environment variable with a SecretVersion in Secret
  Manager.

  Fields:
    env: Environment variable name to associate with the secret. Secret
      environment variables must be unique across all of a build's secrets,
      and must be used by at least one build step.
    versionName: Resource name of the SecretVersion. In format:
      projects/*/secrets/*/versions/*
  """

  env = _messages.StringField(1)
  versionName = _messages.StringField(2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1Secrets(_messages.Message):
  r"""Secrets and secret environment variables.

  Fields:
    inline: Secrets encrypted with KMS key and the associated secret
      environment variable.
    secretManager: Secrets in Secret Manager and associated secret environment
      variable.
  """

  inline = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1InlineSecret', 1, repeated=True)
  secretManager = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1SecretManagerSecret', 2, repeated=True)


class ContaineranalysisGoogleDevtoolsCloudbuildV1Source(_messages.Message):
  r"""Location of the source in a supported storage service.

  Fields:
    gitSource: If provided, get the source from this Git repository.
    repoSource: If provided, get the source from this location in a Cloud
      Source Repository.
    storageSource: If provided, get the source from this location in Cloud
      Storage.
    storageSourceManifest: If provided, get the source from this manifest in
      Cloud Storage. This feature is in Preview; see description
      [here](https://github.com/GoogleCloudPlatform/cloud-
      builders/tree/master/gcs-fetcher).
  """

  gitSource = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1GitSource', 1)
  repoSource = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1RepoSource', 2)
  storageSource = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSource', 3)
  storageSourceManifest = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSourceManifest', 4)


class ContaineranalysisGoogleDevtoolsCloudbuildV1SourceProvenance(_messages.Message):
  r"""Provenance of the source. Ways to find the original source, or verify
  that some source was used for this build.

  Messages:
    FileHashesValue: Output only. Hash(es) of the build source, which can be
      used to verify that the original source integrity was maintained in the
      build. Note that `FileHashes` will only be populated if `BuildOptions`
      has requested a `SourceProvenanceHash`. The keys to this map are file
      paths used as build source and the values contain the hash values for
      those files. If the build source came in a single package such as a
      gzipped tarfile (`.tar.gz`), the `FileHash` will be for the single path
      to that file.

  Fields:
    fileHashes: Output only. Hash(es) of the build source, which can be used
      to verify that the original source integrity was maintained in the
      build. Note that `FileHashes` will only be populated if `BuildOptions`
      has requested a `SourceProvenanceHash`. The keys to this map are file
      paths used as build source and the values contain the hash values for
      those files. If the build source came in a single package such as a
      gzipped tarfile (`.tar.gz`), the `FileHash` will be for the single path
      to that file.
    resolvedRepoSource: A copy of the build's `source.repo_source`, if exists,
      with any revisions resolved.
    resolvedStorageSource: A copy of the build's `source.storage_source`, if
      exists, with any generations resolved.
    resolvedStorageSourceManifest: A copy of the build's
      `source.storage_source_manifest`, if exists, with any revisions
      resolved. This feature is in Preview.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FileHashesValue(_messages.Message):
    r"""Output only. Hash(es) of the build source, which can be used to verify
    that the original source integrity was maintained in the build. Note that
    `FileHashes` will only be populated if `BuildOptions` has requested a
    `SourceProvenanceHash`. The keys to this map are file paths used as build
    source and the values contain the hash values for those files. If the
    build source came in a single package such as a gzipped tarfile
    (`.tar.gz`), the `FileHash` will be for the single path to that file.

    Messages:
      AdditionalProperty: An additional property for a FileHashesValue object.

    Fields:
      additionalProperties: Additional properties of type FileHashesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FileHashesValue object.

      Fields:
        key: Name of the additional property.
        value: A ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes
          attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  fileHashes = _messages.MessageField('FileHashesValue', 1)
  resolvedRepoSource = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1RepoSource', 2)
  resolvedStorageSource = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSource', 3)
  resolvedStorageSourceManifest = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSourceManifest', 4)


class ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSource(_messages.Message):
  r"""Location of the source in an archive file in Cloud Storage.

  Fields:
    bucket: Cloud Storage bucket containing the source (see [Bucket Name
      Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)).
    generation: Cloud Storage generation for the object. If the generation is
      omitted, the latest generation will be used.
    object: Cloud Storage object containing the source. This object must be a
      zipped (`.zip`) or gzipped archive file (`.tar.gz`) containing source to
      build.
  """

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1StorageSourceManifest(_messages.Message):
  r"""Location of the source manifest in Cloud Storage. This feature is in
  Preview; see description
  [here](https://github.com/GoogleCloudPlatform/cloud-
  builders/tree/master/gcs-fetcher).

  Fields:
    bucket: Cloud Storage bucket containing the source manifest (see [Bucket
      Name Requirements](https://cloud.google.com/storage/docs/bucket-
      naming#requirements)).
    generation: Cloud Storage generation for the object. If the generation is
      omitted, the latest generation will be used.
    object: Cloud Storage object containing the source manifest. This object
      must be a JSON file.
  """

  bucket = _messages.StringField(1)
  generation = _messages.IntegerField(2)
  object = _messages.StringField(3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan(_messages.Message):
  r"""Start and end times for a build execution phase.

  Fields:
    endTime: End of time span.
    startTime: Start of time span.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedMavenArtifact(_messages.Message):
  r"""A Maven artifact uploaded using the MavenArtifact directive.

  Fields:
    fileHashes: Hash types and values of the Maven Artifact.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded artifact.
  """

  fileHashes = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes', 1)
  pushTiming = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 2)
  uri = _messages.StringField(3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedNpmPackage(_messages.Message):
  r"""An npm package uploaded to Artifact Registry using the NpmPackage
  directive.

  Fields:
    fileHashes: Hash types and values of the npm package.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded npm package.
  """

  fileHashes = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes', 1)
  pushTiming = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 2)
  uri = _messages.StringField(3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1UploadedPythonPackage(_messages.Message):
  r"""Artifact uploaded using the PythonPackage directive.

  Fields:
    fileHashes: Hash types and values of the Python Artifact.
    pushTiming: Output only. Stores timing information for pushing the
      specified artifact.
    uri: URI of the uploaded artifact.
  """

  fileHashes = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1FileHashes', 1)
  pushTiming = _messages.MessageField('ContaineranalysisGoogleDevtoolsCloudbuildV1TimeSpan', 2)
  uri = _messages.StringField(3)


class ContaineranalysisGoogleDevtoolsCloudbuildV1Volume(_messages.Message):
  r"""Volume describes a Docker container volume which is mounted into build
  steps in order to persist files across build step execution.

  Fields:
    name: Name of the volume to mount. Volume names must be unique per build
      step and must be valid names for Docker volumes. Each named volume must
      be used by at least two build steps.
    path: Path at which to mount the volume. Paths must be absolute and cannot
      conflict with other volume paths on the same build step or with certain
      reserved volume paths.
  """

  name = _messages.StringField(1)
  path = _messages.StringField(2)


class ContaineranalysisProjectsNotesBatchCreateRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesBatchCreateRequest object.

  Fields:
    batchCreateNotesRequest: A BatchCreateNotesRequest resource to be passed
      as the request body.
    parent: Required. The name of the project in the form of
      `projects/[PROJECT_ID]`, under which the notes are to be created.
  """

  batchCreateNotesRequest = _messages.MessageField('BatchCreateNotesRequest', 1)
  parent = _messages.StringField(2, required=True)


class ContaineranalysisProjectsNotesCreateRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesCreateRequest object.

  Fields:
    note: A Note resource to be passed as the request body.
    noteId: Required. The ID to use for this note.
    parent: Required. The name of the project in the form of
      `projects/[PROJECT_ID]`, under which the note is to be created.
  """

  note = _messages.MessageField('Note', 1)
  noteId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ContaineranalysisProjectsNotesDeleteRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesDeleteRequest object.

  Fields:
    name: Required. The name of the note in the form of
      `projects/[PROVIDER_ID]/notes/[NOTE_ID]`.
  """

  name = _messages.StringField(1, required=True)


class ContaineranalysisProjectsNotesGetIamPolicyRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class ContaineranalysisProjectsNotesGetRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesGetRequest object.

  Fields:
    name: Required. The name of the note in the form of
      `projects/[PROVIDER_ID]/notes/[NOTE_ID]`.
  """

  name = _messages.StringField(1, required=True)


class ContaineranalysisProjectsNotesListRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesListRequest object.

  Fields:
    filter: The filter expression.
    pageSize: Number of notes to return in the list. Must be positive. Max
      allowed page size is 1000. If not specified, page size defaults to 20.
    pageToken: Token to provide to skip to a particular spot in the list.
    parent: Required. The name of the project to list notes for in the form of
      `projects/[PROJECT_ID]`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ContaineranalysisProjectsNotesOccurrencesListRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesOccurrencesListRequest object.

  Fields:
    filter: The filter expression.
    name: Required. The name of the note to list occurrences for in the form
      of `projects/[PROVIDER_ID]/notes/[NOTE_ID]`.
    pageSize: Number of occurrences to return in the list.
    pageToken: Token to provide to skip to a particular spot in the list.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ContaineranalysisProjectsNotesPatchRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesPatchRequest object.

  Fields:
    name: Required. The name of the note in the form of
      `projects/[PROVIDER_ID]/notes/[NOTE_ID]`.
    note: A Note resource to be passed as the request body.
    updateMask: The fields to update.
  """

  name = _messages.StringField(1, required=True)
  note = _messages.MessageField('Note', 2)
  updateMask = _messages.StringField(3)


class ContaineranalysisProjectsNotesSetIamPolicyRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ContaineranalysisProjectsNotesTestIamPermissionsRequest(_messages.Message):
  r"""A ContaineranalysisProjectsNotesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class ContaineranalysisProjectsOccurrencesBatchCreateRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesBatchCreateRequest object.

  Fields:
    batchCreateOccurrencesRequest: A BatchCreateOccurrencesRequest resource to
      be passed as the request body.
    parent: Required. The name of the project in the form of
      `projects/[PROJECT_ID]`, under which the occurrences are to be created.
  """

  batchCreateOccurrencesRequest = _messages.MessageField('BatchCreateOccurrencesRequest', 1)
  parent = _messages.StringField(2, required=True)


class ContaineranalysisProjectsOccurrencesCreateRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesCreateRequest object.

  Fields:
    occurrence: A Occurrence resource to be passed as the request body.
    parent: Required. The name of the project in the form of
      `projects/[PROJECT_ID]`, under which the occurrence is to be created.
  """

  occurrence = _messages.MessageField('Occurrence', 1)
  parent = _messages.StringField(2, required=True)


class ContaineranalysisProjectsOccurrencesDeleteRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesDeleteRequest object.

  Fields:
    name: Required. The name of the occurrence in the form of
      `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.
  """

  name = _messages.StringField(1, required=True)


class ContaineranalysisProjectsOccurrencesGetIamPolicyRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class ContaineranalysisProjectsOccurrencesGetNotesRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesGetNotesRequest object.

  Fields:
    name: Required. The name of the occurrence in the form of
      `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.
  """

  name = _messages.StringField(1, required=True)


class ContaineranalysisProjectsOccurrencesGetRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesGetRequest object.

  Fields:
    name: Required. The name of the occurrence in the form of
      `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.
  """

  name = _messages.StringField(1, required=True)


class ContaineranalysisProjectsOccurrencesGetVulnerabilitySummaryRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesGetVulnerabilitySummaryRequest
  object.

  Fields:
    filter: The filter expression.
    parent: Required. The name of the project to get a vulnerability summary
      for in the form of `projects/[PROJECT_ID]`.
  """

  filter = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)


class ContaineranalysisProjectsOccurrencesListRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesListRequest object.

  Fields:
    filter: The filter expression.
    pageSize: Number of occurrences to return in the list. Must be positive.
      Max allowed page size is 1000. If not specified, page size defaults to
      20.
    pageToken: Token to provide to skip to a particular spot in the list.
    parent: Required. The name of the project to list occurrences for in the
      form of `projects/[PROJECT_ID]`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ContaineranalysisProjectsOccurrencesPatchRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesPatchRequest object.

  Fields:
    name: Required. The name of the occurrence in the form of
      `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.
    occurrence: A Occurrence resource to be passed as the request body.
    updateMask: The fields to update.
  """

  name = _messages.StringField(1, required=True)
  occurrence = _messages.MessageField('Occurrence', 2)
  updateMask = _messages.StringField(3)


class ContaineranalysisProjectsOccurrencesSetIamPolicyRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ContaineranalysisProjectsOccurrencesTestIamPermissionsRequest(_messages.Message):
  r"""A ContaineranalysisProjectsOccurrencesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class ContaineranalysisProjectsResourcesExportSBOMRequest(_messages.Message):
  r"""A ContaineranalysisProjectsResourcesExportSBOMRequest object.

  Fields:
    exportSBOMRequest: A ExportSBOMRequest resource to be passed as the
      request body.
    name: Required. The name of the resource in the form of
      `projects/[PROJECT_ID]/resources/[RESOURCE_URL]`.
  """

  exportSBOMRequest = _messages.MessageField('ExportSBOMRequest', 1)
  name = _messages.StringField(2, required=True)


class ContaineranalysisProjectsResourcesGeneratePackagesSummaryRequest(_messages.Message):
  r"""A ContaineranalysisProjectsResourcesGeneratePackagesSummaryRequest
  object.

  Fields:
    generatePackagesSummaryRequest: A GeneratePackagesSummaryRequest resource
      to be passed as the request body.
    name: Required. The name of the resource to get a packages summary for in
      the form of `projects/[PROJECT_ID]/resources/[RESOURCE_URL]`.
  """

  generatePackagesSummaryRequest = _messages.MessageField('GeneratePackagesSummaryRequest', 1)
  name = _messages.StringField(2, required=True)


class Deployable(_messages.Message):
  r"""An artifact that can be deployed in some runtime.

  Fields:
    resourceUri: Required. Resource URI for the artifact being deployed.
  """

  resourceUri = _messages.StringField(1, repeated=True)


class Deployment(_messages.Message):
  r"""The period during which some deployable was active in a runtime.

  Enums:
    PlatformValueValuesEnum: Platform hosting this deployment.

  Fields:
    address: Address of the runtime element hosting this deployment.
    config: Configuration used to create this deployment.
    deployTime: Required. Beginning of the lifetime of this deployment.
    platform: Platform hosting this deployment.
    resourceUri: Output only. Resource URI for the artifact being deployed
      taken from the deployable field with the same name.
    undeployTime: End of the lifetime of this deployment.
    userEmail: Identity of the user that triggered this deployment.
  """

  class PlatformValueValuesEnum(_messages.Enum):
    r"""Platform hosting this deployment.

    Values:
      PLATFORM_UNSPECIFIED: Unknown.
      GKE: Google Container Engine.
      FLEX: Google App Engine: Flexible Environment.
      CUSTOM: Custom user-defined platform.
    """
    PLATFORM_UNSPECIFIED = 0
    GKE = 1
    FLEX = 2
    CUSTOM = 3

  address = _messages.StringField(1)
  config = _messages.StringField(2)
  deployTime = _messages.StringField(3)
  platform = _messages.EnumField('PlatformValueValuesEnum', 4)
  resourceUri = _messages.StringField(5, repeated=True)
  undeployTime = _messages.StringField(6)
  userEmail = _messages.StringField(7)


class Derived(_messages.Message):
  r"""Derived describes the derived image portion (Occurrence) of the
  DockerImage relationship. This image would be produced from a Dockerfile
  with FROM .

  Fields:
    baseResourceUrl: Output only. This contains the base image URL for the
      derived image occurrence.
    distance: Output only. The number of layers by which this image differs
      from the associated image basis.
    fingerprint: Required. The fingerprint of the derived image.
    layerInfo: This contains layer-specific metadata, if populated it has
      length "distance" and is ordered with [distance] being the layer
      immediately following the base image and [1] being the final layer.
  """

  baseResourceUrl = _messages.StringField(1)
  distance = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  fingerprint = _messages.MessageField('Fingerprint', 3)
  layerInfo = _messages.MessageField('Layer', 4, repeated=True)


class Detail(_messages.Message):
  r"""Identifies all appearances of this vulnerability in the package for a
  specific distro/location. For example: glibc in cpe:/o:debian:debian_linux:8
  for versions 2.1 - 2.2

  Fields:
    cpeUri: Required. The CPE URI in [cpe
      format](https://cpe.mitre.org/specification/) in which the vulnerability
      manifests. Examples include distro or storage location for vulnerable
      jar.
    description: A vendor-specific description of this note.
    fixedLocation: The fix for this specific package version.
    isObsolete: Whether this detail is obsolete. Occurrences are expected not
      to point to obsolete details.
    maxAffectedVersion: The max version of the package in which the
      vulnerability exists.
    minAffectedVersion: The min version of the package in which the
      vulnerability exists.
    package: Required. The name of the package where the vulnerability was
      found.
    packageType: The type of package; whether native or non native(ruby gems,
      node.js packages etc).
    severityName: The severity (eg: distro assigned severity) for this
      vulnerability.
    source: The source from which the information in this Detail was obtained.
    sourceUpdateTime: The time this information was last changed at the
      source. This is an upstream timestamp from the underlying information
      source - e.g. Ubuntu security tracker.
    vendor: The name of the vendor of the product.
  """

  cpeUri = _messages.StringField(1)
  description = _messages.StringField(2)
  fixedLocation = _messages.MessageField('VulnerabilityLocation', 3)
  isObsolete = _messages.BooleanField(4)
  maxAffectedVersion = _messages.MessageField('Version', 5)
  minAffectedVersion = _messages.MessageField('Version', 6)
  package = _messages.StringField(7)
  packageType = _messages.StringField(8)
  severityName = _messages.StringField(9)
  source = _messages.StringField(10)
  sourceUpdateTime = _messages.StringField(11)
  vendor = _messages.StringField(12)


class Details(_messages.Message):
  r"""Details of an attestation occurrence.

  Fields:
    attestation: Required. Attestation for the resource.
  """

  attestation = _messages.MessageField('Attestation', 1)


class Digest(_messages.Message):
  r"""Digest information.

  Fields:
    algo: `SHA1`, `SHA512` etc.
    digestBytes: Value of the digest.
  """

  algo = _messages.StringField(1)
  digestBytes = _messages.BytesField(2)


class Discovered(_messages.Message):
  r"""Provides information about the analysis status of a discovered resource.

  Enums:
    AnalysisStatusValueValuesEnum: The status of discovery for the resource.
    ContinuousAnalysisValueValuesEnum: Whether the resource is continuously
      analyzed.

  Fields:
    analysisCompleted: A AnalysisCompleted attribute.
    analysisError: Indicates any errors encountered during analysis of a
      resource. There could be 0 or more of these errors.
    analysisStatus: The status of discovery for the resource.
    analysisStatusError: When an error is encountered this will contain a
      LocalizedMessage under details to show to the user. The LocalizedMessage
      is output only and populated by the API.
    continuousAnalysis: Whether the resource is continuously analyzed.
    lastAnalysisTime: The last time continuous analysis was done for this
      resource. Deprecated, do not use.
    sbomStatus: The status of an SBOM generation.
  """

  class AnalysisStatusValueValuesEnum(_messages.Enum):
    r"""The status of discovery for the resource.

    Values:
      ANALYSIS_STATUS_UNSPECIFIED: Unknown.
      PENDING: Resource is known but no action has been taken yet.
      SCANNING: Resource is being analyzed.
      FINISHED_SUCCESS: Analysis has finished successfully.
      COMPLETE: Analysis has completed.
      FINISHED_FAILED: Analysis has finished unsuccessfully, the analysis
        itself is in a bad state.
      FINISHED_UNSUPPORTED: The resource is known not to be supported
    """
    ANALYSIS_STATUS_UNSPECIFIED = 0
    PENDING = 1
    SCANNING = 2
    FINISHED_SUCCESS = 3
    COMPLETE = 4
    FINISHED_FAILED = 5
    FINISHED_UNSUPPORTED = 6

  class ContinuousAnalysisValueValuesEnum(_messages.Enum):
    r"""Whether the resource is continuously analyzed.

    Values:
      CONTINUOUS_ANALYSIS_UNSPECIFIED: Unknown.
      ACTIVE: The resource is continuously analyzed.
      INACTIVE: The resource is ignored for continuous analysis.
    """
    CONTINUOUS_ANALYSIS_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2

  analysisCompleted = _messages.MessageField('AnalysisCompleted', 1)
  analysisError = _messages.MessageField('Status', 2, repeated=True)
  analysisStatus = _messages.EnumField('AnalysisStatusValueValuesEnum', 3)
  analysisStatusError = _messages.MessageField('Status', 4)
  continuousAnalysis = _messages.EnumField('ContinuousAnalysisValueValuesEnum', 5)
  lastAnalysisTime = _messages.StringField(6)
  sbomStatus = _messages.MessageField('SBOMStatus', 7)


class Discovery(_messages.Message):
  r"""A note that indicates a type of analysis a provider would perform. This
  note exists in a provider's project. A `Discovery` occurrence is created in
  a consumer's project at the start of analysis.

  Enums:
    AnalysisKindValueValuesEnum: Required. Immutable. The kind of analysis
      that is handled by this discovery.

  Fields:
    analysisKind: Required. Immutable. The kind of analysis that is handled by
      this discovery.
  """

  class AnalysisKindValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. The kind of analysis that is handled by this
    discovery.

    Values:
      NOTE_KIND_UNSPECIFIED: Default value. This value is unused.
      VULNERABILITY: The note and occurrence represent a package
        vulnerability.
      BUILD: The note and occurrence assert build provenance.
      IMAGE: This represents an image basis relationship.
      PACKAGE: This represents a package installed via a package manager.
      DEPLOYMENT: The note and occurrence track deployment events.
      DISCOVERY: The note and occurrence track the initial discovery status of
        a resource.
      ATTESTATION: This represents a logical "role" that can attest to
        artifacts.
      INTOTO: This represents an in-toto link.
      SBOM: This represents a software bill of materials.
      SPDX_PACKAGE: This represents an SPDX Package.
      SPDX_FILE: This represents an SPDX File.
      SPDX_RELATIONSHIP: This represents an SPDX Relationship.
      VULNERABILITY_ASSESSMENT: This represents a Vulnerability Assessment.
      SBOM_REFERENCE: This represents an SBOM Reference.
    """
    NOTE_KIND_UNSPECIFIED = 0
    VULNERABILITY = 1
    BUILD = 2
    IMAGE = 3
    PACKAGE = 4
    DEPLOYMENT = 5
    DISCOVERY = 6
    ATTESTATION = 7
    INTOTO = 8
    SBOM = 9
    SPDX_PACKAGE = 10
    SPDX_FILE = 11
    SPDX_RELATIONSHIP = 12
    VULNERABILITY_ASSESSMENT = 13
    SBOM_REFERENCE = 14

  analysisKind = _messages.EnumField('AnalysisKindValueValuesEnum', 1)


class Distribution(_messages.Message):
  r"""This represents a particular channel of distribution for a given
  package. E.g., Debian's jessie-backports dpkg mirror.

  Enums:
    ArchitectureValueValuesEnum: The CPU architecture for which packages in
      this distribution channel were built.

  Fields:
    architecture: The CPU architecture for which packages in this distribution
      channel were built.
    cpeUri: Required. The cpe_uri in [CPE
      format](https://cpe.mitre.org/specification/) denoting the package
      manager version distributing a package.
    description: The distribution channel-specific description of this
      package.
    latestVersion: The latest available version of this package in this
      distribution channel.
    maintainer: A freeform string denoting the maintainer of this package.
    url: The distribution channel-specific homepage for this package.
  """

  class ArchitectureValueValuesEnum(_messages.Enum):
    r"""The CPU architecture for which packages in this distribution channel
    were built.

    Values:
      ARCHITECTURE_UNSPECIFIED: Unknown architecture.
      X86: X86 architecture.
      X64: X64 architecture.
    """
    ARCHITECTURE_UNSPECIFIED = 0
    X86 = 1
    X64 = 2

  architecture = _messages.EnumField('ArchitectureValueValuesEnum', 1)
  cpeUri = _messages.StringField(2)
  description = _messages.StringField(3)
  latestVersion = _messages.MessageField('Version', 4)
  maintainer = _messages.StringField(5)
  url = _messages.StringField(6)


class DocumentNote(_messages.Message):
  r"""DocumentNote represents an SPDX Document Creation Information section:
  https://spdx.github.io/spdx-spec/v2.3/document-creation-information/

  Fields:
    dataLicence: Compliance with the SPDX specification includes populating
      the SPDX fields therein with data related to such fields ("SPDX-
      Metadata")
    spdxVersion: Provide a reference number that can be used to understand how
      to parse and interpret the rest of the file
  """

  dataLicence = _messages.StringField(1)
  spdxVersion = _messages.StringField(2)


class DocumentOccurrence(_messages.Message):
  r"""DocumentOccurrence represents an SPDX Document Creation Information
  section: https://spdx.github.io/spdx-spec/v2.3/document-creation-
  information/

  Fields:
    createTime: Identify when the SPDX file was originally created. The date
      is to be specified according to combined date and time in UTC format as
      specified in ISO 8601 standard
    creatorComment: A field for creators of the SPDX file to provide general
      comments about the creation of the SPDX file or any other relevant
      comment not included in the other fields
    creators: Identify who (or what, in the case of a tool) created the SPDX
      file. If the SPDX file was created by an individual, indicate the
      person's name
    documentComment: A field for creators of the SPDX file content to provide
      comments to the consumers of the SPDX document
    externalDocumentRefs: Identify any external SPDX documents referenced
      within this SPDX document
    id: Identify the current SPDX document which may be referenced in
      relationships by other files, packages internally and documents
      externally
    licenseListVersion: A field for creators of the SPDX file to provide the
      version of the SPDX License List used when the SPDX file was created
    namespace: Provide an SPDX document specific namespace as a unique
      absolute Uniform Resource Identifier (URI) as specified in RFC-3986,
      with the exception of the '#' delimiter
    title: Identify name of this document as designated by creator
  """

  createTime = _messages.StringField(1)
  creatorComment = _messages.StringField(2)
  creators = _messages.StringField(3, repeated=True)
  documentComment = _messages.StringField(4)
  externalDocumentRefs = _messages.StringField(5, repeated=True)
  id = _messages.StringField(6)
  licenseListVersion = _messages.StringField(7)
  namespace = _messages.StringField(8)
  title = _messages.StringField(9)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Envelope(_messages.Message):
  r"""MUST match https://github.com/secure-systems-
  lab/dsse/blob/master/envelope.proto. An authenticated message of arbitrary
  type.

  Fields:
    payload: A byte attribute.
    payloadType: A string attribute.
    signatures: A EnvelopeSignature attribute.
  """

  payload = _messages.BytesField(1)
  payloadType = _messages.StringField(2)
  signatures = _messages.MessageField('EnvelopeSignature', 3, repeated=True)


class EnvelopeSignature(_messages.Message):
  r"""A EnvelopeSignature object.

  Fields:
    keyid: A string attribute.
    sig: A byte attribute.
  """

  keyid = _messages.StringField(1)
  sig = _messages.BytesField(2)


class Environment(_messages.Message):
  r"""Defines an object for the environment field in in-toto links. The
  suggested fields are "variables", "filesystem", and "workdir".

  Messages:
    CustomValuesValue: A CustomValuesValue object.

  Fields:
    customValues: A CustomValuesValue attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CustomValuesValue(_messages.Message):
    r"""A CustomValuesValue object.

    Messages:
      AdditionalProperty: An additional property for a CustomValuesValue
        object.

    Fields:
      additionalProperties: Additional properties of type CustomValuesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CustomValuesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  customValues = _messages.MessageField('CustomValuesValue', 1)


class ExportSBOMRequest(_messages.Message):
  r"""The request to a call of ExportSBOM"""


class ExportSBOMResponse(_messages.Message):
  r"""The response from a call to ExportSBOM

  Fields:
    discoveryOccurrenceId: The name of the discovery occurrence in the form
      "projects/{project_id}/occurrences/{OCCURRENCE_ID} It can be used to
      track the progression of the SBOM export.
  """

  discoveryOccurrenceId = _messages.StringField(1)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class ExternalRef(_messages.Message):
  r"""An External Reference allows a Package to reference an external source
  of additional information, metadata, enumerations, asset identifiers, or
  downloadable content believed to be relevant to the Package

  Enums:
    CategoryValueValuesEnum: An External Reference allows a Package to
      reference an external source of additional information, metadata,
      enumerations, asset identifiers, or downloadable content believed to be
      relevant to the Package

  Fields:
    category: An External Reference allows a Package to reference an external
      source of additional information, metadata, enumerations, asset
      identifiers, or downloadable content believed to be relevant to the
      Package
    comment: Human-readable information about the purpose and target of the
      reference
    locator: The unique string with no spaces necessary to access the package-
      specific information, metadata, or content within the target location
    type: Type of category (e.g. 'npm' for the PACKAGE_MANAGER category)
  """

  class CategoryValueValuesEnum(_messages.Enum):
    r"""An External Reference allows a Package to reference an external source
    of additional information, metadata, enumerations, asset identifiers, or
    downloadable content believed to be relevant to the Package

    Values:
      CATEGORY_UNSPECIFIED: Unspecified
      SECURITY: Security (e.g. cpe22Type, cpe23Type)
      PACKAGE_MANAGER: Package Manager (e.g. maven-central, npm, nuget, bower,
        purl)
      PERSISTENT_ID: Persistent-Id (e.g. swh)
      OTHER: Other
    """
    CATEGORY_UNSPECIFIED = 0
    SECURITY = 1
    PACKAGE_MANAGER = 2
    PERSISTENT_ID = 3
    OTHER = 4

  category = _messages.EnumField('CategoryValueValuesEnum', 1)
  comment = _messages.StringField(2)
  locator = _messages.StringField(3)
  type = _messages.StringField(4)


class FileHashes(_messages.Message):
  r"""Container message for hashes of byte content of files, used in source
  messages to verify integrity of source input to the build.

  Fields:
    fileHash: Required. Collection of file hashes.
  """

  fileHash = _messages.MessageField('Hash', 1, repeated=True)


class FileNote(_messages.Message):
  r"""FileNote represents an SPDX File Information section:
  https://spdx.github.io/spdx-spec/4-file-information/

  Enums:
    FileTypeValueValuesEnum: This field provides information about the type of
      file identified

  Fields:
    checksum: Provide a unique identifier to match analysis information on
      each specific file in a package
    fileType: This field provides information about the type of file
      identified
    title: Identify the full path and filename that corresponds to the file
      information in this section
  """

  class FileTypeValueValuesEnum(_messages.Enum):
    r"""This field provides information about the type of file identified

    Values:
      FILE_TYPE_UNSPECIFIED: Unspecified
      SOURCE: The file is human readable source code (.c, .html, etc.)
      BINARY: The file is a compiled object, target image or binary executable
        (.o, .a, etc.)
      ARCHIVE: The file represents an archive (.tar, .jar, etc.)
      APPLICATION: The file is associated with a specific application type
        (MIME type of application/*)
      AUDIO: The file is associated with an audio file (MIME type of audio/* ,
        e.g. .mp3)
      IMAGE: The file is associated with an picture image file (MIME type of
        image/*, e.g., .jpg, .gif)
      TEXT: The file is human readable text file (MIME type of text/*)
      VIDEO: The file is associated with a video file type (MIME type of
        video/*)
      DOCUMENTATION: The file serves as documentation
      SPDX: The file is an SPDX document
      OTHER: The file doesn't fit into the above categories (generated
        artifacts, data files, etc.)
    """
    FILE_TYPE_UNSPECIFIED = 0
    SOURCE = 1
    BINARY = 2
    ARCHIVE = 3
    APPLICATION = 4
    AUDIO = 5
    IMAGE = 6
    TEXT = 7
    VIDEO = 8
    DOCUMENTATION = 9
    SPDX = 10
    OTHER = 11

  checksum = _messages.StringField(1, repeated=True)
  fileType = _messages.EnumField('FileTypeValueValuesEnum', 2)
  title = _messages.StringField(3)


class FileOccurrence(_messages.Message):
  r"""FileOccurrence represents an SPDX File Information section:
  https://spdx.github.io/spdx-spec/4-file-information/

  Fields:
    attributions: This field provides a place for the SPDX data creator to
      record, at the file level, acknowledgements that may be needed to be
      communicated in some contexts
    comment: This field provides a place for the SPDX file creator to record
      any general comments about the file
    contributors: This field provides a place for the SPDX file creator to
      record file contributors
    copyright: Identify the copyright holder of the file, as well as any dates
      present
    filesLicenseInfo: This field contains the license information actually
      found in the file, if any
    id: Uniquely identify any element in an SPDX document which may be
      referenced by other elements
    licenseConcluded: This field contains the license the SPDX file creator
      has concluded as governing the file or alternative values if the
      governing license cannot be determined
    notice: This field provides a place for the SPDX file creator to record
      license notices or other such related notices found in the file
  """

  attributions = _messages.StringField(1, repeated=True)
  comment = _messages.StringField(2)
  contributors = _messages.StringField(3, repeated=True)
  copyright = _messages.StringField(4)
  filesLicenseInfo = _messages.StringField(5, repeated=True)
  id = _messages.StringField(6)
  licenseConcluded = _messages.MessageField('License', 7)
  notice = _messages.StringField(8)


class Fingerprint(_messages.Message):
  r"""A set of properties that uniquely identify a given Docker image.

  Fields:
    v1Name: Required. The layer ID of the final layer in the Docker image's v1
      representation.
    v2Blob: Required. The ordered list of v2 blobs that represent a given
      image.
    v2Name: Output only. The name of the image's v2 blobs computed via:
      [bottom] := v2_blobbottom := sha256(v2_blob[N] + " " + v2_name[N+1])
      Only the name of the final blob is kept.
  """

  v1Name = _messages.StringField(1)
  v2Blob = _messages.StringField(2, repeated=True)
  v2Name = _messages.StringField(3)


class FixableTotalByDigest(_messages.Message):
  r"""Per resource and severity counts of fixable and total vulnerabilities.

  Enums:
    SeverityValueValuesEnum: The severity for this count. SEVERITY_UNSPECIFIED
      indicates total across all severities.

  Fields:
    fixableCount: The number of fixable vulnerabilities associated with this
      resource.
    resource: The affected resource.
    severity: The severity for this count. SEVERITY_UNSPECIFIED indicates
      total across all severities.
    totalCount: The total number of vulnerabilities associated with this
      resource.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity for this count. SEVERITY_UNSPECIFIED indicates total
    across all severities.

    Values:
      SEVERITY_UNSPECIFIED: Unknown.
      MINIMAL: Minimal severity.
      LOW: Low severity.
      MEDIUM: Medium severity.
      HIGH: High severity.
      CRITICAL: Critical severity.
    """
    SEVERITY_UNSPECIFIED = 0
    MINIMAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5

  fixableCount = _messages.IntegerField(1)
  resource = _messages.MessageField('Resource', 2)
  severity = _messages.EnumField('SeverityValueValuesEnum', 3)
  totalCount = _messages.IntegerField(4)


class GeneratePackagesSummaryRequest(_messages.Message):
  r"""GeneratePackagesSummaryRequest is the request body for the
  GeneratePackagesSummary API method. It just takes a single name argument,
  referring to the resource.
  """



class GenericSignedAttestation(_messages.Message):
  r"""An attestation wrapper that uses the Grafeas `Signature` message. This
  attestation must define the `serialized_payload` that the `signatures`
  verify and any metadata necessary to interpret that plaintext. The
  signatures should always be over the `serialized_payload` bytestring.

  Enums:
    ContentTypeValueValuesEnum: Type (for example schema) of the attestation
      payload that was signed. The verifier must ensure that the provided type
      is one that the verifier supports, and that the attestation payload is a
      valid instantiation of that type (for example by validating a JSON
      schema).

  Fields:
    contentType: Type (for example schema) of the attestation payload that was
      signed. The verifier must ensure that the provided type is one that the
      verifier supports, and that the attestation payload is a valid
      instantiation of that type (for example by validating a JSON schema).
    serializedPayload: The serialized payload that is verified by one or more
      `signatures`. The encoding and semantic meaning of this payload must
      match what is set in `content_type`.
    signatures: One or more signatures over `serialized_payload`. Verifier
      implementations should consider this attestation message verified if at
      least one `signature` verifies `serialized_payload`. See `Signature` in
      common.proto for more details on signature structure and verification.
  """

  class ContentTypeValueValuesEnum(_messages.Enum):
    r"""Type (for example schema) of the attestation payload that was signed.
    The verifier must ensure that the provided type is one that the verifier
    supports, and that the attestation payload is a valid instantiation of
    that type (for example by validating a JSON schema).

    Values:
      CONTENT_TYPE_UNSPECIFIED: `ContentType` is not set.
      SIMPLE_SIGNING_JSON: Atomic format attestation signature. See https://gi
        thub.com/containers/image/blob/8a5d2f82a6e3263290c8e0276c3e0f64e77723e
        7/docs/atomic-signature.md The payload extracted in `plaintext` is a
        JSON blob conforming to the linked schema.
    """
    CONTENT_TYPE_UNSPECIFIED = 0
    SIMPLE_SIGNING_JSON = 1

  contentType = _messages.EnumField('ContentTypeValueValuesEnum', 1)
  serializedPayload = _messages.BytesField(2)
  signatures = _messages.MessageField('Signature', 3, repeated=True)


class GerritSourceContext(_messages.Message):
  r"""A SourceContext referring to a Gerrit project.

  Fields:
    aliasContext: An alias, which may be a branch or tag.
    gerritProject: The full project name within the host. Projects may be
      nested, so "project/subproject" is a valid project name. The "repo name"
      is the hostURI/project.
    hostUri: The URI of a running Gerrit instance.
    revisionId: A revision (commit) ID.
  """

  aliasContext = _messages.MessageField('AliasContext', 1)
  gerritProject = _messages.StringField(2)
  hostUri = _messages.StringField(3)
  revisionId = _messages.StringField(4)


class GetIamPolicyRequest(_messages.Message):
  r"""Request message for `GetIamPolicy` method.

  Fields:
    options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
      `GetIamPolicy`.
  """

  options = _messages.MessageField('GetPolicyOptions', 1)


class GetPolicyOptions(_messages.Message):
  r"""Encapsulates settings provided to GetIamPolicy.

  Fields:
    requestedPolicyVersion: Optional. The maximum policy version that will be
      used to format the policy. Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected. Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset. The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1. To learn which resources support conditions in
      their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GitSourceContext(_messages.Message):
  r"""A GitSourceContext denotes a particular revision in a third party Git
  repository (e.g., GitHub).

  Fields:
    revisionId: Git commit hash.
    url: Git repository URL.
  """

  revisionId = _messages.StringField(1)
  url = _messages.StringField(2)


class GoogleDevtoolsContaineranalysisV1alpha1OperationMetadata(_messages.Message):
  r"""Metadata for all operations used and required for all operations that
  created by Container Analysis Providers

  Fields:
    createTime: Output only. The time this operation was created.
    endTime: Output only. The time that this operation was marked completed or
      failed.
  """

  createTime = _messages.StringField(1)
  endTime = _messages.StringField(2)


class GrafeasV1beta1BuildDetails(_messages.Message):
  r"""Details of a build occurrence.

  Fields:
    inTotoSlsaProvenanceV1: A InTotoSlsaProvenanceV1 attribute.
    provenance: Required. The actual provenance for the build.
    provenanceBytes: Serialized JSON representation of the provenance, used in
      generating the build signature in the corresponding build note. After
      verifying the signature, `provenance_bytes` can be unmarshalled and
      compared to the provenance to confirm that it is unchanged. A
      base64-encoded string representation of the provenance bytes is used for
      the signature in order to interoperate with openssl which expects this
      format for signature verification. The serialized form is captured both
      to avoid ambiguity in how the provenance is marshalled to json as well
      to prevent incompatibilities with future changes.
  """

  inTotoSlsaProvenanceV1 = _messages.MessageField('InTotoSlsaProvenanceV1', 1)
  provenance = _messages.MessageField('BuildProvenance', 2)
  provenanceBytes = _messages.StringField(3)


class GrafeasV1beta1DeploymentDetails(_messages.Message):
  r"""Details of a deployment occurrence.

  Fields:
    deployment: Required. Deployment history for the resource.
  """

  deployment = _messages.MessageField('Deployment', 1)


class GrafeasV1beta1DiscoveryDetails(_messages.Message):
  r"""Details of a discovery occurrence.

  Fields:
    discovered: Required. Analysis status for the discovered resource.
  """

  discovered = _messages.MessageField('Discovered', 1)


class GrafeasV1beta1ImageDetails(_messages.Message):
  r"""Details of an image occurrence.

  Fields:
    derivedImage: Required. Immutable. The child image derived from the base
      image.
  """

  derivedImage = _messages.MessageField('Derived', 1)


class GrafeasV1beta1IntotoArtifact(_messages.Message):
  r"""A GrafeasV1beta1IntotoArtifact object.

  Fields:
    hashes: A ArtifactHashes attribute.
    resourceUri: A string attribute.
  """

  hashes = _messages.MessageField('ArtifactHashes', 1)
  resourceUri = _messages.StringField(2)


class GrafeasV1beta1IntotoDetails(_messages.Message):
  r"""This corresponds to a signed in-toto link - it is made up of one or more
  signatures and the in-toto link itself. This is used for occurrences of a
  Grafeas in-toto note.

  Fields:
    signatures: A GrafeasV1beta1IntotoSignature attribute.
    signed: A Link attribute.
  """

  signatures = _messages.MessageField('GrafeasV1beta1IntotoSignature', 1, repeated=True)
  signed = _messages.MessageField('Link', 2)


class GrafeasV1beta1IntotoSignature(_messages.Message):
  r"""A signature object consists of the KeyID used and the signature itself.

  Fields:
    keyid: A string attribute.
    sig: A string attribute.
  """

  keyid = _messages.StringField(1)
  sig = _messages.StringField(2)


class GrafeasV1beta1PackageDetails(_messages.Message):
  r"""Details of a package occurrence.

  Fields:
    installation: Required. Where the package was installed.
  """

  installation = _messages.MessageField('Installation', 1)


class GrafeasV1beta1VulnerabilityDetails(_messages.Message):
  r"""Details of a vulnerability Occurrence.

  Enums:
    CvssVersionValueValuesEnum: Output only. CVSS version used to populate
      cvss_score and severity.
    EffectiveSeverityValueValuesEnum: The distro assigned severity for this
      vulnerability when it is available, and note provider assigned severity
      when distro has not yet assigned a severity for this vulnerability. When
      there are multiple PackageIssues for this vulnerability, they can have
      different effective severities because some might be provided by the
      distro while others are provided by the language ecosystem for a
      language pack. For this reason, it is advised to use the effective
      severity on the PackageIssue level. In the case where multiple
      PackageIssues have differing effective severities, this field should be
      the highest severity for any of the PackageIssues.
    SeverityValueValuesEnum: Output only. The note provider assigned Severity
      of the vulnerability.

  Fields:
    cvssScore: Output only. The CVSS score of this vulnerability. CVSS score
      is on a scale of 0-10 where 0 indicates low severity and 10 indicates
      high severity.
    cvssV2: The cvss v2 score for the vulnerability.
    cvssV3: The cvss v3 score for the vulnerability.
    cvssVersion: Output only. CVSS version used to populate cvss_score and
      severity.
    effectiveSeverity: The distro assigned severity for this vulnerability
      when it is available, and note provider assigned severity when distro
      has not yet assigned a severity for this vulnerability. When there are
      multiple PackageIssues for this vulnerability, they can have different
      effective severities because some might be provided by the distro while
      others are provided by the language ecosystem for a language pack. For
      this reason, it is advised to use the effective severity on the
      PackageIssue level. In the case where multiple PackageIssues have
      differing effective severities, this field should be the highest
      severity for any of the PackageIssues.
    longDescription: Output only. A detailed description of this
      vulnerability.
    packageIssue: Required. The set of affected locations and their fixes (if
      available) within the associated resource.
    relatedUrls: Output only. URLs related to this vulnerability.
    severity: Output only. The note provider assigned Severity of the
      vulnerability.
    shortDescription: Output only. A one sentence description of this
      vulnerability.
    type: The type of package; whether native or non native(ruby gems, node.js
      packages etc)
    vexAssessment: A VexAssessment attribute.
  """

  class CvssVersionValueValuesEnum(_messages.Enum):
    r"""Output only. CVSS version used to populate cvss_score and severity.

    Values:
      CVSS_VERSION_UNSPECIFIED: <no description>
      CVSS_VERSION_2: <no description>
      CVSS_VERSION_3: <no description>
    """
    CVSS_VERSION_UNSPECIFIED = 0
    CVSS_VERSION_2 = 1
    CVSS_VERSION_3 = 2

  class EffectiveSeverityValueValuesEnum(_messages.Enum):
    r"""The distro assigned severity for this vulnerability when it is
    available, and note provider assigned severity when distro has not yet
    assigned a severity for this vulnerability. When there are multiple
    PackageIssues for this vulnerability, they can have different effective
    severities because some might be provided by the distro while others are
    provided by the language ecosystem for a language pack. For this reason,
    it is advised to use the effective severity on the PackageIssue level. In
    the case where multiple PackageIssues have differing effective severities,
    this field should be the highest severity for any of the PackageIssues.

    Values:
      SEVERITY_UNSPECIFIED: Unknown.
      MINIMAL: Minimal severity.
      LOW: Low severity.
      MEDIUM: Medium severity.
      HIGH: High severity.
      CRITICAL: Critical severity.
    """
    SEVERITY_UNSPECIFIED = 0
    MINIMAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Output only. The note provider assigned Severity of the vulnerability.

    Values:
      SEVERITY_UNSPECIFIED: Unknown.
      MINIMAL: Minimal severity.
      LOW: Low severity.
      MEDIUM: Medium severity.
      HIGH: High severity.
      CRITICAL: Critical severity.
    """
    SEVERITY_UNSPECIFIED = 0
    MINIMAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5

  cvssScore = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  cvssV2 = _messages.MessageField('CVSS', 2)
  cvssV3 = _messages.MessageField('CVSS', 3)
  cvssVersion = _messages.EnumField('CvssVersionValueValuesEnum', 4)
  effectiveSeverity = _messages.EnumField('EffectiveSeverityValueValuesEnum', 5)
  longDescription = _messages.StringField(6)
  packageIssue = _messages.MessageField('PackageIssue', 7, repeated=True)
  relatedUrls = _messages.MessageField('RelatedUrl', 8, repeated=True)
  severity = _messages.EnumField('SeverityValueValuesEnum', 9)
  shortDescription = _messages.StringField(10)
  type = _messages.StringField(11)
  vexAssessment = _messages.MessageField('VexAssessment', 12)


class Hash(_messages.Message):
  r"""Container message for hash values.

  Enums:
    TypeValueValuesEnum: Required. The type of hash that was performed.

  Fields:
    type: Required. The type of hash that was performed.
    value: Required. The hash value.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of hash that was performed.

    Values:
      HASH_TYPE_UNSPECIFIED: Unknown.
      SHA256: A SHA-256 hash.
      GO_MODULE_H1: Dirhash of a Go module's source code which is then hex-
        encoded. See b/244466565 and https://github.com/in-
        toto/attestation/pull/108.
      SHA512: A SHA-512 hash.
    """
    HASH_TYPE_UNSPECIFIED = 0
    SHA256 = 1
    GO_MODULE_H1 = 2
    SHA512 = 3

  type = _messages.EnumField('TypeValueValuesEnum', 1)
  value = _messages.BytesField(2)


class Hint(_messages.Message):
  r"""This submessage provides human-readable hints about the purpose of the
  authority. Because the name of a note acts as its resource reference, it is
  important to disambiguate the canonical name of the Note (which might be a
  UUID for security purposes) from "readable" names more suitable for debug
  output. Note that these hints should not be used to look up authorities in
  security sensitive contexts, such as when looking up attestations to verify.

  Fields:
    humanReadableName: Required. The human readable name of this attestation
      authority, for example "qa".
  """

  humanReadableName = _messages.StringField(1)


class InToto(_messages.Message):
  r"""This contains the fields corresponding to the definition of a software
  supply chain step in an in-toto layout. This information goes into a Grafeas
  note.

  Fields:
    expectedCommand: This field contains the expected command used to perform
      the step.
    expectedMaterials: The following fields contain in-toto artifact rules
      identifying the artifacts that enter this supply chain step, and exit
      the supply chain step, i.e. materials and products of the step.
    expectedProducts: A ArtifactRule attribute.
    signingKeys: This field contains the public keys that can be used to
      verify the signatures on the step metadata.
    stepName: This field identifies the name of the step in the supply chain.
    threshold: This field contains a value that indicates the minimum number
      of keys that need to be used to sign the step's in-toto link.
  """

  expectedCommand = _messages.StringField(1, repeated=True)
  expectedMaterials = _messages.MessageField('ArtifactRule', 2, repeated=True)
  expectedProducts = _messages.MessageField('ArtifactRule', 3, repeated=True)
  signingKeys = _messages.MessageField('SigningKey', 4, repeated=True)
  stepName = _messages.StringField(5)
  threshold = _messages.IntegerField(6)


class InTotoSlsaProvenanceV1(_messages.Message):
  r"""A InTotoSlsaProvenanceV1 object.

  Fields:
    _type: InToto spec defined at https://github.com/in-
      toto/attestation/tree/main/spec#statement
    predicate: A SlsaProvenanceV1 attribute.
    predicateType: A string attribute.
    subject: A Subject attribute.
  """

  _type = _messages.StringField(1)
  predicate = _messages.MessageField('SlsaProvenanceV1', 2)
  predicateType = _messages.StringField(3)
  subject = _messages.MessageField('Subject', 4, repeated=True)


class Installation(_messages.Message):
  r"""This represents how a particular software package may be installed on a
  system.

  Enums:
    ArchitectureValueValuesEnum: Output only. The CPU architecture for which
      packages in this distribution channel were built. Architecture will be
      blank for language packages.

  Fields:
    architecture: Output only. The CPU architecture for which packages in this
      distribution channel were built. Architecture will be blank for language
      packages.
    cpeUri: Output only. The cpe_uri in [CPE
      format](https://cpe.mitre.org/specification/) denoting the package
      manager version distributing a package. The cpe_uri will be blank for
      language packages.
    license: Licenses that have been declared by the authors of the package.
    location: All of the places within the filesystem versions of this package
      have been found.
    name: Required. Output only. The name of the installed package.
    packageType: Output only. The type of package; whether native or non
      native (e.g., ruby gems, node.js packages, etc.).
    version: Output only. The version of the package.
  """

  class ArchitectureValueValuesEnum(_messages.Enum):
    r"""Output only. The CPU architecture for which packages in this
    distribution channel were built. Architecture will be blank for language
    packages.

    Values:
      ARCHITECTURE_UNSPECIFIED: Unknown architecture.
      X86: X86 architecture.
      X64: X64 architecture.
    """
    ARCHITECTURE_UNSPECIFIED = 0
    X86 = 1
    X64 = 2

  architecture = _messages.EnumField('ArchitectureValueValuesEnum', 1)
  cpeUri = _messages.StringField(2)
  license = _messages.MessageField('License', 3)
  location = _messages.MessageField('Location', 4, repeated=True)
  name = _messages.StringField(5)
  packageType = _messages.StringField(6)
  version = _messages.MessageField('Version', 7)


class Justification(_messages.Message):
  r"""Justification provides the justification when the state of the
  assessment if NOT_AFFECTED.

  Enums:
    JustificationTypeValueValuesEnum: The justification type for this
      vulnerability.

  Fields:
    details: Additional details on why this justification was chosen.
    justificationType: The justification type for this vulnerability.
  """

  class JustificationTypeValueValuesEnum(_messages.Enum):
    r"""The justification type for this vulnerability.

    Values:
      JUSTIFICATION_TYPE_UNSPECIFIED: JUSTIFICATION_TYPE_UNSPECIFIED.
      COMPONENT_NOT_PRESENT: The vulnerable component is not present in the
        product.
      VULNERABLE_CODE_NOT_PRESENT: The vulnerable code is not present.
        Typically this case occurs when source code is configured or built in
        a way that excludes the vulnerable code.
      VULNERABLE_CODE_NOT_IN_EXECUTE_PATH: The vulnerable code can not be
        executed. Typically this case occurs when the product includes the
        vulnerable code but does not call or use the vulnerable code.
      VULNERABLE_CODE_CANNOT_BE_CONTROLLED_BY_ADVERSARY: The vulnerable code
        cannot be controlled by an attacker to exploit the vulnerability.
      INLINE_MITIGATIONS_ALREADY_EXIST: The product includes built-in
        protections or features that prevent exploitation of the
        vulnerability. These built-in protections cannot be subverted by the
        attacker and cannot be configured or disabled by the user. These
        mitigations completely prevent exploitation based on known attack
        vectors.
    """
    JUSTIFICATION_TYPE_UNSPECIFIED = 0
    COMPONENT_NOT_PRESENT = 1
    VULNERABLE_CODE_NOT_PRESENT = 2
    VULNERABLE_CODE_NOT_IN_EXECUTE_PATH = 3
    VULNERABLE_CODE_CANNOT_BE_CONTROLLED_BY_ADVERSARY = 4
    INLINE_MITIGATIONS_ALREADY_EXIST = 5

  details = _messages.StringField(1)
  justificationType = _messages.EnumField('JustificationTypeValueValuesEnum', 2)


class KnowledgeBase(_messages.Message):
  r"""A KnowledgeBase object.

  Fields:
    name: The KB name (generally of the form KB[0-9]+ i.e. KB123456).
    url: A link to the KB in the Windows update catalog -
      https://www.catalog.update.microsoft.com/
  """

  name = _messages.StringField(1)
  url = _messages.StringField(2)


class Layer(_messages.Message):
  r"""Layer holds metadata specific to a layer of a Docker image.

  Enums:
    DirectiveValueValuesEnum: Required. The recovered Dockerfile directive
      used to construct this layer.

  Fields:
    arguments: The recovered arguments to the Dockerfile directive.
    directive: Required. The recovered Dockerfile directive used to construct
      this layer.
  """

  class DirectiveValueValuesEnum(_messages.Enum):
    r"""Required. The recovered Dockerfile directive used to construct this
    layer.

    Values:
      DIRECTIVE_UNSPECIFIED: Default value for unsupported/missing directive.
      MAINTAINER: https://docs.docker.com/engine/reference/builder/
      RUN: https://docs.docker.com/engine/reference/builder/
      CMD: https://docs.docker.com/engine/reference/builder/
      LABEL: https://docs.docker.com/engine/reference/builder/
      EXPOSE: https://docs.docker.com/engine/reference/builder/
      ENV: https://docs.docker.com/engine/reference/builder/
      ADD: https://docs.docker.com/engine/reference/builder/
      COPY: https://docs.docker.com/engine/reference/builder/
      ENTRYPOINT: https://docs.docker.com/engine/reference/builder/
      VOLUME: https://docs.docker.com/engine/reference/builder/
      USER: https://docs.docker.com/engine/reference/builder/
      WORKDIR: https://docs.docker.com/engine/reference/builder/
      ARG: https://docs.docker.com/engine/reference/builder/
      ONBUILD: https://docs.docker.com/engine/reference/builder/
      STOPSIGNAL: https://docs.docker.com/engine/reference/builder/
      HEALTHCHECK: https://docs.docker.com/engine/reference/builder/
      SHELL: https://docs.docker.com/engine/reference/builder/
    """
    DIRECTIVE_UNSPECIFIED = 0
    MAINTAINER = 1
    RUN = 2
    CMD = 3
    LABEL = 4
    EXPOSE = 5
    ENV = 6
    ADD = 7
    COPY = 8
    ENTRYPOINT = 9
    VOLUME = 10
    USER = 11
    WORKDIR = 12
    ARG = 13
    ONBUILD = 14
    STOPSIGNAL = 15
    HEALTHCHECK = 16
    SHELL = 17

  arguments = _messages.StringField(1)
  directive = _messages.EnumField('DirectiveValueValuesEnum', 2)


class License(_messages.Message):
  r"""License information.

  Fields:
    comments: Comments
    expression: Often a single license can be used to represent the licensing
      terms. Sometimes it is necessary to include a choice of one or more
      licenses or some combination of license identifiers. Examples:
      "LGPL-2.1-only OR MIT", "LGPL-2.1-only AND MIT", "GPL-2.0-or-later WITH
      Bison-exception-2.2".
  """

  comments = _messages.StringField(1)
  expression = _messages.StringField(2)


class LicensesSummary(_messages.Message):
  r"""Per license count

  Fields:
    count: The number of fixable vulnerabilities associated with this
      resource.
    license: The license of the package. Note that the format of this value is
      not guaranteed. It may be nil, an empty string, a boolean value (A | B),
      a differently formed boolean value (A OR B), etc...
  """

  count = _messages.IntegerField(1)
  license = _messages.StringField(2)


class Link(_messages.Message):
  r"""This corresponds to an in-toto link.

  Fields:
    byproducts: ByProducts are data generated as part of a software supply
      chain step, but are not the actual result of the step.
    command: This field contains the full command executed for the step. This
      can also be empty if links are generated for operations that aren't
      directly mapped to a specific command. Each term in the command is an
      independent string in the list. An example of a command in the in-toto
      metadata field is: "command": ["git", "clone", "https://github.com/in-
      toto/demo-project.git"]
    environment: This is a field that can be used to capture information about
      the environment. It is suggested for this field to contain information
      that details environment variables, filesystem information, and the
      present working directory. The recommended structure of this field is:
      "environment": { "custom_values": { "variables": "", "filesystem": "",
      "workdir": "", "": "..." } }
    materials: Materials are the supply chain artifacts that go into the step
      and are used for the operation performed. The key of the map is the path
      of the artifact and the structure contains the recorded hash
      information. An example is: "materials": [ { "resource_uri": "foo/bar",
      "hashes": { "sha256": "ebebf...", : } } ]
    products: Products are the supply chain artifacts generated as a result of
      the step. The structure is identical to that of materials.
  """

  byproducts = _messages.MessageField('ByProducts', 1)
  command = _messages.StringField(2, repeated=True)
  environment = _messages.MessageField('Environment', 3)
  materials = _messages.MessageField('GrafeasV1beta1IntotoArtifact', 4, repeated=True)
  products = _messages.MessageField('GrafeasV1beta1IntotoArtifact', 5, repeated=True)


class ListNoteOccurrencesResponse(_messages.Message):
  r"""Response for listing occurrences for a note.

  Fields:
    nextPageToken: Token to provide to skip to a particular spot in the list.
    occurrences: The occurrences attached to the specified note.
  """

  nextPageToken = _messages.StringField(1)
  occurrences = _messages.MessageField('Occurrence', 2, repeated=True)


class ListNotesResponse(_messages.Message):
  r"""Response for listing notes.

  Fields:
    nextPageToken: The next pagination token in the list response. It should
      be used as `page_token` for the following request. An empty value means
      no more results.
    notes: The notes requested.
  """

  nextPageToken = _messages.StringField(1)
  notes = _messages.MessageField('Note', 2, repeated=True)


class ListOccurrencesResponse(_messages.Message):
  r"""Response for listing occurrences.

  Fields:
    nextPageToken: The next pagination token in the list response. It should
      be used as `page_token` for the following request. An empty value means
      no more results.
    occurrences: The occurrences requested.
  """

  nextPageToken = _messages.StringField(1)
  occurrences = _messages.MessageField('Occurrence', 2, repeated=True)


class Location(_messages.Message):
  r"""An occurrence of a particular package installation found within a
  system's filesystem. E.g., glibc was found in `/var/lib/dpkg/status`.

  Fields:
    cpeUri: Deprecated. The CPE URI in [CPE
      format](https://cpe.mitre.org/specification/) denoting the package
      manager version distributing a package.
    path: The path from which we gathered that this package/version is
      installed.
    version: Deprecated. The version installed at this location.
  """

  cpeUri = _messages.StringField(1)
  path = _messages.StringField(2)
  version = _messages.MessageField('Version', 3)


class Note(_messages.Message):
  r"""A type of analysis that can be done for a resource.

  Enums:
    KindValueValuesEnum: Output only. The type of analysis. This field can be
      used as a filter in list requests.

  Fields:
    attestationAuthority: A note describing an attestation role.
    baseImage: A note describing a base image.
    build: A note describing build provenance for a verifiable build.
    createTime: Output only. The time this note was created. This field can be
      used as a filter in list requests.
    deployable: A note describing something that can be deployed.
    discovery: A note describing the initial analysis of a resource.
    expirationTime: Time of expiration for this note. Empty if note does not
      expire.
    intoto: A note describing an in-toto link.
    kind: Output only. The type of analysis. This field can be used as a
      filter in list requests.
    longDescription: A detailed description of this note.
    name: Output only. The name of the note in the form of
      `projects/[PROVIDER_ID]/notes/[NOTE_ID]`.
    package: A note describing a package hosted by various package managers.
    relatedNoteNames: Other notes related to this note.
    relatedUrl: URLs associated with this note.
    sbom: A note describing a software bill of materials.
    sbomReference: A note describing an SBOM reference.
    shortDescription: A one sentence description of this note.
    spdxFile: A note describing an SPDX File.
    spdxPackage: A note describing an SPDX Package.
    spdxRelationship: A note describing an SPDX File.
    updateTime: Output only. The time this note was last updated. This field
      can be used as a filter in list requests.
    vulnerability: A note describing a package vulnerability.
    vulnerabilityAssessment: A note describing a vulnerability assessment.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""Output only. The type of analysis. This field can be used as a filter
    in list requests.

    Values:
      NOTE_KIND_UNSPECIFIED: Default value. This value is unused.
      VULNERABILITY: The note and occurrence represent a package
        vulnerability.
      BUILD: The note and occurrence assert build provenance.
      IMAGE: This represents an image basis relationship.
      PACKAGE: This represents a package installed via a package manager.
      DEPLOYMENT: The note and occurrence track deployment events.
      DISCOVERY: The note and occurrence track the initial discovery status of
        a resource.
      ATTESTATION: This represents a logical "role" that can attest to
        artifacts.
      INTOTO: This represents an in-toto link.
      SBOM: This represents a software bill of materials.
      SPDX_PACKAGE: This represents an SPDX Package.
      SPDX_FILE: This represents an SPDX File.
      SPDX_RELATIONSHIP: This represents an SPDX Relationship.
      VULNERABILITY_ASSESSMENT: This represents a Vulnerability Assessment.
      SBOM_REFERENCE: This represents an SBOM Reference.
    """
    NOTE_KIND_UNSPECIFIED = 0
    VULNERABILITY = 1
    BUILD = 2
    IMAGE = 3
    PACKAGE = 4
    DEPLOYMENT = 5
    DISCOVERY = 6
    ATTESTATION = 7
    INTOTO = 8
    SBOM = 9
    SPDX_PACKAGE = 10
    SPDX_FILE = 11
    SPDX_RELATIONSHIP = 12
    VULNERABILITY_ASSESSMENT = 13
    SBOM_REFERENCE = 14

  attestationAuthority = _messages.MessageField('Authority', 1)
  baseImage = _messages.MessageField('Basis', 2)
  build = _messages.MessageField('Build', 3)
  createTime = _messages.StringField(4)
  deployable = _messages.MessageField('Deployable', 5)
  discovery = _messages.MessageField('Discovery', 6)
  expirationTime = _messages.StringField(7)
  intoto = _messages.MessageField('InToto', 8)
  kind = _messages.EnumField('KindValueValuesEnum', 9)
  longDescription = _messages.StringField(10)
  name = _messages.StringField(11)
  package = _messages.MessageField('Package', 12)
  relatedNoteNames = _messages.StringField(13, repeated=True)
  relatedUrl = _messages.MessageField('RelatedUrl', 14, repeated=True)
  sbom = _messages.MessageField('DocumentNote', 15)
  sbomReference = _messages.MessageField('SBOMReferenceNote', 16)
  shortDescription = _messages.StringField(17)
  spdxFile = _messages.MessageField('FileNote', 18)
  spdxPackage = _messages.MessageField('PackageInfoNote', 19)
  spdxRelationship = _messages.MessageField('RelationshipNote', 20)
  updateTime = _messages.StringField(21)
  vulnerability = _messages.MessageField('Vulnerability', 22)
  vulnerabilityAssessment = _messages.MessageField('VulnerabilityAssessmentNote', 23)


class Occurrence(_messages.Message):
  r"""An instance of an analysis type that has been found on a resource.

  Enums:
    KindValueValuesEnum: Output only. This explicitly denotes which of the
      occurrence details are specified. This field can be used as a filter in
      list requests.

  Fields:
    attestation: Describes an attestation of an artifact.
    build: Describes a verifiable build.
    createTime: Output only. The time this occurrence was created.
    deployment: Describes the deployment of an artifact on a runtime.
    derivedImage: Describes how this resource derives from the basis in the
      associated note.
    discovered: Describes when a resource was discovered.
    envelope: https://github.com/secure-systems-lab/dsse
    installation: Describes the installation of a package on the linked
      resource.
    intoto: Describes a specific in-toto link.
    kind: Output only. This explicitly denotes which of the occurrence details
      are specified. This field can be used as a filter in list requests.
    name: Output only. The name of the occurrence in the form of
      `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.
    noteName: Required. Immutable. The analysis note associated with this
      occurrence, in the form of `projects/[PROVIDER_ID]/notes/[NOTE_ID]`.
      This field can be used as a filter in list requests.
    remediation: A description of actions that can be taken to remedy the
      note.
    resource: Required. Immutable. The resource for which the occurrence
      applies.
    sbom: Describes a specific software bill of materials document.
    sbomReference: Describes a specific SBOM reference occurrences.
    spdxFile: Describes a specific SPDX File.
    spdxPackage: Describes a specific SPDX Package.
    spdxRelationship: Describes a specific SPDX Relationship.
    updateTime: Output only. The time this occurrence was last updated.
    vulnerability: Describes a security vulnerability.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""Output only. This explicitly denotes which of the occurrence details
    are specified. This field can be used as a filter in list requests.

    Values:
      NOTE_KIND_UNSPECIFIED: Default value. This value is unused.
      VULNERABILITY: The note and occurrence represent a package
        vulnerability.
      BUILD: The note and occurrence assert build provenance.
      IMAGE: This represents an image basis relationship.
      PACKAGE: This represents a package installed via a package manager.
      DEPLOYMENT: The note and occurrence track deployment events.
      DISCOVERY: The note and occurrence track the initial discovery status of
        a resource.
      ATTESTATION: This represents a logical "role" that can attest to
        artifacts.
      INTOTO: This represents an in-toto link.
      SBOM: This represents a software bill of materials.
      SPDX_PACKAGE: This represents an SPDX Package.
      SPDX_FILE: This represents an SPDX File.
      SPDX_RELATIONSHIP: This represents an SPDX Relationship.
      VULNERABILITY_ASSESSMENT: This represents a Vulnerability Assessment.
      SBOM_REFERENCE: This represents an SBOM Reference.
    """
    NOTE_KIND_UNSPECIFIED = 0
    VULNERABILITY = 1
    BUILD = 2
    IMAGE = 3
    PACKAGE = 4
    DEPLOYMENT = 5
    DISCOVERY = 6
    ATTESTATION = 7
    INTOTO = 8
    SBOM = 9
    SPDX_PACKAGE = 10
    SPDX_FILE = 11
    SPDX_RELATIONSHIP = 12
    VULNERABILITY_ASSESSMENT = 13
    SBOM_REFERENCE = 14

  attestation = _messages.MessageField('Details', 1)
  build = _messages.MessageField('GrafeasV1beta1BuildDetails', 2)
  createTime = _messages.StringField(3)
  deployment = _messages.MessageField('GrafeasV1beta1DeploymentDetails', 4)
  derivedImage = _messages.MessageField('GrafeasV1beta1ImageDetails', 5)
  discovered = _messages.MessageField('GrafeasV1beta1DiscoveryDetails', 6)
  envelope = _messages.MessageField('Envelope', 7)
  installation = _messages.MessageField('GrafeasV1beta1PackageDetails', 8)
  intoto = _messages.MessageField('GrafeasV1beta1IntotoDetails', 9)
  kind = _messages.EnumField('KindValueValuesEnum', 10)
  name = _messages.StringField(11)
  noteName = _messages.StringField(12)
  remediation = _messages.StringField(13)
  resource = _messages.MessageField('Resource', 14)
  sbom = _messages.MessageField('DocumentOccurrence', 15)
  sbomReference = _messages.MessageField('SBOMReferenceOccurrence', 16)
  spdxFile = _messages.MessageField('FileOccurrence', 17)
  spdxPackage = _messages.MessageField('PackageInfoOccurrence', 18)
  spdxRelationship = _messages.MessageField('RelationshipOccurrence', 19)
  updateTime = _messages.StringField(20)
  vulnerability = _messages.MessageField('GrafeasV1beta1VulnerabilityDetails', 21)


class Package(_messages.Message):
  r"""Package represents a particular package version.

  Enums:
    ArchitectureValueValuesEnum: The CPU architecture for which packages in
      this distribution channel were built. Architecture will be blank for
      language packages.

  Fields:
    architecture: The CPU architecture for which packages in this distribution
      channel were built. Architecture will be blank for language packages.
    cpeUri: The cpe_uri in [CPE format](https://cpe.mitre.org/specification/)
      denoting the package manager version distributing a package. The cpe_uri
      will be blank for language packages.
    description: The description of this package.
    digest: Hash value, typically a file digest, that allows unique
      identification a specific package.
    distribution: The various channels by which a package is distributed.
    license: Licenses that have been declared by the authors of the package.
    maintainer: A freeform text denoting the maintainer of this package.
    name: Required. Immutable. The name of the package.
    packageType: The type of package; whether native or non native (e.g., ruby
      gems, node.js packages, etc.).
    url: The homepage for this package.
    version: The version of the package.
  """

  class ArchitectureValueValuesEnum(_messages.Enum):
    r"""The CPU architecture for which packages in this distribution channel
    were built. Architecture will be blank for language packages.

    Values:
      ARCHITECTURE_UNSPECIFIED: Unknown architecture.
      X86: X86 architecture.
      X64: X64 architecture.
    """
    ARCHITECTURE_UNSPECIFIED = 0
    X86 = 1
    X64 = 2

  architecture = _messages.EnumField('ArchitectureValueValuesEnum', 1)
  cpeUri = _messages.StringField(2)
  description = _messages.StringField(3)
  digest = _messages.MessageField('Digest', 4, repeated=True)
  distribution = _messages.MessageField('Distribution', 5, repeated=True)
  license = _messages.MessageField('License', 6)
  maintainer = _messages.StringField(7)
  name = _messages.StringField(8)
  packageType = _messages.StringField(9)
  url = _messages.StringField(10)
  version = _messages.MessageField('Version', 11)


class PackageInfoNote(_messages.Message):
  r"""PackageInfoNote represents an SPDX Package Information section:
  https://spdx.github.io/spdx-spec/3-package-information/

  Fields:
    analyzed: Indicates whether the file content of this package has been
      available for or subjected to analysis when creating the SPDX document
    attribution: A place for the SPDX data creator to record, at the package
      level, acknowledgements that may be needed to be communicated in some
      contexts
    checksum: Provide an independently reproducible mechanism that permits
      unique identification of a specific package that correlates to the data
      in this SPDX file
    copyright: Identify the copyright holders of the package, as well as any
      dates present
    detailedDescription: A more detailed description of the package
    downloadLocation: This section identifies the download Universal Resource
      Locator (URL), or a specific location within a version control system
      (VCS) for the package at the time that the SPDX file was created
    externalRefs: ExternalRef
    filesLicenseInfo: Contain the license the SPDX file creator has concluded
      as governing the This field is to contain a list of all licenses found
      in the package. The relationship between licenses (i.e., conjunctive,
      disjunctive) is not specified in this field \u2013 it is simply a
      listing of all licenses found
    homePage: Provide a place for the SPDX file creator to record a web site
      that serves as the package's home page
    licenseDeclared: List the licenses that have been declared by the authors
      of the package
    originator: If the package identified in the SPDX file originated from a
      different person or organization than identified as Package Supplier,
      this field identifies from where or whom the package originally came
    packageType: The type of package: OS, MAVEN, GO, GO_STDLIB, etc.
    summaryDescription: A short description of the package
    supplier: Identify the actual distribution source for the
      package/directory identified in the SPDX file
    title: Identify the full name of the package as given by the Package
      Originator
    verificationCode: This field provides an independently reproducible
      mechanism identifying specific contents of a package based on the actual
      files (except the SPDX file itself, if it is included in the package)
      that make up each package and that correlates to the data in this SPDX
      file
    version: Identify the version of the package
  """

  analyzed = _messages.BooleanField(1)
  attribution = _messages.StringField(2)
  checksum = _messages.StringField(3)
  copyright = _messages.StringField(4)
  detailedDescription = _messages.StringField(5)
  downloadLocation = _messages.StringField(6)
  externalRefs = _messages.MessageField('ExternalRef', 7, repeated=True)
  filesLicenseInfo = _messages.StringField(8, repeated=True)
  homePage = _messages.StringField(9)
  licenseDeclared = _messages.MessageField('License', 10)
  originator = _messages.StringField(11)
  packageType = _messages.StringField(12)
  summaryDescription = _messages.StringField(13)
  supplier = _messages.StringField(14)
  title = _messages.StringField(15)
  verificationCode = _messages.StringField(16)
  version = _messages.StringField(17)


class PackageInfoOccurrence(_messages.Message):
  r"""PackageInfoOccurrence represents an SPDX Package Information section:
  https://spdx.github.io/spdx-spec/3-package-information/

  Fields:
    comment: A place for the SPDX file creator to record any general comments
      about the package being described
    filename: Provide the actual file name of the package, or path of the
      directory being treated as a package
    homePage: Output only. Provide a place for the SPDX file creator to record
      a web site that serves as the package's home page
    id: Uniquely identify any element in an SPDX document which may be
      referenced by other elements
    licenseConcluded: package or alternative values, if the governing license
      cannot be determined
    packageType: Output only. The type of package: OS, MAVEN, GO, GO_STDLIB,
      etc.
    sourceInfo: Provide a place for the SPDX file creator to record any
      relevant background information or additional comments about the origin
      of the package
    summaryDescription: Output only. A short description of the package
    title: Output only. Identify the full name of the package as given by the
      Package Originator
    version: Output only. Identify the version of the package
  """

  comment = _messages.StringField(1)
  filename = _messages.StringField(2)
  homePage = _messages.StringField(3)
  id = _messages.StringField(4)
  licenseConcluded = _messages.MessageField('License', 5)
  packageType = _messages.StringField(6)
  sourceInfo = _messages.StringField(7)
  summaryDescription = _messages.StringField(8)
  title = _messages.StringField(9)
  version = _messages.StringField(10)


class PackageIssue(_messages.Message):
  r"""This message wraps a location affected by a vulnerability and its
  associated fix (if one is available).

  Enums:
    EffectiveSeverityValueValuesEnum: Output only. The distro or language
      system assigned severity for this vulnerability when that is available
      and note provider assigned severity when it is not available.

  Fields:
    affectedLocation: Required. The location of the vulnerability.
    effectiveSeverity: Output only. The distro or language system assigned
      severity for this vulnerability when that is available and note provider
      assigned severity when it is not available.
    fixedLocation: The location of the available fix for vulnerability.
    packageType: The type of package (e.g. OS, MAVEN, GO).
    severityName: Deprecated, use Details.effective_severity instead The
      severity (e.g., distro assigned severity) for this vulnerability.
  """

  class EffectiveSeverityValueValuesEnum(_messages.Enum):
    r"""Output only. The distro or language system assigned severity for this
    vulnerability when that is available and note provider assigned severity
    when it is not available.

    Values:
      SEVERITY_UNSPECIFIED: Unknown.
      MINIMAL: Minimal severity.
      LOW: Low severity.
      MEDIUM: Medium severity.
      HIGH: High severity.
      CRITICAL: Critical severity.
    """
    SEVERITY_UNSPECIFIED = 0
    MINIMAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5

  affectedLocation = _messages.MessageField('VulnerabilityLocation', 1)
  effectiveSeverity = _messages.EnumField('EffectiveSeverityValueValuesEnum', 2)
  fixedLocation = _messages.MessageField('VulnerabilityLocation', 3)
  packageType = _messages.StringField(4)
  severityName = _messages.StringField(5)


class PackagesSummaryResponse(_messages.Message):
  r"""A summary of the packages found within the given resource.

  Fields:
    licensesSummary: A listing by license name of each of the licenses and
      their counts.
    resourceUrl: The unique URL of the image or the container for which this
      summary applies.
  """

  licensesSummary = _messages.MessageField('LicensesSummary', 1, repeated=True)
  resourceUrl = _messages.StringField(2)


class PgpSignedAttestation(_messages.Message):
  r"""An attestation wrapper with a PGP-compatible signature. This message
  only supports `ATTACHED` signatures, where the payload that is signed is
  included alongside the signature itself in the same file.

  Enums:
    ContentTypeValueValuesEnum: Type (for example schema) of the attestation
      payload that was signed. The verifier must ensure that the provided type
      is one that the verifier supports, and that the attestation payload is a
      valid instantiation of that type (for example by validating a JSON
      schema).

  Fields:
    contentType: Type (for example schema) of the attestation payload that was
      signed. The verifier must ensure that the provided type is one that the
      verifier supports, and that the attestation payload is a valid
      instantiation of that type (for example by validating a JSON schema).
    pgpKeyId: The cryptographic fingerprint of the key used to generate the
      signature, as output by, e.g. `gpg --list-keys`. This should be the
      version 4, full 160-bit fingerprint, expressed as a 40 character
      hexadecimal string. See https://tools.ietf.org/html/rfc4880#section-12.2
      for details. Implementations may choose to acknowledge "LONG", "SHORT",
      or other abbreviated key IDs, but only the full fingerprint is
      guaranteed to work. In gpg, the full fingerprint can be retrieved from
      the `fpr` field returned when calling --list-keys with --with-colons.
      For example: ``` gpg --with-colons --with-fingerprint --force-v4-certs \
      --list-keys <EMAIL> tru::1:1513631572:0:3:1:5 pub:......
      fpr:::::::::24FF6481B76AC91E66A00AC657A93A81EF3AE6FB: ``` Above, the
      fingerprint is `24FF6481B76AC91E66A00AC657A93A81EF3AE6FB`.
    signature: Required. The raw content of the signature, as output by GNU
      Privacy Guard (GPG) or equivalent. Since this message only supports
      attached signatures, the payload that was signed must be attached. While
      the signature format supported is dependent on the verification
      implementation, currently only ASCII-armored (`--armor` to gpg), non-
      clearsigned (`--sign` rather than `--clearsign` to gpg) are supported.
      Concretely, `gpg --sign --armor --output=signature.gpg payload.json`
      will create the signature content expected in this field in
      `signature.gpg` for the `payload.json` attestation payload.
  """

  class ContentTypeValueValuesEnum(_messages.Enum):
    r"""Type (for example schema) of the attestation payload that was signed.
    The verifier must ensure that the provided type is one that the verifier
    supports, and that the attestation payload is a valid instantiation of
    that type (for example by validating a JSON schema).

    Values:
      CONTENT_TYPE_UNSPECIFIED: `ContentType` is not set.
      SIMPLE_SIGNING_JSON: Atomic format attestation signature. See https://gi
        thub.com/containers/image/blob/8a5d2f82a6e3263290c8e0276c3e0f64e77723e
        7/docs/atomic-signature.md The payload extracted from `signature` is a
        JSON blob conforming to the linked schema.
    """
    CONTENT_TYPE_UNSPECIFIED = 0
    SIMPLE_SIGNING_JSON = 1

  contentType = _messages.EnumField('ContentTypeValueValuesEnum', 1)
  pgpKeyId = _messages.StringField(2)
  signature = _messages.StringField(3)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  etag = _messages.BytesField(2)
  version = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class Product(_messages.Message):
  r"""Product contains information about a product and how to uniquely
  identify it.

  Fields:
    genericUri: Contains a URI which is vendor-specific. Example: The artifact
      repository URL of an image.
    id: Token that identifies a product so that it can be referred to from
      other parts in the document. There is no predefined format as long as it
      uniquely identifies a group in the context of the current document.
    name: Name of the product.
  """

  genericUri = _messages.StringField(1)
  id = _messages.StringField(2)
  name = _messages.StringField(3)


class ProjectRepoId(_messages.Message):
  r"""Selects a repo using a Google Cloud Platform project ID (e.g., winged-
  cargo-31) and a repo name within that project.

  Fields:
    projectId: The ID of the project.
    repoName: The name of the repo. Leave empty for the default repo.
  """

  projectId = _messages.StringField(1)
  repoName = _messages.StringField(2)


class ProvenanceBuilder(_messages.Message):
  r"""A ProvenanceBuilder object.

  Messages:
    VersionValue: A VersionValue object.

  Fields:
    builderDependencies: A ResourceDescriptor attribute.
    id: A string attribute.
    version: A VersionValue attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class VersionValue(_messages.Message):
    r"""A VersionValue object.

    Messages:
      AdditionalProperty: An additional property for a VersionValue object.

    Fields:
      additionalProperties: Additional properties of type VersionValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a VersionValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  builderDependencies = _messages.MessageField('ResourceDescriptor', 1, repeated=True)
  id = _messages.StringField(2)
  version = _messages.MessageField('VersionValue', 3)


class Publisher(_messages.Message):
  r"""Publisher contains information about the publisher of this Note.

  Fields:
    issuingAuthority: Provides information about the authority of the issuing
      party to release the document, in particular, the party's constituency
      and responsibilities or other obligations.
    name: Name of the publisher. Examples: 'Google', 'Google Cloud Platform'.
    publisherNamespace: The context or namespace. Contains a URL which is
      under control of the issuing party and can be used as a globally unique
      identifier for that issuing party. Example: https://csaf.io
  """

  issuingAuthority = _messages.StringField(1)
  name = _messages.StringField(2)
  publisherNamespace = _messages.StringField(3)


class RelatedUrl(_messages.Message):
  r"""Metadata for any related URL information.

  Fields:
    label: Label to describe usage of the URL.
    url: Specific URL associated with the resource.
  """

  label = _messages.StringField(1)
  url = _messages.StringField(2)


class RelationshipNote(_messages.Message):
  r"""RelationshipNote represents an SPDX Relationship section:
  https://spdx.github.io/spdx-spec/7-relationships-between-SPDX-elements/

  Enums:
    TypeValueValuesEnum: The type of relationship between the source and
      target SPDX elements

  Fields:
    type: The type of relationship between the source and target SPDX elements
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of relationship between the source and target SPDX elements

    Values:
      RELATIONSHIP_TYPE_UNSPECIFIED: Unspecified
      DESCRIBES: Is to be used when SPDXRef-DOCUMENT describes SPDXRef-A
      DESCRIBED_BY: Is to be used when SPDXRef-A is described by SPDXREF-
        Document
      CONTAINS: Is to be used when SPDXRef-A contains SPDXRef-B
      CONTAINED_BY: Is to be used when SPDXRef-A is contained by SPDXRef-B
      DEPENDS_ON: Is to be used when SPDXRef-A depends on SPDXRef-B
      DEPENDENCY_OF: Is to be used when SPDXRef-A is dependency of SPDXRef-B
      DEPENDENCY_MANIFEST_OF: Is to be used when SPDXRef-A is a manifest file
        that lists a set of dependencies for SPDXRef-B
      BUILD_DEPENDENCY_OF: Is to be used when SPDXRef-A is a build dependency
        of SPDXRef-B
      DEV_DEPENDENCY_OF: Is to be used when SPDXRef-A is a development
        dependency of SPDXRef-B
      OPTIONAL_DEPENDENCY_OF: Is to be used when SPDXRef-A is an optional
        dependency of SPDXRef-B
      PROVIDED_DEPENDENCY_OF: Is to be used when SPDXRef-A is a to be provided
        dependency of SPDXRef-B
      TEST_DEPENDENCY_OF: Is to be used when SPDXRef-A is a test dependency of
        SPDXRef-B
      RUNTIME_DEPENDENCY_OF: Is to be used when SPDXRef-A is a dependency
        required for the execution of SPDXRef-B
      EXAMPLE_OF: Is to be used when SPDXRef-A is an example of SPDXRef-B
      GENERATES: Is to be used when SPDXRef-A generates SPDXRef-B
      GENERATED_FROM: Is to be used when SPDXRef-A was generated from
        SPDXRef-B
      ANCESTOR_OF: Is to be used when SPDXRef-A is an ancestor (same lineage
        but pre-dates) SPDXRef-B
      DESCENDANT_OF: Is to be used when SPDXRef-A is a descendant of (same
        lineage but postdates) SPDXRef-B
      VARIANT_OF: Is to be used when SPDXRef-A is a variant of (same lineage
        but not clear which came first) SPDXRef-B
      DISTRIBUTION_ARTIFACT: Is to be used when distributing SPDXRef-A
        requires that SPDXRef-B also be distributed
      PATCH_FOR: Is to be used when SPDXRef-A is a patch file for (to be
        applied to) SPDXRef-B
      PATCH_APPLIED: Is to be used when SPDXRef-A is a patch file that has
        been applied to SPDXRef-B
      COPY_OF: Is to be used when SPDXRef-A is an exact copy of SPDXRef-B
      FILE_ADDED: Is to be used when SPDXRef-A is a file that was added to
        SPDXRef-B
      FILE_DELETED: Is to be used when SPDXRef-A is a file that was deleted
        from SPDXRef-B
      FILE_MODIFIED: Is to be used when SPDXRef-A is a file that was modified
        from SPDXRef-B
      EXPANDED_FROM_ARCHIVE: Is to be used when SPDXRef-A is expanded from the
        archive SPDXRef-B
      DYNAMIC_LINK: Is to be used when SPDXRef-A dynamically links to
        SPDXRef-B
      STATIC_LINK: Is to be used when SPDXRef-A statically links to SPDXRef-B
      DATA_FILE_OF: Is to be used when SPDXRef-A is a data file used in
        SPDXRef-B
      TEST_CASE_OF: Is to be used when SPDXRef-A is a test case used in
        testing SPDXRef-B
      BUILD_TOOL_OF: Is to be used when SPDXRef-A is used to build SPDXRef-B
      DEV_TOOL_OF: Is to be used when SPDXRef-A is used as a development tool
        for SPDXRef-B
      TEST_OF: Is to be used when SPDXRef-A is used for testing SPDXRef-B
      TEST_TOOL_OF: Is to be used when SPDXRef-A is used as a test tool for
        SPDXRef-B
      DOCUMENTATION_OF: Is to be used when SPDXRef-A provides documentation of
        SPDXRef-B
      OPTIONAL_COMPONENT_OF: Is to be used when SPDXRef-A is an optional
        component of SPDXRef-B
      METAFILE_OF: Is to be used when SPDXRef-A is a metafile of SPDXRef-B
      PACKAGE_OF: Is to be used when SPDXRef-A is used as a package as part of
        SPDXRef-B
      AMENDS: Is to be used when (current) SPDXRef-DOCUMENT amends the SPDX
        information in SPDXRef-B
      PREREQUISITE_FOR: Is to be used when SPDXRef-A is a prerequisite for
        SPDXRef-B
      HAS_PREREQUISITE: Is to be used when SPDXRef-A has as a prerequisite
        SPDXRef-B
      OTHER: Is to be used for a relationship which has not been defined in
        the formal SPDX specification. A description of the relationship
        should be included in the Relationship comments field
    """
    RELATIONSHIP_TYPE_UNSPECIFIED = 0
    DESCRIBES = 1
    DESCRIBED_BY = 2
    CONTAINS = 3
    CONTAINED_BY = 4
    DEPENDS_ON = 5
    DEPENDENCY_OF = 6
    DEPENDENCY_MANIFEST_OF = 7
    BUILD_DEPENDENCY_OF = 8
    DEV_DEPENDENCY_OF = 9
    OPTIONAL_DEPENDENCY_OF = 10
    PROVIDED_DEPENDENCY_OF = 11
    TEST_DEPENDENCY_OF = 12
    RUNTIME_DEPENDENCY_OF = 13
    EXAMPLE_OF = 14
    GENERATES = 15
    GENERATED_FROM = 16
    ANCESTOR_OF = 17
    DESCENDANT_OF = 18
    VARIANT_OF = 19
    DISTRIBUTION_ARTIFACT = 20
    PATCH_FOR = 21
    PATCH_APPLIED = 22
    COPY_OF = 23
    FILE_ADDED = 24
    FILE_DELETED = 25
    FILE_MODIFIED = 26
    EXPANDED_FROM_ARCHIVE = 27
    DYNAMIC_LINK = 28
    STATIC_LINK = 29
    DATA_FILE_OF = 30
    TEST_CASE_OF = 31
    BUILD_TOOL_OF = 32
    DEV_TOOL_OF = 33
    TEST_OF = 34
    TEST_TOOL_OF = 35
    DOCUMENTATION_OF = 36
    OPTIONAL_COMPONENT_OF = 37
    METAFILE_OF = 38
    PACKAGE_OF = 39
    AMENDS = 40
    PREREQUISITE_FOR = 41
    HAS_PREREQUISITE = 42
    OTHER = 43

  type = _messages.EnumField('TypeValueValuesEnum', 1)


class RelationshipOccurrence(_messages.Message):
  r"""RelationshipOccurrence represents an SPDX Relationship section:
  https://spdx.github.io/spdx-spec/7-relationships-between-SPDX-elements/

  Enums:
    TypeValueValuesEnum: Output only. The type of relationship between the
      source and target SPDX elements

  Fields:
    comment: A place for the SPDX file creator to record any general comments
      about the relationship
    source: Also referred to as SPDXRef-A The source SPDX element (file,
      package, etc)
    target: Also referred to as SPDXRef-B The target SPDC element (file,
      package, etc) In cases where there are "known unknowns", the use of the
      keyword NOASSERTION can be used The keywords NONE can be used to
      indicate that an SPDX element (package/file/snippet) has no other
      elements connected by some relationship to it
    type: Output only. The type of relationship between the source and target
      SPDX elements
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of relationship between the source and target
    SPDX elements

    Values:
      RELATIONSHIP_TYPE_UNSPECIFIED: Unspecified
      DESCRIBES: Is to be used when SPDXRef-DOCUMENT describes SPDXRef-A
      DESCRIBED_BY: Is to be used when SPDXRef-A is described by SPDXREF-
        Document
      CONTAINS: Is to be used when SPDXRef-A contains SPDXRef-B
      CONTAINED_BY: Is to be used when SPDXRef-A is contained by SPDXRef-B
      DEPENDS_ON: Is to be used when SPDXRef-A depends on SPDXRef-B
      DEPENDENCY_OF: Is to be used when SPDXRef-A is dependency of SPDXRef-B
      DEPENDENCY_MANIFEST_OF: Is to be used when SPDXRef-A is a manifest file
        that lists a set of dependencies for SPDXRef-B
      BUILD_DEPENDENCY_OF: Is to be used when SPDXRef-A is a build dependency
        of SPDXRef-B
      DEV_DEPENDENCY_OF: Is to be used when SPDXRef-A is a development
        dependency of SPDXRef-B
      OPTIONAL_DEPENDENCY_OF: Is to be used when SPDXRef-A is an optional
        dependency of SPDXRef-B
      PROVIDED_DEPENDENCY_OF: Is to be used when SPDXRef-A is a to be provided
        dependency of SPDXRef-B
      TEST_DEPENDENCY_OF: Is to be used when SPDXRef-A is a test dependency of
        SPDXRef-B
      RUNTIME_DEPENDENCY_OF: Is to be used when SPDXRef-A is a dependency
        required for the execution of SPDXRef-B
      EXAMPLE_OF: Is to be used when SPDXRef-A is an example of SPDXRef-B
      GENERATES: Is to be used when SPDXRef-A generates SPDXRef-B
      GENERATED_FROM: Is to be used when SPDXRef-A was generated from
        SPDXRef-B
      ANCESTOR_OF: Is to be used when SPDXRef-A is an ancestor (same lineage
        but pre-dates) SPDXRef-B
      DESCENDANT_OF: Is to be used when SPDXRef-A is a descendant of (same
        lineage but postdates) SPDXRef-B
      VARIANT_OF: Is to be used when SPDXRef-A is a variant of (same lineage
        but not clear which came first) SPDXRef-B
      DISTRIBUTION_ARTIFACT: Is to be used when distributing SPDXRef-A
        requires that SPDXRef-B also be distributed
      PATCH_FOR: Is to be used when SPDXRef-A is a patch file for (to be
        applied to) SPDXRef-B
      PATCH_APPLIED: Is to be used when SPDXRef-A is a patch file that has
        been applied to SPDXRef-B
      COPY_OF: Is to be used when SPDXRef-A is an exact copy of SPDXRef-B
      FILE_ADDED: Is to be used when SPDXRef-A is a file that was added to
        SPDXRef-B
      FILE_DELETED: Is to be used when SPDXRef-A is a file that was deleted
        from SPDXRef-B
      FILE_MODIFIED: Is to be used when SPDXRef-A is a file that was modified
        from SPDXRef-B
      EXPANDED_FROM_ARCHIVE: Is to be used when SPDXRef-A is expanded from the
        archive SPDXRef-B
      DYNAMIC_LINK: Is to be used when SPDXRef-A dynamically links to
        SPDXRef-B
      STATIC_LINK: Is to be used when SPDXRef-A statically links to SPDXRef-B
      DATA_FILE_OF: Is to be used when SPDXRef-A is a data file used in
        SPDXRef-B
      TEST_CASE_OF: Is to be used when SPDXRef-A is a test case used in
        testing SPDXRef-B
      BUILD_TOOL_OF: Is to be used when SPDXRef-A is used to build SPDXRef-B
      DEV_TOOL_OF: Is to be used when SPDXRef-A is used as a development tool
        for SPDXRef-B
      TEST_OF: Is to be used when SPDXRef-A is used for testing SPDXRef-B
      TEST_TOOL_OF: Is to be used when SPDXRef-A is used as a test tool for
        SPDXRef-B
      DOCUMENTATION_OF: Is to be used when SPDXRef-A provides documentation of
        SPDXRef-B
      OPTIONAL_COMPONENT_OF: Is to be used when SPDXRef-A is an optional
        component of SPDXRef-B
      METAFILE_OF: Is to be used when SPDXRef-A is a metafile of SPDXRef-B
      PACKAGE_OF: Is to be used when SPDXRef-A is used as a package as part of
        SPDXRef-B
      AMENDS: Is to be used when (current) SPDXRef-DOCUMENT amends the SPDX
        information in SPDXRef-B
      PREREQUISITE_FOR: Is to be used when SPDXRef-A is a prerequisite for
        SPDXRef-B
      HAS_PREREQUISITE: Is to be used when SPDXRef-A has as a prerequisite
        SPDXRef-B
      OTHER: Is to be used for a relationship which has not been defined in
        the formal SPDX specification. A description of the relationship
        should be included in the Relationship comments field
    """
    RELATIONSHIP_TYPE_UNSPECIFIED = 0
    DESCRIBES = 1
    DESCRIBED_BY = 2
    CONTAINS = 3
    CONTAINED_BY = 4
    DEPENDS_ON = 5
    DEPENDENCY_OF = 6
    DEPENDENCY_MANIFEST_OF = 7
    BUILD_DEPENDENCY_OF = 8
    DEV_DEPENDENCY_OF = 9
    OPTIONAL_DEPENDENCY_OF = 10
    PROVIDED_DEPENDENCY_OF = 11
    TEST_DEPENDENCY_OF = 12
    RUNTIME_DEPENDENCY_OF = 13
    EXAMPLE_OF = 14
    GENERATES = 15
    GENERATED_FROM = 16
    ANCESTOR_OF = 17
    DESCENDANT_OF = 18
    VARIANT_OF = 19
    DISTRIBUTION_ARTIFACT = 20
    PATCH_FOR = 21
    PATCH_APPLIED = 22
    COPY_OF = 23
    FILE_ADDED = 24
    FILE_DELETED = 25
    FILE_MODIFIED = 26
    EXPANDED_FROM_ARCHIVE = 27
    DYNAMIC_LINK = 28
    STATIC_LINK = 29
    DATA_FILE_OF = 30
    TEST_CASE_OF = 31
    BUILD_TOOL_OF = 32
    DEV_TOOL_OF = 33
    TEST_OF = 34
    TEST_TOOL_OF = 35
    DOCUMENTATION_OF = 36
    OPTIONAL_COMPONENT_OF = 37
    METAFILE_OF = 38
    PACKAGE_OF = 39
    AMENDS = 40
    PREREQUISITE_FOR = 41
    HAS_PREREQUISITE = 42
    OTHER = 43

  comment = _messages.StringField(1)
  source = _messages.StringField(2)
  target = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class Remediation(_messages.Message):
  r"""Specifies details on how to handle (and presumably, fix) a
  vulnerability.

  Enums:
    RemediationTypeValueValuesEnum: The type of remediation that can be
      applied.

  Fields:
    details: Contains a comprehensive human-readable discussion of the
      remediation.
    remediationType: The type of remediation that can be applied.
    remediationUri: Contains the URL where to obtain the remediation.
  """

  class RemediationTypeValueValuesEnum(_messages.Enum):
    r"""The type of remediation that can be applied.

    Values:
      REMEDIATION_TYPE_UNSPECIFIED: No remediation type specified.
      MITIGATION: A MITIGATION is available.
      NO_FIX_PLANNED: No fix is planned.
      NONE_AVAILABLE: Not available.
      VENDOR_FIX: A vendor fix is available.
      WORKAROUND: A workaround is available.
    """
    REMEDIATION_TYPE_UNSPECIFIED = 0
    MITIGATION = 1
    NO_FIX_PLANNED = 2
    NONE_AVAILABLE = 3
    VENDOR_FIX = 4
    WORKAROUND = 5

  details = _messages.StringField(1)
  remediationType = _messages.EnumField('RemediationTypeValueValuesEnum', 2)
  remediationUri = _messages.MessageField('RelatedUrl', 3)


class RepoId(_messages.Message):
  r"""A unique identifier for a Cloud Repo.

  Fields:
    projectRepoId: A combination of a project ID and a repo name.
    uid: A server-assigned, globally unique identifier.
  """

  projectRepoId = _messages.MessageField('ProjectRepoId', 1)
  uid = _messages.StringField(2)


class Resource(_messages.Message):
  r"""An entity that can have metadata. For example, a Docker image.

  Fields:
    contentHash: Deprecated, do not use. Use uri instead. The hash of the
      resource content. For example, the Docker digest.
    name: Deprecated, do not use. Use uri instead. The name of the resource.
      For example, the name of a Docker image - "Debian".
    uri: Required. The unique URI of the resource. For example,
      `https://gcr.io/project/image@sha256:foo` for a Docker image.
  """

  contentHash = _messages.MessageField('Hash', 1)
  name = _messages.StringField(2)
  uri = _messages.StringField(3)


class ResourceDescriptor(_messages.Message):
  r"""A ResourceDescriptor object.

  Messages:
    AnnotationsValue: A AnnotationsValue object.
    DigestValue: A DigestValue object.

  Fields:
    annotations: A AnnotationsValue attribute.
    content: A byte attribute.
    digest: A DigestValue attribute.
    downloadLocation: A string attribute.
    mediaType: A string attribute.
    name: A string attribute.
    uri: A string attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""A AnnotationsValue object.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DigestValue(_messages.Message):
    r"""A DigestValue object.

    Messages:
      AdditionalProperty: An additional property for a DigestValue object.

    Fields:
      additionalProperties: Additional properties of type DigestValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DigestValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  content = _messages.BytesField(2)
  digest = _messages.MessageField('DigestValue', 3)
  downloadLocation = _messages.StringField(4)
  mediaType = _messages.StringField(5)
  name = _messages.StringField(6)
  uri = _messages.StringField(7)


class RunDetails(_messages.Message):
  r"""A RunDetails object.

  Fields:
    builder: A ProvenanceBuilder attribute.
    byproducts: A ResourceDescriptor attribute.
    metadata: A BuildMetadata attribute.
  """

  builder = _messages.MessageField('ProvenanceBuilder', 1)
  byproducts = _messages.MessageField('ResourceDescriptor', 2, repeated=True)
  metadata = _messages.MessageField('BuildMetadata', 3)


class SBOMReferenceNote(_messages.Message):
  r"""The note representing an SBOM reference.

  Fields:
    format: The format that SBOM takes. E.g. may be spdx, cyclonedx, etc...
    version: The version of the format that the SBOM takes. E.g. if the format
      is spdx, the version may be 2.3.
  """

  format = _messages.StringField(1)
  version = _messages.StringField(2)


class SBOMReferenceOccurrence(_messages.Message):
  r"""The occurrence representing an SBOM reference as applied to a specific
  resource. The occurrence follows the DSSE specification. See
  https://github.com/secure-systems-lab/dsse/blob/master/envelope.md for more
  details.

  Fields:
    payload: The actual payload that contains the SBOM reference data.
    payloadType: The kind of payload that SbomReferenceIntotoPayload takes.
      Since it's in the intoto format, this value is expected to be
      'application/vnd.in-toto+json'.
    signatures: The signatures over the payload.
  """

  payload = _messages.MessageField('SbomReferenceIntotoPayload', 1)
  payloadType = _messages.StringField(2)
  signatures = _messages.MessageField('EnvelopeSignature', 3, repeated=True)


class SBOMStatus(_messages.Message):
  r"""The status of an SBOM generation.

  Enums:
    SbomStateValueValuesEnum: The progress of the SBOM generation.

  Fields:
    error: If there was an error generating an SBOM, this will indicate what
      that error was.
    sbomState: The progress of the SBOM generation.
  """

  class SbomStateValueValuesEnum(_messages.Enum):
    r"""The progress of the SBOM generation.

    Values:
      SBOM_STATE_UNSPECIFIED: Default unknown state.
      PENDING: SBOM scanning is pending.
      COMPLETE: SBOM scanning has completed.
    """
    SBOM_STATE_UNSPECIFIED = 0
    PENDING = 1
    COMPLETE = 2

  error = _messages.StringField(1)
  sbomState = _messages.EnumField('SbomStateValueValuesEnum', 2)


class SbomReferenceIntotoPayload(_messages.Message):
  r"""The actual payload that contains the SBOM Reference data. The payload
  follows the intoto statement specification. See https://github.com/in-
  toto/attestation/blob/main/spec/v1.0/statement.md for more details.

  Fields:
    _type: Identifier for the schema of the Statement.
    predicate: Additional parameters of the Predicate. Includes the actual
      data about the SBOM.
    predicateType: URI identifying the type of the Predicate.
    subject: Set of software artifacts that the attestation applies to. Each
      element represents a single software artifact.
  """

  _type = _messages.StringField(1)
  predicate = _messages.MessageField('SbomReferenceIntotoPredicate', 2)
  predicateType = _messages.StringField(3)
  subject = _messages.MessageField('Subject', 4, repeated=True)


class SbomReferenceIntotoPredicate(_messages.Message):
  r"""A predicate which describes the SBOM being referenced.

  Messages:
    DigestValue: A map of algorithm to digest of the contents of the SBOM.

  Fields:
    digest: A map of algorithm to digest of the contents of the SBOM.
    location: The location of the SBOM.
    mimeType: The mime type of the SBOM.
    referrerId: The person or system referring this predicate to the consumer.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DigestValue(_messages.Message):
    r"""A map of algorithm to digest of the contents of the SBOM.

    Messages:
      AdditionalProperty: An additional property for a DigestValue object.

    Fields:
      additionalProperties: Additional properties of type DigestValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DigestValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  digest = _messages.MessageField('DigestValue', 1)
  location = _messages.StringField(2)
  mimeType = _messages.StringField(3)
  referrerId = _messages.StringField(4)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
  """

  policy = _messages.MessageField('Policy', 1)


class Signature(_messages.Message):
  r"""Verifiers (e.g. Kritis implementations) MUST verify signatures with
  respect to the trust anchors defined in policy (e.g. a Kritis policy).
  Typically this means that the verifier has been configured with a map from
  `public_key_id` to public key material (and any required parameters, e.g.
  signing algorithm). In particular, verification implementations MUST NOT
  treat the signature `public_key_id` as anything more than a key lookup hint.
  The `public_key_id` DOES NOT validate or authenticate a public key; it only
  provides a mechanism for quickly selecting a public key ALREADY CONFIGURED
  on the verifier through a trusted channel. Verification implementations MUST
  reject signatures in any of the following circumstances: * The
  `public_key_id` is not recognized by the verifier. * The public key that
  `public_key_id` refers to does not verify the signature with respect to the
  payload. The `signature` contents SHOULD NOT be "attached" (where the
  payload is included with the serialized `signature` bytes). Verifiers MUST
  ignore any "attached" payload and only verify signatures with respect to
  explicitly provided payload (e.g. a `payload` field on the proto message
  that holds this Signature, or the canonical serialization of the proto
  message that holds this signature).

  Fields:
    publicKeyId: The identifier for the public key that verifies this
      signature. * The `public_key_id` is required. * The `public_key_id`
      SHOULD be an RFC3986 conformant URI. * When possible, the
      `public_key_id` SHOULD be an immutable reference, such as a
      cryptographic digest. Examples of valid `public_key_id`s: OpenPGP V4
      public key fingerprint: *
      "openpgp4fpr:74FAF3B861BDA0870C7B6DEF607E48D2A663AEEA" See
      https://www.iana.org/assignments/uri-schemes/prov/openpgp4fpr for more
      details on this scheme. RFC6920 digest-named SubjectPublicKeyInfo
      (digest of the DER serialization): *
      "ni:///sha-256;cD9o9Cq6LG3jD0iKXqEi_vdjJGecm_iXkbqVoScViaU" * "nih:///sh
      a-256;703f68f42aba2c6de30f488a5ea122fef76324679c9bf89791ba95a1271589a5"
    signature: The content of the signature, an opaque bytestring. The payload
      that this signature verifies MUST be unambiguously provided with the
      Signature during verification. A wrapper message might provide the
      payload explicitly. Alternatively, a message might have a canonical
      serialization that can always be unambiguously computed to derive the
      payload.
  """

  publicKeyId = _messages.StringField(1)
  signature = _messages.BytesField(2)


class SigningKey(_messages.Message):
  r"""This defines the format used to record keys used in the software supply
  chain. An in-toto link is attested using one or more keys defined in the in-
  toto layout. An example of this is: { "key_id":
  "776a00e29f3559e0141b3b096f696abc6cfb0c657ab40f441132b345b0...", "key_type":
  "rsa", "public_key_value": "-----BEGIN PUBLIC
  KEY-----\nMIIBojANBgkqhkiG9w0B...", "key_scheme": "rsassa-pss-sha256" } The
  format for in-toto's key definition can be found in section 4.2 of the in-
  toto specification.

  Fields:
    keyId: key_id is an identifier for the signing key.
    keyScheme: This field contains the corresponding signature scheme. Eg:
      "rsassa-pss-sha256".
    keyType: This field identifies the specific signing method. Eg: "rsa",
      "ed25519", and "ecdsa".
    publicKeyValue: This field contains the actual public key.
  """

  keyId = _messages.StringField(1)
  keyScheme = _messages.StringField(2)
  keyType = _messages.StringField(3)
  publicKeyValue = _messages.StringField(4)


class SlsaProvenanceV1(_messages.Message):
  r"""Keep in sync with schema at https://github.com/slsa-
  framework/slsa/blob/main/docs/provenance/schema/v1/provenance.proto Builder
  renamed to ProvenanceBuilder because of Java conflicts.

  Fields:
    buildDefinition: A BuildDefinition attribute.
    runDetails: A RunDetails attribute.
  """

  buildDefinition = _messages.MessageField('BuildDefinition', 1)
  runDetails = _messages.MessageField('RunDetails', 2)


class Source(_messages.Message):
  r"""Source describes the location of the source used for the build.

  Messages:
    FileHashesValue: Hash(es) of the build source, which can be used to verify
      that the original source integrity was maintained in the build. The keys
      to this map are file paths used as build source and the values contain
      the hash values for those files. If the build source came in a single
      package such as a gzipped tarfile (.tar.gz), the FileHash will be for
      the single path to that file.

  Fields:
    additionalContexts: If provided, some of the source code used for the
      build may be found in these locations, in the case where the source
      repository had multiple remotes or submodules. This list will not
      include the context specified in the context field.
    artifactStorageSourceUri: If provided, the input binary artifacts for the
      build came from this location.
    context: If provided, the source code used for the build came from this
      location.
    fileHashes: Hash(es) of the build source, which can be used to verify that
      the original source integrity was maintained in the build. The keys to
      this map are file paths used as build source and the values contain the
      hash values for those files. If the build source came in a single
      package such as a gzipped tarfile (.tar.gz), the FileHash will be for
      the single path to that file.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FileHashesValue(_messages.Message):
    r"""Hash(es) of the build source, which can be used to verify that the
    original source integrity was maintained in the build. The keys to this
    map are file paths used as build source and the values contain the hash
    values for those files. If the build source came in a single package such
    as a gzipped tarfile (.tar.gz), the FileHash will be for the single path
    to that file.

    Messages:
      AdditionalProperty: An additional property for a FileHashesValue object.

    Fields:
      additionalProperties: Additional properties of type FileHashesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FileHashesValue object.

      Fields:
        key: Name of the additional property.
        value: A FileHashes attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('FileHashes', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalContexts = _messages.MessageField('SourceContext', 1, repeated=True)
  artifactStorageSourceUri = _messages.StringField(2)
  context = _messages.MessageField('SourceContext', 3)
  fileHashes = _messages.MessageField('FileHashesValue', 4)


class SourceContext(_messages.Message):
  r"""A SourceContext is a reference to a tree of files. A SourceContext
  together with a path point to a unique revision of a single file or
  directory.

  Messages:
    LabelsValue: Labels with user defined metadata.

  Fields:
    cloudRepo: A SourceContext referring to a revision in a Google Cloud
      Source Repo.
    gerrit: A SourceContext referring to a Gerrit project.
    git: A SourceContext referring to any third party Git repo (e.g., GitHub).
    labels: Labels with user defined metadata.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels with user defined metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cloudRepo = _messages.MessageField('CloudRepoSourceContext', 1)
  gerrit = _messages.MessageField('GerritSourceContext', 2)
  git = _messages.MessageField('GitSourceContext', 3)
  labels = _messages.MessageField('LabelsValue', 4)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Subject(_messages.Message):
  r"""Set of software artifacts that the attestation applies to. Each element
  represents a single software artifact.

  Messages:
    DigestValue: `"": ""` Algorithms can be e.g. sha256, sha512 See
      https://github.com/in-
      toto/attestation/blob/main/spec/field_types.md#DigestSet

  Fields:
    digest: `"": ""` Algorithms can be e.g. sha256, sha512 See
      https://github.com/in-
      toto/attestation/blob/main/spec/field_types.md#DigestSet
    name: Identifier to distinguish this artifact from others within the
      subject.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DigestValue(_messages.Message):
    r"""`"": ""` Algorithms can be e.g. sha256, sha512 See
    https://github.com/in-
    toto/attestation/blob/main/spec/field_types.md#DigestSet

    Messages:
      AdditionalProperty: An additional property for a DigestValue object.

    Fields:
      additionalProperties: Additional properties of type DigestValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DigestValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  digest = _messages.MessageField('DigestValue', 1)
  name = _messages.StringField(2)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TimeSpan(_messages.Message):
  r"""Start and end times for a build execution phase. Next ID: 3

  Fields:
    endTime: End of time span.
    startTime: Start of time span.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class Version(_messages.Message):
  r"""Version contains structured information about the version of a package.

  Enums:
    KindValueValuesEnum: Required. Distinguishes between sentinel MIN/MAX
      versions and normal versions.

  Fields:
    epoch: Used to correct mistakes in the version numbering scheme.
    inclusive: Whether this version is specifying part of an inclusive range.
      Grafeas does not have the capability to specify version ranges; instead
      we have fields that specify start version and end versions. At times
      this is insufficient - we also need to specify whether the version is
      included in the range or is excluded from the range. This boolean is
      expected to be set to true when the version is included in a range.
    kind: Required. Distinguishes between sentinel MIN/MAX versions and normal
      versions.
    name: Required only when version kind is NORMAL. The main part of the
      version name.
    revision: The iteration of the package build from the above version.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""Required. Distinguishes between sentinel MIN/MAX versions and normal
    versions.

    Values:
      VERSION_KIND_UNSPECIFIED: Unknown.
      NORMAL: A standard package version.
      MINIMUM: A special version representing negative infinity.
      MAXIMUM: A special version representing positive infinity.
    """
    VERSION_KIND_UNSPECIFIED = 0
    NORMAL = 1
    MINIMUM = 2
    MAXIMUM = 3

  epoch = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  inclusive = _messages.BooleanField(2)
  kind = _messages.EnumField('KindValueValuesEnum', 3)
  name = _messages.StringField(4)
  revision = _messages.StringField(5)


class VexAssessment(_messages.Message):
  r"""VexAssessment provides all publisher provided Vex information that is
  related to this vulnerability.

  Enums:
    StateValueValuesEnum: Provides the state of this Vulnerability assessment.

  Fields:
    cve: Holds the MITRE standard Common Vulnerabilities and Exposures (CVE)
      tracking number for the vulnerability. Deprecated: Use vulnerability_id
      instead to denote CVEs.
    impacts: Contains information about the impact of this vulnerability, this
      will change with time.
    justification: Justification provides the justification when the state of
      the assessment if NOT_AFFECTED.
    noteName: The VulnerabilityAssessment note from which this VexAssessment
      was generated. This will be of the form:
      `projects/[PROJECT_ID]/notes/[NOTE_ID]`.
    relatedUris: Holds a list of references associated with this vulnerability
      item and assessment.
    remediations: Specifies details on how to handle (and presumably, fix) a
      vulnerability.
    state: Provides the state of this Vulnerability assessment.
    vulnerabilityId: The vulnerability identifier for this Assessment. Will
      hold one of common identifiers e.g. CVE, GHSA etc.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Provides the state of this Vulnerability assessment.

    Values:
      STATE_UNSPECIFIED: No state is specified.
      AFFECTED: This product is known to be affected by this vulnerability.
      NOT_AFFECTED: This product is known to be not affected by this
        vulnerability.
      FIXED: This product contains a fix for this vulnerability.
      UNDER_INVESTIGATION: It is not known yet whether these versions are or
        are not affected by the vulnerability. However, it is still under
        investigation.
    """
    STATE_UNSPECIFIED = 0
    AFFECTED = 1
    NOT_AFFECTED = 2
    FIXED = 3
    UNDER_INVESTIGATION = 4

  cve = _messages.StringField(1)
  impacts = _messages.StringField(2, repeated=True)
  justification = _messages.MessageField('Justification', 3)
  noteName = _messages.StringField(4)
  relatedUris = _messages.MessageField('RelatedUrl', 5, repeated=True)
  remediations = _messages.MessageField('Remediation', 6, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  vulnerabilityId = _messages.StringField(8)


class Volume(_messages.Message):
  r"""Volume describes a Docker container volume which is mounted into build
  steps in order to persist files across build step execution. Next ID: 3

  Fields:
    name: Name of the volume to mount. Volume names must be unique per build
      step and must be valid names for Docker volumes. Each named volume must
      be used by at least two build steps.
    path: Path at which to mount the volume. Paths must be absolute and cannot
      conflict with other volume paths on the same build step or with certain
      reserved volume paths.
  """

  name = _messages.StringField(1)
  path = _messages.StringField(2)


class Vulnerability(_messages.Message):
  r"""Vulnerability provides metadata about a security vulnerability in a
  Note.

  Enums:
    CvssVersionValueValuesEnum: CVSS version used to populate cvss_score and
      severity.
    SeverityValueValuesEnum: Note provider assigned impact of the
      vulnerability.

  Fields:
    cvssScore: The CVSS score for this vulnerability.
    cvssV2: The full description of the CVSS for version 2.
    cvssV3: The full description of the CVSS for version 3.
    cvssVersion: CVSS version used to populate cvss_score and severity.
    cwe: A list of CWE for this vulnerability. For details, see:
      https://cwe.mitre.org/index.html
    details: All information about the package to specifically identify this
      vulnerability. One entry per (version range and cpe_uri) the package
      vulnerability has manifested in.
    severity: Note provider assigned impact of the vulnerability.
    sourceUpdateTime: The time this information was last changed at the
      source. This is an upstream timestamp from the underlying information
      source - e.g. Ubuntu security tracker.
    windowsDetails: Windows details get their own format because the
      information format and model don't match a normal detail. Specifically
      Windows updates are done as patches, thus Windows vulnerabilities really
      are a missing package, rather than a package being at an incorrect
      version.
  """

  class CvssVersionValueValuesEnum(_messages.Enum):
    r"""CVSS version used to populate cvss_score and severity.

    Values:
      CVSS_VERSION_UNSPECIFIED: <no description>
      CVSS_VERSION_2: <no description>
      CVSS_VERSION_3: <no description>
    """
    CVSS_VERSION_UNSPECIFIED = 0
    CVSS_VERSION_2 = 1
    CVSS_VERSION_3 = 2

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Note provider assigned impact of the vulnerability.

    Values:
      SEVERITY_UNSPECIFIED: Unknown.
      MINIMAL: Minimal severity.
      LOW: Low severity.
      MEDIUM: Medium severity.
      HIGH: High severity.
      CRITICAL: Critical severity.
    """
    SEVERITY_UNSPECIFIED = 0
    MINIMAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5

  cvssScore = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  cvssV2 = _messages.MessageField('CVSS', 2)
  cvssV3 = _messages.MessageField('CVSSv3', 3)
  cvssVersion = _messages.EnumField('CvssVersionValueValuesEnum', 4)
  cwe = _messages.StringField(5, repeated=True)
  details = _messages.MessageField('Detail', 6, repeated=True)
  severity = _messages.EnumField('SeverityValueValuesEnum', 7)
  sourceUpdateTime = _messages.StringField(8)
  windowsDetails = _messages.MessageField('WindowsDetail', 9, repeated=True)


class VulnerabilityAssessmentNote(_messages.Message):
  r"""A single VulnerabilityAssessmentNote represents one particular product's
  vulnerability assessment for one CVE.

  Fields:
    assessment: Represents a vulnerability assessment for the product.
    languageCode: Identifies the language used by this document, corresponding
      to IETF BCP 47 / RFC 5646.
    longDescription: A detailed description of this Vex.
    product: The product affected by this vex.
    publisher: Publisher details of this Note.
    shortDescription: A one sentence description of this Vex.
    title: The title of the note. E.g. `Vex-Debian-11.4`
  """

  assessment = _messages.MessageField('Assessment', 1)
  languageCode = _messages.StringField(2)
  longDescription = _messages.StringField(3)
  product = _messages.MessageField('Product', 4)
  publisher = _messages.MessageField('Publisher', 5)
  shortDescription = _messages.StringField(6)
  title = _messages.StringField(7)


class VulnerabilityLocation(_messages.Message):
  r"""The location of the vulnerability.

  Fields:
    cpeUri: Required. The CPE URI in [cpe
      format](https://cpe.mitre.org/specification/) format. Examples include
      distro or storage location for vulnerable jar.
    package: Required. The package being described.
    version: Required. The version of the package being described.
  """

  cpeUri = _messages.StringField(1)
  package = _messages.StringField(2)
  version = _messages.MessageField('Version', 3)


class VulnerabilityOccurrencesSummary(_messages.Message):
  r"""A summary of how many vulnerability occurrences there are per resource
  and severity type.

  Fields:
    counts: A listing by resource of the number of fixable and total
      vulnerabilities.
  """

  counts = _messages.MessageField('FixableTotalByDigest', 1, repeated=True)


class WindowsDetail(_messages.Message):
  r"""A WindowsDetail object.

  Fields:
    cpeUri: Required. The CPE URI in [cpe
      format](https://cpe.mitre.org/specification/) in which the vulnerability
      manifests. Examples include distro or storage location for vulnerable
      jar.
    description: The description of the vulnerability.
    fixingKbs: Required. The names of the KBs which have hotfixes to mitigate
      this vulnerability. Note that there may be multiple hotfixes (and thus
      multiple KBs) that mitigate a given vulnerability. Currently any listed
      kb's presence is considered a fix.
    name: Required. The name of the vulnerability.
  """

  cpeUri = _messages.StringField(1)
  description = _messages.StringField(2)
  fixingKbs = _messages.MessageField('KnowledgeBase', 3, repeated=True)
  name = _messages.StringField(4)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

"""Generated client library for gkeonprem version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.gkeonprem.v1 import gkeonprem_v1_messages as messages


class GkeonpremV1(base_api.BaseApiClient):
  """Generated client library for service gkeonprem version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://gkeonprem.googleapis.com/'
  MTLS_BASE_URL = 'https://gkeonprem.mtls.googleapis.com/'

  _PACKAGE = 'gkeonprem'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'GkeonpremV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new gkeonprem handle."""
    url = url or self.BASE_URL
    super(GkeonpremV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_bareMetalAdminClusters_operations = self.ProjectsLocationsBareMetalAdminClustersOperationsService(self)
    self.projects_locations_bareMetalAdminClusters = self.ProjectsLocationsBareMetalAdminClustersService(self)
    self.projects_locations_bareMetalClusters_bareMetalNodePools_operations = self.ProjectsLocationsBareMetalClustersBareMetalNodePoolsOperationsService(self)
    self.projects_locations_bareMetalClusters_bareMetalNodePools = self.ProjectsLocationsBareMetalClustersBareMetalNodePoolsService(self)
    self.projects_locations_bareMetalClusters_operations = self.ProjectsLocationsBareMetalClustersOperationsService(self)
    self.projects_locations_bareMetalClusters = self.ProjectsLocationsBareMetalClustersService(self)
    self.projects_locations_bareMetalStandaloneClusters_bareMetalStandaloneNodePools = self.ProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsService(self)
    self.projects_locations_bareMetalStandaloneClusters = self.ProjectsLocationsBareMetalStandaloneClustersService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_vmwareAdminClusters_operations = self.ProjectsLocationsVmwareAdminClustersOperationsService(self)
    self.projects_locations_vmwareAdminClusters = self.ProjectsLocationsVmwareAdminClustersService(self)
    self.projects_locations_vmwareClusters_operations = self.ProjectsLocationsVmwareClustersOperationsService(self)
    self.projects_locations_vmwareClusters_vmwareNodePools_operations = self.ProjectsLocationsVmwareClustersVmwareNodePoolsOperationsService(self)
    self.projects_locations_vmwareClusters_vmwareNodePools = self.ProjectsLocationsVmwareClustersVmwareNodePoolsService(self)
    self.projects_locations_vmwareClusters = self.ProjectsLocationsVmwareClustersService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsBareMetalAdminClustersOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_bareMetalAdminClusters_operations resource."""

    _NAME = 'projects_locations_bareMetalAdminClusters_operations'

    def __init__(self, client):
      super(GkeonpremV1.ProjectsLocationsBareMetalAdminClustersOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalAdminClustersOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters/{bareMetalAdminClustersId}/operations/{operationsId}',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalAdminClusters.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalAdminClustersOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalAdminClustersOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters/{bareMetalAdminClustersId}/operations',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalAdminClusters.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalAdminClustersOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsBareMetalAdminClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_bareMetalAdminClusters resource."""

    _NAME = 'projects_locations_bareMetalAdminClusters'

    def __init__(self, client):
      super(GkeonpremV1.ProjectsLocationsBareMetalAdminClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new bare metal admin cluster in a given project and location. The API needs to be combined with creating a bootstrap cluster to work. See: https://cloud.google.com/anthos/clusters/docs/bare-metal/latest/installing/creating-clusters/create-admin-cluster-api#prepare_bootstrap_environment.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalAdminClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalAdminClusters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bareMetalAdminClusterId', 'validateOnly'],
        relative_path='v1/{+parent}/bareMetalAdminClusters',
        request_field='bareMetalAdminCluster',
        request_type_name='GkeonpremProjectsLocationsBareMetalAdminClustersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Enroll(self, request, global_params=None):
      r"""Enrolls an existing bare metal admin cluster to the Anthos On-Prem API within a given project and location. Through enrollment, an existing admin cluster will become Anthos On-Prem API managed. The corresponding GCP resources will be created and all future modifications to the cluster will be expected to be performed through the API.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalAdminClustersEnrollRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Enroll')
      return self._RunMethod(
          config, request, global_params=global_params)

    Enroll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters:enroll',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalAdminClusters.enroll',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/bareMetalAdminClusters:enroll',
        request_field='enrollBareMetalAdminClusterRequest',
        request_type_name='GkeonpremProjectsLocationsBareMetalAdminClustersEnrollRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single bare metal admin cluster.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalAdminClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BareMetalAdminCluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters/{bareMetalAdminClustersId}',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalAdminClusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalAdminClustersGetRequest',
        response_type_name='BareMetalAdminCluster',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalAdminClustersGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters/{bareMetalAdminClustersId}:getIamPolicy',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalAdminClusters.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalAdminClustersGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists bare metal admin clusters in a given project and location.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalAdminClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBareMetalAdminClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalAdminClusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'view'],
        relative_path='v1/{+parent}/bareMetalAdminClusters',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalAdminClustersListRequest',
        response_type_name='ListBareMetalAdminClustersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single bare metal admin cluster.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalAdminClustersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters/{bareMetalAdminClustersId}',
        http_method='PATCH',
        method_id='gkeonprem.projects.locations.bareMetalAdminClusters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='bareMetalAdminCluster',
        request_type_name='GkeonpremProjectsLocationsBareMetalAdminClustersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def QueryVersionConfig(self, request, global_params=None):
      r"""Queries the bare metal admin cluster version config.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalAdminClustersQueryVersionConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (QueryBareMetalAdminVersionConfigResponse) The response message.
      """
      config = self.GetMethodConfig('QueryVersionConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryVersionConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters:queryVersionConfig',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalAdminClusters.queryVersionConfig',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['upgradeConfig_clusterName'],
        relative_path='v1/{+parent}/bareMetalAdminClusters:queryVersionConfig',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalAdminClustersQueryVersionConfigRequest',
        response_type_name='QueryBareMetalAdminVersionConfigResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalAdminClustersSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters/{bareMetalAdminClustersId}:setIamPolicy',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalAdminClusters.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='GkeonpremProjectsLocationsBareMetalAdminClustersSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalAdminClustersTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters/{bareMetalAdminClustersId}:testIamPermissions',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalAdminClusters.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='GkeonpremProjectsLocationsBareMetalAdminClustersTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Unenroll(self, request, global_params=None):
      r"""Unenrolls an existing bare metal admin cluster from the Anthos On-Prem API within a given project and location. Unenrollment removes the Cloud reference to the cluster without modifying the underlying OnPrem Resources. Clusters will continue to run; however, they will no longer be accessible through the Anthos On-Prem API or its clients.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalAdminClustersUnenrollRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Unenroll')
      return self._RunMethod(
          config, request, global_params=global_params)

    Unenroll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalAdminClusters/{bareMetalAdminClustersId}:unenroll',
        http_method='DELETE',
        method_id='gkeonprem.projects.locations.bareMetalAdminClusters.unenroll',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'ignoreErrors', 'validateOnly'],
        relative_path='v1/{+name}:unenroll',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalAdminClustersUnenrollRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsBareMetalClustersBareMetalNodePoolsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_bareMetalClusters_bareMetalNodePools_operations resource."""

    _NAME = 'projects_locations_bareMetalClusters_bareMetalNodePools_operations'

    def __init__(self, client):
      super(GkeonpremV1.ProjectsLocationsBareMetalClustersBareMetalNodePoolsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}/operations/{operationsId}',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}/operations',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsBareMetalClustersBareMetalNodePoolsService(base_api.BaseApiService):
    """Service class for the projects_locations_bareMetalClusters_bareMetalNodePools resource."""

    _NAME = 'projects_locations_bareMetalClusters_bareMetalNodePools'

    def __init__(self, client):
      super(GkeonpremV1.ProjectsLocationsBareMetalClustersBareMetalNodePoolsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new bare metal node pool in a given project, location and Bare Metal cluster.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bareMetalNodePoolId', 'validateOnly'],
        relative_path='v1/{+parent}/bareMetalNodePools',
        request_field='bareMetalNodePool',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single bare metal node pool.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}',
        http_method='DELETE',
        method_id='gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'ignoreErrors', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Enroll(self, request, global_params=None):
      r"""Enrolls an existing bare metal node pool to the Anthos On-Prem API within a given project and location. Through enrollment, an existing node pool will become Anthos On-Prem API managed. The corresponding GCP resources will be created.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsEnrollRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Enroll')
      return self._RunMethod(
          config, request, global_params=global_params)

    Enroll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools:enroll',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.enroll',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/bareMetalNodePools:enroll',
        request_field='enrollBareMetalNodePoolRequest',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsEnrollRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single bare metal node pool.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BareMetalNodePool) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsGetRequest',
        response_type_name='BareMetalNodePool',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}:getIamPolicy',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists bare metal node pools in a given project, location and bare metal cluster.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBareMetalNodePoolsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'view'],
        relative_path='v1/{+parent}/bareMetalNodePools',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsListRequest',
        response_type_name='ListBareMetalNodePoolsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single bare metal node pool.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}',
        http_method='PATCH',
        method_id='gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='bareMetalNodePool',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}:setIamPolicy',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}:testIamPermissions',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Unenroll(self, request, global_params=None):
      r"""Unenrolls a bare metal node pool from Anthos On-Prem API.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsUnenrollRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Unenroll')
      return self._RunMethod(
          config, request, global_params=global_params)

    Unenroll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/bareMetalNodePools/{bareMetalNodePoolsId}:unenroll',
        http_method='DELETE',
        method_id='gkeonprem.projects.locations.bareMetalClusters.bareMetalNodePools.unenroll',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'validateOnly'],
        relative_path='v1/{+name}:unenroll',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersBareMetalNodePoolsUnenrollRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsBareMetalClustersOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_bareMetalClusters_operations resource."""

    _NAME = 'projects_locations_bareMetalClusters_operations'

    def __init__(self, client):
      super(GkeonpremV1.ProjectsLocationsBareMetalClustersOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/operations/{operationsId}',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalClusters.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}/operations',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalClusters.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsBareMetalClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_bareMetalClusters resource."""

    _NAME = 'projects_locations_bareMetalClusters'

    def __init__(self, client):
      super(GkeonpremV1.ProjectsLocationsBareMetalClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new bare metal cluster in a given project and location.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalClusters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bareMetalClusterId', 'validateOnly'],
        relative_path='v1/{+parent}/bareMetalClusters',
        request_field='bareMetalCluster',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single bare metal Cluster.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}',
        http_method='DELETE',
        method_id='gkeonprem.projects.locations.bareMetalClusters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'force', 'ignoreErrors', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Enroll(self, request, global_params=None):
      r"""Enrolls an existing bare metal user cluster and its node pools to the Anthos On-Prem API within a given project and location. Through enrollment, an existing cluster will become Anthos On-Prem API managed. The corresponding GCP resources will be created and all future modifications to the cluster and/or its node pools will be expected to be performed through the API.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersEnrollRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Enroll')
      return self._RunMethod(
          config, request, global_params=global_params)

    Enroll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters:enroll',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalClusters.enroll',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/bareMetalClusters:enroll',
        request_field='enrollBareMetalClusterRequest',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersEnrollRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single bare metal Cluster.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BareMetalCluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalClusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersGetRequest',
        response_type_name='BareMetalCluster',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}:getIamPolicy',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalClusters.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists bare metal clusters in a given project and location.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBareMetalClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalClusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'view'],
        relative_path='v1/{+parent}/bareMetalClusters',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersListRequest',
        response_type_name='ListBareMetalClustersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single bare metal Cluster.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}',
        http_method='PATCH',
        method_id='gkeonprem.projects.locations.bareMetalClusters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='bareMetalCluster',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def QueryVersionConfig(self, request, global_params=None):
      r"""Queries the bare metal user cluster version config.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersQueryVersionConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (QueryBareMetalVersionConfigResponse) The response message.
      """
      config = self.GetMethodConfig('QueryVersionConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryVersionConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters:queryVersionConfig',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalClusters.queryVersionConfig',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['createConfig_adminClusterMembership', 'createConfig_adminClusterName', 'upgradeConfig_clusterName'],
        relative_path='v1/{+parent}/bareMetalClusters:queryVersionConfig',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersQueryVersionConfigRequest',
        response_type_name='QueryBareMetalVersionConfigResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}:setIamPolicy',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalClusters.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}:testIamPermissions',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalClusters.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Unenroll(self, request, global_params=None):
      r"""Unenrolls an existing bare metal user cluster and its node pools from the Anthos On-Prem API within a given project and location. Unenrollment removes the Cloud reference to the cluster without modifying the underlying OnPrem Resources. Clusters and node pools will continue to run; however, they will no longer be accessible through the Anthos On-Prem API or its clients.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalClustersUnenrollRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Unenroll')
      return self._RunMethod(
          config, request, global_params=global_params)

    Unenroll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalClusters/{bareMetalClustersId}:unenroll',
        http_method='DELETE',
        method_id='gkeonprem.projects.locations.bareMetalClusters.unenroll',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'force', 'validateOnly'],
        relative_path='v1/{+name}:unenroll',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalClustersUnenrollRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsService(base_api.BaseApiService):
    """Service class for the projects_locations_bareMetalStandaloneClusters_bareMetalStandaloneNodePools resource."""

    _NAME = 'projects_locations_bareMetalStandaloneClusters_bareMetalStandaloneNodePools'

    def __init__(self, client):
      super(GkeonpremV1.ProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new bare metal standalone node pool in a given project, location and bare metal standalone cluster.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalStandaloneClusters/{bareMetalStandaloneClustersId}/bareMetalStandaloneNodePools',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalStandaloneClusters.bareMetalStandaloneNodePools.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bareMetalStandaloneNodePoolId', 'validateOnly'],
        relative_path='v1/{+parent}/bareMetalStandaloneNodePools',
        request_field='bareMetalStandaloneNodePool',
        request_type_name='GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single bare metal standalone node pool.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalStandaloneClusters/{bareMetalStandaloneClustersId}/bareMetalStandaloneNodePools/{bareMetalStandaloneNodePoolsId}',
        http_method='DELETE',
        method_id='gkeonprem.projects.locations.bareMetalStandaloneClusters.bareMetalStandaloneNodePools.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'ignoreErrors', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Enroll(self, request, global_params=None):
      r"""Enrolls an existing bare metal standalone node pool to the Anthos On-Prem API within a given project and location. Through enrollment, an existing standalone node pool will become Anthos On-Prem API managed. The corresponding GCP resources will be created.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsEnrollRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Enroll')
      return self._RunMethod(
          config, request, global_params=global_params)

    Enroll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalStandaloneClusters/{bareMetalStandaloneClustersId}/bareMetalStandaloneNodePools:enroll',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalStandaloneClusters.bareMetalStandaloneNodePools.enroll',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/bareMetalStandaloneNodePools:enroll',
        request_field='enrollBareMetalStandaloneNodePoolRequest',
        request_type_name='GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsEnrollRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single bare metal standalone node pool.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BareMetalStandaloneNodePool) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalStandaloneClusters/{bareMetalStandaloneClustersId}/bareMetalStandaloneNodePools/{bareMetalStandaloneNodePoolsId}',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalStandaloneClusters.bareMetalStandaloneNodePools.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsGetRequest',
        response_type_name='BareMetalStandaloneNodePool',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists bare metal standalone node pools in a given project, location and bare metal standalone cluster.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBareMetalStandaloneNodePoolsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalStandaloneClusters/{bareMetalStandaloneClustersId}/bareMetalStandaloneNodePools',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalStandaloneClusters.bareMetalStandaloneNodePools.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/bareMetalStandaloneNodePools',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsListRequest',
        response_type_name='ListBareMetalStandaloneNodePoolsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single bare metal standalone node pool.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalStandaloneClusters/{bareMetalStandaloneClustersId}/bareMetalStandaloneNodePools/{bareMetalStandaloneNodePoolsId}',
        http_method='PATCH',
        method_id='gkeonprem.projects.locations.bareMetalStandaloneClusters.bareMetalStandaloneNodePools.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='bareMetalStandaloneNodePool',
        request_type_name='GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Unenroll(self, request, global_params=None):
      r"""Unenrolls a bare metal standalone node pool from Anthos On-Prem API.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsUnenrollRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Unenroll')
      return self._RunMethod(
          config, request, global_params=global_params)

    Unenroll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalStandaloneClusters/{bareMetalStandaloneClustersId}/bareMetalStandaloneNodePools/{bareMetalStandaloneNodePoolsId}:unenroll',
        http_method='DELETE',
        method_id='gkeonprem.projects.locations.bareMetalStandaloneClusters.bareMetalStandaloneNodePools.unenroll',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'validateOnly'],
        relative_path='v1/{+name}:unenroll',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalStandaloneClustersBareMetalStandaloneNodePoolsUnenrollRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsBareMetalStandaloneClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_bareMetalStandaloneClusters resource."""

    _NAME = 'projects_locations_bareMetalStandaloneClusters'

    def __init__(self, client):
      super(GkeonpremV1.ProjectsLocationsBareMetalStandaloneClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Enroll(self, request, global_params=None):
      r"""Enrolls an existing bare metal standalone cluster to the Anthos On-Prem API within a given project and location. Through enrollment, an existing standalone cluster will become Anthos On-Prem API managed. The corresponding GCP resources will be created and all future modifications to the cluster will be expected to be performed through the API.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalStandaloneClustersEnrollRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Enroll')
      return self._RunMethod(
          config, request, global_params=global_params)

    Enroll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalStandaloneClusters:enroll',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalStandaloneClusters.enroll',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/bareMetalStandaloneClusters:enroll',
        request_field='enrollBareMetalStandaloneClusterRequest',
        request_type_name='GkeonpremProjectsLocationsBareMetalStandaloneClustersEnrollRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single bare metal standalone cluster.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalStandaloneClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BareMetalStandaloneCluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalStandaloneClusters/{bareMetalStandaloneClustersId}',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalStandaloneClusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalStandaloneClustersGetRequest',
        response_type_name='BareMetalStandaloneCluster',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists bare metal standalone clusters in a given project and location.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalStandaloneClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBareMetalStandaloneClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalStandaloneClusters',
        http_method='GET',
        method_id='gkeonprem.projects.locations.bareMetalStandaloneClusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/bareMetalStandaloneClusters',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalStandaloneClustersListRequest',
        response_type_name='ListBareMetalStandaloneClustersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single bare metal standalone cluster.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalStandaloneClustersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalStandaloneClusters/{bareMetalStandaloneClustersId}',
        http_method='PATCH',
        method_id='gkeonprem.projects.locations.bareMetalStandaloneClusters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='bareMetalStandaloneCluster',
        request_type_name='GkeonpremProjectsLocationsBareMetalStandaloneClustersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def QueryVersionConfig(self, request, global_params=None):
      r"""Queries the bare metal standalone cluster version config.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalStandaloneClustersQueryVersionConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (QueryBareMetalStandaloneVersionConfigResponse) The response message.
      """
      config = self.GetMethodConfig('QueryVersionConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryVersionConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalStandaloneClusters:queryVersionConfig',
        http_method='POST',
        method_id='gkeonprem.projects.locations.bareMetalStandaloneClusters.queryVersionConfig',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['upgradeConfig_clusterName'],
        relative_path='v1/{+parent}/bareMetalStandaloneClusters:queryVersionConfig',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalStandaloneClustersQueryVersionConfigRequest',
        response_type_name='QueryBareMetalStandaloneVersionConfigResponse',
        supports_download=False,
    )

    def Unenroll(self, request, global_params=None):
      r"""Unenrolls an existing bare metal standalone cluster from the GKE on-prem API within a given project and location. Unenrollment removes the Cloud reference to the cluster without modifying the underlying OnPrem Resources. Clusters will continue to run; however, they will no longer be accessible through the Anthos On-Prem API or its clients.

      Args:
        request: (GkeonpremProjectsLocationsBareMetalStandaloneClustersUnenrollRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Unenroll')
      return self._RunMethod(
          config, request, global_params=global_params)

    Unenroll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/bareMetalStandaloneClusters/{bareMetalStandaloneClustersId}:unenroll',
        http_method='DELETE',
        method_id='gkeonprem.projects.locations.bareMetalStandaloneClusters.unenroll',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'force', 'ignoreErrors', 'validateOnly'],
        relative_path='v1/{+name}:unenroll',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsBareMetalStandaloneClustersUnenrollRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(GkeonpremV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (GkeonpremProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='gkeonprem.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='GkeonpremProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (GkeonpremProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='gkeonprem.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (GkeonpremProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='gkeonprem.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (GkeonpremProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='gkeonprem.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsVmwareAdminClustersOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_vmwareAdminClusters_operations resource."""

    _NAME = 'projects_locations_vmwareAdminClusters_operations'

    def __init__(self, client):
      super(GkeonpremV1.ProjectsLocationsVmwareAdminClustersOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (GkeonpremProjectsLocationsVmwareAdminClustersOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters/{vmwareAdminClustersId}/operations/{operationsId}',
        http_method='GET',
        method_id='gkeonprem.projects.locations.vmwareAdminClusters.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareAdminClustersOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (GkeonpremProjectsLocationsVmwareAdminClustersOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters/{vmwareAdminClustersId}/operations',
        http_method='GET',
        method_id='gkeonprem.projects.locations.vmwareAdminClusters.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareAdminClustersOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsVmwareAdminClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_vmwareAdminClusters resource."""

    _NAME = 'projects_locations_vmwareAdminClusters'

    def __init__(self, client):
      super(GkeonpremV1.ProjectsLocationsVmwareAdminClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new VMware admin cluster in a given project and location. The API needs to be combined with creating a bootstrap cluster to work.

      Args:
        request: (GkeonpremProjectsLocationsVmwareAdminClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters',
        http_method='POST',
        method_id='gkeonprem.projects.locations.vmwareAdminClusters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['validateOnly', 'vmwareAdminClusterId'],
        relative_path='v1/{+parent}/vmwareAdminClusters',
        request_field='vmwareAdminCluster',
        request_type_name='GkeonpremProjectsLocationsVmwareAdminClustersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Enroll(self, request, global_params=None):
      r"""Enrolls an existing VMware admin cluster to the Anthos On-Prem API within a given project and location. Through enrollment, an existing admin cluster will become Anthos On-Prem API managed. The corresponding GCP resources will be created and all future modifications to the cluster will be expected to be performed through the API.

      Args:
        request: (GkeonpremProjectsLocationsVmwareAdminClustersEnrollRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Enroll')
      return self._RunMethod(
          config, request, global_params=global_params)

    Enroll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters:enroll',
        http_method='POST',
        method_id='gkeonprem.projects.locations.vmwareAdminClusters.enroll',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/vmwareAdminClusters:enroll',
        request_field='enrollVmwareAdminClusterRequest',
        request_type_name='GkeonpremProjectsLocationsVmwareAdminClustersEnrollRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single VMware admin cluster.

      Args:
        request: (GkeonpremProjectsLocationsVmwareAdminClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VmwareAdminCluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters/{vmwareAdminClustersId}',
        http_method='GET',
        method_id='gkeonprem.projects.locations.vmwareAdminClusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareAdminClustersGetRequest',
        response_type_name='VmwareAdminCluster',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (GkeonpremProjectsLocationsVmwareAdminClustersGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters/{vmwareAdminClustersId}:getIamPolicy',
        http_method='GET',
        method_id='gkeonprem.projects.locations.vmwareAdminClusters.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareAdminClustersGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists VMware admin clusters in a given project and location.

      Args:
        request: (GkeonpremProjectsLocationsVmwareAdminClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListVmwareAdminClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters',
        http_method='GET',
        method_id='gkeonprem.projects.locations.vmwareAdminClusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'view'],
        relative_path='v1/{+parent}/vmwareAdminClusters',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareAdminClustersListRequest',
        response_type_name='ListVmwareAdminClustersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single VMware admin cluster.

      Args:
        request: (GkeonpremProjectsLocationsVmwareAdminClustersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters/{vmwareAdminClustersId}',
        http_method='PATCH',
        method_id='gkeonprem.projects.locations.vmwareAdminClusters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='vmwareAdminCluster',
        request_type_name='GkeonpremProjectsLocationsVmwareAdminClustersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (GkeonpremProjectsLocationsVmwareAdminClustersSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters/{vmwareAdminClustersId}:setIamPolicy',
        http_method='POST',
        method_id='gkeonprem.projects.locations.vmwareAdminClusters.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='GkeonpremProjectsLocationsVmwareAdminClustersSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (GkeonpremProjectsLocationsVmwareAdminClustersTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters/{vmwareAdminClustersId}:testIamPermissions',
        http_method='POST',
        method_id='gkeonprem.projects.locations.vmwareAdminClusters.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='GkeonpremProjectsLocationsVmwareAdminClustersTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Unenroll(self, request, global_params=None):
      r"""Unenrolls an existing VMware admin cluster from the Anthos On-Prem API within a given project and location. Unenrollment removes the Cloud reference to the cluster without modifying the underlying OnPrem Resources. Clusters will continue to run; however, they will no longer be accessible through the Anthos On-Prem API or its clients.

      Args:
        request: (GkeonpremProjectsLocationsVmwareAdminClustersUnenrollRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Unenroll')
      return self._RunMethod(
          config, request, global_params=global_params)

    Unenroll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareAdminClusters/{vmwareAdminClustersId}:unenroll',
        http_method='DELETE',
        method_id='gkeonprem.projects.locations.vmwareAdminClusters.unenroll',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'validateOnly'],
        relative_path='v1/{+name}:unenroll',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareAdminClustersUnenrollRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsVmwareClustersOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_vmwareClusters_operations resource."""

    _NAME = 'projects_locations_vmwareClusters_operations'

    def __init__(self, client):
      super(GkeonpremV1.ProjectsLocationsVmwareClustersOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/operations/{operationsId}',
        http_method='GET',
        method_id='gkeonprem.projects.locations.vmwareClusters.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/operations',
        http_method='GET',
        method_id='gkeonprem.projects.locations.vmwareClusters.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsVmwareClustersVmwareNodePoolsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_vmwareClusters_vmwareNodePools_operations resource."""

    _NAME = 'projects_locations_vmwareClusters_vmwareNodePools_operations'

    def __init__(self, client):
      super(GkeonpremV1.ProjectsLocationsVmwareClustersVmwareNodePoolsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}/operations/{operationsId}',
        http_method='GET',
        method_id='gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}/operations',
        http_method='GET',
        method_id='gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsVmwareClustersVmwareNodePoolsService(base_api.BaseApiService):
    """Service class for the projects_locations_vmwareClusters_vmwareNodePools resource."""

    _NAME = 'projects_locations_vmwareClusters_vmwareNodePools'

    def __init__(self, client):
      super(GkeonpremV1.ProjectsLocationsVmwareClustersVmwareNodePoolsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new VMware node pool in a given project, location and VMWare cluster.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools',
        http_method='POST',
        method_id='gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['validateOnly', 'vmwareNodePoolId'],
        relative_path='v1/{+parent}/vmwareNodePools',
        request_field='vmwareNodePool',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single VMware node pool.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}',
        http_method='DELETE',
        method_id='gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'ignoreErrors', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Enroll(self, request, global_params=None):
      r"""Enrolls a VMware node pool to Anthos On-Prem API.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsEnrollRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Enroll')
      return self._RunMethod(
          config, request, global_params=global_params)

    Enroll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools:enroll',
        http_method='POST',
        method_id='gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.enroll',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/vmwareNodePools:enroll',
        request_field='enrollVmwareNodePoolRequest',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsEnrollRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single VMware node pool.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VmwareNodePool) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}',
        http_method='GET',
        method_id='gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsGetRequest',
        response_type_name='VmwareNodePool',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}:getIamPolicy',
        http_method='GET',
        method_id='gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists VMware node pools in a given project, location and VMWare cluster.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListVmwareNodePoolsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools',
        http_method='GET',
        method_id='gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'view'],
        relative_path='v1/{+parent}/vmwareNodePools',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsListRequest',
        response_type_name='ListVmwareNodePoolsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single VMware node pool.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}',
        http_method='PATCH',
        method_id='gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='vmwareNodePool',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}:setIamPolicy',
        http_method='POST',
        method_id='gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}:testIamPermissions',
        http_method='POST',
        method_id='gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Unenroll(self, request, global_params=None):
      r"""Unenrolls a VMware node pool to Anthos On-Prem API.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsUnenrollRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Unenroll')
      return self._RunMethod(
          config, request, global_params=global_params)

    Unenroll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}/vmwareNodePools/{vmwareNodePoolsId}:unenroll',
        http_method='DELETE',
        method_id='gkeonprem.projects.locations.vmwareClusters.vmwareNodePools.unenroll',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'validateOnly'],
        relative_path='v1/{+name}:unenroll',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersVmwareNodePoolsUnenrollRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsVmwareClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_vmwareClusters resource."""

    _NAME = 'projects_locations_vmwareClusters'

    def __init__(self, client):
      super(GkeonpremV1.ProjectsLocationsVmwareClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new VMware user cluster in a given project and location.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters',
        http_method='POST',
        method_id='gkeonprem.projects.locations.vmwareClusters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['validateOnly', 'vmwareClusterId'],
        relative_path='v1/{+parent}/vmwareClusters',
        request_field='vmwareCluster',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single VMware Cluster.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}',
        http_method='DELETE',
        method_id='gkeonprem.projects.locations.vmwareClusters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'force', 'ignoreErrors', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Enroll(self, request, global_params=None):
      r"""Enrolls an existing VMware user cluster and its node pools to the Anthos On-Prem API within a given project and location. Through enrollment, an existing cluster will become Anthos On-Prem API managed. The corresponding GCP resources will be created and all future modifications to the cluster and/or its node pools will be expected to be performed through the API.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersEnrollRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Enroll')
      return self._RunMethod(
          config, request, global_params=global_params)

    Enroll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters:enroll',
        http_method='POST',
        method_id='gkeonprem.projects.locations.vmwareClusters.enroll',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/vmwareClusters:enroll',
        request_field='enrollVmwareClusterRequest',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersEnrollRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single VMware Cluster.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (VmwareCluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}',
        http_method='GET',
        method_id='gkeonprem.projects.locations.vmwareClusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersGetRequest',
        response_type_name='VmwareCluster',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}:getIamPolicy',
        http_method='GET',
        method_id='gkeonprem.projects.locations.vmwareClusters.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists VMware Clusters in a given project and location.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListVmwareClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters',
        http_method='GET',
        method_id='gkeonprem.projects.locations.vmwareClusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'view'],
        relative_path='v1/{+parent}/vmwareClusters',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersListRequest',
        response_type_name='ListVmwareClustersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single VMware cluster.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}',
        http_method='PATCH',
        method_id='gkeonprem.projects.locations.vmwareClusters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask', 'validateOnly'],
        relative_path='v1/{+name}',
        request_field='vmwareCluster',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def QueryVersionConfig(self, request, global_params=None):
      r"""Queries the VMware user cluster version config.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersQueryVersionConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (QueryVmwareVersionConfigResponse) The response message.
      """
      config = self.GetMethodConfig('QueryVersionConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryVersionConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters:queryVersionConfig',
        http_method='POST',
        method_id='gkeonprem.projects.locations.vmwareClusters.queryVersionConfig',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['createConfig_adminClusterMembership', 'createConfig_adminClusterName', 'upgradeConfig_clusterName'],
        relative_path='v1/{+parent}/vmwareClusters:queryVersionConfig',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersQueryVersionConfigRequest',
        response_type_name='QueryVmwareVersionConfigResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}:setIamPolicy',
        http_method='POST',
        method_id='gkeonprem.projects.locations.vmwareClusters.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}:testIamPermissions',
        http_method='POST',
        method_id='gkeonprem.projects.locations.vmwareClusters.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

    def Unenroll(self, request, global_params=None):
      r"""Unenrolls an existing VMware user cluster and its node pools from the Anthos On-Prem API within a given project and location. Unenrollment removes the Cloud reference to the cluster without modifying the underlying OnPrem Resources. Clusters and node pools will continue to run; however, they will no longer be accessible through the Anthos On-Prem API or UI.

      Args:
        request: (GkeonpremProjectsLocationsVmwareClustersUnenrollRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Unenroll')
      return self._RunMethod(
          config, request, global_params=global_params)

    Unenroll.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/vmwareClusters/{vmwareClustersId}:unenroll',
        http_method='DELETE',
        method_id='gkeonprem.projects.locations.vmwareClusters.unenroll',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'etag', 'force', 'validateOnly'],
        relative_path='v1/{+name}:unenroll',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsVmwareClustersUnenrollRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(GkeonpremV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (GkeonpremProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='gkeonprem.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (GkeonpremProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='gkeonprem.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/locations',
        request_field='',
        request_type_name='GkeonpremProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(GkeonpremV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

"""Generated message classes for datacatalog version v1beta1.

A fully managed and highly scalable data discovery and metadata management
service.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'datacatalog'


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class DatacatalogEntriesLookupRequest(_messages.Message):
  r"""A DatacatalogEntriesLookupRequest object.

  Fields:
    linkedResource: The full name of the Google Cloud Platform resource the
      Data Catalog entry represents. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name.
      Full names are case-sensitive. Examples: * //bigquery.googleapis.com/pro
      jects/projectId/datasets/datasetId/tables/tableId *
      //pubsub.googleapis.com/projects/projectId/topics/topicId
    sqlResource: The SQL name of the entry. SQL names are case-sensitive.
      Examples: * `pubsub.project_id.topic_id` *
      ``pubsub.project_id.`topic.id.with.dots` `` *
      `bigquery.table.project_id.dataset_id.table_id` *
      `bigquery.dataset.project_id.dataset_id` *
      `datacatalog.entry.project_id.location_id.entry_group_id.entry_id`
      `*_id`s should satisfy the standard SQL rules for identifiers.
      https://cloud.google.com/bigquery/docs/reference/standard-sql/lexical.
  """

  linkedResource = _messages.StringField(1)
  sqlResource = _messages.StringField(2)


class DatacatalogProjectsLocationsEntryGroupsCreateRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsCreateRequest object.

  Fields:
    entryGroupId: Required. The id of the entry group to create. The id must
      begin with a letter or underscore, contain only English letters, numbers
      and underscores, and be at most 64 characters.
    googleCloudDatacatalogV1beta1EntryGroup: A
      GoogleCloudDatacatalogV1beta1EntryGroup resource to be passed as the
      request body.
    parent: Required. The name of the project this entry group is in. Example:
      * projects/{project_id}/locations/{location} Note that this EntryGroup
      and its child resources may not actually be stored in the location in
      this name.
  """

  entryGroupId = _messages.StringField(1)
  googleCloudDatacatalogV1beta1EntryGroup = _messages.MessageField('GoogleCloudDatacatalogV1beta1EntryGroup', 2)
  parent = _messages.StringField(3, required=True)


class DatacatalogProjectsLocationsEntryGroupsDeleteRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsDeleteRequest object.

  Fields:
    force: Optional. If true, deletes all entries in the entry group.
    name: Required. The name of the entry group. For example,
      `projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}
      `.
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class DatacatalogProjectsLocationsEntryGroupsEntriesCreateRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsEntriesCreateRequest object.

  Fields:
    entryId: Required. The id of the entry to create.
    googleCloudDatacatalogV1beta1Entry: A GoogleCloudDatacatalogV1beta1Entry
      resource to be passed as the request body.
    parent: Required. The name of the entry group this entry is in. Example: *
      projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}
      Note that this Entry and its child resources may not actually be stored
      in the location in this name.
  """

  entryId = _messages.StringField(1)
  googleCloudDatacatalogV1beta1Entry = _messages.MessageField('GoogleCloudDatacatalogV1beta1Entry', 2)
  parent = _messages.StringField(3, required=True)


class DatacatalogProjectsLocationsEntryGroupsEntriesDeleteRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsEntriesDeleteRequest object.

  Fields:
    name: Required. The name of the entry. Example: * projects/{project_id}/lo
      cations/{location}/entryGroups/{entry_group_id}/entries/{entry_id}
  """

  name = _messages.StringField(1, required=True)


class DatacatalogProjectsLocationsEntryGroupsEntriesGetIamPolicyRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsEntriesGetIamPolicyRequest
  object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DatacatalogProjectsLocationsEntryGroupsEntriesGetRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsEntriesGetRequest object.

  Fields:
    name: Required. The name of the entry. Example: * projects/{project_id}/lo
      cations/{location}/entryGroups/{entry_group_id}/entries/{entry_id}
  """

  name = _messages.StringField(1, required=True)


class DatacatalogProjectsLocationsEntryGroupsEntriesListRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsEntriesListRequest object.

  Fields:
    pageSize: The maximum number of items to return. Default is 10. Max limit
      is 1000. Throws an invalid argument for `page_size > 1000`.
    pageToken: Token that specifies which page is requested. If empty, the
      first page is returned.
    parent: Required. The name of the entry group that contains the entries,
      which can be provided in URL format. Example: *
      projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}
    readMask: The fields to return for each Entry. If not set or empty, all
      fields are returned. For example, setting read_mask to contain only one
      path "name" will cause ListEntries to return a list of Entries with only
      "name" field.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  readMask = _messages.StringField(4)


class DatacatalogProjectsLocationsEntryGroupsEntriesPatchRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsEntriesPatchRequest object.

  Fields:
    googleCloudDatacatalogV1beta1Entry: A GoogleCloudDatacatalogV1beta1Entry
      resource to be passed as the request body.
    name: Output only. The Data Catalog resource name of the entry in URL
      format. Example: * projects/{project_id}/locations/{location}/entryGroup
      s/{entry_group_id}/entries/{entry_id} Note that this Entry and its child
      resources may not actually be stored in the location in this name.
    updateMask: Names of fields whose values to overwrite on an entry. If this
      parameter is absent or empty, all modifiable fields are overwritten. If
      such fields are non-required and omitted in the request body, their
      values are emptied. The following fields are modifiable: * For entries
      with type `DATA_STREAM`: * `schema` * For entries with type `FILESET`: *
      `schema` * `display_name` * `description` * `gcs_fileset_spec` *
      `gcs_fileset_spec.file_patterns` * For entries with
      `user_specified_type`: * `schema` * `display_name` * `description` *
      `user_specified_type` * `user_specified_system` * `linked_resource` *
      `source_system_timestamps`
  """

  googleCloudDatacatalogV1beta1Entry = _messages.MessageField('GoogleCloudDatacatalogV1beta1Entry', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class DatacatalogProjectsLocationsEntryGroupsEntriesTagsCreateRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsEntriesTagsCreateRequest
  object.

  Fields:
    googleCloudDatacatalogV1beta1Tag: A GoogleCloudDatacatalogV1beta1Tag
      resource to be passed as the request body.
    parent: Required. The name of the resource to attach this tag to. Tags can
      be attached to Entries. Example: * projects/{project_id}/locations/{loca
      tion}/entryGroups/{entry_group_id}/entries/{entry_id} Note that this Tag
      and its child resources may not actually be stored in the location in
      this name.
  """

  googleCloudDatacatalogV1beta1Tag = _messages.MessageField('GoogleCloudDatacatalogV1beta1Tag', 1)
  parent = _messages.StringField(2, required=True)


class DatacatalogProjectsLocationsEntryGroupsEntriesTagsDeleteRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsEntriesTagsDeleteRequest
  object.

  Fields:
    name: Required. The name of the tag to delete. Example: * projects/{projec
      t_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_i
      d}/tags/{tag_id}
  """

  name = _messages.StringField(1, required=True)


class DatacatalogProjectsLocationsEntryGroupsEntriesTagsListRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsEntriesTagsListRequest object.

  Fields:
    pageSize: The maximum number of tags to return. Default is 10. Max limit
      is 1000.
    pageToken: Token that specifies which page is requested. If empty, the
      first page is returned.
    parent: Required. The name of the Data Catalog resource to list the tags
      of. The resource could be an Entry or an EntryGroup. Examples: *
      projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}
      * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id
      }/entries/{entry_id}
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DatacatalogProjectsLocationsEntryGroupsEntriesTagsPatchRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsEntriesTagsPatchRequest object.

  Fields:
    googleCloudDatacatalogV1beta1Tag: A GoogleCloudDatacatalogV1beta1Tag
      resource to be passed as the request body.
    name: The resource name of the tag in URL format. Example: * projects/{pro
      ject_id}/locations/{location}/entrygroups/{entry_group_id}/entries/{entr
      y_id}/tags/{tag_id} where `tag_id` is a system-generated identifier.
      Note that this Tag may not actually be stored in the location in this
      name.
    updateMask: Note: Currently, this parameter can only take `"fields"` as
      value. Names of fields whose values to overwrite on a tag. Currently, a
      tag has the only modifiable field with the name `fields`. In general, if
      this parameter is absent or empty, all modifiable fields are
      overwritten. If such fields are non-required and omitted in the request
      body, their values are emptied.
  """

  googleCloudDatacatalogV1beta1Tag = _messages.MessageField('GoogleCloudDatacatalogV1beta1Tag', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class DatacatalogProjectsLocationsEntryGroupsEntriesTestIamPermissionsRequest(_messages.Message):
  r"""A
  DatacatalogProjectsLocationsEntryGroupsEntriesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class DatacatalogProjectsLocationsEntryGroupsGetIamPolicyRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DatacatalogProjectsLocationsEntryGroupsGetRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsGetRequest object.

  Fields:
    name: Required. The name of the entry group. For example,
      `projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}
      `.
    readMask: The fields to return. If not set or empty, all fields are
      returned.
  """

  name = _messages.StringField(1, required=True)
  readMask = _messages.StringField(2)


class DatacatalogProjectsLocationsEntryGroupsListRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of items to return. Default is 10.
      Max limit is 1000. Throws an invalid argument for `page_size > 1000`.
    pageToken: Optional. Token that specifies which page is requested. If
      empty, the first page is returned.
    parent: Required. The name of the location that contains the entry groups,
      which can be provided in URL format. Example: *
      projects/{project_id}/locations/{location}
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DatacatalogProjectsLocationsEntryGroupsPatchRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsPatchRequest object.

  Fields:
    googleCloudDatacatalogV1beta1EntryGroup: A
      GoogleCloudDatacatalogV1beta1EntryGroup resource to be passed as the
      request body.
    name: The resource name of the entry group in URL format. Example: *
      projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}
      Note that this EntryGroup and its child resources may not actually be
      stored in the location in this name.
    updateMask: Names of fields whose values to overwrite on an entry group.
      If this parameter is absent or empty, all modifiable fields are
      overwritten. If such fields are non-required and omitted in the request
      body, their values are emptied.
  """

  googleCloudDatacatalogV1beta1EntryGroup = _messages.MessageField('GoogleCloudDatacatalogV1beta1EntryGroup', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class DatacatalogProjectsLocationsEntryGroupsSetIamPolicyRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class DatacatalogProjectsLocationsEntryGroupsTagsCreateRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsTagsCreateRequest object.

  Fields:
    googleCloudDatacatalogV1beta1Tag: A GoogleCloudDatacatalogV1beta1Tag
      resource to be passed as the request body.
    parent: Required. The name of the resource to attach this tag to. Tags can
      be attached to Entries. Example: * projects/{project_id}/locations/{loca
      tion}/entryGroups/{entry_group_id}/entries/{entry_id} Note that this Tag
      and its child resources may not actually be stored in the location in
      this name.
  """

  googleCloudDatacatalogV1beta1Tag = _messages.MessageField('GoogleCloudDatacatalogV1beta1Tag', 1)
  parent = _messages.StringField(2, required=True)


class DatacatalogProjectsLocationsEntryGroupsTagsDeleteRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsTagsDeleteRequest object.

  Fields:
    name: Required. The name of the tag to delete. Example: * projects/{projec
      t_id}/locations/{location}/entryGroups/{entry_group_id}/entries/{entry_i
      d}/tags/{tag_id}
  """

  name = _messages.StringField(1, required=True)


class DatacatalogProjectsLocationsEntryGroupsTagsListRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsTagsListRequest object.

  Fields:
    pageSize: The maximum number of tags to return. Default is 10. Max limit
      is 1000.
    pageToken: Token that specifies which page is requested. If empty, the
      first page is returned.
    parent: Required. The name of the Data Catalog resource to list the tags
      of. The resource could be an Entry or an EntryGroup. Examples: *
      projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}
      * projects/{project_id}/locations/{location}/entryGroups/{entry_group_id
      }/entries/{entry_id}
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DatacatalogProjectsLocationsEntryGroupsTagsPatchRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsTagsPatchRequest object.

  Fields:
    googleCloudDatacatalogV1beta1Tag: A GoogleCloudDatacatalogV1beta1Tag
      resource to be passed as the request body.
    name: The resource name of the tag in URL format. Example: * projects/{pro
      ject_id}/locations/{location}/entrygroups/{entry_group_id}/entries/{entr
      y_id}/tags/{tag_id} where `tag_id` is a system-generated identifier.
      Note that this Tag may not actually be stored in the location in this
      name.
    updateMask: Note: Currently, this parameter can only take `"fields"` as
      value. Names of fields whose values to overwrite on a tag. Currently, a
      tag has the only modifiable field with the name `fields`. In general, if
      this parameter is absent or empty, all modifiable fields are
      overwritten. If such fields are non-required and omitted in the request
      body, their values are emptied.
  """

  googleCloudDatacatalogV1beta1Tag = _messages.MessageField('GoogleCloudDatacatalogV1beta1Tag', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class DatacatalogProjectsLocationsEntryGroupsTestIamPermissionsRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsEntryGroupsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class DatacatalogProjectsLocationsTagTemplatesCreateRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTagTemplatesCreateRequest object.

  Fields:
    googleCloudDatacatalogV1beta1TagTemplate: A
      GoogleCloudDatacatalogV1beta1TagTemplate resource to be passed as the
      request body.
    parent: Required. The name of the project and the template location
      [region](https://cloud.google.com/data-catalog/docs/concepts/regions.
      Example: * projects/{project_id}/locations/us-central1
    tagTemplateId: Required. The id of the tag template to create.
  """

  googleCloudDatacatalogV1beta1TagTemplate = _messages.MessageField('GoogleCloudDatacatalogV1beta1TagTemplate', 1)
  parent = _messages.StringField(2, required=True)
  tagTemplateId = _messages.StringField(3)


class DatacatalogProjectsLocationsTagTemplatesDeleteRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTagTemplatesDeleteRequest object.

  Fields:
    force: Required. Currently, this field must always be set to `true`. This
      confirms the deletion of any possible tags using this template. `force =
      false` will be supported in the future.
    name: Required. The name of the tag template to delete. Example: *
      projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id
      }
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class DatacatalogProjectsLocationsTagTemplatesFieldsCreateRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTagTemplatesFieldsCreateRequest object.

  Fields:
    googleCloudDatacatalogV1beta1TagTemplateField: A
      GoogleCloudDatacatalogV1beta1TagTemplateField resource to be passed as
      the request body.
    parent: Required. The name of the project and the template location
      [region](https://cloud.google.com/data-catalog/docs/concepts/regions).
      Example: * projects/{project_id}/locations/us-
      central1/tagTemplates/{tag_template_id}
    tagTemplateFieldId: Required. The ID of the tag template field to create.
      Field ids can contain letters (both uppercase and lowercase), numbers
      (0-9), underscores (_) and dashes (-). Field IDs must be at least 1
      character long and at most 128 characters long. Field IDs must also be
      unique within their template.
  """

  googleCloudDatacatalogV1beta1TagTemplateField = _messages.MessageField('GoogleCloudDatacatalogV1beta1TagTemplateField', 1)
  parent = _messages.StringField(2, required=True)
  tagTemplateFieldId = _messages.StringField(3)


class DatacatalogProjectsLocationsTagTemplatesFieldsDeleteRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTagTemplatesFieldsDeleteRequest object.

  Fields:
    force: Required. Currently, this field must always be set to `true`. This
      confirms the deletion of this field from any tags using this field.
      `force = false` will be supported in the future.
    name: Required. The name of the tag template field to delete. Example: * p
      rojects/{project_id}/locations/{location}/tagTemplates/{tag_template_id}
      /fields/{tag_template_field_id}
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class DatacatalogProjectsLocationsTagTemplatesFieldsEnumValuesRenameRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTagTemplatesFieldsEnumValuesRenameRequest
  object.

  Fields:
    googleCloudDatacatalogV1beta1RenameTagTemplateFieldEnumValueRequest: A
      GoogleCloudDatacatalogV1beta1RenameTagTemplateFieldEnumValueRequest
      resource to be passed as the request body.
    name: Required. The name of the enum field value. Example: * projects/{pro
      ject_id}/locations/{location}/tagTemplates/{tag_template_id}/fields/{tag
      _template_field_id}/enumValues/{enum_value_display_name}
  """

  googleCloudDatacatalogV1beta1RenameTagTemplateFieldEnumValueRequest = _messages.MessageField('GoogleCloudDatacatalogV1beta1RenameTagTemplateFieldEnumValueRequest', 1)
  name = _messages.StringField(2, required=True)


class DatacatalogProjectsLocationsTagTemplatesFieldsPatchRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTagTemplatesFieldsPatchRequest object.

  Fields:
    googleCloudDatacatalogV1beta1TagTemplateField: A
      GoogleCloudDatacatalogV1beta1TagTemplateField resource to be passed as
      the request body.
    name: Required. The name of the tag template field. Example: * projects/{p
      roject_id}/locations/{location}/tagTemplates/{tag_template_id}/fields/{t
      ag_template_field_id}
    updateMask: Optional. Names of fields whose values to overwrite on an
      individual field of a tag template. The following fields are modifiable:
      * `display_name` * `type.enum_type` * `is_required` If this parameter is
      absent or empty, all modifiable fields are overwritten. If such fields
      are non-required and omitted in the request body, their values are
      emptied with one exception: when updating an enum type, the provided
      values are merged with the existing values. Therefore, enum values can
      only be added, existing enum values cannot be deleted or renamed.
      Additionally, updating a template field from optional to required is
      *not* allowed.
  """

  googleCloudDatacatalogV1beta1TagTemplateField = _messages.MessageField('GoogleCloudDatacatalogV1beta1TagTemplateField', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class DatacatalogProjectsLocationsTagTemplatesFieldsRenameRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTagTemplatesFieldsRenameRequest object.

  Fields:
    googleCloudDatacatalogV1beta1RenameTagTemplateFieldRequest: A
      GoogleCloudDatacatalogV1beta1RenameTagTemplateFieldRequest resource to
      be passed as the request body.
    name: Required. The name of the tag template. Example: * projects/{project
      _id}/locations/{location}/tagTemplates/{tag_template_id}/fields/{tag_tem
      plate_field_id}
  """

  googleCloudDatacatalogV1beta1RenameTagTemplateFieldRequest = _messages.MessageField('GoogleCloudDatacatalogV1beta1RenameTagTemplateFieldRequest', 1)
  name = _messages.StringField(2, required=True)


class DatacatalogProjectsLocationsTagTemplatesGetIamPolicyRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTagTemplatesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DatacatalogProjectsLocationsTagTemplatesGetRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTagTemplatesGetRequest object.

  Fields:
    name: Required. The name of the tag template. Example: *
      projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id
      }
  """

  name = _messages.StringField(1, required=True)


class DatacatalogProjectsLocationsTagTemplatesPatchRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTagTemplatesPatchRequest object.

  Fields:
    googleCloudDatacatalogV1beta1TagTemplate: A
      GoogleCloudDatacatalogV1beta1TagTemplate resource to be passed as the
      request body.
    name: The resource name of the tag template in URL format. Example: *
      projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id
      } Note that this TagTemplate and its child resources may not actually be
      stored in the location in this name.
    updateMask: Names of fields whose values to overwrite on a tag template.
      Currently, only `display_name` can be overwritten. In general, if this
      parameter is absent or empty, all modifiable fields are overwritten. If
      such fields are non-required and omitted in the request body, their
      values are emptied.
  """

  googleCloudDatacatalogV1beta1TagTemplate = _messages.MessageField('GoogleCloudDatacatalogV1beta1TagTemplate', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class DatacatalogProjectsLocationsTagTemplatesSetIamPolicyRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTagTemplatesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class DatacatalogProjectsLocationsTagTemplatesTestIamPermissionsRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTagTemplatesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class DatacatalogProjectsLocationsTaxonomiesCreateRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTaxonomiesCreateRequest object.

  Fields:
    googleCloudDatacatalogV1beta1Taxonomy: A
      GoogleCloudDatacatalogV1beta1Taxonomy resource to be passed as the
      request body.
    parent: Required. Resource name of the project that the taxonomy will
      belong to.
  """

  googleCloudDatacatalogV1beta1Taxonomy = _messages.MessageField('GoogleCloudDatacatalogV1beta1Taxonomy', 1)
  parent = _messages.StringField(2, required=True)


class DatacatalogProjectsLocationsTaxonomiesDeleteRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTaxonomiesDeleteRequest object.

  Fields:
    name: Required. Resource name of the taxonomy to be deleted. All policy
      tags in this taxonomy will also be deleted.
  """

  name = _messages.StringField(1, required=True)


class DatacatalogProjectsLocationsTaxonomiesExportRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTaxonomiesExportRequest object.

  Fields:
    parent: Required. Resource name of the project that taxonomies to be
      exported will share.
    serializedTaxonomies: Export taxonomies as serialized taxonomies.
    taxonomies: Required. Resource names of the taxonomies to be exported.
  """

  parent = _messages.StringField(1, required=True)
  serializedTaxonomies = _messages.BooleanField(2)
  taxonomies = _messages.StringField(3, repeated=True)


class DatacatalogProjectsLocationsTaxonomiesGetIamPolicyRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTaxonomiesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DatacatalogProjectsLocationsTaxonomiesGetRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTaxonomiesGetRequest object.

  Fields:
    name: Required. Resource name of the requested taxonomy.
  """

  name = _messages.StringField(1, required=True)


class DatacatalogProjectsLocationsTaxonomiesImportRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTaxonomiesImportRequest object.

  Fields:
    googleCloudDatacatalogV1beta1ImportTaxonomiesRequest: A
      GoogleCloudDatacatalogV1beta1ImportTaxonomiesRequest resource to be
      passed as the request body.
    parent: Required. Resource name of project that the imported taxonomies
      will belong to.
  """

  googleCloudDatacatalogV1beta1ImportTaxonomiesRequest = _messages.MessageField('GoogleCloudDatacatalogV1beta1ImportTaxonomiesRequest', 1)
  parent = _messages.StringField(2, required=True)


class DatacatalogProjectsLocationsTaxonomiesListRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTaxonomiesListRequest object.

  Fields:
    filter: Supported field for filter is 'service' and value is 'dataplex'.
      Eg: service=dataplex.
    pageSize: The maximum number of items to return. Must be a value between 1
      and 1000. If not set, defaults to 50.
    pageToken: The next_page_token value returned from a previous list
      request, if any. If not set, defaults to an empty string.
    parent: Required. Resource name of the project to list the taxonomies of.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class DatacatalogProjectsLocationsTaxonomiesPatchRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTaxonomiesPatchRequest object.

  Fields:
    googleCloudDatacatalogV1beta1Taxonomy: A
      GoogleCloudDatacatalogV1beta1Taxonomy resource to be passed as the
      request body.
    name: Output only. Resource name of this taxonomy, whose format is:
      "projects/{project_number}/locations/{location_id}/taxonomies/{id}".
    updateMask: The update mask applies to the resource. For the `FieldMask`
      definition, see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask If not set, defaults to
      all of the fields that are allowed to update.
  """

  googleCloudDatacatalogV1beta1Taxonomy = _messages.MessageField('GoogleCloudDatacatalogV1beta1Taxonomy', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class DatacatalogProjectsLocationsTaxonomiesPolicyTagsCreateRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTaxonomiesPolicyTagsCreateRequest object.

  Fields:
    googleCloudDatacatalogV1beta1PolicyTag: A
      GoogleCloudDatacatalogV1beta1PolicyTag resource to be passed as the
      request body.
    parent: Required. Resource name of the taxonomy that the policy tag will
      belong to.
  """

  googleCloudDatacatalogV1beta1PolicyTag = _messages.MessageField('GoogleCloudDatacatalogV1beta1PolicyTag', 1)
  parent = _messages.StringField(2, required=True)


class DatacatalogProjectsLocationsTaxonomiesPolicyTagsDeleteRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTaxonomiesPolicyTagsDeleteRequest object.

  Fields:
    name: Required. Resource name of the policy tag to be deleted. All of its
      descendant policy tags will also be deleted.
  """

  name = _messages.StringField(1, required=True)


class DatacatalogProjectsLocationsTaxonomiesPolicyTagsGetIamPolicyRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTaxonomiesPolicyTagsGetIamPolicyRequest
  object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DatacatalogProjectsLocationsTaxonomiesPolicyTagsGetRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTaxonomiesPolicyTagsGetRequest object.

  Fields:
    name: Required. Resource name of the requested policy tag.
  """

  name = _messages.StringField(1, required=True)


class DatacatalogProjectsLocationsTaxonomiesPolicyTagsListRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTaxonomiesPolicyTagsListRequest object.

  Fields:
    pageSize: The maximum number of items to return. Must be a value between 1
      and 1000. If not set, defaults to 50.
    pageToken: The next_page_token value returned from a previous List
      request, if any. If not set, defaults to an empty string.
    parent: Required. Resource name of the taxonomy to list the policy tags
      of.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DatacatalogProjectsLocationsTaxonomiesPolicyTagsPatchRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTaxonomiesPolicyTagsPatchRequest object.

  Fields:
    googleCloudDatacatalogV1beta1PolicyTag: A
      GoogleCloudDatacatalogV1beta1PolicyTag resource to be passed as the
      request body.
    name: Output only. Resource name of this policy tag, whose format is: "pro
      jects/{project_number}/locations/{location_id}/taxonomies/{taxonomy_id}/
      policyTags/{id}".
    updateMask: The update mask applies to the resource. Only display_name,
      description and parent_policy_tag can be updated and thus can be listed
      in the mask. If update_mask is not provided, all allowed fields (i.e.
      display_name, description and parent) will be updated. For more
      information including the `FieldMask` definition, see
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#fieldmask If not set, defaults to
      all of the fields that are allowed to update.
  """

  googleCloudDatacatalogV1beta1PolicyTag = _messages.MessageField('GoogleCloudDatacatalogV1beta1PolicyTag', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class DatacatalogProjectsLocationsTaxonomiesPolicyTagsSetIamPolicyRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTaxonomiesPolicyTagsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class DatacatalogProjectsLocationsTaxonomiesPolicyTagsTestIamPermissionsRequest(_messages.Message):
  r"""A
  DatacatalogProjectsLocationsTaxonomiesPolicyTagsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class DatacatalogProjectsLocationsTaxonomiesSetIamPolicyRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTaxonomiesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class DatacatalogProjectsLocationsTaxonomiesTestIamPermissionsRequest(_messages.Message):
  r"""A DatacatalogProjectsLocationsTaxonomiesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GetIamPolicyRequest(_messages.Message):
  r"""Request message for `GetIamPolicy` method.

  Fields:
    options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
      `GetIamPolicy`.
  """

  options = _messages.MessageField('GetPolicyOptions', 1)


class GetPolicyOptions(_messages.Message):
  r"""Encapsulates settings provided to GetIamPolicy.

  Fields:
    requestedPolicyVersion: Optional. The maximum policy version that will be
      used to format the policy. Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected. Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset. The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1. To learn which resources support conditions in
      their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GoogleCloudDatacatalogV1BigQueryConnectionSpec(_messages.Message):
  r"""Specification for the BigQuery connection.

  Enums:
    ConnectionTypeValueValuesEnum: The type of the BigQuery connection.

  Fields:
    cloudSql: Specification for the BigQuery connection to a Cloud SQL
      instance.
    connectionType: The type of the BigQuery connection.
    hasCredential: True if there are credentials attached to the BigQuery
      connection; false otherwise.
  """

  class ConnectionTypeValueValuesEnum(_messages.Enum):
    r"""The type of the BigQuery connection.

    Values:
      CONNECTION_TYPE_UNSPECIFIED: Unspecified type.
      CLOUD_SQL: Cloud SQL connection.
    """
    CONNECTION_TYPE_UNSPECIFIED = 0
    CLOUD_SQL = 1

  cloudSql = _messages.MessageField('GoogleCloudDatacatalogV1CloudSqlBigQueryConnectionSpec', 1)
  connectionType = _messages.EnumField('ConnectionTypeValueValuesEnum', 2)
  hasCredential = _messages.BooleanField(3)


class GoogleCloudDatacatalogV1BigQueryDateShardedSpec(_messages.Message):
  r"""Specification for a group of BigQuery tables with the `[prefix]YYYYMMDD`
  name pattern. For more information, see [Introduction to partitioned tables]
  (https://cloud.google.com/bigquery/docs/partitioned-
  tables#partitioning_versus_sharding).

  Fields:
    dataset: Output only. The Data Catalog resource name of the dataset entry
      the current table belongs to. For example: `projects/{PROJECT_ID}/locati
      ons/{LOCATION}/entrygroups/{ENTRY_GROUP_ID}/entries/{ENTRY_ID}`.
    latestShardResource: Output only. BigQuery resource name of the latest
      shard.
    shardCount: Output only. Total number of shards.
    tablePrefix: Output only. The table name prefix of the shards. The name of
      any given shard is `[table_prefix]YYYYMMDD`. For example, for the
      `MyTable20180101` shard, the `table_prefix` is `MyTable`.
  """

  dataset = _messages.StringField(1)
  latestShardResource = _messages.StringField(2)
  shardCount = _messages.IntegerField(3)
  tablePrefix = _messages.StringField(4)


class GoogleCloudDatacatalogV1BigQueryRoutineSpec(_messages.Message):
  r"""Fields specific for BigQuery routines.

  Fields:
    importedLibraries: Paths of the imported libraries.
  """

  importedLibraries = _messages.StringField(1, repeated=True)


class GoogleCloudDatacatalogV1BigQueryTableSpec(_messages.Message):
  r"""Describes a BigQuery table.

  Enums:
    TableSourceTypeValueValuesEnum: Output only. The table source type.

  Fields:
    tableSourceType: Output only. The table source type.
    tableSpec: Specification of a BigQuery table. Populated only if the
      `table_source_type` is `BIGQUERY_TABLE`.
    viewSpec: Table view specification. Populated only if the
      `table_source_type` is `BIGQUERY_VIEW`.
  """

  class TableSourceTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The table source type.

    Values:
      TABLE_SOURCE_TYPE_UNSPECIFIED: Default unknown type.
      BIGQUERY_VIEW: Table view.
      BIGQUERY_TABLE: BigQuery native table.
      BIGQUERY_MATERIALIZED_VIEW: BigQuery materialized view.
    """
    TABLE_SOURCE_TYPE_UNSPECIFIED = 0
    BIGQUERY_VIEW = 1
    BIGQUERY_TABLE = 2
    BIGQUERY_MATERIALIZED_VIEW = 3

  tableSourceType = _messages.EnumField('TableSourceTypeValueValuesEnum', 1)
  tableSpec = _messages.MessageField('GoogleCloudDatacatalogV1TableSpec', 2)
  viewSpec = _messages.MessageField('GoogleCloudDatacatalogV1ViewSpec', 3)


class GoogleCloudDatacatalogV1BusinessContext(_messages.Message):
  r"""Business Context of the entry.

  Fields:
    contacts: Contact people for the entry.
    entryOverview: Entry overview fields for rich text descriptions of
      entries.
  """

  contacts = _messages.MessageField('GoogleCloudDatacatalogV1Contacts', 1)
  entryOverview = _messages.MessageField('GoogleCloudDatacatalogV1EntryOverview', 2)


class GoogleCloudDatacatalogV1CloudBigtableInstanceSpec(_messages.Message):
  r"""Specification that applies to Instance entries that are part of
  `CLOUD_BIGTABLE` system. (user_specified_type)

  Fields:
    cloudBigtableClusterSpecs: The list of clusters for the Instance.
  """

  cloudBigtableClusterSpecs = _messages.MessageField('GoogleCloudDatacatalogV1CloudBigtableInstanceSpecCloudBigtableClusterSpec', 1, repeated=True)


class GoogleCloudDatacatalogV1CloudBigtableInstanceSpecCloudBigtableClusterSpec(_messages.Message):
  r"""Spec that applies to clusters of an Instance of Cloud Bigtable.

  Fields:
    displayName: Name of the cluster.
    linkedResource: A link back to the parent resource, in this case Instance.
    location: Location of the cluster, typically a Cloud zone.
    type: Type of the resource. For a cluster this would be "CLUSTER".
  """

  displayName = _messages.StringField(1)
  linkedResource = _messages.StringField(2)
  location = _messages.StringField(3)
  type = _messages.StringField(4)


class GoogleCloudDatacatalogV1CloudBigtableSystemSpec(_messages.Message):
  r"""Specification that applies to all entries that are part of
  `CLOUD_BIGTABLE` system (user_specified_type)

  Fields:
    instanceDisplayName: Display name of the Instance. This is user specified
      and different from the resource name.
  """

  instanceDisplayName = _messages.StringField(1)


class GoogleCloudDatacatalogV1CloudSqlBigQueryConnectionSpec(_messages.Message):
  r"""Specification for the BigQuery connection to a Cloud SQL instance.

  Enums:
    TypeValueValuesEnum: Type of the Cloud SQL database.

  Fields:
    database: Database name.
    instanceId: Cloud SQL instance ID in the format of
      `project:location:instance`.
    type: Type of the Cloud SQL database.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of the Cloud SQL database.

    Values:
      DATABASE_TYPE_UNSPECIFIED: Unspecified database type.
      POSTGRES: Cloud SQL for PostgreSQL.
      MYSQL: Cloud SQL for MySQL.
    """
    DATABASE_TYPE_UNSPECIFIED = 0
    POSTGRES = 1
    MYSQL = 2

  database = _messages.StringField(1)
  instanceId = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class GoogleCloudDatacatalogV1ColumnSchema(_messages.Message):
  r"""A column within a schema. Columns can be nested inside other columns.

  Enums:
    HighestIndexingTypeValueValuesEnum: Optional. Most important inclusion of
      this column.

  Fields:
    column: Required. Name of the column. Must be a UTF-8 string without dots
      (.). The maximum size is 64 bytes.
    defaultValue: Optional. Default value for the column.
    description: Optional. Description of the column. Default value is an
      empty string. The description must be a UTF-8 string with the maximum
      size of 2000 bytes.
    gcRule: Optional. Garbage collection policy for the column or column
      family. Applies to systems like Cloud Bigtable.
    highestIndexingType: Optional. Most important inclusion of this column.
    lookerColumnSpec: Looker specific column info of this column.
    mode: Optional. A column's mode indicates whether values in this column
      are required, nullable, or repeated. Only `NULLABLE`, `REQUIRED`, and
      `REPEATED` values are supported. Default mode is `NULLABLE`.
    ordinalPosition: Optional. Ordinal position
    subcolumns: Optional. Schema of sub-columns. A column can have zero or
      more sub-columns.
    type: Required. Type of the column. Must be a UTF-8 string with the
      maximum size of 128 bytes.
  """

  class HighestIndexingTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Most important inclusion of this column.

    Values:
      INDEXING_TYPE_UNSPECIFIED: Unspecified.
      INDEXING_TYPE_NONE: Column not a part of an index.
      INDEXING_TYPE_NON_UNIQUE: Column Part of non unique index.
      INDEXING_TYPE_UNIQUE: Column part of unique index.
      INDEXING_TYPE_PRIMARY_KEY: Column part of the primary key.
    """
    INDEXING_TYPE_UNSPECIFIED = 0
    INDEXING_TYPE_NONE = 1
    INDEXING_TYPE_NON_UNIQUE = 2
    INDEXING_TYPE_UNIQUE = 3
    INDEXING_TYPE_PRIMARY_KEY = 4

  column = _messages.StringField(1)
  defaultValue = _messages.StringField(2)
  description = _messages.StringField(3)
  gcRule = _messages.StringField(4)
  highestIndexingType = _messages.EnumField('HighestIndexingTypeValueValuesEnum', 5)
  lookerColumnSpec = _messages.MessageField('GoogleCloudDatacatalogV1ColumnSchemaLookerColumnSpec', 6)
  mode = _messages.StringField(7)
  ordinalPosition = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  subcolumns = _messages.MessageField('GoogleCloudDatacatalogV1ColumnSchema', 9, repeated=True)
  type = _messages.StringField(10)


class GoogleCloudDatacatalogV1ColumnSchemaLookerColumnSpec(_messages.Message):
  r"""Column info specific to Looker System.

  Enums:
    TypeValueValuesEnum: Looker specific column type of this column.

  Fields:
    type: Looker specific column type of this column.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Looker specific column type of this column.

    Values:
      LOOKER_COLUMN_TYPE_UNSPECIFIED: Unspecified.
      DIMENSION: Dimension.
      DIMENSION_GROUP: Dimension group - parent for Dimension.
      FILTER: Filter.
      MEASURE: Measure.
      PARAMETER: Parameter.
    """
    LOOKER_COLUMN_TYPE_UNSPECIFIED = 0
    DIMENSION = 1
    DIMENSION_GROUP = 2
    FILTER = 3
    MEASURE = 4
    PARAMETER = 5

  type = _messages.EnumField('TypeValueValuesEnum', 1)


class GoogleCloudDatacatalogV1CommonUsageStats(_messages.Message):
  r"""Common statistics on the entry's usage. They can be set on any system.

  Fields:
    viewCount: View count in source system.
  """

  viewCount = _messages.IntegerField(1)


class GoogleCloudDatacatalogV1Contacts(_messages.Message):
  r"""Contact people for the entry.

  Fields:
    people: The list of contact people for the entry.
  """

  people = _messages.MessageField('GoogleCloudDatacatalogV1ContactsPerson', 1, repeated=True)


class GoogleCloudDatacatalogV1ContactsPerson(_messages.Message):
  r"""A contact person for the entry.

  Fields:
    designation: Designation of the person, for example, Data Steward.
    email: Email of the person in the format of `john.doe@xyz`, ``, or `John
      Doe`.
  """

  designation = _messages.StringField(1)
  email = _messages.StringField(2)


class GoogleCloudDatacatalogV1DataSource(_messages.Message):
  r"""Physical location of an entry.

  Enums:
    ServiceValueValuesEnum: Service that physically stores the data.

  Fields:
    resource: Full name of a resource as defined by the service. For example:
      `//bigquery.googleapis.com/projects/{PROJECT_ID}/locations/{LOCATION}/da
      tasets/{DATASET_ID}/tables/{TABLE_ID}`
    service: Service that physically stores the data.
    sourceEntry: Output only. Data Catalog entry name, if applicable.
    storageProperties: Detailed properties of the underlying storage.
  """

  class ServiceValueValuesEnum(_messages.Enum):
    r"""Service that physically stores the data.

    Values:
      SERVICE_UNSPECIFIED: Default unknown service.
      CLOUD_STORAGE: Google Cloud Storage service.
      BIGQUERY: BigQuery service.
    """
    SERVICE_UNSPECIFIED = 0
    CLOUD_STORAGE = 1
    BIGQUERY = 2

  resource = _messages.StringField(1)
  service = _messages.EnumField('ServiceValueValuesEnum', 2)
  sourceEntry = _messages.StringField(3)
  storageProperties = _messages.MessageField('GoogleCloudDatacatalogV1StorageProperties', 4)


class GoogleCloudDatacatalogV1DataSourceConnectionSpec(_messages.Message):
  r"""Specification that applies to a data source connection. Valid only for
  entries with the `DATA_SOURCE_CONNECTION` type. Only one of internal specs
  can be set at the time, and cannot be changed later.

  Fields:
    bigqueryConnectionSpec: Output only. Fields specific to BigQuery
      connections.
  """

  bigqueryConnectionSpec = _messages.MessageField('GoogleCloudDatacatalogV1BigQueryConnectionSpec', 1)


class GoogleCloudDatacatalogV1DatabaseTableSpec(_messages.Message):
  r"""Specification that applies to a table resource. Valid only for entries
  with the `TABLE` type.

  Enums:
    TypeValueValuesEnum: Type of this table.

  Fields:
    databaseViewSpec: Spec what aplies to tables that are actually views. Not
      set for "real" tables.
    dataplexTable: Output only. Fields specific to a Dataplex table and
      present only in the Dataplex table entries.
    type: Type of this table.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this table.

    Values:
      TABLE_TYPE_UNSPECIFIED: Default unknown table type.
      NATIVE: Native table.
      EXTERNAL: External table.
    """
    TABLE_TYPE_UNSPECIFIED = 0
    NATIVE = 1
    EXTERNAL = 2

  databaseViewSpec = _messages.MessageField('GoogleCloudDatacatalogV1DatabaseTableSpecDatabaseViewSpec', 1)
  dataplexTable = _messages.MessageField('GoogleCloudDatacatalogV1DataplexTableSpec', 2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class GoogleCloudDatacatalogV1DatabaseTableSpecDatabaseViewSpec(_messages.Message):
  r"""Specification that applies to database view.

  Enums:
    ViewTypeValueValuesEnum: Type of this view.

  Fields:
    baseTable: Name of a singular table this view reflects one to one.
    sqlQuery: SQL query used to generate this view.
    viewType: Type of this view.
  """

  class ViewTypeValueValuesEnum(_messages.Enum):
    r"""Type of this view.

    Values:
      VIEW_TYPE_UNSPECIFIED: Default unknown view type.
      STANDARD_VIEW: Standard view.
      MATERIALIZED_VIEW: Materialized view.
    """
    VIEW_TYPE_UNSPECIFIED = 0
    STANDARD_VIEW = 1
    MATERIALIZED_VIEW = 2

  baseTable = _messages.StringField(1)
  sqlQuery = _messages.StringField(2)
  viewType = _messages.EnumField('ViewTypeValueValuesEnum', 3)


class GoogleCloudDatacatalogV1DataplexExternalTable(_messages.Message):
  r"""External table registered by Dataplex. Dataplex publishes data
  discovered from an asset into multiple other systems (BigQuery, DPMS) in
  form of tables. We call them "external tables". External tables are also
  synced into the Data Catalog. This message contains pointers to those
  external tables (fully qualified name, resource name et cetera) within the
  Data Catalog.

  Enums:
    SystemValueValuesEnum: Service in which the external table is registered.

  Fields:
    dataCatalogEntry: Name of the Data Catalog entry representing the external
      table.
    fullyQualifiedName: Fully qualified name (FQN) of the external table.
    googleCloudResource: Google Cloud resource name of the external table.
    system: Service in which the external table is registered.
  """

  class SystemValueValuesEnum(_messages.Enum):
    r"""Service in which the external table is registered.

    Values:
      INTEGRATED_SYSTEM_UNSPECIFIED: Default unknown system.
      BIGQUERY: BigQuery.
      CLOUD_PUBSUB: Cloud Pub/Sub.
      DATAPROC_METASTORE: Dataproc Metastore.
      DATAPLEX: Dataplex.
      CLOUD_SPANNER: Cloud Spanner
      CLOUD_BIGTABLE: Cloud Bigtable
      CLOUD_SQL: Cloud Sql
      LOOKER: Looker
    """
    INTEGRATED_SYSTEM_UNSPECIFIED = 0
    BIGQUERY = 1
    CLOUD_PUBSUB = 2
    DATAPROC_METASTORE = 3
    DATAPLEX = 4
    CLOUD_SPANNER = 5
    CLOUD_BIGTABLE = 6
    CLOUD_SQL = 7
    LOOKER = 8

  dataCatalogEntry = _messages.StringField(1)
  fullyQualifiedName = _messages.StringField(2)
  googleCloudResource = _messages.StringField(3)
  system = _messages.EnumField('SystemValueValuesEnum', 4)


class GoogleCloudDatacatalogV1DataplexFilesetSpec(_messages.Message):
  r"""Entry specyfication for a Dataplex fileset.

  Fields:
    dataplexSpec: Common Dataplex fields.
  """

  dataplexSpec = _messages.MessageField('GoogleCloudDatacatalogV1DataplexSpec', 1)


class GoogleCloudDatacatalogV1DataplexSpec(_messages.Message):
  r"""Common Dataplex fields.

  Fields:
    asset: Fully qualified resource name of an asset in Dataplex, to which the
      underlying data source (Cloud Storage bucket or BigQuery dataset) of the
      entity is attached.
    compressionFormat: Compression format of the data, e.g., zip, gzip etc.
    dataFormat: Format of the data.
    projectId: Project ID of the underlying Cloud Storage or BigQuery data.
      Note that this may not be the same project as the correspondingly
      Dataplex lake / zone / asset.
  """

  asset = _messages.StringField(1)
  compressionFormat = _messages.StringField(2)
  dataFormat = _messages.MessageField('GoogleCloudDatacatalogV1PhysicalSchema', 3)
  projectId = _messages.StringField(4)


class GoogleCloudDatacatalogV1DataplexTableSpec(_messages.Message):
  r"""Entry specification for a Dataplex table.

  Fields:
    dataplexSpec: Common Dataplex fields.
    externalTables: List of external tables registered by Dataplex in other
      systems based on the same underlying data. External tables allow to
      query this data in those systems.
    userManaged: Indicates if the table schema is managed by the user or not.
  """

  dataplexSpec = _messages.MessageField('GoogleCloudDatacatalogV1DataplexSpec', 1)
  externalTables = _messages.MessageField('GoogleCloudDatacatalogV1DataplexExternalTable', 2, repeated=True)
  userManaged = _messages.BooleanField(3)


class GoogleCloudDatacatalogV1DumpItem(_messages.Message):
  r"""Wrapper for any item that can be contained in the dump.

  Fields:
    taggedEntry: Entry and its tags.
  """

  taggedEntry = _messages.MessageField('GoogleCloudDatacatalogV1TaggedEntry', 1)


class GoogleCloudDatacatalogV1Entry(_messages.Message):
  r"""Entry metadata. A Data Catalog entry represents another resource in
  Google Cloud Platform (such as a BigQuery dataset or a Pub/Sub topic) or
  outside of it. You can use the `linked_resource` field in the entry resource
  to refer to the original resource ID of the source system. An entry resource
  contains resource details, for example, its schema. Additionally, you can
  attach flexible metadata to an entry in the form of a Tag.

  Enums:
    IntegratedSystemValueValuesEnum: Output only. Indicates the entry's source
      system that Data Catalog integrates with, such as BigQuery, Pub/Sub, or
      Dataproc Metastore.
    TypeValueValuesEnum: The type of the entry. For details, see
      [`EntryType`](#entrytype).

  Messages:
    LabelsValue: Cloud labels attached to the entry. In Data Catalog, you can
      create and modify labels attached only to custom entries. Synced entries
      have unmodifiable labels that come from the source system.

  Fields:
    bigqueryDateShardedSpec: Output only. Specification for a group of
      BigQuery tables with the `[prefix]YYYYMMDD` name pattern. For more
      information, see [Introduction to partitioned tables]
      (https://cloud.google.com/bigquery/docs/partitioned-
      tables#partitioning_versus_sharding).
    bigqueryTableSpec: Output only. Specification that applies to a BigQuery
      table. Valid only for entries with the `TABLE` type.
    businessContext: Business Context of the entry. Not supported for BigQuery
      datasets
    cloudBigtableSystemSpec: Specification that applies to Cloud Bigtable
      system. Only settable when `integrated_system` is equal to
      `CLOUD_BIGTABLE`
    dataSource: Output only. Physical location of the entry.
    dataSourceConnectionSpec: Specification that applies to a data source
      connection. Valid only for entries with the `DATA_SOURCE_CONNECTION`
      type.
    databaseTableSpec: Specification that applies to a table resource. Valid
      only for entries with the `TABLE` or `EXPLORE` type.
    description: Entry description that can consist of several sentences or
      paragraphs that describe entry contents. The description must not
      contain Unicode non-characters as well as C0 and C1 control codes except
      tabs (HT), new lines (LF), carriage returns (CR), and page breaks (FF).
      The maximum size is 2000 bytes when encoded in UTF-8. Default value is
      an empty string.
    displayName: Display name of an entry. The maximum size is 500 bytes when
      encoded in UTF-8. Default value is an empty string.
    filesetSpec: Specification that applies to a fileset resource. Valid only
      for entries with the `FILESET` type.
    fullyQualifiedName: [Fully Qualified Name
      (FQN)](https://cloud.google.com//data-catalog/docs/fully-qualified-
      names) of the resource. Set automatically for entries representing
      resources from synced systems. Settable only during creation, and read-
      only later. Can be used for search and lookup of the entries.
    gcsFilesetSpec: Specification that applies to a Cloud Storage fileset.
      Valid only for entries with the `FILESET` type.
    integratedSystem: Output only. Indicates the entry's source system that
      Data Catalog integrates with, such as BigQuery, Pub/Sub, or Dataproc
      Metastore.
    labels: Cloud labels attached to the entry. In Data Catalog, you can
      create and modify labels attached only to custom entries. Synced entries
      have unmodifiable labels that come from the source system.
    linkedResource: The resource this metadata entry refers to. For Google
      Cloud Platform resources, `linked_resource` is the [Full Resource Name]
      (https://cloud.google.com/apis/design/resource_names#full_resource_name)
      . For example, the `linked_resource` for a table resource from BigQuery
      is: `//bigquery.googleapis.com/projects/{PROJECT_ID}/datasets/{DATASET_I
      D}/tables/{TABLE_ID}` Output only when the entry is one of the types in
      the `EntryType` enum. For entries with a `user_specified_type`, this
      field is optional and defaults to an empty string. The resource string
      must contain only letters (a-z, A-Z), numbers (0-9), underscores (_),
      periods (.), colons (:), slashes (/), dashes (-), and hashes (#). The
      maximum size is 200 bytes when encoded in UTF-8.
    lookerSystemSpec: Specification that applies to Looker sysstem. Only
      settable when `user_specified_system` is equal to `LOOKER`
    name: Output only. The resource name of an entry in URL format. Note: The
      entry itself and its child resources might not be stored in the location
      specified in its name.
    personalDetails: Output only. Additional information related to the entry.
      Private to the current user.
    routineSpec: Specification that applies to a user-defined function or
      procedure. Valid only for entries with the `ROUTINE` type.
    schema: Schema of the entry. An entry might not have any schema attached
      to it.
    serviceSpec: Specification that applies to a Service resource.
    sourceSystemTimestamps: Timestamps from the underlying resource, not from
      the Data Catalog entry. Output only when the entry has a system listed
      in the `IntegratedSystem` enum. For entries with
      `user_specified_system`, this field is optional and defaults to an empty
      timestamp.
    sqlDatabaseSystemSpec: Specification that applies to a relational database
      system. Only settable when `user_specified_system` is equal to
      `SQL_DATABASE`
    type: The type of the entry. For details, see [`EntryType`](#entrytype).
    usageSignal: Resource usage statistics.
    userSpecifiedSystem: Indicates the entry's source system that Data Catalog
      doesn't automatically integrate with. The `user_specified_system` string
      has the following limitations: * Is case insensitive. * Must begin with
      a letter or underscore. * Can only contain letters, numbers, and
      underscores. * Must be at least 1 character and at most 64 characters
      long.
    userSpecifiedType: Custom entry type that doesn't match any of the values
      allowed for input and listed in the `EntryType` enum. When creating an
      entry, first check the type values in the enum. If there are no
      appropriate types for the new entry, provide a custom value, for
      example, `my_special_type`. The `user_specified_type` string has the
      following limitations: * Is case insensitive. * Must begin with a letter
      or underscore. * Can only contain letters, numbers, and underscores. *
      Must be at least 1 character and at most 64 characters long.
  """

  class IntegratedSystemValueValuesEnum(_messages.Enum):
    r"""Output only. Indicates the entry's source system that Data Catalog
    integrates with, such as BigQuery, Pub/Sub, or Dataproc Metastore.

    Values:
      INTEGRATED_SYSTEM_UNSPECIFIED: Default unknown system.
      BIGQUERY: BigQuery.
      CLOUD_PUBSUB: Cloud Pub/Sub.
      DATAPROC_METASTORE: Dataproc Metastore.
      DATAPLEX: Dataplex.
      CLOUD_SPANNER: Cloud Spanner
      CLOUD_BIGTABLE: Cloud Bigtable
      CLOUD_SQL: Cloud Sql
      LOOKER: Looker
    """
    INTEGRATED_SYSTEM_UNSPECIFIED = 0
    BIGQUERY = 1
    CLOUD_PUBSUB = 2
    DATAPROC_METASTORE = 3
    DATAPLEX = 4
    CLOUD_SPANNER = 5
    CLOUD_BIGTABLE = 6
    CLOUD_SQL = 7
    LOOKER = 8

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of the entry. For details, see [`EntryType`](#entrytype).

    Values:
      ENTRY_TYPE_UNSPECIFIED: Default unknown type.
      TABLE: The entry type that has a GoogleSQL schema, including logical
        views.
      MODEL: The type of models. For more information, see [Supported models
        in BigQuery ML](/bigquery/docs/bqml-introduction#supported_models).
      DATA_STREAM: An entry type for streaming entries. For example, a Pub/Sub
        topic.
      FILESET: An entry type for a set of files or objects. For example, a
        Cloud Storage fileset.
      CLUSTER: A group of servers that work together. For example, a Kafka
        cluster.
      DATABASE: A database.
      DATA_SOURCE_CONNECTION: Connection to a data source. For example, a
        BigQuery connection.
      ROUTINE: Routine, for example, a BigQuery routine.
      LAKE: A Dataplex lake.
      ZONE: A Dataplex zone.
      SERVICE: A service, for example, a Dataproc Metastore service.
      DATABASE_SCHEMA: Schema within a relational database.
      DASHBOARD: A Dashboard, for example from Looker.
      EXPLORE: A Looker Explore. For more information, see [Looker Explore
        API] (https://developers.looker.com/api/explorer/4.0/methods/LookmlMod
        el/lookml_model_explore).
      LOOK: A Looker Look. For more information, see [Looker Look API]
        (https://developers.looker.com/api/explorer/4.0/methods/Look).
    """
    ENTRY_TYPE_UNSPECIFIED = 0
    TABLE = 1
    MODEL = 2
    DATA_STREAM = 3
    FILESET = 4
    CLUSTER = 5
    DATABASE = 6
    DATA_SOURCE_CONNECTION = 7
    ROUTINE = 8
    LAKE = 9
    ZONE = 10
    SERVICE = 11
    DATABASE_SCHEMA = 12
    DASHBOARD = 13
    EXPLORE = 14
    LOOK = 15

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cloud labels attached to the entry. In Data Catalog, you can create
    and modify labels attached only to custom entries. Synced entries have
    unmodifiable labels that come from the source system.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bigqueryDateShardedSpec = _messages.MessageField('GoogleCloudDatacatalogV1BigQueryDateShardedSpec', 1)
  bigqueryTableSpec = _messages.MessageField('GoogleCloudDatacatalogV1BigQueryTableSpec', 2)
  businessContext = _messages.MessageField('GoogleCloudDatacatalogV1BusinessContext', 3)
  cloudBigtableSystemSpec = _messages.MessageField('GoogleCloudDatacatalogV1CloudBigtableSystemSpec', 4)
  dataSource = _messages.MessageField('GoogleCloudDatacatalogV1DataSource', 5)
  dataSourceConnectionSpec = _messages.MessageField('GoogleCloudDatacatalogV1DataSourceConnectionSpec', 6)
  databaseTableSpec = _messages.MessageField('GoogleCloudDatacatalogV1DatabaseTableSpec', 7)
  description = _messages.StringField(8)
  displayName = _messages.StringField(9)
  filesetSpec = _messages.MessageField('GoogleCloudDatacatalogV1FilesetSpec', 10)
  fullyQualifiedName = _messages.StringField(11)
  gcsFilesetSpec = _messages.MessageField('GoogleCloudDatacatalogV1GcsFilesetSpec', 12)
  integratedSystem = _messages.EnumField('IntegratedSystemValueValuesEnum', 13)
  labels = _messages.MessageField('LabelsValue', 14)
  linkedResource = _messages.StringField(15)
  lookerSystemSpec = _messages.MessageField('GoogleCloudDatacatalogV1LookerSystemSpec', 16)
  name = _messages.StringField(17)
  personalDetails = _messages.MessageField('GoogleCloudDatacatalogV1PersonalDetails', 18)
  routineSpec = _messages.MessageField('GoogleCloudDatacatalogV1RoutineSpec', 19)
  schema = _messages.MessageField('GoogleCloudDatacatalogV1Schema', 20)
  serviceSpec = _messages.MessageField('GoogleCloudDatacatalogV1ServiceSpec', 21)
  sourceSystemTimestamps = _messages.MessageField('GoogleCloudDatacatalogV1SystemTimestamps', 22)
  sqlDatabaseSystemSpec = _messages.MessageField('GoogleCloudDatacatalogV1SqlDatabaseSystemSpec', 23)
  type = _messages.EnumField('TypeValueValuesEnum', 24)
  usageSignal = _messages.MessageField('GoogleCloudDatacatalogV1UsageSignal', 25)
  userSpecifiedSystem = _messages.StringField(26)
  userSpecifiedType = _messages.StringField(27)


class GoogleCloudDatacatalogV1EntryOverview(_messages.Message):
  r"""Entry overview fields for rich text descriptions of entries.

  Fields:
    overview: Entry overview with support for rich text. The overview must
      only contain Unicode characters, and should be formatted using HTML. The
      maximum length is 10 MiB as this value holds HTML descriptions including
      encoded images. The maximum length of the text without images is 100
      KiB.
  """

  overview = _messages.StringField(1)


class GoogleCloudDatacatalogV1FilesetSpec(_messages.Message):
  r"""Specification that applies to a fileset. Valid only for entries with the
  'FILESET' type.

  Fields:
    dataplexFileset: Fields specific to a Dataplex fileset and present only in
      the Dataplex fileset entries.
  """

  dataplexFileset = _messages.MessageField('GoogleCloudDatacatalogV1DataplexFilesetSpec', 1)


class GoogleCloudDatacatalogV1GcsFileSpec(_messages.Message):
  r"""Specification of a single file in Cloud Storage.

  Fields:
    filePath: Required. Full file path. Example: `gs://bucket_name/a/b.txt`.
    gcsTimestamps: Output only. Creation, modification, and expiration
      timestamps of a Cloud Storage file.
    sizeBytes: Output only. File size in bytes.
  """

  filePath = _messages.StringField(1)
  gcsTimestamps = _messages.MessageField('GoogleCloudDatacatalogV1SystemTimestamps', 2)
  sizeBytes = _messages.IntegerField(3)


class GoogleCloudDatacatalogV1GcsFilesetSpec(_messages.Message):
  r"""Describes a Cloud Storage fileset entry.

  Fields:
    filePatterns: Required. Patterns to identify a set of files in Google
      Cloud Storage. For more information, see [Wildcard Names]
      (https://cloud.google.com/storage/docs/gsutil/addlhelp/WildcardNames).
      Note: Currently, bucket wildcards are not supported. Examples of valid
      `file_patterns`: * `gs://bucket_name/dir/*`: matches all files in
      `bucket_name/dir` directory * `gs://bucket_name/dir/**`: matches all
      files in `bucket_name/dir` and all subdirectories *
      `gs://bucket_name/file*`: matches files prefixed by `file` in
      `bucket_name` * `gs://bucket_name/??.txt`: matches files with two
      characters followed by `.txt` in `bucket_name` *
      `gs://bucket_name/[aeiou].txt`: matches files that contain a single
      vowel character followed by `.txt` in `bucket_name` *
      `gs://bucket_name/[a-m].txt`: matches files that contain `a`, `b`, ...
      or `m` followed by `.txt` in `bucket_name` * `gs://bucket_name/a/*/b`:
      matches all files in `bucket_name` that match the `a/*/b` pattern, such
      as `a/c/b`, `a/d/b` * `gs://another_bucket/a.txt`: matches
      `gs://another_bucket/a.txt` You can combine wildcards to match complex
      sets of files, for example: `gs://bucket_name/[a-m]??.j*g`
    sampleGcsFileSpecs: Output only. Sample files contained in this fileset,
      not all files contained in this fileset are represented here.
  """

  filePatterns = _messages.StringField(1, repeated=True)
  sampleGcsFileSpecs = _messages.MessageField('GoogleCloudDatacatalogV1GcsFileSpec', 2, repeated=True)


class GoogleCloudDatacatalogV1ImportEntriesMetadata(_messages.Message):
  r"""Metadata message for long-running operation returned by the
  ImportEntries.

  Enums:
    StateValueValuesEnum: State of the import operation.

  Fields:
    errors: Partial errors that are encountered during the ImportEntries
      operation. There is no guarantee that all the encountered errors are
      reported. However, if no errors are reported, it means that no errors
      were encountered.
    state: State of the import operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of the import operation.

    Values:
      IMPORT_STATE_UNSPECIFIED: Default value. This value is unused.
      IMPORT_QUEUED: The dump with entries has been queued for import.
      IMPORT_IN_PROGRESS: The import of entries is in progress.
      IMPORT_DONE: The import of entries has been finished.
      IMPORT_OBSOLETE: The import of entries has been abandoned in favor of a
        newer request.
    """
    IMPORT_STATE_UNSPECIFIED = 0
    IMPORT_QUEUED = 1
    IMPORT_IN_PROGRESS = 2
    IMPORT_DONE = 3
    IMPORT_OBSOLETE = 4

  errors = _messages.MessageField('Status', 1, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class GoogleCloudDatacatalogV1ImportEntriesResponse(_messages.Message):
  r"""Response message for long-running operation returned by the
  ImportEntries.

  Fields:
    deletedEntriesCount: Number of entries deleted as a result of import
      operation.
    upsertedEntriesCount: Cumulative number of entries created and entries
      updated as a result of import operation.
  """

  deletedEntriesCount = _messages.IntegerField(1)
  upsertedEntriesCount = _messages.IntegerField(2)


class GoogleCloudDatacatalogV1LookerSystemSpec(_messages.Message):
  r"""Specification that applies to entries that are part `LOOKER` system
  (user_specified_type)

  Fields:
    parentInstanceDisplayName: Name of the parent Looker Instance. Empty if it
      does not exist.
    parentInstanceId: ID of the parent Looker Instance. Empty if it does not
      exist. Example value: `someinstance.looker.com`
    parentModelDisplayName: Name of the parent Model. Empty if it does not
      exist.
    parentModelId: ID of the parent Model. Empty if it does not exist.
    parentViewDisplayName: Name of the parent View. Empty if it does not
      exist.
    parentViewId: ID of the parent View. Empty if it does not exist.
  """

  parentInstanceDisplayName = _messages.StringField(1)
  parentInstanceId = _messages.StringField(2)
  parentModelDisplayName = _messages.StringField(3)
  parentModelId = _messages.StringField(4)
  parentViewDisplayName = _messages.StringField(5)
  parentViewId = _messages.StringField(6)


class GoogleCloudDatacatalogV1PersonalDetails(_messages.Message):
  r"""Entry metadata relevant only to the user and private to them.

  Fields:
    starTime: Set if the entry is starred; unset otherwise.
    starred: True if the entry is starred by the user; false otherwise.
  """

  starTime = _messages.StringField(1)
  starred = _messages.BooleanField(2)


class GoogleCloudDatacatalogV1PhysicalSchema(_messages.Message):
  r"""Native schema used by a resource represented as an entry. Used by query
  engines for deserializing and parsing source data.

  Fields:
    avro: Schema in Avro JSON format.
    csv: Marks a CSV-encoded data source.
    orc: Marks an ORC-encoded data source.
    parquet: Marks a Parquet-encoded data source.
    protobuf: Schema in protocol buffer format.
    thrift: Schema in Thrift format.
  """

  avro = _messages.MessageField('GoogleCloudDatacatalogV1PhysicalSchemaAvroSchema', 1)
  csv = _messages.MessageField('GoogleCloudDatacatalogV1PhysicalSchemaCsvSchema', 2)
  orc = _messages.MessageField('GoogleCloudDatacatalogV1PhysicalSchemaOrcSchema', 3)
  parquet = _messages.MessageField('GoogleCloudDatacatalogV1PhysicalSchemaParquetSchema', 4)
  protobuf = _messages.MessageField('GoogleCloudDatacatalogV1PhysicalSchemaProtobufSchema', 5)
  thrift = _messages.MessageField('GoogleCloudDatacatalogV1PhysicalSchemaThriftSchema', 6)


class GoogleCloudDatacatalogV1PhysicalSchemaAvroSchema(_messages.Message):
  r"""Schema in Avro JSON format.

  Fields:
    text: JSON source of the Avro schema.
  """

  text = _messages.StringField(1)


class GoogleCloudDatacatalogV1PhysicalSchemaCsvSchema(_messages.Message):
  r"""Marks a CSV-encoded data source."""


class GoogleCloudDatacatalogV1PhysicalSchemaOrcSchema(_messages.Message):
  r"""Marks an ORC-encoded data source."""


class GoogleCloudDatacatalogV1PhysicalSchemaParquetSchema(_messages.Message):
  r"""Marks a Parquet-encoded data source."""


class GoogleCloudDatacatalogV1PhysicalSchemaProtobufSchema(_messages.Message):
  r"""Schema in protocol buffer format.

  Fields:
    text: Protocol buffer source of the schema.
  """

  text = _messages.StringField(1)


class GoogleCloudDatacatalogV1PhysicalSchemaThriftSchema(_messages.Message):
  r"""Schema in Thrift format.

  Fields:
    text: Thrift IDL source of the schema.
  """

  text = _messages.StringField(1)


class GoogleCloudDatacatalogV1ReconcileTagsMetadata(_messages.Message):
  r"""Long-running operation metadata message returned by the ReconcileTags.

  Enums:
    StateValueValuesEnum: State of the reconciliation operation.

  Messages:
    ErrorsValue: Maps the name of each tagged column (or empty string for a
      sole entry) to tagging operation status.

  Fields:
    errors: Maps the name of each tagged column (or empty string for a sole
      entry) to tagging operation status.
    state: State of the reconciliation operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of the reconciliation operation.

    Values:
      RECONCILIATION_STATE_UNSPECIFIED: Default value. This value is unused.
      RECONCILIATION_QUEUED: The reconciliation has been queued and awaits for
        execution.
      RECONCILIATION_IN_PROGRESS: The reconciliation is in progress.
      RECONCILIATION_DONE: The reconciliation has been finished.
    """
    RECONCILIATION_STATE_UNSPECIFIED = 0
    RECONCILIATION_QUEUED = 1
    RECONCILIATION_IN_PROGRESS = 2
    RECONCILIATION_DONE = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ErrorsValue(_messages.Message):
    r"""Maps the name of each tagged column (or empty string for a sole entry)
    to tagging operation status.

    Messages:
      AdditionalProperty: An additional property for a ErrorsValue object.

    Fields:
      additionalProperties: Additional properties of type ErrorsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ErrorsValue object.

      Fields:
        key: Name of the additional property.
        value: A Status attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Status', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  errors = _messages.MessageField('ErrorsValue', 1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class GoogleCloudDatacatalogV1ReconcileTagsResponse(_messages.Message):
  r"""Long-running operation response message returned by ReconcileTags.

  Fields:
    createdTagsCount: Number of tags created in the request.
    deletedTagsCount: Number of tags deleted in the request.
    updatedTagsCount: Number of tags updated in the request.
  """

  createdTagsCount = _messages.IntegerField(1)
  deletedTagsCount = _messages.IntegerField(2)
  updatedTagsCount = _messages.IntegerField(3)


class GoogleCloudDatacatalogV1RoutineSpec(_messages.Message):
  r"""Specification that applies to a routine. Valid only for entries with the
  `ROUTINE` type.

  Enums:
    RoutineTypeValueValuesEnum: The type of the routine.

  Fields:
    bigqueryRoutineSpec: Fields specific for BigQuery routines.
    definitionBody: The body of the routine.
    language: The language the routine is written in. The exact value depends
      on the source system. For BigQuery routines, possible values are: *
      `SQL` * `JAVASCRIPT`
    returnType: Return type of the argument. The exact value depends on the
      source system and the language.
    routineArguments: Arguments of the routine.
    routineType: The type of the routine.
  """

  class RoutineTypeValueValuesEnum(_messages.Enum):
    r"""The type of the routine.

    Values:
      ROUTINE_TYPE_UNSPECIFIED: Unspecified type.
      SCALAR_FUNCTION: Non-builtin permanent scalar function.
      PROCEDURE: Stored procedure.
    """
    ROUTINE_TYPE_UNSPECIFIED = 0
    SCALAR_FUNCTION = 1
    PROCEDURE = 2

  bigqueryRoutineSpec = _messages.MessageField('GoogleCloudDatacatalogV1BigQueryRoutineSpec', 1)
  definitionBody = _messages.StringField(2)
  language = _messages.StringField(3)
  returnType = _messages.StringField(4)
  routineArguments = _messages.MessageField('GoogleCloudDatacatalogV1RoutineSpecArgument', 5, repeated=True)
  routineType = _messages.EnumField('RoutineTypeValueValuesEnum', 6)


class GoogleCloudDatacatalogV1RoutineSpecArgument(_messages.Message):
  r"""Input or output argument of a function or stored procedure.

  Enums:
    ModeValueValuesEnum: Specifies whether the argument is input or output.

  Fields:
    mode: Specifies whether the argument is input or output.
    name: The name of the argument. A return argument of a function might not
      have a name.
    type: Type of the argument. The exact value depends on the source system
      and the language.
  """

  class ModeValueValuesEnum(_messages.Enum):
    r"""Specifies whether the argument is input or output.

    Values:
      MODE_UNSPECIFIED: Unspecified mode.
      IN: The argument is input-only.
      OUT: The argument is output-only.
      INOUT: The argument is both an input and an output.
    """
    MODE_UNSPECIFIED = 0
    IN = 1
    OUT = 2
    INOUT = 3

  mode = _messages.EnumField('ModeValueValuesEnum', 1)
  name = _messages.StringField(2)
  type = _messages.StringField(3)


class GoogleCloudDatacatalogV1Schema(_messages.Message):
  r"""Represents a schema, for example, a BigQuery, GoogleSQL, or Avro schema.

  Fields:
    columns: The unified GoogleSQL-like schema of columns. The overall maximum
      number of columns and nested columns is 10,000. The maximum nested depth
      is 15 levels.
  """

  columns = _messages.MessageField('GoogleCloudDatacatalogV1ColumnSchema', 1, repeated=True)


class GoogleCloudDatacatalogV1ServiceSpec(_messages.Message):
  r"""Specification that applies to a Service resource. Valid only for entries
  with the `SERVICE` type.

  Fields:
    cloudBigtableInstanceSpec: Specification that applies to Instance entries
      of `CLOUD_BIGTABLE` system.
  """

  cloudBigtableInstanceSpec = _messages.MessageField('GoogleCloudDatacatalogV1CloudBigtableInstanceSpec', 1)


class GoogleCloudDatacatalogV1SqlDatabaseSystemSpec(_messages.Message):
  r"""Specification that applies to entries that are part `SQL_DATABASE`
  system (user_specified_type)

  Fields:
    databaseVersion: Version of the database engine.
    instanceHost: Host of the SQL database enum InstanceHost { UNDEFINED = 0;
      SELF_HOSTED = 1; CLOUD_SQL = 2; AMAZON_RDS = 3; AZURE_SQL = 4; } Host of
      the enclousing database instance.
    sqlEngine: SQL Database Engine. enum SqlEngine { UNDEFINED = 0; MY_SQL =
      1; POSTGRE_SQL = 2; SQL_SERVER = 3; } Engine of the enclosing database
      instance.
  """

  databaseVersion = _messages.StringField(1)
  instanceHost = _messages.StringField(2)
  sqlEngine = _messages.StringField(3)


class GoogleCloudDatacatalogV1StorageProperties(_messages.Message):
  r"""Details the properties of the underlying storage.

  Fields:
    filePattern: Patterns to identify a set of files for this fileset.
      Examples of a valid `file_pattern`: * `gs://bucket_name/dir/*`: matches
      all files in the `bucket_name/dir` directory *
      `gs://bucket_name/dir/**`: matches all files in the `bucket_name/dir`
      and all subdirectories recursively * `gs://bucket_name/file*`: matches
      files prefixed by `file` in `bucket_name` * `gs://bucket_name/??.txt`:
      matches files with two characters followed by `.txt` in `bucket_name` *
      `gs://bucket_name/[aeiou].txt`: matches files that contain a single
      vowel character followed by `.txt` in `bucket_name` *
      `gs://bucket_name/[a-m].txt`: matches files that contain `a`, `b`, ...
      or `m` followed by `.txt` in `bucket_name` * `gs://bucket_name/a/*/b`:
      matches all files in `bucket_name` that match the `a/*/b` pattern, such
      as `a/c/b`, `a/d/b` * `gs://another_bucket/a.txt`: matches
      `gs://another_bucket/a.txt`
    fileType: File type in MIME format, for example, `text/plain`.
  """

  filePattern = _messages.StringField(1, repeated=True)
  fileType = _messages.StringField(2)


class GoogleCloudDatacatalogV1SystemTimestamps(_messages.Message):
  r"""Timestamps associated with this resource in a particular system.

  Fields:
    createTime: Creation timestamp of the resource within the given system.
    expireTime: Output only. Expiration timestamp of the resource within the
      given system. Currently only applicable to BigQuery resources.
    updateTime: Timestamp of the last modification of the resource or its
      metadata within a given system. Note: Depending on the source system,
      not every modification updates this timestamp. For example, BigQuery
      timestamps every metadata modification but not data or permission
      changes.
  """

  createTime = _messages.StringField(1)
  expireTime = _messages.StringField(2)
  updateTime = _messages.StringField(3)


class GoogleCloudDatacatalogV1TableSpec(_messages.Message):
  r"""Normal BigQuery table specification.

  Fields:
    groupedEntry: Output only. If the table is date-sharded, that is, it
      matches the `[prefix]YYYYMMDD` name pattern, this field is the Data
      Catalog resource name of the date-sharded grouped entry. For example: `p
      rojects/{PROJECT_ID}/locations/{LOCATION}/entrygroups/{ENTRY_GROUP_ID}/e
      ntries/{ENTRY_ID}`. Otherwise, `grouped_entry` is empty.
  """

  groupedEntry = _messages.StringField(1)


class GoogleCloudDatacatalogV1Tag(_messages.Message):
  r"""Tags contain custom metadata and are attached to Data Catalog resources.
  Tags conform with the specification of their tag template. See [Data Catalog
  IAM](https://cloud.google.com/data-catalog/docs/concepts/iam) for
  information on the permissions needed to create or view tags.

  Messages:
    FieldsValue: Required. Maps the ID of a tag field to its value and
      additional information about that field. Tag template defines valid
      field IDs. A tag must have at least 1 field and at most 500 fields.

  Fields:
    column: Resources like entry can have schemas associated with them. This
      scope allows you to attach tags to an individual column based on that
      schema. To attach a tag to a nested column, separate column names with a
      dot (`.`). Example: `column.nested_column`.
    fields: Required. Maps the ID of a tag field to its value and additional
      information about that field. Tag template defines valid field IDs. A
      tag must have at least 1 field and at most 500 fields.
    name: The resource name of the tag in URL format where tag ID is a system-
      generated identifier. Note: The tag itself might not be stored in the
      location specified in its name.
    template: Required. The resource name of the tag template this tag uses.
      Example: `projects/{PROJECT_ID}/locations/{LOCATION}/tagTemplates/{TAG_T
      EMPLATE_ID}` This field cannot be modified after creation.
    templateDisplayName: Output only. The display name of the tag template.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FieldsValue(_messages.Message):
    r"""Required. Maps the ID of a tag field to its value and additional
    information about that field. Tag template defines valid field IDs. A tag
    must have at least 1 field and at most 500 fields.

    Messages:
      AdditionalProperty: An additional property for a FieldsValue object.

    Fields:
      additionalProperties: Additional properties of type FieldsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FieldsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudDatacatalogV1TagField attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudDatacatalogV1TagField', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  column = _messages.StringField(1)
  fields = _messages.MessageField('FieldsValue', 2)
  name = _messages.StringField(3)
  template = _messages.StringField(4)
  templateDisplayName = _messages.StringField(5)


class GoogleCloudDatacatalogV1TagField(_messages.Message):
  r"""Contains the value and additional information on a field within a Tag.

  Fields:
    boolValue: The value of a tag field with a boolean type.
    displayName: Output only. The display name of this field.
    doubleValue: The value of a tag field with a double type.
    enumValue: The value of a tag field with an enum type. This value must be
      one of the allowed values listed in this enum.
    order: Output only. The order of this field with respect to other fields
      in this tag. Can be set by Tag. For example, a higher value can indicate
      a more important field. The value can be negative. Multiple fields can
      have the same order, and field orders within a tag don't have to be
      sequential.
    richtextValue: The value of a tag field with a rich text type. The maximum
      length is 10 MiB as this value holds HTML descriptions including encoded
      images. The maximum length of the text without images is 100 KiB.
    stringValue: The value of a tag field with a string type. The maximum
      length is 2000 UTF-8 characters.
    timestampValue: The value of a tag field with a timestamp type.
  """

  boolValue = _messages.BooleanField(1)
  displayName = _messages.StringField(2)
  doubleValue = _messages.FloatField(3)
  enumValue = _messages.MessageField('GoogleCloudDatacatalogV1TagFieldEnumValue', 4)
  order = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  richtextValue = _messages.StringField(6)
  stringValue = _messages.StringField(7)
  timestampValue = _messages.StringField(8)


class GoogleCloudDatacatalogV1TagFieldEnumValue(_messages.Message):
  r"""An enum value.

  Fields:
    displayName: The display name of the enum value.
  """

  displayName = _messages.StringField(1)


class GoogleCloudDatacatalogV1TaggedEntry(_messages.Message):
  r"""Wrapper containing Entry and information about Tags that should and
  should not be attached to it.

  Fields:
    absentTags: Optional. Tags that should be deleted from the Data Catalog.
      Caller should populate template name and column only.
    presentTags: Optional. Tags that should be ingested into the Data Catalog.
      Caller should populate template name, column and fields.
    v1Entry: Non-encrypted Data Catalog v1 Entry.
  """

  absentTags = _messages.MessageField('GoogleCloudDatacatalogV1Tag', 1, repeated=True)
  presentTags = _messages.MessageField('GoogleCloudDatacatalogV1Tag', 2, repeated=True)
  v1Entry = _messages.MessageField('GoogleCloudDatacatalogV1Entry', 3)


class GoogleCloudDatacatalogV1UsageSignal(_messages.Message):
  r"""The set of all usage signals that Data Catalog stores. Note: Usually,
  these signals are updated daily. In rare cases, an update may fail but will
  be performed again on the next day.

  Messages:
    CommonUsageWithinTimeRangeValue: Common usage statistics over each of the
      predefined time ranges. Supported time ranges are `{"24H", "7D", "30D",
      "Lifetime"}`.
    UsageWithinTimeRangeValue: Output only. BigQuery usage statistics over
      each of the predefined time ranges. Supported time ranges are `{"24H",
      "7D", "30D"}`.

  Fields:
    commonUsageWithinTimeRange: Common usage statistics over each of the
      predefined time ranges. Supported time ranges are `{"24H", "7D", "30D",
      "Lifetime"}`.
    favoriteCount: Favorite count in the source system.
    updateTime: The end timestamp of the duration of usage statistics.
    usageWithinTimeRange: Output only. BigQuery usage statistics over each of
      the predefined time ranges. Supported time ranges are `{"24H", "7D",
      "30D"}`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class CommonUsageWithinTimeRangeValue(_messages.Message):
    r"""Common usage statistics over each of the predefined time ranges.
    Supported time ranges are `{"24H", "7D", "30D", "Lifetime"}`.

    Messages:
      AdditionalProperty: An additional property for a
        CommonUsageWithinTimeRangeValue object.

    Fields:
      additionalProperties: Additional properties of type
        CommonUsageWithinTimeRangeValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a CommonUsageWithinTimeRangeValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudDatacatalogV1CommonUsageStats attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudDatacatalogV1CommonUsageStats', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class UsageWithinTimeRangeValue(_messages.Message):
    r"""Output only. BigQuery usage statistics over each of the predefined
    time ranges. Supported time ranges are `{"24H", "7D", "30D"}`.

    Messages:
      AdditionalProperty: An additional property for a
        UsageWithinTimeRangeValue object.

    Fields:
      additionalProperties: Additional properties of type
        UsageWithinTimeRangeValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a UsageWithinTimeRangeValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudDatacatalogV1UsageStats attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudDatacatalogV1UsageStats', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  commonUsageWithinTimeRange = _messages.MessageField('CommonUsageWithinTimeRangeValue', 1)
  favoriteCount = _messages.IntegerField(2)
  updateTime = _messages.StringField(3)
  usageWithinTimeRange = _messages.MessageField('UsageWithinTimeRangeValue', 4)


class GoogleCloudDatacatalogV1UsageStats(_messages.Message):
  r"""Detailed statistics on the entry's usage. Usage statistics have the
  following limitations: - Only BigQuery tables have them. - They only include
  BigQuery query jobs. - They might be underestimated because wildcard table
  references are not yet counted. For more information, see [Querying multiple
  tables using a wildcard table]
  (https://cloud.google.com/bigquery/docs/querying-wildcard-tables)

  Fields:
    totalCancellations: The number of cancelled attempts to use the underlying
      entry.
    totalCompletions: The number of successful uses of the underlying entry.
    totalExecutionTimeForCompletionsMillis: Total time spent only on
      successful uses, in milliseconds.
    totalFailures: The number of failed attempts to use the underlying entry.
  """

  totalCancellations = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  totalCompletions = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  totalExecutionTimeForCompletionsMillis = _messages.FloatField(3, variant=_messages.Variant.FLOAT)
  totalFailures = _messages.FloatField(4, variant=_messages.Variant.FLOAT)


class GoogleCloudDatacatalogV1ViewSpec(_messages.Message):
  r"""Table view specification.

  Fields:
    viewQuery: Output only. The query that defines the table view.
  """

  viewQuery = _messages.StringField(1)


class GoogleCloudDatacatalogV1beta1BigQueryDateShardedSpec(_messages.Message):
  r"""Spec for a group of BigQuery tables with name pattern
  `[prefix]YYYYMMDD`. Context:
  https://cloud.google.com/bigquery/docs/partitioned-
  tables#partitioning_versus_sharding

  Fields:
    dataset: Output only. The Data Catalog resource name of the dataset entry
      the current table belongs to, for example, `projects/{project_id}/locati
      ons/{location}/entrygroups/{entry_group_id}/entries/{entry_id}`.
    shardCount: Output only. Total number of shards.
    tablePrefix: Output only. The table name prefix of the shards. The name of
      any given shard is `[table_prefix]YYYYMMDD`, for example, for shard
      `MyTable20180101`, the `table_prefix` is `MyTable`.
  """

  dataset = _messages.StringField(1)
  shardCount = _messages.IntegerField(2)
  tablePrefix = _messages.StringField(3)


class GoogleCloudDatacatalogV1beta1BigQueryTableSpec(_messages.Message):
  r"""Describes a BigQuery table.

  Enums:
    TableSourceTypeValueValuesEnum: Output only. The table source type.

  Fields:
    tableSourceType: Output only. The table source type.
    tableSpec: Spec of a BigQuery table. This field should only be populated
      if `table_source_type` is `BIGQUERY_TABLE`.
    viewSpec: Table view specification. This field should only be populated if
      `table_source_type` is `BIGQUERY_VIEW`.
  """

  class TableSourceTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The table source type.

    Values:
      TABLE_SOURCE_TYPE_UNSPECIFIED: Default unknown type.
      BIGQUERY_VIEW: Table view.
      BIGQUERY_TABLE: BigQuery native table.
      BIGQUERY_MATERIALIZED_VIEW: BigQuery materialized view.
    """
    TABLE_SOURCE_TYPE_UNSPECIFIED = 0
    BIGQUERY_VIEW = 1
    BIGQUERY_TABLE = 2
    BIGQUERY_MATERIALIZED_VIEW = 3

  tableSourceType = _messages.EnumField('TableSourceTypeValueValuesEnum', 1)
  tableSpec = _messages.MessageField('GoogleCloudDatacatalogV1beta1TableSpec', 2)
  viewSpec = _messages.MessageField('GoogleCloudDatacatalogV1beta1ViewSpec', 3)


class GoogleCloudDatacatalogV1beta1ColumnSchema(_messages.Message):
  r"""Representation of a column within a schema. Columns could be nested
  inside other columns.

  Fields:
    column: Required. Name of the column.
    description: Optional. Description of the column. Default value is an
      empty string.
    mode: Optional. A column's mode indicates whether the values in this
      column are required, nullable, etc. Only `NULLABLE`, `REQUIRED` and
      `REPEATED` are supported. Default mode is `NULLABLE`.
    subcolumns: Optional. Schema of sub-columns. A column can have zero or
      more sub-columns.
    type: Required. Type of the column.
  """

  column = _messages.StringField(1)
  description = _messages.StringField(2)
  mode = _messages.StringField(3)
  subcolumns = _messages.MessageField('GoogleCloudDatacatalogV1beta1ColumnSchema', 4, repeated=True)
  type = _messages.StringField(5)


class GoogleCloudDatacatalogV1beta1Entry(_messages.Message):
  r"""Entry Metadata. A Data Catalog Entry resource represents another
  resource in Google Cloud Platform (such as a BigQuery dataset or a Pub/Sub
  topic), or outside of Google Cloud Platform. Clients can use the
  `linked_resource` field in the Entry resource to refer to the original
  resource ID of the source system. An Entry resource contains resource
  details, such as its schema. An Entry can also be used to attach flexible
  metadata, such as a Tag.

  Enums:
    IntegratedSystemValueValuesEnum: Output only. This field indicates the
      entry's source system that Data Catalog integrates with, such as
      BigQuery or Pub/Sub.
    TypeValueValuesEnum: The type of the entry. Only used for Entries with
      types in the EntryType enum.

  Fields:
    bigqueryDateShardedSpec: Specification for a group of BigQuery tables with
      name pattern `[prefix]YYYYMMDD`. Context:
      https://cloud.google.com/bigquery/docs/partitioned-
      tables#partitioning_versus_sharding.
    bigqueryTableSpec: Specification that applies to a BigQuery table. This is
      only valid on entries of type `TABLE`.
    description: Entry description, which can consist of several sentences or
      paragraphs that describe entry contents. Default value is an empty
      string.
    displayName: Display information such as title and description. A short
      name to identify the entry, for example, "Analytics Data - Jan 2011".
      Default value is an empty string.
    gcsFilesetSpec: Specification that applies to a Cloud Storage fileset.
      This is only valid on entries of type FILESET.
    integratedSystem: Output only. This field indicates the entry's source
      system that Data Catalog integrates with, such as BigQuery or Pub/Sub.
    linkedResource: The resource this metadata entry refers to. For Google
      Cloud Platform resources, `linked_resource` is the [full name of the res
      ource](https://cloud.google.com/apis/design/resource_names#full_resource
      _name). For example, the `linked_resource` for a table resource from
      BigQuery is: * //bigquery.googleapis.com/projects/projectId/datasets/dat
      asetId/tables/tableId Output only when Entry is of type in the EntryType
      enum. For entries with user_specified_type, this field is optional and
      defaults to an empty string.
    name: Output only. The Data Catalog resource name of the entry in URL
      format. Example: * projects/{project_id}/locations/{location}/entryGroup
      s/{entry_group_id}/entries/{entry_id} Note that this Entry and its child
      resources may not actually be stored in the location in this name.
    schema: Schema of the entry. An entry might not have any schema attached
      to it.
    sourceSystemTimestamps: Output only. Timestamps about the underlying
      resource, not about this Data Catalog entry. Output only when Entry is
      of type in the EntryType enum. For entries with user_specified_type,
      this field is optional and defaults to an empty timestamp.
    type: The type of the entry. Only used for Entries with types in the
      EntryType enum.
    usageSignal: Output only. Statistics on the usage level of the resource.
    userSpecifiedSystem: This field indicates the entry's source system that
      Data Catalog does not integrate with. `user_specified_system` strings
      must begin with a letter or underscore and can only contain letters,
      numbers, and underscores; are case insensitive; must be at least 1
      character and at most 64 characters long.
    userSpecifiedType: Entry type if it does not fit any of the input-allowed
      values listed in `EntryType` enum above. When creating an entry, users
      should check the enum values first, if nothing matches the entry to be
      created, then provide a custom value, for example "my_special_type".
      `user_specified_type` strings must begin with a letter or underscore and
      can only contain letters, numbers, and underscores; are case
      insensitive; must be at least 1 character and at most 64 characters
      long. Currently, only FILESET enum value is allowed. All other entries
      created through Data Catalog must use `user_specified_type`.
  """

  class IntegratedSystemValueValuesEnum(_messages.Enum):
    r"""Output only. This field indicates the entry's source system that Data
    Catalog integrates with, such as BigQuery or Pub/Sub.

    Values:
      INTEGRATED_SYSTEM_UNSPECIFIED: Default unknown system.
      BIGQUERY: BigQuery.
      CLOUD_PUBSUB: Cloud Pub/Sub.
    """
    INTEGRATED_SYSTEM_UNSPECIFIED = 0
    BIGQUERY = 1
    CLOUD_PUBSUB = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of the entry. Only used for Entries with types in the
    EntryType enum.

    Values:
      ENTRY_TYPE_UNSPECIFIED: Default unknown type.
      TABLE: Output only. The type of entry that has a GoogleSQL schema,
        including logical views.
      MODEL: Output only. The type of models.
        https://cloud.google.com/bigquery-ml/docs/bigqueryml-intro
      DATA_STREAM: Output only. An entry type which is used for streaming
        entries. Example: Pub/Sub topic.
      FILESET: An entry type which is a set of files or objects. Example:
        Cloud Storage fileset.
    """
    ENTRY_TYPE_UNSPECIFIED = 0
    TABLE = 1
    MODEL = 2
    DATA_STREAM = 3
    FILESET = 4

  bigqueryDateShardedSpec = _messages.MessageField('GoogleCloudDatacatalogV1beta1BigQueryDateShardedSpec', 1)
  bigqueryTableSpec = _messages.MessageField('GoogleCloudDatacatalogV1beta1BigQueryTableSpec', 2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  gcsFilesetSpec = _messages.MessageField('GoogleCloudDatacatalogV1beta1GcsFilesetSpec', 5)
  integratedSystem = _messages.EnumField('IntegratedSystemValueValuesEnum', 6)
  linkedResource = _messages.StringField(7)
  name = _messages.StringField(8)
  schema = _messages.MessageField('GoogleCloudDatacatalogV1beta1Schema', 9)
  sourceSystemTimestamps = _messages.MessageField('GoogleCloudDatacatalogV1beta1SystemTimestamps', 10)
  type = _messages.EnumField('TypeValueValuesEnum', 11)
  usageSignal = _messages.MessageField('GoogleCloudDatacatalogV1beta1UsageSignal', 12)
  userSpecifiedSystem = _messages.StringField(13)
  userSpecifiedType = _messages.StringField(14)


class GoogleCloudDatacatalogV1beta1EntryGroup(_messages.Message):
  r"""EntryGroup Metadata. An EntryGroup resource represents a logical
  grouping of zero or more Data Catalog Entry resources.

  Fields:
    dataCatalogTimestamps: Output only. Timestamps about this EntryGroup.
      Default value is empty timestamps.
    description: Entry group description, which can consist of several
      sentences or paragraphs that describe entry group contents. Default
      value is an empty string.
    displayName: A short name to identify the entry group, for example,
      "analytics data - jan 2011". Default value is an empty string.
    name: The resource name of the entry group in URL format. Example: *
      projects/{project_id}/locations/{location}/entryGroups/{entry_group_id}
      Note that this EntryGroup and its child resources may not actually be
      stored in the location in this name.
  """

  dataCatalogTimestamps = _messages.MessageField('GoogleCloudDatacatalogV1beta1SystemTimestamps', 1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  name = _messages.StringField(4)


class GoogleCloudDatacatalogV1beta1ExportTaxonomiesResponse(_messages.Message):
  r"""Response message for ExportTaxonomies.

  Fields:
    taxonomies: List of taxonomies and policy tags in a tree structure.
  """

  taxonomies = _messages.MessageField('GoogleCloudDatacatalogV1beta1SerializedTaxonomy', 1, repeated=True)


class GoogleCloudDatacatalogV1beta1FieldType(_messages.Message):
  r"""A GoogleCloudDatacatalogV1beta1FieldType object.

  Enums:
    PrimitiveTypeValueValuesEnum: Represents primitive types - string, bool
      etc.

  Fields:
    enumType: Represents an enum type.
    primitiveType: Represents primitive types - string, bool etc.
  """

  class PrimitiveTypeValueValuesEnum(_messages.Enum):
    r"""Represents primitive types - string, bool etc.

    Values:
      PRIMITIVE_TYPE_UNSPECIFIED: This is the default invalid value for a
        type.
      DOUBLE: A double precision number.
      STRING: An UTF-8 string.
      BOOL: A boolean value.
      TIMESTAMP: A timestamp.
    """
    PRIMITIVE_TYPE_UNSPECIFIED = 0
    DOUBLE = 1
    STRING = 2
    BOOL = 3
    TIMESTAMP = 4

  enumType = _messages.MessageField('GoogleCloudDatacatalogV1beta1FieldTypeEnumType', 1)
  primitiveType = _messages.EnumField('PrimitiveTypeValueValuesEnum', 2)


class GoogleCloudDatacatalogV1beta1FieldTypeEnumType(_messages.Message):
  r"""A GoogleCloudDatacatalogV1beta1FieldTypeEnumType object.

  Fields:
    allowedValues: A GoogleCloudDatacatalogV1beta1FieldTypeEnumTypeEnumValue
      attribute.
  """

  allowedValues = _messages.MessageField('GoogleCloudDatacatalogV1beta1FieldTypeEnumTypeEnumValue', 1, repeated=True)


class GoogleCloudDatacatalogV1beta1FieldTypeEnumTypeEnumValue(_messages.Message):
  r"""A GoogleCloudDatacatalogV1beta1FieldTypeEnumTypeEnumValue object.

  Fields:
    displayName: Required. The display name of the enum value. Must not be an
      empty string.
  """

  displayName = _messages.StringField(1)


class GoogleCloudDatacatalogV1beta1GcsFileSpec(_messages.Message):
  r"""Specifications of a single file in Cloud Storage.

  Fields:
    filePath: Required. The full file path. Example:
      `gs://bucket_name/a/b.txt`.
    gcsTimestamps: Output only. Timestamps about the Cloud Storage file.
    sizeBytes: Output only. The size of the file, in bytes.
  """

  filePath = _messages.StringField(1)
  gcsTimestamps = _messages.MessageField('GoogleCloudDatacatalogV1beta1SystemTimestamps', 2)
  sizeBytes = _messages.IntegerField(3)


class GoogleCloudDatacatalogV1beta1GcsFilesetSpec(_messages.Message):
  r"""Describes a Cloud Storage fileset entry.

  Fields:
    filePatterns: Required. Patterns to identify a set of files in Google
      Cloud Storage. See [Cloud Storage documentation](https://cloud.google.co
      m/storage/docs/gsutil/addlhelp/WildcardNames) for more information. Note
      that bucket wildcards are currently not supported. Examples of valid
      file_patterns: * `gs://bucket_name/dir/*`: matches all files within
      `bucket_name/dir` directory. * `gs://bucket_name/dir/**`: matches all
      files in `bucket_name/dir` spanning all subdirectories. *
      `gs://bucket_name/file*`: matches files prefixed by `file` in
      `bucket_name` * `gs://bucket_name/??.txt`: matches files with two
      characters followed by `.txt` in `bucket_name` *
      `gs://bucket_name/[aeiou].txt`: matches files that contain a single
      vowel character followed by `.txt` in `bucket_name` *
      `gs://bucket_name/[a-m].txt`: matches files that contain `a`, `b`, ...
      or `m` followed by `.txt` in `bucket_name` * `gs://bucket_name/a/*/b`:
      matches all files in `bucket_name` that match `a/*/b` pattern, such as
      `a/c/b`, `a/d/b` * `gs://another_bucket/a.txt`: matches
      `gs://another_bucket/a.txt` You can combine wildcards to provide more
      powerful matches, for example: * `gs://bucket_name/[a-m]??.j*g`
    sampleGcsFileSpecs: Output only. Sample files contained in this fileset,
      not all files contained in this fileset are represented here.
  """

  filePatterns = _messages.StringField(1, repeated=True)
  sampleGcsFileSpecs = _messages.MessageField('GoogleCloudDatacatalogV1beta1GcsFileSpec', 2, repeated=True)


class GoogleCloudDatacatalogV1beta1ImportTaxonomiesRequest(_messages.Message):
  r"""Request message for ImportTaxonomies.

  Fields:
    inlineSource: Inline source used for taxonomies to be imported.
  """

  inlineSource = _messages.MessageField('GoogleCloudDatacatalogV1beta1InlineSource', 1)


class GoogleCloudDatacatalogV1beta1ImportTaxonomiesResponse(_messages.Message):
  r"""Response message for ImportTaxonomies.

  Fields:
    taxonomies: Taxonomies that were imported.
  """

  taxonomies = _messages.MessageField('GoogleCloudDatacatalogV1beta1Taxonomy', 1, repeated=True)


class GoogleCloudDatacatalogV1beta1InlineSource(_messages.Message):
  r"""Inline source used for taxonomies import.

  Fields:
    taxonomies: Required. Taxonomies to be imported.
  """

  taxonomies = _messages.MessageField('GoogleCloudDatacatalogV1beta1SerializedTaxonomy', 1, repeated=True)


class GoogleCloudDatacatalogV1beta1ListEntriesResponse(_messages.Message):
  r"""Response message for ListEntries.

  Fields:
    entries: Entry details.
    nextPageToken: Token to retrieve the next page of results. It is set to
      empty if no items remain in results.
  """

  entries = _messages.MessageField('GoogleCloudDatacatalogV1beta1Entry', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudDatacatalogV1beta1ListEntryGroupsResponse(_messages.Message):
  r"""Response message for ListEntryGroups.

  Fields:
    entryGroups: EntryGroup details.
    nextPageToken: Token to retrieve the next page of results. It is set to
      empty if no items remain in results.
  """

  entryGroups = _messages.MessageField('GoogleCloudDatacatalogV1beta1EntryGroup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudDatacatalogV1beta1ListPolicyTagsResponse(_messages.Message):
  r"""Response message for ListPolicyTags.

  Fields:
    nextPageToken: Token used to retrieve the next page of results, or empty
      if there are no more results in the list.
    policyTags: The policy tags that are in the requested taxonomy.
  """

  nextPageToken = _messages.StringField(1)
  policyTags = _messages.MessageField('GoogleCloudDatacatalogV1beta1PolicyTag', 2, repeated=True)


class GoogleCloudDatacatalogV1beta1ListTagsResponse(_messages.Message):
  r"""Response message for ListTags.

  Fields:
    nextPageToken: Token to retrieve the next page of results. It is set to
      empty if no items remain in results.
    tags: Tag details.
  """

  nextPageToken = _messages.StringField(1)
  tags = _messages.MessageField('GoogleCloudDatacatalogV1beta1Tag', 2, repeated=True)


class GoogleCloudDatacatalogV1beta1ListTaxonomiesResponse(_messages.Message):
  r"""Response message for ListTaxonomies.

  Fields:
    nextPageToken: Token used to retrieve the next page of results, or empty
      if there are no more results in the list.
    taxonomies: Taxonomies that the project contains.
  """

  nextPageToken = _messages.StringField(1)
  taxonomies = _messages.MessageField('GoogleCloudDatacatalogV1beta1Taxonomy', 2, repeated=True)


class GoogleCloudDatacatalogV1beta1PolicyTag(_messages.Message):
  r"""Denotes one policy tag in a taxonomy (e.g. ssn). Policy Tags can be
  defined in a hierarchy. For example, consider the following hierarchy:
  Geolocation -> (LatLong, City, ZipCode). PolicyTag "Geolocation" contains
  three child policy tags: "LatLong", "City", and "ZipCode".

  Fields:
    childPolicyTags: Output only. Resource names of child policy tags of this
      policy tag.
    description: Description of this policy tag. It must: contain only unicode
      characters, tabs, newlines, carriage returns and page breaks; and be at
      most 2000 bytes long when encoded in UTF-8. If not set, defaults to an
      empty description. If not set, defaults to an empty description.
    displayName: Required. User defined name of this policy tag. It must: be
      unique within the parent taxonomy; contain only unicode letters,
      numbers, underscores, dashes and spaces; not start or end with spaces;
      and be at most 200 bytes long when encoded in UTF-8.
    name: Output only. Resource name of this policy tag, whose format is: "pro
      jects/{project_number}/locations/{location_id}/taxonomies/{taxonomy_id}/
      policyTags/{id}".
    parentPolicyTag: Resource name of this policy tag's parent policy tag
      (e.g. for the "LatLong" policy tag in the example above, this field
      contains the resource name of the "Geolocation" policy tag). If empty,
      it means this policy tag is a top level policy tag (e.g. this field is
      empty for the "Geolocation" policy tag in the example above). If not
      set, defaults to an empty string.
  """

  childPolicyTags = _messages.StringField(1, repeated=True)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  name = _messages.StringField(4)
  parentPolicyTag = _messages.StringField(5)


class GoogleCloudDatacatalogV1beta1RenameTagTemplateFieldEnumValueRequest(_messages.Message):
  r"""Request message for RenameTagTemplateFieldEnumValue.

  Fields:
    newEnumValueDisplayName: Required. The new display name of the enum value.
      For example, `my_new_enum_value`.
  """

  newEnumValueDisplayName = _messages.StringField(1)


class GoogleCloudDatacatalogV1beta1RenameTagTemplateFieldRequest(_messages.Message):
  r"""Request message for RenameTagTemplateField.

  Fields:
    newTagTemplateFieldId: Required. The new ID of this tag template field.
      For example, `my_new_field`.
  """

  newTagTemplateFieldId = _messages.StringField(1)


class GoogleCloudDatacatalogV1beta1Schema(_messages.Message):
  r"""Represents a schema (e.g. BigQuery, GoogleSQL, Avro schema).

  Fields:
    columns: Required. Schema of columns. A maximum of 10,000 columns and sub-
      columns can be specified.
  """

  columns = _messages.MessageField('GoogleCloudDatacatalogV1beta1ColumnSchema', 1, repeated=True)


class GoogleCloudDatacatalogV1beta1SearchCatalogRequest(_messages.Message):
  r"""Request message for SearchCatalog.

  Fields:
    orderBy: Specifies the ordering of results, currently supported case-
      sensitive choices are: * `relevance`, only supports descending *
      `last_modified_timestamp [asc|desc]`, defaults to descending if not
      specified * `default` that can only be descending If not specified,
      defaults to `relevance` descending.
    pageSize: Number of results in the search page. If <=0 then defaults to
      10. Max limit for page_size is 1000. Throws an invalid argument for
      page_size > 1000.
    pageToken: Optional. Pagination token returned in an earlier
      SearchCatalogResponse.next_page_token, which indicates that this is a
      continuation of a prior SearchCatalogRequest call, and that the system
      should return the next page of data. If empty, the first page is
      returned.
    query: Optional. The query string in search query syntax. An empty query
      string will result in all data assets (in the specified scope) that the
      user has access to. Query strings can be simple as "x" or more qualified
      as: * name:x * column:x * description:y Note: Query tokens need to have
      a minimum of 3 characters for substring matching to work correctly. See
      [Data Catalog Search Syntax](https://cloud.google.com/data-
      catalog/docs/how-to/search-reference) for more information.
    scope: Required. The scope of this search request. A `scope` that has
      empty `include_org_ids`, `include_project_ids` AND false
      `include_gcp_public_datasets` is considered invalid. Data Catalog will
      return an error in such a case.
  """

  orderBy = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  query = _messages.StringField(4)
  scope = _messages.MessageField('GoogleCloudDatacatalogV1beta1SearchCatalogRequestScope', 5)


class GoogleCloudDatacatalogV1beta1SearchCatalogRequestScope(_messages.Message):
  r"""The criteria that select the subspace used for query matching.

  Fields:
    includeGcpPublicDatasets: If `true`, include Google Cloud public datasets
      in the search results. Info on Google Cloud public datasets is available
      at https://cloud.google.com/public-datasets/. By default, Google Cloud
      public datasets are excluded.
    includeOrgIds: The list of organization IDs to search within. To find your
      organization ID, follow instructions in
      https://cloud.google.com/resource-manager/docs/creating-managing-
      organization.
    includeProjectIds: The list of project IDs to search within. To learn more
      about the distinction between project names/IDs/numbers, go to
      https://cloud.google.com/docs/overview/#projects.
    restrictedLocations: Optional. The list of locations to search within. 1.
      If empty, search will be performed in all locations; 2. If any of the
      locations are NOT in the valid locations list, error will be returned;
      3. Otherwise, search only the given locations for matching results.
      Typical usage is to leave this field empty. When a location is
      unreachable as returned in the `SearchCatalogResponse.unreachable`
      field, users can repeat the search request with this parameter set to
      get additional information on the error. Valid locations: * asia-east1 *
      asia-east2 * asia-northeast1 * asia-northeast2 * asia-northeast3 * asia-
      south1 * asia-southeast1 * australia-southeast1 * eu * europe-north1 *
      europe-west1 * europe-west2 * europe-west3 * europe-west4 * europe-west6
      * global * northamerica-northeast1 * southamerica-east1 * us * us-
      central1 * us-east1 * us-east4 * us-west1 * us-west2
  """

  includeGcpPublicDatasets = _messages.BooleanField(1)
  includeOrgIds = _messages.StringField(2, repeated=True)
  includeProjectIds = _messages.StringField(3, repeated=True)
  restrictedLocations = _messages.StringField(4, repeated=True)


class GoogleCloudDatacatalogV1beta1SearchCatalogResponse(_messages.Message):
  r"""Response message for SearchCatalog.

  Fields:
    nextPageToken: The token that can be used to retrieve the next page of
      results.
    results: Search results.
    totalSize: The approximate total number of entries matched by the query.
    unreachable: Unreachable locations. Search result does not include data
      from those locations. Users can get additional information on the error
      by repeating the search request with a more restrictive parameter --
      setting the value for
      `SearchDataCatalogRequest.scope.restricted_locations`.
  """

  nextPageToken = _messages.StringField(1)
  results = _messages.MessageField('GoogleCloudDatacatalogV1beta1SearchCatalogResult', 2, repeated=True)
  totalSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  unreachable = _messages.StringField(4, repeated=True)


class GoogleCloudDatacatalogV1beta1SearchCatalogResult(_messages.Message):
  r"""A result that appears in the response of a search request. Each result
  captures details of one entry that matches the search.

  Enums:
    SearchResultTypeValueValuesEnum: Type of the search result. This field can
      be used to determine which Get method to call to fetch the full
      resource.

  Fields:
    linkedResource: The full name of the cloud resource the entry belongs to.
      See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name.
      Example: * `//bigquery.googleapis.com/projects/projectId/datasets/datase
      tId/tables/tableId`
    modifyTime: Last-modified timestamp of the entry from the managing system.
    relativeResourceName: The relative resource name of the resource in URL
      format. Examples: * `projects/{project_id}/locations/{location_id}/entry
      Groups/{entry_group_id}/entries/{entry_id}` *
      `projects/{project_id}/tagTemplates/{tag_template_id}`
    searchResultSubtype: Sub-type of the search result. This is a dot-
      delimited description of the resource's full type, and is the same as
      the value callers would provide in the "type" search facet. Examples:
      `entry.table`, `entry.dataStream`, `tagTemplate`.
    searchResultType: Type of the search result. This field can be used to
      determine which Get method to call to fetch the full resource.
  """

  class SearchResultTypeValueValuesEnum(_messages.Enum):
    r"""Type of the search result. This field can be used to determine which
    Get method to call to fetch the full resource.

    Values:
      SEARCH_RESULT_TYPE_UNSPECIFIED: Default unknown type.
      ENTRY: An Entry.
      TAG_TEMPLATE: A TagTemplate.
      ENTRY_GROUP: An EntryGroup.
    """
    SEARCH_RESULT_TYPE_UNSPECIFIED = 0
    ENTRY = 1
    TAG_TEMPLATE = 2
    ENTRY_GROUP = 3

  linkedResource = _messages.StringField(1)
  modifyTime = _messages.StringField(2)
  relativeResourceName = _messages.StringField(3)
  searchResultSubtype = _messages.StringField(4)
  searchResultType = _messages.EnumField('SearchResultTypeValueValuesEnum', 5)


class GoogleCloudDatacatalogV1beta1SerializedPolicyTag(_messages.Message):
  r"""Message representing one policy tag when exported as a nested proto.

  Fields:
    childPolicyTags: Children of the policy tag if any.
    description: Description of the serialized policy tag. The length of the
      description is limited to 2000 bytes when encoded in UTF-8. If not set,
      defaults to an empty description.
    displayName: Required. Display name of the policy tag. Max 200 bytes when
      encoded in UTF-8.
    policyTag: Resource name of the policy tag. This field will be ignored
      when calling ImportTaxonomies.
  """

  childPolicyTags = _messages.MessageField('GoogleCloudDatacatalogV1beta1SerializedPolicyTag', 1, repeated=True)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  policyTag = _messages.StringField(4)


class GoogleCloudDatacatalogV1beta1SerializedTaxonomy(_messages.Message):
  r"""Message capturing a taxonomy and its policy tag hierarchy as a nested
  proto. Used for taxonomy import/export and mutation.

  Enums:
    ActivatedPolicyTypesValueListEntryValuesEnum:

  Fields:
    activatedPolicyTypes: A list of policy types that are activated for a
      taxonomy.
    description: Description of the serialized taxonomy. The length of the
      description is limited to 2000 bytes when encoded in UTF-8. If not set,
      defaults to an empty description.
    displayName: Required. Display name of the taxonomy. Max 200 bytes when
      encoded in UTF-8.
    policyTags: Top level policy tags associated with the taxonomy if any.
  """

  class ActivatedPolicyTypesValueListEntryValuesEnum(_messages.Enum):
    r"""ActivatedPolicyTypesValueListEntryValuesEnum enum type.

    Values:
      POLICY_TYPE_UNSPECIFIED: Unspecified policy type.
      FINE_GRAINED_ACCESS_CONTROL: Fine grained access control policy, which
        enables access control on tagged resources.
    """
    POLICY_TYPE_UNSPECIFIED = 0
    FINE_GRAINED_ACCESS_CONTROL = 1

  activatedPolicyTypes = _messages.EnumField('ActivatedPolicyTypesValueListEntryValuesEnum', 1, repeated=True)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  policyTags = _messages.MessageField('GoogleCloudDatacatalogV1beta1SerializedPolicyTag', 4, repeated=True)


class GoogleCloudDatacatalogV1beta1SystemTimestamps(_messages.Message):
  r"""Timestamps about this resource according to a particular system.

  Fields:
    createTime: The creation time of the resource within the given system.
    expireTime: Output only. The expiration time of the resource within the
      given system. Currently only apllicable to BigQuery resources.
    updateTime: The last-modified time of the resource within the given
      system.
  """

  createTime = _messages.StringField(1)
  expireTime = _messages.StringField(2)
  updateTime = _messages.StringField(3)


class GoogleCloudDatacatalogV1beta1TableSpec(_messages.Message):
  r"""Normal BigQuery table spec.

  Fields:
    groupedEntry: Output only. If the table is a dated shard, i.e., with name
      pattern `[prefix]YYYYMMDD`, `grouped_entry` is the Data Catalog resource
      name of the date sharded grouped entry, for example, `projects/{project_
      id}/locations/{location}/entrygroups/{entry_group_id}/entries/{entry_id}
      `. Otherwise, `grouped_entry` is empty.
  """

  groupedEntry = _messages.StringField(1)


class GoogleCloudDatacatalogV1beta1Tag(_messages.Message):
  r"""Tags are used to attach custom metadata to Data Catalog resources. Tags
  conform to the specifications within their tag template. See [Data Catalog
  IAM](https://cloud.google.com/data-catalog/docs/concepts/iam) for
  information on the permissions needed to create or view tags.

  Messages:
    FieldsValue: Required. This maps the ID of a tag field to the value of and
      additional information about that field. Valid field IDs are defined by
      the tag's template. A tag must have at least 1 field and at most 500
      fields.

  Fields:
    column: Resources like Entry can have schemas associated with them. This
      scope allows users to attach tags to an individual column based on that
      schema. For attaching a tag to a nested column, use `.` to separate the
      column names. Example: * `outer_column.inner_column`
    fields: Required. This maps the ID of a tag field to the value of and
      additional information about that field. Valid field IDs are defined by
      the tag's template. A tag must have at least 1 field and at most 500
      fields.
    name: The resource name of the tag in URL format. Example: * projects/{pro
      ject_id}/locations/{location}/entrygroups/{entry_group_id}/entries/{entr
      y_id}/tags/{tag_id} where `tag_id` is a system-generated identifier.
      Note that this Tag may not actually be stored in the location in this
      name.
    template: Required. The resource name of the tag template that this tag
      uses. Example: * projects/{project_id}/locations/{location}/tagTemplates
      /{tag_template_id} This field cannot be modified after creation.
    templateDisplayName: Output only. The display name of the tag template.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FieldsValue(_messages.Message):
    r"""Required. This maps the ID of a tag field to the value of and
    additional information about that field. Valid field IDs are defined by
    the tag's template. A tag must have at least 1 field and at most 500
    fields.

    Messages:
      AdditionalProperty: An additional property for a FieldsValue object.

    Fields:
      additionalProperties: Additional properties of type FieldsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FieldsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudDatacatalogV1beta1TagField attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudDatacatalogV1beta1TagField', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  column = _messages.StringField(1)
  fields = _messages.MessageField('FieldsValue', 2)
  name = _messages.StringField(3)
  template = _messages.StringField(4)
  templateDisplayName = _messages.StringField(5)


class GoogleCloudDatacatalogV1beta1TagField(_messages.Message):
  r"""Contains the value and supporting information for a field within a Tag.

  Fields:
    boolValue: Holds the value for a tag field with boolean type.
    displayName: Output only. The display name of this field.
    doubleValue: Holds the value for a tag field with double type.
    enumValue: Holds the value for a tag field with enum type. This value must
      be one of the allowed values in the definition of this enum.
    order: Output only. The order of this field with respect to other fields
      in this tag. It can be set in Tag. For example, a higher value can
      indicate a more important field. The value can be negative. Multiple
      fields can have the same order, and field orders within a tag do not
      have to be sequential.
    stringValue: Holds the value for a tag field with string type.
    timestampValue: Holds the value for a tag field with timestamp type.
  """

  boolValue = _messages.BooleanField(1)
  displayName = _messages.StringField(2)
  doubleValue = _messages.FloatField(3)
  enumValue = _messages.MessageField('GoogleCloudDatacatalogV1beta1TagFieldEnumValue', 4)
  order = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  stringValue = _messages.StringField(6)
  timestampValue = _messages.StringField(7)


class GoogleCloudDatacatalogV1beta1TagFieldEnumValue(_messages.Message):
  r"""Holds an enum value.

  Fields:
    displayName: The display name of the enum value.
  """

  displayName = _messages.StringField(1)


class GoogleCloudDatacatalogV1beta1TagTemplate(_messages.Message):
  r"""A tag template defines a tag, which can have one or more typed fields.
  The template is used to create and attach the tag to Google Cloud resources.
  [Tag template roles](https://cloud.google.com/iam/docs/understanding-
  roles#data-catalog-roles) provide permissions to create, edit, and use the
  template. See, for example, the [TagTemplate
  User](https://cloud.google.com/data-catalog/docs/how-to/template-user) role,
  which includes permission to use the tag template to tag resources.

  Messages:
    FieldsValue: Required. Map of tag template field IDs to the settings for
      the field. This map is an exhaustive list of the allowed fields. This
      map must contain at least one field and at most 500 fields. The keys to
      this map are tag template field IDs. Field IDs can contain letters (both
      uppercase and lowercase), numbers (0-9) and underscores (_). Field IDs
      must be at least 1 character long and at most 64 characters long. Field
      IDs must start with a letter or underscore.

  Fields:
    displayName: The display name for this template. Defaults to an empty
      string.
    fields: Required. Map of tag template field IDs to the settings for the
      field. This map is an exhaustive list of the allowed fields. This map
      must contain at least one field and at most 500 fields. The keys to this
      map are tag template field IDs. Field IDs can contain letters (both
      uppercase and lowercase), numbers (0-9) and underscores (_). Field IDs
      must be at least 1 character long and at most 64 characters long. Field
      IDs must start with a letter or underscore.
    name: The resource name of the tag template in URL format. Example: *
      projects/{project_id}/locations/{location}/tagTemplates/{tag_template_id
      } Note that this TagTemplate and its child resources may not actually be
      stored in the location in this name.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FieldsValue(_messages.Message):
    r"""Required. Map of tag template field IDs to the settings for the field.
    This map is an exhaustive list of the allowed fields. This map must
    contain at least one field and at most 500 fields. The keys to this map
    are tag template field IDs. Field IDs can contain letters (both uppercase
    and lowercase), numbers (0-9) and underscores (_). Field IDs must be at
    least 1 character long and at most 64 characters long. Field IDs must
    start with a letter or underscore.

    Messages:
      AdditionalProperty: An additional property for a FieldsValue object.

    Fields:
      additionalProperties: Additional properties of type FieldsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FieldsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudDatacatalogV1beta1TagTemplateField attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudDatacatalogV1beta1TagTemplateField', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  fields = _messages.MessageField('FieldsValue', 2)
  name = _messages.StringField(3)


class GoogleCloudDatacatalogV1beta1TagTemplateField(_messages.Message):
  r"""The template for an individual field within a tag template.

  Fields:
    description: The description for this field. Defaults to an empty string.
    displayName: The display name for this field. Defaults to an empty string.
    isRequired: Whether this is a required field. Defaults to false.
    name: Output only. The resource name of the tag template field in URL
      format. Example: * projects/{project_id}/locations/{location}/tagTemplat
      es/{tag_template}/fields/{field} Note that this TagTemplateField may not
      actually be stored in the location in this name.
    order: The order of this field with respect to other fields in this tag
      template. A higher value indicates a more important field. The value can
      be negative. Multiple fields can have the same order, and field orders
      within a tag do not have to be sequential.
    type: Required. The type of value this tag field can contain.
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  isRequired = _messages.BooleanField(3)
  name = _messages.StringField(4)
  order = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  type = _messages.MessageField('GoogleCloudDatacatalogV1beta1FieldType', 6)


class GoogleCloudDatacatalogV1beta1Taxonomy(_messages.Message):
  r"""A taxonomy is a collection of policy tags that classify data along a
  common axis. For instance a data *sensitivity* taxonomy could contain policy
  tags denoting PII such as age, zipcode, and SSN. A data *origin* taxonomy
  could contain policy tags to distinguish user data, employee data, partner
  data, public data.

  Enums:
    ActivatedPolicyTypesValueListEntryValuesEnum:

  Fields:
    activatedPolicyTypes: Optional. A list of policy types that are activated
      for this taxonomy. If not set, defaults to an empty list.
    description: Optional. Description of this taxonomy. It must: contain only
      unicode characters, tabs, newlines, carriage returns and page breaks;
      and be at most 2000 bytes long when encoded in UTF-8. If not set,
      defaults to an empty description.
    displayName: Required. User defined name of this taxonomy. It must:
      contain only unicode letters, numbers, underscores, dashes and spaces;
      not start or end with spaces; and be at most 200 bytes long when encoded
      in UTF-8. The taxonomy display name must be unique within an
      organization.
    name: Output only. Resource name of this taxonomy, whose format is:
      "projects/{project_number}/locations/{location_id}/taxonomies/{id}".
    policyTagCount: Output only. Number of policy tags contained in this
      taxonomy.
    service: Output only. Identity of the service which owns the Taxonomy.
      This field is only populated when the taxonomy is created by a Google
      Cloud service. Currently only 'DATAPLEX' is supported.
    taxonomyTimestamps: Output only. Timestamps about this taxonomy. Only
      create_time and update_time are used.
  """

  class ActivatedPolicyTypesValueListEntryValuesEnum(_messages.Enum):
    r"""ActivatedPolicyTypesValueListEntryValuesEnum enum type.

    Values:
      POLICY_TYPE_UNSPECIFIED: Unspecified policy type.
      FINE_GRAINED_ACCESS_CONTROL: Fine grained access control policy, which
        enables access control on tagged resources.
    """
    POLICY_TYPE_UNSPECIFIED = 0
    FINE_GRAINED_ACCESS_CONTROL = 1

  activatedPolicyTypes = _messages.EnumField('ActivatedPolicyTypesValueListEntryValuesEnum', 1, repeated=True)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  name = _messages.StringField(4)
  policyTagCount = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  service = _messages.MessageField('GoogleCloudDatacatalogV1beta1TaxonomyService', 6)
  taxonomyTimestamps = _messages.MessageField('GoogleCloudDatacatalogV1beta1SystemTimestamps', 7)


class GoogleCloudDatacatalogV1beta1TaxonomyService(_messages.Message):
  r"""The source system of the Taxonomy.

  Enums:
    NameValueValuesEnum: The Google Cloud service name.

  Fields:
    identity: The service agent for the service.
    name: The Google Cloud service name.
  """

  class NameValueValuesEnum(_messages.Enum):
    r"""The Google Cloud service name.

    Values:
      MANAGING_SYSTEM_UNSPECIFIED: Default value
      MANAGING_SYSTEM_DATAPLEX: Dataplex.
      MANAGING_SYSTEM_OTHER: Other
    """
    MANAGING_SYSTEM_UNSPECIFIED = 0
    MANAGING_SYSTEM_DATAPLEX = 1
    MANAGING_SYSTEM_OTHER = 2

  identity = _messages.StringField(1)
  name = _messages.EnumField('NameValueValuesEnum', 2)


class GoogleCloudDatacatalogV1beta1UsageSignal(_messages.Message):
  r"""The set of all usage signals that we store in Data Catalog.

  Messages:
    UsageWithinTimeRangeValue: Usage statistics over each of the pre-defined
      time ranges, supported strings for time ranges are {"24H", "7D", "30D"}.

  Fields:
    updateTime: The timestamp of the end of the usage statistics duration.
    usageWithinTimeRange: Usage statistics over each of the pre-defined time
      ranges, supported strings for time ranges are {"24H", "7D", "30D"}.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class UsageWithinTimeRangeValue(_messages.Message):
    r"""Usage statistics over each of the pre-defined time ranges, supported
    strings for time ranges are {"24H", "7D", "30D"}.

    Messages:
      AdditionalProperty: An additional property for a
        UsageWithinTimeRangeValue object.

    Fields:
      additionalProperties: Additional properties of type
        UsageWithinTimeRangeValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a UsageWithinTimeRangeValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudDatacatalogV1beta1UsageStats attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudDatacatalogV1beta1UsageStats', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  updateTime = _messages.StringField(1)
  usageWithinTimeRange = _messages.MessageField('UsageWithinTimeRangeValue', 2)


class GoogleCloudDatacatalogV1beta1UsageStats(_messages.Message):
  r"""Detailed counts on the entry's usage. Caveats: - Only BigQuery tables
  have usage stats - The usage stats only include BigQuery query jobs - The
  usage stats might be underestimated, e.g. wildcard table references are not
  yet counted in usage computation
  https://cloud.google.com/bigquery/docs/querying-wildcard-tables

  Fields:
    totalCancellations: The number of times that the underlying entry was
      attempted to be used but was cancelled by the user.
    totalCompletions: The number of times that the underlying entry was
      successfully used.
    totalExecutionTimeForCompletionsMillis: Total time spent (in milliseconds)
      during uses the resulted in completions.
    totalFailures: The number of times that the underlying entry was attempted
      to be used but failed.
  """

  totalCancellations = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  totalCompletions = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  totalExecutionTimeForCompletionsMillis = _messages.FloatField(3, variant=_messages.Variant.FLOAT)
  totalFailures = _messages.FloatField(4, variant=_messages.Variant.FLOAT)


class GoogleCloudDatacatalogV1beta1ViewSpec(_messages.Message):
  r"""Table view specification.

  Fields:
    viewQuery: Output only. The query that defines the table view.
  """

  viewQuery = _messages.StringField(1)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  etag = _messages.BytesField(2)
  version = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
  """

  policy = _messages.MessageField('Policy', 1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

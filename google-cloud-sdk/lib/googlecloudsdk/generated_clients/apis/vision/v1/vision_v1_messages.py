"""Generated message classes for vision version v1.

Integrates Google Vision features, including image labeling, face, logo, and
landmark detection, optical character recognition (OCR), and detection of
explicit content, into applications.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'vision'


class AddProductToProductSetRequest(_messages.Message):
  r"""Request message for the `AddProductToProductSet` method.

  Fields:
    product: Required. The resource name for the Product to be added to this
      ProductSet. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`
  """

  product = _messages.StringField(1)


class AnnotateFileRequest(_messages.Message):
  r"""A request to annotate one single file, e.g. a PDF, TIFF or GIF file.

  Fields:
    features: Required. Requested features.
    imageContext: Additional context that may accompany the image(s) in the
      file.
    inputConfig: Required. Information about the input file.
    pages: Pages of the file to perform image annotation. Pages starts from 1,
      we assume the first page of the file is page 1. At most 5 pages are
      supported per request. Pages can be negative. Page 1 means the first
      page. Page 2 means the second page. Page -1 means the last page. Page -2
      means the second to the last page. If the file is GIF instead of PDF or
      TIFF, page refers to GIF frames. If this field is empty, by default the
      service performs image annotation for the first 5 pages of the file.
  """

  features = _messages.MessageField('Feature', 1, repeated=True)
  imageContext = _messages.MessageField('ImageContext', 2)
  inputConfig = _messages.MessageField('InputConfig', 3)
  pages = _messages.IntegerField(4, repeated=True, variant=_messages.Variant.INT32)


class AnnotateFileResponse(_messages.Message):
  r"""Response to a single file annotation request. A file may contain one or
  more images, which individually have their own responses.

  Fields:
    error: If set, represents the error message for the failed request. The
      `responses` field will not be set in this case.
    inputConfig: Information about the file for which this response is
      generated.
    responses: Individual responses to images found within the file. This
      field will be empty if the `error` field is set.
    totalPages: This field gives the total number of pages in the file.
  """

  error = _messages.MessageField('Status', 1)
  inputConfig = _messages.MessageField('InputConfig', 2)
  responses = _messages.MessageField('AnnotateImageResponse', 3, repeated=True)
  totalPages = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class AnnotateImageRequest(_messages.Message):
  r"""Request for performing Google Cloud Vision API tasks over a user-
  provided image, with user-requested features, and with context information.

  Fields:
    features: Requested features.
    image: The image to be processed.
    imageContext: Additional context that may accompany the image.
  """

  features = _messages.MessageField('Feature', 1, repeated=True)
  image = _messages.MessageField('Image', 2)
  imageContext = _messages.MessageField('ImageContext', 3)


class AnnotateImageResponse(_messages.Message):
  r"""Response to an image annotation request.

  Fields:
    context: If present, contextual information is needed to understand where
      this image comes from.
    cropHintsAnnotation: If present, crop hints have completed successfully.
    error: If set, represents the error message for the operation. Note that
      filled-in image annotations are guaranteed to be correct, even when
      `error` is set.
    faceAnnotations: If present, face detection has completed successfully.
    fullTextAnnotation: If present, text (OCR) detection or document (OCR)
      text detection has completed successfully. This annotation provides the
      structural hierarchy for the OCR detected text.
    imagePropertiesAnnotation: If present, image properties were extracted
      successfully.
    labelAnnotations: If present, label detection has completed successfully.
    landmarkAnnotations: If present, landmark detection has completed
      successfully.
    localizedObjectAnnotations: If present, localized object detection has
      completed successfully. This will be sorted descending by confidence
      score.
    logoAnnotations: If present, logo detection has completed successfully.
    productSearchResults: If present, product search has completed
      successfully.
    safeSearchAnnotation: If present, safe-search annotation has completed
      successfully.
    textAnnotations: If present, text (OCR) detection has completed
      successfully.
    webDetection: If present, web detection has completed successfully.
  """

  context = _messages.MessageField('ImageAnnotationContext', 1)
  cropHintsAnnotation = _messages.MessageField('CropHintsAnnotation', 2)
  error = _messages.MessageField('Status', 3)
  faceAnnotations = _messages.MessageField('FaceAnnotation', 4, repeated=True)
  fullTextAnnotation = _messages.MessageField('TextAnnotation', 5)
  imagePropertiesAnnotation = _messages.MessageField('ImageProperties', 6)
  labelAnnotations = _messages.MessageField('EntityAnnotation', 7, repeated=True)
  landmarkAnnotations = _messages.MessageField('EntityAnnotation', 8, repeated=True)
  localizedObjectAnnotations = _messages.MessageField('LocalizedObjectAnnotation', 9, repeated=True)
  logoAnnotations = _messages.MessageField('EntityAnnotation', 10, repeated=True)
  productSearchResults = _messages.MessageField('ProductSearchResults', 11)
  safeSearchAnnotation = _messages.MessageField('SafeSearchAnnotation', 12)
  textAnnotations = _messages.MessageField('EntityAnnotation', 13, repeated=True)
  webDetection = _messages.MessageField('WebDetection', 14)


class AsyncAnnotateFileRequest(_messages.Message):
  r"""An offline file annotation request.

  Fields:
    features: Required. Requested features.
    imageContext: Additional context that may accompany the image(s) in the
      file.
    inputConfig: Required. Information about the input file.
    outputConfig: Required. The desired output location and metadata (e.g.
      format).
  """

  features = _messages.MessageField('Feature', 1, repeated=True)
  imageContext = _messages.MessageField('ImageContext', 2)
  inputConfig = _messages.MessageField('InputConfig', 3)
  outputConfig = _messages.MessageField('OutputConfig', 4)


class AsyncAnnotateFileResponse(_messages.Message):
  r"""The response for a single offline file annotation request.

  Fields:
    outputConfig: The output location and metadata from
      AsyncAnnotateFileRequest.
  """

  outputConfig = _messages.MessageField('OutputConfig', 1)


class AsyncBatchAnnotateFilesRequest(_messages.Message):
  r"""Multiple async file annotation requests are batched into a single
  service call.

  Fields:
    parent: Optional. Target project and location to make a call. Format:
      `projects/{project-id}/locations/{location-id}`. If no parent is
      specified, a region will be chosen automatically. Supported location-
      ids: `us`: USA country only, `asia`: East asia areas, like Japan,
      Taiwan, `eu`: The European Union. Example:
      `projects/project-A/locations/eu`.
    requests: Required. Individual async file annotation requests for this
      batch.
  """

  parent = _messages.StringField(1)
  requests = _messages.MessageField('AsyncAnnotateFileRequest', 2, repeated=True)


class AsyncBatchAnnotateFilesResponse(_messages.Message):
  r"""Response to an async batch file annotation request.

  Fields:
    responses: The list of file annotation responses, one for each request in
      AsyncBatchAnnotateFilesRequest.
  """

  responses = _messages.MessageField('AsyncAnnotateFileResponse', 1, repeated=True)


class AsyncBatchAnnotateImagesRequest(_messages.Message):
  r"""Request for async image annotation for a list of images.

  Fields:
    outputConfig: Required. The desired output location and metadata (e.g.
      format).
    parent: Optional. Target project and location to make a call. Format:
      `projects/{project-id}/locations/{location-id}`. If no parent is
      specified, a region will be chosen automatically. Supported location-
      ids: `us`: USA country only, `asia`: East asia areas, like Japan,
      Taiwan, `eu`: The European Union. Example:
      `projects/project-A/locations/eu`.
    requests: Required. Individual image annotation requests for this batch.
  """

  outputConfig = _messages.MessageField('OutputConfig', 1)
  parent = _messages.StringField(2)
  requests = _messages.MessageField('AnnotateImageRequest', 3, repeated=True)


class AsyncBatchAnnotateImagesResponse(_messages.Message):
  r"""Response to an async batch image annotation request.

  Fields:
    outputConfig: The output location and metadata from
      AsyncBatchAnnotateImagesRequest.
  """

  outputConfig = _messages.MessageField('OutputConfig', 1)


class BatchAnnotateFilesRequest(_messages.Message):
  r"""A list of requests to annotate files using the BatchAnnotateFiles API.

  Fields:
    parent: Optional. Target project and location to make a call. Format:
      `projects/{project-id}/locations/{location-id}`. If no parent is
      specified, a region will be chosen automatically. Supported location-
      ids: `us`: USA country only, `asia`: East asia areas, like Japan,
      Taiwan, `eu`: The European Union. Example:
      `projects/project-A/locations/eu`.
    requests: Required. The list of file annotation requests. Right now we
      support only one AnnotateFileRequest in BatchAnnotateFilesRequest.
  """

  parent = _messages.StringField(1)
  requests = _messages.MessageField('AnnotateFileRequest', 2, repeated=True)


class BatchAnnotateFilesResponse(_messages.Message):
  r"""A list of file annotation responses.

  Fields:
    responses: The list of file annotation responses, each response
      corresponding to each AnnotateFileRequest in BatchAnnotateFilesRequest.
  """

  responses = _messages.MessageField('AnnotateFileResponse', 1, repeated=True)


class BatchAnnotateImagesRequest(_messages.Message):
  r"""Multiple image annotation requests are batched into a single service
  call.

  Fields:
    parent: Optional. Target project and location to make a call. Format:
      `projects/{project-id}/locations/{location-id}`. If no parent is
      specified, a region will be chosen automatically. Supported location-
      ids: `us`: USA country only, `asia`: East asia areas, like Japan,
      Taiwan, `eu`: The European Union. Example:
      `projects/project-A/locations/eu`.
    requests: Required. Individual image annotation requests for this batch.
  """

  parent = _messages.StringField(1)
  requests = _messages.MessageField('AnnotateImageRequest', 2, repeated=True)


class BatchAnnotateImagesResponse(_messages.Message):
  r"""Response to a batch image annotation request.

  Fields:
    responses: Individual responses to image annotation requests within the
      batch.
  """

  responses = _messages.MessageField('AnnotateImageResponse', 1, repeated=True)


class BatchOperationMetadata(_messages.Message):
  r"""Metadata for the batch operations such as the current state. This is
  included in the `metadata` field of the `Operation` returned by the
  `GetOperation` call of the `google::longrunning::Operations` service.

  Enums:
    StateValueValuesEnum: The current state of the batch operation.

  Fields:
    endTime: The time when the batch request is finished and
      google.longrunning.Operation.done is set to true.
    state: The current state of the batch operation.
    submitTime: The time when the batch request was submitted to the server.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The current state of the batch operation.

    Values:
      STATE_UNSPECIFIED: Invalid.
      PROCESSING: Request is actively being processed.
      SUCCESSFUL: The request is done and at least one item has been
        successfully processed.
      FAILED: The request is done and no item has been successfully processed.
      CANCELLED: The request is done after the
        longrunning.Operations.CancelOperation has been called by the user.
        Any records that were processed before the cancel command are output
        as specified in the request.
    """
    STATE_UNSPECIFIED = 0
    PROCESSING = 1
    SUCCESSFUL = 2
    FAILED = 3
    CANCELLED = 4

  endTime = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  submitTime = _messages.StringField(3)


class Block(_messages.Message):
  r"""Logical element on the page.

  Enums:
    BlockTypeValueValuesEnum: Detected block type (text, image etc) for this
      block.

  Fields:
    blockType: Detected block type (text, image etc) for this block.
    boundingBox: The bounding box for the block. The vertices are in the order
      of top-left, top-right, bottom-right, bottom-left. When a rotation of
      the bounding box is detected the rotation is represented as around the
      top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results on the block. Range [0, 1].
    paragraphs: List of paragraphs in this block (if this blocks is of type
      text).
    property: Additional information detected for the block.
  """

  class BlockTypeValueValuesEnum(_messages.Enum):
    r"""Detected block type (text, image etc) for this block.

    Values:
      UNKNOWN: Unknown block type.
      TEXT: Regular text block.
      TABLE: Table block.
      PICTURE: Image block.
      RULER: Horizontal/vertical line box.
      BARCODE: Barcode block.
    """
    UNKNOWN = 0
    TEXT = 1
    TABLE = 2
    PICTURE = 3
    RULER = 4
    BARCODE = 5

  blockType = _messages.EnumField('BlockTypeValueValuesEnum', 1)
  boundingBox = _messages.MessageField('BoundingPoly', 2)
  confidence = _messages.FloatField(3, variant=_messages.Variant.FLOAT)
  paragraphs = _messages.MessageField('Paragraph', 4, repeated=True)
  property = _messages.MessageField('TextProperty', 5)


class BoundingPoly(_messages.Message):
  r"""A bounding polygon for the detected image annotation.

  Fields:
    normalizedVertices: The bounding polygon normalized vertices.
    vertices: The bounding polygon vertices.
  """

  normalizedVertices = _messages.MessageField('NormalizedVertex', 1, repeated=True)
  vertices = _messages.MessageField('Vertex', 2, repeated=True)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Color(_messages.Message):
  r"""Represents a color in the RGBA color space. This representation is
  designed for simplicity of conversion to and from color representations in
  various languages over compactness. For example, the fields of this
  representation can be trivially provided to the constructor of
  `java.awt.Color` in Java; it can also be trivially provided to UIColor's
  `+colorWithRed:green:blue:alpha` method in iOS; and, with just a little
  work, it can be easily formatted into a CSS `rgba()` string in JavaScript.
  This reference page doesn't have information about the absolute color space
  that should be used to interpret the RGB value-for example, sRGB, Adobe RGB,
  DCI-P3, and BT.2020. By default, applications should assume the sRGB color
  space. When color equality needs to be decided, implementations, unless
  documented otherwise, treat two colors as equal if all their red, green,
  blue, and alpha values each differ by at most `1e-5`. Example (Java): import
  com.google.type.Color; // ... public static java.awt.Color fromProto(Color
  protocolor) { float alpha = protocolor.hasAlpha() ?
  protocolor.getAlpha().getValue() : 1.0; return new java.awt.Color(
  protocolor.getRed(), protocolor.getGreen(), protocolor.getBlue(), alpha); }
  public static Color toProto(java.awt.Color color) { float red = (float)
  color.getRed(); float green = (float) color.getGreen(); float blue = (float)
  color.getBlue(); float denominator = 255.0; Color.Builder resultBuilder =
  Color .newBuilder() .setRed(red / denominator) .setGreen(green /
  denominator) .setBlue(blue / denominator); int alpha = color.getAlpha(); if
  (alpha != 255) { result.setAlpha( FloatValue .newBuilder()
  .setValue(((float) alpha) / denominator) .build()); } return
  resultBuilder.build(); } // ... Example (iOS / Obj-C): // ... static
  UIColor* fromProto(Color* protocolor) { float red = [protocolor red]; float
  green = [protocolor green]; float blue = [protocolor blue]; FloatValue*
  alpha_wrapper = [protocolor alpha]; float alpha = 1.0; if (alpha_wrapper !=
  nil) { alpha = [alpha_wrapper value]; } return [UIColor colorWithRed:red
  green:green blue:blue alpha:alpha]; } static Color* toProto(UIColor* color)
  { CGFloat red, green, blue, alpha; if (![color getRed:&red green:&green
  blue:&blue alpha:&alpha]) { return nil; } Color* result = [[Color alloc]
  init]; [result setRed:red]; [result setGreen:green]; [result setBlue:blue];
  if (alpha <= 0.9999) { [result setAlpha:floatWrapperWithValue(alpha)]; }
  [result autorelease]; return result; } // ... Example (JavaScript): // ...
  var protoToCssColor = function(rgb_color) { var redFrac = rgb_color.red ||
  0.0; var greenFrac = rgb_color.green || 0.0; var blueFrac = rgb_color.blue
  || 0.0; var red = Math.floor(redFrac * 255); var green =
  Math.floor(greenFrac * 255); var blue = Math.floor(blueFrac * 255); if
  (!('alpha' in rgb_color)) { return rgbToCssColor(red, green, blue); } var
  alphaFrac = rgb_color.alpha.value || 0.0; var rgbParams = [red, green,
  blue].join(','); return ['rgba(', rgbParams, ',', alphaFrac, ')'].join('');
  }; var rgbToCssColor = function(red, green, blue) { var rgbNumber = new
  Number((red << 16) | (green << 8) | blue); var hexString =
  rgbNumber.toString(16); var missingZeros = 6 - hexString.length; var
  resultBuilder = ['#']; for (var i = 0; i < missingZeros; i++) {
  resultBuilder.push('0'); } resultBuilder.push(hexString); return
  resultBuilder.join(''); }; // ...

  Fields:
    alpha: The fraction of this color that should be applied to the pixel.
      That is, the final pixel color is defined by the equation: `pixel color
      = alpha * (this color) + (1.0 - alpha) * (background color)` This means
      that a value of 1.0 corresponds to a solid color, whereas a value of 0.0
      corresponds to a completely transparent color. This uses a wrapper
      message rather than a simple float scalar so that it is possible to
      distinguish between a default value and the value being unset. If
      omitted, this color object is rendered as a solid color (as if the alpha
      value had been explicitly given a value of 1.0).
    blue: The amount of blue in the color as a value in the interval [0, 1].
    green: The amount of green in the color as a value in the interval [0, 1].
    red: The amount of red in the color as a value in the interval [0, 1].
  """

  alpha = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  blue = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  green = _messages.FloatField(3, variant=_messages.Variant.FLOAT)
  red = _messages.FloatField(4, variant=_messages.Variant.FLOAT)


class ColorInfo(_messages.Message):
  r"""Color information consists of RGB channels, score, and the fraction of
  the image that the color occupies in the image.

  Fields:
    color: RGB components of the color.
    pixelFraction: The fraction of pixels the color occupies in the image.
      Value in range [0, 1].
    score: Image-specific score for this color. Value in range [0, 1].
  """

  color = _messages.MessageField('Color', 1)
  pixelFraction = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  score = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class CropHint(_messages.Message):
  r"""Single crop hint that is used to generate a new crop when serving an
  image.

  Fields:
    boundingPoly: The bounding polygon for the crop region. The coordinates of
      the bounding box are in the original image's scale.
    confidence: Confidence of this being a salient region. Range [0, 1].
    importanceFraction: Fraction of importance of this salient region with
      respect to the original image.
  """

  boundingPoly = _messages.MessageField('BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  importanceFraction = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class CropHintsAnnotation(_messages.Message):
  r"""Set of crop hints that are used to generate new crops when serving
  images.

  Fields:
    cropHints: Crop hint results.
  """

  cropHints = _messages.MessageField('CropHint', 1, repeated=True)


class CropHintsParams(_messages.Message):
  r"""Parameters for crop hints annotation request.

  Fields:
    aspectRatios: Aspect ratios in floats, representing the ratio of the width
      to the height of the image. For example, if the desired aspect ratio is
      4/3, the corresponding float value should be 1.33333. If not specified,
      the best possible crop is returned. The number of provided aspect ratios
      is limited to a maximum of 16; any aspect ratios provided after the 16th
      are ignored.
  """

  aspectRatios = _messages.FloatField(1, repeated=True, variant=_messages.Variant.FLOAT)


class DetectedBreak(_messages.Message):
  r"""Detected start or end of a structural component.

  Enums:
    TypeValueValuesEnum: Detected break type.

  Fields:
    isPrefix: True if break prepends the element.
    type: Detected break type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Detected break type.

    Values:
      UNKNOWN: Unknown break label type.
      SPACE: Regular space.
      SURE_SPACE: Sure space (very wide).
      EOL_SURE_SPACE: Line-wrapping break.
      HYPHEN: End-line hyphen that is not present in text; does not co-occur
        with `SPACE`, `LEADER_SPACE`, or `LINE_BREAK`.
      LINE_BREAK: Line break that ends a paragraph.
    """
    UNKNOWN = 0
    SPACE = 1
    SURE_SPACE = 2
    EOL_SURE_SPACE = 3
    HYPHEN = 4
    LINE_BREAK = 5

  isPrefix = _messages.BooleanField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class DetectedLanguage(_messages.Message):
  r"""Detected language for a structural component.

  Fields:
    confidence: Confidence of detected language. Range [0, 1].
    languageCode: The BCP-47 language code, such as "en-US" or "sr-Latn". For
      more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
  """

  confidence = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  languageCode = _messages.StringField(2)


class DominantColorsAnnotation(_messages.Message):
  r"""Set of dominant colors and their corresponding scores.

  Fields:
    colors: RGB color values with their score and pixel fraction.
  """

  colors = _messages.MessageField('ColorInfo', 1, repeated=True)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EntityAnnotation(_messages.Message):
  r"""Set of detected entity features.

  Fields:
    boundingPoly: Image region to which this entity belongs. Not produced for
      `LABEL_DETECTION` features.
    confidence: **Deprecated. Use `score` instead.** The accuracy of the
      entity detection in an image. For example, for an image in which the
      "Eiffel Tower" entity is detected, this field represents the confidence
      that there is a tower in the query image. Range [0, 1].
    description: Entity textual description, expressed in its `locale`
      language.
    locale: The language code for the locale in which the entity textual
      `description` is expressed.
    locations: The location information for the detected entity. Multiple
      `LocationInfo` elements can be present because one location may indicate
      the location of the scene in the image, and another location may
      indicate the location of the place where the image was taken. Location
      information is usually present for landmarks.
    mid: Opaque entity ID. Some IDs may be available in [Google Knowledge
      Graph Search API](https://developers.google.com/knowledge-graph/).
    properties: Some entities may have optional user-supplied `Property`
      (name/value) fields, such a score or string that qualifies the entity.
    score: Overall score of the result. Range [0, 1].
    topicality: The relevancy of the ICA (Image Content Annotation) label to
      the image. For example, the relevancy of "tower" is likely higher to an
      image containing the detected "Eiffel Tower" than to an image containing
      a detected distant towering building, even though the confidence that
      there is a tower in each image may be the same. Range [0, 1].
  """

  boundingPoly = _messages.MessageField('BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  description = _messages.StringField(3)
  locale = _messages.StringField(4)
  locations = _messages.MessageField('LocationInfo', 5, repeated=True)
  mid = _messages.StringField(6)
  properties = _messages.MessageField('Property', 7, repeated=True)
  score = _messages.FloatField(8, variant=_messages.Variant.FLOAT)
  topicality = _messages.FloatField(9, variant=_messages.Variant.FLOAT)


class FaceAnnotation(_messages.Message):
  r"""A face annotation object contains the results of face detection.

  Enums:
    AngerLikelihoodValueValuesEnum: Anger likelihood.
    BlurredLikelihoodValueValuesEnum: Blurred likelihood.
    HeadwearLikelihoodValueValuesEnum: Headwear likelihood.
    JoyLikelihoodValueValuesEnum: Joy likelihood.
    SorrowLikelihoodValueValuesEnum: Sorrow likelihood.
    SurpriseLikelihoodValueValuesEnum: Surprise likelihood.
    UnderExposedLikelihoodValueValuesEnum: Under-exposed likelihood.

  Fields:
    angerLikelihood: Anger likelihood.
    blurredLikelihood: Blurred likelihood.
    boundingPoly: The bounding polygon around the face. The coordinates of the
      bounding box are in the original image's scale. The bounding box is
      computed to "frame" the face in accordance with human expectations. It
      is based on the landmarker results. Note that one or more x and/or y
      coordinates may not be generated in the `BoundingPoly` (the polygon will
      be unbounded) if only a partial face appears in the image to be
      annotated.
    detectionConfidence: Detection confidence. Range [0, 1].
    fdBoundingPoly: The `fd_bounding_poly` bounding polygon is tighter than
      the `boundingPoly`, and encloses only the skin part of the face.
      Typically, it is used to eliminate the face from any image analysis that
      detects the "amount of skin" visible in an image. It is not based on the
      landmarker results, only on the initial face detection, hence the fd
      (face detection) prefix.
    headwearLikelihood: Headwear likelihood.
    joyLikelihood: Joy likelihood.
    landmarkingConfidence: Face landmarking confidence. Range [0, 1].
    landmarks: Detected face landmarks.
    panAngle: Yaw angle, which indicates the leftward/rightward angle that the
      face is pointing relative to the vertical plane perpendicular to the
      image. Range [-180,180].
    rollAngle: Roll angle, which indicates the amount of clockwise/anti-
      clockwise rotation of the face relative to the image vertical about the
      axis perpendicular to the face. Range [-180,180].
    sorrowLikelihood: Sorrow likelihood.
    surpriseLikelihood: Surprise likelihood.
    tiltAngle: Pitch angle, which indicates the upwards/downwards angle that
      the face is pointing relative to the image's horizontal plane. Range
      [-180,180].
    underExposedLikelihood: Under-exposed likelihood.
  """

  class AngerLikelihoodValueValuesEnum(_messages.Enum):
    r"""Anger likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class BlurredLikelihoodValueValuesEnum(_messages.Enum):
    r"""Blurred likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class HeadwearLikelihoodValueValuesEnum(_messages.Enum):
    r"""Headwear likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class JoyLikelihoodValueValuesEnum(_messages.Enum):
    r"""Joy likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class SorrowLikelihoodValueValuesEnum(_messages.Enum):
    r"""Sorrow likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class SurpriseLikelihoodValueValuesEnum(_messages.Enum):
    r"""Surprise likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class UnderExposedLikelihoodValueValuesEnum(_messages.Enum):
    r"""Under-exposed likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  angerLikelihood = _messages.EnumField('AngerLikelihoodValueValuesEnum', 1)
  blurredLikelihood = _messages.EnumField('BlurredLikelihoodValueValuesEnum', 2)
  boundingPoly = _messages.MessageField('BoundingPoly', 3)
  detectionConfidence = _messages.FloatField(4, variant=_messages.Variant.FLOAT)
  fdBoundingPoly = _messages.MessageField('BoundingPoly', 5)
  headwearLikelihood = _messages.EnumField('HeadwearLikelihoodValueValuesEnum', 6)
  joyLikelihood = _messages.EnumField('JoyLikelihoodValueValuesEnum', 7)
  landmarkingConfidence = _messages.FloatField(8, variant=_messages.Variant.FLOAT)
  landmarks = _messages.MessageField('Landmark', 9, repeated=True)
  panAngle = _messages.FloatField(10, variant=_messages.Variant.FLOAT)
  rollAngle = _messages.FloatField(11, variant=_messages.Variant.FLOAT)
  sorrowLikelihood = _messages.EnumField('SorrowLikelihoodValueValuesEnum', 12)
  surpriseLikelihood = _messages.EnumField('SurpriseLikelihoodValueValuesEnum', 13)
  tiltAngle = _messages.FloatField(14, variant=_messages.Variant.FLOAT)
  underExposedLikelihood = _messages.EnumField('UnderExposedLikelihoodValueValuesEnum', 15)


class Feature(_messages.Message):
  r"""The type of Google Cloud Vision API detection to perform, and the
  maximum number of results to return for that type. Multiple `Feature`
  objects can be specified in the `features` list.

  Enums:
    TypeValueValuesEnum: The feature type.

  Fields:
    maxResults: Maximum number of results of this type. Does not apply to
      `TEXT_DETECTION`, `DOCUMENT_TEXT_DETECTION`, or `CROP_HINTS`.
    model: Model to use for the feature. Supported values: "builtin/stable"
      (the default if unset) and "builtin/latest". `DOCUMENT_TEXT_DETECTION`
      and `TEXT_DETECTION` also support "builtin/weekly" for the bleeding edge
      release updated weekly.
    type: The feature type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The feature type.

    Values:
      TYPE_UNSPECIFIED: Unspecified feature type.
      FACE_DETECTION: Run face detection.
      LANDMARK_DETECTION: Run landmark detection.
      LOGO_DETECTION: Run logo detection.
      LABEL_DETECTION: Run label detection.
      TEXT_DETECTION: Run text detection / optical character recognition
        (OCR). Text detection is optimized for areas of text within a larger
        image; if the image is a document, use `DOCUMENT_TEXT_DETECTION`
        instead.
      DOCUMENT_TEXT_DETECTION: Run dense text document OCR. Takes precedence
        when both `DOCUMENT_TEXT_DETECTION` and `TEXT_DETECTION` are present.
      SAFE_SEARCH_DETECTION: Run Safe Search to detect potentially unsafe or
        undesirable content.
      IMAGE_PROPERTIES: Compute a set of image properties, such as the image's
        dominant colors.
      CROP_HINTS: Run crop hints.
      WEB_DETECTION: Run web detection.
      PRODUCT_SEARCH: Run Product Search.
      OBJECT_LOCALIZATION: Run localizer for object detection.
    """
    TYPE_UNSPECIFIED = 0
    FACE_DETECTION = 1
    LANDMARK_DETECTION = 2
    LOGO_DETECTION = 3
    LABEL_DETECTION = 4
    TEXT_DETECTION = 5
    DOCUMENT_TEXT_DETECTION = 6
    SAFE_SEARCH_DETECTION = 7
    IMAGE_PROPERTIES = 8
    CROP_HINTS = 9
    WEB_DETECTION = 10
    PRODUCT_SEARCH = 11
    OBJECT_LOCALIZATION = 12

  maxResults = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  model = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class GcsDestination(_messages.Message):
  r"""The Google Cloud Storage location where the output will be written to.

  Fields:
    uri: Google Cloud Storage URI prefix where the results will be stored.
      Results will be in JSON format and preceded by its corresponding input
      URI prefix. This field can either represent a gcs file prefix or gcs
      directory. In either case, the uri should be unique because in order to
      get all of the output files, you will need to do a wildcard gcs search
      on the uri prefix you provide. Examples: * File Prefix: gs://bucket-
      name/here/filenameprefix The output files will be created in
      gs://bucket-name/here/ and the names of the output files will begin with
      "filenameprefix". * Directory Prefix: gs://bucket-name/some/location/
      The output files will be created in gs://bucket-name/some/location/ and
      the names of the output files could be anything because there was no
      filename prefix specified. If multiple outputs, each response is still
      AnnotateFileResponse, each of which contains some subset of the full
      list of AnnotateImageResponse. Multiple outputs can happen if, for
      example, the output JSON is too large and overflows into multiple
      sharded files.
  """

  uri = _messages.StringField(1)


class GcsSource(_messages.Message):
  r"""The Google Cloud Storage location where the input will be read from.

  Fields:
    uri: Google Cloud Storage URI for the input file. This must only be a
      Google Cloud Storage object. Wildcards are not currently supported.
  """

  uri = _messages.StringField(1)


class GoogleCloudVisionV1p1beta1AnnotateFileResponse(_messages.Message):
  r"""Response to a single file annotation request. A file may contain one or
  more images, which individually have their own responses.

  Fields:
    error: If set, represents the error message for the failed request. The
      `responses` field will not be set in this case.
    inputConfig: Information about the file for which this response is
      generated.
    responses: Individual responses to images found within the file. This
      field will be empty if the `error` field is set.
    totalPages: This field gives the total number of pages in the file.
  """

  error = _messages.MessageField('Status', 1)
  inputConfig = _messages.MessageField('GoogleCloudVisionV1p1beta1InputConfig', 2)
  responses = _messages.MessageField('GoogleCloudVisionV1p1beta1AnnotateImageResponse', 3, repeated=True)
  totalPages = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudVisionV1p1beta1AnnotateImageResponse(_messages.Message):
  r"""Response to an image annotation request.

  Fields:
    context: If present, contextual information is needed to understand where
      this image comes from.
    cropHintsAnnotation: If present, crop hints have completed successfully.
    error: If set, represents the error message for the operation. Note that
      filled-in image annotations are guaranteed to be correct, even when
      `error` is set.
    faceAnnotations: If present, face detection has completed successfully.
    fullTextAnnotation: If present, text (OCR) detection or document (OCR)
      text detection has completed successfully. This annotation provides the
      structural hierarchy for the OCR detected text.
    imagePropertiesAnnotation: If present, image properties were extracted
      successfully.
    labelAnnotations: If present, label detection has completed successfully.
    landmarkAnnotations: If present, landmark detection has completed
      successfully.
    localizedObjectAnnotations: If present, localized object detection has
      completed successfully. This will be sorted descending by confidence
      score.
    logoAnnotations: If present, logo detection has completed successfully.
    productSearchResults: If present, product search has completed
      successfully.
    safeSearchAnnotation: If present, safe-search annotation has completed
      successfully.
    textAnnotations: If present, text (OCR) detection has completed
      successfully.
    webDetection: If present, web detection has completed successfully.
  """

  context = _messages.MessageField('GoogleCloudVisionV1p1beta1ImageAnnotationContext', 1)
  cropHintsAnnotation = _messages.MessageField('GoogleCloudVisionV1p1beta1CropHintsAnnotation', 2)
  error = _messages.MessageField('Status', 3)
  faceAnnotations = _messages.MessageField('GoogleCloudVisionV1p1beta1FaceAnnotation', 4, repeated=True)
  fullTextAnnotation = _messages.MessageField('GoogleCloudVisionV1p1beta1TextAnnotation', 5)
  imagePropertiesAnnotation = _messages.MessageField('GoogleCloudVisionV1p1beta1ImageProperties', 6)
  labelAnnotations = _messages.MessageField('GoogleCloudVisionV1p1beta1EntityAnnotation', 7, repeated=True)
  landmarkAnnotations = _messages.MessageField('GoogleCloudVisionV1p1beta1EntityAnnotation', 8, repeated=True)
  localizedObjectAnnotations = _messages.MessageField('GoogleCloudVisionV1p1beta1LocalizedObjectAnnotation', 9, repeated=True)
  logoAnnotations = _messages.MessageField('GoogleCloudVisionV1p1beta1EntityAnnotation', 10, repeated=True)
  productSearchResults = _messages.MessageField('GoogleCloudVisionV1p1beta1ProductSearchResults', 11)
  safeSearchAnnotation = _messages.MessageField('GoogleCloudVisionV1p1beta1SafeSearchAnnotation', 12)
  textAnnotations = _messages.MessageField('GoogleCloudVisionV1p1beta1EntityAnnotation', 13, repeated=True)
  webDetection = _messages.MessageField('GoogleCloudVisionV1p1beta1WebDetection', 14)


class GoogleCloudVisionV1p1beta1AsyncAnnotateFileResponse(_messages.Message):
  r"""The response for a single offline file annotation request.

  Fields:
    outputConfig: The output location and metadata from
      AsyncAnnotateFileRequest.
  """

  outputConfig = _messages.MessageField('GoogleCloudVisionV1p1beta1OutputConfig', 1)


class GoogleCloudVisionV1p1beta1AsyncBatchAnnotateFilesResponse(_messages.Message):
  r"""Response to an async batch file annotation request.

  Fields:
    responses: The list of file annotation responses, one for each request in
      AsyncBatchAnnotateFilesRequest.
  """

  responses = _messages.MessageField('GoogleCloudVisionV1p1beta1AsyncAnnotateFileResponse', 1, repeated=True)


class GoogleCloudVisionV1p1beta1Block(_messages.Message):
  r"""Logical element on the page.

  Enums:
    BlockTypeValueValuesEnum: Detected block type (text, image etc) for this
      block.

  Fields:
    blockType: Detected block type (text, image etc) for this block.
    boundingBox: The bounding box for the block. The vertices are in the order
      of top-left, top-right, bottom-right, bottom-left. When a rotation of
      the bounding box is detected the rotation is represented as around the
      top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results on the block. Range [0, 1].
    paragraphs: List of paragraphs in this block (if this blocks is of type
      text).
    property: Additional information detected for the block.
  """

  class BlockTypeValueValuesEnum(_messages.Enum):
    r"""Detected block type (text, image etc) for this block.

    Values:
      UNKNOWN: Unknown block type.
      TEXT: Regular text block.
      TABLE: Table block.
      PICTURE: Image block.
      RULER: Horizontal/vertical line box.
      BARCODE: Barcode block.
    """
    UNKNOWN = 0
    TEXT = 1
    TABLE = 2
    PICTURE = 3
    RULER = 4
    BARCODE = 5

  blockType = _messages.EnumField('BlockTypeValueValuesEnum', 1)
  boundingBox = _messages.MessageField('GoogleCloudVisionV1p1beta1BoundingPoly', 2)
  confidence = _messages.FloatField(3, variant=_messages.Variant.FLOAT)
  paragraphs = _messages.MessageField('GoogleCloudVisionV1p1beta1Paragraph', 4, repeated=True)
  property = _messages.MessageField('GoogleCloudVisionV1p1beta1TextAnnotationTextProperty', 5)


class GoogleCloudVisionV1p1beta1BoundingPoly(_messages.Message):
  r"""A bounding polygon for the detected image annotation.

  Fields:
    normalizedVertices: The bounding polygon normalized vertices.
    vertices: The bounding polygon vertices.
  """

  normalizedVertices = _messages.MessageField('GoogleCloudVisionV1p1beta1NormalizedVertex', 1, repeated=True)
  vertices = _messages.MessageField('GoogleCloudVisionV1p1beta1Vertex', 2, repeated=True)


class GoogleCloudVisionV1p1beta1ColorInfo(_messages.Message):
  r"""Color information consists of RGB channels, score, and the fraction of
  the image that the color occupies in the image.

  Fields:
    color: RGB components of the color.
    pixelFraction: The fraction of pixels the color occupies in the image.
      Value in range [0, 1].
    score: Image-specific score for this color. Value in range [0, 1].
  """

  color = _messages.MessageField('Color', 1)
  pixelFraction = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  score = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p1beta1CropHint(_messages.Message):
  r"""Single crop hint that is used to generate a new crop when serving an
  image.

  Fields:
    boundingPoly: The bounding polygon for the crop region. The coordinates of
      the bounding box are in the original image's scale.
    confidence: Confidence of this being a salient region. Range [0, 1].
    importanceFraction: Fraction of importance of this salient region with
      respect to the original image.
  """

  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p1beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  importanceFraction = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p1beta1CropHintsAnnotation(_messages.Message):
  r"""Set of crop hints that are used to generate new crops when serving
  images.

  Fields:
    cropHints: Crop hint results.
  """

  cropHints = _messages.MessageField('GoogleCloudVisionV1p1beta1CropHint', 1, repeated=True)


class GoogleCloudVisionV1p1beta1DominantColorsAnnotation(_messages.Message):
  r"""Set of dominant colors and their corresponding scores.

  Fields:
    colors: RGB color values with their score and pixel fraction.
  """

  colors = _messages.MessageField('GoogleCloudVisionV1p1beta1ColorInfo', 1, repeated=True)


class GoogleCloudVisionV1p1beta1EntityAnnotation(_messages.Message):
  r"""Set of detected entity features.

  Fields:
    boundingPoly: Image region to which this entity belongs. Not produced for
      `LABEL_DETECTION` features.
    confidence: **Deprecated. Use `score` instead.** The accuracy of the
      entity detection in an image. For example, for an image in which the
      "Eiffel Tower" entity is detected, this field represents the confidence
      that there is a tower in the query image. Range [0, 1].
    description: Entity textual description, expressed in its `locale`
      language.
    locale: The language code for the locale in which the entity textual
      `description` is expressed.
    locations: The location information for the detected entity. Multiple
      `LocationInfo` elements can be present because one location may indicate
      the location of the scene in the image, and another location may
      indicate the location of the place where the image was taken. Location
      information is usually present for landmarks.
    mid: Opaque entity ID. Some IDs may be available in [Google Knowledge
      Graph Search API](https://developers.google.com/knowledge-graph/).
    properties: Some entities may have optional user-supplied `Property`
      (name/value) fields, such a score or string that qualifies the entity.
    score: Overall score of the result. Range [0, 1].
    topicality: The relevancy of the ICA (Image Content Annotation) label to
      the image. For example, the relevancy of "tower" is likely higher to an
      image containing the detected "Eiffel Tower" than to an image containing
      a detected distant towering building, even though the confidence that
      there is a tower in each image may be the same. Range [0, 1].
  """

  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p1beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  description = _messages.StringField(3)
  locale = _messages.StringField(4)
  locations = _messages.MessageField('GoogleCloudVisionV1p1beta1LocationInfo', 5, repeated=True)
  mid = _messages.StringField(6)
  properties = _messages.MessageField('GoogleCloudVisionV1p1beta1Property', 7, repeated=True)
  score = _messages.FloatField(8, variant=_messages.Variant.FLOAT)
  topicality = _messages.FloatField(9, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p1beta1FaceAnnotation(_messages.Message):
  r"""A face annotation object contains the results of face detection.

  Enums:
    AngerLikelihoodValueValuesEnum: Anger likelihood.
    BlurredLikelihoodValueValuesEnum: Blurred likelihood.
    HeadwearLikelihoodValueValuesEnum: Headwear likelihood.
    JoyLikelihoodValueValuesEnum: Joy likelihood.
    SorrowLikelihoodValueValuesEnum: Sorrow likelihood.
    SurpriseLikelihoodValueValuesEnum: Surprise likelihood.
    UnderExposedLikelihoodValueValuesEnum: Under-exposed likelihood.

  Fields:
    angerLikelihood: Anger likelihood.
    blurredLikelihood: Blurred likelihood.
    boundingPoly: The bounding polygon around the face. The coordinates of the
      bounding box are in the original image's scale. The bounding box is
      computed to "frame" the face in accordance with human expectations. It
      is based on the landmarker results. Note that one or more x and/or y
      coordinates may not be generated in the `BoundingPoly` (the polygon will
      be unbounded) if only a partial face appears in the image to be
      annotated.
    detectionConfidence: Detection confidence. Range [0, 1].
    fdBoundingPoly: The `fd_bounding_poly` bounding polygon is tighter than
      the `boundingPoly`, and encloses only the skin part of the face.
      Typically, it is used to eliminate the face from any image analysis that
      detects the "amount of skin" visible in an image. It is not based on the
      landmarker results, only on the initial face detection, hence the fd
      (face detection) prefix.
    headwearLikelihood: Headwear likelihood.
    joyLikelihood: Joy likelihood.
    landmarkingConfidence: Face landmarking confidence. Range [0, 1].
    landmarks: Detected face landmarks.
    panAngle: Yaw angle, which indicates the leftward/rightward angle that the
      face is pointing relative to the vertical plane perpendicular to the
      image. Range [-180,180].
    rollAngle: Roll angle, which indicates the amount of clockwise/anti-
      clockwise rotation of the face relative to the image vertical about the
      axis perpendicular to the face. Range [-180,180].
    sorrowLikelihood: Sorrow likelihood.
    surpriseLikelihood: Surprise likelihood.
    tiltAngle: Pitch angle, which indicates the upwards/downwards angle that
      the face is pointing relative to the image's horizontal plane. Range
      [-180,180].
    underExposedLikelihood: Under-exposed likelihood.
  """

  class AngerLikelihoodValueValuesEnum(_messages.Enum):
    r"""Anger likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class BlurredLikelihoodValueValuesEnum(_messages.Enum):
    r"""Blurred likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class HeadwearLikelihoodValueValuesEnum(_messages.Enum):
    r"""Headwear likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class JoyLikelihoodValueValuesEnum(_messages.Enum):
    r"""Joy likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class SorrowLikelihoodValueValuesEnum(_messages.Enum):
    r"""Sorrow likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class SurpriseLikelihoodValueValuesEnum(_messages.Enum):
    r"""Surprise likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class UnderExposedLikelihoodValueValuesEnum(_messages.Enum):
    r"""Under-exposed likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  angerLikelihood = _messages.EnumField('AngerLikelihoodValueValuesEnum', 1)
  blurredLikelihood = _messages.EnumField('BlurredLikelihoodValueValuesEnum', 2)
  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p1beta1BoundingPoly', 3)
  detectionConfidence = _messages.FloatField(4, variant=_messages.Variant.FLOAT)
  fdBoundingPoly = _messages.MessageField('GoogleCloudVisionV1p1beta1BoundingPoly', 5)
  headwearLikelihood = _messages.EnumField('HeadwearLikelihoodValueValuesEnum', 6)
  joyLikelihood = _messages.EnumField('JoyLikelihoodValueValuesEnum', 7)
  landmarkingConfidence = _messages.FloatField(8, variant=_messages.Variant.FLOAT)
  landmarks = _messages.MessageField('GoogleCloudVisionV1p1beta1FaceAnnotationLandmark', 9, repeated=True)
  panAngle = _messages.FloatField(10, variant=_messages.Variant.FLOAT)
  rollAngle = _messages.FloatField(11, variant=_messages.Variant.FLOAT)
  sorrowLikelihood = _messages.EnumField('SorrowLikelihoodValueValuesEnum', 12)
  surpriseLikelihood = _messages.EnumField('SurpriseLikelihoodValueValuesEnum', 13)
  tiltAngle = _messages.FloatField(14, variant=_messages.Variant.FLOAT)
  underExposedLikelihood = _messages.EnumField('UnderExposedLikelihoodValueValuesEnum', 15)


class GoogleCloudVisionV1p1beta1FaceAnnotationLandmark(_messages.Message):
  r"""A face-specific landmark (for example, a face feature).

  Enums:
    TypeValueValuesEnum: Face landmark type.

  Fields:
    position: Face landmark position.
    type: Face landmark type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Face landmark type.

    Values:
      UNKNOWN_LANDMARK: Unknown face landmark detected. Should not be filled.
      LEFT_EYE: Left eye.
      RIGHT_EYE: Right eye.
      LEFT_OF_LEFT_EYEBROW: Left of left eyebrow.
      RIGHT_OF_LEFT_EYEBROW: Right of left eyebrow.
      LEFT_OF_RIGHT_EYEBROW: Left of right eyebrow.
      RIGHT_OF_RIGHT_EYEBROW: Right of right eyebrow.
      MIDPOINT_BETWEEN_EYES: Midpoint between eyes.
      NOSE_TIP: Nose tip.
      UPPER_LIP: Upper lip.
      LOWER_LIP: Lower lip.
      MOUTH_LEFT: Mouth left.
      MOUTH_RIGHT: Mouth right.
      MOUTH_CENTER: Mouth center.
      NOSE_BOTTOM_RIGHT: Nose, bottom right.
      NOSE_BOTTOM_LEFT: Nose, bottom left.
      NOSE_BOTTOM_CENTER: Nose, bottom center.
      LEFT_EYE_TOP_BOUNDARY: Left eye, top boundary.
      LEFT_EYE_RIGHT_CORNER: Left eye, right corner.
      LEFT_EYE_BOTTOM_BOUNDARY: Left eye, bottom boundary.
      LEFT_EYE_LEFT_CORNER: Left eye, left corner.
      RIGHT_EYE_TOP_BOUNDARY: Right eye, top boundary.
      RIGHT_EYE_RIGHT_CORNER: Right eye, right corner.
      RIGHT_EYE_BOTTOM_BOUNDARY: Right eye, bottom boundary.
      RIGHT_EYE_LEFT_CORNER: Right eye, left corner.
      LEFT_EYEBROW_UPPER_MIDPOINT: Left eyebrow, upper midpoint.
      RIGHT_EYEBROW_UPPER_MIDPOINT: Right eyebrow, upper midpoint.
      LEFT_EAR_TRAGION: Left ear tragion.
      RIGHT_EAR_TRAGION: Right ear tragion.
      LEFT_EYE_PUPIL: Left eye pupil.
      RIGHT_EYE_PUPIL: Right eye pupil.
      FOREHEAD_GLABELLA: Forehead glabella.
      CHIN_GNATHION: Chin gnathion.
      CHIN_LEFT_GONION: Chin left gonion.
      CHIN_RIGHT_GONION: Chin right gonion.
      LEFT_CHEEK_CENTER: Left cheek center.
      RIGHT_CHEEK_CENTER: Right cheek center.
    """
    UNKNOWN_LANDMARK = 0
    LEFT_EYE = 1
    RIGHT_EYE = 2
    LEFT_OF_LEFT_EYEBROW = 3
    RIGHT_OF_LEFT_EYEBROW = 4
    LEFT_OF_RIGHT_EYEBROW = 5
    RIGHT_OF_RIGHT_EYEBROW = 6
    MIDPOINT_BETWEEN_EYES = 7
    NOSE_TIP = 8
    UPPER_LIP = 9
    LOWER_LIP = 10
    MOUTH_LEFT = 11
    MOUTH_RIGHT = 12
    MOUTH_CENTER = 13
    NOSE_BOTTOM_RIGHT = 14
    NOSE_BOTTOM_LEFT = 15
    NOSE_BOTTOM_CENTER = 16
    LEFT_EYE_TOP_BOUNDARY = 17
    LEFT_EYE_RIGHT_CORNER = 18
    LEFT_EYE_BOTTOM_BOUNDARY = 19
    LEFT_EYE_LEFT_CORNER = 20
    RIGHT_EYE_TOP_BOUNDARY = 21
    RIGHT_EYE_RIGHT_CORNER = 22
    RIGHT_EYE_BOTTOM_BOUNDARY = 23
    RIGHT_EYE_LEFT_CORNER = 24
    LEFT_EYEBROW_UPPER_MIDPOINT = 25
    RIGHT_EYEBROW_UPPER_MIDPOINT = 26
    LEFT_EAR_TRAGION = 27
    RIGHT_EAR_TRAGION = 28
    LEFT_EYE_PUPIL = 29
    RIGHT_EYE_PUPIL = 30
    FOREHEAD_GLABELLA = 31
    CHIN_GNATHION = 32
    CHIN_LEFT_GONION = 33
    CHIN_RIGHT_GONION = 34
    LEFT_CHEEK_CENTER = 35
    RIGHT_CHEEK_CENTER = 36

  position = _messages.MessageField('GoogleCloudVisionV1p1beta1Position', 1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class GoogleCloudVisionV1p1beta1GcsDestination(_messages.Message):
  r"""The Google Cloud Storage location where the output will be written to.

  Fields:
    uri: Google Cloud Storage URI prefix where the results will be stored.
      Results will be in JSON format and preceded by its corresponding input
      URI prefix. This field can either represent a gcs file prefix or gcs
      directory. In either case, the uri should be unique because in order to
      get all of the output files, you will need to do a wildcard gcs search
      on the uri prefix you provide. Examples: * File Prefix: gs://bucket-
      name/here/filenameprefix The output files will be created in
      gs://bucket-name/here/ and the names of the output files will begin with
      "filenameprefix". * Directory Prefix: gs://bucket-name/some/location/
      The output files will be created in gs://bucket-name/some/location/ and
      the names of the output files could be anything because there was no
      filename prefix specified. If multiple outputs, each response is still
      AnnotateFileResponse, each of which contains some subset of the full
      list of AnnotateImageResponse. Multiple outputs can happen if, for
      example, the output JSON is too large and overflows into multiple
      sharded files.
  """

  uri = _messages.StringField(1)


class GoogleCloudVisionV1p1beta1GcsSource(_messages.Message):
  r"""The Google Cloud Storage location where the input will be read from.

  Fields:
    uri: Google Cloud Storage URI for the input file. This must only be a
      Google Cloud Storage object. Wildcards are not currently supported.
  """

  uri = _messages.StringField(1)


class GoogleCloudVisionV1p1beta1ImageAnnotationContext(_messages.Message):
  r"""If an image was produced from a file (e.g. a PDF), this message gives
  information about the source of that image.

  Fields:
    pageNumber: If the file was a PDF or TIFF, this field gives the page
      number within the file used to produce the image.
    uri: The URI of the file used to produce the image.
  """

  pageNumber = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  uri = _messages.StringField(2)


class GoogleCloudVisionV1p1beta1ImageProperties(_messages.Message):
  r"""Stores image properties, such as dominant colors.

  Fields:
    dominantColors: If present, dominant colors completed successfully.
  """

  dominantColors = _messages.MessageField('GoogleCloudVisionV1p1beta1DominantColorsAnnotation', 1)


class GoogleCloudVisionV1p1beta1InputConfig(_messages.Message):
  r"""The desired input location and metadata.

  Fields:
    content: File content, represented as a stream of bytes. Note: As with all
      `bytes` fields, protobuffers use a pure binary representation, whereas
      JSON representations use base64. Currently, this field only works for
      BatchAnnotateFiles requests. It does not work for
      AsyncBatchAnnotateFiles requests.
    gcsSource: The Google Cloud Storage location to read the input from.
    mimeType: The type of the file. Currently only "application/pdf",
      "image/tiff" and "image/gif" are supported. Wildcards are not supported.
  """

  content = _messages.BytesField(1)
  gcsSource = _messages.MessageField('GoogleCloudVisionV1p1beta1GcsSource', 2)
  mimeType = _messages.StringField(3)


class GoogleCloudVisionV1p1beta1LocalizedObjectAnnotation(_messages.Message):
  r"""Set of detected objects with bounding boxes.

  Fields:
    boundingPoly: Image region to which this object belongs. This must be
      populated.
    languageCode: The BCP-47 language code, such as "en-US" or "sr-Latn". For
      more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
    mid: Object ID that should align with EntityAnnotation mid.
    name: Object name, expressed in its `language_code` language.
    score: Score of the result. Range [0, 1].
  """

  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p1beta1BoundingPoly', 1)
  languageCode = _messages.StringField(2)
  mid = _messages.StringField(3)
  name = _messages.StringField(4)
  score = _messages.FloatField(5, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p1beta1LocationInfo(_messages.Message):
  r"""Detected entity location information.

  Fields:
    latLng: lat/long location coordinates.
  """

  latLng = _messages.MessageField('LatLng', 1)


class GoogleCloudVisionV1p1beta1NormalizedVertex(_messages.Message):
  r"""A vertex represents a 2D point in the image. NOTE: the normalized vertex
  coordinates are relative to the original image and range from 0 to 1.

  Fields:
    x: X coordinate.
    y: Y coordinate.
  """

  x = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  y = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p1beta1OperationMetadata(_messages.Message):
  r"""Contains metadata for the BatchAnnotateImages operation.

  Enums:
    StateValueValuesEnum: Current state of the batch operation.

  Fields:
    createTime: The time when the batch request was received.
    state: Current state of the batch operation.
    updateTime: The time when the operation result was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Current state of the batch operation.

    Values:
      STATE_UNSPECIFIED: Invalid.
      CREATED: Request is received.
      RUNNING: Request is actively being processed.
      DONE: The batch processing is done.
      CANCELLED: The batch processing was cancelled.
    """
    STATE_UNSPECIFIED = 0
    CREATED = 1
    RUNNING = 2
    DONE = 3
    CANCELLED = 4

  createTime = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  updateTime = _messages.StringField(3)


class GoogleCloudVisionV1p1beta1OutputConfig(_messages.Message):
  r"""The desired output location and metadata.

  Fields:
    batchSize: The max number of response protos to put into each output JSON
      file on Google Cloud Storage. The valid range is [1, 100]. If not
      specified, the default value is 20. For example, for one pdf file with
      100 pages, 100 response protos will be generated. If `batch_size` = 20,
      then 5 json files each containing 20 response protos will be written
      under the prefix `gcs_destination`.`uri`. Currently, batch_size only
      applies to GcsDestination, with potential future support for other
      output configurations.
    gcsDestination: The Google Cloud Storage location to write the output(s)
      to.
  """

  batchSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  gcsDestination = _messages.MessageField('GoogleCloudVisionV1p1beta1GcsDestination', 2)


class GoogleCloudVisionV1p1beta1Page(_messages.Message):
  r"""Detected page from OCR.

  Fields:
    blocks: List of blocks of text, images etc on this page.
    confidence: Confidence of the OCR results on the page. Range [0, 1].
    height: Page height. For PDFs the unit is points. For images (including
      TIFFs) the unit is pixels.
    property: Additional information detected on the page.
    width: Page width. For PDFs the unit is points. For images (including
      TIFFs) the unit is pixels.
  """

  blocks = _messages.MessageField('GoogleCloudVisionV1p1beta1Block', 1, repeated=True)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  height = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  property = _messages.MessageField('GoogleCloudVisionV1p1beta1TextAnnotationTextProperty', 4)
  width = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class GoogleCloudVisionV1p1beta1Paragraph(_messages.Message):
  r"""Structural unit of text representing a number of words in certain order.

  Fields:
    boundingBox: The bounding box for the paragraph. The vertices are in the
      order of top-left, top-right, bottom-right, bottom-left. When a rotation
      of the bounding box is detected the rotation is represented as around
      the top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results for the paragraph. Range [0, 1].
    property: Additional information detected for the paragraph.
    words: List of all words in this paragraph.
  """

  boundingBox = _messages.MessageField('GoogleCloudVisionV1p1beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  property = _messages.MessageField('GoogleCloudVisionV1p1beta1TextAnnotationTextProperty', 3)
  words = _messages.MessageField('GoogleCloudVisionV1p1beta1Word', 4, repeated=True)


class GoogleCloudVisionV1p1beta1Position(_messages.Message):
  r"""A 3D position in the image, used primarily for Face detection landmarks.
  A valid Position must have both x and y coordinates. The position
  coordinates are in the same scale as the original image.

  Fields:
    x: X coordinate.
    y: Y coordinate.
    z: Z coordinate (or depth).
  """

  x = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  y = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  z = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p1beta1Product(_messages.Message):
  r"""A Product contains ReferenceImages.

  Fields:
    description: User-provided metadata to be stored with this product. Must
      be at most 4096 characters long.
    displayName: The user-provided name for this Product. Must not be empty.
      Must be at most 4096 characters long.
    name: The resource name of the product. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`. This field
      is ignored when creating a product.
    productCategory: Immutable. The category for the product identified by the
      reference image. This should be one of "homegoods-v2", "apparel-v2",
      "toys-v2", "packagedgoods-v1" or "general-v1". The legacy categories
      "homegoods", "apparel", and "toys" are still supported, but these should
      not be used for new products.
    productLabels: Key-value pairs that can be attached to a product. At query
      time, constraints can be specified based on the product_labels. Note
      that integer values can be provided as strings, e.g. "1199". Only
      strings with integer values can match a range-based restriction which is
      to be supported soon. Multiple values can be assigned to the same key.
      One product may have up to 500 product_labels. Notice that the total
      number of distinct product_labels over all products in one ProductSet
      cannot exceed 1M, otherwise the product search pipeline will refuse to
      work for that ProductSet.
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  name = _messages.StringField(3)
  productCategory = _messages.StringField(4)
  productLabels = _messages.MessageField('GoogleCloudVisionV1p1beta1ProductKeyValue', 5, repeated=True)


class GoogleCloudVisionV1p1beta1ProductKeyValue(_messages.Message):
  r"""A product label represented as a key-value pair.

  Fields:
    key: The key of the label attached to the product. Cannot be empty and
      cannot exceed 128 bytes.
    value: The value of the label attached to the product. Cannot be empty and
      cannot exceed 128 bytes.
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleCloudVisionV1p1beta1ProductSearchResults(_messages.Message):
  r"""Results for a product search request.

  Fields:
    indexTime: Timestamp of the index which provided these results. Products
      added to the product set and products removed from the product set after
      this time are not reflected in the current results.
    productGroupedResults: List of results grouped by products detected in the
      query image. Each entry corresponds to one bounding polygon in the query
      image, and contains the matching products specific to that region. There
      may be duplicate product matches in the union of all the per-product
      results.
    results: List of results, one for each product match.
  """

  indexTime = _messages.StringField(1)
  productGroupedResults = _messages.MessageField('GoogleCloudVisionV1p1beta1ProductSearchResultsGroupedResult', 2, repeated=True)
  results = _messages.MessageField('GoogleCloudVisionV1p1beta1ProductSearchResultsResult', 3, repeated=True)


class GoogleCloudVisionV1p1beta1ProductSearchResultsGroupedResult(_messages.Message):
  r"""Information about the products similar to a single product in a query
  image.

  Fields:
    boundingPoly: The bounding polygon around the product detected in the
      query image.
    objectAnnotations: List of generic predictions for the object in the
      bounding box.
    results: List of results, one for each product match.
  """

  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p1beta1BoundingPoly', 1)
  objectAnnotations = _messages.MessageField('GoogleCloudVisionV1p1beta1ProductSearchResultsObjectAnnotation', 2, repeated=True)
  results = _messages.MessageField('GoogleCloudVisionV1p1beta1ProductSearchResultsResult', 3, repeated=True)


class GoogleCloudVisionV1p1beta1ProductSearchResultsObjectAnnotation(_messages.Message):
  r"""Prediction for what the object in the bounding box is.

  Fields:
    languageCode: The BCP-47 language code, such as "en-US" or "sr-Latn". For
      more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
    mid: Object ID that should align with EntityAnnotation mid.
    name: Object name, expressed in its `language_code` language.
    score: Score of the result. Range [0, 1].
  """

  languageCode = _messages.StringField(1)
  mid = _messages.StringField(2)
  name = _messages.StringField(3)
  score = _messages.FloatField(4, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p1beta1ProductSearchResultsResult(_messages.Message):
  r"""Information about a product.

  Fields:
    image: The resource name of the image from the product that is the closest
      match to the query.
    product: The Product.
    score: A confidence level on the match, ranging from 0 (no confidence) to
      1 (full confidence).
  """

  image = _messages.StringField(1)
  product = _messages.MessageField('GoogleCloudVisionV1p1beta1Product', 2)
  score = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p1beta1Property(_messages.Message):
  r"""A `Property` consists of a user-supplied name/value pair.

  Fields:
    name: Name of the property.
    uint64Value: Value of numeric properties.
    value: Value of the property.
  """

  name = _messages.StringField(1)
  uint64Value = _messages.IntegerField(2, variant=_messages.Variant.UINT64)
  value = _messages.StringField(3)


class GoogleCloudVisionV1p1beta1SafeSearchAnnotation(_messages.Message):
  r"""Set of features pertaining to the image, computed by computer vision
  methods over safe-search verticals (for example, adult, spoof, medical,
  violence).

  Enums:
    AdultValueValuesEnum: Represents the adult content likelihood for the
      image. Adult content may contain elements such as nudity, pornographic
      images or cartoons, or sexual activities.
    MedicalValueValuesEnum: Likelihood that this is a medical image.
    RacyValueValuesEnum: Likelihood that the request image contains racy
      content. Racy content may include (but is not limited to) skimpy or
      sheer clothing, strategically covered nudity, lewd or provocative poses,
      or close-ups of sensitive body areas.
    SpoofValueValuesEnum: Spoof likelihood. The likelihood that an
      modification was made to the image's canonical version to make it appear
      funny or offensive.
    ViolenceValueValuesEnum: Likelihood that this image contains violent
      content.

  Fields:
    adult: Represents the adult content likelihood for the image. Adult
      content may contain elements such as nudity, pornographic images or
      cartoons, or sexual activities.
    medical: Likelihood that this is a medical image.
    racy: Likelihood that the request image contains racy content. Racy
      content may include (but is not limited to) skimpy or sheer clothing,
      strategically covered nudity, lewd or provocative poses, or close-ups of
      sensitive body areas.
    spoof: Spoof likelihood. The likelihood that an modification was made to
      the image's canonical version to make it appear funny or offensive.
    violence: Likelihood that this image contains violent content.
  """

  class AdultValueValuesEnum(_messages.Enum):
    r"""Represents the adult content likelihood for the image. Adult content
    may contain elements such as nudity, pornographic images or cartoons, or
    sexual activities.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class MedicalValueValuesEnum(_messages.Enum):
    r"""Likelihood that this is a medical image.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class RacyValueValuesEnum(_messages.Enum):
    r"""Likelihood that the request image contains racy content. Racy content
    may include (but is not limited to) skimpy or sheer clothing,
    strategically covered nudity, lewd or provocative poses, or close-ups of
    sensitive body areas.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class SpoofValueValuesEnum(_messages.Enum):
    r"""Spoof likelihood. The likelihood that an modification was made to the
    image's canonical version to make it appear funny or offensive.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class ViolenceValueValuesEnum(_messages.Enum):
    r"""Likelihood that this image contains violent content.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  adult = _messages.EnumField('AdultValueValuesEnum', 1)
  medical = _messages.EnumField('MedicalValueValuesEnum', 2)
  racy = _messages.EnumField('RacyValueValuesEnum', 3)
  spoof = _messages.EnumField('SpoofValueValuesEnum', 4)
  violence = _messages.EnumField('ViolenceValueValuesEnum', 5)


class GoogleCloudVisionV1p1beta1Symbol(_messages.Message):
  r"""A single symbol representation.

  Fields:
    boundingBox: The bounding box for the symbol. The vertices are in the
      order of top-left, top-right, bottom-right, bottom-left. When a rotation
      of the bounding box is detected the rotation is represented as around
      the top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results for the symbol. Range [0, 1].
    property: Additional information detected for the symbol.
    text: The actual UTF-8 representation of the symbol.
  """

  boundingBox = _messages.MessageField('GoogleCloudVisionV1p1beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  property = _messages.MessageField('GoogleCloudVisionV1p1beta1TextAnnotationTextProperty', 3)
  text = _messages.StringField(4)


class GoogleCloudVisionV1p1beta1TextAnnotation(_messages.Message):
  r"""TextAnnotation contains a structured representation of OCR extracted
  text. The hierarchy of an OCR extracted text structure is like this:
  TextAnnotation -> Page -> Block -> Paragraph -> Word -> Symbol Each
  structural component, starting from Page, may further have their own
  properties. Properties describe detected languages, breaks etc.. Please
  refer to the TextAnnotation.TextProperty message definition below for more
  detail.

  Fields:
    pages: List of pages detected by OCR.
    text: UTF-8 text detected on the pages.
  """

  pages = _messages.MessageField('GoogleCloudVisionV1p1beta1Page', 1, repeated=True)
  text = _messages.StringField(2)


class GoogleCloudVisionV1p1beta1TextAnnotationDetectedBreak(_messages.Message):
  r"""Detected start or end of a structural component.

  Enums:
    TypeValueValuesEnum: Detected break type.

  Fields:
    isPrefix: True if break prepends the element.
    type: Detected break type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Detected break type.

    Values:
      UNKNOWN: Unknown break label type.
      SPACE: Regular space.
      SURE_SPACE: Sure space (very wide).
      EOL_SURE_SPACE: Line-wrapping break.
      HYPHEN: End-line hyphen that is not present in text; does not co-occur
        with `SPACE`, `LEADER_SPACE`, or `LINE_BREAK`.
      LINE_BREAK: Line break that ends a paragraph.
    """
    UNKNOWN = 0
    SPACE = 1
    SURE_SPACE = 2
    EOL_SURE_SPACE = 3
    HYPHEN = 4
    LINE_BREAK = 5

  isPrefix = _messages.BooleanField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class GoogleCloudVisionV1p1beta1TextAnnotationDetectedLanguage(_messages.Message):
  r"""Detected language for a structural component.

  Fields:
    confidence: Confidence of detected language. Range [0, 1].
    languageCode: The BCP-47 language code, such as "en-US" or "sr-Latn". For
      more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
  """

  confidence = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  languageCode = _messages.StringField(2)


class GoogleCloudVisionV1p1beta1TextAnnotationTextProperty(_messages.Message):
  r"""Additional information detected on the structural component.

  Fields:
    detectedBreak: Detected start or end of a text segment.
    detectedLanguages: A list of detected languages together with confidence.
  """

  detectedBreak = _messages.MessageField('GoogleCloudVisionV1p1beta1TextAnnotationDetectedBreak', 1)
  detectedLanguages = _messages.MessageField('GoogleCloudVisionV1p1beta1TextAnnotationDetectedLanguage', 2, repeated=True)


class GoogleCloudVisionV1p1beta1Vertex(_messages.Message):
  r"""A vertex represents a 2D point in the image. NOTE: the vertex
  coordinates are in the same scale as the original image.

  Fields:
    x: X coordinate.
    y: Y coordinate.
  """

  x = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  y = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudVisionV1p1beta1WebDetection(_messages.Message):
  r"""Relevant information for the image from the Internet.

  Fields:
    bestGuessLabels: The service's best guess as to the topic of the request
      image. Inferred from similar images on the open web.
    fullMatchingImages: Fully matching images from the Internet. Can include
      resized copies of the query image.
    pagesWithMatchingImages: Web pages containing the matching images from the
      Internet.
    partialMatchingImages: Partial matching images from the Internet. Those
      images are similar enough to share some key-point features. For example
      an original image will likely have partial matching for its crops.
    visuallySimilarImages: The visually similar image results.
    webEntities: Deduced entities from similar images on the Internet.
  """

  bestGuessLabels = _messages.MessageField('GoogleCloudVisionV1p1beta1WebDetectionWebLabel', 1, repeated=True)
  fullMatchingImages = _messages.MessageField('GoogleCloudVisionV1p1beta1WebDetectionWebImage', 2, repeated=True)
  pagesWithMatchingImages = _messages.MessageField('GoogleCloudVisionV1p1beta1WebDetectionWebPage', 3, repeated=True)
  partialMatchingImages = _messages.MessageField('GoogleCloudVisionV1p1beta1WebDetectionWebImage', 4, repeated=True)
  visuallySimilarImages = _messages.MessageField('GoogleCloudVisionV1p1beta1WebDetectionWebImage', 5, repeated=True)
  webEntities = _messages.MessageField('GoogleCloudVisionV1p1beta1WebDetectionWebEntity', 6, repeated=True)


class GoogleCloudVisionV1p1beta1WebDetectionWebEntity(_messages.Message):
  r"""Entity deduced from similar images on the Internet.

  Fields:
    description: Canonical description of the entity, in English.
    entityId: Opaque entity ID.
    score: Overall relevancy score for the entity. Not normalized and not
      comparable across different image queries.
  """

  description = _messages.StringField(1)
  entityId = _messages.StringField(2)
  score = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p1beta1WebDetectionWebImage(_messages.Message):
  r"""Metadata for online images.

  Fields:
    score: (Deprecated) Overall relevancy score for the image.
    url: The result image URL.
  """

  score = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  url = _messages.StringField(2)


class GoogleCloudVisionV1p1beta1WebDetectionWebLabel(_messages.Message):
  r"""Label to provide extra metadata for the web detection.

  Fields:
    label: Label for extra metadata.
    languageCode: The BCP-47 language code for `label`, such as "en-US" or
      "sr-Latn". For more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
  """

  label = _messages.StringField(1)
  languageCode = _messages.StringField(2)


class GoogleCloudVisionV1p1beta1WebDetectionWebPage(_messages.Message):
  r"""Metadata for web pages.

  Fields:
    fullMatchingImages: Fully matching images on the page. Can include resized
      copies of the query image.
    pageTitle: Title for the web page, may contain HTML markups.
    partialMatchingImages: Partial matching images on the page. Those images
      are similar enough to share some key-point features. For example an
      original image will likely have partial matching for its crops.
    score: (Deprecated) Overall relevancy score for the web page.
    url: The result web page URL.
  """

  fullMatchingImages = _messages.MessageField('GoogleCloudVisionV1p1beta1WebDetectionWebImage', 1, repeated=True)
  pageTitle = _messages.StringField(2)
  partialMatchingImages = _messages.MessageField('GoogleCloudVisionV1p1beta1WebDetectionWebImage', 3, repeated=True)
  score = _messages.FloatField(4, variant=_messages.Variant.FLOAT)
  url = _messages.StringField(5)


class GoogleCloudVisionV1p1beta1Word(_messages.Message):
  r"""A word representation.

  Fields:
    boundingBox: The bounding box for the word. The vertices are in the order
      of top-left, top-right, bottom-right, bottom-left. When a rotation of
      the bounding box is detected the rotation is represented as around the
      top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results for the word. Range [0, 1].
    property: Additional information detected for the word.
    symbols: List of symbols in the word. The order of the symbols follows the
      natural reading order.
  """

  boundingBox = _messages.MessageField('GoogleCloudVisionV1p1beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  property = _messages.MessageField('GoogleCloudVisionV1p1beta1TextAnnotationTextProperty', 3)
  symbols = _messages.MessageField('GoogleCloudVisionV1p1beta1Symbol', 4, repeated=True)


class GoogleCloudVisionV1p2beta1AnnotateFileResponse(_messages.Message):
  r"""Response to a single file annotation request. A file may contain one or
  more images, which individually have their own responses.

  Fields:
    error: If set, represents the error message for the failed request. The
      `responses` field will not be set in this case.
    inputConfig: Information about the file for which this response is
      generated.
    responses: Individual responses to images found within the file. This
      field will be empty if the `error` field is set.
    totalPages: This field gives the total number of pages in the file.
  """

  error = _messages.MessageField('Status', 1)
  inputConfig = _messages.MessageField('GoogleCloudVisionV1p2beta1InputConfig', 2)
  responses = _messages.MessageField('GoogleCloudVisionV1p2beta1AnnotateImageResponse', 3, repeated=True)
  totalPages = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudVisionV1p2beta1AnnotateImageResponse(_messages.Message):
  r"""Response to an image annotation request.

  Fields:
    context: If present, contextual information is needed to understand where
      this image comes from.
    cropHintsAnnotation: If present, crop hints have completed successfully.
    error: If set, represents the error message for the operation. Note that
      filled-in image annotations are guaranteed to be correct, even when
      `error` is set.
    faceAnnotations: If present, face detection has completed successfully.
    fullTextAnnotation: If present, text (OCR) detection or document (OCR)
      text detection has completed successfully. This annotation provides the
      structural hierarchy for the OCR detected text.
    imagePropertiesAnnotation: If present, image properties were extracted
      successfully.
    labelAnnotations: If present, label detection has completed successfully.
    landmarkAnnotations: If present, landmark detection has completed
      successfully.
    localizedObjectAnnotations: If present, localized object detection has
      completed successfully. This will be sorted descending by confidence
      score.
    logoAnnotations: If present, logo detection has completed successfully.
    productSearchResults: If present, product search has completed
      successfully.
    safeSearchAnnotation: If present, safe-search annotation has completed
      successfully.
    textAnnotations: If present, text (OCR) detection has completed
      successfully.
    webDetection: If present, web detection has completed successfully.
  """

  context = _messages.MessageField('GoogleCloudVisionV1p2beta1ImageAnnotationContext', 1)
  cropHintsAnnotation = _messages.MessageField('GoogleCloudVisionV1p2beta1CropHintsAnnotation', 2)
  error = _messages.MessageField('Status', 3)
  faceAnnotations = _messages.MessageField('GoogleCloudVisionV1p2beta1FaceAnnotation', 4, repeated=True)
  fullTextAnnotation = _messages.MessageField('GoogleCloudVisionV1p2beta1TextAnnotation', 5)
  imagePropertiesAnnotation = _messages.MessageField('GoogleCloudVisionV1p2beta1ImageProperties', 6)
  labelAnnotations = _messages.MessageField('GoogleCloudVisionV1p2beta1EntityAnnotation', 7, repeated=True)
  landmarkAnnotations = _messages.MessageField('GoogleCloudVisionV1p2beta1EntityAnnotation', 8, repeated=True)
  localizedObjectAnnotations = _messages.MessageField('GoogleCloudVisionV1p2beta1LocalizedObjectAnnotation', 9, repeated=True)
  logoAnnotations = _messages.MessageField('GoogleCloudVisionV1p2beta1EntityAnnotation', 10, repeated=True)
  productSearchResults = _messages.MessageField('GoogleCloudVisionV1p2beta1ProductSearchResults', 11)
  safeSearchAnnotation = _messages.MessageField('GoogleCloudVisionV1p2beta1SafeSearchAnnotation', 12)
  textAnnotations = _messages.MessageField('GoogleCloudVisionV1p2beta1EntityAnnotation', 13, repeated=True)
  webDetection = _messages.MessageField('GoogleCloudVisionV1p2beta1WebDetection', 14)


class GoogleCloudVisionV1p2beta1AsyncAnnotateFileResponse(_messages.Message):
  r"""The response for a single offline file annotation request.

  Fields:
    outputConfig: The output location and metadata from
      AsyncAnnotateFileRequest.
  """

  outputConfig = _messages.MessageField('GoogleCloudVisionV1p2beta1OutputConfig', 1)


class GoogleCloudVisionV1p2beta1AsyncBatchAnnotateFilesResponse(_messages.Message):
  r"""Response to an async batch file annotation request.

  Fields:
    responses: The list of file annotation responses, one for each request in
      AsyncBatchAnnotateFilesRequest.
  """

  responses = _messages.MessageField('GoogleCloudVisionV1p2beta1AsyncAnnotateFileResponse', 1, repeated=True)


class GoogleCloudVisionV1p2beta1Block(_messages.Message):
  r"""Logical element on the page.

  Enums:
    BlockTypeValueValuesEnum: Detected block type (text, image etc) for this
      block.

  Fields:
    blockType: Detected block type (text, image etc) for this block.
    boundingBox: The bounding box for the block. The vertices are in the order
      of top-left, top-right, bottom-right, bottom-left. When a rotation of
      the bounding box is detected the rotation is represented as around the
      top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results on the block. Range [0, 1].
    paragraphs: List of paragraphs in this block (if this blocks is of type
      text).
    property: Additional information detected for the block.
  """

  class BlockTypeValueValuesEnum(_messages.Enum):
    r"""Detected block type (text, image etc) for this block.

    Values:
      UNKNOWN: Unknown block type.
      TEXT: Regular text block.
      TABLE: Table block.
      PICTURE: Image block.
      RULER: Horizontal/vertical line box.
      BARCODE: Barcode block.
    """
    UNKNOWN = 0
    TEXT = 1
    TABLE = 2
    PICTURE = 3
    RULER = 4
    BARCODE = 5

  blockType = _messages.EnumField('BlockTypeValueValuesEnum', 1)
  boundingBox = _messages.MessageField('GoogleCloudVisionV1p2beta1BoundingPoly', 2)
  confidence = _messages.FloatField(3, variant=_messages.Variant.FLOAT)
  paragraphs = _messages.MessageField('GoogleCloudVisionV1p2beta1Paragraph', 4, repeated=True)
  property = _messages.MessageField('GoogleCloudVisionV1p2beta1TextAnnotationTextProperty', 5)


class GoogleCloudVisionV1p2beta1BoundingPoly(_messages.Message):
  r"""A bounding polygon for the detected image annotation.

  Fields:
    normalizedVertices: The bounding polygon normalized vertices.
    vertices: The bounding polygon vertices.
  """

  normalizedVertices = _messages.MessageField('GoogleCloudVisionV1p2beta1NormalizedVertex', 1, repeated=True)
  vertices = _messages.MessageField('GoogleCloudVisionV1p2beta1Vertex', 2, repeated=True)


class GoogleCloudVisionV1p2beta1ColorInfo(_messages.Message):
  r"""Color information consists of RGB channels, score, and the fraction of
  the image that the color occupies in the image.

  Fields:
    color: RGB components of the color.
    pixelFraction: The fraction of pixels the color occupies in the image.
      Value in range [0, 1].
    score: Image-specific score for this color. Value in range [0, 1].
  """

  color = _messages.MessageField('Color', 1)
  pixelFraction = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  score = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p2beta1CropHint(_messages.Message):
  r"""Single crop hint that is used to generate a new crop when serving an
  image.

  Fields:
    boundingPoly: The bounding polygon for the crop region. The coordinates of
      the bounding box are in the original image's scale.
    confidence: Confidence of this being a salient region. Range [0, 1].
    importanceFraction: Fraction of importance of this salient region with
      respect to the original image.
  """

  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p2beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  importanceFraction = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p2beta1CropHintsAnnotation(_messages.Message):
  r"""Set of crop hints that are used to generate new crops when serving
  images.

  Fields:
    cropHints: Crop hint results.
  """

  cropHints = _messages.MessageField('GoogleCloudVisionV1p2beta1CropHint', 1, repeated=True)


class GoogleCloudVisionV1p2beta1DominantColorsAnnotation(_messages.Message):
  r"""Set of dominant colors and their corresponding scores.

  Fields:
    colors: RGB color values with their score and pixel fraction.
  """

  colors = _messages.MessageField('GoogleCloudVisionV1p2beta1ColorInfo', 1, repeated=True)


class GoogleCloudVisionV1p2beta1EntityAnnotation(_messages.Message):
  r"""Set of detected entity features.

  Fields:
    boundingPoly: Image region to which this entity belongs. Not produced for
      `LABEL_DETECTION` features.
    confidence: **Deprecated. Use `score` instead.** The accuracy of the
      entity detection in an image. For example, for an image in which the
      "Eiffel Tower" entity is detected, this field represents the confidence
      that there is a tower in the query image. Range [0, 1].
    description: Entity textual description, expressed in its `locale`
      language.
    locale: The language code for the locale in which the entity textual
      `description` is expressed.
    locations: The location information for the detected entity. Multiple
      `LocationInfo` elements can be present because one location may indicate
      the location of the scene in the image, and another location may
      indicate the location of the place where the image was taken. Location
      information is usually present for landmarks.
    mid: Opaque entity ID. Some IDs may be available in [Google Knowledge
      Graph Search API](https://developers.google.com/knowledge-graph/).
    properties: Some entities may have optional user-supplied `Property`
      (name/value) fields, such a score or string that qualifies the entity.
    score: Overall score of the result. Range [0, 1].
    topicality: The relevancy of the ICA (Image Content Annotation) label to
      the image. For example, the relevancy of "tower" is likely higher to an
      image containing the detected "Eiffel Tower" than to an image containing
      a detected distant towering building, even though the confidence that
      there is a tower in each image may be the same. Range [0, 1].
  """

  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p2beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  description = _messages.StringField(3)
  locale = _messages.StringField(4)
  locations = _messages.MessageField('GoogleCloudVisionV1p2beta1LocationInfo', 5, repeated=True)
  mid = _messages.StringField(6)
  properties = _messages.MessageField('GoogleCloudVisionV1p2beta1Property', 7, repeated=True)
  score = _messages.FloatField(8, variant=_messages.Variant.FLOAT)
  topicality = _messages.FloatField(9, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p2beta1FaceAnnotation(_messages.Message):
  r"""A face annotation object contains the results of face detection.

  Enums:
    AngerLikelihoodValueValuesEnum: Anger likelihood.
    BlurredLikelihoodValueValuesEnum: Blurred likelihood.
    HeadwearLikelihoodValueValuesEnum: Headwear likelihood.
    JoyLikelihoodValueValuesEnum: Joy likelihood.
    SorrowLikelihoodValueValuesEnum: Sorrow likelihood.
    SurpriseLikelihoodValueValuesEnum: Surprise likelihood.
    UnderExposedLikelihoodValueValuesEnum: Under-exposed likelihood.

  Fields:
    angerLikelihood: Anger likelihood.
    blurredLikelihood: Blurred likelihood.
    boundingPoly: The bounding polygon around the face. The coordinates of the
      bounding box are in the original image's scale. The bounding box is
      computed to "frame" the face in accordance with human expectations. It
      is based on the landmarker results. Note that one or more x and/or y
      coordinates may not be generated in the `BoundingPoly` (the polygon will
      be unbounded) if only a partial face appears in the image to be
      annotated.
    detectionConfidence: Detection confidence. Range [0, 1].
    fdBoundingPoly: The `fd_bounding_poly` bounding polygon is tighter than
      the `boundingPoly`, and encloses only the skin part of the face.
      Typically, it is used to eliminate the face from any image analysis that
      detects the "amount of skin" visible in an image. It is not based on the
      landmarker results, only on the initial face detection, hence the fd
      (face detection) prefix.
    headwearLikelihood: Headwear likelihood.
    joyLikelihood: Joy likelihood.
    landmarkingConfidence: Face landmarking confidence. Range [0, 1].
    landmarks: Detected face landmarks.
    panAngle: Yaw angle, which indicates the leftward/rightward angle that the
      face is pointing relative to the vertical plane perpendicular to the
      image. Range [-180,180].
    rollAngle: Roll angle, which indicates the amount of clockwise/anti-
      clockwise rotation of the face relative to the image vertical about the
      axis perpendicular to the face. Range [-180,180].
    sorrowLikelihood: Sorrow likelihood.
    surpriseLikelihood: Surprise likelihood.
    tiltAngle: Pitch angle, which indicates the upwards/downwards angle that
      the face is pointing relative to the image's horizontal plane. Range
      [-180,180].
    underExposedLikelihood: Under-exposed likelihood.
  """

  class AngerLikelihoodValueValuesEnum(_messages.Enum):
    r"""Anger likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class BlurredLikelihoodValueValuesEnum(_messages.Enum):
    r"""Blurred likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class HeadwearLikelihoodValueValuesEnum(_messages.Enum):
    r"""Headwear likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class JoyLikelihoodValueValuesEnum(_messages.Enum):
    r"""Joy likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class SorrowLikelihoodValueValuesEnum(_messages.Enum):
    r"""Sorrow likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class SurpriseLikelihoodValueValuesEnum(_messages.Enum):
    r"""Surprise likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class UnderExposedLikelihoodValueValuesEnum(_messages.Enum):
    r"""Under-exposed likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  angerLikelihood = _messages.EnumField('AngerLikelihoodValueValuesEnum', 1)
  blurredLikelihood = _messages.EnumField('BlurredLikelihoodValueValuesEnum', 2)
  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p2beta1BoundingPoly', 3)
  detectionConfidence = _messages.FloatField(4, variant=_messages.Variant.FLOAT)
  fdBoundingPoly = _messages.MessageField('GoogleCloudVisionV1p2beta1BoundingPoly', 5)
  headwearLikelihood = _messages.EnumField('HeadwearLikelihoodValueValuesEnum', 6)
  joyLikelihood = _messages.EnumField('JoyLikelihoodValueValuesEnum', 7)
  landmarkingConfidence = _messages.FloatField(8, variant=_messages.Variant.FLOAT)
  landmarks = _messages.MessageField('GoogleCloudVisionV1p2beta1FaceAnnotationLandmark', 9, repeated=True)
  panAngle = _messages.FloatField(10, variant=_messages.Variant.FLOAT)
  rollAngle = _messages.FloatField(11, variant=_messages.Variant.FLOAT)
  sorrowLikelihood = _messages.EnumField('SorrowLikelihoodValueValuesEnum', 12)
  surpriseLikelihood = _messages.EnumField('SurpriseLikelihoodValueValuesEnum', 13)
  tiltAngle = _messages.FloatField(14, variant=_messages.Variant.FLOAT)
  underExposedLikelihood = _messages.EnumField('UnderExposedLikelihoodValueValuesEnum', 15)


class GoogleCloudVisionV1p2beta1FaceAnnotationLandmark(_messages.Message):
  r"""A face-specific landmark (for example, a face feature).

  Enums:
    TypeValueValuesEnum: Face landmark type.

  Fields:
    position: Face landmark position.
    type: Face landmark type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Face landmark type.

    Values:
      UNKNOWN_LANDMARK: Unknown face landmark detected. Should not be filled.
      LEFT_EYE: Left eye.
      RIGHT_EYE: Right eye.
      LEFT_OF_LEFT_EYEBROW: Left of left eyebrow.
      RIGHT_OF_LEFT_EYEBROW: Right of left eyebrow.
      LEFT_OF_RIGHT_EYEBROW: Left of right eyebrow.
      RIGHT_OF_RIGHT_EYEBROW: Right of right eyebrow.
      MIDPOINT_BETWEEN_EYES: Midpoint between eyes.
      NOSE_TIP: Nose tip.
      UPPER_LIP: Upper lip.
      LOWER_LIP: Lower lip.
      MOUTH_LEFT: Mouth left.
      MOUTH_RIGHT: Mouth right.
      MOUTH_CENTER: Mouth center.
      NOSE_BOTTOM_RIGHT: Nose, bottom right.
      NOSE_BOTTOM_LEFT: Nose, bottom left.
      NOSE_BOTTOM_CENTER: Nose, bottom center.
      LEFT_EYE_TOP_BOUNDARY: Left eye, top boundary.
      LEFT_EYE_RIGHT_CORNER: Left eye, right corner.
      LEFT_EYE_BOTTOM_BOUNDARY: Left eye, bottom boundary.
      LEFT_EYE_LEFT_CORNER: Left eye, left corner.
      RIGHT_EYE_TOP_BOUNDARY: Right eye, top boundary.
      RIGHT_EYE_RIGHT_CORNER: Right eye, right corner.
      RIGHT_EYE_BOTTOM_BOUNDARY: Right eye, bottom boundary.
      RIGHT_EYE_LEFT_CORNER: Right eye, left corner.
      LEFT_EYEBROW_UPPER_MIDPOINT: Left eyebrow, upper midpoint.
      RIGHT_EYEBROW_UPPER_MIDPOINT: Right eyebrow, upper midpoint.
      LEFT_EAR_TRAGION: Left ear tragion.
      RIGHT_EAR_TRAGION: Right ear tragion.
      LEFT_EYE_PUPIL: Left eye pupil.
      RIGHT_EYE_PUPIL: Right eye pupil.
      FOREHEAD_GLABELLA: Forehead glabella.
      CHIN_GNATHION: Chin gnathion.
      CHIN_LEFT_GONION: Chin left gonion.
      CHIN_RIGHT_GONION: Chin right gonion.
      LEFT_CHEEK_CENTER: Left cheek center.
      RIGHT_CHEEK_CENTER: Right cheek center.
    """
    UNKNOWN_LANDMARK = 0
    LEFT_EYE = 1
    RIGHT_EYE = 2
    LEFT_OF_LEFT_EYEBROW = 3
    RIGHT_OF_LEFT_EYEBROW = 4
    LEFT_OF_RIGHT_EYEBROW = 5
    RIGHT_OF_RIGHT_EYEBROW = 6
    MIDPOINT_BETWEEN_EYES = 7
    NOSE_TIP = 8
    UPPER_LIP = 9
    LOWER_LIP = 10
    MOUTH_LEFT = 11
    MOUTH_RIGHT = 12
    MOUTH_CENTER = 13
    NOSE_BOTTOM_RIGHT = 14
    NOSE_BOTTOM_LEFT = 15
    NOSE_BOTTOM_CENTER = 16
    LEFT_EYE_TOP_BOUNDARY = 17
    LEFT_EYE_RIGHT_CORNER = 18
    LEFT_EYE_BOTTOM_BOUNDARY = 19
    LEFT_EYE_LEFT_CORNER = 20
    RIGHT_EYE_TOP_BOUNDARY = 21
    RIGHT_EYE_RIGHT_CORNER = 22
    RIGHT_EYE_BOTTOM_BOUNDARY = 23
    RIGHT_EYE_LEFT_CORNER = 24
    LEFT_EYEBROW_UPPER_MIDPOINT = 25
    RIGHT_EYEBROW_UPPER_MIDPOINT = 26
    LEFT_EAR_TRAGION = 27
    RIGHT_EAR_TRAGION = 28
    LEFT_EYE_PUPIL = 29
    RIGHT_EYE_PUPIL = 30
    FOREHEAD_GLABELLA = 31
    CHIN_GNATHION = 32
    CHIN_LEFT_GONION = 33
    CHIN_RIGHT_GONION = 34
    LEFT_CHEEK_CENTER = 35
    RIGHT_CHEEK_CENTER = 36

  position = _messages.MessageField('GoogleCloudVisionV1p2beta1Position', 1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class GoogleCloudVisionV1p2beta1GcsDestination(_messages.Message):
  r"""The Google Cloud Storage location where the output will be written to.

  Fields:
    uri: Google Cloud Storage URI prefix where the results will be stored.
      Results will be in JSON format and preceded by its corresponding input
      URI prefix. This field can either represent a gcs file prefix or gcs
      directory. In either case, the uri should be unique because in order to
      get all of the output files, you will need to do a wildcard gcs search
      on the uri prefix you provide. Examples: * File Prefix: gs://bucket-
      name/here/filenameprefix The output files will be created in
      gs://bucket-name/here/ and the names of the output files will begin with
      "filenameprefix". * Directory Prefix: gs://bucket-name/some/location/
      The output files will be created in gs://bucket-name/some/location/ and
      the names of the output files could be anything because there was no
      filename prefix specified. If multiple outputs, each response is still
      AnnotateFileResponse, each of which contains some subset of the full
      list of AnnotateImageResponse. Multiple outputs can happen if, for
      example, the output JSON is too large and overflows into multiple
      sharded files.
  """

  uri = _messages.StringField(1)


class GoogleCloudVisionV1p2beta1GcsSource(_messages.Message):
  r"""The Google Cloud Storage location where the input will be read from.

  Fields:
    uri: Google Cloud Storage URI for the input file. This must only be a
      Google Cloud Storage object. Wildcards are not currently supported.
  """

  uri = _messages.StringField(1)


class GoogleCloudVisionV1p2beta1ImageAnnotationContext(_messages.Message):
  r"""If an image was produced from a file (e.g. a PDF), this message gives
  information about the source of that image.

  Fields:
    pageNumber: If the file was a PDF or TIFF, this field gives the page
      number within the file used to produce the image.
    uri: The URI of the file used to produce the image.
  """

  pageNumber = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  uri = _messages.StringField(2)


class GoogleCloudVisionV1p2beta1ImageProperties(_messages.Message):
  r"""Stores image properties, such as dominant colors.

  Fields:
    dominantColors: If present, dominant colors completed successfully.
  """

  dominantColors = _messages.MessageField('GoogleCloudVisionV1p2beta1DominantColorsAnnotation', 1)


class GoogleCloudVisionV1p2beta1InputConfig(_messages.Message):
  r"""The desired input location and metadata.

  Fields:
    content: File content, represented as a stream of bytes. Note: As with all
      `bytes` fields, protobuffers use a pure binary representation, whereas
      JSON representations use base64. Currently, this field only works for
      BatchAnnotateFiles requests. It does not work for
      AsyncBatchAnnotateFiles requests.
    gcsSource: The Google Cloud Storage location to read the input from.
    mimeType: The type of the file. Currently only "application/pdf",
      "image/tiff" and "image/gif" are supported. Wildcards are not supported.
  """

  content = _messages.BytesField(1)
  gcsSource = _messages.MessageField('GoogleCloudVisionV1p2beta1GcsSource', 2)
  mimeType = _messages.StringField(3)


class GoogleCloudVisionV1p2beta1LocalizedObjectAnnotation(_messages.Message):
  r"""Set of detected objects with bounding boxes.

  Fields:
    boundingPoly: Image region to which this object belongs. This must be
      populated.
    languageCode: The BCP-47 language code, such as "en-US" or "sr-Latn". For
      more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
    mid: Object ID that should align with EntityAnnotation mid.
    name: Object name, expressed in its `language_code` language.
    score: Score of the result. Range [0, 1].
  """

  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p2beta1BoundingPoly', 1)
  languageCode = _messages.StringField(2)
  mid = _messages.StringField(3)
  name = _messages.StringField(4)
  score = _messages.FloatField(5, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p2beta1LocationInfo(_messages.Message):
  r"""Detected entity location information.

  Fields:
    latLng: lat/long location coordinates.
  """

  latLng = _messages.MessageField('LatLng', 1)


class GoogleCloudVisionV1p2beta1NormalizedVertex(_messages.Message):
  r"""A vertex represents a 2D point in the image. NOTE: the normalized vertex
  coordinates are relative to the original image and range from 0 to 1.

  Fields:
    x: X coordinate.
    y: Y coordinate.
  """

  x = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  y = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p2beta1OperationMetadata(_messages.Message):
  r"""Contains metadata for the BatchAnnotateImages operation.

  Enums:
    StateValueValuesEnum: Current state of the batch operation.

  Fields:
    createTime: The time when the batch request was received.
    state: Current state of the batch operation.
    updateTime: The time when the operation result was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Current state of the batch operation.

    Values:
      STATE_UNSPECIFIED: Invalid.
      CREATED: Request is received.
      RUNNING: Request is actively being processed.
      DONE: The batch processing is done.
      CANCELLED: The batch processing was cancelled.
    """
    STATE_UNSPECIFIED = 0
    CREATED = 1
    RUNNING = 2
    DONE = 3
    CANCELLED = 4

  createTime = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  updateTime = _messages.StringField(3)


class GoogleCloudVisionV1p2beta1OutputConfig(_messages.Message):
  r"""The desired output location and metadata.

  Fields:
    batchSize: The max number of response protos to put into each output JSON
      file on Google Cloud Storage. The valid range is [1, 100]. If not
      specified, the default value is 20. For example, for one pdf file with
      100 pages, 100 response protos will be generated. If `batch_size` = 20,
      then 5 json files each containing 20 response protos will be written
      under the prefix `gcs_destination`.`uri`. Currently, batch_size only
      applies to GcsDestination, with potential future support for other
      output configurations.
    gcsDestination: The Google Cloud Storage location to write the output(s)
      to.
  """

  batchSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  gcsDestination = _messages.MessageField('GoogleCloudVisionV1p2beta1GcsDestination', 2)


class GoogleCloudVisionV1p2beta1Page(_messages.Message):
  r"""Detected page from OCR.

  Fields:
    blocks: List of blocks of text, images etc on this page.
    confidence: Confidence of the OCR results on the page. Range [0, 1].
    height: Page height. For PDFs the unit is points. For images (including
      TIFFs) the unit is pixels.
    property: Additional information detected on the page.
    width: Page width. For PDFs the unit is points. For images (including
      TIFFs) the unit is pixels.
  """

  blocks = _messages.MessageField('GoogleCloudVisionV1p2beta1Block', 1, repeated=True)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  height = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  property = _messages.MessageField('GoogleCloudVisionV1p2beta1TextAnnotationTextProperty', 4)
  width = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class GoogleCloudVisionV1p2beta1Paragraph(_messages.Message):
  r"""Structural unit of text representing a number of words in certain order.

  Fields:
    boundingBox: The bounding box for the paragraph. The vertices are in the
      order of top-left, top-right, bottom-right, bottom-left. When a rotation
      of the bounding box is detected the rotation is represented as around
      the top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results for the paragraph. Range [0, 1].
    property: Additional information detected for the paragraph.
    words: List of all words in this paragraph.
  """

  boundingBox = _messages.MessageField('GoogleCloudVisionV1p2beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  property = _messages.MessageField('GoogleCloudVisionV1p2beta1TextAnnotationTextProperty', 3)
  words = _messages.MessageField('GoogleCloudVisionV1p2beta1Word', 4, repeated=True)


class GoogleCloudVisionV1p2beta1Position(_messages.Message):
  r"""A 3D position in the image, used primarily for Face detection landmarks.
  A valid Position must have both x and y coordinates. The position
  coordinates are in the same scale as the original image.

  Fields:
    x: X coordinate.
    y: Y coordinate.
    z: Z coordinate (or depth).
  """

  x = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  y = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  z = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p2beta1Product(_messages.Message):
  r"""A Product contains ReferenceImages.

  Fields:
    description: User-provided metadata to be stored with this product. Must
      be at most 4096 characters long.
    displayName: The user-provided name for this Product. Must not be empty.
      Must be at most 4096 characters long.
    name: The resource name of the product. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`. This field
      is ignored when creating a product.
    productCategory: Immutable. The category for the product identified by the
      reference image. This should be one of "homegoods-v2", "apparel-v2",
      "toys-v2", "packagedgoods-v1" or "general-v1". The legacy categories
      "homegoods", "apparel", and "toys" are still supported, but these should
      not be used for new products.
    productLabels: Key-value pairs that can be attached to a product. At query
      time, constraints can be specified based on the product_labels. Note
      that integer values can be provided as strings, e.g. "1199". Only
      strings with integer values can match a range-based restriction which is
      to be supported soon. Multiple values can be assigned to the same key.
      One product may have up to 500 product_labels. Notice that the total
      number of distinct product_labels over all products in one ProductSet
      cannot exceed 1M, otherwise the product search pipeline will refuse to
      work for that ProductSet.
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  name = _messages.StringField(3)
  productCategory = _messages.StringField(4)
  productLabels = _messages.MessageField('GoogleCloudVisionV1p2beta1ProductKeyValue', 5, repeated=True)


class GoogleCloudVisionV1p2beta1ProductKeyValue(_messages.Message):
  r"""A product label represented as a key-value pair.

  Fields:
    key: The key of the label attached to the product. Cannot be empty and
      cannot exceed 128 bytes.
    value: The value of the label attached to the product. Cannot be empty and
      cannot exceed 128 bytes.
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleCloudVisionV1p2beta1ProductSearchResults(_messages.Message):
  r"""Results for a product search request.

  Fields:
    indexTime: Timestamp of the index which provided these results. Products
      added to the product set and products removed from the product set after
      this time are not reflected in the current results.
    productGroupedResults: List of results grouped by products detected in the
      query image. Each entry corresponds to one bounding polygon in the query
      image, and contains the matching products specific to that region. There
      may be duplicate product matches in the union of all the per-product
      results.
    results: List of results, one for each product match.
  """

  indexTime = _messages.StringField(1)
  productGroupedResults = _messages.MessageField('GoogleCloudVisionV1p2beta1ProductSearchResultsGroupedResult', 2, repeated=True)
  results = _messages.MessageField('GoogleCloudVisionV1p2beta1ProductSearchResultsResult', 3, repeated=True)


class GoogleCloudVisionV1p2beta1ProductSearchResultsGroupedResult(_messages.Message):
  r"""Information about the products similar to a single product in a query
  image.

  Fields:
    boundingPoly: The bounding polygon around the product detected in the
      query image.
    objectAnnotations: List of generic predictions for the object in the
      bounding box.
    results: List of results, one for each product match.
  """

  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p2beta1BoundingPoly', 1)
  objectAnnotations = _messages.MessageField('GoogleCloudVisionV1p2beta1ProductSearchResultsObjectAnnotation', 2, repeated=True)
  results = _messages.MessageField('GoogleCloudVisionV1p2beta1ProductSearchResultsResult', 3, repeated=True)


class GoogleCloudVisionV1p2beta1ProductSearchResultsObjectAnnotation(_messages.Message):
  r"""Prediction for what the object in the bounding box is.

  Fields:
    languageCode: The BCP-47 language code, such as "en-US" or "sr-Latn". For
      more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
    mid: Object ID that should align with EntityAnnotation mid.
    name: Object name, expressed in its `language_code` language.
    score: Score of the result. Range [0, 1].
  """

  languageCode = _messages.StringField(1)
  mid = _messages.StringField(2)
  name = _messages.StringField(3)
  score = _messages.FloatField(4, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p2beta1ProductSearchResultsResult(_messages.Message):
  r"""Information about a product.

  Fields:
    image: The resource name of the image from the product that is the closest
      match to the query.
    product: The Product.
    score: A confidence level on the match, ranging from 0 (no confidence) to
      1 (full confidence).
  """

  image = _messages.StringField(1)
  product = _messages.MessageField('GoogleCloudVisionV1p2beta1Product', 2)
  score = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p2beta1Property(_messages.Message):
  r"""A `Property` consists of a user-supplied name/value pair.

  Fields:
    name: Name of the property.
    uint64Value: Value of numeric properties.
    value: Value of the property.
  """

  name = _messages.StringField(1)
  uint64Value = _messages.IntegerField(2, variant=_messages.Variant.UINT64)
  value = _messages.StringField(3)


class GoogleCloudVisionV1p2beta1SafeSearchAnnotation(_messages.Message):
  r"""Set of features pertaining to the image, computed by computer vision
  methods over safe-search verticals (for example, adult, spoof, medical,
  violence).

  Enums:
    AdultValueValuesEnum: Represents the adult content likelihood for the
      image. Adult content may contain elements such as nudity, pornographic
      images or cartoons, or sexual activities.
    MedicalValueValuesEnum: Likelihood that this is a medical image.
    RacyValueValuesEnum: Likelihood that the request image contains racy
      content. Racy content may include (but is not limited to) skimpy or
      sheer clothing, strategically covered nudity, lewd or provocative poses,
      or close-ups of sensitive body areas.
    SpoofValueValuesEnum: Spoof likelihood. The likelihood that an
      modification was made to the image's canonical version to make it appear
      funny or offensive.
    ViolenceValueValuesEnum: Likelihood that this image contains violent
      content.

  Fields:
    adult: Represents the adult content likelihood for the image. Adult
      content may contain elements such as nudity, pornographic images or
      cartoons, or sexual activities.
    medical: Likelihood that this is a medical image.
    racy: Likelihood that the request image contains racy content. Racy
      content may include (but is not limited to) skimpy or sheer clothing,
      strategically covered nudity, lewd or provocative poses, or close-ups of
      sensitive body areas.
    spoof: Spoof likelihood. The likelihood that an modification was made to
      the image's canonical version to make it appear funny or offensive.
    violence: Likelihood that this image contains violent content.
  """

  class AdultValueValuesEnum(_messages.Enum):
    r"""Represents the adult content likelihood for the image. Adult content
    may contain elements such as nudity, pornographic images or cartoons, or
    sexual activities.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class MedicalValueValuesEnum(_messages.Enum):
    r"""Likelihood that this is a medical image.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class RacyValueValuesEnum(_messages.Enum):
    r"""Likelihood that the request image contains racy content. Racy content
    may include (but is not limited to) skimpy or sheer clothing,
    strategically covered nudity, lewd or provocative poses, or close-ups of
    sensitive body areas.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class SpoofValueValuesEnum(_messages.Enum):
    r"""Spoof likelihood. The likelihood that an modification was made to the
    image's canonical version to make it appear funny or offensive.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class ViolenceValueValuesEnum(_messages.Enum):
    r"""Likelihood that this image contains violent content.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  adult = _messages.EnumField('AdultValueValuesEnum', 1)
  medical = _messages.EnumField('MedicalValueValuesEnum', 2)
  racy = _messages.EnumField('RacyValueValuesEnum', 3)
  spoof = _messages.EnumField('SpoofValueValuesEnum', 4)
  violence = _messages.EnumField('ViolenceValueValuesEnum', 5)


class GoogleCloudVisionV1p2beta1Symbol(_messages.Message):
  r"""A single symbol representation.

  Fields:
    boundingBox: The bounding box for the symbol. The vertices are in the
      order of top-left, top-right, bottom-right, bottom-left. When a rotation
      of the bounding box is detected the rotation is represented as around
      the top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results for the symbol. Range [0, 1].
    property: Additional information detected for the symbol.
    text: The actual UTF-8 representation of the symbol.
  """

  boundingBox = _messages.MessageField('GoogleCloudVisionV1p2beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  property = _messages.MessageField('GoogleCloudVisionV1p2beta1TextAnnotationTextProperty', 3)
  text = _messages.StringField(4)


class GoogleCloudVisionV1p2beta1TextAnnotation(_messages.Message):
  r"""TextAnnotation contains a structured representation of OCR extracted
  text. The hierarchy of an OCR extracted text structure is like this:
  TextAnnotation -> Page -> Block -> Paragraph -> Word -> Symbol Each
  structural component, starting from Page, may further have their own
  properties. Properties describe detected languages, breaks etc.. Please
  refer to the TextAnnotation.TextProperty message definition below for more
  detail.

  Fields:
    pages: List of pages detected by OCR.
    text: UTF-8 text detected on the pages.
  """

  pages = _messages.MessageField('GoogleCloudVisionV1p2beta1Page', 1, repeated=True)
  text = _messages.StringField(2)


class GoogleCloudVisionV1p2beta1TextAnnotationDetectedBreak(_messages.Message):
  r"""Detected start or end of a structural component.

  Enums:
    TypeValueValuesEnum: Detected break type.

  Fields:
    isPrefix: True if break prepends the element.
    type: Detected break type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Detected break type.

    Values:
      UNKNOWN: Unknown break label type.
      SPACE: Regular space.
      SURE_SPACE: Sure space (very wide).
      EOL_SURE_SPACE: Line-wrapping break.
      HYPHEN: End-line hyphen that is not present in text; does not co-occur
        with `SPACE`, `LEADER_SPACE`, or `LINE_BREAK`.
      LINE_BREAK: Line break that ends a paragraph.
    """
    UNKNOWN = 0
    SPACE = 1
    SURE_SPACE = 2
    EOL_SURE_SPACE = 3
    HYPHEN = 4
    LINE_BREAK = 5

  isPrefix = _messages.BooleanField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class GoogleCloudVisionV1p2beta1TextAnnotationDetectedLanguage(_messages.Message):
  r"""Detected language for a structural component.

  Fields:
    confidence: Confidence of detected language. Range [0, 1].
    languageCode: The BCP-47 language code, such as "en-US" or "sr-Latn". For
      more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
  """

  confidence = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  languageCode = _messages.StringField(2)


class GoogleCloudVisionV1p2beta1TextAnnotationTextProperty(_messages.Message):
  r"""Additional information detected on the structural component.

  Fields:
    detectedBreak: Detected start or end of a text segment.
    detectedLanguages: A list of detected languages together with confidence.
  """

  detectedBreak = _messages.MessageField('GoogleCloudVisionV1p2beta1TextAnnotationDetectedBreak', 1)
  detectedLanguages = _messages.MessageField('GoogleCloudVisionV1p2beta1TextAnnotationDetectedLanguage', 2, repeated=True)


class GoogleCloudVisionV1p2beta1Vertex(_messages.Message):
  r"""A vertex represents a 2D point in the image. NOTE: the vertex
  coordinates are in the same scale as the original image.

  Fields:
    x: X coordinate.
    y: Y coordinate.
  """

  x = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  y = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudVisionV1p2beta1WebDetection(_messages.Message):
  r"""Relevant information for the image from the Internet.

  Fields:
    bestGuessLabels: The service's best guess as to the topic of the request
      image. Inferred from similar images on the open web.
    fullMatchingImages: Fully matching images from the Internet. Can include
      resized copies of the query image.
    pagesWithMatchingImages: Web pages containing the matching images from the
      Internet.
    partialMatchingImages: Partial matching images from the Internet. Those
      images are similar enough to share some key-point features. For example
      an original image will likely have partial matching for its crops.
    visuallySimilarImages: The visually similar image results.
    webEntities: Deduced entities from similar images on the Internet.
  """

  bestGuessLabels = _messages.MessageField('GoogleCloudVisionV1p2beta1WebDetectionWebLabel', 1, repeated=True)
  fullMatchingImages = _messages.MessageField('GoogleCloudVisionV1p2beta1WebDetectionWebImage', 2, repeated=True)
  pagesWithMatchingImages = _messages.MessageField('GoogleCloudVisionV1p2beta1WebDetectionWebPage', 3, repeated=True)
  partialMatchingImages = _messages.MessageField('GoogleCloudVisionV1p2beta1WebDetectionWebImage', 4, repeated=True)
  visuallySimilarImages = _messages.MessageField('GoogleCloudVisionV1p2beta1WebDetectionWebImage', 5, repeated=True)
  webEntities = _messages.MessageField('GoogleCloudVisionV1p2beta1WebDetectionWebEntity', 6, repeated=True)


class GoogleCloudVisionV1p2beta1WebDetectionWebEntity(_messages.Message):
  r"""Entity deduced from similar images on the Internet.

  Fields:
    description: Canonical description of the entity, in English.
    entityId: Opaque entity ID.
    score: Overall relevancy score for the entity. Not normalized and not
      comparable across different image queries.
  """

  description = _messages.StringField(1)
  entityId = _messages.StringField(2)
  score = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p2beta1WebDetectionWebImage(_messages.Message):
  r"""Metadata for online images.

  Fields:
    score: (Deprecated) Overall relevancy score for the image.
    url: The result image URL.
  """

  score = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  url = _messages.StringField(2)


class GoogleCloudVisionV1p2beta1WebDetectionWebLabel(_messages.Message):
  r"""Label to provide extra metadata for the web detection.

  Fields:
    label: Label for extra metadata.
    languageCode: The BCP-47 language code for `label`, such as "en-US" or
      "sr-Latn". For more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
  """

  label = _messages.StringField(1)
  languageCode = _messages.StringField(2)


class GoogleCloudVisionV1p2beta1WebDetectionWebPage(_messages.Message):
  r"""Metadata for web pages.

  Fields:
    fullMatchingImages: Fully matching images on the page. Can include resized
      copies of the query image.
    pageTitle: Title for the web page, may contain HTML markups.
    partialMatchingImages: Partial matching images on the page. Those images
      are similar enough to share some key-point features. For example an
      original image will likely have partial matching for its crops.
    score: (Deprecated) Overall relevancy score for the web page.
    url: The result web page URL.
  """

  fullMatchingImages = _messages.MessageField('GoogleCloudVisionV1p2beta1WebDetectionWebImage', 1, repeated=True)
  pageTitle = _messages.StringField(2)
  partialMatchingImages = _messages.MessageField('GoogleCloudVisionV1p2beta1WebDetectionWebImage', 3, repeated=True)
  score = _messages.FloatField(4, variant=_messages.Variant.FLOAT)
  url = _messages.StringField(5)


class GoogleCloudVisionV1p2beta1Word(_messages.Message):
  r"""A word representation.

  Fields:
    boundingBox: The bounding box for the word. The vertices are in the order
      of top-left, top-right, bottom-right, bottom-left. When a rotation of
      the bounding box is detected the rotation is represented as around the
      top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results for the word. Range [0, 1].
    property: Additional information detected for the word.
    symbols: List of symbols in the word. The order of the symbols follows the
      natural reading order.
  """

  boundingBox = _messages.MessageField('GoogleCloudVisionV1p2beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  property = _messages.MessageField('GoogleCloudVisionV1p2beta1TextAnnotationTextProperty', 3)
  symbols = _messages.MessageField('GoogleCloudVisionV1p2beta1Symbol', 4, repeated=True)


class GoogleCloudVisionV1p3beta1AnnotateFileResponse(_messages.Message):
  r"""Response to a single file annotation request. A file may contain one or
  more images, which individually have their own responses.

  Fields:
    error: If set, represents the error message for the failed request. The
      `responses` field will not be set in this case.
    inputConfig: Information about the file for which this response is
      generated.
    responses: Individual responses to images found within the file. This
      field will be empty if the `error` field is set.
    totalPages: This field gives the total number of pages in the file.
  """

  error = _messages.MessageField('Status', 1)
  inputConfig = _messages.MessageField('GoogleCloudVisionV1p3beta1InputConfig', 2)
  responses = _messages.MessageField('GoogleCloudVisionV1p3beta1AnnotateImageResponse', 3, repeated=True)
  totalPages = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudVisionV1p3beta1AnnotateImageResponse(_messages.Message):
  r"""Response to an image annotation request.

  Fields:
    context: If present, contextual information is needed to understand where
      this image comes from.
    cropHintsAnnotation: If present, crop hints have completed successfully.
    error: If set, represents the error message for the operation. Note that
      filled-in image annotations are guaranteed to be correct, even when
      `error` is set.
    faceAnnotations: If present, face detection has completed successfully.
    fullTextAnnotation: If present, text (OCR) detection or document (OCR)
      text detection has completed successfully. This annotation provides the
      structural hierarchy for the OCR detected text.
    imagePropertiesAnnotation: If present, image properties were extracted
      successfully.
    labelAnnotations: If present, label detection has completed successfully.
    landmarkAnnotations: If present, landmark detection has completed
      successfully.
    localizedObjectAnnotations: If present, localized object detection has
      completed successfully. This will be sorted descending by confidence
      score.
    logoAnnotations: If present, logo detection has completed successfully.
    productSearchResults: If present, product search has completed
      successfully.
    safeSearchAnnotation: If present, safe-search annotation has completed
      successfully.
    textAnnotations: If present, text (OCR) detection has completed
      successfully.
    webDetection: If present, web detection has completed successfully.
  """

  context = _messages.MessageField('GoogleCloudVisionV1p3beta1ImageAnnotationContext', 1)
  cropHintsAnnotation = _messages.MessageField('GoogleCloudVisionV1p3beta1CropHintsAnnotation', 2)
  error = _messages.MessageField('Status', 3)
  faceAnnotations = _messages.MessageField('GoogleCloudVisionV1p3beta1FaceAnnotation', 4, repeated=True)
  fullTextAnnotation = _messages.MessageField('GoogleCloudVisionV1p3beta1TextAnnotation', 5)
  imagePropertiesAnnotation = _messages.MessageField('GoogleCloudVisionV1p3beta1ImageProperties', 6)
  labelAnnotations = _messages.MessageField('GoogleCloudVisionV1p3beta1EntityAnnotation', 7, repeated=True)
  landmarkAnnotations = _messages.MessageField('GoogleCloudVisionV1p3beta1EntityAnnotation', 8, repeated=True)
  localizedObjectAnnotations = _messages.MessageField('GoogleCloudVisionV1p3beta1LocalizedObjectAnnotation', 9, repeated=True)
  logoAnnotations = _messages.MessageField('GoogleCloudVisionV1p3beta1EntityAnnotation', 10, repeated=True)
  productSearchResults = _messages.MessageField('GoogleCloudVisionV1p3beta1ProductSearchResults', 11)
  safeSearchAnnotation = _messages.MessageField('GoogleCloudVisionV1p3beta1SafeSearchAnnotation', 12)
  textAnnotations = _messages.MessageField('GoogleCloudVisionV1p3beta1EntityAnnotation', 13, repeated=True)
  webDetection = _messages.MessageField('GoogleCloudVisionV1p3beta1WebDetection', 14)


class GoogleCloudVisionV1p3beta1AsyncAnnotateFileResponse(_messages.Message):
  r"""The response for a single offline file annotation request.

  Fields:
    outputConfig: The output location and metadata from
      AsyncAnnotateFileRequest.
  """

  outputConfig = _messages.MessageField('GoogleCloudVisionV1p3beta1OutputConfig', 1)


class GoogleCloudVisionV1p3beta1AsyncBatchAnnotateFilesResponse(_messages.Message):
  r"""Response to an async batch file annotation request.

  Fields:
    responses: The list of file annotation responses, one for each request in
      AsyncBatchAnnotateFilesRequest.
  """

  responses = _messages.MessageField('GoogleCloudVisionV1p3beta1AsyncAnnotateFileResponse', 1, repeated=True)


class GoogleCloudVisionV1p3beta1BatchOperationMetadata(_messages.Message):
  r"""Metadata for the batch operations such as the current state. This is
  included in the `metadata` field of the `Operation` returned by the
  `GetOperation` call of the `google::longrunning::Operations` service.

  Enums:
    StateValueValuesEnum: The current state of the batch operation.

  Fields:
    endTime: The time when the batch request is finished and
      google.longrunning.Operation.done is set to true.
    state: The current state of the batch operation.
    submitTime: The time when the batch request was submitted to the server.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The current state of the batch operation.

    Values:
      STATE_UNSPECIFIED: Invalid.
      PROCESSING: Request is actively being processed.
      SUCCESSFUL: The request is done and at least one item has been
        successfully processed.
      FAILED: The request is done and no item has been successfully processed.
      CANCELLED: The request is done after the
        longrunning.Operations.CancelOperation has been called by the user.
        Any records that were processed before the cancel command are output
        as specified in the request.
    """
    STATE_UNSPECIFIED = 0
    PROCESSING = 1
    SUCCESSFUL = 2
    FAILED = 3
    CANCELLED = 4

  endTime = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  submitTime = _messages.StringField(3)


class GoogleCloudVisionV1p3beta1Block(_messages.Message):
  r"""Logical element on the page.

  Enums:
    BlockTypeValueValuesEnum: Detected block type (text, image etc) for this
      block.

  Fields:
    blockType: Detected block type (text, image etc) for this block.
    boundingBox: The bounding box for the block. The vertices are in the order
      of top-left, top-right, bottom-right, bottom-left. When a rotation of
      the bounding box is detected the rotation is represented as around the
      top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results on the block. Range [0, 1].
    paragraphs: List of paragraphs in this block (if this blocks is of type
      text).
    property: Additional information detected for the block.
  """

  class BlockTypeValueValuesEnum(_messages.Enum):
    r"""Detected block type (text, image etc) for this block.

    Values:
      UNKNOWN: Unknown block type.
      TEXT: Regular text block.
      TABLE: Table block.
      PICTURE: Image block.
      RULER: Horizontal/vertical line box.
      BARCODE: Barcode block.
    """
    UNKNOWN = 0
    TEXT = 1
    TABLE = 2
    PICTURE = 3
    RULER = 4
    BARCODE = 5

  blockType = _messages.EnumField('BlockTypeValueValuesEnum', 1)
  boundingBox = _messages.MessageField('GoogleCloudVisionV1p3beta1BoundingPoly', 2)
  confidence = _messages.FloatField(3, variant=_messages.Variant.FLOAT)
  paragraphs = _messages.MessageField('GoogleCloudVisionV1p3beta1Paragraph', 4, repeated=True)
  property = _messages.MessageField('GoogleCloudVisionV1p3beta1TextAnnotationTextProperty', 5)


class GoogleCloudVisionV1p3beta1BoundingPoly(_messages.Message):
  r"""A bounding polygon for the detected image annotation.

  Fields:
    normalizedVertices: The bounding polygon normalized vertices.
    vertices: The bounding polygon vertices.
  """

  normalizedVertices = _messages.MessageField('GoogleCloudVisionV1p3beta1NormalizedVertex', 1, repeated=True)
  vertices = _messages.MessageField('GoogleCloudVisionV1p3beta1Vertex', 2, repeated=True)


class GoogleCloudVisionV1p3beta1ColorInfo(_messages.Message):
  r"""Color information consists of RGB channels, score, and the fraction of
  the image that the color occupies in the image.

  Fields:
    color: RGB components of the color.
    pixelFraction: The fraction of pixels the color occupies in the image.
      Value in range [0, 1].
    score: Image-specific score for this color. Value in range [0, 1].
  """

  color = _messages.MessageField('Color', 1)
  pixelFraction = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  score = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p3beta1CropHint(_messages.Message):
  r"""Single crop hint that is used to generate a new crop when serving an
  image.

  Fields:
    boundingPoly: The bounding polygon for the crop region. The coordinates of
      the bounding box are in the original image's scale.
    confidence: Confidence of this being a salient region. Range [0, 1].
    importanceFraction: Fraction of importance of this salient region with
      respect to the original image.
  """

  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p3beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  importanceFraction = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p3beta1CropHintsAnnotation(_messages.Message):
  r"""Set of crop hints that are used to generate new crops when serving
  images.

  Fields:
    cropHints: Crop hint results.
  """

  cropHints = _messages.MessageField('GoogleCloudVisionV1p3beta1CropHint', 1, repeated=True)


class GoogleCloudVisionV1p3beta1DominantColorsAnnotation(_messages.Message):
  r"""Set of dominant colors and their corresponding scores.

  Fields:
    colors: RGB color values with their score and pixel fraction.
  """

  colors = _messages.MessageField('GoogleCloudVisionV1p3beta1ColorInfo', 1, repeated=True)


class GoogleCloudVisionV1p3beta1EntityAnnotation(_messages.Message):
  r"""Set of detected entity features.

  Fields:
    boundingPoly: Image region to which this entity belongs. Not produced for
      `LABEL_DETECTION` features.
    confidence: **Deprecated. Use `score` instead.** The accuracy of the
      entity detection in an image. For example, for an image in which the
      "Eiffel Tower" entity is detected, this field represents the confidence
      that there is a tower in the query image. Range [0, 1].
    description: Entity textual description, expressed in its `locale`
      language.
    locale: The language code for the locale in which the entity textual
      `description` is expressed.
    locations: The location information for the detected entity. Multiple
      `LocationInfo` elements can be present because one location may indicate
      the location of the scene in the image, and another location may
      indicate the location of the place where the image was taken. Location
      information is usually present for landmarks.
    mid: Opaque entity ID. Some IDs may be available in [Google Knowledge
      Graph Search API](https://developers.google.com/knowledge-graph/).
    properties: Some entities may have optional user-supplied `Property`
      (name/value) fields, such a score or string that qualifies the entity.
    score: Overall score of the result. Range [0, 1].
    topicality: The relevancy of the ICA (Image Content Annotation) label to
      the image. For example, the relevancy of "tower" is likely higher to an
      image containing the detected "Eiffel Tower" than to an image containing
      a detected distant towering building, even though the confidence that
      there is a tower in each image may be the same. Range [0, 1].
  """

  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p3beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  description = _messages.StringField(3)
  locale = _messages.StringField(4)
  locations = _messages.MessageField('GoogleCloudVisionV1p3beta1LocationInfo', 5, repeated=True)
  mid = _messages.StringField(6)
  properties = _messages.MessageField('GoogleCloudVisionV1p3beta1Property', 7, repeated=True)
  score = _messages.FloatField(8, variant=_messages.Variant.FLOAT)
  topicality = _messages.FloatField(9, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p3beta1FaceAnnotation(_messages.Message):
  r"""A face annotation object contains the results of face detection.

  Enums:
    AngerLikelihoodValueValuesEnum: Anger likelihood.
    BlurredLikelihoodValueValuesEnum: Blurred likelihood.
    HeadwearLikelihoodValueValuesEnum: Headwear likelihood.
    JoyLikelihoodValueValuesEnum: Joy likelihood.
    SorrowLikelihoodValueValuesEnum: Sorrow likelihood.
    SurpriseLikelihoodValueValuesEnum: Surprise likelihood.
    UnderExposedLikelihoodValueValuesEnum: Under-exposed likelihood.

  Fields:
    angerLikelihood: Anger likelihood.
    blurredLikelihood: Blurred likelihood.
    boundingPoly: The bounding polygon around the face. The coordinates of the
      bounding box are in the original image's scale. The bounding box is
      computed to "frame" the face in accordance with human expectations. It
      is based on the landmarker results. Note that one or more x and/or y
      coordinates may not be generated in the `BoundingPoly` (the polygon will
      be unbounded) if only a partial face appears in the image to be
      annotated.
    detectionConfidence: Detection confidence. Range [0, 1].
    fdBoundingPoly: The `fd_bounding_poly` bounding polygon is tighter than
      the `boundingPoly`, and encloses only the skin part of the face.
      Typically, it is used to eliminate the face from any image analysis that
      detects the "amount of skin" visible in an image. It is not based on the
      landmarker results, only on the initial face detection, hence the fd
      (face detection) prefix.
    headwearLikelihood: Headwear likelihood.
    joyLikelihood: Joy likelihood.
    landmarkingConfidence: Face landmarking confidence. Range [0, 1].
    landmarks: Detected face landmarks.
    panAngle: Yaw angle, which indicates the leftward/rightward angle that the
      face is pointing relative to the vertical plane perpendicular to the
      image. Range [-180,180].
    rollAngle: Roll angle, which indicates the amount of clockwise/anti-
      clockwise rotation of the face relative to the image vertical about the
      axis perpendicular to the face. Range [-180,180].
    sorrowLikelihood: Sorrow likelihood.
    surpriseLikelihood: Surprise likelihood.
    tiltAngle: Pitch angle, which indicates the upwards/downwards angle that
      the face is pointing relative to the image's horizontal plane. Range
      [-180,180].
    underExposedLikelihood: Under-exposed likelihood.
  """

  class AngerLikelihoodValueValuesEnum(_messages.Enum):
    r"""Anger likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class BlurredLikelihoodValueValuesEnum(_messages.Enum):
    r"""Blurred likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class HeadwearLikelihoodValueValuesEnum(_messages.Enum):
    r"""Headwear likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class JoyLikelihoodValueValuesEnum(_messages.Enum):
    r"""Joy likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class SorrowLikelihoodValueValuesEnum(_messages.Enum):
    r"""Sorrow likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class SurpriseLikelihoodValueValuesEnum(_messages.Enum):
    r"""Surprise likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class UnderExposedLikelihoodValueValuesEnum(_messages.Enum):
    r"""Under-exposed likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  angerLikelihood = _messages.EnumField('AngerLikelihoodValueValuesEnum', 1)
  blurredLikelihood = _messages.EnumField('BlurredLikelihoodValueValuesEnum', 2)
  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p3beta1BoundingPoly', 3)
  detectionConfidence = _messages.FloatField(4, variant=_messages.Variant.FLOAT)
  fdBoundingPoly = _messages.MessageField('GoogleCloudVisionV1p3beta1BoundingPoly', 5)
  headwearLikelihood = _messages.EnumField('HeadwearLikelihoodValueValuesEnum', 6)
  joyLikelihood = _messages.EnumField('JoyLikelihoodValueValuesEnum', 7)
  landmarkingConfidence = _messages.FloatField(8, variant=_messages.Variant.FLOAT)
  landmarks = _messages.MessageField('GoogleCloudVisionV1p3beta1FaceAnnotationLandmark', 9, repeated=True)
  panAngle = _messages.FloatField(10, variant=_messages.Variant.FLOAT)
  rollAngle = _messages.FloatField(11, variant=_messages.Variant.FLOAT)
  sorrowLikelihood = _messages.EnumField('SorrowLikelihoodValueValuesEnum', 12)
  surpriseLikelihood = _messages.EnumField('SurpriseLikelihoodValueValuesEnum', 13)
  tiltAngle = _messages.FloatField(14, variant=_messages.Variant.FLOAT)
  underExposedLikelihood = _messages.EnumField('UnderExposedLikelihoodValueValuesEnum', 15)


class GoogleCloudVisionV1p3beta1FaceAnnotationLandmark(_messages.Message):
  r"""A face-specific landmark (for example, a face feature).

  Enums:
    TypeValueValuesEnum: Face landmark type.

  Fields:
    position: Face landmark position.
    type: Face landmark type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Face landmark type.

    Values:
      UNKNOWN_LANDMARK: Unknown face landmark detected. Should not be filled.
      LEFT_EYE: Left eye.
      RIGHT_EYE: Right eye.
      LEFT_OF_LEFT_EYEBROW: Left of left eyebrow.
      RIGHT_OF_LEFT_EYEBROW: Right of left eyebrow.
      LEFT_OF_RIGHT_EYEBROW: Left of right eyebrow.
      RIGHT_OF_RIGHT_EYEBROW: Right of right eyebrow.
      MIDPOINT_BETWEEN_EYES: Midpoint between eyes.
      NOSE_TIP: Nose tip.
      UPPER_LIP: Upper lip.
      LOWER_LIP: Lower lip.
      MOUTH_LEFT: Mouth left.
      MOUTH_RIGHT: Mouth right.
      MOUTH_CENTER: Mouth center.
      NOSE_BOTTOM_RIGHT: Nose, bottom right.
      NOSE_BOTTOM_LEFT: Nose, bottom left.
      NOSE_BOTTOM_CENTER: Nose, bottom center.
      LEFT_EYE_TOP_BOUNDARY: Left eye, top boundary.
      LEFT_EYE_RIGHT_CORNER: Left eye, right corner.
      LEFT_EYE_BOTTOM_BOUNDARY: Left eye, bottom boundary.
      LEFT_EYE_LEFT_CORNER: Left eye, left corner.
      RIGHT_EYE_TOP_BOUNDARY: Right eye, top boundary.
      RIGHT_EYE_RIGHT_CORNER: Right eye, right corner.
      RIGHT_EYE_BOTTOM_BOUNDARY: Right eye, bottom boundary.
      RIGHT_EYE_LEFT_CORNER: Right eye, left corner.
      LEFT_EYEBROW_UPPER_MIDPOINT: Left eyebrow, upper midpoint.
      RIGHT_EYEBROW_UPPER_MIDPOINT: Right eyebrow, upper midpoint.
      LEFT_EAR_TRAGION: Left ear tragion.
      RIGHT_EAR_TRAGION: Right ear tragion.
      LEFT_EYE_PUPIL: Left eye pupil.
      RIGHT_EYE_PUPIL: Right eye pupil.
      FOREHEAD_GLABELLA: Forehead glabella.
      CHIN_GNATHION: Chin gnathion.
      CHIN_LEFT_GONION: Chin left gonion.
      CHIN_RIGHT_GONION: Chin right gonion.
      LEFT_CHEEK_CENTER: Left cheek center.
      RIGHT_CHEEK_CENTER: Right cheek center.
    """
    UNKNOWN_LANDMARK = 0
    LEFT_EYE = 1
    RIGHT_EYE = 2
    LEFT_OF_LEFT_EYEBROW = 3
    RIGHT_OF_LEFT_EYEBROW = 4
    LEFT_OF_RIGHT_EYEBROW = 5
    RIGHT_OF_RIGHT_EYEBROW = 6
    MIDPOINT_BETWEEN_EYES = 7
    NOSE_TIP = 8
    UPPER_LIP = 9
    LOWER_LIP = 10
    MOUTH_LEFT = 11
    MOUTH_RIGHT = 12
    MOUTH_CENTER = 13
    NOSE_BOTTOM_RIGHT = 14
    NOSE_BOTTOM_LEFT = 15
    NOSE_BOTTOM_CENTER = 16
    LEFT_EYE_TOP_BOUNDARY = 17
    LEFT_EYE_RIGHT_CORNER = 18
    LEFT_EYE_BOTTOM_BOUNDARY = 19
    LEFT_EYE_LEFT_CORNER = 20
    RIGHT_EYE_TOP_BOUNDARY = 21
    RIGHT_EYE_RIGHT_CORNER = 22
    RIGHT_EYE_BOTTOM_BOUNDARY = 23
    RIGHT_EYE_LEFT_CORNER = 24
    LEFT_EYEBROW_UPPER_MIDPOINT = 25
    RIGHT_EYEBROW_UPPER_MIDPOINT = 26
    LEFT_EAR_TRAGION = 27
    RIGHT_EAR_TRAGION = 28
    LEFT_EYE_PUPIL = 29
    RIGHT_EYE_PUPIL = 30
    FOREHEAD_GLABELLA = 31
    CHIN_GNATHION = 32
    CHIN_LEFT_GONION = 33
    CHIN_RIGHT_GONION = 34
    LEFT_CHEEK_CENTER = 35
    RIGHT_CHEEK_CENTER = 36

  position = _messages.MessageField('GoogleCloudVisionV1p3beta1Position', 1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class GoogleCloudVisionV1p3beta1GcsDestination(_messages.Message):
  r"""The Google Cloud Storage location where the output will be written to.

  Fields:
    uri: Google Cloud Storage URI prefix where the results will be stored.
      Results will be in JSON format and preceded by its corresponding input
      URI prefix. This field can either represent a gcs file prefix or gcs
      directory. In either case, the uri should be unique because in order to
      get all of the output files, you will need to do a wildcard gcs search
      on the uri prefix you provide. Examples: * File Prefix: gs://bucket-
      name/here/filenameprefix The output files will be created in
      gs://bucket-name/here/ and the names of the output files will begin with
      "filenameprefix". * Directory Prefix: gs://bucket-name/some/location/
      The output files will be created in gs://bucket-name/some/location/ and
      the names of the output files could be anything because there was no
      filename prefix specified. If multiple outputs, each response is still
      AnnotateFileResponse, each of which contains some subset of the full
      list of AnnotateImageResponse. Multiple outputs can happen if, for
      example, the output JSON is too large and overflows into multiple
      sharded files.
  """

  uri = _messages.StringField(1)


class GoogleCloudVisionV1p3beta1GcsSource(_messages.Message):
  r"""The Google Cloud Storage location where the input will be read from.

  Fields:
    uri: Google Cloud Storage URI for the input file. This must only be a
      Google Cloud Storage object. Wildcards are not currently supported.
  """

  uri = _messages.StringField(1)


class GoogleCloudVisionV1p3beta1ImageAnnotationContext(_messages.Message):
  r"""If an image was produced from a file (e.g. a PDF), this message gives
  information about the source of that image.

  Fields:
    pageNumber: If the file was a PDF or TIFF, this field gives the page
      number within the file used to produce the image.
    uri: The URI of the file used to produce the image.
  """

  pageNumber = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  uri = _messages.StringField(2)


class GoogleCloudVisionV1p3beta1ImageProperties(_messages.Message):
  r"""Stores image properties, such as dominant colors.

  Fields:
    dominantColors: If present, dominant colors completed successfully.
  """

  dominantColors = _messages.MessageField('GoogleCloudVisionV1p3beta1DominantColorsAnnotation', 1)


class GoogleCloudVisionV1p3beta1ImportProductSetsResponse(_messages.Message):
  r"""Response message for the `ImportProductSets` method. This message is
  returned by the google.longrunning.Operations.GetOperation method in the
  returned google.longrunning.Operation.response field.

  Fields:
    referenceImages: The list of reference_images that are imported
      successfully.
    statuses: The rpc status for each ImportProductSet request, including both
      successes and errors. The number of statuses here matches the number of
      lines in the csv file, and statuses[i] stores the success or failure
      status of processing the i-th line of the csv, starting from line 0.
  """

  referenceImages = _messages.MessageField('GoogleCloudVisionV1p3beta1ReferenceImage', 1, repeated=True)
  statuses = _messages.MessageField('Status', 2, repeated=True)


class GoogleCloudVisionV1p3beta1InputConfig(_messages.Message):
  r"""The desired input location and metadata.

  Fields:
    content: File content, represented as a stream of bytes. Note: As with all
      `bytes` fields, protobuffers use a pure binary representation, whereas
      JSON representations use base64. Currently, this field only works for
      BatchAnnotateFiles requests. It does not work for
      AsyncBatchAnnotateFiles requests.
    gcsSource: The Google Cloud Storage location to read the input from.
    mimeType: The type of the file. Currently only "application/pdf",
      "image/tiff" and "image/gif" are supported. Wildcards are not supported.
  """

  content = _messages.BytesField(1)
  gcsSource = _messages.MessageField('GoogleCloudVisionV1p3beta1GcsSource', 2)
  mimeType = _messages.StringField(3)


class GoogleCloudVisionV1p3beta1LocalizedObjectAnnotation(_messages.Message):
  r"""Set of detected objects with bounding boxes.

  Fields:
    boundingPoly: Image region to which this object belongs. This must be
      populated.
    languageCode: The BCP-47 language code, such as "en-US" or "sr-Latn". For
      more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
    mid: Object ID that should align with EntityAnnotation mid.
    name: Object name, expressed in its `language_code` language.
    score: Score of the result. Range [0, 1].
  """

  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p3beta1BoundingPoly', 1)
  languageCode = _messages.StringField(2)
  mid = _messages.StringField(3)
  name = _messages.StringField(4)
  score = _messages.FloatField(5, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p3beta1LocationInfo(_messages.Message):
  r"""Detected entity location information.

  Fields:
    latLng: lat/long location coordinates.
  """

  latLng = _messages.MessageField('LatLng', 1)


class GoogleCloudVisionV1p3beta1NormalizedVertex(_messages.Message):
  r"""A vertex represents a 2D point in the image. NOTE: the normalized vertex
  coordinates are relative to the original image and range from 0 to 1.

  Fields:
    x: X coordinate.
    y: Y coordinate.
  """

  x = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  y = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p3beta1OperationMetadata(_messages.Message):
  r"""Contains metadata for the BatchAnnotateImages operation.

  Enums:
    StateValueValuesEnum: Current state of the batch operation.

  Fields:
    createTime: The time when the batch request was received.
    state: Current state of the batch operation.
    updateTime: The time when the operation result was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Current state of the batch operation.

    Values:
      STATE_UNSPECIFIED: Invalid.
      CREATED: Request is received.
      RUNNING: Request is actively being processed.
      DONE: The batch processing is done.
      CANCELLED: The batch processing was cancelled.
    """
    STATE_UNSPECIFIED = 0
    CREATED = 1
    RUNNING = 2
    DONE = 3
    CANCELLED = 4

  createTime = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  updateTime = _messages.StringField(3)


class GoogleCloudVisionV1p3beta1OutputConfig(_messages.Message):
  r"""The desired output location and metadata.

  Fields:
    batchSize: The max number of response protos to put into each output JSON
      file on Google Cloud Storage. The valid range is [1, 100]. If not
      specified, the default value is 20. For example, for one pdf file with
      100 pages, 100 response protos will be generated. If `batch_size` = 20,
      then 5 json files each containing 20 response protos will be written
      under the prefix `gcs_destination`.`uri`. Currently, batch_size only
      applies to GcsDestination, with potential future support for other
      output configurations.
    gcsDestination: The Google Cloud Storage location to write the output(s)
      to.
  """

  batchSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  gcsDestination = _messages.MessageField('GoogleCloudVisionV1p3beta1GcsDestination', 2)


class GoogleCloudVisionV1p3beta1Page(_messages.Message):
  r"""Detected page from OCR.

  Fields:
    blocks: List of blocks of text, images etc on this page.
    confidence: Confidence of the OCR results on the page. Range [0, 1].
    height: Page height. For PDFs the unit is points. For images (including
      TIFFs) the unit is pixels.
    property: Additional information detected on the page.
    width: Page width. For PDFs the unit is points. For images (including
      TIFFs) the unit is pixels.
  """

  blocks = _messages.MessageField('GoogleCloudVisionV1p3beta1Block', 1, repeated=True)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  height = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  property = _messages.MessageField('GoogleCloudVisionV1p3beta1TextAnnotationTextProperty', 4)
  width = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class GoogleCloudVisionV1p3beta1Paragraph(_messages.Message):
  r"""Structural unit of text representing a number of words in certain order.

  Fields:
    boundingBox: The bounding box for the paragraph. The vertices are in the
      order of top-left, top-right, bottom-right, bottom-left. When a rotation
      of the bounding box is detected the rotation is represented as around
      the top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results for the paragraph. Range [0, 1].
    property: Additional information detected for the paragraph.
    words: List of all words in this paragraph.
  """

  boundingBox = _messages.MessageField('GoogleCloudVisionV1p3beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  property = _messages.MessageField('GoogleCloudVisionV1p3beta1TextAnnotationTextProperty', 3)
  words = _messages.MessageField('GoogleCloudVisionV1p3beta1Word', 4, repeated=True)


class GoogleCloudVisionV1p3beta1Position(_messages.Message):
  r"""A 3D position in the image, used primarily for Face detection landmarks.
  A valid Position must have both x and y coordinates. The position
  coordinates are in the same scale as the original image.

  Fields:
    x: X coordinate.
    y: Y coordinate.
    z: Z coordinate (or depth).
  """

  x = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  y = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  z = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p3beta1Product(_messages.Message):
  r"""A Product contains ReferenceImages.

  Fields:
    description: User-provided metadata to be stored with this product. Must
      be at most 4096 characters long.
    displayName: The user-provided name for this Product. Must not be empty.
      Must be at most 4096 characters long.
    name: The resource name of the product. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`. This field
      is ignored when creating a product.
    productCategory: Immutable. The category for the product identified by the
      reference image. This should be one of "homegoods-v2", "apparel-v2",
      "toys-v2", "packagedgoods-v1" or "general-v1". The legacy categories
      "homegoods", "apparel", and "toys" are still supported, but these should
      not be used for new products.
    productLabels: Key-value pairs that can be attached to a product. At query
      time, constraints can be specified based on the product_labels. Note
      that integer values can be provided as strings, e.g. "1199". Only
      strings with integer values can match a range-based restriction which is
      to be supported soon. Multiple values can be assigned to the same key.
      One product may have up to 500 product_labels. Notice that the total
      number of distinct product_labels over all products in one ProductSet
      cannot exceed 1M, otherwise the product search pipeline will refuse to
      work for that ProductSet.
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  name = _messages.StringField(3)
  productCategory = _messages.StringField(4)
  productLabels = _messages.MessageField('GoogleCloudVisionV1p3beta1ProductKeyValue', 5, repeated=True)


class GoogleCloudVisionV1p3beta1ProductKeyValue(_messages.Message):
  r"""A product label represented as a key-value pair.

  Fields:
    key: The key of the label attached to the product. Cannot be empty and
      cannot exceed 128 bytes.
    value: The value of the label attached to the product. Cannot be empty and
      cannot exceed 128 bytes.
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleCloudVisionV1p3beta1ProductSearchResults(_messages.Message):
  r"""Results for a product search request.

  Fields:
    indexTime: Timestamp of the index which provided these results. Products
      added to the product set and products removed from the product set after
      this time are not reflected in the current results.
    productGroupedResults: List of results grouped by products detected in the
      query image. Each entry corresponds to one bounding polygon in the query
      image, and contains the matching products specific to that region. There
      may be duplicate product matches in the union of all the per-product
      results.
    results: List of results, one for each product match.
  """

  indexTime = _messages.StringField(1)
  productGroupedResults = _messages.MessageField('GoogleCloudVisionV1p3beta1ProductSearchResultsGroupedResult', 2, repeated=True)
  results = _messages.MessageField('GoogleCloudVisionV1p3beta1ProductSearchResultsResult', 3, repeated=True)


class GoogleCloudVisionV1p3beta1ProductSearchResultsGroupedResult(_messages.Message):
  r"""Information about the products similar to a single product in a query
  image.

  Fields:
    boundingPoly: The bounding polygon around the product detected in the
      query image.
    objectAnnotations: List of generic predictions for the object in the
      bounding box.
    results: List of results, one for each product match.
  """

  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p3beta1BoundingPoly', 1)
  objectAnnotations = _messages.MessageField('GoogleCloudVisionV1p3beta1ProductSearchResultsObjectAnnotation', 2, repeated=True)
  results = _messages.MessageField('GoogleCloudVisionV1p3beta1ProductSearchResultsResult', 3, repeated=True)


class GoogleCloudVisionV1p3beta1ProductSearchResultsObjectAnnotation(_messages.Message):
  r"""Prediction for what the object in the bounding box is.

  Fields:
    languageCode: The BCP-47 language code, such as "en-US" or "sr-Latn". For
      more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
    mid: Object ID that should align with EntityAnnotation mid.
    name: Object name, expressed in its `language_code` language.
    score: Score of the result. Range [0, 1].
  """

  languageCode = _messages.StringField(1)
  mid = _messages.StringField(2)
  name = _messages.StringField(3)
  score = _messages.FloatField(4, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p3beta1ProductSearchResultsResult(_messages.Message):
  r"""Information about a product.

  Fields:
    image: The resource name of the image from the product that is the closest
      match to the query.
    product: The Product.
    score: A confidence level on the match, ranging from 0 (no confidence) to
      1 (full confidence).
  """

  image = _messages.StringField(1)
  product = _messages.MessageField('GoogleCloudVisionV1p3beta1Product', 2)
  score = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p3beta1Property(_messages.Message):
  r"""A `Property` consists of a user-supplied name/value pair.

  Fields:
    name: Name of the property.
    uint64Value: Value of numeric properties.
    value: Value of the property.
  """

  name = _messages.StringField(1)
  uint64Value = _messages.IntegerField(2, variant=_messages.Variant.UINT64)
  value = _messages.StringField(3)


class GoogleCloudVisionV1p3beta1ReferenceImage(_messages.Message):
  r"""A `ReferenceImage` represents a product image and its associated
  metadata, such as bounding boxes.

  Fields:
    boundingPolys: Optional. Bounding polygons around the areas of interest in
      the reference image. If this field is empty, the system will try to
      detect regions of interest. At most 10 bounding polygons will be used.
      The provided shape is converted into a non-rotated rectangle. Once
      converted, the small edge of the rectangle must be greater than or equal
      to 300 pixels. The aspect ratio must be 1:4 or less (i.e. 1:3 is ok; 1:5
      is not).
    name: The resource name of the reference image. Format is: `projects/PROJE
      CT_ID/locations/LOC_ID/products/PRODUCT_ID/referenceImages/IMAGE_ID`.
      This field is ignored when creating a reference image.
    uri: Required. The Google Cloud Storage URI of the reference image. The
      URI must start with `gs://`.
  """

  boundingPolys = _messages.MessageField('GoogleCloudVisionV1p3beta1BoundingPoly', 1, repeated=True)
  name = _messages.StringField(2)
  uri = _messages.StringField(3)


class GoogleCloudVisionV1p3beta1SafeSearchAnnotation(_messages.Message):
  r"""Set of features pertaining to the image, computed by computer vision
  methods over safe-search verticals (for example, adult, spoof, medical,
  violence).

  Enums:
    AdultValueValuesEnum: Represents the adult content likelihood for the
      image. Adult content may contain elements such as nudity, pornographic
      images or cartoons, or sexual activities.
    MedicalValueValuesEnum: Likelihood that this is a medical image.
    RacyValueValuesEnum: Likelihood that the request image contains racy
      content. Racy content may include (but is not limited to) skimpy or
      sheer clothing, strategically covered nudity, lewd or provocative poses,
      or close-ups of sensitive body areas.
    SpoofValueValuesEnum: Spoof likelihood. The likelihood that an
      modification was made to the image's canonical version to make it appear
      funny or offensive.
    ViolenceValueValuesEnum: Likelihood that this image contains violent
      content.

  Fields:
    adult: Represents the adult content likelihood for the image. Adult
      content may contain elements such as nudity, pornographic images or
      cartoons, or sexual activities.
    medical: Likelihood that this is a medical image.
    racy: Likelihood that the request image contains racy content. Racy
      content may include (but is not limited to) skimpy or sheer clothing,
      strategically covered nudity, lewd or provocative poses, or close-ups of
      sensitive body areas.
    spoof: Spoof likelihood. The likelihood that an modification was made to
      the image's canonical version to make it appear funny or offensive.
    violence: Likelihood that this image contains violent content.
  """

  class AdultValueValuesEnum(_messages.Enum):
    r"""Represents the adult content likelihood for the image. Adult content
    may contain elements such as nudity, pornographic images or cartoons, or
    sexual activities.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class MedicalValueValuesEnum(_messages.Enum):
    r"""Likelihood that this is a medical image.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class RacyValueValuesEnum(_messages.Enum):
    r"""Likelihood that the request image contains racy content. Racy content
    may include (but is not limited to) skimpy or sheer clothing,
    strategically covered nudity, lewd or provocative poses, or close-ups of
    sensitive body areas.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class SpoofValueValuesEnum(_messages.Enum):
    r"""Spoof likelihood. The likelihood that an modification was made to the
    image's canonical version to make it appear funny or offensive.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class ViolenceValueValuesEnum(_messages.Enum):
    r"""Likelihood that this image contains violent content.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  adult = _messages.EnumField('AdultValueValuesEnum', 1)
  medical = _messages.EnumField('MedicalValueValuesEnum', 2)
  racy = _messages.EnumField('RacyValueValuesEnum', 3)
  spoof = _messages.EnumField('SpoofValueValuesEnum', 4)
  violence = _messages.EnumField('ViolenceValueValuesEnum', 5)


class GoogleCloudVisionV1p3beta1Symbol(_messages.Message):
  r"""A single symbol representation.

  Fields:
    boundingBox: The bounding box for the symbol. The vertices are in the
      order of top-left, top-right, bottom-right, bottom-left. When a rotation
      of the bounding box is detected the rotation is represented as around
      the top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results for the symbol. Range [0, 1].
    property: Additional information detected for the symbol.
    text: The actual UTF-8 representation of the symbol.
  """

  boundingBox = _messages.MessageField('GoogleCloudVisionV1p3beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  property = _messages.MessageField('GoogleCloudVisionV1p3beta1TextAnnotationTextProperty', 3)
  text = _messages.StringField(4)


class GoogleCloudVisionV1p3beta1TextAnnotation(_messages.Message):
  r"""TextAnnotation contains a structured representation of OCR extracted
  text. The hierarchy of an OCR extracted text structure is like this:
  TextAnnotation -> Page -> Block -> Paragraph -> Word -> Symbol Each
  structural component, starting from Page, may further have their own
  properties. Properties describe detected languages, breaks etc.. Please
  refer to the TextAnnotation.TextProperty message definition below for more
  detail.

  Fields:
    pages: List of pages detected by OCR.
    text: UTF-8 text detected on the pages.
  """

  pages = _messages.MessageField('GoogleCloudVisionV1p3beta1Page', 1, repeated=True)
  text = _messages.StringField(2)


class GoogleCloudVisionV1p3beta1TextAnnotationDetectedBreak(_messages.Message):
  r"""Detected start or end of a structural component.

  Enums:
    TypeValueValuesEnum: Detected break type.

  Fields:
    isPrefix: True if break prepends the element.
    type: Detected break type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Detected break type.

    Values:
      UNKNOWN: Unknown break label type.
      SPACE: Regular space.
      SURE_SPACE: Sure space (very wide).
      EOL_SURE_SPACE: Line-wrapping break.
      HYPHEN: End-line hyphen that is not present in text; does not co-occur
        with `SPACE`, `LEADER_SPACE`, or `LINE_BREAK`.
      LINE_BREAK: Line break that ends a paragraph.
    """
    UNKNOWN = 0
    SPACE = 1
    SURE_SPACE = 2
    EOL_SURE_SPACE = 3
    HYPHEN = 4
    LINE_BREAK = 5

  isPrefix = _messages.BooleanField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class GoogleCloudVisionV1p3beta1TextAnnotationDetectedLanguage(_messages.Message):
  r"""Detected language for a structural component.

  Fields:
    confidence: Confidence of detected language. Range [0, 1].
    languageCode: The BCP-47 language code, such as "en-US" or "sr-Latn". For
      more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
  """

  confidence = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  languageCode = _messages.StringField(2)


class GoogleCloudVisionV1p3beta1TextAnnotationTextProperty(_messages.Message):
  r"""Additional information detected on the structural component.

  Fields:
    detectedBreak: Detected start or end of a text segment.
    detectedLanguages: A list of detected languages together with confidence.
  """

  detectedBreak = _messages.MessageField('GoogleCloudVisionV1p3beta1TextAnnotationDetectedBreak', 1)
  detectedLanguages = _messages.MessageField('GoogleCloudVisionV1p3beta1TextAnnotationDetectedLanguage', 2, repeated=True)


class GoogleCloudVisionV1p3beta1Vertex(_messages.Message):
  r"""A vertex represents a 2D point in the image. NOTE: the vertex
  coordinates are in the same scale as the original image.

  Fields:
    x: X coordinate.
    y: Y coordinate.
  """

  x = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  y = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudVisionV1p3beta1WebDetection(_messages.Message):
  r"""Relevant information for the image from the Internet.

  Fields:
    bestGuessLabels: The service's best guess as to the topic of the request
      image. Inferred from similar images on the open web.
    fullMatchingImages: Fully matching images from the Internet. Can include
      resized copies of the query image.
    pagesWithMatchingImages: Web pages containing the matching images from the
      Internet.
    partialMatchingImages: Partial matching images from the Internet. Those
      images are similar enough to share some key-point features. For example
      an original image will likely have partial matching for its crops.
    visuallySimilarImages: The visually similar image results.
    webEntities: Deduced entities from similar images on the Internet.
  """

  bestGuessLabels = _messages.MessageField('GoogleCloudVisionV1p3beta1WebDetectionWebLabel', 1, repeated=True)
  fullMatchingImages = _messages.MessageField('GoogleCloudVisionV1p3beta1WebDetectionWebImage', 2, repeated=True)
  pagesWithMatchingImages = _messages.MessageField('GoogleCloudVisionV1p3beta1WebDetectionWebPage', 3, repeated=True)
  partialMatchingImages = _messages.MessageField('GoogleCloudVisionV1p3beta1WebDetectionWebImage', 4, repeated=True)
  visuallySimilarImages = _messages.MessageField('GoogleCloudVisionV1p3beta1WebDetectionWebImage', 5, repeated=True)
  webEntities = _messages.MessageField('GoogleCloudVisionV1p3beta1WebDetectionWebEntity', 6, repeated=True)


class GoogleCloudVisionV1p3beta1WebDetectionWebEntity(_messages.Message):
  r"""Entity deduced from similar images on the Internet.

  Fields:
    description: Canonical description of the entity, in English.
    entityId: Opaque entity ID.
    score: Overall relevancy score for the entity. Not normalized and not
      comparable across different image queries.
  """

  description = _messages.StringField(1)
  entityId = _messages.StringField(2)
  score = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p3beta1WebDetectionWebImage(_messages.Message):
  r"""Metadata for online images.

  Fields:
    score: (Deprecated) Overall relevancy score for the image.
    url: The result image URL.
  """

  score = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  url = _messages.StringField(2)


class GoogleCloudVisionV1p3beta1WebDetectionWebLabel(_messages.Message):
  r"""Label to provide extra metadata for the web detection.

  Fields:
    label: Label for extra metadata.
    languageCode: The BCP-47 language code for `label`, such as "en-US" or
      "sr-Latn". For more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
  """

  label = _messages.StringField(1)
  languageCode = _messages.StringField(2)


class GoogleCloudVisionV1p3beta1WebDetectionWebPage(_messages.Message):
  r"""Metadata for web pages.

  Fields:
    fullMatchingImages: Fully matching images on the page. Can include resized
      copies of the query image.
    pageTitle: Title for the web page, may contain HTML markups.
    partialMatchingImages: Partial matching images on the page. Those images
      are similar enough to share some key-point features. For example an
      original image will likely have partial matching for its crops.
    score: (Deprecated) Overall relevancy score for the web page.
    url: The result web page URL.
  """

  fullMatchingImages = _messages.MessageField('GoogleCloudVisionV1p3beta1WebDetectionWebImage', 1, repeated=True)
  pageTitle = _messages.StringField(2)
  partialMatchingImages = _messages.MessageField('GoogleCloudVisionV1p3beta1WebDetectionWebImage', 3, repeated=True)
  score = _messages.FloatField(4, variant=_messages.Variant.FLOAT)
  url = _messages.StringField(5)


class GoogleCloudVisionV1p3beta1Word(_messages.Message):
  r"""A word representation.

  Fields:
    boundingBox: The bounding box for the word. The vertices are in the order
      of top-left, top-right, bottom-right, bottom-left. When a rotation of
      the bounding box is detected the rotation is represented as around the
      top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results for the word. Range [0, 1].
    property: Additional information detected for the word.
    symbols: List of symbols in the word. The order of the symbols follows the
      natural reading order.
  """

  boundingBox = _messages.MessageField('GoogleCloudVisionV1p3beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  property = _messages.MessageField('GoogleCloudVisionV1p3beta1TextAnnotationTextProperty', 3)
  symbols = _messages.MessageField('GoogleCloudVisionV1p3beta1Symbol', 4, repeated=True)


class GoogleCloudVisionV1p4beta1AnnotateFileResponse(_messages.Message):
  r"""Response to a single file annotation request. A file may contain one or
  more images, which individually have their own responses.

  Fields:
    error: If set, represents the error message for the failed request. The
      `responses` field will not be set in this case.
    inputConfig: Information about the file for which this response is
      generated.
    responses: Individual responses to images found within the file. This
      field will be empty if the `error` field is set.
    totalPages: This field gives the total number of pages in the file.
  """

  error = _messages.MessageField('Status', 1)
  inputConfig = _messages.MessageField('GoogleCloudVisionV1p4beta1InputConfig', 2)
  responses = _messages.MessageField('GoogleCloudVisionV1p4beta1AnnotateImageResponse', 3, repeated=True)
  totalPages = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudVisionV1p4beta1AnnotateImageResponse(_messages.Message):
  r"""Response to an image annotation request.

  Fields:
    context: If present, contextual information is needed to understand where
      this image comes from.
    cropHintsAnnotation: If present, crop hints have completed successfully.
    error: If set, represents the error message for the operation. Note that
      filled-in image annotations are guaranteed to be correct, even when
      `error` is set.
    faceAnnotations: If present, face detection has completed successfully.
    fullTextAnnotation: If present, text (OCR) detection or document (OCR)
      text detection has completed successfully. This annotation provides the
      structural hierarchy for the OCR detected text.
    imagePropertiesAnnotation: If present, image properties were extracted
      successfully.
    labelAnnotations: If present, label detection has completed successfully.
    landmarkAnnotations: If present, landmark detection has completed
      successfully.
    localizedObjectAnnotations: If present, localized object detection has
      completed successfully. This will be sorted descending by confidence
      score.
    logoAnnotations: If present, logo detection has completed successfully.
    productSearchResults: If present, product search has completed
      successfully.
    safeSearchAnnotation: If present, safe-search annotation has completed
      successfully.
    textAnnotations: If present, text (OCR) detection has completed
      successfully.
    webDetection: If present, web detection has completed successfully.
  """

  context = _messages.MessageField('GoogleCloudVisionV1p4beta1ImageAnnotationContext', 1)
  cropHintsAnnotation = _messages.MessageField('GoogleCloudVisionV1p4beta1CropHintsAnnotation', 2)
  error = _messages.MessageField('Status', 3)
  faceAnnotations = _messages.MessageField('GoogleCloudVisionV1p4beta1FaceAnnotation', 4, repeated=True)
  fullTextAnnotation = _messages.MessageField('GoogleCloudVisionV1p4beta1TextAnnotation', 5)
  imagePropertiesAnnotation = _messages.MessageField('GoogleCloudVisionV1p4beta1ImageProperties', 6)
  labelAnnotations = _messages.MessageField('GoogleCloudVisionV1p4beta1EntityAnnotation', 7, repeated=True)
  landmarkAnnotations = _messages.MessageField('GoogleCloudVisionV1p4beta1EntityAnnotation', 8, repeated=True)
  localizedObjectAnnotations = _messages.MessageField('GoogleCloudVisionV1p4beta1LocalizedObjectAnnotation', 9, repeated=True)
  logoAnnotations = _messages.MessageField('GoogleCloudVisionV1p4beta1EntityAnnotation', 10, repeated=True)
  productSearchResults = _messages.MessageField('GoogleCloudVisionV1p4beta1ProductSearchResults', 11)
  safeSearchAnnotation = _messages.MessageField('GoogleCloudVisionV1p4beta1SafeSearchAnnotation', 12)
  textAnnotations = _messages.MessageField('GoogleCloudVisionV1p4beta1EntityAnnotation', 13, repeated=True)
  webDetection = _messages.MessageField('GoogleCloudVisionV1p4beta1WebDetection', 14)


class GoogleCloudVisionV1p4beta1AsyncAnnotateFileResponse(_messages.Message):
  r"""The response for a single offline file annotation request.

  Fields:
    outputConfig: The output location and metadata from
      AsyncAnnotateFileRequest.
  """

  outputConfig = _messages.MessageField('GoogleCloudVisionV1p4beta1OutputConfig', 1)


class GoogleCloudVisionV1p4beta1AsyncBatchAnnotateFilesResponse(_messages.Message):
  r"""Response to an async batch file annotation request.

  Fields:
    responses: The list of file annotation responses, one for each request in
      AsyncBatchAnnotateFilesRequest.
  """

  responses = _messages.MessageField('GoogleCloudVisionV1p4beta1AsyncAnnotateFileResponse', 1, repeated=True)


class GoogleCloudVisionV1p4beta1AsyncBatchAnnotateImagesResponse(_messages.Message):
  r"""Response to an async batch image annotation request.

  Fields:
    outputConfig: The output location and metadata from
      AsyncBatchAnnotateImagesRequest.
  """

  outputConfig = _messages.MessageField('GoogleCloudVisionV1p4beta1OutputConfig', 1)


class GoogleCloudVisionV1p4beta1BatchAnnotateFilesResponse(_messages.Message):
  r"""A list of file annotation responses.

  Fields:
    responses: The list of file annotation responses, each response
      corresponding to each AnnotateFileRequest in BatchAnnotateFilesRequest.
  """

  responses = _messages.MessageField('GoogleCloudVisionV1p4beta1AnnotateFileResponse', 1, repeated=True)


class GoogleCloudVisionV1p4beta1BatchOperationMetadata(_messages.Message):
  r"""Metadata for the batch operations such as the current state. This is
  included in the `metadata` field of the `Operation` returned by the
  `GetOperation` call of the `google::longrunning::Operations` service.

  Enums:
    StateValueValuesEnum: The current state of the batch operation.

  Fields:
    endTime: The time when the batch request is finished and
      google.longrunning.Operation.done is set to true.
    state: The current state of the batch operation.
    submitTime: The time when the batch request was submitted to the server.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The current state of the batch operation.

    Values:
      STATE_UNSPECIFIED: Invalid.
      PROCESSING: Request is actively being processed.
      SUCCESSFUL: The request is done and at least one item has been
        successfully processed.
      FAILED: The request is done and no item has been successfully processed.
      CANCELLED: The request is done after the
        longrunning.Operations.CancelOperation has been called by the user.
        Any records that were processed before the cancel command are output
        as specified in the request.
    """
    STATE_UNSPECIFIED = 0
    PROCESSING = 1
    SUCCESSFUL = 2
    FAILED = 3
    CANCELLED = 4

  endTime = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  submitTime = _messages.StringField(3)


class GoogleCloudVisionV1p4beta1Block(_messages.Message):
  r"""Logical element on the page.

  Enums:
    BlockTypeValueValuesEnum: Detected block type (text, image etc) for this
      block.

  Fields:
    blockType: Detected block type (text, image etc) for this block.
    boundingBox: The bounding box for the block. The vertices are in the order
      of top-left, top-right, bottom-right, bottom-left. When a rotation of
      the bounding box is detected the rotation is represented as around the
      top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results on the block. Range [0, 1].
    paragraphs: List of paragraphs in this block (if this blocks is of type
      text).
    property: Additional information detected for the block.
  """

  class BlockTypeValueValuesEnum(_messages.Enum):
    r"""Detected block type (text, image etc) for this block.

    Values:
      UNKNOWN: Unknown block type.
      TEXT: Regular text block.
      TABLE: Table block.
      PICTURE: Image block.
      RULER: Horizontal/vertical line box.
      BARCODE: Barcode block.
    """
    UNKNOWN = 0
    TEXT = 1
    TABLE = 2
    PICTURE = 3
    RULER = 4
    BARCODE = 5

  blockType = _messages.EnumField('BlockTypeValueValuesEnum', 1)
  boundingBox = _messages.MessageField('GoogleCloudVisionV1p4beta1BoundingPoly', 2)
  confidence = _messages.FloatField(3, variant=_messages.Variant.FLOAT)
  paragraphs = _messages.MessageField('GoogleCloudVisionV1p4beta1Paragraph', 4, repeated=True)
  property = _messages.MessageField('GoogleCloudVisionV1p4beta1TextAnnotationTextProperty', 5)


class GoogleCloudVisionV1p4beta1BoundingPoly(_messages.Message):
  r"""A bounding polygon for the detected image annotation.

  Fields:
    normalizedVertices: The bounding polygon normalized vertices.
    vertices: The bounding polygon vertices.
  """

  normalizedVertices = _messages.MessageField('GoogleCloudVisionV1p4beta1NormalizedVertex', 1, repeated=True)
  vertices = _messages.MessageField('GoogleCloudVisionV1p4beta1Vertex', 2, repeated=True)


class GoogleCloudVisionV1p4beta1Celebrity(_messages.Message):
  r"""A Celebrity is a group of Faces with an identity.

  Fields:
    description: The Celebrity's description.
    displayName: The Celebrity's display name.
    name: The resource name of the preloaded Celebrity. Has the format
      `builtin/{mid}`.
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  name = _messages.StringField(3)


class GoogleCloudVisionV1p4beta1ColorInfo(_messages.Message):
  r"""Color information consists of RGB channels, score, and the fraction of
  the image that the color occupies in the image.

  Fields:
    color: RGB components of the color.
    pixelFraction: The fraction of pixels the color occupies in the image.
      Value in range [0, 1].
    score: Image-specific score for this color. Value in range [0, 1].
  """

  color = _messages.MessageField('Color', 1)
  pixelFraction = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  score = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p4beta1CropHint(_messages.Message):
  r"""Single crop hint that is used to generate a new crop when serving an
  image.

  Fields:
    boundingPoly: The bounding polygon for the crop region. The coordinates of
      the bounding box are in the original image's scale.
    confidence: Confidence of this being a salient region. Range [0, 1].
    importanceFraction: Fraction of importance of this salient region with
      respect to the original image.
  """

  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p4beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  importanceFraction = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p4beta1CropHintsAnnotation(_messages.Message):
  r"""Set of crop hints that are used to generate new crops when serving
  images.

  Fields:
    cropHints: Crop hint results.
  """

  cropHints = _messages.MessageField('GoogleCloudVisionV1p4beta1CropHint', 1, repeated=True)


class GoogleCloudVisionV1p4beta1DominantColorsAnnotation(_messages.Message):
  r"""Set of dominant colors and their corresponding scores.

  Fields:
    colors: RGB color values with their score and pixel fraction.
  """

  colors = _messages.MessageField('GoogleCloudVisionV1p4beta1ColorInfo', 1, repeated=True)


class GoogleCloudVisionV1p4beta1EntityAnnotation(_messages.Message):
  r"""Set of detected entity features.

  Fields:
    boundingPoly: Image region to which this entity belongs. Not produced for
      `LABEL_DETECTION` features.
    confidence: **Deprecated. Use `score` instead.** The accuracy of the
      entity detection in an image. For example, for an image in which the
      "Eiffel Tower" entity is detected, this field represents the confidence
      that there is a tower in the query image. Range [0, 1].
    description: Entity textual description, expressed in its `locale`
      language.
    locale: The language code for the locale in which the entity textual
      `description` is expressed.
    locations: The location information for the detected entity. Multiple
      `LocationInfo` elements can be present because one location may indicate
      the location of the scene in the image, and another location may
      indicate the location of the place where the image was taken. Location
      information is usually present for landmarks.
    mid: Opaque entity ID. Some IDs may be available in [Google Knowledge
      Graph Search API](https://developers.google.com/knowledge-graph/).
    properties: Some entities may have optional user-supplied `Property`
      (name/value) fields, such a score or string that qualifies the entity.
    score: Overall score of the result. Range [0, 1].
    topicality: The relevancy of the ICA (Image Content Annotation) label to
      the image. For example, the relevancy of "tower" is likely higher to an
      image containing the detected "Eiffel Tower" than to an image containing
      a detected distant towering building, even though the confidence that
      there is a tower in each image may be the same. Range [0, 1].
  """

  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p4beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  description = _messages.StringField(3)
  locale = _messages.StringField(4)
  locations = _messages.MessageField('GoogleCloudVisionV1p4beta1LocationInfo', 5, repeated=True)
  mid = _messages.StringField(6)
  properties = _messages.MessageField('GoogleCloudVisionV1p4beta1Property', 7, repeated=True)
  score = _messages.FloatField(8, variant=_messages.Variant.FLOAT)
  topicality = _messages.FloatField(9, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p4beta1FaceAnnotation(_messages.Message):
  r"""A face annotation object contains the results of face detection.

  Enums:
    AngerLikelihoodValueValuesEnum: Anger likelihood.
    BlurredLikelihoodValueValuesEnum: Blurred likelihood.
    HeadwearLikelihoodValueValuesEnum: Headwear likelihood.
    JoyLikelihoodValueValuesEnum: Joy likelihood.
    SorrowLikelihoodValueValuesEnum: Sorrow likelihood.
    SurpriseLikelihoodValueValuesEnum: Surprise likelihood.
    UnderExposedLikelihoodValueValuesEnum: Under-exposed likelihood.

  Fields:
    angerLikelihood: Anger likelihood.
    blurredLikelihood: Blurred likelihood.
    boundingPoly: The bounding polygon around the face. The coordinates of the
      bounding box are in the original image's scale. The bounding box is
      computed to "frame" the face in accordance with human expectations. It
      is based on the landmarker results. Note that one or more x and/or y
      coordinates may not be generated in the `BoundingPoly` (the polygon will
      be unbounded) if only a partial face appears in the image to be
      annotated.
    detectionConfidence: Detection confidence. Range [0, 1].
    fdBoundingPoly: The `fd_bounding_poly` bounding polygon is tighter than
      the `boundingPoly`, and encloses only the skin part of the face.
      Typically, it is used to eliminate the face from any image analysis that
      detects the "amount of skin" visible in an image. It is not based on the
      landmarker results, only on the initial face detection, hence the fd
      (face detection) prefix.
    headwearLikelihood: Headwear likelihood.
    joyLikelihood: Joy likelihood.
    landmarkingConfidence: Face landmarking confidence. Range [0, 1].
    landmarks: Detected face landmarks.
    panAngle: Yaw angle, which indicates the leftward/rightward angle that the
      face is pointing relative to the vertical plane perpendicular to the
      image. Range [-180,180].
    recognitionResult: Additional recognition information. Only computed if
      image_context.face_recognition_params is provided, **and** a match is
      found to a Celebrity in the input CelebritySet. This field is sorted in
      order of decreasing confidence values.
    rollAngle: Roll angle, which indicates the amount of clockwise/anti-
      clockwise rotation of the face relative to the image vertical about the
      axis perpendicular to the face. Range [-180,180].
    sorrowLikelihood: Sorrow likelihood.
    surpriseLikelihood: Surprise likelihood.
    tiltAngle: Pitch angle, which indicates the upwards/downwards angle that
      the face is pointing relative to the image's horizontal plane. Range
      [-180,180].
    underExposedLikelihood: Under-exposed likelihood.
  """

  class AngerLikelihoodValueValuesEnum(_messages.Enum):
    r"""Anger likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class BlurredLikelihoodValueValuesEnum(_messages.Enum):
    r"""Blurred likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class HeadwearLikelihoodValueValuesEnum(_messages.Enum):
    r"""Headwear likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class JoyLikelihoodValueValuesEnum(_messages.Enum):
    r"""Joy likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class SorrowLikelihoodValueValuesEnum(_messages.Enum):
    r"""Sorrow likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class SurpriseLikelihoodValueValuesEnum(_messages.Enum):
    r"""Surprise likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class UnderExposedLikelihoodValueValuesEnum(_messages.Enum):
    r"""Under-exposed likelihood.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  angerLikelihood = _messages.EnumField('AngerLikelihoodValueValuesEnum', 1)
  blurredLikelihood = _messages.EnumField('BlurredLikelihoodValueValuesEnum', 2)
  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p4beta1BoundingPoly', 3)
  detectionConfidence = _messages.FloatField(4, variant=_messages.Variant.FLOAT)
  fdBoundingPoly = _messages.MessageField('GoogleCloudVisionV1p4beta1BoundingPoly', 5)
  headwearLikelihood = _messages.EnumField('HeadwearLikelihoodValueValuesEnum', 6)
  joyLikelihood = _messages.EnumField('JoyLikelihoodValueValuesEnum', 7)
  landmarkingConfidence = _messages.FloatField(8, variant=_messages.Variant.FLOAT)
  landmarks = _messages.MessageField('GoogleCloudVisionV1p4beta1FaceAnnotationLandmark', 9, repeated=True)
  panAngle = _messages.FloatField(10, variant=_messages.Variant.FLOAT)
  recognitionResult = _messages.MessageField('GoogleCloudVisionV1p4beta1FaceRecognitionResult', 11, repeated=True)
  rollAngle = _messages.FloatField(12, variant=_messages.Variant.FLOAT)
  sorrowLikelihood = _messages.EnumField('SorrowLikelihoodValueValuesEnum', 13)
  surpriseLikelihood = _messages.EnumField('SurpriseLikelihoodValueValuesEnum', 14)
  tiltAngle = _messages.FloatField(15, variant=_messages.Variant.FLOAT)
  underExposedLikelihood = _messages.EnumField('UnderExposedLikelihoodValueValuesEnum', 16)


class GoogleCloudVisionV1p4beta1FaceAnnotationLandmark(_messages.Message):
  r"""A face-specific landmark (for example, a face feature).

  Enums:
    TypeValueValuesEnum: Face landmark type.

  Fields:
    position: Face landmark position.
    type: Face landmark type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Face landmark type.

    Values:
      UNKNOWN_LANDMARK: Unknown face landmark detected. Should not be filled.
      LEFT_EYE: Left eye.
      RIGHT_EYE: Right eye.
      LEFT_OF_LEFT_EYEBROW: Left of left eyebrow.
      RIGHT_OF_LEFT_EYEBROW: Right of left eyebrow.
      LEFT_OF_RIGHT_EYEBROW: Left of right eyebrow.
      RIGHT_OF_RIGHT_EYEBROW: Right of right eyebrow.
      MIDPOINT_BETWEEN_EYES: Midpoint between eyes.
      NOSE_TIP: Nose tip.
      UPPER_LIP: Upper lip.
      LOWER_LIP: Lower lip.
      MOUTH_LEFT: Mouth left.
      MOUTH_RIGHT: Mouth right.
      MOUTH_CENTER: Mouth center.
      NOSE_BOTTOM_RIGHT: Nose, bottom right.
      NOSE_BOTTOM_LEFT: Nose, bottom left.
      NOSE_BOTTOM_CENTER: Nose, bottom center.
      LEFT_EYE_TOP_BOUNDARY: Left eye, top boundary.
      LEFT_EYE_RIGHT_CORNER: Left eye, right corner.
      LEFT_EYE_BOTTOM_BOUNDARY: Left eye, bottom boundary.
      LEFT_EYE_LEFT_CORNER: Left eye, left corner.
      RIGHT_EYE_TOP_BOUNDARY: Right eye, top boundary.
      RIGHT_EYE_RIGHT_CORNER: Right eye, right corner.
      RIGHT_EYE_BOTTOM_BOUNDARY: Right eye, bottom boundary.
      RIGHT_EYE_LEFT_CORNER: Right eye, left corner.
      LEFT_EYEBROW_UPPER_MIDPOINT: Left eyebrow, upper midpoint.
      RIGHT_EYEBROW_UPPER_MIDPOINT: Right eyebrow, upper midpoint.
      LEFT_EAR_TRAGION: Left ear tragion.
      RIGHT_EAR_TRAGION: Right ear tragion.
      LEFT_EYE_PUPIL: Left eye pupil.
      RIGHT_EYE_PUPIL: Right eye pupil.
      FOREHEAD_GLABELLA: Forehead glabella.
      CHIN_GNATHION: Chin gnathion.
      CHIN_LEFT_GONION: Chin left gonion.
      CHIN_RIGHT_GONION: Chin right gonion.
      LEFT_CHEEK_CENTER: Left cheek center.
      RIGHT_CHEEK_CENTER: Right cheek center.
    """
    UNKNOWN_LANDMARK = 0
    LEFT_EYE = 1
    RIGHT_EYE = 2
    LEFT_OF_LEFT_EYEBROW = 3
    RIGHT_OF_LEFT_EYEBROW = 4
    LEFT_OF_RIGHT_EYEBROW = 5
    RIGHT_OF_RIGHT_EYEBROW = 6
    MIDPOINT_BETWEEN_EYES = 7
    NOSE_TIP = 8
    UPPER_LIP = 9
    LOWER_LIP = 10
    MOUTH_LEFT = 11
    MOUTH_RIGHT = 12
    MOUTH_CENTER = 13
    NOSE_BOTTOM_RIGHT = 14
    NOSE_BOTTOM_LEFT = 15
    NOSE_BOTTOM_CENTER = 16
    LEFT_EYE_TOP_BOUNDARY = 17
    LEFT_EYE_RIGHT_CORNER = 18
    LEFT_EYE_BOTTOM_BOUNDARY = 19
    LEFT_EYE_LEFT_CORNER = 20
    RIGHT_EYE_TOP_BOUNDARY = 21
    RIGHT_EYE_RIGHT_CORNER = 22
    RIGHT_EYE_BOTTOM_BOUNDARY = 23
    RIGHT_EYE_LEFT_CORNER = 24
    LEFT_EYEBROW_UPPER_MIDPOINT = 25
    RIGHT_EYEBROW_UPPER_MIDPOINT = 26
    LEFT_EAR_TRAGION = 27
    RIGHT_EAR_TRAGION = 28
    LEFT_EYE_PUPIL = 29
    RIGHT_EYE_PUPIL = 30
    FOREHEAD_GLABELLA = 31
    CHIN_GNATHION = 32
    CHIN_LEFT_GONION = 33
    CHIN_RIGHT_GONION = 34
    LEFT_CHEEK_CENTER = 35
    RIGHT_CHEEK_CENTER = 36

  position = _messages.MessageField('GoogleCloudVisionV1p4beta1Position', 1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class GoogleCloudVisionV1p4beta1FaceRecognitionResult(_messages.Message):
  r"""Information about a face's identity.

  Fields:
    celebrity: The Celebrity that this face was matched to.
    confidence: Recognition confidence. Range [0, 1].
  """

  celebrity = _messages.MessageField('GoogleCloudVisionV1p4beta1Celebrity', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p4beta1GcsDestination(_messages.Message):
  r"""The Google Cloud Storage location where the output will be written to.

  Fields:
    uri: Google Cloud Storage URI prefix where the results will be stored.
      Results will be in JSON format and preceded by its corresponding input
      URI prefix. This field can either represent a gcs file prefix or gcs
      directory. In either case, the uri should be unique because in order to
      get all of the output files, you will need to do a wildcard gcs search
      on the uri prefix you provide. Examples: * File Prefix: gs://bucket-
      name/here/filenameprefix The output files will be created in
      gs://bucket-name/here/ and the names of the output files will begin with
      "filenameprefix". * Directory Prefix: gs://bucket-name/some/location/
      The output files will be created in gs://bucket-name/some/location/ and
      the names of the output files could be anything because there was no
      filename prefix specified. If multiple outputs, each response is still
      AnnotateFileResponse, each of which contains some subset of the full
      list of AnnotateImageResponse. Multiple outputs can happen if, for
      example, the output JSON is too large and overflows into multiple
      sharded files.
  """

  uri = _messages.StringField(1)


class GoogleCloudVisionV1p4beta1GcsSource(_messages.Message):
  r"""The Google Cloud Storage location where the input will be read from.

  Fields:
    uri: Google Cloud Storage URI for the input file. This must only be a
      Google Cloud Storage object. Wildcards are not currently supported.
  """

  uri = _messages.StringField(1)


class GoogleCloudVisionV1p4beta1ImageAnnotationContext(_messages.Message):
  r"""If an image was produced from a file (e.g. a PDF), this message gives
  information about the source of that image.

  Fields:
    pageNumber: If the file was a PDF or TIFF, this field gives the page
      number within the file used to produce the image.
    uri: The URI of the file used to produce the image.
  """

  pageNumber = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  uri = _messages.StringField(2)


class GoogleCloudVisionV1p4beta1ImageProperties(_messages.Message):
  r"""Stores image properties, such as dominant colors.

  Fields:
    dominantColors: If present, dominant colors completed successfully.
  """

  dominantColors = _messages.MessageField('GoogleCloudVisionV1p4beta1DominantColorsAnnotation', 1)


class GoogleCloudVisionV1p4beta1ImportProductSetsResponse(_messages.Message):
  r"""Response message for the `ImportProductSets` method. This message is
  returned by the google.longrunning.Operations.GetOperation method in the
  returned google.longrunning.Operation.response field.

  Fields:
    referenceImages: The list of reference_images that are imported
      successfully.
    statuses: The rpc status for each ImportProductSet request, including both
      successes and errors. The number of statuses here matches the number of
      lines in the csv file, and statuses[i] stores the success or failure
      status of processing the i-th line of the csv, starting from line 0.
  """

  referenceImages = _messages.MessageField('GoogleCloudVisionV1p4beta1ReferenceImage', 1, repeated=True)
  statuses = _messages.MessageField('Status', 2, repeated=True)


class GoogleCloudVisionV1p4beta1InputConfig(_messages.Message):
  r"""The desired input location and metadata.

  Fields:
    content: File content, represented as a stream of bytes. Note: As with all
      `bytes` fields, protobuffers use a pure binary representation, whereas
      JSON representations use base64. Currently, this field only works for
      BatchAnnotateFiles requests. It does not work for
      AsyncBatchAnnotateFiles requests.
    gcsSource: The Google Cloud Storage location to read the input from.
    mimeType: The type of the file. Currently only "application/pdf",
      "image/tiff" and "image/gif" are supported. Wildcards are not supported.
  """

  content = _messages.BytesField(1)
  gcsSource = _messages.MessageField('GoogleCloudVisionV1p4beta1GcsSource', 2)
  mimeType = _messages.StringField(3)


class GoogleCloudVisionV1p4beta1LocalizedObjectAnnotation(_messages.Message):
  r"""Set of detected objects with bounding boxes.

  Fields:
    boundingPoly: Image region to which this object belongs. This must be
      populated.
    languageCode: The BCP-47 language code, such as "en-US" or "sr-Latn". For
      more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
    mid: Object ID that should align with EntityAnnotation mid.
    name: Object name, expressed in its `language_code` language.
    score: Score of the result. Range [0, 1].
  """

  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p4beta1BoundingPoly', 1)
  languageCode = _messages.StringField(2)
  mid = _messages.StringField(3)
  name = _messages.StringField(4)
  score = _messages.FloatField(5, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p4beta1LocationInfo(_messages.Message):
  r"""Detected entity location information.

  Fields:
    latLng: lat/long location coordinates.
  """

  latLng = _messages.MessageField('LatLng', 1)


class GoogleCloudVisionV1p4beta1NormalizedVertex(_messages.Message):
  r"""A vertex represents a 2D point in the image. NOTE: the normalized vertex
  coordinates are relative to the original image and range from 0 to 1.

  Fields:
    x: X coordinate.
    y: Y coordinate.
  """

  x = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  y = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p4beta1OperationMetadata(_messages.Message):
  r"""Contains metadata for the BatchAnnotateImages operation.

  Enums:
    StateValueValuesEnum: Current state of the batch operation.

  Fields:
    createTime: The time when the batch request was received.
    state: Current state of the batch operation.
    updateTime: The time when the operation result was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Current state of the batch operation.

    Values:
      STATE_UNSPECIFIED: Invalid.
      CREATED: Request is received.
      RUNNING: Request is actively being processed.
      DONE: The batch processing is done.
      CANCELLED: The batch processing was cancelled.
    """
    STATE_UNSPECIFIED = 0
    CREATED = 1
    RUNNING = 2
    DONE = 3
    CANCELLED = 4

  createTime = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  updateTime = _messages.StringField(3)


class GoogleCloudVisionV1p4beta1OutputConfig(_messages.Message):
  r"""The desired output location and metadata.

  Fields:
    batchSize: The max number of response protos to put into each output JSON
      file on Google Cloud Storage. The valid range is [1, 100]. If not
      specified, the default value is 20. For example, for one pdf file with
      100 pages, 100 response protos will be generated. If `batch_size` = 20,
      then 5 json files each containing 20 response protos will be written
      under the prefix `gcs_destination`.`uri`. Currently, batch_size only
      applies to GcsDestination, with potential future support for other
      output configurations.
    gcsDestination: The Google Cloud Storage location to write the output(s)
      to.
  """

  batchSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  gcsDestination = _messages.MessageField('GoogleCloudVisionV1p4beta1GcsDestination', 2)


class GoogleCloudVisionV1p4beta1Page(_messages.Message):
  r"""Detected page from OCR.

  Fields:
    blocks: List of blocks of text, images etc on this page.
    confidence: Confidence of the OCR results on the page. Range [0, 1].
    height: Page height. For PDFs the unit is points. For images (including
      TIFFs) the unit is pixels.
    property: Additional information detected on the page.
    width: Page width. For PDFs the unit is points. For images (including
      TIFFs) the unit is pixels.
  """

  blocks = _messages.MessageField('GoogleCloudVisionV1p4beta1Block', 1, repeated=True)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  height = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  property = _messages.MessageField('GoogleCloudVisionV1p4beta1TextAnnotationTextProperty', 4)
  width = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class GoogleCloudVisionV1p4beta1Paragraph(_messages.Message):
  r"""Structural unit of text representing a number of words in certain order.

  Fields:
    boundingBox: The bounding box for the paragraph. The vertices are in the
      order of top-left, top-right, bottom-right, bottom-left. When a rotation
      of the bounding box is detected the rotation is represented as around
      the top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results for the paragraph. Range [0, 1].
    property: Additional information detected for the paragraph.
    words: List of all words in this paragraph.
  """

  boundingBox = _messages.MessageField('GoogleCloudVisionV1p4beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  property = _messages.MessageField('GoogleCloudVisionV1p4beta1TextAnnotationTextProperty', 3)
  words = _messages.MessageField('GoogleCloudVisionV1p4beta1Word', 4, repeated=True)


class GoogleCloudVisionV1p4beta1Position(_messages.Message):
  r"""A 3D position in the image, used primarily for Face detection landmarks.
  A valid Position must have both x and y coordinates. The position
  coordinates are in the same scale as the original image.

  Fields:
    x: X coordinate.
    y: Y coordinate.
    z: Z coordinate (or depth).
  """

  x = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  y = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  z = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p4beta1Product(_messages.Message):
  r"""A Product contains ReferenceImages.

  Fields:
    description: User-provided metadata to be stored with this product. Must
      be at most 4096 characters long.
    displayName: The user-provided name for this Product. Must not be empty.
      Must be at most 4096 characters long.
    name: The resource name of the product. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`. This field
      is ignored when creating a product.
    productCategory: Immutable. The category for the product identified by the
      reference image. This should be one of "homegoods-v2", "apparel-v2",
      "toys-v2", "packagedgoods-v1" or "general-v1". The legacy categories
      "homegoods", "apparel", and "toys" are still supported, but these should
      not be used for new products.
    productLabels: Key-value pairs that can be attached to a product. At query
      time, constraints can be specified based on the product_labels. Note
      that integer values can be provided as strings, e.g. "1199". Only
      strings with integer values can match a range-based restriction which is
      to be supported soon. Multiple values can be assigned to the same key.
      One product may have up to 500 product_labels. Notice that the total
      number of distinct product_labels over all products in one ProductSet
      cannot exceed 1M, otherwise the product search pipeline will refuse to
      work for that ProductSet.
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  name = _messages.StringField(3)
  productCategory = _messages.StringField(4)
  productLabels = _messages.MessageField('GoogleCloudVisionV1p4beta1ProductKeyValue', 5, repeated=True)


class GoogleCloudVisionV1p4beta1ProductKeyValue(_messages.Message):
  r"""A product label represented as a key-value pair.

  Fields:
    key: The key of the label attached to the product. Cannot be empty and
      cannot exceed 128 bytes.
    value: The value of the label attached to the product. Cannot be empty and
      cannot exceed 128 bytes.
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleCloudVisionV1p4beta1ProductSearchResults(_messages.Message):
  r"""Results for a product search request.

  Fields:
    indexTime: Timestamp of the index which provided these results. Products
      added to the product set and products removed from the product set after
      this time are not reflected in the current results.
    productGroupedResults: List of results grouped by products detected in the
      query image. Each entry corresponds to one bounding polygon in the query
      image, and contains the matching products specific to that region. There
      may be duplicate product matches in the union of all the per-product
      results.
    results: List of results, one for each product match.
  """

  indexTime = _messages.StringField(1)
  productGroupedResults = _messages.MessageField('GoogleCloudVisionV1p4beta1ProductSearchResultsGroupedResult', 2, repeated=True)
  results = _messages.MessageField('GoogleCloudVisionV1p4beta1ProductSearchResultsResult', 3, repeated=True)


class GoogleCloudVisionV1p4beta1ProductSearchResultsGroupedResult(_messages.Message):
  r"""Information about the products similar to a single product in a query
  image.

  Fields:
    boundingPoly: The bounding polygon around the product detected in the
      query image.
    objectAnnotations: List of generic predictions for the object in the
      bounding box.
    results: List of results, one for each product match.
  """

  boundingPoly = _messages.MessageField('GoogleCloudVisionV1p4beta1BoundingPoly', 1)
  objectAnnotations = _messages.MessageField('GoogleCloudVisionV1p4beta1ProductSearchResultsObjectAnnotation', 2, repeated=True)
  results = _messages.MessageField('GoogleCloudVisionV1p4beta1ProductSearchResultsResult', 3, repeated=True)


class GoogleCloudVisionV1p4beta1ProductSearchResultsObjectAnnotation(_messages.Message):
  r"""Prediction for what the object in the bounding box is.

  Fields:
    languageCode: The BCP-47 language code, such as "en-US" or "sr-Latn". For
      more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
    mid: Object ID that should align with EntityAnnotation mid.
    name: Object name, expressed in its `language_code` language.
    score: Score of the result. Range [0, 1].
  """

  languageCode = _messages.StringField(1)
  mid = _messages.StringField(2)
  name = _messages.StringField(3)
  score = _messages.FloatField(4, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p4beta1ProductSearchResultsResult(_messages.Message):
  r"""Information about a product.

  Fields:
    image: The resource name of the image from the product that is the closest
      match to the query.
    product: The Product.
    score: A confidence level on the match, ranging from 0 (no confidence) to
      1 (full confidence).
  """

  image = _messages.StringField(1)
  product = _messages.MessageField('GoogleCloudVisionV1p4beta1Product', 2)
  score = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p4beta1Property(_messages.Message):
  r"""A `Property` consists of a user-supplied name/value pair.

  Fields:
    name: Name of the property.
    uint64Value: Value of numeric properties.
    value: Value of the property.
  """

  name = _messages.StringField(1)
  uint64Value = _messages.IntegerField(2, variant=_messages.Variant.UINT64)
  value = _messages.StringField(3)


class GoogleCloudVisionV1p4beta1ReferenceImage(_messages.Message):
  r"""A `ReferenceImage` represents a product image and its associated
  metadata, such as bounding boxes.

  Fields:
    boundingPolys: Optional. Bounding polygons around the areas of interest in
      the reference image. If this field is empty, the system will try to
      detect regions of interest. At most 10 bounding polygons will be used.
      The provided shape is converted into a non-rotated rectangle. Once
      converted, the small edge of the rectangle must be greater than or equal
      to 300 pixels. The aspect ratio must be 1:4 or less (i.e. 1:3 is ok; 1:5
      is not).
    name: The resource name of the reference image. Format is: `projects/PROJE
      CT_ID/locations/LOC_ID/products/PRODUCT_ID/referenceImages/IMAGE_ID`.
      This field is ignored when creating a reference image.
    uri: Required. The Google Cloud Storage URI of the reference image. The
      URI must start with `gs://`.
  """

  boundingPolys = _messages.MessageField('GoogleCloudVisionV1p4beta1BoundingPoly', 1, repeated=True)
  name = _messages.StringField(2)
  uri = _messages.StringField(3)


class GoogleCloudVisionV1p4beta1SafeSearchAnnotation(_messages.Message):
  r"""Set of features pertaining to the image, computed by computer vision
  methods over safe-search verticals (for example, adult, spoof, medical,
  violence).

  Enums:
    AdultValueValuesEnum: Represents the adult content likelihood for the
      image. Adult content may contain elements such as nudity, pornographic
      images or cartoons, or sexual activities.
    MedicalValueValuesEnum: Likelihood that this is a medical image.
    RacyValueValuesEnum: Likelihood that the request image contains racy
      content. Racy content may include (but is not limited to) skimpy or
      sheer clothing, strategically covered nudity, lewd or provocative poses,
      or close-ups of sensitive body areas.
    SpoofValueValuesEnum: Spoof likelihood. The likelihood that an
      modification was made to the image's canonical version to make it appear
      funny or offensive.
    ViolenceValueValuesEnum: Likelihood that this image contains violent
      content.

  Fields:
    adult: Represents the adult content likelihood for the image. Adult
      content may contain elements such as nudity, pornographic images or
      cartoons, or sexual activities.
    medical: Likelihood that this is a medical image.
    racy: Likelihood that the request image contains racy content. Racy
      content may include (but is not limited to) skimpy or sheer clothing,
      strategically covered nudity, lewd or provocative poses, or close-ups of
      sensitive body areas.
    spoof: Spoof likelihood. The likelihood that an modification was made to
      the image's canonical version to make it appear funny or offensive.
    violence: Likelihood that this image contains violent content.
  """

  class AdultValueValuesEnum(_messages.Enum):
    r"""Represents the adult content likelihood for the image. Adult content
    may contain elements such as nudity, pornographic images or cartoons, or
    sexual activities.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class MedicalValueValuesEnum(_messages.Enum):
    r"""Likelihood that this is a medical image.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class RacyValueValuesEnum(_messages.Enum):
    r"""Likelihood that the request image contains racy content. Racy content
    may include (but is not limited to) skimpy or sheer clothing,
    strategically covered nudity, lewd or provocative poses, or close-ups of
    sensitive body areas.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class SpoofValueValuesEnum(_messages.Enum):
    r"""Spoof likelihood. The likelihood that an modification was made to the
    image's canonical version to make it appear funny or offensive.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class ViolenceValueValuesEnum(_messages.Enum):
    r"""Likelihood that this image contains violent content.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  adult = _messages.EnumField('AdultValueValuesEnum', 1)
  medical = _messages.EnumField('MedicalValueValuesEnum', 2)
  racy = _messages.EnumField('RacyValueValuesEnum', 3)
  spoof = _messages.EnumField('SpoofValueValuesEnum', 4)
  violence = _messages.EnumField('ViolenceValueValuesEnum', 5)


class GoogleCloudVisionV1p4beta1Symbol(_messages.Message):
  r"""A single symbol representation.

  Fields:
    boundingBox: The bounding box for the symbol. The vertices are in the
      order of top-left, top-right, bottom-right, bottom-left. When a rotation
      of the bounding box is detected the rotation is represented as around
      the top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results for the symbol. Range [0, 1].
    property: Additional information detected for the symbol.
    text: The actual UTF-8 representation of the symbol.
  """

  boundingBox = _messages.MessageField('GoogleCloudVisionV1p4beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  property = _messages.MessageField('GoogleCloudVisionV1p4beta1TextAnnotationTextProperty', 3)
  text = _messages.StringField(4)


class GoogleCloudVisionV1p4beta1TextAnnotation(_messages.Message):
  r"""TextAnnotation contains a structured representation of OCR extracted
  text. The hierarchy of an OCR extracted text structure is like this:
  TextAnnotation -> Page -> Block -> Paragraph -> Word -> Symbol Each
  structural component, starting from Page, may further have their own
  properties. Properties describe detected languages, breaks etc.. Please
  refer to the TextAnnotation.TextProperty message definition below for more
  detail.

  Fields:
    pages: List of pages detected by OCR.
    text: UTF-8 text detected on the pages.
  """

  pages = _messages.MessageField('GoogleCloudVisionV1p4beta1Page', 1, repeated=True)
  text = _messages.StringField(2)


class GoogleCloudVisionV1p4beta1TextAnnotationDetectedBreak(_messages.Message):
  r"""Detected start or end of a structural component.

  Enums:
    TypeValueValuesEnum: Detected break type.

  Fields:
    isPrefix: True if break prepends the element.
    type: Detected break type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Detected break type.

    Values:
      UNKNOWN: Unknown break label type.
      SPACE: Regular space.
      SURE_SPACE: Sure space (very wide).
      EOL_SURE_SPACE: Line-wrapping break.
      HYPHEN: End-line hyphen that is not present in text; does not co-occur
        with `SPACE`, `LEADER_SPACE`, or `LINE_BREAK`.
      LINE_BREAK: Line break that ends a paragraph.
    """
    UNKNOWN = 0
    SPACE = 1
    SURE_SPACE = 2
    EOL_SURE_SPACE = 3
    HYPHEN = 4
    LINE_BREAK = 5

  isPrefix = _messages.BooleanField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class GoogleCloudVisionV1p4beta1TextAnnotationDetectedLanguage(_messages.Message):
  r"""Detected language for a structural component.

  Fields:
    confidence: Confidence of detected language. Range [0, 1].
    languageCode: The BCP-47 language code, such as "en-US" or "sr-Latn". For
      more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
  """

  confidence = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  languageCode = _messages.StringField(2)


class GoogleCloudVisionV1p4beta1TextAnnotationTextProperty(_messages.Message):
  r"""Additional information detected on the structural component.

  Fields:
    detectedBreak: Detected start or end of a text segment.
    detectedLanguages: A list of detected languages together with confidence.
  """

  detectedBreak = _messages.MessageField('GoogleCloudVisionV1p4beta1TextAnnotationDetectedBreak', 1)
  detectedLanguages = _messages.MessageField('GoogleCloudVisionV1p4beta1TextAnnotationDetectedLanguage', 2, repeated=True)


class GoogleCloudVisionV1p4beta1Vertex(_messages.Message):
  r"""A vertex represents a 2D point in the image. NOTE: the vertex
  coordinates are in the same scale as the original image.

  Fields:
    x: X coordinate.
    y: Y coordinate.
  """

  x = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  y = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudVisionV1p4beta1WebDetection(_messages.Message):
  r"""Relevant information for the image from the Internet.

  Fields:
    bestGuessLabels: The service's best guess as to the topic of the request
      image. Inferred from similar images on the open web.
    fullMatchingImages: Fully matching images from the Internet. Can include
      resized copies of the query image.
    pagesWithMatchingImages: Web pages containing the matching images from the
      Internet.
    partialMatchingImages: Partial matching images from the Internet. Those
      images are similar enough to share some key-point features. For example
      an original image will likely have partial matching for its crops.
    visuallySimilarImages: The visually similar image results.
    webEntities: Deduced entities from similar images on the Internet.
  """

  bestGuessLabels = _messages.MessageField('GoogleCloudVisionV1p4beta1WebDetectionWebLabel', 1, repeated=True)
  fullMatchingImages = _messages.MessageField('GoogleCloudVisionV1p4beta1WebDetectionWebImage', 2, repeated=True)
  pagesWithMatchingImages = _messages.MessageField('GoogleCloudVisionV1p4beta1WebDetectionWebPage', 3, repeated=True)
  partialMatchingImages = _messages.MessageField('GoogleCloudVisionV1p4beta1WebDetectionWebImage', 4, repeated=True)
  visuallySimilarImages = _messages.MessageField('GoogleCloudVisionV1p4beta1WebDetectionWebImage', 5, repeated=True)
  webEntities = _messages.MessageField('GoogleCloudVisionV1p4beta1WebDetectionWebEntity', 6, repeated=True)


class GoogleCloudVisionV1p4beta1WebDetectionWebEntity(_messages.Message):
  r"""Entity deduced from similar images on the Internet.

  Fields:
    description: Canonical description of the entity, in English.
    entityId: Opaque entity ID.
    score: Overall relevancy score for the entity. Not normalized and not
      comparable across different image queries.
  """

  description = _messages.StringField(1)
  entityId = _messages.StringField(2)
  score = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class GoogleCloudVisionV1p4beta1WebDetectionWebImage(_messages.Message):
  r"""Metadata for online images.

  Fields:
    score: (Deprecated) Overall relevancy score for the image.
    url: The result image URL.
  """

  score = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  url = _messages.StringField(2)


class GoogleCloudVisionV1p4beta1WebDetectionWebLabel(_messages.Message):
  r"""Label to provide extra metadata for the web detection.

  Fields:
    label: Label for extra metadata.
    languageCode: The BCP-47 language code for `label`, such as "en-US" or
      "sr-Latn". For more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
  """

  label = _messages.StringField(1)
  languageCode = _messages.StringField(2)


class GoogleCloudVisionV1p4beta1WebDetectionWebPage(_messages.Message):
  r"""Metadata for web pages.

  Fields:
    fullMatchingImages: Fully matching images on the page. Can include resized
      copies of the query image.
    pageTitle: Title for the web page, may contain HTML markups.
    partialMatchingImages: Partial matching images on the page. Those images
      are similar enough to share some key-point features. For example an
      original image will likely have partial matching for its crops.
    score: (Deprecated) Overall relevancy score for the web page.
    url: The result web page URL.
  """

  fullMatchingImages = _messages.MessageField('GoogleCloudVisionV1p4beta1WebDetectionWebImage', 1, repeated=True)
  pageTitle = _messages.StringField(2)
  partialMatchingImages = _messages.MessageField('GoogleCloudVisionV1p4beta1WebDetectionWebImage', 3, repeated=True)
  score = _messages.FloatField(4, variant=_messages.Variant.FLOAT)
  url = _messages.StringField(5)


class GoogleCloudVisionV1p4beta1Word(_messages.Message):
  r"""A word representation.

  Fields:
    boundingBox: The bounding box for the word. The vertices are in the order
      of top-left, top-right, bottom-right, bottom-left. When a rotation of
      the bounding box is detected the rotation is represented as around the
      top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results for the word. Range [0, 1].
    property: Additional information detected for the word.
    symbols: List of symbols in the word. The order of the symbols follows the
      natural reading order.
  """

  boundingBox = _messages.MessageField('GoogleCloudVisionV1p4beta1BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  property = _messages.MessageField('GoogleCloudVisionV1p4beta1TextAnnotationTextProperty', 3)
  symbols = _messages.MessageField('GoogleCloudVisionV1p4beta1Symbol', 4, repeated=True)


class GroupedResult(_messages.Message):
  r"""Information about the products similar to a single product in a query
  image.

  Fields:
    boundingPoly: The bounding polygon around the product detected in the
      query image.
    objectAnnotations: List of generic predictions for the object in the
      bounding box.
    results: List of results, one for each product match.
  """

  boundingPoly = _messages.MessageField('BoundingPoly', 1)
  objectAnnotations = _messages.MessageField('ObjectAnnotation', 2, repeated=True)
  results = _messages.MessageField('Result', 3, repeated=True)


class Image(_messages.Message):
  r"""Client image to perform Google Cloud Vision API tasks over.

  Fields:
    content: Image content, represented as a stream of bytes. Note: As with
      all `bytes` fields, protobuffers use a pure binary representation,
      whereas JSON representations use base64. Currently, this field only
      works for BatchAnnotateImages requests. It does not work for
      AsyncBatchAnnotateImages requests.
    source: Google Cloud Storage image location, or publicly-accessible image
      URL. If both `content` and `source` are provided for an image, `content`
      takes precedence and is used to perform the image annotation request.
  """

  content = _messages.BytesField(1)
  source = _messages.MessageField('ImageSource', 2)


class ImageAnnotationContext(_messages.Message):
  r"""If an image was produced from a file (e.g. a PDF), this message gives
  information about the source of that image.

  Fields:
    pageNumber: If the file was a PDF or TIFF, this field gives the page
      number within the file used to produce the image.
    uri: The URI of the file used to produce the image.
  """

  pageNumber = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  uri = _messages.StringField(2)


class ImageContext(_messages.Message):
  r"""Image context and/or feature-specific parameters.

  Fields:
    cropHintsParams: Parameters for crop hints annotation request.
    languageHints: List of languages to use for TEXT_DETECTION. In most cases,
      an empty value yields the best results since it enables automatic
      language detection. For languages based on the Latin alphabet, setting
      `language_hints` is not needed. In rare cases, when the language of the
      text in the image is known, setting a hint will help get better results
      (although it will be a significant hindrance if the hint is wrong). Text
      detection returns an error if one or more of the specified languages is
      not one of the [supported
      languages](https://cloud.google.com/vision/docs/languages).
    latLongRect: Not used.
    productSearchParams: Parameters for product search.
    textDetectionParams: Parameters for text detection and document text
      detection.
    webDetectionParams: Parameters for web detection.
  """

  cropHintsParams = _messages.MessageField('CropHintsParams', 1)
  languageHints = _messages.StringField(2, repeated=True)
  latLongRect = _messages.MessageField('LatLongRect', 3)
  productSearchParams = _messages.MessageField('ProductSearchParams', 4)
  textDetectionParams = _messages.MessageField('TextDetectionParams', 5)
  webDetectionParams = _messages.MessageField('WebDetectionParams', 6)


class ImageProperties(_messages.Message):
  r"""Stores image properties, such as dominant colors.

  Fields:
    dominantColors: If present, dominant colors completed successfully.
  """

  dominantColors = _messages.MessageField('DominantColorsAnnotation', 1)


class ImageSource(_messages.Message):
  r"""External image source (Google Cloud Storage or web URL image location).

  Fields:
    gcsImageUri: **Use `image_uri` instead.** The Google Cloud Storage URI of
      the form `gs://bucket_name/object_name`. Object versioning is not
      supported. See [Google Cloud Storage Request
      URIs](https://cloud.google.com/storage/docs/reference-uris) for more
      info.
    imageUri: The URI of the source image. Can be either: 1. A Google Cloud
      Storage URI of the form `gs://bucket_name/object_name`. Object
      versioning is not supported. See [Google Cloud Storage Request
      URIs](https://cloud.google.com/storage/docs/reference-uris) for more
      info. 2. A publicly-accessible image HTTP/HTTPS URL. When fetching
      images from HTTP/HTTPS URLs, Google cannot guarantee that the request
      will be completed. Your request may fail if the specified host denies
      the request (e.g. due to request throttling or DOS prevention), or if
      Google throttles requests to the site for abuse prevention. You should
      not depend on externally-hosted images for production applications. When
      both `gcs_image_uri` and `image_uri` are specified, `image_uri` takes
      precedence.
  """

  gcsImageUri = _messages.StringField(1)
  imageUri = _messages.StringField(2)


class ImportProductSetsGcsSource(_messages.Message):
  r"""The Google Cloud Storage location for a csv file which preserves a list
  of ImportProductSetRequests in each line.

  Fields:
    csvFileUri: The Google Cloud Storage URI of the input csv file. The URI
      must start with `gs://`. The format of the input csv file should be one
      image per line. In each line, there are 8 columns. 1. image-uri 2.
      image-id 3. product-set-id 4. product-id 5. product-category 6. product-
      display-name 7. labels 8. bounding-poly The `image-uri`, `product-set-
      id`, `product-id`, and `product-category` columns are required. All
      other columns are optional. If the `ProductSet` or `Product` specified
      by the `product-set-id` and `product-id` values does not exist, then the
      system will create a new `ProductSet` or `Product` for the image. In
      this case, the `product-display-name` column refers to display_name, the
      `product-category` column refers to product_category, and the `labels`
      column refers to product_labels. The `image-id` column is optional but
      must be unique if provided. If it is empty, the system will
      automatically assign a unique id to the image. The `product-display-
      name` column is optional. If it is empty, the system sets the
      display_name field for the product to a space (" "). You can update the
      `display_name` later by using the API. If a `Product` with the specified
      `product-id` already exists, then the system ignores the `product-
      display-name`, `product-category`, and `labels` columns. The `labels`
      column (optional) is a line containing a list of comma-separated key-
      value pairs, in the following format:
      "key_1=value_1,key_2=value_2,...,key_n=value_n" The `bounding-poly`
      column (optional) identifies one region of interest from the image in
      the same manner as `CreateReferenceImage`. If you do not specify the
      `bounding-poly` column, then the system will try to detect regions of
      interest automatically. At most one `bounding-poly` column is allowed
      per line. If the image contains multiple regions of interest, add a line
      to the CSV file that includes the same product information, and the
      `bounding-poly` values for each region of interest. The `bounding-poly`
      column must contain an even number of comma-separated numbers, in the
      format "p1_x,p1_y,p2_x,p2_y,...,pn_x,pn_y". Use non-negative integers
      for absolute bounding polygons, and float values in [0, 1] for
      normalized bounding polygons. The system will resize the image if the
      image resolution is too large to process (larger than 20MP).
  """

  csvFileUri = _messages.StringField(1)


class ImportProductSetsInputConfig(_messages.Message):
  r"""The input content for the `ImportProductSets` method.

  Fields:
    gcsSource: The Google Cloud Storage location for a csv file which
      preserves a list of ImportProductSetRequests in each line.
  """

  gcsSource = _messages.MessageField('ImportProductSetsGcsSource', 1)


class ImportProductSetsRequest(_messages.Message):
  r"""Request message for the `ImportProductSets` method.

  Fields:
    inputConfig: Required. The input content for the list of requests.
  """

  inputConfig = _messages.MessageField('ImportProductSetsInputConfig', 1)


class ImportProductSetsResponse(_messages.Message):
  r"""Response message for the `ImportProductSets` method. This message is
  returned by the google.longrunning.Operations.GetOperation method in the
  returned google.longrunning.Operation.response field.

  Fields:
    referenceImages: The list of reference_images that are imported
      successfully.
    statuses: The rpc status for each ImportProductSet request, including both
      successes and errors. The number of statuses here matches the number of
      lines in the csv file, and statuses[i] stores the success or failure
      status of processing the i-th line of the csv, starting from line 0.
  """

  referenceImages = _messages.MessageField('ReferenceImage', 1, repeated=True)
  statuses = _messages.MessageField('Status', 2, repeated=True)


class InputConfig(_messages.Message):
  r"""The desired input location and metadata.

  Fields:
    content: File content, represented as a stream of bytes. Note: As with all
      `bytes` fields, protobuffers use a pure binary representation, whereas
      JSON representations use base64. Currently, this field only works for
      BatchAnnotateFiles requests. It does not work for
      AsyncBatchAnnotateFiles requests.
    gcsSource: The Google Cloud Storage location to read the input from.
    mimeType: The type of the file. Currently only "application/pdf",
      "image/tiff" and "image/gif" are supported. Wildcards are not supported.
  """

  content = _messages.BytesField(1)
  gcsSource = _messages.MessageField('GcsSource', 2)
  mimeType = _messages.StringField(3)


class KeyValue(_messages.Message):
  r"""A product label represented as a key-value pair.

  Fields:
    key: The key of the label attached to the product. Cannot be empty and
      cannot exceed 128 bytes.
    value: The value of the label attached to the product. Cannot be empty and
      cannot exceed 128 bytes.
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class Landmark(_messages.Message):
  r"""A face-specific landmark (for example, a face feature).

  Enums:
    TypeValueValuesEnum: Face landmark type.

  Fields:
    position: Face landmark position.
    type: Face landmark type.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Face landmark type.

    Values:
      UNKNOWN_LANDMARK: Unknown face landmark detected. Should not be filled.
      LEFT_EYE: Left eye.
      RIGHT_EYE: Right eye.
      LEFT_OF_LEFT_EYEBROW: Left of left eyebrow.
      RIGHT_OF_LEFT_EYEBROW: Right of left eyebrow.
      LEFT_OF_RIGHT_EYEBROW: Left of right eyebrow.
      RIGHT_OF_RIGHT_EYEBROW: Right of right eyebrow.
      MIDPOINT_BETWEEN_EYES: Midpoint between eyes.
      NOSE_TIP: Nose tip.
      UPPER_LIP: Upper lip.
      LOWER_LIP: Lower lip.
      MOUTH_LEFT: Mouth left.
      MOUTH_RIGHT: Mouth right.
      MOUTH_CENTER: Mouth center.
      NOSE_BOTTOM_RIGHT: Nose, bottom right.
      NOSE_BOTTOM_LEFT: Nose, bottom left.
      NOSE_BOTTOM_CENTER: Nose, bottom center.
      LEFT_EYE_TOP_BOUNDARY: Left eye, top boundary.
      LEFT_EYE_RIGHT_CORNER: Left eye, right corner.
      LEFT_EYE_BOTTOM_BOUNDARY: Left eye, bottom boundary.
      LEFT_EYE_LEFT_CORNER: Left eye, left corner.
      RIGHT_EYE_TOP_BOUNDARY: Right eye, top boundary.
      RIGHT_EYE_RIGHT_CORNER: Right eye, right corner.
      RIGHT_EYE_BOTTOM_BOUNDARY: Right eye, bottom boundary.
      RIGHT_EYE_LEFT_CORNER: Right eye, left corner.
      LEFT_EYEBROW_UPPER_MIDPOINT: Left eyebrow, upper midpoint.
      RIGHT_EYEBROW_UPPER_MIDPOINT: Right eyebrow, upper midpoint.
      LEFT_EAR_TRAGION: Left ear tragion.
      RIGHT_EAR_TRAGION: Right ear tragion.
      LEFT_EYE_PUPIL: Left eye pupil.
      RIGHT_EYE_PUPIL: Right eye pupil.
      FOREHEAD_GLABELLA: Forehead glabella.
      CHIN_GNATHION: Chin gnathion.
      CHIN_LEFT_GONION: Chin left gonion.
      CHIN_RIGHT_GONION: Chin right gonion.
      LEFT_CHEEK_CENTER: Left cheek center.
      RIGHT_CHEEK_CENTER: Right cheek center.
    """
    UNKNOWN_LANDMARK = 0
    LEFT_EYE = 1
    RIGHT_EYE = 2
    LEFT_OF_LEFT_EYEBROW = 3
    RIGHT_OF_LEFT_EYEBROW = 4
    LEFT_OF_RIGHT_EYEBROW = 5
    RIGHT_OF_RIGHT_EYEBROW = 6
    MIDPOINT_BETWEEN_EYES = 7
    NOSE_TIP = 8
    UPPER_LIP = 9
    LOWER_LIP = 10
    MOUTH_LEFT = 11
    MOUTH_RIGHT = 12
    MOUTH_CENTER = 13
    NOSE_BOTTOM_RIGHT = 14
    NOSE_BOTTOM_LEFT = 15
    NOSE_BOTTOM_CENTER = 16
    LEFT_EYE_TOP_BOUNDARY = 17
    LEFT_EYE_RIGHT_CORNER = 18
    LEFT_EYE_BOTTOM_BOUNDARY = 19
    LEFT_EYE_LEFT_CORNER = 20
    RIGHT_EYE_TOP_BOUNDARY = 21
    RIGHT_EYE_RIGHT_CORNER = 22
    RIGHT_EYE_BOTTOM_BOUNDARY = 23
    RIGHT_EYE_LEFT_CORNER = 24
    LEFT_EYEBROW_UPPER_MIDPOINT = 25
    RIGHT_EYEBROW_UPPER_MIDPOINT = 26
    LEFT_EAR_TRAGION = 27
    RIGHT_EAR_TRAGION = 28
    LEFT_EYE_PUPIL = 29
    RIGHT_EYE_PUPIL = 30
    FOREHEAD_GLABELLA = 31
    CHIN_GNATHION = 32
    CHIN_LEFT_GONION = 33
    CHIN_RIGHT_GONION = 34
    LEFT_CHEEK_CENTER = 35
    RIGHT_CHEEK_CENTER = 36

  position = _messages.MessageField('Position', 1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class LatLng(_messages.Message):
  r"""An object that represents a latitude/longitude pair. This is expressed
  as a pair of doubles to represent degrees latitude and degrees longitude.
  Unless specified otherwise, this object must conform to the WGS84 standard.
  Values must be within normalized ranges.

  Fields:
    latitude: The latitude in degrees. It must be in the range [-90.0, +90.0].
    longitude: The longitude in degrees. It must be in the range [-180.0,
      +180.0].
  """

  latitude = _messages.FloatField(1)
  longitude = _messages.FloatField(2)


class LatLongRect(_messages.Message):
  r"""Rectangle determined by min and max `LatLng` pairs.

  Fields:
    maxLatLng: Max lat/long pair.
    minLatLng: Min lat/long pair.
  """

  maxLatLng = _messages.MessageField('LatLng', 1)
  minLatLng = _messages.MessageField('LatLng', 2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListProductSetsResponse(_messages.Message):
  r"""Response message for the `ListProductSets` method.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    productSets: List of ProductSets.
  """

  nextPageToken = _messages.StringField(1)
  productSets = _messages.MessageField('ProductSet', 2, repeated=True)


class ListProductsInProductSetResponse(_messages.Message):
  r"""Response message for the `ListProductsInProductSet` method.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    products: The list of Products.
  """

  nextPageToken = _messages.StringField(1)
  products = _messages.MessageField('Product', 2, repeated=True)


class ListProductsResponse(_messages.Message):
  r"""Response message for the `ListProducts` method.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    products: List of products.
  """

  nextPageToken = _messages.StringField(1)
  products = _messages.MessageField('Product', 2, repeated=True)


class ListReferenceImagesResponse(_messages.Message):
  r"""Response message for the `ListReferenceImages` method.

  Fields:
    nextPageToken: The next_page_token returned from a previous List request,
      if any.
    pageSize: The maximum number of items to return. Default 10, maximum 100.
    referenceImages: The list of reference images.
  """

  nextPageToken = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  referenceImages = _messages.MessageField('ReferenceImage', 3, repeated=True)


class LocalizedObjectAnnotation(_messages.Message):
  r"""Set of detected objects with bounding boxes.

  Fields:
    boundingPoly: Image region to which this object belongs. This must be
      populated.
    languageCode: The BCP-47 language code, such as "en-US" or "sr-Latn". For
      more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
    mid: Object ID that should align with EntityAnnotation mid.
    name: Object name, expressed in its `language_code` language.
    score: Score of the result. Range [0, 1].
  """

  boundingPoly = _messages.MessageField('BoundingPoly', 1)
  languageCode = _messages.StringField(2)
  mid = _messages.StringField(3)
  name = _messages.StringField(4)
  score = _messages.FloatField(5, variant=_messages.Variant.FLOAT)


class LocationInfo(_messages.Message):
  r"""Detected entity location information.

  Fields:
    latLng: lat/long location coordinates.
  """

  latLng = _messages.MessageField('LatLng', 1)


class NormalizedVertex(_messages.Message):
  r"""A vertex represents a 2D point in the image. NOTE: the normalized vertex
  coordinates are relative to the original image and range from 0 to 1.

  Fields:
    x: X coordinate.
    y: Y coordinate.
  """

  x = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  y = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class ObjectAnnotation(_messages.Message):
  r"""Prediction for what the object in the bounding box is.

  Fields:
    languageCode: The BCP-47 language code, such as "en-US" or "sr-Latn". For
      more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
    mid: Object ID that should align with EntityAnnotation mid.
    name: Object name, expressed in its `language_code` language.
    score: Score of the result. Range [0, 1].
  """

  languageCode = _messages.StringField(1)
  mid = _messages.StringField(2)
  name = _messages.StringField(3)
  score = _messages.FloatField(4, variant=_messages.Variant.FLOAT)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Contains metadata for the BatchAnnotateImages operation.

  Enums:
    StateValueValuesEnum: Current state of the batch operation.

  Fields:
    createTime: The time when the batch request was received.
    state: Current state of the batch operation.
    updateTime: The time when the operation result was last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Current state of the batch operation.

    Values:
      STATE_UNSPECIFIED: Invalid.
      CREATED: Request is received.
      RUNNING: Request is actively being processed.
      DONE: The batch processing is done.
      CANCELLED: The batch processing was cancelled.
    """
    STATE_UNSPECIFIED = 0
    CREATED = 1
    RUNNING = 2
    DONE = 3
    CANCELLED = 4

  createTime = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  updateTime = _messages.StringField(3)


class OutputConfig(_messages.Message):
  r"""The desired output location and metadata.

  Fields:
    batchSize: The max number of response protos to put into each output JSON
      file on Google Cloud Storage. The valid range is [1, 100]. If not
      specified, the default value is 20. For example, for one pdf file with
      100 pages, 100 response protos will be generated. If `batch_size` = 20,
      then 5 json files each containing 20 response protos will be written
      under the prefix `gcs_destination`.`uri`. Currently, batch_size only
      applies to GcsDestination, with potential future support for other
      output configurations.
    gcsDestination: The Google Cloud Storage location to write the output(s)
      to.
  """

  batchSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  gcsDestination = _messages.MessageField('GcsDestination', 2)


class Page(_messages.Message):
  r"""Detected page from OCR.

  Fields:
    blocks: List of blocks of text, images etc on this page.
    confidence: Confidence of the OCR results on the page. Range [0, 1].
    height: Page height. For PDFs the unit is points. For images (including
      TIFFs) the unit is pixels.
    property: Additional information detected on the page.
    width: Page width. For PDFs the unit is points. For images (including
      TIFFs) the unit is pixels.
  """

  blocks = _messages.MessageField('Block', 1, repeated=True)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  height = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  property = _messages.MessageField('TextProperty', 4)
  width = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class Paragraph(_messages.Message):
  r"""Structural unit of text representing a number of words in certain order.

  Fields:
    boundingBox: The bounding box for the paragraph. The vertices are in the
      order of top-left, top-right, bottom-right, bottom-left. When a rotation
      of the bounding box is detected the rotation is represented as around
      the top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results for the paragraph. Range [0, 1].
    property: Additional information detected for the paragraph.
    words: List of all words in this paragraph.
  """

  boundingBox = _messages.MessageField('BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  property = _messages.MessageField('TextProperty', 3)
  words = _messages.MessageField('Word', 4, repeated=True)


class Position(_messages.Message):
  r"""A 3D position in the image, used primarily for Face detection landmarks.
  A valid Position must have both x and y coordinates. The position
  coordinates are in the same scale as the original image.

  Fields:
    x: X coordinate.
    y: Y coordinate.
    z: Z coordinate (or depth).
  """

  x = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  y = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  z = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class Product(_messages.Message):
  r"""A Product contains ReferenceImages.

  Fields:
    description: User-provided metadata to be stored with this product. Must
      be at most 4096 characters long.
    displayName: The user-provided name for this Product. Must not be empty.
      Must be at most 4096 characters long.
    name: The resource name of the product. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`. This field
      is ignored when creating a product.
    productCategory: Immutable. The category for the product identified by the
      reference image. This should be one of "homegoods-v2", "apparel-v2",
      "toys-v2", "packagedgoods-v1" or "general-v1". The legacy categories
      "homegoods", "apparel", and "toys" are still supported, but these should
      not be used for new products.
    productLabels: Key-value pairs that can be attached to a product. At query
      time, constraints can be specified based on the product_labels. Note
      that integer values can be provided as strings, e.g. "1199". Only
      strings with integer values can match a range-based restriction which is
      to be supported soon. Multiple values can be assigned to the same key.
      One product may have up to 500 product_labels. Notice that the total
      number of distinct product_labels over all products in one ProductSet
      cannot exceed 1M, otherwise the product search pipeline will refuse to
      work for that ProductSet.
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  name = _messages.StringField(3)
  productCategory = _messages.StringField(4)
  productLabels = _messages.MessageField('KeyValue', 5, repeated=True)


class ProductSearchParams(_messages.Message):
  r"""Parameters for a product search request.

  Fields:
    boundingPoly: The bounding polygon around the area of interest in the
      image. If it is not specified, system discretion will be applied.
    filter: The filtering expression. This can be used to restrict search
      results based on Product labels. We currently support an AND of OR of
      key-value expressions, where each expression within an OR must have the
      same key. An '=' should be used to connect the key and value. For
      example, "(color = red OR color = blue) AND brand = Google" is
      acceptable, but "(color = red OR brand = Google)" is not acceptable.
      "color: red" is not acceptable because it uses a ':' instead of an '='.
    productCategories: The list of product categories to search in. Currently,
      we only consider the first category, and either "homegoods-v2",
      "apparel-v2", "toys-v2", "packagedgoods-v1", or "general-v1" should be
      specified. The legacy categories "homegoods", "apparel", and "toys" are
      still supported but will be deprecated. For new products, please use
      "homegoods-v2", "apparel-v2", or "toys-v2" for better product search
      accuracy. It is recommended to migrate existing products to these
      categories as well.
    productSet: The resource name of a ProductSet to be searched for similar
      images. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`.
  """

  boundingPoly = _messages.MessageField('BoundingPoly', 1)
  filter = _messages.StringField(2)
  productCategories = _messages.StringField(3, repeated=True)
  productSet = _messages.StringField(4)


class ProductSearchResults(_messages.Message):
  r"""Results for a product search request.

  Fields:
    indexTime: Timestamp of the index which provided these results. Products
      added to the product set and products removed from the product set after
      this time are not reflected in the current results.
    productGroupedResults: List of results grouped by products detected in the
      query image. Each entry corresponds to one bounding polygon in the query
      image, and contains the matching products specific to that region. There
      may be duplicate product matches in the union of all the per-product
      results.
    results: List of results, one for each product match.
  """

  indexTime = _messages.StringField(1)
  productGroupedResults = _messages.MessageField('GroupedResult', 2, repeated=True)
  results = _messages.MessageField('Result', 3, repeated=True)


class ProductSet(_messages.Message):
  r"""A ProductSet contains Products. A ProductSet can contain a maximum of 1
  million reference images. If the limit is exceeded, periodic indexing will
  fail.

  Fields:
    displayName: The user-provided name for this ProductSet. Must not be
      empty. Must be at most 4096 characters long.
    indexError: Output only. If there was an error with indexing the product
      set, the field is populated. This field is ignored when creating a
      ProductSet.
    indexTime: Output only. The time at which this ProductSet was last
      indexed. Query results will reflect all updates before this time. If
      this ProductSet has never been indexed, this timestamp is the default
      value "1970-01-01T00:00:00Z". This field is ignored when creating a
      ProductSet.
    name: The resource name of the ProductSet. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`. This
      field is ignored when creating a ProductSet.
  """

  displayName = _messages.StringField(1)
  indexError = _messages.MessageField('Status', 2)
  indexTime = _messages.StringField(3)
  name = _messages.StringField(4)


class ProductSetPurgeConfig(_messages.Message):
  r"""Config to control which ProductSet contains the Products to be deleted.

  Fields:
    productSetId: The ProductSet that contains the Products to delete. If a
      Product is a member of product_set_id in addition to other ProductSets,
      the Product will still be deleted.
  """

  productSetId = _messages.StringField(1)


class Property(_messages.Message):
  r"""A `Property` consists of a user-supplied name/value pair.

  Fields:
    name: Name of the property.
    uint64Value: Value of numeric properties.
    value: Value of the property.
  """

  name = _messages.StringField(1)
  uint64Value = _messages.IntegerField(2, variant=_messages.Variant.UINT64)
  value = _messages.StringField(3)


class PurgeProductsRequest(_messages.Message):
  r"""Request message for the `PurgeProducts` method.

  Fields:
    deleteOrphanProducts: If delete_orphan_products is true, all Products that
      are not in any ProductSet will be deleted.
    force: The default value is false. Override this value to true to actually
      perform the purge.
    productSetPurgeConfig: Specify which ProductSet contains the Products to
      be deleted.
  """

  deleteOrphanProducts = _messages.BooleanField(1)
  force = _messages.BooleanField(2)
  productSetPurgeConfig = _messages.MessageField('ProductSetPurgeConfig', 3)


class ReferenceImage(_messages.Message):
  r"""A `ReferenceImage` represents a product image and its associated
  metadata, such as bounding boxes.

  Fields:
    boundingPolys: Optional. Bounding polygons around the areas of interest in
      the reference image. If this field is empty, the system will try to
      detect regions of interest. At most 10 bounding polygons will be used.
      The provided shape is converted into a non-rotated rectangle. Once
      converted, the small edge of the rectangle must be greater than or equal
      to 300 pixels. The aspect ratio must be 1:4 or less (i.e. 1:3 is ok; 1:5
      is not).
    name: The resource name of the reference image. Format is: `projects/PROJE
      CT_ID/locations/LOC_ID/products/PRODUCT_ID/referenceImages/IMAGE_ID`.
      This field is ignored when creating a reference image.
    uri: Required. The Google Cloud Storage URI of the reference image. The
      URI must start with `gs://`.
  """

  boundingPolys = _messages.MessageField('BoundingPoly', 1, repeated=True)
  name = _messages.StringField(2)
  uri = _messages.StringField(3)


class RemoveProductFromProductSetRequest(_messages.Message):
  r"""Request message for the `RemoveProductFromProductSet` method.

  Fields:
    product: Required. The resource name for the Product to be removed from
      this ProductSet. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`
  """

  product = _messages.StringField(1)


class Result(_messages.Message):
  r"""Information about a product.

  Fields:
    image: The resource name of the image from the product that is the closest
      match to the query.
    product: The Product.
    score: A confidence level on the match, ranging from 0 (no confidence) to
      1 (full confidence).
  """

  image = _messages.StringField(1)
  product = _messages.MessageField('Product', 2)
  score = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class SafeSearchAnnotation(_messages.Message):
  r"""Set of features pertaining to the image, computed by computer vision
  methods over safe-search verticals (for example, adult, spoof, medical,
  violence).

  Enums:
    AdultValueValuesEnum: Represents the adult content likelihood for the
      image. Adult content may contain elements such as nudity, pornographic
      images or cartoons, or sexual activities.
    MedicalValueValuesEnum: Likelihood that this is a medical image.
    RacyValueValuesEnum: Likelihood that the request image contains racy
      content. Racy content may include (but is not limited to) skimpy or
      sheer clothing, strategically covered nudity, lewd or provocative poses,
      or close-ups of sensitive body areas.
    SpoofValueValuesEnum: Spoof likelihood. The likelihood that an
      modification was made to the image's canonical version to make it appear
      funny or offensive.
    ViolenceValueValuesEnum: Likelihood that this image contains violent
      content.

  Fields:
    adult: Represents the adult content likelihood for the image. Adult
      content may contain elements such as nudity, pornographic images or
      cartoons, or sexual activities.
    medical: Likelihood that this is a medical image.
    racy: Likelihood that the request image contains racy content. Racy
      content may include (but is not limited to) skimpy or sheer clothing,
      strategically covered nudity, lewd or provocative poses, or close-ups of
      sensitive body areas.
    spoof: Spoof likelihood. The likelihood that an modification was made to
      the image's canonical version to make it appear funny or offensive.
    violence: Likelihood that this image contains violent content.
  """

  class AdultValueValuesEnum(_messages.Enum):
    r"""Represents the adult content likelihood for the image. Adult content
    may contain elements such as nudity, pornographic images or cartoons, or
    sexual activities.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class MedicalValueValuesEnum(_messages.Enum):
    r"""Likelihood that this is a medical image.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class RacyValueValuesEnum(_messages.Enum):
    r"""Likelihood that the request image contains racy content. Racy content
    may include (but is not limited to) skimpy or sheer clothing,
    strategically covered nudity, lewd or provocative poses, or close-ups of
    sensitive body areas.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class SpoofValueValuesEnum(_messages.Enum):
    r"""Spoof likelihood. The likelihood that an modification was made to the
    image's canonical version to make it appear funny or offensive.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  class ViolenceValueValuesEnum(_messages.Enum):
    r"""Likelihood that this image contains violent content.

    Values:
      UNKNOWN: Unknown likelihood.
      VERY_UNLIKELY: It is very unlikely.
      UNLIKELY: It is unlikely.
      POSSIBLE: It is possible.
      LIKELY: It is likely.
      VERY_LIKELY: It is very likely.
    """
    UNKNOWN = 0
    VERY_UNLIKELY = 1
    UNLIKELY = 2
    POSSIBLE = 3
    LIKELY = 4
    VERY_LIKELY = 5

  adult = _messages.EnumField('AdultValueValuesEnum', 1)
  medical = _messages.EnumField('MedicalValueValuesEnum', 2)
  racy = _messages.EnumField('RacyValueValuesEnum', 3)
  spoof = _messages.EnumField('SpoofValueValuesEnum', 4)
  violence = _messages.EnumField('ViolenceValueValuesEnum', 5)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Symbol(_messages.Message):
  r"""A single symbol representation.

  Fields:
    boundingBox: The bounding box for the symbol. The vertices are in the
      order of top-left, top-right, bottom-right, bottom-left. When a rotation
      of the bounding box is detected the rotation is represented as around
      the top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results for the symbol. Range [0, 1].
    property: Additional information detected for the symbol.
    text: The actual UTF-8 representation of the symbol.
  """

  boundingBox = _messages.MessageField('BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  property = _messages.MessageField('TextProperty', 3)
  text = _messages.StringField(4)


class TextAnnotation(_messages.Message):
  r"""TextAnnotation contains a structured representation of OCR extracted
  text. The hierarchy of an OCR extracted text structure is like this:
  TextAnnotation -> Page -> Block -> Paragraph -> Word -> Symbol Each
  structural component, starting from Page, may further have their own
  properties. Properties describe detected languages, breaks etc.. Please
  refer to the TextAnnotation.TextProperty message definition below for more
  detail.

  Fields:
    pages: List of pages detected by OCR.
    text: UTF-8 text detected on the pages.
  """

  pages = _messages.MessageField('Page', 1, repeated=True)
  text = _messages.StringField(2)


class TextDetectionParams(_messages.Message):
  r"""Parameters for text detections. This is used to control TEXT_DETECTION
  and DOCUMENT_TEXT_DETECTION features.

  Fields:
    advancedOcrOptions: A list of advanced OCR options to further fine-tune
      OCR behavior. Current valid values are: - `legacy_layout`: a heuristics
      layout detection algorithm, which serves as an alternative to the
      current ML-based layout detection algorithm. Customers can choose the
      best suitable layout algorithm based on their situation.
    enableTextDetectionConfidenceScore: By default, Cloud Vision API only
      includes confidence score for DOCUMENT_TEXT_DETECTION result. Set the
      flag to true to include confidence score for TEXT_DETECTION as well.
  """

  advancedOcrOptions = _messages.StringField(1, repeated=True)
  enableTextDetectionConfidenceScore = _messages.BooleanField(2)


class TextProperty(_messages.Message):
  r"""Additional information detected on the structural component.

  Fields:
    detectedBreak: Detected start or end of a text segment.
    detectedLanguages: A list of detected languages together with confidence.
  """

  detectedBreak = _messages.MessageField('DetectedBreak', 1)
  detectedLanguages = _messages.MessageField('DetectedLanguage', 2, repeated=True)


class Vertex(_messages.Message):
  r"""A vertex represents a 2D point in the image. NOTE: the vertex
  coordinates are in the same scale as the original image.

  Fields:
    x: X coordinate.
    y: Y coordinate.
  """

  x = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  y = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class VisionLocationsOperationsGetRequest(_messages.Message):
  r"""A VisionLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class VisionOperationsCancelRequest(_messages.Message):
  r"""A VisionOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class VisionOperationsDeleteRequest(_messages.Message):
  r"""A VisionOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class VisionOperationsGetRequest(_messages.Message):
  r"""A VisionOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class VisionOperationsListRequest(_messages.Message):
  r"""A VisionOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class VisionProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A VisionProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class VisionProjectsLocationsProductSetsAddProductRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductSetsAddProductRequest object.

  Fields:
    addProductToProductSetRequest: A AddProductToProductSetRequest resource to
      be passed as the request body.
    name: Required. The resource name for the ProductSet to modify. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
  """

  addProductToProductSetRequest = _messages.MessageField('AddProductToProductSetRequest', 1)
  name = _messages.StringField(2, required=True)


class VisionProjectsLocationsProductSetsCreateRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductSetsCreateRequest object.

  Fields:
    parent: Required. The project in which the ProductSet should be created.
      Format is `projects/PROJECT_ID/locations/LOC_ID`.
    productSet: A ProductSet resource to be passed as the request body.
    productSetId: A user-supplied resource id for this ProductSet. If set, the
      server will attempt to use this value as the resource id. If it is
      already in use, an error is returned with code ALREADY_EXISTS. Must be
      at most 128 characters long. It cannot contain the character `/`.
  """

  parent = _messages.StringField(1, required=True)
  productSet = _messages.MessageField('ProductSet', 2)
  productSetId = _messages.StringField(3)


class VisionProjectsLocationsProductSetsDeleteRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductSetsDeleteRequest object.

  Fields:
    name: Required. Resource name of the ProductSet to delete. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
  """

  name = _messages.StringField(1, required=True)


class VisionProjectsLocationsProductSetsGetRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductSetsGetRequest object.

  Fields:
    name: Required. Resource name of the ProductSet to get. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
  """

  name = _messages.StringField(1, required=True)


class VisionProjectsLocationsProductSetsImportRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductSetsImportRequest object.

  Fields:
    importProductSetsRequest: A ImportProductSetsRequest resource to be passed
      as the request body.
    parent: Required. The project in which the ProductSets should be imported.
      Format is `projects/PROJECT_ID/locations/LOC_ID`.
  """

  importProductSetsRequest = _messages.MessageField('ImportProductSetsRequest', 1)
  parent = _messages.StringField(2, required=True)


class VisionProjectsLocationsProductSetsListRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductSetsListRequest object.

  Fields:
    pageSize: The maximum number of items to return. Default 10, maximum 100.
    pageToken: The next_page_token returned from a previous List request, if
      any.
    parent: Required. The project from which ProductSets should be listed.
      Format is `projects/PROJECT_ID/locations/LOC_ID`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class VisionProjectsLocationsProductSetsPatchRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductSetsPatchRequest object.

  Fields:
    name: The resource name of the ProductSet. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`. This
      field is ignored when creating a ProductSet.
    productSet: A ProductSet resource to be passed as the request body.
    updateMask: The FieldMask that specifies which fields to update. If
      update_mask isn't specified, all mutable fields are to be updated. Valid
      mask path is `display_name`.
  """

  name = _messages.StringField(1, required=True)
  productSet = _messages.MessageField('ProductSet', 2)
  updateMask = _messages.StringField(3)


class VisionProjectsLocationsProductSetsProductsListRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductSetsProductsListRequest object.

  Fields:
    name: Required. The ProductSet resource for which to retrieve Products.
      Format is:
      `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
    pageSize: The maximum number of items to return. Default 10, maximum 100.
    pageToken: The next_page_token returned from a previous List request, if
      any.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class VisionProjectsLocationsProductSetsRemoveProductRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductSetsRemoveProductRequest object.

  Fields:
    name: Required. The resource name for the ProductSet to modify. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/productSets/PRODUCT_SET_ID`
    removeProductFromProductSetRequest: A RemoveProductFromProductSetRequest
      resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  removeProductFromProductSetRequest = _messages.MessageField('RemoveProductFromProductSetRequest', 2)


class VisionProjectsLocationsProductsCreateRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductsCreateRequest object.

  Fields:
    parent: Required. The project in which the Product should be created.
      Format is `projects/PROJECT_ID/locations/LOC_ID`.
    product: A Product resource to be passed as the request body.
    productId: A user-supplied resource id for this Product. If set, the
      server will attempt to use this value as the resource id. If it is
      already in use, an error is returned with code ALREADY_EXISTS. Must be
      at most 128 characters long. It cannot contain the character `/`.
  """

  parent = _messages.StringField(1, required=True)
  product = _messages.MessageField('Product', 2)
  productId = _messages.StringField(3)


class VisionProjectsLocationsProductsDeleteRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductsDeleteRequest object.

  Fields:
    name: Required. Resource name of product to delete. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`
  """

  name = _messages.StringField(1, required=True)


class VisionProjectsLocationsProductsGetRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductsGetRequest object.

  Fields:
    name: Required. Resource name of the Product to get. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`
  """

  name = _messages.StringField(1, required=True)


class VisionProjectsLocationsProductsListRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductsListRequest object.

  Fields:
    pageSize: The maximum number of items to return. Default 10, maximum 100.
    pageToken: The next_page_token returned from a previous List request, if
      any.
    parent: Required. The project OR ProductSet from which Products should be
      listed. Format: `projects/PROJECT_ID/locations/LOC_ID`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class VisionProjectsLocationsProductsPatchRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductsPatchRequest object.

  Fields:
    name: The resource name of the product. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`. This field
      is ignored when creating a product.
    product: A Product resource to be passed as the request body.
    updateMask: The FieldMask that specifies which fields to update. If
      update_mask isn't specified, all mutable fields are to be updated. Valid
      mask paths include `product_labels`, `display_name`, and `description`.
  """

  name = _messages.StringField(1, required=True)
  product = _messages.MessageField('Product', 2)
  updateMask = _messages.StringField(3)


class VisionProjectsLocationsProductsPurgeRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductsPurgeRequest object.

  Fields:
    parent: Required. The project and location in which the Products should be
      deleted. Format is `projects/PROJECT_ID/locations/LOC_ID`.
    purgeProductsRequest: A PurgeProductsRequest resource to be passed as the
      request body.
  """

  parent = _messages.StringField(1, required=True)
  purgeProductsRequest = _messages.MessageField('PurgeProductsRequest', 2)


class VisionProjectsLocationsProductsReferenceImagesCreateRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductsReferenceImagesCreateRequest object.

  Fields:
    parent: Required. Resource name of the product in which to create the
      reference image. Format is
      `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`.
    referenceImage: A ReferenceImage resource to be passed as the request
      body.
    referenceImageId: A user-supplied resource id for the ReferenceImage to be
      added. If set, the server will attempt to use this value as the resource
      id. If it is already in use, an error is returned with code
      ALREADY_EXISTS. Must be at most 128 characters long. It cannot contain
      the character `/`.
  """

  parent = _messages.StringField(1, required=True)
  referenceImage = _messages.MessageField('ReferenceImage', 2)
  referenceImageId = _messages.StringField(3)


class VisionProjectsLocationsProductsReferenceImagesDeleteRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductsReferenceImagesDeleteRequest object.

  Fields:
    name: Required. The resource name of the reference image to delete. Format
      is: `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID/referenceI
      mages/IMAGE_ID`
  """

  name = _messages.StringField(1, required=True)


class VisionProjectsLocationsProductsReferenceImagesGetRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductsReferenceImagesGetRequest object.

  Fields:
    name: Required. The resource name of the ReferenceImage to get. Format is:
      `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID/referenceImage
      s/IMAGE_ID`.
  """

  name = _messages.StringField(1, required=True)


class VisionProjectsLocationsProductsReferenceImagesListRequest(_messages.Message):
  r"""A VisionProjectsLocationsProductsReferenceImagesListRequest object.

  Fields:
    pageSize: The maximum number of items to return. Default 10, maximum 100.
    pageToken: A token identifying a page of results to be returned. This is
      the value of `nextPageToken` returned in a previous reference image list
      request. Defaults to the first page if not specified.
    parent: Required. Resource name of the product containing the reference
      images. Format is
      `projects/PROJECT_ID/locations/LOC_ID/products/PRODUCT_ID`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class VisionProjectsOperationsGetRequest(_messages.Message):
  r"""A VisionProjectsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class WebDetection(_messages.Message):
  r"""Relevant information for the image from the Internet.

  Fields:
    bestGuessLabels: The service's best guess as to the topic of the request
      image. Inferred from similar images on the open web.
    fullMatchingImages: Fully matching images from the Internet. Can include
      resized copies of the query image.
    pagesWithMatchingImages: Web pages containing the matching images from the
      Internet.
    partialMatchingImages: Partial matching images from the Internet. Those
      images are similar enough to share some key-point features. For example
      an original image will likely have partial matching for its crops.
    visuallySimilarImages: The visually similar image results.
    webEntities: Deduced entities from similar images on the Internet.
  """

  bestGuessLabels = _messages.MessageField('WebLabel', 1, repeated=True)
  fullMatchingImages = _messages.MessageField('WebImage', 2, repeated=True)
  pagesWithMatchingImages = _messages.MessageField('WebPage', 3, repeated=True)
  partialMatchingImages = _messages.MessageField('WebImage', 4, repeated=True)
  visuallySimilarImages = _messages.MessageField('WebImage', 5, repeated=True)
  webEntities = _messages.MessageField('WebEntity', 6, repeated=True)


class WebDetectionParams(_messages.Message):
  r"""Parameters for web detection request.

  Fields:
    includeGeoResults: Whether to include results derived from the geo
      information in the image.
  """

  includeGeoResults = _messages.BooleanField(1)


class WebEntity(_messages.Message):
  r"""Entity deduced from similar images on the Internet.

  Fields:
    description: Canonical description of the entity, in English.
    entityId: Opaque entity ID.
    score: Overall relevancy score for the entity. Not normalized and not
      comparable across different image queries.
  """

  description = _messages.StringField(1)
  entityId = _messages.StringField(2)
  score = _messages.FloatField(3, variant=_messages.Variant.FLOAT)


class WebImage(_messages.Message):
  r"""Metadata for online images.

  Fields:
    score: (Deprecated) Overall relevancy score for the image.
    url: The result image URL.
  """

  score = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  url = _messages.StringField(2)


class WebLabel(_messages.Message):
  r"""Label to provide extra metadata for the web detection.

  Fields:
    label: Label for extra metadata.
    languageCode: The BCP-47 language code for `label`, such as "en-US" or
      "sr-Latn". For more information, see
      http://www.unicode.org/reports/tr35/#Unicode_locale_identifier.
  """

  label = _messages.StringField(1)
  languageCode = _messages.StringField(2)


class WebPage(_messages.Message):
  r"""Metadata for web pages.

  Fields:
    fullMatchingImages: Fully matching images on the page. Can include resized
      copies of the query image.
    pageTitle: Title for the web page, may contain HTML markups.
    partialMatchingImages: Partial matching images on the page. Those images
      are similar enough to share some key-point features. For example an
      original image will likely have partial matching for its crops.
    score: (Deprecated) Overall relevancy score for the web page.
    url: The result web page URL.
  """

  fullMatchingImages = _messages.MessageField('WebImage', 1, repeated=True)
  pageTitle = _messages.StringField(2)
  partialMatchingImages = _messages.MessageField('WebImage', 3, repeated=True)
  score = _messages.FloatField(4, variant=_messages.Variant.FLOAT)
  url = _messages.StringField(5)


class Word(_messages.Message):
  r"""A word representation.

  Fields:
    boundingBox: The bounding box for the word. The vertices are in the order
      of top-left, top-right, bottom-right, bottom-left. When a rotation of
      the bounding box is detected the rotation is represented as around the
      top-left corner as defined when the text is read in the 'natural'
      orientation. For example: * when the text is horizontal it might look
      like: 0----1 | | 3----2 * when it's rotated 180 degrees around the top-
      left corner it becomes: 2----3 | | 1----0 and the vertex order will
      still be (0, 1, 2, 3).
    confidence: Confidence of the OCR results for the word. Range [0, 1].
    property: Additional information detected for the word.
    symbols: List of symbols in the word. The order of the symbols follows the
      natural reading order.
  """

  boundingBox = _messages.MessageField('BoundingPoly', 1)
  confidence = _messages.FloatField(2, variant=_messages.Variant.FLOAT)
  property = _messages.MessageField('TextProperty', 3)
  symbols = _messages.MessageField('Symbol', 4, repeated=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

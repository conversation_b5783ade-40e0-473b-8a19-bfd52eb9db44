"""Generated message classes for bigtableadmin version v2.

Administer your Cloud Bigtable tables and instances.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'bigtableadmin'


class AppProfile(_messages.Message):
  r"""A configuration object describing how Cloud Bigtable should treat
  traffic from a particular end user application.

  Enums:
    PriorityValueValuesEnum: This field has been deprecated in favor of
      `standard_isolation.priority`. If you set this field,
      `standard_isolation.priority` will be set instead. The priority of
      requests sent using this app profile.

  Fields:
    description: Long form description of the use case for this AppProfile.
    etag: Strongly validated etag for optimistic concurrency control. Preserve
      the value returned from `GetAppProfile` when calling `UpdateAppProfile`
      to fail the request if there has been a modification in the mean time.
      The `update_mask` of the request need not include `etag` for this
      protection to apply. See
      [Wikipedia](https://en.wikipedia.org/wiki/HTTP_ETag) and [RFC
      7232](https://tools.ietf.org/html/rfc7232#section-2.3) for more details.
    multiClusterRoutingUseAny: Use a multi-cluster routing policy.
    name: The unique name of the app profile. Values are of the form
      `projects/{project}/instances/{instance}/appProfiles/_a-zA-Z0-9*`.
    priority: This field has been deprecated in favor of
      `standard_isolation.priority`. If you set this field,
      `standard_isolation.priority` will be set instead. The priority of
      requests sent using this app profile.
    singleClusterRouting: Use a single-cluster routing policy.
    standardIsolation: The standard options used for isolating this app
      profile's traffic from other use cases.
  """

  class PriorityValueValuesEnum(_messages.Enum):
    r"""This field has been deprecated in favor of
    `standard_isolation.priority`. If you set this field,
    `standard_isolation.priority` will be set instead. The priority of
    requests sent using this app profile.

    Values:
      PRIORITY_UNSPECIFIED: Default value. Mapped to PRIORITY_HIGH (the legacy
        behavior) on creation.
      PRIORITY_LOW: <no description>
      PRIORITY_MEDIUM: <no description>
      PRIORITY_HIGH: <no description>
    """
    PRIORITY_UNSPECIFIED = 0
    PRIORITY_LOW = 1
    PRIORITY_MEDIUM = 2
    PRIORITY_HIGH = 3

  description = _messages.StringField(1)
  etag = _messages.StringField(2)
  multiClusterRoutingUseAny = _messages.MessageField('MultiClusterRoutingUseAny', 3)
  name = _messages.StringField(4)
  priority = _messages.EnumField('PriorityValueValuesEnum', 5)
  singleClusterRouting = _messages.MessageField('SingleClusterRouting', 6)
  standardIsolation = _messages.MessageField('StandardIsolation', 7)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class AutoscalingLimits(_messages.Message):
  r"""Limits for the number of nodes a Cluster can autoscale up/down to.

  Fields:
    maxServeNodes: Required. Maximum number of nodes to scale up to.
    minServeNodes: Required. Minimum number of nodes to scale down to.
  """

  maxServeNodes = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minServeNodes = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class AutoscalingTargets(_messages.Message):
  r"""The Autoscaling targets for a Cluster. These determine the recommended
  nodes.

  Fields:
    cpuUtilizationPercent: The cpu utilization that the Autoscaler should be
      trying to achieve. This number is on a scale from 0 (no utilization) to
      100 (total utilization), and is limited between 10 and 80, otherwise it
      will return INVALID_ARGUMENT error.
    storageUtilizationGibPerNode: The storage utilization that the Autoscaler
      should be trying to achieve. This number is limited between 2560
      (2.5TiB) and 5120 (5TiB) for a SSD cluster and between 8192 (8TiB) and
      16384 (16TiB) for an HDD cluster, otherwise it will return
      INVALID_ARGUMENT error. If this value is set to 0, it will be treated as
      if it were set to the default value: 2560 for SSD, 8192 for HDD.
  """

  cpuUtilizationPercent = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  storageUtilizationGibPerNode = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class Backup(_messages.Message):
  r"""A backup of a Cloud Bigtable table.

  Enums:
    StateValueValuesEnum: Output only. The current state of the backup.

  Fields:
    encryptionInfo: Output only. The encryption information for the backup.
    endTime: Output only. `end_time` is the time that the backup was finished.
      The row data in the backup will be no newer than this timestamp.
    expireTime: Required. The expiration time of the backup, with microseconds
      granularity that must be at least 6 hours and at most 90 days from the
      time the request is received. Once the `expire_time` has passed, Cloud
      Bigtable will delete the backup and free the resources used by the
      backup.
    name: A globally unique identifier for the backup which cannot be changed.
      Values are of the form
      `projects/{project}/instances/{instance}/clusters/{cluster}/ backups/_a-
      zA-Z0-9*` The final segment of the name must be between 1 and 50
      characters in length. The backup is stored in the cluster identified by
      the prefix of the backup name of the form
      `projects/{project}/instances/{instance}/clusters/{cluster}`.
    sizeBytes: Output only. Size of the backup in bytes.
    sourceBackup: Output only. Name of the backup from which this backup was
      copied. If a backup is not created by copying a backup, this field will
      be empty. Values are of the form: projects//instances//backups/.
    sourceTable: Required. Immutable. Name of the table from which this backup
      was created. This needs to be in the same instance as the backup. Values
      are of the form
      `projects/{project}/instances/{instance}/tables/{source_table}`.
    startTime: Output only. `start_time` is the time that the backup was
      started (i.e. approximately the time the CreateBackup request is
      received). The row data in this backup will be no older than this
      timestamp.
    state: Output only. The current state of the backup.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the backup.

    Values:
      STATE_UNSPECIFIED: Not specified.
      CREATING: The pending backup is still being created. Operations on the
        backup may fail with `FAILED_PRECONDITION` in this state.
      READY: The backup is complete and ready for use.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2

  encryptionInfo = _messages.MessageField('EncryptionInfo', 1)
  endTime = _messages.StringField(2)
  expireTime = _messages.StringField(3)
  name = _messages.StringField(4)
  sizeBytes = _messages.IntegerField(5)
  sourceBackup = _messages.StringField(6)
  sourceTable = _messages.StringField(7)
  startTime = _messages.StringField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)


class BackupInfo(_messages.Message):
  r"""Information about a backup.

  Fields:
    backup: Output only. Name of the backup.
    endTime: Output only. This time that the backup was finished. Row data in
      the backup will be no newer than this timestamp.
    sourceBackup: Output only. Name of the backup from which this backup was
      copied. If a backup is not created by copying a backup, this field will
      be empty. Values are of the form: projects//instances//backups/.
    sourceTable: Output only. Name of the table the backup was created from.
    startTime: Output only. The time that the backup was started. Row data in
      the backup will be no older than this timestamp.
  """

  backup = _messages.StringField(1)
  endTime = _messages.StringField(2)
  sourceBackup = _messages.StringField(3)
  sourceTable = _messages.StringField(4)
  startTime = _messages.StringField(5)


class BigtableadminOperationsCancelRequest(_messages.Message):
  r"""A BigtableadminOperationsCancelRequest object.

  Fields:
    name: The name of the operation resource to be cancelled.
  """

  name = _messages.StringField(1, required=True)


class BigtableadminOperationsDeleteRequest(_messages.Message):
  r"""A BigtableadminOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class BigtableadminOperationsGetRequest(_messages.Message):
  r"""A BigtableadminOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class BigtableadminOperationsProjectsOperationsListRequest(_messages.Message):
  r"""A BigtableadminOperationsProjectsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class BigtableadminProjectsInstancesAppProfilesCreateRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesAppProfilesCreateRequest object.

  Fields:
    appProfile: A AppProfile resource to be passed as the request body.
    appProfileId: Required. The ID to be used when referring to the new app
      profile within its instance, e.g., just `myprofile` rather than
      `projects/myproject/instances/myinstance/appProfiles/myprofile`.
    ignoreWarnings: If true, ignore safety checks when creating the app
      profile.
    parent: Required. The unique name of the instance in which to create the
      new app profile. Values are of the form
      `projects/{project}/instances/{instance}`.
  """

  appProfile = _messages.MessageField('AppProfile', 1)
  appProfileId = _messages.StringField(2)
  ignoreWarnings = _messages.BooleanField(3)
  parent = _messages.StringField(4, required=True)


class BigtableadminProjectsInstancesAppProfilesDeleteRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesAppProfilesDeleteRequest object.

  Fields:
    ignoreWarnings: Required. If true, ignore safety checks when deleting the
      app profile.
    name: Required. The unique name of the app profile to be deleted. Values
      are of the form
      `projects/{project}/instances/{instance}/appProfiles/{app_profile}`.
  """

  ignoreWarnings = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)


class BigtableadminProjectsInstancesAppProfilesGetRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesAppProfilesGetRequest object.

  Fields:
    name: Required. The unique name of the requested app profile. Values are
      of the form
      `projects/{project}/instances/{instance}/appProfiles/{app_profile}`.
  """

  name = _messages.StringField(1, required=True)


class BigtableadminProjectsInstancesAppProfilesListRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesAppProfilesListRequest object.

  Fields:
    pageSize: Maximum number of results per page. A page_size of zero lets the
      server choose the number of items to return. A page_size which is
      strictly positive will return at most that many items. A negative
      page_size will cause an error. Following the first request, subsequent
      paginated calls are not required to pass a page_size. If a page_size is
      set in subsequent calls, it must match the page_size given in the first
      request.
    pageToken: The value of `next_page_token` returned by a previous call.
    parent: Required. The unique name of the instance for which a list of app
      profiles is requested. Values are of the form
      `projects/{project}/instances/{instance}`. Use `{instance} = '-'` to
      list AppProfiles for all Instances in a project, e.g.,
      `projects/myproject/instances/-`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BigtableadminProjectsInstancesAppProfilesPatchRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesAppProfilesPatchRequest object.

  Fields:
    appProfile: A AppProfile resource to be passed as the request body.
    ignoreWarnings: If true, ignore safety checks when updating the app
      profile.
    name: The unique name of the app profile. Values are of the form
      `projects/{project}/instances/{instance}/appProfiles/_a-zA-Z0-9*`.
    updateMask: Required. The subset of app profile fields which should be
      replaced. If unset, all fields will be replaced.
  """

  appProfile = _messages.MessageField('AppProfile', 1)
  ignoreWarnings = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  updateMask = _messages.StringField(4)


class BigtableadminProjectsInstancesClustersBackupsCopyRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesClustersBackupsCopyRequest object.

  Fields:
    copyBackupRequest: A CopyBackupRequest resource to be passed as the
      request body.
    parent: Required. The name of the destination cluster that will contain
      the backup copy. The cluster must already exists. Values are of the
      form: `projects/{project}/instances/{instance}/clusters/{cluster}`.
  """

  copyBackupRequest = _messages.MessageField('CopyBackupRequest', 1)
  parent = _messages.StringField(2, required=True)


class BigtableadminProjectsInstancesClustersBackupsCreateRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesClustersBackupsCreateRequest object.

  Fields:
    backup: A Backup resource to be passed as the request body.
    backupId: Required. The id of the backup to be created. The `backup_id`
      along with the parent `parent` are combined as
      {parent}/backups/{backup_id} to create the full backup name, of the
      form: `projects/{project}/instances/{instance}/clusters/{cluster}/backup
      s/{backup_id}`. This string must be between 1 and 50 characters in
      length and match the regex _a-zA-Z0-9*.
    parent: Required. This must be one of the clusters in the instance in
      which this table is located. The backup will be stored in this cluster.
      Values are of the form
      `projects/{project}/instances/{instance}/clusters/{cluster}`.
  """

  backup = _messages.MessageField('Backup', 1)
  backupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BigtableadminProjectsInstancesClustersBackupsDeleteRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesClustersBackupsDeleteRequest object.

  Fields:
    name: Required. Name of the backup to delete. Values are of the form `proj
      ects/{project}/instances/{instance}/clusters/{cluster}/backups/{backup}`
      .
  """

  name = _messages.StringField(1, required=True)


class BigtableadminProjectsInstancesClustersBackupsGetIamPolicyRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesClustersBackupsGetIamPolicyRequest
  object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BigtableadminProjectsInstancesClustersBackupsGetRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesClustersBackupsGetRequest object.

  Fields:
    name: Required. Name of the backup. Values are of the form `projects/{proj
      ect}/instances/{instance}/clusters/{cluster}/backups/{backup}`.
  """

  name = _messages.StringField(1, required=True)


class BigtableadminProjectsInstancesClustersBackupsListRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesClustersBackupsListRequest object.

  Fields:
    filter: A filter expression that filters backups listed in the response.
      The expression must specify the field name, a comparison operator, and
      the value that you want to use for filtering. The value must be a
      string, a number, or a boolean. The comparison operator must be <, >,
      <=, >=, !=, =, or :. Colon ':' represents a HAS operator which is
      roughly synonymous with equality. Filter rules are case insensitive. The
      fields eligible for filtering are: * `name` * `source_table` * `state` *
      `start_time` (and values are of the format YYYY-MM-DDTHH:MM:SSZ) *
      `end_time` (and values are of the format YYYY-MM-DDTHH:MM:SSZ) *
      `expire_time` (and values are of the format YYYY-MM-DDTHH:MM:SSZ) *
      `size_bytes` To filter on multiple expressions, provide each separate
      expression within parentheses. By default, each expression is an AND
      expression. However, you can include AND, OR, and NOT expressions
      explicitly. Some examples of using filters are: * `name:"exact"` --> The
      backup's name is the string "exact". * `name:howl` --> The backup's name
      contains the string "howl". * `source_table:prod` --> The source_table's
      name contains the string "prod". * `state:CREATING` --> The backup is
      pending creation. * `state:READY` --> The backup is fully created and
      ready for use. * `(name:howl) AND (start_time <
      \"2018-03-28T14:50:00Z\")` --> The backup name contains the string
      "howl" and start_time of the backup is before 2018-03-28T14:50:00Z. *
      `size_bytes > 10000000000` --> The backup's size is greater than 10GB
    orderBy: An expression for specifying the sort order of the results of the
      request. The string value should specify one or more fields in Backup.
      The full syntax is described at https://aip.dev/132#ordering. Fields
      supported are: * name * source_table * expire_time * start_time *
      end_time * size_bytes * state For example, "start_time". The default
      sorting order is ascending. To specify descending order for the field, a
      suffix " desc" should be appended to the field name. For example,
      "start_time desc". Redundant space characters in the syntax are
      insigificant. If order_by is empty, results will be sorted by
      `start_time` in descending order starting from the most recently created
      backup.
    pageSize: Number of backups to be returned in the response. If 0 or less,
      defaults to the server's maximum allowed page size.
    pageToken: If non-empty, `page_token` should contain a next_page_token
      from a previous ListBackupsResponse to the same `parent` and with the
      same `filter`.
    parent: Required. The cluster to list backups from. Values are of the form
      `projects/{project}/instances/{instance}/clusters/{cluster}`. Use
      `{cluster} = '-'` to list backups for all clusters in an instance, e.g.,
      `projects/{project}/instances/{instance}/clusters/-`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class BigtableadminProjectsInstancesClustersBackupsPatchRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesClustersBackupsPatchRequest object.

  Fields:
    backup: A Backup resource to be passed as the request body.
    name: A globally unique identifier for the backup which cannot be changed.
      Values are of the form
      `projects/{project}/instances/{instance}/clusters/{cluster}/ backups/_a-
      zA-Z0-9*` The final segment of the name must be between 1 and 50
      characters in length. The backup is stored in the cluster identified by
      the prefix of the backup name of the form
      `projects/{project}/instances/{instance}/clusters/{cluster}`.
    updateMask: Required. A mask specifying which fields (e.g. `expire_time`)
      in the Backup resource should be updated. This mask is relative to the
      Backup resource, not to the request message. The field mask must always
      be specified; this prevents any future fields from being erased
      accidentally by clients that do not know about them.
  """

  backup = _messages.MessageField('Backup', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class BigtableadminProjectsInstancesClustersBackupsSetIamPolicyRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesClustersBackupsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class BigtableadminProjectsInstancesClustersBackupsTestIamPermissionsRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesClustersBackupsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class BigtableadminProjectsInstancesClustersCreateRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesClustersCreateRequest object.

  Fields:
    cluster: A Cluster resource to be passed as the request body.
    clusterId: Required. The ID to be used when referring to the new cluster
      within its instance, e.g., just `mycluster` rather than
      `projects/myproject/instances/myinstance/clusters/mycluster`.
    parent: Required. The unique name of the instance in which to create the
      new cluster. Values are of the form
      `projects/{project}/instances/{instance}`.
  """

  cluster = _messages.MessageField('Cluster', 1)
  clusterId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class BigtableadminProjectsInstancesClustersDeleteRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesClustersDeleteRequest object.

  Fields:
    name: Required. The unique name of the cluster to be deleted. Values are
      of the form
      `projects/{project}/instances/{instance}/clusters/{cluster}`.
  """

  name = _messages.StringField(1, required=True)


class BigtableadminProjectsInstancesClustersGetRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesClustersGetRequest object.

  Fields:
    name: Required. The unique name of the requested cluster. Values are of
      the form `projects/{project}/instances/{instance}/clusters/{cluster}`.
  """

  name = _messages.StringField(1, required=True)


class BigtableadminProjectsInstancesClustersHotTabletsListRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesClustersHotTabletsListRequest object.

  Fields:
    endTime: The end time to list hot tablets.
    pageSize: Maximum number of results per page. A page_size that is empty or
      zero lets the server choose the number of items to return. A page_size
      which is strictly positive will return at most that many items. A
      negative page_size will cause an error. Following the first request,
      subsequent paginated calls do not need a page_size field. If a page_size
      is set in subsequent calls, it must match the page_size given in the
      first request.
    pageToken: The value of `next_page_token` returned by a previous call.
    parent: Required. The cluster name to list hot tablets. Value is in the
      following form:
      `projects/{project}/instances/{instance}/clusters/{cluster}`.
    startTime: The start time to list hot tablets. The hot tablets in the
      response will have start times between the requested start time and end
      time. Start time defaults to Now if it is unset, and end time defaults
      to Now - 24 hours if it is unset. The start time should be less than the
      end time, and the maximum allowed time range between start time and end
      time is 48 hours. Start time and end time should have values between Now
      and Now - 14 days.
  """

  endTime = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  startTime = _messages.StringField(5)


class BigtableadminProjectsInstancesClustersListRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesClustersListRequest object.

  Fields:
    pageToken: DEPRECATED: This field is unused and ignored.
    parent: Required. The unique name of the instance for which a list of
      clusters is requested. Values are of the form
      `projects/{project}/instances/{instance}`. Use `{instance} = '-'` to
      list Clusters for all Instances in a project, e.g.,
      `projects/myproject/instances/-`.
  """

  pageToken = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)


class BigtableadminProjectsInstancesClustersPartialUpdateClusterRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesClustersPartialUpdateClusterRequest
  object.

  Fields:
    cluster: A Cluster resource to be passed as the request body.
    name: The unique name of the cluster. Values are of the form
      `projects/{project}/instances/{instance}/clusters/a-z*`.
    updateMask: Required. The subset of Cluster fields which should be
      replaced.
  """

  cluster = _messages.MessageField('Cluster', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class BigtableadminProjectsInstancesDeleteRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesDeleteRequest object.

  Fields:
    name: Required. The unique name of the instance to be deleted. Values are
      of the form `projects/{project}/instances/{instance}`.
  """

  name = _messages.StringField(1, required=True)


class BigtableadminProjectsInstancesGetIamPolicyRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BigtableadminProjectsInstancesGetRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesGetRequest object.

  Fields:
    name: Required. The unique name of the requested instance. Values are of
      the form `projects/{project}/instances/{instance}`.
  """

  name = _messages.StringField(1, required=True)


class BigtableadminProjectsInstancesListRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesListRequest object.

  Fields:
    pageToken: DEPRECATED: This field is unused and ignored.
    parent: Required. The unique name of the project for which a list of
      instances is requested. Values are of the form `projects/{project}`.
  """

  pageToken = _messages.StringField(1)
  parent = _messages.StringField(2, required=True)


class BigtableadminProjectsInstancesPartialUpdateInstanceRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesPartialUpdateInstanceRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    name: The unique name of the instance. Values are of the form
      `projects/{project}/instances/a-z+[a-z0-9]`.
    updateMask: Required. The subset of Instance fields which should be
      replaced. Must be explicitly set.
  """

  instance = _messages.MessageField('Instance', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class BigtableadminProjectsInstancesSetIamPolicyRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class BigtableadminProjectsInstancesTablesCheckConsistencyRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesCheckConsistencyRequest object.

  Fields:
    checkConsistencyRequest: A CheckConsistencyRequest resource to be passed
      as the request body.
    name: Required. The unique name of the Table for which to check
      replication consistency. Values are of the form
      `projects/{project}/instances/{instance}/tables/{table}`.
  """

  checkConsistencyRequest = _messages.MessageField('CheckConsistencyRequest', 1)
  name = _messages.StringField(2, required=True)


class BigtableadminProjectsInstancesTablesCreateRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesCreateRequest object.

  Fields:
    createTableRequest: A CreateTableRequest resource to be passed as the
      request body.
    parent: Required. The unique name of the instance in which to create the
      table. Values are of the form `projects/{project}/instances/{instance}`.
  """

  createTableRequest = _messages.MessageField('CreateTableRequest', 1)
  parent = _messages.StringField(2, required=True)


class BigtableadminProjectsInstancesTablesDeleteRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesDeleteRequest object.

  Fields:
    name: Required. The unique name of the table to be deleted. Values are of
      the form `projects/{project}/instances/{instance}/tables/{table}`.
  """

  name = _messages.StringField(1, required=True)


class BigtableadminProjectsInstancesTablesDropRowRangeRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesDropRowRangeRequest object.

  Fields:
    dropRowRangeRequest: A DropRowRangeRequest resource to be passed as the
      request body.
    name: Required. The unique name of the table on which to drop a range of
      rows. Values are of the form
      `projects/{project}/instances/{instance}/tables/{table}`.
  """

  dropRowRangeRequest = _messages.MessageField('DropRowRangeRequest', 1)
  name = _messages.StringField(2, required=True)


class BigtableadminProjectsInstancesTablesGenerateConsistencyTokenRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesGenerateConsistencyTokenRequest
  object.

  Fields:
    generateConsistencyTokenRequest: A GenerateConsistencyTokenRequest
      resource to be passed as the request body.
    name: Required. The unique name of the Table for which to create a
      consistency token. Values are of the form
      `projects/{project}/instances/{instance}/tables/{table}`.
  """

  generateConsistencyTokenRequest = _messages.MessageField('GenerateConsistencyTokenRequest', 1)
  name = _messages.StringField(2, required=True)


class BigtableadminProjectsInstancesTablesGetIamPolicyRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BigtableadminProjectsInstancesTablesGetRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesGetRequest object.

  Enums:
    ViewValueValuesEnum: The view to be applied to the returned table's
      fields. Defaults to `SCHEMA_VIEW` if unspecified.

  Fields:
    name: Required. The unique name of the requested table. Values are of the
      form `projects/{project}/instances/{instance}/tables/{table}`.
    view: The view to be applied to the returned table's fields. Defaults to
      `SCHEMA_VIEW` if unspecified.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The view to be applied to the returned table's fields. Defaults to
    `SCHEMA_VIEW` if unspecified.

    Values:
      VIEW_UNSPECIFIED: Uses the default view for each method as documented in
        its request.
      NAME_ONLY: Only populates `name`.
      SCHEMA_VIEW: Only populates `name` and fields related to the table's
        schema.
      REPLICATION_VIEW: Only populates `name` and fields related to the
        table's replication state.
      ENCRYPTION_VIEW: Only populates `name` and fields related to the table's
        encryption state.
      STATS_VIEW: Only populates `name` and fields related to the table's
        stats (e.g. TableStats and ColumnFamilyStats).
      FULL: Populates all fields except for stats. See STATS_VIEW to request
        stats.
    """
    VIEW_UNSPECIFIED = 0
    NAME_ONLY = 1
    SCHEMA_VIEW = 2
    REPLICATION_VIEW = 3
    ENCRYPTION_VIEW = 4
    STATS_VIEW = 5
    FULL = 6

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class BigtableadminProjectsInstancesTablesListRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesListRequest object.

  Enums:
    ViewValueValuesEnum: The view to be applied to the returned tables'
      fields. Only NAME_ONLY view (default), REPLICATION_VIEW and
      ENCRYPTION_VIEW are supported.

  Fields:
    pageSize: Maximum number of results per page. A page_size of zero lets the
      server choose the number of items to return. A page_size which is
      strictly positive will return at most that many items. A negative
      page_size will cause an error. Following the first request, subsequent
      paginated calls are not required to pass a page_size. If a page_size is
      set in subsequent calls, it must match the page_size given in the first
      request.
    pageToken: The value of `next_page_token` returned by a previous call.
    parent: Required. The unique name of the instance for which tables should
      be listed. Values are of the form
      `projects/{project}/instances/{instance}`.
    view: The view to be applied to the returned tables' fields. Only
      NAME_ONLY view (default), REPLICATION_VIEW and ENCRYPTION_VIEW are
      supported.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The view to be applied to the returned tables' fields. Only NAME_ONLY
    view (default), REPLICATION_VIEW and ENCRYPTION_VIEW are supported.

    Values:
      VIEW_UNSPECIFIED: Uses the default view for each method as documented in
        its request.
      NAME_ONLY: Only populates `name`.
      SCHEMA_VIEW: Only populates `name` and fields related to the table's
        schema.
      REPLICATION_VIEW: Only populates `name` and fields related to the
        table's replication state.
      ENCRYPTION_VIEW: Only populates `name` and fields related to the table's
        encryption state.
      STATS_VIEW: Only populates `name` and fields related to the table's
        stats (e.g. TableStats and ColumnFamilyStats).
      FULL: Populates all fields except for stats. See STATS_VIEW to request
        stats.
    """
    VIEW_UNSPECIFIED = 0
    NAME_ONLY = 1
    SCHEMA_VIEW = 2
    REPLICATION_VIEW = 3
    ENCRYPTION_VIEW = 4
    STATS_VIEW = 5
    FULL = 6

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 4)


class BigtableadminProjectsInstancesTablesModifyColumnFamiliesRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesModifyColumnFamiliesRequest
  object.

  Fields:
    modifyColumnFamiliesRequest: A ModifyColumnFamiliesRequest resource to be
      passed as the request body.
    name: Required. The unique name of the table whose families should be
      modified. Values are of the form
      `projects/{project}/instances/{instance}/tables/{table}`.
  """

  modifyColumnFamiliesRequest = _messages.MessageField('ModifyColumnFamiliesRequest', 1)
  name = _messages.StringField(2, required=True)


class BigtableadminProjectsInstancesTablesPatchRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesPatchRequest object.

  Fields:
    name: The unique name of the table. Values are of the form
      `projects/{project}/instances/{instance}/tables/_a-zA-Z0-9*`. Views:
      `NAME_ONLY`, `SCHEMA_VIEW`, `REPLICATION_VIEW`, `STATS_VIEW`, `FULL`
    table: A Table resource to be passed as the request body.
    updateMask: Required. The list of fields to update. A mask specifying
      which fields (e.g. `change_stream_config`) in the `table` field should
      be updated. This mask is relative to the `table` field, not to the
      request message. The wildcard (*) path is currently not supported.
      Currently UpdateTable is only supported for the following fields: *
      `change_stream_config` * `change_stream_config.retention_period` *
      `deletion_protection` If `column_families` is set in `update_mask`, it
      will return an UNIMPLEMENTED error.
  """

  name = _messages.StringField(1, required=True)
  table = _messages.MessageField('Table', 2)
  updateMask = _messages.StringField(3)


class BigtableadminProjectsInstancesTablesRestoreRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesRestoreRequest object.

  Fields:
    parent: Required. The name of the instance in which to create the restored
      table. Values are of the form `projects//instances/`.
    restoreTableRequest: A RestoreTableRequest resource to be passed as the
      request body.
  """

  parent = _messages.StringField(1, required=True)
  restoreTableRequest = _messages.MessageField('RestoreTableRequest', 2)


class BigtableadminProjectsInstancesTablesSetIamPolicyRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class BigtableadminProjectsInstancesTablesTestIamPermissionsRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class BigtableadminProjectsInstancesTablesUndeleteRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesUndeleteRequest object.

  Fields:
    name: Required. The unique name of the table to be restored. Values are of
      the form `projects/{project}/instances/{instance}/tables/{table}`.
    undeleteTableRequest: A UndeleteTableRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteTableRequest = _messages.MessageField('UndeleteTableRequest', 2)


class BigtableadminProjectsInstancesTablesViewsCreateRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesViewsCreateRequest object.

  Fields:
    parent: A string attribute.
    view: A View resource to be passed as the request body.
    viewId: A string attribute.
  """

  parent = _messages.StringField(1, required=True)
  view = _messages.MessageField('View', 2)
  viewId = _messages.StringField(3)


class BigtableadminProjectsInstancesTablesViewsDeleteRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesViewsDeleteRequest object.

  Fields:
    etag: Optional. The current etag of the view. If an etag is provided and
      does not match the current etag of the view, deletion will be blocked
      and an ABORTED error will be returned.
    name: Required. The unique name of the view to be deleted. Values are of
      the form
      `projects/{project}/instances/{instance}/tables/{table}/views/{view}`.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class BigtableadminProjectsInstancesTablesViewsGetRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesViewsGetRequest object.

  Enums:
    ViewValueValuesEnum: The resource_view to be applied to the returned
      views' fields. Default to BASIC.

  Fields:
    name: Required. The unique name of the requested view. Values are of the
      form
      `projects/{project}/instances/{instance}/tables/{table}/views/{view}`.
    view: The resource_view to be applied to the returned views' fields.
      Default to BASIC.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The resource_view to be applied to the returned views' fields. Default
    to BASIC.

    Values:
      RESPONSE_VIEW_UNSPECIFIED: Uses the default view for each method as
        documented in the request
      NAME_ONLY: Only populates `name`.
      BASIC: Only populates the view's basic metadata. This includes: name,
        view_type, deletion_protection, create_time, update_time.
      FULL: Populates every fields
    """
    RESPONSE_VIEW_UNSPECIFIED = 0
    NAME_ONLY = 1
    BASIC = 2
    FULL = 3

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class BigtableadminProjectsInstancesTablesViewsListRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesViewsListRequest object.

  Enums:
    ViewValueValuesEnum: The resource_view to be applied to the returned
      views' fields. Default to NAME_ONLY.

  Fields:
    pageSize: Maximum number of results per page. A page_size of zero lets the
      server choose the number of items to return. A page_size which is
      strictly positive will return at most that many items. A negative
      page_size will cause an error. Following the first request, subsequent
      paginated calls are not required to pass a page_size. If a page_size is
      set in subsequent calls, it must match the page_size given in the first
      request.
    pageToken: The value of `next_page_token` returned by a previous call.
    parent: Required. The unique name of the table for which views should be
      listed. Values are of the form
      `projects/{project}/instances/{instance}/tables/{table}`.
    view: The resource_view to be applied to the returned views' fields.
      Default to NAME_ONLY.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The resource_view to be applied to the returned views' fields. Default
    to NAME_ONLY.

    Values:
      RESPONSE_VIEW_UNSPECIFIED: Uses the default view for each method as
        documented in the request
      NAME_ONLY: Only populates `name`.
      BASIC: Only populates the view's basic metadata. This includes: name,
        view_type, deletion_protection, create_time, update_time.
      FULL: Populates every fields
    """
    RESPONSE_VIEW_UNSPECIFIED = 0
    NAME_ONLY = 1
    BASIC = 2
    FULL = 3

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 4)


class BigtableadminProjectsInstancesTablesViewsPatchRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTablesViewsPatchRequest object.

  Fields:
    name: The name of the view. Values are of the form
      `projects/{project}/instances/{instance}/tables/{table}/views/{view}`
    updateMask: Optional. The list of fields to update. A mask specifying
      which fields in the View resource should be updated. This mask is
      relative to the View resource, not to the request message. A field will
      be overwritten if it is in the mask. If empty, all fields set in the
      request will be overwritten. A special value `*` means to overwrite all
      fields (including fields not set in the request).
    view: A View resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  view = _messages.MessageField('View', 3)


class BigtableadminProjectsInstancesTestIamPermissionsRequest(_messages.Message):
  r"""A BigtableadminProjectsInstancesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class BigtableadminProjectsLocationsGetRequest(_messages.Message):
  r"""A BigtableadminProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class BigtableadminProjectsLocationsListRequest(_messages.Message):
  r"""A BigtableadminProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class ChangeStreamConfig(_messages.Message):
  r"""Change stream configuration.

  Fields:
    retentionPeriod: How long the change stream should be retained. Change
      stream data older than the retention period will not be returned when
      reading the change stream from the table. Values must be at least 1 day
      and at most 7 days, and will be truncated to microsecond granularity.
  """

  retentionPeriod = _messages.StringField(1)


class CheckConsistencyRequest(_messages.Message):
  r"""Request message for
  google.bigtable.admin.v2.BigtableTableAdmin.CheckConsistency

  Fields:
    consistencyToken: Required. The token created using
      GenerateConsistencyToken for the Table.
  """

  consistencyToken = _messages.StringField(1)


class CheckConsistencyResponse(_messages.Message):
  r"""Response message for
  google.bigtable.admin.v2.BigtableTableAdmin.CheckConsistency

  Fields:
    consistent: True only if the token is consistent. A token is consistent if
      replication has caught up with the restrictions specified in the
      request.
  """

  consistent = _messages.BooleanField(1)


class Cluster(_messages.Message):
  r"""A resizable group of nodes in a particular cloud location, capable of
  serving all Tables in the parent Instance.

  Enums:
    DefaultStorageTypeValueValuesEnum: Immutable. The type of storage used by
      this cluster to serve its parent instance's tables, unless explicitly
      overridden.
    StateValueValuesEnum: Output only. The current state of the cluster.

  Fields:
    clusterConfig: Configuration for this cluster.
    defaultStorageType: Immutable. The type of storage used by this cluster to
      serve its parent instance's tables, unless explicitly overridden.
    encryptionConfig: Immutable. The encryption configuration for CMEK-
      protected clusters.
    location: Immutable. The location where this cluster's nodes and storage
      reside. For best performance, clients should be located as close as
      possible to this cluster. Currently only zones are supported, so values
      should be of the form `projects/{project}/locations/{zone}`.
    name: The unique name of the cluster. Values are of the form
      `projects/{project}/instances/{instance}/clusters/a-z*`.
    serveNodes: The number of nodes in the cluster. If no value is set, Cloud
      Bigtable automatically allocates nodes based on your data footprint and
      optimized for 50% storage utilization.
    state: Output only. The current state of the cluster.
  """

  class DefaultStorageTypeValueValuesEnum(_messages.Enum):
    r"""Immutable. The type of storage used by this cluster to serve its
    parent instance's tables, unless explicitly overridden.

    Values:
      STORAGE_TYPE_UNSPECIFIED: The user did not specify a storage type.
      SSD: Flash (SSD) storage should be used.
      HDD: Magnetic drive (HDD) storage should be used.
    """
    STORAGE_TYPE_UNSPECIFIED = 0
    SSD = 1
    HDD = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the cluster.

    Values:
      STATE_NOT_KNOWN: The state of the cluster could not be determined.
      READY: The cluster has been successfully created and is ready to serve
        requests.
      CREATING: The cluster is currently being created, and may be destroyed
        if the creation process encounters an error. A cluster may not be able
        to serve requests while being created.
      RESIZING: The cluster is currently being resized, and may revert to its
        previous node count if the process encounters an error. A cluster is
        still capable of serving requests while being resized, but may exhibit
        performance as if its number of allocated nodes is between the
        starting and requested states.
      DISABLED: The cluster has no backing nodes. The data (tables) still
        exist, but no operations can be performed on the cluster.
    """
    STATE_NOT_KNOWN = 0
    READY = 1
    CREATING = 2
    RESIZING = 3
    DISABLED = 4

  clusterConfig = _messages.MessageField('ClusterConfig', 1)
  defaultStorageType = _messages.EnumField('DefaultStorageTypeValueValuesEnum', 2)
  encryptionConfig = _messages.MessageField('EncryptionConfig', 3)
  location = _messages.StringField(4)
  name = _messages.StringField(5)
  serveNodes = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  state = _messages.EnumField('StateValueValuesEnum', 7)


class ClusterAutoscalingConfig(_messages.Message):
  r"""Autoscaling config for a cluster.

  Fields:
    autoscalingLimits: Required. Autoscaling limits for this cluster.
    autoscalingTargets: Required. Autoscaling targets for this cluster.
  """

  autoscalingLimits = _messages.MessageField('AutoscalingLimits', 1)
  autoscalingTargets = _messages.MessageField('AutoscalingTargets', 2)


class ClusterConfig(_messages.Message):
  r"""Configuration for a cluster.

  Fields:
    clusterAutoscalingConfig: Autoscaling configuration for this cluster.
  """

  clusterAutoscalingConfig = _messages.MessageField('ClusterAutoscalingConfig', 1)


class ClusterState(_messages.Message):
  r"""The state of a table's data in a particular cluster.

  Enums:
    ReplicationStateValueValuesEnum: Output only. The state of replication for
      the table in this cluster.

  Fields:
    encryptionInfo: Output only. The encryption information for the table in
      this cluster. If the encryption key protecting this resource is customer
      managed, then its version can be rotated in Cloud Key Management Service
      (Cloud KMS). The primary version of the key and its status will be
      reflected here when changes propagate from Cloud KMS.
    replicationState: Output only. The state of replication for the table in
      this cluster.
  """

  class ReplicationStateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of replication for the table in this cluster.

    Values:
      STATE_NOT_KNOWN: The replication state of the table is unknown in this
        cluster.
      INITIALIZING: The cluster was recently created, and the table must
        finish copying over pre-existing data from other clusters before it
        can begin receiving live replication updates and serving Data API
        requests.
      PLANNED_MAINTENANCE: The table is temporarily unable to serve Data API
        requests from this cluster due to planned internal maintenance.
      UNPLANNED_MAINTENANCE: The table is temporarily unable to serve Data API
        requests from this cluster due to unplanned or emergency maintenance.
      READY: The table can serve Data API requests from this cluster.
        Depending on replication delay, reads may not immediately reflect the
        state of the table in other clusters.
      READY_OPTIMIZING: The table is fully created and ready for use after a
        restore, and is being optimized for performance. When optimizations
        are complete, the table will transition to `READY` state.
    """
    STATE_NOT_KNOWN = 0
    INITIALIZING = 1
    PLANNED_MAINTENANCE = 2
    UNPLANNED_MAINTENANCE = 3
    READY = 4
    READY_OPTIMIZING = 5

  encryptionInfo = _messages.MessageField('EncryptionInfo', 1, repeated=True)
  replicationState = _messages.EnumField('ReplicationStateValueValuesEnum', 2)


class ColumnFamily(_messages.Message):
  r"""A set of columns within a table which share a common configuration.

  Fields:
    gcRule: Garbage collection rule specified as a protobuf. Must serialize to
      at most 500 bytes. NOTE: Garbage collection executes opportunistically
      in the background, and so it's possible for reads to return a cell even
      if it matches the active GC expression for its family.
    stats: Only available with STATS_VIEW, this includes summary statistics
      about column family contents. For statistics over an entire table, see
      TableStats above.
  """

  gcRule = _messages.MessageField('GcRule', 1)
  stats = _messages.MessageField('ColumnFamilyStats', 2)


class ColumnFamilyStats(_messages.Message):
  r"""Approximate statistics related to a single column family within a table.
  This information may change rapidly, interpreting these values at a point in
  time may already preset out-of-date information. Everything below is
  approximate, unless otherwise specified.

  Fields:
    averageCellsPerColumn: How many cells are present per column qualifier in
      this column family, averaged over all rows containing any column in the
      column family. e.g. For column family "family" in a table with 3 rows: *
      A row with 3 cells in "family:col" and 1 cell in "other:col" (3 cells /
      1 column in "family") * A row with 1 cell in "family:col", 7 cells in
      "family:other_col", and 7 cells in "other:data" (8 cells / 2 columns in
      "family") * A row with 3 cells in "other:col" (0 columns in "family",
      "family" not present) would report (3 + 8 + 0)/(1 + 2 + 0) = 3.66 in
      this field.
    averageColumnsPerRow: How many column qualifiers are present in this
      column family, averaged over all rows in the table. e.g. For column
      family "family" in a table with 3 rows: * A row with cells in
      "family:col" and "other:col" (1 column in "family") * A row with cells
      in "family:col", "family:other_col", and "other:data" (2 columns in
      "family") * A row with cells in "other:col" (0 columns in "family",
      "family" not present) would report (1 + 2 + 0)/3 = 1.5 in this field.
    logicalDataBytes: How much space the data in the column family occupies.
      This is roughly how many bytes would be needed to read the contents of
      the entire column family (e.g. by streaming all contents out).
  """

  averageCellsPerColumn = _messages.FloatField(1)
  averageColumnsPerRow = _messages.FloatField(2)
  logicalDataBytes = _messages.IntegerField(3)


class CopyBackupMetadata(_messages.Message):
  r"""Metadata type for the google.longrunning.Operation returned by
  CopyBackup.

  Fields:
    name: The name of the backup being created through the copy operation.
      Values are of the form `projects//instances//clusters//backups/`.
    progress: The progress of the CopyBackup operation.
    sourceBackupInfo: Information about the source backup that is being copied
      from.
  """

  name = _messages.StringField(1)
  progress = _messages.MessageField('OperationProgress', 2)
  sourceBackupInfo = _messages.MessageField('BackupInfo', 3)


class CopyBackupRequest(_messages.Message):
  r"""The request for CopyBackup.

  Fields:
    backupId: Required. The id of the new backup. The `backup_id` along with
      `parent` are combined as {parent}/backups/{backup_id} to create the full
      backup name, of the form: `projects/{project}/instances/{instance}/clust
      ers/{cluster}/backups/{backup_id}`. This string must be between 1 and 50
      characters in length and match the regex _a-zA-Z0-9*.
    expireTime: Required. Required. The expiration time of the copied backup
      with microsecond granularity that must be at least 6 hours and at most
      30 days from the time the request is received. Once the `expire_time`
      has passed, Cloud Bigtable will delete the backup and free the resources
      used by the backup.
    sourceBackup: Required. The source backup to be copied from. The source
      backup needs to be in READY state for it to be copied. Copying a copied
      backup is not allowed. Once CopyBackup is in progress, the source backup
      cannot be deleted or cleaned up on expiration until CopyBackup is
      finished. Values are of the form:
      `projects//instances//clusters//backups/`.
  """

  backupId = _messages.StringField(1)
  expireTime = _messages.StringField(2)
  sourceBackup = _messages.StringField(3)


class CreateBackupMetadata(_messages.Message):
  r"""Metadata type for the operation returned by CreateBackup.

  Fields:
    endTime: If set, the time at which this operation finished or was
      cancelled.
    name: The name of the backup being created.
    sourceTable: The name of the table the backup is created from.
    startTime: The time at which this operation started.
  """

  endTime = _messages.StringField(1)
  name = _messages.StringField(2)
  sourceTable = _messages.StringField(3)
  startTime = _messages.StringField(4)


class CreateClusterMetadata(_messages.Message):
  r"""The metadata for the Operation returned by CreateCluster.

  Messages:
    TablesValue: Keys: the full `name` of each table that existed in the
      instance when CreateCluster was first called, i.e.
      `projects//instances//tables/`. Any table added to the instance by a
      later API call will be created in the new cluster by that API call, not
      this one. Values: information on how much of a table's data has been
      copied to the newly-created cluster so far.

  Fields:
    finishTime: The time at which the operation failed or was completed
      successfully.
    originalRequest: The request that prompted the initiation of this
      CreateCluster operation.
    requestTime: The time at which the original request was received.
    tables: Keys: the full `name` of each table that existed in the instance
      when CreateCluster was first called, i.e.
      `projects//instances//tables/`. Any table added to the instance by a
      later API call will be created in the new cluster by that API call, not
      this one. Values: information on how much of a table's data has been
      copied to the newly-created cluster so far.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TablesValue(_messages.Message):
    r"""Keys: the full `name` of each table that existed in the instance when
    CreateCluster was first called, i.e. `projects//instances//tables/`. Any
    table added to the instance by a later API call will be created in the new
    cluster by that API call, not this one. Values: information on how much of
    a table's data has been copied to the newly-created cluster so far.

    Messages:
      AdditionalProperty: An additional property for a TablesValue object.

    Fields:
      additionalProperties: Additional properties of type TablesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TablesValue object.

      Fields:
        key: Name of the additional property.
        value: A TableProgress attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('TableProgress', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  finishTime = _messages.StringField(1)
  originalRequest = _messages.MessageField('CreateClusterRequest', 2)
  requestTime = _messages.StringField(3)
  tables = _messages.MessageField('TablesValue', 4)


class CreateClusterRequest(_messages.Message):
  r"""Request message for BigtableInstanceAdmin.CreateCluster.

  Fields:
    cluster: Required. The cluster to be created. Fields marked `OutputOnly`
      must be left blank.
    clusterId: Required. The ID to be used when referring to the new cluster
      within its instance, e.g., just `mycluster` rather than
      `projects/myproject/instances/myinstance/clusters/mycluster`.
    parent: Required. The unique name of the instance in which to create the
      new cluster. Values are of the form
      `projects/{project}/instances/{instance}`.
  """

  cluster = _messages.MessageField('Cluster', 1)
  clusterId = _messages.StringField(2)
  parent = _messages.StringField(3)


class CreateInstanceMetadata(_messages.Message):
  r"""The metadata for the Operation returned by CreateInstance.

  Fields:
    finishTime: The time at which the operation failed or was completed
      successfully.
    originalRequest: The request that prompted the initiation of this
      CreateInstance operation.
    requestTime: The time at which the original request was received.
  """

  finishTime = _messages.StringField(1)
  originalRequest = _messages.MessageField('CreateInstanceRequest', 2)
  requestTime = _messages.StringField(3)


class CreateInstanceRequest(_messages.Message):
  r"""Request message for BigtableInstanceAdmin.CreateInstance.

  Messages:
    ClustersValue: Required. The clusters to be created within the instance,
      mapped by desired cluster ID, e.g., just `mycluster` rather than
      `projects/myproject/instances/myinstance/clusters/mycluster`. Fields
      marked `OutputOnly` must be left blank.

  Fields:
    clusters: Required. The clusters to be created within the instance, mapped
      by desired cluster ID, e.g., just `mycluster` rather than
      `projects/myproject/instances/myinstance/clusters/mycluster`. Fields
      marked `OutputOnly` must be left blank.
    instance: Required. The instance to create. Fields marked `OutputOnly`
      must be left blank.
    instanceId: Required. The ID to be used when referring to the new instance
      within its project, e.g., just `myinstance` rather than
      `projects/myproject/instances/myinstance`.
    parent: Required. The unique name of the project in which to create the
      new instance. Values are of the form `projects/{project}`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ClustersValue(_messages.Message):
    r"""Required. The clusters to be created within the instance, mapped by
    desired cluster ID, e.g., just `mycluster` rather than
    `projects/myproject/instances/myinstance/clusters/mycluster`. Fields
    marked `OutputOnly` must be left blank.

    Messages:
      AdditionalProperty: An additional property for a ClustersValue object.

    Fields:
      additionalProperties: Additional properties of type ClustersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ClustersValue object.

      Fields:
        key: Name of the additional property.
        value: A Cluster attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Cluster', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  clusters = _messages.MessageField('ClustersValue', 1)
  instance = _messages.MessageField('Instance', 2)
  instanceId = _messages.StringField(3)
  parent = _messages.StringField(4)


class CreateTableRequest(_messages.Message):
  r"""Request message for
  google.bigtable.admin.v2.BigtableTableAdmin.CreateTable

  Fields:
    initialSplits: The optional list of row keys that will be used to
      initially split the table into several tablets (tablets are similar to
      HBase regions). Given two split keys, `s1` and `s2`, three tablets will
      be created, spanning the key ranges: `[, s1), [s1, s2), [s2, )`.
      Example: * Row keys := `["a", "apple", "custom", "customer_1",
      "customer_2",` `"other", "zz"]` * initial_split_keys := `["apple",
      "customer_1", "customer_2", "other"]` * Key assignment: - Tablet 1 `[,
      apple) => {"a"}.` - Tablet 2 `[apple, customer_1) => {"apple",
      "custom"}.` - Tablet 3 `[customer_1, customer_2) => {"customer_1"}.` -
      Tablet 4 `[customer_2, other) => {"customer_2"}.` - Tablet 5 `[other, )
      => {"other", "zz"}.`
    table: Required. The Table to create.
    tableId: Required. The name by which the new table should be referred to
      within the parent instance, e.g., `foobar` rather than
      `{parent}/tables/foobar`. Maximum 50 characters.
  """

  initialSplits = _messages.MessageField('Split', 1, repeated=True)
  table = _messages.MessageField('Table', 2)
  tableId = _messages.StringField(3)


class DropRowRangeRequest(_messages.Message):
  r"""Request message for
  google.bigtable.admin.v2.BigtableTableAdmin.DropRowRange

  Fields:
    deleteAllDataFromTable: Delete all rows in the table. Setting this to
      false is a no-op.
    rowKeyPrefix: Delete all rows that start with this row key prefix. Prefix
      cannot be zero length.
  """

  deleteAllDataFromTable = _messages.BooleanField(1)
  rowKeyPrefix = _messages.BytesField(2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EncryptionConfig(_messages.Message):
  r"""Cloud Key Management Service (Cloud KMS) settings for a CMEK-protected
  cluster.

  Fields:
    kmsKeyName: Describes the Cloud KMS encryption key that will be used to
      protect the destination Bigtable cluster. The requirements for this key
      are: 1) The Cloud Bigtable service account associated with the project
      that contains this cluster must be granted the
      `cloudkms.cryptoKeyEncrypterDecrypter` role on the CMEK key. 2) Only
      regional keys can be used and the region of the CMEK key must match the
      region of the cluster. Values are of the form `projects/{project}/locati
      ons/{location}/keyRings/{keyring}/cryptoKeys/{key}`
  """

  kmsKeyName = _messages.StringField(1)


class EncryptionInfo(_messages.Message):
  r"""Encryption information for a given resource. If this resource is
  protected with customer managed encryption, the in-use Cloud Key Management
  Service (Cloud KMS) key version is specified along with its status.

  Enums:
    EncryptionTypeValueValuesEnum: Output only. The type of encryption used to
      protect this resource.

  Fields:
    encryptionStatus: Output only. The status of encrypt/decrypt calls on
      underlying data for this resource. Regardless of status, the existing
      data is always encrypted at rest.
    encryptionType: Output only. The type of encryption used to protect this
      resource.
    kmsKeyVersion: Output only. The version of the Cloud KMS key specified in
      the parent cluster that is in use for the data underlying this table.
  """

  class EncryptionTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of encryption used to protect this resource.

    Values:
      ENCRYPTION_TYPE_UNSPECIFIED: Encryption type was not specified, though
        data at rest remains encrypted.
      GOOGLE_DEFAULT_ENCRYPTION: The data backing this resource is encrypted
        at rest with a key that is fully managed by Google. No key version or
        status will be populated. This is the default state.
      CUSTOMER_MANAGED_ENCRYPTION: The data backing this resource is encrypted
        at rest with a key that is managed by the customer. The in-use version
        of the key and its status are populated for CMEK-protected tables.
        CMEK-protected backups are pinned to the key version that was in use
        at the time the backup was taken. This key version is populated but
        its status is not tracked and is reported as `UNKNOWN`.
    """
    ENCRYPTION_TYPE_UNSPECIFIED = 0
    GOOGLE_DEFAULT_ENCRYPTION = 1
    CUSTOMER_MANAGED_ENCRYPTION = 2

  encryptionStatus = _messages.MessageField('Status', 1)
  encryptionType = _messages.EnumField('EncryptionTypeValueValuesEnum', 2)
  kmsKeyVersion = _messages.StringField(3)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class FamilySubsets(_messages.Message):
  r"""Subsets of a column family that are included in this View.

  Fields:
    qualifierPrefixes: Prefixes for qualifiers to be included in the View.
      Every qualifier starting with one of these prefixes is included in the
      View. To provide access to all qualifiers, include the empty string as a
      prefix ("").
    qualifiers: Individual exact column qualifiers to be included in the View.
  """

  qualifierPrefixes = _messages.BytesField(1, repeated=True)
  qualifiers = _messages.BytesField(2, repeated=True)


class GcRule(_messages.Message):
  r"""Rule for determining which cells to delete during garbage collection.

  Fields:
    intersection: Delete cells that would be deleted by every nested rule.
    maxAge: Delete cells in a column older than the given age. Values must be
      at least one millisecond, and will be truncated to microsecond
      granularity.
    maxNumVersions: Delete all cells in a column except the most recent N.
    union: Delete cells that would be deleted by any nested rule.
  """

  intersection = _messages.MessageField('Intersection', 1)
  maxAge = _messages.StringField(2)
  maxNumVersions = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  union = _messages.MessageField('Union', 4)


class GenerateConsistencyTokenRequest(_messages.Message):
  r"""Request message for
  google.bigtable.admin.v2.BigtableTableAdmin.GenerateConsistencyToken
  """



class GenerateConsistencyTokenResponse(_messages.Message):
  r"""Response message for
  google.bigtable.admin.v2.BigtableTableAdmin.GenerateConsistencyToken

  Fields:
    consistencyToken: The generated consistency token.
  """

  consistencyToken = _messages.StringField(1)


class GetIamPolicyRequest(_messages.Message):
  r"""Request message for `GetIamPolicy` method.

  Fields:
    options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
      `GetIamPolicy`.
  """

  options = _messages.MessageField('GetPolicyOptions', 1)


class GetPolicyOptions(_messages.Message):
  r"""Encapsulates settings provided to GetIamPolicy.

  Fields:
    requestedPolicyVersion: Optional. The maximum policy version that will be
      used to format the policy. Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected. Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset. The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1. To learn which resources support conditions in
      their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class HotTablet(_messages.Message):
  r"""A tablet is a defined by a start and end key and is explained in
  https://cloud.google.com/bigtable/docs/overview#architecture and
  https://cloud.google.com/bigtable/docs/performance#optimization. A Hot
  tablet is a tablet that exhibits high average cpu usage during the time
  interval from start time to end time.

  Fields:
    endKey: Tablet End Key (inclusive).
    endTime: Output only. The end time of the hot tablet.
    name: The unique name of the hot tablet. Values are of the form `projects/
      {project}/instances/{instance}/clusters/{cluster}/hotTablets/[a-zA-Z0-
      9_-]*`.
    nodeCpuUsagePercent: Output only. The average CPU usage spent by a node on
      this tablet over the start_time to end_time time range. The percentage
      is the amount of CPU used by the node to serve the tablet, from 0%
      (tablet was not interacted with) to 100% (the node spent all cycles
      serving the hot tablet).
    startKey: Tablet Start Key (inclusive).
    startTime: Output only. The start time of the hot tablet.
    tableName: Name of the table that contains the tablet. Values are of the
      form `projects/{project}/instances/{instance}/tables/_a-zA-Z0-9*`.
  """

  endKey = _messages.StringField(1)
  endTime = _messages.StringField(2)
  name = _messages.StringField(3)
  nodeCpuUsagePercent = _messages.FloatField(4, variant=_messages.Variant.FLOAT)
  startKey = _messages.StringField(5)
  startTime = _messages.StringField(6)
  tableName = _messages.StringField(7)


class Instance(_messages.Message):
  r"""A collection of Bigtable Tables and the resources that serve them. All
  tables in an instance are served from all Clusters in the instance.

  Enums:
    StateValueValuesEnum: Output only. The current state of the instance.
    TypeValueValuesEnum: The type of the instance. Defaults to `PRODUCTION`.

  Messages:
    LabelsValue: Labels are a flexible and lightweight mechanism for
      organizing cloud resources into groups that reflect a customer's
      organizational needs and deployment strategies. They can be used to
      filter resources and aggregate metrics. * Label keys must be between 1
      and 63 characters long and must conform to the regular expression:
      `\p{Ll}\p{Lo}{0,62}`. * Label values must be between 0 and 63 characters
      long and must conform to the regular expression:
      `[\p{Ll}\p{Lo}\p{N}_-]{0,63}`. * No more than 64 labels can be
      associated with a given resource. * Keys and values must both be under
      128 bytes.

  Fields:
    createTime: Output only. A commit timestamp representing when this
      Instance was created. For instances created before this field was added
      (August 2021), this value is `seconds: 0, nanos: 1`.
    displayName: Required. The descriptive name for this instance as it
      appears in UIs. Can be changed at any time, but should be kept globally
      unique to avoid confusion.
    labels: Labels are a flexible and lightweight mechanism for organizing
      cloud resources into groups that reflect a customer's organizational
      needs and deployment strategies. They can be used to filter resources
      and aggregate metrics. * Label keys must be between 1 and 63 characters
      long and must conform to the regular expression: `\p{Ll}\p{Lo}{0,62}`. *
      Label values must be between 0 and 63 characters long and must conform
      to the regular expression: `[\p{Ll}\p{Lo}\p{N}_-]{0,63}`. * No more than
      64 labels can be associated with a given resource. * Keys and values
      must both be under 128 bytes.
    name: The unique name of the instance. Values are of the form
      `projects/{project}/instances/a-z+[a-z0-9]`.
    satisfiesPzs: Output only. Reserved for future use.
    state: Output only. The current state of the instance.
    type: The type of the instance. Defaults to `PRODUCTION`.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the instance.

    Values:
      STATE_NOT_KNOWN: The state of the instance could not be determined.
      READY: The instance has been successfully created and can serve requests
        to its tables.
      CREATING: The instance is currently being created, and may be destroyed
        if the creation process encounters an error.
    """
    STATE_NOT_KNOWN = 0
    READY = 1
    CREATING = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of the instance. Defaults to `PRODUCTION`.

    Values:
      TYPE_UNSPECIFIED: The type of the instance is unspecified. If set when
        creating an instance, a `PRODUCTION` instance will be created. If set
        when updating an instance, the type will be left unchanged.
      PRODUCTION: An instance meant for production use. `serve_nodes` must be
        set on the cluster.
      DEVELOPMENT: DEPRECATED: Prefer PRODUCTION for all use cases, as it no
        longer enforces a higher minimum node count than DEVELOPMENT.
    """
    TYPE_UNSPECIFIED = 0
    PRODUCTION = 1
    DEVELOPMENT = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels are a flexible and lightweight mechanism for organizing cloud
    resources into groups that reflect a customer's organizational needs and
    deployment strategies. They can be used to filter resources and aggregate
    metrics. * Label keys must be between 1 and 63 characters long and must
    conform to the regular expression: `\p{Ll}\p{Lo}{0,62}`. * Label values
    must be between 0 and 63 characters long and must conform to the regular
    expression: `[\p{Ll}\p{Lo}\p{N}_-]{0,63}`. * No more than 64 labels can be
    associated with a given resource. * Keys and values must both be under 128
    bytes.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  satisfiesPzs = _messages.BooleanField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  type = _messages.EnumField('TypeValueValuesEnum', 7)


class Intersection(_messages.Message):
  r"""A GcRule which deletes cells matching all of the given rules.

  Fields:
    rules: Only delete cells which would be deleted by every element of
      `rules`.
  """

  rules = _messages.MessageField('GcRule', 1, repeated=True)


class ListAppProfilesResponse(_messages.Message):
  r"""Response message for BigtableInstanceAdmin.ListAppProfiles.

  Fields:
    appProfiles: The list of requested app profiles.
    failedLocations: Locations from which AppProfile information could not be
      retrieved, due to an outage or some other transient condition.
      AppProfiles from these locations may be missing from `app_profiles`.
      Values are of the form `projects//locations/`
    nextPageToken: Set if not all app profiles could be returned in a single
      response. Pass this value to `page_token` in another request to get the
      next page of results.
  """

  appProfiles = _messages.MessageField('AppProfile', 1, repeated=True)
  failedLocations = _messages.StringField(2, repeated=True)
  nextPageToken = _messages.StringField(3)


class ListBackupsResponse(_messages.Message):
  r"""The response for ListBackups.

  Fields:
    backups: The list of matching backups.
    nextPageToken: `next_page_token` can be sent in a subsequent ListBackups
      call to fetch more of the matching backups.
  """

  backups = _messages.MessageField('Backup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListClustersResponse(_messages.Message):
  r"""Response message for BigtableInstanceAdmin.ListClusters.

  Fields:
    clusters: The list of requested clusters.
    failedLocations: Locations from which Cluster information could not be
      retrieved, due to an outage or some other transient condition. Clusters
      from these locations may be missing from `clusters`, or may only have
      partial information returned. Values are of the form
      `projects//locations/`
    nextPageToken: DEPRECATED: This field is unused and ignored.
  """

  clusters = _messages.MessageField('Cluster', 1, repeated=True)
  failedLocations = _messages.StringField(2, repeated=True)
  nextPageToken = _messages.StringField(3)


class ListHotTabletsResponse(_messages.Message):
  r"""Response message for BigtableInstanceAdmin.ListHotTablets.

  Fields:
    hotTablets: List of hot tablets in the tables of the requested cluster
      that fall within the requested time range. Hot tablets are ordered by
      node cpu usage percent. If there are multiple hot tablets that
      correspond to the same tablet within a 15-minute interval, only the hot
      tablet with the highest node cpu usage will be included in the response.
    nextPageToken: Set if not all hot tablets could be returned in a single
      response. Pass this value to `page_token` in another request to get the
      next page of results.
  """

  hotTablets = _messages.MessageField('HotTablet', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListInstancesResponse(_messages.Message):
  r"""Response message for BigtableInstanceAdmin.ListInstances.

  Fields:
    failedLocations: Locations from which Instance information could not be
      retrieved, due to an outage or some other transient condition. Instances
      whose Clusters are all in one of the failed locations may be missing
      from `instances`, and Instances with at least one Cluster in a failed
      location may only have partial information returned. Values are of the
      form `projects//locations/`
    instances: The list of requested instances.
    nextPageToken: DEPRECATED: This field is unused and ignored.
  """

  failedLocations = _messages.StringField(1, repeated=True)
  instances = _messages.MessageField('Instance', 2, repeated=True)
  nextPageToken = _messages.StringField(3)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListTablesResponse(_messages.Message):
  r"""Response message for
  google.bigtable.admin.v2.BigtableTableAdmin.ListTables

  Fields:
    nextPageToken: Set if not all tables could be returned in a single
      response. Pass this value to `page_token` in another request to get the
      next page of results.
    tables: The tables present in the requested instance.
  """

  nextPageToken = _messages.StringField(1)
  tables = _messages.MessageField('Table', 2, repeated=True)


class ListViewsResponse(_messages.Message):
  r"""Response message for
  google.bigtable.admin.v2.BigtableTableAdmin.ListViews

  Fields:
    nextPageToken: Set if not all tables could be returned in a single
      response. Pass this value to `page_token` in another request to get the
      next page of results.
    views: The views present in the requested table.
  """

  nextPageToken = _messages.StringField(1)
  views = _messages.MessageField('View', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Modification(_messages.Message):
  r"""A create, update, or delete of a particular column family.

  Fields:
    create: Create a new column family with the specified schema, or fail if
      one already exists with the given ID.
    drop: Drop (delete) the column family with the given ID, or fail if no
      such family exists.
    id: The ID of the column family to be modified.
    update: Update an existing column family to the specified schema, or fail
      if no column family exists with the given ID.
  """

  create = _messages.MessageField('ColumnFamily', 1)
  drop = _messages.BooleanField(2)
  id = _messages.StringField(3)
  update = _messages.MessageField('ColumnFamily', 4)


class ModifyColumnFamiliesRequest(_messages.Message):
  r"""Request message for
  google.bigtable.admin.v2.BigtableTableAdmin.ModifyColumnFamilies

  Fields:
    ignoreWarnings: If true, ignore safety checks when modifying the column
      families.
    modifications: Required. Modifications to be atomically applied to the
      specified table's families. Entries are applied in order, meaning that
      earlier modifications can be masked by later ones (in the case of
      repeated updates to the same family, for example).
  """

  ignoreWarnings = _messages.BooleanField(1)
  modifications = _messages.MessageField('Modification', 2, repeated=True)


class MultiClusterRoutingUseAny(_messages.Message):
  r"""Read/write requests are routed to the nearest cluster in the instance,
  and will fail over to the nearest cluster that is available in the event of
  transient errors or delays. Clusters in a region are considered equidistant.
  Choosing this option sacrifices read-your-writes consistency to improve
  availability.

  Enums:
    FailoverRadiusValueValuesEnum: The failover radius of this app profile,
      determining which clusters request failovers could be routed to.
      FAILOVER_RADIUS_UNSPECIFIED is interpreted as ANY_REGION. If specified
      in addition to cluster_ids, both restrictions will be applied. For
      example, let there be four clusters in the following zones: - us-east1-b
      - us-east1-c - us-east1-d - europe-west1-b If a multi-cluster app
      profile specifies the set of cluster IDs in us-east1-b, us-east1-c, and
      europe-west1-b, requests will never arrive at or fail over to us-
      east1-d. If the app profile also specifies a INITIAL_REGION_ONLY
      failover radius, requests will, in addition, only be able to fail over
      within the region of the first cluster routed to. As an example,
      requests that are first routed to us-east1-b will only be able to fail
      over to us-east1-c (since us-east1-d is not in the set of cluster IDs
      specified and europe-west1-b is in a different region). Requests that
      are first routed to europe-west1-b will not fail over at all.

  Fields:
    clusterIds: The set of clusters to route to. The order is ignored;
      clusters will be tried in order of distance. If left empty, all clusters
      are eligible.
    failoverRadius: The failover radius of this app profile, determining which
      clusters request failovers could be routed to.
      FAILOVER_RADIUS_UNSPECIFIED is interpreted as ANY_REGION. If specified
      in addition to cluster_ids, both restrictions will be applied. For
      example, let there be four clusters in the following zones: - us-east1-b
      - us-east1-c - us-east1-d - europe-west1-b If a multi-cluster app
      profile specifies the set of cluster IDs in us-east1-b, us-east1-c, and
      europe-west1-b, requests will never arrive at or fail over to us-
      east1-d. If the app profile also specifies a INITIAL_REGION_ONLY
      failover radius, requests will, in addition, only be able to fail over
      within the region of the first cluster routed to. As an example,
      requests that are first routed to us-east1-b will only be able to fail
      over to us-east1-c (since us-east1-d is not in the set of cluster IDs
      specified and europe-west1-b is in a different region). Requests that
      are first routed to europe-west1-b will not fail over at all.
    rowAffinity: Row affinity sticky routing based on the row key of the
      request. Requests that span multiple rows are routed non-
      deterministically.
  """

  class FailoverRadiusValueValuesEnum(_messages.Enum):
    r"""The failover radius of this app profile, determining which clusters
    request failovers could be routed to. FAILOVER_RADIUS_UNSPECIFIED is
    interpreted as ANY_REGION. If specified in addition to cluster_ids, both
    restrictions will be applied. For example, let there be four clusters in
    the following zones: - us-east1-b - us-east1-c - us-east1-d - europe-
    west1-b If a multi-cluster app profile specifies the set of cluster IDs in
    us-east1-b, us-east1-c, and europe-west1-b, requests will never arrive at
    or fail over to us-east1-d. If the app profile also specifies a
    INITIAL_REGION_ONLY failover radius, requests will, in addition, only be
    able to fail over within the region of the first cluster routed to. As an
    example, requests that are first routed to us-east1-b will only be able to
    fail over to us-east1-c (since us-east1-d is not in the set of cluster IDs
    specified and europe-west1-b is in a different region). Requests that are
    first routed to europe-west1-b will not fail over at all.

    Values:
      FAILOVER_RADIUS_UNSPECIFIED: No failover radius specified.
      ANY_REGION: Fail over to all clusters in the instance.
      INITIAL_REGION_ONLY: Fail over only to clusters in the same region as
        the first cluster routed to.
    """
    FAILOVER_RADIUS_UNSPECIFIED = 0
    ANY_REGION = 1
    INITIAL_REGION_ONLY = 2

  clusterIds = _messages.StringField(1, repeated=True)
  failoverRadius = _messages.EnumField('FailoverRadiusValueValuesEnum', 2)
  rowAffinity = _messages.MessageField('RowAffinity', 3)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationProgress(_messages.Message):
  r"""Encapsulates progress related information for a Cloud Bigtable long
  running operation.

  Fields:
    endTime: If set, the time at which this operation failed or was completed
      successfully.
    progressPercent: Percent completion of the operation. Values are between 0
      and 100 inclusive.
    startTime: Time the request was received.
  """

  endTime = _messages.StringField(1)
  progressPercent = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  startTime = _messages.StringField(3)


class OptimizeRestoredTableMetadata(_messages.Message):
  r"""Metadata type for the long-running operation used to track the progress
  of optimizations performed on a newly restored table. This long-running
  operation is automatically created by the system after the successful
  completion of a table restore, and cannot be cancelled.

  Fields:
    name: Name of the restored table being optimized.
    progress: The progress of the post-restore optimizations.
  """

  name = _messages.StringField(1)
  progress = _messages.MessageField('OperationProgress', 2)


class PartialUpdateClusterMetadata(_messages.Message):
  r"""The metadata for the Operation returned by PartialUpdateCluster.

  Fields:
    finishTime: The time at which the operation failed or was completed
      successfully.
    originalRequest: The original request for PartialUpdateCluster.
    requestTime: The time at which the original request was received.
  """

  finishTime = _messages.StringField(1)
  originalRequest = _messages.MessageField('PartialUpdateClusterRequest', 2)
  requestTime = _messages.StringField(3)


class PartialUpdateClusterRequest(_messages.Message):
  r"""Request message for BigtableInstanceAdmin.PartialUpdateCluster.

  Fields:
    cluster: Required. The Cluster which contains the partial updates to be
      applied, subject to the update_mask.
    updateMask: Required. The subset of Cluster fields which should be
      replaced.
  """

  cluster = _messages.MessageField('Cluster', 1)
  updateMask = _messages.StringField(2)


class PartialUpdateInstanceRequest(_messages.Message):
  r"""Request message for BigtableInstanceAdmin.PartialUpdateInstance.

  Fields:
    instance: Required. The Instance which will (partially) replace the
      current value.
    updateMask: Required. The subset of Instance fields which should be
      replaced. Must be explicitly set.
  """

  instance = _messages.MessageField('Instance', 1)
  updateMask = _messages.StringField(2)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class RestoreInfo(_messages.Message):
  r"""Information about a table restore.

  Enums:
    SourceTypeValueValuesEnum: The type of the restore source.

  Fields:
    backupInfo: Information about the backup used to restore the table. The
      backup may no longer exist.
    sourceType: The type of the restore source.
  """

  class SourceTypeValueValuesEnum(_messages.Enum):
    r"""The type of the restore source.

    Values:
      RESTORE_SOURCE_TYPE_UNSPECIFIED: No restore associated.
      BACKUP: A backup was used as the source of the restore.
    """
    RESTORE_SOURCE_TYPE_UNSPECIFIED = 0
    BACKUP = 1

  backupInfo = _messages.MessageField('BackupInfo', 1)
  sourceType = _messages.EnumField('SourceTypeValueValuesEnum', 2)


class RestoreTableMetadata(_messages.Message):
  r"""Metadata type for the long-running operation returned by RestoreTable.

  Enums:
    SourceTypeValueValuesEnum: The type of the restore source.

  Fields:
    backupInfo: A BackupInfo attribute.
    name: Name of the table being created and restored to.
    optimizeTableOperationName: If exists, the name of the long-running
      operation that will be used to track the post-restore optimization
      process to optimize the performance of the restored table. The metadata
      type of the long-running operation is OptimizeRestoreTableMetadata. The
      response type is Empty. This long-running operation may be automatically
      created by the system if applicable after the RestoreTable long-running
      operation completes successfully. This operation may not be created if
      the table is already optimized or the restore was not successful.
    progress: The progress of the RestoreTable operation.
    sourceType: The type of the restore source.
  """

  class SourceTypeValueValuesEnum(_messages.Enum):
    r"""The type of the restore source.

    Values:
      RESTORE_SOURCE_TYPE_UNSPECIFIED: No restore associated.
      BACKUP: A backup was used as the source of the restore.
    """
    RESTORE_SOURCE_TYPE_UNSPECIFIED = 0
    BACKUP = 1

  backupInfo = _messages.MessageField('BackupInfo', 1)
  name = _messages.StringField(2)
  optimizeTableOperationName = _messages.StringField(3)
  progress = _messages.MessageField('OperationProgress', 4)
  sourceType = _messages.EnumField('SourceTypeValueValuesEnum', 5)


class RestoreTableRequest(_messages.Message):
  r"""The request for RestoreTable.

  Fields:
    backup: Name of the backup from which to restore. Values are of the form
      `projects//instances//clusters//backups/`.
    tableId: Required. The id of the table to create and restore to. This
      table must not already exist. The `table_id` appended to `parent` forms
      the full table name of the form `projects//instances//tables/`.
  """

  backup = _messages.StringField(1)
  tableId = _messages.StringField(2)


class RowAffinity(_messages.Message):
  r"""If enabled, the AFE will route the request based on the row key of the
  request, rather than randomly. Instead, each row key will be assigned to a
  cluster, and will stick to that cluster. If clusters are added or removed,
  then this may affect which row keys stick to which clusters. To avoid this,
  users can specify a group cluster.
  """



class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class SingleClusterRouting(_messages.Message):
  r"""Unconditionally routes all read/write requests to a specific cluster.
  This option preserves read-your-writes consistency but does not improve
  availability.

  Fields:
    allowTransactionalWrites: Whether or not `CheckAndMutateRow` and
      `ReadModifyWriteRow` requests are allowed by this app profile. It is
      unsafe to send these requests to the same table/row/column in multiple
      clusters.
    clusterId: The cluster to which read/write requests should be routed.
  """

  allowTransactionalWrites = _messages.BooleanField(1)
  clusterId = _messages.StringField(2)


class Split(_messages.Message):
  r"""An initial split point for a newly created table.

  Fields:
    key: Row key to use as an initial tablet boundary.
  """

  key = _messages.BytesField(1)


class StandardIsolation(_messages.Message):
  r"""Standard options for isolating this app profile's traffic from other use
  cases.

  Enums:
    PriorityValueValuesEnum: The priority of requests sent using this app
      profile.

  Fields:
    priority: The priority of requests sent using this app profile.
  """

  class PriorityValueValuesEnum(_messages.Enum):
    r"""The priority of requests sent using this app profile.

    Values:
      PRIORITY_UNSPECIFIED: Default value. Mapped to PRIORITY_HIGH (the legacy
        behavior) on creation.
      PRIORITY_LOW: <no description>
      PRIORITY_MEDIUM: <no description>
      PRIORITY_HIGH: <no description>
    """
    PRIORITY_UNSPECIFIED = 0
    PRIORITY_LOW = 1
    PRIORITY_MEDIUM = 2
    PRIORITY_HIGH = 3

  priority = _messages.EnumField('PriorityValueValuesEnum', 1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class SubsetView(_messages.Message):
  r"""Defines a simple view that is a subset of the underlying Table.

  Messages:
    FamilySubsetsValue: Map from column family name to the columns in this
      family to be included in the view.

  Fields:
    familySubsets: Map from column family name to the columns in this family
      to be included in the view.
    rowPrefixes: Row prefixes to be included in the View. To provide access to
      all rows, include the empty string as a prefix ("").
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FamilySubsetsValue(_messages.Message):
    r"""Map from column family name to the columns in this family to be
    included in the view.

    Messages:
      AdditionalProperty: An additional property for a FamilySubsetsValue
        object.

    Fields:
      additionalProperties: Additional properties of type FamilySubsetsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FamilySubsetsValue object.

      Fields:
        key: Name of the additional property.
        value: A FamilySubsets attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('FamilySubsets', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  familySubsets = _messages.MessageField('FamilySubsetsValue', 1)
  rowPrefixes = _messages.BytesField(2, repeated=True)


class Table(_messages.Message):
  r"""A collection of user data indexed by row, column, and timestamp. Each
  table is served using the resources of its parent cluster.

  Enums:
    GranularityValueValuesEnum: Immutable. The granularity (i.e. `MILLIS`) at
      which timestamps are stored in this table. Timestamps not matching the
      granularity will be rejected. If unspecified at creation time, the value
      will be set to `MILLIS`. Views: `SCHEMA_VIEW`, `FULL`.

  Messages:
    ClusterStatesValue: Output only. Map from cluster ID to per-cluster table
      state. If it could not be determined whether or not the table has data
      in a particular cluster (for example, if its zone is unavailable), then
      there will be an entry for the cluster with UNKNOWN
      `replication_status`. Views: `REPLICATION_VIEW`, `ENCRYPTION_VIEW`,
      `FULL`
    ColumnFamiliesValue: The column families configured for this table, mapped
      by column family ID. Views: `SCHEMA_VIEW`, `STATS_VIEW`, `FULL`

  Fields:
    changeStreamConfig: If specified, enable the change stream on this table.
      Otherwise, the change stream is disabled and the change stream is not
      retained.
    clusterStates: Output only. Map from cluster ID to per-cluster table
      state. If it could not be determined whether or not the table has data
      in a particular cluster (for example, if its zone is unavailable), then
      there will be an entry for the cluster with UNKNOWN
      `replication_status`. Views: `REPLICATION_VIEW`, `ENCRYPTION_VIEW`,
      `FULL`
    columnFamilies: The column families configured for this table, mapped by
      column family ID. Views: `SCHEMA_VIEW`, `STATS_VIEW`, `FULL`
    deletionProtection: Set to true to make the table protected against data
      loss. i.e. deleting the following resources through Admin APIs are
      prohibited: * The table. * The column families in the table. * The
      instance containing the table. Note one can still delete the data stored
      in the table through Data APIs.
    granularity: Immutable. The granularity (i.e. `MILLIS`) at which
      timestamps are stored in this table. Timestamps not matching the
      granularity will be rejected. If unspecified at creation time, the value
      will be set to `MILLIS`. Views: `SCHEMA_VIEW`, `FULL`.
    name: The unique name of the table. Values are of the form
      `projects/{project}/instances/{instance}/tables/_a-zA-Z0-9*`. Views:
      `NAME_ONLY`, `SCHEMA_VIEW`, `REPLICATION_VIEW`, `STATS_VIEW`, `FULL`
    restoreInfo: Output only. If this table was restored from another data
      source (e.g. a backup), this field will be populated with information
      about the restore.
    stats: Only available with STATS_VIEW, this includes summary statistics
      about the entire table contents. For statistics about a specific column
      family, see ColumnFamilyStats in the mapped ColumnFamily collection
      above.
  """

  class GranularityValueValuesEnum(_messages.Enum):
    r"""Immutable. The granularity (i.e. `MILLIS`) at which timestamps are
    stored in this table. Timestamps not matching the granularity will be
    rejected. If unspecified at creation time, the value will be set to
    `MILLIS`. Views: `SCHEMA_VIEW`, `FULL`.

    Values:
      TIMESTAMP_GRANULARITY_UNSPECIFIED: The user did not specify a
        granularity. Should not be returned. When specified during table
        creation, MILLIS will be used.
      MILLIS: The table keeps data versioned at a granularity of 1ms.
    """
    TIMESTAMP_GRANULARITY_UNSPECIFIED = 0
    MILLIS = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ClusterStatesValue(_messages.Message):
    r"""Output only. Map from cluster ID to per-cluster table state. If it
    could not be determined whether or not the table has data in a particular
    cluster (for example, if its zone is unavailable), then there will be an
    entry for the cluster with UNKNOWN `replication_status`. Views:
    `REPLICATION_VIEW`, `ENCRYPTION_VIEW`, `FULL`

    Messages:
      AdditionalProperty: An additional property for a ClusterStatesValue
        object.

    Fields:
      additionalProperties: Additional properties of type ClusterStatesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ClusterStatesValue object.

      Fields:
        key: Name of the additional property.
        value: A ClusterState attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ClusterState', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ColumnFamiliesValue(_messages.Message):
    r"""The column families configured for this table, mapped by column family
    ID. Views: `SCHEMA_VIEW`, `STATS_VIEW`, `FULL`

    Messages:
      AdditionalProperty: An additional property for a ColumnFamiliesValue
        object.

    Fields:
      additionalProperties: Additional properties of type ColumnFamiliesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ColumnFamiliesValue object.

      Fields:
        key: Name of the additional property.
        value: A ColumnFamily attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ColumnFamily', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  changeStreamConfig = _messages.MessageField('ChangeStreamConfig', 1)
  clusterStates = _messages.MessageField('ClusterStatesValue', 2)
  columnFamilies = _messages.MessageField('ColumnFamiliesValue', 3)
  deletionProtection = _messages.BooleanField(4)
  granularity = _messages.EnumField('GranularityValueValuesEnum', 5)
  name = _messages.StringField(6)
  restoreInfo = _messages.MessageField('RestoreInfo', 7)
  stats = _messages.MessageField('TableStats', 8)


class TableProgress(_messages.Message):
  r"""Progress info for copying a table's data to the new cluster.

  Enums:
    StateValueValuesEnum:

  Fields:
    estimatedCopiedBytes: Estimate of the number of bytes copied so far for
      this table. This will eventually reach 'estimated_size_bytes' unless the
      table copy is CANCELLED.
    estimatedSizeBytes: Estimate of the size of the table to be copied.
    state: A StateValueValuesEnum attribute.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""StateValueValuesEnum enum type.

    Values:
      STATE_UNSPECIFIED: <no description>
      PENDING: The table has not yet begun copying to the new cluster.
      COPYING: The table is actively being copied to the new cluster.
      COMPLETED: The table has been fully copied to the new cluster.
      CANCELLED: The table was deleted before it finished copying to the new
        cluster. Note that tables deleted after completion will stay marked as
        COMPLETED, not CANCELLED.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    COPYING = 2
    COMPLETED = 3
    CANCELLED = 4

  estimatedCopiedBytes = _messages.IntegerField(1)
  estimatedSizeBytes = _messages.IntegerField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class TableStats(_messages.Message):
  r"""Approximate statistics related to a table. These statistics are
  calculated infrequently, while simultaneously, data in the table can change
  rapidly. Thus the values reported here (e.g. row count) are very likely out-
  of date, even the instant they are received in this API. Thus, only treat
  these values as approximate. IMPORTANT: Everything below is approximate,
  unless otherwise specified.

  Fields:
    averageCellsPerColumn: How many cells are present per column (column
      family, column qualifier) combinations, averaged over all columns in all
      rows in the table. e.g. A table with 2 rows: * A row with 3 cells in
      "family:col" and 1 cell in "other:col" (4 cells / 2 columns) * A row
      with 1 cell in "family:col", 7 cells in "family:other_col", and 7 cells
      in "other:data" (15 cells / 3 columns) would report (4 + 15)/(2 + 3) =
      3.8 in this field.
    averageColumnsPerRow: How many (column family, column qualifier)
      combinations are present per row in the table, averaged over all rows in
      the table. e.g. A table with 2 rows: * A row with cells in "family:col"
      and "other:col" (2 distinct columns) * A row with cells in "family:col",
      "family:other_col", and "other:data" (3 distinct columns) would report
      (2 + 3)/2 = 2.5 in this field.
    logicalDataBytes: This is roughly how many bytes would be needed to read
      the entire table (e.g. by streaming all contents out).
    rowCount: How many rows are in the table.
  """

  averageCellsPerColumn = _messages.FloatField(1)
  averageColumnsPerRow = _messages.FloatField(2)
  logicalDataBytes = _messages.IntegerField(3)
  rowCount = _messages.IntegerField(4)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class UndeleteTableMetadata(_messages.Message):
  r"""Metadata type for the operation returned by
  google.bigtable.admin.v2.BigtableTableAdmin.UndeleteTable.

  Fields:
    endTime: If set, the time at which this operation finished or was
      cancelled.
    name: The name of the table being restored.
    startTime: The time at which this operation started.
  """

  endTime = _messages.StringField(1)
  name = _messages.StringField(2)
  startTime = _messages.StringField(3)


class UndeleteTableRequest(_messages.Message):
  r"""Request message for
  google.bigtable.admin.v2.BigtableTableAdmin.UndeleteTable
  """



class Union(_messages.Message):
  r"""A GcRule which deletes cells matching any of the given rules.

  Fields:
    rules: Delete cells which would be deleted by any element of `rules`.
  """

  rules = _messages.MessageField('GcRule', 1, repeated=True)


class UpdateAppProfileMetadata(_messages.Message):
  r"""The metadata for the Operation returned by UpdateAppProfile."""


class UpdateClusterMetadata(_messages.Message):
  r"""The metadata for the Operation returned by UpdateCluster.

  Fields:
    finishTime: The time at which the operation failed or was completed
      successfully.
    originalRequest: The request that prompted the initiation of this
      UpdateCluster operation.
    requestTime: The time at which the original request was received.
  """

  finishTime = _messages.StringField(1)
  originalRequest = _messages.MessageField('Cluster', 2)
  requestTime = _messages.StringField(3)


class UpdateInstanceMetadata(_messages.Message):
  r"""The metadata for the Operation returned by UpdateInstance.

  Fields:
    finishTime: The time at which the operation failed or was completed
      successfully.
    originalRequest: The request that prompted the initiation of this
      UpdateInstance operation.
    requestTime: The time at which the original request was received.
  """

  finishTime = _messages.StringField(1)
  originalRequest = _messages.MessageField('PartialUpdateInstanceRequest', 2)
  requestTime = _messages.StringField(3)


class UpdateTableMetadata(_messages.Message):
  r"""Metadata type for the operation returned by UpdateTable.

  Fields:
    endTime: If set, the time at which this operation finished or was
      canceled.
    name: The name of the table being updated.
    startTime: The time at which this operation started.
  """

  endTime = _messages.StringField(1)
  name = _messages.StringField(2)
  startTime = _messages.StringField(3)


class UpdateViewMetadata(_messages.Message):
  r"""Metadata for the google.longrunning.Operation returned by UpdateView.

  Fields:
    finishTime: The time at which the operation failed or was completed
      successfully.
    originalRequest: The request that prompted the initiation of this
      UpdateView operation.
    requestTime: The time at which the original request was received.
  """

  finishTime = _messages.StringField(1)
  originalRequest = _messages.MessageField('UpdateViewRequest', 2)
  requestTime = _messages.StringField(3)


class UpdateViewRequest(_messages.Message):
  r"""The request for UpdateView.

  Fields:
    updateMask: Optional. The list of fields to update. A mask specifying
      which fields in the View resource should be updated. This mask is
      relative to the View resource, not to the request message. A field will
      be overwritten if it is in the mask. If empty, all fields set in the
      request will be overwritten. A special value `*` means to overwrite all
      fields (including fields not set in the request).
    view: Required. The view to update. The `name` in `view` is used to
      identify the view. View name must in this format
      projects//instances//tables//views/
  """

  updateMask = _messages.StringField(1)
  view = _messages.MessageField('View', 2)


class View(_messages.Message):
  r"""Placeholder for admin API work while we work out the internals.

  Fields:
    createTime: Output only. A commit timestamp representing when this View
      was created.
    deletionProtection: Set to true to make the view protected against
      deletion. The parent Table and containing Instance cannot be deleted if
      a View has this bit set.
    name: The name of the view. Values are of the form
      `projects/{project}/instances/{instance}/tables/{table}/views/{view}`
    subsetView: A view permitting access to an explicit subset of a Table.
    updateTime: Output only. A commit timestamp representing when this View
      was updated.
  """

  createTime = _messages.StringField(1)
  deletionProtection = _messages.BooleanField(2)
  name = _messages.StringField(3)
  subsetView = _messages.MessageField('SubsetView', 4)
  updateTime = _messages.StringField(5)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

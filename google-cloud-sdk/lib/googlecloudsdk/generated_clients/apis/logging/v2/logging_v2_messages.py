"""Generated message classes for logging version v2.

Writes log entries and manages your Cloud Logging configuration.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'logging'


class AggregateValueThreshold(_messages.Message):
  r"""A threshold condition that compares an aggregation to a threshold.

  Fields:
    aggregateColumn: Required. The column to provide aggregation on for
      comparison.
    aggregation: Required. The aggregation config that will be applied to the
      provided column.
  """

  aggregateColumn = _messages.StringField(1)
  aggregation = _messages.MessageField('QueryStepAggregation', 2)


class AlertingQueryStep(_messages.Message):
  r"""A query step defined as a set of alerting configuration options. This
  may not be used as the first step in a query.

  Fields:
    booleanCondition: A test representing the boolean value of a column.
    partitionColumns: Optional. The list of columns to GROUP BY in the
      generated SQL. NOTE: partition columns are not yet supported.
    stringCondition: A test representing a comparison against a string.
    thresholdCondition: A test representing a comparison against a threshold.
  """

  booleanCondition = _messages.MessageField('BooleanTest', 1)
  partitionColumns = _messages.StringField(2, repeated=True)
  stringCondition = _messages.MessageField('StringTest', 3)
  thresholdCondition = _messages.MessageField('ThresholdTest', 4)


class ApproveRedactionOperationResponse(_messages.Message):
  r"""Response type for ApproveRedaction method."""


class BigQueryDataset(_messages.Message):
  r"""Describes a BigQuery dataset that was created by a link.

  Fields:
    datasetId: Output only. The full resource name of the BigQuery dataset.
      The DATASET_ID will match the ID of the link, so the link must match the
      naming restrictions of BigQuery datasets (alphanumeric characters and
      underscores only).The dataset will have a resource path of
      "bigquery.googleapis.com/projects/PROJECT_ID/datasets/DATASET_ID"
  """

  datasetId = _messages.StringField(1)


class BigQueryOptions(_messages.Message):
  r"""Options that change functionality of a sink exporting data to BigQuery.

  Fields:
    usePartitionedTables: Optional. Whether to use BigQuery's partition tables
      (https://cloud.google.com/bigquery/docs/partitioned-tables). By default,
      Cloud Logging creates dated tables based on the log entries' timestamps,
      e.g. syslog_20170523. With partitioned tables the date suffix is no
      longer present and special query syntax
      (https://cloud.google.com/bigquery/docs/querying-partitioned-tables) has
      to be used instead. In both cases, tables are sharded based on UTC
      timezone.
    usesTimestampColumnPartitioning: Output only. True if new timestamp column
      based partitioning is in use, false if legacy ingress-time partitioning
      is in use.All new sinks will have this field set true and will use
      timestamp column based partitioning. If use_partitioned_tables is false,
      this value has no meaning and will be false. Legacy sinks using
      partitioned tables will have this field set to false.
  """

  usePartitionedTables = _messages.BooleanField(1)
  usesTimestampColumnPartitioning = _messages.BooleanField(2)


class BooleanTest(_messages.Message):
  r"""A test that reads a boolean column as the result.

  Fields:
    booleanColumn: Required. The column that contains a boolean that we want
      to use as our result.
    trigger: Optional. The number/percent of rows that must match in order for
      the result set (partition set) to be considered in violation. If
      unspecified, then the result set (partition set) will be in violation if
      a single row matches.NOTE: Triggers are not yet supported for
      BooleanTest.
  """

  booleanColumn = _messages.StringField(1)
  trigger = _messages.MessageField('Trigger', 2)


class Breakdown(_messages.Message):
  r"""Columns within the output of the previous step to use to break down the
  measures. We will generate one output measure for each value in the cross
  product of measure_columns plus the top limit values in each of the
  breakdown columns.In other words, if there is one measure column "foo" and
  two breakdown columns "bar" with values ("bar1","bar2") and "baz" with
  values ("baz1", "baz2"), we will end up with four output measures with
  names: foo_bar1_baz1, foo_bar1_baz2, foo_bar2_baz1, foo_bar2_baz2 Each of
  these measures will contain a subset of the values in "foo".

  Enums:
    SortOrderValueValuesEnum: Optional. The ordering that defines the behavior
      of limit. If limit is not zero, this may not be set to
      SORT_ORDER_NONE.Note that this will not control the ordering of the rows
      in the result table in any useful way. Use the top-level sort ordering
      for that purpose.

  Fields:
    column: Required. The name of the column containing the breakdown values.
    limit: Optional. Values to choose how many breakdowns to create for each
      measure. If limit is zero, all possible breakdowns will be generated. If
      not, limit determines how many breakdowns, and sort_aggregation
      determines the function we will use to sort the breakdowns.For example,
      if limit is 3, we will generate at most three breakdowns per measure. If
      sort_aggregation is "average" and sort_order is DESCENDING, those three
      will be chosen as the ones where the average of all the points in the
      breakdown set is the greatest.
    sortAggregation: Optional. The aggregation to apply to the measure values
      when choosing which breakdowns to generate. If sort_order is
      SORT_ORDER_NONE, this is not used.
    sortOrder: Optional. The ordering that defines the behavior of limit. If
      limit is not zero, this may not be set to SORT_ORDER_NONE.Note that this
      will not control the ordering of the rows in the result table in any
      useful way. Use the top-level sort ordering for that purpose.
  """

  class SortOrderValueValuesEnum(_messages.Enum):
    r"""Optional. The ordering that defines the behavior of limit. If limit is
    not zero, this may not be set to SORT_ORDER_NONE.Note that this will not
    control the ordering of the rows in the result table in any useful way.
    Use the top-level sort ordering for that purpose.

    Values:
      SORT_ORDER_UNSPECIFIED: Invalid value, do not use.
      SORT_ORDER_NONE: No sorting will be applied.
      SORT_ORDER_ASCENDING: The lowest-valued entries will be selected.
      SORT_ORDER_DESCENDING: The highest-valued entries will be selected.
    """
    SORT_ORDER_UNSPECIFIED = 0
    SORT_ORDER_NONE = 1
    SORT_ORDER_ASCENDING = 2
    SORT_ORDER_DESCENDING = 3

  column = _messages.StringField(1)
  limit = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  sortAggregation = _messages.MessageField('QueryStepAggregation', 3)
  sortOrder = _messages.EnumField('SortOrderValueValuesEnum', 4)


class BucketMetadata(_messages.Message):
  r"""Metadata for LongRunningUpdateBucket Operations.

  Enums:
    StateValueValuesEnum: State of an operation.

  Fields:
    createBucketRequest: LongRunningCreateBucket RPC request.
    endTime: The end time of an operation.
    startTime: The create time of an operation.
    state: State of an operation.
    updateBucketRequest: LongRunningUpdateBucket RPC request.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of an operation.

    Values:
      OPERATION_STATE_UNSPECIFIED: Should not be used.
      OPERATION_STATE_SCHEDULED: The operation is scheduled.
      OPERATION_STATE_WAITING_FOR_PERMISSIONS: Waiting for necessary
        permissions.
      OPERATION_STATE_RUNNING: The operation is running.
      OPERATION_STATE_SUCCEEDED: The operation was completed successfully.
      OPERATION_STATE_FAILED: The operation failed.
      OPERATION_STATE_CANCELLED: The operation was cancelled by the user.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    OPERATION_STATE_SCHEDULED = 1
    OPERATION_STATE_WAITING_FOR_PERMISSIONS = 2
    OPERATION_STATE_RUNNING = 3
    OPERATION_STATE_SUCCEEDED = 4
    OPERATION_STATE_FAILED = 5
    OPERATION_STATE_CANCELLED = 6

  createBucketRequest = _messages.MessageField('CreateBucketRequest', 1)
  endTime = _messages.StringField(2)
  startTime = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  updateBucketRequest = _messages.MessageField('UpdateBucketRequest', 5)


class BucketOptions(_messages.Message):
  r"""BucketOptions describes the bucket boundaries used to create a histogram
  for the distribution. The buckets can be in a linear sequence, an
  exponential sequence, or each bucket can be specified explicitly.
  BucketOptions does not include the number of values in each bucket.A bucket
  has an inclusive lower bound and exclusive upper bound for the values that
  are counted for that bucket. The upper bound of a bucket must be strictly
  greater than the lower bound. The sequence of N buckets for a distribution
  consists of an underflow bucket (number 0), zero or more finite buckets
  (number 1 through N - 2) and an overflow bucket (number N - 1). The buckets
  are contiguous: the lower bound of bucket i (i > 0) is the same as the upper
  bound of bucket i - 1. The buckets span the whole range of finite values:
  lower bound of the underflow bucket is -infinity and the upper bound of the
  overflow bucket is +infinity. The finite buckets are so-called because both
  bounds are finite.

  Fields:
    explicitBuckets: The explicit buckets.
    exponentialBuckets: The exponential buckets.
    linearBuckets: The linear bucket.
  """

  explicitBuckets = _messages.MessageField('Explicit', 1)
  exponentialBuckets = _messages.MessageField('Exponential', 2)
  linearBuckets = _messages.MessageField('Linear', 3)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class ChartingQueryStep(_messages.Message):
  r"""A query step defined as a set of charting configuration options. This
  may not be used as the first step in a query.

  Enums:
    SortOrderValueValuesEnum: Optional. The sort order that controls the final
      results.

  Fields:
    breakdowns: Optional. The breakdowns for the measures of the chart. A
      breakdown turns a single measure into multiple effective measures, each
      one associated with a single value from the breakdown column.
    dimensions: Required. The dimension columns. How many dimensions to choose
      and how they're configured will depend on the chart type. A dimension is
      the labels for the data; e.g., the X axis for a line graph or the
      segment labels for a pie chart.
    measures: Required. The measures to be displayed within the chart. A
      measure is a data set to be displayed; e.g., a line on a line graph, a
      set of bars on a bar graph, or the segment widths on a pie chart.
    sortColumn: Optional. The column name to sort the results on. This may be
      set to one of the dimension columns or left empty, which is equivalent.
      If no breakdowns are requested, it may be set to any measure column; if
      breakdowns are requested, sorting by measures is not supported. If
      sort_order is SORT_ORDER_NONE, this value is not used. If there is an
      anonymous measure using aggregation "count", use the string "*" to name
      it here.
    sortOrder: Optional. The sort order that controls the final results.
  """

  class SortOrderValueValuesEnum(_messages.Enum):
    r"""Optional. The sort order that controls the final results.

    Values:
      SORT_ORDER_UNSPECIFIED: Invalid value, do not use.
      SORT_ORDER_NONE: No sorting will be applied.
      SORT_ORDER_ASCENDING: The lowest-valued entries will be selected.
      SORT_ORDER_DESCENDING: The highest-valued entries will be selected.
    """
    SORT_ORDER_UNSPECIFIED = 0
    SORT_ORDER_NONE = 1
    SORT_ORDER_ASCENDING = 2
    SORT_ORDER_DESCENDING = 3

  breakdowns = _messages.MessageField('Breakdown', 1, repeated=True)
  dimensions = _messages.MessageField('Dimension', 2, repeated=True)
  measures = _messages.MessageField('Measure', 3, repeated=True)
  sortColumn = _messages.StringField(4)
  sortOrder = _messages.EnumField('SortOrderValueValuesEnum', 5)


class CmekSettings(_messages.Message):
  r"""Describes the customer-managed encryption key (CMEK) settings associated
  with a project, folder, organization, billing account, or flexible
  resource.Note: CMEK for the Log Router can currently only be configured for
  Google Cloud organizations. Once configured, it applies to all projects and
  folders in the Google Cloud organization.See Enabling CMEK for Log Router
  (https://cloud.google.com/logging/docs/routing/managed-encryption) for more
  information.

  Fields:
    kmsKeyName: The resource name for the configured Cloud KMS key.KMS key
      name format: "projects/[PROJECT_ID]/locations/[LOCATION]/keyRings/[KEYRI
      NG]/cryptoKeys/[KEY]" For example:"projects/my-project/locations/us-
      central1/keyRings/my-ring/cryptoKeys/my-key"To enable CMEK for the Log
      Router, set this field to a valid kms_key_name for which the associated
      service account has the needed cloudkms.cryptoKeyEncrypterDecrypter
      roles assigned for the key.The Cloud KMS key used by the Log Router can
      be updated by changing the kms_key_name to a new valid key name or
      disabled by setting the key name to an empty string. Encryption
      operations that are in progress will be completed with the key that was
      in use when they started. Decryption operations will be completed using
      the key that was used at the time of encryption unless access to that
      key has been revoked.To disable CMEK for the Log Router, set this field
      to an empty string.See Enabling CMEK for Log Router
      (https://cloud.google.com/logging/docs/routing/managed-encryption) for
      more information.
    kmsKeyVersionName: The CryptoKeyVersion resource name for the configured
      Cloud KMS key.KMS key name format: "projects/[PROJECT_ID]/locations/[LOC
      ATION]/keyRings/[KEYRING]/cryptoKeys/[KEY]/cryptoKeyVersions/[VERSION]"
      For example:"projects/my-project/locations/us-central1/keyRings/my-
      ring/cryptoKeys/my-key/cryptoKeyVersions/1"This is a read-only field
      used to convey the specific configured CryptoKeyVersion of kms_key that
      has been configured. It will be populated in cases where the CMEK
      settings are bound to a single key version.If this field is populated,
      the kms_key is tied to a specific CryptoKeyVersion.
    name: Output only. The resource name of the CMEK settings.
    serviceAccountId: Output only. The service account that will be used by
      the Log Router to access your Cloud KMS key.Before enabling CMEK for Log
      Router, you must first assign the cloudkms.cryptoKeyEncrypterDecrypter
      role to the service account that the Log Router will use to access your
      Cloud KMS key. Use GetCmekSettings to obtain the service account ID.See
      Enabling CMEK for Log Router
      (https://cloud.google.com/logging/docs/routing/managed-encryption) for
      more information.
  """

  kmsKeyName = _messages.StringField(1)
  kmsKeyVersionName = _messages.StringField(2)
  name = _messages.StringField(3)
  serviceAccountId = _messages.StringField(4)


class CopyLogEntriesMetadata(_messages.Message):
  r"""Metadata for CopyLogEntries long running operations.

  Enums:
    StateValueValuesEnum: State of an operation.

  Fields:
    cancellationRequested: Identifies whether the user has requested
      cancellation of the operation.
    endTime: The end time of an operation.
    progress: Estimated progress of the operation (0 - 100%).
    request: CopyLogEntries RPC request.
    startTime: The create time of an operation.
    state: State of an operation.
    writerIdentity: The IAM identity of a service account that must be granted
      access to the destination.If the service account is not granted
      permission to the destination within an hour, the operation will be
      cancelled.For example: "serviceAccount:<EMAIL>"
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of an operation.

    Values:
      OPERATION_STATE_UNSPECIFIED: Should not be used.
      OPERATION_STATE_SCHEDULED: The operation is scheduled.
      OPERATION_STATE_WAITING_FOR_PERMISSIONS: Waiting for necessary
        permissions.
      OPERATION_STATE_RUNNING: The operation is running.
      OPERATION_STATE_SUCCEEDED: The operation was completed successfully.
      OPERATION_STATE_FAILED: The operation failed.
      OPERATION_STATE_CANCELLED: The operation was cancelled by the user.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    OPERATION_STATE_SCHEDULED = 1
    OPERATION_STATE_WAITING_FOR_PERMISSIONS = 2
    OPERATION_STATE_RUNNING = 3
    OPERATION_STATE_SUCCEEDED = 4
    OPERATION_STATE_FAILED = 5
    OPERATION_STATE_CANCELLED = 6

  cancellationRequested = _messages.BooleanField(1)
  endTime = _messages.StringField(2)
  progress = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  request = _messages.MessageField('CopyLogEntriesRequest', 4)
  startTime = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  writerIdentity = _messages.StringField(7)


class CopyLogEntriesRequest(_messages.Message):
  r"""The parameters to CopyLogEntries.

  Fields:
    destination: Required. Destination to which to copy log entries.
    filter: Optional. A filter specifying which log entries to copy. The
      filter must be no more than 20k characters. An empty filter matches all
      log entries.
    name: Required. Log bucket from which to copy log entries.For
      example:"projects/my-project/locations/global/buckets/my-source-bucket"
  """

  destination = _messages.StringField(1)
  filter = _messages.StringField(2)
  name = _messages.StringField(3)


class CopyLogEntriesResponse(_messages.Message):
  r"""Response type for CopyLogEntries long running operations.

  Fields:
    logEntriesCopiedCount: Number of log entries copied.
  """

  logEntriesCopiedCount = _messages.IntegerField(1)


class CreateBucketRequest(_messages.Message):
  r"""The parameters to CreateBucket.

  Fields:
    bucket: Required. The new bucket. The region specified in the new bucket
      must be compliant with any Location Restriction Org Policy. The name
      field in the bucket is ignored.
    bucketId: Required. A client-assigned identifier such as "my-bucket".
      Identifiers are limited to 100 characters and can include only letters,
      digits, underscores, hyphens, and periods.
    parent: Required. The resource in which to create the log bucket:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]" For
      example:"projects/my-project/locations/global"
  """

  bucket = _messages.MessageField('LogBucket', 1)
  bucketId = _messages.StringField(2)
  parent = _messages.StringField(3)


class CreateLinkRequest(_messages.Message):
  r"""The parameters to CreateLink.

  Fields:
    link: Required. The new link.
    linkId: Required. The ID to use for the link. The link_id can have up to
      100 characters. A valid link_id must only have alphanumeric characters
      and underscores within it.
    parent: Required. The full resource name of the bucket to create a link
      for. "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
      "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET
      _ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buck
      ets/[BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  """

  link = _messages.MessageField('Link', 1)
  linkId = _messages.StringField(2)
  parent = _messages.StringField(3)


class DeleteLinkRequest(_messages.Message):
  r"""The parameters to DeleteLink.

  Fields:
    name: Required. The full resource name of the link to delete. "projects/[P
      ROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
      "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET
      _ID]/links/[LINK_ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[L
      OCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]" "folders/[FOLDER_ID]/lo
      cations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  """

  name = _messages.StringField(1)


class Dimension(_messages.Message):
  r"""A definition for the (one) dimension column in the output. Multiple
  dimensions can be defined, but only a single column will be generated,
  containing the cross-product of the defined dimensions.

  Enums:
    SortOrderValueValuesEnum: Optional. The ordering that defines the behavior
      of limit. If limit is not zero, this may not be set to
      SORT_ORDER_NONE.Note that this will not control the ordering of the rows
      in the result table in any useful way. Use the top-level sort ordering
      for that purpose.

  Fields:
    column: Required. The column name within the output of the previous step
      to use.
    integerBinSize: Optional. Used for an integer column.
    limit: Optional. If this value is nonzero, the number of bins to generate.
      If zero, all possible bins will be generated.
    sortColumn: Optional. The column name to sort on. This may be set to this
      dimension column or any measure column. If the field is empty, it will
      sort on the dimension column. If sort_order is SORT_ORDER_NONE, this
      value is not used. If there is an anonymous measure using aggregation
      "count", use the string "*" to name it here.
    sortOrder: Optional. The ordering that defines the behavior of limit. If
      limit is not zero, this may not be set to SORT_ORDER_NONE.Note that this
      will not control the ordering of the rows in the result table in any
      useful way. Use the top-level sort ordering for that purpose.
    timeBinSize: Optional. Used for a Timestamp column.
  """

  class SortOrderValueValuesEnum(_messages.Enum):
    r"""Optional. The ordering that defines the behavior of limit. If limit is
    not zero, this may not be set to SORT_ORDER_NONE.Note that this will not
    control the ordering of the rows in the result table in any useful way.
    Use the top-level sort ordering for that purpose.

    Values:
      SORT_ORDER_UNSPECIFIED: Invalid value, do not use.
      SORT_ORDER_NONE: No sorting will be applied.
      SORT_ORDER_ASCENDING: The lowest-valued entries will be selected.
      SORT_ORDER_DESCENDING: The highest-valued entries will be selected.
    """
    SORT_ORDER_UNSPECIFIED = 0
    SORT_ORDER_NONE = 1
    SORT_ORDER_ASCENDING = 2
    SORT_ORDER_DESCENDING = 3

  column = _messages.StringField(1)
  integerBinSize = _messages.IntegerField(2)
  limit = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  sortColumn = _messages.StringField(4)
  sortOrder = _messages.EnumField('SortOrderValueValuesEnum', 5)
  timeBinSize = _messages.StringField(6)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Explicit(_messages.Message):
  r"""Specifies a set of buckets with arbitrary widths.There are size(bounds)
  + 1 (= N) buckets. Bucket i has the following boundaries:Upper bound (0 <= i
  < N-1): boundsi Lower bound (1 <= i < N); boundsi - 1The bounds field must
  contain at least one element. If bounds has only one element, then there are
  no finite buckets, and that single element is the common boundary of the
  overflow and underflow buckets.

  Fields:
    bounds: The values must be monotonically increasing.
  """

  bounds = _messages.FloatField(1, repeated=True)


class Exponential(_messages.Message):
  r"""Specifies an exponential sequence of buckets that have a width that is
  proportional to the value of the lower bound. Each bucket represents a
  constant relative uncertainty on a specific value in the bucket.There are
  num_finite_buckets + 2 (= N) buckets. Bucket i has the following
  boundaries:Upper bound (0 <= i < N-1): scale * (growth_factor ^ i).Lower
  bound (1 <= i < N): scale * (growth_factor ^ (i - 1)).

  Fields:
    growthFactor: Must be greater than 1.
    numFiniteBuckets: Must be greater than 0.
    scale: Must be greater than 0.
  """

  growthFactor = _messages.FloatField(1)
  numFiniteBuckets = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  scale = _messages.FloatField(3)


class HandleQueryStep(_messages.Message):
  r"""A query step that reads the results of a step in a previous query
  operation as its input.

  Fields:
    queryStepHandle: Required. A handle to a query step from a previous call
      to QueryData.
  """

  queryStepHandle = _messages.StringField(1)


class HttpRequest(_messages.Message):
  r"""A common proto for logging HTTP requests. Only contains semantics
  defined by the HTTP specification. Product-specific logging information MUST
  be defined in a separate message.

  Fields:
    cacheFillBytes: The number of HTTP response bytes inserted into cache. Set
      only when a cache fill was attempted.
    cacheHit: Whether or not an entity was served from cache (with or without
      validation).
    cacheLookup: Whether or not a cache lookup was attempted.
    cacheValidatedWithOriginServer: Whether or not the response was validated
      with the origin server before being served from cache. This field is
      only meaningful if cache_hit is True.
    latency: The request processing latency on the server, from the time the
      request was received until the response was sent.
    protocol: Protocol used for the request. Examples: "HTTP/1.1", "HTTP/2",
      "websocket"
    referer: The referer URL of the request, as defined in HTTP/1.1 Header
      Field Definitions
      (https://datatracker.ietf.org/doc/html/rfc2616#section-14.36).
    remoteIp: The IP address (IPv4 or IPv6) of the client that issued the HTTP
      request. This field can include port information. Examples:
      "***********", "********:80", "FE80::0202:B3FF:FE1E:8329".
    requestMethod: The request method. Examples: "GET", "HEAD", "PUT", "POST".
    requestSize: The size of the HTTP request message in bytes, including the
      request headers and the request body.
    requestUrl: The scheme (http, https), the host name, the path and the
      query portion of the URL that was requested. Example:
      "http://example.com/some/info?color=red".
    responseSize: The size of the HTTP response message sent back to the
      client, in bytes, including the response headers and the response body.
    serverIp: The IP address (IPv4 or IPv6) of the origin server that the
      request was sent to. This field can include port information. Examples:
      "***********", "********:80", "FE80::0202:B3FF:FE1E:8329".
    status: The response code indicating the status of response. Examples:
      200, 404.
    userAgent: The user agent sent by the client. Example: "Mozilla/4.0
      (compatible; MSIE 6.0; Windows 98; Q312461; .NET CLR 1.0.3705)".
  """

  cacheFillBytes = _messages.IntegerField(1)
  cacheHit = _messages.BooleanField(2)
  cacheLookup = _messages.BooleanField(3)
  cacheValidatedWithOriginServer = _messages.BooleanField(4)
  latency = _messages.StringField(5)
  protocol = _messages.StringField(6)
  referer = _messages.StringField(7)
  remoteIp = _messages.StringField(8)
  requestMethod = _messages.StringField(9)
  requestSize = _messages.IntegerField(10)
  requestUrl = _messages.StringField(11)
  responseSize = _messages.IntegerField(12)
  serverIp = _messages.StringField(13)
  status = _messages.IntegerField(14, variant=_messages.Variant.INT32)
  userAgent = _messages.StringField(15)


class IndexConfig(_messages.Message):
  r"""Configuration for an indexed field.

  Enums:
    TypeValueValuesEnum: Required. The type of data in this index.

  Fields:
    createTime: Output only. The timestamp when the index was last
      modified.This is used to return the timestamp, and will be ignored if
      supplied during update.
    fieldPath: Required. The LogEntry field path to index.Note that some paths
      are automatically indexed, and other paths are not eligible for
      indexing. See indexing documentation(
      https://cloud.google.com/logging/docs/view/advanced-queries#indexed-
      fields) for details.For example: jsonPayload.request.status
    type: Required. The type of data in this index.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of data in this index.

    Values:
      INDEX_TYPE_UNSPECIFIED: The index's type is unspecified.
      INDEX_TYPE_STRING: The index is a string-type index.
      INDEX_TYPE_INTEGER: The index is a integer-type index.
    """
    INDEX_TYPE_UNSPECIFIED = 0
    INDEX_TYPE_STRING = 1
    INDEX_TYPE_INTEGER = 2

  createTime = _messages.StringField(1)
  fieldPath = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class IntegerArrayValue(_messages.Message):
  r"""An array of integers within a parameter.

  Fields:
    values: The values of the array.
  """

  values = _messages.IntegerField(1, repeated=True)


class Interval(_messages.Message):
  r"""Represents a time interval, encoded as a Timestamp start (inclusive) and
  a Timestamp end (exclusive).The start must be less than or equal to the end.
  When the start equals the end, the interval is empty (matches no time). When
  both start and end are unspecified, the interval matches any time.

  Fields:
    endTime: Optional. Exclusive end of the interval.If specified, a Timestamp
      matching this interval will have to be before the end.
    startTime: Optional. Inclusive start of the interval.If specified, a
      Timestamp matching this interval will have to be the same or after the
      start.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class LabelDescriptor(_messages.Message):
  r"""A description of a label.

  Enums:
    ValueTypeValueValuesEnum: The type of data that can be assigned to the
      label.

  Fields:
    description: A human-readable description for the label.
    key: The label key.
    valueType: The type of data that can be assigned to the label.
  """

  class ValueTypeValueValuesEnum(_messages.Enum):
    r"""The type of data that can be assigned to the label.

    Values:
      STRING: A variable-length string. This is the default.
      BOOL: Boolean; true or false.
      INT64: A 64-bit signed integer.
    """
    STRING = 0
    BOOL = 1
    INT64 = 2

  description = _messages.StringField(1)
  key = _messages.StringField(2)
  valueType = _messages.EnumField('ValueTypeValueValuesEnum', 3)


class Linear(_messages.Message):
  r"""Specifies a linear sequence of buckets that all have the same width
  (except overflow and underflow). Each bucket represents a constant absolute
  uncertainty on the specific value in the bucket.There are num_finite_buckets
  + 2 (= N) buckets. Bucket i has the following boundaries:Upper bound (0 <= i
  < N-1): offset + (width * i).Lower bound (1 <= i < N): offset + (width * (i
  - 1)).

  Fields:
    numFiniteBuckets: Must be greater than 0.
    offset: Lower bound of the first bucket.
    width: Must be greater than 0.
  """

  numFiniteBuckets = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  offset = _messages.FloatField(2)
  width = _messages.FloatField(3)


class Link(_messages.Message):
  r"""Describes a link connected to an analytics enabled bucket.

  Enums:
    LifecycleStateValueValuesEnum: Output only. The resource lifecycle state.

  Fields:
    bigqueryDataset: The information of a BigQuery Dataset. When a link is
      created, a BigQuery dataset is created along with it, in the same
      project as the LogBucket it's linked to. This dataset will also have
      BigQuery Views corresponding to the LogViews in the bucket.
    createTime: Output only. The creation timestamp of the link.
    description: Describes this link.The maximum length of the description is
      8000 characters.
    lifecycleState: Output only. The resource lifecycle state.
    name: The resource name of the link. The name can have up to 100
      characters. A valid link id (at the end of the link name) must only have
      alphanumeric characters and underscores within it. "projects/[PROJECT_ID
      ]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]" "organiza
      tions/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/link
      s/[LINK_ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_I
      D]/buckets/[BUCKET_ID]/links/[LINK_ID]" "folders/[FOLDER_ID]/locations/[
      LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]" For
      example:`projects/my-project/locations/global/buckets/my-
      bucket/links/my_link
  """

  class LifecycleStateValueValuesEnum(_messages.Enum):
    r"""Output only. The resource lifecycle state.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: Unspecified state. This is only used/useful
        for distinguishing unset values.
      ACTIVE: The normal and active state.
      DELETE_REQUESTED: The resource has been marked for deletion by the user.
        For some resources (e.g. buckets), this can be reversed by an un-
        delete operation.
      UPDATING: The resource has been marked for an update by the user. It
        will remain in this state until the update is complete.
      CREATING: The resource has been marked for creation by the user. It will
        remain in this state until the creation is complete.
      FAILED: The resource is in an INTERNAL error state.
      ARCHIVED: The resource has been archived such that it can still be
        queried but can no longer be modified or used as a sink destination.
        The source bucket after a move bucket operation enters this state.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETE_REQUESTED = 2
    UPDATING = 3
    CREATING = 4
    FAILED = 5
    ARCHIVED = 6

  bigqueryDataset = _messages.MessageField('BigQueryDataset', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  lifecycleState = _messages.EnumField('LifecycleStateValueValuesEnum', 4)
  name = _messages.StringField(5)


class LinkMetadata(_messages.Message):
  r"""Metadata for long running Link operations.

  Enums:
    StateValueValuesEnum: State of an operation.

  Fields:
    createLinkRequest: CreateLink RPC request.
    deleteLinkRequest: DeleteLink RPC request.
    endTime: The end time of an operation.
    startTime: The start time of an operation.
    state: State of an operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of an operation.

    Values:
      OPERATION_STATE_UNSPECIFIED: Should not be used.
      OPERATION_STATE_SCHEDULED: The operation is scheduled.
      OPERATION_STATE_WAITING_FOR_PERMISSIONS: Waiting for necessary
        permissions.
      OPERATION_STATE_RUNNING: The operation is running.
      OPERATION_STATE_SUCCEEDED: The operation was completed successfully.
      OPERATION_STATE_FAILED: The operation failed.
      OPERATION_STATE_CANCELLED: The operation was cancelled by the user.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    OPERATION_STATE_SCHEDULED = 1
    OPERATION_STATE_WAITING_FOR_PERMISSIONS = 2
    OPERATION_STATE_RUNNING = 3
    OPERATION_STATE_SUCCEEDED = 4
    OPERATION_STATE_FAILED = 5
    OPERATION_STATE_CANCELLED = 6

  createLinkRequest = _messages.MessageField('CreateLinkRequest', 1)
  deleteLinkRequest = _messages.MessageField('DeleteLinkRequest', 2)
  endTime = _messages.StringField(3)
  startTime = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class ListBucketsResponse(_messages.Message):
  r"""The response from ListBuckets.

  Fields:
    buckets: A list of buckets.
    nextPageToken: If there might be more results than appear in this
      response, then nextPageToken is included. To get the next set of
      results, call the same method again using the value of nextPageToken as
      pageToken.
  """

  buckets = _messages.MessageField('LogBucket', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListExclusionsResponse(_messages.Message):
  r"""Result returned from ListExclusions.

  Fields:
    exclusions: A list of exclusions.
    nextPageToken: If there might be more results than appear in this
      response, then nextPageToken is included. To get the next set of
      results, call the same method again using the value of nextPageToken as
      pageToken.
  """

  exclusions = _messages.MessageField('LogExclusion', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLinksResponse(_messages.Message):
  r"""The response from ListLinks.

  Fields:
    links: A list of links.
    nextPageToken: If there might be more results than those appearing in this
      response, then nextPageToken is included. To get the next set of
      results, call the same method again using the value of nextPageToken as
      pageToken.
  """

  links = _messages.MessageField('Link', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLogEntriesRequest(_messages.Message):
  r"""The parameters to ListLogEntries.

  Fields:
    filter: Optional. A filter that chooses which log entries to return. For
      more information, see Logging query language
      (https://cloud.google.com/logging/docs/view/logging-query-language).Only
      log entries that match the filter are returned. An empty filter matches
      all log entries in the resources listed in resource_names. Referencing a
      parent resource that is not listed in resource_names will cause the
      filter to return no results. The maximum length of a filter is 20,000
      characters.
    orderBy: Optional. How the results should be sorted. Presently, the only
      permitted values are "timestamp asc" (default) and "timestamp desc". The
      first option returns entries in order of increasing values of
      LogEntry.timestamp (oldest first), and the second option returns entries
      in order of decreasing timestamps (newest first). Entries with equal
      timestamps are returned in order of their insert_id values.
    pageSize: Optional. The maximum number of results to return from this
      request. Default is 50. If the value is negative or exceeds 1000, the
      request is rejected. The presence of next_page_token in the response
      indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. page_token must be the value of
      next_page_token from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    projectIds: Optional. Deprecated. Use resource_names instead. One or more
      project identifiers or project numbers from which to retrieve log
      entries. Example: "my-project-1A".
    resourceNames: Required. Names of one or more parent resources from which
      to retrieve log entries: projects/[PROJECT_ID]
      organizations/[ORGANIZATION_ID] billingAccounts/[BILLING_ACCOUNT_ID]
      folders/[FOLDER_ID]May alternatively be one or more views: projects/[PRO
      JECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID] org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      /views/[VIEW_ID] billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATIO
      N_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID] folders/[FOLDER_ID]/locations/
      [LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]Projects listed in the
      project_ids field are added to this list. A maximum of 100 resources may
      be specified in a single request.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  projectIds = _messages.StringField(5, repeated=True)
  resourceNames = _messages.StringField(6, repeated=True)


class ListLogEntriesResponse(_messages.Message):
  r"""Result returned from ListLogEntries.

  Fields:
    entries: A list of log entries. If entries is empty, nextPageToken may
      still be returned, indicating that more entries may exist. See
      nextPageToken for more information.
    nextPageToken: If there might be more results than those appearing in this
      response, then nextPageToken is included. To get the next set of
      results, call this method again using the value of nextPageToken as
      pageToken.If a value for next_page_token appears and the entries field
      is empty, it means that the search found no log entries so far but it
      did not have time to search all the possible log entries. Retry the
      method with this value for page_token to continue the search.
      Alternatively, consider speeding up the search by changing your filter
      to specify a single log name or resource type, or to narrow the time
      range of the search.
  """

  entries = _messages.MessageField('LogEntry', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLogMetricsResponse(_messages.Message):
  r"""Result returned from ListLogMetrics.

  Fields:
    metrics: A list of logs-based metrics.
    nextPageToken: If there might be more results than appear in this
      response, then nextPageToken is included. To get the next set of
      results, call this method again using the value of nextPageToken as
      pageToken.
  """

  metrics = _messages.MessageField('LogMetric', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLogsResponse(_messages.Message):
  r"""Result returned from ListLogs.

  Fields:
    logNames: A list of log names. For example, "projects/my-
      project/logs/syslog" or
      "organizations/123/logs/cloudresourcemanager.googleapis.com%2Factivity".
    nextPageToken: If there might be more results than those appearing in this
      response, then nextPageToken is included. To get the next set of
      results, call this method again using the value of nextPageToken as
      pageToken.
  """

  logNames = _messages.StringField(1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMonitoredResourceDescriptorsResponse(_messages.Message):
  r"""Result returned from ListMonitoredResourceDescriptors.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then nextPageToken is included. To get the next set of
      results, call this method again using the value of nextPageToken as
      pageToken.
    resourceDescriptors: A list of resource descriptors.
  """

  nextPageToken = _messages.StringField(1)
  resourceDescriptors = _messages.MessageField('MonitoredResourceDescriptor', 2, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListSinksResponse(_messages.Message):
  r"""Result returned from ListSinks.

  Fields:
    nextPageToken: If there might be more results than appear in this
      response, then nextPageToken is included. To get the next set of
      results, call the same method again using the value of nextPageToken as
      pageToken.
    sinks: A list of sinks.
  """

  nextPageToken = _messages.StringField(1)
  sinks = _messages.MessageField('LogSink', 2, repeated=True)


class ListViewsResponse(_messages.Message):
  r"""The response from ListViews.

  Fields:
    nextPageToken: If there might be more results than appear in this
      response, then nextPageToken is included. To get the next set of
      results, call the same method again using the value of nextPageToken as
      pageToken.
    views: A list of views.
  """

  nextPageToken = _messages.StringField(1)
  views = _messages.MessageField('LogView', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: "us-east1".
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: "projects/example-project/locations/us-
      east1"
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LocationMetadata(_messages.Message):
  r"""Cloud Logging specific location metadata.

  Fields:
    logAnalyticsEnabled: Indicates whether or not Log Analytics features are
      supported in the given location.
  """

  logAnalyticsEnabled = _messages.BooleanField(1)


class LogBucket(_messages.Message):
  r"""Describes a repository in which log entries are stored.

  Enums:
    LifecycleStateValueValuesEnum: Output only. The bucket lifecycle state.
    UnmetAnalyticsUpgradeRequirementsValueListEntryValuesEnum:

  Fields:
    analyticsEnabled: Whether log analytics is enabled for this bucket.Once
      enabled, log analytics features cannot be disabled.
    analyticsUpgradeTime: Output only. The time that the bucket was upgraded
      to enable analytics. This will eventually be deprecated once there is
      not a need to upgrade existing buckets (i.e. when analytics becomes
      default-enabled).
    cmekSettings: The CMEK settings of the log bucket. If present, new log
      entries written to this log bucket are encrypted using the CMEK key
      provided in this configuration. If a log bucket has CMEK settings, the
      CMEK settings cannot be disabled later by updating the log bucket.
      Changing the KMS key is allowed.
    createTime: Output only. The creation timestamp of the bucket. This is not
      set for any of the default buckets.
    description: Describes this bucket.
    indexConfigs: A list of indexed fields and related configuration data.
    lifecycleState: Output only. The bucket lifecycle state.
    linkedBigqueryDataset: Output only. The name of the BigQuery dataset this
      log bucket is linked to.For
      example:bigquery.googleapis.com/projects/[PROJECT_ID]/datasets/[DATASET]
      DEPRECATED: Do not use this field. Use the Link API instead.
    locked: Whether the bucket is locked.The retention period on a locked
      bucket cannot be changed. Locked buckets may only be deleted if they are
      empty.
    logLink: Configures a linked dataset in BigQuery corresponding to this log
      bucket.Requires analytics_enabled to be true. A log link can only be
      enabled by updating an existing bucket with analytics enabled.
      DEPRECATED: Do not use this field. Use the link API instead.
    name: Output only. The resource name of the bucket.For
      example:projects/my-project/locations/global/buckets/my-bucketFor a list
      of supported locations, see Supported Regions
      (https://cloud.google.com/logging/docs/region-support)For the location
      of global it is unspecified where log entries are actually stored.After
      a bucket has been created, the location cannot be changed.
    restrictedFields: Log entry field paths that are denied access in this
      bucket.The following fields and their children are eligible:
      textPayload, jsonPayload, protoPayload, httpRequest, labels,
      sourceLocation.Restricting a repeated field will restrict all values.
      Adding a parent will block all child fields. (e.g. foo.bar will block
      foo.bar.baz)
    retentionDays: Logs will be retained by default for this amount of time,
      after which they will automatically be deleted. The minimum retention
      period is 1 day. If this value is set to zero at bucket creation time,
      the default time of 30 days will be used.
    unmetAnalyticsUpgradeRequirements: Output only. The requirements for an
      upgrade to analytics that are not satisfied by the current bucket
      configuration, in an arbitrary order. This will eventually be deprecated
      once there is not a need to upgrade existing buckets (i.e. when
      analytics becomes default-enabled).
    updateTime: Output only. The last update timestamp of the bucket.
  """

  class LifecycleStateValueValuesEnum(_messages.Enum):
    r"""Output only. The bucket lifecycle state.

    Values:
      LIFECYCLE_STATE_UNSPECIFIED: Unspecified state. This is only used/useful
        for distinguishing unset values.
      ACTIVE: The normal and active state.
      DELETE_REQUESTED: The resource has been marked for deletion by the user.
        For some resources (e.g. buckets), this can be reversed by an un-
        delete operation.
      UPDATING: The resource has been marked for an update by the user. It
        will remain in this state until the update is complete.
      CREATING: The resource has been marked for creation by the user. It will
        remain in this state until the creation is complete.
      FAILED: The resource is in an INTERNAL error state.
      ARCHIVED: The resource has been archived such that it can still be
        queried but can no longer be modified or used as a sink destination.
        The source bucket after a move bucket operation enters this state.
    """
    LIFECYCLE_STATE_UNSPECIFIED = 0
    ACTIVE = 1
    DELETE_REQUESTED = 2
    UPDATING = 3
    CREATING = 4
    FAILED = 5
    ARCHIVED = 6

  class UnmetAnalyticsUpgradeRequirementsValueListEntryValuesEnum(_messages.Enum):
    r"""UnmetAnalyticsUpgradeRequirementsValueListEntryValuesEnum enum type.

    Values:
      REQUIREMENT_UNSPECIFIED: Unexpected default.
      ACTIVE_LIFECYCLE_STATE: The requirement that a bucket must be in the
        ACTIVE lifecycle state.
      GLOBAL_BUCKET_REGION: The requirement that a bucket must be in the
        "global" region. This requirement is deprecated and replaced with
        SUPPORTED_BUCKET_REGION.
      DEFAULT_RETENTION_DURATION: The requirement that buckets other than the
        "_Required" bucket must have the default retention duration of 30 days
        set. This requirement is deprecated as buckets with custom retention
        can now upgrade to Log Analytics.
      REQUIRED_RETENTION_DURATION: The requirement that the "_Required" bucket
        must have its default retention of 400 days set.
      FIELD_LEVEL_ACCESS_CONTROLS_UNSET: The requirement that no field level
        access controls are configured for the bucket.
      CMEK_UNSET: The requirement that no CMEK configuration is set for the
        bucket.
      NOT_LOCKED: The requirement that the bucket is not locked.
      ORGANIZATION_BUCKET: The requirement that the bucket must not be
        contained within an org.
      FOLDER_BUCKET: The requirement that the bucket must not be contained
        within a folder.
      BILLING_ACCOUNT_BUCKET: The requirement that the bucket must not be
        contained within a billing account.
      SUPPORTED_BUCKET_REGION: The requirement that the bucket must be in a
        region supported by Log Analytics.
    """
    REQUIREMENT_UNSPECIFIED = 0
    ACTIVE_LIFECYCLE_STATE = 1
    GLOBAL_BUCKET_REGION = 2
    DEFAULT_RETENTION_DURATION = 3
    REQUIRED_RETENTION_DURATION = 4
    FIELD_LEVEL_ACCESS_CONTROLS_UNSET = 5
    CMEK_UNSET = 6
    NOT_LOCKED = 7
    ORGANIZATION_BUCKET = 8
    FOLDER_BUCKET = 9
    BILLING_ACCOUNT_BUCKET = 10
    SUPPORTED_BUCKET_REGION = 11

  analyticsEnabled = _messages.BooleanField(1)
  analyticsUpgradeTime = _messages.StringField(2)
  cmekSettings = _messages.MessageField('CmekSettings', 3)
  createTime = _messages.StringField(4)
  description = _messages.StringField(5)
  indexConfigs = _messages.MessageField('IndexConfig', 6, repeated=True)
  lifecycleState = _messages.EnumField('LifecycleStateValueValuesEnum', 7)
  linkedBigqueryDataset = _messages.StringField(8)
  locked = _messages.BooleanField(9)
  logLink = _messages.MessageField('LogLink', 10)
  name = _messages.StringField(11)
  restrictedFields = _messages.StringField(12, repeated=True)
  retentionDays = _messages.IntegerField(13, variant=_messages.Variant.INT32)
  unmetAnalyticsUpgradeRequirements = _messages.EnumField('UnmetAnalyticsUpgradeRequirementsValueListEntryValuesEnum', 14, repeated=True)
  updateTime = _messages.StringField(15)


class LogEntry(_messages.Message):
  r"""An individual entry in a log.

  Enums:
    SeverityValueValuesEnum: Optional. The severity of the log entry. The
      default value is LogSeverity.DEFAULT.

  Messages:
    JsonPayloadValue: The log entry payload, represented as a structure that
      is expressed as a JSON object.
    LabelsValue: Optional. A map of key, value pairs that provides additional
      information about the log entry. The labels can be user-defined or
      system-defined.User-defined labels are arbitrary key, value pairs that
      you can use to classify logs.System-defined labels are defined by GCP
      services for platform logs. They have two components - a service
      namespace component and the attribute name. For example:
      compute.googleapis.com/resource_name.Cloud Logging truncates label keys
      that exceed 512 B and label values that exceed 64 KB upon their
      associated log entry being written. The truncation is indicated by an
      ellipsis at the end of the character string.
    ProtoPayloadValue: The log entry payload, represented as a protocol
      buffer. Some Google Cloud Platform services use this field for their log
      entry payloads.The following protocol buffer types are supported; user-
      defined types are not
      supported:"type.googleapis.com/google.cloud.audit.AuditLog"
      "type.googleapis.com/google.appengine.logging.v1.RequestLog"

  Fields:
    httpRequest: Optional. Information about the HTTP request associated with
      this log entry, if applicable.
    insertId: Optional. A unique identifier for the log entry. If you provide
      a value, then Logging considers other log entries in the same project,
      with the same timestamp, and with the same insert_id to be duplicates
      which are removed in a single query result. However, there are no
      guarantees of de-duplication in the export of logs.If the insert_id is
      omitted when writing a log entry, the Logging API assigns its own unique
      identifier in this field.In queries, the insert_id is also used to order
      log entries that have the same log_name and timestamp values.
    jsonPayload: The log entry payload, represented as a structure that is
      expressed as a JSON object.
    labels: Optional. A map of key, value pairs that provides additional
      information about the log entry. The labels can be user-defined or
      system-defined.User-defined labels are arbitrary key, value pairs that
      you can use to classify logs.System-defined labels are defined by GCP
      services for platform logs. They have two components - a service
      namespace component and the attribute name. For example:
      compute.googleapis.com/resource_name.Cloud Logging truncates label keys
      that exceed 512 B and label values that exceed 64 KB upon their
      associated log entry being written. The truncation is indicated by an
      ellipsis at the end of the character string.
    logName: Required. The resource name of the log to which this log entry
      belongs: "projects/[PROJECT_ID]/logs/[LOG_ID]"
      "organizations/[ORGANIZATION_ID]/logs/[LOG_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/logs/[LOG_ID]"
      "folders/[FOLDER_ID]/logs/[LOG_ID]" A project number may be used in
      place of PROJECT_ID. The project number is translated to its
      corresponding PROJECT_ID internally and the log_name field will contain
      PROJECT_ID in queries and exports.[LOG_ID] must be URL-encoded within
      log_name. Example: "organizations/*********0/logs/cloudresourcemanager.g
      oogleapis.com%2Factivity".[LOG_ID] must be less than 512 characters long
      and can only include the following characters: upper and lower case
      alphanumeric characters, forward-slash, underscore, hyphen, and
      period.For backward compatibility, if log_name begins with a forward-
      slash, such as /projects/..., then the log entry is processed as usual,
      but the forward-slash is removed. Listing the log entry will not show
      the leading slash and filtering for a log name with a leading slash will
      never return any results.
    metadata: Output only. Deprecated. This field is not used by Logging. Any
      value written to it is cleared.
    operation: Optional. Information about an operation associated with the
      log entry, if applicable.
    protoPayload: The log entry payload, represented as a protocol buffer.
      Some Google Cloud Platform services use this field for their log entry
      payloads.The following protocol buffer types are supported; user-defined
      types are not
      supported:"type.googleapis.com/google.cloud.audit.AuditLog"
      "type.googleapis.com/google.appengine.logging.v1.RequestLog"
    receiveTimestamp: Output only. The time the log entry was received by
      Logging.
    resource: Required. The monitored resource that produced this log
      entry.Example: a log entry that reports a database error would be
      associated with the monitored resource designating the particular
      database that reported the error.
    severity: Optional. The severity of the log entry. The default value is
      LogSeverity.DEFAULT.
    sourceLocation: Optional. Source code location information associated with
      the log entry, if any.
    spanId: Optional. The ID of the Cloud Trace
      (https://cloud.google.com/trace) span associated with the current
      operation in which the log is being written. For example, if a span has
      the REST resource name of "projects/some-project/traces/some-
      trace/spans/some-span-id", then the span_id field is "some-span-id".A
      Span (https://cloud.google.com/trace/docs/reference/v2/rest/v2/projects.
      traces/batchWrite#Span) represents a single operation within a trace.
      Whereas a trace may involve multiple different microservices running on
      multiple different machines, a span generally corresponds to a single
      logical operation being performed in a single instance of a microservice
      on one specific machine. Spans are the nodes within the tree that is a
      trace.Applications that are instrumented for tracing
      (https://cloud.google.com/trace/docs/setup) will generally assign a new,
      unique span ID on each incoming request. It is also common to create and
      record additional spans corresponding to internal processing elements as
      well as issuing requests to dependencies.The span ID is expected to be a
      16-character, hexadecimal encoding of an 8-byte array and should not be
      zero. It should be unique within the trace and should, ideally, be
      generated in a manner that is uniformly random.Example values:
      000000000000004a 7a2190356c3fc94b 0000f00300090021 d39223e101960076
    split: Optional. Information indicating this LogEntry is part of a
      sequence of multiple log entries split from a single LogEntry.
    textPayload: The log entry payload, represented as a Unicode string
      (UTF-8).
    timestamp: Optional. The time the event described by the log entry
      occurred. This time is used to compute the log entry's age and to
      enforce the logs retention period. If this field is omitted in a new log
      entry, then Logging assigns it the current time. Timestamps have
      nanosecond accuracy, but trailing zeros in the fractional seconds might
      be omitted when the timestamp is displayed.Incoming log entries must
      have timestamps that don't exceed the logs retention period
      (https://cloud.google.com/logging/quotas#logs_retention_periods) in the
      past, and that don't exceed 24 hours in the future. Log entries outside
      those time boundaries are rejected by Logging.
    trace: Optional. The REST resource name of the trace being written to
      Cloud Trace (https://cloud.google.com/trace) in association with this
      log entry. For example, if your trace data is stored in the Cloud
      project "my-trace-project" and if the service that is creating the log
      entry receives a trace header that includes the trace ID "12345", then
      the service should use "projects/my-tracing-project/traces/12345".The
      trace field provides the link between logs and traces. By using this
      field, you can navigate from a log entry to a trace.
    traceSampled: Optional. The sampling decision of the trace associated with
      the log entry.True means that the trace resource name in the trace field
      was sampled for storage in a trace backend. False means that the trace
      was not sampled for storage when this log entry was written, or the
      sampling decision was unknown at the time. A non-sampled trace value is
      still useful as a request correlation identifier. The default is False.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Optional. The severity of the log entry. The default value is
    LogSeverity.DEFAULT.

    Values:
      DEFAULT: (0) The log entry has no assigned severity level.
      DEBUG: (100) Debug or trace information.
      INFO: (200) Routine information, such as ongoing status or performance.
      NOTICE: (300) Normal but significant events, such as start up, shut
        down, or a configuration change.
      WARNING: (400) Warning events might cause problems.
      ERROR: (500) Error events are likely to cause problems.
      CRITICAL: (600) Critical events cause more severe problems or outages.
      ALERT: (700) A person must take an action immediately.
      EMERGENCY: (800) One or more systems are unusable.
    """
    DEFAULT = 0
    DEBUG = 1
    INFO = 2
    NOTICE = 3
    WARNING = 4
    ERROR = 5
    CRITICAL = 6
    ALERT = 7
    EMERGENCY = 8

  @encoding.MapUnrecognizedFields('additionalProperties')
  class JsonPayloadValue(_messages.Message):
    r"""The log entry payload, represented as a structure that is expressed as
    a JSON object.

    Messages:
      AdditionalProperty: An additional property for a JsonPayloadValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a JsonPayloadValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. A map of key, value pairs that provides additional
    information about the log entry. The labels can be user-defined or system-
    defined.User-defined labels are arbitrary key, value pairs that you can
    use to classify logs.System-defined labels are defined by GCP services for
    platform logs. They have two components - a service namespace component
    and the attribute name. For example:
    compute.googleapis.com/resource_name.Cloud Logging truncates label keys
    that exceed 512 B and label values that exceed 64 KB upon their associated
    log entry being written. The truncation is indicated by an ellipsis at the
    end of the character string.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ProtoPayloadValue(_messages.Message):
    r"""The log entry payload, represented as a protocol buffer. Some Google
    Cloud Platform services use this field for their log entry payloads.The
    following protocol buffer types are supported; user-defined types are not
    supported:"type.googleapis.com/google.cloud.audit.AuditLog"
    "type.googleapis.com/google.appengine.logging.v1.RequestLog"

    Messages:
      AdditionalProperty: An additional property for a ProtoPayloadValue
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ProtoPayloadValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  httpRequest = _messages.MessageField('HttpRequest', 1)
  insertId = _messages.StringField(2)
  jsonPayload = _messages.MessageField('JsonPayloadValue', 3)
  labels = _messages.MessageField('LabelsValue', 4)
  logName = _messages.StringField(5)
  metadata = _messages.MessageField('MonitoredResourceMetadata', 6)
  operation = _messages.MessageField('LogEntryOperation', 7)
  protoPayload = _messages.MessageField('ProtoPayloadValue', 8)
  receiveTimestamp = _messages.StringField(9)
  resource = _messages.MessageField('MonitoredResource', 10)
  severity = _messages.EnumField('SeverityValueValuesEnum', 11)
  sourceLocation = _messages.MessageField('LogEntrySourceLocation', 12)
  spanId = _messages.StringField(13)
  split = _messages.MessageField('LogSplit', 14)
  textPayload = _messages.StringField(15)
  timestamp = _messages.StringField(16)
  trace = _messages.StringField(17)
  traceSampled = _messages.BooleanField(18)


class LogEntryOperation(_messages.Message):
  r"""Additional information about a potentially long-running operation with
  which a log entry is associated.

  Fields:
    first: Optional. Set this to True if this is the first log entry in the
      operation.
    id: Optional. An arbitrary operation identifier. Log entries with the same
      identifier are assumed to be part of the same operation.
    last: Optional. Set this to True if this is the last log entry in the
      operation.
    producer: Optional. An arbitrary producer identifier. The combination of
      id and producer must be globally unique. Examples for producer:
      "MyDivision.MyBigCompany.com", "github.com/MyProject/MyApplication".
  """

  first = _messages.BooleanField(1)
  id = _messages.StringField(2)
  last = _messages.BooleanField(3)
  producer = _messages.StringField(4)


class LogEntrySourceLocation(_messages.Message):
  r"""Additional information about the source code location that produced the
  log entry.

  Fields:
    file: Optional. Source file name. Depending on the runtime environment,
      this might be a simple name or a fully-qualified name.
    function: Optional. Human-readable name of the function or method being
      invoked, with optional context such as the class or package name. This
      information may be used in contexts such as the logs viewer, where a
      file and line number are less meaningful. The format can vary by
      language. For example: qual.if.ied.Class.method (Java), dir/package.func
      (Go), function (Python).
    line: Optional. Line within the source file. 1-based; 0 indicates no line
      number available.
  """

  file = _messages.StringField(1)
  function = _messages.StringField(2)
  line = _messages.IntegerField(3)


class LogExclusion(_messages.Message):
  r"""Specifies a set of log entries that are filtered out by a sink. If your
  Google Cloud resource receives a large volume of log entries, you can use
  exclusions to reduce your chargeable logs. Note that exclusions on
  organization-level and folder-level sinks don't apply to child resources.
  Note also that you cannot modify the _Required sink or exclude logs from it.

  Fields:
    createTime: Output only. The creation timestamp of the exclusion.This
      field may not be present for older exclusions.
    description: Optional. A description of this exclusion.
    disabled: Optional. If set to True, then this exclusion is disabled and it
      does not exclude any log entries. You can update an exclusion to change
      the value of this field.
    filter: Required. An advanced logs filter
      (https://cloud.google.com/logging/docs/view/advanced-queries) that
      matches the log entries to be excluded. By using the sample function
      (https://cloud.google.com/logging/docs/view/advanced-queries#sample),
      you can exclude less than 100% of the matching log entries.For example,
      the following query matches 99% of low-severity log entries from Google
      Cloud Storage buckets:resource.type=gcs_bucket severity<ERROR
      sample(insertId, 0.99)
    name: Required. A client-assigned identifier, such as "load-balancer-
      exclusion". Identifiers are limited to 100 characters and can include
      only letters, digits, underscores, hyphens, and periods. First character
      has to be alphanumeric.
    updateTime: Output only. The last update timestamp of the exclusion.This
      field may not be present for older exclusions.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  disabled = _messages.BooleanField(3)
  filter = _messages.StringField(4)
  name = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class LogLine(_messages.Message):
  r"""Application log line emitted while processing a request.

  Enums:
    SeverityValueValuesEnum: Severity of this log entry.

  Fields:
    logMessage: App-provided log message.
    severity: Severity of this log entry.
    sourceLocation: Where in the source code this log message was written.
    time: Approximate time when this log entry was made.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Severity of this log entry.

    Values:
      DEFAULT: (0) The log entry has no assigned severity level.
      DEBUG: (100) Debug or trace information.
      INFO: (200) Routine information, such as ongoing status or performance.
      NOTICE: (300) Normal but significant events, such as start up, shut
        down, or a configuration change.
      WARNING: (400) Warning events might cause problems.
      ERROR: (500) Error events are likely to cause problems.
      CRITICAL: (600) Critical events cause more severe problems or outages.
      ALERT: (700) A person must take an action immediately.
      EMERGENCY: (800) One or more systems are unusable.
    """
    DEFAULT = 0
    DEBUG = 1
    INFO = 2
    NOTICE = 3
    WARNING = 4
    ERROR = 5
    CRITICAL = 6
    ALERT = 7
    EMERGENCY = 8

  logMessage = _messages.StringField(1)
  severity = _messages.EnumField('SeverityValueValuesEnum', 2)
  sourceLocation = _messages.MessageField('SourceLocation', 3)
  time = _messages.StringField(4)


class LogLink(_messages.Message):
  r"""DEPRECATED: Use the Link API to create Log Links.

  Fields:
    enabled: Enables a log link, and creates a BigQuery dataset in the same
      parent project as the log bucket. The dataset will be named the same as
      the log bucket name, and will contain views to access the logs in the
      bucket.If an active log link is disabled, the dataset and views are
      deleted.
    loggingServiceAccount: Output only. An IAM service account used by Cloud
      Logging to create views on the destination dataset.
  """

  enabled = _messages.BooleanField(1)
  loggingServiceAccount = _messages.StringField(2)


class LogMetric(_messages.Message):
  r"""Describes a logs-based metric. The value of the metric is the number of
  log entries that match a logs filter in a given time interval.Logs-based
  metrics can also be used to extract values from logs and create a
  distribution of the values. The distribution records the statistics of the
  extracted values along with an optional histogram of the values as specified
  by the bucket options.

  Enums:
    VersionValueValuesEnum: Deprecated. The API version that created or
      updated this metric. The v2 format is used by default and cannot be
      changed.

  Messages:
    LabelExtractorsValue: Optional. A map from a label key string to an
      extractor expression which is used to extract data from a log entry
      field and assign as the label value. Each label key specified in the
      LabelDescriptor must have an associated extractor expression in this
      map. The syntax of the extractor expression is the same as for the
      value_extractor field.The extracted value is converted to the type
      defined in the label descriptor. If either the extraction or the type
      conversion fails, the label will have a default value. The default value
      for a string label is an empty string, for an integer label its 0, and
      for a boolean label its false.Note that there are upper bounds on the
      maximum number of labels and the number of active time series that are
      allowed in a project.

  Fields:
    bucketName: Optional. The resource name of the Log Bucket that owns the
      Log Metric. Only Log Buckets in projects are supported. The bucket has
      to be in the same project as the metric.For example:projects/my-
      project/locations/global/buckets/my-bucketIf empty, then the Log Metric
      is considered a non-Bucket Log Metric.
    bucketOptions: Optional. The bucket_options are required when the logs-
      based metric is using a DISTRIBUTION value type and it describes the
      bucket boundaries used to create a histogram of the extracted values.
    createTime: Output only. The creation timestamp of the metric.This field
      may not be present for older metrics.
    description: Optional. A description of this metric, which is used in
      documentation. The maximum length of the description is 8000 characters.
    disabled: Optional. If set to True, then this metric is disabled and it
      does not generate any points.
    filter: Required. An advanced logs filter
      (https://cloud.google.com/logging/docs/view/advanced_filters) which is
      used to match log entries. Example: "resource.type=gae_app AND
      severity>=ERROR" The maximum length of the filter is 20000 characters.
    labelExtractors: Optional. A map from a label key string to an extractor
      expression which is used to extract data from a log entry field and
      assign as the label value. Each label key specified in the
      LabelDescriptor must have an associated extractor expression in this
      map. The syntax of the extractor expression is the same as for the
      value_extractor field.The extracted value is converted to the type
      defined in the label descriptor. If either the extraction or the type
      conversion fails, the label will have a default value. The default value
      for a string label is an empty string, for an integer label its 0, and
      for a boolean label its false.Note that there are upper bounds on the
      maximum number of labels and the number of active time series that are
      allowed in a project.
    metricDescriptor: Optional. The metric descriptor associated with the
      logs-based metric. If unspecified, it uses a default metric descriptor
      with a DELTA metric kind, INT64 value type, with no labels and a unit of
      "1". Such a metric counts the number of log entries matching the filter
      expression.The name, type, and description fields in the
      metric_descriptor are output only, and is constructed using the name and
      description field in the LogMetric.To create a logs-based metric that
      records a distribution of log values, a DELTA metric kind with a
      DISTRIBUTION value type must be used along with a value_extractor
      expression in the LogMetric.Each label in the metric descriptor must
      have a matching label name as the key and an extractor expression as the
      value in the label_extractors map.The metric_kind and value_type fields
      in the metric_descriptor cannot be updated once initially configured.
      New labels can be added in the metric_descriptor, but existing labels
      cannot be modified except for their description.
    name: Required. The client-assigned metric identifier. Examples:
      "error_count", "nginx/requests".Metric identifiers are limited to 100
      characters and can include only the following characters: A-Z, a-z, 0-9,
      and the special characters _-.,+!*',()%/. The forward-slash character
      (/) denotes a hierarchy of name pieces, and it cannot be the first
      character of the name.This field is the [METRIC_ID] part of a metric
      resource name in the format "projects/PROJECT_ID/metrics/METRIC_ID".
      Example: If the resource name of a metric is "projects/my-
      project/metrics/nginx%2Frequests", this field's value is
      "nginx/requests".
    updateTime: Output only. The last update timestamp of the metric.This
      field may not be present for older metrics.
    valueExtractor: Optional. A value_extractor is required when using a
      distribution logs-based metric to extract the values to record from a
      log entry. Two functions are supported for value extraction:
      EXTRACT(field) or REGEXP_EXTRACT(field, regex). The arguments are:
      field: The name of the log entry field from which the value is to be
      extracted. regex: A regular expression using the Google RE2 syntax
      (https://github.com/google/re2/wiki/Syntax) with a single capture group
      to extract data from the specified log entry field. The value of the
      field is converted to a string before applying the regex. It is an error
      to specify a regex that does not include exactly one capture group.The
      result of the extraction must be convertible to a double type, as the
      distribution always records double values. If either the extraction or
      the conversion to double fails, then those values are not recorded in
      the distribution.Example: REGEXP_EXTRACT(jsonPayload.request,
      ".*quantity=(\d+).*")
    version: Deprecated. The API version that created or updated this metric.
      The v2 format is used by default and cannot be changed.
  """

  class VersionValueValuesEnum(_messages.Enum):
    r"""Deprecated. The API version that created or updated this metric. The
    v2 format is used by default and cannot be changed.

    Values:
      V2: Logging API v2.
      V1: Logging API v1.
    """
    V2 = 0
    V1 = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelExtractorsValue(_messages.Message):
    r"""Optional. A map from a label key string to an extractor expression
    which is used to extract data from a log entry field and assign as the
    label value. Each label key specified in the LabelDescriptor must have an
    associated extractor expression in this map. The syntax of the extractor
    expression is the same as for the value_extractor field.The extracted
    value is converted to the type defined in the label descriptor. If either
    the extraction or the type conversion fails, the label will have a default
    value. The default value for a string label is an empty string, for an
    integer label its 0, and for a boolean label its false.Note that there are
    upper bounds on the maximum number of labels and the number of active time
    series that are allowed in a project.

    Messages:
      AdditionalProperty: An additional property for a LabelExtractorsValue
        object.

    Fields:
      additionalProperties: Additional properties of type LabelExtractorsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelExtractorsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bucketName = _messages.StringField(1)
  bucketOptions = _messages.MessageField('BucketOptions', 2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  disabled = _messages.BooleanField(5)
  filter = _messages.StringField(6)
  labelExtractors = _messages.MessageField('LabelExtractorsValue', 7)
  metricDescriptor = _messages.MessageField('MetricDescriptor', 8)
  name = _messages.StringField(9)
  updateTime = _messages.StringField(10)
  valueExtractor = _messages.StringField(11)
  version = _messages.EnumField('VersionValueValuesEnum', 12)


class LogSink(_messages.Message):
  r"""Describes a sink used to export log entries to one of the following
  destinations in any project: a Cloud Storage bucket, a BigQuery dataset, a
  Pub/Sub topic or a Cloud Logging log bucket. A logs filter controls which
  log entries are exported. The sink must be created within a project,
  organization, billing account, or folder.

  Enums:
    OutputVersionFormatValueValuesEnum: Deprecated. This field is unused.

  Fields:
    bigqueryOptions: Optional. Options that affect sinks exporting data to
      BigQuery.
    createTime: Output only. The creation timestamp of the sink.This field may
      not be present for older sinks.
    description: Optional. A description of this sink.The maximum length of
      the description is 8000 characters.
    destination: Required. The export destination:
      "storage.googleapis.com/[GCS_BUCKET]"
      "bigquery.googleapis.com/projects/[PROJECT_ID]/datasets/[DATASET]"
      "pubsub.googleapis.com/projects/[PROJECT_ID]/topics/[TOPIC_ID]"
      "logging.googleapis.com/projects/[PROJECT_ID]" "logging.googleapis.com/p
      rojects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" The
      sink's writer_identity, set when the sink is created, must have
      permission to write to the destination or else the log entries are not
      exported. For more information, see Exporting Logs with Sinks
      (https://cloud.google.com/logging/docs/api/tasks/exporting-logs).
    disabled: Optional. If set to true, then this sink is disabled and it does
      not export any log entries.
    exclusions: Optional. Log entries that match any of these exclusion
      filters will not be exported.If a log entry is matched by both filter
      and one of exclusion_filters it will not be exported.
    filter: Optional. An advanced logs filter
      (https://cloud.google.com/logging/docs/view/advanced-queries). The only
      exported log entries are those that are in the resource owning the sink
      and that match the filter.For
      example:logName="projects/[PROJECT_ID]/logs/[LOG_ID]" AND
      severity>=ERROR
    includeChildren: Optional. This field applies only to sinks owned by
      organizations and folders. If the field is false, the default, only the
      logs owned by the sink's parent resource are available for export. If
      the field is true, then log entries from all the projects, folders, and
      billing accounts contained in the sink's parent resource are also
      available for export. Whether a particular log entry from the children
      is exported depends on the sink's filter expression.For example, if this
      field is true, then the filter resource.type=gce_instance would export
      all Compute Engine VM instance log entries from all projects in the
      sink's parent.To only export entries from certain child projects, filter
      on the project part of the log name:logName:("projects/test-project1/"
      OR "projects/test-project2/") AND resource.type=gce_instance
    name: Required. The client-assigned sink identifier, unique within the
      project.For example: "my-syslog-errors-to-pubsub". Sink identifiers are
      limited to 100 characters and can include only the following characters:
      upper and lower-case alphanumeric characters, underscores, hyphens, and
      periods. First character has to be alphanumeric.
    outputVersionFormat: Deprecated. This field is unused.
    updateTime: Output only. The last update timestamp of the sink.This field
      may not be present for older sinks.
    writerIdentity: Output only. An IAM identity-a service account or group-
      under which Cloud Logging writes the exported log entries to the sink's
      destination. This field is either set by specifying
      custom_writer_identity or set automatically by sinks.create and
      sinks.update based on the value of unique_writer_identity in those
      methods.Until you grant this identity write-access to the destination,
      log entry exports from this sink will fail. For more information, see
      Granting Access for a Resource
      (https://cloud.google.com/iam/docs/granting-roles-to-service-
      accounts#granting_access_to_a_service_account_for_a_resource). Consult
      the destination service's documentation to determine the appropriate IAM
      roles to assign to the identity.Sinks that have a destination that is a
      log bucket in the same project as the sink cannot have a writer_identity
      and no additional permissions are required.
  """

  class OutputVersionFormatValueValuesEnum(_messages.Enum):
    r"""Deprecated. This field is unused.

    Values:
      VERSION_FORMAT_UNSPECIFIED: An unspecified format version that will
        default to V2.
      V2: LogEntry version 2 format.
      V1: LogEntry version 1 format.
    """
    VERSION_FORMAT_UNSPECIFIED = 0
    V2 = 1
    V1 = 2

  bigqueryOptions = _messages.MessageField('BigQueryOptions', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  destination = _messages.StringField(4)
  disabled = _messages.BooleanField(5)
  exclusions = _messages.MessageField('LogExclusion', 6, repeated=True)
  filter = _messages.StringField(7)
  includeChildren = _messages.BooleanField(8)
  name = _messages.StringField(9)
  outputVersionFormat = _messages.EnumField('OutputVersionFormatValueValuesEnum', 10)
  updateTime = _messages.StringField(11)
  writerIdentity = _messages.StringField(12)


class LogSplit(_messages.Message):
  r"""Additional information used to correlate multiple log entries. Used when
  a single LogEntry would exceed the Google Cloud Logging size limit and is
  split across multiple log entries.

  Fields:
    index: The index of this LogEntry in the sequence of split log entries.
      Log entries are given |index| values 0, 1, ..., n-1 for a sequence of n
      log entries.
    totalSplits: The total number of log entries that the original LogEntry
      was split into.
    uid: A globally unique identifier for all log entries in a sequence of
      split log entries. All log entries with the same |LogSplit.uid| are
      assumed to be part of the same sequence of split log entries.
  """

  index = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  totalSplits = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  uid = _messages.StringField(3)


class LogView(_messages.Message):
  r"""Describes a view over log entries in a bucket.

  Fields:
    createTime: Output only. The creation timestamp of the view.
    description: Describes this view.
    filter: Filter that restricts which log entries in a bucket are visible in
      this view.Filters are restricted to be a logical AND of ==/!= of any of
      the following: originating project/folder/organization/billing account.
      resource type log idFor example:SOURCE("projects/myproject") AND
      resource.type = "gce_instance" AND LOG_ID("stdout")
    name: The resource name of the view.For example:projects/my-
      project/locations/global/buckets/my-bucket/views/my-view
    schema: Output only. Describes the schema of the logs stored in the bucket
      that are accessible via this view.This field is only populated for views
      in analytics-enabled buckets.
    updateTime: Output only. The last update timestamp of the view.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  filter = _messages.StringField(3)
  name = _messages.StringField(4)
  schema = _messages.MessageField('TableSchema', 5)
  updateTime = _messages.StringField(6)


class LoggingBillingAccountsExclusionsCreateRequest(_messages.Message):
  r"""A LoggingBillingAccountsExclusionsCreateRequest object.

  Fields:
    logExclusion: A LogExclusion resource to be passed as the request body.
    parent: Required. The parent resource in which to create the exclusion:
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]" For
      examples:"projects/my-logging-project" "organizations/*********"
  """

  logExclusion = _messages.MessageField('LogExclusion', 1)
  parent = _messages.StringField(2, required=True)


class LoggingBillingAccountsExclusionsDeleteRequest(_messages.Message):
  r"""A LoggingBillingAccountsExclusionsDeleteRequest object.

  Fields:
    name: Required. The resource name of an existing exclusion to delete:
      "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
      "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
      "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]" For
      example:"projects/my-project/exclusions/my-exclusion"
  """

  name = _messages.StringField(1, required=True)


class LoggingBillingAccountsExclusionsGetRequest(_messages.Message):
  r"""A LoggingBillingAccountsExclusionsGetRequest object.

  Fields:
    name: Required. The resource name of an existing exclusion:
      "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
      "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
      "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]" For
      example:"projects/my-project/exclusions/my-exclusion"
  """

  name = _messages.StringField(1, required=True)


class LoggingBillingAccountsExclusionsListRequest(_messages.Message):
  r"""A LoggingBillingAccountsExclusionsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource whose exclusions are to be listed.
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingBillingAccountsExclusionsPatchRequest(_messages.Message):
  r"""A LoggingBillingAccountsExclusionsPatchRequest object.

  Fields:
    logExclusion: A LogExclusion resource to be passed as the request body.
    name: Required. The resource name of the exclusion to update:
      "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
      "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
      "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]" For
      example:"projects/my-project/exclusions/my-exclusion"
    updateMask: Required. A non-empty list of fields to change in the existing
      exclusion. New values for the fields are taken from the corresponding
      fields in the LogExclusion included in this request. Fields not
      mentioned in update_mask are not changed and are ignored in the
      request.For example, to change the filter and description of an
      exclusion, specify an update_mask of "filter,description".
  """

  logExclusion = _messages.MessageField('LogExclusion', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingBillingAccountsGetCmekSettingsRequest(_messages.Message):
  r"""A LoggingBillingAccountsGetCmekSettingsRequest object.

  Fields:
    name: Required. The resource for which to retrieve CMEK settings.
      "projects/[PROJECT_ID]/cmekSettings"
      "organizations/[ORGANIZATION_ID]/cmekSettings"
      "billingAccounts/[BILLING_ACCOUNT_ID]/cmekSettings"
      "folders/[FOLDER_ID]/cmekSettings" For
      example:"organizations/12345/cmekSettings"Note: CMEK for the Log Router
      can be configured for Google Cloud projects, folders, organizations and
      billing accounts. Once configured for an organization, it applies to all
      projects and folders in the Google Cloud organization.
  """

  name = _messages.StringField(1, required=True)


class LoggingBillingAccountsGetSettingsRequest(_messages.Message):
  r"""A LoggingBillingAccountsGetSettingsRequest object.

  Fields:
    name: Required. The resource for which to retrieve settings.
      "projects/[PROJECT_ID]/settings"
      "organizations/[ORGANIZATION_ID]/settings"
      "billingAccounts/[BILLING_ACCOUNT_ID]/settings"
      "folders/[FOLDER_ID]/settings" For
      example:"organizations/12345/settings"Note: Settings for the Log Router
      can be get for Google Cloud projects, folders, organizations and billing
      accounts. Currently it can only be configured for organizations. Once
      configured for an organization, it applies to all projects and folders
      in the Google Cloud organization.
  """

  name = _messages.StringField(1, required=True)


class LoggingBillingAccountsLocationsBucketsCreateAsyncRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsCreateAsyncRequest object.

  Fields:
    bucketId: Required. A client-assigned identifier such as "my-bucket".
      Identifiers are limited to 100 characters and can include only letters,
      digits, underscores, hyphens, and periods.
    logBucket: A LogBucket resource to be passed as the request body.
    parent: Required. The resource in which to create the log bucket:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]" For
      example:"projects/my-project/locations/global"
  """

  bucketId = _messages.StringField(1)
  logBucket = _messages.MessageField('LogBucket', 2)
  parent = _messages.StringField(3, required=True)


class LoggingBillingAccountsLocationsBucketsCreateRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsCreateRequest object.

  Fields:
    bucketId: Required. A client-assigned identifier such as "my-bucket".
      Identifiers are limited to 100 characters and can include only letters,
      digits, underscores, hyphens, and periods.
    logBucket: A LogBucket resource to be passed as the request body.
    parent: Required. The resource in which to create the log bucket:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]" For
      example:"projects/my-project/locations/global"
  """

  bucketId = _messages.StringField(1)
  logBucket = _messages.MessageField('LogBucket', 2)
  parent = _messages.StringField(3, required=True)


class LoggingBillingAccountsLocationsBucketsDeleteRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsDeleteRequest object.

  Fields:
    name: Required. The full resource name of the bucket to delete.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
  """

  name = _messages.StringField(1, required=True)


class LoggingBillingAccountsLocationsBucketsGetRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsGetRequest object.

  Fields:
    name: Required. The resource name of the bucket:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
  """

  name = _messages.StringField(1, required=True)


class LoggingBillingAccountsLocationsBucketsLinksCreateRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsLinksCreateRequest object.

  Fields:
    link: A Link resource to be passed as the request body.
    linkId: Required. The ID to use for the link. The link_id can have up to
      100 characters. A valid link_id must only have alphanumeric characters
      and underscores within it.
    parent: Required. The full resource name of the bucket to create a link
      for. "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
      "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET
      _ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buck
      ets/[BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  """

  link = _messages.MessageField('Link', 1)
  linkId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingBillingAccountsLocationsBucketsLinksDeleteRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsLinksDeleteRequest object.

  Fields:
    name: Required. The full resource name of the link to delete. "projects/[P
      ROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
      "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET
      _ID]/links/[LINK_ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[L
      OCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]" "folders/[FOLDER_ID]/lo
      cations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  """

  name = _messages.StringField(1, required=True)


class LoggingBillingAccountsLocationsBucketsLinksGetRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsLinksGetRequest object.

  Fields:
    name: Required. The resource name of the link: "projects/[PROJECT_ID]/loca
      tions/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]" "organizations/
      [ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LIN
      K_ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buc
      kets/[BUCKET_ID]/links/[LINK_ID]" "folders/[FOLDER_ID]/locations/[LOCATI
      ON_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  """

  name = _messages.StringField(1, required=True)


class LoggingBillingAccountsLocationsBucketsLinksListRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsLinksListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response.
    parent: Required. The parent resource whose links are to be listed:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingBillingAccountsLocationsBucketsListRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource whose buckets are to be listed:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]"
      "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]" Note: The locations
      portion of the resource must be specified, but supplying the character -
      in place of LOCATION_ID will return all buckets.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingBillingAccountsLocationsBucketsPatchRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsPatchRequest object.

  Fields:
    logBucket: A LogBucket resource to be passed as the request body.
    name: Required. The full resource name of the bucket to update.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
    updateMask: Required. Field mask that specifies the fields in bucket that
      need an update. A bucket field will be overwritten if, and only if, it
      is in the update mask. name and output only fields cannot be updated.For
      a detailed FieldMask definition, see:
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=retention_days
  """

  logBucket = _messages.MessageField('LogBucket', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingBillingAccountsLocationsBucketsUndeleteRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsUndeleteRequest object.

  Fields:
    name: Required. The full resource name of the bucket to undelete.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
    undeleteBucketRequest: A UndeleteBucketRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteBucketRequest = _messages.MessageField('UndeleteBucketRequest', 2)


class LoggingBillingAccountsLocationsBucketsUpdateAsyncRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsUpdateAsyncRequest object.

  Fields:
    logBucket: A LogBucket resource to be passed as the request body.
    name: Required. The full resource name of the bucket to update.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
    updateMask: Required. Field mask that specifies the fields in bucket that
      need an update. A bucket field will be overwritten if, and only if, it
      is in the update mask. name and output only fields cannot be updated.For
      a detailed FieldMask definition, see:
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=retention_days
  """

  logBucket = _messages.MessageField('LogBucket', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingBillingAccountsLocationsBucketsViewsCreateRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsViewsCreateRequest object.

  Fields:
    logView: A LogView resource to be passed as the request body.
    parent: Required. The bucket in which to create the view
      `"projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"`
      For example:"projects/my-project/locations/global/buckets/my-bucket"
    viewId: Required. A client-assigned identifier such as "my-view".
      Identifiers are limited to 100 characters and can include only letters,
      digits, underscores, hyphens, and periods.
  """

  logView = _messages.MessageField('LogView', 1)
  parent = _messages.StringField(2, required=True)
  viewId = _messages.StringField(3)


class LoggingBillingAccountsLocationsBucketsViewsDeleteRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsViewsDeleteRequest object.

  Fields:
    name: Required. The full resource name of the view to delete: "projects/[P
      ROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]"
      For example:"projects/my-project/locations/global/buckets/my-
      bucket/views/my-view"
  """

  name = _messages.StringField(1, required=True)


class LoggingBillingAccountsLocationsBucketsViewsGetRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsViewsGetRequest object.

  Fields:
    name: Required. The resource name of the policy: "projects/[PROJECT_ID]/lo
      cations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]" For
      example:"projects/my-project/locations/global/buckets/my-
      bucket/views/my-view"
  """

  name = _messages.StringField(1, required=True)


class LoggingBillingAccountsLocationsBucketsViewsListRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsViewsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request.Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The bucket whose views are to be listed:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingBillingAccountsLocationsBucketsViewsLogsListRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsViewsLogsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The resource name to list logs for:
      projects/[PROJECT_ID] organizations/[ORGANIZATION_ID]
      billingAccounts/[BILLING_ACCOUNT_ID] folders/[FOLDER_ID]
    resourceNames: Optional. List of resource names to list logs for: projects
      /[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID
      ] organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKE
      T_ID]/views/[VIEW_ID] billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LO
      CATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID] folders/[FOLDER_ID]/locat
      ions/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]To support legacy
      queries, it could also be: projects/[PROJECT_ID]
      organizations/[ORGANIZATION_ID] billingAccounts/[BILLING_ACCOUNT_ID]
      folders/[FOLDER_ID]The resource name in the parent field is added to
      this list.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  resourceNames = _messages.StringField(4, repeated=True)


class LoggingBillingAccountsLocationsBucketsViewsPatchRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsBucketsViewsPatchRequest object.

  Fields:
    logView: A LogView resource to be passed as the request body.
    name: Required. The full resource name of the view to update "projects/[PR
      OJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]"
      For example:"projects/my-project/locations/global/buckets/my-
      bucket/views/my-view"
    updateMask: Optional. Field mask that specifies the fields in view that
      need an update. A field will be overwritten if, and only if, it is in
      the update mask. name and output only fields cannot be updated.For a
      detailed FieldMask definition, see
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=filter
  """

  logView = _messages.MessageField('LogView', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingBillingAccountsLocationsGetRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class LoggingBillingAccountsLocationsListRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like "displayName=tokyo", and is
      documented in more detail in AIP-160 (https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the next_page_token field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class LoggingBillingAccountsLocationsOperationsApproveRedactionRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsOperationsApproveRedactionRequest
  object.

  Fields:
    name: Required. Name of the redaction operation.For example:"projects/my-
      project/locations/global/operations/my-operation"
  """

  name = _messages.StringField(1, required=True)


class LoggingBillingAccountsLocationsOperationsCancelRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class LoggingBillingAccountsLocationsOperationsGetRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class LoggingBillingAccountsLocationsOperationsListRequest(_messages.Message):
  r"""A LoggingBillingAccountsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class LoggingBillingAccountsLogsDeleteRequest(_messages.Message):
  r"""A LoggingBillingAccountsLogsDeleteRequest object.

  Fields:
    logName: Required. The resource name of the log to delete:
      projects/[PROJECT_ID]/logs/[LOG_ID]
      organizations/[ORGANIZATION_ID]/logs/[LOG_ID]
      billingAccounts/[BILLING_ACCOUNT_ID]/logs/[LOG_ID]
      folders/[FOLDER_ID]/logs/[LOG_ID][LOG_ID] must be URL-encoded. For
      example, "projects/my-project-id/logs/syslog",
      "organizations/123/logs/cloudaudit.googleapis.com%2Factivity".For more
      information about log names, see LogEntry.
  """

  logName = _messages.StringField(1, required=True)


class LoggingBillingAccountsLogsListRequest(_messages.Message):
  r"""A LoggingBillingAccountsLogsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The resource name to list logs for:
      projects/[PROJECT_ID] organizations/[ORGANIZATION_ID]
      billingAccounts/[BILLING_ACCOUNT_ID] folders/[FOLDER_ID]
    resourceNames: Optional. List of resource names to list logs for: projects
      /[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID
      ] organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKE
      T_ID]/views/[VIEW_ID] billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LO
      CATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID] folders/[FOLDER_ID]/locat
      ions/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]To support legacy
      queries, it could also be: projects/[PROJECT_ID]
      organizations/[ORGANIZATION_ID] billingAccounts/[BILLING_ACCOUNT_ID]
      folders/[FOLDER_ID]The resource name in the parent field is added to
      this list.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  resourceNames = _messages.StringField(4, repeated=True)


class LoggingBillingAccountsSinksCreateRequest(_messages.Message):
  r"""A LoggingBillingAccountsSinksCreateRequest object.

  Fields:
    customWriterIdentity: Optional. A service account provided by the caller
      that will be used to write the log entries. The format must be
      serviceAccount:some@email. This field can only be specified if you are
      routing logs to a destination outside this sink's project. If not
      specified, a Logging service account will automatically be generated.
    logSink: A LogSink resource to be passed as the request body.
    parent: Required. The resource in which to create the sink:
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]" For
      examples:"projects/my-project" "organizations/*********"
    uniqueWriterIdentity: Optional. Determines the kind of IAM identity
      returned as writer_identity in the new sink. If this value is omitted or
      set to false, and if the sink's parent is a project, then the value
      returned as writer_identity is the same group or service account used by
      Cloud Logging before the addition of writer identities to this API. The
      sink's destination must be in the same project as the sink itself.If
      this field is set to true, or if the sink is owned by a non-project
      resource such as an organization, then the value of writer_identity will
      be a service agent (https://cloud.google.com/iam/docs/service-account-
      types#service-agents) used by the sinks with the same parent. For more
      information, see writer_identity in LogSink.
  """

  customWriterIdentity = _messages.StringField(1)
  logSink = _messages.MessageField('LogSink', 2)
  parent = _messages.StringField(3, required=True)
  uniqueWriterIdentity = _messages.BooleanField(4)


class LoggingBillingAccountsSinksDeleteRequest(_messages.Message):
  r"""A LoggingBillingAccountsSinksDeleteRequest object.

  Fields:
    sinkName: Required. The full resource name of the sink to delete,
      including the parent resource and the sink identifier:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
  """

  sinkName = _messages.StringField(1, required=True)


class LoggingBillingAccountsSinksGetRequest(_messages.Message):
  r"""A LoggingBillingAccountsSinksGetRequest object.

  Fields:
    sinkName: Required. The resource name of the sink:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
  """

  sinkName = _messages.StringField(1, required=True)


class LoggingBillingAccountsSinksListRequest(_messages.Message):
  r"""A LoggingBillingAccountsSinksListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource whose sinks are to be listed:
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingBillingAccountsSinksPatchRequest(_messages.Message):
  r"""A LoggingBillingAccountsSinksPatchRequest object.

  Fields:
    customWriterIdentity: Optional. A service account provided by the caller
      that will be used to write the log entries. Must be of format
      serviceAccount:some@email. This can only be specified if writing to a
      destination outside the sink's project. If not specified, a p4 service
      account will automatically be generated.
    logSink: A LogSink resource to be passed as the request body.
    sinkName: Required. The full resource name of the sink to update,
      including the parent resource and the sink identifier:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
    uniqueWriterIdentity: Optional. See sinks.create for a description of this
      field. When updating a sink, the effect of this field on the value of
      writer_identity in the updated sink depends on both the old and new
      values of this field: If the old and new values of this field are both
      false or both true, then there is no change to the sink's
      writer_identity. If the old value is false and the new value is true,
      then writer_identity is changed to a service agent
      (https://cloud.google.com/iam/docs/service-account-types#service-agents)
      owned by Cloud Logging. It is an error if the old value is true and the
      new value is set to false or defaulted to false.
    updateMask: Optional. Field mask that specifies the fields in sink that
      need an update. A sink field will be overwritten if, and only if, it is
      in the update mask. name and output only fields cannot be updated.An
      empty updateMask is temporarily treated as using the following mask for
      backwards compatibility purposes:destination,filter,includeChildrenAt
      some point in the future, behavior will be removed and specifying an
      empty updateMask will be an error.For a detailed FieldMask definition,
      see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=filter
  """

  customWriterIdentity = _messages.StringField(1)
  logSink = _messages.MessageField('LogSink', 2)
  sinkName = _messages.StringField(3, required=True)
  uniqueWriterIdentity = _messages.BooleanField(4)
  updateMask = _messages.StringField(5)


class LoggingBillingAccountsSinksUpdateRequest(_messages.Message):
  r"""A LoggingBillingAccountsSinksUpdateRequest object.

  Fields:
    customWriterIdentity: Optional. A service account provided by the caller
      that will be used to write the log entries. Must be of format
      serviceAccount:some@email. This can only be specified if writing to a
      destination outside the sink's project. If not specified, a p4 service
      account will automatically be generated.
    logSink: A LogSink resource to be passed as the request body.
    sinkName: Required. The full resource name of the sink to update,
      including the parent resource and the sink identifier:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
    uniqueWriterIdentity: Optional. See sinks.create for a description of this
      field. When updating a sink, the effect of this field on the value of
      writer_identity in the updated sink depends on both the old and new
      values of this field: If the old and new values of this field are both
      false or both true, then there is no change to the sink's
      writer_identity. If the old value is false and the new value is true,
      then writer_identity is changed to a service agent
      (https://cloud.google.com/iam/docs/service-account-types#service-agents)
      owned by Cloud Logging. It is an error if the old value is true and the
      new value is set to false or defaulted to false.
    updateMask: Optional. Field mask that specifies the fields in sink that
      need an update. A sink field will be overwritten if, and only if, it is
      in the update mask. name and output only fields cannot be updated.An
      empty updateMask is temporarily treated as using the following mask for
      backwards compatibility purposes:destination,filter,includeChildrenAt
      some point in the future, behavior will be removed and specifying an
      empty updateMask will be an error.For a detailed FieldMask definition,
      see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=filter
  """

  customWriterIdentity = _messages.StringField(1)
  logSink = _messages.MessageField('LogSink', 2)
  sinkName = _messages.StringField(3, required=True)
  uniqueWriterIdentity = _messages.BooleanField(4)
  updateMask = _messages.StringField(5)


class LoggingExclusionsCreateRequest(_messages.Message):
  r"""A LoggingExclusionsCreateRequest object.

  Fields:
    logExclusion: A LogExclusion resource to be passed as the request body.
    parent: Required. The parent resource in which to create the exclusion:
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]" For
      examples:"projects/my-logging-project" "organizations/*********"
  """

  logExclusion = _messages.MessageField('LogExclusion', 1)
  parent = _messages.StringField(2, required=True)


class LoggingExclusionsDeleteRequest(_messages.Message):
  r"""A LoggingExclusionsDeleteRequest object.

  Fields:
    name: Required. The resource name of an existing exclusion to delete:
      "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
      "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
      "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]" For
      example:"projects/my-project/exclusions/my-exclusion"
  """

  name = _messages.StringField(1, required=True)


class LoggingExclusionsGetRequest(_messages.Message):
  r"""A LoggingExclusionsGetRequest object.

  Fields:
    name: Required. The resource name of an existing exclusion:
      "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
      "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
      "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]" For
      example:"projects/my-project/exclusions/my-exclusion"
  """

  name = _messages.StringField(1, required=True)


class LoggingExclusionsListRequest(_messages.Message):
  r"""A LoggingExclusionsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource whose exclusions are to be listed.
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingExclusionsPatchRequest(_messages.Message):
  r"""A LoggingExclusionsPatchRequest object.

  Fields:
    logExclusion: A LogExclusion resource to be passed as the request body.
    name: Required. The resource name of the exclusion to update:
      "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
      "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
      "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]" For
      example:"projects/my-project/exclusions/my-exclusion"
    updateMask: Required. A non-empty list of fields to change in the existing
      exclusion. New values for the fields are taken from the corresponding
      fields in the LogExclusion included in this request. Fields not
      mentioned in update_mask are not changed and are ignored in the
      request.For example, to change the filter and description of an
      exclusion, specify an update_mask of "filter,description".
  """

  logExclusion = _messages.MessageField('LogExclusion', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingFoldersExclusionsCreateRequest(_messages.Message):
  r"""A LoggingFoldersExclusionsCreateRequest object.

  Fields:
    logExclusion: A LogExclusion resource to be passed as the request body.
    parent: Required. The parent resource in which to create the exclusion:
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]" For
      examples:"projects/my-logging-project" "organizations/*********"
  """

  logExclusion = _messages.MessageField('LogExclusion', 1)
  parent = _messages.StringField(2, required=True)


class LoggingFoldersExclusionsDeleteRequest(_messages.Message):
  r"""A LoggingFoldersExclusionsDeleteRequest object.

  Fields:
    name: Required. The resource name of an existing exclusion to delete:
      "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
      "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
      "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]" For
      example:"projects/my-project/exclusions/my-exclusion"
  """

  name = _messages.StringField(1, required=True)


class LoggingFoldersExclusionsGetRequest(_messages.Message):
  r"""A LoggingFoldersExclusionsGetRequest object.

  Fields:
    name: Required. The resource name of an existing exclusion:
      "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
      "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
      "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]" For
      example:"projects/my-project/exclusions/my-exclusion"
  """

  name = _messages.StringField(1, required=True)


class LoggingFoldersExclusionsListRequest(_messages.Message):
  r"""A LoggingFoldersExclusionsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource whose exclusions are to be listed.
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingFoldersExclusionsPatchRequest(_messages.Message):
  r"""A LoggingFoldersExclusionsPatchRequest object.

  Fields:
    logExclusion: A LogExclusion resource to be passed as the request body.
    name: Required. The resource name of the exclusion to update:
      "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
      "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
      "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]" For
      example:"projects/my-project/exclusions/my-exclusion"
    updateMask: Required. A non-empty list of fields to change in the existing
      exclusion. New values for the fields are taken from the corresponding
      fields in the LogExclusion included in this request. Fields not
      mentioned in update_mask are not changed and are ignored in the
      request.For example, to change the filter and description of an
      exclusion, specify an update_mask of "filter,description".
  """

  logExclusion = _messages.MessageField('LogExclusion', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingFoldersGetCmekSettingsRequest(_messages.Message):
  r"""A LoggingFoldersGetCmekSettingsRequest object.

  Fields:
    name: Required. The resource for which to retrieve CMEK settings.
      "projects/[PROJECT_ID]/cmekSettings"
      "organizations/[ORGANIZATION_ID]/cmekSettings"
      "billingAccounts/[BILLING_ACCOUNT_ID]/cmekSettings"
      "folders/[FOLDER_ID]/cmekSettings" For
      example:"organizations/12345/cmekSettings"Note: CMEK for the Log Router
      can be configured for Google Cloud projects, folders, organizations and
      billing accounts. Once configured for an organization, it applies to all
      projects and folders in the Google Cloud organization.
  """

  name = _messages.StringField(1, required=True)


class LoggingFoldersGetSettingsRequest(_messages.Message):
  r"""A LoggingFoldersGetSettingsRequest object.

  Fields:
    name: Required. The resource for which to retrieve settings.
      "projects/[PROJECT_ID]/settings"
      "organizations/[ORGANIZATION_ID]/settings"
      "billingAccounts/[BILLING_ACCOUNT_ID]/settings"
      "folders/[FOLDER_ID]/settings" For
      example:"organizations/12345/settings"Note: Settings for the Log Router
      can be get for Google Cloud projects, folders, organizations and billing
      accounts. Currently it can only be configured for organizations. Once
      configured for an organization, it applies to all projects and folders
      in the Google Cloud organization.
  """

  name = _messages.StringField(1, required=True)


class LoggingFoldersLocationsBucketsCreateAsyncRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsCreateAsyncRequest object.

  Fields:
    bucketId: Required. A client-assigned identifier such as "my-bucket".
      Identifiers are limited to 100 characters and can include only letters,
      digits, underscores, hyphens, and periods.
    logBucket: A LogBucket resource to be passed as the request body.
    parent: Required. The resource in which to create the log bucket:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]" For
      example:"projects/my-project/locations/global"
  """

  bucketId = _messages.StringField(1)
  logBucket = _messages.MessageField('LogBucket', 2)
  parent = _messages.StringField(3, required=True)


class LoggingFoldersLocationsBucketsCreateRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsCreateRequest object.

  Fields:
    bucketId: Required. A client-assigned identifier such as "my-bucket".
      Identifiers are limited to 100 characters and can include only letters,
      digits, underscores, hyphens, and periods.
    logBucket: A LogBucket resource to be passed as the request body.
    parent: Required. The resource in which to create the log bucket:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]" For
      example:"projects/my-project/locations/global"
  """

  bucketId = _messages.StringField(1)
  logBucket = _messages.MessageField('LogBucket', 2)
  parent = _messages.StringField(3, required=True)


class LoggingFoldersLocationsBucketsDeleteRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsDeleteRequest object.

  Fields:
    name: Required. The full resource name of the bucket to delete.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
  """

  name = _messages.StringField(1, required=True)


class LoggingFoldersLocationsBucketsGetRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsGetRequest object.

  Fields:
    name: Required. The resource name of the bucket:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
  """

  name = _messages.StringField(1, required=True)


class LoggingFoldersLocationsBucketsLinksCreateRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsLinksCreateRequest object.

  Fields:
    link: A Link resource to be passed as the request body.
    linkId: Required. The ID to use for the link. The link_id can have up to
      100 characters. A valid link_id must only have alphanumeric characters
      and underscores within it.
    parent: Required. The full resource name of the bucket to create a link
      for. "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
      "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET
      _ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buck
      ets/[BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  """

  link = _messages.MessageField('Link', 1)
  linkId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingFoldersLocationsBucketsLinksDeleteRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsLinksDeleteRequest object.

  Fields:
    name: Required. The full resource name of the link to delete. "projects/[P
      ROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
      "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET
      _ID]/links/[LINK_ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[L
      OCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]" "folders/[FOLDER_ID]/lo
      cations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  """

  name = _messages.StringField(1, required=True)


class LoggingFoldersLocationsBucketsLinksGetRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsLinksGetRequest object.

  Fields:
    name: Required. The resource name of the link: "projects/[PROJECT_ID]/loca
      tions/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]" "organizations/
      [ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LIN
      K_ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buc
      kets/[BUCKET_ID]/links/[LINK_ID]" "folders/[FOLDER_ID]/locations/[LOCATI
      ON_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  """

  name = _messages.StringField(1, required=True)


class LoggingFoldersLocationsBucketsLinksListRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsLinksListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response.
    parent: Required. The parent resource whose links are to be listed:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingFoldersLocationsBucketsListRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource whose buckets are to be listed:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]"
      "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]" Note: The locations
      portion of the resource must be specified, but supplying the character -
      in place of LOCATION_ID will return all buckets.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingFoldersLocationsBucketsPatchRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsPatchRequest object.

  Fields:
    logBucket: A LogBucket resource to be passed as the request body.
    name: Required. The full resource name of the bucket to update.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
    updateMask: Required. Field mask that specifies the fields in bucket that
      need an update. A bucket field will be overwritten if, and only if, it
      is in the update mask. name and output only fields cannot be updated.For
      a detailed FieldMask definition, see:
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=retention_days
  """

  logBucket = _messages.MessageField('LogBucket', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingFoldersLocationsBucketsUndeleteRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsUndeleteRequest object.

  Fields:
    name: Required. The full resource name of the bucket to undelete.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
    undeleteBucketRequest: A UndeleteBucketRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteBucketRequest = _messages.MessageField('UndeleteBucketRequest', 2)


class LoggingFoldersLocationsBucketsUpdateAsyncRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsUpdateAsyncRequest object.

  Fields:
    logBucket: A LogBucket resource to be passed as the request body.
    name: Required. The full resource name of the bucket to update.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
    updateMask: Required. Field mask that specifies the fields in bucket that
      need an update. A bucket field will be overwritten if, and only if, it
      is in the update mask. name and output only fields cannot be updated.For
      a detailed FieldMask definition, see:
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=retention_days
  """

  logBucket = _messages.MessageField('LogBucket', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingFoldersLocationsBucketsViewsCreateRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsViewsCreateRequest object.

  Fields:
    logView: A LogView resource to be passed as the request body.
    parent: Required. The bucket in which to create the view
      `"projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"`
      For example:"projects/my-project/locations/global/buckets/my-bucket"
    viewId: Required. A client-assigned identifier such as "my-view".
      Identifiers are limited to 100 characters and can include only letters,
      digits, underscores, hyphens, and periods.
  """

  logView = _messages.MessageField('LogView', 1)
  parent = _messages.StringField(2, required=True)
  viewId = _messages.StringField(3)


class LoggingFoldersLocationsBucketsViewsDeleteRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsViewsDeleteRequest object.

  Fields:
    name: Required. The full resource name of the view to delete: "projects/[P
      ROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]"
      For example:"projects/my-project/locations/global/buckets/my-
      bucket/views/my-view"
  """

  name = _messages.StringField(1, required=True)


class LoggingFoldersLocationsBucketsViewsGetRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsViewsGetRequest object.

  Fields:
    name: Required. The resource name of the policy: "projects/[PROJECT_ID]/lo
      cations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]" For
      example:"projects/my-project/locations/global/buckets/my-
      bucket/views/my-view"
  """

  name = _messages.StringField(1, required=True)


class LoggingFoldersLocationsBucketsViewsListRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsViewsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request.Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The bucket whose views are to be listed:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingFoldersLocationsBucketsViewsLogsListRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsViewsLogsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The resource name to list logs for:
      projects/[PROJECT_ID] organizations/[ORGANIZATION_ID]
      billingAccounts/[BILLING_ACCOUNT_ID] folders/[FOLDER_ID]
    resourceNames: Optional. List of resource names to list logs for: projects
      /[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID
      ] organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKE
      T_ID]/views/[VIEW_ID] billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LO
      CATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID] folders/[FOLDER_ID]/locat
      ions/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]To support legacy
      queries, it could also be: projects/[PROJECT_ID]
      organizations/[ORGANIZATION_ID] billingAccounts/[BILLING_ACCOUNT_ID]
      folders/[FOLDER_ID]The resource name in the parent field is added to
      this list.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  resourceNames = _messages.StringField(4, repeated=True)


class LoggingFoldersLocationsBucketsViewsPatchRequest(_messages.Message):
  r"""A LoggingFoldersLocationsBucketsViewsPatchRequest object.

  Fields:
    logView: A LogView resource to be passed as the request body.
    name: Required. The full resource name of the view to update "projects/[PR
      OJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]"
      For example:"projects/my-project/locations/global/buckets/my-
      bucket/views/my-view"
    updateMask: Optional. Field mask that specifies the fields in view that
      need an update. A field will be overwritten if, and only if, it is in
      the update mask. name and output only fields cannot be updated.For a
      detailed FieldMask definition, see
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=filter
  """

  logView = _messages.MessageField('LogView', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingFoldersLocationsGetRequest(_messages.Message):
  r"""A LoggingFoldersLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class LoggingFoldersLocationsListRequest(_messages.Message):
  r"""A LoggingFoldersLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like "displayName=tokyo", and is
      documented in more detail in AIP-160 (https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the next_page_token field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class LoggingFoldersLocationsOperationsApproveRedactionRequest(_messages.Message):
  r"""A LoggingFoldersLocationsOperationsApproveRedactionRequest object.

  Fields:
    name: Required. Name of the redaction operation.For example:"projects/my-
      project/locations/global/operations/my-operation"
  """

  name = _messages.StringField(1, required=True)


class LoggingFoldersLocationsOperationsCancelRequest(_messages.Message):
  r"""A LoggingFoldersLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class LoggingFoldersLocationsOperationsGetRequest(_messages.Message):
  r"""A LoggingFoldersLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class LoggingFoldersLocationsOperationsListRequest(_messages.Message):
  r"""A LoggingFoldersLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class LoggingFoldersLogsDeleteRequest(_messages.Message):
  r"""A LoggingFoldersLogsDeleteRequest object.

  Fields:
    logName: Required. The resource name of the log to delete:
      projects/[PROJECT_ID]/logs/[LOG_ID]
      organizations/[ORGANIZATION_ID]/logs/[LOG_ID]
      billingAccounts/[BILLING_ACCOUNT_ID]/logs/[LOG_ID]
      folders/[FOLDER_ID]/logs/[LOG_ID][LOG_ID] must be URL-encoded. For
      example, "projects/my-project-id/logs/syslog",
      "organizations/123/logs/cloudaudit.googleapis.com%2Factivity".For more
      information about log names, see LogEntry.
  """

  logName = _messages.StringField(1, required=True)


class LoggingFoldersLogsListRequest(_messages.Message):
  r"""A LoggingFoldersLogsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The resource name to list logs for:
      projects/[PROJECT_ID] organizations/[ORGANIZATION_ID]
      billingAccounts/[BILLING_ACCOUNT_ID] folders/[FOLDER_ID]
    resourceNames: Optional. List of resource names to list logs for: projects
      /[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID
      ] organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKE
      T_ID]/views/[VIEW_ID] billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LO
      CATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID] folders/[FOLDER_ID]/locat
      ions/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]To support legacy
      queries, it could also be: projects/[PROJECT_ID]
      organizations/[ORGANIZATION_ID] billingAccounts/[BILLING_ACCOUNT_ID]
      folders/[FOLDER_ID]The resource name in the parent field is added to
      this list.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  resourceNames = _messages.StringField(4, repeated=True)


class LoggingFoldersSinksCreateRequest(_messages.Message):
  r"""A LoggingFoldersSinksCreateRequest object.

  Fields:
    customWriterIdentity: Optional. A service account provided by the caller
      that will be used to write the log entries. The format must be
      serviceAccount:some@email. This field can only be specified if you are
      routing logs to a destination outside this sink's project. If not
      specified, a Logging service account will automatically be generated.
    logSink: A LogSink resource to be passed as the request body.
    parent: Required. The resource in which to create the sink:
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]" For
      examples:"projects/my-project" "organizations/*********"
    uniqueWriterIdentity: Optional. Determines the kind of IAM identity
      returned as writer_identity in the new sink. If this value is omitted or
      set to false, and if the sink's parent is a project, then the value
      returned as writer_identity is the same group or service account used by
      Cloud Logging before the addition of writer identities to this API. The
      sink's destination must be in the same project as the sink itself.If
      this field is set to true, or if the sink is owned by a non-project
      resource such as an organization, then the value of writer_identity will
      be a service agent (https://cloud.google.com/iam/docs/service-account-
      types#service-agents) used by the sinks with the same parent. For more
      information, see writer_identity in LogSink.
  """

  customWriterIdentity = _messages.StringField(1)
  logSink = _messages.MessageField('LogSink', 2)
  parent = _messages.StringField(3, required=True)
  uniqueWriterIdentity = _messages.BooleanField(4)


class LoggingFoldersSinksDeleteRequest(_messages.Message):
  r"""A LoggingFoldersSinksDeleteRequest object.

  Fields:
    sinkName: Required. The full resource name of the sink to delete,
      including the parent resource and the sink identifier:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
  """

  sinkName = _messages.StringField(1, required=True)


class LoggingFoldersSinksGetRequest(_messages.Message):
  r"""A LoggingFoldersSinksGetRequest object.

  Fields:
    sinkName: Required. The resource name of the sink:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
  """

  sinkName = _messages.StringField(1, required=True)


class LoggingFoldersSinksListRequest(_messages.Message):
  r"""A LoggingFoldersSinksListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource whose sinks are to be listed:
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingFoldersSinksPatchRequest(_messages.Message):
  r"""A LoggingFoldersSinksPatchRequest object.

  Fields:
    customWriterIdentity: Optional. A service account provided by the caller
      that will be used to write the log entries. Must be of format
      serviceAccount:some@email. This can only be specified if writing to a
      destination outside the sink's project. If not specified, a p4 service
      account will automatically be generated.
    logSink: A LogSink resource to be passed as the request body.
    sinkName: Required. The full resource name of the sink to update,
      including the parent resource and the sink identifier:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
    uniqueWriterIdentity: Optional. See sinks.create for a description of this
      field. When updating a sink, the effect of this field on the value of
      writer_identity in the updated sink depends on both the old and new
      values of this field: If the old and new values of this field are both
      false or both true, then there is no change to the sink's
      writer_identity. If the old value is false and the new value is true,
      then writer_identity is changed to a service agent
      (https://cloud.google.com/iam/docs/service-account-types#service-agents)
      owned by Cloud Logging. It is an error if the old value is true and the
      new value is set to false or defaulted to false.
    updateMask: Optional. Field mask that specifies the fields in sink that
      need an update. A sink field will be overwritten if, and only if, it is
      in the update mask. name and output only fields cannot be updated.An
      empty updateMask is temporarily treated as using the following mask for
      backwards compatibility purposes:destination,filter,includeChildrenAt
      some point in the future, behavior will be removed and specifying an
      empty updateMask will be an error.For a detailed FieldMask definition,
      see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=filter
  """

  customWriterIdentity = _messages.StringField(1)
  logSink = _messages.MessageField('LogSink', 2)
  sinkName = _messages.StringField(3, required=True)
  uniqueWriterIdentity = _messages.BooleanField(4)
  updateMask = _messages.StringField(5)


class LoggingFoldersSinksUpdateRequest(_messages.Message):
  r"""A LoggingFoldersSinksUpdateRequest object.

  Fields:
    customWriterIdentity: Optional. A service account provided by the caller
      that will be used to write the log entries. Must be of format
      serviceAccount:some@email. This can only be specified if writing to a
      destination outside the sink's project. If not specified, a p4 service
      account will automatically be generated.
    logSink: A LogSink resource to be passed as the request body.
    sinkName: Required. The full resource name of the sink to update,
      including the parent resource and the sink identifier:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
    uniqueWriterIdentity: Optional. See sinks.create for a description of this
      field. When updating a sink, the effect of this field on the value of
      writer_identity in the updated sink depends on both the old and new
      values of this field: If the old and new values of this field are both
      false or both true, then there is no change to the sink's
      writer_identity. If the old value is false and the new value is true,
      then writer_identity is changed to a service agent
      (https://cloud.google.com/iam/docs/service-account-types#service-agents)
      owned by Cloud Logging. It is an error if the old value is true and the
      new value is set to false or defaulted to false.
    updateMask: Optional. Field mask that specifies the fields in sink that
      need an update. A sink field will be overwritten if, and only if, it is
      in the update mask. name and output only fields cannot be updated.An
      empty updateMask is temporarily treated as using the following mask for
      backwards compatibility purposes:destination,filter,includeChildrenAt
      some point in the future, behavior will be removed and specifying an
      empty updateMask will be an error.For a detailed FieldMask definition,
      see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=filter
  """

  customWriterIdentity = _messages.StringField(1)
  logSink = _messages.MessageField('LogSink', 2)
  sinkName = _messages.StringField(3, required=True)
  uniqueWriterIdentity = _messages.BooleanField(4)
  updateMask = _messages.StringField(5)


class LoggingFoldersUpdateSettingsRequest(_messages.Message):
  r"""A LoggingFoldersUpdateSettingsRequest object.

  Fields:
    name: Required. The resource name for the settings to update.
      "organizations/[ORGANIZATION_ID]/settings" For
      example:"organizations/12345/settings"Note: Settings for the Log Router
      can currently only be configured for Google Cloud organizations. Once
      configured, it applies to all projects and folders in the Google Cloud
      organization.
    settings: A Settings resource to be passed as the request body.
    updateMask: Optional. Field mask identifying which fields from settings
      should be updated. A field will be overwritten if and only if it is in
      the update mask. Output only fields cannot be updated.See FieldMask for
      more information.For example: "updateMask=kmsKeyName"
  """

  name = _messages.StringField(1, required=True)
  settings = _messages.MessageField('Settings', 2)
  updateMask = _messages.StringField(3)


class LoggingGetCmekSettingsRequest(_messages.Message):
  r"""A LoggingGetCmekSettingsRequest object.

  Fields:
    name: Required. The resource for which to retrieve CMEK settings.
      "projects/[PROJECT_ID]/cmekSettings"
      "organizations/[ORGANIZATION_ID]/cmekSettings"
      "billingAccounts/[BILLING_ACCOUNT_ID]/cmekSettings"
      "folders/[FOLDER_ID]/cmekSettings" For
      example:"organizations/12345/cmekSettings"Note: CMEK for the Log Router
      can be configured for Google Cloud projects, folders, organizations and
      billing accounts. Once configured for an organization, it applies to all
      projects and folders in the Google Cloud organization.
  """

  name = _messages.StringField(1, required=True)


class LoggingGetSettingsRequest(_messages.Message):
  r"""A LoggingGetSettingsRequest object.

  Fields:
    name: Required. The resource for which to retrieve settings.
      "projects/[PROJECT_ID]/settings"
      "organizations/[ORGANIZATION_ID]/settings"
      "billingAccounts/[BILLING_ACCOUNT_ID]/settings"
      "folders/[FOLDER_ID]/settings" For
      example:"organizations/12345/settings"Note: Settings for the Log Router
      can be get for Google Cloud projects, folders, organizations and billing
      accounts. Currently it can only be configured for organizations. Once
      configured for an organization, it applies to all projects and folders
      in the Google Cloud organization.
  """

  name = _messages.StringField(1, required=True)


class LoggingLocationsBucketsCreateAsyncRequest(_messages.Message):
  r"""A LoggingLocationsBucketsCreateAsyncRequest object.

  Fields:
    bucketId: Required. A client-assigned identifier such as "my-bucket".
      Identifiers are limited to 100 characters and can include only letters,
      digits, underscores, hyphens, and periods.
    logBucket: A LogBucket resource to be passed as the request body.
    parent: Required. The resource in which to create the log bucket:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]" For
      example:"projects/my-project/locations/global"
  """

  bucketId = _messages.StringField(1)
  logBucket = _messages.MessageField('LogBucket', 2)
  parent = _messages.StringField(3, required=True)


class LoggingLocationsBucketsCreateRequest(_messages.Message):
  r"""A LoggingLocationsBucketsCreateRequest object.

  Fields:
    bucketId: Required. A client-assigned identifier such as "my-bucket".
      Identifiers are limited to 100 characters and can include only letters,
      digits, underscores, hyphens, and periods.
    logBucket: A LogBucket resource to be passed as the request body.
    parent: Required. The resource in which to create the log bucket:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]" For
      example:"projects/my-project/locations/global"
  """

  bucketId = _messages.StringField(1)
  logBucket = _messages.MessageField('LogBucket', 2)
  parent = _messages.StringField(3, required=True)


class LoggingLocationsBucketsDeleteRequest(_messages.Message):
  r"""A LoggingLocationsBucketsDeleteRequest object.

  Fields:
    name: Required. The full resource name of the bucket to delete.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
  """

  name = _messages.StringField(1, required=True)


class LoggingLocationsBucketsGetRequest(_messages.Message):
  r"""A LoggingLocationsBucketsGetRequest object.

  Fields:
    name: Required. The resource name of the bucket:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
  """

  name = _messages.StringField(1, required=True)


class LoggingLocationsBucketsLinksCreateRequest(_messages.Message):
  r"""A LoggingLocationsBucketsLinksCreateRequest object.

  Fields:
    link: A Link resource to be passed as the request body.
    linkId: Required. The ID to use for the link. The link_id can have up to
      100 characters. A valid link_id must only have alphanumeric characters
      and underscores within it.
    parent: Required. The full resource name of the bucket to create a link
      for. "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
      "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET
      _ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buck
      ets/[BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  """

  link = _messages.MessageField('Link', 1)
  linkId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingLocationsBucketsLinksDeleteRequest(_messages.Message):
  r"""A LoggingLocationsBucketsLinksDeleteRequest object.

  Fields:
    name: Required. The full resource name of the link to delete. "projects/[P
      ROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
      "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET
      _ID]/links/[LINK_ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[L
      OCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]" "folders/[FOLDER_ID]/lo
      cations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  """

  name = _messages.StringField(1, required=True)


class LoggingLocationsBucketsLinksGetRequest(_messages.Message):
  r"""A LoggingLocationsBucketsLinksGetRequest object.

  Fields:
    name: Required. The resource name of the link: "projects/[PROJECT_ID]/loca
      tions/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]" "organizations/
      [ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LIN
      K_ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buc
      kets/[BUCKET_ID]/links/[LINK_ID]" "folders/[FOLDER_ID]/locations/[LOCATI
      ON_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  """

  name = _messages.StringField(1, required=True)


class LoggingLocationsBucketsLinksListRequest(_messages.Message):
  r"""A LoggingLocationsBucketsLinksListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response.
    parent: Required. The parent resource whose links are to be listed:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingLocationsBucketsListRequest(_messages.Message):
  r"""A LoggingLocationsBucketsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource whose buckets are to be listed:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]"
      "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]" Note: The locations
      portion of the resource must be specified, but supplying the character -
      in place of LOCATION_ID will return all buckets.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingLocationsBucketsPatchRequest(_messages.Message):
  r"""A LoggingLocationsBucketsPatchRequest object.

  Fields:
    logBucket: A LogBucket resource to be passed as the request body.
    name: Required. The full resource name of the bucket to update.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
    updateMask: Required. Field mask that specifies the fields in bucket that
      need an update. A bucket field will be overwritten if, and only if, it
      is in the update mask. name and output only fields cannot be updated.For
      a detailed FieldMask definition, see:
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=retention_days
  """

  logBucket = _messages.MessageField('LogBucket', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingLocationsBucketsUndeleteRequest(_messages.Message):
  r"""A LoggingLocationsBucketsUndeleteRequest object.

  Fields:
    name: Required. The full resource name of the bucket to undelete.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
    undeleteBucketRequest: A UndeleteBucketRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteBucketRequest = _messages.MessageField('UndeleteBucketRequest', 2)


class LoggingLocationsBucketsUpdateAsyncRequest(_messages.Message):
  r"""A LoggingLocationsBucketsUpdateAsyncRequest object.

  Fields:
    logBucket: A LogBucket resource to be passed as the request body.
    name: Required. The full resource name of the bucket to update.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
    updateMask: Required. Field mask that specifies the fields in bucket that
      need an update. A bucket field will be overwritten if, and only if, it
      is in the update mask. name and output only fields cannot be updated.For
      a detailed FieldMask definition, see:
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=retention_days
  """

  logBucket = _messages.MessageField('LogBucket', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingLocationsBucketsViewsCreateRequest(_messages.Message):
  r"""A LoggingLocationsBucketsViewsCreateRequest object.

  Fields:
    logView: A LogView resource to be passed as the request body.
    parent: Required. The bucket in which to create the view
      `"projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"`
      For example:"projects/my-project/locations/global/buckets/my-bucket"
    viewId: Required. A client-assigned identifier such as "my-view".
      Identifiers are limited to 100 characters and can include only letters,
      digits, underscores, hyphens, and periods.
  """

  logView = _messages.MessageField('LogView', 1)
  parent = _messages.StringField(2, required=True)
  viewId = _messages.StringField(3)


class LoggingLocationsBucketsViewsDeleteRequest(_messages.Message):
  r"""A LoggingLocationsBucketsViewsDeleteRequest object.

  Fields:
    name: Required. The full resource name of the view to delete: "projects/[P
      ROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]"
      For example:"projects/my-project/locations/global/buckets/my-
      bucket/views/my-view"
  """

  name = _messages.StringField(1, required=True)


class LoggingLocationsBucketsViewsGetRequest(_messages.Message):
  r"""A LoggingLocationsBucketsViewsGetRequest object.

  Fields:
    name: Required. The resource name of the policy: "projects/[PROJECT_ID]/lo
      cations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]" For
      example:"projects/my-project/locations/global/buckets/my-
      bucket/views/my-view"
  """

  name = _messages.StringField(1, required=True)


class LoggingLocationsBucketsViewsListRequest(_messages.Message):
  r"""A LoggingLocationsBucketsViewsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request.Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The bucket whose views are to be listed:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingLocationsBucketsViewsPatchRequest(_messages.Message):
  r"""A LoggingLocationsBucketsViewsPatchRequest object.

  Fields:
    logView: A LogView resource to be passed as the request body.
    name: Required. The full resource name of the view to update "projects/[PR
      OJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]"
      For example:"projects/my-project/locations/global/buckets/my-
      bucket/views/my-view"
    updateMask: Optional. Field mask that specifies the fields in view that
      need an update. A field will be overwritten if, and only if, it is in
      the update mask. name and output only fields cannot be updated.For a
      detailed FieldMask definition, see
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=filter
  """

  logView = _messages.MessageField('LogView', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingLocationsGetRequest(_messages.Message):
  r"""A LoggingLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class LoggingLocationsListRequest(_messages.Message):
  r"""A LoggingLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like "displayName=tokyo", and is
      documented in more detail in AIP-160 (https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the next_page_token field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class LoggingLocationsOperationsApproveRedactionRequest(_messages.Message):
  r"""A LoggingLocationsOperationsApproveRedactionRequest object.

  Fields:
    name: Required. Name of the redaction operation.For example:"projects/my-
      project/locations/global/operations/my-operation"
  """

  name = _messages.StringField(1, required=True)


class LoggingLocationsOperationsCancelRequest(_messages.Message):
  r"""A LoggingLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class LoggingLocationsOperationsGetRequest(_messages.Message):
  r"""A LoggingLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class LoggingLocationsOperationsListRequest(_messages.Message):
  r"""A LoggingLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class LoggingLogsDeleteRequest(_messages.Message):
  r"""A LoggingLogsDeleteRequest object.

  Fields:
    logName: Required. The resource name of the log to delete:
      projects/[PROJECT_ID]/logs/[LOG_ID]
      organizations/[ORGANIZATION_ID]/logs/[LOG_ID]
      billingAccounts/[BILLING_ACCOUNT_ID]/logs/[LOG_ID]
      folders/[FOLDER_ID]/logs/[LOG_ID][LOG_ID] must be URL-encoded. For
      example, "projects/my-project-id/logs/syslog",
      "organizations/123/logs/cloudaudit.googleapis.com%2Factivity".For more
      information about log names, see LogEntry.
  """

  logName = _messages.StringField(1, required=True)


class LoggingLogsListRequest(_messages.Message):
  r"""A LoggingLogsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The resource name to list logs for:
      projects/[PROJECT_ID] organizations/[ORGANIZATION_ID]
      billingAccounts/[BILLING_ACCOUNT_ID] folders/[FOLDER_ID]
    resourceNames: Optional. List of resource names to list logs for: projects
      /[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID
      ] organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKE
      T_ID]/views/[VIEW_ID] billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LO
      CATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID] folders/[FOLDER_ID]/locat
      ions/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]To support legacy
      queries, it could also be: projects/[PROJECT_ID]
      organizations/[ORGANIZATION_ID] billingAccounts/[BILLING_ACCOUNT_ID]
      folders/[FOLDER_ID]The resource name in the parent field is added to
      this list.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  resourceNames = _messages.StringField(4, repeated=True)


class LoggingMonitoredResourceDescriptorsListRequest(_messages.Message):
  r"""A LoggingMonitoredResourceDescriptorsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)


class LoggingOrganizationsExclusionsCreateRequest(_messages.Message):
  r"""A LoggingOrganizationsExclusionsCreateRequest object.

  Fields:
    logExclusion: A LogExclusion resource to be passed as the request body.
    parent: Required. The parent resource in which to create the exclusion:
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]" For
      examples:"projects/my-logging-project" "organizations/*********"
  """

  logExclusion = _messages.MessageField('LogExclusion', 1)
  parent = _messages.StringField(2, required=True)


class LoggingOrganizationsExclusionsDeleteRequest(_messages.Message):
  r"""A LoggingOrganizationsExclusionsDeleteRequest object.

  Fields:
    name: Required. The resource name of an existing exclusion to delete:
      "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
      "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
      "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]" For
      example:"projects/my-project/exclusions/my-exclusion"
  """

  name = _messages.StringField(1, required=True)


class LoggingOrganizationsExclusionsGetRequest(_messages.Message):
  r"""A LoggingOrganizationsExclusionsGetRequest object.

  Fields:
    name: Required. The resource name of an existing exclusion:
      "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
      "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
      "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]" For
      example:"projects/my-project/exclusions/my-exclusion"
  """

  name = _messages.StringField(1, required=True)


class LoggingOrganizationsExclusionsListRequest(_messages.Message):
  r"""A LoggingOrganizationsExclusionsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource whose exclusions are to be listed.
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingOrganizationsExclusionsPatchRequest(_messages.Message):
  r"""A LoggingOrganizationsExclusionsPatchRequest object.

  Fields:
    logExclusion: A LogExclusion resource to be passed as the request body.
    name: Required. The resource name of the exclusion to update:
      "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
      "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
      "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]" For
      example:"projects/my-project/exclusions/my-exclusion"
    updateMask: Required. A non-empty list of fields to change in the existing
      exclusion. New values for the fields are taken from the corresponding
      fields in the LogExclusion included in this request. Fields not
      mentioned in update_mask are not changed and are ignored in the
      request.For example, to change the filter and description of an
      exclusion, specify an update_mask of "filter,description".
  """

  logExclusion = _messages.MessageField('LogExclusion', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingOrganizationsGetCmekSettingsRequest(_messages.Message):
  r"""A LoggingOrganizationsGetCmekSettingsRequest object.

  Fields:
    name: Required. The resource for which to retrieve CMEK settings.
      "projects/[PROJECT_ID]/cmekSettings"
      "organizations/[ORGANIZATION_ID]/cmekSettings"
      "billingAccounts/[BILLING_ACCOUNT_ID]/cmekSettings"
      "folders/[FOLDER_ID]/cmekSettings" For
      example:"organizations/12345/cmekSettings"Note: CMEK for the Log Router
      can be configured for Google Cloud projects, folders, organizations and
      billing accounts. Once configured for an organization, it applies to all
      projects and folders in the Google Cloud organization.
  """

  name = _messages.StringField(1, required=True)


class LoggingOrganizationsGetSettingsRequest(_messages.Message):
  r"""A LoggingOrganizationsGetSettingsRequest object.

  Fields:
    name: Required. The resource for which to retrieve settings.
      "projects/[PROJECT_ID]/settings"
      "organizations/[ORGANIZATION_ID]/settings"
      "billingAccounts/[BILLING_ACCOUNT_ID]/settings"
      "folders/[FOLDER_ID]/settings" For
      example:"organizations/12345/settings"Note: Settings for the Log Router
      can be get for Google Cloud projects, folders, organizations and billing
      accounts. Currently it can only be configured for organizations. Once
      configured for an organization, it applies to all projects and folders
      in the Google Cloud organization.
  """

  name = _messages.StringField(1, required=True)


class LoggingOrganizationsLocationsBucketsCreateAsyncRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsCreateAsyncRequest object.

  Fields:
    bucketId: Required. A client-assigned identifier such as "my-bucket".
      Identifiers are limited to 100 characters and can include only letters,
      digits, underscores, hyphens, and periods.
    logBucket: A LogBucket resource to be passed as the request body.
    parent: Required. The resource in which to create the log bucket:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]" For
      example:"projects/my-project/locations/global"
  """

  bucketId = _messages.StringField(1)
  logBucket = _messages.MessageField('LogBucket', 2)
  parent = _messages.StringField(3, required=True)


class LoggingOrganizationsLocationsBucketsCreateRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsCreateRequest object.

  Fields:
    bucketId: Required. A client-assigned identifier such as "my-bucket".
      Identifiers are limited to 100 characters and can include only letters,
      digits, underscores, hyphens, and periods.
    logBucket: A LogBucket resource to be passed as the request body.
    parent: Required. The resource in which to create the log bucket:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]" For
      example:"projects/my-project/locations/global"
  """

  bucketId = _messages.StringField(1)
  logBucket = _messages.MessageField('LogBucket', 2)
  parent = _messages.StringField(3, required=True)


class LoggingOrganizationsLocationsBucketsDeleteRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsDeleteRequest object.

  Fields:
    name: Required. The full resource name of the bucket to delete.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
  """

  name = _messages.StringField(1, required=True)


class LoggingOrganizationsLocationsBucketsGetRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsGetRequest object.

  Fields:
    name: Required. The resource name of the bucket:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
  """

  name = _messages.StringField(1, required=True)


class LoggingOrganizationsLocationsBucketsLinksCreateRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsLinksCreateRequest object.

  Fields:
    link: A Link resource to be passed as the request body.
    linkId: Required. The ID to use for the link. The link_id can have up to
      100 characters. A valid link_id must only have alphanumeric characters
      and underscores within it.
    parent: Required. The full resource name of the bucket to create a link
      for. "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
      "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET
      _ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buck
      ets/[BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  """

  link = _messages.MessageField('Link', 1)
  linkId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingOrganizationsLocationsBucketsLinksDeleteRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsLinksDeleteRequest object.

  Fields:
    name: Required. The full resource name of the link to delete. "projects/[P
      ROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
      "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET
      _ID]/links/[LINK_ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[L
      OCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]" "folders/[FOLDER_ID]/lo
      cations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  """

  name = _messages.StringField(1, required=True)


class LoggingOrganizationsLocationsBucketsLinksGetRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsLinksGetRequest object.

  Fields:
    name: Required. The resource name of the link: "projects/[PROJECT_ID]/loca
      tions/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]" "organizations/
      [ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LIN
      K_ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buc
      kets/[BUCKET_ID]/links/[LINK_ID]" "folders/[FOLDER_ID]/locations/[LOCATI
      ON_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  """

  name = _messages.StringField(1, required=True)


class LoggingOrganizationsLocationsBucketsLinksListRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsLinksListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response.
    parent: Required. The parent resource whose links are to be listed:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingOrganizationsLocationsBucketsListRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource whose buckets are to be listed:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]"
      "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]" Note: The locations
      portion of the resource must be specified, but supplying the character -
      in place of LOCATION_ID will return all buckets.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingOrganizationsLocationsBucketsPatchRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsPatchRequest object.

  Fields:
    logBucket: A LogBucket resource to be passed as the request body.
    name: Required. The full resource name of the bucket to update.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
    updateMask: Required. Field mask that specifies the fields in bucket that
      need an update. A bucket field will be overwritten if, and only if, it
      is in the update mask. name and output only fields cannot be updated.For
      a detailed FieldMask definition, see:
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=retention_days
  """

  logBucket = _messages.MessageField('LogBucket', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingOrganizationsLocationsBucketsUndeleteRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsUndeleteRequest object.

  Fields:
    name: Required. The full resource name of the bucket to undelete.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
    undeleteBucketRequest: A UndeleteBucketRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteBucketRequest = _messages.MessageField('UndeleteBucketRequest', 2)


class LoggingOrganizationsLocationsBucketsUpdateAsyncRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsUpdateAsyncRequest object.

  Fields:
    logBucket: A LogBucket resource to be passed as the request body.
    name: Required. The full resource name of the bucket to update.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
    updateMask: Required. Field mask that specifies the fields in bucket that
      need an update. A bucket field will be overwritten if, and only if, it
      is in the update mask. name and output only fields cannot be updated.For
      a detailed FieldMask definition, see:
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=retention_days
  """

  logBucket = _messages.MessageField('LogBucket', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingOrganizationsLocationsBucketsViewsCreateRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsViewsCreateRequest object.

  Fields:
    logView: A LogView resource to be passed as the request body.
    parent: Required. The bucket in which to create the view
      `"projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"`
      For example:"projects/my-project/locations/global/buckets/my-bucket"
    viewId: Required. A client-assigned identifier such as "my-view".
      Identifiers are limited to 100 characters and can include only letters,
      digits, underscores, hyphens, and periods.
  """

  logView = _messages.MessageField('LogView', 1)
  parent = _messages.StringField(2, required=True)
  viewId = _messages.StringField(3)


class LoggingOrganizationsLocationsBucketsViewsDeleteRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsViewsDeleteRequest object.

  Fields:
    name: Required. The full resource name of the view to delete: "projects/[P
      ROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]"
      For example:"projects/my-project/locations/global/buckets/my-
      bucket/views/my-view"
  """

  name = _messages.StringField(1, required=True)


class LoggingOrganizationsLocationsBucketsViewsGetRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsViewsGetRequest object.

  Fields:
    name: Required. The resource name of the policy: "projects/[PROJECT_ID]/lo
      cations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]" For
      example:"projects/my-project/locations/global/buckets/my-
      bucket/views/my-view"
  """

  name = _messages.StringField(1, required=True)


class LoggingOrganizationsLocationsBucketsViewsListRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsViewsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request.Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The bucket whose views are to be listed:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingOrganizationsLocationsBucketsViewsLogsListRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsViewsLogsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The resource name to list logs for:
      projects/[PROJECT_ID] organizations/[ORGANIZATION_ID]
      billingAccounts/[BILLING_ACCOUNT_ID] folders/[FOLDER_ID]
    resourceNames: Optional. List of resource names to list logs for: projects
      /[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID
      ] organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKE
      T_ID]/views/[VIEW_ID] billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LO
      CATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID] folders/[FOLDER_ID]/locat
      ions/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]To support legacy
      queries, it could also be: projects/[PROJECT_ID]
      organizations/[ORGANIZATION_ID] billingAccounts/[BILLING_ACCOUNT_ID]
      folders/[FOLDER_ID]The resource name in the parent field is added to
      this list.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  resourceNames = _messages.StringField(4, repeated=True)


class LoggingOrganizationsLocationsBucketsViewsPatchRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsBucketsViewsPatchRequest object.

  Fields:
    logView: A LogView resource to be passed as the request body.
    name: Required. The full resource name of the view to update "projects/[PR
      OJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]"
      For example:"projects/my-project/locations/global/buckets/my-
      bucket/views/my-view"
    updateMask: Optional. Field mask that specifies the fields in view that
      need an update. A field will be overwritten if, and only if, it is in
      the update mask. name and output only fields cannot be updated.For a
      detailed FieldMask definition, see
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=filter
  """

  logView = _messages.MessageField('LogView', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingOrganizationsLocationsGetRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class LoggingOrganizationsLocationsListRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like "displayName=tokyo", and is
      documented in more detail in AIP-160 (https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the next_page_token field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class LoggingOrganizationsLocationsOperationsApproveRedactionRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsOperationsApproveRedactionRequest object.

  Fields:
    name: Required. Name of the redaction operation.For example:"projects/my-
      project/locations/global/operations/my-operation"
  """

  name = _messages.StringField(1, required=True)


class LoggingOrganizationsLocationsOperationsCancelRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class LoggingOrganizationsLocationsOperationsGetRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class LoggingOrganizationsLocationsOperationsListRequest(_messages.Message):
  r"""A LoggingOrganizationsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class LoggingOrganizationsLogsDeleteRequest(_messages.Message):
  r"""A LoggingOrganizationsLogsDeleteRequest object.

  Fields:
    logName: Required. The resource name of the log to delete:
      projects/[PROJECT_ID]/logs/[LOG_ID]
      organizations/[ORGANIZATION_ID]/logs/[LOG_ID]
      billingAccounts/[BILLING_ACCOUNT_ID]/logs/[LOG_ID]
      folders/[FOLDER_ID]/logs/[LOG_ID][LOG_ID] must be URL-encoded. For
      example, "projects/my-project-id/logs/syslog",
      "organizations/123/logs/cloudaudit.googleapis.com%2Factivity".For more
      information about log names, see LogEntry.
  """

  logName = _messages.StringField(1, required=True)


class LoggingOrganizationsLogsListRequest(_messages.Message):
  r"""A LoggingOrganizationsLogsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The resource name to list logs for:
      projects/[PROJECT_ID] organizations/[ORGANIZATION_ID]
      billingAccounts/[BILLING_ACCOUNT_ID] folders/[FOLDER_ID]
    resourceNames: Optional. List of resource names to list logs for: projects
      /[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID
      ] organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKE
      T_ID]/views/[VIEW_ID] billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LO
      CATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID] folders/[FOLDER_ID]/locat
      ions/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]To support legacy
      queries, it could also be: projects/[PROJECT_ID]
      organizations/[ORGANIZATION_ID] billingAccounts/[BILLING_ACCOUNT_ID]
      folders/[FOLDER_ID]The resource name in the parent field is added to
      this list.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  resourceNames = _messages.StringField(4, repeated=True)


class LoggingOrganizationsSinksCreateRequest(_messages.Message):
  r"""A LoggingOrganizationsSinksCreateRequest object.

  Fields:
    customWriterIdentity: Optional. A service account provided by the caller
      that will be used to write the log entries. The format must be
      serviceAccount:some@email. This field can only be specified if you are
      routing logs to a destination outside this sink's project. If not
      specified, a Logging service account will automatically be generated.
    logSink: A LogSink resource to be passed as the request body.
    parent: Required. The resource in which to create the sink:
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]" For
      examples:"projects/my-project" "organizations/*********"
    uniqueWriterIdentity: Optional. Determines the kind of IAM identity
      returned as writer_identity in the new sink. If this value is omitted or
      set to false, and if the sink's parent is a project, then the value
      returned as writer_identity is the same group or service account used by
      Cloud Logging before the addition of writer identities to this API. The
      sink's destination must be in the same project as the sink itself.If
      this field is set to true, or if the sink is owned by a non-project
      resource such as an organization, then the value of writer_identity will
      be a service agent (https://cloud.google.com/iam/docs/service-account-
      types#service-agents) used by the sinks with the same parent. For more
      information, see writer_identity in LogSink.
  """

  customWriterIdentity = _messages.StringField(1)
  logSink = _messages.MessageField('LogSink', 2)
  parent = _messages.StringField(3, required=True)
  uniqueWriterIdentity = _messages.BooleanField(4)


class LoggingOrganizationsSinksDeleteRequest(_messages.Message):
  r"""A LoggingOrganizationsSinksDeleteRequest object.

  Fields:
    sinkName: Required. The full resource name of the sink to delete,
      including the parent resource and the sink identifier:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
  """

  sinkName = _messages.StringField(1, required=True)


class LoggingOrganizationsSinksGetRequest(_messages.Message):
  r"""A LoggingOrganizationsSinksGetRequest object.

  Fields:
    sinkName: Required. The resource name of the sink:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
  """

  sinkName = _messages.StringField(1, required=True)


class LoggingOrganizationsSinksListRequest(_messages.Message):
  r"""A LoggingOrganizationsSinksListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource whose sinks are to be listed:
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingOrganizationsSinksPatchRequest(_messages.Message):
  r"""A LoggingOrganizationsSinksPatchRequest object.

  Fields:
    customWriterIdentity: Optional. A service account provided by the caller
      that will be used to write the log entries. Must be of format
      serviceAccount:some@email. This can only be specified if writing to a
      destination outside the sink's project. If not specified, a p4 service
      account will automatically be generated.
    logSink: A LogSink resource to be passed as the request body.
    sinkName: Required. The full resource name of the sink to update,
      including the parent resource and the sink identifier:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
    uniqueWriterIdentity: Optional. See sinks.create for a description of this
      field. When updating a sink, the effect of this field on the value of
      writer_identity in the updated sink depends on both the old and new
      values of this field: If the old and new values of this field are both
      false or both true, then there is no change to the sink's
      writer_identity. If the old value is false and the new value is true,
      then writer_identity is changed to a service agent
      (https://cloud.google.com/iam/docs/service-account-types#service-agents)
      owned by Cloud Logging. It is an error if the old value is true and the
      new value is set to false or defaulted to false.
    updateMask: Optional. Field mask that specifies the fields in sink that
      need an update. A sink field will be overwritten if, and only if, it is
      in the update mask. name and output only fields cannot be updated.An
      empty updateMask is temporarily treated as using the following mask for
      backwards compatibility purposes:destination,filter,includeChildrenAt
      some point in the future, behavior will be removed and specifying an
      empty updateMask will be an error.For a detailed FieldMask definition,
      see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=filter
  """

  customWriterIdentity = _messages.StringField(1)
  logSink = _messages.MessageField('LogSink', 2)
  sinkName = _messages.StringField(3, required=True)
  uniqueWriterIdentity = _messages.BooleanField(4)
  updateMask = _messages.StringField(5)


class LoggingOrganizationsSinksUpdateRequest(_messages.Message):
  r"""A LoggingOrganizationsSinksUpdateRequest object.

  Fields:
    customWriterIdentity: Optional. A service account provided by the caller
      that will be used to write the log entries. Must be of format
      serviceAccount:some@email. This can only be specified if writing to a
      destination outside the sink's project. If not specified, a p4 service
      account will automatically be generated.
    logSink: A LogSink resource to be passed as the request body.
    sinkName: Required. The full resource name of the sink to update,
      including the parent resource and the sink identifier:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
    uniqueWriterIdentity: Optional. See sinks.create for a description of this
      field. When updating a sink, the effect of this field on the value of
      writer_identity in the updated sink depends on both the old and new
      values of this field: If the old and new values of this field are both
      false or both true, then there is no change to the sink's
      writer_identity. If the old value is false and the new value is true,
      then writer_identity is changed to a service agent
      (https://cloud.google.com/iam/docs/service-account-types#service-agents)
      owned by Cloud Logging. It is an error if the old value is true and the
      new value is set to false or defaulted to false.
    updateMask: Optional. Field mask that specifies the fields in sink that
      need an update. A sink field will be overwritten if, and only if, it is
      in the update mask. name and output only fields cannot be updated.An
      empty updateMask is temporarily treated as using the following mask for
      backwards compatibility purposes:destination,filter,includeChildrenAt
      some point in the future, behavior will be removed and specifying an
      empty updateMask will be an error.For a detailed FieldMask definition,
      see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=filter
  """

  customWriterIdentity = _messages.StringField(1)
  logSink = _messages.MessageField('LogSink', 2)
  sinkName = _messages.StringField(3, required=True)
  uniqueWriterIdentity = _messages.BooleanField(4)
  updateMask = _messages.StringField(5)


class LoggingOrganizationsUpdateCmekSettingsRequest(_messages.Message):
  r"""A LoggingOrganizationsUpdateCmekSettingsRequest object.

  Fields:
    cmekSettings: A CmekSettings resource to be passed as the request body.
    name: Required. The resource name for the CMEK settings to update.
      "projects/[PROJECT_ID]/cmekSettings"
      "organizations/[ORGANIZATION_ID]/cmekSettings"
      "billingAccounts/[BILLING_ACCOUNT_ID]/cmekSettings"
      "folders/[FOLDER_ID]/cmekSettings" For
      example:"organizations/12345/cmekSettings"Note: CMEK for the Log Router
      can currently only be configured for Google Cloud organizations. Once
      configured, it applies to all projects and folders in the Google Cloud
      organization.
    updateMask: Optional. Field mask identifying which fields from
      cmek_settings should be updated. A field will be overwritten if and only
      if it is in the update mask. Output only fields cannot be updated.See
      FieldMask for more information.For example: "updateMask=kmsKeyName"
  """

  cmekSettings = _messages.MessageField('CmekSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingOrganizationsUpdateSettingsRequest(_messages.Message):
  r"""A LoggingOrganizationsUpdateSettingsRequest object.

  Fields:
    name: Required. The resource name for the settings to update.
      "organizations/[ORGANIZATION_ID]/settings" For
      example:"organizations/12345/settings"Note: Settings for the Log Router
      can currently only be configured for Google Cloud organizations. Once
      configured, it applies to all projects and folders in the Google Cloud
      organization.
    settings: A Settings resource to be passed as the request body.
    updateMask: Optional. Field mask identifying which fields from settings
      should be updated. A field will be overwritten if and only if it is in
      the update mask. Output only fields cannot be updated.See FieldMask for
      more information.For example: "updateMask=kmsKeyName"
  """

  name = _messages.StringField(1, required=True)
  settings = _messages.MessageField('Settings', 2)
  updateMask = _messages.StringField(3)


class LoggingProjectsExclusionsCreateRequest(_messages.Message):
  r"""A LoggingProjectsExclusionsCreateRequest object.

  Fields:
    logExclusion: A LogExclusion resource to be passed as the request body.
    parent: Required. The parent resource in which to create the exclusion:
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]" For
      examples:"projects/my-logging-project" "organizations/*********"
  """

  logExclusion = _messages.MessageField('LogExclusion', 1)
  parent = _messages.StringField(2, required=True)


class LoggingProjectsExclusionsDeleteRequest(_messages.Message):
  r"""A LoggingProjectsExclusionsDeleteRequest object.

  Fields:
    name: Required. The resource name of an existing exclusion to delete:
      "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
      "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
      "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]" For
      example:"projects/my-project/exclusions/my-exclusion"
  """

  name = _messages.StringField(1, required=True)


class LoggingProjectsExclusionsGetRequest(_messages.Message):
  r"""A LoggingProjectsExclusionsGetRequest object.

  Fields:
    name: Required. The resource name of an existing exclusion:
      "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
      "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
      "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]" For
      example:"projects/my-project/exclusions/my-exclusion"
  """

  name = _messages.StringField(1, required=True)


class LoggingProjectsExclusionsListRequest(_messages.Message):
  r"""A LoggingProjectsExclusionsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource whose exclusions are to be listed.
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingProjectsExclusionsPatchRequest(_messages.Message):
  r"""A LoggingProjectsExclusionsPatchRequest object.

  Fields:
    logExclusion: A LogExclusion resource to be passed as the request body.
    name: Required. The resource name of the exclusion to update:
      "projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
      "organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
      "folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]" For
      example:"projects/my-project/exclusions/my-exclusion"
    updateMask: Required. A non-empty list of fields to change in the existing
      exclusion. New values for the fields are taken from the corresponding
      fields in the LogExclusion included in this request. Fields not
      mentioned in update_mask are not changed and are ignored in the
      request.For example, to change the filter and description of an
      exclusion, specify an update_mask of "filter,description".
  """

  logExclusion = _messages.MessageField('LogExclusion', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingProjectsGetCmekSettingsRequest(_messages.Message):
  r"""A LoggingProjectsGetCmekSettingsRequest object.

  Fields:
    name: Required. The resource for which to retrieve CMEK settings.
      "projects/[PROJECT_ID]/cmekSettings"
      "organizations/[ORGANIZATION_ID]/cmekSettings"
      "billingAccounts/[BILLING_ACCOUNT_ID]/cmekSettings"
      "folders/[FOLDER_ID]/cmekSettings" For
      example:"organizations/12345/cmekSettings"Note: CMEK for the Log Router
      can be configured for Google Cloud projects, folders, organizations and
      billing accounts. Once configured for an organization, it applies to all
      projects and folders in the Google Cloud organization.
  """

  name = _messages.StringField(1, required=True)


class LoggingProjectsGetSettingsRequest(_messages.Message):
  r"""A LoggingProjectsGetSettingsRequest object.

  Fields:
    name: Required. The resource for which to retrieve settings.
      "projects/[PROJECT_ID]/settings"
      "organizations/[ORGANIZATION_ID]/settings"
      "billingAccounts/[BILLING_ACCOUNT_ID]/settings"
      "folders/[FOLDER_ID]/settings" For
      example:"organizations/12345/settings"Note: Settings for the Log Router
      can be get for Google Cloud projects, folders, organizations and billing
      accounts. Currently it can only be configured for organizations. Once
      configured for an organization, it applies to all projects and folders
      in the Google Cloud organization.
  """

  name = _messages.StringField(1, required=True)


class LoggingProjectsLocationsBucketsCreateAsyncRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsCreateAsyncRequest object.

  Fields:
    bucketId: Required. A client-assigned identifier such as "my-bucket".
      Identifiers are limited to 100 characters and can include only letters,
      digits, underscores, hyphens, and periods.
    logBucket: A LogBucket resource to be passed as the request body.
    parent: Required. The resource in which to create the log bucket:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]" For
      example:"projects/my-project/locations/global"
  """

  bucketId = _messages.StringField(1)
  logBucket = _messages.MessageField('LogBucket', 2)
  parent = _messages.StringField(3, required=True)


class LoggingProjectsLocationsBucketsCreateRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsCreateRequest object.

  Fields:
    bucketId: Required. A client-assigned identifier such as "my-bucket".
      Identifiers are limited to 100 characters and can include only letters,
      digits, underscores, hyphens, and periods.
    logBucket: A LogBucket resource to be passed as the request body.
    parent: Required. The resource in which to create the log bucket:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]" For
      example:"projects/my-project/locations/global"
  """

  bucketId = _messages.StringField(1)
  logBucket = _messages.MessageField('LogBucket', 2)
  parent = _messages.StringField(3, required=True)


class LoggingProjectsLocationsBucketsDeleteRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsDeleteRequest object.

  Fields:
    name: Required. The full resource name of the bucket to delete.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
  """

  name = _messages.StringField(1, required=True)


class LoggingProjectsLocationsBucketsGetRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsGetRequest object.

  Fields:
    name: Required. The resource name of the bucket:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
  """

  name = _messages.StringField(1, required=True)


class LoggingProjectsLocationsBucketsLinksCreateRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsLinksCreateRequest object.

  Fields:
    link: A Link resource to be passed as the request body.
    linkId: Required. The ID to use for the link. The link_id can have up to
      100 characters. A valid link_id must only have alphanumeric characters
      and underscores within it.
    parent: Required. The full resource name of the bucket to create a link
      for. "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
      "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET
      _ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buck
      ets/[BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  """

  link = _messages.MessageField('Link', 1)
  linkId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingProjectsLocationsBucketsLinksDeleteRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsLinksDeleteRequest object.

  Fields:
    name: Required. The full resource name of the link to delete. "projects/[P
      ROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
      "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET
      _ID]/links/[LINK_ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[L
      OCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]" "folders/[FOLDER_ID]/lo
      cations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  """

  name = _messages.StringField(1, required=True)


class LoggingProjectsLocationsBucketsLinksGetRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsLinksGetRequest object.

  Fields:
    name: Required. The resource name of the link: "projects/[PROJECT_ID]/loca
      tions/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]" "organizations/
      [ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/links/[LIN
      K_ID]" "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buc
      kets/[BUCKET_ID]/links/[LINK_ID]" "folders/[FOLDER_ID]/locations/[LOCATI
      ON_ID]/buckets/[BUCKET_ID]/links/[LINK_ID]"
  """

  name = _messages.StringField(1, required=True)


class LoggingProjectsLocationsBucketsLinksListRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsLinksListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response.
    parent: Required. The parent resource whose links are to be listed:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingProjectsLocationsBucketsListRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource whose buckets are to be listed:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]"
      "organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]" Note: The locations
      portion of the resource must be specified, but supplying the character -
      in place of LOCATION_ID will return all buckets.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingProjectsLocationsBucketsPatchRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsPatchRequest object.

  Fields:
    logBucket: A LogBucket resource to be passed as the request body.
    name: Required. The full resource name of the bucket to update.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
    updateMask: Required. Field mask that specifies the fields in bucket that
      need an update. A bucket field will be overwritten if, and only if, it
      is in the update mask. name and output only fields cannot be updated.For
      a detailed FieldMask definition, see:
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=retention_days
  """

  logBucket = _messages.MessageField('LogBucket', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingProjectsLocationsBucketsUndeleteRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsUndeleteRequest object.

  Fields:
    name: Required. The full resource name of the bucket to undelete.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
    undeleteBucketRequest: A UndeleteBucketRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  undeleteBucketRequest = _messages.MessageField('UndeleteBucketRequest', 2)


class LoggingProjectsLocationsBucketsUpdateAsyncRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsUpdateAsyncRequest object.

  Fields:
    logBucket: A LogBucket resource to be passed as the request body.
    name: Required. The full resource name of the bucket to update.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
    updateMask: Required. Field mask that specifies the fields in bucket that
      need an update. A bucket field will be overwritten if, and only if, it
      is in the update mask. name and output only fields cannot be updated.For
      a detailed FieldMask definition, see:
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=retention_days
  """

  logBucket = _messages.MessageField('LogBucket', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingProjectsLocationsBucketsViewsCreateRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsViewsCreateRequest object.

  Fields:
    logView: A LogView resource to be passed as the request body.
    parent: Required. The bucket in which to create the view
      `"projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"`
      For example:"projects/my-project/locations/global/buckets/my-bucket"
    viewId: Required. A client-assigned identifier such as "my-view".
      Identifiers are limited to 100 characters and can include only letters,
      digits, underscores, hyphens, and periods.
  """

  logView = _messages.MessageField('LogView', 1)
  parent = _messages.StringField(2, required=True)
  viewId = _messages.StringField(3)


class LoggingProjectsLocationsBucketsViewsDeleteRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsViewsDeleteRequest object.

  Fields:
    name: Required. The full resource name of the view to delete: "projects/[P
      ROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]"
      For example:"projects/my-project/locations/global/buckets/my-
      bucket/views/my-view"
  """

  name = _messages.StringField(1, required=True)


class LoggingProjectsLocationsBucketsViewsGetRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsViewsGetRequest object.

  Fields:
    name: Required. The resource name of the policy: "projects/[PROJECT_ID]/lo
      cations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]" For
      example:"projects/my-project/locations/global/buckets/my-
      bucket/views/my-view"
  """

  name = _messages.StringField(1, required=True)


class LoggingProjectsLocationsBucketsViewsListRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsViewsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request.Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The bucket whose views are to be listed:
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingProjectsLocationsBucketsViewsLogsListRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsViewsLogsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The resource name to list logs for:
      projects/[PROJECT_ID] organizations/[ORGANIZATION_ID]
      billingAccounts/[BILLING_ACCOUNT_ID] folders/[FOLDER_ID]
    resourceNames: Optional. List of resource names to list logs for: projects
      /[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID
      ] organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKE
      T_ID]/views/[VIEW_ID] billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LO
      CATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID] folders/[FOLDER_ID]/locat
      ions/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]To support legacy
      queries, it could also be: projects/[PROJECT_ID]
      organizations/[ORGANIZATION_ID] billingAccounts/[BILLING_ACCOUNT_ID]
      folders/[FOLDER_ID]The resource name in the parent field is added to
      this list.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  resourceNames = _messages.StringField(4, repeated=True)


class LoggingProjectsLocationsBucketsViewsPatchRequest(_messages.Message):
  r"""A LoggingProjectsLocationsBucketsViewsPatchRequest object.

  Fields:
    logView: A LogView resource to be passed as the request body.
    name: Required. The full resource name of the view to update "projects/[PR
      OJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]"
      For example:"projects/my-project/locations/global/buckets/my-
      bucket/views/my-view"
    updateMask: Optional. Field mask that specifies the fields in view that
      need an update. A field will be overwritten if, and only if, it is in
      the update mask. name and output only fields cannot be updated.For a
      detailed FieldMask definition, see
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=filter
  """

  logView = _messages.MessageField('LogView', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingProjectsLocationsGetRequest(_messages.Message):
  r"""A LoggingProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class LoggingProjectsLocationsListRequest(_messages.Message):
  r"""A LoggingProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like "displayName=tokyo", and is
      documented in more detail in AIP-160 (https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the next_page_token field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class LoggingProjectsLocationsOperationsApproveRedactionRequest(_messages.Message):
  r"""A LoggingProjectsLocationsOperationsApproveRedactionRequest object.

  Fields:
    name: Required. Name of the redaction operation.For example:"projects/my-
      project/locations/global/operations/my-operation"
  """

  name = _messages.StringField(1, required=True)


class LoggingProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A LoggingProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class LoggingProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A LoggingProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class LoggingProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A LoggingProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class LoggingProjectsLogsDeleteRequest(_messages.Message):
  r"""A LoggingProjectsLogsDeleteRequest object.

  Fields:
    logName: Required. The resource name of the log to delete:
      projects/[PROJECT_ID]/logs/[LOG_ID]
      organizations/[ORGANIZATION_ID]/logs/[LOG_ID]
      billingAccounts/[BILLING_ACCOUNT_ID]/logs/[LOG_ID]
      folders/[FOLDER_ID]/logs/[LOG_ID][LOG_ID] must be URL-encoded. For
      example, "projects/my-project-id/logs/syslog",
      "organizations/123/logs/cloudaudit.googleapis.com%2Factivity".For more
      information about log names, see LogEntry.
  """

  logName = _messages.StringField(1, required=True)


class LoggingProjectsLogsListRequest(_messages.Message):
  r"""A LoggingProjectsLogsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The resource name to list logs for:
      projects/[PROJECT_ID] organizations/[ORGANIZATION_ID]
      billingAccounts/[BILLING_ACCOUNT_ID] folders/[FOLDER_ID]
    resourceNames: Optional. List of resource names to list logs for: projects
      /[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID
      ] organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKE
      T_ID]/views/[VIEW_ID] billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LO
      CATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID] folders/[FOLDER_ID]/locat
      ions/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]To support legacy
      queries, it could also be: projects/[PROJECT_ID]
      organizations/[ORGANIZATION_ID] billingAccounts/[BILLING_ACCOUNT_ID]
      folders/[FOLDER_ID]The resource name in the parent field is added to
      this list.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  resourceNames = _messages.StringField(4, repeated=True)


class LoggingProjectsMetricsCreateRequest(_messages.Message):
  r"""A LoggingProjectsMetricsCreateRequest object.

  Fields:
    logMetric: A LogMetric resource to be passed as the request body.
    parent: Required. The resource name of the project in which to create the
      metric: "projects/[PROJECT_ID]" The new metric must be provided in the
      request.
  """

  logMetric = _messages.MessageField('LogMetric', 1)
  parent = _messages.StringField(2, required=True)


class LoggingProjectsMetricsDeleteRequest(_messages.Message):
  r"""A LoggingProjectsMetricsDeleteRequest object.

  Fields:
    metricName: Required. The resource name of the metric to delete:
      "projects/[PROJECT_ID]/metrics/[METRIC_ID]"
  """

  metricName = _messages.StringField(1, required=True)


class LoggingProjectsMetricsGetRequest(_messages.Message):
  r"""A LoggingProjectsMetricsGetRequest object.

  Fields:
    metricName: Required. The resource name of the desired metric:
      "projects/[PROJECT_ID]/metrics/[METRIC_ID]"
  """

  metricName = _messages.StringField(1, required=True)


class LoggingProjectsMetricsListRequest(_messages.Message):
  r"""A LoggingProjectsMetricsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The name of the project containing the metrics:
      "projects/[PROJECT_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingProjectsMetricsUpdateRequest(_messages.Message):
  r"""A LoggingProjectsMetricsUpdateRequest object.

  Fields:
    logMetric: A LogMetric resource to be passed as the request body.
    metricName: Required. The resource name of the metric to update:
      "projects/[PROJECT_ID]/metrics/[METRIC_ID]" The updated metric must be
      provided in the request and it's name field must be the same as
      [METRIC_ID] If the metric does not exist in [PROJECT_ID], then a new
      metric is created.
  """

  logMetric = _messages.MessageField('LogMetric', 1)
  metricName = _messages.StringField(2, required=True)


class LoggingProjectsSinksCreateRequest(_messages.Message):
  r"""A LoggingProjectsSinksCreateRequest object.

  Fields:
    customWriterIdentity: Optional. A service account provided by the caller
      that will be used to write the log entries. The format must be
      serviceAccount:some@email. This field can only be specified if you are
      routing logs to a destination outside this sink's project. If not
      specified, a Logging service account will automatically be generated.
    logSink: A LogSink resource to be passed as the request body.
    parent: Required. The resource in which to create the sink:
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]" For
      examples:"projects/my-project" "organizations/*********"
    uniqueWriterIdentity: Optional. Determines the kind of IAM identity
      returned as writer_identity in the new sink. If this value is omitted or
      set to false, and if the sink's parent is a project, then the value
      returned as writer_identity is the same group or service account used by
      Cloud Logging before the addition of writer identities to this API. The
      sink's destination must be in the same project as the sink itself.If
      this field is set to true, or if the sink is owned by a non-project
      resource such as an organization, then the value of writer_identity will
      be a service agent (https://cloud.google.com/iam/docs/service-account-
      types#service-agents) used by the sinks with the same parent. For more
      information, see writer_identity in LogSink.
  """

  customWriterIdentity = _messages.StringField(1)
  logSink = _messages.MessageField('LogSink', 2)
  parent = _messages.StringField(3, required=True)
  uniqueWriterIdentity = _messages.BooleanField(4)


class LoggingProjectsSinksDeleteRequest(_messages.Message):
  r"""A LoggingProjectsSinksDeleteRequest object.

  Fields:
    sinkName: Required. The full resource name of the sink to delete,
      including the parent resource and the sink identifier:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
  """

  sinkName = _messages.StringField(1, required=True)


class LoggingProjectsSinksGetRequest(_messages.Message):
  r"""A LoggingProjectsSinksGetRequest object.

  Fields:
    sinkName: Required. The resource name of the sink:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
  """

  sinkName = _messages.StringField(1, required=True)


class LoggingProjectsSinksListRequest(_messages.Message):
  r"""A LoggingProjectsSinksListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource whose sinks are to be listed:
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingProjectsSinksPatchRequest(_messages.Message):
  r"""A LoggingProjectsSinksPatchRequest object.

  Fields:
    customWriterIdentity: Optional. A service account provided by the caller
      that will be used to write the log entries. Must be of format
      serviceAccount:some@email. This can only be specified if writing to a
      destination outside the sink's project. If not specified, a p4 service
      account will automatically be generated.
    logSink: A LogSink resource to be passed as the request body.
    sinkName: Required. The full resource name of the sink to update,
      including the parent resource and the sink identifier:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
    uniqueWriterIdentity: Optional. See sinks.create for a description of this
      field. When updating a sink, the effect of this field on the value of
      writer_identity in the updated sink depends on both the old and new
      values of this field: If the old and new values of this field are both
      false or both true, then there is no change to the sink's
      writer_identity. If the old value is false and the new value is true,
      then writer_identity is changed to a service agent
      (https://cloud.google.com/iam/docs/service-account-types#service-agents)
      owned by Cloud Logging. It is an error if the old value is true and the
      new value is set to false or defaulted to false.
    updateMask: Optional. Field mask that specifies the fields in sink that
      need an update. A sink field will be overwritten if, and only if, it is
      in the update mask. name and output only fields cannot be updated.An
      empty updateMask is temporarily treated as using the following mask for
      backwards compatibility purposes:destination,filter,includeChildrenAt
      some point in the future, behavior will be removed and specifying an
      empty updateMask will be an error.For a detailed FieldMask definition,
      see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=filter
  """

  customWriterIdentity = _messages.StringField(1)
  logSink = _messages.MessageField('LogSink', 2)
  sinkName = _messages.StringField(3, required=True)
  uniqueWriterIdentity = _messages.BooleanField(4)
  updateMask = _messages.StringField(5)


class LoggingProjectsSinksUpdateRequest(_messages.Message):
  r"""A LoggingProjectsSinksUpdateRequest object.

  Fields:
    customWriterIdentity: Optional. A service account provided by the caller
      that will be used to write the log entries. Must be of format
      serviceAccount:some@email. This can only be specified if writing to a
      destination outside the sink's project. If not specified, a p4 service
      account will automatically be generated.
    logSink: A LogSink resource to be passed as the request body.
    sinkName: Required. The full resource name of the sink to update,
      including the parent resource and the sink identifier:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
    uniqueWriterIdentity: Optional. See sinks.create for a description of this
      field. When updating a sink, the effect of this field on the value of
      writer_identity in the updated sink depends on both the old and new
      values of this field: If the old and new values of this field are both
      false or both true, then there is no change to the sink's
      writer_identity. If the old value is false and the new value is true,
      then writer_identity is changed to a service agent
      (https://cloud.google.com/iam/docs/service-account-types#service-agents)
      owned by Cloud Logging. It is an error if the old value is true and the
      new value is set to false or defaulted to false.
    updateMask: Optional. Field mask that specifies the fields in sink that
      need an update. A sink field will be overwritten if, and only if, it is
      in the update mask. name and output only fields cannot be updated.An
      empty updateMask is temporarily treated as using the following mask for
      backwards compatibility purposes:destination,filter,includeChildrenAt
      some point in the future, behavior will be removed and specifying an
      empty updateMask will be an error.For a detailed FieldMask definition,
      see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=filter
  """

  customWriterIdentity = _messages.StringField(1)
  logSink = _messages.MessageField('LogSink', 2)
  sinkName = _messages.StringField(3, required=True)
  uniqueWriterIdentity = _messages.BooleanField(4)
  updateMask = _messages.StringField(5)


class LoggingSinksCreateRequest(_messages.Message):
  r"""A LoggingSinksCreateRequest object.

  Fields:
    customWriterIdentity: Optional. A service account provided by the caller
      that will be used to write the log entries. The format must be
      serviceAccount:some@email. This field can only be specified if you are
      routing logs to a destination outside this sink's project. If not
      specified, a Logging service account will automatically be generated.
    logSink: A LogSink resource to be passed as the request body.
    parent: Required. The resource in which to create the sink:
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]" For
      examples:"projects/my-project" "organizations/*********"
    uniqueWriterIdentity: Optional. Determines the kind of IAM identity
      returned as writer_identity in the new sink. If this value is omitted or
      set to false, and if the sink's parent is a project, then the value
      returned as writer_identity is the same group or service account used by
      Cloud Logging before the addition of writer identities to this API. The
      sink's destination must be in the same project as the sink itself.If
      this field is set to true, or if the sink is owned by a non-project
      resource such as an organization, then the value of writer_identity will
      be a service agent (https://cloud.google.com/iam/docs/service-account-
      types#service-agents) used by the sinks with the same parent. For more
      information, see writer_identity in LogSink.
  """

  customWriterIdentity = _messages.StringField(1)
  logSink = _messages.MessageField('LogSink', 2)
  parent = _messages.StringField(3, required=True)
  uniqueWriterIdentity = _messages.BooleanField(4)


class LoggingSinksDeleteRequest(_messages.Message):
  r"""A LoggingSinksDeleteRequest object.

  Fields:
    sinkName: Required. The full resource name of the sink to delete,
      including the parent resource and the sink identifier:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
  """

  sinkName = _messages.StringField(1, required=True)


class LoggingSinksGetRequest(_messages.Message):
  r"""A LoggingSinksGetRequest object.

  Fields:
    sinkName: Required. The resource name of the sink:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
  """

  sinkName = _messages.StringField(1, required=True)


class LoggingSinksListRequest(_messages.Message):
  r"""A LoggingSinksListRequest object.

  Fields:
    pageSize: Optional. The maximum number of results to return from this
      request. Non-positive values are ignored. The presence of nextPageToken
      in the response indicates that more results might be available.
    pageToken: Optional. If present, then retrieve the next batch of results
      from the preceding call to this method. pageToken must be the value of
      nextPageToken from the previous response. The values of other method
      parameters should be identical to those in the previous call.
    parent: Required. The parent resource whose sinks are to be listed:
      "projects/[PROJECT_ID]" "organizations/[ORGANIZATION_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]" "folders/[FOLDER_ID]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class LoggingSinksUpdateRequest(_messages.Message):
  r"""A LoggingSinksUpdateRequest object.

  Fields:
    customWriterIdentity: Optional. A service account provided by the caller
      that will be used to write the log entries. Must be of format
      serviceAccount:some@email. This can only be specified if writing to a
      destination outside the sink's project. If not specified, a p4 service
      account will automatically be generated.
    logSink: A LogSink resource to be passed as the request body.
    sinkName: Required. The full resource name of the sink to update,
      including the parent resource and the sink identifier:
      "projects/[PROJECT_ID]/sinks/[SINK_ID]"
      "organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
      "billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
      "folders/[FOLDER_ID]/sinks/[SINK_ID]" For example:"projects/my-
      project/sinks/my-sink"
    uniqueWriterIdentity: Optional. See sinks.create for a description of this
      field. When updating a sink, the effect of this field on the value of
      writer_identity in the updated sink depends on both the old and new
      values of this field: If the old and new values of this field are both
      false or both true, then there is no change to the sink's
      writer_identity. If the old value is false and the new value is true,
      then writer_identity is changed to a service agent
      (https://cloud.google.com/iam/docs/service-account-types#service-agents)
      owned by Cloud Logging. It is an error if the old value is true and the
      new value is set to false or defaulted to false.
    updateMask: Optional. Field mask that specifies the fields in sink that
      need an update. A sink field will be overwritten if, and only if, it is
      in the update mask. name and output only fields cannot be updated.An
      empty updateMask is temporarily treated as using the following mask for
      backwards compatibility purposes:destination,filter,includeChildrenAt
      some point in the future, behavior will be removed and specifying an
      empty updateMask will be an error.For a detailed FieldMask definition,
      see https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=filter
  """

  customWriterIdentity = _messages.StringField(1)
  logSink = _messages.MessageField('LogSink', 2)
  sinkName = _messages.StringField(3, required=True)
  uniqueWriterIdentity = _messages.BooleanField(4)
  updateMask = _messages.StringField(5)


class LoggingUpdateCmekSettingsRequest(_messages.Message):
  r"""A LoggingUpdateCmekSettingsRequest object.

  Fields:
    cmekSettings: A CmekSettings resource to be passed as the request body.
    name: Required. The resource name for the CMEK settings to update.
      "projects/[PROJECT_ID]/cmekSettings"
      "organizations/[ORGANIZATION_ID]/cmekSettings"
      "billingAccounts/[BILLING_ACCOUNT_ID]/cmekSettings"
      "folders/[FOLDER_ID]/cmekSettings" For
      example:"organizations/12345/cmekSettings"Note: CMEK for the Log Router
      can currently only be configured for Google Cloud organizations. Once
      configured, it applies to all projects and folders in the Google Cloud
      organization.
    updateMask: Optional. Field mask identifying which fields from
      cmek_settings should be updated. A field will be overwritten if and only
      if it is in the update mask. Output only fields cannot be updated.See
      FieldMask for more information.For example: "updateMask=kmsKeyName"
  """

  cmekSettings = _messages.MessageField('CmekSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class LoggingUpdateSettingsRequest(_messages.Message):
  r"""A LoggingUpdateSettingsRequest object.

  Fields:
    name: Required. The resource name for the settings to update.
      "organizations/[ORGANIZATION_ID]/settings" For
      example:"organizations/12345/settings"Note: Settings for the Log Router
      can currently only be configured for Google Cloud organizations. Once
      configured, it applies to all projects and folders in the Google Cloud
      organization.
    settings: A Settings resource to be passed as the request body.
    updateMask: Optional. Field mask identifying which fields from settings
      should be updated. A field will be overwritten if and only if it is in
      the update mask. Output only fields cannot be updated.See FieldMask for
      more information.For example: "updateMask=kmsKeyName"
  """

  name = _messages.StringField(1, required=True)
  settings = _messages.MessageField('Settings', 2)
  updateMask = _messages.StringField(3)


class Measure(_messages.Message):
  r"""A definition for a single measure column in the output table. Multiple
  measure columns will produce multiple curves, stacked bars, etc. depending
  on chart type.

  Fields:
    aggregation: The aggregation to apply to the input column. Required if
      binning is enabled on the dimension.
    column: Required. The column name within the output of the previous step
      to use. May be the same column as the dimension. May be left empty if
      the aggregation is set to "count" (but not "count-distinct" or "count-
      distinct-approx").
  """

  aggregation = _messages.MessageField('QueryStepAggregation', 1)
  column = _messages.StringField(2)


class MetricDescriptor(_messages.Message):
  r"""Defines a metric type and its schema. Once a metric descriptor is
  created, deleting or altering it stops data collection and makes the metric
  type's existing data unusable.

  Enums:
    LaunchStageValueValuesEnum: Optional. The launch stage of the metric
      definition.
    MetricKindValueValuesEnum: Whether the metric records instantaneous
      values, changes to a value, etc. Some combinations of metric_kind and
      value_type might not be supported.
    ValueTypeValueValuesEnum: Whether the measurement is an integer, a
      floating-point number, etc. Some combinations of metric_kind and
      value_type might not be supported.

  Fields:
    description: A detailed description of the metric, which can be used in
      documentation.
    displayName: A concise name for the metric, which can be displayed in user
      interfaces. Use sentence case without an ending period, for example
      "Request count". This field is optional but it is recommended to be set
      for any metrics associated with user-visible concepts, such as Quota.
    labels: The set of labels that can be used to describe a specific instance
      of this metric type. For example, the
      appengine.googleapis.com/http/server/response_latencies metric type has
      a label for the HTTP response code, response_code, so you can look at
      latencies for successful responses or just for responses that failed.
    launchStage: Optional. The launch stage of the metric definition.
    metadata: Optional. Metadata which can be used to guide usage of the
      metric.
    metricKind: Whether the metric records instantaneous values, changes to a
      value, etc. Some combinations of metric_kind and value_type might not be
      supported.
    monitoredResourceTypes: Read-only. If present, then a time series, which
      is identified partially by a metric type and a
      MonitoredResourceDescriptor, that is associated with this metric type
      can only be associated with one of the monitored resource types listed
      here.
    name: The resource name of the metric descriptor.
    type: The metric type, including its DNS name prefix. The type is not URL-
      encoded. All user-defined metric types have the DNS name
      custom.googleapis.com or external.googleapis.com. Metric types should
      use a natural hierarchical grouping. For example:
      "custom.googleapis.com/invoice/paid/amount"
      "external.googleapis.com/prometheus/up"
      "appengine.googleapis.com/http/server/response_latencies"
    unit: The units in which the metric value is reported. It is only
      applicable if the value_type is INT64, DOUBLE, or DISTRIBUTION. The unit
      defines the representation of the stored metric values.Different systems
      might scale the values to be more easily displayed (so a value of
      0.02kBy might be displayed as 20By, and a value of 3523kBy might be
      displayed as 3.5MBy). However, if the unit is kBy, then the value of the
      metric is always in thousands of bytes, no matter how it might be
      displayed.If you want a custom metric to record the exact number of CPU-
      seconds used by a job, you can create an INT64 CUMULATIVE metric whose
      unit is s{CPU} (or equivalently 1s{CPU} or just s). If the job uses
      12,005 CPU-seconds, then the value is written as 12005.Alternatively, if
      you want a custom metric to record data in a more granular way, you can
      create a DOUBLE CUMULATIVE metric whose unit is ks{CPU}, and then write
      the value 12.005 (which is 12005/1000), or use Kis{CPU} and write 11.723
      (which is 12005/1024).The supported units are a subset of The Unified
      Code for Units of Measure (https://unitsofmeasure.org/ucum.html)
      standard:Basic units (UNIT) bit bit By byte s second min minute h hour d
      day 1 dimensionlessPrefixes (PREFIX) k kilo (10^3) M mega (10^6) G giga
      (10^9) T tera (10^12) P peta (10^15) E exa (10^18) Z zetta (10^21) Y
      yotta (10^24) m milli (10^-3) u micro (10^-6) n nano (10^-9) p pico
      (10^-12) f femto (10^-15) a atto (10^-18) z zepto (10^-21) y yocto
      (10^-24) Ki kibi (2^10) Mi mebi (2^20) Gi gibi (2^30) Ti tebi (2^40) Pi
      pebi (2^50)GrammarThe grammar also includes these connectors: / division
      or ratio (as an infix operator). For examples, kBy/{email} or MiBy/10ms
      (although you should almost never have /s in a metric unit; rates should
      always be computed at query time from the underlying cumulative or delta
      value). . multiplication or composition (as an infix operator). For
      examples, GBy.d or k{watt}.h.The grammar for a unit is as follows:
      Expression = Component { "." Component } { "/" Component } ; Component =
      ( [ PREFIX ] UNIT | "%" ) [ Annotation ] | Annotation | "1" ; Annotation
      = "{" NAME "}" ; Notes: Annotation is just a comment if it follows a
      UNIT. If the annotation is used alone, then the unit is equivalent to 1.
      For examples, {request}/s == 1/s, By{transmitted}/s == By/s. NAME is a
      sequence of non-blank printable ASCII characters not containing { or }.
      1 represents a unitary dimensionless unit
      (https://en.wikipedia.org/wiki/Dimensionless_quantity) of 1, such as in
      1/s. It is typically used when none of the basic units are appropriate.
      For example, "new users per day" can be represented as 1/d or {new-
      users}/d (and a metric value 5 would mean "5 new users). Alternatively,
      "thousands of page views per day" would be represented as 1000/d or k1/d
      or k{page_views}/d (and a metric value of 5.3 would mean "5300 page
      views per day"). % represents dimensionless value of 1/100, and
      annotates values giving a percentage (so the metric values are typically
      in the range of 0..100, and a metric value 3 means "3 percent"). 10^2.%
      indicates a metric contains a ratio, typically in the range 0..1, that
      will be multiplied by 100 and displayed as a percentage (so a metric
      value 0.03 means "3 percent").
    valueType: Whether the measurement is an integer, a floating-point number,
      etc. Some combinations of metric_kind and value_type might not be
      supported.
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Optional. The launch stage of the metric definition.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our Terms of Service (https://cloud.google.com/terms/) and the Google
        Cloud Platform Subject to the Deprecation Policy
        (https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  class MetricKindValueValuesEnum(_messages.Enum):
    r"""Whether the metric records instantaneous values, changes to a value,
    etc. Some combinations of metric_kind and value_type might not be
    supported.

    Values:
      METRIC_KIND_UNSPECIFIED: Do not use this default value.
      GAUGE: An instantaneous measurement of a value.
      DELTA: The change in a value during a time interval.
      CUMULATIVE: A value accumulated over a time interval. Cumulative
        measurements in a time series should have the same start time and
        increasing end times, until an event resets the cumulative value to
        zero and sets a new start time for the following points.
    """
    METRIC_KIND_UNSPECIFIED = 0
    GAUGE = 1
    DELTA = 2
    CUMULATIVE = 3

  class ValueTypeValueValuesEnum(_messages.Enum):
    r"""Whether the measurement is an integer, a floating-point number, etc.
    Some combinations of metric_kind and value_type might not be supported.

    Values:
      VALUE_TYPE_UNSPECIFIED: Do not use this default value.
      BOOL: The value is a boolean. This value type can be used only if the
        metric kind is GAUGE.
      INT64: The value is a signed 64-bit integer.
      DOUBLE: The value is a double precision floating point number.
      STRING: The value is a text string. This value type can be used only if
        the metric kind is GAUGE.
      DISTRIBUTION: The value is a Distribution.
      MONEY: The value is money.
    """
    VALUE_TYPE_UNSPECIFIED = 0
    BOOL = 1
    INT64 = 2
    DOUBLE = 3
    STRING = 4
    DISTRIBUTION = 5
    MONEY = 6

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  labels = _messages.MessageField('LabelDescriptor', 3, repeated=True)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 4)
  metadata = _messages.MessageField('MetricDescriptorMetadata', 5)
  metricKind = _messages.EnumField('MetricKindValueValuesEnum', 6)
  monitoredResourceTypes = _messages.StringField(7, repeated=True)
  name = _messages.StringField(8)
  type = _messages.StringField(9)
  unit = _messages.StringField(10)
  valueType = _messages.EnumField('ValueTypeValueValuesEnum', 11)


class MetricDescriptorMetadata(_messages.Message):
  r"""Additional annotations that can be used to guide the usage of a metric.

  Enums:
    LaunchStageValueValuesEnum: Deprecated. Must use the
      MetricDescriptor.launch_stage instead.

  Fields:
    ingestDelay: The delay of data points caused by ingestion. Data points
      older than this age are guaranteed to be ingested and available to be
      read, excluding data loss due to errors.
    launchStage: Deprecated. Must use the MetricDescriptor.launch_stage
      instead.
    samplePeriod: The sampling period of metric data points. For metrics which
      are written periodically, consecutive data points are stored at this
      time interval, excluding data loss due to errors. Metrics with a higher
      granularity have a smaller sampling period.
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Deprecated. Must use the MetricDescriptor.launch_stage instead.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our Terms of Service (https://cloud.google.com/terms/) and the Google
        Cloud Platform Subject to the Deprecation Policy
        (https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  ingestDelay = _messages.StringField(1)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 2)
  samplePeriod = _messages.StringField(3)


class MonitoredResource(_messages.Message):
  r"""An object representing a resource that can be used for monitoring,
  logging, billing, or other purposes. Examples include virtual machine
  instances, databases, and storage devices such as disks. The type field
  identifies a MonitoredResourceDescriptor object that describes the
  resource's schema. Information in the labels field identifies the actual
  resource and its attributes according to the schema. For example, a
  particular Compute Engine VM instance could be represented by the following
  object, because the MonitoredResourceDescriptor for "gce_instance" has
  labels "project_id", "instance_id" and "zone": { "type": "gce_instance",
  "labels": { "project_id": "my-project", "instance_id": "*********01234",
  "zone": "us-central1-a" }}

  Messages:
    LabelsValue: Required. Values for all of the labels listed in the
      associated monitored resource descriptor. For example, Compute Engine VM
      instances use the labels "project_id", "instance_id", and "zone".

  Fields:
    labels: Required. Values for all of the labels listed in the associated
      monitored resource descriptor. For example, Compute Engine VM instances
      use the labels "project_id", "instance_id", and "zone".
    type: Required. The monitored resource type. This field must match the
      type field of a MonitoredResourceDescriptor object. For example, the
      type of a Compute Engine VM instance is gce_instance. Some descriptors
      include the service name in the type; for example, the type of a
      Datastream stream is datastream.googleapis.com/Stream.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Required. Values for all of the labels listed in the associated
    monitored resource descriptor. For example, Compute Engine VM instances
    use the labels "project_id", "instance_id", and "zone".

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  type = _messages.StringField(2)


class MonitoredResourceDescriptor(_messages.Message):
  r"""An object that describes the schema of a MonitoredResource object using
  a type name and a set of labels. For example, the monitored resource
  descriptor for Google Compute Engine VM instances has a type of
  "gce_instance" and specifies the use of the labels "instance_id" and "zone"
  to identify particular VM instances.Different APIs can support different
  monitored resource types. APIs generally provide a list method that returns
  the monitored resource descriptors used by the API.

  Enums:
    LaunchStageValueValuesEnum: Optional. The launch stage of the monitored
      resource definition.

  Fields:
    description: Optional. A detailed description of the monitored resource
      type that might be used in documentation.
    displayName: Optional. A concise name for the monitored resource type that
      might be displayed in user interfaces. It should be a Title Cased Noun
      Phrase, without any article or other determiners. For example, "Google
      Cloud SQL Database".
    labels: Required. A set of labels used to describe instances of this
      monitored resource type. For example, an individual Google Cloud SQL
      database is identified by values for the labels "database_id" and
      "zone".
    launchStage: Optional. The launch stage of the monitored resource
      definition.
    name: Optional. The resource name of the monitored resource descriptor:
      "projects/{project_id}/monitoredResourceDescriptors/{type}" where {type}
      is the value of the type field in this object and {project_id} is a
      project ID that provides API-specific context for accessing the type.
      APIs that do not use project information can use the resource name
      format "monitoredResourceDescriptors/{type}".
    type: Required. The monitored resource type. For example, the type
      "cloudsql_database" represents databases in Google Cloud SQL. For a list
      of types, see Monitoring resource types
      (https://cloud.google.com/monitoring/api/resources) and Logging resource
      types (https://cloud.google.com/logging/docs/api/v2/resource-list).
  """

  class LaunchStageValueValuesEnum(_messages.Enum):
    r"""Optional. The launch stage of the monitored resource definition.

    Values:
      LAUNCH_STAGE_UNSPECIFIED: Do not use this default value.
      UNIMPLEMENTED: The feature is not yet implemented. Users can not use it.
      PRELAUNCH: Prelaunch features are hidden from users and are only visible
        internally.
      EARLY_ACCESS: Early Access features are limited to a closed group of
        testers. To use these features, you must sign up in advance and sign a
        Trusted Tester agreement (which includes confidentiality provisions).
        These features may be unstable, changed in backward-incompatible ways,
        and are not guaranteed to be released.
      ALPHA: Alpha is a limited availability test for releases before they are
        cleared for widespread use. By Alpha, all significant design issues
        are resolved and we are in the process of verifying functionality.
        Alpha customers need to apply for access, agree to applicable terms,
        and have their projects allowlisted. Alpha releases don't have to be
        feature complete, no SLAs are provided, and there are no technical
        support obligations, but they will be far enough along that customers
        can actually use them in test environments or for limited-use tests --
        just like they would in normal production cases.
      BETA: Beta is the point at which we are ready to open a release for any
        customer to use. There are no SLA or technical support obligations in
        a Beta release. Products will be complete from a feature perspective,
        but may have some open outstanding issues. Beta releases are suitable
        for limited production use cases.
      GA: GA features are open to all developers and are considered stable and
        fully qualified for production use.
      DEPRECATED: Deprecated features are scheduled to be shut down and
        removed. For more information, see the "Deprecation Policy" section of
        our Terms of Service (https://cloud.google.com/terms/) and the Google
        Cloud Platform Subject to the Deprecation Policy
        (https://cloud.google.com/terms/deprecation) documentation.
    """
    LAUNCH_STAGE_UNSPECIFIED = 0
    UNIMPLEMENTED = 1
    PRELAUNCH = 2
    EARLY_ACCESS = 3
    ALPHA = 4
    BETA = 5
    GA = 6
    DEPRECATED = 7

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  labels = _messages.MessageField('LabelDescriptor', 3, repeated=True)
  launchStage = _messages.EnumField('LaunchStageValueValuesEnum', 4)
  name = _messages.StringField(5)
  type = _messages.StringField(6)


class MonitoredResourceMetadata(_messages.Message):
  r"""Auxiliary metadata for a MonitoredResource object. MonitoredResource
  objects contain the minimum set of information to uniquely identify a
  monitored resource instance. There is some other useful auxiliary metadata.
  Monitoring and Logging use an ingestion pipeline to extract metadata for
  cloud resources of all types, and store the metadata in this message.

  Messages:
    SystemLabelsValue: Output only. Values for predefined system metadata
      labels. System labels are a kind of metadata extracted by Google,
      including "machine_image", "vpc", "subnet_id", "security_group", "name",
      etc. System label values can be only strings, Boolean values, or a list
      of strings. For example: { "name": "my-test-instance", "security_group":
      ["a", "b", "c"], "spot_instance": false }
    UserLabelsValue: Output only. A map of user-defined metadata labels.

  Fields:
    systemLabels: Output only. Values for predefined system metadata labels.
      System labels are a kind of metadata extracted by Google, including
      "machine_image", "vpc", "subnet_id", "security_group", "name", etc.
      System label values can be only strings, Boolean values, or a list of
      strings. For example: { "name": "my-test-instance", "security_group":
      ["a", "b", "c"], "spot_instance": false }
    userLabels: Output only. A map of user-defined metadata labels.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SystemLabelsValue(_messages.Message):
    r"""Output only. Values for predefined system metadata labels. System
    labels are a kind of metadata extracted by Google, including
    "machine_image", "vpc", "subnet_id", "security_group", "name", etc. System
    label values can be only strings, Boolean values, or a list of strings.
    For example: { "name": "my-test-instance", "security_group": ["a", "b",
    "c"], "spot_instance": false }

    Messages:
      AdditionalProperty: An additional property for a SystemLabelsValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SystemLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class UserLabelsValue(_messages.Message):
    r"""Output only. A map of user-defined metadata labels.

    Messages:
      AdditionalProperty: An additional property for a UserLabelsValue object.

    Fields:
      additionalProperties: Additional properties of type UserLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a UserLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  systemLabels = _messages.MessageField('SystemLabelsValue', 1)
  userLabels = _messages.MessageField('UserLabelsValue', 2)


class MoveBucketMetadata(_messages.Message):
  r"""Metadata for long running MoveBucket operations.

  Enums:
    StateValueValuesEnum: State of the operation.

  Fields:
    endTime: The end time of the operation.
    request: MoveBucket RPC request.
    startTime: The create time of the operation.
    state: State of the operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of the operation.

    Values:
      OPERATION_STATE_UNSPECIFIED: Should not be used.
      OPERATION_STATE_SCHEDULED: The operation is scheduled.
      OPERATION_STATE_WAITING_FOR_PERMISSIONS: Waiting for necessary
        permissions.
      OPERATION_STATE_RUNNING: The operation is running.
      OPERATION_STATE_SUCCEEDED: The operation was completed successfully.
      OPERATION_STATE_FAILED: The operation failed.
      OPERATION_STATE_CANCELLED: The operation was cancelled by the user.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    OPERATION_STATE_SCHEDULED = 1
    OPERATION_STATE_WAITING_FOR_PERMISSIONS = 2
    OPERATION_STATE_RUNNING = 3
    OPERATION_STATE_SUCCEEDED = 4
    OPERATION_STATE_FAILED = 5
    OPERATION_STATE_CANCELLED = 6

  endTime = _messages.StringField(1)
  request = _messages.MessageField('MoveBucketRequest', 2)
  startTime = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class MoveBucketRequest(_messages.Message):
  r"""The parameters to MoveBucket.

  Fields:
    name: Required. The full resource name of the source bucket to move.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
    newName: Required. The full resource name of the relocated bucket.
  """

  name = _messages.StringField(1)
  newName = _messages.StringField(2)


class MoveBucketResponse(_messages.Message):
  r"""The response from MoveBucket.

  Fields:
    bucket: The resulting bucket from the move action.
  """

  bucket = _messages.MessageField('LogBucket', 1)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as Delete, the response
      is google.protobuf.Empty. If the original method is standard
      Get/Create/Update, the response should be the resource. For other
      methods, the response should have the type XxxResponse, where Xxx is the
      original method name. For example, if the original method name is
      TakeSnapshot(), the inferred response type is TakeSnapshotResponse.

  Fields:
    done: If the value is false, it means the operation is still in progress.
      If true, the operation is completed, and either error or response is
      available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the name should be a resource name ending with operations/{unique_id}.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as Delete, the response
      is google.protobuf.Empty. If the original method is standard
      Get/Create/Update, the response should be the resource. For other
      methods, the response should have the type XxxResponse, where Xxx is the
      original method name. For example, if the original method name is
      TakeSnapshot(), the inferred response type is TakeSnapshotResponse.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as Delete, the response is
    google.protobuf.Empty. If the original method is standard
    Get/Create/Update, the response should be the resource. For other methods,
    the response should have the type XxxResponse, where Xxx is the original
    method name. For example, if the original method name is TakeSnapshot(),
    the inferred response type is TakeSnapshotResponse.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class Parameter(_messages.Message):
  r"""A parameter value to be applied to an aggregation.

  Fields:
    doubleValue: Optional. A floating-point parameter value.
    intValue: Optional. An integer parameter value.
  """

  doubleValue = _messages.FloatField(1)
  intValue = _messages.IntegerField(2)


class QueryDataRequest(_messages.Message):
  r"""The parameters to QueryData.

  Fields:
    disableQueryCaching: Optional. If set to true, turns off all query caching
      on both the Log Analytics and BigQuery sides.
    querySteps: The query steps to execute. Each query step will correspond to
      a handle in the result proto.
    resourceNames: Required. Names of one or more log views to run a SQL
      query.Example: projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BU
      CKET_ID]/views/[VIEW_ID]Requires appropriate permissions on each
      resource such as 'logging.views.access' on log view resources.
    timeout: Optional. The timeout for the query. BigQuery will terminate the
      job if this duration is exceeded. If not set, the default is 5 minutes.
  """

  disableQueryCaching = _messages.BooleanField(1)
  querySteps = _messages.MessageField('QueryStep', 2, repeated=True)
  resourceNames = _messages.StringField(3, repeated=True)
  timeout = _messages.StringField(4)


class QueryDataResponse(_messages.Message):
  r"""The response data from QueryData.

  Fields:
    queryStepHandles: Handles to each of the query steps described in the
      request. These may be passed to ReadQueryResults or used in a
      HandleQueryStep in a subsequent call to QueryData.
    restrictionConflicts: Conflicts between the query and the restrictions
      that were requested. Any restrictions present here were ignored when
      executing the query.
  """

  queryStepHandles = _messages.StringField(1, repeated=True)
  restrictionConflicts = _messages.MessageField('QueryRestrictionConflict', 2, repeated=True)


class QueryLogEntriesRequest(_messages.Message):
  r"""The parameters to QueryLogEntries.

  Fields:
    disableQueryCaching: Optional. If set to true, turns off all query caching
      on both the Log Analytics and BigQuery sides.
    pageSize: Optional. The maximum number of rows to return in the results.
      Responses are limited to 10 MB in size.By default, there is no maximum
      row count, and only the byte limit applies. When the byte limit is
      reached, the rest of query results will be paginated.
    pageToken: Optional. Page token returned by a previous call to
      QueryLogEntries to paginate through the response rows.
    query: Optional. A query string, following the BigQuery SQL query syntax.
      The FROM clause should specify a fully qualified log view corresponding
      to the log view in the resource_names in dot separated format like
      PROJECT_ID.LOCATION_ID.BUCKET_ID.VIEW_ID.For example: SELECT count(*)
      FROM my_project.us.my_bucket._AllLogs;If any of the dot separated
      components have special characters, that component needs to be escaped
      separately like the following example:SELECT count(*) FROM
      company.com:abc.us.my-bucket._AllLogs;
    queryRestriction: Optional. Restrictions being requested.
    resourceNames: Required. Names of one or more log views to run a SQL
      query. Currently, only a single view is supported. Multiple view
      selection may be supported in the future.Example: projects/[PROJECT_ID]/
      locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]Requires
      'logging.views.access' on the specified view resources.
    resultReference: Optional. Reference to a result from a previous query. If
      the results have expired the query will return a NOT_FOUND error.
    validateOnly: Optional. If set to true, the query is not executed.
      Instead, if the query is valid, statistics is returned about the query
      such as how many bytes would be processed. If the query is invalid, an
      error returns. The default value is false.
  """

  disableQueryCaching = _messages.BooleanField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  query = _messages.StringField(4)
  queryRestriction = _messages.MessageField('QueryRestriction', 5)
  resourceNames = _messages.StringField(6, repeated=True)
  resultReference = _messages.StringField(7)
  validateOnly = _messages.BooleanField(8)


class QueryParameter(_messages.Message):
  r"""A parameter given to a query.

  Fields:
    description: Optional. Human-oriented description of the field.
    intArray: Optional. The value of a parameter containing an array of
      integers.
    intValue: Optional. The value of an integer parameter.
    name: Optional. If unset, this is a positional parameter. Otherwise,
      should be unique within a query.
    stringArray: Optional. The value of a parameter containing an array of
      strings.
    stringValue: Optional. The value of a string parameter.
  """

  description = _messages.StringField(1)
  intArray = _messages.MessageField('IntegerArrayValue', 2)
  intValue = _messages.IntegerField(3)
  name = _messages.StringField(4)
  stringArray = _messages.MessageField('StringArrayValue', 5)
  stringValue = _messages.StringField(6)


class QueryRestriction(_messages.Message):
  r"""Specifies query restrictions to apply. This allows UI to provide common
  filter needs (e.g. timestamps) without having the user to write them in SQL.

  Fields:
    timerange: Optional. This restriction is the TIME_RANGE restriction type
      in the QueryRestrictionConflict. Range is [start_time, end_time).
      Granularity: down to milliseconds (not nanoseconds)
  """

  timerange = _messages.MessageField('Interval', 1)


class QueryRestrictionConflict(_messages.Message):
  r"""A conflict within a query that prevents applying restrictions. For
  instance, if the query contains a timestamp, this conflicts with timestamp
  restrictions e.g. time picker settings.

  Enums:
    ConfidenceValueValuesEnum: How confident the detector is that the
      restriction would cause a conflict.
    TypeValueValuesEnum: Specifies what conflict is present. Currently, this
      only supports timerange.

  Fields:
    column: One-based column number where the conflict was detected within the
      query.
    confidence: How confident the detector is that the restriction would cause
      a conflict.
    line: One-based line number where the conflict was detected within the
      query.
    type: Specifies what conflict is present. Currently, this only supports
      timerange.
  """

  class ConfidenceValueValuesEnum(_messages.Enum):
    r"""How confident the detector is that the restriction would cause a
    conflict.

    Values:
      CONFIDENCE_UNSPECIFIED: Invalid.
      CERTAIN: If set, the query would be adversely affected by applying the
        restriction.
      UNCERTAIN: If set, the Query used a column being restricted, but might
        not be adversely affected.
    """
    CONFIDENCE_UNSPECIFIED = 0
    CERTAIN = 1
    UNCERTAIN = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""Specifies what conflict is present. Currently, this only supports
    timerange.

    Values:
      RESTRICTION_TYPE_UNSPECIFIED: Invalid.
      TIME_RANGE: This type means that the query conflicts with the time range
        restriction, e.g. query used the timestamp column to filter.
    """
    RESTRICTION_TYPE_UNSPECIFIED = 0
    TIME_RANGE = 1

  column = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  confidence = _messages.EnumField('ConfidenceValueValuesEnum', 2)
  line = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class QueryResults(_messages.Message):
  r"""Results of a SQL query over logs.

  Messages:
    RowsValueListEntry: A RowsValueListEntry object.

  Fields:
    executionDuration: The total execution duration of the query.
    nextPageToken: A token that can be sent as page_token to retrieve the next
      page. If this field is omitted, there are no subsequent pages.
    queryComplete: Whether the query has completed or not. If this is false,
      the rows, total_rows, and execution_time fields will not be populated.
      The client needs to poll on ReadQueryResults specifying the
      result_reference and wait for results.
    restrictionConflicts: Conflicts between the query and the restrictions
      that were requested. Any restrictions present here were ignored when
      executing the query.
    resultReference: An opaque string that can be used as a reference to this
      query result. This result reference can be used in the QueryData query
      to fetch this result up to 24 hours in the future.
    rows: Query result rows. The number of rows returned depends upon the page
      size requested. To get any additional rows, you can call
      ReadQueryResults and specify the result_reference and the page_token.The
      REST-based representation of this data leverages a series of JSON f,v
      objects for indicating fields and values.
    schema: The schema of the results. It shows the columns present in the
      output table. Present only when the query completes successfully.
    totalBytesProcessed: The total number of bytes processed for this query.
      If this query was a validate_only query, this is the number of bytes
      that would be processed if the query were run.
    totalRows: The total number of rows in the complete query result set,
      which can be more than the number of rows in this single page of
      results.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RowsValueListEntry(_messages.Message):
    r"""A RowsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a RowsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RowsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  executionDuration = _messages.StringField(1)
  nextPageToken = _messages.StringField(2)
  queryComplete = _messages.BooleanField(3)
  restrictionConflicts = _messages.MessageField('QueryRestrictionConflict', 4, repeated=True)
  resultReference = _messages.StringField(5)
  rows = _messages.MessageField('RowsValueListEntry', 6, repeated=True)
  schema = _messages.MessageField('TableSchema', 7)
  totalBytesProcessed = _messages.IntegerField(8)
  totalRows = _messages.IntegerField(9)


class QueryStep(_messages.Message):
  r"""One step of the query. Each query step other than the first implicitly
  takes the output of the previous step as its input. Steps will be executed
  in sequence and will return their results independently (in other words,
  each step will finish at a different time and potentially return a different
  schema).

  Fields:
    alertingQueryStep: A query step that builds an alerting query from
      configuration data.
    chartingQueryStep: A query step that builds a charting query from
      configuration data.
    handleQueryStep: A query step that refers to a step within a previously-
      executed query.
    sqlQueryStep: A query step containing a SQL query.
  """

  alertingQueryStep = _messages.MessageField('AlertingQueryStep', 1)
  chartingQueryStep = _messages.MessageField('ChartingQueryStep', 2)
  handleQueryStep = _messages.MessageField('HandleQueryStep', 3)
  sqlQueryStep = _messages.MessageField('SqlQueryStep', 4)


class QueryStepAggregation(_messages.Message):
  r"""An identifier for an aggregation. Aggregations are used for cases where
  we need to collapse a set of values into a single value, such as multiple
  points in a measure into a single bin.

  Fields:
    parameters: Parameters to be applied to the aggregation. Aggregations that
      support or require parameters are listed above.
    type: Required. The type of aggregation to apply. Legal values for this
      string are: "percentile" - Generates an APPROX_QUANTILES. Requires one
      integer or double parameter. Applies only to numeric values. Supports
      precision of up to 3 decimal places. "average" - Generates AVG().
      Applies only to numeric values. "count" - Generates COUNT(). "count-
      distinct" - Generates COUNT(DISTINCT). "count-distinct-approx" -
      Generates APPROX_COUNT_DISTINCT(). "max" - Generates MAX(). Applies only
      to numeric values. "min" - Generates MIN(). Applies only to numeric
      values. "sum" - Generates SUM(). Applies only to numeric values. "none",
      "" - Equivalent to no aggregation.
  """

  parameters = _messages.MessageField('Parameter', 1, repeated=True)
  type = _messages.StringField(2)


class ReadQueryResultsRequest(_messages.Message):
  r"""Parameters to ReadQueryResults.

  Fields:
    pageSize: Optional. The maximum number of rows to return in the results.
      Responses are limited to 10 MB in size.By default, there is no maximum
      row count, and only the byte limit applies. When the byte limit is
      reached, the rest of query results will be paginated.
    pageToken: Optional. Page token returned by a previous call to
      ReadQueryResults to paginate through the response rows.
    queryStepHandle: Required. A query step handle returned by QueryData.
    readMetadataOnly: Optional. If this flag is true, no rows will be returned
      regardless of the value of page_size; the rows, total_rows, and
      next_page_token members of the response will be empty.
    resourceNames: Required. Names of one or more log views that were used in
      the original query.Example: projects/[PROJECT_ID]/locations/[LOCATION_ID
      ]/buckets/[BUCKET_ID]/views/[VIEW_ID]Requires appropriate permissions on
      each resource such as 'logging.views.access' on log view resources.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  queryStepHandle = _messages.StringField(3)
  readMetadataOnly = _messages.BooleanField(4)
  resourceNames = _messages.StringField(5, repeated=True)


class RedactLogEntriesImpact(_messages.Message):
  r"""Information about the impact of a redaction.

  Fields:
    endTime: The time impact assessment was completed.
    logEntriesCount: The number of entries in the requested bucket that match
      the requested filter.
    sqlQuery: The equivalent SQL query to the Logging Query Language filter
      provided by the user. Only populated for analytics-enabled buckets.
    userApprovalTime: The time the user's approval of the impact assessment
      was received. Empty if the impact assessment has not yet finished or the
      user's approval has not yet been given.
  """

  endTime = _messages.StringField(1)
  logEntriesCount = _messages.IntegerField(2)
  sqlQuery = _messages.StringField(3)
  userApprovalTime = _messages.StringField(4)


class RedactLogEntriesMetadata(_messages.Message):
  r"""Metadata for RedactLogEntries long running operations.

  Enums:
    StateValueValuesEnum: State of an operation.

  Fields:
    cancellationRequested: Identifies whether the user has requested
      cancellation of the operation.
    endTime: The time at which the operation completed.
    impactAssessment: The expected impact of the operation. If not set, impact
      has not been fully assessed.
    progress: Estimated progress of the operation (0 - 100%).
    receiveTime: The time at which the redaction request was received.
    request: RedactLogEntries RPC request.
    startTime: The time at which redaction of log entries commenced.
    state: State of an operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of an operation.

    Values:
      OPERATION_STATE_UNSPECIFIED: Should not be used.
      OPERATION_STATE_SCHEDULED: The operation is scheduled.
      OPERATION_STATE_WAITING_FOR_PERMISSIONS: Waiting for necessary
        permissions.
      OPERATION_STATE_RUNNING: The operation is running.
      OPERATION_STATE_SUCCEEDED: The operation was completed successfully.
      OPERATION_STATE_FAILED: The operation failed.
      OPERATION_STATE_CANCELLED: The operation was cancelled by the user.
    """
    OPERATION_STATE_UNSPECIFIED = 0
    OPERATION_STATE_SCHEDULED = 1
    OPERATION_STATE_WAITING_FOR_PERMISSIONS = 2
    OPERATION_STATE_RUNNING = 3
    OPERATION_STATE_SUCCEEDED = 4
    OPERATION_STATE_FAILED = 5
    OPERATION_STATE_CANCELLED = 6

  cancellationRequested = _messages.BooleanField(1)
  endTime = _messages.StringField(2)
  impactAssessment = _messages.MessageField('RedactLogEntriesImpact', 3)
  progress = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  receiveTime = _messages.StringField(5)
  request = _messages.MessageField('RedactLogEntriesRequest', 6)
  startTime = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)


class RedactLogEntriesRequest(_messages.Message):
  r"""The parameters to RedactLogEntries.

  Fields:
    filter: Required. A filter specifying which log entries to redact. The
      filter must be no more than 20k characters. An empty filter matches all
      log entries.
    name: Required. Log bucket from which to redact log entries.For
      example:"projects/my-project/locations/global/buckets/my-source-bucket"
    reason: Required. The reason log entries need to be redacted. This field
      will be recorded in redacted log entries and should omit sensitive
      information. The reason is limited 1,024 characters.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2)
  reason = _messages.StringField(3)


class RedactLogEntriesResponse(_messages.Message):
  r"""Response type for RedactLogEntries long running operations."""


class RequestLog(_messages.Message):
  r"""Complete log information about a single HTTP request to an App Engine
  application.

  Fields:
    appEngineRelease: App Engine release version.
    appId: Application that handled this request.
    cost: An indication of the relative cost of serving this request.
    endTime: Time when the request finished.
    finished: Whether this request is finished or active.
    first: Whether this is the first RequestLog entry for this request. If an
      active request has several RequestLog entries written to Stackdriver
      Logging, then this field will be set for one of them.
    host: Internet host and port number of the resource being requested.
    httpVersion: HTTP version of request. Example: "HTTP/1.1".
    instanceId: An identifier for the instance that handled the request.
    instanceIndex: If the instance processing this request belongs to a
      manually scaled module, then this is the 0-based index of the instance.
      Otherwise, this value is -1.
    ip: Origin IP address.
    latency: Latency of the request.
    line: A list of log lines emitted by the application while serving this
      request.
    megaCycles: Number of CPU megacycles used to process request.
    method: Request method. Example: "GET", "HEAD", "PUT", "POST", "DELETE".
    moduleId: Module of the application that handled this request.
    nickname: The logged-in user who made the request.Most likely, this is the
      part of the user's email before the @ sign. The field value is the same
      for different requests from the same user, but different users can have
      similar names. This information is also available to the application via
      the App Engine Users API.This field will be populated starting with App
      Engine 1.9.21.
    pendingTime: Time this request spent in the pending request queue.
    referrer: Referrer URL of request.
    requestId: Globally unique identifier for a request, which is based on the
      request start time. Request IDs for requests which started later will
      compare greater as strings than those for requests which started
      earlier.
    resource: Contains the path and query portion of the URL that was
      requested. For example, if the URL was
      "http://example.com/app?name=val", the resource would be
      "/app?name=val". The fragment identifier, which is identified by the #
      character, is not included.
    responseSize: Size in bytes sent back to client by request.
    sourceReference: Source code for the application that handled this
      request. There can be more than one source reference per deployed
      application if source code is distributed among multiple repositories.
    spanId: Stackdriver Trace span identifier for this request.
    startTime: Time when the request started.
    status: HTTP response status code. Example: 200, 404.
    taskName: Task name of the request, in the case of an offline request.
    taskQueueName: Queue name of the request, in the case of an offline
      request.
    traceId: Stackdriver Trace identifier for this request.
    traceSampled: If true, the value in the 'trace_id' field was sampled for
      storage in a trace backend.
    urlMapEntry: File or class that handled the request.
    userAgent: User agent that made the request.
    versionId: Version of the application that handled this request.
    wasLoadingRequest: Whether this was a loading request for the instance.
  """

  appEngineRelease = _messages.StringField(1)
  appId = _messages.StringField(2)
  cost = _messages.FloatField(3)
  endTime = _messages.StringField(4)
  finished = _messages.BooleanField(5)
  first = _messages.BooleanField(6)
  host = _messages.StringField(7)
  httpVersion = _messages.StringField(8)
  instanceId = _messages.StringField(9)
  instanceIndex = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  ip = _messages.StringField(11)
  latency = _messages.StringField(12)
  line = _messages.MessageField('LogLine', 13, repeated=True)
  megaCycles = _messages.IntegerField(14)
  method = _messages.StringField(15)
  moduleId = _messages.StringField(16)
  nickname = _messages.StringField(17)
  pendingTime = _messages.StringField(18)
  referrer = _messages.StringField(19)
  requestId = _messages.StringField(20)
  resource = _messages.StringField(21)
  responseSize = _messages.IntegerField(22)
  sourceReference = _messages.MessageField('SourceReference', 23, repeated=True)
  spanId = _messages.StringField(24)
  startTime = _messages.StringField(25)
  status = _messages.IntegerField(26, variant=_messages.Variant.INT32)
  taskName = _messages.StringField(27)
  taskQueueName = _messages.StringField(28)
  traceId = _messages.StringField(29)
  traceSampled = _messages.BooleanField(30)
  urlMapEntry = _messages.StringField(31)
  userAgent = _messages.StringField(32)
  versionId = _messages.StringField(33)
  wasLoadingRequest = _messages.BooleanField(34)


class RowCountThreshold(_messages.Message):
  r"""A threshold condition that compares the row count to a threshold. Ex.
  COUNT(*) > 10

  Fields:
    trigger: Optional. The number/percent of rows that must exceed the
      threshold in order for this result set (partition set) to be considered
      in violation. If unspecified, then the result set (partition set) will
      be in violation when a single row violates the threshold.
  """

  trigger = _messages.MessageField('Trigger', 1)


class Settings(_messages.Message):
  r"""Describes the settings associated with a project, folder, organization,
  billing account, or flexible resource.

  Fields:
    disableDefaultSink: Optional. If set to true, the _Default sink in newly
      created projects and folders will created in a disabled state. This can
      be used to automatically disable log storage if there is already an
      aggregated sink configured in the hierarchy. The _Default sink can be
      re-enabled manually if needed.
    kmsKeyName: Optional. The resource name for the configured Cloud KMS
      key.KMS key name format: "projects/[PROJECT_ID]/locations/[LOCATION]/key
      Rings/[KEYRING]/cryptoKeys/[KEY]" For example:"projects/my-
      project/locations/us-central1/keyRings/my-ring/cryptoKeys/my-key"To
      enable CMEK for the Log Router, set this field to a valid kms_key_name
      for which the associated service account has the required
      roles/cloudkms.cryptoKeyEncrypterDecrypter role assigned for the key.The
      Cloud KMS key used by the Log Router can be updated by changing the
      kms_key_name to a new valid key name. Encryption operations that are in
      progress will be completed with the key that was in use when they
      started. Decryption operations will be completed using the key that was
      used at the time of encryption unless access to that key has been
      revoked.To disable CMEK for the Log Router, set this field to an empty
      string.See Enabling CMEK for Log Router
      (https://cloud.google.com/logging/docs/routing/managed-encryption) for
      more information.
    kmsServiceAccountId: Output only. The service account that will be used by
      the Log Router to access your Cloud KMS key.Before enabling CMEK for Log
      Router, you must first assign the role
      roles/cloudkms.cryptoKeyEncrypterDecrypter to the service account that
      the Log Router will use to access your Cloud KMS key. Use GetSettings to
      obtain the service account ID.See Enabling CMEK for Log Router
      (https://cloud.google.com/logging/docs/routing/managed-encryption) for
      more information.
    loggingServiceAccountId: Output only. The service account for the given
      container. Sinks use this service account as their writer_identity if no
      custom service account is provided.
    name: Output only. The resource name of the settings.
    storageLocation: Optional. The storage location that Cloud Logging will
      use to create new resources when a location is needed but not explicitly
      provided. The use cases includes: The location of _Default and _Required
      log bucket for newly created projects and folders.Example value: europe-
      west1.Note: this setting does not affect the location of resources where
      a location is explicitly provided when created, such as custom log
      buckets.
  """

  disableDefaultSink = _messages.BooleanField(1)
  kmsKeyName = _messages.StringField(2)
  kmsServiceAccountId = _messages.StringField(3)
  loggingServiceAccountId = _messages.StringField(4)
  name = _messages.StringField(5)
  storageLocation = _messages.StringField(6)


class SourceLocation(_messages.Message):
  r"""Specifies a location in a source code file.

  Fields:
    file: Source file name. Depending on the runtime environment, this might
      be a simple name or a fully-qualified name.
    functionName: Human-readable name of the function or method being invoked,
      with optional context such as the class or package name. This
      information is used in contexts such as the logs viewer, where a file
      and line number are less meaningful. The format can vary by language.
      For example: qual.if.ied.Class.method (Java), dir/package.func (Go),
      function (Python).
    line: Line within the source file.
  """

  file = _messages.StringField(1)
  functionName = _messages.StringField(2)
  line = _messages.IntegerField(3)


class SourceReference(_messages.Message):
  r"""A reference to a particular snapshot of the source tree used to build
  and deploy an application.

  Fields:
    repository: Optional. A URI string identifying the repository. Example:
      "https://github.com/GoogleCloudPlatform/kubernetes.git"
    revisionId: The canonical and persistent identifier of the deployed
      revision. Example (git): "0035781c50ec7aa23385dc841529ce8a4b70db1b"
  """

  repository = _messages.StringField(1)
  revisionId = _messages.StringField(2)


class SqlQueryStep(_messages.Message):
  r"""A query step defined in raw SQL.

  Fields:
    parameters: Optional. Parameters to be injected into the query at
      execution time.
    queryRestriction: Optional. Restrictions being requested, e.g. timerange
      restrictions.
    sqlQuery: Required. A query string, following the BigQuery SQL query
      syntax. The FROM clause should specify a fully qualified log view
      corresponding to the log view in the resource_names in dot separated
      format like PROJECT_ID.LOCATION_ID.BUCKET_ID.VIEW_ID.For example: SELECT
      count(*) FROM my_project.us.my_bucket._AllLogs;If any of the dot
      separated components have special characters, that component needs to be
      escaped separately like the following example:SELECT count(*) FROM
      company.com:abc.us.my-bucket._AllLogs;
  """

  parameters = _messages.MessageField('QueryParameter', 1, repeated=True)
  queryRestriction = _messages.MessageField('QueryRestriction', 2)
  sqlQuery = _messages.StringField(3)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The Status type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by gRPC (https://github.com/grpc). Each Status message contains three
  pieces of data: error code, error message, and error details.You can find
  out more about this error model and how to work with it in the API Design
  Guide (https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StringArrayValue(_messages.Message):
  r"""An array of strings within a parameter.

  Fields:
    values: The values of the array.
  """

  values = _messages.StringField(1, repeated=True)


class StringTest(_messages.Message):
  r"""A test that compares a string column against a string to match. NOTE:
  StringTest is not yet supported.

  Enums:
    ComparisonValueValuesEnum: Required. The comparison operator to use.

  Fields:
    column: Required. The column that contains the strings we want to search
      on.
    comparison: Required. The comparison operator to use.
    pattern: Required. The string or regular expression which is compared to
      the value in the column.
    trigger: Optional. The number/percent of rows that must match in order for
      the result set (partition set) to be considered in violation. If
      unspecified, then the result set (partition set) will be in violation if
      a single row matches.
  """

  class ComparisonValueValuesEnum(_messages.Enum):
    r"""Required. The comparison operator to use.

    Values:
      STRING_COMPARISON_TYPE_UNSPECIFIED: No string comparison specified,
        should never happen.
      STRING_COMPARISON_MATCH: String column must equal the pattern.
      STRING_COMPARISON_NOT_MATCH: String column must not equal the pattern.
      STRING_COMPARISON_CONTAINS: String contains contains the pattern as a
        substring.
      STRING_COMPARISON_NOT_CONTAINS: String column does not contain the
        pattern as a substring.
      STRING_COMPARISON_REGEX_MATCH: Regular expression pattern found in
        string column.
      STRING_COMPARISON_REGEX_NOT_MATCH: Regular expression pattern not found
        in string column.
    """
    STRING_COMPARISON_TYPE_UNSPECIFIED = 0
    STRING_COMPARISON_MATCH = 1
    STRING_COMPARISON_NOT_MATCH = 2
    STRING_COMPARISON_CONTAINS = 3
    STRING_COMPARISON_NOT_CONTAINS = 4
    STRING_COMPARISON_REGEX_MATCH = 5
    STRING_COMPARISON_REGEX_NOT_MATCH = 6

  column = _messages.StringField(1)
  comparison = _messages.EnumField('ComparisonValueValuesEnum', 2)
  pattern = _messages.StringField(3)
  trigger = _messages.MessageField('Trigger', 4)


class SuppressionInfo(_messages.Message):
  r"""Information about entries that were omitted from the session.

  Enums:
    ReasonValueValuesEnum: The reason that entries were omitted from the
      session.

  Fields:
    reason: The reason that entries were omitted from the session.
    suppressedCount: A lower bound on the count of entries omitted due to
      reason.
  """

  class ReasonValueValuesEnum(_messages.Enum):
    r"""The reason that entries were omitted from the session.

    Values:
      REASON_UNSPECIFIED: Unexpected default.
      RATE_LIMIT: Indicates suppression occurred due to relevant entries being
        received in excess of rate limits. For quotas and limits, see Logging
        API quotas and limits (https://cloud.google.com/logging/quotas#api-
        limits).
      NOT_CONSUMED: Indicates suppression occurred due to the client not
        consuming responses quickly enough.
    """
    REASON_UNSPECIFIED = 0
    RATE_LIMIT = 1
    NOT_CONSUMED = 2

  reason = _messages.EnumField('ReasonValueValuesEnum', 1)
  suppressedCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class TableFieldSchema(_messages.Message):
  r"""A field in TableSchema. The fields describe the static fields in the
  LogEntry. Any dynamic fields generated by the customer in fields like labels
  and jsonPayload are not listed in the schema as they use a native JSON type
  field.

  Fields:
    description: Optional. The field description.
    fields: Optional. Describes the nested schema fields if the type property
      is set to RECORD.
    mode: Optional. The field mode. Possible values include NULLABLE, REQUIRED
      and REPEATED. The default value is NULLABLE.
    name: Required. The field name corresponding to fields in the LogEntry.
    type: Required. The field data type. Possible values include: STRING
      INTEGER (or INT64) FLOAT (or FLOAT64) BOOLEAN (or BOOL) TIMESTAMP RECORD
      (or STRUCT)Use of RECORD/STRUCT indicates that the field contains a
      nested schema.
  """

  description = _messages.StringField(1)
  fields = _messages.MessageField('TableFieldSchema', 2, repeated=True)
  mode = _messages.StringField(3)
  name = _messages.StringField(4)
  type = _messages.StringField(5)


class TableSchema(_messages.Message):
  r"""Schema of a table containing logs.

  Fields:
    fields: Describes the fields in a table.
  """

  fields = _messages.MessageField('TableFieldSchema', 1, repeated=True)


class TailLogEntriesRequest(_messages.Message):
  r"""The parameters to TailLogEntries.

  Fields:
    bufferWindow: Optional. The amount of time to buffer log entries at the
      server before being returned to prevent out of order results due to late
      arriving log entries. Valid values are between 0-60000 milliseconds.
      Defaults to 2000 milliseconds.
    filter: Optional. Only log entries that match the filter are returned. An
      empty filter matches all log entries in the resources listed in
      resource_names. Referencing a parent resource that is not listed in
      resource_names will cause the filter to return no results. The maximum
      length of a filter is 20,000 characters.
    resourceNames: Required. Name of a parent resource from which to retrieve
      log entries: projects/[PROJECT_ID] organizations/[ORGANIZATION_ID]
      billingAccounts/[BILLING_ACCOUNT_ID] folders/[FOLDER_ID]May
      alternatively be one or more views: projects/[PROJECT_ID]/locations/[LOC
      ATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID] organizations/[ORGANIZATIO
      N_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID] billin
      gAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_I
      D]/views/[VIEW_ID] folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[
      BUCKET_ID]/views/[VIEW_ID]
  """

  bufferWindow = _messages.StringField(1)
  filter = _messages.StringField(2)
  resourceNames = _messages.StringField(3, repeated=True)


class TailLogEntriesResponse(_messages.Message):
  r"""Result returned from TailLogEntries.

  Fields:
    entries: A list of log entries. Each response in the stream will order
      entries with increasing values of LogEntry.timestamp. Ordering is not
      guaranteed between separate responses.
    suppressionInfo: If entries that otherwise would have been included in the
      session were not sent back to the client, counts of relevant entries
      omitted from the session with the reason that they were not included.
      There will be at most one of each reason per response. The counts
      represent the number of suppressed entries since the last streamed
      response.
  """

  entries = _messages.MessageField('LogEntry', 1, repeated=True)
  suppressionInfo = _messages.MessageField('SuppressionInfo', 2, repeated=True)


class ThresholdTest(_messages.Message):
  r"""A test that compares some LHS against a threshold. NOTE: Only
  RowCountThreshold is currently supported.

  Enums:
    ComparisonValueValuesEnum: Required. The comparison to be applied in the
      __alert_result condition.

  Fields:
    aggregateValueThreshold: A value threshold comparison that includes an
      aggregation of the value column.
    comparison: Required. The comparison to be applied in the __alert_result
      condition.
    rowCountThreshold: A threshold based on the number of rows present.
    threshold: Required. The threshold that will be used as the RHS of a
      comparison.
    valueThreshold: A value threshold comparison.
  """

  class ComparisonValueValuesEnum(_messages.Enum):
    r"""Required. The comparison to be applied in the __alert_result
    condition.

    Values:
      COMPARISON_TYPE_UNSPECIFIED: No comparison relationship is specified.
      COMPARISON_GT: True if the aggregate / value_column is greater than the
        threshold.
      COMPARISON_GE: True if the aggregate / value_column is greater than or
        equal to the threshold.
      COMPARISON_LT: True if the aggregate / value_column is less than the
        threshold.
      COMPARISON_LE: True if the aggregate / value_column is less than or
        equal to the threshold.
      COMPARISON_EQ: True if the aggregate / value_column is equal to the
        threshold.
      COMPARISON_NE: True if the aggregate / value_column is not equal to the
        threshold.
    """
    COMPARISON_TYPE_UNSPECIFIED = 0
    COMPARISON_GT = 1
    COMPARISON_GE = 2
    COMPARISON_LT = 3
    COMPARISON_LE = 4
    COMPARISON_EQ = 5
    COMPARISON_NE = 6

  aggregateValueThreshold = _messages.MessageField('AggregateValueThreshold', 1)
  comparison = _messages.EnumField('ComparisonValueValuesEnum', 2)
  rowCountThreshold = _messages.MessageField('RowCountThreshold', 3)
  threshold = _messages.FloatField(4)
  valueThreshold = _messages.MessageField('ValueThreshold', 5)


class Trigger(_messages.Message):
  r"""A restriction on the alert test to require a certain count or percent of
  rows to be present.

  Fields:
    count: Optional. The absolute number of time series that must fail the
      predicate for the test to be triggered.
    percent: Optional. The percentage of time series that must fail the
      predicate for the test to be triggered.
  """

  count = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  percent = _messages.FloatField(2)


class UndeleteBucketRequest(_messages.Message):
  r"""The parameters to UndeleteBucket."""


class UpdateBucketRequest(_messages.Message):
  r"""The parameters to UpdateBucket.

  Fields:
    bucket: Required. The updated bucket.
    name: Required. The full resource name of the bucket to update.
      "projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" "org
      anizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]
      " "billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/
      [BUCKET_ID]"
      "folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]" For
      example:"projects/my-project/locations/global/buckets/my-bucket"
    updateMask: Required. Field mask that specifies the fields in bucket that
      need an update. A bucket field will be overwritten if, and only if, it
      is in the update mask. name and output only fields cannot be updated.For
      a detailed FieldMask definition, see:
      https://developers.google.com/protocol-
      buffers/docs/reference/google.protobuf#google.protobuf.FieldMaskFor
      example: updateMask=retention_days
  """

  bucket = _messages.MessageField('LogBucket', 1)
  name = _messages.StringField(2)
  updateMask = _messages.StringField(3)


class ValidateQueryResponse(_messages.Message):
  r"""The response data from ValidateQuery.

  Fields:
    validateResult: The operation does basic syntactic validation on all steps
      and will return an error if an issue is found. Only the first query step
      is validated through BigQuery, however, and only if it's a SqlQueryStep.
      If the first step is not SQL, this field will be empty.
  """

  validateResult = _messages.MessageField('QueryResults', 1)


class ValueThreshold(_messages.Message):
  r"""A threshold condition that compares a value to a threshold.

  Fields:
    trigger: Optional. The number/percent of rows that must exceed the
      threshold in order for this result set (partition set) to be considered
      in violation. If unspecified, then the result set (partition set) will
      be in violation when a single row violates the threshold.
    valueColumn: Required. The column to compare the threshold against.
  """

  trigger = _messages.MessageField('Trigger', 1)
  valueColumn = _messages.StringField(2)


class WriteLogEntriesRequest(_messages.Message):
  r"""The parameters to WriteLogEntries.

  Messages:
    LabelsValue: Optional. Default labels that are added to the labels field
      of all log entries in entries. If a log entry already has a label with
      the same key as a label in this parameter, then the log entry's label is
      not changed. See LogEntry.

  Fields:
    dryRun: Optional. If true, the request should expect normal response, but
      the entries won't be persisted nor exported. Useful for checking whether
      the logging API endpoints are working properly before sending valuable
      data.
    entries: Required. The log entries to send to Logging. The order of log
      entries in this list does not matter. Values supplied in this method's
      log_name, resource, and labels fields are copied into those log entries
      in this list that do not include values for their corresponding fields.
      For more information, see the LogEntry type.If the timestamp or
      insert_id fields are missing in log entries, then this method supplies
      the current time or a unique identifier, respectively. The supplied
      values are chosen so that, among the log entries that did not supply
      their own values, the entries earlier in the list will sort before the
      entries later in the list. See the entries.list method.Log entries with
      timestamps that are more than the logs retention period
      (https://cloud.google.com/logging/quotas) in the past or more than 24
      hours in the future will not be available when calling entries.list.
      However, those log entries can still be exported with LogSinks
      (https://cloud.google.com/logging/docs/api/tasks/exporting-logs).To
      improve throughput and to avoid exceeding the quota limit
      (https://cloud.google.com/logging/quotas) for calls to entries.write,
      you should try to include several log entries in this list, rather than
      calling this method for each individual log entry.
    labels: Optional. Default labels that are added to the labels field of all
      log entries in entries. If a log entry already has a label with the same
      key as a label in this parameter, then the log entry's label is not
      changed. See LogEntry.
    logName: Optional. A default log resource name that is assigned to all log
      entries in entries that do not specify a value for log_name:
      projects/[PROJECT_ID]/logs/[LOG_ID]
      organizations/[ORGANIZATION_ID]/logs/[LOG_ID]
      billingAccounts/[BILLING_ACCOUNT_ID]/logs/[LOG_ID]
      folders/[FOLDER_ID]/logs/[LOG_ID][LOG_ID] must be URL-encoded. For
      example: "projects/my-project-id/logs/syslog"
      "organizations/123/logs/cloudaudit.googleapis.com%2Factivity" The
      permission logging.logEntries.create is needed on each project,
      organization, billing account, or folder that is receiving new log
      entries, whether the resource is specified in logName or in an
      individual log entry.
    partialSuccess: Optional. Whether a batch's valid entries should be
      written even if some other entry failed due to a permanent error such as
      INVALID_ARGUMENT or PERMISSION_DENIED. If any entry failed, then the
      response status is the response status of one of the failed entries. The
      response will include error details in
      WriteLogEntriesPartialErrors.log_entry_errors keyed by the entries'
      zero-based index in the entries. Failed requests for which no entries
      are written will not include per-entry errors.
    resource: Optional. A default monitored resource object that is assigned
      to all log entries in entries that do not specify a value for resource.
      Example: { "type": "gce_instance", "labels": { "zone": "us-central1-a",
      "instance_id": "00000000000000000000" }} See LogEntry.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Default labels that are added to the labels field of all log
    entries in entries. If a log entry already has a label with the same key
    as a label in this parameter, then the log entry's label is not changed.
    See LogEntry.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  dryRun = _messages.BooleanField(1)
  entries = _messages.MessageField('LogEntry', 2, repeated=True)
  labels = _messages.MessageField('LabelsValue', 3)
  logName = _messages.StringField(4)
  partialSuccess = _messages.BooleanField(5)
  resource = _messages.MessageField('MonitoredResource', 6)


class WriteLogEntriesResponse(_messages.Message):
  r"""Result returned from WriteLogEntries."""


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

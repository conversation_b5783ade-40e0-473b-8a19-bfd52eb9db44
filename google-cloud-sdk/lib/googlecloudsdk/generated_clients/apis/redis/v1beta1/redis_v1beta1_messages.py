"""Generated message classes for redis version v1beta1.

Creates and manages Redis instances on the Google Cloud Platform.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'redis'


class CertChain(_messages.Message):
  r"""A CertChain object.

  Fields:
    certificates: The certificates that form the CA chain, from leaf to root
      order.
  """

  certificates = _messages.StringField(1, repeated=True)


class CertificateAuthority(_messages.Message):
  r"""Redis cluster certificate authority

  Fields:
    managedServerCa: A ManagedCertificateAuthority attribute.
  """

  managedServerCa = _messages.MessageField('ManagedCertificateAuthority', 1)


class Cluster(_messages.Message):
  r"""A cluster instance.

  Enums:
    AuthorizationModeValueValuesEnum: Optional. The authorization mode of the
      Redis cluster. If not provided, auth feature is disabled for the
      cluster.
    StateValueValuesEnum: Output only. The current state of this cluster. Can
      be CREATING, READY, UPDATING, DELETING and SUSPENDED
    TransitEncryptionModeValueValuesEnum: Optional. The in-transit encryption
      for the Redis cluster. If not provided, encryption is disabled for the
      cluster.

  Fields:
    authorizationMode: Optional. The authorization mode of the Redis cluster.
      If not provided, auth feature is disabled for the cluster.
    createTime: Output only. The timestamp associated with the cluster
      creation request.
    discoveryEndpoints: Output only. Endpoints created on each given network,
      for Redis clients to connect to the cluster. Currently only one
      discovery endpoint is supported.
    name: Required. Unique name of the resource in this scope including
      project and location using the form:
      `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`
    pscConfigs: Required. Each PscConfig configures the consumer network where
      IPs will be designated to the cluster for client access through Private
      Service Connect Automation. Currently, only one PscConfig is supported.
    pscConnections: Output only. PSC connections for discovery of the cluster
      topology and accessing the cluster.
    replicaCount: Optional. The number of replica nodes per shard.
    shardCount: Required. Number of shards for the Redis cluster.
    sizeGb: Output only. Redis memory size in GB for the entire cluster.
    state: Output only. The current state of this cluster. Can be CREATING,
      READY, UPDATING, DELETING and SUSPENDED
    stateInfo: Output only. Additional information about the current state of
      the cluster.
    transitEncryptionMode: Optional. The in-transit encryption for the Redis
      cluster. If not provided, encryption is disabled for the cluster.
    uid: Output only. System assigned, unique identifier for the cluster.
  """

  class AuthorizationModeValueValuesEnum(_messages.Enum):
    r"""Optional. The authorization mode of the Redis cluster. If not
    provided, auth feature is disabled for the cluster.

    Values:
      AUTH_MODE_UNSPECIFIED: Not set.
      AUTH_MODE_IAM_AUTH: IAM basic authorization mode
      AUTH_MODE_DISABLED: Authorization disabled mode
    """
    AUTH_MODE_UNSPECIFIED = 0
    AUTH_MODE_IAM_AUTH = 1
    AUTH_MODE_DISABLED = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of this cluster. Can be CREATING,
    READY, UPDATING, DELETING and SUSPENDED

    Values:
      STATE_UNSPECIFIED: Not set.
      CREATING: Redis cluster is being created.
      ACTIVE: Redis cluster has been created and is fully usable.
      UPDATING: Redis cluster configuration is being updated.
      DELETING: Redis cluster is being deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    UPDATING = 3
    DELETING = 4

  class TransitEncryptionModeValueValuesEnum(_messages.Enum):
    r"""Optional. The in-transit encryption for the Redis cluster. If not
    provided, encryption is disabled for the cluster.

    Values:
      TRANSIT_ENCRYPTION_MODE_UNSPECIFIED: In-transit encryption not set.
      TRANSIT_ENCRYPTION_MODE_DISABLED: In-transit encryption disabled.
      TRANSIT_ENCRYPTION_MODE_SERVER_AUTHENTICATION: Use server managed
        encryption for in-transit encryption.
    """
    TRANSIT_ENCRYPTION_MODE_UNSPECIFIED = 0
    TRANSIT_ENCRYPTION_MODE_DISABLED = 1
    TRANSIT_ENCRYPTION_MODE_SERVER_AUTHENTICATION = 2

  authorizationMode = _messages.EnumField('AuthorizationModeValueValuesEnum', 1)
  createTime = _messages.StringField(2)
  discoveryEndpoints = _messages.MessageField('DiscoveryEndpoint', 3, repeated=True)
  name = _messages.StringField(4)
  pscConfigs = _messages.MessageField('PscConfig', 5, repeated=True)
  pscConnections = _messages.MessageField('PscConnection', 6, repeated=True)
  replicaCount = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  shardCount = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  sizeGb = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  stateInfo = _messages.MessageField('StateInfo', 11)
  transitEncryptionMode = _messages.EnumField('TransitEncryptionModeValueValuesEnum', 12)
  uid = _messages.StringField(13)


class DiscoveryEndpoint(_messages.Message):
  r"""Endpoints on each network, for Redis clients to connect to the cluster.

  Fields:
    address: Output only. Address of the exposed Redis endpoint used by
      clients to connect to the service. The address could be either IP or
      hostname.
    port: Output only. The port number of the exposed Redis endpoint.
  """

  address = _messages.StringField(1)
  port = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ExportInstanceRequest(_messages.Message):
  r"""Request for Export.

  Fields:
    outputConfig: Required. Specify data to be exported.
  """

  outputConfig = _messages.MessageField('OutputConfig', 1)


class FailoverInstanceRequest(_messages.Message):
  r"""Request for Failover.

  Enums:
    DataProtectionModeValueValuesEnum: Optional. Available data protection
      modes that the user can choose. If it's unspecified, data protection
      mode will be LIMITED_DATA_LOSS by default.

  Fields:
    dataProtectionMode: Optional. Available data protection modes that the
      user can choose. If it's unspecified, data protection mode will be
      LIMITED_DATA_LOSS by default.
  """

  class DataProtectionModeValueValuesEnum(_messages.Enum):
    r"""Optional. Available data protection modes that the user can choose. If
    it's unspecified, data protection mode will be LIMITED_DATA_LOSS by
    default.

    Values:
      DATA_PROTECTION_MODE_UNSPECIFIED: Defaults to LIMITED_DATA_LOSS if a
        data protection mode is not specified.
      LIMITED_DATA_LOSS: Instance failover will be protected with data loss
        control. More specifically, the failover will only be performed if the
        current replication offset diff between primary and replica is under a
        certain threshold.
      FORCE_DATA_LOSS: Instance failover will be performed without data loss
        control.
    """
    DATA_PROTECTION_MODE_UNSPECIFIED = 0
    LIMITED_DATA_LOSS = 1
    FORCE_DATA_LOSS = 2

  dataProtectionMode = _messages.EnumField('DataProtectionModeValueValuesEnum', 1)


class GcsDestination(_messages.Message):
  r"""The Cloud Storage location for the output content

  Fields:
    uri: Required. Data destination URI (e.g. 'gs://my_bucket/my_object').
      Existing files will be overwritten.
  """

  uri = _messages.StringField(1)


class GcsSource(_messages.Message):
  r"""The Cloud Storage location for the input content

  Fields:
    uri: Required. Source data URI. (e.g. 'gs://my_bucket/my_object').
  """

  uri = _messages.StringField(1)


class GoogleCloudCommonOperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    cancelRequested: Output only. Identifies whether the user has requested
      cancellation of the operation. Operations that have been cancelled
      successfully have Operation.error value with a google.rpc.Status.code of
      1, corresponding to `Code.CANCELLED`.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    statusDetail: Output only. Human-readable status of the operation, if any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class GoogleCloudRedisV1beta1LocationMetadata(_messages.Message):
  r"""This location metadata represents additional configuration options for a
  given location where a Redis instance may be created. All fields are output
  only. It is returned as content of the
  `google.cloud.location.Location.metadata` field.

  Messages:
    AvailableZonesValue: Output only. The set of available zones in the
      location. The map is keyed by the lowercase ID of each zone, as defined
      by GCE. These keys can be specified in `location_id` or
      `alternative_location_id` fields when creating a Redis instance.

  Fields:
    availableZones: Output only. The set of available zones in the location.
      The map is keyed by the lowercase ID of each zone, as defined by GCE.
      These keys can be specified in `location_id` or
      `alternative_location_id` fields when creating a Redis instance.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AvailableZonesValue(_messages.Message):
    r"""Output only. The set of available zones in the location. The map is
    keyed by the lowercase ID of each zone, as defined by GCE. These keys can
    be specified in `location_id` or `alternative_location_id` fields when
    creating a Redis instance.

    Messages:
      AdditionalProperty: An additional property for a AvailableZonesValue
        object.

    Fields:
      additionalProperties: Additional properties of type AvailableZonesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AvailableZonesValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudRedisV1beta1ZoneMetadata attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudRedisV1beta1ZoneMetadata', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  availableZones = _messages.MessageField('AvailableZonesValue', 1)


class GoogleCloudRedisV1beta1ZoneMetadata(_messages.Message):
  r"""Defines specific information for a particular zone. Currently empty and
  reserved for future use only.
  """



class ImportInstanceRequest(_messages.Message):
  r"""Request for Import.

  Fields:
    inputConfig: Required. Specify data to be imported.
  """

  inputConfig = _messages.MessageField('InputConfig', 1)


class InputConfig(_messages.Message):
  r"""The input content

  Fields:
    gcsSource: Google Cloud Storage location where input content is located.
  """

  gcsSource = _messages.MessageField('GcsSource', 1)


class Instance(_messages.Message):
  r"""A Memorystore for Redis instance.

  Enums:
    ConnectModeValueValuesEnum: Optional. The network connect mode of the
      Redis instance. If not provided, the connect mode defaults to
      DIRECT_PEERING.
    ReadReplicasModeValueValuesEnum: Optional. Read replicas mode for the
      instance. Defaults to READ_REPLICAS_DISABLED.
    StateValueValuesEnum: Output only. The current state of this instance.
    SuspensionReasonsValueListEntryValuesEnum:
    TierValueValuesEnum: Required. The service tier of the instance.
    TransitEncryptionModeValueValuesEnum: Optional. The TLS mode of the Redis
      instance. If not provided, TLS is disabled for the instance.

  Messages:
    LabelsValue: Resource labels to represent user provided metadata
    RedisConfigsValue: Optional. Redis configuration parameters, according to
      http://redis.io/topics/config. Currently, the only supported parameters
      are: Redis version 3.2 and newer: * maxmemory-policy * notify-keyspace-
      events Redis version 4.0 and newer: * activedefrag * lfu-decay-time *
      lfu-log-factor * maxmemory-gb Redis version 5.0 and newer: * stream-
      node-max-bytes * stream-node-max-entries

  Fields:
    alternativeLocationId: Optional. If specified, at least one node will be
      provisioned in this zone in addition to the zone specified in
      location_id. Only applicable to standard tier. If provided, it must be a
      different zone from the one provided in [location_id]. Additional nodes
      beyond the first 2 will be placed in zones selected by the service.
    authEnabled: Optional. Indicates whether OSS Redis AUTH is enabled for the
      instance. If set to "true" AUTH is enabled on the instance. Default
      value is "false" meaning AUTH is disabled.
    authorizedNetwork: Optional. The full name of the Google Compute Engine
      [network](https://cloud.google.com/vpc/docs/vpc) to which the instance
      is connected. If left unspecified, the `default` network will be used.
    availableMaintenanceVersions: Optional. The available maintenance versions
      that an instance could update to.
    connectMode: Optional. The network connect mode of the Redis instance. If
      not provided, the connect mode defaults to DIRECT_PEERING.
    createTime: Output only. The time the instance was created.
    currentLocationId: Output only. The current zone where the Redis primary
      node is located. In basic tier, this will always be the same as
      [location_id]. In standard tier, this can be the zone of any node in the
      instance.
    customerManagedKey: Optional. The KMS key reference that the customer
      provides when trying to create the instance.
    displayName: An arbitrary and optional user-provided name for the
      instance.
    host: Output only. Hostname or IP address of the exposed Redis endpoint
      used by clients to connect to the service.
    labels: Resource labels to represent user provided metadata
    locationId: Optional. The zone where the instance will be provisioned. If
      not provided, the service will choose a zone from the specified region
      for the instance. For standard tier, additional nodes will be added
      across multiple zones for protection against zonal failures. If
      specified, at least one node will be provisioned in this zone.
    maintenancePolicy: Optional. The maintenance policy for the instance. If
      not provided, maintenance events can be performed at any time.
    maintenanceSchedule: Output only. Date and time of upcoming maintenance
      events which have been scheduled.
    maintenanceVersion: Optional. The self service update maintenance version.
      The version is date based such as "20210712_00_00".
    memorySizeGb: Required. Redis memory size in GiB.
    name: Required. Unique name of the resource in this scope including
      project and location using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      Note: Redis instances are managed and addressed at regional level so
      location_id here refers to a GCP region; however, users may choose which
      specific zone (or collection of zones for cross-zone instances) an
      instance should be provisioned in. Refer to location_id and
      alternative_location_id fields for more details.
    nodes: Output only. Info per node.
    persistenceConfig: Optional. Persistence configuration parameters
    persistenceIamIdentity: Output only. Cloud IAM identity used by import /
      export operations to transfer data to/from Cloud Storage. Format is
      "serviceAccount:". The value may change over time for a given instance
      so should be checked before each import/export operation.
    port: Output only. The port number of the exposed Redis endpoint.
    readEndpoint: Output only. Hostname or IP address of the exposed readonly
      Redis endpoint. Standard tier only. Targets all healthy replica nodes in
      instance. Replication is asynchronous and replica nodes will exhibit
      some lag behind the primary. Write requests must target 'host'.
    readEndpointPort: Output only. The port number of the exposed readonly
      redis endpoint. Standard tier only. Write requests should target 'port'.
    readReplicasMode: Optional. Read replicas mode for the instance. Defaults
      to READ_REPLICAS_DISABLED.
    redisConfigs: Optional. Redis configuration parameters, according to
      http://redis.io/topics/config. Currently, the only supported parameters
      are: Redis version 3.2 and newer: * maxmemory-policy * notify-keyspace-
      events Redis version 4.0 and newer: * activedefrag * lfu-decay-time *
      lfu-log-factor * maxmemory-gb Redis version 5.0 and newer: * stream-
      node-max-bytes * stream-node-max-entries
    redisVersion: Optional. The version of Redis software. If not provided,
      latest supported version will be used. Currently, the supported values
      are: * `REDIS_3_2` for Redis 3.2 compatibility * `REDIS_4_0` for Redis
      4.0 compatibility (default) * `REDIS_5_0` for Redis 5.0 compatibility *
      `REDIS_6_X` for Redis 6.x compatibility
    replicaCount: Optional. The number of replica nodes. The valid range for
      the Standard Tier with read replicas enabled is [1-5] and defaults to 2.
      If read replicas are not enabled for a Standard Tier instance, the only
      valid value is 1 and the default is 1. The valid value for basic tier is
      0 and the default is also 0.
    reservedIpRange: Optional. For DIRECT_PEERING mode, the CIDR range of
      internal addresses that are reserved for this instance. Range must be
      unique and non-overlapping with existing subnets in an authorized
      network. For PRIVATE_SERVICE_ACCESS mode, the name of one allocated IP
      address ranges associated with this private service access connection.
      If not provided, the service will choose an unused /29 block, for
      example, 10.0.0.0/29 or ***********/29. For READ_REPLICAS_ENABLED the
      default block size is /28.
    secondaryIpRange: Optional. Additional IP range for node placement.
      Required when enabling read replicas on an existing instance. For
      DIRECT_PEERING mode value must be a CIDR range of size /28, or "auto".
      For PRIVATE_SERVICE_ACCESS mode value must be the name of an allocated
      address range associated with the private service access connection, or
      "auto".
    serverCaCerts: Output only. List of server CA certificates for the
      instance.
    state: Output only. The current state of this instance.
    statusMessage: Output only. Additional information about the current
      status of this instance, if available.
    suspensionReasons: Optional. reasons that causes instance in "SUSPENDED"
      state.
    tier: Required. The service tier of the instance.
    transitEncryptionMode: Optional. The TLS mode of the Redis instance. If
      not provided, TLS is disabled for the instance.
  """

  class ConnectModeValueValuesEnum(_messages.Enum):
    r"""Optional. The network connect mode of the Redis instance. If not
    provided, the connect mode defaults to DIRECT_PEERING.

    Values:
      CONNECT_MODE_UNSPECIFIED: Not set.
      DIRECT_PEERING: Connect via direct peering to the Memorystore for Redis
        hosted service.
      PRIVATE_SERVICE_ACCESS: Connect your Memorystore for Redis instance
        using Private Service Access. Private services access provides an IP
        address range for multiple Google Cloud services, including
        Memorystore.
    """
    CONNECT_MODE_UNSPECIFIED = 0
    DIRECT_PEERING = 1
    PRIVATE_SERVICE_ACCESS = 2

  class ReadReplicasModeValueValuesEnum(_messages.Enum):
    r"""Optional. Read replicas mode for the instance. Defaults to
    READ_REPLICAS_DISABLED.

    Values:
      READ_REPLICAS_MODE_UNSPECIFIED: If not set, Memorystore Redis backend
        will default to READ_REPLICAS_DISABLED.
      READ_REPLICAS_DISABLED: If disabled, read endpoint will not be provided
        and the instance cannot scale up or down the number of replicas.
      READ_REPLICAS_ENABLED: If enabled, read endpoint will be provided and
        the instance can scale up and down the number of replicas. Not valid
        for basic tier.
    """
    READ_REPLICAS_MODE_UNSPECIFIED = 0
    READ_REPLICAS_DISABLED = 1
    READ_REPLICAS_ENABLED = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of this instance.

    Values:
      STATE_UNSPECIFIED: Not set.
      CREATING: Redis instance is being created.
      READY: Redis instance has been created and is fully usable.
      UPDATING: Redis instance configuration is being updated. Certain kinds
        of updates may cause the instance to become unusable while the update
        is in progress.
      DELETING: Redis instance is being deleted.
      REPAIRING: Redis instance is being repaired and may be unusable.
      MAINTENANCE: Maintenance is being performed on this Redis instance.
      IMPORTING: Redis instance is importing data (availability may be
        affected).
      FAILING_OVER: Redis instance is failing over (availability may be
        affected).
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    READY = 2
    UPDATING = 3
    DELETING = 4
    REPAIRING = 5
    MAINTENANCE = 6
    IMPORTING = 7
    FAILING_OVER = 8

  class SuspensionReasonsValueListEntryValuesEnum(_messages.Enum):
    r"""SuspensionReasonsValueListEntryValuesEnum enum type.

    Values:
      SUSPENSION_REASON_UNSPECIFIED: Not set.
      CUSTOMER_MANAGED_KEY_ISSUE: Something wrong with the CMEK key provided
        by customer.
    """
    SUSPENSION_REASON_UNSPECIFIED = 0
    CUSTOMER_MANAGED_KEY_ISSUE = 1

  class TierValueValuesEnum(_messages.Enum):
    r"""Required. The service tier of the instance.

    Values:
      TIER_UNSPECIFIED: Not set.
      BASIC: BASIC tier: standalone instance
      STANDARD_HA: STANDARD_HA tier: highly available primary/replica
        instances
    """
    TIER_UNSPECIFIED = 0
    BASIC = 1
    STANDARD_HA = 2

  class TransitEncryptionModeValueValuesEnum(_messages.Enum):
    r"""Optional. The TLS mode of the Redis instance. If not provided, TLS is
    disabled for the instance.

    Values:
      TRANSIT_ENCRYPTION_MODE_UNSPECIFIED: Not set.
      SERVER_AUTHENTICATION: Client to Server traffic encryption enabled with
        server authentication.
      DISABLED: TLS is disabled for the instance.
    """
    TRANSIT_ENCRYPTION_MODE_UNSPECIFIED = 0
    SERVER_AUTHENTICATION = 1
    DISABLED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Resource labels to represent user provided metadata

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class RedisConfigsValue(_messages.Message):
    r"""Optional. Redis configuration parameters, according to
    http://redis.io/topics/config. Currently, the only supported parameters
    are: Redis version 3.2 and newer: * maxmemory-policy * notify-keyspace-
    events Redis version 4.0 and newer: * activedefrag * lfu-decay-time * lfu-
    log-factor * maxmemory-gb Redis version 5.0 and newer: * stream-node-max-
    bytes * stream-node-max-entries

    Messages:
      AdditionalProperty: An additional property for a RedisConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type RedisConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a RedisConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  alternativeLocationId = _messages.StringField(1)
  authEnabled = _messages.BooleanField(2)
  authorizedNetwork = _messages.StringField(3)
  availableMaintenanceVersions = _messages.StringField(4, repeated=True)
  connectMode = _messages.EnumField('ConnectModeValueValuesEnum', 5)
  createTime = _messages.StringField(6)
  currentLocationId = _messages.StringField(7)
  customerManagedKey = _messages.StringField(8)
  displayName = _messages.StringField(9)
  host = _messages.StringField(10)
  labels = _messages.MessageField('LabelsValue', 11)
  locationId = _messages.StringField(12)
  maintenancePolicy = _messages.MessageField('MaintenancePolicy', 13)
  maintenanceSchedule = _messages.MessageField('MaintenanceSchedule', 14)
  maintenanceVersion = _messages.StringField(15)
  memorySizeGb = _messages.IntegerField(16, variant=_messages.Variant.INT32)
  name = _messages.StringField(17)
  nodes = _messages.MessageField('NodeInfo', 18, repeated=True)
  persistenceConfig = _messages.MessageField('PersistenceConfig', 19)
  persistenceIamIdentity = _messages.StringField(20)
  port = _messages.IntegerField(21, variant=_messages.Variant.INT32)
  readEndpoint = _messages.StringField(22)
  readEndpointPort = _messages.IntegerField(23, variant=_messages.Variant.INT32)
  readReplicasMode = _messages.EnumField('ReadReplicasModeValueValuesEnum', 24)
  redisConfigs = _messages.MessageField('RedisConfigsValue', 25)
  redisVersion = _messages.StringField(26)
  replicaCount = _messages.IntegerField(27, variant=_messages.Variant.INT32)
  reservedIpRange = _messages.StringField(28)
  secondaryIpRange = _messages.StringField(29)
  serverCaCerts = _messages.MessageField('TlsCertificate', 30, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 31)
  statusMessage = _messages.StringField(32)
  suspensionReasons = _messages.EnumField('SuspensionReasonsValueListEntryValuesEnum', 33, repeated=True)
  tier = _messages.EnumField('TierValueValuesEnum', 34)
  transitEncryptionMode = _messages.EnumField('TransitEncryptionModeValueValuesEnum', 35)


class InstanceAuthString(_messages.Message):
  r"""Instance AUTH string details.

  Fields:
    authString: AUTH string set on the instance.
  """

  authString = _messages.StringField(1)


class ListClustersResponse(_messages.Message):
  r"""Response for ListClusters.

  Fields:
    clusters: A list of Redis clusters in the project in the specified
      location, or across all locations. If the `location_id` in the parent
      field of the request is "-", all regions available to the project are
      queried, and the results aggregated. If in such an aggregated query a
      location is unavailable, a placeholder Redis entry is included in the
      response with the `name` field set to a value of the form
      `projects/{project_id}/locations/{location_id}/clusters/`- and the
      `status` field set to ERROR and `status_message` field set to "location
      not available for ListClusters".
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Locations that could not be reached.
  """

  clusters = _messages.MessageField('Cluster', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListInstancesResponse(_messages.Message):
  r"""Response for ListInstances.

  Fields:
    instances: A list of Redis instances in the project in the specified
      location, or across all locations. If the `location_id` in the parent
      field of the request is "-", all regions available to the project are
      queried, and the results aggregated. If in such an aggregated query a
      location is unavailable, a placeholder Redis entry is included in the
      response with the `name` field set to a value of the form
      `projects/{project_id}/locations/{location_id}/instances/`- and the
      `status` field set to ERROR and `status_message` field set to "location
      not available for ListInstances".
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    unreachable: Locations that could not be reached.
  """

  instances = _messages.MessageField('Instance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Output only. The set of available zones in the location.
      The map is keyed by the lowercase ID of each zone, as defined by Compute
      Engine. These keys can be specified in `location_id` or
      `alternative_location_id` fields when creating a Redis instance.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: Resource ID for the region. For example: "us-east1".
    metadata: Output only. The set of available zones in the location. The map
      is keyed by the lowercase ID of each zone, as defined by Compute Engine.
      These keys can be specified in `location_id` or
      `alternative_location_id` fields when creating a Redis instance.
    name: Full resource name for the region. For example: "projects/example-
      project/locations/us-east1".
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Output only. The set of available zones in the location. The map is
    keyed by the lowercase ID of each zone, as defined by Compute Engine.
    These keys can be specified in `location_id` or `alternative_location_id`
    fields when creating a Redis instance.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class MaintenancePolicy(_messages.Message):
  r"""Maintenance policy for an instance.

  Fields:
    createTime: Output only. The time when the policy was created.
    description: Optional. Description of what this policy is for.
      Create/Update methods return INVALID_ARGUMENT if the length is greater
      than 512.
    updateTime: Output only. The time when the policy was last updated.
    weeklyMaintenanceWindow: Optional. Maintenance window that is applied to
      resources covered by this policy. Minimum 1. For the current version,
      the maximum number of weekly_window is expected to be one.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  updateTime = _messages.StringField(3)
  weeklyMaintenanceWindow = _messages.MessageField('WeeklyMaintenanceWindow', 4, repeated=True)


class MaintenanceSchedule(_messages.Message):
  r"""Upcoming maintenance schedule. If no maintenance is scheduled, fields
  are not populated.

  Fields:
    canReschedule: If the scheduled maintenance can be rescheduled, default is
      true.
    endTime: Output only. The end time of any upcoming scheduled maintenance
      for this instance.
    scheduleDeadlineTime: Output only. The deadline that the maintenance
      schedule start time can not go beyond, including reschedule.
    startTime: Output only. The start time of any upcoming scheduled
      maintenance for this instance.
  """

  canReschedule = _messages.BooleanField(1)
  endTime = _messages.StringField(2)
  scheduleDeadlineTime = _messages.StringField(3)
  startTime = _messages.StringField(4)


class ManagedCertificateAuthority(_messages.Message):
  r"""A ManagedCertificateAuthority object.

  Fields:
    caCerts: The PEM encoded CA certificate chains for redis managed server
      authentication
  """

  caCerts = _messages.MessageField('CertChain', 1, repeated=True)


class NodeInfo(_messages.Message):
  r"""Node specific properties.

  Fields:
    id: Output only. Node identifying string. e.g. 'node-0', 'node-1'
    zone: Output only. Location of the node.
  """

  id = _messages.StringField(1)
  zone = _messages.StringField(2)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: { `createTime`: The time the operation was created.
      `endTime`: The time the operation finished running. `target`: Server-
      defined resource path for the target of the operation. `verb`: Name of
      the verb executed by the operation. `statusDetail`: Human-readable
      status of the operation, if any. `cancelRequested`: Identifies whether
      the user has requested cancellation of the operation. Operations that
      have successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
      `apiVersion`: API version used to start the operation. }
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: { `createTime`: The time the operation was created. `endTime`:
      The time the operation finished running. `target`: Server-defined
      resource path for the target of the operation. `verb`: Name of the verb
      executed by the operation. `statusDetail`: Human-readable status of the
      operation, if any. `cancelRequested`: Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
      `apiVersion`: API version used to start the operation. }
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""{ `createTime`: The time the operation was created. `endTime`: The
    time the operation finished running. `target`: Server-defined resource
    path for the target of the operation. `verb`: Name of the verb executed by
    the operation. `statusDetail`: Human-readable status of the operation, if
    any. `cancelRequested`: Identifies whether the user has requested
    cancellation of the operation. Operations that have successfully been
    cancelled have Operation.error value with a google.rpc.Status.code of 1,
    corresponding to `Code.CANCELLED`. `apiVersion`: API version used to start
    the operation. }

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Pre-defined metadata fields.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class OutputConfig(_messages.Message):
  r"""The output content

  Fields:
    gcsDestination: Google Cloud Storage destination for output content.
  """

  gcsDestination = _messages.MessageField('GcsDestination', 1)


class PersistenceConfig(_messages.Message):
  r"""Configuration of the persistence functionality.

  Enums:
    PersistenceModeValueValuesEnum: Optional. Controls whether Persistence
      features are enabled. If not provided, the existing value will be used.
    RdbSnapshotPeriodValueValuesEnum: Optional. Period between RDB snapshots.
      Snapshots will be attempted every period starting from the provided
      snapshot start time. For example, a start time of 01/01/2033 06:45 and
      SIX_HOURS snapshot period will do nothing until 01/01/2033, and then
      trigger snapshots every day at 06:45, 12:45, 18:45, and 00:45 the next
      day, and so on. If not provided, TWENTY_FOUR_HOURS will be used as
      default.

  Fields:
    persistenceMode: Optional. Controls whether Persistence features are
      enabled. If not provided, the existing value will be used.
    rdbNextSnapshotTime: Output only. The next time that a snapshot attempt is
      scheduled to occur.
    rdbSnapshotPeriod: Optional. Period between RDB snapshots. Snapshots will
      be attempted every period starting from the provided snapshot start
      time. For example, a start time of 01/01/2033 06:45 and SIX_HOURS
      snapshot period will do nothing until 01/01/2033, and then trigger
      snapshots every day at 06:45, 12:45, 18:45, and 00:45 the next day, and
      so on. If not provided, TWENTY_FOUR_HOURS will be used as default.
    rdbSnapshotStartTime: Optional. Date and time that the first snapshot
      was/will be attempted, and to which future snapshots will be aligned. If
      not provided, the current time will be used.
  """

  class PersistenceModeValueValuesEnum(_messages.Enum):
    r"""Optional. Controls whether Persistence features are enabled. If not
    provided, the existing value will be used.

    Values:
      PERSISTENCE_MODE_UNSPECIFIED: Not set.
      DISABLED: Persistence is disabled for the instance, and any existing
        snapshots are deleted.
      RDB: RDB based Persistence is enabled.
    """
    PERSISTENCE_MODE_UNSPECIFIED = 0
    DISABLED = 1
    RDB = 2

  class RdbSnapshotPeriodValueValuesEnum(_messages.Enum):
    r"""Optional. Period between RDB snapshots. Snapshots will be attempted
    every period starting from the provided snapshot start time. For example,
    a start time of 01/01/2033 06:45 and SIX_HOURS snapshot period will do
    nothing until 01/01/2033, and then trigger snapshots every day at 06:45,
    12:45, 18:45, and 00:45 the next day, and so on. If not provided,
    TWENTY_FOUR_HOURS will be used as default.

    Values:
      SNAPSHOT_PERIOD_UNSPECIFIED: Not set.
      FIFTEEN_MINUTES: Snapshot every 15 minutes.
      THIRTY_MINUTES: Snapshot every 30 minutes.
      ONE_HOUR: Snapshot every 1 hour.
      SIX_HOURS: Snapshot every 6 hours.
      TWELVE_HOURS: Snapshot every 12 hours.
      TWENTY_FOUR_HOURS: Snapshot every 24 hours.
    """
    SNAPSHOT_PERIOD_UNSPECIFIED = 0
    FIFTEEN_MINUTES = 1
    THIRTY_MINUTES = 2
    ONE_HOUR = 3
    SIX_HOURS = 4
    TWELVE_HOURS = 5
    TWENTY_FOUR_HOURS = 6

  persistenceMode = _messages.EnumField('PersistenceModeValueValuesEnum', 1)
  rdbNextSnapshotTime = _messages.StringField(2)
  rdbSnapshotPeriod = _messages.EnumField('RdbSnapshotPeriodValueValuesEnum', 3)
  rdbSnapshotStartTime = _messages.StringField(4)


class PscConfig(_messages.Message):
  r"""A PscConfig object.

  Fields:
    network: Required. The network where the IP address of the discovery
      endpoint will be reserved, in the form of
      projects/{network_project}/global/networks/{network_id}.
  """

  network = _messages.StringField(1)


class PscConnection(_messages.Message):
  r"""Details of consumer resources in a PSC connection.

  Fields:
    address: Output only. The IP allocated on the consumer network for the PSC
      forwarding rule.
    forwardingRule: Output only. The URI of the consumer side forwarding rule.
      Example: projects/{projectNumOrId}/regions/us-
      east1/forwardingRules/{resourceId}.
    network: The consumer network where the IP address resides, in the form of
      projects/{project_id}/global/networks/{network_id}.
    pscConnectionId: Output only. The PSC connection id of the forwarding rule
      connected to the service attachment.
  """

  address = _messages.StringField(1)
  forwardingRule = _messages.StringField(2)
  network = _messages.StringField(3)
  pscConnectionId = _messages.StringField(4)


class ReconciliationOperationMetadata(_messages.Message):
  r"""Operation metadata returned by the CLH during resource state
  reconciliation.

  Enums:
    ExclusiveActionValueValuesEnum: Excluisive action returned by the CLH.

  Fields:
    deleteResource: DEPRECATED. Use exclusive_action instead.
    exclusiveAction: Excluisive action returned by the CLH.
  """

  class ExclusiveActionValueValuesEnum(_messages.Enum):
    r"""Excluisive action returned by the CLH.

    Values:
      UNKNOWN_REPAIR_ACTION: Unknown repair action.
      DELETE: The resource has to be deleted. When using this bit, the CLH
        should fail the operation. DEPRECATED. Instead use DELETE_RESOURCE
        OperationSignal in SideChannel.
      RETRY: This resource could not be repaired but the repair should be
        tried again at a later time. This can happen if there is a dependency
        that needs to be resolved first- e.g. if a parent resource must be
        repaired before a child resource.
    """
    UNKNOWN_REPAIR_ACTION = 0
    DELETE = 1
    RETRY = 2

  deleteResource = _messages.BooleanField(1)
  exclusiveAction = _messages.EnumField('ExclusiveActionValueValuesEnum', 2)


class RedisProjectsLocationsClustersCreateRequest(_messages.Message):
  r"""A RedisProjectsLocationsClustersCreateRequest object.

  Fields:
    cluster: A Cluster resource to be passed as the request body.
    clusterId: Required. The logical name of the Redis cluster in the customer
      project with the following restrictions: * Must contain only lowercase
      letters, numbers, and hyphens. * Must start with a letter. * Must be
      between 1-63 characters. * Must end with a number or a letter. * Must be
      unique within the customer project / location
    parent: Required. The resource name of the cluster location using the
      form: `projects/{project_id}/locations/{location_id}` where
      `location_id` refers to a GCP region.
    requestId: Idempotent request UUID.
  """

  cluster = _messages.MessageField('Cluster', 1)
  clusterId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class RedisProjectsLocationsClustersDeleteRequest(_messages.Message):
  r"""A RedisProjectsLocationsClustersDeleteRequest object.

  Fields:
    name: Required. Redis cluster resource name using the form:
      `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`
      where `location_id` refers to a GCP region.
    requestId: Idempotent request UUID.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class RedisProjectsLocationsClustersGetCertificateAuthorityRequest(_messages.Message):
  r"""A RedisProjectsLocationsClustersGetCertificateAuthorityRequest object.

  Fields:
    name: Required. Redis cluster certificate authority resource name using
      the form: `projects/{project_id}/locations/{location_id}/clusters/{clust
      er_id}/certificateAuthority` where `location_id` refers to a GCP region.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsClustersGetRequest(_messages.Message):
  r"""A RedisProjectsLocationsClustersGetRequest object.

  Fields:
    name: Required. Redis cluster resource name using the form:
      `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`
      where `location_id` refers to a GCP region.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsClustersListRequest(_messages.Message):
  r"""A RedisProjectsLocationsClustersListRequest object.

  Fields:
    pageSize: The maximum number of items to return. If not specified, a
      default value of 1000 will be used by the service. Regardless of the
      page_size value, the response may include a partial list and a caller
      should only rely on response's `next_page_token` to determine if there
      are more clusters left to be queried.
    pageToken: The `next_page_token` value returned from a previous
      ListClusters request, if any.
    parent: Required. The resource name of the cluster location using the
      form: `projects/{project_id}/locations/{location_id}` where
      `location_id` refers to a GCP region.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RedisProjectsLocationsClustersPatchRequest(_messages.Message):
  r"""A RedisProjectsLocationsClustersPatchRequest object.

  Fields:
    cluster: A Cluster resource to be passed as the request body.
    name: Required. Unique name of the resource in this scope including
      project and location using the form:
      `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}`
    requestId: Idempotent request UUID.
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. The elements of the repeated paths field may
      only include these fields from Cluster: * `size_gb` * `replica_count`
  """

  cluster = _messages.MessageField('Cluster', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class RedisProjectsLocationsGetRequest(_messages.Message):
  r"""A RedisProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsInstancesCreateRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesCreateRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    instanceId: Required. The logical name of the Redis instance in the
      customer project with the following restrictions: * Must contain only
      lowercase letters, numbers, and hyphens. * Must start with a letter. *
      Must be between 1-40 characters. * Must end with a number or a letter. *
      Must be unique within the customer project / location
    parent: Required. The resource name of the instance location using the
      form: `projects/{project_id}/locations/{location_id}` where
      `location_id` refers to a GCP region.
  """

  instance = _messages.MessageField('Instance', 1)
  instanceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RedisProjectsLocationsInstancesDeleteRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesDeleteRequest object.

  Fields:
    name: Required. Redis instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsInstancesExportRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesExportRequest object.

  Fields:
    exportInstanceRequest: A ExportInstanceRequest resource to be passed as
      the request body.
    name: Required. Redis instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region.
  """

  exportInstanceRequest = _messages.MessageField('ExportInstanceRequest', 1)
  name = _messages.StringField(2, required=True)


class RedisProjectsLocationsInstancesFailoverRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesFailoverRequest object.

  Fields:
    failoverInstanceRequest: A FailoverInstanceRequest resource to be passed
      as the request body.
    name: Required. Redis instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region.
  """

  failoverInstanceRequest = _messages.MessageField('FailoverInstanceRequest', 1)
  name = _messages.StringField(2, required=True)


class RedisProjectsLocationsInstancesGetAuthStringRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesGetAuthStringRequest object.

  Fields:
    name: Required. Redis instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsInstancesGetRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesGetRequest object.

  Fields:
    name: Required. Redis instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsInstancesImportRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesImportRequest object.

  Fields:
    importInstanceRequest: A ImportInstanceRequest resource to be passed as
      the request body.
    name: Required. Redis instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region.
  """

  importInstanceRequest = _messages.MessageField('ImportInstanceRequest', 1)
  name = _messages.StringField(2, required=True)


class RedisProjectsLocationsInstancesListRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesListRequest object.

  Fields:
    pageSize: The maximum number of items to return. If not specified, a
      default value of 1000 will be used by the service. Regardless of the
      page_size value, the response may include a partial list and a caller
      should only rely on response's `next_page_token` to determine if there
      are more instances left to be queried.
    pageToken: The `next_page_token` value returned from a previous
      ListInstances request, if any.
    parent: Required. The resource name of the instance location using the
      form: `projects/{project_id}/locations/{location_id}` where
      `location_id` refers to a GCP region.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class RedisProjectsLocationsInstancesPatchRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesPatchRequest object.

  Fields:
    instance: A Instance resource to be passed as the request body.
    name: Required. Unique name of the resource in this scope including
      project and location using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      Note: Redis instances are managed and addressed at regional level so
      location_id here refers to a GCP region; however, users may choose which
      specific zone (or collection of zones for cross-zone instances) an
      instance should be provisioned in. Refer to location_id and
      alternative_location_id fields for more details.
    updateMask: Required. Mask of fields to update. At least one path must be
      supplied in this field. The elements of the repeated paths field may
      only include these fields from Instance: * `displayName` * `labels` *
      `memorySizeGb` * `redisConfig` * `replica_count`
  """

  instance = _messages.MessageField('Instance', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class RedisProjectsLocationsInstancesRescheduleMaintenanceRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesRescheduleMaintenanceRequest object.

  Fields:
    name: Required. Redis instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region.
    rescheduleMaintenanceRequest: A RescheduleMaintenanceRequest resource to
      be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  rescheduleMaintenanceRequest = _messages.MessageField('RescheduleMaintenanceRequest', 2)


class RedisProjectsLocationsInstancesUpgradeRequest(_messages.Message):
  r"""A RedisProjectsLocationsInstancesUpgradeRequest object.

  Fields:
    name: Required. Redis instance resource name using the form:
      `projects/{project_id}/locations/{location_id}/instances/{instance_id}`
      where `location_id` refers to a GCP region.
    upgradeInstanceRequest: A UpgradeInstanceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  upgradeInstanceRequest = _messages.MessageField('UpgradeInstanceRequest', 2)


class RedisProjectsLocationsListRequest(_messages.Message):
  r"""A RedisProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class RedisProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A RedisProjectsLocationsOperationsCancelRequest object.

  Fields:
    name: The name of the operation resource to be cancelled.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A RedisProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A RedisProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class RedisProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A RedisProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class RescheduleMaintenanceRequest(_messages.Message):
  r"""Request for RescheduleMaintenance.

  Enums:
    RescheduleTypeValueValuesEnum: Required. If reschedule type is
      SPECIFIC_TIME, must set up schedule_time as well.

  Fields:
    rescheduleType: Required. If reschedule type is SPECIFIC_TIME, must set up
      schedule_time as well.
    scheduleTime: Optional. Timestamp when the maintenance shall be
      rescheduled to if reschedule_type=SPECIFIC_TIME, in RFC 3339 format, for
      example `2012-11-15T16:19:00.094Z`.
  """

  class RescheduleTypeValueValuesEnum(_messages.Enum):
    r"""Required. If reschedule type is SPECIFIC_TIME, must set up
    schedule_time as well.

    Values:
      RESCHEDULE_TYPE_UNSPECIFIED: Not set.
      IMMEDIATE: If the user wants to schedule the maintenance to happen now.
      NEXT_AVAILABLE_WINDOW: If the user wants to use the existing maintenance
        policy to find the next available window.
      SPECIFIC_TIME: If the user wants to reschedule the maintenance to a
        specific time.
    """
    RESCHEDULE_TYPE_UNSPECIFIED = 0
    IMMEDIATE = 1
    NEXT_AVAILABLE_WINDOW = 2
    SPECIFIC_TIME = 3

  rescheduleType = _messages.EnumField('RescheduleTypeValueValuesEnum', 1)
  scheduleTime = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StateInfo(_messages.Message):
  r"""Represents additional information about the state of the cluster.

  Fields:
    updateInfo: Describes ongoing update on the cluster when cluster state is
      UPDATING.
  """

  updateInfo = _messages.MessageField('UpdateInfo', 1)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TimeOfDay(_messages.Message):
  r"""Represents a time of day. The date and time zone are either not
  significant or are specified elsewhere. An API may choose to allow leap
  seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.

  Fields:
    hours: Hours of day in 24 hour format. Should be from 0 to 23. An API may
      choose to allow the value "24:00:00" for scenarios like business closing
      time.
    minutes: Minutes of hour of day. Must be from 0 to 59.
    nanos: Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999.
    seconds: Seconds of minutes of the time. Must normally be from 0 to 59. An
      API may allow the value 60 if it allows leap-seconds.
  """

  hours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class TlsCertificate(_messages.Message):
  r"""TlsCertificate Resource

  Fields:
    cert: PEM representation.
    createTime: Output only. The time when the certificate was created in [RFC
      3339](https://tools.ietf.org/html/rfc3339) format, for example
      `2020-05-18T00:00:00.094Z`.
    expireTime: Output only. The time when the certificate expires in [RFC
      3339](https://tools.ietf.org/html/rfc3339) format, for example
      `2020-05-18T00:00:00.094Z`.
    serialNumber: Serial number, as extracted from the certificate.
    sha1Fingerprint: Sha1 Fingerprint of the certificate.
  """

  cert = _messages.StringField(1)
  createTime = _messages.StringField(2)
  expireTime = _messages.StringField(3)
  serialNumber = _messages.StringField(4)
  sha1Fingerprint = _messages.StringField(5)


class UpdateInfo(_messages.Message):
  r"""Represents information about an updating cluster.

  Fields:
    targetReplicaCount: Target number of replica nodes per shard.
    targetShardCount: Target number of shards for redis cluster
  """

  targetReplicaCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  targetShardCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class UpgradeInstanceRequest(_messages.Message):
  r"""Request for UpgradeInstance.

  Fields:
    redisVersion: Required. Specifies the target version of Redis software to
      upgrade to.
  """

  redisVersion = _messages.StringField(1)


class WeeklyMaintenanceWindow(_messages.Message):
  r"""Time window in which disruptive maintenance updates occur. Non-
  disruptive updates can occur inside or outside this window.

  Enums:
    DayValueValuesEnum: Required. The day of week that maintenance updates
      occur.

  Fields:
    day: Required. The day of week that maintenance updates occur.
    duration: Output only. Duration of the maintenance window. The current
      window is fixed at 1 hour.
    startTime: Required. Start time of the window in UTC time.
  """

  class DayValueValuesEnum(_messages.Enum):
    r"""Required. The day of week that maintenance updates occur.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  day = _messages.EnumField('DayValueValuesEnum', 1)
  duration = _messages.StringField(2)
  startTime = _messages.MessageField('TimeOfDay', 3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

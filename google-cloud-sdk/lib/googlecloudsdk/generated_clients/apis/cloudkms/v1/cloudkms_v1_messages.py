"""Generated message classes for cloudkms version v1.

Manages keys and performs cryptographic operations in a central cloud service,
for direct use by other cloud resources and applications.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'cloudkms'


class AsymmetricDecryptRequest(_messages.Message):
  r"""Request message for KeyManagementService.AsymmetricDecrypt.

  Fields:
    ciphertext: Required. The data encrypted with the named CryptoKeyVersion's
      public key using OAEP.
    ciphertextCrc32c: Optional. An optional CRC32C checksum of the
      AsymmetricDecryptRequest.ciphertext. If specified, KeyManagementService
      will verify the integrity of the received
      AsymmetricDecryptRequest.ciphertext using this checksum.
      KeyManagementService will report an error if the checksum verification
      fails. If you receive a checksum error, your client should verify that
      CRC32C(AsymmetricDecryptRequest.ciphertext) is equal to
      AsymmetricDecryptRequest.ciphertext_crc32c, and if so, perform a limited
      number of retries. A persistent mismatch may indicate an issue in your
      computation of the CRC32C checksum. Note: This field is defined as int64
      for reasons of compatibility across different languages. However, it is
      a non-negative integer, which will never exceed 2^32-1, and can be
      safely downconverted to uint32 in languages that support this type.
  """

  ciphertext = _messages.BytesField(1)
  ciphertextCrc32c = _messages.IntegerField(2)


class AsymmetricDecryptResponse(_messages.Message):
  r"""Response message for KeyManagementService.AsymmetricDecrypt.

  Enums:
    ProtectionLevelValueValuesEnum: The ProtectionLevel of the
      CryptoKeyVersion used in decryption.

  Fields:
    plaintext: The decrypted data originally encrypted with the matching
      public key.
    plaintextCrc32c: Integrity verification field. A CRC32C checksum of the
      returned AsymmetricDecryptResponse.plaintext. An integrity check of
      AsymmetricDecryptResponse.plaintext can be performed by computing the
      CRC32C checksum of AsymmetricDecryptResponse.plaintext and comparing
      your results to this field. Discard the response in case of non-matching
      checksum values, and perform a limited number of retries. A persistent
      mismatch may indicate an issue in your computation of the CRC32C
      checksum. Note: This field is defined as int64 for reasons of
      compatibility across different languages. However, it is a non-negative
      integer, which will never exceed 2^32-1, and can be safely downconverted
      to uint32 in languages that support this type.
    protectionLevel: The ProtectionLevel of the CryptoKeyVersion used in
      decryption.
    verifiedCiphertextCrc32c: Integrity verification field. A flag indicating
      whether AsymmetricDecryptRequest.ciphertext_crc32c was received by
      KeyManagementService and used for the integrity verification of the
      ciphertext. A false value of this field indicates either that
      AsymmetricDecryptRequest.ciphertext_crc32c was left unset or that it was
      not delivered to KeyManagementService. If you've set
      AsymmetricDecryptRequest.ciphertext_crc32c but this field is still
      false, discard the response and perform a limited number of retries.
  """

  class ProtectionLevelValueValuesEnum(_messages.Enum):
    r"""The ProtectionLevel of the CryptoKeyVersion used in decryption.

    Values:
      PROTECTION_LEVEL_UNSPECIFIED: Not specified.
      SOFTWARE: Crypto operations are performed in software.
      HSM: Crypto operations are performed in a Hardware Security Module.
      EXTERNAL: Crypto operations are performed by an external key manager.
      EXTERNAL_VPC: Crypto operations are performed in an EKM-over-VPC
        backend.
    """
    PROTECTION_LEVEL_UNSPECIFIED = 0
    SOFTWARE = 1
    HSM = 2
    EXTERNAL = 3
    EXTERNAL_VPC = 4

  plaintext = _messages.BytesField(1)
  plaintextCrc32c = _messages.IntegerField(2)
  protectionLevel = _messages.EnumField('ProtectionLevelValueValuesEnum', 3)
  verifiedCiphertextCrc32c = _messages.BooleanField(4)


class AsymmetricSignRequest(_messages.Message):
  r"""Request message for KeyManagementService.AsymmetricSign.

  Fields:
    data: Optional. The data to sign. It can't be supplied if
      AsymmetricSignRequest.digest is supplied.
    dataCrc32c: Optional. An optional CRC32C checksum of the
      AsymmetricSignRequest.data. If specified, KeyManagementService will
      verify the integrity of the received AsymmetricSignRequest.data using
      this checksum. KeyManagementService will report an error if the checksum
      verification fails. If you receive a checksum error, your client should
      verify that CRC32C(AsymmetricSignRequest.data) is equal to
      AsymmetricSignRequest.data_crc32c, and if so, perform a limited number
      of retries. A persistent mismatch may indicate an issue in your
      computation of the CRC32C checksum. Note: This field is defined as int64
      for reasons of compatibility across different languages. However, it is
      a non-negative integer, which will never exceed 2^32-1, and can be
      safely downconverted to uint32 in languages that support this type.
    digest: Optional. The digest of the data to sign. The digest must be
      produced with the same digest algorithm as specified by the key
      version's algorithm. This field may not be supplied if
      AsymmetricSignRequest.data is supplied.
    digestCrc32c: Optional. An optional CRC32C checksum of the
      AsymmetricSignRequest.digest. If specified, KeyManagementService will
      verify the integrity of the received AsymmetricSignRequest.digest using
      this checksum. KeyManagementService will report an error if the checksum
      verification fails. If you receive a checksum error, your client should
      verify that CRC32C(AsymmetricSignRequest.digest) is equal to
      AsymmetricSignRequest.digest_crc32c, and if so, perform a limited number
      of retries. A persistent mismatch may indicate an issue in your
      computation of the CRC32C checksum. Note: This field is defined as int64
      for reasons of compatibility across different languages. However, it is
      a non-negative integer, which will never exceed 2^32-1, and can be
      safely downconverted to uint32 in languages that support this type.
  """

  data = _messages.BytesField(1)
  dataCrc32c = _messages.IntegerField(2)
  digest = _messages.MessageField('Digest', 3)
  digestCrc32c = _messages.IntegerField(4)


class AsymmetricSignResponse(_messages.Message):
  r"""Response message for KeyManagementService.AsymmetricSign.

  Enums:
    ProtectionLevelValueValuesEnum: The ProtectionLevel of the
      CryptoKeyVersion used for signing.

  Fields:
    name: The resource name of the CryptoKeyVersion used for signing. Check
      this field to verify that the intended resource was used for signing.
    protectionLevel: The ProtectionLevel of the CryptoKeyVersion used for
      signing.
    signature: The created signature.
    signatureCrc32c: Integrity verification field. A CRC32C checksum of the
      returned AsymmetricSignResponse.signature. An integrity check of
      AsymmetricSignResponse.signature can be performed by computing the
      CRC32C checksum of AsymmetricSignResponse.signature and comparing your
      results to this field. Discard the response in case of non-matching
      checksum values, and perform a limited number of retries. A persistent
      mismatch may indicate an issue in your computation of the CRC32C
      checksum. Note: This field is defined as int64 for reasons of
      compatibility across different languages. However, it is a non-negative
      integer, which will never exceed 2^32-1, and can be safely downconverted
      to uint32 in languages that support this type.
    verifiedDataCrc32c: Integrity verification field. A flag indicating
      whether AsymmetricSignRequest.data_crc32c was received by
      KeyManagementService and used for the integrity verification of the
      data. A false value of this field indicates either that
      AsymmetricSignRequest.data_crc32c was left unset or that it was not
      delivered to KeyManagementService. If you've set
      AsymmetricSignRequest.data_crc32c but this field is still false, discard
      the response and perform a limited number of retries.
    verifiedDigestCrc32c: Integrity verification field. A flag indicating
      whether AsymmetricSignRequest.digest_crc32c was received by
      KeyManagementService and used for the integrity verification of the
      digest. A false value of this field indicates either that
      AsymmetricSignRequest.digest_crc32c was left unset or that it was not
      delivered to KeyManagementService. If you've set
      AsymmetricSignRequest.digest_crc32c but this field is still false,
      discard the response and perform a limited number of retries.
  """

  class ProtectionLevelValueValuesEnum(_messages.Enum):
    r"""The ProtectionLevel of the CryptoKeyVersion used for signing.

    Values:
      PROTECTION_LEVEL_UNSPECIFIED: Not specified.
      SOFTWARE: Crypto operations are performed in software.
      HSM: Crypto operations are performed in a Hardware Security Module.
      EXTERNAL: Crypto operations are performed by an external key manager.
      EXTERNAL_VPC: Crypto operations are performed in an EKM-over-VPC
        backend.
    """
    PROTECTION_LEVEL_UNSPECIFIED = 0
    SOFTWARE = 1
    HSM = 2
    EXTERNAL = 3
    EXTERNAL_VPC = 4

  name = _messages.StringField(1)
  protectionLevel = _messages.EnumField('ProtectionLevelValueValuesEnum', 2)
  signature = _messages.BytesField(3)
  signatureCrc32c = _messages.IntegerField(4)
  verifiedDataCrc32c = _messages.BooleanField(5)
  verifiedDigestCrc32c = _messages.BooleanField(6)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class Certificate(_messages.Message):
  r"""A Certificate represents an X.509 certificate used to authenticate HTTPS
  connections to EKM replicas.

  Fields:
    issuer: Output only. The issuer distinguished name in RFC 2253 format.
      Only present if parsed is true.
    notAfterTime: Output only. The certificate is not valid after this time.
      Only present if parsed is true.
    notBeforeTime: Output only. The certificate is not valid before this time.
      Only present if parsed is true.
    parsed: Output only. True if the certificate was parsed successfully.
    rawDer: Required. The raw certificate bytes in DER format.
    serialNumber: Output only. The certificate serial number as a hex string.
      Only present if parsed is true.
    sha256Fingerprint: Output only. The SHA-256 certificate fingerprint as a
      hex string. Only present if parsed is true.
    subject: Output only. The subject distinguished name in RFC 2253 format.
      Only present if parsed is true.
    subjectAlternativeDnsNames: Output only. The subject Alternative DNS
      names. Only present if parsed is true.
  """

  issuer = _messages.StringField(1)
  notAfterTime = _messages.StringField(2)
  notBeforeTime = _messages.StringField(3)
  parsed = _messages.BooleanField(4)
  rawDer = _messages.BytesField(5)
  serialNumber = _messages.StringField(6)
  sha256Fingerprint = _messages.StringField(7)
  subject = _messages.StringField(8)
  subjectAlternativeDnsNames = _messages.StringField(9, repeated=True)


class CertificateChains(_messages.Message):
  r"""Certificate chains needed to verify the attestation. Certificates in
  chains are PEM-encoded and are ordered based on
  https://tools.ietf.org/html/rfc5246#section-7.4.2.

  Fields:
    caviumCerts: Cavium certificate chain corresponding to the attestation.
    googleCardCerts: Google card certificate chain corresponding to the
      attestation.
    googlePartitionCerts: Google partition certificate chain corresponding to
      the attestation.
  """

  caviumCerts = _messages.StringField(1, repeated=True)
  googleCardCerts = _messages.StringField(2, repeated=True)
  googlePartitionCerts = _messages.StringField(3, repeated=True)


class CloudkmsProjectsLocationsEkmConfigGetIamPolicyRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsEkmConfigGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class CloudkmsProjectsLocationsEkmConfigSetIamPolicyRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsEkmConfigSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class CloudkmsProjectsLocationsEkmConfigTestIamPermissionsRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsEkmConfigTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class CloudkmsProjectsLocationsEkmConnectionsCreateRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsEkmConnectionsCreateRequest object.

  Fields:
    ekmConnection: A EkmConnection resource to be passed as the request body.
    ekmConnectionId: Required. It must be unique within a location and match
      the regular expression `[a-zA-Z0-9_-]{1,63}`.
    parent: Required. The resource name of the location associated with the
      EkmConnection, in the format `projects/*/locations/*`.
  """

  ekmConnection = _messages.MessageField('EkmConnection', 1)
  ekmConnectionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudkmsProjectsLocationsEkmConnectionsGetIamPolicyRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsEkmConnectionsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class CloudkmsProjectsLocationsEkmConnectionsGetRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsEkmConnectionsGetRequest object.

  Fields:
    name: Required. The name of the EkmConnection to get.
  """

  name = _messages.StringField(1, required=True)


class CloudkmsProjectsLocationsEkmConnectionsListRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsEkmConnectionsListRequest object.

  Fields:
    filter: Optional. Only include resources that match the filter in the
      response. For more information, see [Sorting and filtering list
      results](https://cloud.google.com/kms/docs/sorting-and-filtering).
    orderBy: Optional. Specify how the results should be sorted. If not
      specified, the results will be sorted in the default order. For more
      information, see [Sorting and filtering list
      results](https://cloud.google.com/kms/docs/sorting-and-filtering).
    pageSize: Optional. Optional limit on the number of EkmConnections to
      include in the response. Further EkmConnections can subsequently be
      obtained by including the ListEkmConnectionsResponse.next_page_token in
      a subsequent request. If unspecified, the server will pick an
      appropriate default.
    pageToken: Optional. Optional pagination token, returned earlier via
      ListEkmConnectionsResponse.next_page_token.
    parent: Required. The resource name of the location associated with the
      EkmConnections to list, in the format `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class CloudkmsProjectsLocationsEkmConnectionsPatchRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsEkmConnectionsPatchRequest object.

  Fields:
    ekmConnection: A EkmConnection resource to be passed as the request body.
    name: Output only. The resource name for the EkmConnection in the format
      `projects/*/locations/*/ekmConnections/*`.
    updateMask: Required. List of fields to be updated in this request.
  """

  ekmConnection = _messages.MessageField('EkmConnection', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudkmsProjectsLocationsEkmConnectionsSetIamPolicyRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsEkmConnectionsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class CloudkmsProjectsLocationsEkmConnectionsTestIamPermissionsRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsEkmConnectionsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class CloudkmsProjectsLocationsEkmConnectionsVerifyConnectivityRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsEkmConnectionsVerifyConnectivityRequest
  object.

  Fields:
    name: Required. The name of the EkmConnection to verify.
  """

  name = _messages.StringField(1, required=True)


class CloudkmsProjectsLocationsGenerateRandomBytesRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsGenerateRandomBytesRequest object.

  Fields:
    generateRandomBytesRequest: A GenerateRandomBytesRequest resource to be
      passed as the request body.
    location: The project-specific location in which to generate random bytes.
      For example, "projects/my-project/locations/us-central1".
  """

  generateRandomBytesRequest = _messages.MessageField('GenerateRandomBytesRequest', 1)
  location = _messages.StringField(2, required=True)


class CloudkmsProjectsLocationsGetEkmConfigRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsGetEkmConfigRequest object.

  Fields:
    name: Required. The name of the EkmConfig to get.
  """

  name = _messages.StringField(1, required=True)


class CloudkmsProjectsLocationsGetRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class CloudkmsProjectsLocationsKeyRingsCreateRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsCreateRequest object.

  Fields:
    keyRing: A KeyRing resource to be passed as the request body.
    keyRingId: Required. It must be unique within a location and match the
      regular expression `[a-zA-Z0-9_-]{1,63}`
    parent: Required. The resource name of the location associated with the
      KeyRings, in the format `projects/*/locations/*`.
  """

  keyRing = _messages.MessageField('KeyRing', 1)
  keyRingId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysCreateRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsCryptoKeysCreateRequest object.

  Fields:
    cryptoKey: A CryptoKey resource to be passed as the request body.
    cryptoKeyId: Required. It must be unique within a KeyRing and match the
      regular expression `[a-zA-Z0-9_-]{1,63}`
    parent: Required. The name of the KeyRing associated with the CryptoKeys.
    skipInitialVersionCreation: If set to true, the request will create a
      CryptoKey without any CryptoKeyVersions. You must manually call
      CreateCryptoKeyVersion or ImportCryptoKeyVersion before you can use this
      CryptoKey.
  """

  cryptoKey = _messages.MessageField('CryptoKey', 1)
  cryptoKeyId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  skipInitialVersionCreation = _messages.BooleanField(4)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsAsymmetricDecryptRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsAsymmetric
  DecryptRequest object.

  Fields:
    asymmetricDecryptRequest: A AsymmetricDecryptRequest resource to be passed
      as the request body.
    name: Required. The resource name of the CryptoKeyVersion to use for
      decryption.
  """

  asymmetricDecryptRequest = _messages.MessageField('AsymmetricDecryptRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsAsymmetricSignRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsAsymmetric
  SignRequest object.

  Fields:
    asymmetricSignRequest: A AsymmetricSignRequest resource to be passed as
      the request body.
    name: Required. The resource name of the CryptoKeyVersion to use for
      signing.
  """

  asymmetricSignRequest = _messages.MessageField('AsymmetricSignRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsCreateRequest(_messages.Message):
  r"""A
  CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsCreateRequest
  object.

  Fields:
    cryptoKeyVersion: A CryptoKeyVersion resource to be passed as the request
      body.
    parent: Required. The name of the CryptoKey associated with the
      CryptoKeyVersions.
  """

  cryptoKeyVersion = _messages.MessageField('CryptoKeyVersion', 1)
  parent = _messages.StringField(2, required=True)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsDestroyRequest(_messages.Message):
  r"""A
  CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsDestroyRequest
  object.

  Fields:
    destroyCryptoKeyVersionRequest: A DestroyCryptoKeyVersionRequest resource
      to be passed as the request body.
    name: Required. The resource name of the CryptoKeyVersion to destroy.
  """

  destroyCryptoKeyVersionRequest = _messages.MessageField('DestroyCryptoKeyVersionRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsGetPublicKeyRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsGetPublicK
  eyRequest object.

  Fields:
    name: Required. The name of the CryptoKeyVersion public key to get.
  """

  name = _messages.StringField(1, required=True)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsGetRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsGetRequest
  object.

  Fields:
    name: Required. The name of the CryptoKeyVersion to get.
  """

  name = _messages.StringField(1, required=True)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsImportRequest(_messages.Message):
  r"""A
  CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsImportRequest
  object.

  Fields:
    importCryptoKeyVersionRequest: A ImportCryptoKeyVersionRequest resource to
      be passed as the request body.
    parent: Required. The name of the CryptoKey to be imported into. The
      create permission is only required on this key when creating a new
      CryptoKeyVersion.
  """

  importCryptoKeyVersionRequest = _messages.MessageField('ImportCryptoKeyVersionRequest', 1)
  parent = _messages.StringField(2, required=True)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsListRequest(_messages.Message):
  r"""A
  CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsListRequest
  object.

  Enums:
    ViewValueValuesEnum: The fields to include in the response.

  Fields:
    filter: Optional. Only include resources that match the filter in the
      response. For more information, see [Sorting and filtering list
      results](https://cloud.google.com/kms/docs/sorting-and-filtering).
    orderBy: Optional. Specify how the results should be sorted. If not
      specified, the results will be sorted in the default order. For more
      information, see [Sorting and filtering list
      results](https://cloud.google.com/kms/docs/sorting-and-filtering).
    pageSize: Optional. Optional limit on the number of CryptoKeyVersions to
      include in the response. Further CryptoKeyVersions can subsequently be
      obtained by including the ListCryptoKeyVersionsResponse.next_page_token
      in a subsequent request. If unspecified, the server will pick an
      appropriate default.
    pageToken: Optional. Optional pagination token, returned earlier via
      ListCryptoKeyVersionsResponse.next_page_token.
    parent: Required. The resource name of the CryptoKey to list, in the
      format `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
    view: The fields to include in the response.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The fields to include in the response.

    Values:
      CRYPTO_KEY_VERSION_VIEW_UNSPECIFIED: Default view for each
        CryptoKeyVersion. Does not include the attestation field.
      FULL: Provides all fields in each CryptoKeyVersion, including the
        attestation.
    """
    CRYPTO_KEY_VERSION_VIEW_UNSPECIFIED = 0
    FULL = 1

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 6)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsMacSignRequest(_messages.Message):
  r"""A
  CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsMacSignRequest
  object.

  Fields:
    macSignRequest: A MacSignRequest resource to be passed as the request
      body.
    name: Required. The resource name of the CryptoKeyVersion to use for
      signing.
  """

  macSignRequest = _messages.MessageField('MacSignRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsMacVerifyRequest(_messages.Message):
  r"""A
  CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsMacVerifyRequest
  object.

  Fields:
    macVerifyRequest: A MacVerifyRequest resource to be passed as the request
      body.
    name: Required. The resource name of the CryptoKeyVersion to use for
      verification.
  """

  macVerifyRequest = _messages.MessageField('MacVerifyRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsPatchRequest(_messages.Message):
  r"""A
  CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsPatchRequest
  object.

  Fields:
    cryptoKeyVersion: A CryptoKeyVersion resource to be passed as the request
      body.
    name: Output only. The resource name for this CryptoKeyVersion in the
      format
      `projects/*/locations/*/keyRings/*/cryptoKeys/*/cryptoKeyVersions/*`.
    updateMask: Required. List of fields to be updated in this request.
  """

  cryptoKeyVersion = _messages.MessageField('CryptoKeyVersion', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsRawDecryptRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsRawDecrypt
  Request object.

  Fields:
    name: Required. The resource name of the CryptoKeyVersion to use for
      decryption.
    rawDecryptRequest: A RawDecryptRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  rawDecryptRequest = _messages.MessageField('RawDecryptRequest', 2)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsRawEncryptRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsRawEncrypt
  Request object.

  Fields:
    name: Required. The resource name of the CryptoKeyVersion to use for
      encryption.
    rawEncryptRequest: A RawEncryptRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  rawEncryptRequest = _messages.MessageField('RawEncryptRequest', 2)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsRestoreRequest(_messages.Message):
  r"""A
  CloudkmsProjectsLocationsKeyRingsCryptoKeysCryptoKeyVersionsRestoreRequest
  object.

  Fields:
    name: Required. The resource name of the CryptoKeyVersion to restore.
    restoreCryptoKeyVersionRequest: A RestoreCryptoKeyVersionRequest resource
      to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  restoreCryptoKeyVersionRequest = _messages.MessageField('RestoreCryptoKeyVersionRequest', 2)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysDecryptRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsCryptoKeysDecryptRequest object.

  Fields:
    decryptRequest: A DecryptRequest resource to be passed as the request
      body.
    name: Required. The resource name of the CryptoKey to use for decryption.
      The server will choose the appropriate version.
  """

  decryptRequest = _messages.MessageField('DecryptRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysEncryptRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsCryptoKeysEncryptRequest object.

  Fields:
    encryptRequest: A EncryptRequest resource to be passed as the request
      body.
    name: Required. The resource name of the CryptoKey or CryptoKeyVersion to
      use for encryption. If a CryptoKey is specified, the server will use its
      primary version.
  """

  encryptRequest = _messages.MessageField('EncryptRequest', 1)
  name = _messages.StringField(2, required=True)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysGetIamPolicyRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsCryptoKeysGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysGetRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsCryptoKeysGetRequest object.

  Fields:
    name: Required. The name of the CryptoKey to get.
  """

  name = _messages.StringField(1, required=True)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysListRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsCryptoKeysListRequest object.

  Enums:
    VersionViewValueValuesEnum: The fields of the primary version to include
      in the response.

  Fields:
    filter: Optional. Only include resources that match the filter in the
      response. For more information, see [Sorting and filtering list
      results](https://cloud.google.com/kms/docs/sorting-and-filtering).
    orderBy: Optional. Specify how the results should be sorted. If not
      specified, the results will be sorted in the default order. For more
      information, see [Sorting and filtering list
      results](https://cloud.google.com/kms/docs/sorting-and-filtering).
    pageSize: Optional. Optional limit on the number of CryptoKeys to include
      in the response. Further CryptoKeys can subsequently be obtained by
      including the ListCryptoKeysResponse.next_page_token in a subsequent
      request. If unspecified, the server will pick an appropriate default.
    pageToken: Optional. Optional pagination token, returned earlier via
      ListCryptoKeysResponse.next_page_token.
    parent: Required. The resource name of the KeyRing to list, in the format
      `projects/*/locations/*/keyRings/*`.
    versionView: The fields of the primary version to include in the response.
  """

  class VersionViewValueValuesEnum(_messages.Enum):
    r"""The fields of the primary version to include in the response.

    Values:
      CRYPTO_KEY_VERSION_VIEW_UNSPECIFIED: Default view for each
        CryptoKeyVersion. Does not include the attestation field.
      FULL: Provides all fields in each CryptoKeyVersion, including the
        attestation.
    """
    CRYPTO_KEY_VERSION_VIEW_UNSPECIFIED = 0
    FULL = 1

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  versionView = _messages.EnumField('VersionViewValueValuesEnum', 6)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysPatchRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsCryptoKeysPatchRequest object.

  Fields:
    cryptoKey: A CryptoKey resource to be passed as the request body.
    name: Output only. The resource name for this CryptoKey in the format
      `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
    updateMask: Required. List of fields to be updated in this request.
  """

  cryptoKey = _messages.MessageField('CryptoKey', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysSetIamPolicyRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsCryptoKeysSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysTestIamPermissionsRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsCryptoKeysTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class CloudkmsProjectsLocationsKeyRingsCryptoKeysUpdatePrimaryVersionRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsCryptoKeysUpdatePrimaryVersionRequest
  object.

  Fields:
    name: Required. The resource name of the CryptoKey to update.
    updateCryptoKeyPrimaryVersionRequest: A
      UpdateCryptoKeyPrimaryVersionRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  updateCryptoKeyPrimaryVersionRequest = _messages.MessageField('UpdateCryptoKeyPrimaryVersionRequest', 2)


class CloudkmsProjectsLocationsKeyRingsGetIamPolicyRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class CloudkmsProjectsLocationsKeyRingsGetRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsGetRequest object.

  Fields:
    name: Required. The name of the KeyRing to get.
  """

  name = _messages.StringField(1, required=True)


class CloudkmsProjectsLocationsKeyRingsImportJobsCreateRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsImportJobsCreateRequest object.

  Fields:
    importJob: A ImportJob resource to be passed as the request body.
    importJobId: Required. It must be unique within a KeyRing and match the
      regular expression `[a-zA-Z0-9_-]{1,63}`
    parent: Required. The name of the KeyRing associated with the ImportJobs.
  """

  importJob = _messages.MessageField('ImportJob', 1)
  importJobId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class CloudkmsProjectsLocationsKeyRingsImportJobsGetIamPolicyRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsImportJobsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class CloudkmsProjectsLocationsKeyRingsImportJobsGetRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsImportJobsGetRequest object.

  Fields:
    name: Required. The name of the ImportJob to get.
  """

  name = _messages.StringField(1, required=True)


class CloudkmsProjectsLocationsKeyRingsImportJobsListRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsImportJobsListRequest object.

  Fields:
    filter: Optional. Only include resources that match the filter in the
      response. For more information, see [Sorting and filtering list
      results](https://cloud.google.com/kms/docs/sorting-and-filtering).
    orderBy: Optional. Specify how the results should be sorted. If not
      specified, the results will be sorted in the default order. For more
      information, see [Sorting and filtering list
      results](https://cloud.google.com/kms/docs/sorting-and-filtering).
    pageSize: Optional. Optional limit on the number of ImportJobs to include
      in the response. Further ImportJobs can subsequently be obtained by
      including the ListImportJobsResponse.next_page_token in a subsequent
      request. If unspecified, the server will pick an appropriate default.
    pageToken: Optional. Optional pagination token, returned earlier via
      ListImportJobsResponse.next_page_token.
    parent: Required. The resource name of the KeyRing to list, in the format
      `projects/*/locations/*/keyRings/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class CloudkmsProjectsLocationsKeyRingsImportJobsSetIamPolicyRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsImportJobsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class CloudkmsProjectsLocationsKeyRingsImportJobsTestIamPermissionsRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsImportJobsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class CloudkmsProjectsLocationsKeyRingsListRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsListRequest object.

  Fields:
    filter: Optional. Only include resources that match the filter in the
      response. For more information, see [Sorting and filtering list
      results](https://cloud.google.com/kms/docs/sorting-and-filtering).
    orderBy: Optional. Specify how the results should be sorted. If not
      specified, the results will be sorted in the default order. For more
      information, see [Sorting and filtering list
      results](https://cloud.google.com/kms/docs/sorting-and-filtering).
    pageSize: Optional. Optional limit on the number of KeyRings to include in
      the response. Further KeyRings can subsequently be obtained by including
      the ListKeyRingsResponse.next_page_token in a subsequent request. If
      unspecified, the server will pick an appropriate default.
    pageToken: Optional. Optional pagination token, returned earlier via
      ListKeyRingsResponse.next_page_token.
    parent: Required. The resource name of the location associated with the
      KeyRings, in the format `projects/*/locations/*`.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class CloudkmsProjectsLocationsKeyRingsSetIamPolicyRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class CloudkmsProjectsLocationsKeyRingsTestIamPermissionsRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsKeyRingsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class CloudkmsProjectsLocationsListRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class CloudkmsProjectsLocationsUpdateEkmConfigRequest(_messages.Message):
  r"""A CloudkmsProjectsLocationsUpdateEkmConfigRequest object.

  Fields:
    ekmConfig: A EkmConfig resource to be passed as the request body.
    name: Output only. The resource name for the EkmConfig in the format
      `projects/*/locations/*/ekmConfig`.
    updateMask: Required. List of fields to be updated in this request.
  """

  ekmConfig = _messages.MessageField('EkmConfig', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class CryptoKey(_messages.Message):
  r"""A CryptoKey represents a logical key that can be used for cryptographic
  operations. A CryptoKey is made up of zero or more versions, which represent
  the actual key material used in cryptographic operations.

  Enums:
    PurposeValueValuesEnum: Immutable. The immutable purpose of this
      CryptoKey.

  Messages:
    LabelsValue: Labels with user-defined metadata. For more information, see
      [Labeling Keys](https://cloud.google.com/kms/docs/labeling-keys).

  Fields:
    createTime: Output only. The time at which this CryptoKey was created.
    cryptoKeyBackend: Immutable. The resource name of the backend environment
      where the key material for all CryptoKeyVersions associated with this
      CryptoKey reside and where all related cryptographic operations are
      performed. Only applicable if CryptoKeyVersions have a ProtectionLevel
      of EXTERNAL_VPC, with the resource name in the format
      `projects/*/locations/*/ekmConnections/*`. Note, this list is non-
      exhaustive and may apply to additional ProtectionLevels in the future.
    destroyScheduledDuration: Immutable. The period of time that versions of
      this key spend in the DESTROY_SCHEDULED state before transitioning to
      DESTROYED. If not specified at creation time, the default duration is 24
      hours.
    importOnly: Immutable. Whether this key may contain imported versions
      only.
    labels: Labels with user-defined metadata. For more information, see
      [Labeling Keys](https://cloud.google.com/kms/docs/labeling-keys).
    name: Output only. The resource name for this CryptoKey in the format
      `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
    nextRotationTime: At next_rotation_time, the Key Management Service will
      automatically: 1. Create a new version of this CryptoKey. 2. Mark the
      new version as primary. Key rotations performed manually via
      CreateCryptoKeyVersion and UpdateCryptoKeyPrimaryVersion do not affect
      next_rotation_time. Keys with purpose ENCRYPT_DECRYPT support automatic
      rotation. For other keys, this field must be omitted.
    primary: Output only. A copy of the "primary" CryptoKeyVersion that will
      be used by Encrypt when this CryptoKey is given in EncryptRequest.name.
      The CryptoKey's primary version can be updated via
      UpdateCryptoKeyPrimaryVersion. Keys with purpose ENCRYPT_DECRYPT may
      have a primary. For other keys, this field will be omitted.
    purpose: Immutable. The immutable purpose of this CryptoKey.
    rotationPeriod: next_rotation_time will be advanced by this period when
      the service automatically rotates a key. Must be at least 24 hours and
      at most 876,000 hours. If rotation_period is set, next_rotation_time
      must also be set. Keys with purpose ENCRYPT_DECRYPT support automatic
      rotation. For other keys, this field must be omitted.
    versionTemplate: A template describing settings for new CryptoKeyVersion
      instances. The properties of new CryptoKeyVersion instances created by
      either CreateCryptoKeyVersion or auto-rotation are controlled by this
      template.
  """

  class PurposeValueValuesEnum(_messages.Enum):
    r"""Immutable. The immutable purpose of this CryptoKey.

    Values:
      CRYPTO_KEY_PURPOSE_UNSPECIFIED: Not specified.
      ENCRYPT_DECRYPT: CryptoKeys with this purpose may be used with Encrypt
        and Decrypt.
      ASYMMETRIC_SIGN: CryptoKeys with this purpose may be used with
        AsymmetricSign and GetPublicKey.
      ASYMMETRIC_DECRYPT: CryptoKeys with this purpose may be used with
        AsymmetricDecrypt and GetPublicKey.
      RAW_ENCRYPT_DECRYPT: CryptoKeys with this purpose may be used with
        RawEncrypt and RawDecrypt. This purpose is meant to be used for
        interoperable symmetric encryption and does not support automatic
        CryptoKey rotation.
      MAC: CryptoKeys with this purpose may be used with MacSign.
    """
    CRYPTO_KEY_PURPOSE_UNSPECIFIED = 0
    ENCRYPT_DECRYPT = 1
    ASYMMETRIC_SIGN = 2
    ASYMMETRIC_DECRYPT = 3
    RAW_ENCRYPT_DECRYPT = 4
    MAC = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels with user-defined metadata. For more information, see [Labeling
    Keys](https://cloud.google.com/kms/docs/labeling-keys).

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  cryptoKeyBackend = _messages.StringField(2)
  destroyScheduledDuration = _messages.StringField(3)
  importOnly = _messages.BooleanField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  nextRotationTime = _messages.StringField(7)
  primary = _messages.MessageField('CryptoKeyVersion', 8)
  purpose = _messages.EnumField('PurposeValueValuesEnum', 9)
  rotationPeriod = _messages.StringField(10)
  versionTemplate = _messages.MessageField('CryptoKeyVersionTemplate', 11)


class CryptoKeyVersion(_messages.Message):
  r"""A CryptoKeyVersion represents an individual cryptographic key, and the
  associated key material. An ENABLED version can be used for cryptographic
  operations. For security reasons, the raw cryptographic key material
  represented by a CryptoKeyVersion can never be viewed or exported. It can
  only be used to encrypt, decrypt, or sign data when an authorized user or
  application invokes Cloud KMS.

  Enums:
    AlgorithmValueValuesEnum: Output only. The CryptoKeyVersionAlgorithm that
      this CryptoKeyVersion supports.
    ProtectionLevelValueValuesEnum: Output only. The ProtectionLevel
      describing how crypto operations are performed with this
      CryptoKeyVersion.
    StateValueValuesEnum: The current state of the CryptoKeyVersion.

  Fields:
    algorithm: Output only. The CryptoKeyVersionAlgorithm that this
      CryptoKeyVersion supports.
    attestation: Output only. Statement that was generated and signed by the
      HSM at key creation time. Use this statement to verify attributes of the
      key as stored on the HSM, independently of Google. Only provided for key
      versions with protection_level HSM.
    createTime: Output only. The time at which this CryptoKeyVersion was
      created.
    destroyEventTime: Output only. The time this CryptoKeyVersion's key
      material was destroyed. Only present if state is DESTROYED.
    destroyTime: Output only. The time this CryptoKeyVersion's key material is
      scheduled for destruction. Only present if state is DESTROY_SCHEDULED.
    externalDestructionFailureReason: Output only. The root cause of the most
      recent external destruction failure. Only present if state is
      EXTERNAL_DESTRUCTION_FAILED.
    externalProtectionLevelOptions: ExternalProtectionLevelOptions stores a
      group of additional fields for configuring a CryptoKeyVersion that are
      specific to the EXTERNAL protection level and EXTERNAL_VPC protection
      levels.
    generateTime: Output only. The time this CryptoKeyVersion's key material
      was generated.
    generationFailureReason: Output only. The root cause of the most recent
      generation failure. Only present if state is GENERATION_FAILED.
    importFailureReason: Output only. The root cause of the most recent import
      failure. Only present if state is IMPORT_FAILED.
    importJob: Output only. The name of the ImportJob used in the most recent
      import of this CryptoKeyVersion. Only present if the underlying key
      material was imported.
    importTime: Output only. The time at which this CryptoKeyVersion's key
      material was most recently imported.
    name: Output only. The resource name for this CryptoKeyVersion in the
      format
      `projects/*/locations/*/keyRings/*/cryptoKeys/*/cryptoKeyVersions/*`.
    protectionLevel: Output only. The ProtectionLevel describing how crypto
      operations are performed with this CryptoKeyVersion.
    reimportEligible: Output only. Whether or not this key version is eligible
      for reimport, by being specified as a target in
      ImportCryptoKeyVersionRequest.crypto_key_version.
    state: The current state of the CryptoKeyVersion.
  """

  class AlgorithmValueValuesEnum(_messages.Enum):
    r"""Output only. The CryptoKeyVersionAlgorithm that this CryptoKeyVersion
    supports.

    Values:
      CRYPTO_KEY_VERSION_ALGORITHM_UNSPECIFIED: Not specified.
      GOOGLE_SYMMETRIC_ENCRYPTION: Creates symmetric encryption keys.
      AES_128_GCM: AES-GCM (Galois Counter Mode) using 128-bit keys.
      AES_256_GCM: AES-GCM (Galois Counter Mode) using 256-bit keys.
      AES_128_CBC: AES-CBC (Cipher Block Chaining Mode) using 128-bit keys.
      AES_256_CBC: AES-CBC (Cipher Block Chaining Mode) using 256-bit keys.
      AES_128_CTR: AES-CTR (Counter Mode) using 128-bit keys.
      AES_256_CTR: AES-CTR (Counter Mode) using 256-bit keys.
      RSA_SIGN_PSS_2048_SHA256: RSASSA-PSS 2048 bit key with a SHA256 digest.
      RSA_SIGN_PSS_3072_SHA256: RSASSA-PSS 3072 bit key with a SHA256 digest.
      RSA_SIGN_PSS_4096_SHA256: RSASSA-PSS 4096 bit key with a SHA256 digest.
      RSA_SIGN_PSS_4096_SHA512: RSASSA-PSS 4096 bit key with a SHA512 digest.
      RSA_SIGN_PKCS1_2048_SHA256: RSASSA-PKCS1-v1_5 with a 2048 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_3072_SHA256: RSASSA-PKCS1-v1_5 with a 3072 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_4096_SHA256: RSASSA-PKCS1-v1_5 with a 4096 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_4096_SHA512: RSASSA-PKCS1-v1_5 with a 4096 bit key and a
        SHA512 digest.
      RSA_SIGN_RAW_PKCS1_2048: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 2048 bit key.
      RSA_SIGN_RAW_PKCS1_3072: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 3072 bit key.
      RSA_SIGN_RAW_PKCS1_4096: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 4096 bit key.
      RSA_DECRYPT_OAEP_2048_SHA256: RSAES-OAEP 2048 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_3072_SHA256: RSAES-OAEP 3072 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_4096_SHA256: RSAES-OAEP 4096 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_4096_SHA512: RSAES-OAEP 4096 bit key with a SHA512
        digest.
      RSA_DECRYPT_OAEP_2048_SHA1: RSAES-OAEP 2048 bit key with a SHA1 digest.
      RSA_DECRYPT_OAEP_3072_SHA1: RSAES-OAEP 3072 bit key with a SHA1 digest.
      RSA_DECRYPT_OAEP_4096_SHA1: RSAES-OAEP 4096 bit key with a SHA1 digest.
      EC_SIGN_P256_SHA256: ECDSA on the NIST P-256 curve with a SHA256 digest.
        Other hash functions can also be used:
        https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      EC_SIGN_P384_SHA384: ECDSA on the NIST P-384 curve with a SHA384 digest.
        Other hash functions can also be used:
        https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      EC_SIGN_SECP256K1_SHA256: ECDSA on the non-NIST secp256k1 curve. This
        curve is only supported for HSM protection level. Other hash functions
        can also be used: https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      HMAC_SHA256: HMAC-SHA256 signing with a 256 bit key.
      HMAC_SHA1: HMAC-SHA1 signing with a 160 bit key.
      HMAC_SHA384: HMAC-SHA384 signing with a 384 bit key.
      HMAC_SHA512: HMAC-SHA512 signing with a 512 bit key.
      HMAC_SHA224: HMAC-SHA224 signing with a 224 bit key.
      EXTERNAL_SYMMETRIC_ENCRYPTION: Algorithm representing symmetric
        encryption by an external key manager.
    """
    CRYPTO_KEY_VERSION_ALGORITHM_UNSPECIFIED = 0
    GOOGLE_SYMMETRIC_ENCRYPTION = 1
    AES_128_GCM = 2
    AES_256_GCM = 3
    AES_128_CBC = 4
    AES_256_CBC = 5
    AES_128_CTR = 6
    AES_256_CTR = 7
    RSA_SIGN_PSS_2048_SHA256 = 8
    RSA_SIGN_PSS_3072_SHA256 = 9
    RSA_SIGN_PSS_4096_SHA256 = 10
    RSA_SIGN_PSS_4096_SHA512 = 11
    RSA_SIGN_PKCS1_2048_SHA256 = 12
    RSA_SIGN_PKCS1_3072_SHA256 = 13
    RSA_SIGN_PKCS1_4096_SHA256 = 14
    RSA_SIGN_PKCS1_4096_SHA512 = 15
    RSA_SIGN_RAW_PKCS1_2048 = 16
    RSA_SIGN_RAW_PKCS1_3072 = 17
    RSA_SIGN_RAW_PKCS1_4096 = 18
    RSA_DECRYPT_OAEP_2048_SHA256 = 19
    RSA_DECRYPT_OAEP_3072_SHA256 = 20
    RSA_DECRYPT_OAEP_4096_SHA256 = 21
    RSA_DECRYPT_OAEP_4096_SHA512 = 22
    RSA_DECRYPT_OAEP_2048_SHA1 = 23
    RSA_DECRYPT_OAEP_3072_SHA1 = 24
    RSA_DECRYPT_OAEP_4096_SHA1 = 25
    EC_SIGN_P256_SHA256 = 26
    EC_SIGN_P384_SHA384 = 27
    EC_SIGN_SECP256K1_SHA256 = 28
    HMAC_SHA256 = 29
    HMAC_SHA1 = 30
    HMAC_SHA384 = 31
    HMAC_SHA512 = 32
    HMAC_SHA224 = 33
    EXTERNAL_SYMMETRIC_ENCRYPTION = 34

  class ProtectionLevelValueValuesEnum(_messages.Enum):
    r"""Output only. The ProtectionLevel describing how crypto operations are
    performed with this CryptoKeyVersion.

    Values:
      PROTECTION_LEVEL_UNSPECIFIED: Not specified.
      SOFTWARE: Crypto operations are performed in software.
      HSM: Crypto operations are performed in a Hardware Security Module.
      EXTERNAL: Crypto operations are performed by an external key manager.
      EXTERNAL_VPC: Crypto operations are performed in an EKM-over-VPC
        backend.
    """
    PROTECTION_LEVEL_UNSPECIFIED = 0
    SOFTWARE = 1
    HSM = 2
    EXTERNAL = 3
    EXTERNAL_VPC = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""The current state of the CryptoKeyVersion.

    Values:
      CRYPTO_KEY_VERSION_STATE_UNSPECIFIED: Not specified.
      PENDING_GENERATION: This version is still being generated. It may not be
        used, enabled, disabled, or destroyed yet. Cloud KMS will
        automatically mark this version ENABLED as soon as the version is
        ready.
      ENABLED: This version may be used for cryptographic operations.
      DISABLED: This version may not be used, but the key material is still
        available, and the version can be placed back into the ENABLED state.
      DESTROYED: This version is destroyed, and the key material is no longer
        stored. This version may only become ENABLED again if this version is
        reimport_eligible and the original key material is reimported with a
        call to KeyManagementService.ImportCryptoKeyVersion.
      DESTROY_SCHEDULED: This version is scheduled for destruction, and will
        be destroyed soon. Call RestoreCryptoKeyVersion to put it back into
        the DISABLED state.
      PENDING_IMPORT: This version is still being imported. It may not be
        used, enabled, disabled, or destroyed yet. Cloud KMS will
        automatically mark this version ENABLED as soon as the version is
        ready.
      IMPORT_FAILED: This version was not imported successfully. It may not be
        used, enabled, disabled, or destroyed. The submitted key material has
        been discarded. Additional details can be found in
        CryptoKeyVersion.import_failure_reason.
      GENERATION_FAILED: This version was not generated successfully. It may
        not be used, enabled, disabled, or destroyed. Additional details can
        be found in CryptoKeyVersion.generation_failure_reason.
      PENDING_EXTERNAL_DESTRUCTION: This version was destroyed, and it may not
        be used or enabled again. Cloud KMS is waiting for the corresponding
        key material residing in an external key manager to be destroyed.
      EXTERNAL_DESTRUCTION_FAILED: This version was destroyed, and it may not
        be used or enabled again. However, Cloud KMS could not confirm that
        the corresponding key material residing in an external key manager was
        destroyed. Additional details can be found in
        CryptoKeyVersion.external_destruction_failure_reason.
    """
    CRYPTO_KEY_VERSION_STATE_UNSPECIFIED = 0
    PENDING_GENERATION = 1
    ENABLED = 2
    DISABLED = 3
    DESTROYED = 4
    DESTROY_SCHEDULED = 5
    PENDING_IMPORT = 6
    IMPORT_FAILED = 7
    GENERATION_FAILED = 8
    PENDING_EXTERNAL_DESTRUCTION = 9
    EXTERNAL_DESTRUCTION_FAILED = 10

  algorithm = _messages.EnumField('AlgorithmValueValuesEnum', 1)
  attestation = _messages.MessageField('KeyOperationAttestation', 2)
  createTime = _messages.StringField(3)
  destroyEventTime = _messages.StringField(4)
  destroyTime = _messages.StringField(5)
  externalDestructionFailureReason = _messages.StringField(6)
  externalProtectionLevelOptions = _messages.MessageField('ExternalProtectionLevelOptions', 7)
  generateTime = _messages.StringField(8)
  generationFailureReason = _messages.StringField(9)
  importFailureReason = _messages.StringField(10)
  importJob = _messages.StringField(11)
  importTime = _messages.StringField(12)
  name = _messages.StringField(13)
  protectionLevel = _messages.EnumField('ProtectionLevelValueValuesEnum', 14)
  reimportEligible = _messages.BooleanField(15)
  state = _messages.EnumField('StateValueValuesEnum', 16)


class CryptoKeyVersionTemplate(_messages.Message):
  r"""A CryptoKeyVersionTemplate specifies the properties to use when creating
  a new CryptoKeyVersion, either manually with CreateCryptoKeyVersion or
  automatically as a result of auto-rotation.

  Enums:
    AlgorithmValueValuesEnum: Required. Algorithm to use when creating a
      CryptoKeyVersion based on this template. For backwards compatibility,
      GOOGLE_SYMMETRIC_ENCRYPTION is implied if both this field is omitted and
      CryptoKey.purpose is ENCRYPT_DECRYPT.
    ProtectionLevelValueValuesEnum: ProtectionLevel to use when creating a
      CryptoKeyVersion based on this template. Immutable. Defaults to
      SOFTWARE.

  Fields:
    algorithm: Required. Algorithm to use when creating a CryptoKeyVersion
      based on this template. For backwards compatibility,
      GOOGLE_SYMMETRIC_ENCRYPTION is implied if both this field is omitted and
      CryptoKey.purpose is ENCRYPT_DECRYPT.
    protectionLevel: ProtectionLevel to use when creating a CryptoKeyVersion
      based on this template. Immutable. Defaults to SOFTWARE.
  """

  class AlgorithmValueValuesEnum(_messages.Enum):
    r"""Required. Algorithm to use when creating a CryptoKeyVersion based on
    this template. For backwards compatibility, GOOGLE_SYMMETRIC_ENCRYPTION is
    implied if both this field is omitted and CryptoKey.purpose is
    ENCRYPT_DECRYPT.

    Values:
      CRYPTO_KEY_VERSION_ALGORITHM_UNSPECIFIED: Not specified.
      GOOGLE_SYMMETRIC_ENCRYPTION: Creates symmetric encryption keys.
      AES_128_GCM: AES-GCM (Galois Counter Mode) using 128-bit keys.
      AES_256_GCM: AES-GCM (Galois Counter Mode) using 256-bit keys.
      AES_128_CBC: AES-CBC (Cipher Block Chaining Mode) using 128-bit keys.
      AES_256_CBC: AES-CBC (Cipher Block Chaining Mode) using 256-bit keys.
      AES_128_CTR: AES-CTR (Counter Mode) using 128-bit keys.
      AES_256_CTR: AES-CTR (Counter Mode) using 256-bit keys.
      RSA_SIGN_PSS_2048_SHA256: RSASSA-PSS 2048 bit key with a SHA256 digest.
      RSA_SIGN_PSS_3072_SHA256: RSASSA-PSS 3072 bit key with a SHA256 digest.
      RSA_SIGN_PSS_4096_SHA256: RSASSA-PSS 4096 bit key with a SHA256 digest.
      RSA_SIGN_PSS_4096_SHA512: RSASSA-PSS 4096 bit key with a SHA512 digest.
      RSA_SIGN_PKCS1_2048_SHA256: RSASSA-PKCS1-v1_5 with a 2048 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_3072_SHA256: RSASSA-PKCS1-v1_5 with a 3072 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_4096_SHA256: RSASSA-PKCS1-v1_5 with a 4096 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_4096_SHA512: RSASSA-PKCS1-v1_5 with a 4096 bit key and a
        SHA512 digest.
      RSA_SIGN_RAW_PKCS1_2048: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 2048 bit key.
      RSA_SIGN_RAW_PKCS1_3072: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 3072 bit key.
      RSA_SIGN_RAW_PKCS1_4096: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 4096 bit key.
      RSA_DECRYPT_OAEP_2048_SHA256: RSAES-OAEP 2048 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_3072_SHA256: RSAES-OAEP 3072 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_4096_SHA256: RSAES-OAEP 4096 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_4096_SHA512: RSAES-OAEP 4096 bit key with a SHA512
        digest.
      RSA_DECRYPT_OAEP_2048_SHA1: RSAES-OAEP 2048 bit key with a SHA1 digest.
      RSA_DECRYPT_OAEP_3072_SHA1: RSAES-OAEP 3072 bit key with a SHA1 digest.
      RSA_DECRYPT_OAEP_4096_SHA1: RSAES-OAEP 4096 bit key with a SHA1 digest.
      EC_SIGN_P256_SHA256: ECDSA on the NIST P-256 curve with a SHA256 digest.
        Other hash functions can also be used:
        https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      EC_SIGN_P384_SHA384: ECDSA on the NIST P-384 curve with a SHA384 digest.
        Other hash functions can also be used:
        https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      EC_SIGN_SECP256K1_SHA256: ECDSA on the non-NIST secp256k1 curve. This
        curve is only supported for HSM protection level. Other hash functions
        can also be used: https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      HMAC_SHA256: HMAC-SHA256 signing with a 256 bit key.
      HMAC_SHA1: HMAC-SHA1 signing with a 160 bit key.
      HMAC_SHA384: HMAC-SHA384 signing with a 384 bit key.
      HMAC_SHA512: HMAC-SHA512 signing with a 512 bit key.
      HMAC_SHA224: HMAC-SHA224 signing with a 224 bit key.
      EXTERNAL_SYMMETRIC_ENCRYPTION: Algorithm representing symmetric
        encryption by an external key manager.
    """
    CRYPTO_KEY_VERSION_ALGORITHM_UNSPECIFIED = 0
    GOOGLE_SYMMETRIC_ENCRYPTION = 1
    AES_128_GCM = 2
    AES_256_GCM = 3
    AES_128_CBC = 4
    AES_256_CBC = 5
    AES_128_CTR = 6
    AES_256_CTR = 7
    RSA_SIGN_PSS_2048_SHA256 = 8
    RSA_SIGN_PSS_3072_SHA256 = 9
    RSA_SIGN_PSS_4096_SHA256 = 10
    RSA_SIGN_PSS_4096_SHA512 = 11
    RSA_SIGN_PKCS1_2048_SHA256 = 12
    RSA_SIGN_PKCS1_3072_SHA256 = 13
    RSA_SIGN_PKCS1_4096_SHA256 = 14
    RSA_SIGN_PKCS1_4096_SHA512 = 15
    RSA_SIGN_RAW_PKCS1_2048 = 16
    RSA_SIGN_RAW_PKCS1_3072 = 17
    RSA_SIGN_RAW_PKCS1_4096 = 18
    RSA_DECRYPT_OAEP_2048_SHA256 = 19
    RSA_DECRYPT_OAEP_3072_SHA256 = 20
    RSA_DECRYPT_OAEP_4096_SHA256 = 21
    RSA_DECRYPT_OAEP_4096_SHA512 = 22
    RSA_DECRYPT_OAEP_2048_SHA1 = 23
    RSA_DECRYPT_OAEP_3072_SHA1 = 24
    RSA_DECRYPT_OAEP_4096_SHA1 = 25
    EC_SIGN_P256_SHA256 = 26
    EC_SIGN_P384_SHA384 = 27
    EC_SIGN_SECP256K1_SHA256 = 28
    HMAC_SHA256 = 29
    HMAC_SHA1 = 30
    HMAC_SHA384 = 31
    HMAC_SHA512 = 32
    HMAC_SHA224 = 33
    EXTERNAL_SYMMETRIC_ENCRYPTION = 34

  class ProtectionLevelValueValuesEnum(_messages.Enum):
    r"""ProtectionLevel to use when creating a CryptoKeyVersion based on this
    template. Immutable. Defaults to SOFTWARE.

    Values:
      PROTECTION_LEVEL_UNSPECIFIED: Not specified.
      SOFTWARE: Crypto operations are performed in software.
      HSM: Crypto operations are performed in a Hardware Security Module.
      EXTERNAL: Crypto operations are performed by an external key manager.
      EXTERNAL_VPC: Crypto operations are performed in an EKM-over-VPC
        backend.
    """
    PROTECTION_LEVEL_UNSPECIFIED = 0
    SOFTWARE = 1
    HSM = 2
    EXTERNAL = 3
    EXTERNAL_VPC = 4

  algorithm = _messages.EnumField('AlgorithmValueValuesEnum', 1)
  protectionLevel = _messages.EnumField('ProtectionLevelValueValuesEnum', 2)


class DecryptRequest(_messages.Message):
  r"""Request message for KeyManagementService.Decrypt.

  Fields:
    additionalAuthenticatedData: Optional. Optional data that must match the
      data originally supplied in
      EncryptRequest.additional_authenticated_data.
    additionalAuthenticatedDataCrc32c: Optional. An optional CRC32C checksum
      of the DecryptRequest.additional_authenticated_data. If specified,
      KeyManagementService will verify the integrity of the received
      DecryptRequest.additional_authenticated_data using this checksum.
      KeyManagementService will report an error if the checksum verification
      fails. If you receive a checksum error, your client should verify that
      CRC32C(DecryptRequest.additional_authenticated_data) is equal to
      DecryptRequest.additional_authenticated_data_crc32c, and if so, perform
      a limited number of retries. A persistent mismatch may indicate an issue
      in your computation of the CRC32C checksum. Note: This field is defined
      as int64 for reasons of compatibility across different languages.
      However, it is a non-negative integer, which will never exceed 2^32-1,
      and can be safely downconverted to uint32 in languages that support this
      type.
    ciphertext: Required. The encrypted data originally returned in
      EncryptResponse.ciphertext.
    ciphertextCrc32c: Optional. An optional CRC32C checksum of the
      DecryptRequest.ciphertext. If specified, KeyManagementService will
      verify the integrity of the received DecryptRequest.ciphertext using
      this checksum. KeyManagementService will report an error if the checksum
      verification fails. If you receive a checksum error, your client should
      verify that CRC32C(DecryptRequest.ciphertext) is equal to
      DecryptRequest.ciphertext_crc32c, and if so, perform a limited number of
      retries. A persistent mismatch may indicate an issue in your computation
      of the CRC32C checksum. Note: This field is defined as int64 for reasons
      of compatibility across different languages. However, it is a non-
      negative integer, which will never exceed 2^32-1, and can be safely
      downconverted to uint32 in languages that support this type.
  """

  additionalAuthenticatedData = _messages.BytesField(1)
  additionalAuthenticatedDataCrc32c = _messages.IntegerField(2)
  ciphertext = _messages.BytesField(3)
  ciphertextCrc32c = _messages.IntegerField(4)


class DecryptResponse(_messages.Message):
  r"""Response message for KeyManagementService.Decrypt.

  Enums:
    ProtectionLevelValueValuesEnum: The ProtectionLevel of the
      CryptoKeyVersion used in decryption.

  Fields:
    plaintext: The decrypted data originally supplied in
      EncryptRequest.plaintext.
    plaintextCrc32c: Integrity verification field. A CRC32C checksum of the
      returned DecryptResponse.plaintext. An integrity check of
      DecryptResponse.plaintext can be performed by computing the CRC32C
      checksum of DecryptResponse.plaintext and comparing your results to this
      field. Discard the response in case of non-matching checksum values, and
      perform a limited number of retries. A persistent mismatch may indicate
      an issue in your computation of the CRC32C checksum. Note: receiving
      this response message indicates that KeyManagementService is able to
      successfully decrypt the ciphertext. Note: This field is defined as
      int64 for reasons of compatibility across different languages. However,
      it is a non-negative integer, which will never exceed 2^32-1, and can be
      safely downconverted to uint32 in languages that support this type.
    protectionLevel: The ProtectionLevel of the CryptoKeyVersion used in
      decryption.
    usedPrimary: Whether the Decryption was performed using the primary key
      version.
  """

  class ProtectionLevelValueValuesEnum(_messages.Enum):
    r"""The ProtectionLevel of the CryptoKeyVersion used in decryption.

    Values:
      PROTECTION_LEVEL_UNSPECIFIED: Not specified.
      SOFTWARE: Crypto operations are performed in software.
      HSM: Crypto operations are performed in a Hardware Security Module.
      EXTERNAL: Crypto operations are performed by an external key manager.
      EXTERNAL_VPC: Crypto operations are performed in an EKM-over-VPC
        backend.
    """
    PROTECTION_LEVEL_UNSPECIFIED = 0
    SOFTWARE = 1
    HSM = 2
    EXTERNAL = 3
    EXTERNAL_VPC = 4

  plaintext = _messages.BytesField(1)
  plaintextCrc32c = _messages.IntegerField(2)
  protectionLevel = _messages.EnumField('ProtectionLevelValueValuesEnum', 3)
  usedPrimary = _messages.BooleanField(4)


class DestroyCryptoKeyVersionRequest(_messages.Message):
  r"""Request message for KeyManagementService.DestroyCryptoKeyVersion."""


class Digest(_messages.Message):
  r"""A Digest holds a cryptographic message digest.

  Fields:
    sha256: A message digest produced with the SHA-256 algorithm.
    sha384: A message digest produced with the SHA-384 algorithm.
    sha512: A message digest produced with the SHA-512 algorithm.
  """

  sha256 = _messages.BytesField(1)
  sha384 = _messages.BytesField(2)
  sha512 = _messages.BytesField(3)


class EkmConfig(_messages.Message):
  r"""An EkmConfig is a singleton resource that represents configuration
  parameters that apply to all CryptoKeys and CryptoKeyVersions with a
  ProtectionLevel of EXTERNAL_VPC in a given project and location.

  Fields:
    defaultEkmConnection: Optional. Resource name of the default
      EkmConnection. Setting this field to the empty string removes the
      default.
    name: Output only. The resource name for the EkmConfig in the format
      `projects/*/locations/*/ekmConfig`.
  """

  defaultEkmConnection = _messages.StringField(1)
  name = _messages.StringField(2)


class EkmConnection(_messages.Message):
  r"""An EkmConnection represents an individual EKM connection. It can be used
  for creating CryptoKeys and CryptoKeyVersions with a ProtectionLevel of
  EXTERNAL_VPC, as well as performing cryptographic operations using keys
  created within the EkmConnection.

  Enums:
    KeyManagementModeValueValuesEnum: Optional. Describes who can perform
      control plane operations on the EKM. If unset, this defaults to MANUAL.

  Fields:
    createTime: Output only. The time at which the EkmConnection was created.
    cryptoSpacePath: Optional. Identifies the EKM Crypto Space that this
      EkmConnection maps to. Note: This field is required if KeyManagementMode
      is CLOUD_KMS.
    etag: Optional. Etag of the currently stored EkmConnection.
    keyManagementMode: Optional. Describes who can perform control plane
      operations on the EKM. If unset, this defaults to MANUAL.
    name: Output only. The resource name for the EkmConnection in the format
      `projects/*/locations/*/ekmConnections/*`.
    serviceResolvers: A list of ServiceResolvers where the EKM can be reached.
      There should be one ServiceResolver per EKM replica. Currently, only a
      single ServiceResolver is supported.
  """

  class KeyManagementModeValueValuesEnum(_messages.Enum):
    r"""Optional. Describes who can perform control plane operations on the
    EKM. If unset, this defaults to MANUAL.

    Values:
      KEY_MANAGEMENT_MODE_UNSPECIFIED: Not specified.
      MANUAL: EKM-side key management operations on CryptoKeys created with
        this EkmConnection must be initiated from the EKM directly and cannot
        be performed from Cloud KMS. This means that: * When creating a
        CryptoKeyVersion associated with this EkmConnection, the caller must
        supply the key path of pre-existing external key material that will be
        linked to the CryptoKeyVersion. * Destruction of external key material
        cannot be requested via the Cloud KMS API and must be performed
        directly in the EKM. * Automatic rotation of key material is not
        supported.
      CLOUD_KMS: All CryptoKeys created with this EkmConnection use EKM-side
        key management operations initiated from Cloud KMS. This means that: *
        When a CryptoKeyVersion associated with this EkmConnection is created,
        the EKM automatically generates new key material and a new key path.
        The caller cannot supply the key path of pre-existing external key
        material. * Destruction of external key material associated with this
        EkmConnection can be requested by calling DestroyCryptoKeyVersion. *
        Automatic rotation of key material is supported.
    """
    KEY_MANAGEMENT_MODE_UNSPECIFIED = 0
    MANUAL = 1
    CLOUD_KMS = 2

  createTime = _messages.StringField(1)
  cryptoSpacePath = _messages.StringField(2)
  etag = _messages.StringField(3)
  keyManagementMode = _messages.EnumField('KeyManagementModeValueValuesEnum', 4)
  name = _messages.StringField(5)
  serviceResolvers = _messages.MessageField('ServiceResolver', 6, repeated=True)


class EncryptRequest(_messages.Message):
  r"""Request message for KeyManagementService.Encrypt.

  Fields:
    additionalAuthenticatedData: Optional. Optional data that, if specified,
      must also be provided during decryption through
      DecryptRequest.additional_authenticated_data. The maximum size depends
      on the key version's protection_level. For SOFTWARE, EXTERNAL, and
      EXTERNAL_VPC keys the AAD must be no larger than 64KiB. For HSM keys,
      the combined length of the plaintext and additional_authenticated_data
      fields must be no larger than 8KiB.
    additionalAuthenticatedDataCrc32c: Optional. An optional CRC32C checksum
      of the EncryptRequest.additional_authenticated_data. If specified,
      KeyManagementService will verify the integrity of the received
      EncryptRequest.additional_authenticated_data using this checksum.
      KeyManagementService will report an error if the checksum verification
      fails. If you receive a checksum error, your client should verify that
      CRC32C(EncryptRequest.additional_authenticated_data) is equal to
      EncryptRequest.additional_authenticated_data_crc32c, and if so, perform
      a limited number of retries. A persistent mismatch may indicate an issue
      in your computation of the CRC32C checksum. Note: This field is defined
      as int64 for reasons of compatibility across different languages.
      However, it is a non-negative integer, which will never exceed 2^32-1,
      and can be safely downconverted to uint32 in languages that support this
      type.
    plaintext: Required. The data to encrypt. Must be no larger than 64KiB.
      The maximum size depends on the key version's protection_level. For
      SOFTWARE, EXTERNAL, and EXTERNAL_VPC keys, the plaintext must be no
      larger than 64KiB. For HSM keys, the combined length of the plaintext
      and additional_authenticated_data fields must be no larger than 8KiB.
    plaintextCrc32c: Optional. An optional CRC32C checksum of the
      EncryptRequest.plaintext. If specified, KeyManagementService will verify
      the integrity of the received EncryptRequest.plaintext using this
      checksum. KeyManagementService will report an error if the checksum
      verification fails. If you receive a checksum error, your client should
      verify that CRC32C(EncryptRequest.plaintext) is equal to
      EncryptRequest.plaintext_crc32c, and if so, perform a limited number of
      retries. A persistent mismatch may indicate an issue in your computation
      of the CRC32C checksum. Note: This field is defined as int64 for reasons
      of compatibility across different languages. However, it is a non-
      negative integer, which will never exceed 2^32-1, and can be safely
      downconverted to uint32 in languages that support this type.
  """

  additionalAuthenticatedData = _messages.BytesField(1)
  additionalAuthenticatedDataCrc32c = _messages.IntegerField(2)
  plaintext = _messages.BytesField(3)
  plaintextCrc32c = _messages.IntegerField(4)


class EncryptResponse(_messages.Message):
  r"""Response message for KeyManagementService.Encrypt.

  Enums:
    ProtectionLevelValueValuesEnum: The ProtectionLevel of the
      CryptoKeyVersion used in encryption.

  Fields:
    ciphertext: The encrypted data.
    ciphertextCrc32c: Integrity verification field. A CRC32C checksum of the
      returned EncryptResponse.ciphertext. An integrity check of
      EncryptResponse.ciphertext can be performed by computing the CRC32C
      checksum of EncryptResponse.ciphertext and comparing your results to
      this field. Discard the response in case of non-matching checksum
      values, and perform a limited number of retries. A persistent mismatch
      may indicate an issue in your computation of the CRC32C checksum. Note:
      This field is defined as int64 for reasons of compatibility across
      different languages. However, it is a non-negative integer, which will
      never exceed 2^32-1, and can be safely downconverted to uint32 in
      languages that support this type.
    name: The resource name of the CryptoKeyVersion used in encryption. Check
      this field to verify that the intended resource was used for encryption.
    protectionLevel: The ProtectionLevel of the CryptoKeyVersion used in
      encryption.
    verifiedAdditionalAuthenticatedDataCrc32c: Integrity verification field. A
      flag indicating whether
      EncryptRequest.additional_authenticated_data_crc32c was received by
      KeyManagementService and used for the integrity verification of the AAD.
      A false value of this field indicates either that
      EncryptRequest.additional_authenticated_data_crc32c was left unset or
      that it was not delivered to KeyManagementService. If you've set
      EncryptRequest.additional_authenticated_data_crc32c but this field is
      still false, discard the response and perform a limited number of
      retries.
    verifiedPlaintextCrc32c: Integrity verification field. A flag indicating
      whether EncryptRequest.plaintext_crc32c was received by
      KeyManagementService and used for the integrity verification of the
      plaintext. A false value of this field indicates either that
      EncryptRequest.plaintext_crc32c was left unset or that it was not
      delivered to KeyManagementService. If you've set
      EncryptRequest.plaintext_crc32c but this field is still false, discard
      the response and perform a limited number of retries.
  """

  class ProtectionLevelValueValuesEnum(_messages.Enum):
    r"""The ProtectionLevel of the CryptoKeyVersion used in encryption.

    Values:
      PROTECTION_LEVEL_UNSPECIFIED: Not specified.
      SOFTWARE: Crypto operations are performed in software.
      HSM: Crypto operations are performed in a Hardware Security Module.
      EXTERNAL: Crypto operations are performed by an external key manager.
      EXTERNAL_VPC: Crypto operations are performed in an EKM-over-VPC
        backend.
    """
    PROTECTION_LEVEL_UNSPECIFIED = 0
    SOFTWARE = 1
    HSM = 2
    EXTERNAL = 3
    EXTERNAL_VPC = 4

  ciphertext = _messages.BytesField(1)
  ciphertextCrc32c = _messages.IntegerField(2)
  name = _messages.StringField(3)
  protectionLevel = _messages.EnumField('ProtectionLevelValueValuesEnum', 4)
  verifiedAdditionalAuthenticatedDataCrc32c = _messages.BooleanField(5)
  verifiedPlaintextCrc32c = _messages.BooleanField(6)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class ExternalProtectionLevelOptions(_messages.Message):
  r"""ExternalProtectionLevelOptions stores a group of additional fields for
  configuring a CryptoKeyVersion that are specific to the EXTERNAL protection
  level and EXTERNAL_VPC protection levels.

  Fields:
    ekmConnectionKeyPath: The path to the external key material on the EKM
      when using EkmConnection e.g., "v0/my/key". Set this field instead of
      external_key_uri when using an EkmConnection.
    externalKeyUri: The URI for an external resource that this
      CryptoKeyVersion represents.
  """

  ekmConnectionKeyPath = _messages.StringField(1)
  externalKeyUri = _messages.StringField(2)


class GenerateRandomBytesRequest(_messages.Message):
  r"""Request message for KeyManagementService.GenerateRandomBytes.

  Enums:
    ProtectionLevelValueValuesEnum: The ProtectionLevel to use when generating
      the random data. Currently, only HSM protection level is supported.

  Fields:
    lengthBytes: The length in bytes of the amount of randomness to retrieve.
      Minimum 8 bytes, maximum 1024 bytes.
    protectionLevel: The ProtectionLevel to use when generating the random
      data. Currently, only HSM protection level is supported.
  """

  class ProtectionLevelValueValuesEnum(_messages.Enum):
    r"""The ProtectionLevel to use when generating the random data. Currently,
    only HSM protection level is supported.

    Values:
      PROTECTION_LEVEL_UNSPECIFIED: Not specified.
      SOFTWARE: Crypto operations are performed in software.
      HSM: Crypto operations are performed in a Hardware Security Module.
      EXTERNAL: Crypto operations are performed by an external key manager.
      EXTERNAL_VPC: Crypto operations are performed in an EKM-over-VPC
        backend.
    """
    PROTECTION_LEVEL_UNSPECIFIED = 0
    SOFTWARE = 1
    HSM = 2
    EXTERNAL = 3
    EXTERNAL_VPC = 4

  lengthBytes = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  protectionLevel = _messages.EnumField('ProtectionLevelValueValuesEnum', 2)


class GenerateRandomBytesResponse(_messages.Message):
  r"""Response message for KeyManagementService.GenerateRandomBytes.

  Fields:
    data: The generated data.
    dataCrc32c: Integrity verification field. A CRC32C checksum of the
      returned GenerateRandomBytesResponse.data. An integrity check of
      GenerateRandomBytesResponse.data can be performed by computing the
      CRC32C checksum of GenerateRandomBytesResponse.data and comparing your
      results to this field. Discard the response in case of non-matching
      checksum values, and perform a limited number of retries. A persistent
      mismatch may indicate an issue in your computation of the CRC32C
      checksum. Note: This field is defined as int64 for reasons of
      compatibility across different languages. However, it is a non-negative
      integer, which will never exceed 2^32-1, and can be safely downconverted
      to uint32 in languages that support this type.
  """

  data = _messages.BytesField(1)
  dataCrc32c = _messages.IntegerField(2)


class ImportCryptoKeyVersionRequest(_messages.Message):
  r"""Request message for KeyManagementService.ImportCryptoKeyVersion.

  Enums:
    AlgorithmValueValuesEnum: Required. The algorithm of the key being
      imported. This does not need to match the version_template of the
      CryptoKey this version imports into.

  Fields:
    algorithm: Required. The algorithm of the key being imported. This does
      not need to match the version_template of the CryptoKey this version
      imports into.
    cryptoKeyVersion: Optional. The optional name of an existing
      CryptoKeyVersion to target for an import operation. If this field is not
      present, a new CryptoKeyVersion containing the supplied key material is
      created. If this field is present, the supplied key material is imported
      into the existing CryptoKeyVersion. To import into an existing
      CryptoKeyVersion, the CryptoKeyVersion must be a child of
      ImportCryptoKeyVersionRequest.parent, have been previously created via
      ImportCryptoKeyVersion, and be in DESTROYED or IMPORT_FAILED state. The
      key material and algorithm must match the previous CryptoKeyVersion
      exactly if the CryptoKeyVersion has ever contained key material.
    importJob: Required. The name of the ImportJob that was used to wrap this
      key material.
    rsaAesWrappedKey: Optional. This field has the same meaning as
      wrapped_key. Prefer to use that field in new work. Either that field or
      this field (but not both) must be specified.
    wrappedKey: Optional. The wrapped key material to import. Before wrapping,
      key material must be formatted. If importing symmetric key material, the
      expected key material format is plain bytes. If importing asymmetric key
      material, the expected key material format is PKCS#8-encoded DER (the
      PrivateKeyInfo structure from RFC 5208). When wrapping with import
      methods (RSA_OAEP_3072_SHA1_AES_256 or RSA_OAEP_4096_SHA1_AES_256 or
      RSA_OAEP_3072_SHA256_AES_256 or RSA_OAEP_4096_SHA256_AES_256), this
      field must contain the concatenation of: 1. An ephemeral AES-256
      wrapping key wrapped with the public_key using RSAES-OAEP with
      SHA-1/SHA-256, MGF1 with SHA-1/SHA-256, and an empty label. 2. The
      formatted key to be imported, wrapped with the ephemeral AES-256 key
      using AES-KWP (RFC 5649). This format is the same as the format produced
      by PKCS#11 mechanism CKM_RSA_AES_KEY_WRAP. When wrapping with import
      methods (RSA_OAEP_3072_SHA256 or RSA_OAEP_4096_SHA256), this field must
      contain the formatted key to be imported, wrapped with the public_key
      using RSAES-OAEP with SHA-256, MGF1 with SHA-256, and an empty label.
  """

  class AlgorithmValueValuesEnum(_messages.Enum):
    r"""Required. The algorithm of the key being imported. This does not need
    to match the version_template of the CryptoKey this version imports into.

    Values:
      CRYPTO_KEY_VERSION_ALGORITHM_UNSPECIFIED: Not specified.
      GOOGLE_SYMMETRIC_ENCRYPTION: Creates symmetric encryption keys.
      AES_128_GCM: AES-GCM (Galois Counter Mode) using 128-bit keys.
      AES_256_GCM: AES-GCM (Galois Counter Mode) using 256-bit keys.
      AES_128_CBC: AES-CBC (Cipher Block Chaining Mode) using 128-bit keys.
      AES_256_CBC: AES-CBC (Cipher Block Chaining Mode) using 256-bit keys.
      AES_128_CTR: AES-CTR (Counter Mode) using 128-bit keys.
      AES_256_CTR: AES-CTR (Counter Mode) using 256-bit keys.
      RSA_SIGN_PSS_2048_SHA256: RSASSA-PSS 2048 bit key with a SHA256 digest.
      RSA_SIGN_PSS_3072_SHA256: RSASSA-PSS 3072 bit key with a SHA256 digest.
      RSA_SIGN_PSS_4096_SHA256: RSASSA-PSS 4096 bit key with a SHA256 digest.
      RSA_SIGN_PSS_4096_SHA512: RSASSA-PSS 4096 bit key with a SHA512 digest.
      RSA_SIGN_PKCS1_2048_SHA256: RSASSA-PKCS1-v1_5 with a 2048 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_3072_SHA256: RSASSA-PKCS1-v1_5 with a 3072 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_4096_SHA256: RSASSA-PKCS1-v1_5 with a 4096 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_4096_SHA512: RSASSA-PKCS1-v1_5 with a 4096 bit key and a
        SHA512 digest.
      RSA_SIGN_RAW_PKCS1_2048: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 2048 bit key.
      RSA_SIGN_RAW_PKCS1_3072: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 3072 bit key.
      RSA_SIGN_RAW_PKCS1_4096: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 4096 bit key.
      RSA_DECRYPT_OAEP_2048_SHA256: RSAES-OAEP 2048 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_3072_SHA256: RSAES-OAEP 3072 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_4096_SHA256: RSAES-OAEP 4096 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_4096_SHA512: RSAES-OAEP 4096 bit key with a SHA512
        digest.
      RSA_DECRYPT_OAEP_2048_SHA1: RSAES-OAEP 2048 bit key with a SHA1 digest.
      RSA_DECRYPT_OAEP_3072_SHA1: RSAES-OAEP 3072 bit key with a SHA1 digest.
      RSA_DECRYPT_OAEP_4096_SHA1: RSAES-OAEP 4096 bit key with a SHA1 digest.
      EC_SIGN_P256_SHA256: ECDSA on the NIST P-256 curve with a SHA256 digest.
        Other hash functions can also be used:
        https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      EC_SIGN_P384_SHA384: ECDSA on the NIST P-384 curve with a SHA384 digest.
        Other hash functions can also be used:
        https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      EC_SIGN_SECP256K1_SHA256: ECDSA on the non-NIST secp256k1 curve. This
        curve is only supported for HSM protection level. Other hash functions
        can also be used: https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      HMAC_SHA256: HMAC-SHA256 signing with a 256 bit key.
      HMAC_SHA1: HMAC-SHA1 signing with a 160 bit key.
      HMAC_SHA384: HMAC-SHA384 signing with a 384 bit key.
      HMAC_SHA512: HMAC-SHA512 signing with a 512 bit key.
      HMAC_SHA224: HMAC-SHA224 signing with a 224 bit key.
      EXTERNAL_SYMMETRIC_ENCRYPTION: Algorithm representing symmetric
        encryption by an external key manager.
    """
    CRYPTO_KEY_VERSION_ALGORITHM_UNSPECIFIED = 0
    GOOGLE_SYMMETRIC_ENCRYPTION = 1
    AES_128_GCM = 2
    AES_256_GCM = 3
    AES_128_CBC = 4
    AES_256_CBC = 5
    AES_128_CTR = 6
    AES_256_CTR = 7
    RSA_SIGN_PSS_2048_SHA256 = 8
    RSA_SIGN_PSS_3072_SHA256 = 9
    RSA_SIGN_PSS_4096_SHA256 = 10
    RSA_SIGN_PSS_4096_SHA512 = 11
    RSA_SIGN_PKCS1_2048_SHA256 = 12
    RSA_SIGN_PKCS1_3072_SHA256 = 13
    RSA_SIGN_PKCS1_4096_SHA256 = 14
    RSA_SIGN_PKCS1_4096_SHA512 = 15
    RSA_SIGN_RAW_PKCS1_2048 = 16
    RSA_SIGN_RAW_PKCS1_3072 = 17
    RSA_SIGN_RAW_PKCS1_4096 = 18
    RSA_DECRYPT_OAEP_2048_SHA256 = 19
    RSA_DECRYPT_OAEP_3072_SHA256 = 20
    RSA_DECRYPT_OAEP_4096_SHA256 = 21
    RSA_DECRYPT_OAEP_4096_SHA512 = 22
    RSA_DECRYPT_OAEP_2048_SHA1 = 23
    RSA_DECRYPT_OAEP_3072_SHA1 = 24
    RSA_DECRYPT_OAEP_4096_SHA1 = 25
    EC_SIGN_P256_SHA256 = 26
    EC_SIGN_P384_SHA384 = 27
    EC_SIGN_SECP256K1_SHA256 = 28
    HMAC_SHA256 = 29
    HMAC_SHA1 = 30
    HMAC_SHA384 = 31
    HMAC_SHA512 = 32
    HMAC_SHA224 = 33
    EXTERNAL_SYMMETRIC_ENCRYPTION = 34

  algorithm = _messages.EnumField('AlgorithmValueValuesEnum', 1)
  cryptoKeyVersion = _messages.StringField(2)
  importJob = _messages.StringField(3)
  rsaAesWrappedKey = _messages.BytesField(4)
  wrappedKey = _messages.BytesField(5)


class ImportJob(_messages.Message):
  r"""An ImportJob can be used to create CryptoKeys and CryptoKeyVersions
  using pre-existing key material, generated outside of Cloud KMS. When an
  ImportJob is created, Cloud KMS will generate a "wrapping key", which is a
  public/private key pair. You use the wrapping key to encrypt (also known as
  wrap) the pre-existing key material to protect it during the import process.
  The nature of the wrapping key depends on the choice of import_method. When
  the wrapping key generation is complete, the state will be set to ACTIVE and
  the public_key can be fetched. The fetched public key can then be used to
  wrap your pre-existing key material. Once the key material is wrapped, it
  can be imported into a new CryptoKeyVersion in an existing CryptoKey by
  calling ImportCryptoKeyVersion. Multiple CryptoKeyVersions can be imported
  with a single ImportJob. Cloud KMS uses the private key portion of the
  wrapping key to unwrap the key material. Only Cloud KMS has access to the
  private key. An ImportJob expires 3 days after it is created. Once expired,
  Cloud KMS will no longer be able to import or unwrap any key material that
  was wrapped with the ImportJob's public key. For more information, see
  [Importing a key](https://cloud.google.com/kms/docs/importing-a-key).

  Enums:
    ImportMethodValueValuesEnum: Required. Immutable. The wrapping method to
      be used for incoming key material.
    ProtectionLevelValueValuesEnum: Required. Immutable. The protection level
      of the ImportJob. This must match the protection_level of the
      version_template on the CryptoKey you attempt to import into.
    StateValueValuesEnum: Output only. The current state of the ImportJob,
      indicating if it can be used.

  Fields:
    attestation: Output only. Statement that was generated and signed by the
      key creator (for example, an HSM) at key creation time. Use this
      statement to verify attributes of the key as stored on the HSM,
      independently of Google. Only present if the chosen ImportMethod is one
      with a protection level of HSM.
    createTime: Output only. The time at which this ImportJob was created.
    expireEventTime: Output only. The time this ImportJob expired. Only
      present if state is EXPIRED.
    expireTime: Output only. The time at which this ImportJob is scheduled for
      expiration and can no longer be used to import key material.
    generateTime: Output only. The time this ImportJob's key material was
      generated.
    importMethod: Required. Immutable. The wrapping method to be used for
      incoming key material.
    name: Output only. The resource name for this ImportJob in the format
      `projects/*/locations/*/keyRings/*/importJobs/*`.
    protectionLevel: Required. Immutable. The protection level of the
      ImportJob. This must match the protection_level of the version_template
      on the CryptoKey you attempt to import into.
    publicKey: Output only. The public key with which to wrap key material
      prior to import. Only returned if state is ACTIVE.
    state: Output only. The current state of the ImportJob, indicating if it
      can be used.
  """

  class ImportMethodValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. The wrapping method to be used for incoming key
    material.

    Values:
      IMPORT_METHOD_UNSPECIFIED: Not specified.
      RSA_OAEP_3072_SHA1_AES_256: This ImportMethod represents the
        CKM_RSA_AES_KEY_WRAP key wrapping scheme defined in the PKCS #11
        standard. In summary, this involves wrapping the raw key with an
        ephemeral AES key, and wrapping the ephemeral AES key with a 3072 bit
        RSA key. For more details, see [RSA AES key wrap
        mechanism](http://docs.oasis-open.org/pkcs11/pkcs11-
        curr/v2.40/cos01/pkcs11-curr-v2.40-cos01.html#_Toc408226908).
      RSA_OAEP_4096_SHA1_AES_256: This ImportMethod represents the
        CKM_RSA_AES_KEY_WRAP key wrapping scheme defined in the PKCS #11
        standard. In summary, this involves wrapping the raw key with an
        ephemeral AES key, and wrapping the ephemeral AES key with a 4096 bit
        RSA key. For more details, see [RSA AES key wrap
        mechanism](http://docs.oasis-open.org/pkcs11/pkcs11-
        curr/v2.40/cos01/pkcs11-curr-v2.40-cos01.html#_Toc408226908).
      RSA_OAEP_3072_SHA256_AES_256: This ImportMethod represents the
        CKM_RSA_AES_KEY_WRAP key wrapping scheme defined in the PKCS #11
        standard. In summary, this involves wrapping the raw key with an
        ephemeral AES key, and wrapping the ephemeral AES key with a 3072 bit
        RSA key. For more details, see [RSA AES key wrap
        mechanism](http://docs.oasis-open.org/pkcs11/pkcs11-
        curr/v2.40/cos01/pkcs11-curr-v2.40-cos01.html#_Toc408226908).
      RSA_OAEP_4096_SHA256_AES_256: This ImportMethod represents the
        CKM_RSA_AES_KEY_WRAP key wrapping scheme defined in the PKCS #11
        standard. In summary, this involves wrapping the raw key with an
        ephemeral AES key, and wrapping the ephemeral AES key with a 4096 bit
        RSA key. For more details, see [RSA AES key wrap
        mechanism](http://docs.oasis-open.org/pkcs11/pkcs11-
        curr/v2.40/cos01/pkcs11-curr-v2.40-cos01.html#_Toc408226908).
      RSA_OAEP_3072_SHA256: This ImportMethod represents RSAES-OAEP with a
        3072 bit RSA key. The key material to be imported is wrapped directly
        with the RSA key. Due to technical limitations of RSA wrapping, this
        method cannot be used to wrap RSA keys for import.
      RSA_OAEP_4096_SHA256: This ImportMethod represents RSAES-OAEP with a
        4096 bit RSA key. The key material to be imported is wrapped directly
        with the RSA key. Due to technical limitations of RSA wrapping, this
        method cannot be used to wrap RSA keys for import.
    """
    IMPORT_METHOD_UNSPECIFIED = 0
    RSA_OAEP_3072_SHA1_AES_256 = 1
    RSA_OAEP_4096_SHA1_AES_256 = 2
    RSA_OAEP_3072_SHA256_AES_256 = 3
    RSA_OAEP_4096_SHA256_AES_256 = 4
    RSA_OAEP_3072_SHA256 = 5
    RSA_OAEP_4096_SHA256 = 6

  class ProtectionLevelValueValuesEnum(_messages.Enum):
    r"""Required. Immutable. The protection level of the ImportJob. This must
    match the protection_level of the version_template on the CryptoKey you
    attempt to import into.

    Values:
      PROTECTION_LEVEL_UNSPECIFIED: Not specified.
      SOFTWARE: Crypto operations are performed in software.
      HSM: Crypto operations are performed in a Hardware Security Module.
      EXTERNAL: Crypto operations are performed by an external key manager.
      EXTERNAL_VPC: Crypto operations are performed in an EKM-over-VPC
        backend.
    """
    PROTECTION_LEVEL_UNSPECIFIED = 0
    SOFTWARE = 1
    HSM = 2
    EXTERNAL = 3
    EXTERNAL_VPC = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the ImportJob, indicating if it can
    be used.

    Values:
      IMPORT_JOB_STATE_UNSPECIFIED: Not specified.
      PENDING_GENERATION: The wrapping key for this job is still being
        generated. It may not be used. Cloud KMS will automatically mark this
        job as ACTIVE as soon as the wrapping key is generated.
      ACTIVE: This job may be used in CreateCryptoKey and
        CreateCryptoKeyVersion requests.
      EXPIRED: This job can no longer be used and may not leave this state
        once entered.
    """
    IMPORT_JOB_STATE_UNSPECIFIED = 0
    PENDING_GENERATION = 1
    ACTIVE = 2
    EXPIRED = 3

  attestation = _messages.MessageField('KeyOperationAttestation', 1)
  createTime = _messages.StringField(2)
  expireEventTime = _messages.StringField(3)
  expireTime = _messages.StringField(4)
  generateTime = _messages.StringField(5)
  importMethod = _messages.EnumField('ImportMethodValueValuesEnum', 6)
  name = _messages.StringField(7)
  protectionLevel = _messages.EnumField('ProtectionLevelValueValuesEnum', 8)
  publicKey = _messages.MessageField('WrappingPublicKey', 9)
  state = _messages.EnumField('StateValueValuesEnum', 10)


class KeyOperationAttestation(_messages.Message):
  r"""Contains an HSM-generated attestation about a key operation. For more
  information, see [Verifying attestations]
  (https://cloud.google.com/kms/docs/attest-key).

  Enums:
    FormatValueValuesEnum: Output only. The format of the attestation data.

  Fields:
    certChains: Output only. The certificate chains needed to validate the
      attestation
    content: Output only. The attestation data provided by the HSM when the
      key operation was performed.
    format: Output only. The format of the attestation data.
  """

  class FormatValueValuesEnum(_messages.Enum):
    r"""Output only. The format of the attestation data.

    Values:
      ATTESTATION_FORMAT_UNSPECIFIED: Not specified.
      CAVIUM_V1_COMPRESSED: Cavium HSM attestation compressed with gzip. Note
        that this format is defined by Cavium and subject to change at any
        time. See https://www.marvell.com/products/security-solutions/nitrox-
        hs-adapters/software-key-attestation.html.
      CAVIUM_V2_COMPRESSED: Cavium HSM attestation V2 compressed with gzip.
        This is a new format introduced in Cavium's version 3.2-08.
    """
    ATTESTATION_FORMAT_UNSPECIFIED = 0
    CAVIUM_V1_COMPRESSED = 1
    CAVIUM_V2_COMPRESSED = 2

  certChains = _messages.MessageField('CertificateChains', 1)
  content = _messages.BytesField(2)
  format = _messages.EnumField('FormatValueValuesEnum', 3)


class KeyRing(_messages.Message):
  r"""A KeyRing is a toplevel logical grouping of CryptoKeys.

  Fields:
    createTime: Output only. The time at which this KeyRing was created.
    name: Output only. The resource name for the KeyRing in the format
      `projects/*/locations/*/keyRings/*`.
  """

  createTime = _messages.StringField(1)
  name = _messages.StringField(2)


class ListCryptoKeyVersionsResponse(_messages.Message):
  r"""Response message for KeyManagementService.ListCryptoKeyVersions.

  Fields:
    cryptoKeyVersions: The list of CryptoKeyVersions.
    nextPageToken: A token to retrieve next page of results. Pass this value
      in ListCryptoKeyVersionsRequest.page_token to retrieve the next page of
      results.
    totalSize: The total number of CryptoKeyVersions that matched the query.
  """

  cryptoKeyVersions = _messages.MessageField('CryptoKeyVersion', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  totalSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class ListCryptoKeysResponse(_messages.Message):
  r"""Response message for KeyManagementService.ListCryptoKeys.

  Fields:
    cryptoKeys: The list of CryptoKeys.
    nextPageToken: A token to retrieve next page of results. Pass this value
      in ListCryptoKeysRequest.page_token to retrieve the next page of
      results.
    totalSize: The total number of CryptoKeys that matched the query.
  """

  cryptoKeys = _messages.MessageField('CryptoKey', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  totalSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class ListEkmConnectionsResponse(_messages.Message):
  r"""Response message for EkmService.ListEkmConnections.

  Fields:
    ekmConnections: The list of EkmConnections.
    nextPageToken: A token to retrieve next page of results. Pass this value
      in ListEkmConnectionsRequest.page_token to retrieve the next page of
      results.
    totalSize: The total number of EkmConnections that matched the query.
  """

  ekmConnections = _messages.MessageField('EkmConnection', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  totalSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class ListImportJobsResponse(_messages.Message):
  r"""Response message for KeyManagementService.ListImportJobs.

  Fields:
    importJobs: The list of ImportJobs.
    nextPageToken: A token to retrieve next page of results. Pass this value
      in ListImportJobsRequest.page_token to retrieve the next page of
      results.
    totalSize: The total number of ImportJobs that matched the query.
  """

  importJobs = _messages.MessageField('ImportJob', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  totalSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class ListKeyRingsResponse(_messages.Message):
  r"""Response message for KeyManagementService.ListKeyRings.

  Fields:
    keyRings: The list of KeyRings.
    nextPageToken: A token to retrieve next page of results. Pass this value
      in ListKeyRingsRequest.page_token to retrieve the next page of results.
    totalSize: The total number of KeyRings that matched the query.
  """

  keyRings = _messages.MessageField('KeyRing', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  totalSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LocationMetadata(_messages.Message):
  r"""Cloud KMS metadata for the given google.cloud.location.Location.

  Fields:
    ekmAvailable: Indicates whether CryptoKeys with protection_level EXTERNAL
      can be created in this location.
    hsmAvailable: Indicates whether CryptoKeys with protection_level HSM can
      be created in this location.
  """

  ekmAvailable = _messages.BooleanField(1)
  hsmAvailable = _messages.BooleanField(2)


class MacSignRequest(_messages.Message):
  r"""Request message for KeyManagementService.MacSign.

  Fields:
    data: Required. The data to sign. The MAC tag is computed over this data
      field based on the specific algorithm.
    dataCrc32c: Optional. An optional CRC32C checksum of the
      MacSignRequest.data. If specified, KeyManagementService will verify the
      integrity of the received MacSignRequest.data using this checksum.
      KeyManagementService will report an error if the checksum verification
      fails. If you receive a checksum error, your client should verify that
      CRC32C(MacSignRequest.data) is equal to MacSignRequest.data_crc32c, and
      if so, perform a limited number of retries. A persistent mismatch may
      indicate an issue in your computation of the CRC32C checksum. Note: This
      field is defined as int64 for reasons of compatibility across different
      languages. However, it is a non-negative integer, which will never
      exceed 2^32-1, and can be safely downconverted to uint32 in languages
      that support this type.
  """

  data = _messages.BytesField(1)
  dataCrc32c = _messages.IntegerField(2)


class MacSignResponse(_messages.Message):
  r"""Response message for KeyManagementService.MacSign.

  Enums:
    ProtectionLevelValueValuesEnum: The ProtectionLevel of the
      CryptoKeyVersion used for signing.

  Fields:
    mac: The created signature.
    macCrc32c: Integrity verification field. A CRC32C checksum of the returned
      MacSignResponse.mac. An integrity check of MacSignResponse.mac can be
      performed by computing the CRC32C checksum of MacSignResponse.mac and
      comparing your results to this field. Discard the response in case of
      non-matching checksum values, and perform a limited number of retries. A
      persistent mismatch may indicate an issue in your computation of the
      CRC32C checksum. Note: This field is defined as int64 for reasons of
      compatibility across different languages. However, it is a non-negative
      integer, which will never exceed 2^32-1, and can be safely downconverted
      to uint32 in languages that support this type.
    name: The resource name of the CryptoKeyVersion used for signing. Check
      this field to verify that the intended resource was used for signing.
    protectionLevel: The ProtectionLevel of the CryptoKeyVersion used for
      signing.
    verifiedDataCrc32c: Integrity verification field. A flag indicating
      whether MacSignRequest.data_crc32c was received by KeyManagementService
      and used for the integrity verification of the data. A false value of
      this field indicates either that MacSignRequest.data_crc32c was left
      unset or that it was not delivered to KeyManagementService. If you've
      set MacSignRequest.data_crc32c but this field is still false, discard
      the response and perform a limited number of retries.
  """

  class ProtectionLevelValueValuesEnum(_messages.Enum):
    r"""The ProtectionLevel of the CryptoKeyVersion used for signing.

    Values:
      PROTECTION_LEVEL_UNSPECIFIED: Not specified.
      SOFTWARE: Crypto operations are performed in software.
      HSM: Crypto operations are performed in a Hardware Security Module.
      EXTERNAL: Crypto operations are performed by an external key manager.
      EXTERNAL_VPC: Crypto operations are performed in an EKM-over-VPC
        backend.
    """
    PROTECTION_LEVEL_UNSPECIFIED = 0
    SOFTWARE = 1
    HSM = 2
    EXTERNAL = 3
    EXTERNAL_VPC = 4

  mac = _messages.BytesField(1)
  macCrc32c = _messages.IntegerField(2)
  name = _messages.StringField(3)
  protectionLevel = _messages.EnumField('ProtectionLevelValueValuesEnum', 4)
  verifiedDataCrc32c = _messages.BooleanField(5)


class MacVerifyRequest(_messages.Message):
  r"""Request message for KeyManagementService.MacVerify.

  Fields:
    data: Required. The data used previously as a MacSignRequest.data to
      generate the MAC tag.
    dataCrc32c: Optional. An optional CRC32C checksum of the
      MacVerifyRequest.data. If specified, KeyManagementService will verify
      the integrity of the received MacVerifyRequest.data using this checksum.
      KeyManagementService will report an error if the checksum verification
      fails. If you receive a checksum error, your client should verify that
      CRC32C(MacVerifyRequest.data) is equal to MacVerifyRequest.data_crc32c,
      and if so, perform a limited number of retries. A persistent mismatch
      may indicate an issue in your computation of the CRC32C checksum. Note:
      This field is defined as int64 for reasons of compatibility across
      different languages. However, it is a non-negative integer, which will
      never exceed 2^32-1, and can be safely downconverted to uint32 in
      languages that support this type.
    mac: Required. The signature to verify.
    macCrc32c: Optional. An optional CRC32C checksum of the
      MacVerifyRequest.mac. If specified, KeyManagementService will verify the
      integrity of the received MacVerifyRequest.mac using this checksum.
      KeyManagementService will report an error if the checksum verification
      fails. If you receive a checksum error, your client should verify that
      CRC32C(MacVerifyRequest.tag) is equal to MacVerifyRequest.mac_crc32c,
      and if so, perform a limited number of retries. A persistent mismatch
      may indicate an issue in your computation of the CRC32C checksum. Note:
      This field is defined as int64 for reasons of compatibility across
      different languages. However, it is a non-negative integer, which will
      never exceed 2^32-1, and can be safely downconverted to uint32 in
      languages that support this type.
  """

  data = _messages.BytesField(1)
  dataCrc32c = _messages.IntegerField(2)
  mac = _messages.BytesField(3)
  macCrc32c = _messages.IntegerField(4)


class MacVerifyResponse(_messages.Message):
  r"""Response message for KeyManagementService.MacVerify.

  Enums:
    ProtectionLevelValueValuesEnum: The ProtectionLevel of the
      CryptoKeyVersion used for verification.

  Fields:
    name: The resource name of the CryptoKeyVersion used for verification.
      Check this field to verify that the intended resource was used for
      verification.
    protectionLevel: The ProtectionLevel of the CryptoKeyVersion used for
      verification.
    success: This field indicates whether or not the verification operation
      for MacVerifyRequest.mac over MacVerifyRequest.data was successful.
    verifiedDataCrc32c: Integrity verification field. A flag indicating
      whether MacVerifyRequest.data_crc32c was received by
      KeyManagementService and used for the integrity verification of the
      data. A false value of this field indicates either that
      MacVerifyRequest.data_crc32c was left unset or that it was not delivered
      to KeyManagementService. If you've set MacVerifyRequest.data_crc32c but
      this field is still false, discard the response and perform a limited
      number of retries.
    verifiedMacCrc32c: Integrity verification field. A flag indicating whether
      MacVerifyRequest.mac_crc32c was received by KeyManagementService and
      used for the integrity verification of the data. A false value of this
      field indicates either that MacVerifyRequest.mac_crc32c was left unset
      or that it was not delivered to KeyManagementService. If you've set
      MacVerifyRequest.mac_crc32c but this field is still false, discard the
      response and perform a limited number of retries.
    verifiedSuccessIntegrity: Integrity verification field. This value is used
      for the integrity verification of [MacVerifyResponse.success]. If the
      value of this field contradicts the value of
      [MacVerifyResponse.success], discard the response and perform a limited
      number of retries.
  """

  class ProtectionLevelValueValuesEnum(_messages.Enum):
    r"""The ProtectionLevel of the CryptoKeyVersion used for verification.

    Values:
      PROTECTION_LEVEL_UNSPECIFIED: Not specified.
      SOFTWARE: Crypto operations are performed in software.
      HSM: Crypto operations are performed in a Hardware Security Module.
      EXTERNAL: Crypto operations are performed by an external key manager.
      EXTERNAL_VPC: Crypto operations are performed in an EKM-over-VPC
        backend.
    """
    PROTECTION_LEVEL_UNSPECIFIED = 0
    SOFTWARE = 1
    HSM = 2
    EXTERNAL = 3
    EXTERNAL_VPC = 4

  name = _messages.StringField(1)
  protectionLevel = _messages.EnumField('ProtectionLevelValueValuesEnum', 2)
  success = _messages.BooleanField(3)
  verifiedDataCrc32c = _messages.BooleanField(4)
  verifiedMacCrc32c = _messages.BooleanField(5)
  verifiedSuccessIntegrity = _messages.BooleanField(6)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class PublicKey(_messages.Message):
  r"""The public key for a given CryptoKeyVersion. Obtained via GetPublicKey.

  Enums:
    AlgorithmValueValuesEnum: The Algorithm associated with this key.
    ProtectionLevelValueValuesEnum: The ProtectionLevel of the
      CryptoKeyVersion public key.

  Fields:
    algorithm: The Algorithm associated with this key.
    name: The name of the CryptoKeyVersion public key. Provided here for
      verification. NOTE: This field is in Beta.
    pem: The public key, encoded in PEM format. For more information, see the
      [RFC 7468](https://tools.ietf.org/html/rfc7468) sections for [General
      Considerations](https://tools.ietf.org/html/rfc7468#section-2) and
      [Textual Encoding of Subject Public Key Info]
      (https://tools.ietf.org/html/rfc7468#section-13).
    pemCrc32c: Integrity verification field. A CRC32C checksum of the returned
      PublicKey.pem. An integrity check of PublicKey.pem can be performed by
      computing the CRC32C checksum of PublicKey.pem and comparing your
      results to this field. Discard the response in case of non-matching
      checksum values, and perform a limited number of retries. A persistent
      mismatch may indicate an issue in your computation of the CRC32C
      checksum. Note: This field is defined as int64 for reasons of
      compatibility across different languages. However, it is a non-negative
      integer, which will never exceed 2^32-1, and can be safely downconverted
      to uint32 in languages that support this type. NOTE: This field is in
      Beta.
    protectionLevel: The ProtectionLevel of the CryptoKeyVersion public key.
  """

  class AlgorithmValueValuesEnum(_messages.Enum):
    r"""The Algorithm associated with this key.

    Values:
      CRYPTO_KEY_VERSION_ALGORITHM_UNSPECIFIED: Not specified.
      GOOGLE_SYMMETRIC_ENCRYPTION: Creates symmetric encryption keys.
      AES_128_GCM: AES-GCM (Galois Counter Mode) using 128-bit keys.
      AES_256_GCM: AES-GCM (Galois Counter Mode) using 256-bit keys.
      AES_128_CBC: AES-CBC (Cipher Block Chaining Mode) using 128-bit keys.
      AES_256_CBC: AES-CBC (Cipher Block Chaining Mode) using 256-bit keys.
      AES_128_CTR: AES-CTR (Counter Mode) using 128-bit keys.
      AES_256_CTR: AES-CTR (Counter Mode) using 256-bit keys.
      RSA_SIGN_PSS_2048_SHA256: RSASSA-PSS 2048 bit key with a SHA256 digest.
      RSA_SIGN_PSS_3072_SHA256: RSASSA-PSS 3072 bit key with a SHA256 digest.
      RSA_SIGN_PSS_4096_SHA256: RSASSA-PSS 4096 bit key with a SHA256 digest.
      RSA_SIGN_PSS_4096_SHA512: RSASSA-PSS 4096 bit key with a SHA512 digest.
      RSA_SIGN_PKCS1_2048_SHA256: RSASSA-PKCS1-v1_5 with a 2048 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_3072_SHA256: RSASSA-PKCS1-v1_5 with a 3072 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_4096_SHA256: RSASSA-PKCS1-v1_5 with a 4096 bit key and a
        SHA256 digest.
      RSA_SIGN_PKCS1_4096_SHA512: RSASSA-PKCS1-v1_5 with a 4096 bit key and a
        SHA512 digest.
      RSA_SIGN_RAW_PKCS1_2048: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 2048 bit key.
      RSA_SIGN_RAW_PKCS1_3072: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 3072 bit key.
      RSA_SIGN_RAW_PKCS1_4096: RSASSA-PKCS1-v1_5 signing without encoding,
        with a 4096 bit key.
      RSA_DECRYPT_OAEP_2048_SHA256: RSAES-OAEP 2048 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_3072_SHA256: RSAES-OAEP 3072 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_4096_SHA256: RSAES-OAEP 4096 bit key with a SHA256
        digest.
      RSA_DECRYPT_OAEP_4096_SHA512: RSAES-OAEP 4096 bit key with a SHA512
        digest.
      RSA_DECRYPT_OAEP_2048_SHA1: RSAES-OAEP 2048 bit key with a SHA1 digest.
      RSA_DECRYPT_OAEP_3072_SHA1: RSAES-OAEP 3072 bit key with a SHA1 digest.
      RSA_DECRYPT_OAEP_4096_SHA1: RSAES-OAEP 4096 bit key with a SHA1 digest.
      EC_SIGN_P256_SHA256: ECDSA on the NIST P-256 curve with a SHA256 digest.
        Other hash functions can also be used:
        https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      EC_SIGN_P384_SHA384: ECDSA on the NIST P-384 curve with a SHA384 digest.
        Other hash functions can also be used:
        https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      EC_SIGN_SECP256K1_SHA256: ECDSA on the non-NIST secp256k1 curve. This
        curve is only supported for HSM protection level. Other hash functions
        can also be used: https://cloud.google.com/kms/docs/create-validate-
        signatures#ecdsa_support_for_other_hash_algorithms
      HMAC_SHA256: HMAC-SHA256 signing with a 256 bit key.
      HMAC_SHA1: HMAC-SHA1 signing with a 160 bit key.
      HMAC_SHA384: HMAC-SHA384 signing with a 384 bit key.
      HMAC_SHA512: HMAC-SHA512 signing with a 512 bit key.
      HMAC_SHA224: HMAC-SHA224 signing with a 224 bit key.
      EXTERNAL_SYMMETRIC_ENCRYPTION: Algorithm representing symmetric
        encryption by an external key manager.
    """
    CRYPTO_KEY_VERSION_ALGORITHM_UNSPECIFIED = 0
    GOOGLE_SYMMETRIC_ENCRYPTION = 1
    AES_128_GCM = 2
    AES_256_GCM = 3
    AES_128_CBC = 4
    AES_256_CBC = 5
    AES_128_CTR = 6
    AES_256_CTR = 7
    RSA_SIGN_PSS_2048_SHA256 = 8
    RSA_SIGN_PSS_3072_SHA256 = 9
    RSA_SIGN_PSS_4096_SHA256 = 10
    RSA_SIGN_PSS_4096_SHA512 = 11
    RSA_SIGN_PKCS1_2048_SHA256 = 12
    RSA_SIGN_PKCS1_3072_SHA256 = 13
    RSA_SIGN_PKCS1_4096_SHA256 = 14
    RSA_SIGN_PKCS1_4096_SHA512 = 15
    RSA_SIGN_RAW_PKCS1_2048 = 16
    RSA_SIGN_RAW_PKCS1_3072 = 17
    RSA_SIGN_RAW_PKCS1_4096 = 18
    RSA_DECRYPT_OAEP_2048_SHA256 = 19
    RSA_DECRYPT_OAEP_3072_SHA256 = 20
    RSA_DECRYPT_OAEP_4096_SHA256 = 21
    RSA_DECRYPT_OAEP_4096_SHA512 = 22
    RSA_DECRYPT_OAEP_2048_SHA1 = 23
    RSA_DECRYPT_OAEP_3072_SHA1 = 24
    RSA_DECRYPT_OAEP_4096_SHA1 = 25
    EC_SIGN_P256_SHA256 = 26
    EC_SIGN_P384_SHA384 = 27
    EC_SIGN_SECP256K1_SHA256 = 28
    HMAC_SHA256 = 29
    HMAC_SHA1 = 30
    HMAC_SHA384 = 31
    HMAC_SHA512 = 32
    HMAC_SHA224 = 33
    EXTERNAL_SYMMETRIC_ENCRYPTION = 34

  class ProtectionLevelValueValuesEnum(_messages.Enum):
    r"""The ProtectionLevel of the CryptoKeyVersion public key.

    Values:
      PROTECTION_LEVEL_UNSPECIFIED: Not specified.
      SOFTWARE: Crypto operations are performed in software.
      HSM: Crypto operations are performed in a Hardware Security Module.
      EXTERNAL: Crypto operations are performed by an external key manager.
      EXTERNAL_VPC: Crypto operations are performed in an EKM-over-VPC
        backend.
    """
    PROTECTION_LEVEL_UNSPECIFIED = 0
    SOFTWARE = 1
    HSM = 2
    EXTERNAL = 3
    EXTERNAL_VPC = 4

  algorithm = _messages.EnumField('AlgorithmValueValuesEnum', 1)
  name = _messages.StringField(2)
  pem = _messages.StringField(3)
  pemCrc32c = _messages.IntegerField(4)
  protectionLevel = _messages.EnumField('ProtectionLevelValueValuesEnum', 5)


class RawDecryptRequest(_messages.Message):
  r"""Request message for KeyManagementService.RawDecrypt.

  Fields:
    additionalAuthenticatedData: Optional. Optional data that must match the
      data originally supplied in
      RawEncryptRequest.additional_authenticated_data.
    additionalAuthenticatedDataCrc32c: Optional. An optional CRC32C checksum
      of the RawDecryptRequest.additional_authenticated_data. If specified,
      KeyManagementService will verify the integrity of the received
      additional_authenticated_data using this checksum. KeyManagementService
      will report an error if the checksum verification fails. If you receive
      a checksum error, your client should verify that
      CRC32C(additional_authenticated_data) is equal to
      additional_authenticated_data_crc32c, and if so, perform a limited
      number of retries. A persistent mismatch may indicate an issue in your
      computation of the CRC32C checksum. Note: This field is defined as int64
      for reasons of compatibility across different languages. However, it is
      a non-negative integer, which will never exceed 2^32-1, and can be
      safely downconverted to uint32 in languages that support this type.
    ciphertext: Required. The encrypted data originally returned in
      RawEncryptResponse.ciphertext.
    ciphertextCrc32c: Optional. An optional CRC32C checksum of the
      RawDecryptRequest.ciphertext. If specified, KeyManagementService will
      verify the integrity of the received ciphertext using this checksum.
      KeyManagementService will report an error if the checksum verification
      fails. If you receive a checksum error, your client should verify that
      CRC32C(ciphertext) is equal to ciphertext_crc32c, and if so, perform a
      limited number of retries. A persistent mismatch may indicate an issue
      in your computation of the CRC32C checksum. Note: This field is defined
      as int64 for reasons of compatibility across different languages.
      However, it is a non-negative integer, which will never exceed 2^32-1,
      and can be safely downconverted to uint32 in languages that support this
      type.
    initializationVector: Required. The initialization vector (IV) used during
      encryption, which must match the data originally provided in
      RawEncryptResponse.initialization_vector.
    initializationVectorCrc32c: Optional. An optional CRC32C checksum of the
      RawDecryptRequest.initialization_vector. If specified,
      KeyManagementService will verify the integrity of the received
      initialization_vector using this checksum. KeyManagementService will
      report an error if the checksum verification fails. If you receive a
      checksum error, your client should verify that
      CRC32C(initialization_vector) is equal to initialization_vector_crc32c,
      and if so, perform a limited number of retries. A persistent mismatch
      may indicate an issue in your computation of the CRC32C checksum. Note:
      This field is defined as int64 for reasons of compatibility across
      different languages. However, it is a non-negative integer, which will
      never exceed 2^32-1, and can be safely downconverted to uint32 in
      languages that support this type.
    tagLength: The length of the authentication tag that is appended to the
      end of the ciphertext. If unspecified (0), the default value for the
      key's algorithm will be used (for AES-GCM, the default value is 16).
  """

  additionalAuthenticatedData = _messages.BytesField(1)
  additionalAuthenticatedDataCrc32c = _messages.IntegerField(2)
  ciphertext = _messages.BytesField(3)
  ciphertextCrc32c = _messages.IntegerField(4)
  initializationVector = _messages.BytesField(5)
  initializationVectorCrc32c = _messages.IntegerField(6)
  tagLength = _messages.IntegerField(7, variant=_messages.Variant.INT32)


class RawDecryptResponse(_messages.Message):
  r"""Response message for KeyManagementService.RawDecrypt.

  Enums:
    ProtectionLevelValueValuesEnum: The ProtectionLevel of the
      CryptoKeyVersion used in decryption.

  Fields:
    plaintext: The decrypted data.
    plaintextCrc32c: Integrity verification field. A CRC32C checksum of the
      returned RawDecryptResponse.plaintext. An integrity check of plaintext
      can be performed by computing the CRC32C checksum of plaintext and
      comparing your results to this field. Discard the response in case of
      non-matching checksum values, and perform a limited number of retries. A
      persistent mismatch may indicate an issue in your computation of the
      CRC32C checksum. Note: receiving this response message indicates that
      KeyManagementService is able to successfully decrypt the ciphertext.
      Note: This field is defined as int64 for reasons of compatibility across
      different languages. However, it is a non-negative integer, which will
      never exceed 2^32-1, and can be safely downconverted to uint32 in
      languages that support this type.
    protectionLevel: The ProtectionLevel of the CryptoKeyVersion used in
      decryption.
    verifiedAdditionalAuthenticatedDataCrc32c: Integrity verification field. A
      flag indicating whether
      RawDecryptRequest.additional_authenticated_data_crc32c was received by
      KeyManagementService and used for the integrity verification of
      additional_authenticated_data. A false value of this field indicates
      either that // RawDecryptRequest.additional_authenticated_data_crc32c
      was left unset or that it was not delivered to KeyManagementService. If
      you've set RawDecryptRequest.additional_authenticated_data_crc32c but
      this field is still false, discard the response and perform a limited
      number of retries.
    verifiedCiphertextCrc32c: Integrity verification field. A flag indicating
      whether RawDecryptRequest.ciphertext_crc32c was received by
      KeyManagementService and used for the integrity verification of the
      ciphertext. A false value of this field indicates either that
      RawDecryptRequest.ciphertext_crc32c was left unset or that it was not
      delivered to KeyManagementService. If you've set
      RawDecryptRequest.ciphertext_crc32c but this field is still false,
      discard the response and perform a limited number of retries.
    verifiedInitializationVectorCrc32c: Integrity verification field. A flag
      indicating whether RawDecryptRequest.initialization_vector_crc32c was
      received by KeyManagementService and used for the integrity verification
      of initialization_vector. A false value of this field indicates either
      that RawDecryptRequest.initialization_vector_crc32c was left unset or
      that it was not delivered to KeyManagementService. If you've set
      RawDecryptRequest.initialization_vector_crc32c but this field is still
      false, discard the response and perform a limited number of retries.
  """

  class ProtectionLevelValueValuesEnum(_messages.Enum):
    r"""The ProtectionLevel of the CryptoKeyVersion used in decryption.

    Values:
      PROTECTION_LEVEL_UNSPECIFIED: Not specified.
      SOFTWARE: Crypto operations are performed in software.
      HSM: Crypto operations are performed in a Hardware Security Module.
      EXTERNAL: Crypto operations are performed by an external key manager.
      EXTERNAL_VPC: Crypto operations are performed in an EKM-over-VPC
        backend.
    """
    PROTECTION_LEVEL_UNSPECIFIED = 0
    SOFTWARE = 1
    HSM = 2
    EXTERNAL = 3
    EXTERNAL_VPC = 4

  plaintext = _messages.BytesField(1)
  plaintextCrc32c = _messages.IntegerField(2)
  protectionLevel = _messages.EnumField('ProtectionLevelValueValuesEnum', 3)
  verifiedAdditionalAuthenticatedDataCrc32c = _messages.BooleanField(4)
  verifiedCiphertextCrc32c = _messages.BooleanField(5)
  verifiedInitializationVectorCrc32c = _messages.BooleanField(6)


class RawEncryptRequest(_messages.Message):
  r"""Request message for KeyManagementService.RawEncrypt.

  Fields:
    additionalAuthenticatedData: Optional. Optional data that, if specified,
      must also be provided during decryption through
      RawDecryptRequest.additional_authenticated_data. This field may only be
      used in conjunction with an algorithm that accepts additional
      authenticated data (for example, AES-GCM). The maximum size depends on
      the key version's protection_level. For SOFTWARE keys, the plaintext
      must be no larger than 64KiB. For HSM keys, the combined length of the
      plaintext and additional_authenticated_data fields must be no larger
      than 8KiB.
    additionalAuthenticatedDataCrc32c: Optional. An optional CRC32C checksum
      of the RawEncryptRequest.additional_authenticated_data. If specified,
      KeyManagementService will verify the integrity of the received
      additional_authenticated_data using this checksum. KeyManagementService
      will report an error if the checksum verification fails. If you receive
      a checksum error, your client should verify that
      CRC32C(additional_authenticated_data) is equal to
      additional_authenticated_data_crc32c, and if so, perform a limited
      number of retries. A persistent mismatch may indicate an issue in your
      computation of the CRC32C checksum. Note: This field is defined as int64
      for reasons of compatibility across different languages. However, it is
      a non-negative integer, which will never exceed 2^32-1, and can be
      safely downconverted to uint32 in languages that support this type.
    initializationVector: Optional. A customer-supplied initialization vector
      that will be used for encryption. If it is not provided for AES-CBC and
      AES-CTR, one will be generated. It will be returned in
      RawEncryptResponse.initialization_vector.
    initializationVectorCrc32c: Optional. An optional CRC32C checksum of the
      RawEncryptRequest.initialization_vector. If specified,
      KeyManagementService will verify the integrity of the received
      initialization_vector using this checksum. KeyManagementService will
      report an error if the checksum verification fails. If you receive a
      checksum error, your client should verify that
      CRC32C(initialization_vector) is equal to initialization_vector_crc32c,
      and if so, perform a limited number of retries. A persistent mismatch
      may indicate an issue in your computation of the CRC32C checksum. Note:
      This field is defined as int64 for reasons of compatibility across
      different languages. However, it is a non-negative integer, which will
      never exceed 2^32-1, and can be safely downconverted to uint32 in
      languages that support this type.
    plaintext: Required. The data to encrypt. Must be no larger than 64KiB.
      The maximum size depends on the key version's protection_level. For
      SOFTWARE keys, the plaintext must be no larger than 64KiB. For HSM keys,
      the combined length of the plaintext and additional_authenticated_data
      fields must be no larger than 8KiB.
    plaintextCrc32c: Optional. An optional CRC32C checksum of the
      RawEncryptRequest.plaintext. If specified, KeyManagementService will
      verify the integrity of the received plaintext using this checksum.
      KeyManagementService will report an error if the checksum verification
      fails. If you receive a checksum error, your client should verify that
      CRC32C(plaintext) is equal to plaintext_crc32c, and if so, perform a
      limited number of retries. A persistent mismatch may indicate an issue
      in your computation of the CRC32C checksum. Note: This field is defined
      as int64 for reasons of compatibility across different languages.
      However, it is a non-negative integer, which will never exceed 2^32-1,
      and can be safely downconverted to uint32 in languages that support this
      type.
  """

  additionalAuthenticatedData = _messages.BytesField(1)
  additionalAuthenticatedDataCrc32c = _messages.IntegerField(2)
  initializationVector = _messages.BytesField(3)
  initializationVectorCrc32c = _messages.IntegerField(4)
  plaintext = _messages.BytesField(5)
  plaintextCrc32c = _messages.IntegerField(6)


class RawEncryptResponse(_messages.Message):
  r"""Response message for KeyManagementService.RawEncrypt.

  Enums:
    ProtectionLevelValueValuesEnum: The ProtectionLevel of the
      CryptoKeyVersion used in encryption.

  Fields:
    ciphertext: The encrypted data. In the case of AES-GCM, the authentication
      tag is the tag_length bytes at the end of this field.
    ciphertextCrc32c: Integrity verification field. A CRC32C checksum of the
      returned RawEncryptResponse.ciphertext. An integrity check of ciphertext
      can be performed by computing the CRC32C checksum of ciphertext and
      comparing your results to this field. Discard the response in case of
      non-matching checksum values, and perform a limited number of retries. A
      persistent mismatch may indicate an issue in your computation of the
      CRC32C checksum. Note: This field is defined as int64 for reasons of
      compatibility across different languages. However, it is a non-negative
      integer, which will never exceed 2^32-1, and can be safely downconverted
      to uint32 in languages that support this type.
    initializationVector: The initialization vector (IV) generated by the
      service during encryption. This value must be stored and provided in
      RawDecryptRequest.initialization_vector at decryption time.
    initializationVectorCrc32c: Integrity verification field. A CRC32C
      checksum of the returned RawEncryptResponse.initialization_vector. An
      integrity check of initialization_vector can be performed by computing
      the CRC32C checksum of initialization_vector and comparing your results
      to this field. Discard the response in case of non-matching checksum
      values, and perform a limited number of retries. A persistent mismatch
      may indicate an issue in your computation of the CRC32C checksum. Note:
      This field is defined as int64 for reasons of compatibility across
      different languages. However, it is a non-negative integer, which will
      never exceed 2^32-1, and can be safely downconverted to uint32 in
      languages that support this type.
    name: The resource name of the CryptoKeyVersion used in encryption. Check
      this field to verify that the intended resource was used for encryption.
    protectionLevel: The ProtectionLevel of the CryptoKeyVersion used in
      encryption.
    tagLength: The length of the authentication tag that is appended to the
      end of the ciphertext.
    verifiedAdditionalAuthenticatedDataCrc32c: Integrity verification field. A
      flag indicating whether
      RawEncryptRequest.additional_authenticated_data_crc32c was received by
      KeyManagementService and used for the integrity verification of
      additional_authenticated_data. A false value of this field indicates
      either that // RawEncryptRequest.additional_authenticated_data_crc32c
      was left unset or that it was not delivered to KeyManagementService. If
      you've set RawEncryptRequest.additional_authenticated_data_crc32c but
      this field is still false, discard the response and perform a limited
      number of retries.
    verifiedInitializationVectorCrc32c: Integrity verification field. A flag
      indicating whether RawEncryptRequest.initialization_vector_crc32c was
      received by KeyManagementService and used for the integrity verification
      of initialization_vector. A false value of this field indicates either
      that RawEncryptRequest.initialization_vector_crc32c was left unset or
      that it was not delivered to KeyManagementService. If you've set
      RawEncryptRequest.initialization_vector_crc32c but this field is still
      false, discard the response and perform a limited number of retries.
    verifiedPlaintextCrc32c: Integrity verification field. A flag indicating
      whether RawEncryptRequest.plaintext_crc32c was received by
      KeyManagementService and used for the integrity verification of the
      plaintext. A false value of this field indicates either that
      RawEncryptRequest.plaintext_crc32c was left unset or that it was not
      delivered to KeyManagementService. If you've set
      RawEncryptRequest.plaintext_crc32c but this field is still false,
      discard the response and perform a limited number of retries.
  """

  class ProtectionLevelValueValuesEnum(_messages.Enum):
    r"""The ProtectionLevel of the CryptoKeyVersion used in encryption.

    Values:
      PROTECTION_LEVEL_UNSPECIFIED: Not specified.
      SOFTWARE: Crypto operations are performed in software.
      HSM: Crypto operations are performed in a Hardware Security Module.
      EXTERNAL: Crypto operations are performed by an external key manager.
      EXTERNAL_VPC: Crypto operations are performed in an EKM-over-VPC
        backend.
    """
    PROTECTION_LEVEL_UNSPECIFIED = 0
    SOFTWARE = 1
    HSM = 2
    EXTERNAL = 3
    EXTERNAL_VPC = 4

  ciphertext = _messages.BytesField(1)
  ciphertextCrc32c = _messages.IntegerField(2)
  initializationVector = _messages.BytesField(3)
  initializationVectorCrc32c = _messages.IntegerField(4)
  name = _messages.StringField(5)
  protectionLevel = _messages.EnumField('ProtectionLevelValueValuesEnum', 6)
  tagLength = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  verifiedAdditionalAuthenticatedDataCrc32c = _messages.BooleanField(8)
  verifiedInitializationVectorCrc32c = _messages.BooleanField(9)
  verifiedPlaintextCrc32c = _messages.BooleanField(10)


class RestoreCryptoKeyVersionRequest(_messages.Message):
  r"""Request message for KeyManagementService.RestoreCryptoKeyVersion."""


class ServiceResolver(_messages.Message):
  r"""A ServiceResolver represents an EKM replica that can be reached within
  an EkmConnection.

  Fields:
    endpointFilter: Optional. The filter applied to the endpoints of the
      resolved service. If no filter is specified, all endpoints will be
      considered. An endpoint will be chosen arbitrarily from the filtered
      list for each request. For endpoint filter syntax and examples, see
      https://cloud.google.com/service-directory/docs/reference/rpc/google.clo
      ud.servicedirectory.v1#resolveservicerequest.
    hostname: Required. The hostname of the EKM replica used at TLS and HTTP
      layers.
    serverCertificates: Required. A list of leaf server certificates used to
      authenticate HTTPS connections to the EKM replica. Currently, a maximum
      of 10 Certificate is supported.
    serviceDirectoryService: Required. The resource name of the Service
      Directory service pointing to an EKM replica, in the format
      `projects/*/locations/*/namespaces/*/services/*`.
  """

  endpointFilter = _messages.StringField(1)
  hostname = _messages.StringField(2)
  serverCertificates = _messages.MessageField('Certificate', 3, repeated=True)
  serviceDirectoryService = _messages.StringField(4)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class UpdateCryptoKeyPrimaryVersionRequest(_messages.Message):
  r"""Request message for KeyManagementService.UpdateCryptoKeyPrimaryVersion.

  Fields:
    cryptoKeyVersionId: Required. The id of the child CryptoKeyVersion to use
      as primary.
  """

  cryptoKeyVersionId = _messages.StringField(1)


class VerifyConnectivityResponse(_messages.Message):
  r"""Response message for EkmService.VerifyConnectivity."""


class WrappingPublicKey(_messages.Message):
  r"""The public key component of the wrapping key. For details of the type of
  key this public key corresponds to, see the ImportMethod.

  Fields:
    pem: The public key, encoded in PEM format. For more information, see the
      [RFC 7468](https://tools.ietf.org/html/rfc7468) sections for [General
      Considerations](https://tools.ietf.org/html/rfc7468#section-2) and
      [Textual Encoding of Subject Public Key Info]
      (https://tools.ietf.org/html/rfc7468#section-13).
  """

  pem = _messages.StringField(1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

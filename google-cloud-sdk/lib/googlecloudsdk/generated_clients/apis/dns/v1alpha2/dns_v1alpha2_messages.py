"""Generated message classes for dns version v1alpha2.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding


package = 'dns'


class Change(_messages.Message):
  r"""A Change represents a set of ResourceRecordSet additions and deletions
  applied atomically to a ManagedZone. ResourceRecordSets within a ManagedZone
  are modified by creating a new Change element in the Changes collection. In
  turn the Changes collection also records the past modifications to the
  ResourceRecordSets in a ManagedZone. The current state of the ManagedZone is
  the sum effect of applying all Change elements in the Changes collection in
  sequence.

  Enums:
    StatusValueValuesEnum: Status of the operation (output only). A status of
      "done" means that the request to update the authoritative servers has
      been sent, but the servers might not be updated yet.

  Fields:
    additions: Which ResourceRecordSets to add?
    deletions: Which ResourceRecordSets to remove? Must match existing data
      exactly.
    id: Unique identifier for the resource; defined by the server (output
      only).
    isServing: If the DNS queries for the zone will be served.
    kind: A string attribute.
    startTime: The time that this operation was started by the server (output
      only). This is in RFC3339 text format.
    status: Status of the operation (output only). A status of "done" means
      that the request to update the authoritative servers has been sent, but
      the servers might not be updated yet.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Status of the operation (output only). A status of "done" means that
    the request to update the authoritative servers has been sent, but the
    servers might not be updated yet.

    Values:
      pending: <no description>
      done: <no description>
    """
    pending = 0
    done = 1

  additions = _messages.MessageField('ResourceRecordSet', 1, repeated=True)
  deletions = _messages.MessageField('ResourceRecordSet', 2, repeated=True)
  id = _messages.StringField(3)
  isServing = _messages.BooleanField(4)
  kind = _messages.StringField(5, default='dns#change')
  startTime = _messages.StringField(6)
  status = _messages.EnumField('StatusValueValuesEnum', 7)


class ChangesListResponse(_messages.Message):
  r"""The response to a request to enumerate Changes to a ResourceRecordSets
  collection.

  Fields:
    changes: The requested changes.
    header: A ResponseHeader attribute.
    kind: Type of resource.
    nextPageToken: The presence of this field indicates that there exist more
      results following your last page of results in pagination order. To
      fetch them, make another list request using this value as your
      pagination token. This lets you retrieve the complete contents of even
      very large collections one page at a time. However, if the contents of
      the collection change between the first and last paginated list request,
      the set of all elements returned are an inconsistent view of the
      collection. You cannot retrieve a "snapshot" of collections larger than
      the maximum page size.
  """

  changes = _messages.MessageField('Change', 1, repeated=True)
  header = _messages.MessageField('ResponseHeader', 2)
  kind = _messages.StringField(3, default='dns#changesListResponse')
  nextPageToken = _messages.StringField(4)


class DnsActivePeeringZonesDeactivateRequest(_messages.Message):
  r"""A DnsActivePeeringZonesDeactivateRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    peeringZoneId: The unique peering zone id of the consumer peering zone to
      be deactivated.
    project: The project ID for the producer project targeted by the consumer
      peering zone to be deactivated.
  """

  clientOperationId = _messages.StringField(1)
  peeringZoneId = _messages.IntegerField(2, required=True)
  project = _messages.StringField(3, required=True)


class DnsActivePeeringZonesGetPeeringZoneInfoRequest(_messages.Message):
  r"""A DnsActivePeeringZonesGetPeeringZoneInfoRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    peeringZoneId: ManagedZoneId addressed by this request
    project: Identifies the producer project targeted by the peering zone in
      this request.
  """

  clientOperationId = _messages.StringField(1)
  peeringZoneId = _messages.IntegerField(2, required=True)
  project = _messages.StringField(3, required=True)


class DnsActivePeeringZonesListRequest(_messages.Message):
  r"""A DnsActivePeeringZonesListRequest object.

  Fields:
    maxResults: Optional. Maximum number of results to be returned. If
      unspecified, the server decides how many results to return.
    pageToken: Optional. A tag returned by a previous list request that was
      truncated. Use this parameter to continue a previous list request.
    project: Identifies the producer project addressed by this request.
    targetNetwork: Identifies the target network addressed by this request by
      network name.
  """

  maxResults = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  project = _messages.StringField(3, required=True)
  targetNetwork = _messages.StringField(4, required=True)


class DnsChangesCreateRequest(_messages.Message):
  r"""A DnsChangesCreateRequest object.

  Fields:
    change: A Change resource to be passed as the request body.
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    managedZone: Identifies the managed zone addressed by this request. Can be
      the managed zone name or ID.
    project: Identifies the project addressed by this request.
  """

  change = _messages.MessageField('Change', 1)
  clientOperationId = _messages.StringField(2)
  managedZone = _messages.StringField(3, required=True)
  project = _messages.StringField(4, required=True)


class DnsChangesGetRequest(_messages.Message):
  r"""A DnsChangesGetRequest object.

  Fields:
    changeId: The identifier of the requested change, from a previous
      ResourceRecordSetsChangeResponse.
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    managedZone: Identifies the managed zone addressed by this request. Can be
      the managed zone name or ID.
    project: Identifies the project addressed by this request.
  """

  changeId = _messages.StringField(1, required=True)
  clientOperationId = _messages.StringField(2)
  managedZone = _messages.StringField(3, required=True)
  project = _messages.StringField(4, required=True)


class DnsChangesListRequest(_messages.Message):
  r"""A DnsChangesListRequest object.

  Enums:
    SortByValueValuesEnum: Sorting criterion. The only supported value is
      change sequence.

  Fields:
    managedZone: Identifies the managed zone addressed by this request. Can be
      the managed zone name or ID.
    maxResults: Optional. Maximum number of results to be returned. If
      unspecified, the server decides how many results to return.
    pageToken: Optional. A tag returned by a previous list request that was
      truncated. Use this parameter to continue a previous list request.
    project: Identifies the project addressed by this request.
    sortBy: Sorting criterion. The only supported value is change sequence.
    sortOrder: Sorting order direction: 'ascending' or 'descending'.
  """

  class SortByValueValuesEnum(_messages.Enum):
    r"""Sorting criterion. The only supported value is change sequence.

    Values:
      changeSequence: <no description>
    """
    changeSequence = 0

  managedZone = _messages.StringField(1, required=True)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  project = _messages.StringField(4, required=True)
  sortBy = _messages.EnumField('SortByValueValuesEnum', 5, default='changeSequence')
  sortOrder = _messages.StringField(6)


class DnsDnsKeysGetRequest(_messages.Message):
  r"""A DnsDnsKeysGetRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    digestType: An optional comma-separated list of digest types to compute
      and display for key signing keys. If omitted, the recommended digest
      type is computed and displayed.
    dnsKeyId: The identifier of the requested DnsKey.
    managedZone: Identifies the managed zone addressed by this request. Can be
      the managed zone name or ID.
    project: Identifies the project addressed by this request.
  """

  clientOperationId = _messages.StringField(1)
  digestType = _messages.StringField(2)
  dnsKeyId = _messages.StringField(3, required=True)
  managedZone = _messages.StringField(4, required=True)
  project = _messages.StringField(5, required=True)


class DnsDnsKeysListRequest(_messages.Message):
  r"""A DnsDnsKeysListRequest object.

  Fields:
    digestType: An optional comma-separated list of digest types to compute
      and display for key signing keys. If omitted, the recommended digest
      type is computed and displayed.
    managedZone: Identifies the managed zone addressed by this request. Can be
      the managed zone name or ID.
    maxResults: Optional. Maximum number of results to be returned. If
      unspecified, the server decides how many results to return.
    pageToken: Optional. A tag returned by a previous list request that was
      truncated. Use this parameter to continue a previous list request.
    project: Identifies the project addressed by this request.
  """

  digestType = _messages.StringField(1)
  managedZone = _messages.StringField(2, required=True)
  maxResults = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  project = _messages.StringField(5, required=True)


class DnsKey(_messages.Message):
  r"""A DNSSEC key pair.

  Enums:
    AlgorithmValueValuesEnum: String mnemonic specifying the DNSSEC algorithm
      of this key. Immutable after creation time.
    TypeValueValuesEnum: One of "KEY_SIGNING" or "ZONE_SIGNING". Keys of type
      KEY_SIGNING have the Secure Entry Point flag set and, when active, are
      used to sign only resource record sets of type DNSKEY. Otherwise, the
      Secure Entry Point flag is cleared, and this key is used to sign only
      resource record sets of other types. Immutable after creation time.

  Fields:
    algorithm: String mnemonic specifying the DNSSEC algorithm of this key.
      Immutable after creation time.
    creationTime: The time that this resource was created in the control
      plane. This is in RFC3339 text format. Output only.
    description: A mutable string of at most 1024 characters associated with
      this resource for the user's convenience. Has no effect on the
      resource's function.
    digests: Cryptographic hashes of the DNSKEY resource record associated
      with this DnsKey. These digests are needed to construct a DS record that
      points at this DNS key. Output only.
    id: Unique identifier for the resource; defined by the server (output
      only).
    isActive: Active keys are used to sign subsequent changes to the
      ManagedZone. Inactive keys are still present as DNSKEY Resource Records
      for the use of resolvers validating existing signatures.
    keyLength: Length of the key in bits. Specified at creation time, and then
      immutable.
    keyTag: The key tag is a non-cryptographic hash of the a DNSKEY resource
      record associated with this DnsKey. The key tag can be used to identify
      a DNSKEY more quickly (but it is not a unique identifier). In
      particular, the key tag is used in a parent zone's DS record to point at
      the DNSKEY in this child ManagedZone. The key tag is a number in the
      range [0, 65535] and the algorithm to calculate it is specified in
      RFC4034 Appendix B. Output only.
    kind: A string attribute.
    publicKey: Base64 encoded public half of this key. Output only.
    type: One of "KEY_SIGNING" or "ZONE_SIGNING". Keys of type KEY_SIGNING
      have the Secure Entry Point flag set and, when active, are used to sign
      only resource record sets of type DNSKEY. Otherwise, the Secure Entry
      Point flag is cleared, and this key is used to sign only resource record
      sets of other types. Immutable after creation time.
  """

  class AlgorithmValueValuesEnum(_messages.Enum):
    r"""String mnemonic specifying the DNSSEC algorithm of this key. Immutable
    after creation time.

    Values:
      rsasha1: <no description>
      rsasha256: <no description>
      rsasha512: <no description>
      ecdsap256sha256: <no description>
      ecdsap384sha384: <no description>
    """
    rsasha1 = 0
    rsasha256 = 1
    rsasha512 = 2
    ecdsap256sha256 = 3
    ecdsap384sha384 = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""One of "KEY_SIGNING" or "ZONE_SIGNING". Keys of type KEY_SIGNING have
    the Secure Entry Point flag set and, when active, are used to sign only
    resource record sets of type DNSKEY. Otherwise, the Secure Entry Point
    flag is cleared, and this key is used to sign only resource record sets of
    other types. Immutable after creation time.

    Values:
      keySigning: <no description>
      zoneSigning: <no description>
    """
    keySigning = 0
    zoneSigning = 1

  algorithm = _messages.EnumField('AlgorithmValueValuesEnum', 1)
  creationTime = _messages.StringField(2)
  description = _messages.StringField(3)
  digests = _messages.MessageField('DnsKeyDigest', 4, repeated=True)
  id = _messages.StringField(5)
  isActive = _messages.BooleanField(6)
  keyLength = _messages.IntegerField(7, variant=_messages.Variant.UINT32)
  keyTag = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  kind = _messages.StringField(9, default='dns#dnsKey')
  publicKey = _messages.StringField(10)
  type = _messages.EnumField('TypeValueValuesEnum', 11)


class DnsKeyDigest(_messages.Message):
  r"""A DnsKeyDigest object.

  Enums:
    TypeValueValuesEnum: Specifies the algorithm used to calculate this
      digest.

  Fields:
    digest: The base-16 encoded bytes of this digest. Suitable for use in a DS
      resource record.
    type: Specifies the algorithm used to calculate this digest.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Specifies the algorithm used to calculate this digest.

    Values:
      sha1: <no description>
      sha256: <no description>
      sha384: <no description>
    """
    sha1 = 0
    sha256 = 1
    sha384 = 2

  digest = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class DnsKeySpec(_messages.Message):
  r"""Parameters for DnsKey key generation. Used for generating initial keys
  for a new ManagedZone and as default when adding a new DnsKey.

  Enums:
    AlgorithmValueValuesEnum: String mnemonic specifying the DNSSEC algorithm
      of this key.
    KeyTypeValueValuesEnum: Specifies whether this is a key signing key (KSK)
      or a zone signing key (ZSK). Key signing keys have the Secure Entry
      Point flag set and, when active, are only used to sign resource record
      sets of type DNSKEY. Zone signing keys do not have the Secure Entry
      Point flag set and are used to sign all other types of resource record
      sets.

  Fields:
    algorithm: String mnemonic specifying the DNSSEC algorithm of this key.
    keyLength: Length of the keys in bits.
    keyType: Specifies whether this is a key signing key (KSK) or a zone
      signing key (ZSK). Key signing keys have the Secure Entry Point flag set
      and, when active, are only used to sign resource record sets of type
      DNSKEY. Zone signing keys do not have the Secure Entry Point flag set
      and are used to sign all other types of resource record sets.
    kind: A string attribute.
  """

  class AlgorithmValueValuesEnum(_messages.Enum):
    r"""String mnemonic specifying the DNSSEC algorithm of this key.

    Values:
      rsasha1: <no description>
      rsasha256: <no description>
      rsasha512: <no description>
      ecdsap256sha256: <no description>
      ecdsap384sha384: <no description>
    """
    rsasha1 = 0
    rsasha256 = 1
    rsasha512 = 2
    ecdsap256sha256 = 3
    ecdsap384sha384 = 4

  class KeyTypeValueValuesEnum(_messages.Enum):
    r"""Specifies whether this is a key signing key (KSK) or a zone signing
    key (ZSK). Key signing keys have the Secure Entry Point flag set and, when
    active, are only used to sign resource record sets of type DNSKEY. Zone
    signing keys do not have the Secure Entry Point flag set and are used to
    sign all other types of resource record sets.

    Values:
      keySigning: <no description>
      zoneSigning: <no description>
    """
    keySigning = 0
    zoneSigning = 1

  algorithm = _messages.EnumField('AlgorithmValueValuesEnum', 1)
  keyLength = _messages.IntegerField(2, variant=_messages.Variant.UINT32)
  keyType = _messages.EnumField('KeyTypeValueValuesEnum', 3)
  kind = _messages.StringField(4, default='dns#dnsKeySpec')


class DnsKeysListResponse(_messages.Message):
  r"""The response to a request to enumerate DnsKeys in a ManagedZone.

  Fields:
    dnsKeys: The requested resources.
    header: A ResponseHeader attribute.
    kind: Type of resource.
    nextPageToken: The presence of this field indicates that there exist more
      results following your last page of results in pagination order. To
      fetch them, make another list request using this value as your
      pagination token. In this way you can retrieve the complete contents of
      even very large collections one page at a time. However, if the contents
      of the collection change between the first and last paginated list
      request, the set of all elements returned are an inconsistent view of
      the collection. There is no way to retrieve a "snapshot" of collections
      larger than the maximum page size.
  """

  dnsKeys = _messages.MessageField('DnsKey', 1, repeated=True)
  header = _messages.MessageField('ResponseHeader', 2)
  kind = _messages.StringField(3, default='dns#dnsKeysListResponse')
  nextPageToken = _messages.StringField(4)


class DnsManagedZoneOperationsGetRequest(_messages.Message):
  r"""A DnsManagedZoneOperationsGetRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    managedZone: Identifies the managed zone addressed by this request.
    operation: Identifies the operation addressed by this request (ID of the
      operation).
    project: Identifies the project addressed by this request.
  """

  clientOperationId = _messages.StringField(1)
  managedZone = _messages.StringField(2, required=True)
  operation = _messages.StringField(3, required=True)
  project = _messages.StringField(4, required=True)


class DnsManagedZoneOperationsListRequest(_messages.Message):
  r"""A DnsManagedZoneOperationsListRequest object.

  Enums:
    SortByValueValuesEnum: Sorting criterion. The only supported values are
      START_TIME and ID.

  Fields:
    managedZone: Identifies the managed zone addressed by this request.
    maxResults: Optional. Maximum number of results to be returned. If
      unspecified, the server decides how many results to return.
    pageToken: Optional. A tag returned by a previous list request that was
      truncated. Use this parameter to continue a previous list request.
    project: Identifies the project addressed by this request.
    sortBy: Sorting criterion. The only supported values are START_TIME and
      ID.
  """

  class SortByValueValuesEnum(_messages.Enum):
    r"""Sorting criterion. The only supported values are START_TIME and ID.

    Values:
      startTime: <no description>
      id: <no description>
    """
    startTime = 0
    id = 1

  managedZone = _messages.StringField(1, required=True)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  project = _messages.StringField(4, required=True)
  sortBy = _messages.EnumField('SortByValueValuesEnum', 5, default='startTime')


class DnsManagedZonesCreateRequest(_messages.Message):
  r"""A DnsManagedZonesCreateRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    managedZone: A ManagedZone resource to be passed as the request body.
    project: Identifies the project addressed by this request.
  """

  clientOperationId = _messages.StringField(1)
  managedZone = _messages.MessageField('ManagedZone', 2)
  project = _messages.StringField(3, required=True)


class DnsManagedZonesDeleteRequest(_messages.Message):
  r"""A DnsManagedZonesDeleteRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    managedZone: Identifies the managed zone addressed by this request. Can be
      the managed zone name or ID.
    project: Identifies the project addressed by this request.
  """

  clientOperationId = _messages.StringField(1)
  managedZone = _messages.StringField(2, required=True)
  project = _messages.StringField(3, required=True)


class DnsManagedZonesDeleteResponse(_messages.Message):
  r"""An empty DnsManagedZonesDelete response."""


class DnsManagedZonesGetRequest(_messages.Message):
  r"""A DnsManagedZonesGetRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    managedZone: Identifies the managed zone addressed by this request. Can be
      the managed zone name or ID.
    project: Identifies the project addressed by this request.
  """

  clientOperationId = _messages.StringField(1)
  managedZone = _messages.StringField(2, required=True)
  project = _messages.StringField(3, required=True)


class DnsManagedZonesListRequest(_messages.Message):
  r"""A DnsManagedZonesListRequest object.

  Fields:
    dnsName: Restricts the list to return only zones with this domain name.
    maxResults: Optional. Maximum number of results to be returned. If
      unspecified, the server decides how many results to return.
    pageToken: Optional. A tag returned by a previous list request that was
      truncated. Use this parameter to continue a previous list request.
    project: Identifies the project addressed by this request.
  """

  dnsName = _messages.StringField(1)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  project = _messages.StringField(4, required=True)


class DnsManagedZonesPatchRequest(_messages.Message):
  r"""A DnsManagedZonesPatchRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    managedZone: Identifies the managed zone addressed by this request. Can be
      the managed zone name or ID.
    managedZoneResource: A ManagedZone resource to be passed as the request
      body.
    project: Identifies the project addressed by this request.
  """

  clientOperationId = _messages.StringField(1)
  managedZone = _messages.StringField(2, required=True)
  managedZoneResource = _messages.MessageField('ManagedZone', 3)
  project = _messages.StringField(4, required=True)


class DnsManagedZonesUpdateRequest(_messages.Message):
  r"""A DnsManagedZonesUpdateRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    managedZone: Identifies the managed zone addressed by this request. Can be
      the managed zone name or ID.
    managedZoneResource: A ManagedZone resource to be passed as the request
      body.
    project: Identifies the project addressed by this request.
  """

  clientOperationId = _messages.StringField(1)
  managedZone = _messages.StringField(2, required=True)
  managedZoneResource = _messages.MessageField('ManagedZone', 3)
  project = _messages.StringField(4, required=True)


class DnsPoliciesCreateRequest(_messages.Message):
  r"""A DnsPoliciesCreateRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    policy: A Policy resource to be passed as the request body.
    project: Identifies the project addressed by this request.
  """

  clientOperationId = _messages.StringField(1)
  policy = _messages.MessageField('Policy', 2)
  project = _messages.StringField(3, required=True)


class DnsPoliciesDeleteRequest(_messages.Message):
  r"""A DnsPoliciesDeleteRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    policy: User given friendly name of the policy addressed by this request.
    project: Identifies the project addressed by this request.
  """

  clientOperationId = _messages.StringField(1)
  policy = _messages.StringField(2, required=True)
  project = _messages.StringField(3, required=True)


class DnsPoliciesDeleteResponse(_messages.Message):
  r"""An empty DnsPoliciesDelete response."""


class DnsPoliciesGetRequest(_messages.Message):
  r"""A DnsPoliciesGetRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    policy: User given friendly name of the policy addressed by this request.
    project: Identifies the project addressed by this request.
  """

  clientOperationId = _messages.StringField(1)
  policy = _messages.StringField(2, required=True)
  project = _messages.StringField(3, required=True)


class DnsPoliciesListRequest(_messages.Message):
  r"""A DnsPoliciesListRequest object.

  Fields:
    maxResults: Optional. Maximum number of results to be returned. If
      unspecified, the server decides how many results to return.
    pageToken: Optional. A tag returned by a previous list request that was
      truncated. Use this parameter to continue a previous list request.
    project: Identifies the project addressed by this request.
  """

  maxResults = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  project = _messages.StringField(3, required=True)


class DnsPoliciesPatchRequest(_messages.Message):
  r"""A DnsPoliciesPatchRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    policy: User given friendly name of the policy addressed by this request.
    policyResource: A Policy resource to be passed as the request body.
    project: Identifies the project addressed by this request.
  """

  clientOperationId = _messages.StringField(1)
  policy = _messages.StringField(2, required=True)
  policyResource = _messages.MessageField('Policy', 3)
  project = _messages.StringField(4, required=True)


class DnsPoliciesUpdateRequest(_messages.Message):
  r"""A DnsPoliciesUpdateRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    policy: User given friendly name of the policy addressed by this request.
    policyResource: A Policy resource to be passed as the request body.
    project: Identifies the project addressed by this request.
  """

  clientOperationId = _messages.StringField(1)
  policy = _messages.StringField(2, required=True)
  policyResource = _messages.MessageField('Policy', 3)
  project = _messages.StringField(4, required=True)


class DnsProjectsGetRequest(_messages.Message):
  r"""A DnsProjectsGetRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    project: Identifies the project addressed by this request.
  """

  clientOperationId = _messages.StringField(1)
  project = _messages.StringField(2, required=True)


class DnsProjectsManagedZonesGetIamPolicyRequest(_messages.Message):
  r"""A DnsProjectsManagedZonesGetIamPolicyRequest object.

  Fields:
    googleIamV1GetIamPolicyRequest: A GoogleIamV1GetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1GetIamPolicyRequest = _messages.MessageField('GoogleIamV1GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DnsProjectsManagedZonesSetIamPolicyRequest(_messages.Message):
  r"""A DnsProjectsManagedZonesSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class DnsProjectsManagedZonesTestIamPermissionsRequest(_messages.Message):
  r"""A DnsProjectsManagedZonesTestIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class DnsResourceRecordSetsCreateRequest(_messages.Message):
  r"""A DnsResourceRecordSetsCreateRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    managedZone: Identifies the managed zone addressed by this request. Can be
      the managed zone name or ID.
    project: Identifies the project addressed by this request.
    resourceRecordSet: A ResourceRecordSet resource to be passed as the
      request body.
  """

  clientOperationId = _messages.StringField(1)
  managedZone = _messages.StringField(2, required=True)
  project = _messages.StringField(3, required=True)
  resourceRecordSet = _messages.MessageField('ResourceRecordSet', 4)


class DnsResourceRecordSetsDeleteRequest(_messages.Message):
  r"""A DnsResourceRecordSetsDeleteRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    managedZone: Identifies the managed zone addressed by this request. Can be
      the managed zone name or ID.
    name: Fully qualified domain name.
    project: Identifies the project addressed by this request.
    type: RRSet type.
  """

  clientOperationId = _messages.StringField(1)
  managedZone = _messages.StringField(2, required=True)
  name = _messages.StringField(3, required=True)
  project = _messages.StringField(4, required=True)
  type = _messages.StringField(5, required=True)


class DnsResourceRecordSetsDeleteResponse(_messages.Message):
  r"""An empty DnsResourceRecordSetsDelete response."""


class DnsResourceRecordSetsGetRequest(_messages.Message):
  r"""A DnsResourceRecordSetsGetRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    managedZone: Identifies the managed zone addressed by this request. Can be
      the managed zone name or ID.
    name: Fully qualified domain name.
    project: Identifies the project addressed by this request.
    type: RRSet type.
  """

  clientOperationId = _messages.StringField(1)
  managedZone = _messages.StringField(2, required=True)
  name = _messages.StringField(3, required=True)
  project = _messages.StringField(4, required=True)
  type = _messages.StringField(5, required=True)


class DnsResourceRecordSetsListRequest(_messages.Message):
  r"""A DnsResourceRecordSetsListRequest object.

  Fields:
    managedZone: Identifies the managed zone addressed by this request. Can be
      the managed zone name or ID.
    maxResults: Optional. Maximum number of results to be returned. If
      unspecified, the server decides how many results to return.
    name: Restricts the list to return only records with this fully qualified
      domain name.
    pageToken: Optional. A tag returned by a previous list request that was
      truncated. Use this parameter to continue a previous list request.
    project: Identifies the project addressed by this request.
    type: Restricts the list to return only records of this type. If present,
      the "name" parameter must also be present.
  """

  managedZone = _messages.StringField(1, required=True)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  name = _messages.StringField(3)
  pageToken = _messages.StringField(4)
  project = _messages.StringField(5, required=True)
  type = _messages.StringField(6)


class DnsResourceRecordSetsPatchRequest(_messages.Message):
  r"""A DnsResourceRecordSetsPatchRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    managedZone: Identifies the managed zone addressed by this request. Can be
      the managed zone name or ID.
    name: Fully qualified domain name.
    project: Identifies the project addressed by this request.
    resourceRecordSet: A ResourceRecordSet resource to be passed as the
      request body.
    type: RRSet type.
  """

  clientOperationId = _messages.StringField(1)
  managedZone = _messages.StringField(2, required=True)
  name = _messages.StringField(3, required=True)
  project = _messages.StringField(4, required=True)
  resourceRecordSet = _messages.MessageField('ResourceRecordSet', 5)
  type = _messages.StringField(6, required=True)


class DnsResponsePoliciesCreateRequest(_messages.Message):
  r"""A DnsResponsePoliciesCreateRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    project: Identifies the project addressed by this request.
    responsePolicy: A ResponsePolicy resource to be passed as the request
      body.
  """

  clientOperationId = _messages.StringField(1)
  project = _messages.StringField(2, required=True)
  responsePolicy = _messages.MessageField('ResponsePolicy', 3)


class DnsResponsePoliciesDeleteRequest(_messages.Message):
  r"""A DnsResponsePoliciesDeleteRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    project: Identifies the project addressed by this request.
    responsePolicy: User assigned name of the Response Policy addressed by
      this request.
  """

  clientOperationId = _messages.StringField(1)
  project = _messages.StringField(2, required=True)
  responsePolicy = _messages.StringField(3, required=True)


class DnsResponsePoliciesDeleteResponse(_messages.Message):
  r"""An empty DnsResponsePoliciesDelete response."""


class DnsResponsePoliciesGetRequest(_messages.Message):
  r"""A DnsResponsePoliciesGetRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    project: Identifies the project addressed by this request.
    responsePolicy: User assigned name of the Response Policy addressed by
      this request.
  """

  clientOperationId = _messages.StringField(1)
  project = _messages.StringField(2, required=True)
  responsePolicy = _messages.StringField(3, required=True)


class DnsResponsePoliciesListRequest(_messages.Message):
  r"""A DnsResponsePoliciesListRequest object.

  Fields:
    maxResults: Optional. Maximum number of results to be returned. If
      unspecified, the server decides how many results to return.
    pageToken: Optional. A tag returned by a previous list request that was
      truncated. Use this parameter to continue a previous list request.
    project: Identifies the project addressed by this request.
  """

  maxResults = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  project = _messages.StringField(3, required=True)


class DnsResponsePoliciesPatchRequest(_messages.Message):
  r"""A DnsResponsePoliciesPatchRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    project: Identifies the project addressed by this request.
    responsePolicy: User assigned name of the response policy addressed by
      this request.
    responsePolicyResource: A ResponsePolicy resource to be passed as the
      request body.
  """

  clientOperationId = _messages.StringField(1)
  project = _messages.StringField(2, required=True)
  responsePolicy = _messages.StringField(3, required=True)
  responsePolicyResource = _messages.MessageField('ResponsePolicy', 4)


class DnsResponsePoliciesUpdateRequest(_messages.Message):
  r"""A DnsResponsePoliciesUpdateRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    project: Identifies the project addressed by this request.
    responsePolicy: User assigned name of the Response Policy addressed by
      this request.
    responsePolicyResource: A ResponsePolicy resource to be passed as the
      request body.
  """

  clientOperationId = _messages.StringField(1)
  project = _messages.StringField(2, required=True)
  responsePolicy = _messages.StringField(3, required=True)
  responsePolicyResource = _messages.MessageField('ResponsePolicy', 4)


class DnsResponsePolicyRulesCreateRequest(_messages.Message):
  r"""A DnsResponsePolicyRulesCreateRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    project: Identifies the project addressed by this request.
    responsePolicy: User assigned name of the Response Policy containing the
      Response Policy Rule.
    responsePolicyRule: A ResponsePolicyRule resource to be passed as the
      request body.
  """

  clientOperationId = _messages.StringField(1)
  project = _messages.StringField(2, required=True)
  responsePolicy = _messages.StringField(3, required=True)
  responsePolicyRule = _messages.MessageField('ResponsePolicyRule', 4)


class DnsResponsePolicyRulesDeleteRequest(_messages.Message):
  r"""A DnsResponsePolicyRulesDeleteRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    project: Identifies the project addressed by this request.
    responsePolicy: User assigned name of the Response Policy containing the
      Response Policy Rule.
    responsePolicyRule: User assigned name of the Response Policy Rule
      addressed by this request.
  """

  clientOperationId = _messages.StringField(1)
  project = _messages.StringField(2, required=True)
  responsePolicy = _messages.StringField(3, required=True)
  responsePolicyRule = _messages.StringField(4, required=True)


class DnsResponsePolicyRulesDeleteResponse(_messages.Message):
  r"""An empty DnsResponsePolicyRulesDelete response."""


class DnsResponsePolicyRulesGetRequest(_messages.Message):
  r"""A DnsResponsePolicyRulesGetRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    project: Identifies the project addressed by this request.
    responsePolicy: User assigned name of the Response Policy containing the
      Response Policy Rule.
    responsePolicyRule: User assigned name of the Response Policy Rule
      addressed by this request.
  """

  clientOperationId = _messages.StringField(1)
  project = _messages.StringField(2, required=True)
  responsePolicy = _messages.StringField(3, required=True)
  responsePolicyRule = _messages.StringField(4, required=True)


class DnsResponsePolicyRulesListRequest(_messages.Message):
  r"""A DnsResponsePolicyRulesListRequest object.

  Fields:
    maxResults: Optional. Maximum number of results to be returned. If
      unspecified, the server decides how many results to return.
    pageToken: Optional. A tag returned by a previous list request that was
      truncated. Use this parameter to continue a previous list request.
    project: Identifies the project addressed by this request.
    responsePolicy: User assigned name of the Response Policy to list.
  """

  maxResults = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  project = _messages.StringField(3, required=True)
  responsePolicy = _messages.StringField(4, required=True)


class DnsResponsePolicyRulesPatchRequest(_messages.Message):
  r"""A DnsResponsePolicyRulesPatchRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    project: Identifies the project addressed by this request.
    responsePolicy: User assigned name of the Response Policy containing the
      Response Policy Rule.
    responsePolicyRule: User assigned name of the Response Policy Rule
      addressed by this request.
    responsePolicyRuleResource: A ResponsePolicyRule resource to be passed as
      the request body.
  """

  clientOperationId = _messages.StringField(1)
  project = _messages.StringField(2, required=True)
  responsePolicy = _messages.StringField(3, required=True)
  responsePolicyRule = _messages.StringField(4, required=True)
  responsePolicyRuleResource = _messages.MessageField('ResponsePolicyRule', 5)


class DnsResponsePolicyRulesUpdateRequest(_messages.Message):
  r"""A DnsResponsePolicyRulesUpdateRequest object.

  Fields:
    clientOperationId: For mutating operation requests only. An optional
      identifier specified by the client. Must be unique for operation
      resources in the Operations collection.
    project: Identifies the project addressed by this request.
    responsePolicy: User assigned name of the Response Policy containing the
      Response Policy Rule.
    responsePolicyRule: User assigned name of the Response Policy Rule
      addressed by this request.
    responsePolicyRuleResource: A ResponsePolicyRule resource to be passed as
      the request body.
  """

  clientOperationId = _messages.StringField(1)
  project = _messages.StringField(2, required=True)
  responsePolicy = _messages.StringField(3, required=True)
  responsePolicyRule = _messages.StringField(4, required=True)
  responsePolicyRuleResource = _messages.MessageField('ResponsePolicyRule', 5)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GoogleIamV1AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('GoogleIamV1AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class GoogleIamV1AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class GoogleIamV1Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class GoogleIamV1GetIamPolicyRequest(_messages.Message):
  r"""Request message for `GetIamPolicy` method.

  Fields:
    options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
      `GetIamPolicy`.
  """

  options = _messages.MessageField('GoogleIamV1GetPolicyOptions', 1)


class GoogleIamV1GetPolicyOptions(_messages.Message):
  r"""Encapsulates settings provided to GetIamPolicy.

  Fields:
    requestedPolicyVersion: Optional. The maximum policy version that will be
      used to format the policy. Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected. Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset. The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1. To learn which resources support conditions in
      their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GoogleIamV1Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('GoogleIamV1AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('GoogleIamV1Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleIamV1SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('GoogleIamV1Policy', 1)
  updateMask = _messages.StringField(2)


class GoogleIamV1TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class GoogleIamV1TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class ManagedZone(_messages.Message):
  r"""A zone is a subtree of the DNS namespace under one administrative
  responsibility. A ManagedZone is a resource that represents a DNS zone
  hosted by the Cloud DNS service.

  Enums:
    VisibilityValueValuesEnum: The zone's visibility: public zones are exposed
      to the Internet, while private zones are visible only to Virtual Private
      Cloud resources.

  Messages:
    LabelsValue: User labels.

  Fields:
    cloudLoggingConfig: A ManagedZoneCloudLoggingConfig attribute.
    creationTime: The time that this resource was created on the server. This
      is in RFC3339 text format. Output only.
    description: A mutable string of at most 1024 characters associated with
      this resource for the user's convenience. Has no effect on the managed
      zone's function.
    dnsName: The DNS name of this managed zone, for instance "example.com.".
    dnssecConfig: DNSSEC configuration.
    forwardingConfig: The presence for this field indicates that outbound
      forwarding is enabled for this zone. The value of this field contains
      the set of destinations to forward to.
    id: Unique identifier for the resource; defined by the server (output
      only)
    kind: A string attribute.
    labels: User labels.
    name: User assigned name for this resource. Must be unique within the
      project. The name must be 1-63 characters long, must begin with a
      letter, end with a letter or digit, and only contain lowercase letters,
      digits or dashes.
    nameServerSet: Optionally specifies the NameServerSet for this
      ManagedZone. A NameServerSet is a set of DNS name servers that all host
      the same ManagedZones. Most users leave this field unset. If you need to
      use this field, contact your account team.
    nameServers: Delegate your managed_zone to these virtual name servers;
      defined by the server (output only)
    peeringConfig: The presence of this field indicates that DNS Peering is
      enabled for this zone. The value of this field contains the network to
      peer with.
    privateVisibilityConfig: For privately visible zones, the set of Virtual
      Private Cloud resources that the zone is visible from.
    reverseLookupConfig: The presence of this field indicates that this is a
      managed reverse lookup zone and Cloud DNS resolves reverse lookup
      queries using automatically configured records for VPC resources. This
      only applies to networks listed under private_visibility_config.
    serviceDirectoryConfig: This field links to the associated service
      directory namespace. Do not set this field for public zones or
      forwarding zones.
    visibility: The zone's visibility: public zones are exposed to the
      Internet, while private zones are visible only to Virtual Private Cloud
      resources.
  """

  class VisibilityValueValuesEnum(_messages.Enum):
    r"""The zone's visibility: public zones are exposed to the Internet, while
    private zones are visible only to Virtual Private Cloud resources.

    Values:
      public: <no description>
      private: <no description>
    """
    public = 0
    private = 1

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cloudLoggingConfig = _messages.MessageField('ManagedZoneCloudLoggingConfig', 1)
  creationTime = _messages.StringField(2)
  description = _messages.StringField(3)
  dnsName = _messages.StringField(4)
  dnssecConfig = _messages.MessageField('ManagedZoneDnsSecConfig', 5)
  forwardingConfig = _messages.MessageField('ManagedZoneForwardingConfig', 6)
  id = _messages.IntegerField(7, variant=_messages.Variant.UINT64)
  kind = _messages.StringField(8, default='dns#managedZone')
  labels = _messages.MessageField('LabelsValue', 9)
  name = _messages.StringField(10)
  nameServerSet = _messages.StringField(11)
  nameServers = _messages.StringField(12, repeated=True)
  peeringConfig = _messages.MessageField('ManagedZonePeeringConfig', 13)
  privateVisibilityConfig = _messages.MessageField('ManagedZonePrivateVisibilityConfig', 14)
  reverseLookupConfig = _messages.MessageField('ManagedZoneReverseLookupConfig', 15)
  serviceDirectoryConfig = _messages.MessageField('ManagedZoneServiceDirectoryConfig', 16)
  visibility = _messages.EnumField('VisibilityValueValuesEnum', 17)


class ManagedZoneCloudLoggingConfig(_messages.Message):
  r"""Cloud Logging configurations for publicly visible zones.

  Fields:
    enableLogging: If set, enable query logging for this ManagedZone. False by
      default, making logging opt-in.
    kind: A string attribute.
  """

  enableLogging = _messages.BooleanField(1)
  kind = _messages.StringField(2, default='dns#managedZoneCloudLoggingConfig')


class ManagedZoneDnsSecConfig(_messages.Message):
  r"""A ManagedZoneDnsSecConfig object.

  Enums:
    NonExistenceValueValuesEnum: Specifies the mechanism for authenticated
      denial-of-existence responses. Can only be changed while the state is
      OFF.
    StateValueValuesEnum: Specifies whether DNSSEC is enabled, and what mode
      it is in.

  Fields:
    defaultKeySpecs: Specifies parameters for generating initial DnsKeys for
      this ManagedZone. Can only be changed while the state is OFF.
    kind: A string attribute.
    nonExistence: Specifies the mechanism for authenticated denial-of-
      existence responses. Can only be changed while the state is OFF.
    state: Specifies whether DNSSEC is enabled, and what mode it is in.
  """

  class NonExistenceValueValuesEnum(_messages.Enum):
    r"""Specifies the mechanism for authenticated denial-of-existence
    responses. Can only be changed while the state is OFF.

    Values:
      nsec: <no description>
      nsec3: <no description>
    """
    nsec = 0
    nsec3 = 1

  class StateValueValuesEnum(_messages.Enum):
    r"""Specifies whether DNSSEC is enabled, and what mode it is in.

    Values:
      off: DNSSEC is disabled; the zone is not signed.
      on: DNSSEC is enabled; the zone is signed and fully managed.
      transfer: DNSSEC is enabled, but in a "transfer" mode.
    """
    off = 0
    on = 1
    transfer = 2

  defaultKeySpecs = _messages.MessageField('DnsKeySpec', 1, repeated=True)
  kind = _messages.StringField(2, default='dns#managedZoneDnsSecConfig')
  nonExistence = _messages.EnumField('NonExistenceValueValuesEnum', 3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class ManagedZoneForwardingConfig(_messages.Message):
  r"""A ManagedZoneForwardingConfig object.

  Fields:
    kind: A string attribute.
    targetNameServers: List of target name servers to forward to. Cloud DNS
      selects the best available name server if more than one target is given.
  """

  kind = _messages.StringField(1, default='dns#managedZoneForwardingConfig')
  targetNameServers = _messages.MessageField('ManagedZoneForwardingConfigNameServerTarget', 2, repeated=True)


class ManagedZoneForwardingConfigNameServerTarget(_messages.Message):
  r"""A ManagedZoneForwardingConfigNameServerTarget object.

  Enums:
    ForwardingPathValueValuesEnum: Forwarding path for this NameServerTarget.
      If unset or set to DEFAULT, Cloud DNS makes forwarding decisions based
      on IP address ranges; that is, RFC1918 addresses go to the VPC network,
      non-RFC1918 addresses go to the internet. When set to PRIVATE, Cloud DNS
      always sends queries through the VPC network for this target.

  Fields:
    forwardingPath: Forwarding path for this NameServerTarget. If unset or set
      to DEFAULT, Cloud DNS makes forwarding decisions based on IP address
      ranges; that is, RFC1918 addresses go to the VPC network, non-RFC1918
      addresses go to the internet. When set to PRIVATE, Cloud DNS always
      sends queries through the VPC network for this target.
    ipv4Address: IPv4 address of a target name server.
    ipv6Address: IPv6 address of a target name server. Does not accept both
      fields (ipv4 & ipv6) being populated. Public preview as of November
      2022.
    kind: A string attribute.
  """

  class ForwardingPathValueValuesEnum(_messages.Enum):
    r"""Forwarding path for this NameServerTarget. If unset or set to DEFAULT,
    Cloud DNS makes forwarding decisions based on IP address ranges; that is,
    RFC1918 addresses go to the VPC network, non-RFC1918 addresses go to the
    internet. When set to PRIVATE, Cloud DNS always sends queries through the
    VPC network for this target.

    Values:
      default: Cloud DNS makes forwarding decisions based on address ranges;
        that is, RFC1918 addresses forward to the target through the VPC and
        non-RFC1918 addresses forward to the target through the internet
      private: Cloud DNS always forwards to this target through the VPC.
    """
    default = 0
    private = 1

  forwardingPath = _messages.EnumField('ForwardingPathValueValuesEnum', 1)
  ipv4Address = _messages.StringField(2)
  ipv6Address = _messages.StringField(3)
  kind = _messages.StringField(4, default='dns#managedZoneForwardingConfigNameServerTarget')


class ManagedZoneOperationsListResponse(_messages.Message):
  r"""A ManagedZoneOperationsListResponse object.

  Fields:
    header: A ResponseHeader attribute.
    kind: Type of resource.
    nextPageToken: The presence of this field indicates that there exist more
      results following your last page of results in pagination order. To
      fetch them, make another list request using this value as your page
      token. This lets you retrieve the complete contents of even very large
      collections one page at a time. However, if the contents of the
      collection change between the first and last paginated list request, the
      set of all elements returned are an inconsistent view of the collection.
      You cannot retrieve a consistent snapshot of a collection larger than
      the maximum page size.
    operations: The operation resources.
  """

  header = _messages.MessageField('ResponseHeader', 1)
  kind = _messages.StringField(2, default='dns#managedZoneOperationsListResponse')
  nextPageToken = _messages.StringField(3)
  operations = _messages.MessageField('Operation', 4, repeated=True)


class ManagedZonePeeringConfig(_messages.Message):
  r"""A ManagedZonePeeringConfig object.

  Fields:
    kind: A string attribute.
    targetNetwork: The network with which to peer.
  """

  kind = _messages.StringField(1, default='dns#managedZonePeeringConfig')
  targetNetwork = _messages.MessageField('ManagedZonePeeringConfigTargetNetwork', 2)


class ManagedZonePeeringConfigTargetNetwork(_messages.Message):
  r"""A ManagedZonePeeringConfigTargetNetwork object.

  Fields:
    deactivateTime: The time at which the zone was deactivated, in RFC 3339
      date-time format. An empty string indicates that the peering connection
      is active. The producer network can deactivate a zone. The zone is
      automatically deactivated if the producer network that the zone targeted
      is deleted. Output only.
    kind: A string attribute.
    networkUrl: The fully qualified URL of the VPC network to forward queries
      to. This should be formatted like https://www.googleapis.com/compute/v1/
      projects/{project}/global/networks/{network}
  """

  deactivateTime = _messages.StringField(1)
  kind = _messages.StringField(2, default='dns#managedZonePeeringConfigTargetNetwork')
  networkUrl = _messages.StringField(3)


class ManagedZonePrivateVisibilityConfig(_messages.Message):
  r"""A ManagedZonePrivateVisibilityConfig object.

  Fields:
    gkeClusters: The list of Google Kubernetes Engine clusters that can see
      this zone.
    kind: A string attribute.
    networks: The list of VPC networks that can see this zone.
  """

  gkeClusters = _messages.MessageField('ManagedZonePrivateVisibilityConfigGKECluster', 1, repeated=True)
  kind = _messages.StringField(2, default='dns#managedZonePrivateVisibilityConfig')
  networks = _messages.MessageField('ManagedZonePrivateVisibilityConfigNetwork', 3, repeated=True)


class ManagedZonePrivateVisibilityConfigGKECluster(_messages.Message):
  r"""A ManagedZonePrivateVisibilityConfigGKECluster object.

  Fields:
    gkeClusterName: The resource name of the cluster to bind this ManagedZone
      to. This should be specified in the format like:
      projects/*/locations/*/clusters/*. This is referenced from GKE
      projects.locations.clusters.get API:
      https://cloud.google.com/kubernetes-
      engine/docs/reference/rest/v1/projects.locations.clusters/get
    kind: A string attribute.
  """

  gkeClusterName = _messages.StringField(1)
  kind = _messages.StringField(2, default='dns#managedZonePrivateVisibilityConfigGKECluster')


class ManagedZonePrivateVisibilityConfigNetwork(_messages.Message):
  r"""A ManagedZonePrivateVisibilityConfigNetwork object.

  Fields:
    kind: A string attribute.
    networkUrl: The fully qualified URL of the VPC network to bind to. Format
      this URL like https://www.googleapis.com/compute/v1/projects/{project}/g
      lobal/networks/{network}
  """

  kind = _messages.StringField(1, default='dns#managedZonePrivateVisibilityConfigNetwork')
  networkUrl = _messages.StringField(2)


class ManagedZoneReverseLookupConfig(_messages.Message):
  r"""A ManagedZoneReverseLookupConfig object.

  Fields:
    kind: A string attribute.
  """

  kind = _messages.StringField(1, default='dns#managedZoneReverseLookupConfig')


class ManagedZoneServiceDirectoryConfig(_messages.Message):
  r"""Contains information about Service Directory-backed zones.

  Fields:
    kind: A string attribute.
    namespace: Contains information about the namespace associated with the
      zone.
  """

  kind = _messages.StringField(1, default='dns#managedZoneServiceDirectoryConfig')
  namespace = _messages.MessageField('ManagedZoneServiceDirectoryConfigNamespace', 2)


class ManagedZoneServiceDirectoryConfigNamespace(_messages.Message):
  r"""A ManagedZoneServiceDirectoryConfigNamespace object.

  Fields:
    deletionTime: The time that the namespace backing this zone was deleted;
      an empty string if it still exists. This is in RFC3339 text format.
      Output only.
    kind: A string attribute.
    namespaceUrl: The fully qualified URL of the namespace associated with the
      zone. Format must be https://servicedirectory.googleapis.com/v1/projects
      /{project}/locations/{location}/namespaces/{namespace}
  """

  deletionTime = _messages.StringField(1)
  kind = _messages.StringField(2, default='dns#managedZoneServiceDirectoryConfigNamespace')
  namespaceUrl = _messages.StringField(3)


class ManagedZonesListResponse(_messages.Message):
  r"""A ManagedZonesListResponse object.

  Fields:
    header: A ResponseHeader attribute.
    kind: Type of resource.
    managedZones: The managed zone resources.
    nextPageToken: The presence of this field indicates that there exist more
      results following your last page of results in pagination order. To
      fetch them, make another list request using this value as your page
      token. This lets you the complete contents of even very large
      collections one page at a time. However, if the contents of the
      collection change between the first and last paginated list request, the
      set of all elements returned are an inconsistent view of the collection.
      You cannot retrieve a consistent snapshot of a collection larger than
      the maximum page size.
  """

  header = _messages.MessageField('ResponseHeader', 1)
  kind = _messages.StringField(2, default='dns#managedZonesListResponse')
  managedZones = _messages.MessageField('ManagedZone', 3, repeated=True)
  nextPageToken = _messages.StringField(4)


class Operation(_messages.Message):
  r"""An operation represents a successful mutation performed on a Cloud DNS
  resource. Operations provide: - An audit log of server resource mutations. -
  A way to recover/retry API calls in the case where the response is never
  received by the caller. Use the caller specified client_operation_id.

  Enums:
    StatusValueValuesEnum: Status of the operation. Can be one of the
      following: "PENDING" or "DONE" (output only). A status of "DONE" means
      that the request to update the authoritative servers has been sent, but
      the servers might not be updated yet.

  Fields:
    dnsKeyContext: Only populated if the operation targeted a DnsKey (output
      only).
    id: Unique identifier for the resource. This is the client_operation_id if
      the client specified it when the mutation was initiated, otherwise, it
      is generated by the server. The name must be 1-63 characters long and
      match the regular expression [-a-z0-9]? (output only)
    kind: A string attribute.
    startTime: The time that this operation was started by the server. This is
      in RFC3339 text format (output only).
    status: Status of the operation. Can be one of the following: "PENDING" or
      "DONE" (output only). A status of "DONE" means that the request to
      update the authoritative servers has been sent, but the servers might
      not be updated yet.
    type: Type of the operation. Operations include insert, update, and delete
      (output only).
    user: User who requested the operation, for example: <EMAIL>.
      cloud-dns-system for operations automatically done by the system.
      (output only)
    zoneContext: Only populated if the operation targeted a ManagedZone
      (output only).
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Status of the operation. Can be one of the following: "PENDING" or
    "DONE" (output only). A status of "DONE" means that the request to update
    the authoritative servers has been sent, but the servers might not be
    updated yet.

    Values:
      pending: <no description>
      done: <no description>
    """
    pending = 0
    done = 1

  dnsKeyContext = _messages.MessageField('OperationDnsKeyContext', 1)
  id = _messages.StringField(2)
  kind = _messages.StringField(3, default='dns#operation')
  startTime = _messages.StringField(4)
  status = _messages.EnumField('StatusValueValuesEnum', 5)
  type = _messages.StringField(6)
  user = _messages.StringField(7)
  zoneContext = _messages.MessageField('OperationManagedZoneContext', 8)


class OperationDnsKeyContext(_messages.Message):
  r"""A OperationDnsKeyContext object.

  Fields:
    newValue: The post-operation DnsKey resource.
    oldValue: The pre-operation DnsKey resource.
  """

  newValue = _messages.MessageField('DnsKey', 1)
  oldValue = _messages.MessageField('DnsKey', 2)


class OperationManagedZoneContext(_messages.Message):
  r"""A OperationManagedZoneContext object.

  Fields:
    newValue: The post-operation ManagedZone resource.
    oldValue: The pre-operation ManagedZone resource.
  """

  newValue = _messages.MessageField('ManagedZone', 1)
  oldValue = _messages.MessageField('ManagedZone', 2)


class PeeringZoneDeactivateResponse(_messages.Message):
  r"""A PeeringZoneDeactivateResponse object.

  Fields:
    deactivateSucceeded: True if the zone is deactivated by this request,
      false if the zone exists and is of type peering zone but was already
      deactivated.
    header: A ResponseHeader attribute.
  """

  deactivateSucceeded = _messages.BooleanField(1)
  header = _messages.MessageField('ResponseHeader', 2)


class PeeringZonesListResponse(_messages.Message):
  r"""A PeeringZonesListResponse object.

  Fields:
    header: A ResponseHeader attribute.
    kind: A string attribute.
    nextPageToken: The presence of this field indicates that there exist more
      results following your last page of results in pagination order. To
      fetch them, make another list request using this value as your page
      token. This lets you the complete contents of even very large
      collections one page at a time. However, if the contents of the
      collection change between the first and last paginated list request, the
      set of all elements returned are an inconsistent view of the collection.
      You cannot retrieve a consistent snapshot of a collection larger than
      the maximum page size.
    peeringZones: The active peering zone resources matching the request. Each
      ManagedZone has only the id field set.
  """

  header = _messages.MessageField('ResponseHeader', 1)
  kind = _messages.StringField(2, default='dns#peeringZonesListResponse')
  nextPageToken = _messages.StringField(3)
  peeringZones = _messages.MessageField('ManagedZone', 4, repeated=True)


class PoliciesListResponse(_messages.Message):
  r"""A PoliciesListResponse object.

  Fields:
    header: A ResponseHeader attribute.
    kind: Type of resource.
    nextPageToken: The presence of this field indicates that there exist more
      results following your last page of results in pagination order. To
      fetch them, make another list request using this value as your page
      token. This lets you the complete contents of even very large
      collections one page at a time. However, if the contents of the
      collection change between the first and last paginated list request, the
      set of all elements returned are an inconsistent view of the collection.
      You cannot retrieve a consistent snapshot of a collection larger than
      the maximum page size.
    policies: The policy resources.
  """

  header = _messages.MessageField('ResponseHeader', 1)
  kind = _messages.StringField(2, default='dns#policiesListResponse')
  nextPageToken = _messages.StringField(3)
  policies = _messages.MessageField('Policy', 4, repeated=True)


class PoliciesPatchResponse(_messages.Message):
  r"""A PoliciesPatchResponse object.

  Fields:
    header: A ResponseHeader attribute.
    policy: A Policy attribute.
  """

  header = _messages.MessageField('ResponseHeader', 1)
  policy = _messages.MessageField('Policy', 2)


class PoliciesUpdateResponse(_messages.Message):
  r"""A PoliciesUpdateResponse object.

  Fields:
    header: A ResponseHeader attribute.
    policy: A Policy attribute.
  """

  header = _messages.MessageField('ResponseHeader', 1)
  policy = _messages.MessageField('Policy', 2)


class Policy(_messages.Message):
  r"""A policy is a collection of DNS rules applied to one or more Virtual
  Private Cloud resources.

  Fields:
    alternativeNameServerConfig: Sets an alternative name server for the
      associated networks. When specified, all DNS queries are forwarded to a
      name server that you choose. Names such as .internal are not available
      when an alternative name server is specified.
    description: A mutable string of at most 1024 characters associated with
      this resource for the user's convenience. Has no effect on the policy's
      function.
    enableInboundForwarding: Allows networks bound to this policy to receive
      DNS queries sent by VMs or applications over VPN connections. When
      enabled, a virtual IP address is allocated from each of the subnetworks
      that are bound to this policy.
    enableLogging: Controls whether logging is enabled for the networks bound
      to this policy. Defaults to no logging if not set.
    id: Unique identifier for the resource; defined by the server (output
      only).
    kind: A string attribute.
    name: User-assigned name for this policy.
    networks: List of network names specifying networks to which this policy
      is applied.
  """

  alternativeNameServerConfig = _messages.MessageField('PolicyAlternativeNameServerConfig', 1)
  description = _messages.StringField(2)
  enableInboundForwarding = _messages.BooleanField(3)
  enableLogging = _messages.BooleanField(4)
  id = _messages.IntegerField(5, variant=_messages.Variant.UINT64)
  kind = _messages.StringField(6, default='dns#policy')
  name = _messages.StringField(7)
  networks = _messages.MessageField('PolicyNetwork', 8, repeated=True)


class PolicyAlternativeNameServerConfig(_messages.Message):
  r"""A PolicyAlternativeNameServerConfig object.

  Fields:
    kind: A string attribute.
    targetNameServers: Sets an alternative name server for the associated
      networks. When specified, all DNS queries are forwarded to a name server
      that you choose. Names such as .internal are not available when an
      alternative name server is specified.
  """

  kind = _messages.StringField(1, default='dns#policyAlternativeNameServerConfig')
  targetNameServers = _messages.MessageField('PolicyAlternativeNameServerConfigTargetNameServer', 2, repeated=True)


class PolicyAlternativeNameServerConfigTargetNameServer(_messages.Message):
  r"""A PolicyAlternativeNameServerConfigTargetNameServer object.

  Enums:
    ForwardingPathValueValuesEnum: Forwarding path for this TargetNameServer.
      If unset or set to DEFAULT, Cloud DNS makes forwarding decisions based
      on address ranges; that is, RFC1918 addresses go to the VPC network,
      non-RFC1918 addresses go to the internet. When set to PRIVATE, Cloud DNS
      always sends queries through the VPC network for this target.

  Fields:
    forwardingPath: Forwarding path for this TargetNameServer. If unset or set
      to DEFAULT, Cloud DNS makes forwarding decisions based on address
      ranges; that is, RFC1918 addresses go to the VPC network, non-RFC1918
      addresses go to the internet. When set to PRIVATE, Cloud DNS always
      sends queries through the VPC network for this target.
    ipv4Address: IPv4 address to forward queries to.
    ipv6Address: IPv6 address to forward to. Does not accept both fields (ipv4
      & ipv6) being populated. Public preview as of November 2022.
    kind: A string attribute.
  """

  class ForwardingPathValueValuesEnum(_messages.Enum):
    r"""Forwarding path for this TargetNameServer. If unset or set to DEFAULT,
    Cloud DNS makes forwarding decisions based on address ranges; that is,
    RFC1918 addresses go to the VPC network, non-RFC1918 addresses go to the
    internet. When set to PRIVATE, Cloud DNS always sends queries through the
    VPC network for this target.

    Values:
      default: Cloud DNS makes forwarding decision based on IP address ranges;
        that is, RFC1918 addresses forward to the target through the VPC and
        non-RFC1918 addresses forward to the target through the internet
      private: Cloud DNS always forwards to this target through the VPC.
    """
    default = 0
    private = 1

  forwardingPath = _messages.EnumField('ForwardingPathValueValuesEnum', 1)
  ipv4Address = _messages.StringField(2)
  ipv6Address = _messages.StringField(3)
  kind = _messages.StringField(4, default='dns#policyAlternativeNameServerConfigTargetNameServer')


class PolicyNetwork(_messages.Message):
  r"""A PolicyNetwork object.

  Fields:
    kind: A string attribute.
    networkUrl: The fully qualified URL of the VPC network to bind to. This
      should be formatted like https://www.googleapis.com/compute/v1/projects/
      {project}/global/networks/{network}
  """

  kind = _messages.StringField(1, default='dns#policyNetwork')
  networkUrl = _messages.StringField(2)


class Project(_messages.Message):
  r"""A project resource. The project is a top level container for resources
  including Cloud DNS ManagedZones. Projects can be created only in the APIs
  console. Next tag: 7.

  Fields:
    id: User assigned unique identifier for the resource (output only).
    kind: A string attribute.
    number: Unique numeric identifier for the resource; defined by the server
      (output only).
    quota: Quotas assigned to this project (output only).
    whitelistedFeatures: A list of alpha/temporary/experimental features
      enabled for this project (output only).
  """

  id = _messages.StringField(1)
  kind = _messages.StringField(2, default='dns#project')
  number = _messages.IntegerField(3, variant=_messages.Variant.UINT64)
  quota = _messages.MessageField('Quota', 4)
  whitelistedFeatures = _messages.StringField(5, repeated=True)


class Quota(_messages.Message):
  r"""Limits associated with a Project.

  Fields:
    dnsKeysPerManagedZone: Maximum allowed number of DnsKeys per ManagedZone.
    gkeClustersPerManagedZone: Maximum allowed number of GKE clusters to which
      a privately scoped zone can be attached.
    gkeClustersPerPolicy: Maximum allowed number of GKE clusters per policy.
    gkeClustersPerResponsePolicy: Maximum allowed number of GKE clusters per
      response policy.
    itemsPerRoutingPolicy: Maximum allowed number of items per routing policy.
    kind: A string attribute.
    managedZones: Maximum allowed number of managed zones in the project.
    managedZonesPerGkeCluster: Maximum allowed number of managed zones which
      can be attached to a GKE cluster.
    managedZonesPerNetwork: Maximum allowed number of managed zones which can
      be attached to a network.
    networksPerManagedZone: Maximum allowed number of networks to which a
      privately scoped zone can be attached.
    networksPerPolicy: Maximum allowed number of networks per policy.
    networksPerResponsePolicy: Maximum allowed number of networks per response
      policy.
    peeringZonesPerTargetNetwork: Maximum allowed number of consumer peering
      zones per target network owned by this producer project
    policies: Maximum allowed number of policies per project.
    resourceRecordsPerRrset: Maximum allowed number of ResourceRecords per
      ResourceRecordSet.
    responsePolicies: Maximum allowed number of response policies per project.
    responsePolicyRulesPerResponsePolicy: Maximum allowed number of rules per
      response policy.
    rrsetAdditionsPerChange: Maximum allowed number of ResourceRecordSets to
      add per ChangesCreateRequest.
    rrsetDeletionsPerChange: Maximum allowed number of ResourceRecordSets to
      delete per ChangesCreateRequest.
    rrsetsPerManagedZone: Maximum allowed number of ResourceRecordSets per
      zone in the project.
    targetNameServersPerManagedZone: Maximum allowed number of target name
      servers per managed forwarding zone.
    targetNameServersPerPolicy: Maximum allowed number of alternative target
      name servers per policy.
    totalRrdataSizePerChange: Maximum allowed size for total rrdata in one
      ChangesCreateRequest in bytes.
    whitelistedKeySpecs: DNSSEC algorithm and key length types that can be
      used for DnsKeys.
  """

  dnsKeysPerManagedZone = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  gkeClustersPerManagedZone = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  gkeClustersPerPolicy = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  gkeClustersPerResponsePolicy = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  itemsPerRoutingPolicy = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  kind = _messages.StringField(6, default='dns#quota')
  managedZones = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  managedZonesPerGkeCluster = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  managedZonesPerNetwork = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  networksPerManagedZone = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  networksPerPolicy = _messages.IntegerField(11, variant=_messages.Variant.INT32)
  networksPerResponsePolicy = _messages.IntegerField(12, variant=_messages.Variant.INT32)
  peeringZonesPerTargetNetwork = _messages.IntegerField(13, variant=_messages.Variant.INT32)
  policies = _messages.IntegerField(14, variant=_messages.Variant.INT32)
  resourceRecordsPerRrset = _messages.IntegerField(15, variant=_messages.Variant.INT32)
  responsePolicies = _messages.IntegerField(16, variant=_messages.Variant.INT32)
  responsePolicyRulesPerResponsePolicy = _messages.IntegerField(17, variant=_messages.Variant.INT32)
  rrsetAdditionsPerChange = _messages.IntegerField(18, variant=_messages.Variant.INT32)
  rrsetDeletionsPerChange = _messages.IntegerField(19, variant=_messages.Variant.INT32)
  rrsetsPerManagedZone = _messages.IntegerField(20, variant=_messages.Variant.INT32)
  targetNameServersPerManagedZone = _messages.IntegerField(21, variant=_messages.Variant.INT32)
  targetNameServersPerPolicy = _messages.IntegerField(22, variant=_messages.Variant.INT32)
  totalRrdataSizePerChange = _messages.IntegerField(23, variant=_messages.Variant.INT32)
  whitelistedKeySpecs = _messages.MessageField('DnsKeySpec', 24, repeated=True)


class RRSetRoutingPolicy(_messages.Message):
  r"""A RRSetRoutingPolicy represents ResourceRecordSet data that is returned
  dynamically with the response varying based on configured properties such as
  geolocation or by weighted random selection.

  Fields:
    geo: A RRSetRoutingPolicyGeoPolicy attribute.
    geoPolicy: A RRSetRoutingPolicyGeoPolicy attribute.
    kind: A string attribute.
    primaryBackup: A RRSetRoutingPolicyPrimaryBackupPolicy attribute.
    wrr: A RRSetRoutingPolicyWrrPolicy attribute.
    wrrPolicy: A RRSetRoutingPolicyWrrPolicy attribute.
  """

  geo = _messages.MessageField('RRSetRoutingPolicyGeoPolicy', 1)
  geoPolicy = _messages.MessageField('RRSetRoutingPolicyGeoPolicy', 2)
  kind = _messages.StringField(3, default='dns#rRSetRoutingPolicy')
  primaryBackup = _messages.MessageField('RRSetRoutingPolicyPrimaryBackupPolicy', 4)
  wrr = _messages.MessageField('RRSetRoutingPolicyWrrPolicy', 5)
  wrrPolicy = _messages.MessageField('RRSetRoutingPolicyWrrPolicy', 6)


class RRSetRoutingPolicyGeoPolicy(_messages.Message):
  r"""Configures a RRSetRoutingPolicy that routes based on the geo location of
  the querying user.

  Fields:
    enableFencing: Without fencing, if health check fails for all configured
      items in the current geo bucket, we'll failover to the next nearest geo
      bucket. With fencing, if health check is enabled, as long as some
      targets in the current geo bucket are healthy, we'll return only the
      healthy targets. However, if they're all unhealthy, we won't failover to
      the next nearest bucket, we'll simply return all the items in the
      current bucket even though they're unhealthy.
    items: The primary geo routing configuration. If there are multiple items
      with the same location, an error is returned instead.
    kind: A string attribute.
  """

  enableFencing = _messages.BooleanField(1)
  items = _messages.MessageField('RRSetRoutingPolicyGeoPolicyGeoPolicyItem', 2, repeated=True)
  kind = _messages.StringField(3, default='dns#rRSetRoutingPolicyGeoPolicy')


class RRSetRoutingPolicyGeoPolicyGeoPolicyItem(_messages.Message):
  r"""ResourceRecordSet data for one geo location.

  Fields:
    healthCheckedTargets: For A and AAAA types only. Endpoints to return in
      the query result only if they are healthy. These can be specified along
      with rrdata within this item.
    kind: A string attribute.
    location: The geo-location granularity is a GCP region. This location
      string should correspond to a GCP region. e.g. "us-east1",
      "southamerica-east1", "asia-east1", etc.
    rrdatas: A string attribute.
    signatureRrdatas: DNSSEC generated signatures for all the rrdata within
      this item. Note that if health checked targets are provided for DNSSEC
      enabled zones, there's a restriction of 1 ip per item. .
  """

  healthCheckedTargets = _messages.MessageField('RRSetRoutingPolicyHealthCheckTargets', 1)
  kind = _messages.StringField(2, default='dns#rRSetRoutingPolicyGeoPolicyGeoPolicyItem')
  location = _messages.StringField(3)
  rrdatas = _messages.StringField(4, repeated=True)
  signatureRrdatas = _messages.StringField(5, repeated=True)


class RRSetRoutingPolicyHealthCheckTargets(_messages.Message):
  r"""HealthCheckTargets describes endpoints to health-check when responding
  to Routing Policy queries. Only the healthy endpoints will be included in
  the response.

  Fields:
    internalLoadBalancers: A RRSetRoutingPolicyLoadBalancerTarget attribute.
  """

  internalLoadBalancers = _messages.MessageField('RRSetRoutingPolicyLoadBalancerTarget', 1, repeated=True)


class RRSetRoutingPolicyLoadBalancerTarget(_messages.Message):
  r"""A RRSetRoutingPolicyLoadBalancerTarget object.

  Enums:
    IpProtocolValueValuesEnum:
    LoadBalancerTypeValueValuesEnum: The type of Load Balancer specified by
      this target. Must match the configuration of the Load Balancer located
      at the LoadBalancerTarget's IP address/port and region.

  Fields:
    ipAddress: The frontend IP address of the Load Balancer to health check.
    ipProtocol: A IpProtocolValueValuesEnum attribute.
    kind: A string attribute.
    loadBalancerType: The type of Load Balancer specified by this target. Must
      match the configuration of the Load Balancer located at the
      LoadBalancerTarget's IP address/port and region.
    networkUrl: The fully qualified url of the network on which the ILB is
      present. This should be formatted like https://www.googleapis.com/comput
      e/v1/projects/{project}/global/networks/{network}
    port: The configured port of the Load Balancer.
    project: The project ID in which the ILB exists.
    region: The region in which the ILB exists.
  """

  class IpProtocolValueValuesEnum(_messages.Enum):
    r"""IpProtocolValueValuesEnum enum type.

    Values:
      undefined: <no description>
      tcp: <no description>
      udp: <no description>
    """
    undefined = 0
    tcp = 1
    udp = 2

  class LoadBalancerTypeValueValuesEnum(_messages.Enum):
    r"""The type of Load Balancer specified by this target. Must match the
    configuration of the Load Balancer located at the LoadBalancerTarget's IP
    address/port and region.

    Values:
      none: <no description>
      globalL7ilb: Global internal Application Load Balancer
      regionalL4ilb: Regional internal passthrough Network Load Balancer
      regionalL7ilb: Regional internal Application Load Balancer
    """
    none = 0
    globalL7ilb = 1
    regionalL4ilb = 2
    regionalL7ilb = 3

  ipAddress = _messages.StringField(1)
  ipProtocol = _messages.EnumField('IpProtocolValueValuesEnum', 2)
  kind = _messages.StringField(3, default='dns#rRSetRoutingPolicyLoadBalancerTarget')
  loadBalancerType = _messages.EnumField('LoadBalancerTypeValueValuesEnum', 4)
  networkUrl = _messages.StringField(5)
  port = _messages.StringField(6)
  project = _messages.StringField(7)
  region = _messages.StringField(8)


class RRSetRoutingPolicyPrimaryBackupPolicy(_messages.Message):
  r"""Configures a RRSetRoutingPolicy such that all queries are responded with
  the primary_targets if they are healthy. And if all of them are unhealthy,
  then we fallback to a geo localized policy.

  Fields:
    backupGeoTargets: Backup targets provide a regional failover policy for
      the otherwise global primary targets. If serving state is set to BACKUP,
      this policy essentially becomes a geo routing policy.
    kind: A string attribute.
    primaryTargets: A RRSetRoutingPolicyHealthCheckTargets attribute.
    trickleTraffic: When serving state is PRIMARY, this field provides the
      option of sending a small percentage of the traffic to the backup
      targets.
  """

  backupGeoTargets = _messages.MessageField('RRSetRoutingPolicyGeoPolicy', 1)
  kind = _messages.StringField(2, default='dns#rRSetRoutingPolicyPrimaryBackupPolicy')
  primaryTargets = _messages.MessageField('RRSetRoutingPolicyHealthCheckTargets', 3)
  trickleTraffic = _messages.FloatField(4)


class RRSetRoutingPolicyWrrPolicy(_messages.Message):
  r"""Configures a RRSetRoutingPolicy that routes in a weighted round robin
  fashion.

  Fields:
    items: A RRSetRoutingPolicyWrrPolicyWrrPolicyItem attribute.
    kind: A string attribute.
  """

  items = _messages.MessageField('RRSetRoutingPolicyWrrPolicyWrrPolicyItem', 1, repeated=True)
  kind = _messages.StringField(2, default='dns#rRSetRoutingPolicyWrrPolicy')


class RRSetRoutingPolicyWrrPolicyWrrPolicyItem(_messages.Message):
  r"""A routing block which contains the routing information for one WRR item.

  Fields:
    healthCheckedTargets: endpoints that need to be health checked before
      making the routing decision. The unhealthy endpoints will be omitted
      from the result. If all endpoints within a buckete are unhealthy, we'll
      choose a different bucket (sampled w.r.t. its weight) for responding.
      Note that if DNSSEC is enabled for this zone, only one of rrdata or
      health_checked_targets can be set.
    kind: A string attribute.
    rrdatas: A string attribute.
    signatureRrdatas: DNSSEC generated signatures for all the rrdata within
      this item. Note that if health checked targets are provided for DNSSEC
      enabled zones, there's a restriction of 1 ip per item. .
    weight: The weight corresponding to this subset of rrdata. When multiple
      WeightedRoundRobinPolicyItems are configured, the probability of
      returning an rrset is proportional to its weight relative to the sum of
      weights configured for all items. This weight should be non-negative.
  """

  healthCheckedTargets = _messages.MessageField('RRSetRoutingPolicyHealthCheckTargets', 1)
  kind = _messages.StringField(2, default='dns#rRSetRoutingPolicyWrrPolicyWrrPolicyItem')
  rrdatas = _messages.StringField(3, repeated=True)
  signatureRrdatas = _messages.StringField(4, repeated=True)
  weight = _messages.FloatField(5)


class ResourceRecordSet(_messages.Message):
  r"""A unit of data that is returned by the DNS servers.

  Fields:
    kind: A string attribute.
    name: For example, www.example.com.
    routingPolicy: Configures dynamic query responses based on geo location of
      querying user or a weighted round robin based routing policy. A
      ResourceRecordSet should only have either rrdata (static) or
      routing_policy (dynamic). An error is returned otherwise.
    rrdatas: As defined in RFC 1035 (section 5) and RFC 1034 (section 3.6.1)
      -- see examples.
    signatureRrdatas: As defined in RFC 4034 (section 3.2).
    ttl: Number of seconds that this ResourceRecordSet can be cached by
      resolvers.
    type: The identifier of a supported record type. See the list of Supported
      DNS record types.
  """

  kind = _messages.StringField(1, default='dns#resourceRecordSet')
  name = _messages.StringField(2)
  routingPolicy = _messages.MessageField('RRSetRoutingPolicy', 3)
  rrdatas = _messages.StringField(4, repeated=True)
  signatureRrdatas = _messages.StringField(5, repeated=True)
  ttl = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  type = _messages.StringField(7)


class ResourceRecordSetsListResponse(_messages.Message):
  r"""A ResourceRecordSetsListResponse object.

  Fields:
    header: A ResponseHeader attribute.
    kind: Type of resource.
    nextPageToken: The presence of this field indicates that there exist more
      results following your last page of results in pagination order. To
      fetch them, make another list request using this value as your
      pagination token. This lets you retrieve complete contents of even
      larger collections, one page at a time. However, if the contents of the
      collection change between the first and last paginated list request, the
      set of elements returned are an inconsistent view of the collection. You
      cannot retrieve a consistent snapshot of a collection larger than the
      maximum page size.
    rrsets: The resource record set resources.
  """

  header = _messages.MessageField('ResponseHeader', 1)
  kind = _messages.StringField(2, default='dns#resourceRecordSetsListResponse')
  nextPageToken = _messages.StringField(3)
  rrsets = _messages.MessageField('ResourceRecordSet', 4, repeated=True)


class ResponseHeader(_messages.Message):
  r"""Elements common to every response.

  Fields:
    operationId: For mutating operation requests that completed successfully.
      This is the client_operation_id if the client specified it, otherwise it
      is generated by the server (output only).
  """

  operationId = _messages.StringField(1)


class ResponsePoliciesListResponse(_messages.Message):
  r"""A ResponsePoliciesListResponse object.

  Fields:
    header: A ResponseHeader attribute.
    nextPageToken: The presence of this field indicates that more results
      exist following your last page of results in pagination order. To fetch
      them, make another list request by using this value as your page token.
      This lets you view the complete contents of even very large collections
      one page at a time. However, if the contents of the collection change
      between the first and last paginated list request, the set of all
      elements returned are an inconsistent view of the collection. You cannot
      retrieve a consistent snapshot of a collection larger than the maximum
      page size.
    responsePolicies: The Response Policy resources.
  """

  header = _messages.MessageField('ResponseHeader', 1)
  nextPageToken = _messages.StringField(2)
  responsePolicies = _messages.MessageField('ResponsePolicy', 3, repeated=True)


class ResponsePoliciesPatchResponse(_messages.Message):
  r"""A ResponsePoliciesPatchResponse object.

  Fields:
    header: A ResponseHeader attribute.
    responsePolicy: A ResponsePolicy attribute.
  """

  header = _messages.MessageField('ResponseHeader', 1)
  responsePolicy = _messages.MessageField('ResponsePolicy', 2)


class ResponsePoliciesUpdateResponse(_messages.Message):
  r"""A ResponsePoliciesUpdateResponse object.

  Fields:
    header: A ResponseHeader attribute.
    responsePolicy: A ResponsePolicy attribute.
  """

  header = _messages.MessageField('ResponseHeader', 1)
  responsePolicy = _messages.MessageField('ResponsePolicy', 2)


class ResponsePolicy(_messages.Message):
  r"""A Response Policy is a collection of selectors that apply to queries
  made against one or more Virtual Private Cloud networks.

  Messages:
    LabelsValue: User labels.

  Fields:
    description: User-provided description for this Response Policy.
    gkeClusters: The list of Google Kubernetes Engine clusters to which this
      response policy is applied.
    id: Unique identifier for the resource; defined by the server (output
      only).
    kind: A string attribute.
    labels: User labels.
    networks: List of network names specifying networks to which this policy
      is applied.
    responsePolicyName: User assigned name for this Response Policy.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  description = _messages.StringField(1)
  gkeClusters = _messages.MessageField('ResponsePolicyGKECluster', 2, repeated=True)
  id = _messages.IntegerField(3)
  kind = _messages.StringField(4, default='dns#responsePolicy')
  labels = _messages.MessageField('LabelsValue', 5)
  networks = _messages.MessageField('ResponsePolicyNetwork', 6, repeated=True)
  responsePolicyName = _messages.StringField(7)


class ResponsePolicyGKECluster(_messages.Message):
  r"""A ResponsePolicyGKECluster object.

  Fields:
    gkeClusterName: The resource name of the cluster to bind this response
      policy to. This should be specified in the format like:
      projects/*/locations/*/clusters/*. This is referenced from GKE
      projects.locations.clusters.get API:
      https://cloud.google.com/kubernetes-
      engine/docs/reference/rest/v1/projects.locations.clusters/get
    kind: A string attribute.
  """

  gkeClusterName = _messages.StringField(1)
  kind = _messages.StringField(2, default='dns#responsePolicyGKECluster')


class ResponsePolicyNetwork(_messages.Message):
  r"""A ResponsePolicyNetwork object.

  Fields:
    kind: A string attribute.
    networkUrl: The fully qualified URL of the VPC network to bind to. This
      should be formatted like https://www.googleapis.com/compute/v1/projects/
      {project}/global/networks/{network}
  """

  kind = _messages.StringField(1, default='dns#responsePolicyNetwork')
  networkUrl = _messages.StringField(2)


class ResponsePolicyRule(_messages.Message):
  r"""A Response Policy Rule is a selector that applies its behavior to
  queries that match the selector. Selectors are DNS names, which may be
  wildcards or exact matches. Each DNS query subject to a Response Policy
  matches at most one ResponsePolicyRule, as identified by the dns_name field
  with the longest matching suffix.

  Enums:
    BehaviorValueValuesEnum: Answer this query with a behavior rather than DNS
      data.

  Fields:
    behavior: Answer this query with a behavior rather than DNS data.
    dnsName: The DNS name (wildcard or exact) to apply this rule to. Must be
      unique within the Response Policy Rule.
    kind: A string attribute.
    localData: Answer this query directly with DNS data. These
      ResourceRecordSets override any other DNS behavior for the matched name;
      in particular they override private zones, the public internet, and GCP
      internal DNS. No SOA nor NS types are allowed.
    ruleName: An identifier for this rule. Must be unique with the
      ResponsePolicy.
  """

  class BehaviorValueValuesEnum(_messages.Enum):
    r"""Answer this query with a behavior rather than DNS data.

    Values:
      behaviorUnspecified: <no description>
      bypassResponsePolicy: Skip a less-specific ResponsePolicyRule and
        continue normal query logic. This can be used with a less-specific
        wildcard selector to exempt a subset of the wildcard
        ResponsePolicyRule from the ResponsePolicy behavior and query the
        public Internet instead. For instance, if these rules exist:
        *.example.com -> LocalData ******* foo.example.com -> Behavior
        'bypassResponsePolicy' Then a query for 'foo.example.com' skips the
        wildcard. This additionally functions to facilitate the allowlist
        feature. RPZs can be applied to multiple levels in the (eventually
        org, folder, project, network) hierarchy. If a rule is applied at a
        higher level of the hierarchy, adding a passthru rule at a lower level
        will supersede that, and a query from an affected vm to that domain
        will be exempt from the RPZ and proceed to normal resolution behavior.
    """
    behaviorUnspecified = 0
    bypassResponsePolicy = 1

  behavior = _messages.EnumField('BehaviorValueValuesEnum', 1)
  dnsName = _messages.StringField(2)
  kind = _messages.StringField(3, default='dns#responsePolicyRule')
  localData = _messages.MessageField('ResponsePolicyRuleLocalData', 4)
  ruleName = _messages.StringField(5)


class ResponsePolicyRuleLocalData(_messages.Message):
  r"""A ResponsePolicyRuleLocalData object.

  Fields:
    localDatas: All resource record sets for this selector, one per resource
      record type. The name must match the dns_name.
  """

  localDatas = _messages.MessageField('ResourceRecordSet', 1, repeated=True)


class ResponsePolicyRulesListResponse(_messages.Message):
  r"""A ResponsePolicyRulesListResponse object.

  Fields:
    header: A ResponseHeader attribute.
    nextPageToken: The presence of this field indicates that there exist more
      results following your last page of results in pagination order. To
      fetch them, make another list request using this value as your page
      token. This lets you the complete contents of even very large
      collections one page at a time. However, if the contents of the
      collection change between the first and last paginated list request, the
      set of all elements returned are an inconsistent view of the collection.
      You cannot retrieve a consistent snapshot of a collection larger than
      the maximum page size.
    responsePolicyRules: The Response Policy Rule resources.
  """

  header = _messages.MessageField('ResponseHeader', 1)
  nextPageToken = _messages.StringField(2)
  responsePolicyRules = _messages.MessageField('ResponsePolicyRule', 3, repeated=True)


class ResponsePolicyRulesPatchResponse(_messages.Message):
  r"""A ResponsePolicyRulesPatchResponse object.

  Fields:
    header: A ResponseHeader attribute.
    responsePolicyRule: A ResponsePolicyRule attribute.
  """

  header = _messages.MessageField('ResponseHeader', 1)
  responsePolicyRule = _messages.MessageField('ResponsePolicyRule', 2)


class ResponsePolicyRulesUpdateResponse(_messages.Message):
  r"""A ResponsePolicyRulesUpdateResponse object.

  Fields:
    header: A ResponseHeader attribute.
    responsePolicyRule: A ResponsePolicyRule attribute.
  """

  header = _messages.MessageField('ResponseHeader', 1)
  responsePolicyRule = _messages.MessageField('ResponsePolicyRule', 2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

"""Generated message classes for policytroubleshooter version v3beta.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'policytroubleshooter'


class GoogleCloudAuditAuthorizationLoggingOptions(_messages.Message):
  r"""Authorization-related information used by Cloud Audit Logging.

  Enums:
    PermissionTypeValueValuesEnum: The type of the permission that was
      checked.

  Fields:
    permissionType: The type of the permission that was checked.
  """

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type of the permission that was checked.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: A read of admin (meta) data.
      ADMIN_WRITE: A write of admin (meta) data.
      DATA_READ: A read of standard data.
      DATA_WRITE: A write of standard data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 1)


class GoogleCloudPolicytroubleshooterIamV3betaAccessTuple(_messages.Message):
  r"""Information about the principal, resource, and permission to check.

  Fields:
    conditionContext: Optional. Additional context for the request, such as
      the request time or IP address. This context allows Policy
      Troubleshooter to troubleshoot conditional role bindings and deny rules.
    fullResourceName: Required. The full resource name that identifies the
      resource. For example, `//compute.googleapis.com/projects/my-
      project/zones/us-central1-a/instances/my-instance`. For examples of full
      resource names for Google Cloud services, see
      https://cloud.google.com/iam/help/troubleshooter/full-resource-names.
    permission: Required. The IAM permission to check for, either in the `v1`
      permission format or the `v2` permission format. For a complete list of
      IAM permissions in the `v1` format, see
      https://cloud.google.com/iam/help/permissions/reference. For a list of
      IAM permissions in the `v2` format, see
      https://cloud.google.com/iam/help/deny/supported-permissions. For a
      complete list of predefined IAM roles and the permissions in each role,
      see https://cloud.google.com/iam/help/roles/reference.
    permissionFqdn: Output only. The permission that Policy Troubleshooter
      checked for, in the `v2` format.
    principal: Required. The email address of the principal whose access you
      want to check. For example, `<EMAIL>` or `my-service-
      <EMAIL>`. The principal must be a
      Google Account or a service account. Other types of principals are not
      supported.
  """

  conditionContext = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaConditionContext', 1)
  fullResourceName = _messages.StringField(2)
  permission = _messages.StringField(3)
  permissionFqdn = _messages.StringField(4)
  principal = _messages.StringField(5)


class GoogleCloudPolicytroubleshooterIamV3betaAllowBindingExplanation(_messages.Message):
  r"""Details about how a role binding in an allow policy affects a
  principal's ability to use a permission.

  Enums:
    AllowAccessStateValueValuesEnum: Required. Indicates whether _this role
      binding_ gives the specified permission to the specified principal on
      the specified resource. This field does _not_ indicate whether the
      principal actually has the permission on the resource. There might be
      another role binding that overrides this role binding. To determine
      whether the principal actually has the permission, use the
      `overall_access_state` field in the TroubleshootIamPolicyResponse.
    RelevanceValueValuesEnum: The relevance of this role binding to the
      overall determination for the entire policy.
    RolePermissionValueValuesEnum: Indicates whether the role granted by this
      role binding contains the specified permission.
    RolePermissionRelevanceValueValuesEnum: The relevance of the permission's
      existence, or nonexistence, in the role to the overall determination for
      the entire policy.

  Messages:
    MembershipsValue: Indicates whether each role binding includes the
      principal specified in the request, either directly or indirectly. Each
      key identifies a principal in the role binding, and each value indicates
      whether the principal in the role binding includes the principal in the
      request. For example, suppose that a role binding includes the following
      principals: * `user:<EMAIL>` * `group:<EMAIL>`
      You want to troubleshoot access for `user:<EMAIL>`. This user is
      a member of the group `group:<EMAIL>`. For the first
      principal in the role binding, the key is `user:<EMAIL>`, and
      the `membership` field in the value is set to `NOT_INCLUDED`. For the
      second principal in the role binding, the key is `group:product-
      <EMAIL>`, and the `membership` field in the value is set to
      `INCLUDED`.

  Fields:
    allowAccessState: Required. Indicates whether _this role binding_ gives
      the specified permission to the specified principal on the specified
      resource. This field does _not_ indicate whether the principal actually
      has the permission on the resource. There might be another role binding
      that overrides this role binding. To determine whether the principal
      actually has the permission, use the `overall_access_state` field in the
      TroubleshootIamPolicyResponse.
    combinedMembership: The combined result of all memberships. Indicates if
      the principal is included in any role binding, either directly or
      indirectly.
    condition: A condition expression that specifies when the role binding
      grants access. To learn about IAM Conditions, see
      https://cloud.google.com/iam/help/conditions/overview.
    conditionExplanation: Condition evaluation state for this role binding.
    memberships: Indicates whether each role binding includes the principal
      specified in the request, either directly or indirectly. Each key
      identifies a principal in the role binding, and each value indicates
      whether the principal in the role binding includes the principal in the
      request. For example, suppose that a role binding includes the following
      principals: * `user:<EMAIL>` * `group:<EMAIL>`
      You want to troubleshoot access for `user:<EMAIL>`. This user is
      a member of the group `group:<EMAIL>`. For the first
      principal in the role binding, the key is `user:<EMAIL>`, and
      the `membership` field in the value is set to `NOT_INCLUDED`. For the
      second principal in the role binding, the key is `group:product-
      <EMAIL>`, and the `membership` field in the value is set to
      `INCLUDED`.
    relevance: The relevance of this role binding to the overall determination
      for the entire policy.
    role: The role that this role binding grants. For example,
      `roles/compute.admin`. For a complete list of predefined IAM roles, as
      well as the permissions in each role, see
      https://cloud.google.com/iam/help/roles/reference.
    rolePermission: Indicates whether the role granted by this role binding
      contains the specified permission.
    rolePermissionRelevance: The relevance of the permission's existence, or
      nonexistence, in the role to the overall determination for the entire
      policy.
  """

  class AllowAccessStateValueValuesEnum(_messages.Enum):
    r"""Required. Indicates whether _this role binding_ gives the specified
    permission to the specified principal on the specified resource. This
    field does _not_ indicate whether the principal actually has the
    permission on the resource. There might be another role binding that
    overrides this role binding. To determine whether the principal actually
    has the permission, use the `overall_access_state` field in the
    TroubleshootIamPolicyResponse.

    Values:
      ALLOW_ACCESS_STATE_UNSPECIFIED: Not specified.
      ALLOW_ACCESS_STATE_GRANTED: The allow policy gives the principal the
        permission.
      ALLOW_ACCESS_STATE_NOT_GRANTED: The allow policy doesn't give the
        principal the permission.
      ALLOW_ACCESS_STATE_UNKNOWN_CONDITIONAL: The allow policy gives the
        principal the permission if a condition expression evaluate to `true`.
        However, the sender of the request didn't provide enough context for
        Policy Troubleshooter to evaluate the condition expression.
      ALLOW_ACCESS_STATE_UNKNOWN_INFO: The sender of the request doesn't have
        access to all of the allow policies that Policy Troubleshooter needs
        to evaluate the principal's access.
    """
    ALLOW_ACCESS_STATE_UNSPECIFIED = 0
    ALLOW_ACCESS_STATE_GRANTED = 1
    ALLOW_ACCESS_STATE_NOT_GRANTED = 2
    ALLOW_ACCESS_STATE_UNKNOWN_CONDITIONAL = 3
    ALLOW_ACCESS_STATE_UNKNOWN_INFO = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of this role binding to the overall determination for
    the entire policy.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  class RolePermissionRelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of the permission's existence, or nonexistence, in the
    role to the overall determination for the entire policy.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  class RolePermissionValueValuesEnum(_messages.Enum):
    r"""Indicates whether the role granted by this role binding contains the
    specified permission.

    Values:
      ROLE_PERMISSION_INCLUSION_STATE_UNSPECIFIED: Not specified.
      ROLE_PERMISSION_INCLUDED: The permission is included in the role.
      ROLE_PERMISSION_NOT_INCLUDED: The permission is not included in the
        role.
      ROLE_PERMISSION_UNKNOWN_INFO: The sender of the request is not allowed
        to access the role definition.
    """
    ROLE_PERMISSION_INCLUSION_STATE_UNSPECIFIED = 0
    ROLE_PERMISSION_INCLUDED = 1
    ROLE_PERMISSION_NOT_INCLUDED = 2
    ROLE_PERMISSION_UNKNOWN_INFO = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MembershipsValue(_messages.Message):
    r"""Indicates whether each role binding includes the principal specified
    in the request, either directly or indirectly. Each key identifies a
    principal in the role binding, and each value indicates whether the
    principal in the role binding includes the principal in the request. For
    example, suppose that a role binding includes the following principals: *
    `user:<EMAIL>` * `group:<EMAIL>` You want to
    troubleshoot access for `user:<EMAIL>`. This user is a member of
    the group `group:<EMAIL>`. For the first principal in the
    role binding, the key is `user:<EMAIL>`, and the `membership`
    field in the value is set to `NOT_INCLUDED`. For the second principal in
    the role binding, the key is `group:<EMAIL>`, and the
    `membership` field in the value is set to `INCLUDED`.

    Messages:
      AdditionalProperty: An additional property for a MembershipsValue
        object.

    Fields:
      additionalProperties: Additional properties of type MembershipsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MembershipsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudPolicytroubleshooterIamV3betaAllowBindingExplanati
          onAnnotatedAllowMembership attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaAllowBindingExplanationAnnotatedAllowMembership', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allowAccessState = _messages.EnumField('AllowAccessStateValueValuesEnum', 1)
  combinedMembership = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaAllowBindingExplanationAnnotatedAllowMembership', 2)
  condition = _messages.MessageField('GoogleTypeExpr', 3)
  conditionExplanation = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaConditionExplanation', 4)
  memberships = _messages.MessageField('MembershipsValue', 5)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 6)
  role = _messages.StringField(7)
  rolePermission = _messages.EnumField('RolePermissionValueValuesEnum', 8)
  rolePermissionRelevance = _messages.EnumField('RolePermissionRelevanceValueValuesEnum', 9)


class GoogleCloudPolicytroubleshooterIamV3betaAllowBindingExplanationAnnotatedAllowMembership(_messages.Message):
  r"""Details about whether the role binding includes the principal.

  Enums:
    MembershipValueValuesEnum: Indicates whether the role binding includes the
      principal.
    RelevanceValueValuesEnum: The relevance of the principal's status to the
      overall determination for the role binding.

  Fields:
    membership: Indicates whether the role binding includes the principal.
    relevance: The relevance of the principal's status to the overall
      determination for the role binding.
  """

  class MembershipValueValuesEnum(_messages.Enum):
    r"""Indicates whether the role binding includes the principal.

    Values:
      MEMBERSHIP_MATCHING_STATE_UNSPECIFIED: Not specified.
      MEMBERSHIP_MATCHED: The principal in the request matches the principal
        in the policy. The principal can be included directly or indirectly: *
        A principal is included directly if that principal is listed in the
        role binding. * A principal is included indirectly if that principal
        is in a Google group, Google Workspace account, or Cloud Identity
        domain that is listed in the policy.
      MEMBERSHIP_NOT_MATCHED: The principal in the request doesn't match the
        principal in the policy.
      MEMBERSHIP_UNKNOWN_INFO: The principal in the policy is a group or
        domain, and the sender of the request doesn't have permission to view
        whether the principal in the request is a member of the group or
        domain.
      MEMBERSHIP_UNKNOWN_UNSUPPORTED: The principal is an unsupported type.
    """
    MEMBERSHIP_MATCHING_STATE_UNSPECIFIED = 0
    MEMBERSHIP_MATCHED = 1
    MEMBERSHIP_NOT_MATCHED = 2
    MEMBERSHIP_UNKNOWN_INFO = 3
    MEMBERSHIP_UNKNOWN_UNSUPPORTED = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of the principal's status to the overall determination
    for the role binding.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  membership = _messages.EnumField('MembershipValueValuesEnum', 1)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 2)


class GoogleCloudPolicytroubleshooterIamV3betaAllowPolicyExplanation(_messages.Message):
  r"""Details about how the relevant IAM allow policies affect the final
  access state.

  Enums:
    AllowAccessStateValueValuesEnum: Indicates whether the principal has the
      specified permission for the specified resource, based on evaluating all
      applicable IAM allow policies.
    RelevanceValueValuesEnum: The relevance of the allow policy type to the
      overall access state.

  Fields:
    allowAccessState: Indicates whether the principal has the specified
      permission for the specified resource, based on evaluating all
      applicable IAM allow policies.
    explainedPolicies: List of IAM allow policies that were evaluated to check
      the principal's permissions, with annotations to indicate how each
      policy contributed to the final result. The list of policies includes
      the policy for the resource itself, as well as allow policies that are
      inherited from higher levels of the resource hierarchy, including the
      organization, the folder, and the project. To learn more about the
      resource hierarchy, see https://cloud.google.com/iam/help/resource-
      hierarchy.
    relevance: The relevance of the allow policy type to the overall access
      state.
  """

  class AllowAccessStateValueValuesEnum(_messages.Enum):
    r"""Indicates whether the principal has the specified permission for the
    specified resource, based on evaluating all applicable IAM allow policies.

    Values:
      ALLOW_ACCESS_STATE_UNSPECIFIED: Not specified.
      ALLOW_ACCESS_STATE_GRANTED: The allow policy gives the principal the
        permission.
      ALLOW_ACCESS_STATE_NOT_GRANTED: The allow policy doesn't give the
        principal the permission.
      ALLOW_ACCESS_STATE_UNKNOWN_CONDITIONAL: The allow policy gives the
        principal the permission if a condition expression evaluate to `true`.
        However, the sender of the request didn't provide enough context for
        Policy Troubleshooter to evaluate the condition expression.
      ALLOW_ACCESS_STATE_UNKNOWN_INFO: The sender of the request doesn't have
        access to all of the allow policies that Policy Troubleshooter needs
        to evaluate the principal's access.
    """
    ALLOW_ACCESS_STATE_UNSPECIFIED = 0
    ALLOW_ACCESS_STATE_GRANTED = 1
    ALLOW_ACCESS_STATE_NOT_GRANTED = 2
    ALLOW_ACCESS_STATE_UNKNOWN_CONDITIONAL = 3
    ALLOW_ACCESS_STATE_UNKNOWN_INFO = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of the allow policy type to the overall access state.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  allowAccessState = _messages.EnumField('AllowAccessStateValueValuesEnum', 1)
  explainedPolicies = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaExplainedAllowPolicy', 2, repeated=True)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 3)


class GoogleCloudPolicytroubleshooterIamV3betaConditionContext(_messages.Message):
  r"""Additional context for troubleshooting conditional role bindings and
  deny rules.

  Fields:
    destination: The destination of a network activity, such as accepting a
      TCP connection. In a multi-hop network activity, the destination
      represents the receiver of the last hop.
    effectiveTags: Output only. The effective tags on the resource. The
      effective tags are fetched during troubleshooting.
    request: Represents a network request, such as an HTTP request.
    resource: Represents a target resource that is involved with a network
      activity. If multiple resources are involved with an activity, this must
      be the primary one.
  """

  destination = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaConditionContextPeer', 1)
  effectiveTags = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaConditionContextEffectiveTag', 2, repeated=True)
  request = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaConditionContextRequest', 3)
  resource = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaConditionContextResource', 4)


class GoogleCloudPolicytroubleshooterIamV3betaConditionContextEffectiveTag(_messages.Message):
  r"""A tag that applies to a resource during policy evaluation. Tags can be
  either directly bound to a resource or inherited from its ancestor.
  `EffectiveTag` contains the `name` and `namespaced_name` of the tag value
  and tag key, with additional fields of `inherited` to indicate the
  inheritance status of the effective tag.

  Fields:
    inherited: Output only. Indicates the inheritance status of a tag value
      attached to the given resource. If the tag value is inherited from one
      of the resource's ancestors, inherited will be true. If false, then the
      tag value is directly attached to the resource, inherited will be false.
    namespacedTagKey: Output only. The namespaced name of the TagKey. Can be
      in the form `{organization_id}/{tag_key_short_name}` or
      `{project_id}/{tag_key_short_name}` or
      `{project_number}/{tag_key_short_name}`.
    namespacedTagValue: Output only. The namespaced name of the TagValue. Can
      be in the form
      `{organization_id}/{tag_key_short_name}/{tag_value_short_name}` or
      `{project_id}/{tag_key_short_name}/{tag_value_short_name}` or
      `{project_number}/{tag_key_short_name}/{tag_value_short_name}`.
    tagKey: Output only. The name of the TagKey, in the format `tagKeys/{id}`,
      such as `tagKeys/123`.
    tagKeyParentName: The parent name of the tag key. Must be in the format
      `organizations/{organization_id}` or `projects/{project_number}`
    tagValue: Output only. Resource name for TagValue in the format
      `tagValues/456`.
  """

  inherited = _messages.BooleanField(1)
  namespacedTagKey = _messages.StringField(2)
  namespacedTagValue = _messages.StringField(3)
  tagKey = _messages.StringField(4)
  tagKeyParentName = _messages.StringField(5)
  tagValue = _messages.StringField(6)


class GoogleCloudPolicytroubleshooterIamV3betaConditionContextPeer(_messages.Message):
  r"""This message defines attributes for a node that handles a network
  request. The node can be either a service or an application that sends,
  forwards, or receives the request. Service peers should fill in `principal`
  and `labels` as appropriate.

  Fields:
    ip: The IPv4 or IPv6 address of the peer.
    port: The network port of the peer.
  """

  ip = _messages.StringField(1)
  port = _messages.IntegerField(2)


class GoogleCloudPolicytroubleshooterIamV3betaConditionContextRequest(_messages.Message):
  r"""This message defines attributes for an HTTP request. If the actual
  request is not an HTTP request, the runtime system should try to map the
  actual request to an equivalent HTTP request.

  Fields:
    receiveTime: Optional. The timestamp when the destination service receives
      the first byte of the request.
    satisfiedAccessLevels: Optional. The information for access levels that
      are satisfied for the given access tuple.
    unsatisfiedAccessLevels: Optional. The information for access levels that
      are unsatisfied for the given access tuple.
  """

  receiveTime = _messages.StringField(1)
  satisfiedAccessLevels = _messages.StringField(2, repeated=True)
  unsatisfiedAccessLevels = _messages.StringField(3, repeated=True)


class GoogleCloudPolicytroubleshooterIamV3betaConditionContextResource(_messages.Message):
  r"""Core attributes for a resource. A resource is an addressable (named)
  entity provided by the destination service. For example, a Compute Engine
  instance.

  Fields:
    name: The stable identifier (name) of a resource on the `service`. A
      resource can be logically identified as
      `//{resource.service}/{resource.name}`. Unlike the resource URI, the
      resource name doesn't contain any protocol and version information. For
      a list of full resource name formats, see
      https://cloud.google.com/iam/help/troubleshooter/full-resource-names
    service: The name of the service that this resource belongs to, such as
      `compute.googleapis.com`. The service name might not match the DNS
      hostname that actually serves the request. For a full list of resource
      service values, see
      https://cloud.google.com/iam/help/conditions/resource-services
    type: The type of the resource, in the format `{service}/{kind}`. For a
      full list of resource type values, see
      https://cloud.google.com/iam/help/conditions/resource-types
  """

  name = _messages.StringField(1)
  service = _messages.StringField(2)
  type = _messages.StringField(3)


class GoogleCloudPolicytroubleshooterIamV3betaConditionExplanation(_messages.Message):
  r"""Explanation for how a condition affects a principal's access

  Fields:
    errors: Any errors that prevented complete evaluation of the condition
      expression.
    evaluationStates: The value of each statement of the condition expression.
      The value can be `true`, `false`, or `null`. The value is `null` if the
      statement can't be evaluated.
    value: Value of the condition.
  """

  errors = _messages.MessageField('GoogleRpcStatus', 1, repeated=True)
  evaluationStates = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaConditionExplanationEvaluationState', 2, repeated=True)
  value = _messages.MessageField('extra_types.JsonValue', 3)


class GoogleCloudPolicytroubleshooterIamV3betaConditionExplanationEvaluationState(_messages.Message):
  r"""Evaluated state of a condition expression.

  Fields:
    end: End position of an expression in the condition, by character, end
      included, for example: the end position of the first part of `a==b ||
      c==d` would be 4.
    errors: Any errors that prevented complete evaluation of the condition
      expression.
    start: Start position of an expression in the condition, by character.
    value: Value of this expression.
  """

  end = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  errors = _messages.MessageField('GoogleRpcStatus', 2, repeated=True)
  start = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  value = _messages.MessageField('extra_types.JsonValue', 4)


class GoogleCloudPolicytroubleshooterIamV3betaDenyPolicyExplanation(_messages.Message):
  r"""Details about how the relevant IAM deny policies affect the final access
  state.

  Enums:
    DenyAccessStateValueValuesEnum: Indicates whether the principal is denied
      the specified permission for the specified resource, based on evaluating
      all applicable IAM deny policies.
    RelevanceValueValuesEnum: The relevance of the deny policy result to the
      overall access state.

  Fields:
    denyAccessState: Indicates whether the principal is denied the specified
      permission for the specified resource, based on evaluating all
      applicable IAM deny policies.
    explainedResources: List of resources with IAM deny policies that were
      evaluated to check the principal's denied permissions, with annotations
      to indicate how each policy contributed to the final result. The list of
      resources includes the policy for the resource itself, as well as
      policies that are inherited from higher levels of the resource
      hierarchy, including the organization, the folder, and the project. The
      order of the resources starts from the resource and climbs up the
      resource hierarchy. To learn more about the resource hierarchy, see
      https://cloud.google.com/iam/help/resource-hierarchy.
    permissionDeniable: Indicates whether the permission to troubleshoot is
      supported in deny policies.
    relevance: The relevance of the deny policy result to the overall access
      state.
  """

  class DenyAccessStateValueValuesEnum(_messages.Enum):
    r"""Indicates whether the principal is denied the specified permission for
    the specified resource, based on evaluating all applicable IAM deny
    policies.

    Values:
      DENY_ACCESS_STATE_UNSPECIFIED: Not specified.
      DENY_ACCESS_STATE_DENIED: The deny policy denies the principal the
        permission.
      DENY_ACCESS_STATE_NOT_DENIED: The deny policy doesn't deny the principal
        the permission.
      DENY_ACCESS_STATE_UNKNOWN_CONDITIONAL: The deny policy denies the
        principal the permission if a condition expression evaluates to
        `true`. However, the sender of the request didn't provide enough
        context for Policy Troubleshooter to evaluate the condition
        expression.
      DENY_ACCESS_STATE_UNKNOWN_INFO: The sender of the request does not have
        access to all of the deny policies that Policy Troubleshooter needs to
        evaluate the principal's access.
    """
    DENY_ACCESS_STATE_UNSPECIFIED = 0
    DENY_ACCESS_STATE_DENIED = 1
    DENY_ACCESS_STATE_NOT_DENIED = 2
    DENY_ACCESS_STATE_UNKNOWN_CONDITIONAL = 3
    DENY_ACCESS_STATE_UNKNOWN_INFO = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of the deny policy result to the overall access state.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  denyAccessState = _messages.EnumField('DenyAccessStateValueValuesEnum', 1)
  explainedResources = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaExplainedDenyResource', 2, repeated=True)
  permissionDeniable = _messages.BooleanField(3)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 4)


class GoogleCloudPolicytroubleshooterIamV3betaDenyRuleExplanation(_messages.Message):
  r"""Details about how a deny rule in a deny policy affects a principal's
  ability to use a permission.

  Enums:
    DenyAccessStateValueValuesEnum: Required. Indicates whether _this rule_
      denies the specified permission to the specified principal for the
      specified resource. This field does _not_ indicate whether the principal
      is actually denied on the permission for the resource. There might be
      another rule that overrides this rule. To determine whether the
      principal actually has the permission, use the `overall_access_state`
      field in the TroubleshootIamPolicyResponse.
    RelevanceValueValuesEnum: The relevance of this role binding to the
      overall determination for the entire policy.

  Messages:
    DeniedPermissionsValue: Lists all denied permissions in the deny rule and
      indicates whether each permission matches the permission in the request.
      Each key identifies a denied permission in the rule, and each value
      indicates whether the denied permission matches the permission in the
      request.
    DeniedPrincipalsValue: Lists all denied principals in the deny rule and
      indicates whether each principal matches the principal in the request,
      either directly or through membership in a principal set. Each key
      identifies a denied principal in the rule, and each value indicates
      whether the denied principal matches the principal in the request.
    ExceptionPermissionsValue: Lists all exception permissions in the deny
      rule and indicates whether each permission matches the permission in the
      request. Each key identifies a exception permission in the rule, and
      each value indicates whether the exception permission matches the
      permission in the request.
    ExceptionPrincipalsValue: Lists all exception principals in the deny rule
      and indicates whether each principal matches the principal in the
      request, either directly or through membership in a principal set. Each
      key identifies a exception principal in the rule, and each value
      indicates whether the exception principal matches the principal in the
      request.

  Fields:
    combinedDeniedPermission: Indicates whether the permission in the request
      is listed as a denied permission in the deny rule.
    combinedDeniedPrincipal: Indicates whether the principal is listed as a
      denied principal in the deny rule, either directly or through membership
      in a principal set.
    combinedExceptionPermission: Indicates whether the permission in the
      request is listed as an exception permission in the deny rule.
    combinedExceptionPrincipal: Indicates whether the principal is listed as
      an exception principal in the deny rule, either directly or through
      membership in a principal set.
    condition: A condition expression that specifies when the deny rule denies
      the principal access. To learn about IAM Conditions, see
      https://cloud.google.com/iam/help/conditions/overview.
    conditionExplanation: Condition evaluation state for this role binding.
    deniedPermissions: Lists all denied permissions in the deny rule and
      indicates whether each permission matches the permission in the request.
      Each key identifies a denied permission in the rule, and each value
      indicates whether the denied permission matches the permission in the
      request.
    deniedPrincipals: Lists all denied principals in the deny rule and
      indicates whether each principal matches the principal in the request,
      either directly or through membership in a principal set. Each key
      identifies a denied principal in the rule, and each value indicates
      whether the denied principal matches the principal in the request.
    denyAccessState: Required. Indicates whether _this rule_ denies the
      specified permission to the specified principal for the specified
      resource. This field does _not_ indicate whether the principal is
      actually denied on the permission for the resource. There might be
      another rule that overrides this rule. To determine whether the
      principal actually has the permission, use the `overall_access_state`
      field in the TroubleshootIamPolicyResponse.
    exceptionPermissions: Lists all exception permissions in the deny rule and
      indicates whether each permission matches the permission in the request.
      Each key identifies a exception permission in the rule, and each value
      indicates whether the exception permission matches the permission in the
      request.
    exceptionPrincipals: Lists all exception principals in the deny rule and
      indicates whether each principal matches the principal in the request,
      either directly or through membership in a principal set. Each key
      identifies a exception principal in the rule, and each value indicates
      whether the exception principal matches the principal in the request.
    relevance: The relevance of this role binding to the overall determination
      for the entire policy.
  """

  class DenyAccessStateValueValuesEnum(_messages.Enum):
    r"""Required. Indicates whether _this rule_ denies the specified
    permission to the specified principal for the specified resource. This
    field does _not_ indicate whether the principal is actually denied on the
    permission for the resource. There might be another rule that overrides
    this rule. To determine whether the principal actually has the permission,
    use the `overall_access_state` field in the TroubleshootIamPolicyResponse.

    Values:
      DENY_ACCESS_STATE_UNSPECIFIED: Not specified.
      DENY_ACCESS_STATE_DENIED: The deny policy denies the principal the
        permission.
      DENY_ACCESS_STATE_NOT_DENIED: The deny policy doesn't deny the principal
        the permission.
      DENY_ACCESS_STATE_UNKNOWN_CONDITIONAL: The deny policy denies the
        principal the permission if a condition expression evaluates to
        `true`. However, the sender of the request didn't provide enough
        context for Policy Troubleshooter to evaluate the condition
        expression.
      DENY_ACCESS_STATE_UNKNOWN_INFO: The sender of the request does not have
        access to all of the deny policies that Policy Troubleshooter needs to
        evaluate the principal's access.
    """
    DENY_ACCESS_STATE_UNSPECIFIED = 0
    DENY_ACCESS_STATE_DENIED = 1
    DENY_ACCESS_STATE_NOT_DENIED = 2
    DENY_ACCESS_STATE_UNKNOWN_CONDITIONAL = 3
    DENY_ACCESS_STATE_UNKNOWN_INFO = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of this role binding to the overall determination for
    the entire policy.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DeniedPermissionsValue(_messages.Message):
    r"""Lists all denied permissions in the deny rule and indicates whether
    each permission matches the permission in the request. Each key identifies
    a denied permission in the rule, and each value indicates whether the
    denied permission matches the permission in the request.

    Messages:
      AdditionalProperty: An additional property for a DeniedPermissionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        DeniedPermissionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DeniedPermissionsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudPolicytroubleshooterIamV3betaDenyRuleExplanationAn
          notatedPermissionMatching attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaDenyRuleExplanationAnnotatedPermissionMatching', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DeniedPrincipalsValue(_messages.Message):
    r"""Lists all denied principals in the deny rule and indicates whether
    each principal matches the principal in the request, either directly or
    through membership in a principal set. Each key identifies a denied
    principal in the rule, and each value indicates whether the denied
    principal matches the principal in the request.

    Messages:
      AdditionalProperty: An additional property for a DeniedPrincipalsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        DeniedPrincipalsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DeniedPrincipalsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudPolicytroubleshooterIamV3betaDenyRuleExplanationAn
          notatedDenyPrincipalMatching attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaDenyRuleExplanationAnnotatedDenyPrincipalMatching', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExceptionPermissionsValue(_messages.Message):
    r"""Lists all exception permissions in the deny rule and indicates whether
    each permission matches the permission in the request. Each key identifies
    a exception permission in the rule, and each value indicates whether the
    exception permission matches the permission in the request.

    Messages:
      AdditionalProperty: An additional property for a
        ExceptionPermissionsValue object.

    Fields:
      additionalProperties: Additional properties of type
        ExceptionPermissionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExceptionPermissionsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudPolicytroubleshooterIamV3betaDenyRuleExplanationAn
          notatedPermissionMatching attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaDenyRuleExplanationAnnotatedPermissionMatching', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExceptionPrincipalsValue(_messages.Message):
    r"""Lists all exception principals in the deny rule and indicates whether
    each principal matches the principal in the request, either directly or
    through membership in a principal set. Each key identifies a exception
    principal in the rule, and each value indicates whether the exception
    principal matches the principal in the request.

    Messages:
      AdditionalProperty: An additional property for a
        ExceptionPrincipalsValue object.

    Fields:
      additionalProperties: Additional properties of type
        ExceptionPrincipalsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExceptionPrincipalsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudPolicytroubleshooterIamV3betaDenyRuleExplanationAn
          notatedDenyPrincipalMatching attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaDenyRuleExplanationAnnotatedDenyPrincipalMatching', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  combinedDeniedPermission = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaDenyRuleExplanationAnnotatedPermissionMatching', 1)
  combinedDeniedPrincipal = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaDenyRuleExplanationAnnotatedDenyPrincipalMatching', 2)
  combinedExceptionPermission = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaDenyRuleExplanationAnnotatedPermissionMatching', 3)
  combinedExceptionPrincipal = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaDenyRuleExplanationAnnotatedDenyPrincipalMatching', 4)
  condition = _messages.MessageField('GoogleTypeExpr', 5)
  conditionExplanation = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaConditionExplanation', 6)
  deniedPermissions = _messages.MessageField('DeniedPermissionsValue', 7)
  deniedPrincipals = _messages.MessageField('DeniedPrincipalsValue', 8)
  denyAccessState = _messages.EnumField('DenyAccessStateValueValuesEnum', 9)
  exceptionPermissions = _messages.MessageField('ExceptionPermissionsValue', 10)
  exceptionPrincipals = _messages.MessageField('ExceptionPrincipalsValue', 11)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 12)


class GoogleCloudPolicytroubleshooterIamV3betaDenyRuleExplanationAnnotatedDenyPrincipalMatching(_messages.Message):
  r"""Details about whether the principal in the request is listed as a denied
  principal in the deny rule, either directly or through membership in a
  principal set.

  Enums:
    MembershipValueValuesEnum: Indicates whether the principal is listed as a
      denied principal in the deny rule, either directly or through membership
      in a principal set.
    RelevanceValueValuesEnum: The relevance of the principal's status to the
      overall determination for the role binding.

  Fields:
    membership: Indicates whether the principal is listed as a denied
      principal in the deny rule, either directly or through membership in a
      principal set.
    relevance: The relevance of the principal's status to the overall
      determination for the role binding.
  """

  class MembershipValueValuesEnum(_messages.Enum):
    r"""Indicates whether the principal is listed as a denied principal in the
    deny rule, either directly or through membership in a principal set.

    Values:
      MEMBERSHIP_MATCHING_STATE_UNSPECIFIED: Not specified.
      MEMBERSHIP_MATCHED: The principal in the request matches the principal
        in the policy. The principal can be included directly or indirectly: *
        A principal is included directly if that principal is listed in the
        role binding. * A principal is included indirectly if that principal
        is in a Google group, Google Workspace account, or Cloud Identity
        domain that is listed in the policy.
      MEMBERSHIP_NOT_MATCHED: The principal in the request doesn't match the
        principal in the policy.
      MEMBERSHIP_UNKNOWN_INFO: The principal in the policy is a group or
        domain, and the sender of the request doesn't have permission to view
        whether the principal in the request is a member of the group or
        domain.
      MEMBERSHIP_UNKNOWN_UNSUPPORTED: The principal is an unsupported type.
    """
    MEMBERSHIP_MATCHING_STATE_UNSPECIFIED = 0
    MEMBERSHIP_MATCHED = 1
    MEMBERSHIP_NOT_MATCHED = 2
    MEMBERSHIP_UNKNOWN_INFO = 3
    MEMBERSHIP_UNKNOWN_UNSUPPORTED = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of the principal's status to the overall determination
    for the role binding.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  membership = _messages.EnumField('MembershipValueValuesEnum', 1)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 2)


class GoogleCloudPolicytroubleshooterIamV3betaDenyRuleExplanationAnnotatedPermissionMatching(_messages.Message):
  r"""Details about whether the permission in the request is denied by the
  deny rule.

  Enums:
    PermissionMatchingStateValueValuesEnum: Indicates whether the permission
      in the request is denied by the deny rule.
    RelevanceValueValuesEnum: The relevance of the permission status to the
      overall determination for the rule.

  Fields:
    permissionMatchingState: Indicates whether the permission in the request
      is denied by the deny rule.
    relevance: The relevance of the permission status to the overall
      determination for the rule.
  """

  class PermissionMatchingStateValueValuesEnum(_messages.Enum):
    r"""Indicates whether the permission in the request is denied by the deny
    rule.

    Values:
      PERMISSION_PATTERN_MATCHING_STATE_UNSPECIFIED: Not specified.
      PERMISSION_PATTERN_MATCHED: The permission in the request matches the
        permission in the policy.
      PERMISSION_PATTERN_NOT_MATCHED: The permission in the request matches
        the permission in the policy.
    """
    PERMISSION_PATTERN_MATCHING_STATE_UNSPECIFIED = 0
    PERMISSION_PATTERN_MATCHED = 1
    PERMISSION_PATTERN_NOT_MATCHED = 2

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of the permission status to the overall determination
    for the rule.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  permissionMatchingState = _messages.EnumField('PermissionMatchingStateValueValuesEnum', 1)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 2)


class GoogleCloudPolicytroubleshooterIamV3betaExplainedAllowPolicy(_messages.Message):
  r"""Details about how a specific IAM allow policy contributed to the final
  access state.

  Enums:
    AllowAccessStateValueValuesEnum: Required. Indicates whether _this policy_
      provides the specified permission to the specified principal for the
      specified resource. This field does _not_ indicate whether the principal
      actually has the permission for the resource. There might be another
      policy that overrides this policy. To determine whether the principal
      actually has the permission, use the `overall_access_state` field in the
      TroubleshootIamPolicyResponse.
    RelevanceValueValuesEnum: The relevance of this policy to the overall
      access state in the TroubleshootIamPolicyResponse. If the sender of the
      request does not have access to the policy, this field is omitted.

  Fields:
    allowAccessState: Required. Indicates whether _this policy_ provides the
      specified permission to the specified principal for the specified
      resource. This field does _not_ indicate whether the principal actually
      has the permission for the resource. There might be another policy that
      overrides this policy. To determine whether the principal actually has
      the permission, use the `overall_access_state` field in the
      TroubleshootIamPolicyResponse.
    bindingExplanations: Details about how each role binding in the policy
      affects the principal's ability, or inability, to use the permission for
      the resource. The order of the role bindings matches the role binding
      order in the policy. If the sender of the request does not have access
      to the policy, this field is omitted.
    fullResourceName: The full resource name that identifies the resource. For
      example, `//compute.googleapis.com/projects/my-project/zones/us-
      central1-a/instances/my-instance`. If the sender of the request does not
      have access to the policy, this field is omitted. For examples of full
      resource names for Google Cloud services, see
      https://cloud.google.com/iam/help/troubleshooter/full-resource-names.
    policy: The IAM allow policy attached to the resource. If the sender of
      the request does not have access to the policy, this field is empty.
    relevance: The relevance of this policy to the overall access state in the
      TroubleshootIamPolicyResponse. If the sender of the request does not
      have access to the policy, this field is omitted.
  """

  class AllowAccessStateValueValuesEnum(_messages.Enum):
    r"""Required. Indicates whether _this policy_ provides the specified
    permission to the specified principal for the specified resource. This
    field does _not_ indicate whether the principal actually has the
    permission for the resource. There might be another policy that overrides
    this policy. To determine whether the principal actually has the
    permission, use the `overall_access_state` field in the
    TroubleshootIamPolicyResponse.

    Values:
      ALLOW_ACCESS_STATE_UNSPECIFIED: Not specified.
      ALLOW_ACCESS_STATE_GRANTED: The allow policy gives the principal the
        permission.
      ALLOW_ACCESS_STATE_NOT_GRANTED: The allow policy doesn't give the
        principal the permission.
      ALLOW_ACCESS_STATE_UNKNOWN_CONDITIONAL: The allow policy gives the
        principal the permission if a condition expression evaluate to `true`.
        However, the sender of the request didn't provide enough context for
        Policy Troubleshooter to evaluate the condition expression.
      ALLOW_ACCESS_STATE_UNKNOWN_INFO: The sender of the request doesn't have
        access to all of the allow policies that Policy Troubleshooter needs
        to evaluate the principal's access.
    """
    ALLOW_ACCESS_STATE_UNSPECIFIED = 0
    ALLOW_ACCESS_STATE_GRANTED = 1
    ALLOW_ACCESS_STATE_NOT_GRANTED = 2
    ALLOW_ACCESS_STATE_UNKNOWN_CONDITIONAL = 3
    ALLOW_ACCESS_STATE_UNKNOWN_INFO = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of this policy to the overall access state in the
    TroubleshootIamPolicyResponse. If the sender of the request does not have
    access to the policy, this field is omitted.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  allowAccessState = _messages.EnumField('AllowAccessStateValueValuesEnum', 1)
  bindingExplanations = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaAllowBindingExplanation', 2, repeated=True)
  fullResourceName = _messages.StringField(3)
  policy = _messages.MessageField('GoogleIamV1Policy', 4)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 5)


class GoogleCloudPolicytroubleshooterIamV3betaExplainedDenyPolicy(_messages.Message):
  r"""Details about how a specific IAM deny policy Policy contributed to the
  access check.

  Enums:
    DenyAccessStateValueValuesEnum: Required. Indicates whether _this policy_
      denies the specified permission to the specified principal for the
      specified resource. This field does _not_ indicate whether the principal
      actually has the permission for the resource. There might be another
      policy that overrides this policy. To determine whether the principal
      actually has the permission, use the `overall_access_state` field in the
      TroubleshootIamPolicyResponse.
    RelevanceValueValuesEnum: The relevance of this policy to the overall
      access state in the TroubleshootIamPolicyResponse. If the sender of the
      request does not have access to the policy, this field is omitted.

  Fields:
    denyAccessState: Required. Indicates whether _this policy_ denies the
      specified permission to the specified principal for the specified
      resource. This field does _not_ indicate whether the principal actually
      has the permission for the resource. There might be another policy that
      overrides this policy. To determine whether the principal actually has
      the permission, use the `overall_access_state` field in the
      TroubleshootIamPolicyResponse.
    policy: The IAM deny policy attached to the resource. If the sender of the
      request does not have access to the policy, this field is omitted.
    relevance: The relevance of this policy to the overall access state in the
      TroubleshootIamPolicyResponse. If the sender of the request does not
      have access to the policy, this field is omitted.
    ruleExplanations: Details about how each rule in the policy affects the
      principal's inability to use the permission for the resource. The order
      of the deny rule matches the order of the rules in the deny policy. If
      the sender of the request does not have access to the policy, this field
      is omitted.
  """

  class DenyAccessStateValueValuesEnum(_messages.Enum):
    r"""Required. Indicates whether _this policy_ denies the specified
    permission to the specified principal for the specified resource. This
    field does _not_ indicate whether the principal actually has the
    permission for the resource. There might be another policy that overrides
    this policy. To determine whether the principal actually has the
    permission, use the `overall_access_state` field in the
    TroubleshootIamPolicyResponse.

    Values:
      DENY_ACCESS_STATE_UNSPECIFIED: Not specified.
      DENY_ACCESS_STATE_DENIED: The deny policy denies the principal the
        permission.
      DENY_ACCESS_STATE_NOT_DENIED: The deny policy doesn't deny the principal
        the permission.
      DENY_ACCESS_STATE_UNKNOWN_CONDITIONAL: The deny policy denies the
        principal the permission if a condition expression evaluates to
        `true`. However, the sender of the request didn't provide enough
        context for Policy Troubleshooter to evaluate the condition
        expression.
      DENY_ACCESS_STATE_UNKNOWN_INFO: The sender of the request does not have
        access to all of the deny policies that Policy Troubleshooter needs to
        evaluate the principal's access.
    """
    DENY_ACCESS_STATE_UNSPECIFIED = 0
    DENY_ACCESS_STATE_DENIED = 1
    DENY_ACCESS_STATE_NOT_DENIED = 2
    DENY_ACCESS_STATE_UNKNOWN_CONDITIONAL = 3
    DENY_ACCESS_STATE_UNKNOWN_INFO = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of this policy to the overall access state in the
    TroubleshootIamPolicyResponse. If the sender of the request does not have
    access to the policy, this field is omitted.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  denyAccessState = _messages.EnumField('DenyAccessStateValueValuesEnum', 1)
  policy = _messages.MessageField('GoogleIamV2Policy', 2)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 3)
  ruleExplanations = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaDenyRuleExplanation', 4, repeated=True)


class GoogleCloudPolicytroubleshooterIamV3betaExplainedDenyResource(_messages.Message):
  r"""Details about how a specific resource contributed to the deny policy
  evaluation.

  Enums:
    DenyAccessStateValueValuesEnum: Required. Indicates whether any policies
      attached to _this resource_ deny the specific permission to the
      specified principal for the specified resource. This field does _not_
      indicate whether the principal actually has the permission for the
      resource. There might be another policy that overrides this policy. To
      determine whether the principal actually has the permission, use the
      `overall_access_state` field in the TroubleshootIamPolicyResponse.
    RelevanceValueValuesEnum: The relevance of this policy to the overall
      access state in the TroubleshootIamPolicyResponse. If the sender of the
      request does not have access to the policy, this field is omitted.

  Fields:
    denyAccessState: Required. Indicates whether any policies attached to
      _this resource_ deny the specific permission to the specified principal
      for the specified resource. This field does _not_ indicate whether the
      principal actually has the permission for the resource. There might be
      another policy that overrides this policy. To determine whether the
      principal actually has the permission, use the `overall_access_state`
      field in the TroubleshootIamPolicyResponse.
    explainedPolicies: List of IAM deny policies that were evaluated to check
      the principal's denied permissions, with annotations to indicate how
      each policy contributed to the final result.
    fullResourceName: The full resource name that identifies the resource. For
      example, `//compute.googleapis.com/projects/my-project/zones/us-
      central1-a/instances/my-instance`. If the sender of the request does not
      have access to the policy, this field is omitted. For examples of full
      resource names for Google Cloud services, see
      https://cloud.google.com/iam/help/troubleshooter/full-resource-names.
    relevance: The relevance of this policy to the overall access state in the
      TroubleshootIamPolicyResponse. If the sender of the request does not
      have access to the policy, this field is omitted.
  """

  class DenyAccessStateValueValuesEnum(_messages.Enum):
    r"""Required. Indicates whether any policies attached to _this resource_
    deny the specific permission to the specified principal for the specified
    resource. This field does _not_ indicate whether the principal actually
    has the permission for the resource. There might be another policy that
    overrides this policy. To determine whether the principal actually has the
    permission, use the `overall_access_state` field in the
    TroubleshootIamPolicyResponse.

    Values:
      DENY_ACCESS_STATE_UNSPECIFIED: Not specified.
      DENY_ACCESS_STATE_DENIED: The deny policy denies the principal the
        permission.
      DENY_ACCESS_STATE_NOT_DENIED: The deny policy doesn't deny the principal
        the permission.
      DENY_ACCESS_STATE_UNKNOWN_CONDITIONAL: The deny policy denies the
        principal the permission if a condition expression evaluates to
        `true`. However, the sender of the request didn't provide enough
        context for Policy Troubleshooter to evaluate the condition
        expression.
      DENY_ACCESS_STATE_UNKNOWN_INFO: The sender of the request does not have
        access to all of the deny policies that Policy Troubleshooter needs to
        evaluate the principal's access.
    """
    DENY_ACCESS_STATE_UNSPECIFIED = 0
    DENY_ACCESS_STATE_DENIED = 1
    DENY_ACCESS_STATE_NOT_DENIED = 2
    DENY_ACCESS_STATE_UNKNOWN_CONDITIONAL = 3
    DENY_ACCESS_STATE_UNKNOWN_INFO = 4

  class RelevanceValueValuesEnum(_messages.Enum):
    r"""The relevance of this policy to the overall access state in the
    TroubleshootIamPolicyResponse. If the sender of the request does not have
    access to the policy, this field is omitted.

    Values:
      HEURISTIC_RELEVANCE_UNSPECIFIED: Not specified.
      HEURISTIC_RELEVANCE_NORMAL: The data point has a limited effect on the
        result. Changing the data point is unlikely to affect the overall
        determination.
      HEURISTIC_RELEVANCE_HIGH: The data point has a strong effect on the
        result. Changing the data point is likely to affect the overall
        determination.
    """
    HEURISTIC_RELEVANCE_UNSPECIFIED = 0
    HEURISTIC_RELEVANCE_NORMAL = 1
    HEURISTIC_RELEVANCE_HIGH = 2

  denyAccessState = _messages.EnumField('DenyAccessStateValueValuesEnum', 1)
  explainedPolicies = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaExplainedDenyPolicy', 2, repeated=True)
  fullResourceName = _messages.StringField(3)
  relevance = _messages.EnumField('RelevanceValueValuesEnum', 4)


class GoogleCloudPolicytroubleshooterIamV3betaTroubleshootIamPolicyRequest(_messages.Message):
  r"""Request for TroubleshootIamPolicy.

  Fields:
    accessTuple: The information to use for checking whether a principal has a
      permission for a resource.
  """

  accessTuple = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaAccessTuple', 1)


class GoogleCloudPolicytroubleshooterIamV3betaTroubleshootIamPolicyResponse(_messages.Message):
  r"""Response for TroubleshootIamPolicy.

  Enums:
    OverallAccessStateValueValuesEnum: Indicates whether the principal has the
      specified permission for the specified resource, based on evaluating all
      types of the applicable IAM policies.

  Fields:
    accessTuple: The access tuple from the request, including any provided
      context used to evaluate the condition.
    allowPolicyExplanation: An explanation of how the applicable IAM allow
      policies affect the final access state.
    denyPolicyExplanation: An explanation of how the applicable IAM deny
      policies affect the final access state.
    overallAccessState: Indicates whether the principal has the specified
      permission for the specified resource, based on evaluating all types of
      the applicable IAM policies.
  """

  class OverallAccessStateValueValuesEnum(_messages.Enum):
    r"""Indicates whether the principal has the specified permission for the
    specified resource, based on evaluating all types of the applicable IAM
    policies.

    Values:
      OVERALL_ACCESS_STATE_UNSPECIFIED: Not specified.
      CAN_ACCESS: The principal has the permission.
      CANNOT_ACCESS: The principal doesn't have the permission.
      UNKNOWN_INFO: The principal might have the permission, but the sender
        can't access all of the information needed to fully evaluate the
        principal's access.
      UNKNOWN_CONDITIONAL: The principal might have the permission, but Policy
        Troubleshooter can't fully evaluate the principal's access because the
        sender didn't provide the required context to evaluate the condition.
    """
    OVERALL_ACCESS_STATE_UNSPECIFIED = 0
    CAN_ACCESS = 1
    CANNOT_ACCESS = 2
    UNKNOWN_INFO = 3
    UNKNOWN_CONDITIONAL = 4

  accessTuple = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaAccessTuple', 1)
  allowPolicyExplanation = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaAllowPolicyExplanation', 2)
  denyPolicyExplanation = _messages.MessageField('GoogleCloudPolicytroubleshooterIamV3betaDenyPolicyExplanation', 3)
  overallAccessState = _messages.EnumField('OverallAccessStateValueValuesEnum', 4)


class GoogleIamV1AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('GoogleIamV1AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class GoogleIamV1AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    ignoreChildExemptions: A boolean attribute.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  ignoreChildExemptions = _messages.BooleanField(2)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 3)


class GoogleIamV1Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    bindingId: A string attribute.
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=********************1`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=********************1`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=********************1`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  bindingId = _messages.StringField(1)
  condition = _messages.MessageField('GoogleTypeExpr', 2)
  members = _messages.StringField(3, repeated=True)
  role = _messages.StringField(4)


class GoogleIamV1Condition(_messages.Message):
  r"""A condition to be met.

  Enums:
    IamValueValuesEnum: Trusted attributes supplied by the IAM system.
    OpValueValuesEnum: An operator to apply the subject with.
    SysValueValuesEnum: Trusted attributes supplied by any service that owns
      resources and uses the IAM system for access control.

  Fields:
    iam: Trusted attributes supplied by the IAM system.
    op: An operator to apply the subject with.
    svc: Trusted attributes discharged by the service.
    sys: Trusted attributes supplied by any service that owns resources and
      uses the IAM system for access control.
    values: The objects of the condition.
  """

  class IamValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by the IAM system.

    Values:
      NO_ATTR: Default non-attribute.
      AUTHORITY: Either principal or (if present) authority selector.
      ATTRIBUTION: The principal (even if an authority selector is present),
        which must only be used for attribution, not authorization.
      SECURITY_REALM: Any of the security realms in the IAMContext
        (go/security-realms). When used with IN, the condition indicates "any
        of the request's realms match one of the given values; with NOT_IN,
        "none of the realms match any of the given values". Note that a value
        can be: - 'self:campus' (i.e., clients that are in the same campus) -
        'self:metro' (i.e., clients that are in the same metro) - 'self:cloud-
        region' (i.e., allow connections from clients that are in the same
        cloud region) - 'self:prod-region' (i.e., allow connections from
        clients that are in the same prod region) - 'guardians' (i.e., allow
        connections from its guardian realms. See go/security-realms-
        glossary#guardian for more information.) - 'self' [DEPRECATED] (i.e.,
        allow connections from clients that are in the same security realm,
        which is currently but not guaranteed to be campus-sized) - a realm
        (e.g., 'campus-abc') - a realm group (e.g., 'realms-for-borg-cell-xx',
        see: go/realm-groups) A match is determined by a realm group
        membership check performed by a RealmAclRep object (go/realm-acl-
        howto). It is not permitted to grant access based on the *absence* of
        a realm, so realm conditions can only be used in a "positive" context
        (e.g., ALLOW/IN or DENY/NOT_IN).
      APPROVER: An approver (distinct from the requester) that has authorized
        this request. When used with IN, the condition indicates that one of
        the approvers associated with the request matches the specified
        principal, or is a member of the specified group. Approvers can only
        grant additional access, and are thus only used in a strictly positive
        context (e.g. ALLOW/IN or DENY/NOT_IN).
      JUSTIFICATION_TYPE: What types of justifications have been supplied with
        this request. String values should match enum names from
        security.credentials.JustificationType, e.g. "MANUAL_STRING". It is
        not permitted to grant access based on the *absence* of a
        justification, so justification conditions can only be used in a
        "positive" context (e.g., ALLOW/IN or DENY/NOT_IN). Multiple
        justifications, e.g., a Buganizer ID and a manually-entered reason,
        are normal and supported.
      CREDENTIALS_TYPE: What type of credentials have been supplied with this
        request. String values should match enum names from
        security_loas_l2.CredentialsType - currently, only
        CREDS_TYPE_EMERGENCY is supported. It is not permitted to grant access
        based on the *absence* of a credentials type, so the conditions can
        only be used in a "positive" context (e.g., ALLOW/IN or DENY/NOT_IN).
      CREDS_ASSERTION: EXPERIMENTAL -- DO NOT USE. The conditions can only be
        used in a "positive" context (e.g., ALLOW/IN or DENY/NOT_IN).
    """
    NO_ATTR = 0
    AUTHORITY = 1
    ATTRIBUTION = 2
    SECURITY_REALM = 3
    APPROVER = 4
    JUSTIFICATION_TYPE = 5
    CREDENTIALS_TYPE = 6
    CREDS_ASSERTION = 7

  class OpValueValuesEnum(_messages.Enum):
    r"""An operator to apply the subject with.

    Values:
      NO_OP: Default no-op.
      EQUALS: DEPRECATED. Use IN instead.
      NOT_EQUALS: DEPRECATED. Use NOT_IN instead.
      IN: The condition is true if the subject (or any element of it if it is
        a set) matches any of the supplied values.
      NOT_IN: The condition is true if the subject (or every element of it if
        it is a set) matches none of the supplied values.
      DISCHARGED: Subject is discharged
    """
    NO_OP = 0
    EQUALS = 1
    NOT_EQUALS = 2
    IN = 3
    NOT_IN = 4
    DISCHARGED = 5

  class SysValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by any service that owns resources and
    uses the IAM system for access control.

    Values:
      NO_ATTR: Default non-attribute type
      REGION: Region of the resource
      SERVICE: Service name
      NAME: Resource name
      IP: IP address of the caller
    """
    NO_ATTR = 0
    REGION = 1
    SERVICE = 2
    NAME = 3
    IP = 4

  iam = _messages.EnumField('IamValueValuesEnum', 1)
  op = _messages.EnumField('OpValueValuesEnum', 2)
  svc = _messages.StringField(3)
  sys = _messages.EnumField('SysValueValuesEnum', 4)
  values = _messages.StringField(5, repeated=True)


class GoogleIamV1LogConfig(_messages.Message):
  r"""Specifies what kind of log the caller must write

  Fields:
    cloudAudit: Cloud audit options.
    counter: Counter options.
    dataAccess: Data access options.
  """

  cloudAudit = _messages.MessageField('GoogleIamV1LogConfigCloudAuditOptions', 1)
  counter = _messages.MessageField('GoogleIamV1LogConfigCounterOptions', 2)
  dataAccess = _messages.MessageField('GoogleIamV1LogConfigDataAccessOptions', 3)


class GoogleIamV1LogConfigCloudAuditOptions(_messages.Message):
  r"""Write a Cloud Audit log

  Enums:
    LogNameValueValuesEnum: The log_name to populate in the Cloud Audit
      Record.

  Fields:
    authorizationLoggingOptions: Information used by the Cloud Audit Logging
      pipeline.
    logName: The log_name to populate in the Cloud Audit Record.
  """

  class LogNameValueValuesEnum(_messages.Enum):
    r"""The log_name to populate in the Cloud Audit Record.

    Values:
      UNSPECIFIED_LOG_NAME: Default. Should not be used.
      ADMIN_ACTIVITY: Corresponds to "cloudaudit.googleapis.com/activity"
      DATA_ACCESS: Corresponds to "cloudaudit.googleapis.com/data_access"
    """
    UNSPECIFIED_LOG_NAME = 0
    ADMIN_ACTIVITY = 1
    DATA_ACCESS = 2

  authorizationLoggingOptions = _messages.MessageField('GoogleCloudAuditAuthorizationLoggingOptions', 1)
  logName = _messages.EnumField('LogNameValueValuesEnum', 2)


class GoogleIamV1LogConfigCounterOptions(_messages.Message):
  r"""Increment a streamz counter with the specified metric and field names.
  Metric names should start with a '/', generally be lowercase-only, and end
  in "_count". Field names should not contain an initial slash. The actual
  exported metric names will have "/iam/policy" prepended. Field names
  correspond to IAM request parameters and field values are their respective
  values. Supported field names: - "authority", which is "[token]" if
  IAMContext.token is present, otherwise the value of
  IAMContext.authority_selector if present, and otherwise a representation of
  IAMContext.principal; or - "iam_principal", a representation of
  IAMContext.principal even if a token or authority selector is present; or -
  "" (empty string), resulting in a counter with no fields. Examples: counter
  { metric: "/debug_access_count" field: "iam_principal" } ==> increment
  counter /iam/policy/debug_access_count {iam_principal=[value of
  IAMContext.principal]}

  Fields:
    customFields: Custom fields.
    field: The field value to attribute.
    metric: The metric to update.
  """

  customFields = _messages.MessageField('GoogleIamV1LogConfigCounterOptionsCustomField', 1, repeated=True)
  field = _messages.StringField(2)
  metric = _messages.StringField(3)


class GoogleIamV1LogConfigCounterOptionsCustomField(_messages.Message):
  r"""Custom fields. These can be used to create a counter with arbitrary
  field/value pairs. See: go/rpcsp-custom-fields.

  Fields:
    name: Name is the field name.
    value: Value is the field value. It is important that in contrast to the
      CounterOptions.field, the value here is a constant that is not derived
      from the IAMContext.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleIamV1LogConfigDataAccessOptions(_messages.Message):
  r"""Write a Data Access (Gin) log

  Enums:
    LogModeValueValuesEnum:

  Fields:
    logMode: A LogModeValueValuesEnum attribute.
  """

  class LogModeValueValuesEnum(_messages.Enum):
    r"""LogModeValueValuesEnum enum type.

    Values:
      LOG_MODE_UNSPECIFIED: Client is not required to write a partial Gin log
        immediately after the authorization check. If client chooses to write
        one and it fails, client may either fail open (allow the operation to
        continue) or fail closed (handle as a DENY outcome).
      LOG_FAIL_CLOSED: The application's operation in the context of which
        this authorization check is being made may only be performed if it is
        successfully logged to Gin. For instance, the authorization library
        may satisfy this obligation by emitting a partial log entry at
        authorization check time and only returning ALLOW to the application
        if it succeeds. If a matching Rule has this directive, but the client
        has not indicated that it will honor such requirements, then the IAM
        check will result in authorization failure by setting
        CheckPolicyResponse.success=false.
    """
    LOG_MODE_UNSPECIFIED = 0
    LOG_FAIL_CLOSED = 1

  logMode = _messages.EnumField('LogModeValueValuesEnum', 1)


class GoogleIamV1Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    rules: If more than one rule is specified, the rules are applied in the
      following manner: - All matching LOG rules are always applied. - If any
      DENY/DENY_WITH_LOG rule matches, permission is denied. Logging will be
      applied if one or more matching rule requires logging. - Otherwise, if
      any ALLOW/ALLOW_WITH_LOG rule matches, permission is granted. Logging
      will be applied if one or more matching rule requires logging. -
      Otherwise, if no rule applies, permission is denied.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('GoogleIamV1AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('GoogleIamV1Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  rules = _messages.MessageField('GoogleIamV1Rule', 4, repeated=True)
  version = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class GoogleIamV1Rule(_messages.Message):
  r"""A rule to be applied in a Policy.

  Enums:
    ActionValueValuesEnum: Required

  Fields:
    action: Required
    conditions: Additional restrictions that must be met. All conditions must
      pass for the rule to match.
    description: Human-readable description of the rule.
    in_: If one or more 'in' clauses are specified, the rule matches if the
      PRINCIPAL/AUTHORITY_SELECTOR is in at least one of these entries.
    logConfig: The config returned to callers of CheckPolicy for any entries
      that match the LOG action.
    notIn: If one or more 'not_in' clauses are specified, the rule matches if
      the PRINCIPAL/AUTHORITY_SELECTOR is in none of the entries. The format
      for in and not_in entries can be found at in the Local IAM documentation
      (see go/local-iam#features).
    permissions: A permission is a string of form '..' (e.g.,
      'storage.buckets.list'). A value of '*' matches all permissions, and a
      verb part of '*' (e.g., 'storage.buckets.*') matches all verbs.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required

    Values:
      NO_ACTION: Default no action.
      ALLOW: Matching 'Entries' grant access.
      ALLOW_WITH_LOG: Matching 'Entries' grant access and the caller promises
        to log the request per the returned log_configs.
      DENY: Matching 'Entries' deny access.
      DENY_WITH_LOG: Matching 'Entries' deny access and the caller promises to
        log the request per the returned log_configs.
      LOG: Matching 'Entries' tell IAM.Check callers to generate logs.
    """
    NO_ACTION = 0
    ALLOW = 1
    ALLOW_WITH_LOG = 2
    DENY = 3
    DENY_WITH_LOG = 4
    LOG = 5

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  conditions = _messages.MessageField('GoogleIamV1Condition', 2, repeated=True)
  description = _messages.StringField(3)
  in_ = _messages.StringField(4, repeated=True)
  logConfig = _messages.MessageField('GoogleIamV1LogConfig', 5, repeated=True)
  notIn = _messages.StringField(6, repeated=True)
  permissions = _messages.StringField(7, repeated=True)


class GoogleIamV2AccessBoundaryRule(_messages.Message):
  r"""An IAM access boundary rule, which defines an upper bound of IAM
  permissions on a single resource. All access boundary rules in an access
  boundary policy are evaluated together as a union. Even if this access
  boundary rule does not allow access to the resource, another access boundary
  rule might allow access.

  Fields:
    availabilityCondition: Optional. An availability condition that further
      constrains the access allowed by the access boundary rule. If the
      condition evaluates to `true`, then this access boundary rule will
      provide access to the specified resource, assuming the principal has the
      required permissions for the resource. If the condition does not
      evaluate to `true`, then access to the specified resource will not be
      available. The condition can only evaluate the access level for the
      request. Access levels use the format
      `accessPolicies/{policy_name}/accessLevels/{access_level_shortname}`.
    availablePermissions: Required. A list of permissions that may be allowed
      for use on the specified resource. The only supported value is `*`,
      which represents all permissions.
    availableResource: Required. The full resource name of a Google Cloud
      resource. The format is defined at
      https://cloud.google.com/apis/design/resource_names. The only supported
      value is `*`, which represents all resources.
  """

  availabilityCondition = _messages.MessageField('GoogleTypeExpr', 1)
  availablePermissions = _messages.StringField(2, repeated=True)
  availableResource = _messages.StringField(3)


class GoogleIamV2DenyRule(_messages.Message):
  r"""A deny rule in an IAM deny policy.

  Fields:
    denialCondition: The condition that determines whether this deny rule
      applies to a request. If the condition expression evaluates to `true`,
      then the deny rule is applied; otherwise, the deny rule is not applied.
      Each deny rule is evaluated independently. If this deny rule does not
      apply to a request, other deny rules might still apply. The condition
      can use CEL functions that evaluate [resource
      tags](https://cloud.google.com/iam/help/conditions/resource-tags). Other
      functions and operators are not supported.
    deniedPermissions: The permissions that are explicitly denied by this
      rule. Each permission uses the format
      `{service_fqdn}/{resource}.{verb}`, where `{service_fqdn}` is the fully
      qualified domain name for the service. For example,
      `iam.googleapis.com/roles.list`.
    deniedPrincipals: The identities that are prevented from using one or more
      permissions on Google Cloud resources. This field can contain the
      following values: * `principalSet://goog/public:all`: A special
      identifier that represents any principal that is on the internet, even
      if they do not have a Google Account or are not logged in. *
      `principal://goog/subject/{email_id}`: A specific Google Account.
      Includes Gmail, Cloud Identity, and Google Workspace user accounts. For
      example, `principal://goog/subject/<EMAIL>`. *
      `deleted:principal://goog/subject/{email_id}?uid={uid}`: A specific
      Google Account that was deleted recently. For example,
      `deleted:principal://goog/subject/<EMAIL>?uid=**********`. If
      the Google Account is recovered, this identifier reverts to the standard
      identifier for a Google Account. *
      `principalSet://goog/group/{group_id}`: A Google group. For example,
      `principalSet://goog/group/<EMAIL>`. *
      `deleted:principalSet://goog/group/{group_id}?uid={uid}`: A Google group
      that was deleted recently. For example,
      `deleted:principalSet://goog/group/<EMAIL>?uid=**********`.
      If the Google group is restored, this identifier reverts to the standard
      identifier for a Google group. * `principal://iam.googleapis.com/project
      s/-/serviceAccounts/{service_account_id}`: A Google Cloud service
      account. For example,
      `principal://iam.googleapis.com/projects/-/serviceAccounts/my-service-
      <EMAIL>`. * `deleted:principal://iam.googleapis.
      com/projects/-/serviceAccounts/{service_account_id}?uid={uid}`: A Google
      Cloud service account that was deleted recently. For example,
      `deleted:principal://iam.googleapis.com/projects/-/serviceAccounts/my-
      <EMAIL>?uid=**********`. If the service
      account is undeleted, this identifier reverts to the standard identifier
      for a service account. *
      `principalSet://goog/cloudIdentityCustomerId/{customer_id}`: All of the
      principals associated with the specified Google Workspace or Cloud
      Identity customer ID. For example,
      `principalSet://goog/cloudIdentityCustomerId/C01Abc35`.
    exceptionPermissions: Specifies the permissions that this rule excludes
      from the set of denied permissions given by `denied_permissions`. If a
      permission appears in `denied_permissions` _and_ in
      `exception_permissions` then it will _not_ be denied. The excluded
      permissions can be specified using the same syntax as
      `denied_permissions`.
    exceptionPrincipals: The identities that are excluded from the deny rule,
      even if they are listed in the `denied_principals`. For example, you
      could add a Google group to the `denied_principals`, then exclude
      specific users who belong to that group. This field can contain the same
      values as the `denied_principals` field, excluding
      `principalSet://goog/public:all`, which represents all users on the
      internet.
  """

  denialCondition = _messages.MessageField('GoogleTypeExpr', 1)
  deniedPermissions = _messages.StringField(2, repeated=True)
  deniedPrincipals = _messages.StringField(3, repeated=True)
  exceptionPermissions = _messages.StringField(4, repeated=True)
  exceptionPrincipals = _messages.StringField(5, repeated=True)


class GoogleIamV2Policy(_messages.Message):
  r"""Data for an IAM policy.

  Messages:
    AnnotationsValue: A key-value map to store arbitrary metadata for the
      `Policy`. Keys can be up to 63 characters. Values can be up to 255
      characters.

  Fields:
    annotations: A key-value map to store arbitrary metadata for the `Policy`.
      Keys can be up to 63 characters. Values can be up to 255 characters.
    createTime: Output only. The time when the `Policy` was created.
    deleteTime: Output only. The time when the `Policy` was deleted. Empty if
      the policy is not deleted.
    displayName: A user-specified description of the `Policy`. This value can
      be up to 63 characters.
    etag: An opaque tag that identifies the current version of the `Policy`.
      IAM uses this value to help manage concurrent updates, so they do not
      cause one update to be overwritten by another. If this field is present
      in a CreatePolicyRequest, the value is ignored.
    kind: Output only. The kind of the `Policy`. Always contains the value
      `DenyPolicy`.
    managingAuthority: Immutable. Specifies that this policy is managed by an
      authority and can only be modified by that authority. Usage is
      restricted.
    name: Immutable. The resource name of the `Policy`, which must be unique.
      Format: `policies/{attachment_point}/denypolicies/{policy_id}` The
      attachment point is identified by its URL-encoded full resource name,
      which means that the forward-slash character, `/`, must be written as
      `%2F`. For example,
      `policies/cloudresourcemanager.googleapis.com%2Fprojects%2Fmy-
      project/denypolicies/my-deny-policy`. For organizations and folders, use
      the numeric ID in the full resource name. For projects, requests can use
      the alphanumeric or the numeric ID. Responses always contain the numeric
      ID.
    rules: A list of rules that specify the behavior of the `Policy`. All of
      the rules should be of the `kind` specified in the `Policy`.
    uid: Immutable. The globally unique ID of the `Policy`. Assigned
      automatically when the `Policy` is created.
    updateTime: Output only. The time when the `Policy` was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""A key-value map to store arbitrary metadata for the `Policy`. Keys can
    be up to 63 characters. Values can be up to 255 characters.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  deleteTime = _messages.StringField(3)
  displayName = _messages.StringField(4)
  etag = _messages.StringField(5)
  kind = _messages.StringField(6)
  managingAuthority = _messages.StringField(7)
  name = _messages.StringField(8)
  rules = _messages.MessageField('GoogleIamV2PolicyRule', 9, repeated=True)
  uid = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class GoogleIamV2PolicyRule(_messages.Message):
  r"""A single rule in a `Policy`.

  Fields:
    accessBoundaryRule: A rule for an access boundary policy.
    denyRule: A rule for a deny policy.
    description: A user-specified description of the rule. This value can be
      up to 256 characters.
  """

  accessBoundaryRule = _messages.MessageField('GoogleIamV2AccessBoundaryRule', 1)
  denyRule = _messages.MessageField('GoogleIamV2DenyRule', 2)
  description = _messages.StringField(3)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class GoogleTypeExpr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    GoogleIamV1Rule, 'in_', 'in')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

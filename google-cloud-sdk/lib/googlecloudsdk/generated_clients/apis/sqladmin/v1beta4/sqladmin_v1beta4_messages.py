"""Generated message classes for sqladmin version v1beta4.

API for Cloud SQL database instance management
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding


package = 'sqladmin'


class AclEntry(_messages.Message):
  r"""An entry for an Access Control list.

  Fields:
    expirationTime: The time when this access control entry expires in [RFC
      3339](https://tools.ietf.org/html/rfc3339) format, for example
      `2012-11-15T16:19:00.094Z`.
    kind: This is always `sql#aclEntry`.
    name: Optional. A label to identify this entry.
    value: The allowlisted value for the access control list.
  """

  expirationTime = _messages.StringField(1)
  kind = _messages.StringField(2)
  name = _messages.StringField(3)
  value = _messages.StringField(4)


class AdvancedMachineFeatures(_messages.Message):
  r"""Specifies options for controlling advanced machine features.

  Fields:
    threadsPerCore: The number of threads per physical core.
  """

  threadsPerCore = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class ApiWarning(_messages.Message):
  r"""An Admin API warning message.

  Enums:
    CodeValueValuesEnum: Code to uniquely identify the warning type.

  Fields:
    code: Code to uniquely identify the warning type.
    message: The warning message.
    region: The region name for REGION_UNREACHABLE warning.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Code to uniquely identify the warning type.

    Values:
      SQL_API_WARNING_CODE_UNSPECIFIED: An unknown or unset warning type from
        Cloud SQL API.
      REGION_UNREACHABLE: Warning when one or more regions are not reachable.
        The returned result set may be incomplete.
      MAX_RESULTS_EXCEEDS_LIMIT: Warning when user provided maxResults
        parameter exceeds the limit. The returned result set may be
        incomplete.
    """
    SQL_API_WARNING_CODE_UNSPECIFIED = 0
    REGION_UNREACHABLE = 1
    MAX_RESULTS_EXCEEDS_LIMIT = 2

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  message = _messages.StringField(2)
  region = _messages.StringField(3)


class BackupConfiguration(_messages.Message):
  r"""Database instance backup configuration.

  Fields:
    backupRetentionSettings: Backup retention settings.
    binaryLogEnabled: (MySQL only) Whether binary log is enabled. If backup
      configuration is disabled, binarylog must be disabled as well.
    enabled: Whether this configuration is enabled.
    kind: This is always `sql#backupConfiguration`.
    location: Location of the backup
    pointInTimeRecoveryEnabled: Whether point in time recovery is enabled.
    replicationLogArchivingEnabled: Reserved for future use.
    startTime: Start time for the daily backup configuration in UTC timezone
      in the 24 hour format - `HH:MM`.
    transactionLogRetentionDays: The number of days of transaction logs we
      retain for point in time restore, from 1-7.
  """

  backupRetentionSettings = _messages.MessageField('BackupRetentionSettings', 1)
  binaryLogEnabled = _messages.BooleanField(2)
  enabled = _messages.BooleanField(3)
  kind = _messages.StringField(4)
  location = _messages.StringField(5)
  pointInTimeRecoveryEnabled = _messages.BooleanField(6)
  replicationLogArchivingEnabled = _messages.BooleanField(7)
  startTime = _messages.StringField(8)
  transactionLogRetentionDays = _messages.IntegerField(9, variant=_messages.Variant.INT32)


class BackupContext(_messages.Message):
  r"""Backup context.

  Fields:
    backupId: The identifier of the backup.
    kind: This is always `sql#backupContext`.
    name: The name of the backup. Format: projects/{project}/backups/{backup}
  """

  backupId = _messages.IntegerField(1)
  kind = _messages.StringField(2)
  name = _messages.StringField(3)


class BackupReencryptionConfig(_messages.Message):
  r"""Backup Reencryption Config

  Enums:
    BackupTypeValueValuesEnum: Type of backups users want to re-encrypt.

  Fields:
    backupLimit: Backup re-encryption limit
    backupType: Type of backups users want to re-encrypt.
  """

  class BackupTypeValueValuesEnum(_messages.Enum):
    r"""Type of backups users want to re-encrypt.

    Values:
      BACKUP_TYPE_UNSPECIFIED: Unknown backup type, will be defaulted to
        AUTOMATIC backup type
      AUTOMATED: Reencrypt automatic backups
      ON_DEMAND: Reencrypt on-demand backups
    """
    BACKUP_TYPE_UNSPECIFIED = 0
    AUTOMATED = 1
    ON_DEMAND = 2

  backupLimit = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  backupType = _messages.EnumField('BackupTypeValueValuesEnum', 2)


class BackupRetentionSettings(_messages.Message):
  r"""We currently only support backup retention by specifying the number of
  backups we will retain.

  Enums:
    RetentionUnitValueValuesEnum: The unit that 'retained_backups' represents.

  Fields:
    retainedBackups: Depending on the value of retention_unit, this is used to
      determine if a backup needs to be deleted. If retention_unit is 'COUNT',
      we will retain this many backups.
    retentionUnit: The unit that 'retained_backups' represents.
  """

  class RetentionUnitValueValuesEnum(_messages.Enum):
    r"""The unit that 'retained_backups' represents.

    Values:
      RETENTION_UNIT_UNSPECIFIED: Backup retention unit is unspecified, will
        be treated as COUNT.
      COUNT: Retention will be by count, eg. "retain the most recent 7
        backups".
    """
    RETENTION_UNIT_UNSPECIFIED = 0
    COUNT = 1

  retainedBackups = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  retentionUnit = _messages.EnumField('RetentionUnitValueValuesEnum', 2)


class BackupRun(_messages.Message):
  r"""A BackupRun resource.

  Enums:
    BackupKindValueValuesEnum: Specifies the kind of backup, PHYSICAL or
      DEFAULT_SNAPSHOT.
    StatusValueValuesEnum: The status of this run.
    TypeValueValuesEnum: The type of this run; can be either "AUTOMATED" or
      "ON_DEMAND" or "FINAL". This field defaults to "ON_DEMAND" and is
      ignored, when specified for insert requests.

  Fields:
    backupDatabaseInstalledVersion: Output only. Currently installed database
      version on the instance, including minor versions, such as
      `MYSQL_8_0_18`.
    backupKind: Specifies the kind of backup, PHYSICAL or DEFAULT_SNAPSHOT.
    description: The description of this run, only applicable to on-demand
      backups.
    diskEncryptionConfiguration: Encryption configuration specific to a
      backup.
    diskEncryptionStatus: Encryption status specific to a backup.
    endTime: The time the backup operation completed in UTC timezone in [RFC
      3339](https://tools.ietf.org/html/rfc3339) format, for example
      `2012-11-15T16:19:00.094Z`.
    enqueuedTime: The time the run was enqueued in UTC timezone in [RFC
      3339](https://tools.ietf.org/html/rfc3339) format, for example
      `2012-11-15T16:19:00.094Z`.
    error: Information about why the backup operation failed. This is only
      present if the run has the FAILED status.
    id: The identifier for this backup run. Unique only for a specific Cloud
      SQL instance.
    instance: Name of the database instance.
    kind: This is always `sql#backupRun`.
    location: Location of the backups.
    selfLink: The URI of this resource.
    startTime: The time the backup operation actually started in UTC timezone
      in [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
      `2012-11-15T16:19:00.094Z`.
    status: The status of this run.
    timeZone: Backup time zone to prevent restores to an instance with a
      different time zone. Now relevant only for SQL Server.
    type: The type of this run; can be either "AUTOMATED" or "ON_DEMAND" or
      "FINAL". This field defaults to "ON_DEMAND" and is ignored, when
      specified for insert requests.
    windowStartTime: The start time of the backup window during which this the
      backup was attempted in [RFC 3339](https://tools.ietf.org/html/rfc3339)
      format, for example `2012-11-15T16:19:00.094Z`.
  """

  class BackupKindValueValuesEnum(_messages.Enum):
    r"""Specifies the kind of backup, PHYSICAL or DEFAULT_SNAPSHOT.

    Values:
      SQL_BACKUP_KIND_UNSPECIFIED: This is an unknown BackupKind.
      SNAPSHOT: The snapshot based backups
      PHYSICAL: Physical backups
    """
    SQL_BACKUP_KIND_UNSPECIFIED = 0
    SNAPSHOT = 1
    PHYSICAL = 2

  class StatusValueValuesEnum(_messages.Enum):
    r"""The status of this run.

    Values:
      SQL_BACKUP_RUN_STATUS_UNSPECIFIED: The status of the run is unknown.
      ENQUEUED: The backup operation was enqueued.
      OVERDUE: The backup is overdue across a given backup window. Indicates a
        problem. Example: Long-running operation in progress during the whole
        window.
      RUNNING: The backup is in progress.
      FAILED: The backup failed.
      SUCCESSFUL: The backup was successful.
      SKIPPED: The backup was skipped (without problems) for a given backup
        window. Example: Instance was idle.
      DELETION_PENDING: The backup is about to be deleted.
      DELETION_FAILED: The backup deletion failed.
      DELETED: The backup has been deleted.
    """
    SQL_BACKUP_RUN_STATUS_UNSPECIFIED = 0
    ENQUEUED = 1
    OVERDUE = 2
    RUNNING = 3
    FAILED = 4
    SUCCESSFUL = 5
    SKIPPED = 6
    DELETION_PENDING = 7
    DELETION_FAILED = 8
    DELETED = 9

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of this run; can be either "AUTOMATED" or "ON_DEMAND" or
    "FINAL". This field defaults to "ON_DEMAND" and is ignored, when specified
    for insert requests.

    Values:
      SQL_BACKUP_RUN_TYPE_UNSPECIFIED: This is an unknown BackupRun type.
      AUTOMATED: The backup schedule automatically triggers a backup.
      ON_DEMAND: The user manually triggers a backup.
      FINAL: The backup created when instance is deleted.
    """
    SQL_BACKUP_RUN_TYPE_UNSPECIFIED = 0
    AUTOMATED = 1
    ON_DEMAND = 2
    FINAL = 3

  backupDatabaseInstalledVersion = _messages.StringField(1)
  backupKind = _messages.EnumField('BackupKindValueValuesEnum', 2)
  description = _messages.StringField(3)
  diskEncryptionConfiguration = _messages.MessageField('DiskEncryptionConfiguration', 4)
  diskEncryptionStatus = _messages.MessageField('DiskEncryptionStatus', 5)
  endTime = _messages.StringField(6)
  enqueuedTime = _messages.StringField(7)
  error = _messages.MessageField('OperationError', 8)
  id = _messages.IntegerField(9)
  instance = _messages.StringField(10)
  kind = _messages.StringField(11)
  location = _messages.StringField(12)
  selfLink = _messages.StringField(13)
  startTime = _messages.StringField(14)
  status = _messages.EnumField('StatusValueValuesEnum', 15)
  timeZone = _messages.StringField(16)
  type = _messages.EnumField('TypeValueValuesEnum', 17)
  windowStartTime = _messages.StringField(18)


class BackupRunsListResponse(_messages.Message):
  r"""Backup run list results.

  Fields:
    items: A list of backup runs in reverse chronological order of the
      enqueued time.
    kind: This is always `sql#backupRunsList`.
    nextPageToken: The continuation token, used to page through large result
      sets. Provide this value in a subsequent request to return the next page
      of results.
  """

  items = _messages.MessageField('BackupRun', 1, repeated=True)
  kind = _messages.StringField(2)
  nextPageToken = _messages.StringField(3)


class BinLogCoordinates(_messages.Message):
  r"""Binary log coordinates.

  Fields:
    binLogFileName: Name of the binary log file for a Cloud SQL instance.
    binLogPosition: Position (offset) within the binary log file.
    kind: This is always `sql#binLogCoordinates`.
  """

  binLogFileName = _messages.StringField(1)
  binLogPosition = _messages.IntegerField(2)
  kind = _messages.StringField(3)


class CloneContext(_messages.Message):
  r"""Database instance clone context.

  Fields:
    allocatedIpRange: The name of the allocated ip range for the private ip
      Cloud SQL instance. For example: "google-managed-services-default". If
      set, the cloned instance ip will be created in the allocated range. The
      range name must comply with [RFC
      1035](https://tools.ietf.org/html/rfc1035). Specifically, the name must
      be 1-63 characters long and match the regular expression
      [a-z]([-a-z0-9]*[a-z0-9])?. Reserved for future use.
    binLogCoordinates: Binary log coordinates, if specified, identify the
      position up to which the source instance is cloned. If not specified,
      the source instance is cloned up to the most recent binary log
      coordinates.
    databaseNames: (SQL Server only) Clone only the specified databases from
      the source instance. Clone all databases if empty.
    destinationInstanceName: Name of the Cloud SQL instance to be created as a
      clone.
    kind: This is always `sql#cloneContext`.
    pitrTimestampMs: Reserved for future use.
    pointInTime: Timestamp, if specified, identifies the time to which the
      source instance is cloned.
    preferredZone: Optional. (Point-in-time recovery for PostgreSQL only)
      Clone to an instance in the specified zone. If no zone is specified,
      clone to the same zone as the source instance.
  """

  allocatedIpRange = _messages.StringField(1)
  binLogCoordinates = _messages.MessageField('BinLogCoordinates', 2)
  databaseNames = _messages.StringField(3, repeated=True)
  destinationInstanceName = _messages.StringField(4)
  kind = _messages.StringField(5)
  pitrTimestampMs = _messages.IntegerField(6)
  pointInTime = _messages.StringField(7)
  preferredZone = _messages.StringField(8)


class ConnectSettings(_messages.Message):
  r"""Connect settings retrieval response.

  Enums:
    BackendTypeValueValuesEnum: `SECOND_GEN`: Cloud SQL database instance.
      `EXTERNAL`: A database server that is not managed by Google. This
      property is read-only; use the `tier` property in the `settings` object
      to determine the database type.
    DatabaseVersionValueValuesEnum: The database engine type and version. The
      `databaseVersion` field cannot be changed after instance creation. MySQL
      instances: `MYSQL_8_0`, `MYSQL_5_7` (default), or `MYSQL_5_6`.
      PostgreSQL instances: `POSTGRES_9_6`, `POSTGRES_10`, `POSTGRES_11` or
      `POSTGRES_12` (default), `POSTGRES_13`, or `POSTGRES_14`. SQL Server
      instances: `SQLSERVER_2017_STANDARD` (default),
      `SQLSERVER_2017_ENTERPRISE`, `SQLSERVER_2017_EXPRESS`,
      `SQLSERVER_2017_WEB`, `SQLSERVER_2019_STANDARD`,
      `SQLSERVER_2019_ENTERPRISE`, `SQLSERVER_2019_EXPRESS`, or
      `SQLSERVER_2019_WEB`.

  Fields:
    backendType: `SECOND_GEN`: Cloud SQL database instance. `EXTERNAL`: A
      database server that is not managed by Google. This property is read-
      only; use the `tier` property in the `settings` object to determine the
      database type.
    databaseVersion: The database engine type and version. The
      `databaseVersion` field cannot be changed after instance creation. MySQL
      instances: `MYSQL_8_0`, `MYSQL_5_7` (default), or `MYSQL_5_6`.
      PostgreSQL instances: `POSTGRES_9_6`, `POSTGRES_10`, `POSTGRES_11` or
      `POSTGRES_12` (default), `POSTGRES_13`, or `POSTGRES_14`. SQL Server
      instances: `SQLSERVER_2017_STANDARD` (default),
      `SQLSERVER_2017_ENTERPRISE`, `SQLSERVER_2017_EXPRESS`,
      `SQLSERVER_2017_WEB`, `SQLSERVER_2019_STANDARD`,
      `SQLSERVER_2019_ENTERPRISE`, `SQLSERVER_2019_EXPRESS`, or
      `SQLSERVER_2019_WEB`.
    dnsName: The dns name of the instance.
    ipAddresses: The assigned IP addresses for the instance.
    kind: This is always `sql#connectSettings`.
    pscEnabled: Whether PSC connectivity is enabled for this instance.
    region: The cloud region for the instance. e.g. `us-central1`, `europe-
      west1`. The region cannot be changed after instance creation.
    serverCaCert: SSL configuration.
  """

  class BackendTypeValueValuesEnum(_messages.Enum):
    r"""`SECOND_GEN`: Cloud SQL database instance. `EXTERNAL`: A database
    server that is not managed by Google. This property is read-only; use the
    `tier` property in the `settings` object to determine the database type.

    Values:
      SQL_BACKEND_TYPE_UNSPECIFIED: This is an unknown backend type for
        instance.
      FIRST_GEN: V1 speckle instance.
      SECOND_GEN: V2 speckle instance.
      EXTERNAL: On premises instance.
    """
    SQL_BACKEND_TYPE_UNSPECIFIED = 0
    FIRST_GEN = 1
    SECOND_GEN = 2
    EXTERNAL = 3

  class DatabaseVersionValueValuesEnum(_messages.Enum):
    r"""The database engine type and version. The `databaseVersion` field
    cannot be changed after instance creation. MySQL instances: `MYSQL_8_0`,
    `MYSQL_5_7` (default), or `MYSQL_5_6`. PostgreSQL instances:
    `POSTGRES_9_6`, `POSTGRES_10`, `POSTGRES_11` or `POSTGRES_12` (default),
    `POSTGRES_13`, or `POSTGRES_14`. SQL Server instances:
    `SQLSERVER_2017_STANDARD` (default), `SQLSERVER_2017_ENTERPRISE`,
    `SQLSERVER_2017_EXPRESS`, `SQLSERVER_2017_WEB`, `SQLSERVER_2019_STANDARD`,
    `SQLSERVER_2019_ENTERPRISE`, `SQLSERVER_2019_EXPRESS`, or
    `SQLSERVER_2019_WEB`.

    Values:
      SQL_DATABASE_VERSION_UNSPECIFIED: This is an unknown database version.
      MYSQL_5_1: The database version is MySQL 5.1.
      MYSQL_5_5: The database version is MySQL 5.5.
      MYSQL_5_6: The database version is MySQL 5.6.
      MYSQL_5_7: The database version is MySQL 5.7.
      SQLSERVER_2017_STANDARD: The database version is SQL Server 2017
        Standard.
      SQLSERVER_2017_ENTERPRISE: The database version is SQL Server 2017
        Enterprise.
      SQLSERVER_2017_EXPRESS: The database version is SQL Server 2017 Express.
      SQLSERVER_2017_WEB: The database version is SQL Server 2017 Web.
      POSTGRES_9_6: The database version is PostgreSQL 9.6.
      POSTGRES_10: The database version is PostgreSQL 10.
      POSTGRES_11: The database version is PostgreSQL 11.
      POSTGRES_12: The database version is PostgreSQL 12.
      POSTGRES_13: The database version is PostgreSQL 13.
      POSTGRES_14: The database version is PostgreSQL 14.
      POSTGRES_15: The database version is PostgreSQL 15.
      MYSQL_8_0: The database version is MySQL 8.
      MYSQL_8_0_18: The database major version is MySQL 8.0 and the minor
        version is 18.
      MYSQL_8_0_26: The database major version is MySQL 8.0 and the minor
        version is 26.
      MYSQL_8_0_27: The database major version is MySQL 8.0 and the minor
        version is 27.
      MYSQL_8_0_28: The database major version is MySQL 8.0 and the minor
        version is 28.
      MYSQL_8_0_29: The database major version is MySQL 8.0 and the minor
        version is 29.
      MYSQL_8_0_30: The database major version is MySQL 8.0 and the minor
        version is 30.
      MYSQL_8_0_31: The database major version is MySQL 8.0 and the minor
        version is 31.
      MYSQL_8_0_32: The database major version is MySQL 8.0 and the minor
        version is 32.
      MYSQL_8_0_33: The database major version is MySQL 8.0 and the minor
        version is 33.
      MYSQL_8_0_34: The database major version is MySQL 8.0 and the minor
        version is 34.
      MYSQL_8_0_35: The database major version is MySQL 8.0 and the minor
        version is 35.
      MYSQL_8_0_36: The database major version is MySQL 8.0 and the minor
        version is 36.
      SQLSERVER_2019_STANDARD: The database version is SQL Server 2019
        Standard.
      SQLSERVER_2019_ENTERPRISE: The database version is SQL Server 2019
        Enterprise.
      SQLSERVER_2019_EXPRESS: The database version is SQL Server 2019 Express.
      SQLSERVER_2019_WEB: The database version is SQL Server 2019 Web.
      SQLSERVER_2022_STANDARD: The database version is SQL Server 2022
        Standard.
      SQLSERVER_2022_ENTERPRISE: The database version is SQL Server 2022
        Enterprise.
      SQLSERVER_2022_EXPRESS: The database version is SQL Server 2022 Express.
      SQLSERVER_2022_WEB: The database version is SQL Server 2022 Web.
    """
    SQL_DATABASE_VERSION_UNSPECIFIED = 0
    MYSQL_5_1 = 1
    MYSQL_5_5 = 2
    MYSQL_5_6 = 3
    MYSQL_5_7 = 4
    SQLSERVER_2017_STANDARD = 5
    SQLSERVER_2017_ENTERPRISE = 6
    SQLSERVER_2017_EXPRESS = 7
    SQLSERVER_2017_WEB = 8
    POSTGRES_9_6 = 9
    POSTGRES_10 = 10
    POSTGRES_11 = 11
    POSTGRES_12 = 12
    POSTGRES_13 = 13
    POSTGRES_14 = 14
    POSTGRES_15 = 15
    MYSQL_8_0 = 16
    MYSQL_8_0_18 = 17
    MYSQL_8_0_26 = 18
    MYSQL_8_0_27 = 19
    MYSQL_8_0_28 = 20
    MYSQL_8_0_29 = 21
    MYSQL_8_0_30 = 22
    MYSQL_8_0_31 = 23
    MYSQL_8_0_32 = 24
    MYSQL_8_0_33 = 25
    MYSQL_8_0_34 = 26
    MYSQL_8_0_35 = 27
    MYSQL_8_0_36 = 28
    SQLSERVER_2019_STANDARD = 29
    SQLSERVER_2019_ENTERPRISE = 30
    SQLSERVER_2019_EXPRESS = 31
    SQLSERVER_2019_WEB = 32
    SQLSERVER_2022_STANDARD = 33
    SQLSERVER_2022_ENTERPRISE = 34
    SQLSERVER_2022_EXPRESS = 35
    SQLSERVER_2022_WEB = 36

  backendType = _messages.EnumField('BackendTypeValueValuesEnum', 1)
  databaseVersion = _messages.EnumField('DatabaseVersionValueValuesEnum', 2)
  dnsName = _messages.StringField(3)
  ipAddresses = _messages.MessageField('IpMapping', 4, repeated=True)
  kind = _messages.StringField(5)
  pscEnabled = _messages.BooleanField(6)
  region = _messages.StringField(7)
  serverCaCert = _messages.MessageField('SslCert', 8)


class DataCacheConfig(_messages.Message):
  r"""Data cache configurations.

  Fields:
    dataCacheEnabled: Whether data cache is enabled for the instance.
  """

  dataCacheEnabled = _messages.BooleanField(1)


class Database(_messages.Message):
  r"""Represents a SQL database on the Cloud SQL instance.

  Fields:
    charset: The Cloud SQL charset value.
    collation: The Cloud SQL collation value.
    etag: This field is deprecated and will be removed from a future version
      of the API.
    instance: The name of the Cloud SQL instance. This does not include the
      project ID.
    kind: This is always `sql#database`.
    name: The name of the database in the Cloud SQL instance. This does not
      include the project ID or instance name.
    project: The project ID of the project containing the Cloud SQL database.
      The Google apps domain is prefixed if applicable.
    selfLink: The URI of this resource.
    sqlserverDatabaseDetails: A SqlServerDatabaseDetails attribute.
  """

  charset = _messages.StringField(1)
  collation = _messages.StringField(2)
  etag = _messages.StringField(3)
  instance = _messages.StringField(4)
  kind = _messages.StringField(5)
  name = _messages.StringField(6)
  project = _messages.StringField(7)
  selfLink = _messages.StringField(8)
  sqlserverDatabaseDetails = _messages.MessageField('SqlServerDatabaseDetails', 9)


class DatabaseFlags(_messages.Message):
  r"""Database flags for Cloud SQL instances.

  Fields:
    name: The name of the flag. These flags are passed at instance startup, so
      include both server options and system variables. Flags are specified
      with underscores, not hyphens. For more information, see [Configuring
      Database Flags](https://cloud.google.com/sql/docs/mysql/flags) in the
      Cloud SQL documentation.
    value: The value of the flag. Boolean flags are set to `on` for true and
      `off` for false. This field must be omitted if the flag doesn't take a
      value.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class DatabaseInstance(_messages.Message):
  r"""A Cloud SQL instance resource.

  Enums:
    BackendTypeValueValuesEnum: The backend type. `SECOND_GEN`: Cloud SQL
      database instance. `EXTERNAL`: A database server that is not managed by
      Google. This property is read-only; use the `tier` property in the
      `settings` object to determine the database type.
    DatabaseVersionValueValuesEnum: The database engine type and version. The
      `databaseVersion` field cannot be changed after instance creation.
    InstalledVersionValueValuesEnum: Stores the current database version
      including minor version such as `MYSQL_8_0_18`.
    InstanceTypeValueValuesEnum: The instance type.
    SqlNetworkArchitectureValueValuesEnum:
    StateValueValuesEnum: The current serving state of the Cloud SQL instance.
    SuspensionReasonValueListEntryValuesEnum:

  Messages:
    FailoverReplicaValue: The name and status of the failover replica.

  Fields:
    availableMaintenanceVersions: Output only. List all maintenance versions
      applicable on the instance
    backendType: The backend type. `SECOND_GEN`: Cloud SQL database instance.
      `EXTERNAL`: A database server that is not managed by Google. This
      property is read-only; use the `tier` property in the `settings` object
      to determine the database type.
    connectionName: Connection name of the Cloud SQL instance used in
      connection strings.
    createTime: Output only. The time when the instance was created in [RFC
      3339](https://tools.ietf.org/html/rfc3339) format, for example
      `2012-11-15T16:19:00.094Z`.
    currentDiskSize: The current disk usage of the instance in bytes. This
      property has been deprecated. Use the
      "cloudsql.googleapis.com/database/disk/bytes_used" metric in Cloud
      Monitoring API instead. Please see [this
      announcement](https://groups.google.com/d/msg/google-cloud-sql-
      announce/I_7-F9EBhT0/BtvFtdFeAgAJ) for details.
    databaseInstalledVersion: Output only. Stores the current database version
      running on the instance including minor version such as `MYSQL_8_0_18`.
    databaseVersion: The database engine type and version. The
      `databaseVersion` field cannot be changed after instance creation.
    diskEncryptionConfiguration: Disk encryption configuration specific to an
      instance.
    diskEncryptionStatus: Disk encryption status specific to an instance.
    dnsName: Output only. The dns name of the instance.
    etag: This field is deprecated and will be removed from a future version
      of the API. Use the `settings.settingsVersion` field instead.
    failoverReplica: The name and status of the failover replica.
    gceZone: The Compute Engine zone that the instance is currently serving
      from. This value could be different from the zone that was specified
      when the instance was created if the instance has failed over to its
      secondary zone. WARNING: Changing this might restart the instance.
    installedVersion: Stores the current database version including minor
      version such as `MYSQL_8_0_18`.
    instanceType: The instance type.
    ipAddresses: The assigned IP addresses for the instance.
    ipv6Address: The IPv6 address assigned to the instance. (Deprecated) This
      property was applicable only to First Generation instances.
    kind: This is always `sql#instance`.
    maintenanceVersion: The current software version on the instance.
    masterInstanceName: The name of the instance which will act as primary in
      the replication setup.
    maxDiskSize: The maximum disk size of the instance in bytes.
    name: Name of the Cloud SQL instance. This does not include the project
      ID.
    onPremisesConfiguration: Configuration specific to on-premises instances.
    outOfDiskReport: This field represents the report generated by the
      proactive database wellness job for OutOfDisk issues. * Writers: * the
      proactive database wellness job for OOD. * Readers: * the proactive
      database wellness job
    project: The project ID of the project containing the Cloud SQL instance.
      The Google apps domain is prefixed if applicable.
    pscServiceAttachmentLink: Output only. The link to service attachment of
      PSC instance.
    region: The geographical region. Can be: * `us-central` (`FIRST_GEN`
      instances only) * `us-central1` (`SECOND_GEN` instances only) * `asia-
      east1` or `europe-west1`. Defaults to `us-central` or `us-central1`
      depending on the instance type. The region cannot be changed after
      instance creation.
    replicaConfiguration: Configuration specific to failover replicas and read
      replicas.
    replicaNames: The replicas of the instance.
    rootPassword: Initial root password. Use only on creation. You must set
      root passwords before you can connect to PostgreSQL instances.
    satisfiesPzs: The status indicating if instance satisfiesPzs. Reserved for
      future use.
    scheduledMaintenance: The start time of any upcoming scheduled maintenance
      for this instance.
    secondaryGceZone: The Compute Engine zone that the failover instance is
      currently serving from for a regional instance. This value could be
      different from the zone that was specified when the instance was created
      if the instance has failed over to its secondary/failover zone.
    selfLink: The URI of this resource.
    serverCaCert: SSL configuration.
    serviceAccountEmailAddress: The service account email address assigned to
      the instance. \This property is read-only.
    settings: The user settings.
    sqlNetworkArchitecture: A SqlNetworkArchitectureValueValuesEnum attribute.
    state: The current serving state of the Cloud SQL instance.
    suspensionReason: If the instance state is SUSPENDED, the reason for the
      suspension.
  """

  class BackendTypeValueValuesEnum(_messages.Enum):
    r"""The backend type. `SECOND_GEN`: Cloud SQL database instance.
    `EXTERNAL`: A database server that is not managed by Google. This property
    is read-only; use the `tier` property in the `settings` object to
    determine the database type.

    Values:
      SQL_BACKEND_TYPE_UNSPECIFIED: This is an unknown backend type for
        instance.
      FIRST_GEN: V1 speckle instance.
      SECOND_GEN: V2 speckle instance.
      EXTERNAL: On premises instance.
    """
    SQL_BACKEND_TYPE_UNSPECIFIED = 0
    FIRST_GEN = 1
    SECOND_GEN = 2
    EXTERNAL = 3

  class DatabaseVersionValueValuesEnum(_messages.Enum):
    r"""The database engine type and version. The `databaseVersion` field
    cannot be changed after instance creation.

    Values:
      SQL_DATABASE_VERSION_UNSPECIFIED: This is an unknown database version.
      MYSQL_5_1: The database version is MySQL 5.1.
      MYSQL_5_5: The database version is MySQL 5.5.
      MYSQL_5_6: The database version is MySQL 5.6.
      MYSQL_5_7: The database version is MySQL 5.7.
      SQLSERVER_2017_STANDARD: The database version is SQL Server 2017
        Standard.
      SQLSERVER_2017_ENTERPRISE: The database version is SQL Server 2017
        Enterprise.
      SQLSERVER_2017_EXPRESS: The database version is SQL Server 2017 Express.
      SQLSERVER_2017_WEB: The database version is SQL Server 2017 Web.
      POSTGRES_9_6: The database version is PostgreSQL 9.6.
      POSTGRES_10: The database version is PostgreSQL 10.
      POSTGRES_11: The database version is PostgreSQL 11.
      POSTGRES_12: The database version is PostgreSQL 12.
      POSTGRES_13: The database version is PostgreSQL 13.
      POSTGRES_14: The database version is PostgreSQL 14.
      POSTGRES_15: The database version is PostgreSQL 15.
      MYSQL_8_0: The database version is MySQL 8.
      MYSQL_8_0_18: The database major version is MySQL 8.0 and the minor
        version is 18.
      MYSQL_8_0_26: The database major version is MySQL 8.0 and the minor
        version is 26.
      MYSQL_8_0_27: The database major version is MySQL 8.0 and the minor
        version is 27.
      MYSQL_8_0_28: The database major version is MySQL 8.0 and the minor
        version is 28.
      MYSQL_8_0_29: The database major version is MySQL 8.0 and the minor
        version is 29.
      MYSQL_8_0_30: The database major version is MySQL 8.0 and the minor
        version is 30.
      MYSQL_8_0_31: The database major version is MySQL 8.0 and the minor
        version is 31.
      MYSQL_8_0_32: The database major version is MySQL 8.0 and the minor
        version is 32.
      MYSQL_8_0_33: The database major version is MySQL 8.0 and the minor
        version is 33.
      MYSQL_8_0_34: The database major version is MySQL 8.0 and the minor
        version is 34.
      MYSQL_8_0_35: The database major version is MySQL 8.0 and the minor
        version is 35.
      MYSQL_8_0_36: The database major version is MySQL 8.0 and the minor
        version is 36.
      SQLSERVER_2019_STANDARD: The database version is SQL Server 2019
        Standard.
      SQLSERVER_2019_ENTERPRISE: The database version is SQL Server 2019
        Enterprise.
      SQLSERVER_2019_EXPRESS: The database version is SQL Server 2019 Express.
      SQLSERVER_2019_WEB: The database version is SQL Server 2019 Web.
      SQLSERVER_2022_STANDARD: The database version is SQL Server 2022
        Standard.
      SQLSERVER_2022_ENTERPRISE: The database version is SQL Server 2022
        Enterprise.
      SQLSERVER_2022_EXPRESS: The database version is SQL Server 2022 Express.
      SQLSERVER_2022_WEB: The database version is SQL Server 2022 Web.
    """
    SQL_DATABASE_VERSION_UNSPECIFIED = 0
    MYSQL_5_1 = 1
    MYSQL_5_5 = 2
    MYSQL_5_6 = 3
    MYSQL_5_7 = 4
    SQLSERVER_2017_STANDARD = 5
    SQLSERVER_2017_ENTERPRISE = 6
    SQLSERVER_2017_EXPRESS = 7
    SQLSERVER_2017_WEB = 8
    POSTGRES_9_6 = 9
    POSTGRES_10 = 10
    POSTGRES_11 = 11
    POSTGRES_12 = 12
    POSTGRES_13 = 13
    POSTGRES_14 = 14
    POSTGRES_15 = 15
    MYSQL_8_0 = 16
    MYSQL_8_0_18 = 17
    MYSQL_8_0_26 = 18
    MYSQL_8_0_27 = 19
    MYSQL_8_0_28 = 20
    MYSQL_8_0_29 = 21
    MYSQL_8_0_30 = 22
    MYSQL_8_0_31 = 23
    MYSQL_8_0_32 = 24
    MYSQL_8_0_33 = 25
    MYSQL_8_0_34 = 26
    MYSQL_8_0_35 = 27
    MYSQL_8_0_36 = 28
    SQLSERVER_2019_STANDARD = 29
    SQLSERVER_2019_ENTERPRISE = 30
    SQLSERVER_2019_EXPRESS = 31
    SQLSERVER_2019_WEB = 32
    SQLSERVER_2022_STANDARD = 33
    SQLSERVER_2022_ENTERPRISE = 34
    SQLSERVER_2022_EXPRESS = 35
    SQLSERVER_2022_WEB = 36

  class InstalledVersionValueValuesEnum(_messages.Enum):
    r"""Stores the current database version including minor version such as
    `MYSQL_8_0_18`.

    Values:
      SQL_DATABASE_VERSION_UNSPECIFIED: This is an unknown database version.
      MYSQL_5_1: The database version is MySQL 5.1.
      MYSQL_5_5: The database version is MySQL 5.5.
      MYSQL_5_6: The database version is MySQL 5.6.
      MYSQL_5_7: The database version is MySQL 5.7.
      SQLSERVER_2017_STANDARD: The database version is SQL Server 2017
        Standard.
      SQLSERVER_2017_ENTERPRISE: The database version is SQL Server 2017
        Enterprise.
      SQLSERVER_2017_EXPRESS: The database version is SQL Server 2017 Express.
      SQLSERVER_2017_WEB: The database version is SQL Server 2017 Web.
      POSTGRES_9_6: The database version is PostgreSQL 9.6.
      POSTGRES_10: The database version is PostgreSQL 10.
      POSTGRES_11: The database version is PostgreSQL 11.
      POSTGRES_12: The database version is PostgreSQL 12.
      POSTGRES_13: The database version is PostgreSQL 13.
      POSTGRES_14: The database version is PostgreSQL 14.
      POSTGRES_15: The database version is PostgreSQL 15.
      MYSQL_8_0: The database version is MySQL 8.
      MYSQL_8_0_18: The database major version is MySQL 8.0 and the minor
        version is 18.
      MYSQL_8_0_26: The database major version is MySQL 8.0 and the minor
        version is 26.
      MYSQL_8_0_27: The database major version is MySQL 8.0 and the minor
        version is 27.
      MYSQL_8_0_28: The database major version is MySQL 8.0 and the minor
        version is 28.
      MYSQL_8_0_29: The database major version is MySQL 8.0 and the minor
        version is 29.
      MYSQL_8_0_30: The database major version is MySQL 8.0 and the minor
        version is 30.
      MYSQL_8_0_31: The database major version is MySQL 8.0 and the minor
        version is 31.
      MYSQL_8_0_32: The database major version is MySQL 8.0 and the minor
        version is 32.
      MYSQL_8_0_33: The database major version is MySQL 8.0 and the minor
        version is 33.
      MYSQL_8_0_34: The database major version is MySQL 8.0 and the minor
        version is 34.
      MYSQL_8_0_35: The database major version is MySQL 8.0 and the minor
        version is 35.
      MYSQL_8_0_36: The database major version is MySQL 8.0 and the minor
        version is 36.
      SQLSERVER_2019_STANDARD: The database version is SQL Server 2019
        Standard.
      SQLSERVER_2019_ENTERPRISE: The database version is SQL Server 2019
        Enterprise.
      SQLSERVER_2019_EXPRESS: The database version is SQL Server 2019 Express.
      SQLSERVER_2019_WEB: The database version is SQL Server 2019 Web.
      SQLSERVER_2022_STANDARD: The database version is SQL Server 2022
        Standard.
      SQLSERVER_2022_ENTERPRISE: The database version is SQL Server 2022
        Enterprise.
      SQLSERVER_2022_EXPRESS: The database version is SQL Server 2022 Express.
      SQLSERVER_2022_WEB: The database version is SQL Server 2022 Web.
    """
    SQL_DATABASE_VERSION_UNSPECIFIED = 0
    MYSQL_5_1 = 1
    MYSQL_5_5 = 2
    MYSQL_5_6 = 3
    MYSQL_5_7 = 4
    SQLSERVER_2017_STANDARD = 5
    SQLSERVER_2017_ENTERPRISE = 6
    SQLSERVER_2017_EXPRESS = 7
    SQLSERVER_2017_WEB = 8
    POSTGRES_9_6 = 9
    POSTGRES_10 = 10
    POSTGRES_11 = 11
    POSTGRES_12 = 12
    POSTGRES_13 = 13
    POSTGRES_14 = 14
    POSTGRES_15 = 15
    MYSQL_8_0 = 16
    MYSQL_8_0_18 = 17
    MYSQL_8_0_26 = 18
    MYSQL_8_0_27 = 19
    MYSQL_8_0_28 = 20
    MYSQL_8_0_29 = 21
    MYSQL_8_0_30 = 22
    MYSQL_8_0_31 = 23
    MYSQL_8_0_32 = 24
    MYSQL_8_0_33 = 25
    MYSQL_8_0_34 = 26
    MYSQL_8_0_35 = 27
    MYSQL_8_0_36 = 28
    SQLSERVER_2019_STANDARD = 29
    SQLSERVER_2019_ENTERPRISE = 30
    SQLSERVER_2019_EXPRESS = 31
    SQLSERVER_2019_WEB = 32
    SQLSERVER_2022_STANDARD = 33
    SQLSERVER_2022_ENTERPRISE = 34
    SQLSERVER_2022_EXPRESS = 35
    SQLSERVER_2022_WEB = 36

  class InstanceTypeValueValuesEnum(_messages.Enum):
    r"""The instance type.

    Values:
      SQL_INSTANCE_TYPE_UNSPECIFIED: This is an unknown Cloud SQL instance
        type.
      CLOUD_SQL_INSTANCE: A regular Cloud SQL instance that is not replicating
        from a primary instance.
      ON_PREMISES_INSTANCE: An instance running on the customer's premises
        that is not managed by Cloud SQL.
      READ_REPLICA_INSTANCE: A Cloud SQL instance acting as a read-replica.
    """
    SQL_INSTANCE_TYPE_UNSPECIFIED = 0
    CLOUD_SQL_INSTANCE = 1
    ON_PREMISES_INSTANCE = 2
    READ_REPLICA_INSTANCE = 3

  class SqlNetworkArchitectureValueValuesEnum(_messages.Enum):
    r"""SqlNetworkArchitectureValueValuesEnum enum type.

    Values:
      SQL_NETWORK_ARCHITECTURE_UNSPECIFIED: <no description>
      NEW_NETWORK_ARCHITECTURE: Instance is a Tenancy Unit (TU) instance.
      OLD_NETWORK_ARCHITECTURE: Instance is an Umbrella instance.
    """
    SQL_NETWORK_ARCHITECTURE_UNSPECIFIED = 0
    NEW_NETWORK_ARCHITECTURE = 1
    OLD_NETWORK_ARCHITECTURE = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""The current serving state of the Cloud SQL instance.

    Values:
      SQL_INSTANCE_STATE_UNSPECIFIED: The state of the instance is unknown.
      RUNNABLE: The instance is running, or has been stopped by owner.
      SUSPENDED: The instance is not available, for example due to problems
        with billing.
      PENDING_DELETE: The instance is being deleted.
      PENDING_CREATE: The instance is being created.
      MAINTENANCE: The instance is down for maintenance.
      FAILED: The creation of the instance failed or a fatal error occurred
        during maintenance.
      ONLINE_MAINTENANCE: Deprecated
    """
    SQL_INSTANCE_STATE_UNSPECIFIED = 0
    RUNNABLE = 1
    SUSPENDED = 2
    PENDING_DELETE = 3
    PENDING_CREATE = 4
    MAINTENANCE = 5
    FAILED = 6
    ONLINE_MAINTENANCE = 7

  class SuspensionReasonValueListEntryValuesEnum(_messages.Enum):
    r"""SuspensionReasonValueListEntryValuesEnum enum type.

    Values:
      SQL_SUSPENSION_REASON_UNSPECIFIED: This is an unknown suspension reason.
      BILLING_ISSUE: The instance is suspended due to billing issues (for
        example:, GCP account issue)
      LEGAL_ISSUE: The instance is suspended due to illegal content (for
        example:, child pornography, copyrighted material, etc.).
      OPERATIONAL_ISSUE: The instance is causing operational issues (for
        example:, causing the database to crash).
      KMS_KEY_ISSUE: The KMS key used by the instance is either revoked or
        denied access to
    """
    SQL_SUSPENSION_REASON_UNSPECIFIED = 0
    BILLING_ISSUE = 1
    LEGAL_ISSUE = 2
    OPERATIONAL_ISSUE = 3
    KMS_KEY_ISSUE = 4

  class FailoverReplicaValue(_messages.Message):
    r"""The name and status of the failover replica.

    Fields:
      available: The availability status of the failover replica. A false
        status indicates that the failover replica is out of sync. The primary
        instance can only failover to the failover replica when the status is
        true.
      name: The name of the failover replica. If specified at instance
        creation, a failover replica is created for the instance. The name
        doesn't include the project ID.
    """

    available = _messages.BooleanField(1)
    name = _messages.StringField(2)

  availableMaintenanceVersions = _messages.StringField(1, repeated=True)
  backendType = _messages.EnumField('BackendTypeValueValuesEnum', 2)
  connectionName = _messages.StringField(3)
  createTime = _messages.StringField(4)
  currentDiskSize = _messages.IntegerField(5)
  databaseInstalledVersion = _messages.StringField(6)
  databaseVersion = _messages.EnumField('DatabaseVersionValueValuesEnum', 7)
  diskEncryptionConfiguration = _messages.MessageField('DiskEncryptionConfiguration', 8)
  diskEncryptionStatus = _messages.MessageField('DiskEncryptionStatus', 9)
  dnsName = _messages.StringField(10)
  etag = _messages.StringField(11)
  failoverReplica = _messages.MessageField('FailoverReplicaValue', 12)
  gceZone = _messages.StringField(13)
  installedVersion = _messages.EnumField('InstalledVersionValueValuesEnum', 14)
  instanceType = _messages.EnumField('InstanceTypeValueValuesEnum', 15)
  ipAddresses = _messages.MessageField('IpMapping', 16, repeated=True)
  ipv6Address = _messages.StringField(17)
  kind = _messages.StringField(18)
  maintenanceVersion = _messages.StringField(19)
  masterInstanceName = _messages.StringField(20)
  maxDiskSize = _messages.IntegerField(21)
  name = _messages.StringField(22)
  onPremisesConfiguration = _messages.MessageField('OnPremisesConfiguration', 23)
  outOfDiskReport = _messages.MessageField('SqlOutOfDiskReport', 24)
  project = _messages.StringField(25)
  pscServiceAttachmentLink = _messages.StringField(26)
  region = _messages.StringField(27)
  replicaConfiguration = _messages.MessageField('ReplicaConfiguration', 28)
  replicaNames = _messages.StringField(29, repeated=True)
  rootPassword = _messages.StringField(30)
  satisfiesPzs = _messages.BooleanField(31)
  scheduledMaintenance = _messages.MessageField('SqlScheduledMaintenance', 32)
  secondaryGceZone = _messages.StringField(33)
  selfLink = _messages.StringField(34)
  serverCaCert = _messages.MessageField('SslCert', 35)
  serviceAccountEmailAddress = _messages.StringField(36)
  settings = _messages.MessageField('Settings', 37)
  sqlNetworkArchitecture = _messages.EnumField('SqlNetworkArchitectureValueValuesEnum', 38)
  state = _messages.EnumField('StateValueValuesEnum', 39)
  suspensionReason = _messages.EnumField('SuspensionReasonValueListEntryValuesEnum', 40, repeated=True)


class DatabasesListResponse(_messages.Message):
  r"""Database list response.

  Fields:
    items: List of database resources in the instance.
    kind: This is always `sql#databasesList`.
  """

  items = _messages.MessageField('Database', 1, repeated=True)
  kind = _messages.StringField(2)


class DemoteMasterConfiguration(_messages.Message):
  r"""Read-replica configuration for connecting to the on-premises primary
  instance.

  Fields:
    kind: This is always `sql#demoteMasterConfiguration`.
    mysqlReplicaConfiguration: MySQL specific configuration when replicating
      from a MySQL on-premises primary instance. Replication configuration
      information such as the username, password, certificates, and keys are
      not stored in the instance metadata. The configuration information is
      used only to set up the replication connection and is stored by MySQL in
      a file named `master.info` in the data directory.
  """

  kind = _messages.StringField(1)
  mysqlReplicaConfiguration = _messages.MessageField('DemoteMasterMySqlReplicaConfiguration', 2)


class DemoteMasterContext(_messages.Message):
  r"""Database instance demote primary instance context.

  Fields:
    kind: This is always `sql#demoteMasterContext`.
    masterInstanceName: The name of the instance which will act as on-premises
      primary instance in the replication setup.
    replicaConfiguration: Configuration specific to read-replicas replicating
      from the on-premises primary instance.
    skipReplicationSetup: Flag to skip replication setup on the instance.
    verifyGtidConsistency: Verify the GTID consistency for demote operation.
      Default value: `True`. Setting this flag to `false` enables you to
      bypass the GTID consistency check between on-premises primary instance
      and Cloud SQL instance during the demotion operation but also exposes
      you to the risk of future replication failures. Change the value only if
      you know the reason for the GTID divergence and are confident that doing
      so will not cause any replication issues.
  """

  kind = _messages.StringField(1)
  masterInstanceName = _messages.StringField(2)
  replicaConfiguration = _messages.MessageField('DemoteMasterConfiguration', 3)
  skipReplicationSetup = _messages.BooleanField(4)
  verifyGtidConsistency = _messages.BooleanField(5)


class DemoteMasterMySqlReplicaConfiguration(_messages.Message):
  r"""Read-replica configuration specific to MySQL databases.

  Fields:
    caCertificate: PEM representation of the trusted CA's x509 certificate.
    clientCertificate: PEM representation of the replica's x509 certificate.
    clientKey: PEM representation of the replica's private key. The
      corresponsing public key is encoded in the client's certificate. The
      format of the replica's private key can be either PKCS #1 or PKCS #8.
    kind: This is always `sql#demoteMasterMysqlReplicaConfiguration`.
    password: The password for the replication connection.
    username: The username for the replication connection.
  """

  caCertificate = _messages.StringField(1)
  clientCertificate = _messages.StringField(2)
  clientKey = _messages.StringField(3)
  kind = _messages.StringField(4)
  password = _messages.StringField(5)
  username = _messages.StringField(6)


class DenyMaintenancePeriod(_messages.Message):
  r"""Deny Maintenance Periods. This specifies a date range during when all
  CSA rollout will be denied.

  Fields:
    endDate: "deny maintenance period" end date. If the year of the end date
      is empty, the year of the start date also must be empty. In this case,
      it means the deny maintenance period recurs every year. The date is in
      format yyyy-mm-dd i.e., 2020-11-01, or mm-dd, i.e., 11-01
    startDate: "deny maintenance period" start date. If the year of the start
      date is empty, the year of the end date also must be empty. In this
      case, it means the deny maintenance period recurs every year. The date
      is in format yyyy-mm-dd i.e., 2020-11-01, or mm-dd, i.e., 11-01
    time: Time in UTC when the "deny maintenance period" starts on start_date
      and ends on end_date. The time is in format: HH:mm:SS, i.e., 00:00:00
  """

  endDate = _messages.StringField(1)
  startDate = _messages.StringField(2)
  time = _messages.StringField(3)


class DiskEncryptionConfiguration(_messages.Message):
  r"""Disk encryption configuration for an instance.

  Fields:
    kind: This is always `sql#diskEncryptionConfiguration`.
    kmsKeyName: Resource name of KMS key for disk encryption
  """

  kind = _messages.StringField(1)
  kmsKeyName = _messages.StringField(2)


class DiskEncryptionStatus(_messages.Message):
  r"""Disk encryption status for an instance.

  Fields:
    kind: This is always `sql#diskEncryptionStatus`.
    kmsKeyVersionName: KMS key version used to encrypt the Cloud SQL instance
      resource
  """

  kind = _messages.StringField(1)
  kmsKeyVersionName = _messages.StringField(2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ExportContext(_messages.Message):
  r"""Database instance export context.

  Enums:
    FileTypeValueValuesEnum: The file type for the specified uri.

  Messages:
    BakExportOptionsValue: Options for exporting BAK files (SQL Server-only)
    CsvExportOptionsValue: Options for exporting data as CSV. `MySQL` and
      `PostgreSQL` instances only.
    SqlExportOptionsValue: Options for exporting data as SQL statements.

  Fields:
    bakExportOptions: Options for exporting BAK files (SQL Server-only)
    csvExportOptions: Options for exporting data as CSV. `MySQL` and
      `PostgreSQL` instances only.
    databases: Databases to be exported. `MySQL instances:` If `fileType` is
      `SQL` and no database is specified, all databases are exported, except
      for the `mysql` system database. If `fileType` is `CSV`, you can specify
      one database, either by using this property or by using the
      `csvExportOptions.selectQuery` property, which takes precedence over
      this property. `PostgreSQL instances:` You must specify one database to
      be exported. If `fileType` is `CSV`, this database must match the one
      specified in the `csvExportOptions.selectQuery` property. `SQL Server
      instances:` You must specify one database to be exported, and the
      `fileType` must be `BAK`.
    fileType: The file type for the specified uri.
    kind: This is always `sql#exportContext`.
    offload: Option for export offload.
    sqlExportOptions: Options for exporting data as SQL statements.
    uri: The path to the file in Google Cloud Storage where the export will be
      stored. The URI is in the form `gs://bucketName/fileName`. If the file
      already exists, the request succeeds, but the operation fails. If
      `fileType` is `SQL` and the filename ends with .gz, the contents are
      compressed.
  """

  class FileTypeValueValuesEnum(_messages.Enum):
    r"""The file type for the specified uri.

    Values:
      SQL_FILE_TYPE_UNSPECIFIED: Unknown file type.
      SQL: File containing SQL statements.
      CSV: File in CSV format.
      BAK: <no description>
    """
    SQL_FILE_TYPE_UNSPECIFIED = 0
    SQL = 1
    CSV = 2
    BAK = 3

  class BakExportOptionsValue(_messages.Message):
    r"""Options for exporting BAK files (SQL Server-only)

    Enums:
      BakTypeValueValuesEnum: Type of this bak file will be export, FULL or
        DIFF, SQL Server only

    Fields:
      bakType: Type of this bak file will be export, FULL or DIFF, SQL Server
        only
      copyOnly: Deprecated: copy_only is deprecated. Use differential_base
        instead
      differentialBase: Whether or not the backup can be used as a
        differential base copy_only backup can not be served as differential
        base
      stripeCount: Option for specifying how many stripes to use for the
        export. If blank, and the value of the striped field is true, the
        number of stripes is automatically chosen.
      striped: Whether or not the export should be striped.
    """

    class BakTypeValueValuesEnum(_messages.Enum):
      r"""Type of this bak file will be export, FULL or DIFF, SQL Server only

      Values:
        BAK_TYPE_UNSPECIFIED: Default type.
        FULL: Full backup.
        DIFF: Differential backup.
        TLOG: SQL Server Transaction Log
      """
      BAK_TYPE_UNSPECIFIED = 0
      FULL = 1
      DIFF = 2
      TLOG = 3

    bakType = _messages.EnumField('BakTypeValueValuesEnum', 1)
    copyOnly = _messages.BooleanField(2)
    differentialBase = _messages.BooleanField(3)
    stripeCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
    striped = _messages.BooleanField(5)

  class CsvExportOptionsValue(_messages.Message):
    r"""Options for exporting data as CSV. `MySQL` and `PostgreSQL` instances
    only.

    Fields:
      escapeCharacter: Specifies the character that should appear before a
        data character that needs to be escaped.
      fieldsTerminatedBy: Specifies the character that separates columns
        within each row (line) of the file.
      linesTerminatedBy: This is used to separate lines. If a line does not
        contain all fields, the rest of the columns are set to their default
        values.
      quoteCharacter: Specifies the quoting character to be used when a data
        value is quoted.
      selectQuery: The select query used to extract the data.
    """

    escapeCharacter = _messages.StringField(1)
    fieldsTerminatedBy = _messages.StringField(2)
    linesTerminatedBy = _messages.StringField(3)
    quoteCharacter = _messages.StringField(4)
    selectQuery = _messages.StringField(5)

  class SqlExportOptionsValue(_messages.Message):
    r"""Options for exporting data as SQL statements.

    Messages:
      MysqlExportOptionsValue: Options for exporting from MySQL.

    Fields:
      mysqlExportOptions: Options for exporting from MySQL.
      schemaOnly: Export only schemas.
      tables: Tables to export, or that were exported, from the specified
        database. If you specify tables, specify one and only one database.
        For PostgreSQL instances, you can specify only one table.
      threads: The number of threads to use for parallel export.
    """

    class MysqlExportOptionsValue(_messages.Message):
      r"""Options for exporting from MySQL.

      Fields:
        masterData: Option to include SQL statement required to set up
          replication. If set to `1`, the dump file includes a CHANGE MASTER
          TO statement with the binary log coordinates, and --set-gtid-purged
          is set to ON. If set to `2`, the CHANGE MASTER TO statement is
          written as a SQL comment and has no effect. If set to any value
          other than `1`, --set-gtid-purged is set to OFF.
      """

      masterData = _messages.IntegerField(1, variant=_messages.Variant.INT32)

    mysqlExportOptions = _messages.MessageField('MysqlExportOptionsValue', 1)
    schemaOnly = _messages.BooleanField(2)
    tables = _messages.StringField(3, repeated=True)
    threads = _messages.IntegerField(4, variant=_messages.Variant.INT32)

  bakExportOptions = _messages.MessageField('BakExportOptionsValue', 1)
  csvExportOptions = _messages.MessageField('CsvExportOptionsValue', 2)
  databases = _messages.StringField(3, repeated=True)
  fileType = _messages.EnumField('FileTypeValueValuesEnum', 4)
  kind = _messages.StringField(5)
  offload = _messages.BooleanField(6)
  sqlExportOptions = _messages.MessageField('SqlExportOptionsValue', 7)
  uri = _messages.StringField(8)


class FailoverContext(_messages.Message):
  r"""Database instance failover context.

  Fields:
    kind: This is always `sql#failoverContext`.
    settingsVersion: The current settings version of this instance. Request
      will be rejected if this version doesn't match the current settings
      version.
  """

  kind = _messages.StringField(1)
  settingsVersion = _messages.IntegerField(2)


class Flag(_messages.Message):
  r"""A flag resource.

  Enums:
    AppliesToValueListEntryValuesEnum:
    TypeValueValuesEnum: The type of the flag. Flags are typed to being
      `BOOLEAN`, `STRING`, `INTEGER` or `NONE`. `NONE` is used for flags which
      do not take a value, such as `skip_grant_tables`.

  Fields:
    allowedIntValues: Use this field if only certain integers are accepted.
      Can be combined with min_value and max_value to add additional values.
    allowedStringValues: For `STRING` flags, a list of strings that the value
      can be set to.
    appliesTo: The database version this flag applies to. Can be MySQL
      instances: `MYSQL_8_0`, `MYSQL_8_0_18`, `MYSQL_8_0_26`, `MYSQL_5_7`, or
      `MYSQL_5_6`. PostgreSQL instances: `POSTGRES_9_6`, `POSTGRES_10`,
      `POSTGRES_11` or `POSTGRES_12`. SQL Server instances:
      `SQLSERVER_2017_STANDARD`, `SQLSERVER_2017_ENTERPRISE`,
      `SQLSERVER_2017_EXPRESS`, `SQLSERVER_2017_WEB`,
      `SQLSERVER_2019_STANDARD`, `SQLSERVER_2019_ENTERPRISE`,
      `SQLSERVER_2019_EXPRESS`, or `SQLSERVER_2019_WEB`. See [the complete
      list](/sql/docs/mysql/admin-api/rest/v1/SqlDatabaseVersion).
    inBeta: Whether or not the flag is considered in beta.
    kind: This is always `sql#flag`.
    maxValue: For `INTEGER` flags, the maximum allowed value.
    minValue: For `INTEGER` flags, the minimum allowed value.
    name: This is the name of the flag. Flag names always use underscores, not
      hyphens, for example: `max_allowed_packet`
    requiresRestart: Indicates whether changing this flag will trigger a
      database restart. Only applicable to Second Generation instances.
    type: The type of the flag. Flags are typed to being `BOOLEAN`, `STRING`,
      `INTEGER` or `NONE`. `NONE` is used for flags which do not take a value,
      such as `skip_grant_tables`.
  """

  class AppliesToValueListEntryValuesEnum(_messages.Enum):
    r"""AppliesToValueListEntryValuesEnum enum type.

    Values:
      SQL_DATABASE_VERSION_UNSPECIFIED: This is an unknown database version.
      MYSQL_5_1: The database version is MySQL 5.1.
      MYSQL_5_5: The database version is MySQL 5.5.
      MYSQL_5_6: The database version is MySQL 5.6.
      MYSQL_5_7: The database version is MySQL 5.7.
      SQLSERVER_2017_STANDARD: The database version is SQL Server 2017
        Standard.
      SQLSERVER_2017_ENTERPRISE: The database version is SQL Server 2017
        Enterprise.
      SQLSERVER_2017_EXPRESS: The database version is SQL Server 2017 Express.
      SQLSERVER_2017_WEB: The database version is SQL Server 2017 Web.
      POSTGRES_9_6: The database version is PostgreSQL 9.6.
      POSTGRES_10: The database version is PostgreSQL 10.
      POSTGRES_11: The database version is PostgreSQL 11.
      POSTGRES_12: The database version is PostgreSQL 12.
      POSTGRES_13: The database version is PostgreSQL 13.
      POSTGRES_14: The database version is PostgreSQL 14.
      POSTGRES_15: The database version is PostgreSQL 15.
      MYSQL_8_0: The database version is MySQL 8.
      MYSQL_8_0_18: The database major version is MySQL 8.0 and the minor
        version is 18.
      MYSQL_8_0_26: The database major version is MySQL 8.0 and the minor
        version is 26.
      MYSQL_8_0_27: The database major version is MySQL 8.0 and the minor
        version is 27.
      MYSQL_8_0_28: The database major version is MySQL 8.0 and the minor
        version is 28.
      MYSQL_8_0_29: The database major version is MySQL 8.0 and the minor
        version is 29.
      MYSQL_8_0_30: The database major version is MySQL 8.0 and the minor
        version is 30.
      MYSQL_8_0_31: The database major version is MySQL 8.0 and the minor
        version is 31.
      MYSQL_8_0_32: The database major version is MySQL 8.0 and the minor
        version is 32.
      MYSQL_8_0_33: The database major version is MySQL 8.0 and the minor
        version is 33.
      MYSQL_8_0_34: The database major version is MySQL 8.0 and the minor
        version is 34.
      MYSQL_8_0_35: The database major version is MySQL 8.0 and the minor
        version is 35.
      MYSQL_8_0_36: The database major version is MySQL 8.0 and the minor
        version is 36.
      SQLSERVER_2019_STANDARD: The database version is SQL Server 2019
        Standard.
      SQLSERVER_2019_ENTERPRISE: The database version is SQL Server 2019
        Enterprise.
      SQLSERVER_2019_EXPRESS: The database version is SQL Server 2019 Express.
      SQLSERVER_2019_WEB: The database version is SQL Server 2019 Web.
      SQLSERVER_2022_STANDARD: The database version is SQL Server 2022
        Standard.
      SQLSERVER_2022_ENTERPRISE: The database version is SQL Server 2022
        Enterprise.
      SQLSERVER_2022_EXPRESS: The database version is SQL Server 2022 Express.
      SQLSERVER_2022_WEB: The database version is SQL Server 2022 Web.
    """
    SQL_DATABASE_VERSION_UNSPECIFIED = 0
    MYSQL_5_1 = 1
    MYSQL_5_5 = 2
    MYSQL_5_6 = 3
    MYSQL_5_7 = 4
    SQLSERVER_2017_STANDARD = 5
    SQLSERVER_2017_ENTERPRISE = 6
    SQLSERVER_2017_EXPRESS = 7
    SQLSERVER_2017_WEB = 8
    POSTGRES_9_6 = 9
    POSTGRES_10 = 10
    POSTGRES_11 = 11
    POSTGRES_12 = 12
    POSTGRES_13 = 13
    POSTGRES_14 = 14
    POSTGRES_15 = 15
    MYSQL_8_0 = 16
    MYSQL_8_0_18 = 17
    MYSQL_8_0_26 = 18
    MYSQL_8_0_27 = 19
    MYSQL_8_0_28 = 20
    MYSQL_8_0_29 = 21
    MYSQL_8_0_30 = 22
    MYSQL_8_0_31 = 23
    MYSQL_8_0_32 = 24
    MYSQL_8_0_33 = 25
    MYSQL_8_0_34 = 26
    MYSQL_8_0_35 = 27
    MYSQL_8_0_36 = 28
    SQLSERVER_2019_STANDARD = 29
    SQLSERVER_2019_ENTERPRISE = 30
    SQLSERVER_2019_EXPRESS = 31
    SQLSERVER_2019_WEB = 32
    SQLSERVER_2022_STANDARD = 33
    SQLSERVER_2022_ENTERPRISE = 34
    SQLSERVER_2022_EXPRESS = 35
    SQLSERVER_2022_WEB = 36

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of the flag. Flags are typed to being `BOOLEAN`, `STRING`,
    `INTEGER` or `NONE`. `NONE` is used for flags which do not take a value,
    such as `skip_grant_tables`.

    Values:
      SQL_FLAG_TYPE_UNSPECIFIED: This is an unknown flag type.
      BOOLEAN: Boolean type flag.
      STRING: String type flag.
      INTEGER: Integer type flag.
      NONE: Flag type used for a server startup option.
      MYSQL_TIMEZONE_OFFSET: Type introduced specially for MySQL TimeZone
        offset. Accept a string value with the format [-12:59, 13:00].
      FLOAT: Float type flag.
      REPEATED_STRING: Comma-separated list of the strings in a SqlFlagType
        enum.
    """
    SQL_FLAG_TYPE_UNSPECIFIED = 0
    BOOLEAN = 1
    STRING = 2
    INTEGER = 3
    NONE = 4
    MYSQL_TIMEZONE_OFFSET = 5
    FLOAT = 6
    REPEATED_STRING = 7

  allowedIntValues = _messages.IntegerField(1, repeated=True)
  allowedStringValues = _messages.StringField(2, repeated=True)
  appliesTo = _messages.EnumField('AppliesToValueListEntryValuesEnum', 3, repeated=True)
  inBeta = _messages.BooleanField(4)
  kind = _messages.StringField(5)
  maxValue = _messages.IntegerField(6)
  minValue = _messages.IntegerField(7)
  name = _messages.StringField(8)
  requiresRestart = _messages.BooleanField(9)
  type = _messages.EnumField('TypeValueValuesEnum', 10)


class FlagsListResponse(_messages.Message):
  r"""Flags list response.

  Fields:
    items: List of flags.
    kind: This is always `sql#flagsList`.
  """

  items = _messages.MessageField('Flag', 1, repeated=True)
  kind = _messages.StringField(2)


class GenerateEphemeralCertRequest(_messages.Message):
  r"""Ephemeral certificate creation request.

  Fields:
    access_token: Optional. Access token to include in the signed certificate.
    public_key: PEM encoded public key to include in the signed certificate.
    readTime: Optional. Optional snapshot read timestamp to trade freshness
      for performance.
    validDuration: Optional. If set, it will contain the cert valid duration.
  """

  access_token = _messages.StringField(1)
  public_key = _messages.StringField(2)
  readTime = _messages.StringField(3)
  validDuration = _messages.StringField(4)


class GenerateEphemeralCertResponse(_messages.Message):
  r"""Ephemeral certificate creation request.

  Fields:
    ephemeralCert: Generated cert
  """

  ephemeralCert = _messages.MessageField('SslCert', 1)


class ImportContext(_messages.Message):
  r"""Database instance import context.

  Enums:
    FileTypeValueValuesEnum: The file type for the specified uri. * `SQL`: The
      file contains SQL statements. * `CSV`: The file contains CSV data. *
      `BAK`: The file contains backup data for a SQL Server instance.

  Messages:
    BakImportOptionsValue: Import parameters specific to SQL Server .BAK files
    CsvImportOptionsValue: Options for importing data as CSV.
    SqlImportOptionsValue: Options for importing data from SQL statements.

  Fields:
    bakImportOptions: Import parameters specific to SQL Server .BAK files
    csvImportOptions: Options for importing data as CSV.
    database: The target database for the import. If `fileType` is `SQL`, this
      field is required only if the import file does not specify a database,
      and is overridden by any database specification in the import file. If
      `fileType` is `CSV`, one database must be specified.
    fileType: The file type for the specified uri. * `SQL`: The file contains
      SQL statements. * `CSV`: The file contains CSV data. * `BAK`: The file
      contains backup data for a SQL Server instance.
    importUser: The PostgreSQL user for this import operation. PostgreSQL
      instances only.
    kind: This is always `sql#importContext`.
    sqlImportOptions: Options for importing data from SQL statements.
    uri: Path to the import file in Cloud Storage, in the form
      `gs://bucketName/fileName`. Compressed gzip files (.gz) are supported
      when `fileType` is `SQL`. The instance must have write permissions to
      the bucket and read access to the file.
  """

  class FileTypeValueValuesEnum(_messages.Enum):
    r"""The file type for the specified uri. * `SQL`: The file contains SQL
    statements. * `CSV`: The file contains CSV data. * `BAK`: The file
    contains backup data for a SQL Server instance.

    Values:
      SQL_FILE_TYPE_UNSPECIFIED: Unknown file type.
      SQL: File containing SQL statements.
      CSV: File in CSV format.
      BAK: <no description>
    """
    SQL_FILE_TYPE_UNSPECIFIED = 0
    SQL = 1
    CSV = 2
    BAK = 3

  class BakImportOptionsValue(_messages.Message):
    r"""Import parameters specific to SQL Server .BAK files

    Enums:
      BakTypeValueValuesEnum: Type of the bak content, FULL or DIFF.

    Messages:
      EncryptionOptionsValue: A EncryptionOptionsValue object.

    Fields:
      bakType: Type of the bak content, FULL or DIFF.
      encryptionOptions: A EncryptionOptionsValue attribute.
      noRecovery: Whether or not the backup importing will restore database
        with NORECOVERY option Applies only to Cloud SQL for SQL Server.
      recoveryOnly: Whether or not the backup importing request will just
        bring database online without downloading Bak content only one of
        "no_recovery" and "recovery_only" can be true otherwise error will
        return. Applies only to Cloud SQL for SQL Server.
      stopAt: Optional. StopAt keyword for transaction log import, Applies to
        Cloud SQL for SQL Server only
      stopAtMark: Optional. StopAtMark keyword for transaction log import,
        Applies to Cloud SQL for SQL Server only
      striped: Whether or not the backup set being restored is striped.
        Applies only to Cloud SQL for SQL Server.
    """

    class BakTypeValueValuesEnum(_messages.Enum):
      r"""Type of the bak content, FULL or DIFF.

      Values:
        BAK_TYPE_UNSPECIFIED: Default type.
        FULL: Full backup.
        DIFF: Differential backup.
        TLOG: SQL Server Transaction Log
      """
      BAK_TYPE_UNSPECIFIED = 0
      FULL = 1
      DIFF = 2
      TLOG = 3

    class EncryptionOptionsValue(_messages.Message):
      r"""A EncryptionOptionsValue object.

      Fields:
        certPath: Path to the Certificate (.cer) in Cloud Storage, in the form
          `gs://bucketName/fileName`. The instance must have write permissions
          to the bucket and read access to the file.
        pvkPassword: Password that encrypts the private key
        pvkPath: Path to the Certificate Private Key (.pvk) in Cloud Storage,
          in the form `gs://bucketName/fileName`. The instance must have write
          permissions to the bucket and read access to the file.
      """

      certPath = _messages.StringField(1)
      pvkPassword = _messages.StringField(2)
      pvkPath = _messages.StringField(3)

    bakType = _messages.EnumField('BakTypeValueValuesEnum', 1)
    encryptionOptions = _messages.MessageField('EncryptionOptionsValue', 2)
    noRecovery = _messages.BooleanField(3)
    recoveryOnly = _messages.BooleanField(4)
    stopAt = _messages.StringField(5)
    stopAtMark = _messages.StringField(6)
    striped = _messages.BooleanField(7)

  class CsvImportOptionsValue(_messages.Message):
    r"""Options for importing data as CSV.

    Fields:
      columns: The columns to which CSV data is imported. If not specified,
        all columns of the database table are loaded with CSV data.
      escapeCharacter: Specifies the character that should appear before a
        data character that needs to be escaped.
      fieldsTerminatedBy: Specifies the character that separates columns
        within each row (line) of the file.
      linesTerminatedBy: This is used to separate lines. If a line does not
        contain all fields, the rest of the columns are set to their default
        values.
      quoteCharacter: Specifies the quoting character to be used when a data
        value is quoted.
      table: The table to which CSV data is imported.
    """

    columns = _messages.StringField(1, repeated=True)
    escapeCharacter = _messages.StringField(2)
    fieldsTerminatedBy = _messages.StringField(3)
    linesTerminatedBy = _messages.StringField(4)
    quoteCharacter = _messages.StringField(5)
    table = _messages.StringField(6)

  class SqlImportOptionsValue(_messages.Message):
    r"""Options for importing data from SQL statements.

    Fields:
      threads: The number of threads to use for parallel import.
    """

    threads = _messages.IntegerField(1, variant=_messages.Variant.INT32)

  bakImportOptions = _messages.MessageField('BakImportOptionsValue', 1)
  csvImportOptions = _messages.MessageField('CsvImportOptionsValue', 2)
  database = _messages.StringField(3)
  fileType = _messages.EnumField('FileTypeValueValuesEnum', 4)
  importUser = _messages.StringField(5)
  kind = _messages.StringField(6)
  sqlImportOptions = _messages.MessageField('SqlImportOptionsValue', 7)
  uri = _messages.StringField(8)


class InsightsConfig(_messages.Message):
  r"""Insights configuration. This specifies when Cloud SQL Insights feature
  is enabled and optional configuration.

  Fields:
    queryInsightsEnabled: Whether Query Insights feature is enabled.
    queryPlansPerMinute: Number of query execution plans captured by Insights
      per minute for all queries combined. Default is 5.
    queryStringLength: Maximum query length stored in bytes. Default value:
      1024 bytes. Range: 256-4500 bytes. Query length more than this field
      value will be truncated to this value. When unset, query length will be
      the default value. Changing query length will restart the database.
    recordApplicationTags: Whether Query Insights will record application tags
      from query when enabled.
    recordClientAddress: Whether Query Insights will record client address
      when enabled.
  """

  queryInsightsEnabled = _messages.BooleanField(1)
  queryPlansPerMinute = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  queryStringLength = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  recordApplicationTags = _messages.BooleanField(4)
  recordClientAddress = _messages.BooleanField(5)


class InstanceReference(_messages.Message):
  r"""Reference to another Cloud SQL instance.

  Fields:
    name: The name of the Cloud SQL instance being referenced. This does not
      include the project ID.
    project: The project ID of the Cloud SQL instance being referenced. The
      default is the same project ID as the instance references it.
    region: The region of the Cloud SQL instance being referenced.
  """

  name = _messages.StringField(1)
  project = _messages.StringField(2)
  region = _messages.StringField(3)


class InstancesCloneRequest(_messages.Message):
  r"""Database instance clone request.

  Fields:
    cloneContext: Contains details about the clone operation.
  """

  cloneContext = _messages.MessageField('CloneContext', 1)


class InstancesDemoteMasterRequest(_messages.Message):
  r"""Database demote primary instance request.

  Fields:
    demoteMasterContext: Contains details about the demoteMaster operation.
  """

  demoteMasterContext = _messages.MessageField('DemoteMasterContext', 1)


class InstancesExportRequest(_messages.Message):
  r"""Database instance export request.

  Fields:
    exportContext: Contains details about the export operation.
  """

  exportContext = _messages.MessageField('ExportContext', 1)


class InstancesFailoverRequest(_messages.Message):
  r"""Instance failover request.

  Fields:
    failoverContext: Failover Context.
  """

  failoverContext = _messages.MessageField('FailoverContext', 1)


class InstancesImportRequest(_messages.Message):
  r"""Database instance import request.

  Fields:
    importContext: Contains details about the import operation.
  """

  importContext = _messages.MessageField('ImportContext', 1)


class InstancesListResponse(_messages.Message):
  r"""Database instances list response.

  Fields:
    items: List of database instance resources.
    kind: This is always `sql#instancesList`.
    nextPageToken: The continuation token, used to page through large result
      sets. Provide this value in a subsequent request to return the next page
      of results.
    warnings: List of warnings that occurred while handling the request.
  """

  items = _messages.MessageField('DatabaseInstance', 1, repeated=True)
  kind = _messages.StringField(2)
  nextPageToken = _messages.StringField(3)
  warnings = _messages.MessageField('ApiWarning', 4, repeated=True)


class InstancesListServerCasResponse(_messages.Message):
  r"""Instances ListServerCas response.

  Fields:
    activeVersion: A string attribute.
    certs: List of server CA certificates for the instance.
    kind: This is always `sql#instancesListServerCas`.
  """

  activeVersion = _messages.StringField(1)
  certs = _messages.MessageField('SslCert', 2, repeated=True)
  kind = _messages.StringField(3)


class InstancesReencryptRequest(_messages.Message):
  r"""Database Instance reencrypt request.

  Fields:
    backupReencryptionConfig: Configuration specific to backup re-encryption
  """

  backupReencryptionConfig = _messages.MessageField('BackupReencryptionConfig', 1)


class InstancesRestoreBackupRequest(_messages.Message):
  r"""Database instance restore backup request.

  Fields:
    backup: The name of the backup to restore from in following format:
      projects/{project-id}/backups/{backup-uid} Only one of
      restore_backup_context or backup can be passed to the input.
    restoreBackupContext: Parameters required to perform the restore backup
      operation.
    restoreInstanceSettings: Restore instance settings overrides the instance
      settings stored as part of the backup. Instance's major database version
      cannot be changed and the disk size can only be increased. This feature
      is only available for restores to new instances using the backup name.
  """

  backup = _messages.StringField(1)
  restoreBackupContext = _messages.MessageField('RestoreBackupContext', 2)
  restoreInstanceSettings = _messages.MessageField('DatabaseInstance', 3)


class InstancesRotateServerCaRequest(_messages.Message):
  r"""Rotate Server CA request.

  Fields:
    rotateServerCaContext: Contains details about the rotate server CA
      operation.
  """

  rotateServerCaContext = _messages.MessageField('RotateServerCaContext', 1)


class InstancesTruncateLogRequest(_messages.Message):
  r"""Instance truncate log request.

  Fields:
    truncateLogContext: Contains details about the truncate log operation.
  """

  truncateLogContext = _messages.MessageField('TruncateLogContext', 1)


class IpConfiguration(_messages.Message):
  r"""IP Management configuration.

  Fields:
    allocatedIpRange: The name of the allocated ip range for the private ip
      Cloud SQL instance. For example: "google-managed-services-default". If
      set, the instance ip will be created in the allocated range. The range
      name must comply with [RFC 1035](https://tools.ietf.org/html/rfc1035).
      Specifically, the name must be 1-63 characters long and match the
      regular expression `[a-z]([-a-z0-9]*[a-z0-9])?.`
    authorizedNetworks: The list of external networks that are allowed to
      connect to the instance using the IP. In 'CIDR' notation, also known as
      'slash' notation (for example: `*************/24`).
    enablePrivatePathForGoogleCloudServices: Controls connectivity to private
      IP instances from Google services, such as BigQuery.
    ipv4Enabled: Whether the instance is assigned a public IP address or not.
    privateNetwork: The resource link for the VPC network from which the Cloud
      SQL instance is accessible for private IP. For example,
      `/projects/myProject/global/networks/default`. This setting can be
      updated, but it cannot be removed after it is set.
    pscConfig: PSC settings for this instance.
    requireSsl: Whether SSL connections over IP are enforced or not.
    reservedIpRange: This field is deprecated and will be removed from a
      future version of the API.
  """

  allocatedIpRange = _messages.StringField(1)
  authorizedNetworks = _messages.MessageField('AclEntry', 2, repeated=True)
  enablePrivatePathForGoogleCloudServices = _messages.BooleanField(3)
  ipv4Enabled = _messages.BooleanField(4)
  privateNetwork = _messages.StringField(5)
  pscConfig = _messages.MessageField('PscConfig', 6)
  requireSsl = _messages.BooleanField(7)
  reservedIpRange = _messages.StringField(8)


class IpMapping(_messages.Message):
  r"""Database instance IP Mapping.

  Enums:
    TypeValueValuesEnum: The type of this IP address. A `PRIMARY` address is a
      public address that can accept incoming connections. A `PRIVATE` address
      is a private address that can accept incoming connections. An `OUTGOING`
      address is the source address of connections originating from the
      instance, if supported.

  Fields:
    ipAddress: The IP address assigned.
    timeToRetire: The due time for this IP to be retired in [RFC
      3339](https://tools.ietf.org/html/rfc3339) format, for example
      `2012-11-15T16:19:00.094Z`. This field is only available when the IP is
      scheduled to be retired.
    type: The type of this IP address. A `PRIMARY` address is a public address
      that can accept incoming connections. A `PRIVATE` address is a private
      address that can accept incoming connections. An `OUTGOING` address is
      the source address of connections originating from the instance, if
      supported.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of this IP address. A `PRIMARY` address is a public address
    that can accept incoming connections. A `PRIVATE` address is a private
    address that can accept incoming connections. An `OUTGOING` address is the
    source address of connections originating from the instance, if supported.

    Values:
      SQL_IP_ADDRESS_TYPE_UNSPECIFIED: This is an unknown IP address type.
      PRIMARY: IP address the customer is supposed to connect to. Usually this
        is the load balancer's IP address
      OUTGOING: Source IP address of the connection a read replica establishes
        to its external primary instance. This IP address can be allowlisted
        by the customer in case it has a firewall that filters incoming
        connection to its on premises primary instance.
      PRIVATE: Private IP used when using private IPs and network peering.
      MIGRATED_1ST_GEN: V1 IP of a migrated instance. We want the user to
        decommission this IP as soon as the migration is complete. Note: V1
        instances with V1 ip addresses will be counted as PRIMARY.
    """
    SQL_IP_ADDRESS_TYPE_UNSPECIFIED = 0
    PRIMARY = 1
    OUTGOING = 2
    PRIVATE = 3
    MIGRATED_1ST_GEN = 4

  ipAddress = _messages.StringField(1)
  timeToRetire = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class LocationPreference(_messages.Message):
  r"""Preferred location. This specifies where a Cloud SQL instance is
  located. Note that if the preferred location is not available, the instance
  will be located as close as possible within the region. Only one location
  may be specified.

  Fields:
    followGaeApplication: The App Engine application to follow, it must be in
      the same region as the Cloud SQL instance. WARNING: Changing this might
      restart the instance.
    kind: This is always `sql#locationPreference`.
    secondaryZone: The preferred Compute Engine zone for the
      secondary/failover (for example: us-central1-a, us-central1-b, etc.).
    zone: The preferred Compute Engine zone (for example: us-central1-a, us-
      central1-b, etc.). WARNING: Changing this might restart the instance.
  """

  followGaeApplication = _messages.StringField(1)
  kind = _messages.StringField(2)
  secondaryZone = _messages.StringField(3)
  zone = _messages.StringField(4)


class MaintenanceWindow(_messages.Message):
  r"""Maintenance window. This specifies when a Cloud SQL instance is
  restarted for system maintenance purposes.

  Enums:
    UpdateTrackValueValuesEnum: Maintenance timing setting: `canary` (Earlier)
      or `stable` (Later). [Learn
      more](https://cloud.google.com/sql/docs/mysql/instance-
      settings#maintenance-timing-2ndgen).

  Fields:
    day: day of week (1-7), starting on Monday.
    hour: hour of day - 0 to 23.
    kind: This is always `sql#maintenanceWindow`.
    updateTrack: Maintenance timing setting: `canary` (Earlier) or `stable`
      (Later). [Learn more](https://cloud.google.com/sql/docs/mysql/instance-
      settings#maintenance-timing-2ndgen).
  """

  class UpdateTrackValueValuesEnum(_messages.Enum):
    r"""Maintenance timing setting: `canary` (Earlier) or `stable` (Later).
    [Learn more](https://cloud.google.com/sql/docs/mysql/instance-
    settings#maintenance-timing-2ndgen).

    Values:
      SQL_UPDATE_TRACK_UNSPECIFIED: This is an unknown maintenance timing
        preference.
      canary: For instance update that requires a restart, this update track
        indicates your instance prefer to restart for new version early in
        maintenance window.
      stable: For instance update that requires a restart, this update track
        indicates your instance prefer to let Cloud SQL choose the timing of
        restart (within its Maintenance window, if applicable).
    """
    SQL_UPDATE_TRACK_UNSPECIFIED = 0
    canary = 1
    stable = 2

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  hour = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  kind = _messages.StringField(3)
  updateTrack = _messages.EnumField('UpdateTrackValueValuesEnum', 4)


class MySqlReplicaConfiguration(_messages.Message):
  r"""Read-replica configuration specific to MySQL databases.

  Fields:
    caCertificate: PEM representation of the trusted CA's x509 certificate.
    clientCertificate: PEM representation of the replica's x509 certificate.
    clientKey: PEM representation of the replica's private key. The
      corresponsing public key is encoded in the client's certificate.
    connectRetryInterval: Seconds to wait between connect retries. MySQL's
      default is 60 seconds.
    dumpFilePath: Path to a SQL dump file in Google Cloud Storage from which
      the replica instance is to be created. The URI is in the form
      gs://bucketName/fileName. Compressed gzip files (.gz) are also
      supported. Dumps have the binlog co-ordinates from which replication
      begins. This can be accomplished by setting --master-data to 1 when
      using mysqldump.
    kind: This is always `sql#mysqlReplicaConfiguration`.
    masterHeartbeatPeriod: Interval in milliseconds between replication
      heartbeats.
    password: The password for the replication connection.
    sslCipher: A list of permissible ciphers to use for SSL encryption.
    username: The username for the replication connection.
    verifyServerCertificate: Whether or not to check the primary instance's
      Common Name value in the certificate that it sends during the SSL
      handshake.
  """

  caCertificate = _messages.StringField(1)
  clientCertificate = _messages.StringField(2)
  clientKey = _messages.StringField(3)
  connectRetryInterval = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  dumpFilePath = _messages.StringField(5)
  kind = _messages.StringField(6)
  masterHeartbeatPeriod = _messages.IntegerField(7)
  password = _messages.StringField(8)
  sslCipher = _messages.StringField(9)
  username = _messages.StringField(10)
  verifyServerCertificate = _messages.BooleanField(11)


class MySqlSyncConfig(_messages.Message):
  r"""MySQL-specific external server sync settings.

  Fields:
    initialSyncFlags: Flags to use for the initial dump.
  """

  initialSyncFlags = _messages.MessageField('SyncFlags', 1, repeated=True)


class OnPremisesConfiguration(_messages.Message):
  r"""On-premises instance configuration.

  Fields:
    caCertificate: PEM representation of the trusted CA's x509 certificate.
    clientCertificate: PEM representation of the replica's x509 certificate.
    clientKey: PEM representation of the replica's private key. The
      corresponsing public key is encoded in the client's certificate.
    dumpFilePath: The dump file to create the Cloud SQL replica.
    hostPort: The host and port of the on-premises instance in host:port
      format
    kind: This is always `sql#onPremisesConfiguration`.
    password: The password for connecting to on-premises instance.
    sourceInstance: The reference to Cloud SQL instance if the source is Cloud
      SQL.
    username: The username for connecting to on-premises instance.
  """

  caCertificate = _messages.StringField(1)
  clientCertificate = _messages.StringField(2)
  clientKey = _messages.StringField(3)
  dumpFilePath = _messages.StringField(4)
  hostPort = _messages.StringField(5)
  kind = _messages.StringField(6)
  password = _messages.StringField(7)
  sourceInstance = _messages.MessageField('InstanceReference', 8)
  username = _messages.StringField(9)


class Operation(_messages.Message):
  r"""An Operation resource. For successful operations that return an
  Operation resource, only the fields relevant to the operation are populated
  in the resource.

  Enums:
    OperationTypeValueValuesEnum: The type of the operation. Valid values are:
      * `CREATE` * `DELETE` * `UPDATE` * `RESTART` * `IMPORT` * `EXPORT` *
      `BACKUP_VOLUME` * `RESTORE_VOLUME` * `CREATE_USER` * `DELETE_USER` *
      `CREATE_DATABASE` * `DELETE_DATABASE`
    StatusValueValuesEnum: The status of an operation.

  Fields:
    backupContext: The context for backup operation, if applicable.
    endTime: The time this operation finished in UTC timezone in [RFC
      3339](https://tools.ietf.org/html/rfc3339) format, for example
      `2012-11-15T16:19:00.094Z`.
    error: If errors occurred during processing of this operation, this field
      will be populated.
    exportContext: The context for export operation, if applicable.
    importContext: The context for import operation, if applicable.
    insertTime: The time this operation was enqueued in UTC timezone in [RFC
      3339](https://tools.ietf.org/html/rfc3339) format, for example
      `2012-11-15T16:19:00.094Z`.
    kind: This is always `sql#operation`.
    name: An identifier that uniquely identifies the operation. You can use
      this identifier to retrieve the Operations resource that has information
      about the operation.
    operationType: The type of the operation. Valid values are: * `CREATE` *
      `DELETE` * `UPDATE` * `RESTART` * `IMPORT` * `EXPORT` * `BACKUP_VOLUME`
      * `RESTORE_VOLUME` * `CREATE_USER` * `DELETE_USER` * `CREATE_DATABASE` *
      `DELETE_DATABASE`
    selfLink: The URI of this resource.
    startTime: The time this operation actually started in UTC timezone in
      [RFC 3339](https://tools.ietf.org/html/rfc3339) format, for example
      `2012-11-15T16:19:00.094Z`.
    status: The status of an operation.
    targetId: Name of the database instance related to this operation.
    targetLink: A string attribute.
    targetProject: The project ID of the target instance related to this
      operation.
    user: The email address of the user who initiated this operation.
  """

  class OperationTypeValueValuesEnum(_messages.Enum):
    r"""The type of the operation. Valid values are: * `CREATE` * `DELETE` *
    `UPDATE` * `RESTART` * `IMPORT` * `EXPORT` * `BACKUP_VOLUME` *
    `RESTORE_VOLUME` * `CREATE_USER` * `DELETE_USER` * `CREATE_DATABASE` *
    `DELETE_DATABASE`

    Values:
      SQL_OPERATION_TYPE_UNSPECIFIED: Unknown operation type.
      IMPORT: Imports data into a Cloud SQL instance.
      EXPORT: Exports data from a Cloud SQL instance to a Cloud Storage
        bucket.
      CREATE: Creates a new Cloud SQL instance.
      UPDATE: Updates the settings of a Cloud SQL instance.
      DELETE: Deletes a Cloud SQL instance.
      RESTART: Restarts the Cloud SQL instance.
      BACKUP: <no description>
      SNAPSHOT: <no description>
      BACKUP_VOLUME: Performs instance backup.
      DELETE_VOLUME: Deletes an instance backup.
      RESTORE_VOLUME: Restores an instance backup.
      INJECT_USER: Injects a privileged user in mysql for MOB instances.
      CLONE: Clones a Cloud SQL instance.
      STOP_REPLICA: Stops replication on a Cloud SQL read replica instance.
      START_REPLICA: Starts replication on a Cloud SQL read replica instance.
      PROMOTE_REPLICA: Promotes a Cloud SQL replica instance.
      CREATE_REPLICA: Creates a Cloud SQL replica instance.
      CREATE_USER: Creates a new user in a Cloud SQL instance.
      DELETE_USER: Deletes a user from a Cloud SQL instance.
      UPDATE_USER: Updates an existing user in a Cloud SQL instance.
      CREATE_DATABASE: Creates a database in the Cloud SQL instance.
      DELETE_DATABASE: Deletes a database in the Cloud SQL instance.
      UPDATE_DATABASE: Updates a database in the Cloud SQL instance.
      FAILOVER: Performs failover of an HA-enabled Cloud SQL failover replica.
      DELETE_BACKUP: Deletes the backup taken by a backup run.
      RECREATE_REPLICA: <no description>
      TRUNCATE_LOG: Truncates a general or slow log table in MySQL.
      DEMOTE_MASTER: Demotes the stand-alone instance to be a Cloud SQL read
        replica for an external database server.
      MAINTENANCE: Indicates that the instance is currently in maintenance.
        Maintenance typically causes the instance to be unavailable for 1-3
        minutes.
      ENABLE_PRIVATE_IP: This field is deprecated, and will be removed in
        future version of API.
      DEFER_MAINTENANCE: <no description>
      CREATE_CLONE: Creates clone instance.
      RESCHEDULE_MAINTENANCE: Reschedule maintenance to another time.
      START_EXTERNAL_SYNC: Starts external sync of a Cloud SQL EM replica to
        an external primary instance.
      LOG_CLEANUP: Recovers logs from an instance's old data disk.
      AUTO_RESTART: Performs auto-restart of an HA-enabled Cloud SQL database
        for auto recovery.
      REENCRYPT: Re-encrypts CMEK instances with latest key version.
      SWITCHOVER: Switches over to replica instance from primary.
      UPDATE_BACKUP: Update a backup.
    """
    SQL_OPERATION_TYPE_UNSPECIFIED = 0
    IMPORT = 1
    EXPORT = 2
    CREATE = 3
    UPDATE = 4
    DELETE = 5
    RESTART = 6
    BACKUP = 7
    SNAPSHOT = 8
    BACKUP_VOLUME = 9
    DELETE_VOLUME = 10
    RESTORE_VOLUME = 11
    INJECT_USER = 12
    CLONE = 13
    STOP_REPLICA = 14
    START_REPLICA = 15
    PROMOTE_REPLICA = 16
    CREATE_REPLICA = 17
    CREATE_USER = 18
    DELETE_USER = 19
    UPDATE_USER = 20
    CREATE_DATABASE = 21
    DELETE_DATABASE = 22
    UPDATE_DATABASE = 23
    FAILOVER = 24
    DELETE_BACKUP = 25
    RECREATE_REPLICA = 26
    TRUNCATE_LOG = 27
    DEMOTE_MASTER = 28
    MAINTENANCE = 29
    ENABLE_PRIVATE_IP = 30
    DEFER_MAINTENANCE = 31
    CREATE_CLONE = 32
    RESCHEDULE_MAINTENANCE = 33
    START_EXTERNAL_SYNC = 34
    LOG_CLEANUP = 35
    AUTO_RESTART = 36
    REENCRYPT = 37
    SWITCHOVER = 38
    UPDATE_BACKUP = 39

  class StatusValueValuesEnum(_messages.Enum):
    r"""The status of an operation.

    Values:
      SQL_OPERATION_STATUS_UNSPECIFIED: The state of the operation is unknown.
      PENDING: The operation has been queued, but has not started yet.
      RUNNING: The operation is running.
      DONE: The operation completed.
    """
    SQL_OPERATION_STATUS_UNSPECIFIED = 0
    PENDING = 1
    RUNNING = 2
    DONE = 3

  backupContext = _messages.MessageField('BackupContext', 1)
  endTime = _messages.StringField(2)
  error = _messages.MessageField('OperationErrors', 3)
  exportContext = _messages.MessageField('ExportContext', 4)
  importContext = _messages.MessageField('ImportContext', 5)
  insertTime = _messages.StringField(6)
  kind = _messages.StringField(7)
  name = _messages.StringField(8)
  operationType = _messages.EnumField('OperationTypeValueValuesEnum', 9)
  selfLink = _messages.StringField(10)
  startTime = _messages.StringField(11)
  status = _messages.EnumField('StatusValueValuesEnum', 12)
  targetId = _messages.StringField(13)
  targetLink = _messages.StringField(14)
  targetProject = _messages.StringField(15)
  user = _messages.StringField(16)


class OperationError(_messages.Message):
  r"""Database instance operation error.

  Fields:
    code: Identifies the specific error that occurred.
    kind: This is always `sql#operationError`.
    message: Additional information about the error encountered.
  """

  code = _messages.StringField(1)
  kind = _messages.StringField(2)
  message = _messages.StringField(3)


class OperationErrors(_messages.Message):
  r"""Database instance operation errors list wrapper.

  Fields:
    errors: The list of errors encountered while processing this operation.
    kind: This is always `sql#operationErrors`.
  """

  errors = _messages.MessageField('OperationError', 1, repeated=True)
  kind = _messages.StringField(2)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    cancelRequested: Output only. Identifies whether the user has requested
      cancellation of the operation. Operations that have been cancelled
      successfully have Operation.error value with a google.rpc.Status.code of
      1, corresponding to `Code.CANCELLED`.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    statusDetail: Output only. Human-readable status of the operation, if any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  statusDetail = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class OperationsListResponse(_messages.Message):
  r"""Operations list response.

  Fields:
    items: List of operation resources.
    kind: This is always `sql#operationsList`.
    nextPageToken: The continuation token, used to page through large result
      sets. Provide this value in a subsequent request to return the next page
      of results.
  """

  items = _messages.MessageField('Operation', 1, repeated=True)
  kind = _messages.StringField(2)
  nextPageToken = _messages.StringField(3)


class PasswordStatus(_messages.Message):
  r"""Read-only password status.

  Fields:
    locked: If true, user does not have login privileges.
    passwordExpirationTime: The expiration time of the current password.
  """

  locked = _messages.BooleanField(1)
  passwordExpirationTime = _messages.StringField(2)


class PasswordValidationPolicy(_messages.Message):
  r"""Database instance local user password validation policy

  Enums:
    ComplexityValueValuesEnum: The complexity of the password.

  Fields:
    complexity: The complexity of the password.
    disallowCompromisedCredentials: Disallow credentials that have been
      compromised by a data breach. This flag is only supported for MySQL.
    disallowUsernameSubstring: Disallow username as a part of the password.
    enablePasswordPolicy: Whether the password policy is enabled or not.
    minLength: Minimum number of characters allowed.
    passwordChangeInterval: Minimum interval after which the password can be
      changed. This flag is only supported for PostgreSQL.
    reuseInterval: Number of previous passwords that cannot be reused.
  """

  class ComplexityValueValuesEnum(_messages.Enum):
    r"""The complexity of the password.

    Values:
      COMPLEXITY_UNSPECIFIED: Complexity check is not specified.
      COMPLEXITY_DEFAULT: A combination of lowercase, uppercase, numeric, and
        non-alphanumeric characters.
    """
    COMPLEXITY_UNSPECIFIED = 0
    COMPLEXITY_DEFAULT = 1

  complexity = _messages.EnumField('ComplexityValueValuesEnum', 1)
  disallowCompromisedCredentials = _messages.BooleanField(2)
  disallowUsernameSubstring = _messages.BooleanField(3)
  enablePasswordPolicy = _messages.BooleanField(4)
  minLength = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  passwordChangeInterval = _messages.StringField(6)
  reuseInterval = _messages.IntegerField(7, variant=_messages.Variant.INT32)


class PerformDiskShrinkContext(_messages.Message):
  r"""Perform disk shrink context.

  Fields:
    targetSizeGb: The target disk shrink size in GigaBytes.
  """

  targetSizeGb = _messages.IntegerField(1)


class PscConfig(_messages.Message):
  r"""PSC settings for a Cloud SQL instance.

  Fields:
    allowedConsumerProjects: List of consumer projects that are allow-listed
      for PSC connections to this instance. This instance can be connected to
      with PSC from any network in these projects. Each consumer project in
      this list may be represented by a project number (numeric) or by a
      project id (alphanumeric).
    pscEnabled: Whether PSC connectivity is enabled for this instance.
  """

  allowedConsumerProjects = _messages.StringField(1, repeated=True)
  pscEnabled = _messages.BooleanField(2)


class ReplicaConfiguration(_messages.Message):
  r"""Read-replica configuration for connecting to the primary instance.

  Fields:
    cascadableReplica: Optional. Specifies if a SQL Server replica is a
      cascadable replica. A cascadable replica is a SQL Server cross region
      replica that supports replica(s) under it.
    failoverTarget: Specifies if the replica is the failover target. If the
      field is set to `true` the replica will be designated as a failover
      replica. In case the primary instance fails, the replica instance will
      be promoted as the new primary instance. Only one replica can be
      specified as failover target, and the replica has to be in different
      zone with the primary instance.
    kind: This is always `sql#replicaConfiguration`.
    mysqlReplicaConfiguration: MySQL specific configuration when replicating
      from a MySQL on-premises primary instance. Replication configuration
      information such as the username, password, certificates, and keys are
      not stored in the instance metadata. The configuration information is
      used only to set up the replication connection and is stored by MySQL in
      a file named `master.info` in the data directory.
  """

  cascadableReplica = _messages.BooleanField(1)
  failoverTarget = _messages.BooleanField(2)
  kind = _messages.StringField(3)
  mysqlReplicaConfiguration = _messages.MessageField('MySqlReplicaConfiguration', 4)


class Reschedule(_messages.Message):
  r"""A Reschedule object.

  Enums:
    RescheduleTypeValueValuesEnum: Required. The type of the reschedule.

  Fields:
    rescheduleType: Required. The type of the reschedule.
    scheduleTime: Optional. Timestamp when the maintenance shall be
      rescheduled to if reschedule_type=SPECIFIC_TIME, in [RFC
      3339](https://tools.ietf.org/html/rfc3339) format, for example
      `2012-11-15T16:19:00.094Z`.
  """

  class RescheduleTypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of the reschedule.

    Values:
      RESCHEDULE_TYPE_UNSPECIFIED: <no description>
      IMMEDIATE: Reschedules maintenance to happen now (within 5 minutes).
      NEXT_AVAILABLE_WINDOW: Reschedules maintenance to occur within one week
        from the originally scheduled day and time.
      SPECIFIC_TIME: Reschedules maintenance to a specific time and day.
    """
    RESCHEDULE_TYPE_UNSPECIFIED = 0
    IMMEDIATE = 1
    NEXT_AVAILABLE_WINDOW = 2
    SPECIFIC_TIME = 3

  rescheduleType = _messages.EnumField('RescheduleTypeValueValuesEnum', 1)
  scheduleTime = _messages.StringField(2)


class RestoreBackupContext(_messages.Message):
  r"""Database instance restore from backup context. Backup context contains
  source instance id and project id.

  Fields:
    backupRunId: The ID of the backup run to restore from.
    instanceId: The ID of the instance that the backup was taken from.
    kind: This is always `sql#restoreBackupContext`.
    project: The full project ID of the source instance.
  """

  backupRunId = _messages.IntegerField(1)
  instanceId = _messages.StringField(2)
  kind = _messages.StringField(3)
  project = _messages.StringField(4)


class RotateServerCaContext(_messages.Message):
  r"""Instance rotate server CA context.

  Fields:
    kind: This is always `sql#rotateServerCaContext`.
    nextVersion: The fingerprint of the next version to be rotated to. If left
      unspecified, will be rotated to the most recently added server CA
      version.
  """

  kind = _messages.StringField(1)
  nextVersion = _messages.StringField(2)


class Settings(_messages.Message):
  r"""Database instance settings.

  Enums:
    ActivationPolicyValueValuesEnum: The activation policy specifies when the
      instance is activated; it is applicable only when the instance state is
      RUNNABLE. Valid values: * `ALWAYS`: The instance is on, and remains so
      even in the absence of connection requests. * `NEVER`: The instance is
      off; it is not activated, even if a connection request arrives.
    AvailabilityTypeValueValuesEnum: Availability type. Potential values: *
      `ZONAL`: The instance serves data from only one zone. Outages in that
      zone affect data accessibility. * `REGIONAL`: The instance can serve
      data from more than one zone in a region (it is highly available)./ For
      more information, see [Overview of the High Availability
      Configuration](https://cloud.google.com/sql/docs/mysql/high-
      availability).
    ConnectorEnforcementValueValuesEnum: Specifies if connections must use
      Cloud SQL connectors. Option values include the following:
      `NOT_REQUIRED` (Cloud SQL instances can be connected without Cloud SQL
      Connectors) and `REQUIRED` (Only allow connections that use Cloud SQL
      Connectors) Note that using REQUIRED disables all existing authorized
      networks. If this field is not specified when creating a new instance,
      NOT_REQUIRED is used. If this field is not specified when patching or
      updating an existing instance, it is left unchanged in the instance.
    DataDiskTypeValueValuesEnum: The type of data disk: `PD_SSD` (default) or
      `PD_HDD`. Not used for First Generation instances.
    EditionValueValuesEnum: Optional. The edition of the instance.
    PricingPlanValueValuesEnum: The pricing plan for this instance. This can
      be either `PER_USE` or `PACKAGE`. Only `PER_USE` is supported for Second
      Generation instances.
    ReplicationTypeValueValuesEnum: The type of replication this instance
      uses. This can be either `ASYNCHRONOUS` or `SYNCHRONOUS`. (Deprecated)
      This property was only applicable to First Generation instances.

  Messages:
    UserLabelsValue: User-provided labels, represented as a dictionary where
      each label is a single key value pair.

  Fields:
    activationPolicy: The activation policy specifies when the instance is
      activated; it is applicable only when the instance state is RUNNABLE.
      Valid values: * `ALWAYS`: The instance is on, and remains so even in the
      absence of connection requests. * `NEVER`: The instance is off; it is
      not activated, even if a connection request arrives.
    activeDirectoryConfig: Active Directory configuration, relevant only for
      Cloud SQL for SQL Server.
    advancedMachineFeatures: Specifies advance machine configuration for the
      instance relevant only for SQL Server.
    authorizedGaeApplications: The App Engine app IDs that can access this
      instance. (Deprecated) Applied to First Generation instances only.
    availabilityType: Availability type. Potential values: * `ZONAL`: The
      instance serves data from only one zone. Outages in that zone affect
      data accessibility. * `REGIONAL`: The instance can serve data from more
      than one zone in a region (it is highly available)./ For more
      information, see [Overview of the High Availability
      Configuration](https://cloud.google.com/sql/docs/mysql/high-
      availability).
    backupConfiguration: The daily backup configuration for the instance.
    collation: The name of server Instance collation.
    connectorEnforcement: Specifies if connections must use Cloud SQL
      connectors. Option values include the following: `NOT_REQUIRED` (Cloud
      SQL instances can be connected without Cloud SQL Connectors) and
      `REQUIRED` (Only allow connections that use Cloud SQL Connectors) Note
      that using REQUIRED disables all existing authorized networks. If this
      field is not specified when creating a new instance, NOT_REQUIRED is
      used. If this field is not specified when patching or updating an
      existing instance, it is left unchanged in the instance.
    crashSafeReplicationEnabled: Configuration specific to read replica
      instances. Indicates whether database flags for crash-safe replication
      are enabled. This property was only applicable to First Generation
      instances.
    dataCacheConfig: Configuration for data cache.
    dataDiskSizeGb: The size of data disk, in GB. The data disk size minimum
      is 10GB.
    dataDiskType: The type of data disk: `PD_SSD` (default) or `PD_HDD`. Not
      used for First Generation instances.
    databaseFlags: The database flags passed to the instance at startup.
    databaseReplicationEnabled: Configuration specific to read replica
      instances. Indicates whether replication is enabled or not. WARNING:
      Changing this restarts the instance.
    deletionProtectionEnabled: Configuration to protect against accidental
      instance deletion.
    denyMaintenancePeriods: Deny maintenance periods
    edition: Optional. The edition of the instance.
    insightsConfig: Insights configuration, for now relevant only for
      Postgres.
    instanceVersion: The current software version the instance is running on.
    ipConfiguration: The settings for IP Management. This allows to enable or
      disable the instance IP and manage which external networks can connect
      to the instance. The IPv4 address cannot be disabled for Second
      Generation instances.
    kind: This is always `sql#settings`.
    locationPreference: The location preference settings. This allows the
      instance to be located as near as possible to either an App Engine app
      or Compute Engine zone for better performance. App Engine co-location
      was only applicable to First Generation instances.
    maintenanceVersion: The current software version on the instance.
    maintenanceWindow: The maintenance window for this instance. This
      specifies when the instance can be restarted for maintenance purposes.
    passwordValidationPolicy: The local user password validation policy of the
      instance.
    pricingPlan: The pricing plan for this instance. This can be either
      `PER_USE` or `PACKAGE`. Only `PER_USE` is supported for Second
      Generation instances.
    recreateReplicasOnPrimaryCrash: Specifies if replicas should automatically
      be recreated on a MySQL primary instance crashes when it is operating in
      low durability mode.
    replicationLagMaxSeconds: Optional. Configuration value for recreation of
      replica after certain replication lag
    replicationType: The type of replication this instance uses. This can be
      either `ASYNCHRONOUS` or `SYNCHRONOUS`. (Deprecated) This property was
      only applicable to First Generation instances.
    settingsVersion: The version of instance settings. This is a required
      field for update method to make sure concurrent updates are handled
      properly. During update, use the most recent settingsVersion value for
      this instance and do not try to update this value.
    sqlServerAuditConfig: SQL Server specific audit configuration.
    storageAutoResize: Configuration to increase storage size automatically.
      The default value is true.
    storageAutoResizeLimit: The maximum size to which storage capacity can be
      automatically increased. The default value is 0, which specifies that
      there is no limit.
    tier: The tier (or machine type) for this instance, for example `db-
      custom-1-3840`. WARNING: Changing this restarts the instance.
    timeZone: Server timezone, relevant only for Cloud SQL for SQL Server.
    userLabels: User-provided labels, represented as a dictionary where each
      label is a single key value pair.
  """

  class ActivationPolicyValueValuesEnum(_messages.Enum):
    r"""The activation policy specifies when the instance is activated; it is
    applicable only when the instance state is RUNNABLE. Valid values: *
    `ALWAYS`: The instance is on, and remains so even in the absence of
    connection requests. * `NEVER`: The instance is off; it is not activated,
    even if a connection request arrives.

    Values:
      SQL_ACTIVATION_POLICY_UNSPECIFIED: Unknown activation plan.
      ALWAYS: The instance is always up and running.
      NEVER: The instance never starts.
      ON_DEMAND: The instance starts upon receiving requests.
    """
    SQL_ACTIVATION_POLICY_UNSPECIFIED = 0
    ALWAYS = 1
    NEVER = 2
    ON_DEMAND = 3

  class AvailabilityTypeValueValuesEnum(_messages.Enum):
    r"""Availability type. Potential values: * `ZONAL`: The instance serves
    data from only one zone. Outages in that zone affect data accessibility. *
    `REGIONAL`: The instance can serve data from more than one zone in a
    region (it is highly available)./ For more information, see [Overview of
    the High Availability
    Configuration](https://cloud.google.com/sql/docs/mysql/high-availability).

    Values:
      SQL_AVAILABILITY_TYPE_UNSPECIFIED: This is an unknown Availability type.
      ZONAL: Zonal available instance.
      REGIONAL: Regional available instance.
    """
    SQL_AVAILABILITY_TYPE_UNSPECIFIED = 0
    ZONAL = 1
    REGIONAL = 2

  class ConnectorEnforcementValueValuesEnum(_messages.Enum):
    r"""Specifies if connections must use Cloud SQL connectors. Option values
    include the following: `NOT_REQUIRED` (Cloud SQL instances can be
    connected without Cloud SQL Connectors) and `REQUIRED` (Only allow
    connections that use Cloud SQL Connectors) Note that using REQUIRED
    disables all existing authorized networks. If this field is not specified
    when creating a new instance, NOT_REQUIRED is used. If this field is not
    specified when patching or updating an existing instance, it is left
    unchanged in the instance.

    Values:
      CONNECTOR_ENFORCEMENT_UNSPECIFIED: The requirement for Cloud SQL
        connectors is unknown.
      NOT_REQUIRED: Do not require Cloud SQL connectors.
      REQUIRED: Require all connections to use Cloud SQL connectors, including
        the Cloud SQL Auth Proxy and Cloud SQL Java, Python, and Go
        connectors. Note: This disables all existing authorized networks.
    """
    CONNECTOR_ENFORCEMENT_UNSPECIFIED = 0
    NOT_REQUIRED = 1
    REQUIRED = 2

  class DataDiskTypeValueValuesEnum(_messages.Enum):
    r"""The type of data disk: `PD_SSD` (default) or `PD_HDD`. Not used for
    First Generation instances.

    Values:
      SQL_DATA_DISK_TYPE_UNSPECIFIED: This is an unknown data disk type.
      PD_SSD: An SSD data disk.
      PD_HDD: An HDD data disk.
      OBSOLETE_LOCAL_SSD: This field is deprecated and will be removed from a
        future version of the API.
    """
    SQL_DATA_DISK_TYPE_UNSPECIFIED = 0
    PD_SSD = 1
    PD_HDD = 2
    OBSOLETE_LOCAL_SSD = 3

  class EditionValueValuesEnum(_messages.Enum):
    r"""Optional. The edition of the instance.

    Values:
      EDITION_UNSPECIFIED: The instance did not specify the edition.
      ENTERPRISE: The instance is an enterprise edition.
      ENTERPRISE_PLUS: The instance is an Enterprise Plus edition.
    """
    EDITION_UNSPECIFIED = 0
    ENTERPRISE = 1
    ENTERPRISE_PLUS = 2

  class PricingPlanValueValuesEnum(_messages.Enum):
    r"""The pricing plan for this instance. This can be either `PER_USE` or
    `PACKAGE`. Only `PER_USE` is supported for Second Generation instances.

    Values:
      SQL_PRICING_PLAN_UNSPECIFIED: This is an unknown pricing plan for this
        instance.
      PACKAGE: The instance is billed at a monthly flat rate.
      PER_USE: The instance is billed per usage.
    """
    SQL_PRICING_PLAN_UNSPECIFIED = 0
    PACKAGE = 1
    PER_USE = 2

  class ReplicationTypeValueValuesEnum(_messages.Enum):
    r"""The type of replication this instance uses. This can be either
    `ASYNCHRONOUS` or `SYNCHRONOUS`. (Deprecated) This property was only
    applicable to First Generation instances.

    Values:
      SQL_REPLICATION_TYPE_UNSPECIFIED: This is an unknown replication type
        for a Cloud SQL instance.
      SYNCHRONOUS: The synchronous replication mode for First Generation
        instances. It is the default value.
      ASYNCHRONOUS: The asynchronous replication mode for First Generation
        instances. It provides a slight performance gain, but if an outage
        occurs while this option is set to asynchronous, you can lose up to a
        few seconds of updates to your data.
    """
    SQL_REPLICATION_TYPE_UNSPECIFIED = 0
    SYNCHRONOUS = 1
    ASYNCHRONOUS = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class UserLabelsValue(_messages.Message):
    r"""User-provided labels, represented as a dictionary where each label is
    a single key value pair.

    Messages:
      AdditionalProperty: An additional property for a UserLabelsValue object.

    Fields:
      additionalProperties: Additional properties of type UserLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a UserLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  activationPolicy = _messages.EnumField('ActivationPolicyValueValuesEnum', 1)
  activeDirectoryConfig = _messages.MessageField('SqlActiveDirectoryConfig', 2)
  advancedMachineFeatures = _messages.MessageField('AdvancedMachineFeatures', 3)
  authorizedGaeApplications = _messages.StringField(4, repeated=True)
  availabilityType = _messages.EnumField('AvailabilityTypeValueValuesEnum', 5)
  backupConfiguration = _messages.MessageField('BackupConfiguration', 6)
  collation = _messages.StringField(7)
  connectorEnforcement = _messages.EnumField('ConnectorEnforcementValueValuesEnum', 8)
  crashSafeReplicationEnabled = _messages.BooleanField(9)
  dataCacheConfig = _messages.MessageField('DataCacheConfig', 10)
  dataDiskSizeGb = _messages.IntegerField(11)
  dataDiskType = _messages.EnumField('DataDiskTypeValueValuesEnum', 12)
  databaseFlags = _messages.MessageField('DatabaseFlags', 13, repeated=True)
  databaseReplicationEnabled = _messages.BooleanField(14)
  deletionProtectionEnabled = _messages.BooleanField(15)
  denyMaintenancePeriods = _messages.MessageField('DenyMaintenancePeriod', 16, repeated=True)
  edition = _messages.EnumField('EditionValueValuesEnum', 17)
  insightsConfig = _messages.MessageField('InsightsConfig', 18)
  instanceVersion = _messages.StringField(19)
  ipConfiguration = _messages.MessageField('IpConfiguration', 20)
  kind = _messages.StringField(21)
  locationPreference = _messages.MessageField('LocationPreference', 22)
  maintenanceVersion = _messages.StringField(23)
  maintenanceWindow = _messages.MessageField('MaintenanceWindow', 24)
  passwordValidationPolicy = _messages.MessageField('PasswordValidationPolicy', 25)
  pricingPlan = _messages.EnumField('PricingPlanValueValuesEnum', 26)
  recreateReplicasOnPrimaryCrash = _messages.BooleanField(27)
  replicationLagMaxSeconds = _messages.IntegerField(28, variant=_messages.Variant.INT32)
  replicationType = _messages.EnumField('ReplicationTypeValueValuesEnum', 29)
  settingsVersion = _messages.IntegerField(30)
  sqlServerAuditConfig = _messages.MessageField('SqlServerAuditConfig', 31)
  storageAutoResize = _messages.BooleanField(32)
  storageAutoResizeLimit = _messages.IntegerField(33)
  tier = _messages.StringField(34)
  timeZone = _messages.StringField(35)
  userLabels = _messages.MessageField('UserLabelsValue', 36)


class SqlActiveDirectoryConfig(_messages.Message):
  r"""Active Directory configuration, relevant only for Cloud SQL for SQL
  Server.

  Fields:
    domain: The name of the domain (e.g., mydomain.com).
    kind: This is always sql#activeDirectoryConfig.
  """

  domain = _messages.StringField(1)
  kind = _messages.StringField(2)


class SqlBackupRunsDeleteRequest(_messages.Message):
  r"""A SqlBackupRunsDeleteRequest object.

  Fields:
    id: The ID of the backup run to delete. To find a backup run ID, use the
      [list](https://cloud.google.com/sql/docs/mysql/admin-
      api/rest/v1beta4/backupRuns/list) method.
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  id = _messages.IntegerField(1, required=True)
  instance = _messages.StringField(2, required=True)
  project = _messages.StringField(3, required=True)


class SqlBackupRunsGetRequest(_messages.Message):
  r"""A SqlBackupRunsGetRequest object.

  Fields:
    id: The ID of this backup run.
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  id = _messages.IntegerField(1, required=True)
  instance = _messages.StringField(2, required=True)
  project = _messages.StringField(3, required=True)


class SqlBackupRunsInsertRequest(_messages.Message):
  r"""A SqlBackupRunsInsertRequest object.

  Fields:
    backupRun: A BackupRun resource to be passed as the request body.
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  backupRun = _messages.MessageField('BackupRun', 1)
  instance = _messages.StringField(2, required=True)
  project = _messages.StringField(3, required=True)


class SqlBackupRunsListRequest(_messages.Message):
  r"""A SqlBackupRunsListRequest object.

  Fields:
    instance: Cloud SQL instance ID, or "-" for all instances. This does not
      include the project ID.
    maxResults: Maximum number of backup runs per response.
    pageToken: A previously-returned page token representing part of the
      larger set of results to view.
    project: Project ID of the project that contains the instance.
  """

  instance = _messages.StringField(1, required=True)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  project = _messages.StringField(4, required=True)


class SqlConnectGenerateEphemeralRequest(_messages.Message):
  r"""A SqlConnectGenerateEphemeralRequest object.

  Fields:
    generateEphemeralCertRequest: A GenerateEphemeralCertRequest resource to
      be passed as the request body.
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  generateEphemeralCertRequest = _messages.MessageField('GenerateEphemeralCertRequest', 1)
  instance = _messages.StringField(2, required=True)
  project = _messages.StringField(3, required=True)


class SqlConnectGetRequest(_messages.Message):
  r"""A SqlConnectGetRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
    readTime: Optional. Optional snapshot read timestamp to trade freshness
      for performance.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)
  readTime = _messages.StringField(3)


class SqlDatabasesDeleteRequest(_messages.Message):
  r"""A SqlDatabasesDeleteRequest object.

  Fields:
    database: Name of the database to be deleted in the instance.
    instance: Database instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  database = _messages.StringField(1, required=True)
  instance = _messages.StringField(2, required=True)
  project = _messages.StringField(3, required=True)


class SqlDatabasesGetRequest(_messages.Message):
  r"""A SqlDatabasesGetRequest object.

  Fields:
    database: Name of the database in the instance.
    instance: Database instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  database = _messages.StringField(1, required=True)
  instance = _messages.StringField(2, required=True)
  project = _messages.StringField(3, required=True)


class SqlDatabasesListRequest(_messages.Message):
  r"""A SqlDatabasesListRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class SqlDatabasesPatchRequest(_messages.Message):
  r"""A SqlDatabasesPatchRequest object.

  Fields:
    database: Name of the database to be updated in the instance.
    databaseResource: A Database resource to be passed as the request body.
    instance: Database instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  database = _messages.StringField(1, required=True)
  databaseResource = _messages.MessageField('Database', 2)
  instance = _messages.StringField(3, required=True)
  project = _messages.StringField(4, required=True)


class SqlDatabasesUpdateRequest(_messages.Message):
  r"""A SqlDatabasesUpdateRequest object.

  Fields:
    database: Name of the database to be updated in the instance.
    databaseResource: A Database resource to be passed as the request body.
    instance: Database instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  database = _messages.StringField(1, required=True)
  databaseResource = _messages.MessageField('Database', 2)
  instance = _messages.StringField(3, required=True)
  project = _messages.StringField(4, required=True)


class SqlExternalSyncSettingError(_messages.Message):
  r"""External primary instance migration setting error/warning.

  Enums:
    TypeValueValuesEnum: Identifies the specific error that occurred.

  Fields:
    detail: Additional information about the error encountered.
    kind: Can be `sql#externalSyncSettingError` or
      `sql#externalSyncSettingWarning`.
    type: Identifies the specific error that occurred.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Identifies the specific error that occurred.

    Values:
      SQL_EXTERNAL_SYNC_SETTING_ERROR_TYPE_UNSPECIFIED: <no description>
      CONNECTION_FAILURE: <no description>
      BINLOG_NOT_ENABLED: <no description>
      INCOMPATIBLE_DATABASE_VERSION: <no description>
      REPLICA_ALREADY_SETUP: <no description>
      INSUFFICIENT_PRIVILEGE: The replication user is missing privileges that
        are required.
      UNSUPPORTED_MIGRATION_TYPE: Unsupported migration type.
      NO_PGLOGICAL_INSTALLED: No pglogical extension installed on databases,
        applicable for postgres.
      PGLOGICAL_NODE_ALREADY_EXISTS: pglogical node already exists on
        databases, applicable for postgres.
      INVALID_WAL_LEVEL: The value of parameter wal_level is not set to
        logical.
      INVALID_SHARED_PRELOAD_LIBRARY: The value of parameter
        shared_preload_libraries does not include pglogical.
      INSUFFICIENT_MAX_REPLICATION_SLOTS: The value of parameter
        max_replication_slots is not sufficient.
      INSUFFICIENT_MAX_WAL_SENDERS: The value of parameter max_wal_senders is
        not sufficient.
      INSUFFICIENT_MAX_WORKER_PROCESSES: The value of parameter
        max_worker_processes is not sufficient.
      UNSUPPORTED_EXTENSIONS: Extensions installed are either not supported or
        having unsupported versions
      INVALID_RDS_LOGICAL_REPLICATION: The value of parameter
        rds.logical_replication is not set to 1.
      INVALID_LOGGING_SETUP: The primary instance logging setup doesn't allow
        EM sync.
      INVALID_DB_PARAM: The primary instance database parameter setup doesn't
        allow EM sync.
      UNSUPPORTED_GTID_MODE: The gtid_mode is not supported, applicable for
        MySQL.
      SQLSERVER_AGENT_NOT_RUNNING: SQL Server Agent is not running.
      UNSUPPORTED_TABLE_DEFINITION: The table definition is not support due to
        missing primary key or replica identity, applicable for postgres.
      UNSUPPORTED_DEFINER: The customer has a definer that will break EM
        setup.
      SQLSERVER_SERVERNAME_MISMATCH: SQL Server @@SERVERNAME does not match
        actual host name.
      PRIMARY_ALREADY_SETUP: The primary instance has been setup and will fail
        the setup.
      UNSUPPORTED_BINLOG_FORMAT: The primary instance has unsupported binary
        log format.
      BINLOG_RETENTION_SETTING: The primary instance's binary log retention
        setting.
      UNSUPPORTED_STORAGE_ENGINE: The primary instance has tables with
        unsupported storage engine.
      LIMITED_SUPPORT_TABLES: Source has tables with limited support eg:
        PostgreSQL tables without primary keys.
      EXISTING_DATA_IN_REPLICA: The replica instance contains existing data.
      MISSING_OPTIONAL_PRIVILEGES: The replication user is missing privileges
        that are optional.
      RISKY_BACKUP_ADMIN_PRIVILEGE: Additional BACKUP_ADMIN privilege is
        granted to the replication user which may lock source MySQL 8 instance
        for DDLs during initial sync.
      INSUFFICIENT_GCS_PERMISSIONS: The Cloud Storage bucket is missing
        necessary permissions.
      INVALID_FILE_INFO: The Cloud Storage bucket has an error in the file or
        contains invalid file information.
      UNSUPPORTED_DATABASE_SETTINGS: The source instance has unsupported
        database settings for migration.
      MYSQL_PARALLEL_IMPORT_INSUFFICIENT_PRIVILEGE: The replication user is
        missing parallel import specific privileges. (e.g. LOCK TABLES) for
        MySQL.
    """
    SQL_EXTERNAL_SYNC_SETTING_ERROR_TYPE_UNSPECIFIED = 0
    CONNECTION_FAILURE = 1
    BINLOG_NOT_ENABLED = 2
    INCOMPATIBLE_DATABASE_VERSION = 3
    REPLICA_ALREADY_SETUP = 4
    INSUFFICIENT_PRIVILEGE = 5
    UNSUPPORTED_MIGRATION_TYPE = 6
    NO_PGLOGICAL_INSTALLED = 7
    PGLOGICAL_NODE_ALREADY_EXISTS = 8
    INVALID_WAL_LEVEL = 9
    INVALID_SHARED_PRELOAD_LIBRARY = 10
    INSUFFICIENT_MAX_REPLICATION_SLOTS = 11
    INSUFFICIENT_MAX_WAL_SENDERS = 12
    INSUFFICIENT_MAX_WORKER_PROCESSES = 13
    UNSUPPORTED_EXTENSIONS = 14
    INVALID_RDS_LOGICAL_REPLICATION = 15
    INVALID_LOGGING_SETUP = 16
    INVALID_DB_PARAM = 17
    UNSUPPORTED_GTID_MODE = 18
    SQLSERVER_AGENT_NOT_RUNNING = 19
    UNSUPPORTED_TABLE_DEFINITION = 20
    UNSUPPORTED_DEFINER = 21
    SQLSERVER_SERVERNAME_MISMATCH = 22
    PRIMARY_ALREADY_SETUP = 23
    UNSUPPORTED_BINLOG_FORMAT = 24
    BINLOG_RETENTION_SETTING = 25
    UNSUPPORTED_STORAGE_ENGINE = 26
    LIMITED_SUPPORT_TABLES = 27
    EXISTING_DATA_IN_REPLICA = 28
    MISSING_OPTIONAL_PRIVILEGES = 29
    RISKY_BACKUP_ADMIN_PRIVILEGE = 30
    INSUFFICIENT_GCS_PERMISSIONS = 31
    INVALID_FILE_INFO = 32
    UNSUPPORTED_DATABASE_SETTINGS = 33
    MYSQL_PARALLEL_IMPORT_INSUFFICIENT_PRIVILEGE = 34

  detail = _messages.StringField(1)
  kind = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class SqlFlagsListRequest(_messages.Message):
  r"""A SqlFlagsListRequest object.

  Fields:
    databaseVersion: Database type and version you want to retrieve flags for.
      By default, this method returns flags for all database types and
      versions.
  """

  databaseVersion = _messages.StringField(1)


class SqlInstancesAddServerCaRequest(_messages.Message):
  r"""A SqlInstancesAddServerCaRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class SqlInstancesCloneRequest(_messages.Message):
  r"""A SqlInstancesCloneRequest object.

  Fields:
    instance: The ID of the Cloud SQL instance to be cloned (source). This
      does not include the project ID.
    instancesCloneRequest: A InstancesCloneRequest resource to be passed as
      the request body.
    project: Project ID of the source as well as the clone Cloud SQL instance.
  """

  instance = _messages.StringField(1, required=True)
  instancesCloneRequest = _messages.MessageField('InstancesCloneRequest', 2)
  project = _messages.StringField(3, required=True)


class SqlInstancesDeleteRequest(_messages.Message):
  r"""A SqlInstancesDeleteRequest object.

  Fields:
    finalBackupDescription: The description of the final backup.
    finalBackupExpiryTime: Optional. Final Backup expiration time. Timestamp
      in UTC of when this resource is considered expired.
    finalBackupTtlDays: Optional. Retention period of the final backup.
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance to be
      deleted.
    skipFinalBackup: By default we opt in for creating final backup
  """

  finalBackupDescription = _messages.StringField(1)
  finalBackupExpiryTime = _messages.StringField(2)
  finalBackupTtlDays = _messages.IntegerField(3)
  instance = _messages.StringField(4, required=True)
  project = _messages.StringField(5, required=True)
  skipFinalBackup = _messages.BooleanField(6)


class SqlInstancesDemoteMasterRequest(_messages.Message):
  r"""A SqlInstancesDemoteMasterRequest object.

  Fields:
    instance: Cloud SQL instance name.
    instancesDemoteMasterRequest: A InstancesDemoteMasterRequest resource to
      be passed as the request body.
    project: ID of the project that contains the instance.
  """

  instance = _messages.StringField(1, required=True)
  instancesDemoteMasterRequest = _messages.MessageField('InstancesDemoteMasterRequest', 2)
  project = _messages.StringField(3, required=True)


class SqlInstancesExportRequest(_messages.Message):
  r"""A SqlInstancesExportRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    instancesExportRequest: A InstancesExportRequest resource to be passed as
      the request body.
    project: Project ID of the project that contains the instance to be
      exported.
  """

  instance = _messages.StringField(1, required=True)
  instancesExportRequest = _messages.MessageField('InstancesExportRequest', 2)
  project = _messages.StringField(3, required=True)


class SqlInstancesFailoverRequest(_messages.Message):
  r"""A SqlInstancesFailoverRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    instancesFailoverRequest: A InstancesFailoverRequest resource to be passed
      as the request body.
    project: ID of the project that contains the read replica.
  """

  instance = _messages.StringField(1, required=True)
  instancesFailoverRequest = _messages.MessageField('InstancesFailoverRequest', 2)
  project = _messages.StringField(3, required=True)


class SqlInstancesGetDiskShrinkConfigResponse(_messages.Message):
  r"""Instance get disk shrink config response.

  Fields:
    kind: This is always `sql#getDiskShrinkConfig`.
    message: Additional message to customers.
    minimalTargetSizeGb: The minimum size to which a disk can be shrunk in
      GigaBytes.
  """

  kind = _messages.StringField(1)
  message = _messages.StringField(2)
  minimalTargetSizeGb = _messages.IntegerField(3)


class SqlInstancesGetLatestRecoveryTimeResponse(_messages.Message):
  r"""Instance get latest recovery time response.

  Fields:
    kind: This is always `sql#getLatestRecoveryTime`.
    latestRecoveryTime: Timestamp, identifies the latest recovery time of the
      source instance.
  """

  kind = _messages.StringField(1)
  latestRecoveryTime = _messages.StringField(2)


class SqlInstancesGetRequest(_messages.Message):
  r"""A SqlInstancesGetRequest object.

  Fields:
    instance: Database instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class SqlInstancesImportRequest(_messages.Message):
  r"""A SqlInstancesImportRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    instancesImportRequest: A InstancesImportRequest resource to be passed as
      the request body.
    project: Project ID of the project that contains the instance.
  """

  instance = _messages.StringField(1, required=True)
  instancesImportRequest = _messages.MessageField('InstancesImportRequest', 2)
  project = _messages.StringField(3, required=True)


class SqlInstancesInsertRequest(_messages.Message):
  r"""A SqlInstancesInsertRequest object.

  Fields:
    databaseInstance: A DatabaseInstance resource to be passed as the request
      body.
    project: Project ID of the project to which the newly created Cloud SQL
      instances should belong.
  """

  databaseInstance = _messages.MessageField('DatabaseInstance', 1)
  project = _messages.StringField(2, required=True)


class SqlInstancesListRequest(_messages.Message):
  r"""A SqlInstancesListRequest object.

  Fields:
    filter: A filter expression that filters resources listed in the response.
      The expression is in the form of field:value. For example,
      'instanceType:CLOUD_SQL_INSTANCE'. Fields can be nested as needed as per
      their JSON representation, such as
      'settings.userLabels.auto_start:true'. Multiple filter queries are
      space-separated. For example. 'state:RUNNABLE
      instanceType:CLOUD_SQL_INSTANCE'. By default, each expression is an AND
      expression. However, you can include AND and OR expressions explicitly.
    maxResults: The maximum number of instances to return. The service may
      return fewer than this value. If unspecified, at most 500 instances are
      returned. The maximum value is 1000; values above 1000 are coerced to
      1000.
    pageToken: A previously-returned page token representing part of the
      larger set of results to view.
    project: Project ID of the project for which to list Cloud SQL instances.
  """

  filter = _messages.StringField(1)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.UINT32)
  pageToken = _messages.StringField(3)
  project = _messages.StringField(4, required=True)


class SqlInstancesListServerCasRequest(_messages.Message):
  r"""A SqlInstancesListServerCasRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class SqlInstancesPatchRequest(_messages.Message):
  r"""A SqlInstancesPatchRequest object.

  Fields:
    databaseInstance: A DatabaseInstance resource to be passed as the request
      body.
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  databaseInstance = _messages.MessageField('DatabaseInstance', 1)
  instance = _messages.StringField(2, required=True)
  project = _messages.StringField(3, required=True)


class SqlInstancesPromoteReplicaRequest(_messages.Message):
  r"""A SqlInstancesPromoteReplicaRequest object.

  Fields:
    failover: Set to true if the promote operation should attempt to re-add
      the original primary as a replica when it comes back online. Otherwise,
      if this value is false or not set, the original primary will be a
      standalone instance.
    instance: Cloud SQL read replica instance name.
    project: ID of the project that contains the read replica.
  """

  failover = _messages.BooleanField(1)
  instance = _messages.StringField(2, required=True)
  project = _messages.StringField(3, required=True)


class SqlInstancesReencryptRequest(_messages.Message):
  r"""A SqlInstancesReencryptRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    instancesReencryptRequest: A InstancesReencryptRequest resource to be
      passed as the request body.
    project: ID of the project that contains the instance.
  """

  instance = _messages.StringField(1, required=True)
  instancesReencryptRequest = _messages.MessageField('InstancesReencryptRequest', 2)
  project = _messages.StringField(3, required=True)


class SqlInstancesRescheduleMaintenanceRequestBody(_messages.Message):
  r"""Reschedule options for maintenance windows.

  Fields:
    reschedule: Required. The type of the reschedule the user wants.
  """

  reschedule = _messages.MessageField('Reschedule', 1)


class SqlInstancesResetReplicaSizeRequest(_messages.Message):
  r"""Instance reset replica size request."""


class SqlInstancesResetSslConfigRequest(_messages.Message):
  r"""A SqlInstancesResetSslConfigRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class SqlInstancesRestartRequest(_messages.Message):
  r"""A SqlInstancesRestartRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance to be
      restarted.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class SqlInstancesRestoreBackupRequest(_messages.Message):
  r"""A SqlInstancesRestoreBackupRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    instancesRestoreBackupRequest: A InstancesRestoreBackupRequest resource to
      be passed as the request body.
    project: Project ID of the project that contains the instance.
  """

  instance = _messages.StringField(1, required=True)
  instancesRestoreBackupRequest = _messages.MessageField('InstancesRestoreBackupRequest', 2)
  project = _messages.StringField(3, required=True)


class SqlInstancesRotateServerCaRequest(_messages.Message):
  r"""A SqlInstancesRotateServerCaRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    instancesRotateServerCaRequest: A InstancesRotateServerCaRequest resource
      to be passed as the request body.
    project: Project ID of the project that contains the instance.
  """

  instance = _messages.StringField(1, required=True)
  instancesRotateServerCaRequest = _messages.MessageField('InstancesRotateServerCaRequest', 2)
  project = _messages.StringField(3, required=True)


class SqlInstancesStartExternalSyncRequest(_messages.Message):
  r"""A SqlInstancesStartExternalSyncRequest object.

  Enums:
    SyncModeValueValuesEnum: External sync mode.
    SyncParallelLevelValueValuesEnum: Optional. Parallel level for initial
      data sync. Currently only applicable for MySQL.

  Fields:
    mysqlSyncConfig: MySQL-specific settings for start external sync.
    skipVerification: Whether to skip the verification step (VESS).
    syncMode: External sync mode.
    syncParallelLevel: Optional. Parallel level for initial data sync.
      Currently only applicable for MySQL.
  """

  class SyncModeValueValuesEnum(_messages.Enum):
    r"""External sync mode.

    Values:
      EXTERNAL_SYNC_MODE_UNSPECIFIED: Unknown external sync mode, will be
        defaulted to ONLINE mode
      ONLINE: Online external sync will set up replication after initial data
        external sync
      OFFLINE: Offline external sync only dumps and loads a one-time snapshot
        of the primary instance's data
    """
    EXTERNAL_SYNC_MODE_UNSPECIFIED = 0
    ONLINE = 1
    OFFLINE = 2

  class SyncParallelLevelValueValuesEnum(_messages.Enum):
    r"""Optional. Parallel level for initial data sync. Currently only
    applicable for MySQL.

    Values:
      EXTERNAL_SYNC_PARALLEL_LEVEL_UNSPECIFIED: Unknown sync parallel level.
        Will be defaulted to OPTIMAL.
      MIN: Minimal parallel level.
      OPTIMAL: Optimal parallel level.
      MAX: Maximum parallel level.
    """
    EXTERNAL_SYNC_PARALLEL_LEVEL_UNSPECIFIED = 0
    MIN = 1
    OPTIMAL = 2
    MAX = 3

  mysqlSyncConfig = _messages.MessageField('MySqlSyncConfig', 1)
  skipVerification = _messages.BooleanField(2)
  syncMode = _messages.EnumField('SyncModeValueValuesEnum', 3)
  syncParallelLevel = _messages.EnumField('SyncParallelLevelValueValuesEnum', 4)


class SqlInstancesStartReplicaRequest(_messages.Message):
  r"""A SqlInstancesStartReplicaRequest object.

  Fields:
    instance: Cloud SQL read replica instance name.
    project: ID of the project that contains the read replica.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class SqlInstancesStopReplicaRequest(_messages.Message):
  r"""A SqlInstancesStopReplicaRequest object.

  Fields:
    instance: Cloud SQL read replica instance name.
    project: ID of the project that contains the read replica.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class SqlInstancesSwitchoverRequest(_messages.Message):
  r"""A SqlInstancesSwitchoverRequest object.

  Fields:
    dbTimeout: Optional. (MySQL only) Cloud SQL instance operations timeout,
      which is a sum of all database operations. Default value is 10 minutes
      and can be modified to a maximum value of 24 hours.
    instance: Cloud SQL read replica instance name.
    project: ID of the project that contains the replica.
  """

  dbTimeout = _messages.StringField(1)
  instance = _messages.StringField(2, required=True)
  project = _messages.StringField(3, required=True)


class SqlInstancesTruncateLogRequest(_messages.Message):
  r"""A SqlInstancesTruncateLogRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    instancesTruncateLogRequest: A InstancesTruncateLogRequest resource to be
      passed as the request body.
    project: Project ID of the Cloud SQL project.
  """

  instance = _messages.StringField(1, required=True)
  instancesTruncateLogRequest = _messages.MessageField('InstancesTruncateLogRequest', 2)
  project = _messages.StringField(3, required=True)


class SqlInstancesUpdateRequest(_messages.Message):
  r"""A SqlInstancesUpdateRequest object.

  Fields:
    databaseInstance: A DatabaseInstance resource to be passed as the request
      body.
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  databaseInstance = _messages.MessageField('DatabaseInstance', 1)
  instance = _messages.StringField(2, required=True)
  project = _messages.StringField(3, required=True)


class SqlInstancesVerifyExternalSyncSettingsRequest(_messages.Message):
  r"""A SqlInstancesVerifyExternalSyncSettingsRequest object.

  Enums:
    SyncModeValueValuesEnum: External sync mode

  Fields:
    mysqlSyncConfig: Optional. MySQL-specific settings for start external
      sync.
    syncMode: External sync mode
    verifyConnectionOnly: Flag to enable verifying connection only
    verifyReplicationOnly: Optional. Flag to verify settings required by
      replication setup only
  """

  class SyncModeValueValuesEnum(_messages.Enum):
    r"""External sync mode

    Values:
      EXTERNAL_SYNC_MODE_UNSPECIFIED: Unknown external sync mode, will be
        defaulted to ONLINE mode
      ONLINE: Online external sync will set up replication after initial data
        external sync
      OFFLINE: Offline external sync only dumps and loads a one-time snapshot
        of the primary instance's data
    """
    EXTERNAL_SYNC_MODE_UNSPECIFIED = 0
    ONLINE = 1
    OFFLINE = 2

  mysqlSyncConfig = _messages.MessageField('MySqlSyncConfig', 1)
  syncMode = _messages.EnumField('SyncModeValueValuesEnum', 2)
  verifyConnectionOnly = _messages.BooleanField(3)
  verifyReplicationOnly = _messages.BooleanField(4)


class SqlInstancesVerifyExternalSyncSettingsResponse(_messages.Message):
  r"""Instance verify external sync settings response.

  Fields:
    errors: List of migration violations.
    kind: This is always `sql#migrationSettingErrorList`.
    warnings: List of migration warnings.
  """

  errors = _messages.MessageField('SqlExternalSyncSettingError', 1, repeated=True)
  kind = _messages.StringField(2)
  warnings = _messages.MessageField('SqlExternalSyncSettingError', 3, repeated=True)


class SqlOperationsCancelRequest(_messages.Message):
  r"""A SqlOperationsCancelRequest object.

  Fields:
    operation: Instance operation ID.
    project: Project ID of the project that contains the instance.
  """

  operation = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class SqlOperationsGetRequest(_messages.Message):
  r"""A SqlOperationsGetRequest object.

  Fields:
    operation: Instance operation ID.
    project: Project ID of the project that contains the instance.
  """

  operation = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class SqlOperationsListRequest(_messages.Message):
  r"""A SqlOperationsListRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    maxResults: Maximum number of operations per response.
    pageToken: A previously-returned page token representing part of the
      larger set of results to view.
    project: Project ID of the project that contains the instance.
  """

  instance = _messages.StringField(1)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.UINT32)
  pageToken = _messages.StringField(3)
  project = _messages.StringField(4, required=True)


class SqlOutOfDiskReport(_messages.Message):
  r"""This message wraps up the information written by out-of-disk detection
  job.

  Enums:
    SqlOutOfDiskStateValueValuesEnum: This field represents the state
      generated by the proactive database wellness job for OutOfDisk issues. *
      Writers: * the proactive database wellness job for OOD. * Readers: * the
      proactive database wellness job

  Fields:
    sqlMinRecommendedIncreaseSizeGb: The minimum recommended increase size in
      GigaBytes This field is consumed by the frontend * Writers: * the
      proactive database wellness job for OOD. * Readers:
    sqlOutOfDiskState: This field represents the state generated by the
      proactive database wellness job for OutOfDisk issues. * Writers: * the
      proactive database wellness job for OOD. * Readers: * the proactive
      database wellness job
  """

  class SqlOutOfDiskStateValueValuesEnum(_messages.Enum):
    r"""This field represents the state generated by the proactive database
    wellness job for OutOfDisk issues. * Writers: * the proactive database
    wellness job for OOD. * Readers: * the proactive database wellness job

    Values:
      SQL_OUT_OF_DISK_STATE_UNSPECIFIED: Unspecified state
      NORMAL: The instance has plenty space on data disk
      SOFT_SHUTDOWN: Data disk is almost used up. It is shutdown to prevent
        data corruption.
    """
    SQL_OUT_OF_DISK_STATE_UNSPECIFIED = 0
    NORMAL = 1
    SOFT_SHUTDOWN = 2

  sqlMinRecommendedIncreaseSizeGb = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  sqlOutOfDiskState = _messages.EnumField('SqlOutOfDiskStateValueValuesEnum', 2)


class SqlProjectsInstancesGetDiskShrinkConfigRequest(_messages.Message):
  r"""A SqlProjectsInstancesGetDiskShrinkConfigRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class SqlProjectsInstancesGetLatestRecoveryTimeRequest(_messages.Message):
  r"""A SqlProjectsInstancesGetLatestRecoveryTimeRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class SqlProjectsInstancesPerformDiskShrinkRequest(_messages.Message):
  r"""A SqlProjectsInstancesPerformDiskShrinkRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    performDiskShrinkContext: A PerformDiskShrinkContext resource to be passed
      as the request body.
    project: Project ID of the project that contains the instance.
  """

  instance = _messages.StringField(1, required=True)
  performDiskShrinkContext = _messages.MessageField('PerformDiskShrinkContext', 2)
  project = _messages.StringField(3, required=True)


class SqlProjectsInstancesRescheduleMaintenanceRequest(_messages.Message):
  r"""A SqlProjectsInstancesRescheduleMaintenanceRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: ID of the project that contains the instance.
    sqlInstancesRescheduleMaintenanceRequestBody: A
      SqlInstancesRescheduleMaintenanceRequestBody resource to be passed as
      the request body.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)
  sqlInstancesRescheduleMaintenanceRequestBody = _messages.MessageField('SqlInstancesRescheduleMaintenanceRequestBody', 3)


class SqlProjectsInstancesResetReplicaSizeRequest(_messages.Message):
  r"""A SqlProjectsInstancesResetReplicaSizeRequest object.

  Fields:
    instance: Cloud SQL read replica instance name.
    project: ID of the project that contains the read replica.
    sqlInstancesResetReplicaSizeRequest: A SqlInstancesResetReplicaSizeRequest
      resource to be passed as the request body.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)
  sqlInstancesResetReplicaSizeRequest = _messages.MessageField('SqlInstancesResetReplicaSizeRequest', 3)


class SqlProjectsInstancesStartExternalSyncRequest(_messages.Message):
  r"""A SqlProjectsInstancesStartExternalSyncRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: ID of the project that contains the instance.
    sqlInstancesStartExternalSyncRequest: A
      SqlInstancesStartExternalSyncRequest resource to be passed as the
      request body.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)
  sqlInstancesStartExternalSyncRequest = _messages.MessageField('SqlInstancesStartExternalSyncRequest', 3)


class SqlProjectsInstancesVerifyExternalSyncSettingsRequest(_messages.Message):
  r"""A SqlProjectsInstancesVerifyExternalSyncSettingsRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
    sqlInstancesVerifyExternalSyncSettingsRequest: A
      SqlInstancesVerifyExternalSyncSettingsRequest resource to be passed as
      the request body.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)
  sqlInstancesVerifyExternalSyncSettingsRequest = _messages.MessageField('SqlInstancesVerifyExternalSyncSettingsRequest', 3)


class SqlScheduledMaintenance(_messages.Message):
  r"""Any scheduled maintenance for this instance.

  Fields:
    canDefer: A boolean attribute.
    canReschedule: If the scheduled maintenance can be rescheduled.
    scheduleDeadlineTime: Maintenance cannot be rescheduled to start beyond
      this deadline.
    startTime: The start time of any upcoming scheduled maintenance for this
      instance.
  """

  canDefer = _messages.BooleanField(1)
  canReschedule = _messages.BooleanField(2)
  scheduleDeadlineTime = _messages.StringField(3)
  startTime = _messages.StringField(4)


class SqlServerAuditConfig(_messages.Message):
  r"""SQL Server specific audit configuration.

  Fields:
    bucket: The name of the destination bucket (e.g., gs://mybucket).
    kind: This is always sql#sqlServerAuditConfig
    retentionInterval: How long to keep generated audit files.
    uploadInterval: How often to upload generated audit files.
  """

  bucket = _messages.StringField(1)
  kind = _messages.StringField(2)
  retentionInterval = _messages.StringField(3)
  uploadInterval = _messages.StringField(4)


class SqlServerDatabaseDetails(_messages.Message):
  r"""Represents a Sql Server database on the Cloud SQL instance.

  Fields:
    compatibilityLevel: The version of SQL Server with which the database is
      to be made compatible
    recoveryModel: The recovery model of a SQL Server database
  """

  compatibilityLevel = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  recoveryModel = _messages.StringField(2)


class SqlServerUserDetails(_messages.Message):
  r"""Represents a Sql Server user on the Cloud SQL instance.

  Fields:
    disabled: If the user has been disabled
    serverRoles: The server roles for this user
  """

  disabled = _messages.BooleanField(1)
  serverRoles = _messages.StringField(2, repeated=True)


class SqlSslCertsCreateEphemeralRequest(_messages.Message):
  r"""A SqlSslCertsCreateEphemeralRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the Cloud SQL project.
    sslCertsCreateEphemeralRequest: A SslCertsCreateEphemeralRequest resource
      to be passed as the request body.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)
  sslCertsCreateEphemeralRequest = _messages.MessageField('SslCertsCreateEphemeralRequest', 3)


class SqlSslCertsDeleteRequest(_messages.Message):
  r"""A SqlSslCertsDeleteRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
    sha1Fingerprint: Sha1 FingerPrint.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)
  sha1Fingerprint = _messages.StringField(3, required=True)


class SqlSslCertsGetRequest(_messages.Message):
  r"""A SqlSslCertsGetRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
    sha1Fingerprint: Sha1 FingerPrint.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)
  sha1Fingerprint = _messages.StringField(3, required=True)


class SqlSslCertsInsertRequest(_messages.Message):
  r"""A SqlSslCertsInsertRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
    sslCertsInsertRequest: A SslCertsInsertRequest resource to be passed as
      the request body.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)
  sslCertsInsertRequest = _messages.MessageField('SslCertsInsertRequest', 3)


class SqlSslCertsListRequest(_messages.Message):
  r"""A SqlSslCertsListRequest object.

  Fields:
    instance: Cloud SQL instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class SqlTiersListRequest(_messages.Message):
  r"""A SqlTiersListRequest object.

  Fields:
    project: Project ID of the project for which to list tiers.
  """

  project = _messages.StringField(1, required=True)


class SqlUsersDeleteRequest(_messages.Message):
  r"""A SqlUsersDeleteRequest object.

  Fields:
    host: Host of the user in the instance.
    instance: Database instance ID. This does not include the project ID.
    name: Name of the user in the instance.
    project: Project ID of the project that contains the instance.
  """

  host = _messages.StringField(1)
  instance = _messages.StringField(2, required=True)
  name = _messages.StringField(3)
  project = _messages.StringField(4, required=True)


class SqlUsersGetRequest(_messages.Message):
  r"""A SqlUsersGetRequest object.

  Fields:
    host: Host of a user of the instance.
    instance: Database instance ID. This does not include the project ID.
    name: User of the instance.
    project: Project ID of the project that contains the instance.
  """

  host = _messages.StringField(1)
  instance = _messages.StringField(2, required=True)
  name = _messages.StringField(3, required=True)
  project = _messages.StringField(4, required=True)


class SqlUsersListRequest(_messages.Message):
  r"""A SqlUsersListRequest object.

  Fields:
    instance: Database instance ID. This does not include the project ID.
    project: Project ID of the project that contains the instance.
  """

  instance = _messages.StringField(1, required=True)
  project = _messages.StringField(2, required=True)


class SqlUsersUpdateRequest(_messages.Message):
  r"""A SqlUsersUpdateRequest object.

  Fields:
    host: Optional. Host of the user in the instance.
    instance: Database instance ID. This does not include the project ID.
    name: Name of the user in the instance.
    project: Project ID of the project that contains the instance.
    user: A User resource to be passed as the request body.
  """

  host = _messages.StringField(1)
  instance = _messages.StringField(2, required=True)
  name = _messages.StringField(3)
  project = _messages.StringField(4, required=True)
  user = _messages.MessageField('User', 5)


class SslCert(_messages.Message):
  r"""SslCerts Resource

  Fields:
    cert: PEM representation.
    certSerialNumber: Serial number, as extracted from the certificate.
    commonName: User supplied name. Constrained to [a-zA-Z.-_ ]+.
    createTime: The time when the certificate was created in [RFC
      3339](https://tools.ietf.org/html/rfc3339) format, for example
      `2012-11-15T16:19:00.094Z`.
    expirationTime: The time when the certificate expires in [RFC
      3339](https://tools.ietf.org/html/rfc3339) format, for example
      `2012-11-15T16:19:00.094Z`.
    instance: Name of the database instance.
    kind: This is always `sql#sslCert`.
    selfLink: The URI of this resource.
    sha1Fingerprint: Sha1 Fingerprint.
  """

  cert = _messages.StringField(1)
  certSerialNumber = _messages.StringField(2)
  commonName = _messages.StringField(3)
  createTime = _messages.StringField(4)
  expirationTime = _messages.StringField(5)
  instance = _messages.StringField(6)
  kind = _messages.StringField(7)
  selfLink = _messages.StringField(8)
  sha1Fingerprint = _messages.StringField(9)


class SslCertDetail(_messages.Message):
  r"""SslCertDetail.

  Fields:
    certInfo: The public information about the cert.
    certPrivateKey: The private key for the client cert, in pem format. Keep
      private in order to protect your security.
  """

  certInfo = _messages.MessageField('SslCert', 1)
  certPrivateKey = _messages.StringField(2)


class SslCertsCreateEphemeralRequest(_messages.Message):
  r"""SslCerts create ephemeral certificate request.

  Fields:
    access_token: Access token to include in the signed certificate.
    public_key: PEM encoded public key to include in the signed certificate.
  """

  access_token = _messages.StringField(1)
  public_key = _messages.StringField(2)


class SslCertsInsertRequest(_messages.Message):
  r"""SslCerts insert request.

  Fields:
    commonName: User supplied name. Must be a distinct name from the other
      certificates for this instance.
  """

  commonName = _messages.StringField(1)


class SslCertsInsertResponse(_messages.Message):
  r"""SslCert insert response.

  Fields:
    clientCert: The new client certificate and private key.
    kind: This is always `sql#sslCertsInsert`.
    operation: The operation to track the ssl certs insert request.
    serverCaCert: The server Certificate Authority's certificate. If this is
      missing you can force a new one to be generated by calling
      resetSslConfig method on instances resource.
  """

  clientCert = _messages.MessageField('SslCertDetail', 1)
  kind = _messages.StringField(2)
  operation = _messages.MessageField('Operation', 3)
  serverCaCert = _messages.MessageField('SslCert', 4)


class SslCertsListResponse(_messages.Message):
  r"""SslCerts list response.

  Fields:
    items: List of client certificates for the instance.
    kind: This is always `sql#sslCertsList`.
  """

  items = _messages.MessageField('SslCert', 1, repeated=True)
  kind = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class SyncFlags(_messages.Message):
  r"""Initial sync flags for certain Cloud SQL APIs. Currently used for the
  MySQL external server initial dump.

  Fields:
    name: The name of the flag.
    value: The value of the flag. This field must be omitted if the flag
      doesn't take a value.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class Tier(_messages.Message):
  r"""A Google Cloud SQL service tier resource.

  Fields:
    DiskQuota: The maximum disk size of this tier in bytes.
    RAM: The maximum RAM usage of this tier in bytes.
    edition: Edition can be STANDARD or ENTERPRISE.
    kind: This is always `sql#tier`.
    region: The applicable regions for this tier.
    tier: An identifier for the machine type, for example, `db-custom-1-3840`.
      For related information, see [Pricing](/sql/pricing).
  """

  DiskQuota = _messages.IntegerField(1)
  RAM = _messages.IntegerField(2)
  edition = _messages.StringField(3)
  kind = _messages.StringField(4)
  region = _messages.StringField(5, repeated=True)
  tier = _messages.StringField(6)


class TiersListResponse(_messages.Message):
  r"""Tiers list response.

  Fields:
    items: List of tiers.
    kind: This is always `sql#tiersList`.
  """

  items = _messages.MessageField('Tier', 1, repeated=True)
  kind = _messages.StringField(2)


class TruncateLogContext(_messages.Message):
  r"""Database Instance truncate log context.

  Fields:
    kind: This is always `sql#truncateLogContext`.
    logType: The type of log to truncate. Valid values are
      `MYSQL_GENERAL_TABLE` and `MYSQL_SLOW_TABLE`.
  """

  kind = _messages.StringField(1)
  logType = _messages.StringField(2)


class User(_messages.Message):
  r"""A Cloud SQL user resource.

  Enums:
    DualPasswordTypeValueValuesEnum: Dual password status for the user.
    TypeValueValuesEnum: The user type. It determines the method to
      authenticate the user during login. The default is the database's built-
      in user type.

  Fields:
    dualPasswordType: Dual password status for the user.
    etag: This field is deprecated and will be removed from a future version
      of the API.
    host: Optional. The host from which the user can connect. For `insert`
      operations, host defaults to an empty string. For `update` operations,
      host is specified as part of the request URL. The host name cannot be
      updated after insertion. For a MySQL instance, it's required; for a
      PostgreSQL or SQL Server instance, it's optional.
    iamEmail: The full email for an IAM user. For normal database users, this
      will not be filled. Only applicable to MySQL database users.
    instance: The name of the Cloud SQL instance. This does not include the
      project ID. Can be omitted for *update* because it is already specified
      on the URL.
    kind: This is always `sql#user`.
    name: The name of the user in the Cloud SQL instance. Can be omitted for
      `update` because it is already specified in the URL.
    password: The password for the user.
    passwordPolicy: User level password validation policy.
    project: The project ID of the project containing the Cloud SQL database.
      The Google apps domain is prefixed if applicable. Can be omitted for
      *update* because it is already specified on the URL.
    sqlserverUserDetails: A SqlServerUserDetails attribute.
    type: The user type. It determines the method to authenticate the user
      during login. The default is the database's built-in user type.
  """

  class DualPasswordTypeValueValuesEnum(_messages.Enum):
    r"""Dual password status for the user.

    Values:
      DUAL_PASSWORD_TYPE_UNSPECIFIED: The default value.
      NO_MODIFY_DUAL_PASSWORD: Do not update the user's dual password status.
      NO_DUAL_PASSWORD: No dual password usable for connecting using this
        user.
      DUAL_PASSWORD: Dual password usable for connecting using this user.
    """
    DUAL_PASSWORD_TYPE_UNSPECIFIED = 0
    NO_MODIFY_DUAL_PASSWORD = 1
    NO_DUAL_PASSWORD = 2
    DUAL_PASSWORD = 3

  class TypeValueValuesEnum(_messages.Enum):
    r"""The user type. It determines the method to authenticate the user
    during login. The default is the database's built-in user type.

    Values:
      BUILT_IN: The database's built-in user type.
      CLOUD_IAM_USER: Cloud IAM user.
      CLOUD_IAM_SERVICE_ACCOUNT: Cloud IAM service account.
      CLOUD_IAM_GROUP: Cloud IAM Group non-login user.
      CLOUD_IAM_GROUP_USER: Cloud IAM Group login user.
      CLOUD_IAM_GROUP_SERVICE_ACCOUNT: Cloud IAM Group service account.
    """
    BUILT_IN = 0
    CLOUD_IAM_USER = 1
    CLOUD_IAM_SERVICE_ACCOUNT = 2
    CLOUD_IAM_GROUP = 3
    CLOUD_IAM_GROUP_USER = 4
    CLOUD_IAM_GROUP_SERVICE_ACCOUNT = 5

  dualPasswordType = _messages.EnumField('DualPasswordTypeValueValuesEnum', 1)
  etag = _messages.StringField(2)
  host = _messages.StringField(3)
  iamEmail = _messages.StringField(4)
  instance = _messages.StringField(5)
  kind = _messages.StringField(6)
  name = _messages.StringField(7)
  password = _messages.StringField(8)
  passwordPolicy = _messages.MessageField('UserPasswordValidationPolicy', 9)
  project = _messages.StringField(10)
  sqlserverUserDetails = _messages.MessageField('SqlServerUserDetails', 11)
  type = _messages.EnumField('TypeValueValuesEnum', 12)


class UserPasswordValidationPolicy(_messages.Message):
  r"""User level password validation policy.

  Fields:
    allowedFailedAttempts: Number of failed login attempts allowed before user
      get locked.
    enableFailedAttemptsCheck: If true, failed login attempts check will be
      enabled.
    enablePasswordVerification: If true, the user must specify the current
      password before changing the password. This flag is supported only for
      MySQL.
    passwordExpirationDuration: Expiration duration after password is updated.
    status: Output only. Read-only password status.
  """

  allowedFailedAttempts = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  enableFailedAttemptsCheck = _messages.BooleanField(2)
  enablePasswordVerification = _messages.BooleanField(3)
  passwordExpirationDuration = _messages.StringField(4)
  status = _messages.MessageField('PasswordStatus', 5)


class UsersListResponse(_messages.Message):
  r"""User list response.

  Fields:
    items: List of user resources in the instance.
    kind: This is always *sql#usersList*.
    nextPageToken: Unused.
  """

  items = _messages.MessageField('User', 1, repeated=True)
  kind = _messages.StringField(2)
  nextPageToken = _messages.StringField(3)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

"""Generated message classes for gkebackup version v1.

Backup for GKE is a managed Kubernetes workload backup and restore service for
GKE clusters.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'gkebackup'


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Backup(_messages.Message):
  r"""Represents a request to perform a single point-in-time capture of some
  portion of the state of a GKE cluster, the record of the backup operation
  itself, and an anchor for the underlying artifacts that comprise the Backup
  (the config backup and VolumeBackups). Next id: 29

  Enums:
    StateValueValuesEnum: Output only. Current state of the Backup

  Messages:
    LabelsValue: Optional. A set of custom labels supplied by user.

  Fields:
    allNamespaces: Output only. If True, all namespaces were included in the
      Backup.
    clusterMetadata: Output only. Information about the GKE cluster from which
      this Backup was created.
    completeTime: Output only. Completion time of the Backup
    configBackupSizeBytes: Output only. The size of the config backup in
      bytes.
    containsSecrets: Output only. Whether or not the Backup contains
      Kubernetes Secrets. Controlled by the parent BackupPlan's
      include_secrets value.
    containsVolumeData: Output only. Whether or not the Backup contains volume
      data. Controlled by the parent BackupPlan's include_volume_data value.
    createTime: Output only. The timestamp when this Backup resource was
      created.
    deleteLockDays: Optional. Minimum age for this Backup (in days). If this
      field is set to a non-zero value, the Backup will be "locked" against
      deletion (either manual or automatic deletion) for the number of days
      provided (measured from the creation time of the Backup). MUST be an
      integer value between 0-90 (inclusive). Defaults to parent BackupPlan's
      backup_delete_lock_days setting and may only be increased (either at
      creation time or in a subsequent update).
    deleteLockExpireTime: Output only. The time at which an existing delete
      lock will expire for this backup (calculated from create_time +
      delete_lock_days).
    description: Optional. User specified descriptive string for this Backup.
    encryptionKey: Output only. The customer managed encryption key that was
      used to encrypt the Backup's artifacts. Inherited from the parent
      BackupPlan's encryption_key value.
    etag: Output only. `etag` is used for optimistic concurrency control as a
      way to help prevent simultaneous updates of a backup from overwriting
      each other. It is strongly suggested that systems make use of the `etag`
      in the read-modify-write cycle to perform backup updates in order to
      avoid race conditions: An `etag` is returned in the response to
      `GetBackup`, and systems are expected to put that etag in the request to
      `UpdateBackup` or `DeleteBackup` to ensure that their change will be
      applied to the same version of the resource.
    labels: Optional. A set of custom labels supplied by user.
    manual: Output only. This flag indicates whether this Backup resource was
      created manually by a user or via a schedule in the BackupPlan. A value
      of True means that the Backup was created manually.
    name: Output only. The fully qualified name of the Backup.
      `projects/*/locations/*/backupPlans/*/backups/*`
    permissiveMode: Output only. If false, Backup will fail when Backup for
      GKE detects Kubernetes configuration that is non-standard or requires
      additional setup to restore. Inherited from the parent BackupPlan's
      permissive_mode value.
    podCount: Output only. The total number of Kubernetes Pods contained in
      the Backup.
    resourceCount: Output only. The total number of Kubernetes resources
      included in the Backup.
    retainDays: Optional. The age (in days) after which this Backup will be
      automatically deleted. Must be an integer value >= 0: - If 0, no
      automatic deletion will occur for this Backup. - If not 0, this must be
      >= delete_lock_days and <= 365. Once a Backup is created, this value may
      only be increased. Defaults to the parent BackupPlan's
      backup_retain_days value.
    retainExpireTime: Output only. The time at which this Backup will be
      automatically deleted (calculated from create_time + retain_days).
    selectedApplications: Output only. If set, the list of
      ProtectedApplications whose resources were included in the Backup.
    selectedNamespaces: Output only. If set, the list of namespaces that were
      included in the Backup.
    sizeBytes: Output only. The total size of the Backup in bytes = config
      backup size + sum(volume backup sizes)
    state: Output only. Current state of the Backup
    stateReason: Output only. Human-readable description of why the backup is
      in the current `state`.
    uid: Output only. Server generated global unique identifier of
      [UUID4](https://en.wikipedia.org/wiki/Universally_unique_identifier)
    updateTime: Output only. The timestamp when this Backup resource was last
      updated.
    volumeCount: Output only. The total number of volume backups contained in
      the Backup.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the Backup

    Values:
      STATE_UNSPECIFIED: The Backup resource is in the process of being
        created.
      CREATING: The Backup resource has been created and the associated
        BackupJob Kubernetes resource has been injected into the source
        cluster.
      IN_PROGRESS: The gkebackup agent in the cluster has begun executing the
        backup operation.
      SUCCEEDED: The backup operation has completed successfully.
      FAILED: The backup operation has failed.
      DELETING: This Backup resource (and its associated artifacts) is in the
        process of being deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    IN_PROGRESS = 2
    SUCCEEDED = 3
    FAILED = 4
    DELETING = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. A set of custom labels supplied by user.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allNamespaces = _messages.BooleanField(1)
  clusterMetadata = _messages.MessageField('ClusterMetadata', 2)
  completeTime = _messages.StringField(3)
  configBackupSizeBytes = _messages.IntegerField(4)
  containsSecrets = _messages.BooleanField(5)
  containsVolumeData = _messages.BooleanField(6)
  createTime = _messages.StringField(7)
  deleteLockDays = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  deleteLockExpireTime = _messages.StringField(9)
  description = _messages.StringField(10)
  encryptionKey = _messages.MessageField('EncryptionKey', 11)
  etag = _messages.StringField(12)
  labels = _messages.MessageField('LabelsValue', 13)
  manual = _messages.BooleanField(14)
  name = _messages.StringField(15)
  permissiveMode = _messages.BooleanField(16)
  podCount = _messages.IntegerField(17, variant=_messages.Variant.INT32)
  resourceCount = _messages.IntegerField(18, variant=_messages.Variant.INT32)
  retainDays = _messages.IntegerField(19, variant=_messages.Variant.INT32)
  retainExpireTime = _messages.StringField(20)
  selectedApplications = _messages.MessageField('NamespacedNames', 21)
  selectedNamespaces = _messages.MessageField('Namespaces', 22)
  sizeBytes = _messages.IntegerField(23)
  state = _messages.EnumField('StateValueValuesEnum', 24)
  stateReason = _messages.StringField(25)
  uid = _messages.StringField(26)
  updateTime = _messages.StringField(27)
  volumeCount = _messages.IntegerField(28, variant=_messages.Variant.INT32)


class BackupConfig(_messages.Message):
  r"""BackupConfig defines the configuration of Backups created via this
  BackupPlan.

  Fields:
    allNamespaces: If True, include all namespaced resources
    encryptionKey: Optional. This defines a customer managed encryption key
      that will be used to encrypt the "config" portion (the Kubernetes
      resources) of Backups created via this plan. Default (empty): Config
      backup artifacts will not be encrypted.
    includeSecrets: Optional. This flag specifies whether Kubernetes Secret
      resources should be included when they fall into the scope of Backups.
      Default: False
    includeVolumeData: Optional. This flag specifies whether volume data
      should be backed up when PVCs are included in the scope of a Backup.
      Default: False
    permissiveMode: Optional. If false, Backups will fail when Backup for GKE
      detects Kubernetes configuration that is non-standard or requires
      additional setup to restore. Default: False
    selectedApplications: If set, include just the resources referenced by the
      listed ProtectedApplications.
    selectedNamespaces: If set, include just the resources in the listed
      namespaces.
  """

  allNamespaces = _messages.BooleanField(1)
  encryptionKey = _messages.MessageField('EncryptionKey', 2)
  includeSecrets = _messages.BooleanField(3)
  includeVolumeData = _messages.BooleanField(4)
  permissiveMode = _messages.BooleanField(5)
  selectedApplications = _messages.MessageField('NamespacedNames', 6)
  selectedNamespaces = _messages.MessageField('Namespaces', 7)


class BackupPlan(_messages.Message):
  r"""Defines the configuration and scheduling for a "line" of Backups.

  Enums:
    StateValueValuesEnum: Output only. State of the BackupPlan. This State
      field reflects the various stages a BackupPlan can be in during the
      Create operation. It will be set to "DEACTIVATED" if the BackupPlan is
      deactivated on an Update

  Messages:
    LabelsValue: Optional. A set of custom labels supplied by user.

  Fields:
    backupConfig: Optional. Defines the configuration of Backups created via
      this BackupPlan.
    backupSchedule: Optional. Defines a schedule for automatic Backup creation
      via this BackupPlan.
    cluster: Required. Immutable. The source cluster from which Backups will
      be created via this BackupPlan. Valid formats: -
      `projects/*/locations/*/clusters/*` - `projects/*/zones/*/clusters/*`
    createTime: Output only. The timestamp when this BackupPlan resource was
      created.
    deactivated: Optional. This flag indicates whether this BackupPlan has
      been deactivated. Setting this field to True locks the BackupPlan such
      that no further updates will be allowed (except deletes), including the
      deactivated field itself. It also prevents any new Backups from being
      created via this BackupPlan (including scheduled Backups). Default:
      False
    description: Optional. User specified descriptive string for this
      BackupPlan.
    etag: Output only. `etag` is used for optimistic concurrency control as a
      way to help prevent simultaneous updates of a backup plan from
      overwriting each other. It is strongly suggested that systems make use
      of the 'etag' in the read-modify-write cycle to perform BackupPlan
      updates in order to avoid race conditions: An `etag` is returned in the
      response to `GetBackupPlan`, and systems are expected to put that etag
      in the request to `UpdateBackupPlan` or `DeleteBackupPlan` to ensure
      that their change will be applied to the same version of the resource.
    labels: Optional. A set of custom labels supplied by user.
    name: Output only. The full name of the BackupPlan resource. Format:
      `projects/*/locations/*/backupPlans/*`
    protectedPodCount: Output only. The number of Kubernetes Pods backed up in
      the last successful Backup created via this BackupPlan.
    retentionPolicy: Optional. RetentionPolicy governs lifecycle of Backups
      created under this plan.
    rpoRiskLevel: Output only. A number that represents the current risk level
      of this BackupPlan from RPO perspective with 1 being no risk and 5 being
      highest risk.
    rpoRiskReason: Output only. Human-readable description of why the
      BackupPlan is in the current rpo_risk_level and action items if any.
    state: Output only. State of the BackupPlan. This State field reflects the
      various stages a BackupPlan can be in during the Create operation. It
      will be set to "DEACTIVATED" if the BackupPlan is deactivated on an
      Update
    stateReason: Output only. Human-readable description of why BackupPlan is
      in the current `state`
    uid: Output only. Server generated global unique identifier of
      [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier)
      format.
    updateTime: Output only. The timestamp when this BackupPlan resource was
      last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the BackupPlan. This State field reflects the
    various stages a BackupPlan can be in during the Create operation. It will
    be set to "DEACTIVATED" if the BackupPlan is deactivated on an Update

    Values:
      STATE_UNSPECIFIED: Default first value for Enums.
      CLUSTER_PENDING: Waiting for cluster state to be RUNNING.
      PROVISIONING: The BackupPlan is in the process of being created.
      READY: The BackupPlan has successfully been created and is ready for
        Backups.
      FAILED: BackupPlan creation has failed.
      DEACTIVATED: The BackupPlan has been deactivated.
      DELETING: The BackupPlan is in the process of being deleted.
    """
    STATE_UNSPECIFIED = 0
    CLUSTER_PENDING = 1
    PROVISIONING = 2
    READY = 3
    FAILED = 4
    DEACTIVATED = 5
    DELETING = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. A set of custom labels supplied by user.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  backupConfig = _messages.MessageField('BackupConfig', 1)
  backupSchedule = _messages.MessageField('Schedule', 2)
  cluster = _messages.StringField(3)
  createTime = _messages.StringField(4)
  deactivated = _messages.BooleanField(5)
  description = _messages.StringField(6)
  etag = _messages.StringField(7)
  labels = _messages.MessageField('LabelsValue', 8)
  name = _messages.StringField(9)
  protectedPodCount = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  retentionPolicy = _messages.MessageField('RetentionPolicy', 11)
  rpoRiskLevel = _messages.IntegerField(12, variant=_messages.Variant.INT32)
  rpoRiskReason = _messages.StringField(13)
  state = _messages.EnumField('StateValueValuesEnum', 14)
  stateReason = _messages.StringField(15)
  uid = _messages.StringField(16)
  updateTime = _messages.StringField(17)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class ClusterMetadata(_messages.Message):
  r"""Information about the GKE cluster from which this Backup was created.

  Messages:
    BackupCrdVersionsValue: Output only. A list of the Backup for GKE CRD
      versions found in the cluster.

  Fields:
    anthosVersion: Output only. Anthos version
    backupCrdVersions: Output only. A list of the Backup for GKE CRD versions
      found in the cluster.
    cluster: Output only. The source cluster from which this Backup was
      created. Valid formats: - `projects/*/locations/*/clusters/*` -
      `projects/*/zones/*/clusters/*` This is inherited from the parent
      BackupPlan's cluster field.
    gkeVersion: Output only. GKE version
    k8sVersion: Output only. The Kubernetes server version of the source
      cluster.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class BackupCrdVersionsValue(_messages.Message):
    r"""Output only. A list of the Backup for GKE CRD versions found in the
    cluster.

    Messages:
      AdditionalProperty: An additional property for a BackupCrdVersionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        BackupCrdVersionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a BackupCrdVersionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  anthosVersion = _messages.StringField(1)
  backupCrdVersions = _messages.MessageField('BackupCrdVersionsValue', 2)
  cluster = _messages.StringField(3)
  gkeVersion = _messages.StringField(4)
  k8sVersion = _messages.StringField(5)


class ClusterResourceRestoreScope(_messages.Message):
  r"""Defines the scope of cluster-scoped resources to restore. Some group
  kinds are not reasonable choices for a restore, and will cause an error if
  selected here. Any scope selection that would restore "all valid" resources
  automatically excludes these group kinds. - gkebackup.gke.io/BackupJob -
  gkebackup.gke.io/RestoreJob - metrics.k8s.io/NodeMetrics -
  migration.k8s.io/StorageState - migration.k8s.io/StorageVersionMigration -
  Node - snapshot.storage.k8s.io/VolumeSnapshotContent -
  storage.k8s.io/CSINode Some group kinds are driven by restore configuration
  elsewhere, and will cause an error if selected here. - Namespace -
  PersistentVolume

  Fields:
    allGroupKinds: Optional. If True, all valid cluster-scoped resources will
      be restored. Mutually exclusive to any other field in the message.
    excludedGroupKinds: Optional. A list of cluster-scoped resource group
      kinds to NOT restore from the backup. If specified, all valid cluster-
      scoped resources will be restored except for those specified in the
      list. Mutually exclusive to any other field in the message.
    noGroupKinds: Optional. If True, no cluster-scoped resources will be
      restored. This has the same restore scope as if the message is not
      defined. Mutually exclusive to any other field in the message.
    selectedGroupKinds: Optional. A list of cluster-scoped resource group
      kinds to restore from the backup. If specified, only the selected
      resources will be restored. Mutually exclusive to any other field in the
      message.
  """

  allGroupKinds = _messages.BooleanField(1)
  excludedGroupKinds = _messages.MessageField('GroupKind', 2, repeated=True)
  noGroupKinds = _messages.BooleanField(3)
  selectedGroupKinds = _messages.MessageField('GroupKind', 4, repeated=True)


class Date(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class DayOfWeekList(_messages.Message):
  r"""Holds repeated DyasOfWeek values as a container.

  Enums:
    DaysOfWeekValueListEntryValuesEnum:

  Fields:
    daysOfWeek: Optional. A list of days of week.
  """

  class DaysOfWeekValueListEntryValuesEnum(_messages.Enum):
    r"""DaysOfWeekValueListEntryValuesEnum enum type.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  daysOfWeek = _messages.EnumField('DaysOfWeekValueListEntryValuesEnum', 1, repeated=True)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EncryptionKey(_messages.Message):
  r"""Defined a customer managed encryption key that will be used to encrypt
  Backup artifacts.

  Fields:
    gcpKmsEncryptionKey: Optional. Google Cloud KMS encryption key. Format:
      `projects/*/locations/*/keyRings/*/cryptoKeys/*`
  """

  gcpKmsEncryptionKey = _messages.StringField(1)


class ExclusionWindow(_messages.Message):
  r"""Defines a time window during which no backup should happen. All time and
  date are in UTC. Next id: 6

  Fields:
    daily: The exclusion window occurs every day if set to "True". Specifying
      this field to "False" is an error.
    daysOfWeek: The exclusion window occurs on these days of each week in UTC.
    duration: Required. Specifies duration of the window. Duration must be at
      most 23 hours for daily recurrence windows and days of week recurrence
      windows that includes all seven days a week to allow at least one hour
      for backup to happen.
    singleOccurrenceDate: No recurrence. The exclusion window occurs only once
      and on this date in UTC.
    startTime: Required. Specifies the start time of the window using time of
      the day in UTC.
  """

  daily = _messages.BooleanField(1)
  daysOfWeek = _messages.MessageField('DayOfWeekList', 2)
  duration = _messages.StringField(3)
  singleOccurrenceDate = _messages.MessageField('Date', 4)
  startTime = _messages.MessageField('TimeOfDay', 5)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GetBackupIndexDownloadUrlResponse(_messages.Message):
  r"""Response message for GetBackupIndexDownloadUrl.

  Fields:
    signedUrl: A string attribute.
  """

  signedUrl = _messages.StringField(1)


class GkebackupProjectsLocationsBackupPlansBackupsCreateRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansBackupsCreateRequest object.

  Fields:
    backup: A Backup resource to be passed as the request body.
    backupId: Optional. The client-provided short name for the Backup
      resource. This name must: - be between 1 and 63 characters long
      (inclusive) - consist of only lower-case ASCII letters, numbers, and
      dashes - start with a lower-case letter - end with a lower-case letter
      or number - be unique within the set of Backups in this BackupPlan
    parent: Required. The BackupPlan within which to create the Backup.
      Format: `projects/*/locations/*/backupPlans/*`
  """

  backup = _messages.MessageField('Backup', 1)
  backupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkebackupProjectsLocationsBackupPlansBackupsDeleteRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansBackupsDeleteRequest object.

  Fields:
    etag: Optional. If provided, this value must match the current value of
      the target Backup's etag field or the request is rejected.
    force: Optional. If set to true, any VolumeBackups below this Backup will
      also be deleted. Otherwise, the request will only succeed if the Backup
      has no VolumeBackups.
    name: Required. Name of the Backup resource. Format:
      `projects/*/locations/*/backupPlans/*/backups/*`
  """

  etag = _messages.StringField(1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)


class GkebackupProjectsLocationsBackupPlansBackupsGetBackupIndexDownloadUrlRequest(_messages.Message):
  r"""A
  GkebackupProjectsLocationsBackupPlansBackupsGetBackupIndexDownloadUrlRequest
  object.

  Fields:
    backup: Required. Full name of Backup resource. Format: projects/{project}
      /locations/{location}/backupPlans/{backup_plan}/backups/{backup}
  """

  backup = _messages.StringField(1, required=True)


class GkebackupProjectsLocationsBackupPlansBackupsGetIamPolicyRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansBackupsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GkebackupProjectsLocationsBackupPlansBackupsGetRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansBackupsGetRequest object.

  Fields:
    name: Required. Full name of the Backup resource. Format:
      `projects/*/locations/*/backupPlans/*/backups/*`
  """

  name = _messages.StringField(1, required=True)


class GkebackupProjectsLocationsBackupPlansBackupsListRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansBackupsListRequest object.

  Fields:
    filter: Optional. Field match expression used to filter the results.
    orderBy: Optional. Field by which to sort the results.
    pageSize: Optional. The target number of results to return in a single
      response. If not specified, a default value will be chosen by the
      service. Note that the response may include a partial list and a caller
      should only rely on the response's next_page_token to determine if there
      are more instances left to be queried.
    pageToken: Optional. The value of next_page_token received from a previous
      `ListBackups` call. Provide this to retrieve the subsequent page in a
      multi-page list of results. When paginating, all other parameters
      provided to `ListBackups` must match the call that provided the page
      token.
    parent: Required. The BackupPlan that contains the Backups to list.
      Format: `projects/*/locations/*/backupPlans/*`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class GkebackupProjectsLocationsBackupPlansBackupsPatchRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansBackupsPatchRequest object.

  Fields:
    backup: A Backup resource to be passed as the request body.
    name: Output only. The fully qualified name of the Backup.
      `projects/*/locations/*/backupPlans/*/backups/*`
    updateMask: Optional. This is used to specify the fields to be overwritten
      in the Backup targeted for update. The values for each of these updated
      fields will be taken from the `backup_plan` provided with this request.
      Field names are relative to the root of the resource. If no
      `update_mask` is provided, all fields in `backup` will be written to the
      target Backup resource. Note that OUTPUT_ONLY and IMMUTABLE fields in
      `backup` are ignored and are not used to update the target Backup.
  """

  backup = _messages.MessageField('Backup', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class GkebackupProjectsLocationsBackupPlansBackupsSetIamPolicyRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansBackupsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GkebackupProjectsLocationsBackupPlansBackupsTestIamPermissionsRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansBackupsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsGetIamPolicyRequest(_messages.Message):
  r"""A
  GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsGetRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsGetRequest
  object.

  Fields:
    name: Required. Full name of the VolumeBackup resource. Format:
      `projects/*/locations/*/backupPlans/*/backups/*/volumeBackups/*`
  """

  name = _messages.StringField(1, required=True)


class GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsListRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsListRequest
  object.

  Fields:
    filter: Optional. Field match expression used to filter the results.
    orderBy: Optional. Field by which to sort the results.
    pageSize: Optional. The target number of results to return in a single
      response. If not specified, a default value will be chosen by the
      service. Note that the response may include a partial list and a caller
      should only rely on the response's next_page_token to determine if there
      are more instances left to be queried.
    pageToken: Optional. The value of next_page_token received from a previous
      `ListVolumeBackups` call. Provide this to retrieve the subsequent page
      in a multi-page list of results. When paginating, all other parameters
      provided to `ListVolumeBackups` must match the call that provided the
      page token.
    parent: Required. The Backup that contains the VolumeBackups to list.
      Format: `projects/*/locations/*/backupPlans/*/backups/*`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsSetIamPolicyRequest(_messages.Message):
  r"""A
  GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsTestIamPermissionsRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansBackupsVolumeBackupsTestIamPermis
  sionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GkebackupProjectsLocationsBackupPlansCreateRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansCreateRequest object.

  Fields:
    backupPlan: A BackupPlan resource to be passed as the request body.
    backupPlanId: Required. The client-provided short name for the BackupPlan
      resource. This name must: - be between 1 and 63 characters long
      (inclusive) - consist of only lower-case ASCII letters, numbers, and
      dashes - start with a lower-case letter - end with a lower-case letter
      or number - be unique within the set of BackupPlans in this location
    parent: Required. The location within which to create the BackupPlan.
      Format: `projects/*/locations/*`
  """

  backupPlan = _messages.MessageField('BackupPlan', 1)
  backupPlanId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class GkebackupProjectsLocationsBackupPlansDeleteRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansDeleteRequest object.

  Fields:
    etag: Optional. If provided, this value must match the current value of
      the target BackupPlan's etag field or the request is rejected.
    name: Required. Fully qualified BackupPlan name. Format:
      `projects/*/locations/*/backupPlans/*`
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class GkebackupProjectsLocationsBackupPlansGetIamPolicyRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GkebackupProjectsLocationsBackupPlansGetRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansGetRequest object.

  Fields:
    name: Required. Fully qualified BackupPlan name. Format:
      `projects/*/locations/*/backupPlans/*`
  """

  name = _messages.StringField(1, required=True)


class GkebackupProjectsLocationsBackupPlansListRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansListRequest object.

  Fields:
    filter: Optional. Field match expression used to filter the results.
    orderBy: Optional. Field by which to sort the results.
    pageSize: Optional. The target number of results to return in a single
      response. If not specified, a default value will be chosen by the
      service. Note that the response may include a partial list and a caller
      should only rely on the response's next_page_token to determine if there
      are more instances left to be queried.
    pageToken: Optional. The value of next_page_token received from a previous
      `ListBackupPlans` call. Provide this to retrieve the subsequent page in
      a multi-page list of results. When paginating, all other parameters
      provided to `ListBackupPlans` must match the call that provided the page
      token.
    parent: Required. The location that contains the BackupPlans to list.
      Format: `projects/*/locations/*`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class GkebackupProjectsLocationsBackupPlansPatchRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansPatchRequest object.

  Fields:
    backupPlan: A BackupPlan resource to be passed as the request body.
    name: Output only. The full name of the BackupPlan resource. Format:
      `projects/*/locations/*/backupPlans/*`
    updateMask: Optional. This is used to specify the fields to be overwritten
      in the BackupPlan targeted for update. The values for each of these
      updated fields will be taken from the `backup_plan` provided with this
      request. Field names are relative to the root of the resource (e.g.,
      `description`, `backup_config.include_volume_data`, etc.) If no
      `update_mask` is provided, all fields in `backup_plan` will be written
      to the target BackupPlan resource. Note that OUTPUT_ONLY and IMMUTABLE
      fields in `backup_plan` are ignored and are not used to update the
      target BackupPlan.
  """

  backupPlan = _messages.MessageField('BackupPlan', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class GkebackupProjectsLocationsBackupPlansSetIamPolicyRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GkebackupProjectsLocationsBackupPlansTestIamPermissionsRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsBackupPlansTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GkebackupProjectsLocationsDeleteOperationsRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsDeleteOperationsRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class GkebackupProjectsLocationsGetRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class GkebackupProjectsLocationsListRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class GkebackupProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsOperationsCancelRequest object.

  Fields:
    googleLongrunningCancelOperationRequest: A
      GoogleLongrunningCancelOperationRequest resource to be passed as the
      request body.
    name: The name of the operation resource to be cancelled.
  """

  googleLongrunningCancelOperationRequest = _messages.MessageField('GoogleLongrunningCancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class GkebackupProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class GkebackupProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class GkebackupProjectsLocationsRestorePlansCreateRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansCreateRequest object.

  Fields:
    parent: Required. The location within which to create the RestorePlan.
      Format: `projects/*/locations/*`
    restorePlan: A RestorePlan resource to be passed as the request body.
    restorePlanId: Required. The client-provided short name for the
      RestorePlan resource. This name must: - be between 1 and 63 characters
      long (inclusive) - consist of only lower-case ASCII letters, numbers,
      and dashes - start with a lower-case letter - end with a lower-case
      letter or number - be unique within the set of RestorePlans in this
      location
  """

  parent = _messages.StringField(1, required=True)
  restorePlan = _messages.MessageField('RestorePlan', 2)
  restorePlanId = _messages.StringField(3)


class GkebackupProjectsLocationsRestorePlansDeleteRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansDeleteRequest object.

  Fields:
    etag: Optional. If provided, this value must match the current value of
      the target RestorePlan's etag field or the request is rejected.
    force: Optional. If set to true, any Restores below this RestorePlan will
      also be deleted. Otherwise, the request will only succeed if the
      RestorePlan has no Restores.
    name: Required. Fully qualified RestorePlan name. Format:
      `projects/*/locations/*/restorePlans/*`
  """

  etag = _messages.StringField(1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)


class GkebackupProjectsLocationsRestorePlansGetIamPolicyRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GkebackupProjectsLocationsRestorePlansGetRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansGetRequest object.

  Fields:
    name: Required. Fully qualified RestorePlan name. Format:
      `projects/*/locations/*/restorePlans/*`
  """

  name = _messages.StringField(1, required=True)


class GkebackupProjectsLocationsRestorePlansListRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansListRequest object.

  Fields:
    filter: Optional. Field match expression used to filter the results.
    orderBy: Optional. Field by which to sort the results.
    pageSize: Optional. The target number of results to return in a single
      response. If not specified, a default value will be chosen by the
      service. Note that the response may include a partial list and a caller
      should only rely on the response's next_page_token to determine if there
      are more instances left to be queried.
    pageToken: Optional. The value of next_page_token received from a previous
      `ListRestorePlans` call. Provide this to retrieve the subsequent page in
      a multi-page list of results. When paginating, all other parameters
      provided to `ListRestorePlans` must match the call that provided the
      page token.
    parent: Required. The location that contains the RestorePlans to list.
      Format: `projects/*/locations/*`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class GkebackupProjectsLocationsRestorePlansPatchRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansPatchRequest object.

  Fields:
    name: Output only. The full name of the RestorePlan resource. Format:
      `projects/*/locations/*/restorePlans/*`.
    restorePlan: A RestorePlan resource to be passed as the request body.
    updateMask: Optional. This is used to specify the fields to be overwritten
      in the RestorePlan targeted for update. The values for each of these
      updated fields will be taken from the `restore_plan` provided with this
      request. Field names are relative to the root of the resource. If no
      `update_mask` is provided, all fields in `restore_plan` will be written
      to the target RestorePlan resource. Note that OUTPUT_ONLY and IMMUTABLE
      fields in `restore_plan` are ignored and are not used to update the
      target RestorePlan.
  """

  name = _messages.StringField(1, required=True)
  restorePlan = _messages.MessageField('RestorePlan', 2)
  updateMask = _messages.StringField(3)


class GkebackupProjectsLocationsRestorePlansRestoresCreateRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansRestoresCreateRequest object.

  Fields:
    parent: Required. The RestorePlan within which to create the Restore.
      Format: `projects/*/locations/*/restorePlans/*`
    restore: A Restore resource to be passed as the request body.
    restoreId: Required. The client-provided short name for the Restore
      resource. This name must: - be between 1 and 63 characters long
      (inclusive) - consist of only lower-case ASCII letters, numbers, and
      dashes - start with a lower-case letter - end with a lower-case letter
      or number - be unique within the set of Restores in this RestorePlan.
  """

  parent = _messages.StringField(1, required=True)
  restore = _messages.MessageField('Restore', 2)
  restoreId = _messages.StringField(3)


class GkebackupProjectsLocationsRestorePlansRestoresDeleteRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansRestoresDeleteRequest object.

  Fields:
    etag: Optional. If provided, this value must match the current value of
      the target Restore's etag field or the request is rejected.
    force: Optional. If set to true, any VolumeRestores below this restore
      will also be deleted. Otherwise, the request will only succeed if the
      restore has no VolumeRestores.
    name: Required. Full name of the Restore Format:
      `projects/*/locations/*/restorePlans/*/restores/*`
  """

  etag = _messages.StringField(1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)


class GkebackupProjectsLocationsRestorePlansRestoresGetIamPolicyRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansRestoresGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GkebackupProjectsLocationsRestorePlansRestoresGetRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansRestoresGetRequest object.

  Fields:
    name: Required. Name of the restore resource. Format:
      `projects/*/locations/*/restorePlans/*/restores/*`
  """

  name = _messages.StringField(1, required=True)


class GkebackupProjectsLocationsRestorePlansRestoresListRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansRestoresListRequest object.

  Fields:
    filter: Optional. Field match expression used to filter the results.
    orderBy: Optional. Field by which to sort the results.
    pageSize: Optional. The target number of results to return in a single
      response. If not specified, a default value will be chosen by the
      service. Note that the response may include a partial list and a caller
      should only rely on the response's next_page_token to determine if there
      are more instances left to be queried.
    pageToken: Optional. The value of next_page_token received from a previous
      `ListRestores` call. Provide this to retrieve the subsequent page in a
      multi-page list of results. When paginating, all other parameters
      provided to `ListRestores` must match the call that provided the page
      token.
    parent: Required. The RestorePlan that contains the Restores to list.
      Format: `projects/*/locations/*/restorePlans/*`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class GkebackupProjectsLocationsRestorePlansRestoresPatchRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansRestoresPatchRequest object.

  Fields:
    name: Output only. The full name of the Restore resource. Format:
      `projects/*/locations/*/restorePlans/*/restores/*`
    restore: A Restore resource to be passed as the request body.
    updateMask: Optional. This is used to specify the fields to be overwritten
      in the Restore targeted for update. The values for each of these updated
      fields will be taken from the `restore` provided with this request.
      Field names are relative to the root of the resource. If no
      `update_mask` is provided, all fields in `restore` will be written to
      the target Restore resource. Note that OUTPUT_ONLY and IMMUTABLE fields
      in `restore` are ignored and are not used to update the target Restore.
  """

  name = _messages.StringField(1, required=True)
  restore = _messages.MessageField('Restore', 2)
  updateMask = _messages.StringField(3)


class GkebackupProjectsLocationsRestorePlansRestoresSetIamPolicyRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansRestoresSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GkebackupProjectsLocationsRestorePlansRestoresTestIamPermissionsRequest(_messages.Message):
  r"""A
  GkebackupProjectsLocationsRestorePlansRestoresTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresGetIamPolicyRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresGetIamPoli
  cyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresGetRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresGetRequest
  object.

  Fields:
    name: Required. Full name of the VolumeRestore resource. Format:
      `projects/*/locations/*/restorePlans/*/restores/*/volumeRestores/*`
  """

  name = _messages.StringField(1, required=True)


class GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresListRequest(_messages.Message):
  r"""A
  GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresListRequest
  object.

  Fields:
    filter: Optional. Field match expression used to filter the results.
    orderBy: Optional. Field by which to sort the results.
    pageSize: Optional. The target number of results to return in a single
      response. If not specified, a default value will be chosen by the
      service. Note that the response may include a partial list and a caller
      should only rely on the response's next_page_token to determine if there
      are more instances left to be queried.
    pageToken: Optional. The value of next_page_token received from a previous
      `ListVolumeRestores` call. Provide this to retrieve the subsequent page
      in a multi-page list of results. When paginating, all other parameters
      provided to `ListVolumeRestores` must match the call that provided the
      page token.
    parent: Required. The Restore that contains the VolumeRestores to list.
      Format: `projects/*/locations/*/restorePlans/*/restores/*`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresSetIamPolicyRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresSetIamPoli
  cyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresTestIamPermissionsRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansRestoresVolumeRestoresTestIamPer
  missionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GkebackupProjectsLocationsRestorePlansSetIamPolicyRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class GkebackupProjectsLocationsRestorePlansTestIamPermissionsRequest(_messages.Message):
  r"""A GkebackupProjectsLocationsRestorePlansTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class GoogleLongrunningCancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class GoogleLongrunningListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('GoogleLongrunningOperation', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class GroupKind(_messages.Message):
  r"""This is a direct map to the Kubernetes GroupKind type [GroupKind](https:
  //godoc.org/k8s.io/apimachinery/pkg/runtime/schema#GroupKind) and is used
  for identifying specific "types" of resources to restore.

  Fields:
    resourceGroup: Optional. API group string of a Kubernetes resource, e.g.
      "apiextensions.k8s.io", "storage.k8s.io", etc. Note: use empty string
      for core API group
    resourceKind: Optional. Kind of a Kubernetes resource, e.g.
      "CustomResourceDefinition", "StorageClass", etc.
  """

  resourceGroup = _messages.StringField(1)
  resourceKind = _messages.StringField(2)


class ListBackupPlansResponse(_messages.Message):
  r"""Response message for ListBackupPlans.

  Fields:
    backupPlans: The list of BackupPlans matching the given criteria.
    nextPageToken: A token which may be sent as page_token in a subsequent
      `ListBackupPlans` call to retrieve the next page of results. If this
      field is omitted or empty, then there are no more results to return.
    unreachable: Locations that could not be reached.
  """

  backupPlans = _messages.MessageField('BackupPlan', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListBackupsResponse(_messages.Message):
  r"""Response message for ListBackups.

  Fields:
    backups: The list of Backups matching the given criteria.
    nextPageToken: A token which may be sent as page_token in a subsequent
      `ListBackups` call to retrieve the next page of results. If this field
      is omitted or empty, then there are no more results to return.
  """

  backups = _messages.MessageField('Backup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListRestorePlansResponse(_messages.Message):
  r"""Response message for ListRestorePlans.

  Fields:
    nextPageToken: A token which may be sent as page_token in a subsequent
      `ListRestorePlans` call to retrieve the next page of results. If this
      field is omitted or empty, then there are no more results to return.
    restorePlans: The list of RestorePlans matching the given criteria.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  restorePlans = _messages.MessageField('RestorePlan', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListRestoresResponse(_messages.Message):
  r"""Response message for ListRestores.

  Fields:
    nextPageToken: A token which may be sent as page_token in a subsequent
      `ListRestores` call to retrieve the next page of results. If this field
      is omitted or empty, then there are no more results to return.
    restores: The list of Restores matching the given criteria.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  restores = _messages.MessageField('Restore', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListVolumeBackupsResponse(_messages.Message):
  r"""Response message for ListVolumeBackups.

  Fields:
    nextPageToken: A token which may be sent as page_token in a subsequent
      `ListVolumeBackups` call to retrieve the next page of results. If this
      field is omitted or empty, then there are no more results to return.
    volumeBackups: The list of VolumeBackups matching the given criteria.
  """

  nextPageToken = _messages.StringField(1)
  volumeBackups = _messages.MessageField('VolumeBackup', 2, repeated=True)


class ListVolumeRestoresResponse(_messages.Message):
  r"""Response message for ListVolumeRestores.

  Fields:
    nextPageToken: A token which may be sent as page_token in a subsequent
      `ListVolumeRestores` call to retrieve the next page of results. If this
      field is omitted or empty, then there are no more results to return.
    volumeRestores: The list of VolumeRestores matching the given criteria.
  """

  nextPageToken = _messages.StringField(1)
  volumeRestores = _messages.MessageField('VolumeRestore', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class NamespacedName(_messages.Message):
  r"""A reference to a namespaced resource in Kubernetes.

  Fields:
    name: Optional. The name of the Kubernetes resource.
    namespace: Optional. The Namespace of the Kubernetes resource.
  """

  name = _messages.StringField(1)
  namespace = _messages.StringField(2)


class NamespacedNames(_messages.Message):
  r"""A list of namespaced Kubernetes resources.

  Fields:
    namespacedNames: Optional. A list of namespaced Kubernetes resources.
  """

  namespacedNames = _messages.MessageField('NamespacedName', 1, repeated=True)


class Namespaces(_messages.Message):
  r"""A list of Kubernetes Namespaces

  Fields:
    namespaces: Optional. A list of Kubernetes Namespaces
  """

  namespaces = _messages.StringField(1, repeated=True)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class ResourceFilter(_messages.Message):
  r"""ResourceFilter specifies matching criteria to limit the scope of a
  change to a specific set of kubernetes resources that are selected for
  restoration from a backup.

  Fields:
    groupKinds: Optional. (Filtering parameter) Any resource subject to
      transformation must belong to one of the listed "types". If this field
      is not provided, no type filtering will be performed (all resources of
      all types matching previous filtering parameters will be candidates for
      transformation).
    jsonPath: Optional. This is a [JSONPath] (https://github.com/json-
      path/JsonPath/blob/master/README.md) expression that matches specific
      fields of candidate resources and it operates as a filtering parameter
      (resources that are not matched with this expression will not be
      candidates for transformation).
    namespaces: Optional. (Filtering parameter) Any resource subject to
      transformation must be contained within one of the listed Kubernetes
      Namespace in the Backup. If this field is not provided, no namespace
      filtering will be performed (all resources in all Namespaces, including
      all cluster-scoped resources, will be candidates for transformation). To
      mix cluster-scoped and namespaced resources in the same rule, use an
      empty string ("") as one of the target namespaces.
  """

  groupKinds = _messages.MessageField('GroupKind', 1, repeated=True)
  jsonPath = _messages.StringField(2)
  namespaces = _messages.StringField(3, repeated=True)


class Restore(_messages.Message):
  r"""Represents both a request to Restore some portion of a Backup into a
  target GKE cluster and a record of the restore operation itself. Next id: 18

  Enums:
    StateValueValuesEnum: Output only. The current state of the Restore.

  Messages:
    LabelsValue: A set of custom labels supplied by user.

  Fields:
    backup: Required. Immutable. A reference to the Backup used as the source
      from which this Restore will restore. Note that this Backup must be a
      sub-resource of the RestorePlan's backup_plan. Format:
      `projects/*/locations/*/backupPlans/*/backups/*`.
    cluster: Output only. The target cluster into which this Restore will
      restore data. Valid formats: - `projects/*/locations/*/clusters/*` -
      `projects/*/zones/*/clusters/*` Inherited from parent RestorePlan's
      cluster value.
    completeTime: Output only. Timestamp of when the restore operation
      completed.
    createTime: Output only. The timestamp when this Restore resource was
      created.
    description: User specified descriptive string for this Restore.
    etag: Output only. `etag` is used for optimistic concurrency control as a
      way to help prevent simultaneous updates of a restore from overwriting
      each other. It is strongly suggested that systems make use of the `etag`
      in the read-modify-write cycle to perform restore updates in order to
      avoid race conditions: An `etag` is returned in the response to
      `GetRestore`, and systems are expected to put that etag in the request
      to `UpdateRestore` or `DeleteRestore` to ensure that their change will
      be applied to the same version of the resource.
    labels: A set of custom labels supplied by user.
    name: Output only. The full name of the Restore resource. Format:
      `projects/*/locations/*/restorePlans/*/restores/*`
    resourcesExcludedCount: Output only. Number of resources excluded during
      the restore execution.
    resourcesFailedCount: Output only. Number of resources that failed to be
      restored during the restore execution.
    resourcesRestoredCount: Output only. Number of resources restored during
      the restore execution.
    restoreConfig: Output only. Configuration of the Restore. Inherited from
      parent RestorePlan's restore_config.
    state: Output only. The current state of the Restore.
    stateReason: Output only. Human-readable description of why the Restore is
      in its current state.
    uid: Output only. Server generated global unique identifier of
      [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier)
      format.
    updateTime: Output only. The timestamp when this Restore resource was last
      updated.
    volumesRestoredCount: Output only. Number of volumes restored during the
      restore execution.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the Restore.

    Values:
      STATE_UNSPECIFIED: The Restore resource is in the process of being
        created.
      CREATING: The Restore resource has been created and the associated
        RestoreJob Kubernetes resource has been injected into target cluster.
      IN_PROGRESS: The gkebackup agent in the cluster has begun executing the
        restore operation.
      SUCCEEDED: The restore operation has completed successfully. Restored
        workloads may not yet be operational.
      FAILED: The restore operation has failed.
      DELETING: This Restore resource is in the process of being deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    IN_PROGRESS = 2
    SUCCEEDED = 3
    FAILED = 4
    DELETING = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""A set of custom labels supplied by user.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  backup = _messages.StringField(1)
  cluster = _messages.StringField(2)
  completeTime = _messages.StringField(3)
  createTime = _messages.StringField(4)
  description = _messages.StringField(5)
  etag = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  resourcesExcludedCount = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  resourcesFailedCount = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  resourcesRestoredCount = _messages.IntegerField(11, variant=_messages.Variant.INT32)
  restoreConfig = _messages.MessageField('RestoreConfig', 12)
  state = _messages.EnumField('StateValueValuesEnum', 13)
  stateReason = _messages.StringField(14)
  uid = _messages.StringField(15)
  updateTime = _messages.StringField(16)
  volumesRestoredCount = _messages.IntegerField(17, variant=_messages.Variant.INT32)


class RestoreConfig(_messages.Message):
  r"""Configuration of a restore. Next id: 12

  Enums:
    ClusterResourceConflictPolicyValueValuesEnum: Optional. Defines the
      behavior for handling the situation where cluster-scoped resources being
      restored already exist in the target cluster. This MUST be set to a
      value other than CLUSTER_RESOURCE_CONFLICT_POLICY_UNSPECIFIED if
      cluster_resource_restore_scope is not empty.
    NamespacedResourceRestoreModeValueValuesEnum: Optional. Defines the
      behavior for handling the situation where sets of namespaced resources
      being restored already exist in the target cluster. This MUST be set to
      a value other than NAMESPACED_RESOURCE_RESTORE_MODE_UNSPECIFIED.
    VolumeDataRestorePolicyValueValuesEnum: Optional. Specifies the mechanism
      to be used to restore volume data. Default:
      VOLUME_DATA_RESTORE_POLICY_UNSPECIFIED (will be treated as
      NO_VOLUME_DATA_RESTORATION).

  Fields:
    allNamespaces: Restore all namespaced resources in the Backup if set to
      "True". Specifying this field to "False" is an error.
    clusterResourceConflictPolicy: Optional. Defines the behavior for handling
      the situation where cluster-scoped resources being restored already
      exist in the target cluster. This MUST be set to a value other than
      CLUSTER_RESOURCE_CONFLICT_POLICY_UNSPECIFIED if
      cluster_resource_restore_scope is not empty.
    clusterResourceRestoreScope: Optional. Identifies the cluster-scoped
      resources to restore from the Backup. Not specifying it means NO cluster
      resource will be restored.
    excludedNamespaces: A list of selected namespaces excluded from
      restoration. All namespaces except those in this list will be restored.
    namespacedResourceRestoreMode: Optional. Defines the behavior for handling
      the situation where sets of namespaced resources being restored already
      exist in the target cluster. This MUST be set to a value other than
      NAMESPACED_RESOURCE_RESTORE_MODE_UNSPECIFIED.
    noNamespaces: Do not restore any namespaced resources if set to "True".
      Specifying this field to "False" is not allowed.
    selectedApplications: A list of selected ProtectedApplications to restore.
      The listed ProtectedApplications and all the resources to which they
      refer will be restored.
    selectedNamespaces: A list of selected Namespaces to restore from the
      Backup. The listed Namespaces and all resources contained in them will
      be restored.
    substitutionRules: Optional. A list of transformation rules to be applied
      against Kubernetes resources as they are selected for restoration from a
      Backup. Rules are executed in order defined - this order matters, as
      changes made by a rule may impact the filtering logic of subsequent
      rules. An empty list means no substitution will occur.
    transformationRules: Optional. A list of transformation rules to be
      applied against Kubernetes resources as they are selected for
      restoration from a Backup. Rules are executed in order defined - this
      order matters, as changes made by a rule may impact the filtering logic
      of subsequent rules. An empty list means no transformation will occur.
    volumeDataRestorePolicy: Optional. Specifies the mechanism to be used to
      restore volume data. Default: VOLUME_DATA_RESTORE_POLICY_UNSPECIFIED
      (will be treated as NO_VOLUME_DATA_RESTORATION).
  """

  class ClusterResourceConflictPolicyValueValuesEnum(_messages.Enum):
    r"""Optional. Defines the behavior for handling the situation where
    cluster-scoped resources being restored already exist in the target
    cluster. This MUST be set to a value other than
    CLUSTER_RESOURCE_CONFLICT_POLICY_UNSPECIFIED if
    cluster_resource_restore_scope is not empty.

    Values:
      CLUSTER_RESOURCE_CONFLICT_POLICY_UNSPECIFIED: Unspecified. Only allowed
        if no cluster-scoped resources will be restored.
      USE_EXISTING_VERSION: Do not attempt to restore the conflicting
        resource.
      USE_BACKUP_VERSION: Delete the existing version before re-creating it
        from the Backup. This is a dangerous option which could cause
        unintentional data loss if used inappropriately. For example, deleting
        a CRD will cause Kubernetes to delete all CRs of that type.
    """
    CLUSTER_RESOURCE_CONFLICT_POLICY_UNSPECIFIED = 0
    USE_EXISTING_VERSION = 1
    USE_BACKUP_VERSION = 2

  class NamespacedResourceRestoreModeValueValuesEnum(_messages.Enum):
    r"""Optional. Defines the behavior for handling the situation where sets
    of namespaced resources being restored already exist in the target
    cluster. This MUST be set to a value other than
    NAMESPACED_RESOURCE_RESTORE_MODE_UNSPECIFIED.

    Values:
      NAMESPACED_RESOURCE_RESTORE_MODE_UNSPECIFIED: Unspecified (invalid).
      DELETE_AND_RESTORE: When conflicting top-level resources (either
        Namespaces or ProtectedApplications, depending upon the scope) are
        encountered, this will first trigger a delete of the conflicting
        resource AND ALL OF ITS REFERENCED RESOURCES (e.g., all resources in
        the Namespace or all resources referenced by the ProtectedApplication)
        before restoring the resources from the Backup. This mode should only
        be used when you are intending to revert some portion of a cluster to
        an earlier state.
      FAIL_ON_CONFLICT: If conflicting top-level resources (either Namespaces
        or ProtectedApplications, depending upon the scope) are encountered at
        the beginning of a restore process, the Restore will fail. If a
        conflict occurs during the restore process itself (e.g., because an
        out of band process creates conflicting resources), a conflict will be
        reported.
    """
    NAMESPACED_RESOURCE_RESTORE_MODE_UNSPECIFIED = 0
    DELETE_AND_RESTORE = 1
    FAIL_ON_CONFLICT = 2

  class VolumeDataRestorePolicyValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies the mechanism to be used to restore volume data.
    Default: VOLUME_DATA_RESTORE_POLICY_UNSPECIFIED (will be treated as
    NO_VOLUME_DATA_RESTORATION).

    Values:
      VOLUME_DATA_RESTORE_POLICY_UNSPECIFIED: Unspecified (illegal).
      RESTORE_VOLUME_DATA_FROM_BACKUP: For each PVC to be restored, create a
        new underlying volume and PV from the corresponding VolumeBackup
        contained within the Backup.
      REUSE_VOLUME_HANDLE_FROM_BACKUP: For each PVC to be restored, attempt to
        reuse the original PV contained in the Backup (with its original
        underlying volume). This option is likely only usable when restoring a
        workload to its original cluster.
      NO_VOLUME_DATA_RESTORATION: For each PVC to be restored, create PVC
        without any particular action to restore data. In this case, the
        normal Kubernetes provisioning logic would kick in, and this would
        likely result in either dynamically provisioning blank PVs or binding
        to statically provisioned PVs.
    """
    VOLUME_DATA_RESTORE_POLICY_UNSPECIFIED = 0
    RESTORE_VOLUME_DATA_FROM_BACKUP = 1
    REUSE_VOLUME_HANDLE_FROM_BACKUP = 2
    NO_VOLUME_DATA_RESTORATION = 3

  allNamespaces = _messages.BooleanField(1)
  clusterResourceConflictPolicy = _messages.EnumField('ClusterResourceConflictPolicyValueValuesEnum', 2)
  clusterResourceRestoreScope = _messages.MessageField('ClusterResourceRestoreScope', 3)
  excludedNamespaces = _messages.MessageField('Namespaces', 4)
  namespacedResourceRestoreMode = _messages.EnumField('NamespacedResourceRestoreModeValueValuesEnum', 5)
  noNamespaces = _messages.BooleanField(6)
  selectedApplications = _messages.MessageField('NamespacedNames', 7)
  selectedNamespaces = _messages.MessageField('Namespaces', 8)
  substitutionRules = _messages.MessageField('SubstitutionRule', 9, repeated=True)
  transformationRules = _messages.MessageField('TransformationRule', 10, repeated=True)
  volumeDataRestorePolicy = _messages.EnumField('VolumeDataRestorePolicyValueValuesEnum', 11)


class RestorePlan(_messages.Message):
  r"""The configuration of a potential series of Restore operations to be
  performed against Backups belong to a particular BackupPlan. Next id: 13

  Enums:
    StateValueValuesEnum: Output only. State of the RestorePlan. This State
      field reflects the various stages a RestorePlan can be in during the
      Create operation.

  Messages:
    LabelsValue: Optional. A set of custom labels supplied by user.

  Fields:
    backupPlan: Required. Immutable. A reference to the BackupPlan from which
      Backups may be used as the source for Restores created via this
      RestorePlan. Format: `projects/*/locations/*/backupPlans/*`.
    cluster: Required. Immutable. The target cluster into which Restores
      created via this RestorePlan will restore data. NOTE: the cluster's
      region must be the same as the RestorePlan. Valid formats: -
      `projects/*/locations/*/clusters/*` - `projects/*/zones/*/clusters/*`
    createTime: Output only. The timestamp when this RestorePlan resource was
      created.
    description: Optional. User specified descriptive string for this
      RestorePlan.
    etag: Output only. `etag` is used for optimistic concurrency control as a
      way to help prevent simultaneous updates of a restore from overwriting
      each other. It is strongly suggested that systems make use of the `etag`
      in the read-modify-write cycle to perform restore updates in order to
      avoid race conditions: An `etag` is returned in the response to
      `GetRestorePlan`, and systems are expected to put that etag in the
      request to `UpdateRestorePlan` or `DeleteRestorePlan` to ensure that
      their change will be applied to the same version of the resource.
    labels: Optional. A set of custom labels supplied by user.
    name: Output only. The full name of the RestorePlan resource. Format:
      `projects/*/locations/*/restorePlans/*`.
    restoreConfig: Required. Configuration of Restores created via this
      RestorePlan.
    state: Output only. State of the RestorePlan. This State field reflects
      the various stages a RestorePlan can be in during the Create operation.
    stateReason: Output only. Human-readable description of why RestorePlan is
      in the current `state`
    uid: Output only. Server generated global unique identifier of
      [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier)
      format.
    updateTime: Output only. The timestamp when this RestorePlan resource was
      last updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the RestorePlan. This State field reflects the
    various stages a RestorePlan can be in during the Create operation.

    Values:
      STATE_UNSPECIFIED: Default first value for Enums.
      CLUSTER_PENDING: Waiting for cluster state to be RUNNING.
      READY: The RestorePlan has successfully been created and is ready for
        Restores.
      FAILED: RestorePlan creation has failed.
      DELETING: The RestorePlan is in the process of being deleted.
    """
    STATE_UNSPECIFIED = 0
    CLUSTER_PENDING = 1
    READY = 2
    FAILED = 3
    DELETING = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. A set of custom labels supplied by user.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  backupPlan = _messages.StringField(1)
  cluster = _messages.StringField(2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  etag = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  restoreConfig = _messages.MessageField('RestoreConfig', 8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  stateReason = _messages.StringField(10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class RetentionPolicy(_messages.Message):
  r"""RetentionPolicy defines a Backup retention policy for a BackupPlan.

  Fields:
    backupDeleteLockDays: Optional. Minimum age for Backups created via this
      BackupPlan (in days). This field MUST be an integer value between 0-90
      (inclusive). A Backup created under this BackupPlan will NOT be
      deletable until it reaches Backup's (create_time +
      backup_delete_lock_days). Updating this field of a BackupPlan does NOT
      affect existing Backups under it. Backups created AFTER a successful
      update will inherit the new value. Default: 0 (no delete blocking)
    backupRetainDays: Optional. The default maximum age of a Backup created
      via this BackupPlan. This field MUST be an integer value >= 0 and <=
      365. If specified, a Backup created under this BackupPlan will be
      automatically deleted after its age reaches (create_time +
      backup_retain_days). If not specified, Backups created under this
      BackupPlan will NOT be subject to automatic deletion. Updating this
      field does NOT affect existing Backups under it. Backups created AFTER a
      successful update will automatically pick up the new value. NOTE:
      backup_retain_days must be >= backup_delete_lock_days. If cron_schedule
      is defined, then this must be <= 360 * the creation interval. Default: 0
      (no automatic deletion)
    locked: Optional. This flag denotes whether the retention policy of this
      BackupPlan is locked. If set to True, no further update is allowed on
      this policy, including the `locked` field itself. Default: False
  """

  backupDeleteLockDays = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  backupRetainDays = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  locked = _messages.BooleanField(3)


class RpoConfig(_messages.Message):
  r"""Defines RPO scheduling configuration for automatically creating Backups
  via this BackupPlan. Next id: 3

  Fields:
    exclusionWindows: Optional. User specified time windows during which
      backup can NOT happen for this BackupPlan - backups should start and
      finish outside of any given exclusion window. Note: backup jobs will be
      scheduled to start and finish outside the duration of the window as much
      as possible, but running jobs will not get canceled when it runs into
      the window. We only support single exclusion_window for a BackupPLan
      currently. All the time and date values in exclusion_windows entry in
      the API are in UTC.
    targetRpoMinutes: Required. Defines the target RPO for the BackupPlan in
      minutes, which means the target maximum data loss in time that is
      acceptable for this BackupPlan. This must be at least 1.
  """

  exclusionWindows = _messages.MessageField('ExclusionWindow', 1, repeated=True)
  targetRpoMinutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class Schedule(_messages.Message):
  r"""Defines scheduling parameters for automatically creating Backups via
  this BackupPlan.

  Fields:
    cronSchedule: Optional. A standard [cron](https://wikipedia.com/wiki/cron)
      string that defines a repeating schedule for creating Backups via this
      BackupPlan. This is mutually exclusive with the rpo_config field since
      at most one schedule can be defined for a BackupPlan. If this is
      defined, then backup_retain_days must also be defined. Default (empty):
      no automatic backup creation will occur.
    nextScheduledBackupTime: Output only. Start time of next scheduled backup
      under this BackupPlan by either cron_schedule or rpo config.
    paused: Optional. This flag denotes whether automatic Backup creation is
      paused for this BackupPlan. Default: False
    rpoConfig: Optional. Defines the RPO schedule configuration for this
      BackupPlan. This is mutually exclusive with the cron_schedule field
      since at most one schedule can be defined for a BackupPLan. If this is
      defined, then backup_retain_days must also be defined. Default (empty):
      no automatic backup creation will occur.
  """

  cronSchedule = _messages.StringField(1)
  nextScheduledBackupTime = _messages.StringField(2)
  paused = _messages.BooleanField(3)
  rpoConfig = _messages.MessageField('RpoConfig', 4)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class SubstitutionRule(_messages.Message):
  r"""A transformation rule to be applied against Kubernetes resources as they
  are selected for restoration from a Backup. A rule contains both filtering
  logic (which resources are subject to substitution) and substitution logic.

  Fields:
    newValue: Optional. This is the new value to set for any fields that pass
      the filtering and selection criteria. To remove a value from a
      Kubernetes resource, either leave this field unspecified, or set it to
      the empty string ("").
    originalValuePattern: Optional. (Filtering parameter) This is a [regular
      expression] (https://en.wikipedia.org/wiki/Regular_expression) that is
      compared against the fields matched by the target_json_path expression
      (and must also have passed the previous filters). Substitution will not
      be performed against fields whose value does not match this expression.
      If this field is NOT specified, then ALL fields matched by the
      target_json_path expression will undergo substitution. Note that an
      empty (e.g., "", rather than unspecified) value for this field will only
      match empty fields.
    targetGroupKinds: Optional. (Filtering parameter) Any resource subject to
      substitution must belong to one of the listed "types". If this field is
      not provided, no type filtering will be performed (all resources of all
      types matching previous filtering parameters will be candidates for
      substitution).
    targetJsonPath: Required. This is a [JSONPath]
      (https://kubernetes.io/docs/reference/kubectl/jsonpath/) expression that
      matches specific fields of candidate resources and it operates as both a
      filtering parameter (resources that are not matched with this expression
      will not be candidates for substitution) as well as a field identifier
      (identifies exactly which fields out of the candidate resources will be
      modified).
    targetNamespaces: Optional. (Filtering parameter) Any resource subject to
      substitution must be contained within one of the listed Kubernetes
      Namespace in the Backup. If this field is not provided, no namespace
      filtering will be performed (all resources in all Namespaces, including
      all cluster-scoped resources, will be candidates for substitution). To
      mix cluster-scoped and namespaced resources in the same rule, use an
      empty string ("") as one of the target namespaces.
  """

  newValue = _messages.StringField(1)
  originalValuePattern = _messages.StringField(2)
  targetGroupKinds = _messages.MessageField('GroupKind', 3, repeated=True)
  targetJsonPath = _messages.StringField(4)
  targetNamespaces = _messages.StringField(5, repeated=True)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TimeOfDay(_messages.Message):
  r"""Represents a time of day. The date and time zone are either not
  significant or are specified elsewhere. An API may choose to allow leap
  seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.

  Fields:
    hours: Hours of day in 24 hour format. Should be from 0 to 23. An API may
      choose to allow the value "24:00:00" for scenarios like business closing
      time.
    minutes: Minutes of hour of day. Must be from 0 to 59.
    nanos: Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999.
    seconds: Seconds of minutes of the time. Must normally be from 0 to 59. An
      API may allow the value 60 if it allows leap-seconds.
  """

  hours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class TransformationRule(_messages.Message):
  r"""A transformation rule to be applied against Kubernetes resources as they
  are selected for restoration from a Backup. A rule contains both filtering
  logic (which resources are subject to transform) and transformation logic.

  Fields:
    description: Optional. The description is a user specified string
      description of the transformation rule.
    fieldActions: Required. A list of transformation rule actions to take
      against candidate resources. Actions are executed in order defined -
      this order matters, as they could potentially interfere with each other
      and the first operation could affect the outcome of the second
      operation.
    resourceFilter: Optional. This field is used to specify a set of fields
      that should be used to determine which resources in backup should be
      acted upon by the supplied transformation rule actions, and this will
      ensure that only specific resources are affected by transformation rule
      actions.
  """

  description = _messages.StringField(1)
  fieldActions = _messages.MessageField('TransformationRuleAction', 2, repeated=True)
  resourceFilter = _messages.MessageField('ResourceFilter', 3)


class TransformationRuleAction(_messages.Message):
  r"""TransformationRuleAction defines a TransformationRule action based on
  the JSON Patch RFC (https://www.rfc-editor.org/rfc/rfc6902)

  Enums:
    OpValueValuesEnum: Required. op specifies the operation to perform.

  Fields:
    fromPath: Optional. A string containing a JSON Pointer value that
      references the location in the target document to move the value from.
    op: Required. op specifies the operation to perform.
    path: Optional. A string containing a JSON-Pointer value that references a
      location within the target document where the operation is performed.
    value: Optional. A string that specifies the desired value in string
      format to use for transformation.
  """

  class OpValueValuesEnum(_messages.Enum):
    r"""Required. op specifies the operation to perform.

    Values:
      OP_UNSPECIFIED: Unspecified operation
      REMOVE: The "remove" operation removes the value at the target location.
      MOVE: The "move" operation removes the value at a specified location and
        adds it to the target location.
      COPY: The "copy" operation copies the value at a specified location to
        the target location.
      ADD: The "add" operation performs one of the following functions,
        depending upon what the target location references: 1. If the target
        location specifies an array index, a new value is inserted into the
        array at the specified index. 2. If the target location specifies an
        object member that does not already exist, a new member is added to
        the object. 3. If the target location specifies an object member that
        does exist, that member's value is replaced.
      TEST: The "test" operation tests that a value at the target location is
        equal to a specified value.
      REPLACE: The "replace" operation replaces the value at the target
        location with a new value. The operation object MUST contain a "value"
        member whose content specifies the replacement value.
    """
    OP_UNSPECIFIED = 0
    REMOVE = 1
    MOVE = 2
    COPY = 3
    ADD = 4
    TEST = 5
    REPLACE = 6

  fromPath = _messages.StringField(1)
  op = _messages.EnumField('OpValueValuesEnum', 2)
  path = _messages.StringField(3)
  value = _messages.StringField(4)


class VolumeBackup(_messages.Message):
  r"""Represents the backup of a specific persistent volume as a component of
  a Backup - both the record of the operation and a pointer to the underlying
  storage-specific artifacts. Next id: 14

  Enums:
    FormatValueValuesEnum: Output only. The format used for the volume backup.
    StateValueValuesEnum: Output only. The current state of this VolumeBackup.

  Fields:
    completeTime: Output only. The timestamp when the associated underlying
      volume backup operation completed.
    createTime: Output only. The timestamp when this VolumeBackup resource was
      created.
    diskSizeBytes: Output only. The minimum size of the disk to which this
      VolumeBackup can be restored.
    etag: Output only. `etag` is used for optimistic concurrency control as a
      way to help prevent simultaneous updates of a volume backup from
      overwriting each other. It is strongly suggested that systems make use
      of the `etag` in the read-modify-write cycle to perform volume backup
      updates in order to avoid race conditions.
    format: Output only. The format used for the volume backup.
    name: Output only. The full name of the VolumeBackup resource. Format:
      `projects/*/locations/*/backupPlans/*/backups/*/volumeBackups/*`.
    sourcePvc: Output only. A reference to the source Kubernetes PVC from
      which this VolumeBackup was created.
    state: Output only. The current state of this VolumeBackup.
    stateMessage: Output only. A human readable message explaining why the
      VolumeBackup is in its current state.
    storageBytes: Output only. The aggregate size of the underlying artifacts
      associated with this VolumeBackup in the backup storage. This may change
      over time when multiple backups of the same volume share the same backup
      storage location. In particular, this is likely to increase in size when
      the immediately preceding backup of the same volume is deleted.
    uid: Output only. Server generated global unique identifier of
      [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier)
      format.
    updateTime: Output only. The timestamp when this VolumeBackup resource was
      last updated.
    volumeBackupHandle: Output only. A storage system-specific opaque handle
      to the underlying volume backup.
  """

  class FormatValueValuesEnum(_messages.Enum):
    r"""Output only. The format used for the volume backup.

    Values:
      VOLUME_BACKUP_FORMAT_UNSPECIFIED: Default value, not specified.
      GCE_PERSISTENT_DISK: Compute Engine Persistent Disk snapshot based
        volume backup.
    """
    VOLUME_BACKUP_FORMAT_UNSPECIFIED = 0
    GCE_PERSISTENT_DISK = 1

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of this VolumeBackup.

    Values:
      STATE_UNSPECIFIED: This is an illegal state and should not be
        encountered.
      CREATING: A volume for the backup was identified and backup process is
        about to start.
      SNAPSHOTTING: The volume backup operation has begun and is in the
        initial "snapshot" phase of the process. Any defined
        ProtectedApplication "pre" hooks will be executed before entering this
        state and "post" hooks will be executed upon leaving this state.
      UPLOADING: The snapshot phase of the volume backup operation has
        completed and the snapshot is now being uploaded to backup storage.
      SUCCEEDED: The volume backup operation has completed successfully.
      FAILED: The volume backup operation has failed.
      DELETING: This VolumeBackup resource (and its associated artifacts) is
        in the process of being deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    SNAPSHOTTING = 2
    UPLOADING = 3
    SUCCEEDED = 4
    FAILED = 5
    DELETING = 6

  completeTime = _messages.StringField(1)
  createTime = _messages.StringField(2)
  diskSizeBytes = _messages.IntegerField(3)
  etag = _messages.StringField(4)
  format = _messages.EnumField('FormatValueValuesEnum', 5)
  name = _messages.StringField(6)
  sourcePvc = _messages.MessageField('NamespacedName', 7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  stateMessage = _messages.StringField(9)
  storageBytes = _messages.IntegerField(10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)
  volumeBackupHandle = _messages.StringField(13)


class VolumeRestore(_messages.Message):
  r"""Represents the operation of restoring a volume from a VolumeBackup. Next
  id: 13

  Enums:
    StateValueValuesEnum: Output only. The current state of this
      VolumeRestore.
    VolumeTypeValueValuesEnum: Output only. The type of volume provisioned

  Fields:
    completeTime: Output only. The timestamp when the associated underlying
      volume restoration completed.
    createTime: Output only. The timestamp when this VolumeRestore resource
      was created.
    etag: Output only. `etag` is used for optimistic concurrency control as a
      way to help prevent simultaneous updates of a volume restore from
      overwriting each other. It is strongly suggested that systems make use
      of the `etag` in the read-modify-write cycle to perform volume restore
      updates in order to avoid race conditions.
    name: Output only. Full name of the VolumeRestore resource. Format:
      `projects/*/locations/*/restorePlans/*/restores/*/volumeRestores/*`
    state: Output only. The current state of this VolumeRestore.
    stateMessage: Output only. A human readable message explaining why the
      VolumeRestore is in its current state.
    targetPvc: Output only. The reference to the target Kubernetes PVC to be
      restored.
    uid: Output only. Server generated global unique identifier of
      [UUID](https://en.wikipedia.org/wiki/Universally_unique_identifier)
      format.
    updateTime: Output only. The timestamp when this VolumeRestore resource
      was last updated.
    volumeBackup: Output only. The full name of the VolumeBackup from which
      the volume will be restored. Format:
      `projects/*/locations/*/backupPlans/*/backups/*/volumeBackups/*`.
    volumeHandle: Output only. A storage system-specific opaque handler to the
      underlying volume created for the target PVC from the volume backup.
    volumeType: Output only. The type of volume provisioned
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of this VolumeRestore.

    Values:
      STATE_UNSPECIFIED: This is an illegal state and should not be
        encountered.
      CREATING: A volume for the restore was identified and restore process is
        about to start.
      RESTORING: The volume is currently being restored.
      SUCCEEDED: The volume has been successfully restored.
      FAILED: The volume restoration process failed.
      DELETING: This VolumeRestore resource is in the process of being
        deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    RESTORING = 2
    SUCCEEDED = 3
    FAILED = 4
    DELETING = 5

  class VolumeTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of volume provisioned

    Values:
      VOLUME_TYPE_UNSPECIFIED: Default
      GCE_PERSISTENT_DISK: Compute Engine Persistent Disk volume
    """
    VOLUME_TYPE_UNSPECIFIED = 0
    GCE_PERSISTENT_DISK = 1

  completeTime = _messages.StringField(1)
  createTime = _messages.StringField(2)
  etag = _messages.StringField(3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  stateMessage = _messages.StringField(6)
  targetPvc = _messages.MessageField('NamespacedName', 7)
  uid = _messages.StringField(8)
  updateTime = _messages.StringField(9)
  volumeBackup = _messages.StringField(10)
  volumeHandle = _messages.StringField(11)
  volumeType = _messages.EnumField('VolumeTypeValueValuesEnum', 12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

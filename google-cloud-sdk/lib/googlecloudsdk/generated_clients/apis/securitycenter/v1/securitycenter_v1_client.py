"""Generated client library for securitycenter version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.securitycenter.v1 import securitycenter_v1_messages as messages


class SecuritycenterV1(base_api.BaseApiClient):
  """Generated client library for service securitycenter version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://securitycenter.googleapis.com/'
  MTLS_BASE_URL = 'https://securitycenter.mtls.googleapis.com/'

  _PACKAGE = 'securitycenter'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'SecuritycenterV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new securitycenter handle."""
    url = url or self.BASE_URL
    super(SecuritycenterV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.folders_assets = self.FoldersAssetsService(self)
    self.folders_bigQueryExports = self.FoldersBigQueryExportsService(self)
    self.folders_eventThreatDetectionSettings_customModules = self.FoldersEventThreatDetectionSettingsCustomModulesService(self)
    self.folders_eventThreatDetectionSettings_effectiveCustomModules = self.FoldersEventThreatDetectionSettingsEffectiveCustomModulesService(self)
    self.folders_eventThreatDetectionSettings = self.FoldersEventThreatDetectionSettingsService(self)
    self.folders_findings = self.FoldersFindingsService(self)
    self.folders_muteConfigs = self.FoldersMuteConfigsService(self)
    self.folders_notificationConfigs = self.FoldersNotificationConfigsService(self)
    self.folders_securityHealthAnalyticsSettings_customModules = self.FoldersSecurityHealthAnalyticsSettingsCustomModulesService(self)
    self.folders_securityHealthAnalyticsSettings_effectiveCustomModules = self.FoldersSecurityHealthAnalyticsSettingsEffectiveCustomModulesService(self)
    self.folders_securityHealthAnalyticsSettings = self.FoldersSecurityHealthAnalyticsSettingsService(self)
    self.folders_sources_findings_externalSystems = self.FoldersSourcesFindingsExternalSystemsService(self)
    self.folders_sources_findings = self.FoldersSourcesFindingsService(self)
    self.folders_sources = self.FoldersSourcesService(self)
    self.folders = self.FoldersService(self)
    self.organizations_assets = self.OrganizationsAssetsService(self)
    self.organizations_bigQueryExports = self.OrganizationsBigQueryExportsService(self)
    self.organizations_eventThreatDetectionSettings_customModules = self.OrganizationsEventThreatDetectionSettingsCustomModulesService(self)
    self.organizations_eventThreatDetectionSettings_effectiveCustomModules = self.OrganizationsEventThreatDetectionSettingsEffectiveCustomModulesService(self)
    self.organizations_eventThreatDetectionSettings = self.OrganizationsEventThreatDetectionSettingsService(self)
    self.organizations_findings = self.OrganizationsFindingsService(self)
    self.organizations_muteConfigs = self.OrganizationsMuteConfigsService(self)
    self.organizations_notificationConfigs = self.OrganizationsNotificationConfigsService(self)
    self.organizations_operations = self.OrganizationsOperationsService(self)
    self.organizations_resourceValueConfigs = self.OrganizationsResourceValueConfigsService(self)
    self.organizations_securityHealthAnalyticsSettings_customModules = self.OrganizationsSecurityHealthAnalyticsSettingsCustomModulesService(self)
    self.organizations_securityHealthAnalyticsSettings_effectiveCustomModules = self.OrganizationsSecurityHealthAnalyticsSettingsEffectiveCustomModulesService(self)
    self.organizations_securityHealthAnalyticsSettings = self.OrganizationsSecurityHealthAnalyticsSettingsService(self)
    self.organizations_simulations_attackExposureResults_attackPaths = self.OrganizationsSimulationsAttackExposureResultsAttackPathsService(self)
    self.organizations_simulations_attackExposureResults_valuedResources = self.OrganizationsSimulationsAttackExposureResultsValuedResourcesService(self)
    self.organizations_simulations_attackExposureResults = self.OrganizationsSimulationsAttackExposureResultsService(self)
    self.organizations_simulations_attackPaths = self.OrganizationsSimulationsAttackPathsService(self)
    self.organizations_simulations_valuedResources_attackPaths = self.OrganizationsSimulationsValuedResourcesAttackPathsService(self)
    self.organizations_simulations_valuedResources = self.OrganizationsSimulationsValuedResourcesService(self)
    self.organizations_simulations = self.OrganizationsSimulationsService(self)
    self.organizations_sources_findings_externalSystems = self.OrganizationsSourcesFindingsExternalSystemsService(self)
    self.organizations_sources_findings = self.OrganizationsSourcesFindingsService(self)
    self.organizations_sources = self.OrganizationsSourcesService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects_assets = self.ProjectsAssetsService(self)
    self.projects_bigQueryExports = self.ProjectsBigQueryExportsService(self)
    self.projects_eventThreatDetectionSettings_customModules = self.ProjectsEventThreatDetectionSettingsCustomModulesService(self)
    self.projects_eventThreatDetectionSettings_effectiveCustomModules = self.ProjectsEventThreatDetectionSettingsEffectiveCustomModulesService(self)
    self.projects_eventThreatDetectionSettings = self.ProjectsEventThreatDetectionSettingsService(self)
    self.projects_findings = self.ProjectsFindingsService(self)
    self.projects_muteConfigs = self.ProjectsMuteConfigsService(self)
    self.projects_notificationConfigs = self.ProjectsNotificationConfigsService(self)
    self.projects_securityHealthAnalyticsSettings_customModules = self.ProjectsSecurityHealthAnalyticsSettingsCustomModulesService(self)
    self.projects_securityHealthAnalyticsSettings_effectiveCustomModules = self.ProjectsSecurityHealthAnalyticsSettingsEffectiveCustomModulesService(self)
    self.projects_securityHealthAnalyticsSettings = self.ProjectsSecurityHealthAnalyticsSettingsService(self)
    self.projects_sources_findings_externalSystems = self.ProjectsSourcesFindingsExternalSystemsService(self)
    self.projects_sources_findings = self.ProjectsSourcesFindingsService(self)
    self.projects_sources = self.ProjectsSourcesService(self)
    self.projects = self.ProjectsService(self)

  class FoldersAssetsService(base_api.BaseApiService):
    """Service class for the folders_assets resource."""

    _NAME = 'folders_assets'

    def __init__(self, client):
      super(SecuritycenterV1.FoldersAssetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Group(self, request, global_params=None):
      r"""Filters an organization's assets and groups them by their specified properties.

      Args:
        request: (SecuritycenterFoldersAssetsGroupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GroupAssetsResponse) The response message.
      """
      config = self.GetMethodConfig('Group')
      return self._RunMethod(
          config, request, global_params=global_params)

    Group.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/assets:group',
        http_method='POST',
        method_id='securitycenter.folders.assets.group',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/assets:group',
        request_field='groupAssetsRequest',
        request_type_name='SecuritycenterFoldersAssetsGroupRequest',
        response_type_name='GroupAssetsResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists an organization's assets.

      Args:
        request: (SecuritycenterFoldersAssetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAssetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/assets',
        http_method='GET',
        method_id='securitycenter.folders.assets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['compareDuration', 'fieldMask', 'filter', 'orderBy', 'pageSize', 'pageToken', 'readTime'],
        relative_path='v1/{+parent}/assets',
        request_field='',
        request_type_name='SecuritycenterFoldersAssetsListRequest',
        response_type_name='ListAssetsResponse',
        supports_download=False,
    )

    def UpdateSecurityMarks(self, request, global_params=None):
      r"""Updates security marks.

      Args:
        request: (SecuritycenterFoldersAssetsUpdateSecurityMarksRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityMarks) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityMarks')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityMarks.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/assets/{assetsId}/securityMarks',
        http_method='PATCH',
        method_id='securitycenter.folders.assets.updateSecurityMarks',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['startTime', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='securityMarks',
        request_type_name='SecuritycenterFoldersAssetsUpdateSecurityMarksRequest',
        response_type_name='SecurityMarks',
        supports_download=False,
    )

  class FoldersBigQueryExportsService(base_api.BaseApiService):
    """Service class for the folders_bigQueryExports resource."""

    _NAME = 'folders_bigQueryExports'

    def __init__(self, client):
      super(SecuritycenterV1.FoldersBigQueryExportsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a BigQuery export.

      Args:
        request: (SecuritycenterFoldersBigQueryExportsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/bigQueryExports',
        http_method='POST',
        method_id='securitycenter.folders.bigQueryExports.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bigQueryExportId'],
        relative_path='v1/{+parent}/bigQueryExports',
        request_field='googleCloudSecuritycenterV1BigQueryExport',
        request_type_name='SecuritycenterFoldersBigQueryExportsCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV1BigQueryExport',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing BigQuery export.

      Args:
        request: (SecuritycenterFoldersBigQueryExportsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/bigQueryExports/{bigQueryExportsId}',
        http_method='DELETE',
        method_id='securitycenter.folders.bigQueryExports.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersBigQueryExportsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a BigQuery export.

      Args:
        request: (SecuritycenterFoldersBigQueryExportsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/bigQueryExports/{bigQueryExportsId}',
        http_method='GET',
        method_id='securitycenter.folders.bigQueryExports.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersBigQueryExportsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV1BigQueryExport',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists BigQuery exports. Note that when requesting BigQuery exports at a given level all exports under that level are also returned e.g. if requesting BigQuery exports under a folder, then all BigQuery exports immediately under the folder plus the ones created under the projects within the folder are returned.

      Args:
        request: (SecuritycenterFoldersBigQueryExportsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBigQueryExportsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/bigQueryExports',
        http_method='GET',
        method_id='securitycenter.folders.bigQueryExports.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/bigQueryExports',
        request_field='',
        request_type_name='SecuritycenterFoldersBigQueryExportsListRequest',
        response_type_name='ListBigQueryExportsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a BigQuery export.

      Args:
        request: (SecuritycenterFoldersBigQueryExportsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/bigQueryExports/{bigQueryExportsId}',
        http_method='PATCH',
        method_id='securitycenter.folders.bigQueryExports.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudSecuritycenterV1BigQueryExport',
        request_type_name='SecuritycenterFoldersBigQueryExportsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV1BigQueryExport',
        supports_download=False,
    )

  class FoldersEventThreatDetectionSettingsCustomModulesService(base_api.BaseApiService):
    """Service class for the folders_eventThreatDetectionSettings_customModules resource."""

    _NAME = 'folders_eventThreatDetectionSettings_customModules'

    def __init__(self, client):
      super(SecuritycenterV1.FoldersEventThreatDetectionSettingsCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def ListDescendant(self, request, global_params=None):
      r"""Lists current and descendant resident Event Threat Detections custom modules. Retrieves all resident modules at the given level and descendant levels.

      Args:
        request: (SecuritycenterFoldersEventThreatDetectionSettingsCustomModulesListDescendantRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDescendantEventThreatDetectionCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('ListDescendant')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListDescendant.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/eventThreatDetectionSettings/customModules:listDescendant',
        http_method='GET',
        method_id='securitycenter.folders.eventThreatDetectionSettings.customModules.listDescendant',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/customModules:listDescendant',
        request_field='',
        request_type_name='SecuritycenterFoldersEventThreatDetectionSettingsCustomModulesListDescendantRequest',
        response_type_name='ListDescendantEventThreatDetectionCustomModulesResponse',
        supports_download=False,
    )

  class FoldersEventThreatDetectionSettingsEffectiveCustomModulesService(base_api.BaseApiService):
    """Service class for the folders_eventThreatDetectionSettings_effectiveCustomModules resource."""

    _NAME = 'folders_eventThreatDetectionSettings_effectiveCustomModules'

    def __init__(self, client):
      super(SecuritycenterV1.FoldersEventThreatDetectionSettingsEffectiveCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets an effective Event Threat Detection custom module. Retrieves the effective module at the given level.

      Args:
        request: (SecuritycenterFoldersEventThreatDetectionSettingsEffectiveCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EffectiveEventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/eventThreatDetectionSettings/effectiveCustomModules/{effectiveCustomModulesId}',
        http_method='GET',
        method_id='securitycenter.folders.eventThreatDetectionSettings.effectiveCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersEventThreatDetectionSettingsEffectiveCustomModulesGetRequest',
        response_type_name='EffectiveEventThreatDetectionCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists effective Event Threat Detection custom modules. Retrieve all effective inherited and resident modules at the given level (no descendants).

      Args:
        request: (SecuritycenterFoldersEventThreatDetectionSettingsEffectiveCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEffectiveEventThreatDetectionCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/eventThreatDetectionSettings/effectiveCustomModules',
        http_method='GET',
        method_id='securitycenter.folders.eventThreatDetectionSettings.effectiveCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/effectiveCustomModules',
        request_field='',
        request_type_name='SecuritycenterFoldersEventThreatDetectionSettingsEffectiveCustomModulesListRequest',
        response_type_name='ListEffectiveEventThreatDetectionCustomModulesResponse',
        supports_download=False,
    )

  class FoldersEventThreatDetectionSettingsService(base_api.BaseApiService):
    """Service class for the folders_eventThreatDetectionSettings resource."""

    _NAME = 'folders_eventThreatDetectionSettings'

    def __init__(self, client):
      super(SecuritycenterV1.FoldersEventThreatDetectionSettingsService, self).__init__(client)
      self._upload_configs = {
          }

  class FoldersFindingsService(base_api.BaseApiService):
    """Service class for the folders_findings resource."""

    _NAME = 'folders_findings'

    def __init__(self, client):
      super(SecuritycenterV1.FoldersFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def BulkMute(self, request, global_params=None):
      r"""Kicks off an LRO to bulk mute findings for a parent based on a filter. The parent can be either an organization, folder or project. The findings matched by the filter will be muted after the LRO is done.

      Args:
        request: (SecuritycenterFoldersFindingsBulkMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('BulkMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    BulkMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/findings:bulkMute',
        http_method='POST',
        method_id='securitycenter.folders.findings.bulkMute',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/findings:bulkMute',
        request_field='bulkMuteFindingsRequest',
        request_type_name='SecuritycenterFoldersFindingsBulkMuteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class FoldersMuteConfigsService(base_api.BaseApiService):
    """Service class for the folders_muteConfigs resource."""

    _NAME = 'folders_muteConfigs'

    def __init__(self, client):
      super(SecuritycenterV1.FoldersMuteConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a mute config.

      Args:
        request: (SecuritycenterFoldersMuteConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/muteConfigs',
        http_method='POST',
        method_id='securitycenter.folders.muteConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['muteConfigId'],
        relative_path='v1/{+parent}/muteConfigs',
        request_field='googleCloudSecuritycenterV1MuteConfig',
        request_type_name='SecuritycenterFoldersMuteConfigsCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV1MuteConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing mute config.

      Args:
        request: (SecuritycenterFoldersMuteConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/muteConfigs/{muteConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.folders.muteConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersMuteConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a mute config.

      Args:
        request: (SecuritycenterFoldersMuteConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/muteConfigs/{muteConfigsId}',
        http_method='GET',
        method_id='securitycenter.folders.muteConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersMuteConfigsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV1MuteConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists mute configs.

      Args:
        request: (SecuritycenterFoldersMuteConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMuteConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/muteConfigs',
        http_method='GET',
        method_id='securitycenter.folders.muteConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/muteConfigs',
        request_field='',
        request_type_name='SecuritycenterFoldersMuteConfigsListRequest',
        response_type_name='ListMuteConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a mute config.

      Args:
        request: (SecuritycenterFoldersMuteConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/muteConfigs/{muteConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.folders.muteConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudSecuritycenterV1MuteConfig',
        request_type_name='SecuritycenterFoldersMuteConfigsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV1MuteConfig',
        supports_download=False,
    )

  class FoldersNotificationConfigsService(base_api.BaseApiService):
    """Service class for the folders_notificationConfigs resource."""

    _NAME = 'folders_notificationConfigs'

    def __init__(self, client):
      super(SecuritycenterV1.FoldersNotificationConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a notification config.

      Args:
        request: (SecuritycenterFoldersNotificationConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/notificationConfigs',
        http_method='POST',
        method_id='securitycenter.folders.notificationConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['configId'],
        relative_path='v1/{+parent}/notificationConfigs',
        request_field='notificationConfig',
        request_type_name='SecuritycenterFoldersNotificationConfigsCreateRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a notification config.

      Args:
        request: (SecuritycenterFoldersNotificationConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/notificationConfigs/{notificationConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.folders.notificationConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersNotificationConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a notification config.

      Args:
        request: (SecuritycenterFoldersNotificationConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/notificationConfigs/{notificationConfigsId}',
        http_method='GET',
        method_id='securitycenter.folders.notificationConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersNotificationConfigsGetRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists notification configs.

      Args:
        request: (SecuritycenterFoldersNotificationConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNotificationConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/notificationConfigs',
        http_method='GET',
        method_id='securitycenter.folders.notificationConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/notificationConfigs',
        request_field='',
        request_type_name='SecuritycenterFoldersNotificationConfigsListRequest',
        response_type_name='ListNotificationConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r""" Updates a notification config. The following update fields are allowed: description, pubsub_topic, streaming_config.filter.

      Args:
        request: (SecuritycenterFoldersNotificationConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/notificationConfigs/{notificationConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.folders.notificationConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='notificationConfig',
        request_type_name='SecuritycenterFoldersNotificationConfigsPatchRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

  class FoldersSecurityHealthAnalyticsSettingsCustomModulesService(base_api.BaseApiService):
    """Service class for the folders_securityHealthAnalyticsSettings_customModules resource."""

    _NAME = 'folders_securityHealthAnalyticsSettings_customModules'

    def __init__(self, client):
      super(SecuritycenterV1.FoldersSecurityHealthAnalyticsSettingsCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a resident SecurityHealthAnalyticsCustomModule at the scope of the given CRM parent, and also creates inherited SecurityHealthAnalyticsCustomModules for all CRM descendants of the given parent. These modules are enabled by default.

      Args:
        request: (SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/securityHealthAnalyticsSettings/customModules',
        http_method='POST',
        method_id='securitycenter.folders.securityHealthAnalyticsSettings.customModules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/customModules',
        request_field='googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule',
        request_type_name='SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified SecurityHealthAnalyticsCustomModule and all of its descendants in the CRM hierarchy. This method is only supported for resident custom modules.

      Args:
        request: (SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/securityHealthAnalyticsSettings/customModules/{customModulesId}',
        http_method='DELETE',
        method_id='securitycenter.folders.securityHealthAnalyticsSettings.customModules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a SecurityHealthAnalyticsCustomModule.

      Args:
        request: (SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/securityHealthAnalyticsSettings/customModules/{customModulesId}',
        http_method='GET',
        method_id='securitycenter.folders.securityHealthAnalyticsSettings.customModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesGetRequest',
        response_type_name='GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of all SecurityHealthAnalyticsCustomModules for the given parent. This includes resident modules defined at the scope of the parent, and inherited modules, inherited from CRM ancestors.

      Args:
        request: (SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/securityHealthAnalyticsSettings/customModules',
        http_method='GET',
        method_id='securitycenter.folders.securityHealthAnalyticsSettings.customModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/customModules',
        request_field='',
        request_type_name='SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesListRequest',
        response_type_name='ListSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

    def ListDescendant(self, request, global_params=None):
      r"""Returns a list of all resident SecurityHealthAnalyticsCustomModules under the given CRM parent and all of the parent's CRM descendants.

      Args:
        request: (SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesListDescendantRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDescendantSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('ListDescendant')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListDescendant.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/securityHealthAnalyticsSettings/customModules:listDescendant',
        http_method='GET',
        method_id='securitycenter.folders.securityHealthAnalyticsSettings.customModules.listDescendant',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/customModules:listDescendant',
        request_field='',
        request_type_name='SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesListDescendantRequest',
        response_type_name='ListDescendantSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the SecurityHealthAnalyticsCustomModule under the given name based on the given update mask. Updating the enablement state is supported on both resident and inherited modules (though resident modules cannot have an enablement state of "inherited"). Updating the display name and custom config of a module is supported on resident modules only.

      Args:
        request: (SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/securityHealthAnalyticsSettings/customModules/{customModulesId}',
        http_method='PATCH',
        method_id='securitycenter.folders.securityHealthAnalyticsSettings.customModules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule',
        request_type_name='SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def Simulate(self, request, global_params=None):
      r"""Simulates a given SecurityHealthAnalyticsCustomModule and Resource.

      Args:
        request: (SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesSimulateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SimulateSecurityHealthAnalyticsCustomModuleResponse) The response message.
      """
      config = self.GetMethodConfig('Simulate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Simulate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/securityHealthAnalyticsSettings/customModules:simulate',
        http_method='POST',
        method_id='securitycenter.folders.securityHealthAnalyticsSettings.customModules.simulate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/customModules:simulate',
        request_field='simulateSecurityHealthAnalyticsCustomModuleRequest',
        request_type_name='SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesSimulateRequest',
        response_type_name='SimulateSecurityHealthAnalyticsCustomModuleResponse',
        supports_download=False,
    )

    def Test(self, request, global_params=None):
      r"""Tests a specified or given SecurityHealthAnalyticsCustomModule.

      Args:
        request: (SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesTestRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestSecurityHealthAnalyticsCustomModuleResponse) The response message.
      """
      config = self.GetMethodConfig('Test')
      return self._RunMethod(
          config, request, global_params=global_params)

    Test.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/securityHealthAnalyticsSettings/customModules/{customModulesId}:test',
        http_method='POST',
        method_id='securitycenter.folders.securityHealthAnalyticsSettings.customModules.test',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:test',
        request_field='testSecurityHealthAnalyticsCustomModuleRequest',
        request_type_name='SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesTestRequest',
        response_type_name='TestSecurityHealthAnalyticsCustomModuleResponse',
        supports_download=False,
    )

  class FoldersSecurityHealthAnalyticsSettingsEffectiveCustomModulesService(base_api.BaseApiService):
    """Service class for the folders_securityHealthAnalyticsSettings_effectiveCustomModules resource."""

    _NAME = 'folders_securityHealthAnalyticsSettings_effectiveCustomModules'

    def __init__(self, client):
      super(SecuritycenterV1.FoldersSecurityHealthAnalyticsSettingsEffectiveCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieves an EffectiveSecurityHealthAnalyticsCustomModule.

      Args:
        request: (SecuritycenterFoldersSecurityHealthAnalyticsSettingsEffectiveCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1EffectiveSecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/securityHealthAnalyticsSettings/effectiveCustomModules/{effectiveCustomModulesId}',
        http_method='GET',
        method_id='securitycenter.folders.securityHealthAnalyticsSettings.effectiveCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterFoldersSecurityHealthAnalyticsSettingsEffectiveCustomModulesGetRequest',
        response_type_name='GoogleCloudSecuritycenterV1EffectiveSecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of all EffectiveSecurityHealthAnalyticsCustomModules for the given parent. This includes resident modules defined at the scope of the parent, and inherited modules, inherited from CRM ancestors.

      Args:
        request: (SecuritycenterFoldersSecurityHealthAnalyticsSettingsEffectiveCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEffectiveSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/securityHealthAnalyticsSettings/effectiveCustomModules',
        http_method='GET',
        method_id='securitycenter.folders.securityHealthAnalyticsSettings.effectiveCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/effectiveCustomModules',
        request_field='',
        request_type_name='SecuritycenterFoldersSecurityHealthAnalyticsSettingsEffectiveCustomModulesListRequest',
        response_type_name='ListEffectiveSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

  class FoldersSecurityHealthAnalyticsSettingsService(base_api.BaseApiService):
    """Service class for the folders_securityHealthAnalyticsSettings resource."""

    _NAME = 'folders_securityHealthAnalyticsSettings'

    def __init__(self, client):
      super(SecuritycenterV1.FoldersSecurityHealthAnalyticsSettingsService, self).__init__(client)
      self._upload_configs = {
          }

  class FoldersSourcesFindingsExternalSystemsService(base_api.BaseApiService):
    """Service class for the folders_sources_findings_externalSystems resource."""

    _NAME = 'folders_sources_findings_externalSystems'

    def __init__(self, client):
      super(SecuritycenterV1.FoldersSourcesFindingsExternalSystemsService, self).__init__(client)
      self._upload_configs = {
          }

    def Patch(self, request, global_params=None):
      r"""Updates external system. This is for a given finding.

      Args:
        request: (SecuritycenterFoldersSourcesFindingsExternalSystemsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1ExternalSystem) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/sources/{sourcesId}/findings/{findingsId}/externalSystems/{externalSystemsId}',
        http_method='PATCH',
        method_id='securitycenter.folders.sources.findings.externalSystems.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudSecuritycenterV1ExternalSystem',
        request_type_name='SecuritycenterFoldersSourcesFindingsExternalSystemsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV1ExternalSystem',
        supports_download=False,
    )

  class FoldersSourcesFindingsService(base_api.BaseApiService):
    """Service class for the folders_sources_findings resource."""

    _NAME = 'folders_sources_findings'

    def __init__(self, client):
      super(SecuritycenterV1.FoldersSourcesFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Group(self, request, global_params=None):
      r"""Filters an organization or source's findings and groups them by their specified properties. To group across all sources provide a `-` as the source id. Example: /v1/organizations/{organization_id}/sources/-/findings, /v1/folders/{folder_id}/sources/-/findings, /v1/projects/{project_id}/sources/-/findings.

      Args:
        request: (SecuritycenterFoldersSourcesFindingsGroupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GroupFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('Group')
      return self._RunMethod(
          config, request, global_params=global_params)

    Group.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/sources/{sourcesId}/findings:group',
        http_method='POST',
        method_id='securitycenter.folders.sources.findings.group',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/findings:group',
        request_field='groupFindingsRequest',
        request_type_name='SecuritycenterFoldersSourcesFindingsGroupRequest',
        response_type_name='GroupFindingsResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists an organization or source's findings. To list across all sources provide a `-` as the source id. Example: /v1/organizations/{organization_id}/sources/-/findings.

      Args:
        request: (SecuritycenterFoldersSourcesFindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/sources/{sourcesId}/findings',
        http_method='GET',
        method_id='securitycenter.folders.sources.findings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['compareDuration', 'fieldMask', 'filter', 'orderBy', 'pageSize', 'pageToken', 'readTime'],
        relative_path='v1/{+parent}/findings',
        request_field='',
        request_type_name='SecuritycenterFoldersSourcesFindingsListRequest',
        response_type_name='ListFindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Creates or updates a finding. The corresponding source must exist for a finding creation to succeed.

      Args:
        request: (SecuritycenterFoldersSourcesFindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Finding) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/sources/{sourcesId}/findings/{findingsId}',
        http_method='PATCH',
        method_id='securitycenter.folders.sources.findings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='finding',
        request_type_name='SecuritycenterFoldersSourcesFindingsPatchRequest',
        response_type_name='Finding',
        supports_download=False,
    )

    def SetMute(self, request, global_params=None):
      r"""Updates the mute state of a finding.

      Args:
        request: (SecuritycenterFoldersSourcesFindingsSetMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Finding) The response message.
      """
      config = self.GetMethodConfig('SetMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/sources/{sourcesId}/findings/{findingsId}:setMute',
        http_method='POST',
        method_id='securitycenter.folders.sources.findings.setMute',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:setMute',
        request_field='setMuteRequest',
        request_type_name='SecuritycenterFoldersSourcesFindingsSetMuteRequest',
        response_type_name='Finding',
        supports_download=False,
    )

    def SetState(self, request, global_params=None):
      r"""Updates the state of a finding.

      Args:
        request: (SecuritycenterFoldersSourcesFindingsSetStateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Finding) The response message.
      """
      config = self.GetMethodConfig('SetState')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetState.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/sources/{sourcesId}/findings/{findingsId}:setState',
        http_method='POST',
        method_id='securitycenter.folders.sources.findings.setState',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:setState',
        request_field='setFindingStateRequest',
        request_type_name='SecuritycenterFoldersSourcesFindingsSetStateRequest',
        response_type_name='Finding',
        supports_download=False,
    )

    def UpdateSecurityMarks(self, request, global_params=None):
      r"""Updates security marks.

      Args:
        request: (SecuritycenterFoldersSourcesFindingsUpdateSecurityMarksRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityMarks) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityMarks')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityMarks.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/sources/{sourcesId}/findings/{findingsId}/securityMarks',
        http_method='PATCH',
        method_id='securitycenter.folders.sources.findings.updateSecurityMarks',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['startTime', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='securityMarks',
        request_type_name='SecuritycenterFoldersSourcesFindingsUpdateSecurityMarksRequest',
        response_type_name='SecurityMarks',
        supports_download=False,
    )

  class FoldersSourcesService(base_api.BaseApiService):
    """Service class for the folders_sources resource."""

    _NAME = 'folders_sources'

    def __init__(self, client):
      super(SecuritycenterV1.FoldersSourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all sources belonging to an organization.

      Args:
        request: (SecuritycenterFoldersSourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/folders/{foldersId}/sources',
        http_method='GET',
        method_id='securitycenter.folders.sources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/sources',
        request_field='',
        request_type_name='SecuritycenterFoldersSourcesListRequest',
        response_type_name='ListSourcesResponse',
        supports_download=False,
    )

  class FoldersService(base_api.BaseApiService):
    """Service class for the folders resource."""

    _NAME = 'folders'

    def __init__(self, client):
      super(SecuritycenterV1.FoldersService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsAssetsService(base_api.BaseApiService):
    """Service class for the organizations_assets resource."""

    _NAME = 'organizations_assets'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsAssetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Group(self, request, global_params=None):
      r"""Filters an organization's assets and groups them by their specified properties.

      Args:
        request: (SecuritycenterOrganizationsAssetsGroupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GroupAssetsResponse) The response message.
      """
      config = self.GetMethodConfig('Group')
      return self._RunMethod(
          config, request, global_params=global_params)

    Group.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/assets:group',
        http_method='POST',
        method_id='securitycenter.organizations.assets.group',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/assets:group',
        request_field='groupAssetsRequest',
        request_type_name='SecuritycenterOrganizationsAssetsGroupRequest',
        response_type_name='GroupAssetsResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists an organization's assets.

      Args:
        request: (SecuritycenterOrganizationsAssetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAssetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/assets',
        http_method='GET',
        method_id='securitycenter.organizations.assets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['compareDuration', 'fieldMask', 'filter', 'orderBy', 'pageSize', 'pageToken', 'readTime'],
        relative_path='v1/{+parent}/assets',
        request_field='',
        request_type_name='SecuritycenterOrganizationsAssetsListRequest',
        response_type_name='ListAssetsResponse',
        supports_download=False,
    )

    def RunDiscovery(self, request, global_params=None):
      r"""Runs asset discovery. The discovery is tracked with a long-running operation. This API can only be called with limited frequency for an organization. If it is called too frequently the caller will receive a TOO_MANY_REQUESTS error.

      Args:
        request: (SecuritycenterOrganizationsAssetsRunDiscoveryRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('RunDiscovery')
      return self._RunMethod(
          config, request, global_params=global_params)

    RunDiscovery.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/assets:runDiscovery',
        http_method='POST',
        method_id='securitycenter.organizations.assets.runDiscovery',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/assets:runDiscovery',
        request_field='runAssetDiscoveryRequest',
        request_type_name='SecuritycenterOrganizationsAssetsRunDiscoveryRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def UpdateSecurityMarks(self, request, global_params=None):
      r"""Updates security marks.

      Args:
        request: (SecuritycenterOrganizationsAssetsUpdateSecurityMarksRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityMarks) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityMarks')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityMarks.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/assets/{assetsId}/securityMarks',
        http_method='PATCH',
        method_id='securitycenter.organizations.assets.updateSecurityMarks',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['startTime', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='securityMarks',
        request_type_name='SecuritycenterOrganizationsAssetsUpdateSecurityMarksRequest',
        response_type_name='SecurityMarks',
        supports_download=False,
    )

  class OrganizationsBigQueryExportsService(base_api.BaseApiService):
    """Service class for the organizations_bigQueryExports resource."""

    _NAME = 'organizations_bigQueryExports'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsBigQueryExportsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a BigQuery export.

      Args:
        request: (SecuritycenterOrganizationsBigQueryExportsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/bigQueryExports',
        http_method='POST',
        method_id='securitycenter.organizations.bigQueryExports.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bigQueryExportId'],
        relative_path='v1/{+parent}/bigQueryExports',
        request_field='googleCloudSecuritycenterV1BigQueryExport',
        request_type_name='SecuritycenterOrganizationsBigQueryExportsCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV1BigQueryExport',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing BigQuery export.

      Args:
        request: (SecuritycenterOrganizationsBigQueryExportsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/bigQueryExports/{bigQueryExportsId}',
        http_method='DELETE',
        method_id='securitycenter.organizations.bigQueryExports.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsBigQueryExportsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a BigQuery export.

      Args:
        request: (SecuritycenterOrganizationsBigQueryExportsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/bigQueryExports/{bigQueryExportsId}',
        http_method='GET',
        method_id='securitycenter.organizations.bigQueryExports.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsBigQueryExportsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV1BigQueryExport',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists BigQuery exports. Note that when requesting BigQuery exports at a given level all exports under that level are also returned e.g. if requesting BigQuery exports under a folder, then all BigQuery exports immediately under the folder plus the ones created under the projects within the folder are returned.

      Args:
        request: (SecuritycenterOrganizationsBigQueryExportsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBigQueryExportsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/bigQueryExports',
        http_method='GET',
        method_id='securitycenter.organizations.bigQueryExports.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/bigQueryExports',
        request_field='',
        request_type_name='SecuritycenterOrganizationsBigQueryExportsListRequest',
        response_type_name='ListBigQueryExportsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a BigQuery export.

      Args:
        request: (SecuritycenterOrganizationsBigQueryExportsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/bigQueryExports/{bigQueryExportsId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.bigQueryExports.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudSecuritycenterV1BigQueryExport',
        request_type_name='SecuritycenterOrganizationsBigQueryExportsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV1BigQueryExport',
        supports_download=False,
    )

  class OrganizationsEventThreatDetectionSettingsCustomModulesService(base_api.BaseApiService):
    """Service class for the organizations_eventThreatDetectionSettings_customModules resource."""

    _NAME = 'organizations_eventThreatDetectionSettings_customModules'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsEventThreatDetectionSettingsCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an Event Threat Detection custom module.

      Args:
        request: (SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/eventThreatDetectionSettings/customModules',
        http_method='POST',
        method_id='securitycenter.organizations.eventThreatDetectionSettings.customModules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/customModules',
        request_field='eventThreatDetectionCustomModule',
        request_type_name='SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesCreateRequest',
        response_type_name='EventThreatDetectionCustomModule',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an Event Threat Detection custom module.

      Args:
        request: (SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/eventThreatDetectionSettings/customModules/{customModulesId}',
        http_method='DELETE',
        method_id='securitycenter.organizations.eventThreatDetectionSettings.customModules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an Event Threat Detection custom module.

      Args:
        request: (SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/eventThreatDetectionSettings/customModules/{customModulesId}',
        http_method='GET',
        method_id='securitycenter.organizations.eventThreatDetectionSettings.customModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesGetRequest',
        response_type_name='EventThreatDetectionCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Event Threat Detection custom modules.

      Args:
        request: (SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEventThreatDetectionCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/eventThreatDetectionSettings/customModules',
        http_method='GET',
        method_id='securitycenter.organizations.eventThreatDetectionSettings.customModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/customModules',
        request_field='',
        request_type_name='SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesListRequest',
        response_type_name='ListEventThreatDetectionCustomModulesResponse',
        supports_download=False,
    )

    def ListDescendant(self, request, global_params=None):
      r"""Lists current and descendant resident Event Threat Detections custom modules. Retrieves all resident modules at the given level and descendant levels.

      Args:
        request: (SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesListDescendantRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDescendantEventThreatDetectionCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('ListDescendant')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListDescendant.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/eventThreatDetectionSettings/customModules:listDescendant',
        http_method='GET',
        method_id='securitycenter.organizations.eventThreatDetectionSettings.customModules.listDescendant',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/customModules:listDescendant',
        request_field='',
        request_type_name='SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesListDescendantRequest',
        response_type_name='ListDescendantEventThreatDetectionCustomModulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an Event Threat Detection custom module.

      Args:
        request: (SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/eventThreatDetectionSettings/customModules/{customModulesId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.eventThreatDetectionSettings.customModules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='eventThreatDetectionCustomModule',
        request_type_name='SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesPatchRequest',
        response_type_name='EventThreatDetectionCustomModule',
        supports_download=False,
    )

  class OrganizationsEventThreatDetectionSettingsEffectiveCustomModulesService(base_api.BaseApiService):
    """Service class for the organizations_eventThreatDetectionSettings_effectiveCustomModules resource."""

    _NAME = 'organizations_eventThreatDetectionSettings_effectiveCustomModules'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsEventThreatDetectionSettingsEffectiveCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets an effective Event Threat Detection custom module. Retrieves the effective module at the given level.

      Args:
        request: (SecuritycenterOrganizationsEventThreatDetectionSettingsEffectiveCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EffectiveEventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/eventThreatDetectionSettings/effectiveCustomModules/{effectiveCustomModulesId}',
        http_method='GET',
        method_id='securitycenter.organizations.eventThreatDetectionSettings.effectiveCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsEventThreatDetectionSettingsEffectiveCustomModulesGetRequest',
        response_type_name='EffectiveEventThreatDetectionCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists effective Event Threat Detection custom modules. Retrieve all effective inherited and resident modules at the given level (no descendants).

      Args:
        request: (SecuritycenterOrganizationsEventThreatDetectionSettingsEffectiveCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEffectiveEventThreatDetectionCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/eventThreatDetectionSettings/effectiveCustomModules',
        http_method='GET',
        method_id='securitycenter.organizations.eventThreatDetectionSettings.effectiveCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/effectiveCustomModules',
        request_field='',
        request_type_name='SecuritycenterOrganizationsEventThreatDetectionSettingsEffectiveCustomModulesListRequest',
        response_type_name='ListEffectiveEventThreatDetectionCustomModulesResponse',
        supports_download=False,
    )

  class OrganizationsEventThreatDetectionSettingsService(base_api.BaseApiService):
    """Service class for the organizations_eventThreatDetectionSettings resource."""

    _NAME = 'organizations_eventThreatDetectionSettings'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsEventThreatDetectionSettingsService, self).__init__(client)
      self._upload_configs = {
          }

    def ValidateCustomModule(self, request, global_params=None):
      r"""Validates the given Event Threat Detection custom module.

      Args:
        request: (SecuritycenterOrganizationsEventThreatDetectionSettingsValidateCustomModuleRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ValidateEventThreatDetectionCustomModuleResponse) The response message.
      """
      config = self.GetMethodConfig('ValidateCustomModule')
      return self._RunMethod(
          config, request, global_params=global_params)

    ValidateCustomModule.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/eventThreatDetectionSettings:validateCustomModule',
        http_method='POST',
        method_id='securitycenter.organizations.eventThreatDetectionSettings.validateCustomModule',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:validateCustomModule',
        request_field='validateEventThreatDetectionCustomModuleRequest',
        request_type_name='SecuritycenterOrganizationsEventThreatDetectionSettingsValidateCustomModuleRequest',
        response_type_name='ValidateEventThreatDetectionCustomModuleResponse',
        supports_download=False,
    )

  class OrganizationsFindingsService(base_api.BaseApiService):
    """Service class for the organizations_findings resource."""

    _NAME = 'organizations_findings'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def BulkMute(self, request, global_params=None):
      r"""Kicks off an LRO to bulk mute findings for a parent based on a filter. The parent can be either an organization, folder or project. The findings matched by the filter will be muted after the LRO is done.

      Args:
        request: (SecuritycenterOrganizationsFindingsBulkMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('BulkMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    BulkMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/findings:bulkMute',
        http_method='POST',
        method_id='securitycenter.organizations.findings.bulkMute',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/findings:bulkMute',
        request_field='bulkMuteFindingsRequest',
        request_type_name='SecuritycenterOrganizationsFindingsBulkMuteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class OrganizationsMuteConfigsService(base_api.BaseApiService):
    """Service class for the organizations_muteConfigs resource."""

    _NAME = 'organizations_muteConfigs'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsMuteConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a mute config.

      Args:
        request: (SecuritycenterOrganizationsMuteConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/muteConfigs',
        http_method='POST',
        method_id='securitycenter.organizations.muteConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['muteConfigId'],
        relative_path='v1/{+parent}/muteConfigs',
        request_field='googleCloudSecuritycenterV1MuteConfig',
        request_type_name='SecuritycenterOrganizationsMuteConfigsCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV1MuteConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing mute config.

      Args:
        request: (SecuritycenterOrganizationsMuteConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/muteConfigs/{muteConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.organizations.muteConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsMuteConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a mute config.

      Args:
        request: (SecuritycenterOrganizationsMuteConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/muteConfigs/{muteConfigsId}',
        http_method='GET',
        method_id='securitycenter.organizations.muteConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsMuteConfigsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV1MuteConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists mute configs.

      Args:
        request: (SecuritycenterOrganizationsMuteConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMuteConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/muteConfigs',
        http_method='GET',
        method_id='securitycenter.organizations.muteConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/muteConfigs',
        request_field='',
        request_type_name='SecuritycenterOrganizationsMuteConfigsListRequest',
        response_type_name='ListMuteConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a mute config.

      Args:
        request: (SecuritycenterOrganizationsMuteConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/muteConfigs/{muteConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.muteConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudSecuritycenterV1MuteConfig',
        request_type_name='SecuritycenterOrganizationsMuteConfigsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV1MuteConfig',
        supports_download=False,
    )

  class OrganizationsNotificationConfigsService(base_api.BaseApiService):
    """Service class for the organizations_notificationConfigs resource."""

    _NAME = 'organizations_notificationConfigs'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsNotificationConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a notification config.

      Args:
        request: (SecuritycenterOrganizationsNotificationConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/notificationConfigs',
        http_method='POST',
        method_id='securitycenter.organizations.notificationConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['configId'],
        relative_path='v1/{+parent}/notificationConfigs',
        request_field='notificationConfig',
        request_type_name='SecuritycenterOrganizationsNotificationConfigsCreateRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a notification config.

      Args:
        request: (SecuritycenterOrganizationsNotificationConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/notificationConfigs/{notificationConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.organizations.notificationConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsNotificationConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a notification config.

      Args:
        request: (SecuritycenterOrganizationsNotificationConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/notificationConfigs/{notificationConfigsId}',
        http_method='GET',
        method_id='securitycenter.organizations.notificationConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsNotificationConfigsGetRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists notification configs.

      Args:
        request: (SecuritycenterOrganizationsNotificationConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNotificationConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/notificationConfigs',
        http_method='GET',
        method_id='securitycenter.organizations.notificationConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/notificationConfigs',
        request_field='',
        request_type_name='SecuritycenterOrganizationsNotificationConfigsListRequest',
        response_type_name='ListNotificationConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r""" Updates a notification config. The following update fields are allowed: description, pubsub_topic, streaming_config.filter.

      Args:
        request: (SecuritycenterOrganizationsNotificationConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/notificationConfigs/{notificationConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.notificationConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='notificationConfig',
        request_type_name='SecuritycenterOrganizationsNotificationConfigsPatchRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

  class OrganizationsOperationsService(base_api.BaseApiService):
    """Service class for the organizations_operations resource."""

    _NAME = 'organizations_operations'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (SecuritycenterOrganizationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='securitycenter.organizations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='',
        request_type_name='SecuritycenterOrganizationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (SecuritycenterOrganizationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='securitycenter.organizations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (SecuritycenterOrganizationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='securitycenter.organizations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (SecuritycenterOrganizationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/operations',
        http_method='GET',
        method_id='securitycenter.organizations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class OrganizationsResourceValueConfigsService(base_api.BaseApiService):
    """Service class for the organizations_resourceValueConfigs resource."""

    _NAME = 'organizations_resourceValueConfigs'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsResourceValueConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Creates a ResourceValueConfig for an organization. Maps user's tags to difference resource values for use by the attack path simulation.

      Args:
        request: (SecuritycenterOrganizationsResourceValueConfigsBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (BatchCreateResourceValueConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/resourceValueConfigs:batchCreate',
        http_method='POST',
        method_id='securitycenter.organizations.resourceValueConfigs.batchCreate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/resourceValueConfigs:batchCreate',
        request_field='batchCreateResourceValueConfigsRequest',
        request_type_name='SecuritycenterOrganizationsResourceValueConfigsBatchCreateRequest',
        response_type_name='BatchCreateResourceValueConfigsResponse',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a ResourceValueConfig.

      Args:
        request: (SecuritycenterOrganizationsResourceValueConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/resourceValueConfigs/{resourceValueConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.organizations.resourceValueConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsResourceValueConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a ResourceValueConfig.

      Args:
        request: (SecuritycenterOrganizationsResourceValueConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1ResourceValueConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/resourceValueConfigs/{resourceValueConfigsId}',
        http_method='GET',
        method_id='securitycenter.organizations.resourceValueConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsResourceValueConfigsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV1ResourceValueConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all ResourceValueConfigs.

      Args:
        request: (SecuritycenterOrganizationsResourceValueConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListResourceValueConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/resourceValueConfigs',
        http_method='GET',
        method_id='securitycenter.organizations.resourceValueConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/resourceValueConfigs',
        request_field='',
        request_type_name='SecuritycenterOrganizationsResourceValueConfigsListRequest',
        response_type_name='ListResourceValueConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing ResourceValueConfigs with new rules.

      Args:
        request: (SecuritycenterOrganizationsResourceValueConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1ResourceValueConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/resourceValueConfigs/{resourceValueConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.resourceValueConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudSecuritycenterV1ResourceValueConfig',
        request_type_name='SecuritycenterOrganizationsResourceValueConfigsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV1ResourceValueConfig',
        supports_download=False,
    )

  class OrganizationsSecurityHealthAnalyticsSettingsCustomModulesService(base_api.BaseApiService):
    """Service class for the organizations_securityHealthAnalyticsSettings_customModules resource."""

    _NAME = 'organizations_securityHealthAnalyticsSettings_customModules'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsSecurityHealthAnalyticsSettingsCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a resident SecurityHealthAnalyticsCustomModule at the scope of the given CRM parent, and also creates inherited SecurityHealthAnalyticsCustomModules for all CRM descendants of the given parent. These modules are enabled by default.

      Args:
        request: (SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityHealthAnalyticsSettings/customModules',
        http_method='POST',
        method_id='securitycenter.organizations.securityHealthAnalyticsSettings.customModules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/customModules',
        request_field='googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule',
        request_type_name='SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified SecurityHealthAnalyticsCustomModule and all of its descendants in the CRM hierarchy. This method is only supported for resident custom modules.

      Args:
        request: (SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityHealthAnalyticsSettings/customModules/{customModulesId}',
        http_method='DELETE',
        method_id='securitycenter.organizations.securityHealthAnalyticsSettings.customModules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a SecurityHealthAnalyticsCustomModule.

      Args:
        request: (SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityHealthAnalyticsSettings/customModules/{customModulesId}',
        http_method='GET',
        method_id='securitycenter.organizations.securityHealthAnalyticsSettings.customModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesGetRequest',
        response_type_name='GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of all SecurityHealthAnalyticsCustomModules for the given parent. This includes resident modules defined at the scope of the parent, and inherited modules, inherited from CRM ancestors.

      Args:
        request: (SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityHealthAnalyticsSettings/customModules',
        http_method='GET',
        method_id='securitycenter.organizations.securityHealthAnalyticsSettings.customModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/customModules',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesListRequest',
        response_type_name='ListSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

    def ListDescendant(self, request, global_params=None):
      r"""Returns a list of all resident SecurityHealthAnalyticsCustomModules under the given CRM parent and all of the parent's CRM descendants.

      Args:
        request: (SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesListDescendantRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDescendantSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('ListDescendant')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListDescendant.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityHealthAnalyticsSettings/customModules:listDescendant',
        http_method='GET',
        method_id='securitycenter.organizations.securityHealthAnalyticsSettings.customModules.listDescendant',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/customModules:listDescendant',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesListDescendantRequest',
        response_type_name='ListDescendantSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the SecurityHealthAnalyticsCustomModule under the given name based on the given update mask. Updating the enablement state is supported on both resident and inherited modules (though resident modules cannot have an enablement state of "inherited"). Updating the display name and custom config of a module is supported on resident modules only.

      Args:
        request: (SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityHealthAnalyticsSettings/customModules/{customModulesId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.securityHealthAnalyticsSettings.customModules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule',
        request_type_name='SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def Simulate(self, request, global_params=None):
      r"""Simulates a given SecurityHealthAnalyticsCustomModule and Resource.

      Args:
        request: (SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesSimulateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SimulateSecurityHealthAnalyticsCustomModuleResponse) The response message.
      """
      config = self.GetMethodConfig('Simulate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Simulate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityHealthAnalyticsSettings/customModules:simulate',
        http_method='POST',
        method_id='securitycenter.organizations.securityHealthAnalyticsSettings.customModules.simulate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/customModules:simulate',
        request_field='simulateSecurityHealthAnalyticsCustomModuleRequest',
        request_type_name='SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesSimulateRequest',
        response_type_name='SimulateSecurityHealthAnalyticsCustomModuleResponse',
        supports_download=False,
    )

    def Test(self, request, global_params=None):
      r"""Tests a specified or given SecurityHealthAnalyticsCustomModule.

      Args:
        request: (SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesTestRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestSecurityHealthAnalyticsCustomModuleResponse) The response message.
      """
      config = self.GetMethodConfig('Test')
      return self._RunMethod(
          config, request, global_params=global_params)

    Test.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityHealthAnalyticsSettings/customModules/{customModulesId}:test',
        http_method='POST',
        method_id='securitycenter.organizations.securityHealthAnalyticsSettings.customModules.test',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:test',
        request_field='testSecurityHealthAnalyticsCustomModuleRequest',
        request_type_name='SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesTestRequest',
        response_type_name='TestSecurityHealthAnalyticsCustomModuleResponse',
        supports_download=False,
    )

  class OrganizationsSecurityHealthAnalyticsSettingsEffectiveCustomModulesService(base_api.BaseApiService):
    """Service class for the organizations_securityHealthAnalyticsSettings_effectiveCustomModules resource."""

    _NAME = 'organizations_securityHealthAnalyticsSettings_effectiveCustomModules'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsSecurityHealthAnalyticsSettingsEffectiveCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieves an EffectiveSecurityHealthAnalyticsCustomModule.

      Args:
        request: (SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsEffectiveCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1EffectiveSecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityHealthAnalyticsSettings/effectiveCustomModules/{effectiveCustomModulesId}',
        http_method='GET',
        method_id='securitycenter.organizations.securityHealthAnalyticsSettings.effectiveCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsEffectiveCustomModulesGetRequest',
        response_type_name='GoogleCloudSecuritycenterV1EffectiveSecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of all EffectiveSecurityHealthAnalyticsCustomModules for the given parent. This includes resident modules defined at the scope of the parent, and inherited modules, inherited from CRM ancestors.

      Args:
        request: (SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsEffectiveCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEffectiveSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityHealthAnalyticsSettings/effectiveCustomModules',
        http_method='GET',
        method_id='securitycenter.organizations.securityHealthAnalyticsSettings.effectiveCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/effectiveCustomModules',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsEffectiveCustomModulesListRequest',
        response_type_name='ListEffectiveSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

  class OrganizationsSecurityHealthAnalyticsSettingsService(base_api.BaseApiService):
    """Service class for the organizations_securityHealthAnalyticsSettings resource."""

    _NAME = 'organizations_securityHealthAnalyticsSettings'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsSecurityHealthAnalyticsSettingsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsSimulationsAttackExposureResultsAttackPathsService(base_api.BaseApiService):
    """Service class for the organizations_simulations_attackExposureResults_attackPaths resource."""

    _NAME = 'organizations_simulations_attackExposureResults_attackPaths'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsSimulationsAttackExposureResultsAttackPathsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the attack paths for a set of simulation results or valued resources and filter.

      Args:
        request: (SecuritycenterOrganizationsSimulationsAttackExposureResultsAttackPathsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAttackPathsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/simulations/{simulationsId}/attackExposureResults/{attackExposureResultsId}/attackPaths',
        http_method='GET',
        method_id='securitycenter.organizations.simulations.attackExposureResults.attackPaths.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/attackPaths',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSimulationsAttackExposureResultsAttackPathsListRequest',
        response_type_name='ListAttackPathsResponse',
        supports_download=False,
    )

  class OrganizationsSimulationsAttackExposureResultsValuedResourcesService(base_api.BaseApiService):
    """Service class for the organizations_simulations_attackExposureResults_valuedResources resource."""

    _NAME = 'organizations_simulations_attackExposureResults_valuedResources'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsSimulationsAttackExposureResultsValuedResourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the valued resources for a set of simulation results and filter.

      Args:
        request: (SecuritycenterOrganizationsSimulationsAttackExposureResultsValuedResourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListValuedResourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/simulations/{simulationsId}/attackExposureResults/{attackExposureResultsId}/valuedResources',
        http_method='GET',
        method_id='securitycenter.organizations.simulations.attackExposureResults.valuedResources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/valuedResources',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSimulationsAttackExposureResultsValuedResourcesListRequest',
        response_type_name='ListValuedResourcesResponse',
        supports_download=False,
    )

  class OrganizationsSimulationsAttackExposureResultsService(base_api.BaseApiService):
    """Service class for the organizations_simulations_attackExposureResults resource."""

    _NAME = 'organizations_simulations_attackExposureResults'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsSimulationsAttackExposureResultsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsSimulationsAttackPathsService(base_api.BaseApiService):
    """Service class for the organizations_simulations_attackPaths resource."""

    _NAME = 'organizations_simulations_attackPaths'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsSimulationsAttackPathsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the attack paths for a set of simulation results or valued resources and filter.

      Args:
        request: (SecuritycenterOrganizationsSimulationsAttackPathsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAttackPathsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/simulations/{simulationsId}/attackPaths',
        http_method='GET',
        method_id='securitycenter.organizations.simulations.attackPaths.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/attackPaths',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSimulationsAttackPathsListRequest',
        response_type_name='ListAttackPathsResponse',
        supports_download=False,
    )

  class OrganizationsSimulationsValuedResourcesAttackPathsService(base_api.BaseApiService):
    """Service class for the organizations_simulations_valuedResources_attackPaths resource."""

    _NAME = 'organizations_simulations_valuedResources_attackPaths'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsSimulationsValuedResourcesAttackPathsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the attack paths for a set of simulation results or valued resources and filter.

      Args:
        request: (SecuritycenterOrganizationsSimulationsValuedResourcesAttackPathsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAttackPathsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/simulations/{simulationsId}/valuedResources/{valuedResourcesId}/attackPaths',
        http_method='GET',
        method_id='securitycenter.organizations.simulations.valuedResources.attackPaths.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/attackPaths',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSimulationsValuedResourcesAttackPathsListRequest',
        response_type_name='ListAttackPathsResponse',
        supports_download=False,
    )

  class OrganizationsSimulationsValuedResourcesService(base_api.BaseApiService):
    """Service class for the organizations_simulations_valuedResources resource."""

    _NAME = 'organizations_simulations_valuedResources'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsSimulationsValuedResourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the valued resources for a set of simulation results and filter.

      Args:
        request: (SecuritycenterOrganizationsSimulationsValuedResourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListValuedResourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/simulations/{simulationsId}/valuedResources',
        http_method='GET',
        method_id='securitycenter.organizations.simulations.valuedResources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/valuedResources',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSimulationsValuedResourcesListRequest',
        response_type_name='ListValuedResourcesResponse',
        supports_download=False,
    )

  class OrganizationsSimulationsService(base_api.BaseApiService):
    """Service class for the organizations_simulations resource."""

    _NAME = 'organizations_simulations'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsSimulationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Get the simulation by name or the latest simulation for the given organization.

      Args:
        request: (SecuritycenterOrganizationsSimulationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Simulation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/simulations/{simulationsId}',
        http_method='GET',
        method_id='securitycenter.organizations.simulations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSimulationsGetRequest',
        response_type_name='Simulation',
        supports_download=False,
    )

  class OrganizationsSourcesFindingsExternalSystemsService(base_api.BaseApiService):
    """Service class for the organizations_sources_findings_externalSystems resource."""

    _NAME = 'organizations_sources_findings_externalSystems'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsSourcesFindingsExternalSystemsService, self).__init__(client)
      self._upload_configs = {
          }

    def Patch(self, request, global_params=None):
      r"""Updates external system. This is for a given finding.

      Args:
        request: (SecuritycenterOrganizationsSourcesFindingsExternalSystemsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1ExternalSystem) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sources/{sourcesId}/findings/{findingsId}/externalSystems/{externalSystemsId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.sources.findings.externalSystems.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudSecuritycenterV1ExternalSystem',
        request_type_name='SecuritycenterOrganizationsSourcesFindingsExternalSystemsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV1ExternalSystem',
        supports_download=False,
    )

  class OrganizationsSourcesFindingsService(base_api.BaseApiService):
    """Service class for the organizations_sources_findings resource."""

    _NAME = 'organizations_sources_findings'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsSourcesFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a finding. The corresponding source must exist for finding creation to succeed.

      Args:
        request: (SecuritycenterOrganizationsSourcesFindingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Finding) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sources/{sourcesId}/findings',
        http_method='POST',
        method_id='securitycenter.organizations.sources.findings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['findingId'],
        relative_path='v1/{+parent}/findings',
        request_field='finding',
        request_type_name='SecuritycenterOrganizationsSourcesFindingsCreateRequest',
        response_type_name='Finding',
        supports_download=False,
    )

    def Group(self, request, global_params=None):
      r"""Filters an organization or source's findings and groups them by their specified properties. To group across all sources provide a `-` as the source id. Example: /v1/organizations/{organization_id}/sources/-/findings, /v1/folders/{folder_id}/sources/-/findings, /v1/projects/{project_id}/sources/-/findings.

      Args:
        request: (SecuritycenterOrganizationsSourcesFindingsGroupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GroupFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('Group')
      return self._RunMethod(
          config, request, global_params=global_params)

    Group.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sources/{sourcesId}/findings:group',
        http_method='POST',
        method_id='securitycenter.organizations.sources.findings.group',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/findings:group',
        request_field='groupFindingsRequest',
        request_type_name='SecuritycenterOrganizationsSourcesFindingsGroupRequest',
        response_type_name='GroupFindingsResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists an organization or source's findings. To list across all sources provide a `-` as the source id. Example: /v1/organizations/{organization_id}/sources/-/findings.

      Args:
        request: (SecuritycenterOrganizationsSourcesFindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sources/{sourcesId}/findings',
        http_method='GET',
        method_id='securitycenter.organizations.sources.findings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['compareDuration', 'fieldMask', 'filter', 'orderBy', 'pageSize', 'pageToken', 'readTime'],
        relative_path='v1/{+parent}/findings',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSourcesFindingsListRequest',
        response_type_name='ListFindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Creates or updates a finding. The corresponding source must exist for a finding creation to succeed.

      Args:
        request: (SecuritycenterOrganizationsSourcesFindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Finding) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sources/{sourcesId}/findings/{findingsId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.sources.findings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='finding',
        request_type_name='SecuritycenterOrganizationsSourcesFindingsPatchRequest',
        response_type_name='Finding',
        supports_download=False,
    )

    def SetMute(self, request, global_params=None):
      r"""Updates the mute state of a finding.

      Args:
        request: (SecuritycenterOrganizationsSourcesFindingsSetMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Finding) The response message.
      """
      config = self.GetMethodConfig('SetMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sources/{sourcesId}/findings/{findingsId}:setMute',
        http_method='POST',
        method_id='securitycenter.organizations.sources.findings.setMute',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:setMute',
        request_field='setMuteRequest',
        request_type_name='SecuritycenterOrganizationsSourcesFindingsSetMuteRequest',
        response_type_name='Finding',
        supports_download=False,
    )

    def SetState(self, request, global_params=None):
      r"""Updates the state of a finding.

      Args:
        request: (SecuritycenterOrganizationsSourcesFindingsSetStateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Finding) The response message.
      """
      config = self.GetMethodConfig('SetState')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetState.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sources/{sourcesId}/findings/{findingsId}:setState',
        http_method='POST',
        method_id='securitycenter.organizations.sources.findings.setState',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:setState',
        request_field='setFindingStateRequest',
        request_type_name='SecuritycenterOrganizationsSourcesFindingsSetStateRequest',
        response_type_name='Finding',
        supports_download=False,
    )

    def UpdateSecurityMarks(self, request, global_params=None):
      r"""Updates security marks.

      Args:
        request: (SecuritycenterOrganizationsSourcesFindingsUpdateSecurityMarksRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityMarks) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityMarks')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityMarks.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sources/{sourcesId}/findings/{findingsId}/securityMarks',
        http_method='PATCH',
        method_id='securitycenter.organizations.sources.findings.updateSecurityMarks',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['startTime', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='securityMarks',
        request_type_name='SecuritycenterOrganizationsSourcesFindingsUpdateSecurityMarksRequest',
        response_type_name='SecurityMarks',
        supports_download=False,
    )

  class OrganizationsSourcesService(base_api.BaseApiService):
    """Service class for the organizations_sources resource."""

    _NAME = 'organizations_sources'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsSourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a source.

      Args:
        request: (SecuritycenterOrganizationsSourcesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Source) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sources',
        http_method='POST',
        method_id='securitycenter.organizations.sources.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/sources',
        request_field='source',
        request_type_name='SecuritycenterOrganizationsSourcesCreateRequest',
        response_type_name='Source',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a source.

      Args:
        request: (SecuritycenterOrganizationsSourcesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Source) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sources/{sourcesId}',
        http_method='GET',
        method_id='securitycenter.organizations.sources.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSourcesGetRequest',
        response_type_name='Source',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy on the specified Source.

      Args:
        request: (SecuritycenterOrganizationsSourcesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sources/{sourcesId}:getIamPolicy',
        http_method='POST',
        method_id='securitycenter.organizations.sources.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='getIamPolicyRequest',
        request_type_name='SecuritycenterOrganizationsSourcesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all sources belonging to an organization.

      Args:
        request: (SecuritycenterOrganizationsSourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sources',
        http_method='GET',
        method_id='securitycenter.organizations.sources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/sources',
        request_field='',
        request_type_name='SecuritycenterOrganizationsSourcesListRequest',
        response_type_name='ListSourcesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a source.

      Args:
        request: (SecuritycenterOrganizationsSourcesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Source) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sources/{sourcesId}',
        http_method='PATCH',
        method_id='securitycenter.organizations.sources.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='source',
        request_type_name='SecuritycenterOrganizationsSourcesPatchRequest',
        response_type_name='Source',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified Source.

      Args:
        request: (SecuritycenterOrganizationsSourcesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sources/{sourcesId}:setIamPolicy',
        http_method='POST',
        method_id='securitycenter.organizations.sources.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='SecuritycenterOrganizationsSourcesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns the permissions that a caller has on the specified source.

      Args:
        request: (SecuritycenterOrganizationsSourcesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sources/{sourcesId}:testIamPermissions',
        http_method='POST',
        method_id='securitycenter.organizations.sources.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='SecuritycenterOrganizationsSourcesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(SecuritycenterV1.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetOrganizationSettings(self, request, global_params=None):
      r"""Gets the settings for an organization.

      Args:
        request: (SecuritycenterOrganizationsGetOrganizationSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OrganizationSettings) The response message.
      """
      config = self.GetMethodConfig('GetOrganizationSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetOrganizationSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/organizationSettings',
        http_method='GET',
        method_id='securitycenter.organizations.getOrganizationSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterOrganizationsGetOrganizationSettingsRequest',
        response_type_name='OrganizationSettings',
        supports_download=False,
    )

    def UpdateOrganizationSettings(self, request, global_params=None):
      r"""Updates an organization's settings.

      Args:
        request: (SecuritycenterOrganizationsUpdateOrganizationSettingsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OrganizationSettings) The response message.
      """
      config = self.GetMethodConfig('UpdateOrganizationSettings')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateOrganizationSettings.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/organizationSettings',
        http_method='PATCH',
        method_id='securitycenter.organizations.updateOrganizationSettings',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='organizationSettings',
        request_type_name='SecuritycenterOrganizationsUpdateOrganizationSettingsRequest',
        response_type_name='OrganizationSettings',
        supports_download=False,
    )

  class ProjectsAssetsService(base_api.BaseApiService):
    """Service class for the projects_assets resource."""

    _NAME = 'projects_assets'

    def __init__(self, client):
      super(SecuritycenterV1.ProjectsAssetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Group(self, request, global_params=None):
      r"""Filters an organization's assets and groups them by their specified properties.

      Args:
        request: (SecuritycenterProjectsAssetsGroupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GroupAssetsResponse) The response message.
      """
      config = self.GetMethodConfig('Group')
      return self._RunMethod(
          config, request, global_params=global_params)

    Group.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/assets:group',
        http_method='POST',
        method_id='securitycenter.projects.assets.group',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/assets:group',
        request_field='groupAssetsRequest',
        request_type_name='SecuritycenterProjectsAssetsGroupRequest',
        response_type_name='GroupAssetsResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists an organization's assets.

      Args:
        request: (SecuritycenterProjectsAssetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListAssetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/assets',
        http_method='GET',
        method_id='securitycenter.projects.assets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['compareDuration', 'fieldMask', 'filter', 'orderBy', 'pageSize', 'pageToken', 'readTime'],
        relative_path='v1/{+parent}/assets',
        request_field='',
        request_type_name='SecuritycenterProjectsAssetsListRequest',
        response_type_name='ListAssetsResponse',
        supports_download=False,
    )

    def UpdateSecurityMarks(self, request, global_params=None):
      r"""Updates security marks.

      Args:
        request: (SecuritycenterProjectsAssetsUpdateSecurityMarksRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityMarks) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityMarks')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityMarks.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/assets/{assetsId}/securityMarks',
        http_method='PATCH',
        method_id='securitycenter.projects.assets.updateSecurityMarks',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['startTime', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='securityMarks',
        request_type_name='SecuritycenterProjectsAssetsUpdateSecurityMarksRequest',
        response_type_name='SecurityMarks',
        supports_download=False,
    )

  class ProjectsBigQueryExportsService(base_api.BaseApiService):
    """Service class for the projects_bigQueryExports resource."""

    _NAME = 'projects_bigQueryExports'

    def __init__(self, client):
      super(SecuritycenterV1.ProjectsBigQueryExportsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a BigQuery export.

      Args:
        request: (SecuritycenterProjectsBigQueryExportsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/bigQueryExports',
        http_method='POST',
        method_id='securitycenter.projects.bigQueryExports.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['bigQueryExportId'],
        relative_path='v1/{+parent}/bigQueryExports',
        request_field='googleCloudSecuritycenterV1BigQueryExport',
        request_type_name='SecuritycenterProjectsBigQueryExportsCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV1BigQueryExport',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing BigQuery export.

      Args:
        request: (SecuritycenterProjectsBigQueryExportsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/bigQueryExports/{bigQueryExportsId}',
        http_method='DELETE',
        method_id='securitycenter.projects.bigQueryExports.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsBigQueryExportsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a BigQuery export.

      Args:
        request: (SecuritycenterProjectsBigQueryExportsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/bigQueryExports/{bigQueryExportsId}',
        http_method='GET',
        method_id='securitycenter.projects.bigQueryExports.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsBigQueryExportsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV1BigQueryExport',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists BigQuery exports. Note that when requesting BigQuery exports at a given level all exports under that level are also returned e.g. if requesting BigQuery exports under a folder, then all BigQuery exports immediately under the folder plus the ones created under the projects within the folder are returned.

      Args:
        request: (SecuritycenterProjectsBigQueryExportsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBigQueryExportsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/bigQueryExports',
        http_method='GET',
        method_id='securitycenter.projects.bigQueryExports.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/bigQueryExports',
        request_field='',
        request_type_name='SecuritycenterProjectsBigQueryExportsListRequest',
        response_type_name='ListBigQueryExportsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a BigQuery export.

      Args:
        request: (SecuritycenterProjectsBigQueryExportsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1BigQueryExport) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/bigQueryExports/{bigQueryExportsId}',
        http_method='PATCH',
        method_id='securitycenter.projects.bigQueryExports.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudSecuritycenterV1BigQueryExport',
        request_type_name='SecuritycenterProjectsBigQueryExportsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV1BigQueryExport',
        supports_download=False,
    )

  class ProjectsEventThreatDetectionSettingsCustomModulesService(base_api.BaseApiService):
    """Service class for the projects_eventThreatDetectionSettings_customModules resource."""

    _NAME = 'projects_eventThreatDetectionSettings_customModules'

    def __init__(self, client):
      super(SecuritycenterV1.ProjectsEventThreatDetectionSettingsCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def ListDescendant(self, request, global_params=None):
      r"""Lists current and descendant resident Event Threat Detections custom modules. Retrieves all resident modules at the given level and descendant levels.

      Args:
        request: (SecuritycenterProjectsEventThreatDetectionSettingsCustomModulesListDescendantRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDescendantEventThreatDetectionCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('ListDescendant')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListDescendant.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/eventThreatDetectionSettings/customModules:listDescendant',
        http_method='GET',
        method_id='securitycenter.projects.eventThreatDetectionSettings.customModules.listDescendant',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/customModules:listDescendant',
        request_field='',
        request_type_name='SecuritycenterProjectsEventThreatDetectionSettingsCustomModulesListDescendantRequest',
        response_type_name='ListDescendantEventThreatDetectionCustomModulesResponse',
        supports_download=False,
    )

  class ProjectsEventThreatDetectionSettingsEffectiveCustomModulesService(base_api.BaseApiService):
    """Service class for the projects_eventThreatDetectionSettings_effectiveCustomModules resource."""

    _NAME = 'projects_eventThreatDetectionSettings_effectiveCustomModules'

    def __init__(self, client):
      super(SecuritycenterV1.ProjectsEventThreatDetectionSettingsEffectiveCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets an effective Event Threat Detection custom module. Retrieves the effective module at the given level.

      Args:
        request: (SecuritycenterProjectsEventThreatDetectionSettingsEffectiveCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EffectiveEventThreatDetectionCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/eventThreatDetectionSettings/effectiveCustomModules/{effectiveCustomModulesId}',
        http_method='GET',
        method_id='securitycenter.projects.eventThreatDetectionSettings.effectiveCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsEventThreatDetectionSettingsEffectiveCustomModulesGetRequest',
        response_type_name='EffectiveEventThreatDetectionCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists effective Event Threat Detection custom modules. Retrieve all effective inherited and resident modules at the given level (no descendants).

      Args:
        request: (SecuritycenterProjectsEventThreatDetectionSettingsEffectiveCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEffectiveEventThreatDetectionCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/eventThreatDetectionSettings/effectiveCustomModules',
        http_method='GET',
        method_id='securitycenter.projects.eventThreatDetectionSettings.effectiveCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/effectiveCustomModules',
        request_field='',
        request_type_name='SecuritycenterProjectsEventThreatDetectionSettingsEffectiveCustomModulesListRequest',
        response_type_name='ListEffectiveEventThreatDetectionCustomModulesResponse',
        supports_download=False,
    )

  class ProjectsEventThreatDetectionSettingsService(base_api.BaseApiService):
    """Service class for the projects_eventThreatDetectionSettings resource."""

    _NAME = 'projects_eventThreatDetectionSettings'

    def __init__(self, client):
      super(SecuritycenterV1.ProjectsEventThreatDetectionSettingsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsFindingsService(base_api.BaseApiService):
    """Service class for the projects_findings resource."""

    _NAME = 'projects_findings'

    def __init__(self, client):
      super(SecuritycenterV1.ProjectsFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def BulkMute(self, request, global_params=None):
      r"""Kicks off an LRO to bulk mute findings for a parent based on a filter. The parent can be either an organization, folder or project. The findings matched by the filter will be muted after the LRO is done.

      Args:
        request: (SecuritycenterProjectsFindingsBulkMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('BulkMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    BulkMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/findings:bulkMute',
        http_method='POST',
        method_id='securitycenter.projects.findings.bulkMute',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/findings:bulkMute',
        request_field='bulkMuteFindingsRequest',
        request_type_name='SecuritycenterProjectsFindingsBulkMuteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsMuteConfigsService(base_api.BaseApiService):
    """Service class for the projects_muteConfigs resource."""

    _NAME = 'projects_muteConfigs'

    def __init__(self, client):
      super(SecuritycenterV1.ProjectsMuteConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a mute config.

      Args:
        request: (SecuritycenterProjectsMuteConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/muteConfigs',
        http_method='POST',
        method_id='securitycenter.projects.muteConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['muteConfigId'],
        relative_path='v1/{+parent}/muteConfigs',
        request_field='googleCloudSecuritycenterV1MuteConfig',
        request_type_name='SecuritycenterProjectsMuteConfigsCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV1MuteConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing mute config.

      Args:
        request: (SecuritycenterProjectsMuteConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/muteConfigs/{muteConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.projects.muteConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsMuteConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a mute config.

      Args:
        request: (SecuritycenterProjectsMuteConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/muteConfigs/{muteConfigsId}',
        http_method='GET',
        method_id='securitycenter.projects.muteConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsMuteConfigsGetRequest',
        response_type_name='GoogleCloudSecuritycenterV1MuteConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists mute configs.

      Args:
        request: (SecuritycenterProjectsMuteConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMuteConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/muteConfigs',
        http_method='GET',
        method_id='securitycenter.projects.muteConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/muteConfigs',
        request_field='',
        request_type_name='SecuritycenterProjectsMuteConfigsListRequest',
        response_type_name='ListMuteConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a mute config.

      Args:
        request: (SecuritycenterProjectsMuteConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1MuteConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/muteConfigs/{muteConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.projects.muteConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudSecuritycenterV1MuteConfig',
        request_type_name='SecuritycenterProjectsMuteConfigsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV1MuteConfig',
        supports_download=False,
    )

  class ProjectsNotificationConfigsService(base_api.BaseApiService):
    """Service class for the projects_notificationConfigs resource."""

    _NAME = 'projects_notificationConfigs'

    def __init__(self, client):
      super(SecuritycenterV1.ProjectsNotificationConfigsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a notification config.

      Args:
        request: (SecuritycenterProjectsNotificationConfigsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/notificationConfigs',
        http_method='POST',
        method_id='securitycenter.projects.notificationConfigs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['configId'],
        relative_path='v1/{+parent}/notificationConfigs',
        request_field='notificationConfig',
        request_type_name='SecuritycenterProjectsNotificationConfigsCreateRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a notification config.

      Args:
        request: (SecuritycenterProjectsNotificationConfigsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/notificationConfigs/{notificationConfigsId}',
        http_method='DELETE',
        method_id='securitycenter.projects.notificationConfigs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsNotificationConfigsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a notification config.

      Args:
        request: (SecuritycenterProjectsNotificationConfigsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/notificationConfigs/{notificationConfigsId}',
        http_method='GET',
        method_id='securitycenter.projects.notificationConfigs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsNotificationConfigsGetRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists notification configs.

      Args:
        request: (SecuritycenterProjectsNotificationConfigsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListNotificationConfigsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/notificationConfigs',
        http_method='GET',
        method_id='securitycenter.projects.notificationConfigs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/notificationConfigs',
        request_field='',
        request_type_name='SecuritycenterProjectsNotificationConfigsListRequest',
        response_type_name='ListNotificationConfigsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r""" Updates a notification config. The following update fields are allowed: description, pubsub_topic, streaming_config.filter.

      Args:
        request: (SecuritycenterProjectsNotificationConfigsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (NotificationConfig) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/notificationConfigs/{notificationConfigsId}',
        http_method='PATCH',
        method_id='securitycenter.projects.notificationConfigs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='notificationConfig',
        request_type_name='SecuritycenterProjectsNotificationConfigsPatchRequest',
        response_type_name='NotificationConfig',
        supports_download=False,
    )

  class ProjectsSecurityHealthAnalyticsSettingsCustomModulesService(base_api.BaseApiService):
    """Service class for the projects_securityHealthAnalyticsSettings_customModules resource."""

    _NAME = 'projects_securityHealthAnalyticsSettings_customModules'

    def __init__(self, client):
      super(SecuritycenterV1.ProjectsSecurityHealthAnalyticsSettingsCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a resident SecurityHealthAnalyticsCustomModule at the scope of the given CRM parent, and also creates inherited SecurityHealthAnalyticsCustomModules for all CRM descendants of the given parent. These modules are enabled by default.

      Args:
        request: (SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/securityHealthAnalyticsSettings/customModules',
        http_method='POST',
        method_id='securitycenter.projects.securityHealthAnalyticsSettings.customModules.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/customModules',
        request_field='googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule',
        request_type_name='SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesCreateRequest',
        response_type_name='GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified SecurityHealthAnalyticsCustomModule and all of its descendants in the CRM hierarchy. This method is only supported for resident custom modules.

      Args:
        request: (SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/securityHealthAnalyticsSettings/customModules/{customModulesId}',
        http_method='DELETE',
        method_id='securitycenter.projects.securityHealthAnalyticsSettings.customModules.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a SecurityHealthAnalyticsCustomModule.

      Args:
        request: (SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/securityHealthAnalyticsSettings/customModules/{customModulesId}',
        http_method='GET',
        method_id='securitycenter.projects.securityHealthAnalyticsSettings.customModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesGetRequest',
        response_type_name='GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of all SecurityHealthAnalyticsCustomModules for the given parent. This includes resident modules defined at the scope of the parent, and inherited modules, inherited from CRM ancestors.

      Args:
        request: (SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/securityHealthAnalyticsSettings/customModules',
        http_method='GET',
        method_id='securitycenter.projects.securityHealthAnalyticsSettings.customModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/customModules',
        request_field='',
        request_type_name='SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesListRequest',
        response_type_name='ListSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

    def ListDescendant(self, request, global_params=None):
      r"""Returns a list of all resident SecurityHealthAnalyticsCustomModules under the given CRM parent and all of the parent's CRM descendants.

      Args:
        request: (SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesListDescendantRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDescendantSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('ListDescendant')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListDescendant.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/securityHealthAnalyticsSettings/customModules:listDescendant',
        http_method='GET',
        method_id='securitycenter.projects.securityHealthAnalyticsSettings.customModules.listDescendant',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/customModules:listDescendant',
        request_field='',
        request_type_name='SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesListDescendantRequest',
        response_type_name='ListDescendantSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the SecurityHealthAnalyticsCustomModule under the given name based on the given update mask. Updating the enablement state is supported on both resident and inherited modules (though resident modules cannot have an enablement state of "inherited"). Updating the display name and custom config of a module is supported on resident modules only.

      Args:
        request: (SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/securityHealthAnalyticsSettings/customModules/{customModulesId}',
        http_method='PATCH',
        method_id='securitycenter.projects.securityHealthAnalyticsSettings.customModules.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule',
        request_type_name='SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def Simulate(self, request, global_params=None):
      r"""Simulates a given SecurityHealthAnalyticsCustomModule and Resource.

      Args:
        request: (SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesSimulateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SimulateSecurityHealthAnalyticsCustomModuleResponse) The response message.
      """
      config = self.GetMethodConfig('Simulate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Simulate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/securityHealthAnalyticsSettings/customModules:simulate',
        http_method='POST',
        method_id='securitycenter.projects.securityHealthAnalyticsSettings.customModules.simulate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/customModules:simulate',
        request_field='simulateSecurityHealthAnalyticsCustomModuleRequest',
        request_type_name='SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesSimulateRequest',
        response_type_name='SimulateSecurityHealthAnalyticsCustomModuleResponse',
        supports_download=False,
    )

    def Test(self, request, global_params=None):
      r"""Tests a specified or given SecurityHealthAnalyticsCustomModule.

      Args:
        request: (SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesTestRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestSecurityHealthAnalyticsCustomModuleResponse) The response message.
      """
      config = self.GetMethodConfig('Test')
      return self._RunMethod(
          config, request, global_params=global_params)

    Test.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/securityHealthAnalyticsSettings/customModules/{customModulesId}:test',
        http_method='POST',
        method_id='securitycenter.projects.securityHealthAnalyticsSettings.customModules.test',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:test',
        request_field='testSecurityHealthAnalyticsCustomModuleRequest',
        request_type_name='SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesTestRequest',
        response_type_name='TestSecurityHealthAnalyticsCustomModuleResponse',
        supports_download=False,
    )

  class ProjectsSecurityHealthAnalyticsSettingsEffectiveCustomModulesService(base_api.BaseApiService):
    """Service class for the projects_securityHealthAnalyticsSettings_effectiveCustomModules resource."""

    _NAME = 'projects_securityHealthAnalyticsSettings_effectiveCustomModules'

    def __init__(self, client):
      super(SecuritycenterV1.ProjectsSecurityHealthAnalyticsSettingsEffectiveCustomModulesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieves an EffectiveSecurityHealthAnalyticsCustomModule.

      Args:
        request: (SecuritycenterProjectsSecurityHealthAnalyticsSettingsEffectiveCustomModulesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1EffectiveSecurityHealthAnalyticsCustomModule) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/securityHealthAnalyticsSettings/effectiveCustomModules/{effectiveCustomModulesId}',
        http_method='GET',
        method_id='securitycenter.projects.securityHealthAnalyticsSettings.effectiveCustomModules.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='SecuritycenterProjectsSecurityHealthAnalyticsSettingsEffectiveCustomModulesGetRequest',
        response_type_name='GoogleCloudSecuritycenterV1EffectiveSecurityHealthAnalyticsCustomModule',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of all EffectiveSecurityHealthAnalyticsCustomModules for the given parent. This includes resident modules defined at the scope of the parent, and inherited modules, inherited from CRM ancestors.

      Args:
        request: (SecuritycenterProjectsSecurityHealthAnalyticsSettingsEffectiveCustomModulesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEffectiveSecurityHealthAnalyticsCustomModulesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/securityHealthAnalyticsSettings/effectiveCustomModules',
        http_method='GET',
        method_id='securitycenter.projects.securityHealthAnalyticsSettings.effectiveCustomModules.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/effectiveCustomModules',
        request_field='',
        request_type_name='SecuritycenterProjectsSecurityHealthAnalyticsSettingsEffectiveCustomModulesListRequest',
        response_type_name='ListEffectiveSecurityHealthAnalyticsCustomModulesResponse',
        supports_download=False,
    )

  class ProjectsSecurityHealthAnalyticsSettingsService(base_api.BaseApiService):
    """Service class for the projects_securityHealthAnalyticsSettings resource."""

    _NAME = 'projects_securityHealthAnalyticsSettings'

    def __init__(self, client):
      super(SecuritycenterV1.ProjectsSecurityHealthAnalyticsSettingsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsSourcesFindingsExternalSystemsService(base_api.BaseApiService):
    """Service class for the projects_sources_findings_externalSystems resource."""

    _NAME = 'projects_sources_findings_externalSystems'

    def __init__(self, client):
      super(SecuritycenterV1.ProjectsSourcesFindingsExternalSystemsService, self).__init__(client)
      self._upload_configs = {
          }

    def Patch(self, request, global_params=None):
      r"""Updates external system. This is for a given finding.

      Args:
        request: (SecuritycenterProjectsSourcesFindingsExternalSystemsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudSecuritycenterV1ExternalSystem) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/sources/{sourcesId}/findings/{findingsId}/externalSystems/{externalSystemsId}',
        http_method='PATCH',
        method_id='securitycenter.projects.sources.findings.externalSystems.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudSecuritycenterV1ExternalSystem',
        request_type_name='SecuritycenterProjectsSourcesFindingsExternalSystemsPatchRequest',
        response_type_name='GoogleCloudSecuritycenterV1ExternalSystem',
        supports_download=False,
    )

  class ProjectsSourcesFindingsService(base_api.BaseApiService):
    """Service class for the projects_sources_findings resource."""

    _NAME = 'projects_sources_findings'

    def __init__(self, client):
      super(SecuritycenterV1.ProjectsSourcesFindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Group(self, request, global_params=None):
      r"""Filters an organization or source's findings and groups them by their specified properties. To group across all sources provide a `-` as the source id. Example: /v1/organizations/{organization_id}/sources/-/findings, /v1/folders/{folder_id}/sources/-/findings, /v1/projects/{project_id}/sources/-/findings.

      Args:
        request: (SecuritycenterProjectsSourcesFindingsGroupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GroupFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('Group')
      return self._RunMethod(
          config, request, global_params=global_params)

    Group.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/sources/{sourcesId}/findings:group',
        http_method='POST',
        method_id='securitycenter.projects.sources.findings.group',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/findings:group',
        request_field='groupFindingsRequest',
        request_type_name='SecuritycenterProjectsSourcesFindingsGroupRequest',
        response_type_name='GroupFindingsResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists an organization or source's findings. To list across all sources provide a `-` as the source id. Example: /v1/organizations/{organization_id}/sources/-/findings.

      Args:
        request: (SecuritycenterProjectsSourcesFindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListFindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/sources/{sourcesId}/findings',
        http_method='GET',
        method_id='securitycenter.projects.sources.findings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['compareDuration', 'fieldMask', 'filter', 'orderBy', 'pageSize', 'pageToken', 'readTime'],
        relative_path='v1/{+parent}/findings',
        request_field='',
        request_type_name='SecuritycenterProjectsSourcesFindingsListRequest',
        response_type_name='ListFindingsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Creates or updates a finding. The corresponding source must exist for a finding creation to succeed.

      Args:
        request: (SecuritycenterProjectsSourcesFindingsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Finding) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/sources/{sourcesId}/findings/{findingsId}',
        http_method='PATCH',
        method_id='securitycenter.projects.sources.findings.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='finding',
        request_type_name='SecuritycenterProjectsSourcesFindingsPatchRequest',
        response_type_name='Finding',
        supports_download=False,
    )

    def SetMute(self, request, global_params=None):
      r"""Updates the mute state of a finding.

      Args:
        request: (SecuritycenterProjectsSourcesFindingsSetMuteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Finding) The response message.
      """
      config = self.GetMethodConfig('SetMute')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetMute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/sources/{sourcesId}/findings/{findingsId}:setMute',
        http_method='POST',
        method_id='securitycenter.projects.sources.findings.setMute',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:setMute',
        request_field='setMuteRequest',
        request_type_name='SecuritycenterProjectsSourcesFindingsSetMuteRequest',
        response_type_name='Finding',
        supports_download=False,
    )

    def SetState(self, request, global_params=None):
      r"""Updates the state of a finding.

      Args:
        request: (SecuritycenterProjectsSourcesFindingsSetStateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Finding) The response message.
      """
      config = self.GetMethodConfig('SetState')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetState.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/sources/{sourcesId}/findings/{findingsId}:setState',
        http_method='POST',
        method_id='securitycenter.projects.sources.findings.setState',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:setState',
        request_field='setFindingStateRequest',
        request_type_name='SecuritycenterProjectsSourcesFindingsSetStateRequest',
        response_type_name='Finding',
        supports_download=False,
    )

    def UpdateSecurityMarks(self, request, global_params=None):
      r"""Updates security marks.

      Args:
        request: (SecuritycenterProjectsSourcesFindingsUpdateSecurityMarksRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (SecurityMarks) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityMarks')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityMarks.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/sources/{sourcesId}/findings/{findingsId}/securityMarks',
        http_method='PATCH',
        method_id='securitycenter.projects.sources.findings.updateSecurityMarks',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['startTime', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='securityMarks',
        request_type_name='SecuritycenterProjectsSourcesFindingsUpdateSecurityMarksRequest',
        response_type_name='SecurityMarks',
        supports_download=False,
    )

  class ProjectsSourcesService(base_api.BaseApiService):
    """Service class for the projects_sources resource."""

    _NAME = 'projects_sources'

    def __init__(self, client):
      super(SecuritycenterV1.ProjectsSourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all sources belonging to an organization.

      Args:
        request: (SecuritycenterProjectsSourcesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListSourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/sources',
        http_method='GET',
        method_id='securitycenter.projects.sources.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/sources',
        request_field='',
        request_type_name='SecuritycenterProjectsSourcesListRequest',
        response_type_name='ListSourcesResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(SecuritycenterV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

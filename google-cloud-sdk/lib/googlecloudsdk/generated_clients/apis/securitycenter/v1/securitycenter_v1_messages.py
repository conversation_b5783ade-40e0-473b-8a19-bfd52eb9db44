"""Generated message classes for securitycenter version v1.

Security Command Center API provides access to temporal views of assets and
findings within an organization.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'securitycenter'


class Access(_messages.Message):
  r"""Represents an access event.

  Fields:
    callerIp: Caller's IP address, such as "*******".
    callerIpGeo: The caller IP's geolocation, which identifies where the call
      came from.
    methodName: The method that the service account called, e.g.
      "SetIamPolicy".
    principalEmail: Associated email, such as "<EMAIL>". The email
      address of the authenticated user or a service account acting on behalf
      of a third party principal making the request. For third party identity
      callers, the `principal_subject` field is populated instead of this
      field. For privacy reasons, the principal email address is sometimes
      redacted. For more information, see [Caller identities in audit
      logs](https://cloud.google.com/logging/docs/audit#user-id).
    principalSubject: A string that represents the principal_subject that is
      associated with the identity. Unlike `principal_email`,
      `principal_subject` supports principals that aren't associated with
      email addresses, such as third party principals. For most identities,
      the format is `principal://iam.googleapis.com/{identity pool
      name}/subject/{subject}`. Some GKE identities, such as GKE_WORKLOAD,
      FREEFORM, and GKE_HUB_WORKLOAD, still use the legacy format
      `serviceAccount:{identity pool name}[{subject}]`.
    serviceAccountDelegationInfo: The identity delegation history of an
      authenticated service account that made the request. The
      `serviceAccountDelegationInfo[]` object contains information about the
      real authorities that try to access Google Cloud resources by delegating
      on a service account. When multiple authorities are present, they are
      guaranteed to be sorted based on the original ordering of the identity
      delegation events.
    serviceAccountKeyName: The name of the service account key that was used
      to create or exchange credentials when authenticating the service
      account that made the request. This is a scheme-less URI full resource
      name. For example: "//iam.googleapis.com/projects/{PROJECT_ID}/serviceAc
      counts/{ACCOUNT}/keys/{key}".
    serviceName: This is the API service that the service account made a call
      to, e.g. "iam.googleapis.com"
    userAgent: The caller's user agent string associated with the finding.
    userAgentFamily: Type of user agent associated with the finding. For
      example, an operating system shell or an embedded or standalone
      application.
    userName: A string that represents a username. The username provided
      depends on the type of the finding and is likely not an IAM principal.
      For example, this can be a system username if the finding is related to
      a virtual machine, or it can be an application login username.
  """

  callerIp = _messages.StringField(1)
  callerIpGeo = _messages.MessageField('Geolocation', 2)
  methodName = _messages.StringField(3)
  principalEmail = _messages.StringField(4)
  principalSubject = _messages.StringField(5)
  serviceAccountDelegationInfo = _messages.MessageField('ServiceAccountDelegationInfo', 6, repeated=True)
  serviceAccountKeyName = _messages.StringField(7)
  serviceName = _messages.StringField(8)
  userAgent = _messages.StringField(9)
  userAgentFamily = _messages.StringField(10)
  userName = _messages.StringField(11)


class AccessReview(_messages.Message):
  r"""Conveys information about a Kubernetes access review (such as one
  returned by a [`kubectl auth
  can-i`](https://kubernetes.io/docs/reference/access-authn-
  authz/authorization/#checking-api-access) command) that was involved in a
  finding.

  Fields:
    group: The API group of the resource. "*" means all.
    name: The name of the resource being requested. Empty means all.
    ns: Namespace of the action being requested. Currently, there is no
      distinction between no namespace and all namespaces. Both are
      represented by "" (empty).
    resource: The optional resource type requested. "*" means all.
    subresource: The optional subresource type.
    verb: A Kubernetes resource API verb, like get, list, watch, create,
      update, delete, proxy. "*" means all.
    version: The API version of the resource. "*" means all.
  """

  group = _messages.StringField(1)
  name = _messages.StringField(2)
  ns = _messages.StringField(3)
  resource = _messages.StringField(4)
  subresource = _messages.StringField(5)
  verb = _messages.StringField(6)
  version = _messages.StringField(7)


class Asset(_messages.Message):
  r"""Security Command Center representation of a Google Cloud resource. The
  Asset is a Security Command Center resource that captures information about
  a single Google Cloud resource. All modifications to an Asset are only
  within the context of Security Command Center and don't affect the
  referenced Google Cloud resource.

  Messages:
    ResourcePropertiesValue: Resource managed properties. These properties are
      managed and defined by the Google Cloud resource and cannot be modified
      by the user.

  Fields:
    canonicalName: The canonical name of the resource. It's either
      "organizations/{organization_id}/assets/{asset_id}",
      "folders/{folder_id}/assets/{asset_id}" or
      "projects/{project_number}/assets/{asset_id}", depending on the closest
      CRM ancestor of the resource.
    createTime: The time at which the asset was created in Security Command
      Center.
    iamPolicy: Cloud IAM Policy information associated with the Google Cloud
      resource described by the Security Command Center asset. This
      information is managed and defined by the Google Cloud resource and
      cannot be modified by the user.
    name: The relative resource name of this asset. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Example: "organizations/{organization_id}/assets/{asset_id}".
    resourceProperties: Resource managed properties. These properties are
      managed and defined by the Google Cloud resource and cannot be modified
      by the user.
    securityCenterProperties: Security Command Center managed properties.
      These properties are managed by Security Command Center and cannot be
      modified by the user.
    securityMarks: User specified security marks. These marks are entirely
      managed by the user and come from the SecurityMarks resource that
      belongs to the asset.
    updateTime: The time at which the asset was last updated or added in Cloud
      SCC.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourcePropertiesValue(_messages.Message):
    r"""Resource managed properties. These properties are managed and defined
    by the Google Cloud resource and cannot be modified by the user.

    Messages:
      AdditionalProperty: An additional property for a ResourcePropertiesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        ResourcePropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourcePropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  canonicalName = _messages.StringField(1)
  createTime = _messages.StringField(2)
  iamPolicy = _messages.MessageField('IamPolicy', 3)
  name = _messages.StringField(4)
  resourceProperties = _messages.MessageField('ResourcePropertiesValue', 5)
  securityCenterProperties = _messages.MessageField('SecurityCenterProperties', 6)
  securityMarks = _messages.MessageField('SecurityMarks', 7)
  updateTime = _messages.StringField(8)


class AssetDiscoveryConfig(_messages.Message):
  r"""The configuration used for Asset Discovery runs.

  Enums:
    InclusionModeValueValuesEnum: The mode to use for filtering asset
      discovery.

  Fields:
    folderIds: The folder ids to use for filtering asset discovery. It
      consists of only digits, e.g., 756619654966.
    inclusionMode: The mode to use for filtering asset discovery.
    projectIds: The project ids to use for filtering asset discovery.
  """

  class InclusionModeValueValuesEnum(_messages.Enum):
    r"""The mode to use for filtering asset discovery.

    Values:
      INCLUSION_MODE_UNSPECIFIED: Unspecified. Setting the mode with this
        value will disable inclusion/exclusion filtering for Asset Discovery.
      INCLUDE_ONLY: Asset Discovery will capture only the resources within the
        projects specified. All other resources will be ignored.
      EXCLUDE: Asset Discovery will ignore all resources under the projects
        specified. All other resources will be retrieved.
    """
    INCLUSION_MODE_UNSPECIFIED = 0
    INCLUDE_ONLY = 1
    EXCLUDE = 2

  folderIds = _messages.StringField(1, repeated=True)
  inclusionMode = _messages.EnumField('InclusionModeValueValuesEnum', 2)
  projectIds = _messages.StringField(3, repeated=True)


class AttackExposure(_messages.Message):
  r"""An attack exposure contains the results of an attack path simulation
  run.

  Enums:
    StateValueValuesEnum: What state this AttackExposure is in. This captures
      whether or not an attack exposure has been calculated or not.

  Fields:
    attackExposureResult: The resource name of the attack path simulation
      result that contains the details regarding this attack exposure score.
      Example: organizations/123/attackExposureResults/456
    exposedHighValueResourcesCount: The number of high value resources that
      are exposed as a result of this finding.
    exposedLowValueResourcesCount: The number of high value resources that are
      exposed as a result of this finding.
    exposedMediumValueResourcesCount: The number of medium value resources
      that are exposed as a result of this finding.
    latestCalculationTime: The most recent time the attack exposure was
      updated on this finding.
    score: A number between 0 (inclusive) and infinity that represents how
      important this finding is to remediate. The higher the score, the more
      important it is to remediate.
    state: What state this AttackExposure is in. This captures whether or not
      an attack exposure has been calculated or not.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""What state this AttackExposure is in. This captures whether or not an
    attack exposure has been calculated or not.

    Values:
      STATE_UNSPECIFIED: The state is not specified.
      CALCULATED: The attack exposure has been calculated.
      NOT_CALCULATED: The attack exposure has not been calculated.
    """
    STATE_UNSPECIFIED = 0
    CALCULATED = 1
    NOT_CALCULATED = 2

  attackExposureResult = _messages.StringField(1)
  exposedHighValueResourcesCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  exposedLowValueResourcesCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  exposedMediumValueResourcesCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  latestCalculationTime = _messages.StringField(5)
  score = _messages.FloatField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)


class AttackPath(_messages.Message):
  r"""A path that an attacker could take to reach an exposed resource.

  Fields:
    edges: A list of the edges between nodes in this attack path.
    name: The attack path name, for example,
      `organizations/12/simulation/34/valuedResources/56/attackPaths/78`
    pathNodes: A list of nodes that exist in this attack path.
  """

  edges = _messages.MessageField('AttackPathEdge', 1, repeated=True)
  name = _messages.StringField(2)
  pathNodes = _messages.MessageField('AttackPathNode', 3, repeated=True)


class AttackPathEdge(_messages.Message):
  r"""Represents a connection between a source node and a destination node in
  this attack path.

  Fields:
    destination: The attack node uuid of the destination node.
    source: The attack node uuid of the source node.
  """

  destination = _messages.StringField(1)
  source = _messages.StringField(2)


class AttackPathNode(_messages.Message):
  r"""Represents one point that an attacker passes through in this attack
  path.

  Fields:
    associatedFindings: The findings associated with this node in the attack
      path.
    attackSteps: A list of attack step nodes that exist in this attack path
      node.
    displayName: Human-readable name of this resource.
    resource: The name of the resource at this point in the attack path. The
      format of the name follows the Cloud Asset Inventory [resource name
      format]("https://cloud.google.com/asset-inventory/docs/resource-name-
      format")
    resourceType: The [supported resource
      type](https://cloud.google.com/asset-inventory/docs/supported-asset-
      types")
    uuid: Unique id of the attack path node.
  """

  associatedFindings = _messages.MessageField('PathNodeAssociatedFinding', 1, repeated=True)
  attackSteps = _messages.MessageField('AttackStepNode', 2, repeated=True)
  displayName = _messages.StringField(3)
  resource = _messages.StringField(4)
  resourceType = _messages.StringField(5)
  uuid = _messages.StringField(6)


class AttackStepNode(_messages.Message):
  r"""Detailed steps the attack can take between path nodes.

  Enums:
    TypeValueValuesEnum: Attack step type. Can be either AND, OR or DEFENSE

  Messages:
    LabelsValue: Attack step labels for metadata

  Fields:
    description: Attack step description
    displayName: User friendly name of the attack step
    labels: Attack step labels for metadata
    type: Attack step type. Can be either AND, OR or DEFENSE
    uuid: Unique ID for one Node
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Attack step type. Can be either AND, OR or DEFENSE

    Values:
      NODE_TYPE_UNSPECIFIED: Type not specified
      NODE_TYPE_AND: Incoming edge joined with AND
      NODE_TYPE_OR: Incoming edge joined with OR
      NODE_TYPE_DEFENSE: Incoming edge is defense
      NODE_TYPE_ATTACKER: Incoming edge is attacker
    """
    NODE_TYPE_UNSPECIFIED = 0
    NODE_TYPE_AND = 1
    NODE_TYPE_OR = 2
    NODE_TYPE_DEFENSE = 3
    NODE_TYPE_ATTACKER = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Attack step labels for metadata

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)
  uuid = _messages.StringField(5)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class BatchCreateResourceValueConfigsRequest(_messages.Message):
  r"""Request message to create multiple resource value configs

  Fields:
    requests: Required. The resource value configs to be created.
  """

  requests = _messages.MessageField('CreateResourceValueConfigRequest', 1, repeated=True)


class BatchCreateResourceValueConfigsResponse(_messages.Message):
  r"""Response message for BatchCreateResourceValueConfigs

  Fields:
    resourceValueConfigs: The resource value configs created
  """

  resourceValueConfigs = _messages.MessageField('GoogleCloudSecuritycenterV1ResourceValueConfig', 1, repeated=True)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class BulkMuteFindingsRequest(_messages.Message):
  r"""Request message for bulk findings update. Note: 1. If multiple bulk
  update requests match the same resource, the order in which they get
  executed is not defined. 2. Once a bulk operation is started, there is no
  way to stop it.

  Fields:
    filter: Expression that identifies findings that should be updated. The
      expression is a list of zero or more restrictions combined via logical
      operators `AND` and `OR`. Parentheses are supported, and `OR` has higher
      precedence than `AND`. Restrictions have the form ` ` and may have a `-`
      character in front of them to indicate negation. The fields map to those
      defined in the corresponding resource. The supported operators are: *
      `=` for all value types. * `>`, `<`, `>=`, `<=` for integer values. *
      `:`, meaning substring matching, for strings. The supported value types
      are: * string literals in quotes. * integer literals without quotes. *
      boolean literals `true` and `false` without quotes.
    muteAnnotation: This can be a mute configuration name or any identifier
      for mute/unmute of findings based on the filter.
  """

  filter = _messages.StringField(1)
  muteAnnotation = _messages.StringField(2)


class CloudDlpDataProfile(_messages.Message):
  r"""The [data profile](https://cloud.google.com/dlp/docs/data-profiles)
  associated with the finding.

  Enums:
    ParentTypeValueValuesEnum: The resource hierarchy level at which the data
      profile was generated.

  Fields:
    dataProfile: Name of the data profile, for example,
      `projects/123/locations/europe/tableProfiles/8383929`.
    parentType: The resource hierarchy level at which the data profile was
      generated.
  """

  class ParentTypeValueValuesEnum(_messages.Enum):
    r"""The resource hierarchy level at which the data profile was generated.

    Values:
      PARENT_TYPE_UNSPECIFIED: Unspecified parent type.
      ORGANIZATION: Organization-level configurations.
      PROJECT: Project-level configurations.
    """
    PARENT_TYPE_UNSPECIFIED = 0
    ORGANIZATION = 1
    PROJECT = 2

  dataProfile = _messages.StringField(1)
  parentType = _messages.EnumField('ParentTypeValueValuesEnum', 2)


class CloudDlpInspection(_messages.Message):
  r"""Details about the Cloud Data Loss Prevention (Cloud DLP) [inspection
  job](https://cloud.google.com/dlp/docs/concepts-job-triggers) that produced
  the finding.

  Fields:
    fullScan: Whether Cloud DLP scanned the complete resource or a sampled
      subset.
    infoType: The type of information (or
      *[infoType](https://cloud.google.com/dlp/docs/infotypes-reference)*)
      found, for example, `EMAIL_ADDRESS` or `STREET_ADDRESS`.
    infoTypeCount: The number of times Cloud DLP found this infoType within
      this job and resource.
    inspectJob: Name of the inspection job, for example,
      `projects/123/locations/europe/dlpJobs/i-8383929`.
  """

  fullScan = _messages.BooleanField(1)
  infoType = _messages.StringField(2)
  infoTypeCount = _messages.IntegerField(3)
  inspectJob = _messages.StringField(4)


class Compliance(_messages.Message):
  r"""Contains compliance information about a security standard indicating
  unmet recommendations.

  Fields:
    ids: Policies within the standard or benchmark, for example, A.12.4.1
    standard: Industry-wide compliance standards or benchmarks, such as CIS,
      PCI, and OWASP.
    version: Version of the standard or benchmark, for example, 1.1
  """

  ids = _messages.StringField(1, repeated=True)
  standard = _messages.StringField(2)
  version = _messages.StringField(3)


class Connection(_messages.Message):
  r"""Contains information about the IP connection associated with the
  finding.

  Enums:
    ProtocolValueValuesEnum: IANA Internet Protocol Number such as TCP(6) and
      UDP(17).

  Fields:
    destinationIp: Destination IP address. Not present for sockets that are
      listening and not connected.
    destinationPort: Destination port. Not present for sockets that are
      listening and not connected.
    protocol: IANA Internet Protocol Number such as TCP(6) and UDP(17).
    sourceIp: Source IP address.
    sourcePort: Source port.
  """

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""IANA Internet Protocol Number such as TCP(6) and UDP(17).

    Values:
      PROTOCOL_UNSPECIFIED: Unspecified protocol (not HOPOPT).
      ICMP: Internet Control Message Protocol.
      TCP: Transmission Control Protocol.
      UDP: User Datagram Protocol.
      GRE: Generic Routing Encapsulation.
      ESP: Encap Security Payload.
    """
    PROTOCOL_UNSPECIFIED = 0
    ICMP = 1
    TCP = 2
    UDP = 3
    GRE = 4
    ESP = 5

  destinationIp = _messages.StringField(1)
  destinationPort = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 3)
  sourceIp = _messages.StringField(4)
  sourcePort = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class Contact(_messages.Message):
  r"""The email address of a contact.

  Fields:
    email: An email address. For example, "`<EMAIL>`".
  """

  email = _messages.StringField(1)


class ContactDetails(_messages.Message):
  r"""Details about specific contacts

  Fields:
    contacts: A list of contacts
  """

  contacts = _messages.MessageField('Contact', 1, repeated=True)


class Container(_messages.Message):
  r"""Container associated with the finding.

  Fields:
    createTime: The time that the container was created.
    imageId: Optional container image ID, if provided by the container
      runtime. Uniquely identifies the container image launched using a
      container image digest.
    labels: Container labels, as provided by the container runtime.
    name: Name of the container.
    uri: Container image URI provided when configuring a pod or container.
      This string can identify a container image version using mutable tags.
  """

  createTime = _messages.StringField(1)
  imageId = _messages.StringField(2)
  labels = _messages.MessageField('Label', 3, repeated=True)
  name = _messages.StringField(4)
  uri = _messages.StringField(5)


class CreateResourceValueConfigRequest(_messages.Message):
  r"""Request message to create single resource value config

  Fields:
    parent: Required. Resource name of the new ResourceValueConfig's parent.
    resourceValueConfig: Required. The resource value config being created.
  """

  parent = _messages.StringField(1)
  resourceValueConfig = _messages.MessageField('GoogleCloudSecuritycenterV1ResourceValueConfig', 2)


class CustomModuleValidationError(_messages.Message):
  r"""An error encountered while validating the uploaded configuration of an
  Event Threat Detection Custom Module.

  Fields:
    description: A description of the error, suitable for human consumption.
      Required.
    end: The end position of the error in the uploaded text version of the
      module. This field may be omitted if no specific position applies, or if
      one could not be computed..
    fieldPath: The path, in RFC 8901 JSON Pointer format, to the field that
      failed validation. This may be left empty if no specific field is
      affected.
    start: The initial position of the error in the uploaded text version of
      the module. This field may be omitted if no specific position applies,
      or if one could not be computed.
  """

  description = _messages.StringField(1)
  end = _messages.MessageField('Position', 2)
  fieldPath = _messages.StringField(3)
  start = _messages.MessageField('Position', 4)


class CustomModuleValidationErrors(_messages.Message):
  r"""A list of zero or more errors encountered while validating the uploaded
  configuration of an Event Threat Detection Custom Module.

  Fields:
    errors: A CustomModuleValidationError attribute.
  """

  errors = _messages.MessageField('CustomModuleValidationError', 1, repeated=True)


class Cve(_messages.Message):
  r"""CVE stands for Common Vulnerabilities and Exposures. More information:
  https://cve.mitre.org

  Fields:
    cvssv3: Describe Common Vulnerability Scoring System specified at
      https://www.first.org/cvss/v3.1/specification-document
    id: The unique identifier for the vulnerability. e.g. CVE-2021-34527
    references: Additional information about the CVE. e.g.
      https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-34527
    upstreamFixAvailable: Whether upstream fix is available for the CVE.
  """

  cvssv3 = _messages.MessageField('Cvssv3', 1)
  id = _messages.StringField(2)
  references = _messages.MessageField('Reference', 3, repeated=True)
  upstreamFixAvailable = _messages.BooleanField(4)


class Cvssv3(_messages.Message):
  r"""Common Vulnerability Scoring System version 3.

  Enums:
    AttackComplexityValueValuesEnum: This metric describes the conditions
      beyond the attacker's control that must exist in order to exploit the
      vulnerability.
    AttackVectorValueValuesEnum: Base Metrics Represents the intrinsic
      characteristics of a vulnerability that are constant over time and
      across user environments. This metric reflects the context by which
      vulnerability exploitation is possible.
    AvailabilityImpactValueValuesEnum: This metric measures the impact to the
      availability of the impacted component resulting from a successfully
      exploited vulnerability.
    ConfidentialityImpactValueValuesEnum: This metric measures the impact to
      the confidentiality of the information resources managed by a software
      component due to a successfully exploited vulnerability.
    IntegrityImpactValueValuesEnum: This metric measures the impact to
      integrity of a successfully exploited vulnerability.
    PrivilegesRequiredValueValuesEnum: This metric describes the level of
      privileges an attacker must possess before successfully exploiting the
      vulnerability.
    ScopeValueValuesEnum: The Scope metric captures whether a vulnerability in
      one vulnerable component impacts resources in components beyond its
      security scope.
    UserInteractionValueValuesEnum: This metric captures the requirement for a
      human user, other than the attacker, to participate in the successful
      compromise of the vulnerable component.

  Fields:
    attackComplexity: This metric describes the conditions beyond the
      attacker's control that must exist in order to exploit the
      vulnerability.
    attackVector: Base Metrics Represents the intrinsic characteristics of a
      vulnerability that are constant over time and across user environments.
      This metric reflects the context by which vulnerability exploitation is
      possible.
    availabilityImpact: This metric measures the impact to the availability of
      the impacted component resulting from a successfully exploited
      vulnerability.
    baseScore: The base score is a function of the base metric scores.
    confidentialityImpact: This metric measures the impact to the
      confidentiality of the information resources managed by a software
      component due to a successfully exploited vulnerability.
    integrityImpact: This metric measures the impact to integrity of a
      successfully exploited vulnerability.
    privilegesRequired: This metric describes the level of privileges an
      attacker must possess before successfully exploiting the vulnerability.
    scope: The Scope metric captures whether a vulnerability in one vulnerable
      component impacts resources in components beyond its security scope.
    userInteraction: This metric captures the requirement for a human user,
      other than the attacker, to participate in the successful compromise of
      the vulnerable component.
  """

  class AttackComplexityValueValuesEnum(_messages.Enum):
    r"""This metric describes the conditions beyond the attacker's control
    that must exist in order to exploit the vulnerability.

    Values:
      ATTACK_COMPLEXITY_UNSPECIFIED: Invalid value.
      ATTACK_COMPLEXITY_LOW: Specialized access conditions or extenuating
        circumstances do not exist. An attacker can expect repeatable success
        when attacking the vulnerable component.
      ATTACK_COMPLEXITY_HIGH: A successful attack depends on conditions beyond
        the attacker's control. That is, a successful attack cannot be
        accomplished at will, but requires the attacker to invest in some
        measurable amount of effort in preparation or execution against the
        vulnerable component before a successful attack can be expected.
    """
    ATTACK_COMPLEXITY_UNSPECIFIED = 0
    ATTACK_COMPLEXITY_LOW = 1
    ATTACK_COMPLEXITY_HIGH = 2

  class AttackVectorValueValuesEnum(_messages.Enum):
    r"""Base Metrics Represents the intrinsic characteristics of a
    vulnerability that are constant over time and across user environments.
    This metric reflects the context by which vulnerability exploitation is
    possible.

    Values:
      ATTACK_VECTOR_UNSPECIFIED: Invalid value.
      ATTACK_VECTOR_NETWORK: The vulnerable component is bound to the network
        stack and the set of possible attackers extends beyond the other
        options listed below, up to and including the entire Internet.
      ATTACK_VECTOR_ADJACENT: The vulnerable component is bound to the network
        stack, but the attack is limited at the protocol level to a logically
        adjacent topology.
      ATTACK_VECTOR_LOCAL: The vulnerable component is not bound to the
        network stack and the attacker's path is via read/write/execute
        capabilities.
      ATTACK_VECTOR_PHYSICAL: The attack requires the attacker to physically
        touch or manipulate the vulnerable component.
    """
    ATTACK_VECTOR_UNSPECIFIED = 0
    ATTACK_VECTOR_NETWORK = 1
    ATTACK_VECTOR_ADJACENT = 2
    ATTACK_VECTOR_LOCAL = 3
    ATTACK_VECTOR_PHYSICAL = 4

  class AvailabilityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to the availability of the impacted
    component resulting from a successfully exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class ConfidentialityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to the confidentiality of the
    information resources managed by a software component due to a
    successfully exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class IntegrityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to integrity of a successfully
    exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class PrivilegesRequiredValueValuesEnum(_messages.Enum):
    r"""This metric describes the level of privileges an attacker must possess
    before successfully exploiting the vulnerability.

    Values:
      PRIVILEGES_REQUIRED_UNSPECIFIED: Invalid value.
      PRIVILEGES_REQUIRED_NONE: The attacker is unauthorized prior to attack,
        and therefore does not require any access to settings or files of the
        vulnerable system to carry out an attack.
      PRIVILEGES_REQUIRED_LOW: The attacker requires privileges that provide
        basic user capabilities that could normally affect only settings and
        files owned by a user. Alternatively, an attacker with Low privileges
        has the ability to access only non-sensitive resources.
      PRIVILEGES_REQUIRED_HIGH: The attacker requires privileges that provide
        significant (e.g., administrative) control over the vulnerable
        component allowing access to component-wide settings and files.
    """
    PRIVILEGES_REQUIRED_UNSPECIFIED = 0
    PRIVILEGES_REQUIRED_NONE = 1
    PRIVILEGES_REQUIRED_LOW = 2
    PRIVILEGES_REQUIRED_HIGH = 3

  class ScopeValueValuesEnum(_messages.Enum):
    r"""The Scope metric captures whether a vulnerability in one vulnerable
    component impacts resources in components beyond its security scope.

    Values:
      SCOPE_UNSPECIFIED: Invalid value.
      SCOPE_UNCHANGED: An exploited vulnerability can only affect resources
        managed by the same security authority.
      SCOPE_CHANGED: An exploited vulnerability can affect resources beyond
        the security scope managed by the security authority of the vulnerable
        component.
    """
    SCOPE_UNSPECIFIED = 0
    SCOPE_UNCHANGED = 1
    SCOPE_CHANGED = 2

  class UserInteractionValueValuesEnum(_messages.Enum):
    r"""This metric captures the requirement for a human user, other than the
    attacker, to participate in the successful compromise of the vulnerable
    component.

    Values:
      USER_INTERACTION_UNSPECIFIED: Invalid value.
      USER_INTERACTION_NONE: The vulnerable system can be exploited without
        interaction from any user.
      USER_INTERACTION_REQUIRED: Successful exploitation of this vulnerability
        requires a user to take some action before the vulnerability can be
        exploited.
    """
    USER_INTERACTION_UNSPECIFIED = 0
    USER_INTERACTION_NONE = 1
    USER_INTERACTION_REQUIRED = 2

  attackComplexity = _messages.EnumField('AttackComplexityValueValuesEnum', 1)
  attackVector = _messages.EnumField('AttackVectorValueValuesEnum', 2)
  availabilityImpact = _messages.EnumField('AvailabilityImpactValueValuesEnum', 3)
  baseScore = _messages.FloatField(4)
  confidentialityImpact = _messages.EnumField('ConfidentialityImpactValueValuesEnum', 5)
  integrityImpact = _messages.EnumField('IntegrityImpactValueValuesEnum', 6)
  privilegesRequired = _messages.EnumField('PrivilegesRequiredValueValuesEnum', 7)
  scope = _messages.EnumField('ScopeValueValuesEnum', 8)
  userInteraction = _messages.EnumField('UserInteractionValueValuesEnum', 9)


class Database(_messages.Message):
  r"""Represents database access information, such as queries. A database may
  be a sub-resource of an instance (as in the case of Cloud SQL instances or
  Cloud Spanner instances), or the database instance itself. Some database
  resources might not have the [full resource
  name](https://google.aip.dev/122#full-resource-names) populated because
  these resource types, such as Cloud SQL databases, are not yet supported by
  Cloud Asset Inventory. In these cases only the display name is provided.

  Fields:
    displayName: The human-readable name of the database that the user
      connected to.
    grantees: The target usernames, roles, or groups of an SQL privilege
      grant, which is not an IAM policy change.
    name: Some database resources may not have the [full resource
      name](https://google.aip.dev/122#full-resource-names) populated because
      these resource types are not yet supported by Cloud Asset Inventory
      (e.g. Cloud SQL databases). In these cases only the display name will be
      provided. The [full resource name](https://google.aip.dev/122#full-
      resource-names) of the database that the user connected to, if it is
      supported by Cloud Asset Inventory.
    query: The SQL statement that is associated with the database access.
    userName: The username used to connect to the database. The username might
      not be an IAM principal and does not have a set format.
    version: The version of the database, for example, POSTGRES_14. See [the
      complete list](https://cloud.google.com/sql/docs/mysql/admin-
      api/rest/v1/SqlDatabaseVersion).
  """

  displayName = _messages.StringField(1)
  grantees = _messages.StringField(2, repeated=True)
  name = _messages.StringField(3)
  query = _messages.StringField(4)
  userName = _messages.StringField(5)
  version = _messages.StringField(6)


class Detection(_messages.Message):
  r"""Memory hash detection contributing to the binary family match.

  Fields:
    binary: The name of the binary associated with the memory hash signature
      detection.
    percentPagesMatched: The percentage of memory page hashes in the signature
      that were matched.
  """

  binary = _messages.StringField(1)
  percentPagesMatched = _messages.FloatField(2)


class EffectiveEventThreatDetectionCustomModule(_messages.Message):
  r"""An EffectiveEventThreatDetectionCustomModule is the representation of
  EventThreatDetectionCustomModule at a given level taking hierarchy into
  account and resolving various fields accordingly. e.g. if the module is
  enabled at the ancestor level, effective modules at all descendant levels
  will have enablement_state set to enabled. Similarly, if module.inherited is
  set, then effective module's config will contain the ancestor's config
  details. EffectiveEventThreatDetectionCustomModule is read-only.

  Enums:
    EnablementStateValueValuesEnum: Output only. The effective state of
      enablement for the module at the given level of the hierarchy.

  Messages:
    ConfigValue: Output only. Config for the effective module.

  Fields:
    config: Output only. Config for the effective module.
    description: Output only. The description for the module.
    displayName: Output only. The human readable name to be displayed for the
      module.
    enablementState: Output only. The effective state of enablement for the
      module at the given level of the hierarchy.
    name: Output only. The resource name of the effective ETD custom module.
      Its format is: * "organizations/{organization}/eventThreatDetectionSetti
      ngs/effectiveCustomModules/{module}". * "folders/{folder}/eventThreatDet
      ectionSettings/effectiveCustomModules/{module}". * "projects/{project}/e
      ventThreatDetectionSettings/effectiveCustomModules/{module}".
    type: Output only. Type for the module. e.g. CONFIGURABLE_BAD_IP.
  """

  class EnablementStateValueValuesEnum(_messages.Enum):
    r"""Output only. The effective state of enablement for the module at the
    given level of the hierarchy.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Unspecified enablement state.
      ENABLED: The module is enabled at the given level.
      DISABLED: The module is disabled at the given level.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConfigValue(_messages.Message):
    r"""Output only. Config for the effective module.

    Messages:
      AdditionalProperty: An additional property for a ConfigValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConfigValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  config = _messages.MessageField('ConfigValue', 1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  enablementState = _messages.EnumField('EnablementStateValueValuesEnum', 4)
  name = _messages.StringField(5)
  type = _messages.StringField(6)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EnvironmentVariable(_messages.Message):
  r"""A name-value pair representing an environment variable used in an
  operating system process.

  Fields:
    name: Environment variable name as a JSON encoded string.
    val: Environment variable value as a JSON encoded string.
  """

  name = _messages.StringField(1)
  val = _messages.StringField(2)


class EventThreatDetectionCustomModule(_messages.Message):
  r"""Represents an instance of an Event Threat Detection custom module,
  including its full module name, display name, enablement state, and last
  updated time. You can create a custom module at the organization level only.

  Enums:
    EnablementStateValueValuesEnum: The state of enablement for the module at
      the given level of the hierarchy.

  Messages:
    ConfigValue: Config for the module. For the resident module, its config
      value is defined at this level. For the inherited module, its config
      value is inherited from the ancestor module.

  Fields:
    ancestorModule: Output only. The closest ancestor module that this module
      inherits the enablement state from. The format is the same as the
      EventThreatDetectionCustomModule resource name.
    config: Config for the module. For the resident module, its config value
      is defined at this level. For the inherited module, its config value is
      inherited from the ancestor module.
    description: The description for the module.
    displayName: The human readable name to be displayed for the module.
    enablementState: The state of enablement for the module at the given level
      of the hierarchy.
    lastEditor: Output only. The editor the module was last updated by.
    name: Immutable. The resource name of the Event Threat Detection custom
      module. Its format is: * "organizations/{organization}/eventThreatDetect
      ionSettings/customModules/{module}". *
      "folders/{folder}/eventThreatDetectionSettings/customModules/{module}".
      * "projects/{project}/eventThreatDetectionSettings/customModules/{module
      }".
    type: Type for the module. e.g. CONFIGURABLE_BAD_IP.
    updateTime: Output only. The time the module was last updated.
  """

  class EnablementStateValueValuesEnum(_messages.Enum):
    r"""The state of enablement for the module at the given level of the
    hierarchy.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Unspecified enablement state.
      ENABLED: The module is enabled at the given level.
      DISABLED: The module is disabled at the given level.
      INHERITED: When the enablement state is inherited.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2
    INHERITED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConfigValue(_messages.Message):
    r"""Config for the module. For the resident module, its config value is
    defined at this level. For the inherited module, its config value is
    inherited from the ancestor module.

    Messages:
      AdditionalProperty: An additional property for a ConfigValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConfigValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  ancestorModule = _messages.StringField(1)
  config = _messages.MessageField('ConfigValue', 2)
  description = _messages.StringField(3)
  displayName = _messages.StringField(4)
  enablementState = _messages.EnumField('EnablementStateValueValuesEnum', 5)
  lastEditor = _messages.StringField(6)
  name = _messages.StringField(7)
  type = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class ExfilResource(_messages.Message):
  r"""Resource where data was exfiltrated from or exfiltrated to.

  Fields:
    components: Subcomponents of the asset that was exfiltrated, like URIs
      used during exfiltration, table names, databases, and filenames. For
      example, multiple tables might have been exfiltrated from the same Cloud
      SQL instance, or multiple files might have been exfiltrated from the
      same Cloud Storage bucket.
    name: The resource's [full resource name](https://cloud.google.com/apis/de
      sign/resource_names#full_resource_name).
  """

  components = _messages.StringField(1, repeated=True)
  name = _messages.StringField(2)


class Exfiltration(_messages.Message):
  r"""Exfiltration represents a data exfiltration attempt from one or more
  sources to one or more targets. The `sources` attribute lists the sources of
  the exfiltrated data. The `targets` attribute lists the destinations the
  data was copied to.

  Fields:
    sources: If there are multiple sources, then the data is considered
      "joined" between them. For instance, BigQuery can join multiple tables,
      and each table would be considered a source.
    targets: If there are multiple targets, each target would get a complete
      copy of the "joined" source data.
  """

  sources = _messages.MessageField('ExfilResource', 1, repeated=True)
  targets = _messages.MessageField('ExfilResource', 2, repeated=True)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class File(_messages.Message):
  r"""File information about the related binary/library used by an executable,
  or the script used by a script interpreter

  Fields:
    contents: Prefix of the file contents as a JSON-encoded string.
    hashedSize: The length in bytes of the file prefix that was hashed. If
      hashed_size == size, any hashes reported represent the entire file.
    partiallyHashed: True when the hash covers only a prefix of the file.
    path: Absolute path of the file as a JSON encoded string.
    sha256: SHA256 hash of the first hashed_size bytes of the file encoded as
      a hex string. If hashed_size == size, sha256 represents the SHA256 hash
      of the entire file.
    size: Size of the file in bytes.
  """

  contents = _messages.StringField(1)
  hashedSize = _messages.IntegerField(2)
  partiallyHashed = _messages.BooleanField(3)
  path = _messages.StringField(4)
  sha256 = _messages.StringField(5)
  size = _messages.IntegerField(6)


class Finding(_messages.Message):
  r"""Security Command Center finding. A finding is a record of assessment
  data like security, risk, health, or privacy, that is ingested into Security
  Command Center for presentation, notification, analysis, policy testing, and
  enforcement. For example, a cross-site scripting (XSS) vulnerability in an
  App Engine application is a finding.

  Enums:
    FindingClassValueValuesEnum: The class of the finding.
    MuteValueValuesEnum: Indicates the mute state of a finding (either muted,
      unmuted or undefined). Unlike other attributes of a finding, a finding
      provider shouldn't set the value of mute.
    SeverityValueValuesEnum: The severity of the finding. This field is
      managed by the source that writes the finding.
    StateValueValuesEnum: The state of the finding.

  Messages:
    ContactsValue: Output only. Map containing the points of contact for the
      given finding. The key represents the type of contact, while the value
      contains a list of all the contacts that pertain. Please refer to:
      https://cloud.google.com/resource-manager/docs/managing-notification-
      contacts#notification-categories { "security": { "contacts": [ {
      "email": "<EMAIL>" }, { "email": "<EMAIL>" } ] }
      }
    ExternalSystemsValue: Output only. Third party SIEM/SOAR fields within
      SCC, contains external system information and external system finding
      fields.
    SourcePropertiesValue: Source specific properties. These properties are
      managed by the source that writes the finding. The key names in the
      source_properties map must be between 1 and 255 characters, and must
      start with a letter and contain alphanumeric characters or underscores
      only.

  Fields:
    access: Access details associated with the finding, such as more
      information on the caller, which method was accessed, and from where.
    attackExposure: The results of an attack path simulation relevant to this
      finding.
    canonicalName: The canonical name of the finding. It's either "organizatio
      ns/{organization_id}/sources/{source_id}/findings/{finding_id}",
      "folders/{folder_id}/sources/{source_id}/findings/{finding_id}" or
      "projects/{project_number}/sources/{source_id}/findings/{finding_id}",
      depending on the closest CRM ancestor of the resource associated with
      the finding.
    category: The additional taxonomy group within findings from a given
      source. This field is immutable after creation time. Example:
      "XSS_FLASH_INJECTION"
    cloudDlpDataProfile: Cloud DLP data profile that is associated with the
      finding.
    cloudDlpInspection: Cloud Data Loss Prevention (Cloud DLP) inspection
      results that are associated with the finding.
    compliances: Contains compliance information for security standards
      associated to the finding.
    connections: Contains information about the IP connection associated with
      the finding.
    contacts: Output only. Map containing the points of contact for the given
      finding. The key represents the type of contact, while the value
      contains a list of all the contacts that pertain. Please refer to:
      https://cloud.google.com/resource-manager/docs/managing-notification-
      contacts#notification-categories { "security": { "contacts": [ {
      "email": "<EMAIL>" }, { "email": "<EMAIL>" } ] }
      }
    containers: Containers associated with the finding. This field provides
      information for both Kubernetes and non-Kubernetes containers.
    createTime: The time at which the finding was created in Security Command
      Center.
    database: Database associated with the finding.
    description: Contains more details about the finding.
    eventTime: The time the finding was first detected. If an existing finding
      is updated, then this is the time the update occurred. For example, if
      the finding represents an open firewall, this property captures the time
      the detector believes the firewall became open. The accuracy is
      determined by the detector. If the finding is later resolved, then this
      time reflects when the finding was resolved. This must not be set to a
      value greater than the current timestamp.
    exfiltration: Represents exfiltrations associated with the finding.
    externalSystems: Output only. Third party SIEM/SOAR fields within SCC,
      contains external system information and external system finding fields.
    externalUri: The URI that, if available, points to a web page outside of
      Security Command Center where additional information about the finding
      can be found. This field is guaranteed to be either empty or a well
      formed URL.
    files: File associated with the finding.
    findingClass: The class of the finding.
    iamBindings: Represents IAM bindings associated with the finding.
    indicator: Represents what's commonly known as an *indicator of
      compromise* (IoC) in computer forensics. This is an artifact observed on
      a network or in an operating system that, with high confidence,
      indicates a computer intrusion. For more information, see [Indicator of
      compromise](https://en.wikipedia.org/wiki/Indicator_of_compromise).
    kernelRootkit: Signature of the kernel rootkit.
    kubernetes: Kubernetes resources associated with the finding.
    mitreAttack: MITRE ATT&CK tactics and techniques related to this finding.
      See: https://attack.mitre.org
    moduleName: Unique identifier of the module which generated the finding.
      Example: folders/598186756061/securityHealthAnalyticsSettings/customModu
      les/56799441161885
    mute: Indicates the mute state of a finding (either muted, unmuted or
      undefined). Unlike other attributes of a finding, a finding provider
      shouldn't set the value of mute.
    muteAnnotation: Records additional information about the mute operation
      e.g. mute config that muted the finding etc. Unlike other attributes of
      a finding, a finding provider shouldn't set the value of
      mute_annotation.
    muteInitiator: Records additional information about the mute operation,
      for example, the [mute configuration](/security-command-center/docs/how-
      to-mute-findings) that muted the finding and the user who muted the
      finding.
    muteUpdateTime: Output only. The most recent time this finding was muted
      or unmuted.
    name: The [relative resource name](https://cloud.google.com/apis/design/re
      source_names#relative_resource_name) of the finding. Example: "organizat
      ions/{organization_id}/sources/{source_id}/findings/{finding_id}",
      "folders/{folder_id}/sources/{source_id}/findings/{finding_id}",
      "projects/{project_id}/sources/{source_id}/findings/{finding_id}".
    nextSteps: Steps to address the finding.
    parent: The relative resource name of the source the finding belongs to.
      See: https://cloud.google.com/apis/design/resource_names#relative_resour
      ce_name This field is immutable after creation time. For example:
      "organizations/{organization_id}/sources/{source_id}"
    parentDisplayName: Output only. The human readable display name of the
      finding source such as "Event Threat Detection" or "Security Health
      Analytics".
    processes: Represents operating system processes associated with the
      Finding.
    resourceName: For findings on Google Cloud resources, the full resource
      name of the Google Cloud resource this finding is for. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
      When the finding is for a non-Google Cloud resource, the resourceName
      can be a customer or partner defined string. This field is immutable
      after creation time.
    securityMarks: Output only. User specified security marks. These marks are
      entirely managed by the user and come from the SecurityMarks resource
      that belongs to the finding.
    severity: The severity of the finding. This field is managed by the source
      that writes the finding.
    sourceProperties: Source specific properties. These properties are managed
      by the source that writes the finding. The key names in the
      source_properties map must be between 1 and 255 characters, and must
      start with a letter and contain alphanumeric characters or underscores
      only.
    state: The state of the finding.
    vulnerability: Represents vulnerability-specific fields like CVE and CVSS
      scores. CVE stands for Common Vulnerabilities and Exposures
      (https://cve.mitre.org/about/)
  """

  class FindingClassValueValuesEnum(_messages.Enum):
    r"""The class of the finding.

    Values:
      FINDING_CLASS_UNSPECIFIED: Unspecified finding class.
      THREAT: Describes unwanted or malicious activity.
      VULNERABILITY: Describes a potential weakness in software that increases
        risk to Confidentiality & Integrity & Availability.
      MISCONFIGURATION: Describes a potential weakness in cloud resource/asset
        configuration that increases risk.
      OBSERVATION: Describes a security observation that is for informational
        purposes.
      SCC_ERROR: Describes an error that prevents some SCC functionality.
    """
    FINDING_CLASS_UNSPECIFIED = 0
    THREAT = 1
    VULNERABILITY = 2
    MISCONFIGURATION = 3
    OBSERVATION = 4
    SCC_ERROR = 5

  class MuteValueValuesEnum(_messages.Enum):
    r"""Indicates the mute state of a finding (either muted, unmuted or
    undefined). Unlike other attributes of a finding, a finding provider
    shouldn't set the value of mute.

    Values:
      MUTE_UNSPECIFIED: Unspecified.
      MUTED: Finding has been muted.
      UNMUTED: Finding has been unmuted.
      UNDEFINED: Finding has never been muted/unmuted.
    """
    MUTE_UNSPECIFIED = 0
    MUTED = 1
    UNMUTED = 2
    UNDEFINED = 3

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity of the finding. This field is managed by the source that
    writes the finding.

    Values:
      SEVERITY_UNSPECIFIED: This value is used for findings when a source
        doesn't write a severity value.
      CRITICAL: Vulnerability: A critical vulnerability is easily discoverable
        by an external actor, exploitable, and results in the direct ability
        to execute arbitrary code, exfiltrate data, and otherwise gain
        additional access and privileges to cloud resources and workloads.
        Examples include publicly accessible unprotected user data and public
        SSH access with weak or no passwords. Threat: Indicates a threat that
        is able to access, modify, or delete data or execute unauthorized code
        within existing resources.
      HIGH: Vulnerability: A high risk vulnerability can be easily discovered
        and exploited in combination with other vulnerabilities in order to
        gain direct access and the ability to execute arbitrary code,
        exfiltrate data, and otherwise gain additional access and privileges
        to cloud resources and workloads. An example is a database with weak
        or no passwords that is only accessible internally. This database
        could easily be compromised by an actor that had access to the
        internal network. Threat: Indicates a threat that is able to create
        new computational resources in an environment but not able to access
        data or execute code in existing resources.
      MEDIUM: Vulnerability: A medium risk vulnerability could be used by an
        actor to gain access to resources or privileges that enable them to
        eventually (through multiple steps or a complex exploit) gain access
        and the ability to execute arbitrary code or exfiltrate data. An
        example is a service account with access to more projects than it
        should have. If an actor gains access to the service account, they
        could potentially use that access to manipulate a project the service
        account was not intended to. Threat: Indicates a threat that is able
        to cause operational impact but may not access data or execute
        unauthorized code.
      LOW: Vulnerability: A low risk vulnerability hampers a security
        organization's ability to detect vulnerabilities or active threats in
        their deployment, or prevents the root cause investigation of security
        issues. An example is monitoring and logs being disabled for resource
        configurations and access. Threat: Indicates a threat that has
        obtained minimal access to an environment but is not able to access
        data, execute code, or create resources.
    """
    SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the finding.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      ACTIVE: The finding requires attention and has not been addressed yet.
      INACTIVE: The finding has been fixed, triaged as a non-issue or
        otherwise addressed and is no longer active.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ContactsValue(_messages.Message):
    r"""Output only. Map containing the points of contact for the given
    finding. The key represents the type of contact, while the value contains
    a list of all the contacts that pertain. Please refer to:
    https://cloud.google.com/resource-manager/docs/managing-notification-
    contacts#notification-categories { "security": { "contacts": [ { "email":
    "<EMAIL>" }, { "email": "<EMAIL>" } ] } }

    Messages:
      AdditionalProperty: An additional property for a ContactsValue object.

    Fields:
      additionalProperties: Additional properties of type ContactsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ContactsValue object.

      Fields:
        key: Name of the additional property.
        value: A ContactDetails attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ContactDetails', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExternalSystemsValue(_messages.Message):
    r"""Output only. Third party SIEM/SOAR fields within SCC, contains
    external system information and external system finding fields.

    Messages:
      AdditionalProperty: An additional property for a ExternalSystemsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ExternalSystemsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExternalSystemsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudSecuritycenterV1ExternalSystem attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudSecuritycenterV1ExternalSystem', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SourcePropertiesValue(_messages.Message):
    r"""Source specific properties. These properties are managed by the source
    that writes the finding. The key names in the source_properties map must
    be between 1 and 255 characters, and must start with a letter and contain
    alphanumeric characters or underscores only.

    Messages:
      AdditionalProperty: An additional property for a SourcePropertiesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        SourcePropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SourcePropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  access = _messages.MessageField('Access', 1)
  attackExposure = _messages.MessageField('AttackExposure', 2)
  canonicalName = _messages.StringField(3)
  category = _messages.StringField(4)
  cloudDlpDataProfile = _messages.MessageField('CloudDlpDataProfile', 5)
  cloudDlpInspection = _messages.MessageField('CloudDlpInspection', 6)
  compliances = _messages.MessageField('Compliance', 7, repeated=True)
  connections = _messages.MessageField('Connection', 8, repeated=True)
  contacts = _messages.MessageField('ContactsValue', 9)
  containers = _messages.MessageField('Container', 10, repeated=True)
  createTime = _messages.StringField(11)
  database = _messages.MessageField('Database', 12)
  description = _messages.StringField(13)
  eventTime = _messages.StringField(14)
  exfiltration = _messages.MessageField('Exfiltration', 15)
  externalSystems = _messages.MessageField('ExternalSystemsValue', 16)
  externalUri = _messages.StringField(17)
  files = _messages.MessageField('File', 18, repeated=True)
  findingClass = _messages.EnumField('FindingClassValueValuesEnum', 19)
  iamBindings = _messages.MessageField('IamBinding', 20, repeated=True)
  indicator = _messages.MessageField('Indicator', 21)
  kernelRootkit = _messages.MessageField('KernelRootkit', 22)
  kubernetes = _messages.MessageField('Kubernetes', 23)
  mitreAttack = _messages.MessageField('MitreAttack', 24)
  moduleName = _messages.StringField(25)
  mute = _messages.EnumField('MuteValueValuesEnum', 26)
  muteAnnotation = _messages.StringField(27)
  muteInitiator = _messages.StringField(28)
  muteUpdateTime = _messages.StringField(29)
  name = _messages.StringField(30)
  nextSteps = _messages.StringField(31)
  parent = _messages.StringField(32)
  parentDisplayName = _messages.StringField(33)
  processes = _messages.MessageField('Process', 34, repeated=True)
  resourceName = _messages.StringField(35)
  securityMarks = _messages.MessageField('SecurityMarks', 36)
  severity = _messages.EnumField('SeverityValueValuesEnum', 37)
  sourceProperties = _messages.MessageField('SourcePropertiesValue', 38)
  state = _messages.EnumField('StateValueValuesEnum', 39)
  vulnerability = _messages.MessageField('Vulnerability', 40)


class Folder(_messages.Message):
  r"""Message that contains the resource name and display name of a folder
  resource.

  Fields:
    resourceFolder: Full resource name of this folder. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    resourceFolderDisplayName: The user defined display name for this folder.
  """

  resourceFolder = _messages.StringField(1)
  resourceFolderDisplayName = _messages.StringField(2)


class Geolocation(_messages.Message):
  r"""Represents a geographical location for a given access.

  Fields:
    regionCode: A CLDR.
  """

  regionCode = _messages.StringField(1)


class GetIamPolicyRequest(_messages.Message):
  r"""Request message for `GetIamPolicy` method.

  Fields:
    options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
      `GetIamPolicy`.
  """

  options = _messages.MessageField('GetPolicyOptions', 1)


class GetPolicyOptions(_messages.Message):
  r"""Encapsulates settings provided to GetIamPolicy.

  Fields:
    requestedPolicyVersion: Optional. The maximum policy version that will be
      used to format the policy. Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected. Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset. The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1. To learn which resources support conditions in
      their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GoogleCloudSecuritycenterV1BigQueryExport(_messages.Message):
  r"""Configures how to deliver Findings to BigQuery Instance.

  Fields:
    createTime: Output only. The time at which the BigQuery export was
      created. This field is set by the server and will be ignored if provided
      on export on creation.
    dataset: The dataset to write findings' updates to. Its format is
      "projects/[project_id]/datasets/[bigquery_dataset_id]". BigQuery Dataset
      unique ID must contain only letters (a-z, A-Z), numbers (0-9), or
      underscores (_).
    description: The description of the export (max of 1024 characters).
    filter: Expression that defines the filter to apply across create/update
      events of findings. The expression is a list of zero or more
      restrictions combined via logical operators `AND` and `OR`. Parentheses
      are supported, and `OR` has higher precedence than `AND`. Restrictions
      have the form ` ` and may have a `-` character in front of them to
      indicate negation. The fields map to those defined in the corresponding
      resource. The supported operators are: * `=` for all value types. * `>`,
      `<`, `>=`, `<=` for integer values. * `:`, meaning substring matching,
      for strings. The supported value types are: * string literals in quotes.
      * integer literals without quotes. * boolean literals `true` and `false`
      without quotes.
    mostRecentEditor: Output only. Email address of the user who last edited
      the BigQuery export. This field is set by the server and will be ignored
      if provided on export creation or update.
    name: The relative resource name of this export. See: https://cloud.google
      .com/apis/design/resource_names#relative_resource_name. Example format:
      "organizations/{organization_id}/bigQueryExports/{export_id}" Example
      format: "folders/{folder_id}/bigQueryExports/{export_id}" Example
      format: "projects/{project_id}/bigQueryExports/{export_id}" This field
      is provided in responses, and is ignored when provided in create
      requests.
    principal: Output only. The service account that needs permission to
      create table and upload data to the BigQuery dataset.
    updateTime: Output only. The most recent time at which the BigQuery export
      was updated. This field is set by the server and will be ignored if
      provided on export creation or update.
  """

  createTime = _messages.StringField(1)
  dataset = _messages.StringField(2)
  description = _messages.StringField(3)
  filter = _messages.StringField(4)
  mostRecentEditor = _messages.StringField(5)
  name = _messages.StringField(6)
  principal = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class GoogleCloudSecuritycenterV1Binding(_messages.Message):
  r"""Represents a Kubernetes RoleBinding or ClusterRoleBinding.

  Fields:
    name: Name for the binding.
    ns: Namespace for the binding.
    role: The Role or ClusterRole referenced by the binding.
    subjects: Represents one or more subjects that are bound to the role. Not
      always available for PATCH requests.
  """

  name = _messages.StringField(1)
  ns = _messages.StringField(2)
  role = _messages.MessageField('Role', 3)
  subjects = _messages.MessageField('Subject', 4, repeated=True)


class GoogleCloudSecuritycenterV1BulkMuteFindingsResponse(_messages.Message):
  r"""The response to a BulkMute request. Contains the LRO information."""


class GoogleCloudSecuritycenterV1CustomConfig(_messages.Message):
  r"""Defines the properties in a custom module configuration for Security
  Health Analytics. Use the custom module configuration to create custom
  detectors that generate custom findings for resources that you specify.

  Enums:
    SeverityValueValuesEnum: The severity to assign to findings generated by
      the module.

  Fields:
    customOutput: Custom output properties.
    description: Text that describes the vulnerability or misconfiguration
      that the custom module detects. This explanation is returned with each
      finding instance to help investigators understand the detected issue.
      The text must be enclosed in quotation marks.
    predicate: The CEL expression to evaluate to produce findings. When the
      expression evaluates to true against a resource, a finding is generated.
    recommendation: An explanation of the recommended steps that security
      teams can take to resolve the detected issue. This explanation is
      returned with each finding generated by this module in the `nextSteps`
      property of the finding JSON.
    resourceSelector: The resource types that the custom module operates on.
      Each custom module can specify up to 5 resource types.
    severity: The severity to assign to findings generated by the module.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity to assign to findings generated by the module.

    Values:
      SEVERITY_UNSPECIFIED: Unspecified severity.
      CRITICAL: Critical severity.
      HIGH: High severity.
      MEDIUM: Medium severity.
      LOW: Low severity.
    """
    SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  customOutput = _messages.MessageField('GoogleCloudSecuritycenterV1CustomOutputSpec', 1)
  description = _messages.StringField(2)
  predicate = _messages.MessageField('Expr', 3)
  recommendation = _messages.StringField(4)
  resourceSelector = _messages.MessageField('GoogleCloudSecuritycenterV1ResourceSelector', 5)
  severity = _messages.EnumField('SeverityValueValuesEnum', 6)


class GoogleCloudSecuritycenterV1CustomOutputSpec(_messages.Message):
  r"""A set of optional name-value pairs that define custom source properties
  to return with each finding that is generated by the custom module. The
  custom source properties that are defined here are included in the finding
  JSON under `sourceProperties`.

  Fields:
    properties: A list of custom output properties to add to the finding.
  """

  properties = _messages.MessageField('GoogleCloudSecuritycenterV1Property', 1, repeated=True)


class GoogleCloudSecuritycenterV1EffectiveSecurityHealthAnalyticsCustomModule(_messages.Message):
  r"""An EffectiveSecurityHealthAnalyticsCustomModule is the representation of
  a Security Health Analytics custom module at a specified level of the
  resource hierarchy: organization, folder, or project. If a custom module is
  inherited from a parent organization or folder, the value of the
  `enablementState` property in EffectiveSecurityHealthAnalyticsCustomModule
  is set to the value that is effective in the parent, instead of `INHERITED`.
  For example, if the module is enabled in a parent organization or folder,
  the effective enablement_state for the module in all child folders or
  projects is also `enabled`. EffectiveSecurityHealthAnalyticsCustomModule is
  read-only.

  Enums:
    EnablementStateValueValuesEnum: Output only. The effective state of
      enablement for the module at the given level of the hierarchy.

  Fields:
    customConfig: Output only. The user-specified configuration for the
      module.
    displayName: Output only. The display name for the custom module. The name
      must be between 1 and 128 characters, start with a lowercase letter, and
      contain alphanumeric characters or underscores only.
    enablementState: Output only. The effective state of enablement for the
      module at the given level of the hierarchy.
    name: Output only. The resource name of the custom module. Its format is "
      organizations/{organization}/securityHealthAnalyticsSettings/effectiveCu
      stomModules/{customModule}", or "folders/{folder}/securityHealthAnalytic
      sSettings/effectiveCustomModules/{customModule}", or "projects/{project}
      /securityHealthAnalyticsSettings/effectiveCustomModules/{customModule}"
  """

  class EnablementStateValueValuesEnum(_messages.Enum):
    r"""Output only. The effective state of enablement for the module at the
    given level of the hierarchy.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Unspecified enablement state.
      ENABLED: The module is enabled at the given level.
      DISABLED: The module is disabled at the given level.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  customConfig = _messages.MessageField('GoogleCloudSecuritycenterV1CustomConfig', 1)
  displayName = _messages.StringField(2)
  enablementState = _messages.EnumField('EnablementStateValueValuesEnum', 3)
  name = _messages.StringField(4)


class GoogleCloudSecuritycenterV1ExternalSystem(_messages.Message):
  r"""Representation of third party SIEM/SOAR fields within SCC.

  Fields:
    assignees: References primary/secondary etc assignees in the external
      system.
    externalSystemUpdateTime: The most recent time when the corresponding
      finding's ticket/tracker was updated in the external system.
    externalUid: Identifier that's used to track the given finding in the
      external system.
    name: Full resource name of the external system, for example:
      "organizations/1234/sources/5678/findings/123456/externalSystems/jira",
      "folders/1234/sources/5678/findings/123456/externalSystems/jira",
      "projects/1234/sources/5678/findings/123456/externalSystems/jira"
    status: Most recent status of the corresponding finding's ticket/tracker
      in the external system.
  """

  assignees = _messages.StringField(1, repeated=True)
  externalSystemUpdateTime = _messages.StringField(2)
  externalUid = _messages.StringField(3)
  name = _messages.StringField(4)
  status = _messages.StringField(5)


class GoogleCloudSecuritycenterV1MuteConfig(_messages.Message):
  r"""A mute config is a Cloud SCC resource that contains the configuration to
  mute create/update events of findings.

  Fields:
    createTime: Output only. The time at which the mute config was created.
      This field is set by the server and will be ignored if provided on
      config creation.
    description: A description of the mute config.
    displayName: The human readable name to be displayed for the mute config.
    filter: Required. An expression that defines the filter to apply across
      create/update events of findings. While creating a filter string, be
      mindful of the scope in which the mute configuration is being created.
      E.g., If a filter contains project = X but is created under the project
      = Y scope, it might not match any findings. The following field and
      operator combinations are supported: * severity: `=`, `:` * category:
      `=`, `:` * resource.name: `=`, `:` * resource.project_name: `=`, `:` *
      resource.project_display_name: `=`, `:` *
      resource.folders.resource_folder: `=`, `:` * resource.parent_name: `=`,
      `:` * resource.parent_display_name: `=`, `:` * resource.type: `=`, `:` *
      finding_class: `=`, `:` * indicator.ip_addresses: `=`, `:` *
      indicator.domains: `=`, `:`
    mostRecentEditor: Output only. Email address of the user who last edited
      the mute config. This field is set by the server and will be ignored if
      provided on config creation or update.
    name: This field will be ignored if provided on config creation. Format
      "organizations/{organization}/muteConfigs/{mute_config}"
      "folders/{folder}/muteConfigs/{mute_config}"
      "projects/{project}/muteConfigs/{mute_config}"
    updateTime: Output only. The most recent time at which the mute config was
      updated. This field is set by the server and will be ignored if provided
      on config creation or update.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  filter = _messages.StringField(4)
  mostRecentEditor = _messages.StringField(5)
  name = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class GoogleCloudSecuritycenterV1NotificationMessage(_messages.Message):
  r"""Cloud SCC's Notification

  Fields:
    finding: If it's a Finding based notification config, this field will be
      populated.
    notificationConfigName: Name of the notification config that generated
      current notification.
    resource: The Cloud resource tied to this notification's Finding.
  """

  finding = _messages.MessageField('Finding', 1)
  notificationConfigName = _messages.StringField(2)
  resource = _messages.MessageField('GoogleCloudSecuritycenterV1Resource', 3)


class GoogleCloudSecuritycenterV1Property(_messages.Message):
  r"""An individual name-value pair that defines a custom source property.

  Fields:
    name: Name of the property for the custom output.
    valueExpression: The CEL expression for the custom output. A resource
      property can be specified to return the value of the property or a text
      string enclosed in quotation marks.
  """

  name = _messages.StringField(1)
  valueExpression = _messages.MessageField('Expr', 2)


class GoogleCloudSecuritycenterV1Resource(_messages.Message):
  r"""Information related to the Google Cloud resource.

  Fields:
    displayName: The human readable name of the resource.
    folders: Output only. Contains a Folder message for each folder in the
      assets ancestry. The first folder is the deepest nested folder, and the
      last folder is the folder directly under the Organization.
    name: The full resource name of the resource. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    parent: The full resource name of resource's parent.
    parentDisplayName: The human readable name of resource's parent.
    project: The full resource name of project that the resource belongs to.
    projectDisplayName: The project ID that the resource belongs to.
    type: The full resource type of the resource.
  """

  displayName = _messages.StringField(1)
  folders = _messages.MessageField('Folder', 2, repeated=True)
  name = _messages.StringField(3)
  parent = _messages.StringField(4)
  parentDisplayName = _messages.StringField(5)
  project = _messages.StringField(6)
  projectDisplayName = _messages.StringField(7)
  type = _messages.StringField(8)


class GoogleCloudSecuritycenterV1ResourceSelector(_messages.Message):
  r"""Resource for selecting resource type.

  Fields:
    resourceTypes: The resource types to run the detector on.
  """

  resourceTypes = _messages.StringField(1, repeated=True)


class GoogleCloudSecuritycenterV1ResourceValueConfig(_messages.Message):
  r"""A resource value config is a mapping configuration of user's tag values
  to resource values. Used by the attack path simulation.

  Enums:
    ResourceValueValueValuesEnum: Required. Resource value level this
      expression represents

  Messages:
    ResourceLabelsSelectorValue: List of resource labels to search for,
      evaluated with AND. E.g. "resource_labels_selector": {"key": "value",
      "env": "prod"} will match resources with labels "key": "value" AND
      "env": "prod" https://cloud.google.com/resource-manager/docs/creating-
      managing-labels

  Fields:
    createTime: Output only. Timestamp this resource value config was created.
    description: Description of the resource value config.
    name: Name for the resource value config
    resourceLabelsSelector: List of resource labels to search for, evaluated
      with AND. E.g. "resource_labels_selector": {"key": "value", "env":
      "prod"} will match resources with labels "key": "value" AND "env":
      "prod" https://cloud.google.com/resource-manager/docs/creating-managing-
      labels
    resourceType: Apply resource_value only to resources that match
      resource_type. resource_type will be checked with "AND" of other
      resources. E.g. "storage.googleapis.com/Bucket" with resource_value
      "HIGH" will apply "HIGH" value only to "storage.googleapis.com/Bucket"
      resources.
    resourceValue: Required. Resource value level this expression represents
    scope: Project or folder to scope this config to. For example,
      "project/456" would apply this config only to resources in "project/456"
      scope will be checked with "AND" of other resources.
    tagValues: Required. Tag values combined with AND to check against. Values
      in the form "tagValues/123" E.g. [ "tagValues/123", "tagValues/456",
      "tagValues/789" ] https://cloud.google.com/resource-
      manager/docs/tags/tags-creating-and-managing
    updateTime: Output only. Timestamp this resource value config was last
      updated.
  """

  class ResourceValueValueValuesEnum(_messages.Enum):
    r"""Required. Resource value level this expression represents

    Values:
      RESOURCE_VALUE_UNSPECIFIED: Unspecific value
      HIGH: High resource value
      MEDIUM: Medium resource value
      LOW: Low resource value
      NONE: No resource value, e.g. ignore these resources
    """
    RESOURCE_VALUE_UNSPECIFIED = 0
    HIGH = 1
    MEDIUM = 2
    LOW = 3
    NONE = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceLabelsSelectorValue(_messages.Message):
    r"""List of resource labels to search for, evaluated with AND. E.g.
    "resource_labels_selector": {"key": "value", "env": "prod"} will match
    resources with labels "key": "value" AND "env": "prod"
    https://cloud.google.com/resource-manager/docs/creating-managing-labels

    Messages:
      AdditionalProperty: An additional property for a
        ResourceLabelsSelectorValue object.

    Fields:
      additionalProperties: Additional properties of type
        ResourceLabelsSelectorValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceLabelsSelectorValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  name = _messages.StringField(3)
  resourceLabelsSelector = _messages.MessageField('ResourceLabelsSelectorValue', 4)
  resourceType = _messages.StringField(5)
  resourceValue = _messages.EnumField('ResourceValueValueValuesEnum', 6)
  scope = _messages.StringField(7)
  tagValues = _messages.StringField(8, repeated=True)
  updateTime = _messages.StringField(9)


class GoogleCloudSecuritycenterV1RunAssetDiscoveryResponse(_messages.Message):
  r"""Response of asset discovery run

  Enums:
    StateValueValuesEnum: The state of an asset discovery run.

  Fields:
    duration: The duration between asset discovery run start and end
    state: The state of an asset discovery run.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of an asset discovery run.

    Values:
      STATE_UNSPECIFIED: Asset discovery run state was unspecified.
      COMPLETED: Asset discovery run completed successfully.
      SUPERSEDED: Asset discovery run was cancelled with tasks still pending,
        as another run for the same organization was started with a higher
        priority.
      TERMINATED: Asset discovery run was killed and terminated.
    """
    STATE_UNSPECIFIED = 0
    COMPLETED = 1
    SUPERSEDED = 2
    TERMINATED = 3

  duration = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule(_messages.Message):
  r"""Represents an instance of a Security Health Analytics custom module,
  including its full module name, display name, enablement state, and last
  updated time. You can create a custom module at the organization, folder, or
  project level. Custom modules that you create at the organization or folder
  level are inherited by the child folders and projects.

  Enums:
    EnablementStateValueValuesEnum: The enablement state of the custom module.

  Fields:
    ancestorModule: Output only. If empty, indicates that the custom module
      was created in the organization, folder, or project in which you are
      viewing the custom module. Otherwise, `ancestor_module` specifies the
      organization or folder from which the custom module is inherited.
    customConfig: The user specified custom configuration for the module.
    displayName: The display name of the Security Health Analytics custom
      module. This display name becomes the finding category for all findings
      that are returned by this custom module. The display name must be
      between 1 and 128 characters, start with a lowercase letter, and contain
      alphanumeric characters or underscores only.
    enablementState: The enablement state of the custom module.
    lastEditor: Output only. The editor that last updated the custom module.
    name: Immutable. The resource name of the custom module. Its format is "or
      ganizations/{organization}/securityHealthAnalyticsSettings/customModules
      /{customModule}", or "folders/{folder}/securityHealthAnalyticsSettings/c
      ustomModules/{customModule}", or "projects/{project}/securityHealthAnaly
      ticsSettings/customModules/{customModule}" The id {customModule} is
      server-generated and is not user settable. It will be a numeric id
      containing 1-20 digits.
    updateTime: Output only. The time at which the custom module was last
      updated.
  """

  class EnablementStateValueValuesEnum(_messages.Enum):
    r"""The enablement state of the custom module.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Unspecified enablement state.
      ENABLED: The module is enabled at the given CRM resource.
      DISABLED: The module is disabled at the given CRM resource.
      INHERITED: State is inherited from an ancestor module. The module will
        either be effectively ENABLED or DISABLED based on its closest non-
        inherited ancestor module in the CRM hierarchy.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2
    INHERITED = 3

  ancestorModule = _messages.StringField(1)
  customConfig = _messages.MessageField('GoogleCloudSecuritycenterV1CustomConfig', 2)
  displayName = _messages.StringField(3)
  enablementState = _messages.EnumField('EnablementStateValueValuesEnum', 4)
  lastEditor = _messages.StringField(5)
  name = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class GoogleCloudSecuritycenterV1beta1RunAssetDiscoveryResponse(_messages.Message):
  r"""Response of asset discovery run

  Enums:
    StateValueValuesEnum: The state of an asset discovery run.

  Fields:
    duration: The duration between asset discovery run start and end
    state: The state of an asset discovery run.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of an asset discovery run.

    Values:
      STATE_UNSPECIFIED: Asset discovery run state was unspecified.
      COMPLETED: Asset discovery run completed successfully.
      SUPERSEDED: Asset discovery run was cancelled with tasks still pending,
        as another run for the same organization was started with a higher
        priority.
      TERMINATED: Asset discovery run was killed and terminated.
    """
    STATE_UNSPECIFIED = 0
    COMPLETED = 1
    SUPERSEDED = 2
    TERMINATED = 3

  duration = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class GoogleCloudSecuritycenterV1p1beta1Finding(_messages.Message):
  r"""Security Command Center finding. A finding is a record of assessment
  data (security, risk, health or privacy) ingested into Security Command
  Center for presentation, notification, analysis, policy testing, and
  enforcement. For example, an XSS vulnerability in an App Engine application
  is a finding.

  Enums:
    SeverityValueValuesEnum: The severity of the finding. This field is
      managed by the source that writes the finding.
    StateValueValuesEnum: The state of the finding.

  Messages:
    SourcePropertiesValue: Source specific properties. These properties are
      managed by the source that writes the finding. The key names in the
      source_properties map must be between 1 and 255 characters, and must
      start with a letter and contain alphanumeric characters or underscores
      only.

  Fields:
    canonicalName: The canonical name of the finding. It's either "organizatio
      ns/{organization_id}/sources/{source_id}/findings/{finding_id}",
      "folders/{folder_id}/sources/{source_id}/findings/{finding_id}" or
      "projects/{project_number}/sources/{source_id}/findings/{finding_id}",
      depending on the closest CRM ancestor of the resource associated with
      the finding.
    category: The additional taxonomy group within findings from a given
      source. This field is immutable after creation time. Example:
      "XSS_FLASH_INJECTION"
    createTime: The time at which the finding was created in Security Command
      Center.
    eventTime: The time at which the event took place, or when an update to
      the finding occurred. For example, if the finding represents an open
      firewall it would capture the time the detector believes the firewall
      became open. The accuracy is determined by the detector. If the finding
      were to be resolved afterward, this time would reflect when the finding
      was resolved. Must not be set to a value greater than the current
      timestamp.
    externalUri: The URI that, if available, points to a web page outside of
      Security Command Center where additional information about the finding
      can be found. This field is guaranteed to be either empty or a well
      formed URL.
    name: The relative resource name of this finding. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Example: "organizations/{organization_id}/sources/{source_id}/finding
      s/{finding_id}"
    parent: The relative resource name of the source the finding belongs to.
      See: https://cloud.google.com/apis/design/resource_names#relative_resour
      ce_name This field is immutable after creation time. For example:
      "organizations/{organization_id}/sources/{source_id}"
    resourceName: For findings on Google Cloud resources, the full resource
      name of the Google Cloud resource this finding is for. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
      When the finding is for a non-Google Cloud resource, the resourceName
      can be a customer or partner defined string. This field is immutable
      after creation time.
    securityMarks: Output only. User specified security marks. These marks are
      entirely managed by the user and come from the SecurityMarks resource
      that belongs to the finding.
    severity: The severity of the finding. This field is managed by the source
      that writes the finding.
    sourceProperties: Source specific properties. These properties are managed
      by the source that writes the finding. The key names in the
      source_properties map must be between 1 and 255 characters, and must
      start with a letter and contain alphanumeric characters or underscores
      only.
    state: The state of the finding.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity of the finding. This field is managed by the source that
    writes the finding.

    Values:
      SEVERITY_UNSPECIFIED: No severity specified. The default value.
      CRITICAL: Critical severity.
      HIGH: High severity.
      MEDIUM: Medium severity.
      LOW: Low severity.
    """
    SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the finding.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      ACTIVE: The finding requires attention and has not been addressed yet.
      INACTIVE: The finding has been fixed, triaged as a non-issue or
        otherwise addressed and is no longer active.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SourcePropertiesValue(_messages.Message):
    r"""Source specific properties. These properties are managed by the source
    that writes the finding. The key names in the source_properties map must
    be between 1 and 255 characters, and must start with a letter and contain
    alphanumeric characters or underscores only.

    Messages:
      AdditionalProperty: An additional property for a SourcePropertiesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        SourcePropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SourcePropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  canonicalName = _messages.StringField(1)
  category = _messages.StringField(2)
  createTime = _messages.StringField(3)
  eventTime = _messages.StringField(4)
  externalUri = _messages.StringField(5)
  name = _messages.StringField(6)
  parent = _messages.StringField(7)
  resourceName = _messages.StringField(8)
  securityMarks = _messages.MessageField('GoogleCloudSecuritycenterV1p1beta1SecurityMarks', 9)
  severity = _messages.EnumField('SeverityValueValuesEnum', 10)
  sourceProperties = _messages.MessageField('SourcePropertiesValue', 11)
  state = _messages.EnumField('StateValueValuesEnum', 12)


class GoogleCloudSecuritycenterV1p1beta1Folder(_messages.Message):
  r"""Message that contains the resource name and display name of a folder
  resource.

  Fields:
    resourceFolder: Full resource name of this folder. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    resourceFolderDisplayName: The user defined display name for this folder.
  """

  resourceFolder = _messages.StringField(1)
  resourceFolderDisplayName = _messages.StringField(2)


class GoogleCloudSecuritycenterV1p1beta1NotificationMessage(_messages.Message):
  r"""Security Command Center's Notification

  Fields:
    finding: If it's a Finding based notification config, this field will be
      populated.
    notificationConfigName: Name of the notification config that generated
      current notification.
    resource: The Cloud resource tied to the notification.
  """

  finding = _messages.MessageField('GoogleCloudSecuritycenterV1p1beta1Finding', 1)
  notificationConfigName = _messages.StringField(2)
  resource = _messages.MessageField('GoogleCloudSecuritycenterV1p1beta1Resource', 3)


class GoogleCloudSecuritycenterV1p1beta1Resource(_messages.Message):
  r"""Information related to the Google Cloud resource.

  Fields:
    folders: Output only. Contains a Folder message for each folder in the
      assets ancestry. The first folder is the deepest nested folder, and the
      last folder is the folder directly under the Organization.
    name: The full resource name of the resource. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    parent: The full resource name of resource's parent.
    parentDisplayName: The human readable name of resource's parent.
    project: The full resource name of project that the resource belongs to.
    projectDisplayName: The project id that the resource belongs to.
  """

  folders = _messages.MessageField('GoogleCloudSecuritycenterV1p1beta1Folder', 1, repeated=True)
  name = _messages.StringField(2)
  parent = _messages.StringField(3)
  parentDisplayName = _messages.StringField(4)
  project = _messages.StringField(5)
  projectDisplayName = _messages.StringField(6)


class GoogleCloudSecuritycenterV1p1beta1RunAssetDiscoveryResponse(_messages.Message):
  r"""Response of asset discovery run

  Enums:
    StateValueValuesEnum: The state of an asset discovery run.

  Fields:
    duration: The duration between asset discovery run start and end
    state: The state of an asset discovery run.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of an asset discovery run.

    Values:
      STATE_UNSPECIFIED: Asset discovery run state was unspecified.
      COMPLETED: Asset discovery run completed successfully.
      SUPERSEDED: Asset discovery run was cancelled with tasks still pending,
        as another run for the same organization was started with a higher
        priority.
      TERMINATED: Asset discovery run was killed and terminated.
    """
    STATE_UNSPECIFIED = 0
    COMPLETED = 1
    SUPERSEDED = 2
    TERMINATED = 3

  duration = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class GoogleCloudSecuritycenterV1p1beta1SecurityMarks(_messages.Message):
  r"""User specified security marks that are attached to the parent Security
  Command Center resource. Security marks are scoped within a Security Command
  Center organization -- they can be modified and viewed by all users who have
  proper permissions on the organization.

  Messages:
    MarksValue: Mutable user specified security marks belonging to the parent
      resource. Constraints are as follows: * Keys and values are treated as
      case insensitive * Keys must be between 1 - 256 characters (inclusive) *
      Keys must be letters, numbers, underscores, or dashes * Values have
      leading and trailing whitespace trimmed, remaining characters must be
      between 1 - 4096 characters (inclusive)

  Fields:
    canonicalName: The canonical name of the marks. Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks"
      "folders/{folder_id}/assets/{asset_id}/securityMarks"
      "projects/{project_number}/assets/{asset_id}/securityMarks" "organizatio
      ns/{organization_id}/sources/{source_id}/findings/{finding_id}/securityM
      arks" "folders/{folder_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks" "projects/{project_number}/sources/{source_id}/findings/{fin
      ding_id}/securityMarks"
    marks: Mutable user specified security marks belonging to the parent
      resource. Constraints are as follows: * Keys and values are treated as
      case insensitive * Keys must be between 1 - 256 characters (inclusive) *
      Keys must be letters, numbers, underscores, or dashes * Values have
      leading and trailing whitespace trimmed, remaining characters must be
      between 1 - 4096 characters (inclusive)
    name: The relative resource name of the SecurityMarks. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks" "organ
      izations/{organization_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks".
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MarksValue(_messages.Message):
    r"""Mutable user specified security marks belonging to the parent
    resource. Constraints are as follows: * Keys and values are treated as
    case insensitive * Keys must be between 1 - 256 characters (inclusive) *
    Keys must be letters, numbers, underscores, or dashes * Values have
    leading and trailing whitespace trimmed, remaining characters must be
    between 1 - 4096 characters (inclusive)

    Messages:
      AdditionalProperty: An additional property for a MarksValue object.

    Fields:
      additionalProperties: Additional properties of type MarksValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MarksValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  canonicalName = _messages.StringField(1)
  marks = _messages.MessageField('MarksValue', 2)
  name = _messages.StringField(3)


class GroupAssetsRequest(_messages.Message):
  r"""Request message for grouping by assets.

  Fields:
    compareDuration: When compare_duration is set, the GroupResult's
      "state_change" property is updated to indicate whether the asset was
      added, removed, or remained present during the compare_duration period
      of time that precedes the read_time. This is the time between (read_time
      - compare_duration) and read_time. The state change value is derived
      based on the presence of the asset at the two points in time.
      Intermediate state changes between the two times don't affect the
      result. For example, the results aren't affected if the asset is removed
      and re-created again. Possible "state_change" values when
      compare_duration is specified: * "ADDED": indicates that the asset was
      not present at the start of compare_duration, but present at
      reference_time. * "REMOVED": indicates that the asset was present at the
      start of compare_duration, but not present at reference_time. *
      "ACTIVE": indicates that the asset was present at both the start and the
      end of the time period defined by compare_duration and reference_time.
      If compare_duration is not specified, then the only possible
      state_change is "UNUSED", which will be the state_change set for all
      assets present at read_time. If this field is set then `state_change`
      must be a specified field in `group_by`.
    filter: Expression that defines the filter to apply across assets. The
      expression is a list of zero or more restrictions combined via logical
      operators `AND` and `OR`. Parentheses are supported, and `OR` has higher
      precedence than `AND`. Restrictions have the form ` ` and may have a `-`
      character in front of them to indicate negation. The fields map to those
      defined in the Asset resource. Examples include: * name *
      security_center_properties.resource_name *
      resource_properties.a_property * security_marks.marks.marka The
      supported operators are: * `=` for all value types. * `>`, `<`, `>=`,
      `<=` for integer values. * `:`, meaning substring matching, for strings.
      The supported value types are: * string literals in quotes. * integer
      literals without quotes. * boolean literals `true` and `false` without
      quotes. The following field and operator combinations are supported: *
      name: `=` * update_time: `=`, `>`, `<`, `>=`, `<=` Usage: This should be
      milliseconds since epoch or an RFC3339 string. Examples: `update_time =
      "2019-06-10T16:07:18-07:00"` `update_time = 1560208038000` *
      create_time: `=`, `>`, `<`, `>=`, `<=` Usage: This should be
      milliseconds since epoch or an RFC3339 string. Examples: `create_time =
      "2019-06-10T16:07:18-07:00"` `create_time = 1560208038000` *
      iam_policy.policy_blob: `=`, `:` * resource_properties: `=`, `:`, `>`,
      `<`, `>=`, `<=` * security_marks.marks: `=`, `:` *
      security_center_properties.resource_name: `=`, `:` *
      security_center_properties.resource_display_name: `=`, `:` *
      security_center_properties.resource_type: `=`, `:` *
      security_center_properties.resource_parent: `=`, `:` *
      security_center_properties.resource_parent_display_name: `=`, `:` *
      security_center_properties.resource_project: `=`, `:` *
      security_center_properties.resource_project_display_name: `=`, `:` *
      security_center_properties.resource_owners: `=`, `:` For example,
      `resource_properties.size = 100` is a valid filter string. Use a partial
      match on the empty string to filter based on a property existing:
      `resource_properties.my_property : ""` Use a negated partial match on
      the empty string to filter based on a property not existing:
      `-resource_properties.my_property : ""`
    groupBy: Required. Expression that defines what assets fields to use for
      grouping. The string value should follow SQL syntax: comma separated
      list of fields. For example: "security_center_properties.resource_projec
      t,security_center_properties.project". The following fields are
      supported when compare_duration is not set: *
      security_center_properties.resource_project *
      security_center_properties.resource_project_display_name *
      security_center_properties.resource_type *
      security_center_properties.resource_parent *
      security_center_properties.resource_parent_display_name The following
      fields are supported when compare_duration is set: *
      security_center_properties.resource_type *
      security_center_properties.resource_project_display_name *
      security_center_properties.resource_parent_display_name
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last `GroupAssetsResponse`; indicates
      that this is a continuation of a prior `GroupAssets` call, and that the
      system should return the next page of data.
    readTime: Time used as a reference point when filtering assets. The filter
      is limited to assets existing at the supplied time and their values are
      those at that specific time. Absence of this field will default to the
      API's version of NOW.
  """

  compareDuration = _messages.StringField(1)
  filter = _messages.StringField(2)
  groupBy = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  readTime = _messages.StringField(6)


class GroupAssetsResponse(_messages.Message):
  r"""Response message for grouping by assets.

  Fields:
    groupByResults: Group results. There exists an element for each existing
      unique combination of property/values. The element contains a count for
      the number of times those specific property/values appear.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results.
    readTime: Time used for executing the groupBy request.
    totalSize: The total number of results matching the query.
  """

  groupByResults = _messages.MessageField('GroupResult', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  readTime = _messages.StringField(3)
  totalSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GroupFindingsRequest(_messages.Message):
  r"""Request message for grouping by findings.

  Fields:
    compareDuration: When compare_duration is set, the GroupResult's
      "state_change" attribute is updated to indicate whether the finding had
      its state changed, the finding's state remained unchanged, or if the
      finding was added during the compare_duration period of time that
      precedes the read_time. This is the time between (read_time -
      compare_duration) and read_time. The state_change value is derived based
      on the presence and state of the finding at the two points in time.
      Intermediate state changes between the two times don't affect the
      result. For example, the results aren't affected if the finding is made
      inactive and then active again. Possible "state_change" values when
      compare_duration is specified: * "CHANGED": indicates that the finding
      was present and matched the given filter at the start of
      compare_duration, but changed its state at read_time. * "UNCHANGED":
      indicates that the finding was present and matched the given filter at
      the start of compare_duration and did not change state at read_time. *
      "ADDED": indicates that the finding did not match the given filter or
      was not present at the start of compare_duration, but was present at
      read_time. * "REMOVED": indicates that the finding was present and
      matched the filter at the start of compare_duration, but did not match
      the filter at read_time. If compare_duration is not specified, then the
      only possible state_change is "UNUSED", which will be the state_change
      set for all findings present at read_time. If this field is set then
      `state_change` must be a specified field in `group_by`.
    filter: Expression that defines the filter to apply across findings. The
      expression is a list of one or more restrictions combined via logical
      operators `AND` and `OR`. Parentheses are supported, and `OR` has higher
      precedence than `AND`. Restrictions have the form ` ` and may have a `-`
      character in front of them to indicate negation. Examples include: *
      name * source_properties.a_property * security_marks.marks.marka The
      supported operators are: * `=` for all value types. * `>`, `<`, `>=`,
      `<=` for integer values. * `:`, meaning substring matching, for strings.
      The supported value types are: * string literals in quotes. * integer
      literals without quotes. * boolean literals `true` and `false` without
      quotes. The following field and operator combinations are supported: *
      name: `=` * parent: `=`, `:` * resource_name: `=`, `:` * state: `=`, `:`
      * category: `=`, `:` * external_uri: `=`, `:` * event_time: `=`, `>`,
      `<`, `>=`, `<=` Usage: This should be milliseconds since epoch or an
      RFC3339 string. Examples: `event_time = "2019-06-10T16:07:18-07:00"`
      `event_time = 1560208038000` * severity: `=`, `:` * workflow_state: `=`,
      `:` * security_marks.marks: `=`, `:` * source_properties: `=`, `:`, `>`,
      `<`, `>=`, `<=` For example, `source_properties.size = 100` is a valid
      filter string. Use a partial match on the empty string to filter based
      on a property existing: `source_properties.my_property : ""` Use a
      negated partial match on the empty string to filter based on a property
      not existing: `-source_properties.my_property : ""` * resource: *
      resource.name: `=`, `:` * resource.parent_name: `=`, `:` *
      resource.parent_display_name: `=`, `:` * resource.project_name: `=`, `:`
      * resource.project_display_name: `=`, `:` * resource.type: `=`, `:`
    groupBy: Required. Expression that defines what assets fields to use for
      grouping (including `state_change`). The string value should follow SQL
      syntax: comma separated list of fields. For example:
      "parent,resource_name". The following fields are supported: *
      resource_name * category * state * parent * severity The following
      fields are supported when compare_duration is set: * state_change
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last `GroupFindingsResponse`;
      indicates that this is a continuation of a prior `GroupFindings` call,
      and that the system should return the next page of data.
    readTime: Time used as a reference point when filtering findings. The
      filter is limited to findings existing at the supplied time and their
      values are those at that specific time. Absence of this field will
      default to the API's version of NOW.
  """

  compareDuration = _messages.StringField(1)
  filter = _messages.StringField(2)
  groupBy = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  readTime = _messages.StringField(6)


class GroupFindingsResponse(_messages.Message):
  r"""Response message for group by findings.

  Fields:
    groupByResults: Group results. There exists an element for each existing
      unique combination of property/values. The element contains a count for
      the number of times those specific property/values appear.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results.
    readTime: Time used for executing the groupBy request.
    totalSize: The total number of results matching the query.
  """

  groupByResults = _messages.MessageField('GroupResult', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  readTime = _messages.StringField(3)
  totalSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GroupResult(_messages.Message):
  r"""Result containing the properties and count of a groupBy request.

  Messages:
    PropertiesValue: Properties matching the groupBy fields in the request.

  Fields:
    count: Total count of resources for the given properties.
    properties: Properties matching the groupBy fields in the request.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PropertiesValue(_messages.Message):
    r"""Properties matching the groupBy fields in the request.

    Messages:
      AdditionalProperty: An additional property for a PropertiesValue object.

    Fields:
      additionalProperties: Additional properties of type PropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  count = _messages.IntegerField(1)
  properties = _messages.MessageField('PropertiesValue', 2)


class IamBinding(_messages.Message):
  r"""Represents a particular IAM binding, which captures a member's role
  addition, removal, or state.

  Enums:
    ActionValueValuesEnum: The action that was performed on a Binding.

  Fields:
    action: The action that was performed on a Binding.
    member: A single identity requesting access for a Cloud Platform resource,
      for example, "<EMAIL>".
    role: Role that is assigned to "members". For example, "roles/viewer",
      "roles/editor", or "roles/owner".
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""The action that was performed on a Binding.

    Values:
      ACTION_UNSPECIFIED: Unspecified.
      ADD: Addition of a Binding.
      REMOVE: Removal of a Binding.
    """
    ACTION_UNSPECIFIED = 0
    ADD = 1
    REMOVE = 2

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  member = _messages.StringField(2)
  role = _messages.StringField(3)


class IamPolicy(_messages.Message):
  r"""Cloud IAM Policy information associated with the Google Cloud resource
  described by the Security Command Center asset. This information is managed
  and defined by the Google Cloud resource and cannot be modified by the user.

  Fields:
    policyBlob: The JSON representation of the Policy associated with the
      asset. See https://cloud.google.com/iam/reference/rest/v1/Policy for
      format details.
  """

  policyBlob = _messages.StringField(1)


class Indicator(_messages.Message):
  r"""Represents what's commonly known as an _indicator of compromise_ (IoC)
  in computer forensics. This is an artifact observed on a network or in an
  operating system that, with high confidence, indicates a computer intrusion.
  For more information, see [Indicator of
  compromise](https://en.wikipedia.org/wiki/Indicator_of_compromise).

  Fields:
    domains: List of domains associated to the Finding.
    ipAddresses: The list of IP addresses that are associated with the
      finding.
    signatures: The list of matched signatures indicating that the given
      process is present in the environment.
    uris: The list of URIs associated to the Findings.
  """

  domains = _messages.StringField(1, repeated=True)
  ipAddresses = _messages.StringField(2, repeated=True)
  signatures = _messages.MessageField('ProcessSignature', 3, repeated=True)
  uris = _messages.StringField(4, repeated=True)


class KernelRootkit(_messages.Message):
  r"""Kernel mode rootkit signatures.

  Fields:
    name: Rootkit name, when available.
    unexpectedCodeModification: True if unexpected modifications of kernel
      code memory are present.
    unexpectedFtraceHandler: True if `ftrace` points are present with
      callbacks pointing to regions that are not in the expected kernel or
      module code range.
    unexpectedInterruptHandler: True if interrupt handlers that are are not in
      the expected kernel or module code regions are present.
    unexpectedKernelCodePages: True if kernel code pages that are not in the
      expected kernel or module code regions are present.
    unexpectedKprobeHandler: True if `kprobe` points are present with
      callbacks pointing to regions that are not in the expected kernel or
      module code range.
    unexpectedProcessesInRunqueue: True if unexpected processes in the
      scheduler run queue are present. Such processes are in the run queue,
      but not in the process task list.
    unexpectedReadOnlyDataModification: True if unexpected modifications of
      kernel read-only data memory are present.
    unexpectedSystemCallHandler: True if system call handlers that are are not
      in the expected kernel or module code regions are present.
  """

  name = _messages.StringField(1)
  unexpectedCodeModification = _messages.BooleanField(2)
  unexpectedFtraceHandler = _messages.BooleanField(3)
  unexpectedInterruptHandler = _messages.BooleanField(4)
  unexpectedKernelCodePages = _messages.BooleanField(5)
  unexpectedKprobeHandler = _messages.BooleanField(6)
  unexpectedProcessesInRunqueue = _messages.BooleanField(7)
  unexpectedReadOnlyDataModification = _messages.BooleanField(8)
  unexpectedSystemCallHandler = _messages.BooleanField(9)


class Kubernetes(_messages.Message):
  r"""Kubernetes-related attributes.

  Fields:
    accessReviews: Provides information on any Kubernetes access reviews
      (privilege checks) relevant to the finding.
    bindings: Provides Kubernetes role binding information for findings that
      involve [RoleBindings or
      ClusterRoleBindings](https://cloud.google.com/kubernetes-
      engine/docs/how-to/role-based-access-control).
    nodePools: GKE [node pools](https://cloud.google.com/kubernetes-
      engine/docs/concepts/node-pools) associated with the finding. This field
      contains node pool information for each node, when it is available.
    nodes: Provides Kubernetes [node](https://cloud.google.com/kubernetes-
      engine/docs/concepts/cluster-architecture#nodes) information.
    pods: Kubernetes [Pods](https://cloud.google.com/kubernetes-
      engine/docs/concepts/pod) associated with the finding. This field
      contains Pod records for each container that is owned by a Pod.
    roles: Provides Kubernetes role information for findings that involve
      [Roles or ClusterRoles](https://cloud.google.com/kubernetes-
      engine/docs/how-to/role-based-access-control).
  """

  accessReviews = _messages.MessageField('AccessReview', 1, repeated=True)
  bindings = _messages.MessageField('GoogleCloudSecuritycenterV1Binding', 2, repeated=True)
  nodePools = _messages.MessageField('NodePool', 3, repeated=True)
  nodes = _messages.MessageField('Node', 4, repeated=True)
  pods = _messages.MessageField('Pod', 5, repeated=True)
  roles = _messages.MessageField('Role', 6, repeated=True)


class Label(_messages.Message):
  r"""Represents a generic name-value label. A label has separate name and
  value fields to support filtering with the `contains()` function. For more
  information, see [Filtering on array-type
  fields](https://cloud.google.com/security-command-center/docs/how-to-api-
  list-findings#array-contains-filtering).

  Fields:
    name: Name of the label.
    value: Value that corresponds to the label's name.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class ListAssetsResponse(_messages.Message):
  r"""Response message for listing assets.

  Fields:
    listAssetsResults: Assets matching the list request.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results.
    readTime: Time used for executing the list request.
    totalSize: The total number of assets matching the query.
  """

  listAssetsResults = _messages.MessageField('ListAssetsResult', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  readTime = _messages.StringField(3)
  totalSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class ListAssetsResult(_messages.Message):
  r"""Result containing the Asset and its State.

  Enums:
    StateChangeValueValuesEnum: State change of the asset between the points
      in time.

  Fields:
    asset: Asset matching the search request.
    stateChange: State change of the asset between the points in time.
  """

  class StateChangeValueValuesEnum(_messages.Enum):
    r"""State change of the asset between the points in time.

    Values:
      UNUSED: State change is unused, this is the canonical default for this
        enum.
      ADDED: Asset was added between the points in time.
      REMOVED: Asset was removed between the points in time.
      ACTIVE: Asset was present at both point(s) in time.
    """
    UNUSED = 0
    ADDED = 1
    REMOVED = 2
    ACTIVE = 3

  asset = _messages.MessageField('Asset', 1)
  stateChange = _messages.EnumField('StateChangeValueValuesEnum', 2)


class ListAttackPathsResponse(_messages.Message):
  r"""Response message for listing the attack paths for a given simulation or
  valued resource.

  Fields:
    attackPaths: The attack paths that the attack path simulation identified.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results.
  """

  attackPaths = _messages.MessageField('AttackPath', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListBigQueryExportsResponse(_messages.Message):
  r"""Response message for listing BigQuery exports.

  Fields:
    bigQueryExports: The BigQuery exports from the specified parent.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  bigQueryExports = _messages.MessageField('GoogleCloudSecuritycenterV1BigQueryExport', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDescendantEventThreatDetectionCustomModulesResponse(_messages.Message):
  r"""Response for listing current and descendant resident Event Threat
  Detection custom modules.

  Fields:
    eventThreatDetectionCustomModules: Custom modules belonging to the
      requested parent.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  eventThreatDetectionCustomModules = _messages.MessageField('EventThreatDetectionCustomModule', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDescendantSecurityHealthAnalyticsCustomModulesResponse(_messages.Message):
  r"""Response message for listing descendant Security Health Analytics custom
  modules.

  Fields:
    nextPageToken: If not empty, indicates that there may be more custom
      modules to be returned.
    securityHealthAnalyticsCustomModules: Custom modules belonging to the
      requested parent and its descendants.
  """

  nextPageToken = _messages.StringField(1)
  securityHealthAnalyticsCustomModules = _messages.MessageField('GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule', 2, repeated=True)


class ListEffectiveEventThreatDetectionCustomModulesResponse(_messages.Message):
  r"""Response for listing EffectiveEventThreatDetectionCustomModules.

  Fields:
    effectiveEventThreatDetectionCustomModules: Effective custom modules
      belonging to the requested parent.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  effectiveEventThreatDetectionCustomModules = _messages.MessageField('EffectiveEventThreatDetectionCustomModule', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListEffectiveSecurityHealthAnalyticsCustomModulesResponse(_messages.Message):
  r"""Response message for listing effective Security Health Analytics custom
  modules.

  Fields:
    effectiveSecurityHealthAnalyticsCustomModules: Effective custom modules
      belonging to the requested parent.
    nextPageToken: If not empty, indicates that there may be more effective
      custom modules to be returned.
  """

  effectiveSecurityHealthAnalyticsCustomModules = _messages.MessageField('GoogleCloudSecuritycenterV1EffectiveSecurityHealthAnalyticsCustomModule', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListEventThreatDetectionCustomModulesResponse(_messages.Message):
  r"""Response for listing Event Threat Detection custom modules.

  Fields:
    eventThreatDetectionCustomModules: Custom modules belonging to the
      requested parent.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  eventThreatDetectionCustomModules = _messages.MessageField('EventThreatDetectionCustomModule', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListFindingsResponse(_messages.Message):
  r"""Response message for listing findings.

  Fields:
    listFindingsResults: Findings matching the list request.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results.
    readTime: Time used for executing the list request.
    totalSize: The total number of findings matching the query.
  """

  listFindingsResults = _messages.MessageField('ListFindingsResult', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  readTime = _messages.StringField(3)
  totalSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class ListFindingsResult(_messages.Message):
  r"""Result containing the Finding and its StateChange.

  Enums:
    StateChangeValueValuesEnum: State change of the finding between the points
      in time.

  Fields:
    finding: Finding matching the search request.
    resource: Output only. Resource that is associated with this finding.
    stateChange: State change of the finding between the points in time.
  """

  class StateChangeValueValuesEnum(_messages.Enum):
    r"""State change of the finding between the points in time.

    Values:
      UNUSED: State change is unused, this is the canonical default for this
        enum.
      CHANGED: The finding has changed state in some way between the points in
        time and existed at both points.
      UNCHANGED: The finding has not changed state between the points in time
        and existed at both points.
      ADDED: The finding was created between the points in time.
      REMOVED: The finding at timestamp does not match the filter specified,
        but it did at timestamp - compare_duration.
    """
    UNUSED = 0
    CHANGED = 1
    UNCHANGED = 2
    ADDED = 3
    REMOVED = 4

  finding = _messages.MessageField('Finding', 1)
  resource = _messages.MessageField('Resource', 2)
  stateChange = _messages.EnumField('StateChangeValueValuesEnum', 3)


class ListMuteConfigsResponse(_messages.Message):
  r"""Response message for listing mute configs.

  Fields:
    muteConfigs: The mute configs from the specified parent.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  muteConfigs = _messages.MessageField('GoogleCloudSecuritycenterV1MuteConfig', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListNotificationConfigsResponse(_messages.Message):
  r"""Response message for listing notification configs.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results.
    notificationConfigs: Notification configs belonging to the requested
      parent.
  """

  nextPageToken = _messages.StringField(1)
  notificationConfigs = _messages.MessageField('NotificationConfig', 2, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListResourceValueConfigsResponse(_messages.Message):
  r"""Response message to list resource value configs

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is empty, there are no subsequent pages.
    resourceValueConfigs: The resource value configs from the specified
      parent.
  """

  nextPageToken = _messages.StringField(1)
  resourceValueConfigs = _messages.MessageField('GoogleCloudSecuritycenterV1ResourceValueConfig', 2, repeated=True)


class ListSecurityHealthAnalyticsCustomModulesResponse(_messages.Message):
  r"""Response message for listing Security Health Analytics custom modules.

  Fields:
    nextPageToken: If not empty, indicates that there may be more custom
      modules to be returned.
    securityHealthAnalyticsCustomModules: Custom modules belonging to the
      requested parent.
  """

  nextPageToken = _messages.StringField(1)
  securityHealthAnalyticsCustomModules = _messages.MessageField('GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule', 2, repeated=True)


class ListSourcesResponse(_messages.Message):
  r"""Response message for listing sources.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results.
    sources: Sources belonging to the requested parent.
  """

  nextPageToken = _messages.StringField(1)
  sources = _messages.MessageField('Source', 2, repeated=True)


class ListValuedResourcesResponse(_messages.Message):
  r"""Response message for listing the valued resources for a given
  simulation.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results.
    totalSize: The estimated total number of results matching the query.
    valuedResources: The valued resources that the attack path simulation
      identified.
  """

  nextPageToken = _messages.StringField(1)
  totalSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  valuedResources = _messages.MessageField('ValuedResource', 3, repeated=True)


class MemoryHashSignature(_messages.Message):
  r"""A signature corresponding to memory page hashes.

  Fields:
    binaryFamily: The binary family.
    detections: The list of memory hash detections contributing to the binary
      family match.
  """

  binaryFamily = _messages.StringField(1)
  detections = _messages.MessageField('Detection', 2, repeated=True)


class MitreAttack(_messages.Message):
  r"""MITRE ATT&CK tactics and techniques related to this finding. See:
  https://attack.mitre.org

  Enums:
    AdditionalTacticsValueListEntryValuesEnum:
    AdditionalTechniquesValueListEntryValuesEnum:
    PrimaryTacticValueValuesEnum: The MITRE ATT&CK tactic most closely
      represented by this finding, if any.
    PrimaryTechniquesValueListEntryValuesEnum:

  Fields:
    additionalTactics: Additional MITRE ATT&CK tactics related to this
      finding, if any.
    additionalTechniques: Additional MITRE ATT&CK techniques related to this
      finding, if any, along with any of their respective parent techniques.
    primaryTactic: The MITRE ATT&CK tactic most closely represented by this
      finding, if any.
    primaryTechniques: The MITRE ATT&CK technique most closely represented by
      this finding, if any. primary_techniques is a repeated field because
      there are multiple levels of MITRE ATT&CK techniques. If the technique
      most closely represented by this finding is a sub-technique (e.g.
      `SCANNING_IP_BLOCKS`), both the sub-technique and its parent
      technique(s) will be listed (e.g. `SCANNING_IP_BLOCKS`,
      `ACTIVE_SCANNING`).
    version: The MITRE ATT&CK version referenced by the above fields. E.g.
      "8".
  """

  class AdditionalTacticsValueListEntryValuesEnum(_messages.Enum):
    r"""AdditionalTacticsValueListEntryValuesEnum enum type.

    Values:
      TACTIC_UNSPECIFIED: Unspecified value.
      RECONNAISSANCE: TA0043
      RESOURCE_DEVELOPMENT: TA0042
      INITIAL_ACCESS: TA0001
      EXECUTION: TA0002
      PERSISTENCE: TA0003
      PRIVILEGE_ESCALATION: TA0004
      DEFENSE_EVASION: TA0005
      CREDENTIAL_ACCESS: TA0006
      DISCOVERY: TA0007
      LATERAL_MOVEMENT: TA0008
      COLLECTION: TA0009
      COMMAND_AND_CONTROL: TA0011
      EXFILTRATION: TA0010
      IMPACT: TA0040
    """
    TACTIC_UNSPECIFIED = 0
    RECONNAISSANCE = 1
    RESOURCE_DEVELOPMENT = 2
    INITIAL_ACCESS = 3
    EXECUTION = 4
    PERSISTENCE = 5
    PRIVILEGE_ESCALATION = 6
    DEFENSE_EVASION = 7
    CREDENTIAL_ACCESS = 8
    DISCOVERY = 9
    LATERAL_MOVEMENT = 10
    COLLECTION = 11
    COMMAND_AND_CONTROL = 12
    EXFILTRATION = 13
    IMPACT = 14

  class AdditionalTechniquesValueListEntryValuesEnum(_messages.Enum):
    r"""AdditionalTechniquesValueListEntryValuesEnum enum type.

    Values:
      TECHNIQUE_UNSPECIFIED: Unspecified value.
      ACTIVE_SCANNING: T1595
      SCANNING_IP_BLOCKS: T1595.001
      INGRESS_TOOL_TRANSFER: T1105
      NATIVE_API: T1106
      SHARED_MODULES: T1129
      COMMAND_AND_SCRIPTING_INTERPRETER: T1059
      UNIX_SHELL: T1059.004
      RESOURCE_HIJACKING: T1496
      PROXY: T1090
      EXTERNAL_PROXY: T1090.002
      MULTI_HOP_PROXY: T1090.003
      DYNAMIC_RESOLUTION: T1568
      UNSECURED_CREDENTIALS: T1552
      VALID_ACCOUNTS: T1078
      LOCAL_ACCOUNTS: T1078.003
      CLOUD_ACCOUNTS: T1078.004
      NETWORK_DENIAL_OF_SERVICE: T1498
      PERMISSION_GROUPS_DISCOVERY: T1069
      CLOUD_GROUPS: T1069.003
      EXFILTRATION_OVER_WEB_SERVICE: T1567
      EXFILTRATION_TO_CLOUD_STORAGE: T1567.002
      ACCOUNT_MANIPULATION: T1098
      SSH_AUTHORIZED_KEYS: T1098.004
      CREATE_OR_MODIFY_SYSTEM_PROCESS: T1543
      STEAL_WEB_SESSION_COOKIE: T1539
      MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE: T1578
      EXPLOIT_PUBLIC_FACING_APPLICATION: T1190
      MODIFY_AUTHENTICATION_PROCESS: T1556
      DATA_DESTRUCTION: T1485
      DOMAIN_POLICY_MODIFICATION: T1484
      IMPAIR_DEFENSES: T1562
      NETWORK_SERVICE_DISCOVERY: T1046
      ACCESS_TOKEN_MANIPULATION: T1134
      ABUSE_ELEVATION_CONTROL_MECHANISM: T1548
      DEFAULT_ACCOUNTS: T1078.001
      INHIBIT_SYSTEM_RECOVERY: T1490
    """
    TECHNIQUE_UNSPECIFIED = 0
    ACTIVE_SCANNING = 1
    SCANNING_IP_BLOCKS = 2
    INGRESS_TOOL_TRANSFER = 3
    NATIVE_API = 4
    SHARED_MODULES = 5
    COMMAND_AND_SCRIPTING_INTERPRETER = 6
    UNIX_SHELL = 7
    RESOURCE_HIJACKING = 8
    PROXY = 9
    EXTERNAL_PROXY = 10
    MULTI_HOP_PROXY = 11
    DYNAMIC_RESOLUTION = 12
    UNSECURED_CREDENTIALS = 13
    VALID_ACCOUNTS = 14
    LOCAL_ACCOUNTS = 15
    CLOUD_ACCOUNTS = 16
    NETWORK_DENIAL_OF_SERVICE = 17
    PERMISSION_GROUPS_DISCOVERY = 18
    CLOUD_GROUPS = 19
    EXFILTRATION_OVER_WEB_SERVICE = 20
    EXFILTRATION_TO_CLOUD_STORAGE = 21
    ACCOUNT_MANIPULATION = 22
    SSH_AUTHORIZED_KEYS = 23
    CREATE_OR_MODIFY_SYSTEM_PROCESS = 24
    STEAL_WEB_SESSION_COOKIE = 25
    MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE = 26
    EXPLOIT_PUBLIC_FACING_APPLICATION = 27
    MODIFY_AUTHENTICATION_PROCESS = 28
    DATA_DESTRUCTION = 29
    DOMAIN_POLICY_MODIFICATION = 30
    IMPAIR_DEFENSES = 31
    NETWORK_SERVICE_DISCOVERY = 32
    ACCESS_TOKEN_MANIPULATION = 33
    ABUSE_ELEVATION_CONTROL_MECHANISM = 34
    DEFAULT_ACCOUNTS = 35
    INHIBIT_SYSTEM_RECOVERY = 36

  class PrimaryTacticValueValuesEnum(_messages.Enum):
    r"""The MITRE ATT&CK tactic most closely represented by this finding, if
    any.

    Values:
      TACTIC_UNSPECIFIED: Unspecified value.
      RECONNAISSANCE: TA0043
      RESOURCE_DEVELOPMENT: TA0042
      INITIAL_ACCESS: TA0001
      EXECUTION: TA0002
      PERSISTENCE: TA0003
      PRIVILEGE_ESCALATION: TA0004
      DEFENSE_EVASION: TA0005
      CREDENTIAL_ACCESS: TA0006
      DISCOVERY: TA0007
      LATERAL_MOVEMENT: TA0008
      COLLECTION: TA0009
      COMMAND_AND_CONTROL: TA0011
      EXFILTRATION: TA0010
      IMPACT: TA0040
    """
    TACTIC_UNSPECIFIED = 0
    RECONNAISSANCE = 1
    RESOURCE_DEVELOPMENT = 2
    INITIAL_ACCESS = 3
    EXECUTION = 4
    PERSISTENCE = 5
    PRIVILEGE_ESCALATION = 6
    DEFENSE_EVASION = 7
    CREDENTIAL_ACCESS = 8
    DISCOVERY = 9
    LATERAL_MOVEMENT = 10
    COLLECTION = 11
    COMMAND_AND_CONTROL = 12
    EXFILTRATION = 13
    IMPACT = 14

  class PrimaryTechniquesValueListEntryValuesEnum(_messages.Enum):
    r"""PrimaryTechniquesValueListEntryValuesEnum enum type.

    Values:
      TECHNIQUE_UNSPECIFIED: Unspecified value.
      ACTIVE_SCANNING: T1595
      SCANNING_IP_BLOCKS: T1595.001
      INGRESS_TOOL_TRANSFER: T1105
      NATIVE_API: T1106
      SHARED_MODULES: T1129
      COMMAND_AND_SCRIPTING_INTERPRETER: T1059
      UNIX_SHELL: T1059.004
      RESOURCE_HIJACKING: T1496
      PROXY: T1090
      EXTERNAL_PROXY: T1090.002
      MULTI_HOP_PROXY: T1090.003
      DYNAMIC_RESOLUTION: T1568
      UNSECURED_CREDENTIALS: T1552
      VALID_ACCOUNTS: T1078
      LOCAL_ACCOUNTS: T1078.003
      CLOUD_ACCOUNTS: T1078.004
      NETWORK_DENIAL_OF_SERVICE: T1498
      PERMISSION_GROUPS_DISCOVERY: T1069
      CLOUD_GROUPS: T1069.003
      EXFILTRATION_OVER_WEB_SERVICE: T1567
      EXFILTRATION_TO_CLOUD_STORAGE: T1567.002
      ACCOUNT_MANIPULATION: T1098
      SSH_AUTHORIZED_KEYS: T1098.004
      CREATE_OR_MODIFY_SYSTEM_PROCESS: T1543
      STEAL_WEB_SESSION_COOKIE: T1539
      MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE: T1578
      EXPLOIT_PUBLIC_FACING_APPLICATION: T1190
      MODIFY_AUTHENTICATION_PROCESS: T1556
      DATA_DESTRUCTION: T1485
      DOMAIN_POLICY_MODIFICATION: T1484
      IMPAIR_DEFENSES: T1562
      NETWORK_SERVICE_DISCOVERY: T1046
      ACCESS_TOKEN_MANIPULATION: T1134
      ABUSE_ELEVATION_CONTROL_MECHANISM: T1548
      DEFAULT_ACCOUNTS: T1078.001
      INHIBIT_SYSTEM_RECOVERY: T1490
    """
    TECHNIQUE_UNSPECIFIED = 0
    ACTIVE_SCANNING = 1
    SCANNING_IP_BLOCKS = 2
    INGRESS_TOOL_TRANSFER = 3
    NATIVE_API = 4
    SHARED_MODULES = 5
    COMMAND_AND_SCRIPTING_INTERPRETER = 6
    UNIX_SHELL = 7
    RESOURCE_HIJACKING = 8
    PROXY = 9
    EXTERNAL_PROXY = 10
    MULTI_HOP_PROXY = 11
    DYNAMIC_RESOLUTION = 12
    UNSECURED_CREDENTIALS = 13
    VALID_ACCOUNTS = 14
    LOCAL_ACCOUNTS = 15
    CLOUD_ACCOUNTS = 16
    NETWORK_DENIAL_OF_SERVICE = 17
    PERMISSION_GROUPS_DISCOVERY = 18
    CLOUD_GROUPS = 19
    EXFILTRATION_OVER_WEB_SERVICE = 20
    EXFILTRATION_TO_CLOUD_STORAGE = 21
    ACCOUNT_MANIPULATION = 22
    SSH_AUTHORIZED_KEYS = 23
    CREATE_OR_MODIFY_SYSTEM_PROCESS = 24
    STEAL_WEB_SESSION_COOKIE = 25
    MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE = 26
    EXPLOIT_PUBLIC_FACING_APPLICATION = 27
    MODIFY_AUTHENTICATION_PROCESS = 28
    DATA_DESTRUCTION = 29
    DOMAIN_POLICY_MODIFICATION = 30
    IMPAIR_DEFENSES = 31
    NETWORK_SERVICE_DISCOVERY = 32
    ACCESS_TOKEN_MANIPULATION = 33
    ABUSE_ELEVATION_CONTROL_MECHANISM = 34
    DEFAULT_ACCOUNTS = 35
    INHIBIT_SYSTEM_RECOVERY = 36

  additionalTactics = _messages.EnumField('AdditionalTacticsValueListEntryValuesEnum', 1, repeated=True)
  additionalTechniques = _messages.EnumField('AdditionalTechniquesValueListEntryValuesEnum', 2, repeated=True)
  primaryTactic = _messages.EnumField('PrimaryTacticValueValuesEnum', 3)
  primaryTechniques = _messages.EnumField('PrimaryTechniquesValueListEntryValuesEnum', 4, repeated=True)
  version = _messages.StringField(5)


class Node(_messages.Message):
  r"""Kubernetes nodes associated with the finding.

  Fields:
    name: [Full resource name](https://google.aip.dev/122#full-resource-names)
      of the Compute Engine VM running the cluster node.
  """

  name = _messages.StringField(1)


class NodePool(_messages.Message):
  r"""Provides GKE node pool information.

  Fields:
    name: Kubernetes node pool name.
    nodes: Nodes associated with the finding.
  """

  name = _messages.StringField(1)
  nodes = _messages.MessageField('Node', 2, repeated=True)


class NotificationConfig(_messages.Message):
  r"""Cloud Security Command Center (Cloud SCC) notification configs. A
  notification config is a Cloud SCC resource that contains the configuration
  to send notifications for create/update events of findings, assets and etc.

  Fields:
    description: The description of the notification config (max of 1024
      characters).
    name: The relative resource name of this notification config. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Example: "organizations/{organization_id}/notificationConfigs/notify_
      public_bucket",
      "folders/{folder_id}/notificationConfigs/notify_public_bucket", or
      "projects/{project_id}/notificationConfigs/notify_public_bucket".
    pubsubTopic: The Pub/Sub topic to send notifications to. Its format is
      "projects/[project_id]/topics/[topic]".
    serviceAccount: Output only. The service account that needs
      "pubsub.topics.publish" permission to publish to the Pub/Sub topic.
    streamingConfig: The config for triggering streaming-based notifications.
  """

  description = _messages.StringField(1)
  name = _messages.StringField(2)
  pubsubTopic = _messages.StringField(3)
  serviceAccount = _messages.StringField(4)
  streamingConfig = _messages.MessageField('StreamingConfig', 5)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OrganizationSettings(_messages.Message):
  r"""User specified settings that are attached to the Security Command Center
  organization.

  Fields:
    assetDiscoveryConfig: The configuration used for Asset Discovery runs.
    enableAssetDiscovery: A flag that indicates if Asset Discovery should be
      enabled. If the flag is set to `true`, then discovery of assets will
      occur. If it is set to `false`, all historical assets will remain, but
      discovery of future assets will not occur.
    name: The relative resource name of the settings. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Example: "organizations/{organization_id}/organizationSettings".
  """

  assetDiscoveryConfig = _messages.MessageField('AssetDiscoveryConfig', 1)
  enableAssetDiscovery = _messages.BooleanField(2)
  name = _messages.StringField(3)


class PathNodeAssociatedFinding(_messages.Message):
  r"""A finding that is associated with this node in the attack path.

  Fields:
    canonicalFinding: Canonical name of the associated findings. Example:
      organizations/123/sources/456/findings/789
    findingCategory: The additional taxonomy group within findings from a
      given source.
    name: Full resource name of the finding.
  """

  canonicalFinding = _messages.StringField(1)
  findingCategory = _messages.StringField(2)
  name = _messages.StringField(3)


class Pod(_messages.Message):
  r"""A Kubernetes Pod.

  Fields:
    containers: Pod containers associated with this finding, if any.
    labels: Pod labels. For Kubernetes containers, these are applied to the
      container.
    name: Kubernetes Pod name.
    ns: Kubernetes Pod namespace.
  """

  containers = _messages.MessageField('Container', 1, repeated=True)
  labels = _messages.MessageField('Label', 2, repeated=True)
  name = _messages.StringField(3)
  ns = _messages.StringField(4)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Position(_messages.Message):
  r"""A position in the uploaded text version of a module.

  Fields:
    columnNumber: A integer attribute.
    lineNumber: A integer attribute.
  """

  columnNumber = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  lineNumber = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class Process(_messages.Message):
  r"""Represents an operating system process.

  Fields:
    args: Process arguments as JSON encoded strings.
    argumentsTruncated: True if `args` is incomplete.
    binary: File information for the process executable.
    envVariables: Process environment variables.
    envVariablesTruncated: True if `env_variables` is incomplete.
    libraries: File information for libraries loaded by the process.
    name: The process name, as displayed in utilities like `top` and `ps`.
      This name can be accessed through `/proc/[pid]/comm` and changed with
      `prctl(PR_SET_NAME)`.
    parentPid: The parent process ID.
    pid: The process ID.
    script: When the process represents the invocation of a script, `binary`
      provides information about the interpreter, while `script` provides
      information about the script file provided to the interpreter.
  """

  args = _messages.StringField(1, repeated=True)
  argumentsTruncated = _messages.BooleanField(2)
  binary = _messages.MessageField('File', 3)
  envVariables = _messages.MessageField('EnvironmentVariable', 4, repeated=True)
  envVariablesTruncated = _messages.BooleanField(5)
  libraries = _messages.MessageField('File', 6, repeated=True)
  name = _messages.StringField(7)
  parentPid = _messages.IntegerField(8)
  pid = _messages.IntegerField(9)
  script = _messages.MessageField('File', 10)


class ProcessSignature(_messages.Message):
  r"""Indicates what signature matched this process.

  Fields:
    memoryHashSignature: Signature indicating that a binary family was
      matched.
    yaraRuleSignature: Signature indicating that a YARA rule was matched.
  """

  memoryHashSignature = _messages.MessageField('MemoryHashSignature', 1)
  yaraRuleSignature = _messages.MessageField('YaraRuleSignature', 2)


class Reference(_messages.Message):
  r"""Additional Links

  Fields:
    source: Source of the reference e.g. NVD
    uri: Uri for the mentioned source e.g. https://cve.mitre.org/cgi-
      bin/cvename.cgi?name=CVE-2021-34527.
  """

  source = _messages.StringField(1)
  uri = _messages.StringField(2)


class Resource(_messages.Message):
  r"""Information related to the Google Cloud resource that is associated with
  this finding.

  Fields:
    displayName: The human readable name of the resource.
    folders: Contains a Folder message for each folder in the assets ancestry.
      The first folder is the deepest nested folder, and the last folder is
      the folder directly under the Organization.
    name: The full resource name of the resource. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    parentDisplayName: The human readable name of resource's parent.
    parentName: The full resource name of resource's parent.
    projectDisplayName: The project ID that the resource belongs to.
    projectName: The full resource name of project that the resource belongs
      to.
    type: The full resource type of the resource.
  """

  displayName = _messages.StringField(1)
  folders = _messages.MessageField('Folder', 2, repeated=True)
  name = _messages.StringField(3)
  parentDisplayName = _messages.StringField(4)
  parentName = _messages.StringField(5)
  projectDisplayName = _messages.StringField(6)
  projectName = _messages.StringField(7)
  type = _messages.StringField(8)


class ResourceValueConfigMetadata(_messages.Message):
  r"""Metadata about a ResourceValueConfig. For example, id and name.

  Fields:
    name: Resource value config name
  """

  name = _messages.StringField(1)


class Role(_messages.Message):
  r"""Kubernetes Role or ClusterRole.

  Enums:
    KindValueValuesEnum: Role type.

  Fields:
    kind: Role type.
    name: Role name.
    ns: Role namespace.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""Role type.

    Values:
      KIND_UNSPECIFIED: Role type is not specified.
      ROLE: Kubernetes Role.
      CLUSTER_ROLE: Kubernetes ClusterRole.
    """
    KIND_UNSPECIFIED = 0
    ROLE = 1
    CLUSTER_ROLE = 2

  kind = _messages.EnumField('KindValueValuesEnum', 1)
  name = _messages.StringField(2)
  ns = _messages.StringField(3)


class RunAssetDiscoveryRequest(_messages.Message):
  r"""Request message for running asset discovery for an organization."""


class SecurityCenterProperties(_messages.Message):
  r"""Security Command Center managed properties. These properties are managed
  by Security Command Center and cannot be modified by the user.

  Fields:
    folders: Contains a Folder message for each folder in the assets ancestry.
      The first folder is the deepest nested folder, and the last folder is
      the folder directly under the Organization.
    resourceDisplayName: The user defined display name for this resource.
    resourceName: The full resource name of the Google Cloud resource this
      asset represents. This field is immutable after create time. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    resourceOwners: Owners of the Google Cloud resource.
    resourceParent: The full resource name of the immediate parent of the
      resource. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    resourceParentDisplayName: The user defined display name for the parent of
      this resource.
    resourceProject: The full resource name of the project the resource
      belongs to. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    resourceProjectDisplayName: The user defined display name for the project
      of this resource.
    resourceType: The type of the Google Cloud resource. Examples include:
      APPLICATION, PROJECT, and ORGANIZATION. This is a case insensitive field
      defined by Security Command Center and/or the producer of the resource
      and is immutable after create time.
  """

  folders = _messages.MessageField('Folder', 1, repeated=True)
  resourceDisplayName = _messages.StringField(2)
  resourceName = _messages.StringField(3)
  resourceOwners = _messages.StringField(4, repeated=True)
  resourceParent = _messages.StringField(5)
  resourceParentDisplayName = _messages.StringField(6)
  resourceProject = _messages.StringField(7)
  resourceProjectDisplayName = _messages.StringField(8)
  resourceType = _messages.StringField(9)


class SecurityMarks(_messages.Message):
  r"""User specified security marks that are attached to the parent Security
  Command Center resource. Security marks are scoped within a Security Command
  Center organization -- they can be modified and viewed by all users who have
  proper permissions on the organization.

  Messages:
    MarksValue: Mutable user specified security marks belonging to the parent
      resource. Constraints are as follows: * Keys and values are treated as
      case insensitive * Keys must be between 1 - 256 characters (inclusive) *
      Keys must be letters, numbers, underscores, or dashes * Values have
      leading and trailing whitespace trimmed, remaining characters must be
      between 1 - 4096 characters (inclusive)

  Fields:
    canonicalName: The canonical name of the marks. Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks"
      "folders/{folder_id}/assets/{asset_id}/securityMarks"
      "projects/{project_number}/assets/{asset_id}/securityMarks" "organizatio
      ns/{organization_id}/sources/{source_id}/findings/{finding_id}/securityM
      arks" "folders/{folder_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks" "projects/{project_number}/sources/{source_id}/findings/{fin
      ding_id}/securityMarks"
    marks: Mutable user specified security marks belonging to the parent
      resource. Constraints are as follows: * Keys and values are treated as
      case insensitive * Keys must be between 1 - 256 characters (inclusive) *
      Keys must be letters, numbers, underscores, or dashes * Values have
      leading and trailing whitespace trimmed, remaining characters must be
      between 1 - 4096 characters (inclusive)
    name: The relative resource name of the SecurityMarks. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks" "organ
      izations/{organization_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks".
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MarksValue(_messages.Message):
    r"""Mutable user specified security marks belonging to the parent
    resource. Constraints are as follows: * Keys and values are treated as
    case insensitive * Keys must be between 1 - 256 characters (inclusive) *
    Keys must be letters, numbers, underscores, or dashes * Values have
    leading and trailing whitespace trimmed, remaining characters must be
    between 1 - 4096 characters (inclusive)

    Messages:
      AdditionalProperty: An additional property for a MarksValue object.

    Fields:
      additionalProperties: Additional properties of type MarksValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MarksValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  canonicalName = _messages.StringField(1)
  marks = _messages.MessageField('MarksValue', 2)
  name = _messages.StringField(3)


class SecuritycenterFoldersAssetsGroupRequest(_messages.Message):
  r"""A SecuritycenterFoldersAssetsGroupRequest object.

  Fields:
    groupAssetsRequest: A GroupAssetsRequest resource to be passed as the
      request body.
    parent: Required. The name of the parent to group the assets by. Its
      format is "organizations/[organization_id]", "folders/[folder_id]", or
      "projects/[project_id]".
  """

  groupAssetsRequest = _messages.MessageField('GroupAssetsRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecuritycenterFoldersAssetsListRequest(_messages.Message):
  r"""A SecuritycenterFoldersAssetsListRequest object.

  Fields:
    compareDuration: When compare_duration is set, the ListAssetsResult's
      "state_change" attribute is updated to indicate whether the asset was
      added, removed, or remained present during the compare_duration period
      of time that precedes the read_time. This is the time between (read_time
      - compare_duration) and read_time. The state_change value is derived
      based on the presence of the asset at the two points in time.
      Intermediate state changes between the two times don't affect the
      result. For example, the results aren't affected if the asset is removed
      and re-created again. Possible "state_change" values when
      compare_duration is specified: * "ADDED": indicates that the asset was
      not present at the start of compare_duration, but present at read_time.
      * "REMOVED": indicates that the asset was present at the start of
      compare_duration, but not present at read_time. * "ACTIVE": indicates
      that the asset was present at both the start and the end of the time
      period defined by compare_duration and read_time. If compare_duration is
      not specified, then the only possible state_change is "UNUSED", which
      will be the state_change set for all assets present at read_time.
    fieldMask: A field mask to specify the ListAssetsResult fields to be
      listed in the response. An empty field mask will list all fields.
    filter: Expression that defines the filter to apply across assets. The
      expression is a list of zero or more restrictions combined via logical
      operators `AND` and `OR`. Parentheses are supported, and `OR` has higher
      precedence than `AND`. Restrictions have the form ` ` and may have a `-`
      character in front of them to indicate negation. The fields map to those
      defined in the Asset resource. Examples include: * name *
      security_center_properties.resource_name *
      resource_properties.a_property * security_marks.marks.marka The
      supported operators are: * `=` for all value types. * `>`, `<`, `>=`,
      `<=` for integer values. * `:`, meaning substring matching, for strings.
      The supported value types are: * string literals in quotes. * integer
      literals without quotes. * boolean literals `true` and `false` without
      quotes. The following are the allowed field and operator combinations: *
      name: `=` * update_time: `=`, `>`, `<`, `>=`, `<=` Usage: This should be
      milliseconds since epoch or an RFC3339 string. Examples: `update_time =
      "2019-06-10T16:07:18-07:00"` `update_time = 1560208038000` *
      create_time: `=`, `>`, `<`, `>=`, `<=` Usage: This should be
      milliseconds since epoch or an RFC3339 string. Examples: `create_time =
      "2019-06-10T16:07:18-07:00"` `create_time = 1560208038000` *
      iam_policy.policy_blob: `=`, `:` * resource_properties: `=`, `:`, `>`,
      `<`, `>=`, `<=` * security_marks.marks: `=`, `:` *
      security_center_properties.resource_name: `=`, `:` *
      security_center_properties.resource_display_name: `=`, `:` *
      security_center_properties.resource_type: `=`, `:` *
      security_center_properties.resource_parent: `=`, `:` *
      security_center_properties.resource_parent_display_name: `=`, `:` *
      security_center_properties.resource_project: `=`, `:` *
      security_center_properties.resource_project_display_name: `=`, `:` *
      security_center_properties.resource_owners: `=`, `:` For example,
      `resource_properties.size = 100` is a valid filter string. Use a partial
      match on the empty string to filter based on a property existing:
      `resource_properties.my_property : ""` Use a negated partial match on
      the empty string to filter based on a property not existing:
      `-resource_properties.my_property : ""`
    orderBy: Expression that defines what fields and order to use for sorting.
      The string value should follow SQL syntax: comma separated list of
      fields. For example: "name,resource_properties.a_property". The default
      sorting order is ascending. To specify descending order for a field, a
      suffix " desc" should be appended to the field name. For example: "name
      desc,resource_properties.a_property". Redundant space characters in the
      syntax are insignificant. "name desc,resource_properties.a_property" and
      " name desc , resource_properties.a_property " are equivalent. The
      following fields are supported: name update_time resource_properties
      security_marks.marks security_center_properties.resource_name
      security_center_properties.resource_display_name
      security_center_properties.resource_parent
      security_center_properties.resource_parent_display_name
      security_center_properties.resource_project
      security_center_properties.resource_project_display_name
      security_center_properties.resource_type
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last `ListAssetsResponse`; indicates
      that this is a continuation of a prior `ListAssets` call, and that the
      system should return the next page of data.
    parent: Required. The name of the parent resource that contains the
      assets. The value that you can specify on parent depends on the method
      in which you specify parent. You can specify one of the following
      values: "organizations/[organization_id]", "folders/[folder_id]", or
      "projects/[project_id]".
    readTime: Time used as a reference point when filtering assets. The filter
      is limited to assets existing at the supplied time and their values are
      those at that specific time. Absence of this field will default to the
      API's version of NOW.
  """

  compareDuration = _messages.StringField(1)
  fieldMask = _messages.StringField(2)
  filter = _messages.StringField(3)
  orderBy = _messages.StringField(4)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)
  parent = _messages.StringField(7, required=True)
  readTime = _messages.StringField(8)


class SecuritycenterFoldersAssetsUpdateSecurityMarksRequest(_messages.Message):
  r"""A SecuritycenterFoldersAssetsUpdateSecurityMarksRequest object.

  Fields:
    name: The relative resource name of the SecurityMarks. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks" "organ
      izations/{organization_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks".
    securityMarks: A SecurityMarks resource to be passed as the request body.
    startTime: The time at which the updated SecurityMarks take effect. If not
      set uses current server time. Updates will be applied to the
      SecurityMarks that are active immediately preceding this time. Must be
      earlier or equal to the server time.
    updateMask: The FieldMask to use when updating the security marks
      resource. The field mask must not contain duplicate fields. If empty or
      set to "marks", all marks will be replaced. Individual marks can be
      updated using "marks.".
  """

  name = _messages.StringField(1, required=True)
  securityMarks = _messages.MessageField('SecurityMarks', 2)
  startTime = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class SecuritycenterFoldersBigQueryExportsCreateRequest(_messages.Message):
  r"""A SecuritycenterFoldersBigQueryExportsCreateRequest object.

  Fields:
    bigQueryExportId: Required. Unique identifier provided by the client
      within the parent scope. It must consist of only lowercase letters,
      numbers, and hyphens, must start with a letter, must end with either a
      letter or a number, and must be 63 characters or less.
    googleCloudSecuritycenterV1BigQueryExport: A
      GoogleCloudSecuritycenterV1BigQueryExport resource to be passed as the
      request body.
    parent: Required. The name of the parent resource of the new BigQuery
      export. Its format is "organizations/[organization_id]",
      "folders/[folder_id]", or "projects/[project_id]".
  """

  bigQueryExportId = _messages.StringField(1)
  googleCloudSecuritycenterV1BigQueryExport = _messages.MessageField('GoogleCloudSecuritycenterV1BigQueryExport', 2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterFoldersBigQueryExportsDeleteRequest(_messages.Message):
  r"""A SecuritycenterFoldersBigQueryExportsDeleteRequest object.

  Fields:
    name: Required. The name of the BigQuery export to delete. Its format is
      organizations/{organization}/bigQueryExports/{export_id},
      folders/{folder}/bigQueryExports/{export_id}, or
      projects/{project}/bigQueryExports/{export_id}
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersBigQueryExportsGetRequest(_messages.Message):
  r"""A SecuritycenterFoldersBigQueryExportsGetRequest object.

  Fields:
    name: Required. Name of the BigQuery export to retrieve. Its format is
      organizations/{organization}/bigQueryExports/{export_id},
      folders/{folder}/bigQueryExports/{export_id}, or
      projects/{project}/bigQueryExports/{export_id}
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersBigQueryExportsListRequest(_messages.Message):
  r"""A SecuritycenterFoldersBigQueryExportsListRequest object.

  Fields:
    pageSize: The maximum number of configs to return. The service may return
      fewer than this value. If unspecified, at most 10 configs will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: A page token, received from a previous `ListBigQueryExports`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListBigQueryExports` must match the call
      that provided the page token.
    parent: Required. The parent, which owns the collection of BigQuery
      exports. Its format is "organizations/[organization_id]",
      "folders/[folder_id]", "projects/[project_id]".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterFoldersBigQueryExportsPatchRequest(_messages.Message):
  r"""A SecuritycenterFoldersBigQueryExportsPatchRequest object.

  Fields:
    googleCloudSecuritycenterV1BigQueryExport: A
      GoogleCloudSecuritycenterV1BigQueryExport resource to be passed as the
      request body.
    name: The relative resource name of this export. See: https://cloud.google
      .com/apis/design/resource_names#relative_resource_name. Example format:
      "organizations/{organization_id}/bigQueryExports/{export_id}" Example
      format: "folders/{folder_id}/bigQueryExports/{export_id}" Example
      format: "projects/{project_id}/bigQueryExports/{export_id}" This field
      is provided in responses, and is ignored when provided in create
      requests.
    updateMask: The list of fields to be updated. If empty all mutable fields
      will be updated.
  """

  googleCloudSecuritycenterV1BigQueryExport = _messages.MessageField('GoogleCloudSecuritycenterV1BigQueryExport', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterFoldersEventThreatDetectionSettingsCustomModulesListDescendantRequest(_messages.Message):
  r"""A SecuritycenterFoldersEventThreatDetectionSettingsCustomModulesListDesc
  endantRequest object.

  Fields:
    pageSize: The maximum number of modules to return. The service may return
      fewer than this value. If unspecified, at most 10 configs will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: A page token, received from a previous
      `ListDescendantEventThreatDetectionCustomModules` call. Provide this to
      retrieve the subsequent page. When paginating, all other parameters
      provided to `ListDescendantEventThreatDetectionCustomModules` must match
      the call that provided the page token.
    parent: Required. Name of the parent to list custom modules under. Its
      format is: *
      "organizations/{organization}/eventThreatDetectionSettings".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterFoldersEventThreatDetectionSettingsEffectiveCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycenterFoldersEventThreatDetectionSettingsEffectiveCustomModule
  sGetRequest object.

  Fields:
    name: Required. The resource name of the effective Event Threat Detection
      custom module. Its format is: * "organizations/{organization}/eventThrea
      tDetectionSettings/effectiveCustomModules/{module}".
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersEventThreatDetectionSettingsEffectiveCustomModulesListRequest(_messages.Message):
  r"""A SecuritycenterFoldersEventThreatDetectionSettingsEffectiveCustomModule
  sListRequest object.

  Fields:
    pageSize: The maximum number of modules to return. The service may return
      fewer than this value. If unspecified, at most 10 configs will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: A page token, received from a previous
      `ListEffectiveEventThreatDetectionCustomModules` call. Provide this to
      retrieve the subsequent page. When paginating, all other parameters
      provided to `ListEffectiveEventThreatDetectionCustomModules` must match
      the call that provided the page token.
    parent: Required. Name of the parent to list custom modules for. Its
      format is: *
      "organizations/{organization}/eventThreatDetectionSettings".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterFoldersFindingsBulkMuteRequest(_messages.Message):
  r"""A SecuritycenterFoldersFindingsBulkMuteRequest object.

  Fields:
    bulkMuteFindingsRequest: A BulkMuteFindingsRequest resource to be passed
      as the request body.
    parent: Required. The parent, at which bulk action needs to be applied.
      Its format is "organizations/[organization_id]", "folders/[folder_id]",
      "projects/[project_id]".
  """

  bulkMuteFindingsRequest = _messages.MessageField('BulkMuteFindingsRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecuritycenterFoldersMuteConfigsCreateRequest(_messages.Message):
  r"""A SecuritycenterFoldersMuteConfigsCreateRequest object.

  Fields:
    googleCloudSecuritycenterV1MuteConfig: A
      GoogleCloudSecuritycenterV1MuteConfig resource to be passed as the
      request body.
    muteConfigId: Required. Unique identifier provided by the client within
      the parent scope. It must consist of only lowercase letters, numbers,
      and hyphens, must start with a letter, must end with either a letter or
      a number, and must be 63 characters or less.
    parent: Required. Resource name of the new mute configs's parent. Its
      format is "organizations/[organization_id]", "folders/[folder_id]", or
      "projects/[project_id]".
  """

  googleCloudSecuritycenterV1MuteConfig = _messages.MessageField('GoogleCloudSecuritycenterV1MuteConfig', 1)
  muteConfigId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterFoldersMuteConfigsDeleteRequest(_messages.Message):
  r"""A SecuritycenterFoldersMuteConfigsDeleteRequest object.

  Fields:
    name: Required. Name of the mute config to delete. Its format is
      organizations/{organization}/muteConfigs/{config_id},
      folders/{folder}/muteConfigs/{config_id}, or
      projects/{project}/muteConfigs/{config_id}
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersMuteConfigsGetRequest(_messages.Message):
  r"""A SecuritycenterFoldersMuteConfigsGetRequest object.

  Fields:
    name: Required. Name of the mute config to retrieve. Its format is
      organizations/{organization}/muteConfigs/{config_id},
      folders/{folder}/muteConfigs/{config_id}, or
      projects/{project}/muteConfigs/{config_id}
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersMuteConfigsListRequest(_messages.Message):
  r"""A SecuritycenterFoldersMuteConfigsListRequest object.

  Fields:
    pageSize: The maximum number of configs to return. The service may return
      fewer than this value. If unspecified, at most 10 configs will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: A page token, received from a previous `ListMuteConfigs` call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to `ListMuteConfigs` must match the call that
      provided the page token.
    parent: Required. The parent, which owns the collection of mute configs.
      Its format is "organizations/[organization_id]", "folders/[folder_id]",
      "projects/[project_id]".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterFoldersMuteConfigsPatchRequest(_messages.Message):
  r"""A SecuritycenterFoldersMuteConfigsPatchRequest object.

  Fields:
    googleCloudSecuritycenterV1MuteConfig: A
      GoogleCloudSecuritycenterV1MuteConfig resource to be passed as the
      request body.
    name: This field will be ignored if provided on config creation. Format
      "organizations/{organization}/muteConfigs/{mute_config}"
      "folders/{folder}/muteConfigs/{mute_config}"
      "projects/{project}/muteConfigs/{mute_config}"
    updateMask: The list of fields to be updated. If empty all mutable fields
      will be updated.
  """

  googleCloudSecuritycenterV1MuteConfig = _messages.MessageField('GoogleCloudSecuritycenterV1MuteConfig', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterFoldersNotificationConfigsCreateRequest(_messages.Message):
  r"""A SecuritycenterFoldersNotificationConfigsCreateRequest object.

  Fields:
    configId: Required. Unique identifier provided by the client within the
      parent scope. It must be between 1 and 128 characters and contain
      alphanumeric characters, underscores, or hyphens only.
    notificationConfig: A NotificationConfig resource to be passed as the
      request body.
    parent: Required. Resource name of the new notification config's parent.
      Its format is "organizations/[organization_id]", "folders/[folder_id]",
      or "projects/[project_id]".
  """

  configId = _messages.StringField(1)
  notificationConfig = _messages.MessageField('NotificationConfig', 2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterFoldersNotificationConfigsDeleteRequest(_messages.Message):
  r"""A SecuritycenterFoldersNotificationConfigsDeleteRequest object.

  Fields:
    name: Required. Name of the notification config to delete. Its format is
      "organizations/[organization_id]/notificationConfigs/[config_id]",
      "folders/[folder_id]/notificationConfigs/[config_id]", or
      "projects/[project_id]/notificationConfigs/[config_id]".
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersNotificationConfigsGetRequest(_messages.Message):
  r"""A SecuritycenterFoldersNotificationConfigsGetRequest object.

  Fields:
    name: Required. Name of the notification config to get. Its format is
      "organizations/[organization_id]/notificationConfigs/[config_id]",
      "folders/[folder_id]/notificationConfigs/[config_id]", or
      "projects/[project_id]/notificationConfigs/[config_id]".
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersNotificationConfigsListRequest(_messages.Message):
  r"""A SecuritycenterFoldersNotificationConfigsListRequest object.

  Fields:
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last
      `ListNotificationConfigsResponse`; indicates that this is a continuation
      of a prior `ListNotificationConfigs` call, and that the system should
      return the next page of data.
    parent: Required. The name of the parent in which to list the notification
      configurations. Its format is "organizations/[organization_id]",
      "folders/[folder_id]", or "projects/[project_id]".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterFoldersNotificationConfigsPatchRequest(_messages.Message):
  r"""A SecuritycenterFoldersNotificationConfigsPatchRequest object.

  Fields:
    name: The relative resource name of this notification config. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Example: "organizations/{organization_id}/notificationConfigs/notify_
      public_bucket",
      "folders/{folder_id}/notificationConfigs/notify_public_bucket", or
      "projects/{project_id}/notificationConfigs/notify_public_bucket".
    notificationConfig: A NotificationConfig resource to be passed as the
      request body.
    updateMask: The FieldMask to use when updating the notification config. If
      empty all mutable fields will be updated.
  """

  name = _messages.StringField(1, required=True)
  notificationConfig = _messages.MessageField('NotificationConfig', 2)
  updateMask = _messages.StringField(3)


class SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesCreateRequest(_messages.Message):
  r"""A SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesCreat
  eRequest object.

  Fields:
    googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule: A
      GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule resource
      to be passed as the request body.
    parent: Required. Resource name of the new custom module's parent. Its
      format is
      "organizations/{organization}/securityHealthAnalyticsSettings",
      "folders/{folder}/securityHealthAnalyticsSettings", or
      "projects/{project}/securityHealthAnalyticsSettings"
  """

  googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule = _messages.MessageField('GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule', 1)
  parent = _messages.StringField(2, required=True)


class SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesDeleteRequest(_messages.Message):
  r"""A SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesDelet
  eRequest object.

  Fields:
    name: Required. Name of the custom module to delete. Its format is "organi
      zations/{organization}/securityHealthAnalyticsSettings/customModules/{cu
      stomModule}", "folders/{folder}/securityHealthAnalyticsSettings/customMo
      dules/{customModule}", or "projects/{project}/securityHealthAnalyticsSet
      tings/customModules/{customModule}"
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesGetRequest(_messages.Message):
  r"""A
  SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesGetRequest
  object.

  Fields:
    name: Required. Name of the custom module to get. Its format is "organizat
      ions/{organization}/securityHealthAnalyticsSettings/customModules/{custo
      mModule}", "folders/{folder}/securityHealthAnalyticsSettings/customModul
      es/{customModule}", or "projects/{project}/securityHealthAnalyticsSettin
      gs/customModules/{customModule}"
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesListDescendantRequest(_messages.Message):
  r"""A SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesListD
  escendantRequest object.

  Fields:
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last call indicating a continuation
    parent: Required. Name of parent to list descendant custom modules. Its
      format is
      "organizations/{organization}/securityHealthAnalyticsSettings",
      "folders/{folder}/securityHealthAnalyticsSettings", or
      "projects/{project}/securityHealthAnalyticsSettings"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesListRequest(_messages.Message):
  r"""A
  SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesListRequest
  object.

  Fields:
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last call indicating a continuation
    parent: Required. Name of parent to list custom modules. Its format is
      "organizations/{organization}/securityHealthAnalyticsSettings",
      "folders/{folder}/securityHealthAnalyticsSettings", or
      "projects/{project}/securityHealthAnalyticsSettings"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesPatchRequest(_messages.Message):
  r"""A SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesPatch
  Request object.

  Fields:
    googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule: A
      GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule resource
      to be passed as the request body.
    name: Immutable. The resource name of the custom module. Its format is "or
      ganizations/{organization}/securityHealthAnalyticsSettings/customModules
      /{customModule}", or "folders/{folder}/securityHealthAnalyticsSettings/c
      ustomModules/{customModule}", or "projects/{project}/securityHealthAnaly
      ticsSettings/customModules/{customModule}" The id {customModule} is
      server-generated and is not user settable. It will be a numeric id
      containing 1-20 digits.
    updateMask: The list of fields to update.
  """

  googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule = _messages.MessageField('GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesSimulateRequest(_messages.Message):
  r"""A SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesSimul
  ateRequest object.

  Fields:
    parent: Required. The relative resource name of the organization, project,
      or folder. See: https://cloud.google.com/apis/design/resource_names#rela
      tive_resource_name An example is: "organizations/{organization_id}".
    simulateSecurityHealthAnalyticsCustomModuleRequest: A
      SimulateSecurityHealthAnalyticsCustomModuleRequest resource to be passed
      as the request body.
  """

  parent = _messages.StringField(1, required=True)
  simulateSecurityHealthAnalyticsCustomModuleRequest = _messages.MessageField('SimulateSecurityHealthAnalyticsCustomModuleRequest', 2)


class SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesTestRequest(_messages.Message):
  r"""A
  SecuritycenterFoldersSecurityHealthAnalyticsSettingsCustomModulesTestRequest
  object.

  Fields:
    name: Required. Name of the custom module to test. Its format is "organiza
      tions/[organization_id]/securityHealthAnalyticsSettings/customModules/[m
      odule_id]". If the custom_module field is empty, it is assumed that the
      custom module already exists; otherwise the specified custom_module will
      be used.
    testSecurityHealthAnalyticsCustomModuleRequest: A
      TestSecurityHealthAnalyticsCustomModuleRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  testSecurityHealthAnalyticsCustomModuleRequest = _messages.MessageField('TestSecurityHealthAnalyticsCustomModuleRequest', 2)


class SecuritycenterFoldersSecurityHealthAnalyticsSettingsEffectiveCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycenterFoldersSecurityHealthAnalyticsSettingsEffectiveCustomMod
  ulesGetRequest object.

  Fields:
    name: Required. Name of the effective custom module to get. Its format is
      "organizations/{organization}/securityHealthAnalyticsSettings/effectiveC
      ustomModules/{customModule}", "folders/{folder}/securityHealthAnalyticsS
      ettings/effectiveCustomModules/{customModule}", or "projects/{project}/s
      ecurityHealthAnalyticsSettings/effectiveCustomModules/{customModule}"
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersSecurityHealthAnalyticsSettingsEffectiveCustomModulesListRequest(_messages.Message):
  r"""A SecuritycenterFoldersSecurityHealthAnalyticsSettingsEffectiveCustomMod
  ulesListRequest object.

  Fields:
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last call indicating a continuation
    parent: Required. Name of parent to list effective custom modules. Its
      format is
      "organizations/{organization}/securityHealthAnalyticsSettings",
      "folders/{folder}/securityHealthAnalyticsSettings", or
      "projects/{project}/securityHealthAnalyticsSettings"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterFoldersSourcesFindingsExternalSystemsPatchRequest(_messages.Message):
  r"""A SecuritycenterFoldersSourcesFindingsExternalSystemsPatchRequest
  object.

  Fields:
    googleCloudSecuritycenterV1ExternalSystem: A
      GoogleCloudSecuritycenterV1ExternalSystem resource to be passed as the
      request body.
    name: Full resource name of the external system, for example:
      "organizations/1234/sources/5678/findings/123456/externalSystems/jira",
      "folders/1234/sources/5678/findings/123456/externalSystems/jira",
      "projects/1234/sources/5678/findings/123456/externalSystems/jira"
    updateMask: The FieldMask to use when updating the external system
      resource. If empty all mutable fields will be updated.
  """

  googleCloudSecuritycenterV1ExternalSystem = _messages.MessageField('GoogleCloudSecuritycenterV1ExternalSystem', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterFoldersSourcesFindingsGroupRequest(_messages.Message):
  r"""A SecuritycenterFoldersSourcesFindingsGroupRequest object.

  Fields:
    groupFindingsRequest: A GroupFindingsRequest resource to be passed as the
      request body.
    parent: Required. Name of the source to groupBy. Its format is
      "organizations/[organization_id]/sources/[source_id]",
      folders/[folder_id]/sources/[source_id], or
      projects/[project_id]/sources/[source_id]. To groupBy across all sources
      provide a source_id of `-`. For example:
      organizations/{organization_id}/sources/-,
      folders/{folder_id}/sources/-, or projects/{project_id}/sources/-
  """

  groupFindingsRequest = _messages.MessageField('GroupFindingsRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecuritycenterFoldersSourcesFindingsListRequest(_messages.Message):
  r"""A SecuritycenterFoldersSourcesFindingsListRequest object.

  Fields:
    compareDuration: When compare_duration is set, the ListFindingsResult's
      "state_change" attribute is updated to indicate whether the finding had
      its state changed, the finding's state remained unchanged, or if the
      finding was added in any state during the compare_duration period of
      time that precedes the read_time. This is the time between (read_time -
      compare_duration) and read_time. The state_change value is derived based
      on the presence and state of the finding at the two points in time.
      Intermediate state changes between the two times don't affect the
      result. For example, the results aren't affected if the finding is made
      inactive and then active again. Possible "state_change" values when
      compare_duration is specified: * "CHANGED": indicates that the finding
      was present and matched the given filter at the start of
      compare_duration, but changed its state at read_time. * "UNCHANGED":
      indicates that the finding was present and matched the given filter at
      the start of compare_duration and did not change state at read_time. *
      "ADDED": indicates that the finding did not match the given filter or
      was not present at the start of compare_duration, but was present at
      read_time. * "REMOVED": indicates that the finding was present and
      matched the filter at the start of compare_duration, but did not match
      the filter at read_time. If compare_duration is not specified, then the
      only possible state_change is "UNUSED", which will be the state_change
      set for all findings present at read_time.
    fieldMask: A field mask to specify the Finding fields to be listed in the
      response. An empty field mask will list all fields.
    filter: Expression that defines the filter to apply across findings. The
      expression is a list of one or more restrictions combined via logical
      operators `AND` and `OR`. Parentheses are supported, and `OR` has higher
      precedence than `AND`. Restrictions have the form ` ` and may have a `-`
      character in front of them to indicate negation. Examples include: *
      name * source_properties.a_property * security_marks.marks.marka The
      supported operators are: * `=` for all value types. * `>`, `<`, `>=`,
      `<=` for integer values. * `:`, meaning substring matching, for strings.
      The supported value types are: * string literals in quotes. * integer
      literals without quotes. * boolean literals `true` and `false` without
      quotes. The following field and operator combinations are supported: *
      name: `=` * parent: `=`, `:` * resource_name: `=`, `:` * state: `=`, `:`
      * category: `=`, `:` * external_uri: `=`, `:` * event_time: `=`, `>`,
      `<`, `>=`, `<=` Usage: This should be milliseconds since epoch or an
      RFC3339 string. Examples: `event_time = "2019-06-10T16:07:18-07:00"`
      `event_time = 1560208038000` * severity: `=`, `:` * workflow_state: `=`,
      `:` * security_marks.marks: `=`, `:` * source_properties: `=`, `:`, `>`,
      `<`, `>=`, `<=` For example, `source_properties.size = 100` is a valid
      filter string. Use a partial match on the empty string to filter based
      on a property existing: `source_properties.my_property : ""` Use a
      negated partial match on the empty string to filter based on a property
      not existing: `-source_properties.my_property : ""` * resource: *
      resource.name: `=`, `:` * resource.parent_name: `=`, `:` *
      resource.parent_display_name: `=`, `:` * resource.project_name: `=`, `:`
      * resource.project_display_name: `=`, `:` * resource.type: `=`, `:` *
      resource.folders.resource_folder: `=`, `:` * resource.display_name: `=`,
      `:`
    orderBy: Expression that defines what fields and order to use for sorting.
      The string value should follow SQL syntax: comma separated list of
      fields. For example: "name,resource_properties.a_property". The default
      sorting order is ascending. To specify descending order for a field, a
      suffix " desc" should be appended to the field name. For example: "name
      desc,source_properties.a_property". Redundant space characters in the
      syntax are insignificant. "name desc,source_properties.a_property" and "
      name desc , source_properties.a_property " are equivalent. The following
      fields are supported: name parent state category resource_name
      event_time source_properties security_marks.marks
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last `ListFindingsResponse`;
      indicates that this is a continuation of a prior `ListFindings` call,
      and that the system should return the next page of data.
    parent: Required. Name of the source the findings belong to. Its format is
      "organizations/[organization_id]/sources/[source_id],
      folders/[folder_id]/sources/[source_id], or
      projects/[project_id]/sources/[source_id]". To list across all sources
      provide a source_id of `-`. For example:
      organizations/{organization_id}/sources/-, folders/{folder_id}/sources/-
      or projects/{projects_id}/sources/-
    readTime: Time used as a reference point when filtering findings. The
      filter is limited to findings existing at the supplied time and their
      values are those at that specific time. Absence of this field will
      default to the API's version of NOW.
  """

  compareDuration = _messages.StringField(1)
  fieldMask = _messages.StringField(2)
  filter = _messages.StringField(3)
  orderBy = _messages.StringField(4)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)
  parent = _messages.StringField(7, required=True)
  readTime = _messages.StringField(8)


class SecuritycenterFoldersSourcesFindingsPatchRequest(_messages.Message):
  r"""A SecuritycenterFoldersSourcesFindingsPatchRequest object.

  Fields:
    finding: A Finding resource to be passed as the request body.
    name: The [relative resource name](https://cloud.google.com/apis/design/re
      source_names#relative_resource_name) of the finding. Example: "organizat
      ions/{organization_id}/sources/{source_id}/findings/{finding_id}",
      "folders/{folder_id}/sources/{source_id}/findings/{finding_id}",
      "projects/{project_id}/sources/{source_id}/findings/{finding_id}".
    updateMask: The FieldMask to use when updating the finding resource. This
      field should not be specified when creating a finding. When updating a
      finding, an empty mask is treated as updating all mutable fields and
      replacing source_properties. Individual source_properties can be
      added/updated by using "source_properties." in the field mask.
  """

  finding = _messages.MessageField('Finding', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterFoldersSourcesFindingsSetMuteRequest(_messages.Message):
  r"""A SecuritycenterFoldersSourcesFindingsSetMuteRequest object.

  Fields:
    name: Required. The [relative resource name](https://cloud.google.com/apis
      /design/resource_names#relative_resource_name) of the finding. Example:
      "organizations/{organization_id}/sources/{source_id}/findings/{finding_i
      d}", "folders/{folder_id}/sources/{source_id}/findings/{finding_id}",
      "projects/{project_id}/sources/{source_id}/findings/{finding_id}".
    setMuteRequest: A SetMuteRequest resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  setMuteRequest = _messages.MessageField('SetMuteRequest', 2)


class SecuritycenterFoldersSourcesFindingsSetStateRequest(_messages.Message):
  r"""A SecuritycenterFoldersSourcesFindingsSetStateRequest object.

  Fields:
    name: Required. The [relative resource name](https://cloud.google.com/apis
      /design/resource_names#relative_resource_name) of the finding. Example:
      "organizations/{organization_id}/sources/{source_id}/findings/{finding_i
      d}", "folders/{folder_id}/sources/{source_id}/findings/{finding_id}",
      "projects/{project_id}/sources/{source_id}/findings/{finding_id}".
    setFindingStateRequest: A SetFindingStateRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  setFindingStateRequest = _messages.MessageField('SetFindingStateRequest', 2)


class SecuritycenterFoldersSourcesFindingsUpdateSecurityMarksRequest(_messages.Message):
  r"""A SecuritycenterFoldersSourcesFindingsUpdateSecurityMarksRequest object.

  Fields:
    name: The relative resource name of the SecurityMarks. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks" "organ
      izations/{organization_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks".
    securityMarks: A SecurityMarks resource to be passed as the request body.
    startTime: The time at which the updated SecurityMarks take effect. If not
      set uses current server time. Updates will be applied to the
      SecurityMarks that are active immediately preceding this time. Must be
      earlier or equal to the server time.
    updateMask: The FieldMask to use when updating the security marks
      resource. The field mask must not contain duplicate fields. If empty or
      set to "marks", all marks will be replaced. Individual marks can be
      updated using "marks.".
  """

  name = _messages.StringField(1, required=True)
  securityMarks = _messages.MessageField('SecurityMarks', 2)
  startTime = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class SecuritycenterFoldersSourcesListRequest(_messages.Message):
  r"""A SecuritycenterFoldersSourcesListRequest object.

  Fields:
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last `ListSourcesResponse`; indicates
      that this is a continuation of a prior `ListSources` call, and that the
      system should return the next page of data.
    parent: Required. Resource name of the parent of sources to list. Its
      format should be "organizations/[organization_id]",
      "folders/[folder_id]", or "projects/[project_id]".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterOrganizationsAssetsGroupRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsAssetsGroupRequest object.

  Fields:
    groupAssetsRequest: A GroupAssetsRequest resource to be passed as the
      request body.
    parent: Required. The name of the parent to group the assets by. Its
      format is "organizations/[organization_id]", "folders/[folder_id]", or
      "projects/[project_id]".
  """

  groupAssetsRequest = _messages.MessageField('GroupAssetsRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecuritycenterOrganizationsAssetsListRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsAssetsListRequest object.

  Fields:
    compareDuration: When compare_duration is set, the ListAssetsResult's
      "state_change" attribute is updated to indicate whether the asset was
      added, removed, or remained present during the compare_duration period
      of time that precedes the read_time. This is the time between (read_time
      - compare_duration) and read_time. The state_change value is derived
      based on the presence of the asset at the two points in time.
      Intermediate state changes between the two times don't affect the
      result. For example, the results aren't affected if the asset is removed
      and re-created again. Possible "state_change" values when
      compare_duration is specified: * "ADDED": indicates that the asset was
      not present at the start of compare_duration, but present at read_time.
      * "REMOVED": indicates that the asset was present at the start of
      compare_duration, but not present at read_time. * "ACTIVE": indicates
      that the asset was present at both the start and the end of the time
      period defined by compare_duration and read_time. If compare_duration is
      not specified, then the only possible state_change is "UNUSED", which
      will be the state_change set for all assets present at read_time.
    fieldMask: A field mask to specify the ListAssetsResult fields to be
      listed in the response. An empty field mask will list all fields.
    filter: Expression that defines the filter to apply across assets. The
      expression is a list of zero or more restrictions combined via logical
      operators `AND` and `OR`. Parentheses are supported, and `OR` has higher
      precedence than `AND`. Restrictions have the form ` ` and may have a `-`
      character in front of them to indicate negation. The fields map to those
      defined in the Asset resource. Examples include: * name *
      security_center_properties.resource_name *
      resource_properties.a_property * security_marks.marks.marka The
      supported operators are: * `=` for all value types. * `>`, `<`, `>=`,
      `<=` for integer values. * `:`, meaning substring matching, for strings.
      The supported value types are: * string literals in quotes. * integer
      literals without quotes. * boolean literals `true` and `false` without
      quotes. The following are the allowed field and operator combinations: *
      name: `=` * update_time: `=`, `>`, `<`, `>=`, `<=` Usage: This should be
      milliseconds since epoch or an RFC3339 string. Examples: `update_time =
      "2019-06-10T16:07:18-07:00"` `update_time = 1560208038000` *
      create_time: `=`, `>`, `<`, `>=`, `<=` Usage: This should be
      milliseconds since epoch or an RFC3339 string. Examples: `create_time =
      "2019-06-10T16:07:18-07:00"` `create_time = 1560208038000` *
      iam_policy.policy_blob: `=`, `:` * resource_properties: `=`, `:`, `>`,
      `<`, `>=`, `<=` * security_marks.marks: `=`, `:` *
      security_center_properties.resource_name: `=`, `:` *
      security_center_properties.resource_display_name: `=`, `:` *
      security_center_properties.resource_type: `=`, `:` *
      security_center_properties.resource_parent: `=`, `:` *
      security_center_properties.resource_parent_display_name: `=`, `:` *
      security_center_properties.resource_project: `=`, `:` *
      security_center_properties.resource_project_display_name: `=`, `:` *
      security_center_properties.resource_owners: `=`, `:` For example,
      `resource_properties.size = 100` is a valid filter string. Use a partial
      match on the empty string to filter based on a property existing:
      `resource_properties.my_property : ""` Use a negated partial match on
      the empty string to filter based on a property not existing:
      `-resource_properties.my_property : ""`
    orderBy: Expression that defines what fields and order to use for sorting.
      The string value should follow SQL syntax: comma separated list of
      fields. For example: "name,resource_properties.a_property". The default
      sorting order is ascending. To specify descending order for a field, a
      suffix " desc" should be appended to the field name. For example: "name
      desc,resource_properties.a_property". Redundant space characters in the
      syntax are insignificant. "name desc,resource_properties.a_property" and
      " name desc , resource_properties.a_property " are equivalent. The
      following fields are supported: name update_time resource_properties
      security_marks.marks security_center_properties.resource_name
      security_center_properties.resource_display_name
      security_center_properties.resource_parent
      security_center_properties.resource_parent_display_name
      security_center_properties.resource_project
      security_center_properties.resource_project_display_name
      security_center_properties.resource_type
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last `ListAssetsResponse`; indicates
      that this is a continuation of a prior `ListAssets` call, and that the
      system should return the next page of data.
    parent: Required. The name of the parent resource that contains the
      assets. The value that you can specify on parent depends on the method
      in which you specify parent. You can specify one of the following
      values: "organizations/[organization_id]", "folders/[folder_id]", or
      "projects/[project_id]".
    readTime: Time used as a reference point when filtering assets. The filter
      is limited to assets existing at the supplied time and their values are
      those at that specific time. Absence of this field will default to the
      API's version of NOW.
  """

  compareDuration = _messages.StringField(1)
  fieldMask = _messages.StringField(2)
  filter = _messages.StringField(3)
  orderBy = _messages.StringField(4)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)
  parent = _messages.StringField(7, required=True)
  readTime = _messages.StringField(8)


class SecuritycenterOrganizationsAssetsRunDiscoveryRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsAssetsRunDiscoveryRequest object.

  Fields:
    parent: Required. Name of the organization to run asset discovery for. Its
      format is "organizations/[organization_id]".
    runAssetDiscoveryRequest: A RunAssetDiscoveryRequest resource to be passed
      as the request body.
  """

  parent = _messages.StringField(1, required=True)
  runAssetDiscoveryRequest = _messages.MessageField('RunAssetDiscoveryRequest', 2)


class SecuritycenterOrganizationsAssetsUpdateSecurityMarksRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsAssetsUpdateSecurityMarksRequest object.

  Fields:
    name: The relative resource name of the SecurityMarks. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks" "organ
      izations/{organization_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks".
    securityMarks: A SecurityMarks resource to be passed as the request body.
    startTime: The time at which the updated SecurityMarks take effect. If not
      set uses current server time. Updates will be applied to the
      SecurityMarks that are active immediately preceding this time. Must be
      earlier or equal to the server time.
    updateMask: The FieldMask to use when updating the security marks
      resource. The field mask must not contain duplicate fields. If empty or
      set to "marks", all marks will be replaced. Individual marks can be
      updated using "marks.".
  """

  name = _messages.StringField(1, required=True)
  securityMarks = _messages.MessageField('SecurityMarks', 2)
  startTime = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class SecuritycenterOrganizationsBigQueryExportsCreateRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsBigQueryExportsCreateRequest object.

  Fields:
    bigQueryExportId: Required. Unique identifier provided by the client
      within the parent scope. It must consist of only lowercase letters,
      numbers, and hyphens, must start with a letter, must end with either a
      letter or a number, and must be 63 characters or less.
    googleCloudSecuritycenterV1BigQueryExport: A
      GoogleCloudSecuritycenterV1BigQueryExport resource to be passed as the
      request body.
    parent: Required. The name of the parent resource of the new BigQuery
      export. Its format is "organizations/[organization_id]",
      "folders/[folder_id]", or "projects/[project_id]".
  """

  bigQueryExportId = _messages.StringField(1)
  googleCloudSecuritycenterV1BigQueryExport = _messages.MessageField('GoogleCloudSecuritycenterV1BigQueryExport', 2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterOrganizationsBigQueryExportsDeleteRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsBigQueryExportsDeleteRequest object.

  Fields:
    name: Required. The name of the BigQuery export to delete. Its format is
      organizations/{organization}/bigQueryExports/{export_id},
      folders/{folder}/bigQueryExports/{export_id}, or
      projects/{project}/bigQueryExports/{export_id}
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsBigQueryExportsGetRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsBigQueryExportsGetRequest object.

  Fields:
    name: Required. Name of the BigQuery export to retrieve. Its format is
      organizations/{organization}/bigQueryExports/{export_id},
      folders/{folder}/bigQueryExports/{export_id}, or
      projects/{project}/bigQueryExports/{export_id}
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsBigQueryExportsListRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsBigQueryExportsListRequest object.

  Fields:
    pageSize: The maximum number of configs to return. The service may return
      fewer than this value. If unspecified, at most 10 configs will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: A page token, received from a previous `ListBigQueryExports`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListBigQueryExports` must match the call
      that provided the page token.
    parent: Required. The parent, which owns the collection of BigQuery
      exports. Its format is "organizations/[organization_id]",
      "folders/[folder_id]", "projects/[project_id]".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterOrganizationsBigQueryExportsPatchRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsBigQueryExportsPatchRequest object.

  Fields:
    googleCloudSecuritycenterV1BigQueryExport: A
      GoogleCloudSecuritycenterV1BigQueryExport resource to be passed as the
      request body.
    name: The relative resource name of this export. See: https://cloud.google
      .com/apis/design/resource_names#relative_resource_name. Example format:
      "organizations/{organization_id}/bigQueryExports/{export_id}" Example
      format: "folders/{folder_id}/bigQueryExports/{export_id}" Example
      format: "projects/{project_id}/bigQueryExports/{export_id}" This field
      is provided in responses, and is ignored when provided in create
      requests.
    updateMask: The list of fields to be updated. If empty all mutable fields
      will be updated.
  """

  googleCloudSecuritycenterV1BigQueryExport = _messages.MessageField('GoogleCloudSecuritycenterV1BigQueryExport', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesCreateRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesCr
  eateRequest object.

  Fields:
    eventThreatDetectionCustomModule: A EventThreatDetectionCustomModule
      resource to be passed as the request body.
    parent: Required. The new custom module's parent. Its format is: *
      "organizations/{organization}/eventThreatDetectionSettings".
  """

  eventThreatDetectionCustomModule = _messages.MessageField('EventThreatDetectionCustomModule', 1)
  parent = _messages.StringField(2, required=True)


class SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesDeleteRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesDe
  leteRequest object.

  Fields:
    name: Required. Name of the custom module to delete. Its format is: * "org
      anizations/{organization}/eventThreatDetectionSettings/customModules/{mo
      dule}".
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesGe
  tRequest object.

  Fields:
    name: Required. Name of the custom module to get. Its format is: * "organi
      zations/{organization}/eventThreatDetectionSettings/customModules/{modul
      e}".
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesListDescendantRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesLi
  stDescendantRequest object.

  Fields:
    pageSize: The maximum number of modules to return. The service may return
      fewer than this value. If unspecified, at most 10 configs will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: A page token, received from a previous
      `ListDescendantEventThreatDetectionCustomModules` call. Provide this to
      retrieve the subsequent page. When paginating, all other parameters
      provided to `ListDescendantEventThreatDetectionCustomModules` must match
      the call that provided the page token.
    parent: Required. Name of the parent to list custom modules under. Its
      format is: *
      "organizations/{organization}/eventThreatDetectionSettings".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesListRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesLi
  stRequest object.

  Fields:
    pageSize: The maximum number of modules to return. The service may return
      fewer than this value. If unspecified, at most 10 configs will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: A page token, received from a previous
      `ListEventThreatDetectionCustomModules` call. Provide this to retrieve
      the subsequent page. When paginating, all other parameters provided to
      `ListEventThreatDetectionCustomModules` must match the call that
      provided the page token.
    parent: Required. Name of the parent to list custom modules under. Its
      format is: *
      "organizations/{organization}/eventThreatDetectionSettings".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesPatchRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsEventThreatDetectionSettingsCustomModulesPa
  tchRequest object.

  Fields:
    eventThreatDetectionCustomModule: A EventThreatDetectionCustomModule
      resource to be passed as the request body.
    name: Immutable. The resource name of the Event Threat Detection custom
      module. Its format is: * "organizations/{organization}/eventThreatDetect
      ionSettings/customModules/{module}". *
      "folders/{folder}/eventThreatDetectionSettings/customModules/{module}".
      * "projects/{project}/eventThreatDetectionSettings/customModules/{module
      }".
    updateMask: The list of fields to be updated. If empty all mutable fields
      will be updated.
  """

  eventThreatDetectionCustomModule = _messages.MessageField('EventThreatDetectionCustomModule', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterOrganizationsEventThreatDetectionSettingsEffectiveCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsEventThreatDetectionSettingsEffectiveCustom
  ModulesGetRequest object.

  Fields:
    name: Required. The resource name of the effective Event Threat Detection
      custom module. Its format is: * "organizations/{organization}/eventThrea
      tDetectionSettings/effectiveCustomModules/{module}".
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsEventThreatDetectionSettingsEffectiveCustomModulesListRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsEventThreatDetectionSettingsEffectiveCustom
  ModulesListRequest object.

  Fields:
    pageSize: The maximum number of modules to return. The service may return
      fewer than this value. If unspecified, at most 10 configs will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: A page token, received from a previous
      `ListEffectiveEventThreatDetectionCustomModules` call. Provide this to
      retrieve the subsequent page. When paginating, all other parameters
      provided to `ListEffectiveEventThreatDetectionCustomModules` must match
      the call that provided the page token.
    parent: Required. Name of the parent to list custom modules for. Its
      format is: *
      "organizations/{organization}/eventThreatDetectionSettings".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterOrganizationsEventThreatDetectionSettingsValidateCustomModuleRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsEventThreatDetectionSettingsValidateCustomM
  oduleRequest object.

  Fields:
    parent: Required. Resource name of the parent to validate the Custom
      Module under. Its format is: *
      "organizations/{organization}/eventThreatDetectionSettings".
    validateEventThreatDetectionCustomModuleRequest: A
      ValidateEventThreatDetectionCustomModuleRequest resource to be passed as
      the request body.
  """

  parent = _messages.StringField(1, required=True)
  validateEventThreatDetectionCustomModuleRequest = _messages.MessageField('ValidateEventThreatDetectionCustomModuleRequest', 2)


class SecuritycenterOrganizationsFindingsBulkMuteRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsFindingsBulkMuteRequest object.

  Fields:
    bulkMuteFindingsRequest: A BulkMuteFindingsRequest resource to be passed
      as the request body.
    parent: Required. The parent, at which bulk action needs to be applied.
      Its format is "organizations/[organization_id]", "folders/[folder_id]",
      "projects/[project_id]".
  """

  bulkMuteFindingsRequest = _messages.MessageField('BulkMuteFindingsRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecuritycenterOrganizationsGetOrganizationSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsGetOrganizationSettingsRequest object.

  Fields:
    name: Required. Name of the organization to get organization settings for.
      Its format is "organizations/[organization_id]/organizationSettings".
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsMuteConfigsCreateRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsMuteConfigsCreateRequest object.

  Fields:
    googleCloudSecuritycenterV1MuteConfig: A
      GoogleCloudSecuritycenterV1MuteConfig resource to be passed as the
      request body.
    muteConfigId: Required. Unique identifier provided by the client within
      the parent scope. It must consist of only lowercase letters, numbers,
      and hyphens, must start with a letter, must end with either a letter or
      a number, and must be 63 characters or less.
    parent: Required. Resource name of the new mute configs's parent. Its
      format is "organizations/[organization_id]", "folders/[folder_id]", or
      "projects/[project_id]".
  """

  googleCloudSecuritycenterV1MuteConfig = _messages.MessageField('GoogleCloudSecuritycenterV1MuteConfig', 1)
  muteConfigId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterOrganizationsMuteConfigsDeleteRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsMuteConfigsDeleteRequest object.

  Fields:
    name: Required. Name of the mute config to delete. Its format is
      organizations/{organization}/muteConfigs/{config_id},
      folders/{folder}/muteConfigs/{config_id}, or
      projects/{project}/muteConfigs/{config_id}
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsMuteConfigsGetRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsMuteConfigsGetRequest object.

  Fields:
    name: Required. Name of the mute config to retrieve. Its format is
      organizations/{organization}/muteConfigs/{config_id},
      folders/{folder}/muteConfigs/{config_id}, or
      projects/{project}/muteConfigs/{config_id}
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsMuteConfigsListRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsMuteConfigsListRequest object.

  Fields:
    pageSize: The maximum number of configs to return. The service may return
      fewer than this value. If unspecified, at most 10 configs will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: A page token, received from a previous `ListMuteConfigs` call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to `ListMuteConfigs` must match the call that
      provided the page token.
    parent: Required. The parent, which owns the collection of mute configs.
      Its format is "organizations/[organization_id]", "folders/[folder_id]",
      "projects/[project_id]".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterOrganizationsMuteConfigsPatchRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsMuteConfigsPatchRequest object.

  Fields:
    googleCloudSecuritycenterV1MuteConfig: A
      GoogleCloudSecuritycenterV1MuteConfig resource to be passed as the
      request body.
    name: This field will be ignored if provided on config creation. Format
      "organizations/{organization}/muteConfigs/{mute_config}"
      "folders/{folder}/muteConfigs/{mute_config}"
      "projects/{project}/muteConfigs/{mute_config}"
    updateMask: The list of fields to be updated. If empty all mutable fields
      will be updated.
  """

  googleCloudSecuritycenterV1MuteConfig = _messages.MessageField('GoogleCloudSecuritycenterV1MuteConfig', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterOrganizationsNotificationConfigsCreateRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsNotificationConfigsCreateRequest object.

  Fields:
    configId: Required. Unique identifier provided by the client within the
      parent scope. It must be between 1 and 128 characters and contain
      alphanumeric characters, underscores, or hyphens only.
    notificationConfig: A NotificationConfig resource to be passed as the
      request body.
    parent: Required. Resource name of the new notification config's parent.
      Its format is "organizations/[organization_id]", "folders/[folder_id]",
      or "projects/[project_id]".
  """

  configId = _messages.StringField(1)
  notificationConfig = _messages.MessageField('NotificationConfig', 2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterOrganizationsNotificationConfigsDeleteRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsNotificationConfigsDeleteRequest object.

  Fields:
    name: Required. Name of the notification config to delete. Its format is
      "organizations/[organization_id]/notificationConfigs/[config_id]",
      "folders/[folder_id]/notificationConfigs/[config_id]", or
      "projects/[project_id]/notificationConfigs/[config_id]".
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsNotificationConfigsGetRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsNotificationConfigsGetRequest object.

  Fields:
    name: Required. Name of the notification config to get. Its format is
      "organizations/[organization_id]/notificationConfigs/[config_id]",
      "folders/[folder_id]/notificationConfigs/[config_id]", or
      "projects/[project_id]/notificationConfigs/[config_id]".
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsNotificationConfigsListRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsNotificationConfigsListRequest object.

  Fields:
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last
      `ListNotificationConfigsResponse`; indicates that this is a continuation
      of a prior `ListNotificationConfigs` call, and that the system should
      return the next page of data.
    parent: Required. The name of the parent in which to list the notification
      configurations. Its format is "organizations/[organization_id]",
      "folders/[folder_id]", or "projects/[project_id]".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterOrganizationsNotificationConfigsPatchRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsNotificationConfigsPatchRequest object.

  Fields:
    name: The relative resource name of this notification config. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Example: "organizations/{organization_id}/notificationConfigs/notify_
      public_bucket",
      "folders/{folder_id}/notificationConfigs/notify_public_bucket", or
      "projects/{project_id}/notificationConfigs/notify_public_bucket".
    notificationConfig: A NotificationConfig resource to be passed as the
      request body.
    updateMask: The FieldMask to use when updating the notification config. If
      empty all mutable fields will be updated.
  """

  name = _messages.StringField(1, required=True)
  notificationConfig = _messages.MessageField('NotificationConfig', 2)
  updateMask = _messages.StringField(3)


class SecuritycenterOrganizationsOperationsCancelRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsOperationsCancelRequest object.

  Fields:
    name: The name of the operation resource to be cancelled.
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsOperationsDeleteRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsOperationsGetRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsOperationsListRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class SecuritycenterOrganizationsResourceValueConfigsBatchCreateRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsResourceValueConfigsBatchCreateRequest
  object.

  Fields:
    batchCreateResourceValueConfigsRequest: A
      BatchCreateResourceValueConfigsRequest resource to be passed as the
      request body.
    parent: Required. Resource name of the new ResourceValueConfig's parent.
      The parent field in the CreateResourceValueConfigRequest messages must
      either be empty or match this field.
  """

  batchCreateResourceValueConfigsRequest = _messages.MessageField('BatchCreateResourceValueConfigsRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecuritycenterOrganizationsResourceValueConfigsDeleteRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsResourceValueConfigsDeleteRequest object.

  Fields:
    name: Required. Name of the ResourceValueConfig to delete
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsResourceValueConfigsGetRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsResourceValueConfigsGetRequest object.

  Fields:
    name: Required. Name of the resource value config to retrieve. Its format
      is organizations/{organization}/resourceValueConfigs/{config_id}.
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsResourceValueConfigsListRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsResourceValueConfigsListRequest object.

  Fields:
    pageSize: The number of results to return. The service may return fewer
      than this value. If unspecified, at most 10 configs will be returned.
      The maximum value is 1000; values above 1000 will be coerced to 1000.
    pageToken: A page token, received from a previous
      `ListResourceValueConfigs` call. Provide this to retrieve the subsequent
      page. When paginating, all other parameters provided to
      `ListResourceValueConfigs` must match the call that provided the page
      token. page_size can be specified, and the new page_size will be used.
    parent: Required. The parent, which owns the collection of resource value
      configs. Its format is "organizations/[organization_id]"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterOrganizationsResourceValueConfigsPatchRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsResourceValueConfigsPatchRequest object.

  Fields:
    googleCloudSecuritycenterV1ResourceValueConfig: A
      GoogleCloudSecuritycenterV1ResourceValueConfig resource to be passed as
      the request body.
    name: Name for the resource value config
    updateMask: The list of fields to be updated. If empty all mutable fields
      will be updated.
  """

  googleCloudSecuritycenterV1ResourceValueConfig = _messages.MessageField('GoogleCloudSecuritycenterV1ResourceValueConfig', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesCreateRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModule
  sCreateRequest object.

  Fields:
    googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule: A
      GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule resource
      to be passed as the request body.
    parent: Required. Resource name of the new custom module's parent. Its
      format is
      "organizations/{organization}/securityHealthAnalyticsSettings",
      "folders/{folder}/securityHealthAnalyticsSettings", or
      "projects/{project}/securityHealthAnalyticsSettings"
  """

  googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule = _messages.MessageField('GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule', 1)
  parent = _messages.StringField(2, required=True)


class SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesDeleteRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModule
  sDeleteRequest object.

  Fields:
    name: Required. Name of the custom module to delete. Its format is "organi
      zations/{organization}/securityHealthAnalyticsSettings/customModules/{cu
      stomModule}", "folders/{folder}/securityHealthAnalyticsSettings/customMo
      dules/{customModule}", or "projects/{project}/securityHealthAnalyticsSet
      tings/customModules/{customModule}"
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModule
  sGetRequest object.

  Fields:
    name: Required. Name of the custom module to get. Its format is "organizat
      ions/{organization}/securityHealthAnalyticsSettings/customModules/{custo
      mModule}", "folders/{folder}/securityHealthAnalyticsSettings/customModul
      es/{customModule}", or "projects/{project}/securityHealthAnalyticsSettin
      gs/customModules/{customModule}"
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesListDescendantRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModule
  sListDescendantRequest object.

  Fields:
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last call indicating a continuation
    parent: Required. Name of parent to list descendant custom modules. Its
      format is
      "organizations/{organization}/securityHealthAnalyticsSettings",
      "folders/{folder}/securityHealthAnalyticsSettings", or
      "projects/{project}/securityHealthAnalyticsSettings"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesListRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModule
  sListRequest object.

  Fields:
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last call indicating a continuation
    parent: Required. Name of parent to list custom modules. Its format is
      "organizations/{organization}/securityHealthAnalyticsSettings",
      "folders/{folder}/securityHealthAnalyticsSettings", or
      "projects/{project}/securityHealthAnalyticsSettings"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesPatchRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModule
  sPatchRequest object.

  Fields:
    googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule: A
      GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule resource
      to be passed as the request body.
    name: Immutable. The resource name of the custom module. Its format is "or
      ganizations/{organization}/securityHealthAnalyticsSettings/customModules
      /{customModule}", or "folders/{folder}/securityHealthAnalyticsSettings/c
      ustomModules/{customModule}", or "projects/{project}/securityHealthAnaly
      ticsSettings/customModules/{customModule}" The id {customModule} is
      server-generated and is not user settable. It will be a numeric id
      containing 1-20 digits.
    updateMask: The list of fields to update.
  """

  googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule = _messages.MessageField('GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesSimulateRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModule
  sSimulateRequest object.

  Fields:
    parent: Required. The relative resource name of the organization, project,
      or folder. See: https://cloud.google.com/apis/design/resource_names#rela
      tive_resource_name An example is: "organizations/{organization_id}".
    simulateSecurityHealthAnalyticsCustomModuleRequest: A
      SimulateSecurityHealthAnalyticsCustomModuleRequest resource to be passed
      as the request body.
  """

  parent = _messages.StringField(1, required=True)
  simulateSecurityHealthAnalyticsCustomModuleRequest = _messages.MessageField('SimulateSecurityHealthAnalyticsCustomModuleRequest', 2)


class SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModulesTestRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCustomModule
  sTestRequest object.

  Fields:
    name: Required. Name of the custom module to test. Its format is "organiza
      tions/[organization_id]/securityHealthAnalyticsSettings/customModules/[m
      odule_id]". If the custom_module field is empty, it is assumed that the
      custom module already exists; otherwise the specified custom_module will
      be used.
    testSecurityHealthAnalyticsCustomModuleRequest: A
      TestSecurityHealthAnalyticsCustomModuleRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  testSecurityHealthAnalyticsCustomModuleRequest = _messages.MessageField('TestSecurityHealthAnalyticsCustomModuleRequest', 2)


class SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsEffectiveCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsEffectiveCus
  tomModulesGetRequest object.

  Fields:
    name: Required. Name of the effective custom module to get. Its format is
      "organizations/{organization}/securityHealthAnalyticsSettings/effectiveC
      ustomModules/{customModule}", "folders/{folder}/securityHealthAnalyticsS
      ettings/effectiveCustomModules/{customModule}", or "projects/{project}/s
      ecurityHealthAnalyticsSettings/effectiveCustomModules/{customModule}"
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsEffectiveCustomModulesListRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsEffectiveCus
  tomModulesListRequest object.

  Fields:
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last call indicating a continuation
    parent: Required. Name of parent to list effective custom modules. Its
      format is
      "organizations/{organization}/securityHealthAnalyticsSettings",
      "folders/{folder}/securityHealthAnalyticsSettings", or
      "projects/{project}/securityHealthAnalyticsSettings"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterOrganizationsSimulationsAttackExposureResultsAttackPathsListRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSimulationsAttackExposureResultsAttackPaths
  ListRequest object.

  Fields:
    filter: The filter expression that filters the attack path in the
      response. Supported fields: * `valued_resources` supports =
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last `ListAttackPathsResponse`;
      indicates that this is a continuation of a prior `ListAttackPaths` call,
      and that the system should return the next page of data.
    parent: Required. Name of parent to list attack paths. Valid formats:
      "organizations/{organization}",
      "organizations/{organization}/simulations/{simulation}" "organizations/{
      organization}/simulations/{simulation}/attackExposureResults/{attack_exp
      osure_result_v2}" "organizations/{organization}/simulations/{simulation}
      /valuedResources/{valued_resource}"
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SecuritycenterOrganizationsSimulationsAttackExposureResultsValuedResourcesListRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSimulationsAttackExposureResultsValuedResou
  rcesListRequest object.

  Fields:
    filter: The filter expression that filters the valued resources in the
      response. Supported fields: * `resource_value` supports = *
      `resource_type` supports =
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last `ListValuedResourcesResponse`;
      indicates that this is a continuation of a prior `ListValuedResources`
      call, and that the system should return the next page of data.
    parent: Required. Name of parent to list valued resources. Valid formats:
      "organizations/{organization}",
      "organizations/{organization}/simulations/{simulation}" "organizations/{
      organization}/simulations/{simulation}/attackExposureResults/{attack_exp
      osure_result_v2}"
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SecuritycenterOrganizationsSimulationsAttackPathsListRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSimulationsAttackPathsListRequest object.

  Fields:
    filter: The filter expression that filters the attack path in the
      response. Supported fields: * `valued_resources` supports =
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last `ListAttackPathsResponse`;
      indicates that this is a continuation of a prior `ListAttackPaths` call,
      and that the system should return the next page of data.
    parent: Required. Name of parent to list attack paths. Valid formats:
      "organizations/{organization}",
      "organizations/{organization}/simulations/{simulation}" "organizations/{
      organization}/simulations/{simulation}/attackExposureResults/{attack_exp
      osure_result_v2}" "organizations/{organization}/simulations/{simulation}
      /valuedResources/{valued_resource}"
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SecuritycenterOrganizationsSimulationsGetRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSimulationsGetRequest object.

  Fields:
    name: Required. The organization name or simulation name of this
      simulation Valid format:
      "organizations/{organization}/simulations/latest"
      "organizations/{organization}/simulations/{simulation}"
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsSimulationsValuedResourcesAttackPathsListRequest(_messages.Message):
  r"""A
  SecuritycenterOrganizationsSimulationsValuedResourcesAttackPathsListRequest
  object.

  Fields:
    filter: The filter expression that filters the attack path in the
      response. Supported fields: * `valued_resources` supports =
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last `ListAttackPathsResponse`;
      indicates that this is a continuation of a prior `ListAttackPaths` call,
      and that the system should return the next page of data.
    parent: Required. Name of parent to list attack paths. Valid formats:
      "organizations/{organization}",
      "organizations/{organization}/simulations/{simulation}" "organizations/{
      organization}/simulations/{simulation}/attackExposureResults/{attack_exp
      osure_result_v2}" "organizations/{organization}/simulations/{simulation}
      /valuedResources/{valued_resource}"
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SecuritycenterOrganizationsSimulationsValuedResourcesListRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSimulationsValuedResourcesListRequest
  object.

  Fields:
    filter: The filter expression that filters the valued resources in the
      response. Supported fields: * `resource_value` supports = *
      `resource_type` supports =
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last `ListValuedResourcesResponse`;
      indicates that this is a continuation of a prior `ListValuedResources`
      call, and that the system should return the next page of data.
    parent: Required. Name of parent to list valued resources. Valid formats:
      "organizations/{organization}",
      "organizations/{organization}/simulations/{simulation}" "organizations/{
      organization}/simulations/{simulation}/attackExposureResults/{attack_exp
      osure_result_v2}"
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class SecuritycenterOrganizationsSourcesCreateRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSourcesCreateRequest object.

  Fields:
    parent: Required. Resource name of the new source's parent. Its format
      should be "organizations/[organization_id]".
    source: A Source resource to be passed as the request body.
  """

  parent = _messages.StringField(1, required=True)
  source = _messages.MessageField('Source', 2)


class SecuritycenterOrganizationsSourcesFindingsCreateRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSourcesFindingsCreateRequest object.

  Fields:
    finding: A Finding resource to be passed as the request body.
    findingId: Required. Unique identifier provided by the client within the
      parent scope. It must be alphanumeric and less than or equal to 32
      characters and greater than 0 characters in length.
    parent: Required. Resource name of the new finding's parent. Its format
      should be "organizations/[organization_id]/sources/[source_id]".
  """

  finding = _messages.MessageField('Finding', 1)
  findingId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterOrganizationsSourcesFindingsExternalSystemsPatchRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSourcesFindingsExternalSystemsPatchRequest
  object.

  Fields:
    googleCloudSecuritycenterV1ExternalSystem: A
      GoogleCloudSecuritycenterV1ExternalSystem resource to be passed as the
      request body.
    name: Full resource name of the external system, for example:
      "organizations/1234/sources/5678/findings/123456/externalSystems/jira",
      "folders/1234/sources/5678/findings/123456/externalSystems/jira",
      "projects/1234/sources/5678/findings/123456/externalSystems/jira"
    updateMask: The FieldMask to use when updating the external system
      resource. If empty all mutable fields will be updated.
  """

  googleCloudSecuritycenterV1ExternalSystem = _messages.MessageField('GoogleCloudSecuritycenterV1ExternalSystem', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterOrganizationsSourcesFindingsGroupRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSourcesFindingsGroupRequest object.

  Fields:
    groupFindingsRequest: A GroupFindingsRequest resource to be passed as the
      request body.
    parent: Required. Name of the source to groupBy. Its format is
      "organizations/[organization_id]/sources/[source_id]",
      folders/[folder_id]/sources/[source_id], or
      projects/[project_id]/sources/[source_id]. To groupBy across all sources
      provide a source_id of `-`. For example:
      organizations/{organization_id}/sources/-,
      folders/{folder_id}/sources/-, or projects/{project_id}/sources/-
  """

  groupFindingsRequest = _messages.MessageField('GroupFindingsRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecuritycenterOrganizationsSourcesFindingsListRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSourcesFindingsListRequest object.

  Fields:
    compareDuration: When compare_duration is set, the ListFindingsResult's
      "state_change" attribute is updated to indicate whether the finding had
      its state changed, the finding's state remained unchanged, or if the
      finding was added in any state during the compare_duration period of
      time that precedes the read_time. This is the time between (read_time -
      compare_duration) and read_time. The state_change value is derived based
      on the presence and state of the finding at the two points in time.
      Intermediate state changes between the two times don't affect the
      result. For example, the results aren't affected if the finding is made
      inactive and then active again. Possible "state_change" values when
      compare_duration is specified: * "CHANGED": indicates that the finding
      was present and matched the given filter at the start of
      compare_duration, but changed its state at read_time. * "UNCHANGED":
      indicates that the finding was present and matched the given filter at
      the start of compare_duration and did not change state at read_time. *
      "ADDED": indicates that the finding did not match the given filter or
      was not present at the start of compare_duration, but was present at
      read_time. * "REMOVED": indicates that the finding was present and
      matched the filter at the start of compare_duration, but did not match
      the filter at read_time. If compare_duration is not specified, then the
      only possible state_change is "UNUSED", which will be the state_change
      set for all findings present at read_time.
    fieldMask: A field mask to specify the Finding fields to be listed in the
      response. An empty field mask will list all fields.
    filter: Expression that defines the filter to apply across findings. The
      expression is a list of one or more restrictions combined via logical
      operators `AND` and `OR`. Parentheses are supported, and `OR` has higher
      precedence than `AND`. Restrictions have the form ` ` and may have a `-`
      character in front of them to indicate negation. Examples include: *
      name * source_properties.a_property * security_marks.marks.marka The
      supported operators are: * `=` for all value types. * `>`, `<`, `>=`,
      `<=` for integer values. * `:`, meaning substring matching, for strings.
      The supported value types are: * string literals in quotes. * integer
      literals without quotes. * boolean literals `true` and `false` without
      quotes. The following field and operator combinations are supported: *
      name: `=` * parent: `=`, `:` * resource_name: `=`, `:` * state: `=`, `:`
      * category: `=`, `:` * external_uri: `=`, `:` * event_time: `=`, `>`,
      `<`, `>=`, `<=` Usage: This should be milliseconds since epoch or an
      RFC3339 string. Examples: `event_time = "2019-06-10T16:07:18-07:00"`
      `event_time = 1560208038000` * severity: `=`, `:` * workflow_state: `=`,
      `:` * security_marks.marks: `=`, `:` * source_properties: `=`, `:`, `>`,
      `<`, `>=`, `<=` For example, `source_properties.size = 100` is a valid
      filter string. Use a partial match on the empty string to filter based
      on a property existing: `source_properties.my_property : ""` Use a
      negated partial match on the empty string to filter based on a property
      not existing: `-source_properties.my_property : ""` * resource: *
      resource.name: `=`, `:` * resource.parent_name: `=`, `:` *
      resource.parent_display_name: `=`, `:` * resource.project_name: `=`, `:`
      * resource.project_display_name: `=`, `:` * resource.type: `=`, `:` *
      resource.folders.resource_folder: `=`, `:` * resource.display_name: `=`,
      `:`
    orderBy: Expression that defines what fields and order to use for sorting.
      The string value should follow SQL syntax: comma separated list of
      fields. For example: "name,resource_properties.a_property". The default
      sorting order is ascending. To specify descending order for a field, a
      suffix " desc" should be appended to the field name. For example: "name
      desc,source_properties.a_property". Redundant space characters in the
      syntax are insignificant. "name desc,source_properties.a_property" and "
      name desc , source_properties.a_property " are equivalent. The following
      fields are supported: name parent state category resource_name
      event_time source_properties security_marks.marks
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last `ListFindingsResponse`;
      indicates that this is a continuation of a prior `ListFindings` call,
      and that the system should return the next page of data.
    parent: Required. Name of the source the findings belong to. Its format is
      "organizations/[organization_id]/sources/[source_id],
      folders/[folder_id]/sources/[source_id], or
      projects/[project_id]/sources/[source_id]". To list across all sources
      provide a source_id of `-`. For example:
      organizations/{organization_id}/sources/-, folders/{folder_id}/sources/-
      or projects/{projects_id}/sources/-
    readTime: Time used as a reference point when filtering findings. The
      filter is limited to findings existing at the supplied time and their
      values are those at that specific time. Absence of this field will
      default to the API's version of NOW.
  """

  compareDuration = _messages.StringField(1)
  fieldMask = _messages.StringField(2)
  filter = _messages.StringField(3)
  orderBy = _messages.StringField(4)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)
  parent = _messages.StringField(7, required=True)
  readTime = _messages.StringField(8)


class SecuritycenterOrganizationsSourcesFindingsPatchRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSourcesFindingsPatchRequest object.

  Fields:
    finding: A Finding resource to be passed as the request body.
    name: The [relative resource name](https://cloud.google.com/apis/design/re
      source_names#relative_resource_name) of the finding. Example: "organizat
      ions/{organization_id}/sources/{source_id}/findings/{finding_id}",
      "folders/{folder_id}/sources/{source_id}/findings/{finding_id}",
      "projects/{project_id}/sources/{source_id}/findings/{finding_id}".
    updateMask: The FieldMask to use when updating the finding resource. This
      field should not be specified when creating a finding. When updating a
      finding, an empty mask is treated as updating all mutable fields and
      replacing source_properties. Individual source_properties can be
      added/updated by using "source_properties." in the field mask.
  """

  finding = _messages.MessageField('Finding', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterOrganizationsSourcesFindingsSetMuteRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSourcesFindingsSetMuteRequest object.

  Fields:
    name: Required. The [relative resource name](https://cloud.google.com/apis
      /design/resource_names#relative_resource_name) of the finding. Example:
      "organizations/{organization_id}/sources/{source_id}/findings/{finding_i
      d}", "folders/{folder_id}/sources/{source_id}/findings/{finding_id}",
      "projects/{project_id}/sources/{source_id}/findings/{finding_id}".
    setMuteRequest: A SetMuteRequest resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  setMuteRequest = _messages.MessageField('SetMuteRequest', 2)


class SecuritycenterOrganizationsSourcesFindingsSetStateRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSourcesFindingsSetStateRequest object.

  Fields:
    name: Required. The [relative resource name](https://cloud.google.com/apis
      /design/resource_names#relative_resource_name) of the finding. Example:
      "organizations/{organization_id}/sources/{source_id}/findings/{finding_i
      d}", "folders/{folder_id}/sources/{source_id}/findings/{finding_id}",
      "projects/{project_id}/sources/{source_id}/findings/{finding_id}".
    setFindingStateRequest: A SetFindingStateRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  setFindingStateRequest = _messages.MessageField('SetFindingStateRequest', 2)


class SecuritycenterOrganizationsSourcesFindingsUpdateSecurityMarksRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSourcesFindingsUpdateSecurityMarksRequest
  object.

  Fields:
    name: The relative resource name of the SecurityMarks. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks" "organ
      izations/{organization_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks".
    securityMarks: A SecurityMarks resource to be passed as the request body.
    startTime: The time at which the updated SecurityMarks take effect. If not
      set uses current server time. Updates will be applied to the
      SecurityMarks that are active immediately preceding this time. Must be
      earlier or equal to the server time.
    updateMask: The FieldMask to use when updating the security marks
      resource. The field mask must not contain duplicate fields. If empty or
      set to "marks", all marks will be replaced. Individual marks can be
      updated using "marks.".
  """

  name = _messages.StringField(1, required=True)
  securityMarks = _messages.MessageField('SecurityMarks', 2)
  startTime = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class SecuritycenterOrganizationsSourcesGetIamPolicyRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSourcesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class SecuritycenterOrganizationsSourcesGetRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSourcesGetRequest object.

  Fields:
    name: Required. Relative resource name of the source. Its format is
      "organizations/[organization_id]/source/[source_id]".
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsSourcesListRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSourcesListRequest object.

  Fields:
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last `ListSourcesResponse`; indicates
      that this is a continuation of a prior `ListSources` call, and that the
      system should return the next page of data.
    parent: Required. Resource name of the parent of sources to list. Its
      format should be "organizations/[organization_id]",
      "folders/[folder_id]", or "projects/[project_id]".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterOrganizationsSourcesPatchRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSourcesPatchRequest object.

  Fields:
    name: The relative resource name of this source. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Example: "organizations/{organization_id}/sources/{source_id}"
    source: A Source resource to be passed as the request body.
    updateMask: The FieldMask to use when updating the source resource. If
      empty all mutable fields will be updated.
  """

  name = _messages.StringField(1, required=True)
  source = _messages.MessageField('Source', 2)
  updateMask = _messages.StringField(3)


class SecuritycenterOrganizationsSourcesSetIamPolicyRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSourcesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class SecuritycenterOrganizationsSourcesTestIamPermissionsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsSourcesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class SecuritycenterOrganizationsUpdateOrganizationSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsUpdateOrganizationSettingsRequest object.

  Fields:
    name: The relative resource name of the settings. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Example: "organizations/{organization_id}/organizationSettings".
    organizationSettings: A OrganizationSettings resource to be passed as the
      request body.
    updateMask: The FieldMask to use when updating the settings resource. If
      empty all mutable fields will be updated.
  """

  name = _messages.StringField(1, required=True)
  organizationSettings = _messages.MessageField('OrganizationSettings', 2)
  updateMask = _messages.StringField(3)


class SecuritycenterProjectsAssetsGroupRequest(_messages.Message):
  r"""A SecuritycenterProjectsAssetsGroupRequest object.

  Fields:
    groupAssetsRequest: A GroupAssetsRequest resource to be passed as the
      request body.
    parent: Required. The name of the parent to group the assets by. Its
      format is "organizations/[organization_id]", "folders/[folder_id]", or
      "projects/[project_id]".
  """

  groupAssetsRequest = _messages.MessageField('GroupAssetsRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecuritycenterProjectsAssetsListRequest(_messages.Message):
  r"""A SecuritycenterProjectsAssetsListRequest object.

  Fields:
    compareDuration: When compare_duration is set, the ListAssetsResult's
      "state_change" attribute is updated to indicate whether the asset was
      added, removed, or remained present during the compare_duration period
      of time that precedes the read_time. This is the time between (read_time
      - compare_duration) and read_time. The state_change value is derived
      based on the presence of the asset at the two points in time.
      Intermediate state changes between the two times don't affect the
      result. For example, the results aren't affected if the asset is removed
      and re-created again. Possible "state_change" values when
      compare_duration is specified: * "ADDED": indicates that the asset was
      not present at the start of compare_duration, but present at read_time.
      * "REMOVED": indicates that the asset was present at the start of
      compare_duration, but not present at read_time. * "ACTIVE": indicates
      that the asset was present at both the start and the end of the time
      period defined by compare_duration and read_time. If compare_duration is
      not specified, then the only possible state_change is "UNUSED", which
      will be the state_change set for all assets present at read_time.
    fieldMask: A field mask to specify the ListAssetsResult fields to be
      listed in the response. An empty field mask will list all fields.
    filter: Expression that defines the filter to apply across assets. The
      expression is a list of zero or more restrictions combined via logical
      operators `AND` and `OR`. Parentheses are supported, and `OR` has higher
      precedence than `AND`. Restrictions have the form ` ` and may have a `-`
      character in front of them to indicate negation. The fields map to those
      defined in the Asset resource. Examples include: * name *
      security_center_properties.resource_name *
      resource_properties.a_property * security_marks.marks.marka The
      supported operators are: * `=` for all value types. * `>`, `<`, `>=`,
      `<=` for integer values. * `:`, meaning substring matching, for strings.
      The supported value types are: * string literals in quotes. * integer
      literals without quotes. * boolean literals `true` and `false` without
      quotes. The following are the allowed field and operator combinations: *
      name: `=` * update_time: `=`, `>`, `<`, `>=`, `<=` Usage: This should be
      milliseconds since epoch or an RFC3339 string. Examples: `update_time =
      "2019-06-10T16:07:18-07:00"` `update_time = 1560208038000` *
      create_time: `=`, `>`, `<`, `>=`, `<=` Usage: This should be
      milliseconds since epoch or an RFC3339 string. Examples: `create_time =
      "2019-06-10T16:07:18-07:00"` `create_time = 1560208038000` *
      iam_policy.policy_blob: `=`, `:` * resource_properties: `=`, `:`, `>`,
      `<`, `>=`, `<=` * security_marks.marks: `=`, `:` *
      security_center_properties.resource_name: `=`, `:` *
      security_center_properties.resource_display_name: `=`, `:` *
      security_center_properties.resource_type: `=`, `:` *
      security_center_properties.resource_parent: `=`, `:` *
      security_center_properties.resource_parent_display_name: `=`, `:` *
      security_center_properties.resource_project: `=`, `:` *
      security_center_properties.resource_project_display_name: `=`, `:` *
      security_center_properties.resource_owners: `=`, `:` For example,
      `resource_properties.size = 100` is a valid filter string. Use a partial
      match on the empty string to filter based on a property existing:
      `resource_properties.my_property : ""` Use a negated partial match on
      the empty string to filter based on a property not existing:
      `-resource_properties.my_property : ""`
    orderBy: Expression that defines what fields and order to use for sorting.
      The string value should follow SQL syntax: comma separated list of
      fields. For example: "name,resource_properties.a_property". The default
      sorting order is ascending. To specify descending order for a field, a
      suffix " desc" should be appended to the field name. For example: "name
      desc,resource_properties.a_property". Redundant space characters in the
      syntax are insignificant. "name desc,resource_properties.a_property" and
      " name desc , resource_properties.a_property " are equivalent. The
      following fields are supported: name update_time resource_properties
      security_marks.marks security_center_properties.resource_name
      security_center_properties.resource_display_name
      security_center_properties.resource_parent
      security_center_properties.resource_parent_display_name
      security_center_properties.resource_project
      security_center_properties.resource_project_display_name
      security_center_properties.resource_type
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last `ListAssetsResponse`; indicates
      that this is a continuation of a prior `ListAssets` call, and that the
      system should return the next page of data.
    parent: Required. The name of the parent resource that contains the
      assets. The value that you can specify on parent depends on the method
      in which you specify parent. You can specify one of the following
      values: "organizations/[organization_id]", "folders/[folder_id]", or
      "projects/[project_id]".
    readTime: Time used as a reference point when filtering assets. The filter
      is limited to assets existing at the supplied time and their values are
      those at that specific time. Absence of this field will default to the
      API's version of NOW.
  """

  compareDuration = _messages.StringField(1)
  fieldMask = _messages.StringField(2)
  filter = _messages.StringField(3)
  orderBy = _messages.StringField(4)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)
  parent = _messages.StringField(7, required=True)
  readTime = _messages.StringField(8)


class SecuritycenterProjectsAssetsUpdateSecurityMarksRequest(_messages.Message):
  r"""A SecuritycenterProjectsAssetsUpdateSecurityMarksRequest object.

  Fields:
    name: The relative resource name of the SecurityMarks. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks" "organ
      izations/{organization_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks".
    securityMarks: A SecurityMarks resource to be passed as the request body.
    startTime: The time at which the updated SecurityMarks take effect. If not
      set uses current server time. Updates will be applied to the
      SecurityMarks that are active immediately preceding this time. Must be
      earlier or equal to the server time.
    updateMask: The FieldMask to use when updating the security marks
      resource. The field mask must not contain duplicate fields. If empty or
      set to "marks", all marks will be replaced. Individual marks can be
      updated using "marks.".
  """

  name = _messages.StringField(1, required=True)
  securityMarks = _messages.MessageField('SecurityMarks', 2)
  startTime = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class SecuritycenterProjectsBigQueryExportsCreateRequest(_messages.Message):
  r"""A SecuritycenterProjectsBigQueryExportsCreateRequest object.

  Fields:
    bigQueryExportId: Required. Unique identifier provided by the client
      within the parent scope. It must consist of only lowercase letters,
      numbers, and hyphens, must start with a letter, must end with either a
      letter or a number, and must be 63 characters or less.
    googleCloudSecuritycenterV1BigQueryExport: A
      GoogleCloudSecuritycenterV1BigQueryExport resource to be passed as the
      request body.
    parent: Required. The name of the parent resource of the new BigQuery
      export. Its format is "organizations/[organization_id]",
      "folders/[folder_id]", or "projects/[project_id]".
  """

  bigQueryExportId = _messages.StringField(1)
  googleCloudSecuritycenterV1BigQueryExport = _messages.MessageField('GoogleCloudSecuritycenterV1BigQueryExport', 2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterProjectsBigQueryExportsDeleteRequest(_messages.Message):
  r"""A SecuritycenterProjectsBigQueryExportsDeleteRequest object.

  Fields:
    name: Required. The name of the BigQuery export to delete. Its format is
      organizations/{organization}/bigQueryExports/{export_id},
      folders/{folder}/bigQueryExports/{export_id}, or
      projects/{project}/bigQueryExports/{export_id}
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsBigQueryExportsGetRequest(_messages.Message):
  r"""A SecuritycenterProjectsBigQueryExportsGetRequest object.

  Fields:
    name: Required. Name of the BigQuery export to retrieve. Its format is
      organizations/{organization}/bigQueryExports/{export_id},
      folders/{folder}/bigQueryExports/{export_id}, or
      projects/{project}/bigQueryExports/{export_id}
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsBigQueryExportsListRequest(_messages.Message):
  r"""A SecuritycenterProjectsBigQueryExportsListRequest object.

  Fields:
    pageSize: The maximum number of configs to return. The service may return
      fewer than this value. If unspecified, at most 10 configs will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: A page token, received from a previous `ListBigQueryExports`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListBigQueryExports` must match the call
      that provided the page token.
    parent: Required. The parent, which owns the collection of BigQuery
      exports. Its format is "organizations/[organization_id]",
      "folders/[folder_id]", "projects/[project_id]".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterProjectsBigQueryExportsPatchRequest(_messages.Message):
  r"""A SecuritycenterProjectsBigQueryExportsPatchRequest object.

  Fields:
    googleCloudSecuritycenterV1BigQueryExport: A
      GoogleCloudSecuritycenterV1BigQueryExport resource to be passed as the
      request body.
    name: The relative resource name of this export. See: https://cloud.google
      .com/apis/design/resource_names#relative_resource_name. Example format:
      "organizations/{organization_id}/bigQueryExports/{export_id}" Example
      format: "folders/{folder_id}/bigQueryExports/{export_id}" Example
      format: "projects/{project_id}/bigQueryExports/{export_id}" This field
      is provided in responses, and is ignored when provided in create
      requests.
    updateMask: The list of fields to be updated. If empty all mutable fields
      will be updated.
  """

  googleCloudSecuritycenterV1BigQueryExport = _messages.MessageField('GoogleCloudSecuritycenterV1BigQueryExport', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterProjectsEventThreatDetectionSettingsCustomModulesListDescendantRequest(_messages.Message):
  r"""A SecuritycenterProjectsEventThreatDetectionSettingsCustomModulesListDes
  cendantRequest object.

  Fields:
    pageSize: The maximum number of modules to return. The service may return
      fewer than this value. If unspecified, at most 10 configs will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: A page token, received from a previous
      `ListDescendantEventThreatDetectionCustomModules` call. Provide this to
      retrieve the subsequent page. When paginating, all other parameters
      provided to `ListDescendantEventThreatDetectionCustomModules` must match
      the call that provided the page token.
    parent: Required. Name of the parent to list custom modules under. Its
      format is: *
      "organizations/{organization}/eventThreatDetectionSettings".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterProjectsEventThreatDetectionSettingsEffectiveCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycenterProjectsEventThreatDetectionSettingsEffectiveCustomModul
  esGetRequest object.

  Fields:
    name: Required. The resource name of the effective Event Threat Detection
      custom module. Its format is: * "organizations/{organization}/eventThrea
      tDetectionSettings/effectiveCustomModules/{module}".
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsEventThreatDetectionSettingsEffectiveCustomModulesListRequest(_messages.Message):
  r"""A SecuritycenterProjectsEventThreatDetectionSettingsEffectiveCustomModul
  esListRequest object.

  Fields:
    pageSize: The maximum number of modules to return. The service may return
      fewer than this value. If unspecified, at most 10 configs will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: A page token, received from a previous
      `ListEffectiveEventThreatDetectionCustomModules` call. Provide this to
      retrieve the subsequent page. When paginating, all other parameters
      provided to `ListEffectiveEventThreatDetectionCustomModules` must match
      the call that provided the page token.
    parent: Required. Name of the parent to list custom modules for. Its
      format is: *
      "organizations/{organization}/eventThreatDetectionSettings".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterProjectsFindingsBulkMuteRequest(_messages.Message):
  r"""A SecuritycenterProjectsFindingsBulkMuteRequest object.

  Fields:
    bulkMuteFindingsRequest: A BulkMuteFindingsRequest resource to be passed
      as the request body.
    parent: Required. The parent, at which bulk action needs to be applied.
      Its format is "organizations/[organization_id]", "folders/[folder_id]",
      "projects/[project_id]".
  """

  bulkMuteFindingsRequest = _messages.MessageField('BulkMuteFindingsRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecuritycenterProjectsMuteConfigsCreateRequest(_messages.Message):
  r"""A SecuritycenterProjectsMuteConfigsCreateRequest object.

  Fields:
    googleCloudSecuritycenterV1MuteConfig: A
      GoogleCloudSecuritycenterV1MuteConfig resource to be passed as the
      request body.
    muteConfigId: Required. Unique identifier provided by the client within
      the parent scope. It must consist of only lowercase letters, numbers,
      and hyphens, must start with a letter, must end with either a letter or
      a number, and must be 63 characters or less.
    parent: Required. Resource name of the new mute configs's parent. Its
      format is "organizations/[organization_id]", "folders/[folder_id]", or
      "projects/[project_id]".
  """

  googleCloudSecuritycenterV1MuteConfig = _messages.MessageField('GoogleCloudSecuritycenterV1MuteConfig', 1)
  muteConfigId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterProjectsMuteConfigsDeleteRequest(_messages.Message):
  r"""A SecuritycenterProjectsMuteConfigsDeleteRequest object.

  Fields:
    name: Required. Name of the mute config to delete. Its format is
      organizations/{organization}/muteConfigs/{config_id},
      folders/{folder}/muteConfigs/{config_id}, or
      projects/{project}/muteConfigs/{config_id}
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsMuteConfigsGetRequest(_messages.Message):
  r"""A SecuritycenterProjectsMuteConfigsGetRequest object.

  Fields:
    name: Required. Name of the mute config to retrieve. Its format is
      organizations/{organization}/muteConfigs/{config_id},
      folders/{folder}/muteConfigs/{config_id}, or
      projects/{project}/muteConfigs/{config_id}
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsMuteConfigsListRequest(_messages.Message):
  r"""A SecuritycenterProjectsMuteConfigsListRequest object.

  Fields:
    pageSize: The maximum number of configs to return. The service may return
      fewer than this value. If unspecified, at most 10 configs will be
      returned. The maximum value is 1000; values above 1000 will be coerced
      to 1000.
    pageToken: A page token, received from a previous `ListMuteConfigs` call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to `ListMuteConfigs` must match the call that
      provided the page token.
    parent: Required. The parent, which owns the collection of mute configs.
      Its format is "organizations/[organization_id]", "folders/[folder_id]",
      "projects/[project_id]".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterProjectsMuteConfigsPatchRequest(_messages.Message):
  r"""A SecuritycenterProjectsMuteConfigsPatchRequest object.

  Fields:
    googleCloudSecuritycenterV1MuteConfig: A
      GoogleCloudSecuritycenterV1MuteConfig resource to be passed as the
      request body.
    name: This field will be ignored if provided on config creation. Format
      "organizations/{organization}/muteConfigs/{mute_config}"
      "folders/{folder}/muteConfigs/{mute_config}"
      "projects/{project}/muteConfigs/{mute_config}"
    updateMask: The list of fields to be updated. If empty all mutable fields
      will be updated.
  """

  googleCloudSecuritycenterV1MuteConfig = _messages.MessageField('GoogleCloudSecuritycenterV1MuteConfig', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterProjectsNotificationConfigsCreateRequest(_messages.Message):
  r"""A SecuritycenterProjectsNotificationConfigsCreateRequest object.

  Fields:
    configId: Required. Unique identifier provided by the client within the
      parent scope. It must be between 1 and 128 characters and contain
      alphanumeric characters, underscores, or hyphens only.
    notificationConfig: A NotificationConfig resource to be passed as the
      request body.
    parent: Required. Resource name of the new notification config's parent.
      Its format is "organizations/[organization_id]", "folders/[folder_id]",
      or "projects/[project_id]".
  """

  configId = _messages.StringField(1)
  notificationConfig = _messages.MessageField('NotificationConfig', 2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterProjectsNotificationConfigsDeleteRequest(_messages.Message):
  r"""A SecuritycenterProjectsNotificationConfigsDeleteRequest object.

  Fields:
    name: Required. Name of the notification config to delete. Its format is
      "organizations/[organization_id]/notificationConfigs/[config_id]",
      "folders/[folder_id]/notificationConfigs/[config_id]", or
      "projects/[project_id]/notificationConfigs/[config_id]".
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsNotificationConfigsGetRequest(_messages.Message):
  r"""A SecuritycenterProjectsNotificationConfigsGetRequest object.

  Fields:
    name: Required. Name of the notification config to get. Its format is
      "organizations/[organization_id]/notificationConfigs/[config_id]",
      "folders/[folder_id]/notificationConfigs/[config_id]", or
      "projects/[project_id]/notificationConfigs/[config_id]".
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsNotificationConfigsListRequest(_messages.Message):
  r"""A SecuritycenterProjectsNotificationConfigsListRequest object.

  Fields:
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last
      `ListNotificationConfigsResponse`; indicates that this is a continuation
      of a prior `ListNotificationConfigs` call, and that the system should
      return the next page of data.
    parent: Required. The name of the parent in which to list the notification
      configurations. Its format is "organizations/[organization_id]",
      "folders/[folder_id]", or "projects/[project_id]".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterProjectsNotificationConfigsPatchRequest(_messages.Message):
  r"""A SecuritycenterProjectsNotificationConfigsPatchRequest object.

  Fields:
    name: The relative resource name of this notification config. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Example: "organizations/{organization_id}/notificationConfigs/notify_
      public_bucket",
      "folders/{folder_id}/notificationConfigs/notify_public_bucket", or
      "projects/{project_id}/notificationConfigs/notify_public_bucket".
    notificationConfig: A NotificationConfig resource to be passed as the
      request body.
    updateMask: The FieldMask to use when updating the notification config. If
      empty all mutable fields will be updated.
  """

  name = _messages.StringField(1, required=True)
  notificationConfig = _messages.MessageField('NotificationConfig', 2)
  updateMask = _messages.StringField(3)


class SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesCreateRequest(_messages.Message):
  r"""A SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesCrea
  teRequest object.

  Fields:
    googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule: A
      GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule resource
      to be passed as the request body.
    parent: Required. Resource name of the new custom module's parent. Its
      format is
      "organizations/{organization}/securityHealthAnalyticsSettings",
      "folders/{folder}/securityHealthAnalyticsSettings", or
      "projects/{project}/securityHealthAnalyticsSettings"
  """

  googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule = _messages.MessageField('GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule', 1)
  parent = _messages.StringField(2, required=True)


class SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesDeleteRequest(_messages.Message):
  r"""A SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesDele
  teRequest object.

  Fields:
    name: Required. Name of the custom module to delete. Its format is "organi
      zations/{organization}/securityHealthAnalyticsSettings/customModules/{cu
      stomModule}", "folders/{folder}/securityHealthAnalyticsSettings/customMo
      dules/{customModule}", or "projects/{project}/securityHealthAnalyticsSet
      tings/customModules/{customModule}"
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesGetRequest(_messages.Message):
  r"""A
  SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesGetRequest
  object.

  Fields:
    name: Required. Name of the custom module to get. Its format is "organizat
      ions/{organization}/securityHealthAnalyticsSettings/customModules/{custo
      mModule}", "folders/{folder}/securityHealthAnalyticsSettings/customModul
      es/{customModule}", or "projects/{project}/securityHealthAnalyticsSettin
      gs/customModules/{customModule}"
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesListDescendantRequest(_messages.Message):
  r"""A SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesList
  DescendantRequest object.

  Fields:
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last call indicating a continuation
    parent: Required. Name of parent to list descendant custom modules. Its
      format is
      "organizations/{organization}/securityHealthAnalyticsSettings",
      "folders/{folder}/securityHealthAnalyticsSettings", or
      "projects/{project}/securityHealthAnalyticsSettings"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesListRequest(_messages.Message):
  r"""A SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesList
  Request object.

  Fields:
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last call indicating a continuation
    parent: Required. Name of parent to list custom modules. Its format is
      "organizations/{organization}/securityHealthAnalyticsSettings",
      "folders/{folder}/securityHealthAnalyticsSettings", or
      "projects/{project}/securityHealthAnalyticsSettings"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesPatchRequest(_messages.Message):
  r"""A SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesPatc
  hRequest object.

  Fields:
    googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule: A
      GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule resource
      to be passed as the request body.
    name: Immutable. The resource name of the custom module. Its format is "or
      ganizations/{organization}/securityHealthAnalyticsSettings/customModules
      /{customModule}", or "folders/{folder}/securityHealthAnalyticsSettings/c
      ustomModules/{customModule}", or "projects/{project}/securityHealthAnaly
      ticsSettings/customModules/{customModule}" The id {customModule} is
      server-generated and is not user settable. It will be a numeric id
      containing 1-20 digits.
    updateMask: The list of fields to update.
  """

  googleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule = _messages.MessageField('GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesSimulateRequest(_messages.Message):
  r"""A SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesSimu
  lateRequest object.

  Fields:
    parent: Required. The relative resource name of the organization, project,
      or folder. See: https://cloud.google.com/apis/design/resource_names#rela
      tive_resource_name An example is: "organizations/{organization_id}".
    simulateSecurityHealthAnalyticsCustomModuleRequest: A
      SimulateSecurityHealthAnalyticsCustomModuleRequest resource to be passed
      as the request body.
  """

  parent = _messages.StringField(1, required=True)
  simulateSecurityHealthAnalyticsCustomModuleRequest = _messages.MessageField('SimulateSecurityHealthAnalyticsCustomModuleRequest', 2)


class SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesTestRequest(_messages.Message):
  r"""A SecuritycenterProjectsSecurityHealthAnalyticsSettingsCustomModulesTest
  Request object.

  Fields:
    name: Required. Name of the custom module to test. Its format is "organiza
      tions/[organization_id]/securityHealthAnalyticsSettings/customModules/[m
      odule_id]". If the custom_module field is empty, it is assumed that the
      custom module already exists; otherwise the specified custom_module will
      be used.
    testSecurityHealthAnalyticsCustomModuleRequest: A
      TestSecurityHealthAnalyticsCustomModuleRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  testSecurityHealthAnalyticsCustomModuleRequest = _messages.MessageField('TestSecurityHealthAnalyticsCustomModuleRequest', 2)


class SecuritycenterProjectsSecurityHealthAnalyticsSettingsEffectiveCustomModulesGetRequest(_messages.Message):
  r"""A SecuritycenterProjectsSecurityHealthAnalyticsSettingsEffectiveCustomMo
  dulesGetRequest object.

  Fields:
    name: Required. Name of the effective custom module to get. Its format is
      "organizations/{organization}/securityHealthAnalyticsSettings/effectiveC
      ustomModules/{customModule}", "folders/{folder}/securityHealthAnalyticsS
      ettings/effectiveCustomModules/{customModule}", or "projects/{project}/s
      ecurityHealthAnalyticsSettings/effectiveCustomModules/{customModule}"
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsSecurityHealthAnalyticsSettingsEffectiveCustomModulesListRequest(_messages.Message):
  r"""A SecuritycenterProjectsSecurityHealthAnalyticsSettingsEffectiveCustomMo
  dulesListRequest object.

  Fields:
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last call indicating a continuation
    parent: Required. Name of parent to list effective custom modules. Its
      format is
      "organizations/{organization}/securityHealthAnalyticsSettings",
      "folders/{folder}/securityHealthAnalyticsSettings", or
      "projects/{project}/securityHealthAnalyticsSettings"
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class SecuritycenterProjectsSourcesFindingsExternalSystemsPatchRequest(_messages.Message):
  r"""A SecuritycenterProjectsSourcesFindingsExternalSystemsPatchRequest
  object.

  Fields:
    googleCloudSecuritycenterV1ExternalSystem: A
      GoogleCloudSecuritycenterV1ExternalSystem resource to be passed as the
      request body.
    name: Full resource name of the external system, for example:
      "organizations/1234/sources/5678/findings/123456/externalSystems/jira",
      "folders/1234/sources/5678/findings/123456/externalSystems/jira",
      "projects/1234/sources/5678/findings/123456/externalSystems/jira"
    updateMask: The FieldMask to use when updating the external system
      resource. If empty all mutable fields will be updated.
  """

  googleCloudSecuritycenterV1ExternalSystem = _messages.MessageField('GoogleCloudSecuritycenterV1ExternalSystem', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterProjectsSourcesFindingsGroupRequest(_messages.Message):
  r"""A SecuritycenterProjectsSourcesFindingsGroupRequest object.

  Fields:
    groupFindingsRequest: A GroupFindingsRequest resource to be passed as the
      request body.
    parent: Required. Name of the source to groupBy. Its format is
      "organizations/[organization_id]/sources/[source_id]",
      folders/[folder_id]/sources/[source_id], or
      projects/[project_id]/sources/[source_id]. To groupBy across all sources
      provide a source_id of `-`. For example:
      organizations/{organization_id}/sources/-,
      folders/{folder_id}/sources/-, or projects/{project_id}/sources/-
  """

  groupFindingsRequest = _messages.MessageField('GroupFindingsRequest', 1)
  parent = _messages.StringField(2, required=True)


class SecuritycenterProjectsSourcesFindingsListRequest(_messages.Message):
  r"""A SecuritycenterProjectsSourcesFindingsListRequest object.

  Fields:
    compareDuration: When compare_duration is set, the ListFindingsResult's
      "state_change" attribute is updated to indicate whether the finding had
      its state changed, the finding's state remained unchanged, or if the
      finding was added in any state during the compare_duration period of
      time that precedes the read_time. This is the time between (read_time -
      compare_duration) and read_time. The state_change value is derived based
      on the presence and state of the finding at the two points in time.
      Intermediate state changes between the two times don't affect the
      result. For example, the results aren't affected if the finding is made
      inactive and then active again. Possible "state_change" values when
      compare_duration is specified: * "CHANGED": indicates that the finding
      was present and matched the given filter at the start of
      compare_duration, but changed its state at read_time. * "UNCHANGED":
      indicates that the finding was present and matched the given filter at
      the start of compare_duration and did not change state at read_time. *
      "ADDED": indicates that the finding did not match the given filter or
      was not present at the start of compare_duration, but was present at
      read_time. * "REMOVED": indicates that the finding was present and
      matched the filter at the start of compare_duration, but did not match
      the filter at read_time. If compare_duration is not specified, then the
      only possible state_change is "UNUSED", which will be the state_change
      set for all findings present at read_time.
    fieldMask: A field mask to specify the Finding fields to be listed in the
      response. An empty field mask will list all fields.
    filter: Expression that defines the filter to apply across findings. The
      expression is a list of one or more restrictions combined via logical
      operators `AND` and `OR`. Parentheses are supported, and `OR` has higher
      precedence than `AND`. Restrictions have the form ` ` and may have a `-`
      character in front of them to indicate negation. Examples include: *
      name * source_properties.a_property * security_marks.marks.marka The
      supported operators are: * `=` for all value types. * `>`, `<`, `>=`,
      `<=` for integer values. * `:`, meaning substring matching, for strings.
      The supported value types are: * string literals in quotes. * integer
      literals without quotes. * boolean literals `true` and `false` without
      quotes. The following field and operator combinations are supported: *
      name: `=` * parent: `=`, `:` * resource_name: `=`, `:` * state: `=`, `:`
      * category: `=`, `:` * external_uri: `=`, `:` * event_time: `=`, `>`,
      `<`, `>=`, `<=` Usage: This should be milliseconds since epoch or an
      RFC3339 string. Examples: `event_time = "2019-06-10T16:07:18-07:00"`
      `event_time = 1560208038000` * severity: `=`, `:` * workflow_state: `=`,
      `:` * security_marks.marks: `=`, `:` * source_properties: `=`, `:`, `>`,
      `<`, `>=`, `<=` For example, `source_properties.size = 100` is a valid
      filter string. Use a partial match on the empty string to filter based
      on a property existing: `source_properties.my_property : ""` Use a
      negated partial match on the empty string to filter based on a property
      not existing: `-source_properties.my_property : ""` * resource: *
      resource.name: `=`, `:` * resource.parent_name: `=`, `:` *
      resource.parent_display_name: `=`, `:` * resource.project_name: `=`, `:`
      * resource.project_display_name: `=`, `:` * resource.type: `=`, `:` *
      resource.folders.resource_folder: `=`, `:` * resource.display_name: `=`,
      `:`
    orderBy: Expression that defines what fields and order to use for sorting.
      The string value should follow SQL syntax: comma separated list of
      fields. For example: "name,resource_properties.a_property". The default
      sorting order is ascending. To specify descending order for a field, a
      suffix " desc" should be appended to the field name. For example: "name
      desc,source_properties.a_property". Redundant space characters in the
      syntax are insignificant. "name desc,source_properties.a_property" and "
      name desc , source_properties.a_property " are equivalent. The following
      fields are supported: name parent state category resource_name
      event_time source_properties security_marks.marks
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last `ListFindingsResponse`;
      indicates that this is a continuation of a prior `ListFindings` call,
      and that the system should return the next page of data.
    parent: Required. Name of the source the findings belong to. Its format is
      "organizations/[organization_id]/sources/[source_id],
      folders/[folder_id]/sources/[source_id], or
      projects/[project_id]/sources/[source_id]". To list across all sources
      provide a source_id of `-`. For example:
      organizations/{organization_id}/sources/-, folders/{folder_id}/sources/-
      or projects/{projects_id}/sources/-
    readTime: Time used as a reference point when filtering findings. The
      filter is limited to findings existing at the supplied time and their
      values are those at that specific time. Absence of this field will
      default to the API's version of NOW.
  """

  compareDuration = _messages.StringField(1)
  fieldMask = _messages.StringField(2)
  filter = _messages.StringField(3)
  orderBy = _messages.StringField(4)
  pageSize = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(6)
  parent = _messages.StringField(7, required=True)
  readTime = _messages.StringField(8)


class SecuritycenterProjectsSourcesFindingsPatchRequest(_messages.Message):
  r"""A SecuritycenterProjectsSourcesFindingsPatchRequest object.

  Fields:
    finding: A Finding resource to be passed as the request body.
    name: The [relative resource name](https://cloud.google.com/apis/design/re
      source_names#relative_resource_name) of the finding. Example: "organizat
      ions/{organization_id}/sources/{source_id}/findings/{finding_id}",
      "folders/{folder_id}/sources/{source_id}/findings/{finding_id}",
      "projects/{project_id}/sources/{source_id}/findings/{finding_id}".
    updateMask: The FieldMask to use when updating the finding resource. This
      field should not be specified when creating a finding. When updating a
      finding, an empty mask is treated as updating all mutable fields and
      replacing source_properties. Individual source_properties can be
      added/updated by using "source_properties." in the field mask.
  """

  finding = _messages.MessageField('Finding', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterProjectsSourcesFindingsSetMuteRequest(_messages.Message):
  r"""A SecuritycenterProjectsSourcesFindingsSetMuteRequest object.

  Fields:
    name: Required. The [relative resource name](https://cloud.google.com/apis
      /design/resource_names#relative_resource_name) of the finding. Example:
      "organizations/{organization_id}/sources/{source_id}/findings/{finding_i
      d}", "folders/{folder_id}/sources/{source_id}/findings/{finding_id}",
      "projects/{project_id}/sources/{source_id}/findings/{finding_id}".
    setMuteRequest: A SetMuteRequest resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  setMuteRequest = _messages.MessageField('SetMuteRequest', 2)


class SecuritycenterProjectsSourcesFindingsSetStateRequest(_messages.Message):
  r"""A SecuritycenterProjectsSourcesFindingsSetStateRequest object.

  Fields:
    name: Required. The [relative resource name](https://cloud.google.com/apis
      /design/resource_names#relative_resource_name) of the finding. Example:
      "organizations/{organization_id}/sources/{source_id}/findings/{finding_i
      d}", "folders/{folder_id}/sources/{source_id}/findings/{finding_id}",
      "projects/{project_id}/sources/{source_id}/findings/{finding_id}".
    setFindingStateRequest: A SetFindingStateRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  setFindingStateRequest = _messages.MessageField('SetFindingStateRequest', 2)


class SecuritycenterProjectsSourcesFindingsUpdateSecurityMarksRequest(_messages.Message):
  r"""A SecuritycenterProjectsSourcesFindingsUpdateSecurityMarksRequest
  object.

  Fields:
    name: The relative resource name of the SecurityMarks. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks" "organ
      izations/{organization_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks".
    securityMarks: A SecurityMarks resource to be passed as the request body.
    startTime: The time at which the updated SecurityMarks take effect. If not
      set uses current server time. Updates will be applied to the
      SecurityMarks that are active immediately preceding this time. Must be
      earlier or equal to the server time.
    updateMask: The FieldMask to use when updating the security marks
      resource. The field mask must not contain duplicate fields. If empty or
      set to "marks", all marks will be replaced. Individual marks can be
      updated using "marks.".
  """

  name = _messages.StringField(1, required=True)
  securityMarks = _messages.MessageField('SecurityMarks', 2)
  startTime = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class SecuritycenterProjectsSourcesListRequest(_messages.Message):
  r"""A SecuritycenterProjectsSourcesListRequest object.

  Fields:
    pageSize: The maximum number of results to return in a single response.
      Default is 10, minimum is 1, maximum is 1000.
    pageToken: The value returned by the last `ListSourcesResponse`; indicates
      that this is a continuation of a prior `ListSources` call, and that the
      system should return the next page of data.
    parent: Required. Resource name of the parent of sources to list. Its
      format should be "organizations/[organization_id]",
      "folders/[folder_id]", or "projects/[project_id]".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ServiceAccountDelegationInfo(_messages.Message):
  r"""Identity delegation history of an authenticated service account.

  Fields:
    principalEmail: The email address of a Google account.
    principalSubject: A string representing the principal_subject associated
      with the identity. As compared to `principal_email`, supports principals
      that aren't associated with email addresses, such as third party
      principals. For most identities, the format will be
      `principal://iam.googleapis.com/{identity pool name}/subjects/{subject}`
      except for some GKE identities (GKE_WORKLOAD, FREEFORM,
      GKE_HUB_WORKLOAD) that are still in the legacy format
      `serviceAccount:{identity pool name}[{subject}]`
  """

  principalEmail = _messages.StringField(1)
  principalSubject = _messages.StringField(2)


class SetFindingStateRequest(_messages.Message):
  r"""Request message for updating a finding's state.

  Enums:
    StateValueValuesEnum: Required. The desired State of the finding.

  Fields:
    startTime: Required. The time at which the updated state takes effect.
    state: Required. The desired State of the finding.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Required. The desired State of the finding.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      ACTIVE: The finding requires attention and has not been addressed yet.
      INACTIVE: The finding has been fixed, triaged as a non-issue or
        otherwise addressed and is no longer active.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2

  startTime = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class SetMuteRequest(_messages.Message):
  r"""Request message for updating a finding's mute status.

  Enums:
    MuteValueValuesEnum: Required. The desired state of the Mute.

  Fields:
    mute: Required. The desired state of the Mute.
  """

  class MuteValueValuesEnum(_messages.Enum):
    r"""Required. The desired state of the Mute.

    Values:
      MUTE_UNSPECIFIED: Unspecified.
      MUTED: Finding has been muted.
      UNMUTED: Finding has been unmuted.
      UNDEFINED: Finding has never been muted/unmuted.
    """
    MUTE_UNSPECIFIED = 0
    MUTED = 1
    UNMUTED = 2
    UNDEFINED = 3

  mute = _messages.EnumField('MuteValueValuesEnum', 1)


class SimulateSecurityHealthAnalyticsCustomModuleRequest(_messages.Message):
  r"""Request message to simulate a CustomConfig against a given test
  resource. Maximum size of the request is 4 MB by default.

  Fields:
    customConfig: Required. The user specified custom configuration to test.
    resource: Required. Resource data to simulate custom module against.
  """

  customConfig = _messages.MessageField('GoogleCloudSecuritycenterV1CustomConfig', 1)
  resource = _messages.MessageField('SimulatedResource', 2)


class SimulateSecurityHealthAnalyticsCustomModuleResponse(_messages.Message):
  r"""Response message for simulating a SecurityHealthAnalyticsCustomModule
  against a given resource.

  Fields:
    result: Result for test case in the corresponding request.
  """

  result = _messages.MessageField('SimulatedResult', 1)


class SimulatedResource(_messages.Message):
  r"""Manually constructed resource. If the custom module only evaluates
  against the resource data, the iam_policy_data field can be omitted, and
  vice versa.

  Messages:
    ResourceDataValue: Optional. A representation of the GCP resource. Should
      match the GCP resource JSON format.

  Fields:
    iamPolicyData: Optional. A representation of the IAM policy.
    resourceData: Optional. A representation of the GCP resource. Should match
      the GCP resource JSON format.
    resourceType: Required. The type of the resource, e.g.
      `compute.googleapis.com/Disk`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceDataValue(_messages.Message):
    r"""Optional. A representation of the GCP resource. Should match the GCP
    resource JSON format.

    Messages:
      AdditionalProperty: An additional property for a ResourceDataValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceDataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  iamPolicyData = _messages.MessageField('Policy', 1)
  resourceData = _messages.MessageField('ResourceDataValue', 2)
  resourceType = _messages.StringField(3)


class SimulatedResult(_messages.Message):
  r"""Possible test result.

  Fields:
    error: Error encountered during the test.
    finding: Finding that would be published for the test case, if a violation
      is detected.
    noViolation: Indicates that the test case does not trigger any violation.
  """

  error = _messages.MessageField('Status', 1)
  finding = _messages.MessageField('Finding', 2)
  noViolation = _messages.MessageField('Empty', 3)


class Simulation(_messages.Message):
  r"""Attack path simulation

  Fields:
    createTime: Output only. Time simulation was created
    name: Full resource name of the Simulation:
      organizations/123/simulations/456
    resourceValueConfigsMetadata: Resource value configurations' metadata used
      in this simulation. Maximum of 100.
  """

  createTime = _messages.StringField(1)
  name = _messages.StringField(2)
  resourceValueConfigsMetadata = _messages.MessageField('ResourceValueConfigMetadata', 3, repeated=True)


class Source(_messages.Message):
  r"""Security Command Center finding source. A finding source is an entity or
  a mechanism that can produce a finding. A source is like a container of
  findings that come from the same scanner, logger, monitor, and other tools.

  Fields:
    canonicalName: The canonical name of the finding. It's either
      "organizations/{organization_id}/sources/{source_id}",
      "folders/{folder_id}/sources/{source_id}" or
      "projects/{project_number}/sources/{source_id}", depending on the
      closest CRM ancestor of the resource associated with the finding.
    description: The description of the source (max of 1024 characters).
      Example: "Web Security Scanner is a web security scanner for common
      vulnerabilities in App Engine applications. It can automatically scan
      and detect four common vulnerabilities, including cross-site-scripting
      (XSS), Flash injection, mixed content (HTTP in HTTPS), and outdated or
      insecure libraries."
    displayName: The source's display name. A source's display name must be
      unique amongst its siblings, for example, two sources with the same
      parent can't share the same display name. The display name must have a
      length between 1 and 64 characters (inclusive).
    name: The relative resource name of this source. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Example: "organizations/{organization_id}/sources/{source_id}"
  """

  canonicalName = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  name = _messages.StringField(4)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StreamingConfig(_messages.Message):
  r"""The config for streaming-based notifications, which send each event as
  soon as it is detected.

  Fields:
    filter: Expression that defines the filter to apply across create/update
      events of assets or findings as specified by the event type. The
      expression is a list of zero or more restrictions combined via logical
      operators `AND` and `OR`. Parentheses are supported, and `OR` has higher
      precedence than `AND`. Restrictions have the form ` ` and may have a `-`
      character in front of them to indicate negation. The fields map to those
      defined in the corresponding resource. The supported operators are: *
      `=` for all value types. * `>`, `<`, `>=`, `<=` for integer values. *
      `:`, meaning substring matching, for strings. The supported value types
      are: * string literals in quotes. * integer literals without quotes. *
      boolean literals `true` and `false` without quotes.
  """

  filter = _messages.StringField(1)


class Subject(_messages.Message):
  r"""Represents a Kubernetes subject.

  Enums:
    KindValueValuesEnum: Authentication type for the subject.

  Fields:
    kind: Authentication type for the subject.
    name: Name for the subject.
    ns: Namespace for the subject.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""Authentication type for the subject.

    Values:
      AUTH_TYPE_UNSPECIFIED: Authentication is not specified.
      USER: User with valid certificate.
      SERVICEACCOUNT: Users managed by Kubernetes API with credentials stored
        as secrets.
      GROUP: Collection of users.
    """
    AUTH_TYPE_UNSPECIFIED = 0
    USER = 1
    SERVICEACCOUNT = 2
    GROUP = 3

  kind = _messages.EnumField('KindValueValuesEnum', 1)
  name = _messages.StringField(2)
  ns = _messages.StringField(3)


class TestAsset(_messages.Message):
  r"""Manually constructed resource. If the custom module only evaluates
  against the resource data, the iam_policy_data field can be omitted, and
  vice versa.

  Messages:
    ResourceDataValue: A representation of the resource.

  Fields:
    assetType: The type of the asset, e.g. `compute.googleapis.com/Disk`.
    iamPolicyData: A representation of the IAM policy.
    resource: Pseudo name identifying the resource, must be within 128
      characters.
    resourceData: A representation of the resource.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceDataValue(_messages.Message):
    r"""A representation of the resource.

    Messages:
      AdditionalProperty: An additional property for a ResourceDataValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceDataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  assetType = _messages.StringField(1)
  iamPolicyData = _messages.MessageField('Policy', 2)
  resource = _messages.StringField(3)
  resourceData = _messages.MessageField('ResourceDataValue', 4)


class TestData(_messages.Message):
  r"""Test data used for testing.

  Fields:
    asset: Manually constructed asset data.
    resource: The full name of the resource within the organization to test
      against. Example: "//cloudresourcemanager.googleapis.com/projects/123".
      Both resource data and IAM policy will be available if a valid resource
      name is provided.
  """

  asset = _messages.MessageField('TestAsset', 1)
  resource = _messages.StringField(2)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TestResult(_messages.Message):
  r"""Possible test result.

  Fields:
    error: Error encountered during the test.
    finding: Finding that would be published for the test case, if a violation
      is detected.
    noViolation: Indicates that the test case does not trigger any violation.
  """

  error = _messages.MessageField('Status', 1)
  finding = _messages.MessageField('Finding', 2)
  noViolation = _messages.MessageField('Empty', 3)


class TestSecurityHealthAnalyticsCustomModuleRequest(_messages.Message):
  r"""Request message to test a SecurityHealthAnalyticsCustomModule.

  Fields:
    securityHealthAnalyticsCustomModule: Custom module to test if provided.
      The name will be ignored in favor of the explicitly specified name
      field.
    testData: Resource data to test against.
  """

  securityHealthAnalyticsCustomModule = _messages.MessageField('GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule', 1)
  testData = _messages.MessageField('TestData', 2, repeated=True)


class TestSecurityHealthAnalyticsCustomModuleResponse(_messages.Message):
  r"""Response message for testing a SecurityHealthAnalyticsCustomModule.

  Fields:
    results: Test results for each of the test cases in the corresponding
      request, in the same order.
  """

  results = _messages.MessageField('TestResult', 1, repeated=True)


class ValidateEventThreatDetectionCustomModuleRequest(_messages.Message):
  r"""Request to validate an Event Threat Detection custom module.

  Fields:
    rawText: Required. The raw text of the module's contents. Used to generate
      error messages.
    type: Required. The type of the module (e.g. CONFIGURABLE_BAD_IP).
  """

  rawText = _messages.StringField(1)
  type = _messages.StringField(2)


class ValidateEventThreatDetectionCustomModuleResponse(_messages.Message):
  r"""Response to validating an Event Threat Detection custom module.

  Fields:
    errors: A list of errors returned by the validator. If the list is empty,
      there were no errors.
  """

  errors = _messages.MessageField('CustomModuleValidationErrors', 1)


class ValuedResource(_messages.Message):
  r"""A resource that is determined to have value to a user's system

  Enums:
    ResourceValueValueValuesEnum: How valuable this resource is.

  Fields:
    displayName: Human-readable name of the valued resource.
    exposedScore: Exposed score for this valued resource. A value of 0 means
      no exposure was detected exposure.
    name: Valued resource name, for example, e.g.:
      `organizations/123/simulations/456/valuedResources/789`
    resource: The [full resource name](https://cloud.google.com/apis/design/re
      source_names#full_resource_name) of the valued resource.
    resourceType: The [resource type](https://cloud.google.com/asset-
      inventory/docs/supported-asset-types) of the valued resource.
    resourceValue: How valuable this resource is.
    resourceValueConfigsUsed: List of resource value configurations' metadata
      used to determine the value of this resource. Maximum of 100.
  """

  class ResourceValueValueValuesEnum(_messages.Enum):
    r"""How valuable this resource is.

    Values:
      RESOURCE_VALUE_UNSPECIFIED: The resource value isn't specified.
      RESOURCE_VALUE_LOW: This is a low-value resource.
      RESOURCE_VALUE_MEDIUM: This is a medium-value resource.
      RESOURCE_VALUE_HIGH: This is a high-value resource.
    """
    RESOURCE_VALUE_UNSPECIFIED = 0
    RESOURCE_VALUE_LOW = 1
    RESOURCE_VALUE_MEDIUM = 2
    RESOURCE_VALUE_HIGH = 3

  displayName = _messages.StringField(1)
  exposedScore = _messages.FloatField(2)
  name = _messages.StringField(3)
  resource = _messages.StringField(4)
  resourceType = _messages.StringField(5)
  resourceValue = _messages.EnumField('ResourceValueValueValuesEnum', 6)
  resourceValueConfigsUsed = _messages.MessageField('ResourceValueConfigMetadata', 7, repeated=True)


class Vulnerability(_messages.Message):
  r"""Refers to common vulnerability fields e.g. cve, cvss, cwe etc.

  Fields:
    cve: CVE stands for Common Vulnerabilities and Exposures
      (https://cve.mitre.org/about/)
  """

  cve = _messages.MessageField('Cve', 1)


class YaraRuleSignature(_messages.Message):
  r"""A signature corresponding to a YARA rule.

  Fields:
    yaraRule: The name of the YARA rule.
  """

  yaraRule = _messages.StringField(1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

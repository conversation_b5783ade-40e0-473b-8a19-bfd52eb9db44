"""Generated message classes for securitycenter version v1beta2.

Security Command Center API provides access to temporal views of assets and
findings within an organization.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'securitycenter'


class Access(_messages.Message):
  r"""Represents an access event.

  Fields:
    callerIp: Caller's IP address, such as "*******".
    callerIpGeo: The caller IP's geolocation, which identifies where the call
      came from.
    methodName: The method that the service account called, e.g.
      "SetIamPolicy".
    principalEmail: Associated email, such as "<EMAIL>". The email
      address of the authenticated user or a service account acting on behalf
      of a third party principal making the request. For third party identity
      callers, the `principal_subject` field is populated instead of this
      field. For privacy reasons, the principal email address is sometimes
      redacted. For more information, see [Caller identities in audit
      logs](https://cloud.google.com/logging/docs/audit#user-id).
    principalSubject: A string that represents the principal_subject that is
      associated with the identity. Unlike `principal_email`,
      `principal_subject` supports principals that aren't associated with
      email addresses, such as third party principals. For most identities,
      the format is `principal://iam.googleapis.com/{identity pool
      name}/subject/{subject}`. Some GKE identities, such as GKE_WORKLOAD,
      FREEFORM, and GKE_HUB_WORKLOAD, still use the legacy format
      `serviceAccount:{identity pool name}[{subject}]`.
    serviceAccountDelegationInfo: The identity delegation history of an
      authenticated service account that made the request. The
      `serviceAccountDelegationInfo[]` object contains information about the
      real authorities that try to access Google Cloud resources by delegating
      on a service account. When multiple authorities are present, they are
      guaranteed to be sorted based on the original ordering of the identity
      delegation events.
    serviceAccountKeyName: The name of the service account key that was used
      to create or exchange credentials when authenticating the service
      account that made the request. This is a scheme-less URI full resource
      name. For example: "//iam.googleapis.com/projects/{PROJECT_ID}/serviceAc
      counts/{ACCOUNT}/keys/{key}".
    serviceName: This is the API service that the service account made a call
      to, e.g. "iam.googleapis.com"
    userAgent: The caller's user agent string associated with the finding.
    userAgentFamily: Type of user agent associated with the finding. For
      example, an operating system shell or an embedded or standalone
      application.
    userName: A string that represents a username. The username provided
      depends on the type of the finding and is likely not an IAM principal.
      For example, this can be a system username if the finding is related to
      a virtual machine, or it can be an application login username.
  """

  callerIp = _messages.StringField(1)
  callerIpGeo = _messages.MessageField('Geolocation', 2)
  methodName = _messages.StringField(3)
  principalEmail = _messages.StringField(4)
  principalSubject = _messages.StringField(5)
  serviceAccountDelegationInfo = _messages.MessageField('ServiceAccountDelegationInfo', 6, repeated=True)
  serviceAccountKeyName = _messages.StringField(7)
  serviceName = _messages.StringField(8)
  userAgent = _messages.StringField(9)
  userAgentFamily = _messages.StringField(10)
  userName = _messages.StringField(11)


class AccessReview(_messages.Message):
  r"""Conveys information about a Kubernetes access review (such as one
  returned by a [`kubectl auth
  can-i`](https://kubernetes.io/docs/reference/access-authn-
  authz/authorization/#checking-api-access) command) that was involved in a
  finding.

  Fields:
    group: The API group of the resource. "*" means all.
    name: The name of the resource being requested. Empty means all.
    ns: Namespace of the action being requested. Currently, there is no
      distinction between no namespace and all namespaces. Both are
      represented by "" (empty).
    resource: The optional resource type requested. "*" means all.
    subresource: The optional subresource type.
    verb: A Kubernetes resource API verb, like get, list, watch, create,
      update, delete, proxy. "*" means all.
    version: The API version of the resource. "*" means all.
  """

  group = _messages.StringField(1)
  name = _messages.StringField(2)
  ns = _messages.StringField(3)
  resource = _messages.StringField(4)
  subresource = _messages.StringField(5)
  verb = _messages.StringField(6)
  version = _messages.StringField(7)


class AttackExposure(_messages.Message):
  r"""An attack exposure contains the results of an attack path simulation
  run.

  Enums:
    StateValueValuesEnum: What state this AttackExposure is in. This captures
      whether or not an attack exposure has been calculated or not.

  Fields:
    attackExposureResult: The resource name of the attack path simulation
      result that contains the details regarding this attack exposure score.
      Example: organizations/123/attackExposureResults/456
    exposedHighValueResourcesCount: The number of high value resources that
      are exposed as a result of this finding.
    exposedLowValueResourcesCount: The number of high value resources that are
      exposed as a result of this finding.
    exposedMediumValueResourcesCount: The number of medium value resources
      that are exposed as a result of this finding.
    latestCalculationTime: The most recent time the attack exposure was
      updated on this finding.
    score: A number between 0 (inclusive) and infinity that represents how
      important this finding is to remediate. The higher the score, the more
      important it is to remediate.
    state: What state this AttackExposure is in. This captures whether or not
      an attack exposure has been calculated or not.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""What state this AttackExposure is in. This captures whether or not an
    attack exposure has been calculated or not.

    Values:
      STATE_UNSPECIFIED: The state is not specified.
      CALCULATED: The attack exposure has been calculated.
      NOT_CALCULATED: The attack exposure has not been calculated.
    """
    STATE_UNSPECIFIED = 0
    CALCULATED = 1
    NOT_CALCULATED = 2

  attackExposureResult = _messages.StringField(1)
  exposedHighValueResourcesCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  exposedLowValueResourcesCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  exposedMediumValueResourcesCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  latestCalculationTime = _messages.StringField(5)
  score = _messages.FloatField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)


class CloudDlpDataProfile(_messages.Message):
  r"""The [data profile](https://cloud.google.com/dlp/docs/data-profiles)
  associated with the finding.

  Enums:
    ParentTypeValueValuesEnum: The resource hierarchy level at which the data
      profile was generated.

  Fields:
    dataProfile: Name of the data profile, for example,
      `projects/123/locations/europe/tableProfiles/8383929`.
    parentType: The resource hierarchy level at which the data profile was
      generated.
  """

  class ParentTypeValueValuesEnum(_messages.Enum):
    r"""The resource hierarchy level at which the data profile was generated.

    Values:
      PARENT_TYPE_UNSPECIFIED: Unspecified parent type.
      ORGANIZATION: Organization-level configurations.
      PROJECT: Project-level configurations.
    """
    PARENT_TYPE_UNSPECIFIED = 0
    ORGANIZATION = 1
    PROJECT = 2

  dataProfile = _messages.StringField(1)
  parentType = _messages.EnumField('ParentTypeValueValuesEnum', 2)


class CloudDlpInspection(_messages.Message):
  r"""Details about the Cloud Data Loss Prevention (Cloud DLP) [inspection
  job](https://cloud.google.com/dlp/docs/concepts-job-triggers) that produced
  the finding.

  Fields:
    fullScan: Whether Cloud DLP scanned the complete resource or a sampled
      subset.
    infoType: The type of information (or
      *[infoType](https://cloud.google.com/dlp/docs/infotypes-reference)*)
      found, for example, `EMAIL_ADDRESS` or `STREET_ADDRESS`.
    infoTypeCount: The number of times Cloud DLP found this infoType within
      this job and resource.
    inspectJob: Name of the inspection job, for example,
      `projects/123/locations/europe/dlpJobs/i-8383929`.
  """

  fullScan = _messages.BooleanField(1)
  infoType = _messages.StringField(2)
  infoTypeCount = _messages.IntegerField(3)
  inspectJob = _messages.StringField(4)


class Compliance(_messages.Message):
  r"""Contains compliance information about a security standard indicating
  unmet recommendations.

  Fields:
    ids: Policies within the standard or benchmark, for example, A.12.4.1
    standard: Industry-wide compliance standards or benchmarks, such as CIS,
      PCI, and OWASP.
    version: Version of the standard or benchmark, for example, 1.1
  """

  ids = _messages.StringField(1, repeated=True)
  standard = _messages.StringField(2)
  version = _messages.StringField(3)


class Config(_messages.Message):
  r"""Configuration of a module.

  Enums:
    ModuleEnablementStateValueValuesEnum: The state of enablement for the
      module at its level of the resource hierarchy.

  Messages:
    ValueValue: The configuration value for the module. The absence of this
      field implies its inheritance from the parent.

  Fields:
    moduleEnablementState: The state of enablement for the module at its level
      of the resource hierarchy.
    value: The configuration value for the module. The absence of this field
      implies its inheritance from the parent.
  """

  class ModuleEnablementStateValueValuesEnum(_messages.Enum):
    r"""The state of enablement for the module at its level of the resource
    hierarchy.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ValueValue(_messages.Message):
    r"""The configuration value for the module. The absence of this field
    implies its inheritance from the parent.

    Messages:
      AdditionalProperty: An additional property for a ValueValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ValueValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  moduleEnablementState = _messages.EnumField('ModuleEnablementStateValueValuesEnum', 1)
  value = _messages.MessageField('ValueValue', 2)


class Connection(_messages.Message):
  r"""Contains information about the IP connection associated with the
  finding.

  Enums:
    ProtocolValueValuesEnum: IANA Internet Protocol Number such as TCP(6) and
      UDP(17).

  Fields:
    destinationIp: Destination IP address. Not present for sockets that are
      listening and not connected.
    destinationPort: Destination port. Not present for sockets that are
      listening and not connected.
    protocol: IANA Internet Protocol Number such as TCP(6) and UDP(17).
    sourceIp: Source IP address.
    sourcePort: Source port.
  """

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""IANA Internet Protocol Number such as TCP(6) and UDP(17).

    Values:
      PROTOCOL_UNSPECIFIED: Unspecified protocol (not HOPOPT).
      ICMP: Internet Control Message Protocol.
      TCP: Transmission Control Protocol.
      UDP: User Datagram Protocol.
      GRE: Generic Routing Encapsulation.
      ESP: Encap Security Payload.
    """
    PROTOCOL_UNSPECIFIED = 0
    ICMP = 1
    TCP = 2
    UDP = 3
    GRE = 4
    ESP = 5

  destinationIp = _messages.StringField(1)
  destinationPort = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 3)
  sourceIp = _messages.StringField(4)
  sourcePort = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class Contact(_messages.Message):
  r"""The email address of a contact.

  Fields:
    email: An email address. For example, "`<EMAIL>`".
  """

  email = _messages.StringField(1)


class ContactDetails(_messages.Message):
  r"""Details about specific contacts

  Fields:
    contacts: A list of contacts
  """

  contacts = _messages.MessageField('Contact', 1, repeated=True)


class Container(_messages.Message):
  r"""Container associated with the finding.

  Fields:
    createTime: The time that the container was created.
    imageId: Optional container image ID, if provided by the container
      runtime. Uniquely identifies the container image launched using a
      container image digest.
    labels: Container labels, as provided by the container runtime.
    name: Name of the container.
    uri: Container image URI provided when configuring a pod or container.
      This string can identify a container image version using mutable tags.
  """

  createTime = _messages.StringField(1)
  imageId = _messages.StringField(2)
  labels = _messages.MessageField('Label', 3, repeated=True)
  name = _messages.StringField(4)
  uri = _messages.StringField(5)


class ContainerThreatDetectionSettings(_messages.Message):
  r"""Resource capturing the settings for the Container Threat Detection
  service.

  Enums:
    ServiceEnablementStateValueValuesEnum: The state of enablement for the
      service at its level of the resource hierarchy. A DISABLED state will
      override all module enablement_states to DISABLED.

  Messages:
    ModulesValue: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's.

  Fields:
    modules: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's.
    name: The resource name of the ContainerThreatDetectionSettings. Formats:
      * organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
    serviceAccount: Output only. The service account used by Container Threat
      Detection for scanning. Service accounts are scoped at the project level
      meaning this field will be empty at any level above a project.
    serviceEnablementState: The state of enablement for the service at its
      level of the resource hierarchy. A DISABLED state will override all
      module enablement_states to DISABLED.
    updateTime: Output only. The time the settings were last updated.
  """

  class ServiceEnablementStateValueValuesEnum(_messages.Enum):
    r"""The state of enablement for the service at its level of the resource
    hierarchy. A DISABLED state will override all module enablement_states to
    DISABLED.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ModulesValue(_messages.Message):
    r"""The configurations including the state of enablement for the service's
    different modules. The absence of a module in the map implies its
    configuration is inherited from its parent's.

    Messages:
      AdditionalProperty: An additional property for a ModulesValue object.

    Fields:
      additionalProperties: Additional properties of type ModulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ModulesValue object.

      Fields:
        key: Name of the additional property.
        value: A Config attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Config', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  modules = _messages.MessageField('ModulesValue', 1)
  name = _messages.StringField(2)
  serviceAccount = _messages.StringField(3)
  serviceEnablementState = _messages.EnumField('ServiceEnablementStateValueValuesEnum', 4)
  updateTime = _messages.StringField(5)


class Cve(_messages.Message):
  r"""CVE stands for Common Vulnerabilities and Exposures. More information:
  https://cve.mitre.org

  Fields:
    cvssv3: Describe Common Vulnerability Scoring System specified at
      https://www.first.org/cvss/v3.1/specification-document
    id: The unique identifier for the vulnerability. e.g. CVE-2021-34527
    references: Additional information about the CVE. e.g.
      https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2021-34527
    upstreamFixAvailable: Whether upstream fix is available for the CVE.
  """

  cvssv3 = _messages.MessageField('Cvssv3', 1)
  id = _messages.StringField(2)
  references = _messages.MessageField('Reference', 3, repeated=True)
  upstreamFixAvailable = _messages.BooleanField(4)


class Cvssv3(_messages.Message):
  r"""Common Vulnerability Scoring System version 3.

  Enums:
    AttackComplexityValueValuesEnum: This metric describes the conditions
      beyond the attacker's control that must exist in order to exploit the
      vulnerability.
    AttackVectorValueValuesEnum: Base Metrics Represents the intrinsic
      characteristics of a vulnerability that are constant over time and
      across user environments. This metric reflects the context by which
      vulnerability exploitation is possible.
    AvailabilityImpactValueValuesEnum: This metric measures the impact to the
      availability of the impacted component resulting from a successfully
      exploited vulnerability.
    ConfidentialityImpactValueValuesEnum: This metric measures the impact to
      the confidentiality of the information resources managed by a software
      component due to a successfully exploited vulnerability.
    IntegrityImpactValueValuesEnum: This metric measures the impact to
      integrity of a successfully exploited vulnerability.
    PrivilegesRequiredValueValuesEnum: This metric describes the level of
      privileges an attacker must possess before successfully exploiting the
      vulnerability.
    ScopeValueValuesEnum: The Scope metric captures whether a vulnerability in
      one vulnerable component impacts resources in components beyond its
      security scope.
    UserInteractionValueValuesEnum: This metric captures the requirement for a
      human user, other than the attacker, to participate in the successful
      compromise of the vulnerable component.

  Fields:
    attackComplexity: This metric describes the conditions beyond the
      attacker's control that must exist in order to exploit the
      vulnerability.
    attackVector: Base Metrics Represents the intrinsic characteristics of a
      vulnerability that are constant over time and across user environments.
      This metric reflects the context by which vulnerability exploitation is
      possible.
    availabilityImpact: This metric measures the impact to the availability of
      the impacted component resulting from a successfully exploited
      vulnerability.
    baseScore: The base score is a function of the base metric scores.
    confidentialityImpact: This metric measures the impact to the
      confidentiality of the information resources managed by a software
      component due to a successfully exploited vulnerability.
    integrityImpact: This metric measures the impact to integrity of a
      successfully exploited vulnerability.
    privilegesRequired: This metric describes the level of privileges an
      attacker must possess before successfully exploiting the vulnerability.
    scope: The Scope metric captures whether a vulnerability in one vulnerable
      component impacts resources in components beyond its security scope.
    userInteraction: This metric captures the requirement for a human user,
      other than the attacker, to participate in the successful compromise of
      the vulnerable component.
  """

  class AttackComplexityValueValuesEnum(_messages.Enum):
    r"""This metric describes the conditions beyond the attacker's control
    that must exist in order to exploit the vulnerability.

    Values:
      ATTACK_COMPLEXITY_UNSPECIFIED: Invalid value.
      ATTACK_COMPLEXITY_LOW: Specialized access conditions or extenuating
        circumstances do not exist. An attacker can expect repeatable success
        when attacking the vulnerable component.
      ATTACK_COMPLEXITY_HIGH: A successful attack depends on conditions beyond
        the attacker's control. That is, a successful attack cannot be
        accomplished at will, but requires the attacker to invest in some
        measurable amount of effort in preparation or execution against the
        vulnerable component before a successful attack can be expected.
    """
    ATTACK_COMPLEXITY_UNSPECIFIED = 0
    ATTACK_COMPLEXITY_LOW = 1
    ATTACK_COMPLEXITY_HIGH = 2

  class AttackVectorValueValuesEnum(_messages.Enum):
    r"""Base Metrics Represents the intrinsic characteristics of a
    vulnerability that are constant over time and across user environments.
    This metric reflects the context by which vulnerability exploitation is
    possible.

    Values:
      ATTACK_VECTOR_UNSPECIFIED: Invalid value.
      ATTACK_VECTOR_NETWORK: The vulnerable component is bound to the network
        stack and the set of possible attackers extends beyond the other
        options listed below, up to and including the entire Internet.
      ATTACK_VECTOR_ADJACENT: The vulnerable component is bound to the network
        stack, but the attack is limited at the protocol level to a logically
        adjacent topology.
      ATTACK_VECTOR_LOCAL: The vulnerable component is not bound to the
        network stack and the attacker's path is via read/write/execute
        capabilities.
      ATTACK_VECTOR_PHYSICAL: The attack requires the attacker to physically
        touch or manipulate the vulnerable component.
    """
    ATTACK_VECTOR_UNSPECIFIED = 0
    ATTACK_VECTOR_NETWORK = 1
    ATTACK_VECTOR_ADJACENT = 2
    ATTACK_VECTOR_LOCAL = 3
    ATTACK_VECTOR_PHYSICAL = 4

  class AvailabilityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to the availability of the impacted
    component resulting from a successfully exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class ConfidentialityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to the confidentiality of the
    information resources managed by a software component due to a
    successfully exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class IntegrityImpactValueValuesEnum(_messages.Enum):
    r"""This metric measures the impact to integrity of a successfully
    exploited vulnerability.

    Values:
      IMPACT_UNSPECIFIED: Invalid value.
      IMPACT_HIGH: High impact.
      IMPACT_LOW: Low impact.
      IMPACT_NONE: No impact.
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3

  class PrivilegesRequiredValueValuesEnum(_messages.Enum):
    r"""This metric describes the level of privileges an attacker must possess
    before successfully exploiting the vulnerability.

    Values:
      PRIVILEGES_REQUIRED_UNSPECIFIED: Invalid value.
      PRIVILEGES_REQUIRED_NONE: The attacker is unauthorized prior to attack,
        and therefore does not require any access to settings or files of the
        vulnerable system to carry out an attack.
      PRIVILEGES_REQUIRED_LOW: The attacker requires privileges that provide
        basic user capabilities that could normally affect only settings and
        files owned by a user. Alternatively, an attacker with Low privileges
        has the ability to access only non-sensitive resources.
      PRIVILEGES_REQUIRED_HIGH: The attacker requires privileges that provide
        significant (e.g., administrative) control over the vulnerable
        component allowing access to component-wide settings and files.
    """
    PRIVILEGES_REQUIRED_UNSPECIFIED = 0
    PRIVILEGES_REQUIRED_NONE = 1
    PRIVILEGES_REQUIRED_LOW = 2
    PRIVILEGES_REQUIRED_HIGH = 3

  class ScopeValueValuesEnum(_messages.Enum):
    r"""The Scope metric captures whether a vulnerability in one vulnerable
    component impacts resources in components beyond its security scope.

    Values:
      SCOPE_UNSPECIFIED: Invalid value.
      SCOPE_UNCHANGED: An exploited vulnerability can only affect resources
        managed by the same security authority.
      SCOPE_CHANGED: An exploited vulnerability can affect resources beyond
        the security scope managed by the security authority of the vulnerable
        component.
    """
    SCOPE_UNSPECIFIED = 0
    SCOPE_UNCHANGED = 1
    SCOPE_CHANGED = 2

  class UserInteractionValueValuesEnum(_messages.Enum):
    r"""This metric captures the requirement for a human user, other than the
    attacker, to participate in the successful compromise of the vulnerable
    component.

    Values:
      USER_INTERACTION_UNSPECIFIED: Invalid value.
      USER_INTERACTION_NONE: The vulnerable system can be exploited without
        interaction from any user.
      USER_INTERACTION_REQUIRED: Successful exploitation of this vulnerability
        requires a user to take some action before the vulnerability can be
        exploited.
    """
    USER_INTERACTION_UNSPECIFIED = 0
    USER_INTERACTION_NONE = 1
    USER_INTERACTION_REQUIRED = 2

  attackComplexity = _messages.EnumField('AttackComplexityValueValuesEnum', 1)
  attackVector = _messages.EnumField('AttackVectorValueValuesEnum', 2)
  availabilityImpact = _messages.EnumField('AvailabilityImpactValueValuesEnum', 3)
  baseScore = _messages.FloatField(4)
  confidentialityImpact = _messages.EnumField('ConfidentialityImpactValueValuesEnum', 5)
  integrityImpact = _messages.EnumField('IntegrityImpactValueValuesEnum', 6)
  privilegesRequired = _messages.EnumField('PrivilegesRequiredValueValuesEnum', 7)
  scope = _messages.EnumField('ScopeValueValuesEnum', 8)
  userInteraction = _messages.EnumField('UserInteractionValueValuesEnum', 9)


class Database(_messages.Message):
  r"""Represents database access information, such as queries. A database may
  be a sub-resource of an instance (as in the case of Cloud SQL instances or
  Cloud Spanner instances), or the database instance itself. Some database
  resources might not have the [full resource
  name](https://google.aip.dev/122#full-resource-names) populated because
  these resource types, such as Cloud SQL databases, are not yet supported by
  Cloud Asset Inventory. In these cases only the display name is provided.

  Fields:
    displayName: The human-readable name of the database that the user
      connected to.
    grantees: The target usernames, roles, or groups of an SQL privilege
      grant, which is not an IAM policy change.
    name: Some database resources may not have the [full resource
      name](https://google.aip.dev/122#full-resource-names) populated because
      these resource types are not yet supported by Cloud Asset Inventory
      (e.g. Cloud SQL databases). In these cases only the display name will be
      provided. The [full resource name](https://google.aip.dev/122#full-
      resource-names) of the database that the user connected to, if it is
      supported by Cloud Asset Inventory.
    query: The SQL statement that is associated with the database access.
    userName: The username used to connect to the database. The username might
      not be an IAM principal and does not have a set format.
    version: The version of the database, for example, POSTGRES_14. See [the
      complete list](https://cloud.google.com/sql/docs/mysql/admin-
      api/rest/v1/SqlDatabaseVersion).
  """

  displayName = _messages.StringField(1)
  grantees = _messages.StringField(2, repeated=True)
  name = _messages.StringField(3)
  query = _messages.StringField(4)
  userName = _messages.StringField(5)
  version = _messages.StringField(6)


class Details(_messages.Message):
  r"""Details of a subscription.

  Enums:
    TypeValueValuesEnum: The type of subscription

  Fields:
    endTime: The time the subscription has or will end.
    startTime: The time the subscription has or will start.
    type: The type of subscription
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of subscription

    Values:
      TYPE_UNSPECIFIED: Default value. This value is unused.
      STANDARD: The standard subscription.
      TRIAL: The trial subscription.
      ALPHA: The alpha subscription.
      DEMO: The demo subscription for channel partners.
      PAY_AS_YOU_GO: Pay-as-you-go subscription.
    """
    TYPE_UNSPECIFIED = 0
    STANDARD = 1
    TRIAL = 2
    ALPHA = 3
    DEMO = 4
    PAY_AS_YOU_GO = 5

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class Detection(_messages.Message):
  r"""Memory hash detection contributing to the binary family match.

  Fields:
    binary: The name of the binary associated with the memory hash signature
      detection.
    percentPagesMatched: The percentage of memory page hashes in the signature
      that were matched.
  """

  binary = _messages.StringField(1)
  percentPagesMatched = _messages.FloatField(2)


class EnvironmentVariable(_messages.Message):
  r"""A name-value pair representing an environment variable used in an
  operating system process.

  Fields:
    name: Environment variable name as a JSON encoded string.
    val: Environment variable value as a JSON encoded string.
  """

  name = _messages.StringField(1)
  val = _messages.StringField(2)


class EventThreatDetectionSettings(_messages.Message):
  r"""Resource capturing the settings for the Event Threat Detection service.

  Enums:
    ServiceEnablementStateValueValuesEnum: The state of enablement for the
      service at its level of the resource hierarchy. A DISABLED state will
      override all module enablement_states to DISABLED.

  Messages:
    ModulesValue: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's.

  Fields:
    modules: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's.
    name: The resource name of the EventThreatDetectionSettings. Formats: *
      organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
    serviceEnablementState: The state of enablement for the service at its
      level of the resource hierarchy. A DISABLED state will override all
      module enablement_states to DISABLED.
    updateTime: Output only. The time the settings were last updated.
  """

  class ServiceEnablementStateValueValuesEnum(_messages.Enum):
    r"""The state of enablement for the service at its level of the resource
    hierarchy. A DISABLED state will override all module enablement_states to
    DISABLED.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ModulesValue(_messages.Message):
    r"""The configurations including the state of enablement for the service's
    different modules. The absence of a module in the map implies its
    configuration is inherited from its parent's.

    Messages:
      AdditionalProperty: An additional property for a ModulesValue object.

    Fields:
      additionalProperties: Additional properties of type ModulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ModulesValue object.

      Fields:
        key: Name of the additional property.
        value: A Config attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Config', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  modules = _messages.MessageField('ModulesValue', 1)
  name = _messages.StringField(2)
  serviceEnablementState = _messages.EnumField('ServiceEnablementStateValueValuesEnum', 3)
  updateTime = _messages.StringField(4)


class ExfilResource(_messages.Message):
  r"""Resource where data was exfiltrated from or exfiltrated to.

  Fields:
    components: Subcomponents of the asset that was exfiltrated, like URIs
      used during exfiltration, table names, databases, and filenames. For
      example, multiple tables might have been exfiltrated from the same Cloud
      SQL instance, or multiple files might have been exfiltrated from the
      same Cloud Storage bucket.
    name: The resource's [full resource name](https://cloud.google.com/apis/de
      sign/resource_names#full_resource_name).
  """

  components = _messages.StringField(1, repeated=True)
  name = _messages.StringField(2)


class Exfiltration(_messages.Message):
  r"""Exfiltration represents a data exfiltration attempt from one or more
  sources to one or more targets. The `sources` attribute lists the sources of
  the exfiltrated data. The `targets` attribute lists the destinations the
  data was copied to.

  Fields:
    sources: If there are multiple sources, then the data is considered
      "joined" between them. For instance, BigQuery can join multiple tables,
      and each table would be considered a source.
    targets: If there are multiple targets, each target would get a complete
      copy of the "joined" source data.
  """

  sources = _messages.MessageField('ExfilResource', 1, repeated=True)
  targets = _messages.MessageField('ExfilResource', 2, repeated=True)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class File(_messages.Message):
  r"""File information about the related binary/library used by an executable,
  or the script used by a script interpreter

  Fields:
    contents: Prefix of the file contents as a JSON-encoded string.
    hashedSize: The length in bytes of the file prefix that was hashed. If
      hashed_size == size, any hashes reported represent the entire file.
    partiallyHashed: True when the hash covers only a prefix of the file.
    path: Absolute path of the file as a JSON encoded string.
    sha256: SHA256 hash of the first hashed_size bytes of the file encoded as
      a hex string. If hashed_size == size, sha256 represents the SHA256 hash
      of the entire file.
    size: Size of the file in bytes.
  """

  contents = _messages.StringField(1)
  hashedSize = _messages.IntegerField(2)
  partiallyHashed = _messages.BooleanField(3)
  path = _messages.StringField(4)
  sha256 = _messages.StringField(5)
  size = _messages.IntegerField(6)


class Finding(_messages.Message):
  r"""Security Command Center finding. A finding is a record of assessment
  data like security, risk, health, or privacy, that is ingested into Security
  Command Center for presentation, notification, analysis, policy testing, and
  enforcement. For example, a cross-site scripting (XSS) vulnerability in an
  App Engine application is a finding.

  Enums:
    FindingClassValueValuesEnum: The class of the finding.
    MuteValueValuesEnum: Indicates the mute state of a finding (either muted,
      unmuted or undefined). Unlike other attributes of a finding, a finding
      provider shouldn't set the value of mute.
    SeverityValueValuesEnum: The severity of the finding. This field is
      managed by the source that writes the finding.
    StateValueValuesEnum: The state of the finding.

  Messages:
    ContactsValue: Output only. Map containing the points of contact for the
      given finding. The key represents the type of contact, while the value
      contains a list of all the contacts that pertain. Please refer to:
      https://cloud.google.com/resource-manager/docs/managing-notification-
      contacts#notification-categories { "security": { "contacts": [ {
      "email": "<EMAIL>" }, { "email": "<EMAIL>" } ] }
      }
    ExternalSystemsValue: Output only. Third party SIEM/SOAR fields within
      SCC, contains external system information and external system finding
      fields.
    SourcePropertiesValue: Source specific properties. These properties are
      managed by the source that writes the finding. The key names in the
      source_properties map must be between 1 and 255 characters, and must
      start with a letter and contain alphanumeric characters or underscores
      only.

  Fields:
    access: Access details associated with the finding, such as more
      information on the caller, which method was accessed, and from where.
    attackExposure: The results of an attack path simulation relevant to this
      finding.
    canonicalName: The canonical name of the finding. It's either "organizatio
      ns/{organization_id}/sources/{source_id}/findings/{finding_id}",
      "folders/{folder_id}/sources/{source_id}/findings/{finding_id}" or
      "projects/{project_number}/sources/{source_id}/findings/{finding_id}",
      depending on the closest CRM ancestor of the resource associated with
      the finding.
    category: The additional taxonomy group within findings from a given
      source. This field is immutable after creation time. Example:
      "XSS_FLASH_INJECTION"
    cloudDlpDataProfile: Cloud DLP data profile that is associated with the
      finding.
    cloudDlpInspection: Cloud Data Loss Prevention (Cloud DLP) inspection
      results that are associated with the finding.
    compliances: Contains compliance information for security standards
      associated to the finding.
    connections: Contains information about the IP connection associated with
      the finding.
    contacts: Output only. Map containing the points of contact for the given
      finding. The key represents the type of contact, while the value
      contains a list of all the contacts that pertain. Please refer to:
      https://cloud.google.com/resource-manager/docs/managing-notification-
      contacts#notification-categories { "security": { "contacts": [ {
      "email": "<EMAIL>" }, { "email": "<EMAIL>" } ] }
      }
    containers: Containers associated with the finding. This field provides
      information for both Kubernetes and non-Kubernetes containers.
    createTime: The time at which the finding was created in Security Command
      Center.
    database: Database associated with the finding.
    description: Contains more details about the finding.
    eventTime: The time the finding was first detected. If an existing finding
      is updated, then this is the time the update occurred. For example, if
      the finding represents an open firewall, this property captures the time
      the detector believes the firewall became open. The accuracy is
      determined by the detector. If the finding is later resolved, then this
      time reflects when the finding was resolved. This must not be set to a
      value greater than the current timestamp.
    exfiltration: Represents exfiltrations associated with the finding.
    externalSystems: Output only. Third party SIEM/SOAR fields within SCC,
      contains external system information and external system finding fields.
    externalUri: The URI that, if available, points to a web page outside of
      Security Command Center where additional information about the finding
      can be found. This field is guaranteed to be either empty or a well
      formed URL.
    files: File associated with the finding.
    findingClass: The class of the finding.
    iamBindings: Represents IAM bindings associated with the finding.
    indicator: Represents what's commonly known as an *indicator of
      compromise* (IoC) in computer forensics. This is an artifact observed on
      a network or in an operating system that, with high confidence,
      indicates a computer intrusion. For more information, see [Indicator of
      compromise](https://en.wikipedia.org/wiki/Indicator_of_compromise).
    kernelRootkit: Signature of the kernel rootkit.
    kubernetes: Kubernetes resources associated with the finding.
    mitreAttack: MITRE ATT&CK tactics and techniques related to this finding.
      See: https://attack.mitre.org
    moduleName: Unique identifier of the module which generated the finding.
      Example: folders/598186756061/securityHealthAnalyticsSettings/customModu
      les/56799441161885
    mute: Indicates the mute state of a finding (either muted, unmuted or
      undefined). Unlike other attributes of a finding, a finding provider
      shouldn't set the value of mute.
    muteInitiator: Records additional information about the mute operation,
      for example, the [mute configuration](/security-command-center/docs/how-
      to-mute-findings) that muted the finding and the user who muted the
      finding.
    muteUpdateTime: Output only. The most recent time this finding was muted
      or unmuted.
    name: The [relative resource name](https://cloud.google.com/apis/design/re
      source_names#relative_resource_name) of the finding. Example: "organizat
      ions/{organization_id}/sources/{source_id}/findings/{finding_id}",
      "folders/{folder_id}/sources/{source_id}/findings/{finding_id}",
      "projects/{project_id}/sources/{source_id}/findings/{finding_id}".
    nextSteps: Steps to address the finding.
    parent: The relative resource name of the source the finding belongs to.
      See: https://cloud.google.com/apis/design/resource_names#relative_resour
      ce_name This field is immutable after creation time. For example:
      "organizations/{organization_id}/sources/{source_id}"
    parentDisplayName: Output only. The human readable display name of the
      finding source such as "Event Threat Detection" or "Security Health
      Analytics".
    processes: Represents operating system processes associated with the
      Finding.
    resourceName: For findings on Google Cloud resources, the full resource
      name of the Google Cloud resource this finding is for. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
      When the finding is for a non-Google Cloud resource, the resourceName
      can be a customer or partner defined string. This field is immutable
      after creation time.
    securityMarks: Output only. User specified security marks. These marks are
      entirely managed by the user and come from the SecurityMarks resource
      that belongs to the finding.
    severity: The severity of the finding. This field is managed by the source
      that writes the finding.
    sourceProperties: Source specific properties. These properties are managed
      by the source that writes the finding. The key names in the
      source_properties map must be between 1 and 255 characters, and must
      start with a letter and contain alphanumeric characters or underscores
      only.
    state: The state of the finding.
    vulnerability: Represents vulnerability-specific fields like CVE and CVSS
      scores. CVE stands for Common Vulnerabilities and Exposures
      (https://cve.mitre.org/about/)
  """

  class FindingClassValueValuesEnum(_messages.Enum):
    r"""The class of the finding.

    Values:
      FINDING_CLASS_UNSPECIFIED: Unspecified finding class.
      THREAT: Describes unwanted or malicious activity.
      VULNERABILITY: Describes a potential weakness in software that increases
        risk to Confidentiality & Integrity & Availability.
      MISCONFIGURATION: Describes a potential weakness in cloud resource/asset
        configuration that increases risk.
      OBSERVATION: Describes a security observation that is for informational
        purposes.
      SCC_ERROR: Describes an error that prevents some SCC functionality.
    """
    FINDING_CLASS_UNSPECIFIED = 0
    THREAT = 1
    VULNERABILITY = 2
    MISCONFIGURATION = 3
    OBSERVATION = 4
    SCC_ERROR = 5

  class MuteValueValuesEnum(_messages.Enum):
    r"""Indicates the mute state of a finding (either muted, unmuted or
    undefined). Unlike other attributes of a finding, a finding provider
    shouldn't set the value of mute.

    Values:
      MUTE_UNSPECIFIED: Unspecified.
      MUTED: Finding has been muted.
      UNMUTED: Finding has been unmuted.
      UNDEFINED: Finding has never been muted/unmuted.
    """
    MUTE_UNSPECIFIED = 0
    MUTED = 1
    UNMUTED = 2
    UNDEFINED = 3

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity of the finding. This field is managed by the source that
    writes the finding.

    Values:
      SEVERITY_UNSPECIFIED: This value is used for findings when a source
        doesn't write a severity value.
      CRITICAL: Vulnerability: A critical vulnerability is easily discoverable
        by an external actor, exploitable, and results in the direct ability
        to execute arbitrary code, exfiltrate data, and otherwise gain
        additional access and privileges to cloud resources and workloads.
        Examples include publicly accessible unprotected user data and public
        SSH access with weak or no passwords. Threat: Indicates a threat that
        is able to access, modify, or delete data or execute unauthorized code
        within existing resources.
      HIGH: Vulnerability: A high risk vulnerability can be easily discovered
        and exploited in combination with other vulnerabilities in order to
        gain direct access and the ability to execute arbitrary code,
        exfiltrate data, and otherwise gain additional access and privileges
        to cloud resources and workloads. An example is a database with weak
        or no passwords that is only accessible internally. This database
        could easily be compromised by an actor that had access to the
        internal network. Threat: Indicates a threat that is able to create
        new computational resources in an environment but not able to access
        data or execute code in existing resources.
      MEDIUM: Vulnerability: A medium risk vulnerability could be used by an
        actor to gain access to resources or privileges that enable them to
        eventually (through multiple steps or a complex exploit) gain access
        and the ability to execute arbitrary code or exfiltrate data. An
        example is a service account with access to more projects than it
        should have. If an actor gains access to the service account, they
        could potentially use that access to manipulate a project the service
        account was not intended to. Threat: Indicates a threat that is able
        to cause operational impact but may not access data or execute
        unauthorized code.
      LOW: Vulnerability: A low risk vulnerability hampers a security
        organization's ability to detect vulnerabilities or active threats in
        their deployment, or prevents the root cause investigation of security
        issues. An example is monitoring and logs being disabled for resource
        configurations and access. Threat: Indicates a threat that has
        obtained minimal access to an environment but is not able to access
        data, execute code, or create resources.
    """
    SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the finding.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      ACTIVE: The finding requires attention and has not been addressed yet.
      INACTIVE: The finding has been fixed, triaged as a non-issue or
        otherwise addressed and is no longer active.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ContactsValue(_messages.Message):
    r"""Output only. Map containing the points of contact for the given
    finding. The key represents the type of contact, while the value contains
    a list of all the contacts that pertain. Please refer to:
    https://cloud.google.com/resource-manager/docs/managing-notification-
    contacts#notification-categories { "security": { "contacts": [ { "email":
    "<EMAIL>" }, { "email": "<EMAIL>" } ] } }

    Messages:
      AdditionalProperty: An additional property for a ContactsValue object.

    Fields:
      additionalProperties: Additional properties of type ContactsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ContactsValue object.

      Fields:
        key: Name of the additional property.
        value: A ContactDetails attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ContactDetails', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExternalSystemsValue(_messages.Message):
    r"""Output only. Third party SIEM/SOAR fields within SCC, contains
    external system information and external system finding fields.

    Messages:
      AdditionalProperty: An additional property for a ExternalSystemsValue
        object.

    Fields:
      additionalProperties: Additional properties of type ExternalSystemsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExternalSystemsValue object.

      Fields:
        key: Name of the additional property.
        value: A GoogleCloudSecuritycenterV1ExternalSystem attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('GoogleCloudSecuritycenterV1ExternalSystem', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SourcePropertiesValue(_messages.Message):
    r"""Source specific properties. These properties are managed by the source
    that writes the finding. The key names in the source_properties map must
    be between 1 and 255 characters, and must start with a letter and contain
    alphanumeric characters or underscores only.

    Messages:
      AdditionalProperty: An additional property for a SourcePropertiesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        SourcePropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SourcePropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  access = _messages.MessageField('Access', 1)
  attackExposure = _messages.MessageField('AttackExposure', 2)
  canonicalName = _messages.StringField(3)
  category = _messages.StringField(4)
  cloudDlpDataProfile = _messages.MessageField('CloudDlpDataProfile', 5)
  cloudDlpInspection = _messages.MessageField('CloudDlpInspection', 6)
  compliances = _messages.MessageField('Compliance', 7, repeated=True)
  connections = _messages.MessageField('Connection', 8, repeated=True)
  contacts = _messages.MessageField('ContactsValue', 9)
  containers = _messages.MessageField('Container', 10, repeated=True)
  createTime = _messages.StringField(11)
  database = _messages.MessageField('Database', 12)
  description = _messages.StringField(13)
  eventTime = _messages.StringField(14)
  exfiltration = _messages.MessageField('Exfiltration', 15)
  externalSystems = _messages.MessageField('ExternalSystemsValue', 16)
  externalUri = _messages.StringField(17)
  files = _messages.MessageField('File', 18, repeated=True)
  findingClass = _messages.EnumField('FindingClassValueValuesEnum', 19)
  iamBindings = _messages.MessageField('IamBinding', 20, repeated=True)
  indicator = _messages.MessageField('Indicator', 21)
  kernelRootkit = _messages.MessageField('KernelRootkit', 22)
  kubernetes = _messages.MessageField('Kubernetes', 23)
  mitreAttack = _messages.MessageField('MitreAttack', 24)
  moduleName = _messages.StringField(25)
  mute = _messages.EnumField('MuteValueValuesEnum', 26)
  muteInitiator = _messages.StringField(27)
  muteUpdateTime = _messages.StringField(28)
  name = _messages.StringField(29)
  nextSteps = _messages.StringField(30)
  parent = _messages.StringField(31)
  parentDisplayName = _messages.StringField(32)
  processes = _messages.MessageField('Process', 33, repeated=True)
  resourceName = _messages.StringField(34)
  securityMarks = _messages.MessageField('SecurityMarks', 35)
  severity = _messages.EnumField('SeverityValueValuesEnum', 36)
  sourceProperties = _messages.MessageField('SourcePropertiesValue', 37)
  state = _messages.EnumField('StateValueValuesEnum', 38)
  vulnerability = _messages.MessageField('Vulnerability', 39)


class Folder(_messages.Message):
  r"""Message that contains the resource name and display name of a folder
  resource.

  Fields:
    resourceFolder: Full resource name of this folder. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    resourceFolderDisplayName: The user defined display name for this folder.
  """

  resourceFolder = _messages.StringField(1)
  resourceFolderDisplayName = _messages.StringField(2)


class Geolocation(_messages.Message):
  r"""Represents a geographical location for a given access.

  Fields:
    regionCode: A CLDR.
  """

  regionCode = _messages.StringField(1)


class GoogleCloudSecuritycenterV1BigQueryExport(_messages.Message):
  r"""Configures how to deliver Findings to BigQuery Instance.

  Fields:
    createTime: Output only. The time at which the BigQuery export was
      created. This field is set by the server and will be ignored if provided
      on export on creation.
    dataset: The dataset to write findings' updates to. Its format is
      "projects/[project_id]/datasets/[bigquery_dataset_id]". BigQuery Dataset
      unique ID must contain only letters (a-z, A-Z), numbers (0-9), or
      underscores (_).
    description: The description of the export (max of 1024 characters).
    filter: Expression that defines the filter to apply across create/update
      events of findings. The expression is a list of zero or more
      restrictions combined via logical operators `AND` and `OR`. Parentheses
      are supported, and `OR` has higher precedence than `AND`. Restrictions
      have the form ` ` and may have a `-` character in front of them to
      indicate negation. The fields map to those defined in the corresponding
      resource. The supported operators are: * `=` for all value types. * `>`,
      `<`, `>=`, `<=` for integer values. * `:`, meaning substring matching,
      for strings. The supported value types are: * string literals in quotes.
      * integer literals without quotes. * boolean literals `true` and `false`
      without quotes.
    mostRecentEditor: Output only. Email address of the user who last edited
      the BigQuery export. This field is set by the server and will be ignored
      if provided on export creation or update.
    name: The relative resource name of this export. See: https://cloud.google
      .com/apis/design/resource_names#relative_resource_name. Example format:
      "organizations/{organization_id}/bigQueryExports/{export_id}" Example
      format: "folders/{folder_id}/bigQueryExports/{export_id}" Example
      format: "projects/{project_id}/bigQueryExports/{export_id}" This field
      is provided in responses, and is ignored when provided in create
      requests.
    principal: Output only. The service account that needs permission to
      create table and upload data to the BigQuery dataset.
    updateTime: Output only. The most recent time at which the BigQuery export
      was updated. This field is set by the server and will be ignored if
      provided on export creation or update.
  """

  createTime = _messages.StringField(1)
  dataset = _messages.StringField(2)
  description = _messages.StringField(3)
  filter = _messages.StringField(4)
  mostRecentEditor = _messages.StringField(5)
  name = _messages.StringField(6)
  principal = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class GoogleCloudSecuritycenterV1Binding(_messages.Message):
  r"""Represents a Kubernetes RoleBinding or ClusterRoleBinding.

  Fields:
    name: Name for the binding.
    ns: Namespace for the binding.
    role: The Role or ClusterRole referenced by the binding.
    subjects: Represents one or more subjects that are bound to the role. Not
      always available for PATCH requests.
  """

  name = _messages.StringField(1)
  ns = _messages.StringField(2)
  role = _messages.MessageField('Role', 3)
  subjects = _messages.MessageField('Subject', 4, repeated=True)


class GoogleCloudSecuritycenterV1BulkMuteFindingsResponse(_messages.Message):
  r"""The response to a BulkMute request. Contains the LRO information."""


class GoogleCloudSecuritycenterV1CustomConfig(_messages.Message):
  r"""Defines the properties in a custom module configuration for Security
  Health Analytics. Use the custom module configuration to create custom
  detectors that generate custom findings for resources that you specify.

  Enums:
    SeverityValueValuesEnum: The severity to assign to findings generated by
      the module.

  Fields:
    customOutput: Custom output properties.
    description: Text that describes the vulnerability or misconfiguration
      that the custom module detects. This explanation is returned with each
      finding instance to help investigators understand the detected issue.
      The text must be enclosed in quotation marks.
    predicate: The CEL expression to evaluate to produce findings. When the
      expression evaluates to true against a resource, a finding is generated.
    recommendation: An explanation of the recommended steps that security
      teams can take to resolve the detected issue. This explanation is
      returned with each finding generated by this module in the `nextSteps`
      property of the finding JSON.
    resourceSelector: The resource types that the custom module operates on.
      Each custom module can specify up to 5 resource types.
    severity: The severity to assign to findings generated by the module.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity to assign to findings generated by the module.

    Values:
      SEVERITY_UNSPECIFIED: Unspecified severity.
      CRITICAL: Critical severity.
      HIGH: High severity.
      MEDIUM: Medium severity.
      LOW: Low severity.
    """
    SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  customOutput = _messages.MessageField('GoogleCloudSecuritycenterV1CustomOutputSpec', 1)
  description = _messages.StringField(2)
  predicate = _messages.MessageField('Expr', 3)
  recommendation = _messages.StringField(4)
  resourceSelector = _messages.MessageField('GoogleCloudSecuritycenterV1ResourceSelector', 5)
  severity = _messages.EnumField('SeverityValueValuesEnum', 6)


class GoogleCloudSecuritycenterV1CustomOutputSpec(_messages.Message):
  r"""A set of optional name-value pairs that define custom source properties
  to return with each finding that is generated by the custom module. The
  custom source properties that are defined here are included in the finding
  JSON under `sourceProperties`.

  Fields:
    properties: A list of custom output properties to add to the finding.
  """

  properties = _messages.MessageField('GoogleCloudSecuritycenterV1Property', 1, repeated=True)


class GoogleCloudSecuritycenterV1EffectiveSecurityHealthAnalyticsCustomModule(_messages.Message):
  r"""An EffectiveSecurityHealthAnalyticsCustomModule is the representation of
  a Security Health Analytics custom module at a specified level of the
  resource hierarchy: organization, folder, or project. If a custom module is
  inherited from a parent organization or folder, the value of the
  `enablementState` property in EffectiveSecurityHealthAnalyticsCustomModule
  is set to the value that is effective in the parent, instead of `INHERITED`.
  For example, if the module is enabled in a parent organization or folder,
  the effective enablement_state for the module in all child folders or
  projects is also `enabled`. EffectiveSecurityHealthAnalyticsCustomModule is
  read-only.

  Enums:
    EnablementStateValueValuesEnum: Output only. The effective state of
      enablement for the module at the given level of the hierarchy.

  Fields:
    customConfig: Output only. The user-specified configuration for the
      module.
    displayName: Output only. The display name for the custom module. The name
      must be between 1 and 128 characters, start with a lowercase letter, and
      contain alphanumeric characters or underscores only.
    enablementState: Output only. The effective state of enablement for the
      module at the given level of the hierarchy.
    name: Output only. The resource name of the custom module. Its format is "
      organizations/{organization}/securityHealthAnalyticsSettings/effectiveCu
      stomModules/{customModule}", or "folders/{folder}/securityHealthAnalytic
      sSettings/effectiveCustomModules/{customModule}", or "projects/{project}
      /securityHealthAnalyticsSettings/effectiveCustomModules/{customModule}"
  """

  class EnablementStateValueValuesEnum(_messages.Enum):
    r"""Output only. The effective state of enablement for the module at the
    given level of the hierarchy.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Unspecified enablement state.
      ENABLED: The module is enabled at the given level.
      DISABLED: The module is disabled at the given level.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  customConfig = _messages.MessageField('GoogleCloudSecuritycenterV1CustomConfig', 1)
  displayName = _messages.StringField(2)
  enablementState = _messages.EnumField('EnablementStateValueValuesEnum', 3)
  name = _messages.StringField(4)


class GoogleCloudSecuritycenterV1ExternalSystem(_messages.Message):
  r"""Representation of third party SIEM/SOAR fields within SCC.

  Fields:
    assignees: References primary/secondary etc assignees in the external
      system.
    externalSystemUpdateTime: The most recent time when the corresponding
      finding's ticket/tracker was updated in the external system.
    externalUid: Identifier that's used to track the given finding in the
      external system.
    name: Full resource name of the external system, for example:
      "organizations/1234/sources/5678/findings/123456/externalSystems/jira",
      "folders/1234/sources/5678/findings/123456/externalSystems/jira",
      "projects/1234/sources/5678/findings/123456/externalSystems/jira"
    status: Most recent status of the corresponding finding's ticket/tracker
      in the external system.
  """

  assignees = _messages.StringField(1, repeated=True)
  externalSystemUpdateTime = _messages.StringField(2)
  externalUid = _messages.StringField(3)
  name = _messages.StringField(4)
  status = _messages.StringField(5)


class GoogleCloudSecuritycenterV1MuteConfig(_messages.Message):
  r"""A mute config is a Cloud SCC resource that contains the configuration to
  mute create/update events of findings.

  Fields:
    createTime: Output only. The time at which the mute config was created.
      This field is set by the server and will be ignored if provided on
      config creation.
    description: A description of the mute config.
    displayName: The human readable name to be displayed for the mute config.
    filter: Required. An expression that defines the filter to apply across
      create/update events of findings. While creating a filter string, be
      mindful of the scope in which the mute configuration is being created.
      E.g., If a filter contains project = X but is created under the project
      = Y scope, it might not match any findings. The following field and
      operator combinations are supported: * severity: `=`, `:` * category:
      `=`, `:` * resource.name: `=`, `:` * resource.project_name: `=`, `:` *
      resource.project_display_name: `=`, `:` *
      resource.folders.resource_folder: `=`, `:` * resource.parent_name: `=`,
      `:` * resource.parent_display_name: `=`, `:` * resource.type: `=`, `:` *
      finding_class: `=`, `:` * indicator.ip_addresses: `=`, `:` *
      indicator.domains: `=`, `:`
    mostRecentEditor: Output only. Email address of the user who last edited
      the mute config. This field is set by the server and will be ignored if
      provided on config creation or update.
    name: This field will be ignored if provided on config creation. Format
      "organizations/{organization}/muteConfigs/{mute_config}"
      "folders/{folder}/muteConfigs/{mute_config}"
      "projects/{project}/muteConfigs/{mute_config}"
    updateTime: Output only. The most recent time at which the mute config was
      updated. This field is set by the server and will be ignored if provided
      on config creation or update.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  displayName = _messages.StringField(3)
  filter = _messages.StringField(4)
  mostRecentEditor = _messages.StringField(5)
  name = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class GoogleCloudSecuritycenterV1NotificationMessage(_messages.Message):
  r"""Cloud SCC's Notification

  Fields:
    finding: If it's a Finding based notification config, this field will be
      populated.
    notificationConfigName: Name of the notification config that generated
      current notification.
    resource: The Cloud resource tied to this notification's Finding.
  """

  finding = _messages.MessageField('Finding', 1)
  notificationConfigName = _messages.StringField(2)
  resource = _messages.MessageField('GoogleCloudSecuritycenterV1Resource', 3)


class GoogleCloudSecuritycenterV1Property(_messages.Message):
  r"""An individual name-value pair that defines a custom source property.

  Fields:
    name: Name of the property for the custom output.
    valueExpression: The CEL expression for the custom output. A resource
      property can be specified to return the value of the property or a text
      string enclosed in quotation marks.
  """

  name = _messages.StringField(1)
  valueExpression = _messages.MessageField('Expr', 2)


class GoogleCloudSecuritycenterV1Resource(_messages.Message):
  r"""Information related to the Google Cloud resource.

  Fields:
    displayName: The human readable name of the resource.
    folders: Output only. Contains a Folder message for each folder in the
      assets ancestry. The first folder is the deepest nested folder, and the
      last folder is the folder directly under the Organization.
    name: The full resource name of the resource. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    parent: The full resource name of resource's parent.
    parentDisplayName: The human readable name of resource's parent.
    project: The full resource name of project that the resource belongs to.
    projectDisplayName: The project ID that the resource belongs to.
    type: The full resource type of the resource.
  """

  displayName = _messages.StringField(1)
  folders = _messages.MessageField('Folder', 2, repeated=True)
  name = _messages.StringField(3)
  parent = _messages.StringField(4)
  parentDisplayName = _messages.StringField(5)
  project = _messages.StringField(6)
  projectDisplayName = _messages.StringField(7)
  type = _messages.StringField(8)


class GoogleCloudSecuritycenterV1ResourceSelector(_messages.Message):
  r"""Resource for selecting resource type.

  Fields:
    resourceTypes: The resource types to run the detector on.
  """

  resourceTypes = _messages.StringField(1, repeated=True)


class GoogleCloudSecuritycenterV1ResourceValueConfig(_messages.Message):
  r"""A resource value config is a mapping configuration of user's tag values
  to resource values. Used by the attack path simulation.

  Enums:
    ResourceValueValueValuesEnum: Required. Resource value level this
      expression represents

  Messages:
    ResourceLabelsSelectorValue: List of resource labels to search for,
      evaluated with AND. E.g. "resource_labels_selector": {"key": "value",
      "env": "prod"} will match resources with labels "key": "value" AND
      "env": "prod" https://cloud.google.com/resource-manager/docs/creating-
      managing-labels

  Fields:
    createTime: Output only. Timestamp this resource value config was created.
    description: Description of the resource value config.
    name: Name for the resource value config
    resourceLabelsSelector: List of resource labels to search for, evaluated
      with AND. E.g. "resource_labels_selector": {"key": "value", "env":
      "prod"} will match resources with labels "key": "value" AND "env":
      "prod" https://cloud.google.com/resource-manager/docs/creating-managing-
      labels
    resourceType: Apply resource_value only to resources that match
      resource_type. resource_type will be checked with "AND" of other
      resources. E.g. "storage.googleapis.com/Bucket" with resource_value
      "HIGH" will apply "HIGH" value only to "storage.googleapis.com/Bucket"
      resources.
    resourceValue: Required. Resource value level this expression represents
    scope: Project or folder to scope this config to. For example,
      "project/456" would apply this config only to resources in "project/456"
      scope will be checked with "AND" of other resources.
    tagValues: Required. Tag values combined with AND to check against. Values
      in the form "tagValues/123" E.g. [ "tagValues/123", "tagValues/456",
      "tagValues/789" ] https://cloud.google.com/resource-
      manager/docs/tags/tags-creating-and-managing
    updateTime: Output only. Timestamp this resource value config was last
      updated.
  """

  class ResourceValueValueValuesEnum(_messages.Enum):
    r"""Required. Resource value level this expression represents

    Values:
      RESOURCE_VALUE_UNSPECIFIED: Unspecific value
      HIGH: High resource value
      MEDIUM: Medium resource value
      LOW: Low resource value
      NONE: No resource value, e.g. ignore these resources
    """
    RESOURCE_VALUE_UNSPECIFIED = 0
    HIGH = 1
    MEDIUM = 2
    LOW = 3
    NONE = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourceLabelsSelectorValue(_messages.Message):
    r"""List of resource labels to search for, evaluated with AND. E.g.
    "resource_labels_selector": {"key": "value", "env": "prod"} will match
    resources with labels "key": "value" AND "env": "prod"
    https://cloud.google.com/resource-manager/docs/creating-managing-labels

    Messages:
      AdditionalProperty: An additional property for a
        ResourceLabelsSelectorValue object.

    Fields:
      additionalProperties: Additional properties of type
        ResourceLabelsSelectorValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourceLabelsSelectorValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  name = _messages.StringField(3)
  resourceLabelsSelector = _messages.MessageField('ResourceLabelsSelectorValue', 4)
  resourceType = _messages.StringField(5)
  resourceValue = _messages.EnumField('ResourceValueValueValuesEnum', 6)
  scope = _messages.StringField(7)
  tagValues = _messages.StringField(8, repeated=True)
  updateTime = _messages.StringField(9)


class GoogleCloudSecuritycenterV1RunAssetDiscoveryResponse(_messages.Message):
  r"""Response of asset discovery run

  Enums:
    StateValueValuesEnum: The state of an asset discovery run.

  Fields:
    duration: The duration between asset discovery run start and end
    state: The state of an asset discovery run.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of an asset discovery run.

    Values:
      STATE_UNSPECIFIED: Asset discovery run state was unspecified.
      COMPLETED: Asset discovery run completed successfully.
      SUPERSEDED: Asset discovery run was cancelled with tasks still pending,
        as another run for the same organization was started with a higher
        priority.
      TERMINATED: Asset discovery run was killed and terminated.
    """
    STATE_UNSPECIFIED = 0
    COMPLETED = 1
    SUPERSEDED = 2
    TERMINATED = 3

  duration = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class GoogleCloudSecuritycenterV1SecurityHealthAnalyticsCustomModule(_messages.Message):
  r"""Represents an instance of a Security Health Analytics custom module,
  including its full module name, display name, enablement state, and last
  updated time. You can create a custom module at the organization, folder, or
  project level. Custom modules that you create at the organization or folder
  level are inherited by the child folders and projects.

  Enums:
    EnablementStateValueValuesEnum: The enablement state of the custom module.

  Fields:
    ancestorModule: Output only. If empty, indicates that the custom module
      was created in the organization, folder, or project in which you are
      viewing the custom module. Otherwise, `ancestor_module` specifies the
      organization or folder from which the custom module is inherited.
    customConfig: The user specified custom configuration for the module.
    displayName: The display name of the Security Health Analytics custom
      module. This display name becomes the finding category for all findings
      that are returned by this custom module. The display name must be
      between 1 and 128 characters, start with a lowercase letter, and contain
      alphanumeric characters or underscores only.
    enablementState: The enablement state of the custom module.
    lastEditor: Output only. The editor that last updated the custom module.
    name: Immutable. The resource name of the custom module. Its format is "or
      ganizations/{organization}/securityHealthAnalyticsSettings/customModules
      /{customModule}", or "folders/{folder}/securityHealthAnalyticsSettings/c
      ustomModules/{customModule}", or "projects/{project}/securityHealthAnaly
      ticsSettings/customModules/{customModule}" The id {customModule} is
      server-generated and is not user settable. It will be a numeric id
      containing 1-20 digits.
    updateTime: Output only. The time at which the custom module was last
      updated.
  """

  class EnablementStateValueValuesEnum(_messages.Enum):
    r"""The enablement state of the custom module.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Unspecified enablement state.
      ENABLED: The module is enabled at the given CRM resource.
      DISABLED: The module is disabled at the given CRM resource.
      INHERITED: State is inherited from an ancestor module. The module will
        either be effectively ENABLED or DISABLED based on its closest non-
        inherited ancestor module in the CRM hierarchy.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2
    INHERITED = 3

  ancestorModule = _messages.StringField(1)
  customConfig = _messages.MessageField('GoogleCloudSecuritycenterV1CustomConfig', 2)
  displayName = _messages.StringField(3)
  enablementState = _messages.EnumField('EnablementStateValueValuesEnum', 4)
  lastEditor = _messages.StringField(5)
  name = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class GoogleCloudSecuritycenterV1beta1RunAssetDiscoveryResponse(_messages.Message):
  r"""Response of asset discovery run

  Enums:
    StateValueValuesEnum: The state of an asset discovery run.

  Fields:
    duration: The duration between asset discovery run start and end
    state: The state of an asset discovery run.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of an asset discovery run.

    Values:
      STATE_UNSPECIFIED: Asset discovery run state was unspecified.
      COMPLETED: Asset discovery run completed successfully.
      SUPERSEDED: Asset discovery run was cancelled with tasks still pending,
        as another run for the same organization was started with a higher
        priority.
      TERMINATED: Asset discovery run was killed and terminated.
    """
    STATE_UNSPECIFIED = 0
    COMPLETED = 1
    SUPERSEDED = 2
    TERMINATED = 3

  duration = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class GoogleCloudSecuritycenterV1p1beta1Finding(_messages.Message):
  r"""Security Command Center finding. A finding is a record of assessment
  data (security, risk, health or privacy) ingested into Security Command
  Center for presentation, notification, analysis, policy testing, and
  enforcement. For example, an XSS vulnerability in an App Engine application
  is a finding.

  Enums:
    SeverityValueValuesEnum: The severity of the finding. This field is
      managed by the source that writes the finding.
    StateValueValuesEnum: The state of the finding.

  Messages:
    SourcePropertiesValue: Source specific properties. These properties are
      managed by the source that writes the finding. The key names in the
      source_properties map must be between 1 and 255 characters, and must
      start with a letter and contain alphanumeric characters or underscores
      only.

  Fields:
    canonicalName: The canonical name of the finding. It's either "organizatio
      ns/{organization_id}/sources/{source_id}/findings/{finding_id}",
      "folders/{folder_id}/sources/{source_id}/findings/{finding_id}" or
      "projects/{project_number}/sources/{source_id}/findings/{finding_id}",
      depending on the closest CRM ancestor of the resource associated with
      the finding.
    category: The additional taxonomy group within findings from a given
      source. This field is immutable after creation time. Example:
      "XSS_FLASH_INJECTION"
    createTime: The time at which the finding was created in Security Command
      Center.
    eventTime: The time at which the event took place, or when an update to
      the finding occurred. For example, if the finding represents an open
      firewall it would capture the time the detector believes the firewall
      became open. The accuracy is determined by the detector. If the finding
      were to be resolved afterward, this time would reflect when the finding
      was resolved. Must not be set to a value greater than the current
      timestamp.
    externalUri: The URI that, if available, points to a web page outside of
      Security Command Center where additional information about the finding
      can be found. This field is guaranteed to be either empty or a well
      formed URL.
    name: The relative resource name of this finding. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Example: "organizations/{organization_id}/sources/{source_id}/finding
      s/{finding_id}"
    parent: The relative resource name of the source the finding belongs to.
      See: https://cloud.google.com/apis/design/resource_names#relative_resour
      ce_name This field is immutable after creation time. For example:
      "organizations/{organization_id}/sources/{source_id}"
    resourceName: For findings on Google Cloud resources, the full resource
      name of the Google Cloud resource this finding is for. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
      When the finding is for a non-Google Cloud resource, the resourceName
      can be a customer or partner defined string. This field is immutable
      after creation time.
    securityMarks: Output only. User specified security marks. These marks are
      entirely managed by the user and come from the SecurityMarks resource
      that belongs to the finding.
    severity: The severity of the finding. This field is managed by the source
      that writes the finding.
    sourceProperties: Source specific properties. These properties are managed
      by the source that writes the finding. The key names in the
      source_properties map must be between 1 and 255 characters, and must
      start with a letter and contain alphanumeric characters or underscores
      only.
    state: The state of the finding.
  """

  class SeverityValueValuesEnum(_messages.Enum):
    r"""The severity of the finding. This field is managed by the source that
    writes the finding.

    Values:
      SEVERITY_UNSPECIFIED: No severity specified. The default value.
      CRITICAL: Critical severity.
      HIGH: High severity.
      MEDIUM: Medium severity.
      LOW: Low severity.
    """
    SEVERITY_UNSPECIFIED = 0
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the finding.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      ACTIVE: The finding requires attention and has not been addressed yet.
      INACTIVE: The finding has been fixed, triaged as a non-issue or
        otherwise addressed and is no longer active.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SourcePropertiesValue(_messages.Message):
    r"""Source specific properties. These properties are managed by the source
    that writes the finding. The key names in the source_properties map must
    be between 1 and 255 characters, and must start with a letter and contain
    alphanumeric characters or underscores only.

    Messages:
      AdditionalProperty: An additional property for a SourcePropertiesValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        SourcePropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SourcePropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  canonicalName = _messages.StringField(1)
  category = _messages.StringField(2)
  createTime = _messages.StringField(3)
  eventTime = _messages.StringField(4)
  externalUri = _messages.StringField(5)
  name = _messages.StringField(6)
  parent = _messages.StringField(7)
  resourceName = _messages.StringField(8)
  securityMarks = _messages.MessageField('GoogleCloudSecuritycenterV1p1beta1SecurityMarks', 9)
  severity = _messages.EnumField('SeverityValueValuesEnum', 10)
  sourceProperties = _messages.MessageField('SourcePropertiesValue', 11)
  state = _messages.EnumField('StateValueValuesEnum', 12)


class GoogleCloudSecuritycenterV1p1beta1Folder(_messages.Message):
  r"""Message that contains the resource name and display name of a folder
  resource.

  Fields:
    resourceFolder: Full resource name of this folder. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    resourceFolderDisplayName: The user defined display name for this folder.
  """

  resourceFolder = _messages.StringField(1)
  resourceFolderDisplayName = _messages.StringField(2)


class GoogleCloudSecuritycenterV1p1beta1NotificationMessage(_messages.Message):
  r"""Security Command Center's Notification

  Fields:
    finding: If it's a Finding based notification config, this field will be
      populated.
    notificationConfigName: Name of the notification config that generated
      current notification.
    resource: The Cloud resource tied to the notification.
  """

  finding = _messages.MessageField('GoogleCloudSecuritycenterV1p1beta1Finding', 1)
  notificationConfigName = _messages.StringField(2)
  resource = _messages.MessageField('GoogleCloudSecuritycenterV1p1beta1Resource', 3)


class GoogleCloudSecuritycenterV1p1beta1Resource(_messages.Message):
  r"""Information related to the Google Cloud resource.

  Fields:
    folders: Output only. Contains a Folder message for each folder in the
      assets ancestry. The first folder is the deepest nested folder, and the
      last folder is the folder directly under the Organization.
    name: The full resource name of the resource. See:
      https://cloud.google.com/apis/design/resource_names#full_resource_name
    parent: The full resource name of resource's parent.
    parentDisplayName: The human readable name of resource's parent.
    project: The full resource name of project that the resource belongs to.
    projectDisplayName: The project id that the resource belongs to.
  """

  folders = _messages.MessageField('GoogleCloudSecuritycenterV1p1beta1Folder', 1, repeated=True)
  name = _messages.StringField(2)
  parent = _messages.StringField(3)
  parentDisplayName = _messages.StringField(4)
  project = _messages.StringField(5)
  projectDisplayName = _messages.StringField(6)


class GoogleCloudSecuritycenterV1p1beta1RunAssetDiscoveryResponse(_messages.Message):
  r"""Response of asset discovery run

  Enums:
    StateValueValuesEnum: The state of an asset discovery run.

  Fields:
    duration: The duration between asset discovery run start and end
    state: The state of an asset discovery run.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of an asset discovery run.

    Values:
      STATE_UNSPECIFIED: Asset discovery run state was unspecified.
      COMPLETED: Asset discovery run completed successfully.
      SUPERSEDED: Asset discovery run was cancelled with tasks still pending,
        as another run for the same organization was started with a higher
        priority.
      TERMINATED: Asset discovery run was killed and terminated.
    """
    STATE_UNSPECIFIED = 0
    COMPLETED = 1
    SUPERSEDED = 2
    TERMINATED = 3

  duration = _messages.StringField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class GoogleCloudSecuritycenterV1p1beta1SecurityMarks(_messages.Message):
  r"""User specified security marks that are attached to the parent Security
  Command Center resource. Security marks are scoped within a Security Command
  Center organization -- they can be modified and viewed by all users who have
  proper permissions on the organization.

  Messages:
    MarksValue: Mutable user specified security marks belonging to the parent
      resource. Constraints are as follows: * Keys and values are treated as
      case insensitive * Keys must be between 1 - 256 characters (inclusive) *
      Keys must be letters, numbers, underscores, or dashes * Values have
      leading and trailing whitespace trimmed, remaining characters must be
      between 1 - 4096 characters (inclusive)

  Fields:
    canonicalName: The canonical name of the marks. Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks"
      "folders/{folder_id}/assets/{asset_id}/securityMarks"
      "projects/{project_number}/assets/{asset_id}/securityMarks" "organizatio
      ns/{organization_id}/sources/{source_id}/findings/{finding_id}/securityM
      arks" "folders/{folder_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks" "projects/{project_number}/sources/{source_id}/findings/{fin
      ding_id}/securityMarks"
    marks: Mutable user specified security marks belonging to the parent
      resource. Constraints are as follows: * Keys and values are treated as
      case insensitive * Keys must be between 1 - 256 characters (inclusive) *
      Keys must be letters, numbers, underscores, or dashes * Values have
      leading and trailing whitespace trimmed, remaining characters must be
      between 1 - 4096 characters (inclusive)
    name: The relative resource name of the SecurityMarks. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks" "organ
      izations/{organization_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks".
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MarksValue(_messages.Message):
    r"""Mutable user specified security marks belonging to the parent
    resource. Constraints are as follows: * Keys and values are treated as
    case insensitive * Keys must be between 1 - 256 characters (inclusive) *
    Keys must be letters, numbers, underscores, or dashes * Values have
    leading and trailing whitespace trimmed, remaining characters must be
    between 1 - 4096 characters (inclusive)

    Messages:
      AdditionalProperty: An additional property for a MarksValue object.

    Fields:
      additionalProperties: Additional properties of type MarksValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MarksValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  canonicalName = _messages.StringField(1)
  marks = _messages.MessageField('MarksValue', 2)
  name = _messages.StringField(3)


class IamBinding(_messages.Message):
  r"""Represents a particular IAM binding, which captures a member's role
  addition, removal, or state.

  Enums:
    ActionValueValuesEnum: The action that was performed on a Binding.

  Fields:
    action: The action that was performed on a Binding.
    member: A single identity requesting access for a Cloud Platform resource,
      for example, "<EMAIL>".
    role: Role that is assigned to "members". For example, "roles/viewer",
      "roles/editor", or "roles/owner".
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""The action that was performed on a Binding.

    Values:
      ACTION_UNSPECIFIED: Unspecified.
      ADD: Addition of a Binding.
      REMOVE: Removal of a Binding.
    """
    ACTION_UNSPECIFIED = 0
    ADD = 1
    REMOVE = 2

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  member = _messages.StringField(2)
  role = _messages.StringField(3)


class Indicator(_messages.Message):
  r"""Represents what's commonly known as an _indicator of compromise_ (IoC)
  in computer forensics. This is an artifact observed on a network or in an
  operating system that, with high confidence, indicates a computer intrusion.
  For more information, see [Indicator of
  compromise](https://en.wikipedia.org/wiki/Indicator_of_compromise).

  Fields:
    domains: List of domains associated to the Finding.
    ipAddresses: The list of IP addresses that are associated with the
      finding.
    signatures: The list of matched signatures indicating that the given
      process is present in the environment.
    uris: The list of URIs associated to the Findings.
  """

  domains = _messages.StringField(1, repeated=True)
  ipAddresses = _messages.StringField(2, repeated=True)
  signatures = _messages.MessageField('ProcessSignature', 3, repeated=True)
  uris = _messages.StringField(4, repeated=True)


class KernelRootkit(_messages.Message):
  r"""Kernel mode rootkit signatures.

  Fields:
    name: Rootkit name, when available.
    unexpectedCodeModification: True if unexpected modifications of kernel
      code memory are present.
    unexpectedFtraceHandler: True if `ftrace` points are present with
      callbacks pointing to regions that are not in the expected kernel or
      module code range.
    unexpectedInterruptHandler: True if interrupt handlers that are are not in
      the expected kernel or module code regions are present.
    unexpectedKernelCodePages: True if kernel code pages that are not in the
      expected kernel or module code regions are present.
    unexpectedKprobeHandler: True if `kprobe` points are present with
      callbacks pointing to regions that are not in the expected kernel or
      module code range.
    unexpectedProcessesInRunqueue: True if unexpected processes in the
      scheduler run queue are present. Such processes are in the run queue,
      but not in the process task list.
    unexpectedReadOnlyDataModification: True if unexpected modifications of
      kernel read-only data memory are present.
    unexpectedSystemCallHandler: True if system call handlers that are are not
      in the expected kernel or module code regions are present.
  """

  name = _messages.StringField(1)
  unexpectedCodeModification = _messages.BooleanField(2)
  unexpectedFtraceHandler = _messages.BooleanField(3)
  unexpectedInterruptHandler = _messages.BooleanField(4)
  unexpectedKernelCodePages = _messages.BooleanField(5)
  unexpectedKprobeHandler = _messages.BooleanField(6)
  unexpectedProcessesInRunqueue = _messages.BooleanField(7)
  unexpectedReadOnlyDataModification = _messages.BooleanField(8)
  unexpectedSystemCallHandler = _messages.BooleanField(9)


class Kubernetes(_messages.Message):
  r"""Kubernetes-related attributes.

  Fields:
    accessReviews: Provides information on any Kubernetes access reviews
      (privilege checks) relevant to the finding.
    bindings: Provides Kubernetes role binding information for findings that
      involve [RoleBindings or
      ClusterRoleBindings](https://cloud.google.com/kubernetes-
      engine/docs/how-to/role-based-access-control).
    nodePools: GKE [node pools](https://cloud.google.com/kubernetes-
      engine/docs/concepts/node-pools) associated with the finding. This field
      contains node pool information for each node, when it is available.
    nodes: Provides Kubernetes [node](https://cloud.google.com/kubernetes-
      engine/docs/concepts/cluster-architecture#nodes) information.
    pods: Kubernetes [Pods](https://cloud.google.com/kubernetes-
      engine/docs/concepts/pod) associated with the finding. This field
      contains Pod records for each container that is owned by a Pod.
    roles: Provides Kubernetes role information for findings that involve
      [Roles or ClusterRoles](https://cloud.google.com/kubernetes-
      engine/docs/how-to/role-based-access-control).
  """

  accessReviews = _messages.MessageField('AccessReview', 1, repeated=True)
  bindings = _messages.MessageField('GoogleCloudSecuritycenterV1Binding', 2, repeated=True)
  nodePools = _messages.MessageField('NodePool', 3, repeated=True)
  nodes = _messages.MessageField('Node', 4, repeated=True)
  pods = _messages.MessageField('Pod', 5, repeated=True)
  roles = _messages.MessageField('Role', 6, repeated=True)


class Label(_messages.Message):
  r"""Represents a generic name-value label. A label has separate name and
  value fields to support filtering with the `contains()` function. For more
  information, see [Filtering on array-type
  fields](https://cloud.google.com/security-command-center/docs/how-to-api-
  list-findings#array-contains-filtering).

  Fields:
    name: Name of the label.
    value: Value that corresponds to the label's name.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class MemoryHashSignature(_messages.Message):
  r"""A signature corresponding to memory page hashes.

  Fields:
    binaryFamily: The binary family.
    detections: The list of memory hash detections contributing to the binary
      family match.
  """

  binaryFamily = _messages.StringField(1)
  detections = _messages.MessageField('Detection', 2, repeated=True)


class MitreAttack(_messages.Message):
  r"""MITRE ATT&CK tactics and techniques related to this finding. See:
  https://attack.mitre.org

  Enums:
    AdditionalTacticsValueListEntryValuesEnum:
    AdditionalTechniquesValueListEntryValuesEnum:
    PrimaryTacticValueValuesEnum: The MITRE ATT&CK tactic most closely
      represented by this finding, if any.
    PrimaryTechniquesValueListEntryValuesEnum:

  Fields:
    additionalTactics: Additional MITRE ATT&CK tactics related to this
      finding, if any.
    additionalTechniques: Additional MITRE ATT&CK techniques related to this
      finding, if any, along with any of their respective parent techniques.
    primaryTactic: The MITRE ATT&CK tactic most closely represented by this
      finding, if any.
    primaryTechniques: The MITRE ATT&CK technique most closely represented by
      this finding, if any. primary_techniques is a repeated field because
      there are multiple levels of MITRE ATT&CK techniques. If the technique
      most closely represented by this finding is a sub-technique (e.g.
      `SCANNING_IP_BLOCKS`), both the sub-technique and its parent
      technique(s) will be listed (e.g. `SCANNING_IP_BLOCKS`,
      `ACTIVE_SCANNING`).
    version: The MITRE ATT&CK version referenced by the above fields. E.g.
      "8".
  """

  class AdditionalTacticsValueListEntryValuesEnum(_messages.Enum):
    r"""AdditionalTacticsValueListEntryValuesEnum enum type.

    Values:
      TACTIC_UNSPECIFIED: Unspecified value.
      RECONNAISSANCE: TA0043
      RESOURCE_DEVELOPMENT: TA0042
      INITIAL_ACCESS: TA0001
      EXECUTION: TA0002
      PERSISTENCE: TA0003
      PRIVILEGE_ESCALATION: TA0004
      DEFENSE_EVASION: TA0005
      CREDENTIAL_ACCESS: TA0006
      DISCOVERY: TA0007
      LATERAL_MOVEMENT: TA0008
      COLLECTION: TA0009
      COMMAND_AND_CONTROL: TA0011
      EXFILTRATION: TA0010
      IMPACT: TA0040
    """
    TACTIC_UNSPECIFIED = 0
    RECONNAISSANCE = 1
    RESOURCE_DEVELOPMENT = 2
    INITIAL_ACCESS = 3
    EXECUTION = 4
    PERSISTENCE = 5
    PRIVILEGE_ESCALATION = 6
    DEFENSE_EVASION = 7
    CREDENTIAL_ACCESS = 8
    DISCOVERY = 9
    LATERAL_MOVEMENT = 10
    COLLECTION = 11
    COMMAND_AND_CONTROL = 12
    EXFILTRATION = 13
    IMPACT = 14

  class AdditionalTechniquesValueListEntryValuesEnum(_messages.Enum):
    r"""AdditionalTechniquesValueListEntryValuesEnum enum type.

    Values:
      TECHNIQUE_UNSPECIFIED: Unspecified value.
      ACTIVE_SCANNING: T1595
      SCANNING_IP_BLOCKS: T1595.001
      INGRESS_TOOL_TRANSFER: T1105
      NATIVE_API: T1106
      SHARED_MODULES: T1129
      COMMAND_AND_SCRIPTING_INTERPRETER: T1059
      UNIX_SHELL: T1059.004
      RESOURCE_HIJACKING: T1496
      PROXY: T1090
      EXTERNAL_PROXY: T1090.002
      MULTI_HOP_PROXY: T1090.003
      DYNAMIC_RESOLUTION: T1568
      UNSECURED_CREDENTIALS: T1552
      VALID_ACCOUNTS: T1078
      LOCAL_ACCOUNTS: T1078.003
      CLOUD_ACCOUNTS: T1078.004
      NETWORK_DENIAL_OF_SERVICE: T1498
      PERMISSION_GROUPS_DISCOVERY: T1069
      CLOUD_GROUPS: T1069.003
      EXFILTRATION_OVER_WEB_SERVICE: T1567
      EXFILTRATION_TO_CLOUD_STORAGE: T1567.002
      ACCOUNT_MANIPULATION: T1098
      SSH_AUTHORIZED_KEYS: T1098.004
      CREATE_OR_MODIFY_SYSTEM_PROCESS: T1543
      STEAL_WEB_SESSION_COOKIE: T1539
      MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE: T1578
      EXPLOIT_PUBLIC_FACING_APPLICATION: T1190
      MODIFY_AUTHENTICATION_PROCESS: T1556
      DATA_DESTRUCTION: T1485
      DOMAIN_POLICY_MODIFICATION: T1484
      IMPAIR_DEFENSES: T1562
      NETWORK_SERVICE_DISCOVERY: T1046
      ACCESS_TOKEN_MANIPULATION: T1134
      ABUSE_ELEVATION_CONTROL_MECHANISM: T1548
      DEFAULT_ACCOUNTS: T1078.001
      INHIBIT_SYSTEM_RECOVERY: T1490
    """
    TECHNIQUE_UNSPECIFIED = 0
    ACTIVE_SCANNING = 1
    SCANNING_IP_BLOCKS = 2
    INGRESS_TOOL_TRANSFER = 3
    NATIVE_API = 4
    SHARED_MODULES = 5
    COMMAND_AND_SCRIPTING_INTERPRETER = 6
    UNIX_SHELL = 7
    RESOURCE_HIJACKING = 8
    PROXY = 9
    EXTERNAL_PROXY = 10
    MULTI_HOP_PROXY = 11
    DYNAMIC_RESOLUTION = 12
    UNSECURED_CREDENTIALS = 13
    VALID_ACCOUNTS = 14
    LOCAL_ACCOUNTS = 15
    CLOUD_ACCOUNTS = 16
    NETWORK_DENIAL_OF_SERVICE = 17
    PERMISSION_GROUPS_DISCOVERY = 18
    CLOUD_GROUPS = 19
    EXFILTRATION_OVER_WEB_SERVICE = 20
    EXFILTRATION_TO_CLOUD_STORAGE = 21
    ACCOUNT_MANIPULATION = 22
    SSH_AUTHORIZED_KEYS = 23
    CREATE_OR_MODIFY_SYSTEM_PROCESS = 24
    STEAL_WEB_SESSION_COOKIE = 25
    MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE = 26
    EXPLOIT_PUBLIC_FACING_APPLICATION = 27
    MODIFY_AUTHENTICATION_PROCESS = 28
    DATA_DESTRUCTION = 29
    DOMAIN_POLICY_MODIFICATION = 30
    IMPAIR_DEFENSES = 31
    NETWORK_SERVICE_DISCOVERY = 32
    ACCESS_TOKEN_MANIPULATION = 33
    ABUSE_ELEVATION_CONTROL_MECHANISM = 34
    DEFAULT_ACCOUNTS = 35
    INHIBIT_SYSTEM_RECOVERY = 36

  class PrimaryTacticValueValuesEnum(_messages.Enum):
    r"""The MITRE ATT&CK tactic most closely represented by this finding, if
    any.

    Values:
      TACTIC_UNSPECIFIED: Unspecified value.
      RECONNAISSANCE: TA0043
      RESOURCE_DEVELOPMENT: TA0042
      INITIAL_ACCESS: TA0001
      EXECUTION: TA0002
      PERSISTENCE: TA0003
      PRIVILEGE_ESCALATION: TA0004
      DEFENSE_EVASION: TA0005
      CREDENTIAL_ACCESS: TA0006
      DISCOVERY: TA0007
      LATERAL_MOVEMENT: TA0008
      COLLECTION: TA0009
      COMMAND_AND_CONTROL: TA0011
      EXFILTRATION: TA0010
      IMPACT: TA0040
    """
    TACTIC_UNSPECIFIED = 0
    RECONNAISSANCE = 1
    RESOURCE_DEVELOPMENT = 2
    INITIAL_ACCESS = 3
    EXECUTION = 4
    PERSISTENCE = 5
    PRIVILEGE_ESCALATION = 6
    DEFENSE_EVASION = 7
    CREDENTIAL_ACCESS = 8
    DISCOVERY = 9
    LATERAL_MOVEMENT = 10
    COLLECTION = 11
    COMMAND_AND_CONTROL = 12
    EXFILTRATION = 13
    IMPACT = 14

  class PrimaryTechniquesValueListEntryValuesEnum(_messages.Enum):
    r"""PrimaryTechniquesValueListEntryValuesEnum enum type.

    Values:
      TECHNIQUE_UNSPECIFIED: Unspecified value.
      ACTIVE_SCANNING: T1595
      SCANNING_IP_BLOCKS: T1595.001
      INGRESS_TOOL_TRANSFER: T1105
      NATIVE_API: T1106
      SHARED_MODULES: T1129
      COMMAND_AND_SCRIPTING_INTERPRETER: T1059
      UNIX_SHELL: T1059.004
      RESOURCE_HIJACKING: T1496
      PROXY: T1090
      EXTERNAL_PROXY: T1090.002
      MULTI_HOP_PROXY: T1090.003
      DYNAMIC_RESOLUTION: T1568
      UNSECURED_CREDENTIALS: T1552
      VALID_ACCOUNTS: T1078
      LOCAL_ACCOUNTS: T1078.003
      CLOUD_ACCOUNTS: T1078.004
      NETWORK_DENIAL_OF_SERVICE: T1498
      PERMISSION_GROUPS_DISCOVERY: T1069
      CLOUD_GROUPS: T1069.003
      EXFILTRATION_OVER_WEB_SERVICE: T1567
      EXFILTRATION_TO_CLOUD_STORAGE: T1567.002
      ACCOUNT_MANIPULATION: T1098
      SSH_AUTHORIZED_KEYS: T1098.004
      CREATE_OR_MODIFY_SYSTEM_PROCESS: T1543
      STEAL_WEB_SESSION_COOKIE: T1539
      MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE: T1578
      EXPLOIT_PUBLIC_FACING_APPLICATION: T1190
      MODIFY_AUTHENTICATION_PROCESS: T1556
      DATA_DESTRUCTION: T1485
      DOMAIN_POLICY_MODIFICATION: T1484
      IMPAIR_DEFENSES: T1562
      NETWORK_SERVICE_DISCOVERY: T1046
      ACCESS_TOKEN_MANIPULATION: T1134
      ABUSE_ELEVATION_CONTROL_MECHANISM: T1548
      DEFAULT_ACCOUNTS: T1078.001
      INHIBIT_SYSTEM_RECOVERY: T1490
    """
    TECHNIQUE_UNSPECIFIED = 0
    ACTIVE_SCANNING = 1
    SCANNING_IP_BLOCKS = 2
    INGRESS_TOOL_TRANSFER = 3
    NATIVE_API = 4
    SHARED_MODULES = 5
    COMMAND_AND_SCRIPTING_INTERPRETER = 6
    UNIX_SHELL = 7
    RESOURCE_HIJACKING = 8
    PROXY = 9
    EXTERNAL_PROXY = 10
    MULTI_HOP_PROXY = 11
    DYNAMIC_RESOLUTION = 12
    UNSECURED_CREDENTIALS = 13
    VALID_ACCOUNTS = 14
    LOCAL_ACCOUNTS = 15
    CLOUD_ACCOUNTS = 16
    NETWORK_DENIAL_OF_SERVICE = 17
    PERMISSION_GROUPS_DISCOVERY = 18
    CLOUD_GROUPS = 19
    EXFILTRATION_OVER_WEB_SERVICE = 20
    EXFILTRATION_TO_CLOUD_STORAGE = 21
    ACCOUNT_MANIPULATION = 22
    SSH_AUTHORIZED_KEYS = 23
    CREATE_OR_MODIFY_SYSTEM_PROCESS = 24
    STEAL_WEB_SESSION_COOKIE = 25
    MODIFY_CLOUD_COMPUTE_INFRASTRUCTURE = 26
    EXPLOIT_PUBLIC_FACING_APPLICATION = 27
    MODIFY_AUTHENTICATION_PROCESS = 28
    DATA_DESTRUCTION = 29
    DOMAIN_POLICY_MODIFICATION = 30
    IMPAIR_DEFENSES = 31
    NETWORK_SERVICE_DISCOVERY = 32
    ACCESS_TOKEN_MANIPULATION = 33
    ABUSE_ELEVATION_CONTROL_MECHANISM = 34
    DEFAULT_ACCOUNTS = 35
    INHIBIT_SYSTEM_RECOVERY = 36

  additionalTactics = _messages.EnumField('AdditionalTacticsValueListEntryValuesEnum', 1, repeated=True)
  additionalTechniques = _messages.EnumField('AdditionalTechniquesValueListEntryValuesEnum', 2, repeated=True)
  primaryTactic = _messages.EnumField('PrimaryTacticValueValuesEnum', 3)
  primaryTechniques = _messages.EnumField('PrimaryTechniquesValueListEntryValuesEnum', 4, repeated=True)
  version = _messages.StringField(5)


class Node(_messages.Message):
  r"""Kubernetes nodes associated with the finding.

  Fields:
    name: [Full resource name](https://google.aip.dev/122#full-resource-names)
      of the Compute Engine VM running the cluster node.
  """

  name = _messages.StringField(1)


class NodePool(_messages.Message):
  r"""Provides GKE node pool information.

  Fields:
    name: Kubernetes node pool name.
    nodes: Nodes associated with the finding.
  """

  name = _messages.StringField(1)
  nodes = _messages.MessageField('Node', 2, repeated=True)


class OnboardingState(_messages.Message):
  r"""Resource capturing onboarding information for a given CRM resource.

  Enums:
    OnboardingLevelValueValuesEnum: Describes the level a given organization,
      folder, or project is onboarded with SCC. If the resource wasn't
      onboarded, NOT_FOUND would have been thrown.

  Fields:
    name: The resource name of the OnboardingState. Format:
      organizations/{organization}/onboardingState Format:
      folders/{folder}/onboardingState Format:
      projects/{project}/onboardingState
    onboardingLevel: Describes the level a given organization, folder, or
      project is onboarded with SCC. If the resource wasn't onboarded,
      NOT_FOUND would have been thrown.
  """

  class OnboardingLevelValueValuesEnum(_messages.Enum):
    r"""Describes the level a given organization, folder, or project is
    onboarded with SCC. If the resource wasn't onboarded, NOT_FOUND would have
    been thrown.

    Values:
      ONBOARDING_LEVEL_UNSPECIFIED: Unused.
      ONBOARDING_LEVEL_PROJECT: This resource is onboarded at the project
        level. Only possible for projects.
      ONBOARDING_LEVEL_ORGANIZATION: This resource is onboarded at the
        organization level. Possible for organizations, folders, and projects.
    """
    ONBOARDING_LEVEL_UNSPECIFIED = 0
    ONBOARDING_LEVEL_PROJECT = 1
    ONBOARDING_LEVEL_ORGANIZATION = 2

  name = _messages.StringField(1)
  onboardingLevel = _messages.EnumField('OnboardingLevelValueValuesEnum', 2)


class Pod(_messages.Message):
  r"""A Kubernetes Pod.

  Fields:
    containers: Pod containers associated with this finding, if any.
    labels: Pod labels. For Kubernetes containers, these are applied to the
      container.
    name: Kubernetes Pod name.
    ns: Kubernetes Pod namespace.
  """

  containers = _messages.MessageField('Container', 1, repeated=True)
  labels = _messages.MessageField('Label', 2, repeated=True)
  name = _messages.StringField(3)
  ns = _messages.StringField(4)


class Process(_messages.Message):
  r"""Represents an operating system process.

  Fields:
    args: Process arguments as JSON encoded strings.
    argumentsTruncated: True if `args` is incomplete.
    binary: File information for the process executable.
    envVariables: Process environment variables.
    envVariablesTruncated: True if `env_variables` is incomplete.
    libraries: File information for libraries loaded by the process.
    name: The process name, as displayed in utilities like `top` and `ps`.
      This name can be accessed through `/proc/[pid]/comm` and changed with
      `prctl(PR_SET_NAME)`.
    parentPid: The parent process ID.
    pid: The process ID.
    script: When the process represents the invocation of a script, `binary`
      provides information about the interpreter, while `script` provides
      information about the script file provided to the interpreter.
  """

  args = _messages.StringField(1, repeated=True)
  argumentsTruncated = _messages.BooleanField(2)
  binary = _messages.MessageField('File', 3)
  envVariables = _messages.MessageField('EnvironmentVariable', 4, repeated=True)
  envVariablesTruncated = _messages.BooleanField(5)
  libraries = _messages.MessageField('File', 6, repeated=True)
  name = _messages.StringField(7)
  parentPid = _messages.IntegerField(8)
  pid = _messages.IntegerField(9)
  script = _messages.MessageField('File', 10)


class ProcessSignature(_messages.Message):
  r"""Indicates what signature matched this process.

  Fields:
    memoryHashSignature: Signature indicating that a binary family was
      matched.
    yaraRuleSignature: Signature indicating that a YARA rule was matched.
  """

  memoryHashSignature = _messages.MessageField('MemoryHashSignature', 1)
  yaraRuleSignature = _messages.MessageField('YaraRuleSignature', 2)


class RapidVulnerabilityDetectionSettings(_messages.Message):
  r"""Resource capturing the settings for the Rapid Vulnerability Detection
  service.

  Enums:
    ServiceEnablementStateValueValuesEnum: The state of enablement for the
      service at its level of the resource hierarchy. A DISABLED state will
      override all module enablement_states to DISABLED.

  Messages:
    ModulesValue: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's.

  Fields:
    modules: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's.
    name: The resource name of the RapidVulnerabilityDetectionSettings.
      Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
    serviceEnablementState: The state of enablement for the service at its
      level of the resource hierarchy. A DISABLED state will override all
      module enablement_states to DISABLED.
    updateTime: Output only. The time the settings were last updated.
  """

  class ServiceEnablementStateValueValuesEnum(_messages.Enum):
    r"""The state of enablement for the service at its level of the resource
    hierarchy. A DISABLED state will override all module enablement_states to
    DISABLED.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ModulesValue(_messages.Message):
    r"""The configurations including the state of enablement for the service's
    different modules. The absence of a module in the map implies its
    configuration is inherited from its parent's.

    Messages:
      AdditionalProperty: An additional property for a ModulesValue object.

    Fields:
      additionalProperties: Additional properties of type ModulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ModulesValue object.

      Fields:
        key: Name of the additional property.
        value: A Config attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Config', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  modules = _messages.MessageField('ModulesValue', 1)
  name = _messages.StringField(2)
  serviceEnablementState = _messages.EnumField('ServiceEnablementStateValueValuesEnum', 3)
  updateTime = _messages.StringField(4)


class Reference(_messages.Message):
  r"""Additional Links

  Fields:
    source: Source of the reference e.g. NVD
    uri: Uri for the mentioned source e.g. https://cve.mitre.org/cgi-
      bin/cvename.cgi?name=CVE-2021-34527.
  """

  source = _messages.StringField(1)
  uri = _messages.StringField(2)


class Role(_messages.Message):
  r"""Kubernetes Role or ClusterRole.

  Enums:
    KindValueValuesEnum: Role type.

  Fields:
    kind: Role type.
    name: Role name.
    ns: Role namespace.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""Role type.

    Values:
      KIND_UNSPECIFIED: Role type is not specified.
      ROLE: Kubernetes Role.
      CLUSTER_ROLE: Kubernetes ClusterRole.
    """
    KIND_UNSPECIFIED = 0
    ROLE = 1
    CLUSTER_ROLE = 2

  kind = _messages.EnumField('KindValueValuesEnum', 1)
  name = _messages.StringField(2)
  ns = _messages.StringField(3)


class SecurityCenterSettings(_messages.Message):
  r"""Resource capturing the settings for Security Center.

  Fields:
    logSinkProject: The resource name of the project to send logs to. This
      project must be part of the organization this resource resides in. The
      format is `projects/{project_id}`. An empty value disables logging. This
      value is only referenced by services that support log sink. Please refer
      to the documentation for an updated list of compatible services. This
      may only be specified for organization level onboarding.
    name: The resource name of the SecurityCenterSettings. Format:
      organizations/{organization}/securityCenterSettings Format:
      folders/{folder}/securityCenterSettings Format:
      projects/{project}/securityCenterSettings
    onboardingTime: Output only. Timestamp of when the customer organization
      was onboarded to SCC.
    orgServiceAccount: Output only. The organization level service account to
      be used for security center components.
  """

  logSinkProject = _messages.StringField(1)
  name = _messages.StringField(2)
  onboardingTime = _messages.StringField(3)
  orgServiceAccount = _messages.StringField(4)


class SecurityHealthAnalyticsSettings(_messages.Message):
  r"""Resource capturing the settings for the Security Health Analytics
  service.

  Enums:
    ServiceEnablementStateValueValuesEnum: The state of enablement for the
      service at its level of the resource hierarchy. A DISABLED state will
      override all module enablement_states to DISABLED.

  Messages:
    ModulesValue: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's.

  Fields:
    modules: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's.
    name: The resource name of the SecurityHealthAnalyticsSettings. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
    serviceAccount: Output only. The service account used by Security Health
      Analytics detectors.
    serviceEnablementState: The state of enablement for the service at its
      level of the resource hierarchy. A DISABLED state will override all
      module enablement_states to DISABLED.
    updateTime: Output only. The time the settings were last updated.
  """

  class ServiceEnablementStateValueValuesEnum(_messages.Enum):
    r"""The state of enablement for the service at its level of the resource
    hierarchy. A DISABLED state will override all module enablement_states to
    DISABLED.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ModulesValue(_messages.Message):
    r"""The configurations including the state of enablement for the service's
    different modules. The absence of a module in the map implies its
    configuration is inherited from its parent's.

    Messages:
      AdditionalProperty: An additional property for a ModulesValue object.

    Fields:
      additionalProperties: Additional properties of type ModulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ModulesValue object.

      Fields:
        key: Name of the additional property.
        value: A Config attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Config', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  modules = _messages.MessageField('ModulesValue', 1)
  name = _messages.StringField(2)
  serviceAccount = _messages.StringField(3)
  serviceEnablementState = _messages.EnumField('ServiceEnablementStateValueValuesEnum', 4)
  updateTime = _messages.StringField(5)


class SecurityMarks(_messages.Message):
  r"""User specified security marks that are attached to the parent Security
  Command Center resource. Security marks are scoped within a Security Command
  Center organization -- they can be modified and viewed by all users who have
  proper permissions on the organization.

  Messages:
    MarksValue: Mutable user specified security marks belonging to the parent
      resource. Constraints are as follows: * Keys and values are treated as
      case insensitive * Keys must be between 1 - 256 characters (inclusive) *
      Keys must be letters, numbers, underscores, or dashes * Values have
      leading and trailing whitespace trimmed, remaining characters must be
      between 1 - 4096 characters (inclusive)

  Fields:
    canonicalName: The canonical name of the marks. Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks"
      "folders/{folder_id}/assets/{asset_id}/securityMarks"
      "projects/{project_number}/assets/{asset_id}/securityMarks" "organizatio
      ns/{organization_id}/sources/{source_id}/findings/{finding_id}/securityM
      arks" "folders/{folder_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks" "projects/{project_number}/sources/{source_id}/findings/{fin
      ding_id}/securityMarks"
    marks: Mutable user specified security marks belonging to the parent
      resource. Constraints are as follows: * Keys and values are treated as
      case insensitive * Keys must be between 1 - 256 characters (inclusive) *
      Keys must be letters, numbers, underscores, or dashes * Values have
      leading and trailing whitespace trimmed, remaining characters must be
      between 1 - 4096 characters (inclusive)
    name: The relative resource name of the SecurityMarks. See:
      https://cloud.google.com/apis/design/resource_names#relative_resource_na
      me Examples:
      "organizations/{organization_id}/assets/{asset_id}/securityMarks" "organ
      izations/{organization_id}/sources/{source_id}/findings/{finding_id}/sec
      urityMarks".
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MarksValue(_messages.Message):
    r"""Mutable user specified security marks belonging to the parent
    resource. Constraints are as follows: * Keys and values are treated as
    case insensitive * Keys must be between 1 - 256 characters (inclusive) *
    Keys must be letters, numbers, underscores, or dashes * Values have
    leading and trailing whitespace trimmed, remaining characters must be
    between 1 - 4096 characters (inclusive)

    Messages:
      AdditionalProperty: An additional property for a MarksValue object.

    Fields:
      additionalProperties: Additional properties of type MarksValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MarksValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  canonicalName = _messages.StringField(1)
  marks = _messages.MessageField('MarksValue', 2)
  name = _messages.StringField(3)


class SecuritycenterFoldersContainerThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterFoldersContainerThreatDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the ContainerThreatDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersEventThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterFoldersEventThreatDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the EventThreatDetectionSettings to calculate.
      Formats: * organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersGetContainerThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersGetContainerThreatDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the ContainerThreatDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersGetEventThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersGetEventThreatDetectionSettingsRequest object.

  Fields:
    name: Required. The name of the EventThreatDetectionSettings to retrieve.
      Formats: * organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersGetOnboardingStateRequest(_messages.Message):
  r"""A SecuritycenterFoldersGetOnboardingStateRequest object.

  Fields:
    name: Required. The name of the OnboardingState to retrieve. Formats: *
      organizations/{organization}/onboardingState *
      folders/{folder}/onboardingState * projects/{project}/onboardingState
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersGetRapidVulnerabilityDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersGetRapidVulnerabilityDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the RapidVulnerabilityDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersGetSecurityCenterSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersGetSecurityCenterSettingsRequest object.

  Fields:
    name: Required. The name of the SecurityCenterSettings to retrieve.
      Format: organizations/{organization}/securityCenterSettings Format:
      folders/{folder}/securityCenterSettings Format:
      projects/{project}/securityCenterSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersGetSecurityHealthAnalyticsSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersGetSecurityHealthAnalyticsSettingsRequest object.

  Fields:
    name: Required. The name of the SecurityHealthAnalyticsSettings to
      retrieve. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersGetVirtualMachineThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersGetVirtualMachineThreatDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the VirtualMachineThreatDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersGetWebSecurityScannerSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersGetWebSecurityScannerSettingsRequest object.

  Fields:
    name: Required. The name of the WebSecurityScannerSettings to retrieve.
      Formats: * organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersRapidVulnerabilityDetectionSettingsCalculateRequest(_messages.Message):
  r"""A
  SecuritycenterFoldersRapidVulnerabilityDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the RapidVulnerabilityDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersSecurityHealthAnalyticsSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterFoldersSecurityHealthAnalyticsSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the SecurityHealthAnalyticsSettings to
      calculate. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersUpdateContainerThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersUpdateContainerThreatDetectionSettingsRequest
  object.

  Fields:
    containerThreatDetectionSettings: A ContainerThreatDetectionSettings
      resource to be passed as the request body.
    name: The resource name of the ContainerThreatDetectionSettings. Formats:
      * organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
    updateMask: The list of fields to be updated.
  """

  containerThreatDetectionSettings = _messages.MessageField('ContainerThreatDetectionSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterFoldersUpdateEventThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersUpdateEventThreatDetectionSettingsRequest object.

  Fields:
    eventThreatDetectionSettings: A EventThreatDetectionSettings resource to
      be passed as the request body.
    name: The resource name of the EventThreatDetectionSettings. Formats: *
      organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
    updateMask: The list of fields to be updated.
  """

  eventThreatDetectionSettings = _messages.MessageField('EventThreatDetectionSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterFoldersUpdateRapidVulnerabilityDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersUpdateRapidVulnerabilityDetectionSettingsRequest
  object.

  Fields:
    name: The resource name of the RapidVulnerabilityDetectionSettings.
      Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
    rapidVulnerabilityDetectionSettings: A RapidVulnerabilityDetectionSettings
      resource to be passed as the request body.
    updateMask: The list of fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  rapidVulnerabilityDetectionSettings = _messages.MessageField('RapidVulnerabilityDetectionSettings', 2)
  updateMask = _messages.StringField(3)


class SecuritycenterFoldersUpdateSecurityHealthAnalyticsSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersUpdateSecurityHealthAnalyticsSettingsRequest
  object.

  Fields:
    name: The resource name of the SecurityHealthAnalyticsSettings. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
    securityHealthAnalyticsSettings: A SecurityHealthAnalyticsSettings
      resource to be passed as the request body.
    updateMask: The list of fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  securityHealthAnalyticsSettings = _messages.MessageField('SecurityHealthAnalyticsSettings', 2)
  updateMask = _messages.StringField(3)


class SecuritycenterFoldersUpdateVirtualMachineThreatDetectionSettingsRequest(_messages.Message):
  r"""A
  SecuritycenterFoldersUpdateVirtualMachineThreatDetectionSettingsRequest
  object.

  Fields:
    name: The resource name of the VirtualMachineThreatDetectionSettings.
      Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
    updateMask: The list of fields to be updated.
    virtualMachineThreatDetectionSettings: A
      VirtualMachineThreatDetectionSettings resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  virtualMachineThreatDetectionSettings = _messages.MessageField('VirtualMachineThreatDetectionSettings', 3)


class SecuritycenterFoldersUpdateWebSecurityScannerSettingsRequest(_messages.Message):
  r"""A SecuritycenterFoldersUpdateWebSecurityScannerSettingsRequest object.

  Fields:
    name: The resource name of the WebSecurityScannerSettings. Formats: *
      organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
    updateMask: The list of fields to be updated.
    webSecurityScannerSettings: A WebSecurityScannerSettings resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  webSecurityScannerSettings = _messages.MessageField('WebSecurityScannerSettings', 3)


class SecuritycenterFoldersVirtualMachineThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A
  SecuritycenterFoldersVirtualMachineThreatDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the VirtualMachineThreatDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterFoldersWebSecurityScannerSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterFoldersWebSecurityScannerSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the WebSecurityScannerSettings to calculate.
      Formats: * organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsContainerThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A
  SecuritycenterOrganizationsContainerThreatDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the ContainerThreatDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsEventThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A
  SecuritycenterOrganizationsEventThreatDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the EventThreatDetectionSettings to calculate.
      Formats: * organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsGetContainerThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsGetContainerThreatDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the ContainerThreatDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsGetEventThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsGetEventThreatDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the EventThreatDetectionSettings to retrieve.
      Formats: * organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsGetOnboardingStateRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsGetOnboardingStateRequest object.

  Fields:
    name: Required. The name of the OnboardingState to retrieve. Formats: *
      organizations/{organization}/onboardingState *
      folders/{folder}/onboardingState * projects/{project}/onboardingState
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsGetRapidVulnerabilityDetectionSettingsRequest(_messages.Message):
  r"""A
  SecuritycenterOrganizationsGetRapidVulnerabilityDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the RapidVulnerabilityDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsGetSecurityCenterSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsGetSecurityCenterSettingsRequest object.

  Fields:
    name: Required. The name of the SecurityCenterSettings to retrieve.
      Format: organizations/{organization}/securityCenterSettings Format:
      folders/{folder}/securityCenterSettings Format:
      projects/{project}/securityCenterSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsGetSecurityHealthAnalyticsSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsGetSecurityHealthAnalyticsSettingsRequest
  object.

  Fields:
    name: Required. The name of the SecurityHealthAnalyticsSettings to
      retrieve. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsGetSubscriptionRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsGetSubscriptionRequest object.

  Fields:
    name: Required. The name of the subscription to retrieve. Format:
      organizations/{organization}/subscription
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsGetVirtualMachineThreatDetectionSettingsRequest(_messages.Message):
  r"""A
  SecuritycenterOrganizationsGetVirtualMachineThreatDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the VirtualMachineThreatDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsGetWebSecurityScannerSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsGetWebSecurityScannerSettingsRequest
  object.

  Fields:
    name: Required. The name of the WebSecurityScannerSettings to retrieve.
      Formats: * organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsRapidVulnerabilityDetectionSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsRapidVulnerabilityDetectionSettingsCalculat
  eRequest object.

  Fields:
    name: Required. The name of the RapidVulnerabilityDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCalculateRequest(_messages.Message):
  r"""A
  SecuritycenterOrganizationsSecurityHealthAnalyticsSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the SecurityHealthAnalyticsSettings to
      calculate. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsUpdateContainerThreatDetectionSettingsRequest(_messages.Message):
  r"""A
  SecuritycenterOrganizationsUpdateContainerThreatDetectionSettingsRequest
  object.

  Fields:
    containerThreatDetectionSettings: A ContainerThreatDetectionSettings
      resource to be passed as the request body.
    name: The resource name of the ContainerThreatDetectionSettings. Formats:
      * organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
    updateMask: The list of fields to be updated.
  """

  containerThreatDetectionSettings = _messages.MessageField('ContainerThreatDetectionSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterOrganizationsUpdateEventThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsUpdateEventThreatDetectionSettingsRequest
  object.

  Fields:
    eventThreatDetectionSettings: A EventThreatDetectionSettings resource to
      be passed as the request body.
    name: The resource name of the EventThreatDetectionSettings. Formats: *
      organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
    updateMask: The list of fields to be updated.
  """

  eventThreatDetectionSettings = _messages.MessageField('EventThreatDetectionSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterOrganizationsUpdateRapidVulnerabilityDetectionSettingsRequest(_messages.Message):
  r"""A
  SecuritycenterOrganizationsUpdateRapidVulnerabilityDetectionSettingsRequest
  object.

  Fields:
    name: The resource name of the RapidVulnerabilityDetectionSettings.
      Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
    rapidVulnerabilityDetectionSettings: A RapidVulnerabilityDetectionSettings
      resource to be passed as the request body.
    updateMask: The list of fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  rapidVulnerabilityDetectionSettings = _messages.MessageField('RapidVulnerabilityDetectionSettings', 2)
  updateMask = _messages.StringField(3)


class SecuritycenterOrganizationsUpdateSecurityHealthAnalyticsSettingsRequest(_messages.Message):
  r"""A
  SecuritycenterOrganizationsUpdateSecurityHealthAnalyticsSettingsRequest
  object.

  Fields:
    name: The resource name of the SecurityHealthAnalyticsSettings. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
    securityHealthAnalyticsSettings: A SecurityHealthAnalyticsSettings
      resource to be passed as the request body.
    updateMask: The list of fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  securityHealthAnalyticsSettings = _messages.MessageField('SecurityHealthAnalyticsSettings', 2)
  updateMask = _messages.StringField(3)


class SecuritycenterOrganizationsUpdateVirtualMachineThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsUpdateVirtualMachineThreatDetectionSettings
  Request object.

  Fields:
    name: The resource name of the VirtualMachineThreatDetectionSettings.
      Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
    updateMask: The list of fields to be updated.
    virtualMachineThreatDetectionSettings: A
      VirtualMachineThreatDetectionSettings resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  virtualMachineThreatDetectionSettings = _messages.MessageField('VirtualMachineThreatDetectionSettings', 3)


class SecuritycenterOrganizationsUpdateWebSecurityScannerSettingsRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsUpdateWebSecurityScannerSettingsRequest
  object.

  Fields:
    name: The resource name of the WebSecurityScannerSettings. Formats: *
      organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
    updateMask: The list of fields to be updated.
    webSecurityScannerSettings: A WebSecurityScannerSettings resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  webSecurityScannerSettings = _messages.MessageField('WebSecurityScannerSettings', 3)


class SecuritycenterOrganizationsVirtualMachineThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsVirtualMachineThreatDetectionSettingsCalcul
  ateRequest object.

  Fields:
    name: Required. The name of the VirtualMachineThreatDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterOrganizationsWebSecurityScannerSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterOrganizationsWebSecurityScannerSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the WebSecurityScannerSettings to calculate.
      Formats: * organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsContainerThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterProjectsContainerThreatDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the ContainerThreatDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsEventThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterProjectsEventThreatDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the EventThreatDetectionSettings to calculate.
      Formats: * organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsGetContainerThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsGetContainerThreatDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the ContainerThreatDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsGetEventThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsGetEventThreatDetectionSettingsRequest object.

  Fields:
    name: Required. The name of the EventThreatDetectionSettings to retrieve.
      Formats: * organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsGetOnboardingStateRequest(_messages.Message):
  r"""A SecuritycenterProjectsGetOnboardingStateRequest object.

  Fields:
    name: Required. The name of the OnboardingState to retrieve. Formats: *
      organizations/{organization}/onboardingState *
      folders/{folder}/onboardingState * projects/{project}/onboardingState
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsGetRapidVulnerabilityDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsGetRapidVulnerabilityDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the RapidVulnerabilityDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsGetSecurityCenterSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsGetSecurityCenterSettingsRequest object.

  Fields:
    name: Required. The name of the SecurityCenterSettings to retrieve.
      Format: organizations/{organization}/securityCenterSettings Format:
      folders/{folder}/securityCenterSettings Format:
      projects/{project}/securityCenterSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsGetSecurityHealthAnalyticsSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsGetSecurityHealthAnalyticsSettingsRequest
  object.

  Fields:
    name: Required. The name of the SecurityHealthAnalyticsSettings to
      retrieve. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsGetVirtualMachineThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsGetVirtualMachineThreatDetectionSettingsRequest
  object.

  Fields:
    name: Required. The name of the VirtualMachineThreatDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsGetWebSecurityScannerSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsGetWebSecurityScannerSettingsRequest object.

  Fields:
    name: Required. The name of the WebSecurityScannerSettings to retrieve.
      Formats: * organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsLocationsClustersContainerThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterProjectsLocationsClustersContainerThreatDetectionSetting
  sCalculateRequest object.

  Fields:
    name: Required. The name of the ContainerThreatDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsLocationsClustersGetContainerThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsLocationsClustersGetContainerThreatDetectionSett
  ingsRequest object.

  Fields:
    name: Required. The name of the ContainerThreatDetectionSettings to
      retrieve. Formats: *
      organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsLocationsClustersUpdateContainerThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsLocationsClustersUpdateContainerThreatDetectionS
  ettingsRequest object.

  Fields:
    containerThreatDetectionSettings: A ContainerThreatDetectionSettings
      resource to be passed as the request body.
    name: The resource name of the ContainerThreatDetectionSettings. Formats:
      * organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
    updateMask: The list of fields to be updated.
  """

  containerThreatDetectionSettings = _messages.MessageField('ContainerThreatDetectionSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterProjectsRapidVulnerabilityDetectionSettingsCalculateRequest(_messages.Message):
  r"""A
  SecuritycenterProjectsRapidVulnerabilityDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the RapidVulnerabilityDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsSecurityHealthAnalyticsSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterProjectsSecurityHealthAnalyticsSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the SecurityHealthAnalyticsSettings to
      calculate. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsUpdateContainerThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsUpdateContainerThreatDetectionSettingsRequest
  object.

  Fields:
    containerThreatDetectionSettings: A ContainerThreatDetectionSettings
      resource to be passed as the request body.
    name: The resource name of the ContainerThreatDetectionSettings. Formats:
      * organizations/{organization}/containerThreatDetectionSettings *
      folders/{folder}/containerThreatDetectionSettings *
      projects/{project}/containerThreatDetectionSettings * projects/{project}
      /locations/{location}/clusters/{cluster}/containerThreatDetectionSetting
      s
    updateMask: The list of fields to be updated.
  """

  containerThreatDetectionSettings = _messages.MessageField('ContainerThreatDetectionSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterProjectsUpdateEventThreatDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsUpdateEventThreatDetectionSettingsRequest
  object.

  Fields:
    eventThreatDetectionSettings: A EventThreatDetectionSettings resource to
      be passed as the request body.
    name: The resource name of the EventThreatDetectionSettings. Formats: *
      organizations/{organization}/eventThreatDetectionSettings *
      folders/{folder}/eventThreatDetectionSettings *
      projects/{project}/eventThreatDetectionSettings
    updateMask: The list of fields to be updated.
  """

  eventThreatDetectionSettings = _messages.MessageField('EventThreatDetectionSettings', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class SecuritycenterProjectsUpdateRapidVulnerabilityDetectionSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsUpdateRapidVulnerabilityDetectionSettingsRequest
  object.

  Fields:
    name: The resource name of the RapidVulnerabilityDetectionSettings.
      Formats: *
      organizations/{organization}/rapidVulnerabilityDetectionSettings *
      folders/{folder}/rapidVulnerabilityDetectionSettings *
      projects/{project}/rapidVulnerabilityDetectionSettings
    rapidVulnerabilityDetectionSettings: A RapidVulnerabilityDetectionSettings
      resource to be passed as the request body.
    updateMask: The list of fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  rapidVulnerabilityDetectionSettings = _messages.MessageField('RapidVulnerabilityDetectionSettings', 2)
  updateMask = _messages.StringField(3)


class SecuritycenterProjectsUpdateSecurityHealthAnalyticsSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsUpdateSecurityHealthAnalyticsSettingsRequest
  object.

  Fields:
    name: The resource name of the SecurityHealthAnalyticsSettings. Formats: *
      organizations/{organization}/securityHealthAnalyticsSettings *
      folders/{folder}/securityHealthAnalyticsSettings *
      projects/{project}/securityHealthAnalyticsSettings
    securityHealthAnalyticsSettings: A SecurityHealthAnalyticsSettings
      resource to be passed as the request body.
    updateMask: The list of fields to be updated.
  """

  name = _messages.StringField(1, required=True)
  securityHealthAnalyticsSettings = _messages.MessageField('SecurityHealthAnalyticsSettings', 2)
  updateMask = _messages.StringField(3)


class SecuritycenterProjectsUpdateVirtualMachineThreatDetectionSettingsRequest(_messages.Message):
  r"""A
  SecuritycenterProjectsUpdateVirtualMachineThreatDetectionSettingsRequest
  object.

  Fields:
    name: The resource name of the VirtualMachineThreatDetectionSettings.
      Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
    updateMask: The list of fields to be updated.
    virtualMachineThreatDetectionSettings: A
      VirtualMachineThreatDetectionSettings resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  virtualMachineThreatDetectionSettings = _messages.MessageField('VirtualMachineThreatDetectionSettings', 3)


class SecuritycenterProjectsUpdateWebSecurityScannerSettingsRequest(_messages.Message):
  r"""A SecuritycenterProjectsUpdateWebSecurityScannerSettingsRequest object.

  Fields:
    name: The resource name of the WebSecurityScannerSettings. Formats: *
      organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
    updateMask: The list of fields to be updated.
    webSecurityScannerSettings: A WebSecurityScannerSettings resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  webSecurityScannerSettings = _messages.MessageField('WebSecurityScannerSettings', 3)


class SecuritycenterProjectsVirtualMachineThreatDetectionSettingsCalculateRequest(_messages.Message):
  r"""A
  SecuritycenterProjectsVirtualMachineThreatDetectionSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the VirtualMachineThreatDetectionSettings to
      calculate. Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
  """

  name = _messages.StringField(1, required=True)


class SecuritycenterProjectsWebSecurityScannerSettingsCalculateRequest(_messages.Message):
  r"""A SecuritycenterProjectsWebSecurityScannerSettingsCalculateRequest
  object.

  Fields:
    name: Required. The name of the WebSecurityScannerSettings to calculate.
      Formats: * organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
  """

  name = _messages.StringField(1, required=True)


class ServiceAccountDelegationInfo(_messages.Message):
  r"""Identity delegation history of an authenticated service account.

  Fields:
    principalEmail: The email address of a Google account.
    principalSubject: A string representing the principal_subject associated
      with the identity. As compared to `principal_email`, supports principals
      that aren't associated with email addresses, such as third party
      principals. For most identities, the format will be
      `principal://iam.googleapis.com/{identity pool name}/subjects/{subject}`
      except for some GKE identities (GKE_WORKLOAD, FREEFORM,
      GKE_HUB_WORKLOAD) that are still in the legacy format
      `serviceAccount:{identity pool name}[{subject}]`
  """

  principalEmail = _messages.StringField(1)
  principalSubject = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Subject(_messages.Message):
  r"""Represents a Kubernetes subject.

  Enums:
    KindValueValuesEnum: Authentication type for the subject.

  Fields:
    kind: Authentication type for the subject.
    name: Name for the subject.
    ns: Namespace for the subject.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""Authentication type for the subject.

    Values:
      AUTH_TYPE_UNSPECIFIED: Authentication is not specified.
      USER: User with valid certificate.
      SERVICEACCOUNT: Users managed by Kubernetes API with credentials stored
        as secrets.
      GROUP: Collection of users.
    """
    AUTH_TYPE_UNSPECIFIED = 0
    USER = 1
    SERVICEACCOUNT = 2
    GROUP = 3

  kind = _messages.EnumField('KindValueValuesEnum', 1)
  name = _messages.StringField(2)
  ns = _messages.StringField(3)


class Subscription(_messages.Message):
  r"""Resource capturing the state of an organization's subscription.

  Enums:
    TierValueValuesEnum: The tier of SCC features this organization currently
      has access to.

  Fields:
    details: The details of the most recent active subscription. If there has
      never been a subscription this will be empty.
    name: The resource name of the subscription. Format:
      organizations/{organization}/subscription
    tier: The tier of SCC features this organization currently has access to.
  """

  class TierValueValuesEnum(_messages.Enum):
    r"""The tier of SCC features this organization currently has access to.

    Values:
      TIER_UNSPECIFIED: Default value. This value is unused.
      STANDARD: The standard tier.
      PREMIUM: The premium tier.
    """
    TIER_UNSPECIFIED = 0
    STANDARD = 1
    PREMIUM = 2

  details = _messages.MessageField('Details', 1)
  name = _messages.StringField(2)
  tier = _messages.EnumField('TierValueValuesEnum', 3)


class VirtualMachineThreatDetectionSettings(_messages.Message):
  r"""Resource capturing the settings for the Virtual Machine Threat Detection
  service.

  Enums:
    ServiceEnablementStateValueValuesEnum: The state of enablement for the
      service at its level of the resource hierarchy. A DISABLED state will
      override all module enablement_states to DISABLED.

  Messages:
    ModulesValue: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's.

  Fields:
    modules: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's.
    name: The resource name of the VirtualMachineThreatDetectionSettings.
      Formats: *
      organizations/{organization}/virtualMachineThreatDetectionSettings *
      folders/{folder}/virtualMachineThreatDetectionSettings *
      projects/{project}/virtualMachineThreatDetectionSettings
    serviceAccount: Output only. The service account used by Virtual Machine
      Threat Detection detectors.
    serviceEnablementState: The state of enablement for the service at its
      level of the resource hierarchy. A DISABLED state will override all
      module enablement_states to DISABLED.
    updateTime: Output only. The time the settings were last updated.
  """

  class ServiceEnablementStateValueValuesEnum(_messages.Enum):
    r"""The state of enablement for the service at its level of the resource
    hierarchy. A DISABLED state will override all module enablement_states to
    DISABLED.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ModulesValue(_messages.Message):
    r"""The configurations including the state of enablement for the service's
    different modules. The absence of a module in the map implies its
    configuration is inherited from its parent's.

    Messages:
      AdditionalProperty: An additional property for a ModulesValue object.

    Fields:
      additionalProperties: Additional properties of type ModulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ModulesValue object.

      Fields:
        key: Name of the additional property.
        value: A Config attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Config', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  modules = _messages.MessageField('ModulesValue', 1)
  name = _messages.StringField(2)
  serviceAccount = _messages.StringField(3)
  serviceEnablementState = _messages.EnumField('ServiceEnablementStateValueValuesEnum', 4)
  updateTime = _messages.StringField(5)


class Vulnerability(_messages.Message):
  r"""Refers to common vulnerability fields e.g. cve, cvss, cwe etc.

  Fields:
    cve: CVE stands for Common Vulnerabilities and Exposures
      (https://cve.mitre.org/about/)
  """

  cve = _messages.MessageField('Cve', 1)


class WebSecurityScannerSettings(_messages.Message):
  r"""Resource capturing the settings for the Web Security Scanner service.

  Enums:
    ServiceEnablementStateValueValuesEnum: The state of enablement for the
      service at its level of the resource hierarchy. A DISABLED state will
      override all module enablement_states to DISABLED.

  Messages:
    ModulesValue: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's.

  Fields:
    modules: The configurations including the state of enablement for the
      service's different modules. The absence of a module in the map implies
      its configuration is inherited from its parent's.
    name: The resource name of the WebSecurityScannerSettings. Formats: *
      organizations/{organization}/webSecurityScannerSettings *
      folders/{folder}/webSecurityScannerSettings *
      projects/{project}/webSecurityScannerSettings
    serviceEnablementState: The state of enablement for the service at its
      level of the resource hierarchy. A DISABLED state will override all
      module enablement_states to DISABLED.
    updateTime: Output only. The time the settings were last updated.
  """

  class ServiceEnablementStateValueValuesEnum(_messages.Enum):
    r"""The state of enablement for the service at its level of the resource
    hierarchy. A DISABLED state will override all module enablement_states to
    DISABLED.

    Values:
      ENABLEMENT_STATE_UNSPECIFIED: Default value. This value is unused.
      INHERITED: State is inherited from the parent resource.
      ENABLED: State is enabled.
      DISABLED: State is disabled.
    """
    ENABLEMENT_STATE_UNSPECIFIED = 0
    INHERITED = 1
    ENABLED = 2
    DISABLED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ModulesValue(_messages.Message):
    r"""The configurations including the state of enablement for the service's
    different modules. The absence of a module in the map implies its
    configuration is inherited from its parent's.

    Messages:
      AdditionalProperty: An additional property for a ModulesValue object.

    Fields:
      additionalProperties: Additional properties of type ModulesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ModulesValue object.

      Fields:
        key: Name of the additional property.
        value: A Config attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('Config', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  modules = _messages.MessageField('ModulesValue', 1)
  name = _messages.StringField(2)
  serviceEnablementState = _messages.EnumField('ServiceEnablementStateValueValuesEnum', 3)
  updateTime = _messages.StringField(4)


class YaraRuleSignature(_messages.Message):
  r"""A signature corresponding to a YARA rule.

  Fields:
    yaraRule: The name of the YARA rule.
  """

  yaraRule = _messages.StringField(1)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

"""Generated message classes for vmwareengine version v1.

The Google VMware Engine API lets you programmatically manage VMware
environments.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'vmwareengine'


class AnyDomainController(_messages.Message):
  r"""Message that should be set to indicate use of any domain controller."""


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class Cluster(_messages.Message):
  r"""A cluster in a private cloud.

  Enums:
    StateValueValuesEnum: Output only. State of the resource.

  Messages:
    NodeTypeConfigsValue: Required. The map of cluster node types in this
      cluster, where the key is canonical identifier of the node type
      (corresponds to the `NodeType`).

  Fields:
    createTime: Output only. Creation time of this resource.
    management: Output only. True if the cluster is a management cluster;
      false otherwise. There can only be one management cluster in a private
      cloud and it has to be the first one.
    name: Output only. The resource name of this cluster. Resource names are
      schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/clusters/my-cluster`
    nodeCount: Optional. Deprecated: Number of nodes in this cluster.
    nodeCustomCoreCount: Optional. Deprecated: Customized number of cores
      available to each node of the cluster. This number must always be one of
      `nodeType.availableCustomCoreCounts`. If zero is provided max value from
      `nodeType.availableCustomCoreCounts` will be used.
    nodeTypeConfigs: Required. The map of cluster node types in this cluster,
      where the key is canonical identifier of the node type (corresponds to
      the `NodeType`).
    nodeTypeId: Optional. Deprecated: The canonical identifier of node types
      (`NodeType`) in this cluster. For example: standard-72.
    state: Output only. State of the resource.
    stretchedClusterConfig: Optional. Configuration of a stretched cluster.
      Required for clusters that belong to a STRETCHED private cloud.
    uid: Output only. System-generated unique identifier for the resource.
    updateTime: Output only. Last update time of this resource.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the resource.

    Values:
      STATE_UNSPECIFIED: The default value. This value should never be used.
      ACTIVE: The Cluster is operational and can be used by the user.
      CREATING: The Cluster is being deployed.
      UPDATING: Adding or removing of a node to the cluster, any other cluster
        specific updates.
      DELETING: The Cluster is being deleted.
      REPAIRING: The Cluster is undergoing maintenance, for example: a failed
        node is getting replaced.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    UPDATING = 3
    DELETING = 4
    REPAIRING = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NodeTypeConfigsValue(_messages.Message):
    r"""Required. The map of cluster node types in this cluster, where the key
    is canonical identifier of the node type (corresponds to the `NodeType`).

    Messages:
      AdditionalProperty: An additional property for a NodeTypeConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type NodeTypeConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NodeTypeConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A NodeTypeConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('NodeTypeConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  management = _messages.BooleanField(2)
  name = _messages.StringField(3)
  nodeCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  nodeCustomCoreCount = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  nodeTypeConfigs = _messages.MessageField('NodeTypeConfigsValue', 6)
  nodeTypeId = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  stretchedClusterConfig = _messages.MessageField('StretchedClusterConfig', 9)
  uid = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class Credentials(_messages.Message):
  r"""Credentials for a private cloud.

  Fields:
    password: Initial password.
    username: Initial username.
  """

  password = _messages.StringField(1)
  username = _messages.StringField(2)


class DnsBindPermission(_messages.Message):
  r"""DnsBindPermission resource that contains the accounts having the
  consumer DNS bind permission on the corresponding intranet VPC of the
  consumer project.

  Fields:
    etag: Checksum that may be sent on grant and revoke requests to ensure
      that the user-provided value is up to date before the server processes a
      request. The server computes checksums based on the value of other
      fields in the request.
    name: Required. Output only. The name of the resource which stores the
      users/service accounts having the permission to bind to the
      corresponding intranet VPC of the consumer project. DnsBindPermission is
      a global resource. Resource names are schemeless URIs that follow the
      conventions in https://cloud.google.com/apis/design/resource_names. For
      example: `projects/my-project/locations/global/dnsBindPermission`
    principals: Output only. Users/Service accounts which have access for
      binding on the intranet VPC project corresponding to the consumer
      project.
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2)
  principals = _messages.MessageField('Principal', 3, repeated=True)


class DnsForwarding(_messages.Message):
  r"""DNS forwarding config. This config defines a list of domain to name
  server mappings, and is attached to the private cloud for custom domain
  resolution.

  Fields:
    createTime: Output only. Creation time of this resource.
    etag: Checksum that may be sent on update requests to ensure that the
      user-provided value is up to date before the server processes a request.
      The server computes checksums based on the value of other fields in the
      request.
    forwardingRules: Required. List of domain mappings to configure
    name: Output only. The resource name of this DNS profile. Resource names
      are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/dnsForwarding`
    updateTime: Output only. Last update time of this resource.
  """

  createTime = _messages.StringField(1)
  etag = _messages.StringField(2)
  forwardingRules = _messages.MessageField('ForwardingRule', 3, repeated=True)
  name = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class ExternalAccessRule(_messages.Message):
  r"""External access firewall rules for filtering incoming traffic destined
  to `ExternalAddress` resources.

  Enums:
    ActionValueValuesEnum: The action that the external access rule performs.
    StateValueValuesEnum: Output only. The state of the resource.

  Fields:
    action: The action that the external access rule performs.
    createTime: Output only. Creation time of this resource.
    description: User-provided description for this external access rule.
    destinationIpRanges: If destination ranges are specified, the external
      access rule applies only to the traffic that has a destination IP
      address in these ranges. The specified IP addresses must have reserved
      external IP addresses in the scope of the parent network policy. To
      match all external IP addresses in the scope of the parent network
      policy, specify `0.0.0.0/0`. To match a specific external IP address,
      specify it using the `IpRange.external_address` property.
    destinationPorts: A list of destination ports to which the external access
      rule applies. This field is only applicable for the UDP or TCP protocol.
      Each entry must be either an integer or a range. For example: `["22"]`,
      `["80","443"]`, or `["12345-12349"]`. To match all destination ports,
      specify `["0-65535"]`.
    ipProtocol: The IP protocol to which the external access rule applies.
      This value can be one of the following three protocol strings (not case-
      sensitive): `tcp`, `udp`, or `icmp`.
    name: Output only. The resource name of this external access rule.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/networkPolicies/my-
      policy/externalAccessRules/my-rule`
    priority: External access rule priority, which determines the external
      access rule to use when multiple rules apply. If multiple rules have the
      same priority, their ordering is non-deterministic. If specific ordering
      is required, assign unique priorities to enforce such ordering. The
      external access rule priority is an integer from 100 to 4096, both
      inclusive. Lower integers indicate higher precedence. For example, a
      rule with priority `100` has higher precedence than a rule with priority
      `101`.
    sourceIpRanges: If source ranges are specified, the external access rule
      applies only to traffic that has a source IP address in these ranges.
      These ranges can either be expressed in the CIDR format or as an IP
      address. As only inbound rules are supported, `ExternalAddress`
      resources cannot be the source IP addresses of an external access rule.
      To match all source addresses, specify `0.0.0.0/0`.
    sourcePorts: A list of source ports to which the external access rule
      applies. This field is only applicable for the UDP or TCP protocol. Each
      entry must be either an integer or a range. For example: `["22"]`,
      `["80","443"]`, or `["12345-12349"]`. To match all source ports, specify
      `["0-65535"]`.
    state: Output only. The state of the resource.
    uid: Output only. System-generated unique identifier for the resource.
    updateTime: Output only. Last update time of this resource.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""The action that the external access rule performs.

    Values:
      ACTION_UNSPECIFIED: Defaults to allow.
      ALLOW: Allows connections that match the other specified components.
      DENY: Blocks connections that match the other specified components.
    """
    ACTION_UNSPECIFIED = 0
    ALLOW = 1
    DENY = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the resource.

    Values:
      STATE_UNSPECIFIED: The default value. This value is used if the state is
        omitted.
      ACTIVE: The rule is ready.
      CREATING: The rule is being created.
      UPDATING: The rule is being updated.
      DELETING: The rule is being deleted.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    UPDATING = 3
    DELETING = 4

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  destinationIpRanges = _messages.MessageField('IpRange', 4, repeated=True)
  destinationPorts = _messages.StringField(5, repeated=True)
  ipProtocol = _messages.StringField(6)
  name = _messages.StringField(7)
  priority = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  sourceIpRanges = _messages.MessageField('IpRange', 9, repeated=True)
  sourcePorts = _messages.StringField(10, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 11)
  uid = _messages.StringField(12)
  updateTime = _messages.StringField(13)


class ExternalAddress(_messages.Message):
  r"""Represents an allocated external IP address and its corresponding
  internal IP address in a private cloud.

  Enums:
    StateValueValuesEnum: Output only. The state of the resource.

  Fields:
    createTime: Output only. Creation time of this resource.
    description: User-provided description for this resource.
    externalIp: Output only. The external IP address of a workload VM.
    internalIp: The internal IP address of a workload VM.
    name: Output only. The resource name of this external IP address. Resource
      names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/externalAddresses/my-address`
    state: Output only. The state of the resource.
    uid: Output only. System-generated unique identifier for the resource.
    updateTime: Output only. Last update time of this resource.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the resource.

    Values:
      STATE_UNSPECIFIED: The default value. This value should never be used.
      ACTIVE: The address is ready.
      CREATING: The address is being created.
      UPDATING: The address is being updated.
      DELETING: The address is being deleted.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    UPDATING = 3
    DELETING = 4

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  externalIp = _messages.StringField(3)
  internalIp = _messages.StringField(4)
  name = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  uid = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class FetchNetworkPolicyExternalAddressesResponse(_messages.Message):
  r"""Response message for VmwareEngine.FetchNetworkPolicyExternalAddresses

  Fields:
    externalAddresses: A list of external IP addresses assigned to VMware
      workload VMs within the scope of the given network policy.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  externalAddresses = _messages.MessageField('ExternalAddress', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ForwardingRule(_messages.Message):
  r"""A forwarding rule is a mapping of a `domain` to `name_servers`. This
  mapping allows VMware Engine to resolve domains for attached private clouds
  by forwarding DNS requests for a given domain to the specified nameservers.

  Fields:
    domain: Required. Domain used to resolve a `name_servers` list.
    nameServers: Required. List of DNS servers to use for domain resolution
  """

  domain = _messages.StringField(1)
  nameServers = _messages.StringField(2, repeated=True)


class GrantDnsBindPermissionRequest(_messages.Message):
  r"""Request message for VmwareEngine.GrantDnsBindPermission

  Fields:
    etag: Optional. Checksum used to ensure that the user-provided value is up
      to date before the server processes the request. The server compares
      provided checksum with the current checksum of the resource. If the
      user-provided value is out of date, this request returns an `ABORTED`
      error.
    principal: Required. The consumer provided user/service account which
      needs to be granted permission to bind with the intranet VPC
      corresponding to the consumer project.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  etag = _messages.StringField(1)
  principal = _messages.MessageField('Principal', 2)
  requestId = _messages.StringField(3)


class Hcx(_messages.Message):
  r"""Details about a HCX Cloud Manager appliance.

  Enums:
    StateValueValuesEnum: Output only. The state of the appliance.

  Fields:
    fqdn: Fully qualified domain name of the appliance.
    internalIp: Internal IP address of the appliance.
    state: Output only. The state of the appliance.
    version: Version of the appliance.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the appliance.

    Values:
      STATE_UNSPECIFIED: Unspecified appliance state. This is the default
        value.
      ACTIVE: The appliance is operational and can be used.
      CREATING: The appliance is being deployed.
      ACTIVATING: The appliance is being activated.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    ACTIVATING = 3

  fqdn = _messages.StringField(1)
  internalIp = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  version = _messages.StringField(4)


class HcxActivationKey(_messages.Message):
  r"""HCX activation key. A default key is created during private cloud
  provisioning, but this behavior is subject to change and you should always
  verify active keys. Use VmwareEngine.ListHcxActivationKeys to retrieve
  existing keys and VmwareEngine.CreateHcxActivationKey to create new ones.

  Enums:
    StateValueValuesEnum: Output only. State of HCX activation key.

  Fields:
    activationKey: Output only. HCX activation key.
    createTime: Output only. Creation time of HCX activation key.
    name: Output only. The resource name of this HcxActivationKey. Resource
      names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/privateClouds/my-
      cloud/hcxActivationKeys/my-key`
    state: Output only. State of HCX activation key.
    uid: Output only. System-generated unique identifier for the resource.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of HCX activation key.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      AVAILABLE: State of a newly generated activation key.
      CONSUMED: State of key when it has been used to activate HCX appliance.
      CREATING: State of key when it is being created.
    """
    STATE_UNSPECIFIED = 0
    AVAILABLE = 1
    CONSUMED = 2
    CREATING = 3

  activationKey = _messages.StringField(1)
  createTime = _messages.StringField(2)
  name = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  uid = _messages.StringField(5)


class IdentitySource(_messages.Message):
  r"""The VMware identity source in a private cloud. The identity source
  defines the Active Directory domain that you can configure and use for
  authentication. Currently, the only supported identity source type is Active
  Directory over LDAP.

  Enums:
    ApplianceTypeValueValuesEnum: Required. The appliance type of identity
      source. Can be vCenter or NSX-T.
    ProtocolValueValuesEnum: Required. The LDAP server connection protocol.
    StateValueValuesEnum: Output only. The state of identity source.

  Fields:
    anyDomainController: Any domain controller.
    applianceType: Required. The appliance type of identity source. Can be
      vCenter or NSX-T.
    baseGroupsDn: Required. The base distinguished name for groups.
    baseUsersDn: Required. The base distinguished name for users.
    createTime: Output only. Creation time of this resource.
    domain: Required. The domain name of the identity source.
    domainAlias: Optional. The domain alias of the identity source.
    domainPassword: Required. Input only. Input only and required. Password of
      the user in the domain who has a minimum of read-only access to the base
      distinguished names of users and groups.
    domainUser: Required. ID of a user in the domain who has a minimum of
      read-only access to the base distinguished names of users and groups.
    etag: Optional. Checksum that may be sent on update and delete requests to
      ensure that the user-provided value is up to date before the server
      processes a request. The server computes checksums based on the value of
      other fields in the request.
    name: Output only. The resource name of this identity source. Resource
      names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/identitySources/my-identity-source`
    protocol: Required. The LDAP server connection protocol.
    specificDomainControllers: Specific domain controllers.
    sslCertificates: Optional. Input only. The root CA certificate files in
      CER format for the LDAPS server.
    state: Output only. The state of identity source.
    uid: Output only. System-generated unique identifier for the resource.
    updateTime: Output only. Last update time of this resource.
    vmwareIdentitySource: Output only. The name of the identity source in
      VMware vCenter.
  """

  class ApplianceTypeValueValuesEnum(_messages.Enum):
    r"""Required. The appliance type of identity source. Can be vCenter or
    NSX-T.

    Values:
      APPLIANCE_TYPE_UNSPECIFIED: The default value. This value should never
        be used.
      VCENTER: A vCenter appliance.
    """
    APPLIANCE_TYPE_UNSPECIFIED = 0
    VCENTER = 1

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""Required. The LDAP server connection protocol.

    Values:
      PROTOCOL_UNSPECIFIED: The default value. This value should never be
        used.
      LDAP: A LDAP protocol.
      LDAPS: A LDAPS protocol.
    """
    PROTOCOL_UNSPECIFIED = 0
    LDAP = 1
    LDAPS = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of identity source.

    Values:
      STATE_UNSPECIFIED: The default value. This value should never be used.
      ACTIVE: The identity source is ready.
      CREATING: The identity source is being created.
      DELETING: The identity source is being deleted.
      UPDATING: The identity source is being updated.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    DELETING = 3
    UPDATING = 4

  anyDomainController = _messages.MessageField('AnyDomainController', 1)
  applianceType = _messages.EnumField('ApplianceTypeValueValuesEnum', 2)
  baseGroupsDn = _messages.StringField(3)
  baseUsersDn = _messages.StringField(4)
  createTime = _messages.StringField(5)
  domain = _messages.StringField(6)
  domainAlias = _messages.StringField(7)
  domainPassword = _messages.StringField(8)
  domainUser = _messages.StringField(9)
  etag = _messages.StringField(10)
  name = _messages.StringField(11)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 12)
  specificDomainControllers = _messages.MessageField('SpecificDomainControllers', 13)
  sslCertificates = _messages.StringField(14, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 15)
  uid = _messages.StringField(16)
  updateTime = _messages.StringField(17)
  vmwareIdentitySource = _messages.StringField(18)


class IpRange(_messages.Message):
  r"""An IP range provided in any one of the supported formats.

  Fields:
    externalAddress: The name of an `ExternalAddress` resource. The external
      address must have been reserved in the scope of this external access
      rule's parent network policy. Provide the external address name in the
      form of `projects/{project}/locations/{location}/privateClouds/{private_
      cloud}/externalAddresses/{external_address}`. For example: `projects/my-
      project/locations/us-central1-a/privateClouds/my-
      cloud/externalAddresses/my-address`.
    ipAddress: A single IP address. For example: `********`.
    ipAddressRange: An IP address range in the CIDR format. For example:
      `10.0.0.0/24`.
  """

  externalAddress = _messages.StringField(1)
  ipAddress = _messages.StringField(2)
  ipAddressRange = _messages.StringField(3)


class ListClustersResponse(_messages.Message):
  r"""Response message for VmwareEngine.ListClusters

  Fields:
    clusters: A list of private cloud clusters.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached when making an aggregated
      query using wildcards.
  """

  clusters = _messages.MessageField('Cluster', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListExternalAccessRulesResponse(_messages.Message):
  r"""Response message for VmwareEngine.ListExternalAccessRules

  Fields:
    externalAccessRules: A list of external access firewall rules.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached when making an aggregated
      query using wildcards.
  """

  externalAccessRules = _messages.MessageField('ExternalAccessRule', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListExternalAddressesResponse(_messages.Message):
  r"""Response message for VmwareEngine.ListExternalAddresses

  Fields:
    externalAddresses: A list of external IP addresses.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached when making an aggregated
      query using wildcards.
  """

  externalAddresses = _messages.MessageField('ExternalAddress', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListHcxActivationKeysResponse(_messages.Message):
  r"""Response message for VmwareEngine.ListHcxActivationKeys

  Fields:
    hcxActivationKeys: List of HCX activation keys.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached when making an aggregated
      query using wildcards.
  """

  hcxActivationKeys = _messages.MessageField('HcxActivationKey', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListIdentitySourcesResponse(_messages.Message):
  r"""Response message for VmwareEngine.ListIdentitySources

  Fields:
    identitySources: A list of private cloud identity sources.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached when making an aggregated
      query using wildcards.
  """

  identitySources = _messages.MessageField('IdentitySource', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLoggingServersResponse(_messages.Message):
  r"""Response message for VmwareEngine.ListLoggingServers

  Fields:
    loggingServers: A list of Logging Servers.
    nextPageToken: A token, which can be send as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached when making an aggregated
      query using wildcards.
  """

  loggingServers = _messages.MessageField('LoggingServer', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListManagementDnsZoneBindingsResponse(_messages.Message):
  r"""Response message for VmwareEngine.ListManagementDnsZoneBindings

  Fields:
    managementDnsZoneBindings: A list of management DNS zone bindings.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached when making an aggregated
      query using wildcards.
  """

  managementDnsZoneBindings = _messages.MessageField('ManagementDnsZoneBinding', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListNetworkPeeringsResponse(_messages.Message):
  r"""Response message for VmwareEngine.ListNetworkPeerings

  Fields:
    networkPeerings: A list of network peerings.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Unreachable resources.
  """

  networkPeerings = _messages.MessageField('NetworkPeering', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListNetworkPoliciesResponse(_messages.Message):
  r"""Response message for VmwareEngine.ListNetworkPolicies

  Fields:
    networkPolicies: A list of network policies.
    nextPageToken: A token, which can be send as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached when making an aggregated
      query using wildcards.
  """

  networkPolicies = _messages.MessageField('NetworkPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListNodeTypesResponse(_messages.Message):
  r"""Response message for VmwareEngine.ListNodeTypes

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    nodeTypes: A list of Node Types.
    unreachable: Locations that could not be reached when making an aggregated
      query using wildcards.
  """

  nextPageToken = _messages.StringField(1)
  nodeTypes = _messages.MessageField('NodeType', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListNodesResponse(_messages.Message):
  r"""Response message for VmwareEngine.ListNodes

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    nodes: The nodes.
  """

  nextPageToken = _messages.StringField(1)
  nodes = _messages.MessageField('Node', 2, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListPeeringRoutesResponse(_messages.Message):
  r"""Response message for VmwareEngine.ListPeeringRoutes

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    peeringRoutes: A list of peering routes.
  """

  nextPageToken = _messages.StringField(1)
  peeringRoutes = _messages.MessageField('PeeringRoute', 2, repeated=True)


class ListPrivateCloudsResponse(_messages.Message):
  r"""Response message for VmwareEngine.ListPrivateClouds

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    privateClouds: A list of private clouds.
    unreachable: Locations that could not be reached when making an aggregated
      query using wildcards.
  """

  nextPageToken = _messages.StringField(1)
  privateClouds = _messages.MessageField('PrivateCloud', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListPrivateConnectionPeeringRoutesResponse(_messages.Message):
  r"""Response message for VmwareEngine.ListPrivateConnectionPeeringRoutes

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    peeringRoutes: A list of peering routes.
  """

  nextPageToken = _messages.StringField(1)
  peeringRoutes = _messages.MessageField('PeeringRoute', 2, repeated=True)


class ListPrivateConnectionsResponse(_messages.Message):
  r"""Response message for VmwareEngine.ListPrivateConnections

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    privateConnections: A list of private connections.
    unreachable: Unreachable resources.
  """

  nextPageToken = _messages.StringField(1)
  privateConnections = _messages.MessageField('PrivateConnection', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListSubnetsResponse(_messages.Message):
  r"""Response message for VmwareEngine.ListSubnets

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    subnets: A list of subnets.
    unreachable: Locations that could not be reached when making an aggregated
      query using wildcards.
  """

  nextPageToken = _messages.StringField(1)
  subnets = _messages.MessageField('Subnet', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListVmwareEngineNetworksResponse(_messages.Message):
  r"""Response message for VmwareEngine.ListVmwareEngineNetworks

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Unreachable resources.
    vmwareEngineNetworks: A list of VMware Engine networks.
  """

  nextPageToken = _messages.StringField(1)
  unreachable = _messages.StringField(2, repeated=True)
  vmwareEngineNetworks = _messages.MessageField('VmwareEngineNetwork', 3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LocationMetadata(_messages.Message):
  r"""VmwareEngine specific metadata for the given
  google.cloud.location.Location. It is returned as a content of the
  `google.cloud.location.Location.metadata` field.

  Enums:
    CapabilitiesValueListEntryValuesEnum:

  Fields:
    capabilities: Output only. Capabilities of this location.
  """

  class CapabilitiesValueListEntryValuesEnum(_messages.Enum):
    r"""CapabilitiesValueListEntryValuesEnum enum type.

    Values:
      CAPABILITY_UNSPECIFIED: The default value. This value is used if the
        capability is omitted or unknown.
      STRETCHED_CLUSTERS: Stretch clusters are supported in this location.
    """
    CAPABILITY_UNSPECIFIED = 0
    STRETCHED_CLUSTERS = 1

  capabilities = _messages.EnumField('CapabilitiesValueListEntryValuesEnum', 1, repeated=True)


class LoggingServer(_messages.Message):
  r"""Logging server to receive vCenter or ESXi logs.

  Enums:
    ProtocolValueValuesEnum: Required. Protocol used by vCenter to send logs
      to a logging server.
    SourceTypeValueValuesEnum: Required. The type of component that produces
      logs that will be forwarded to this logging server.

  Fields:
    createTime: Output only. Creation time of this resource.
    etag: Optional. Checksum that may be sent on update and delete requests to
      ensure that the user-provided value is up to date before the server
      processes a request. The server computes checksums based on the value of
      other fields in the request.
    hostname: Required. Fully-qualified domain name (FQDN) or IP Address of
      the logging server.
    name: Output only. The resource name of this logging server. Resource
      names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/loggingServers/my-logging-server`
    port: Required. Port number at which the logging server receives logs.
    protocol: Required. Protocol used by vCenter to send logs to a logging
      server.
    sourceType: Required. The type of component that produces logs that will
      be forwarded to this logging server.
    uid: Output only. System-generated unique identifier for the resource.
    updateTime: Output only. Last update time of this resource.
  """

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""Required. Protocol used by vCenter to send logs to a logging server.

    Values:
      PROTOCOL_UNSPECIFIED: Unspecified communications protocol. This is the
        default value.
      UDP: UDP
      TCP: TCP
    """
    PROTOCOL_UNSPECIFIED = 0
    UDP = 1
    TCP = 2

  class SourceTypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of component that produces logs that will be
    forwarded to this logging server.

    Values:
      SOURCE_TYPE_UNSPECIFIED: The default value. This value should never be
        used.
      ESXI: Logs produced by ESXI hosts
      VCSA: Logs produced by vCenter server
    """
    SOURCE_TYPE_UNSPECIFIED = 0
    ESXI = 1
    VCSA = 2

  createTime = _messages.StringField(1)
  etag = _messages.StringField(2)
  hostname = _messages.StringField(3)
  name = _messages.StringField(4)
  port = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 6)
  sourceType = _messages.EnumField('SourceTypeValueValuesEnum', 7)
  uid = _messages.StringField(8)
  updateTime = _messages.StringField(9)


class ManagementCluster(_messages.Message):
  r"""Management cluster configuration.

  Messages:
    NodeTypeConfigsValue: Required. The map of cluster node types in this
      cluster, where the key is canonical identifier of the node type
      (corresponds to the `NodeType`).

  Fields:
    clusterId: Required. The user-provided identifier of the new `Cluster`.
      The identifier must meet the following requirements: * Only contains
      1-63 alphanumeric characters and hyphens * Begins with an alphabetical
      character * Ends with a non-hyphen character * Not formatted as a UUID *
      Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034)
      (section 3.5)
    nodeCount: Optional. Deprecated: Number of nodes in this cluster.
    nodeCustomCoreCount: Optional. Deprecated: Customized number of cores
      available to each node of the cluster. This number must always be one of
      `nodeType.availableCustomCoreCounts`. If zero is provided max value from
      `nodeType.availableCustomCoreCounts` will be used.
    nodeTypeConfigs: Required. The map of cluster node types in this cluster,
      where the key is canonical identifier of the node type (corresponds to
      the `NodeType`).
    nodeTypeId: Optional. Deprecated: The canonical identifier of node types
      (`NodeType`) in this cluster. For example: standard-72.
    stretchedClusterConfig: Optional. Configuration of a stretched cluster.
      Required for STRETCHED private clouds.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NodeTypeConfigsValue(_messages.Message):
    r"""Required. The map of cluster node types in this cluster, where the key
    is canonical identifier of the node type (corresponds to the `NodeType`).

    Messages:
      AdditionalProperty: An additional property for a NodeTypeConfigsValue
        object.

    Fields:
      additionalProperties: Additional properties of type NodeTypeConfigsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NodeTypeConfigsValue object.

      Fields:
        key: Name of the additional property.
        value: A NodeTypeConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('NodeTypeConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  clusterId = _messages.StringField(1)
  nodeCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nodeCustomCoreCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  nodeTypeConfigs = _messages.MessageField('NodeTypeConfigsValue', 4)
  nodeTypeId = _messages.StringField(5)
  stretchedClusterConfig = _messages.MessageField('StretchedClusterConfig', 6)


class ManagementDnsZoneBinding(_messages.Message):
  r"""Represents a binding between a network and the management DNS zone. A
  management DNS zone is the Cloud DNS cross-project binding zone that VMware
  Engine creates for each private cloud. It contains FQDNs and corresponding
  IP addresses for the private cloud's ESXi hosts and management VM appliances
  like vCenter and NSX Manager.

  Enums:
    StateValueValuesEnum: Output only. The state of the resource.

  Fields:
    createTime: Output only. Creation time of this resource.
    description: User-provided description for this resource.
    etag: Checksum that may be sent on update and delete requests to ensure
      that the user-provided value is up to date before the server processes a
      request. The server computes checksums based on the value of other
      fields in the request.
    name: Output only. The resource name of this binding. Resource names are
      schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/managementDnsZoneBindings/my-management-dns-zone-binding`
    state: Output only. The state of the resource.
    uid: Output only. System-generated unique identifier for the resource.
    updateTime: Output only. Last update time of this resource.
    vmwareEngineNetwork: Network to bind is a VMware Engine network. Specify
      the name in the following form for VMware engine network: `projects/{pro
      ject}/locations/global/vmwareEngineNetworks/{vmware_engine_network_id}`.
      `{project}` can either be a project number or a project ID.
    vpcNetwork: Network to bind is a standard consumer VPC. Specify the name
      in the following form for consumer VPC network:
      `projects/{project}/global/networks/{network_id}`. `{project}` can
      either be a project number or a project ID.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the resource.

    Values:
      STATE_UNSPECIFIED: The default value. This value should never be used.
      ACTIVE: The binding is ready.
      CREATING: The binding is being created.
      UPDATING: The binding is being updated.
      DELETING: The binding is being deleted.
      FAILED: The binding has failed.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    UPDATING = 3
    DELETING = 4
    FAILED = 5

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  etag = _messages.StringField(3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  uid = _messages.StringField(6)
  updateTime = _messages.StringField(7)
  vmwareEngineNetwork = _messages.StringField(8)
  vpcNetwork = _messages.StringField(9)


class NetworkConfig(_messages.Message):
  r"""Network configuration in the consumer project with which the peering has
  to be done.

  Fields:
    dnsServerIp: Output only. DNS Server IP of the Private Cloud. All DNS
      queries can be forwarded to this address for name resolution of Private
      Cloud's management entities like vCenter, NSX-T Manager and ESXi hosts.
    managementCidr: Required. Management CIDR used by VMware management
      appliances.
    managementIpAddressLayoutVersion: Output only. The IP address layout
      version of the management IP address range. Possible versions include: *
      `managementIpAddressLayoutVersion=1`: Indicates the legacy IP address
      layout used by some existing private clouds. This is no longer supported
      for new private clouds as it does not support all features. *
      `managementIpAddressLayoutVersion=2`: Indicates the latest IP address
      layout used by all newly created private clouds. This version supports
      all current features.
    network: Optional. Deprecated: Optional. The relative resource name of the
      consumer VPC network this private cloud is attached to. Specify the name
      in the following form: `projects/{project}/global/networks/{network_id}`
      where `{project}` can either be a project number or a project ID.
    serviceNetwork: Output only. Deprecated: Output only. The relative
      resource name of the service VPC network this private cloud is attached
      to. The name is specified in the following form:
      `projects/{service_project_number}/global/networks/{network_id}`.
    vmwareEngineNetwork: Optional. The relative resource name of the VMware
      Engine network attached to the private cloud. Specify the name in the
      following form: `projects/{project}/locations/{location}/vmwareEngineNet
      works/{vmware_engine_network_id}` where `{project}` can either be a
      project number or a project ID.
    vmwareEngineNetworkCanonical: Output only. The canonical name of the
      VMware Engine network in the form: `projects/{project_number}/locations/
      {location}/vmwareEngineNetworks/{vmware_engine_network_id}`
  """

  dnsServerIp = _messages.StringField(1)
  managementCidr = _messages.StringField(2)
  managementIpAddressLayoutVersion = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  network = _messages.StringField(4)
  serviceNetwork = _messages.StringField(5)
  vmwareEngineNetwork = _messages.StringField(6)
  vmwareEngineNetworkCanonical = _messages.StringField(7)


class NetworkPeering(_messages.Message):
  r"""Details of a network peering.

  Enums:
    PeerNetworkTypeValueValuesEnum: Required. The type of the network to peer
      with the VMware Engine network.
    StateValueValuesEnum: Output only. State of the network peering. This
      field has a value of 'ACTIVE' when there's a matching configuration in
      the peer network. New values may be added to this enum when appropriate.

  Fields:
    createTime: Output only. Creation time of this resource.
    description: Optional. User-provided description for this network peering.
    exchangeSubnetRoutes: Optional. True if full mesh connectivity is created
      and managed automatically between peered networks; false otherwise.
      Currently this field is always true because Google Compute Engine
      automatically creates and manages subnetwork routes between two VPC
      networks when peering state is 'ACTIVE'.
    exportCustomRoutes: Optional. True if custom routes are exported to the
      peered network; false otherwise. The default value is true.
    exportCustomRoutesWithPublicIp: Optional. True if all subnet routes with a
      public IP address range are exported; false otherwise. The default value
      is true. IPv4 special-use ranges
      (https://en.wikipedia.org/wiki/IPv4#Special_addresses) are always
      exported to peers and are not controlled by this field.
    importCustomRoutes: Optional. True if custom routes are imported from the
      peered network; false otherwise. The default value is true.
    importCustomRoutesWithPublicIp: Optional. True if all subnet routes with
      public IP address range are imported; false otherwise. The default value
      is true. IPv4 special-use ranges
      (https://en.wikipedia.org/wiki/IPv4#Special_addresses) are always
      imported to peers and are not controlled by this field.
    name: Output only. The resource name of the network peering. Resource
      names are scheme-less URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/global/networkPeerings/my-peering`
    peerMtu: Optional. Maximum transmission unit (MTU) in bytes. The default
      value is `1500`. If a value of `0` is provided for this field, VMware
      Engine uses the default value instead.
    peerNetwork: Required. The relative resource name of the network to peer
      with a standard VMware Engine network. The provided network can be a
      consumer VPC network or another standard VMware Engine network. If the
      `peer_network_type` is VMWARE_ENGINE_NETWORK, specify the name in the
      form: `projects/{project}/locations/global/vmwareEngineNetworks/{vmware_
      engine_network_id}`. Otherwise specify the name in the form:
      `projects/{project}/global/networks/{network_id}`, where `{project}` can
      either be a project number or a project ID.
    peerNetworkType: Required. The type of the network to peer with the VMware
      Engine network.
    state: Output only. State of the network peering. This field has a value
      of 'ACTIVE' when there's a matching configuration in the peer network.
      New values may be added to this enum when appropriate.
    stateDetails: Output only. Output Only. Details about the current state of
      the network peering.
    uid: Output only. System-generated unique identifier for the resource.
    updateTime: Output only. Last update time of this resource.
    vmwareEngineNetwork: Required. The relative resource name of the VMware
      Engine network. Specify the name in the following form: `projects/{proje
      ct}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network_id}
      ` where `{project}` can either be a project number or a project ID.
  """

  class PeerNetworkTypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of the network to peer with the VMware Engine
    network.

    Values:
      PEER_NETWORK_TYPE_UNSPECIFIED: Unspecified
      STANDARD: Peering connection used for connecting to another VPC network
        established by the same user. For example, a peering connection to
        another VPC network in the same project or to an on-premises network.
      VMWARE_ENGINE_NETWORK: Peering connection used for connecting to another
        VMware Engine network.
      PRIVATE_SERVICES_ACCESS: Peering connection used for establishing
        [private services access](https://cloud.google.com/vpc/docs/private-
        services-access).
      NETAPP_CLOUD_VOLUMES: Peering connection used for connecting to NetApp
        Cloud Volumes.
      THIRD_PARTY_SERVICE: Peering connection used for connecting to third-
        party services. Most third-party services require manual setup of
        reverse peering on the VPC network associated with the third-party
        service.
      DELL_POWERSCALE: Peering connection used for connecting to Dell
        PowerScale Filers
    """
    PEER_NETWORK_TYPE_UNSPECIFIED = 0
    STANDARD = 1
    VMWARE_ENGINE_NETWORK = 2
    PRIVATE_SERVICES_ACCESS = 3
    NETAPP_CLOUD_VOLUMES = 4
    THIRD_PARTY_SERVICE = 5
    DELL_POWERSCALE = 6

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the network peering. This field has a value of
    'ACTIVE' when there's a matching configuration in the peer network. New
    values may be added to this enum when appropriate.

    Values:
      STATE_UNSPECIFIED: Unspecified network peering state. This is the
        default value.
      INACTIVE: The peering is not active.
      ACTIVE: The peering is active.
      CREATING: The peering is being created.
      DELETING: The peering is being deleted.
    """
    STATE_UNSPECIFIED = 0
    INACTIVE = 1
    ACTIVE = 2
    CREATING = 3
    DELETING = 4

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  exchangeSubnetRoutes = _messages.BooleanField(3)
  exportCustomRoutes = _messages.BooleanField(4)
  exportCustomRoutesWithPublicIp = _messages.BooleanField(5)
  importCustomRoutes = _messages.BooleanField(6)
  importCustomRoutesWithPublicIp = _messages.BooleanField(7)
  name = _messages.StringField(8)
  peerMtu = _messages.IntegerField(9, variant=_messages.Variant.INT32)
  peerNetwork = _messages.StringField(10)
  peerNetworkType = _messages.EnumField('PeerNetworkTypeValueValuesEnum', 11)
  state = _messages.EnumField('StateValueValuesEnum', 12)
  stateDetails = _messages.StringField(13)
  uid = _messages.StringField(14)
  updateTime = _messages.StringField(15)
  vmwareEngineNetwork = _messages.StringField(16)


class NetworkPolicy(_messages.Message):
  r"""Represents a network policy resource. Network policies are regional
  resources. You can use a network policy to enable or disable internet access
  and external IP access. Network policies are associated with a VMware Engine
  network, which might span across regions. For a given region, a network
  policy applies to all private clouds in the VMware Engine network associated
  with the policy.

  Fields:
    createTime: Output only. Creation time of this resource.
    description: Optional. User-provided description for this network policy.
    edgeServicesCidr: Required. IP address range in CIDR notation used to
      create internet access and external IP access. An RFC 1918 CIDR block,
      with a "/26" prefix, is required. The range cannot overlap with any
      prefixes either in the consumer VPC network or in use by the private
      clouds attached to that VPC network.
    externalIp: Network service that allows External IP addresses to be
      assigned to VMware workloads. This service can only be enabled when
      `internet_access` is also enabled.
    internetAccess: Network service that allows VMware workloads to access the
      internet.
    name: Output only. The resource name of this network policy. Resource
      names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/networkPolicies/my-network-
      policy`
    network: Optional. Deprecated: Optional. Name of the network in the
      consumer project which is peered or will be peered with the service
      network. Provide the network name in the form of
      `projects/{project}/global/networks/{network}`, where `{project}` is the
      project ID or project number of the project containing the network. In
      case of shared VPC, use the project ID or project number of the host
      project containing the shared VPC network.
    uid: Output only. System-generated unique identifier for the resource.
    updateTime: Output only. Last update time of this resource.
    vmwareEngineNetwork: Optional. The relative resource name of the VMware
      Engine network. Specify the name in the following form: `projects/{proje
      ct}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network_id}
      ` where `{project}` can either be a project number or a project ID.
    vmwareEngineNetworkCanonical: Output only. The canonical name of the
      VMware Engine network in the form: `projects/{project_number}/locations/
      {location}/vmwareEngineNetworks/{vmware_engine_network_id}`
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  edgeServicesCidr = _messages.StringField(3)
  externalIp = _messages.MessageField('NetworkService', 4)
  internetAccess = _messages.MessageField('NetworkService', 5)
  name = _messages.StringField(6)
  network = _messages.StringField(7)
  uid = _messages.StringField(8)
  updateTime = _messages.StringField(9)
  vmwareEngineNetwork = _messages.StringField(10)
  vmwareEngineNetworkCanonical = _messages.StringField(11)


class NetworkService(_messages.Message):
  r"""Represents a network service that is managed by a `NetworkPolicy`
  resource. A network service provides a way to control an aspect of external
  access to VMware workloads. For example, whether the VMware workloads in the
  private clouds governed by a network policy can access or be accessed from
  the internet.

  Enums:
    StateValueValuesEnum: Output only. State of the service. New values may be
      added to this enum when appropriate.

  Fields:
    enabled: True if the service is enabled; false otherwise.
    state: Output only. State of the service. New values may be added to this
      enum when appropriate.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the service. New values may be added to this
    enum when appropriate.

    Values:
      STATE_UNSPECIFIED: Unspecified service state. This is the default value.
      UNPROVISIONED: Service is not provisioned.
      RECONCILING: Service is in the process of being
        provisioned/deprovisioned.
      ACTIVE: Service is active.
    """
    STATE_UNSPECIFIED = 0
    UNPROVISIONED = 1
    RECONCILING = 2
    ACTIVE = 3

  enabled = _messages.BooleanField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)


class Node(_messages.Message):
  r"""Node in a cluster.

  Enums:
    StateValueValuesEnum: Output only. The state of the appliance.

  Fields:
    customCoreCount: Output only. Customized number of cores
    fqdn: Output only. Fully qualified domain name of the node.
    internalIp: Output only. Internal IP address of the node.
    name: Output only. The resource name of this node. Resource names are
      schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/clusters/my-cluster/nodes/my-node
    nodeTypeId: Output only. The canonical identifier of the node type
      (corresponds to the `NodeType`). For example: standard-72.
    state: Output only. The state of the appliance.
    version: Output only. The version number of the VMware ESXi management
      component in this cluster.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the appliance.

    Values:
      STATE_UNSPECIFIED: The default value. This value should never be used.
      ACTIVE: Node is operational and can be used by the user.
      CREATING: Node is being provisioned.
      FAILED: Node is in a failed state.
      UPGRADING: Node is undergoing maintenance, e.g.: during private cloud
        upgrade.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    FAILED = 3
    UPGRADING = 4

  customCoreCount = _messages.IntegerField(1)
  fqdn = _messages.StringField(2)
  internalIp = _messages.StringField(3)
  name = _messages.StringField(4)
  nodeTypeId = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  version = _messages.StringField(7)


class NodeType(_messages.Message):
  r"""Describes node type.

  Enums:
    CapabilitiesValueListEntryValuesEnum:
    KindValueValuesEnum: Output only. The type of the resource.

  Fields:
    availableCustomCoreCounts: Output only. List of possible values of custom
      core count.
    capabilities: Output only. Capabilities of this node type.
    diskSizeGb: Output only. The amount of storage available, defined in GB.
    displayName: Output only. The friendly name for this node type. For
      example: ve1-standard-72
    families: Output only. Families of the node type. For node types to be in
      the same cluster they must share at least one element in the `families`.
    kind: Output only. The type of the resource.
    memoryGb: Output only. The amount of physical memory available, defined in
      GB.
    name: Output only. The resource name of this node type. Resource names are
      schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-proj/locations/us-central1-a/nodeTypes/standard-72`
    nodeTypeId: Output only. The canonical identifier of the node type
      (corresponds to the `NodeType`). For example: standard-72.
    totalCoreCount: Output only. The total number of CPU cores in a single
      node.
    virtualCpuCount: Output only. The total number of virtual CPUs in a single
      node.
  """

  class CapabilitiesValueListEntryValuesEnum(_messages.Enum):
    r"""CapabilitiesValueListEntryValuesEnum enum type.

    Values:
      CAPABILITY_UNSPECIFIED: The default value. This value is used if the
        capability is omitted or unknown.
      STRETCHED_CLUSTERS: This node type supports stretch clusters.
    """
    CAPABILITY_UNSPECIFIED = 0
    STRETCHED_CLUSTERS = 1

  class KindValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the resource.

    Values:
      KIND_UNSPECIFIED: The default value. This value should never be used.
      STANDARD: Standard HCI node.
      STORAGE_ONLY: Storage only Node.
    """
    KIND_UNSPECIFIED = 0
    STANDARD = 1
    STORAGE_ONLY = 2

  availableCustomCoreCounts = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)
  capabilities = _messages.EnumField('CapabilitiesValueListEntryValuesEnum', 2, repeated=True)
  diskSizeGb = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  displayName = _messages.StringField(4)
  families = _messages.StringField(5, repeated=True)
  kind = _messages.EnumField('KindValueValuesEnum', 6)
  memoryGb = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  name = _messages.StringField(8)
  nodeTypeId = _messages.StringField(9)
  totalCoreCount = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  virtualCpuCount = _messages.IntegerField(11, variant=_messages.Variant.INT32)


class NodeTypeConfig(_messages.Message):
  r"""Information about the type and number of nodes associated with the
  cluster.

  Fields:
    customCoreCount: Optional. Customized number of cores available to each
      node of the type. This number must always be one of
      `nodeType.availableCustomCoreCounts`. If zero is provided max value from
      `nodeType.availableCustomCoreCounts` will be used.
    nodeCount: Required. The number of nodes of this type in the cluster
  """

  customCoreCount = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  nodeCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class Nsx(_messages.Message):
  r"""Details about a NSX Manager appliance.

  Enums:
    StateValueValuesEnum: Output only. The state of the appliance.

  Fields:
    fqdn: Fully qualified domain name of the appliance.
    internalIp: Internal IP address of the appliance.
    state: Output only. The state of the appliance.
    version: Version of the appliance.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the appliance.

    Values:
      STATE_UNSPECIFIED: Unspecified appliance state. This is the default
        value.
      ACTIVE: The appliance is operational and can be used.
      CREATING: The appliance is being deployed.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2

  fqdn = _messages.StringField(1)
  internalIp = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  version = _messages.StringField(4)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. True if the user has requested
      cancellation of the operation; false otherwise. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class PeeringRoute(_messages.Message):
  r"""Exchanged network peering route.

  Enums:
    DirectionValueValuesEnum: Output only. Direction of the routes exchanged
      with the peer network, from the VMware Engine network perspective: *
      Routes of direction `INCOMING` are imported from the peer network. *
      Routes of direction `OUTGOING` are exported from the intranet VPC
      network of the VMware Engine network.
    TypeValueValuesEnum: Output only. Type of the route in the peer VPC
      network.

  Fields:
    destRange: Output only. Destination range of the peering route in CIDR
      notation.
    direction: Output only. Direction of the routes exchanged with the peer
      network, from the VMware Engine network perspective: * Routes of
      direction `INCOMING` are imported from the peer network. * Routes of
      direction `OUTGOING` are exported from the intranet VPC network of the
      VMware Engine network.
    imported: Output only. True if the peering route has been imported from a
      peered VPC network; false otherwise. The import happens if the field
      `NetworkPeering.importCustomRoutes` is true for this network,
      `NetworkPeering.exportCustomRoutes` is true for the peer VPC network,
      and the import does not result in a route conflict.
    nextHopRegion: Output only. Region containing the next hop of the peering
      route. This field only applies to dynamic routes in the peer VPC
      network.
    priority: Output only. The priority of the peering route.
    type: Output only. Type of the route in the peer VPC network.
  """

  class DirectionValueValuesEnum(_messages.Enum):
    r"""Output only. Direction of the routes exchanged with the peer network,
    from the VMware Engine network perspective: * Routes of direction
    `INCOMING` are imported from the peer network. * Routes of direction
    `OUTGOING` are exported from the intranet VPC network of the VMware Engine
    network.

    Values:
      DIRECTION_UNSPECIFIED: Unspecified exchanged routes direction. This is
        default.
      INCOMING: Routes imported from the peer network.
      OUTGOING: Routes exported to the peer network.
    """
    DIRECTION_UNSPECIFIED = 0
    INCOMING = 1
    OUTGOING = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of the route in the peer VPC network.

    Values:
      TYPE_UNSPECIFIED: Unspecified peering route type. This is the default
        value.
      DYNAMIC_PEERING_ROUTE: Dynamic routes in the peer network.
      STATIC_PEERING_ROUTE: Static routes in the peer network.
      SUBNET_PEERING_ROUTE: Created, updated, and removed automatically by
        Google Cloud when subnets are created, modified, or deleted in the
        peer network.
    """
    TYPE_UNSPECIFIED = 0
    DYNAMIC_PEERING_ROUTE = 1
    STATIC_PEERING_ROUTE = 2
    SUBNET_PEERING_ROUTE = 3

  destRange = _messages.StringField(1)
  direction = _messages.EnumField('DirectionValueValuesEnum', 2)
  imported = _messages.BooleanField(3)
  nextHopRegion = _messages.StringField(4)
  priority = _messages.IntegerField(5)
  type = _messages.EnumField('TypeValueValuesEnum', 6)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class Principal(_messages.Message):
  r"""Users/Service accounts which have access for DNS binding on the intranet
  VPC corresponding to the consumer project.

  Fields:
    serviceAccount: The service account which needs to be granted the
      permission.
    user: The user who needs to be granted permission.
  """

  serviceAccount = _messages.StringField(1)
  user = _messages.StringField(2)


class PrivateCloud(_messages.Message):
  r"""Represents a private cloud resource. Private clouds of type `STANDARD`
  and `TIME_LIMITED` are zonal resources, `STRETCHED` private clouds are
  regional.

  Enums:
    StateValueValuesEnum: Output only. State of the resource. New values may
      be added to this enum when appropriate.
    TypeValueValuesEnum: Optional. Type of the private cloud. Defaults to
      STANDARD.

  Fields:
    createTime: Output only. Creation time of this resource.
    deleteTime: Output only. Time when the resource was scheduled for
      deletion.
    description: User-provided description for this private cloud.
    expireTime: Output only. Time when the resource will be irreversibly
      deleted.
    hcx: Output only. HCX appliance.
    managementCluster: Required. Input only. The management cluster for this
      private cloud. This field is required during creation of the private
      cloud to provide details for the default cluster. The following fields
      can't be changed after private cloud creation:
      `ManagementCluster.clusterId`, `ManagementCluster.nodeTypeId`.
    name: Output only. The resource name of this private cloud. Resource names
      are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
    networkConfig: Required. Network configuration of the private cloud.
    nsx: Output only. NSX appliance.
    state: Output only. State of the resource. New values may be added to this
      enum when appropriate.
    type: Optional. Type of the private cloud. Defaults to STANDARD.
    uid: Output only. System-generated unique identifier for the resource.
    updateTime: Output only. Last update time of this resource.
    vcenter: Output only. Vcenter appliance.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the resource. New values may be added to this
    enum when appropriate.

    Values:
      STATE_UNSPECIFIED: The default value. This value should never be used.
      ACTIVE: The private cloud is ready.
      CREATING: The private cloud is being created.
      UPDATING: The private cloud is being updated.
      FAILED: The private cloud is in failed state.
      DELETED: The private cloud is scheduled for deletion. The deletion
        process can be cancelled by using the corresponding undelete method.
      PURGING: The private cloud is irreversibly deleted and is being removed
        from the system.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    UPDATING = 3
    FAILED = 4
    DELETED = 5
    PURGING = 6

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. Type of the private cloud. Defaults to STANDARD.

    Values:
      STANDARD: Standard private is a zonal resource, with 3+ nodes. Default
        type.
      TIME_LIMITED: Time limited private cloud is a zonal resource, can have
        only 1 node and has limited life span. Will be deleted after defined
        period of time, can be converted into standard private cloud by
        expanding it up to 3 or more nodes.
      STRETCHED: Stretched private cloud is a regional resource with
        redundancy, with a minimum of 6 nodes, nodes count has to be even.
    """
    STANDARD = 0
    TIME_LIMITED = 1
    STRETCHED = 2

  createTime = _messages.StringField(1)
  deleteTime = _messages.StringField(2)
  description = _messages.StringField(3)
  expireTime = _messages.StringField(4)
  hcx = _messages.MessageField('Hcx', 5)
  managementCluster = _messages.MessageField('ManagementCluster', 6)
  name = _messages.StringField(7)
  networkConfig = _messages.MessageField('NetworkConfig', 8)
  nsx = _messages.MessageField('Nsx', 9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  type = _messages.EnumField('TypeValueValuesEnum', 11)
  uid = _messages.StringField(12)
  updateTime = _messages.StringField(13)
  vcenter = _messages.MessageField('Vcenter', 14)


class PrivateConnection(_messages.Message):
  r"""Private connection resource that provides connectivity for VMware Engine
  private clouds.

  Enums:
    PeeringStateValueValuesEnum: Output only. Peering state between service
      network and VMware Engine network.
    RoutingModeValueValuesEnum: Optional. Routing Mode. Default value is set
      to GLOBAL. For type = PRIVATE_SERVICE_ACCESS, this field can be set to
      GLOBAL or REGIONAL, for other types only GLOBAL is supported.
    StateValueValuesEnum: Output only. State of the private connection.
    TypeValueValuesEnum: Required. Private connection type.

  Fields:
    createTime: Output only. Creation time of this resource.
    description: Optional. User-provided description for this private
      connection.
    name: Output only. The resource name of the private connection. Resource
      names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/privateConnections/my-
      connection`
    peeringId: Output only. VPC network peering id between given network VPC
      and VMwareEngineNetwork.
    peeringState: Output only. Peering state between service network and
      VMware Engine network.
    routingMode: Optional. Routing Mode. Default value is set to GLOBAL. For
      type = PRIVATE_SERVICE_ACCESS, this field can be set to GLOBAL or
      REGIONAL, for other types only GLOBAL is supported.
    serviceNetwork: Required. Service network to create private connection.
      Specify the name in the following form:
      `projects/{project}/global/networks/{network_id}` For type =
      PRIVATE_SERVICE_ACCESS, this field represents servicenetworking VPC,
      e.g. projects/project-tp/global/networks/servicenetworking. For type =
      NETAPP_CLOUD_VOLUME, this field represents NetApp service VPC, e.g.
      projects/project-tp/global/networks/netapp-tenant-vpc. For type =
      DELL_POWERSCALE, this field represent Dell service VPC, e.g.
      projects/project-tp/global/networks/dell-tenant-vpc. For type=
      THIRD_PARTY_SERVICE, this field could represent a consumer VPC or any
      other producer VPC to which the VMware Engine Network needs to be
      connected, e.g. projects/project/global/networks/vpc.
    state: Output only. State of the private connection.
    type: Required. Private connection type.
    uid: Output only. System-generated unique identifier for the resource.
    updateTime: Output only. Last update time of this resource.
    vmwareEngineNetwork: Required. The relative resource name of Legacy VMware
      Engine network. Specify the name in the following form: `projects/{proje
      ct}/locations/{location}/vmwareEngineNetworks/{vmware_engine_network_id}
      ` where `{project}`, `{location}` will be same as specified in private
      connection resource name and `{vmware_engine_network_id}` will be in the
      form of `{location}`-default e.g. projects/project/locations/us-
      central1/vmwareEngineNetworks/us-central1-default.
    vmwareEngineNetworkCanonical: Output only. The canonical name of the
      VMware Engine network in the form: `projects/{project_number}/locations/
      {location}/vmwareEngineNetworks/{vmware_engine_network_id}`
  """

  class PeeringStateValueValuesEnum(_messages.Enum):
    r"""Output only. Peering state between service network and VMware Engine
    network.

    Values:
      PEERING_STATE_UNSPECIFIED: The default value. This value is used if the
        peering state is omitted or unknown.
      PEERING_ACTIVE: The peering is in active state.
      PEERING_INACTIVE: The peering is in inactive state.
    """
    PEERING_STATE_UNSPECIFIED = 0
    PEERING_ACTIVE = 1
    PEERING_INACTIVE = 2

  class RoutingModeValueValuesEnum(_messages.Enum):
    r"""Optional. Routing Mode. Default value is set to GLOBAL. For type =
    PRIVATE_SERVICE_ACCESS, this field can be set to GLOBAL or REGIONAL, for
    other types only GLOBAL is supported.

    Values:
      ROUTING_MODE_UNSPECIFIED: The default value. This value should never be
        used.
      GLOBAL: Global Routing Mode
      REGIONAL: Regional Routing Mode
    """
    ROUTING_MODE_UNSPECIFIED = 0
    GLOBAL = 1
    REGIONAL = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the private connection.

    Values:
      STATE_UNSPECIFIED: The default value. This value is used if the state is
        omitted.
      CREATING: The private connection is being created.
      ACTIVE: The private connection is ready.
      UPDATING: The private connection is being updated.
      DELETING: The private connection is being deleted.
      UNPROVISIONED: The private connection is not provisioned, since no
        private cloud is present for which this private connection is needed.
      FAILED: The private connection is in failed state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    UPDATING = 3
    DELETING = 4
    UNPROVISIONED = 5
    FAILED = 6

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. Private connection type.

    Values:
      TYPE_UNSPECIFIED: The default value. This value should never be used.
      PRIVATE_SERVICE_ACCESS: Connection used for establishing [private
        services access](https://cloud.google.com/vpc/docs/private-services-
        access).
      NETAPP_CLOUD_VOLUMES: Connection used for connecting to NetApp Cloud
        Volumes.
      DELL_POWERSCALE: Connection used for connecting to Dell PowerScale.
      THIRD_PARTY_SERVICE: Connection used for connecting to third-party
        services.
    """
    TYPE_UNSPECIFIED = 0
    PRIVATE_SERVICE_ACCESS = 1
    NETAPP_CLOUD_VOLUMES = 2
    DELL_POWERSCALE = 3
    THIRD_PARTY_SERVICE = 4

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  name = _messages.StringField(3)
  peeringId = _messages.StringField(4)
  peeringState = _messages.EnumField('PeeringStateValueValuesEnum', 5)
  routingMode = _messages.EnumField('RoutingModeValueValuesEnum', 6)
  serviceNetwork = _messages.StringField(7)
  state = _messages.EnumField('StateValueValuesEnum', 8)
  type = _messages.EnumField('TypeValueValuesEnum', 9)
  uid = _messages.StringField(10)
  updateTime = _messages.StringField(11)
  vmwareEngineNetwork = _messages.StringField(12)
  vmwareEngineNetworkCanonical = _messages.StringField(13)


class ProjectState(_messages.Message):
  r"""Describes a Project State. This contains migration state as well as
  network type of the project

  Enums:
    MigrationStateValueValuesEnum: Output only. The migration state of the
      alias.
    NetworkTypeValueValuesEnum: Output only. The network type of the alias.

  Fields:
    migrationState: Output only. The migration state of the alias.
    name: Output only. Name of the resource which represents the Project state
      of a given project
    networkType: Output only. The network type of the alias.
  """

  class MigrationStateValueValuesEnum(_messages.Enum):
    r"""Output only. The migration state of the alias.

    Values:
      MIGRATION_STATE_UNSPECIFIED: Default migration state. This value should
        never be used.
      PENDING: Indicates migration is yet to be performed.
      COMPLETED: Indicates migration is successfully completed.
      IN_PROGRESS: Indicates migration in progress.
      NOT_REQUIRED: This value indicates there was no migration state for the
        alias defined in the system. This would be the case for the aliases
        that are new i.e., do not have resource at the time of cutover.
    """
    MIGRATION_STATE_UNSPECIFIED = 0
    PENDING = 1
    COMPLETED = 2
    IN_PROGRESS = 3
    NOT_REQUIRED = 4

  class NetworkTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The network type of the alias.

    Values:
      NETWORK_TYPE_UNSPECIFIED: Default network type. This value should never
        be used.
      LEGACY: Indicates project is using legacy resources.
      STANDARD: Indicates project is using standard resources.
    """
    NETWORK_TYPE_UNSPECIFIED = 0
    LEGACY = 1
    STANDARD = 2

  migrationState = _messages.EnumField('MigrationStateValueValuesEnum', 1)
  name = _messages.StringField(2)
  networkType = _messages.EnumField('NetworkTypeValueValuesEnum', 3)


class RepairManagementDnsZoneBindingRequest(_messages.Message):
  r"""Request message for VmwareEngine.RepairManagementDnsZoneBindings

  Fields:
    etag: Optional. Checksum used to ensure that the user-provided value is up
      to date before the server processes the request. The server compares
      provided checksum with the current checksum of the resource. If the
      user-provided value is out of date, this request returns an `ABORTED`
      error.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if the
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  etag = _messages.StringField(1)
  requestId = _messages.StringField(2)


class ResetNsxCredentialsRequest(_messages.Message):
  r"""Request message for VmwareEngine.ResetNsxCredentials

  Fields:
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  requestId = _messages.StringField(1)


class ResetVcenterCredentialsRequest(_messages.Message):
  r"""Request message for VmwareEngine.ResetVcenterCredentials

  Fields:
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
    username: Optional. The username of the user to be to reset the
      credentials. The default value of this <NAME_EMAIL>.
      The provided value should be one of the following: solution-
      <EMAIL>, <EMAIL>, solution-
      <EMAIL>, <EMAIL>, solution-
      <EMAIL>, <EMAIL>.
  """

  requestId = _messages.StringField(1)
  username = _messages.StringField(2)


class RevokeDnsBindPermissionRequest(_messages.Message):
  r"""Request message for VmwareEngine.RevokeDnsBindPermission

  Fields:
    etag: Optional. Checksum used to ensure that the user-provided value is up
      to date before the server processes the request. The server compares
      provided checksum with the current checksum of the resource. If the
      user-provided value is out of date, this request returns an `ABORTED`
      error.
    principal: Required. The consumer provided user/service account which
      needs to be granted permission to bind with the intranet VPC
      corresponding to the consumer project.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  etag = _messages.StringField(1)
  principal = _messages.MessageField('Principal', 2)
  requestId = _messages.StringField(3)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class SpecificDomainControllers(_messages.Message):
  r"""Configuration of specific domain controllers.

  Fields:
    primaryServerUri: Required. Primary domain controller LDAP server for the
      domain. Format `ldap://hostname:port` or `ldaps://hostname:port`.
    secondaryServerUri: Optional. Secondary domain controller LDAP server for
      the domain. Format `ldap://hostname:port` or `ldaps://hostname:port`.
  """

  primaryServerUri = _messages.StringField(1)
  secondaryServerUri = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StretchedClusterConfig(_messages.Message):
  r"""Configuration of a stretched cluster.

  Fields:
    preferredLocation: Required. Zone that will remain operational when
      connection between the two zones is lost. Specify the resource name of a
      zone that belongs to the region of the private cloud. For example:
      `projects/{project}/locations/europe-west3-a` where `{project}` can
      either be a project number or a project ID.
    secondaryLocation: Required. Additional zone for a higher level of
      availability and load balancing. Specify the resource name of a zone
      that belongs to the region of the private cloud. For example:
      `projects/{project}/locations/europe-west3-b` where `{project}` can
      either be a project number or a project ID.
  """

  preferredLocation = _messages.StringField(1)
  secondaryLocation = _messages.StringField(2)


class Subnet(_messages.Message):
  r"""Subnet in a private cloud. Either `management` subnets (such as vMotion)
  that are read-only, or `userDefined`, which can also be updated.

  Enums:
    StateValueValuesEnum: Output only. The state of the resource.

  Fields:
    gatewayIp: The IP address of the gateway of this subnet. Must fall within
      the IP prefix defined above.
    ipCidrRange: The IP address range of the subnet in CIDR format
      '10.0.0.0/24'.
    name: Output only. The resource name of this subnet. Resource names are
      schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/subnets/my-subnet`
    state: Output only. The state of the resource.
    type: Output only. The type of the subnet. For example "management" or
      "userDefined".
    vlanId: Output only. VLAN ID of the VLAN on which the subnet is configured
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the resource.

    Values:
      STATE_UNSPECIFIED: The default value. This value should never be used.
      ACTIVE: The subnet is ready.
      CREATING: The subnet is being created.
      UPDATING: The subnet is being updated.
      DELETING: The subnet is being deleted.
      RECONCILING: Changes requested in the last operation are being
        propagated.
      FAILED: Last operation on the subnet did not succeed. Subnet's payload
        is reverted back to its most recent working state.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2
    UPDATING = 3
    DELETING = 4
    RECONCILING = 5
    FAILED = 6

  gatewayIp = _messages.StringField(1)
  ipCidrRange = _messages.StringField(2)
  name = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  type = _messages.StringField(5)
  vlanId = _messages.IntegerField(6, variant=_messages.Variant.INT32)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class UndeletePrivateCloudRequest(_messages.Message):
  r"""Request message for VmwareEngine.UndeletePrivateCloud

  Fields:
    requestId: Optional. The request ID must be a valid UUID with the
      exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  requestId = _messages.StringField(1)


class Vcenter(_messages.Message):
  r"""Details about a vCenter Server management appliance.

  Enums:
    StateValueValuesEnum: Output only. The state of the appliance.

  Fields:
    fqdn: Fully qualified domain name of the appliance.
    internalIp: Internal IP address of the appliance.
    state: Output only. The state of the appliance.
    version: Version of the appliance.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the appliance.

    Values:
      STATE_UNSPECIFIED: Unspecified appliance state. This is the default
        value.
      ACTIVE: The appliance is operational and can be used.
      CREATING: The appliance is being deployed.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    CREATING = 2

  fqdn = _messages.StringField(1)
  internalIp = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  version = _messages.StringField(4)


class VmwareEngineNetwork(_messages.Message):
  r"""VMware Engine network resource that provides connectivity for VMware
  Engine private clouds.

  Enums:
    StateValueValuesEnum: Output only. State of the VMware Engine network.
    TypeValueValuesEnum: Required. VMware Engine network type.

  Fields:
    createTime: Output only. Creation time of this resource.
    description: User-provided description for this VMware Engine network.
    etag: Checksum that may be sent on update and delete requests to ensure
      that the user-provided value is up to date before the server processes a
      request. The server computes checksums based on the value of other
      fields in the request.
    name: Output only. The resource name of the VMware Engine network.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/global/vmwareEngineNetworks/my-network`
    state: Output only. State of the VMware Engine network.
    type: Required. VMware Engine network type.
    uid: Output only. System-generated unique identifier for the resource.
    updateTime: Output only. Last update time of this resource.
    vpcNetworks: Output only. VMware Engine service VPC networks that provide
      connectivity from a private cloud to customer projects, the internet,
      and other Google Cloud services.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the VMware Engine network.

    Values:
      STATE_UNSPECIFIED: The default value. This value is used if the state is
        omitted.
      CREATING: The VMware Engine network is being created.
      ACTIVE: The VMware Engine network is ready.
      UPDATING: The VMware Engine network is being updated.
      DELETING: The VMware Engine network is being deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    UPDATING = 3
    DELETING = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. VMware Engine network type.

    Values:
      TYPE_UNSPECIFIED: The default value. This value should never be used.
      LEGACY: Network type used by private clouds created in projects without
        a network of type `STANDARD`. This network type is no longer used for
        new VMware Engine private cloud deployments.
      STANDARD: Standard network type used for private cloud connectivity.
    """
    TYPE_UNSPECIFIED = 0
    LEGACY = 1
    STANDARD = 2

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  etag = _messages.StringField(3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  type = _messages.EnumField('TypeValueValuesEnum', 6)
  uid = _messages.StringField(7)
  updateTime = _messages.StringField(8)
  vpcNetworks = _messages.MessageField('VpcNetwork', 9, repeated=True)


class VmwareengineProjectsLocationsGetProjectStateRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsGetProjectStateRequest object.

  Fields:
    name: Required. The name of the project state resource to retrieve. For
      example: `projects/{project}/location/{location}/projectState`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsGetRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsGlobalDnsBindPermissionGrantRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsGlobalDnsBindPermissionGrantRequest
  object.

  Fields:
    grantDnsBindPermissionRequest: A GrantDnsBindPermissionRequest resource to
      be passed as the request body.
    name: Required. The name of the resource which stores the users/service
      accounts having the permission to bind to the corresponding intranet VPC
      of the consumer project. DnsBindPermission is a global resource.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/global/dnsBindPermission`
  """

  grantDnsBindPermissionRequest = _messages.MessageField('GrantDnsBindPermissionRequest', 1)
  name = _messages.StringField(2, required=True)


class VmwareengineProjectsLocationsGlobalDnsBindPermissionRevokeRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsGlobalDnsBindPermissionRevokeRequest
  object.

  Fields:
    name: Required. The name of the resource which stores the users/service
      accounts having the permission to bind to the corresponding intranet VPC
      of the consumer project. DnsBindPermission is a global resource.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/global/dnsBindPermission`
    revokeDnsBindPermissionRequest: A RevokeDnsBindPermissionRequest resource
      to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  revokeDnsBindPermissionRequest = _messages.MessageField('RevokeDnsBindPermissionRequest', 2)


class VmwareengineProjectsLocationsGlobalGetDnsBindPermissionRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsGlobalGetDnsBindPermissionRequest object.

  Fields:
    name: Required. The name of the resource which stores the users/service
      accounts having the permission to bind to the corresponding intranet VPC
      of the consumer project. DnsBindPermission is a global resource.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/global/dnsBindPermission`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsGlobalNetworkPeeringsCreateRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsGlobalNetworkPeeringsCreateRequest
  object.

  Fields:
    networkPeering: A NetworkPeering resource to be passed as the request
      body.
    networkPeeringId: Required. The user-provided identifier of the new
      `NetworkPeering`. This identifier must be unique among `NetworkPeering`
      resources within the parent and becomes the final token in the name URI.
      The identifier must meet the following requirements: * Only contains
      1-63 alphanumeric characters and hyphens * Begins with an alphabetical
      character * Ends with a non-hyphen character * Not formatted as a UUID *
      Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034)
      (section 3.5)
    parent: Required. The resource name of the location to create the new
      network peering in. This value is always `global`, because
      `NetworkPeering` is a global resource. Resource names are schemeless
      URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/global`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  networkPeering = _messages.MessageField('NetworkPeering', 1)
  networkPeeringId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class VmwareengineProjectsLocationsGlobalNetworkPeeringsDeleteRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsGlobalNetworkPeeringsDeleteRequest
  object.

  Fields:
    name: Required. The resource name of the network peering to be deleted.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/global/networkPeerings/my-peering`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class VmwareengineProjectsLocationsGlobalNetworkPeeringsGetRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsGlobalNetworkPeeringsGetRequest object.

  Fields:
    name: Required. The resource name of the network peering to retrieve.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/global/networkPeerings/my-peering`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsGlobalNetworkPeeringsListRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsGlobalNetworkPeeringsListRequest object.

  Fields:
    filter: A filter expression that matches resources returned in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of
      network peerings, you can exclude the ones named `example-peering` by
      specifying `name != "example-peering"`. To filter on multiple
      expressions, provide each separate expression within parentheses. For
      example: ``` (name = "example-peering") (createTime >
      "2021-04-12T08:15:10.40Z") ``` By default, each expression is an `AND`
      expression. However, you can include `AND` and `OR` expressions
      explicitly. For example: ``` (name = "example-peering-1") AND
      (createTime > "2021-04-12T08:15:10.40Z") OR (name = "example-peering-2")
      ```
    orderBy: Sorts list results by a certain order. By default, returned
      results are ordered by `name` in ascending order. You can also sort
      results in descending order based on the `name` value using
      `orderBy="name desc"`. Currently, only ordering by `name` is supported.
    pageSize: The maximum number of network peerings to return in one page.
      The maximum value is coerced to 1000. The default value of this field is
      500.
    pageToken: A page token, received from a previous `ListNetworkPeerings`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListNetworkPeerings` must match the call
      that provided the page token.
    parent: Required. The resource name of the location (global) to query for
      network peerings. Resource names are schemeless URIs that follow the
      conventions in https://cloud.google.com/apis/design/resource_names. For
      example: `projects/my-project/locations/global`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmwareengineProjectsLocationsGlobalNetworkPeeringsPatchRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsGlobalNetworkPeeringsPatchRequest object.

  Fields:
    name: Output only. The resource name of the network peering. Resource
      names are scheme-less URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/global/networkPeerings/my-peering`
    networkPeering: A NetworkPeering resource to be passed as the request
      body.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the `NetworkPeering` resource by the update. The fields
      specified in the `update_mask` are relative to the resource, not the
      full request. A field will be overwritten if it is in the mask. If the
      user does not provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  networkPeering = _messages.MessageField('NetworkPeering', 2)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class VmwareengineProjectsLocationsGlobalNetworkPeeringsPeeringRoutesListRequest(_messages.Message):
  r"""A
  VmwareengineProjectsLocationsGlobalNetworkPeeringsPeeringRoutesListRequest
  object.

  Fields:
    filter: A filter expression that matches resources returned in the
      response. Currently, only filtering on the `direction` field is
      supported. To return routes imported from the peer network, provide
      "direction=INCOMING". To return routes exported from the VMware Engine
      network, provide "direction=OUTGOING". Other filter expressions return
      an error.
    pageSize: The maximum number of peering routes to return in one page. The
      service may return fewer than this value. The maximum value is coerced
      to 1000. The default value of this field is 500.
    pageToken: A page token, received from a previous `ListPeeringRoutes`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListPeeringRoutes` must match the call
      that provided the page token.
    parent: Required. The resource name of the network peering to retrieve
      peering routes from. Resource names are schemeless URIs that follow the
      conventions in https://cloud.google.com/apis/design/resource_names. For
      example: `projects/my-project/locations/global/networkPeerings/my-
      peering`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class VmwareengineProjectsLocationsListRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class VmwareengineProjectsLocationsNetworkPoliciesCreateRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsNetworkPoliciesCreateRequest object.

  Fields:
    networkPolicy: A NetworkPolicy resource to be passed as the request body.
    networkPolicyId: Required. The user-provided identifier of the network
      policy to be created. This identifier must be unique within parent
      `projects/{my-project}/locations/{us-central1}/networkPolicies` and
      becomes the final token in the name URI. The identifier must meet the
      following requirements: * Only contains 1-63 alphanumeric characters and
      hyphens * Begins with an alphabetical character * Ends with a non-hyphen
      character * Not formatted as a UUID * Complies with [RFC
      1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)
    parent: Required. The resource name of the location (region) to create the
      new network policy in. Resource names are schemeless URIs that follow
      the conventions in https://cloud.google.com/apis/design/resource_names.
      For example: `projects/my-project/locations/us-central1`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  networkPolicy = _messages.MessageField('NetworkPolicy', 1)
  networkPolicyId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class VmwareengineProjectsLocationsNetworkPoliciesDeleteRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsNetworkPoliciesDeleteRequest object.

  Fields:
    name: Required. The resource name of the network policy to delete.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/networkPolicies/my-network-
      policy`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesCreateRequest(_messages.Message):
  r"""A
  VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesCreateRequest
  object.

  Fields:
    externalAccessRule: A ExternalAccessRule resource to be passed as the
      request body.
    externalAccessRuleId: Required. The user-provided identifier of the
      `ExternalAccessRule` to be created. This identifier must be unique among
      `ExternalAccessRule` resources within the parent and becomes the final
      token in the name URI. The identifier must meet the following
      requirements: * Only contains 1-63 alphanumeric characters and hyphens *
      Begins with an alphabetical character * Ends with a non-hyphen character
      * Not formatted as a UUID * Complies with [RFC
      1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)
    parent: Required. The resource name of the network policy to create a new
      external access firewall rule in. Resource names are schemeless URIs
      that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/networkPolicies/my-policy`
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server guarantees that
      a request doesn't result in creation of duplicate commitments for at
      least 60 minutes. For example, consider a situation where you make an
      initial request and the request times out. If you make the request again
      with the same request ID, the server can check if the original operation
      with the same request ID was received, and if so, will ignore the second
      request. This prevents clients from accidentally creating duplicate
      commitments. The request ID must be a valid UUID with the exception that
      zero UUID is not supported (********-0000-0000-0000-********0000).
  """

  externalAccessRule = _messages.MessageField('ExternalAccessRule', 1)
  externalAccessRuleId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesDeleteRequest(_messages.Message):
  r"""A
  VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesDeleteRequest
  object.

  Fields:
    name: Required. The resource name of the external access firewall rule to
      delete. Resource names are schemeless URIs that follow the conventions
      in https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/networkPolicies/my-
      policy/externalAccessRules/my-rule`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if the
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesGetRequest(_messages.Message):
  r"""A
  VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesGetRequest
  object.

  Fields:
    name: Required. The resource name of the external access firewall rule to
      retrieve. Resource names are schemeless URIs that follow the conventions
      in https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/networkPolicies/my-
      policy/externalAccessRules/my-rule`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesListRequest(_messages.Message):
  r"""A
  VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesListRequest
  object.

  Fields:
    filter: A filter expression that matches resources returned in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of
      external access rules, you can exclude the ones named `example-rule` by
      specifying `name != "example-rule"`. To filter on multiple expressions,
      provide each separate expression within parentheses. For example: ```
      (name = "example-rule") (createTime > "2021-04-12T08:15:10.40Z") ``` By
      default, each expression is an `AND` expression. However, you can
      include `AND` and `OR` expressions explicitly. For example: ``` (name =
      "example-rule-1") AND (createTime > "2021-04-12T08:15:10.40Z") OR (name
      = "example-rule-2") ```
    orderBy: Sorts list results by a certain order. By default, returned
      results are ordered by `name` in ascending order. You can also sort
      results in descending order based on the `name` value using
      `orderBy="name desc"`. Currently, only ordering by `name` is supported.
    pageSize: The maximum number of external access rules to return in one
      page. The service may return fewer than this value. The maximum value is
      coerced to 1000. The default value of this field is 500.
    pageToken: A page token, received from a previous
      `ListExternalAccessRulesRequest` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListExternalAccessRulesRequest` must match the call that provided the
      page token.
    parent: Required. The resource name of the network policy to query for
      external access firewall rules. Resource names are schemeless URIs that
      follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/networkPolicies/my-policy`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesPatchRequest(_messages.Message):
  r"""A
  VmwareengineProjectsLocationsNetworkPoliciesExternalAccessRulesPatchRequest
  object.

  Fields:
    externalAccessRule: A ExternalAccessRule resource to be passed as the
      request body.
    name: Output only. The resource name of this external access rule.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/networkPolicies/my-
      policy/externalAccessRules/my-rule`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if the
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the `ExternalAccessRule` resource by the update. The
      fields specified in the `update_mask` are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
  """

  externalAccessRule = _messages.MessageField('ExternalAccessRule', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class VmwareengineProjectsLocationsNetworkPoliciesFetchExternalAddressesRequest(_messages.Message):
  r"""A
  VmwareengineProjectsLocationsNetworkPoliciesFetchExternalAddressesRequest
  object.

  Fields:
    networkPolicy: Required. The resource name of the network policy to query
      for assigned external IP addresses. Resource names are schemeless URIs
      that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/networkPolicies/my-policy`
    pageSize: The maximum number of external IP addresses to return in one
      page. The service may return fewer than this value. The maximum value is
      coerced to 1000. The default value of this field is 500.
    pageToken: A page token, received from a previous
      `FetchNetworkPolicyExternalAddresses` call. Provide this to retrieve the
      subsequent page. When paginating, all parameters provided to
      `FetchNetworkPolicyExternalAddresses`, except for `page_size` and
      `page_token`, must match the call that provided the page token.
  """

  networkPolicy = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class VmwareengineProjectsLocationsNetworkPoliciesGetRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsNetworkPoliciesGetRequest object.

  Fields:
    name: Required. The resource name of the network policy to retrieve.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/networkPolicies/my-network-
      policy`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsNetworkPoliciesListRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsNetworkPoliciesListRequest object.

  Fields:
    filter: A filter expression that matches resources returned in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of
      network policies, you can exclude the ones named `example-policy` by
      specifying `name != "example-policy"`. To filter on multiple
      expressions, provide each separate expression within parentheses. For
      example: ``` (name = "example-policy") (createTime >
      "2021-04-12T08:15:10.40Z") ``` By default, each expression is an `AND`
      expression. However, you can include `AND` and `OR` expressions
      explicitly. For example: ``` (name = "example-policy-1") AND (createTime
      > "2021-04-12T08:15:10.40Z") OR (name = "example-policy-2") ```
    orderBy: Sorts list results by a certain order. By default, returned
      results are ordered by `name` in ascending order. You can also sort
      results in descending order based on the `name` value using
      `orderBy="name desc"`. Currently, only ordering by `name` is supported.
    pageSize: The maximum number of network policies to return in one page.
      The service may return fewer than this value. The maximum value is
      coerced to 1000. The default value of this field is 500.
    pageToken: A page token, received from a previous `ListNetworkPolicies`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListNetworkPolicies` must match the call
      that provided the page token.
    parent: Required. The resource name of the location (region) to query for
      network policies. Resource names are schemeless URIs that follow the
      conventions in https://cloud.google.com/apis/design/resource_names. For
      example: `projects/my-project/locations/us-central1`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmwareengineProjectsLocationsNetworkPoliciesPatchRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsNetworkPoliciesPatchRequest object.

  Fields:
    name: Output only. The resource name of this network policy. Resource
      names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/networkPolicies/my-network-
      policy`
    networkPolicy: A NetworkPolicy resource to be passed as the request body.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the `NetworkPolicy` resource by the update. The fields
      specified in the `update_mask` are relative to the resource, not the
      full request. A field will be overwritten if it is in the mask. If the
      user does not provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  networkPolicy = _messages.MessageField('NetworkPolicy', 2)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class VmwareengineProjectsLocationsNodeTypesGetRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsNodeTypesGetRequest object.

  Fields:
    name: Required. The resource name of the node type to retrieve. Resource
      names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-proj/locations/us-central1-a/nodeTypes/standard-72`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsNodeTypesListRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsNodeTypesListRequest object.

  Fields:
    filter: A filter expression that matches resources returned in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of
      node types, you can exclude the ones named `standard-72` by specifying
      `name != "standard-72"`. To filter on multiple expressions, provide each
      separate expression within parentheses. For example: ``` (name =
      "standard-72") (virtual_cpu_count > 2) ``` By default, each expression
      is an `AND` expression. However, you can include `AND` and `OR`
      expressions explicitly. For example: ``` (name = "standard-96") AND
      (virtual_cpu_count > 2) OR (name = "standard-72") ```
    pageSize: The maximum number of node types to return in one page. The
      service may return fewer than this value. The maximum value is coerced
      to 1000. The default value of this field is 500.
    pageToken: A page token, received from a previous `ListNodeTypes` call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to `ListNodeTypes` must match the call that provided
      the page token.
    parent: Required. The resource name of the location to be queried for node
      types. Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class VmwareengineProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class VmwareengineProjectsLocationsPrivateCloudsClustersCreateRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsClustersCreateRequest
  object.

  Fields:
    cluster: A Cluster resource to be passed as the request body.
    clusterId: Required. The user-provided identifier of the new `Cluster`.
      This identifier must be unique among clusters within the parent and
      becomes the final token in the name URI. The identifier must meet the
      following requirements: * Only contains 1-63 alphanumeric characters and
      hyphens * Begins with an alphabetical character * Ends with a non-hyphen
      character * Not formatted as a UUID * Complies with [RFC
      1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)
    parent: Required. The resource name of the private cloud to create a new
      cluster in. Resource names are schemeless URIs that follow the
      conventions in https://cloud.google.com/apis/design/resource_names. For
      example: `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud`
    requestId: Optional. The request ID must be a valid UUID with the
      exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
    validateOnly: Optional. True if you want the request to be validated and
      not executed; false otherwise.
  """

  cluster = _messages.MessageField('Cluster', 1)
  clusterId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class VmwareengineProjectsLocationsPrivateCloudsClustersDeleteRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsClustersDeleteRequest
  object.

  Fields:
    name: Required. The resource name of the cluster to delete. Resource names
      are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/clusters/my-cluster`
    requestId: Optional. The request ID must be a valid UUID with the
      exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class VmwareengineProjectsLocationsPrivateCloudsClustersGetIamPolicyRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsClustersGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class VmwareengineProjectsLocationsPrivateCloudsClustersGetRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsClustersGetRequest object.

  Fields:
    name: Required. The cluster resource name to retrieve. Resource names are
      schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/clusters/my-cluster`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsPrivateCloudsClustersListRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsClustersListRequest object.

  Fields:
    filter:  To filter on multiple expressions, provide each separate
      expression within parentheses. For example: ``` (name = "example-
      cluster") (nodeCount = "3") ``` By default, each expression is an `AND`
      expression. However, you can include `AND` and `OR` expressions
      explicitly. For example: ``` (name = "example-cluster-1") AND
      (createTime > "2021-04-12T08:15:10.40Z") OR (name = "example-cluster-2")
      ```
    orderBy: Sorts list results by a certain order. By default, returned
      results are ordered by `name` in ascending order. You can also sort
      results in descending order based on the `name` value using
      `orderBy="name desc"`. Currently, only ordering by `name` is supported.
    pageSize: The maximum number of clusters to return in one page. The
      service may return fewer than this value. The maximum value is coerced
      to 1000. The default value of this field is 500.
    pageToken: A page token, received from a previous `ListClusters` call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to `ListClusters` must match the call that provided
      the page token.
    parent: Required. The resource name of the private cloud to query for
      clusters. Resource names are schemeless URIs that follow the conventions
      in https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmwareengineProjectsLocationsPrivateCloudsClustersNodesGetRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsClustersNodesGetRequest
  object.

  Fields:
    name: Required. The resource name of the node to retrieve. For example: `p
      rojects/{project}/locations/{location}/privateClouds/{private_cloud}/clu
      sters/{cluster}/nodes/{node}`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsPrivateCloudsClustersNodesListRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsClustersNodesListRequest
  object.

  Fields:
    pageSize: The maximum number of nodes to return in one page. The service
      may return fewer than this value. The maximum value is coerced to 1000.
      The default value of this field is 500.
    pageToken: A page token, received from a previous `ListNodes` call.
      Provide this to retrieve the subsequent page. When paginating, all other
      parameters provided to `ListNodes` must match the call that provided the
      page token.
    parent: Required. The resource name of the cluster to be queried for
      nodes. Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/clusters/my-cluster`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class VmwareengineProjectsLocationsPrivateCloudsClustersPatchRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsClustersPatchRequest object.

  Fields:
    cluster: A Cluster resource to be passed as the request body.
    name: Output only. The resource name of this cluster. Resource names are
      schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/clusters/my-cluster`
    requestId: Optional. The request ID must be a valid UUID with the
      exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the `Cluster` resource by the update. The fields
      specified in the `updateMask` are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
    validateOnly: Optional. True if you want the request to be validated and
      not executed; false otherwise.
  """

  cluster = _messages.MessageField('Cluster', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class VmwareengineProjectsLocationsPrivateCloudsClustersSetIamPolicyRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsClustersSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class VmwareengineProjectsLocationsPrivateCloudsClustersTestIamPermissionsRequest(_messages.Message):
  r"""A
  VmwareengineProjectsLocationsPrivateCloudsClustersTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class VmwareengineProjectsLocationsPrivateCloudsCreateRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsCreateRequest object.

  Fields:
    parent: Required. The resource name of the location to create the new
      private cloud in. Resource names are schemeless URIs that follow the
      conventions in https://cloud.google.com/apis/design/resource_names. For
      example: `projects/my-project/locations/us-central1-a`
    privateCloud: A PrivateCloud resource to be passed as the request body.
    privateCloudId: Required. The user-provided identifier of the private
      cloud to be created. This identifier must be unique among each
      `PrivateCloud` within the parent and becomes the final token in the name
      URI. The identifier must meet the following requirements: * Only
      contains 1-63 alphanumeric characters and hyphens * Begins with an
      alphabetical character * Ends with a non-hyphen character * Not
      formatted as a UUID * Complies with [RFC
      1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)
    requestId: Optional. The request ID must be a valid UUID with the
      exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
    validateOnly: Optional. True if you want the request to be validated and
      not executed; false otherwise.
  """

  parent = _messages.StringField(1, required=True)
  privateCloud = _messages.MessageField('PrivateCloud', 2)
  privateCloudId = _messages.StringField(3)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class VmwareengineProjectsLocationsPrivateCloudsDeleteRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsDeleteRequest object.

  Fields:
    delayHours: Optional. Time delay of the deletion specified in hours. The
      default value is `3`. Specifying a non-zero value for this field changes
      the value of `PrivateCloud.state` to `DELETED` and sets `expire_time` to
      the planned deletion time. Deletion can be cancelled before
      `expire_time` elapses using VmwareEngine.UndeletePrivateCloud.
      Specifying a value of `0` for this field instead begins the deletion
      process and ceases billing immediately. During the final deletion
      process, the value of `PrivateCloud.state` becomes `PURGING`.
    force: Optional. If set to true, cascade delete is enabled and all
      children of this private cloud resource are also deleted. When this flag
      is set to false, the private cloud will not be deleted if there are any
      children other than the management cluster. The management cluster is
      always deleted.
    name: Required. The resource name of the private cloud to delete. Resource
      names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
    requestId: Optional. The request ID must be a valid UUID with the
      exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  delayHours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class VmwareengineProjectsLocationsPrivateCloudsExternalAddressesCreateRequest(_messages.Message):
  r"""A
  VmwareengineProjectsLocationsPrivateCloudsExternalAddressesCreateRequest
  object.

  Fields:
    externalAddress: A ExternalAddress resource to be passed as the request
      body.
    externalAddressId: Required. The user-provided identifier of the
      `ExternalAddress` to be created. This identifier must be unique among
      `ExternalAddress` resources within the parent and becomes the final
      token in the name URI. The identifier must meet the following
      requirements: * Only contains 1-63 alphanumeric characters and hyphens *
      Begins with an alphabetical character * Ends with a non-hyphen character
      * Not formatted as a UUID * Complies with [RFC
      1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)
    parent: Required. The resource name of the private cloud to create a new
      external IP address in. Resource names are schemeless URIs that follow
      the conventions in https://cloud.google.com/apis/design/resource_names.
      For example: `projects/my-project/locations/us-
      central1-a/privateClouds/my-cloud`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if the
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  externalAddress = _messages.MessageField('ExternalAddress', 1)
  externalAddressId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class VmwareengineProjectsLocationsPrivateCloudsExternalAddressesDeleteRequest(_messages.Message):
  r"""A
  VmwareengineProjectsLocationsPrivateCloudsExternalAddressesDeleteRequest
  object.

  Fields:
    name: Required. The resource name of the external IP address to delete.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/externalAddresses/my-ip`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if the
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class VmwareengineProjectsLocationsPrivateCloudsExternalAddressesGetRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsExternalAddressesGetRequest
  object.

  Fields:
    name: Required. The resource name of the external IP address to retrieve.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/externalAddresses/my-ip`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsPrivateCloudsExternalAddressesListRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsExternalAddressesListRequest
  object.

  Fields:
    filter: A filter expression that matches resources returned in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of
      IP addresses, you can exclude the ones named `example-ip` by specifying
      `name != "example-ip"`. To filter on multiple expressions, provide each
      separate expression within parentheses. For example: ``` (name =
      "example-ip") (createTime > "2021-04-12T08:15:10.40Z") ``` By default,
      each expression is an `AND` expression. However, you can include `AND`
      and `OR` expressions explicitly. For example: ``` (name = "example-
      ip-1") AND (createTime > "2021-04-12T08:15:10.40Z") OR (name = "example-
      ip-2") ```
    orderBy: Sorts list results by a certain order. By default, returned
      results are ordered by `name` in ascending order. You can also sort
      results in descending order based on the `name` value using
      `orderBy="name desc"`. Currently, only ordering by `name` is supported.
    pageSize: The maximum number of external IP addresses to return in one
      page. The service may return fewer than this value. The maximum value is
      coerced to 1000. The default value of this field is 500.
    pageToken: A page token, received from a previous `ListExternalAddresses`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListExternalAddresses` must match the call
      that provided the page token.
    parent: Required. The resource name of the private cloud to be queried for
      external IP addresses. Resource names are schemeless URIs that follow
      the conventions in https://cloud.google.com/apis/design/resource_names.
      For example: `projects/my-project/locations/us-
      central1-a/privateClouds/my-cloud`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmwareengineProjectsLocationsPrivateCloudsExternalAddressesPatchRequest(_messages.Message):
  r"""A
  VmwareengineProjectsLocationsPrivateCloudsExternalAddressesPatchRequest
  object.

  Fields:
    externalAddress: A ExternalAddress resource to be passed as the request
      body.
    name: Output only. The resource name of this external IP address. Resource
      names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/externalAddresses/my-address`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if the
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the `ExternalAddress` resource by the update. The fields
      specified in the `update_mask` are relative to the resource, not the
      full request. A field will be overwritten if it is in the mask. If the
      user does not provide a mask then all fields will be overwritten.
  """

  externalAddress = _messages.MessageField('ExternalAddress', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class VmwareengineProjectsLocationsPrivateCloudsGetDnsForwardingRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsGetDnsForwardingRequest
  object.

  Fields:
    name: Required. The resource name of a `DnsForwarding` to retrieve.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/dnsForwarding`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsPrivateCloudsGetIamPolicyRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class VmwareengineProjectsLocationsPrivateCloudsGetRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsGetRequest object.

  Fields:
    name: Required. The resource name of the private cloud to retrieve.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysCreateRequest(_messages.Message):
  r"""A
  VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysCreateRequest
  object.

  Fields:
    hcxActivationKey: A HcxActivationKey resource to be passed as the request
      body.
    hcxActivationKeyId: Required. The user-provided identifier of the
      `HcxActivationKey` to be created. This identifier must be unique among
      `HcxActivationKey` resources within the parent and becomes the final
      token in the name URI. The identifier must meet the following
      requirements: * Only contains 1-63 alphanumeric characters and hyphens *
      Begins with an alphabetical character * Ends with a non-hyphen character
      * Not formatted as a UUID * Complies with [RFC
      1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)
    parent: Required. The resource name of the private cloud to create the key
      for. Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/privateClouds/my-cloud`
    requestId: A request ID to identify requests. Specify a unique request ID
      so that if you must retry your request, the server will know to ignore
      the request if it has already been completed. The server guarantees that
      a request doesn't result in creation of duplicate commitments for at
      least 60 minutes. For example, consider a situation where you make an
      initial request and the request times out. If you make the request again
      with the same request ID, the server can check if original operation
      with the same request ID was received, and if so, will ignore the second
      request. This prevents clients from accidentally creating duplicate
      commitments. The request ID must be a valid UUID with the exception that
      zero UUID is not supported (********-0000-0000-0000-********0000).
  """

  hcxActivationKey = _messages.MessageField('HcxActivationKey', 1)
  hcxActivationKeyId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysGetIamPolicyRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysGetIamPolic
  yRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysGetRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysGetRequest
  object.

  Fields:
    name: Required. The resource name of the HCX activation key to retrieve.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/privateClouds/my-
      cloud/hcxActivationKeys/my-key`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysListRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysListRequest
  object.

  Fields:
    pageSize: The maximum number of HCX activation keys to return in one page.
      The service may return fewer than this value. The maximum value is
      coerced to 1000. The default value of this field is 500.
    pageToken: A page token, received from a previous `ListHcxActivationKeys`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListHcxActivationKeys` must match the call
      that provided the page token.
    parent: Required. The resource name of the private cloud to be queried for
      HCX activation keys. Resource names are schemeless URIs that follow the
      conventions in https://cloud.google.com/apis/design/resource_names. For
      example: `projects/my-project/locations/us-central1/privateClouds/my-
      cloud`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysSetIamPolicyRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysSetIamPolic
  yRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysTestIamPermissionsRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsHcxActivationKeysTestIamPerm
  issionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesCreateRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesCreateRequest
  object.

  Fields:
    identitySource: A IdentitySource resource to be passed as the request
      body.
    identitySourceId: Required. The user-provided identifier of the new
      `IdentitySource`. This identifier must be unique among identity sources
      within the parent and becomes the final token in the name URI. The
      identifier must meet the following requirements: * Only contains 1-63
      alphanumeric characters and hyphens * Begins with an alphabetical
      character * Ends with a non-hyphen character * Not formatted as a UUID *
      Complies with [RFC 1034](https://datatracker.ietf.org/doc/html/rfc1034)
      (section 3.5)
    parent: Required. The resource name of the private cloud to create a new
      identity source in. Resource names are schemeless URIs that follow the
      conventions in https://cloud.google.com/apis/design/resource_names. For
      example: `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if the
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
    validateOnly: Optional. True if you want the request to be validated and
      not executed; false otherwise.
  """

  identitySource = _messages.MessageField('IdentitySource', 1)
  identitySourceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesDeleteRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesDeleteRequest
  object.

  Fields:
    etag: Optional. Checksum used to ensure that the user-provided value is up
      to date before the server processes the request. The server compares
      provided checksum with the current checksum of the resource. If the
      user-provided value is out of date, this request returns an `ABORTED`
      error.
    name: Required. The resource name of the identity source to delete.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/identitySources/my-identity-source`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if the
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesGetRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesGetRequest
  object.

  Fields:
    name: Required. The resource name of the identity source to retrieve.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/identitySources/my-identity-source`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesListRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesListRequest
  object.

  Fields:
    filter: Optional. A filter expression that matches resources returned in
      the response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of
      identity sources, you can exclude the ones named `example-identity-
      source` by specifying `name != "example-identity-source"`. To filter on
      multiple expressions, provide each separate expression within
      parentheses. For example: ``` (name = "example-identity-source")
      (domain_name = "example-domain-name") ``` By default, each expression is
      an `AND` expression. However, you can include `AND` and `OR` expressions
      explicitly. For example: ``` (name = "example-identity-source-1") AND
      (createTime > "2021-04-12T08:15:10.40Z") OR (name = "example-identity-
      source-2") ```
    orderBy: Optional. Sorts list results by a certain order. By default,
      returned results are ordered by `name` in ascending order. You can also
      sort results in descending order based on the `name` value using
      `orderBy="name desc"`. Currently, only ordering by `name` is supported.
    pageSize: Optional. The maximum number of identity sources to return in
      one page. The service may return fewer than this value. The maximum
      value is coerced to 1000. The default value of this field is 500.
    pageToken: Optional. A page token, received from a previous
      `ListIdentitySources` call. Provide this to retrieve the subsequent
      page. When paginating, all other parameters provided to
      `ListIdentitySources` must match the call that provided the page token.
    parent: Required. The resource name of the private cloud to query for
      identity sources. Resource names are schemeless URIs that follow the
      conventions in https://cloud.google.com/apis/design/resource_names. For
      example: `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesPatchRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsIdentitySourcesPatchRequest
  object.

  Fields:
    identitySource: A IdentitySource resource to be passed as the request
      body.
    name: Output only. The resource name of this identity source. Resource
      names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/identitySources/my-identity-source`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the `UpdateIdentitySource` resource by the update. The
      fields specified in the `updateMask` are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
    validateOnly: Optional. True if you want the request to be validated and
      not executed; false otherwise.
  """

  identitySource = _messages.MessageField('IdentitySource', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class VmwareengineProjectsLocationsPrivateCloudsListRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsListRequest object.

  Fields:
    filter: A filter expression that matches resources returned in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of
      private clouds, you can exclude the ones named `example-pc` by
      specifying `name != "example-pc"`. You can also filter nested fields.
      For example, you could specify `networkConfig.managementCidr =
      "***********/24"` to include private clouds only if they have a matching
      address in their network configuration. To filter on multiple
      expressions, provide each separate expression within parentheses. For
      example: ``` (name = "example-pc") (createTime >
      "2021-04-12T08:15:10.40Z") ``` By default, each expression is an `AND`
      expression. However, you can include `AND` and `OR` expressions
      explicitly. For example: ``` (name = "private-cloud-1") AND (createTime
      > "2021-04-12T08:15:10.40Z") OR (name = "private-cloud-2") ```
    orderBy: Sorts list results by a certain order. By default, returned
      results are ordered by `name` in ascending order. You can also sort
      results in descending order based on the `name` value using
      `orderBy="name desc"`. Currently, only ordering by `name` is supported.
    pageSize: The maximum number of private clouds to return in one page. The
      service may return fewer than this value. The maximum value is coerced
      to 1000. The default value of this field is 500.
    pageToken: A page token, received from a previous `ListPrivateClouds`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListPrivateClouds` must match the call
      that provided the page token.
    parent: Required. The resource name of the private cloud to be queried for
      clusters. Resource names are schemeless URIs that follow the conventions
      in https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmwareengineProjectsLocationsPrivateCloudsLoggingServersCreateRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsLoggingServersCreateRequest
  object.

  Fields:
    loggingServer: A LoggingServer resource to be passed as the request body.
    loggingServerId: Required. The user-provided identifier of the
      `LoggingServer` to be created. This identifier must be unique among
      `LoggingServer` resources within the parent and becomes the final token
      in the name URI. The identifier must meet the following requirements: *
      Only contains 1-63 alphanumeric characters and hyphens * Begins with an
      alphabetical character * Ends with a non-hyphen character * Not
      formatted as a UUID * Complies with [RFC
      1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)
    parent: Required. The resource name of the private cloud to create a new
      Logging Server in. Resource names are schemeless URIs that follow the
      conventions in https://cloud.google.com/apis/design/resource_names. For
      example: `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  loggingServer = _messages.MessageField('LoggingServer', 1)
  loggingServerId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class VmwareengineProjectsLocationsPrivateCloudsLoggingServersDeleteRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsLoggingServersDeleteRequest
  object.

  Fields:
    etag: Optional. Checksum used to ensure that the user-provided value is up
      to date before the server processes the request. The server compares
      provided checksum with the current checksum of the resource. If the
      user-provided value is out of date, this request returns an `ABORTED`
      error.
    name: Required. The resource name of the logging server to delete.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/loggingServers/my-logging-server`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class VmwareengineProjectsLocationsPrivateCloudsLoggingServersGetRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsLoggingServersGetRequest
  object.

  Fields:
    name: Required. The resource name of the Logging Server to retrieve.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/loggingServers/my-logging-server`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsPrivateCloudsLoggingServersListRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsLoggingServersListRequest
  object.

  Fields:
    filter: A filter expression that matches resources returned in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of
      logging servers, you can exclude the ones named `example-server` by
      specifying `name != "example-server"`. To filter on multiple
      expressions, provide each separate expression within parentheses. For
      example: ``` (name = "example-server") (createTime >
      "2021-04-12T08:15:10.40Z") ``` By default, each expression is an `AND`
      expression. However, you can include `AND` and `OR` expressions
      explicitly. For example: ``` (name = "example-server-1") AND (createTime
      > "2021-04-12T08:15:10.40Z") OR (name = "example-server-2") ```
    orderBy: Sorts list results by a certain order. By default, returned
      results are ordered by `name` in ascending order. You can also sort
      results in descending order based on the `name` value using
      `orderBy="name desc"`. Currently, only ordering by `name` is supported.
    pageSize: The maximum number of logging servers to return in one page. The
      service may return fewer than this value. The maximum value is coerced
      to 1000. The default value of this field is 500.
    pageToken: A page token, received from a previous
      `ListLoggingServersRequest` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListLoggingServersRequest` must match the call that provided the page
      token.
    parent: Required. The resource name of the private cloud to be queried for
      logging servers. Resource names are schemeless URIs that follow the
      conventions in https://cloud.google.com/apis/design/resource_names. For
      example: `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmwareengineProjectsLocationsPrivateCloudsLoggingServersPatchRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsLoggingServersPatchRequest
  object.

  Fields:
    loggingServer: A LoggingServer resource to be passed as the request body.
    name: Output only. The resource name of this logging server. Resource
      names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/loggingServers/my-logging-server`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the `LoggingServer` resource by the update. The fields
      specified in the `update_mask` are relative to the resource, not the
      full request. A field will be overwritten if it is in the mask. If the
      user does not provide a mask then all fields will be overwritten.
  """

  loggingServer = _messages.MessageField('LoggingServer', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsCreateRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsCre
  ateRequest object.

  Fields:
    managementDnsZoneBinding: A ManagementDnsZoneBinding resource to be passed
      as the request body.
    managementDnsZoneBindingId: Required. The user-provided identifier of the
      `ManagementDnsZoneBinding` resource to be created. This identifier must
      be unique among `ManagementDnsZoneBinding` resources within the parent
      and becomes the final token in the name URI. The identifier must meet
      the following requirements: * Only contains 1-63 alphanumeric characters
      and hyphens * Begins with an alphabetical character * Ends with a non-
      hyphen character * Not formatted as a UUID * Complies with [RFC
      1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)
    parent: Required. The resource name of the private cloud to create a new
      management DNS zone binding for. Resource names are schemeless URIs that
      follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if the
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  managementDnsZoneBinding = _messages.MessageField('ManagementDnsZoneBinding', 1)
  managementDnsZoneBindingId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsDeleteRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsDel
  eteRequest object.

  Fields:
    name: Required. The resource name of the management DNS zone binding to
      delete. Resource names are schemeless URIs that follow the conventions
      in https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/managementDnsZoneBindings/my-management-dns-zone-binding`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if the
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsGetRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsGet
  Request object.

  Fields:
    name: Required. The resource name of the management DNS zone binding to
      retrieve. Resource names are schemeless URIs that follow the conventions
      in https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/managementDnsZoneBindings/my-management-dns-zone-binding`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsListRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsLis
  tRequest object.

  Fields:
    filter: A filter expression that matches resources returned in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of
      Management DNS Zone Bindings, you can exclude the ones named `example-
      management-dns-zone-binding` by specifying `name != "example-management-
      dns-zone-binding"`. To filter on multiple expressions, provide each
      separate expression within parentheses. For example: ``` (name =
      "example-management-dns-zone-binding") (createTime >
      "2021-04-12T08:15:10.40Z") ``` By default, each expression is an `AND`
      expression. However, you can include `AND` and `OR` expressions
      explicitly. For example: ``` (name = "example-management-dns-zone-
      binding-1") AND (createTime > "2021-04-12T08:15:10.40Z") OR (name =
      "example-management-dns-zone-binding-2") ```
    orderBy: Sorts list results by a certain order. By default, returned
      results are ordered by `name` in ascending order. You can also sort
      results in descending order based on the `name` value using
      `orderBy="name desc"`. Currently, only ordering by `name` is supported.
    pageSize: The maximum number of management DNS zone bindings to return in
      one page. The service may return fewer than this value. The maximum
      value is coerced to 1000. The default value of this field is 500.
    pageToken: A page token, received from a previous
      `ListManagementDnsZoneBindings` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListManagementDnsZoneBindings` must match the call that provided the
      page token.
    parent: Required. The resource name of the private cloud to be queried for
      management DNS zone bindings. Resource names are schemeless URIs that
      follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsPatchRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsPat
  chRequest object.

  Fields:
    managementDnsZoneBinding: A ManagementDnsZoneBinding resource to be passed
      as the request body.
    name: Output only. The resource name of this binding. Resource names are
      schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/managementDnsZoneBindings/my-management-dns-zone-binding`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if the
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the `ManagementDnsZoneBinding` resource by the update.
      The fields specified in the `update_mask` are relative to the resource,
      not the full request. A field will be overwritten if it is in the mask.
      If the user does not provide a mask then all fields will be overwritten.
  """

  managementDnsZoneBinding = _messages.MessageField('ManagementDnsZoneBinding', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsRepairRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsManagementDnsZoneBindingsRep
  airRequest object.

  Fields:
    name: Required. The resource name of the management DNS zone binding to
      repair. Resource names are schemeless URIs that follow the conventions
      in https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/managementDnsZoneBindings/my-management-dns-zone-binding`
    repairManagementDnsZoneBindingRequest: A
      RepairManagementDnsZoneBindingRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  repairManagementDnsZoneBindingRequest = _messages.MessageField('RepairManagementDnsZoneBindingRequest', 2)


class VmwareengineProjectsLocationsPrivateCloudsPatchRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsPatchRequest object.

  Fields:
    name: Output only. The resource name of this private cloud. Resource names
      are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
    privateCloud: A PrivateCloud resource to be passed as the request body.
    requestId: Optional. The request ID must be a valid UUID with the
      exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the `PrivateCloud` resource by the update. The fields
      specified in `updateMask` are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  privateCloud = _messages.MessageField('PrivateCloud', 2)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class VmwareengineProjectsLocationsPrivateCloudsResetNsxCredentialsRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsResetNsxCredentialsRequest
  object.

  Fields:
    privateCloud: Required. The resource name of the private cloud to reset
      credentials for. Resource names are schemeless URIs that follow the
      conventions in https://cloud.google.com/apis/design/resource_names. For
      example: `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud`
    resetNsxCredentialsRequest: A ResetNsxCredentialsRequest resource to be
      passed as the request body.
  """

  privateCloud = _messages.StringField(1, required=True)
  resetNsxCredentialsRequest = _messages.MessageField('ResetNsxCredentialsRequest', 2)


class VmwareengineProjectsLocationsPrivateCloudsResetVcenterCredentialsRequest(_messages.Message):
  r"""A
  VmwareengineProjectsLocationsPrivateCloudsResetVcenterCredentialsRequest
  object.

  Fields:
    privateCloud: Required. The resource name of the private cloud to reset
      credentials for. Resource names are schemeless URIs that follow the
      conventions in https://cloud.google.com/apis/design/resource_names. For
      example: `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud`
    resetVcenterCredentialsRequest: A ResetVcenterCredentialsRequest resource
      to be passed as the request body.
  """

  privateCloud = _messages.StringField(1, required=True)
  resetVcenterCredentialsRequest = _messages.MessageField('ResetVcenterCredentialsRequest', 2)


class VmwareengineProjectsLocationsPrivateCloudsSetIamPolicyRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class VmwareengineProjectsLocationsPrivateCloudsShowNsxCredentialsRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsShowNsxCredentialsRequest
  object.

  Fields:
    privateCloud: Required. The resource name of the private cloud to be
      queried for credentials. Resource names are schemeless URIs that follow
      the conventions in https://cloud.google.com/apis/design/resource_names.
      For example: `projects/my-project/locations/us-
      central1-a/privateClouds/my-cloud`
  """

  privateCloud = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsPrivateCloudsShowVcenterCredentialsRequest(_messages.Message):
  r"""A
  VmwareengineProjectsLocationsPrivateCloudsShowVcenterCredentialsRequest
  object.

  Fields:
    privateCloud: Required. The resource name of the private cloud to be
      queried for credentials. Resource names are schemeless URIs that follow
      the conventions in https://cloud.google.com/apis/design/resource_names.
      For example: `projects/my-project/locations/us-
      central1-a/privateClouds/my-cloud`
    username: Optional. The username of the user to be queried for
      credentials. The default value of this <NAME_EMAIL>.
      The provided value must be one of the following: <EMAIL>,
      <EMAIL>, <EMAIL>, solution-
      <EMAIL>, <EMAIL>, solution-
      <EMAIL>, <EMAIL>.
  """

  privateCloud = _messages.StringField(1, required=True)
  username = _messages.StringField(2)


class VmwareengineProjectsLocationsPrivateCloudsSubnetsGetRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsSubnetsGetRequest object.

  Fields:
    name: Required. The resource name of the subnet to retrieve. Resource
      names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/subnets/my-subnet`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsPrivateCloudsSubnetsListRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsSubnetsListRequest object.

  Fields:
    filter: A filter expression that matches resources returned in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of
      subnets, you can exclude the ones named `example-subnet` by specifying
      `name != "example-subnet"`. To filter on multiple expressions, provide
      each separate expression within parentheses. For example: ``` (name =
      "example-subnet") (createTime > "2021-04-12T08:15:10.40Z") ``` By
      default, each expression is an `AND` expression. However, you can
      include `AND` and `OR` expressions explicitly. For example: ``` (name =
      "example-subnet-1") AND (subnetId > "example-subnet-id") OR (name =
      "example-subnet-2") ```
    orderBy: Sorts list results by a certain order. By default, returned
      results are ordered by `name` in ascending order. You can also sort
      results in descending order based on the `name` value using
      `orderBy="name desc"`. Currently, only ordering by `name` is supported.
    pageSize: The maximum number of subnets to return in one page. The service
      may return fewer than this value. The maximum value is coerced to 1000.
      The default value of this field is 500.
    pageToken: A page token, received from a previous `ListSubnetsRequest`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListSubnetsRequest` must match the call
      that provided the page token.
    parent: Required. The resource name of the private cloud to be queried for
      subnets. Resource names are schemeless URIs that follow the conventions
      in https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmwareengineProjectsLocationsPrivateCloudsSubnetsPatchRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsSubnetsPatchRequest object.

  Fields:
    name: Output only. The resource name of this subnet. Resource names are
      schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/subnets/my-subnet`
    subnet: A Subnet resource to be passed as the request body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the `Subnet` resource by the update. The fields specified
      in the `update_mask` are relative to the resource, not the full request.
      A field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  subnet = _messages.MessageField('Subnet', 2)
  updateMask = _messages.StringField(3)


class VmwareengineProjectsLocationsPrivateCloudsTestIamPermissionsRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class VmwareengineProjectsLocationsPrivateCloudsUndeleteRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsUndeleteRequest object.

  Fields:
    name: Required. The resource name of the private cloud scheduled for
      deletion. Resource names are schemeless URIs that follow the conventions
      in https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-cloud`
    undeletePrivateCloudRequest: A UndeletePrivateCloudRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  undeletePrivateCloudRequest = _messages.MessageField('UndeletePrivateCloudRequest', 2)


class VmwareengineProjectsLocationsPrivateCloudsUpdateDnsForwardingRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateCloudsUpdateDnsForwardingRequest
  object.

  Fields:
    dnsForwarding: A DnsForwarding resource to be passed as the request body.
    name: Output only. The resource name of this DNS profile. Resource names
      are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1-a/privateClouds/my-
      cloud/dnsForwarding`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the `DnsForwarding` resource by the update. The fields
      specified in the `update_mask` are relative to the resource, not the
      full request. A field will be overwritten if it is in the mask. If the
      user does not provide a mask then all fields will be overwritten.
  """

  dnsForwarding = _messages.MessageField('DnsForwarding', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class VmwareengineProjectsLocationsPrivateConnectionsCreateRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateConnectionsCreateRequest object.

  Fields:
    parent: Required. The resource name of the location to create the new
      private connection in. Private connection is a regional resource.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1`
    privateConnection: A PrivateConnection resource to be passed as the
      request body.
    privateConnectionId: Required. The user-provided identifier of the new
      private connection. This identifier must be unique among private
      connection resources within the parent and becomes the final token in
      the name URI. The identifier must meet the following requirements: *
      Only contains 1-63 alphanumeric characters and hyphens * Begins with an
      alphabetical character * Ends with a non-hyphen character * Not
      formatted as a UUID * Complies with [RFC
      1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  parent = _messages.StringField(1, required=True)
  privateConnection = _messages.MessageField('PrivateConnection', 2)
  privateConnectionId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class VmwareengineProjectsLocationsPrivateConnectionsDeleteRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateConnectionsDeleteRequest object.

  Fields:
    name: Required. The resource name of the private connection to be deleted.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/privateConnections/my-
      connection`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class VmwareengineProjectsLocationsPrivateConnectionsGetRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateConnectionsGetRequest object.

  Fields:
    name: Required. The resource name of the private connection to retrieve.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/privateConnections/my-
      connection`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsPrivateConnectionsListRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateConnectionsListRequest object.

  Fields:
    filter: A filter expression that matches resources returned in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of
      private connections, you can exclude the ones named `example-connection`
      by specifying `name != "example-connection"`. To filter on multiple
      expressions, provide each separate expression within parentheses. For
      example: ``` (name = "example-connection") (createTime >
      "2022-09-22T08:15:10.40Z") ``` By default, each expression is an `AND`
      expression. However, you can include `AND` and `OR` expressions
      explicitly. For example: ``` (name = "example-connection-1") AND
      (createTime > "2021-04-12T08:15:10.40Z") OR (name = "example-
      connection-2") ```
    orderBy: Sorts list results by a certain order. By default, returned
      results are ordered by `name` in ascending order. You can also sort
      results in descending order based on the `name` value using
      `orderBy="name desc"`. Currently, only ordering by `name` is supported.
    pageSize: The maximum number of private connections to return in one page.
      The maximum value is coerced to 1000. The default value of this field is
      500.
    pageToken: A page token, received from a previous `ListPrivateConnections`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListPrivateConnections` must match the
      call that provided the page token.
    parent: Required. The resource name of the location to query for private
      connections. Resource names are schemeless URIs that follow the
      conventions in https://cloud.google.com/apis/design/resource_names. For
      example: `projects/my-project/locations/us-central1`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmwareengineProjectsLocationsPrivateConnectionsPatchRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsPrivateConnectionsPatchRequest object.

  Fields:
    name: Output only. The resource name of the private connection. Resource
      names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/us-central1/privateConnections/my-
      connection`
    privateConnection: A PrivateConnection resource to be passed as the
      request body.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the `PrivateConnection` resource by the update. The
      fields specified in the `update_mask` are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  privateConnection = _messages.MessageField('PrivateConnection', 2)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class VmwareengineProjectsLocationsPrivateConnectionsPeeringRoutesListRequest(_messages.Message):
  r"""A
  VmwareengineProjectsLocationsPrivateConnectionsPeeringRoutesListRequest
  object.

  Fields:
    pageSize: The maximum number of peering routes to return in one page. The
      service may return fewer than this value. The maximum value is coerced
      to 1000. The default value of this field is 500.
    pageToken: A page token, received from a previous
      `ListPrivateConnectionPeeringRoutes` call. Provide this to retrieve the
      subsequent page. When paginating, all other parameters provided to
      `ListPrivateConnectionPeeringRoutes` must match the call that provided
      the page token.
    parent: Required. The resource name of the private connection to retrieve
      peering routes from. Resource names are schemeless URIs that follow the
      conventions in https://cloud.google.com/apis/design/resource_names. For
      example: `projects/my-project/locations/us-west1/privateConnections/my-
      connection`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class VmwareengineProjectsLocationsVmwareEngineNetworksCreateRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsVmwareEngineNetworksCreateRequest object.

  Fields:
    parent: Required. The resource name of the location to create the new
      VMware Engine network in. A VMware Engine network of type `LEGACY` is a
      regional resource, and a VMware Engine network of type `STANDARD` is a
      global resource. Resource names are schemeless URIs that follow the
      conventions in https://cloud.google.com/apis/design/resource_names. For
      example: `projects/my-project/locations/global`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
    vmwareEngineNetwork: A VmwareEngineNetwork resource to be passed as the
      request body.
    vmwareEngineNetworkId: Required. The user-provided identifier of the new
      VMware Engine network. This identifier must be unique among VMware
      Engine network resources within the parent and becomes the final token
      in the name URI. The identifier must meet the following requirements: *
      For networks of type LEGACY, adheres to the format: `{region-
      id}-default`. Replace `{region-id}` with the region where you want to
      create the VMware Engine network. For example, "us-central1-default". *
      Only contains 1-63 alphanumeric characters and hyphens * Begins with an
      alphabetical character * Ends with a non-hyphen character * Not
      formatted as a UUID * Complies with [RFC
      1034](https://datatracker.ietf.org/doc/html/rfc1034) (section 3.5)
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  vmwareEngineNetwork = _messages.MessageField('VmwareEngineNetwork', 3)
  vmwareEngineNetworkId = _messages.StringField(4)


class VmwareengineProjectsLocationsVmwareEngineNetworksDeleteRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsVmwareEngineNetworksDeleteRequest object.

  Fields:
    etag: Optional. Checksum used to ensure that the user-provided value is up
      to date before the server processes the request. The server compares
      provided checksum with the current checksum of the resource. If the
      user-provided value is out of date, this request returns an `ABORTED`
      error.
    name: Required. The resource name of the VMware Engine network to be
      deleted. Resource names are schemeless URIs that follow the conventions
      in https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/global/vmwareEngineNetworks/my-network`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
  """

  etag = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class VmwareengineProjectsLocationsVmwareEngineNetworksGetRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsVmwareEngineNetworksGetRequest object.

  Fields:
    name: Required. The resource name of the VMware Engine network to
      retrieve. Resource names are schemeless URIs that follow the conventions
      in https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/global/vmwareEngineNetworks/my-network`
  """

  name = _messages.StringField(1, required=True)


class VmwareengineProjectsLocationsVmwareEngineNetworksListRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsVmwareEngineNetworksListRequest object.

  Fields:
    filter: A filter expression that matches resources returned in the
      response. The expression must specify the field name, a comparison
      operator, and the value that you want to use for filtering. The value
      must be a string, a number, or a boolean. The comparison operator must
      be `=`, `!=`, `>`, or `<`. For example, if you are filtering a list of
      network peerings, you can exclude the ones named `example-network` by
      specifying `name != "example-network"`. To filter on multiple
      expressions, provide each separate expression within parentheses. For
      example: ``` (name = "example-network") (createTime >
      "2021-04-12T08:15:10.40Z") ``` By default, each expression is an `AND`
      expression. However, you can include `AND` and `OR` expressions
      explicitly. For example: ``` (name = "example-network-1") AND
      (createTime > "2021-04-12T08:15:10.40Z") OR (name = "example-network-2")
      ```
    orderBy: Sorts list results by a certain order. By default, returned
      results are ordered by `name` in ascending order. You can also sort
      results in descending order based on the `name` value using
      `orderBy="name desc"`. Currently, only ordering by `name` is supported.
    pageSize: The maximum number of results to return in one page. The maximum
      value is coerced to 1000. The default value of this field is 500.
    pageToken: A page token, received from a previous
      `ListVmwareEngineNetworks` call. Provide this to retrieve the subsequent
      page. When paginating, all other parameters provided to
      `ListVmwareEngineNetworks` must match the call that provided the page
      token.
    parent: Required. The resource name of the location to query for VMware
      Engine networks. Resource names are schemeless URIs that follow the
      conventions in https://cloud.google.com/apis/design/resource_names. For
      example: `projects/my-project/locations/global`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class VmwareengineProjectsLocationsVmwareEngineNetworksPatchRequest(_messages.Message):
  r"""A VmwareengineProjectsLocationsVmwareEngineNetworksPatchRequest object.

  Fields:
    name: Output only. The resource name of the VMware Engine network.
      Resource names are schemeless URIs that follow the conventions in
      https://cloud.google.com/apis/design/resource_names. For example:
      `projects/my-project/locations/global/vmwareEngineNetworks/my-network`
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server
      guarantees that a request doesn't result in creation of duplicate
      commitments for at least 60 minutes. For example, consider a situation
      where you make an initial request and the request times out. If you make
      the request again with the same request ID, the server can check if
      original operation with the same request ID was received, and if so,
      will ignore the second request. This prevents clients from accidentally
      creating duplicate commitments. The request ID must be a valid UUID with
      the exception that zero UUID is not supported
      (********-0000-0000-0000-********0000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the VMware Engine network resource by the update. The
      fields specified in the `update_mask` are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
      Only the following fields can be updated: `description`.
    vmwareEngineNetwork: A VmwareEngineNetwork resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  updateMask = _messages.StringField(3)
  vmwareEngineNetwork = _messages.MessageField('VmwareEngineNetwork', 4)


class VpcNetwork(_messages.Message):
  r"""Represents a VMware Engine VPC network that is managed by a VMware
  Engine network resource.

  Enums:
    TypeValueValuesEnum: Output only. Type of VPC network (INTRANET, INTERNET,
      or GOOGLE_CLOUD)

  Fields:
    network: Output only. The relative resource name of the service VPC
      network this VMware Engine network is attached to. For example:
      `projects/123123/global/networks/my-network`
    type: Output only. Type of VPC network (INTRANET, INTERNET, or
      GOOGLE_CLOUD)
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of VPC network (INTRANET, INTERNET, or GOOGLE_CLOUD)

    Values:
      TYPE_UNSPECIFIED: The default value. This value should never be used.
      INTRANET: VPC network that will be peered with a consumer VPC network or
        the intranet VPC of another VMware Engine network. Access a private
        cloud through Compute Engine VMs on a peered VPC network or an on-
        premises resource connected to a peered consumer VPC network.
      INTERNET: VPC network used for internet access to and from a private
        cloud.
      GOOGLE_CLOUD: VPC network used for access to Google Cloud services like
        Cloud Storage.
    """
    TYPE_UNSPECIFIED = 0
    INTRANET = 1
    INTERNET = 2
    GOOGLE_CLOUD = 3

  network = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

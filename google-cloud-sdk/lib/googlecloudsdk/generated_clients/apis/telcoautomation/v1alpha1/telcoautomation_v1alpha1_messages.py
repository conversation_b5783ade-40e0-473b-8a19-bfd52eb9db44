"""Generated message classes for telcoautomation version v1alpha1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'telcoautomation'


class ApplyDeploymentRequest(_messages.Message):
  r"""Request object for `ApplyDeployment`. The resources in given deployment
  gets applied to Orchestration Cluster. A new revision is created when a
  deployment is applied.
  """



class ApplyHydratedDeploymentRequest(_messages.Message):
  r"""Request for applying a hydrated deployment."""


class ApproveBlueprintRequest(_messages.Message):
  r"""Request object for `ApproveBlueprint`."""


class Blueprint(_messages.Message):
  r"""A Blueprint contains a collection of kubernetes resources in the form of
  YAML files. The file contents of a blueprint are collectively known as
  package. A blueprint can be a) imported from TNA's public catalog b)
  modified as per a user's need c) proposed and approved. On approval, a
  revision of blueprint is created which can be used to create a deployment on
  Orchestration Cluster.

  Enums:
    ApprovalStateValueValuesEnum: Output only. Approval state of the blueprint
      (DRAFT, PROPOSED, APPROVED)
    DeploymentLevelValueValuesEnum: Output only. DeploymentLevel of a
      blueprint signifies where the blueprint will be applied. e.g.
      [HYDRATION, DEPLOYMENT]

  Messages:
    LabelsValue: Optional. Labels are key-value attributes that can be set on
      a blueprint resource by the user.

  Fields:
    approvalState: Output only. Approval state of the blueprint (DRAFT,
      PROPOSED, APPROVED)
    createTime: Output only. Blueprint creation time.
    deploymentLevel: Output only. DeploymentLevel of a blueprint signifies
      where the blueprint will be applied. e.g. [HYDRATION, DEPLOYMENT]
    displayName: Optional. Human readable name of a Blueprint.
    files: Optional. Files present in a blueprint. When invoking
      UpdateBlueprint API, only the modified files should be included in this.
      Files that are not included in the update of a blueprint will not be
      changed.
    labels: Optional. Labels are key-value attributes that can be set on a
      blueprint resource by the user.
    name: The name of the blueprint. If unspecified, the name will be
      autogenerated from server side. Name of the blueprint must not contain
      `@` character.
    repository: Output only. Name of the repository where the blueprint files
      are stored.
    revisionCreateTime: Output only. The timestamp that the revision was
      created.
    revisionId: Output only. Immutable. The revision ID of the blueprint. A
      new revision is committed whenever a blueprint is approved.
    sourceBlueprint: Required. Immutable. The public blueprint ID from which
      this blueprint was created.
    updateTime: Output only. The timestamp when the blueprint was updated.
  """

  class ApprovalStateValueValuesEnum(_messages.Enum):
    r"""Output only. Approval state of the blueprint (DRAFT, PROPOSED,
    APPROVED)

    Values:
      APPROVAL_STATE_UNSPECIFIED: Unspecified state.
      DRAFT: A blueprint starts in DRAFT state once it is created. All edits
        are made to the blueprint in DRAFT state.
      PROPOSED: When the edits are ready for review, blueprint can be proposed
        and moves to PROPOSED state. Edits cannot be made to a blueprint in
        PROPOSED state.
      APPROVED: When a proposed blueprint is approved, it moves to APPROVED
        state. A new revision is committed. The latest committed revision can
        be used to create a deployment on Orchestration Cluster. Edits to an
        APPROVED blueprint changes its state back to DRAFT. The last committed
        revision of a blueprint represents its latest APPROVED state.
    """
    APPROVAL_STATE_UNSPECIFIED = 0
    DRAFT = 1
    PROPOSED = 2
    APPROVED = 3

  class DeploymentLevelValueValuesEnum(_messages.Enum):
    r"""Output only. DeploymentLevel of a blueprint signifies where the
    blueprint will be applied. e.g. [HYDRATION, DEPLOYMENT]

    Values:
      DEPLOYMENT_LEVEL_UNSPECIFIED: Default unspecified deployment level.
      HYDRATION: Blueprints at HYDRATION level cannot be used to create a
        Deployment (A user cannot manually initate deployment of these
        blueprints on orchestration or workload cluster). These blueprints
        stay in a user's private catalog and are configured and deployed by
        TNA automation.
      DEPLOYMENT: Blueprints at DEPLOYMENT level can be a) Modified in private
        catalog. b) Used to create a deployment on orchestration cluster by
        the user, once approved.
    """
    DEPLOYMENT_LEVEL_UNSPECIFIED = 0
    HYDRATION = 1
    DEPLOYMENT = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels are key-value attributes that can be set on a
    blueprint resource by the user.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  approvalState = _messages.EnumField('ApprovalStateValueValuesEnum', 1)
  createTime = _messages.StringField(2)
  deploymentLevel = _messages.EnumField('DeploymentLevelValueValuesEnum', 3)
  displayName = _messages.StringField(4)
  files = _messages.MessageField('File', 5, repeated=True)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  repository = _messages.StringField(8)
  revisionCreateTime = _messages.StringField(9)
  revisionId = _messages.StringField(10)
  sourceBlueprint = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CidrBlock(_messages.Message):
  r"""CidrBlock contains an optional name and one CIDR block.

  Fields:
    cidrBlock: Optional. cidr_block must be specified in CIDR notation when
      using master_authorized_networks_config. Currently, the user could still
      use the deprecated man_block field, so this field is currently optional,
      but will be required in the future.
    displayName: Optional. display_name is an optional field for users to
      identify CIDR blocks.
  """

  cidrBlock = _messages.StringField(1)
  displayName = _messages.StringField(2)


class Deployment(_messages.Message):
  r"""Deployment contains a collection of YAML files (This collection is also
  known as package) that can to applied on an orchestration cluster (GKE
  cluster with TNA addons).

  Enums:
    StateValueValuesEnum: Output only. State of the deployment (DRAFT,
      APPLIED).

  Messages:
    LabelsValue: Optional. Labels are key-value attributes that can be set on
      a deployment resource by the user.

  Fields:
    createTime: Output only. Deployment creation time.
    displayName: Optional. Human readable name of a Deployment.
    files: Optional. Files present in a deployment. When invoking
      UpdateDeployment API, only the modified files should be included in
      this. Files that are not included in the update of a deployment will not
      be changed.
    labels: Optional. Labels are key-value attributes that can be set on a
      deployment resource by the user.
    name: The name of the deployment.
    repository: Output only. Name of the repository where the deployment
      package files are stored.
    revisionCreateTime: Output only. The timestamp that the revision was
      created.
    revisionId: Output only. Immutable. The revision ID of the deployment. A
      new revision is committed whenever a change in deployment is applied.
    sourceBlueprintRevision: Required. Immutable. The blueprint revision from
      which this deployment was created.
    state: Output only. State of the deployment (DRAFT, APPLIED).
    updateTime: Output only. The timestamp when the deployment was updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the deployment (DRAFT, APPLIED).

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      DRAFT: A deployment starts in DRAFT state. All edits are made in DRAFT
        state. A deployment opened for editing after applying will be in draft
        state, while its prevision revision will be its current applied
        version.
      APPLIED: This state means that the contents (YAML files containing
        kubernetes resources) of the deployment have been applied to an
        Orchestration Cluster. A revision is created when a deployment is
        applied. This revision will represent the latest view of what is
        applied on the cluster until the deployment is modified and applied
        again, which will create a new revision.
    """
    STATE_UNSPECIFIED = 0
    DRAFT = 1
    APPLIED = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels are key-value attributes that can be set on a
    deployment resource by the user.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  files = _messages.MessageField('File', 3, repeated=True)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  repository = _messages.StringField(6)
  revisionCreateTime = _messages.StringField(7)
  revisionId = _messages.StringField(8)
  sourceBlueprintRevision = _messages.StringField(9)
  state = _messages.EnumField('StateValueValuesEnum', 10)
  updateTime = _messages.StringField(11)


class DeploymentStatusResponse(_messages.Message):
  r"""Response of deployment status.

  Enums:
    StatusValueValuesEnum: Output only. Aggregated status of a deployment.

  Fields:
    name: Deployment name.
    resourceStatusDetail: Output only. Resource level status details in
      deployments.
    status: Output only. Aggregated status of a deployment.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. Aggregated status of a deployment.

    Values:
      STATUS_UNSPECIFIED: Unknown state.
      STATUS_IN_PROGRESS: Under progress.
      STATUS_ACTIVE: Running and ready to serve traffic.
      STATUS_FAILED: Failed or stalled.
      STATUS_PEERING: NFDeploy specific status.
    """
    STATUS_UNSPECIFIED = 0
    STATUS_IN_PROGRESS = 1
    STATUS_ACTIVE = 2
    STATUS_FAILED = 3
    STATUS_PEERING = 4

  name = _messages.StringField(1)
  resourceStatusDetail = _messages.MessageField('ResourceStatusDetail', 2, repeated=True)
  status = _messages.EnumField('StatusValueValuesEnum', 3)


class DiscardBlueprintChangesRequest(_messages.Message):
  r"""Request object for `DiscardBlueprintChanges`."""


class DiscardBlueprintChangesResponse(_messages.Message):
  r"""Response object for `DiscardBlueprintChanges`."""


class DiscardDeploymentChangesRequest(_messages.Message):
  r"""Request object for `DiscardDeploymentChanges`."""


class DiscardDeploymentChangesResponse(_messages.Message):
  r"""Response object for `DiscardDeploymentChanges`."""


class EdgeSlm(_messages.Message):
  r"""EdgeSlm represents an SLM instance which manages the lifecycle of edge
  components installed on Workload clusters managed by an Orchestration
  Cluster.

  Enums:
    StateValueValuesEnum: Output only. State of the EdgeSlm resource.
    WorkloadClusterTypeValueValuesEnum: Optional. Type of workload cluster for
      which an EdgeSLM resource is created.

  Messages:
    LabelsValue: Optional. Labels as key value pairs. The key and value should
      contain characters which are UTF-8 compliant and less than 50
      characters.

  Fields:
    createTime: Output only. [Output only] Create time stamp.
    labels: Optional. Labels as key value pairs. The key and value should
      contain characters which are UTF-8 compliant and less than 50
      characters.
    name: Name of the EdgeSlm resource.
    orchestrationCluster: Immutable. Reference to the orchestration cluster on
      which templates for this resources will be applied. This should be of
      format projects/{project}/locations/{location}/orchestrationClusters/{or
      chestration_cluster}.
    state: Output only. State of the EdgeSlm resource.
    tnaVersion: Output only. Provides the active TNA version for this
      resource.
    updateTime: Output only. [Output only] Update time stamp.
    workloadClusterType: Optional. Type of workload cluster for which an
      EdgeSLM resource is created.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the EdgeSlm resource.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      CREATING: EdgeSlm is being created.
      ACTIVE: EdgeSlm has been created and is ready for use.
      DELETING: EdgeSlm is being deleted.
      FAILED: EdgeSlm encountered an error and is in an indeterministic state.
        User can still initiate a delete operation on this state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    FAILED = 4

  class WorkloadClusterTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Type of workload cluster for which an EdgeSLM resource is
    created.

    Values:
      WORKLOAD_CLUSTER_TYPE_UNSPECIFIED: Unspecified workload cluster.
      GDCE: Workload cluster is a GDCE cluster.
      GKE: Workload cluster is a GKE cluster.
    """
    WORKLOAD_CLUSTER_TYPE_UNSPECIFIED = 0
    GDCE = 1
    GKE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels as key value pairs. The key and value should contain
    characters which are UTF-8 compliant and less than 50 characters.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  name = _messages.StringField(3)
  orchestrationCluster = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  tnaVersion = _messages.StringField(6)
  updateTime = _messages.StringField(7)
  workloadClusterType = _messages.EnumField('WorkloadClusterTypeValueValuesEnum', 8)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class File(_messages.Message):
  r"""File represents a yaml file present in a blueprint's package.

  Fields:
    content: Optional. The contents of a file in string format.
    deleted: Optional. Signifies whether a file is marked for deletion.
    editable: Optional. Indicates whether changes are allowed to a file. If
      the field is not set, the file cannot be edited.
    path: Required. Path of the file in package. e.g. `gdce/v1/cluster.yaml`
  """

  content = _messages.StringField(1)
  deleted = _messages.BooleanField(2)
  editable = _messages.BooleanField(3)
  path = _messages.StringField(4)


class FullManagementConfig(_messages.Message):
  r"""Configuration of the full (Autopilot) cluster management

  Fields:
    clusterCidrBlock: Optional. The IP address range for the cluster pod IPs.
      Set to blank to have a range chosen with the default size. Set to
      /netmask (e.g. /14) to have a range chosen with a specific netmask. Set
      to a CIDR notation (e.g. *********/14) from the RFC-1918 private
      networks (e.g. 10.0.0.0/8, **********/12, ***********/16) to pick a
      specific range to use.
    clusterNamedRange: Optional. The name of the existing secondary range in
      the cluster's subnetwork to use for pod IP addresses. Alternatively,
      cluster_cidr_block can be used to automatically create a GKE-managed
      one.
    masterAuthorizedNetworksConfig: Optional. Master Authorized Network that
      supports multiple CIDR blocks. Allows access to the k8s master from
      multiple blocks. It cannot be set at the same time with the field
      man_block.
    masterIpv4CidrBlock: Optional. The /28 network that the masters will use.
    network: Optional. Name of the VPC Network to put the GKE cluster and
      nodes in. The VPC will be created if it doesn't exist.
    servicesCidrBlock: Optional. The IP address range for the cluster service
      IPs. Set to blank to have a range chosen with the default size. Set to
      /netmask (e.g. /14) to have a range chosen with a specific netmask. Set
      to a CIDR notation (e.g. *********/14) from the RFC-1918 private
      networks (e.g. 10.0.0.0/8, **********/12, ***********/16) to pick a
      specific range to use.
    servicesNamedRange: Optional. The name of the existing secondary range in
      the cluster's subnetwork to use for service ClusterIPs. Alternatively,
      services_cidr_block can be used to automatically create a GKE-managed
      one.
    subnet: Optional. Specifies the subnet that the interface will be part of.
      Network key must be specified and the subnet must be a subnetwork of the
      specified network.
  """

  clusterCidrBlock = _messages.StringField(1)
  clusterNamedRange = _messages.StringField(2)
  masterAuthorizedNetworksConfig = _messages.MessageField('MasterAuthorizedNetworksConfig', 3)
  masterIpv4CidrBlock = _messages.StringField(4)
  network = _messages.StringField(5)
  servicesCidrBlock = _messages.StringField(6)
  servicesNamedRange = _messages.StringField(7)
  subnet = _messages.StringField(8)


class HydratedDeployment(_messages.Message):
  r"""A collection of kubernetes yaml files which are deployed on a Workload
  Cluster. Hydrated Deployments are created by TNA intent based automation.

  Enums:
    StateValueValuesEnum: Output only. State of the hydrated deployment
      (DRAFT, APPLIED).

  Fields:
    files: Optional. File contents of a hydrated deployment. When invoking
      UpdateHydratedBlueprint API, only the modified files should be included
      in this. Files that are not included in the update of a hydrated
      deployment will not be changed.
    name: Output only. The name of the hydrated deployment.
    state: Output only. State of the hydrated deployment (DRAFT, APPLIED).
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the hydrated deployment (DRAFT, APPLIED).

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      DRAFT: A hydrated deployment starts in DRAFT state. All edits are made
        in DRAFT state.
      APPLIED: When the edit is applied, the hydrated deployment moves to
        APPLIED state. No changes can be made once a hydrated deployment is
        applied.
    """
    STATE_UNSPECIFIED = 0
    DRAFT = 1
    APPLIED = 2

  files = _messages.MessageField('File', 1, repeated=True)
  name = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class ListBlueprintRevisionsResponse(_messages.Message):
  r"""Response object for `ListBlueprintRevisions`.

  Fields:
    blueprints: The revisions of the blueprint.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  blueprints = _messages.MessageField('Blueprint', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListBlueprintsResponse(_messages.Message):
  r"""Response object for `ListBlueprints`.

  Fields:
    blueprints: The list of requested blueprints.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  blueprints = _messages.MessageField('Blueprint', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDeploymentRevisionsResponse(_messages.Message):
  r"""List of deployment revisions for a given deployment.

  Fields:
    deployments: The revisions of the deployment.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  deployments = _messages.MessageField('Deployment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListDeploymentsResponse(_messages.Message):
  r"""Response object for `ListDeployments`.

  Fields:
    deployments: The list of requested deployments.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  deployments = _messages.MessageField('Deployment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListEdgeSlmsResponse(_messages.Message):
  r"""Message for response to listing EdgeSlms.

  Fields:
    edgeSlms: The list of EdgeSlm
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  edgeSlms = _messages.MessageField('EdgeSlm', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListHydratedDeploymentsResponse(_messages.Message):
  r"""Response object for `ListHydratedDeployments`.

  Fields:
    hydratedDeployments: The list of hydrated deployments.
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  hydratedDeployments = _messages.MessageField('HydratedDeployment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListOrchestrationClustersResponse(_messages.Message):
  r"""Message for response to listing OrchestrationClusters.

  Fields:
    nextPageToken: A token identifying a page of results the server should
      return.
    orchestrationClusters: The list of OrchestrationCluster
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  orchestrationClusters = _messages.MessageField('OrchestrationCluster', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListPublicBlueprintsResponse(_messages.Message):
  r"""Message for the response to list all the blueprints from the public
  catalog.

  Fields:
    nextPageToken: Output only. A token identifying a page of results the
      server should return.
    publicBlueprints: The List of blueprints in the public catalog.
  """

  nextPageToken = _messages.StringField(1)
  publicBlueprints = _messages.MessageField('PublicBlueprint', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class ManagementConfig(_messages.Message):
  r"""Configuration of the cluster management

  Fields:
    fullManagementConfig: Configuration of the full (Autopilot) cluster
      management. Full cluster management is a preview feature.
    standardManagementConfig: Configuration of the standard (GKE) cluster
      management
  """

  fullManagementConfig = _messages.MessageField('FullManagementConfig', 1)
  standardManagementConfig = _messages.MessageField('StandardManagementConfig', 2)


class MasterAuthorizedNetworksConfig(_messages.Message):
  r"""Configuration of the Master Authorized Network that support multiple
  CIDRs

  Fields:
    cidrBlocks: Optional. cidr_blocks define up to 50 external networks that
      could access Kubernetes master through HTTPS.
  """

  cidrBlocks = _messages.MessageField('CidrBlock', 1, repeated=True)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have been
      cancelled successfully have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class OrchestrationCluster(_messages.Message):
  r"""Orchestration cluster represents a GKE cluster with config controller
  and TNA specific components installed on it.

  Enums:
    StateValueValuesEnum: Output only. State of the Orchestration Cluster.

  Messages:
    LabelsValue: Labels as key value pairs.

  Fields:
    createTime: Output only. [Output only] Create time stamp.
    labels: Labels as key value pairs.
    managementConfig: Management configuration of the underlying GKE cluster.
    name: Name of the orchestration cluster.
    state: Output only. State of the Orchestration Cluster.
    tnaVersion: Output only. Provides the TNA version installed on the
      cluster.
    updateTime: Output only. [Output only] Update time stamp.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the Orchestration Cluster.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      CREATING: OrchestrationCluster is being created.
      ACTIVE: OrchestrationCluster has been created and is ready for use.
      DELETING: OrchestrationCluster is being deleted.
      FAILED: OrchestrationCluster encountered an error and is in an
        indeterministic state. User can still initiate a delete operation on
        this state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    FAILED = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  managementConfig = _messages.MessageField('ManagementConfig', 3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)
  tnaVersion = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class ProposeBlueprintRequest(_messages.Message):
  r"""Request object for `ProposeBlueprint`."""


class PublicBlueprint(_messages.Message):
  r"""Message for blueprint from the public catalog.

  Enums:
    DeploymentLevelValueValuesEnum: DeploymentLevel of a blueprint signifies
      where the blueprint will be applied. e.g. [HYDRATION, DEPLOYMENT]

  Fields:
    branch: Branch in the repo where the blueprint is available.
    commitId: A commit ID which can be used as a version identifier.
    deploymentLevel: DeploymentLevel of a blueprint signifies where the
      blueprint will be applied. e.g. [HYDRATION, DEPLOYMENT]
    description: The description of the public blueprint.
    displayName: The name which will be displayed in the UI.
    id: Unique ID for this blueprint.
    path: Directory in the repo where the blueprint is available.
    repo: The repository of the blueprint.
  """

  class DeploymentLevelValueValuesEnum(_messages.Enum):
    r"""DeploymentLevel of a blueprint signifies where the blueprint will be
    applied. e.g. [HYDRATION, DEPLOYMENT]

    Values:
      DEPLOYMENT_LEVEL_UNSPECIFIED: Default unspecified deployment level.
      HYDRATION: Blueprints at HYDRATION level cannot be used to create a
        Deployment (A user cannot manually initate deployment of these
        blueprints on orchestration or workload cluster). These blueprints
        stay in a user's private catalog and are configured and deployed by
        TNA automation.
      DEPLOYMENT: Blueprints at DEPLOYMENT level can be a) Modified in private
        catalog. b) Used to create a deployment on orchestration cluster by
        the user, once approved.
    """
    DEPLOYMENT_LEVEL_UNSPECIFIED = 0
    HYDRATION = 1
    DEPLOYMENT = 2

  branch = _messages.StringField(1)
  commitId = _messages.StringField(2)
  deploymentLevel = _messages.EnumField('DeploymentLevelValueValuesEnum', 3)
  description = _messages.StringField(4)
  displayName = _messages.StringField(5)
  id = _messages.StringField(6)
  path = _messages.StringField(7)
  repo = _messages.StringField(8)


class RejectBlueprintRequest(_messages.Message):
  r"""Request object for `RejectBlueprint`."""


class ResourceStatusDetail(_messages.Message):
  r"""Status of a deployment resource.

  Enums:
    ResourceTypeValueValuesEnum: Represent type of CR.
    StatusValueValuesEnum: Output only. Status of a resource.

  Fields:
    resourceType: Represent type of CR.
    status: Output only. Status of a resource.
  """

  class ResourceTypeValueValuesEnum(_messages.Enum):
    r"""Represent type of CR.

    Values:
      RESOURCE_TYPE_UNSPECIFIED: Unspecified resource type.
      NF_DEPLOY_CUSTOM_RESOURCE: User specified NF Deploy CR.
      BLUEPRINT_CUSTOM_RESOURCE: CRs that are part of a blueprint.
    """
    RESOURCE_TYPE_UNSPECIFIED = 0
    NF_DEPLOY_CUSTOM_RESOURCE = 1
    BLUEPRINT_CUSTOM_RESOURCE = 2

  class StatusValueValuesEnum(_messages.Enum):
    r"""Output only. Status of a resource.

    Values:
      STATUS_UNSPECIFIED: Unknown state.
      STATUS_IN_PROGRESS: Under progress.
      STATUS_ACTIVE: Running and ready to serve traffic.
      STATUS_FAILED: Failed or stalled.
      STATUS_PEERING: NFDeploy specific status.
    """
    STATUS_UNSPECIFIED = 0
    STATUS_IN_PROGRESS = 1
    STATUS_ACTIVE = 2
    STATUS_FAILED = 3
    STATUS_PEERING = 4

  resourceType = _messages.EnumField('ResourceTypeValueValuesEnum', 1)
  status = _messages.EnumField('StatusValueValuesEnum', 2)


class StandardManagementConfig(_messages.Message):
  r"""Configuration of the standard (GKE) cluster management.

  Fields:
    clusterCidrBlock: Optional. The IP address range for the cluster pod IPs.
      Set to blank to have a range chosen with the default size. Set to
      /netmask (e.g. /14) to have a range chosen with a specific netmask. Set
      to a CIDR notation (e.g. *********/14) from the RFC-1918 private
      networks (e.g. 10.0.0.0/8, **********/12, ***********/16) to pick a
      specific range to use.
    clusterNamedRange: Optional. The name of the existing secondary range in
      the cluster's subnetwork to use for pod IP addresses. Alternatively,
      cluster_cidr_block can be used to automatically create a GKE-managed
      one.
    masterAuthorizedNetworksConfig: Optional. Master Authorized Network that
      supports multiple CIDR blocks. Allows access to the k8s master from
      multiple blocks. It cannot be set at the same time with the field
      man_block.
    masterIpv4CidrBlock: Optional. The /28 network that the masters will use.
    network: Optional. Name of the VPC Network to put the GKE cluster and
      nodes in. The VPC will be created if it doesn't exist.
    servicesCidrBlock: Optional. The IP address range for the cluster service
      IPs. Set to blank to have a range chosen with the default size. Set to
      /netmask (e.g. /14) to have a range chosen with a specific netmask. Set
      to a CIDR notation (e.g. *********/14) from the RFC-1918 private
      networks (e.g. 10.0.0.0/8, **********/12, ***********/16) to pick a
      specific range to use.
    servicesNamedRange: Optional. The name of the existing secondary range in
      the cluster's subnetwork to use for service ClusterIPs. Alternatively,
      services_cidr_block can be used to automatically create a GKE-managed
      one.
    subnet: Optional. Specifies the subnet that the interface will be part of.
      Network key must be specified and the subnet must be a subnetwork of the
      specified network.
  """

  clusterCidrBlock = _messages.StringField(1)
  clusterNamedRange = _messages.StringField(2)
  masterAuthorizedNetworksConfig = _messages.MessageField('MasterAuthorizedNetworksConfig', 3)
  masterIpv4CidrBlock = _messages.StringField(4)
  network = _messages.StringField(5)
  servicesCidrBlock = _messages.StringField(6)
  servicesNamedRange = _messages.StringField(7)
  subnet = _messages.StringField(8)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TelcoautomationProjectsLocationsEdgeSlmsCreateRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsEdgeSlmsCreateRequest object.

  Fields:
    edgeSlm: A EdgeSlm resource to be passed as the request body.
    edgeSlmId: Required. Id of the requesting object If auto-generating Id
      server-side, remove this field and edge_slm_id from the method_signature
      of Create RPC
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  edgeSlm = _messages.MessageField('EdgeSlm', 1)
  edgeSlmId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class TelcoautomationProjectsLocationsEdgeSlmsDeleteRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsEdgeSlmsDeleteRequest object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class TelcoautomationProjectsLocationsEdgeSlmsGetRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsEdgeSlmsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class TelcoautomationProjectsLocationsEdgeSlmsListRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsEdgeSlmsListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListEdgeSlmsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class TelcoautomationProjectsLocationsEdgeSlmsPatchRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsEdgeSlmsPatchRequest object.

  Fields:
    edgeSlm: A EdgeSlm resource to be passed as the request body.
    name: Name of the EdgeSlm resource.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the EdgeSlm resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  edgeSlm = _messages.MessageField('EdgeSlm', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class TelcoautomationProjectsLocationsGetRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class TelcoautomationProjectsLocationsListRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class TelcoautomationProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class TelcoautomationProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class TelcoautomationProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class TelcoautomationProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsApproveRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsApprove
  Request object.

  Fields:
    approveBlueprintRequest: A ApproveBlueprintRequest resource to be passed
      as the request body.
    name: Required. The name of the blueprint to approve. The blueprint must
      be in Proposed state. A new revision is committed on approval.
  """

  approveBlueprintRequest = _messages.MessageField('ApproveBlueprintRequest', 1)
  name = _messages.StringField(2, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsCreateRequest(_messages.Message):
  r"""A
  TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsCreateRequest
  object.

  Fields:
    blueprint: A Blueprint resource to be passed as the request body.
    blueprintId: Optional. The name of the blueprint.
    parent: Required. The name of parent resource. Format should be - "project
      s/{project_id}/locations/{location_name}/orchestrationClusters/{orchestr
      ation_cluster}".
  """

  blueprint = _messages.MessageField('Blueprint', 1)
  blueprintId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsDeleteRequest(_messages.Message):
  r"""A
  TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsDeleteRequest
  object.

  Fields:
    name: Required. The name of blueprint to delete.
  """

  name = _messages.StringField(1, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsDeleteRevisionRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsDeleteR
  evisionRequest object.

  Fields:
    name: Required. The name of the blueprint revision in the form
      {blueprint_id}@{revision_id}.
  """

  name = _messages.StringField(1, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsDiscardRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsDiscard
  Request object.

  Fields:
    discardBlueprintChangesRequest: A DiscardBlueprintChangesRequest resource
      to be passed as the request body.
    name: Required. The name of the blueprint of which changes are being
      discarded.
  """

  discardBlueprintChangesRequest = _messages.MessageField('DiscardBlueprintChangesRequest', 1)
  name = _messages.StringField(2, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsGetRequest(_messages.Message):
  r"""A
  TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsGetRequest
  object.

  Enums:
    ViewValueValuesEnum: Optional. Defines the type of view of the blueprint.
      When field is not present BLUEPRINT_VIEW_BASIC is considered as default.

  Fields:
    name: Required. The name of the blueprint. Case 1: If the name provided in
      the request is {blueprint_id}@{revision_id}, then the revision with
      revision_id will be returned. Case 2: If the name provided in the
      request is {blueprint}, then the current state of the blueprint is
      returned.
    view: Optional. Defines the type of view of the blueprint. When field is
      not present BLUEPRINT_VIEW_BASIC is considered as default.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Optional. Defines the type of view of the blueprint. When field is not
    present BLUEPRINT_VIEW_BASIC is considered as default.

    Values:
      BLUEPRINT_VIEW_UNSPECIFIED: Unspecified enum value.
      BLUEPRINT_VIEW_BASIC: Blueprint view which only contains metadata.
      BLUEPRINT_VIEW_FULL: Blueprint view which contains metadata and files it
        encapsulates.
    """
    BLUEPRINT_VIEW_UNSPECIFIED = 0
    BLUEPRINT_VIEW_BASIC = 1
    BLUEPRINT_VIEW_FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsListRequest(_messages.Message):
  r"""A
  TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsListRequest
  object.

  Fields:
    filter: Optional. Filtering only supports equality on blueprint state. It
      should be in the form: "state = DRAFT". `OR` operator can be used to get
      response for multiple states. e.g. "state = DRAFT OR state = PROPOSED".
    pageSize: Optional. The maximum number of blueprints to return per page.
    pageToken: Optional. The page token, received from a previous
      ListBlueprints call. It can be provided to retrieve the subsequent page.
    parent: Required. The name of parent orchestration cluster resource.
      Format should be - "projects/{project_id}/locations/{location_name}/orch
      estrationClusters/{orchestration_cluster}".
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsListRevisionsRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsListRev
  isionsRequest object.

  Fields:
    name: Required. The name of the blueprint to list revisions for.
    pageSize: The maximum number of revisions to return per page.
    pageToken: The page token, received from a previous ListBlueprintRevisions
      call It can be provided to retrieve the subsequent page.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsPatchRequest(_messages.Message):
  r"""A
  TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsPatchRequest
  object.

  Fields:
    blueprint: A Blueprint resource to be passed as the request body.
    name: The name of the blueprint. If unspecified, the name will be
      autogenerated from server side. Name of the blueprint must not contain
      `@` character.
    updateMask: Required. Update mask is used to specify the fields to be
      overwritten in the `blueprint` resource by the update.
  """

  blueprint = _messages.MessageField('Blueprint', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsProposeRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsPropose
  Request object.

  Fields:
    name: Required. The name of the blueprint being proposed.
    proposeBlueprintRequest: A ProposeBlueprintRequest resource to be passed
      as the request body.
  """

  name = _messages.StringField(1, required=True)
  proposeBlueprintRequest = _messages.MessageField('ProposeBlueprintRequest', 2)


class TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsRejectRequest(_messages.Message):
  r"""A
  TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsRejectRequest
  object.

  Fields:
    name: Required. The name of the blueprint being rejected.
    rejectBlueprintRequest: A RejectBlueprintRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  rejectBlueprintRequest = _messages.MessageField('RejectBlueprintRequest', 2)


class TelcoautomationProjectsLocationsOrchestrationClustersCreateRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersCreateRequest
  object.

  Fields:
    orchestrationCluster: A OrchestrationCluster resource to be passed as the
      request body.
    orchestrationClusterId: Required. Id of the requesting object If auto-
      generating Id server-side, remove this field and
      orchestration_cluster_id from the method_signature of Create RPC
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  orchestrationCluster = _messages.MessageField('OrchestrationCluster', 1)
  orchestrationClusterId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class TelcoautomationProjectsLocationsOrchestrationClustersDeleteRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsApplyRequest(_messages.Message):
  r"""A
  TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsApplyRequest
  object.

  Fields:
    applyDeploymentRequest: A ApplyDeploymentRequest resource to be passed as
      the request body.
    name: Required. The name of the deployment to apply to orchestration
      cluster.
  """

  applyDeploymentRequest = _messages.MessageField('ApplyDeploymentRequest', 1)
  name = _messages.StringField(2, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsComputeDeploymentStatusRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsComput
  eDeploymentStatusRequest object.

  Fields:
    name: Required. The unique deployment name across orchestration cluster.
  """

  name = _messages.StringField(1, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsCreateRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsCreate
  Request object.

  Fields:
    deployment: A Deployment resource to be passed as the request body.
    deploymentId: Optional. The name of the deployment.
    parent: Required. The name of parent resource. Format should be - "project
      s/{project_id}/locations/{location_name}/orchestrationClusters/{orchestr
      ation_cluster}".
  """

  deployment = _messages.MessageField('Deployment', 1)
  deploymentId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsDeleteRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsDelete
  Request object.

  Fields:
    name: Required. The name of deployment to delete.
  """

  name = _messages.StringField(1, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsDeleteRevisionRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsDelete
  RevisionRequest object.

  Fields:
    name: Required. The name of the deployment revision in the form
      {deployment_id}@{revision_id}.
  """

  name = _messages.StringField(1, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsDiscardRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsDiscar
  dRequest object.

  Fields:
    discardDeploymentChangesRequest: A DiscardDeploymentChangesRequest
      resource to be passed as the request body.
    name: Required. The name of the deployment of which changes are being
      discarded.
  """

  discardDeploymentChangesRequest = _messages.MessageField('DiscardDeploymentChangesRequest', 1)
  name = _messages.StringField(2, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsGetRequest(_messages.Message):
  r"""A
  TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsGetRequest
  object.

  Fields:
    name: Required. The name of the deployment. Case 1: If the name provided
      in the request is {deployment_id}@{revision_id}, then the revision with
      revision_id will be returned. Case 2: If the name provided in the
      request is {deployment}, then the current state of the deployment is
      returned.
  """

  name = _messages.StringField(1, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsHydratedDeploymentsApplyRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsHydrat
  edDeploymentsApplyRequest object.

  Fields:
    applyHydratedDeploymentRequest: A ApplyHydratedDeploymentRequest resource
      to be passed as the request body.
    name: Required. The name of the hydrated deployment to apply.
  """

  applyHydratedDeploymentRequest = _messages.MessageField('ApplyHydratedDeploymentRequest', 1)
  name = _messages.StringField(2, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsHydratedDeploymentsGetRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsHydrat
  edDeploymentsGetRequest object.

  Fields:
    name: Required. Name of the hydrated deployment.
  """

  name = _messages.StringField(1, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsHydratedDeploymentsListRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsHydrat
  edDeploymentsListRequest object.

  Fields:
    pageSize: Optional. The maximum number of hydrated deployments to return.
      The service may return fewer than this value. If unspecified, at most 50
      hydrated deployments will be returned. The maximum value is 1000. Values
      above 1000 will be set to 1000.
    pageToken: Optional. The page token, received from a previous
      ListHydratedDeployments call. Provide this to retrieve the subsequent
      page.
    parent: Required. The deployment managing the hydrated deployments.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsHydratedDeploymentsPatchRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsHydrat
  edDeploymentsPatchRequest object.

  Fields:
    hydratedDeployment: A HydratedDeployment resource to be passed as the
      request body.
    name: Output only. The name of the hydrated deployment.
    updateMask: Required. The list of fields to update. Update mask supports a
      special value `*` which fully replaces (equivalent to PUT) the resource
      provided.
  """

  hydratedDeployment = _messages.MessageField('HydratedDeployment', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsListRequest(_messages.Message):
  r"""A
  TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsListRequest
  object.

  Fields:
    filter: Optional. Filtering only supports equality on deployment state. It
      should be in the form: "state = DRAFT". `OR` operator can be used to get
      response for multiple states. e.g. "state = DRAFT OR state = APPLIED".
    pageSize: Optional. The maximum number of deployments to return per page.
    pageToken: Optional. The page token, received from a previous
      ListDeployments call. It can be provided to retrieve the subsequent
      page.
    parent: Required. The name of parent orchestration cluster resource.
      Format should be - "projects/{project_id}/locations/{location_name}/orch
      estrationClusters/{orchestration_cluster}".
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsListRevisionsRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsListRe
  visionsRequest object.

  Fields:
    name: Required. The name of the deployment to list revisions for.
    pageSize: Optional. The maximum number of revisions to return per page.
    pageToken: Optional. The page token, received from a previous
      ListDeploymentRevisions call Provide this to retrieve the subsequent
      page.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsPatchRequest(_messages.Message):
  r"""A
  TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsPatchRequest
  object.

  Fields:
    deployment: A Deployment resource to be passed as the request body.
    name: The name of the deployment.
    updateMask: Required. Update mask is used to specify the fields to be
      overwritten in the `deployment` resource by the update.
  """

  deployment = _messages.MessageField('Deployment', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class TelcoautomationProjectsLocationsOrchestrationClustersGetRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersGetRequest
  object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersListRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersListRequest
  object.

  Fields:
    filter: Filtering results.
    orderBy: Hint for how to order the results.
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListOrchestrationClustersRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class TelcoautomationProjectsLocationsOrchestrationClustersPatchRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsOrchestrationClustersPatchRequest
  object.

  Fields:
    name: Name of the orchestration cluster.
    orchestrationCluster: A OrchestrationCluster resource to be passed as the
      request body.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the OrchestrationCluster resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  orchestrationCluster = _messages.MessageField('OrchestrationCluster', 2)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class TelcoautomationProjectsLocationsPublicBlueprintsListRequest(_messages.Message):
  r"""A TelcoautomationProjectsLocationsPublicBlueprintsListRequest object.

  Fields:
    pageSize: Optional. Requested page size. Server may return fewer items
      than requested. If unspecified, server will pick an appropriate default.
    pageToken: Optional. A token identifying a page of results the server
      should return.
    parent: Required. Parent value for ListPublicBlueprintsRequest
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

"""Generated client library for telcoautomation version v1alpha1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.telcoautomation.v1alpha1 import telcoautomation_v1alpha1_messages as messages


class TelcoautomationV1alpha1(base_api.BaseApiClient):
  """Generated client library for service telcoautomation version v1alpha1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://telcoautomation.googleapis.com/'
  MTLS_BASE_URL = 'https://telcoautomation.mtls.googleapis.com/'

  _PACKAGE = 'telcoautomation'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'TelcoautomationV1alpha1'
  _URL_VERSION = 'v1alpha1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new telcoautomation handle."""
    url = url or self.BASE_URL
    super(TelcoautomationV1alpha1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_edgeSlms = self.ProjectsLocationsEdgeSlmsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_orchestrationClusters_blueprints = self.ProjectsLocationsOrchestrationClustersBlueprintsService(self)
    self.projects_locations_orchestrationClusters_deployments_hydratedDeployments = self.ProjectsLocationsOrchestrationClustersDeploymentsHydratedDeploymentsService(self)
    self.projects_locations_orchestrationClusters_deployments = self.ProjectsLocationsOrchestrationClustersDeploymentsService(self)
    self.projects_locations_orchestrationClusters = self.ProjectsLocationsOrchestrationClustersService(self)
    self.projects_locations_publicBlueprints = self.ProjectsLocationsPublicBlueprintsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsEdgeSlmsService(base_api.BaseApiService):
    """Service class for the projects_locations_edgeSlms resource."""

    _NAME = 'projects_locations_edgeSlms'

    def __init__(self, client):
      super(TelcoautomationV1alpha1.ProjectsLocationsEdgeSlmsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new EdgeSlm in a given project and location.

      Args:
        request: (TelcoautomationProjectsLocationsEdgeSlmsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeSlms',
        http_method='POST',
        method_id='telcoautomation.projects.locations.edgeSlms.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['edgeSlmId', 'requestId'],
        relative_path='v1alpha1/{+parent}/edgeSlms',
        request_field='edgeSlm',
        request_type_name='TelcoautomationProjectsLocationsEdgeSlmsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single EdgeSlm.

      Args:
        request: (TelcoautomationProjectsLocationsEdgeSlmsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeSlms/{edgeSlmsId}',
        http_method='DELETE',
        method_id='telcoautomation.projects.locations.edgeSlms.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsEdgeSlmsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single EdgeSlm.

      Args:
        request: (TelcoautomationProjectsLocationsEdgeSlmsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EdgeSlm) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeSlms/{edgeSlmsId}',
        http_method='GET',
        method_id='telcoautomation.projects.locations.edgeSlms.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsEdgeSlmsGetRequest',
        response_type_name='EdgeSlm',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists EdgeSlms in a given project and location.

      Args:
        request: (TelcoautomationProjectsLocationsEdgeSlmsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEdgeSlmsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeSlms',
        http_method='GET',
        method_id='telcoautomation.projects.locations.edgeSlms.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/edgeSlms',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsEdgeSlmsListRequest',
        response_type_name='ListEdgeSlmsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single EdgeSlm.

      Args:
        request: (TelcoautomationProjectsLocationsEdgeSlmsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeSlms/{edgeSlmsId}',
        http_method='PATCH',
        method_id='telcoautomation.projects.locations.edgeSlms.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='edgeSlm',
        request_type_name='TelcoautomationProjectsLocationsEdgeSlmsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(TelcoautomationV1alpha1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (TelcoautomationProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='telcoautomation.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='TelcoautomationProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (TelcoautomationProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='telcoautomation.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (TelcoautomationProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='telcoautomation.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (TelcoautomationProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='telcoautomation.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOrchestrationClustersBlueprintsService(base_api.BaseApiService):
    """Service class for the projects_locations_orchestrationClusters_blueprints resource."""

    _NAME = 'projects_locations_orchestrationClusters_blueprints'

    def __init__(self, client):
      super(TelcoautomationV1alpha1.ProjectsLocationsOrchestrationClustersBlueprintsService, self).__init__(client)
      self._upload_configs = {
          }

    def Approve(self, request, global_params=None):
      r"""Approves a blueprint and commits a new revision.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsApproveRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Blueprint) The response message.
      """
      config = self.GetMethodConfig('Approve')
      return self._RunMethod(
          config, request, global_params=global_params)

    Approve.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/blueprints/{blueprintsId}:approve',
        http_method='POST',
        method_id='telcoautomation.projects.locations.orchestrationClusters.blueprints.approve',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:approve',
        request_field='approveBlueprintRequest',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsApproveRequest',
        response_type_name='Blueprint',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a blueprint.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Blueprint) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/blueprints',
        http_method='POST',
        method_id='telcoautomation.projects.locations.orchestrationClusters.blueprints.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['blueprintId'],
        relative_path='v1alpha1/{+parent}/blueprints',
        request_field='blueprint',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsCreateRequest',
        response_type_name='Blueprint',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a blueprint and all its revisions.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/blueprints/{blueprintsId}',
        http_method='DELETE',
        method_id='telcoautomation.projects.locations.orchestrationClusters.blueprints.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def DeleteRevision(self, request, global_params=None):
      r"""Deletes the specified revision of the blueprint.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsDeleteRevisionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Blueprint) The response message.
      """
      config = self.GetMethodConfig('DeleteRevision')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteRevision.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/blueprints/{blueprintsId}:deleteRevision',
        http_method='DELETE',
        method_id='telcoautomation.projects.locations.orchestrationClusters.blueprints.deleteRevision',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:deleteRevision',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsDeleteRevisionRequest',
        response_type_name='Blueprint',
        supports_download=False,
    )

    def Discard(self, request, global_params=None):
      r"""Discards the changes in a blueprint and reverts the blueprint to the last approved blueprint revision. No changes take place if a blueprint does not have revisions.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsDiscardRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DiscardBlueprintChangesResponse) The response message.
      """
      config = self.GetMethodConfig('Discard')
      return self._RunMethod(
          config, request, global_params=global_params)

    Discard.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/blueprints/{blueprintsId}:discard',
        http_method='POST',
        method_id='telcoautomation.projects.locations.orchestrationClusters.blueprints.discard',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:discard',
        request_field='discardBlueprintChangesRequest',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsDiscardRequest',
        response_type_name='DiscardBlueprintChangesResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the requested blueprint.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Blueprint) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/blueprints/{blueprintsId}',
        http_method='GET',
        method_id='telcoautomation.projects.locations.orchestrationClusters.blueprints.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsGetRequest',
        response_type_name='Blueprint',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all blueprints.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBlueprintsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/blueprints',
        http_method='GET',
        method_id='telcoautomation.projects.locations.orchestrationClusters.blueprints.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/blueprints',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsListRequest',
        response_type_name='ListBlueprintsResponse',
        supports_download=False,
    )

    def ListRevisions(self, request, global_params=None):
      r"""List blueprint revisions of a given blueprint.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsListRevisionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListBlueprintRevisionsResponse) The response message.
      """
      config = self.GetMethodConfig('ListRevisions')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListRevisions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/blueprints/{blueprintsId}:listRevisions',
        http_method='GET',
        method_id='telcoautomation.projects.locations.orchestrationClusters.blueprints.listRevisions',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}:listRevisions',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsListRevisionsRequest',
        response_type_name='ListBlueprintRevisionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a blueprint.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Blueprint) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/blueprints/{blueprintsId}',
        http_method='PATCH',
        method_id='telcoautomation.projects.locations.orchestrationClusters.blueprints.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='blueprint',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsPatchRequest',
        response_type_name='Blueprint',
        supports_download=False,
    )

    def Propose(self, request, global_params=None):
      r"""Proposes a blueprint for approval of changes.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsProposeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Blueprint) The response message.
      """
      config = self.GetMethodConfig('Propose')
      return self._RunMethod(
          config, request, global_params=global_params)

    Propose.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/blueprints/{blueprintsId}:propose',
        http_method='POST',
        method_id='telcoautomation.projects.locations.orchestrationClusters.blueprints.propose',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:propose',
        request_field='proposeBlueprintRequest',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsProposeRequest',
        response_type_name='Blueprint',
        supports_download=False,
    )

    def Reject(self, request, global_params=None):
      r"""Rejects a blueprint revision proposal and flips it back to Draft state.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsRejectRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Blueprint) The response message.
      """
      config = self.GetMethodConfig('Reject')
      return self._RunMethod(
          config, request, global_params=global_params)

    Reject.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/blueprints/{blueprintsId}:reject',
        http_method='POST',
        method_id='telcoautomation.projects.locations.orchestrationClusters.blueprints.reject',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:reject',
        request_field='rejectBlueprintRequest',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersBlueprintsRejectRequest',
        response_type_name='Blueprint',
        supports_download=False,
    )

  class ProjectsLocationsOrchestrationClustersDeploymentsHydratedDeploymentsService(base_api.BaseApiService):
    """Service class for the projects_locations_orchestrationClusters_deployments_hydratedDeployments resource."""

    _NAME = 'projects_locations_orchestrationClusters_deployments_hydratedDeployments'

    def __init__(self, client):
      super(TelcoautomationV1alpha1.ProjectsLocationsOrchestrationClustersDeploymentsHydratedDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Apply(self, request, global_params=None):
      r"""Applies a hydrated deployment to a workload cluster.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsHydratedDeploymentsApplyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HydratedDeployment) The response message.
      """
      config = self.GetMethodConfig('Apply')
      return self._RunMethod(
          config, request, global_params=global_params)

    Apply.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/deployments/{deploymentsId}/hydratedDeployments/{hydratedDeploymentsId}:apply',
        http_method='POST',
        method_id='telcoautomation.projects.locations.orchestrationClusters.deployments.hydratedDeployments.apply',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:apply',
        request_field='applyHydratedDeploymentRequest',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsHydratedDeploymentsApplyRequest',
        response_type_name='HydratedDeployment',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the requested hydrated deployment.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsHydratedDeploymentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HydratedDeployment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/deployments/{deploymentsId}/hydratedDeployments/{hydratedDeploymentsId}',
        http_method='GET',
        method_id='telcoautomation.projects.locations.orchestrationClusters.deployments.hydratedDeployments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsHydratedDeploymentsGetRequest',
        response_type_name='HydratedDeployment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all hydrated deployments present under a deployment.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsHydratedDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListHydratedDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/deployments/{deploymentsId}/hydratedDeployments',
        http_method='GET',
        method_id='telcoautomation.projects.locations.orchestrationClusters.deployments.hydratedDeployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/hydratedDeployments',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsHydratedDeploymentsListRequest',
        response_type_name='ListHydratedDeploymentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a hydrated deployment.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsHydratedDeploymentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HydratedDeployment) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/deployments/{deploymentsId}/hydratedDeployments/{hydratedDeploymentsId}',
        http_method='PATCH',
        method_id='telcoautomation.projects.locations.orchestrationClusters.deployments.hydratedDeployments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='hydratedDeployment',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsHydratedDeploymentsPatchRequest',
        response_type_name='HydratedDeployment',
        supports_download=False,
    )

  class ProjectsLocationsOrchestrationClustersDeploymentsService(base_api.BaseApiService):
    """Service class for the projects_locations_orchestrationClusters_deployments resource."""

    _NAME = 'projects_locations_orchestrationClusters_deployments'

    def __init__(self, client):
      super(TelcoautomationV1alpha1.ProjectsLocationsOrchestrationClustersDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Apply(self, request, global_params=None):
      r"""Applies the deployment's YAML files to the parent orchestration cluster.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsApplyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Deployment) The response message.
      """
      config = self.GetMethodConfig('Apply')
      return self._RunMethod(
          config, request, global_params=global_params)

    Apply.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/deployments/{deploymentsId}:apply',
        http_method='POST',
        method_id='telcoautomation.projects.locations.orchestrationClusters.deployments.apply',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:apply',
        request_field='applyDeploymentRequest',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsApplyRequest',
        response_type_name='Deployment',
        supports_download=False,
    )

    def ComputeDeploymentStatus(self, request, global_params=None):
      r"""Returns the requested deployment status.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsComputeDeploymentStatusRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DeploymentStatusResponse) The response message.
      """
      config = self.GetMethodConfig('ComputeDeploymentStatus')
      return self._RunMethod(
          config, request, global_params=global_params)

    ComputeDeploymentStatus.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/deployments/{deploymentsId}:computeDeploymentStatus',
        http_method='GET',
        method_id='telcoautomation.projects.locations.orchestrationClusters.deployments.computeDeploymentStatus',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:computeDeploymentStatus',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsComputeDeploymentStatusRequest',
        response_type_name='DeploymentStatusResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a deployment.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Deployment) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/deployments',
        http_method='POST',
        method_id='telcoautomation.projects.locations.orchestrationClusters.deployments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['deploymentId'],
        relative_path='v1alpha1/{+parent}/deployments',
        request_field='deployment',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsCreateRequest',
        response_type_name='Deployment',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a deployment and all its revisions.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/deployments/{deploymentsId}',
        http_method='DELETE',
        method_id='telcoautomation.projects.locations.orchestrationClusters.deployments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def DeleteRevision(self, request, global_params=None):
      r"""Deletes the specified revision of the deployment.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsDeleteRevisionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Deployment) The response message.
      """
      config = self.GetMethodConfig('DeleteRevision')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteRevision.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/deployments/{deploymentsId}:deleteRevision',
        http_method='DELETE',
        method_id='telcoautomation.projects.locations.orchestrationClusters.deployments.deleteRevision',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:deleteRevision',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsDeleteRevisionRequest',
        response_type_name='Deployment',
        supports_download=False,
    )

    def Discard(self, request, global_params=None):
      r"""Discards the changes in a deployment and reverts the deployment to the last approved deployment revision. No changes take place if a deployment does not have revisions.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsDiscardRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (DiscardDeploymentChangesResponse) The response message.
      """
      config = self.GetMethodConfig('Discard')
      return self._RunMethod(
          config, request, global_params=global_params)

    Discard.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/deployments/{deploymentsId}:discard',
        http_method='POST',
        method_id='telcoautomation.projects.locations.orchestrationClusters.deployments.discard',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:discard',
        request_field='discardDeploymentChangesRequest',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsDiscardRequest',
        response_type_name='DiscardDeploymentChangesResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the requested deployment.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Deployment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/deployments/{deploymentsId}',
        http_method='GET',
        method_id='telcoautomation.projects.locations.orchestrationClusters.deployments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsGetRequest',
        response_type_name='Deployment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List all deployments.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/deployments',
        http_method='GET',
        method_id='telcoautomation.projects.locations.orchestrationClusters.deployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/deployments',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsListRequest',
        response_type_name='ListDeploymentsResponse',
        supports_download=False,
    )

    def ListRevisions(self, request, global_params=None):
      r"""List deployment revisions of a given deployment.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsListRevisionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListDeploymentRevisionsResponse) The response message.
      """
      config = self.GetMethodConfig('ListRevisions')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListRevisions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/deployments/{deploymentsId}:listRevisions',
        http_method='GET',
        method_id='telcoautomation.projects.locations.orchestrationClusters.deployments.listRevisions',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}:listRevisions',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsListRevisionsRequest',
        response_type_name='ListDeploymentRevisionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a deployment.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Deployment) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}/deployments/{deploymentsId}',
        http_method='PATCH',
        method_id='telcoautomation.projects.locations.orchestrationClusters.deployments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='deployment',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersDeploymentsPatchRequest',
        response_type_name='Deployment',
        supports_download=False,
    )

  class ProjectsLocationsOrchestrationClustersService(base_api.BaseApiService):
    """Service class for the projects_locations_orchestrationClusters resource."""

    _NAME = 'projects_locations_orchestrationClusters'

    def __init__(self, client):
      super(TelcoautomationV1alpha1.ProjectsLocationsOrchestrationClustersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new OrchestrationCluster in a given project and location.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters',
        http_method='POST',
        method_id='telcoautomation.projects.locations.orchestrationClusters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['orchestrationClusterId', 'requestId'],
        relative_path='v1alpha1/{+parent}/orchestrationClusters',
        request_field='orchestrationCluster',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single OrchestrationCluster.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}',
        http_method='DELETE',
        method_id='telcoautomation.projects.locations.orchestrationClusters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single OrchestrationCluster.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (OrchestrationCluster) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}',
        http_method='GET',
        method_id='telcoautomation.projects.locations.orchestrationClusters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersGetRequest',
        response_type_name='OrchestrationCluster',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists OrchestrationClusters in a given project and location.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOrchestrationClustersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters',
        http_method='GET',
        method_id='telcoautomation.projects.locations.orchestrationClusters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/orchestrationClusters',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersListRequest',
        response_type_name='ListOrchestrationClustersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single OrchestrationCluster.

      Args:
        request: (TelcoautomationProjectsLocationsOrchestrationClustersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/orchestrationClusters/{orchestrationClustersId}',
        http_method='PATCH',
        method_id='telcoautomation.projects.locations.orchestrationClusters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='orchestrationCluster',
        request_type_name='TelcoautomationProjectsLocationsOrchestrationClustersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsPublicBlueprintsService(base_api.BaseApiService):
    """Service class for the projects_locations_publicBlueprints resource."""

    _NAME = 'projects_locations_publicBlueprints'

    def __init__(self, client):
      super(TelcoautomationV1alpha1.ProjectsLocationsPublicBlueprintsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists the blueprints in TNA's public catalog. Default page size = 20, Max Page Size = 100.

      Args:
        request: (TelcoautomationProjectsLocationsPublicBlueprintsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListPublicBlueprintsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/publicBlueprints',
        http_method='GET',
        method_id='telcoautomation.projects.locations.publicBlueprints.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/publicBlueprints',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsPublicBlueprintsListRequest',
        response_type_name='ListPublicBlueprintsResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(TelcoautomationV1alpha1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (TelcoautomationProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='telcoautomation.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (TelcoautomationProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='telcoautomation.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/locations',
        request_field='',
        request_type_name='TelcoautomationProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(TelcoautomationV1alpha1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

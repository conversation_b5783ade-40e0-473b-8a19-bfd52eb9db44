"""Generated message classes for datastream version v1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'datastream'


class AvroFileFormat(_messages.Message):
  r"""AVRO file format configuration."""


class BackfillAllStrategy(_messages.Message):
  r"""Backfill strategy to automatically backfill the Stream's objects.
  Specific objects can be excluded.

  Fields:
    mysqlExcludedObjects: MySQL data source objects to avoid backfilling.
    oracleExcludedObjects: Oracle data source objects to avoid backfilling.
    postgresqlExcludedObjects: PostgreSQL data source objects to avoid
      backfilling.
  """

  mysqlExcludedObjects = _messages.MessageField('MysqlRdbms', 1)
  oracleExcludedObjects = _messages.MessageField('OracleRdbms', 2)
  postgresqlExcludedObjects = _messages.MessageField('PostgresqlRdbms', 3)


class BackfillJob(_messages.Message):
  r"""Represents a backfill job on a specific stream object.

  Enums:
    StateValueValuesEnum: Output only. Backfill job state.
    TriggerValueValuesEnum: Backfill job's triggering reason.

  Fields:
    errors: Output only. Errors which caused the backfill job to fail.
    lastEndTime: Output only. Backfill job's end time.
    lastStartTime: Output only. Backfill job's start time.
    state: Output only. Backfill job state.
    trigger: Backfill job's triggering reason.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Backfill job state.

    Values:
      STATE_UNSPECIFIED: Default value.
      NOT_STARTED: Backfill job was never started for the stream object
        (stream has backfill strategy defined as manual or object was
        explicitly excluded from automatic backfill).
      PENDING: Backfill job will start pending available resources.
      ACTIVE: Backfill job is running.
      STOPPED: Backfill job stopped (next job run will start from beginning).
      FAILED: Backfill job failed (due to an error).
      COMPLETED: Backfill completed successfully.
      UNSUPPORTED: Backfill job failed since the table structure is currently
        unsupported for backfill.
    """
    STATE_UNSPECIFIED = 0
    NOT_STARTED = 1
    PENDING = 2
    ACTIVE = 3
    STOPPED = 4
    FAILED = 5
    COMPLETED = 6
    UNSUPPORTED = 7

  class TriggerValueValuesEnum(_messages.Enum):
    r"""Backfill job's triggering reason.

    Values:
      TRIGGER_UNSPECIFIED: Default value.
      AUTOMATIC: Object backfill job was triggered automatically according to
        the stream's backfill strategy.
      MANUAL: Object backfill job was triggered manually using the dedicated
        API.
    """
    TRIGGER_UNSPECIFIED = 0
    AUTOMATIC = 1
    MANUAL = 2

  errors = _messages.MessageField('Error', 1, repeated=True)
  lastEndTime = _messages.StringField(2)
  lastStartTime = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  trigger = _messages.EnumField('TriggerValueValuesEnum', 5)


class BackfillNoneStrategy(_messages.Message):
  r"""Backfill strategy to disable automatic backfill for the Stream's
  objects.
  """



class BigQueryDestinationConfig(_messages.Message):
  r"""BigQuery destination configuration

  Fields:
    dataFreshness: The guaranteed data freshness (in seconds) when querying
      tables created by the stream. Editing this field will only affect new
      tables created in the future, but existing tables will not be impacted.
      Lower values mean that queries will return fresher data, but may result
      in higher cost.
    singleTargetDataset: Single destination dataset.
    sourceHierarchyDatasets: Source hierarchy datasets.
  """

  dataFreshness = _messages.StringField(1)
  singleTargetDataset = _messages.MessageField('SingleTargetDataset', 2)
  sourceHierarchyDatasets = _messages.MessageField('SourceHierarchyDatasets', 3)


class BigQueryProfile(_messages.Message):
  r"""BigQuery warehouse profile."""


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CdcStrategy(_messages.Message):
  r"""The strategy that the stream uses for CDC replication.

  Fields:
    mostRecentStartPosition: Optional. Start replicating from the most recent
      position in the source.
    nextAvailableStartPosition: Optional. Resume replication from the next
      available position in the source.
    specificStartPosition: Optional. Start replicating from a specific
      position in the source.
  """

  mostRecentStartPosition = _messages.MessageField('MostRecentStartPosition', 1)
  nextAvailableStartPosition = _messages.MessageField('NextAvailableStartPosition', 2)
  specificStartPosition = _messages.MessageField('SpecificStartPosition', 3)


class ConnectionProfile(_messages.Message):
  r"""A set of reusable connection configurations to be used as a source or
  destination for a stream.

  Messages:
    LabelsValue: Labels.

  Fields:
    bigqueryProfile: BigQuery Connection Profile configuration.
    createTime: Output only. The create time of the resource.
    displayName: Required. Display name.
    forwardSshConnectivity: Forward SSH tunnel connectivity.
    gcsProfile: Cloud Storage ConnectionProfile configuration.
    labels: Labels.
    mysqlProfile: MySQL ConnectionProfile configuration.
    name: Output only. The resource's name.
    oracleProfile: Oracle ConnectionProfile configuration.
    postgresqlProfile: PostgreSQL Connection Profile configuration.
    privateConnectivity: Private connectivity.
    staticServiceIpConnectivity: Static Service IP connectivity.
    updateTime: Output only. The update time of the resource.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bigqueryProfile = _messages.MessageField('BigQueryProfile', 1)
  createTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  forwardSshConnectivity = _messages.MessageField('ForwardSshTunnelConnectivity', 4)
  gcsProfile = _messages.MessageField('GcsProfile', 5)
  labels = _messages.MessageField('LabelsValue', 6)
  mysqlProfile = _messages.MessageField('MysqlProfile', 7)
  name = _messages.StringField(8)
  oracleProfile = _messages.MessageField('OracleProfile', 9)
  postgresqlProfile = _messages.MessageField('PostgresqlProfile', 10)
  privateConnectivity = _messages.MessageField('PrivateConnectivity', 11)
  staticServiceIpConnectivity = _messages.MessageField('StaticServiceIpConnectivity', 12)
  updateTime = _messages.StringField(13)


class DatasetTemplate(_messages.Message):
  r"""Dataset template used for dynamic dataset creation.

  Fields:
    datasetIdPrefix: If supplied, every created dataset will have its name
      prefixed by the provided value. The prefix and name will be separated by
      an underscore. i.e. _.
    kmsKeyName: Describes the Cloud KMS encryption key that will be used to
      protect destination BigQuery table. The BigQuery Service Account
      associated with your project requires access to this encryption key.
      i.e. projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoK
      eys/{cryptoKey}. See https://cloud.google.com/bigquery/docs/customer-
      managed-encryption for more information.
    location: Required. The geographic location where the dataset should
      reside. See https://cloud.google.com/bigquery/docs/locations for
      supported locations.
  """

  datasetIdPrefix = _messages.StringField(1)
  kmsKeyName = _messages.StringField(2)
  location = _messages.StringField(3)


class DatastreamProjectsLocationsConnectionProfilesCreateRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsConnectionProfilesCreateRequest object.

  Fields:
    connectionProfile: A ConnectionProfile resource to be passed as the
      request body.
    connectionProfileId: Required. The connection profile identifier.
    force: Optional. Create the connection profile without validating it.
    parent: Required. The parent that owns the collection of
      ConnectionProfiles.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. Only validate the connection profile, but don't
      create any resources. The default is false.
  """

  connectionProfile = _messages.MessageField('ConnectionProfile', 1)
  connectionProfileId = _messages.StringField(2)
  force = _messages.BooleanField(3)
  parent = _messages.StringField(4, required=True)
  requestId = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class DatastreamProjectsLocationsConnectionProfilesDeleteRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsConnectionProfilesDeleteRequest object.

  Fields:
    name: Required. The name of the connection profile resource to delete.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class DatastreamProjectsLocationsConnectionProfilesDiscoverRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsConnectionProfilesDiscoverRequest object.

  Fields:
    discoverConnectionProfileRequest: A DiscoverConnectionProfileRequest
      resource to be passed as the request body.
    parent: Required. The parent resource of the connection profile type. Must
      be in the format `projects/*/locations/*`.
  """

  discoverConnectionProfileRequest = _messages.MessageField('DiscoverConnectionProfileRequest', 1)
  parent = _messages.StringField(2, required=True)


class DatastreamProjectsLocationsConnectionProfilesGetRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsConnectionProfilesGetRequest object.

  Fields:
    name: Required. The name of the connection profile resource to get.
  """

  name = _messages.StringField(1, required=True)


class DatastreamProjectsLocationsConnectionProfilesListRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsConnectionProfilesListRequest object.

  Fields:
    filter: Filter request.
    orderBy: Order by fields for the result.
    pageSize: Maximum number of connection profiles to return. If unspecified,
      at most 50 connection profiles will be returned. The maximum value is
      1000; values above 1000 will be coerced to 1000.
    pageToken: Page token received from a previous `ListConnectionProfiles`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListConnectionProfiles` must match the
      call that provided the page token.
    parent: Required. The parent that owns the collection of connection
      profiles.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DatastreamProjectsLocationsConnectionProfilesPatchRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsConnectionProfilesPatchRequest object.

  Fields:
    connectionProfile: A ConnectionProfile resource to be passed as the
      request body.
    force: Optional. Update the connection profile without validating it.
    name: Output only. The resource's name.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the ConnectionProfile resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
    validateOnly: Optional. Only validate the connection profile, but don't
      update any resources. The default is false.
  """

  connectionProfile = _messages.MessageField('ConnectionProfile', 1)
  force = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class DatastreamProjectsLocationsFetchStaticIpsRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsFetchStaticIpsRequest object.

  Fields:
    name: Required. The resource name for the location for which static IPs
      should be returned. Must be in the format `projects/*/locations/*`.
    pageSize: Maximum number of Ips to return, will likely not be specified.
    pageToken: A page token, received from a previous `ListStaticIps` call.
      will likely not be specified.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class DatastreamProjectsLocationsGetRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class DatastreamProjectsLocationsListRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class DatastreamProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class DatastreamProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class DatastreamProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class DatastreamProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class DatastreamProjectsLocationsPrivateConnectionsCreateRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsPrivateConnectionsCreateRequest object.

  Fields:
    force: Optional. If set to true, will skip validations.
    parent: Required. The parent that owns the collection of
      PrivateConnections.
    privateConnection: A PrivateConnection resource to be passed as the
      request body.
    privateConnectionId: Required. The private connectivity identifier.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  force = _messages.BooleanField(1)
  parent = _messages.StringField(2, required=True)
  privateConnection = _messages.MessageField('PrivateConnection', 3)
  privateConnectionId = _messages.StringField(4)
  requestId = _messages.StringField(5)


class DatastreamProjectsLocationsPrivateConnectionsDeleteRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsPrivateConnectionsDeleteRequest object.

  Fields:
    force: Optional. If set to true, any child routes that belong to this
      PrivateConnection will also be deleted.
    name: Required. The name of the private connectivity configuration to
      delete.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class DatastreamProjectsLocationsPrivateConnectionsGetRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsPrivateConnectionsGetRequest object.

  Fields:
    name: Required. The name of the private connectivity configuration to get.
  """

  name = _messages.StringField(1, required=True)


class DatastreamProjectsLocationsPrivateConnectionsListRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsPrivateConnectionsListRequest object.

  Fields:
    filter: Filter request.
    orderBy: Order by fields for the result.
    pageSize: Maximum number of private connectivity configurations to return.
      If unspecified, at most 50 private connectivity configurations that will
      be returned. The maximum value is 1000; values above 1000 will be
      coerced to 1000.
    pageToken: Page token received from a previous `ListPrivateConnections`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListPrivateConnections` must match the
      call that provided the page token.
    parent: Required. The parent that owns the collection of private
      connectivity configurations.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DatastreamProjectsLocationsPrivateConnectionsRoutesCreateRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsPrivateConnectionsRoutesCreateRequest
  object.

  Fields:
    parent: Required. The parent that owns the collection of Routes.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    route: A Route resource to be passed as the request body.
    routeId: Required. The Route identifier.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  route = _messages.MessageField('Route', 3)
  routeId = _messages.StringField(4)


class DatastreamProjectsLocationsPrivateConnectionsRoutesDeleteRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsPrivateConnectionsRoutesDeleteRequest
  object.

  Fields:
    name: Required. The name of the Route resource to delete.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class DatastreamProjectsLocationsPrivateConnectionsRoutesGetRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsPrivateConnectionsRoutesGetRequest object.

  Fields:
    name: Required. The name of the Route resource to get.
  """

  name = _messages.StringField(1, required=True)


class DatastreamProjectsLocationsPrivateConnectionsRoutesListRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsPrivateConnectionsRoutesListRequest object.

  Fields:
    filter: Filter request.
    orderBy: Order by fields for the result.
    pageSize: Maximum number of Routes to return. The service may return fewer
      than this value. If unspecified, at most 50 Routes will be returned. The
      maximum value is 1000; values above 1000 will be coerced to 1000.
    pageToken: Page token received from a previous `ListRoutes` call. Provide
      this to retrieve the subsequent page. When paginating, all other
      parameters provided to `ListRoutes` must match the call that provided
      the page token.
    parent: Required. The parent that owns the collection of Routess.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DatastreamProjectsLocationsStreamsCreateRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsStreamsCreateRequest object.

  Fields:
    force: Optional. Create the stream without validating it.
    parent: Required. The parent that owns the collection of streams.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    stream: A Stream resource to be passed as the request body.
    streamId: Required. The stream identifier.
    validateOnly: Optional. Only validate the stream, but don't create any
      resources. The default is false.
  """

  force = _messages.BooleanField(1)
  parent = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  stream = _messages.MessageField('Stream', 4)
  streamId = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class DatastreamProjectsLocationsStreamsDeleteRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsStreamsDeleteRequest object.

  Fields:
    name: Required. The name of the stream resource to delete.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class DatastreamProjectsLocationsStreamsGetRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsStreamsGetRequest object.

  Fields:
    name: Required. The name of the stream resource to get.
  """

  name = _messages.StringField(1, required=True)


class DatastreamProjectsLocationsStreamsListRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsStreamsListRequest object.

  Fields:
    filter: Filter request.
    orderBy: Order by fields for the result.
    pageSize: Maximum number of streams to return. If unspecified, at most 50
      streams will be returned. The maximum value is 1000; values above 1000
      will be coerced to 1000.
    pageToken: Page token received from a previous `ListStreams` call. Provide
      this to retrieve the subsequent page. When paginating, all other
      parameters provided to `ListStreams` must match the call that provided
      the page token.
    parent: Required. The parent that owns the collection of streams.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class DatastreamProjectsLocationsStreamsObjectsGetRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsStreamsObjectsGetRequest object.

  Fields:
    name: Required. The name of the stream object resource to get.
  """

  name = _messages.StringField(1, required=True)


class DatastreamProjectsLocationsStreamsObjectsListRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsStreamsObjectsListRequest object.

  Fields:
    pageSize: Maximum number of objects to return. Default is 50. The maximum
      value is 1000; values above 1000 will be coerced to 1000.
    pageToken: Page token received from a previous `ListStreamObjectsRequest`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListStreamObjectsRequest` must match the
      call that provided the page token.
    parent: Required. The parent stream that owns the collection of objects.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class DatastreamProjectsLocationsStreamsObjectsLookupRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsStreamsObjectsLookupRequest object.

  Fields:
    lookupStreamObjectRequest: A LookupStreamObjectRequest resource to be
      passed as the request body.
    parent: Required. The parent stream that owns the collection of objects.
  """

  lookupStreamObjectRequest = _messages.MessageField('LookupStreamObjectRequest', 1)
  parent = _messages.StringField(2, required=True)


class DatastreamProjectsLocationsStreamsObjectsStartBackfillJobRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsStreamsObjectsStartBackfillJobRequest
  object.

  Fields:
    object: Required. The name of the stream object resource to start a
      backfill job for.
    startBackfillJobRequest: A StartBackfillJobRequest resource to be passed
      as the request body.
  """

  object = _messages.StringField(1, required=True)
  startBackfillJobRequest = _messages.MessageField('StartBackfillJobRequest', 2)


class DatastreamProjectsLocationsStreamsObjectsStopBackfillJobRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsStreamsObjectsStopBackfillJobRequest
  object.

  Fields:
    object: Required. The name of the stream object resource to stop the
      backfill job for.
    stopBackfillJobRequest: A StopBackfillJobRequest resource to be passed as
      the request body.
  """

  object = _messages.StringField(1, required=True)
  stopBackfillJobRequest = _messages.MessageField('StopBackfillJobRequest', 2)


class DatastreamProjectsLocationsStreamsPatchRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsStreamsPatchRequest object.

  Fields:
    cdcStrategy_specificStartPosition_mysqlLogPosition_logFile: The binary log
      file name.
    cdcStrategy_specificStartPosition_mysqlLogPosition_logPosition: The
      position within the binary log file. Default is head of file.
    force: Optional. Update the stream without validating it.
    name: Output only. The stream's name.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    stream: A Stream resource to be passed as the request body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the stream resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
    validateOnly: Optional. Only validate the stream with the changes, without
      actually updating it. The default is false.
  """

  cdcStrategy_specificStartPosition_mysqlLogPosition_logFile = _messages.StringField(1)
  cdcStrategy_specificStartPosition_mysqlLogPosition_logPosition = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  force = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  requestId = _messages.StringField(5)
  stream = _messages.MessageField('Stream', 6)
  updateMask = _messages.StringField(7)
  validateOnly = _messages.BooleanField(8)


class DatastreamProjectsLocationsStreamsRunRequest(_messages.Message):
  r"""A DatastreamProjectsLocationsStreamsRunRequest object.

  Fields:
    name: Required. Name of the stream resource to start, in the format:
      projects/{project_id}/locations/{location}/streams/{stream_name}
    runStreamRequest: A RunStreamRequest resource to be passed as the request
      body.
  """

  name = _messages.StringField(1, required=True)
  runStreamRequest = _messages.MessageField('RunStreamRequest', 2)


class DestinationConfig(_messages.Message):
  r"""The configuration of the stream destination.

  Fields:
    bigqueryDestinationConfig: BigQuery destination configuration.
    destinationConnectionProfile: Required. Destination connection profile
      resource. Format:
      `projects/{project}/locations/{location}/connectionProfiles/{name}`
    gcsDestinationConfig: A configuration for how data should be loaded to
      Cloud Storage.
  """

  bigqueryDestinationConfig = _messages.MessageField('BigQueryDestinationConfig', 1)
  destinationConnectionProfile = _messages.StringField(2)
  gcsDestinationConfig = _messages.MessageField('GcsDestinationConfig', 3)


class DiscoverConnectionProfileRequest(_messages.Message):
  r"""Request message for 'discover' ConnectionProfile request.

  Fields:
    connectionProfile: An ad-hoc connection profile configuration.
    connectionProfileName: A reference to an existing connection profile.
    fullHierarchy: Whether to retrieve the full hierarchy of data objects
      (TRUE) or only the current level (FALSE).
    hierarchyDepth: The number of hierarchy levels below the current level to
      be retrieved.
    mysqlRdbms: MySQL RDBMS to enrich with child data objects and metadata.
    oracleRdbms: Oracle RDBMS to enrich with child data objects and metadata.
    postgresqlRdbms: PostgreSQL RDBMS to enrich with child data objects and
      metadata.
  """

  connectionProfile = _messages.MessageField('ConnectionProfile', 1)
  connectionProfileName = _messages.StringField(2)
  fullHierarchy = _messages.BooleanField(3)
  hierarchyDepth = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  mysqlRdbms = _messages.MessageField('MysqlRdbms', 5)
  oracleRdbms = _messages.MessageField('OracleRdbms', 6)
  postgresqlRdbms = _messages.MessageField('PostgresqlRdbms', 7)


class DiscoverConnectionProfileResponse(_messages.Message):
  r"""Response from a discover request.

  Fields:
    mysqlRdbms: Enriched MySQL RDBMS object.
    oracleRdbms: Enriched Oracle RDBMS object.
    postgresqlRdbms: Enriched PostgreSQL RDBMS object.
  """

  mysqlRdbms = _messages.MessageField('MysqlRdbms', 1)
  oracleRdbms = _messages.MessageField('OracleRdbms', 2)
  postgresqlRdbms = _messages.MessageField('PostgresqlRdbms', 3)


class DropLargeObjects(_messages.Message):
  r"""Configuration to drop large object values."""


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Error(_messages.Message):
  r"""Represent a user-facing Error.

  Messages:
    DetailsValue: Additional information about the error.

  Fields:
    details: Additional information about the error.
    errorTime: The time when the error occurred.
    errorUuid: A unique identifier for this specific error, allowing it to be
      traced throughout the system in logs and API responses.
    message: A message containing more information about the error that
      occurred.
    reason: A title that explains the reason for the error.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValue(_messages.Message):
    r"""Additional information about the error.

    Messages:
      AdditionalProperty: An additional property for a DetailsValue object.

    Fields:
      additionalProperties: Additional properties of type DetailsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  details = _messages.MessageField('DetailsValue', 1)
  errorTime = _messages.StringField(2)
  errorUuid = _messages.StringField(3)
  message = _messages.StringField(4)
  reason = _messages.StringField(5)


class FetchStaticIpsResponse(_messages.Message):
  r"""Response message for a 'FetchStaticIps' response.

  Fields:
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    staticIps: list of static ips by account
  """

  nextPageToken = _messages.StringField(1)
  staticIps = _messages.StringField(2, repeated=True)


class ForwardSshTunnelConnectivity(_messages.Message):
  r"""Forward SSH Tunnel connectivity.

  Fields:
    hostname: Required. Hostname for the SSH tunnel.
    password: Input only. SSH password.
    port: Port for the SSH tunnel, default value is 22.
    privateKey: Input only. SSH private key.
    username: Required. Username for the SSH tunnel.
  """

  hostname = _messages.StringField(1)
  password = _messages.StringField(2)
  port = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  privateKey = _messages.StringField(4)
  username = _messages.StringField(5)


class GcsDestinationConfig(_messages.Message):
  r"""Google Cloud Storage destination configuration

  Fields:
    avroFileFormat: AVRO file format configuration.
    fileRotationInterval: The maximum duration for which new events are added
      before a file is closed and a new file is created. Values within the
      range of 15-60 seconds are allowed.
    fileRotationMb: The maximum file size to be saved in the bucket.
    jsonFileFormat: JSON file format configuration.
    path: Path inside the Cloud Storage bucket to write data to.
  """

  avroFileFormat = _messages.MessageField('AvroFileFormat', 1)
  fileRotationInterval = _messages.StringField(2)
  fileRotationMb = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  jsonFileFormat = _messages.MessageField('JsonFileFormat', 4)
  path = _messages.StringField(5)


class GcsProfile(_messages.Message):
  r"""Cloud Storage bucket profile.

  Fields:
    bucket: Required. The Cloud Storage bucket name.
    rootPath: The root path inside the Cloud Storage bucket.
  """

  bucket = _messages.StringField(1)
  rootPath = _messages.StringField(2)


class JsonFileFormat(_messages.Message):
  r"""JSON file format configuration.

  Enums:
    CompressionValueValuesEnum: Compression of the loaded JSON file.
    SchemaFileFormatValueValuesEnum: The schema file format along JSON data
      files.

  Fields:
    compression: Compression of the loaded JSON file.
    schemaFileFormat: The schema file format along JSON data files.
  """

  class CompressionValueValuesEnum(_messages.Enum):
    r"""Compression of the loaded JSON file.

    Values:
      JSON_COMPRESSION_UNSPECIFIED: Unspecified json file compression.
      NO_COMPRESSION: Do not compress JSON file.
      GZIP: Gzip compression.
    """
    JSON_COMPRESSION_UNSPECIFIED = 0
    NO_COMPRESSION = 1
    GZIP = 2

  class SchemaFileFormatValueValuesEnum(_messages.Enum):
    r"""The schema file format along JSON data files.

    Values:
      SCHEMA_FILE_FORMAT_UNSPECIFIED: Unspecified schema file format.
      NO_SCHEMA_FILE: Do not attach schema file.
      AVRO_SCHEMA_FILE: Avro schema format.
    """
    SCHEMA_FILE_FORMAT_UNSPECIFIED = 0
    NO_SCHEMA_FILE = 1
    AVRO_SCHEMA_FILE = 2

  compression = _messages.EnumField('CompressionValueValuesEnum', 1)
  schemaFileFormat = _messages.EnumField('SchemaFileFormatValueValuesEnum', 2)


class ListConnectionProfilesResponse(_messages.Message):
  r"""Response message for listing connection profiles.

  Fields:
    connectionProfiles: List of connection profiles.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  connectionProfiles = _messages.MessageField('ConnectionProfile', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListPrivateConnectionsResponse(_messages.Message):
  r"""Response containing a list of private connection configurations.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    privateConnections: List of private connectivity configurations.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  privateConnections = _messages.MessageField('PrivateConnection', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListRoutesResponse(_messages.Message):
  r"""Route list response.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    routes: List of Routes.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  routes = _messages.MessageField('Route', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListStreamObjectsResponse(_messages.Message):
  r"""Response containing the objects for a stream.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page.
    streamObjects: List of stream objects.
  """

  nextPageToken = _messages.StringField(1)
  streamObjects = _messages.MessageField('StreamObject', 2, repeated=True)


class ListStreamsResponse(_messages.Message):
  r"""Response message for listing streams.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    streams: List of streams
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  streams = _messages.MessageField('Stream', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LookupStreamObjectRequest(_messages.Message):
  r"""Request for looking up a specific stream object by its source object
  identifier.

  Fields:
    sourceObjectIdentifier: Required. The source object identifier which maps
      to the stream object.
  """

  sourceObjectIdentifier = _messages.MessageField('SourceObjectIdentifier', 1)


class MostRecentStartPosition(_messages.Message):
  r"""CDC strategy to start replicating from the most recent position in the
  source.
  """



class MysqlColumn(_messages.Message):
  r"""MySQL Column.

  Fields:
    collation: Column collation.
    column: Column name.
    dataType: The MySQL data type. Full data types list can be found here:
      https://dev.mysql.com/doc/refman/8.0/en/data-types.html
    length: Column length.
    nullable: Whether or not the column can accept a null value.
    ordinalPosition: The ordinal position of the column in the table.
    precision: Column precision.
    primaryKey: Whether or not the column represents a primary key.
    scale: Column scale.
  """

  collation = _messages.StringField(1)
  column = _messages.StringField(2)
  dataType = _messages.StringField(3)
  length = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  nullable = _messages.BooleanField(5)
  ordinalPosition = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  precision = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  primaryKey = _messages.BooleanField(8)
  scale = _messages.IntegerField(9, variant=_messages.Variant.INT32)


class MysqlDatabase(_messages.Message):
  r"""MySQL database.

  Fields:
    database: Database name.
    mysqlTables: Tables in the database.
  """

  database = _messages.StringField(1)
  mysqlTables = _messages.MessageField('MysqlTable', 2, repeated=True)


class MysqlLogPosition(_messages.Message):
  r"""MySQL log position

  Fields:
    logFile: The binary log file name.
    logPosition: The position within the binary log file. Default is head of
      file.
  """

  logFile = _messages.StringField(1)
  logPosition = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class MysqlObjectIdentifier(_messages.Message):
  r"""Mysql data source object identifier.

  Fields:
    database: Required. The database name.
    table: Required. The table name.
  """

  database = _messages.StringField(1)
  table = _messages.StringField(2)


class MysqlProfile(_messages.Message):
  r"""MySQL database profile.

  Fields:
    hostname: Required. Hostname for the MySQL connection.
    password: Required. Input only. Password for the MySQL connection.
    port: Port for the MySQL connection, default value is 3306.
    sslConfig: SSL configuration for the MySQL connection.
    username: Required. Username for the MySQL connection.
  """

  hostname = _messages.StringField(1)
  password = _messages.StringField(2)
  port = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  sslConfig = _messages.MessageField('MysqlSslConfig', 4)
  username = _messages.StringField(5)


class MysqlRdbms(_messages.Message):
  r"""MySQL database structure

  Fields:
    mysqlDatabases: Mysql databases on the server
  """

  mysqlDatabases = _messages.MessageField('MysqlDatabase', 1, repeated=True)


class MysqlSourceConfig(_messages.Message):
  r"""MySQL source configuration

  Fields:
    excludeObjects: MySQL objects to exclude from the stream.
    includeObjects: MySQL objects to retrieve from the source.
    maxConcurrentBackfillTasks: Maximum number of concurrent backfill tasks.
      The number should be non negative. If not set (or set to 0), the
      system's default value will be used.
    maxConcurrentCdcTasks: Maximum number of concurrent CDC tasks. The number
      should be non negative. If not set (or set to 0), the system's default
      value will be used.
  """

  excludeObjects = _messages.MessageField('MysqlRdbms', 1)
  includeObjects = _messages.MessageField('MysqlRdbms', 2)
  maxConcurrentBackfillTasks = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  maxConcurrentCdcTasks = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class MysqlSslConfig(_messages.Message):
  r"""MySQL SSL configuration information.

  Fields:
    caCertificate: Input only. PEM-encoded certificate of the CA that signed
      the source database server's certificate.
    caCertificateSet: Output only. Indicates whether the ca_certificate field
      is set.
    clientCertificate: Input only. PEM-encoded certificate that will be used
      by the replica to authenticate against the source database server. If
      this field is used then the 'client_key' and the 'ca_certificate' fields
      are mandatory.
    clientCertificateSet: Output only. Indicates whether the
      client_certificate field is set.
    clientKey: Input only. PEM-encoded private key associated with the Client
      Certificate. If this field is used then the 'client_certificate' and the
      'ca_certificate' fields are mandatory.
    clientKeySet: Output only. Indicates whether the client_key field is set.
  """

  caCertificate = _messages.StringField(1)
  caCertificateSet = _messages.BooleanField(2)
  clientCertificate = _messages.StringField(3)
  clientCertificateSet = _messages.BooleanField(4)
  clientKey = _messages.StringField(5)
  clientKeySet = _messages.BooleanField(6)


class MysqlTable(_messages.Message):
  r"""MySQL table.

  Fields:
    mysqlColumns: MySQL columns in the database. When unspecified as part of
      include/exclude objects, includes/excludes everything.
    table: Table name.
  """

  mysqlColumns = _messages.MessageField('MysqlColumn', 1, repeated=True)
  table = _messages.StringField(2)


class NextAvailableStartPosition(_messages.Message):
  r"""CDC strategy to resume replication from the next available position in
  the source.
  """



class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    validationResult: Output only. Results of executed validations if there
      are any.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  validationResult = _messages.MessageField('ValidationResult', 7)
  verb = _messages.StringField(8)


class OracleColumn(_messages.Message):
  r"""Oracle Column.

  Fields:
    column: Column name.
    dataType: The Oracle data type.
    encoding: Column encoding.
    length: Column length.
    nullable: Whether or not the column can accept a null value.
    ordinalPosition: The ordinal position of the column in the table.
    precision: Column precision.
    primaryKey: Whether or not the column represents a primary key.
    scale: Column scale.
  """

  column = _messages.StringField(1)
  dataType = _messages.StringField(2)
  encoding = _messages.StringField(3)
  length = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  nullable = _messages.BooleanField(5)
  ordinalPosition = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  precision = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  primaryKey = _messages.BooleanField(8)
  scale = _messages.IntegerField(9, variant=_messages.Variant.INT32)


class OracleObjectIdentifier(_messages.Message):
  r"""Oracle data source object identifier.

  Fields:
    schema: Required. The schema name.
    table: Required. The table name.
  """

  schema = _messages.StringField(1)
  table = _messages.StringField(2)


class OracleProfile(_messages.Message):
  r"""Oracle database profile.

  Messages:
    ConnectionAttributesValue: Connection string attributes

  Fields:
    connectionAttributes: Connection string attributes
    databaseService: Required. Database for the Oracle connection.
    hostname: Required. Hostname for the Oracle connection.
    password: Required. Password for the Oracle connection.
    port: Port for the Oracle connection, default value is 1521.
    username: Required. Username for the Oracle connection.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConnectionAttributesValue(_messages.Message):
    r"""Connection string attributes

    Messages:
      AdditionalProperty: An additional property for a
        ConnectionAttributesValue object.

    Fields:
      additionalProperties: Additional properties of type
        ConnectionAttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConnectionAttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  connectionAttributes = _messages.MessageField('ConnectionAttributesValue', 1)
  databaseService = _messages.StringField(2)
  hostname = _messages.StringField(3)
  password = _messages.StringField(4)
  port = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  username = _messages.StringField(6)


class OracleRdbms(_messages.Message):
  r"""Oracle database structure.

  Fields:
    oracleSchemas: Oracle schemas/databases in the database server.
  """

  oracleSchemas = _messages.MessageField('OracleSchema', 1, repeated=True)


class OracleSchema(_messages.Message):
  r"""Oracle schema.

  Fields:
    oracleTables: Tables in the schema.
    schema: Schema name.
  """

  oracleTables = _messages.MessageField('OracleTable', 1, repeated=True)
  schema = _messages.StringField(2)


class OracleSourceConfig(_messages.Message):
  r"""Oracle data source configuration

  Fields:
    dropLargeObjects: Drop large object values.
    excludeObjects: Oracle objects to exclude from the stream.
    includeObjects: Oracle objects to include in the stream.
    maxConcurrentBackfillTasks: Maximum number of concurrent backfill tasks.
      The number should be non-negative. If not set (or set to 0), the
      system's default value is used.
    maxConcurrentCdcTasks: Maximum number of concurrent CDC tasks. The number
      should be non-negative. If not set (or set to 0), the system's default
      value is used.
    streamLargeObjects: Stream large object values. NOTE: This feature is
      currently experimental.
  """

  dropLargeObjects = _messages.MessageField('DropLargeObjects', 1)
  excludeObjects = _messages.MessageField('OracleRdbms', 2)
  includeObjects = _messages.MessageField('OracleRdbms', 3)
  maxConcurrentBackfillTasks = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  maxConcurrentCdcTasks = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  streamLargeObjects = _messages.MessageField('StreamLargeObjects', 6)


class OracleTable(_messages.Message):
  r"""Oracle table.

  Fields:
    oracleColumns: Oracle columns in the schema. When unspecified as part of
      include/exclude objects, includes/excludes everything.
    table: Table name.
  """

  oracleColumns = _messages.MessageField('OracleColumn', 1, repeated=True)
  table = _messages.StringField(2)


class PostgresqlColumn(_messages.Message):
  r"""PostgreSQL Column.

  Fields:
    column: Column name.
    dataType: The PostgreSQL data type.
    length: Column length.
    nullable: Whether or not the column can accept a null value.
    ordinalPosition: The ordinal position of the column in the table.
    precision: Column precision.
    primaryKey: Whether or not the column represents a primary key.
    scale: Column scale.
  """

  column = _messages.StringField(1)
  dataType = _messages.StringField(2)
  length = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  nullable = _messages.BooleanField(4)
  ordinalPosition = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  precision = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  primaryKey = _messages.BooleanField(7)
  scale = _messages.IntegerField(8, variant=_messages.Variant.INT32)


class PostgresqlObjectIdentifier(_messages.Message):
  r"""PostgreSQL data source object identifier.

  Fields:
    schema: Required. The schema name.
    table: Required. The table name.
  """

  schema = _messages.StringField(1)
  table = _messages.StringField(2)


class PostgresqlProfile(_messages.Message):
  r"""PostgreSQL database profile.

  Fields:
    database: Required. Database for the PostgreSQL connection.
    hostname: Required. Hostname for the PostgreSQL connection.
    password: Required. Password for the PostgreSQL connection.
    port: Port for the PostgreSQL connection, default value is 5432.
    username: Required. Username for the PostgreSQL connection.
  """

  database = _messages.StringField(1)
  hostname = _messages.StringField(2)
  password = _messages.StringField(3)
  port = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  username = _messages.StringField(5)


class PostgresqlRdbms(_messages.Message):
  r"""PostgreSQL database structure.

  Fields:
    postgresqlSchemas: PostgreSQL schemas in the database server.
  """

  postgresqlSchemas = _messages.MessageField('PostgresqlSchema', 1, repeated=True)


class PostgresqlSchema(_messages.Message):
  r"""PostgreSQL schema.

  Fields:
    postgresqlTables: Tables in the schema.
    schema: Schema name.
  """

  postgresqlTables = _messages.MessageField('PostgresqlTable', 1, repeated=True)
  schema = _messages.StringField(2)


class PostgresqlSourceConfig(_messages.Message):
  r"""PostgreSQL data source configuration

  Fields:
    excludeObjects: PostgreSQL objects to exclude from the stream.
    includeObjects: PostgreSQL objects to include in the stream.
    maxConcurrentBackfillTasks: Maximum number of concurrent backfill tasks.
      The number should be non negative. If not set (or set to 0), the
      system's default value will be used.
    publication: Required. The name of the publication that includes the set
      of all tables that are defined in the stream's include_objects.
    replicationSlot: Required. Immutable. The name of the logical replication
      slot that's configured with the pgoutput plugin.
  """

  excludeObjects = _messages.MessageField('PostgresqlRdbms', 1)
  includeObjects = _messages.MessageField('PostgresqlRdbms', 2)
  maxConcurrentBackfillTasks = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  publication = _messages.StringField(4)
  replicationSlot = _messages.StringField(5)


class PostgresqlTable(_messages.Message):
  r"""PostgreSQL table.

  Fields:
    postgresqlColumns: PostgreSQL columns in the schema. When unspecified as
      part of include/exclude objects, includes/excludes everything.
    table: Table name.
  """

  postgresqlColumns = _messages.MessageField('PostgresqlColumn', 1, repeated=True)
  table = _messages.StringField(2)


class PrivateConnection(_messages.Message):
  r"""The PrivateConnection resource is used to establish private connectivity
  between Datastream and a customer's network.

  Enums:
    StateValueValuesEnum: Output only. The state of the Private Connection.

  Messages:
    LabelsValue: Labels.

  Fields:
    createTime: Output only. The create time of the resource.
    displayName: Required. Display name.
    error: Output only. In case of error, the details of the error in a user-
      friendly format.
    labels: Labels.
    name: Output only. The resource's name.
    state: Output only. The state of the Private Connection.
    updateTime: Output only. The update time of the resource.
    vpcPeeringConfig: VPC Peering Config.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the Private Connection.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      CREATING: The private connection is in creation state - creating
        resources.
      CREATED: The private connection has been created with all of its
        resources.
      FAILED: The private connection creation has failed.
      DELETING: The private connection is being deleted.
      FAILED_TO_DELETE: Delete request has failed, resource is in invalid
        state.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    CREATED = 2
    FAILED = 3
    DELETING = 4
    FAILED_TO_DELETE = 5

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  displayName = _messages.StringField(2)
  error = _messages.MessageField('Error', 3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  updateTime = _messages.StringField(7)
  vpcPeeringConfig = _messages.MessageField('VpcPeeringConfig', 8)


class PrivateConnectivity(_messages.Message):
  r"""Private Connectivity

  Fields:
    privateConnection: Required. A reference to a private connection resource.
      Format:
      `projects/{project}/locations/{location}/privateConnections/{name}`
  """

  privateConnection = _messages.StringField(1)


class Route(_messages.Message):
  r"""The route resource is the child of the private connection resource, used
  for defining a route for a private connection.

  Messages:
    LabelsValue: Labels.

  Fields:
    createTime: Output only. The create time of the resource.
    destinationAddress: Required. Destination address for connection
    destinationPort: Destination port for connection
    displayName: Required. Display name.
    labels: Labels.
    name: Output only. The resource's name.
    updateTime: Output only. The update time of the resource.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  destinationAddress = _messages.StringField(2)
  destinationPort = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  displayName = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class RunStreamRequest(_messages.Message):
  r"""Request message for running a stream.

  Fields:
    cdcStrategy: Optional. The CDC strategy of the stream. If not set, the
      system's default value will be used.
  """

  cdcStrategy = _messages.MessageField('CdcStrategy', 1)


class SingleTargetDataset(_messages.Message):
  r"""A single target dataset to which all data will be streamed.

  Fields:
    datasetId: The dataset ID of the target dataset. DatasetIds allowed
      characters: https://cloud.google.com/bigquery/docs/reference/rest/v2/dat
      asets#datasetreference.
  """

  datasetId = _messages.StringField(1)


class SourceConfig(_messages.Message):
  r"""The configuration of the stream source.

  Fields:
    mysqlSourceConfig: MySQL data source configuration.
    oracleSourceConfig: Oracle data source configuration.
    postgresqlSourceConfig: PostgreSQL data source configuration.
    sourceConnectionProfile: Required. Source connection profile resoource.
      Format:
      `projects/{project}/locations/{location}/connectionProfiles/{name}`
  """

  mysqlSourceConfig = _messages.MessageField('MysqlSourceConfig', 1)
  oracleSourceConfig = _messages.MessageField('OracleSourceConfig', 2)
  postgresqlSourceConfig = _messages.MessageField('PostgresqlSourceConfig', 3)
  sourceConnectionProfile = _messages.StringField(4)


class SourceHierarchyDatasets(_messages.Message):
  r"""Destination datasets are created so that hierarchy of the destination
  data objects matches the source hierarchy.

  Fields:
    datasetTemplate: The dataset template to use for dynamic dataset creation.
  """

  datasetTemplate = _messages.MessageField('DatasetTemplate', 1)


class SourceObjectIdentifier(_messages.Message):
  r"""Represents an identifier of an object in the data source.

  Fields:
    mysqlIdentifier: Mysql data source object identifier.
    oracleIdentifier: Oracle data source object identifier.
    postgresqlIdentifier: PostgreSQL data source object identifier.
  """

  mysqlIdentifier = _messages.MessageField('MysqlObjectIdentifier', 1)
  oracleIdentifier = _messages.MessageField('OracleObjectIdentifier', 2)
  postgresqlIdentifier = _messages.MessageField('PostgresqlObjectIdentifier', 3)


class SpecificStartPosition(_messages.Message):
  r"""CDC strategy to start replicating from a specific position in the
  source.

  Fields:
    mysqlLogPosition: MySQL specific log position to start replicating from.
  """

  mysqlLogPosition = _messages.MessageField('MysqlLogPosition', 1)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class StartBackfillJobRequest(_messages.Message):
  r"""Request for manually initiating a backfill job for a specific stream
  object.
  """



class StartBackfillJobResponse(_messages.Message):
  r"""Response for manually initiating a backfill job for a specific stream
  object.

  Fields:
    object: The stream object resource a backfill job was started for.
  """

  object = _messages.MessageField('StreamObject', 1)


class StaticServiceIpConnectivity(_messages.Message):
  r"""Static IP address connectivity. Used when the source database is
  configured to allow incoming connections from the Datastream public IP
  addresses for the region specified in the connection profile.
  """



class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class StopBackfillJobRequest(_messages.Message):
  r"""Request for manually stopping a running backfill job for a specific
  stream object.
  """



class StopBackfillJobResponse(_messages.Message):
  r"""Response for manually stop a backfill job for a specific stream object.

  Fields:
    object: The stream object resource the backfill job was stopped for.
  """

  object = _messages.MessageField('StreamObject', 1)


class Stream(_messages.Message):
  r"""A resource representing streaming data from a source to a destination.

  Enums:
    StateValueValuesEnum: The state of the stream.

  Messages:
    LabelsValue: Labels.

  Fields:
    backfillAll: Automatically backfill objects included in the stream source
      configuration. Specific objects can be excluded.
    backfillNone: Do not automatically backfill any objects.
    createTime: Output only. The creation time of the stream.
    customerManagedEncryptionKey: Immutable. A reference to a KMS encryption
      key. If provided, it will be used to encrypt the data. If left blank,
      data will be encrypted using an internal Stream-specific encryption key
      provisioned through KMS.
    destinationConfig: Required. Destination connection profile configuration.
    displayName: Required. Display name.
    errors: Output only. Errors on the Stream.
    labels: Labels.
    lastRecoveryTime: Output only. If the stream was recovered, the time of
      the last recovery. Note: This field is currently experimental.
    name: Output only. The stream's name.
    sourceConfig: Required. Source connection profile configuration.
    state: The state of the stream.
    updateTime: Output only. The last update time of the stream.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the stream.

    Values:
      STATE_UNSPECIFIED: Unspecified stream state.
      NOT_STARTED: The stream has been created but has not yet started
        streaming data.
      RUNNING: The stream is running.
      PAUSED: The stream is paused.
      MAINTENANCE: The stream is in maintenance mode. Updates are rejected on
        the resource in this state.
      FAILED: The stream is experiencing an error that is preventing data from
        being streamed.
      FAILED_PERMANENTLY: The stream has experienced a terminal failure.
      STARTING: The stream is starting, but not yet running.
      DRAINING: The Stream is no longer reading new events, but still writing
        events in the buffer.
    """
    STATE_UNSPECIFIED = 0
    NOT_STARTED = 1
    RUNNING = 2
    PAUSED = 3
    MAINTENANCE = 4
    FAILED = 5
    FAILED_PERMANENTLY = 6
    STARTING = 7
    DRAINING = 8

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  backfillAll = _messages.MessageField('BackfillAllStrategy', 1)
  backfillNone = _messages.MessageField('BackfillNoneStrategy', 2)
  createTime = _messages.StringField(3)
  customerManagedEncryptionKey = _messages.StringField(4)
  destinationConfig = _messages.MessageField('DestinationConfig', 5)
  displayName = _messages.StringField(6)
  errors = _messages.MessageField('Error', 7, repeated=True)
  labels = _messages.MessageField('LabelsValue', 8)
  lastRecoveryTime = _messages.StringField(9)
  name = _messages.StringField(10)
  sourceConfig = _messages.MessageField('SourceConfig', 11)
  state = _messages.EnumField('StateValueValuesEnum', 12)
  updateTime = _messages.StringField(13)


class StreamLargeObjects(_messages.Message):
  r"""Configuration to stream large object values."""


class StreamObject(_messages.Message):
  r"""A specific stream object (e.g a specific DB table).

  Fields:
    backfillJob: The latest backfill job that was initiated for the stream
      object.
    createTime: Output only. The creation time of the object.
    displayName: Required. Display name.
    errors: Output only. Active errors on the object.
    name: Output only. The object resource's name.
    sourceObject: The object identifier in the data source.
    updateTime: Output only. The last update time of the object.
  """

  backfillJob = _messages.MessageField('BackfillJob', 1)
  createTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  errors = _messages.MessageField('Error', 4, repeated=True)
  name = _messages.StringField(5)
  sourceObject = _messages.MessageField('SourceObjectIdentifier', 6)
  updateTime = _messages.StringField(7)


class Validation(_messages.Message):
  r"""A validation to perform on a stream.

  Enums:
    StateValueValuesEnum: Output only. Validation execution status.

  Fields:
    code: A custom code identifying this validation.
    description: A short description of the validation.
    message: Messages reflecting the validation results.
    state: Output only. Validation execution status.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Validation execution status.

    Values:
      STATE_UNSPECIFIED: Unspecified state.
      NOT_EXECUTED: Validation did not execute.
      FAILED: Validation failed.
      PASSED: Validation passed.
    """
    STATE_UNSPECIFIED = 0
    NOT_EXECUTED = 1
    FAILED = 2
    PASSED = 3

  code = _messages.StringField(1)
  description = _messages.StringField(2)
  message = _messages.MessageField('ValidationMessage', 3, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class ValidationMessage(_messages.Message):
  r"""Represent user-facing validation result message.

  Enums:
    LevelValueValuesEnum: Message severity level (warning or error).

  Messages:
    MetadataValue: Additional metadata related to the result.

  Fields:
    code: A custom code identifying this specific message.
    level: Message severity level (warning or error).
    message: The result of the validation.
    metadata: Additional metadata related to the result.
  """

  class LevelValueValuesEnum(_messages.Enum):
    r"""Message severity level (warning or error).

    Values:
      LEVEL_UNSPECIFIED: Unspecified level.
      WARNING: Potentially cause issues with the Stream.
      ERROR: Definitely cause issues with the Stream.
    """
    LEVEL_UNSPECIFIED = 0
    WARNING = 1
    ERROR = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Additional metadata related to the result.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.StringField(1)
  level = _messages.EnumField('LevelValueValuesEnum', 2)
  message = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)


class ValidationResult(_messages.Message):
  r"""Contains the current validation results.

  Fields:
    validations: A list of validations (includes both executed as well as not
      executed validations).
  """

  validations = _messages.MessageField('Validation', 1, repeated=True)


class VpcPeeringConfig(_messages.Message):
  r"""The VPC Peering configuration is used to create VPC peering between
  Datastream and the consumer's VPC.

  Fields:
    subnet: Required. A free subnet for peering. (CIDR of /29)
    vpc: Required. Fully qualified name of the VPC that Datastream will peer
      to. Format: `projects/{project}/global/{networks}/{name}`
  """

  subnet = _messages.StringField(1)
  vpc = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

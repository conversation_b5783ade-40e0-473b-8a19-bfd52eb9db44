"""Generated message classes for ondemandscanning version v1beta1.

A service to scan container images for vulnerabilities.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'ondemandscanning'


class AliasContext(_messages.Message):
  r"""An alias to a repo revision.

  Enums:
    KindValueValuesEnum: The alias kind.

  Fields:
    kind: The alias kind.
    name: The alias name.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""The alias kind.

    Values:
      KIND_UNSPECIFIED: Unknown.
      FIXED: Git tag.
      MOVABLE: Git branch.
      OTHER: Used to specify non-standard aliases. For example, if a Git repo
        has a ref named "refs/foo/bar".
    """
    KIND_UNSPECIFIED = 0
    FIXED = 1
    MOVABLE = 2
    OTHER = 3

  kind = _messages.EnumField('KindValueValuesEnum', 1)
  name = _messages.StringField(2)


class AnalysisCompleted(_messages.Message):
  r"""Indicates which analysis completed successfully. Multiple types of
  analysis can be performed on a single resource.

  Fields:
    analysisType: A string attribute.
  """

  analysisType = _messages.StringField(1, repeated=True)


class AnalyzePackagesMetadata(_messages.Message):
  r"""AnalyzePackagesMetadata contains metadata for an active scan of a
  container image.

  Fields:
    createTime: When the scan was created.
    resourceUri: The resource URI of the container image being scanned.
  """

  createTime = _messages.StringField(1)
  resourceUri = _messages.StringField(2)


class AnalyzePackagesMetadataV1(_messages.Message):
  r"""AnalyzePackagesMetadata contains metadata for an active scan of a
  container image.

  Fields:
    createTime: When the scan was created.
    resourceUri: The resource URI of the container image being scanned.
  """

  createTime = _messages.StringField(1)
  resourceUri = _messages.StringField(2)


class AnalyzePackagesRequest(_messages.Message):
  r"""AnalyzePackagesRequest is the request to analyze a list of packages and
  create Vulnerability Occurrences for it.

  Fields:
    packages: The packages to analyze.
    resourceUri: Required. The resource URI of the container image being
      scanned.
  """

  packages = _messages.MessageField('PackageData', 1, repeated=True)
  resourceUri = _messages.StringField(2)


class AnalyzePackagesResponse(_messages.Message):
  r"""AnalyzePackagesResponse contains the information necessary to find
  results for the given scan.

  Fields:
    scan: The name of the scan resource created by this successful scan.
  """

  scan = _messages.StringField(1)


class AnalyzePackagesResponseV1(_messages.Message):
  r"""AnalyzePackagesResponse contains the information necessary to find
  results for the given scan.

  Fields:
    scan: The name of the scan resource created by this successful scan.
  """

  scan = _messages.StringField(1)


class Artifact(_messages.Message):
  r"""Artifact describes a build product.

  Fields:
    checksum: Hash or checksum value of a binary, or Docker Registry 2.0
      digest of a container.
    id: Artifact ID, if any; for container images, this will be a URL by
      digest like `gcr.io/projectID/imagename@sha256:123456`.
    names: Related artifact names. This may be the path to a binary or jar
      file, or in the case of a container build, the name used to push the
      container image to Google Container Registry, as presented to `docker
      push`. Note that a single Artifact ID can have multiple names, for
      example if two tags are applied to one image.
  """

  checksum = _messages.StringField(1)
  id = _messages.StringField(2)
  names = _messages.StringField(3, repeated=True)


class AttestationOccurrence(_messages.Message):
  r"""Occurrence that represents a single "attestation". The authenticity of
  an attestation can be verified using the attached signature. If the verifier
  trusts the public key of the signer, then verifying the signature is
  sufficient to establish trust. In this circumstance, the authority to which
  this attestation is attached is primarily useful for lookup (how to find
  this attestation if you already know the authority and artifact to be
  verified) and intent (for which authority this attestation was intended to
  sign.

  Fields:
    jwts: One or more JWTs encoding a self-contained attestation. Each JWT
      encodes the payload that it verifies within the JWT itself. Verifier
      implementation SHOULD ignore the `serialized_payload` field when
      verifying these JWTs. If only JWTs are present on this
      AttestationOccurrence, then the `serialized_payload` SHOULD be left
      empty. Each JWT SHOULD encode a claim specific to the `resource_uri` of
      this Occurrence, but this is not validated by Grafeas metadata API
      implementations. The JWT itself is opaque to Grafeas.
    serializedPayload: Required. The serialized payload that is verified by
      one or more `signatures`.
    signatures: One or more signatures over `serialized_payload`. Verifier
      implementations should consider this attestation message verified if at
      least one `signature` verifies `serialized_payload`. See `Signature` in
      common.proto for more details on signature structure and verification.
  """

  jwts = _messages.MessageField('Jwt', 1, repeated=True)
  serializedPayload = _messages.BytesField(2)
  signatures = _messages.MessageField('Signature', 3, repeated=True)


class BinarySourceInfo(_messages.Message):
  r"""A BinarySourceInfo object.

  Fields:
    binaryVersion: The binary package. This is significant when the source is
      different than the binary itself. Historically if they've differed,
      we've stored the name of the source and its version in the
      package/version fields, but we should also store the binary package
      info, as that's what's actually installed. See b/175908657#comment15.
    sourceVersion: The source package. Similar to the above, this is
      significant when the source is different than the binary itself. Since
      the top-level package/version fields are based on an if/else, we need a
      separate field for both binary and source if we want to know
      definitively where the data is coming from.
  """

  binaryVersion = _messages.MessageField('PackageVersion', 1)
  sourceVersion = _messages.MessageField('PackageVersion', 2)


class BuildDefinition(_messages.Message):
  r"""A BuildDefinition object.

  Messages:
    ExternalParametersValue: A ExternalParametersValue object.
    InternalParametersValue: A InternalParametersValue object.

  Fields:
    buildType: A string attribute.
    externalParameters: A ExternalParametersValue attribute.
    internalParameters: A InternalParametersValue attribute.
    resolvedDependencies: A ResourceDescriptor attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExternalParametersValue(_messages.Message):
    r"""A ExternalParametersValue object.

    Messages:
      AdditionalProperty: An additional property for a ExternalParametersValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExternalParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class InternalParametersValue(_messages.Message):
    r"""A InternalParametersValue object.

    Messages:
      AdditionalProperty: An additional property for a InternalParametersValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a InternalParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  buildType = _messages.StringField(1)
  externalParameters = _messages.MessageField('ExternalParametersValue', 2)
  internalParameters = _messages.MessageField('InternalParametersValue', 3)
  resolvedDependencies = _messages.MessageField('ResourceDescriptor', 4, repeated=True)


class BuildMetadata(_messages.Message):
  r"""A BuildMetadata object.

  Fields:
    finishedOn: A string attribute.
    invocationId: A string attribute.
    startedOn: A string attribute.
  """

  finishedOn = _messages.StringField(1)
  invocationId = _messages.StringField(2)
  startedOn = _messages.StringField(3)


class BuildOccurrence(_messages.Message):
  r"""Details of a build occurrence.

  Fields:
    inTotoSlsaProvenanceV1: In-Toto Slsa Provenance V1 represents a slsa
      provenance meeting the slsa spec, wrapped in an in-toto statement. This
      allows for direct jsonification of a to-spec in-toto slsa statement with
      a to-spec slsa provenance.
    intotoProvenance: Deprecated. See InTotoStatement for the replacement. In-
      toto Provenance representation as defined in spec.
    intotoStatement: In-toto Statement representation as defined in spec. The
      intoto_statement can contain any type of provenance. The serialized
      payload of the statement can be stored and signed in the Occurrence's
      envelope.
    provenance: The actual provenance for the build.
    provenanceBytes: Serialized JSON representation of the provenance, used in
      generating the build signature in the corresponding build note. After
      verifying the signature, `provenance_bytes` can be unmarshalled and
      compared to the provenance to confirm that it is unchanged. A
      base64-encoded string representation of the provenance bytes is used for
      the signature in order to interoperate with openssl which expects this
      format for signature verification. The serialized form is captured both
      to avoid ambiguity in how the provenance is marshalled to json as well
      to prevent incompatibilities with future changes.
  """

  inTotoSlsaProvenanceV1 = _messages.MessageField('InTotoSlsaProvenanceV1', 1)
  intotoProvenance = _messages.MessageField('InTotoProvenance', 2)
  intotoStatement = _messages.MessageField('InTotoStatement', 3)
  provenance = _messages.MessageField('BuildProvenance', 4)
  provenanceBytes = _messages.StringField(5)


class BuildProvenance(_messages.Message):
  r"""Provenance of a build. Contains all information needed to verify the
  full details about the build from source to completion.

  Messages:
    BuildOptionsValue: Special options applied to this build. This is a catch-
      all field where build providers can enter any desired additional
      details.

  Fields:
    buildOptions: Special options applied to this build. This is a catch-all
      field where build providers can enter any desired additional details.
    builderVersion: Version string of the builder at the time this build was
      executed.
    builtArtifacts: Output of the build.
    commands: Commands requested by the build.
    createTime: Time at which the build was created.
    creator: E-mail address of the user who initiated this build. Note that
      this was the user's e-mail address at the time the build was initiated;
      this address may not represent the same end-user for all time.
    endTime: Time at which execution of the build was finished.
    id: Required. Unique identifier of the build.
    logsUri: URI where any logs for this provenance were written.
    projectId: ID of the project.
    sourceProvenance: Details of the Source input to the build.
    startTime: Time at which execution of the build was started.
    triggerId: Trigger identifier if the build was triggered automatically;
      empty if not.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class BuildOptionsValue(_messages.Message):
    r"""Special options applied to this build. This is a catch-all field where
    build providers can enter any desired additional details.

    Messages:
      AdditionalProperty: An additional property for a BuildOptionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type BuildOptionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a BuildOptionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  buildOptions = _messages.MessageField('BuildOptionsValue', 1)
  builderVersion = _messages.StringField(2)
  builtArtifacts = _messages.MessageField('Artifact', 3, repeated=True)
  commands = _messages.MessageField('Command', 4, repeated=True)
  createTime = _messages.StringField(5)
  creator = _messages.StringField(6)
  endTime = _messages.StringField(7)
  id = _messages.StringField(8)
  logsUri = _messages.StringField(9)
  projectId = _messages.StringField(10)
  sourceProvenance = _messages.MessageField('Source', 11)
  startTime = _messages.StringField(12)
  triggerId = _messages.StringField(13)


class BuilderConfig(_messages.Message):
  r"""A BuilderConfig object.

  Fields:
    id: A string attribute.
  """

  id = _messages.StringField(1)


class CVSS(_messages.Message):
  r"""Common Vulnerability Scoring System. For details, see
  https://www.first.org/cvss/specification-document This is a message we will
  try to use for storing various versions of CVSS rather than making a
  separate proto for storing a specific version.

  Enums:
    AttackComplexityValueValuesEnum:
    AttackVectorValueValuesEnum: Base Metrics Represents the intrinsic
      characteristics of a vulnerability that are constant over time and
      across user environments.
    AuthenticationValueValuesEnum:
    AvailabilityImpactValueValuesEnum:
    ConfidentialityImpactValueValuesEnum:
    IntegrityImpactValueValuesEnum:
    PrivilegesRequiredValueValuesEnum:
    ScopeValueValuesEnum:
    UserInteractionValueValuesEnum:

  Fields:
    attackComplexity: A AttackComplexityValueValuesEnum attribute.
    attackVector: Base Metrics Represents the intrinsic characteristics of a
      vulnerability that are constant over time and across user environments.
    authentication: A AuthenticationValueValuesEnum attribute.
    availabilityImpact: A AvailabilityImpactValueValuesEnum attribute.
    baseScore: The base score is a function of the base metric scores.
    confidentialityImpact: A ConfidentialityImpactValueValuesEnum attribute.
    exploitabilityScore: A number attribute.
    impactScore: A number attribute.
    integrityImpact: A IntegrityImpactValueValuesEnum attribute.
    privilegesRequired: A PrivilegesRequiredValueValuesEnum attribute.
    scope: A ScopeValueValuesEnum attribute.
    userInteraction: A UserInteractionValueValuesEnum attribute.
  """

  class AttackComplexityValueValuesEnum(_messages.Enum):
    r"""AttackComplexityValueValuesEnum enum type.

    Values:
      ATTACK_COMPLEXITY_UNSPECIFIED: <no description>
      ATTACK_COMPLEXITY_LOW: <no description>
      ATTACK_COMPLEXITY_HIGH: <no description>
      ATTACK_COMPLEXITY_MEDIUM: <no description>
    """
    ATTACK_COMPLEXITY_UNSPECIFIED = 0
    ATTACK_COMPLEXITY_LOW = 1
    ATTACK_COMPLEXITY_HIGH = 2
    ATTACK_COMPLEXITY_MEDIUM = 3

  class AttackVectorValueValuesEnum(_messages.Enum):
    r"""Base Metrics Represents the intrinsic characteristics of a
    vulnerability that are constant over time and across user environments.

    Values:
      ATTACK_VECTOR_UNSPECIFIED: <no description>
      ATTACK_VECTOR_NETWORK: <no description>
      ATTACK_VECTOR_ADJACENT: <no description>
      ATTACK_VECTOR_LOCAL: <no description>
      ATTACK_VECTOR_PHYSICAL: <no description>
    """
    ATTACK_VECTOR_UNSPECIFIED = 0
    ATTACK_VECTOR_NETWORK = 1
    ATTACK_VECTOR_ADJACENT = 2
    ATTACK_VECTOR_LOCAL = 3
    ATTACK_VECTOR_PHYSICAL = 4

  class AuthenticationValueValuesEnum(_messages.Enum):
    r"""AuthenticationValueValuesEnum enum type.

    Values:
      AUTHENTICATION_UNSPECIFIED: <no description>
      AUTHENTICATION_MULTIPLE: <no description>
      AUTHENTICATION_SINGLE: <no description>
      AUTHENTICATION_NONE: <no description>
    """
    AUTHENTICATION_UNSPECIFIED = 0
    AUTHENTICATION_MULTIPLE = 1
    AUTHENTICATION_SINGLE = 2
    AUTHENTICATION_NONE = 3

  class AvailabilityImpactValueValuesEnum(_messages.Enum):
    r"""AvailabilityImpactValueValuesEnum enum type.

    Values:
      IMPACT_UNSPECIFIED: <no description>
      IMPACT_HIGH: <no description>
      IMPACT_LOW: <no description>
      IMPACT_NONE: <no description>
      IMPACT_PARTIAL: <no description>
      IMPACT_COMPLETE: <no description>
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3
    IMPACT_PARTIAL = 4
    IMPACT_COMPLETE = 5

  class ConfidentialityImpactValueValuesEnum(_messages.Enum):
    r"""ConfidentialityImpactValueValuesEnum enum type.

    Values:
      IMPACT_UNSPECIFIED: <no description>
      IMPACT_HIGH: <no description>
      IMPACT_LOW: <no description>
      IMPACT_NONE: <no description>
      IMPACT_PARTIAL: <no description>
      IMPACT_COMPLETE: <no description>
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3
    IMPACT_PARTIAL = 4
    IMPACT_COMPLETE = 5

  class IntegrityImpactValueValuesEnum(_messages.Enum):
    r"""IntegrityImpactValueValuesEnum enum type.

    Values:
      IMPACT_UNSPECIFIED: <no description>
      IMPACT_HIGH: <no description>
      IMPACT_LOW: <no description>
      IMPACT_NONE: <no description>
      IMPACT_PARTIAL: <no description>
      IMPACT_COMPLETE: <no description>
    """
    IMPACT_UNSPECIFIED = 0
    IMPACT_HIGH = 1
    IMPACT_LOW = 2
    IMPACT_NONE = 3
    IMPACT_PARTIAL = 4
    IMPACT_COMPLETE = 5

  class PrivilegesRequiredValueValuesEnum(_messages.Enum):
    r"""PrivilegesRequiredValueValuesEnum enum type.

    Values:
      PRIVILEGES_REQUIRED_UNSPECIFIED: <no description>
      PRIVILEGES_REQUIRED_NONE: <no description>
      PRIVILEGES_REQUIRED_LOW: <no description>
      PRIVILEGES_REQUIRED_HIGH: <no description>
    """
    PRIVILEGES_REQUIRED_UNSPECIFIED = 0
    PRIVILEGES_REQUIRED_NONE = 1
    PRIVILEGES_REQUIRED_LOW = 2
    PRIVILEGES_REQUIRED_HIGH = 3

  class ScopeValueValuesEnum(_messages.Enum):
    r"""ScopeValueValuesEnum enum type.

    Values:
      SCOPE_UNSPECIFIED: <no description>
      SCOPE_UNCHANGED: <no description>
      SCOPE_CHANGED: <no description>
    """
    SCOPE_UNSPECIFIED = 0
    SCOPE_UNCHANGED = 1
    SCOPE_CHANGED = 2

  class UserInteractionValueValuesEnum(_messages.Enum):
    r"""UserInteractionValueValuesEnum enum type.

    Values:
      USER_INTERACTION_UNSPECIFIED: <no description>
      USER_INTERACTION_NONE: <no description>
      USER_INTERACTION_REQUIRED: <no description>
    """
    USER_INTERACTION_UNSPECIFIED = 0
    USER_INTERACTION_NONE = 1
    USER_INTERACTION_REQUIRED = 2

  attackComplexity = _messages.EnumField('AttackComplexityValueValuesEnum', 1)
  attackVector = _messages.EnumField('AttackVectorValueValuesEnum', 2)
  authentication = _messages.EnumField('AuthenticationValueValuesEnum', 3)
  availabilityImpact = _messages.EnumField('AvailabilityImpactValueValuesEnum', 4)
  baseScore = _messages.FloatField(5, variant=_messages.Variant.FLOAT)
  confidentialityImpact = _messages.EnumField('ConfidentialityImpactValueValuesEnum', 6)
  exploitabilityScore = _messages.FloatField(7, variant=_messages.Variant.FLOAT)
  impactScore = _messages.FloatField(8, variant=_messages.Variant.FLOAT)
  integrityImpact = _messages.EnumField('IntegrityImpactValueValuesEnum', 9)
  privilegesRequired = _messages.EnumField('PrivilegesRequiredValueValuesEnum', 10)
  scope = _messages.EnumField('ScopeValueValuesEnum', 11)
  userInteraction = _messages.EnumField('UserInteractionValueValuesEnum', 12)


class Category(_messages.Message):
  r"""The category to which the update belongs.

  Fields:
    categoryId: The identifier of the category.
    name: The localized name of the category.
  """

  categoryId = _messages.StringField(1)
  name = _messages.StringField(2)


class CloudRepoSourceContext(_messages.Message):
  r"""A CloudRepoSourceContext denotes a particular revision in a Google Cloud
  Source Repo.

  Fields:
    aliasContext: An alias, which may be a branch or tag.
    repoId: The ID of the repo.
    revisionId: A revision ID.
  """

  aliasContext = _messages.MessageField('AliasContext', 1)
  repoId = _messages.MessageField('RepoId', 2)
  revisionId = _messages.StringField(3)


class Command(_messages.Message):
  r"""Command describes a step performed as part of the build pipeline.

  Fields:
    args: Command-line arguments used when executing this command.
    dir: Working directory (relative to project source root) used when running
      this command.
    env: Environment variables set before running this command.
    id: Optional unique identifier for this command, used in wait_for to
      reference this command as a dependency.
    name: Required. Name of the command, as presented on the command line, or
      if the command is packaged as a Docker container, as presented to
      `docker pull`.
    waitFor: The ID(s) of the command(s) that this command depends on.
  """

  args = _messages.StringField(1, repeated=True)
  dir = _messages.StringField(2)
  env = _messages.StringField(3, repeated=True)
  id = _messages.StringField(4)
  name = _messages.StringField(5)
  waitFor = _messages.StringField(6, repeated=True)


class Completeness(_messages.Message):
  r"""Indicates that the builder claims certain fields in this message to be
  complete.

  Fields:
    arguments: If true, the builder claims that recipe.arguments is complete,
      meaning that all external inputs are properly captured in the recipe.
    environment: If true, the builder claims that recipe.environment is
      claimed to be complete.
    materials: If true, the builder claims that materials are complete,
      usually through some controls to prevent network access. Sometimes
      called "hermetic".
  """

  arguments = _messages.BooleanField(1)
  environment = _messages.BooleanField(2)
  materials = _messages.BooleanField(3)


class ComplianceOccurrence(_messages.Message):
  r"""An indication that the compliance checks in the associated
  ComplianceNote were not satisfied for particular resources or a specified
  reason.

  Fields:
    nonComplianceReason: A string attribute.
    nonCompliantFiles: A NonCompliantFile attribute.
  """

  nonComplianceReason = _messages.StringField(1)
  nonCompliantFiles = _messages.MessageField('NonCompliantFile', 2, repeated=True)


class DSSEAttestationOccurrence(_messages.Message):
  r"""Deprecated. Prefer to use a regular Occurrence, and populate the
  Envelope at the top level of the Occurrence.

  Fields:
    envelope: If doing something security critical, make sure to verify the
      signatures in this metadata.
    statement: A InTotoStatement attribute.
  """

  envelope = _messages.MessageField('Envelope', 1)
  statement = _messages.MessageField('InTotoStatement', 2)


class DeploymentOccurrence(_messages.Message):
  r"""The period during which some deployable was active in a runtime.

  Enums:
    PlatformValueValuesEnum: Platform hosting this deployment.

  Fields:
    address: Address of the runtime element hosting this deployment.
    config: Configuration used to create this deployment.
    deployTime: Required. Beginning of the lifetime of this deployment.
    platform: Platform hosting this deployment.
    resourceUri: Output only. Resource URI for the artifact being deployed
      taken from the deployable field with the same name.
    undeployTime: End of the lifetime of this deployment.
    userEmail: Identity of the user that triggered this deployment.
  """

  class PlatformValueValuesEnum(_messages.Enum):
    r"""Platform hosting this deployment.

    Values:
      PLATFORM_UNSPECIFIED: Unknown.
      GKE: Google Container Engine.
      FLEX: Google App Engine: Flexible Environment.
      CUSTOM: Custom user-defined platform.
    """
    PLATFORM_UNSPECIFIED = 0
    GKE = 1
    FLEX = 2
    CUSTOM = 3

  address = _messages.StringField(1)
  config = _messages.StringField(2)
  deployTime = _messages.StringField(3)
  platform = _messages.EnumField('PlatformValueValuesEnum', 4)
  resourceUri = _messages.StringField(5, repeated=True)
  undeployTime = _messages.StringField(6)
  userEmail = _messages.StringField(7)


class DiscoveryOccurrence(_messages.Message):
  r"""Provides information about the analysis status of a discovered resource.

  Enums:
    AnalysisStatusValueValuesEnum: The status of discovery for the resource.
    ContinuousAnalysisValueValuesEnum: Whether the resource is continuously
      analyzed.

  Fields:
    analysisCompleted: A AnalysisCompleted attribute.
    analysisError: Indicates any errors encountered during analysis of a
      resource. There could be 0 or more of these errors.
    analysisStatus: The status of discovery for the resource.
    analysisStatusError: When an error is encountered this will contain a
      LocalizedMessage under details to show to the user. The LocalizedMessage
      is output only and populated by the API.
    archiveTime: Output only. The time occurrences related to this discovery
      occurrence were archived.
    continuousAnalysis: Whether the resource is continuously analyzed.
    cpe: The CPE of the resource being scanned.
    lastScanTime: The last time this resource was scanned.
    sbomStatus: The status of an SBOM generation.
  """

  class AnalysisStatusValueValuesEnum(_messages.Enum):
    r"""The status of discovery for the resource.

    Values:
      ANALYSIS_STATUS_UNSPECIFIED: Unknown.
      PENDING: Resource is known but no action has been taken yet.
      SCANNING: Resource is being analyzed.
      FINISHED_SUCCESS: Analysis has finished successfully.
      COMPLETE: Analysis has completed.
      FINISHED_FAILED: Analysis has finished unsuccessfully, the analysis
        itself is in a bad state.
      FINISHED_UNSUPPORTED: The resource is known not to be supported.
    """
    ANALYSIS_STATUS_UNSPECIFIED = 0
    PENDING = 1
    SCANNING = 2
    FINISHED_SUCCESS = 3
    COMPLETE = 4
    FINISHED_FAILED = 5
    FINISHED_UNSUPPORTED = 6

  class ContinuousAnalysisValueValuesEnum(_messages.Enum):
    r"""Whether the resource is continuously analyzed.

    Values:
      CONTINUOUS_ANALYSIS_UNSPECIFIED: Unknown.
      ACTIVE: The resource is continuously analyzed.
      INACTIVE: The resource is ignored for continuous analysis.
    """
    CONTINUOUS_ANALYSIS_UNSPECIFIED = 0
    ACTIVE = 1
    INACTIVE = 2

  analysisCompleted = _messages.MessageField('AnalysisCompleted', 1)
  analysisError = _messages.MessageField('Status', 2, repeated=True)
  analysisStatus = _messages.EnumField('AnalysisStatusValueValuesEnum', 3)
  analysisStatusError = _messages.MessageField('Status', 4)
  archiveTime = _messages.StringField(5)
  continuousAnalysis = _messages.EnumField('ContinuousAnalysisValueValuesEnum', 6)
  cpe = _messages.StringField(7)
  lastScanTime = _messages.StringField(8)
  sbomStatus = _messages.MessageField('SBOMStatus', 9)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Envelope(_messages.Message):
  r"""MUST match https://github.com/secure-systems-
  lab/dsse/blob/master/envelope.proto. An authenticated message of arbitrary
  type.

  Fields:
    payload: A byte attribute.
    payloadType: A string attribute.
    signatures: A EnvelopeSignature attribute.
  """

  payload = _messages.BytesField(1)
  payloadType = _messages.StringField(2)
  signatures = _messages.MessageField('EnvelopeSignature', 3, repeated=True)


class EnvelopeSignature(_messages.Message):
  r"""A EnvelopeSignature object.

  Fields:
    keyid: A string attribute.
    sig: A byte attribute.
  """

  keyid = _messages.StringField(1)
  sig = _messages.BytesField(2)


class FileHashes(_messages.Message):
  r"""Container message for hashes of byte content of files, used in source
  messages to verify integrity of source input to the build.

  Fields:
    fileHash: Required. Collection of file hashes.
  """

  fileHash = _messages.MessageField('Hash', 1, repeated=True)


class FileLocation(_messages.Message):
  r"""Indicates the location at which a package was found.

  Fields:
    filePath: For jars that are contained inside .war files, this filepath can
      indicate the path to war file combined with the path to jar file.
  """

  filePath = _messages.StringField(1)


class Fingerprint(_messages.Message):
  r"""A set of properties that uniquely identify a given Docker image.

  Fields:
    v1Name: Required. The layer ID of the final layer in the Docker image's v1
      representation.
    v2Blob: Required. The ordered list of v2 blobs that represent a given
      image.
    v2Name: Output only. The name of the image's v2 blobs computed via:
      [bottom] := v2_blobbottom := sha256(v2_blob[N] + " " + v2_name[N+1])
      Only the name of the final blob is kept.
  """

  v1Name = _messages.StringField(1)
  v2Blob = _messages.StringField(2, repeated=True)
  v2Name = _messages.StringField(3)


class GerritSourceContext(_messages.Message):
  r"""A SourceContext referring to a Gerrit project.

  Fields:
    aliasContext: An alias, which may be a branch or tag.
    gerritProject: The full project name within the host. Projects may be
      nested, so "project/subproject" is a valid project name. The "repo name"
      is the hostURI/project.
    hostUri: The URI of a running Gerrit instance.
    revisionId: A revision (commit) ID.
  """

  aliasContext = _messages.MessageField('AliasContext', 1)
  gerritProject = _messages.StringField(2)
  hostUri = _messages.StringField(3)
  revisionId = _messages.StringField(4)


class GitSourceContext(_messages.Message):
  r"""A GitSourceContext denotes a particular revision in a third party Git
  repository (e.g., GitHub).

  Fields:
    revisionId: Git commit hash.
    url: Git repository URL.
  """

  revisionId = _messages.StringField(1)
  url = _messages.StringField(2)


class GrafeasV1FileLocation(_messages.Message):
  r"""Indicates the location at which a package was found.

  Fields:
    filePath: For jars that are contained inside .war files, this filepath can
      indicate the path to war file combined with the path to jar file.
  """

  filePath = _messages.StringField(1)


class GrafeasV1SlsaProvenanceZeroTwoSlsaBuilder(_messages.Message):
  r"""Identifies the entity that executed the recipe, which is trusted to have
  correctly performed the operation and populated this provenance.

  Fields:
    id: A string attribute.
  """

  id = _messages.StringField(1)


class GrafeasV1SlsaProvenanceZeroTwoSlsaCompleteness(_messages.Message):
  r"""Indicates that the builder claims certain fields in this message to be
  complete.

  Fields:
    environment: A boolean attribute.
    materials: A boolean attribute.
    parameters: A boolean attribute.
  """

  environment = _messages.BooleanField(1)
  materials = _messages.BooleanField(2)
  parameters = _messages.BooleanField(3)


class GrafeasV1SlsaProvenanceZeroTwoSlsaConfigSource(_messages.Message):
  r"""Describes where the config file that kicked off the build came from.
  This is effectively a pointer to the source where buildConfig came from.

  Messages:
    DigestValue: A DigestValue object.

  Fields:
    digest: A DigestValue attribute.
    entryPoint: A string attribute.
    uri: A string attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DigestValue(_messages.Message):
    r"""A DigestValue object.

    Messages:
      AdditionalProperty: An additional property for a DigestValue object.

    Fields:
      additionalProperties: Additional properties of type DigestValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DigestValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  digest = _messages.MessageField('DigestValue', 1)
  entryPoint = _messages.StringField(2)
  uri = _messages.StringField(3)


class GrafeasV1SlsaProvenanceZeroTwoSlsaInvocation(_messages.Message):
  r"""Identifies the event that kicked off the build.

  Messages:
    EnvironmentValue: A EnvironmentValue object.
    ParametersValue: A ParametersValue object.

  Fields:
    configSource: A GrafeasV1SlsaProvenanceZeroTwoSlsaConfigSource attribute.
    environment: A EnvironmentValue attribute.
    parameters: A ParametersValue attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvironmentValue(_messages.Message):
    r"""A EnvironmentValue object.

    Messages:
      AdditionalProperty: An additional property for a EnvironmentValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvironmentValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ParametersValue(_messages.Message):
    r"""A ParametersValue object.

    Messages:
      AdditionalProperty: An additional property for a ParametersValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  configSource = _messages.MessageField('GrafeasV1SlsaProvenanceZeroTwoSlsaConfigSource', 1)
  environment = _messages.MessageField('EnvironmentValue', 2)
  parameters = _messages.MessageField('ParametersValue', 3)


class GrafeasV1SlsaProvenanceZeroTwoSlsaMaterial(_messages.Message):
  r"""The collection of artifacts that influenced the build including sources,
  dependencies, build tools, base images, and so on.

  Messages:
    DigestValue: A DigestValue object.

  Fields:
    digest: A DigestValue attribute.
    uri: A string attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DigestValue(_messages.Message):
    r"""A DigestValue object.

    Messages:
      AdditionalProperty: An additional property for a DigestValue object.

    Fields:
      additionalProperties: Additional properties of type DigestValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DigestValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  digest = _messages.MessageField('DigestValue', 1)
  uri = _messages.StringField(2)


class GrafeasV1SlsaProvenanceZeroTwoSlsaMetadata(_messages.Message):
  r"""Other properties of the build.

  Fields:
    buildFinishedOn: A string attribute.
    buildInvocationId: A string attribute.
    buildStartedOn: A string attribute.
    completeness: A GrafeasV1SlsaProvenanceZeroTwoSlsaCompleteness attribute.
    reproducible: A boolean attribute.
  """

  buildFinishedOn = _messages.StringField(1)
  buildInvocationId = _messages.StringField(2)
  buildStartedOn = _messages.StringField(3)
  completeness = _messages.MessageField('GrafeasV1SlsaProvenanceZeroTwoSlsaCompleteness', 4)
  reproducible = _messages.BooleanField(5)


class Hash(_messages.Message):
  r"""Container message for hash values.

  Fields:
    type: Required. The type of hash that was performed, e.g. "SHA-256".
    value: Required. The hash value.
  """

  type = _messages.StringField(1)
  value = _messages.BytesField(2)


class Identity(_messages.Message):
  r"""The unique identifier of the update.

  Fields:
    revision: The revision number of the update.
    updateId: The revision independent identifier of the update.
  """

  revision = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  updateId = _messages.StringField(2)


class ImageOccurrence(_messages.Message):
  r"""Details of the derived image portion of the DockerImage relationship.
  This image would be produced from a Dockerfile with FROM .

  Fields:
    baseResourceUrl: Output only. This contains the base image URL for the
      derived image occurrence.
    distance: Output only. The number of layers by which this image differs
      from the associated image basis.
    fingerprint: Required. The fingerprint of the derived image.
    layerInfo: This contains layer-specific metadata, if populated it has
      length "distance" and is ordered with [distance] being the layer
      immediately following the base image and [1] being the final layer.
  """

  baseResourceUrl = _messages.StringField(1)
  distance = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  fingerprint = _messages.MessageField('Fingerprint', 3)
  layerInfo = _messages.MessageField('Layer', 4, repeated=True)


class InTotoProvenance(_messages.Message):
  r"""A InTotoProvenance object.

  Fields:
    builderConfig: required
    materials: The collection of artifacts that influenced the build including
      sources, dependencies, build tools, base images, and so on. This is
      considered to be incomplete unless metadata.completeness.materials is
      true. Unset or null is equivalent to empty.
    metadata: A Metadata attribute.
    recipe: Identifies the configuration used for the build. When combined
      with materials, this SHOULD fully describe the build, such that re-
      running this recipe results in bit-for-bit identical output (if the
      build is reproducible). required
  """

  builderConfig = _messages.MessageField('BuilderConfig', 1)
  materials = _messages.StringField(2, repeated=True)
  metadata = _messages.MessageField('Metadata', 3)
  recipe = _messages.MessageField('Recipe', 4)


class InTotoSlsaProvenanceV1(_messages.Message):
  r"""A InTotoSlsaProvenanceV1 object.

  Fields:
    _type: InToto spec defined at https://github.com/in-
      toto/attestation/tree/main/spec#statement
    predicate: A SlsaProvenanceV1 attribute.
    predicateType: A string attribute.
    subject: A Subject attribute.
  """

  _type = _messages.StringField(1)
  predicate = _messages.MessageField('SlsaProvenanceV1', 2)
  predicateType = _messages.StringField(3)
  subject = _messages.MessageField('Subject', 4, repeated=True)


class InTotoStatement(_messages.Message):
  r"""Spec defined at https://github.com/in-
  toto/attestation/tree/main/spec#statement The serialized InTotoStatement
  will be stored as Envelope.payload. Envelope.payloadType is always
  "application/vnd.in-toto+json".

  Fields:
    _type: Always `https://in-toto.io/Statement/v0.1`.
    predicateType: `https://slsa.dev/provenance/v0.1` for SlsaProvenance.
    provenance: A InTotoProvenance attribute.
    slsaProvenance: A SlsaProvenance attribute.
    slsaProvenanceZeroTwo: A SlsaProvenanceZeroTwo attribute.
    subject: A Subject attribute.
  """

  _type = _messages.StringField(1)
  predicateType = _messages.StringField(2)
  provenance = _messages.MessageField('InTotoProvenance', 3)
  slsaProvenance = _messages.MessageField('SlsaProvenance', 4)
  slsaProvenanceZeroTwo = _messages.MessageField('SlsaProvenanceZeroTwo', 5)
  subject = _messages.MessageField('Subject', 6, repeated=True)


class Justification(_messages.Message):
  r"""Justification provides the justification when the state of the
  assessment if NOT_AFFECTED.

  Enums:
    JustificationTypeValueValuesEnum: The justification type for this
      vulnerability.

  Fields:
    details: Additional details on why this justification was chosen.
    justificationType: The justification type for this vulnerability.
  """

  class JustificationTypeValueValuesEnum(_messages.Enum):
    r"""The justification type for this vulnerability.

    Values:
      JUSTIFICATION_TYPE_UNSPECIFIED: JUSTIFICATION_TYPE_UNSPECIFIED.
      COMPONENT_NOT_PRESENT: The vulnerable component is not present in the
        product.
      VULNERABLE_CODE_NOT_PRESENT: The vulnerable code is not present.
        Typically this case occurs when source code is configured or built in
        a way that excludes the vulnerable code.
      VULNERABLE_CODE_NOT_IN_EXECUTE_PATH: The vulnerable code can not be
        executed. Typically this case occurs when the product includes the
        vulnerable code but does not call or use the vulnerable code.
      VULNERABLE_CODE_CANNOT_BE_CONTROLLED_BY_ADVERSARY: The vulnerable code
        cannot be controlled by an attacker to exploit the vulnerability.
      INLINE_MITIGATIONS_ALREADY_EXIST: The product includes built-in
        protections or features that prevent exploitation of the
        vulnerability. These built-in protections cannot be subverted by the
        attacker and cannot be configured or disabled by the user. These
        mitigations completely prevent exploitation based on known attack
        vectors.
    """
    JUSTIFICATION_TYPE_UNSPECIFIED = 0
    COMPONENT_NOT_PRESENT = 1
    VULNERABLE_CODE_NOT_PRESENT = 2
    VULNERABLE_CODE_NOT_IN_EXECUTE_PATH = 3
    VULNERABLE_CODE_CANNOT_BE_CONTROLLED_BY_ADVERSARY = 4
    INLINE_MITIGATIONS_ALREADY_EXIST = 5

  details = _messages.StringField(1)
  justificationType = _messages.EnumField('JustificationTypeValueValuesEnum', 2)


class Jwt(_messages.Message):
  r"""A Jwt object.

  Fields:
    compactJwt: The compact encoding of a JWS, which is always three base64
      encoded strings joined by periods. For details, see:
      https://tools.ietf.org/html/rfc7515.html#section-3.1
  """

  compactJwt = _messages.StringField(1)


class LanguagePackageDependency(_messages.Message):
  r"""Indicates a language package available between this package and the
  customer's resource artifact.

  Fields:
    package: A string attribute.
    version: A string attribute.
  """

  package = _messages.StringField(1)
  version = _messages.StringField(2)


class Layer(_messages.Message):
  r"""Layer holds metadata specific to a layer of a Docker image.

  Fields:
    arguments: The recovered arguments to the Dockerfile directive.
    directive: Required. The recovered Dockerfile directive used to construct
      this layer. See https://docs.docker.com/engine/reference/builder/ for
      more information.
  """

  arguments = _messages.StringField(1)
  directive = _messages.StringField(2)


class License(_messages.Message):
  r"""License information.

  Fields:
    comments: Comments
    expression: Often a single license can be used to represent the licensing
      terms. Sometimes it is necessary to include a choice of one or more
      licenses or some combination of license identifiers. Examples:
      "LGPL-2.1-only OR MIT", "LGPL-2.1-only AND MIT", "GPL-2.0-or-later WITH
      Bison-exception-2.2".
  """

  comments = _messages.StringField(1)
  expression = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListVulnerabilitiesResponse(_messages.Message):
  r"""ListVulnerabilitiesResponse contains a single page of vulnerabilities
  resulting from a scan.

  Fields:
    nextPageToken: A page token that can be used in a subsequent call to
      ListVulnerabilities to continue retrieving results.
    occurrences: The list of Vulnerability Occurrences resulting from a scan.
  """

  nextPageToken = _messages.StringField(1)
  occurrences = _messages.MessageField('Occurrence', 2, repeated=True)


class Location(_messages.Message):
  r"""An occurrence of a particular package installation found within a
  system's filesystem. E.g., glibc was found in `/var/lib/dpkg/status`.

  Fields:
    cpeUri: Deprecated. The CPE URI in [CPE
      format](https://cpe.mitre.org/specification/)
    path: The path from which we gathered that this package/version is
      installed.
    version: Deprecated. The version installed at this location.
  """

  cpeUri = _messages.StringField(1)
  path = _messages.StringField(2)
  version = _messages.MessageField('Version', 3)


class Maintainer(_messages.Message):
  r"""A Maintainer object.

  Fields:
    kind: A string attribute.
    name: A string attribute.
  """

  kind = _messages.StringField(1)
  name = _messages.StringField(2)


class Material(_messages.Message):
  r"""A Material object.

  Messages:
    DigestValue: A DigestValue object.

  Fields:
    digest: A DigestValue attribute.
    uri: A string attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DigestValue(_messages.Message):
    r"""A DigestValue object.

    Messages:
      AdditionalProperty: An additional property for a DigestValue object.

    Fields:
      additionalProperties: Additional properties of type DigestValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DigestValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  digest = _messages.MessageField('DigestValue', 1)
  uri = _messages.StringField(2)


class Metadata(_messages.Message):
  r"""Other properties of the build.

  Fields:
    buildFinishedOn: The timestamp of when the build completed.
    buildInvocationId: Identifies the particular build invocation, which can
      be useful for finding associated logs or other ad-hoc analysis. The
      value SHOULD be globally unique, per in-toto Provenance spec.
    buildStartedOn: The timestamp of when the build started.
    completeness: Indicates that the builder claims certain fields in this
      message to be complete.
    reproducible: If true, the builder claims that running the recipe on
      materials will produce bit-for-bit identical output.
  """

  buildFinishedOn = _messages.StringField(1)
  buildInvocationId = _messages.StringField(2)
  buildStartedOn = _messages.StringField(3)
  completeness = _messages.MessageField('Completeness', 4)
  reproducible = _messages.BooleanField(5)


class NonCompliantFile(_messages.Message):
  r"""Details about files that caused a compliance check to fail.
  display_command is a single command that can be used to display a list of
  non compliant files. When there is no such command, we can also iterate a
  list of non compliant file using 'path'.

  Fields:
    displayCommand: Command to display the non-compliant files.
    path: Empty if `display_command` is set.
    reason: Explains why a file is non compliant for a CIS check.
  """

  displayCommand = _messages.StringField(1)
  path = _messages.StringField(2)
  reason = _messages.StringField(3)


class Occurrence(_messages.Message):
  r"""An instance of an analysis type that has been found on a resource.

  Enums:
    KindValueValuesEnum: Output only. This explicitly denotes which of the
      occurrence details are specified. This field can be used as a filter in
      list requests.

  Fields:
    attestation: Describes an attestation of an artifact.
    build: Describes a verifiable build.
    compliance: Describes a compliance violation on a linked resource.
    createTime: Output only. The time this occurrence was created.
    deployment: Describes the deployment of an artifact on a runtime.
    discovery: Describes when a resource was discovered.
    dsseAttestation: Describes an attestation of an artifact using dsse.
    envelope: https://github.com/secure-systems-lab/dsse
    image: Describes how this resource derives from the basis in the
      associated note.
    kind: Output only. This explicitly denotes which of the occurrence details
      are specified. This field can be used as a filter in list requests.
    name: Output only. The name of the occurrence in the form of
      `projects/[PROJECT_ID]/occurrences/[OCCURRENCE_ID]`.
    noteName: Required. Immutable. The analysis note associated with this
      occurrence, in the form of `projects/[PROVIDER_ID]/notes/[NOTE_ID]`.
      This field can be used as a filter in list requests.
    package: Describes the installation of a package on the linked resource.
    remediation: A description of actions that can be taken to remedy the
      note.
    resourceUri: Required. Immutable. A URI that represents the resource for
      which the occurrence applies. For example,
      `https://gcr.io/project/image@sha256:123abc` for a Docker image.
    sbomReference: Describes a specific SBOM reference occurrences.
    updateTime: Output only. The time this occurrence was last updated.
    upgrade: Describes an available package upgrade on the linked resource.
    vulnerability: Describes a security vulnerability.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""Output only. This explicitly denotes which of the occurrence details
    are specified. This field can be used as a filter in list requests.

    Values:
      NOTE_KIND_UNSPECIFIED: Default value. This value is unused.
      VULNERABILITY: The note and occurrence represent a package
        vulnerability.
      BUILD: The note and occurrence assert build provenance.
      IMAGE: This represents an image basis relationship.
      PACKAGE: This represents a package installed via a package manager.
      DEPLOYMENT: The note and occurrence track deployment events.
      DISCOVERY: The note and occurrence track the initial discovery status of
        a resource.
      ATTESTATION: This represents a logical "role" that can attest to
        artifacts.
      UPGRADE: This represents an available package upgrade.
      COMPLIANCE: This represents a Compliance Note
      DSSE_ATTESTATION: This represents a DSSE attestation Note
      VULNERABILITY_ASSESSMENT: This represents a Vulnerability Assessment.
      SBOM_REFERENCE: This represents an SBOM Reference.
    """
    NOTE_KIND_UNSPECIFIED = 0
    VULNERABILITY = 1
    BUILD = 2
    IMAGE = 3
    PACKAGE = 4
    DEPLOYMENT = 5
    DISCOVERY = 6
    ATTESTATION = 7
    UPGRADE = 8
    COMPLIANCE = 9
    DSSE_ATTESTATION = 10
    VULNERABILITY_ASSESSMENT = 11
    SBOM_REFERENCE = 12

  attestation = _messages.MessageField('AttestationOccurrence', 1)
  build = _messages.MessageField('BuildOccurrence', 2)
  compliance = _messages.MessageField('ComplianceOccurrence', 3)
  createTime = _messages.StringField(4)
  deployment = _messages.MessageField('DeploymentOccurrence', 5)
  discovery = _messages.MessageField('DiscoveryOccurrence', 6)
  dsseAttestation = _messages.MessageField('DSSEAttestationOccurrence', 7)
  envelope = _messages.MessageField('Envelope', 8)
  image = _messages.MessageField('ImageOccurrence', 9)
  kind = _messages.EnumField('KindValueValuesEnum', 10)
  name = _messages.StringField(11)
  noteName = _messages.StringField(12)
  package = _messages.MessageField('PackageOccurrence', 13)
  remediation = _messages.StringField(14)
  resourceUri = _messages.StringField(15)
  sbomReference = _messages.MessageField('SBOMReferenceOccurrence', 16)
  updateTime = _messages.StringField(17)
  upgrade = _messages.MessageField('UpgradeOccurrence', 18)
  vulnerability = _messages.MessageField('VulnerabilityOccurrence', 19)


class OndemandscanningProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A OndemandscanningProjectsLocationsOperationsCancelRequest object.

  Fields:
    name: The name of the operation resource to be cancelled.
  """

  name = _messages.StringField(1, required=True)


class OndemandscanningProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A OndemandscanningProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class OndemandscanningProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A OndemandscanningProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class OndemandscanningProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A OndemandscanningProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class OndemandscanningProjectsLocationsOperationsWaitRequest(_messages.Message):
  r"""A OndemandscanningProjectsLocationsOperationsWaitRequest object.

  Fields:
    name: The name of the operation resource to wait on.
    timeout: The maximum duration to wait before timing out. If left blank,
      the wait will be at most the time permitted by the underlying HTTP/RPC
      protocol. If RPC context deadline is also specified, the shorter one
      will be used.
  """

  name = _messages.StringField(1, required=True)
  timeout = _messages.StringField(2)


class OndemandscanningProjectsLocationsScansAnalyzePackagesRequest(_messages.Message):
  r"""A OndemandscanningProjectsLocationsScansAnalyzePackagesRequest object.

  Fields:
    analyzePackagesRequest: A AnalyzePackagesRequest resource to be passed as
      the request body.
    parent: Required. The parent of the resource for which analysis is
      requested. Format: projects/[project_name]/locations/[location]
  """

  analyzePackagesRequest = _messages.MessageField('AnalyzePackagesRequest', 1)
  parent = _messages.StringField(2, required=True)


class OndemandscanningProjectsLocationsScansVulnerabilitiesListRequest(_messages.Message):
  r"""A OndemandscanningProjectsLocationsScansVulnerabilitiesListRequest
  object.

  Fields:
    pageSize: The number of vulnerabilities to retrieve.
    pageToken: The page token, resulting from a previous call to
      ListVulnerabilities.
    parent: Required. The parent of the collection of Vulnerabilities being
      requested. Format:
      projects/[project_name]/locations/[location]/scans/[scan_id]
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class PackageData(_messages.Message):
  r"""A PackageData object.

  Enums:
    PackageTypeValueValuesEnum: The type of package: os, maven, go, etc.

  Fields:
    architecture: The architecture of the package.
    binarySourceInfo: A bundle containing the binary and source information.
    binaryVersion: DEPRECATED
    cpeUri: The cpe_uri in [cpe format] (https://cpe.mitre.org/specification/)
      in which the vulnerability may manifest. Examples include distro or
      storage location for vulnerable jar.
    dependencyChain: The dependency chain between this package and the user's
      artifact. List in order from the customer's package under review first,
      to the current package last. Inclusive of the original package and the
      current package.
    fileLocation: The path to the jar file / go binary file.
    hashDigest: HashDigest stores the SHA512 hash digest of the jar file if
      the package is of type Maven. This field will be unset for non Maven
      packages.
    maintainer: The maintainer of the package.
    os: The OS affected by a vulnerability Used to generate the cpe_uri for OS
      packages
    osVersion: The version of the OS Used to generate the cpe_uri for OS
      packages
    package: The package being analysed for vulnerabilities
    packageType: The type of package: os, maven, go, etc.
    patchedCve: CVEs that this package is no longer vulnerable to go/drydock-
      dd-custom-binary-scanning
    sourceVersion: DEPRECATED
    unused: A string attribute.
    version: The version of the package being analysed
  """

  class PackageTypeValueValuesEnum(_messages.Enum):
    r"""The type of package: os, maven, go, etc.

    Values:
      PACKAGE_TYPE_UNSPECIFIED: <no description>
      OS: Operating System
      MAVEN: Java packages from Maven.
      GO: Go third-party packages.
      GO_STDLIB: Go toolchain + standard library packages.
      PYPI: Python packages.
      NPM: NPM packages.
    """
    PACKAGE_TYPE_UNSPECIFIED = 0
    OS = 1
    MAVEN = 2
    GO = 3
    GO_STDLIB = 4
    PYPI = 5
    NPM = 6

  architecture = _messages.StringField(1)
  binarySourceInfo = _messages.MessageField('BinarySourceInfo', 2, repeated=True)
  binaryVersion = _messages.MessageField('PackageVersion', 3)
  cpeUri = _messages.StringField(4)
  dependencyChain = _messages.MessageField('LanguagePackageDependency', 5, repeated=True)
  fileLocation = _messages.MessageField('FileLocation', 6, repeated=True)
  hashDigest = _messages.StringField(7)
  maintainer = _messages.MessageField('Maintainer', 8)
  os = _messages.StringField(9)
  osVersion = _messages.StringField(10)
  package = _messages.StringField(11)
  packageType = _messages.EnumField('PackageTypeValueValuesEnum', 12)
  patchedCve = _messages.StringField(13, repeated=True)
  sourceVersion = _messages.MessageField('PackageVersion', 14)
  unused = _messages.StringField(15)
  version = _messages.StringField(16)


class PackageIssue(_messages.Message):
  r"""A detail for a distro and package this vulnerability occurrence was
  found in and its associated fix (if one is available).

  Enums:
    EffectiveSeverityValueValuesEnum: Output only. The distro or language
      system assigned severity for this vulnerability when that is available
      and note provider assigned severity when it is not available.

  Fields:
    affectedCpeUri: Required. The [CPE
      URI](https://cpe.mitre.org/specification/) this vulnerability was found
      in.
    affectedPackage: Required. The package this vulnerability was found in.
    affectedVersion: Required. The version of the package that is installed on
      the resource affected by this vulnerability.
    effectiveSeverity: Output only. The distro or language system assigned
      severity for this vulnerability when that is available and note provider
      assigned severity when it is not available.
    fileLocation: The location at which this package was found.
    fixAvailable: Output only. Whether a fix is available for this package.
    fixedCpeUri: The [CPE URI](https://cpe.mitre.org/specification/) this
      vulnerability was fixed in. It is possible for this to be different from
      the affected_cpe_uri.
    fixedPackage: The package this vulnerability was fixed in. It is possible
      for this to be different from the affected_package.
    fixedVersion: Required. The version of the package this vulnerability was
      fixed in. Setting this to VersionKind.MAXIMUM means no fix is yet
      available.
    packageType: The type of package (e.g. OS, MAVEN, GO).
  """

  class EffectiveSeverityValueValuesEnum(_messages.Enum):
    r"""Output only. The distro or language system assigned severity for this
    vulnerability when that is available and note provider assigned severity
    when it is not available.

    Values:
      SEVERITY_UNSPECIFIED: Unknown.
      MINIMAL: Minimal severity.
      LOW: Low severity.
      MEDIUM: Medium severity.
      HIGH: High severity.
      CRITICAL: Critical severity.
    """
    SEVERITY_UNSPECIFIED = 0
    MINIMAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5

  affectedCpeUri = _messages.StringField(1)
  affectedPackage = _messages.StringField(2)
  affectedVersion = _messages.MessageField('Version', 3)
  effectiveSeverity = _messages.EnumField('EffectiveSeverityValueValuesEnum', 4)
  fileLocation = _messages.MessageField('GrafeasV1FileLocation', 5, repeated=True)
  fixAvailable = _messages.BooleanField(6)
  fixedCpeUri = _messages.StringField(7)
  fixedPackage = _messages.StringField(8)
  fixedVersion = _messages.MessageField('Version', 9)
  packageType = _messages.StringField(10)


class PackageOccurrence(_messages.Message):
  r"""Details on how a particular software package was installed on a system.

  Enums:
    ArchitectureValueValuesEnum: Output only. The CPU architecture for which
      packages in this distribution channel were built. Architecture will be
      blank for language packages.

  Fields:
    architecture: Output only. The CPU architecture for which packages in this
      distribution channel were built. Architecture will be blank for language
      packages.
    cpeUri: Output only. The cpe_uri in [CPE
      format](https://cpe.mitre.org/specification/) denoting the package
      manager version distributing a package. The cpe_uri will be blank for
      language packages.
    license: Licenses that have been declared by the authors of the package.
    location: All of the places within the filesystem versions of this package
      have been found.
    name: Required. Output only. The name of the installed package.
    packageType: Output only. The type of package; whether native or non
      native (e.g., ruby gems, node.js packages, etc.).
    version: Output only. The version of the package.
  """

  class ArchitectureValueValuesEnum(_messages.Enum):
    r"""Output only. The CPU architecture for which packages in this
    distribution channel were built. Architecture will be blank for language
    packages.

    Values:
      ARCHITECTURE_UNSPECIFIED: Unknown architecture.
      X86: X86 architecture.
      X64: X64 architecture.
    """
    ARCHITECTURE_UNSPECIFIED = 0
    X86 = 1
    X64 = 2

  architecture = _messages.EnumField('ArchitectureValueValuesEnum', 1)
  cpeUri = _messages.StringField(2)
  license = _messages.MessageField('License', 3)
  location = _messages.MessageField('Location', 4, repeated=True)
  name = _messages.StringField(5)
  packageType = _messages.StringField(6)
  version = _messages.MessageField('Version', 7)


class PackageVersion(_messages.Message):
  r"""A PackageVersion object.

  Fields:
    name: A string attribute.
    version: A string attribute.
  """

  name = _messages.StringField(1)
  version = _messages.StringField(2)


class ProjectRepoId(_messages.Message):
  r"""Selects a repo using a Google Cloud Platform project ID (e.g., winged-
  cargo-31) and a repo name within that project.

  Fields:
    projectId: The ID of the project.
    repoName: The name of the repo. Leave empty for the default repo.
  """

  projectId = _messages.StringField(1)
  repoName = _messages.StringField(2)


class ProvenanceBuilder(_messages.Message):
  r"""A ProvenanceBuilder object.

  Messages:
    VersionValue: A VersionValue object.

  Fields:
    builderDependencies: A ResourceDescriptor attribute.
    id: A string attribute.
    version: A VersionValue attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class VersionValue(_messages.Message):
    r"""A VersionValue object.

    Messages:
      AdditionalProperty: An additional property for a VersionValue object.

    Fields:
      additionalProperties: Additional properties of type VersionValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a VersionValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  builderDependencies = _messages.MessageField('ResourceDescriptor', 1, repeated=True)
  id = _messages.StringField(2)
  version = _messages.MessageField('VersionValue', 3)


class Recipe(_messages.Message):
  r"""Steps taken to build the artifact. For a TaskRun, typically each
  container corresponds to one step in the recipe.

  Messages:
    ArgumentsValueListEntry: A ArgumentsValueListEntry object.
    EnvironmentValueListEntry: A EnvironmentValueListEntry object.

  Fields:
    arguments: Collection of all external inputs that influenced the build on
      top of recipe.definedInMaterial and recipe.entryPoint. For example, if
      the recipe type were "make", then this might be the flags passed to make
      aside from the target, which is captured in recipe.entryPoint. Since the
      arguments field can greatly vary in structure, depending on the builder
      and recipe type, this is of form "Any".
    definedInMaterial: Index in materials containing the recipe steps that are
      not implied by recipe.type. For example, if the recipe type were "make",
      then this would point to the source containing the Makefile, not the
      make program itself. Set to -1 if the recipe doesn't come from a
      material, as zero is default unset value for int64.
    entryPoint: String identifying the entry point into the build. This is
      often a path to a configuration file and/or a target label within that
      file. The syntax and meaning are defined by recipe.type. For example, if
      the recipe type were "make", then this would reference the directory in
      which to run make as well as which target to use.
    environment: Any other builder-controlled inputs necessary for correctly
      evaluating the recipe. Usually only needed for reproducing the build but
      not evaluated as part of policy. Since the environment field can greatly
      vary in structure, depending on the builder and recipe type, this is of
      form "Any".
    type: URI indicating what type of recipe was performed. It determines the
      meaning of recipe.entryPoint, recipe.arguments, recipe.environment, and
      materials.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ArgumentsValueListEntry(_messages.Message):
    r"""A ArgumentsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a ArgumentsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ArgumentsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvironmentValueListEntry(_messages.Message):
    r"""A EnvironmentValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a
        EnvironmentValueListEntry object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvironmentValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  arguments = _messages.MessageField('ArgumentsValueListEntry', 1, repeated=True)
  definedInMaterial = _messages.IntegerField(2)
  entryPoint = _messages.StringField(3)
  environment = _messages.MessageField('EnvironmentValueListEntry', 4, repeated=True)
  type = _messages.StringField(5)


class RelatedUrl(_messages.Message):
  r"""Metadata for any related URL information.

  Fields:
    label: Label to describe usage of the URL.
    url: Specific URL associated with the resource.
  """

  label = _messages.StringField(1)
  url = _messages.StringField(2)


class Remediation(_messages.Message):
  r"""Specifies details on how to handle (and presumably, fix) a
  vulnerability.

  Enums:
    RemediationTypeValueValuesEnum: The type of remediation that can be
      applied.

  Fields:
    details: Contains a comprehensive human-readable discussion of the
      remediation.
    remediationType: The type of remediation that can be applied.
    remediationUri: Contains the URL where to obtain the remediation.
  """

  class RemediationTypeValueValuesEnum(_messages.Enum):
    r"""The type of remediation that can be applied.

    Values:
      REMEDIATION_TYPE_UNSPECIFIED: No remediation type specified.
      MITIGATION: A MITIGATION is available.
      NO_FIX_PLANNED: No fix is planned.
      NONE_AVAILABLE: Not available.
      VENDOR_FIX: A vendor fix is available.
      WORKAROUND: A workaround is available.
    """
    REMEDIATION_TYPE_UNSPECIFIED = 0
    MITIGATION = 1
    NO_FIX_PLANNED = 2
    NONE_AVAILABLE = 3
    VENDOR_FIX = 4
    WORKAROUND = 5

  details = _messages.StringField(1)
  remediationType = _messages.EnumField('RemediationTypeValueValuesEnum', 2)
  remediationUri = _messages.MessageField('RelatedUrl', 3)


class RepoId(_messages.Message):
  r"""A unique identifier for a Cloud Repo.

  Fields:
    projectRepoId: A combination of a project ID and a repo name.
    uid: A server-assigned, globally unique identifier.
  """

  projectRepoId = _messages.MessageField('ProjectRepoId', 1)
  uid = _messages.StringField(2)


class ResourceDescriptor(_messages.Message):
  r"""A ResourceDescriptor object.

  Messages:
    AnnotationsValue: A AnnotationsValue object.
    DigestValue: A DigestValue object.

  Fields:
    annotations: A AnnotationsValue attribute.
    content: A byte attribute.
    digest: A DigestValue attribute.
    downloadLocation: A string attribute.
    mediaType: A string attribute.
    name: A string attribute.
    uri: A string attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""A AnnotationsValue object.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DigestValue(_messages.Message):
    r"""A DigestValue object.

    Messages:
      AdditionalProperty: An additional property for a DigestValue object.

    Fields:
      additionalProperties: Additional properties of type DigestValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DigestValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  content = _messages.BytesField(2)
  digest = _messages.MessageField('DigestValue', 3)
  downloadLocation = _messages.StringField(4)
  mediaType = _messages.StringField(5)
  name = _messages.StringField(6)
  uri = _messages.StringField(7)


class RunDetails(_messages.Message):
  r"""A RunDetails object.

  Fields:
    builder: A ProvenanceBuilder attribute.
    byproducts: A ResourceDescriptor attribute.
    metadata: A BuildMetadata attribute.
  """

  builder = _messages.MessageField('ProvenanceBuilder', 1)
  byproducts = _messages.MessageField('ResourceDescriptor', 2, repeated=True)
  metadata = _messages.MessageField('BuildMetadata', 3)


class SBOMReferenceOccurrence(_messages.Message):
  r"""The occurrence representing an SBOM reference as applied to a specific
  resource. The occurrence follows the DSSE specification. See
  https://github.com/secure-systems-lab/dsse/blob/master/envelope.md for more
  details.

  Fields:
    payload: The actual payload that contains the SBOM reference data.
    payloadType: The kind of payload that SbomReferenceIntotoPayload takes.
      Since it's in the intoto format, this value is expected to be
      'application/vnd.in-toto+json'.
    signatures: The signatures over the payload.
  """

  payload = _messages.MessageField('SbomReferenceIntotoPayload', 1)
  payloadType = _messages.StringField(2)
  signatures = _messages.MessageField('EnvelopeSignature', 3, repeated=True)


class SBOMStatus(_messages.Message):
  r"""The status of an SBOM generation.

  Enums:
    SbomStateValueValuesEnum: The progress of the SBOM generation.

  Fields:
    error: If there was an error generating an SBOM, this will indicate what
      that error was.
    sbomState: The progress of the SBOM generation.
  """

  class SbomStateValueValuesEnum(_messages.Enum):
    r"""The progress of the SBOM generation.

    Values:
      SBOM_STATE_UNSPECIFIED: Default unknown state.
      PENDING: SBOM scanning is pending.
      COMPLETE: SBOM scanning has completed.
    """
    SBOM_STATE_UNSPECIFIED = 0
    PENDING = 1
    COMPLETE = 2

  error = _messages.StringField(1)
  sbomState = _messages.EnumField('SbomStateValueValuesEnum', 2)


class SbomReferenceIntotoPayload(_messages.Message):
  r"""The actual payload that contains the SBOM Reference data. The payload
  follows the intoto statement specification. See https://github.com/in-
  toto/attestation/blob/main/spec/v1.0/statement.md for more details.

  Fields:
    _type: Identifier for the schema of the Statement.
    predicate: Additional parameters of the Predicate. Includes the actual
      data about the SBOM.
    predicateType: URI identifying the type of the Predicate.
    subject: Set of software artifacts that the attestation applies to. Each
      element represents a single software artifact.
  """

  _type = _messages.StringField(1)
  predicate = _messages.MessageField('SbomReferenceIntotoPredicate', 2)
  predicateType = _messages.StringField(3)
  subject = _messages.MessageField('Subject', 4, repeated=True)


class SbomReferenceIntotoPredicate(_messages.Message):
  r"""A predicate which describes the SBOM being referenced.

  Messages:
    DigestValue: A map of algorithm to digest of the contents of the SBOM.

  Fields:
    digest: A map of algorithm to digest of the contents of the SBOM.
    location: The location of the SBOM.
    mimeType: The mime type of the SBOM.
    referrerId: The person or system referring this predicate to the consumer.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DigestValue(_messages.Message):
    r"""A map of algorithm to digest of the contents of the SBOM.

    Messages:
      AdditionalProperty: An additional property for a DigestValue object.

    Fields:
      additionalProperties: Additional properties of type DigestValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DigestValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  digest = _messages.MessageField('DigestValue', 1)
  location = _messages.StringField(2)
  mimeType = _messages.StringField(3)
  referrerId = _messages.StringField(4)


class Signature(_messages.Message):
  r"""Verifiers (e.g. Kritis implementations) MUST verify signatures with
  respect to the trust anchors defined in policy (e.g. a Kritis policy).
  Typically this means that the verifier has been configured with a map from
  `public_key_id` to public key material (and any required parameters, e.g.
  signing algorithm). In particular, verification implementations MUST NOT
  treat the signature `public_key_id` as anything more than a key lookup hint.
  The `public_key_id` DOES NOT validate or authenticate a public key; it only
  provides a mechanism for quickly selecting a public key ALREADY CONFIGURED
  on the verifier through a trusted channel. Verification implementations MUST
  reject signatures in any of the following circumstances: * The
  `public_key_id` is not recognized by the verifier. * The public key that
  `public_key_id` refers to does not verify the signature with respect to the
  payload. The `signature` contents SHOULD NOT be "attached" (where the
  payload is included with the serialized `signature` bytes). Verifiers MUST
  ignore any "attached" payload and only verify signatures with respect to
  explicitly provided payload (e.g. a `payload` field on the proto message
  that holds this Signature, or the canonical serialization of the proto
  message that holds this signature).

  Fields:
    publicKeyId: The identifier for the public key that verifies this
      signature. * The `public_key_id` is required. * The `public_key_id`
      SHOULD be an RFC3986 conformant URI. * When possible, the
      `public_key_id` SHOULD be an immutable reference, such as a
      cryptographic digest. Examples of valid `public_key_id`s: OpenPGP V4
      public key fingerprint: *
      "openpgp4fpr:74FAF3B861BDA0870C7B6DEF607E48D2A663AEEA" See
      https://www.iana.org/assignments/uri-schemes/prov/openpgp4fpr for more
      details on this scheme. RFC6920 digest-named SubjectPublicKeyInfo
      (digest of the DER serialization): *
      "ni:///sha-256;cD9o9Cq6LG3jD0iKXqEi_vdjJGecm_iXkbqVoScViaU" * "nih:///sh
      a-256;703f68f42aba2c6de30f488a5ea122fef76324679c9bf89791ba95a1271589a5"
    signature: The content of the signature, an opaque bytestring. The payload
      that this signature verifies MUST be unambiguously provided with the
      Signature during verification. A wrapper message might provide the
      payload explicitly. Alternatively, a message might have a canonical
      serialization that can always be unambiguously computed to derive the
      payload.
  """

  publicKeyId = _messages.StringField(1)
  signature = _messages.BytesField(2)


class SlsaBuilder(_messages.Message):
  r"""A SlsaBuilder object.

  Fields:
    id: A string attribute.
  """

  id = _messages.StringField(1)


class SlsaCompleteness(_messages.Message):
  r"""Indicates that the builder claims certain fields in this message to be
  complete.

  Fields:
    arguments: If true, the builder claims that recipe.arguments is complete,
      meaning that all external inputs are properly captured in the recipe.
    environment: If true, the builder claims that recipe.environment is
      claimed to be complete.
    materials: If true, the builder claims that materials are complete,
      usually through some controls to prevent network access. Sometimes
      called "hermetic".
  """

  arguments = _messages.BooleanField(1)
  environment = _messages.BooleanField(2)
  materials = _messages.BooleanField(3)


class SlsaMetadata(_messages.Message):
  r"""Other properties of the build.

  Fields:
    buildFinishedOn: The timestamp of when the build completed.
    buildInvocationId: Identifies the particular build invocation, which can
      be useful for finding associated logs or other ad-hoc analysis. The
      value SHOULD be globally unique, per in-toto Provenance spec.
    buildStartedOn: The timestamp of when the build started.
    completeness: Indicates that the builder claims certain fields in this
      message to be complete.
    reproducible: If true, the builder claims that running the recipe on
      materials will produce bit-for-bit identical output.
  """

  buildFinishedOn = _messages.StringField(1)
  buildInvocationId = _messages.StringField(2)
  buildStartedOn = _messages.StringField(3)
  completeness = _messages.MessageField('SlsaCompleteness', 4)
  reproducible = _messages.BooleanField(5)


class SlsaProvenance(_messages.Message):
  r"""A SlsaProvenance object.

  Fields:
    builder: required
    materials: The collection of artifacts that influenced the build including
      sources, dependencies, build tools, base images, and so on. This is
      considered to be incomplete unless metadata.completeness.materials is
      true. Unset or null is equivalent to empty.
    metadata: A SlsaMetadata attribute.
    recipe: Identifies the configuration used for the build. When combined
      with materials, this SHOULD fully describe the build, such that re-
      running this recipe results in bit-for-bit identical output (if the
      build is reproducible). required
  """

  builder = _messages.MessageField('SlsaBuilder', 1)
  materials = _messages.MessageField('Material', 2, repeated=True)
  metadata = _messages.MessageField('SlsaMetadata', 3)
  recipe = _messages.MessageField('SlsaRecipe', 4)


class SlsaProvenanceV1(_messages.Message):
  r"""Keep in sync with schema at https://github.com/slsa-
  framework/slsa/blob/main/docs/provenance/schema/v1/provenance.proto Builder
  renamed to ProvenanceBuilder because of Java conflicts.

  Fields:
    buildDefinition: A BuildDefinition attribute.
    runDetails: A RunDetails attribute.
  """

  buildDefinition = _messages.MessageField('BuildDefinition', 1)
  runDetails = _messages.MessageField('RunDetails', 2)


class SlsaProvenanceZeroTwo(_messages.Message):
  r"""See full explanation of fields at slsa.dev/provenance/v0.2.

  Messages:
    BuildConfigValue: A BuildConfigValue object.

  Fields:
    buildConfig: A BuildConfigValue attribute.
    buildType: A string attribute.
    builder: A GrafeasV1SlsaProvenanceZeroTwoSlsaBuilder attribute.
    invocation: A GrafeasV1SlsaProvenanceZeroTwoSlsaInvocation attribute.
    materials: A GrafeasV1SlsaProvenanceZeroTwoSlsaMaterial attribute.
    metadata: A GrafeasV1SlsaProvenanceZeroTwoSlsaMetadata attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class BuildConfigValue(_messages.Message):
    r"""A BuildConfigValue object.

    Messages:
      AdditionalProperty: An additional property for a BuildConfigValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a BuildConfigValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  buildConfig = _messages.MessageField('BuildConfigValue', 1)
  buildType = _messages.StringField(2)
  builder = _messages.MessageField('GrafeasV1SlsaProvenanceZeroTwoSlsaBuilder', 3)
  invocation = _messages.MessageField('GrafeasV1SlsaProvenanceZeroTwoSlsaInvocation', 4)
  materials = _messages.MessageField('GrafeasV1SlsaProvenanceZeroTwoSlsaMaterial', 5, repeated=True)
  metadata = _messages.MessageField('GrafeasV1SlsaProvenanceZeroTwoSlsaMetadata', 6)


class SlsaRecipe(_messages.Message):
  r"""Steps taken to build the artifact. For a TaskRun, typically each
  container corresponds to one step in the recipe.

  Messages:
    ArgumentsValue: Collection of all external inputs that influenced the
      build on top of recipe.definedInMaterial and recipe.entryPoint. For
      example, if the recipe type were "make", then this might be the flags
      passed to make aside from the target, which is captured in
      recipe.entryPoint. Depending on the recipe Type, the structure may be
      different.
    EnvironmentValue: Any other builder-controlled inputs necessary for
      correctly evaluating the recipe. Usually only needed for reproducing the
      build but not evaluated as part of policy. Depending on the recipe Type,
      the structure may be different.

  Fields:
    arguments: Collection of all external inputs that influenced the build on
      top of recipe.definedInMaterial and recipe.entryPoint. For example, if
      the recipe type were "make", then this might be the flags passed to make
      aside from the target, which is captured in recipe.entryPoint. Depending
      on the recipe Type, the structure may be different.
    definedInMaterial: Index in materials containing the recipe steps that are
      not implied by recipe.type. For example, if the recipe type were "make",
      then this would point to the source containing the Makefile, not the
      make program itself. Set to -1 if the recipe doesn't come from a
      material, as zero is default unset value for int64.
    entryPoint: String identifying the entry point into the build. This is
      often a path to a configuration file and/or a target label within that
      file. The syntax and meaning are defined by recipe.type. For example, if
      the recipe type were "make", then this would reference the directory in
      which to run make as well as which target to use.
    environment: Any other builder-controlled inputs necessary for correctly
      evaluating the recipe. Usually only needed for reproducing the build but
      not evaluated as part of policy. Depending on the recipe Type, the
      structure may be different.
    type: URI indicating what type of recipe was performed. It determines the
      meaning of recipe.entryPoint, recipe.arguments, recipe.environment, and
      materials.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ArgumentsValue(_messages.Message):
    r"""Collection of all external inputs that influenced the build on top of
    recipe.definedInMaterial and recipe.entryPoint. For example, if the recipe
    type were "make", then this might be the flags passed to make aside from
    the target, which is captured in recipe.entryPoint. Depending on the
    recipe Type, the structure may be different.

    Messages:
      AdditionalProperty: An additional property for a ArgumentsValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ArgumentsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvironmentValue(_messages.Message):
    r"""Any other builder-controlled inputs necessary for correctly evaluating
    the recipe. Usually only needed for reproducing the build but not
    evaluated as part of policy. Depending on the recipe Type, the structure
    may be different.

    Messages:
      AdditionalProperty: An additional property for a EnvironmentValue
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvironmentValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  arguments = _messages.MessageField('ArgumentsValue', 1)
  definedInMaterial = _messages.IntegerField(2)
  entryPoint = _messages.StringField(3)
  environment = _messages.MessageField('EnvironmentValue', 4)
  type = _messages.StringField(5)


class Source(_messages.Message):
  r"""Source describes the location of the source used for the build.

  Messages:
    FileHashesValue: Hash(es) of the build source, which can be used to verify
      that the original source integrity was maintained in the build. The keys
      to this map are file paths used as build source and the values contain
      the hash values for those files. If the build source came in a single
      package such as a gzipped tarfile (.tar.gz), the FileHash will be for
      the single path to that file.

  Fields:
    additionalContexts: If provided, some of the source code used for the
      build may be found in these locations, in the case where the source
      repository had multiple remotes or submodules. This list will not
      include the context specified in the context field.
    artifactStorageSourceUri: If provided, the input binary artifacts for the
      build came from this location.
    context: If provided, the source code used for the build came from this
      location.
    fileHashes: Hash(es) of the build source, which can be used to verify that
      the original source integrity was maintained in the build. The keys to
      this map are file paths used as build source and the values contain the
      hash values for those files. If the build source came in a single
      package such as a gzipped tarfile (.tar.gz), the FileHash will be for
      the single path to that file.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FileHashesValue(_messages.Message):
    r"""Hash(es) of the build source, which can be used to verify that the
    original source integrity was maintained in the build. The keys to this
    map are file paths used as build source and the values contain the hash
    values for those files. If the build source came in a single package such
    as a gzipped tarfile (.tar.gz), the FileHash will be for the single path
    to that file.

    Messages:
      AdditionalProperty: An additional property for a FileHashesValue object.

    Fields:
      additionalProperties: Additional properties of type FileHashesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FileHashesValue object.

      Fields:
        key: Name of the additional property.
        value: A FileHashes attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('FileHashes', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  additionalContexts = _messages.MessageField('SourceContext', 1, repeated=True)
  artifactStorageSourceUri = _messages.StringField(2)
  context = _messages.MessageField('SourceContext', 3)
  fileHashes = _messages.MessageField('FileHashesValue', 4)


class SourceContext(_messages.Message):
  r"""A SourceContext is a reference to a tree of files. A SourceContext
  together with a path point to a unique revision of a single file or
  directory.

  Messages:
    LabelsValue: Labels with user defined metadata.

  Fields:
    cloudRepo: A SourceContext referring to a revision in a Google Cloud
      Source Repo.
    gerrit: A SourceContext referring to a Gerrit project.
    git: A SourceContext referring to any third party Git repo (e.g., GitHub).
    labels: Labels with user defined metadata.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels with user defined metadata.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  cloudRepo = _messages.MessageField('CloudRepoSourceContext', 1)
  gerrit = _messages.MessageField('GerritSourceContext', 2)
  git = _messages.MessageField('GitSourceContext', 3)
  labels = _messages.MessageField('LabelsValue', 4)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Subject(_messages.Message):
  r"""A Subject object.

  Messages:
    DigestValue: `"": ""` Algorithms can be e.g. sha256, sha512 See
      https://github.com/in-
      toto/attestation/blob/main/spec/field_types.md#DigestSet

  Fields:
    digest: `"": ""` Algorithms can be e.g. sha256, sha512 See
      https://github.com/in-
      toto/attestation/blob/main/spec/field_types.md#DigestSet
    name: A string attribute.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DigestValue(_messages.Message):
    r"""`"": ""` Algorithms can be e.g. sha256, sha512 See
    https://github.com/in-
    toto/attestation/blob/main/spec/field_types.md#DigestSet

    Messages:
      AdditionalProperty: An additional property for a DigestValue object.

    Fields:
      additionalProperties: Additional properties of type DigestValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DigestValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  digest = _messages.MessageField('DigestValue', 1)
  name = _messages.StringField(2)


class UpgradeDistribution(_messages.Message):
  r"""The Upgrade Distribution represents metadata about the Upgrade for each
  operating system (CPE). Some distributions have additional metadata around
  updates, classifying them into various categories and severities.

  Fields:
    classification: The operating system classification of this Upgrade, as
      specified by the upstream operating system upgrade feed. For Windows the
      classification is one of the category_ids listed at
      https://docs.microsoft.com/en-us/previous-
      versions/windows/desktop/ff357803(v=vs.85)
    cpeUri: Required - The specific operating system this metadata applies to.
      See https://cpe.mitre.org/specification/.
    cve: The cve tied to this Upgrade.
    severity: The severity as specified by the upstream operating system.
  """

  classification = _messages.StringField(1)
  cpeUri = _messages.StringField(2)
  cve = _messages.StringField(3, repeated=True)
  severity = _messages.StringField(4)


class UpgradeOccurrence(_messages.Message):
  r"""An Upgrade Occurrence represents that a specific resource_url could
  install a specific upgrade. This presence is supplied via local sources
  (i.e. it is present in the mirror and the running system has noticed its
  availability). For Windows, both distribution and windows_update contain
  information for the Windows update.

  Fields:
    distribution: Metadata about the upgrade for available for the specific
      operating system for the resource_url. This allows efficient filtering,
      as well as making it easier to use the occurrence.
    package: Required for non-Windows OS. The package this Upgrade is for.
    parsedVersion: Required for non-Windows OS. The version of the package in
      a machine + human readable form.
    windowsUpdate: Required for Windows OS. Represents the metadata about the
      Windows update.
  """

  distribution = _messages.MessageField('UpgradeDistribution', 1)
  package = _messages.StringField(2)
  parsedVersion = _messages.MessageField('Version', 3)
  windowsUpdate = _messages.MessageField('WindowsUpdate', 4)


class Version(_messages.Message):
  r"""Version contains structured information about the version of a package.

  Enums:
    KindValueValuesEnum: Required. Distinguishes between sentinel MIN/MAX
      versions and normal versions.

  Fields:
    epoch: Used to correct mistakes in the version numbering scheme.
    fullName: Human readable version string. This string is of the form :- and
      is only set when kind is NORMAL.
    inclusive: Whether this version is specifying part of an inclusive range.
      Grafeas does not have the capability to specify version ranges; instead
      we have fields that specify start version and end versions. At times
      this is insufficient - we also need to specify whether the version is
      included in the range or is excluded from the range. This boolean is
      expected to be set to true when the version is included in a range.
    kind: Required. Distinguishes between sentinel MIN/MAX versions and normal
      versions.
    name: Required only when version kind is NORMAL. The main part of the
      version name.
    revision: The iteration of the package build from the above version.
  """

  class KindValueValuesEnum(_messages.Enum):
    r"""Required. Distinguishes between sentinel MIN/MAX versions and normal
    versions.

    Values:
      VERSION_KIND_UNSPECIFIED: Unknown.
      NORMAL: A standard package version.
      MINIMUM: A special version representing negative infinity.
      MAXIMUM: A special version representing positive infinity.
    """
    VERSION_KIND_UNSPECIFIED = 0
    NORMAL = 1
    MINIMUM = 2
    MAXIMUM = 3

  epoch = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  fullName = _messages.StringField(2)
  inclusive = _messages.BooleanField(3)
  kind = _messages.EnumField('KindValueValuesEnum', 4)
  name = _messages.StringField(5)
  revision = _messages.StringField(6)


class VexAssessment(_messages.Message):
  r"""VexAssessment provides all publisher provided Vex information that is
  related to this vulnerability.

  Enums:
    StateValueValuesEnum: Provides the state of this Vulnerability assessment.

  Fields:
    cve: Holds the MITRE standard Common Vulnerabilities and Exposures (CVE)
      tracking number for the vulnerability. Deprecated: Use vulnerability_id
      instead to denote CVEs.
    impacts: Contains information about the impact of this vulnerability, this
      will change with time.
    justification: Justification provides the justification when the state of
      the assessment if NOT_AFFECTED.
    noteName: The VulnerabilityAssessment note from which this VexAssessment
      was generated. This will be of the form:
      `projects/[PROJECT_ID]/notes/[NOTE_ID]`.
    relatedUris: Holds a list of references associated with this vulnerability
      item and assessment.
    remediations: Specifies details on how to handle (and presumably, fix) a
      vulnerability.
    state: Provides the state of this Vulnerability assessment.
    vulnerabilityId: The vulnerability identifier for this Assessment. Will
      hold one of common identifiers e.g. CVE, GHSA etc.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Provides the state of this Vulnerability assessment.

    Values:
      STATE_UNSPECIFIED: No state is specified.
      AFFECTED: This product is known to be affected by this vulnerability.
      NOT_AFFECTED: This product is known to be not affected by this
        vulnerability.
      FIXED: This product contains a fix for this vulnerability.
      UNDER_INVESTIGATION: It is not known yet whether these versions are or
        are not affected by the vulnerability. However, it is still under
        investigation.
    """
    STATE_UNSPECIFIED = 0
    AFFECTED = 1
    NOT_AFFECTED = 2
    FIXED = 3
    UNDER_INVESTIGATION = 4

  cve = _messages.StringField(1)
  impacts = _messages.StringField(2, repeated=True)
  justification = _messages.MessageField('Justification', 3)
  noteName = _messages.StringField(4)
  relatedUris = _messages.MessageField('RelatedUrl', 5, repeated=True)
  remediations = _messages.MessageField('Remediation', 6, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  vulnerabilityId = _messages.StringField(8)


class VulnerabilityOccurrence(_messages.Message):
  r"""An occurrence of a severity vulnerability on a resource.

  Enums:
    CvssVersionValueValuesEnum: Output only. CVSS version used to populate
      cvss_score and severity.
    EffectiveSeverityValueValuesEnum: The distro assigned severity for this
      vulnerability when it is available, otherwise this is the note provider
      assigned severity. When there are multiple PackageIssues for this
      vulnerability, they can have different effective severities because some
      might be provided by the distro while others are provided by the
      language ecosystem for a language pack. For this reason, it is advised
      to use the effective severity on the PackageIssue level. In the case
      where multiple PackageIssues have differing effective severities, this
      field should be the highest severity for any of the PackageIssues.
    SeverityValueValuesEnum: Output only. The note provider assigned severity
      of this vulnerability.

  Fields:
    cvssScore: Output only. The CVSS score of this vulnerability. CVSS score
      is on a scale of 0 - 10 where 0 indicates low severity and 10 indicates
      high severity.
    cvssV2: The cvss v2 score for the vulnerability.
    cvssVersion: Output only. CVSS version used to populate cvss_score and
      severity.
    cvssv3: The cvss v3 score for the vulnerability.
    effectiveSeverity: The distro assigned severity for this vulnerability
      when it is available, otherwise this is the note provider assigned
      severity. When there are multiple PackageIssues for this vulnerability,
      they can have different effective severities because some might be
      provided by the distro while others are provided by the language
      ecosystem for a language pack. For this reason, it is advised to use the
      effective severity on the PackageIssue level. In the case where multiple
      PackageIssues have differing effective severities, this field should be
      the highest severity for any of the PackageIssues.
    fixAvailable: Output only. Whether at least one of the affected packages
      has a fix available.
    longDescription: Output only. A detailed description of this
      vulnerability.
    packageIssue: Required. The set of affected locations and their fixes (if
      available) within the associated resource.
    relatedUrls: Output only. URLs related to this vulnerability.
    severity: Output only. The note provider assigned severity of this
      vulnerability.
    shortDescription: Output only. A one sentence description of this
      vulnerability.
    type: The type of package; whether native or non native (e.g., ruby gems,
      node.js packages, etc.).
    vexAssessment: A VexAssessment attribute.
  """

  class CvssVersionValueValuesEnum(_messages.Enum):
    r"""Output only. CVSS version used to populate cvss_score and severity.

    Values:
      CVSS_VERSION_UNSPECIFIED: <no description>
      CVSS_VERSION_2: <no description>
      CVSS_VERSION_3: <no description>
    """
    CVSS_VERSION_UNSPECIFIED = 0
    CVSS_VERSION_2 = 1
    CVSS_VERSION_3 = 2

  class EffectiveSeverityValueValuesEnum(_messages.Enum):
    r"""The distro assigned severity for this vulnerability when it is
    available, otherwise this is the note provider assigned severity. When
    there are multiple PackageIssues for this vulnerability, they can have
    different effective severities because some might be provided by the
    distro while others are provided by the language ecosystem for a language
    pack. For this reason, it is advised to use the effective severity on the
    PackageIssue level. In the case where multiple PackageIssues have
    differing effective severities, this field should be the highest severity
    for any of the PackageIssues.

    Values:
      SEVERITY_UNSPECIFIED: Unknown.
      MINIMAL: Minimal severity.
      LOW: Low severity.
      MEDIUM: Medium severity.
      HIGH: High severity.
      CRITICAL: Critical severity.
    """
    SEVERITY_UNSPECIFIED = 0
    MINIMAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5

  class SeverityValueValuesEnum(_messages.Enum):
    r"""Output only. The note provider assigned severity of this
    vulnerability.

    Values:
      SEVERITY_UNSPECIFIED: Unknown.
      MINIMAL: Minimal severity.
      LOW: Low severity.
      MEDIUM: Medium severity.
      HIGH: High severity.
      CRITICAL: Critical severity.
    """
    SEVERITY_UNSPECIFIED = 0
    MINIMAL = 1
    LOW = 2
    MEDIUM = 3
    HIGH = 4
    CRITICAL = 5

  cvssScore = _messages.FloatField(1, variant=_messages.Variant.FLOAT)
  cvssV2 = _messages.MessageField('CVSS', 2)
  cvssVersion = _messages.EnumField('CvssVersionValueValuesEnum', 3)
  cvssv3 = _messages.MessageField('CVSS', 4)
  effectiveSeverity = _messages.EnumField('EffectiveSeverityValueValuesEnum', 5)
  fixAvailable = _messages.BooleanField(6)
  longDescription = _messages.StringField(7)
  packageIssue = _messages.MessageField('PackageIssue', 8, repeated=True)
  relatedUrls = _messages.MessageField('RelatedUrl', 9, repeated=True)
  severity = _messages.EnumField('SeverityValueValuesEnum', 10)
  shortDescription = _messages.StringField(11)
  type = _messages.StringField(12)
  vexAssessment = _messages.MessageField('VexAssessment', 13)


class WindowsUpdate(_messages.Message):
  r"""Windows Update represents the metadata about the update for the Windows
  operating system. The fields in this message come from the Windows Update
  API documented at https://docs.microsoft.com/en-
  us/windows/win32/api/wuapi/nn-wuapi-iupdate.

  Fields:
    categories: The list of categories to which the update belongs.
    description: The localized description of the update.
    identity: Required - The unique identifier for the update.
    kbArticleIds: The Microsoft Knowledge Base article IDs that are associated
      with the update.
    lastPublishedTimestamp: The last published timestamp of the update.
    supportUrl: The hyperlink to the support information for the update.
    title: The localized title of the update.
  """

  categories = _messages.MessageField('Category', 1, repeated=True)
  description = _messages.StringField(2)
  identity = _messages.MessageField('Identity', 3)
  kbArticleIds = _messages.StringField(4, repeated=True)
  lastPublishedTimestamp = _messages.StringField(5)
  supportUrl = _messages.StringField(6)
  title = _messages.StringField(7)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

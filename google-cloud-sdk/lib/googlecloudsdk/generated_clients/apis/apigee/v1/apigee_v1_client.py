"""Generated client library for apigee version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.apigee.v1 import apigee_v1_messages as messages


class ApigeeV1(base_api.BaseApiClient):
  """Generated client library for service apigee version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://apigee.googleapis.com/'
  MTLS_BASE_URL = 'https://apigee.mtls.googleapis.com/'

  _PACKAGE = 'apigee'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'ApigeeV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new apigee handle."""
    url = url or self.BASE_URL
    super(ApigeeV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.hybrid_issuers = self.HybridIssuersService(self)
    self.hybrid = self.HybridService(self)
    self.organizations_analytics_datastores = self.OrganizationsAnalyticsDatastoresService(self)
    self.organizations_analytics = self.OrganizationsAnalyticsService(self)
    self.organizations_apiproducts_attributes = self.OrganizationsApiproductsAttributesService(self)
    self.organizations_apiproducts_rateplans = self.OrganizationsApiproductsRateplansService(self)
    self.organizations_apiproducts = self.OrganizationsApiproductsService(self)
    self.organizations_apis_deployments = self.OrganizationsApisDeploymentsService(self)
    self.organizations_apis_keyvaluemaps_entries = self.OrganizationsApisKeyvaluemapsEntriesService(self)
    self.organizations_apis_keyvaluemaps = self.OrganizationsApisKeyvaluemapsService(self)
    self.organizations_apis_revisions_deployments = self.OrganizationsApisRevisionsDeploymentsService(self)
    self.organizations_apis_revisions = self.OrganizationsApisRevisionsService(self)
    self.organizations_apis = self.OrganizationsApisService(self)
    self.organizations_appgroups_apps_keys_apiproducts = self.OrganizationsAppgroupsAppsKeysApiproductsService(self)
    self.organizations_appgroups_apps_keys = self.OrganizationsAppgroupsAppsKeysService(self)
    self.organizations_appgroups_apps = self.OrganizationsAppgroupsAppsService(self)
    self.organizations_appgroups = self.OrganizationsAppgroupsService(self)
    self.organizations_apps = self.OrganizationsAppsService(self)
    self.organizations_datacollectors = self.OrganizationsDatacollectorsService(self)
    self.organizations_deployments = self.OrganizationsDeploymentsService(self)
    self.organizations_developers_apps_attributes = self.OrganizationsDevelopersAppsAttributesService(self)
    self.organizations_developers_apps_keys_apiproducts = self.OrganizationsDevelopersAppsKeysApiproductsService(self)
    self.organizations_developers_apps_keys_create = self.OrganizationsDevelopersAppsKeysCreateService(self)
    self.organizations_developers_apps_keys = self.OrganizationsDevelopersAppsKeysService(self)
    self.organizations_developers_apps = self.OrganizationsDevelopersAppsService(self)
    self.organizations_developers_attributes = self.OrganizationsDevelopersAttributesService(self)
    self.organizations_developers_balance = self.OrganizationsDevelopersBalanceService(self)
    self.organizations_developers_subscriptions = self.OrganizationsDevelopersSubscriptionsService(self)
    self.organizations_developers = self.OrganizationsDevelopersService(self)
    self.organizations_endpointAttachments = self.OrganizationsEndpointAttachmentsService(self)
    self.organizations_envgroups_attachments = self.OrganizationsEnvgroupsAttachmentsService(self)
    self.organizations_envgroups = self.OrganizationsEnvgroupsService(self)
    self.organizations_environments_analytics_admin = self.OrganizationsEnvironmentsAnalyticsAdminService(self)
    self.organizations_environments_analytics_exports = self.OrganizationsEnvironmentsAnalyticsExportsService(self)
    self.organizations_environments_analytics = self.OrganizationsEnvironmentsAnalyticsService(self)
    self.organizations_environments_apis_deployments = self.OrganizationsEnvironmentsApisDeploymentsService(self)
    self.organizations_environments_apis_revisions_debugsessions_data = self.OrganizationsEnvironmentsApisRevisionsDebugsessionsDataService(self)
    self.organizations_environments_apis_revisions_debugsessions = self.OrganizationsEnvironmentsApisRevisionsDebugsessionsService(self)
    self.organizations_environments_apis_revisions_deployments = self.OrganizationsEnvironmentsApisRevisionsDeploymentsService(self)
    self.organizations_environments_apis_revisions = self.OrganizationsEnvironmentsApisRevisionsService(self)
    self.organizations_environments_apis = self.OrganizationsEnvironmentsApisService(self)
    self.organizations_environments_archiveDeployments = self.OrganizationsEnvironmentsArchiveDeploymentsService(self)
    self.organizations_environments_caches = self.OrganizationsEnvironmentsCachesService(self)
    self.organizations_environments_deployments = self.OrganizationsEnvironmentsDeploymentsService(self)
    self.organizations_environments_flowhooks = self.OrganizationsEnvironmentsFlowhooksService(self)
    self.organizations_environments_keystores_aliases = self.OrganizationsEnvironmentsKeystoresAliasesService(self)
    self.organizations_environments_keystores = self.OrganizationsEnvironmentsKeystoresService(self)
    self.organizations_environments_keyvaluemaps_entries = self.OrganizationsEnvironmentsKeyvaluemapsEntriesService(self)
    self.organizations_environments_keyvaluemaps = self.OrganizationsEnvironmentsKeyvaluemapsService(self)
    self.organizations_environments_optimizedStats = self.OrganizationsEnvironmentsOptimizedStatsService(self)
    self.organizations_environments_queries = self.OrganizationsEnvironmentsQueriesService(self)
    self.organizations_environments_references = self.OrganizationsEnvironmentsReferencesService(self)
    self.organizations_environments_resourcefiles = self.OrganizationsEnvironmentsResourcefilesService(self)
    self.organizations_environments_securityActions = self.OrganizationsEnvironmentsSecurityActionsService(self)
    self.organizations_environments_securityIncidents = self.OrganizationsEnvironmentsSecurityIncidentsService(self)
    self.organizations_environments_securityReports = self.OrganizationsEnvironmentsSecurityReportsService(self)
    self.organizations_environments_securityStats = self.OrganizationsEnvironmentsSecurityStatsService(self)
    self.organizations_environments_sharedflows_deployments = self.OrganizationsEnvironmentsSharedflowsDeploymentsService(self)
    self.organizations_environments_sharedflows_revisions = self.OrganizationsEnvironmentsSharedflowsRevisionsService(self)
    self.organizations_environments_sharedflows = self.OrganizationsEnvironmentsSharedflowsService(self)
    self.organizations_environments_stats = self.OrganizationsEnvironmentsStatsService(self)
    self.organizations_environments_targetservers = self.OrganizationsEnvironmentsTargetserversService(self)
    self.organizations_environments_traceConfig_overrides = self.OrganizationsEnvironmentsTraceConfigOverridesService(self)
    self.organizations_environments_traceConfig = self.OrganizationsEnvironmentsTraceConfigService(self)
    self.organizations_environments = self.OrganizationsEnvironmentsService(self)
    self.organizations_hostQueries = self.OrganizationsHostQueriesService(self)
    self.organizations_hostSecurityReports = self.OrganizationsHostSecurityReportsService(self)
    self.organizations_hostStats = self.OrganizationsHostStatsService(self)
    self.organizations_instances_attachments = self.OrganizationsInstancesAttachmentsService(self)
    self.organizations_instances_canaryevaluations = self.OrganizationsInstancesCanaryevaluationsService(self)
    self.organizations_instances_natAddresses = self.OrganizationsInstancesNatAddressesService(self)
    self.organizations_instances = self.OrganizationsInstancesService(self)
    self.organizations_keyvaluemaps_entries = self.OrganizationsKeyvaluemapsEntriesService(self)
    self.organizations_keyvaluemaps = self.OrganizationsKeyvaluemapsService(self)
    self.organizations_operations = self.OrganizationsOperationsService(self)
    self.organizations_optimizedHostStats = self.OrganizationsOptimizedHostStatsService(self)
    self.organizations_reports = self.OrganizationsReportsService(self)
    self.organizations_securityProfiles_environments = self.OrganizationsSecurityProfilesEnvironmentsService(self)
    self.organizations_securityProfiles = self.OrganizationsSecurityProfilesService(self)
    self.organizations_securityincidentenvironments = self.OrganizationsSecurityincidentenvironmentsService(self)
    self.organizations_sharedflows_deployments = self.OrganizationsSharedflowsDeploymentsService(self)
    self.organizations_sharedflows_revisions_deployments = self.OrganizationsSharedflowsRevisionsDeploymentsService(self)
    self.organizations_sharedflows_revisions = self.OrganizationsSharedflowsRevisionsService(self)
    self.organizations_sharedflows = self.OrganizationsSharedflowsService(self)
    self.organizations_sites_apicategories = self.OrganizationsSitesApicategoriesService(self)
    self.organizations_sites = self.OrganizationsSitesService(self)
    self.organizations = self.OrganizationsService(self)
    self.projects = self.ProjectsService(self)

  class HybridIssuersService(base_api.BaseApiService):
    """Service class for the hybrid_issuers resource."""

    _NAME = 'hybrid_issuers'

    def __init__(self, client):
      super(ApigeeV1.HybridIssuersService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists hybrid services and its trusted issuers service account ids. This api is authenticated and unauthorized(allow all the users) and used by runtime authn-authz service to query control plane's issuer service account ids.

      Args:
        request: (ApigeeHybridIssuersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListHybridIssuersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/hybrid/issuers',
        http_method='GET',
        method_id='apigee.hybrid.issuers.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeHybridIssuersListRequest',
        response_type_name='GoogleCloudApigeeV1ListHybridIssuersResponse',
        supports_download=False,
    )

  class HybridService(base_api.BaseApiService):
    """Service class for the hybrid resource."""

    _NAME = 'hybrid'

    def __init__(self, client):
      super(ApigeeV1.HybridService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsAnalyticsDatastoresService(base_api.BaseApiService):
    """Service class for the organizations_analytics_datastores resource."""

    _NAME = 'organizations_analytics_datastores'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsAnalyticsDatastoresService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a Datastore for an org.

      Args:
        request: (ApigeeOrganizationsAnalyticsDatastoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Datastore) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/analytics/datastores',
        http_method='POST',
        method_id='apigee.organizations.analytics.datastores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/analytics/datastores',
        request_field='googleCloudApigeeV1Datastore',
        request_type_name='ApigeeOrganizationsAnalyticsDatastoresCreateRequest',
        response_type_name='GoogleCloudApigeeV1Datastore',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete a Datastore from an org.

      Args:
        request: (ApigeeOrganizationsAnalyticsDatastoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/analytics/datastores/{datastoresId}',
        http_method='DELETE',
        method_id='apigee.organizations.analytics.datastores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsAnalyticsDatastoresDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get a Datastore.

      Args:
        request: (ApigeeOrganizationsAnalyticsDatastoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Datastore) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/analytics/datastores/{datastoresId}',
        http_method='GET',
        method_id='apigee.organizations.analytics.datastores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsAnalyticsDatastoresGetRequest',
        response_type_name='GoogleCloudApigeeV1Datastore',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""List Datastores.

      Args:
        request: (ApigeeOrganizationsAnalyticsDatastoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListDatastoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/analytics/datastores',
        http_method='GET',
        method_id='apigee.organizations.analytics.datastores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['targetType'],
        relative_path='v1/{+parent}/analytics/datastores',
        request_field='',
        request_type_name='ApigeeOrganizationsAnalyticsDatastoresListRequest',
        response_type_name='GoogleCloudApigeeV1ListDatastoresResponse',
        supports_download=False,
    )

    def Test(self, request, global_params=None):
      r"""Test if Datastore configuration is correct. This includes checking if credentials provided by customer have required permissions in target destination storage.

      Args:
        request: (ApigeeOrganizationsAnalyticsDatastoresTestRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1TestDatastoreResponse) The response message.
      """
      config = self.GetMethodConfig('Test')
      return self._RunMethod(
          config, request, global_params=global_params)

    Test.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/analytics/datastores:test',
        http_method='POST',
        method_id='apigee.organizations.analytics.datastores.test',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/analytics/datastores:test',
        request_field='googleCloudApigeeV1Datastore',
        request_type_name='ApigeeOrganizationsAnalyticsDatastoresTestRequest',
        response_type_name='GoogleCloudApigeeV1TestDatastoreResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Update a Datastore.

      Args:
        request: (ApigeeOrganizationsAnalyticsDatastoresUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Datastore) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/analytics/datastores/{datastoresId}',
        http_method='PUT',
        method_id='apigee.organizations.analytics.datastores.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1Datastore',
        request_type_name='ApigeeOrganizationsAnalyticsDatastoresUpdateRequest',
        response_type_name='GoogleCloudApigeeV1Datastore',
        supports_download=False,
    )

  class OrganizationsAnalyticsService(base_api.BaseApiService):
    """Service class for the organizations_analytics resource."""

    _NAME = 'organizations_analytics'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsAnalyticsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsApiproductsAttributesService(base_api.BaseApiService):
    """Service class for the organizations_apiproducts_attributes resource."""

    _NAME = 'organizations_apiproducts_attributes'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsApiproductsAttributesService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes an API product attribute.

      Args:
        request: (ApigeeOrganizationsApiproductsAttributesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Attribute) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apiproducts/{apiproductsId}/attributes/{attributesId}',
        http_method='DELETE',
        method_id='apigee.organizations.apiproducts.attributes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsApiproductsAttributesDeleteRequest',
        response_type_name='GoogleCloudApigeeV1Attribute',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the value of an API product attribute.

      Args:
        request: (ApigeeOrganizationsApiproductsAttributesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Attribute) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apiproducts/{apiproductsId}/attributes/{attributesId}',
        http_method='GET',
        method_id='apigee.organizations.apiproducts.attributes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsApiproductsAttributesGetRequest',
        response_type_name='GoogleCloudApigeeV1Attribute',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all API product attributes.

      Args:
        request: (ApigeeOrganizationsApiproductsAttributesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Attributes) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apiproducts/{apiproductsId}/attributes',
        http_method='GET',
        method_id='apigee.organizations.apiproducts.attributes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/attributes',
        request_field='',
        request_type_name='ApigeeOrganizationsApiproductsAttributesListRequest',
        response_type_name='GoogleCloudApigeeV1Attributes',
        supports_download=False,
    )

    def UpdateApiProductAttribute(self, request, global_params=None):
      r"""Updates the value of an API product attribute. **Note**: OAuth access tokens and Key Management Service (KMS) entities (apps, developers, and API products) are cached for 180 seconds (current default). Any custom attributes associated with entities also get cached for at least 180 seconds after entity is accessed during runtime. In this case, the `ExpiresIn` element on the OAuthV2 policy won't be able to expire an access token in less than 180 seconds.

      Args:
        request: (GoogleCloudApigeeV1Attribute) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Attribute) The response message.
      """
      config = self.GetMethodConfig('UpdateApiProductAttribute')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateApiProductAttribute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apiproducts/{apiproductsId}/attributes/{attributesId}',
        http_method='POST',
        method_id='apigee.organizations.apiproducts.attributes.updateApiProductAttribute',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='GoogleCloudApigeeV1Attribute',
        response_type_name='GoogleCloudApigeeV1Attribute',
        supports_download=False,
    )

  class OrganizationsApiproductsRateplansService(base_api.BaseApiService):
    """Service class for the organizations_apiproducts_rateplans resource."""

    _NAME = 'organizations_apiproducts_rateplans'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsApiproductsRateplansService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Create a rate plan that is associated with an API product in an organization. Using rate plans, API product owners can monetize their API products by configuring one or more of the following: - Billing frequency - Initial setup fees for using an API product - Payment funding model (postpaid only) - Fixed recurring or consumption-based charges for using an API product - Revenue sharing with developer partners An API product can have multiple rate plans associated with it but *only one* rate plan can be active at any point of time. **Note: From the developer's perspective, they purchase API products not rate plans.

      Args:
        request: (ApigeeOrganizationsApiproductsRateplansCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1RatePlan) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apiproducts/{apiproductsId}/rateplans',
        http_method='POST',
        method_id='apigee.organizations.apiproducts.rateplans.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/rateplans',
        request_field='googleCloudApigeeV1RatePlan',
        request_type_name='ApigeeOrganizationsApiproductsRateplansCreateRequest',
        response_type_name='GoogleCloudApigeeV1RatePlan',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a rate plan.

      Args:
        request: (ApigeeOrganizationsApiproductsRateplansDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1RatePlan) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apiproducts/{apiproductsId}/rateplans/{rateplansId}',
        http_method='DELETE',
        method_id='apigee.organizations.apiproducts.rateplans.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsApiproductsRateplansDeleteRequest',
        response_type_name='GoogleCloudApigeeV1RatePlan',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the details of a rate plan.

      Args:
        request: (ApigeeOrganizationsApiproductsRateplansGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1RatePlan) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apiproducts/{apiproductsId}/rateplans/{rateplansId}',
        http_method='GET',
        method_id='apigee.organizations.apiproducts.rateplans.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsApiproductsRateplansGetRequest',
        response_type_name='GoogleCloudApigeeV1RatePlan',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the rate plans for an API product.

      Args:
        request: (ApigeeOrganizationsApiproductsRateplansListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListRatePlansResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apiproducts/{apiproductsId}/rateplans',
        http_method='GET',
        method_id='apigee.organizations.apiproducts.rateplans.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['count', 'expand', 'orderBy', 'startKey', 'state'],
        relative_path='v1/{+parent}/rateplans',
        request_field='',
        request_type_name='ApigeeOrganizationsApiproductsRateplansListRequest',
        response_type_name='GoogleCloudApigeeV1ListRatePlansResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates an existing rate plan.

      Args:
        request: (GoogleCloudApigeeV1RatePlan) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1RatePlan) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apiproducts/{apiproductsId}/rateplans/{rateplansId}',
        http_method='PUT',
        method_id='apigee.organizations.apiproducts.rateplans.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='GoogleCloudApigeeV1RatePlan',
        response_type_name='GoogleCloudApigeeV1RatePlan',
        supports_download=False,
    )

  class OrganizationsApiproductsService(base_api.BaseApiService):
    """Service class for the organizations_apiproducts resource."""

    _NAME = 'organizations_apiproducts'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsApiproductsService, self).__init__(client)
      self._upload_configs = {
          }

    def Attributes(self, request, global_params=None):
      r"""Updates or creates API product attributes. This API **replaces** the current list of attributes with the attributes specified in the request body. In this way, you can update existing attributes, add new attributes, or delete existing attributes by omitting them from the request body. **Note**: OAuth access tokens and Key Management Service (KMS) entities (apps, developers, and API products) are cached for 180 seconds (current default). Any custom attributes associated with entities also get cached for at least 180 seconds after entity is accessed during runtime. In this case, the `ExpiresIn` element on the OAuthV2 policy won't be able to expire an access token in less than 180 seconds.

      Args:
        request: (ApigeeOrganizationsApiproductsAttributesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Attributes) The response message.
      """
      config = self.GetMethodConfig('Attributes')
      return self._RunMethod(
          config, request, global_params=global_params)

    Attributes.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apiproducts/{apiproductsId}/attributes',
        http_method='POST',
        method_id='apigee.organizations.apiproducts.attributes',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}/attributes',
        request_field='googleCloudApigeeV1Attributes',
        request_type_name='ApigeeOrganizationsApiproductsAttributesRequest',
        response_type_name='GoogleCloudApigeeV1Attributes',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates an API product in an organization. You create API products after you have proxied backend services using API proxies. An API product is a collection of API resources combined with quota settings and metadata that you can use to deliver customized and productized API bundles to your developer community. This metadata can include: - Scope - Environments - API proxies - Extensible profile API products enable you repackage APIs on the fly, without having to do any additional coding or configuration. Apigee recommends that you start with a simple API product including only required elements. You then provision credentials to apps to enable them to start testing your APIs. After you have authentication and authorization working against a simple API product, you can iterate to create finer-grained API products, defining different sets of API resources for each API product. **WARNING:** - If you don't specify an API proxy in the request body, *any* app associated with the product can make calls to *any* API in your entire organization. - If you don't specify an environment in the request body, the product allows access to all environments. For more information, see What is an API product?.

      Args:
        request: (ApigeeOrganizationsApiproductsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ApiProduct) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apiproducts',
        http_method='POST',
        method_id='apigee.organizations.apiproducts.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/apiproducts',
        request_field='googleCloudApigeeV1ApiProduct',
        request_type_name='ApigeeOrganizationsApiproductsCreateRequest',
        response_type_name='GoogleCloudApigeeV1ApiProduct',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an API product from an organization. Deleting an API product causes app requests to the resource URIs defined in the API product to fail. Ensure that you create a new API product to serve existing apps, unless your intention is to disable access to the resources defined in the API product. The API product name required in the request URL is the internal name of the product, not the display name. While they may be the same, it depends on whether the API product was created via the UI or the API. View the list of API products to verify the internal name.

      Args:
        request: (ApigeeOrganizationsApiproductsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ApiProduct) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apiproducts/{apiproductsId}',
        http_method='DELETE',
        method_id='apigee.organizations.apiproducts.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsApiproductsDeleteRequest',
        response_type_name='GoogleCloudApigeeV1ApiProduct',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets configuration details for an API product. The API product name required in the request URL is the internal name of the product, not the display name. While they may be the same, it depends on whether the API product was created via the UI or the API. View the list of API products to verify the internal name.

      Args:
        request: (ApigeeOrganizationsApiproductsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ApiProduct) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apiproducts/{apiproductsId}',
        http_method='GET',
        method_id='apigee.organizations.apiproducts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsApiproductsGetRequest',
        response_type_name='GoogleCloudApigeeV1ApiProduct',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all API product names for an organization. Filter the list by passing an `attributename` and `attibutevalue`. The maximum number of API products returned is 1000. You can paginate the list of API products returned using the `startKey` and `count` query parameters.

      Args:
        request: (ApigeeOrganizationsApiproductsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListApiProductsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apiproducts',
        http_method='GET',
        method_id='apigee.organizations.apiproducts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['attributename', 'attributevalue', 'count', 'expand', 'filter', 'pageSize', 'pageToken', 'startKey'],
        relative_path='v1/{+parent}/apiproducts',
        request_field='',
        request_type_name='ApigeeOrganizationsApiproductsListRequest',
        response_type_name='GoogleCloudApigeeV1ListApiProductsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates an existing API product. You must include all required values, whether or not you are updating them, as well as any optional values that you are updating. The API product name required in the request URL is the internal name of the product, not the display name. While they may be the same, it depends on whether the API product was created via UI or API. View the list of API products to identify their internal names.

      Args:
        request: (GoogleCloudApigeeV1ApiProduct) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ApiProduct) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apiproducts/{apiproductsId}',
        http_method='PUT',
        method_id='apigee.organizations.apiproducts.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='GoogleCloudApigeeV1ApiProduct',
        response_type_name='GoogleCloudApigeeV1ApiProduct',
        supports_download=False,
    )

  class OrganizationsApisDeploymentsService(base_api.BaseApiService):
    """Service class for the organizations_apis_deployments resource."""

    _NAME = 'organizations_apis_deployments'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsApisDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all deployments of an API proxy.

      Args:
        request: (ApigeeOrganizationsApisDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apis/{apisId}/deployments',
        http_method='GET',
        method_id='apigee.organizations.apis.deployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/deployments',
        request_field='',
        request_type_name='ApigeeOrganizationsApisDeploymentsListRequest',
        response_type_name='GoogleCloudApigeeV1ListDeploymentsResponse',
        supports_download=False,
    )

  class OrganizationsApisKeyvaluemapsEntriesService(base_api.BaseApiService):
    """Service class for the organizations_apis_keyvaluemaps_entries resource."""

    _NAME = 'organizations_apis_keyvaluemaps_entries'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsApisKeyvaluemapsEntriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates key value entries in a key value map scoped to an organization, environment, or API proxy. **Note**: Supported for Apigee hybrid 1.8.x and higher.

      Args:
        request: (ApigeeOrganizationsApisKeyvaluemapsEntriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1KeyValueEntry) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apis/{apisId}/keyvaluemaps/{keyvaluemapsId}/entries',
        http_method='POST',
        method_id='apigee.organizations.apis.keyvaluemaps.entries.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/entries',
        request_field='googleCloudApigeeV1KeyValueEntry',
        request_type_name='ApigeeOrganizationsApisKeyvaluemapsEntriesCreateRequest',
        response_type_name='GoogleCloudApigeeV1KeyValueEntry',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a key value entry from a key value map scoped to an organization, environment, or API proxy. **Notes:** * After you delete the key value entry, the policy consuming the entry will continue to function with its cached values for a few minutes. This is expected behavior. * Supported for Apigee hybrid 1.8.x and higher.

      Args:
        request: (ApigeeOrganizationsApisKeyvaluemapsEntriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1KeyValueEntry) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apis/{apisId}/keyvaluemaps/{keyvaluemapsId}/entries/{entriesId}',
        http_method='DELETE',
        method_id='apigee.organizations.apis.keyvaluemaps.entries.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsApisKeyvaluemapsEntriesDeleteRequest',
        response_type_name='GoogleCloudApigeeV1KeyValueEntry',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get the key value entry value for a key value map scoped to an organization, environment, or API proxy. **Note**: Supported for Apigee hybrid 1.8.x and higher.

      Args:
        request: (ApigeeOrganizationsApisKeyvaluemapsEntriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1KeyValueEntry) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apis/{apisId}/keyvaluemaps/{keyvaluemapsId}/entries/{entriesId}',
        http_method='GET',
        method_id='apigee.organizations.apis.keyvaluemaps.entries.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsApisKeyvaluemapsEntriesGetRequest',
        response_type_name='GoogleCloudApigeeV1KeyValueEntry',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists key value entries for key values maps scoped to an organization, environment, or API proxy. **Note**: Supported for Apigee hybrid 1.8.x and higher.

      Args:
        request: (ApigeeOrganizationsApisKeyvaluemapsEntriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListKeyValueEntriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apis/{apisId}/keyvaluemaps/{keyvaluemapsId}/entries',
        http_method='GET',
        method_id='apigee.organizations.apis.keyvaluemaps.entries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/entries',
        request_field='',
        request_type_name='ApigeeOrganizationsApisKeyvaluemapsEntriesListRequest',
        response_type_name='GoogleCloudApigeeV1ListKeyValueEntriesResponse',
        supports_download=False,
    )

  class OrganizationsApisKeyvaluemapsService(base_api.BaseApiService):
    """Service class for the organizations_apis_keyvaluemaps resource."""

    _NAME = 'organizations_apis_keyvaluemaps'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsApisKeyvaluemapsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a key value map in an API proxy.

      Args:
        request: (ApigeeOrganizationsApisKeyvaluemapsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1KeyValueMap) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apis/{apisId}/keyvaluemaps',
        http_method='POST',
        method_id='apigee.organizations.apis.keyvaluemaps.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/keyvaluemaps',
        request_field='googleCloudApigeeV1KeyValueMap',
        request_type_name='ApigeeOrganizationsApisKeyvaluemapsCreateRequest',
        response_type_name='GoogleCloudApigeeV1KeyValueMap',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a key value map from an API proxy.

      Args:
        request: (ApigeeOrganizationsApisKeyvaluemapsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1KeyValueMap) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apis/{apisId}/keyvaluemaps/{keyvaluemapsId}',
        http_method='DELETE',
        method_id='apigee.organizations.apis.keyvaluemaps.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsApisKeyvaluemapsDeleteRequest',
        response_type_name='GoogleCloudApigeeV1KeyValueMap',
        supports_download=False,
    )

  class OrganizationsApisRevisionsDeploymentsService(base_api.BaseApiService):
    """Service class for the organizations_apis_revisions_deployments resource."""

    _NAME = 'organizations_apis_revisions_deployments'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsApisRevisionsDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all deployments of an API proxy revision.

      Args:
        request: (ApigeeOrganizationsApisRevisionsDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apis/{apisId}/revisions/{revisionsId}/deployments',
        http_method='GET',
        method_id='apigee.organizations.apis.revisions.deployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/deployments',
        request_field='',
        request_type_name='ApigeeOrganizationsApisRevisionsDeploymentsListRequest',
        response_type_name='GoogleCloudApigeeV1ListDeploymentsResponse',
        supports_download=False,
    )

  class OrganizationsApisRevisionsService(base_api.BaseApiService):
    """Service class for the organizations_apis_revisions resource."""

    _NAME = 'organizations_apis_revisions'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsApisRevisionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes an API proxy revision and all policies, resources, endpoints, and revisions associated with it. The API proxy revision must be undeployed before you can delete it.

      Args:
        request: (ApigeeOrganizationsApisRevisionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ApiProxyRevision) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apis/{apisId}/revisions/{revisionsId}',
        http_method='DELETE',
        method_id='apigee.organizations.apis.revisions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsApisRevisionsDeleteRequest',
        response_type_name='GoogleCloudApigeeV1ApiProxyRevision',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an API proxy revision. To download the API proxy configuration bundle for the specified revision as a zip file, set the `format` query parameter to `bundle`. If you are using curl, specify `-o filename.zip` to save the output to a file; otherwise, it displays to `stdout`. Then, develop the API proxy configuration locally and upload the updated API proxy configuration revision, as described in [updateApiProxyRevision](updateApiProxyRevision).

      Args:
        request: (ApigeeOrganizationsApisRevisionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apis/{apisId}/revisions/{revisionsId}',
        http_method='GET',
        method_id='apigee.organizations.apis.revisions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['format'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsApisRevisionsGetRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

    def UpdateApiProxyRevision(self, request, global_params=None):
      r"""Updates an existing API proxy revision by uploading the API proxy configuration bundle as a zip file from your local machine. You can update only API proxy revisions that have never been deployed. After deployment, an API proxy revision becomes immutable, even if it is undeployed. Set the `Content-Type` header to either `multipart/form-data` or `application/octet-stream`.

      Args:
        request: (ApigeeOrganizationsApisRevisionsUpdateApiProxyRevisionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ApiProxyRevision) The response message.
      """
      config = self.GetMethodConfig('UpdateApiProxyRevision')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateApiProxyRevision.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apis/{apisId}/revisions/{revisionsId}',
        http_method='POST',
        method_id='apigee.organizations.apis.revisions.updateApiProxyRevision',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['validate'],
        relative_path='v1/{+name}',
        request_field='googleApiHttpBody',
        request_type_name='ApigeeOrganizationsApisRevisionsUpdateApiProxyRevisionRequest',
        response_type_name='GoogleCloudApigeeV1ApiProxyRevision',
        supports_download=False,
    )

  class OrganizationsApisService(base_api.BaseApiService):
    """Service class for the organizations_apis resource."""

    _NAME = 'organizations_apis'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsApisService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an API proxy. The API proxy created will not be accessible at runtime until it is deployed to an environment. Create a new API proxy by setting the `name` query parameter to the name of the API proxy. Import an API proxy configuration bundle stored in zip format on your local machine to your organization by doing the following: * Set the `name` query parameter to the name of the API proxy. * Set the `action` query parameter to `import`. * Set the `Content-Type` header to `multipart/form-data`. * Pass as a file the name of API proxy configuration bundle stored in zip format on your local machine using the `file` form field. **Note**: To validate the API proxy configuration bundle only without importing it, set the `action` query parameter to `validate`. When importing an API proxy configuration bundle, if the API proxy does not exist, it will be created. If the API proxy exists, then a new revision is created. Invalid API proxy configurations are rejected, and a list of validation errors is returned to the client.

      Args:
        request: (ApigeeOrganizationsApisCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ApiProxyRevision) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apis',
        http_method='POST',
        method_id='apigee.organizations.apis.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['action', 'name', 'validate'],
        relative_path='v1/{+parent}/apis',
        request_field='googleApiHttpBody',
        request_type_name='ApigeeOrganizationsApisCreateRequest',
        response_type_name='GoogleCloudApigeeV1ApiProxyRevision',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an API proxy and all associated endpoints, policies, resources, and revisions. The API proxy must be undeployed before you can delete it.

      Args:
        request: (ApigeeOrganizationsApisDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ApiProxy) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apis/{apisId}',
        http_method='DELETE',
        method_id='apigee.organizations.apis.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsApisDeleteRequest',
        response_type_name='GoogleCloudApigeeV1ApiProxy',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an API proxy including a list of existing revisions.

      Args:
        request: (ApigeeOrganizationsApisGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ApiProxy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apis/{apisId}',
        http_method='GET',
        method_id='apigee.organizations.apis.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsApisGetRequest',
        response_type_name='GoogleCloudApigeeV1ApiProxy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the names of all API proxies in an organization. The names returned correspond to the names defined in the configuration files for each API proxy.

      Args:
        request: (ApigeeOrganizationsApisListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListApiProxiesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apis',
        http_method='GET',
        method_id='apigee.organizations.apis.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['includeMetaData', 'includeRevisions'],
        relative_path='v1/{+parent}/apis',
        request_field='',
        request_type_name='ApigeeOrganizationsApisListRequest',
        response_type_name='GoogleCloudApigeeV1ListApiProxiesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing API proxy.

      Args:
        request: (ApigeeOrganizationsApisPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ApiProxy) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apis/{apisId}',
        http_method='PATCH',
        method_id='apigee.organizations.apis.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1ApiProxy',
        request_type_name='ApigeeOrganizationsApisPatchRequest',
        response_type_name='GoogleCloudApigeeV1ApiProxy',
        supports_download=False,
    )

  class OrganizationsAppgroupsAppsKeysApiproductsService(base_api.BaseApiService):
    """Service class for the organizations_appgroups_apps_keys_apiproducts resource."""

    _NAME = 'organizations_appgroups_apps_keys_apiproducts'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsAppgroupsAppsKeysApiproductsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Removes an API product from an app's consumer key. After the API product is removed, the app cannot access the API resources defined in that API product. **Note**: The consumer key is not removed, only its association with the API product.

      Args:
        request: (ApigeeOrganizationsAppgroupsAppsKeysApiproductsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AppGroupAppKey) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/appgroups/{appgroupsId}/apps/{appsId}/keys/{keysId}/apiproducts/{apiproductsId}',
        http_method='DELETE',
        method_id='apigee.organizations.appgroups.apps.keys.apiproducts.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsAppgroupsAppsKeysApiproductsDeleteRequest',
        response_type_name='GoogleCloudApigeeV1AppGroupAppKey',
        supports_download=False,
    )

    def UpdateAppGroupAppKeyApiProduct(self, request, global_params=None):
      r"""Approves or revokes the consumer key for an API product. After a consumer key is approved, the app can use it to access APIs. A consumer key that is revoked or pending cannot be used to access an API. Any access tokens associated with a revoked consumer key will remain active. However, Apigee checks the status of the consumer key and if set to `revoked` will not allow access to the API.

      Args:
        request: (ApigeeOrganizationsAppgroupsAppsKeysApiproductsUpdateAppGroupAppKeyApiProductRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('UpdateAppGroupAppKeyApiProduct')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateAppGroupAppKeyApiProduct.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/appgroups/{appgroupsId}/apps/{appsId}/keys/{keysId}/apiproducts/{apiproductsId}',
        http_method='POST',
        method_id='apigee.organizations.appgroups.apps.keys.apiproducts.updateAppGroupAppKeyApiProduct',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['action'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsAppgroupsAppsKeysApiproductsUpdateAppGroupAppKeyApiProductRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

  class OrganizationsAppgroupsAppsKeysService(base_api.BaseApiService):
    """Service class for the organizations_appgroups_apps_keys resource."""

    _NAME = 'organizations_appgroups_apps_keys'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsAppgroupsAppsKeysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a custom consumer key and secret for a AppGroup app. This is particularly useful if you want to migrate existing consumer keys and secrets to Apigee from another system. Consumer keys and secrets can contain letters, numbers, underscores, and hyphens. No other special characters are allowed. To avoid service disruptions, a consumer key and secret should not exceed 2 KBs each. **Note**: When creating the consumer key and secret, an association to API products will not be made. Therefore, you should not specify the associated API products in your request. Instead, use the ProductizeAppGroupAppKey API to make the association after the consumer key and secret are created. If a consumer key and secret already exist, you can keep them or delete them using the DeleteAppGroupAppKey API.

      Args:
        request: (ApigeeOrganizationsAppgroupsAppsKeysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AppGroupAppKey) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/appgroups/{appgroupsId}/apps/{appsId}/keys',
        http_method='POST',
        method_id='apigee.organizations.appgroups.apps.keys.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/keys',
        request_field='googleCloudApigeeV1AppGroupAppKey',
        request_type_name='ApigeeOrganizationsAppgroupsAppsKeysCreateRequest',
        response_type_name='GoogleCloudApigeeV1AppGroupAppKey',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an app's consumer key and removes all API products associated with the app. After the consumer key is deleted, it cannot be used to access any APIs.

      Args:
        request: (ApigeeOrganizationsAppgroupsAppsKeysDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AppGroupAppKey) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/appgroups/{appgroupsId}/apps/{appsId}/keys/{keysId}',
        http_method='DELETE',
        method_id='apigee.organizations.appgroups.apps.keys.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsAppgroupsAppsKeysDeleteRequest',
        response_type_name='GoogleCloudApigeeV1AppGroupAppKey',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details for a consumer key for a AppGroup app, including the key and secret value, associated API products, and other information.

      Args:
        request: (ApigeeOrganizationsAppgroupsAppsKeysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AppGroupAppKey) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/appgroups/{appgroupsId}/apps/{appsId}/keys/{keysId}',
        http_method='GET',
        method_id='apigee.organizations.appgroups.apps.keys.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsAppgroupsAppsKeysGetRequest',
        response_type_name='GoogleCloudApigeeV1AppGroupAppKey',
        supports_download=False,
    )

    def UpdateAppGroupAppKey(self, request, global_params=None):
      r"""Adds an API product to an AppGroupAppKey, enabling the app that holds the key to access the API resources bundled in the API product. In addition, you can add attributes to the AppGroupAppKey. This API replaces the existing attributes with those specified in the request. Include or exclude any existing attributes that you want to retain or delete, respectively. You can use the same key to access all API products associated with the app.

      Args:
        request: (ApigeeOrganizationsAppgroupsAppsKeysUpdateAppGroupAppKeyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AppGroupAppKey) The response message.
      """
      config = self.GetMethodConfig('UpdateAppGroupAppKey')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateAppGroupAppKey.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/appgroups/{appgroupsId}/apps/{appsId}/keys/{keysId}',
        http_method='POST',
        method_id='apigee.organizations.appgroups.apps.keys.updateAppGroupAppKey',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1UpdateAppGroupAppKeyRequest',
        request_type_name='ApigeeOrganizationsAppgroupsAppsKeysUpdateAppGroupAppKeyRequest',
        response_type_name='GoogleCloudApigeeV1AppGroupAppKey',
        supports_download=False,
    )

  class OrganizationsAppgroupsAppsService(base_api.BaseApiService):
    """Service class for the organizations_appgroups_apps resource."""

    _NAME = 'organizations_appgroups_apps'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsAppgroupsAppsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an app and associates it with an AppGroup. This API associates the AppGroup app with the specified API product and auto-generates an API key for the app to use in calls to API proxies inside that API product. The `name` is the unique ID of the app that you can use in API calls.

      Args:
        request: (ApigeeOrganizationsAppgroupsAppsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AppGroupApp) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/appgroups/{appgroupsId}/apps',
        http_method='POST',
        method_id='apigee.organizations.appgroups.apps.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/apps',
        request_field='googleCloudApigeeV1AppGroupApp',
        request_type_name='ApigeeOrganizationsAppgroupsAppsCreateRequest',
        response_type_name='GoogleCloudApigeeV1AppGroupApp',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an AppGroup app. **Note**: The delete operation is asynchronous. The AppGroup app is deleted immediately, but its associated resources, such as app keys or access tokens, may take anywhere from a few seconds to a few minutes to be deleted.

      Args:
        request: (ApigeeOrganizationsAppgroupsAppsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AppGroupApp) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/appgroups/{appgroupsId}/apps/{appsId}',
        http_method='DELETE',
        method_id='apigee.organizations.appgroups.apps.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsAppgroupsAppsDeleteRequest',
        response_type_name='GoogleCloudApigeeV1AppGroupApp',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the details for an AppGroup app.

      Args:
        request: (ApigeeOrganizationsAppgroupsAppsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AppGroupApp) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/appgroups/{appgroupsId}/apps/{appsId}',
        http_method='GET',
        method_id='apigee.organizations.appgroups.apps.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsAppgroupsAppsGetRequest',
        response_type_name='GoogleCloudApigeeV1AppGroupApp',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all apps created by an AppGroup in an Apigee organization. Optionally, you can request an expanded view of the AppGroup apps. Lists all AppGroupApps in an AppGroup. A maximum of 1000 AppGroup apps are returned in the response if PageSize is not specified, or if the PageSize is greater than 1000.

      Args:
        request: (ApigeeOrganizationsAppgroupsAppsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListAppGroupAppsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/appgroups/{appgroupsId}/apps',
        http_method='GET',
        method_id='apigee.organizations.appgroups.apps.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/apps',
        request_field='',
        request_type_name='ApigeeOrganizationsAppgroupsAppsListRequest',
        response_type_name='GoogleCloudApigeeV1ListAppGroupAppsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates the details for an AppGroup app. In addition, you can add an API product to an AppGroup app and automatically generate an API key for the app to use when calling APIs in the API product. If you want to use an existing API key for the API product, add the API product to the API key using the UpdateAppGroupAppKey API. Using this API, you cannot update the app name, as it is the primary key used to identify the app and cannot be changed. This API replaces the existing attributes with those specified in the request. Include or exclude any existing attributes that you want to retain or delete, respectively.

      Args:
        request: (ApigeeOrganizationsAppgroupsAppsUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AppGroupApp) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/appgroups/{appgroupsId}/apps/{appsId}',
        http_method='PUT',
        method_id='apigee.organizations.appgroups.apps.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['action'],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1AppGroupApp',
        request_type_name='ApigeeOrganizationsAppgroupsAppsUpdateRequest',
        response_type_name='GoogleCloudApigeeV1AppGroupApp',
        supports_download=False,
    )

  class OrganizationsAppgroupsService(base_api.BaseApiService):
    """Service class for the organizations_appgroups resource."""

    _NAME = 'organizations_appgroups'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsAppgroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an AppGroup. Once created, user can register apps under the AppGroup to obtain secret key and password. At creation time, the AppGroup's state is set as `active`. The attribute `Attribute` with key `attribute_name` as `__apigee_reserved__developer_details` can be used to store developers and their roles. The JSON format expected is: [ { "developer_id":"", "roles":[ "" ] } ] and is dealt in base64encoded format. Etag will be available in attribute `Attribute` with key `attribute_name` as `__apigee_reserved__developer_details_etag` for that AppGroup.

      Args:
        request: (ApigeeOrganizationsAppgroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AppGroup) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/appgroups',
        http_method='POST',
        method_id='apigee.organizations.appgroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/appgroups',
        request_field='googleCloudApigeeV1AppGroup',
        request_type_name='ApigeeOrganizationsAppgroupsCreateRequest',
        response_type_name='GoogleCloudApigeeV1AppGroup',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an AppGroup. All app and API keys associations with the AppGroup are also removed. **Warning**: This API will permanently delete the AppGroup and related artifacts. **Note**: The delete operation is asynchronous. The AppGroup app is deleted immediately, but its associated resources, such as apps and API keys, may take anywhere from a few seconds to a few minutes to be deleted.

      Args:
        request: (ApigeeOrganizationsAppgroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AppGroup) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/appgroups/{appgroupsId}',
        http_method='DELETE',
        method_id='apigee.organizations.appgroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsAppgroupsDeleteRequest',
        response_type_name='GoogleCloudApigeeV1AppGroup',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the AppGroup details for the provided AppGroup name in the request URI.

      Args:
        request: (ApigeeOrganizationsAppgroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AppGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/appgroups/{appgroupsId}',
        http_method='GET',
        method_id='apigee.organizations.appgroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsAppgroupsGetRequest',
        response_type_name='GoogleCloudApigeeV1AppGroup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all AppGroups in an organization. A maximum of 1000 AppGroups are returned in the response if PageSize is not specified, or if the PageSize is greater than 1000.

      Args:
        request: (ApigeeOrganizationsAppgroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListAppGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/appgroups',
        http_method='GET',
        method_id='apigee.organizations.appgroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/appgroups',
        request_field='',
        request_type_name='ApigeeOrganizationsAppgroupsListRequest',
        response_type_name='GoogleCloudApigeeV1ListAppGroupsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates an appGroup. This API replaces the existing appGroup details with those specified in the request. Include or exclude any existing details that you want to retain or delete, respectively. Note that the state of the AppGroup should be updated using `action`, and not via AppGroup. The custom attribute limit is 1000, and is how `__apigee_reserved__developer_details` can be updated. **Note**: OAuth access tokens and Key Management Service (KMS) entities (apps, developers, and API products) are cached for 180 seconds (current default). Any custom attributes associated with these entities are cached for at least 180 seconds after the entity is accessed at runtime. Therefore, an `ExpiresIn` element on the OAuthV2 policy won't be able to expire an access token in less than 180 seconds.

      Args:
        request: (ApigeeOrganizationsAppgroupsUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AppGroup) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/appgroups/{appgroupsId}',
        http_method='PUT',
        method_id='apigee.organizations.appgroups.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['action'],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1AppGroup',
        request_type_name='ApigeeOrganizationsAppgroupsUpdateRequest',
        response_type_name='GoogleCloudApigeeV1AppGroup',
        supports_download=False,
    )

  class OrganizationsAppsService(base_api.BaseApiService):
    """Service class for the organizations_apps resource."""

    _NAME = 'organizations_apps'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsAppsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the app profile for the specified app ID.

      Args:
        request: (ApigeeOrganizationsAppsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1App) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apps/{appsId}',
        http_method='GET',
        method_id='apigee.organizations.apps.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsAppsGetRequest',
        response_type_name='GoogleCloudApigeeV1App',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists IDs of apps within an organization that have the specified app status (approved or revoked) or are of the specified app type (developer or company).

      Args:
        request: (ApigeeOrganizationsAppsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListAppsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/apps',
        http_method='GET',
        method_id='apigee.organizations.apps.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['apiProduct', 'apptype', 'expand', 'filter', 'ids', 'includeCred', 'keyStatus', 'pageSize', 'pageToken', 'rows', 'startKey', 'status'],
        relative_path='v1/{+parent}/apps',
        request_field='',
        request_type_name='ApigeeOrganizationsAppsListRequest',
        response_type_name='GoogleCloudApigeeV1ListAppsResponse',
        supports_download=False,
    )

  class OrganizationsDatacollectorsService(base_api.BaseApiService):
    """Service class for the organizations_datacollectors resource."""

    _NAME = 'organizations_datacollectors'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsDatacollectorsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new data collector.

      Args:
        request: (ApigeeOrganizationsDatacollectorsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DataCollector) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/datacollectors',
        http_method='POST',
        method_id='apigee.organizations.datacollectors.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['dataCollectorId'],
        relative_path='v1/{+parent}/datacollectors',
        request_field='googleCloudApigeeV1DataCollector',
        request_type_name='ApigeeOrganizationsDatacollectorsCreateRequest',
        response_type_name='GoogleCloudApigeeV1DataCollector',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a data collector.

      Args:
        request: (ApigeeOrganizationsDatacollectorsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/datacollectors/{datacollectorsId}',
        http_method='DELETE',
        method_id='apigee.organizations.datacollectors.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDatacollectorsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a data collector.

      Args:
        request: (ApigeeOrganizationsDatacollectorsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DataCollector) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/datacollectors/{datacollectorsId}',
        http_method='GET',
        method_id='apigee.organizations.datacollectors.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDatacollectorsGetRequest',
        response_type_name='GoogleCloudApigeeV1DataCollector',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all data collectors.

      Args:
        request: (ApigeeOrganizationsDatacollectorsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListDataCollectorsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/datacollectors',
        http_method='GET',
        method_id='apigee.organizations.datacollectors.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/datacollectors',
        request_field='',
        request_type_name='ApigeeOrganizationsDatacollectorsListRequest',
        response_type_name='GoogleCloudApigeeV1ListDataCollectorsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a data collector.

      Args:
        request: (ApigeeOrganizationsDatacollectorsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DataCollector) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/datacollectors/{datacollectorsId}',
        http_method='PATCH',
        method_id='apigee.organizations.datacollectors.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1DataCollector',
        request_type_name='ApigeeOrganizationsDatacollectorsPatchRequest',
        response_type_name='GoogleCloudApigeeV1DataCollector',
        supports_download=False,
    )

  class OrganizationsDeploymentsService(base_api.BaseApiService):
    """Service class for the organizations_deployments resource."""

    _NAME = 'organizations_deployments'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all deployments of API proxies or shared flows.

      Args:
        request: (ApigeeOrganizationsDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/deployments',
        http_method='GET',
        method_id='apigee.organizations.deployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['sharedFlows'],
        relative_path='v1/{+parent}/deployments',
        request_field='',
        request_type_name='ApigeeOrganizationsDeploymentsListRequest',
        response_type_name='GoogleCloudApigeeV1ListDeploymentsResponse',
        supports_download=False,
    )

  class OrganizationsDevelopersAppsAttributesService(base_api.BaseApiService):
    """Service class for the organizations_developers_apps_attributes resource."""

    _NAME = 'organizations_developers_apps_attributes'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsDevelopersAppsAttributesService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a developer app attribute.

      Args:
        request: (ApigeeOrganizationsDevelopersAppsAttributesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Attribute) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps/{appsId}/attributes/{attributesId}',
        http_method='DELETE',
        method_id='apigee.organizations.developers.apps.attributes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersAppsAttributesDeleteRequest',
        response_type_name='GoogleCloudApigeeV1Attribute',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns a developer app attribute.

      Args:
        request: (ApigeeOrganizationsDevelopersAppsAttributesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Attribute) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps/{appsId}/attributes/{attributesId}',
        http_method='GET',
        method_id='apigee.organizations.developers.apps.attributes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersAppsAttributesGetRequest',
        response_type_name='GoogleCloudApigeeV1Attribute',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of all developer app attributes.

      Args:
        request: (ApigeeOrganizationsDevelopersAppsAttributesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Attributes) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps/{appsId}/attributes',
        http_method='GET',
        method_id='apigee.organizations.developers.apps.attributes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/attributes',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersAppsAttributesListRequest',
        response_type_name='GoogleCloudApigeeV1Attributes',
        supports_download=False,
    )

    def UpdateDeveloperAppAttribute(self, request, global_params=None):
      r"""Updates a developer app attribute. **Note**: OAuth access tokens and Key Management Service (KMS) entities (apps, developers, and API products) are cached for 180 seconds (current default). Any custom attributes associated with these entities are cached for at least 180 seconds after the entity is accessed at runtime. Therefore, an `ExpiresIn` element on the OAuthV2 policy won't be able to expire an access token in less than 180 seconds.

      Args:
        request: (GoogleCloudApigeeV1Attribute) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Attribute) The response message.
      """
      config = self.GetMethodConfig('UpdateDeveloperAppAttribute')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateDeveloperAppAttribute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps/{appsId}/attributes/{attributesId}',
        http_method='POST',
        method_id='apigee.organizations.developers.apps.attributes.updateDeveloperAppAttribute',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='GoogleCloudApigeeV1Attribute',
        response_type_name='GoogleCloudApigeeV1Attribute',
        supports_download=False,
    )

  class OrganizationsDevelopersAppsKeysApiproductsService(base_api.BaseApiService):
    """Service class for the organizations_developers_apps_keys_apiproducts resource."""

    _NAME = 'organizations_developers_apps_keys_apiproducts'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsDevelopersAppsKeysApiproductsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Removes an API product from an app's consumer key. After the API product is removed, the app cannot access the API resources defined in that API product. **Note**: The consumer key is not removed, only its association with the API product.

      Args:
        request: (ApigeeOrganizationsDevelopersAppsKeysApiproductsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperAppKey) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps/{appsId}/keys/{keysId}/apiproducts/{apiproductsId}',
        http_method='DELETE',
        method_id='apigee.organizations.developers.apps.keys.apiproducts.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersAppsKeysApiproductsDeleteRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperAppKey',
        supports_download=False,
    )

    def UpdateDeveloperAppKeyApiProduct(self, request, global_params=None):
      r"""Approves or revokes the consumer key for an API product. After a consumer key is approved, the app can use it to access APIs. A consumer key that is revoked or pending cannot be used to access an API. Any access tokens associated with a revoked consumer key will remain active. However, Apigee checks the status of the consumer key and if set to `revoked` will not allow access to the API.

      Args:
        request: (ApigeeOrganizationsDevelopersAppsKeysApiproductsUpdateDeveloperAppKeyApiProductRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('UpdateDeveloperAppKeyApiProduct')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateDeveloperAppKeyApiProduct.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps/{appsId}/keys/{keysId}/apiproducts/{apiproductsId}',
        http_method='POST',
        method_id='apigee.organizations.developers.apps.keys.apiproducts.updateDeveloperAppKeyApiProduct',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['action'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersAppsKeysApiproductsUpdateDeveloperAppKeyApiProductRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

  class OrganizationsDevelopersAppsKeysCreateService(base_api.BaseApiService):
    """Service class for the organizations_developers_apps_keys_create resource."""

    _NAME = 'organizations_developers_apps_keys_create'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsDevelopersAppsKeysCreateService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a custom consumer key and secret for a developer app. This is particularly useful if you want to migrate existing consumer keys and secrets to Apigee from another system. Consumer keys and secrets can contain letters, numbers, underscores, and hyphens. No other special characters are allowed. To avoid service disruptions, a consumer key and secret should not exceed 2 KBs each. **Note**: When creating the consumer key and secret, an association to API products will not be made. Therefore, you should not specify the associated API products in your request. Instead, use the UpdateDeveloperAppKey API to make the association after the consumer key and secret are created. If a consumer key and secret already exist, you can keep them or delete them using the DeleteDeveloperAppKey API. **Note**: All keys start out with status=approved, even if status=revoked is passed when the key is created. To revoke a key, use the UpdateDeveloperAppKey API.

      Args:
        request: (ApigeeOrganizationsDevelopersAppsKeysCreateCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperAppKey) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps/{appsId}/keys/create',
        http_method='POST',
        method_id='apigee.organizations.developers.apps.keys.create.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/keys/create',
        request_field='googleCloudApigeeV1DeveloperAppKey',
        request_type_name='ApigeeOrganizationsDevelopersAppsKeysCreateCreateRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperAppKey',
        supports_download=False,
    )

  class OrganizationsDevelopersAppsKeysService(base_api.BaseApiService):
    """Service class for the organizations_developers_apps_keys resource."""

    _NAME = 'organizations_developers_apps_keys'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsDevelopersAppsKeysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a custom consumer key and secret for a developer app. This is particularly useful if you want to migrate existing consumer keys and secrets to Apigee from another system. Consumer keys and secrets can contain letters, numbers, underscores, and hyphens. No other special characters are allowed. To avoid service disruptions, a consumer key and secret should not exceed 2 KBs each. **Note**: When creating the consumer key and secret, an association to API products will not be made. Therefore, you should not specify the associated API products in your request. Instead, use the UpdateDeveloperAppKey API to make the association after the consumer key and secret are created. If a consumer key and secret already exist, you can keep them or delete them using the DeleteDeveloperAppKey API. **Note**: All keys start out with status=approved, even if status=revoked is passed when the key is created. To revoke a key, use the UpdateDeveloperAppKey API.

      Args:
        request: (ApigeeOrganizationsDevelopersAppsKeysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperAppKey) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps/{appsId}/keys',
        http_method='POST',
        method_id='apigee.organizations.developers.apps.keys.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/keys',
        request_field='googleCloudApigeeV1DeveloperAppKey',
        request_type_name='ApigeeOrganizationsDevelopersAppsKeysCreateRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperAppKey',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an app's consumer key and removes all API products associated with the app. After the consumer key is deleted, it cannot be used to access any APIs. **Note**: After you delete a consumer key, you may want to: 1. Create a new consumer key and secret for the developer app using the CreateDeveloperAppKey API, and subsequently add an API product to the key using the UpdateDeveloperAppKey API. 2. Delete the developer app, if it is no longer required.

      Args:
        request: (ApigeeOrganizationsDevelopersAppsKeysDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperAppKey) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps/{appsId}/keys/{keysId}',
        http_method='DELETE',
        method_id='apigee.organizations.developers.apps.keys.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersAppsKeysDeleteRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperAppKey',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details for a consumer key for a developer app, including the key and secret value, associated API products, and other information.

      Args:
        request: (ApigeeOrganizationsDevelopersAppsKeysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperAppKey) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps/{appsId}/keys/{keysId}',
        http_method='GET',
        method_id='apigee.organizations.developers.apps.keys.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersAppsKeysGetRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperAppKey',
        supports_download=False,
    )

    def ReplaceDeveloperAppKey(self, request, global_params=None):
      r"""Updates the scope of an app. This API replaces the existing scopes with those specified in the request. Include or exclude any existing scopes that you want to retain or delete, respectively. The specified scopes must already be defined for the API products associated with the app. This API sets the `scopes` element under the `apiProducts` element in the attributes of the app.

      Args:
        request: (ApigeeOrganizationsDevelopersAppsKeysReplaceDeveloperAppKeyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperAppKey) The response message.
      """
      config = self.GetMethodConfig('ReplaceDeveloperAppKey')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReplaceDeveloperAppKey.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps/{appsId}/keys/{keysId}',
        http_method='PUT',
        method_id='apigee.organizations.developers.apps.keys.replaceDeveloperAppKey',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1DeveloperAppKey',
        request_type_name='ApigeeOrganizationsDevelopersAppsKeysReplaceDeveloperAppKeyRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperAppKey',
        supports_download=False,
    )

    def UpdateDeveloperAppKey(self, request, global_params=None):
      r"""Adds an API product to a developer app key, enabling the app that holds the key to access the API resources bundled in the API product. In addition, you can add attributes to a developer app key. This API replaces the existing attributes with those specified in the request. Include or exclude any existing attributes that you want to retain or delete, respectively. You can use the same key to access all API products associated with the app.

      Args:
        request: (ApigeeOrganizationsDevelopersAppsKeysUpdateDeveloperAppKeyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperAppKey) The response message.
      """
      config = self.GetMethodConfig('UpdateDeveloperAppKey')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateDeveloperAppKey.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps/{appsId}/keys/{keysId}',
        http_method='POST',
        method_id='apigee.organizations.developers.apps.keys.updateDeveloperAppKey',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['action'],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1DeveloperAppKey',
        request_type_name='ApigeeOrganizationsDevelopersAppsKeysUpdateDeveloperAppKeyRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperAppKey',
        supports_download=False,
    )

  class OrganizationsDevelopersAppsService(base_api.BaseApiService):
    """Service class for the organizations_developers_apps resource."""

    _NAME = 'organizations_developers_apps'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsDevelopersAppsService, self).__init__(client)
      self._upload_configs = {
          }

    def Attributes(self, request, global_params=None):
      r"""Updates attributes for a developer app. This API replaces the current attributes with those specified in the request.

      Args:
        request: (ApigeeOrganizationsDevelopersAppsAttributesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Attributes) The response message.
      """
      config = self.GetMethodConfig('Attributes')
      return self._RunMethod(
          config, request, global_params=global_params)

    Attributes.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps/{appsId}/attributes',
        http_method='POST',
        method_id='apigee.organizations.developers.apps.attributes',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}/attributes',
        request_field='googleCloudApigeeV1Attributes',
        request_type_name='ApigeeOrganizationsDevelopersAppsAttributesRequest',
        response_type_name='GoogleCloudApigeeV1Attributes',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates an app associated with a developer. This API associates the developer app with the specified API product and auto-generates an API key for the app to use in calls to API proxies inside that API product. The `name` is the unique ID of the app that you can use in API calls. The `DisplayName` (set as an attribute) appears in the UI. If you don't set the `DisplayName` attribute, the `name` appears in the UI.

      Args:
        request: (ApigeeOrganizationsDevelopersAppsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperApp) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps',
        http_method='POST',
        method_id='apigee.organizations.developers.apps.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/apps',
        request_field='googleCloudApigeeV1DeveloperApp',
        request_type_name='ApigeeOrganizationsDevelopersAppsCreateRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperApp',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a developer app. **Note**: The delete operation is asynchronous. The developer app is deleted immediately, but its associated resources, such as app keys or access tokens, may take anywhere from a few seconds to a few minutes to be deleted.

      Args:
        request: (ApigeeOrganizationsDevelopersAppsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperApp) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps/{appsId}',
        http_method='DELETE',
        method_id='apigee.organizations.developers.apps.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersAppsDeleteRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperApp',
        supports_download=False,
    )

    def GenerateKeyPairOrUpdateDeveloperAppStatus(self, request, global_params=None):
      r"""Manages access to a developer app by enabling you to: * Approve or revoke a developer app * Generate a new consumer key and secret for a developer app To approve or revoke a developer app, set the `action` query parameter to `approve` or `revoke`, respectively, and the `Content-Type` header to `application/octet-stream`. If a developer app is revoked, none of its API keys are valid for API calls even though the keys are still approved. If successful, the API call returns the following HTTP status code: `204 No Content` To generate a new consumer key and secret for a developer app, pass the new key/secret details. Rather than replace an existing key, this API generates a new key. In this case, multiple key pairs may be associated with a single developer app. Each key pair has an independent status (`approve` or `revoke`) and expiration time. Any approved, non-expired key can be used in an API call. For example, if you're using API key rotation, you can generate new keys with expiration times that overlap keys that are going to expire. You might also generate a new consumer key/secret if the security of the original key/secret is compromised. The `keyExpiresIn` property defines the expiration time for the API key in milliseconds. If you don't set this property or set it to `-1`, the API key never expires. **Notes**: * When generating a new key/secret, this API replaces the existing attributes, notes, and callback URLs with those specified in the request. Include or exclude any existing information that you want to retain or delete, respectively. * To migrate existing consumer keys and secrets to hybrid from another system, see the CreateDeveloperAppKey API.

      Args:
        request: (ApigeeOrganizationsDevelopersAppsGenerateKeyPairOrUpdateDeveloperAppStatusRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperApp) The response message.
      """
      config = self.GetMethodConfig('GenerateKeyPairOrUpdateDeveloperAppStatus')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateKeyPairOrUpdateDeveloperAppStatus.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps/{appsId}',
        http_method='POST',
        method_id='apigee.organizations.developers.apps.generateKeyPairOrUpdateDeveloperAppStatus',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['action'],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1DeveloperApp',
        request_type_name='ApigeeOrganizationsDevelopersAppsGenerateKeyPairOrUpdateDeveloperAppStatusRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperApp',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the details for a developer app.

      Args:
        request: (ApigeeOrganizationsDevelopersAppsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperApp) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps/{appsId}',
        http_method='GET',
        method_id='apigee.organizations.developers.apps.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['entity', 'query'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersAppsGetRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperApp',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all apps created by a developer in an Apigee organization. Optionally, you can request an expanded view of the developer apps. A maximum of 100 developer apps are returned per API call. You can paginate the list of deveoper apps returned using the `startKey` and `count` query parameters.

      Args:
        request: (ApigeeOrganizationsDevelopersAppsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListDeveloperAppsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps',
        http_method='GET',
        method_id='apigee.organizations.developers.apps.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['count', 'expand', 'shallowExpand', 'startKey'],
        relative_path='v1/{+parent}/apps',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersAppsListRequest',
        response_type_name='GoogleCloudApigeeV1ListDeveloperAppsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates the details for a developer app. In addition, you can add an API product to a developer app and automatically generate an API key for the app to use when calling APIs in the API product. If you want to use an existing API key for the API product, add the API product to the API key using the UpdateDeveloperAppKey API. Using this API, you cannot update the following: * App name as it is the primary key used to identify the app and cannot be changed. * Scopes associated with the app. Instead, use the ReplaceDeveloperAppKey API. This API replaces the existing attributes with those specified in the request. Include or exclude any existing attributes that you want to retain or delete, respectively.

      Args:
        request: (GoogleCloudApigeeV1DeveloperApp) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperApp) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/apps/{appsId}',
        http_method='PUT',
        method_id='apigee.organizations.developers.apps.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='GoogleCloudApigeeV1DeveloperApp',
        response_type_name='GoogleCloudApigeeV1DeveloperApp',
        supports_download=False,
    )

  class OrganizationsDevelopersAttributesService(base_api.BaseApiService):
    """Service class for the organizations_developers_attributes resource."""

    _NAME = 'organizations_developers_attributes'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsDevelopersAttributesService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a developer attribute.

      Args:
        request: (ApigeeOrganizationsDevelopersAttributesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Attribute) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/attributes/{attributesId}',
        http_method='DELETE',
        method_id='apigee.organizations.developers.attributes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersAttributesDeleteRequest',
        response_type_name='GoogleCloudApigeeV1Attribute',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the value of the specified developer attribute.

      Args:
        request: (ApigeeOrganizationsDevelopersAttributesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Attribute) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/attributes/{attributesId}',
        http_method='GET',
        method_id='apigee.organizations.developers.attributes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersAttributesGetRequest',
        response_type_name='GoogleCloudApigeeV1Attribute',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of all developer attributes.

      Args:
        request: (ApigeeOrganizationsDevelopersAttributesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Attributes) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/attributes',
        http_method='GET',
        method_id='apigee.organizations.developers.attributes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/attributes',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersAttributesListRequest',
        response_type_name='GoogleCloudApigeeV1Attributes',
        supports_download=False,
    )

    def UpdateDeveloperAttribute(self, request, global_params=None):
      r"""Updates a developer attribute. **Note**: OAuth access tokens and Key Management Service (KMS) entities (apps, developers, and API products) are cached for 180 seconds (default). Any custom attributes associated with these entities are cached for at least 180 seconds after the entity is accessed at runtime. Therefore, an `ExpiresIn` element on the OAuthV2 policy won't be able to expire an access token in less than 180 seconds.

      Args:
        request: (GoogleCloudApigeeV1Attribute) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Attribute) The response message.
      """
      config = self.GetMethodConfig('UpdateDeveloperAttribute')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateDeveloperAttribute.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/attributes/{attributesId}',
        http_method='POST',
        method_id='apigee.organizations.developers.attributes.updateDeveloperAttribute',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='GoogleCloudApigeeV1Attribute',
        response_type_name='GoogleCloudApigeeV1Attribute',
        supports_download=False,
    )

  class OrganizationsDevelopersBalanceService(base_api.BaseApiService):
    """Service class for the organizations_developers_balance resource."""

    _NAME = 'organizations_developers_balance'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsDevelopersBalanceService, self).__init__(client)
      self._upload_configs = {
          }

    def Adjust(self, request, global_params=None):
      r"""Adjust the prepaid balance for the developer. This API will be used in scenarios where the developer has been under-charged or over-charged.

      Args:
        request: (ApigeeOrganizationsDevelopersBalanceAdjustRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperBalance) The response message.
      """
      config = self.GetMethodConfig('Adjust')
      return self._RunMethod(
          config, request, global_params=global_params)

    Adjust.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/balance:adjust',
        http_method='POST',
        method_id='apigee.organizations.developers.balance.adjust',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:adjust',
        request_field='googleCloudApigeeV1AdjustDeveloperBalanceRequest',
        request_type_name='ApigeeOrganizationsDevelopersBalanceAdjustRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperBalance',
        supports_download=False,
    )

    def Credit(self, request, global_params=None):
      r"""Credits the account balance for the developer.

      Args:
        request: (ApigeeOrganizationsDevelopersBalanceCreditRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperBalance) The response message.
      """
      config = self.GetMethodConfig('Credit')
      return self._RunMethod(
          config, request, global_params=global_params)

    Credit.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/balance:credit',
        http_method='POST',
        method_id='apigee.organizations.developers.balance.credit',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:credit',
        request_field='googleCloudApigeeV1CreditDeveloperBalanceRequest',
        request_type_name='ApigeeOrganizationsDevelopersBalanceCreditRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperBalance',
        supports_download=False,
    )

  class OrganizationsDevelopersSubscriptionsService(base_api.BaseApiService):
    """Service class for the organizations_developers_subscriptions resource."""

    _NAME = 'organizations_developers_subscriptions'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsDevelopersSubscriptionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a subscription to an API product. .

      Args:
        request: (ApigeeOrganizationsDevelopersSubscriptionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperSubscription) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/subscriptions',
        http_method='POST',
        method_id='apigee.organizations.developers.subscriptions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/subscriptions',
        request_field='googleCloudApigeeV1DeveloperSubscription',
        request_type_name='ApigeeOrganizationsDevelopersSubscriptionsCreateRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperSubscription',
        supports_download=False,
    )

    def Expire(self, request, global_params=None):
      r"""Expires an API product subscription immediately.

      Args:
        request: (ApigeeOrganizationsDevelopersSubscriptionsExpireRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperSubscription) The response message.
      """
      config = self.GetMethodConfig('Expire')
      return self._RunMethod(
          config, request, global_params=global_params)

    Expire.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/subscriptions/{subscriptionsId}:expire',
        http_method='POST',
        method_id='apigee.organizations.developers.subscriptions.expire',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:expire',
        request_field='googleCloudApigeeV1ExpireDeveloperSubscriptionRequest',
        request_type_name='ApigeeOrganizationsDevelopersSubscriptionsExpireRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperSubscription',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details for an API product subscription.

      Args:
        request: (ApigeeOrganizationsDevelopersSubscriptionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperSubscription) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/subscriptions/{subscriptionsId}',
        http_method='GET',
        method_id='apigee.organizations.developers.subscriptions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersSubscriptionsGetRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperSubscription',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all API product subscriptions for a developer.

      Args:
        request: (ApigeeOrganizationsDevelopersSubscriptionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListDeveloperSubscriptionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/subscriptions',
        http_method='GET',
        method_id='apigee.organizations.developers.subscriptions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['count', 'startKey'],
        relative_path='v1/{+parent}/subscriptions',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersSubscriptionsListRequest',
        response_type_name='GoogleCloudApigeeV1ListDeveloperSubscriptionsResponse',
        supports_download=False,
    )

  class OrganizationsDevelopersService(base_api.BaseApiService):
    """Service class for the organizations_developers resource."""

    _NAME = 'organizations_developers'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsDevelopersService, self).__init__(client)
      self._upload_configs = {
          }

    def Attributes(self, request, global_params=None):
      r"""Updates developer attributes. This API replaces the existing attributes with those specified in the request. Add new attributes, and include or exclude any existing attributes that you want to retain or remove, respectively. The custom attribute limit is 18. **Note**: OAuth access tokens and Key Management Service (KMS) entities (apps, developers, and API products) are cached for 180 seconds (default). Any custom attributes associated with these entities are cached for at least 180 seconds after the entity is accessed at runtime. Therefore, an `ExpiresIn` element on the OAuthV2 policy won't be able to expire an access token in less than 180 seconds.

      Args:
        request: (ApigeeOrganizationsDevelopersAttributesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Attributes) The response message.
      """
      config = self.GetMethodConfig('Attributes')
      return self._RunMethod(
          config, request, global_params=global_params)

    Attributes.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/attributes',
        http_method='POST',
        method_id='apigee.organizations.developers.attributes',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/attributes',
        request_field='googleCloudApigeeV1Attributes',
        request_type_name='ApigeeOrganizationsDevelopersAttributesRequest',
        response_type_name='GoogleCloudApigeeV1Attributes',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a developer. Once created, the developer can register an app and obtain an API key. At creation time, a developer is set as `active`. To change the developer status, use the SetDeveloperStatus API.

      Args:
        request: (ApigeeOrganizationsDevelopersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Developer) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers',
        http_method='POST',
        method_id='apigee.organizations.developers.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/developers',
        request_field='googleCloudApigeeV1Developer',
        request_type_name='ApigeeOrganizationsDevelopersCreateRequest',
        response_type_name='GoogleCloudApigeeV1Developer',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a developer. All apps and API keys associated with the developer are also removed. **Warning**: This API will permanently delete the developer and related artifacts. To avoid permanently deleting developers and their artifacts, set the developer status to `inactive` using the SetDeveloperStatus API. **Note**: The delete operation is asynchronous. The developer app is deleted immediately, but its associated resources, such as apps and API keys, may take anywhere from a few seconds to a few minutes to be deleted.

      Args:
        request: (ApigeeOrganizationsDevelopersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Developer) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}',
        http_method='DELETE',
        method_id='apigee.organizations.developers.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersDeleteRequest',
        response_type_name='GoogleCloudApigeeV1Developer',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the developer details, including the developer's name, email address, apps, and other information. **Note**: The response includes only the first 100 developer apps.

      Args:
        request: (ApigeeOrganizationsDevelopersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Developer) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}',
        http_method='GET',
        method_id='apigee.organizations.developers.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['action'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersGetRequest',
        response_type_name='GoogleCloudApigeeV1Developer',
        supports_download=False,
    )

    def GetBalance(self, request, global_params=None):
      r"""Gets the account balance for the developer.

      Args:
        request: (ApigeeOrganizationsDevelopersGetBalanceRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperBalance) The response message.
      """
      config = self.GetMethodConfig('GetBalance')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetBalance.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/balance',
        http_method='GET',
        method_id='apigee.organizations.developers.getBalance',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersGetBalanceRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperBalance',
        supports_download=False,
    )

    def GetMonetizationConfig(self, request, global_params=None):
      r"""Gets the monetization configuration for the developer.

      Args:
        request: (ApigeeOrganizationsDevelopersGetMonetizationConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperMonetizationConfig) The response message.
      """
      config = self.GetMethodConfig('GetMonetizationConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetMonetizationConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/monetizationConfig',
        http_method='GET',
        method_id='apigee.organizations.developers.getMonetizationConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersGetMonetizationConfigRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperMonetizationConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all developers in an organization by email address. By default, the response does not include company developers. Set the `includeCompany` query parameter to `true` to include company developers. **Note**: A maximum of 1000 developers are returned in the response. You paginate the list of developers returned using the `startKey` and `count` query parameters.

      Args:
        request: (ApigeeOrganizationsDevelopersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListOfDevelopersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers',
        http_method='GET',
        method_id='apigee.organizations.developers.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['app', 'count', 'expand', 'filter', 'ids', 'includeCompany', 'pageSize', 'pageToken', 'startKey'],
        relative_path='v1/{+parent}/developers',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersListRequest',
        response_type_name='GoogleCloudApigeeV1ListOfDevelopersResponse',
        supports_download=False,
    )

    def SetDeveloperStatus(self, request, global_params=None):
      r"""Sets the status of a developer. A developer is `active` by default. If you set a developer's status to `inactive`, the API keys assigned to the developer apps are no longer valid even though the API keys are set to `approved`. Inactive developers can still sign in to the developer portal and create apps; however, any new API keys generated during app creation won't work. To set the status of a developer, set the `action` query parameter to `active` or `inactive`, and the `Content-Type` header to `application/octet-stream`. If successful, the API call returns the following HTTP status code: `204 No Content`.

      Args:
        request: (ApigeeOrganizationsDevelopersSetDeveloperStatusRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('SetDeveloperStatus')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetDeveloperStatus.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}',
        http_method='POST',
        method_id='apigee.organizations.developers.setDeveloperStatus',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['action'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDevelopersSetDeveloperStatusRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates a developer. This API replaces the existing developer details with those specified in the request. Include or exclude any existing details that you want to retain or delete, respectively. The custom attribute limit is 18. **Note**: OAuth access tokens and Key Management Service (KMS) entities (apps, developers, and API products) are cached for 180 seconds (current default). Any custom attributes associated with these entities are cached for at least 180 seconds after the entity is accessed at runtime. Therefore, an `ExpiresIn` element on the OAuthV2 policy won't be able to expire an access token in less than 180 seconds.

      Args:
        request: (ApigeeOrganizationsDevelopersUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Developer) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}',
        http_method='PUT',
        method_id='apigee.organizations.developers.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1Developer',
        request_type_name='ApigeeOrganizationsDevelopersUpdateRequest',
        response_type_name='GoogleCloudApigeeV1Developer',
        supports_download=False,
    )

    def UpdateMonetizationConfig(self, request, global_params=None):
      r"""Updates the monetization configuration for the developer.

      Args:
        request: (ApigeeOrganizationsDevelopersUpdateMonetizationConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeveloperMonetizationConfig) The response message.
      """
      config = self.GetMethodConfig('UpdateMonetizationConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateMonetizationConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/developers/{developersId}/monetizationConfig',
        http_method='PUT',
        method_id='apigee.organizations.developers.updateMonetizationConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1DeveloperMonetizationConfig',
        request_type_name='ApigeeOrganizationsDevelopersUpdateMonetizationConfigRequest',
        response_type_name='GoogleCloudApigeeV1DeveloperMonetizationConfig',
        supports_download=False,
    )

  class OrganizationsEndpointAttachmentsService(base_api.BaseApiService):
    """Service class for the organizations_endpointAttachments resource."""

    _NAME = 'organizations_endpointAttachments'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEndpointAttachmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an endpoint attachment. **Note:** Not supported for Apigee hybrid.

      Args:
        request: (ApigeeOrganizationsEndpointAttachmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/endpointAttachments',
        http_method='POST',
        method_id='apigee.organizations.endpointAttachments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['endpointAttachmentId'],
        relative_path='v1/{+parent}/endpointAttachments',
        request_field='googleCloudApigeeV1EndpointAttachment',
        request_type_name='ApigeeOrganizationsEndpointAttachmentsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an endpoint attachment.

      Args:
        request: (ApigeeOrganizationsEndpointAttachmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/endpointAttachments/{endpointAttachmentsId}',
        http_method='DELETE',
        method_id='apigee.organizations.endpointAttachments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEndpointAttachmentsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the endpoint attachment.

      Args:
        request: (ApigeeOrganizationsEndpointAttachmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1EndpointAttachment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/endpointAttachments/{endpointAttachmentsId}',
        http_method='GET',
        method_id='apigee.organizations.endpointAttachments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEndpointAttachmentsGetRequest',
        response_type_name='GoogleCloudApigeeV1EndpointAttachment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the endpoint attachments in an organization.

      Args:
        request: (ApigeeOrganizationsEndpointAttachmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListEndpointAttachmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/endpointAttachments',
        http_method='GET',
        method_id='apigee.organizations.endpointAttachments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/endpointAttachments',
        request_field='',
        request_type_name='ApigeeOrganizationsEndpointAttachmentsListRequest',
        response_type_name='GoogleCloudApigeeV1ListEndpointAttachmentsResponse',
        supports_download=False,
    )

  class OrganizationsEnvgroupsAttachmentsService(base_api.BaseApiService):
    """Service class for the organizations_envgroups_attachments resource."""

    _NAME = 'organizations_envgroups_attachments'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvgroupsAttachmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new attachment of an environment to an environment group.

      Args:
        request: (ApigeeOrganizationsEnvgroupsAttachmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/envgroups/{envgroupsId}/attachments',
        http_method='POST',
        method_id='apigee.organizations.envgroups.attachments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/attachments',
        request_field='googleCloudApigeeV1EnvironmentGroupAttachment',
        request_type_name='ApigeeOrganizationsEnvgroupsAttachmentsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an environment group attachment.

      Args:
        request: (ApigeeOrganizationsEnvgroupsAttachmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/envgroups/{envgroupsId}/attachments/{attachmentsId}',
        http_method='DELETE',
        method_id='apigee.organizations.envgroups.attachments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvgroupsAttachmentsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an environment group attachment.

      Args:
        request: (ApigeeOrganizationsEnvgroupsAttachmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1EnvironmentGroupAttachment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/envgroups/{envgroupsId}/attachments/{attachmentsId}',
        http_method='GET',
        method_id='apigee.organizations.envgroups.attachments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvgroupsAttachmentsGetRequest',
        response_type_name='GoogleCloudApigeeV1EnvironmentGroupAttachment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all attachments of an environment group.

      Args:
        request: (ApigeeOrganizationsEnvgroupsAttachmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListEnvironmentGroupAttachmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/envgroups/{envgroupsId}/attachments',
        http_method='GET',
        method_id='apigee.organizations.envgroups.attachments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/attachments',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvgroupsAttachmentsListRequest',
        response_type_name='GoogleCloudApigeeV1ListEnvironmentGroupAttachmentsResponse',
        supports_download=False,
    )

  class OrganizationsEnvgroupsService(base_api.BaseApiService):
    """Service class for the organizations_envgroups resource."""

    _NAME = 'organizations_envgroups'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvgroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new environment group.

      Args:
        request: (ApigeeOrganizationsEnvgroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/envgroups',
        http_method='POST',
        method_id='apigee.organizations.envgroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['name'],
        relative_path='v1/{+parent}/envgroups',
        request_field='googleCloudApigeeV1EnvironmentGroup',
        request_type_name='ApigeeOrganizationsEnvgroupsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an environment group.

      Args:
        request: (ApigeeOrganizationsEnvgroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/envgroups/{envgroupsId}',
        http_method='DELETE',
        method_id='apigee.organizations.envgroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvgroupsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an environment group.

      Args:
        request: (ApigeeOrganizationsEnvgroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1EnvironmentGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/envgroups/{envgroupsId}',
        http_method='GET',
        method_id='apigee.organizations.envgroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvgroupsGetRequest',
        response_type_name='GoogleCloudApigeeV1EnvironmentGroup',
        supports_download=False,
    )

    def GetDeployedIngressConfig(self, request, global_params=None):
      r"""Gets the deployed ingress configuration for an environment group.

      Args:
        request: (ApigeeOrganizationsEnvgroupsGetDeployedIngressConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1EnvironmentGroupConfig) The response message.
      """
      config = self.GetMethodConfig('GetDeployedIngressConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetDeployedIngressConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/envgroups/{envgroupsId}/deployedIngressConfig',
        http_method='GET',
        method_id='apigee.organizations.envgroups.getDeployedIngressConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvgroupsGetDeployedIngressConfigRequest',
        response_type_name='GoogleCloudApigeeV1EnvironmentGroupConfig',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all environment groups.

      Args:
        request: (ApigeeOrganizationsEnvgroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListEnvironmentGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/envgroups',
        http_method='GET',
        method_id='apigee.organizations.envgroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/envgroups',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvgroupsListRequest',
        response_type_name='GoogleCloudApigeeV1ListEnvironmentGroupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an environment group.

      Args:
        request: (ApigeeOrganizationsEnvgroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/envgroups/{envgroupsId}',
        http_method='PATCH',
        method_id='apigee.organizations.envgroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1EnvironmentGroup',
        request_type_name='ApigeeOrganizationsEnvgroupsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class OrganizationsEnvironmentsAnalyticsAdminService(base_api.BaseApiService):
    """Service class for the organizations_environments_analytics_admin resource."""

    _NAME = 'organizations_environments_analytics_admin'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsAnalyticsAdminService, self).__init__(client)
      self._upload_configs = {
          }

    def GetSchemav2(self, request, global_params=None):
      r"""Gets a list of metrics and dimensions that can be used to create analytics queries and reports. Each schema element contains the name of the field, its associated type, and a flag indicating whether it is a standard or custom field.

      Args:
        request: (ApigeeOrganizationsEnvironmentsAnalyticsAdminGetSchemav2Request) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Schema) The response message.
      """
      config = self.GetMethodConfig('GetSchemav2')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSchemav2.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/analytics/admin/schemav2',
        http_method='GET',
        method_id='apigee.organizations.environments.analytics.admin.getSchemav2',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['disableCache', 'type'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsAnalyticsAdminGetSchemav2Request',
        response_type_name='GoogleCloudApigeeV1Schema',
        supports_download=False,
    )

  class OrganizationsEnvironmentsAnalyticsExportsService(base_api.BaseApiService):
    """Service class for the organizations_environments_analytics_exports resource."""

    _NAME = 'organizations_environments_analytics_exports'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsAnalyticsExportsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Submit a data export job to be processed in the background. If the request is successful, the API returns a 201 status, a URI that can be used to retrieve the status of the export job, and the `state` value of "enqueued".

      Args:
        request: (ApigeeOrganizationsEnvironmentsAnalyticsExportsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Export) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/analytics/exports',
        http_method='POST',
        method_id='apigee.organizations.environments.analytics.exports.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/analytics/exports',
        request_field='googleCloudApigeeV1ExportRequest',
        request_type_name='ApigeeOrganizationsEnvironmentsAnalyticsExportsCreateRequest',
        response_type_name='GoogleCloudApigeeV1Export',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the details and status of an analytics export job. If the export job is still in progress, its `state` is set to "running". After the export job has completed successfully, its `state` is set to "completed". If the export job fails, its `state` is set to `failed`.

      Args:
        request: (ApigeeOrganizationsEnvironmentsAnalyticsExportsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Export) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/analytics/exports/{exportsId}',
        http_method='GET',
        method_id='apigee.organizations.environments.analytics.exports.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsAnalyticsExportsGetRequest',
        response_type_name='GoogleCloudApigeeV1Export',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the details and status of all analytics export jobs belonging to the parent organization and environment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsAnalyticsExportsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListExportsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/analytics/exports',
        http_method='GET',
        method_id='apigee.organizations.environments.analytics.exports.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/analytics/exports',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsAnalyticsExportsListRequest',
        response_type_name='GoogleCloudApigeeV1ListExportsResponse',
        supports_download=False,
    )

  class OrganizationsEnvironmentsAnalyticsService(base_api.BaseApiService):
    """Service class for the organizations_environments_analytics resource."""

    _NAME = 'organizations_environments_analytics'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsAnalyticsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsEnvironmentsApisDeploymentsService(base_api.BaseApiService):
    """Service class for the organizations_environments_apis_deployments resource."""

    _NAME = 'organizations_environments_apis_deployments'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsApisDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all deployments of an API proxy in an environment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsApisDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/apis/{apisId}/deployments',
        http_method='GET',
        method_id='apigee.organizations.environments.apis.deployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/deployments',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsApisDeploymentsListRequest',
        response_type_name='GoogleCloudApigeeV1ListDeploymentsResponse',
        supports_download=False,
    )

  class OrganizationsEnvironmentsApisRevisionsDebugsessionsDataService(base_api.BaseApiService):
    """Service class for the organizations_environments_apis_revisions_debugsessions_data resource."""

    _NAME = 'organizations_environments_apis_revisions_debugsessions_data'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsApisRevisionsDebugsessionsDataService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the debug data from a transaction.

      Args:
        request: (ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsDataGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DebugSessionTransaction) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/apis/{apisId}/revisions/{revisionsId}/debugsessions/{debugsessionsId}/data/{dataId}',
        http_method='GET',
        method_id='apigee.organizations.environments.apis.revisions.debugsessions.data.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsDataGetRequest',
        response_type_name='GoogleCloudApigeeV1DebugSessionTransaction',
        supports_download=False,
    )

  class OrganizationsEnvironmentsApisRevisionsDebugsessionsService(base_api.BaseApiService):
    """Service class for the organizations_environments_apis_revisions_debugsessions resource."""

    _NAME = 'organizations_environments_apis_revisions_debugsessions'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsApisRevisionsDebugsessionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a debug session for a deployed API Proxy revision.

      Args:
        request: (ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DebugSession) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/apis/{apisId}/revisions/{revisionsId}/debugsessions',
        http_method='POST',
        method_id='apigee.organizations.environments.apis.revisions.debugsessions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['timeout'],
        relative_path='v1/{+parent}/debugsessions',
        request_field='googleCloudApigeeV1DebugSession',
        request_type_name='ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsCreateRequest',
        response_type_name='GoogleCloudApigeeV1DebugSession',
        supports_download=False,
    )

    def DeleteData(self, request, global_params=None):
      r"""Deletes the data from a debug session. This does not cancel the debug session or prevent further data from being collected if the session is still active in runtime pods.

      Args:
        request: (ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsDeleteDataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('DeleteData')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteData.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/apis/{apisId}/revisions/{revisionsId}/debugsessions/{debugsessionsId}/data',
        http_method='DELETE',
        method_id='apigee.organizations.environments.apis.revisions.debugsessions.deleteData',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}/data',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsDeleteDataRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a debug session.

      Args:
        request: (ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DebugSession) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/apis/{apisId}/revisions/{revisionsId}/debugsessions/{debugsessionsId}',
        http_method='GET',
        method_id='apigee.organizations.environments.apis.revisions.debugsessions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsGetRequest',
        response_type_name='GoogleCloudApigeeV1DebugSession',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists debug sessions that are currently active in the given API Proxy revision.

      Args:
        request: (ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListDebugSessionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/apis/{apisId}/revisions/{revisionsId}/debugsessions',
        http_method='GET',
        method_id='apigee.organizations.environments.apis.revisions.debugsessions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/debugsessions',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsListRequest',
        response_type_name='GoogleCloudApigeeV1ListDebugSessionsResponse',
        supports_download=False,
    )

  class OrganizationsEnvironmentsApisRevisionsDeploymentsService(base_api.BaseApiService):
    """Service class for the organizations_environments_apis_revisions_deployments resource."""

    _NAME = 'organizations_environments_apis_revisions_deployments'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsApisRevisionsDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def GenerateDeployChangeReport(self, request, global_params=None):
      r"""Generates a report for a dry run analysis of a DeployApiProxy request without committing the deployment. In addition to the standard validations performed when adding deployments, additional analysis will be done to detect possible traffic routing changes that would result from this deployment being created. Any potential routing conflicts or unsafe changes will be reported in the response. This routing analysis is not performed for a non-dry-run DeployApiProxy request. For a request path `organizations/{org}/environments/{env}/apis/{api}/revisions/{rev}/deployments:generateDeployChangeReport`, two permissions are required: * `apigee.deployments.create` on the resource `organizations/{org}/environments/{env}` * `apigee.proxyrevisions.deploy` on the resource `organizations/{org}/apis/{api}/revisions/{rev}`.

      Args:
        request: (ApigeeOrganizationsEnvironmentsApisRevisionsDeploymentsGenerateDeployChangeReportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeploymentChangeReport) The response message.
      """
      config = self.GetMethodConfig('GenerateDeployChangeReport')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateDeployChangeReport.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/apis/{apisId}/revisions/{revisionsId}/deployments:generateDeployChangeReport',
        http_method='POST',
        method_id='apigee.organizations.environments.apis.revisions.deployments.generateDeployChangeReport',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['override'],
        relative_path='v1/{+name}/deployments:generateDeployChangeReport',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsApisRevisionsDeploymentsGenerateDeployChangeReportRequest',
        response_type_name='GoogleCloudApigeeV1DeploymentChangeReport',
        supports_download=False,
    )

    def GenerateUndeployChangeReport(self, request, global_params=None):
      r"""Generates a report for a dry run analysis of an UndeployApiProxy request without committing the undeploy. In addition to the standard validations performed when removing deployments, additional analysis will be done to detect possible traffic routing changes that would result from this deployment being removed. Any potential routing conflicts or unsafe changes will be reported in the response. This routing analysis is not performed for a non-dry-run UndeployApiProxy request. For a request path `organizations/{org}/environments/{env}/apis/{api}/revisions/{rev}/deployments:generateUndeployChangeReport`, two permissions are required: * `apigee.deployments.delete` on the resource `organizations/{org}/environments/{env}` * `apigee.proxyrevisions.undeploy` on the resource `organizations/{org}/apis/{api}/revisions/{rev}`.

      Args:
        request: (ApigeeOrganizationsEnvironmentsApisRevisionsDeploymentsGenerateUndeployChangeReportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeploymentChangeReport) The response message.
      """
      config = self.GetMethodConfig('GenerateUndeployChangeReport')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateUndeployChangeReport.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/apis/{apisId}/revisions/{revisionsId}/deployments:generateUndeployChangeReport',
        http_method='POST',
        method_id='apigee.organizations.environments.apis.revisions.deployments.generateUndeployChangeReport',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}/deployments:generateUndeployChangeReport',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsApisRevisionsDeploymentsGenerateUndeployChangeReportRequest',
        response_type_name='GoogleCloudApigeeV1DeploymentChangeReport',
        supports_download=False,
    )

  class OrganizationsEnvironmentsApisRevisionsService(base_api.BaseApiService):
    """Service class for the organizations_environments_apis_revisions resource."""

    _NAME = 'organizations_environments_apis_revisions'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsApisRevisionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Deploy(self, request, global_params=None):
      r"""Deploys a revision of an API proxy. If another revision of the same API proxy revision is currently deployed, set the `override` parameter to `true` to have this revision replace the currently deployed revision. You cannot invoke an API proxy until it has been deployed to an environment. After you deploy an API proxy revision, you cannot edit it. To edit the API proxy, you must create and deploy a new revision. For a request path `organizations/{org}/environments/{env}/apis/{api}/revisions/{rev}/deployments`, two permissions are required: * `apigee.deployments.create` on the resource `organizations/{org}/environments/{env}` * `apigee.proxyrevisions.deploy` on the resource `organizations/{org}/apis/{api}/revisions/{rev}` .

      Args:
        request: (ApigeeOrganizationsEnvironmentsApisRevisionsDeployRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Deployment) The response message.
      """
      config = self.GetMethodConfig('Deploy')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deploy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/apis/{apisId}/revisions/{revisionsId}/deployments',
        http_method='POST',
        method_id='apigee.organizations.environments.apis.revisions.deploy',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['override', 'sequencedRollout', 'serviceAccount'],
        relative_path='v1/{+name}/deployments',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsApisRevisionsDeployRequest',
        response_type_name='GoogleCloudApigeeV1Deployment',
        supports_download=False,
    )

    def GetDeployments(self, request, global_params=None):
      r"""Gets the deployment of an API proxy revision and actual state reported by runtime pods.

      Args:
        request: (ApigeeOrganizationsEnvironmentsApisRevisionsGetDeploymentsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Deployment) The response message.
      """
      config = self.GetMethodConfig('GetDeployments')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetDeployments.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/apis/{apisId}/revisions/{revisionsId}/deployments',
        http_method='GET',
        method_id='apigee.organizations.environments.apis.revisions.getDeployments',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}/deployments',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsApisRevisionsGetDeploymentsRequest',
        response_type_name='GoogleCloudApigeeV1Deployment',
        supports_download=False,
    )

    def Undeploy(self, request, global_params=None):
      r"""Undeploys an API proxy revision from an environment. For a request path `organizations/{org}/environments/{env}/apis/{api}/revisions/{rev}/deployments`, two permissions are required: * `apigee.deployments.delete` on the resource `organizations/{org}/environments/{env}` * `apigee.proxyrevisions.undeploy` on the resource `organizations/{org}/apis/{api}/revisions/{rev}`.

      Args:
        request: (ApigeeOrganizationsEnvironmentsApisRevisionsUndeployRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Undeploy')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undeploy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/apis/{apisId}/revisions/{revisionsId}/deployments',
        http_method='DELETE',
        method_id='apigee.organizations.environments.apis.revisions.undeploy',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['sequencedRollout'],
        relative_path='v1/{+name}/deployments',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsApisRevisionsUndeployRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

  class OrganizationsEnvironmentsApisService(base_api.BaseApiService):
    """Service class for the organizations_environments_apis resource."""

    _NAME = 'organizations_environments_apis'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsApisService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsEnvironmentsArchiveDeploymentsService(base_api.BaseApiService):
    """Service class for the organizations_environments_archiveDeployments resource."""

    _NAME = 'organizations_environments_archiveDeployments'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsArchiveDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ArchiveDeployment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsArchiveDeploymentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/archiveDeployments',
        http_method='POST',
        method_id='apigee.organizations.environments.archiveDeployments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/archiveDeployments',
        request_field='googleCloudApigeeV1ArchiveDeployment',
        request_type_name='ApigeeOrganizationsEnvironmentsArchiveDeploymentsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an archive deployment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsArchiveDeploymentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/archiveDeployments/{archiveDeploymentsId}',
        http_method='DELETE',
        method_id='apigee.organizations.environments.archiveDeployments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsArchiveDeploymentsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def GenerateDownloadUrl(self, request, global_params=None):
      r"""Generates a signed URL for downloading the original zip file used to create an Archive Deployment. The URL is only valid for a limited period and should be used within minutes after generation. Each call returns a new upload URL.

      Args:
        request: (ApigeeOrganizationsEnvironmentsArchiveDeploymentsGenerateDownloadUrlRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1GenerateDownloadUrlResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateDownloadUrl')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateDownloadUrl.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/archiveDeployments/{archiveDeploymentsId}:generateDownloadUrl',
        http_method='POST',
        method_id='apigee.organizations.environments.archiveDeployments.generateDownloadUrl',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:generateDownloadUrl',
        request_field='googleCloudApigeeV1GenerateDownloadUrlRequest',
        request_type_name='ApigeeOrganizationsEnvironmentsArchiveDeploymentsGenerateDownloadUrlRequest',
        response_type_name='GoogleCloudApigeeV1GenerateDownloadUrlResponse',
        supports_download=False,
    )

    def GenerateUploadUrl(self, request, global_params=None):
      r"""Generates a signed URL for uploading an Archive zip file to Google Cloud Storage. Once the upload is complete, the signed URL should be passed to CreateArchiveDeployment. When uploading to the generated signed URL, please follow these restrictions: * Source file type should be a zip file. * Source file size should not exceed 1GB limit. * No credentials should be attached - the signed URLs provide access to the target bucket using internal service identity; if credentials were attached, the identity from the credentials would be used, but that identity does not have permissions to upload files to the URL. When making a HTTP PUT request, these two headers need to be specified: * `content-type: application/zip` * `x-goog-content-length-range: 0,1073741824` And this header SHOULD NOT be specified: * `Authorization: Bearer YOUR_TOKEN`.

      Args:
        request: (ApigeeOrganizationsEnvironmentsArchiveDeploymentsGenerateUploadUrlRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1GenerateUploadUrlResponse) The response message.
      """
      config = self.GetMethodConfig('GenerateUploadUrl')
      return self._RunMethod(
          config, request, global_params=global_params)

    GenerateUploadUrl.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/archiveDeployments:generateUploadUrl',
        http_method='POST',
        method_id='apigee.organizations.environments.archiveDeployments.generateUploadUrl',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/archiveDeployments:generateUploadUrl',
        request_field='googleCloudApigeeV1GenerateUploadUrlRequest',
        request_type_name='ApigeeOrganizationsEnvironmentsArchiveDeploymentsGenerateUploadUrlRequest',
        response_type_name='GoogleCloudApigeeV1GenerateUploadUrlResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the specified ArchiveDeployment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsArchiveDeploymentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ArchiveDeployment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/archiveDeployments/{archiveDeploymentsId}',
        http_method='GET',
        method_id='apigee.organizations.environments.archiveDeployments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsArchiveDeploymentsGetRequest',
        response_type_name='GoogleCloudApigeeV1ArchiveDeployment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the ArchiveDeployments in the specified Environment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsArchiveDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListArchiveDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/archiveDeployments',
        http_method='GET',
        method_id='apigee.organizations.environments.archiveDeployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/archiveDeployments',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsArchiveDeploymentsListRequest',
        response_type_name='GoogleCloudApigeeV1ListArchiveDeploymentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an existing ArchiveDeployment. Labels can modified but most of the other fields are not modifiable.

      Args:
        request: (ApigeeOrganizationsEnvironmentsArchiveDeploymentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ArchiveDeployment) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/archiveDeployments/{archiveDeploymentsId}',
        http_method='PATCH',
        method_id='apigee.organizations.environments.archiveDeployments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1ArchiveDeployment',
        request_type_name='ApigeeOrganizationsEnvironmentsArchiveDeploymentsPatchRequest',
        response_type_name='GoogleCloudApigeeV1ArchiveDeployment',
        supports_download=False,
    )

  class OrganizationsEnvironmentsCachesService(base_api.BaseApiService):
    """Service class for the organizations_environments_caches resource."""

    _NAME = 'organizations_environments_caches'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsCachesService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a cache.

      Args:
        request: (ApigeeOrganizationsEnvironmentsCachesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/caches/{cachesId}',
        http_method='DELETE',
        method_id='apigee.organizations.environments.caches.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsCachesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

  class OrganizationsEnvironmentsDeploymentsService(base_api.BaseApiService):
    """Service class for the organizations_environments_deployments resource."""

    _NAME = 'organizations_environments_deployments'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all deployments of API proxies or shared flows in an environment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/deployments',
        http_method='GET',
        method_id='apigee.organizations.environments.deployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['sharedFlows'],
        relative_path='v1/{+parent}/deployments',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsDeploymentsListRequest',
        response_type_name='GoogleCloudApigeeV1ListDeploymentsResponse',
        supports_download=False,
    )

  class OrganizationsEnvironmentsFlowhooksService(base_api.BaseApiService):
    """Service class for the organizations_environments_flowhooks resource."""

    _NAME = 'organizations_environments_flowhooks'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsFlowhooksService, self).__init__(client)
      self._upload_configs = {
          }

    def AttachSharedFlowToFlowHook(self, request, global_params=None):
      r"""Attaches a shared flow to a flow hook.

      Args:
        request: (ApigeeOrganizationsEnvironmentsFlowhooksAttachSharedFlowToFlowHookRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1FlowHook) The response message.
      """
      config = self.GetMethodConfig('AttachSharedFlowToFlowHook')
      return self._RunMethod(
          config, request, global_params=global_params)

    AttachSharedFlowToFlowHook.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/flowhooks/{flowhooksId}',
        http_method='PUT',
        method_id='apigee.organizations.environments.flowhooks.attachSharedFlowToFlowHook',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1FlowHook',
        request_type_name='ApigeeOrganizationsEnvironmentsFlowhooksAttachSharedFlowToFlowHookRequest',
        response_type_name='GoogleCloudApigeeV1FlowHook',
        supports_download=False,
    )

    def DetachSharedFlowFromFlowHook(self, request, global_params=None):
      r"""Detaches a shared flow from a flow hook.

      Args:
        request: (ApigeeOrganizationsEnvironmentsFlowhooksDetachSharedFlowFromFlowHookRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1FlowHook) The response message.
      """
      config = self.GetMethodConfig('DetachSharedFlowFromFlowHook')
      return self._RunMethod(
          config, request, global_params=global_params)

    DetachSharedFlowFromFlowHook.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/flowhooks/{flowhooksId}',
        http_method='DELETE',
        method_id='apigee.organizations.environments.flowhooks.detachSharedFlowFromFlowHook',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsFlowhooksDetachSharedFlowFromFlowHookRequest',
        response_type_name='GoogleCloudApigeeV1FlowHook',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Returns the name of the shared flow attached to the specified flow hook. If there's no shared flow attached to the flow hook, the API does not return an error; it simply does not return a name in the response.

      Args:
        request: (ApigeeOrganizationsEnvironmentsFlowhooksGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1FlowHook) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/flowhooks/{flowhooksId}',
        http_method='GET',
        method_id='apigee.organizations.environments.flowhooks.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsFlowhooksGetRequest',
        response_type_name='GoogleCloudApigeeV1FlowHook',
        supports_download=False,
    )

  class OrganizationsEnvironmentsKeystoresAliasesService(base_api.BaseApiService):
    """Service class for the organizations_environments_keystores_aliases resource."""

    _NAME = 'organizations_environments_keystores_aliases'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsKeystoresAliasesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an alias from a key/certificate pair. The structure of the request is controlled by the `format` query parameter: - `keycertfile` - Separate PEM-encoded key and certificate files are uploaded. Set `Content-Type: multipart/form-data` and include the `keyFile`, `certFile`, and `password` (if keys are encrypted) fields in the request body. If uploading to a truststore, omit `keyFile`. - `pkcs12` - A PKCS12 file is uploaded. Set `Content-Type: multipart/form-data`, provide the file in the `file` field, and include the `password` field if the file is encrypted in the request body. - `selfsignedcert` - A new private key and certificate are generated. Set `Content-Type: application/json` and include CertificateGenerationSpec in the request body.

      Args:
        request: (ApigeeOrganizationsEnvironmentsKeystoresAliasesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Alias) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/keystores/{keystoresId}/aliases',
        http_method='POST',
        method_id='apigee.organizations.environments.keystores.aliases.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['_password', 'alias', 'format', 'ignoreExpiryValidation', 'ignoreNewlineValidation'],
        relative_path='v1/{+parent}/aliases',
        request_field='googleApiHttpBody',
        request_type_name='ApigeeOrganizationsEnvironmentsKeystoresAliasesCreateRequest',
        response_type_name='GoogleCloudApigeeV1Alias',
        supports_download=False,
    )

    def Csr(self, request, global_params=None):
      r"""Generates a PKCS #10 Certificate Signing Request for the private key in an alias.

      Args:
        request: (ApigeeOrganizationsEnvironmentsKeystoresAliasesCsrRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('Csr')
      return self._RunMethod(
          config, request, global_params=global_params)

    Csr.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/keystores/{keystoresId}/aliases/{aliasesId}/csr',
        http_method='GET',
        method_id='apigee.organizations.environments.keystores.aliases.csr',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}/csr',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsKeystoresAliasesCsrRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an alias.

      Args:
        request: (ApigeeOrganizationsEnvironmentsKeystoresAliasesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Alias) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/keystores/{keystoresId}/aliases/{aliasesId}',
        http_method='DELETE',
        method_id='apigee.organizations.environments.keystores.aliases.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsKeystoresAliasesDeleteRequest',
        response_type_name='GoogleCloudApigeeV1Alias',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an alias.

      Args:
        request: (ApigeeOrganizationsEnvironmentsKeystoresAliasesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Alias) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/keystores/{keystoresId}/aliases/{aliasesId}',
        http_method='GET',
        method_id='apigee.organizations.environments.keystores.aliases.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsKeystoresAliasesGetRequest',
        response_type_name='GoogleCloudApigeeV1Alias',
        supports_download=False,
    )

    def GetCertificate(self, request, global_params=None):
      r"""Gets the certificate from an alias in PEM-encoded form.

      Args:
        request: (ApigeeOrganizationsEnvironmentsKeystoresAliasesGetCertificateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('GetCertificate')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetCertificate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/keystores/{keystoresId}/aliases/{aliasesId}/certificate',
        http_method='GET',
        method_id='apigee.organizations.environments.keystores.aliases.getCertificate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}/certificate',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsKeystoresAliasesGetCertificateRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates the certificate in an alias.

      Args:
        request: (ApigeeOrganizationsEnvironmentsKeystoresAliasesUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Alias) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/keystores/{keystoresId}/aliases/{aliasesId}',
        http_method='PUT',
        method_id='apigee.organizations.environments.keystores.aliases.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['ignoreExpiryValidation', 'ignoreNewlineValidation'],
        relative_path='v1/{+name}',
        request_field='googleApiHttpBody',
        request_type_name='ApigeeOrganizationsEnvironmentsKeystoresAliasesUpdateRequest',
        response_type_name='GoogleCloudApigeeV1Alias',
        supports_download=False,
    )

  class OrganizationsEnvironmentsKeystoresService(base_api.BaseApiService):
    """Service class for the organizations_environments_keystores resource."""

    _NAME = 'organizations_environments_keystores'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsKeystoresService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a keystore or truststore. - Keystore: Contains certificates and their associated keys. - Truststore: Contains trusted certificates used to validate a server's certificate. These certificates are typically self-signed certificates or certificates that are not signed by a trusted CA.

      Args:
        request: (ApigeeOrganizationsEnvironmentsKeystoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Keystore) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/keystores',
        http_method='POST',
        method_id='apigee.organizations.environments.keystores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['name'],
        relative_path='v1/{+parent}/keystores',
        request_field='googleCloudApigeeV1Keystore',
        request_type_name='ApigeeOrganizationsEnvironmentsKeystoresCreateRequest',
        response_type_name='GoogleCloudApigeeV1Keystore',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a keystore or truststore.

      Args:
        request: (ApigeeOrganizationsEnvironmentsKeystoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Keystore) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/keystores/{keystoresId}',
        http_method='DELETE',
        method_id='apigee.organizations.environments.keystores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsKeystoresDeleteRequest',
        response_type_name='GoogleCloudApigeeV1Keystore',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a keystore or truststore.

      Args:
        request: (ApigeeOrganizationsEnvironmentsKeystoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Keystore) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/keystores/{keystoresId}',
        http_method='GET',
        method_id='apigee.organizations.environments.keystores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsKeystoresGetRequest',
        response_type_name='GoogleCloudApigeeV1Keystore',
        supports_download=False,
    )

  class OrganizationsEnvironmentsKeyvaluemapsEntriesService(base_api.BaseApiService):
    """Service class for the organizations_environments_keyvaluemaps_entries resource."""

    _NAME = 'organizations_environments_keyvaluemaps_entries'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsKeyvaluemapsEntriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates key value entries in a key value map scoped to an organization, environment, or API proxy. **Note**: Supported for Apigee hybrid 1.8.x and higher.

      Args:
        request: (ApigeeOrganizationsEnvironmentsKeyvaluemapsEntriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1KeyValueEntry) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/keyvaluemaps/{keyvaluemapsId}/entries',
        http_method='POST',
        method_id='apigee.organizations.environments.keyvaluemaps.entries.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/entries',
        request_field='googleCloudApigeeV1KeyValueEntry',
        request_type_name='ApigeeOrganizationsEnvironmentsKeyvaluemapsEntriesCreateRequest',
        response_type_name='GoogleCloudApigeeV1KeyValueEntry',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a key value entry from a key value map scoped to an organization, environment, or API proxy. **Notes:** * After you delete the key value entry, the policy consuming the entry will continue to function with its cached values for a few minutes. This is expected behavior. * Supported for Apigee hybrid 1.8.x and higher.

      Args:
        request: (ApigeeOrganizationsEnvironmentsKeyvaluemapsEntriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1KeyValueEntry) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/keyvaluemaps/{keyvaluemapsId}/entries/{entriesId}',
        http_method='DELETE',
        method_id='apigee.organizations.environments.keyvaluemaps.entries.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsKeyvaluemapsEntriesDeleteRequest',
        response_type_name='GoogleCloudApigeeV1KeyValueEntry',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get the key value entry value for a key value map scoped to an organization, environment, or API proxy. **Note**: Supported for Apigee hybrid 1.8.x and higher.

      Args:
        request: (ApigeeOrganizationsEnvironmentsKeyvaluemapsEntriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1KeyValueEntry) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/keyvaluemaps/{keyvaluemapsId}/entries/{entriesId}',
        http_method='GET',
        method_id='apigee.organizations.environments.keyvaluemaps.entries.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsKeyvaluemapsEntriesGetRequest',
        response_type_name='GoogleCloudApigeeV1KeyValueEntry',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists key value entries for key values maps scoped to an organization, environment, or API proxy. **Note**: Supported for Apigee hybrid 1.8.x and higher.

      Args:
        request: (ApigeeOrganizationsEnvironmentsKeyvaluemapsEntriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListKeyValueEntriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/keyvaluemaps/{keyvaluemapsId}/entries',
        http_method='GET',
        method_id='apigee.organizations.environments.keyvaluemaps.entries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/entries',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsKeyvaluemapsEntriesListRequest',
        response_type_name='GoogleCloudApigeeV1ListKeyValueEntriesResponse',
        supports_download=False,
    )

  class OrganizationsEnvironmentsKeyvaluemapsService(base_api.BaseApiService):
    """Service class for the organizations_environments_keyvaluemaps resource."""

    _NAME = 'organizations_environments_keyvaluemaps'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsKeyvaluemapsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a key value map in an environment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsKeyvaluemapsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1KeyValueMap) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/keyvaluemaps',
        http_method='POST',
        method_id='apigee.organizations.environments.keyvaluemaps.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/keyvaluemaps',
        request_field='googleCloudApigeeV1KeyValueMap',
        request_type_name='ApigeeOrganizationsEnvironmentsKeyvaluemapsCreateRequest',
        response_type_name='GoogleCloudApigeeV1KeyValueMap',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a key value map from an environment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsKeyvaluemapsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1KeyValueMap) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/keyvaluemaps/{keyvaluemapsId}',
        http_method='DELETE',
        method_id='apigee.organizations.environments.keyvaluemaps.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsKeyvaluemapsDeleteRequest',
        response_type_name='GoogleCloudApigeeV1KeyValueMap',
        supports_download=False,
    )

  class OrganizationsEnvironmentsOptimizedStatsService(base_api.BaseApiService):
    """Service class for the organizations_environments_optimizedStats resource."""

    _NAME = 'organizations_environments_optimizedStats'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsOptimizedStatsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Similar to GetStats except that the response is less verbose.

      Args:
        request: (ApigeeOrganizationsEnvironmentsOptimizedStatsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1OptimizedStats) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/optimizedStats/{optimizedStatsId}',
        http_method='GET',
        method_id='apigee.organizations.environments.optimizedStats.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['accuracy', 'aggTable', 'filter', 'limit', 'offset', 'realtime', 'select', 'sonar', 'sort', 'sortby', 'timeRange', 'timeUnit', 'topk', 'tsAscending', 'tzo'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsOptimizedStatsGetRequest',
        response_type_name='GoogleCloudApigeeV1OptimizedStats',
        supports_download=False,
    )

  class OrganizationsEnvironmentsQueriesService(base_api.BaseApiService):
    """Service class for the organizations_environments_queries resource."""

    _NAME = 'organizations_environments_queries'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsQueriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Submit a query to be processed in the background. If the submission of the query succeeds, the API returns a 201 status and an ID that refer to the query. In addition to the HTTP status 201, the `state` of "enqueued" means that the request succeeded.

      Args:
        request: (ApigeeOrganizationsEnvironmentsQueriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AsyncQuery) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/queries',
        http_method='POST',
        method_id='apigee.organizations.environments.queries.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/queries',
        request_field='googleCloudApigeeV1Query',
        request_type_name='ApigeeOrganizationsEnvironmentsQueriesCreateRequest',
        response_type_name='GoogleCloudApigeeV1AsyncQuery',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get query status If the query is still in progress, the `state` is set to "running" After the query has completed successfully, `state` is set to "completed".

      Args:
        request: (ApigeeOrganizationsEnvironmentsQueriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AsyncQuery) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/queries/{queriesId}',
        http_method='GET',
        method_id='apigee.organizations.environments.queries.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsQueriesGetRequest',
        response_type_name='GoogleCloudApigeeV1AsyncQuery',
        supports_download=False,
    )

    def GetResult(self, request, global_params=None):
      r"""After the query is completed, use this API to retrieve the results. If the request succeeds, and there is a non-zero result set, the result is downloaded to the client as a zipped JSON file. The name of the downloaded file will be: OfflineQueryResult-.zip Example: `OfflineQueryResult-9cfc0d85-0f30-46d6-ae6f-318d0cb961bd.zip`.

      Args:
        request: (ApigeeOrganizationsEnvironmentsQueriesGetResultRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('GetResult')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetResult.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/queries/{queriesId}/result',
        http_method='GET',
        method_id='apigee.organizations.environments.queries.getResult',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsQueriesGetResultRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

    def GetResulturl(self, request, global_params=None):
      r"""After the query is completed, use this API to retrieve the results. If the request succeeds, and there is a non-zero result set, the result is sent to the client as a list of urls to JSON files.

      Args:
        request: (ApigeeOrganizationsEnvironmentsQueriesGetResulturlRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1GetAsyncQueryResultUrlResponse) The response message.
      """
      config = self.GetMethodConfig('GetResulturl')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetResulturl.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/queries/{queriesId}/resulturl',
        http_method='GET',
        method_id='apigee.organizations.environments.queries.getResulturl',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsQueriesGetResulturlRequest',
        response_type_name='GoogleCloudApigeeV1GetAsyncQueryResultUrlResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Return a list of Asynchronous Queries.

      Args:
        request: (ApigeeOrganizationsEnvironmentsQueriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListAsyncQueriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/queries',
        http_method='GET',
        method_id='apigee.organizations.environments.queries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['dataset', 'from_', 'inclQueriesWithoutReport', 'status', 'submittedBy', 'to'],
        relative_path='v1/{+parent}/queries',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsQueriesListRequest',
        response_type_name='GoogleCloudApigeeV1ListAsyncQueriesResponse',
        supports_download=False,
    )

  class OrganizationsEnvironmentsReferencesService(base_api.BaseApiService):
    """Service class for the organizations_environments_references resource."""

    _NAME = 'organizations_environments_references'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsReferencesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Reference in the specified environment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsReferencesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Reference) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/references',
        http_method='POST',
        method_id='apigee.organizations.environments.references.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/references',
        request_field='googleCloudApigeeV1Reference',
        request_type_name='ApigeeOrganizationsEnvironmentsReferencesCreateRequest',
        response_type_name='GoogleCloudApigeeV1Reference',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Reference from an environment. Returns the deleted Reference resource.

      Args:
        request: (ApigeeOrganizationsEnvironmentsReferencesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Reference) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/references/{referencesId}',
        http_method='DELETE',
        method_id='apigee.organizations.environments.references.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsReferencesDeleteRequest',
        response_type_name='GoogleCloudApigeeV1Reference',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Reference resource.

      Args:
        request: (ApigeeOrganizationsEnvironmentsReferencesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Reference) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/references/{referencesId}',
        http_method='GET',
        method_id='apigee.organizations.environments.references.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsReferencesGetRequest',
        response_type_name='GoogleCloudApigeeV1Reference',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates an existing Reference. Note that this operation has PUT semantics; it will replace the entirety of the existing Reference with the resource in the request body.

      Args:
        request: (GoogleCloudApigeeV1Reference) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Reference) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/references/{referencesId}',
        http_method='PUT',
        method_id='apigee.organizations.environments.references.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='GoogleCloudApigeeV1Reference',
        response_type_name='GoogleCloudApigeeV1Reference',
        supports_download=False,
    )

  class OrganizationsEnvironmentsResourcefilesService(base_api.BaseApiService):
    """Service class for the organizations_environments_resourcefiles resource."""

    _NAME = 'organizations_environments_resourcefiles'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsResourcefilesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a resource file. Specify the `Content-Type` as `application/octet-stream` or `multipart/form-data`. For more information about resource files, see [Resource files](https://cloud.google.com/apigee/docs/api-platform/develop/resource-files).

      Args:
        request: (ApigeeOrganizationsEnvironmentsResourcefilesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ResourceFile) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/resourcefiles',
        http_method='POST',
        method_id='apigee.organizations.environments.resourcefiles.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['name', 'type'],
        relative_path='v1/{+parent}/resourcefiles',
        request_field='googleApiHttpBody',
        request_type_name='ApigeeOrganizationsEnvironmentsResourcefilesCreateRequest',
        response_type_name='GoogleCloudApigeeV1ResourceFile',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a resource file. For more information about resource files, see [Resource files](https://cloud.google.com/apigee/docs/api-platform/develop/resource-files).

      Args:
        request: (ApigeeOrganizationsEnvironmentsResourcefilesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ResourceFile) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/resourcefiles/{type}/{name}',
        http_method='DELETE',
        method_id='apigee.organizations.environments.resourcefiles.delete',
        ordered_params=['parent', 'type', 'name'],
        path_params=['name', 'parent', 'type'],
        query_params=[],
        relative_path='v1/{+parent}/resourcefiles/{type}/{name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsResourcefilesDeleteRequest',
        response_type_name='GoogleCloudApigeeV1ResourceFile',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the contents of a resource file. For more information about resource files, see [Resource files](https://cloud.google.com/apigee/docs/api-platform/develop/resource-files).

      Args:
        request: (ApigeeOrganizationsEnvironmentsResourcefilesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/resourcefiles/{type}/{name}',
        http_method='GET',
        method_id='apigee.organizations.environments.resourcefiles.get',
        ordered_params=['parent', 'type', 'name'],
        path_params=['name', 'parent', 'type'],
        query_params=[],
        relative_path='v1/{+parent}/resourcefiles/{type}/{name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsResourcefilesGetRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all resource files, optionally filtering by type. For more information about resource files, see [Resource files](https://cloud.google.com/apigee/docs/api-platform/develop/resource-files).

      Args:
        request: (ApigeeOrganizationsEnvironmentsResourcefilesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListEnvironmentResourcesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/resourcefiles',
        http_method='GET',
        method_id='apigee.organizations.environments.resourcefiles.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['type'],
        relative_path='v1/{+parent}/resourcefiles',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsResourcefilesListRequest',
        response_type_name='GoogleCloudApigeeV1ListEnvironmentResourcesResponse',
        supports_download=False,
    )

    def ListEnvironmentResources(self, request, global_params=None):
      r"""Lists all resource files, optionally filtering by type. For more information about resource files, see [Resource files](https://cloud.google.com/apigee/docs/api-platform/develop/resource-files).

      Args:
        request: (ApigeeOrganizationsEnvironmentsResourcefilesListEnvironmentResourcesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListEnvironmentResourcesResponse) The response message.
      """
      config = self.GetMethodConfig('ListEnvironmentResources')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListEnvironmentResources.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/resourcefiles/{type}',
        http_method='GET',
        method_id='apigee.organizations.environments.resourcefiles.listEnvironmentResources',
        ordered_params=['parent', 'type'],
        path_params=['parent', 'type'],
        query_params=[],
        relative_path='v1/{+parent}/resourcefiles/{type}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsResourcefilesListEnvironmentResourcesRequest',
        response_type_name='GoogleCloudApigeeV1ListEnvironmentResourcesResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates a resource file. Specify the `Content-Type` as `application/octet-stream` or `multipart/form-data`. For more information about resource files, see [Resource files](https://cloud.google.com/apigee/docs/api-platform/develop/resource-files).

      Args:
        request: (ApigeeOrganizationsEnvironmentsResourcefilesUpdateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ResourceFile) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/resourcefiles/{type}/{name}',
        http_method='PUT',
        method_id='apigee.organizations.environments.resourcefiles.update',
        ordered_params=['parent', 'type', 'name'],
        path_params=['name', 'parent', 'type'],
        query_params=[],
        relative_path='v1/{+parent}/resourcefiles/{type}/{name}',
        request_field='googleApiHttpBody',
        request_type_name='ApigeeOrganizationsEnvironmentsResourcefilesUpdateRequest',
        response_type_name='GoogleCloudApigeeV1ResourceFile',
        supports_download=False,
    )

  class OrganizationsEnvironmentsSecurityActionsService(base_api.BaseApiService):
    """Service class for the organizations_environments_securityActions resource."""

    _NAME = 'organizations_environments_securityActions'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsSecurityActionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""CreateSecurityAction creates a SecurityAction.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSecurityActionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SecurityAction) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/securityActions',
        http_method='POST',
        method_id='apigee.organizations.environments.securityActions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['securityActionId'],
        relative_path='v1/{+parent}/securityActions',
        request_field='googleCloudApigeeV1SecurityAction',
        request_type_name='ApigeeOrganizationsEnvironmentsSecurityActionsCreateRequest',
        response_type_name='GoogleCloudApigeeV1SecurityAction',
        supports_download=False,
    )

    def Disable(self, request, global_params=None):
      r"""Disable a SecurityAction. The `state` of the SecurityAction after disabling is `DISABLED`. `DisableSecurityAction` can be called on SecurityActions in the state `ENABLED`; SecurityActions in a different state (including `DISABLED`) return an error.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSecurityActionsDisableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SecurityAction) The response message.
      """
      config = self.GetMethodConfig('Disable')
      return self._RunMethod(
          config, request, global_params=global_params)

    Disable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/securityActions/{securityActionsId}:disable',
        http_method='POST',
        method_id='apigee.organizations.environments.securityActions.disable',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:disable',
        request_field='googleCloudApigeeV1DisableSecurityActionRequest',
        request_type_name='ApigeeOrganizationsEnvironmentsSecurityActionsDisableRequest',
        response_type_name='GoogleCloudApigeeV1SecurityAction',
        supports_download=False,
    )

    def Enable(self, request, global_params=None):
      r"""Enable a SecurityAction. The `state` of the SecurityAction after enabling is `ENABLED`. `EnableSecurityAction` can be called on SecurityActions in the state `DISABLED`; SecurityActions in a different state (including `ENABLED) return an error.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSecurityActionsEnableRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SecurityAction) The response message.
      """
      config = self.GetMethodConfig('Enable')
      return self._RunMethod(
          config, request, global_params=global_params)

    Enable.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/securityActions/{securityActionsId}:enable',
        http_method='POST',
        method_id='apigee.organizations.environments.securityActions.enable',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:enable',
        request_field='googleCloudApigeeV1EnableSecurityActionRequest',
        request_type_name='ApigeeOrganizationsEnvironmentsSecurityActionsEnableRequest',
        response_type_name='GoogleCloudApigeeV1SecurityAction',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get a SecurityAction by name.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSecurityActionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SecurityAction) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/securityActions/{securityActionsId}',
        http_method='GET',
        method_id='apigee.organizations.environments.securityActions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsSecurityActionsGetRequest',
        response_type_name='GoogleCloudApigeeV1SecurityAction',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Returns a list of SecurityActions. This returns both enabled and disabled actions.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSecurityActionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListSecurityActionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/securityActions',
        http_method='GET',
        method_id='apigee.organizations.environments.securityActions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/securityActions',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsSecurityActionsListRequest',
        response_type_name='GoogleCloudApigeeV1ListSecurityActionsResponse',
        supports_download=False,
    )

  class OrganizationsEnvironmentsSecurityIncidentsService(base_api.BaseApiService):
    """Service class for the organizations_environments_securityIncidents resource."""

    _NAME = 'organizations_environments_securityIncidents'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsSecurityIncidentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""GetSecurityIncident gets the specified security incident. Returns NOT_FOUND if security incident is not present for the specified organization and environment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSecurityIncidentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SecurityIncident) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/securityIncidents/{securityIncidentsId}',
        http_method='GET',
        method_id='apigee.organizations.environments.securityIncidents.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsSecurityIncidentsGetRequest',
        response_type_name='GoogleCloudApigeeV1SecurityIncident',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""ListSecurityIncidents lists all the security incident associated with the environment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSecurityIncidentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListSecurityIncidentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/securityIncidents',
        http_method='GET',
        method_id='apigee.organizations.environments.securityIncidents.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/securityIncidents',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsSecurityIncidentsListRequest',
        response_type_name='GoogleCloudApigeeV1ListSecurityIncidentsResponse',
        supports_download=False,
    )

  class OrganizationsEnvironmentsSecurityReportsService(base_api.BaseApiService):
    """Service class for the organizations_environments_securityReports resource."""

    _NAME = 'organizations_environments_securityReports'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsSecurityReportsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Submit a report request to be processed in the background. If the submission succeeds, the API returns a 200 status and an ID that refer to the report request. In addition to the HTTP status 200, the `state` of "enqueued" means that the request succeeded.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSecurityReportsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SecurityReport) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/securityReports',
        http_method='POST',
        method_id='apigee.organizations.environments.securityReports.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/securityReports',
        request_field='googleCloudApigeeV1SecurityReportQuery',
        request_type_name='ApigeeOrganizationsEnvironmentsSecurityReportsCreateRequest',
        response_type_name='GoogleCloudApigeeV1SecurityReport',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get security report status If the query is still in progress, the `state` is set to "running" After the query has completed successfully, `state` is set to "completed".

      Args:
        request: (ApigeeOrganizationsEnvironmentsSecurityReportsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SecurityReport) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/securityReports/{securityReportsId}',
        http_method='GET',
        method_id='apigee.organizations.environments.securityReports.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsSecurityReportsGetRequest',
        response_type_name='GoogleCloudApigeeV1SecurityReport',
        supports_download=False,
    )

    def GetResult(self, request, global_params=None):
      r"""After the query is completed, use this API to retrieve the results as file. If the request succeeds, and there is a non-zero result set, the result is downloaded to the client as a zipped JSON file. The name of the downloaded file will be: OfflineQueryResult-.zip Example: `OfflineQueryResult-9cfc0d85-0f30-46d6-ae6f-318d0cb961bd.zip`.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSecurityReportsGetResultRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('GetResult')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetResult.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/securityReports/{securityReportsId}/result',
        http_method='GET',
        method_id='apigee.organizations.environments.securityReports.getResult',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsSecurityReportsGetResultRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

    def GetResultView(self, request, global_params=None):
      r"""After the query is completed, use this API to view the query result when result size is small.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSecurityReportsGetResultViewRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SecurityReportResultView) The response message.
      """
      config = self.GetMethodConfig('GetResultView')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetResultView.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/securityReports/{securityReportsId}/resultView',
        http_method='GET',
        method_id='apigee.organizations.environments.securityReports.getResultView',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsSecurityReportsGetResultViewRequest',
        response_type_name='GoogleCloudApigeeV1SecurityReportResultView',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Return a list of Security Reports.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSecurityReportsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListSecurityReportsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/securityReports',
        http_method='GET',
        method_id='apigee.organizations.environments.securityReports.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['dataset', 'from_', 'pageSize', 'pageToken', 'status', 'submittedBy', 'to'],
        relative_path='v1/{+parent}/securityReports',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsSecurityReportsListRequest',
        response_type_name='GoogleCloudApigeeV1ListSecurityReportsResponse',
        supports_download=False,
    )

  class OrganizationsEnvironmentsSecurityStatsService(base_api.BaseApiService):
    """Service class for the organizations_environments_securityStats resource."""

    _NAME = 'organizations_environments_securityStats'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsSecurityStatsService, self).__init__(client)
      self._upload_configs = {
          }

    def QueryTabularStats(self, request, global_params=None):
      r"""Retrieve security statistics as tabular rows.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSecurityStatsQueryTabularStatsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1QueryTabularStatsResponse) The response message.
      """
      config = self.GetMethodConfig('QueryTabularStats')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryTabularStats.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/securityStats:queryTabularStats',
        http_method='POST',
        method_id='apigee.organizations.environments.securityStats.queryTabularStats',
        ordered_params=['orgenv'],
        path_params=['orgenv'],
        query_params=[],
        relative_path='v1/{+orgenv}/securityStats:queryTabularStats',
        request_field='googleCloudApigeeV1QueryTabularStatsRequest',
        request_type_name='ApigeeOrganizationsEnvironmentsSecurityStatsQueryTabularStatsRequest',
        response_type_name='GoogleCloudApigeeV1QueryTabularStatsResponse',
        supports_download=False,
    )

    def QueryTimeSeriesStats(self, request, global_params=None):
      r"""Retrieve security statistics as a collection of time series.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSecurityStatsQueryTimeSeriesStatsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1QueryTimeSeriesStatsResponse) The response message.
      """
      config = self.GetMethodConfig('QueryTimeSeriesStats')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryTimeSeriesStats.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/securityStats:queryTimeSeriesStats',
        http_method='POST',
        method_id='apigee.organizations.environments.securityStats.queryTimeSeriesStats',
        ordered_params=['orgenv'],
        path_params=['orgenv'],
        query_params=[],
        relative_path='v1/{+orgenv}/securityStats:queryTimeSeriesStats',
        request_field='googleCloudApigeeV1QueryTimeSeriesStatsRequest',
        request_type_name='ApigeeOrganizationsEnvironmentsSecurityStatsQueryTimeSeriesStatsRequest',
        response_type_name='GoogleCloudApigeeV1QueryTimeSeriesStatsResponse',
        supports_download=False,
    )

  class OrganizationsEnvironmentsSharedflowsDeploymentsService(base_api.BaseApiService):
    """Service class for the organizations_environments_sharedflows_deployments resource."""

    _NAME = 'organizations_environments_sharedflows_deployments'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsSharedflowsDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all deployments of a shared flow in an environment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSharedflowsDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/sharedflows/{sharedflowsId}/deployments',
        http_method='GET',
        method_id='apigee.organizations.environments.sharedflows.deployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/deployments',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsSharedflowsDeploymentsListRequest',
        response_type_name='GoogleCloudApigeeV1ListDeploymentsResponse',
        supports_download=False,
    )

  class OrganizationsEnvironmentsSharedflowsRevisionsService(base_api.BaseApiService):
    """Service class for the organizations_environments_sharedflows_revisions resource."""

    _NAME = 'organizations_environments_sharedflows_revisions'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsSharedflowsRevisionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Deploy(self, request, global_params=None):
      r"""Deploys a revision of a shared flow. If another revision of the same shared flow is currently deployed, set the `override` parameter to `true` to have this revision replace the currently deployed revision. You cannot use a shared flow until it has been deployed to an environment. For a request path `organizations/{org}/environments/{env}/sharedflows/{sf}/revisions/{rev}/deployments`, two permissions are required: * `apigee.deployments.create` on the resource `organizations/{org}/environments/{env}` * `apigee.sharedflowrevisions.deploy` on the resource `organizations/{org}/sharedflows/{sf}/revisions/{rev}`.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSharedflowsRevisionsDeployRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Deployment) The response message.
      """
      config = self.GetMethodConfig('Deploy')
      return self._RunMethod(
          config, request, global_params=global_params)

    Deploy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/sharedflows/{sharedflowsId}/revisions/{revisionsId}/deployments',
        http_method='POST',
        method_id='apigee.organizations.environments.sharedflows.revisions.deploy',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['override', 'serviceAccount'],
        relative_path='v1/{+name}/deployments',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsSharedflowsRevisionsDeployRequest',
        response_type_name='GoogleCloudApigeeV1Deployment',
        supports_download=False,
    )

    def GetDeployments(self, request, global_params=None):
      r"""Gets the deployment of a shared flow revision and actual state reported by runtime pods.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSharedflowsRevisionsGetDeploymentsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Deployment) The response message.
      """
      config = self.GetMethodConfig('GetDeployments')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetDeployments.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/sharedflows/{sharedflowsId}/revisions/{revisionsId}/deployments',
        http_method='GET',
        method_id='apigee.organizations.environments.sharedflows.revisions.getDeployments',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}/deployments',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsSharedflowsRevisionsGetDeploymentsRequest',
        response_type_name='GoogleCloudApigeeV1Deployment',
        supports_download=False,
    )

    def Undeploy(self, request, global_params=None):
      r"""Undeploys a shared flow revision from an environment. For a request path `organizations/{org}/environments/{env}/sharedflows/{sf}/revisions/{rev}/deployments`, two permissions are required: * `apigee.deployments.delete` on the resource `organizations/{org}/environments/{env}` * `apigee.sharedflowrevisions.undeploy` on the resource `organizations/{org}/sharedflows/{sf}/revisions/{rev}`.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSharedflowsRevisionsUndeployRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Undeploy')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undeploy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/sharedflows/{sharedflowsId}/revisions/{revisionsId}/deployments',
        http_method='DELETE',
        method_id='apigee.organizations.environments.sharedflows.revisions.undeploy',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}/deployments',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsSharedflowsRevisionsUndeployRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

  class OrganizationsEnvironmentsSharedflowsService(base_api.BaseApiService):
    """Service class for the organizations_environments_sharedflows resource."""

    _NAME = 'organizations_environments_sharedflows'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsSharedflowsService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsEnvironmentsStatsService(base_api.BaseApiService):
    """Service class for the organizations_environments_stats resource."""

    _NAME = 'organizations_environments_stats'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsStatsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieve metrics grouped by dimensions. The types of metrics you can retrieve include traffic, message counts, API call latency, response size, and cache hits and counts. Dimensions let you view metrics in meaningful groups. You can optionally pass dimensions as path parameters to the `stats` API. If dimensions are not specified, the metrics are computed on the entire set of data for the given time range.

      Args:
        request: (ApigeeOrganizationsEnvironmentsStatsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Stats) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/stats/{statsId}',
        http_method='GET',
        method_id='apigee.organizations.environments.stats.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['accuracy', 'aggTable', 'filter', 'limit', 'offset', 'realtime', 'select', 'sonar', 'sort', 'sortby', 'timeRange', 'timeUnit', 'topk', 'tsAscending', 'tzo'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsStatsGetRequest',
        response_type_name='GoogleCloudApigeeV1Stats',
        supports_download=False,
    )

  class OrganizationsEnvironmentsTargetserversService(base_api.BaseApiService):
    """Service class for the organizations_environments_targetservers resource."""

    _NAME = 'organizations_environments_targetservers'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsTargetserversService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a TargetServer in the specified environment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsTargetserversCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1TargetServer) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/targetservers',
        http_method='POST',
        method_id='apigee.organizations.environments.targetservers.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['name'],
        relative_path='v1/{+parent}/targetservers',
        request_field='googleCloudApigeeV1TargetServer',
        request_type_name='ApigeeOrganizationsEnvironmentsTargetserversCreateRequest',
        response_type_name='GoogleCloudApigeeV1TargetServer',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a TargetServer from an environment. Returns the deleted TargetServer resource.

      Args:
        request: (ApigeeOrganizationsEnvironmentsTargetserversDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1TargetServer) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/targetservers/{targetserversId}',
        http_method='DELETE',
        method_id='apigee.organizations.environments.targetservers.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsTargetserversDeleteRequest',
        response_type_name='GoogleCloudApigeeV1TargetServer',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a TargetServer resource.

      Args:
        request: (ApigeeOrganizationsEnvironmentsTargetserversGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1TargetServer) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/targetservers/{targetserversId}',
        http_method='GET',
        method_id='apigee.organizations.environments.targetservers.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsTargetserversGetRequest',
        response_type_name='GoogleCloudApigeeV1TargetServer',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates an existing TargetServer. Note that this operation has PUT semantics; it will replace the entirety of the existing TargetServer with the resource in the request body.

      Args:
        request: (GoogleCloudApigeeV1TargetServer) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1TargetServer) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/targetservers/{targetserversId}',
        http_method='PUT',
        method_id='apigee.organizations.environments.targetservers.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='GoogleCloudApigeeV1TargetServer',
        response_type_name='GoogleCloudApigeeV1TargetServer',
        supports_download=False,
    )

  class OrganizationsEnvironmentsTraceConfigOverridesService(base_api.BaseApiService):
    """Service class for the organizations_environments_traceConfig_overrides resource."""

    _NAME = 'organizations_environments_traceConfig_overrides'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsTraceConfigOverridesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a trace configuration override. The response contains a system-generated UUID, that can be used to view, update, or delete the configuration override. Use the List API to view the existing trace configuration overrides.

      Args:
        request: (ApigeeOrganizationsEnvironmentsTraceConfigOverridesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1TraceConfigOverride) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/traceConfig/overrides',
        http_method='POST',
        method_id='apigee.organizations.environments.traceConfig.overrides.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/overrides',
        request_field='googleCloudApigeeV1TraceConfigOverride',
        request_type_name='ApigeeOrganizationsEnvironmentsTraceConfigOverridesCreateRequest',
        response_type_name='GoogleCloudApigeeV1TraceConfigOverride',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a distributed trace configuration override.

      Args:
        request: (ApigeeOrganizationsEnvironmentsTraceConfigOverridesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/traceConfig/overrides/{overridesId}',
        http_method='DELETE',
        method_id='apigee.organizations.environments.traceConfig.overrides.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsTraceConfigOverridesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a trace configuration override.

      Args:
        request: (ApigeeOrganizationsEnvironmentsTraceConfigOverridesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1TraceConfigOverride) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/traceConfig/overrides/{overridesId}',
        http_method='GET',
        method_id='apigee.organizations.environments.traceConfig.overrides.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsTraceConfigOverridesGetRequest',
        response_type_name='GoogleCloudApigeeV1TraceConfigOverride',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all of the distributed trace configuration overrides in an environment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsTraceConfigOverridesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListTraceConfigOverridesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/traceConfig/overrides',
        http_method='GET',
        method_id='apigee.organizations.environments.traceConfig.overrides.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/overrides',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsTraceConfigOverridesListRequest',
        response_type_name='GoogleCloudApigeeV1ListTraceConfigOverridesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a distributed trace configuration override. Note that the repeated fields have replace semantics when included in the field mask and that they will be overwritten by the value of the fields in the request body.

      Args:
        request: (ApigeeOrganizationsEnvironmentsTraceConfigOverridesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1TraceConfigOverride) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/traceConfig/overrides/{overridesId}',
        http_method='PATCH',
        method_id='apigee.organizations.environments.traceConfig.overrides.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1TraceConfigOverride',
        request_type_name='ApigeeOrganizationsEnvironmentsTraceConfigOverridesPatchRequest',
        response_type_name='GoogleCloudApigeeV1TraceConfigOverride',
        supports_download=False,
    )

  class OrganizationsEnvironmentsTraceConfigService(base_api.BaseApiService):
    """Service class for the organizations_environments_traceConfig resource."""

    _NAME = 'organizations_environments_traceConfig'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsTraceConfigService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsEnvironmentsService(base_api.BaseApiService):
    """Service class for the organizations_environments resource."""

    _NAME = 'organizations_environments'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsEnvironmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an environment in an organization.

      Args:
        request: (ApigeeOrganizationsEnvironmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments',
        http_method='POST',
        method_id='apigee.organizations.environments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['name'],
        relative_path='v1/{+parent}/environments',
        request_field='googleCloudApigeeV1Environment',
        request_type_name='ApigeeOrganizationsEnvironmentsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an environment from an organization. **Warning: You must delete all key value maps and key value entries before you delete an environment.** Otherwise, if you re-create the environment the key value map entry operations will encounter encryption/decryption discrepancies.

      Args:
        request: (ApigeeOrganizationsEnvironmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}',
        http_method='DELETE',
        method_id='apigee.organizations.environments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets environment details.

      Args:
        request: (ApigeeOrganizationsEnvironmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Environment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}',
        http_method='GET',
        method_id='apigee.organizations.environments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsGetRequest',
        response_type_name='GoogleCloudApigeeV1Environment',
        supports_download=False,
    )

    def GetApiSecurityRuntimeConfig(self, request, global_params=None):
      r"""Gets the API Security runtime configuration for an environment. This named ApiSecurityRuntimeConfig to prevent conflicts with ApiSecurityConfig from addon config.

      Args:
        request: (ApigeeOrganizationsEnvironmentsGetApiSecurityRuntimeConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ApiSecurityRuntimeConfig) The response message.
      """
      config = self.GetMethodConfig('GetApiSecurityRuntimeConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetApiSecurityRuntimeConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/apiSecurityRuntimeConfig',
        http_method='GET',
        method_id='apigee.organizations.environments.getApiSecurityRuntimeConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsGetApiSecurityRuntimeConfigRequest',
        response_type_name='GoogleCloudApigeeV1ApiSecurityRuntimeConfig',
        supports_download=False,
    )

    def GetDebugmask(self, request, global_params=None):
      r"""Gets the debug mask singleton resource for an environment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsGetDebugmaskRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DebugMask) The response message.
      """
      config = self.GetMethodConfig('GetDebugmask')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetDebugmask.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/debugmask',
        http_method='GET',
        method_id='apigee.organizations.environments.getDebugmask',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsGetDebugmaskRequest',
        response_type_name='GoogleCloudApigeeV1DebugMask',
        supports_download=False,
    )

    def GetDeployedConfig(self, request, global_params=None):
      r"""Gets the deployed configuration for an environment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsGetDeployedConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1EnvironmentConfig) The response message.
      """
      config = self.GetMethodConfig('GetDeployedConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetDeployedConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/deployedConfig',
        http_method='GET',
        method_id='apigee.organizations.environments.getDeployedConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsGetDeployedConfigRequest',
        response_type_name='GoogleCloudApigeeV1EnvironmentConfig',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the IAM policy on an environment. For more information, see [Manage users, roles, and permissions using the API](https://cloud.google.com/apigee/docs/api-platform/system-administration/manage-users-roles). You must have the `apigee.environments.getIamPolicy` permission to call this API.

      Args:
        request: (ApigeeOrganizationsEnvironmentsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}:getIamPolicy',
        http_method='GET',
        method_id='apigee.organizations.environments.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def GetSecurityActionsConfig(self, request, global_params=None):
      r"""GetSecurityActionConfig returns the current SecurityActions configuration.

      Args:
        request: (ApigeeOrganizationsEnvironmentsGetSecurityActionsConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SecurityActionsConfig) The response message.
      """
      config = self.GetMethodConfig('GetSecurityActionsConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSecurityActionsConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/securityActionsConfig',
        http_method='GET',
        method_id='apigee.organizations.environments.getSecurityActionsConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsGetSecurityActionsConfigRequest',
        response_type_name='GoogleCloudApigeeV1SecurityActionsConfig',
        supports_download=False,
    )

    def GetTraceConfig(self, request, global_params=None):
      r"""Get distributed trace configuration in an environment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsGetTraceConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1TraceConfig) The response message.
      """
      config = self.GetMethodConfig('GetTraceConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetTraceConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/traceConfig',
        http_method='GET',
        method_id='apigee.organizations.environments.getTraceConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsGetTraceConfigRequest',
        response_type_name='GoogleCloudApigeeV1TraceConfig',
        supports_download=False,
    )

    def ModifyEnvironment(self, request, global_params=None):
      r"""Updates properties for an Apigee environment with patch semantics using a field mask. **Note:** Not supported for Apigee hybrid.

      Args:
        request: (ApigeeOrganizationsEnvironmentsModifyEnvironmentRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('ModifyEnvironment')
      return self._RunMethod(
          config, request, global_params=global_params)

    ModifyEnvironment.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}',
        http_method='PATCH',
        method_id='apigee.organizations.environments.modifyEnvironment',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1Environment',
        request_type_name='ApigeeOrganizationsEnvironmentsModifyEnvironmentRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the IAM policy on an environment, if the policy already exists it will be replaced. For more information, see [Manage users, roles, and permissions using the API](https://cloud.google.com/apigee/docs/api-platform/system-administration/manage-users-roles). You must have the `apigee.environments.setIamPolicy` permission to call this API.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}:setIamPolicy',
        http_method='POST',
        method_id='apigee.organizations.environments.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='ApigeeOrganizationsEnvironmentsSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def Subscribe(self, request, global_params=None):
      r"""Creates a subscription for the environment's Pub/Sub topic. The server will assign a random name for this subscription. The "name" and "push_config" must *not* be specified.

      Args:
        request: (ApigeeOrganizationsEnvironmentsSubscribeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Subscription) The response message.
      """
      config = self.GetMethodConfig('Subscribe')
      return self._RunMethod(
          config, request, global_params=global_params)

    Subscribe.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}:subscribe',
        http_method='POST',
        method_id='apigee.organizations.environments.subscribe',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:subscribe',
        request_field='',
        request_type_name='ApigeeOrganizationsEnvironmentsSubscribeRequest',
        response_type_name='GoogleCloudApigeeV1Subscription',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Tests the permissions of a user on an environment, and returns a subset of permissions that the user has on the environment. If the environment does not exist, an empty permission set is returned (a NOT_FOUND error is not returned).

      Args:
        request: (ApigeeOrganizationsEnvironmentsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}:testIamPermissions',
        http_method='POST',
        method_id='apigee.organizations.environments.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='ApigeeOrganizationsEnvironmentsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

    def Unsubscribe(self, request, global_params=None):
      r"""Deletes a subscription for the environment's Pub/Sub topic.

      Args:
        request: (ApigeeOrganizationsEnvironmentsUnsubscribeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Unsubscribe')
      return self._RunMethod(
          config, request, global_params=global_params)

    Unsubscribe.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}:unsubscribe',
        http_method='POST',
        method_id='apigee.organizations.environments.unsubscribe',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:unsubscribe',
        request_field='googleCloudApigeeV1Subscription',
        request_type_name='ApigeeOrganizationsEnvironmentsUnsubscribeRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates an existing environment. When updating properties, you must pass all existing properties to the API, even if they are not being changed. If you omit properties from the payload, the properties are removed. To get the current list of properties for the environment, use the [Get Environment API](get). **Note**: Both `PUT` and `POST` methods are supported for updating an existing environment.

      Args:
        request: (GoogleCloudApigeeV1Environment) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Environment) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}',
        http_method='PUT',
        method_id='apigee.organizations.environments.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='GoogleCloudApigeeV1Environment',
        response_type_name='GoogleCloudApigeeV1Environment',
        supports_download=False,
    )

    def UpdateDebugmask(self, request, global_params=None):
      r"""Updates the debug mask singleton resource for an environment.

      Args:
        request: (ApigeeOrganizationsEnvironmentsUpdateDebugmaskRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DebugMask) The response message.
      """
      config = self.GetMethodConfig('UpdateDebugmask')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateDebugmask.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/debugmask',
        http_method='PATCH',
        method_id='apigee.organizations.environments.updateDebugmask',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['replaceRepeatedFields', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1DebugMask',
        request_type_name='ApigeeOrganizationsEnvironmentsUpdateDebugmaskRequest',
        response_type_name='GoogleCloudApigeeV1DebugMask',
        supports_download=False,
    )

    def UpdateEnvironment(self, request, global_params=None):
      r"""Updates an existing environment. When updating properties, you must pass all existing properties to the API, even if they are not being changed. If you omit properties from the payload, the properties are removed. To get the current list of properties for the environment, use the [Get Environment API](get). **Note**: Both `PUT` and `POST` methods are supported for updating an existing environment.

      Args:
        request: (GoogleCloudApigeeV1Environment) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Environment) The response message.
      """
      config = self.GetMethodConfig('UpdateEnvironment')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateEnvironment.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}',
        http_method='POST',
        method_id='apigee.organizations.environments.updateEnvironment',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='GoogleCloudApigeeV1Environment',
        response_type_name='GoogleCloudApigeeV1Environment',
        supports_download=False,
    )

    def UpdateSecurityActionsConfig(self, request, global_params=None):
      r"""UpdateSecurityActionConfig updates the current SecurityActions configuration. This method is used to enable/disable the feature at the environment level.

      Args:
        request: (ApigeeOrganizationsEnvironmentsUpdateSecurityActionsConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SecurityActionsConfig) The response message.
      """
      config = self.GetMethodConfig('UpdateSecurityActionsConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSecurityActionsConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/securityActionsConfig',
        http_method='PATCH',
        method_id='apigee.organizations.environments.updateSecurityActionsConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1SecurityActionsConfig',
        request_type_name='ApigeeOrganizationsEnvironmentsUpdateSecurityActionsConfigRequest',
        response_type_name='GoogleCloudApigeeV1SecurityActionsConfig',
        supports_download=False,
    )

    def UpdateTraceConfig(self, request, global_params=None):
      r"""Updates the trace configurations in an environment. Note that the repeated fields have replace semantics when included in the field mask and that they will be overwritten by the value of the fields in the request body.

      Args:
        request: (ApigeeOrganizationsEnvironmentsUpdateTraceConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1TraceConfig) The response message.
      """
      config = self.GetMethodConfig('UpdateTraceConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateTraceConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/environments/{environmentsId}/traceConfig',
        http_method='PATCH',
        method_id='apigee.organizations.environments.updateTraceConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1TraceConfig',
        request_type_name='ApigeeOrganizationsEnvironmentsUpdateTraceConfigRequest',
        response_type_name='GoogleCloudApigeeV1TraceConfig',
        supports_download=False,
    )

  class OrganizationsHostQueriesService(base_api.BaseApiService):
    """Service class for the organizations_hostQueries resource."""

    _NAME = 'organizations_hostQueries'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsHostQueriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Submit a query at host level to be processed in the background. If the submission of the query succeeds, the API returns a 201 status and an ID that refer to the query. In addition to the HTTP status 201, the `state` of "enqueued" means that the request succeeded.

      Args:
        request: (ApigeeOrganizationsHostQueriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AsyncQuery) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/hostQueries',
        http_method='POST',
        method_id='apigee.organizations.hostQueries.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/hostQueries',
        request_field='googleCloudApigeeV1Query',
        request_type_name='ApigeeOrganizationsHostQueriesCreateRequest',
        response_type_name='GoogleCloudApigeeV1AsyncQuery',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get status of a query submitted at host level. If the query is still in progress, the `state` is set to "running" After the query has completed successfully, `state` is set to "completed".

      Args:
        request: (ApigeeOrganizationsHostQueriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AsyncQuery) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/hostQueries/{hostQueriesId}',
        http_method='GET',
        method_id='apigee.organizations.hostQueries.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsHostQueriesGetRequest',
        response_type_name='GoogleCloudApigeeV1AsyncQuery',
        supports_download=False,
    )

    def GetResult(self, request, global_params=None):
      r"""After the query is completed, use this API to retrieve the results. If the request succeeds, and there is a non-zero result set, the result is downloaded to the client as a zipped JSON file. The name of the downloaded file will be: OfflineQueryResult-.zip Example: `OfflineQueryResult-9cfc0d85-0f30-46d6-ae6f-318d0cb961bd.zip`.

      Args:
        request: (ApigeeOrganizationsHostQueriesGetResultRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('GetResult')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetResult.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/hostQueries/{hostQueriesId}/result',
        http_method='GET',
        method_id='apigee.organizations.hostQueries.getResult',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsHostQueriesGetResultRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

    def GetResultView(self, request, global_params=None):
      r"""GetResultView method for the organizations_hostQueries service.

      Args:
        request: (ApigeeOrganizationsHostQueriesGetResultViewRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1AsyncQueryResultView) The response message.
      """
      config = self.GetMethodConfig('GetResultView')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetResultView.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/hostQueries/{hostQueriesId}/resultView',
        http_method='GET',
        method_id='apigee.organizations.hostQueries.getResultView',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsHostQueriesGetResultViewRequest',
        response_type_name='GoogleCloudApigeeV1AsyncQueryResultView',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Return a list of Asynchronous Queries at host level.

      Args:
        request: (ApigeeOrganizationsHostQueriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListAsyncQueriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/hostQueries',
        http_method='GET',
        method_id='apigee.organizations.hostQueries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['dataset', 'envgroupHostname', 'from_', 'inclQueriesWithoutReport', 'status', 'submittedBy', 'to'],
        relative_path='v1/{+parent}/hostQueries',
        request_field='',
        request_type_name='ApigeeOrganizationsHostQueriesListRequest',
        response_type_name='GoogleCloudApigeeV1ListAsyncQueriesResponse',
        supports_download=False,
    )

  class OrganizationsHostSecurityReportsService(base_api.BaseApiService):
    """Service class for the organizations_hostSecurityReports resource."""

    _NAME = 'organizations_hostSecurityReports'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsHostSecurityReportsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Submit a query at host level to be processed in the background. If the submission of the query succeeds, the API returns a 201 status and an ID that refer to the query. In addition to the HTTP status 201, the `state` of "enqueued" means that the request succeeded.

      Args:
        request: (ApigeeOrganizationsHostSecurityReportsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SecurityReport) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/hostSecurityReports',
        http_method='POST',
        method_id='apigee.organizations.hostSecurityReports.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/hostSecurityReports',
        request_field='googleCloudApigeeV1SecurityReportQuery',
        request_type_name='ApigeeOrganizationsHostSecurityReportsCreateRequest',
        response_type_name='GoogleCloudApigeeV1SecurityReport',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get status of a query submitted at host level. If the query is still in progress, the `state` is set to "running" After the query has completed successfully, `state` is set to "completed".

      Args:
        request: (ApigeeOrganizationsHostSecurityReportsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SecurityReport) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/hostSecurityReports/{hostSecurityReportsId}',
        http_method='GET',
        method_id='apigee.organizations.hostSecurityReports.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsHostSecurityReportsGetRequest',
        response_type_name='GoogleCloudApigeeV1SecurityReport',
        supports_download=False,
    )

    def GetResult(self, request, global_params=None):
      r"""After the query is completed, use this API to retrieve the results. If the request succeeds, and there is a non-zero result set, the result is downloaded to the client as a zipped JSON file. The name of the downloaded file will be: OfflineQueryResult-.zip Example: `OfflineQueryResult-9cfc0d85-0f30-46d6-ae6f-318d0cb961bd.zip`.

      Args:
        request: (ApigeeOrganizationsHostSecurityReportsGetResultRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('GetResult')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetResult.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/hostSecurityReports/{hostSecurityReportsId}/result',
        http_method='GET',
        method_id='apigee.organizations.hostSecurityReports.getResult',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsHostSecurityReportsGetResultRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

    def GetResultView(self, request, global_params=None):
      r"""After the query is completed, use this API to view the query result when result size is small.

      Args:
        request: (ApigeeOrganizationsHostSecurityReportsGetResultViewRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SecurityReportResultView) The response message.
      """
      config = self.GetMethodConfig('GetResultView')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetResultView.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/hostSecurityReports/{hostSecurityReportsId}/resultView',
        http_method='GET',
        method_id='apigee.organizations.hostSecurityReports.getResultView',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsHostSecurityReportsGetResultViewRequest',
        response_type_name='GoogleCloudApigeeV1SecurityReportResultView',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Return a list of Security Reports at host level.

      Args:
        request: (ApigeeOrganizationsHostSecurityReportsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListSecurityReportsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/hostSecurityReports',
        http_method='GET',
        method_id='apigee.organizations.hostSecurityReports.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['dataset', 'envgroupHostname', 'from_', 'pageSize', 'pageToken', 'status', 'submittedBy', 'to'],
        relative_path='v1/{+parent}/hostSecurityReports',
        request_field='',
        request_type_name='ApigeeOrganizationsHostSecurityReportsListRequest',
        response_type_name='GoogleCloudApigeeV1ListSecurityReportsResponse',
        supports_download=False,
    )

  class OrganizationsHostStatsService(base_api.BaseApiService):
    """Service class for the organizations_hostStats resource."""

    _NAME = 'organizations_hostStats'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsHostStatsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Retrieve metrics grouped by dimensions in host level. The types of metrics you can retrieve include traffic, message counts, API call latency, response size, and cache hits and counts. Dimensions let you view metrics in meaningful groups. You can optionally pass dimensions as path parameters to the `stats` API. If dimensions are not specified, the metrics are computed on the entire set of data for the given time range.

      Args:
        request: (ApigeeOrganizationsHostStatsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Stats) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/hostStats/{hostStatsId}',
        http_method='GET',
        method_id='apigee.organizations.hostStats.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['accuracy', 'envgroupHostname', 'filter', 'limit', 'offset', 'realtime', 'select', 'sort', 'sortby', 'timeRange', 'timeUnit', 'topk', 'tsAscending', 'tzo'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsHostStatsGetRequest',
        response_type_name='GoogleCloudApigeeV1Stats',
        supports_download=False,
    )

  class OrganizationsInstancesAttachmentsService(base_api.BaseApiService):
    """Service class for the organizations_instances_attachments resource."""

    _NAME = 'organizations_instances_attachments'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsInstancesAttachmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new attachment of an environment to an instance. **Note:** Not supported for Apigee hybrid.

      Args:
        request: (ApigeeOrganizationsInstancesAttachmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/instances/{instancesId}/attachments',
        http_method='POST',
        method_id='apigee.organizations.instances.attachments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/attachments',
        request_field='googleCloudApigeeV1InstanceAttachment',
        request_type_name='ApigeeOrganizationsInstancesAttachmentsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an attachment. **Note:** Not supported for Apigee hybrid.

      Args:
        request: (ApigeeOrganizationsInstancesAttachmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/instances/{instancesId}/attachments/{attachmentsId}',
        http_method='DELETE',
        method_id='apigee.organizations.instances.attachments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsInstancesAttachmentsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an attachment. **Note:** Not supported for Apigee hybrid.

      Args:
        request: (ApigeeOrganizationsInstancesAttachmentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1InstanceAttachment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/instances/{instancesId}/attachments/{attachmentsId}',
        http_method='GET',
        method_id='apigee.organizations.instances.attachments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsInstancesAttachmentsGetRequest',
        response_type_name='GoogleCloudApigeeV1InstanceAttachment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all attachments to an instance. **Note:** Not supported for Apigee hybrid.

      Args:
        request: (ApigeeOrganizationsInstancesAttachmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListInstanceAttachmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/instances/{instancesId}/attachments',
        http_method='GET',
        method_id='apigee.organizations.instances.attachments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/attachments',
        request_field='',
        request_type_name='ApigeeOrganizationsInstancesAttachmentsListRequest',
        response_type_name='GoogleCloudApigeeV1ListInstanceAttachmentsResponse',
        supports_download=False,
    )

  class OrganizationsInstancesCanaryevaluationsService(base_api.BaseApiService):
    """Service class for the organizations_instances_canaryevaluations resource."""

    _NAME = 'organizations_instances_canaryevaluations'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsInstancesCanaryevaluationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new canary evaluation for an organization.

      Args:
        request: (ApigeeOrganizationsInstancesCanaryevaluationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/instances/{instancesId}/canaryevaluations',
        http_method='POST',
        method_id='apigee.organizations.instances.canaryevaluations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/canaryevaluations',
        request_field='googleCloudApigeeV1CanaryEvaluation',
        request_type_name='ApigeeOrganizationsInstancesCanaryevaluationsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a CanaryEvaluation for an organization.

      Args:
        request: (ApigeeOrganizationsInstancesCanaryevaluationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1CanaryEvaluation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/instances/{instancesId}/canaryevaluations/{canaryevaluationsId}',
        http_method='GET',
        method_id='apigee.organizations.instances.canaryevaluations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsInstancesCanaryevaluationsGetRequest',
        response_type_name='GoogleCloudApigeeV1CanaryEvaluation',
        supports_download=False,
    )

  class OrganizationsInstancesNatAddressesService(base_api.BaseApiService):
    """Service class for the organizations_instances_natAddresses resource."""

    _NAME = 'organizations_instances_natAddresses'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsInstancesNatAddressesService, self).__init__(client)
      self._upload_configs = {
          }

    def Activate(self, request, global_params=None):
      r"""Activates the NAT address. The Apigee instance can now use this for Internet egress traffic. **Note:** Not supported for Apigee hybrid.

      Args:
        request: (ApigeeOrganizationsInstancesNatAddressesActivateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Activate')
      return self._RunMethod(
          config, request, global_params=global_params)

    Activate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/instances/{instancesId}/natAddresses/{natAddressesId}:activate',
        http_method='POST',
        method_id='apigee.organizations.instances.natAddresses.activate',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:activate',
        request_field='googleCloudApigeeV1ActivateNatAddressRequest',
        request_type_name='ApigeeOrganizationsInstancesNatAddressesActivateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a NAT address. The address is created in the RESERVED state and a static external IP address will be provisioned. At this time, the instance will not use this IP address for Internet egress traffic. The address can be activated for use once any required firewall IP whitelisting has been completed. **Note:** Not supported for Apigee hybrid.

      Args:
        request: (ApigeeOrganizationsInstancesNatAddressesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/instances/{instancesId}/natAddresses',
        http_method='POST',
        method_id='apigee.organizations.instances.natAddresses.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/natAddresses',
        request_field='googleCloudApigeeV1NatAddress',
        request_type_name='ApigeeOrganizationsInstancesNatAddressesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the NAT address. Connections that are actively using the address are drained before it is removed. **Note:** Not supported for Apigee hybrid.

      Args:
        request: (ApigeeOrganizationsInstancesNatAddressesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/instances/{instancesId}/natAddresses/{natAddressesId}',
        http_method='DELETE',
        method_id='apigee.organizations.instances.natAddresses.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsInstancesNatAddressesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the details of a NAT address. **Note:** Not supported for Apigee hybrid.

      Args:
        request: (ApigeeOrganizationsInstancesNatAddressesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1NatAddress) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/instances/{instancesId}/natAddresses/{natAddressesId}',
        http_method='GET',
        method_id='apigee.organizations.instances.natAddresses.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsInstancesNatAddressesGetRequest',
        response_type_name='GoogleCloudApigeeV1NatAddress',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the NAT addresses for an Apigee instance. **Note:** Not supported for Apigee hybrid.

      Args:
        request: (ApigeeOrganizationsInstancesNatAddressesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListNatAddressesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/instances/{instancesId}/natAddresses',
        http_method='GET',
        method_id='apigee.organizations.instances.natAddresses.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/natAddresses',
        request_field='',
        request_type_name='ApigeeOrganizationsInstancesNatAddressesListRequest',
        response_type_name='GoogleCloudApigeeV1ListNatAddressesResponse',
        supports_download=False,
    )

  class OrganizationsInstancesService(base_api.BaseApiService):
    """Service class for the organizations_instances resource."""

    _NAME = 'organizations_instances'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsInstancesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an Apigee runtime instance. The instance is accessible from the authorized network configured on the organization. **Note:** Not supported for Apigee hybrid.

      Args:
        request: (ApigeeOrganizationsInstancesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/instances',
        http_method='POST',
        method_id='apigee.organizations.instances.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['environments', 'runtimeVersion'],
        relative_path='v1/{+parent}/instances',
        request_field='googleCloudApigeeV1Instance',
        request_type_name='ApigeeOrganizationsInstancesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an Apigee runtime instance. The instance stops serving requests and the runtime data is deleted. **Note:** Not supported for Apigee hybrid.

      Args:
        request: (ApigeeOrganizationsInstancesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/instances/{instancesId}',
        http_method='DELETE',
        method_id='apigee.organizations.instances.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsInstancesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the details for an Apigee runtime instance. **Note:** Not supported for Apigee hybrid.

      Args:
        request: (ApigeeOrganizationsInstancesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Instance) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/instances/{instancesId}',
        http_method='GET',
        method_id='apigee.organizations.instances.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsInstancesGetRequest',
        response_type_name='GoogleCloudApigeeV1Instance',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all Apigee runtime instances for the organization. **Note:** Not supported for Apigee hybrid.

      Args:
        request: (ApigeeOrganizationsInstancesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListInstancesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/instances',
        http_method='GET',
        method_id='apigee.organizations.instances.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/instances',
        request_field='',
        request_type_name='ApigeeOrganizationsInstancesListRequest',
        response_type_name='GoogleCloudApigeeV1ListInstancesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an Apigee runtime instance. You can update the fields described in NodeConfig. No other fields will be updated. **Note:** Not supported for Apigee hybrid.

      Args:
        request: (ApigeeOrganizationsInstancesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/instances/{instancesId}',
        http_method='PATCH',
        method_id='apigee.organizations.instances.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1Instance',
        request_type_name='ApigeeOrganizationsInstancesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def ReportStatus(self, request, global_params=None):
      r"""Reports the latest status for a runtime instance.

      Args:
        request: (ApigeeOrganizationsInstancesReportStatusRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ReportInstanceStatusResponse) The response message.
      """
      config = self.GetMethodConfig('ReportStatus')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReportStatus.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/instances/{instancesId}:reportStatus',
        http_method='POST',
        method_id='apigee.organizations.instances.reportStatus',
        ordered_params=['instance'],
        path_params=['instance'],
        query_params=[],
        relative_path='v1/{+instance}:reportStatus',
        request_field='googleCloudApigeeV1ReportInstanceStatusRequest',
        request_type_name='ApigeeOrganizationsInstancesReportStatusRequest',
        response_type_name='GoogleCloudApigeeV1ReportInstanceStatusResponse',
        supports_download=False,
    )

  class OrganizationsKeyvaluemapsEntriesService(base_api.BaseApiService):
    """Service class for the organizations_keyvaluemaps_entries resource."""

    _NAME = 'organizations_keyvaluemaps_entries'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsKeyvaluemapsEntriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates key value entries in a key value map scoped to an organization, environment, or API proxy. **Note**: Supported for Apigee hybrid 1.8.x and higher.

      Args:
        request: (ApigeeOrganizationsKeyvaluemapsEntriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1KeyValueEntry) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/keyvaluemaps/{keyvaluemapsId}/entries',
        http_method='POST',
        method_id='apigee.organizations.keyvaluemaps.entries.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/entries',
        request_field='googleCloudApigeeV1KeyValueEntry',
        request_type_name='ApigeeOrganizationsKeyvaluemapsEntriesCreateRequest',
        response_type_name='GoogleCloudApigeeV1KeyValueEntry',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a key value entry from a key value map scoped to an organization, environment, or API proxy. **Notes:** * After you delete the key value entry, the policy consuming the entry will continue to function with its cached values for a few minutes. This is expected behavior. * Supported for Apigee hybrid 1.8.x and higher.

      Args:
        request: (ApigeeOrganizationsKeyvaluemapsEntriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1KeyValueEntry) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/keyvaluemaps/{keyvaluemapsId}/entries/{entriesId}',
        http_method='DELETE',
        method_id='apigee.organizations.keyvaluemaps.entries.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsKeyvaluemapsEntriesDeleteRequest',
        response_type_name='GoogleCloudApigeeV1KeyValueEntry',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Get the key value entry value for a key value map scoped to an organization, environment, or API proxy. **Note**: Supported for Apigee hybrid 1.8.x and higher.

      Args:
        request: (ApigeeOrganizationsKeyvaluemapsEntriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1KeyValueEntry) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/keyvaluemaps/{keyvaluemapsId}/entries/{entriesId}',
        http_method='GET',
        method_id='apigee.organizations.keyvaluemaps.entries.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsKeyvaluemapsEntriesGetRequest',
        response_type_name='GoogleCloudApigeeV1KeyValueEntry',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists key value entries for key values maps scoped to an organization, environment, or API proxy. **Note**: Supported for Apigee hybrid 1.8.x and higher.

      Args:
        request: (ApigeeOrganizationsKeyvaluemapsEntriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListKeyValueEntriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/keyvaluemaps/{keyvaluemapsId}/entries',
        http_method='GET',
        method_id='apigee.organizations.keyvaluemaps.entries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/entries',
        request_field='',
        request_type_name='ApigeeOrganizationsKeyvaluemapsEntriesListRequest',
        response_type_name='GoogleCloudApigeeV1ListKeyValueEntriesResponse',
        supports_download=False,
    )

  class OrganizationsKeyvaluemapsService(base_api.BaseApiService):
    """Service class for the organizations_keyvaluemaps resource."""

    _NAME = 'organizations_keyvaluemaps'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsKeyvaluemapsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a key value map in an organization.

      Args:
        request: (ApigeeOrganizationsKeyvaluemapsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1KeyValueMap) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/keyvaluemaps',
        http_method='POST',
        method_id='apigee.organizations.keyvaluemaps.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/keyvaluemaps',
        request_field='googleCloudApigeeV1KeyValueMap',
        request_type_name='ApigeeOrganizationsKeyvaluemapsCreateRequest',
        response_type_name='GoogleCloudApigeeV1KeyValueMap',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a key value map from an organization.

      Args:
        request: (ApigeeOrganizationsKeyvaluemapsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1KeyValueMap) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/keyvaluemaps/{keyvaluemapsId}',
        http_method='DELETE',
        method_id='apigee.organizations.keyvaluemaps.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsKeyvaluemapsDeleteRequest',
        response_type_name='GoogleCloudApigeeV1KeyValueMap',
        supports_download=False,
    )

  class OrganizationsOperationsService(base_api.BaseApiService):
    """Service class for the organizations_operations resource."""

    _NAME = 'organizations_operations'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (ApigeeOrganizationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='apigee.organizations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (ApigeeOrganizationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/operations',
        http_method='GET',
        method_id='apigee.organizations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='ApigeeOrganizationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

  class OrganizationsOptimizedHostStatsService(base_api.BaseApiService):
    """Service class for the organizations_optimizedHostStats resource."""

    _NAME = 'organizations_optimizedHostStats'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsOptimizedHostStatsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Similar to GetHostStats except that the response is less verbose.

      Args:
        request: (ApigeeOrganizationsOptimizedHostStatsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1OptimizedStats) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/optimizedHostStats/{optimizedHostStatsId}',
        http_method='GET',
        method_id='apigee.organizations.optimizedHostStats.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['accuracy', 'envgroupHostname', 'filter', 'limit', 'offset', 'realtime', 'select', 'sort', 'sortby', 'timeRange', 'timeUnit', 'topk', 'tsAscending', 'tzo'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsOptimizedHostStatsGetRequest',
        response_type_name='GoogleCloudApigeeV1OptimizedStats',
        supports_download=False,
    )

  class OrganizationsReportsService(base_api.BaseApiService):
    """Service class for the organizations_reports resource."""

    _NAME = 'organizations_reports'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsReportsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Custom Report for an Organization. A Custom Report provides Apigee Customers to create custom dashboards in addition to the standard dashboards which are provided. The Custom Report in its simplest form contains specifications about metrics, dimensions and filters. It is important to note that the custom report by itself does not provide an executable entity. The Edge UI converts the custom report definition into an analytics query and displays the result in a chart.

      Args:
        request: (ApigeeOrganizationsReportsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1CustomReport) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/reports',
        http_method='POST',
        method_id='apigee.organizations.reports.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/reports',
        request_field='googleCloudApigeeV1CustomReport',
        request_type_name='ApigeeOrganizationsReportsCreateRequest',
        response_type_name='GoogleCloudApigeeV1CustomReport',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an existing custom report definition.

      Args:
        request: (ApigeeOrganizationsReportsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeleteCustomReportResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/reports/{reportsId}',
        http_method='DELETE',
        method_id='apigee.organizations.reports.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsReportsDeleteRequest',
        response_type_name='GoogleCloudApigeeV1DeleteCustomReportResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieve a custom report definition.

      Args:
        request: (ApigeeOrganizationsReportsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1CustomReport) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/reports/{reportsId}',
        http_method='GET',
        method_id='apigee.organizations.reports.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsReportsGetRequest',
        response_type_name='GoogleCloudApigeeV1CustomReport',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Return a list of Custom Reports.

      Args:
        request: (ApigeeOrganizationsReportsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListCustomReportsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/reports',
        http_method='GET',
        method_id='apigee.organizations.reports.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['expand'],
        relative_path='v1/{+parent}/reports',
        request_field='',
        request_type_name='ApigeeOrganizationsReportsListRequest',
        response_type_name='GoogleCloudApigeeV1ListCustomReportsResponse',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Update an existing custom report definition.

      Args:
        request: (GoogleCloudApigeeV1CustomReport) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1CustomReport) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/reports/{reportsId}',
        http_method='PUT',
        method_id='apigee.organizations.reports.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='GoogleCloudApigeeV1CustomReport',
        response_type_name='GoogleCloudApigeeV1CustomReport',
        supports_download=False,
    )

  class OrganizationsSecurityProfilesEnvironmentsService(base_api.BaseApiService):
    """Service class for the organizations_securityProfiles_environments resource."""

    _NAME = 'organizations_securityProfiles_environments'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsSecurityProfilesEnvironmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def ComputeEnvironmentScores(self, request, global_params=None):
      r"""ComputeEnvironmentScores calculates scores for requested time range for the specified security profile and environment.

      Args:
        request: (ApigeeOrganizationsSecurityProfilesEnvironmentsComputeEnvironmentScoresRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ComputeEnvironmentScoresResponse) The response message.
      """
      config = self.GetMethodConfig('ComputeEnvironmentScores')
      return self._RunMethod(
          config, request, global_params=global_params)

    ComputeEnvironmentScores.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityProfiles/{securityProfilesId}/environments/{environmentsId}:computeEnvironmentScores',
        http_method='POST',
        method_id='apigee.organizations.securityProfiles.environments.computeEnvironmentScores',
        ordered_params=['profileEnvironment'],
        path_params=['profileEnvironment'],
        query_params=[],
        relative_path='v1/{+profileEnvironment}:computeEnvironmentScores',
        request_field='googleCloudApigeeV1ComputeEnvironmentScoresRequest',
        request_type_name='ApigeeOrganizationsSecurityProfilesEnvironmentsComputeEnvironmentScoresRequest',
        response_type_name='GoogleCloudApigeeV1ComputeEnvironmentScoresResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""CreateSecurityProfileEnvironmentAssociation creates profile environment association i.e. attaches environment to security profile.

      Args:
        request: (ApigeeOrganizationsSecurityProfilesEnvironmentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SecurityProfileEnvironmentAssociation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityProfiles/{securityProfilesId}/environments',
        http_method='POST',
        method_id='apigee.organizations.securityProfiles.environments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/environments',
        request_field='googleCloudApigeeV1SecurityProfileEnvironmentAssociation',
        request_type_name='ApigeeOrganizationsSecurityProfilesEnvironmentsCreateRequest',
        response_type_name='GoogleCloudApigeeV1SecurityProfileEnvironmentAssociation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""DeleteSecurityProfileEnvironmentAssociation removes profile environment association i.e. detaches environment from security profile.

      Args:
        request: (ApigeeOrganizationsSecurityProfilesEnvironmentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityProfiles/{securityProfilesId}/environments/{environmentsId}',
        http_method='DELETE',
        method_id='apigee.organizations.securityProfiles.environments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsSecurityProfilesEnvironmentsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

  class OrganizationsSecurityProfilesService(base_api.BaseApiService):
    """Service class for the organizations_securityProfiles resource."""

    _NAME = 'organizations_securityProfiles'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsSecurityProfilesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""CreateSecurityProfile create a new custom security profile.

      Args:
        request: (ApigeeOrganizationsSecurityProfilesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SecurityProfile) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityProfiles',
        http_method='POST',
        method_id='apigee.organizations.securityProfiles.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['securityProfileId'],
        relative_path='v1/{+parent}/securityProfiles',
        request_field='googleCloudApigeeV1SecurityProfile',
        request_type_name='ApigeeOrganizationsSecurityProfilesCreateRequest',
        response_type_name='GoogleCloudApigeeV1SecurityProfile',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""DeleteSecurityProfile delete a profile with all its revisions.

      Args:
        request: (ApigeeOrganizationsSecurityProfilesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityProfiles/{securityProfilesId}',
        http_method='DELETE',
        method_id='apigee.organizations.securityProfiles.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsSecurityProfilesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""GetSecurityProfile gets the specified security profile. Returns NOT_FOUND if security profile is not present for the specified organization.

      Args:
        request: (ApigeeOrganizationsSecurityProfilesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SecurityProfile) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityProfiles/{securityProfilesId}',
        http_method='GET',
        method_id='apigee.organizations.securityProfiles.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsSecurityProfilesGetRequest',
        response_type_name='GoogleCloudApigeeV1SecurityProfile',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""ListSecurityProfiles lists all the security profiles associated with the org including attached and unattached profiles.

      Args:
        request: (ApigeeOrganizationsSecurityProfilesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListSecurityProfilesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityProfiles',
        http_method='GET',
        method_id='apigee.organizations.securityProfiles.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/securityProfiles',
        request_field='',
        request_type_name='ApigeeOrganizationsSecurityProfilesListRequest',
        response_type_name='GoogleCloudApigeeV1ListSecurityProfilesResponse',
        supports_download=False,
    )

    def ListRevisions(self, request, global_params=None):
      r"""ListSecurityProfileRevisions lists all the revisions of the security profile.

      Args:
        request: (ApigeeOrganizationsSecurityProfilesListRevisionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListSecurityProfileRevisionsResponse) The response message.
      """
      config = self.GetMethodConfig('ListRevisions')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListRevisions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityProfiles/{securityProfilesId}:listRevisions',
        http_method='GET',
        method_id='apigee.organizations.securityProfiles.listRevisions',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+name}:listRevisions',
        request_field='',
        request_type_name='ApigeeOrganizationsSecurityProfilesListRevisionsRequest',
        response_type_name='GoogleCloudApigeeV1ListSecurityProfileRevisionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""UpdateSecurityProfile update the metadata of security profile.

      Args:
        request: (ApigeeOrganizationsSecurityProfilesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SecurityProfile) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityProfiles/{securityProfilesId}',
        http_method='PATCH',
        method_id='apigee.organizations.securityProfiles.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1SecurityProfile',
        request_type_name='ApigeeOrganizationsSecurityProfilesPatchRequest',
        response_type_name='GoogleCloudApigeeV1SecurityProfile',
        supports_download=False,
    )

  class OrganizationsSecurityincidentenvironmentsService(base_api.BaseApiService):
    """Service class for the organizations_securityincidentenvironments resource."""

    _NAME = 'organizations_securityincidentenvironments'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsSecurityincidentenvironmentsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all the Environments in an organization with Security Incident Stats.

      Args:
        request: (ApigeeOrganizationsSecurityincidentenvironmentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListSecurityIncidentEnvironmentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/securityincidentenvironments',
        http_method='GET',
        method_id='apigee.organizations.securityincidentenvironments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/securityincidentenvironments',
        request_field='',
        request_type_name='ApigeeOrganizationsSecurityincidentenvironmentsListRequest',
        response_type_name='GoogleCloudApigeeV1ListSecurityIncidentEnvironmentsResponse',
        supports_download=False,
    )

  class OrganizationsSharedflowsDeploymentsService(base_api.BaseApiService):
    """Service class for the organizations_sharedflows_deployments resource."""

    _NAME = 'organizations_sharedflows_deployments'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsSharedflowsDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all deployments of a shared flow.

      Args:
        request: (ApigeeOrganizationsSharedflowsDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sharedflows/{sharedflowsId}/deployments',
        http_method='GET',
        method_id='apigee.organizations.sharedflows.deployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/deployments',
        request_field='',
        request_type_name='ApigeeOrganizationsSharedflowsDeploymentsListRequest',
        response_type_name='GoogleCloudApigeeV1ListDeploymentsResponse',
        supports_download=False,
    )

  class OrganizationsSharedflowsRevisionsDeploymentsService(base_api.BaseApiService):
    """Service class for the organizations_sharedflows_revisions_deployments resource."""

    _NAME = 'organizations_sharedflows_revisions_deployments'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsSharedflowsRevisionsDeploymentsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists all deployments of a shared flow revision.

      Args:
        request: (ApigeeOrganizationsSharedflowsRevisionsDeploymentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListDeploymentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sharedflows/{sharedflowsId}/revisions/{revisionsId}/deployments',
        http_method='GET',
        method_id='apigee.organizations.sharedflows.revisions.deployments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/deployments',
        request_field='',
        request_type_name='ApigeeOrganizationsSharedflowsRevisionsDeploymentsListRequest',
        response_type_name='GoogleCloudApigeeV1ListDeploymentsResponse',
        supports_download=False,
    )

  class OrganizationsSharedflowsRevisionsService(base_api.BaseApiService):
    """Service class for the organizations_sharedflows_revisions resource."""

    _NAME = 'organizations_sharedflows_revisions'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsSharedflowsRevisionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a shared flow and all associated policies, resources, and revisions. You must undeploy the shared flow before deleting it.

      Args:
        request: (ApigeeOrganizationsSharedflowsRevisionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SharedFlowRevision) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sharedflows/{sharedflowsId}/revisions/{revisionsId}',
        http_method='DELETE',
        method_id='apigee.organizations.sharedflows.revisions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsSharedflowsRevisionsDeleteRequest',
        response_type_name='GoogleCloudApigeeV1SharedFlowRevision',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a revision of a shared flow. To download the shared flow configuration bundle for the specified revision as a zip file, set the `format` query parameter to `bundle`. If you are using curl, specify `-o filename.zip` to save the output to a file; otherwise, it displays to `stdout`. Then, develop the shared flow configuration locally and upload the updated sharedFlow configuration revision, as described in [updateSharedFlowRevision](updateSharedFlowRevision).

      Args:
        request: (ApigeeOrganizationsSharedflowsRevisionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sharedflows/{sharedflowsId}/revisions/{revisionsId}',
        http_method='GET',
        method_id='apigee.organizations.sharedflows.revisions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['format'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsSharedflowsRevisionsGetRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

    def UpdateSharedFlowRevision(self, request, global_params=None):
      r"""Updates a shared flow revision. This operation is only allowed on revisions which have never been deployed. After deployment a revision becomes immutable, even if it becomes undeployed. The payload is a ZIP-formatted shared flow. Content type must be either multipart/form-data or application/octet-stream.

      Args:
        request: (ApigeeOrganizationsSharedflowsRevisionsUpdateSharedFlowRevisionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SharedFlowRevision) The response message.
      """
      config = self.GetMethodConfig('UpdateSharedFlowRevision')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateSharedFlowRevision.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sharedflows/{sharedflowsId}/revisions/{revisionsId}',
        http_method='POST',
        method_id='apigee.organizations.sharedflows.revisions.updateSharedFlowRevision',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['validate'],
        relative_path='v1/{+name}',
        request_field='googleApiHttpBody',
        request_type_name='ApigeeOrganizationsSharedflowsRevisionsUpdateSharedFlowRevisionRequest',
        response_type_name='GoogleCloudApigeeV1SharedFlowRevision',
        supports_download=False,
    )

  class OrganizationsSharedflowsService(base_api.BaseApiService):
    """Service class for the organizations_sharedflows resource."""

    _NAME = 'organizations_sharedflows'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsSharedflowsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Uploads a ZIP-formatted shared flow configuration bundle to an organization. If the shared flow already exists, this creates a new revision of it. If the shared flow does not exist, this creates it. Once imported, the shared flow revision must be deployed before it can be accessed at runtime. The size limit of a shared flow bundle is 15 MB.

      Args:
        request: (ApigeeOrganizationsSharedflowsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SharedFlowRevision) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sharedflows',
        http_method='POST',
        method_id='apigee.organizations.sharedflows.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['action', 'name'],
        relative_path='v1/{+parent}/sharedflows',
        request_field='googleApiHttpBody',
        request_type_name='ApigeeOrganizationsSharedflowsCreateRequest',
        response_type_name='GoogleCloudApigeeV1SharedFlowRevision',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a shared flow and all it's revisions. The shared flow must be undeployed before you can delete it.

      Args:
        request: (ApigeeOrganizationsSharedflowsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SharedFlow) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sharedflows/{sharedflowsId}',
        http_method='DELETE',
        method_id='apigee.organizations.sharedflows.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsSharedflowsDeleteRequest',
        response_type_name='GoogleCloudApigeeV1SharedFlow',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a shared flow by name, including a list of its revisions.

      Args:
        request: (ApigeeOrganizationsSharedflowsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SharedFlow) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sharedflows/{sharedflowsId}',
        http_method='GET',
        method_id='apigee.organizations.sharedflows.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsSharedflowsGetRequest',
        response_type_name='GoogleCloudApigeeV1SharedFlow',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all shared flows in the organization.

      Args:
        request: (ApigeeOrganizationsSharedflowsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListSharedFlowsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sharedflows',
        http_method='GET',
        method_id='apigee.organizations.sharedflows.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['includeMetaData', 'includeRevisions'],
        relative_path='v1/{+parent}/sharedflows',
        request_field='',
        request_type_name='ApigeeOrganizationsSharedflowsListRequest',
        response_type_name='GoogleCloudApigeeV1ListSharedFlowsResponse',
        supports_download=False,
    )

  class OrganizationsSitesApicategoriesService(base_api.BaseApiService):
    """Service class for the organizations_sites_apicategories resource."""

    _NAME = 'organizations_sites_apicategories'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsSitesApicategoriesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new category on the portal.

      Args:
        request: (ApigeeOrganizationsSitesApicategoriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ApiCategory) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sites/{sitesId}/apicategories',
        http_method='POST',
        method_id='apigee.organizations.sites.apicategories.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/apicategories',
        request_field='googleCloudApigeeV1ApiCategoryData',
        request_type_name='ApigeeOrganizationsSitesApicategoriesCreateRequest',
        response_type_name='GoogleCloudApigeeV1ApiCategory',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a category from the portal.

      Args:
        request: (ApigeeOrganizationsSitesApicategoriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1DeleteResponse) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sites/{sitesId}/apicategories/{apicategoriesId}',
        http_method='DELETE',
        method_id='apigee.organizations.sites.apicategories.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsSitesApicategoriesDeleteRequest',
        response_type_name='GoogleCloudApigeeV1DeleteResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a category on the portal.

      Args:
        request: (ApigeeOrganizationsSitesApicategoriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ApiCategory) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sites/{sitesId}/apicategories/{apicategoriesId}',
        http_method='GET',
        method_id='apigee.organizations.sites.apicategories.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsSitesApicategoriesGetRequest',
        response_type_name='GoogleCloudApigeeV1ApiCategory',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the categories on the portal.

      Args:
        request: (ApigeeOrganizationsSitesApicategoriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListApiCategoriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sites/{sitesId}/apicategories',
        http_method='GET',
        method_id='apigee.organizations.sites.apicategories.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/apicategories',
        request_field='',
        request_type_name='ApigeeOrganizationsSitesApicategoriesListRequest',
        response_type_name='GoogleCloudApigeeV1ListApiCategoriesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a category on the portal.

      Args:
        request: (GoogleCloudApigeeV1ApiCategoryData) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ApiCategory) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/sites/{sitesId}/apicategories/{apicategoriesId}',
        http_method='PATCH',
        method_id='apigee.organizations.sites.apicategories.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='GoogleCloudApigeeV1ApiCategoryData',
        response_type_name='GoogleCloudApigeeV1ApiCategory',
        supports_download=False,
    )

  class OrganizationsSitesService(base_api.BaseApiService):
    """Service class for the organizations_sites resource."""

    _NAME = 'organizations_sites'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsSitesService, self).__init__(client)
      self._upload_configs = {
          }

  class OrganizationsService(base_api.BaseApiService):
    """Service class for the organizations resource."""

    _NAME = 'organizations'

    def __init__(self, client):
      super(ApigeeV1.OrganizationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an Apigee organization. See [Create an Apigee organization](https://cloud.google.com/apigee/docs/api-platform/get-started/create-org).

      Args:
        request: (ApigeeOrganizationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        http_method='POST',
        method_id='apigee.organizations.create',
        ordered_params=[],
        path_params=[],
        query_params=['parent'],
        relative_path='v1/organizations',
        request_field='googleCloudApigeeV1Organization',
        request_type_name='ApigeeOrganizationsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Delete an Apigee organization. For organizations with BillingType EVALUATION, an immediate deletion is performed. For paid organizations, a soft-deletion is performed. The organization can be restored within the soft-deletion period which can be controlled using the retention field in the request.

      Args:
        request: (ApigeeOrganizationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}',
        http_method='DELETE',
        method_id='apigee.organizations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['retention'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the profile for an Apigee organization. See [Understanding organizations](https://cloud.google.com/apigee/docs/api-platform/fundamentals/organization-structure).

      Args:
        request: (ApigeeOrganizationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Organization) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}',
        http_method='GET',
        method_id='apigee.organizations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsGetRequest',
        response_type_name='GoogleCloudApigeeV1Organization',
        supports_download=False,
    )

    def GetControlPlaneAccess(self, request, global_params=None):
      r"""Lists the service accounts with the permissions required to allow Apigee runtime-plane components access to control plane resources. Currently, the permissions required are to: 1. Allow Synchronizer to download environment data from the control plane. 2. Allow the UDCA to upload analytics data. 3. Allow the Logger component to write logs to the control plane. For more information regarding the Synchronizer, see [Configure the Synchronizer](https://cloud.google.com/apigee/docs/hybrid/latest/synchronizer-access). **Note**: Available to Apigee hybrid only.

      Args:
        request: (ApigeeOrganizationsGetControlPlaneAccessRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ControlPlaneAccess) The response message.
      """
      config = self.GetMethodConfig('GetControlPlaneAccess')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetControlPlaneAccess.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/controlPlaneAccess',
        http_method='GET',
        method_id='apigee.organizations.getControlPlaneAccess',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsGetControlPlaneAccessRequest',
        response_type_name='GoogleCloudApigeeV1ControlPlaneAccess',
        supports_download=False,
    )

    def GetDeployedIngressConfig(self, request, global_params=None):
      r"""Gets the deployed ingress configuration for an organization.

      Args:
        request: (ApigeeOrganizationsGetDeployedIngressConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1IngressConfig) The response message.
      """
      config = self.GetMethodConfig('GetDeployedIngressConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetDeployedIngressConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/deployedIngressConfig',
        http_method='GET',
        method_id='apigee.organizations.getDeployedIngressConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['view'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsGetDeployedIngressConfigRequest',
        response_type_name='GoogleCloudApigeeV1IngressConfig',
        supports_download=False,
    )

    def GetProjectMapping(self, request, global_params=None):
      r"""Gets the project ID and region for an Apigee organization.

      Args:
        request: (ApigeeOrganizationsGetProjectMappingRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1OrganizationProjectMapping) The response message.
      """
      config = self.GetMethodConfig('GetProjectMapping')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetProjectMapping.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}:getProjectMapping',
        http_method='GET',
        method_id='apigee.organizations.getProjectMapping',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:getProjectMapping',
        request_field='',
        request_type_name='ApigeeOrganizationsGetProjectMappingRequest',
        response_type_name='GoogleCloudApigeeV1OrganizationProjectMapping',
        supports_download=False,
    )

    def GetRuntimeConfig(self, request, global_params=None):
      r"""Get runtime config for an organization.

      Args:
        request: (ApigeeOrganizationsGetRuntimeConfigRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1RuntimeConfig) The response message.
      """
      config = self.GetMethodConfig('GetRuntimeConfig')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetRuntimeConfig.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/runtimeConfig',
        http_method='GET',
        method_id='apigee.organizations.getRuntimeConfig',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='ApigeeOrganizationsGetRuntimeConfigRequest',
        response_type_name='GoogleCloudApigeeV1RuntimeConfig',
        supports_download=False,
    )

    def GetSyncAuthorization(self, request, global_params=None):
      r"""Lists the service accounts with the permissions required to allow the Synchronizer to download environment data from the control plane. An ETag is returned in the response to `getSyncAuthorization`. Pass that ETag when calling [setSyncAuthorization](setSyncAuthorization) to ensure that you are updating the correct version. If you don't pass the ETag in the call to `setSyncAuthorization`, then the existing authorization is overwritten indiscriminately. For more information, see [Configure the Synchronizer](https://cloud.google.com/apigee/docs/hybrid/latest/synchronizer-access). **Note**: Available to Apigee hybrid only.

      Args:
        request: (ApigeeOrganizationsGetSyncAuthorizationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SyncAuthorization) The response message.
      """
      config = self.GetMethodConfig('GetSyncAuthorization')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetSyncAuthorization.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}:getSyncAuthorization',
        http_method='POST',
        method_id='apigee.organizations.getSyncAuthorization',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:getSyncAuthorization',
        request_field='googleCloudApigeeV1GetSyncAuthorizationRequest',
        request_type_name='ApigeeOrganizationsGetSyncAuthorizationRequest',
        response_type_name='GoogleCloudApigeeV1SyncAuthorization',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Apigee organizations and associated Google Cloud projects that you have permission to access. See [Understanding organizations](https://cloud.google.com/apigee/docs/api-platform/fundamentals/organization-structure).

      Args:
        request: (ApigeeOrganizationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1ListOrganizationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations',
        http_method='GET',
        method_id='apigee.organizations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}',
        request_field='',
        request_type_name='ApigeeOrganizationsListRequest',
        response_type_name='GoogleCloudApigeeV1ListOrganizationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates properties for an Apigee organization with patch semantics using a field mask.

      Args:
        request: (ApigeeOrganizationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}',
        http_method='PATCH',
        method_id='apigee.organizations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1Organization',
        request_type_name='ApigeeOrganizationsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetAddons(self, request, global_params=None):
      r"""Configures the add-ons for the Apigee organization. The existing add-on configuration will be fully replaced.

      Args:
        request: (ApigeeOrganizationsSetAddonsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('SetAddons')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetAddons.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}:setAddons',
        http_method='POST',
        method_id='apigee.organizations.setAddons',
        ordered_params=['org'],
        path_params=['org'],
        query_params=[],
        relative_path='v1/{+org}:setAddons',
        request_field='googleCloudApigeeV1SetAddonsRequest',
        request_type_name='ApigeeOrganizationsSetAddonsRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SetSyncAuthorization(self, request, global_params=None):
      r"""Sets the permissions required to allow the Synchronizer to download environment data from the control plane. You must call this API to enable proper functioning of hybrid. Pass the ETag when calling `setSyncAuthorization` to ensure that you are updating the correct version. To get an ETag, call [getSyncAuthorization](getSyncAuthorization). If you don't pass the ETag in the call to `setSyncAuthorization`, then the existing authorization is overwritten indiscriminately. For more information, see [Configure the Synchronizer](https://cloud.google.com/apigee/docs/hybrid/latest/synchronizer-access). **Note**: Available to Apigee hybrid only.

      Args:
        request: (ApigeeOrganizationsSetSyncAuthorizationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1SyncAuthorization) The response message.
      """
      config = self.GetMethodConfig('SetSyncAuthorization')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetSyncAuthorization.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}:setSyncAuthorization',
        http_method='POST',
        method_id='apigee.organizations.setSyncAuthorization',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:setSyncAuthorization',
        request_field='googleCloudApigeeV1SyncAuthorization',
        request_type_name='ApigeeOrganizationsSetSyncAuthorizationRequest',
        response_type_name='GoogleCloudApigeeV1SyncAuthorization',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Tests the permissions of a user on an organization, and returns a subset of permissions that the user has on the organization. If the organization does not exist, an empty permission set is returned (a NOT_FOUND error is not returned).

      Args:
        request: (ApigeeOrganizationsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}:testIamPermissions',
        http_method='POST',
        method_id='apigee.organizations.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='googleIamV1TestIamPermissionsRequest',
        request_type_name='ApigeeOrganizationsTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

    def Undelete(self, request, global_params=None):
      r"""Undelete an Apigee organization that is soft-deleted.

      Args:
        request: (ApigeeOrganizationsUndeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Undelete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Undelete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}:undelete',
        http_method='POST',
        method_id='apigee.organizations.undelete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:undelete',
        request_field='googleCloudApigeeV1UndeleteOrganizationRequest',
        request_type_name='ApigeeOrganizationsUndeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Update(self, request, global_params=None):
      r"""Updates the properties for an Apigee organization. No other fields in the organization profile will be updated.

      Args:
        request: (GoogleCloudApigeeV1Organization) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudApigeeV1Organization) The response message.
      """
      config = self.GetMethodConfig('Update')
      return self._RunMethod(
          config, request, global_params=global_params)

    Update.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}',
        http_method='PUT',
        method_id='apigee.organizations.update',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='<request>',
        request_type_name='GoogleCloudApigeeV1Organization',
        response_type_name='GoogleCloudApigeeV1Organization',
        supports_download=False,
    )

    def UpdateControlPlaneAccess(self, request, global_params=None):
      r"""Updates the permissions required to allow Apigee runtime-plane components access to the control plane. Currently, the permissions required are to: 1. Allow Synchronizer to download environment data from the control plane. 2. Allow the UDCA to upload analytics data. 3. Allow the Logger component to write logs to the control plane. You must call this API to enable proper functioning of hybrid. For more information regarding the Synchronizer, see [Configure the Synchronizer](https://cloud.google.com/apigee/docs/hybrid/latest/synchronizer-access). **Note**: Available to Apigee hybrid only.

      Args:
        request: (ApigeeOrganizationsUpdateControlPlaneAccessRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('UpdateControlPlaneAccess')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateControlPlaneAccess.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/organizations/{organizationsId}/controlPlaneAccess',
        http_method='PATCH',
        method_id='apigee.organizations.updateControlPlaneAccess',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudApigeeV1ControlPlaneAccess',
        request_type_name='ApigeeOrganizationsUpdateControlPlaneAccessRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(ApigeeV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

    def ProvisionOrganization(self, request, global_params=None):
      r"""Provisions a new Apigee organization with a functioning runtime. This is the standard way to create trial organizations for a free Apigee trial.

      Args:
        request: (ApigeeProjectsProvisionOrganizationRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('ProvisionOrganization')
      return self._RunMethod(
          config, request, global_params=global_params)

    ProvisionOrganization.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}:provisionOrganization',
        http_method='POST',
        method_id='apigee.projects.provisionOrganization',
        ordered_params=['project'],
        path_params=['project'],
        query_params=[],
        relative_path='v1/{+project}:provisionOrganization',
        request_field='googleCloudApigeeV1ProvisionOrganizationRequest',
        request_type_name='ApigeeProjectsProvisionOrganizationRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

"""Generated message classes for apigee version v1.

Use the Apigee API to programmatically develop and manage APIs with a set of
RESTful operations. Develop and secure API proxies, deploy and undeploy API
proxy revisions, monitor APIs, configure environments, manage users, and more.
Note: This product is available as a free trial for a time period of 60 days.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'apigee'


class ApigeeHybridIssuersListRequest(_messages.Message):
  r"""A ApigeeHybridIssuersListRequest object.

  Fields:
    name: Required. Must be of the form `hybrid/issuers`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsAnalyticsDatastoresCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsAnalyticsDatastoresCreateRequest object.

  Fields:
    googleCloudApigeeV1Datastore: A GoogleCloudApigeeV1Datastore resource to
      be passed as the request body.
    parent: Required. The parent organization name. Must be of the form
      `organizations/{org}`.
  """

  googleCloudApigeeV1Datastore = _messages.MessageField('GoogleCloudApigeeV1Datastore', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsAnalyticsDatastoresDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsAnalyticsDatastoresDeleteRequest object.

  Fields:
    name: Required. Resource name of the Datastore to be deleted. Must be of
      the form `organizations/{org}/analytics/datastores/{datastoreId}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsAnalyticsDatastoresGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsAnalyticsDatastoresGetRequest object.

  Fields:
    name: Required. Resource name of the Datastore to be get. Must be of the
      form `organizations/{org}/analytics/datastores/{datastoreId}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsAnalyticsDatastoresListRequest(_messages.Message):
  r"""A ApigeeOrganizationsAnalyticsDatastoresListRequest object.

  Fields:
    parent: Required. The parent organization name. Must be of the form
      `organizations/{org}`.
    targetType: Optional. TargetType is used to fetch all Datastores that
      match the type
  """

  parent = _messages.StringField(1, required=True)
  targetType = _messages.StringField(2)


class ApigeeOrganizationsAnalyticsDatastoresTestRequest(_messages.Message):
  r"""A ApigeeOrganizationsAnalyticsDatastoresTestRequest object.

  Fields:
    googleCloudApigeeV1Datastore: A GoogleCloudApigeeV1Datastore resource to
      be passed as the request body.
    parent: Required. The parent organization name Must be of the form
      `organizations/{org}`
  """

  googleCloudApigeeV1Datastore = _messages.MessageField('GoogleCloudApigeeV1Datastore', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsAnalyticsDatastoresUpdateRequest(_messages.Message):
  r"""A ApigeeOrganizationsAnalyticsDatastoresUpdateRequest object.

  Fields:
    googleCloudApigeeV1Datastore: A GoogleCloudApigeeV1Datastore resource to
      be passed as the request body.
    name: Required. The resource name of datastore to be updated. Must be of
      the form `organizations/{org}/analytics/datastores/{datastoreId}`
  """

  googleCloudApigeeV1Datastore = _messages.MessageField('GoogleCloudApigeeV1Datastore', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsApiproductsAttributesDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsApiproductsAttributesDeleteRequest object.

  Fields:
    name: Required. Name of the API product attribute. Use the following
      structure in your request:
      `organizations/{org}/apiproducts/{apiproduct}/attributes/{attribute}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsApiproductsAttributesGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsApiproductsAttributesGetRequest object.

  Fields:
    name: Required. Name of the API product attribute. Use the following
      structure in your request:
      `organizations/{org}/apiproducts/{apiproduct}/attributes/{attribute}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsApiproductsAttributesListRequest(_messages.Message):
  r"""A ApigeeOrganizationsApiproductsAttributesListRequest object.

  Fields:
    parent: Required. Name of the API product. Use the following structure in
      your request: `organizations/{org}/apiproducts/{apiproduct}`
  """

  parent = _messages.StringField(1, required=True)


class ApigeeOrganizationsApiproductsAttributesRequest(_messages.Message):
  r"""A ApigeeOrganizationsApiproductsAttributesRequest object.

  Fields:
    googleCloudApigeeV1Attributes: A GoogleCloudApigeeV1Attributes resource to
      be passed as the request body.
    name: Required. Name of the API product. Use the following structure in
      your request: `organizations/{org}/apiproducts/{apiproduct}`
  """

  googleCloudApigeeV1Attributes = _messages.MessageField('GoogleCloudApigeeV1Attributes', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsApiproductsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsApiproductsCreateRequest object.

  Fields:
    googleCloudApigeeV1ApiProduct: A GoogleCloudApigeeV1ApiProduct resource to
      be passed as the request body.
    parent: Required. Name of the organization in which the API product will
      be created. Use the following structure in your request:
      `organizations/{org}`
  """

  googleCloudApigeeV1ApiProduct = _messages.MessageField('GoogleCloudApigeeV1ApiProduct', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsApiproductsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsApiproductsDeleteRequest object.

  Fields:
    name: Required. Name of the API product. Use the following structure in
      your request: `organizations/{org}/apiproducts/{apiproduct}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsApiproductsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsApiproductsGetRequest object.

  Fields:
    name: Required. Name of the API product. Use the following structure in
      your request: `organizations/{org}/apiproducts/{apiproduct}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsApiproductsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsApiproductsListRequest object.

  Fields:
    attributename: Name of the attribute used to filter the search.
    attributevalue: Value of the attribute used to filter the search.
    count: Enter the number of API products you want returned in the API call.
      The limit is 1000.
    expand: Flag that specifies whether to expand the results. Set to `true`
      to get expanded details about each API.
    filter: The filter expression to be used to get the list of API products,
      where filtering can be done on name. Example: filter = "name = foobar"
    pageSize: Count of API products a single page can have in the response. If
      unspecified, at most 100 API products will be returned. The maximum
      value is 100; values above 100 will be coerced to 100.
    pageToken: The starting index record for listing the developers.
    parent: Required. Name of the organization. Use the following structure in
      your request: `organizations/{org}`
    startKey: Gets a list of API products starting with a specific API product
      in the list. For example, if you're returning 50 API products at a time
      (using the `count` query parameter), you can view products 50-99 by
      entering the name of the 50th API product in the first API (without
      using `startKey`). Product name is case sensitive.
  """

  attributename = _messages.StringField(1)
  attributevalue = _messages.StringField(2)
  count = _messages.IntegerField(3)
  expand = _messages.BooleanField(4)
  filter = _messages.StringField(5)
  pageSize = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(7)
  parent = _messages.StringField(8, required=True)
  startKey = _messages.StringField(9)


class ApigeeOrganizationsApiproductsRateplansCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsApiproductsRateplansCreateRequest object.

  Fields:
    googleCloudApigeeV1RatePlan: A GoogleCloudApigeeV1RatePlan resource to be
      passed as the request body.
    parent: Required. Name of the API product that is associated with the rate
      plan. Use the following structure in your request:
      `organizations/{org}/apiproducts/{apiproduct}`
  """

  googleCloudApigeeV1RatePlan = _messages.MessageField('GoogleCloudApigeeV1RatePlan', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsApiproductsRateplansDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsApiproductsRateplansDeleteRequest object.

  Fields:
    name: Required. ID of the rate plan. Use the following structure in your
      request:
      `organizations/{org}/apiproducts/{apiproduct}/rateplans/{rateplan}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsApiproductsRateplansGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsApiproductsRateplansGetRequest object.

  Fields:
    name: Required. Name of the rate plan. Use the following structure in your
      request:
      `organizations/{org}/apiproducts/{apiproduct}/rateplans/{rateplan}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsApiproductsRateplansListRequest(_messages.Message):
  r"""A ApigeeOrganizationsApiproductsRateplansListRequest object.

  Enums:
    StateValueValuesEnum: State of the rate plans (`DRAFT`, `PUBLISHED`) that
      you want to display.

  Fields:
    count: Number of rate plans to return in the API call. Use with the
      `startKey` parameter to provide more targeted filtering. The maximum
      limit is 1000. Defaults to 100.
    expand: Flag that specifies whether to expand the results. Set to `true`
      to get expanded details about each API. Defaults to `false`.
    orderBy: Name of the attribute used for sorting. Valid values include: *
      `name`: Name of the rate plan. * `state`: State of the rate plan
      (`DRAFT`, `PUBLISHED`). * `startTime`: Time when the rate plan becomes
      active. * `endTime`: Time when the rate plan expires. **Note**: Not
      supported by Apigee at this time.
    parent: Required. Name of the API product. Use the following structure in
      your request: `organizations/{org}/apiproducts/{apiproduct}` Use
      `organizations/{org}/apiproducts/-` to return rate plans for all API
      products within the organization.
    startKey: Name of the rate plan from which to start displaying the list of
      rate plans. If omitted, the list starts from the first item. For
      example, to view the rate plans from 51-150, set the value of `startKey`
      to the name of the 51st rate plan and set the value of `count` to 100.
    state: State of the rate plans (`DRAFT`, `PUBLISHED`) that you want to
      display.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of the rate plans (`DRAFT`, `PUBLISHED`) that you want to
    display.

    Values:
      STATE_UNSPECIFIED: State of the rate plan is not specified.
      DRAFT: Rate plan is in draft mode and only visible to API providers.
      PUBLISHED: Rate plan is published and will become visible to developers
        for the configured duration (between `startTime` and `endTime`).
    """
    STATE_UNSPECIFIED = 0
    DRAFT = 1
    PUBLISHED = 2

  count = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  expand = _messages.BooleanField(2)
  orderBy = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  startKey = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class ApigeeOrganizationsApisCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsApisCreateRequest object.

  Fields:
    action: Action to perform when importing an API proxy configuration
      bundle. Set this parameter to one of the following values: * `import` to
      import the API proxy configuration bundle. * `validate` to validate the
      API proxy configuration bundle without importing it.
    googleApiHttpBody: A GoogleApiHttpBody resource to be passed as the
      request body.
    name: Name of the API proxy. Restrict the characters used to: A-Za-z0-9._-
    parent: Required. Name of the organization in the following format:
      `organizations/{org}`
    validate: Ignored. All uploads are validated regardless of the value of
      this field. Maintained for compatibility with Apigee Edge API.
  """

  action = _messages.StringField(1)
  googleApiHttpBody = _messages.MessageField('GoogleApiHttpBody', 2)
  name = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  validate = _messages.BooleanField(5)


class ApigeeOrganizationsApisDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsApisDeleteRequest object.

  Fields:
    name: Required. Name of the API proxy in the following format:
      `organizations/{org}/apis/{api}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsApisDeploymentsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsApisDeploymentsListRequest object.

  Fields:
    parent: Required. Name of the API proxy for which to return deployment
      information in the following format: `organizations/{org}/apis/{api}`
  """

  parent = _messages.StringField(1, required=True)


class ApigeeOrganizationsApisGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsApisGetRequest object.

  Fields:
    name: Required. Name of the API proxy in the following format:
      `organizations/{org}/apis/{api}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsApisKeyvaluemapsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsApisKeyvaluemapsCreateRequest object.

  Fields:
    googleCloudApigeeV1KeyValueMap: A GoogleCloudApigeeV1KeyValueMap resource
      to be passed as the request body.
    parent: Required. Name of the environment in which to create the key value
      map. Use the following structure in your request:
      `organizations/{org}/apis/{api}`
  """

  googleCloudApigeeV1KeyValueMap = _messages.MessageField('GoogleCloudApigeeV1KeyValueMap', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsApisKeyvaluemapsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsApisKeyvaluemapsDeleteRequest object.

  Fields:
    name: Required. Name of the key value map. Use the following structure in
      your request:
      `organizations/{org}/apis/{api}/keyvaluemaps/{keyvaluemap}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsApisKeyvaluemapsEntriesCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsApisKeyvaluemapsEntriesCreateRequest object.

  Fields:
    googleCloudApigeeV1KeyValueEntry: A GoogleCloudApigeeV1KeyValueEntry
      resource to be passed as the request body.
    parent: Required. Scope as indicated by the URI in which to create the key
      value map entry. Use **one** of the following structures in your
      request: *
      `organizations/{organization}/apis/{api}/keyvaluemaps/{keyvaluemap}`. *
      `organizations/{organization}/environments/{environment}/keyvaluemaps/{k
      eyvaluemap}` *
      `organizations/{organization}/keyvaluemaps/{keyvaluemap}`.
  """

  googleCloudApigeeV1KeyValueEntry = _messages.MessageField('GoogleCloudApigeeV1KeyValueEntry', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsApisKeyvaluemapsEntriesDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsApisKeyvaluemapsEntriesDeleteRequest object.

  Fields:
    name: Required. Scope as indicated by the URI in which to delete the key
      value map entry. Use **one** of the following structures in your
      request: * `organizations/{organization}/apis/{api}/keyvaluemaps/{keyval
      uemap}/entries/{entry}`. * `organizations/{organization}/environments/{e
      nvironment}/keyvaluemaps/{keyvaluemap}/entries/{entry}` * `organizations
      /{organization}/keyvaluemaps/{keyvaluemap}/entries/{entry}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsApisKeyvaluemapsEntriesGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsApisKeyvaluemapsEntriesGetRequest object.

  Fields:
    name: Required. Scope as indicated by the URI in which to fetch the key
      value map entry/value. Use **one** of the following structures in your
      request: * `organizations/{organization}/apis/{api}/keyvaluemaps/{keyval
      uemap}/entries/{entry}`. * `organizations/{organization}/environments/{e
      nvironment}/keyvaluemaps/{keyvaluemap}/entries/{entry}` * `organizations
      /{organization}/keyvaluemaps/{keyvaluemap}/entries/{entry}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsApisKeyvaluemapsEntriesListRequest(_messages.Message):
  r"""A ApigeeOrganizationsApisKeyvaluemapsEntriesListRequest object.

  Fields:
    pageSize: Optional. Maximum number of key value entries to return. If
      unspecified, at most 100 entries will be returned.
    pageToken: Optional. Page token. If provides, must be a valid key value
      entry returned from a previous call that can be used to retrieve the
      next page.
    parent: Required. Scope as indicated by the URI in which to list key value
      maps. Use **one** of the following structures in your request: *
      `organizations/{organization}/apis/{api}/keyvaluemaps/{keyvaluemap}`. *
      `organizations/{organization}/environments/{environment}/keyvaluemaps/{k
      eyvaluemap}` *
      `organizations/{organization}/keyvaluemaps/{keyvaluemap}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsApisListRequest(_messages.Message):
  r"""A ApigeeOrganizationsApisListRequest object.

  Fields:
    includeMetaData: Flag that specifies whether to include API proxy metadata
      in the response.
    includeRevisions: Flag that specifies whether to include a list of
      revisions in the response.
    parent: Required. Name of the organization in the following format:
      `organizations/{org}`
  """

  includeMetaData = _messages.BooleanField(1)
  includeRevisions = _messages.BooleanField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsApisPatchRequest(_messages.Message):
  r"""A ApigeeOrganizationsApisPatchRequest object.

  Fields:
    googleCloudApigeeV1ApiProxy: A GoogleCloudApigeeV1ApiProxy resource to be
      passed as the request body.
    name: Required. API proxy to update in the following format:
      `organizations/{org}/apis/{api}`
    updateMask: Required. The list of fields to update.
  """

  googleCloudApigeeV1ApiProxy = _messages.MessageField('GoogleCloudApigeeV1ApiProxy', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApigeeOrganizationsApisRevisionsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsApisRevisionsDeleteRequest object.

  Fields:
    name: Required. API proxy revision in the following format:
      `organizations/{org}/apis/{api}/revisions/{rev}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsApisRevisionsDeploymentsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsApisRevisionsDeploymentsListRequest object.

  Fields:
    parent: Required. Name of the API proxy revision for which to return
      deployment information in the following format:
      `organizations/{org}/apis/{api}/revisions/{rev}`.
  """

  parent = _messages.StringField(1, required=True)


class ApigeeOrganizationsApisRevisionsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsApisRevisionsGetRequest object.

  Fields:
    format: Format used when downloading the API proxy configuration revision.
      Set to `bundle` to download the API proxy configuration revision as a
      zip file.
    name: Required. API proxy revision in the following format:
      `organizations/{org}/apis/{api}/revisions/{rev}`
  """

  format = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsApisRevisionsUpdateApiProxyRevisionRequest(_messages.Message):
  r"""A ApigeeOrganizationsApisRevisionsUpdateApiProxyRevisionRequest object.

  Fields:
    googleApiHttpBody: A GoogleApiHttpBody resource to be passed as the
      request body.
    name: Required. API proxy revision to update in the following format:
      `organizations/{org}/apis/{api}/revisions/{rev}`
    validate: Ignored. All uploads are validated regardless of the value of
      this field. Maintained for compatibility with Apigee Edge API.
  """

  googleApiHttpBody = _messages.MessageField('GoogleApiHttpBody', 1)
  name = _messages.StringField(2, required=True)
  validate = _messages.BooleanField(3)


class ApigeeOrganizationsAppgroupsAppsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppgroupsAppsCreateRequest object.

  Fields:
    googleCloudApigeeV1AppGroupApp: A GoogleCloudApigeeV1AppGroupApp resource
      to be passed as the request body.
    parent: Required. Name of the AppGroup. Use the following structure in
      your request: `organizations/{org}/appgroups/{app_group_name}`
  """

  googleCloudApigeeV1AppGroupApp = _messages.MessageField('GoogleCloudApigeeV1AppGroupApp', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsAppgroupsAppsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppgroupsAppsDeleteRequest object.

  Fields:
    name: Required. Name of the AppGroup app. Use the following structure in
      your request:
      `organizations/{org}/appgroups/{app_group_name}/apps/{app}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsAppgroupsAppsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppgroupsAppsGetRequest object.

  Fields:
    name: Required. Name of the AppGroup app. Use the following structure in
      your request:
      `organizations/{org}/appgroups/{app_group_name}/apps/{app}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsAppgroupsAppsKeysApiproductsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppgroupsAppsKeysApiproductsDeleteRequest object.

  Fields:
    name: Required. Parent of the AppGroup app key. Use the following
      structure in your request: `organizations/{org}/appgroups/{app_group_nam
      e}/apps/{app}/keys/{key}/apiproducts/{apiproduct}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsAppgroupsAppsKeysApiproductsUpdateAppGroupAppKeyApiProductRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppgroupsAppsKeysApiproductsUpdateAppGroupAppKeyApi
  ProductRequest object.

  Fields:
    action: Approve or revoke the consumer key by setting this value to
      `approve` or `revoke` respectively. The `Content-Type` header, if set,
      must be set to `application/octet-stream`, with empty body.
    name: Required. Name of the API product in the developer app key in the
      following format: `organizations/{org}/appgroups/{app_group_name}/apps/{
      app}/keys/{key}/apiproducts/{apiproduct}`
  """

  action = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsAppgroupsAppsKeysCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppgroupsAppsKeysCreateRequest object.

  Fields:
    googleCloudApigeeV1AppGroupAppKey: A GoogleCloudApigeeV1AppGroupAppKey
      resource to be passed as the request body.
    parent: Required. Parent of the AppGroup app key. Use the following
      structure in your request:
      `organizations/{org}/appgroups/{app_group_name}/apps/{app}/keys`
  """

  googleCloudApigeeV1AppGroupAppKey = _messages.MessageField('GoogleCloudApigeeV1AppGroupAppKey', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsAppgroupsAppsKeysDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppgroupsAppsKeysDeleteRequest object.

  Fields:
    name: Required. Name of the AppGroup app key. Use the following structure
      in your request:
      `organizations/{org}/appgroups/{app_group_name}/apps/{app}/keys/{key}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsAppgroupsAppsKeysGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppgroupsAppsKeysGetRequest object.

  Fields:
    name: Required. Name of the AppGroup app key. Use the following structure
      in your request:
      `organizations/{org}/appgroups/{app_group_name}/apps/{app}/keys/{key}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsAppgroupsAppsKeysUpdateAppGroupAppKeyRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppgroupsAppsKeysUpdateAppGroupAppKeyRequest
  object.

  Fields:
    googleCloudApigeeV1UpdateAppGroupAppKeyRequest: A
      GoogleCloudApigeeV1UpdateAppGroupAppKeyRequest resource to be passed as
      the request body.
    name: Required. Name of the AppGroup app key. Use the following structure
      in your request:
      `organizations/{org}/appgroups/{app_group_name}/apps/{app}/keys/{key}`
  """

  googleCloudApigeeV1UpdateAppGroupAppKeyRequest = _messages.MessageField('GoogleCloudApigeeV1UpdateAppGroupAppKeyRequest', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsAppgroupsAppsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppgroupsAppsListRequest object.

  Fields:
    pageSize: Optional. Maximum number entries to return. If unspecified, at
      most 1000 entries will be returned.
    pageToken: Optional. Page token. If provides, must be a valid AppGroup app
      returned from a previous call that can be used to retrieve the next
      page.
    parent: Required. Name of the AppGroup. Use the following structure in
      your request: `organizations/{org}/appgroups/{app_group_name}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsAppgroupsAppsUpdateRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppgroupsAppsUpdateRequest object.

  Fields:
    action: Approve or revoke the consumer key by setting this value to
      `approve` or `revoke`. The `Content-Type` header must be set to
      `application/octet-stream`, with empty body.
    googleCloudApigeeV1AppGroupApp: A GoogleCloudApigeeV1AppGroupApp resource
      to be passed as the request body.
    name: Required. Name of the AppGroup app. Use the following structure in
      your request:
      `organizations/{org}/appgroups/{app_group_name}/apps/{app}`
  """

  action = _messages.StringField(1)
  googleCloudApigeeV1AppGroupApp = _messages.MessageField('GoogleCloudApigeeV1AppGroupApp', 2)
  name = _messages.StringField(3, required=True)


class ApigeeOrganizationsAppgroupsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppgroupsCreateRequest object.

  Fields:
    googleCloudApigeeV1AppGroup: A GoogleCloudApigeeV1AppGroup resource to be
      passed as the request body.
    parent: Required. Name of the Apigee organization in which the AppGroup is
      created. Use the following structure in your request:
      `organizations/{org}`.
  """

  googleCloudApigeeV1AppGroup = _messages.MessageField('GoogleCloudApigeeV1AppGroup', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsAppgroupsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppgroupsDeleteRequest object.

  Fields:
    name: Required. Name of the AppGroup. Use the following structure in your
      request: `organizations/{org}/appgroups/{app_group_name}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsAppgroupsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppgroupsGetRequest object.

  Fields:
    name: Required. Name of the AppGroup. Use the following structure in your
      request: `organizations/{org}/appgroups/{app_group_name}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsAppgroupsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppgroupsListRequest object.

  Fields:
    filter: The filter expression to be used to get the list of AppGroups,
      where filtering can be done on name, correlationID or channelID of the
      app group. Example: filter = "name = foobar"
    pageSize: Count of AppGroups a single page can have in the response. If
      unspecified, at most 1000 AppGroups will be returned. The maximum value
      is 1000; values above 1000 will be coerced to 1000.
    pageToken: The starting index record for listing the AppGroups.
    parent: Required. Name of the Apigee organization. Use the following
      structure in your request: `organizations/{org}`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ApigeeOrganizationsAppgroupsUpdateRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppgroupsUpdateRequest object.

  Fields:
    action: Activate or de-activate the appGroup by setting the action as
      `active` or `inactive`. The `Content-Type` header must be set to
      `application/octet-stream`, with empty body.
    googleCloudApigeeV1AppGroup: A GoogleCloudApigeeV1AppGroup resource to be
      passed as the request body.
    name: Required. Name of the AppGroup. Use the following structure in your
      request: `organizations/{org}/appgroups/{app_group_name}`
  """

  action = _messages.StringField(1)
  googleCloudApigeeV1AppGroup = _messages.MessageField('GoogleCloudApigeeV1AppGroup', 2)
  name = _messages.StringField(3, required=True)


class ApigeeOrganizationsAppsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppsGetRequest object.

  Fields:
    name: Required. App ID in the following format:
      `organizations/{org}/apps/{app}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsAppsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsAppsListRequest object.

  Fields:
    apiProduct: API product.
    apptype: Optional. 'apptype' is no longer available. Use a 'filter'
      instead.
    expand: Optional. Flag that specifies whether to return an expanded list
      of apps for the organization. Defaults to `false`.
    filter: Optional. The filter expression to be used to get the list of
      apps, where filtering can be done on developerEmail, apiProduct,
      consumerKey, status, appId, appName and appType. Examples:
      "developerEmail=<EMAIL>", "appType=AppGroup", or "appType=Developer"
      "filter" is supported from ver 1.10.0 and above.
    ids: Optional. Comma-separated list of app IDs on which to filter.
    includeCred: Optional. Flag that specifies whether to include credentials
      in the response.
    keyStatus: Optional. Key status of the app. Valid values include
      `approved` or `revoked`. Defaults to `approved`.
    pageSize: Optional. Count of apps a single page can have in the response.
      If unspecified, at most 100 apps will be returned. The maximum value is
      100; values above 100 will be coerced to 100. "page_size" is supported
      from ver 1.10.0 and above.
    pageToken: Optional. The starting index record for listing the developers.
      "page_token" is supported from ver 1.10.0 and above.
    parent: Required. Resource path of the parent in the following format:
      `organizations/{org}`
    rows: Optional. Maximum number of app IDs to return. Defaults to 10000.
    startKey: Returns the list of apps starting from the specified app ID.
    status: Optional. Filter by the status of the app. Valid values are
      `approved` or `revoked`. Defaults to `approved`.
  """

  apiProduct = _messages.StringField(1)
  apptype = _messages.StringField(2)
  expand = _messages.BooleanField(3)
  filter = _messages.StringField(4)
  ids = _messages.StringField(5)
  includeCred = _messages.BooleanField(6)
  keyStatus = _messages.StringField(7)
  pageSize = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(9)
  parent = _messages.StringField(10, required=True)
  rows = _messages.IntegerField(11)
  startKey = _messages.StringField(12)
  status = _messages.StringField(13)


class ApigeeOrganizationsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsCreateRequest object.

  Fields:
    googleCloudApigeeV1Organization: A GoogleCloudApigeeV1Organization
      resource to be passed as the request body.
    parent: Required. Name of the Google Cloud project in which to associate
      the Apigee organization. Pass the information as a query parameter using
      the following structure in your request: `projects/`
  """

  googleCloudApigeeV1Organization = _messages.MessageField('GoogleCloudApigeeV1Organization', 1)
  parent = _messages.StringField(2)


class ApigeeOrganizationsDatacollectorsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsDatacollectorsCreateRequest object.

  Fields:
    dataCollectorId: ID of the data collector. Overrides any ID in the data
      collector resource. Must be a string beginning with `dc_` that contains
      only letters, numbers, and underscores.
    googleCloudApigeeV1DataCollector: A GoogleCloudApigeeV1DataCollector
      resource to be passed as the request body.
    parent: Required. Name of the organization in which to create the data
      collector in the following format: `organizations/{org}`.
  """

  dataCollectorId = _messages.StringField(1)
  googleCloudApigeeV1DataCollector = _messages.MessageField('GoogleCloudApigeeV1DataCollector', 2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsDatacollectorsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsDatacollectorsDeleteRequest object.

  Fields:
    name: Required. Name of the data collector in the following format:
      `organizations/{org}/datacollectors/{data_collector_id}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsDatacollectorsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsDatacollectorsGetRequest object.

  Fields:
    name: Required. Name of the data collector in the following format:
      `organizations/{org}/datacollectors/{data_collector_id}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsDatacollectorsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsDatacollectorsListRequest object.

  Fields:
    pageSize: Maximum number of data collectors to return. The page size
      defaults to 25.
    pageToken: Page token, returned from a previous ListDataCollectors call,
      that you can use to retrieve the next page.
    parent: Required. Name of the organization for which to list data
      collectors in the following format: `organizations/{org}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsDatacollectorsPatchRequest(_messages.Message):
  r"""A ApigeeOrganizationsDatacollectorsPatchRequest object.

  Fields:
    googleCloudApigeeV1DataCollector: A GoogleCloudApigeeV1DataCollector
      resource to be passed as the request body.
    name: Required. Name of the data collector in the following format:
      `organizations/{org}/datacollectors/{data_collector_id}`.
    updateMask: List of fields to be updated.
  """

  googleCloudApigeeV1DataCollector = _messages.MessageField('GoogleCloudApigeeV1DataCollector', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApigeeOrganizationsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsDeleteRequest object.

  Enums:
    RetentionValueValuesEnum: Optional. This setting is applicable only for
      organizations that are soft-deleted (i.e., BillingType is not
      EVALUATION). It controls how long Organization data will be retained
      after the initial delete operation completes. During this period, the
      Organization may be restored to its last known state. After this period,
      the Organization will no longer be able to be restored.

  Fields:
    name: Required. Name of the organization. Use the following structure in
      your request: `organizations/{org}`
    retention: Optional. This setting is applicable only for organizations
      that are soft-deleted (i.e., BillingType is not EVALUATION). It controls
      how long Organization data will be retained after the initial delete
      operation completes. During this period, the Organization may be
      restored to its last known state. After this period, the Organization
      will no longer be able to be restored.
  """

  class RetentionValueValuesEnum(_messages.Enum):
    r"""Optional. This setting is applicable only for organizations that are
    soft-deleted (i.e., BillingType is not EVALUATION). It controls how long
    Organization data will be retained after the initial delete operation
    completes. During this period, the Organization may be restored to its
    last known state. After this period, the Organization will no longer be
    able to be restored.

    Values:
      DELETION_RETENTION_UNSPECIFIED: Default data retention setting of seven
        days will be applied.
      MINIMUM: Organization data will be retained for the minimum period of 24
        hours.
    """
    DELETION_RETENTION_UNSPECIFIED = 0
    MINIMUM = 1

  name = _messages.StringField(1, required=True)
  retention = _messages.EnumField('RetentionValueValuesEnum', 2)


class ApigeeOrganizationsDeploymentsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsDeploymentsListRequest object.

  Fields:
    parent: Required. Name of the organization for which to return deployment
      information in the following format: `organizations/{org}`
    sharedFlows: Optional. Flag that specifies whether to return shared flow
      or API proxy deployments. Set to `true` to return shared flow
      deployments; set to `false` to return API proxy deployments. Defaults to
      `false`.
  """

  parent = _messages.StringField(1, required=True)
  sharedFlows = _messages.BooleanField(2)


class ApigeeOrganizationsDevelopersAppsAttributesDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAppsAttributesDeleteRequest object.

  Fields:
    name: Required. Name of the developer app attribute. Use the following
      structure in your request: `organizations/{org}/developers/{developer_em
      ail}/apps/{app}/attributes/{attribute}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsDevelopersAppsAttributesGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAppsAttributesGetRequest object.

  Fields:
    name: Required. Name of the developer app attribute. Use the following
      structure in your request: `organizations/{org}/developers/{developer_em
      ail}/apps/{app}/attributes/{attribute}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsDevelopersAppsAttributesListRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAppsAttributesListRequest object.

  Fields:
    parent: Required. Name of the developer app. Use the following structure
      in your request:
      `organizations/{org}/developers/{developer_email}/apps/{app}`
  """

  parent = _messages.StringField(1, required=True)


class ApigeeOrganizationsDevelopersAppsAttributesRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAppsAttributesRequest object.

  Fields:
    googleCloudApigeeV1Attributes: A GoogleCloudApigeeV1Attributes resource to
      be passed as the request body.
    name: Required. Name of the developer app. Use the following structure in
      your request:
      `organizations/{org}/developers/{developer_email}/apps/{app}`
  """

  googleCloudApigeeV1Attributes = _messages.MessageField('GoogleCloudApigeeV1Attributes', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsDevelopersAppsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAppsCreateRequest object.

  Fields:
    googleCloudApigeeV1DeveloperApp: A GoogleCloudApigeeV1DeveloperApp
      resource to be passed as the request body.
    parent: Required. Name of the developer. Use the following structure in
      your request: `organizations/{org}/developers/{developer_email}`
  """

  googleCloudApigeeV1DeveloperApp = _messages.MessageField('GoogleCloudApigeeV1DeveloperApp', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsDevelopersAppsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAppsDeleteRequest object.

  Fields:
    name: Required. Name of the developer app. Use the following structure in
      your request:
      `organizations/{org}/developers/{developer_email}/apps/{app}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsDevelopersAppsGenerateKeyPairOrUpdateDeveloperAppStatusRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAppsGenerateKeyPairOrUpdateDeveloperAppSt
  atusRequest object.

  Fields:
    action: Action. Valid values are `approve` or `revoke`.
    googleCloudApigeeV1DeveloperApp: A GoogleCloudApigeeV1DeveloperApp
      resource to be passed as the request body.
    name: Required. Name of the developer app. Use the following structure in
      your request:
      `organizations/{org}/developers/{developer_email}/apps/{app}`
  """

  action = _messages.StringField(1)
  googleCloudApigeeV1DeveloperApp = _messages.MessageField('GoogleCloudApigeeV1DeveloperApp', 2)
  name = _messages.StringField(3, required=True)


class ApigeeOrganizationsDevelopersAppsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAppsGetRequest object.

  Fields:
    entity: **Note**: Must be used in conjunction with the `query` parameter.
      Set to `apiresources` to return the number of API resources that have
      been approved for access by a developer app in the specified Apigee
      organization.
    name: Required. Name of the developer app. Use the following structure in
      your request:
      `organizations/{org}/developers/{developer_email}/apps/{app}`
    query: **Note**: Must be used in conjunction with the `entity` parameter.
      Set to `count` to return the number of API resources that have been
      approved for access by a developer app in the specified Apigee
      organization.
  """

  entity = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  query = _messages.StringField(3)


class ApigeeOrganizationsDevelopersAppsKeysApiproductsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAppsKeysApiproductsDeleteRequest object.

  Fields:
    name: Name of the API product in the developer app key in the following
      format: `organizations/{org}/developers/{developer_email}/apps/{app}/key
      s/{key}/apiproducts/{apiproduct}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsDevelopersAppsKeysApiproductsUpdateDeveloperAppKeyApiProductRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAppsKeysApiproductsUpdateDeveloperAppKeyA
  piProductRequest object.

  Fields:
    action: Approve or revoke the consumer key by setting this value to
      `approve` or `revoke`, respectively.
    name: Name of the API product in the developer app key in the following
      format: `organizations/{org}/developers/{developer_email}/apps/{app}/key
      s/{key}/apiproducts/{apiproduct}`
  """

  action = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsDevelopersAppsKeysCreateCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAppsKeysCreateCreateRequest object.

  Fields:
    googleCloudApigeeV1DeveloperAppKey: A GoogleCloudApigeeV1DeveloperAppKey
      resource to be passed as the request body.
    parent: Parent of the developer app key. Use the following structure in
      your request: `organizations/{org}/developers/{developer_email}/apps`
  """

  googleCloudApigeeV1DeveloperAppKey = _messages.MessageField('GoogleCloudApigeeV1DeveloperAppKey', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsDevelopersAppsKeysCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAppsKeysCreateRequest object.

  Fields:
    googleCloudApigeeV1DeveloperAppKey: A GoogleCloudApigeeV1DeveloperAppKey
      resource to be passed as the request body.
    parent: Parent of the developer app key. Use the following structure in
      your request: `organizations/{org}/developers/{developer_email}/apps`
  """

  googleCloudApigeeV1DeveloperAppKey = _messages.MessageField('GoogleCloudApigeeV1DeveloperAppKey', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsDevelopersAppsKeysDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAppsKeysDeleteRequest object.

  Fields:
    name: Name of the developer app key. Use the following structure in your
      request:
      `organizations/{org}/developers/{developer_email}/apps/{app}/keys/{key}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsDevelopersAppsKeysGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAppsKeysGetRequest object.

  Fields:
    name: Name of the developer app key. Use the following structure in your
      request:
      `organizations/{org}/developers/{developer_email}/apps/{app}/keys/{key}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsDevelopersAppsKeysReplaceDeveloperAppKeyRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAppsKeysReplaceDeveloperAppKeyRequest
  object.

  Fields:
    googleCloudApigeeV1DeveloperAppKey: A GoogleCloudApigeeV1DeveloperAppKey
      resource to be passed as the request body.
    name: Name of the developer app key. Use the following structure in your
      request:
      `organizations/{org}/developers/{developer_email}/apps/{app}/keys/{key}`
  """

  googleCloudApigeeV1DeveloperAppKey = _messages.MessageField('GoogleCloudApigeeV1DeveloperAppKey', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsDevelopersAppsKeysUpdateDeveloperAppKeyRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAppsKeysUpdateDeveloperAppKeyRequest
  object.

  Fields:
    action: Approve or revoke the consumer key by setting this value to
      `approve` or `revoke`, respectively. The `Content-Type` header must be
      set to `application/octet-stream`.
    googleCloudApigeeV1DeveloperAppKey: A GoogleCloudApigeeV1DeveloperAppKey
      resource to be passed as the request body.
    name: Name of the developer app key. Use the following structure in your
      request:
      `organizations/{org}/developers/{developer_email}/apps/{app}/keys/{key}`
  """

  action = _messages.StringField(1)
  googleCloudApigeeV1DeveloperAppKey = _messages.MessageField('GoogleCloudApigeeV1DeveloperAppKey', 2)
  name = _messages.StringField(3, required=True)


class ApigeeOrganizationsDevelopersAppsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAppsListRequest object.

  Fields:
    count: Number of developer apps to return in the API call. Use with the
      `startKey` parameter to provide more targeted filtering. The limit is
      1000.
    expand: Optional. Specifies whether to expand the results. Set to `true`
      to expand the results. This query parameter is not valid if you use the
      `count` or `startKey` query parameters.
    parent: Required. Name of the developer. Use the following structure in
      your request: `organizations/{org}/developers/{developer_email}`
    shallowExpand: Optional. Specifies whether to expand the results in
      shallow mode. Set to `true` to expand the results in shallow mode.
    startKey: **Note**: Must be used in conjunction with the `count`
      parameter. Name of the developer app from which to start displaying the
      list of developer apps. For example, if you're returning 50 developer
      apps at a time (using the `count` query parameter), you can view
      developer apps 50-99 by entering the name of the 50th developer app. The
      developer app name is case sensitive.
  """

  count = _messages.IntegerField(1)
  expand = _messages.BooleanField(2)
  parent = _messages.StringField(3, required=True)
  shallowExpand = _messages.BooleanField(4)
  startKey = _messages.StringField(5)


class ApigeeOrganizationsDevelopersAttributesDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAttributesDeleteRequest object.

  Fields:
    name: Required. Name of the developer attribute. Use the following
      structure in your request: `organizations/{org}/developers/{developer_em
      ail}/attributes/{attribute}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsDevelopersAttributesGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAttributesGetRequest object.

  Fields:
    name: Required. Name of the developer attribute. Use the following
      structure in your request: `organizations/{org}/developers/{developer_em
      ail}/attributes/{attribute}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsDevelopersAttributesListRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAttributesListRequest object.

  Fields:
    parent: Required. Email address of the developer for which attributes are
      being listed. Use the following structure in your request:
      `organizations/{org}/developers/{developer_email}`
  """

  parent = _messages.StringField(1, required=True)


class ApigeeOrganizationsDevelopersAttributesRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersAttributesRequest object.

  Fields:
    googleCloudApigeeV1Attributes: A GoogleCloudApigeeV1Attributes resource to
      be passed as the request body.
    parent: Required. Email address of the developer for which attributes are
      being updated. Use the following structure in your request:
      `organizations/{org}/developers/{developer_email}`
  """

  googleCloudApigeeV1Attributes = _messages.MessageField('GoogleCloudApigeeV1Attributes', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsDevelopersBalanceAdjustRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersBalanceAdjustRequest object.

  Fields:
    googleCloudApigeeV1AdjustDeveloperBalanceRequest: A
      GoogleCloudApigeeV1AdjustDeveloperBalanceRequest resource to be passed
      as the request body.
    name: Required. Account balance for the developer. Use the following
      structure in your request:
      `organizations/{org}/developers/{developer}/balance`
  """

  googleCloudApigeeV1AdjustDeveloperBalanceRequest = _messages.MessageField('GoogleCloudApigeeV1AdjustDeveloperBalanceRequest', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsDevelopersBalanceCreditRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersBalanceCreditRequest object.

  Fields:
    googleCloudApigeeV1CreditDeveloperBalanceRequest: A
      GoogleCloudApigeeV1CreditDeveloperBalanceRequest resource to be passed
      as the request body.
    name: Required. Account balance for the developer. Use the following
      structure in your request:
      `organizations/{org}/developers/{developer}/balance`
  """

  googleCloudApigeeV1CreditDeveloperBalanceRequest = _messages.MessageField('GoogleCloudApigeeV1CreditDeveloperBalanceRequest', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsDevelopersCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersCreateRequest object.

  Fields:
    googleCloudApigeeV1Developer: A GoogleCloudApigeeV1Developer resource to
      be passed as the request body.
    parent: Required. Name of the Apigee organization in which the developer
      is created. Use the following structure in your request:
      `organizations/{org}`.
  """

  googleCloudApigeeV1Developer = _messages.MessageField('GoogleCloudApigeeV1Developer', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsDevelopersDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersDeleteRequest object.

  Fields:
    name: Required. Email address of the developer. Use the following
      structure in your request:
      `organizations/{org}/developers/{developer_email}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsDevelopersGetBalanceRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersGetBalanceRequest object.

  Fields:
    name: Required. Account balance for the developer. Use the following
      structure in your request:
      `organizations/{org}/developers/{developer}/balance`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsDevelopersGetMonetizationConfigRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersGetMonetizationConfigRequest object.

  Fields:
    name: Required. Monetization configuration for the developer. Use the
      following structure in your request:
      `organizations/{org}/developers/{developer}/monetizationConfig`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsDevelopersGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersGetRequest object.

  Fields:
    action: Status of the developer. Valid values are `active` or `inactive`.
    name: Required. Email address of the developer. Use the following
      structure in your request:
      `organizations/{org}/developers/{developer_email}`
  """

  action = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsDevelopersListRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersListRequest object.

  Fields:
    app: Optional. List only Developers that are associated with the app. Note
      that start_key, count are not applicable for this filter criteria.
    count: Optional. Number of developers to return in the API call. Use with
      the `startKey` parameter to provide more targeted filtering. The limit
      is 1000.
    expand: Specifies whether to expand the results. Set to `true` to expand
      the results. This query parameter is not valid if you use the `count` or
      `startKey` query parameters.
    filter: Optional. The filter expression to be used to get the list of
      developers, where filtering can be done on email. Example: filter =
      "email = <EMAIL>"
    ids: Optional. List of IDs to include, separated by commas.
    includeCompany: Flag that specifies whether to include company details in
      the response.
    pageSize: Optional. Count of developers a single page can have in the
      response. If unspecified, at most 100 developers will be returned. The
      maximum value is 100; values above 100 will be coerced to 100.
    pageToken: Optional. The starting index record for listing the developers.
    parent: Required. Name of the Apigee organization. Use the following
      structure in your request: `organizations/{org}`.
    startKey: **Note**: Must be used in conjunction with the `count`
      parameter. Email address of the developer from which to start displaying
      the list of developers. For example, if the an unfiltered list returns:
      ``` <EMAIL> <EMAIL> <EMAIL> ``` and
      your `startKey` is `<EMAIL>`, the list returned will be ```
      <EMAIL> <EMAIL> ```
  """

  app = _messages.StringField(1)
  count = _messages.IntegerField(2)
  expand = _messages.BooleanField(3)
  filter = _messages.StringField(4)
  ids = _messages.StringField(5)
  includeCompany = _messages.BooleanField(6)
  pageSize = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(8)
  parent = _messages.StringField(9, required=True)
  startKey = _messages.StringField(10)


class ApigeeOrganizationsDevelopersSetDeveloperStatusRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersSetDeveloperStatusRequest object.

  Fields:
    action: Status of the developer. Valid values are `active` and `inactive`.
    name: Required. Name of the developer. Use the following structure in your
      request: `organizations/{org}/developers/{developer_id}`
  """

  action = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsDevelopersSubscriptionsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersSubscriptionsCreateRequest object.

  Fields:
    googleCloudApigeeV1DeveloperSubscription: A
      GoogleCloudApigeeV1DeveloperSubscription resource to be passed as the
      request body.
    parent: Required. Email address of the developer that is purchasing a
      subscription to the API product. Use the following structure in your
      request: `organizations/{org}/developers/{developer_email}`
  """

  googleCloudApigeeV1DeveloperSubscription = _messages.MessageField('GoogleCloudApigeeV1DeveloperSubscription', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsDevelopersSubscriptionsExpireRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersSubscriptionsExpireRequest object.

  Fields:
    googleCloudApigeeV1ExpireDeveloperSubscriptionRequest: A
      GoogleCloudApigeeV1ExpireDeveloperSubscriptionRequest resource to be
      passed as the request body.
    name: Required. Name of the API product subscription. Use the following
      structure in your request: `organizations/{org}/developers/{developer_em
      ail}/subscriptions/{subscription}`
  """

  googleCloudApigeeV1ExpireDeveloperSubscriptionRequest = _messages.MessageField('GoogleCloudApigeeV1ExpireDeveloperSubscriptionRequest', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsDevelopersSubscriptionsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersSubscriptionsGetRequest object.

  Fields:
    name: Required. Name of the API product subscription. Use the following
      structure in your request: `organizations/{org}/developers/{developer_em
      ail}/subscriptions/{subscription}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsDevelopersSubscriptionsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersSubscriptionsListRequest object.

  Fields:
    count: Number of API product subscriptions to return in the API call. Use
      with `startKey` to provide more targeted filtering. Defaults to 100. The
      maximum limit is 1000.
    parent: Required. Email address of the developer. Use the following
      structure in your request:
      `organizations/{org}/developers/{developer_email}`
    startKey: Name of the API product subscription from which to start
      displaying the list of subscriptions. If omitted, the list starts from
      the first item. For example, to view the API product subscriptions from
      51-150, set the value of `startKey` to the name of the 51st subscription
      and set the value of `count` to 100.
  """

  count = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  parent = _messages.StringField(2, required=True)
  startKey = _messages.StringField(3)


class ApigeeOrganizationsDevelopersUpdateMonetizationConfigRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersUpdateMonetizationConfigRequest object.

  Fields:
    googleCloudApigeeV1DeveloperMonetizationConfig: A
      GoogleCloudApigeeV1DeveloperMonetizationConfig resource to be passed as
      the request body.
    name: Required. Monetization configuration for the developer. Use the
      following structure in your request:
      `organizations/{org}/developers/{developer}/monetizationConfig`
  """

  googleCloudApigeeV1DeveloperMonetizationConfig = _messages.MessageField('GoogleCloudApigeeV1DeveloperMonetizationConfig', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsDevelopersUpdateRequest(_messages.Message):
  r"""A ApigeeOrganizationsDevelopersUpdateRequest object.

  Fields:
    googleCloudApigeeV1Developer: A GoogleCloudApigeeV1Developer resource to
      be passed as the request body.
    name: Required. Email address of the developer. Use the following
      structure in your request:
      `organizations/{org}/developers/{developer_email}`
  """

  googleCloudApigeeV1Developer = _messages.MessageField('GoogleCloudApigeeV1Developer', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsEndpointAttachmentsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEndpointAttachmentsCreateRequest object.

  Fields:
    endpointAttachmentId: ID to use for the endpoint attachment. ID must start
      with a lowercase letter followed by up to 31 lowercase letters, numbers,
      or hyphens, and cannot end with a hyphen. The minimum length is 2.
    googleCloudApigeeV1EndpointAttachment: A
      GoogleCloudApigeeV1EndpointAttachment resource to be passed as the
      request body.
    parent: Required. Organization the endpoint attachment will be created in.
  """

  endpointAttachmentId = _messages.StringField(1)
  googleCloudApigeeV1EndpointAttachment = _messages.MessageField('GoogleCloudApigeeV1EndpointAttachment', 2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsEndpointAttachmentsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsEndpointAttachmentsDeleteRequest object.

  Fields:
    name: Required. Name of the endpoint attachment. Use the following
      structure in your request:
      `organizations/{org}/endpointAttachments/{endpoint_attachment}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEndpointAttachmentsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEndpointAttachmentsGetRequest object.

  Fields:
    name: Required. Name of the endpoint attachment. Use the following
      structure in your request:
      `organizations/{org}/endpointAttachments/{endpoint_attachment}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEndpointAttachmentsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsEndpointAttachmentsListRequest object.

  Fields:
    pageSize: Optional. Maximum number of endpoint attachments to return. If
      unspecified, at most 25 attachments will be returned.
    pageToken: Optional. Page token, returned from a previous
      `ListEndpointAttachments` call, that you can use to retrieve the next
      page.
    parent: Required. Name of the organization for which to list endpoint
      attachments. Use the following structure in your request:
      `organizations/{org}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsEnvgroupsAttachmentsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvgroupsAttachmentsCreateRequest object.

  Fields:
    googleCloudApigeeV1EnvironmentGroupAttachment: A
      GoogleCloudApigeeV1EnvironmentGroupAttachment resource to be passed as
      the request body.
    parent: Required. EnvironmentGroup under which to create the attachment in
      the following format: `organizations/{org}/envgroups/{envgroup}`.
  """

  googleCloudApigeeV1EnvironmentGroupAttachment = _messages.MessageField('GoogleCloudApigeeV1EnvironmentGroupAttachment', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvgroupsAttachmentsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvgroupsAttachmentsDeleteRequest object.

  Fields:
    name: Required. Name of the environment group attachment to delete in the
      following format:
      `organizations/{org}/envgroups/{envgroup}/attachments/{attachment}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvgroupsAttachmentsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvgroupsAttachmentsGetRequest object.

  Fields:
    name: Required. Name of the environment group attachment in the following
      format:
      `organizations/{org}/envgroups/{envgroup}/attachments/{attachment}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvgroupsAttachmentsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvgroupsAttachmentsListRequest object.

  Fields:
    pageSize: Maximum number of environment group attachments to return. The
      page size defaults to 25.
    pageToken: Page token, returned by a previous
      ListEnvironmentGroupAttachments call, that you can use to retrieve the
      next page.
    parent: Required. Name of the environment group in the following format:
      `organizations/{org}/envgroups/{envgroup}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsEnvgroupsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvgroupsCreateRequest object.

  Fields:
    googleCloudApigeeV1EnvironmentGroup: A GoogleCloudApigeeV1EnvironmentGroup
      resource to be passed as the request body.
    name: ID of the environment group. Overrides any ID in the
      environment_group resource.
    parent: Required. Name of the organization in which to create the
      environment group in the following format: `organizations/{org}`.
  """

  googleCloudApigeeV1EnvironmentGroup = _messages.MessageField('GoogleCloudApigeeV1EnvironmentGroup', 1)
  name = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsEnvgroupsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvgroupsDeleteRequest object.

  Fields:
    name: Required. Name of the environment group in the following format:
      `organizations/{org}/envgroups/{envgroup}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvgroupsGetDeployedIngressConfigRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvgroupsGetDeployedIngressConfigRequest object.

  Enums:
    ViewValueValuesEnum: When set to FULL, additional details about the
      specific deployments receiving traffic will be included in the
      IngressConfig response's RoutingRules.

  Fields:
    name: Required. Name of the deployed configuration for the environment
      group in the following format:
      'organizations/{org}/envgroups/{envgroup}/deployedIngressConfig'.
    view: When set to FULL, additional details about the specific deployments
      receiving traffic will be included in the IngressConfig response's
      RoutingRules.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""When set to FULL, additional details about the specific deployments
    receiving traffic will be included in the IngressConfig response's
    RoutingRules.

    Values:
      INGRESS_CONFIG_VIEW_UNSPECIFIED: The default/unset value. The API will
        default to the BASIC view.
      BASIC: Include all ingress config data necessary for the runtime to
        configure ingress, but no more. Routing rules will include only
        basepath and destination environment. This the default value.
      FULL: Include all ingress config data, including internal debug info for
        each routing rule such as the proxy claiming a particular basepath and
        when the routing rule first appeared in the env group.
    """
    INGRESS_CONFIG_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class ApigeeOrganizationsEnvgroupsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvgroupsGetRequest object.

  Fields:
    name: Required. Name of the environment group in the following format:
      `organizations/{org}/envgroups/{envgroup}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvgroupsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvgroupsListRequest object.

  Fields:
    pageSize: Maximum number of environment groups to return. The page size
      defaults to 25.
    pageToken: Page token, returned from a previous ListEnvironmentGroups
      call, that you can use to retrieve the next page.
    parent: Required. Name of the organization for which to list environment
      groups in the following format: `organizations/{org}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsEnvgroupsPatchRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvgroupsPatchRequest object.

  Fields:
    googleCloudApigeeV1EnvironmentGroup: A GoogleCloudApigeeV1EnvironmentGroup
      resource to be passed as the request body.
    name: Required. Name of the environment group to update in the format:
      `organizations/{org}/envgroups/{envgroup}.
    updateMask: List of fields to be updated.
  """

  googleCloudApigeeV1EnvironmentGroup = _messages.MessageField('GoogleCloudApigeeV1EnvironmentGroup', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApigeeOrganizationsEnvironmentsAnalyticsAdminGetSchemav2Request(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsAnalyticsAdminGetSchemav2Request
  object.

  Fields:
    disableCache: Flag that specifies whether the schema is be read from the
      database or cache. Set to `true` to read the schema from the database.
      Defaults to cache.
    name: Required. Path to the schema. Use the following structure in your
      request:
      `organizations/{org}/environments/{env}/analytics/admin/schemav2`.
    type: Required. Name of the dataset for which you want to retrieve the
      schema. For example: `fact` or `agg_cus1`
  """

  disableCache = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  type = _messages.StringField(3)


class ApigeeOrganizationsEnvironmentsAnalyticsExportsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsAnalyticsExportsCreateRequest object.

  Fields:
    googleCloudApigeeV1ExportRequest: A GoogleCloudApigeeV1ExportRequest
      resource to be passed as the request body.
    parent: Required. Names of the parent organization and environment. Must
      be of the form `organizations/{org}/environments/{env}`.
  """

  googleCloudApigeeV1ExportRequest = _messages.MessageField('GoogleCloudApigeeV1ExportRequest', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsAnalyticsExportsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsAnalyticsExportsGetRequest object.

  Fields:
    name: Required. Resource name of the export to get.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsAnalyticsExportsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsAnalyticsExportsListRequest object.

  Fields:
    parent: Required. Names of the parent organization and environment. Must
      be of the form `organizations/{org}/environments/{env}`.
  """

  parent = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsApisDeploymentsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsApisDeploymentsListRequest object.

  Fields:
    parent: Required. Name representing an API proxy in an environment in the
      following format: `organizations/{org}/environments/{env}/apis/{api}`
  """

  parent = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsCreateRequest
  object.

  Fields:
    googleCloudApigeeV1DebugSession: A GoogleCloudApigeeV1DebugSession
      resource to be passed as the request body.
    parent: Required. The resource name of the API Proxy revision deployment
      for which to create the DebugSession. Must be of the form `organizations
      /{organization}/environments/{environment}/apis/{api}/revisions/{revisio
      n}`.
    timeout: Optional. The time in seconds after which this DebugSession
      should end. A timeout specified in DebugSession will overwrite this
      value.
  """

  googleCloudApigeeV1DebugSession = _messages.MessageField('GoogleCloudApigeeV1DebugSession', 1)
  parent = _messages.StringField(2, required=True)
  timeout = _messages.IntegerField(3)


class ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsDataGetRequest(_messages.Message):
  r"""A
  ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsDataGetRequest
  object.

  Fields:
    name: Required. The name of the debug session transaction. Must be of the
      form: `organizations/{organization}/environments/{environment}/apis/{api
      }/revisions/{revision}/debugsessions/{session}/data/{transaction}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsDeleteDataRequest(_messages.Message):
  r"""A
  ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsDeleteDataRequest
  object.

  Fields:
    name: Required. The name of the debug session to delete. Must be of the
      form: `organizations/{organization}/environments/{environment}/apis/{api
      }/revisions/{revision}/debugsessions/{debugsession}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsGetRequest
  object.

  Fields:
    name: Required. The name of the debug session to retrieve. Must be of the
      form: `organizations/{organization}/environments/{environment}/apis/{api
      }/revisions/{revision}/debugsessions/{session}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsApisRevisionsDebugsessionsListRequest
  object.

  Fields:
    pageSize: Maximum number of debug sessions to return. The page size
      defaults to 25.
    pageToken: Page token, returned from a previous ListDebugSessions call,
      that you can use to retrieve the next page.
    parent: Required. The name of the API Proxy revision deployment for which
      to list debug sessions. Must be of the form: `organizations/{organizatio
      n}/environments/{environment}/apis/{api}/revisions/{revision}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsEnvironmentsApisRevisionsDeployRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsApisRevisionsDeployRequest object.

  Fields:
    name: Required. Name of the API proxy revision deployment in the following
      format:
      `organizations/{org}/environments/{env}/apis/{api}/revisions/{rev}`
    override: Flag that specifies whether the new deployment replaces other
      deployed revisions of the API proxy in the environment. Set `override`
      to `true` to replace other deployed revisions. By default, `override` is
      `false` and the deployment is rejected if other revisions of the API
      proxy are deployed in the environment.
    sequencedRollout: Flag that specifies whether to enable sequenced rollout.
      If set to `true`, the routing rules for this deployment and the
      environment changes to add the deployment will be rolled out in a safe
      order. This reduces the risk of downtime that could be caused by
      changing the environment group's routing before the new destination for
      the affected traffic is ready to receive it. This should only be
      necessary if the new deployment will be capturing traffic from another
      environment under a shared environment group or if traffic will be
      rerouted to a different environment due to a base path removal. The
      [generateDeployChangeReport API](generateDeployChangeReport) may be used
      to examine routing changes before issuing the deployment request, and
      its response will indicate if a sequenced rollout is recommended for the
      deployment.
    serviceAccount: Google Cloud IAM service account. The service account
      represents the identity of the deployed proxy, and determines what
      permissions it has. The format must be
      `{ACCOUNT_ID}@{PROJECT}.iam.gserviceaccount.com`.
  """

  name = _messages.StringField(1, required=True)
  override = _messages.BooleanField(2)
  sequencedRollout = _messages.BooleanField(3)
  serviceAccount = _messages.StringField(4)


class ApigeeOrganizationsEnvironmentsApisRevisionsDeploymentsGenerateDeployChangeReportRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsApisRevisionsDeploymentsGenerateDeployC
  hangeReportRequest object.

  Fields:
    name: Name of the API proxy revision deployment in the following format:
      `organizations/{org}/environments/{env}/apis/{api}/revisions/{rev}`
    override: Flag that specifies whether to force the deployment of the new
      revision over the currently deployed revision by overriding conflict
      checks.
  """

  name = _messages.StringField(1, required=True)
  override = _messages.BooleanField(2)


class ApigeeOrganizationsEnvironmentsApisRevisionsDeploymentsGenerateUndeployChangeReportRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsApisRevisionsDeploymentsGenerateUndeplo
  yChangeReportRequest object.

  Fields:
    name: Name of the API proxy revision deployment in the following format:
      `organizations/{org}/environments/{env}/apis/{api}/revisions/{rev}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsApisRevisionsGetDeploymentsRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsApisRevisionsGetDeploymentsRequest
  object.

  Fields:
    name: Required. Name representing an API proxy revision in an environment
      in the following format:
      `organizations/{org}/environments/{env}/apis/{api}/revisions/{rev}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsApisRevisionsUndeployRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsApisRevisionsUndeployRequest object.

  Fields:
    name: Required. Name of the API proxy revision deployment in the following
      format:
      `organizations/{org}/environments/{env}/apis/{api}/revisions/{rev}`
    sequencedRollout: Flag that specifies whether to enable sequenced rollout.
      If set to `true`, the environment group routing rules corresponding to
      this deployment will be removed before removing the deployment from the
      runtime. This is likely to be a rare use case; it is only needed when
      the intended effect of undeploying this proxy is to cause the traffic it
      currently handles to be rerouted to some other existing proxy in the
      environment group. The [GenerateUndeployChangeReport
      API](GenerateUndeployChangeReport) may be used to examine routing
      changes before issuing the undeployment request, and its response will
      indicate if a sequenced rollout is recommended for the undeployment.
  """

  name = _messages.StringField(1, required=True)
  sequencedRollout = _messages.BooleanField(2)


class ApigeeOrganizationsEnvironmentsArchiveDeploymentsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsArchiveDeploymentsCreateRequest object.

  Fields:
    googleCloudApigeeV1ArchiveDeployment: A
      GoogleCloudApigeeV1ArchiveDeployment resource to be passed as the
      request body.
    parent: Required. The Environment this Archive Deployment will be created
      in.
  """

  googleCloudApigeeV1ArchiveDeployment = _messages.MessageField('GoogleCloudApigeeV1ArchiveDeployment', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsArchiveDeploymentsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsArchiveDeploymentsDeleteRequest object.

  Fields:
    name: Required. Name of the Archive Deployment in the following format:
      `organizations/{org}/environments/{env}/archiveDeployments/{id}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsArchiveDeploymentsGenerateDownloadUrlRequest(_messages.Message):
  r"""A
  ApigeeOrganizationsEnvironmentsArchiveDeploymentsGenerateDownloadUrlRequest
  object.

  Fields:
    googleCloudApigeeV1GenerateDownloadUrlRequest: A
      GoogleCloudApigeeV1GenerateDownloadUrlRequest resource to be passed as
      the request body.
    name: Required. The name of the Archive Deployment you want to download.
  """

  googleCloudApigeeV1GenerateDownloadUrlRequest = _messages.MessageField('GoogleCloudApigeeV1GenerateDownloadUrlRequest', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsArchiveDeploymentsGenerateUploadUrlRequest(_messages.Message):
  r"""A
  ApigeeOrganizationsEnvironmentsArchiveDeploymentsGenerateUploadUrlRequest
  object.

  Fields:
    googleCloudApigeeV1GenerateUploadUrlRequest: A
      GoogleCloudApigeeV1GenerateUploadUrlRequest resource to be passed as the
      request body.
    parent: Required. The organization and environment to upload to.
  """

  googleCloudApigeeV1GenerateUploadUrlRequest = _messages.MessageField('GoogleCloudApigeeV1GenerateUploadUrlRequest', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsArchiveDeploymentsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsArchiveDeploymentsGetRequest object.

  Fields:
    name: Required. Name of the Archive Deployment in the following format:
      `organizations/{org}/environments/{env}/archiveDeployments/{id}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsArchiveDeploymentsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsArchiveDeploymentsListRequest object.

  Fields:
    filter: Optional. An optional query used to return a subset of Archive
      Deployments using the semantics defined in https://google.aip.dev/160.
    pageSize: Optional. Maximum number of Archive Deployments to return. If
      unspecified, at most 25 deployments will be returned.
    pageToken: Optional. Page token, returned from a previous
      ListArchiveDeployments call, that you can use to retrieve the next page.
    parent: Required. Name of the Environment for which to list Archive
      Deployments in the format: `organizations/{org}/environments/{env}`.
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ApigeeOrganizationsEnvironmentsArchiveDeploymentsPatchRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsArchiveDeploymentsPatchRequest object.

  Fields:
    googleCloudApigeeV1ArchiveDeployment: A
      GoogleCloudApigeeV1ArchiveDeployment resource to be passed as the
      request body.
    name: Name of the Archive Deployment in the following format:
      `organizations/{org}/environments/{env}/archiveDeployments/{id}`.
    updateMask: Required. The list of fields to be updated.
  """

  googleCloudApigeeV1ArchiveDeployment = _messages.MessageField('GoogleCloudApigeeV1ArchiveDeployment', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApigeeOrganizationsEnvironmentsCachesDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsCachesDeleteRequest object.

  Fields:
    name: Required. Cache resource name of the form: `organizations/{organizat
      ion_id}/environments/{environment_id}/caches/{cache_id}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsCreateRequest object.

  Fields:
    googleCloudApigeeV1Environment: A GoogleCloudApigeeV1Environment resource
      to be passed as the request body.
    name: Optional. Name of the environment.
    parent: Required. Name of the organization in which the environment will
      be created. Use the following structure in your request:
      `organizations/{org}`
  """

  googleCloudApigeeV1Environment = _messages.MessageField('GoogleCloudApigeeV1Environment', 1)
  name = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsEnvironmentsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsDeleteRequest object.

  Fields:
    name: Required. Name of the environment. Use the following structure in
      your request: `organizations/{org}/environments/{env}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsDeploymentsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsDeploymentsListRequest object.

  Fields:
    parent: Required. Name of the environment for which to return deployment
      information in the following format:
      `organizations/{org}/environments/{env}`
    sharedFlows: Optional. Flag that specifies whether to return shared flow
      or API proxy deployments. Set to `true` to return shared flow
      deployments; set to `false` to return API proxy deployments. Defaults to
      `false`.
  """

  parent = _messages.StringField(1, required=True)
  sharedFlows = _messages.BooleanField(2)


class ApigeeOrganizationsEnvironmentsFlowhooksAttachSharedFlowToFlowHookRequest(_messages.Message):
  r"""A
  ApigeeOrganizationsEnvironmentsFlowhooksAttachSharedFlowToFlowHookRequest
  object.

  Fields:
    googleCloudApigeeV1FlowHook: A GoogleCloudApigeeV1FlowHook resource to be
      passed as the request body.
    name: Required. Name of the flow hook to which the shared flow should be
      attached in the following format:
      `organizations/{org}/environments/{env}/flowhooks/{flowhook}`
  """

  googleCloudApigeeV1FlowHook = _messages.MessageField('GoogleCloudApigeeV1FlowHook', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsFlowhooksDetachSharedFlowFromFlowHookRequest(_messages.Message):
  r"""A
  ApigeeOrganizationsEnvironmentsFlowhooksDetachSharedFlowFromFlowHookRequest
  object.

  Fields:
    name: Required. Name of the flow hook to detach in the following format:
      `organizations/{org}/environments/{env}/flowhooks/{flowhook}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsFlowhooksGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsFlowhooksGetRequest object.

  Fields:
    name: Required. Name of the flow hook in the following format:
      `organizations/{org}/environments/{env}/flowhooks/{flowhook}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsGetApiSecurityRuntimeConfigRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsGetApiSecurityRuntimeConfigRequest
  object.

  Fields:
    name: Required. Name of the environment API Security Runtime configuration
      resource. Use the following structure in your request:
      `organizations/{org}/environments/{env}/apiSecurityRuntimeConfig`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsGetDebugmaskRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsGetDebugmaskRequest object.

  Fields:
    name: Required. Name of the debug mask. Use the following structure in
      your request: `organizations/{org}/environments/{env}/debugmask`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsGetDeployedConfigRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsGetDeployedConfigRequest object.

  Fields:
    name: Required. Name of the environment deployed configuration resource.
      Use the following structure in your request:
      `organizations/{org}/environments/{env}/deployedConfig`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsGetIamPolicyRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsGetRequest object.

  Fields:
    name: Required. Name of the environment. Use the following structure in
      your request: `organizations/{org}/environments/{env}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsGetSecurityActionsConfigRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsGetSecurityActionsConfigRequest object.

  Fields:
    name: Required. The name of the SecurityActionsConfig to retrieve. This
      will always be:
      `organizations/{org}/environments/{env}/security_actions_config`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsGetTraceConfigRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsGetTraceConfigRequest object.

  Fields:
    name: Required. Name of the trace configuration. Use the following
      structure in your request: "organizations/*/environments/*/traceConfig".
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsKeystoresAliasesCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsKeystoresAliasesCreateRequest object.

  Fields:
    _password: DEPRECATED: For improved security, specify the password in the
      request body instead of using the query parameter. To specify the
      password in the request body, set `Content-type: multipart/form-data`
      part with name `password`. Password for the private key file, if
      required.
    alias: Alias for the key/certificate pair. Values must match the regular
      expression `[\w\s-.]{1,255}`. This must be provided for all formats
      except `selfsignedcert`; self-signed certs may specify the alias in
      either this parameter or the JSON body.
    format: Required. Format of the data. Valid values include:
      `selfsignedcert`, `keycertfile`, or `pkcs12`
    googleApiHttpBody: A GoogleApiHttpBody resource to be passed as the
      request body.
    ignoreExpiryValidation: Flag that specifies whether to ignore expiry
      validation. If set to `true`, no expiry validation will be performed.
    ignoreNewlineValidation: Flag that specifies whether to ignore newline
      validation. If set to `true`, no error is thrown when the file contains
      a certificate chain with no newline between each certificate. Defaults
      to `false`.
    parent: Required. Name of the keystore. Use the following format in your
      request: `organizations/{org}/environments/{env}/keystores/{keystore}`.
  """

  _password = _messages.StringField(1)
  alias = _messages.StringField(2)
  format = _messages.StringField(3)
  googleApiHttpBody = _messages.MessageField('GoogleApiHttpBody', 4)
  ignoreExpiryValidation = _messages.BooleanField(5)
  ignoreNewlineValidation = _messages.BooleanField(6)
  parent = _messages.StringField(7, required=True)


class ApigeeOrganizationsEnvironmentsKeystoresAliasesCsrRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsKeystoresAliasesCsrRequest object.

  Fields:
    name: Required. Name of the alias. Use the following format in your
      request: `organizations/{org}/environments/{env}/keystores/{keystore}/al
      iases/{alias}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsKeystoresAliasesDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsKeystoresAliasesDeleteRequest object.

  Fields:
    name: Required. Name of the alias. Use the following format in your
      request: `organizations/{org}/environments/{env}/keystores/{keystore}/al
      iases/{alias}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsKeystoresAliasesGetCertificateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsKeystoresAliasesGetCertificateRequest
  object.

  Fields:
    name: Required. Name of the alias. Use the following format in your
      request: `organizations/{org}/environments/{env}/keystores/{keystore}/al
      iases/{alias}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsKeystoresAliasesGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsKeystoresAliasesGetRequest object.

  Fields:
    name: Required. Name of the alias. Use the following format in your
      request: `organizations/{org}/environments/{env}/keystores/{keystore}/al
      iases/{alias}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsKeystoresAliasesUpdateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsKeystoresAliasesUpdateRequest object.

  Fields:
    googleApiHttpBody: A GoogleApiHttpBody resource to be passed as the
      request body.
    ignoreExpiryValidation: Required. Flag that specifies whether to ignore
      expiry validation. If set to `true`, no expiry validation will be
      performed.
    ignoreNewlineValidation: Flag that specifies whether to ignore newline
      validation. If set to `true`, no error is thrown when the file contains
      a certificate chain with no newline between each certificate. Defaults
      to `false`.
    name: Required. Name of the alias. Use the following format in your
      request: `organizations/{org}/environments/{env}/keystores/{keystore}/al
      iases/{alias}`
  """

  googleApiHttpBody = _messages.MessageField('GoogleApiHttpBody', 1)
  ignoreExpiryValidation = _messages.BooleanField(2)
  ignoreNewlineValidation = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)


class ApigeeOrganizationsEnvironmentsKeystoresCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsKeystoresCreateRequest object.

  Fields:
    googleCloudApigeeV1Keystore: A GoogleCloudApigeeV1Keystore resource to be
      passed as the request body.
    name: Optional. Name of the keystore. Overrides the value in Keystore.
    parent: Required. Name of the environment in which to create the keystore.
      Use the following format in your request:
      `organizations/{org}/environments/{env}`
  """

  googleCloudApigeeV1Keystore = _messages.MessageField('GoogleCloudApigeeV1Keystore', 1)
  name = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsEnvironmentsKeystoresDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsKeystoresDeleteRequest object.

  Fields:
    name: Required. Name of the keystore. Use the following format in your
      request: `organizations/{org}/environments/{env}/keystores/{keystore}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsKeystoresGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsKeystoresGetRequest object.

  Fields:
    name: Required. Name of the keystore. Use the following format in your
      request: `organizations/{org}/environments/{env}/keystores/{keystore}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsKeyvaluemapsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsKeyvaluemapsCreateRequest object.

  Fields:
    googleCloudApigeeV1KeyValueMap: A GoogleCloudApigeeV1KeyValueMap resource
      to be passed as the request body.
    parent: Required. Name of the environment in which to create the key value
      map. Use the following structure in your request:
      `organizations/{org}/environments/{env}`
  """

  googleCloudApigeeV1KeyValueMap = _messages.MessageField('GoogleCloudApigeeV1KeyValueMap', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsKeyvaluemapsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsKeyvaluemapsDeleteRequest object.

  Fields:
    name: Required. Name of the key value map. Use the following structure in
      your request:
      `organizations/{org}/environments/{env}/keyvaluemaps/{keyvaluemap}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsKeyvaluemapsEntriesCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsKeyvaluemapsEntriesCreateRequest
  object.

  Fields:
    googleCloudApigeeV1KeyValueEntry: A GoogleCloudApigeeV1KeyValueEntry
      resource to be passed as the request body.
    parent: Required. Scope as indicated by the URI in which to create the key
      value map entry. Use **one** of the following structures in your
      request: *
      `organizations/{organization}/apis/{api}/keyvaluemaps/{keyvaluemap}`. *
      `organizations/{organization}/environments/{environment}/keyvaluemaps/{k
      eyvaluemap}` *
      `organizations/{organization}/keyvaluemaps/{keyvaluemap}`.
  """

  googleCloudApigeeV1KeyValueEntry = _messages.MessageField('GoogleCloudApigeeV1KeyValueEntry', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsKeyvaluemapsEntriesDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsKeyvaluemapsEntriesDeleteRequest
  object.

  Fields:
    name: Required. Scope as indicated by the URI in which to delete the key
      value map entry. Use **one** of the following structures in your
      request: * `organizations/{organization}/apis/{api}/keyvaluemaps/{keyval
      uemap}/entries/{entry}`. * `organizations/{organization}/environments/{e
      nvironment}/keyvaluemaps/{keyvaluemap}/entries/{entry}` * `organizations
      /{organization}/keyvaluemaps/{keyvaluemap}/entries/{entry}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsKeyvaluemapsEntriesGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsKeyvaluemapsEntriesGetRequest object.

  Fields:
    name: Required. Scope as indicated by the URI in which to fetch the key
      value map entry/value. Use **one** of the following structures in your
      request: * `organizations/{organization}/apis/{api}/keyvaluemaps/{keyval
      uemap}/entries/{entry}`. * `organizations/{organization}/environments/{e
      nvironment}/keyvaluemaps/{keyvaluemap}/entries/{entry}` * `organizations
      /{organization}/keyvaluemaps/{keyvaluemap}/entries/{entry}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsKeyvaluemapsEntriesListRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsKeyvaluemapsEntriesListRequest object.

  Fields:
    pageSize: Optional. Maximum number of key value entries to return. If
      unspecified, at most 100 entries will be returned.
    pageToken: Optional. Page token. If provides, must be a valid key value
      entry returned from a previous call that can be used to retrieve the
      next page.
    parent: Required. Scope as indicated by the URI in which to list key value
      maps. Use **one** of the following structures in your request: *
      `organizations/{organization}/apis/{api}/keyvaluemaps/{keyvaluemap}`. *
      `organizations/{organization}/environments/{environment}/keyvaluemaps/{k
      eyvaluemap}` *
      `organizations/{organization}/keyvaluemaps/{keyvaluemap}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsEnvironmentsModifyEnvironmentRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsModifyEnvironmentRequest object.

  Fields:
    googleCloudApigeeV1Environment: A GoogleCloudApigeeV1Environment resource
      to be passed as the request body.
    name: Required. Name of the environment. Use the following structure in
      your request: `organizations/{org}/environments/{environment}`.
    updateMask: List of fields to be updated. Fields that can be updated:
      node_config.
  """

  googleCloudApigeeV1Environment = _messages.MessageField('GoogleCloudApigeeV1Environment', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApigeeOrganizationsEnvironmentsOptimizedStatsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsOptimizedStatsGetRequest object.

  Fields:
    accuracy: No longer used by Apigee. Supported for backwards compatibility.
    aggTable: Table name used to query custom aggregate tables. If this
      parameter is skipped, then Apigee will try to retrieve the data from
      fact tables which will be expensive.
    filter: Filter that enables you to drill-down on specific dimension
      values.
    limit: Maximum number of result items to return. The default and maximum
      value that can be returned is 14400.
    name: Required. Resource name for which the interactive query will be
      executed. Use the following format in your request:
      `organizations/{org}/environments/{env}/optimizedStats/{dimensions}`
      Dimensions let you view metrics in meaningful groupings, such as
      `apiproxy`, `target_host`. The value of `dimensions` should be a comma-
      separated list as shown below: `organizations/{org}/environments/{env}/o
      ptimizedStats/apiproxy,request_verb`
    offset: Offset value. Use `offset` with `limit` to enable pagination of
      results. For example, to display results 11-20, set limit to `10` and
      offset to `10`.
    realtime: No longer used by Apigee. Supported for backwards compatibility.
    select: Required. Comma-separated list of metrics. For example:
      `sum(message_count),sum(error_count)`
    sonar: Routes the query to API Monitoring for the last hour.
    sort: Flag that specifies whether the sort order should be ascending or
      descending. Valid values include `DESC` and `ASC`.
    sortby: Comma-separated list of columns to sort the final result.
    timeRange: Required. Time interval for the interactive query. Time range
      is specified in GMT as `start~end`. For example: `04/15/2017
      00:00~05/15/2017 23:59`
    timeUnit: Granularity of metrics returned. Valid values include: `second`,
      `minute`, `hour`, `day`, `week`, or `month`.
    topk: Top number of results to return. For example, to return the top 5
      results, set `topk=5`.
    tsAscending: Flag that specifies whether to list timestamps in ascending
      (`true`) or descending (`false`) order. Apigee recommends setting this
      value to `true` if you are using `sortby` with `sort=DESC`.
    tzo: Timezone offset value.
  """

  accuracy = _messages.StringField(1)
  aggTable = _messages.StringField(2)
  filter = _messages.StringField(3)
  limit = _messages.StringField(4)
  name = _messages.StringField(5, required=True)
  offset = _messages.StringField(6)
  realtime = _messages.BooleanField(7)
  select = _messages.StringField(8)
  sonar = _messages.BooleanField(9)
  sort = _messages.StringField(10)
  sortby = _messages.StringField(11)
  timeRange = _messages.StringField(12)
  timeUnit = _messages.StringField(13)
  topk = _messages.StringField(14)
  tsAscending = _messages.BooleanField(15)
  tzo = _messages.StringField(16)


class ApigeeOrganizationsEnvironmentsQueriesCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsQueriesCreateRequest object.

  Fields:
    googleCloudApigeeV1Query: A GoogleCloudApigeeV1Query resource to be passed
      as the request body.
    parent: Required. The parent resource name. Must be of the form
      `organizations/{org}/environments/{env}`.
  """

  googleCloudApigeeV1Query = _messages.MessageField('GoogleCloudApigeeV1Query', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsQueriesGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsQueriesGetRequest object.

  Fields:
    name: Required. Name of the asynchronous query to get. Must be of the form
      `organizations/{org}/environments/{env}/queries/{queryId}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsQueriesGetResultRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsQueriesGetResultRequest object.

  Fields:
    name: Required. Name of the asynchronous query result to get. Must be of
      the form
      `organizations/{org}/environments/{env}/queries/{queryId}/result`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsQueriesGetResulturlRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsQueriesGetResulturlRequest object.

  Fields:
    name: Required. Name of the asynchronous query result to get. Must be of
      the form
      `organizations/{org}/environments/{env}/queries/{queryId}/resulturl`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsQueriesListRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsQueriesListRequest object.

  Fields:
    dataset: Filter response list by dataset. Example: `api`, `mint`
    from_: Filter response list by returning asynchronous queries that created
      after this date time. Time must be in ISO date-time format like
      '2011-12-03T10:15:30Z'.
    inclQueriesWithoutReport: Flag to include asynchronous queries that don't
      have a report denifition.
    parent: Required. The parent resource name. Must be of the form
      `organizations/{org}/environments/{env}`.
    status: Filter response list by asynchronous query status.
    submittedBy: Filter response list by user who submitted queries.
    to: Filter response list by returning asynchronous queries that created
      before this date time. Time must be in ISO date-time format like
      '2011-12-03T10:16:30Z'.
  """

  dataset = _messages.StringField(1)
  from_ = _messages.StringField(2)
  inclQueriesWithoutReport = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)
  status = _messages.StringField(5)
  submittedBy = _messages.StringField(6)
  to = _messages.StringField(7)


class ApigeeOrganizationsEnvironmentsReferencesCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsReferencesCreateRequest object.

  Fields:
    googleCloudApigeeV1Reference: A GoogleCloudApigeeV1Reference resource to
      be passed as the request body.
    parent: Required. The parent environment name under which the Reference
      will be created. Must be of the form
      `organizations/{org}/environments/{env}`.
  """

  googleCloudApigeeV1Reference = _messages.MessageField('GoogleCloudApigeeV1Reference', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsReferencesDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsReferencesDeleteRequest object.

  Fields:
    name: Required. The name of the Reference to delete. Must be of the form
      `organizations/{org}/environments/{env}/references/{ref}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsReferencesGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsReferencesGetRequest object.

  Fields:
    name: Required. The name of the Reference to get. Must be of the form
      `organizations/{org}/environments/{env}/references/{ref}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsResourcefilesCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsResourcefilesCreateRequest object.

  Fields:
    googleApiHttpBody: A GoogleApiHttpBody resource to be passed as the
      request body.
    name: Required. Name of the resource file. Must match the regular
      expression: [a-zA-Z0-9:/\\!@#$%^&{}\[\]()+\-=,.~'` ]{1,255}
    parent: Required. Name of the environment in which to create the resource
      file in the following format: `organizations/{org}/environments/{env}`.
    type: Required. Resource file type. {{ resource_file_type }}
  """

  googleApiHttpBody = _messages.MessageField('GoogleApiHttpBody', 1)
  name = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  type = _messages.StringField(4)


class ApigeeOrganizationsEnvironmentsResourcefilesDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsResourcefilesDeleteRequest object.

  Fields:
    name: Required. ID of the resource file to delete. Must match the regular
      expression: [a-zA-Z0-9:/\\!@#$%^&{}\[\]()+\-=,.~'` ]{1,255}
    parent: Required. Name of the environment in the following format:
      `organizations/{org}/environments/{env}`.
    type: Required. Resource file type. {{ resource_file_type }}
  """

  name = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)
  type = _messages.StringField(3, required=True)


class ApigeeOrganizationsEnvironmentsResourcefilesGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsResourcefilesGetRequest object.

  Fields:
    name: Required. ID of the resource file. Must match the regular
      expression: [a-zA-Z0-9:/\\!@#$%^&{}\[\]()+\-=,.~'` ]{1,255}
    parent: Required. Name of the environment in the following format:
      `organizations/{org}/environments/{env}`.
    type: Required. Resource file type. {{ resource_file_type }}
  """

  name = _messages.StringField(1, required=True)
  parent = _messages.StringField(2, required=True)
  type = _messages.StringField(3, required=True)


class ApigeeOrganizationsEnvironmentsResourcefilesListEnvironmentResourcesRequest(_messages.Message):
  r"""A
  ApigeeOrganizationsEnvironmentsResourcefilesListEnvironmentResourcesRequest
  object.

  Fields:
    parent: Required. Name of the environment in which to list resource files
      in the following format: `organizations/{org}/environments/{env}`.
    type: Optional. Type of resource files to list. {{ resource_file_type }}
  """

  parent = _messages.StringField(1, required=True)
  type = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsResourcefilesListRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsResourcefilesListRequest object.

  Fields:
    parent: Required. Name of the environment in which to list resource files
      in the following format: `organizations/{org}/environments/{env}`.
    type: Optional. Type of resource files to list. {{ resource_file_type }}
  """

  parent = _messages.StringField(1, required=True)
  type = _messages.StringField(2)


class ApigeeOrganizationsEnvironmentsResourcefilesUpdateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsResourcefilesUpdateRequest object.

  Fields:
    googleApiHttpBody: A GoogleApiHttpBody resource to be passed as the
      request body.
    name: Required. ID of the resource file to update. Must match the regular
      expression: [a-zA-Z0-9:/\\!@#$%^&{}\[\]()+\-=,.~'` ]{1,255}
    parent: Required. Name of the environment in the following format:
      `organizations/{org}/environments/{env}`.
    type: Required. Resource file type. {{ resource_file_type }}
  """

  googleApiHttpBody = _messages.MessageField('GoogleApiHttpBody', 1)
  name = _messages.StringField(2, required=True)
  parent = _messages.StringField(3, required=True)
  type = _messages.StringField(4, required=True)


class ApigeeOrganizationsEnvironmentsSecurityActionsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSecurityActionsCreateRequest object.

  Fields:
    googleCloudApigeeV1SecurityAction: A GoogleCloudApigeeV1SecurityAction
      resource to be passed as the request body.
    parent: Required. The organization and environment that this
      SecurityAction applies to. Format:
      organizations/{org}/environments/{env}
    securityActionId: Required. The ID to use for the SecurityAction, which
      will become the final component of the action's resource name. This
      value should be 0-61 characters, and valid format is
      (^[a-z]([a-z0-9-]{\u200b0,61}[a-z0-9])?$).
  """

  googleCloudApigeeV1SecurityAction = _messages.MessageField('GoogleCloudApigeeV1SecurityAction', 1)
  parent = _messages.StringField(2, required=True)
  securityActionId = _messages.StringField(3)


class ApigeeOrganizationsEnvironmentsSecurityActionsDisableRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSecurityActionsDisableRequest object.

  Fields:
    googleCloudApigeeV1DisableSecurityActionRequest: A
      GoogleCloudApigeeV1DisableSecurityActionRequest resource to be passed as
      the request body.
    name: Required. The name of the SecurityAction to disable. Format:
      organizations/{org}/environments/{env}/securityActions/{security_action}
  """

  googleCloudApigeeV1DisableSecurityActionRequest = _messages.MessageField('GoogleCloudApigeeV1DisableSecurityActionRequest', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsSecurityActionsEnableRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSecurityActionsEnableRequest object.

  Fields:
    googleCloudApigeeV1EnableSecurityActionRequest: A
      GoogleCloudApigeeV1EnableSecurityActionRequest resource to be passed as
      the request body.
    name: Required. The name of the SecurityAction to enable. Format:
      organizations/{org}/environments/{env}/securityActions/{security_action}
  """

  googleCloudApigeeV1EnableSecurityActionRequest = _messages.MessageField('GoogleCloudApigeeV1EnableSecurityActionRequest', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsSecurityActionsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSecurityActionsGetRequest object.

  Fields:
    name: Required. The fully qualified name of the SecurityAction to
      retrieve. Format:
      organizations/{org}/environments/{env}/securityActions/{security_action}
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsSecurityActionsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSecurityActionsListRequest object.

  Fields:
    filter: The filter expression to filter List results.
      https://google.aip.dev/160. Allows for filtering over: state and
      api_proxies. E.g.: state = ACTIVE AND apiProxies:foo. Filtering by
      action is not supported https://github.com/aip-
      dev/google.aip.dev/issues/624
    pageSize: The maximum number of SecurityActions to return. If unspecified,
      at most 50 SecurityActions will be returned. The maximum value is 1000;
      values above 1000 will be coerced to 1000.
    pageToken: A page token, received from a previous `ListSecurityActions`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other parameters provided to `ListSecurityActions` must match the call
      that provided the page token.
    parent: Required. The parent, which owns this collection of
      SecurityActions. Format: organizations/{org}/environments/{env}
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ApigeeOrganizationsEnvironmentsSecurityIncidentsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSecurityIncidentsGetRequest object.

  Fields:
    name: Required. Security incident in the following format: `organizations/
      {org}/environments/{environment}/securityIncidents/{incident}'. Example:
      organizations/testOrg/environments/testEnv/securityIncidents/1234-4567-
      890-111
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsSecurityIncidentsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSecurityIncidentsListRequest object.

  Fields:
    filter: The filter expression to be used to get the list of security
      incidents, where filtering can be done on API Proxies. Example: filter =
      "api_proxy = /", "first_detected_time >", "last_detected_time <"
    pageSize: The maximum number of incidents to return. The service may
      return fewer than this value. If unspecified, at most 50 incidents will
      be returned.
    pageToken: A page token, received from a previous `ListSecurityIncident`
      call. Provide this to retrieve the subsequent page.
    parent: Required. For a specific organization, list of all the security
      incidents. Format: `organizations/{org}/environments/{environment}`
  """

  filter = _messages.StringField(1)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ApigeeOrganizationsEnvironmentsSecurityReportsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSecurityReportsCreateRequest object.

  Fields:
    googleCloudApigeeV1SecurityReportQuery: A
      GoogleCloudApigeeV1SecurityReportQuery resource to be passed as the
      request body.
    parent: Required. The parent resource name. Must be of the form
      `organizations/{org}/environments/{env}`.
  """

  googleCloudApigeeV1SecurityReportQuery = _messages.MessageField('GoogleCloudApigeeV1SecurityReportQuery', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsSecurityReportsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSecurityReportsGetRequest object.

  Fields:
    name: Required. Name of the security report to get. Must be of the form
      `organizations/{org}/environments/{env}/securityReports/{reportId}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsSecurityReportsGetResultRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSecurityReportsGetResultRequest object.

  Fields:
    name: Required. Name of the security report result to get. Must be of the
      form `organizations/{org}/environments/{env}/securityReports/{reportId}/
      result`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsSecurityReportsGetResultViewRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSecurityReportsGetResultViewRequest
  object.

  Fields:
    name: Required. Name of the security report result view to get. Must be of
      the form `organizations/{org}/environments/{env}/securityReports/{report
      Id}/resultView`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsSecurityReportsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSecurityReportsListRequest object.

  Fields:
    dataset: Filter response list by dataset. Example: `api`, `mint`
    from_: Filter response list by returning security reports that created
      after this date time. Time must be in ISO date-time format like
      '2011-12-03T10:15:30Z'.
    pageSize: The maximum number of security report to return in the list
      response.
    pageToken: Token returned from the previous list response to fetch the
      next page.
    parent: Required. The parent resource name. Must be of the form
      `organizations/{org}/environments/{env}`.
    status: Filter response list by security reports status.
    submittedBy: Filter response list by user who submitted queries.
    to: Filter response list by returning security reports that created before
      this date time. Time must be in ISO date-time format like
      '2011-12-03T10:16:30Z'.
  """

  dataset = _messages.StringField(1)
  from_ = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  status = _messages.StringField(6)
  submittedBy = _messages.StringField(7)
  to = _messages.StringField(8)


class ApigeeOrganizationsEnvironmentsSecurityStatsQueryTabularStatsRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSecurityStatsQueryTabularStatsRequest
  object.

  Fields:
    googleCloudApigeeV1QueryTabularStatsRequest: A
      GoogleCloudApigeeV1QueryTabularStatsRequest resource to be passed as the
      request body.
    orgenv: Required. Should be of the form organizations//environments/.
  """

  googleCloudApigeeV1QueryTabularStatsRequest = _messages.MessageField('GoogleCloudApigeeV1QueryTabularStatsRequest', 1)
  orgenv = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsSecurityStatsQueryTimeSeriesStatsRequest(_messages.Message):
  r"""A
  ApigeeOrganizationsEnvironmentsSecurityStatsQueryTimeSeriesStatsRequest
  object.

  Fields:
    googleCloudApigeeV1QueryTimeSeriesStatsRequest: A
      GoogleCloudApigeeV1QueryTimeSeriesStatsRequest resource to be passed as
      the request body.
    orgenv: Required. Should be of the form organizations//environments/.
  """

  googleCloudApigeeV1QueryTimeSeriesStatsRequest = _messages.MessageField('GoogleCloudApigeeV1QueryTimeSeriesStatsRequest', 1)
  orgenv = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsSetIamPolicyRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSetIamPolicyRequest object.

  Fields:
    googleIamV1SetIamPolicyRequest: A GoogleIamV1SetIamPolicyRequest resource
      to be passed as the request body.
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1SetIamPolicyRequest = _messages.MessageField('GoogleIamV1SetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsSharedflowsDeploymentsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSharedflowsDeploymentsListRequest
  object.

  Fields:
    parent: Required. Name representing a shared flow in an environment in the
      following format:
      `organizations/{org}/environments/{env}/sharedflows/{sharedflow}`
  """

  parent = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsSharedflowsRevisionsDeployRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSharedflowsRevisionsDeployRequest
  object.

  Fields:
    name: Required. Name of the shared flow revision to deploy in the
      following format: `organizations/{org}/environments/{env}/sharedflows/{s
      haredflow}/revisions/{rev}`
    override: Flag that specifies whether the new deployment replaces other
      deployed revisions of the shared flow in the environment. Set `override`
      to `true` to replace other deployed revisions. By default, `override` is
      `false` and the deployment is rejected if other revisions of the shared
      flow are deployed in the environment.
    serviceAccount: Google Cloud IAM service account. The service account
      represents the identity of the deployed proxy, and determines what
      permissions it has. The format must be
      `{ACCOUNT_ID}@{PROJECT}.iam.gserviceaccount.com`.
  """

  name = _messages.StringField(1, required=True)
  override = _messages.BooleanField(2)
  serviceAccount = _messages.StringField(3)


class ApigeeOrganizationsEnvironmentsSharedflowsRevisionsGetDeploymentsRequest(_messages.Message):
  r"""A
  ApigeeOrganizationsEnvironmentsSharedflowsRevisionsGetDeploymentsRequest
  object.

  Fields:
    name: Required. Name representing a shared flow in an environment in the
      following format: `organizations/{org}/environments/{env}/sharedflows/{s
      haredflow}/revisions/{rev}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsSharedflowsRevisionsUndeployRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSharedflowsRevisionsUndeployRequest
  object.

  Fields:
    name: Required. Name of the shared flow revision to undeploy in the
      following format: `organizations/{org}/environments/{env}/sharedflows/{s
      haredflow}/revisions/{rev}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsStatsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsStatsGetRequest object.

  Fields:
    accuracy: No longer used by Apigee. Supported for backwards compatibility.
    aggTable: Table name used to query custom aggregate tables. If this
      parameter is skipped, then Apigee will try to retrieve the data from
      fact tables which will be expensive.
    filter: Filter that enables you to drill down on specific dimension
      values.
    limit: Maximum number of result items to return. The default and maximum
      value that can be returned is 14400.
    name: Required. Resource name for which the interactive query will be
      executed. Use the following format in your request:
      `organizations/{org}/environments/{env}/stats/{dimensions}` Dimensions
      let you view metrics in meaningful groupings, such as `apiproxy` or
      `target_host`. The value of dimensions should be a comma-separated list,
      as shown below:
      `organizations/{org}/environments/{env}/stats/apiproxy,request_verb`
    offset: Offset value. Use `offset` with `limit` to enable pagination of
      results. For example, to display results 11-20, set limit to `10` and
      offset to `10`.
    realtime: No longer used by Apigee. Supported for backwards compatibility.
    select: Comma-separated list of metrics. For example:
      `sum(message_count),sum(error_count)`
    sonar: Routes the query to API Monitoring for the last hour.
    sort: Flag that specifies whether the sort order should be ascending or
      descending. Valid values include: `DESC` and `ASC`.
    sortby: Comma-separated list of columns to sort the final result.
    timeRange: Time interval for the interactive query. Time range is
      specified in GMT as `start~end`. For example: `04/15/2017
      00:00~05/15/2017 23:59`
    timeUnit: Granularity of metrics returned. Valid values include: `second`,
      `minute`, `hour`, `day`, `week`, or` month`.
    topk: Top number of results to return. For example, to return the top 5
      results, set `topk=5`.
    tsAscending: Flag that specifies whether to list timestamps in ascending
      (`true`) or descending (`false`) order. Apigee recommends that you set
      this value to `true` if you are using `sortby` with `sort=DESC`.
    tzo: Timezone offset value.
  """

  accuracy = _messages.StringField(1)
  aggTable = _messages.StringField(2)
  filter = _messages.StringField(3)
  limit = _messages.StringField(4)
  name = _messages.StringField(5, required=True)
  offset = _messages.StringField(6)
  realtime = _messages.BooleanField(7)
  select = _messages.StringField(8)
  sonar = _messages.BooleanField(9)
  sort = _messages.StringField(10)
  sortby = _messages.StringField(11)
  timeRange = _messages.StringField(12)
  timeUnit = _messages.StringField(13)
  topk = _messages.StringField(14)
  tsAscending = _messages.BooleanField(15)
  tzo = _messages.StringField(16)


class ApigeeOrganizationsEnvironmentsSubscribeRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsSubscribeRequest object.

  Fields:
    parent: Required. Name of the environment. Use the following structure in
      your request: `organizations/{org}/environments/{env}`
  """

  parent = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsTargetserversCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsTargetserversCreateRequest object.

  Fields:
    googleCloudApigeeV1TargetServer: A GoogleCloudApigeeV1TargetServer
      resource to be passed as the request body.
    name: Optional. The ID to give the TargetServer. This will overwrite the
      value in TargetServer.
    parent: Required. The parent environment name under which the TargetServer
      will be created. Must be of the form
      `organizations/{org}/environments/{env}`.
  """

  googleCloudApigeeV1TargetServer = _messages.MessageField('GoogleCloudApigeeV1TargetServer', 1)
  name = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsEnvironmentsTargetserversDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsTargetserversDeleteRequest object.

  Fields:
    name: Required. The name of the TargetServer to delete. Must be of the
      form `organizations/{org}/environments/{env}/targetservers/{target_serve
      r_id}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsTargetserversGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsTargetserversGetRequest object.

  Fields:
    name: Required. The name of the TargetServer to get. Must be of the form
      `organizations/{org}/environments/{env}/targetservers/{target_server_id}
      `.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsTestIamPermissionsRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsTestIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsTraceConfigOverridesCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsTraceConfigOverridesCreateRequest
  object.

  Fields:
    googleCloudApigeeV1TraceConfigOverride: A
      GoogleCloudApigeeV1TraceConfigOverride resource to be passed as the
      request body.
    parent: Required. Parent resource of the trace configuration override. Use
      the following structure in your request.
      "organizations/*/environments/*/traceConfig".
  """

  googleCloudApigeeV1TraceConfigOverride = _messages.MessageField('GoogleCloudApigeeV1TraceConfigOverride', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsTraceConfigOverridesDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsTraceConfigOverridesDeleteRequest
  object.

  Fields:
    name: Required. Name of the trace configuration override. Use the
      following structure in your request:
      "organizations/*/environments/*/traceConfig/overrides/*".
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsTraceConfigOverridesGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsTraceConfigOverridesGetRequest object.

  Fields:
    name: Required. Name of the trace configuration override. Use the
      following structure in your request:
      "organizations/*/environments/*/traceConfig/overrides/*".
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsEnvironmentsTraceConfigOverridesListRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsTraceConfigOverridesListRequest object.

  Fields:
    pageSize: Maximum number of trace configuration overrides to return. If
      not specified, the maximum number returned is 25. The maximum number
      cannot exceed 100.
    pageToken: A page token, returned from a previous
      `ListTraceConfigOverrides` call. Token value that can be used to
      retrieve the subsequent page. When paginating, all other parameters
      provided to `ListTraceConfigOverrides` must match those specified in the
      call to obtain the page token.
    parent: Required. Parent resource of the trace configuration override. Use
      the following structure in your request:
      "organizations/*/environments/*/traceConfig".
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsEnvironmentsTraceConfigOverridesPatchRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsTraceConfigOverridesPatchRequest
  object.

  Fields:
    googleCloudApigeeV1TraceConfigOverride: A
      GoogleCloudApigeeV1TraceConfigOverride resource to be passed as the
      request body.
    name: Required. Name of the trace configuration override. Use the
      following structure in your request:
      "organizations/*/environments/*/traceConfig/overrides/*".
    updateMask: List of fields to be updated.
  """

  googleCloudApigeeV1TraceConfigOverride = _messages.MessageField('GoogleCloudApigeeV1TraceConfigOverride', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApigeeOrganizationsEnvironmentsUnsubscribeRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsUnsubscribeRequest object.

  Fields:
    googleCloudApigeeV1Subscription: A GoogleCloudApigeeV1Subscription
      resource to be passed as the request body.
    parent: Required. Name of the environment. Use the following structure in
      your request: `organizations/{org}/environments/{env}`
  """

  googleCloudApigeeV1Subscription = _messages.MessageField('GoogleCloudApigeeV1Subscription', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsEnvironmentsUpdateDebugmaskRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsUpdateDebugmaskRequest object.

  Fields:
    googleCloudApigeeV1DebugMask: A GoogleCloudApigeeV1DebugMask resource to
      be passed as the request body.
    name: Name of the debug mask.
    replaceRepeatedFields: Boolean flag that specifies whether to replace
      existing values in the debug mask when doing an update. Set to true to
      replace existing values. The default behavior is to append the values
      (false).
    updateMask: Field debug mask to support partial updates.
  """

  googleCloudApigeeV1DebugMask = _messages.MessageField('GoogleCloudApigeeV1DebugMask', 1)
  name = _messages.StringField(2, required=True)
  replaceRepeatedFields = _messages.BooleanField(3)
  updateMask = _messages.StringField(4)


class ApigeeOrganizationsEnvironmentsUpdateSecurityActionsConfigRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsUpdateSecurityActionsConfigRequest
  object.

  Fields:
    googleCloudApigeeV1SecurityActionsConfig: A
      GoogleCloudApigeeV1SecurityActionsConfig resource to be passed as the
      request body.
    name: This is a singleton resource, the name will always be set by
      SecurityActions and any user input will be ignored. The name is always:
      `organizations/{org}/environments/{env}/security_actions_config`
    updateMask: The list of fields to update.
  """

  googleCloudApigeeV1SecurityActionsConfig = _messages.MessageField('GoogleCloudApigeeV1SecurityActionsConfig', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApigeeOrganizationsEnvironmentsUpdateTraceConfigRequest(_messages.Message):
  r"""A ApigeeOrganizationsEnvironmentsUpdateTraceConfigRequest object.

  Fields:
    googleCloudApigeeV1TraceConfig: A GoogleCloudApigeeV1TraceConfig resource
      to be passed as the request body.
    name: Required. Name of the trace configuration. Use the following
      structure in your request: "organizations/*/environments/*/traceConfig".
    updateMask: List of fields to be updated.
  """

  googleCloudApigeeV1TraceConfig = _messages.MessageField('GoogleCloudApigeeV1TraceConfig', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApigeeOrganizationsGetControlPlaneAccessRequest(_messages.Message):
  r"""A ApigeeOrganizationsGetControlPlaneAccessRequest object.

  Fields:
    name: Required. Resource name of the Control Plane Access. Use the
      following structure in your request:
      `organizations/{org}/controlPlaneAccess`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsGetDeployedIngressConfigRequest(_messages.Message):
  r"""A ApigeeOrganizationsGetDeployedIngressConfigRequest object.

  Enums:
    ViewValueValuesEnum: When set to FULL, additional details about the
      specific deployments receiving traffic will be included in the
      IngressConfig response's RoutingRules.

  Fields:
    name: Required. Name of the deployed configuration for the organization in
      the following format: 'organizations/{org}/deployedIngressConfig'.
    view: When set to FULL, additional details about the specific deployments
      receiving traffic will be included in the IngressConfig response's
      RoutingRules.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""When set to FULL, additional details about the specific deployments
    receiving traffic will be included in the IngressConfig response's
    RoutingRules.

    Values:
      INGRESS_CONFIG_VIEW_UNSPECIFIED: The default/unset value. The API will
        default to the BASIC view.
      BASIC: Include all ingress config data necessary for the runtime to
        configure ingress, but no more. Routing rules will include only
        basepath and destination environment. This the default value.
      FULL: Include all ingress config data, including internal debug info for
        each routing rule such as the proxy claiming a particular basepath and
        when the routing rule first appeared in the env group.
    """
    INGRESS_CONFIG_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class ApigeeOrganizationsGetProjectMappingRequest(_messages.Message):
  r"""A ApigeeOrganizationsGetProjectMappingRequest object.

  Fields:
    name: Required. Apigee organization name in the following format:
      `organizations/{org}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsGetRequest object.

  Fields:
    name: Required. Apigee organization name in the following format:
      `organizations/{org}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsGetRuntimeConfigRequest(_messages.Message):
  r"""A ApigeeOrganizationsGetRuntimeConfigRequest object.

  Fields:
    name: Required. Name of the runtime config for the organization in the
      following format: 'organizations/{org}/runtimeConfig'.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsGetSyncAuthorizationRequest(_messages.Message):
  r"""A ApigeeOrganizationsGetSyncAuthorizationRequest object.

  Fields:
    googleCloudApigeeV1GetSyncAuthorizationRequest: A
      GoogleCloudApigeeV1GetSyncAuthorizationRequest resource to be passed as
      the request body.
    name: Required. Name of the Apigee organization. Use the following
      structure in your request: `organizations/{org}`
  """

  googleCloudApigeeV1GetSyncAuthorizationRequest = _messages.MessageField('GoogleCloudApigeeV1GetSyncAuthorizationRequest', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsHostQueriesCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsHostQueriesCreateRequest object.

  Fields:
    googleCloudApigeeV1Query: A GoogleCloudApigeeV1Query resource to be passed
      as the request body.
    parent: Required. The parent resource name. Must be of the form
      `organizations/{org}`.
  """

  googleCloudApigeeV1Query = _messages.MessageField('GoogleCloudApigeeV1Query', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsHostQueriesGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsHostQueriesGetRequest object.

  Fields:
    name: Required. Name of the asynchronous query to get. Must be of the form
      `organizations/{org}/queries/{queryId}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsHostQueriesGetResultRequest(_messages.Message):
  r"""A ApigeeOrganizationsHostQueriesGetResultRequest object.

  Fields:
    name: Required. Name of the asynchronous query result to get. Must be of
      the form `organizations/{org}/queries/{queryId}/result`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsHostQueriesGetResultViewRequest(_messages.Message):
  r"""A ApigeeOrganizationsHostQueriesGetResultViewRequest object.

  Fields:
    name: Required. Name of the asynchronous query result view to get. Must be
      of the form `organizations/{org}/queries/{queryId}/resultView`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsHostQueriesListRequest(_messages.Message):
  r"""A ApigeeOrganizationsHostQueriesListRequest object.

  Fields:
    dataset: Filter response list by dataset. Example: `api`, `mint`
    envgroupHostname: Required. Filter response list by hostname.
    from_: Filter response list by returning asynchronous queries that created
      after this date time. Time must be in ISO date-time format like
      '2011-12-03T10:15:30Z'.
    inclQueriesWithoutReport: Flag to include asynchronous queries that don't
      have a report denifition.
    parent: Required. The parent resource name. Must be of the form
      `organizations/{org}`.
    status: Filter response list by asynchronous query status.
    submittedBy: Filter response list by user who submitted queries.
    to: Filter response list by returning asynchronous queries that created
      before this date time. Time must be in ISO date-time format like
      '2011-12-03T10:16:30Z'.
  """

  dataset = _messages.StringField(1)
  envgroupHostname = _messages.StringField(2)
  from_ = _messages.StringField(3)
  inclQueriesWithoutReport = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)
  status = _messages.StringField(6)
  submittedBy = _messages.StringField(7)
  to = _messages.StringField(8)


class ApigeeOrganizationsHostSecurityReportsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsHostSecurityReportsCreateRequest object.

  Fields:
    googleCloudApigeeV1SecurityReportQuery: A
      GoogleCloudApigeeV1SecurityReportQuery resource to be passed as the
      request body.
    parent: Required. The parent resource name. Must be of the form
      `organizations/{org}`.
  """

  googleCloudApigeeV1SecurityReportQuery = _messages.MessageField('GoogleCloudApigeeV1SecurityReportQuery', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsHostSecurityReportsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsHostSecurityReportsGetRequest object.

  Fields:
    name: Required. Name of the security report to get. Must be of the form
      `organizations/{org}/securityReports/{reportId}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsHostSecurityReportsGetResultRequest(_messages.Message):
  r"""A ApigeeOrganizationsHostSecurityReportsGetResultRequest object.

  Fields:
    name: Required. Name of the security report result to get. Must be of the
      form `organizations/{org}/securityReports/{reportId}/result`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsHostSecurityReportsGetResultViewRequest(_messages.Message):
  r"""A ApigeeOrganizationsHostSecurityReportsGetResultViewRequest object.

  Fields:
    name: Required. Name of the security report result view to get. Must be of
      the form `organizations/{org}/securityReports/{reportId}/resultView`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsHostSecurityReportsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsHostSecurityReportsListRequest object.

  Fields:
    dataset: Filter response list by dataset. Example: `api`, `mint`
    envgroupHostname: Required. Filter response list by hostname.
    from_: Filter response list by returning security reports that created
      after this date time. Time must be in ISO date-time format like
      '2011-12-03T10:15:30Z'.
    pageSize: The maximum number of security report to return in the list
      response.
    pageToken: Token returned from the previous list response to fetch the
      next page.
    parent: Required. The parent resource name. Must be of the form
      `organizations/{org}`.
    status: Filter response list by security report status.
    submittedBy: Filter response list by user who submitted queries.
    to: Filter response list by returning security reports that created before
      this date time. Time must be in ISO date-time format like
      '2011-12-03T10:16:30Z'.
  """

  dataset = _messages.StringField(1)
  envgroupHostname = _messages.StringField(2)
  from_ = _messages.StringField(3)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  parent = _messages.StringField(6, required=True)
  status = _messages.StringField(7)
  submittedBy = _messages.StringField(8)
  to = _messages.StringField(9)


class ApigeeOrganizationsHostStatsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsHostStatsGetRequest object.

  Fields:
    accuracy: No longer used by Apigee. Supported for backwards compatibility.
    envgroupHostname: Required. Hostname for which the interactive query will
      be executed.
    filter: Flag that enables drill-down on specific dimension values.
    limit: Maximum number of result items to return. The default and maximum
      value that can be returned is 14400.
    name: Required. Resource name for which the interactive query will be
      executed. Use the following format in your request:
      `organizations/{org}/hostStats/{dimensions}` Dimensions let you view
      metrics in meaningful groupings, such as `apiproxy`, `target_host`. The
      value of dimensions should be a comma-separated list as shown below
      `organizations/{org}/hostStats/apiproxy,request_verb`
    offset: Offset value. Use `offset` with `limit` to enable pagination of
      results. For example, to display results 11-20, set limit to `10` and
      offset to `10`.
    realtime: No longer used by Apigee. Supported for backwards compatibility.
    select: Comma-separated list of metrics. For example:
      `sum(message_count),sum(error_count)`
    sort: Flag that specifies if the sort order should be ascending or
      descending. Valid values are `DESC` and `ASC`.
    sortby: Comma-separated list of columns to sort the final result.
    timeRange: Time interval for the interactive query. Time range is
      specified in GMT as `start~end`. For example: `04/15/2017
      00:00~05/15/2017 23:59`
    timeUnit: Granularity of metrics returned. Valid values include: `second`,
      `minute`, `hour`, `day`, `week`, or `month`.
    topk: Top number of results to return. For example, to return the top 5
      results, set `topk=5`.
    tsAscending: Flag that specifies whether to list timestamps in ascending
      (`true`) or descending (`false`) order. Apigee recommends that you set
      this value to `true` if you are using `sortby` with `sort=DESC`.
    tzo: Timezone offset value.
  """

  accuracy = _messages.StringField(1)
  envgroupHostname = _messages.StringField(2)
  filter = _messages.StringField(3)
  limit = _messages.StringField(4)
  name = _messages.StringField(5, required=True)
  offset = _messages.StringField(6)
  realtime = _messages.BooleanField(7)
  select = _messages.StringField(8)
  sort = _messages.StringField(9)
  sortby = _messages.StringField(10)
  timeRange = _messages.StringField(11)
  timeUnit = _messages.StringField(12)
  topk = _messages.StringField(13)
  tsAscending = _messages.BooleanField(14)
  tzo = _messages.StringField(15)


class ApigeeOrganizationsInstancesAttachmentsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsInstancesAttachmentsCreateRequest object.

  Fields:
    googleCloudApigeeV1InstanceAttachment: A
      GoogleCloudApigeeV1InstanceAttachment resource to be passed as the
      request body.
    parent: Required. Name of the instance. Use the following structure in
      your request: `organizations/{org}/instances/{instance}`.
  """

  googleCloudApigeeV1InstanceAttachment = _messages.MessageField('GoogleCloudApigeeV1InstanceAttachment', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsInstancesAttachmentsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsInstancesAttachmentsDeleteRequest object.

  Fields:
    name: Required. Name of the attachment. Use the following structure in
      your request:
      `organizations/{org}/instances/{instance}/attachments/{attachment}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsInstancesAttachmentsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsInstancesAttachmentsGetRequest object.

  Fields:
    name: Required. Name of the attachment. Use the following structure in
      your request:
      `organizations/{org}/instances/{instance}/attachments/{attachment}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsInstancesAttachmentsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsInstancesAttachmentsListRequest object.

  Fields:
    pageSize: Maximum number of instance attachments to return. Defaults to
      25.
    pageToken: Page token, returned by a previous ListInstanceAttachments
      call, that you can use to retrieve the next page of content.
    parent: Required. Name of the organization. Use the following structure in
      your request: `organizations/{org}/instances/{instance}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsInstancesCanaryevaluationsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsInstancesCanaryevaluationsCreateRequest object.

  Fields:
    googleCloudApigeeV1CanaryEvaluation: A GoogleCloudApigeeV1CanaryEvaluation
      resource to be passed as the request body.
    parent: Required. Name of the organization. Use the following structure in
      your request: `organizations/{org}/instances/{instance}`.
  """

  googleCloudApigeeV1CanaryEvaluation = _messages.MessageField('GoogleCloudApigeeV1CanaryEvaluation', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsInstancesCanaryevaluationsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsInstancesCanaryevaluationsGetRequest object.

  Fields:
    name: Required. Name of the CanaryEvaluation. Use the following structure
      in your request:
      `organizations/{org}/instances/*/canaryevaluations/{evaluation}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsInstancesCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsInstancesCreateRequest object.

  Fields:
    environments: Optional. DEPRECATED: DO NOT USE. List of environments that
      will be attached to the instance during creation.
    googleCloudApigeeV1Instance: A GoogleCloudApigeeV1Instance resource to be
      passed as the request body.
    parent: Required. Name of the organization. Use the following structure in
      your request: `organizations/{org}`.
    runtimeVersion: Optional. Software config version for instance creation.
      runtime_version value can contain only alphanumeric characters and
      hyphens (-) and cannot begin or end with a hyphen.
  """

  environments = _messages.StringField(1, repeated=True)
  googleCloudApigeeV1Instance = _messages.MessageField('GoogleCloudApigeeV1Instance', 2)
  parent = _messages.StringField(3, required=True)
  runtimeVersion = _messages.StringField(4)


class ApigeeOrganizationsInstancesDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsInstancesDeleteRequest object.

  Fields:
    name: Required. Name of the instance. Use the following structure in your
      request: `organizations/{org}/instances/{instance}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsInstancesGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsInstancesGetRequest object.

  Fields:
    name: Required. Name of the instance. Use the following structure in your
      request: `organizations/{org}/instances/{instance}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsInstancesListRequest(_messages.Message):
  r"""A ApigeeOrganizationsInstancesListRequest object.

  Fields:
    pageSize: Maximum number of instances to return. Defaults to 25.
    pageToken: Page token, returned from a previous ListInstances call, that
      you can use to retrieve the next page of content.
    parent: Required. Name of the organization. Use the following structure in
      your request: `organizations/{org}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsInstancesNatAddressesActivateRequest(_messages.Message):
  r"""A ApigeeOrganizationsInstancesNatAddressesActivateRequest object.

  Fields:
    googleCloudApigeeV1ActivateNatAddressRequest: A
      GoogleCloudApigeeV1ActivateNatAddressRequest resource to be passed as
      the request body.
    name: Required. Name of the nat address. Use the following structure in
      your request:
      `organizations/{org}/instances/{instances}/natAddresses/{nataddress}``
  """

  googleCloudApigeeV1ActivateNatAddressRequest = _messages.MessageField('GoogleCloudApigeeV1ActivateNatAddressRequest', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsInstancesNatAddressesCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsInstancesNatAddressesCreateRequest object.

  Fields:
    googleCloudApigeeV1NatAddress: A GoogleCloudApigeeV1NatAddress resource to
      be passed as the request body.
    parent: Required. Name of the instance. Use the following structure in
      your request: `organizations/{org}/instances/{instance}`
  """

  googleCloudApigeeV1NatAddress = _messages.MessageField('GoogleCloudApigeeV1NatAddress', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsInstancesNatAddressesDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsInstancesNatAddressesDeleteRequest object.

  Fields:
    name: Required. Name of the nat address. Use the following structure in
      your request:
      `organizations/{org}/instances/{instances}/natAddresses/{nataddress}``
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsInstancesNatAddressesGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsInstancesNatAddressesGetRequest object.

  Fields:
    name: Required. Name of the nat address. Use the following structure in
      your request:
      `organizations/{org}/instances/{instances}/natAddresses/{nataddress}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsInstancesNatAddressesListRequest(_messages.Message):
  r"""A ApigeeOrganizationsInstancesNatAddressesListRequest object.

  Fields:
    pageSize: Maximum number of natAddresses to return. Defaults to 25.
    pageToken: Page token, returned from a previous ListNatAddresses call,
      that you can use to retrieve the next page of content.
    parent: Required. Name of the instance. Use the following structure in
      your request: `organizations/{org}/instances/{instance}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsInstancesPatchRequest(_messages.Message):
  r"""A ApigeeOrganizationsInstancesPatchRequest object.

  Fields:
    googleCloudApigeeV1Instance: A GoogleCloudApigeeV1Instance resource to be
      passed as the request body.
    name: Required. Name of the instance. Use the following structure in your
      request: `organizations/{org}/instances/{instance}`.
    updateMask: List of fields to be updated.
  """

  googleCloudApigeeV1Instance = _messages.MessageField('GoogleCloudApigeeV1Instance', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApigeeOrganizationsInstancesReportStatusRequest(_messages.Message):
  r"""A ApigeeOrganizationsInstancesReportStatusRequest object.

  Fields:
    googleCloudApigeeV1ReportInstanceStatusRequest: A
      GoogleCloudApigeeV1ReportInstanceStatusRequest resource to be passed as
      the request body.
    instance: The name of the instance reporting this status. For SaaS the
      request will be rejected if no instance exists under this name. Format
      is organizations/{org}/instances/{instance}
  """

  googleCloudApigeeV1ReportInstanceStatusRequest = _messages.MessageField('GoogleCloudApigeeV1ReportInstanceStatusRequest', 1)
  instance = _messages.StringField(2, required=True)


class ApigeeOrganizationsKeyvaluemapsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsKeyvaluemapsCreateRequest object.

  Fields:
    googleCloudApigeeV1KeyValueMap: A GoogleCloudApigeeV1KeyValueMap resource
      to be passed as the request body.
    parent: Required. Name of the organization in which to create the key
      value map file. Use the following structure in your request:
      `organizations/{org}`
  """

  googleCloudApigeeV1KeyValueMap = _messages.MessageField('GoogleCloudApigeeV1KeyValueMap', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsKeyvaluemapsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsKeyvaluemapsDeleteRequest object.

  Fields:
    name: Required. Name of the key value map. Use the following structure in
      your request: `organizations/{org}/keyvaluemaps/{keyvaluemap}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsKeyvaluemapsEntriesCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsKeyvaluemapsEntriesCreateRequest object.

  Fields:
    googleCloudApigeeV1KeyValueEntry: A GoogleCloudApigeeV1KeyValueEntry
      resource to be passed as the request body.
    parent: Required. Scope as indicated by the URI in which to create the key
      value map entry. Use **one** of the following structures in your
      request: *
      `organizations/{organization}/apis/{api}/keyvaluemaps/{keyvaluemap}`. *
      `organizations/{organization}/environments/{environment}/keyvaluemaps/{k
      eyvaluemap}` *
      `organizations/{organization}/keyvaluemaps/{keyvaluemap}`.
  """

  googleCloudApigeeV1KeyValueEntry = _messages.MessageField('GoogleCloudApigeeV1KeyValueEntry', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsKeyvaluemapsEntriesDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsKeyvaluemapsEntriesDeleteRequest object.

  Fields:
    name: Required. Scope as indicated by the URI in which to delete the key
      value map entry. Use **one** of the following structures in your
      request: * `organizations/{organization}/apis/{api}/keyvaluemaps/{keyval
      uemap}/entries/{entry}`. * `organizations/{organization}/environments/{e
      nvironment}/keyvaluemaps/{keyvaluemap}/entries/{entry}` * `organizations
      /{organization}/keyvaluemaps/{keyvaluemap}/entries/{entry}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsKeyvaluemapsEntriesGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsKeyvaluemapsEntriesGetRequest object.

  Fields:
    name: Required. Scope as indicated by the URI in which to fetch the key
      value map entry/value. Use **one** of the following structures in your
      request: * `organizations/{organization}/apis/{api}/keyvaluemaps/{keyval
      uemap}/entries/{entry}`. * `organizations/{organization}/environments/{e
      nvironment}/keyvaluemaps/{keyvaluemap}/entries/{entry}` * `organizations
      /{organization}/keyvaluemaps/{keyvaluemap}/entries/{entry}`.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsKeyvaluemapsEntriesListRequest(_messages.Message):
  r"""A ApigeeOrganizationsKeyvaluemapsEntriesListRequest object.

  Fields:
    pageSize: Optional. Maximum number of key value entries to return. If
      unspecified, at most 100 entries will be returned.
    pageToken: Optional. Page token. If provides, must be a valid key value
      entry returned from a previous call that can be used to retrieve the
      next page.
    parent: Required. Scope as indicated by the URI in which to list key value
      maps. Use **one** of the following structures in your request: *
      `organizations/{organization}/apis/{api}/keyvaluemaps/{keyvaluemap}`. *
      `organizations/{organization}/environments/{environment}/keyvaluemaps/{k
      eyvaluemap}` *
      `organizations/{organization}/keyvaluemaps/{keyvaluemap}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsListRequest object.

  Fields:
    parent: Required. Use the following structure in your request:
      `organizations`
  """

  parent = _messages.StringField(1, required=True)


class ApigeeOrganizationsOperationsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsOperationsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ApigeeOrganizationsOptimizedHostStatsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsOptimizedHostStatsGetRequest object.

  Fields:
    accuracy: No longer used by Apigee. Supported for backwards compatibility.
    envgroupHostname: Required. Hostname for which the interactive query will
      be executed.
    filter: Filter that enables you to drill-down on specific dimension
      values.
    limit: Maximum number of result items to return. The default and maximum
      value that can be returned is 14400.
    name: Required. Resource name for which the interactive query will be
      executed. Use the following format in your request:
      `organizations/{organization_id}/optimizedHostStats/{dimensions}`
      Dimensions let you view metrics in meaningful groupings, such as
      `apiproxy`, `target_host`. The value of dimensions should be a comma-
      separated list as shown below:
      `organizations/{org}/optimizedHostStats/apiproxy,request_verb`
    offset: Offset value. Use `offset` with `limit` to enable pagination of
      results. For example, to display results 11-20, set limit to `10` and
      offset to `10`.
    realtime: No longer used by Apigee. Supported for backwards compatibility.
    select: Required. Comma-separated list of metrics. For example:
      `sum(message_count),sum(error_count)`
    sort: Flag that specifies whether the sort order should be ascending or
      descending. Valid values include `DESC` and `ASC`.
    sortby: Comma-separated list of columns used to sort the final result.
    timeRange: Required. Time interval for the interactive query. Time range
      is specified in GMT as `start~end`. For example: `04/15/2017
      00:00~05/15/2017 23:59`.
    timeUnit: Granularity of metrics returned. Valid values include: `second`,
      `minute`, `hour`, `day`, `week`, or `month`.
    topk: Top number of results to return. For example, to return the top 5
      results, set `topk=5`.
    tsAscending: Flag that specifies whether to list timestamps in ascending
      (`true`) or descending (`false`) order. Apigee recommends that you set
      this value to `true` if you are using `sortby` with `sort=DESC`.
    tzo: Timezone offset value.
  """

  accuracy = _messages.StringField(1)
  envgroupHostname = _messages.StringField(2)
  filter = _messages.StringField(3)
  limit = _messages.StringField(4)
  name = _messages.StringField(5, required=True)
  offset = _messages.StringField(6)
  realtime = _messages.BooleanField(7)
  select = _messages.StringField(8)
  sort = _messages.StringField(9)
  sortby = _messages.StringField(10)
  timeRange = _messages.StringField(11)
  timeUnit = _messages.StringField(12)
  topk = _messages.StringField(13)
  tsAscending = _messages.BooleanField(14)
  tzo = _messages.StringField(15)


class ApigeeOrganizationsPatchRequest(_messages.Message):
  r"""A ApigeeOrganizationsPatchRequest object.

  Fields:
    googleCloudApigeeV1Organization: A GoogleCloudApigeeV1Organization
      resource to be passed as the request body.
    name: Required. Apigee organization name in the following format:
      `organizations/{org}`
    updateMask: List of fields to be updated. Fields that can be updated:
      release_channel.
  """

  googleCloudApigeeV1Organization = _messages.MessageField('GoogleCloudApigeeV1Organization', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApigeeOrganizationsReportsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsReportsCreateRequest object.

  Fields:
    googleCloudApigeeV1CustomReport: A GoogleCloudApigeeV1CustomReport
      resource to be passed as the request body.
    parent: Required. The parent organization name under which the Custom
      Report will be created. Must be of the form:
      `organizations/{organization_id}/reports`
  """

  googleCloudApigeeV1CustomReport = _messages.MessageField('GoogleCloudApigeeV1CustomReport', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsReportsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsReportsDeleteRequest object.

  Fields:
    name: Required. Custom Report name of the form:
      `organizations/{organization_id}/reports/{report_name}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsReportsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsReportsGetRequest object.

  Fields:
    name: Required. Custom Report name of the form:
      `organizations/{organization_id}/reports/{report_name}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsReportsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsReportsListRequest object.

  Fields:
    expand: Set to 'true' to get expanded details about each custom report.
    parent: Required. The parent organization name under which the API product
      will be listed `organizations/{organization_id}/reports`
  """

  expand = _messages.BooleanField(1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsSecurityProfilesCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsSecurityProfilesCreateRequest object.

  Fields:
    googleCloudApigeeV1SecurityProfile: A GoogleCloudApigeeV1SecurityProfile
      resource to be passed as the request body.
    parent: Required. Name of organization. Format: organizations/{org}
    securityProfileId: Required. The ID to use for the SecurityProfile, which
      will become the final component of the action's resource name. This
      value should be 4-63 characters, and valid characters are
      /(^[a-z]([a-z0-9-]{\u200b0,61}[a-z0-9])?$/.
  """

  googleCloudApigeeV1SecurityProfile = _messages.MessageField('GoogleCloudApigeeV1SecurityProfile', 1)
  parent = _messages.StringField(2, required=True)
  securityProfileId = _messages.StringField(3)


class ApigeeOrganizationsSecurityProfilesDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsSecurityProfilesDeleteRequest object.

  Fields:
    name: Required. Name of profile. Format:
      organizations/{org}/securityProfiles/{profile}
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsSecurityProfilesEnvironmentsComputeEnvironmentScoresRequest(_messages.Message):
  r"""A ApigeeOrganizationsSecurityProfilesEnvironmentsComputeEnvironmentScore
  sRequest object.

  Fields:
    googleCloudApigeeV1ComputeEnvironmentScoresRequest: A
      GoogleCloudApigeeV1ComputeEnvironmentScoresRequest resource to be passed
      as the request body.
    profileEnvironment: Required. Name of organization and environment and
      profile id for which score needs to be computed. Format:
      organizations/{org}/securityProfiles/{profile}/environments/{env}
  """

  googleCloudApigeeV1ComputeEnvironmentScoresRequest = _messages.MessageField('GoogleCloudApigeeV1ComputeEnvironmentScoresRequest', 1)
  profileEnvironment = _messages.StringField(2, required=True)


class ApigeeOrganizationsSecurityProfilesEnvironmentsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsSecurityProfilesEnvironmentsCreateRequest object.

  Fields:
    googleCloudApigeeV1SecurityProfileEnvironmentAssociation: A
      GoogleCloudApigeeV1SecurityProfileEnvironmentAssociation resource to be
      passed as the request body.
    parent: Required. Name of organization and security profile ID. Format:
      organizations/{org}/securityProfiles/{profile}
  """

  googleCloudApigeeV1SecurityProfileEnvironmentAssociation = _messages.MessageField('GoogleCloudApigeeV1SecurityProfileEnvironmentAssociation', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsSecurityProfilesEnvironmentsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsSecurityProfilesEnvironmentsDeleteRequest object.

  Fields:
    name: Required. The name of the environment attachment to delete. Format:
      organizations/{org}/securityProfiles/{profile}/environments/{env}
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsSecurityProfilesGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsSecurityProfilesGetRequest object.

  Fields:
    name: Required. Security profile in the following format:
      `organizations/{org}/securityProfiles/{profile}'. Profile may optionally
      contain revision ID. If revision ID is not provided, the response will
      contain latest revision by default. Example:
      organizations/testOrg/securityProfiles/testProfile@5
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsSecurityProfilesListRequest(_messages.Message):
  r"""A ApigeeOrganizationsSecurityProfilesListRequest object.

  Fields:
    pageSize: The maximum number of profiles to return. The service may return
      fewer than this value. If unspecified, at most 50 profiles will be
      returned.
    pageToken: A page token, received from a previous `ListSecurityProfiles`
      call. Provide this to retrieve the subsequent page.
    parent: Required. For a specific organization, list of all the security
      profiles. Format: `organizations/{org}`
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsSecurityProfilesListRevisionsRequest(_messages.Message):
  r"""A ApigeeOrganizationsSecurityProfilesListRevisionsRequest object.

  Fields:
    name: Required. For a specific profile, list all the revisions. Format:
      `organizations/{org}/securityProfiles/{profile}`
    pageSize: The maximum number of profile revisions to return. The service
      may return fewer than this value. If unspecified, at most 50 revisions
      will be returned.
    pageToken: A page token, received from a previous
      `ListSecurityProfileRevisions` call. Provide this to retrieve the
      subsequent page.
  """

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)


class ApigeeOrganizationsSecurityProfilesPatchRequest(_messages.Message):
  r"""A ApigeeOrganizationsSecurityProfilesPatchRequest object.

  Fields:
    googleCloudApigeeV1SecurityProfile: A GoogleCloudApigeeV1SecurityProfile
      resource to be passed as the request body.
    name: Immutable. Name of the security profile resource. Format:
      organizations/{org}/securityProfiles/{profile}
    updateMask: Required. The list of fields to update.
  """

  googleCloudApigeeV1SecurityProfile = _messages.MessageField('GoogleCloudApigeeV1SecurityProfile', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApigeeOrganizationsSecurityincidentenvironmentsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsSecurityincidentenvironmentsListRequest object.

  Fields:
    filter: Filter list security incident stats per environment by time range
      "first_detected_time >", "last_detected_time <"
    orderBy: Field to sort by. See https://google.aip.dev/132#ordering for
      more details. If not specified, the results will be sorted in the
      default order.
    pageSize: The maximum number of environments to return. The service may
      return fewer than this value. If unspecified, at most 50 environments
      will be returned.
    pageToken: A page token, received from a previous
      `ListSecurityIncidentEnvironments` call. Provide this to retrieve the
      subsequent page.
    parent: Required. For a specific organization, list all environments with
      security incidents stats. Format: `organizations/{org}}`
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ApigeeOrganizationsSetAddonsRequest(_messages.Message):
  r"""A ApigeeOrganizationsSetAddonsRequest object.

  Fields:
    googleCloudApigeeV1SetAddonsRequest: A GoogleCloudApigeeV1SetAddonsRequest
      resource to be passed as the request body.
    org: Required. Name of the organization. Use the following structure in
      your request: `organizations/{org}`
  """

  googleCloudApigeeV1SetAddonsRequest = _messages.MessageField('GoogleCloudApigeeV1SetAddonsRequest', 1)
  org = _messages.StringField(2, required=True)


class ApigeeOrganizationsSetSyncAuthorizationRequest(_messages.Message):
  r"""A ApigeeOrganizationsSetSyncAuthorizationRequest object.

  Fields:
    googleCloudApigeeV1SyncAuthorization: A
      GoogleCloudApigeeV1SyncAuthorization resource to be passed as the
      request body.
    name: Required. Name of the Apigee organization. Use the following
      structure in your request: `organizations/{org}`
  """

  googleCloudApigeeV1SyncAuthorization = _messages.MessageField('GoogleCloudApigeeV1SyncAuthorization', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsSharedflowsCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsSharedflowsCreateRequest object.

  Fields:
    action: Required. Must be set to either `import` or `validate`.
    googleApiHttpBody: A GoogleApiHttpBody resource to be passed as the
      request body.
    name: Required. The name to give the shared flow
    parent: Required. The name of the parent organization under which to
      create the shared flow. Must be of the form:
      `organizations/{organization_id}`
  """

  action = _messages.StringField(1)
  googleApiHttpBody = _messages.MessageField('GoogleApiHttpBody', 2)
  name = _messages.StringField(3)
  parent = _messages.StringField(4, required=True)


class ApigeeOrganizationsSharedflowsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsSharedflowsDeleteRequest object.

  Fields:
    name: Required. shared flow name of the form:
      `organizations/{organization_id}/sharedflows/{shared_flow_id}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsSharedflowsDeploymentsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsSharedflowsDeploymentsListRequest object.

  Fields:
    parent: Required. Name of the shared flow for which to return deployment
      information in the following format:
      `organizations/{org}/sharedflows/{sharedflow}`
  """

  parent = _messages.StringField(1, required=True)


class ApigeeOrganizationsSharedflowsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsSharedflowsGetRequest object.

  Fields:
    name: Required. The name of the shared flow to get. Must be of the form:
      `organizations/{organization_id}/sharedflows/{shared_flow_id}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsSharedflowsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsSharedflowsListRequest object.

  Fields:
    includeMetaData: Indicates whether to include shared flow metadata in the
      response.
    includeRevisions: Indicates whether to include a list of revisions in the
      response.
    parent: Required. The name of the parent organization under which to get
      shared flows. Must be of the form: `organizations/{organization_id}`
  """

  includeMetaData = _messages.BooleanField(1)
  includeRevisions = _messages.BooleanField(2)
  parent = _messages.StringField(3, required=True)


class ApigeeOrganizationsSharedflowsRevisionsDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsSharedflowsRevisionsDeleteRequest object.

  Fields:
    name: Required. The name of the shared flow revision to delete. Must be of
      the form: `organizations/{organization_id}/sharedflows/{shared_flow_id}/
      revisions/{revision_id}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsSharedflowsRevisionsDeploymentsListRequest(_messages.Message):
  r"""A ApigeeOrganizationsSharedflowsRevisionsDeploymentsListRequest object.

  Fields:
    parent: Required. Name of the API proxy revision for which to return
      deployment information in the following format:
      `organizations/{org}/sharedflows/{sharedflow}/revisions/{rev}`.
  """

  parent = _messages.StringField(1, required=True)


class ApigeeOrganizationsSharedflowsRevisionsGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsSharedflowsRevisionsGetRequest object.

  Fields:
    format: Specify `bundle` to export the contents of the shared flow bundle.
      Otherwise, the bundle metadata is returned.
    name: Required. The name of the shared flow revision to get. Must be of
      the form: `organizations/{organization_id}/sharedflows/{shared_flow_id}/
      revisions/{revision_id}`
  """

  format = _messages.StringField(1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsSharedflowsRevisionsUpdateSharedFlowRevisionRequest(_messages.Message):
  r"""A ApigeeOrganizationsSharedflowsRevisionsUpdateSharedFlowRevisionRequest
  object.

  Fields:
    googleApiHttpBody: A GoogleApiHttpBody resource to be passed as the
      request body.
    name: Required. The name of the shared flow revision to update. Must be of
      the form: `organizations/{organization_id}/sharedflows/{shared_flow_id}/
      revisions/{revision_id}`
    validate: Ignored. All uploads are validated regardless of the value of
      this field. It is kept for compatibility with existing APIs. Must be
      `true` or `false` if provided.
  """

  googleApiHttpBody = _messages.MessageField('GoogleApiHttpBody', 1)
  name = _messages.StringField(2, required=True)
  validate = _messages.BooleanField(3)


class ApigeeOrganizationsSitesApicategoriesCreateRequest(_messages.Message):
  r"""A ApigeeOrganizationsSitesApicategoriesCreateRequest object.

  Fields:
    googleCloudApigeeV1ApiCategoryData: A GoogleCloudApigeeV1ApiCategoryData
      resource to be passed as the request body.
    parent: Required. Name of the portal. Use the following structure in your
      request: `organizations/{org}/sites/{site}`
  """

  googleCloudApigeeV1ApiCategoryData = _messages.MessageField('GoogleCloudApigeeV1ApiCategoryData', 1)
  parent = _messages.StringField(2, required=True)


class ApigeeOrganizationsSitesApicategoriesDeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsSitesApicategoriesDeleteRequest object.

  Fields:
    name: Required. Name of the category. Use the following structure in your
      request: `organizations/{org}/sites/{site}/apicategories/{apicategory}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsSitesApicategoriesGetRequest(_messages.Message):
  r"""A ApigeeOrganizationsSitesApicategoriesGetRequest object.

  Fields:
    name: Required. Name of the category. Use the following structure in your
      request: `organizations/{org}/sites/{site}/apicategories/{apicategory}`
  """

  name = _messages.StringField(1, required=True)


class ApigeeOrganizationsSitesApicategoriesListRequest(_messages.Message):
  r"""A ApigeeOrganizationsSitesApicategoriesListRequest object.

  Fields:
    parent: Required. Name of the portal. Use the following structure in your
      request: `organizations/{org}/sites/{site}`
  """

  parent = _messages.StringField(1, required=True)


class ApigeeOrganizationsTestIamPermissionsRequest(_messages.Message):
  r"""A ApigeeOrganizationsTestIamPermissionsRequest object.

  Fields:
    googleIamV1TestIamPermissionsRequest: A
      GoogleIamV1TestIamPermissionsRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  googleIamV1TestIamPermissionsRequest = _messages.MessageField('GoogleIamV1TestIamPermissionsRequest', 1)
  resource = _messages.StringField(2, required=True)


class ApigeeOrganizationsUndeleteRequest(_messages.Message):
  r"""A ApigeeOrganizationsUndeleteRequest object.

  Fields:
    googleCloudApigeeV1UndeleteOrganizationRequest: A
      GoogleCloudApigeeV1UndeleteOrganizationRequest resource to be passed as
      the request body.
    name: Required. Name of the organization. Use the following structure in
      your request: `organizations/{org}`
  """

  googleCloudApigeeV1UndeleteOrganizationRequest = _messages.MessageField('GoogleCloudApigeeV1UndeleteOrganizationRequest', 1)
  name = _messages.StringField(2, required=True)


class ApigeeOrganizationsUpdateControlPlaneAccessRequest(_messages.Message):
  r"""A ApigeeOrganizationsUpdateControlPlaneAccessRequest object.

  Fields:
    googleCloudApigeeV1ControlPlaneAccess: A
      GoogleCloudApigeeV1ControlPlaneAccess resource to be passed as the
      request body.
    name: The resource name of the ControlPlaneAccess. Format:
      "organizations/{org}/controlPlaneAccess"
    updateMask: List of fields to be updated. Fields that can be updated:
      portal_disabled, release_channel, addon_config.
  """

  googleCloudApigeeV1ControlPlaneAccess = _messages.MessageField('GoogleCloudApigeeV1ControlPlaneAccess', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ApigeeProjectsProvisionOrganizationRequest(_messages.Message):
  r"""A ApigeeProjectsProvisionOrganizationRequest object.

  Fields:
    googleCloudApigeeV1ProvisionOrganizationRequest: A
      GoogleCloudApigeeV1ProvisionOrganizationRequest resource to be passed as
      the request body.
    project: Required. Name of the GCP project with which to associate the
      Apigee organization.
  """

  googleCloudApigeeV1ProvisionOrganizationRequest = _messages.MessageField('GoogleCloudApigeeV1ProvisionOrganizationRequest', 1)
  project = _messages.StringField(2, required=True)


class EdgeConfigstoreBundleBadBundle(_messages.Message):
  r"""Describes why a bundle is invalid. Intended for use in error details.

  Fields:
    violations: Describes all precondition violations.
  """

  violations = _messages.MessageField('EdgeConfigstoreBundleBadBundleViolation', 1, repeated=True)


class EdgeConfigstoreBundleBadBundleViolation(_messages.Message):
  r"""A message type used to describe a single bundle validation error.

  Fields:
    description: A description of why the bundle is invalid and how to fix it.
    filename: The filename (including relative path from the bundle root) in
      which the error occurred.
  """

  description = _messages.StringField(1)
  filename = _messages.StringField(2)


class GoogleApiHttpBody(_messages.Message):
  r"""Message that represents an arbitrary HTTP body. It should only be used
  for payload formats that can't be represented as JSON, such as raw binary or
  an HTML page. This message can be used both in streaming and non-streaming
  API methods in the request as well as the response. It can be used as a top-
  level request field, which is convenient if one wants to extract parameters
  from either the URL or HTTP template into the request fields and also want
  access to the raw HTTP body. Example: message GetResourceRequest { // A
  unique request id. string request_id = 1; // The raw HTTP body is bound to
  this field. google.api.HttpBody http_body = 2; } service ResourceService {
  rpc GetResource(GetResourceRequest) returns (google.api.HttpBody); rpc
  UpdateResource(google.api.HttpBody) returns (google.protobuf.Empty); }
  Example with streaming methods: service CaldavService { rpc
  GetCalendar(stream google.api.HttpBody) returns (stream
  google.api.HttpBody); rpc UpdateCalendar(stream google.api.HttpBody) returns
  (stream google.api.HttpBody); } Use of this type only changes how the
  request and response bodies are handled, all other features will continue to
  work unchanged.

  Messages:
    ExtensionsValueListEntry: A ExtensionsValueListEntry object.

  Fields:
    contentType: The HTTP Content-Type header value specifying the content
      type of the body.
    data: The HTTP request/response body as raw binary.
    extensions: Application specific response metadata. Must be set in the
      first response for streaming APIs.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExtensionsValueListEntry(_messages.Message):
    r"""A ExtensionsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a
        ExtensionsValueListEntry object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExtensionsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  contentType = _messages.StringField(1)
  data = _messages.BytesField(2)
  extensions = _messages.MessageField('ExtensionsValueListEntry', 3, repeated=True)


class GoogleCloudApigeeV1APIProductAssociation(_messages.Message):
  r"""APIProductAssociation has the API product and its administrative state
  association.

  Fields:
    apiproduct: API product to be associated with the credential.
    status: The API product credential associated status. Valid values are
      `approved` or `revoked`.
  """

  apiproduct = _messages.StringField(1)
  status = _messages.StringField(2)


class GoogleCloudApigeeV1Access(_messages.Message):
  r"""A GoogleCloudApigeeV1Access object.

  Fields:
    Get: A GoogleCloudApigeeV1AccessGet attribute.
    Remove: A GoogleCloudApigeeV1AccessRemove attribute.
    Set: A GoogleCloudApigeeV1AccessSet attribute.
  """

  Get = _messages.MessageField('GoogleCloudApigeeV1AccessGet', 1)
  Remove = _messages.MessageField('GoogleCloudApigeeV1AccessRemove', 2)
  Set = _messages.MessageField('GoogleCloudApigeeV1AccessSet', 3)


class GoogleCloudApigeeV1AccessGet(_messages.Message):
  r"""Get action. For example, "Get" : { "name" : "target.name", "value" :
  "default" }

  Fields:
    name: A string attribute.
    value: A string attribute.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleCloudApigeeV1AccessLoggingConfig(_messages.Message):
  r"""Access logging configuration enables customers to ship the access logs
  from the tenant projects to their own project's cloud logging. The feature
  is at the instance level ad disabled by default. It can be enabled during
  CreateInstance or UpdateInstance.

  Fields:
    enabled: Optional. Boolean flag that specifies whether the customer access
      log feature is enabled.
    filter: Optional. Ship the access log entries that match the status_code
      defined in the filter. The status_code is the only expected/supported
      filter field. (Ex: status_code) The filter will parse it to the Common
      Expression Language semantics for expression evaluation to build the
      filter condition. (Ex: "filter": status_code >= 200 && status_code < 300
      )
  """

  enabled = _messages.BooleanField(1)
  filter = _messages.StringField(2)


class GoogleCloudApigeeV1AccessRemove(_messages.Message):
  r"""Remove action. For example, "Remove" : { "name" : "target.name",
  "success" : true }

  Fields:
    name: A string attribute.
    success: A boolean attribute.
  """

  name = _messages.StringField(1)
  success = _messages.BooleanField(2)


class GoogleCloudApigeeV1AccessSet(_messages.Message):
  r"""Set action. For example, "Set" : { "name" : "target.name", "success" :
  true, "value" : "default" }

  Fields:
    name: A string attribute.
    success: A boolean attribute.
    value: A string attribute.
  """

  name = _messages.StringField(1)
  success = _messages.BooleanField(2)
  value = _messages.StringField(3)


class GoogleCloudApigeeV1ActivateNatAddressRequest(_messages.Message):
  r"""Request for ActivateNatAddressRequest. Activate the nat address request.
  """



class GoogleCloudApigeeV1AddonsConfig(_messages.Message):
  r"""Add-on configurations for the Apigee organization.

  Fields:
    advancedApiOpsConfig: Configuration for the Advanced API Ops add-on.
    analyticsConfig: Configuration for the Analytics add-on.
    apiSecurityConfig: Configuration for the API Security add-on.
    connectorsPlatformConfig: Configuration for the Connectors Platform add-
      on.
    integrationConfig: Configuration for the Integration add-on.
    monetizationConfig: Configuration for the Monetization add-on.
  """

  advancedApiOpsConfig = _messages.MessageField('GoogleCloudApigeeV1AdvancedApiOpsConfig', 1)
  analyticsConfig = _messages.MessageField('GoogleCloudApigeeV1AnalyticsConfig', 2)
  apiSecurityConfig = _messages.MessageField('GoogleCloudApigeeV1ApiSecurityConfig', 3)
  connectorsPlatformConfig = _messages.MessageField('GoogleCloudApigeeV1ConnectorsPlatformConfig', 4)
  integrationConfig = _messages.MessageField('GoogleCloudApigeeV1IntegrationConfig', 5)
  monetizationConfig = _messages.MessageField('GoogleCloudApigeeV1MonetizationConfig', 6)


class GoogleCloudApigeeV1AdjustDeveloperBalanceRequest(_messages.Message):
  r"""Request for AdjustDeveloperBalance.

  Fields:
    adjustment: * A positive value of `adjustment` means that that the API
      provider wants to adjust the balance for an under-charged developer i.e.
      the balance of the developer will decrease. * A negative value of
      `adjustment` means that that the API provider wants to adjust the
      balance for an over-charged developer i.e. the balance of the developer
      will increase.
  """

  adjustment = _messages.MessageField('GoogleTypeMoney', 1)


class GoogleCloudApigeeV1AdvancedApiOpsConfig(_messages.Message):
  r"""Configuration for the Advanced API Ops add-on.

  Fields:
    enabled: Flag that specifies whether the Advanced API Ops add-on is
      enabled.
    expiresAt: Output only. Time at which the Advanced API Ops add-on expires
      in in milliseconds since epoch. If unspecified, the add-on will never
      expire.
  """

  enabled = _messages.BooleanField(1)
  expiresAt = _messages.IntegerField(2)


class GoogleCloudApigeeV1Alias(_messages.Message):
  r"""Reference to a certificate or key/certificate pair.

  Enums:
    TypeValueValuesEnum: Type of alias.

  Fields:
    alias: Resource ID for this alias. Values must match the regular
      expression `[^/]{1,255}`.
    certsInfo: Chain of certificates under this alias.
    type: Type of alias.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of alias.

    Values:
      ALIAS_TYPE_UNSPECIFIED: Alias type is not specified.
      CERT: Certificate.
      KEY_CERT: Key/certificate pair.
    """
    ALIAS_TYPE_UNSPECIFIED = 0
    CERT = 1
    KEY_CERT = 2

  alias = _messages.StringField(1)
  certsInfo = _messages.MessageField('GoogleCloudApigeeV1Certificate', 2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class GoogleCloudApigeeV1AliasRevisionConfig(_messages.Message):
  r"""A GoogleCloudApigeeV1AliasRevisionConfig object.

  Enums:
    TypeValueValuesEnum:

  Fields:
    location: Location of the alias file. For example, a Google Cloud Storage
      URI.
    name: Name of the alias revision included in the keystore in the following
      format: `organizations/{org}/environments/{env}/keystores/{keystore}/ali
      ases/{alias}/revisions/{rev}`
    type: A TypeValueValuesEnum attribute.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""TypeValueValuesEnum enum type.

    Values:
      ALIAS_TYPE_UNSPECIFIED: Alias type is not specified.
      CERT: Certificate.
      KEY_CERT: Key/certificate pair.
    """
    ALIAS_TYPE_UNSPECIFIED = 0
    CERT = 1
    KEY_CERT = 2

  location = _messages.StringField(1)
  name = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class GoogleCloudApigeeV1AnalyticsConfig(_messages.Message):
  r"""Configuration for the Analytics add-on.

  Enums:
    StateValueValuesEnum: Output only. The state of the Analytics add-on.

  Fields:
    enabled: Whether the Analytics add-on is enabled.
    expireTimeMillis: Output only. Time at which the Analytics add-on expires
      in milliseconds since epoch. If unspecified, the add-on will never
      expire.
    state: Output only. The state of the Analytics add-on.
    updateTime: Output only. The latest update time.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the Analytics add-on.

    Values:
      ADDON_STATE_UNSPECIFIED: Default value.
      ENABLING: Add-on is in progress of enabling.
      ENABLED: Add-on is fully enabled and ready to use.
      DISABLING: Add-on is in progress of disabling.
      DISABLED: Add-on is fully disabled.
    """
    ADDON_STATE_UNSPECIFIED = 0
    ENABLING = 1
    ENABLED = 2
    DISABLING = 3
    DISABLED = 4

  enabled = _messages.BooleanField(1)
  expireTimeMillis = _messages.IntegerField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  updateTime = _messages.StringField(4)


class GoogleCloudApigeeV1ApiCategory(_messages.Message):
  r"""the Api category resource wrapped with response status, error_code etc.

  Fields:
    data: Details of category.
    errorCode: ID that can be used to find errors in the log files.
    message: Description of the operation.
    requestId: ID that can be used to find request details in the log files.
    status: Status of the operation.
  """

  data = _messages.MessageField('GoogleCloudApigeeV1ApiCategoryData', 1)
  errorCode = _messages.StringField(2)
  message = _messages.StringField(3)
  requestId = _messages.StringField(4)
  status = _messages.StringField(5)


class GoogleCloudApigeeV1ApiCategoryData(_messages.Message):
  r"""the Api category resource.

  Fields:
    gcpResource: GCP name of api category resource.
    id: ID of the category (a UUID).
    name: Name of the category.
    siteId: Name of the portal.
    updateTime: Time the category was last modified in milliseconds since
      epoch.
  """

  gcpResource = _messages.StringField(1)
  id = _messages.StringField(2)
  name = _messages.StringField(3)
  siteId = _messages.StringField(4)
  updateTime = _messages.IntegerField(5)


class GoogleCloudApigeeV1ApiProduct(_messages.Message):
  r"""A GoogleCloudApigeeV1ApiProduct object.

  Enums:
    QuotaCounterScopeValueValuesEnum: Scope of the quota decides how the quota
      counter gets applied and evaluate for quota violation. If the Scope is
      set as PROXY, then all the operations defined for the APIproduct that
      are associated with the same proxy will share the same quota counter set
      at the APIproduct level, making it a global counter at a proxy level. If
      the Scope is set as OPERATION, then each operations get the counter set
      at the API product dedicated, making it a local counter. Note that, the
      QuotaCounterScope applies only when an operation does not have dedicated
      quota set for itself.

  Fields:
    apiResources: Comma-separated list of API resources to be bundled in the
      API product. By default, the resource paths are mapped from the
      `proxy.pathsuffix` variable. The proxy path suffix is defined as the URI
      fragment following the ProxyEndpoint base path. For example, if the
      `apiResources` element is defined to be `/forecastrss` and the base path
      defined for the API proxy is `/weather`, then only requests to
      `/weather/forecastrss` are permitted by the API product. You can select
      a specific path, or you can select all subpaths with the following
      wildcard: - `/**`: Indicates that all sub-URIs are included. - `/*` :
      Indicates that only URIs one level down are included. By default, /
      supports the same resources as /** as well as the base path defined by
      the API proxy. For example, if the base path of the API proxy is
      `/v1/weatherapikey`, then the API product supports requests to
      `/v1/weatherapikey` and to any sub-URIs, such as
      `/v1/weatherapikey/forecastrss`, `/v1/weatherapikey/region/CA`, and so
      on. For more information, see Managing API products.
    approvalType: Flag that specifies how API keys are approved to access the
      APIs defined by the API product. If set to `manual`, the consumer key is
      generated and returned in "pending" state. In this case, the API keys
      won't work until they have been explicitly approved. If set to `auto`,
      the consumer key is generated and returned in "approved" state and can
      be used immediately. **Note:** Typically, `auto` is used to provide
      access to free or trial API products that provide limited quota or
      capabilities.
    attributes: Array of attributes that may be used to extend the default API
      product profile with customer-specific metadata. You can specify a
      maximum of 18 attributes. Use this property to specify the access level
      of the API product as either `public`, `private`, or `internal`. Only
      products marked `public` are available to developers in the Apigee
      developer portal. For example, you can set a product to `internal` while
      it is in development and then change access to `public` when it is ready
      to release on the portal. API products marked as `private` do not appear
      on the portal, but can be accessed by external developers.
    createdAt: Response only. Creation time of this environment as
      milliseconds since epoch.
    description: Description of the API product. Include key information about
      the API product that is not captured by other fields.
    displayName: Name displayed in the UI or developer portal to developers
      registering for API access.
    environments: Comma-separated list of environment names to which the API
      product is bound. Requests to environments that are not listed are
      rejected. By specifying one or more environments, you can bind the
      resources listed in the API product to a specific environment,
      preventing developers from accessing those resources through API proxies
      deployed in another environment. This setting is used, for example, to
      prevent resources associated with API proxies in `prod` from being
      accessed by API proxies deployed in `test`.
    graphqlOperationGroup: Configuration used to group Apigee proxies or
      remote services with graphQL operation name, graphQL operation type and
      quotas. This grouping allows us to precisely set quota for a particular
      combination of graphQL name and operation type for a particular proxy
      request. If graphQL name is not set, this would imply quota will be
      applied on all graphQL requests matching the operation type.
    grpcOperationGroup: Optional. Configuration used to group Apigee proxies
      with gRPC services and method names. This grouping allows us to set
      quota for a particular proxy with the gRPC service name and method. If a
      method name is not set, this implies quota and authorization are applied
      to all gRPC methods implemented by that proxy for that particular gRPC
      service.
    lastModifiedAt: Response only. Modified time of this environment as
      milliseconds since epoch.
    name: Internal name of the API product. Characters you can use in the name
      are restricted to: `A-Z0-9._\-$ %`. **Note:** The internal name cannot
      be edited when updating the API product.
    operationGroup: Configuration used to group Apigee proxies or remote
      services with resources, method types, and quotas. The resource refers
      to the resource URI (excluding the base path). With this grouping, the
      API product creator is able to fine-tune and give precise control over
      which REST methods have access to specific resources and how many calls
      can be made (using the `quota` setting). **Note:** The `api_resources`
      setting cannot be specified for both the API product and operation
      group; otherwise the call will fail.
    proxies: Comma-separated list of API proxy names to which this API product
      is bound. By specifying API proxies, you can associate resources in the
      API product with specific API proxies, preventing developers from
      accessing those resources through other API proxies. Apigee rejects
      requests to API proxies that are not listed. **Note:** The API proxy
      names must already exist in the specified environment as they will be
      validated upon creation.
    quota: Number of request messages permitted per app by this API product
      for the specified `quotaInterval` and `quotaTimeUnit`. For example, a
      `quota` of 50, for a `quotaInterval` of 12 and a `quotaTimeUnit` of
      hours means 50 requests are allowed every 12 hours.
    quotaCounterScope: Scope of the quota decides how the quota counter gets
      applied and evaluate for quota violation. If the Scope is set as PROXY,
      then all the operations defined for the APIproduct that are associated
      with the same proxy will share the same quota counter set at the
      APIproduct level, making it a global counter at a proxy level. If the
      Scope is set as OPERATION, then each operations get the counter set at
      the API product dedicated, making it a local counter. Note that, the
      QuotaCounterScope applies only when an operation does not have dedicated
      quota set for itself.
    quotaInterval: Time interval over which the number of request messages is
      calculated.
    quotaTimeUnit: Time unit defined for the `quotaInterval`. Valid values
      include `minute`, `hour`, `day`, or `month`.
    scopes: Comma-separated list of OAuth scopes that are validated at
      runtime. Apigee validates that the scopes in any access token presented
      match the scopes defined in the OAuth policy associated with the API
      product.
  """

  class QuotaCounterScopeValueValuesEnum(_messages.Enum):
    r"""Scope of the quota decides how the quota counter gets applied and
    evaluate for quota violation. If the Scope is set as PROXY, then all the
    operations defined for the APIproduct that are associated with the same
    proxy will share the same quota counter set at the APIproduct level,
    making it a global counter at a proxy level. If the Scope is set as
    OPERATION, then each operations get the counter set at the API product
    dedicated, making it a local counter. Note that, the QuotaCounterScope
    applies only when an operation does not have dedicated quota set for
    itself.

    Values:
      QUOTA_COUNTER_SCOPE_UNSPECIFIED: When quota is not explicitly defined
        for each operation(REST/GraphQL), the limits set at product level will
        be used as a local counter for quota evaluation by all the operations,
        independent of proxy association.
      PROXY: When quota is not explicitly defined for each
        operation(REST/GraphQL), set at product level will be used as a global
        counter for quota evaluation by all the operations associated with a
        particular proxy.
      OPERATION: When quota is not explicitly defined for each
        operation(REST/GraphQL), the limits set at product level will be used
        as a local counter for quota evaluation by all the operations,
        independent of proxy association. This behavior mimics the same as
        QUOTA_COUNTER_SCOPE_UNSPECIFIED.
    """
    QUOTA_COUNTER_SCOPE_UNSPECIFIED = 0
    PROXY = 1
    OPERATION = 2

  apiResources = _messages.StringField(1, repeated=True)
  approvalType = _messages.StringField(2)
  attributes = _messages.MessageField('GoogleCloudApigeeV1Attribute', 3, repeated=True)
  createdAt = _messages.IntegerField(4)
  description = _messages.StringField(5)
  displayName = _messages.StringField(6)
  environments = _messages.StringField(7, repeated=True)
  graphqlOperationGroup = _messages.MessageField('GoogleCloudApigeeV1GraphQLOperationGroup', 8)
  grpcOperationGroup = _messages.MessageField('GoogleCloudApigeeV1GrpcOperationGroup', 9)
  lastModifiedAt = _messages.IntegerField(10)
  name = _messages.StringField(11)
  operationGroup = _messages.MessageField('GoogleCloudApigeeV1OperationGroup', 12)
  proxies = _messages.StringField(13, repeated=True)
  quota = _messages.StringField(14)
  quotaCounterScope = _messages.EnumField('QuotaCounterScopeValueValuesEnum', 15)
  quotaInterval = _messages.StringField(16)
  quotaTimeUnit = _messages.StringField(17)
  scopes = _messages.StringField(18, repeated=True)


class GoogleCloudApigeeV1ApiProductRef(_messages.Message):
  r"""A GoogleCloudApigeeV1ApiProductRef object.

  Fields:
    apiproduct: Name of the API product.
    status: Status of the API product. Valid values are `approved` or
      `revoked`.
  """

  apiproduct = _messages.StringField(1)
  status = _messages.StringField(2)


class GoogleCloudApigeeV1ApiProxy(_messages.Message):
  r"""Metadata describing the API proxy

  Enums:
    ApiProxyTypeValueValuesEnum: Output only. The type of the API proxy.

  Messages:
    LabelsValue: User labels applied to this API Proxy.

  Fields:
    apiProxyType: Output only. The type of the API proxy.
    labels: User labels applied to this API Proxy.
    latestRevisionId: Output only. The id of the most recently created
      revision for this api proxy.
    metaData: Output only. Metadata describing the API proxy.
    name: Output only. Name of the API proxy.
    readOnly: Output only. Whether this proxy is read-only. A read-only proxy
      cannot have new revisions created through calls to
      CreateApiProxyRevision. A proxy is read-only if it was generated by an
      archive.
    revision: Output only. List of revisions defined for the API proxy.
  """

  class ApiProxyTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the API proxy.

    Values:
      API_PROXY_TYPE_UNSPECIFIED: API proxy type not specified.
      PROGRAMMABLE: Programmable API Proxies enable you to develop APIs with
        highly flexible behavior using bundled policy configuration and one or
        more programming languages to describe complex sequential and/or
        conditional flows of logic.
      CONFIGURABLE: Configurable API Proxies enable you to develop efficient
        APIs using simple configuration while complex execution control flow
        logic is handled by Apigee. This type only works with the ARCHIVE
        deployment type and cannot be combined with the PROXY deployment type.
    """
    API_PROXY_TYPE_UNSPECIFIED = 0
    PROGRAMMABLE = 1
    CONFIGURABLE = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User labels applied to this API Proxy.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  apiProxyType = _messages.EnumField('ApiProxyTypeValueValuesEnum', 1)
  labels = _messages.MessageField('LabelsValue', 2)
  latestRevisionId = _messages.StringField(3)
  metaData = _messages.MessageField('GoogleCloudApigeeV1EntityMetadata', 4)
  name = _messages.StringField(5)
  readOnly = _messages.BooleanField(6)
  revision = _messages.StringField(7, repeated=True)


class GoogleCloudApigeeV1ApiProxyRevision(_messages.Message):
  r"""API proxy revision.

  Messages:
    EntityMetaDataAsPropertiesValue: Metadata describing the API proxy
      revision as a key-value map.

  Fields:
    archive: Output only. The archive that generated this proxy revision. This
      field is only present on proxy revisions that were generated by an
      archive. Proxies generated by archives cannot be updated, deleted, or
      deployed to other environments. Format:
      `organizations/*/environments/*/archiveDeployments/*`
    basepaths: Base URL of the API proxy.
    configurationVersion: Version of the API proxy configuration schema to
      which the API proxy conforms. Currently, the only supported value is 4.0
      (`majorVersion.minorVersion`). This setting may be used in the future to
      track the evolution of the API proxy format.
    contextInfo: Revision number, app name, and organization for the API
      proxy.
    createdAt: Time that the API proxy revision was created in milliseconds
      since epoch.
    description: Description of the API proxy revision.
    displayName: Human-readable name of the API proxy.
    entityMetaDataAsProperties: Metadata describing the API proxy revision as
      a key-value map.
    hasExtensiblePolicy: Output only. This field will be marked as true if
      revision contains any policies marked as extensible.
    integrationEndpoints: List of IntegrationEndpoints in the '/integration-
      endpoints' directory of the API proxy. This is a 'manifest' setting
      designed to provide visibility into the contents of the API proxy.
    lastModifiedAt: Time that the API proxy revision was last modified in
      milliseconds since epoch.
    name: Name of the API proxy.
    policies: List of policy names included in the API proxy revision..
    proxies: List of proxy names included in the API proxy revision.
    proxyEndpoints: List of ProxyEndpoints in the `/proxies` directory of the
      API proxy. Typically, this element is included only when the API proxy
      was created using the Edge UI. This is a 'manifest' setting designed to
      provide visibility into the contents of the API proxy.
    resourceFiles: List of resource files included in the API proxy revision.
    resources: List of the resources included in the API proxy revision
      formatted as "{type}://{name}".
    revision: API proxy revision.
    sharedFlows: List of the shared flows included in the API proxy revision.
    spec: OpenAPI Specification that is associated with the API proxy. The
      value is set to a URL or to a path in the specification store.
    targetEndpoints: List of TargetEndpoints in the `/targets` directory of
      the API proxy. Typically, this element is included only when the API
      proxy was created using the Edge UI. This is a 'manifest' setting
      designed to provide visibility into the contents of the API proxy.
    targetServers: List of TargetServers referenced in any TargetEndpoint in
      the API proxy. Typically, you will see this element only when the API
      proxy was created using the Edge UI. This is a 'manifest' setting
      designed to provide visibility into the contents of the API proxy.
    targets: List of the targets included in the API proxy revision.
    teams: List of the teams included in the API proxy revision.
    type: Type. Set to `Application`. Maintained for compatibility with the
      Apigee Edge API.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EntityMetaDataAsPropertiesValue(_messages.Message):
    r"""Metadata describing the API proxy revision as a key-value map.

    Messages:
      AdditionalProperty: An additional property for a
        EntityMetaDataAsPropertiesValue object.

    Fields:
      additionalProperties: Additional properties of type
        EntityMetaDataAsPropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EntityMetaDataAsPropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  archive = _messages.StringField(1)
  basepaths = _messages.StringField(2, repeated=True)
  configurationVersion = _messages.MessageField('GoogleCloudApigeeV1ConfigVersion', 3)
  contextInfo = _messages.StringField(4)
  createdAt = _messages.IntegerField(5)
  description = _messages.StringField(6)
  displayName = _messages.StringField(7)
  entityMetaDataAsProperties = _messages.MessageField('EntityMetaDataAsPropertiesValue', 8)
  hasExtensiblePolicy = _messages.BooleanField(9)
  integrationEndpoints = _messages.StringField(10, repeated=True)
  lastModifiedAt = _messages.IntegerField(11)
  name = _messages.StringField(12)
  policies = _messages.StringField(13, repeated=True)
  proxies = _messages.StringField(14, repeated=True)
  proxyEndpoints = _messages.StringField(15, repeated=True)
  resourceFiles = _messages.MessageField('GoogleCloudApigeeV1ResourceFiles', 16)
  resources = _messages.StringField(17, repeated=True)
  revision = _messages.StringField(18)
  sharedFlows = _messages.StringField(19, repeated=True)
  spec = _messages.StringField(20)
  targetEndpoints = _messages.StringField(21, repeated=True)
  targetServers = _messages.StringField(22, repeated=True)
  targets = _messages.StringField(23, repeated=True)
  teams = _messages.StringField(24, repeated=True)
  type = _messages.StringField(25)


class GoogleCloudApigeeV1ApiSecurityConfig(_messages.Message):
  r"""Configurations of the API Security add-on.

  Enums:
    StateValueValuesEnum: Output only. The state of the API Security add-on.

  Fields:
    enabled: Flag that specifies whether the API security add-on is enabled.
    expiresAt: Output only. Time at which the API Security add-on expires in
      in milliseconds since epoch. If unspecified, the add-on will never
      expire.
    state: Output only. The state of the API Security add-on.
    updateTime: Output only. The latest update time.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The state of the API Security add-on.

    Values:
      ADDON_STATE_UNSPECIFIED: Default value.
      ENABLING: Add-on is in progress of enabling.
      ENABLED: Add-on is fully enabled and ready to use.
      DISABLING: Add-on is in progress of disabling.
      DISABLED: Add-on is fully disabled.
    """
    ADDON_STATE_UNSPECIFIED = 0
    ENABLING = 1
    ENABLED = 2
    DISABLING = 3
    DISABLED = 4

  enabled = _messages.BooleanField(1)
  expiresAt = _messages.IntegerField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  updateTime = _messages.StringField(4)


class GoogleCloudApigeeV1ApiSecurityRuntimeConfig(_messages.Message):
  r"""Response for
  GetApiSecurityRuntimeConfig[EnvironmentService.GetApiSecurityRuntimeConfig].

  Fields:
    location: A list of up to 5 Cloud Storage Blobs that contain
      SecurityActions.
    name: Name of the environment API Security Runtime configuration resource.
      Format:
      `organizations/{org}/environments/{env}/apiSecurityRuntimeConfig`
    revisionId: Revision ID of the API Security Runtime configuration. The
      higher the value, the more recently the configuration was deployed.
    uid: Unique ID for the API Security Runtime configuration. The ID will
      only change if the environment is deleted and recreated.
    updateTime: Time that the API Security Runtime configuration was updated.
  """

  location = _messages.StringField(1, repeated=True)
  name = _messages.StringField(2)
  revisionId = _messages.IntegerField(3)
  uid = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class GoogleCloudApigeeV1App(_messages.Message):
  r"""A GoogleCloudApigeeV1App object.

  Fields:
    apiProducts: List of API products associated with the app.
    appGroup: Name of the AppGroup
    appId: ID of the app.
    attributes: List of attributes.
    callbackUrl: Callback URL used by OAuth 2.0 authorization servers to
      communicate authorization codes back to apps.
    companyName: Name of the company that owns the app.
    createdAt: Output only. Unix time when the app was created.
    credentials: Output only. Set of credentials for the app. Credentials are
      API key/secret pairs associated with API products.
    developerEmail: Email of the developer.
    developerId: ID of the developer.
    keyExpiresIn: Duration, in milliseconds, of the consumer key that will be
      generated for the app. The default value, -1, indicates an infinite
      validity period. Once set, the expiration can't be updated. json key:
      keyExpiresIn
    lastModifiedAt: Output only. Last modified time as milliseconds since
      epoch.
    name: Name of the app.
    scopes: Scopes to apply to the app. The specified scope names must already
      exist on the API product that you associate with the app.
    status: Status of the credential.
  """

  apiProducts = _messages.MessageField('GoogleCloudApigeeV1ApiProductRef', 1, repeated=True)
  appGroup = _messages.StringField(2)
  appId = _messages.StringField(3)
  attributes = _messages.MessageField('GoogleCloudApigeeV1Attribute', 4, repeated=True)
  callbackUrl = _messages.StringField(5)
  companyName = _messages.StringField(6)
  createdAt = _messages.IntegerField(7)
  credentials = _messages.MessageField('GoogleCloudApigeeV1Credential', 8, repeated=True)
  developerEmail = _messages.StringField(9)
  developerId = _messages.StringField(10)
  keyExpiresIn = _messages.IntegerField(11)
  lastModifiedAt = _messages.IntegerField(12)
  name = _messages.StringField(13)
  scopes = _messages.StringField(14, repeated=True)
  status = _messages.StringField(15)


class GoogleCloudApigeeV1AppGroup(_messages.Message):
  r"""AppGroup contains the request/response fields representing the logical
  grouping of apps. Note that appgroup_id, create_time and update_time cannot
  be changed by the user, and gets updated by the system. The name and the
  organization once provided cannot be edited subsequently.

  Fields:
    appGroupId: Output only. Internal identifier that cannot be edited
    attributes: A list of attributes
    channelId: channel identifier identifies the owner maintaing this
      grouping.
    channelUri: A reference to the associated storefront/marketplace.
    createdAt: Output only. Created time as milliseconds since epoch.
    displayName: app group name displayed in the UI
    lastModifiedAt: Output only. Modified time as milliseconds since epoch.
    name: Immutable. Name of the AppGroup. Characters you can use in the name
      are restricted to: A-Z0-9._\-$ %.
    organization: Immutable. the org the app group is created
    status: Valid values are `active` or `inactive`. Note that the status of
      the AppGroup should be updated via UpdateAppGroupRequest by setting the
      action as `active` or `inactive`.
  """

  appGroupId = _messages.StringField(1)
  attributes = _messages.MessageField('GoogleCloudApigeeV1Attribute', 2, repeated=True)
  channelId = _messages.StringField(3)
  channelUri = _messages.StringField(4)
  createdAt = _messages.IntegerField(5)
  displayName = _messages.StringField(6)
  lastModifiedAt = _messages.IntegerField(7)
  name = _messages.StringField(8)
  organization = _messages.StringField(9)
  status = _messages.StringField(10)


class GoogleCloudApigeeV1AppGroupApp(_messages.Message):
  r"""Response for [GetAppGroupApp].[AppGroupApps.GetAppGroupApp],
  [CreateAppGroupAppRequest].[AppGroupApp.CreateAppGroupAppRequest] and
  [DeleteAppGroupApp].[AppGroupApp.DeleteAppGroupApp]

  Fields:
    apiProducts: List of API products associated with the AppGroup app.
    appGroup: Immutable. Name of the parent AppGroup whose resource name
      format is of syntax (organizations/*/appgroups/*).
    appId: Immutable. ID of the AppGroup app.
    attributes: List of attributes for the AppGroup app.
    callbackUrl: Callback URL used by OAuth 2.0 authorization servers to
      communicate authorization codes back to AppGroup apps.
    createdAt: Output only. Time the AppGroup app was created in milliseconds
      since epoch.
    credentials: Output only. Set of credentials for the AppGroup app
      consisting of the consumer key/secret pairs associated with the API
      products.
    keyExpiresIn: Immutable. Expiration time, in seconds, for the consumer key
      that is generated for the AppGroup app. If not set or left to the
      default value of `-1`, the API key never expires. The expiration time
      can't be updated after it is set.
    lastModifiedAt: Output only. Time the AppGroup app was modified in
      milliseconds since epoch.
    name: Immutable. Name of the AppGroup app whose resource name format is of
      syntax (organizations/*/appgroups/*/apps/*).
    scopes: Scopes to apply to the AppGroup app. The specified scopes must
      already exist for the API product that you associate with the AppGroup
      app.
    status: Status of the App. Valid values include `approved` or `revoked`.
  """

  apiProducts = _messages.StringField(1, repeated=True)
  appGroup = _messages.StringField(2)
  appId = _messages.StringField(3)
  attributes = _messages.MessageField('GoogleCloudApigeeV1Attribute', 4, repeated=True)
  callbackUrl = _messages.StringField(5)
  createdAt = _messages.IntegerField(6)
  credentials = _messages.MessageField('GoogleCloudApigeeV1Credential', 7, repeated=True)
  keyExpiresIn = _messages.IntegerField(8)
  lastModifiedAt = _messages.IntegerField(9)
  name = _messages.StringField(10)
  scopes = _messages.StringField(11, repeated=True)
  status = _messages.StringField(12)


class GoogleCloudApigeeV1AppGroupAppKey(_messages.Message):
  r"""AppGroupAppKey contains all the information associated with the
  credentials.

  Fields:
    apiProducts: Output only. List of API products and its status for which
      the credential can be used. **Note**: Use
      UpdateAppGroupAppKeyApiProductRequest API to make the association after
      the consumer key and secret are created.
    attributes: List of attributes associated with the credential.
    consumerKey: Immutable. Consumer key.
    consumerSecret: Secret key.
    expiresAt: Output only. Time the AppGroup app expires in milliseconds
      since epoch.
    expiresInSeconds: Immutable. Expiration time, in seconds, for the consumer
      key. If not set or left to the default value of `-1`, the API key never
      expires. The expiration time can't be updated after it is set.
    issuedAt: Output only. Time the AppGroup app was created in milliseconds
      since epoch.
    scopes: Scopes to apply to the app. The specified scope names must already
      be defined for the API product that you associate with the app.
    status: Status of the credential. Valid values include `approved` or
      `revoked`.
  """

  apiProducts = _messages.MessageField('GoogleCloudApigeeV1APIProductAssociation', 1, repeated=True)
  attributes = _messages.MessageField('GoogleCloudApigeeV1Attribute', 2, repeated=True)
  consumerKey = _messages.StringField(3)
  consumerSecret = _messages.StringField(4)
  expiresAt = _messages.IntegerField(5)
  expiresInSeconds = _messages.IntegerField(6)
  issuedAt = _messages.IntegerField(7)
  scopes = _messages.StringField(8, repeated=True)
  status = _messages.StringField(9)


class GoogleCloudApigeeV1ArchiveDeployment(_messages.Message):
  r"""Archive Deployment information.

  Messages:
    LabelsValue: User-supplied key-value pairs used to organize
      ArchiveDeployments. Label keys must be between 1 and 63 characters long,
      have a UTF-8 encoding of maximum 128 bytes, and must conform to the
      following PCRE regular expression: \p{Ll}\p{Lo}{0,62} Label values must
      be between 1 and 63 characters long, have a UTF-8 encoding of maximum
      128 bytes, and must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.

  Fields:
    createdAt: Output only. The time at which the Archive Deployment was
      created in milliseconds since the epoch.
    gcsUri: Input only. The Google Cloud Storage signed URL returned from
      GenerateUploadUrl and used to upload the Archive zip file.
    labels: User-supplied key-value pairs used to organize ArchiveDeployments.
      Label keys must be between 1 and 63 characters long, have a UTF-8
      encoding of maximum 128 bytes, and must conform to the following PCRE
      regular expression: \p{Ll}\p{Lo}{0,62} Label values must be between 1
      and 63 characters long, have a UTF-8 encoding of maximum 128 bytes, and
      must conform to the following PCRE regular expression:
      [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated
      with a given store.
    name: Name of the Archive Deployment in the following format:
      `organizations/{org}/environments/{env}/archiveDeployments/{id}`.
    operation: Output only. A reference to the LRO that created this Archive
      Deployment in the following format:
      `organizations/{org}/operations/{id}`
    updatedAt: Output only. The time at which the Archive Deployment was
      updated in milliseconds since the epoch.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""User-supplied key-value pairs used to organize ArchiveDeployments.
    Label keys must be between 1 and 63 characters long, have a UTF-8 encoding
    of maximum 128 bytes, and must conform to the following PCRE regular
    expression: \p{Ll}\p{Lo}{0,62} Label values must be between 1 and 63
    characters long, have a UTF-8 encoding of maximum 128 bytes, and must
    conform to the following PCRE regular expression:
    [\p{Ll}\p{Lo}\p{N}_-]{0,63} No more than 64 labels can be associated with
    a given store.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createdAt = _messages.IntegerField(1)
  gcsUri = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  operation = _messages.StringField(5)
  updatedAt = _messages.IntegerField(6)


class GoogleCloudApigeeV1AsyncQuery(_messages.Message):
  r"""A GoogleCloudApigeeV1AsyncQuery object.

  Fields:
    created: Creation time of the query.
    envgroupHostname: Hostname is available only when query is executed at
      host level.
    error: Error is set when query fails.
    executionTime: ExecutionTime is available only after the query is
      completed.
    name: Asynchronous Query Name.
    queryParams: Contains information like metrics, dimenstions etc of the
      AsyncQuery.
    reportDefinitionId: Asynchronous Report ID.
    result: Result is available only after the query is completed.
    resultFileSize: ResultFileSize is available only after the query is
      completed.
    resultRows: ResultRows is available only after the query is completed.
    self: Self link of the query. Example: `/organizations/myorg/environments/
      myenv/queries/9cfc0d85-0f30-46d6-ae6f-318d0cb961bd` or following format
      if query is running at host level:
      `/organizations/myorg/hostQueries/9cfc0d85-0f30-46d6-ae6f-318d0cb961bd`
    state: Query state could be "enqueued", "running", "completed", "failed".
    updated: Last updated timestamp for the query.
  """

  created = _messages.StringField(1)
  envgroupHostname = _messages.StringField(2)
  error = _messages.StringField(3)
  executionTime = _messages.StringField(4)
  name = _messages.StringField(5)
  queryParams = _messages.MessageField('GoogleCloudApigeeV1QueryMetadata', 6)
  reportDefinitionId = _messages.StringField(7)
  result = _messages.MessageField('GoogleCloudApigeeV1AsyncQueryResult', 8)
  resultFileSize = _messages.StringField(9)
  resultRows = _messages.IntegerField(10)
  self = _messages.StringField(11)
  state = _messages.StringField(12)
  updated = _messages.StringField(13)


class GoogleCloudApigeeV1AsyncQueryResult(_messages.Message):
  r"""A GoogleCloudApigeeV1AsyncQueryResult object.

  Fields:
    expires: Query result will be unaccessable after this time.
    self: Self link of the query results. Example: `/organizations/myorg/envir
      onments/myenv/queries/9cfc0d85-0f30-46d6-ae6f-318d0cb961bd/result` or
      following format if query is running at host level: `/organizations/myor
      g/hostQueries/9cfc0d85-0f30-46d6-ae6f-318d0cb961bd/result`
  """

  expires = _messages.StringField(1)
  self = _messages.StringField(2)


class GoogleCloudApigeeV1AsyncQueryResultView(_messages.Message):
  r"""A GoogleCloudApigeeV1AsyncQueryResultView object.

  Fields:
    code: Error code when there is a failure.
    error: Error message when there is a failure.
    metadata: Metadata contains information like metrics, dimenstions etc of
      the AsyncQuery.
    rows: Rows of query result. Each row is a JSON object. Example:
      {sum(message_count): 1, developer_app: "(not set)",...}
    state: State of retrieving ResultView.
  """

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  error = _messages.StringField(2)
  metadata = _messages.MessageField('GoogleCloudApigeeV1QueryMetadata', 3)
  rows = _messages.MessageField('extra_types.JsonValue', 4, repeated=True)
  state = _messages.StringField(5)


class GoogleCloudApigeeV1Attribute(_messages.Message):
  r"""Key-value pair to store extra metadata.

  Fields:
    name: API key of the attribute.
    value: Value of the attribute.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleCloudApigeeV1Attributes(_messages.Message):
  r"""A GoogleCloudApigeeV1Attributes object.

  Fields:
    attribute: List of attributes.
  """

  attribute = _messages.MessageField('GoogleCloudApigeeV1Attribute', 1, repeated=True)


class GoogleCloudApigeeV1CanaryEvaluation(_messages.Message):
  r"""CanaryEvaluation represents the canary analysis between two versions of
  the runtime that is serving requests.

  Enums:
    StateValueValuesEnum: Output only. The current state of the canary
      evaluation.
    VerdictValueValuesEnum: Output only. The resulting verdict of the canary
      evaluations: NONE, PASS, or FAIL.

  Fields:
    control: Required. The stable version that is serving requests.
    createTime: Output only. Create time of the canary evaluation.
    endTime: Required. End time for the evaluation's analysis.
    metricLabels: Required. Labels used to filter the metrics used for a
      canary evaluation.
    name: Output only. Name of the canary evalution.
    startTime: Required. Start time for the canary evaluation's analysis.
    state: Output only. The current state of the canary evaluation.
    treatment: Required. The newer version that is serving requests.
    verdict: Output only. The resulting verdict of the canary evaluations:
      NONE, PASS, or FAIL.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the canary evaluation.

    Values:
      STATE_UNSPECIFIED: No state has been specified.
      RUNNING: The canary evaluation is still in progress.
      SUCCEEDED: The canary evaluation has finished.
    """
    STATE_UNSPECIFIED = 0
    RUNNING = 1
    SUCCEEDED = 2

  class VerdictValueValuesEnum(_messages.Enum):
    r"""Output only. The resulting verdict of the canary evaluations: NONE,
    PASS, or FAIL.

    Values:
      VERDICT_UNSPECIFIED: Verdict is not available yet.
      NONE: No verdict reached.
      FAIL: Evaluation is not good.
      PASS: Evaluation is good.
    """
    VERDICT_UNSPECIFIED = 0
    NONE = 1
    FAIL = 2
    PASS = 3

  control = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  metricLabels = _messages.MessageField('GoogleCloudApigeeV1CanaryEvaluationMetricLabels', 4)
  name = _messages.StringField(5)
  startTime = _messages.StringField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  treatment = _messages.StringField(8)
  verdict = _messages.EnumField('VerdictValueValuesEnum', 9)


class GoogleCloudApigeeV1CanaryEvaluationMetricLabels(_messages.Message):
  r"""Labels that can be used to filter Apigee metrics.

  Fields:
    env: The environment ID associated with the metrics.
    instance_id: Required. The instance ID associated with the metrics. In
      Apigee Hybrid, the value is configured during installation.
    location: Required. The location associated with the metrics.
  """

  env = _messages.StringField(1)
  instance_id = _messages.StringField(2)
  location = _messages.StringField(3)


class GoogleCloudApigeeV1CertInfo(_messages.Message):
  r"""X.509 certificate as defined in RFC 5280.

  Fields:
    basicConstraints: X.509 basic constraints extension.
    expiryDate: X.509 `notAfter` validity period in milliseconds since epoch.
    isValid: Flag that specifies whether the certificate is valid. Flag is set
      to `Yes` if the certificate is valid, `No` if expired, or `Not yet` if
      not yet valid.
    issuer: X.509 issuer.
    publicKey: Public key component of the X.509 subject public key info.
    serialNumber: X.509 serial number.
    sigAlgName: X.509 signatureAlgorithm.
    subject: X.509 subject.
    subjectAlternativeNames: X.509 subject alternative names (SANs) extension.
    validFrom: X.509 `notBefore` validity period in milliseconds since epoch.
    version: X.509 version.
  """

  basicConstraints = _messages.StringField(1)
  expiryDate = _messages.IntegerField(2)
  isValid = _messages.StringField(3)
  issuer = _messages.StringField(4)
  publicKey = _messages.StringField(5)
  serialNumber = _messages.StringField(6)
  sigAlgName = _messages.StringField(7)
  subject = _messages.StringField(8)
  subjectAlternativeNames = _messages.StringField(9, repeated=True)
  validFrom = _messages.IntegerField(10)
  version = _messages.IntegerField(11, variant=_messages.Variant.INT32)


class GoogleCloudApigeeV1Certificate(_messages.Message):
  r"""A GoogleCloudApigeeV1Certificate object.

  Fields:
    certInfo: Chain of certificates under this name.
  """

  certInfo = _messages.MessageField('GoogleCloudApigeeV1CertInfo', 1, repeated=True)


class GoogleCloudApigeeV1CommonNameConfig(_messages.Message):
  r"""A GoogleCloudApigeeV1CommonNameConfig object.

  Fields:
    matchWildCards: A boolean attribute.
    name: A string attribute.
  """

  matchWildCards = _messages.BooleanField(1)
  name = _messages.StringField(2)


class GoogleCloudApigeeV1ComputeEnvironmentScoresRequest(_messages.Message):
  r"""Request for ComputeEnvironmentScores.

  Fields:
    filters: Optional. Filters are used to filter scored components. Return
      all the components if no filter is mentioned. Example: [{ "scorePath":
      "/org@myorg/envgroup@myenvgroup/env@myenv/proxies/proxy@myproxy/source"
      }, { "scorePath":
      "/org@myorg/envgroup@myenvgroup/env@myenv/proxies/proxy@myproxy/target",
      }] This will return components with path:
      "/org@myorg/envgroup@myenvgroup/env@myenv/proxies/proxy@myproxy/source"
      OR
      "/org@myorg/envgroup@myenvgroup/env@myenv/proxies/proxy@myproxy/target"
    pageSize: Optional. The maximum number of subcomponents to be returned in
      a single page. The service may return fewer than this value. If
      unspecified, at most 100 subcomponents will be returned in a single
      page.
    pageToken: Optional. A token that can be sent as `page_token` to retrieve
      the next page. If this field is omitted, there are no subsequent pages.
    timeRange: Required. Time range for score calculation. At most 14 days of
      scores will be returned.
  """

  filters = _messages.MessageField('GoogleCloudApigeeV1ComputeEnvironmentScoresRequestFilter', 1, repeated=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  timeRange = _messages.MessageField('GoogleTypeInterval', 4)


class GoogleCloudApigeeV1ComputeEnvironmentScoresRequestFilter(_messages.Message):
  r"""Filter scores by component path. Used custom filter instead of AIP-160
  as the use cases are highly constrained and predictable.

  Fields:
    scorePath: Optional. Return scores for this component. Example:
      "/org@myorg/envgroup@myenvgroup/env@myenv/proxies/proxy@myproxy/source"
  """

  scorePath = _messages.StringField(1)


class GoogleCloudApigeeV1ComputeEnvironmentScoresResponse(_messages.Message):
  r"""Response for ComputeEnvironmentScores.

  Fields:
    nextPageToken: A page token, received from a previous `ComputeScore` call.
      Provide this to retrieve the subsequent page.
    scores: List of scores. One score per day.
  """

  nextPageToken = _messages.StringField(1)
  scores = _messages.MessageField('GoogleCloudApigeeV1Score', 2, repeated=True)


class GoogleCloudApigeeV1ConfigVersion(_messages.Message):
  r"""Version of the API proxy configuration schema. Currently, only 4.0 is
  supported.

  Fields:
    majorVersion: Major version of the API proxy configuration schema.
    minorVersion: Minor version of the API proxy configuration schema.
  """

  majorVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minorVersion = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GoogleCloudApigeeV1ConnectorsPlatformConfig(_messages.Message):
  r"""Configuration for the Connectors Platform add-on.

  Fields:
    enabled: Flag that specifies whether the Connectors Platform add-on is
      enabled.
    expiresAt: Output only. Time at which the Connectors Platform add-on
      expires in milliseconds since epoch. If unspecified, the add-on will
      never expire.
  """

  enabled = _messages.BooleanField(1)
  expiresAt = _messages.IntegerField(2)


class GoogleCloudApigeeV1ControlPlaneAccess(_messages.Message):
  r"""ControlPlaneAccess is the request body and response body of
  UpdateControlPlaneAccess. and the response body of GetControlPlaneAccess.
  The input identities contains an array of service accounts to grant access
  to the respective control plane resource, with each service account
  specified using the following format: `serviceAccount:`***service-account-
  name***. The ***service-account-name*** is formatted like an email address.
  For example: `my-control-plane-
  service_account@my_project_id.iam.gserviceaccount.com` You might specify
  multiple service accounts, for example, if you have multiple environments
  and wish to assign a unique service account to each one.

  Fields:
    loggerIdentities: Array of service accounts to grant access to control
      plane resources (for the Logger component).
    name: The resource name of the ControlPlaneAccess. Format:
      "organizations/{org}/controlPlaneAccess"
    synchronizerIdentities: Required. Array of service accounts to grant
      access to control plane resources (for the Synchronizer component). The
      service accounts must have **Apigee Synchronizer Manager** role. See
      also [Create service
      accounts](https://cloud.google.com/apigee/docs/hybrid/latest/sa-
      about#create-the-service-accounts).
    udcaIdentities: Required. Array of service accounts to grant access to
      control plane resources (for the UDCA component).
  """

  loggerIdentities = _messages.StringField(1, repeated=True)
  name = _messages.StringField(2)
  synchronizerIdentities = _messages.StringField(3, repeated=True)
  udcaIdentities = _messages.StringField(4, repeated=True)


class GoogleCloudApigeeV1Credential(_messages.Message):
  r"""A GoogleCloudApigeeV1Credential object.

  Fields:
    apiProducts: List of API products this credential can be used for.
    attributes: List of attributes associated with this credential.
    consumerKey: Consumer key.
    consumerSecret: Secret key.
    expiresAt: Time the credential will expire in milliseconds since epoch.
    issuedAt: Time the credential was issued in milliseconds since epoch.
    scopes: List of scopes to apply to the app. Specified scopes must already
      exist on the API product that you associate with the app.
    status: Status of the credential. Valid values include `approved` or
      `revoked`.
  """

  apiProducts = _messages.MessageField('GoogleCloudApigeeV1ApiProductRef', 1, repeated=True)
  attributes = _messages.MessageField('GoogleCloudApigeeV1Attribute', 2, repeated=True)
  consumerKey = _messages.StringField(3)
  consumerSecret = _messages.StringField(4)
  expiresAt = _messages.IntegerField(5)
  issuedAt = _messages.IntegerField(6)
  scopes = _messages.StringField(7, repeated=True)
  status = _messages.StringField(8)


class GoogleCloudApigeeV1CreditDeveloperBalanceRequest(_messages.Message):
  r"""Request for CreditDeveloperBalance.

  Fields:
    transactionAmount: The amount of money to be credited. The wallet
      corresponding to the currency specified within `transaction_amount` will
      be updated. For example, if you specified `currency_code` within
      `transaction_amount` as "USD", then the amount would be added to the
      wallet which has the "USD" currency or if no such wallet exists, a new
      wallet will be created with the "USD" currency.
    transactionId: Each transaction_id uniquely identifies a credit balance
      request. If multiple requests are received with the same transaction_id,
      only one of them will be considered.
  """

  transactionAmount = _messages.MessageField('GoogleTypeMoney', 1)
  transactionId = _messages.StringField(2)


class GoogleCloudApigeeV1CustomReport(_messages.Message):
  r"""A GoogleCloudApigeeV1CustomReport object.

  Fields:
    chartType: This field contains the chart type for the report
    comments: Legacy field: not used. This field contains a list of comments
      associated with custom report
    createdAt: Output only. Unix time when the app was created json key:
      createdAt
    dimensions: This contains the list of dimensions for the report
    displayName: This is the display name for the report
    environment: Output only. Environment name
    filter: This field contains the filter expression
    fromTime: Legacy field: not used. Contains the from time for the report
    lastModifiedAt: Output only. Modified time of this entity as milliseconds
      since epoch. json key: lastModifiedAt
    lastViewedAt: Output only. Last viewed time of this entity as milliseconds
      since epoch
    limit: Legacy field: not used This field contains the limit for the result
      retrieved
    metrics: Required. This contains the list of metrics
    name: Required. Unique identifier for the report T his is a legacy field
      used to encode custom report unique id
    offset: Legacy field: not used. This field contains the offset for the
      data
    organization: Output only. Organization name
    properties: This field contains report properties such as ui metadata etc.
    sortByCols: Legacy field: not used much. Contains the list of sort by
      columns
    sortOrder: Legacy field: not used much. Contains the sort order for the
      sort columns
    tags: Legacy field: not used. This field contains a list of tags
      associated with custom report
    timeUnit: This field contains the time unit of aggregation for the report
    toTime: Legacy field: not used. Contains the end time for the report
    topk: Legacy field: not used. This field contains the top k parameter
      value for restricting the result
  """

  chartType = _messages.StringField(1)
  comments = _messages.StringField(2, repeated=True)
  createdAt = _messages.IntegerField(3)
  dimensions = _messages.StringField(4, repeated=True)
  displayName = _messages.StringField(5)
  environment = _messages.StringField(6)
  filter = _messages.StringField(7)
  fromTime = _messages.StringField(8)
  lastModifiedAt = _messages.IntegerField(9)
  lastViewedAt = _messages.IntegerField(10)
  limit = _messages.StringField(11)
  metrics = _messages.MessageField('GoogleCloudApigeeV1CustomReportMetric', 12, repeated=True)
  name = _messages.StringField(13)
  offset = _messages.StringField(14)
  organization = _messages.StringField(15)
  properties = _messages.MessageField('GoogleCloudApigeeV1ReportProperty', 16, repeated=True)
  sortByCols = _messages.StringField(17, repeated=True)
  sortOrder = _messages.StringField(18)
  tags = _messages.StringField(19, repeated=True)
  timeUnit = _messages.StringField(20)
  toTime = _messages.StringField(21)
  topk = _messages.StringField(22)


class GoogleCloudApigeeV1CustomReportMetric(_messages.Message):
  r"""This encapsulates a metric property of the form sum(message_count) where
  name is message_count and function is sum

  Fields:
    function: aggregate function
    name: name of the metric
  """

  function = _messages.StringField(1)
  name = _messages.StringField(2)


class GoogleCloudApigeeV1DataCollector(_messages.Message):
  r"""Data collector configuration.

  Enums:
    TypeValueValuesEnum: Immutable. The type of data this data collector will
      collect.

  Fields:
    createdAt: Output only. The time at which the data collector was created
      in milliseconds since the epoch.
    description: A description of the data collector.
    lastModifiedAt: Output only. The time at which the Data Collector was last
      updated in milliseconds since the epoch.
    name: ID of the data collector. Must begin with `dc_`.
    type: Immutable. The type of data this data collector will collect.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Immutable. The type of data this data collector will collect.

    Values:
      TYPE_UNSPECIFIED: For future compatibility.
      INTEGER: For integer values.
      FLOAT: For float values.
      STRING: For string values.
      BOOLEAN: For boolean values.
      DATETIME: For datetime values.
    """
    TYPE_UNSPECIFIED = 0
    INTEGER = 1
    FLOAT = 2
    STRING = 3
    BOOLEAN = 4
    DATETIME = 5

  createdAt = _messages.IntegerField(1)
  description = _messages.StringField(2)
  lastModifiedAt = _messages.IntegerField(3)
  name = _messages.StringField(4)
  type = _messages.EnumField('TypeValueValuesEnum', 5)


class GoogleCloudApigeeV1DataCollectorConfig(_messages.Message):
  r"""Data collector and its configuration.

  Enums:
    TypeValueValuesEnum: Data type accepted by the data collector.

  Fields:
    name: Name of the data collector in the following format:
      `organizations/{org}/datacollectors/{datacollector}`
    type: Data type accepted by the data collector.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Data type accepted by the data collector.

    Values:
      TYPE_UNSPECIFIED: For future compatibility.
      INTEGER: For integer values.
      FLOAT: For float values.
      STRING: For string values.
      BOOLEAN: For boolean values.
      DATETIME: For datetime values.
    """
    TYPE_UNSPECIFIED = 0
    INTEGER = 1
    FLOAT = 2
    STRING = 3
    BOOLEAN = 4
    DATETIME = 5

  name = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)


class GoogleCloudApigeeV1Datastore(_messages.Message):
  r"""The data store defines the connection to export data repository (Cloud
  Storage, BigQuery), including the credentials used to access the data
  repository.

  Fields:
    createTime: Output only. Datastore create time, in milliseconds since the
      epoch of 1970-01-01T00:00:00Z
    datastoreConfig: Datastore Configurations.
    displayName: Required. Display name in UI
    lastUpdateTime: Output only. Datastore last update time, in milliseconds
      since the epoch of 1970-01-01T00:00:00Z
    org: Output only. Organization that the datastore belongs to
    self: Output only. Resource link of Datastore. Example:
      `/organizations/{org}/analytics/datastores/{uuid}`
    targetType: Destination storage type. Supported types `gcs` or `bigquery`.
  """

  createTime = _messages.IntegerField(1)
  datastoreConfig = _messages.MessageField('GoogleCloudApigeeV1DatastoreConfig', 2)
  displayName = _messages.StringField(3)
  lastUpdateTime = _messages.IntegerField(4)
  org = _messages.StringField(5)
  self = _messages.StringField(6)
  targetType = _messages.StringField(7)


class GoogleCloudApigeeV1DatastoreConfig(_messages.Message):
  r"""Configuration detail for datastore

  Fields:
    bucketName: Name of the Cloud Storage bucket. Required for `gcs`
      target_type.
    datasetName: BigQuery dataset name Required for `bigquery` target_type.
    path: Path of Cloud Storage bucket Required for `gcs` target_type.
    projectId: Required. GCP project in which the datastore exists
    tablePrefix: Prefix of BigQuery table Required for `bigquery` target_type.
  """

  bucketName = _messages.StringField(1)
  datasetName = _messages.StringField(2)
  path = _messages.StringField(3)
  projectId = _messages.StringField(4)
  tablePrefix = _messages.StringField(5)


class GoogleCloudApigeeV1DateRange(_messages.Message):
  r"""Date range of the data to export.

  Fields:
    end: Required. End date (exclusive) of the data to export in the format
      `yyyy-mm-dd`. The date range ends at 00:00:00 UTC on the end date- which
      will not be in the output.
    start: Required. Start date of the data to export in the format `yyyy-mm-
      dd`. The date range begins at 00:00:00 UTC on the start date.
  """

  end = _messages.StringField(1)
  start = _messages.StringField(2)


class GoogleCloudApigeeV1DebugMask(_messages.Message):
  r"""A GoogleCloudApigeeV1DebugMask object.

  Messages:
    NamespacesValue: Map of namespaces to URIs.

  Fields:
    faultJSONPaths: List of JSON paths that specify the JSON elements to be
      filtered from JSON payloads in error flows.
    faultXPaths: List of XPaths that specify the XML elements to be filtered
      from XML payloads in error flows.
    name: Name of the debug mask.
    namespaces: Map of namespaces to URIs.
    requestJSONPaths: List of JSON paths that specify the JSON elements to be
      filtered from JSON request message payloads.
    requestXPaths: List of XPaths that specify the XML elements to be filtered
      from XML request message payloads.
    responseJSONPaths: List of JSON paths that specify the JSON elements to be
      filtered from JSON response message payloads.
    responseXPaths: List of XPaths that specify the XML elements to be
      filtered from XML response message payloads.
    variables: List of variables that should be masked from the debug output.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class NamespacesValue(_messages.Message):
    r"""Map of namespaces to URIs.

    Messages:
      AdditionalProperty: An additional property for a NamespacesValue object.

    Fields:
      additionalProperties: Additional properties of type NamespacesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a NamespacesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  faultJSONPaths = _messages.StringField(1, repeated=True)
  faultXPaths = _messages.StringField(2, repeated=True)
  name = _messages.StringField(3)
  namespaces = _messages.MessageField('NamespacesValue', 4)
  requestJSONPaths = _messages.StringField(5, repeated=True)
  requestXPaths = _messages.StringField(6, repeated=True)
  responseJSONPaths = _messages.StringField(7, repeated=True)
  responseXPaths = _messages.StringField(8, repeated=True)
  variables = _messages.StringField(9, repeated=True)


class GoogleCloudApigeeV1DebugSession(_messages.Message):
  r"""A GoogleCloudApigeeV1DebugSession object.

  Fields:
    count: Optional. The number of request to be traced. Min = 1, Max = 15,
      Default = 10.
    createTime: Output only. The first transaction creation timestamp,
      recorded by UAP.
    filter: Optional. A conditional statement which is evaluated against the
      request message to determine if it should be traced. Syntax matches that
      of on API Proxy bundle flow Condition.
    name: A unique ID for this DebugSession.
    timeout: Optional. The time in seconds after which this DebugSession
      should end. This value will override the value in query param, if both
      are provided.
    tracesize: Optional. The maximum number of bytes captured from the
      response payload. Min = 0, Max = 5120, Default = 5120.
    validity: Optional. The length of time, in seconds, that this debug
      session is valid, starting from when it's received in the control plane.
      Min = 1, Max = 15, Default = 10.
  """

  count = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  createTime = _messages.StringField(2)
  filter = _messages.StringField(3)
  name = _messages.StringField(4)
  timeout = _messages.IntegerField(5)
  tracesize = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  validity = _messages.IntegerField(7, variant=_messages.Variant.INT32)


class GoogleCloudApigeeV1DebugSessionTransaction(_messages.Message):
  r"""A transaction contains all of the debug information of the entire
  message flow of an API call processed by the runtime plane. The information
  is collected and recorded at critical points of the message flow in the
  runtime apiproxy.

  Fields:
    completed: Flag indicating whether a transaction is completed or not
    point: List of debug data collected by runtime plane at various defined
      points in the flow.
  """

  completed = _messages.BooleanField(1)
  point = _messages.MessageField('GoogleCloudApigeeV1Point', 2, repeated=True)


class GoogleCloudApigeeV1DeleteCustomReportResponse(_messages.Message):
  r"""A GoogleCloudApigeeV1DeleteCustomReportResponse object.

  Fields:
    message: The response contains only a message field.
  """

  message = _messages.StringField(1)


class GoogleCloudApigeeV1DeleteResponse(_messages.Message):
  r"""Response for certain delete operations.

  Fields:
    errorCode: ID that can be used to find errors in the log files.
    gcpResource: GCP name of deleted resource.
    message: Description of the operation.
    requestId: ID that can be used to find request details in the log files.
    status: Status of the operation.
  """

  errorCode = _messages.StringField(1)
  gcpResource = _messages.StringField(2)
  message = _messages.StringField(3)
  requestId = _messages.StringField(4)
  status = _messages.StringField(5)


class GoogleCloudApigeeV1Deployment(_messages.Message):
  r"""A GoogleCloudApigeeV1Deployment object.

  Enums:
    ProxyDeploymentTypeValueValuesEnum: Output only. The type of the
      deployment (standard or extensible) Deployed proxy revision will be
      marked as extensible in following 2 cases. 1. The deployed proxy
      revision uses extensible policies. 2. If a environment supports
      flowhooks and flow hook is configured.
    StateValueValuesEnum: Current state of the deployment. **Note**: This
      field is displayed only when viewing deployment status.

  Fields:
    apiProxy: API proxy.
    deployStartTime: Time the API proxy was marked `deployed` in the control
      plane in millisconds since epoch.
    environment: Environment.
    errors: Errors reported for this deployment. Populated only when state ==
      ERROR. **Note**: This field is displayed only when viewing deployment
      status.
    instances: Status reported by each runtime instance. **Note**: This field
      is displayed only when viewing deployment status.
    pods: Status reported by runtime pods. **Note**: **This field is
      deprecated**. Runtime versions 1.3 and above report instance level
      status rather than pod status.
    proxyDeploymentType: Output only. The type of the deployment (standard or
      extensible) Deployed proxy revision will be marked as extensible in
      following 2 cases. 1. The deployed proxy revision uses extensible
      policies. 2. If a environment supports flowhooks and flow hook is
      configured.
    revision: API proxy revision.
    routeConflicts: Conflicts in the desired state routing configuration. The
      presence of conflicts does not cause the state to be `ERROR`, but it
      will mean that some of the deployment's base paths are not routed to its
      environment. If the conflicts change, the state will transition to
      `PROGRESSING` until the latest configuration is rolled out to all
      instances. **Note**: This field is displayed only when viewing
      deployment status.
    serviceAccount: The full resource name of Cloud IAM Service Account that
      this deployment is using, eg, `projects/-/serviceAccounts/{email}`.
    state: Current state of the deployment. **Note**: This field is displayed
      only when viewing deployment status.
  """

  class ProxyDeploymentTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The type of the deployment (standard or extensible)
    Deployed proxy revision will be marked as extensible in following 2 cases.
    1. The deployed proxy revision uses extensible policies. 2. If a
    environment supports flowhooks and flow hook is configured.

    Values:
      PROXY_DEPLOYMENT_TYPE_UNSPECIFIED: Default value till public preview.
        After public preview this value should not be returned.
      STANDARD: Deployment will be of type Standard if only Standard proxies
        are used
      EXTENSIBLE: Proxy will be of type Extensible if deployments uses one or
        more Extensible proxies
    """
    PROXY_DEPLOYMENT_TYPE_UNSPECIFIED = 0
    STANDARD = 1
    EXTENSIBLE = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Current state of the deployment. **Note**: This field is displayed
    only when viewing deployment status.

    Values:
      RUNTIME_STATE_UNSPECIFIED: This value should never be returned.
      READY: Runtime has loaded the deployment.
      PROGRESSING: Deployment is not fully ready in the runtime.
      ERROR: Encountered an error with the deployment that requires
        intervention.
    """
    RUNTIME_STATE_UNSPECIFIED = 0
    READY = 1
    PROGRESSING = 2
    ERROR = 3

  apiProxy = _messages.StringField(1)
  deployStartTime = _messages.IntegerField(2)
  environment = _messages.StringField(3)
  errors = _messages.MessageField('GoogleRpcStatus', 4, repeated=True)
  instances = _messages.MessageField('GoogleCloudApigeeV1InstanceDeploymentStatus', 5, repeated=True)
  pods = _messages.MessageField('GoogleCloudApigeeV1PodStatus', 6, repeated=True)
  proxyDeploymentType = _messages.EnumField('ProxyDeploymentTypeValueValuesEnum', 7)
  revision = _messages.StringField(8)
  routeConflicts = _messages.MessageField('GoogleCloudApigeeV1DeploymentChangeReportRoutingConflict', 9, repeated=True)
  serviceAccount = _messages.StringField(10)
  state = _messages.EnumField('StateValueValuesEnum', 11)


class GoogleCloudApigeeV1DeploymentChangeReport(_messages.Message):
  r"""Response for GenerateDeployChangeReport and
  GenerateUndeployChangeReport. This report contains any validation failures
  that would cause the deployment to be rejected, as well changes and
  conflicts in routing that may occur due to the new deployment. The existence
  of a routing warning does not necessarily imply that the deployment request
  is bad, if the desired state of the deployment request is to effect a
  routing change. The primary purposes of the routing messages are: 1) To
  inform users of routing changes that may have an effect on traffic currently
  being routed to other existing deployments. 2) To warn users if some base
  path in the proxy will not receive traffic due to an existing deployment
  having already claimed that base path. The presence of routing
  conflicts/changes will not cause non-dry-run DeployApiProxy/UndeployApiProxy
  requests to be rejected.

  Fields:
    routingChanges: All routing changes that may result from a deployment
      request.
    routingConflicts: All base path conflicts detected for a deployment
      request.
    validationErrors: Validation errors that would cause the deployment change
      request to be rejected.
  """

  routingChanges = _messages.MessageField('GoogleCloudApigeeV1DeploymentChangeReportRoutingChange', 1, repeated=True)
  routingConflicts = _messages.MessageField('GoogleCloudApigeeV1DeploymentChangeReportRoutingConflict', 2, repeated=True)
  validationErrors = _messages.MessageField('GoogleRpcPreconditionFailure', 3)


class GoogleCloudApigeeV1DeploymentChangeReportRoutingChange(_messages.Message):
  r"""Describes a potential routing change that may occur as a result of some
  deployment operation.

  Fields:
    description: Human-readable description of this routing change.
    environmentGroup: Name of the environment group affected by this routing
      change.
    fromDeployment: Base path/deployment that may stop receiving some traffic.
    shouldSequenceRollout: Set to `true` if using sequenced rollout would make
      this routing change safer. **Note**: This does not necessarily imply
      that automated sequenced rollout mode is supported for the operation.
    toDeployment: Base path/deployment that may start receiving that traffic.
      May be null if no deployment is able to receive the traffic.
  """

  description = _messages.StringField(1)
  environmentGroup = _messages.StringField(2)
  fromDeployment = _messages.MessageField('GoogleCloudApigeeV1DeploymentChangeReportRoutingDeployment', 3)
  shouldSequenceRollout = _messages.BooleanField(4)
  toDeployment = _messages.MessageField('GoogleCloudApigeeV1DeploymentChangeReportRoutingDeployment', 5)


class GoogleCloudApigeeV1DeploymentChangeReportRoutingConflict(_messages.Message):
  r"""Describes a routing conflict that may cause a deployment not to receive
  traffic at some base path.

  Fields:
    conflictingDeployment: Existing base path/deployment causing the conflict.
    description: Human-readable description of this conflict.
    environmentGroup: Name of the environment group in which this conflict
      exists.
  """

  conflictingDeployment = _messages.MessageField('GoogleCloudApigeeV1DeploymentChangeReportRoutingDeployment', 1)
  description = _messages.StringField(2)
  environmentGroup = _messages.StringField(3)


class GoogleCloudApigeeV1DeploymentChangeReportRoutingDeployment(_messages.Message):
  r"""Tuple representing a base path and the deployment containing it.

  Fields:
    apiProxy: Name of the deployed API proxy revision containing the base
      path.
    basepath: Base path receiving traffic.
    environment: Name of the environment in which the proxy is deployed.
    revision: Name of the deployed API proxy revision containing the base
      path.
  """

  apiProxy = _messages.StringField(1)
  basepath = _messages.StringField(2)
  environment = _messages.StringField(3)
  revision = _messages.StringField(4)


class GoogleCloudApigeeV1DeploymentConfig(_messages.Message):
  r"""NEXT ID: 11

  Messages:
    AttributesValue: Additional key-value metadata for the deployment.
    EndpointsValue: A mapping from basepaths to proxy endpoint names in this
      proxy. Not populated for shared flows.

  Fields:
    attributes: Additional key-value metadata for the deployment.
    basePath: Base path where the application will be hosted. Defaults to "/".
    deploymentGroups: The list of deployment groups in which this proxy should
      be deployed. Not currently populated for shared flows.
    endpoints: A mapping from basepaths to proxy endpoint names in this proxy.
      Not populated for shared flows.
    location: Location of the API proxy bundle as a URI.
    name: Name of the API or shared flow revision to be deployed in the
      following format: `organizations/{org}/apis/{api}/revisions/{rev}` or
      `organizations/{org}/sharedflows/{sharedflow}/revisions/{rev}`
    proxyUid: Unique ID of the API proxy revision.
    serviceAccount: The service account identity associated with this
      deployment. If non-empty, will be in the following format:
      `projects/-/serviceAccounts/{account_email}`
    uid: Unique ID. The ID will only change if the deployment is deleted and
      recreated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributesValue(_messages.Message):
    r"""Additional key-value metadata for the deployment.

    Messages:
      AdditionalProperty: An additional property for a AttributesValue object.

    Fields:
      additionalProperties: Additional properties of type AttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EndpointsValue(_messages.Message):
    r"""A mapping from basepaths to proxy endpoint names in this proxy. Not
    populated for shared flows.

    Messages:
      AdditionalProperty: An additional property for a EndpointsValue object.

    Fields:
      additionalProperties: Additional properties of type EndpointsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EndpointsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributes = _messages.MessageField('AttributesValue', 1)
  basePath = _messages.StringField(2)
  deploymentGroups = _messages.StringField(3, repeated=True)
  endpoints = _messages.MessageField('EndpointsValue', 4)
  location = _messages.StringField(5)
  name = _messages.StringField(6)
  proxyUid = _messages.StringField(7)
  serviceAccount = _messages.StringField(8)
  uid = _messages.StringField(9)


class GoogleCloudApigeeV1DeploymentGroupConfig(_messages.Message):
  r"""DeploymentGroupConfig represents a deployment group that should be
  present in a particular environment.

  Enums:
    DeploymentGroupTypeValueValuesEnum: Type of the deployment group, which
      will be either Standard or Extensible.

  Fields:
    deploymentGroupType: Type of the deployment group, which will be either
      Standard or Extensible.
    name: Name of the deployment group in the following format:
      `organizations/{org}/environments/{env}/deploymentGroups/{group}`.
    revisionId: Revision number which can be used by the runtime to detect if
      the deployment group has changed between two versions.
    uid: Unique ID. The ID will only change if the deployment group is deleted
      and recreated.
  """

  class DeploymentGroupTypeValueValuesEnum(_messages.Enum):
    r"""Type of the deployment group, which will be either Standard or
    Extensible.

    Values:
      DEPLOYMENT_GROUP_TYPE_UNSPECIFIED: Unspecified type
      STANDARD: Standard type
      EXTENSIBLE: Extensible Type
    """
    DEPLOYMENT_GROUP_TYPE_UNSPECIFIED = 0
    STANDARD = 1
    EXTENSIBLE = 2

  deploymentGroupType = _messages.EnumField('DeploymentGroupTypeValueValuesEnum', 1)
  name = _messages.StringField(2)
  revisionId = _messages.IntegerField(3)
  uid = _messages.StringField(4)


class GoogleCloudApigeeV1Developer(_messages.Message):
  r"""A GoogleCloudApigeeV1Developer object.

  Fields:
    accessType: Access type.
    appFamily: Developer app family.
    apps: List of apps associated with the developer.
    attributes: Optional. Developer attributes (name/value pairs). The custom
      attribute limit is 18.
    companies: List of companies associated with the developer.
    createdAt: Output only. Time at which the developer was created in
      milliseconds since epoch.
    developerId: ID of the developer. **Note**: IDs are generated internally
      by Apigee and are not guaranteed to stay the same over time.
    email: Required. Email address of the developer. This value is used to
      uniquely identify the developer in Apigee hybrid. Note that the email
      address has to be in lowercase only.
    firstName: Required. First name of the developer.
    lastModifiedAt: Output only. Time at which the developer was last modified
      in milliseconds since epoch.
    lastName: Required. Last name of the developer.
    organizationName: Output only. Name of the Apigee organization in which
      the developer resides.
    status: Output only. Status of the developer. Valid values are `active`
      and `inactive`.
    userName: Required. User name of the developer. Not used by Apigee hybrid.
  """

  accessType = _messages.StringField(1)
  appFamily = _messages.StringField(2)
  apps = _messages.StringField(3, repeated=True)
  attributes = _messages.MessageField('GoogleCloudApigeeV1Attribute', 4, repeated=True)
  companies = _messages.StringField(5, repeated=True)
  createdAt = _messages.IntegerField(6)
  developerId = _messages.StringField(7)
  email = _messages.StringField(8)
  firstName = _messages.StringField(9)
  lastModifiedAt = _messages.IntegerField(10)
  lastName = _messages.StringField(11)
  organizationName = _messages.StringField(12)
  status = _messages.StringField(13)
  userName = _messages.StringField(14)


class GoogleCloudApigeeV1DeveloperApp(_messages.Message):
  r"""A GoogleCloudApigeeV1DeveloperApp object.

  Fields:
    apiProducts: List of API products associated with the developer app.
    appFamily: Developer app family.
    appId: ID of the developer app.
    attributes: List of attributes for the developer app.
    callbackUrl: Callback URL used by OAuth 2.0 authorization servers to
      communicate authorization codes back to developer apps.
    createdAt: Output only. Time the developer app was created in milliseconds
      since epoch.
    credentials: Output only. Set of credentials for the developer app
      consisting of the consumer key/secret pairs associated with the API
      products.
    developerId: ID of the developer.
    keyExpiresIn: Expiration time, in milliseconds, for the consumer key that
      is generated for the developer app. If not set or left to the default
      value of `-1`, the API key never expires. The expiration time can't be
      updated after it is set.
    lastModifiedAt: Output only. Time the developer app was modified in
      milliseconds since epoch.
    name: Name of the developer app.
    scopes: Scopes to apply to the developer app. The specified scopes must
      already exist for the API product that you associate with the developer
      app.
    status: Status of the credential. Valid values include `approved` or
      `revoked`.
  """

  apiProducts = _messages.StringField(1, repeated=True)
  appFamily = _messages.StringField(2)
  appId = _messages.StringField(3)
  attributes = _messages.MessageField('GoogleCloudApigeeV1Attribute', 4, repeated=True)
  callbackUrl = _messages.StringField(5)
  createdAt = _messages.IntegerField(6)
  credentials = _messages.MessageField('GoogleCloudApigeeV1Credential', 7, repeated=True)
  developerId = _messages.StringField(8)
  keyExpiresIn = _messages.IntegerField(9)
  lastModifiedAt = _messages.IntegerField(10)
  name = _messages.StringField(11)
  scopes = _messages.StringField(12, repeated=True)
  status = _messages.StringField(13)


class GoogleCloudApigeeV1DeveloperAppKey(_messages.Message):
  r"""A GoogleCloudApigeeV1DeveloperAppKey object.

  Fields:
    apiProducts: List of API products for which the credential can be used.
      **Note**: Do not specify the list of API products when creating a
      consumer key and secret for a developer app. Instead, use the
      UpdateDeveloperAppKey API to make the association after the consumer key
      and secret are created.
    attributes: List of attributes associated with the credential.
    consumerKey: Consumer key.
    consumerSecret: Secret key.
    expiresAt: Time the developer app expires in milliseconds since epoch.
    expiresInSeconds: Input only. Expiration time, in seconds, for the
      consumer key. If not set or left to the default value of `-1`, the API
      key never expires. The expiration time can't be updated after it is set.
    issuedAt: Time the developer app was created in milliseconds since epoch.
    scopes: Scopes to apply to the app. The specified scope names must already
      be defined for the API product that you associate with the app.
    status: Status of the credential. Valid values include `approved` or
      `revoked`.
  """

  apiProducts = _messages.MessageField('extra_types.JsonValue', 1, repeated=True)
  attributes = _messages.MessageField('GoogleCloudApigeeV1Attribute', 2, repeated=True)
  consumerKey = _messages.StringField(3)
  consumerSecret = _messages.StringField(4)
  expiresAt = _messages.IntegerField(5)
  expiresInSeconds = _messages.IntegerField(6)
  issuedAt = _messages.IntegerField(7)
  scopes = _messages.StringField(8, repeated=True)
  status = _messages.StringField(9)


class GoogleCloudApigeeV1DeveloperBalance(_messages.Message):
  r"""Account balance for the developer.

  Fields:
    wallets: Output only. List of all wallets. Each individual wallet stores
      the account balance for a particular currency.
  """

  wallets = _messages.MessageField('GoogleCloudApigeeV1DeveloperBalanceWallet', 1, repeated=True)


class GoogleCloudApigeeV1DeveloperBalanceWallet(_messages.Message):
  r"""Wallet used to manage an account balance for a particular currency.

  Fields:
    balance: Current remaining balance of the developer for a particular
      currency.
    lastCreditTime: Output only. Time at which the developer last added credit
      to the account in milliseconds since epoch.
  """

  balance = _messages.MessageField('GoogleTypeMoney', 1)
  lastCreditTime = _messages.IntegerField(2)


class GoogleCloudApigeeV1DeveloperMonetizationConfig(_messages.Message):
  r"""Monetization configuration for the developer.

  Enums:
    BillingTypeValueValuesEnum: Billing type.

  Fields:
    billingType: Billing type.
  """

  class BillingTypeValueValuesEnum(_messages.Enum):
    r"""Billing type.

    Values:
      BILLING_TYPE_UNSPECIFIED: The default/unset value.
      PREPAID: Developer pays in advance for the use of APIs and the charged
        amount is deducted from their account balance.
      POSTPAID: Developer does not maintain an account balance. The API
        provider bills the developer for API usage.
    """
    BILLING_TYPE_UNSPECIFIED = 0
    PREPAID = 1
    POSTPAID = 2

  billingType = _messages.EnumField('BillingTypeValueValuesEnum', 1)


class GoogleCloudApigeeV1DeveloperSubscription(_messages.Message):
  r"""Structure of a DeveloperSubscription.

  Fields:
    apiproduct: Name of the API product for which the developer is purchasing
      a subscription.
    createdAt: Output only. Time when the API product subscription was created
      in milliseconds since epoch.
    endTime: Time when the API product subscription ends in milliseconds since
      epoch.
    lastModifiedAt: Output only. Time when the API product subscription was
      last modified in milliseconds since epoch.
    name: Output only. Name of the API product subscription.
    startTime: Time when the API product subscription starts in milliseconds
      since epoch.
  """

  apiproduct = _messages.StringField(1)
  createdAt = _messages.IntegerField(2)
  endTime = _messages.IntegerField(3)
  lastModifiedAt = _messages.IntegerField(4)
  name = _messages.StringField(5)
  startTime = _messages.IntegerField(6)


class GoogleCloudApigeeV1DimensionMetric(_messages.Message):
  r"""Encapsulates a metric grouped by dimension.

  Fields:
    individualNames: Individual dimension names. E.g. ["dim1_name",
      "dim2_name"].
    metrics: List of metrics.
    name: Comma joined dimension names. E.g. "dim1_name,dim2_name".
      Deprecated. If name already has comma before join, we may get wrong
      splits. Please use individual_names.
  """

  individualNames = _messages.StringField(1, repeated=True)
  metrics = _messages.MessageField('GoogleCloudApigeeV1Metric', 2, repeated=True)
  name = _messages.StringField(3)


class GoogleCloudApigeeV1DisableSecurityActionRequest(_messages.Message):
  r"""Message to disable an enabled SecurityAction."""


class GoogleCloudApigeeV1EnableSecurityActionRequest(_messages.Message):
  r"""Message to enable a disabled SecurityAction."""


class GoogleCloudApigeeV1EndpointAttachment(_messages.Message):
  r"""Apigee endpoint attachment. For more information, see [Southbound
  networking patterns] (https://cloud.google.com/apigee/docs/api-
  platform/architecture/southbound-networking-patterns-endpoints).

  Enums:
    ConnectionStateValueValuesEnum: Output only. State of the endpoint
      attachment connection to the service attachment.
    StateValueValuesEnum: Output only. State of the endpoint attachment.
      Values other than `ACTIVE` mean the resource is not ready to use.

  Fields:
    connectionState: Output only. State of the endpoint attachment connection
      to the service attachment.
    host: Output only. Host that can be used in either the HTTP target
      endpoint directly or as the host in target server.
    location: Required. Location of the endpoint attachment.
    name: Name of the endpoint attachment. Use the following structure in your
      request: `organizations/{org}/endpointAttachments/{endpoint_attachment}`
    serviceAttachment: Format: projects/*/regions/*/serviceAttachments/*
    state: Output only. State of the endpoint attachment. Values other than
      `ACTIVE` mean the resource is not ready to use.
  """

  class ConnectionStateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the endpoint attachment connection to the
    service attachment.

    Values:
      CONNECTION_STATE_UNSPECIFIED: The connection state has not been set.
      UNAVAILABLE: The connection state is unavailable at this time, possibly
        because the endpoint attachment is currently being provisioned.
      PENDING: The connection is pending acceptance by the PSC producer.
      ACCEPTED: The connection has been accepted by the PSC producer.
      REJECTED: The connection has been rejected by the PSC producer.
      CLOSED: The connection has been closed by the PSC producer and will not
        serve traffic going forward.
      FROZEN: The connection has been frozen by the PSC producer and will not
        serve traffic.
      NEEDS_ATTENTION: The connection has been accepted by the PSC producer,
        but it is not ready to serve the traffic due to producer side issues.
    """
    CONNECTION_STATE_UNSPECIFIED = 0
    UNAVAILABLE = 1
    PENDING = 2
    ACCEPTED = 3
    REJECTED = 4
    CLOSED = 5
    FROZEN = 6
    NEEDS_ATTENTION = 7

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the endpoint attachment. Values other than
    `ACTIVE` mean the resource is not ready to use.

    Values:
      STATE_UNSPECIFIED: Resource is in an unspecified state.
      CREATING: Resource is being created.
      ACTIVE: Resource is provisioned and ready to use.
      DELETING: The resource is being deleted.
      UPDATING: The resource is being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    UPDATING = 4

  connectionState = _messages.EnumField('ConnectionStateValueValuesEnum', 1)
  host = _messages.StringField(2)
  location = _messages.StringField(3)
  name = _messages.StringField(4)
  serviceAttachment = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)


class GoogleCloudApigeeV1EndpointChainingRule(_messages.Message):
  r"""EndpointChainingRule specifies the proxies contained in a particular
  deployment group, so that other deployment groups can find them in chaining
  calls.

  Fields:
    deploymentGroup: The deployment group to target for cross-shard chaining
      calls to these proxies.
    proxyIds: List of proxy ids which may be found in the given deployment
      group.
  """

  deploymentGroup = _messages.StringField(1)
  proxyIds = _messages.StringField(2, repeated=True)


class GoogleCloudApigeeV1EntityMetadata(_messages.Message):
  r"""Metadata common to many entities in this API.

  Fields:
    createdAt: Time at which the API proxy was created, in milliseconds since
      epoch.
    lastModifiedAt: Time at which the API proxy was most recently modified, in
      milliseconds since epoch.
    subType: The type of entity described
  """

  createdAt = _messages.IntegerField(1)
  lastModifiedAt = _messages.IntegerField(2)
  subType = _messages.StringField(3)


class GoogleCloudApigeeV1Environment(_messages.Message):
  r"""A GoogleCloudApigeeV1Environment object.

  Enums:
    ApiProxyTypeValueValuesEnum: Optional. API Proxy type supported by the
      environment. The type can be set when creating the Environment and
      cannot be changed.
    DeploymentTypeValueValuesEnum: Optional. Deployment type supported by the
      environment. The deployment type can be set when creating the
      environment and cannot be changed. When you enable archive deployment,
      you will be **prevented from performing** a [subset of
      actions](/apigee/docs/api-platform/local-development/overview#prevented-
      actions) within the environment, including: * Managing the deployment of
      API proxy or shared flow revisions * Creating, updating, or deleting
      resource files * Creating, updating, or deleting target servers
    StateValueValuesEnum: Output only. State of the environment. Values other
      than ACTIVE means the resource is not ready to use.
    TypeValueValuesEnum: Optional. EnvironmentType selected for the
      environment.

  Fields:
    apiProxyType: Optional. API Proxy type supported by the environment. The
      type can be set when creating the Environment and cannot be changed.
    createdAt: Output only. Creation time of this environment as milliseconds
      since epoch.
    deploymentType: Optional. Deployment type supported by the environment.
      The deployment type can be set when creating the environment and cannot
      be changed. When you enable archive deployment, you will be **prevented
      from performing** a [subset of actions](/apigee/docs/api-platform/local-
      development/overview#prevented-actions) within the environment,
      including: * Managing the deployment of API proxy or shared flow
      revisions * Creating, updating, or deleting resource files * Creating,
      updating, or deleting target servers
    description: Optional. Description of the environment.
    displayName: Optional. Display name for this environment.
    forwardProxyUri: Optional. Url of the forward proxy to be applied to the
      runtime instances in this environment. Must be in the format of
      {scheme}://{hostname}:{port}. Note that scheme must be one of "http" or
      "https", and port must be supplied.
    hasAttachedFlowHooks: A boolean attribute.
    lastModifiedAt: Output only. Last modification time of this environment as
      milliseconds since epoch.
    name: Required. Name of the environment. Values must match the regular
      expression `^[.\\p{Alnum}-_]{1,255}$`
    nodeConfig: Optional. NodeConfig of the environment.
    properties: Optional. Key-value pairs that may be used for customizing the
      environment.
    state: Output only. State of the environment. Values other than ACTIVE
      means the resource is not ready to use.
    type: Optional. EnvironmentType selected for the environment.
  """

  class ApiProxyTypeValueValuesEnum(_messages.Enum):
    r"""Optional. API Proxy type supported by the environment. The type can be
    set when creating the Environment and cannot be changed.

    Values:
      API_PROXY_TYPE_UNSPECIFIED: API proxy type not specified.
      PROGRAMMABLE: Programmable API Proxies enable you to develop APIs with
        highly flexible behavior using bundled policy configuration and one or
        more programming languages to describe complex sequential and/or
        conditional flows of logic.
      CONFIGURABLE: Configurable API Proxies enable you to develop efficient
        APIs using simple configuration while complex execution control flow
        logic is handled by Apigee. This type only works with the ARCHIVE
        deployment type and cannot be combined with the PROXY deployment type.
    """
    API_PROXY_TYPE_UNSPECIFIED = 0
    PROGRAMMABLE = 1
    CONFIGURABLE = 2

  class DeploymentTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Deployment type supported by the environment. The deployment
    type can be set when creating the environment and cannot be changed. When
    you enable archive deployment, you will be **prevented from performing** a
    [subset of actions](/apigee/docs/api-platform/local-
    development/overview#prevented-actions) within the environment, including:
    * Managing the deployment of API proxy or shared flow revisions *
    Creating, updating, or deleting resource files * Creating, updating, or
    deleting target servers

    Values:
      DEPLOYMENT_TYPE_UNSPECIFIED: Deployment type not specified.
      PROXY: Proxy deployment enables you to develop and deploy API proxies
        using Apigee on Google Cloud. This cannot currently be combined with
        the CONFIGURABLE API proxy type.
      ARCHIVE: Archive deployment enables you to develop API proxies locally
        then deploy an archive of your API proxy configuration to an
        environment in Apigee on Google Cloud. You will be prevented from
        performing a [subset of actions](/apigee/docs/api-platform/local-
        development/overview#prevented-actions) within the environment.
    """
    DEPLOYMENT_TYPE_UNSPECIFIED = 0
    PROXY = 1
    ARCHIVE = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the environment. Values other than ACTIVE means
    the resource is not ready to use.

    Values:
      STATE_UNSPECIFIED: Resource is in an unspecified state.
      CREATING: Resource is being created.
      ACTIVE: Resource is provisioned and ready to use.
      DELETING: The resource is being deleted.
      UPDATING: The resource is being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    UPDATING = 4

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. EnvironmentType selected for the environment.

    Values:
      ENVIRONMENT_TYPE_UNSPECIFIED: Environment type not specified.
      BASE: Base environment has limited capacity and capabilities and are
        usually used when you are getting started with Apigee or while
        experimenting. Refer to Apigee's public documentation for more
        details.
      INTERMEDIATE: This is the default type and it supports API management
        features and higher capacity than Base environment. Refer to Apigee's
        public documentation for more details.
      COMPREHENSIVE: Comprehensive environment supports advanced capabilites
        and even higher capacity than Intermediate environment. Refer to
        Apigee's public documentation for more details.
    """
    ENVIRONMENT_TYPE_UNSPECIFIED = 0
    BASE = 1
    INTERMEDIATE = 2
    COMPREHENSIVE = 3

  apiProxyType = _messages.EnumField('ApiProxyTypeValueValuesEnum', 1)
  createdAt = _messages.IntegerField(2)
  deploymentType = _messages.EnumField('DeploymentTypeValueValuesEnum', 3)
  description = _messages.StringField(4)
  displayName = _messages.StringField(5)
  forwardProxyUri = _messages.StringField(6)
  hasAttachedFlowHooks = _messages.BooleanField(7)
  lastModifiedAt = _messages.IntegerField(8)
  name = _messages.StringField(9)
  nodeConfig = _messages.MessageField('GoogleCloudApigeeV1NodeConfig', 10)
  properties = _messages.MessageField('GoogleCloudApigeeV1Properties', 11)
  state = _messages.EnumField('StateValueValuesEnum', 12)
  type = _messages.EnumField('TypeValueValuesEnum', 13)


class GoogleCloudApigeeV1EnvironmentConfig(_messages.Message):
  r"""A GoogleCloudApigeeV1EnvironmentConfig object.

  Messages:
    FeatureFlagsValue: Feature flags inherited from the organization and
      environment.

  Fields:
    addonsConfig: The latest runtime configurations for add-ons.
    arcConfigLocation: The location for the config blob of API Runtime
      Control, aka Envoy Adapter, for op-based authentication as a URI, e.g. a
      Cloud Storage URI. This is only used by Envoy-based gateways.
    createTime: Time that the environment configuration was created.
    dataCollectors: List of data collectors used by the deployments in the
      environment.
    debugMask: Debug mask that applies to all deployments in the environment.
    deploymentGroups: List of deployment groups in the environment.
    deployments: List of deployments in the environment.
    envScopedRevisionId: Revision ID for environment-scoped resources (e.g.
      target servers, keystores) in this config. This ID will increment any
      time a resource not scoped to a deployment group changes.
    featureFlags: Feature flags inherited from the organization and
      environment.
    flowhooks: List of flow hooks in the environment.
    forwardProxyUri: The forward proxy's url to be used by the runtime. When
      set, runtime will send requests to the target via the given forward
      proxy. This is only used by programmable gateways.
    gatewayConfigLocation: The location for the gateway config blob as a URI,
      e.g. a Cloud Storage URI. This is only used by Envoy-based gateways.
    keystores: List of keystores in the environment.
    name: Name of the environment configuration in the following format:
      `organizations/{org}/environments/{env}/configs/{config}`
    provider: Used by the Control plane to add context information to help
      detect the source of the document during diagnostics and debugging.
    pubsubTopic: Name of the PubSub topic for the environment.
    resourceReferences: List of resource references in the environment.
    resources: List of resource versions in the environment.
    revisionId: Revision ID of the environment configuration. The higher the
      value, the more recently the configuration was deployed.
    sequenceNumber: DEPRECATED: Use revision_id.
    targets: List of target servers in the environment. Disabled target
      servers are not displayed.
    traceConfig: Trace configurations. Contains config for the environment and
      config overrides for specific API proxies.
    uid: Unique ID for the environment configuration. The ID will only change
      if the environment is deleted and recreated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class FeatureFlagsValue(_messages.Message):
    r"""Feature flags inherited from the organization and environment.

    Messages:
      AdditionalProperty: An additional property for a FeatureFlagsValue
        object.

    Fields:
      additionalProperties: Additional properties of type FeatureFlagsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a FeatureFlagsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  addonsConfig = _messages.MessageField('GoogleCloudApigeeV1RuntimeAddonsConfig', 1)
  arcConfigLocation = _messages.StringField(2)
  createTime = _messages.StringField(3)
  dataCollectors = _messages.MessageField('GoogleCloudApigeeV1DataCollectorConfig', 4, repeated=True)
  debugMask = _messages.MessageField('GoogleCloudApigeeV1DebugMask', 5)
  deploymentGroups = _messages.MessageField('GoogleCloudApigeeV1DeploymentGroupConfig', 6, repeated=True)
  deployments = _messages.MessageField('GoogleCloudApigeeV1DeploymentConfig', 7, repeated=True)
  envScopedRevisionId = _messages.IntegerField(8)
  featureFlags = _messages.MessageField('FeatureFlagsValue', 9)
  flowhooks = _messages.MessageField('GoogleCloudApigeeV1FlowHookConfig', 10, repeated=True)
  forwardProxyUri = _messages.StringField(11)
  gatewayConfigLocation = _messages.StringField(12)
  keystores = _messages.MessageField('GoogleCloudApigeeV1KeystoreConfig', 13, repeated=True)
  name = _messages.StringField(14)
  provider = _messages.StringField(15)
  pubsubTopic = _messages.StringField(16)
  resourceReferences = _messages.MessageField('GoogleCloudApigeeV1ReferenceConfig', 17, repeated=True)
  resources = _messages.MessageField('GoogleCloudApigeeV1ResourceConfig', 18, repeated=True)
  revisionId = _messages.IntegerField(19)
  sequenceNumber = _messages.IntegerField(20)
  targets = _messages.MessageField('GoogleCloudApigeeV1TargetServerConfig', 21, repeated=True)
  traceConfig = _messages.MessageField('GoogleCloudApigeeV1RuntimeTraceConfig', 22)
  uid = _messages.StringField(23)


class GoogleCloudApigeeV1EnvironmentGroup(_messages.Message):
  r"""EnvironmentGroup configuration. An environment group is used to group
  one or more Apigee environments under a single host name.

  Enums:
    StateValueValuesEnum: Output only. State of the environment group. Values
      other than ACTIVE means the resource is not ready to use.

  Fields:
    createdAt: Output only. The time at which the environment group was
      created as milliseconds since epoch.
    hostnames: Required. Host names for this environment group.
    lastModifiedAt: Output only. The time at which the environment group was
      last updated as milliseconds since epoch.
    name: ID of the environment group.
    state: Output only. State of the environment group. Values other than
      ACTIVE means the resource is not ready to use.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the environment group. Values other than ACTIVE
    means the resource is not ready to use.

    Values:
      STATE_UNSPECIFIED: Resource is in an unspecified state.
      CREATING: Resource is being created.
      ACTIVE: Resource is provisioned and ready to use.
      DELETING: The resource is being deleted.
      UPDATING: The resource is being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    UPDATING = 4

  createdAt = _messages.IntegerField(1)
  hostnames = _messages.StringField(2, repeated=True)
  lastModifiedAt = _messages.IntegerField(3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class GoogleCloudApigeeV1EnvironmentGroupAttachment(_messages.Message):
  r"""EnvironmentGroupAttachment is a resource which defines an attachment of
  an environment to an environment group.

  Enums:
    StateValueValuesEnum: Output only. State of the environment group
      attachment. Values other than ACTIVE means the resource is not ready to
      use.

  Fields:
    createdAt: Output only. The time at which the environment group attachment
      was created as milliseconds since epoch.
    environment: Required. ID of the attached environment.
    environmentGroupId: Output only. ID of the environment group.
    name: ID of the environment group attachment.
    state: Output only. State of the environment group attachment. Values
      other than ACTIVE means the resource is not ready to use.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the environment group attachment. Values other
    than ACTIVE means the resource is not ready to use.

    Values:
      STATE_UNSPECIFIED: Resource is in an unspecified state.
      CREATING: Resource is being created.
      ACTIVE: Resource is provisioned and ready to use.
      DELETING: The resource is being deleted.
      UPDATING: The resource is being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    UPDATING = 4

  createdAt = _messages.IntegerField(1)
  environment = _messages.StringField(2)
  environmentGroupId = _messages.StringField(3)
  name = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class GoogleCloudApigeeV1EnvironmentGroupConfig(_messages.Message):
  r"""EnvironmentGroupConfig is a revisioned snapshot of an EnvironmentGroup
  and its associated routing rules.

  Fields:
    endpointChainingRules: A list of proxies in each deployment group for
      proxy chaining calls.
    hostnames: Host names for the environment group.
    location: When this message appears in the top-level IngressConfig, this
      field will be populated in lieu of the inlined routing_rules and
      hostnames fields. Some URL for downloading the full
      EnvironmentGroupConfig for this group.
    name: Name of the environment group in the following format:
      `organizations/{org}/envgroups/{envgroup}`.
    revisionId: Revision id that defines the ordering of the
      EnvironmentGroupConfig resource. The higher the revision, the more
      recently the configuration was deployed.
    routingRules: Ordered list of routing rules defining how traffic to this
      environment group's hostnames should be routed to different
      environments.
    uid: A unique id for the environment group config that will only change if
      the environment group is deleted and recreated.
  """

  endpointChainingRules = _messages.MessageField('GoogleCloudApigeeV1EndpointChainingRule', 1, repeated=True)
  hostnames = _messages.StringField(2, repeated=True)
  location = _messages.StringField(3)
  name = _messages.StringField(4)
  revisionId = _messages.IntegerField(5)
  routingRules = _messages.MessageField('GoogleCloudApigeeV1RoutingRule', 6, repeated=True)
  uid = _messages.StringField(7)


class GoogleCloudApigeeV1ExpireDeveloperSubscriptionRequest(_messages.Message):
  r"""Request for ExpireDeveloperSubscription."""


class GoogleCloudApigeeV1Export(_messages.Message):
  r"""Details of an export job.

  Fields:
    created: Output only. Time the export job was created.
    datastoreName: Name of the datastore that is the destination of the export
      job [datastore]
    description: Description of the export job.
    error: Output only. Error is set when export fails
    executionTime: Output only. Execution time for this export job. If the job
      is still in progress, it will be set to the amount of time that has
      elapsed since`created`, in seconds. Else, it will set to (`updated` -
      `created`), in seconds.
    name: Display name of the export job.
    self: Output only. Self link of the export job. A URI that can be used to
      retrieve the status of an export job. Example: `/organizations/myorg/env
      ironments/myenv/analytics/exports/9cfc0d85-0f30-46d6-ae6f-318d0cb961bd`
    state: Output only. Status of the export job. Valid values include
      `enqueued`, `running`, `completed`, and `failed`.
    updated: Output only. Time the export job was last updated.
  """

  created = _messages.StringField(1)
  datastoreName = _messages.StringField(2)
  description = _messages.StringField(3)
  error = _messages.StringField(4)
  executionTime = _messages.StringField(5)
  name = _messages.StringField(6)
  self = _messages.StringField(7)
  state = _messages.StringField(8)
  updated = _messages.StringField(9)


class GoogleCloudApigeeV1ExportRequest(_messages.Message):
  r"""Request body for [CreateExportRequest]

  Fields:
    csvDelimiter: Optional. Delimiter used in the CSV file, if `outputFormat`
      is set to `csv`. Defaults to the `,` (comma) character. Supported
      delimiter characters include comma (`,`), pipe (`|`), and tab (`\t`).
    datastoreName: Required. Name of the preconfigured datastore.
    dateRange: Required. Date range of the data to export.
    description: Optional. Description of the export job.
    name: Required. Display name of the export job.
    outputFormat: Optional. Output format of the export. Valid values include:
      `csv` or `json`. Defaults to `json`. Note: Configure the delimiter for
      CSV output using the `csvDelimiter` property.
  """

  csvDelimiter = _messages.StringField(1)
  datastoreName = _messages.StringField(2)
  dateRange = _messages.MessageField('GoogleCloudApigeeV1DateRange', 3)
  description = _messages.StringField(4)
  name = _messages.StringField(5)
  outputFormat = _messages.StringField(6)


class GoogleCloudApigeeV1FlowHook(_messages.Message):
  r"""A GoogleCloudApigeeV1FlowHook object.

  Fields:
    continueOnError: Optional. Flag that specifies whether execution should
      continue if the flow hook throws an exception. Set to `true` to continue
      execution. Set to `false` to stop execution if the flow hook throws an
      exception. Defaults to `true`.
    description: Description of the flow hook.
    flowHookPoint: Output only. Where in the API call flow the flow hook is
      invoked. Must be one of `PreProxyFlowHook`, `PostProxyFlowHook`,
      `PreTargetFlowHook`, or `PostTargetFlowHook`.
    sharedFlow: Shared flow attached to this flow hook, or empty if there is
      none attached.
  """

  continueOnError = _messages.BooleanField(1)
  description = _messages.StringField(2)
  flowHookPoint = _messages.StringField(3)
  sharedFlow = _messages.StringField(4)


class GoogleCloudApigeeV1FlowHookConfig(_messages.Message):
  r"""A GoogleCloudApigeeV1FlowHookConfig object.

  Fields:
    continueOnError: Flag that specifies whether the flow should abort after
      an error in the flow hook. Defaults to `true` (continue on error).
    name: Name of the flow hook in the following format:
      `organizations/{org}/environments/{env}/flowhooks/{point}`. Valid
      `point` values include: `PreProxyFlowHook`, `PostProxyFlowHook`,
      `PreTargetFlowHook`, and `PostTargetFlowHook`
    sharedFlowName: Name of the shared flow to invoke in the following format:
      `organizations/{org}/sharedflows/{sharedflow}`
  """

  continueOnError = _messages.BooleanField(1)
  name = _messages.StringField(2)
  sharedFlowName = _messages.StringField(3)


class GoogleCloudApigeeV1GenerateDownloadUrlRequest(_messages.Message):
  r"""Request for GenerateDownloadUrl method."""


class GoogleCloudApigeeV1GenerateDownloadUrlResponse(_messages.Message):
  r"""Response for GenerateDownloadUrl method.

  Fields:
    downloadUri: The Google Cloud Storage signed URL that can be used to
      download the Archive zip file.
  """

  downloadUri = _messages.StringField(1)


class GoogleCloudApigeeV1GenerateUploadUrlRequest(_messages.Message):
  r"""Request for GenerateUploadUrl method."""


class GoogleCloudApigeeV1GenerateUploadUrlResponse(_messages.Message):
  r"""Response for GenerateUploadUrl method.

  Fields:
    uploadUri: The Google Cloud Storage signed URL that can be used to upload
      a new Archive zip file.
  """

  uploadUri = _messages.StringField(1)


class GoogleCloudApigeeV1GetAsyncQueryResultUrlResponse(_messages.Message):
  r"""The response for GetAsyncQueryResultUrl

  Fields:
    urls: The list of Signed URLs generated by the CreateAsyncQuery request
  """

  urls = _messages.MessageField('GoogleCloudApigeeV1GetAsyncQueryResultUrlResponseURLInfo', 1, repeated=True)


class GoogleCloudApigeeV1GetAsyncQueryResultUrlResponseURLInfo(_messages.Message):
  r"""A Signed URL and the relevant metadata associated with it.

  Fields:
    md5: The MD5 Hash of the JSON data
    sizeBytes: The size of the returned file in bytes
    uri: The signed URL of the JSON data. Will be of the form
      `https://storage.googleapis.com/example-bucket/cat.jpeg?X-Goog-
      Algorithm= GOOG4-RSA-SHA256&X-Goog-Credential=example%40example-
      project.iam.gserviceaccount .com%2F20181026%2Fus-
      central1%2Fstorage%2Fgoog4_request&X-Goog-Date=20181026T18 1309Z&X-Goog-
      Expires=900&X-Goog-SignedHeaders=host&X-Goog-Signature=247a2aa45f16 9edf
      4d187d54e7cc46e4731b1e6273242c4f4c39a1d2507a0e58706e25e3a85a7dbb891d62af
      a849 6def8e260c1db863d9ace85ff0a184b894b117fe46d1225c82f2aa19efd52cf21d3
      e2022b3b868dc c1aca2741951ed5bf3bb25a34f5e9316a2841e8ff4c530b22ceaa1c5ce
      09c7cbb5732631510c2058 0e61723f5594de3aea497f195456a2ff2bdd0d13bad47289d
      8611b6f9cfeef0c46c91a455b94e90a 66924f722292d21e24d31dcfb38ce0c0f353ffa5
      a9756fc2a9f2b40bc2113206a81e324fc4fd6823 a29163fa845c8ae7eca1fcf6e5bb48b
      3200983c56c5ca81fffb151cca7402beddfc4a76b13344703 2ea7abedc098d2eb14a7`
  """

  md5 = _messages.StringField(1)
  sizeBytes = _messages.IntegerField(2)
  uri = _messages.StringField(3)


class GoogleCloudApigeeV1GetSyncAuthorizationRequest(_messages.Message):
  r"""Request for GetSyncAuthorization."""


class GoogleCloudApigeeV1GraphQLOperation(_messages.Message):
  r"""Represents the pairing of GraphQL operation types and the GraphQL
  operation name.

  Fields:
    operation: GraphQL operation name. The name and operation type will be
      used to apply quotas. If no name is specified, the quota will be applied
      to all GraphQL operations irrespective of their operation names in the
      payload.
    operationTypes: Required. GraphQL operation types. Valid values include
      `query` or `mutation`. **Note**: Apigee does not currently support
      `subscription` types.
  """

  operation = _messages.StringField(1)
  operationTypes = _messages.StringField(2, repeated=True)


class GoogleCloudApigeeV1GraphQLOperationConfig(_messages.Message):
  r"""Binds the resources in a proxy or remote service with the GraphQL
  operation and its associated quota enforcement.

  Fields:
    apiSource: Required. Name of the API proxy endpoint or remote service with
      which the GraphQL operation and quota are associated.
    attributes: Custom attributes associated with the operation.
    operations: Required. List of GraphQL name/operation type pairs for the
      proxy or remote service to which quota will be applied. If only
      operation types are specified, the quota will be applied to all GraphQL
      requests irrespective of the GraphQL name. **Note**: Currently, you can
      specify only a single GraphQLOperation. Specifying more than one will
      cause the operation to fail.
    quota: Quota parameters to be enforced for the resources, methods, and API
      source combination. If none are specified, quota enforcement will not be
      done.
  """

  apiSource = _messages.StringField(1)
  attributes = _messages.MessageField('GoogleCloudApigeeV1Attribute', 2, repeated=True)
  operations = _messages.MessageField('GoogleCloudApigeeV1GraphQLOperation', 3, repeated=True)
  quota = _messages.MessageField('GoogleCloudApigeeV1Quota', 4)


class GoogleCloudApigeeV1GraphQLOperationGroup(_messages.Message):
  r"""List of graphQL operation configuration details associated with Apigee
  API proxies or remote services. Remote services are non-Apigee proxies, such
  as Istio-Envoy.

  Fields:
    operationConfigType: Flag that specifies whether the configuration is for
      Apigee API proxy or a remote service. Valid values include `proxy` or
      `remoteservice`. Defaults to `proxy`. Set to `proxy` when Apigee API
      proxies are associated with the API product. Set to `remoteservice` when
      non-Apigee proxies like Istio-Envoy are associated with the API product.
    operationConfigs: Required. List of operation configurations for either
      Apigee API proxies or other remote services that are associated with
      this API product.
  """

  operationConfigType = _messages.StringField(1)
  operationConfigs = _messages.MessageField('GoogleCloudApigeeV1GraphQLOperationConfig', 2, repeated=True)


class GoogleCloudApigeeV1GrpcOperationConfig(_messages.Message):
  r"""Binds the resources in a proxy or remote service with the gRPC operation
  and its associated quota enforcement.

  Fields:
    apiSource: Required. Name of the API proxy with which the gRPC operation
      and quota are associated.
    attributes: Custom attributes associated with the operation.
    methods: List of unqualified gRPC method names for the proxy to which
      quota will be applied. If this field is empty, the Quota will apply to
      all operations on the gRPC service defined on the proxy. Example: Given
      a proxy that is configured to serve com.petstore.PetService, the methods
      com.petstore.PetService.ListPets and com.petstore.PetService.GetPet
      would be specified here as simply ["ListPets", "GetPet"].
    quota: Quota parameters to be enforced for the methods and API source
      combination. If none are specified, quota enforcement will not be done.
    service: Required. gRPC Service name associated to be associated with the
      API proxy, on which quota rules can be applied upon.
  """

  apiSource = _messages.StringField(1)
  attributes = _messages.MessageField('GoogleCloudApigeeV1Attribute', 2, repeated=True)
  methods = _messages.StringField(3, repeated=True)
  quota = _messages.MessageField('GoogleCloudApigeeV1Quota', 4)
  service = _messages.StringField(5)


class GoogleCloudApigeeV1GrpcOperationGroup(_messages.Message):
  r"""List of gRPC operation configuration details associated with Apigee API
  proxies.

  Fields:
    operationConfigs: Required. List of operation configurations for either
      Apigee API proxies that are associated with this API product.
  """

  operationConfigs = _messages.MessageField('GoogleCloudApigeeV1GrpcOperationConfig', 1, repeated=True)


class GoogleCloudApigeeV1IngressConfig(_messages.Message):
  r"""A GoogleCloudApigeeV1IngressConfig object.

  Fields:
    environmentGroups: List of environment groups in the organization.
    name: Name of the resource in the following format:
      `organizations/{org}/deployedIngressConfig`.
    revisionCreateTime: Time at which the IngressConfig revision was created.
    revisionId: Revision id that defines the ordering on IngressConfig
      resources. The higher the revision, the more recently the configuration
      was deployed.
    uid: A unique id for the ingress config that will only change if the
      organization is deleted and recreated.
  """

  environmentGroups = _messages.MessageField('GoogleCloudApigeeV1EnvironmentGroupConfig', 1, repeated=True)
  name = _messages.StringField(2)
  revisionCreateTime = _messages.StringField(3)
  revisionId = _messages.IntegerField(4)
  uid = _messages.StringField(5)


class GoogleCloudApigeeV1Instance(_messages.Message):
  r"""Apigee runtime instance.

  Enums:
    PeeringCidrRangeValueValuesEnum: Optional. Size of the CIDR block range
      that will be reserved by the instance. PAID organizations support
      `SLASH_16` to `SLASH_20` and defaults to `SLASH_16`. Evaluation
      organizations support only `SLASH_23`.
    StateValueValuesEnum: Output only. State of the instance. Values other
      than `ACTIVE` means the resource is not ready to use.

  Messages:
    LabelsValue: Optional. Labels associated with the instance.

  Fields:
    accessLoggingConfig: Optional. Access logging configuration enables the
      access logging feature at the instance. Apigee customers can enable
      access logging to ship the access logs to their own project's cloud
      logging.
    consumerAcceptList: Optional. Customer accept list represents the list of
      projects (id/number) on customer side that can privately connect to the
      service attachment. It is an optional field which the customers can
      provide during the instance creation. By default, the customer project
      associated with the Apigee organization will be included to the list.
    createdAt: Output only. Time the instance was created in milliseconds
      since epoch.
    description: Optional. Description of the instance.
    diskEncryptionKeyName: Customer Managed Encryption Key (CMEK) used for
      disk and volume encryption. Required for Apigee paid subscriptions only.
      Use the following format:
      `projects/([^/]+)/locations/([^/]+)/keyRings/([^/]+)/cryptoKeys/([^/]+)`
    displayName: Optional. Display name for the instance.
    externalHost: Output only. External hostname or IP address of the Apigee
      endpoint used by clients to connect to the service.
    externalHostEnabled: Optional. Flag that specifies whether the external
      Apigee endpoint is enabled for the instance.
    host: Output only. Internal hostname or IP address of the Apigee endpoint
      used by clients to connect to the service.
    ipRange: Optional. Comma-separated list of CIDR blocks of length 22 and/or
      28 used to create the Apigee instance. Providing CIDR ranges is
      optional. You can provide just /22 or /28 or both (or neither). Ranges
      you provide should be freely available as part of a larger named range
      you have allocated to the Service Networking peering. If this parameter
      is not provided, Apigee automatically requests an available /22 and /28
      CIDR block from Service Networking. Use the /22 CIDR block for
      configuring your firewall needs to allow traffic from Apigee. Input
      formats: `a.b.c.d/22` or `e.f.g.h/28` or `a.b.c.d/22,e.f.g.h/28`
    labels: Optional. Labels associated with the instance.
    lastModifiedAt: Output only. Time the instance was last modified in
      milliseconds since epoch.
    location: Required. Compute Engine location where the instance resides.
    name: Required. Resource ID of the instance. Values must match the regular
      expression `^a-z{0,30}[a-z\d]$`.
    nodeConfig: Optional. NodeConfig of the instance.
    peeringCidrRange: Optional. Size of the CIDR block range that will be
      reserved by the instance. PAID organizations support `SLASH_16` to
      `SLASH_20` and defaults to `SLASH_16`. Evaluation organizations support
      only `SLASH_23`.
    port: Output only. Port number of the exposed Apigee endpoint.
    runtimeVersion: Output only. Version of the runtime system running in the
      instance. The runtime system is the set of components that serve the API
      Proxy traffic in your Environments.
    serviceAttachment: Output only. Resource name of the service attachment
      created for the instance in the format:
      `projects/*/regions/*/serviceAttachments/*` Apigee customers can
      privately forward traffic to this service attachment using the PSC
      endpoints.
    state: Output only. State of the instance. Values other than `ACTIVE`
      means the resource is not ready to use.
  """

  class PeeringCidrRangeValueValuesEnum(_messages.Enum):
    r"""Optional. Size of the CIDR block range that will be reserved by the
    instance. PAID organizations support `SLASH_16` to `SLASH_20` and defaults
    to `SLASH_16`. Evaluation organizations support only `SLASH_23`.

    Values:
      CIDR_RANGE_UNSPECIFIED: Range not specified.
      SLASH_16: `/16` CIDR range.
      SLASH_17: `/17` CIDR range.
      SLASH_18: `/18` CIDR range.
      SLASH_19: `/19` CIDR range.
      SLASH_20: `/20` CIDR range.
      SLASH_22: `/22` CIDR range. Supported for evaluation only.
      SLASH_23: `/23` CIDR range. Supported for evaluation only.
    """
    CIDR_RANGE_UNSPECIFIED = 0
    SLASH_16 = 1
    SLASH_17 = 2
    SLASH_18 = 3
    SLASH_19 = 4
    SLASH_20 = 5
    SLASH_22 = 6
    SLASH_23 = 7

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the instance. Values other than `ACTIVE` means
    the resource is not ready to use.

    Values:
      STATE_UNSPECIFIED: Resource is in an unspecified state.
      CREATING: Resource is being created.
      ACTIVE: Resource is provisioned and ready to use.
      DELETING: The resource is being deleted.
      UPDATING: The resource is being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    UPDATING = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels associated with the instance.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  accessLoggingConfig = _messages.MessageField('GoogleCloudApigeeV1AccessLoggingConfig', 1)
  consumerAcceptList = _messages.StringField(2, repeated=True)
  createdAt = _messages.IntegerField(3)
  description = _messages.StringField(4)
  diskEncryptionKeyName = _messages.StringField(5)
  displayName = _messages.StringField(6)
  externalHost = _messages.StringField(7)
  externalHostEnabled = _messages.BooleanField(8)
  host = _messages.StringField(9)
  ipRange = _messages.StringField(10)
  labels = _messages.MessageField('LabelsValue', 11)
  lastModifiedAt = _messages.IntegerField(12)
  location = _messages.StringField(13)
  name = _messages.StringField(14)
  nodeConfig = _messages.MessageField('GoogleCloudApigeeV1NodeConfig', 15)
  peeringCidrRange = _messages.EnumField('PeeringCidrRangeValueValuesEnum', 16)
  port = _messages.StringField(17)
  runtimeVersion = _messages.StringField(18)
  serviceAttachment = _messages.StringField(19)
  state = _messages.EnumField('StateValueValuesEnum', 20)


class GoogleCloudApigeeV1InstanceAttachment(_messages.Message):
  r"""InstanceAttachment represents the installation of an environment onto an
  instance.

  Enums:
    StateValueValuesEnum: Output only. State of the instance attachment.
      Values other than ACTIVE means the resource is not ready to use.

  Fields:
    createdAt: Output only. Time the attachment was created in milliseconds
      since epoch.
    environment: ID of the attached environment.
    name: Output only. ID of the attachment.
    state: Output only. State of the instance attachment. Values other than
      ACTIVE means the resource is not ready to use.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the instance attachment. Values other than
    ACTIVE means the resource is not ready to use.

    Values:
      STATE_UNSPECIFIED: Resource is in an unspecified state.
      CREATING: Resource is being created.
      ACTIVE: Resource is provisioned and ready to use.
      DELETING: The resource is being deleted.
      UPDATING: The resource is being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    UPDATING = 4

  createdAt = _messages.IntegerField(1)
  environment = _messages.StringField(2)
  name = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class GoogleCloudApigeeV1InstanceDeploymentStatus(_messages.Message):
  r"""The status of a deployment as reported by a single instance.

  Fields:
    deployedRevisions: Revisions currently deployed in MPs.
    deployedRoutes: Current routes deployed in the ingress routing table. A
      route which is missing will appear in `missing_routes`.
    instance: ID of the instance reporting the status.
  """

  deployedRevisions = _messages.MessageField('GoogleCloudApigeeV1InstanceDeploymentStatusDeployedRevision', 1, repeated=True)
  deployedRoutes = _messages.MessageField('GoogleCloudApigeeV1InstanceDeploymentStatusDeployedRoute', 2, repeated=True)
  instance = _messages.StringField(3)


class GoogleCloudApigeeV1InstanceDeploymentStatusDeployedRevision(_messages.Message):
  r"""Revisions deployed in the MPs.

  Fields:
    percentage: Percentage of MP replicas reporting this revision.
    revision: API proxy revision reported as deployed.
  """

  percentage = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  revision = _messages.StringField(2)


class GoogleCloudApigeeV1InstanceDeploymentStatusDeployedRoute(_messages.Message):
  r"""Route deployed in the ingress routing table.

  Fields:
    basepath: Base path in the routing table.
    envgroup: Environment group where this route is installed.
    environment: Destination environment. This will be empty if the route is
      not yet reported.
    percentage: Percentage of ingress replicas reporting this route.
  """

  basepath = _messages.StringField(1)
  envgroup = _messages.StringField(2)
  environment = _messages.StringField(3)
  percentage = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleCloudApigeeV1IntegrationConfig(_messages.Message):
  r"""Configuration for the Integration add-on.

  Fields:
    enabled: Flag that specifies whether the Integration add-on is enabled.
    expiresAt: Output only. Time at which the Integration add-on expires in in
      milliseconds since epoch. If unspecified, the add-on will never expire.
  """

  enabled = _messages.BooleanField(1)
  expiresAt = _messages.IntegerField(2)


class GoogleCloudApigeeV1KeyAliasReference(_messages.Message):
  r"""A GoogleCloudApigeeV1KeyAliasReference object.

  Fields:
    aliasId: Alias ID. Must exist in the keystore referred to by the
      reference.
    reference: Reference name in the following format:
      `organizations/{org}/environments/{env}/references/{reference}`
  """

  aliasId = _messages.StringField(1)
  reference = _messages.StringField(2)


class GoogleCloudApigeeV1KeyValueEntry(_messages.Message):
  r"""Key value map pair where the value represents the data associated with
  the corresponding key. **Note**: Supported for Apigee hybrid 1.8.x and
  higher.

  Fields:
    name: Resource URI that can be used to identify the scope of the key value
      map entries.
    value: Required. Data or payload that is being retrieved and associated
      with the unique key.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleCloudApigeeV1KeyValueMap(_messages.Message):
  r"""Collection of key/value string pairs.

  Fields:
    encrypted: Required. Flag that specifies whether entry values will be
      encrypted. This field is retained for backward compatibility and the
      value of encrypted will always be `true`. Apigee X and hybrid do not
      support unencrypted key value maps.
    name: Required. ID of the key value map.
    resourceName: Output only. Resource URI on which the key value map is
      based.
  """

  encrypted = _messages.BooleanField(1)
  name = _messages.StringField(2)
  resourceName = _messages.StringField(3)


class GoogleCloudApigeeV1Keystore(_messages.Message):
  r"""Datastore for Certificates and Aliases.

  Fields:
    aliases: Output only. Aliases in this keystore.
    name: Required. Resource ID for this keystore. Values must match the
      regular expression `[\w[:space:].-]{1,255}`.
  """

  aliases = _messages.StringField(1, repeated=True)
  name = _messages.StringField(2)


class GoogleCloudApigeeV1KeystoreConfig(_messages.Message):
  r"""A GoogleCloudApigeeV1KeystoreConfig object.

  Fields:
    aliases: Aliases in the keystore.
    name: Resource name in the following format:
      `organizations/{org}/environments/{env}/keystores/{keystore}`
  """

  aliases = _messages.MessageField('GoogleCloudApigeeV1AliasRevisionConfig', 1, repeated=True)
  name = _messages.StringField(2)


class GoogleCloudApigeeV1ListApiCategoriesResponse(_messages.Message):
  r"""the response for ListApiCategoriesRequest.

  Fields:
    data: Details of categories.
    errorCode: ID that can be used to find errors in the log files.
    message: Description of the operation.
    requestId: ID that can be used to find request details in the log files.
    status: Status of the operation.
  """

  data = _messages.MessageField('GoogleCloudApigeeV1ApiCategoryData', 1, repeated=True)
  errorCode = _messages.StringField(2)
  message = _messages.StringField(3)
  requestId = _messages.StringField(4)
  status = _messages.StringField(5)


class GoogleCloudApigeeV1ListApiProductsResponse(_messages.Message):
  r"""A GoogleCloudApigeeV1ListApiProductsResponse object.

  Fields:
    apiProduct: Lists all API product names defined for an organization.
    nextPageToken: Token that can be sent as `next_page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    totalSize: Total count of API products for this org.
  """

  apiProduct = _messages.MessageField('GoogleCloudApigeeV1ApiProduct', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  totalSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleCloudApigeeV1ListApiProxiesResponse(_messages.Message):
  r"""To change this message, in the same CL add a change log in go/changing-
  api-proto-breaks-ui

  Fields:
    proxies: A GoogleCloudApigeeV1ApiProxy attribute.
  """

  proxies = _messages.MessageField('GoogleCloudApigeeV1ApiProxy', 1, repeated=True)


class GoogleCloudApigeeV1ListAppGroupAppsResponse(_messages.Message):
  r"""Response for ListAppGroupApps

  Fields:
    appGroupApps: List of AppGroup apps and their credentials.
    nextPageToken: Token that can be sent as `next_page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  appGroupApps = _messages.MessageField('GoogleCloudApigeeV1AppGroupApp', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApigeeV1ListAppGroupsResponse(_messages.Message):
  r"""ListAppGroupsResponse contains the 0 or more AppGroups, along with the
  optional page token and the total count of apps.

  Fields:
    appGroups: List of AppGroups.
    nextPageToken: Token that can be sent as `next_page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    totalSize: Total count of AppGroups.
  """

  appGroups = _messages.MessageField('GoogleCloudApigeeV1AppGroup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  totalSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleCloudApigeeV1ListAppsResponse(_messages.Message):
  r"""A GoogleCloudApigeeV1ListAppsResponse object.

  Fields:
    app: A GoogleCloudApigeeV1App attribute.
    nextPageToken: Token that can be sent as `next_page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    totalSize: Total count of Apps.
  """

  app = _messages.MessageField('GoogleCloudApigeeV1App', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  totalSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleCloudApigeeV1ListArchiveDeploymentsResponse(_messages.Message):
  r"""Response for ListArchiveDeployments method.

  Fields:
    archiveDeployments: Archive Deployments in the specified environment.
    nextPageToken: Page token that you can include in a ListArchiveDeployments
      request to retrieve the next page. If omitted, no subsequent pages
      exist.
  """

  archiveDeployments = _messages.MessageField('GoogleCloudApigeeV1ArchiveDeployment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApigeeV1ListAsyncQueriesResponse(_messages.Message):
  r"""The response for ListAsyncQueries.

  Fields:
    queries: The asynchronous queries belong to requested resource name.
  """

  queries = _messages.MessageField('GoogleCloudApigeeV1AsyncQuery', 1, repeated=True)


class GoogleCloudApigeeV1ListCustomReportsResponse(_messages.Message):
  r"""This message encapsulates a list of custom report definitions

  Fields:
    qualifier: A GoogleCloudApigeeV1CustomReport attribute.
  """

  qualifier = _messages.MessageField('GoogleCloudApigeeV1CustomReport', 1, repeated=True)


class GoogleCloudApigeeV1ListDataCollectorsResponse(_messages.Message):
  r"""Response for ListDataCollectors.

  Fields:
    dataCollectors: Data collectors in the specified organization.
    nextPageToken: Page token that you can include in a ListDataCollectors
      request to retrieve the next page. If omitted, no subsequent pages
      exist.
  """

  dataCollectors = _messages.MessageField('GoogleCloudApigeeV1DataCollector', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApigeeV1ListDatastoresResponse(_messages.Message):
  r"""The response for ListDatastores

  Fields:
    datastores: A list of datastores
  """

  datastores = _messages.MessageField('GoogleCloudApigeeV1Datastore', 1, repeated=True)


class GoogleCloudApigeeV1ListDebugSessionsResponse(_messages.Message):
  r"""A GoogleCloudApigeeV1ListDebugSessionsResponse object.

  Fields:
    nextPageToken: Page token that you can include in a
      ListDebugSessionsRequest to retrieve the next page. If omitted, no
      subsequent pages exist.
    sessions: Session info that includes debug session ID and the first
      transaction creation timestamp.
  """

  nextPageToken = _messages.StringField(1)
  sessions = _messages.MessageField('GoogleCloudApigeeV1Session', 2, repeated=True)


class GoogleCloudApigeeV1ListDeploymentsResponse(_messages.Message):
  r"""A GoogleCloudApigeeV1ListDeploymentsResponse object.

  Fields:
    deployments: List of deployments.
  """

  deployments = _messages.MessageField('GoogleCloudApigeeV1Deployment', 1, repeated=True)


class GoogleCloudApigeeV1ListDeveloperAppsResponse(_messages.Message):
  r"""A GoogleCloudApigeeV1ListDeveloperAppsResponse object.

  Fields:
    app: List of developer apps and their credentials.
  """

  app = _messages.MessageField('GoogleCloudApigeeV1DeveloperApp', 1, repeated=True)


class GoogleCloudApigeeV1ListDeveloperSubscriptionsResponse(_messages.Message):
  r"""Response for ListDeveloperSubscriptions.

  Fields:
    developerSubscriptions: List of all subscriptions.
    nextStartKey: Value that can be sent as `startKey` to retrieve the next
      page of content. If this field is omitted, there are no subsequent
      pages.
  """

  developerSubscriptions = _messages.MessageField('GoogleCloudApigeeV1DeveloperSubscription', 1, repeated=True)
  nextStartKey = _messages.StringField(2)


class GoogleCloudApigeeV1ListEndpointAttachmentsResponse(_messages.Message):
  r"""Response for ListEndpointAttachments method.

  Fields:
    endpointAttachments: Endpoint attachments in the specified organization.
    nextPageToken: Page token that you can include in an
      `ListEndpointAttachments` request to retrieve the next page. If omitted,
      no subsequent pages exist.
  """

  endpointAttachments = _messages.MessageField('GoogleCloudApigeeV1EndpointAttachment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApigeeV1ListEnvironmentGroupAttachmentsResponse(_messages.Message):
  r"""Response for ListEnvironmentGroupAttachments.

  Fields:
    environmentGroupAttachments: EnvironmentGroupAttachments for the specified
      environment group.
    nextPageToken: Page token that you can include in a
      ListEnvironmentGroupAttachments request to retrieve the next page. If
      omitted, no subsequent pages exist.
  """

  environmentGroupAttachments = _messages.MessageField('GoogleCloudApigeeV1EnvironmentGroupAttachment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApigeeV1ListEnvironmentGroupsResponse(_messages.Message):
  r"""Response for ListEnvironmentGroups.

  Fields:
    environmentGroups: EnvironmentGroups in the specified organization.
    nextPageToken: Page token that you can include in a ListEnvironmentGroups
      request to retrieve the next page. If omitted, no subsequent pages
      exist.
  """

  environmentGroups = _messages.MessageField('GoogleCloudApigeeV1EnvironmentGroup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApigeeV1ListEnvironmentResourcesResponse(_messages.Message):
  r"""Response for ListEnvironmentResources

  Fields:
    resourceFile: List of resources files.
  """

  resourceFile = _messages.MessageField('GoogleCloudApigeeV1ResourceFile', 1, repeated=True)


class GoogleCloudApigeeV1ListExportsResponse(_messages.Message):
  r"""The response for ListExports

  Fields:
    exports: Details of the export jobs.
  """

  exports = _messages.MessageField('GoogleCloudApigeeV1Export', 1, repeated=True)


class GoogleCloudApigeeV1ListHybridIssuersResponse(_messages.Message):
  r"""A GoogleCloudApigeeV1ListHybridIssuersResponse object.

  Fields:
    issuers: Lists of hybrid services and its trusted issuer email ids.
  """

  issuers = _messages.MessageField('GoogleCloudApigeeV1ServiceIssuersMapping', 1, repeated=True)


class GoogleCloudApigeeV1ListInstanceAttachmentsResponse(_messages.Message):
  r"""Response for ListInstanceAttachments.

  Fields:
    attachments: Attachments for the instance.
    nextPageToken: Page token that you can include in a
      ListInstanceAttachments request to retrieve the next page of content. If
      omitted, no subsequent pages exist.
  """

  attachments = _messages.MessageField('GoogleCloudApigeeV1InstanceAttachment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApigeeV1ListInstancesResponse(_messages.Message):
  r"""Response for ListInstances.

  Fields:
    instances: Instances in the specified organization.
    nextPageToken: Page token that you can include in a ListInstance request
      to retrieve the next page of content. If omitted, no subsequent pages
      exist.
  """

  instances = _messages.MessageField('GoogleCloudApigeeV1Instance', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApigeeV1ListKeyValueEntriesResponse(_messages.Message):
  r"""The request structure for listing key value map keys and its
  corresponding values.

  Fields:
    keyValueEntries: One or more key value map keys and values.
    nextPageToken: Token that can be sent as `next_page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
  """

  keyValueEntries = _messages.MessageField('GoogleCloudApigeeV1KeyValueEntry', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApigeeV1ListNatAddressesResponse(_messages.Message):
  r"""Response for ListNatAddresses.

  Fields:
    natAddresses: List of NAT Addresses for the instance.
    nextPageToken: Page token that you can include in a ListNatAddresses
      request to retrieve the next page of content. If omitted, no subsequent
      pages exist.
  """

  natAddresses = _messages.MessageField('GoogleCloudApigeeV1NatAddress', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class GoogleCloudApigeeV1ListOfDevelopersResponse(_messages.Message):
  r"""A GoogleCloudApigeeV1ListOfDevelopersResponse object.

  Fields:
    developer: List of developers.
    nextPageToken: Token that can be sent as `next_page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    totalSize: Total count of Developers.
  """

  developer = _messages.MessageField('GoogleCloudApigeeV1Developer', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  totalSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class GoogleCloudApigeeV1ListOrganizationsResponse(_messages.Message):
  r"""A GoogleCloudApigeeV1ListOrganizationsResponse object.

  Fields:
    organizations: List of Apigee organizations and associated Google Cloud
      projects.
  """

  organizations = _messages.MessageField('GoogleCloudApigeeV1OrganizationProjectMapping', 1, repeated=True)


class GoogleCloudApigeeV1ListRatePlansResponse(_messages.Message):
  r"""Response for ListRatePlans.

  Fields:
    nextStartKey: Value that can be sent as `startKey` to retrieve the next
      page of content. If this field is omitted, there are no subsequent
      pages.
    ratePlans: List of rate plans in an organization.
  """

  nextStartKey = _messages.StringField(1)
  ratePlans = _messages.MessageField('GoogleCloudApigeeV1RatePlan', 2, repeated=True)


class GoogleCloudApigeeV1ListSecurityActionsResponse(_messages.Message):
  r"""Contains a list of SecurityActions in response to a
  ListSecurityActionRequest.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    securityActions: The SecurityActions for the specified environment.
  """

  nextPageToken = _messages.StringField(1)
  securityActions = _messages.MessageField('GoogleCloudApigeeV1SecurityAction', 2, repeated=True)


class GoogleCloudApigeeV1ListSecurityIncidentEnvironmentsResponse(_messages.Message):
  r"""Response for ListEnvironmentSecurityIncident.

  Fields:
    nextPageToken: Output only. A token that can be sent as `page_token` to
      retrieve the next page. If this field is omitted, there are no
      subsequent pages.
    securityIncidentEnvironments: List of environments with security incident
      stats.
  """

  nextPageToken = _messages.StringField(1)
  securityIncidentEnvironments = _messages.MessageField('GoogleCloudApigeeV1SecurityIncidentEnvironment', 2, repeated=True)


class GoogleCloudApigeeV1ListSecurityIncidentsResponse(_messages.Message):
  r"""Response for ListSecurityIncidents.

  Fields:
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    securityIncidents: List of security incidents in the organization
  """

  nextPageToken = _messages.StringField(1)
  securityIncidents = _messages.MessageField('GoogleCloudApigeeV1SecurityIncident', 2, repeated=True)


class GoogleCloudApigeeV1ListSecurityProfileRevisionsResponse(_messages.Message):
  r"""Response for ListSecurityProfileRevisions.

  Fields:
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    securityProfiles: List of security profile revisions. The revisions may be
      attached or unattached to any environment.
  """

  nextPageToken = _messages.StringField(1)
  securityProfiles = _messages.MessageField('GoogleCloudApigeeV1SecurityProfile', 2, repeated=True)


class GoogleCloudApigeeV1ListSecurityProfilesResponse(_messages.Message):
  r"""Response for ListSecurityProfiles.

  Fields:
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    securityProfiles: List of security profiles in the organization. The
      profiles may be attached or unattached to any environment. This will
      return latest revision of each profile.
  """

  nextPageToken = _messages.StringField(1)
  securityProfiles = _messages.MessageField('GoogleCloudApigeeV1SecurityProfile', 2, repeated=True)


class GoogleCloudApigeeV1ListSecurityReportsResponse(_messages.Message):
  r"""The response for SecurityReports.

  Fields:
    nextPageToken: If the number of security reports exceeded the page size
      requested, the token can be used to fetch the next page in a subsequent
      call. If the response is the last page and there are no more reports to
      return this field is left empty.
    securityReports: The security reports belong to requested resource name.
  """

  nextPageToken = _messages.StringField(1)
  securityReports = _messages.MessageField('GoogleCloudApigeeV1SecurityReport', 2, repeated=True)


class GoogleCloudApigeeV1ListSharedFlowsResponse(_messages.Message):
  r"""To change this message, in the same CL add a change log in go/changing-
  api-proto-breaks-ui

  Fields:
    sharedFlows: A GoogleCloudApigeeV1SharedFlow attribute.
  """

  sharedFlows = _messages.MessageField('GoogleCloudApigeeV1SharedFlow', 1, repeated=True)


class GoogleCloudApigeeV1ListTraceConfigOverridesResponse(_messages.Message):
  r"""Response for ListTraceConfigOverrides.

  Fields:
    nextPageToken: Token value that can be passed as `page_token` to retrieve
      the next page of content.
    traceConfigOverrides: List all trace configuration overrides in an
      environment.
  """

  nextPageToken = _messages.StringField(1)
  traceConfigOverrides = _messages.MessageField('GoogleCloudApigeeV1TraceConfigOverride', 2, repeated=True)


class GoogleCloudApigeeV1Metadata(_messages.Message):
  r"""Encapsulates additional information about query execution.

  Fields:
    errors: List of error messages as strings.
    notices: List of additional information such as data source, if result was
      truncated. For example: ``` "notices": [ "Source:Postgres", "PG
      Host:uappg0rw.e2e.apigeeks.net", "query served
      by:4b64601e-40de-4eb1-bfb9-eeee7ac929ed", "Table used:
      edge.api.uapgroup2.agg_api" ]```
  """

  errors = _messages.StringField(1, repeated=True)
  notices = _messages.StringField(2, repeated=True)


class GoogleCloudApigeeV1Metric(_messages.Message):
  r"""Encapsulates the metric data point. For example: ```{ "name":
  "sum(message_count)", "values" : [ { "timestamp": 1549004400000, "value":
  "39.0" }, { "timestamp" : 1548997200000, "value" : "0.0" } ] }``` or ```{
  "name": "sum(message_count)", "values" : ["39.0"] }```

  Fields:
    name: Metric name.
    values: List of metric values. Possible value formats include:
      `"values":["39.0"]` or `"values":[ { "value": "39.0", "timestamp":
      1232434354} ]`
  """

  name = _messages.StringField(1)
  values = _messages.MessageField('extra_types.JsonValue', 2, repeated=True)


class GoogleCloudApigeeV1MetricAggregation(_messages.Message):
  r"""The optionally aggregated metric to query with its ordering.

  Enums:
    AggregationValueValuesEnum: Aggregation function associated with the
      metric.
    OrderValueValuesEnum: Ordering for this aggregation in the result. For
      time series this is ignored since the ordering of points depends only on
      the timestamp, not the values.

  Fields:
    aggregation: Aggregation function associated with the metric.
    name: Name of the metric
    order: Ordering for this aggregation in the result. For time series this
      is ignored since the ordering of points depends only on the timestamp,
      not the values.
  """

  class AggregationValueValuesEnum(_messages.Enum):
    r"""Aggregation function associated with the metric.

    Values:
      AGGREGATION_FUNCTION_UNSPECIFIED: Unspecified Aggregation function.
      AVG: Average.
      SUM: Summation.
      MIN: Min.
      MAX: Max.
      COUNT_DISTINCT: Count distinct
    """
    AGGREGATION_FUNCTION_UNSPECIFIED = 0
    AVG = 1
    SUM = 2
    MIN = 3
    MAX = 4
    COUNT_DISTINCT = 5

  class OrderValueValuesEnum(_messages.Enum):
    r"""Ordering for this aggregation in the result. For time series this is
    ignored since the ordering of points depends only on the timestamp, not
    the values.

    Values:
      ORDER_UNSPECIFIED: Unspecified order. Default is Descending.
      ASCENDING: Ascending sort order.
      DESCENDING: Descending sort order.
    """
    ORDER_UNSPECIFIED = 0
    ASCENDING = 1
    DESCENDING = 2

  aggregation = _messages.EnumField('AggregationValueValuesEnum', 1)
  name = _messages.StringField(2)
  order = _messages.EnumField('OrderValueValuesEnum', 3)


class GoogleCloudApigeeV1MonetizationConfig(_messages.Message):
  r"""Configuration for the Monetization add-on.

  Fields:
    enabled: Flag that specifies whether the Monetization add-on is enabled.
    expiresAt: Output only. Time at which the Monetization add-on expires in
      in milliseconds since epoch. If unspecified, the add-on will never
      expire.
  """

  enabled = _messages.BooleanField(1)
  expiresAt = _messages.IntegerField(2)


class GoogleCloudApigeeV1NatAddress(_messages.Message):
  r"""Apigee NAT(network address translation) address. A NAT address is a
  static external IP address used for Internet egress traffic.

  Enums:
    StateValueValuesEnum: Output only. State of the nat address.

  Fields:
    ipAddress: Output only. The static IPV4 address.
    name: Required. Resource ID of the NAT address.
    state: Output only. State of the nat address.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the nat address.

    Values:
      STATE_UNSPECIFIED: The resource is in an unspecified state.
      CREATING: The NAT address is being created.
      RESERVED: The NAT address is reserved but not yet used for Internet
        egress.
      ACTIVE: The NAT address is active and used for Internet egress.
      DELETING: The NAT address is being deleted.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    RESERVED = 2
    ACTIVE = 3
    DELETING = 4

  ipAddress = _messages.StringField(1)
  name = _messages.StringField(2)
  state = _messages.EnumField('StateValueValuesEnum', 3)


class GoogleCloudApigeeV1NodeConfig(_messages.Message):
  r"""NodeConfig for setting the min/max number of nodes associated with the
  environment.

  Fields:
    currentAggregateNodeCount: Output only. The current total number of
      gateway nodes that each environment currently has across all instances.
    maxNodeCount: Optional. The maximum total number of gateway nodes that the
      is reserved for all instances that has the specified environment. If not
      specified, the default is determined by the recommended maximum number
      of nodes for that gateway.
    minNodeCount: Optional. The minimum total number of gateway nodes that the
      is reserved for all instances that has the specified environment. If not
      specified, the default is determined by the recommended minimum number
      of nodes for that gateway.
  """

  currentAggregateNodeCount = _messages.IntegerField(1)
  maxNodeCount = _messages.IntegerField(2)
  minNodeCount = _messages.IntegerField(3)


class GoogleCloudApigeeV1Operation(_messages.Message):
  r"""Represents the pairing of REST resource path and the actions (verbs)
  allowed on the resource path.

  Fields:
    methods: methods refers to the REST verbs as in
      https://www.w3.org/Protocols/rfc2616/rfc2616-sec9.html. When none
      specified, all verb types are allowed.
    resource: Required. REST resource path associated with the API proxy or
      remote service.
  """

  methods = _messages.StringField(1, repeated=True)
  resource = _messages.StringField(2)


class GoogleCloudApigeeV1OperationConfig(_messages.Message):
  r"""Binds the resources in an API proxy or remote service with the allowed
  REST methods and associated quota enforcement.

  Fields:
    apiSource: Required. Name of the API proxy or remote service with which
      the resources, methods, and quota are associated.
    attributes: Custom attributes associated with the operation.
    operations: List of resource/method pairs for the API proxy or remote
      service to which quota will applied. **Note**: Currently, you can
      specify only a single resource/method pair. The call will fail if more
      than one resource/method pair is provided.
    quota: Quota parameters to be enforced for the resources, methods, and API
      source combination. If none are specified, quota enforcement will not be
      done.
  """

  apiSource = _messages.StringField(1)
  attributes = _messages.MessageField('GoogleCloudApigeeV1Attribute', 2, repeated=True)
  operations = _messages.MessageField('GoogleCloudApigeeV1Operation', 3, repeated=True)
  quota = _messages.MessageField('GoogleCloudApigeeV1Quota', 4)


class GoogleCloudApigeeV1OperationGroup(_messages.Message):
  r"""List of operation configuration details associated with Apigee API
  proxies or remote services. Remote services are non-Apigee proxies, such as
  Istio-Envoy.

  Fields:
    operationConfigType: Flag that specifes whether the configuration is for
      Apigee API proxy or a remote service. Valid values include `proxy` or
      `remoteservice`. Defaults to `proxy`. Set to `proxy` when Apigee API
      proxies are associated with the API product. Set to `remoteservice` when
      non-Apigee proxies like Istio-Envoy are associated with the API product.
    operationConfigs: Required. List of operation configurations for either
      Apigee API proxies or other remote services that are associated with
      this API product.
  """

  operationConfigType = _messages.StringField(1)
  operationConfigs = _messages.MessageField('GoogleCloudApigeeV1OperationConfig', 2, repeated=True)


class GoogleCloudApigeeV1OperationMetadata(_messages.Message):
  r"""Metadata describing an Operation.

  Enums:
    OperationTypeValueValuesEnum:
    StateValueValuesEnum:

  Fields:
    operationType: A OperationTypeValueValuesEnum attribute.
    progress: Progress of the operation.
    state: A StateValueValuesEnum attribute.
    targetResourceName: Name of the resource for which the operation is
      operating on.
    warnings: Warnings encountered while executing the operation.
  """

  class OperationTypeValueValuesEnum(_messages.Enum):
    r"""OperationTypeValueValuesEnum enum type.

    Values:
      OPERATION_TYPE_UNSPECIFIED: <no description>
      INSERT: <no description>
      DELETE: <no description>
      UPDATE: <no description>
    """
    OPERATION_TYPE_UNSPECIFIED = 0
    INSERT = 1
    DELETE = 2
    UPDATE = 3

  class StateValueValuesEnum(_messages.Enum):
    r"""StateValueValuesEnum enum type.

    Values:
      STATE_UNSPECIFIED: <no description>
      NOT_STARTED: <no description>
      IN_PROGRESS: <no description>
      FINISHED: <no description>
    """
    STATE_UNSPECIFIED = 0
    NOT_STARTED = 1
    IN_PROGRESS = 2
    FINISHED = 3

  operationType = _messages.EnumField('OperationTypeValueValuesEnum', 1)
  progress = _messages.MessageField('GoogleCloudApigeeV1OperationMetadataProgress', 2)
  state = _messages.EnumField('StateValueValuesEnum', 3)
  targetResourceName = _messages.StringField(4)
  warnings = _messages.StringField(5, repeated=True)


class GoogleCloudApigeeV1OperationMetadataProgress(_messages.Message):
  r"""Information about operation progress.

  Enums:
    StateValueValuesEnum: State of the operation.

  Messages:
    DetailsValue: The additional details of the progress.

  Fields:
    description: Description of the operation's progress.
    details: The additional details of the progress.
    percentDone: The percentage of the operation progress.
    state: State of the operation.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of the operation.

    Values:
      STATE_UNSPECIFIED: <no description>
      NOT_STARTED: <no description>
      IN_PROGRESS: <no description>
      FINISHED: <no description>
    """
    STATE_UNSPECIFIED = 0
    NOT_STARTED = 1
    IN_PROGRESS = 2
    FINISHED = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValue(_messages.Message):
    r"""The additional details of the progress.

    Messages:
      AdditionalProperty: An additional property for a DetailsValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  description = _messages.StringField(1)
  details = _messages.MessageField('DetailsValue', 2)
  percentDone = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class GoogleCloudApigeeV1OptimizedStats(_messages.Message):
  r"""A GoogleCloudApigeeV1OptimizedStats object.

  Fields:
    Response: Wraps the `stats` response for JavaScript Optimized Scenario
      with a response key. For example: ```{ "Response": { "TimeUnit": [],
      "metaData": { "errors": [], "notices": [ "Source:Postgres", "Table used:
      edge.api.aaxgroup001.agg_api", "PG
      Host:ruappg08-ro.production.apigeeks.net", "query served
      by:80c4ebca-6a10-4a2e-8faf-c60c1ee306ca" ] }, "resultTruncated": false,
      "stats": { "data": [ { "identifier": { "names": [ "apiproxy" ],
      "values": [ "sirjee" ] }, "metric": [ { "env": "prod", "name":
      "sum(message_count)", "values": [ 36.0 ] }, { "env": "prod", "name":
      "sum(is_error)", "values": [ 36.0 ] } ] } ] } } }```
  """

  Response = _messages.MessageField('GoogleCloudApigeeV1OptimizedStatsResponse', 1)


class GoogleCloudApigeeV1OptimizedStatsNode(_messages.Message):
  r"""Encapsulates a data node as represented below: ``` { "identifier": {
  "names": [ "apiproxy" ], "values": [ "sirjee" ] }, "metric": [ { "env":
  "prod", "name": "sum(message_count)", "values": [ 36.0 ] } ] }``` or ``` {
  "env": "prod", "name": "sum(message_count)", "values": [ 36.0 ] }```
  Depending on whether a dimension is present in the query or not the data
  node type can be a simple metric value or dimension identifier with list of
  metrics.

  Fields:
    data: A extra_types.JsonValue attribute.
  """

  data = _messages.MessageField('extra_types.JsonValue', 1, repeated=True)


class GoogleCloudApigeeV1OptimizedStatsResponse(_messages.Message):
  r"""Encapsulates a response format for JavaScript Optimized Scenario.

  Fields:
    TimeUnit: List of time unit values. Time unit refers to an epoch timestamp
      value.
    metaData: Metadata information about the query executed.
    resultTruncated: Boolean flag that indicates whether the results were
      truncated based on the limit parameter.
    stats: `stats` results.
  """

  TimeUnit = _messages.IntegerField(1, repeated=True)
  metaData = _messages.MessageField('GoogleCloudApigeeV1Metadata', 2)
  resultTruncated = _messages.BooleanField(3)
  stats = _messages.MessageField('GoogleCloudApigeeV1OptimizedStatsNode', 4)


class GoogleCloudApigeeV1Organization(_messages.Message):
  r"""A GoogleCloudApigeeV1Organization object.

  Enums:
    BillingTypeValueValuesEnum: Billing type of the Apigee organization. See
      [Apigee pricing](https://cloud.google.com/apigee/pricing).
    ReleaseChannelValueValuesEnum: Release channel influences the timing and
      frequency of new updates to the Apigee runtimes instances of the
      organization. It can be either STABLE, REGULAR, or RAPID. It can be
      selected during creation of the Organization and it can also be updated
      later on. Each channel has its own combination of release frequency and
      stability expectations. The RAPID channel will get updates early and
      more often. The REGULAR channel will get updates after being validated
      in the RAPID channel for some time. The STABLE channel will get updates
      after being validated in the REGULAR channel for some time.
    RuntimeTypeValueValuesEnum: Required. Runtime type of the Apigee
      organization based on the Apigee subscription purchased.
    StateValueValuesEnum: Output only. State of the organization. Values other
      than ACTIVE means the resource is not ready to use.
    SubscriptionTypeValueValuesEnum: Output only. DEPRECATED: This will
      eventually be replaced by BillingType. Subscription type of the Apigee
      organization. Valid values include trial (free, limited, and for
      evaluation purposes only) or paid (full subscription has been
      purchased). See [Apigee
      pricing](https://cloud.google.com/apigee/pricing/).
    TypeValueValuesEnum: Not used by Apigee.

  Fields:
    addonsConfig: Addon configurations of the Apigee organization.
    analyticsRegion: Required. DEPRECATED: This field will eventually be
      deprecated and replaced with a differently-named field. Primary Google
      Cloud region for analytics data storage. For valid values, see [Create
      an Apigee organization](https://cloud.google.com/apigee/docs/api-
      platform/get-started/create-org).
    apiConsumerDataEncryptionKeyName: Cloud KMS key name used for encrypting
      API consumer data. Required for US/EU regions when
      [BillingType](#BillingType) is `SUBSCRIPTION`. When
      [BillingType](#BillingType) is `EVALUATION` or the region is not US/EU,
      a Google-Managed encryption key will be used. Format:
      `projects/*/locations/*/keyRings/*/cryptoKeys/*`
    apiConsumerDataLocation: This field is needed only for customers with
      control plane in US or EU. Apigee stores some control plane data only in
      single region. This field determines which single region Apigee should
      use. For example: "us-west1" when control plane is in US or "europe-
      west2" when control plane is in EU.
    apigeeProjectId: Output only. Apigee Project ID associated with the
      organization. Use this project to allowlist Apigee in the Service
      Attachment when using private service connect with Apigee.
    attributes: Not used by Apigee.
    authorizedNetwork: Compute Engine network used for Service Networking to
      be peered with Apigee runtime instances. See [Getting started with the
      Service Networking API](https://cloud.google.com/service-
      infrastructure/docs/service-networking/getting-started). Valid only when
      [RuntimeType](#RuntimeType) is set to `CLOUD`. The value must be set
      before the creation of a runtime instance and can be updated only when
      there are no runtime instances. For example: `default`. Apigee also
      supports shared VPC (that is, the host network project is not the same
      as the one that is peering with Apigee). See [Shared VPC
      overview](https://cloud.google.com/vpc/docs/shared-vpc). To use a shared
      VPC network, use the following format: `projects/{host-project-
      id}/{region}/networks/{network-name}`. For example: `projects/my-
      sharedvpc-host/global/networks/mynetwork` **Note:** Not supported for
      Apigee hybrid.
    billingType: Billing type of the Apigee organization. See [Apigee
      pricing](https://cloud.google.com/apigee/pricing).
    caCertificate: Output only. Base64-encoded public certificate for the root
      CA of the Apigee organization. Valid only when
      [RuntimeType](#RuntimeType) is `CLOUD`.
    controlPlaneEncryptionKeyName: Cloud KMS key name used for encrypting
      control plane data that is stored in a multi region. Required when
      [BillingType](#BillingType) is `SUBSCRIPTION`. When
      [BillingType](#BillingType) is `EVALUATION`, a Google-Managed encryption
      key will be used. Format:
      `projects/*/locations/*/keyRings/*/cryptoKeys/*`
    createdAt: Output only. Time that the Apigee organization was created in
      milliseconds since epoch.
    customerName: Not used by Apigee.
    description: Description of the Apigee organization.
    disableVpcPeering: Optional. Flag that specifies whether the VPC Peering
      through Private Google Access should be disabled between the consumer
      network and Apigee. Valid only when RuntimeType is set to CLOUD.
      Required if an authorizedNetwork on the consumer project is not
      provided, in which case the flag should be set to true. The value must
      be set before the creation of any Apigee runtime instance and can be
      updated only when there are no runtime instances. **Note:** Apigee will
      be deprecating the vpc peering model that requires you to provide
      'authorizedNetwork', by making the non-peering model as the default way
      of provisioning Apigee organization in future. So, this will be a
      temporary flag to enable the transition. Not supported for Apigee
      hybrid.
    displayName: Display name for the Apigee organization. Unused, but
      reserved for future use.
    environments: Output only. List of environments in the Apigee
      organization.
    expiresAt: Output only. Time that the Apigee organization is scheduled for
      deletion.
    lastModifiedAt: Output only. Time that the Apigee organization was last
      modified in milliseconds since epoch.
    name: Output only. Name of the Apigee organization.
    portalDisabled: Configuration for the Portals settings.
    projectId: Output only. Project ID associated with the Apigee
      organization.
    properties: Properties defined in the Apigee organization profile.
    releaseChannel: Release channel influences the timing and frequency of new
      updates to the Apigee runtimes instances of the organization. It can be
      either STABLE, REGULAR, or RAPID. It can be selected during creation of
      the Organization and it can also be updated later on. Each channel has
      its own combination of release frequency and stability expectations. The
      RAPID channel will get updates early and more often. The REGULAR channel
      will get updates after being validated in the RAPID channel for some
      time. The STABLE channel will get updates after being validated in the
      REGULAR channel for some time.
    runtimeDatabaseEncryptionKeyName: Cloud KMS key name used for encrypting
      the data that is stored and replicated across runtime instances. Update
      is not allowed after the organization is created. Required when
      [RuntimeType](#RuntimeType) is `CLOUD`. If not specified when
      [RuntimeType](#RuntimeType) is `TRIAL`, a Google-Managed encryption key
      will be used. For example:
      "projects/foo/locations/us/keyRings/bar/cryptoKeys/baz". **Note:** Not
      supported for Apigee hybrid.
    runtimeType: Required. Runtime type of the Apigee organization based on
      the Apigee subscription purchased.
    state: Output only. State of the organization. Values other than ACTIVE
      means the resource is not ready to use.
    subscriptionType: Output only. DEPRECATED: This will eventually be
      replaced by BillingType. Subscription type of the Apigee organization.
      Valid values include trial (free, limited, and for evaluation purposes
      only) or paid (full subscription has been purchased). See [Apigee
      pricing](https://cloud.google.com/apigee/pricing/).
    type: Not used by Apigee.
  """

  class BillingTypeValueValuesEnum(_messages.Enum):
    r"""Billing type of the Apigee organization. See [Apigee
    pricing](https://cloud.google.com/apigee/pricing).

    Values:
      BILLING_TYPE_UNSPECIFIED: Billing type not specified.
      SUBSCRIPTION: A pre-paid subscription to Apigee.
      EVALUATION: Free and limited access to Apigee for evaluation purposes
        only. only.
      PAYG: Access to Apigee using a Pay-As-You-Go plan.
    """
    BILLING_TYPE_UNSPECIFIED = 0
    SUBSCRIPTION = 1
    EVALUATION = 2
    PAYG = 3

  class ReleaseChannelValueValuesEnum(_messages.Enum):
    r"""Release channel influences the timing and frequency of new updates to
    the Apigee runtimes instances of the organization. It can be either
    STABLE, REGULAR, or RAPID. It can be selected during creation of the
    Organization and it can also be updated later on. Each channel has its own
    combination of release frequency and stability expectations. The RAPID
    channel will get updates early and more often. The REGULAR channel will
    get updates after being validated in the RAPID channel for some time. The
    STABLE channel will get updates after being validated in the REGULAR
    channel for some time.

    Values:
      RELEASE_CHANNEL_UNSPECIFIED: Release channel not specified.
      STABLE: Stable release channel.
      REGULAR: Regular release channel.
      RAPID: Rapid release channel.
    """
    RELEASE_CHANNEL_UNSPECIFIED = 0
    STABLE = 1
    REGULAR = 2
    RAPID = 3

  class RuntimeTypeValueValuesEnum(_messages.Enum):
    r"""Required. Runtime type of the Apigee organization based on the Apigee
    subscription purchased.

    Values:
      RUNTIME_TYPE_UNSPECIFIED: Runtime type not specified.
      CLOUD: Google-managed Apigee runtime.
      HYBRID: User-managed Apigee hybrid runtime.
    """
    RUNTIME_TYPE_UNSPECIFIED = 0
    CLOUD = 1
    HYBRID = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. State of the organization. Values other than ACTIVE means
    the resource is not ready to use.

    Values:
      STATE_UNSPECIFIED: Resource is in an unspecified state.
      CREATING: Resource is being created.
      ACTIVE: Resource is provisioned and ready to use.
      DELETING: The resource is being deleted.
      UPDATING: The resource is being updated.
    """
    STATE_UNSPECIFIED = 0
    CREATING = 1
    ACTIVE = 2
    DELETING = 3
    UPDATING = 4

  class SubscriptionTypeValueValuesEnum(_messages.Enum):
    r"""Output only. DEPRECATED: This will eventually be replaced by
    BillingType. Subscription type of the Apigee organization. Valid values
    include trial (free, limited, and for evaluation purposes only) or paid
    (full subscription has been purchased). See [Apigee
    pricing](https://cloud.google.com/apigee/pricing/).

    Values:
      SUBSCRIPTION_TYPE_UNSPECIFIED: Subscription type not specified.
      PAID: Full subscription to Apigee has been purchased.
      TRIAL: Subscription to Apigee is free, limited, and used for evaluation
        purposes only.
    """
    SUBSCRIPTION_TYPE_UNSPECIFIED = 0
    PAID = 1
    TRIAL = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""Not used by Apigee.

    Values:
      TYPE_UNSPECIFIED: Subscription type not specified.
      TYPE_TRIAL: Subscription to Apigee is free, limited, and used for
        evaluation purposes only.
      TYPE_PAID: Full subscription to Apigee has been purchased. See [Apigee
        pricing](https://cloud.google.com/apigee/pricing/).
      TYPE_INTERNAL: For internal users only.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_TRIAL = 1
    TYPE_PAID = 2
    TYPE_INTERNAL = 3

  addonsConfig = _messages.MessageField('GoogleCloudApigeeV1AddonsConfig', 1)
  analyticsRegion = _messages.StringField(2)
  apiConsumerDataEncryptionKeyName = _messages.StringField(3)
  apiConsumerDataLocation = _messages.StringField(4)
  apigeeProjectId = _messages.StringField(5)
  attributes = _messages.StringField(6, repeated=True)
  authorizedNetwork = _messages.StringField(7)
  billingType = _messages.EnumField('BillingTypeValueValuesEnum', 8)
  caCertificate = _messages.BytesField(9)
  controlPlaneEncryptionKeyName = _messages.StringField(10)
  createdAt = _messages.IntegerField(11)
  customerName = _messages.StringField(12)
  description = _messages.StringField(13)
  disableVpcPeering = _messages.BooleanField(14)
  displayName = _messages.StringField(15)
  environments = _messages.StringField(16, repeated=True)
  expiresAt = _messages.IntegerField(17)
  lastModifiedAt = _messages.IntegerField(18)
  name = _messages.StringField(19)
  portalDisabled = _messages.BooleanField(20)
  projectId = _messages.StringField(21)
  properties = _messages.MessageField('GoogleCloudApigeeV1Properties', 22)
  releaseChannel = _messages.EnumField('ReleaseChannelValueValuesEnum', 23)
  runtimeDatabaseEncryptionKeyName = _messages.StringField(24)
  runtimeType = _messages.EnumField('RuntimeTypeValueValuesEnum', 25)
  state = _messages.EnumField('StateValueValuesEnum', 26)
  subscriptionType = _messages.EnumField('SubscriptionTypeValueValuesEnum', 27)
  type = _messages.EnumField('TypeValueValuesEnum', 28)


class GoogleCloudApigeeV1OrganizationProjectMapping(_messages.Message):
  r"""A GoogleCloudApigeeV1OrganizationProjectMapping object.

  Fields:
    location: Output only. The Google Cloud region where control plane data is
      located. For more information, see
      https://cloud.google.com/about/locations/.
    organization: Name of the Apigee organization.
    projectId: Google Cloud project associated with the Apigee organization
    projectIds: DEPRECATED: Use `project_id`. An Apigee Organization is mapped
      to a single project.
  """

  location = _messages.StringField(1)
  organization = _messages.StringField(2)
  projectId = _messages.StringField(3)
  projectIds = _messages.StringField(4, repeated=True)


class GoogleCloudApigeeV1PodStatus(_messages.Message):
  r"""A GoogleCloudApigeeV1PodStatus object.

  Fields:
    appVersion: Version of the application running in the pod.
    deploymentStatus: Status of the deployment. Valid values include: -
      `deployed`: Successful. - `error` : Failed. - `pending` : Pod has not
      yet reported on the deployment.
    deploymentStatusTime: Time the deployment status was reported in
      milliseconds since epoch.
    deploymentTime: Time the proxy was deployed in milliseconds since epoch.
    podName: Name of the pod which is reporting the status.
    podStatus: Overall status of the pod (not this specific deployment). Valid
      values include: - `active`: Up to date. - `stale` : Recently out of
      date. Pods that have not reported status in a long time are excluded
      from the output.
    podStatusTime: Time the pod status was reported in milliseconds since
      epoch.
    statusCode: Code associated with the deployment status.
    statusCodeDetails: Human-readable message associated with the status code.
  """

  appVersion = _messages.StringField(1)
  deploymentStatus = _messages.StringField(2)
  deploymentStatusTime = _messages.IntegerField(3)
  deploymentTime = _messages.IntegerField(4)
  podName = _messages.StringField(5)
  podStatus = _messages.StringField(6)
  podStatusTime = _messages.IntegerField(7)
  statusCode = _messages.StringField(8)
  statusCodeDetails = _messages.StringField(9)


class GoogleCloudApigeeV1Point(_messages.Message):
  r"""Point is a group of information collected by runtime plane at critical
  points of the message flow of the processed API request. This is a list of
  supported point IDs, categorized to three major buckets. For each category,
  debug points that we are currently supporting are listed below: - Flow
  status debug points: StateChange FlowInfo Condition Execution DebugMask
  Error - Flow control debug points: FlowCallout Paused Resumed FlowReturn
  BreakFlow Error - Runtime debug points: ScriptExecutor
  FlowCalloutStepDefinition CustomTarget StepDefinition Oauth2ServicePoint
  RaiseFault NodeJS The detail information of the given debug point is stored
  in a list of results.

  Fields:
    id: Name of a step in the transaction.
    results: List of results extracted from a given debug point.
  """

  id = _messages.StringField(1)
  results = _messages.MessageField('GoogleCloudApigeeV1Result', 2, repeated=True)


class GoogleCloudApigeeV1ProfileConfig(_messages.Message):
  r"""ProfileConfig defines a set of categories and policies which will be
  used to compute security score.

  Fields:
    categories: List of categories of profile config.
  """

  categories = _messages.MessageField('GoogleCloudApigeeV1ProfileConfigCategory', 1, repeated=True)


class GoogleCloudApigeeV1ProfileConfigAbuse(_messages.Message):
  r"""Checks for abuse, which includes any requests sent to the API for
  purposes other than what it is intended for, such as high volumes of
  requests, data scraping, and abuse related to authorization.
  """



class GoogleCloudApigeeV1ProfileConfigAuthorization(_messages.Message):
  r"""By default, following policies will be included: - JWS - JWT - OAuth -
  BasicAuth - APIKey
  """



class GoogleCloudApigeeV1ProfileConfigCORS(_messages.Message):
  r"""Checks to see if you have CORS policy in place."""


class GoogleCloudApigeeV1ProfileConfigCategory(_messages.Message):
  r"""Advanced API Security provides security profile that scores the
  following categories.

  Fields:
    abuse: Checks for abuse, which includes any requests sent to the API for
      purposes other than what it is intended for, such as high volumes of
      requests, data scraping, and abuse related to authorization.
    authorization: Checks to see if you have an authorization policy in place.
    cors: Checks to see if you have CORS policy in place.
    mediation: Checks to see if you have a mediation policy in place.
    mtls: Checks to see if you have configured mTLS for the target server.
    threat: Checks to see if you have a threat protection policy in place.
  """

  abuse = _messages.MessageField('GoogleCloudApigeeV1ProfileConfigAbuse', 1)
  authorization = _messages.MessageField('GoogleCloudApigeeV1ProfileConfigAuthorization', 2)
  cors = _messages.MessageField('GoogleCloudApigeeV1ProfileConfigCORS', 3)
  mediation = _messages.MessageField('GoogleCloudApigeeV1ProfileConfigMediation', 4)
  mtls = _messages.MessageField('GoogleCloudApigeeV1ProfileConfigMTLS', 5)
  threat = _messages.MessageField('GoogleCloudApigeeV1ProfileConfigThreat', 6)


class GoogleCloudApigeeV1ProfileConfigMTLS(_messages.Message):
  r"""Checks to see if you have configured mTLS for the target server."""


class GoogleCloudApigeeV1ProfileConfigMediation(_messages.Message):
  r"""By default, following policies will be included: - OASValidation -
  SOAPMessageValidation
  """



class GoogleCloudApigeeV1ProfileConfigThreat(_messages.Message):
  r"""By default, following policies will be included: - XMLThreatProtection -
  JSONThreatProtection
  """



class GoogleCloudApigeeV1Properties(_messages.Message):
  r"""Message for compatibility with legacy Edge specification for Java
  Properties object in JSON.

  Fields:
    property: List of all properties in the object
  """

  property = _messages.MessageField('GoogleCloudApigeeV1Property', 1, repeated=True)


class GoogleCloudApigeeV1Property(_messages.Message):
  r"""A single property entry in the Properties message.

  Fields:
    name: The property key
    value: The property value
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleCloudApigeeV1ProvisionOrganizationRequest(_messages.Message):
  r"""Request for ProvisionOrganization.

  Fields:
    analyticsRegion: Primary Cloud Platform region for analytics data storage.
      For valid values, see [Create an
      organization](https://cloud.google.com/apigee/docs/hybrid/latest/precog-
      provision). Defaults to `us-west1`.
    authorizedNetwork: Compute Engine network used for Service Networking to
      be peered with Apigee runtime instances. See [Getting started with the
      Service Networking API](https://cloud.google.com/service-
      infrastructure/docs/service-networking/getting-started). Apigee also
      supports shared VPC (that is, the host network project is not the same
      as the one that is peering with Apigee). See [Shared VPC
      overview](https://cloud.google.com/vpc/docs/shared-vpc). To use a shared
      VPC network, use the following format: `projects/{host-project-
      id}/{region}/networks/{network-name}`. For example: `projects/my-
      sharedvpc-host/global/networks/mynetwork`
    disableVpcPeering: Optional. Flag that specifies whether the VPC Peering
      through Private Google Access should be disabled between the consumer
      network and Apigee. Required if an authorizedNetwork on the consumer
      project is not provided, in which case the flag should be set to true.
      The value must be set before the creation of any Apigee runtime instance
      and can be updated only when there are no runtime instances. **Note:**
      Apigee will be deprecating the vpc peering model that requires you to
      provide 'authorizedNetwork', by making the non-peering model as the
      default way of provisioning Apigee organization in future. So, this will
      be a temporary flag to enable the transition. Not supported for Apigee
      hybrid.
    runtimeLocation: Cloud Platform location for the runtime instance.
      Defaults to zone `us-west1-a`. If a region is provided, `EVAL`
      organizations will use the region for automatically selecting a zone for
      the runtime instance.
  """

  analyticsRegion = _messages.StringField(1)
  authorizedNetwork = _messages.StringField(2)
  disableVpcPeering = _messages.BooleanField(3)
  runtimeLocation = _messages.StringField(4)


class GoogleCloudApigeeV1Query(_messages.Message):
  r"""A GoogleCloudApigeeV1Query object.

  Fields:
    csvDelimiter: Delimiter used in the CSV file, if `outputFormat` is set to
      `csv`. Defaults to the `,` (comma) character. Supported delimiter
      characters include comma (`,`), pipe (`|`), and tab (`\t`).
    dimensions: A list of dimensions. https://docs.apigee.com/api-
      platform/analytics/analytics-reference#dimensions
    envgroupHostname: Hostname needs to be specified if query intends to run
      at host level. This field is only allowed when query is submitted by
      CreateHostAsyncQuery where analytics data will be grouped by
      organization and hostname.
    filter: Boolean expression that can be used to filter data. Filter
      expressions can be combined using AND/OR terms and should be fully
      parenthesized to avoid ambiguity. See Analytics metrics, dimensions, and
      filters reference https://docs.apigee.com/api-
      platform/analytics/analytics-reference for more information on the
      fields available to filter on. For more information on the tokens that
      you use to build filter expressions, see Filter expression syntax.
      https://docs.apigee.com/api-platform/analytics/asynch-reports-
      api#filter-expression-syntax
    groupByTimeUnit: Time unit used to group the result set. Valid values
      include: second, minute, hour, day, week, or month. If a query includes
      groupByTimeUnit, then the result is an aggregation based on the
      specified time unit and the resultant timestamp does not include
      milliseconds precision. If a query omits groupByTimeUnit, then the
      resultant timestamp includes milliseconds precision.
    limit: Maximum number of rows that can be returned in the result.
    metrics: A list of Metrics.
    name: Asynchronous Query Name.
    outputFormat: Valid values include: `csv` or `json`. Defaults to `json`.
      Note: Configure the delimiter for CSV output using the csvDelimiter
      property.
    reportDefinitionId: Asynchronous Report ID.
    timeRange: Required. Time range for the query. Can use the following
      predefined strings to specify the time range: `last60minutes`
      `last24hours` `last7days` Or, specify the timeRange as a structure
      describing start and end timestamps in the ISO format: yyyy-mm-
      ddThh:mm:ssZ. Example: "timeRange": { "start": "2018-07-29T00:13:00Z",
      "end": "2018-08-01T00:18:00Z" }
  """

  csvDelimiter = _messages.StringField(1)
  dimensions = _messages.StringField(2, repeated=True)
  envgroupHostname = _messages.StringField(3)
  filter = _messages.StringField(4)
  groupByTimeUnit = _messages.StringField(5)
  limit = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  metrics = _messages.MessageField('GoogleCloudApigeeV1QueryMetric', 7, repeated=True)
  name = _messages.StringField(8)
  outputFormat = _messages.StringField(9)
  reportDefinitionId = _messages.StringField(10)
  timeRange = _messages.MessageField('extra_types.JsonValue', 11)


class GoogleCloudApigeeV1QueryMetadata(_messages.Message):
  r"""A GoogleCloudApigeeV1QueryMetadata object.

  Fields:
    dimensions: Dimensions of the AsyncQuery.
    endTimestamp: End timestamp of the query range.
    metrics: Metrics of the AsyncQuery. Example:
      ["name:message_count,func:sum,alias:sum_message_count"]
    outputFormat: Output format.
    startTimestamp: Start timestamp of the query range.
    timeUnit: Query GroupBy time unit.
  """

  dimensions = _messages.StringField(1, repeated=True)
  endTimestamp = _messages.StringField(2)
  metrics = _messages.StringField(3, repeated=True)
  outputFormat = _messages.StringField(4)
  startTimestamp = _messages.StringField(5)
  timeUnit = _messages.StringField(6)


class GoogleCloudApigeeV1QueryMetric(_messages.Message):
  r"""More info about Metric: https://docs.apigee.com/api-
  platform/analytics/analytics-reference#metrics

  Fields:
    alias: Alias for the metric. Alias will be used to replace metric name in
      query results.
    function: Aggregation function: avg, min, max, or sum.
    name: Required. Metric name.
    operator: One of `+`, `-`, `/`, `%`, `*`.
    value: Operand value should be provided when operator is set.
  """

  alias = _messages.StringField(1)
  function = _messages.StringField(2)
  name = _messages.StringField(3)
  operator = _messages.StringField(4)
  value = _messages.StringField(5)


class GoogleCloudApigeeV1QueryTabularStatsRequest(_messages.Message):
  r"""Request payload representing the query to be run for fetching security
  statistics as rows.

  Fields:
    dimensions: Required. List of dimension names to group the aggregations
      by.
    filter: Filter further on specific dimension values. Follows the same
      grammar as custom report's filter expressions. Example, apiproxy eq
      'foobar'. https://cloud.google.com/apigee/docs/api-
      platform/analytics/analytics-reference#filters
    metrics: Required. List of metrics and their aggregations.
    pageSize: Page size represents the number of rows.
    pageToken: Identifies a sequence of rows.
    timeRange: Time range for the stats.
  """

  dimensions = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  metrics = _messages.MessageField('GoogleCloudApigeeV1MetricAggregation', 3, repeated=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  timeRange = _messages.MessageField('GoogleTypeInterval', 6)


class GoogleCloudApigeeV1QueryTabularStatsResponse(_messages.Message):
  r"""Encapsulates two kinds of stats that are results of the dimensions and
  aggregations requested. - Tabular rows. - Time series data. Example of
  tabular rows, Represents security stats results as a row of flat values.

  Messages:
    ValuesValueListEntry: Single entry in a ValuesValue.

  Fields:
    columns: Column names corresponding to the same order as the inner values
      in the stats field.
    nextPageToken: Next page token.
    values: Resultant rows from the executed query.
  """

  class ValuesValueListEntry(_messages.Message):
    r"""Single entry in a ValuesValue.

    Fields:
      entry: A extra_types.JsonValue attribute.
    """

    entry = _messages.MessageField('extra_types.JsonValue', 1, repeated=True)

  columns = _messages.StringField(1, repeated=True)
  nextPageToken = _messages.StringField(2)
  values = _messages.MessageField('ValuesValueListEntry', 3, repeated=True)


class GoogleCloudApigeeV1QueryTimeSeriesStatsRequest(_messages.Message):
  r"""QueryTimeSeriesStatsRequest represents a query that returns a collection
  of time series sequences grouped by their values.

  Enums:
    TimestampOrderValueValuesEnum: Order the sequences in increasing or
      decreasing order of timestamps. Default is descending order of
      timestamps (latest first).
    WindowSizeValueValuesEnum: Time buckets to group the stats by.

  Fields:
    dimensions: List of dimension names to group the aggregations by. If no
      dimensions are passed, a single trend line representing the requested
      metric aggregations grouped by environment is returned.
    filter: Filter further on specific dimension values. Follows the same
      grammar as custom report's filter expressions. Example, apiproxy eq
      'foobar'. https://cloud.google.com/apigee/docs/api-
      platform/analytics/analytics-reference#filters
    metrics: Required. List of metrics and their aggregations.
    pageSize: Page size represents the number of time series sequences, one
      per unique set of dimensions and their values.
    pageToken: Page token stands for a specific collection of time series
      sequences.
    timeRange: Required. Time range for the stats.
    timestampOrder: Order the sequences in increasing or decreasing order of
      timestamps. Default is descending order of timestamps (latest first).
    windowSize: Time buckets to group the stats by.
  """

  class TimestampOrderValueValuesEnum(_messages.Enum):
    r"""Order the sequences in increasing or decreasing order of timestamps.
    Default is descending order of timestamps (latest first).

    Values:
      ORDER_UNSPECIFIED: Unspecified order. Default is Descending.
      ASCENDING: Ascending sort order.
      DESCENDING: Descending sort order.
    """
    ORDER_UNSPECIFIED = 0
    ASCENDING = 1
    DESCENDING = 2

  class WindowSizeValueValuesEnum(_messages.Enum):
    r"""Time buckets to group the stats by.

    Values:
      WINDOW_SIZE_UNSPECIFIED: Unspecified window size. Default is 1 hour.
      MINUTE: 1 Minute window
      HOUR: 1 Hour window
      DAY: 1 Day window
      MONTH: 1 Month window
    """
    WINDOW_SIZE_UNSPECIFIED = 0
    MINUTE = 1
    HOUR = 2
    DAY = 3
    MONTH = 4

  dimensions = _messages.StringField(1, repeated=True)
  filter = _messages.StringField(2)
  metrics = _messages.MessageField('GoogleCloudApigeeV1MetricAggregation', 3, repeated=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)
  timeRange = _messages.MessageField('GoogleTypeInterval', 6)
  timestampOrder = _messages.EnumField('TimestampOrderValueValuesEnum', 7)
  windowSize = _messages.EnumField('WindowSizeValueValuesEnum', 8)


class GoogleCloudApigeeV1QueryTimeSeriesStatsResponse(_messages.Message):
  r"""Represents security stats result as a collection of time series
  sequences.

  Fields:
    columns: Column names corresponding to the same order as the inner values
      in the stats field.
    nextPageToken: Next page token.
    values: Results of the query returned as a JSON array.
  """

  columns = _messages.StringField(1, repeated=True)
  nextPageToken = _messages.StringField(2)
  values = _messages.MessageField('GoogleCloudApigeeV1QueryTimeSeriesStatsResponseSequence', 3, repeated=True)


class GoogleCloudApigeeV1QueryTimeSeriesStatsResponseSequence(_messages.Message):
  r"""A sequence of time series.

  Messages:
    DimensionsValue: Map of dimensions and their values that uniquely
      identifies a time series sequence.
    PointsValueListEntry: Single entry in a PointsValue.

  Fields:
    dimensions: Map of dimensions and their values that uniquely identifies a
      time series sequence.
    points: List of points. First value of each inner list is a timestamp.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DimensionsValue(_messages.Message):
    r"""Map of dimensions and their values that uniquely identifies a time
    series sequence.

    Messages:
      AdditionalProperty: An additional property for a DimensionsValue object.

    Fields:
      additionalProperties: Additional properties of type DimensionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DimensionsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  class PointsValueListEntry(_messages.Message):
    r"""Single entry in a PointsValue.

    Fields:
      entry: A extra_types.JsonValue attribute.
    """

    entry = _messages.MessageField('extra_types.JsonValue', 1, repeated=True)

  dimensions = _messages.MessageField('DimensionsValue', 1)
  points = _messages.MessageField('PointsValueListEntry', 2, repeated=True)


class GoogleCloudApigeeV1Quota(_messages.Message):
  r"""Quota contains the essential parameters needed that can be applied on
  the resources, methods, API source combination associated with this API
  product. While Quota is optional, setting it prevents requests from
  exceeding the provisioned parameters.

  Fields:
    interval: Required. Time interval over which the number of request
      messages is calculated.
    limit: Required. Upper limit allowed for the time interval and time unit
      specified. Requests exceeding this limit will be rejected.
    timeUnit: Time unit defined for the `interval`. Valid values include
      `minute`, `hour`, `day`, or `month`. If `limit` and `interval` are
      valid, the default value is `hour`; otherwise, the default is null.
  """

  interval = _messages.StringField(1)
  limit = _messages.StringField(2)
  timeUnit = _messages.StringField(3)


class GoogleCloudApigeeV1RatePlan(_messages.Message):
  r"""Rate plan details.

  Enums:
    BillingPeriodValueValuesEnum: Frequency at which the customer will be
      billed.
    ConsumptionPricingTypeValueValuesEnum: Pricing model used for consumption-
      based charges.
    PaymentFundingModelValueValuesEnum: DEPRECATED: This field is no longer
      supported and will eventually be removed when Apigee Hybrid 1.5/1.6 is
      no longer supported. Instead, use the `billingType` field inside
      `DeveloperMonetizationConfig` resource. Flag that specifies the billing
      account type, prepaid or postpaid.
    RevenueShareTypeValueValuesEnum: Method used to calculate the revenue that
      is shared with developers.
    StateValueValuesEnum: Current state of the rate plan (draft or published).

  Fields:
    apiproduct: Name of the API product that the rate plan is associated with.
    billingPeriod: Frequency at which the customer will be billed.
    consumptionPricingRates: API call volume ranges and the fees charged when
      the total number of API calls is within a given range. The method used
      to calculate the final fee depends on the selected pricing model. For
      example, if the pricing model is `STAIRSTEP` and the ranges are defined
      as follows: ``` { "start": 1, "end": 100, "fee": 75 }, { "start": 101,
      "end": 200, "fee": 100 }, } ``` Then the following fees would be charged
      based on the total number of API calls (assuming the currency selected
      is `USD`): * 1 call costs $75 * 50 calls cost $75 * 150 calls cost $100
      The number of API calls cannot exceed 200.
    consumptionPricingType: Pricing model used for consumption-based charges.
    createdAt: Output only. Time that the rate plan was created in
      milliseconds since epoch.
    currencyCode: Currency to be used for billing. Consists of a three-letter
      code as defined by the [ISO
      4217](https://en.wikipedia.org/wiki/ISO_4217) standard.
    description: Description of the rate plan.
    displayName: Display name of the rate plan.
    endTime: Time when the rate plan will expire in milliseconds since epoch.
      Set to 0 or `null` to indicate that the rate plan should never expire.
    fixedFeeFrequency: Frequency at which the fixed fee is charged.
    fixedRecurringFee: Fixed amount that is charged at a defined interval and
      billed in advance of use of the API product. The fee will be prorated
      for the first billing period.
    lastModifiedAt: Output only. Time the rate plan was last modified in
      milliseconds since epoch.
    name: Output only. Name of the rate plan.
    paymentFundingModel: DEPRECATED: This field is no longer supported and
      will eventually be removed when Apigee Hybrid 1.5/1.6 is no longer
      supported. Instead, use the `billingType` field inside
      `DeveloperMonetizationConfig` resource. Flag that specifies the billing
      account type, prepaid or postpaid.
    revenueShareRates: Details of the revenue sharing model.
    revenueShareType: Method used to calculate the revenue that is shared with
      developers.
    setupFee: Initial, one-time fee paid when purchasing the API product.
    startTime: Time when the rate plan becomes active in milliseconds since
      epoch.
    state: Current state of the rate plan (draft or published).
  """

  class BillingPeriodValueValuesEnum(_messages.Enum):
    r"""Frequency at which the customer will be billed.

    Values:
      BILLING_PERIOD_UNSPECIFIED: Billing period not specified.
      WEEKLY: Weekly billing period. **Note**: Not supported by Apigee at this
        time.
      MONTHLY: Monthly billing period.
    """
    BILLING_PERIOD_UNSPECIFIED = 0
    WEEKLY = 1
    MONTHLY = 2

  class ConsumptionPricingTypeValueValuesEnum(_messages.Enum):
    r"""Pricing model used for consumption-based charges.

    Values:
      CONSUMPTION_PRICING_TYPE_UNSPECIFIED: Pricing model not specified. This
        is the default.
      FIXED_PER_UNIT: Fixed rate charged for each API call.
      BANDED: Variable rate charged for each API call based on price tiers.
        Example: * 1-100 calls cost $2 per call * 101-200 calls cost $1.50 per
        call * 201-300 calls cost $1 per call * Total price for 50 calls: 50 x
        $2 = $100 * Total price for 150 calls: 100 x $2 + 50 x $1.5 = $275 *
        Total price for 250 calls: 100 x $2 + 100 x $1.5 + 50 x $1 = $400.
        **Note**: Not supported by Apigee at this time.
      TIERED: **Note**: Not supported by Apigee at this time.
      STAIRSTEP: **Note**: Not supported by Apigee at this time.
      BUNDLES: Cumulative rate charged for bundle of API calls whether or not
        the entire bundle is used. Example: * 1-100 calls cost $150 flat fee.
        * 101-200 calls cost $100 flat free. * 201-300 calls cost $75 flat
        fee. * Total price for 1 call: $150 * Total price for 50 calls: $150 *
        Total price for 150 calls: $150 + $100 * Total price for 250 calls:
        $150 + $100 + $75
    """
    CONSUMPTION_PRICING_TYPE_UNSPECIFIED = 0
    FIXED_PER_UNIT = 1
    BANDED = 2
    TIERED = 3
    STAIRSTEP = 4
    BUNDLES = 5

  class PaymentFundingModelValueValuesEnum(_messages.Enum):
    r"""DEPRECATED: This field is no longer supported and will eventually be
    removed when Apigee Hybrid 1.5/1.6 is no longer supported. Instead, use
    the `billingType` field inside `DeveloperMonetizationConfig` resource.
    Flag that specifies the billing account type, prepaid or postpaid.

    Values:
      PAYMENT_FUNDING_MODEL_UNSPECIFIED: Billing account type not specified.
      PREPAID: Prepaid billing account type. Developer pays in advance for the
        use of your API products. Funds are deducted from their prepaid
        account balance. **Note**: Not supported by Apigee at this time.
      POSTPAID: Postpaid billing account type. Developer is billed through an
        invoice after using your API products.
    """
    PAYMENT_FUNDING_MODEL_UNSPECIFIED = 0
    PREPAID = 1
    POSTPAID = 2

  class RevenueShareTypeValueValuesEnum(_messages.Enum):
    r"""Method used to calculate the revenue that is shared with developers.

    Values:
      REVENUE_SHARE_TYPE_UNSPECIFIED: Revenue share type is not specified.
      FIXED: Fixed percentage of the total revenue will be shared. The
        percentage to be shared can be configured by the API provider.
      VOLUME_BANDED: Amount of revenue shared depends on the number of API
        calls. The API call volume ranges and the revenue share percentage for
        each volume can be configured by the API provider. **Note**: Not
        supported by Apigee at this time.
    """
    REVENUE_SHARE_TYPE_UNSPECIFIED = 0
    FIXED = 1
    VOLUME_BANDED = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Current state of the rate plan (draft or published).

    Values:
      STATE_UNSPECIFIED: State of the rate plan is not specified.
      DRAFT: Rate plan is in draft mode and only visible to API providers.
      PUBLISHED: Rate plan is published and will become visible to developers
        for the configured duration (between `startTime` and `endTime`).
    """
    STATE_UNSPECIFIED = 0
    DRAFT = 1
    PUBLISHED = 2

  apiproduct = _messages.StringField(1)
  billingPeriod = _messages.EnumField('BillingPeriodValueValuesEnum', 2)
  consumptionPricingRates = _messages.MessageField('GoogleCloudApigeeV1RateRange', 3, repeated=True)
  consumptionPricingType = _messages.EnumField('ConsumptionPricingTypeValueValuesEnum', 4)
  createdAt = _messages.IntegerField(5)
  currencyCode = _messages.StringField(6)
  description = _messages.StringField(7)
  displayName = _messages.StringField(8)
  endTime = _messages.IntegerField(9)
  fixedFeeFrequency = _messages.IntegerField(10, variant=_messages.Variant.INT32)
  fixedRecurringFee = _messages.MessageField('GoogleTypeMoney', 11)
  lastModifiedAt = _messages.IntegerField(12)
  name = _messages.StringField(13)
  paymentFundingModel = _messages.EnumField('PaymentFundingModelValueValuesEnum', 14)
  revenueShareRates = _messages.MessageField('GoogleCloudApigeeV1RevenueShareRange', 15, repeated=True)
  revenueShareType = _messages.EnumField('RevenueShareTypeValueValuesEnum', 16)
  setupFee = _messages.MessageField('GoogleTypeMoney', 17)
  startTime = _messages.IntegerField(18)
  state = _messages.EnumField('StateValueValuesEnum', 19)


class GoogleCloudApigeeV1RateRange(_messages.Message):
  r"""API call volume range and the fees charged when the total number of API
  calls is within the range.

  Fields:
    end: Ending value of the range. Set to 0 or `null` for the last range of
      values.
    fee: Fee to charge when total number of API calls falls within this range.
    start: Starting value of the range. Set to 0 or `null` for the initial
      range of values.
  """

  end = _messages.IntegerField(1)
  fee = _messages.MessageField('GoogleTypeMoney', 2)
  start = _messages.IntegerField(3)


class GoogleCloudApigeeV1Reference(_messages.Message):
  r"""A Reference configuration. References must refer to a keystore that also
  exists in the parent environment.

  Fields:
    description: Optional. A human-readable description of this reference.
    name: Required. The resource id of this reference. Values must match the
      regular expression [\w\s\-.]+.
    refers: Required. The id of the resource to which this reference refers.
      Must be the id of a resource that exists in the parent environment and
      is of the given resource_type.
    resourceType: The type of resource referred to by this reference. Valid
      values are 'KeyStore' or 'TrustStore'.
  """

  description = _messages.StringField(1)
  name = _messages.StringField(2)
  refers = _messages.StringField(3)
  resourceType = _messages.StringField(4)


class GoogleCloudApigeeV1ReferenceConfig(_messages.Message):
  r"""A GoogleCloudApigeeV1ReferenceConfig object.

  Fields:
    name: Name of the reference in the following format:
      `organizations/{org}/environments/{env}/references/{reference}`
    resourceName: Name of the referenced resource in the following format:
      `organizations/{org}/environments/{env}/keystores/{keystore}` Only
      references to keystore resources are supported.
  """

  name = _messages.StringField(1)
  resourceName = _messages.StringField(2)


class GoogleCloudApigeeV1ReportInstanceStatusRequest(_messages.Message):
  r"""Request for ReportInstanceStatus.

  Fields:
    instanceUid: A unique ID for the instance which is guaranteed to be unique
      in case the user installs multiple hybrid runtimes with the same
      instance ID.
    reportTime: The time the report was generated in the runtime. Used to
      prevent an old status from overwriting a newer one. An instance should
      space out it's status reports so that clock skew does not play a factor.
    resources: Status for config resources
    spec: Resource spec.
  """

  instanceUid = _messages.StringField(1)
  reportTime = _messages.StringField(2)
  resources = _messages.MessageField('GoogleCloudApigeeV1ResourceStatus', 3, repeated=True)
  spec = _messages.MessageField('GoogleCloudApigeeV1ResourceSpec', 4)


class GoogleCloudApigeeV1ReportInstanceStatusResponse(_messages.Message):
  r"""Placeholder for future enhancements to status reporting protocol"""


class GoogleCloudApigeeV1ReportProperty(_messages.Message):
  r"""A GoogleCloudApigeeV1ReportProperty object.

  Fields:
    property: name of the property
    value: property values
  """

  property = _messages.StringField(1)
  value = _messages.MessageField('GoogleCloudApigeeV1Attribute', 2, repeated=True)


class GoogleCloudApigeeV1ResourceConfig(_messages.Message):
  r"""A GoogleCloudApigeeV1ResourceConfig object.

  Fields:
    location: Location of the resource as a URI.
    name: Resource name in the following format: `organizations/{org}/environm
      ents/{env}/resourcefiles/{type}/{file}/revisions/{rev}` Only
      environment-scoped resource files are supported.
  """

  location = _messages.StringField(1)
  name = _messages.StringField(2)


class GoogleCloudApigeeV1ResourceFile(_messages.Message):
  r"""Metadata about a resource file.

  Fields:
    name: ID of the resource file.
    type: Resource file type. {{ resource_file_type }}
  """

  name = _messages.StringField(1)
  type = _messages.StringField(2)


class GoogleCloudApigeeV1ResourceFiles(_messages.Message):
  r"""List of resource files.

  Fields:
    resourceFile: List of resource files.
  """

  resourceFile = _messages.MessageField('GoogleCloudApigeeV1ResourceFile', 1, repeated=True)


class GoogleCloudApigeeV1ResourceSpec(_messages.Message):
  r"""A resource spec, to be referenced in a ResourceStatus.

  Fields:
    json: The json content of the resource revision.
    resource: The resource name. Currently only two resources are supported:
      EnvironmentGroup - organizations/{org}/envgroups/{envgroup}
      EnvironmentConfig -
      organizations/{org}/environments/{environment}/deployedConfig
    revisionId: The revision of the resource.
    uid: The uid of the resource. In the unexpected case that the instance has
      multiple uids for the same name, they should be reported under separate
      ResourceStatuses.
  """

  json = _messages.StringField(1)
  resource = _messages.StringField(2)
  revisionId = _messages.StringField(3)
  uid = _messages.StringField(4)


class GoogleCloudApigeeV1ResourceStatus(_messages.Message):
  r"""The status of a resource loaded in the runtime.

  Fields:
    resource: The resource name. Currently only two resources are supported:
      EnvironmentGroup - organizations/{org}/envgroups/{envgroup}
      EnvironmentConfig -
      organizations/{org}/environments/{environment}/deployedConfig
    revisions: Revisions of the resource currently deployed in the instance.
    totalReplicas: The total number of replicas that should have this
      resource.
    uid: The uid of the resource. In the unexpected case that the instance has
      multiple uids for the same name, they should be reported under separate
      ResourceStatuses.
  """

  resource = _messages.StringField(1)
  revisions = _messages.MessageField('GoogleCloudApigeeV1RevisionStatus', 2, repeated=True)
  totalReplicas = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  uid = _messages.StringField(4)


class GoogleCloudApigeeV1Result(_messages.Message):
  r"""Result is short for "action result", could be different types identified
  by "action_result" field. Supported types: 1. DebugInfo : generic debug info
  collected by runtime recorded as a list of properties. For example, the
  contents could be virtual host info, state change result, or execution
  metadata. Required fields : properties, timestamp 2. RequestMessage:
  information of a http request. Contains headers, request URI and http
  methods type.Required fields : headers, uri, verb 3. ResponseMessage:
  information of a http response. Contains headers, reason phrase and http
  status code. Required fields : headers, reasonPhrase, statusCode 4.
  ErrorMessage: information of a http error message. Contains detail error
  message, reason phrase and status code. Required fields : content, headers,
  reasonPhrase, statusCode 5. VariableAccess: a list of variable access
  actions, can be Get, Set and Remove. Required fields : accessList

  Fields:
    ActionResult: Type of the action result. Can be one of the five:
      DebugInfo, RequestMessage, ResponseMessage, ErrorMessage, VariableAccess
    accessList: A list of variable access actions agaist the api proxy.
      Supported values: Get, Set, Remove.
    content: Error message content. for example, "content" :
      "{\"fault\":{\"faultstring\":\"API timed
      out\",\"detail\":{\"errorcode\":\"flow.APITimedOut\"}}}"
    headers: A list of HTTP headers. for example, '"headers" : [ { "name" :
      "Content-Length", "value" : "83" }, { "name" : "Content-Type", "value" :
      "application/json" } ]'
    properties: Name value pairs used for DebugInfo ActionResult.
    reasonPhrase: HTTP response phrase
    statusCode: HTTP response code
    timestamp: Timestamp of when the result is recorded. Its format is dd-mm-
      yy hh:mm:ss:xxx. For example, `"timestamp" : "12-08-19 00:31:59:960"`
    uRI: The relative path of the api proxy. for example, `"uRI" :
      "/iloveapis"`
    verb: HTTP method verb
  """

  ActionResult = _messages.StringField(1)
  accessList = _messages.MessageField('GoogleCloudApigeeV1Access', 2, repeated=True)
  content = _messages.StringField(3)
  headers = _messages.MessageField('GoogleCloudApigeeV1Property', 4, repeated=True)
  properties = _messages.MessageField('GoogleCloudApigeeV1Properties', 5)
  reasonPhrase = _messages.StringField(6)
  statusCode = _messages.StringField(7)
  timestamp = _messages.StringField(8)
  uRI = _messages.StringField(9)
  verb = _messages.StringField(10)


class GoogleCloudApigeeV1RevenueShareRange(_messages.Message):
  r"""API call volume range and the percentage of revenue to share with the
  developer when the total number of API calls is within the range.

  Fields:
    end: Ending value of the range. Set to 0 or `null` for the last range of
      values.
    sharePercentage: Percentage of the revenue to be shared with the
      developer. For example, to share 21 percent of the total revenue with
      the developer, set this value to 21. Specify a decimal number with a
      maximum of two digits following the decimal point.
    start: Starting value of the range. Set to 0 or `null` for the initial
      range of values.
  """

  end = _messages.IntegerField(1)
  sharePercentage = _messages.FloatField(2)
  start = _messages.IntegerField(3)


class GoogleCloudApigeeV1RevisionStatus(_messages.Message):
  r"""The status of a specific resource revision.

  Fields:
    errors: Errors reported when attempting to load this revision.
    jsonSpec: The json content of the resource revision. Large specs should be
      sent individually via the spec field to avoid hitting request size
      limits.
    replicas: The number of replicas that have successfully loaded this
      revision.
    revisionId: The revision of the resource.
  """

  errors = _messages.MessageField('GoogleCloudApigeeV1UpdateError', 1, repeated=True)
  jsonSpec = _messages.StringField(2)
  replicas = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  revisionId = _messages.StringField(4)


class GoogleCloudApigeeV1RoutingRule(_messages.Message):
  r"""A GoogleCloudApigeeV1RoutingRule object.

  Fields:
    basepath: URI path prefix used to route to the specified environment. May
      contain one or more wildcards. For example, path segments consisting of
      a single `*` character will match any string.
    deploymentGroup: Name of a deployment group in an environment bound to the
      environment group in the following format:
      `organizations/{org}/environment/{env}/deploymentGroups/{group}` Only
      one of environment or deployment_group will be set.
    envGroupRevision: The env group config revision_id when this rule was
      added or last updated. This value is set when the rule is created and
      will only update if the the environment_id changes. It is used to
      determine if the runtime is up to date with respect to this rule. This
      field is omitted from the IngressConfig unless the
      GetDeployedIngressConfig API is called with view=FULL.
    environment: Name of an environment bound to the environment group in the
      following format: `organizations/{org}/environments/{env}`. Only one of
      environment or deployment_group will be set.
    otherTargets: Conflicting targets, which will be resource names specifying
      either deployment groups or environments.
    receiver: The resource name of the proxy revision that is receiving this
      basepath in the following format:
      `organizations/{org}/apis/{api}/revisions/{rev}`. This field is omitted
      from the IngressConfig unless the GetDeployedIngressConfig API is called
      with view=FULL.
    updateTime: The unix timestamp when this rule was updated. This is updated
      whenever env_group_revision is updated. This field is omitted from the
      IngressConfig unless the GetDeployedIngressConfig API is called with
      view=FULL.
  """

  basepath = _messages.StringField(1)
  deploymentGroup = _messages.StringField(2)
  envGroupRevision = _messages.IntegerField(3)
  environment = _messages.StringField(4)
  otherTargets = _messages.StringField(5, repeated=True)
  receiver = _messages.StringField(6)
  updateTime = _messages.StringField(7)


class GoogleCloudApigeeV1RuntimeAddonsConfig(_messages.Message):
  r"""RuntimeAddonsConfig defines the runtime configurations for add-ons in an
  environment.

  Fields:
    analyticsConfig: Runtime configuration for Analytics add-on.
    apiSecurityConfig: Runtime configuration for API Security add-on.
    name: Name of the addons config in the format:
      `organizations/{org}/environments/{env}/addonsConfig`
    revisionId: Revision number used by the runtime to detect config changes.
    uid: UID is to detect if config is recreated after deletion. The add-on
      config will only be deleted when the environment itself gets deleted,
      thus it will always be the same as the UID of EnvironmentConfig.
  """

  analyticsConfig = _messages.MessageField('GoogleCloudApigeeV1RuntimeAnalyticsConfig', 1)
  apiSecurityConfig = _messages.MessageField('GoogleCloudApigeeV1RuntimeApiSecurityConfig', 2)
  name = _messages.StringField(3)
  revisionId = _messages.StringField(4)
  uid = _messages.StringField(5)


class GoogleCloudApigeeV1RuntimeAnalyticsConfig(_messages.Message):
  r"""Runtime configuration for the Analytics add-on.

  Fields:
    enabled: If the Analytics is enabled or not.
  """

  enabled = _messages.BooleanField(1)


class GoogleCloudApigeeV1RuntimeApiSecurityConfig(_messages.Message):
  r"""Runtime configuration for the API Security add-on.

  Fields:
    enabled: If the API Security is enabled or not.
  """

  enabled = _messages.BooleanField(1)


class GoogleCloudApigeeV1RuntimeConfig(_messages.Message):
  r"""Runtime configuration for the organization. Response for
  GetRuntimeConfig.

  Fields:
    analyticsBucket: Cloud Storage bucket used for uploading Analytics
      records.
    name: Name of the resource in the following format:
      `organizations/{org}/runtimeConfig`.
    tenantProjectId: Output only. Tenant project ID associated with the Apigee
      organization. The tenant project is used to host Google-managed
      resources that are dedicated to this Apigee organization. Clients have
      limited access to resources within the tenant project used to support
      Apigee runtime instances. Access to the tenant project is managed using
      SetSyncAuthorization. It can be empty if the tenant project hasn't been
      created yet.
    traceBucket: Cloud Storage bucket used for uploading Trace records.
  """

  analyticsBucket = _messages.StringField(1)
  name = _messages.StringField(2)
  tenantProjectId = _messages.StringField(3)
  traceBucket = _messages.StringField(4)


class GoogleCloudApigeeV1RuntimeTraceConfig(_messages.Message):
  r"""NEXT ID: 8 RuntimeTraceConfig defines the configurations for distributed
  trace in an environment.

  Enums:
    ExporterValueValuesEnum: Exporter that is used to view the distributed
      trace captured using OpenCensus. An exporter sends traces to any backend
      that is capable of consuming them. Recorded spans can be exported by
      registered exporters.

  Fields:
    endpoint: Endpoint of the exporter.
    exporter: Exporter that is used to view the distributed trace captured
      using OpenCensus. An exporter sends traces to any backend that is
      capable of consuming them. Recorded spans can be exported by registered
      exporters.
    name: Name of the trace config in the following format:
      `organizations/{org}/environment/{env}/traceConfig`
    overrides: List of trace configuration overrides for spicific API proxies.
    revisionCreateTime: The timestamp that the revision was created or
      updated.
    revisionId: Revision number which can be used by the runtime to detect if
      the trace config has changed between two versions.
    samplingConfig: Trace configuration for all API proxies in an environment.
  """

  class ExporterValueValuesEnum(_messages.Enum):
    r"""Exporter that is used to view the distributed trace captured using
    OpenCensus. An exporter sends traces to any backend that is capable of
    consuming them. Recorded spans can be exported by registered exporters.

    Values:
      EXPORTER_UNSPECIFIED: Exporter unspecified
      JAEGER: Jaeger exporter
      CLOUD_TRACE: Cloudtrace exporter
    """
    EXPORTER_UNSPECIFIED = 0
    JAEGER = 1
    CLOUD_TRACE = 2

  endpoint = _messages.StringField(1)
  exporter = _messages.EnumField('ExporterValueValuesEnum', 2)
  name = _messages.StringField(3)
  overrides = _messages.MessageField('GoogleCloudApigeeV1RuntimeTraceConfigOverride', 4, repeated=True)
  revisionCreateTime = _messages.StringField(5)
  revisionId = _messages.StringField(6)
  samplingConfig = _messages.MessageField('GoogleCloudApigeeV1RuntimeTraceSamplingConfig', 7)


class GoogleCloudApigeeV1RuntimeTraceConfigOverride(_messages.Message):
  r"""NEXT ID: 7 Trace configuration override for a specific API proxy in an
  environment.

  Fields:
    apiProxy: Name of the API proxy that will have its trace configuration
      overridden following format: `organizations/{org}/apis/{api}`
    name: Name of the trace config override in the following format:
      `organizations/{org}/environment/{env}/traceConfig/overrides/{override}`
    revisionCreateTime: The timestamp that the revision was created or
      updated.
    revisionId: Revision number which can be used by the runtime to detect if
      the trace config override has changed between two versions.
    samplingConfig: Trace configuration override for a specific API proxy in
      an environment.
    uid: Unique ID for the configuration override. The ID will only change if
      the override is deleted and recreated. Corresponds to name's "override"
      field.
  """

  apiProxy = _messages.StringField(1)
  name = _messages.StringField(2)
  revisionCreateTime = _messages.StringField(3)
  revisionId = _messages.StringField(4)
  samplingConfig = _messages.MessageField('GoogleCloudApigeeV1RuntimeTraceSamplingConfig', 5)
  uid = _messages.StringField(6)


class GoogleCloudApigeeV1RuntimeTraceSamplingConfig(_messages.Message):
  r"""NEXT ID: 3 RuntimeTraceSamplingConfig represents the detail settings of
  distributed tracing. Only the fields that are defined in the distributed
  trace configuration can be overridden using the distribute trace
  configuration override APIs.

  Enums:
    SamplerValueValuesEnum: Sampler of distributed tracing. OFF is the default
      value.

  Fields:
    sampler: Sampler of distributed tracing. OFF is the default value.
    samplingRate: Field sampling rate. This value is only applicable when
      using the PROBABILITY sampler. The supported values are > 0 and <= 0.5.
  """

  class SamplerValueValuesEnum(_messages.Enum):
    r"""Sampler of distributed tracing. OFF is the default value.

    Values:
      SAMPLER_UNSPECIFIED: Sampler unspecified.
      OFF: OFF means distributed trace is disabled, or the sampling
        probability is 0.
      PROBABILITY: PROBABILITY means traces are captured on a probability that
        defined by sampling_rate. The sampling rate is limited to 0 to 0.5
        when this is set.
    """
    SAMPLER_UNSPECIFIED = 0
    OFF = 1
    PROBABILITY = 2

  sampler = _messages.EnumField('SamplerValueValuesEnum', 1)
  samplingRate = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class GoogleCloudApigeeV1Schema(_messages.Message):
  r"""Response for Schema call

  Fields:
    dimensions: List of schema fields grouped as dimensions.
    meta: Additional metadata associated with schema. This is a legacy field
      and usually consists of an empty array of strings.
    metrics: List of schema fields grouped as dimensions that can be used with
      an aggregate function such as `sum`, `avg`, `min`, and `max`.
  """

  dimensions = _messages.MessageField('GoogleCloudApigeeV1SchemaSchemaElement', 1, repeated=True)
  meta = _messages.StringField(2, repeated=True)
  metrics = _messages.MessageField('GoogleCloudApigeeV1SchemaSchemaElement', 3, repeated=True)


class GoogleCloudApigeeV1SchemaSchemaElement(_messages.Message):
  r"""Message type for the schema element

  Fields:
    name: Name of the field.
    properties: Properties for the schema field. For example: { "createTime":
      "2016-02-26T10:23:09.592Z", "custom": "false", "type": "string" }
  """

  name = _messages.StringField(1)
  properties = _messages.MessageField('GoogleCloudApigeeV1SchemaSchemaProperty', 2)


class GoogleCloudApigeeV1SchemaSchemaProperty(_messages.Message):
  r"""Properties for the schema field.

  Fields:
    createTime: Time the field was created in RFC3339 string form. For
      example: `2016-02-26T10:23:09.592Z`.
    custom: Flag that specifies whether the field is standard in the dataset
      or a custom field created by the customer. `true` indicates that it is a
      custom field.
    type: Data type of the field.
  """

  createTime = _messages.StringField(1)
  custom = _messages.StringField(2)
  type = _messages.StringField(3)


class GoogleCloudApigeeV1Score(_messages.Message):
  r"""Represents Security Score.

  Fields:
    component: Component containing score, recommendations and actions.
    subcomponents: List of all the drilldown score components.
    timeRange: Start and end time for the score.
  """

  component = _messages.MessageField('GoogleCloudApigeeV1ScoreComponent', 1)
  subcomponents = _messages.MessageField('GoogleCloudApigeeV1ScoreComponent', 2, repeated=True)
  timeRange = _messages.MessageField('GoogleTypeInterval', 3)


class GoogleCloudApigeeV1ScoreComponent(_messages.Message):
  r"""Component is an individual security element that is scored.

  Fields:
    calculateTime: Time when score was calculated.
    dataCaptureTime: Time in the requested time period when data was last
      captured to compute the score.
    drilldownPaths: List of paths for next components.
    recommendations: List of recommendations to improve API security.
    score: Score for the component.
    scorePath: Path of the component. Example:
      /org@myorg/envgroup@myenvgroup/proxies/proxy@myproxy
  """

  calculateTime = _messages.StringField(1)
  dataCaptureTime = _messages.StringField(2)
  drilldownPaths = _messages.StringField(3, repeated=True)
  recommendations = _messages.MessageField('GoogleCloudApigeeV1ScoreComponentRecommendation', 4, repeated=True)
  score = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  scorePath = _messages.StringField(6)


class GoogleCloudApigeeV1ScoreComponentRecommendation(_messages.Message):
  r"""Recommendation based on security concerns and score.

  Fields:
    actions: Actions for the recommendation to improve the security score.
    description: Description of the recommendation.
    impact: Potential impact of this recommendation on the overall score. This
      denotes how important this recommendation is to improve the score.
    title: Title represents recommendation title.
  """

  actions = _messages.MessageField('GoogleCloudApigeeV1ScoreComponentRecommendationAction', 1, repeated=True)
  description = _messages.StringField(2)
  impact = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  title = _messages.StringField(4)


class GoogleCloudApigeeV1ScoreComponentRecommendationAction(_messages.Message):
  r"""Action to improve security score.

  Fields:
    actionContext: Action context for the action.
    description: Description of the action.
  """

  actionContext = _messages.MessageField('GoogleCloudApigeeV1ScoreComponentRecommendationActionActionContext', 1)
  description = _messages.StringField(2)


class GoogleCloudApigeeV1ScoreComponentRecommendationActionActionContext(_messages.Message):
  r"""Action context are all the relevant details for the action.

  Fields:
    documentationLink: Documentation link for the action.
  """

  documentationLink = _messages.StringField(1)


class GoogleCloudApigeeV1SecurityAction(_messages.Message):
  r"""A SecurityAction is rule that can be enforced at an environment level.
  The result is one of: - A denied API call - An explicitly allowed API call -
  A flagged API call (HTTP headers added before the target receives it) At
  least one condition is required to create a SecurityAction.

  Enums:
    StateValueValuesEnum: Required. Only an ENABLED SecurityAction is
      enforced. An ENABLED SecurityAction past its expiration time will not be
      enforced.

  Fields:
    allow: Allow a request through if it matches this SecurityAction.
    conditionConfig: Required. A valid SecurityAction must contain at least
      one condition.
    createTime: Output only. The create time for this SecurityAction.
    deny: Deny a request through if it matches this SecurityAction.
    description: Optional. An optional user provided description of the
      SecurityAction.
    expireTime: The expiration for this SecurityAction.
    flag: Flag a request through if it matches this SecurityAction.
    name: Immutable. This field is ignored during creation as per AIP-133.
      Please set the `security_action_id` field in the
      CreateSecurityActionRequest when creating a new SecurityAction. Format:
      organizations/{org}/environments/{env}/securityActions/{security_action}
    state: Required. Only an ENABLED SecurityAction is enforced. An ENABLED
      SecurityAction past its expiration time will not be enforced.
    ttl: Input only. The TTL for this SecurityAction.
    updateTime: Output only. The update time for this SecurityAction. This
      reflects when this SecurityAction changed states.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Required. Only an ENABLED SecurityAction is enforced. An ENABLED
    SecurityAction past its expiration time will not be enforced.

    Values:
      STATE_UNSPECIFIED: The default value. This only exists for forward
        compatibility. A create request with this value will be rejected.
      ENABLED: An ENABLED SecurityAction is actively enforced if the
        `expiration_time` is in the future.
      DISABLED: A disabled SecurityAction is never enforced.
    """
    STATE_UNSPECIFIED = 0
    ENABLED = 1
    DISABLED = 2

  allow = _messages.MessageField('GoogleCloudApigeeV1SecurityActionAllow', 1)
  conditionConfig = _messages.MessageField('GoogleCloudApigeeV1SecurityActionConditionConfig', 2)
  createTime = _messages.StringField(3)
  deny = _messages.MessageField('GoogleCloudApigeeV1SecurityActionDeny', 4)
  description = _messages.StringField(5)
  expireTime = _messages.StringField(6)
  flag = _messages.MessageField('GoogleCloudApigeeV1SecurityActionFlag', 7)
  name = _messages.StringField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  ttl = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class GoogleCloudApigeeV1SecurityActionAllow(_messages.Message):
  r"""Message that should be set in case of an Allow Action. This does not
  have any fields.
  """



class GoogleCloudApigeeV1SecurityActionConditionConfig(_messages.Message):
  r"""The following are a list of conditions. A valid SecurityAction must
  contain at least one condition. Within a condition, each element is ORed.
  Across conditions elements are ANDed. For example if a SecurityAction has
  the following: api_keys: ["key1", "key2"] and developers: ["dev1", "dev2"]
  then this is interpreted as: enforce the action if the incoming request has
  ((api_key = "key1" OR api_key="key") AND (developer="dev1" OR
  developer="dev2"))

  Fields:
    botReasons: Optional. A list of Bot Reasons. Current options: Flooder,
      Brute Guessor, Static Content Scraper, OAuth Abuser, Robot Abuser,
      TorListRule, Advanced Anomaly Detection and Advanced API Scraper.
    ipAddressRanges: Optional. A list of IP ranges. This could be either IPv4
      or IPv6. 200,000 per rule at the environment level or 1000 at the proxy
      level.
  """

  botReasons = _messages.StringField(1, repeated=True)
  ipAddressRanges = _messages.StringField(2, repeated=True)


class GoogleCloudApigeeV1SecurityActionDeny(_messages.Message):
  r"""Message that should be set in case of a Deny Action.

  Fields:
    responseCode: Optional. The HTTP response code if the Action = DENY.
  """

  responseCode = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GoogleCloudApigeeV1SecurityActionFlag(_messages.Message):
  r"""The message that should be set in the case of a Flag action.

  Fields:
    headers: Optional. A list of HTTP headers to be sent to the target in case
      of a FLAG SecurityAction. Limit 5 headers per SecurityAction. At least
      one is mandatory.
  """

  headers = _messages.MessageField('GoogleCloudApigeeV1SecurityActionHttpHeader', 1, repeated=True)


class GoogleCloudApigeeV1SecurityActionHttpHeader(_messages.Message):
  r"""An HTTP header.

  Fields:
    name: The header name to be sent to the target.
    value: The header value to be sent to the target.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class GoogleCloudApigeeV1SecurityActionsConfig(_messages.Message):
  r"""SecurityActionsConfig reflects the current state of the SecurityActions
  feature. This is a singleton resource: https://google.aip.dev/156

  Fields:
    enabled: The flag that controls whether this feature is enabled. This is
      `unset` by default. When this flag is `false`, even if individual rules
      are enabled, no SecurityActions will be enforced.
    name: This is a singleton resource, the name will always be set by
      SecurityActions and any user input will be ignored. The name is always:
      `organizations/{org}/environments/{env}/security_actions_config`
    updateTime: Output only. The update time for configuration.
  """

  enabled = _messages.BooleanField(1)
  name = _messages.StringField(2)
  updateTime = _messages.StringField(3)


class GoogleCloudApigeeV1SecurityIncident(_messages.Message):
  r"""Represents an SecurityIncident resource.

  Enums:
    RiskLevelValueValuesEnum: Output only. Risk level of the incident.

  Fields:
    detectionTypes: Output only. Detection types which are part of the
      incident. Examples: Flooder, OAuth Abuser, Static Content Scraper,
      Anomaly Detection.
    displayName: Display name of the security incident.
    firstDetectedTime: Output only. The time when events associated with the
      incident were first detected.
    lastDetectedTime: Output only. The time when events associated with the
      incident were last detected.
    name: Immutable. Name of the security incident resource. Format: organizat
      ions/{org}/environments/{environment}/securityIncidents/{incident}
      Example: organizations/apigee-
      org/environments/dev/securityIncidents/1234-5678-9101-1111
    riskLevel: Output only. Risk level of the incident.
    trafficCount: Total traffic detected as part of the incident.
  """

  class RiskLevelValueValuesEnum(_messages.Enum):
    r"""Output only. Risk level of the incident.

    Values:
      RISK_LEVEL_UNSPECIFIED: Risk Level Unspecified.
      LOW: Risk level of the incident is low.
      MODERATE: Risk level of the incident is moderate.
      SEVERE: Risk level of the incident is severe.
    """
    RISK_LEVEL_UNSPECIFIED = 0
    LOW = 1
    MODERATE = 2
    SEVERE = 3

  detectionTypes = _messages.StringField(1, repeated=True)
  displayName = _messages.StringField(2)
  firstDetectedTime = _messages.StringField(3)
  lastDetectedTime = _messages.StringField(4)
  name = _messages.StringField(5)
  riskLevel = _messages.EnumField('RiskLevelValueValuesEnum', 6)
  trafficCount = _messages.IntegerField(7)


class GoogleCloudApigeeV1SecurityIncidentEnvironment(_messages.Message):
  r"""Represents an SecurityIncidentEnvironment resource.

  Fields:
    environment: Output only. Name of the environment
    lowRiskIncidentsCount: Output only. Total incidents with risk level low.
    moderateRiskIncidentsCount: Output only. Total incidents with risk level
      moderate.
    severeRiskIncidentsCount: Output only. Total incidents with risk level
      severe.
    totalIncidents: Output only. Total incidents count for a given environment
  """

  environment = _messages.StringField(1)
  lowRiskIncidentsCount = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  moderateRiskIncidentsCount = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  severeRiskIncidentsCount = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  totalIncidents = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class GoogleCloudApigeeV1SecurityProfile(_messages.Message):
  r"""Represents a SecurityProfile resource.

  Fields:
    description: Description of the security profile.
    displayName: Display name of the security profile.
    environments: List of environments attached to security profile.
    maxScore: Output only. Maximum security score that can be generated by
      this profile.
    minScore: Output only. Minimum security score that can be generated by
      this profile.
    name: Immutable. Name of the security profile resource. Format:
      organizations/{org}/securityProfiles/{profile}
    profileConfig: Required. Customized profile configuration that computes
      the security score.
    revisionCreateTime: Output only. The time when revision was created.
    revisionId: Output only. Revision ID of the security profile.
    revisionPublishTime: Output only. The time when revision was published.
      Once published, the security profile revision cannot be updated further
      and can be attached to environments.
    revisionUpdateTime: Output only. The time when revision was updated.
    scoringConfigs: List of profile scoring configs in this revision.
  """

  description = _messages.StringField(1)
  displayName = _messages.StringField(2)
  environments = _messages.MessageField('GoogleCloudApigeeV1SecurityProfileEnvironment', 3, repeated=True)
  maxScore = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  minScore = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  name = _messages.StringField(6)
  profileConfig = _messages.MessageField('GoogleCloudApigeeV1ProfileConfig', 7)
  revisionCreateTime = _messages.StringField(8)
  revisionId = _messages.IntegerField(9)
  revisionPublishTime = _messages.StringField(10)
  revisionUpdateTime = _messages.StringField(11)
  scoringConfigs = _messages.MessageField('GoogleCloudApigeeV1SecurityProfileScoringConfig', 12, repeated=True)


class GoogleCloudApigeeV1SecurityProfileEnvironment(_messages.Message):
  r"""Environment information of attached environments. Scoring an environment
  is enabled only if it is attached to a security profile.

  Fields:
    attachTime: Output only. Time at which environment was attached to the
      security profile.
    environment: Output only. Name of the environment.
  """

  attachTime = _messages.StringField(1)
  environment = _messages.StringField(2)


class GoogleCloudApigeeV1SecurityProfileEnvironmentAssociation(_messages.Message):
  r"""Represents a SecurityProfileEnvironmentAssociation resource.

  Fields:
    attachTime: Output only. The time when environment was attached to the
      security profile.
    name: Immutable. Name of the profile-environment association resource.
      Format:
      organizations/{org}/securityProfiles/{profile}/environments/{env}
    securityProfileRevisionId: Revision ID of the security profile.
  """

  attachTime = _messages.StringField(1)
  name = _messages.StringField(2)
  securityProfileRevisionId = _messages.IntegerField(3)


class GoogleCloudApigeeV1SecurityProfileScoringConfig(_messages.Message):
  r"""Security configurations to manage scoring.

  Fields:
    description: Description of the config.
    scorePath: Path of the component config used for scoring.
    title: Title of the config.
  """

  description = _messages.StringField(1)
  scorePath = _messages.StringField(2)
  title = _messages.StringField(3)


class GoogleCloudApigeeV1SecurityReport(_messages.Message):
  r"""SecurityReport saves all the information about the created security
  report.

  Fields:
    created: Creation time of the query.
    displayName: Display Name specified by the user.
    envgroupHostname: Hostname is available only when query is executed at
      host level.
    error: Error is set when query fails.
    executionTime: ExecutionTime is available only after the query is
      completed.
    queryParams: Contains information like metrics, dimenstions etc of the
      Security Report.
    reportDefinitionId: Report Definition ID.
    result: Result is available only after the query is completed.
    resultFileSize: ResultFileSize is available only after the query is
      completed.
    resultRows: ResultRows is available only after the query is completed.
    self: Self link of the query. Example: `/organizations/myorg/environments/
      myenv/securityReports/9cfc0d85-0f30-46d6-ae6f-318d0cb961bd` or following
      format if query is running at host level: `/organizations/myorg/hostSecu
      rityReports/9cfc0d85-0f30-46d6-ae6f-318d0cb961bd`
    state: Query state could be "enqueued", "running", "completed", "expired"
      and "failed".
    updated: Output only. Last updated timestamp for the query.
  """

  created = _messages.StringField(1)
  displayName = _messages.StringField(2)
  envgroupHostname = _messages.StringField(3)
  error = _messages.StringField(4)
  executionTime = _messages.StringField(5)
  queryParams = _messages.MessageField('GoogleCloudApigeeV1SecurityReportMetadata', 6)
  reportDefinitionId = _messages.StringField(7)
  result = _messages.MessageField('GoogleCloudApigeeV1SecurityReportResultMetadata', 8)
  resultFileSize = _messages.StringField(9)
  resultRows = _messages.IntegerField(10)
  self = _messages.StringField(11)
  state = _messages.StringField(12)
  updated = _messages.StringField(13)


class GoogleCloudApigeeV1SecurityReportMetadata(_messages.Message):
  r"""Metadata for the security report.

  Fields:
    dimensions: Dimensions of the SecurityReport.
    endTimestamp: End timestamp of the query range.
    metrics: Metrics of the SecurityReport. Example:
      ["name:bot_count,func:sum,alias:sum_bot_count"]
    mimeType: MIME type / Output format.
    startTimestamp: Start timestamp of the query range.
    timeUnit: Query GroupBy time unit. Example: "seconds", "minute", "hour"
  """

  dimensions = _messages.StringField(1, repeated=True)
  endTimestamp = _messages.StringField(2)
  metrics = _messages.StringField(3, repeated=True)
  mimeType = _messages.StringField(4)
  startTimestamp = _messages.StringField(5)
  timeUnit = _messages.StringField(6)


class GoogleCloudApigeeV1SecurityReportQuery(_messages.Message):
  r"""Body structure when user makes a request to create a security report.

  Fields:
    csvDelimiter: Delimiter used in the CSV file, if `outputFormat` is set to
      `csv`. Defaults to the `,` (comma) character. Supported delimiter
      characters include comma (`,`), pipe (`|`), and tab (`\t`).
    dimensions: A list of dimensions. https://docs.apigee.com/api-
      platform/analytics/analytics-reference#dimensions
    displayName: Security Report display name which users can specify.
    envgroupHostname: Hostname needs to be specified if query intends to run
      at host level. This field is only allowed when query is submitted by
      CreateHostSecurityReport where analytics data will be grouped by
      organization and hostname.
    filter: Boolean expression that can be used to filter data. Filter
      expressions can be combined using AND/OR terms and should be fully
      parenthesized to avoid ambiguity. See Analytics metrics, dimensions, and
      filters reference https://docs.apigee.com/api-
      platform/analytics/analytics-reference for more information on the
      fields available to filter on. For more information on the tokens that
      you use to build filter expressions, see Filter expression syntax.
      https://docs.apigee.com/api-platform/analytics/asynch-reports-
      api#filter-expression-syntax
    groupByTimeUnit: Time unit used to group the result set. Valid values
      include: second, minute, hour, day, week, or month. If a query includes
      groupByTimeUnit, then the result is an aggregation based on the
      specified time unit and the resultant timestamp does not include
      milliseconds precision. If a query omits groupByTimeUnit, then the
      resultant timestamp includes milliseconds precision.
    limit: Maximum number of rows that can be returned in the result.
    metrics: A list of Metrics.
    mimeType: Valid values include: `csv` or `json`. Defaults to `json`. Note:
      Configure the delimiter for CSV output using the csvDelimiter property.
    reportDefinitionId: Report Definition ID.
    timeRange: Required. Time range for the query. Can use the following
      predefined strings to specify the time range: `last60minutes`
      `last24hours` `last7days` Or, specify the timeRange as a structure
      describing start and end timestamps in the ISO format: yyyy-mm-
      ddThh:mm:ssZ. Example: "timeRange": { "start": "2018-07-29T00:13:00Z",
      "end": "2018-08-01T00:18:00Z" }
  """

  csvDelimiter = _messages.StringField(1)
  dimensions = _messages.StringField(2, repeated=True)
  displayName = _messages.StringField(3)
  envgroupHostname = _messages.StringField(4)
  filter = _messages.StringField(5)
  groupByTimeUnit = _messages.StringField(6)
  limit = _messages.IntegerField(7, variant=_messages.Variant.INT32)
  metrics = _messages.MessageField('GoogleCloudApigeeV1SecurityReportQueryMetric', 8, repeated=True)
  mimeType = _messages.StringField(9)
  reportDefinitionId = _messages.StringField(10)
  timeRange = _messages.MessageField('extra_types.JsonValue', 11)


class GoogleCloudApigeeV1SecurityReportQueryMetric(_messages.Message):
  r"""Metric of the Query

  Fields:
    aggregationFunction: Aggregation function: avg, min, max, or sum.
    alias: Alias for the metric. Alias will be used to replace metric name in
      query results.
    name: Required. Metric name.
    operator: One of `+`, `-`, `/`, `%`, `*`.
    value: Operand value should be provided when operator is set.
  """

  aggregationFunction = _messages.StringField(1)
  alias = _messages.StringField(2)
  name = _messages.StringField(3)
  operator = _messages.StringField(4)
  value = _messages.StringField(5)


class GoogleCloudApigeeV1SecurityReportResultMetadata(_messages.Message):
  r"""Contains informations about the security report results.

  Fields:
    expires: Output only. Expire_time is set to 7 days after report creation.
      Query result will be unaccessable after this time. Example:
      "2021-05-04T13:38:52-07:00"
    self: Self link of the query results. Example: `/organizations/myorg/envir
      onments/myenv/securityReports/9cfc0d85-0f30-46d6-ae6f-
      318d0cb961bd/result` or following format if query is running at host
      level: `/organizations/myorg/hostSecurityReports/9cfc0d85-0f30-46d6-
      ae6f-318d0cb961bd/result`
  """

  expires = _messages.StringField(1)
  self = _messages.StringField(2)


class GoogleCloudApigeeV1SecurityReportResultView(_messages.Message):
  r"""The response for security report result view APIs.

  Fields:
    code: Error code when there is a failure.
    error: Error message when there is a failure.
    metadata: Metadata contains information like metrics, dimenstions etc of
      the security report.
    rows: Rows of security report result. Each row is a JSON object. Example:
      {sum(message_count): 1, developer_app: "(not set)",...}
    state: State of retrieving ResultView.
  """

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  error = _messages.StringField(2)
  metadata = _messages.MessageField('GoogleCloudApigeeV1SecurityReportMetadata', 3)
  rows = _messages.MessageField('extra_types.JsonValue', 4, repeated=True)
  state = _messages.StringField(5)


class GoogleCloudApigeeV1ServiceIssuersMapping(_messages.Message):
  r"""A GoogleCloudApigeeV1ServiceIssuersMapping object.

  Fields:
    emailIds: List of trusted issuer email ids.
    service: String indicating the Apigee service name.
  """

  emailIds = _messages.StringField(1, repeated=True)
  service = _messages.StringField(2)


class GoogleCloudApigeeV1Session(_messages.Message):
  r"""Session carries the debug session id and its creation time.

  Fields:
    id: The debug session ID.
    timestampMs: The first transaction creation timestamp in millisecond,
      recorded by UAP.
  """

  id = _messages.StringField(1)
  timestampMs = _messages.IntegerField(2)


class GoogleCloudApigeeV1SetAddonsRequest(_messages.Message):
  r"""Request for SetAddons.

  Fields:
    addonsConfig: Required. Add-on configurations.
  """

  addonsConfig = _messages.MessageField('GoogleCloudApigeeV1AddonsConfig', 1)


class GoogleCloudApigeeV1SharedFlow(_messages.Message):
  r"""The metadata describing a shared flow

  Fields:
    latestRevisionId: The id of the most recently created revision for this
      shared flow.
    metaData: Metadata describing the shared flow.
    name: The ID of the shared flow.
    revision: A list of revisions of this shared flow.
  """

  latestRevisionId = _messages.StringField(1)
  metaData = _messages.MessageField('GoogleCloudApigeeV1EntityMetadata', 2)
  name = _messages.StringField(3)
  revision = _messages.StringField(4, repeated=True)


class GoogleCloudApigeeV1SharedFlowRevision(_messages.Message):
  r"""The metadata describing a shared flow revision.

  Messages:
    EntityMetaDataAsPropertiesValue: A Key-Value map of metadata about this
      shared flow revision.

  Fields:
    configurationVersion: The version of the configuration schema to which
      this shared flow conforms. The only supported value currently is
      majorVersion 4 and minorVersion 0. This setting may be used in the
      future to enable evolution of the shared flow format.
    contextInfo: A textual description of the shared flow revision.
    createdAt: Time at which this shared flow revision was created, in
      milliseconds since epoch.
    description: Description of the shared flow revision.
    displayName: The human readable name of this shared flow.
    entityMetaDataAsProperties: A Key-Value map of metadata about this shared
      flow revision.
    lastModifiedAt: Time at which this shared flow revision was most recently
      modified, in milliseconds since epoch.
    name: The resource ID of the parent shared flow.
    policies: A list of policy names included in this shared flow revision.
    resourceFiles: The resource files included in this shared flow revision.
    resources: A list of the resources included in this shared flow revision
      formatted as "{type}://{name}".
    revision: The resource ID of this revision.
    sharedFlows: A list of the shared flow names included in this shared flow
      revision.
    type: The string "Application"
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EntityMetaDataAsPropertiesValue(_messages.Message):
    r"""A Key-Value map of metadata about this shared flow revision.

    Messages:
      AdditionalProperty: An additional property for a
        EntityMetaDataAsPropertiesValue object.

    Fields:
      additionalProperties: Additional properties of type
        EntityMetaDataAsPropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EntityMetaDataAsPropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  configurationVersion = _messages.MessageField('GoogleCloudApigeeV1ConfigVersion', 1)
  contextInfo = _messages.StringField(2)
  createdAt = _messages.IntegerField(3)
  description = _messages.StringField(4)
  displayName = _messages.StringField(5)
  entityMetaDataAsProperties = _messages.MessageField('EntityMetaDataAsPropertiesValue', 6)
  lastModifiedAt = _messages.IntegerField(7)
  name = _messages.StringField(8)
  policies = _messages.StringField(9, repeated=True)
  resourceFiles = _messages.MessageField('GoogleCloudApigeeV1ResourceFiles', 10)
  resources = _messages.StringField(11, repeated=True)
  revision = _messages.StringField(12)
  sharedFlows = _messages.StringField(13, repeated=True)
  type = _messages.StringField(14)


class GoogleCloudApigeeV1Stats(_messages.Message):
  r"""Encapsulates a `stats` response.

  Fields:
    environments: List of query results on the environment level.
    hosts: List of query results grouped by host.
    metaData: Metadata information.
  """

  environments = _messages.MessageField('GoogleCloudApigeeV1StatsEnvironmentStats', 1, repeated=True)
  hosts = _messages.MessageField('GoogleCloudApigeeV1StatsHostStats', 2, repeated=True)
  metaData = _messages.MessageField('GoogleCloudApigeeV1Metadata', 3)


class GoogleCloudApigeeV1StatsEnvironmentStats(_messages.Message):
  r"""Encapsulates the environment wrapper: ``` "environments": [ { "metrics":
  [ { "name": "sum(message_count)", "values": [ "2.52056245E8" ] } ], "name":
  "prod" } ]```

  Fields:
    dimensions: List of metrics grouped under dimensions.
    metrics: In the final response, only one of the following fields will be
      present based on the dimensions provided. If no dimensions are provided,
      then only top-level metrics is provided. If dimensions are included,
      then there will be a top-level dimensions field under environments which
      will contain metrics values and the dimension name. Example: ```
      "environments": [ { "dimensions": [ { "metrics": [ { "name":
      "sum(message_count)", "values": [ "2.14049521E8" ] } ], "name":
      "nit_proxy" } ], "name": "prod" } ]``` or ```"environments": [ {
      "metrics": [ { "name": "sum(message_count)", "values": [ "2.19026331E8"
      ] } ], "name": "prod" } ]``` List of metric values.
    name: Name of the environment.
  """

  dimensions = _messages.MessageField('GoogleCloudApigeeV1DimensionMetric', 1, repeated=True)
  metrics = _messages.MessageField('GoogleCloudApigeeV1Metric', 2, repeated=True)
  name = _messages.StringField(3)


class GoogleCloudApigeeV1StatsHostStats(_messages.Message):
  r"""Encapsulates the hostname wrapper: ``` "hosts": [ { "metrics": [ {
  "name": "sum(message_count)", "values": [ "2.52056245E8" ] } ], "name":
  "example.com" } ]```

  Fields:
    dimensions: List of metrics grouped under dimensions.
    metrics: In the final response, only one of the following fields will be
      present based on the dimensions provided. If no dimensions are provided,
      then only the top-level metrics are provided. If dimensions are
      included, then there will be a top-level dimensions field under
      hostnames which will contain metrics values and the dimension name.
      Example: ``` "hosts": [ { "dimensions": [ { "metrics": [ { "name":
      "sum(message_count)", "values": [ "2.14049521E8" ] } ], "name":
      "nit_proxy" } ], "name": "example.com" } ]``` OR ```"hosts": [ {
      "metrics": [ { "name": "sum(message_count)", "values": [ "2.19026331E8"
      ] } ], "name": "example.com" } ]``` List of metric values.
    name: Hostname used in query.
  """

  dimensions = _messages.MessageField('GoogleCloudApigeeV1DimensionMetric', 1, repeated=True)
  metrics = _messages.MessageField('GoogleCloudApigeeV1Metric', 2, repeated=True)
  name = _messages.StringField(3)


class GoogleCloudApigeeV1Subscription(_messages.Message):
  r"""Pub/Sub subscription of an environment.

  Fields:
    name: Full name of the Pub/Sub subcription. Use the following structure in
      your request: `subscription "projects/foo/subscription/bar"`
  """

  name = _messages.StringField(1)


class GoogleCloudApigeeV1SyncAuthorization(_messages.Message):
  r"""A GoogleCloudApigeeV1SyncAuthorization object.

  Fields:
    etag: Entity tag (ETag) used for optimistic concurrency control as a way
      to help prevent simultaneous updates from overwriting each other. For
      example, when you call
      [getSyncAuthorization](organizations/getSyncAuthorization) an ETag is
      returned in the response. Pass that ETag when calling the
      [setSyncAuthorization](organizations/setSyncAuthorization) to ensure
      that you are updating the correct version. If you don't pass the ETag in
      the call to `setSyncAuthorization`, then the existing authorization is
      overwritten indiscriminately. **Note**: We strongly recommend that you
      use the ETag in the read-modify-write cycle to avoid race conditions.
    identities: Required. Array of service accounts to grant access to control
      plane resources, each specified using the following format:
      `serviceAccount:` service-account-name. The service-account-name is
      formatted like an email address. For example: `my-synchronizer-manager-
      service_account@my_project_id.iam.gserviceaccount.com` You might specify
      multiple service accounts, for example, if you have multiple
      environments and wish to assign a unique service account to each one.
      The service accounts must have **Apigee Synchronizer Manager** role. See
      also [Create service
      accounts](https://cloud.google.com/apigee/docs/hybrid/latest/sa-
      about#create-the-service-accounts).
  """

  etag = _messages.BytesField(1)
  identities = _messages.StringField(2, repeated=True)


class GoogleCloudApigeeV1TargetServer(_messages.Message):
  r"""TargetServer configuration. TargetServers are used to decouple a proxy
  TargetEndpoint HTTPTargetConnections from concrete URLs for backend
  services.

  Enums:
    ProtocolValueValuesEnum: Immutable. The protocol used by this
      TargetServer.

  Fields:
    description: Optional. A human-readable description of this TargetServer.
    host: Required. The host name this target connects to. Value must be a
      valid hostname as described by RFC-1123.
    isEnabled: Optional. Enabling/disabling a TargetServer is useful when
      TargetServers are used in load balancing configurations, and one or more
      TargetServers need to taken out of rotation periodically. Defaults to
      true.
    name: Required. The resource id of this target server. Values must match
      the regular expression
    port: Required. The port number this target connects to on the given host.
      Value must be between 1 and 65535, inclusive.
    protocol: Immutable. The protocol used by this TargetServer.
    sSLInfo: Optional. Specifies TLS configuration info for this TargetServer.
      The JSON name is `sSLInfo` for legacy/backwards compatibility reasons --
      Edge originally supported SSL, and the name is still used for TLS
      configuration.
  """

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""Immutable. The protocol used by this TargetServer.

    Values:
      PROTOCOL_UNSPECIFIED: UNSPECIFIED defaults to HTTP for backwards
        compatibility.
      HTTP: The TargetServer uses HTTP.
      HTTP2: The TargetSever uses HTTP2.
      GRPC_TARGET: The TargetServer uses GRPC.
      GRPC: GRPC TargetServer to be used in ExternalCallout Policy. Prefer to
        use EXTERNAL_CALLOUT instead. TODO(b/266125112) deprecate once
        EXTERNAL _CALLOUT generally available.
      EXTERNAL_CALLOUT: The TargetServer is to be used in the ExternalCallout
        Policy
    """
    PROTOCOL_UNSPECIFIED = 0
    HTTP = 1
    HTTP2 = 2
    GRPC_TARGET = 3
    GRPC = 4
    EXTERNAL_CALLOUT = 5

  description = _messages.StringField(1)
  host = _messages.StringField(2)
  isEnabled = _messages.BooleanField(3)
  name = _messages.StringField(4)
  port = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 6)
  sSLInfo = _messages.MessageField('GoogleCloudApigeeV1TlsInfo', 7)


class GoogleCloudApigeeV1TargetServerConfig(_messages.Message):
  r"""A GoogleCloudApigeeV1TargetServerConfig object.

  Enums:
    ProtocolValueValuesEnum: The protocol used by this target server.

  Fields:
    enabled: Whether the target server is enabled. An empty/omitted value for
      this field should be interpreted as true.
    host: Host name of the target server.
    name: Target server revision name in the following format: `organizations/
      {org}/environments/{env}/targetservers/{targetserver}/revisions/{rev}`
    port: Port number for the target server.
    protocol: The protocol used by this target server.
    tlsInfo: TLS settings for the target server.
  """

  class ProtocolValueValuesEnum(_messages.Enum):
    r"""The protocol used by this target server.

    Values:
      PROTOCOL_UNSPECIFIED: UNSPECIFIED defaults to HTTP for backwards
        compatibility.
      HTTP: The TargetServer uses HTTP.
      HTTP2: The TargetSever uses HTTP2.
      GRPC_TARGET: The TargetServer uses GRPC.
      GRPC: GRPC TargetServer to be used in ExternalCallout Policy. Prefer to
        use EXTERNAL_CALLOUT instead. TODO(b/266125112) deprecate once
        EXTERNAL _CALLOUT generally available.
      EXTERNAL_CALLOUT: The TargetServer is to be used in the ExternalCallout
        Policy
    """
    PROTOCOL_UNSPECIFIED = 0
    HTTP = 1
    HTTP2 = 2
    GRPC_TARGET = 3
    GRPC = 4
    EXTERNAL_CALLOUT = 5

  enabled = _messages.BooleanField(1)
  host = _messages.StringField(2)
  name = _messages.StringField(3)
  port = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  protocol = _messages.EnumField('ProtocolValueValuesEnum', 5)
  tlsInfo = _messages.MessageField('GoogleCloudApigeeV1TlsInfoConfig', 6)


class GoogleCloudApigeeV1TestDatastoreResponse(_messages.Message):
  r"""The response for TestDatastore

  Fields:
    error: Output only. Error message of test connection failure
    state: Output only. It could be `completed` or `failed`
  """

  error = _messages.StringField(1)
  state = _messages.StringField(2)


class GoogleCloudApigeeV1TlsInfo(_messages.Message):
  r"""TLS configuration information for virtual hosts and TargetServers.

  Fields:
    ciphers: The SSL/TLS cipher suites to be used. For programmable proxies,
      it must be one of the cipher suite names listed in: http://docs.oracle.c
      om/javase/8/docs/technotes/guides/security/StandardNames.html#ciphersuit
      es. For configurable proxies, it must follow the configuration specified
      in: https://commondatastorage.googleapis.com/chromium-boringssl-
      docs/ssl.h.html#Cipher-suite-configuration. This setting has no effect
      for configurable proxies when negotiating TLS 1.3.
    clientAuthEnabled: Optional. Enables two-way TLS.
    commonName: The TLS Common Name of the certificate.
    enabled: Required. Enables TLS. If false, neither one-way nor two-way TLS
      will be enabled.
    ignoreValidationErrors: If true, Edge ignores TLS certificate errors.
      Valid when configuring TLS for target servers and target endpoints, and
      when configuring virtual hosts that use 2-way TLS. When used with a
      target endpoint/target server, if the backend system uses SNI and
      returns a cert with a subject Distinguished Name (DN) that does not
      match the hostname, there is no way to ignore the error and the
      connection fails.
    keyAlias: Required if `client_auth_enabled` is true. The resource ID for
      the alias containing the private key and cert.
    keyStore: Required if `client_auth_enabled` is true. The resource ID of
      the keystore.
    protocols: The TLS versioins to be used.
    trustStore: The resource ID of the truststore.
  """

  ciphers = _messages.StringField(1, repeated=True)
  clientAuthEnabled = _messages.BooleanField(2)
  commonName = _messages.MessageField('GoogleCloudApigeeV1TlsInfoCommonName', 3)
  enabled = _messages.BooleanField(4)
  ignoreValidationErrors = _messages.BooleanField(5)
  keyAlias = _messages.StringField(6)
  keyStore = _messages.StringField(7)
  protocols = _messages.StringField(8, repeated=True)
  trustStore = _messages.StringField(9)


class GoogleCloudApigeeV1TlsInfoCommonName(_messages.Message):
  r"""A GoogleCloudApigeeV1TlsInfoCommonName object.

  Fields:
    value: The TLS Common Name string of the certificate.
    wildcardMatch: Indicates whether the cert should be matched against as a
      wildcard cert.
  """

  value = _messages.StringField(1)
  wildcardMatch = _messages.BooleanField(2)


class GoogleCloudApigeeV1TlsInfoConfig(_messages.Message):
  r"""A GoogleCloudApigeeV1TlsInfoConfig object.

  Fields:
    ciphers: List of ciphers that are granted access.
    clientAuthEnabled: Flag that specifies whether client-side authentication
      is enabled for the target server. Enables two-way TLS.
    commonName: Common name to validate the target server against.
    enabled: Flag that specifies whether one-way TLS is enabled. Set to `true`
      to enable one-way TLS.
    ignoreValidationErrors: Flag that specifies whether to ignore TLS
      certificate validation errors. Set to `true` to ignore errors.
    keyAlias: Name of the alias used for client-side authentication in the
      following format: `organizations/{org}/environments/{env}/keystores/{key
      store}/aliases/{alias}`
    keyAliasReference: Reference name and alias pair to use for client-side
      authentication.
    protocols: List of TLS protocols that are granted access.
    trustStore: Name of the keystore or keystore reference containing trusted
      certificates for the server in the following format:
      `organizations/{org}/environments/{env}/keystores/{keystore}` or
      `organizations/{org}/environments/{env}/references/{reference}`
  """

  ciphers = _messages.StringField(1, repeated=True)
  clientAuthEnabled = _messages.BooleanField(2)
  commonName = _messages.MessageField('GoogleCloudApigeeV1CommonNameConfig', 3)
  enabled = _messages.BooleanField(4)
  ignoreValidationErrors = _messages.BooleanField(5)
  keyAlias = _messages.StringField(6)
  keyAliasReference = _messages.MessageField('GoogleCloudApigeeV1KeyAliasReference', 7)
  protocols = _messages.StringField(8, repeated=True)
  trustStore = _messages.StringField(9)


class GoogleCloudApigeeV1TraceConfig(_messages.Message):
  r"""TraceConfig defines the configurations in an environment of distributed
  trace.

  Enums:
    ExporterValueValuesEnum: Required. Exporter that is used to view the
      distributed trace captured using OpenCensus. An exporter sends traces to
      any backend that is capable of consuming them. Recorded spans can be
      exported by registered exporters.

  Fields:
    endpoint: Required. Endpoint of the exporter.
    exporter: Required. Exporter that is used to view the distributed trace
      captured using OpenCensus. An exporter sends traces to any backend that
      is capable of consuming them. Recorded spans can be exported by
      registered exporters.
    samplingConfig: Distributed trace configuration for all API proxies in an
      environment. You can also override the configuration for a specific API
      proxy using the distributed trace configuration overrides API.
  """

  class ExporterValueValuesEnum(_messages.Enum):
    r"""Required. Exporter that is used to view the distributed trace captured
    using OpenCensus. An exporter sends traces to any backend that is capable
    of consuming them. Recorded spans can be exported by registered exporters.

    Values:
      EXPORTER_UNSPECIFIED: Exporter unspecified
      JAEGER: Jaeger exporter
      CLOUD_TRACE: Cloudtrace exporter
    """
    EXPORTER_UNSPECIFIED = 0
    JAEGER = 1
    CLOUD_TRACE = 2

  endpoint = _messages.StringField(1)
  exporter = _messages.EnumField('ExporterValueValuesEnum', 2)
  samplingConfig = _messages.MessageField('GoogleCloudApigeeV1TraceSamplingConfig', 3)


class GoogleCloudApigeeV1TraceConfigOverride(_messages.Message):
  r"""A representation of a configuration override.

  Fields:
    apiProxy: ID of the API proxy that will have its trace configuration
      overridden.
    name: ID of the trace configuration override specified as a system-
      generated UUID.
    samplingConfig: Trace configuration to override.
  """

  apiProxy = _messages.StringField(1)
  name = _messages.StringField(2)
  samplingConfig = _messages.MessageField('GoogleCloudApigeeV1TraceSamplingConfig', 3)


class GoogleCloudApigeeV1TraceSamplingConfig(_messages.Message):
  r"""TraceSamplingConfig represents the detail settings of distributed
  tracing. Only the fields that are defined in the distributed trace
  configuration can be overridden using the distribute trace configuration
  override APIs.

  Enums:
    SamplerValueValuesEnum: Sampler of distributed tracing. OFF is the default
      value.

  Fields:
    sampler: Sampler of distributed tracing. OFF is the default value.
    samplingRate: Field sampling rate. This value is only applicable when
      using the PROBABILITY sampler. The supported values are > 0 and <= 0.5.
  """

  class SamplerValueValuesEnum(_messages.Enum):
    r"""Sampler of distributed tracing. OFF is the default value.

    Values:
      SAMPLER_UNSPECIFIED: Sampler unspecified.
      OFF: OFF means distributed trace is disabled, or the sampling
        probability is 0.
      PROBABILITY: PROBABILITY means traces are captured on a probability that
        defined by sampling_rate. The sampling rate is limited to 0 to 0.5
        when this is set.
    """
    SAMPLER_UNSPECIFIED = 0
    OFF = 1
    PROBABILITY = 2

  sampler = _messages.EnumField('SamplerValueValuesEnum', 1)
  samplingRate = _messages.FloatField(2, variant=_messages.Variant.FLOAT)


class GoogleCloudApigeeV1UndeleteOrganizationRequest(_messages.Message):
  r"""Request for UndeleteOrganization."""


class GoogleCloudApigeeV1UpdateAppGroupAppKeyRequest(_messages.Message):
  r"""Request for UpdateAppGroupAppKey

  Fields:
    action: Approve or revoke the consumer key by setting this value to
      `approve` or `revoke` respectively. The `Content-Type` header, if set,
      must be set to `application/octet-stream`, with empty body.
    apiProducts: The list of API products that will be associated with the
      credential. This list will be appended to the existing list of
      associated API Products for this App Key. Duplicates will be ignored.
    appGroupAppKey: The new AppGroupKey to be amended. Note that the status
      can be updated only via action.
  """

  action = _messages.StringField(1)
  apiProducts = _messages.StringField(2, repeated=True)
  appGroupAppKey = _messages.MessageField('GoogleCloudApigeeV1AppGroupAppKey', 3)


class GoogleCloudApigeeV1UpdateError(_messages.Message):
  r"""Details on why a resource update failed in the runtime.

  Enums:
    CodeValueValuesEnum: Status code.

  Fields:
    code: Status code.
    message: User-friendly error message.
    resource: The sub resource specific to this error (e.g. a proxy deployed
      within the EnvironmentConfig). If empty the error refers to the top
      level resource.
    type: A string that uniquely identifies the type of error. This provides a
      more reliable means to deduplicate errors across revisions and
      instances.
  """

  class CodeValueValuesEnum(_messages.Enum):
    r"""Status code.

    Values:
      OK: Not an error; returned on success. HTTP Mapping: 200 OK
      CANCELLED: The operation was cancelled, typically by the caller. HTTP
        Mapping: 499 Client Closed Request
      UNKNOWN: Unknown error. For example, this error may be returned when a
        `Status` value received from another address space belongs to an error
        space that is not known in this address space. Also errors raised by
        APIs that do not return enough error information may be converted to
        this error. HTTP Mapping: 500 Internal Server Error
      INVALID_ARGUMENT: The client specified an invalid argument. Note that
        this differs from `FAILED_PRECONDITION`. `INVALID_ARGUMENT` indicates
        arguments that are problematic regardless of the state of the system
        (e.g., a malformed file name). HTTP Mapping: 400 Bad Request
      DEADLINE_EXCEEDED: The deadline expired before the operation could
        complete. For operations that change the state of the system, this
        error may be returned even if the operation has completed
        successfully. For example, a successful response from a server could
        have been delayed long enough for the deadline to expire. HTTP
        Mapping: 504 Gateway Timeout
      NOT_FOUND: Some requested entity (e.g., file or directory) was not
        found. Note to server developers: if a request is denied for an entire
        class of users, such as gradual feature rollout or undocumented
        allowlist, `NOT_FOUND` may be used. If a request is denied for some
        users within a class of users, such as user-based access control,
        `PERMISSION_DENIED` must be used. HTTP Mapping: 404 Not Found
      ALREADY_EXISTS: The entity that a client attempted to create (e.g., file
        or directory) already exists. HTTP Mapping: 409 Conflict
      PERMISSION_DENIED: The caller does not have permission to execute the
        specified operation. `PERMISSION_DENIED` must not be used for
        rejections caused by exhausting some resource (use
        `RESOURCE_EXHAUSTED` instead for those errors). `PERMISSION_DENIED`
        must not be used if the caller can not be identified (use
        `UNAUTHENTICATED` instead for those errors). This error code does not
        imply the request is valid or the requested entity exists or satisfies
        other pre-conditions. HTTP Mapping: 403 Forbidden
      UNAUTHENTICATED: The request does not have valid authentication
        credentials for the operation. HTTP Mapping: 401 Unauthorized
      RESOURCE_EXHAUSTED: Some resource has been exhausted, perhaps a per-user
        quota, or perhaps the entire file system is out of space. HTTP
        Mapping: 429 Too Many Requests
      FAILED_PRECONDITION: The operation was rejected because the system is
        not in a state required for the operation's execution. For example,
        the directory to be deleted is non-empty, an rmdir operation is
        applied to a non-directory, etc. Service implementors can use the
        following guidelines to decide between `FAILED_PRECONDITION`,
        `ABORTED`, and `UNAVAILABLE`: (a) Use `UNAVAILABLE` if the client can
        retry just the failing call. (b) Use `ABORTED` if the client should
        retry at a higher level. For example, when a client-specified test-
        and-set fails, indicating the client should restart a read-modify-
        write sequence. (c) Use `FAILED_PRECONDITION` if the client should not
        retry until the system state has been explicitly fixed. For example,
        if an "rmdir" fails because the directory is non-empty,
        `FAILED_PRECONDITION` should be returned since the client should not
        retry unless the files are deleted from the directory. HTTP Mapping:
        400 Bad Request
      ABORTED: The operation was aborted, typically due to a concurrency issue
        such as a sequencer check failure or transaction abort. See the
        guidelines above for deciding between `FAILED_PRECONDITION`,
        `ABORTED`, and `UNAVAILABLE`. HTTP Mapping: 409 Conflict
      OUT_OF_RANGE: The operation was attempted past the valid range. E.g.,
        seeking or reading past end-of-file. Unlike `INVALID_ARGUMENT`, this
        error indicates a problem that may be fixed if the system state
        changes. For example, a 32-bit file system will generate
        `INVALID_ARGUMENT` if asked to read at an offset that is not in the
        range [0,2^32-1], but it will generate `OUT_OF_RANGE` if asked to read
        from an offset past the current file size. There is a fair bit of
        overlap between `FAILED_PRECONDITION` and `OUT_OF_RANGE`. We recommend
        using `OUT_OF_RANGE` (the more specific error) when it applies so that
        callers who are iterating through a space can easily look for an
        `OUT_OF_RANGE` error to detect when they are done. HTTP Mapping: 400
        Bad Request
      UNIMPLEMENTED: The operation is not implemented or is not
        supported/enabled in this service. HTTP Mapping: 501 Not Implemented
      INTERNAL: Internal errors. This means that some invariants expected by
        the underlying system have been broken. This error code is reserved
        for serious errors. HTTP Mapping: 500 Internal Server Error
      UNAVAILABLE: The service is currently unavailable. This is most likely a
        transient condition, which can be corrected by retrying with a
        backoff. Note that it is not always safe to retry non-idempotent
        operations. See the guidelines above for deciding between
        `FAILED_PRECONDITION`, `ABORTED`, and `UNAVAILABLE`. HTTP Mapping: 503
        Service Unavailable
      DATA_LOSS: Unrecoverable data loss or corruption. HTTP Mapping: 500
        Internal Server Error
    """
    OK = 0
    CANCELLED = 1
    UNKNOWN = 2
    INVALID_ARGUMENT = 3
    DEADLINE_EXCEEDED = 4
    NOT_FOUND = 5
    ALREADY_EXISTS = 6
    PERMISSION_DENIED = 7
    UNAUTHENTICATED = 8
    RESOURCE_EXHAUSTED = 9
    FAILED_PRECONDITION = 10
    ABORTED = 11
    OUT_OF_RANGE = 12
    UNIMPLEMENTED = 13
    INTERNAL = 14
    UNAVAILABLE = 15
    DATA_LOSS = 16

  code = _messages.EnumField('CodeValueValuesEnum', 1)
  message = _messages.StringField(2)
  resource = _messages.StringField(3)
  type = _messages.StringField(4)


class GoogleIamV1AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('GoogleIamV1AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class GoogleIamV1AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class GoogleIamV1Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('GoogleTypeExpr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class GoogleIamV1Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('GoogleIamV1AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('GoogleIamV1Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class GoogleIamV1SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('GoogleIamV1Policy', 1)
  updateMask = _messages.StringField(2)


class GoogleIamV1TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class GoogleIamV1TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class GoogleLongrunningListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('GoogleLongrunningOperation', 2, repeated=True)


class GoogleLongrunningOperation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('GoogleRpcStatus', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class GoogleProtobufEmpty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class GoogleRpcPreconditionFailure(_messages.Message):
  r"""Describes what preconditions have failed. For example, if an RPC failed
  because it required the Terms of Service to be acknowledged, it could list
  the terms of service violation in the PreconditionFailure message.

  Fields:
    violations: Describes all precondition violations.
  """

  violations = _messages.MessageField('GoogleRpcPreconditionFailureViolation', 1, repeated=True)


class GoogleRpcPreconditionFailureViolation(_messages.Message):
  r"""A message type used to describe a single precondition failure.

  Fields:
    description: A description of how the precondition failed. Developers can
      use this description to understand how to fix the failure. For example:
      "Terms of service not accepted".
    subject: The subject, relative to the type, that failed. For example,
      "google.com/cloud" relative to the "TOS" type would indicate which terms
      of service is being referenced.
    type: The type of PreconditionFailure. We recommend using a service-
      specific enum type to define the supported precondition violation
      subjects. For example, "TOS" for "Terms of Service violation".
  """

  description = _messages.StringField(1)
  subject = _messages.StringField(2)
  type = _messages.StringField(3)


class GoogleRpcStatus(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class GoogleTypeExpr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GoogleTypeInterval(_messages.Message):
  r"""Represents a time interval, encoded as a Timestamp start (inclusive) and
  a Timestamp end (exclusive). The start must be less than or equal to the
  end. When the start equals the end, the interval is empty (matches no time).
  When both start and end are unspecified, the interval matches any time.

  Fields:
    endTime: Optional. Exclusive end of the interval. If specified, a
      Timestamp matching this interval will have to be before the end.
    startTime: Optional. Inclusive start of the interval. If specified, a
      Timestamp matching this interval will have to be the same or after the
      start.
  """

  endTime = _messages.StringField(1)
  startTime = _messages.StringField(2)


class GoogleTypeMoney(_messages.Message):
  r"""Represents an amount of money with its currency type.

  Fields:
    currencyCode: The three-letter currency code defined in ISO 4217.
    nanos: Number of nano (10^-9) units of the amount. The value must be
      between -999,999,999 and +999,999,999 inclusive. If `units` is positive,
      `nanos` must be positive or zero. If `units` is zero, `nanos` can be
      positive, zero, or negative. If `units` is negative, `nanos` must be
      negative or zero. For example $-1.75 is represented as `units`=-1 and
      `nanos`=-750,000,000.
    units: The whole units of the amount. For example if `currencyCode` is
      `"USD"`, then 1 unit is one US dollar.
  """

  currencyCode = _messages.StringField(1)
  nanos = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  units = _messages.IntegerField(3)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

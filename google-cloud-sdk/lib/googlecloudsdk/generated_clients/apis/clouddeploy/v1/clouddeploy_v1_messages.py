"""Generated message classes for clouddeploy version v1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'clouddeploy'


class AbandonReleaseRequest(_messages.Message):
  r"""The request object used by `AbandonRelease`."""


class AbandonReleaseResponse(_messages.Message):
  r"""The response object for `AbandonRelease`."""


class AdvanceChildRolloutJob(_messages.Message):
  r"""An advanceChildRollout Job."""


class AdvanceChildRolloutJobRun(_messages.Message):
  r"""AdvanceChildRolloutJobRun contains information specific to a
  advanceChildRollout `JobRun`.

  Fields:
    rollout: Output only. Name of the `ChildRollout`. Format is
      projects/{project}/
      locations/{location}/deliveryPipelines/{deliveryPipeline}/
      releases/{release}/rollouts/a-z{0,62}.
    rolloutPhaseId: Output only. the ID of the ChildRollout's Phase.
  """

  rollout = _messages.StringField(1)
  rolloutPhaseId = _messages.StringField(2)


class AdvanceRolloutOperation(_messages.Message):
  r"""Contains the information of an automated advance-rollout operation.

  Fields:
    destinationPhase: Output only. The phase to which the rollout will be
      advanced to.
    rollout: Output only. The name of the rollout that initiates the
      `AutomationRun`.
    sourcePhase: Output only. The phase of a deployment that initiated the
      operation.
    wait: Output only. How long the operation will be paused.
  """

  destinationPhase = _messages.StringField(1)
  rollout = _messages.StringField(2)
  sourcePhase = _messages.StringField(3)
  wait = _messages.StringField(4)


class AdvanceRolloutRequest(_messages.Message):
  r"""The request object used by `AdvanceRollout`.

  Fields:
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      projects/{project}/ locations/{location}/deployPolicies/a-z{0,62}.
    phaseId: Required. The phase ID to advance the `Rollout` to.
  """

  overrideDeployPolicy = _messages.StringField(1, repeated=True)
  phaseId = _messages.StringField(2)


class AdvanceRolloutResponse(_messages.Message):
  r"""The response object from `AdvanceRollout`."""


class AdvanceRolloutRule(_messages.Message):
  r"""The `AdvanceRollout` automation rule will automatically advance a
  successful Rollout to the next phase.

  Enums:
    WaitPolicyValueValuesEnum: Optional. WaitForDeployPolicy delays a rollout
      advancement when a deploy policy violation is encountered.

  Fields:
    condition: Output only. Information around the state of the Automation
      rule.
    id: Required. ID of the rule. This id must be unique in the `Automation`
      resource to which this rule belongs. The format is a-z{0,62}.
    phases: Optional. Deprecated: use source_phases instead. Proceeds only
      after phase name matched any one in the list. This value must consist of
      lower-case letters, numbers, and hyphens, start with a letter and end
      with a letter or a number, and have a max length of 63 characters. In
      other words, it must match the following regex:
      `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    sourcePhases: Optional. Proceeds only after phase name matched any one in
      the list. This value must consist of lower-case letters, numbers, and
      hyphens, start with a letter and end with a letter or a number, and have
      a max length of 63 characters. In other words, it must match the
      following regex: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    wait: Optional. How long to wait after a rollout is finished.
    waitPolicy: Optional. WaitForDeployPolicy delays a rollout advancement
      when a deploy policy violation is encountered.
  """

  class WaitPolicyValueValuesEnum(_messages.Enum):
    r"""Optional. WaitForDeployPolicy delays a rollout advancement when a
    deploy policy violation is encountered.

    Values:
      WAIT_FOR_DEPLOY_POLICY_UNSPECIFIED: No WaitForDeployPolicy is specified.
      NEVER: Never waits on DeployPolicy, terminates `AutomationRun` if
        DeployPolicy check failed.
      LATEST: When policy passes, execute the latest `AutomationRun` only.
    """
    WAIT_FOR_DEPLOY_POLICY_UNSPECIFIED = 0
    NEVER = 1
    LATEST = 2

  condition = _messages.MessageField('AutomationRuleCondition', 1)
  id = _messages.StringField(2)
  phases = _messages.StringField(3, repeated=True)
  sourcePhases = _messages.StringField(4, repeated=True)
  wait = _messages.StringField(5)
  waitPolicy = _messages.EnumField('WaitPolicyValueValuesEnum', 6)


class AnthosCluster(_messages.Message):
  r"""Information specifying an Anthos Cluster.

  Fields:
    membership: Membership of the GKE Hub-registered cluster to which to apply
      the Skaffold configuration. Format is
      `projects/{project}/locations/{location}/memberships/{membership_name}`.
  """

  membership = _messages.StringField(1)


class ApproveRolloutRequest(_messages.Message):
  r"""The request object used by `ApproveRollout`.

  Fields:
    approved: Required. True = approve; false = reject
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      projects/{project}/ locations/{location}/deployPolicies/a-z{0,62}.
  """

  approved = _messages.BooleanField(1)
  overrideDeployPolicy = _messages.StringField(2, repeated=True)


class ApproveRolloutResponse(_messages.Message):
  r"""The response object from `ApproveRollout`."""


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class Automation(_messages.Message):
  r"""An `Automation` resource in the Cloud Deploy API. An `Automation`
  enables the automation of manually driven actions for a Delivery Pipeline,
  which includes Release promotion amongst Targets, Rollout repair and Rollout
  deployment strategy advancement. The intention of Automation is to reduce
  manual intervention in the continuous delivery process.

  Messages:
    AnnotationsValue: Optional. User annotations. These attributes can only be
      set and used by the user, and not by Cloud Deploy. Annotations must meet
      the following constraints: * Annotations are key/value pairs. * Valid
      annotation keys have two segments: an optional prefix and name,
      separated by a slash (/). * The name segment is required and must be 63
      characters or less, beginning and ending with an alphanumeric character
      ([a-z0-9A-Z]) with dashes (-), underscores (_), dots (.), and
      alphanumerics between. * The prefix is optional. If specified, the
      prefix must be a DNS subdomain: a series of DNS labels separated by
      dots(.), not longer than 253 characters in total, followed by a slash
      (/). See https://kubernetes.io/docs/concepts/overview/working-with-
      objects/annotations/#syntax-and-character-set for more details.
    LabelsValue: Optional. Labels are attributes that can be set and used by
      both the user and by Cloud Deploy. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. Both keys and values are
      additionally constrained to be <= 63 characters.

  Fields:
    annotations: Optional. User annotations. These attributes can only be set
      and used by the user, and not by Cloud Deploy. Annotations must meet the
      following constraints: * Annotations are key/value pairs. * Valid
      annotation keys have two segments: an optional prefix and name,
      separated by a slash (/). * The name segment is required and must be 63
      characters or less, beginning and ending with an alphanumeric character
      ([a-z0-9A-Z]) with dashes (-), underscores (_), dots (.), and
      alphanumerics between. * The prefix is optional. If specified, the
      prefix must be a DNS subdomain: a series of DNS labels separated by
      dots(.), not longer than 253 characters in total, followed by a slash
      (/). See https://kubernetes.io/docs/concepts/overview/working-with-
      objects/annotations/#syntax-and-character-set for more details.
    createTime: Output only. Time at which the automation was created.
    description: Optional. Description of the `Automation`. Max length is 255
      characters.
    etag: Optional. The weak etag of the `Automation` resource. This checksum
      is computed by the server based on the value of other fields, and may be
      sent on update and delete requests to ensure the client has an up-to-
      date value before proceeding.
    labels: Optional. Labels are attributes that can be set and used by both
      the user and by Cloud Deploy. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. Both keys and values are
      additionally constrained to be <= 63 characters.
    name: Output only. Name of the `Automation`. Format is projects/{project}/
      locations/{location}/deliveryPipelines/{delivery_pipeline}/automations/{
      automation}.
    rules: Required. List of Automation rules associated with the Automation
      resource. Must have at least one rule and limited to 250 rules per
      Delivery Pipeline. Note: the order of the rules here is not the same as
      the order of execution.
    selector: Required. Selected resources to which the automation will be
      applied.
    serviceAccount: Required. Email address of the user-managed IAM service
      account that creates Cloud Deploy release and rollout resources.
    suspended: Optional. When Suspended, automation is deactivated from
      execution.
    uid: Output only. Unique identifier of the `Automation`.
    updateTime: Output only. Time at which the automation was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User annotations. These attributes can only be set and used
    by the user, and not by Cloud Deploy. Annotations must meet the following
    constraints: * Annotations are key/value pairs. * Valid annotation keys
    have two segments: an optional prefix and name, separated by a slash (/).
    * The name segment is required and must be 63 characters or less,
    beginning and ending with an alphanumeric character ([a-z0-9A-Z]) with
    dashes (-), underscores (_), dots (.), and alphanumerics between. * The
    prefix is optional. If specified, the prefix must be a DNS subdomain: a
    series of DNS labels separated by dots(.), not longer than 253 characters
    in total, followed by a slash (/). See
    https://kubernetes.io/docs/concepts/overview/working-with-
    objects/annotations/#syntax-and-character-set for more details.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels are attributes that can be set and used by both the
    user and by Cloud Deploy. Labels must meet the following constraints: *
    Keys and values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. Both keys and values are additionally constrained to be <=
    63 characters.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  etag = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  rules = _messages.MessageField('AutomationRule', 7, repeated=True)
  selector = _messages.MessageField('AutomationResourceSelector', 8)
  serviceAccount = _messages.StringField(9)
  suspended = _messages.BooleanField(10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class AutomationResourceSelector(_messages.Message):
  r"""AutomationResourceSelector contains the information to select the
  resources to which an Automation is going to be applied.

  Fields:
    targets: Contains attributes about a target.
  """

  targets = _messages.MessageField('TargetAttribute', 1, repeated=True)


class AutomationRule(_messages.Message):
  r"""`AutomationRule` defines the automation activities.

  Fields:
    advanceRolloutRule: Optional. The `AdvanceRolloutRule` will automatically
      advance a successful Rollout.
    promoteReleaseRule: Optional. `PromoteReleaseRule` will automatically
      promote a release from the current target to a specified target.
  """

  advanceRolloutRule = _messages.MessageField('AdvanceRolloutRule', 1)
  promoteReleaseRule = _messages.MessageField('PromoteReleaseRule', 2)


class AutomationRuleCondition(_messages.Message):
  r"""`AutomationRuleCondition` contains conditions relevant to an
  `Automation` rule.

  Fields:
    targetsPresentCondition: Optional. Details around targets enumerated in
      the rule.
  """

  targetsPresentCondition = _messages.MessageField('TargetsPresentCondition', 1)


class AutomationRun(_messages.Message):
  r"""An `AutomationRun` resource in the Cloud Deploy API. An
  `AutomationResource` represents an automation execution instance of an
  automation rule.

  Enums:
    StateValueValuesEnum: Output only. Current state of the `AutomationRun`.

  Fields:
    advanceRolloutOperation: Output only. Advances a rollout to the next
      phase.
    automationId: Output only. The ID of the automation that initiated the
      operation.
    automationSnapshot: Output only. Snapshot of the Automation taken at
      AutomationRun creation time.
    createTime: Output only. Time at which the `AutomationRun` was created.
    etag: Output only. The weak etag of the `AutomationRun` resource. This
      checksum is computed by the server based on the value of other fields,
      and may be sent on update and delete requests to ensure the client has
      an up-to-date value before proceeding.
    expireTime: Output only. Time the `AutomationRun` will expire. An
      `AutomationRun` will expire after 14 days from its creation date.
    name: Output only. Name of the `AutomationRun`. Format is projects/{projec
      t}/locations/{location}/deliveryPipelines/{delivery_pipeline}/automation
      Runs/{automation_run}.
    policyViolation: Output only. Contains information about what policies
      prevented the `AutomationRun` to proceed.
    promoteReleaseOperation: Output only. Promotes a release to a specified
      target.
    ruleId: Output only. The ID of the automation rule that initiated the
      operation.
    serviceAccount: Output only. Email address of the user-managed IAM service
      account that performs the operations against Cloud Deploy resources.
    state: Output only. Current state of the `AutomationRun`.
    stateDescription: Output only. Explains the current state of the
      `AutomationRun`. Present only an explanation is needed.
    targetId: Output only. The ID of the target that represents the promotion
      stage that initiates the `AutomationRun`. The value of this field is the
      last segment of a target name.
    updateTime: Output only. Time at which the automationRun was updated.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the `AutomationRun`.

    Values:
      STATE_UNSPECIFIED: The `AutomationRun` has an unspecified state.
      SUCCEEDED: The `AutomationRun` has succeeded.
      CANCELLED: The `AutomationRun` was cancelled.
      FAILED: The `AutomationRun` has failed.
      IN_PROGRESS: The `AutomationRun` is in progress.
      PENDING: The `AutomationRun` is pending.
    """
    STATE_UNSPECIFIED = 0
    SUCCEEDED = 1
    CANCELLED = 2
    FAILED = 3
    IN_PROGRESS = 4
    PENDING = 5

  advanceRolloutOperation = _messages.MessageField('AdvanceRolloutOperation', 1)
  automationId = _messages.StringField(2)
  automationSnapshot = _messages.MessageField('Automation', 3)
  createTime = _messages.StringField(4)
  etag = _messages.StringField(5)
  expireTime = _messages.StringField(6)
  name = _messages.StringField(7)
  policyViolation = _messages.MessageField('PolicyViolation', 8)
  promoteReleaseOperation = _messages.MessageField('PromoteReleaseOperation', 9)
  ruleId = _messages.StringField(10)
  serviceAccount = _messages.StringField(11)
  state = _messages.EnumField('StateValueValuesEnum', 12)
  stateDescription = _messages.StringField(13)
  targetId = _messages.StringField(14)
  updateTime = _messages.StringField(15)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class BuildArtifact(_messages.Message):
  r"""Description of an a image to use during Skaffold rendering.

  Fields:
    image: Image name in Skaffold configuration.
    tag: Image tag to use. This will generally be the full path to an image,
      such as "gcr.io/my-project/busybox:1.2.3" or "gcr.io/my-
      project/busybox@sha256:abc123".
  """

  image = _messages.StringField(1)
  tag = _messages.StringField(2)


class Canary(_messages.Message):
  r"""Canary represents the canary deployment strategy.

  Fields:
    canaryDeployment: Configures the progressive based deployment for a
      Target.
    customCanaryDeployment: Configures the progressive based deployment for a
      Target, but allows customizing at the phase level where a phase
      represents each of the percentage deployments.
    runtimeConfig: Optional. Runtime specific configurations for the
      deployment strategy. The runtime configuration is used to determine how
      Cloud Deploy will split traffic to enable a progressive deployment.
  """

  canaryDeployment = _messages.MessageField('CanaryDeployment', 1)
  customCanaryDeployment = _messages.MessageField('CustomCanaryDeployment', 2)
  runtimeConfig = _messages.MessageField('RuntimeConfig', 3)


class CanaryDeployment(_messages.Message):
  r"""CanaryDeployment represents the canary deployment configuration

  Fields:
    percentages: Required. The percentage based deployments that will occur as
      a part of a `Rollout`. List is expected in ascending order and each
      integer n is 0 <= n < 100.
    postdeploy: Optional. Configuration for the postdeploy job of the last
      phase. If this is not configured, there will be no postdeploy job for
      this phase.
    predeploy: Optional. Configuration for the predeploy job of the first
      phase. If this is not configured, there will be no predeploy job for
      this phase.
    verify: Whether to run verify tests after each percentage deployment.
  """

  percentages = _messages.IntegerField(1, repeated=True, variant=_messages.Variant.INT32)
  postdeploy = _messages.MessageField('Postdeploy', 2)
  predeploy = _messages.MessageField('Predeploy', 3)
  verify = _messages.BooleanField(4)


class CancelAutomationRunRequest(_messages.Message):
  r"""The request object used by `CancelAutomationRun`.

  Fields:
    overrideDeployPolicy: Deploy policies to override. Format is
      projects/{project}/ locations/{location}/deployPolicies/{deploy_policy}.
  """

  overrideDeployPolicy = _messages.StringField(1, repeated=True)


class CancelAutomationRunResponse(_messages.Message):
  r"""The response object from `CancelAutomationRun`."""


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CancelRolloutRequest(_messages.Message):
  r"""The request object used by `CancelRollout`.

  Fields:
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      projects/{project}/ locations/{location}/deployPolicies/a-z{0,62}.
  """

  overrideDeployPolicy = _messages.StringField(1, repeated=True)


class CancelRolloutResponse(_messages.Message):
  r"""The response object from `CancelRollout`."""


class ChildRolloutJobs(_messages.Message):
  r"""ChildRollouts job composition

  Fields:
    advanceRolloutJobs: Output only. List of AdvanceChildRolloutJobs
    createRolloutJobs: Output only. List of CreateChildRolloutJobs
  """

  advanceRolloutJobs = _messages.MessageField('Job', 1, repeated=True)
  createRolloutJobs = _messages.MessageField('Job', 2, repeated=True)


class CloudRunConfig(_messages.Message):
  r"""CloudRunConfig contains the Cloud Run runtime configuration.

  Fields:
    automaticTrafficControl: Whether Cloud Deploy should update the traffic
      stanza in a Cloud Run Service on the user's behalf to facilitate traffic
      splitting. This is required to be true for CanaryDeployments, but
      optional for CustomCanaryDeployments.
  """

  automaticTrafficControl = _messages.BooleanField(1)


class CloudRunLocation(_messages.Message):
  r"""Information specifying where to deploy a Cloud Run Service.

  Fields:
    location: Required. The location for the Cloud Run Service. Format must be
      `projects/{project}/locations/{location}`.
  """

  location = _messages.StringField(1)


class CloudRunMetadata(_messages.Message):
  r"""CloudRunMetadata contains information from a Cloud Run deployment.

  Fields:
    revision: Output only. The Cloud Run Revision id associated with a
      `Rollout`.
    service: Output only. The name of the Cloud Run Service that is associated
      with a `Rollout`. Format is
      projects/{project}/locations/{location}/services/{service}.
    serviceUrls: Output only. The Cloud Run Service urls that are associated
      with a `Rollout`.
  """

  revision = _messages.StringField(1)
  service = _messages.StringField(2)
  serviceUrls = _messages.StringField(3, repeated=True)


class CloudRunRenderMetadata(_messages.Message):
  r"""CloudRunRenderMetadata contains Cloud Run information associated with a
  `Release` render.

  Fields:
    service: Output only. The name of the Cloud Run Service in the rendered
      manifest. Format is
      projects/{project}/locations/{location}/services/{service}.
  """

  service = _messages.StringField(1)


class ClouddeployProjectsLocationsCustomTargetTypesCreateRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsCustomTargetTypesCreateRequest object.

  Fields:
    customTargetType: A CustomTargetType resource to be passed as the request
      body.
    customTargetTypeId: Required. ID of the `CustomTargetType`.
    parent: Required. The parent collection in which the `CustomTargetType`
      should be created in. Format should be
      projects/{project_id}/locations/{location_name}.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  customTargetType = _messages.MessageField('CustomTargetType', 1)
  customTargetTypeId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsCustomTargetTypesDeleteRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsCustomTargetTypesDeleteRequest object.

  Fields:
    allowMissing: Optional. If set to true, then deleting an already deleted
      or non-existing `CustomTargetType` will succeed.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    name: Required. The name of the `CustomTargetType` to delete. Format must
      be projects/{project_id}/locations/{location_name}/customTargetTypes/{cu
      stom_target_type}.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated but no
      actual change is made.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsCustomTargetTypesGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsCustomTargetTypesGetRequest object.

  Fields:
    name: Required. Name of the `CustomTargetType`. Format must be projects/{p
      roject_id}/locations/{location_name}/customTargetTypes/{custom_target_ty
      pe}.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsCustomTargetTypesListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsCustomTargetTypesListRequest object.

  Fields:
    filter: Optional. Filter custom target types to be returned. See
      https://google.aip.dev/160 for more details.
    orderBy: Optional. Field to sort by. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. The maximum number of `CustomTargetType` objects to
      return. The service may return fewer than this value. If unspecified, at
      most 50 `CustomTargetType` objects will be returned. The maximum value
      is 1000; values above 1000 will be set to 1000.
    pageToken: Optional. A page token, received from a previous
      `ListCustomTargetTypes` call. Provide this to retrieve the subsequent
      page. When paginating, all other provided parameters match the call that
      provided the page token.
    parent: Required. The parent that owns this collection of custom target
      types. Format must be projects/{project_id}/locations/{location_name}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsCustomTargetTypesPatchRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsCustomTargetTypesPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, updating a `CustomTargetType` that
      does not exist will result in the creation of a new `CustomTargetType`.
    customTargetType: A CustomTargetType resource to be passed as the request
      body.
    name: Optional. Name of the `CustomTargetType`. Format is
      projects/{project}/locations/{location}/customTargetTypes/a-z{0,62}.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the `CustomTargetType` resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  allowMissing = _messages.BooleanField(1)
  customTargetType = _messages.MessageField('CustomTargetType', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsCancelRequest(_messages.Message):
  r"""A
  ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsCancelRequest
  object.

  Fields:
    cancelAutomationRunRequest: A CancelAutomationRunRequest resource to be
      passed as the request body.
    name: Required. Name of the `AutomationRun`. Format is projects/{project}/
      locations/{location}/deliveryPipelines/{delivery_pipeline}/automationRun
      s/{automation_run}.
  """

  cancelAutomationRunRequest = _messages.MessageField('CancelAutomationRunRequest', 1)
  name = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsGetRequest
  object.

  Fields:
    name: Required. Name of the `AutomationRun`. Format must be projects/{proj
      ect}/locations/{location}/deliveryPipelines/{delivery_pipeline}/automati
      onRuns/{automation_run}.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesAutomationRunsListRequest
  object.

  Fields:
    filter: Filter automationRuns to be returned. All fields can be used in
      the filter.
    orderBy: Field to sort by.
    pageSize: The maximum number of automationRuns to return. The service may
      return fewer than this value. If unspecified, at most 50 automationRuns
      will be returned. The maximum value is 1000; values above 1000 will be
      set to 1000.
    pageToken: A page token, received from a previous `ListAutomationRuns`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other provided parameters match the call that provided the page token.
    parent: Required. The parent, which owns this collection of
      automationRuns. Format must be projects/{project}/locations/{location}/d
      eliveryPipelines/{delivery_pipeline}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesAutomationsCreateRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesAutomationsCreateRequest
  object.

  Fields:
    automation: A Automation resource to be passed as the request body.
    automationId: Required. ID of the `Automation`.
    parent: Required. The parent collection in which the `Automation` should
      be created. Format should be projects/{project_id}/locations/{location_n
      ame}/deliveryPipelines/{pipeline_name}.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  automation = _messages.MessageField('Automation', 1)
  automationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsDeliveryPipelinesAutomationsDeleteRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesAutomationsDeleteRequest
  object.

  Fields:
    allowMissing: Optional. If set to true, then deleting an already deleted
      or non-existing `Automation` will succeed.
    etag: Optional. The weak etag of the request. This checksum is computed by
      the server based on the value of other fields, and may be sent on update
      and delete requests to ensure the client has an up-to-date value before
      proceeding.
    name: Required. The name of the `Automation` to delete. Format should be p
      rojects/{project_id}/locations/{location_name}/deliveryPipelines/{pipeli
      ne_name}/automations/{automation_name}.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validate the request and verify whether
      the resource exists, but do not actually post it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsDeliveryPipelinesAutomationsGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesAutomationsGetRequest
  object.

  Fields:
    name: Required. Name of the `Automation`. Format must be projects/{project
      _id}/locations/{location_name}/deliveryPipelines/{pipeline_name}/automat
      ions/{automation_name}.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesAutomationsListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesAutomationsListRequest
  object.

  Fields:
    filter: Filter automations to be returned. All fields can be used in the
      filter.
    orderBy: Field to sort by.
    pageSize: The maximum number of automations to return. The service may
      return fewer than this value. If unspecified, at most 50 automations
      will be returned. The maximum value is 1000; values above 1000 will be
      set to 1000.
    pageToken: A page token, received from a previous `ListAutomations` call.
      Provide this to retrieve the subsequent page. When paginating, all other
      provided parameters match the call that provided the page token.
    parent: Required. The parent, which owns this collection of automations.
      Format must be projects/{project_id}/locations/{location_name}/deliveryP
      ipelines/{pipeline_name}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesAutomationsPatchRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesAutomationsPatchRequest
  object.

  Fields:
    allowMissing: Optional. If set to true, updating a `Automation` that does
      not exist will result in the creation of a new `Automation`.
    automation: A Automation resource to be passed as the request body.
    name: Output only. Name of the `Automation`. Format is projects/{project}/
      locations/{location}/deliveryPipelines/{delivery_pipeline}/automations/{
      automation}.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the `Automation` resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  allowMissing = _messages.BooleanField(1)
  automation = _messages.MessageField('Automation', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class ClouddeployProjectsLocationsDeliveryPipelinesCreateRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesCreateRequest object.

  Fields:
    deliveryPipeline: A DeliveryPipeline resource to be passed as the request
      body.
    deliveryPipelineId: Required. ID of the `DeliveryPipeline`.
    parent: Required. The parent collection in which the `DeliveryPipeline`
      should be created. Format should be
      projects/{project_id}/locations/{location_name}.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  deliveryPipeline = _messages.MessageField('DeliveryPipeline', 1)
  deliveryPipelineId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsDeliveryPipelinesDeleteRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesDeleteRequest object.

  Fields:
    allowMissing: Optional. If set to true, then deleting an already deleted
      or non-existing `DeliveryPipeline` will succeed.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    force: Optional. If set to true, all child resources under this pipeline
      will also be deleted. Otherwise, the request will only work if the
      pipeline has no child resources.
    name: Required. The name of the `DeliveryPipeline` to delete. Format
      should be projects/{project_id}/locations/{location_name}/deliveryPipeli
      nes/{pipeline_name}.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not actually post it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  force = _messages.BooleanField(3)
  name = _messages.StringField(4, required=True)
  requestId = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class ClouddeployProjectsLocationsDeliveryPipelinesGetIamPolicyRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesGetRequest object.

  Fields:
    name: Required. Name of the `DeliveryPipeline`. Format must be projects/{p
      roject_id}/locations/{location_name}/deliveryPipelines/{pipeline_name}.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesListRequest object.

  Fields:
    filter: Filter pipelines to be returned. See https://google.aip.dev/160
      for more details.
    orderBy: Field to sort by. See https://google.aip.dev/132#ordering for
      more details.
    pageSize: The maximum number of pipelines to return. The service may
      return fewer than this value. If unspecified, at most 50 pipelines will
      be returned. The maximum value is 1000; values above 1000 will be set to
      1000.
    pageToken: A page token, received from a previous `ListDeliveryPipelines`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other provided parameters match the call that provided the page token.
    parent: Required. The parent, which owns this collection of pipelines.
      Format must be projects/{project_id}/locations/{location_name}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesPatchRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, updating a `DeliveryPipeline` that
      does not exist will result in the creation of a new `DeliveryPipeline`.
    deliveryPipeline: A DeliveryPipeline resource to be passed as the request
      body.
    name: Optional. Name of the `DeliveryPipeline`. Format is
      projects/{project}/ locations/{location}/deliveryPipelines/a-z{0,62}.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the `DeliveryPipeline` resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  allowMissing = _messages.BooleanField(1)
  deliveryPipeline = _messages.MessageField('DeliveryPipeline', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesAbandonRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesReleasesAbandonRequest
  object.

  Fields:
    abandonReleaseRequest: A AbandonReleaseRequest resource to be passed as
      the request body.
    name: Required. Name of the Release. Format is projects/{project}/location
      s/{location}/deliveryPipelines/{deliveryPipeline}/ releases/{release}.
  """

  abandonReleaseRequest = _messages.MessageField('AbandonReleaseRequest', 1)
  name = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesCreateRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesReleasesCreateRequest
  object.

  Fields:
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      projects/{project}/ locations/{location}/deployPolicies/a-z{0,62}.
    parent: Required. The parent collection in which the `Release` should be
      created. Format should be projects/{project_id}/locations/{location_name
      }/deliveryPipelines/{pipeline_name}.
    release: A Release resource to be passed as the request body.
    releaseId: Required. ID of the `Release`.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  overrideDeployPolicy = _messages.StringField(1, repeated=True)
  parent = _messages.StringField(2, required=True)
  release = _messages.MessageField('Release', 3)
  releaseId = _messages.StringField(4)
  requestId = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesReleasesGetRequest
  object.

  Fields:
    name: Required. Name of the `Release`. Format must be projects/{project_id
      }/locations/{location_name}/deliveryPipelines/{pipeline_name}/releases/{
      release_name}.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesReleasesListRequest
  object.

  Fields:
    filter: Optional. Filter releases to be returned. See
      https://google.aip.dev/160 for more details.
    orderBy: Optional. Field to sort by. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. The maximum number of `Release` objects to return. The
      service may return fewer than this value. If unspecified, at most 50
      `Release` objects will be returned. The maximum value is 1000; values
      above 1000 will be set to 1000.
    pageToken: Optional. A page token, received from a previous `ListReleases`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other provided parameters match the call that provided the page token.
    parent: Required. The `DeliveryPipeline` which owns this collection of
      `Release` objects.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsAdvanceRequest(_messages.Message):
  r"""A
  ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsAdvanceRequest
  object.

  Fields:
    advanceRolloutRequest: A AdvanceRolloutRequest resource to be passed as
      the request body.
    name: Required. Name of the Rollout. Format is projects/{project}/location
      s/{location}/deliveryPipelines/{deliveryPipeline}/
      releases/{release}/rollouts/{rollout}.
  """

  advanceRolloutRequest = _messages.MessageField('AdvanceRolloutRequest', 1)
  name = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsApproveRequest(_messages.Message):
  r"""A
  ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsApproveRequest
  object.

  Fields:
    approveRolloutRequest: A ApproveRolloutRequest resource to be passed as
      the request body.
    name: Required. Name of the Rollout. Format is projects/{project}/location
      s/{location}/deliveryPipelines/{deliveryPipeline}/
      releases/{release}/rollouts/{rollout}.
  """

  approveRolloutRequest = _messages.MessageField('ApproveRolloutRequest', 1)
  name = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsCancelRequest(_messages.Message):
  r"""A
  ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsCancelRequest
  object.

  Fields:
    cancelRolloutRequest: A CancelRolloutRequest resource to be passed as the
      request body.
    name: Required. Name of the Rollout. Format is projects/{project}/location
      s/{location}/deliveryPipelines/{deliveryPipeline}/
      releases/{release}/rollouts/{rollout}.
  """

  cancelRolloutRequest = _messages.MessageField('CancelRolloutRequest', 1)
  name = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsCreateRequest(_messages.Message):
  r"""A
  ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsCreateRequest
  object.

  Fields:
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      projects/{project}/ locations/{location}/deployPolicies/a-z{0,62}.
    parent: Required. The parent collection in which the `Rollout` should be
      created. Format should be projects/{project_id}/locations/{location_name
      }/deliveryPipelines/{pipeline_name}/releases/{release_name}.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    rollout: A Rollout resource to be passed as the request body.
    rolloutId: Required. ID of the `Rollout`.
    startingPhaseId: Optional. The starting phase ID for the `Rollout`. If
      empty the `Rollout` will start at the first phase.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  overrideDeployPolicy = _messages.StringField(1, repeated=True)
  parent = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  rollout = _messages.MessageField('Rollout', 4)
  rolloutId = _messages.StringField(5)
  startingPhaseId = _messages.StringField(6)
  validateOnly = _messages.BooleanField(7)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsGetRequest(_messages.Message):
  r"""A
  ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsGetRequest
  object.

  Fields:
    name: Required. Name of the `Rollout`. Format must be projects/{project_id
      }/locations/{location_name}/deliveryPipelines/{pipeline_name}/releases/{
      release_name}/rollouts/{rollout_name}.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsIgnoreJobRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsIgnoreJob
  Request object.

  Fields:
    ignoreJobRequest: A IgnoreJobRequest resource to be passed as the request
      body.
    rollout: Required. Name of the Rollout. Format is projects/{project}/locat
      ions/{location}/deliveryPipelines/{deliveryPipeline}/
      releases/{release}/rollouts/{rollout}.
  """

  ignoreJobRequest = _messages.MessageField('IgnoreJobRequest', 1)
  rollout = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsGe
  tRequest object.

  Fields:
    name: Required. Name of the `JobRun`. Format must be projects/{project_id}
      /locations/{location_name}/deliveryPipelines/{pipeline_name}/releases/{r
      elease_name}/rollouts/{rollout_name}/jobRuns/{job_run_name}.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsLi
  stRequest object.

  Fields:
    filter: Optional. Filter results to be returned. See
      https://google.aip.dev/160 for more details.
    orderBy: Optional. Field to sort by. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. The maximum number of `JobRun` objects to return. The
      service may return fewer than this value. If unspecified, at most 50
      `JobRun` objects will be returned. The maximum value is 1000; values
      above 1000 will be set to 1000.
    pageToken: Optional. A page token, received from a previous `ListJobRuns`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other provided parameters match the call that provided the page token.
    parent: Required. The `Rollout` which owns this collection of `JobRun`
      objects.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsTerminateRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsJobRunsTe
  rminateRequest object.

  Fields:
    name: Required. Name of the `JobRun`. Format must be projects/{project}/lo
      cations/{location}/deliveryPipelines/{deliveryPipeline}/
      releases/{release}/rollouts/{rollout}/jobRuns/{jobRun}.
    terminateJobRunRequest: A TerminateJobRunRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  terminateJobRunRequest = _messages.MessageField('TerminateJobRunRequest', 2)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsListRequest(_messages.Message):
  r"""A
  ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsListRequest
  object.

  Fields:
    filter: Optional. Filter rollouts to be returned. See
      https://google.aip.dev/160 for more details.
    orderBy: Optional. Field to sort by. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. The maximum number of `Rollout` objects to return. The
      service may return fewer than this value. If unspecified, at most 50
      `Rollout` objects will be returned. The maximum value is 1000; values
      above 1000 will be set to 1000.
    pageToken: Optional. A page token, received from a previous `ListRollouts`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other provided parameters match the call that provided the page token.
    parent: Required. The `Release` which owns this collection of `Rollout`
      objects.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsRetryJobRequest(_messages.Message):
  r"""A
  ClouddeployProjectsLocationsDeliveryPipelinesReleasesRolloutsRetryJobRequest
  object.

  Fields:
    retryJobRequest: A RetryJobRequest resource to be passed as the request
      body.
    rollout: Required. Name of the Rollout. Format is projects/{project}/locat
      ions/{location}/deliveryPipelines/{deliveryPipeline}/
      releases/{release}/rollouts/{rollout}.
  """

  retryJobRequest = _messages.MessageField('RetryJobRequest', 1)
  rollout = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsDeliveryPipelinesRollbackTargetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesRollbackTargetRequest
  object.

  Fields:
    name: Required. The `DeliveryPipeline` in which the rollback `Rollout`
      should be created. Format should be projects/{project_id}/locations/{loc
      ation_name}/deliveryPipelines/{pipeline_name}.
    rollbackTargetRequest: A RollbackTargetRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  rollbackTargetRequest = _messages.MessageField('RollbackTargetRequest', 2)


class ClouddeployProjectsLocationsDeliveryPipelinesSetIamPolicyRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ClouddeployProjectsLocationsDeliveryPipelinesTestIamPermissionsRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeliveryPipelinesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class ClouddeployProjectsLocationsDeployPoliciesCreateRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeployPoliciesCreateRequest object.

  Fields:
    deployPolicy: A DeployPolicy resource to be passed as the request body.
    deployPolicyId: Required. ID of the `DeployPolicy`.
    parent: Required. The parent collection in which the `DeployPolicy` should
      be created. Format should be
      projects/{project_id}/locations/{location_name}.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  deployPolicy = _messages.MessageField('DeployPolicy', 1)
  deployPolicyId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsDeployPoliciesDeleteRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeployPoliciesDeleteRequest object.

  Fields:
    allowMissing: Optional. If set to true, then deleting an already deleted
      or non-existing `DeployPolicy` will succeed.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    name: Required. The name of the `DeployPolicy` to delete. Format should be
      projects/{project_id}/locations/{location_name}/deployPolicies/{deploy_p
      olicy_name}.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not actually post it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsDeployPoliciesGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeployPoliciesGetRequest object.

  Fields:
    name: Required. Name of the `DeployPolicy`. Format must be projects/{proje
      ct_id}/locations/{location_name}/deployPolicies/{deploy_policy_name}.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsDeployPoliciesListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeployPoliciesListRequest object.

  Fields:
    filter: Filter deploy policies to be returned. See
      https://google.aip.dev/160 for more details. All fields can be used in
      the filter.
    orderBy: Field to sort by. See https://google.aip.dev/132#ordering for
      more details.
    pageSize: The maximum number of deploy policies to return. The service may
      return fewer than this value. If unspecified, at most 50 deploy policies
      will be returned. The maximum value is 1000; values above 1000 will be
      set to 1000.
    pageToken: A page token, received from a previous `ListDeployPolicies`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other provided parameters match the call that provided the page token.
    parent: Required. The parent, which owns this collection of deploy
      policies. Format must be
      projects/{project_id}/locations/{location_name}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsDeployPoliciesPatchRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsDeployPoliciesPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, updating a `DeployPolicy` that
      does not exist will result in the creation of a new `DeployPolicy`.
    deployPolicy: A DeployPolicy resource to be passed as the request body.
    name: Output only. Name of the `DeployPolicy`. Format is
      projects/{project}/ locations/{location}/deployPolicies/a-z{0,62}.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the `DeployPolicy` resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  allowMissing = _messages.BooleanField(1)
  deployPolicy = _messages.MessageField('DeployPolicy', 2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class ClouddeployProjectsLocationsGetConfigRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsGetConfigRequest object.

  Fields:
    name: Required. Name of requested configuration.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ClouddeployProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class ClouddeployProjectsLocationsTargetsCreateRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsTargetsCreateRequest object.

  Fields:
    parent: Required. The parent collection in which the `Target` should be
      created. Format should be
      projects/{project_id}/locations/{location_name}.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    target: A Target resource to be passed as the request body.
    targetId: Required. ID of the `Target`.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  parent = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)
  target = _messages.MessageField('Target', 3)
  targetId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsTargetsDeleteRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsTargetsDeleteRequest object.

  Fields:
    allowMissing: Optional. If set to true, then deleting an already deleted
      or non-existing `Target` will succeed.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    name: Required. The name of the `Target` to delete. Format should be
      projects/{project_id}/locations/{location_name}/targets/{target_name}.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes after the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: Optional. If set, validate the request and preview the
      review, but do not actually post it.
  """

  allowMissing = _messages.BooleanField(1)
  etag = _messages.StringField(2)
  name = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class ClouddeployProjectsLocationsTargetsGetIamPolicyRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsTargetsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class ClouddeployProjectsLocationsTargetsGetRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsTargetsGetRequest object.

  Fields:
    name: Required. Name of the `Target`. Format must be
      projects/{project_id}/locations/{location_name}/targets/{target_name}.
  """

  name = _messages.StringField(1, required=True)


class ClouddeployProjectsLocationsTargetsListRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsTargetsListRequest object.

  Fields:
    filter: Optional. Filter targets to be returned. See
      https://google.aip.dev/160 for more details.
    orderBy: Optional. Field to sort by. See
      https://google.aip.dev/132#ordering for more details.
    pageSize: Optional. The maximum number of `Target` objects to return. The
      service may return fewer than this value. If unspecified, at most 50
      `Target` objects will be returned. The maximum value is 1000; values
      above 1000 will be set to 1000.
    pageToken: Optional. A page token, received from a previous `ListTargets`
      call. Provide this to retrieve the subsequent page. When paginating, all
      other provided parameters match the call that provided the page token.
    parent: Required. The parent, which owns this collection of targets.
      Format must be projects/{project_id}/locations/{location_name}.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ClouddeployProjectsLocationsTargetsPatchRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsTargetsPatchRequest object.

  Fields:
    allowMissing: Optional. If set to true, updating a `Target` that does not
      exist will result in the creation of a new `Target`.
    name: Optional. Name of the `Target`. Format is
      projects/{project}/locations/{location}/targets/a-z{0,62}.
    requestId: Optional. A request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    target: A Target resource to be passed as the request body.
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Target resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with an expected result, but no actual change is made.
  """

  allowMissing = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  target = _messages.MessageField('Target', 4)
  updateMask = _messages.StringField(5)
  validateOnly = _messages.BooleanField(6)


class ClouddeployProjectsLocationsTargetsSetIamPolicyRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsTargetsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ClouddeployProjectsLocationsTargetsTestIamPermissionsRequest(_messages.Message):
  r"""A ClouddeployProjectsLocationsTargetsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class Config(_messages.Message):
  r"""Service-wide configuration.

  Fields:
    defaultSkaffoldVersion: Default Skaffold version that is assigned when a
      Release is created without specifying a Skaffold version.
    name: Name of the configuration.
    supportedVersions: All supported versions of Skaffold.
  """

  defaultSkaffoldVersion = _messages.StringField(1)
  name = _messages.StringField(2)
  supportedVersions = _messages.MessageField('SkaffoldVersion', 3, repeated=True)


class CreateChildRolloutJob(_messages.Message):
  r"""A createChildRollout Job."""


class CreateChildRolloutJobRun(_messages.Message):
  r"""CreateChildRolloutJobRun contains information specific to a
  createChildRollout `JobRun`.

  Fields:
    rollout: Output only. Name of the `ChildRollout`. Format is
      projects/{project}/
      locations/{location}/deliveryPipelines/{deliveryPipeline}/
      releases/{release}/rollouts/a-z{0,62}.
    rolloutPhaseId: Output only. The ID of the childRollout Phase initiated by
      this JobRun.
  """

  rollout = _messages.StringField(1)
  rolloutPhaseId = _messages.StringField(2)


class CustomCanaryDeployment(_messages.Message):
  r"""CustomCanaryDeployment represents the custom canary deployment
  configuration.

  Fields:
    phaseConfigs: Required. Configuration for each phase in the canary
      deployment in the order executed.
  """

  phaseConfigs = _messages.MessageField('PhaseConfig', 1, repeated=True)


class CustomTarget(_messages.Message):
  r"""Information specifying a Custom Target.

  Fields:
    customTargetType: Required. The name of the CustomTargetType. Format must
      be `projects/{project}/locations/{location}/customTargetTypes/{custom_ta
      rget_type}.
  """

  customTargetType = _messages.StringField(1)


class CustomTargetSkaffoldActions(_messages.Message):
  r"""CustomTargetSkaffoldActions represents the `CustomTargetType`
  configuration using Skaffold custom actions.

  Fields:
    deployAction: Required. The Skaffold custom action responsible for deploy
      operations.
    includeSkaffoldModules: Optional. List of Skaffold modules Cloud Deploy
      will include in the Skaffold Config as required before performing
      diagnose.
    renderAction: Optional. The Skaffold custom action responsible for render
      operations. If not provided then Cloud Deploy will perform the render
      operations via `skaffold render`.
  """

  deployAction = _messages.StringField(1)
  includeSkaffoldModules = _messages.MessageField('SkaffoldModules', 2, repeated=True)
  renderAction = _messages.StringField(3)


class CustomTargetType(_messages.Message):
  r"""A `CustomTargetType` resource in the Cloud Deploy API. A
  `CustomTargetType` defines a type of custom target that can be referenced in
  a `Target` in order to facilitate deploying to a runtime that does not have
  a 1P integration with Cloud Deploy.

  Messages:
    AnnotationsValue: Optional. User annotations. These attributes can only be
      set and used by the user, and not by Cloud Deploy. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    LabelsValue: Optional. Labels are attributes that can be set and used by
      both the user and by Cloud Deploy. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. Both keys and values are
      additionally constrained to be <= 128 bytes.

  Fields:
    annotations: Optional. User annotations. These attributes can only be set
      and used by the user, and not by Cloud Deploy. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    createTime: Output only. Time at which the `CustomTargetType` was created.
    customActions: Configures render and deploy for the `CustomTargetType`
      using Skaffold custom actions.
    customTargetTypeId: Output only. Resource id of the `CustomTargetType`.
    description: Optional. Description of the `CustomTargetType`. Max length
      is 255 characters.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    labels: Optional. Labels are attributes that can be set and used by both
      the user and by Cloud Deploy. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. Both keys and values are
      additionally constrained to be <= 128 bytes.
    name: Optional. Name of the `CustomTargetType`. Format is
      projects/{project}/locations/{location}/customTargetTypes/a-z{0,62}.
    uid: Output only. Unique identifier of the `CustomTargetType`.
    updateTime: Output only. Most recent time at which the `CustomTargetType`
      was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User annotations. These attributes can only be set and used
    by the user, and not by Cloud Deploy. See
    https://google.aip.dev/128#annotations for more details such as format and
    size limitations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels are attributes that can be set and used by both the
    user and by Cloud Deploy. Labels must meet the following constraints: *
    Keys and values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. Both keys and values are additionally constrained to be <=
    128 bytes.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  customActions = _messages.MessageField('CustomTargetSkaffoldActions', 3)
  customTargetTypeId = _messages.StringField(4)
  description = _messages.StringField(5)
  etag = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  uid = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class Date(_messages.Message):
  r"""Represents a whole or partial calendar date, such as a birthday. The
  time of day and time zone are either specified elsewhere or are
  insignificant. The date is relative to the Gregorian Calendar. This can
  represent one of the following: * A full date, with non-zero year, month,
  and day values. * A month and day, with a zero year (for example, an
  anniversary). * A year on its own, with a zero month and a zero day. * A
  year and month, with a zero day (for example, a credit card expiration
  date). Related types: * google.type.TimeOfDay * google.type.DateTime *
  google.protobuf.Timestamp

  Fields:
    day: Day of a month. Must be from 1 to 31 and valid for the year and
      month, or 0 to specify a year by itself or a year and month where the
      day isn't significant.
    month: Month of a year. Must be from 1 to 12, or 0 to specify a year
      without a month and day.
    year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
      without a year.
  """

  day = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  month = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  year = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class DefaultPool(_messages.Message):
  r"""Execution using the default Cloud Build pool.

  Fields:
    artifactStorage: Optional. Cloud Storage location where execution outputs
      should be stored. This can either be a bucket ("gs://my-bucket") or a
      path within a bucket ("gs://my-bucket/my-dir"). If unspecified, a
      default bucket located in the same region will be used.
    serviceAccount: Optional. Google service account to use for execution. If
      unspecified, the project execution service account
      (-<EMAIL>) will be used.
  """

  artifactStorage = _messages.StringField(1)
  serviceAccount = _messages.StringField(2)


class DeliveryPipeline(_messages.Message):
  r"""A `DeliveryPipeline` resource in the Cloud Deploy API. A
  `DeliveryPipeline` defines a pipeline through which a Skaffold configuration
  can progress.

  Messages:
    AnnotationsValue: User annotations. These attributes can only be set and
      used by the user, and not by Cloud Deploy.
    LabelsValue: Labels are attributes that can be set and used by both the
      user and by Cloud Deploy. Labels must meet the following constraints: *
      Keys and values can contain only lowercase letters, numeric characters,
      underscores, and dashes. * All characters must use UTF-8 encoding, and
      international characters are allowed. * Keys must start with a lowercase
      letter or international character. * Each resource is limited to a
      maximum of 64 labels. Both keys and values are additionally constrained
      to be <= 128 bytes.

  Fields:
    annotations: User annotations. These attributes can only be set and used
      by the user, and not by Cloud Deploy.
    condition: Output only. Information around the state of the Delivery
      Pipeline.
    createTime: Output only. Time at which the pipeline was created.
    description: Description of the `DeliveryPipeline`. Max length is 255
      characters.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding.
    labels: Labels are attributes that can be set and used by both the user
      and by Cloud Deploy. Labels must meet the following constraints: * Keys
      and values can contain only lowercase letters, numeric characters,
      underscores, and dashes. * All characters must use UTF-8 encoding, and
      international characters are allowed. * Keys must start with a lowercase
      letter or international character. * Each resource is limited to a
      maximum of 64 labels. Both keys and values are additionally constrained
      to be <= 128 bytes.
    name: Optional. Name of the `DeliveryPipeline`. Format is
      projects/{project}/ locations/{location}/deliveryPipelines/a-z{0,62}.
    serialPipeline: SerialPipeline defines a sequential set of stages for a
      `DeliveryPipeline`.
    suspended: When suspended, no new releases or rollouts can be created, but
      in-progress ones will complete.
    uid: Output only. Unique identifier of the `DeliveryPipeline`.
    updateTime: Output only. Most recent time at which the pipeline was
      updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""User annotations. These attributes can only be set and used by the
    user, and not by Cloud Deploy.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels are attributes that can be set and used by both the user and by
    Cloud Deploy. Labels must meet the following constraints: * Keys and
    values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. Both keys and values are additionally constrained to be <=
    128 bytes.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  condition = _messages.MessageField('PipelineCondition', 2)
  createTime = _messages.StringField(3)
  description = _messages.StringField(4)
  etag = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  name = _messages.StringField(7)
  serialPipeline = _messages.MessageField('SerialPipeline', 8)
  suspended = _messages.BooleanField(9)
  uid = _messages.StringField(10)
  updateTime = _messages.StringField(11)


class DeliveryPipelineAttribute(_messages.Message):
  r"""Contains criteria for selecting DeliveryPipelines. Attributes provided
  must match the delivery pipeline resource in order for policy restrictions
  to apply. E.g. if id "prod" and labels "foo: bar" are given the delivery
  pipeline resource must match both that id and have that label in order to be
  selected.

  Messages:
    LabelsValue: DeliveryPipeline labels.

  Fields:
    id: ID of the `DeliveryPipeline`. The value of this field could be one of
      the following: * The last segment of a pipeline name. It only needs the
      ID to determine which pipeline is being referred to * "*", all delivery
      pipelines in a location.
    labels: DeliveryPipeline labels.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""DeliveryPipeline labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  id = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)


class DeliveryPipelineNotificationEvent(_messages.Message):
  r"""Payload proto for
  "clouddeploy.googleapis.com/deliverypipeline_notification" Platform Log
  event that describes the failure to send delivery pipeline status change
  Pub/Sub notification.

  Enums:
    TypeValueValuesEnum: Type of this notification, e.g. for a Pub/Sub
      failure.

  Fields:
    deliveryPipeline: The name of the `Delivery Pipeline`.
    message: Debug message for when a notification fails to send.
    type: Type of this notification, e.g. for a Pub/Sub failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this notification, e.g. for a Pub/Sub failure.

    Values:
      TYPE_UNSPECIFIED: Type is unspecified.
      TYPE_PUBSUB_NOTIFICATION_FAILURE: A Pub/Sub notification failed to be
        sent.
      TYPE_RESOURCE_STATE_CHANGE: Resource state changed.
      TYPE_PROCESS_ABORTED: A process aborted.
      TYPE_RESTRICTION_VIOLATED: Restriction check failed.
      TYPE_RESOURCE_DELETED: Resource deleted.
      TYPE_RENDER_STATUES_CHANGE: Deprecated: This field is never used. Use
        release_render log type instead.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PUBSUB_NOTIFICATION_FAILURE = 1
    TYPE_RESOURCE_STATE_CHANGE = 2
    TYPE_PROCESS_ABORTED = 3
    TYPE_RESTRICTION_VIOLATED = 4
    TYPE_RESOURCE_DELETED = 5
    TYPE_RENDER_STATUES_CHANGE = 6

  deliveryPipeline = _messages.StringField(1)
  message = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class DeployArtifact(_messages.Message):
  r"""The artifacts produced by a deploy operation.

  Fields:
    artifactUri: Output only. URI of a directory containing the artifacts. All
      paths are relative to this location.
    manifestPaths: Output only. File paths of the manifests applied during the
      deploy operation relative to the URI.
  """

  artifactUri = _messages.StringField(1)
  manifestPaths = _messages.StringField(2, repeated=True)


class DeployJob(_messages.Message):
  r"""A deploy Job."""


class DeployJobRun(_messages.Message):
  r"""DeployJobRun contains information specific to a deploy `JobRun`.

  Enums:
    FailureCauseValueValuesEnum: Output only. The reason the deploy failed.
      This will always be unspecified while the deploy is in progress or if it
      succeeded.

  Fields:
    artifact: Output only. The artifact of a deploy job run, if available.
    build: Output only. The resource name of the Cloud Build `Build` object
      that is used to deploy. Format is
      projects/{project}/locations/{location}/builds/{build}.
    failureCause: Output only. The reason the deploy failed. This will always
      be unspecified while the deploy is in progress or if it succeeded.
    failureMessage: Output only. Additional information about the deploy
      failure, if available.
    metadata: Output only. Metadata containing information about the deploy
      job run.
  """

  class FailureCauseValueValuesEnum(_messages.Enum):
    r"""Output only. The reason the deploy failed. This will always be
    unspecified while the deploy is in progress or if it succeeded.

    Values:
      FAILURE_CAUSE_UNSPECIFIED: No reason for failure is specified.
      CLOUD_BUILD_UNAVAILABLE: Cloud Build is not available, either because it
        is not enabled or because Cloud Deploy has insufficient permissions.
        See [Required permission](https://cloud.google.com/deploy/docs/cloud-
        deploy-service-account#required_permissions).
      EXECUTION_FAILED: The deploy operation did not complete successfully;
        check Cloud Build logs.
      DEADLINE_EXCEEDED: The deploy job run did not complete within the
        alloted time.
      MISSING_RESOURCES_FOR_CANARY: There were missing resources in the
        runtime environment required for a canary deployment. Check the Cloud
        Build logs for more information.
      CLOUD_BUILD_REQUEST_FAILED: Cloud Build failed to fulfill Cloud Deploy's
        request. See failure_message for additional details.
    """
    FAILURE_CAUSE_UNSPECIFIED = 0
    CLOUD_BUILD_UNAVAILABLE = 1
    EXECUTION_FAILED = 2
    DEADLINE_EXCEEDED = 3
    MISSING_RESOURCES_FOR_CANARY = 4
    CLOUD_BUILD_REQUEST_FAILED = 5

  artifact = _messages.MessageField('DeployArtifact', 1)
  build = _messages.StringField(2)
  failureCause = _messages.EnumField('FailureCauseValueValuesEnum', 3)
  failureMessage = _messages.StringField(4)
  metadata = _messages.MessageField('DeployJobRunMetadata', 5)


class DeployJobRunMetadata(_messages.Message):
  r"""DeployJobRunMetadata surfaces information associated with a
  `DeployJobRun` to the user.

  Fields:
    cloudRun: Output only. The name of the Cloud Run Service that is
      associated with a `DeployJobRun`.
  """

  cloudRun = _messages.MessageField('CloudRunMetadata', 1)


class DeployParameters(_messages.Message):
  r"""DeployParameters contains deploy parameters information.

  Messages:
    MatchTargetLabelsValue: Optional. Deploy parameters are applied to targets
      with match labels. If unspecified, deploy parameters are applied to all
      targets (including child targets of a multi-target).
    ValuesValue: Required. Values are deploy parameters in key-value pairs.

  Fields:
    matchTargetLabels: Optional. Deploy parameters are applied to targets with
      match labels. If unspecified, deploy parameters are applied to all
      targets (including child targets of a multi-target).
    values: Required. Values are deploy parameters in key-value pairs.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MatchTargetLabelsValue(_messages.Message):
    r"""Optional. Deploy parameters are applied to targets with match labels.
    If unspecified, deploy parameters are applied to all targets (including
    child targets of a multi-target).

    Messages:
      AdditionalProperty: An additional property for a MatchTargetLabelsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        MatchTargetLabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MatchTargetLabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ValuesValue(_messages.Message):
    r"""Required. Values are deploy parameters in key-value pairs.

    Messages:
      AdditionalProperty: An additional property for a ValuesValue object.

    Fields:
      additionalProperties: Additional properties of type ValuesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ValuesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  matchTargetLabels = _messages.MessageField('MatchTargetLabelsValue', 1)
  values = _messages.MessageField('ValuesValue', 2)


class DeployPolicy(_messages.Message):
  r"""A `DeployPolicy` resource in the Cloud Deploy API. A `DeployPolicy`
  inhibits manual or automation driven actions within a Delivery Pipeline or
  Target.

  Messages:
    AnnotationsValue: User annotations. These attributes can only be set and
      used by the user, and not by Cloud Deploy. Annotations must meet the
      following constraints: * Annotations are key/value pairs. * Valid
      annotation keys have two segments: an optional prefix and name,
      separated by a slash (/). * The name segment is required and must be 63
      characters or less, beginning and ending with an alphanumeric character
      ([a-z0-9A-Z]) with dashes (-), underscores (_), dots (.), and
      alphanumerics between. * The prefix is optional. If specified, the
      prefix must be a DNS subdomain: a series of DNS labels separated by
      dots(.), not longer than 253 characters in total, followed by a slash
      (/). See https://kubernetes.io/docs/concepts/overview/working-with-
      objects/annotations/#syntax-and-character-set for more details.
    LabelsValue: Labels are attributes that can be set and used by both the
      user and by Cloud Deploy. Labels must meet the following constraints: *
      Keys and values can contain only lowercase letters, numeric characters,
      underscores, and dashes. * All characters must use UTF-8 encoding, and
      international characters are allowed. * Keys must start with a lowercase
      letter or international character. * Each resource is limited to a
      maximum of 64 labels. Both keys and values are additionally constrained
      to be <= 128 bytes.

  Fields:
    annotations: User annotations. These attributes can only be set and used
      by the user, and not by Cloud Deploy. Annotations must meet the
      following constraints: * Annotations are key/value pairs. * Valid
      annotation keys have two segments: an optional prefix and name,
      separated by a slash (/). * The name segment is required and must be 63
      characters or less, beginning and ending with an alphanumeric character
      ([a-z0-9A-Z]) with dashes (-), underscores (_), dots (.), and
      alphanumerics between. * The prefix is optional. If specified, the
      prefix must be a DNS subdomain: a series of DNS labels separated by
      dots(.), not longer than 253 characters in total, followed by a slash
      (/). See https://kubernetes.io/docs/concepts/overview/working-with-
      objects/annotations/#syntax-and-character-set for more details.
    createTime: Output only. Time at which the deploy policy was created.
    description: Description of the `DeployPolicy`. Max length is 255
      characters.
    etag: The weak etag of the `Automation` resource. This checksum is
      computed by the server based on the value of other fields, and may be
      sent on update and delete requests to ensure the client has an up-to-
      date value before proceeding.
    labels: Labels are attributes that can be set and used by both the user
      and by Cloud Deploy. Labels must meet the following constraints: * Keys
      and values can contain only lowercase letters, numeric characters,
      underscores, and dashes. * All characters must use UTF-8 encoding, and
      international characters are allowed. * Keys must start with a lowercase
      letter or international character. * Each resource is limited to a
      maximum of 64 labels. Both keys and values are additionally constrained
      to be <= 128 bytes.
    name: Output only. Name of the `DeployPolicy`. Format is
      projects/{project}/ locations/{location}/deployPolicies/a-z{0,62}.
    rules: Rules to apply.
    selector: Selected resources to which the policy will be applied.
    selects: Resources to apply the policy to.
    suspended: When suspended, the policy will not prevent actions from
      occurring, even if the action violates the policy.
    uid: Output only. Unique identifier of the `DeployPolicy`.
    updateTime: Output only. Most recent time at which the deploy policy was
      updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""User annotations. These attributes can only be set and used by the
    user, and not by Cloud Deploy. Annotations must meet the following
    constraints: * Annotations are key/value pairs. * Valid annotation keys
    have two segments: an optional prefix and name, separated by a slash (/).
    * The name segment is required and must be 63 characters or less,
    beginning and ending with an alphanumeric character ([a-z0-9A-Z]) with
    dashes (-), underscores (_), dots (.), and alphanumerics between. * The
    prefix is optional. If specified, the prefix must be a DNS subdomain: a
    series of DNS labels separated by dots(.), not longer than 253 characters
    in total, followed by a slash (/). See
    https://kubernetes.io/docs/concepts/overview/working-with-
    objects/annotations/#syntax-and-character-set for more details.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels are attributes that can be set and used by both the user and by
    Cloud Deploy. Labels must meet the following constraints: * Keys and
    values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. Both keys and values are additionally constrained to be <=
    128 bytes.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  etag = _messages.StringField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  rules = _messages.MessageField('PolicyRule', 7, repeated=True)
  selector = _messages.MessageField('DeployPolicyResourceSelector', 8)
  selects = _messages.MessageField('Resource', 9)
  suspended = _messages.BooleanField(10)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class DeployPolicyResourceSelector(_messages.Message):
  r"""Contains information on the resources to select for a deploy policy.

  Fields:
    deliveryPipelines: Contains attributes about a delivery pipeline.
    targets: Contains attributes about a target.
  """

  deliveryPipelines = _messages.MessageField('DeliveryPipelineAttribute', 1, repeated=True)
  targets = _messages.MessageField('TargetAttribute', 2, repeated=True)


class DeploymentJobs(_messages.Message):
  r"""Deployment job composition.

  Fields:
    deployJob: Output only. The deploy Job. This is the deploy job in the
      phase.
    postdeployJob: Output only. The postdeploy Job, which is the last job on
      the phase.
    predeployJob: Output only. The predeploy Job, which is the first job on
      the phase.
    verifyJob: Output only. The verify Job. Runs after a deploy if the deploy
      succeeds.
  """

  deployJob = _messages.MessageField('Job', 1)
  postdeployJob = _messages.MessageField('Job', 2)
  predeployJob = _messages.MessageField('Job', 3)
  verifyJob = _messages.MessageField('Job', 4)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ExecutionConfig(_messages.Message):
  r"""Configuration of the environment to use when calling Skaffold.

  Enums:
    UsagesValueListEntryValuesEnum:

  Fields:
    artifactStorage: Optional. Cloud Storage location in which to store
      execution outputs. This can either be a bucket ("gs://my-bucket") or a
      path within a bucket ("gs://my-bucket/my-dir"). If unspecified, a
      default bucket located in the same region will be used.
    defaultPool: Optional. Use default Cloud Build pool.
    executionTimeout: Optional. Execution timeout for a Cloud Build Execution.
      This must be between 10m and 24h in seconds format. If unspecified, a
      default timeout of 1h is used.
    privatePool: Optional. Use private Cloud Build pool.
    serviceAccount: Optional. Google service account to use for execution. If
      unspecified, the project execution service account
      (-<EMAIL>) is used.
    usages: Required. Usages when this configuration should be applied.
    workerPool: Optional. The resource name of the `WorkerPool`, with the
      format
      `projects/{project}/locations/{location}/workerPools/{worker_pool}`. If
      this optional field is unspecified, the default Cloud Build pool will be
      used.
  """

  class UsagesValueListEntryValuesEnum(_messages.Enum):
    r"""UsagesValueListEntryValuesEnum enum type.

    Values:
      EXECUTION_ENVIRONMENT_USAGE_UNSPECIFIED: Default value. This value is
        unused.
      RENDER: Use for rendering.
      DEPLOY: Use for deploying and deployment hooks.
      VERIFY: Use for deployment verification.
      PREDEPLOY: Use for predeploy job execution.
      POSTDEPLOY: Use for postdeploy job execution.
    """
    EXECUTION_ENVIRONMENT_USAGE_UNSPECIFIED = 0
    RENDER = 1
    DEPLOY = 2
    VERIFY = 3
    PREDEPLOY = 4
    POSTDEPLOY = 5

  artifactStorage = _messages.StringField(1)
  defaultPool = _messages.MessageField('DefaultPool', 2)
  executionTimeout = _messages.StringField(3)
  privatePool = _messages.MessageField('PrivatePool', 4)
  serviceAccount = _messages.StringField(5)
  usages = _messages.EnumField('UsagesValueListEntryValuesEnum', 6, repeated=True)
  workerPool = _messages.StringField(7)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GatewayServiceMesh(_messages.Message):
  r"""Information about the Kubernetes Gateway API service mesh configuration.

  Fields:
    deployment: Required. Name of the Kubernetes Deployment whose traffic is
      managed by the specified HTTPRoute and Service.
    httpRoute: Required. Name of the Gateway API HTTPRoute.
    routeUpdateWaitTime: Optional. The time to wait for route updates to
      propagate. The maximum configurable time is 3 hours, in seconds format.
      If unspecified, there is no wait time.
    service: Required. Name of the Kubernetes Service.
  """

  deployment = _messages.StringField(1)
  httpRoute = _messages.StringField(2)
  routeUpdateWaitTime = _messages.StringField(3)
  service = _messages.StringField(4)


class GkeCluster(_messages.Message):
  r"""Information specifying a GKE Cluster.

  Fields:
    cluster: Information specifying a GKE Cluster. Format is
      `projects/{project_id}/locations/{location_id}/clusters/{cluster_id}.
    internalIp: Optional. If true, `cluster` is accessed using the private IP
      address of the control plane endpoint. Otherwise, the default IP address
      of the control plane endpoint is used. The default IP address is the
      private IP address for clusters with private control-plane endpoints and
      the public IP address otherwise. Only specify this option when `cluster`
      is a [private GKE cluster](https://cloud.google.com/kubernetes-
      engine/docs/concepts/private-cluster-concept).
  """

  cluster = _messages.StringField(1)
  internalIp = _messages.BooleanField(2)


class IgnoreJobRequest(_messages.Message):
  r"""The request object used by `IgnoreJob`.

  Fields:
    jobId: Required. The job ID for the Job to ignore.
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      projects/{project}/ locations/{location}/deployPolicies/a-z{0,62}.
    phaseId: Required. The phase ID the Job to ignore belongs to.
  """

  jobId = _messages.StringField(1)
  overrideDeployPolicy = _messages.StringField(2, repeated=True)
  phaseId = _messages.StringField(3)


class IgnoreJobResponse(_messages.Message):
  r"""The response object from `IgnoreJob`."""


class Job(_messages.Message):
  r"""Job represents an operation for a `Rollout`.

  Enums:
    StateValueValuesEnum: Output only. The current state of the Job.

  Fields:
    advanceChildRolloutJob: Output only. An advanceChildRollout Job.
    createChildRolloutJob: Output only. A createChildRollout Job.
    deployJob: Output only. A deploy Job.
    id: Output only. The ID of the Job.
    jobRun: Output only. The name of the `JobRun` responsible for the most
      recent invocation of this Job.
    postdeployJob: Output only. A postdeploy Job.
    predeployJob: Output only. A predeploy Job.
    skipMessage: Output only. Additional information on why the Job was
      skipped, if available.
    state: Output only. The current state of the Job.
    verifyJob: Output only. A verify Job.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the Job.

    Values:
      STATE_UNSPECIFIED: The Job has an unspecified state.
      PENDING: The Job is waiting for an earlier Phase(s) or Job(s) to
        complete.
      DISABLED: The Job is disabled.
      IN_PROGRESS: The Job is in progress.
      SUCCEEDED: The Job succeeded.
      FAILED: The Job failed.
      ABORTED: The Job was aborted.
      SKIPPED: The Job was skipped.
      IGNORED: The Job was ignored.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    DISABLED = 2
    IN_PROGRESS = 3
    SUCCEEDED = 4
    FAILED = 5
    ABORTED = 6
    SKIPPED = 7
    IGNORED = 8

  advanceChildRolloutJob = _messages.MessageField('AdvanceChildRolloutJob', 1)
  createChildRolloutJob = _messages.MessageField('CreateChildRolloutJob', 2)
  deployJob = _messages.MessageField('DeployJob', 3)
  id = _messages.StringField(4)
  jobRun = _messages.StringField(5)
  postdeployJob = _messages.MessageField('PostdeployJob', 6)
  predeployJob = _messages.MessageField('PredeployJob', 7)
  skipMessage = _messages.StringField(8)
  state = _messages.EnumField('StateValueValuesEnum', 9)
  verifyJob = _messages.MessageField('VerifyJob', 10)


class JobRun(_messages.Message):
  r"""A `JobRun` resource in the Cloud Deploy API. A `JobRun` contains
  information of a single `Rollout` job evaluation.

  Enums:
    StateValueValuesEnum: Output only. The current state of the `JobRun`.

  Fields:
    advanceChildRolloutJobRun: Output only. Information specific to an
      advanceChildRollout `JobRun`
    createChildRolloutJobRun: Output only. Information specific to a
      createChildRollout `JobRun`.
    createTime: Output only. Time at which the `JobRun` was created.
    deployJobRun: Output only. Information specific to a deploy `JobRun`.
    endTime: Output only. Time at which the `JobRun` ended.
    etag: Output only. This checksum is computed by the server based on the
      value of other fields, and may be sent on update and delete requests to
      ensure the client has an up-to-date value before proceeding.
    jobId: Output only. ID of the `Rollout` job this `JobRun` corresponds to.
    name: Optional. Name of the `JobRun`. Format is
      projects/{project}/locations/{location}/
      deliveryPipelines/{deliveryPipeline}/releases/{releases}/rollouts/
      {rollouts}/jobRuns/{uuid}.
    phaseId: Output only. ID of the `Rollout` phase this `JobRun` belongs in.
    postdeployJobRun: Output only. Information specific to a postdeploy
      `JobRun`.
    predeployJobRun: Output only. Information specific to a predeploy
      `JobRun`.
    startTime: Output only. Time at which the `JobRun` was started.
    state: Output only. The current state of the `JobRun`.
    uid: Output only. Unique identifier of the `JobRun`.
    verifyJobRun: Output only. Information specific to a verify `JobRun`.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. The current state of the `JobRun`.

    Values:
      STATE_UNSPECIFIED: The `JobRun` has an unspecified state.
      IN_PROGRESS: The `JobRun` is in progress.
      SUCCEEDED: The `JobRun` has succeeded.
      FAILED: The `JobRun` has failed.
      TERMINATING: The `JobRun` is terminating.
      TERMINATED: The `JobRun` was terminated.
    """
    STATE_UNSPECIFIED = 0
    IN_PROGRESS = 1
    SUCCEEDED = 2
    FAILED = 3
    TERMINATING = 4
    TERMINATED = 5

  advanceChildRolloutJobRun = _messages.MessageField('AdvanceChildRolloutJobRun', 1)
  createChildRolloutJobRun = _messages.MessageField('CreateChildRolloutJobRun', 2)
  createTime = _messages.StringField(3)
  deployJobRun = _messages.MessageField('DeployJobRun', 4)
  endTime = _messages.StringField(5)
  etag = _messages.StringField(6)
  jobId = _messages.StringField(7)
  name = _messages.StringField(8)
  phaseId = _messages.StringField(9)
  postdeployJobRun = _messages.MessageField('PostdeployJobRun', 10)
  predeployJobRun = _messages.MessageField('PredeployJobRun', 11)
  startTime = _messages.StringField(12)
  state = _messages.EnumField('StateValueValuesEnum', 13)
  uid = _messages.StringField(14)
  verifyJobRun = _messages.MessageField('VerifyJobRun', 15)


class JobRunNotificationEvent(_messages.Message):
  r"""Payload proto for "clouddeploy.googleapis.com/jobrun_notification"
  Platform Log event that describes the failure to send JobRun resource update
  Pub/Sub notification.

  Enums:
    TypeValueValuesEnum: Type of this notification, e.g. for a Pub/Sub
      failure.

  Fields:
    jobRun: The name of the `JobRun`.
    message: Debug message for when a notification fails to send.
    pipelineUid: Unique identifier of the `DeliveryPipeline`.
    releaseUid: Unique identifier of the `Release`.
    rolloutUid: Unique identifier of the `Rollout`.
    targetId: ID of the `Target`.
    type: Type of this notification, e.g. for a Pub/Sub failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this notification, e.g. for a Pub/Sub failure.

    Values:
      TYPE_UNSPECIFIED: Type is unspecified.
      TYPE_PUBSUB_NOTIFICATION_FAILURE: A Pub/Sub notification failed to be
        sent.
      TYPE_RESOURCE_STATE_CHANGE: Resource state changed.
      TYPE_PROCESS_ABORTED: A process aborted.
      TYPE_RESTRICTION_VIOLATED: Restriction check failed.
      TYPE_RESOURCE_DELETED: Resource deleted.
      TYPE_RENDER_STATUES_CHANGE: Deprecated: This field is never used. Use
        release_render log type instead.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PUBSUB_NOTIFICATION_FAILURE = 1
    TYPE_RESOURCE_STATE_CHANGE = 2
    TYPE_PROCESS_ABORTED = 3
    TYPE_RESTRICTION_VIOLATED = 4
    TYPE_RESOURCE_DELETED = 5
    TYPE_RENDER_STATUES_CHANGE = 6

  jobRun = _messages.StringField(1)
  message = _messages.StringField(2)
  pipelineUid = _messages.StringField(3)
  releaseUid = _messages.StringField(4)
  rolloutUid = _messages.StringField(5)
  targetId = _messages.StringField(6)
  type = _messages.EnumField('TypeValueValuesEnum', 7)


class KubernetesConfig(_messages.Message):
  r"""KubernetesConfig contains the Kubernetes runtime configuration.

  Fields:
    gatewayServiceMesh: Kubernetes Gateway API service mesh configuration.
    serviceNetworking: Kubernetes Service networking configuration.
  """

  gatewayServiceMesh = _messages.MessageField('GatewayServiceMesh', 1)
  serviceNetworking = _messages.MessageField('ServiceNetworking', 2)


class ListAutomationRunsResponse(_messages.Message):
  r"""The response object from `ListAutomationRuns`.

  Fields:
    automationRuns: The `AutomationRuns` objects.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  automationRuns = _messages.MessageField('AutomationRun', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListAutomationsResponse(_messages.Message):
  r"""The response object from `ListAutomations`.

  Fields:
    automations: The `Automations` objects.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  automations = _messages.MessageField('Automation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListCustomTargetTypesResponse(_messages.Message):
  r"""The response object from `ListCustomTargetTypes.`

  Fields:
    customTargetTypes: The `CustomTargetType` objects.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  customTargetTypes = _messages.MessageField('CustomTargetType', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListDeliveryPipelinesResponse(_messages.Message):
  r"""The response object from `ListDeliveryPipelines`.

  Fields:
    deliveryPipelines: The `DeliveryPipeline` objects.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  deliveryPipelines = _messages.MessageField('DeliveryPipeline', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListDeployPoliciesResponse(_messages.Message):
  r"""The response object from `ListDeployPolicies`.

  Fields:
    deployPolicies: The `DeployPolicy` objects.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached.
  """

  deployPolicies = _messages.MessageField('DeployPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListJobRunsResponse(_messages.Message):
  r"""ListJobRunsResponse is the response object returned by `ListJobRuns`.

  Fields:
    jobRuns: The `JobRun` objects.
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    unreachable: Locations that could not be reached
  """

  jobRuns = _messages.MessageField('JobRun', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListReleasesResponse(_messages.Message):
  r"""The response object from `ListReleases`.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    releases: The `Release` objects.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  releases = _messages.MessageField('Release', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListRolloutsResponse(_messages.Message):
  r"""ListRolloutsResponse is the response object reutrned by `ListRollouts`.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    rollouts: The `Rollout` objects.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  rollouts = _messages.MessageField('Rollout', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class ListTargetsResponse(_messages.Message):
  r"""The response object from `ListTargets`.

  Fields:
    nextPageToken: A token, which can be sent as `page_token` to retrieve the
      next page. If this field is omitted, there are no subsequent pages.
    targets: The `Target` objects.
    unreachable: Locations that could not be reached.
  """

  nextPageToken = _messages.StringField(1)
  targets = _messages.MessageField('Target', 2, repeated=True)
  unreachable = _messages.StringField(3, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Metadata(_messages.Message):
  r"""Metadata includes information associated with a `Rollout`.

  Fields:
    cloudRun: Output only. The name of the Cloud Run Service that is
      associated with a `Rollout`.
  """

  cloudRun = _messages.MessageField('CloudRunMetadata', 1)


class MultiTarget(_messages.Message):
  r"""Information specifying a multiTarget.

  Fields:
    targetIds: Required. The target_ids of this multiTarget.
  """

  targetIds = _messages.StringField(1, repeated=True)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Phase(_messages.Message):
  r"""Phase represents a collection of jobs that are logically grouped
  together for a `Rollout`.

  Enums:
    StateValueValuesEnum: Output only. Current state of the Phase.

  Fields:
    childRolloutJobs: Output only. ChildRollout job composition.
    deploymentJobs: Output only. Deployment job composition.
    id: Output only. The ID of the Phase.
    skipMessage: Output only. Additional information on why the Phase was
      skipped, if available.
    state: Output only. Current state of the Phase.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the Phase.

    Values:
      STATE_UNSPECIFIED: The Phase has an unspecified state.
      PENDING: The Phase is waiting for an earlier Phase(s) to complete.
      IN_PROGRESS: The Phase is in progress.
      SUCCEEDED: The Phase has succeeded.
      FAILED: The Phase has failed.
      ABORTED: The Phase was aborted.
      SKIPPED: The Phase was skipped.
    """
    STATE_UNSPECIFIED = 0
    PENDING = 1
    IN_PROGRESS = 2
    SUCCEEDED = 3
    FAILED = 4
    ABORTED = 5
    SKIPPED = 6

  childRolloutJobs = _messages.MessageField('ChildRolloutJobs', 1)
  deploymentJobs = _messages.MessageField('DeploymentJobs', 2)
  id = _messages.StringField(3)
  skipMessage = _messages.StringField(4)
  state = _messages.EnumField('StateValueValuesEnum', 5)


class PhaseArtifact(_messages.Message):
  r"""Contains the paths to the artifacts, relative to the URI, for a phase.

  Fields:
    jobManifestsPath: Output only. File path of the directory of rendered job
      manifests relative to the URI. This is only set if it is applicable.
    manifestPath: Output only. File path of the rendered manifest relative to
      the URI.
    skaffoldConfigPath: Output only. File path of the resolved Skaffold
      configuration relative to the URI.
  """

  jobManifestsPath = _messages.StringField(1)
  manifestPath = _messages.StringField(2)
  skaffoldConfigPath = _messages.StringField(3)


class PhaseConfig(_messages.Message):
  r"""PhaseConfig represents the configuration for a phase in the custom
  canary deployment.

  Fields:
    percentage: Required. Percentage deployment for the phase.
    phaseId: Required. The ID to assign to the `Rollout` phase. This value
      must consist of lower-case letters, numbers, and hyphens, start with a
      letter and end with a letter or a number, and have a max length of 63
      characters. In other words, it must match the following regex:
      `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    postdeploy: Optional. Configuration for the postdeploy job of this phase.
      If this is not configured, there will be no postdeploy job for this
      phase.
    predeploy: Optional. Configuration for the predeploy job of this phase. If
      this is not configured, there will be no predeploy job for this phase.
    profiles: Skaffold profiles to use when rendering the manifest for this
      phase. These are in addition to the profiles list specified in the
      `DeliveryPipeline` stage.
    verify: Whether to run verify tests after the deployment.
  """

  percentage = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  phaseId = _messages.StringField(2)
  postdeploy = _messages.MessageField('Postdeploy', 3)
  predeploy = _messages.MessageField('Predeploy', 4)
  profiles = _messages.StringField(5, repeated=True)
  verify = _messages.BooleanField(6)


class PipelineCondition(_messages.Message):
  r"""PipelineCondition contains all conditions relevant to a Delivery
  Pipeline.

  Fields:
    pipelineReadyCondition: Details around the Pipeline's overall status.
    targetsPresentCondition: Details around targets enumerated in the
      pipeline.
    targetsTypeCondition: Details on the whether the targets enumerated in the
      pipeline are of the same type.
  """

  pipelineReadyCondition = _messages.MessageField('PipelineReadyCondition', 1)
  targetsPresentCondition = _messages.MessageField('TargetsPresentCondition', 2)
  targetsTypeCondition = _messages.MessageField('TargetsTypeCondition', 3)


class PipelineReadyCondition(_messages.Message):
  r"""PipelineReadyCondition contains information around the status of the
  Pipeline.

  Fields:
    status: True if the Pipeline is in a valid state. Otherwise at least one
      condition in `PipelineCondition` is in an invalid state. Iterate over
      those conditions and see which condition(s) has status = false to find
      out what is wrong with the Pipeline.
    updateTime: Last time the condition was updated.
  """

  status = _messages.BooleanField(1)
  updateTime = _messages.StringField(2)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class PolicyRule(_messages.Message):
  r"""Rule to apply.

  Fields:
    restrictRollouts: Rollout restrictions.
  """

  restrictRollouts = _messages.MessageField('RestrictRollout', 1)


class PolicyViolation(_messages.Message):
  r"""Returned from an action if one or more policies were violated, and
  therefore the action was prevented. Contains information about what policies
  were violated and why.

  Fields:
    policyViolationDetails: Policy violation details.
  """

  policyViolationDetails = _messages.MessageField('PolicyViolationDetails', 1, repeated=True)


class PolicyViolationDetails(_messages.Message):
  r"""Policy violation details.

  Fields:
    failureMessage: User readable message about why it failed.
    policy: Name of the policy that was violated. Policy resource will be in
      the format of projects/{project}/locations/{location}/policies/{policy}.
    ruleId: Name of the rule that triggered the policy violation.
    ruleType: Rule type (e.g. rollout restrictions, release requirements) that
      failed.
  """

  failureMessage = _messages.StringField(1)
  policy = _messages.StringField(2)
  ruleId = _messages.StringField(3)
  ruleType = _messages.StringField(4)


class Postdeploy(_messages.Message):
  r"""Postdeploy contains the postdeploy job configuration information.

  Fields:
    actions: Optional. A sequence of Skaffold custom actions to invoke during
      execution of the postdeploy job.
  """

  actions = _messages.StringField(1, repeated=True)


class PostdeployJob(_messages.Message):
  r"""A postdeploy Job.

  Fields:
    actions: Output only. The custom actions that the postdeploy Job executes.
  """

  actions = _messages.StringField(1, repeated=True)


class PostdeployJobRun(_messages.Message):
  r"""PostdeployJobRun contains information specific to a postdeploy `JobRun`.

  Enums:
    FailureCauseValueValuesEnum: Output only. The reason the postdeploy
      failed. This will always be unspecified while the postdeploy is in
      progress or if it succeeded.

  Fields:
    build: Output only. The resource name of the Cloud Build `Build` object
      that is used to execute the custom actions associated with the
      postdeploy Job. Format is
      projects/{project}/locations/{location}/builds/{build}.
    failureCause: Output only. The reason the postdeploy failed. This will
      always be unspecified while the postdeploy is in progress or if it
      succeeded.
    failureMessage: Output only. Additional information about the postdeploy
      failure, if available.
  """

  class FailureCauseValueValuesEnum(_messages.Enum):
    r"""Output only. The reason the postdeploy failed. This will always be
    unspecified while the postdeploy is in progress or if it succeeded.

    Values:
      FAILURE_CAUSE_UNSPECIFIED: No reason for failure is specified.
      CLOUD_BUILD_UNAVAILABLE: Cloud Build is not available, either because it
        is not enabled or because Cloud Deploy has insufficient permissions.
        See [required permission](https://cloud.google.com/deploy/docs/cloud-
        deploy-service-account#required_permissions).
      EXECUTION_FAILED: The postdeploy operation did not complete
        successfully; check Cloud Build logs.
      DEADLINE_EXCEEDED: The postdeploy job run did not complete within the
        alloted time.
      CLOUD_BUILD_REQUEST_FAILED: Cloud Build failed to fulfill Cloud Deploy's
        request. See failure_message for additional details.
    """
    FAILURE_CAUSE_UNSPECIFIED = 0
    CLOUD_BUILD_UNAVAILABLE = 1
    EXECUTION_FAILED = 2
    DEADLINE_EXCEEDED = 3
    CLOUD_BUILD_REQUEST_FAILED = 4

  build = _messages.StringField(1)
  failureCause = _messages.EnumField('FailureCauseValueValuesEnum', 2)
  failureMessage = _messages.StringField(3)


class Predeploy(_messages.Message):
  r"""Predeploy contains the predeploy job configuration information.

  Fields:
    actions: Optional. A sequence of Skaffold custom actions to invoke during
      execution of the predeploy job.
  """

  actions = _messages.StringField(1, repeated=True)


class PredeployJob(_messages.Message):
  r"""A predeploy Job.

  Fields:
    actions: Output only. The custom actions that the predeploy Job executes.
  """

  actions = _messages.StringField(1, repeated=True)


class PredeployJobRun(_messages.Message):
  r"""PredeployJobRun contains information specific to a predeploy `JobRun`.

  Enums:
    FailureCauseValueValuesEnum: Output only. The reason the predeploy failed.
      This will always be unspecified while the predeploy is in progress or if
      it succeeded.

  Fields:
    build: Output only. The resource name of the Cloud Build `Build` object
      that is used to execute the custom actions associated with the predeploy
      Job. Format is projects/{project}/locations/{location}/builds/{build}.
    failureCause: Output only. The reason the predeploy failed. This will
      always be unspecified while the predeploy is in progress or if it
      succeeded.
    failureMessage: Output only. Additional information about the predeploy
      failure, if available.
  """

  class FailureCauseValueValuesEnum(_messages.Enum):
    r"""Output only. The reason the predeploy failed. This will always be
    unspecified while the predeploy is in progress or if it succeeded.

    Values:
      FAILURE_CAUSE_UNSPECIFIED: No reason for failure is specified.
      CLOUD_BUILD_UNAVAILABLE: Cloud Build is not available, either because it
        is not enabled or because Cloud Deploy has insufficient permissions.
        See [required permission](https://cloud.google.com/deploy/docs/cloud-
        deploy-service-account#required_permissions).
      EXECUTION_FAILED: The predeploy operation did not complete successfully;
        check Cloud Build logs.
      DEADLINE_EXCEEDED: The predeploy job run did not complete within the
        alloted time.
      CLOUD_BUILD_REQUEST_FAILED: Cloud Build failed to fulfill Cloud Deploy's
        request. See failure_message for additional details.
    """
    FAILURE_CAUSE_UNSPECIFIED = 0
    CLOUD_BUILD_UNAVAILABLE = 1
    EXECUTION_FAILED = 2
    DEADLINE_EXCEEDED = 3
    CLOUD_BUILD_REQUEST_FAILED = 4

  build = _messages.StringField(1)
  failureCause = _messages.EnumField('FailureCauseValueValuesEnum', 2)
  failureMessage = _messages.StringField(3)


class PrivatePool(_messages.Message):
  r"""Execution using a private Cloud Build pool.

  Fields:
    artifactStorage: Optional. Cloud Storage location where execution outputs
      should be stored. This can either be a bucket ("gs://my-bucket") or a
      path within a bucket ("gs://my-bucket/my-dir"). If unspecified, a
      default bucket located in the same region will be used.
    serviceAccount: Optional. Google service account to use for execution. If
      unspecified, the project execution service account
      (-<EMAIL>) will be used.
    workerPool: Required. Resource name of the Cloud Build worker pool to use.
      The format is
      `projects/{project}/locations/{location}/workerPools/{pool}`.
  """

  artifactStorage = _messages.StringField(1)
  serviceAccount = _messages.StringField(2)
  workerPool = _messages.StringField(3)


class PromoteReleaseOperation(_messages.Message):
  r"""Contains the information of an automated promote-release operation.

  Fields:
    phase: Output only. The starting phase of the rollout created by this
      operation.
    rollout: Output only. The name of the rollout that initiates the
      `AutomationRun`.
    targetId: Output only. The ID of the target that represents the promotion
      stage to which the release will be promoted. The value of this field is
      the last segment of a target name.
    wait: Output only. How long the operation will be paused.
  """

  phase = _messages.StringField(1)
  rollout = _messages.StringField(2)
  targetId = _messages.StringField(3)
  wait = _messages.StringField(4)


class PromoteReleaseRule(_messages.Message):
  r"""`PromoteRelease` rule will automatically promote a release from the
  current target to a specified target.

  Enums:
    WaitPolicyValueValuesEnum: Optional. WaitForDeployPolicy delays a release
      promotion when a deploy policy violation is encountered.

  Fields:
    condition: Output only. Information around the state of the Automation
      rule.
    destinationPhase: Optional. The starting phase of the rollout created by
      this operation. Default to the first phase.
    destinationTargetId: Optional. The ID of the stage in the pipeline to
      which this `Release` is deploying. If unspecified, default it to the
      next stage in the promotion flow. The value of this field could be one
      of the following: * The last segment of a target name. It only needs the
      ID to determine if the target is one of the stages in the promotion
      sequence defined in the pipeline. * "@next", the next target in the
      promotion sequence.
    id: Required. ID of the rule. This id must be unique in the `Automation`
      resource to which this rule belongs. The format is a-z{0,62}.
    phase: Optional. Deprecated: use destination_phase instead. The starting
      phase of the rollout created by this operation. Default to the first
      phase.
    targetId: Optional. Deprecated: use destination_target_id instead. The ID
      of the stage in the pipeline to which this `Release` is deploying. If
      unspecified, default it to the next stage in the promotion flow. The
      value of this field could be one of the following: * The last segment of
      a target name. It only needs the ID to determine if the target is one of
      the stages in the promotion sequence defined in the pipeline. * "@next",
      the next target in the promotion sequence.
    wait: Optional. How long the release need to be paused until being
      promoted to the next target.
    waitPolicy: Optional. WaitForDeployPolicy delays a release promotion when
      a deploy policy violation is encountered.
  """

  class WaitPolicyValueValuesEnum(_messages.Enum):
    r"""Optional. WaitForDeployPolicy delays a release promotion when a deploy
    policy violation is encountered.

    Values:
      WAIT_FOR_DEPLOY_POLICY_UNSPECIFIED: No WaitForDeployPolicy is specified.
      NEVER: Never waits on DeployPolicy, terminates `AutomationRun` if
        DeployPolicy check failed.
      LATEST: When policy passes, execute the latest `AutomationRun` only.
    """
    WAIT_FOR_DEPLOY_POLICY_UNSPECIFIED = 0
    NEVER = 1
    LATEST = 2

  condition = _messages.MessageField('AutomationRuleCondition', 1)
  destinationPhase = _messages.StringField(2)
  destinationTargetId = _messages.StringField(3)
  id = _messages.StringField(4)
  phase = _messages.StringField(5)
  targetId = _messages.StringField(6)
  wait = _messages.StringField(7)
  waitPolicy = _messages.EnumField('WaitPolicyValueValuesEnum', 8)


class Range(_messages.Message):
  r"""Range within which actions are restricted.

  Enums:
    DayOfWeekValueListEntryValuesEnum:

  Fields:
    dayOfWeek: Days of week.
    endDate: End date.
    endTimeOfDay: End time of day.
    startDate: Start date.
    startTimeOfDay: Start time of day.
  """

  class DayOfWeekValueListEntryValuesEnum(_messages.Enum):
    r"""DayOfWeekValueListEntryValuesEnum enum type.

    Values:
      DAY_OF_WEEK_UNSPECIFIED: The day of the week is unspecified.
      MONDAY: Monday
      TUESDAY: Tuesday
      WEDNESDAY: Wednesday
      THURSDAY: Thursday
      FRIDAY: Friday
      SATURDAY: Saturday
      SUNDAY: Sunday
    """
    DAY_OF_WEEK_UNSPECIFIED = 0
    MONDAY = 1
    TUESDAY = 2
    WEDNESDAY = 3
    THURSDAY = 4
    FRIDAY = 5
    SATURDAY = 6
    SUNDAY = 7

  dayOfWeek = _messages.EnumField('DayOfWeekValueListEntryValuesEnum', 1, repeated=True)
  endDate = _messages.MessageField('Date', 2)
  endTimeOfDay = _messages.MessageField('TimeOfDay', 3)
  startDate = _messages.MessageField('Date', 4)
  startTimeOfDay = _messages.MessageField('TimeOfDay', 5)


class Release(_messages.Message):
  r"""A `Release` resource in the Cloud Deploy API. A `Release` defines a
  specific Skaffold configuration instance that can be deployed.

  Enums:
    RenderStateValueValuesEnum: Output only. Current state of the render
      operation.

  Messages:
    AnnotationsValue: User annotations. These attributes can only be set and
      used by the user, and not by Cloud Deploy. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    DeployParametersValue: Optional. The deploy parameters to use for all
      targets in this release.
    LabelsValue: Labels are attributes that can be set and used by both the
      user and by Cloud Deploy. Labels must meet the following constraints: *
      Keys and values can contain only lowercase letters, numeric characters,
      underscores, and dashes. * All characters must use UTF-8 encoding, and
      international characters are allowed. * Keys must start with a lowercase
      letter or international character. * Each resource is limited to a
      maximum of 64 labels. Both keys and values are additionally constrained
      to be <= 128 bytes.
    TargetArtifactsValue: Output only. Map from target ID to the target
      artifacts created during the render operation.
    TargetRendersValue: Output only. Map from target ID to details of the
      render operation for that target.

  Fields:
    abandoned: Output only. Indicates whether this is an abandoned release.
    annotations: User annotations. These attributes can only be set and used
      by the user, and not by Cloud Deploy. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    buildArtifacts: List of artifacts to pass through to Skaffold command.
    condition: Output only. Information around the state of the Release.
    createTime: Output only. Time at which the `Release` was created.
    customTargetTypeSnapshots: Output only. Snapshot of the custom target
      types referenced by the targets taken at release creation time.
    deliveryPipelineSnapshot: Output only. Snapshot of the parent pipeline
      taken at release creation time.
    deployParameters: Optional. The deploy parameters to use for all targets
      in this release.
    description: Description of the `Release`. Max length is 255 characters.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding.
    labels: Labels are attributes that can be set and used by both the user
      and by Cloud Deploy. Labels must meet the following constraints: * Keys
      and values can contain only lowercase letters, numeric characters,
      underscores, and dashes. * All characters must use UTF-8 encoding, and
      international characters are allowed. * Keys must start with a lowercase
      letter or international character. * Each resource is limited to a
      maximum of 64 labels. Both keys and values are additionally constrained
      to be <= 128 bytes.
    name: Optional. Name of the `Release`. Format is projects/{project}/
      locations/{location}/deliveryPipelines/{deliveryPipeline}/
      releases/a-z{0,62}.
    renderEndTime: Output only. Time at which the render completed.
    renderStartTime: Output only. Time at which the render began.
    renderState: Output only. Current state of the render operation.
    skaffoldConfigPath: Filepath of the Skaffold config inside of the config
      URI.
    skaffoldConfigUri: Cloud Storage URI of tar.gz archive containing Skaffold
      configuration.
    skaffoldVersion: The Skaffold version to use when operating on this
      release, such as "1.20.0". Not all versions are valid; Cloud Deploy
      supports a specific set of versions. If unset, the most recent supported
      Skaffold version will be used.
    targetArtifacts: Output only. Map from target ID to the target artifacts
      created during the render operation.
    targetRenders: Output only. Map from target ID to details of the render
      operation for that target.
    targetSnapshots: Output only. Snapshot of the targets taken at release
      creation time.
    uid: Output only. Unique identifier of the `Release`.
  """

  class RenderStateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the render operation.

    Values:
      RENDER_STATE_UNSPECIFIED: The render state is unspecified.
      SUCCEEDED: All rendering operations have completed successfully.
      FAILED: All rendering operations have completed, and one or more have
        failed.
      IN_PROGRESS: Rendering has started and is not complete.
    """
    RENDER_STATE_UNSPECIFIED = 0
    SUCCEEDED = 1
    FAILED = 2
    IN_PROGRESS = 3

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""User annotations. These attributes can only be set and used by the
    user, and not by Cloud Deploy. See https://google.aip.dev/128#annotations
    for more details such as format and size limitations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DeployParametersValue(_messages.Message):
    r"""Optional. The deploy parameters to use for all targets in this
    release.

    Messages:
      AdditionalProperty: An additional property for a DeployParametersValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        DeployParametersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DeployParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels are attributes that can be set and used by both the user and by
    Cloud Deploy. Labels must meet the following constraints: * Keys and
    values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. Both keys and values are additionally constrained to be <=
    128 bytes.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TargetArtifactsValue(_messages.Message):
    r"""Output only. Map from target ID to the target artifacts created during
    the render operation.

    Messages:
      AdditionalProperty: An additional property for a TargetArtifactsValue
        object.

    Fields:
      additionalProperties: Additional properties of type TargetArtifactsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TargetArtifactsValue object.

      Fields:
        key: Name of the additional property.
        value: A TargetArtifact attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('TargetArtifact', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TargetRendersValue(_messages.Message):
    r"""Output only. Map from target ID to details of the render operation for
    that target.

    Messages:
      AdditionalProperty: An additional property for a TargetRendersValue
        object.

    Fields:
      additionalProperties: Additional properties of type TargetRendersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TargetRendersValue object.

      Fields:
        key: Name of the additional property.
        value: A TargetRender attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('TargetRender', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  abandoned = _messages.BooleanField(1)
  annotations = _messages.MessageField('AnnotationsValue', 2)
  buildArtifacts = _messages.MessageField('BuildArtifact', 3, repeated=True)
  condition = _messages.MessageField('ReleaseCondition', 4)
  createTime = _messages.StringField(5)
  customTargetTypeSnapshots = _messages.MessageField('CustomTargetType', 6, repeated=True)
  deliveryPipelineSnapshot = _messages.MessageField('DeliveryPipeline', 7)
  deployParameters = _messages.MessageField('DeployParametersValue', 8)
  description = _messages.StringField(9)
  etag = _messages.StringField(10)
  labels = _messages.MessageField('LabelsValue', 11)
  name = _messages.StringField(12)
  renderEndTime = _messages.StringField(13)
  renderStartTime = _messages.StringField(14)
  renderState = _messages.EnumField('RenderStateValueValuesEnum', 15)
  skaffoldConfigPath = _messages.StringField(16)
  skaffoldConfigUri = _messages.StringField(17)
  skaffoldVersion = _messages.StringField(18)
  targetArtifacts = _messages.MessageField('TargetArtifactsValue', 19)
  targetRenders = _messages.MessageField('TargetRendersValue', 20)
  targetSnapshots = _messages.MessageField('Target', 21, repeated=True)
  uid = _messages.StringField(22)


class ReleaseCondition(_messages.Message):
  r"""ReleaseCondition contains all conditions relevant to a Release.

  Fields:
    releaseReadyCondition: Details around the Releases's overall status.
    skaffoldSupportedCondition: Details around the support state of the
      release's skaffold version.
  """

  releaseReadyCondition = _messages.MessageField('ReleaseReadyCondition', 1)
  skaffoldSupportedCondition = _messages.MessageField('SkaffoldSupportedCondition', 2)


class ReleaseNotificationEvent(_messages.Message):
  r"""Payload proto for "clouddeploy.googleapis.com/release_notification"
  Platform Log event that describes the failure to send release status change
  Pub/Sub notification.

  Enums:
    TypeValueValuesEnum: Type of this notification, e.g. for a Pub/Sub
      failure.

  Fields:
    message: Debug message for when a notification fails to send.
    release: The name of the `Release`.
    type: Type of this notification, e.g. for a Pub/Sub failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this notification, e.g. for a Pub/Sub failure.

    Values:
      TYPE_UNSPECIFIED: Type is unspecified.
      TYPE_PUBSUB_NOTIFICATION_FAILURE: A Pub/Sub notification failed to be
        sent.
      TYPE_RESOURCE_STATE_CHANGE: Resource state changed.
      TYPE_PROCESS_ABORTED: A process aborted.
      TYPE_RESTRICTION_VIOLATED: Restriction check failed.
      TYPE_RESOURCE_DELETED: Resource deleted.
      TYPE_RENDER_STATUES_CHANGE: Deprecated: This field is never used. Use
        release_render log type instead.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PUBSUB_NOTIFICATION_FAILURE = 1
    TYPE_RESOURCE_STATE_CHANGE = 2
    TYPE_PROCESS_ABORTED = 3
    TYPE_RESTRICTION_VIOLATED = 4
    TYPE_RESOURCE_DELETED = 5
    TYPE_RENDER_STATUES_CHANGE = 6

  message = _messages.StringField(1)
  release = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class ReleaseReadyCondition(_messages.Message):
  r"""ReleaseReadyCondition contains information around the status of the
  Release. If a release is not ready, you cannot create a rollout with the
  release.

  Fields:
    status: True if the Release is in a valid state. Otherwise at least one
      condition in `ReleaseCondition` is in an invalid state. Iterate over
      those conditions and see which condition(s) has status = false to find
      out what is wrong with the Release.
  """

  status = _messages.BooleanField(1)


class ReleaseRenderEvent(_messages.Message):
  r"""Payload proto for "clouddeploy.googleapis.com/release_render" Platform
  Log event that describes the render status change.

  Fields:
    message: Debug message for when a render transition occurs. Provides
      further details as rendering progresses through render states.
    release: The name of the `Release`.
  """

  message = _messages.StringField(1)
  release = _messages.StringField(2)


class RenderMetadata(_messages.Message):
  r"""RenderMetadata includes information associated with a `Release` render.

  Fields:
    cloudRun: Output only. Metadata associated with rendering for Cloud Run.
  """

  cloudRun = _messages.MessageField('CloudRunRenderMetadata', 1)


class Resource(_messages.Message):
  r"""Contains information on the resources to select for a deploy policy.

  Fields:
    deliveryPipelines: Contains attributes about a delivery pipeline.
    targets: Contains attributes about a target.
  """

  deliveryPipelines = _messages.MessageField('DeliveryPipelineAttribute', 1, repeated=True)
  targets = _messages.MessageField('TargetAttribute', 2, repeated=True)


class RestrictRollout(_messages.Message):
  r"""Rollout restrictions.

  Enums:
    ActionsValueListEntryValuesEnum:
    InvokerValueListEntryValuesEnum:

  Fields:
    actions: Rollout actions to be restricted as part of the policy. If left
      empty, all actions will be restricted.
    invoker: What invoked the action. If left empty, all invoker types will be
      restricted.
    name: Restriction name.
    timeWindows: Time Windows within which actions are restricted.
  """

  class ActionsValueListEntryValuesEnum(_messages.Enum):
    r"""ActionsValueListEntryValuesEnum enum type.

    Values:
      ACTIONS_UNSPECIFIED: Unspecified.
      ADVANCE: Advance the rollout to the next phase.
      APPROVE: Approve the rollout.
      CANCEL: Cancel the rollout.
      CREATE: Create a rollout.
      DELETE: Delete a rollout.
      IGNORE_JOB: Ignore a job result on the rollout.
      REJECT: Reject a rollout.
      RETRY_JOB: Retry a job for a rollout.
      ROLLBACK: Rollback a rollout.
      TERMINATE_JOBRUN: Terminate a jobrun.
    """
    ACTIONS_UNSPECIFIED = 0
    ADVANCE = 1
    APPROVE = 2
    CANCEL = 3
    CREATE = 4
    DELETE = 5
    IGNORE_JOB = 6
    REJECT = 7
    RETRY_JOB = 8
    ROLLBACK = 9
    TERMINATE_JOBRUN = 10

  class InvokerValueListEntryValuesEnum(_messages.Enum):
    r"""InvokerValueListEntryValuesEnum enum type.

    Values:
      INVOKER_UNSPECIFIED: Unspecified.
      USER: The action is user-driven (e.g. creating a rollout manually via a
        gcloud create command).
      DEPLOY_AUTOMATION: Automated action by Cloud Deploy.
    """
    INVOKER_UNSPECIFIED = 0
    USER = 1
    DEPLOY_AUTOMATION = 2

  actions = _messages.EnumField('ActionsValueListEntryValuesEnum', 1, repeated=True)
  invoker = _messages.EnumField('InvokerValueListEntryValuesEnum', 2, repeated=True)
  name = _messages.StringField(3)
  timeWindows = _messages.MessageField('TimeWindow', 4)


class RetryJobRequest(_messages.Message):
  r"""RetryJobRequest is the request object used by `RetryJob`.

  Fields:
    jobId: Required. The job ID for the Job to retry.
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      projects/{project}/ locations/{location}/deployPolicies/a-z{0,62}.
    phaseId: Required. The phase ID the Job to retry belongs to.
  """

  jobId = _messages.StringField(1)
  overrideDeployPolicy = _messages.StringField(2, repeated=True)
  phaseId = _messages.StringField(3)


class RetryJobResponse(_messages.Message):
  r"""The response object from 'RetryJob'."""


class RollbackTargetConfig(_messages.Message):
  r"""Congs for the Rollback rollout.

  Fields:
    rollout: Optional. The rollback `Rollout` to create.
    startingPhaseId: Optional. The starting phase ID for the `Rollout`. If
      unspecified, the `Rollout` will start in the stable phase.
  """

  rollout = _messages.MessageField('Rollout', 1)
  startingPhaseId = _messages.StringField(2)


class RollbackTargetRequest(_messages.Message):
  r"""The request object for `RollbackTarget`.

  Fields:
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      projects/{project}/ locations/{location}/deployPolicies/{deploy_policy}.
    releaseId: Optional. ID of the `Release` to rollback to. If this isn't
      specified, the previous successful `Rollout` to the specified target
      will be used to determine the `Release`.
    rollbackConfig: Optional. Configs for the rollback `Rollout`.
    rolloutId: Required. ID of the rollback `Rollout` to create.
    rolloutToRollBack: Optional. If provided, this must be the latest
      `Rollout` that is on the `Target`.
    targetId: Required. ID of the `Target` that is being rolled back.
    validateOnly: Optional. If set to true, the request is validated and the
      user is provided with a `RollbackTargetResponse`.
  """

  overrideDeployPolicy = _messages.StringField(1, repeated=True)
  releaseId = _messages.StringField(2)
  rollbackConfig = _messages.MessageField('RollbackTargetConfig', 3)
  rolloutId = _messages.StringField(4)
  rolloutToRollBack = _messages.StringField(5)
  targetId = _messages.StringField(6)
  validateOnly = _messages.BooleanField(7)


class RollbackTargetResponse(_messages.Message):
  r"""The response object from `RollbackTarget`.

  Fields:
    rollbackConfig: The config of the rollback `Rollout` created or will be
      created.
  """

  rollbackConfig = _messages.MessageField('RollbackTargetConfig', 1)


class Rollout(_messages.Message):
  r"""A `Rollout` resource in the Cloud Deploy API. A `Rollout` contains
  information around a specific deployment to a `Target`.

  Enums:
    ApprovalStateValueValuesEnum: Output only. Approval state of the
      `Rollout`.
    DeployFailureCauseValueValuesEnum: Output only. The reason this rollout
      failed. This will always be unspecified while the rollout is in
      progress.
    StateValueValuesEnum: Output only. Current state of the `Rollout`.

  Messages:
    AnnotationsValue: User annotations. These attributes can only be set and
      used by the user, and not by Cloud Deploy. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    LabelsValue: Labels are attributes that can be set and used by both the
      user and by Cloud Deploy. Labels must meet the following constraints: *
      Keys and values can contain only lowercase letters, numeric characters,
      underscores, and dashes. * All characters must use UTF-8 encoding, and
      international characters are allowed. * Keys must start with a lowercase
      letter or international character. * Each resource is limited to a
      maximum of 64 labels. Both keys and values are additionally constrained
      to be <= 128 bytes.

  Fields:
    annotations: User annotations. These attributes can only be set and used
      by the user, and not by Cloud Deploy. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    approvalState: Output only. Approval state of the `Rollout`.
    approveTime: Output only. Time at which the `Rollout` was approved.
    controllerRollout: Output only. Name of the `ControllerRollout`. Format is
      projects/{project}/
      locations/{location}/deliveryPipelines/{deliveryPipeline}/
      releases/{release}/rollouts/a-z{0,62}.
    createTime: Output only. Time at which the `Rollout` was created.
    deployEndTime: Output only. Time at which the `Rollout` finished
      deploying.
    deployFailureCause: Output only. The reason this rollout failed. This will
      always be unspecified while the rollout is in progress.
    deployStartTime: Output only. Time at which the `Rollout` started
      deploying.
    deployingBuild: Output only. The resource name of the Cloud Build `Build`
      object that is used to deploy the Rollout. Format is
      `projects/{project}/locations/{location}/builds/{build}`.
    description: Description of the `Rollout` for user purposes. Max length is
      255 characters.
    enqueueTime: Output only. Time at which the `Rollout` was enqueued.
    etag: This checksum is computed by the server based on the value of other
      fields, and may be sent on update and delete requests to ensure the
      client has an up-to-date value before proceeding.
    failureReason: Output only. Additional information about the rollout
      failure, if available.
    labels: Labels are attributes that can be set and used by both the user
      and by Cloud Deploy. Labels must meet the following constraints: * Keys
      and values can contain only lowercase letters, numeric characters,
      underscores, and dashes. * All characters must use UTF-8 encoding, and
      international characters are allowed. * Keys must start with a lowercase
      letter or international character. * Each resource is limited to a
      maximum of 64 labels. Both keys and values are additionally constrained
      to be <= 128 bytes.
    metadata: Output only. Metadata contains information about the rollout.
    name: Optional. Name of the `Rollout`. Format is projects/{project}/
      locations/{location}/deliveryPipelines/{deliveryPipeline}/
      releases/{release}/rollouts/a-z{0,62}.
    phases: Output only. The phases that represent the workflows of this
      `Rollout`.
    rollbackOfRollout: Output only. Name of the `Rollout` that is rolled back
      by this `Rollout`. Empty if this `Rollout` wasn't created as a rollback.
    rolledBackByRollouts: Output only. Names of `Rollouts` that rolled back
      this `Rollout`.
    state: Output only. Current state of the `Rollout`.
    targetId: Required. The ID of Target to which this `Rollout` is deploying.
    uid: Output only. Unique identifier of the `Rollout`.
  """

  class ApprovalStateValueValuesEnum(_messages.Enum):
    r"""Output only. Approval state of the `Rollout`.

    Values:
      APPROVAL_STATE_UNSPECIFIED: The `Rollout` has an unspecified approval
        state.
      NEEDS_APPROVAL: The `Rollout` requires approval.
      DOES_NOT_NEED_APPROVAL: The `Rollout` does not require approval.
      APPROVED: The `Rollout` has been approved.
      REJECTED: The `Rollout` has been rejected.
    """
    APPROVAL_STATE_UNSPECIFIED = 0
    NEEDS_APPROVAL = 1
    DOES_NOT_NEED_APPROVAL = 2
    APPROVED = 3
    REJECTED = 4

  class DeployFailureCauseValueValuesEnum(_messages.Enum):
    r"""Output only. The reason this rollout failed. This will always be
    unspecified while the rollout is in progress.

    Values:
      FAILURE_CAUSE_UNSPECIFIED: No reason for failure is specified.
      CLOUD_BUILD_UNAVAILABLE: Cloud Build is not available, either because it
        is not enabled or because Cloud Deploy has insufficient permissions.
        See [required permission](https://cloud.google.com/deploy/docs/cloud-
        deploy-service-account#required_permissions).
      EXECUTION_FAILED: The deploy operation did not complete successfully;
        check Cloud Build logs.
      DEADLINE_EXCEEDED: Deployment did not complete within the alloted time.
      RELEASE_FAILED: Release is in a failed state.
      RELEASE_ABANDONED: Release is abandoned.
      VERIFICATION_CONFIG_NOT_FOUND: No skaffold verify configuration was
        found.
      CLOUD_BUILD_REQUEST_FAILED: Cloud Build failed to fulfill Cloud Deploy's
        request. See failure_message for additional details.
    """
    FAILURE_CAUSE_UNSPECIFIED = 0
    CLOUD_BUILD_UNAVAILABLE = 1
    EXECUTION_FAILED = 2
    DEADLINE_EXCEEDED = 3
    RELEASE_FAILED = 4
    RELEASE_ABANDONED = 5
    VERIFICATION_CONFIG_NOT_FOUND = 6
    CLOUD_BUILD_REQUEST_FAILED = 7

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the `Rollout`.

    Values:
      STATE_UNSPECIFIED: The `Rollout` has an unspecified state.
      SUCCEEDED: The `Rollout` has completed successfully.
      FAILED: The `Rollout` has failed.
      IN_PROGRESS: The `Rollout` is being deployed.
      PENDING_APPROVAL: The `Rollout` needs approval.
      APPROVAL_REJECTED: An approver rejected the `Rollout`.
      PENDING: The `Rollout` is waiting for an earlier Rollout(s) to complete
        on this `Target`.
      PENDING_RELEASE: The `Rollout` is waiting for the `Release` to be fully
        rendered.
      CANCELLING: The `Rollout` is in the process of being cancelled.
      CANCELLED: The `Rollout` has been cancelled.
      HALTED: The `Rollout` is halted.
    """
    STATE_UNSPECIFIED = 0
    SUCCEEDED = 1
    FAILED = 2
    IN_PROGRESS = 3
    PENDING_APPROVAL = 4
    APPROVAL_REJECTED = 5
    PENDING = 6
    PENDING_RELEASE = 7
    CANCELLING = 8
    CANCELLED = 9
    HALTED = 10

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""User annotations. These attributes can only be set and used by the
    user, and not by Cloud Deploy. See https://google.aip.dev/128#annotations
    for more details such as format and size limitations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels are attributes that can be set and used by both the user and by
    Cloud Deploy. Labels must meet the following constraints: * Keys and
    values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. Both keys and values are additionally constrained to be <=
    128 bytes.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  approvalState = _messages.EnumField('ApprovalStateValueValuesEnum', 2)
  approveTime = _messages.StringField(3)
  controllerRollout = _messages.StringField(4)
  createTime = _messages.StringField(5)
  deployEndTime = _messages.StringField(6)
  deployFailureCause = _messages.EnumField('DeployFailureCauseValueValuesEnum', 7)
  deployStartTime = _messages.StringField(8)
  deployingBuild = _messages.StringField(9)
  description = _messages.StringField(10)
  enqueueTime = _messages.StringField(11)
  etag = _messages.StringField(12)
  failureReason = _messages.StringField(13)
  labels = _messages.MessageField('LabelsValue', 14)
  metadata = _messages.MessageField('Metadata', 15)
  name = _messages.StringField(16)
  phases = _messages.MessageField('Phase', 17, repeated=True)
  rollbackOfRollout = _messages.StringField(18)
  rolledBackByRollouts = _messages.StringField(19, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 20)
  targetId = _messages.StringField(21)
  uid = _messages.StringField(22)


class RolloutNotificationEvent(_messages.Message):
  r"""Payload proto for "clouddeploy.googleapis.com/rollout_notification"
  Platform Log event that describes the failure to send rollout status change
  Pub/Sub notification.

  Enums:
    TypeValueValuesEnum: Type of this notification, e.g. for a Pub/Sub
      failure.

  Fields:
    message: Debug message for when a notification fails to send.
    pipelineUid: Unique identifier of the `DeliveryPipeline`.
    releaseUid: Unique identifier of the `Release`.
    rollout: The name of the `Rollout`.
    targetId: ID of the `Target` that the rollout is deployed to.
    type: Type of this notification, e.g. for a Pub/Sub failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this notification, e.g. for a Pub/Sub failure.

    Values:
      TYPE_UNSPECIFIED: Type is unspecified.
      TYPE_PUBSUB_NOTIFICATION_FAILURE: A Pub/Sub notification failed to be
        sent.
      TYPE_RESOURCE_STATE_CHANGE: Resource state changed.
      TYPE_PROCESS_ABORTED: A process aborted.
      TYPE_RESTRICTION_VIOLATED: Restriction check failed.
      TYPE_RESOURCE_DELETED: Resource deleted.
      TYPE_RENDER_STATUES_CHANGE: Deprecated: This field is never used. Use
        release_render log type instead.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PUBSUB_NOTIFICATION_FAILURE = 1
    TYPE_RESOURCE_STATE_CHANGE = 2
    TYPE_PROCESS_ABORTED = 3
    TYPE_RESTRICTION_VIOLATED = 4
    TYPE_RESOURCE_DELETED = 5
    TYPE_RENDER_STATUES_CHANGE = 6

  message = _messages.StringField(1)
  pipelineUid = _messages.StringField(2)
  releaseUid = _messages.StringField(3)
  rollout = _messages.StringField(4)
  targetId = _messages.StringField(5)
  type = _messages.EnumField('TypeValueValuesEnum', 6)


class RuntimeConfig(_messages.Message):
  r"""RuntimeConfig contains the runtime specific configurations for a
  deployment strategy.

  Fields:
    cloudRun: Cloud Run runtime configuration.
    kubernetes: Kubernetes runtime configuration.
  """

  cloudRun = _messages.MessageField('CloudRunConfig', 1)
  kubernetes = _messages.MessageField('KubernetesConfig', 2)


class SerialPipeline(_messages.Message):
  r"""SerialPipeline defines a sequential set of stages for a
  `DeliveryPipeline`.

  Fields:
    stages: Each stage specifies configuration for a `Target`. The ordering of
      this list defines the promotion flow.
  """

  stages = _messages.MessageField('Stage', 1, repeated=True)


class ServiceNetworking(_messages.Message):
  r"""Information about the Kubernetes Service networking configuration.

  Fields:
    deployment: Required. Name of the Kubernetes Deployment whose traffic is
      managed by the specified Service.
    disablePodOverprovisioning: Optional. Whether to disable Pod
      overprovisioning. If Pod overprovisioning is disabled then Cloud Deploy
      will limit the number of total Pods used for the deployment strategy to
      the number of Pods the Deployment has on the cluster.
    service: Required. Name of the Kubernetes Service.
  """

  deployment = _messages.StringField(1)
  disablePodOverprovisioning = _messages.BooleanField(2)
  service = _messages.StringField(3)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class SkaffoldGitSource(_messages.Message):
  r"""Git repository containing Skaffold Config modules.

  Fields:
    path: Required. Relative path from the repository root to the Skaffold
      file.
    ref: Required. Git ref the package should be cloned from.
    repo: Required. Git repository the package should be cloned from.
  """

  path = _messages.StringField(1)
  ref = _messages.StringField(2)
  repo = _messages.StringField(3)


class SkaffoldModules(_messages.Message):
  r"""Skaffold Config modules and their remote source.

  Fields:
    configs: Required. The Skaffold Config modules to use from the specified
      source.
    git: Remote git repository containing the Skaffold Config modules.
  """

  configs = _messages.StringField(1, repeated=True)
  git = _messages.MessageField('SkaffoldGitSource', 2)


class SkaffoldSupportedCondition(_messages.Message):
  r"""SkaffoldSupportedCondition contains information about when support for
  the release's version of skaffold ends.

  Enums:
    SkaffoldSupportStateValueValuesEnum: The skaffold support state for this
      release's version of skaffold.

  Fields:
    maintenanceModeTime: The time at which this release's version of skaffold
      will enter maintenance mode.
    skaffoldSupportState: The skaffold support state for this release's
      version of skaffold.
    status: True if the version of skaffold used by this release is supported.
    supportExpirationTime: The time at which this release's version of
      skaffold will no longer be supported.
  """

  class SkaffoldSupportStateValueValuesEnum(_messages.Enum):
    r"""The skaffold support state for this release's version of skaffold.

    Values:
      SKAFFOLD_SUPPORT_STATE_UNSPECIFIED: Default value. This value is unused.
      SKAFFOLD_SUPPORT_STATE_SUPPORTED: This skaffold version is currently
        supported.
      SKAFFOLD_SUPPORT_STATE_MAINTENANCE_MODE: This skaffold version is in
        maintenance mode.
      SKAFFOLD_SUPPORT_STATE_UNSUPPORTED: This skaffold version is no longer
        supported.
    """
    SKAFFOLD_SUPPORT_STATE_UNSPECIFIED = 0
    SKAFFOLD_SUPPORT_STATE_SUPPORTED = 1
    SKAFFOLD_SUPPORT_STATE_MAINTENANCE_MODE = 2
    SKAFFOLD_SUPPORT_STATE_UNSUPPORTED = 3

  maintenanceModeTime = _messages.StringField(1)
  skaffoldSupportState = _messages.EnumField('SkaffoldSupportStateValueValuesEnum', 2)
  status = _messages.BooleanField(3)
  supportExpirationTime = _messages.StringField(4)


class SkaffoldVersion(_messages.Message):
  r"""Details of a supported Skaffold version.

  Fields:
    maintenanceModeTime: The time at which this version of skaffold will enter
      maintenance mode.
    supportEndDate: Date when this version is expected to no longer be
      supported.
    supportExpirationTime: The time at which this version of skaffold will no
      longer be supported.
    version: Release version number. For example, "1.20.3".
  """

  maintenanceModeTime = _messages.StringField(1)
  supportEndDate = _messages.MessageField('Date', 2)
  supportExpirationTime = _messages.StringField(3)
  version = _messages.StringField(4)


class Stage(_messages.Message):
  r"""Stage specifies a location to which to deploy.

  Fields:
    deployParameters: Optional. The deploy parameters to use for the target in
      this stage.
    profiles: Skaffold profiles to use when rendering the manifest for this
      stage's `Target`.
    strategy: Optional. The strategy to use for a `Rollout` to this stage.
    targetId: The target_id to which this stage points. This field refers
      exclusively to the last segment of a target name. For example, this
      field would just be `my-target` (rather than
      `projects/project/locations/location/targets/my-target`). The location
      of the `Target` is inferred to be the same as the location of the
      `DeliveryPipeline` that contains this `Stage`.
  """

  deployParameters = _messages.MessageField('DeployParameters', 1, repeated=True)
  profiles = _messages.StringField(2, repeated=True)
  strategy = _messages.MessageField('Strategy', 3)
  targetId = _messages.StringField(4)


class Standard(_messages.Message):
  r"""Standard represents the standard deployment strategy.

  Fields:
    postdeploy: Optional. Configuration for the postdeploy job. If this is not
      configured, postdeploy job will not be present.
    predeploy: Optional. Configuration for the predeploy job. If this is not
      configured, predeploy job will not be present.
    verify: Whether to verify a deployment.
  """

  postdeploy = _messages.MessageField('Postdeploy', 1)
  predeploy = _messages.MessageField('Predeploy', 2)
  verify = _messages.BooleanField(3)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class Strategy(_messages.Message):
  r"""Strategy contains deployment strategy information.

  Fields:
    canary: Canary deployment strategy provides progressive percentage based
      deployments to a Target.
    standard: Standard deployment strategy executes a single deploy and allows
      verifying the deployment.
  """

  canary = _messages.MessageField('Canary', 1)
  standard = _messages.MessageField('Standard', 2)


class Target(_messages.Message):
  r"""A `Target` resource in the Cloud Deploy API. A `Target` defines a
  location to which a Skaffold configuration can be deployed.

  Messages:
    AnnotationsValue: Optional. User annotations. These attributes can only be
      set and used by the user, and not by Cloud Deploy. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    DeployParametersValue: Optional. The deploy parameters to use for this
      target.
    LabelsValue: Optional. Labels are attributes that can be set and used by
      both the user and by Cloud Deploy. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. Both keys and values are
      additionally constrained to be <= 128 bytes.

  Fields:
    annotations: Optional. User annotations. These attributes can only be set
      and used by the user, and not by Cloud Deploy. See
      https://google.aip.dev/128#annotations for more details such as format
      and size limitations.
    anthosCluster: Optional. Information specifying an Anthos Cluster.
    createTime: Output only. Time at which the `Target` was created.
    customTarget: Optional. Information specifying a Custom Target.
    deployParameters: Optional. The deploy parameters to use for this target.
    description: Optional. Description of the `Target`. Max length is 255
      characters.
    etag: Optional. This checksum is computed by the server based on the value
      of other fields, and may be sent on update and delete requests to ensure
      the client has an up-to-date value before proceeding.
    executionConfigs: Configurations for all execution that relates to this
      `Target`. Each `ExecutionEnvironmentUsage` value may only be used in a
      single configuration; using the same value multiple times is an error.
      When one or more configurations are specified, they must include the
      `RENDER` and `DEPLOY` `ExecutionEnvironmentUsage` values. When no
      configurations are specified, execution will use the default specified
      in `DefaultPool`.
    gke: Optional. Information specifying a GKE Cluster.
    labels: Optional. Labels are attributes that can be set and used by both
      the user and by Cloud Deploy. Labels must meet the following
      constraints: * Keys and values can contain only lowercase letters,
      numeric characters, underscores, and dashes. * All characters must use
      UTF-8 encoding, and international characters are allowed. * Keys must
      start with a lowercase letter or international character. * Each
      resource is limited to a maximum of 64 labels. Both keys and values are
      additionally constrained to be <= 128 bytes.
    multiTarget: Optional. Information specifying a multiTarget.
    name: Optional. Name of the `Target`. Format is
      projects/{project}/locations/{location}/targets/a-z{0,62}.
    requireApproval: Optional. Whether or not the `Target` requires approval.
    run: Optional. Information specifying a Cloud Run deployment target.
    targetId: Output only. Resource id of the `Target`.
    uid: Output only. Unique identifier of the `Target`.
    updateTime: Output only. Most recent time at which the `Target` was
      updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Optional. User annotations. These attributes can only be set and used
    by the user, and not by Cloud Deploy. See
    https://google.aip.dev/128#annotations for more details such as format and
    size limitations.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DeployParametersValue(_messages.Message):
    r"""Optional. The deploy parameters to use for this target.

    Messages:
      AdditionalProperty: An additional property for a DeployParametersValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        DeployParametersValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DeployParametersValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Labels are attributes that can be set and used by both the
    user and by Cloud Deploy. Labels must meet the following constraints: *
    Keys and values can contain only lowercase letters, numeric characters,
    underscores, and dashes. * All characters must use UTF-8 encoding, and
    international characters are allowed. * Keys must start with a lowercase
    letter or international character. * Each resource is limited to a maximum
    of 64 labels. Both keys and values are additionally constrained to be <=
    128 bytes.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  anthosCluster = _messages.MessageField('AnthosCluster', 2)
  createTime = _messages.StringField(3)
  customTarget = _messages.MessageField('CustomTarget', 4)
  deployParameters = _messages.MessageField('DeployParametersValue', 5)
  description = _messages.StringField(6)
  etag = _messages.StringField(7)
  executionConfigs = _messages.MessageField('ExecutionConfig', 8, repeated=True)
  gke = _messages.MessageField('GkeCluster', 9)
  labels = _messages.MessageField('LabelsValue', 10)
  multiTarget = _messages.MessageField('MultiTarget', 11)
  name = _messages.StringField(12)
  requireApproval = _messages.BooleanField(13)
  run = _messages.MessageField('CloudRunLocation', 14)
  targetId = _messages.StringField(15)
  uid = _messages.StringField(16)
  updateTime = _messages.StringField(17)


class TargetArtifact(_messages.Message):
  r"""The artifacts produced by a target render operation.

  Messages:
    PhaseArtifactsValue: Output only. Map from the phase ID to the phase
      artifacts for the `Target`.

  Fields:
    artifactUri: Output only. URI of a directory containing the artifacts.
      This contains deployment configuration used by Skaffold during a
      rollout, and all paths are relative to this location.
    manifestPath: Output only. File path of the rendered manifest relative to
      the URI.
    phaseArtifacts: Output only. Map from the phase ID to the phase artifacts
      for the `Target`.
    skaffoldConfigPath: Output only. File path of the resolved Skaffold
      configuration relative to the URI.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PhaseArtifactsValue(_messages.Message):
    r"""Output only. Map from the phase ID to the phase artifacts for the
    `Target`.

    Messages:
      AdditionalProperty: An additional property for a PhaseArtifactsValue
        object.

    Fields:
      additionalProperties: Additional properties of type PhaseArtifactsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PhaseArtifactsValue object.

      Fields:
        key: Name of the additional property.
        value: A PhaseArtifact attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('PhaseArtifact', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  artifactUri = _messages.StringField(1)
  manifestPath = _messages.StringField(2)
  phaseArtifacts = _messages.MessageField('PhaseArtifactsValue', 3)
  skaffoldConfigPath = _messages.StringField(4)


class TargetAttribute(_messages.Message):
  r"""Contains criteria for selecting Targets. Attributes provided must match
  the target resource in order for policy restrictions to apply. E.g. if id
  "prod" and labels "foo: bar" are given the target resource must match both
  that id and have that label in order to be selected.

  Messages:
    LabelsValue: Target labels.

  Fields:
    id: ID of the `Target`. The value of this field could be one of the
      following: * The last segment of a target name. It only needs the ID to
      determine which target is being referred to * "*", all targets in a
      location.
    labels: Target labels.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Target labels.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  id = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)


class TargetNotificationEvent(_messages.Message):
  r"""Payload proto for "clouddeploy.googleapis.com/target_notification"
  Platform Log event that describes the failure to send target status change
  Pub/Sub notification.

  Enums:
    TypeValueValuesEnum: Type of this notification, e.g. for a Pub/Sub
      failure.

  Fields:
    message: Debug message for when a notification fails to send.
    target: The name of the `Target`.
    type: Type of this notification, e.g. for a Pub/Sub failure.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Type of this notification, e.g. for a Pub/Sub failure.

    Values:
      TYPE_UNSPECIFIED: Type is unspecified.
      TYPE_PUBSUB_NOTIFICATION_FAILURE: A Pub/Sub notification failed to be
        sent.
      TYPE_RESOURCE_STATE_CHANGE: Resource state changed.
      TYPE_PROCESS_ABORTED: A process aborted.
      TYPE_RESTRICTION_VIOLATED: Restriction check failed.
      TYPE_RESOURCE_DELETED: Resource deleted.
      TYPE_RENDER_STATUES_CHANGE: Deprecated: This field is never used. Use
        release_render log type instead.
    """
    TYPE_UNSPECIFIED = 0
    TYPE_PUBSUB_NOTIFICATION_FAILURE = 1
    TYPE_RESOURCE_STATE_CHANGE = 2
    TYPE_PROCESS_ABORTED = 3
    TYPE_RESTRICTION_VIOLATED = 4
    TYPE_RESOURCE_DELETED = 5
    TYPE_RENDER_STATUES_CHANGE = 6

  message = _messages.StringField(1)
  target = _messages.StringField(2)
  type = _messages.EnumField('TypeValueValuesEnum', 3)


class TargetRender(_messages.Message):
  r"""Details of rendering for a single target.

  Enums:
    FailureCauseValueValuesEnum: Output only. Reason this render failed. This
      will always be unspecified while the render in progress.
    RenderingStateValueValuesEnum: Output only. Current state of the render
      operation for this Target.

  Fields:
    failureCause: Output only. Reason this render failed. This will always be
      unspecified while the render in progress.
    failureMessage: Output only. Additional information about the render
      failure, if available.
    metadata: Output only. Metadata related to the `Release` render for this
      Target.
    renderingBuild: Output only. The resource name of the Cloud Build `Build`
      object that is used to render the manifest for this target. Format is
      `projects/{project}/locations/{location}/builds/{build}`.
    renderingState: Output only. Current state of the render operation for
      this Target.
  """

  class FailureCauseValueValuesEnum(_messages.Enum):
    r"""Output only. Reason this render failed. This will always be
    unspecified while the render in progress.

    Values:
      FAILURE_CAUSE_UNSPECIFIED: No reason for failure is specified.
      CLOUD_BUILD_UNAVAILABLE: Cloud Build is not available, either because it
        is not enabled or because Cloud Deploy has insufficient permissions.
        See [required permission](https://cloud.google.com/deploy/docs/cloud-
        deploy-service-account#required_permissions).
      EXECUTION_FAILED: The render operation did not complete successfully;
        check Cloud Build logs.
      CLOUD_BUILD_REQUEST_FAILED: Cloud Build failed to fulfill Cloud Deploy's
        request. See failure_message for additional details.
      CUSTOM_ACTION_NOT_FOUND: The render operation did not complete
        successfully because the custom action required for predeploy or
        postdeploy was not found in the skaffold configuration. See
        failure_message for additional details.
    """
    FAILURE_CAUSE_UNSPECIFIED = 0
    CLOUD_BUILD_UNAVAILABLE = 1
    EXECUTION_FAILED = 2
    CLOUD_BUILD_REQUEST_FAILED = 3
    CUSTOM_ACTION_NOT_FOUND = 4

  class RenderingStateValueValuesEnum(_messages.Enum):
    r"""Output only. Current state of the render operation for this Target.

    Values:
      TARGET_RENDER_STATE_UNSPECIFIED: The render operation state is
        unspecified.
      SUCCEEDED: The render operation has completed successfully.
      FAILED: The render operation has failed.
      IN_PROGRESS: The render operation is in progress.
    """
    TARGET_RENDER_STATE_UNSPECIFIED = 0
    SUCCEEDED = 1
    FAILED = 2
    IN_PROGRESS = 3

  failureCause = _messages.EnumField('FailureCauseValueValuesEnum', 1)
  failureMessage = _messages.StringField(2)
  metadata = _messages.MessageField('RenderMetadata', 3)
  renderingBuild = _messages.StringField(4)
  renderingState = _messages.EnumField('RenderingStateValueValuesEnum', 5)


class TargetsPresentCondition(_messages.Message):
  r"""TargetsPresentCondition contains information on any Targets defined in
  the Delivery Pipeline that do not actually exist.

  Fields:
    missingTargets: The list of Target names that do not exist. For example,
      projects/{project_id}/locations/{location_name}/targets/{target_name}.
    status: True if there aren't any missing Targets.
    updateTime: Last time the condition was updated.
  """

  missingTargets = _messages.StringField(1, repeated=True)
  status = _messages.BooleanField(2)
  updateTime = _messages.StringField(3)


class TargetsTypeCondition(_messages.Message):
  r"""TargetsTypeCondition contains information on whether the Targets defined
  in the Delivery Pipeline are of the same type.

  Fields:
    errorDetails: Human readable error message.
    status: True if the targets are all a comparable type. For example this is
      true if all targets are GKE clusters. This is false if some targets are
      Cloud Run targets and others are GKE clusters.
  """

  errorDetails = _messages.StringField(1)
  status = _messages.BooleanField(2)


class TerminateJobRunRequest(_messages.Message):
  r"""The request object used by `TerminateJobRun`.

  Fields:
    overrideDeployPolicy: Optional. Deploy policies to override. Format is
      projects/{project}/ locations/{location}/deployPolicies/a-z{0,62}.
  """

  overrideDeployPolicy = _messages.StringField(1, repeated=True)


class TerminateJobRunResponse(_messages.Message):
  r"""The response object from `TerminateJobRun`."""


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TimeOfDay(_messages.Message):
  r"""Represents a time of day. The date and time zone are either not
  significant or are specified elsewhere. An API may choose to allow leap
  seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.

  Fields:
    hours: Hours of day in 24 hour format. Should be from 0 to 23. An API may
      choose to allow the value "24:00:00" for scenarios like business closing
      time.
    minutes: Minutes of hour of day. Must be from 0 to 59.
    nanos: Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999.
    seconds: Seconds of minutes of the time. Must normally be from 0 to 59. An
      API may allow the value 60 if it allows leap-seconds.
  """

  hours = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  minutes = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  nanos = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  seconds = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class TimeWindow(_messages.Message):
  r"""Time Window within which actions are restricted.

  Fields:
    ranges: Range within which actions are restricted.
    timeZone: The time zone in IANA format [IANA Time Zone
      Database](https://www.iana.org/time-zones) (e.g. America/New_York).
  """

  ranges = _messages.MessageField('Range', 1, repeated=True)
  timeZone = _messages.StringField(2)


class VerifyJob(_messages.Message):
  r"""A verify Job."""


class VerifyJobRun(_messages.Message):
  r"""VerifyJobRun contains information specific to a verify `JobRun`.

  Enums:
    FailureCauseValueValuesEnum: Output only. The reason the verify failed.
      This will always be unspecified while the verify is in progress or if it
      succeeded.

  Fields:
    artifactUri: Output only. URI of a directory containing the verify
      artifacts. This contains the Skaffold event log.
    build: Output only. The resource name of the Cloud Build `Build` object
      that is used to verify. Format is
      projects/{project}/locations/{location}/builds/{build}.
    eventLogPath: Output only. File path of the Skaffold event log relative to
      the artifact URI.
    failureCause: Output only. The reason the verify failed. This will always
      be unspecified while the verify is in progress or if it succeeded.
    failureMessage: Output only. Additional information about the verify
      failure, if available.
  """

  class FailureCauseValueValuesEnum(_messages.Enum):
    r"""Output only. The reason the verify failed. This will always be
    unspecified while the verify is in progress or if it succeeded.

    Values:
      FAILURE_CAUSE_UNSPECIFIED: No reason for failure is specified.
      CLOUD_BUILD_UNAVAILABLE: Cloud Build is not available, either because it
        is not enabled or because Cloud Deploy has insufficient permissions.
        See [required permission](https://cloud.google.com/deploy/docs/cloud-
        deploy-service-account#required_permissions).
      EXECUTION_FAILED: The verify operation did not complete successfully;
        check Cloud Build logs.
      DEADLINE_EXCEEDED: The verify job run did not complete within the
        alloted time.
      VERIFICATION_CONFIG_NOT_FOUND: No Skaffold verify configuration was
        found.
      CLOUD_BUILD_REQUEST_FAILED: Cloud Build failed to fulfill Cloud Deploy's
        request. See failure_message for additional details.
    """
    FAILURE_CAUSE_UNSPECIFIED = 0
    CLOUD_BUILD_UNAVAILABLE = 1
    EXECUTION_FAILED = 2
    DEADLINE_EXCEEDED = 3
    VERIFICATION_CONFIG_NOT_FOUND = 4
    CLOUD_BUILD_REQUEST_FAILED = 5

  artifactUri = _messages.StringField(1)
  build = _messages.StringField(2)
  eventLogPath = _messages.StringField(3)
  failureCause = _messages.EnumField('FailureCauseValueValuesEnum', 4)
  failureMessage = _messages.StringField(5)


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

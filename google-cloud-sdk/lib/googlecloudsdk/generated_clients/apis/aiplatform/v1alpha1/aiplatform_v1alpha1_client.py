"""Generated client library for aiplatform version v1alpha1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.aiplatform.v1alpha1 import aiplatform_v1alpha1_messages as messages


class AiplatformV1alpha1(base_api.BaseApiClient):
  """Generated client library for service aiplatform version v1alpha1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://aiplatform.googleapis.com/'
  MTLS_BASE_URL = 'https://aiplatform.mtls.googleapis.com/'

  _PACKAGE = 'aiplatform'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/cloud-platform.read-only']
  _VERSION = 'v1alpha1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'AiplatformV1alpha1'
  _URL_VERSION = 'v1alpha1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new aiplatform handle."""
    url = url or self.BASE_URL
    super(AiplatformV1alpha1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_batchPredictionJobs = self.ProjectsLocationsBatchPredictionJobsService(self)
    self.projects_locations_customJobs_operations = self.ProjectsLocationsCustomJobsOperationsService(self)
    self.projects_locations_customJobs = self.ProjectsLocationsCustomJobsService(self)
    self.projects_locations_dataLabelingJobs_operations = self.ProjectsLocationsDataLabelingJobsOperationsService(self)
    self.projects_locations_dataLabelingJobs = self.ProjectsLocationsDataLabelingJobsService(self)
    self.projects_locations_datasets_annotationSpecs_operations = self.ProjectsLocationsDatasetsAnnotationSpecsOperationsService(self)
    self.projects_locations_datasets_annotationSpecs = self.ProjectsLocationsDatasetsAnnotationSpecsService(self)
    self.projects_locations_datasets_dataItems_annotations_operations = self.ProjectsLocationsDatasetsDataItemsAnnotationsOperationsService(self)
    self.projects_locations_datasets_dataItems_annotations = self.ProjectsLocationsDatasetsDataItemsAnnotationsService(self)
    self.projects_locations_datasets_dataItems_operations = self.ProjectsLocationsDatasetsDataItemsOperationsService(self)
    self.projects_locations_datasets_dataItems = self.ProjectsLocationsDatasetsDataItemsService(self)
    self.projects_locations_datasets_operations = self.ProjectsLocationsDatasetsOperationsService(self)
    self.projects_locations_datasets_savedQueries_operations = self.ProjectsLocationsDatasetsSavedQueriesOperationsService(self)
    self.projects_locations_datasets_savedQueries = self.ProjectsLocationsDatasetsSavedQueriesService(self)
    self.projects_locations_datasets = self.ProjectsLocationsDatasetsService(self)
    self.projects_locations_endpoints_operations = self.ProjectsLocationsEndpointsOperationsService(self)
    self.projects_locations_endpoints = self.ProjectsLocationsEndpointsService(self)
    self.projects_locations_featurestores_entityTypes_features_operations = self.ProjectsLocationsFeaturestoresEntityTypesFeaturesOperationsService(self)
    self.projects_locations_featurestores_entityTypes_features = self.ProjectsLocationsFeaturestoresEntityTypesFeaturesService(self)
    self.projects_locations_featurestores_entityTypes_operations = self.ProjectsLocationsFeaturestoresEntityTypesOperationsService(self)
    self.projects_locations_featurestores_entityTypes = self.ProjectsLocationsFeaturestoresEntityTypesService(self)
    self.projects_locations_featurestores_operations = self.ProjectsLocationsFeaturestoresOperationsService(self)
    self.projects_locations_featurestores = self.ProjectsLocationsFeaturestoresService(self)
    self.projects_locations_hyperparameterTuningJobs_operations = self.ProjectsLocationsHyperparameterTuningJobsOperationsService(self)
    self.projects_locations_hyperparameterTuningJobs = self.ProjectsLocationsHyperparameterTuningJobsService(self)
    self.projects_locations_indexEndpoints_operations = self.ProjectsLocationsIndexEndpointsOperationsService(self)
    self.projects_locations_indexEndpoints = self.ProjectsLocationsIndexEndpointsService(self)
    self.projects_locations_indexes_operations = self.ProjectsLocationsIndexesOperationsService(self)
    self.projects_locations_indexes = self.ProjectsLocationsIndexesService(self)
    self.projects_locations_metadataStores_artifacts = self.ProjectsLocationsMetadataStoresArtifactsService(self)
    self.projects_locations_metadataStores_contexts = self.ProjectsLocationsMetadataStoresContextsService(self)
    self.projects_locations_metadataStores_executions = self.ProjectsLocationsMetadataStoresExecutionsService(self)
    self.projects_locations_metadataStores = self.ProjectsLocationsMetadataStoresService(self)
    self.projects_locations_migratableResources_operations = self.ProjectsLocationsMigratableResourcesOperationsService(self)
    self.projects_locations_migratableResources = self.ProjectsLocationsMigratableResourcesService(self)
    self.projects_locations_modelDeploymentMonitoringJobs_operations = self.ProjectsLocationsModelDeploymentMonitoringJobsOperationsService(self)
    self.projects_locations_modelDeploymentMonitoringJobs = self.ProjectsLocationsModelDeploymentMonitoringJobsService(self)
    self.projects_locations_models_evaluations_operations = self.ProjectsLocationsModelsEvaluationsOperationsService(self)
    self.projects_locations_models_evaluations_slices = self.ProjectsLocationsModelsEvaluationsSlicesService(self)
    self.projects_locations_models_evaluations = self.ProjectsLocationsModelsEvaluationsService(self)
    self.projects_locations_models_operations = self.ProjectsLocationsModelsOperationsService(self)
    self.projects_locations_models = self.ProjectsLocationsModelsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_pipelineJobs_operations = self.ProjectsLocationsPipelineJobsOperationsService(self)
    self.projects_locations_pipelineJobs = self.ProjectsLocationsPipelineJobsService(self)
    self.projects_locations_publishers_models = self.ProjectsLocationsPublishersModelsService(self)
    self.projects_locations_publishers = self.ProjectsLocationsPublishersService(self)
    self.projects_locations_specialistPools_operations = self.ProjectsLocationsSpecialistPoolsOperationsService(self)
    self.projects_locations_specialistPools = self.ProjectsLocationsSpecialistPoolsService(self)
    self.projects_locations_studies_operations = self.ProjectsLocationsStudiesOperationsService(self)
    self.projects_locations_studies_trials_operations = self.ProjectsLocationsStudiesTrialsOperationsService(self)
    self.projects_locations_studies_trials = self.ProjectsLocationsStudiesTrialsService(self)
    self.projects_locations_studies = self.ProjectsLocationsStudiesService(self)
    self.projects_locations_tensorboards_experiments_operations = self.ProjectsLocationsTensorboardsExperimentsOperationsService(self)
    self.projects_locations_tensorboards_experiments_runs_operations = self.ProjectsLocationsTensorboardsExperimentsRunsOperationsService(self)
    self.projects_locations_tensorboards_experiments_runs_timeSeries_operations = self.ProjectsLocationsTensorboardsExperimentsRunsTimeSeriesOperationsService(self)
    self.projects_locations_tensorboards_experiments_runs_timeSeries = self.ProjectsLocationsTensorboardsExperimentsRunsTimeSeriesService(self)
    self.projects_locations_tensorboards_experiments_runs = self.ProjectsLocationsTensorboardsExperimentsRunsService(self)
    self.projects_locations_tensorboards_experiments = self.ProjectsLocationsTensorboardsExperimentsService(self)
    self.projects_locations_tensorboards_operations = self.ProjectsLocationsTensorboardsOperationsService(self)
    self.projects_locations_tensorboards = self.ProjectsLocationsTensorboardsService(self)
    self.projects_locations_trainingPipelines_operations = self.ProjectsLocationsTrainingPipelinesOperationsService(self)
    self.projects_locations_trainingPipelines = self.ProjectsLocationsTrainingPipelinesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsBatchPredictionJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_batchPredictionJobs resource."""

    _NAME = 'projects_locations_batchPredictionJobs'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsBatchPredictionJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a BatchPredictionJob. Starts asynchronous cancellation on the BatchPredictionJob. The server makes the best effort to cancel the job, but success is not guaranteed. Clients can use JobService.GetBatchPredictionJob or other methods to check whether the cancellation succeeded or whether the job completed despite cancellation. On a successful cancellation, the BatchPredictionJob is not deleted;instead its BatchPredictionJob.state is set to `CANCELLED`. Any files already outputted by the job are not deleted.

      Args:
        request: (AiplatformProjectsLocationsBatchPredictionJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/batchPredictionJobs/{batchPredictionJobsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.batchPredictionJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='googleCloudAiplatformV1alpha1CancelBatchPredictionJobRequest',
        request_type_name='AiplatformProjectsLocationsBatchPredictionJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a BatchPredictionJob. A BatchPredictionJob once created will right away be attempted to start.

      Args:
        request: (AiplatformProjectsLocationsBatchPredictionJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1BatchPredictionJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/batchPredictionJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.batchPredictionJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/batchPredictionJobs',
        request_field='googleCloudAiplatformV1alpha1BatchPredictionJob',
        request_type_name='AiplatformProjectsLocationsBatchPredictionJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1BatchPredictionJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a BatchPredictionJob. Can only be called on jobs that already finished.

      Args:
        request: (AiplatformProjectsLocationsBatchPredictionJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/batchPredictionJobs/{batchPredictionJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.batchPredictionJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsBatchPredictionJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a BatchPredictionJob.

      Args:
        request: (AiplatformProjectsLocationsBatchPredictionJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1BatchPredictionJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/batchPredictionJobs/{batchPredictionJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.batchPredictionJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsBatchPredictionJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1BatchPredictionJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists BatchPredictionJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsBatchPredictionJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListBatchPredictionJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/batchPredictionJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.batchPredictionJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/batchPredictionJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsBatchPredictionJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListBatchPredictionJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsCustomJobsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_customJobs_operations resource."""

    _NAME = 'projects_locations_customJobs_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsCustomJobsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/customJobs/{customJobsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.customJobs.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsCustomJobsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/customJobs/{customJobsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.customJobs.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsCustomJobsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/customJobs/{customJobsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.customJobs.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsCustomJobsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/customJobs/{customJobsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.customJobs.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsCustomJobsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/customJobs/{customJobsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.customJobs.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsCustomJobsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsCustomJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_customJobs resource."""

    _NAME = 'projects_locations_customJobs'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsCustomJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a CustomJob. Starts asynchronous cancellation on the CustomJob. The server makes a best effort to cancel the job, but success is not guaranteed. Clients can use JobService.GetCustomJob or other methods to check whether the cancellation succeeded or whether the job completed despite cancellation. On successful cancellation, the CustomJob is not deleted; instead it becomes a job with a CustomJob.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`, and CustomJob.state is set to `CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/customJobs/{customJobsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.customJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='googleCloudAiplatformV1alpha1CancelCustomJobRequest',
        request_type_name='AiplatformProjectsLocationsCustomJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a CustomJob. A created CustomJob right away will be attempted to be run.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1CustomJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/customJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.customJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/customJobs',
        request_field='googleCloudAiplatformV1alpha1CustomJob',
        request_type_name='AiplatformProjectsLocationsCustomJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1CustomJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a CustomJob.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/customJobs/{customJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.customJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsCustomJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a CustomJob.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1CustomJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/customJobs/{customJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.customJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsCustomJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1CustomJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists CustomJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListCustomJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/customJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.customJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/customJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsCustomJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListCustomJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDataLabelingJobsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_dataLabelingJobs_operations resource."""

    _NAME = 'projects_locations_dataLabelingJobs_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsDataLabelingJobsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsDataLabelingJobsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/dataLabelingJobs/{dataLabelingJobsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.dataLabelingJobs.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDataLabelingJobsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsDataLabelingJobsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/dataLabelingJobs/{dataLabelingJobsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.dataLabelingJobs.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDataLabelingJobsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsDataLabelingJobsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/dataLabelingJobs/{dataLabelingJobsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.dataLabelingJobs.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDataLabelingJobsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsDataLabelingJobsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/dataLabelingJobs/{dataLabelingJobsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.dataLabelingJobs.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDataLabelingJobsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsDataLabelingJobsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/dataLabelingJobs/{dataLabelingJobsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.dataLabelingJobs.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDataLabelingJobsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsDataLabelingJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_dataLabelingJobs resource."""

    _NAME = 'projects_locations_dataLabelingJobs'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsDataLabelingJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a DataLabelingJob. Success of cancellation is not guaranteed.

      Args:
        request: (AiplatformProjectsLocationsDataLabelingJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/dataLabelingJobs/{dataLabelingJobsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.dataLabelingJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='googleCloudAiplatformV1alpha1CancelDataLabelingJobRequest',
        request_type_name='AiplatformProjectsLocationsDataLabelingJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a DataLabelingJob.

      Args:
        request: (AiplatformProjectsLocationsDataLabelingJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1DataLabelingJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/dataLabelingJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.dataLabelingJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/dataLabelingJobs',
        request_field='googleCloudAiplatformV1alpha1DataLabelingJob',
        request_type_name='AiplatformProjectsLocationsDataLabelingJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1DataLabelingJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a DataLabelingJob.

      Args:
        request: (AiplatformProjectsLocationsDataLabelingJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/dataLabelingJobs/{dataLabelingJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.dataLabelingJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDataLabelingJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a DataLabelingJob.

      Args:
        request: (AiplatformProjectsLocationsDataLabelingJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1DataLabelingJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/dataLabelingJobs/{dataLabelingJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.dataLabelingJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDataLabelingJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1DataLabelingJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists DataLabelingJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsDataLabelingJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListDataLabelingJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/dataLabelingJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.dataLabelingJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/dataLabelingJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDataLabelingJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListDataLabelingJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsAnnotationSpecsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_annotationSpecs_operations resource."""

    _NAME = 'projects_locations_datasets_annotationSpecs_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsDatasetsAnnotationSpecsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsDatasetsAnnotationSpecsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationSpecs/{annotationSpecsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.annotationSpecs.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsAnnotationSpecsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsDatasetsAnnotationSpecsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationSpecs/{annotationSpecsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.datasets.annotationSpecs.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsAnnotationSpecsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsDatasetsAnnotationSpecsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationSpecs/{annotationSpecsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.annotationSpecs.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsAnnotationSpecsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsDatasetsAnnotationSpecsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationSpecs/{annotationSpecsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.annotationSpecs.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsAnnotationSpecsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsDatasetsAnnotationSpecsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationSpecs/{annotationSpecsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.annotationSpecs.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsAnnotationSpecsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsAnnotationSpecsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_annotationSpecs resource."""

    _NAME = 'projects_locations_datasets_annotationSpecs'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsDatasetsAnnotationSpecsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets an AnnotationSpec.

      Args:
        request: (AiplatformProjectsLocationsDatasetsAnnotationSpecsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1AnnotationSpec) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationSpecs/{annotationSpecsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.annotationSpecs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['readMask'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsAnnotationSpecsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1AnnotationSpec',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsDataItemsAnnotationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_dataItems_annotations_operations resource."""

    _NAME = 'projects_locations_datasets_dataItems_annotations_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsDatasetsDataItemsAnnotationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDataItemsAnnotationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataItems/{dataItemsId}/annotations/{annotationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.dataItems.annotations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDataItemsAnnotationsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDataItemsAnnotationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataItems/{dataItemsId}/annotations/{annotationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.datasets.dataItems.annotations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDataItemsAnnotationsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDataItemsAnnotationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataItems/{dataItemsId}/annotations/{annotationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.dataItems.annotations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDataItemsAnnotationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDataItemsAnnotationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataItems/{dataItemsId}/annotations/{annotationsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.dataItems.annotations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDataItemsAnnotationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDataItemsAnnotationsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataItems/{dataItemsId}/annotations/{annotationsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.dataItems.annotations.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDataItemsAnnotationsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsDataItemsAnnotationsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_dataItems_annotations resource."""

    _NAME = 'projects_locations_datasets_dataItems_annotations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsDatasetsDataItemsAnnotationsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists Annotations belongs to a dataitem.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDataItemsAnnotationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListAnnotationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataItems/{dataItemsId}/annotations',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.dataItems.annotations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/annotations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDataItemsAnnotationsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListAnnotationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsDataItemsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_dataItems_operations resource."""

    _NAME = 'projects_locations_datasets_dataItems_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsDatasetsDataItemsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDataItemsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataItems/{dataItemsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.dataItems.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDataItemsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDataItemsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataItems/{dataItemsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.datasets.dataItems.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDataItemsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDataItemsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataItems/{dataItemsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.dataItems.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDataItemsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDataItemsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataItems/{dataItemsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.dataItems.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDataItemsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDataItemsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataItems/{dataItemsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.dataItems.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDataItemsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsDataItemsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_dataItems resource."""

    _NAME = 'projects_locations_datasets_dataItems'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsDatasetsDataItemsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists DataItems in a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDataItemsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListDataItemsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataItems',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.dataItems.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/dataItems',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDataItemsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListDataItemsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_operations resource."""

    _NAME = 'projects_locations_datasets_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsDatasetsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsDatasetsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsDatasetsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.datasets.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsDatasetsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsDatasetsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsDatasetsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsSavedQueriesOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_savedQueries_operations resource."""

    _NAME = 'projects_locations_datasets_savedQueries_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsDatasetsSavedQueriesOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsDatasetsSavedQueriesOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/savedQueries/{savedQueriesId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.savedQueries.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsSavedQueriesOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsDatasetsSavedQueriesOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/savedQueries/{savedQueriesId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.datasets.savedQueries.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsSavedQueriesOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsDatasetsSavedQueriesOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/savedQueries/{savedQueriesId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.savedQueries.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsSavedQueriesOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsDatasetsSavedQueriesOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/savedQueries/{savedQueriesId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.savedQueries.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsSavedQueriesOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsDatasetsSavedQueriesOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/savedQueries/{savedQueriesId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.savedQueries.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsSavedQueriesOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsSavedQueriesService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_savedQueries resource."""

    _NAME = 'projects_locations_datasets_savedQueries'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsDatasetsSavedQueriesService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsDatasetsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets resource."""

    _NAME = 'projects_locations_datasets'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsDatasetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/datasets',
        request_field='googleCloudAiplatformV1alpha1Dataset',
        request_type_name='AiplatformProjectsLocationsDatasetsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.datasets.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Exports data from a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:export',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:export',
        request_field='googleCloudAiplatformV1alpha1ExportDataRequest',
        request_type_name='AiplatformProjectsLocationsDatasetsExportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Dataset) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['readMask'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Dataset',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Imports data into a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:import',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.import',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:import',
        request_field='googleCloudAiplatformV1alpha1ImportDataRequest',
        request_type_name='AiplatformProjectsLocationsDatasetsImportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Datasets in a Location.

      Args:
        request: (AiplatformProjectsLocationsDatasetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListDatasetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/datasets',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListDatasetsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Dataset) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.datasets.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='googleCloudAiplatformV1alpha1Dataset',
        request_type_name='AiplatformProjectsLocationsDatasetsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Dataset',
        supports_download=False,
    )

  class ProjectsLocationsEndpointsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_endpoints_operations resource."""

    _NAME = 'projects_locations_endpoints_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsEndpointsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsEndpointsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsEndpointsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsEndpointsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.endpoints.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsEndpointsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsEndpointsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.endpoints.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsEndpointsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsEndpointsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.endpoints.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsEndpointsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsEndpointsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsEndpointsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsEndpointsService(base_api.BaseApiService):
    """Service class for the projects_locations_endpoints resource."""

    _NAME = 'projects_locations_endpoints'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsEndpointsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an Endpoint.

      Args:
        request: (AiplatformProjectsLocationsEndpointsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpoints',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/endpoints',
        request_field='googleCloudAiplatformV1alpha1Endpoint',
        request_type_name='AiplatformProjectsLocationsEndpointsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an Endpoint.

      Args:
        request: (AiplatformProjectsLocationsEndpointsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.endpoints.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsEndpointsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def DeployModel(self, request, global_params=None):
      r"""Deploys a Model into this Endpoint, creating a DeployedModel within it.

      Args:
        request: (AiplatformProjectsLocationsEndpointsDeployModelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('DeployModel')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeployModel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:deployModel',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.deployModel',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1alpha1/{+endpoint}:deployModel',
        request_field='googleCloudAiplatformV1alpha1DeployModelRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsDeployModelRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Explain(self, request, global_params=None):
      r"""Perform an online explanation. If deployed_model_id is specified, the corresponding DeployModel must have explanation_spec populated. If deployed_model_id is not specified, all DeployedModels must have explanation_spec populated.

      Args:
        request: (AiplatformProjectsLocationsEndpointsExplainRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ExplainResponse) The response message.
      """
      config = self.GetMethodConfig('Explain')
      return self._RunMethod(
          config, request, global_params=global_params)

    Explain.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:explain',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.explain',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1alpha1/{+endpoint}:explain',
        request_field='googleCloudAiplatformV1alpha1ExplainRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsExplainRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ExplainResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an Endpoint.

      Args:
        request: (AiplatformProjectsLocationsEndpointsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Endpoint) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.endpoints.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsEndpointsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Endpoint',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Endpoints in a Location.

      Args:
        request: (AiplatformProjectsLocationsEndpointsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListEndpointsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpoints',
        http_method='GET',
        method_id='aiplatform.projects.locations.endpoints.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/endpoints',
        request_field='',
        request_type_name='AiplatformProjectsLocationsEndpointsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListEndpointsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an Endpoint.

      Args:
        request: (AiplatformProjectsLocationsEndpointsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Endpoint) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.endpoints.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='googleCloudAiplatformV1alpha1Endpoint',
        request_type_name='AiplatformProjectsLocationsEndpointsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Endpoint',
        supports_download=False,
    )

    def Predict(self, request, global_params=None):
      r"""Perform an online prediction.

      Args:
        request: (AiplatformProjectsLocationsEndpointsPredictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1PredictResponse) The response message.
      """
      config = self.GetMethodConfig('Predict')
      return self._RunMethod(
          config, request, global_params=global_params)

    Predict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:predict',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.predict',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1alpha1/{+endpoint}:predict',
        request_field='googleCloudAiplatformV1alpha1PredictRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsPredictRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1PredictResponse',
        supports_download=False,
    )

    def RawPredict(self, request, global_params=None):
      r"""Perform an online prediction with an arbitrary HTTP payload. The response includes the following HTTP headers: * `X-Vertex-AI-Endpoint-Id`: ID of the Endpoint that served this prediction. * `X-Vertex-AI-Deployed-Model-Id`: ID of the Endpoint's DeployedModel that served this prediction.

      Args:
        request: (AiplatformProjectsLocationsEndpointsRawPredictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('RawPredict')
      return self._RunMethod(
          config, request, global_params=global_params)

    RawPredict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:rawPredict',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.rawPredict',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1alpha1/{+endpoint}:rawPredict',
        request_field='googleCloudAiplatformV1alpha1RawPredictRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsRawPredictRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

    def UndeployModel(self, request, global_params=None):
      r"""Undeploys a Model from an Endpoint, removing a DeployedModel from it, and freeing all resources it's using.

      Args:
        request: (AiplatformProjectsLocationsEndpointsUndeployModelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('UndeployModel')
      return self._RunMethod(
          config, request, global_params=global_params)

    UndeployModel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:undeployModel',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.undeployModel',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1alpha1/{+endpoint}:undeployModel',
        request_field='googleCloudAiplatformV1alpha1UndeployModelRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsUndeployModelRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsFeaturestoresEntityTypesFeaturesOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_featurestores_entityTypes_features_operations resource."""

    _NAME = 'projects_locations_featurestores_entityTypes_features_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsFeaturestoresEntityTypesFeaturesOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/features/{featuresId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.features.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/features/{featuresId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.features.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/features/{featuresId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.features.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/features/{featuresId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.features.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/features/{featuresId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.features.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsFeaturestoresEntityTypesFeaturesService(base_api.BaseApiService):
    """Service class for the projects_locations_featurestores_entityTypes_features resource."""

    _NAME = 'projects_locations_featurestores_entityTypes_features'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsFeaturestoresEntityTypesFeaturesService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsFeaturestoresEntityTypesOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_featurestores_entityTypes_operations resource."""

    _NAME = 'projects_locations_featurestores_entityTypes_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsFeaturestoresEntityTypesOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsFeaturestoresEntityTypesService(base_api.BaseApiService):
    """Service class for the projects_locations_featurestores_entityTypes resource."""

    _NAME = 'projects_locations_featurestores_entityTypes'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsFeaturestoresEntityTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def ReadFeatureValues(self, request, global_params=None):
      r"""Reads Feature values of a specific entity of an EntityType. For reading feature values of multiple entities of an EntityType, please use StreamingReadFeatureValues.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesReadFeatureValuesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ReadFeatureValuesResponse) The response message.
      """
      config = self.GetMethodConfig('ReadFeatureValues')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReadFeatureValues.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:readFeatureValues',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.readFeatureValues',
        ordered_params=['entityType'],
        path_params=['entityType'],
        query_params=[],
        relative_path='v1alpha1/{+entityType}:readFeatureValues',
        request_field='googleCloudAiplatformV1alpha1ReadFeatureValuesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesReadFeatureValuesRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ReadFeatureValuesResponse',
        supports_download=False,
    )

    def StreamingReadFeatureValues(self, request, global_params=None):
      r"""Reads Feature values for multiple entities. Depending on their size, data for different entities may be broken up across multiple responses.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesStreamingReadFeatureValuesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ReadFeatureValuesResponse) The response message.
      """
      config = self.GetMethodConfig('StreamingReadFeatureValues')
      return self._RunMethod(
          config, request, global_params=global_params)

    StreamingReadFeatureValues.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:streamingReadFeatureValues',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.streamingReadFeatureValues',
        ordered_params=['entityType'],
        path_params=['entityType'],
        query_params=[],
        relative_path='v1alpha1/{+entityType}:streamingReadFeatureValues',
        request_field='googleCloudAiplatformV1alpha1StreamingReadFeatureValuesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesStreamingReadFeatureValuesRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ReadFeatureValuesResponse',
        supports_download=False,
    )

  class ProjectsLocationsFeaturestoresOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_featurestores_operations resource."""

    _NAME = 'projects_locations_featurestores_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsFeaturestoresOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.featurestores.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsFeaturestoresService(base_api.BaseApiService):
    """Service class for the projects_locations_featurestores resource."""

    _NAME = 'projects_locations_featurestores'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsFeaturestoresService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsHyperparameterTuningJobsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_hyperparameterTuningJobs_operations resource."""

    _NAME = 'projects_locations_hyperparameterTuningJobs_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsHyperparameterTuningJobsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs/{hyperparameterTuningJobsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs/{hyperparameterTuningJobsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs/{hyperparameterTuningJobsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs/{hyperparameterTuningJobsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs/{hyperparameterTuningJobsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsHyperparameterTuningJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_hyperparameterTuningJobs resource."""

    _NAME = 'projects_locations_hyperparameterTuningJobs'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsHyperparameterTuningJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a HyperparameterTuningJob. Starts asynchronous cancellation on the HyperparameterTuningJob. The server makes a best effort to cancel the job, but success is not guaranteed. Clients can use JobService.GetHyperparameterTuningJob or other methods to check whether the cancellation succeeded or whether the job completed despite cancellation. On successful cancellation, the HyperparameterTuningJob is not deleted; instead it becomes a job with a HyperparameterTuningJob.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`, and HyperparameterTuningJob.state is set to `CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs/{hyperparameterTuningJobsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='googleCloudAiplatformV1alpha1CancelHyperparameterTuningJobRequest',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a HyperparameterTuningJob.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1HyperparameterTuningJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/hyperparameterTuningJobs',
        request_field='googleCloudAiplatformV1alpha1HyperparameterTuningJob',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1HyperparameterTuningJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a HyperparameterTuningJob.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs/{hyperparameterTuningJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a HyperparameterTuningJob.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1HyperparameterTuningJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs/{hyperparameterTuningJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1HyperparameterTuningJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists HyperparameterTuningJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListHyperparameterTuningJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/hyperparameterTuningJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListHyperparameterTuningJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsIndexEndpointsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_indexEndpoints_operations resource."""

    _NAME = 'projects_locations_indexEndpoints_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsIndexEndpointsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexEndpoints.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.indexEndpoints.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.indexEndpoints.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.indexEndpoints.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexEndpoints.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsIndexEndpointsService(base_api.BaseApiService):
    """Service class for the projects_locations_indexEndpoints resource."""

    _NAME = 'projects_locations_indexEndpoints'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsIndexEndpointsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an IndexEndpoint.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexEndpoints',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexEndpoints.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/indexEndpoints',
        request_field='googleCloudAiplatformV1alpha1IndexEndpoint',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an IndexEndpoint.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.indexEndpoints.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def DeployIndex(self, request, global_params=None):
      r"""Deploys an Index into this IndexEndpoint, creating a DeployedIndex within it. Only non-empty Indexes can be deployed.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsDeployIndexRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('DeployIndex')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeployIndex.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}:deployIndex',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexEndpoints.deployIndex',
        ordered_params=['indexEndpoint'],
        path_params=['indexEndpoint'],
        query_params=[],
        relative_path='v1alpha1/{+indexEndpoint}:deployIndex',
        request_field='googleCloudAiplatformV1alpha1DeployIndexRequest',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsDeployIndexRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an IndexEndpoint.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1IndexEndpoint) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.indexEndpoints.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1IndexEndpoint',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists IndexEndpoints in a Location.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListIndexEndpointsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexEndpoints',
        http_method='GET',
        method_id='aiplatform.projects.locations.indexEndpoints.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/indexEndpoints',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListIndexEndpointsResponse',
        supports_download=False,
    )

    def MutateDeployedIndex(self, request, global_params=None):
      r"""Update an existing DeployedIndex under an IndexEndpoint.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsMutateDeployedIndexRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('MutateDeployedIndex')
      return self._RunMethod(
          config, request, global_params=global_params)

    MutateDeployedIndex.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}:mutateDeployedIndex',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexEndpoints.mutateDeployedIndex',
        ordered_params=['indexEndpoint'],
        path_params=['indexEndpoint'],
        query_params=[],
        relative_path='v1alpha1/{+indexEndpoint}:mutateDeployedIndex',
        request_field='googleCloudAiplatformV1alpha1DeployedIndex',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsMutateDeployedIndexRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an IndexEndpoint.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1IndexEndpoint) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.indexEndpoints.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='googleCloudAiplatformV1alpha1IndexEndpoint',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1IndexEndpoint',
        supports_download=False,
    )

    def UndeployIndex(self, request, global_params=None):
      r"""Undeploys an Index from an IndexEndpoint, removing a DeployedIndex from it, and freeing all resources it's using.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsUndeployIndexRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('UndeployIndex')
      return self._RunMethod(
          config, request, global_params=global_params)

    UndeployIndex.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}:undeployIndex',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexEndpoints.undeployIndex',
        ordered_params=['indexEndpoint'],
        path_params=['indexEndpoint'],
        query_params=[],
        relative_path='v1alpha1/{+indexEndpoint}:undeployIndex',
        request_field='googleCloudAiplatformV1alpha1UndeployIndexRequest',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsUndeployIndexRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsIndexesOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_indexes_operations resource."""

    _NAME = 'projects_locations_indexes_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsIndexesOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsIndexesOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexes.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexesOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsIndexesOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.indexes.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexesOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsIndexesOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.indexes.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexesOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsIndexesOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.indexes.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexesOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsIndexesOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexes.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexesOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsIndexesService(base_api.BaseApiService):
    """Service class for the projects_locations_indexes resource."""

    _NAME = 'projects_locations_indexes'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsIndexesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an Index.

      Args:
        request: (AiplatformProjectsLocationsIndexesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexes',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/indexes',
        request_field='googleCloudAiplatformV1alpha1Index',
        request_type_name='AiplatformProjectsLocationsIndexesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an Index. An Index can only be deleted when all its DeployedIndexes had been undeployed.

      Args:
        request: (AiplatformProjectsLocationsIndexesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.indexes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an Index.

      Args:
        request: (AiplatformProjectsLocationsIndexesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Index) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.indexes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexesGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Index',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Indexes in a Location.

      Args:
        request: (AiplatformProjectsLocationsIndexesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListIndexesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexes',
        http_method='GET',
        method_id='aiplatform.projects.locations.indexes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/indexes',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexesListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListIndexesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an Index.

      Args:
        request: (AiplatformProjectsLocationsIndexesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.indexes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='googleCloudAiplatformV1alpha1Index',
        request_type_name='AiplatformProjectsLocationsIndexesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def RemoveDatapoints(self, request, global_params=None):
      r"""Remove Datapoints from an Index.

      Args:
        request: (AiplatformProjectsLocationsIndexesRemoveDatapointsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1RemoveDatapointsResponse) The response message.
      """
      config = self.GetMethodConfig('RemoveDatapoints')
      return self._RunMethod(
          config, request, global_params=global_params)

    RemoveDatapoints.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}:removeDatapoints',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexes.removeDatapoints',
        ordered_params=['index'],
        path_params=['index'],
        query_params=[],
        relative_path='v1alpha1/{+index}:removeDatapoints',
        request_field='googleCloudAiplatformV1alpha1RemoveDatapointsRequest',
        request_type_name='AiplatformProjectsLocationsIndexesRemoveDatapointsRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1RemoveDatapointsResponse',
        supports_download=False,
    )

    def UpsertDatapoints(self, request, global_params=None):
      r"""Add/update Datapoints into an Index.

      Args:
        request: (AiplatformProjectsLocationsIndexesUpsertDatapointsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1UpsertDatapointsResponse) The response message.
      """
      config = self.GetMethodConfig('UpsertDatapoints')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpsertDatapoints.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}:upsertDatapoints',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexes.upsertDatapoints',
        ordered_params=['index'],
        path_params=['index'],
        query_params=[],
        relative_path='v1alpha1/{+index}:upsertDatapoints',
        request_field='googleCloudAiplatformV1alpha1UpsertDatapointsRequest',
        request_type_name='AiplatformProjectsLocationsIndexesUpsertDatapointsRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1UpsertDatapointsResponse',
        supports_download=False,
    )

  class ProjectsLocationsMetadataStoresArtifactsService(base_api.BaseApiService):
    """Service class for the projects_locations_metadataStores_artifacts resource."""

    _NAME = 'projects_locations_metadataStores_artifacts'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsMetadataStoresArtifactsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an Artifact associated with a MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Artifact) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['artifactId'],
        relative_path='v1alpha1/{+parent}/artifacts',
        request_field='googleCloudAiplatformV1alpha1Artifact',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Artifact',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a specific Artifact.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Artifact) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts/{artifactsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Artifact',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Artifacts in the MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListArtifactsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/artifacts',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListArtifactsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a stored Artifact.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Artifact) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts/{artifactsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='googleCloudAiplatformV1alpha1Artifact',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Artifact',
        supports_download=False,
    )

  class ProjectsLocationsMetadataStoresContextsService(base_api.BaseApiService):
    """Service class for the projects_locations_metadataStores_contexts resource."""

    _NAME = 'projects_locations_metadataStores_contexts'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsMetadataStoresContextsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddContextArtifactsAndExecutions(self, request, global_params=None):
      r"""Adds a set of Artifacts and Executions to a Context. If any of the Artifacts or Executions have already been added to a Context, they are simply skipped.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsAddContextArtifactsAndExecutionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1AddContextArtifactsAndExecutionsResponse) The response message.
      """
      config = self.GetMethodConfig('AddContextArtifactsAndExecutions')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddContextArtifactsAndExecutions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}:addContextArtifactsAndExecutions',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.contexts.addContextArtifactsAndExecutions',
        ordered_params=['context'],
        path_params=['context'],
        query_params=[],
        relative_path='v1alpha1/{+context}:addContextArtifactsAndExecutions',
        request_field='googleCloudAiplatformV1alpha1AddContextArtifactsAndExecutionsRequest',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsAddContextArtifactsAndExecutionsRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1AddContextArtifactsAndExecutionsResponse',
        supports_download=False,
    )

    def AddContextChildren(self, request, global_params=None):
      r"""Adds a set of Contexts as children to a parent Context. If any of the child Contexts have already been added to the parent Context, they are simply skipped. If this call would create a cycle or cause any Context to have more than 10 parents, the request will fail with an INVALID_ARGUMENT error.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsAddContextChildrenRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1AddContextChildrenResponse) The response message.
      """
      config = self.GetMethodConfig('AddContextChildren')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddContextChildren.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}:addContextChildren',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.contexts.addContextChildren',
        ordered_params=['context'],
        path_params=['context'],
        query_params=[],
        relative_path='v1alpha1/{+context}:addContextChildren',
        request_field='googleCloudAiplatformV1alpha1AddContextChildrenRequest',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsAddContextChildrenRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1AddContextChildrenResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a Context associated with a MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Context) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.contexts.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['contextId'],
        relative_path='v1alpha1/{+parent}/contexts',
        request_field='googleCloudAiplatformV1alpha1Context',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Context',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a specific Context.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Context) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.contexts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Context',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Contexts on the MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListContextsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.contexts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/contexts',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListContextsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a stored Context.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Context) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.metadataStores.contexts.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='googleCloudAiplatformV1alpha1Context',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Context',
        supports_download=False,
    )

    def QueryContextLineageSubgraph(self, request, global_params=None):
      r"""Retrieves Artifacts and Executions within the specified Context, connected by Event edges and returned as a LineageSubgraph.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsQueryContextLineageSubgraphRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1LineageSubgraph) The response message.
      """
      config = self.GetMethodConfig('QueryContextLineageSubgraph')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryContextLineageSubgraph.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}:queryContextLineageSubgraph',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.contexts.queryContextLineageSubgraph',
        ordered_params=['context'],
        path_params=['context'],
        query_params=[],
        relative_path='v1alpha1/{+context}:queryContextLineageSubgraph',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsQueryContextLineageSubgraphRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1LineageSubgraph',
        supports_download=False,
    )

  class ProjectsLocationsMetadataStoresExecutionsService(base_api.BaseApiService):
    """Service class for the projects_locations_metadataStores_executions resource."""

    _NAME = 'projects_locations_metadataStores_executions'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsMetadataStoresExecutionsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddExecutionEvents(self, request, global_params=None):
      r"""Adds Events to the specified Execution. An Event indicates whether an Artifact was used as an input or output for an Execution. If an Event already exists between the Execution and the Artifact, the Event is skipped.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsAddExecutionEventsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1AddExecutionEventsResponse) The response message.
      """
      config = self.GetMethodConfig('AddExecutionEvents')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddExecutionEvents.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions/{executionsId}:addExecutionEvents',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.executions.addExecutionEvents',
        ordered_params=['execution'],
        path_params=['execution'],
        query_params=[],
        relative_path='v1alpha1/{+execution}:addExecutionEvents',
        request_field='googleCloudAiplatformV1alpha1AddExecutionEventsRequest',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsAddExecutionEventsRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1AddExecutionEventsResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates an Execution associated with a MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Execution) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.executions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['executionId'],
        relative_path='v1alpha1/{+parent}/executions',
        request_field='googleCloudAiplatformV1alpha1Execution',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Execution',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a specific Execution.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Execution) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions/{executionsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.executions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Execution',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Executions in the MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListExecutionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.executions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/executions',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListExecutionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a stored Execution.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Execution) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions/{executionsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.metadataStores.executions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='googleCloudAiplatformV1alpha1Execution',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Execution',
        supports_download=False,
    )

    def QueryExecutionInputsAndOutputs(self, request, global_params=None):
      r"""Obtains the set of input and output Artifacts for this Execution, in the form of LineageSubgraph that also contains the Execution and connecting Events.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsQueryExecutionInputsAndOutputsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1LineageSubgraph) The response message.
      """
      config = self.GetMethodConfig('QueryExecutionInputsAndOutputs')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryExecutionInputsAndOutputs.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions/{executionsId}:queryExecutionInputsAndOutputs',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.executions.queryExecutionInputsAndOutputs',
        ordered_params=['execution'],
        path_params=['execution'],
        query_params=[],
        relative_path='v1alpha1/{+execution}:queryExecutionInputsAndOutputs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsQueryExecutionInputsAndOutputsRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1LineageSubgraph',
        supports_download=False,
    )

  class ProjectsLocationsMetadataStoresService(base_api.BaseApiService):
    """Service class for the projects_locations_metadataStores resource."""

    _NAME = 'projects_locations_metadataStores'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsMetadataStoresService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Initializes a MetadataStore, including allocation of resources.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['metadataStoreId'],
        relative_path='v1alpha1/{+parent}/metadataStores',
        request_field='googleCloudAiplatformV1alpha1MetadataStore',
        request_type_name='AiplatformProjectsLocationsMetadataStoresCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single MetadataStore and all its child resources (Artifacts, Executions, and Contexts).

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.metadataStores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a specific MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1MetadataStore) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1MetadataStore',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists MetadataStores for a Location.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListMetadataStoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/metadataStores',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/metadataStores',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListMetadataStoresResponse',
        supports_download=False,
    )

  class ProjectsLocationsMigratableResourcesOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_migratableResources_operations resource."""

    _NAME = 'projects_locations_migratableResources_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsMigratableResourcesOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsMigratableResourcesOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/migratableResources/{migratableResourcesId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.migratableResources.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMigratableResourcesOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsMigratableResourcesOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/migratableResources/{migratableResourcesId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.migratableResources.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMigratableResourcesOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsMigratableResourcesOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/migratableResources/{migratableResourcesId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.migratableResources.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMigratableResourcesOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsMigratableResourcesOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/migratableResources/{migratableResourcesId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.migratableResources.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMigratableResourcesOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsMigratableResourcesOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/migratableResources/{migratableResourcesId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.migratableResources.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMigratableResourcesOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsMigratableResourcesService(base_api.BaseApiService):
    """Service class for the projects_locations_migratableResources resource."""

    _NAME = 'projects_locations_migratableResources'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsMigratableResourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchMigrate(self, request, global_params=None):
      r"""Batch migrates resources from ml.googleapis.com, automl.googleapis.com, and datalabeling.googleapis.com to Vertex AI.

      Args:
        request: (AiplatformProjectsLocationsMigratableResourcesBatchMigrateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchMigrate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchMigrate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/migratableResources:batchMigrate',
        http_method='POST',
        method_id='aiplatform.projects.locations.migratableResources.batchMigrate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/migratableResources:batchMigrate',
        request_field='googleCloudAiplatformV1alpha1BatchMigrateResourcesRequest',
        request_type_name='AiplatformProjectsLocationsMigratableResourcesBatchMigrateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Search(self, request, global_params=None):
      r"""Searches all of the resources in automl.googleapis.com, datalabeling.googleapis.com and ml.googleapis.com that can be migrated to Vertex AI's given location.

      Args:
        request: (AiplatformProjectsLocationsMigratableResourcesSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1SearchMigratableResourcesResponse) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/migratableResources:search',
        http_method='POST',
        method_id='aiplatform.projects.locations.migratableResources.search',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/migratableResources:search',
        request_field='googleCloudAiplatformV1alpha1SearchMigratableResourcesRequest',
        request_type_name='AiplatformProjectsLocationsMigratableResourcesSearchRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1SearchMigratableResourcesResponse',
        supports_download=False,
    )

  class ProjectsLocationsModelDeploymentMonitoringJobsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_modelDeploymentMonitoringJobs_operations resource."""

    _NAME = 'projects_locations_modelDeploymentMonitoringJobs_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsModelDeploymentMonitoringJobsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsModelDeploymentMonitoringJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_modelDeploymentMonitoringJobs resource."""

    _NAME = 'projects_locations_modelDeploymentMonitoringJobs'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsModelDeploymentMonitoringJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a ModelDeploymentMonitoringJob. It will run periodically on a configured interval.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ModelDeploymentMonitoringJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/modelDeploymentMonitoringJobs',
        request_field='googleCloudAiplatformV1alpha1ModelDeploymentMonitoringJob',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ModelDeploymentMonitoringJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a ModelDeploymentMonitoringJob.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a ModelDeploymentMonitoringJob.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ModelDeploymentMonitoringJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ModelDeploymentMonitoringJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ModelDeploymentMonitoringJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListModelDeploymentMonitoringJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/modelDeploymentMonitoringJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListModelDeploymentMonitoringJobsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a ModelDeploymentMonitoringJob.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='googleCloudAiplatformV1alpha1ModelDeploymentMonitoringJob',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Pause(self, request, global_params=None):
      r"""Pauses a ModelDeploymentMonitoringJob. If the job is running, the server makes a best effort to cancel the job. Will mark ModelDeploymentMonitoringJob.state to 'PAUSED'.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsPauseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Pause')
      return self._RunMethod(
          config, request, global_params=global_params)

    Pause.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}:pause',
        http_method='POST',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.pause',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:pause',
        request_field='googleCloudAiplatformV1alpha1PauseModelDeploymentMonitoringJobRequest',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsPauseRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Resume(self, request, global_params=None):
      r"""Resumes a paused ModelDeploymentMonitoringJob. It will start to run from next scheduled time. A deleted ModelDeploymentMonitoringJob can't be resumed.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsResumeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Resume')
      return self._RunMethod(
          config, request, global_params=global_params)

    Resume.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}:resume',
        http_method='POST',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.resume',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:resume',
        request_field='googleCloudAiplatformV1alpha1ResumeModelDeploymentMonitoringJobRequest',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsResumeRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def SearchModelDeploymentMonitoringStatsAnomalies(self, request, global_params=None):
      r"""Searches Model Monitoring Statistics generated within a given time window.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsSearchModelDeploymentMonitoringStatsAnomaliesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1SearchModelDeploymentMonitoringStatsAnomaliesResponse) The response message.
      """
      config = self.GetMethodConfig('SearchModelDeploymentMonitoringStatsAnomalies')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchModelDeploymentMonitoringStatsAnomalies.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}:searchModelDeploymentMonitoringStatsAnomalies',
        http_method='POST',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.searchModelDeploymentMonitoringStatsAnomalies',
        ordered_params=['modelDeploymentMonitoringJob'],
        path_params=['modelDeploymentMonitoringJob'],
        query_params=[],
        relative_path='v1alpha1/{+modelDeploymentMonitoringJob}:searchModelDeploymentMonitoringStatsAnomalies',
        request_field='googleCloudAiplatformV1alpha1SearchModelDeploymentMonitoringStatsAnomaliesRequest',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsSearchModelDeploymentMonitoringStatsAnomaliesRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1SearchModelDeploymentMonitoringStatsAnomaliesResponse',
        supports_download=False,
    )

  class ProjectsLocationsModelsEvaluationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_models_evaluations_operations resource."""

    _NAME = 'projects_locations_models_evaluations_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsModelsEvaluationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations/{evaluationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.evaluations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations/{evaluationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.models.evaluations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations/{evaluationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.evaluations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations/{evaluationsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.evaluations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations/{evaluationsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.evaluations.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsModelsEvaluationsSlicesService(base_api.BaseApiService):
    """Service class for the projects_locations_models_evaluations_slices resource."""

    _NAME = 'projects_locations_models_evaluations_slices'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsModelsEvaluationsSlicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a ModelEvaluationSlice.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsSlicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ModelEvaluationSlice) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations/{evaluationsId}/slices/{slicesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.evaluations.slices.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsSlicesGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ModelEvaluationSlice',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ModelEvaluationSlices in a ModelEvaluation.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsSlicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListModelEvaluationSlicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations/{evaluationsId}/slices',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.evaluations.slices.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/slices',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsSlicesListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListModelEvaluationSlicesResponse',
        supports_download=False,
    )

  class ProjectsLocationsModelsEvaluationsService(base_api.BaseApiService):
    """Service class for the projects_locations_models_evaluations resource."""

    _NAME = 'projects_locations_models_evaluations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsModelsEvaluationsService, self).__init__(client)
      self._upload_configs = {
          }

    def ExportEvaluatedDataItems(self, request, global_params=None):
      r"""Exports DataItems on which the Model was evaluated (i.e. which were in the TEST set of the dataset the Model was created from), together with their ground truth Annotations and the predictions predicted by the Model. The DataItems, ground truth Annotations and predictions are exported in the state they were at the moment the Model was evaluated. This export is available only for 30 days since the ModelEvaluation is created. Currently only available for AutoML tabular Models.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsExportEvaluatedDataItemsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('ExportEvaluatedDataItems')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExportEvaluatedDataItems.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations/{evaluationsId}:exportEvaluatedDataItems',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.evaluations.exportEvaluatedDataItems',
        ordered_params=['modelEvaluation'],
        path_params=['modelEvaluation'],
        query_params=[],
        relative_path='v1alpha1/{+modelEvaluation}:exportEvaluatedDataItems',
        request_field='googleCloudAiplatformV1alpha1ExportEvaluatedDataItemsRequest',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsExportEvaluatedDataItemsRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a ModelEvaluation.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ModelEvaluation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations/{evaluationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.evaluations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ModelEvaluation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ModelEvaluations in a Model.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListModelEvaluationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.evaluations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/evaluations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListModelEvaluationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsModelsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_models_operations resource."""

    _NAME = 'projects_locations_models_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsModelsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsModelsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsModelsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.models.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsModelsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsModelsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsModelsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsModelsService(base_api.BaseApiService):
    """Service class for the projects_locations_models resource."""

    _NAME = 'projects_locations_models'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsModelsService, self).__init__(client)
      self._upload_configs = {
          }

    def Delete(self, request, global_params=None):
      r"""Deletes a Model. A model cannot be deleted if any Endpoint resource has a DeployedModel based on the model in its deployed_models field.

      Args:
        request: (AiplatformProjectsLocationsModelsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.models.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Exports a trained, exportable Model to a location specified by the user. A Model is considered to be exportable if it has at least one supported export format.

      Args:
        request: (AiplatformProjectsLocationsModelsExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}:export',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:export',
        request_field='googleCloudAiplatformV1alpha1ExportModelRequest',
        request_type_name='AiplatformProjectsLocationsModelsExportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Model.

      Args:
        request: (AiplatformProjectsLocationsModelsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Model) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Model',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Models in a Location.

      Args:
        request: (AiplatformProjectsLocationsModelsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListModelsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/models',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListModelsResponse',
        supports_download=False,
    )

    def Upload(self, request, global_params=None):
      r"""Uploads a Model artifact into Vertex AI.

      Args:
        request: (AiplatformProjectsLocationsModelsUploadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Upload')
      return self._RunMethod(
          config, request, global_params=global_params)

    Upload.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/models:upload',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.upload',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/models:upload',
        request_field='googleCloudAiplatformV1alpha1UploadModelRequest',
        request_type_name='AiplatformProjectsLocationsModelsUploadRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='googleLongrunningCancelOperationRequest',
        request_type_name='AiplatformProjectsLocationsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:wait',
        request_field='googleLongrunningWaitOperationRequest',
        request_type_name='AiplatformProjectsLocationsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsPipelineJobsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_pipelineJobs_operations resource."""

    _NAME = 'projects_locations_pipelineJobs_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsPipelineJobsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/pipelineJobs/{pipelineJobsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.pipelineJobs.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsPipelineJobsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/pipelineJobs/{pipelineJobsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.pipelineJobs.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsPipelineJobsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/pipelineJobs/{pipelineJobsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.pipelineJobs.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsPipelineJobsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/pipelineJobs/{pipelineJobsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.pipelineJobs.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsPipelineJobsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/pipelineJobs/{pipelineJobsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.pipelineJobs.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsPipelineJobsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsPipelineJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_pipelineJobs resource."""

    _NAME = 'projects_locations_pipelineJobs'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsPipelineJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a PipelineJob. Starts asynchronous cancellation on the PipelineJob. The server makes a best effort to cancel the pipeline, but success is not guaranteed. Clients can use PipelineService.GetPipelineJob or other methods to check whether the cancellation succeeded or whether the pipeline completed despite cancellation. On successful cancellation, the PipelineJob is not deleted; instead it becomes a pipeline with a PipelineJob.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`, and PipelineJob.state is set to `CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/pipelineJobs/{pipelineJobsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.pipelineJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='googleCloudAiplatformV1alpha1CancelPipelineJobRequest',
        request_type_name='AiplatformProjectsLocationsPipelineJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a PipelineJob. A PipelineJob will run immediately when created.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1PipelineJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/pipelineJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.pipelineJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pipelineJobId'],
        relative_path='v1alpha1/{+parent}/pipelineJobs',
        request_field='googleCloudAiplatformV1alpha1PipelineJob',
        request_type_name='AiplatformProjectsLocationsPipelineJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1PipelineJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a PipelineJob.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/pipelineJobs/{pipelineJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.pipelineJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsPipelineJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a PipelineJob.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1PipelineJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/pipelineJobs/{pipelineJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.pipelineJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsPipelineJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1PipelineJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists PipelineJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListPipelineJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/pipelineJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.pipelineJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/pipelineJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsPipelineJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListPipelineJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsPublishersModelsService(base_api.BaseApiService):
    """Service class for the projects_locations_publishers_models resource."""

    _NAME = 'projects_locations_publishers_models'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsPublishersModelsService, self).__init__(client)
      self._upload_configs = {
          }

    def Predict(self, request, global_params=None):
      r"""Perform an online prediction.

      Args:
        request: (AiplatformProjectsLocationsPublishersModelsPredictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1PredictResponse) The response message.
      """
      config = self.GetMethodConfig('Predict')
      return self._RunMethod(
          config, request, global_params=global_params)

    Predict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/publishers/{publishersId}/models/{modelsId}:predict',
        http_method='POST',
        method_id='aiplatform.projects.locations.publishers.models.predict',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1alpha1/{+endpoint}:predict',
        request_field='googleCloudAiplatformV1alpha1PredictRequest',
        request_type_name='AiplatformProjectsLocationsPublishersModelsPredictRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1PredictResponse',
        supports_download=False,
    )

    def RawPredict(self, request, global_params=None):
      r"""Perform an online prediction with an arbitrary HTTP payload. The response includes the following HTTP headers: * `X-Vertex-AI-Endpoint-Id`: ID of the Endpoint that served this prediction. * `X-Vertex-AI-Deployed-Model-Id`: ID of the Endpoint's DeployedModel that served this prediction.

      Args:
        request: (AiplatformProjectsLocationsPublishersModelsRawPredictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('RawPredict')
      return self._RunMethod(
          config, request, global_params=global_params)

    RawPredict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/publishers/{publishersId}/models/{modelsId}:rawPredict',
        http_method='POST',
        method_id='aiplatform.projects.locations.publishers.models.rawPredict',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1alpha1/{+endpoint}:rawPredict',
        request_field='googleCloudAiplatformV1alpha1RawPredictRequest',
        request_type_name='AiplatformProjectsLocationsPublishersModelsRawPredictRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

  class ProjectsLocationsPublishersService(base_api.BaseApiService):
    """Service class for the projects_locations_publishers resource."""

    _NAME = 'projects_locations_publishers'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsPublishersService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsLocationsSpecialistPoolsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_specialistPools_operations resource."""

    _NAME = 'projects_locations_specialistPools_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsSpecialistPoolsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/specialistPools/{specialistPoolsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.specialistPools.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/specialistPools/{specialistPoolsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.specialistPools.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/specialistPools/{specialistPoolsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.specialistPools.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/specialistPools/{specialistPoolsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.specialistPools.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/specialistPools/{specialistPoolsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.specialistPools.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsSpecialistPoolsService(base_api.BaseApiService):
    """Service class for the projects_locations_specialistPools resource."""

    _NAME = 'projects_locations_specialistPools'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsSpecialistPoolsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a SpecialistPool.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/specialistPools',
        http_method='POST',
        method_id='aiplatform.projects.locations.specialistPools.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/specialistPools',
        request_field='googleCloudAiplatformV1alpha1SpecialistPool',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a SpecialistPool as well as all Specialists in the pool.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/specialistPools/{specialistPoolsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.specialistPools.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a SpecialistPool.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1SpecialistPool) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/specialistPools/{specialistPoolsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.specialistPools.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1SpecialistPool',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SpecialistPools in a Location.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListSpecialistPoolsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/specialistPools',
        http_method='GET',
        method_id='aiplatform.projects.locations.specialistPools.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/specialistPools',
        request_field='',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListSpecialistPoolsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a SpecialistPool.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/specialistPools/{specialistPoolsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.specialistPools.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='googleCloudAiplatformV1alpha1SpecialistPool',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsStudiesOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_studies_operations resource."""

    _NAME = 'projects_locations_studies_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsStudiesOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsStudiesOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsStudiesOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.studies.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsStudiesOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.studies.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsStudiesOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.studies.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsStudiesOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsStudiesTrialsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_studies_trials_operations resource."""

    _NAME = 'projects_locations_studies_trials_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsStudiesTrialsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.studies.trials.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.studies.trials.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.studies.trials.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsStudiesTrialsService(base_api.BaseApiService):
    """Service class for the projects_locations_studies_trials resource."""

    _NAME = 'projects_locations_studies_trials'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsStudiesTrialsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddTrialMeasurement(self, request, global_params=None):
      r"""Adds a measurement of the objective metrics to a Trial. This measurement is assumed to have been taken before the Trial is complete.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsAddTrialMeasurementRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Trial) The response message.
      """
      config = self.GetMethodConfig('AddTrialMeasurement')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddTrialMeasurement.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}:addTrialMeasurement',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.addTrialMeasurement',
        ordered_params=['trialName'],
        path_params=['trialName'],
        query_params=[],
        relative_path='v1alpha1/{+trialName}:addTrialMeasurement',
        request_field='googleCloudAiplatformV1alpha1AddTrialMeasurementRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsAddTrialMeasurementRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Trial',
        supports_download=False,
    )

    def CheckTrialEarlyStoppingState(self, request, global_params=None):
      r"""Checks whether a Trial should stop or not. Returns a long-running operation. When the operation is successful, it will contain a CheckTrialEarlyStoppingStateResponse.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsCheckTrialEarlyStoppingStateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('CheckTrialEarlyStoppingState')
      return self._RunMethod(
          config, request, global_params=global_params)

    CheckTrialEarlyStoppingState.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}:checkTrialEarlyStoppingState',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.checkTrialEarlyStoppingState',
        ordered_params=['trialName'],
        path_params=['trialName'],
        query_params=[],
        relative_path='v1alpha1/{+trialName}:checkTrialEarlyStoppingState',
        request_field='googleCloudAiplatformV1alpha1CheckTrialEarlyStoppingStateRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsCheckTrialEarlyStoppingStateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Complete(self, request, global_params=None):
      r"""Marks a Trial as complete.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsCompleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Trial) The response message.
      """
      config = self.GetMethodConfig('Complete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Complete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}:complete',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.complete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:complete',
        request_field='googleCloudAiplatformV1alpha1CompleteTrialRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsCompleteRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Trial',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Adds a user provided Trial to a Study.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Trial) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/trials',
        request_field='googleCloudAiplatformV1alpha1Trial',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Trial',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Trial.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.studies.trials.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Trial.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Trial) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.studies.trials.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Trial',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Trials associated with a Study.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListTrialsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials',
        http_method='GET',
        method_id='aiplatform.projects.locations.studies.trials.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/trials',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListTrialsResponse',
        supports_download=False,
    )

    def ListOptimalTrials(self, request, global_params=None):
      r"""Lists the pareto-optimal Trials for multi-objective Study or the optimal Trials for single-objective Study. The definition of pareto-optimal can be checked in wiki page. https://en.wikipedia.org/wiki/Pareto_efficiency.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsListOptimalTrialsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListOptimalTrialsResponse) The response message.
      """
      config = self.GetMethodConfig('ListOptimalTrials')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListOptimalTrials.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials:listOptimalTrials',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.listOptimalTrials',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/trials:listOptimalTrials',
        request_field='googleCloudAiplatformV1alpha1ListOptimalTrialsRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsListOptimalTrialsRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListOptimalTrialsResponse',
        supports_download=False,
    )

    def Stop(self, request, global_params=None):
      r"""Stops a Trial.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsStopRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Trial) The response message.
      """
      config = self.GetMethodConfig('Stop')
      return self._RunMethod(
          config, request, global_params=global_params)

    Stop.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}:stop',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.stop',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:stop',
        request_field='googleCloudAiplatformV1alpha1StopTrialRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsStopRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Trial',
        supports_download=False,
    )

    def Suggest(self, request, global_params=None):
      r"""Adds one or more Trials to a Study, with parameter values suggested by Vertex AI Vizier. Returns a long-running operation associated with the generation of Trial suggestions. When this long-running operation succeeds, it will contain a SuggestTrialsResponse.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsSuggestRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Suggest')
      return self._RunMethod(
          config, request, global_params=global_params)

    Suggest.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials:suggest',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.suggest',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/trials:suggest',
        request_field='googleCloudAiplatformV1alpha1SuggestTrialsRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsSuggestRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsStudiesService(base_api.BaseApiService):
    """Service class for the projects_locations_studies resource."""

    _NAME = 'projects_locations_studies'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsStudiesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Study. A resource name will be generated after creation of the Study.

      Args:
        request: (AiplatformProjectsLocationsStudiesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Study) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/studies',
        request_field='googleCloudAiplatformV1alpha1Study',
        request_type_name='AiplatformProjectsLocationsStudiesCreateRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Study',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Study.

      Args:
        request: (AiplatformProjectsLocationsStudiesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.studies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Study by name.

      Args:
        request: (AiplatformProjectsLocationsStudiesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Study) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.studies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Study',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the studies in a region for an associated project.

      Args:
        request: (AiplatformProjectsLocationsStudiesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListStudiesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies',
        http_method='GET',
        method_id='aiplatform.projects.locations.studies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/studies',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListStudiesResponse',
        supports_download=False,
    )

    def Lookup(self, request, global_params=None):
      r"""Looks a study up using the user-defined display_name field instead of the fully qualified resource name.

      Args:
        request: (AiplatformProjectsLocationsStudiesLookupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Study) The response message.
      """
      config = self.GetMethodConfig('Lookup')
      return self._RunMethod(
          config, request, global_params=global_params)

    Lookup.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/studies:lookup',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.lookup',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/studies:lookup',
        request_field='googleCloudAiplatformV1alpha1LookupStudyRequest',
        request_type_name='AiplatformProjectsLocationsStudiesLookupRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Study',
        supports_download=False,
    )

  class ProjectsLocationsTensorboardsExperimentsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_tensorboards_experiments_operations resource."""

    _NAME = 'projects_locations_tensorboards_experiments_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsTensorboardsExperimentsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.tensorboards.experiments.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsTensorboardsExperimentsRunsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_tensorboards_experiments_runs_operations resource."""

    _NAME = 'projects_locations_tensorboards_experiments_runs_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsTensorboardsExperimentsRunsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsTensorboardsExperimentsRunsTimeSeriesOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_tensorboards_experiments_runs_timeSeries_operations resource."""

    _NAME = 'projects_locations_tensorboards_experiments_runs_timeSeries_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsTensorboardsExperimentsRunsTimeSeriesOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsTensorboardsExperimentsRunsTimeSeriesService(base_api.BaseApiService):
    """Service class for the projects_locations_tensorboards_experiments_runs_timeSeries resource."""

    _NAME = 'projects_locations_tensorboards_experiments_runs_timeSeries'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsTensorboardsExperimentsRunsTimeSeriesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Batch create TensorboardTimeSeries that belong to a TensorboardExperiment.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1BatchCreateTensorboardTimeSeriesResponse) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries:batchCreate',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.batchCreate',
        ordered_params=['parent', 'runsId'],
        path_params=['parent', 'runsId'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/runs/{runsId}/timeSeries:batchCreate',
        request_field='googleCloudAiplatformV1alpha1BatchCreateTensorboardTimeSeriesRequest',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesBatchCreateRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1BatchCreateTensorboardTimeSeriesResponse',
        supports_download=False,
    )

    def BatchRead(self, request, global_params=None):
      r"""Reads multiple TensorboardTimeSeries' data. The data point number limit is 1000 for scalars, 100 for tensors and blob references. If the number of data points stored is less than the limit, all data is returned. Otherwise, the number limit of data points is randomly selected from this time series and returned.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesBatchReadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1BatchReadTensorboardTimeSeriesDataResponse) The response message.
      """
      config = self.GetMethodConfig('BatchRead')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchRead.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries:batchRead',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.batchRead',
        ordered_params=['tensorboard', 'experimentsId', 'runsId'],
        path_params=['experimentsId', 'runsId', 'tensorboard'],
        query_params=['timeSeries'],
        relative_path='v1alpha1/{+tensorboard}/experiments/{experimentsId}/runs/{runsId}/timeSeries:batchRead',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesBatchReadRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1BatchReadTensorboardTimeSeriesDataResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a TensorboardTimeSeries.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1TensorboardTimeSeries) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['tensorboardTimeSeriesId'],
        relative_path='v1alpha1/{+parent}/timeSeries',
        request_field='googleCloudAiplatformV1alpha1TensorboardTimeSeries',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesCreateRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1TensorboardTimeSeries',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a TensorboardTimeSeries.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def ExportTensorboardTimeSeries(self, request, global_params=None):
      r"""Exports a TensorboardTimeSeries' data. Data is returned in paginated responses.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesExportTensorboardTimeSeriesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ExportTensorboardTimeSeriesDataResponse) The response message.
      """
      config = self.GetMethodConfig('ExportTensorboardTimeSeries')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExportTensorboardTimeSeries.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}:exportTensorboardTimeSeries',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.exportTensorboardTimeSeries',
        ordered_params=['tensorboardTimeSeries'],
        path_params=['tensorboardTimeSeries'],
        query_params=[],
        relative_path='v1alpha1/{+tensorboardTimeSeries}:exportTensorboardTimeSeries',
        request_field='googleCloudAiplatformV1alpha1ExportTensorboardTimeSeriesDataRequest',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesExportTensorboardTimeSeriesRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ExportTensorboardTimeSeriesDataResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a TensorboardTimeSeries.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1TensorboardTimeSeries) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1TensorboardTimeSeries',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TensorboardTimeSeries in a Location.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListTensorboardTimeSeriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/timeSeries',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListTensorboardTimeSeriesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a TensorboardTimeSeries.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1TensorboardTimeSeries) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='googleCloudAiplatformV1alpha1TensorboardTimeSeries',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesPatchRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1TensorboardTimeSeries',
        supports_download=False,
    )

    def Read(self, request, global_params=None):
      r"""Reads a TensorboardTimeSeries' data. By default, if the number of data points stored is less than 1000, all data is returned. Otherwise, 1000 data points is randomly selected from this time series and returned. This value can be changed by changing max_data_points, which can't be greater than 10k.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesReadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ReadTensorboardTimeSeriesDataResponse) The response message.
      """
      config = self.GetMethodConfig('Read')
      return self._RunMethod(
          config, request, global_params=global_params)

    Read.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}:read',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.read',
        ordered_params=['tensorboardTimeSeries'],
        path_params=['tensorboardTimeSeries'],
        query_params=['filter', 'maxDataPoints'],
        relative_path='v1alpha1/{+tensorboardTimeSeries}:read',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesReadRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ReadTensorboardTimeSeriesDataResponse',
        supports_download=False,
    )

    def ReadBlobData(self, request, global_params=None):
      r"""Gets bytes of TensorboardBlobs. This is to allow reading blob data stored in consumer project's Cloud Storage bucket without users having to obtain Cloud Storage access permission.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesReadBlobDataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ReadTensorboardBlobDataResponse) The response message.
      """
      config = self.GetMethodConfig('ReadBlobData')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReadBlobData.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}:readBlobData',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.readBlobData',
        ordered_params=['timeSeries'],
        path_params=['timeSeries'],
        query_params=['blobIds'],
        relative_path='v1alpha1/{+timeSeries}:readBlobData',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesReadBlobDataRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ReadTensorboardBlobDataResponse',
        supports_download=False,
    )

  class ProjectsLocationsTensorboardsExperimentsRunsService(base_api.BaseApiService):
    """Service class for the projects_locations_tensorboards_experiments_runs resource."""

    _NAME = 'projects_locations_tensorboards_experiments_runs'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsTensorboardsExperimentsRunsService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Batch create TensorboardRuns.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1BatchCreateTensorboardRunsResponse) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs:batchCreate',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.batchCreate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/runs:batchCreate',
        request_field='googleCloudAiplatformV1alpha1BatchCreateTensorboardRunsRequest',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsBatchCreateRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1BatchCreateTensorboardRunsResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a TensorboardRun.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1TensorboardRun) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['tensorboardRunId'],
        relative_path='v1alpha1/{+parent}/runs',
        request_field='googleCloudAiplatformV1alpha1TensorboardRun',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1TensorboardRun',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a TensorboardRun.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a TensorboardRun.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1TensorboardRun) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1TensorboardRun',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TensorboardRuns in a Location.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListTensorboardRunsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/runs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListTensorboardRunsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a TensorboardRun.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1TensorboardRun) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='googleCloudAiplatformV1alpha1TensorboardRun',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1TensorboardRun',
        supports_download=False,
    )

    def Write(self, request, global_params=None):
      r"""Write time series data points into multiple TensorboardTimeSeries under a TensorboardRun. If any data fail to be ingested, an error is returned.

      Args:
        request: (GoogleCloudAiplatformV1alpha1WriteTensorboardRunDataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1WriteTensorboardRunDataResponse) The response message.
      """
      config = self.GetMethodConfig('Write')
      return self._RunMethod(
          config, request, global_params=global_params)

    Write.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}:write',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.write',
        ordered_params=['tensorboardRun'],
        path_params=['tensorboardRun'],
        query_params=[],
        relative_path='v1alpha1/{+tensorboardRun}:write',
        request_field='<request>',
        request_type_name='GoogleCloudAiplatformV1alpha1WriteTensorboardRunDataRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1WriteTensorboardRunDataResponse',
        supports_download=False,
    )

  class ProjectsLocationsTensorboardsExperimentsService(base_api.BaseApiService):
    """Service class for the projects_locations_tensorboards_experiments resource."""

    _NAME = 'projects_locations_tensorboards_experiments'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsTensorboardsExperimentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a TensorboardExperiment.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1TensorboardExperiment) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['tensorboardExperimentId'],
        relative_path='v1alpha1/{+parent}/experiments',
        request_field='googleCloudAiplatformV1alpha1TensorboardExperiment',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1TensorboardExperiment',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a TensorboardExperiment.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.tensorboards.experiments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a TensorboardExperiment.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1TensorboardExperiment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1TensorboardExperiment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TensorboardExperiments in a Location.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListTensorboardExperimentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/experiments',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListTensorboardExperimentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a TensorboardExperiment.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1TensorboardExperiment) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.tensorboards.experiments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='googleCloudAiplatformV1alpha1TensorboardExperiment',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1TensorboardExperiment',
        supports_download=False,
    )

    def Write(self, request, global_params=None):
      r"""Write time series data points of multiple TensorboardTimeSeries in multiple TensorboardRun's. If any data fail to be ingested, an error is returned.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsWriteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1WriteTensorboardExperimentDataResponse) The response message.
      """
      config = self.GetMethodConfig('Write')
      return self._RunMethod(
          config, request, global_params=global_params)

    Write.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}:write',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.write',
        ordered_params=['tensorboardExperiment'],
        path_params=['tensorboardExperiment'],
        query_params=[],
        relative_path='v1alpha1/{+tensorboardExperiment}:write',
        request_field='googleCloudAiplatformV1alpha1WriteTensorboardExperimentDataRequest',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsWriteRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1WriteTensorboardExperimentDataResponse',
        supports_download=False,
    )

  class ProjectsLocationsTensorboardsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_tensorboards_operations resource."""

    _NAME = 'projects_locations_tensorboards_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsTensorboardsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.tensorboards.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsTensorboardsService(base_api.BaseApiService):
    """Service class for the projects_locations_tensorboards resource."""

    _NAME = 'projects_locations_tensorboards'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsTensorboardsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Tensorboard.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/tensorboards',
        request_field='googleCloudAiplatformV1alpha1Tensorboard',
        request_type_name='AiplatformProjectsLocationsTensorboardsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Tensorboard.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.tensorboards.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Tensorboard.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1Tensorboard) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1Tensorboard',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Tensorboards in a Location.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListTensorboardsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/tensorboards',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListTensorboardsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a Tensorboard.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.tensorboards.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='googleCloudAiplatformV1alpha1Tensorboard',
        request_type_name='AiplatformProjectsLocationsTensorboardsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsTrainingPipelinesOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_trainingPipelines_operations resource."""

    _NAME = 'projects_locations_trainingPipelines_operations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsTrainingPipelinesOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/trainingPipelines/{trainingPipelinesId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.trainingPipelines.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/trainingPipelines/{trainingPipelinesId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.trainingPipelines.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/trainingPipelines/{trainingPipelinesId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.trainingPipelines.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/trainingPipelines/{trainingPipelinesId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.trainingPipelines.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/trainingPipelines/{trainingPipelinesId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.trainingPipelines.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1alpha1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsTrainingPipelinesService(base_api.BaseApiService):
    """Service class for the projects_locations_trainingPipelines resource."""

    _NAME = 'projects_locations_trainingPipelines'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsTrainingPipelinesService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a TrainingPipeline. Starts asynchronous cancellation on the TrainingPipeline. The server makes a best effort to cancel the pipeline, but success is not guaranteed. Clients can use PipelineService.GetTrainingPipeline or other methods to check whether the cancellation succeeded or whether the pipeline completed despite cancellation. On successful cancellation, the TrainingPipeline is not deleted; instead it becomes a pipeline with a TrainingPipeline.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`, and TrainingPipeline.state is set to `CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/trainingPipelines/{trainingPipelinesId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.trainingPipelines.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='googleCloudAiplatformV1alpha1CancelTrainingPipelineRequest',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a TrainingPipeline. A created TrainingPipeline right away will be attempted to be run.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1TrainingPipeline) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/trainingPipelines',
        http_method='POST',
        method_id='aiplatform.projects.locations.trainingPipelines.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1alpha1/{+parent}/trainingPipelines',
        request_field='googleCloudAiplatformV1alpha1TrainingPipeline',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesCreateRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1TrainingPipeline',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a TrainingPipeline.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/trainingPipelines/{trainingPipelinesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.trainingPipelines.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a TrainingPipeline.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1TrainingPipeline) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/trainingPipelines/{trainingPipelinesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.trainingPipelines.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesGetRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1TrainingPipeline',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TrainingPipelines in a Location.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1alpha1ListTrainingPipelinesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/trainingPipelines',
        http_method='GET',
        method_id='aiplatform.projects.locations.trainingPipelines.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1alpha1/{+parent}/trainingPipelines',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesListRequest',
        response_type_name='GoogleCloudAiplatformV1alpha1ListTrainingPipelinesResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (AiplatformProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationLocation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsGetRequest',
        response_type_name='GoogleCloudLocationLocation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (AiplatformProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudLocationListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='aiplatform.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/locations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsListRequest',
        response_type_name='GoogleCloudLocationListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(AiplatformV1alpha1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

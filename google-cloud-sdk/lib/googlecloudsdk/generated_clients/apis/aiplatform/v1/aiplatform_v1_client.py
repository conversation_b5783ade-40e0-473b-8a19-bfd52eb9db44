"""Generated client library for aiplatform version v1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.aiplatform.v1 import aiplatform_v1_messages as messages


class AiplatformV1(base_api.BaseApiClient):
  """Generated client library for service aiplatform version v1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://aiplatform.googleapis.com/'
  MTLS_BASE_URL = 'https://aiplatform.mtls.googleapis.com/'

  _PACKAGE = 'aiplatform'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform', 'https://www.googleapis.com/auth/cloud-platform.read-only']
  _VERSION = 'v1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'AiplatformV1'
  _URL_VERSION = 'v1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new aiplatform handle."""
    url = url or self.BASE_URL
    super(AiplatformV1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_batchPredictionJobs = self.ProjectsLocationsBatchPredictionJobsService(self)
    self.projects_locations_customJobs = self.ProjectsLocationsCustomJobsService(self)
    self.projects_locations_dataLabelingJobs = self.ProjectsLocationsDataLabelingJobsService(self)
    self.projects_locations_datasets_annotationSpecs = self.ProjectsLocationsDatasetsAnnotationSpecsService(self)
    self.projects_locations_datasets_dataItems_annotations = self.ProjectsLocationsDatasetsDataItemsAnnotationsService(self)
    self.projects_locations_datasets_dataItems = self.ProjectsLocationsDatasetsDataItemsService(self)
    self.projects_locations_datasets_savedQueries = self.ProjectsLocationsDatasetsSavedQueriesService(self)
    self.projects_locations_datasets = self.ProjectsLocationsDatasetsService(self)
    self.projects_locations_endpoints = self.ProjectsLocationsEndpointsService(self)
    self.projects_locations_featurestores_entityTypes_features = self.ProjectsLocationsFeaturestoresEntityTypesFeaturesService(self)
    self.projects_locations_featurestores_entityTypes = self.ProjectsLocationsFeaturestoresEntityTypesService(self)
    self.projects_locations_featurestores = self.ProjectsLocationsFeaturestoresService(self)
    self.projects_locations_hyperparameterTuningJobs = self.ProjectsLocationsHyperparameterTuningJobsService(self)
    self.projects_locations_indexEndpoints = self.ProjectsLocationsIndexEndpointsService(self)
    self.projects_locations_indexes = self.ProjectsLocationsIndexesService(self)
    self.projects_locations_metadataStores_artifacts = self.ProjectsLocationsMetadataStoresArtifactsService(self)
    self.projects_locations_metadataStores_contexts = self.ProjectsLocationsMetadataStoresContextsService(self)
    self.projects_locations_metadataStores_executions = self.ProjectsLocationsMetadataStoresExecutionsService(self)
    self.projects_locations_metadataStores_metadataSchemas = self.ProjectsLocationsMetadataStoresMetadataSchemasService(self)
    self.projects_locations_metadataStores = self.ProjectsLocationsMetadataStoresService(self)
    self.projects_locations_migratableResources = self.ProjectsLocationsMigratableResourcesService(self)
    self.projects_locations_modelDeploymentMonitoringJobs = self.ProjectsLocationsModelDeploymentMonitoringJobsService(self)
    self.projects_locations_models_evaluations_slices = self.ProjectsLocationsModelsEvaluationsSlicesService(self)
    self.projects_locations_models_evaluations = self.ProjectsLocationsModelsEvaluationsService(self)
    self.projects_locations_models = self.ProjectsLocationsModelsService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_pipelineJobs = self.ProjectsLocationsPipelineJobsService(self)
    self.projects_locations_specialistPools = self.ProjectsLocationsSpecialistPoolsService(self)
    self.projects_locations_studies_trials = self.ProjectsLocationsStudiesTrialsService(self)
    self.projects_locations_studies = self.ProjectsLocationsStudiesService(self)
    self.projects_locations_tensorboards_experiments_runs_timeSeries = self.ProjectsLocationsTensorboardsExperimentsRunsTimeSeriesService(self)
    self.projects_locations_tensorboards_experiments_runs = self.ProjectsLocationsTensorboardsExperimentsRunsService(self)
    self.projects_locations_tensorboards_experiments = self.ProjectsLocationsTensorboardsExperimentsService(self)
    self.projects_locations_tensorboards = self.ProjectsLocationsTensorboardsService(self)
    self.projects_locations_trainingPipelines = self.ProjectsLocationsTrainingPipelinesService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsBatchPredictionJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_batchPredictionJobs resource."""

    _NAME = 'projects_locations_batchPredictionJobs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsBatchPredictionJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a BatchPredictionJob. Starts asynchronous cancellation on the BatchPredictionJob. The server makes the best effort to cancel the job, but success is not guaranteed. Clients can use JobService.GetBatchPredictionJob or other methods to check whether the cancellation succeeded or whether the job completed despite cancellation. On a successful cancellation, the BatchPredictionJob is not deleted;instead its BatchPredictionJob.state is set to `CANCELLED`. Any files already outputted by the job are not deleted.

      Args:
        request: (AiplatformProjectsLocationsBatchPredictionJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/batchPredictionJobs/{batchPredictionJobsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.batchPredictionJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleCloudAiplatformV1CancelBatchPredictionJobRequest',
        request_type_name='AiplatformProjectsLocationsBatchPredictionJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a BatchPredictionJob. A BatchPredictionJob once created will right away be attempted to start.

      Args:
        request: (AiplatformProjectsLocationsBatchPredictionJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1BatchPredictionJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/batchPredictionJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.batchPredictionJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/batchPredictionJobs',
        request_field='googleCloudAiplatformV1BatchPredictionJob',
        request_type_name='AiplatformProjectsLocationsBatchPredictionJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1BatchPredictionJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a BatchPredictionJob. Can only be called on jobs that already finished.

      Args:
        request: (AiplatformProjectsLocationsBatchPredictionJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/batchPredictionJobs/{batchPredictionJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.batchPredictionJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsBatchPredictionJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a BatchPredictionJob.

      Args:
        request: (AiplatformProjectsLocationsBatchPredictionJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1BatchPredictionJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/batchPredictionJobs/{batchPredictionJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.batchPredictionJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsBatchPredictionJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1BatchPredictionJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists BatchPredictionJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsBatchPredictionJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListBatchPredictionJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/batchPredictionJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.batchPredictionJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/batchPredictionJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsBatchPredictionJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListBatchPredictionJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsCustomJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_customJobs resource."""

    _NAME = 'projects_locations_customJobs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsCustomJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a CustomJob. Starts asynchronous cancellation on the CustomJob. The server makes a best effort to cancel the job, but success is not guaranteed. Clients can use JobService.GetCustomJob or other methods to check whether the cancellation succeeded or whether the job completed despite cancellation. On successful cancellation, the CustomJob is not deleted; instead it becomes a job with a CustomJob.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`, and CustomJob.state is set to `CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/customJobs/{customJobsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.customJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleCloudAiplatformV1CancelCustomJobRequest',
        request_type_name='AiplatformProjectsLocationsCustomJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a CustomJob. A created CustomJob right away will be attempted to be run.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1CustomJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/customJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.customJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/customJobs',
        request_field='googleCloudAiplatformV1CustomJob',
        request_type_name='AiplatformProjectsLocationsCustomJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1CustomJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a CustomJob.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/customJobs/{customJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.customJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsCustomJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a CustomJob.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1CustomJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/customJobs/{customJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.customJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsCustomJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1CustomJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists CustomJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsCustomJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListCustomJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/customJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.customJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/customJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsCustomJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListCustomJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDataLabelingJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_dataLabelingJobs resource."""

    _NAME = 'projects_locations_dataLabelingJobs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsDataLabelingJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a DataLabelingJob. Success of cancellation is not guaranteed.

      Args:
        request: (AiplatformProjectsLocationsDataLabelingJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/dataLabelingJobs/{dataLabelingJobsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.dataLabelingJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleCloudAiplatformV1CancelDataLabelingJobRequest',
        request_type_name='AiplatformProjectsLocationsDataLabelingJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a DataLabelingJob.

      Args:
        request: (AiplatformProjectsLocationsDataLabelingJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1DataLabelingJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/dataLabelingJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.dataLabelingJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/dataLabelingJobs',
        request_field='googleCloudAiplatformV1DataLabelingJob',
        request_type_name='AiplatformProjectsLocationsDataLabelingJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1DataLabelingJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a DataLabelingJob.

      Args:
        request: (AiplatformProjectsLocationsDataLabelingJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/dataLabelingJobs/{dataLabelingJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.dataLabelingJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDataLabelingJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a DataLabelingJob.

      Args:
        request: (AiplatformProjectsLocationsDataLabelingJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1DataLabelingJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/dataLabelingJobs/{dataLabelingJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.dataLabelingJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDataLabelingJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1DataLabelingJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists DataLabelingJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsDataLabelingJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListDataLabelingJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/dataLabelingJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.dataLabelingJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/dataLabelingJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDataLabelingJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListDataLabelingJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsAnnotationSpecsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_annotationSpecs resource."""

    _NAME = 'projects_locations_datasets_annotationSpecs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsDatasetsAnnotationSpecsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets an AnnotationSpec.

      Args:
        request: (AiplatformProjectsLocationsDatasetsAnnotationSpecsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1AnnotationSpec) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/annotationSpecs/{annotationSpecsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.annotationSpecs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['readMask'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsAnnotationSpecsGetRequest',
        response_type_name='GoogleCloudAiplatformV1AnnotationSpec',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsDataItemsAnnotationsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_dataItems_annotations resource."""

    _NAME = 'projects_locations_datasets_dataItems_annotations'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsDatasetsDataItemsAnnotationsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists Annotations belongs to a dataitem.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDataItemsAnnotationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListAnnotationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataItems/{dataItemsId}/annotations',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.dataItems.annotations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/annotations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDataItemsAnnotationsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListAnnotationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsDataItemsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_dataItems resource."""

    _NAME = 'projects_locations_datasets_dataItems'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsDatasetsDataItemsService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists DataItems in a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDataItemsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListDataItemsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/dataItems',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.dataItems.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/dataItems',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDataItemsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListDataItemsResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsSavedQueriesService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets_savedQueries resource."""

    _NAME = 'projects_locations_datasets_savedQueries'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsDatasetsSavedQueriesService, self).__init__(client)
      self._upload_configs = {
          }

    def List(self, request, global_params=None):
      r"""Lists SavedQueries in a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsSavedQueriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListSavedQueriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}/savedQueries',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.savedQueries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/savedQueries',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsSavedQueriesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListSavedQueriesResponse',
        supports_download=False,
    )

  class ProjectsLocationsDatasetsService(base_api.BaseApiService):
    """Service class for the projects_locations_datasets resource."""

    _NAME = 'projects_locations_datasets'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsDatasetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/datasets',
        request_field='googleCloudAiplatformV1Dataset',
        request_type_name='AiplatformProjectsLocationsDatasetsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.datasets.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Exports data from a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:export',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:export',
        request_field='googleCloudAiplatformV1ExportDataRequest',
        request_type_name='AiplatformProjectsLocationsDatasetsExportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Dataset) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['readMask'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsGetRequest',
        response_type_name='GoogleCloudAiplatformV1Dataset',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Imports data into a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:import',
        http_method='POST',
        method_id='aiplatform.projects.locations.datasets.import',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:import',
        request_field='googleCloudAiplatformV1ImportDataRequest',
        request_type_name='AiplatformProjectsLocationsDatasetsImportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Datasets in a Location.

      Args:
        request: (AiplatformProjectsLocationsDatasetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListDatasetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/datasets',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListDatasetsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Dataset) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.datasets.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Dataset',
        request_type_name='AiplatformProjectsLocationsDatasetsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1Dataset',
        supports_download=False,
    )

    def SearchDataItems(self, request, global_params=None):
      r"""Searches DataItems in a Dataset.

      Args:
        request: (AiplatformProjectsLocationsDatasetsSearchDataItemsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1SearchDataItemsResponse) The response message.
      """
      config = self.GetMethodConfig('SearchDataItems')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchDataItems.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/datasets/{datasetsId}:searchDataItems',
        http_method='GET',
        method_id='aiplatform.projects.locations.datasets.searchDataItems',
        ordered_params=['dataset'],
        path_params=['dataset'],
        query_params=['annotationFilters', 'annotationsFilter', 'annotationsLimit', 'dataItemFilter', 'dataLabelingJob', 'fieldMask', 'orderBy', 'orderByAnnotation_orderBy', 'orderByAnnotation_savedQuery', 'orderByDataItem', 'pageSize', 'pageToken', 'savedQuery'],
        relative_path='v1/{+dataset}:searchDataItems',
        request_field='',
        request_type_name='AiplatformProjectsLocationsDatasetsSearchDataItemsRequest',
        response_type_name='GoogleCloudAiplatformV1SearchDataItemsResponse',
        supports_download=False,
    )

  class ProjectsLocationsEndpointsService(base_api.BaseApiService):
    """Service class for the projects_locations_endpoints resource."""

    _NAME = 'projects_locations_endpoints'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsEndpointsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an Endpoint.

      Args:
        request: (AiplatformProjectsLocationsEndpointsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['endpointId'],
        relative_path='v1/{+parent}/endpoints',
        request_field='googleCloudAiplatformV1Endpoint',
        request_type_name='AiplatformProjectsLocationsEndpointsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an Endpoint.

      Args:
        request: (AiplatformProjectsLocationsEndpointsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.endpoints.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsEndpointsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def DeployModel(self, request, global_params=None):
      r"""Deploys a Model into this Endpoint, creating a DeployedModel within it.

      Args:
        request: (AiplatformProjectsLocationsEndpointsDeployModelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('DeployModel')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeployModel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:deployModel',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.deployModel',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:deployModel',
        request_field='googleCloudAiplatformV1DeployModelRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsDeployModelRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Explain(self, request, global_params=None):
      r"""Perform an online explanation. If deployed_model_id is specified, the corresponding DeployModel must have explanation_spec populated. If deployed_model_id is not specified, all DeployedModels must have explanation_spec populated. Only deployed AutoML tabular Models have explanation_spec.

      Args:
        request: (AiplatformProjectsLocationsEndpointsExplainRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ExplainResponse) The response message.
      """
      config = self.GetMethodConfig('Explain')
      return self._RunMethod(
          config, request, global_params=global_params)

    Explain.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:explain',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.explain',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:explain',
        request_field='googleCloudAiplatformV1ExplainRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsExplainRequest',
        response_type_name='GoogleCloudAiplatformV1ExplainResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an Endpoint.

      Args:
        request: (AiplatformProjectsLocationsEndpointsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Endpoint) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.endpoints.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsEndpointsGetRequest',
        response_type_name='GoogleCloudAiplatformV1Endpoint',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Endpoints in a Location.

      Args:
        request: (AiplatformProjectsLocationsEndpointsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListEndpointsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints',
        http_method='GET',
        method_id='aiplatform.projects.locations.endpoints.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/endpoints',
        request_field='',
        request_type_name='AiplatformProjectsLocationsEndpointsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListEndpointsResponse',
        supports_download=False,
    )

    def MutateDeployedModel(self, request, global_params=None):
      r"""Updates an existing deployed model. Updatable fields include `min_replica_count`, `max_replica_count`, `autoscaling_metric_specs`, `disable_container_logging` (v1 only), and `enable_container_logging` (v1beta1 only).

      Args:
        request: (AiplatformProjectsLocationsEndpointsMutateDeployedModelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('MutateDeployedModel')
      return self._RunMethod(
          config, request, global_params=global_params)

    MutateDeployedModel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:mutateDeployedModel',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.mutateDeployedModel',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:mutateDeployedModel',
        request_field='googleCloudAiplatformV1MutateDeployedModelRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsMutateDeployedModelRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an Endpoint.

      Args:
        request: (AiplatformProjectsLocationsEndpointsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Endpoint) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.endpoints.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Endpoint',
        request_type_name='AiplatformProjectsLocationsEndpointsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1Endpoint',
        supports_download=False,
    )

    def Predict(self, request, global_params=None):
      r"""Perform an online prediction.

      Args:
        request: (AiplatformProjectsLocationsEndpointsPredictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1PredictResponse) The response message.
      """
      config = self.GetMethodConfig('Predict')
      return self._RunMethod(
          config, request, global_params=global_params)

    Predict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:predict',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.predict',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:predict',
        request_field='googleCloudAiplatformV1PredictRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsPredictRequest',
        response_type_name='GoogleCloudAiplatformV1PredictResponse',
        supports_download=False,
    )

    def RawPredict(self, request, global_params=None):
      r"""Perform an online prediction with an arbitrary HTTP payload. The response includes the following HTTP headers: * `X-Vertex-AI-Endpoint-Id`: ID of the Endpoint that served this prediction. * `X-Vertex-AI-Deployed-Model-Id`: ID of the Endpoint's DeployedModel that served this prediction.

      Args:
        request: (AiplatformProjectsLocationsEndpointsRawPredictRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleApiHttpBody) The response message.
      """
      config = self.GetMethodConfig('RawPredict')
      return self._RunMethod(
          config, request, global_params=global_params)

    RawPredict.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:rawPredict',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.rawPredict',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:rawPredict',
        request_field='googleCloudAiplatformV1RawPredictRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsRawPredictRequest',
        response_type_name='GoogleApiHttpBody',
        supports_download=False,
    )

    def UndeployModel(self, request, global_params=None):
      r"""Undeploys a Model from an Endpoint, removing a DeployedModel from it, and freeing all resources it's using.

      Args:
        request: (AiplatformProjectsLocationsEndpointsUndeployModelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('UndeployModel')
      return self._RunMethod(
          config, request, global_params=global_params)

    UndeployModel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/endpoints/{endpointsId}:undeployModel',
        http_method='POST',
        method_id='aiplatform.projects.locations.endpoints.undeployModel',
        ordered_params=['endpoint'],
        path_params=['endpoint'],
        query_params=[],
        relative_path='v1/{+endpoint}:undeployModel',
        request_field='googleCloudAiplatformV1UndeployModelRequest',
        request_type_name='AiplatformProjectsLocationsEndpointsUndeployModelRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsFeaturestoresEntityTypesFeaturesService(base_api.BaseApiService):
    """Service class for the projects_locations_featurestores_entityTypes_features resource."""

    _NAME = 'projects_locations_featurestores_entityTypes_features'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsFeaturestoresEntityTypesFeaturesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Creates a batch of Features in a given EntityType.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/features:batchCreate',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.features.batchCreate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/features:batchCreate',
        request_field='googleCloudAiplatformV1BatchCreateFeaturesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesBatchCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new Feature in a given EntityType.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/features',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.features.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['featureId'],
        relative_path='v1/{+parent}/features',
        request_field='googleCloudAiplatformV1Feature',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Feature.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/features/{featuresId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.features.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Feature.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Feature) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/features/{featuresId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.features.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesGetRequest',
        response_type_name='GoogleCloudAiplatformV1Feature',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Features in a given EntityType.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListFeaturesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/features',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.features.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'latestStatsCount', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/features',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListFeaturesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Feature.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Feature) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}/features/{featuresId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.features.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Feature',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesFeaturesPatchRequest',
        response_type_name='GoogleCloudAiplatformV1Feature',
        supports_download=False,
    )

  class ProjectsLocationsFeaturestoresEntityTypesService(base_api.BaseApiService):
    """Service class for the projects_locations_featurestores_entityTypes resource."""

    _NAME = 'projects_locations_featurestores_entityTypes'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsFeaturestoresEntityTypesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new EntityType in a given Featurestore.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['entityTypeId'],
        relative_path='v1/{+parent}/entityTypes',
        request_field='googleCloudAiplatformV1EntityType',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single EntityType. The EntityType must not have any Features or `force` must be set to true for the request to succeed.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def DeleteFeatureValues(self, request, global_params=None):
      r"""Delete Feature values from Featurestore. The progress of the deletion is tracked by the returned operation. The deleted feature values are guaranteed to be invisible to subsequent read operations after the operation is marked as successfully done. If a delete feature values operation fails, the feature values returned from reads and exports may be inconsistent. If consistency is required, the caller must retry the same delete request again and wait till the new operation returned is marked as successfully done.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesDeleteFeatureValuesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('DeleteFeatureValues')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteFeatureValues.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:deleteFeatureValues',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.deleteFeatureValues',
        ordered_params=['entityType'],
        path_params=['entityType'],
        query_params=[],
        relative_path='v1/{+entityType}:deleteFeatureValues',
        request_field='googleCloudAiplatformV1DeleteFeatureValuesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesDeleteFeatureValuesRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def ExportFeatureValues(self, request, global_params=None):
      r"""Exports Feature values from all the entities of a target EntityType.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesExportFeatureValuesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('ExportFeatureValues')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExportFeatureValues.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:exportFeatureValues',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.exportFeatureValues',
        ordered_params=['entityType'],
        path_params=['entityType'],
        query_params=[],
        relative_path='v1/{+entityType}:exportFeatureValues',
        request_field='googleCloudAiplatformV1ExportFeatureValuesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesExportFeatureValuesRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single EntityType.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1EntityType) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesGetRequest',
        response_type_name='GoogleCloudAiplatformV1EntityType',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:getIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def ImportFeatureValues(self, request, global_params=None):
      r"""Imports Feature values into the Featurestore from a source storage. The progress of the import is tracked by the returned operation. The imported features are guaranteed to be visible to subsequent read operations after the operation is marked as successfully done. If an import operation fails, the Feature values returned from reads and exports may be inconsistent. If consistency is required, the caller must retry the same import request again and wait till the new operation returned is marked as successfully done. There are also scenarios where the caller can cause inconsistency. - Source data for import contains multiple distinct Feature values for the same entity ID and timestamp. - Source is modified during an import. This includes adding, updating, or removing source data and/or metadata. Examples of updating metadata include but are not limited to changing storage location, storage class, or retention policy. - Online serving cluster is under-provisioned.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesImportFeatureValuesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('ImportFeatureValues')
      return self._RunMethod(
          config, request, global_params=global_params)

    ImportFeatureValues.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:importFeatureValues',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.importFeatureValues',
        ordered_params=['entityType'],
        path_params=['entityType'],
        query_params=[],
        relative_path='v1/{+entityType}:importFeatureValues',
        request_field='googleCloudAiplatformV1ImportFeatureValuesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesImportFeatureValuesRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists EntityTypes in a given Featurestore.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListEntityTypesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/entityTypes',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListEntityTypesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single EntityType.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1EntityType) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1EntityType',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesPatchRequest',
        response_type_name='GoogleCloudAiplatformV1EntityType',
        supports_download=False,
    )

    def ReadFeatureValues(self, request, global_params=None):
      r"""Reads Feature values of a specific entity of an EntityType. For reading feature values of multiple entities of an EntityType, please use StreamingReadFeatureValues.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesReadFeatureValuesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ReadFeatureValuesResponse) The response message.
      """
      config = self.GetMethodConfig('ReadFeatureValues')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReadFeatureValues.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:readFeatureValues',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.readFeatureValues',
        ordered_params=['entityType'],
        path_params=['entityType'],
        query_params=[],
        relative_path='v1/{+entityType}:readFeatureValues',
        request_field='googleCloudAiplatformV1ReadFeatureValuesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesReadFeatureValuesRequest',
        response_type_name='GoogleCloudAiplatformV1ReadFeatureValuesResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:setIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def StreamingReadFeatureValues(self, request, global_params=None):
      r"""Reads Feature values for multiple entities. Depending on their size, data for different entities may be broken up across multiple responses.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesStreamingReadFeatureValuesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ReadFeatureValuesResponse) The response message.
      """
      config = self.GetMethodConfig('StreamingReadFeatureValues')
      return self._RunMethod(
          config, request, global_params=global_params)

    StreamingReadFeatureValues.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:streamingReadFeatureValues',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.streamingReadFeatureValues',
        ordered_params=['entityType'],
        path_params=['entityType'],
        query_params=[],
        relative_path='v1/{+entityType}:streamingReadFeatureValues',
        request_field='googleCloudAiplatformV1StreamingReadFeatureValuesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesStreamingReadFeatureValuesRequest',
        response_type_name='GoogleCloudAiplatformV1ReadFeatureValuesResponse',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:testIamPermissions',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['permissions'],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

    def WriteFeatureValues(self, request, global_params=None):
      r"""Writes Feature values of one or more entities of an EntityType. The Feature values are merged into existing entities if any. The Feature values to be written must have timestamp within the online storage retention.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresEntityTypesWriteFeatureValuesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1WriteFeatureValuesResponse) The response message.
      """
      config = self.GetMethodConfig('WriteFeatureValues')
      return self._RunMethod(
          config, request, global_params=global_params)

    WriteFeatureValues.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}/entityTypes/{entityTypesId}:writeFeatureValues',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.entityTypes.writeFeatureValues',
        ordered_params=['entityType'],
        path_params=['entityType'],
        query_params=[],
        relative_path='v1/{+entityType}:writeFeatureValues',
        request_field='googleCloudAiplatformV1WriteFeatureValuesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresEntityTypesWriteFeatureValuesRequest',
        response_type_name='GoogleCloudAiplatformV1WriteFeatureValuesResponse',
        supports_download=False,
    )

  class ProjectsLocationsFeaturestoresService(base_api.BaseApiService):
    """Service class for the projects_locations_featurestores resource."""

    _NAME = 'projects_locations_featurestores'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsFeaturestoresService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchReadFeatureValues(self, request, global_params=None):
      r"""Batch reads Feature values from a Featurestore. This API enables batch reading Feature values, where each read instance in the batch may read Feature values of entities from one or more EntityTypes. Point-in-time correctness is guaranteed for Feature values of each read instance as of each instance's read timestamp.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresBatchReadFeatureValuesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchReadFeatureValues')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchReadFeatureValues.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}:batchReadFeatureValues',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.batchReadFeatureValues',
        ordered_params=['featurestore'],
        path_params=['featurestore'],
        query_params=[],
        relative_path='v1/{+featurestore}:batchReadFeatureValues',
        request_field='googleCloudAiplatformV1BatchReadFeatureValuesRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresBatchReadFeatureValuesRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a new Featurestore in a given project and location.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['featurestoreId'],
        relative_path='v1/{+parent}/featurestores',
        request_field='googleCloudAiplatformV1Featurestore',
        request_type_name='AiplatformProjectsLocationsFeaturestoresCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Featurestore. The Featurestore must not contain any EntityTypes or `force` must be set to true for the request to succeed.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.featurestores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Featurestore.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Featurestore) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresGetRequest',
        response_type_name='GoogleCloudAiplatformV1Featurestore',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}:getIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresGetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Featurestores in a given project and location.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListFeaturestoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/featurestores',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresListRequest',
        response_type_name='GoogleCloudAiplatformV1ListFeaturestoresResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Featurestore.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.featurestores.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Featurestore',
        request_type_name='AiplatformProjectsLocationsFeaturestoresPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def SearchFeatures(self, request, global_params=None):
      r"""Searches Features matching a query in a given project.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresSearchFeaturesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1SearchFeaturesResponse) The response message.
      """
      config = self.GetMethodConfig('SearchFeatures')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchFeatures.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores:searchFeatures',
        http_method='GET',
        method_id='aiplatform.projects.locations.featurestores.searchFeatures',
        ordered_params=['location'],
        path_params=['location'],
        query_params=['pageSize', 'pageToken', 'query'],
        relative_path='v1/{+location}/featurestores:searchFeatures',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresSearchFeaturesRequest',
        response_type_name='GoogleCloudAiplatformV1SearchFeaturesResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}:setIamPolicy',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1/{+resource}:setIamPolicy',
        request_field='googleIamV1SetIamPolicyRequest',
        request_type_name='AiplatformProjectsLocationsFeaturestoresSetIamPolicyRequest',
        response_type_name='GoogleIamV1Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (AiplatformProjectsLocationsFeaturestoresTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleIamV1TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/featurestores/{featurestoresId}:testIamPermissions',
        http_method='POST',
        method_id='aiplatform.projects.locations.featurestores.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['permissions'],
        relative_path='v1/{+resource}:testIamPermissions',
        request_field='',
        request_type_name='AiplatformProjectsLocationsFeaturestoresTestIamPermissionsRequest',
        response_type_name='GoogleIamV1TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsHyperparameterTuningJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_hyperparameterTuningJobs resource."""

    _NAME = 'projects_locations_hyperparameterTuningJobs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsHyperparameterTuningJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a HyperparameterTuningJob. Starts asynchronous cancellation on the HyperparameterTuningJob. The server makes a best effort to cancel the job, but success is not guaranteed. Clients can use JobService.GetHyperparameterTuningJob or other methods to check whether the cancellation succeeded or whether the job completed despite cancellation. On successful cancellation, the HyperparameterTuningJob is not deleted; instead it becomes a job with a HyperparameterTuningJob.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`, and HyperparameterTuningJob.state is set to `CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs/{hyperparameterTuningJobsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleCloudAiplatformV1CancelHyperparameterTuningJobRequest',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a HyperparameterTuningJob.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1HyperparameterTuningJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/hyperparameterTuningJobs',
        request_field='googleCloudAiplatformV1HyperparameterTuningJob',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1HyperparameterTuningJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a HyperparameterTuningJob.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs/{hyperparameterTuningJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a HyperparameterTuningJob.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1HyperparameterTuningJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs/{hyperparameterTuningJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1HyperparameterTuningJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists HyperparameterTuningJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsHyperparameterTuningJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListHyperparameterTuningJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/hyperparameterTuningJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.hyperparameterTuningJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/hyperparameterTuningJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsHyperparameterTuningJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListHyperparameterTuningJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsIndexEndpointsService(base_api.BaseApiService):
    """Service class for the projects_locations_indexEndpoints resource."""

    _NAME = 'projects_locations_indexEndpoints'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsIndexEndpointsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an IndexEndpoint.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexEndpoints',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexEndpoints.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/indexEndpoints',
        request_field='googleCloudAiplatformV1IndexEndpoint',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an IndexEndpoint.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.indexEndpoints.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def DeployIndex(self, request, global_params=None):
      r"""Deploys an Index into this IndexEndpoint, creating a DeployedIndex within it. Only non-empty Indexes can be deployed.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsDeployIndexRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('DeployIndex')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeployIndex.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}:deployIndex',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexEndpoints.deployIndex',
        ordered_params=['indexEndpoint'],
        path_params=['indexEndpoint'],
        query_params=[],
        relative_path='v1/{+indexEndpoint}:deployIndex',
        request_field='googleCloudAiplatformV1DeployIndexRequest',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsDeployIndexRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an IndexEndpoint.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1IndexEndpoint) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.indexEndpoints.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsGetRequest',
        response_type_name='GoogleCloudAiplatformV1IndexEndpoint',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists IndexEndpoints in a Location.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListIndexEndpointsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexEndpoints',
        http_method='GET',
        method_id='aiplatform.projects.locations.indexEndpoints.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/indexEndpoints',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListIndexEndpointsResponse',
        supports_download=False,
    )

    def MutateDeployedIndex(self, request, global_params=None):
      r"""Update an existing DeployedIndex under an IndexEndpoint.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsMutateDeployedIndexRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('MutateDeployedIndex')
      return self._RunMethod(
          config, request, global_params=global_params)

    MutateDeployedIndex.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}:mutateDeployedIndex',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexEndpoints.mutateDeployedIndex',
        ordered_params=['indexEndpoint'],
        path_params=['indexEndpoint'],
        query_params=[],
        relative_path='v1/{+indexEndpoint}:mutateDeployedIndex',
        request_field='googleCloudAiplatformV1DeployedIndex',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsMutateDeployedIndexRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an IndexEndpoint.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1IndexEndpoint) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.indexEndpoints.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1IndexEndpoint',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1IndexEndpoint',
        supports_download=False,
    )

    def UndeployIndex(self, request, global_params=None):
      r"""Undeploys an Index from an IndexEndpoint, removing a DeployedIndex from it, and freeing all resources it's using.

      Args:
        request: (AiplatformProjectsLocationsIndexEndpointsUndeployIndexRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('UndeployIndex')
      return self._RunMethod(
          config, request, global_params=global_params)

    UndeployIndex.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexEndpoints/{indexEndpointsId}:undeployIndex',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexEndpoints.undeployIndex',
        ordered_params=['indexEndpoint'],
        path_params=['indexEndpoint'],
        query_params=[],
        relative_path='v1/{+indexEndpoint}:undeployIndex',
        request_field='googleCloudAiplatformV1UndeployIndexRequest',
        request_type_name='AiplatformProjectsLocationsIndexEndpointsUndeployIndexRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsIndexesService(base_api.BaseApiService):
    """Service class for the projects_locations_indexes resource."""

    _NAME = 'projects_locations_indexes'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsIndexesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an Index.

      Args:
        request: (AiplatformProjectsLocationsIndexesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexes',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/indexes',
        request_field='googleCloudAiplatformV1Index',
        request_type_name='AiplatformProjectsLocationsIndexesCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an Index. An Index can only be deleted when all its DeployedIndexes had been undeployed.

      Args:
        request: (AiplatformProjectsLocationsIndexesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.indexes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets an Index.

      Args:
        request: (AiplatformProjectsLocationsIndexesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Index) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.indexes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexesGetRequest',
        response_type_name='GoogleCloudAiplatformV1Index',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Indexes in a Location.

      Args:
        request: (AiplatformProjectsLocationsIndexesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListIndexesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexes',
        http_method='GET',
        method_id='aiplatform.projects.locations.indexes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/indexes',
        request_field='',
        request_type_name='AiplatformProjectsLocationsIndexesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListIndexesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates an Index.

      Args:
        request: (AiplatformProjectsLocationsIndexesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.indexes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Index',
        request_type_name='AiplatformProjectsLocationsIndexesPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def RemoveDatapoints(self, request, global_params=None):
      r"""Remove Datapoints from an Index.

      Args:
        request: (AiplatformProjectsLocationsIndexesRemoveDatapointsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1RemoveDatapointsResponse) The response message.
      """
      config = self.GetMethodConfig('RemoveDatapoints')
      return self._RunMethod(
          config, request, global_params=global_params)

    RemoveDatapoints.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}:removeDatapoints',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexes.removeDatapoints',
        ordered_params=['index'],
        path_params=['index'],
        query_params=[],
        relative_path='v1/{+index}:removeDatapoints',
        request_field='googleCloudAiplatformV1RemoveDatapointsRequest',
        request_type_name='AiplatformProjectsLocationsIndexesRemoveDatapointsRequest',
        response_type_name='GoogleCloudAiplatformV1RemoveDatapointsResponse',
        supports_download=False,
    )

    def UpsertDatapoints(self, request, global_params=None):
      r"""Add/update Datapoints into an Index.

      Args:
        request: (AiplatformProjectsLocationsIndexesUpsertDatapointsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1UpsertDatapointsResponse) The response message.
      """
      config = self.GetMethodConfig('UpsertDatapoints')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpsertDatapoints.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/indexes/{indexesId}:upsertDatapoints',
        http_method='POST',
        method_id='aiplatform.projects.locations.indexes.upsertDatapoints',
        ordered_params=['index'],
        path_params=['index'],
        query_params=[],
        relative_path='v1/{+index}:upsertDatapoints',
        request_field='googleCloudAiplatformV1UpsertDatapointsRequest',
        request_type_name='AiplatformProjectsLocationsIndexesUpsertDatapointsRequest',
        response_type_name='GoogleCloudAiplatformV1UpsertDatapointsResponse',
        supports_download=False,
    )

  class ProjectsLocationsMetadataStoresArtifactsService(base_api.BaseApiService):
    """Service class for the projects_locations_metadataStores_artifacts resource."""

    _NAME = 'projects_locations_metadataStores_artifacts'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsMetadataStoresArtifactsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates an Artifact associated with a MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Artifact) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['artifactId'],
        relative_path='v1/{+parent}/artifacts',
        request_field='googleCloudAiplatformV1Artifact',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1Artifact',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an Artifact.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts/{artifactsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a specific Artifact.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Artifact) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts/{artifactsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsGetRequest',
        response_type_name='GoogleCloudAiplatformV1Artifact',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Artifacts in the MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListArtifactsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/artifacts',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListArtifactsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a stored Artifact.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Artifact) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts/{artifactsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Artifact',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1Artifact',
        supports_download=False,
    )

    def Purge(self, request, global_params=None):
      r"""Purges Artifacts.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsPurgeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Purge')
      return self._RunMethod(
          config, request, global_params=global_params)

    Purge.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts:purge',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.purge',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/artifacts:purge',
        request_field='googleCloudAiplatformV1PurgeArtifactsRequest',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsPurgeRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def QueryArtifactLineageSubgraph(self, request, global_params=None):
      r"""Retrieves lineage of an Artifact represented through Artifacts and Executions connected by Event edges and returned as a LineageSubgraph.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresArtifactsQueryArtifactLineageSubgraphRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1LineageSubgraph) The response message.
      """
      config = self.GetMethodConfig('QueryArtifactLineageSubgraph')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryArtifactLineageSubgraph.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/artifacts/{artifactsId}:queryArtifactLineageSubgraph',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.artifacts.queryArtifactLineageSubgraph',
        ordered_params=['artifact'],
        path_params=['artifact'],
        query_params=['filter', 'maxHops'],
        relative_path='v1/{+artifact}:queryArtifactLineageSubgraph',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresArtifactsQueryArtifactLineageSubgraphRequest',
        response_type_name='GoogleCloudAiplatformV1LineageSubgraph',
        supports_download=False,
    )

  class ProjectsLocationsMetadataStoresContextsService(base_api.BaseApiService):
    """Service class for the projects_locations_metadataStores_contexts resource."""

    _NAME = 'projects_locations_metadataStores_contexts'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsMetadataStoresContextsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddContextArtifactsAndExecutions(self, request, global_params=None):
      r"""Adds a set of Artifacts and Executions to a Context. If any of the Artifacts or Executions have already been added to a Context, they are simply skipped.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsAddContextArtifactsAndExecutionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1AddContextArtifactsAndExecutionsResponse) The response message.
      """
      config = self.GetMethodConfig('AddContextArtifactsAndExecutions')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddContextArtifactsAndExecutions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}:addContextArtifactsAndExecutions',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.contexts.addContextArtifactsAndExecutions',
        ordered_params=['context'],
        path_params=['context'],
        query_params=[],
        relative_path='v1/{+context}:addContextArtifactsAndExecutions',
        request_field='googleCloudAiplatformV1AddContextArtifactsAndExecutionsRequest',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsAddContextArtifactsAndExecutionsRequest',
        response_type_name='GoogleCloudAiplatformV1AddContextArtifactsAndExecutionsResponse',
        supports_download=False,
    )

    def AddContextChildren(self, request, global_params=None):
      r"""Adds a set of Contexts as children to a parent Context. If any of the child Contexts have already been added to the parent Context, they are simply skipped. If this call would create a cycle or cause any Context to have more than 10 parents, the request will fail with an INVALID_ARGUMENT error.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsAddContextChildrenRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1AddContextChildrenResponse) The response message.
      """
      config = self.GetMethodConfig('AddContextChildren')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddContextChildren.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}:addContextChildren',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.contexts.addContextChildren',
        ordered_params=['context'],
        path_params=['context'],
        query_params=[],
        relative_path='v1/{+context}:addContextChildren',
        request_field='googleCloudAiplatformV1AddContextChildrenRequest',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsAddContextChildrenRequest',
        response_type_name='GoogleCloudAiplatformV1AddContextChildrenResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a Context associated with a MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Context) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.contexts.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['contextId'],
        relative_path='v1/{+parent}/contexts',
        request_field='googleCloudAiplatformV1Context',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1Context',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a stored Context.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.metadataStores.contexts.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag', 'force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a specific Context.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Context) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.contexts.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsGetRequest',
        response_type_name='GoogleCloudAiplatformV1Context',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Contexts on the MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListContextsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.contexts.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/contexts',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListContextsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a stored Context.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Context) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.metadataStores.contexts.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Context',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1Context',
        supports_download=False,
    )

    def Purge(self, request, global_params=None):
      r"""Purges Contexts.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsPurgeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Purge')
      return self._RunMethod(
          config, request, global_params=global_params)

    Purge.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts:purge',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.contexts.purge',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/contexts:purge',
        request_field='googleCloudAiplatformV1PurgeContextsRequest',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsPurgeRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def QueryContextLineageSubgraph(self, request, global_params=None):
      r"""Retrieves Artifacts and Executions within the specified Context, connected by Event edges and returned as a LineageSubgraph.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsQueryContextLineageSubgraphRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1LineageSubgraph) The response message.
      """
      config = self.GetMethodConfig('QueryContextLineageSubgraph')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryContextLineageSubgraph.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}:queryContextLineageSubgraph',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.contexts.queryContextLineageSubgraph',
        ordered_params=['context'],
        path_params=['context'],
        query_params=[],
        relative_path='v1/{+context}:queryContextLineageSubgraph',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsQueryContextLineageSubgraphRequest',
        response_type_name='GoogleCloudAiplatformV1LineageSubgraph',
        supports_download=False,
    )

    def RemoveContextChildren(self, request, global_params=None):
      r"""Remove a set of children contexts from a parent Context. If any of the child Contexts were NOT added to the parent Context, they are simply skipped.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresContextsRemoveContextChildrenRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1RemoveContextChildrenResponse) The response message.
      """
      config = self.GetMethodConfig('RemoveContextChildren')
      return self._RunMethod(
          config, request, global_params=global_params)

    RemoveContextChildren.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/contexts/{contextsId}:removeContextChildren',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.contexts.removeContextChildren',
        ordered_params=['context'],
        path_params=['context'],
        query_params=[],
        relative_path='v1/{+context}:removeContextChildren',
        request_field='googleCloudAiplatformV1RemoveContextChildrenRequest',
        request_type_name='AiplatformProjectsLocationsMetadataStoresContextsRemoveContextChildrenRequest',
        response_type_name='GoogleCloudAiplatformV1RemoveContextChildrenResponse',
        supports_download=False,
    )

  class ProjectsLocationsMetadataStoresExecutionsService(base_api.BaseApiService):
    """Service class for the projects_locations_metadataStores_executions resource."""

    _NAME = 'projects_locations_metadataStores_executions'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsMetadataStoresExecutionsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddExecutionEvents(self, request, global_params=None):
      r"""Adds Events to the specified Execution. An Event indicates whether an Artifact was used as an input or output for an Execution. If an Event already exists between the Execution and the Artifact, the Event is skipped.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsAddExecutionEventsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1AddExecutionEventsResponse) The response message.
      """
      config = self.GetMethodConfig('AddExecutionEvents')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddExecutionEvents.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions/{executionsId}:addExecutionEvents',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.executions.addExecutionEvents',
        ordered_params=['execution'],
        path_params=['execution'],
        query_params=[],
        relative_path='v1/{+execution}:addExecutionEvents',
        request_field='googleCloudAiplatformV1AddExecutionEventsRequest',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsAddExecutionEventsRequest',
        response_type_name='GoogleCloudAiplatformV1AddExecutionEventsResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates an Execution associated with a MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Execution) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.executions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['executionId'],
        relative_path='v1/{+parent}/executions',
        request_field='googleCloudAiplatformV1Execution',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1Execution',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes an Execution.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions/{executionsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.metadataStores.executions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['etag'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a specific Execution.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Execution) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions/{executionsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.executions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsGetRequest',
        response_type_name='GoogleCloudAiplatformV1Execution',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Executions in the MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListExecutionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.executions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/executions',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListExecutionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a stored Execution.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Execution) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions/{executionsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.metadataStores.executions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['allowMissing', 'updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Execution',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1Execution',
        supports_download=False,
    )

    def Purge(self, request, global_params=None):
      r"""Purges Executions.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsPurgeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Purge')
      return self._RunMethod(
          config, request, global_params=global_params)

    Purge.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions:purge',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.executions.purge',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/executions:purge',
        request_field='googleCloudAiplatformV1PurgeExecutionsRequest',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsPurgeRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def QueryExecutionInputsAndOutputs(self, request, global_params=None):
      r"""Obtains the set of input and output Artifacts for this Execution, in the form of LineageSubgraph that also contains the Execution and connecting Events.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresExecutionsQueryExecutionInputsAndOutputsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1LineageSubgraph) The response message.
      """
      config = self.GetMethodConfig('QueryExecutionInputsAndOutputs')
      return self._RunMethod(
          config, request, global_params=global_params)

    QueryExecutionInputsAndOutputs.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/executions/{executionsId}:queryExecutionInputsAndOutputs',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.executions.queryExecutionInputsAndOutputs',
        ordered_params=['execution'],
        path_params=['execution'],
        query_params=[],
        relative_path='v1/{+execution}:queryExecutionInputsAndOutputs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresExecutionsQueryExecutionInputsAndOutputsRequest',
        response_type_name='GoogleCloudAiplatformV1LineageSubgraph',
        supports_download=False,
    )

  class ProjectsLocationsMetadataStoresMetadataSchemasService(base_api.BaseApiService):
    """Service class for the projects_locations_metadataStores_metadataSchemas resource."""

    _NAME = 'projects_locations_metadataStores_metadataSchemas'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsMetadataStoresMetadataSchemasService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a MetadataSchema.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresMetadataSchemasCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1MetadataSchema) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/metadataSchemas',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.metadataSchemas.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['metadataSchemaId'],
        relative_path='v1/{+parent}/metadataSchemas',
        request_field='googleCloudAiplatformV1MetadataSchema',
        request_type_name='AiplatformProjectsLocationsMetadataStoresMetadataSchemasCreateRequest',
        response_type_name='GoogleCloudAiplatformV1MetadataSchema',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a specific MetadataSchema.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresMetadataSchemasGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1MetadataSchema) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/metadataSchemas/{metadataSchemasId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.metadataSchemas.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresMetadataSchemasGetRequest',
        response_type_name='GoogleCloudAiplatformV1MetadataSchema',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists MetadataSchemas.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresMetadataSchemasListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListMetadataSchemasResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}/metadataSchemas',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.metadataSchemas.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+parent}/metadataSchemas',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresMetadataSchemasListRequest',
        response_type_name='GoogleCloudAiplatformV1ListMetadataSchemasResponse',
        supports_download=False,
    )

  class ProjectsLocationsMetadataStoresService(base_api.BaseApiService):
    """Service class for the projects_locations_metadataStores resource."""

    _NAME = 'projects_locations_metadataStores'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsMetadataStoresService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Initializes a MetadataStore, including allocation of resources.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores',
        http_method='POST',
        method_id='aiplatform.projects.locations.metadataStores.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['metadataStoreId'],
        relative_path='v1/{+parent}/metadataStores',
        request_field='googleCloudAiplatformV1MetadataStore',
        request_type_name='AiplatformProjectsLocationsMetadataStoresCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single MetadataStore and all its child resources (Artifacts, Executions, and Contexts).

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.metadataStores.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Retrieves a specific MetadataStore.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1MetadataStore) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores/{metadataStoresId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresGetRequest',
        response_type_name='GoogleCloudAiplatformV1MetadataStore',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists MetadataStores for a Location.

      Args:
        request: (AiplatformProjectsLocationsMetadataStoresListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListMetadataStoresResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/metadataStores',
        http_method='GET',
        method_id='aiplatform.projects.locations.metadataStores.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/metadataStores',
        request_field='',
        request_type_name='AiplatformProjectsLocationsMetadataStoresListRequest',
        response_type_name='GoogleCloudAiplatformV1ListMetadataStoresResponse',
        supports_download=False,
    )

  class ProjectsLocationsMigratableResourcesService(base_api.BaseApiService):
    """Service class for the projects_locations_migratableResources resource."""

    _NAME = 'projects_locations_migratableResources'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsMigratableResourcesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchMigrate(self, request, global_params=None):
      r"""Batch migrates resources from ml.googleapis.com, automl.googleapis.com, and datalabeling.googleapis.com to Vertex AI.

      Args:
        request: (AiplatformProjectsLocationsMigratableResourcesBatchMigrateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('BatchMigrate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchMigrate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/migratableResources:batchMigrate',
        http_method='POST',
        method_id='aiplatform.projects.locations.migratableResources.batchMigrate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/migratableResources:batchMigrate',
        request_field='googleCloudAiplatformV1BatchMigrateResourcesRequest',
        request_type_name='AiplatformProjectsLocationsMigratableResourcesBatchMigrateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Search(self, request, global_params=None):
      r"""Searches all of the resources in automl.googleapis.com, datalabeling.googleapis.com and ml.googleapis.com that can be migrated to Vertex AI's given location.

      Args:
        request: (AiplatformProjectsLocationsMigratableResourcesSearchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1SearchMigratableResourcesResponse) The response message.
      """
      config = self.GetMethodConfig('Search')
      return self._RunMethod(
          config, request, global_params=global_params)

    Search.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/migratableResources:search',
        http_method='POST',
        method_id='aiplatform.projects.locations.migratableResources.search',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/migratableResources:search',
        request_field='googleCloudAiplatformV1SearchMigratableResourcesRequest',
        request_type_name='AiplatformProjectsLocationsMigratableResourcesSearchRequest',
        response_type_name='GoogleCloudAiplatformV1SearchMigratableResourcesResponse',
        supports_download=False,
    )

  class ProjectsLocationsModelDeploymentMonitoringJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_modelDeploymentMonitoringJobs resource."""

    _NAME = 'projects_locations_modelDeploymentMonitoringJobs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsModelDeploymentMonitoringJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a ModelDeploymentMonitoringJob. It will run periodically on a configured interval.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ModelDeploymentMonitoringJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/modelDeploymentMonitoringJobs',
        request_field='googleCloudAiplatformV1ModelDeploymentMonitoringJob',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1ModelDeploymentMonitoringJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a ModelDeploymentMonitoringJob.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a ModelDeploymentMonitoringJob.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ModelDeploymentMonitoringJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1ModelDeploymentMonitoringJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ModelDeploymentMonitoringJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListModelDeploymentMonitoringJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/modelDeploymentMonitoringJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListModelDeploymentMonitoringJobsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a ModelDeploymentMonitoringJob.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1ModelDeploymentMonitoringJob',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Pause(self, request, global_params=None):
      r"""Pauses a ModelDeploymentMonitoringJob. If the job is running, the server makes a best effort to cancel the job. Will mark ModelDeploymentMonitoringJob.state to 'PAUSED'.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsPauseRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Pause')
      return self._RunMethod(
          config, request, global_params=global_params)

    Pause.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}:pause',
        http_method='POST',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.pause',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:pause',
        request_field='googleCloudAiplatformV1PauseModelDeploymentMonitoringJobRequest',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsPauseRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Resume(self, request, global_params=None):
      r"""Resumes a paused ModelDeploymentMonitoringJob. It will start to run from next scheduled time. A deleted ModelDeploymentMonitoringJob can't be resumed.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsResumeRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Resume')
      return self._RunMethod(
          config, request, global_params=global_params)

    Resume.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}:resume',
        http_method='POST',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.resume',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:resume',
        request_field='googleCloudAiplatformV1ResumeModelDeploymentMonitoringJobRequest',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsResumeRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def SearchModelDeploymentMonitoringStatsAnomalies(self, request, global_params=None):
      r"""Searches Model Monitoring Statistics generated within a given time window.

      Args:
        request: (AiplatformProjectsLocationsModelDeploymentMonitoringJobsSearchModelDeploymentMonitoringStatsAnomaliesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1SearchModelDeploymentMonitoringStatsAnomaliesResponse) The response message.
      """
      config = self.GetMethodConfig('SearchModelDeploymentMonitoringStatsAnomalies')
      return self._RunMethod(
          config, request, global_params=global_params)

    SearchModelDeploymentMonitoringStatsAnomalies.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/modelDeploymentMonitoringJobs/{modelDeploymentMonitoringJobsId}:searchModelDeploymentMonitoringStatsAnomalies',
        http_method='POST',
        method_id='aiplatform.projects.locations.modelDeploymentMonitoringJobs.searchModelDeploymentMonitoringStatsAnomalies',
        ordered_params=['modelDeploymentMonitoringJob'],
        path_params=['modelDeploymentMonitoringJob'],
        query_params=[],
        relative_path='v1/{+modelDeploymentMonitoringJob}:searchModelDeploymentMonitoringStatsAnomalies',
        request_field='googleCloudAiplatformV1SearchModelDeploymentMonitoringStatsAnomaliesRequest',
        request_type_name='AiplatformProjectsLocationsModelDeploymentMonitoringJobsSearchModelDeploymentMonitoringStatsAnomaliesRequest',
        response_type_name='GoogleCloudAiplatformV1SearchModelDeploymentMonitoringStatsAnomaliesResponse',
        supports_download=False,
    )

  class ProjectsLocationsModelsEvaluationsSlicesService(base_api.BaseApiService):
    """Service class for the projects_locations_models_evaluations_slices resource."""

    _NAME = 'projects_locations_models_evaluations_slices'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsModelsEvaluationsSlicesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchImport(self, request, global_params=None):
      r"""Imports a list of externally generated EvaluatedAnnotations.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsSlicesBatchImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1BatchImportEvaluatedAnnotationsResponse) The response message.
      """
      config = self.GetMethodConfig('BatchImport')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchImport.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations/{evaluationsId}/slices/{slicesId}:batchImport',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.evaluations.slices.batchImport',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}:batchImport',
        request_field='googleCloudAiplatformV1BatchImportEvaluatedAnnotationsRequest',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsSlicesBatchImportRequest',
        response_type_name='GoogleCloudAiplatformV1BatchImportEvaluatedAnnotationsResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a ModelEvaluationSlice.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsSlicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ModelEvaluationSlice) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations/{evaluationsId}/slices/{slicesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.evaluations.slices.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsSlicesGetRequest',
        response_type_name='GoogleCloudAiplatformV1ModelEvaluationSlice',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ModelEvaluationSlices in a ModelEvaluation.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsSlicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListModelEvaluationSlicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations/{evaluationsId}/slices',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.evaluations.slices.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/slices',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsSlicesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListModelEvaluationSlicesResponse',
        supports_download=False,
    )

  class ProjectsLocationsModelsEvaluationsService(base_api.BaseApiService):
    """Service class for the projects_locations_models_evaluations resource."""

    _NAME = 'projects_locations_models_evaluations'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsModelsEvaluationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets a ModelEvaluation.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ModelEvaluation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations/{evaluationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.evaluations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsGetRequest',
        response_type_name='GoogleCloudAiplatformV1ModelEvaluation',
        supports_download=False,
    )

    def Import(self, request, global_params=None):
      r"""Imports an externally generated ModelEvaluation.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsImportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ModelEvaluation) The response message.
      """
      config = self.GetMethodConfig('Import')
      return self._RunMethod(
          config, request, global_params=global_params)

    Import.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations:import',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.evaluations.import',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/evaluations:import',
        request_field='googleCloudAiplatformV1ImportModelEvaluationRequest',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsImportRequest',
        response_type_name='GoogleCloudAiplatformV1ModelEvaluation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ModelEvaluations in a Model.

      Args:
        request: (AiplatformProjectsLocationsModelsEvaluationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListModelEvaluationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}/evaluations',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.evaluations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/evaluations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsEvaluationsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListModelEvaluationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsModelsService(base_api.BaseApiService):
    """Service class for the projects_locations_models resource."""

    _NAME = 'projects_locations_models'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsModelsService, self).__init__(client)
      self._upload_configs = {
          }

    def Copy(self, request, global_params=None):
      r"""Copies an already existing Vertex AI Model into the specified Location. The source Model must exist in the same Project. When copying custom Models, the users themselves are responsible for Model.metadata content to be region-agnostic, as well as making sure that any resources (e.g. files) it depends on remain accessible.

      Args:
        request: (AiplatformProjectsLocationsModelsCopyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Copy')
      return self._RunMethod(
          config, request, global_params=global_params)

    Copy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models:copy',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.copy',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/models:copy',
        request_field='googleCloudAiplatformV1CopyModelRequest',
        request_type_name='AiplatformProjectsLocationsModelsCopyRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Model. A model cannot be deleted if any Endpoint resource has a DeployedModel based on the model in its deployed_models field.

      Args:
        request: (AiplatformProjectsLocationsModelsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.models.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def DeleteVersion(self, request, global_params=None):
      r"""Deletes a Model version. Model version can only be deleted if there are no DeployedModels created from it. Deleting the only version in the Model is not allowed. Use DeleteModel for deleting the Model instead.

      Args:
        request: (AiplatformProjectsLocationsModelsDeleteVersionRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('DeleteVersion')
      return self._RunMethod(
          config, request, global_params=global_params)

    DeleteVersion.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}:deleteVersion',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.models.deleteVersion',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:deleteVersion',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsDeleteVersionRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Export(self, request, global_params=None):
      r"""Exports a trained, exportable Model to a location specified by the user. A Model is considered to be exportable if it has at least one supported export format.

      Args:
        request: (AiplatformProjectsLocationsModelsExportRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Export')
      return self._RunMethod(
          config, request, global_params=global_params)

    Export.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}:export',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.export',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:export',
        request_field='googleCloudAiplatformV1ExportModelRequest',
        request_type_name='AiplatformProjectsLocationsModelsExportRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Model.

      Args:
        request: (AiplatformProjectsLocationsModelsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Model) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsGetRequest',
        response_type_name='GoogleCloudAiplatformV1Model',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Models in a Location.

      Args:
        request: (AiplatformProjectsLocationsModelsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListModelsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/models',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListModelsResponse',
        supports_download=False,
    )

    def ListVersions(self, request, global_params=None):
      r"""Lists versions of the specified model.

      Args:
        request: (AiplatformProjectsLocationsModelsListVersionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListModelVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('ListVersions')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListVersions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}:listVersions',
        http_method='GET',
        method_id='aiplatform.projects.locations.models.listVersions',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+name}:listVersions',
        request_field='',
        request_type_name='AiplatformProjectsLocationsModelsListVersionsRequest',
        response_type_name='GoogleCloudAiplatformV1ListModelVersionsResponse',
        supports_download=False,
    )

    def MergeVersionAliases(self, request, global_params=None):
      r"""Merges a set of aliases for a Model version.

      Args:
        request: (AiplatformProjectsLocationsModelsMergeVersionAliasesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Model) The response message.
      """
      config = self.GetMethodConfig('MergeVersionAliases')
      return self._RunMethod(
          config, request, global_params=global_params)

    MergeVersionAliases.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}:mergeVersionAliases',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.mergeVersionAliases',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:mergeVersionAliases',
        request_field='googleCloudAiplatformV1MergeVersionAliasesRequest',
        request_type_name='AiplatformProjectsLocationsModelsMergeVersionAliasesRequest',
        response_type_name='GoogleCloudAiplatformV1Model',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a Model.

      Args:
        request: (AiplatformProjectsLocationsModelsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Model) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models/{modelsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.models.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Model',
        request_type_name='AiplatformProjectsLocationsModelsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1Model',
        supports_download=False,
    )

    def Upload(self, request, global_params=None):
      r"""Uploads a Model artifact into Vertex AI.

      Args:
        request: (AiplatformProjectsLocationsModelsUploadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Upload')
      return self._RunMethod(
          config, request, global_params=global_params)

    Upload.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/models:upload',
        http_method='POST',
        method_id='aiplatform.projects.locations.models.upload',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/models:upload',
        request_field='googleCloudAiplatformV1UploadModelRequest',
        request_type_name='AiplatformProjectsLocationsModelsUploadRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='',
        request_type_name='AiplatformProjectsLocationsOperationsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsOperationsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (AiplatformProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsOperationsGetRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (AiplatformProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='aiplatform.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1/{+name}/operations',
        request_field='',
        request_type_name='AiplatformProjectsLocationsOperationsListRequest',
        response_type_name='GoogleLongrunningListOperationsResponse',
        supports_download=False,
    )

    def Wait(self, request, global_params=None):
      r"""Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.

      Args:
        request: (AiplatformProjectsLocationsOperationsWaitRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Wait')
      return self._RunMethod(
          config, request, global_params=global_params)

    Wait.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:wait',
        http_method='POST',
        method_id='aiplatform.projects.locations.operations.wait',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['timeout'],
        relative_path='v1/{+name}:wait',
        request_field='',
        request_type_name='AiplatformProjectsLocationsOperationsWaitRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsPipelineJobsService(base_api.BaseApiService):
    """Service class for the projects_locations_pipelineJobs resource."""

    _NAME = 'projects_locations_pipelineJobs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsPipelineJobsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a PipelineJob. Starts asynchronous cancellation on the PipelineJob. The server makes a best effort to cancel the pipeline, but success is not guaranteed. Clients can use PipelineService.GetPipelineJob or other methods to check whether the cancellation succeeded or whether the pipeline completed despite cancellation. On successful cancellation, the PipelineJob is not deleted; instead it becomes a pipeline with a PipelineJob.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`, and PipelineJob.state is set to `CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelineJobs/{pipelineJobsId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.pipelineJobs.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleCloudAiplatformV1CancelPipelineJobRequest',
        request_type_name='AiplatformProjectsLocationsPipelineJobsCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a PipelineJob. A PipelineJob will run immediately when created.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1PipelineJob) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelineJobs',
        http_method='POST',
        method_id='aiplatform.projects.locations.pipelineJobs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pipelineJobId'],
        relative_path='v1/{+parent}/pipelineJobs',
        request_field='googleCloudAiplatformV1PipelineJob',
        request_type_name='AiplatformProjectsLocationsPipelineJobsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1PipelineJob',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a PipelineJob.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelineJobs/{pipelineJobsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.pipelineJobs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsPipelineJobsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a PipelineJob.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1PipelineJob) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelineJobs/{pipelineJobsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.pipelineJobs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsPipelineJobsGetRequest',
        response_type_name='GoogleCloudAiplatformV1PipelineJob',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists PipelineJobs in a Location.

      Args:
        request: (AiplatformProjectsLocationsPipelineJobsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListPipelineJobsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/pipelineJobs',
        http_method='GET',
        method_id='aiplatform.projects.locations.pipelineJobs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/pipelineJobs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsPipelineJobsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListPipelineJobsResponse',
        supports_download=False,
    )

  class ProjectsLocationsSpecialistPoolsService(base_api.BaseApiService):
    """Service class for the projects_locations_specialistPools resource."""

    _NAME = 'projects_locations_specialistPools'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsSpecialistPoolsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a SpecialistPool.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/specialistPools',
        http_method='POST',
        method_id='aiplatform.projects.locations.specialistPools.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/specialistPools',
        request_field='googleCloudAiplatformV1SpecialistPool',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a SpecialistPool as well as all Specialists in the pool.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/specialistPools/{specialistPoolsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.specialistPools.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a SpecialistPool.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1SpecialistPool) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/specialistPools/{specialistPoolsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.specialistPools.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsGetRequest',
        response_type_name='GoogleCloudAiplatformV1SpecialistPool',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists SpecialistPools in a Location.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListSpecialistPoolsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/specialistPools',
        http_method='GET',
        method_id='aiplatform.projects.locations.specialistPools.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/specialistPools',
        request_field='',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListSpecialistPoolsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a SpecialistPool.

      Args:
        request: (AiplatformProjectsLocationsSpecialistPoolsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/specialistPools/{specialistPoolsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.specialistPools.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1SpecialistPool',
        request_type_name='AiplatformProjectsLocationsSpecialistPoolsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsStudiesTrialsService(base_api.BaseApiService):
    """Service class for the projects_locations_studies_trials resource."""

    _NAME = 'projects_locations_studies_trials'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsStudiesTrialsService, self).__init__(client)
      self._upload_configs = {
          }

    def AddTrialMeasurement(self, request, global_params=None):
      r"""Adds a measurement of the objective metrics to a Trial. This measurement is assumed to have been taken before the Trial is complete.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsAddTrialMeasurementRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Trial) The response message.
      """
      config = self.GetMethodConfig('AddTrialMeasurement')
      return self._RunMethod(
          config, request, global_params=global_params)

    AddTrialMeasurement.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}:addTrialMeasurement',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.addTrialMeasurement',
        ordered_params=['trialName'],
        path_params=['trialName'],
        query_params=[],
        relative_path='v1/{+trialName}:addTrialMeasurement',
        request_field='googleCloudAiplatformV1AddTrialMeasurementRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsAddTrialMeasurementRequest',
        response_type_name='GoogleCloudAiplatformV1Trial',
        supports_download=False,
    )

    def CheckTrialEarlyStoppingState(self, request, global_params=None):
      r"""Checks whether a Trial should stop or not. Returns a long-running operation. When the operation is successful, it will contain a CheckTrialEarlyStoppingStateResponse.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsCheckTrialEarlyStoppingStateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('CheckTrialEarlyStoppingState')
      return self._RunMethod(
          config, request, global_params=global_params)

    CheckTrialEarlyStoppingState.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}:checkTrialEarlyStoppingState',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.checkTrialEarlyStoppingState',
        ordered_params=['trialName'],
        path_params=['trialName'],
        query_params=[],
        relative_path='v1/{+trialName}:checkTrialEarlyStoppingState',
        request_field='googleCloudAiplatformV1CheckTrialEarlyStoppingStateRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsCheckTrialEarlyStoppingStateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Complete(self, request, global_params=None):
      r"""Marks a Trial as complete.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsCompleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Trial) The response message.
      """
      config = self.GetMethodConfig('Complete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Complete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}:complete',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.complete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:complete',
        request_field='googleCloudAiplatformV1CompleteTrialRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsCompleteRequest',
        response_type_name='GoogleCloudAiplatformV1Trial',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Adds a user provided Trial to a Study.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Trial) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/trials',
        request_field='googleCloudAiplatformV1Trial',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1Trial',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Trial.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.studies.trials.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Trial.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Trial) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.studies.trials.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsGetRequest',
        response_type_name='GoogleCloudAiplatformV1Trial',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists the Trials associated with a Study.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListTrialsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials',
        http_method='GET',
        method_id='aiplatform.projects.locations.studies.trials.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/trials',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListTrialsResponse',
        supports_download=False,
    )

    def ListOptimalTrials(self, request, global_params=None):
      r"""Lists the pareto-optimal Trials for multi-objective Study or the optimal Trials for single-objective Study. The definition of pareto-optimal can be checked in wiki page. https://en.wikipedia.org/wiki/Pareto_efficiency.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsListOptimalTrialsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListOptimalTrialsResponse) The response message.
      """
      config = self.GetMethodConfig('ListOptimalTrials')
      return self._RunMethod(
          config, request, global_params=global_params)

    ListOptimalTrials.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials:listOptimalTrials',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.listOptimalTrials',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/trials:listOptimalTrials',
        request_field='googleCloudAiplatformV1ListOptimalTrialsRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsListOptimalTrialsRequest',
        response_type_name='GoogleCloudAiplatformV1ListOptimalTrialsResponse',
        supports_download=False,
    )

    def Stop(self, request, global_params=None):
      r"""Stops a Trial.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsStopRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Trial) The response message.
      """
      config = self.GetMethodConfig('Stop')
      return self._RunMethod(
          config, request, global_params=global_params)

    Stop.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials/{trialsId}:stop',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.stop',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:stop',
        request_field='googleCloudAiplatformV1StopTrialRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsStopRequest',
        response_type_name='GoogleCloudAiplatformV1Trial',
        supports_download=False,
    )

    def Suggest(self, request, global_params=None):
      r"""Adds one or more Trials to a Study, with parameter values suggested by Vertex AI Vizier. Returns a long-running operation associated with the generation of Trial suggestions. When this long-running operation succeeds, it will contain a SuggestTrialsResponse.

      Args:
        request: (AiplatformProjectsLocationsStudiesTrialsSuggestRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Suggest')
      return self._RunMethod(
          config, request, global_params=global_params)

    Suggest.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}/trials:suggest',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.trials.suggest',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/trials:suggest',
        request_field='googleCloudAiplatformV1SuggestTrialsRequest',
        request_type_name='AiplatformProjectsLocationsStudiesTrialsSuggestRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

  class ProjectsLocationsStudiesService(base_api.BaseApiService):
    """Service class for the projects_locations_studies resource."""

    _NAME = 'projects_locations_studies'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsStudiesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Study. A resource name will be generated after creation of the Study.

      Args:
        request: (AiplatformProjectsLocationsStudiesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Study) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/studies',
        request_field='googleCloudAiplatformV1Study',
        request_type_name='AiplatformProjectsLocationsStudiesCreateRequest',
        response_type_name='GoogleCloudAiplatformV1Study',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Study.

      Args:
        request: (AiplatformProjectsLocationsStudiesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.studies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesDeleteRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Study by name.

      Args:
        request: (AiplatformProjectsLocationsStudiesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Study) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies/{studiesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.studies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesGetRequest',
        response_type_name='GoogleCloudAiplatformV1Study',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists all the studies in a region for an associated project.

      Args:
        request: (AiplatformProjectsLocationsStudiesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListStudiesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies',
        http_method='GET',
        method_id='aiplatform.projects.locations.studies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1/{+parent}/studies',
        request_field='',
        request_type_name='AiplatformProjectsLocationsStudiesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListStudiesResponse',
        supports_download=False,
    )

    def Lookup(self, request, global_params=None):
      r"""Looks a study up using the user-defined display_name field instead of the fully qualified resource name.

      Args:
        request: (AiplatformProjectsLocationsStudiesLookupRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Study) The response message.
      """
      config = self.GetMethodConfig('Lookup')
      return self._RunMethod(
          config, request, global_params=global_params)

    Lookup.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/studies:lookup',
        http_method='POST',
        method_id='aiplatform.projects.locations.studies.lookup',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/studies:lookup',
        request_field='googleCloudAiplatformV1LookupStudyRequest',
        request_type_name='AiplatformProjectsLocationsStudiesLookupRequest',
        response_type_name='GoogleCloudAiplatformV1Study',
        supports_download=False,
    )

  class ProjectsLocationsTensorboardsExperimentsRunsTimeSeriesService(base_api.BaseApiService):
    """Service class for the projects_locations_tensorboards_experiments_runs_timeSeries resource."""

    _NAME = 'projects_locations_tensorboards_experiments_runs_timeSeries'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsTensorboardsExperimentsRunsTimeSeriesService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Batch create TensorboardTimeSeries that belong to a TensorboardExperiment.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1BatchCreateTensorboardTimeSeriesResponse) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries:batchCreate',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.batchCreate',
        ordered_params=['parent', 'runsId'],
        path_params=['parent', 'runsId'],
        query_params=[],
        relative_path='v1/{+parent}/runs/{runsId}/timeSeries:batchCreate',
        request_field='googleCloudAiplatformV1BatchCreateTensorboardTimeSeriesRequest',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesBatchCreateRequest',
        response_type_name='GoogleCloudAiplatformV1BatchCreateTensorboardTimeSeriesResponse',
        supports_download=False,
    )

    def BatchRead(self, request, global_params=None):
      r"""Reads multiple TensorboardTimeSeries' data. The data point number limit is 1000 for scalars, 100 for tensors and blob references. If the number of data points stored is less than the limit, all data is returned. Otherwise, the number limit of data points is randomly selected from this time series and returned.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesBatchReadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1BatchReadTensorboardTimeSeriesDataResponse) The response message.
      """
      config = self.GetMethodConfig('BatchRead')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchRead.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries:batchRead',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.batchRead',
        ordered_params=['tensorboard', 'experimentsId', 'runsId'],
        path_params=['experimentsId', 'runsId', 'tensorboard'],
        query_params=['timeSeries'],
        relative_path='v1/{+tensorboard}/experiments/{experimentsId}/runs/{runsId}/timeSeries:batchRead',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesBatchReadRequest',
        response_type_name='GoogleCloudAiplatformV1BatchReadTensorboardTimeSeriesDataResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a TensorboardTimeSeries.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardTimeSeries) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['tensorboardTimeSeriesId'],
        relative_path='v1/{+parent}/timeSeries',
        request_field='googleCloudAiplatformV1TensorboardTimeSeries',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesCreateRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardTimeSeries',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a TensorboardTimeSeries.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def ExportTensorboardTimeSeries(self, request, global_params=None):
      r"""Exports a TensorboardTimeSeries' data. Data is returned in paginated responses.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesExportTensorboardTimeSeriesRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ExportTensorboardTimeSeriesDataResponse) The response message.
      """
      config = self.GetMethodConfig('ExportTensorboardTimeSeries')
      return self._RunMethod(
          config, request, global_params=global_params)

    ExportTensorboardTimeSeries.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}:exportTensorboardTimeSeries',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.exportTensorboardTimeSeries',
        ordered_params=['tensorboardTimeSeries'],
        path_params=['tensorboardTimeSeries'],
        query_params=[],
        relative_path='v1/{+tensorboardTimeSeries}:exportTensorboardTimeSeries',
        request_field='googleCloudAiplatformV1ExportTensorboardTimeSeriesDataRequest',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesExportTensorboardTimeSeriesRequest',
        response_type_name='GoogleCloudAiplatformV1ExportTensorboardTimeSeriesDataResponse',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a TensorboardTimeSeries.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardTimeSeries) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesGetRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardTimeSeries',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TensorboardTimeSeries in a Location.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListTensorboardTimeSeriesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/timeSeries',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListTensorboardTimeSeriesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a TensorboardTimeSeries.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardTimeSeries) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1TensorboardTimeSeries',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesPatchRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardTimeSeries',
        supports_download=False,
    )

    def Read(self, request, global_params=None):
      r"""Reads a TensorboardTimeSeries' data. By default, if the number of data points stored is less than 1000, all data is returned. Otherwise, 1000 data points is randomly selected from this time series and returned. This value can be changed by changing max_data_points, which can't be greater than 10k.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesReadRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ReadTensorboardTimeSeriesDataResponse) The response message.
      """
      config = self.GetMethodConfig('Read')
      return self._RunMethod(
          config, request, global_params=global_params)

    Read.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}:read',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.read',
        ordered_params=['tensorboardTimeSeries'],
        path_params=['tensorboardTimeSeries'],
        query_params=['filter', 'maxDataPoints'],
        relative_path='v1/{+tensorboardTimeSeries}:read',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesReadRequest',
        response_type_name='GoogleCloudAiplatformV1ReadTensorboardTimeSeriesDataResponse',
        supports_download=False,
    )

    def ReadBlobData(self, request, global_params=None):
      r"""Gets bytes of TensorboardBlobs. This is to allow reading blob data stored in consumer project's Cloud Storage bucket without users having to obtain Cloud Storage access permission.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesReadBlobDataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ReadTensorboardBlobDataResponse) The response message.
      """
      config = self.GetMethodConfig('ReadBlobData')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReadBlobData.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}/timeSeries/{timeSeriesId}:readBlobData',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.timeSeries.readBlobData',
        ordered_params=['timeSeries'],
        path_params=['timeSeries'],
        query_params=['blobIds'],
        relative_path='v1/{+timeSeries}:readBlobData',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsTimeSeriesReadBlobDataRequest',
        response_type_name='GoogleCloudAiplatformV1ReadTensorboardBlobDataResponse',
        supports_download=False,
    )

  class ProjectsLocationsTensorboardsExperimentsRunsService(base_api.BaseApiService):
    """Service class for the projects_locations_tensorboards_experiments_runs resource."""

    _NAME = 'projects_locations_tensorboards_experiments_runs'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsTensorboardsExperimentsRunsService, self).__init__(client)
      self._upload_configs = {
          }

    def BatchCreate(self, request, global_params=None):
      r"""Batch create TensorboardRuns.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsBatchCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1BatchCreateTensorboardRunsResponse) The response message.
      """
      config = self.GetMethodConfig('BatchCreate')
      return self._RunMethod(
          config, request, global_params=global_params)

    BatchCreate.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs:batchCreate',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.batchCreate',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/runs:batchCreate',
        request_field='googleCloudAiplatformV1BatchCreateTensorboardRunsRequest',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsBatchCreateRequest',
        response_type_name='GoogleCloudAiplatformV1BatchCreateTensorboardRunsResponse',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a TensorboardRun.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardRun) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['tensorboardRunId'],
        relative_path='v1/{+parent}/runs',
        request_field='googleCloudAiplatformV1TensorboardRun',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardRun',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a TensorboardRun.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a TensorboardRun.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardRun) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsGetRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardRun',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TensorboardRuns in a Location.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListTensorboardRunsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/runs',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListTensorboardRunsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a TensorboardRun.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsRunsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardRun) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1TensorboardRun',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsRunsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardRun',
        supports_download=False,
    )

    def Write(self, request, global_params=None):
      r"""Write time series data points into multiple TensorboardTimeSeries under a TensorboardRun. If any data fail to be ingested, an error is returned.

      Args:
        request: (GoogleCloudAiplatformV1WriteTensorboardRunDataRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1WriteTensorboardRunDataResponse) The response message.
      """
      config = self.GetMethodConfig('Write')
      return self._RunMethod(
          config, request, global_params=global_params)

    Write.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}/runs/{runsId}:write',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.runs.write',
        ordered_params=['tensorboardRun'],
        path_params=['tensorboardRun'],
        query_params=[],
        relative_path='v1/{+tensorboardRun}:write',
        request_field='<request>',
        request_type_name='GoogleCloudAiplatformV1WriteTensorboardRunDataRequest',
        response_type_name='GoogleCloudAiplatformV1WriteTensorboardRunDataResponse',
        supports_download=False,
    )

  class ProjectsLocationsTensorboardsExperimentsService(base_api.BaseApiService):
    """Service class for the projects_locations_tensorboards_experiments resource."""

    _NAME = 'projects_locations_tensorboards_experiments'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsTensorboardsExperimentsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a TensorboardExperiment.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardExperiment) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['tensorboardExperimentId'],
        relative_path='v1/{+parent}/experiments',
        request_field='googleCloudAiplatformV1TensorboardExperiment',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsCreateRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardExperiment',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a TensorboardExperiment.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.tensorboards.experiments.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a TensorboardExperiment.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardExperiment) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsGetRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardExperiment',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TensorboardExperiments in a Location.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListTensorboardExperimentsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.experiments.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/experiments',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListTensorboardExperimentsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a TensorboardExperiment.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TensorboardExperiment) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.tensorboards.experiments.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1TensorboardExperiment',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsPatchRequest',
        response_type_name='GoogleCloudAiplatformV1TensorboardExperiment',
        supports_download=False,
    )

    def Write(self, request, global_params=None):
      r"""Write time series data points of multiple TensorboardTimeSeries in multiple TensorboardRun's. If any data fail to be ingested, an error is returned.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsExperimentsWriteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1WriteTensorboardExperimentDataResponse) The response message.
      """
      config = self.GetMethodConfig('Write')
      return self._RunMethod(
          config, request, global_params=global_params)

    Write.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}/experiments/{experimentsId}:write',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.experiments.write',
        ordered_params=['tensorboardExperiment'],
        path_params=['tensorboardExperiment'],
        query_params=[],
        relative_path='v1/{+tensorboardExperiment}:write',
        request_field='googleCloudAiplatformV1WriteTensorboardExperimentDataRequest',
        request_type_name='AiplatformProjectsLocationsTensorboardsExperimentsWriteRequest',
        response_type_name='GoogleCloudAiplatformV1WriteTensorboardExperimentDataResponse',
        supports_download=False,
    )

  class ProjectsLocationsTensorboardsService(base_api.BaseApiService):
    """Service class for the projects_locations_tensorboards resource."""

    _NAME = 'projects_locations_tensorboards'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsTensorboardsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a Tensorboard.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards',
        http_method='POST',
        method_id='aiplatform.projects.locations.tensorboards.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/tensorboards',
        request_field='googleCloudAiplatformV1Tensorboard',
        request_type_name='AiplatformProjectsLocationsTensorboardsCreateRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a Tensorboard.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.tensorboards.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a Tensorboard.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1Tensorboard) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsGetRequest',
        response_type_name='GoogleCloudAiplatformV1Tensorboard',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Tensorboards in a Location.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListTensorboardsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/tensorboards',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsListRequest',
        response_type_name='GoogleCloudAiplatformV1ListTensorboardsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates a Tensorboard.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}',
        http_method='PATCH',
        method_id='aiplatform.projects.locations.tensorboards.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1/{+name}',
        request_field='googleCloudAiplatformV1Tensorboard',
        request_type_name='AiplatformProjectsLocationsTensorboardsPatchRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def ReadUsage(self, request, global_params=None):
      r"""Returns a list of monthly active users for a given TensorBoard instance.

      Args:
        request: (AiplatformProjectsLocationsTensorboardsReadUsageRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ReadTensorboardUsageResponse) The response message.
      """
      config = self.GetMethodConfig('ReadUsage')
      return self._RunMethod(
          config, request, global_params=global_params)

    ReadUsage.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/tensorboards/{tensorboardsId}:readUsage',
        http_method='GET',
        method_id='aiplatform.projects.locations.tensorboards.readUsage',
        ordered_params=['tensorboard'],
        path_params=['tensorboard'],
        query_params=[],
        relative_path='v1/{+tensorboard}:readUsage',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTensorboardsReadUsageRequest',
        response_type_name='GoogleCloudAiplatformV1ReadTensorboardUsageResponse',
        supports_download=False,
    )

  class ProjectsLocationsTrainingPipelinesService(base_api.BaseApiService):
    """Service class for the projects_locations_trainingPipelines resource."""

    _NAME = 'projects_locations_trainingPipelines'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsTrainingPipelinesService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Cancels a TrainingPipeline. Starts asynchronous cancellation on the TrainingPipeline. The server makes a best effort to cancel the pipeline, but success is not guaranteed. Clients can use PipelineService.GetTrainingPipeline or other methods to check whether the cancellation succeeded or whether the pipeline completed despite cancellation. On successful cancellation, the TrainingPipeline is not deleted; instead it becomes a pipeline with a TrainingPipeline.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`, and TrainingPipeline.state is set to `CANCELLED`.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleProtobufEmpty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/trainingPipelines/{trainingPipelinesId}:cancel',
        http_method='POST',
        method_id='aiplatform.projects.locations.trainingPipelines.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}:cancel',
        request_field='googleCloudAiplatformV1CancelTrainingPipelineRequest',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesCancelRequest',
        response_type_name='GoogleProtobufEmpty',
        supports_download=False,
    )

    def Create(self, request, global_params=None):
      r"""Creates a TrainingPipeline. A created TrainingPipeline right away will be attempted to be run.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TrainingPipeline) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/trainingPipelines',
        http_method='POST',
        method_id='aiplatform.projects.locations.trainingPipelines.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=[],
        relative_path='v1/{+parent}/trainingPipelines',
        request_field='googleCloudAiplatformV1TrainingPipeline',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesCreateRequest',
        response_type_name='GoogleCloudAiplatformV1TrainingPipeline',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a TrainingPipeline.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleLongrunningOperation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/trainingPipelines/{trainingPipelinesId}',
        http_method='DELETE',
        method_id='aiplatform.projects.locations.trainingPipelines.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesDeleteRequest',
        response_type_name='GoogleLongrunningOperation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets a TrainingPipeline.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1TrainingPipeline) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/trainingPipelines/{trainingPipelinesId}',
        http_method='GET',
        method_id='aiplatform.projects.locations.trainingPipelines.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1/{+name}',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesGetRequest',
        response_type_name='GoogleCloudAiplatformV1TrainingPipeline',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TrainingPipelines in a Location.

      Args:
        request: (AiplatformProjectsLocationsTrainingPipelinesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GoogleCloudAiplatformV1ListTrainingPipelinesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1/projects/{projectsId}/locations/{locationsId}/trainingPipelines',
        http_method='GET',
        method_id='aiplatform.projects.locations.trainingPipelines.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'pageSize', 'pageToken', 'readMask'],
        relative_path='v1/{+parent}/trainingPipelines',
        request_field='',
        request_type_name='AiplatformProjectsLocationsTrainingPipelinesListRequest',
        response_type_name='GoogleCloudAiplatformV1ListTrainingPipelinesResponse',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(AiplatformV1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(AiplatformV1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

"""Generated message classes for bigquery version v2.

A data platform for customers to create, manage, share and query data.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import message_types as _message_types
from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'bigquery'


class AggregateClassificationMetrics(_messages.Message):
  r"""Aggregate metrics for classification/classifier models. For multi-class
  models, the metrics are either macro-averaged or micro-averaged. When macro-
  averaged, the metrics are calculated for each label and then an unweighted
  average is taken of those values. When micro-averaged, the metric is
  calculated globally by counting the total number of correctly predicted
  rows.

  Fields:
    accuracy: Accuracy is the fraction of predictions given the correct label.
      For multiclass this is a micro-averaged metric.
    f1Score: The F1 score is an average of recall and precision. For
      multiclass this is a macro-averaged metric.
    logLoss: Logarithmic Loss. For multiclass this is a macro-averaged metric.
    precision: Precision is the fraction of actual positive predictions that
      had positive actual labels. For multiclass this is a macro-averaged
      metric treating each class as a binary classifier.
    recall: Recall is the fraction of actual positive labels that were given a
      positive prediction. For multiclass this is a macro-averaged metric.
    rocAuc: Area Under a ROC Curve. For multiclass this is a macro-averaged
      metric.
    threshold: Threshold at which the metrics are computed. For binary
      classification models this is the positive class threshold. For multi-
      class classfication models this is the confidence threshold.
  """

  accuracy = _messages.FloatField(1)
  f1Score = _messages.FloatField(2)
  logLoss = _messages.FloatField(3)
  precision = _messages.FloatField(4)
  recall = _messages.FloatField(5)
  rocAuc = _messages.FloatField(6)
  threshold = _messages.FloatField(7)


class Argument(_messages.Message):
  r"""Input/output argument of a function or a stored procedure.

  Enums:
    ArgumentKindValueValuesEnum: Optional. Defaults to FIXED_TYPE.
    ModeValueValuesEnum: Optional. Specifies whether the argument is input or
      output. Can be set for procedures only.

  Fields:
    argumentKind: Optional. Defaults to FIXED_TYPE.
    dataType: Required unless argument_kind = ANY_TYPE.
    mode: Optional. Specifies whether the argument is input or output. Can be
      set for procedures only.
    name: Optional. The name of this argument. Can be absent for function
      return argument.
  """

  class ArgumentKindValueValuesEnum(_messages.Enum):
    r"""Optional. Defaults to FIXED_TYPE.

    Values:
      ARGUMENT_KIND_UNSPECIFIED: <no description>
      FIXED_TYPE: The argument is a variable with fully specified type, which
        can be a struct or an array, but not a table.
      ANY_TYPE: The argument is any type, including struct or array, but not a
        table. To be added: FIXED_TABLE, ANY_TABLE
    """
    ARGUMENT_KIND_UNSPECIFIED = 0
    FIXED_TYPE = 1
    ANY_TYPE = 2

  class ModeValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies whether the argument is input or output. Can be
    set for procedures only.

    Values:
      MODE_UNSPECIFIED: <no description>
      IN: The argument is input-only.
      OUT: The argument is output-only.
      INOUT: The argument is both an input and an output.
    """
    MODE_UNSPECIFIED = 0
    IN = 1
    OUT = 2
    INOUT = 3

  argumentKind = _messages.EnumField('ArgumentKindValueValuesEnum', 1)
  dataType = _messages.MessageField('StandardSqlDataType', 2)
  mode = _messages.EnumField('ModeValueValuesEnum', 3)
  name = _messages.StringField(4)


class ArimaCoefficients(_messages.Message):
  r"""Arima coefficients.

  Fields:
    autoRegressiveCoefficients: Auto-regressive coefficients, an array of
      double.
    interceptCoefficient: Intercept coefficient, just a double not an array.
    movingAverageCoefficients: Moving-average coefficients, an array of
      double.
  """

  autoRegressiveCoefficients = _messages.FloatField(1, repeated=True)
  interceptCoefficient = _messages.FloatField(2)
  movingAverageCoefficients = _messages.FloatField(3, repeated=True)


class ArimaFittingMetrics(_messages.Message):
  r"""ARIMA model fitting metrics.

  Fields:
    aic: AIC.
    logLikelihood: Log-likelihood.
    variance: Variance.
  """

  aic = _messages.FloatField(1)
  logLikelihood = _messages.FloatField(2)
  variance = _messages.FloatField(3)


class ArimaForecastingMetrics(_messages.Message):
  r"""Model evaluation metrics for ARIMA forecasting models.

  Enums:
    SeasonalPeriodsValueListEntryValuesEnum:

  Fields:
    arimaFittingMetrics: Arima model fitting metrics.
    arimaSingleModelForecastingMetrics: Repeated as there can be many metric
      sets (one for each model) in auto-arima and the large-scale case.
    hasDrift: Whether Arima model fitted with drift or not. It is always false
      when d is not 1.
    nonSeasonalOrder: Non-seasonal order.
    seasonalPeriods: Seasonal periods. Repeated because multiple periods are
      supported for one time series.
    timeSeriesId: Id to differentiate different time series for the large-
      scale case.
  """

  class SeasonalPeriodsValueListEntryValuesEnum(_messages.Enum):
    r"""SeasonalPeriodsValueListEntryValuesEnum enum type.

    Values:
      SEASONAL_PERIOD_TYPE_UNSPECIFIED: Unspecified seasonal period.
      NO_SEASONALITY: No seasonality
      DAILY: Daily period, 24 hours.
      WEEKLY: Weekly period, 7 days.
      MONTHLY: Monthly period, 30 days or irregular.
      QUARTERLY: Quarterly period, 90 days or irregular.
      YEARLY: Yearly period, 365 days or irregular.
    """
    SEASONAL_PERIOD_TYPE_UNSPECIFIED = 0
    NO_SEASONALITY = 1
    DAILY = 2
    WEEKLY = 3
    MONTHLY = 4
    QUARTERLY = 5
    YEARLY = 6

  arimaFittingMetrics = _messages.MessageField('ArimaFittingMetrics', 1, repeated=True)
  arimaSingleModelForecastingMetrics = _messages.MessageField('ArimaSingleModelForecastingMetrics', 2, repeated=True)
  hasDrift = _messages.BooleanField(3, repeated=True)
  nonSeasonalOrder = _messages.MessageField('ArimaOrder', 4, repeated=True)
  seasonalPeriods = _messages.EnumField('SeasonalPeriodsValueListEntryValuesEnum', 5, repeated=True)
  timeSeriesId = _messages.StringField(6, repeated=True)


class ArimaModelInfo(_messages.Message):
  r"""Arima model information.

  Enums:
    SeasonalPeriodsValueListEntryValuesEnum:

  Fields:
    arimaCoefficients: Arima coefficients.
    arimaFittingMetrics: Arima fitting metrics.
    hasDrift: Whether Arima model fitted with drift or not. It is always false
      when d is not 1.
    hasHolidayEffect: If true, holiday_effect is a part of time series
      decomposition result.
    hasSpikesAndDips: If true, spikes_and_dips is a part of time series
      decomposition result.
    hasStepChanges: If true, step_changes is a part of time series
      decomposition result.
    nonSeasonalOrder: Non-seasonal order.
    seasonalPeriods: Seasonal periods. Repeated because multiple periods are
      supported for one time series.
    timeSeriesId: The time_series_id value for this time series. It will be
      one of the unique values from the time_series_id_column specified during
      ARIMA model training. Only present when time_series_id_column training
      option was used.
    timeSeriesIds: The tuple of time_series_ids identifying this time series.
      It will be one of the unique tuples of values present in the
      time_series_id_columns specified during ARIMA model training. Only
      present when time_series_id_columns training option was used and the
      order of values here are same as the order of time_series_id_columns.
  """

  class SeasonalPeriodsValueListEntryValuesEnum(_messages.Enum):
    r"""SeasonalPeriodsValueListEntryValuesEnum enum type.

    Values:
      SEASONAL_PERIOD_TYPE_UNSPECIFIED: Unspecified seasonal period.
      NO_SEASONALITY: No seasonality
      DAILY: Daily period, 24 hours.
      WEEKLY: Weekly period, 7 days.
      MONTHLY: Monthly period, 30 days or irregular.
      QUARTERLY: Quarterly period, 90 days or irregular.
      YEARLY: Yearly period, 365 days or irregular.
    """
    SEASONAL_PERIOD_TYPE_UNSPECIFIED = 0
    NO_SEASONALITY = 1
    DAILY = 2
    WEEKLY = 3
    MONTHLY = 4
    QUARTERLY = 5
    YEARLY = 6

  arimaCoefficients = _messages.MessageField('ArimaCoefficients', 1)
  arimaFittingMetrics = _messages.MessageField('ArimaFittingMetrics', 2)
  hasDrift = _messages.BooleanField(3)
  hasHolidayEffect = _messages.BooleanField(4)
  hasSpikesAndDips = _messages.BooleanField(5)
  hasStepChanges = _messages.BooleanField(6)
  nonSeasonalOrder = _messages.MessageField('ArimaOrder', 7)
  seasonalPeriods = _messages.EnumField('SeasonalPeriodsValueListEntryValuesEnum', 8, repeated=True)
  timeSeriesId = _messages.StringField(9)
  timeSeriesIds = _messages.StringField(10, repeated=True)


class ArimaOrder(_messages.Message):
  r"""Arima order, can be used for both non-seasonal and seasonal parts.

  Fields:
    d: Order of the differencing part.
    p: Order of the autoregressive part.
    q: Order of the moving-average part.
  """

  d = _messages.IntegerField(1)
  p = _messages.IntegerField(2)
  q = _messages.IntegerField(3)


class ArimaResult(_messages.Message):
  r"""(Auto-)arima fitting result. Wrap everything in ArimaResult for easier
  refactoring if we want to use model-specific iteration results.

  Enums:
    SeasonalPeriodsValueListEntryValuesEnum:

  Fields:
    arimaModelInfo: This message is repeated because there are multiple arima
      models fitted in auto-arima. For non-auto-arima model, its size is one.
    seasonalPeriods: Seasonal periods. Repeated because multiple periods are
      supported for one time series.
  """

  class SeasonalPeriodsValueListEntryValuesEnum(_messages.Enum):
    r"""SeasonalPeriodsValueListEntryValuesEnum enum type.

    Values:
      SEASONAL_PERIOD_TYPE_UNSPECIFIED: Unspecified seasonal period.
      NO_SEASONALITY: No seasonality
      DAILY: Daily period, 24 hours.
      WEEKLY: Weekly period, 7 days.
      MONTHLY: Monthly period, 30 days or irregular.
      QUARTERLY: Quarterly period, 90 days or irregular.
      YEARLY: Yearly period, 365 days or irregular.
    """
    SEASONAL_PERIOD_TYPE_UNSPECIFIED = 0
    NO_SEASONALITY = 1
    DAILY = 2
    WEEKLY = 3
    MONTHLY = 4
    QUARTERLY = 5
    YEARLY = 6

  arimaModelInfo = _messages.MessageField('ArimaModelInfo', 1, repeated=True)
  seasonalPeriods = _messages.EnumField('SeasonalPeriodsValueListEntryValuesEnum', 2, repeated=True)


class ArimaSingleModelForecastingMetrics(_messages.Message):
  r"""Model evaluation metrics for a single ARIMA forecasting model.

  Enums:
    SeasonalPeriodsValueListEntryValuesEnum:

  Fields:
    arimaFittingMetrics: Arima fitting metrics.
    hasDrift: Is arima model fitted with drift or not. It is always false when
      d is not 1.
    hasHolidayEffect: If true, holiday_effect is a part of time series
      decomposition result.
    hasSpikesAndDips: If true, spikes_and_dips is a part of time series
      decomposition result.
    hasStepChanges: If true, step_changes is a part of time series
      decomposition result.
    nonSeasonalOrder: Non-seasonal order.
    seasonalPeriods: Seasonal periods. Repeated because multiple periods are
      supported for one time series.
    timeSeriesId: The time_series_id value for this time series. It will be
      one of the unique values from the time_series_id_column specified during
      ARIMA model training. Only present when time_series_id_column training
      option was used.
    timeSeriesIds: The tuple of time_series_ids identifying this time series.
      It will be one of the unique tuples of values present in the
      time_series_id_columns specified during ARIMA model training. Only
      present when time_series_id_columns training option was used and the
      order of values here are same as the order of time_series_id_columns.
  """

  class SeasonalPeriodsValueListEntryValuesEnum(_messages.Enum):
    r"""SeasonalPeriodsValueListEntryValuesEnum enum type.

    Values:
      SEASONAL_PERIOD_TYPE_UNSPECIFIED: Unspecified seasonal period.
      NO_SEASONALITY: No seasonality
      DAILY: Daily period, 24 hours.
      WEEKLY: Weekly period, 7 days.
      MONTHLY: Monthly period, 30 days or irregular.
      QUARTERLY: Quarterly period, 90 days or irregular.
      YEARLY: Yearly period, 365 days or irregular.
    """
    SEASONAL_PERIOD_TYPE_UNSPECIFIED = 0
    NO_SEASONALITY = 1
    DAILY = 2
    WEEKLY = 3
    MONTHLY = 4
    QUARTERLY = 5
    YEARLY = 6

  arimaFittingMetrics = _messages.MessageField('ArimaFittingMetrics', 1)
  hasDrift = _messages.BooleanField(2)
  hasHolidayEffect = _messages.BooleanField(3)
  hasSpikesAndDips = _messages.BooleanField(4)
  hasStepChanges = _messages.BooleanField(5)
  nonSeasonalOrder = _messages.MessageField('ArimaOrder', 6)
  seasonalPeriods = _messages.EnumField('SeasonalPeriodsValueListEntryValuesEnum', 7, repeated=True)
  timeSeriesId = _messages.StringField(8)
  timeSeriesIds = _messages.StringField(9, repeated=True)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 2)


class AvroOptions(_messages.Message):
  r"""A AvroOptions object.

  Fields:
    useAvroLogicalTypes: [Optional] If sourceFormat is set to "AVRO",
      indicates whether to interpret logical types as the corresponding
      BigQuery data type (for example, TIMESTAMP), instead of using the raw
      type (for example, INTEGER).
  """

  useAvroLogicalTypes = _messages.BooleanField(1)


class BiEngineReason(_messages.Message):
  r"""A BiEngineReason object.

  Fields:
    code: [Output-only] High-level BI Engine reason for partial or disabled
      acceleration.
    message: [Output-only] Free form human-readable reason for partial or
      disabled acceleration.
  """

  code = _messages.StringField(1)
  message = _messages.StringField(2)


class BiEngineStatistics(_messages.Message):
  r"""A BiEngineStatistics object.

  Fields:
    accelerationMode: [Output-only] Specifies which mode of BI Engine
      acceleration was performed (if any).
    biEngineMode: [Output-only] Specifies which mode of BI Engine acceleration
      was performed (if any).
    biEngineReasons: In case of DISABLED or PARTIAL bi_engine_mode, these
      contain the explanatory reasons as to why BI Engine could not
      accelerate. In case the full query was accelerated, this field is not
      populated.
  """

  accelerationMode = _messages.StringField(1)
  biEngineMode = _messages.StringField(2)
  biEngineReasons = _messages.MessageField('BiEngineReason', 3, repeated=True)


class BigLakeConfiguration(_messages.Message):
  r"""A BigLakeConfiguration object.

  Fields:
    connectionId: [Required] Required and immutable. Credential reference for
      accessing external storage system. Normalized as
      project_id.location_id.connection_id.
    fileFormat: [Required] Required and immutable. Open source file format
      that the table data is stored in. Currently only PARQUET is supported.
    storageUri: [Required] Required and immutable. Fully qualified location
      prefix of the external folder where data is stored. Normalized to
      standard format: "gs:////". Starts with "gs://" rather than
      "/bigstore/". Ends with "/". Does not contain "*". See also
      BigLakeStorageMetadata on how it is used.
    tableFormat: [Required] Required and immutable. Open source file format
      that the table data is stored in. Currently only PARQUET is supported.
  """

  connectionId = _messages.StringField(1)
  fileFormat = _messages.StringField(2)
  storageUri = _messages.StringField(3)
  tableFormat = _messages.StringField(4)


class BigQueryModelTraining(_messages.Message):
  r"""A BigQueryModelTraining object.

  Fields:
    currentIteration: [Output-only, Beta] Index of current ML training
      iteration. Updated during create model query job to show job progress.
    expectedTotalIterations: [Output-only, Beta] Expected number of iterations
      for the create model query job specified as num_iterations in the input
      query. The actual total number of iterations may be less than this
      number due to early stop.
  """

  currentIteration = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  expectedTotalIterations = _messages.IntegerField(2)


class BigqueryDatasetsDeleteRequest(_messages.Message):
  r"""A BigqueryDatasetsDeleteRequest object.

  Fields:
    datasetId: Dataset ID of dataset being deleted
    deleteContents: If True, delete all the tables in the dataset. If False
      and the dataset contains tables, the request will fail. Default is False
    projectId: Project ID of the dataset being deleted
  """

  datasetId = _messages.StringField(1, required=True)
  deleteContents = _messages.BooleanField(2)
  projectId = _messages.StringField(3, required=True)


class BigqueryDatasetsDeleteResponse(_messages.Message):
  r"""An empty BigqueryDatasetsDelete response."""


class BigqueryDatasetsGetRequest(_messages.Message):
  r"""A BigqueryDatasetsGetRequest object.

  Fields:
    datasetId: Dataset ID of the requested dataset
    projectId: Project ID of the requested dataset
  """

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)


class BigqueryDatasetsInsertRequest(_messages.Message):
  r"""A BigqueryDatasetsInsertRequest object.

  Fields:
    dataset: A Dataset resource to be passed as the request body.
    projectId: Project ID of the new dataset
  """

  dataset = _messages.MessageField('Dataset', 1)
  projectId = _messages.StringField(2, required=True)


class BigqueryDatasetsListRequest(_messages.Message):
  r"""A BigqueryDatasetsListRequest object.

  Fields:
    all: Whether to list all datasets, including hidden ones
    filter: An expression for filtering the results of the request by label.
      The syntax is "labels.<name>[:<value>]". Multiple filters can be ANDed
      together by connecting with a space. Example:
      "labels.department:receiving labels.active". See Filtering datasets
      using labels for details.
    maxResults: The maximum number of results to return
    pageToken: Page token, returned by a previous call, to request the next
      page of results
    projectId: Project ID of the datasets to be listed
  """

  all = _messages.BooleanField(1)
  filter = _messages.StringField(2)
  maxResults = _messages.IntegerField(3, variant=_messages.Variant.UINT32)
  pageToken = _messages.StringField(4)
  projectId = _messages.StringField(5, required=True)


class BigqueryDatasetsPatchRequest(_messages.Message):
  r"""A BigqueryDatasetsPatchRequest object.

  Fields:
    dataset: A Dataset resource to be passed as the request body.
    datasetId: Dataset ID of the dataset being updated
    projectId: Project ID of the dataset being updated
  """

  dataset = _messages.MessageField('Dataset', 1)
  datasetId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)


class BigqueryDatasetsUpdateRequest(_messages.Message):
  r"""A BigqueryDatasetsUpdateRequest object.

  Fields:
    dataset: A Dataset resource to be passed as the request body.
    datasetId: Dataset ID of the dataset being updated
    projectId: Project ID of the dataset being updated
  """

  dataset = _messages.MessageField('Dataset', 1)
  datasetId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)


class BigqueryJobsCancelRequest(_messages.Message):
  r"""A BigqueryJobsCancelRequest object.

  Fields:
    jobId: [Required] Job ID of the job to cancel
    location: The geographic location of the job. Required except for US and
      EU. See details at https://cloud.google.com/bigquery/docs/locations#spec
      ifying_your_location.
    projectId: [Required] Project ID of the job to cancel
  """

  jobId = _messages.StringField(1, required=True)
  location = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)


class BigqueryJobsDeleteRequest(_messages.Message):
  r"""A BigqueryJobsDeleteRequest object.

  Fields:
    jobId: Required. Job ID of the job for which metadata is to be deleted. If
      this is a parent job which has child jobs, the metadata from all child
      jobs will be deleted as well. Direct deletion of the metadata of child
      jobs is not allowed.
    location: The geographic location of the job. Required. See details at:
      https://cloud.google.com/bigquery/docs/locations#specifying_your_locatio
      n.
    projectId: Required. Project ID of the job for which metadata is to be
      deleted.
  """

  jobId = _messages.StringField(1, required=True)
  location = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)


class BigqueryJobsDeleteResponse(_messages.Message):
  r"""An empty BigqueryJobsDelete response."""


class BigqueryJobsGetQueryResultsRequest(_messages.Message):
  r"""A BigqueryJobsGetQueryResultsRequest object.

  Fields:
    jobId: [Required] Job ID of the query job
    location: The geographic location where the job should run. Required
      except for US and EU. See details at https://cloud.google.com/bigquery/d
      ocs/locations#specifying_your_location.
    maxResults: Maximum number of results to read
    pageToken: Page token, returned by a previous call, to request the next
      page of results
    projectId: [Required] Project ID of the query job
    startIndex: Zero-based index of the starting row
    timeoutMs: How long to wait for the query to complete, in milliseconds,
      before returning. Default is 10 seconds. If the timeout passes before
      the job completes, the 'jobComplete' field in the response will be false
  """

  jobId = _messages.StringField(1, required=True)
  location = _messages.StringField(2)
  maxResults = _messages.IntegerField(3, variant=_messages.Variant.UINT32)
  pageToken = _messages.StringField(4)
  projectId = _messages.StringField(5, required=True)
  startIndex = _messages.IntegerField(6, variant=_messages.Variant.UINT64)
  timeoutMs = _messages.IntegerField(7, variant=_messages.Variant.UINT32)


class BigqueryJobsGetRequest(_messages.Message):
  r"""A BigqueryJobsGetRequest object.

  Fields:
    jobId: [Required] Job ID of the requested job
    location: The geographic location of the job. Required except for US and
      EU. See details at https://cloud.google.com/bigquery/docs/locations#spec
      ifying_your_location.
    projectId: [Required] Project ID of the requested job
  """

  jobId = _messages.StringField(1, required=True)
  location = _messages.StringField(2)
  projectId = _messages.StringField(3, required=True)


class BigqueryJobsInsertRequest(_messages.Message):
  r"""A BigqueryJobsInsertRequest object.

  Fields:
    job: A Job resource to be passed as the request body.
    projectId: Project ID of the project that will be billed for the job
  """

  job = _messages.MessageField('Job', 1)
  projectId = _messages.StringField(2, required=True)


class BigqueryJobsListRequest(_messages.Message):
  r"""A BigqueryJobsListRequest object.

  Enums:
    ProjectionValueValuesEnum: Restrict information returned to a set of
      selected fields
    StateFilterValueValuesEnum: Filter for job state

  Fields:
    allUsers: Whether to display jobs owned by all users in the project.
      Default false
    maxCreationTime: Max value for job creation time, in milliseconds since
      the POSIX epoch. If set, only jobs created before or at this timestamp
      are returned
    maxResults: Maximum number of results to return
    minCreationTime: Min value for job creation time, in milliseconds since
      the POSIX epoch. If set, only jobs created after or at this timestamp
      are returned
    pageToken: Page token, returned by a previous call, to request the next
      page of results
    parentJobId: If set, retrieves only jobs whose parent is this job.
      Otherwise, retrieves only jobs which have no parent
    projectId: Project ID of the jobs to list
    projection: Restrict information returned to a set of selected fields
    stateFilter: Filter for job state
  """

  class ProjectionValueValuesEnum(_messages.Enum):
    r"""Restrict information returned to a set of selected fields

    Values:
      full: Includes all job data
      minimal: Does not include the job configuration
    """
    full = 0
    minimal = 1

  class StateFilterValueValuesEnum(_messages.Enum):
    r"""Filter for job state

    Values:
      done: Finished jobs
      pending: Pending jobs
      running: Running jobs
    """
    done = 0
    pending = 1
    running = 2

  allUsers = _messages.BooleanField(1)
  maxCreationTime = _messages.IntegerField(2, variant=_messages.Variant.UINT64)
  maxResults = _messages.IntegerField(3, variant=_messages.Variant.UINT32)
  minCreationTime = _messages.IntegerField(4, variant=_messages.Variant.UINT64)
  pageToken = _messages.StringField(5)
  parentJobId = _messages.StringField(6)
  projectId = _messages.StringField(7, required=True)
  projection = _messages.EnumField('ProjectionValueValuesEnum', 8)
  stateFilter = _messages.EnumField('StateFilterValueValuesEnum', 9, repeated=True)


class BigqueryJobsQueryRequest(_messages.Message):
  r"""A BigqueryJobsQueryRequest object.

  Fields:
    projectId: Project ID of the project billed for the query
    queryRequest: A QueryRequest resource to be passed as the request body.
  """

  projectId = _messages.StringField(1, required=True)
  queryRequest = _messages.MessageField('QueryRequest', 2)


class BigqueryModelsDeleteRequest(_messages.Message):
  r"""A BigqueryModelsDeleteRequest object.

  Fields:
    datasetId: Required. Dataset ID of the model to delete.
    modelId: Required. Model ID of the model to delete.
    projectId: Required. Project ID of the model to delete.
  """

  datasetId = _messages.StringField(1, required=True)
  modelId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)


class BigqueryModelsDeleteResponse(_messages.Message):
  r"""An empty BigqueryModelsDelete response."""


class BigqueryModelsGetRequest(_messages.Message):
  r"""A BigqueryModelsGetRequest object.

  Fields:
    datasetId: Required. Dataset ID of the requested model.
    modelId: Required. Model ID of the requested model.
    projectId: Required. Project ID of the requested model.
  """

  datasetId = _messages.StringField(1, required=True)
  modelId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)


class BigqueryModelsListRequest(_messages.Message):
  r"""A BigqueryModelsListRequest object.

  Fields:
    datasetId: Required. Dataset ID of the models to list.
    maxResults: The maximum number of results to return in a single response
      page. Leverage the page tokens to iterate through the entire collection.
    pageToken: Page token, returned by a previous call to request the next
      page of results
    projectId: Required. Project ID of the models to list.
  """

  datasetId = _messages.StringField(1, required=True)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.UINT32)
  pageToken = _messages.StringField(3)
  projectId = _messages.StringField(4, required=True)


class BigqueryModelsPatchRequest(_messages.Message):
  r"""A BigqueryModelsPatchRequest object.

  Fields:
    datasetId: Required. Dataset ID of the model to patch.
    model: A Model resource to be passed as the request body.
    modelId: Required. Model ID of the model to patch.
    projectId: Required. Project ID of the model to patch.
  """

  datasetId = _messages.StringField(1, required=True)
  model = _messages.MessageField('Model', 2)
  modelId = _messages.StringField(3, required=True)
  projectId = _messages.StringField(4, required=True)


class BigqueryProjectsGetServiceAccountRequest(_messages.Message):
  r"""A BigqueryProjectsGetServiceAccountRequest object.

  Fields:
    projectId: Project ID for which the service account is requested.
  """

  projectId = _messages.StringField(1, required=True)


class BigqueryProjectsListRequest(_messages.Message):
  r"""A BigqueryProjectsListRequest object.

  Fields:
    maxResults: Maximum number of results to return
    pageToken: Page token, returned by a previous call, to request the next
      page of results
  """

  maxResults = _messages.IntegerField(1, variant=_messages.Variant.UINT32)
  pageToken = _messages.StringField(2)


class BigqueryRoutinesDeleteRequest(_messages.Message):
  r"""A BigqueryRoutinesDeleteRequest object.

  Fields:
    datasetId: Required. Dataset ID of the routine to delete
    projectId: Required. Project ID of the routine to delete
    routineId: Required. Routine ID of the routine to delete
  """

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  routineId = _messages.StringField(3, required=True)


class BigqueryRoutinesDeleteResponse(_messages.Message):
  r"""An empty BigqueryRoutinesDelete response."""


class BigqueryRoutinesGetRequest(_messages.Message):
  r"""A BigqueryRoutinesGetRequest object.

  Fields:
    datasetId: Required. Dataset ID of the requested routine
    projectId: Required. Project ID of the requested routine
    readMask: If set, only the Routine fields in the field mask are returned
      in the response. If unset, all Routine fields are returned.
    routineId: Required. Routine ID of the requested routine
  """

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  readMask = _messages.StringField(3)
  routineId = _messages.StringField(4, required=True)


class BigqueryRoutinesInsertRequest(_messages.Message):
  r"""A BigqueryRoutinesInsertRequest object.

  Fields:
    datasetId: Required. Dataset ID of the new routine
    projectId: Required. Project ID of the new routine
    routine: A Routine resource to be passed as the request body.
  """

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  routine = _messages.MessageField('Routine', 3)


class BigqueryRoutinesListRequest(_messages.Message):
  r"""A BigqueryRoutinesListRequest object.

  Fields:
    datasetId: Required. Dataset ID of the routines to list
    filter: If set, then only the Routines matching this filter are returned.
      The supported format is `routineType:{RoutineType}`, where
      `{RoutineType}` is a RoutineType enum. For example:
      `routineType:SCALAR_FUNCTION`.
    maxResults: The maximum number of results to return in a single response
      page. Leverage the page tokens to iterate through the entire collection.
    pageToken: Page token, returned by a previous call, to request the next
      page of results
    projectId: Required. Project ID of the routines to list
    readMask: If set, then only the Routine fields in the field mask, as well
      as project_id, dataset_id and routine_id, are returned in the response.
      If unset, then the following Routine fields are returned: etag,
      project_id, dataset_id, routine_id, routine_type, creation_time,
      last_modified_time, and language.
  """

  datasetId = _messages.StringField(1, required=True)
  filter = _messages.StringField(2)
  maxResults = _messages.IntegerField(3, variant=_messages.Variant.UINT32)
  pageToken = _messages.StringField(4)
  projectId = _messages.StringField(5, required=True)
  readMask = _messages.StringField(6)


class BigqueryRoutinesUpdateRequest(_messages.Message):
  r"""A BigqueryRoutinesUpdateRequest object.

  Fields:
    datasetId: Required. Dataset ID of the routine to update
    projectId: Required. Project ID of the routine to update
    routine: A Routine resource to be passed as the request body.
    routineId: Required. Routine ID of the routine to update
  """

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  routine = _messages.MessageField('Routine', 3)
  routineId = _messages.StringField(4, required=True)


class BigqueryRowAccessPoliciesGetIamPolicyRequest(_messages.Message):
  r"""A BigqueryRowAccessPoliciesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BigqueryRowAccessPoliciesListRequest(_messages.Message):
  r"""A BigqueryRowAccessPoliciesListRequest object.

  Fields:
    datasetId: Required. Dataset ID of row access policies to list.
    pageSize: The maximum number of results to return in a single response
      page. Leverage the page tokens to iterate through the entire collection.
    pageToken: Page token, returned by a previous call, to request the next
      page of results.
    projectId: Required. Project ID of the row access policies to list.
    tableId: Required. Table ID of the table to list row access policies.
  """

  datasetId = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  projectId = _messages.StringField(4, required=True)
  tableId = _messages.StringField(5, required=True)


class BigqueryRowAccessPoliciesTestIamPermissionsRequest(_messages.Message):
  r"""A BigqueryRowAccessPoliciesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class BigqueryTabledataInsertAllRequest(_messages.Message):
  r"""A BigqueryTabledataInsertAllRequest object.

  Fields:
    datasetId: Dataset ID of the destination table.
    projectId: Project ID of the destination table.
    tableDataInsertAllRequest: A TableDataInsertAllRequest resource to be
      passed as the request body.
    tableId: Table ID of the destination table.
  """

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  tableDataInsertAllRequest = _messages.MessageField('TableDataInsertAllRequest', 3)
  tableId = _messages.StringField(4, required=True)


class BigqueryTabledataListRequest(_messages.Message):
  r"""A BigqueryTabledataListRequest object.

  Fields:
    datasetId: Dataset ID of the table to read
    maxResults: Maximum number of results to return
    pageToken: Page token, returned by a previous call, identifying the result
      set
    projectId: Project ID of the table to read
    selectedFields: List of fields to return (comma-separated). If
      unspecified, all fields are returned
    startIndex: Zero-based index of the starting row to read
    tableId: Table ID of the table to read
  """

  datasetId = _messages.StringField(1, required=True)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.UINT32)
  pageToken = _messages.StringField(3)
  projectId = _messages.StringField(4, required=True)
  selectedFields = _messages.StringField(5)
  startIndex = _messages.IntegerField(6, variant=_messages.Variant.UINT64)
  tableId = _messages.StringField(7, required=True)


class BigqueryTablesDeleteRequest(_messages.Message):
  r"""A BigqueryTablesDeleteRequest object.

  Fields:
    datasetId: Dataset ID of the table to delete
    projectId: Project ID of the table to delete
    tableId: Table ID of the table to delete
  """

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  tableId = _messages.StringField(3, required=True)


class BigqueryTablesDeleteResponse(_messages.Message):
  r"""An empty BigqueryTablesDelete response."""


class BigqueryTablesGetIamPolicyRequest(_messages.Message):
  r"""A BigqueryTablesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class BigqueryTablesGetRequest(_messages.Message):
  r"""A BigqueryTablesGetRequest object.

  Enums:
    ViewValueValuesEnum: Specifies the view that determines which table
      information is returned. By default, basic table information and storage
      statistics (STORAGE_STATS) are returned.

  Fields:
    datasetId: Dataset ID of the requested table
    projectId: Project ID of the requested table
    selectedFields: List of fields to return (comma-separated). If
      unspecified, all fields are returned
    tableId: Table ID of the requested table
    view: Specifies the view that determines which table information is
      returned. By default, basic table information and storage statistics
      (STORAGE_STATS) are returned.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""Specifies the view that determines which table information is
    returned. By default, basic table information and storage statistics
    (STORAGE_STATS) are returned.

    Values:
      BASIC: Includes basic table information including schema and
        partitioning specification. This view does not include storage
        statistics such as numRows or numBytes. This view is significantly
        more efficient and should be used to support high query rates.
      FULL: Includes all table information, including storage statistics. It
        returns same information as STORAGE_STATS view, but may contain
        additional information in the future.
      STORAGE_STATS: Includes all information in the BASIC view as well as
        storage statistics (numBytes, numLongTermBytes, numRows and
        lastModifiedTime).
      TABLE_METADATA_VIEW_UNSPECIFIED: The default value. Default to the
        STORAGE_STATS view.
    """
    BASIC = 0
    FULL = 1
    STORAGE_STATS = 2
    TABLE_METADATA_VIEW_UNSPECIFIED = 3

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  selectedFields = _messages.StringField(3)
  tableId = _messages.StringField(4, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 5)


class BigqueryTablesInsertRequest(_messages.Message):
  r"""A BigqueryTablesInsertRequest object.

  Fields:
    datasetId: Dataset ID of the new table
    projectId: Project ID of the new table
    table: A Table resource to be passed as the request body.
  """

  datasetId = _messages.StringField(1, required=True)
  projectId = _messages.StringField(2, required=True)
  table = _messages.MessageField('Table', 3)


class BigqueryTablesListRequest(_messages.Message):
  r"""A BigqueryTablesListRequest object.

  Fields:
    datasetId: Dataset ID of the tables to list
    maxResults: Maximum number of results to return
    pageToken: Page token, returned by a previous call, to request the next
      page of results
    projectId: Project ID of the tables to list
  """

  datasetId = _messages.StringField(1, required=True)
  maxResults = _messages.IntegerField(2, variant=_messages.Variant.UINT32)
  pageToken = _messages.StringField(3)
  projectId = _messages.StringField(4, required=True)


class BigqueryTablesPatchRequest(_messages.Message):
  r"""A BigqueryTablesPatchRequest object.

  Fields:
    autodetect_schema: When true will autodetect schema, else will keep
      original schema
    datasetId: Dataset ID of the table to update
    projectId: Project ID of the table to update
    table: A Table resource to be passed as the request body.
    tableId: Table ID of the table to update
  """

  autodetect_schema = _messages.BooleanField(1)
  datasetId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)
  table = _messages.MessageField('Table', 4)
  tableId = _messages.StringField(5, required=True)


class BigqueryTablesSetIamPolicyRequest(_messages.Message):
  r"""A BigqueryTablesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class BigqueryTablesTestIamPermissionsRequest(_messages.Message):
  r"""A BigqueryTablesTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class BigqueryTablesUpdateRequest(_messages.Message):
  r"""A BigqueryTablesUpdateRequest object.

  Fields:
    autodetect_schema: When true will autodetect schema, else will keep
      original schema
    datasetId: Dataset ID of the table to update
    projectId: Project ID of the table to update
    table: A Table resource to be passed as the request body.
    tableId: Table ID of the table to update
  """

  autodetect_schema = _messages.BooleanField(1)
  datasetId = _messages.StringField(2, required=True)
  projectId = _messages.StringField(3, required=True)
  table = _messages.MessageField('Table', 4)
  tableId = _messages.StringField(5, required=True)


class BigtableColumn(_messages.Message):
  r"""A BigtableColumn object.

  Fields:
    encoding: [Optional] The encoding of the values when the type is not
      STRING. Acceptable encoding values are: TEXT - indicates values are
      alphanumeric text strings. BINARY - indicates values are encoded using
      HBase Bytes.toBytes family of functions. 'encoding' can also be set at
      the column family level. However, the setting at this level takes
      precedence if 'encoding' is set at both levels.
    fieldName: [Optional] If the qualifier is not a valid BigQuery field
      identifier i.e. does not match [a-zA-Z][a-zA-Z0-9_]*, a valid identifier
      must be provided as the column field name and is used as field name in
      queries.
    onlyReadLatest: [Optional] If this is set, only the latest version of
      value in this column are exposed. 'onlyReadLatest' can also be set at
      the column family level. However, the setting at this level takes
      precedence if 'onlyReadLatest' is set at both levels.
    qualifierEncoded: [Required] Qualifier of the column. Columns in the
      parent column family that has this exact qualifier are exposed as .
      field. If the qualifier is valid UTF-8 string, it can be specified in
      the qualifier_string field. Otherwise, a base-64 encoded value must be
      set to qualifier_encoded. The column field name is the same as the
      column qualifier. However, if the qualifier is not a valid BigQuery
      field identifier i.e. does not match [a-zA-Z][a-zA-Z0-9_]*, a valid
      identifier must be provided as field_name.
    qualifierString: A string attribute.
    type: [Optional] The type to convert the value in cells of this column.
      The values are expected to be encoded using HBase Bytes.toBytes function
      when using the BINARY encoding value. Following BigQuery types are
      allowed (case-sensitive) - BYTES STRING INTEGER FLOAT BOOLEAN Default
      type is BYTES. 'type' can also be set at the column family level.
      However, the setting at this level takes precedence if 'type' is set at
      both levels.
  """

  encoding = _messages.StringField(1)
  fieldName = _messages.StringField(2)
  onlyReadLatest = _messages.BooleanField(3)
  qualifierEncoded = _messages.BytesField(4)
  qualifierString = _messages.StringField(5)
  type = _messages.StringField(6)


class BigtableColumnFamily(_messages.Message):
  r"""A BigtableColumnFamily object.

  Fields:
    columns: [Optional] Lists of columns that should be exposed as individual
      fields as opposed to a list of (column name, value) pairs. All columns
      whose qualifier matches a qualifier in this list can be accessed as ..
      Other columns can be accessed as a list through .Column field.
    encoding: [Optional] The encoding of the values when the type is not
      STRING. Acceptable encoding values are: TEXT - indicates values are
      alphanumeric text strings. BINARY - indicates values are encoded using
      HBase Bytes.toBytes family of functions. This can be overridden for a
      specific column by listing that column in 'columns' and specifying an
      encoding for it.
    familyId: Identifier of the column family.
    onlyReadLatest: [Optional] If this is set only the latest version of value
      are exposed for all columns in this column family. This can be
      overridden for a specific column by listing that column in 'columns' and
      specifying a different setting for that column.
    type: [Optional] The type to convert the value in cells of this column
      family. The values are expected to be encoded using HBase Bytes.toBytes
      function when using the BINARY encoding value. Following BigQuery types
      are allowed (case-sensitive) - BYTES STRING INTEGER FLOAT BOOLEAN
      Default type is BYTES. This can be overridden for a specific column by
      listing that column in 'columns' and specifying a type for it.
  """

  columns = _messages.MessageField('BigtableColumn', 1, repeated=True)
  encoding = _messages.StringField(2)
  familyId = _messages.StringField(3)
  onlyReadLatest = _messages.BooleanField(4)
  type = _messages.StringField(5)


class BigtableOptions(_messages.Message):
  r"""A BigtableOptions object.

  Fields:
    columnFamilies: [Optional] List of column families to expose in the table
      schema along with their types. This list restricts the column families
      that can be referenced in queries and specifies their value types. You
      can use this list to do type conversions - see the 'type' field for more
      details. If you leave this list empty, all column families are present
      in the table schema and their values are read as BYTES. During a query
      only the column families referenced in that query are read from
      Bigtable.
    ignoreUnspecifiedColumnFamilies: [Optional] If field is true, then the
      column families that are not specified in columnFamilies list are not
      exposed in the table schema. Otherwise, they are read with BYTES type
      values. The default value is false.
    readRowkeyAsString: [Optional] If field is true, then the rowkey column
      families will be read and converted to string. Otherwise they are read
      with BYTES type values and users need to manually cast them with CAST if
      necessary. The default value is false.
  """

  columnFamilies = _messages.MessageField('BigtableColumnFamily', 1, repeated=True)
  ignoreUnspecifiedColumnFamilies = _messages.BooleanField(2)
  readRowkeyAsString = _messages.BooleanField(3)


class BinaryClassificationMetrics(_messages.Message):
  r"""Evaluation metrics for binary classification/classifier models.

  Fields:
    aggregateClassificationMetrics: Aggregate classification metrics.
    binaryConfusionMatrixList: Binary confusion matrix at multiple thresholds.
    negativeLabel: Label representing the negative class.
    positiveLabel: Label representing the positive class.
  """

  aggregateClassificationMetrics = _messages.MessageField('AggregateClassificationMetrics', 1)
  binaryConfusionMatrixList = _messages.MessageField('BinaryConfusionMatrix', 2, repeated=True)
  negativeLabel = _messages.StringField(3)
  positiveLabel = _messages.StringField(4)


class BinaryConfusionMatrix(_messages.Message):
  r"""Confusion matrix for binary classification models.

  Fields:
    accuracy: The fraction of predictions given the correct label.
    f1Score: The equally weighted average of recall and precision.
    falseNegatives: Number of false samples predicted as false.
    falsePositives: Number of false samples predicted as true.
    positiveClassThreshold: Threshold value used when computing each of the
      following metric.
    precision: The fraction of actual positive predictions that had positive
      actual labels.
    recall: The fraction of actual positive labels that were given a positive
      prediction.
    trueNegatives: Number of true samples predicted as false.
    truePositives: Number of true samples predicted as true.
  """

  accuracy = _messages.FloatField(1)
  f1Score = _messages.FloatField(2)
  falseNegatives = _messages.IntegerField(3)
  falsePositives = _messages.IntegerField(4)
  positiveClassThreshold = _messages.FloatField(5)
  precision = _messages.FloatField(6)
  recall = _messages.FloatField(7)
  trueNegatives = _messages.IntegerField(8)
  truePositives = _messages.IntegerField(9)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class BqmlIterationResult(_messages.Message):
  r"""A BqmlIterationResult object.

  Fields:
    durationMs: [Output-only, Beta] Time taken to run the training iteration
      in milliseconds.
    evalLoss: [Output-only, Beta] Eval loss computed on the eval data at the
      end of the iteration. The eval loss is used for early stopping to avoid
      overfitting. No eval loss if eval_split_method option is specified as
      no_split or auto_split with input data size less than 500 rows.
    index: [Output-only, Beta] Index of the ML training iteration, starting
      from zero for each training run.
    learnRate: [Output-only, Beta] Learning rate used for this iteration, it
      varies for different training iterations if learn_rate_strategy option
      is not constant.
    trainingLoss: [Output-only, Beta] Training loss computed on the training
      data at the end of the iteration. The training loss function is defined
      by model type.
  """

  durationMs = _messages.IntegerField(1)
  evalLoss = _messages.FloatField(2)
  index = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  learnRate = _messages.FloatField(4)
  trainingLoss = _messages.FloatField(5)


class BqmlTrainingRun(_messages.Message):
  r"""A BqmlTrainingRun object.

  Messages:
    TrainingOptionsValue: [Output-only, Beta] Training options used by this
      training run. These options are mutable for subsequent training runs.
      Default values are explicitly stored for options not specified in the
      input query of the first training run. For subsequent training runs, any
      option not explicitly specified in the input query will be copied from
      the previous training run.

  Fields:
    iterationResults: [Output-only, Beta] List of each iteration results.
    startTime: [Output-only, Beta] Training run start time in milliseconds
      since the epoch.
    state: [Output-only, Beta] Different state applicable for a training run.
      IN PROGRESS: Training run is in progress. FAILED: Training run ended due
      to a non-retryable failure. SUCCEEDED: Training run successfully
      completed. CANCELLED: Training run cancelled by the user.
    trainingOptions: [Output-only, Beta] Training options used by this
      training run. These options are mutable for subsequent training runs.
      Default values are explicitly stored for options not specified in the
      input query of the first training run. For subsequent training runs, any
      option not explicitly specified in the input query will be copied from
      the previous training run.
  """

  class TrainingOptionsValue(_messages.Message):
    r"""[Output-only, Beta] Training options used by this training run. These
    options are mutable for subsequent training runs. Default values are
    explicitly stored for options not specified in the input query of the
    first training run. For subsequent training runs, any option not
    explicitly specified in the input query will be copied from the previous
    training run.

    Fields:
      earlyStop: A boolean attribute.
      l1Reg: A number attribute.
      l2Reg: A number attribute.
      learnRate: A number attribute.
      learnRateStrategy: A string attribute.
      lineSearchInitLearnRate: A number attribute.
      maxIteration: A string attribute.
      minRelProgress: A number attribute.
      warmStart: A boolean attribute.
    """

    earlyStop = _messages.BooleanField(1)
    l1Reg = _messages.FloatField(2)
    l2Reg = _messages.FloatField(3)
    learnRate = _messages.FloatField(4)
    learnRateStrategy = _messages.StringField(5)
    lineSearchInitLearnRate = _messages.FloatField(6)
    maxIteration = _messages.IntegerField(7)
    minRelProgress = _messages.FloatField(8)
    warmStart = _messages.BooleanField(9)

  iterationResults = _messages.MessageField('BqmlIterationResult', 1, repeated=True)
  startTime = _message_types.DateTimeField(2)
  state = _messages.StringField(3)
  trainingOptions = _messages.MessageField('TrainingOptionsValue', 4)


class CategoricalValue(_messages.Message):
  r"""Representative value of a categorical feature.

  Fields:
    categoryCounts: Counts of all categories for the categorical feature. If
      there are more than ten categories, we return top ten (by count) and
      return one more CategoryCount with category "_OTHER_" and count as
      aggregate counts of remaining categories.
  """

  categoryCounts = _messages.MessageField('CategoryCount', 1, repeated=True)


class CategoryCount(_messages.Message):
  r"""Represents the count of a single category within the cluster.

  Fields:
    category: The name of category.
    count: The count of training samples matching the category within the
      cluster.
  """

  category = _messages.StringField(1)
  count = _messages.IntegerField(2)


class CloneDefinition(_messages.Message):
  r"""A CloneDefinition object.

  Fields:
    baseTableReference: [Required] Reference describing the ID of the table
      that was cloned.
    cloneTime: [Required] The time at which the base table was cloned. This
      value is reported in the JSON response using RFC3339 format.
  """

  baseTableReference = _messages.MessageField('TableReference', 1)
  cloneTime = _message_types.DateTimeField(2)


class Cluster(_messages.Message):
  r"""Message containing the information about one cluster.

  Fields:
    centroidId: Centroid id.
    count: Count of training data rows that were assigned to this cluster.
    featureValues: Values of highly variant features for this cluster.
  """

  centroidId = _messages.IntegerField(1)
  count = _messages.IntegerField(2)
  featureValues = _messages.MessageField('FeatureValue', 3, repeated=True)


class ClusterInfo(_messages.Message):
  r"""Information about a single cluster for clustering model.

  Fields:
    centroidId: Centroid id.
    clusterRadius: Cluster radius, the average distance from centroid to each
      point assigned to the cluster.
    clusterSize: Cluster size, the total number of points assigned to the
      cluster.
  """

  centroidId = _messages.IntegerField(1)
  clusterRadius = _messages.FloatField(2)
  clusterSize = _messages.IntegerField(3)


class Clustering(_messages.Message):
  r"""A Clustering object.

  Fields:
    fields: [Repeated] One or more fields on which data should be clustered.
      Only top-level, non-repeated, simple-type fields are supported. When you
      cluster a table using multiple columns, the order of columns you specify
      is important. The order of the specified columns determines the sort
      order of the data.
  """

  fields = _messages.StringField(1, repeated=True)


class ClusteringMetrics(_messages.Message):
  r"""Evaluation metrics for clustering models.

  Fields:
    clusters: Information for all clusters.
    daviesBouldinIndex: Davies-Bouldin index.
    meanSquaredDistance: Mean of squared distances between each sample to its
      cluster centroid.
  """

  clusters = _messages.MessageField('Cluster', 1, repeated=True)
  daviesBouldinIndex = _messages.FloatField(2)
  meanSquaredDistance = _messages.FloatField(3)


class ConfusionMatrix(_messages.Message):
  r"""Confusion matrix for multi-class classification models.

  Fields:
    confidenceThreshold: Confidence threshold used when computing the entries
      of the confusion matrix.
    rows: One row per actual label.
  """

  confidenceThreshold = _messages.FloatField(1)
  rows = _messages.MessageField('Row', 2, repeated=True)


class ConnectionProperty(_messages.Message):
  r"""A ConnectionProperty object.

  Fields:
    key: [Required] Name of the connection property to set.
    value: [Required] Value of the connection property.
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


class CsvOptions(_messages.Message):
  r"""A CsvOptions object.

  Fields:
    allowJaggedRows: [Optional] Indicates if BigQuery should accept rows that
      are missing trailing optional columns. If true, BigQuery treats missing
      trailing columns as null values. If false, records with missing trailing
      columns are treated as bad records, and if there are too many bad
      records, an invalid error is returned in the job result. The default
      value is false.
    allowQuotedNewlines: [Optional] Indicates if BigQuery should allow quoted
      data sections that contain newline characters in a CSV file. The default
      value is false.
    encoding: [Optional] The character encoding of the data. The supported
      values are UTF-8 or ISO-8859-1. The default value is UTF-8. BigQuery
      decodes the data after the raw, binary data has been split using the
      values of the quote and fieldDelimiter properties.
    fieldDelimiter: [Optional] The separator for fields in a CSV file.
      BigQuery converts the string to ISO-8859-1 encoding, and then uses the
      first byte of the encoded string to split the data in its raw, binary
      state. BigQuery also supports the escape sequence "\t" to specify a tab
      separator. The default value is a comma (',').
    null_marker: [Optional] An custom string that will represent a NULL value
      in CSV import data.
    preserveAsciiControlCharacters: [Optional] Preserves the embedded ASCII
      control characters (the first 32 characters in the ASCII-table, from
      '\x00' to '\x1F') when loading from CSV. Only applicable to CSV, ignored
      for other formats.
    quote: [Optional] The value that is used to quote data sections in a CSV
      file. BigQuery converts the string to ISO-8859-1 encoding, and then uses
      the first byte of the encoded string to split the data in its raw,
      binary state. The default value is a double-quote ('"'). If your data
      does not contain quoted sections, set the property value to an empty
      string. If your data contains quoted newline characters, you must also
      set the allowQuotedNewlines property to true.
    skipLeadingRows: [Optional] The number of rows at the top of a CSV file
      that BigQuery will skip when reading the data. The default value is 0.
      This property is useful if you have header rows in the file that should
      be skipped. When autodetect is on, the behavior is the following: *
      skipLeadingRows unspecified - Autodetect tries to detect headers in the
      first row. If they are not detected, the row is read as data. Otherwise
      data is read starting from the second row. * skipLeadingRows is 0 -
      Instructs autodetect that there are no headers and data should be read
      starting from the first row. * skipLeadingRows = N > 0 - Autodetect
      skips N-1 rows and tries to detect headers in row N. If headers are not
      detected, row N is just skipped. Otherwise row N is used to extract
      column names for the detected schema.
  """

  allowJaggedRows = _messages.BooleanField(1)
  allowQuotedNewlines = _messages.BooleanField(2)
  encoding = _messages.StringField(3)
  fieldDelimiter = _messages.StringField(4)
  null_marker = _messages.StringField(5)
  preserveAsciiControlCharacters = _messages.BooleanField(6)
  quote = _messages.StringField(7, default='"')
  skipLeadingRows = _messages.IntegerField(8)


class DataMaskingStatistics(_messages.Message):
  r"""A DataMaskingStatistics object.

  Fields:
    dataMaskingApplied: [Output-only] [Preview] Whether any accessed data was
      protected by data masking. The actual evaluation is done by
      accessStats.masked_field_count > 0. Since this is only used for the
      discovery_doc generation purpose, as long as the type (boolean) matches,
      client library can leverage this. The actual evaluation of the variable
      is done else-where.
  """

  dataMaskingApplied = _messages.BooleanField(1, default=False)


class DataSplitResult(_messages.Message):
  r"""Data split result. This contains references to the training and
  evaluation data tables that were used to train the model.

  Fields:
    evaluationTable: Table reference of the evaluation data after split.
    testTable: Table reference of the test data after split.
    trainingTable: Table reference of the training data after split.
  """

  evaluationTable = _messages.MessageField('TableReference', 1)
  testTable = _messages.MessageField('TableReference', 2)
  trainingTable = _messages.MessageField('TableReference', 3)


class Dataset(_messages.Message):
  r"""A Dataset object.

  Messages:
    AccessValueListEntry: A AccessValueListEntry object.
    LabelsValue: The labels associated with this dataset. You can use these to
      organize and group your datasets. You can set this property when
      inserting or updating a dataset. See Creating and Updating Dataset
      Labels for more information.
    TagsValueListEntry: A TagsValueListEntry object.

  Fields:
    access: [Optional] An array of objects that define dataset access for one
      or more entities. You can set this property when inserting or updating a
      dataset in order to control who is allowed to access the data. If
      unspecified at dataset creation time, BigQuery adds default dataset
      access for the following entities: access.specialGroup: projectReaders;
      access.role: READER; access.specialGroup: projectWriters; access.role:
      WRITER; access.specialGroup: projectOwners; access.role: OWNER;
      access.userByEmail: [dataset creator email]; access.role: OWNER;
    creationTime: [Output-only] The time when this dataset was created, in
      milliseconds since the epoch.
    datasetReference: [Required] A reference that identifies the dataset.
    defaultCollation: [Output-only] The default collation of the dataset.
    defaultEncryptionConfiguration: A EncryptionConfiguration attribute.
    defaultPartitionExpirationMs: [Optional] The default partition expiration
      for all partitioned tables in the dataset, in milliseconds. Once this
      property is set, all newly-created partitioned tables in the dataset
      will have an expirationMs property in the timePartitioning settings set
      to this value, and changing the value will only affect new tables, not
      existing ones. The storage in a partition will have an expiration time
      of its partition time plus this value. Setting this property overrides
      the use of defaultTableExpirationMs for partitioned tables: only one of
      defaultTableExpirationMs and defaultPartitionExpirationMs will be used
      for any new partitioned table. If you provide an explicit
      timePartitioning.expirationMs when creating or updating a partitioned
      table, that value takes precedence over the default partition expiration
      time indicated by this property.
    defaultRoundingMode: [Output-only] The default rounding mode of the
      dataset.
    defaultTableExpirationMs: [Optional] The default lifetime of all tables in
      the dataset, in milliseconds. The minimum value is 3600000 milliseconds
      (one hour). Once this property is set, all newly-created tables in the
      dataset will have an expirationTime property set to the creation time
      plus the value in this property, and changing the value will only affect
      new tables, not existing ones. When the expirationTime for a given table
      is reached, that table will be deleted automatically. If a table's
      expirationTime is modified or removed before the table expires, or if
      you provide an explicit expirationTime when creating a table, that value
      takes precedence over the default expiration time indicated by this
      property.
    description: [Optional] A user-friendly description of the dataset.
    etag: [Output-only] A hash of the resource.
    friendlyName: [Optional] A descriptive name for the dataset.
    id: [Output-only] The fully-qualified unique name of the dataset in the
      format projectId:datasetId. The dataset name without the project name is
      given in the datasetId field. When creating a new dataset, leave this
      field blank, and instead specify the datasetId field.
    isCaseInsensitive: [Optional] Indicates if table names are case
      insensitive in the dataset.
    kind: [Output-only] The resource type.
    labels: The labels associated with this dataset. You can use these to
      organize and group your datasets. You can set this property when
      inserting or updating a dataset. See Creating and Updating Dataset
      Labels for more information.
    lastModifiedTime: [Output-only] The date when this dataset or any of its
      tables was last modified, in milliseconds since the epoch.
    location: The geographic location where the dataset should reside. The
      default value is US. See details at
      https://cloud.google.com/bigquery/docs/locations.
    maxTimeTravelHours: [Optional] Number of hours for the max time travel for
      all tables in the dataset.
    satisfiesPzs: [Output-only] Reserved for future use.
    selfLink: [Output-only] A URL that can be used to access the resource
      again. You can use this URL in Get or Update requests to the resource.
    storageBillingModel: [Optional] Storage billing model to be used for all
      tables in the dataset. Can be set to PHYSICAL. Default is LOGICAL.
    tags: [Optional]The tags associated with this dataset. Tag keys are
      globally unique.
  """

  class AccessValueListEntry(_messages.Message):
    r"""A AccessValueListEntry object.

    Fields:
      dataset: [Pick one] A grant authorizing all resources of a particular
        type in a particular dataset access to this dataset. Only views are
        supported for now. The role field is not required when this field is
        set. If that dataset is deleted and re-created, its access needs to be
        granted again via an update operation.
      domain: [Pick one] A domain to grant access to. Any users signed in with
        the domain specified will be granted the specified access. Example:
        "example.com". Maps to IAM policy member "domain:DOMAIN".
      groupByEmail: [Pick one] An email address of a Google Group to grant
        access to. Maps to IAM policy member "group:GROUP".
      iamMember: [Pick one] Some other type of member that appears in the IAM
        Policy but isn't a user, group, domain, or special group.
      role: [Required] An IAM role ID that should be granted to the user,
        group, or domain specified in this access entry. The following legacy
        mappings will be applied: OWNER  roles/bigquery.dataOwner WRITER
        roles/bigquery.dataEditor READER  roles/bigquery.dataViewer This field
        will accept any of the above formats, but will return only the legacy
        format. For example, if you set this field to
        "roles/bigquery.dataOwner", it will be returned back as "OWNER".
      routine: [Pick one] A routine from a different dataset to grant access
        to. Queries executed against that routine will have read access to
        views/tables/routines in this dataset. Only UDF is supported for now.
        The role field is not required when this field is set. If that routine
        is updated by any user, access to the routine needs to be granted
        again via an update operation.
      specialGroup: [Pick one] A special group to grant access to. Possible
        values include: projectOwners: Owners of the enclosing project.
        projectReaders: Readers of the enclosing project. projectWriters:
        Writers of the enclosing project. allAuthenticatedUsers: All
        authenticated BigQuery users. Maps to similarly-named IAM members.
      userByEmail: [Pick one] An email address of a user to grant access to.
        For example: <EMAIL>. Maps to IAM policy member "user:EMAIL"
        or "serviceAccount:EMAIL".
      view: [Pick one] A view from a different dataset to grant access to.
        Queries executed against that view will have read access to tables in
        this dataset. The role field is not required when this field is set.
        If that view is updated by any user, access to the view needs to be
        granted again via an update operation.
    """

    dataset = _messages.MessageField('DatasetAccessEntry', 1)
    domain = _messages.StringField(2)
    groupByEmail = _messages.StringField(3)
    iamMember = _messages.StringField(4)
    role = _messages.StringField(5)
    routine = _messages.MessageField('RoutineReference', 6)
    specialGroup = _messages.StringField(7)
    userByEmail = _messages.StringField(8)
    view = _messages.MessageField('TableReference', 9)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this dataset. You can use these to organize
    and group your datasets. You can set this property when inserting or
    updating a dataset. See Creating and Updating Dataset Labels for more
    information.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  class TagsValueListEntry(_messages.Message):
    r"""A TagsValueListEntry object.

    Fields:
      tagKey: [Required] The namespaced friendly name of the tag key, e.g.
        "12345/environment" where 12345 is org id.
      tagValue: [Required] Friendly short name of the tag value, e.g.
        "production".
    """

    tagKey = _messages.StringField(1)
    tagValue = _messages.StringField(2)

  access = _messages.MessageField('AccessValueListEntry', 1, repeated=True)
  creationTime = _messages.IntegerField(2)
  datasetReference = _messages.MessageField('DatasetReference', 3)
  defaultCollation = _messages.StringField(4)
  defaultEncryptionConfiguration = _messages.MessageField('EncryptionConfiguration', 5)
  defaultPartitionExpirationMs = _messages.IntegerField(6)
  defaultRoundingMode = _messages.StringField(7)
  defaultTableExpirationMs = _messages.IntegerField(8)
  description = _messages.StringField(9)
  etag = _messages.StringField(10)
  friendlyName = _messages.StringField(11)
  id = _messages.StringField(12)
  isCaseInsensitive = _messages.BooleanField(13)
  kind = _messages.StringField(14, default='bigquery#dataset')
  labels = _messages.MessageField('LabelsValue', 15)
  lastModifiedTime = _messages.IntegerField(16)
  location = _messages.StringField(17)
  maxTimeTravelHours = _messages.IntegerField(18)
  satisfiesPzs = _messages.BooleanField(19)
  selfLink = _messages.StringField(20)
  storageBillingModel = _messages.StringField(21)
  tags = _messages.MessageField('TagsValueListEntry', 22, repeated=True)


class DatasetAccessEntry(_messages.Message):
  r"""A DatasetAccessEntry object.

  Enums:
    TargetTypesValueListEntryValuesEnum:

  Fields:
    dataset: [Required] The dataset this entry applies to.
    targetTypes: A TargetTypesValueListEntryValuesEnum attribute.
  """

  class TargetTypesValueListEntryValuesEnum(_messages.Enum):
    r"""TargetTypesValueListEntryValuesEnum enum type.

    Values:
      TARGET_TYPE_UNSPECIFIED: Do not use. You must set a target type
        explicitly.
      VIEWS: This entry applies to views in the dataset.
      ROUTINES: This entry applies to routines in the dataset.
    """
    TARGET_TYPE_UNSPECIFIED = 0
    VIEWS = 1
    ROUTINES = 2

  dataset = _messages.MessageField('DatasetReference', 1)
  targetTypes = _messages.EnumField('TargetTypesValueListEntryValuesEnum', 2, repeated=True)


class DatasetList(_messages.Message):
  r"""A DatasetList object.

  Messages:
    DatasetsValueListEntry: A DatasetsValueListEntry object.

  Fields:
    datasets: An array of the dataset resources in the project. Each resource
      contains basic information. For full information about a particular
      dataset resource, use the Datasets: get method. This property is omitted
      when there are no datasets in the project.
    etag: A hash value of the results page. You can use this property to
      determine if the page has changed since the last request.
    kind: The list type. This property always returns the value
      "bigquery#datasetList".
    nextPageToken: A token that can be used to request the next results page.
      This property is omitted on the final results page.
  """

  class DatasetsValueListEntry(_messages.Message):
    r"""A DatasetsValueListEntry object.

    Messages:
      LabelsValue: The labels associated with this dataset. You can use these
        to organize and group your datasets.

    Fields:
      datasetReference: The dataset reference. Use this property to access
        specific parts of the dataset's ID, such as project ID or dataset ID.
      friendlyName: A descriptive name for the dataset, if one exists.
      id: The fully-qualified, unique, opaque ID of the dataset.
      kind: The resource type. This property always returns the value
        "bigquery#dataset".
      labels: The labels associated with this dataset. You can use these to
        organize and group your datasets.
      location: The geographic location where the data resides.
    """

    @encoding.MapUnrecognizedFields('additionalProperties')
    class LabelsValue(_messages.Message):
      r"""The labels associated with this dataset. You can use these to
      organize and group your datasets.

      Messages:
        AdditionalProperty: An additional property for a LabelsValue object.

      Fields:
        additionalProperties: Additional properties of type LabelsValue
      """

      class AdditionalProperty(_messages.Message):
        r"""An additional property for a LabelsValue object.

        Fields:
          key: Name of the additional property.
          value: A string attribute.
        """

        key = _messages.StringField(1)
        value = _messages.StringField(2)

      additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

    datasetReference = _messages.MessageField('DatasetReference', 1)
    friendlyName = _messages.StringField(2)
    id = _messages.StringField(3)
    kind = _messages.StringField(4, default='bigquery#dataset')
    labels = _messages.MessageField('LabelsValue', 5)
    location = _messages.StringField(6)

  datasets = _messages.MessageField('DatasetsValueListEntry', 1, repeated=True)
  etag = _messages.StringField(2)
  kind = _messages.StringField(3, default='bigquery#datasetList')
  nextPageToken = _messages.StringField(4)


class DatasetReference(_messages.Message):
  r"""A DatasetReference object.

  Fields:
    datasetId: [Required] A unique ID for this dataset, without the project
      name. The ID must contain only letters (a-z, A-Z), numbers (0-9), or
      underscores (_). The maximum length is 1,024 characters.
    projectId: [Optional] The ID of the project containing this dataset.
  """

  datasetId = _messages.StringField(1)
  projectId = _messages.StringField(2)


class DestinationTableProperties(_messages.Message):
  r"""A DestinationTableProperties object.

  Messages:
    LabelsValue: [Optional] The labels associated with this table. You can use
      these to organize and group your tables. This will only be used if the
      destination table is newly created. If the table already exists and
      labels are different than the current labels are provided, the job will
      fail.

  Fields:
    description: [Optional] The description for the destination table. This
      will only be used if the destination table is newly created. If the
      table already exists and a value different than the current description
      is provided, the job will fail.
    expirationTime: [Internal] This field is for Google internal use only.
    friendlyName: [Optional] The friendly name for the destination table. This
      will only be used if the destination table is newly created. If the
      table already exists and a value different than the current friendly
      name is provided, the job will fail.
    labels: [Optional] The labels associated with this table. You can use
      these to organize and group your tables. This will only be used if the
      destination table is newly created. If the table already exists and
      labels are different than the current labels are provided, the job will
      fail.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""[Optional] The labels associated with this table. You can use these to
    organize and group your tables. This will only be used if the destination
    table is newly created. If the table already exists and labels are
    different than the current labels are provided, the job will fail.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  description = _messages.StringField(1)
  expirationTime = _message_types.DateTimeField(2)
  friendlyName = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)


class DimensionalityReductionMetrics(_messages.Message):
  r"""Model evaluation metrics for dimensionality reduction models.

  Fields:
    totalExplainedVarianceRatio: Total percentage of variance explained by the
      selected principal components.
  """

  totalExplainedVarianceRatio = _messages.FloatField(1)


class DmlStatistics(_messages.Message):
  r"""A DmlStatistics object.

  Fields:
    deletedRowCount: Number of deleted Rows. populated by DML DELETE, MERGE
      and TRUNCATE statements.
    insertedRowCount: Number of inserted Rows. Populated by DML INSERT and
      MERGE statements.
    updatedRowCount: Number of updated Rows. Populated by DML UPDATE and MERGE
      statements.
  """

  deletedRowCount = _messages.IntegerField(1)
  insertedRowCount = _messages.IntegerField(2)
  updatedRowCount = _messages.IntegerField(3)


class DoubleCandidates(_messages.Message):
  r"""Discrete candidates of a double hyperparameter.

  Fields:
    candidates: Candidates for the double parameter in increasing order.
  """

  candidates = _messages.FloatField(1, repeated=True)


class DoubleHparamSearchSpace(_messages.Message):
  r"""Search space for a double hyperparameter.

  Fields:
    candidates: Candidates of the double hyperparameter.
    range: Range of the double hyperparameter.
  """

  candidates = _messages.MessageField('DoubleCandidates', 1)
  range = _messages.MessageField('DoubleRange', 2)


class DoubleRange(_messages.Message):
  r"""Range of a double hyperparameter.

  Fields:
    max: Max value of the double parameter.
    min: Min value of the double parameter.
  """

  max = _messages.FloatField(1)
  min = _messages.FloatField(2)


class EncryptionConfiguration(_messages.Message):
  r"""A EncryptionConfiguration object.

  Fields:
    kmsKeyName: Optional. Describes the Cloud KMS encryption key that will be
      used to protect destination BigQuery table. The BigQuery Service Account
      associated with your project requires access to this encryption key.
  """

  kmsKeyName = _messages.StringField(1)


class Entry(_messages.Message):
  r"""A single entry in the confusion matrix.

  Fields:
    itemCount: Number of items being predicted as this label.
    predictedLabel: The predicted label. For confidence_threshold > 0, we will
      also add an entry indicating the number of items under the confidence
      threshold.
  """

  itemCount = _messages.IntegerField(1)
  predictedLabel = _messages.StringField(2)


class ErrorProto(_messages.Message):
  r"""A ErrorProto object.

  Fields:
    debugInfo: Debugging information. This property is internal to Google and
      should not be used.
    location: Specifies where the error occurred, if present.
    message: A human-readable description of the error.
    reason: A short error code that summarizes the error.
  """

  debugInfo = _messages.StringField(1)
  location = _messages.StringField(2)
  message = _messages.StringField(3)
  reason = _messages.StringField(4)


class EvaluationMetrics(_messages.Message):
  r"""Evaluation metrics of a model. These are either computed on all training
  data or just the eval data based on whether eval data was used during
  training. These are not present for imported models.

  Fields:
    arimaForecastingMetrics: Populated for ARIMA models.
    binaryClassificationMetrics: Populated for binary
      classification/classifier models.
    clusteringMetrics: Populated for clustering models.
    dimensionalityReductionMetrics: Evaluation metrics when the model is a
      dimensionality reduction model, which currently includes PCA.
    multiClassClassificationMetrics: Populated for multi-class
      classification/classifier models.
    rankingMetrics: Populated for implicit feedback type matrix factorization
      models.
    regressionMetrics: Populated for regression models and explicit feedback
      type matrix factorization models.
  """

  arimaForecastingMetrics = _messages.MessageField('ArimaForecastingMetrics', 1)
  binaryClassificationMetrics = _messages.MessageField('BinaryClassificationMetrics', 2)
  clusteringMetrics = _messages.MessageField('ClusteringMetrics', 3)
  dimensionalityReductionMetrics = _messages.MessageField('DimensionalityReductionMetrics', 4)
  multiClassClassificationMetrics = _messages.MessageField('MultiClassClassificationMetrics', 5)
  rankingMetrics = _messages.MessageField('RankingMetrics', 6)
  regressionMetrics = _messages.MessageField('RegressionMetrics', 7)


class ExplainQueryStage(_messages.Message):
  r"""A ExplainQueryStage object.

  Fields:
    completedParallelInputs: Number of parallel input segments completed.
    computeMsAvg: Milliseconds the average shard spent on CPU-bound tasks.
    computeMsMax: Milliseconds the slowest shard spent on CPU-bound tasks.
    computeRatioAvg: Relative amount of time the average shard spent on CPU-
      bound tasks.
    computeRatioMax: Relative amount of time the slowest shard spent on CPU-
      bound tasks.
    endMs: Stage end time represented as milliseconds since epoch.
    id: Unique ID for stage within plan.
    inputStages: IDs for stages that are inputs to this stage.
    name: Human-readable name for stage.
    parallelInputs: Number of parallel input segments to be processed.
    readMsAvg: Milliseconds the average shard spent reading input.
    readMsMax: Milliseconds the slowest shard spent reading input.
    readRatioAvg: Relative amount of time the average shard spent reading
      input.
    readRatioMax: Relative amount of time the slowest shard spent reading
      input.
    recordsRead: Number of records read into the stage.
    recordsWritten: Number of records written by the stage.
    shuffleOutputBytes: Total number of bytes written to shuffle.
    shuffleOutputBytesSpilled: Total number of bytes written to shuffle and
      spilled to disk.
    slotMs: Slot-milliseconds used by the stage.
    startMs: Stage start time represented as milliseconds since epoch.
    status: Current status for the stage.
    steps: List of operations within the stage in dependency order
      (approximately chronological).
    waitMsAvg: Milliseconds the average shard spent waiting to be scheduled.
    waitMsMax: Milliseconds the slowest shard spent waiting to be scheduled.
    waitRatioAvg: Relative amount of time the average shard spent waiting to
      be scheduled.
    waitRatioMax: Relative amount of time the slowest shard spent waiting to
      be scheduled.
    writeMsAvg: Milliseconds the average shard spent on writing output.
    writeMsMax: Milliseconds the slowest shard spent on writing output.
    writeRatioAvg: Relative amount of time the average shard spent on writing
      output.
    writeRatioMax: Relative amount of time the slowest shard spent on writing
      output.
  """

  completedParallelInputs = _messages.IntegerField(1)
  computeMsAvg = _messages.IntegerField(2)
  computeMsMax = _messages.IntegerField(3)
  computeRatioAvg = _messages.FloatField(4)
  computeRatioMax = _messages.FloatField(5)
  endMs = _messages.IntegerField(6)
  id = _messages.IntegerField(7)
  inputStages = _messages.IntegerField(8, repeated=True)
  name = _messages.StringField(9)
  parallelInputs = _messages.IntegerField(10)
  readMsAvg = _messages.IntegerField(11)
  readMsMax = _messages.IntegerField(12)
  readRatioAvg = _messages.FloatField(13)
  readRatioMax = _messages.FloatField(14)
  recordsRead = _messages.IntegerField(15)
  recordsWritten = _messages.IntegerField(16)
  shuffleOutputBytes = _messages.IntegerField(17)
  shuffleOutputBytesSpilled = _messages.IntegerField(18)
  slotMs = _messages.IntegerField(19)
  startMs = _messages.IntegerField(20)
  status = _messages.StringField(21)
  steps = _messages.MessageField('ExplainQueryStep', 22, repeated=True)
  waitMsAvg = _messages.IntegerField(23)
  waitMsMax = _messages.IntegerField(24)
  waitRatioAvg = _messages.FloatField(25)
  waitRatioMax = _messages.FloatField(26)
  writeMsAvg = _messages.IntegerField(27)
  writeMsMax = _messages.IntegerField(28)
  writeRatioAvg = _messages.FloatField(29)
  writeRatioMax = _messages.FloatField(30)


class ExplainQueryStep(_messages.Message):
  r"""A ExplainQueryStep object.

  Fields:
    kind: Machine-readable operation type.
    substeps: Human-readable stage descriptions.
  """

  kind = _messages.StringField(1)
  substeps = _messages.StringField(2, repeated=True)


class Explanation(_messages.Message):
  r"""Explanation for a single feature.

  Fields:
    attribution: Attribution of feature.
    featureName: The full feature name. For non-numerical features, will be
      formatted like `.`. Overall size of feature name will always be
      truncated to first 120 characters.
  """

  attribution = _messages.FloatField(1)
  featureName = _messages.StringField(2)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class ExternalDataConfiguration(_messages.Message):
  r"""A ExternalDataConfiguration object.

  Fields:
    autodetect: Try to detect schema and format options automatically. Any
      option specified explicitly will be honored.
    avroOptions: Additional properties to set if sourceFormat is set to Avro.
    bigtableOptions: [Optional] Additional options if sourceFormat is set to
      BIGTABLE.
    compression: [Optional] The compression type of the data source. Possible
      values include GZIP and NONE. The default value is NONE. This setting is
      ignored for Google Cloud Bigtable, Google Cloud Datastore backups and
      Avro formats.
    connectionId: [Optional, Trusted Tester] Connection for external data
      source.
    csvOptions: Additional properties to set if sourceFormat is set to CSV.
    decimalTargetTypes: [Optional] Defines the list of possible SQL data types
      to which the source decimal values are converted. This list and the
      precision and the scale parameters of the decimal field determine the
      target type. In the order of NUMERIC, BIGNUMERIC, and STRING, a type is
      picked if it is in the specified list and if it supports the precision
      and the scale. STRING supports all precision and scale values. If none
      of the listed types supports the precision and the scale, the type
      supporting the widest range in the specified list is picked, and if a
      value exceeds the supported range when reading the data, an error will
      be thrown. Example: Suppose the value of this field is ["NUMERIC",
      "BIGNUMERIC"]. If (precision,scale) is: (38,9) -> NUMERIC; (39,9) ->
      BIGNUMERIC (NUMERIC cannot hold 30 integer digits); (38,10) ->
      BIGNUMERIC (NUMERIC cannot hold 10 fractional digits); (76,38) ->
      BIGNUMERIC; (77,38) -> BIGNUMERIC (error if value exeeds supported
      range). This field cannot contain duplicate types. The order of the
      types in this field is ignored. For example, ["BIGNUMERIC", "NUMERIC"]
      is the same as ["NUMERIC", "BIGNUMERIC"] and NUMERIC always takes
      precedence over BIGNUMERIC. Defaults to ["NUMERIC", "STRING"] for ORC
      and ["NUMERIC"] for the other file formats.
    fileSetSpecType: [Optional] Specifies how source URIs are interpreted for
      constructing the file set to load. By default source URIs are expanded
      against the underlying storage. Other options include specifying
      manifest files. Only applicable to object storage systems.
    googleSheetsOptions: [Optional] Additional options if sourceFormat is set
      to GOOGLE_SHEETS.
    hivePartitioningOptions: [Optional] Options to configure hive partitioning
      support.
    ignoreUnknownValues: [Optional] Indicates if BigQuery should allow extra
      values that are not represented in the table schema. If true, the extra
      values are ignored. If false, records with extra columns are treated as
      bad records, and if there are too many bad records, an invalid error is
      returned in the job result. The default value is false. The sourceFormat
      property determines what BigQuery treats as an extra value: CSV:
      Trailing columns JSON: Named values that don't match any column names
      Google Cloud Bigtable: This setting is ignored. Google Cloud Datastore
      backups: This setting is ignored. Avro: This setting is ignored.
    jsonOptions: Additional properties to set if `sourceFormat` is set to
      `NEWLINE_DELIMITED_JSON`.
    maxBadRecords: [Optional] The maximum number of bad records that BigQuery
      can ignore when reading data. If the number of bad records exceeds this
      value, an invalid error is returned in the job result. This is only
      valid for CSV, JSON, and Google Sheets. The default value is 0, which
      requires that all records are valid. This setting is ignored for Google
      Cloud Bigtable, Google Cloud Datastore backups and Avro formats.
    metadataCacheMode: [Optional] Metadata Cache Mode for the table. Set this
      to enable caching of metadata from external data source.
    objectMetadata: ObjectMetadata is used to create Object Tables. Object
      Tables contain a listing of objects (with their metadata) found at the
      source_uris. If ObjectMetadata is set, source_format should be omitted.
      Currently SIMPLE is the only supported Object Metadata type.
    parquetOptions: Additional properties to set if sourceFormat is set to
      Parquet.
    referenceFileSchemaUri: [Optional] Provide a referencing file with the
      expected table schema. Enabled for the format: AVRO, PARQUET, ORC.
    schema: [Optional] The schema for the data. Schema is required for CSV and
      JSON formats. Schema is disallowed for Google Cloud Bigtable, Cloud
      Datastore backups, and Avro formats.
    sourceFormat: [Required] The data format. For CSV files, specify "CSV".
      For Google sheets, specify "GOOGLE_SHEETS". For newline-delimited JSON,
      specify "NEWLINE_DELIMITED_JSON". For Avro files, specify "AVRO". For
      Google Cloud Datastore backups, specify "DATASTORE_BACKUP". [Beta] For
      Google Cloud Bigtable, specify "BIGTABLE".
    sourceUris: [Required] The fully-qualified URIs that point to your data in
      Google Cloud. For Google Cloud Storage URIs: Each URI can contain one
      '*' wildcard character and it must come after the 'bucket' name. Size
      limits related to load jobs apply to external data sources. For Google
      Cloud Bigtable URIs: Exactly one URI can be specified and it has be a
      fully specified and valid HTTPS URL for a Google Cloud Bigtable table.
      For Google Cloud Datastore backups, exactly one URI can be specified.
      Also, the '*' wildcard character is not allowed.
  """

  autodetect = _messages.BooleanField(1)
  avroOptions = _messages.MessageField('AvroOptions', 2)
  bigtableOptions = _messages.MessageField('BigtableOptions', 3)
  compression = _messages.StringField(4)
  connectionId = _messages.StringField(5)
  csvOptions = _messages.MessageField('CsvOptions', 6)
  decimalTargetTypes = _messages.StringField(7, repeated=True)
  fileSetSpecType = _messages.StringField(8)
  googleSheetsOptions = _messages.MessageField('GoogleSheetsOptions', 9)
  hivePartitioningOptions = _messages.MessageField('HivePartitioningOptions', 10)
  ignoreUnknownValues = _messages.BooleanField(11)
  jsonOptions = _messages.MessageField('JsonOptions', 12)
  maxBadRecords = _messages.IntegerField(13, variant=_messages.Variant.INT32)
  metadataCacheMode = _messages.StringField(14)
  objectMetadata = _messages.StringField(15)
  parquetOptions = _messages.MessageField('ParquetOptions', 16)
  referenceFileSchemaUri = _messages.StringField(17)
  schema = _messages.MessageField('TableSchema', 18)
  sourceFormat = _messages.StringField(19)
  sourceUris = _messages.StringField(20, repeated=True)


class FeatureValue(_messages.Message):
  r"""Representative value of a single feature within the cluster.

  Fields:
    categoricalValue: The categorical feature value.
    featureColumn: The feature column name.
    numericalValue: The numerical feature value. This is the centroid value
      for this feature.
  """

  categoricalValue = _messages.MessageField('CategoricalValue', 1)
  featureColumn = _messages.StringField(2)
  numericalValue = _messages.FloatField(3)


class GetIamPolicyRequest(_messages.Message):
  r"""Request message for `GetIamPolicy` method.

  Fields:
    options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
      `GetIamPolicy`.
  """

  options = _messages.MessageField('GetPolicyOptions', 1)


class GetPolicyOptions(_messages.Message):
  r"""Encapsulates settings provided to GetIamPolicy.

  Fields:
    requestedPolicyVersion: Optional. The maximum policy version that will be
      used to format the policy. Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected. Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset. The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1. To learn which resources support conditions in
      their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class GetQueryResultsResponse(_messages.Message):
  r"""A GetQueryResultsResponse object.

  Fields:
    cacheHit: Whether the query result was fetched from the query cache.
    errors: [Output-only] The first errors or warnings encountered during the
      running of the job. The final message includes the number of errors that
      caused the process to stop. Errors here do not necessarily mean that the
      job has completed or was unsuccessful.
    etag: A hash of this response.
    jobComplete: Whether the query has completed or not. If rows or totalRows
      are present, this will always be true. If this is false, totalRows will
      not be available.
    jobReference: Reference to the BigQuery Job that was created to run the
      query. This field will be present even if the original request timed
      out, in which case GetQueryResults can be used to read the results once
      the query has completed. Since this API only returns the first page of
      results, subsequent pages can be fetched via the same mechanism
      (GetQueryResults).
    kind: The resource type of the response.
    numDmlAffectedRows: [Output-only] The number of rows affected by a DML
      statement. Present only for DML statements INSERT, UPDATE or DELETE.
    pageToken: A token used for paging results.
    rows: An object with as many results as can be contained within the
      maximum permitted reply size. To get any additional rows, you can call
      GetQueryResults and specify the jobReference returned above. Present
      only when the query completes successfully.
    schema: The schema of the results. Present only when the query completes
      successfully.
    totalBytesProcessed: The total number of bytes processed for this query.
    totalRows: The total number of rows in the complete query result set,
      which can be more than the number of rows in this single page of
      results. Present only when the query completes successfully.
  """

  cacheHit = _messages.BooleanField(1)
  errors = _messages.MessageField('ErrorProto', 2, repeated=True)
  etag = _messages.StringField(3)
  jobComplete = _messages.BooleanField(4)
  jobReference = _messages.MessageField('JobReference', 5)
  kind = _messages.StringField(6, default='bigquery#getQueryResultsResponse')
  numDmlAffectedRows = _messages.IntegerField(7)
  pageToken = _messages.StringField(8)
  rows = _messages.MessageField('TableRow', 9, repeated=True)
  schema = _messages.MessageField('TableSchema', 10)
  totalBytesProcessed = _messages.IntegerField(11)
  totalRows = _messages.IntegerField(12, variant=_messages.Variant.UINT64)


class GetServiceAccountResponse(_messages.Message):
  r"""A GetServiceAccountResponse object.

  Fields:
    email: The service account email address.
    kind: The resource type of the response.
  """

  email = _messages.StringField(1)
  kind = _messages.StringField(2, default='bigquery#getServiceAccountResponse')


class GlobalExplanation(_messages.Message):
  r"""Global explanations containing the top most important features after
  training.

  Fields:
    classLabel: Class label for this set of global explanations. Will be
      empty/null for binary logistic and linear regression models. Sorted
      alphabetically in descending order.
    explanations: A list of the top global explanations. Sorted by absolute
      value of attribution in descending order.
  """

  classLabel = _messages.StringField(1)
  explanations = _messages.MessageField('Explanation', 2, repeated=True)


class GoogleSheetsOptions(_messages.Message):
  r"""A GoogleSheetsOptions object.

  Fields:
    range: [Optional] Range of a sheet to query from. Only used when non-
      empty. Typical format: sheet_name!top_left_cell_id:bottom_right_cell_id
      For example: sheet1!A1:B20
    skipLeadingRows: [Optional] The number of rows at the top of a sheet that
      BigQuery will skip when reading the data. The default value is 0. This
      property is useful if you have header rows that should be skipped. When
      autodetect is on, behavior is the following: * skipLeadingRows
      unspecified - Autodetect tries to detect headers in the first row. If
      they are not detected, the row is read as data. Otherwise data is read
      starting from the second row. * skipLeadingRows is 0 - Instructs
      autodetect that there are no headers and data should be read starting
      from the first row. * skipLeadingRows = N > 0 - Autodetect skips N-1
      rows and tries to detect headers in row N. If headers are not detected,
      row N is just skipped. Otherwise row N is used to extract column names
      for the detected schema.
  """

  range = _messages.StringField(1)
  skipLeadingRows = _messages.IntegerField(2)


class HivePartitioningOptions(_messages.Message):
  r"""A HivePartitioningOptions object.

  Fields:
    fields: [Output-only] For permanent external tables, this field is
      populated with the hive partition keys in the order they were inferred.
      The types of the partition keys can be deduced by checking the table
      schema (which will include the partition keys). Not every API will
      populate this field in the output. For example, Tables.Get will populate
      it, but Tables.List will not contain this field.
    mode: [Optional] When set, what mode of hive partitioning to use when
      reading data. The following modes are supported. (1) AUTO: automatically
      infer partition key name(s) and type(s). (2) STRINGS: automatically
      infer partition key name(s). All types are interpreted as strings. (3)
      CUSTOM: partition key schema is encoded in the source URI prefix. Not
      all storage formats support hive partitioning. Requesting hive
      partitioning on an unsupported format will lead to an error. Currently
      supported types include: AVRO, CSV, JSON, ORC and Parquet.
    requirePartitionFilter: [Optional] If set to true, queries over this table
      require a partition filter that can be used for partition elimination to
      be specified. Note that this field should only be true when creating a
      permanent external table or querying a temporary external table. Hive-
      partitioned loads with requirePartitionFilter explicitly set to true
      will fail.
    sourceUriPrefix: [Optional] When hive partition detection is requested, a
      common prefix for all source uris should be supplied. The prefix must
      end immediately before the partition key encoding begins. For example,
      consider files following this data layout.
      gs://bucket/path_to_table/dt=2019-01-01/country=BR/id=7/file.avro
      gs://bucket/path_to_table/dt=2018-12-31/country=CA/id=3/file.avro When
      hive partitioning is requested with either AUTO or STRINGS detection,
      the common prefix can be either of gs://bucket/path_to_table or
      gs://bucket/path_to_table/ (trailing slash does not matter).
  """

  fields = _messages.StringField(1, repeated=True)
  mode = _messages.StringField(2)
  requirePartitionFilter = _messages.BooleanField(3)
  sourceUriPrefix = _messages.StringField(4)


class HparamSearchSpaces(_messages.Message):
  r"""Hyperparameter search spaces. These should be a subset of
  training_options.

  Fields:
    activationFn: Activation functions of neural network models.
    batchSize: Mini batch sample size.
    boosterType: Booster type for boosted tree models.
    colsampleBylevel: Subsample ratio of columns for each level for boosted
      tree models.
    colsampleBynode: Subsample ratio of columns for each node(split) for
      boosted tree models.
    colsampleBytree: Subsample ratio of columns when constructing each tree
      for boosted tree models.
    dartNormalizeType: Dart normalization type for boosted tree models.
    dropout: Dropout probability for dnn model training and boosted tree
      models using dart booster.
    hiddenUnits: Hidden units for neural network models.
    l1Reg: L1 regularization coefficient.
    l2Reg: L2 regularization coefficient.
    learnRate: Learning rate of training jobs.
    maxTreeDepth: Maximum depth of a tree for boosted tree models.
    minSplitLoss: Minimum split loss for boosted tree models.
    minTreeChildWeight: Minimum sum of instance weight needed in a child for
      boosted tree models.
    numClusters: Number of clusters for k-means.
    numFactors: Number of latent factors to train on.
    numParallelTree: Number of parallel trees for boosted tree models.
    optimizer: Optimizer of TF models.
    subsample: Subsample the training data to grow tree to prevent overfitting
      for boosted tree models.
    treeMethod: Tree construction algorithm for boosted tree models.
    walsAlpha: Hyperparameter for matrix factoration when implicit feedback
      type is specified.
  """

  activationFn = _messages.MessageField('StringHparamSearchSpace', 1)
  batchSize = _messages.MessageField('IntHparamSearchSpace', 2)
  boosterType = _messages.MessageField('StringHparamSearchSpace', 3)
  colsampleBylevel = _messages.MessageField('DoubleHparamSearchSpace', 4)
  colsampleBynode = _messages.MessageField('DoubleHparamSearchSpace', 5)
  colsampleBytree = _messages.MessageField('DoubleHparamSearchSpace', 6)
  dartNormalizeType = _messages.MessageField('StringHparamSearchSpace', 7)
  dropout = _messages.MessageField('DoubleHparamSearchSpace', 8)
  hiddenUnits = _messages.MessageField('IntArrayHparamSearchSpace', 9)
  l1Reg = _messages.MessageField('DoubleHparamSearchSpace', 10)
  l2Reg = _messages.MessageField('DoubleHparamSearchSpace', 11)
  learnRate = _messages.MessageField('DoubleHparamSearchSpace', 12)
  maxTreeDepth = _messages.MessageField('IntHparamSearchSpace', 13)
  minSplitLoss = _messages.MessageField('DoubleHparamSearchSpace', 14)
  minTreeChildWeight = _messages.MessageField('IntHparamSearchSpace', 15)
  numClusters = _messages.MessageField('IntHparamSearchSpace', 16)
  numFactors = _messages.MessageField('IntHparamSearchSpace', 17)
  numParallelTree = _messages.MessageField('IntHparamSearchSpace', 18)
  optimizer = _messages.MessageField('StringHparamSearchSpace', 19)
  subsample = _messages.MessageField('DoubleHparamSearchSpace', 20)
  treeMethod = _messages.MessageField('StringHparamSearchSpace', 21)
  walsAlpha = _messages.MessageField('DoubleHparamSearchSpace', 22)


class HparamTuningTrial(_messages.Message):
  r"""Training info of a trial in [hyperparameter tuning](/bigquery-
  ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview) models.

  Enums:
    StatusValueValuesEnum: The status of the trial.

  Fields:
    endTimeMs: Ending time of the trial.
    errorMessage: Error message for FAILED and INFEASIBLE trial.
    evalLoss: Loss computed on the eval data at the end of trial.
    evaluationMetrics: Evaluation metrics of this trial calculated on the test
      data. Empty in Job API.
    hparamTuningEvaluationMetrics: Hyperparameter tuning evaluation metrics of
      this trial calculated on the eval data. Unlike evaluation_metrics, only
      the fields corresponding to the hparam_tuning_objectives are set.
    hparams: The hyperprameters selected for this trial.
    startTimeMs: Starting time of the trial.
    status: The status of the trial.
    trainingLoss: Loss computed on the training data at the end of trial.
    trialId: 1-based index of the trial.
  """

  class StatusValueValuesEnum(_messages.Enum):
    r"""The status of the trial.

    Values:
      TRIAL_STATUS_UNSPECIFIED: <no description>
      NOT_STARTED: Scheduled but not started.
      RUNNING: Running state.
      SUCCEEDED: The trial succeeded.
      FAILED: The trial failed.
      INFEASIBLE: The trial is infeasible due to the invalid params.
      STOPPED_EARLY: Trial stopped early because it's not promising.
    """
    TRIAL_STATUS_UNSPECIFIED = 0
    NOT_STARTED = 1
    RUNNING = 2
    SUCCEEDED = 3
    FAILED = 4
    INFEASIBLE = 5
    STOPPED_EARLY = 6

  endTimeMs = _messages.IntegerField(1)
  errorMessage = _messages.StringField(2)
  evalLoss = _messages.FloatField(3)
  evaluationMetrics = _messages.MessageField('EvaluationMetrics', 4)
  hparamTuningEvaluationMetrics = _messages.MessageField('EvaluationMetrics', 5)
  hparams = _messages.MessageField('TrainingOptions', 6)
  startTimeMs = _messages.IntegerField(7)
  status = _messages.EnumField('StatusValueValuesEnum', 8)
  trainingLoss = _messages.FloatField(9)
  trialId = _messages.IntegerField(10)


class IndexUnusedReason(_messages.Message):
  r"""A IndexUnusedReason object.

  Fields:
    baseTable: [Output-only] Specifies the base table involved in the reason
      that no search index was used.
    code: [Output-only] Specifies the high-level reason for the scenario when
      no search index was used.
    indexName: [Output-only] Specifies the name of the unused search index, if
      available.
    message: [Output-only] Free form human-readable reason for the scenario
      when no search index was used.
  """

  baseTable = _messages.MessageField('TableReference', 1)
  code = _messages.StringField(2, default='$(reason.code)')
  indexName = _messages.StringField(3, default='$(reason.index_name)')
  message = _messages.StringField(4, default='$(reason.message)')


class IntArray(_messages.Message):
  r"""An array of int.

  Fields:
    elements: Elements in the int array.
  """

  elements = _messages.IntegerField(1, repeated=True)


class IntArrayHparamSearchSpace(_messages.Message):
  r"""Search space for int array.

  Fields:
    candidates: Candidates for the int array parameter.
  """

  candidates = _messages.MessageField('IntArray', 1, repeated=True)


class IntCandidates(_messages.Message):
  r"""Discrete candidates of an int hyperparameter.

  Fields:
    candidates: Candidates for the int parameter in increasing order.
  """

  candidates = _messages.IntegerField(1, repeated=True)


class IntHparamSearchSpace(_messages.Message):
  r"""Search space for an int hyperparameter.

  Fields:
    candidates: Candidates of the int hyperparameter.
    range: Range of the int hyperparameter.
  """

  candidates = _messages.MessageField('IntCandidates', 1)
  range = _messages.MessageField('IntRange', 2)


class IntRange(_messages.Message):
  r"""Range of an int hyperparameter.

  Fields:
    max: Max value of the int parameter.
    min: Min value of the int parameter.
  """

  max = _messages.IntegerField(1)
  min = _messages.IntegerField(2)


class IterationResult(_messages.Message):
  r"""A IterationResult object.

  Fields:
    durationMs: Time taken to run the iteration in milliseconds.
    evalLoss: Loss computed on the eval data at the end of iteration.
    index: Index of the iteration, 0 based.
    learnRate: Learn rate used for this iteration.
    trainingLoss: Loss computed on the training data at the end of iteration.
  """

  durationMs = _messages.IntegerField(1)
  evalLoss = _messages.FloatField(2)
  index = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  learnRate = _messages.FloatField(4)
  trainingLoss = _messages.FloatField(5)


class Job(_messages.Message):
  r"""A Job object.

  Fields:
    configuration: [Required] Describes the job configuration.
    etag: [Output-only] A hash of this resource.
    id: [Output-only] Opaque ID field of the job
    jobReference: [Optional] Reference describing the unique-per-user name of
      the job.
    kind: [Output-only] The type of the resource.
    selfLink: [Output-only] A URL that can be used to access this resource
      again.
    statistics: [Output-only] Information about the job, including starting
      time and ending time of the job.
    status: [Output-only] The status of this job. Examine this value when
      polling an asynchronous job to see if the job is complete.
    user_email: [Output-only] Email address of the user who ran the job.
  """

  configuration = _messages.MessageField('JobConfiguration', 1)
  etag = _messages.StringField(2)
  id = _messages.StringField(3)
  jobReference = _messages.MessageField('JobReference', 4)
  kind = _messages.StringField(5, default='bigquery#job')
  selfLink = _messages.StringField(6)
  statistics = _messages.MessageField('JobStatistics', 7)
  status = _messages.MessageField('JobStatus', 8)
  user_email = _messages.StringField(9)


class JobCancelResponse(_messages.Message):
  r"""A JobCancelResponse object.

  Fields:
    job: The final state of the job.
    kind: The resource type of the response.
  """

  job = _messages.MessageField('Job', 1)
  kind = _messages.StringField(2, default='bigquery#jobCancelResponse')


class JobConfiguration(_messages.Message):
  r"""A JobConfiguration object.

  Messages:
    LabelsValue: The labels associated with this job. You can use these to
      organize and group your jobs. Label keys and values can be no longer
      than 63 characters, can only contain lowercase letters, numeric
      characters, underscores and dashes. International characters are
      allowed. Label values are optional. Label keys must start with a letter
      and each label in the list must have a different key.

  Fields:
    copy: [Pick one] Copies a table.
    dryRun: [Optional] If set, don't actually run this job. A valid query will
      return a mostly empty response with some processing statistics, while an
      invalid query will return the same error it would if it wasn't a dry
      run. Behavior of non-query jobs is undefined.
    extract: [Pick one] Configures an extract job.
    jobTimeoutMs: [Optional] Job timeout in milliseconds. If this time limit
      is exceeded, BigQuery may attempt to terminate the job.
    jobType: [Output-only] The type of the job. Can be QUERY, LOAD, EXTRACT,
      COPY or UNKNOWN.
    labels: The labels associated with this job. You can use these to organize
      and group your jobs. Label keys and values can be no longer than 63
      characters, can only contain lowercase letters, numeric characters,
      underscores and dashes. International characters are allowed. Label
      values are optional. Label keys must start with a letter and each label
      in the list must have a different key.
    load: [Pick one] Configures a load job.
    query: [Pick one] Configures a query job.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this job. You can use these to organize and
    group your jobs. Label keys and values can be no longer than 63
    characters, can only contain lowercase letters, numeric characters,
    underscores and dashes. International characters are allowed. Label values
    are optional. Label keys must start with a letter and each label in the
    list must have a different key.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  copy = _messages.MessageField('JobConfigurationTableCopy', 1)
  dryRun = _messages.BooleanField(2)
  extract = _messages.MessageField('JobConfigurationExtract', 3)
  jobTimeoutMs = _messages.IntegerField(4)
  jobType = _messages.StringField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  load = _messages.MessageField('JobConfigurationLoad', 7)
  query = _messages.MessageField('JobConfigurationQuery', 8)


class JobConfigurationExtract(_messages.Message):
  r"""A JobConfigurationExtract object.

  Fields:
    compression: [Optional] The compression type to use for exported files.
      Possible values include GZIP, DEFLATE, SNAPPY, and NONE. The default
      value is NONE. DEFLATE and SNAPPY are only supported for Avro. Not
      applicable when extracting models.
    destinationFormat: [Optional] The exported file format. Possible values
      include CSV, NEWLINE_DELIMITED_JSON, PARQUET or AVRO for tables and
      ML_TF_SAVED_MODEL or ML_XGBOOST_BOOSTER for models. The default value
      for tables is CSV. Tables with nested or repeated fields cannot be
      exported as CSV. The default value for models is ML_TF_SAVED_MODEL.
    destinationUri: [Pick one] DEPRECATED: Use destinationUris instead,
      passing only one URI as necessary. The fully-qualified Google Cloud
      Storage URI where the extracted table should be written.
    destinationUris: [Pick one] A list of fully-qualified Google Cloud Storage
      URIs where the extracted table should be written.
    fieldDelimiter: [Optional] Delimiter to use between fields in the exported
      data. Default is ','. Not applicable when extracting models.
    printHeader: [Optional] Whether to print out a header row in the results.
      Default is true. Not applicable when extracting models.
    sourceModel: A reference to the model being exported.
    sourceTable: A reference to the table being exported.
    useAvroLogicalTypes: [Optional] If destinationFormat is set to "AVRO",
      this flag indicates whether to enable extracting applicable column types
      (such as TIMESTAMP) to their corresponding AVRO logical types
      (timestamp-micros), instead of only using their raw types (avro-long).
      Not applicable when extracting models.
  """

  compression = _messages.StringField(1)
  destinationFormat = _messages.StringField(2)
  destinationUri = _messages.StringField(3)
  destinationUris = _messages.StringField(4, repeated=True)
  fieldDelimiter = _messages.StringField(5)
  printHeader = _messages.BooleanField(6, default=True)
  sourceModel = _messages.MessageField('ModelReference', 7)
  sourceTable = _messages.MessageField('TableReference', 8)
  useAvroLogicalTypes = _messages.BooleanField(9)


class JobConfigurationLoad(_messages.Message):
  r"""A JobConfigurationLoad object.

  Fields:
    allowJaggedRows: [Optional] Accept rows that are missing trailing optional
      columns. The missing values are treated as nulls. If false, records with
      missing trailing columns are treated as bad records, and if there are
      too many bad records, an invalid error is returned in the job result.
      The default value is false. Only applicable to CSV, ignored for other
      formats.
    allowQuotedNewlines: Indicates if BigQuery should allow quoted data
      sections that contain newline characters in a CSV file. The default
      value is false.
    autodetect: [Optional] Indicates if we should automatically infer the
      options and schema for CSV and JSON sources.
    clustering: [Beta] Clustering specification for the destination table.
      Must be specified with time-based partitioning, data in the table will
      be first partitioned and subsequently clustered.
    connectionProperties: Connection properties.
    createDisposition: [Optional] Specifies whether the job is allowed to
      create new tables. The following values are supported: CREATE_IF_NEEDED:
      If the table does not exist, BigQuery creates the table. CREATE_NEVER:
      The table must already exist. If it does not, a 'notFound' error is
      returned in the job result. The default value is CREATE_IF_NEEDED.
      Creation, truncation and append actions occur as one atomic update upon
      job completion.
    createSession: If true, creates a new session, where session id will be a
      server generated random id. If false, runs query with an existing
      session_id passed in ConnectionProperty, otherwise runs the load job in
      non-session mode.
    decimalTargetTypes: [Optional] Defines the list of possible SQL data types
      to which the source decimal values are converted. This list and the
      precision and the scale parameters of the decimal field determine the
      target type. In the order of NUMERIC, BIGNUMERIC, and STRING, a type is
      picked if it is in the specified list and if it supports the precision
      and the scale. STRING supports all precision and scale values. If none
      of the listed types supports the precision and the scale, the type
      supporting the widest range in the specified list is picked, and if a
      value exceeds the supported range when reading the data, an error will
      be thrown. Example: Suppose the value of this field is ["NUMERIC",
      "BIGNUMERIC"]. If (precision,scale) is: (38,9) -> NUMERIC; (39,9) ->
      BIGNUMERIC (NUMERIC cannot hold 30 integer digits); (38,10) ->
      BIGNUMERIC (NUMERIC cannot hold 10 fractional digits); (76,38) ->
      BIGNUMERIC; (77,38) -> BIGNUMERIC (error if value exeeds supported
      range). This field cannot contain duplicate types. The order of the
      types in this field is ignored. For example, ["BIGNUMERIC", "NUMERIC"]
      is the same as ["NUMERIC", "BIGNUMERIC"] and NUMERIC always takes
      precedence over BIGNUMERIC. Defaults to ["NUMERIC", "STRING"] for ORC
      and ["NUMERIC"] for the other file formats.
    destinationEncryptionConfiguration: Custom encryption configuration (e.g.,
      Cloud KMS keys).
    destinationTable: [Required] The destination table to load the data into.
    destinationTableProperties: [Beta] [Optional] Properties with which to
      create the destination table if it is new.
    encoding: [Optional] The character encoding of the data. The supported
      values are UTF-8 or ISO-8859-1. The default value is UTF-8. BigQuery
      decodes the data after the raw, binary data has been split using the
      values of the quote and fieldDelimiter properties.
    fieldDelimiter: [Optional] The separator for fields in a CSV file. The
      separator can be any ISO-8859-1 single-byte character. To use a
      character in the range 128-255, you must encode the character as UTF8.
      BigQuery converts the string to ISO-8859-1 encoding, and then uses the
      first byte of the encoded string to split the data in its raw, binary
      state. BigQuery also supports the escape sequence "\t" to specify a tab
      separator. The default value is a comma (',').
    fileSetSpecType: [Optional] Specifies how source URIs are interpreted for
      constructing the file set to load. By default source URIs are expanded
      against the underlying storage. Other options include specifying
      manifest files. Only applicable to object storage systems.
    hivePartitioningOptions: [Optional] Options to configure hive partitioning
      support.
    ignoreUnknownValues: [Optional] Indicates if BigQuery should allow extra
      values that are not represented in the table schema. If true, the extra
      values are ignored. If false, records with extra columns are treated as
      bad records, and if there are too many bad records, an invalid error is
      returned in the job result. The default value is false. The sourceFormat
      property determines what BigQuery treats as an extra value: CSV:
      Trailing columns JSON: Named values that don't match any column names
    jsonExtension: [Optional] If sourceFormat is set to newline-delimited
      JSON, indicates whether it should be processed as a JSON variant such as
      GeoJSON. For a sourceFormat other than JSON, omit this field. If the
      sourceFormat is newline-delimited JSON: - for newline-delimited GeoJSON:
      set to GEOJSON.
    maxBadRecords: [Optional] The maximum number of bad records that BigQuery
      can ignore when running the job. If the number of bad records exceeds
      this value, an invalid error is returned in the job result. This is only
      valid for CSV and JSON. The default value is 0, which requires that all
      records are valid.
    nullMarker: [Optional] Specifies a string that represents a null value in
      a CSV file. For example, if you specify "\\N", BigQuery interprets "\\N"
      as a null value when loading a CSV file. The default value is the empty
      string. If you set this property to a custom value, BigQuery throws an
      error if an empty string is present for all data types except for STRING
      and BYTE. For STRING and BYTE columns, BigQuery interprets the empty
      string as an empty value.
    parquetOptions: [Optional] Options to configure parquet support.
    preserveAsciiControlCharacters: [Optional] Preserves the embedded ASCII
      control characters (the first 32 characters in the ASCII-table, from
      '\x00' to '\x1F') when loading from CSV. Only applicable to CSV, ignored
      for other formats.
    projectionFields: If sourceFormat is set to "DATASTORE_BACKUP", indicates
      which entity properties to load into BigQuery from a Cloud Datastore
      backup. Property names are case sensitive and must be top-level
      properties. If no properties are specified, BigQuery loads all
      properties. If any named property isn't found in the Cloud Datastore
      backup, an invalid error is returned in the job result.
    quote: [Optional] The value that is used to quote data sections in a CSV
      file. BigQuery converts the string to ISO-8859-1 encoding, and then uses
      the first byte of the encoded string to split the data in its raw,
      binary state. The default value is a double-quote ('"'). If your data
      does not contain quoted sections, set the property value to an empty
      string. If your data contains quoted newline characters, you must also
      set the allowQuotedNewlines property to true.
    rangePartitioning: [TrustedTester] Range partitioning specification for
      this table. Only one of timePartitioning and rangePartitioning should be
      specified.
    referenceFileSchemaUri: User provided referencing file with the expected
      reader schema, Available for the format: AVRO, PARQUET, ORC.
    schema: [Optional] The schema for the destination table. The schema can be
      omitted if the destination table already exists, or if you're loading
      data from Google Cloud Datastore.
    schemaInline: [Deprecated] The inline schema. For CSV schemas, specify as
      "Field1:Type1[,Field2:Type2]*". For example, "foo:STRING, bar:INTEGER,
      baz:FLOAT".
    schemaInlineFormat: [Deprecated] The format of the schemaInline property.
    schemaUpdateOptions: Allows the schema of the destination table to be
      updated as a side effect of the load job if a schema is autodetected or
      supplied in the job configuration. Schema update options are supported
      in two cases: when writeDisposition is WRITE_APPEND; when
      writeDisposition is WRITE_TRUNCATE and the destination table is a
      partition of a table, specified by partition decorators. For normal
      tables, WRITE_TRUNCATE will always overwrite the schema. One or more of
      the following values are specified: ALLOW_FIELD_ADDITION: allow adding a
      nullable field to the schema. ALLOW_FIELD_RELAXATION: allow relaxing a
      required field in the original schema to nullable.
    skipLeadingRows: [Optional] The number of rows at the top of a CSV file
      that BigQuery will skip when loading the data. The default value is 0.
      This property is useful if you have header rows in the file that should
      be skipped.
    sourceFormat: [Optional] The format of the data files. For CSV files,
      specify "CSV". For datastore backups, specify "DATASTORE_BACKUP". For
      newline-delimited JSON, specify "NEWLINE_DELIMITED_JSON". For Avro,
      specify "AVRO". For parquet, specify "PARQUET". For orc, specify "ORC".
      The default value is CSV.
    sourceUris: [Required] The fully-qualified URIs that point to your data in
      Google Cloud. For Google Cloud Storage URIs: Each URI can contain one
      '*' wildcard character and it must come after the 'bucket' name. Size
      limits related to load jobs apply to external data sources. For Google
      Cloud Bigtable URIs: Exactly one URI can be specified and it has be a
      fully specified and valid HTTPS URL for a Google Cloud Bigtable table.
      For Google Cloud Datastore backups: Exactly one URI can be specified.
      Also, the '*' wildcard character is not allowed.
    timePartitioning: Time-based partitioning specification for the
      destination table. Only one of timePartitioning and rangePartitioning
      should be specified.
    useAvroLogicalTypes: [Optional] If sourceFormat is set to "AVRO",
      indicates whether to interpret logical types as the corresponding
      BigQuery data type (for example, TIMESTAMP), instead of using the raw
      type (for example, INTEGER).
    writeDisposition: [Optional] Specifies the action that occurs if the
      destination table already exists. The following values are supported:
      WRITE_TRUNCATE: If the table already exists, BigQuery overwrites the
      table data. WRITE_APPEND: If the table already exists, BigQuery appends
      the data to the table. WRITE_EMPTY: If the table already exists and
      contains data, a 'duplicate' error is returned in the job result. The
      default value is WRITE_APPEND. Each action is atomic and only occurs if
      BigQuery is able to complete the job successfully. Creation, truncation
      and append actions occur as one atomic update upon job completion.
  """

  allowJaggedRows = _messages.BooleanField(1)
  allowQuotedNewlines = _messages.BooleanField(2)
  autodetect = _messages.BooleanField(3)
  clustering = _messages.MessageField('Clustering', 4)
  connectionProperties = _messages.MessageField('ConnectionProperty', 5, repeated=True)
  createDisposition = _messages.StringField(6)
  createSession = _messages.BooleanField(7)
  decimalTargetTypes = _messages.StringField(8, repeated=True)
  destinationEncryptionConfiguration = _messages.MessageField('EncryptionConfiguration', 9)
  destinationTable = _messages.MessageField('TableReference', 10)
  destinationTableProperties = _messages.MessageField('DestinationTableProperties', 11)
  encoding = _messages.StringField(12)
  fieldDelimiter = _messages.StringField(13)
  fileSetSpecType = _messages.StringField(14)
  hivePartitioningOptions = _messages.MessageField('HivePartitioningOptions', 15)
  ignoreUnknownValues = _messages.BooleanField(16)
  jsonExtension = _messages.StringField(17)
  maxBadRecords = _messages.IntegerField(18, variant=_messages.Variant.INT32)
  nullMarker = _messages.StringField(19)
  parquetOptions = _messages.MessageField('ParquetOptions', 20)
  preserveAsciiControlCharacters = _messages.BooleanField(21)
  projectionFields = _messages.StringField(22, repeated=True)
  quote = _messages.StringField(23, default='"')
  rangePartitioning = _messages.MessageField('RangePartitioning', 24)
  referenceFileSchemaUri = _messages.StringField(25)
  schema = _messages.MessageField('TableSchema', 26)
  schemaInline = _messages.StringField(27)
  schemaInlineFormat = _messages.StringField(28)
  schemaUpdateOptions = _messages.StringField(29, repeated=True)
  skipLeadingRows = _messages.IntegerField(30, variant=_messages.Variant.INT32)
  sourceFormat = _messages.StringField(31)
  sourceUris = _messages.StringField(32, repeated=True)
  timePartitioning = _messages.MessageField('TimePartitioning', 33)
  useAvroLogicalTypes = _messages.BooleanField(34)
  writeDisposition = _messages.StringField(35)


class JobConfigurationQuery(_messages.Message):
  r"""A JobConfigurationQuery object.

  Messages:
    TableDefinitionsValue: [Optional] If querying an external data source
      outside of BigQuery, describes the data format, location and other
      properties of the data source. By defining these properties, the data
      source can then be queried as if it were a standard BigQuery table.

  Fields:
    allowLargeResults: [Optional] If true and query uses legacy SQL dialect,
      allows the query to produce arbitrarily large result tables at a slight
      cost in performance. Requires destinationTable to be set. For standard
      SQL queries, this flag is ignored and large results are always allowed.
      However, you must still set destinationTable when result size exceeds
      the allowed maximum response size.
    clustering: [Beta] Clustering specification for the destination table.
      Must be specified with time-based partitioning, data in the table will
      be first partitioned and subsequently clustered.
    connectionProperties: Connection properties.
    continuous: [Optional] Specifies whether the query should be executed as a
      continuous query. The default value is false.
    createDisposition: [Optional] Specifies whether the job is allowed to
      create new tables. The following values are supported: CREATE_IF_NEEDED:
      If the table does not exist, BigQuery creates the table. CREATE_NEVER:
      The table must already exist. If it does not, a 'notFound' error is
      returned in the job result. The default value is CREATE_IF_NEEDED.
      Creation, truncation and append actions occur as one atomic update upon
      job completion.
    createSession: If true, creates a new session, where session id will be a
      server generated random id. If false, runs query with an existing
      session_id passed in ConnectionProperty, otherwise runs query in non-
      session mode.
    defaultDataset: [Optional] Specifies the default dataset to use for
      unqualified table names in the query. Note that this does not alter
      behavior of unqualified dataset names.
    destinationEncryptionConfiguration: Custom encryption configuration (e.g.,
      Cloud KMS keys).
    destinationTable: [Optional] Describes the table where the query results
      should be stored. If not present, a new table will be created to store
      the results. This property must be set for large results that exceed the
      maximum response size.
    flattenResults: [Optional] If true and query uses legacy SQL dialect,
      flattens all nested and repeated fields in the query results.
      allowLargeResults must be true if this is set to false. For standard SQL
      queries, this flag is ignored and results are never flattened.
    maximumBillingTier: [Optional] Limits the billing tier for this job.
      Queries that have resource usage beyond this tier will fail (without
      incurring a charge). If unspecified, this will be set to your project
      default.
    maximumBytesBilled: [Optional] Limits the bytes billed for this job.
      Queries that will have bytes billed beyond this limit will fail (without
      incurring a charge). If unspecified, this will be set to your project
      default.
    parameterMode: Standard SQL only. Set to POSITIONAL to use positional (?)
      query parameters or to NAMED to use named (@myparam) query parameters in
      this query.
    preserveNulls: [Deprecated] This property is deprecated.
    priority: [Optional] Specifies a priority for the query. Possible values
      include INTERACTIVE and BATCH. The default value is INTERACTIVE.
    query: [Required] SQL query text to execute. The useLegacySql field can be
      used to indicate whether the query uses legacy SQL or standard SQL.
    queryParameters: Query parameters for standard SQL queries.
    rangePartitioning: [TrustedTester] Range partitioning specification for
      this table. Only one of timePartitioning and rangePartitioning should be
      specified.
    schemaUpdateOptions: Allows the schema of the destination table to be
      updated as a side effect of the query job. Schema update options are
      supported in two cases: when writeDisposition is WRITE_APPEND; when
      writeDisposition is WRITE_TRUNCATE and the destination table is a
      partition of a table, specified by partition decorators. For normal
      tables, WRITE_TRUNCATE will always overwrite the schema. One or more of
      the following values are specified: ALLOW_FIELD_ADDITION: allow adding a
      nullable field to the schema. ALLOW_FIELD_RELAXATION: allow relaxing a
      required field in the original schema to nullable.
    tableDefinitions: [Optional] If querying an external data source outside
      of BigQuery, describes the data format, location and other properties of
      the data source. By defining these properties, the data source can then
      be queried as if it were a standard BigQuery table.
    timePartitioning: Time-based partitioning specification for the
      destination table. Only one of timePartitioning and rangePartitioning
      should be specified.
    useLegacySql: Specifies whether to use BigQuery's legacy SQL dialect for
      this query. The default value is true. If set to false, the query will
      use BigQuery's standard SQL: https://cloud.google.com/bigquery/sql-
      reference/ When useLegacySql is set to false, the value of
      flattenResults is ignored; query will be run as if flattenResults is
      false.
    useQueryCache: [Optional] Whether to look for the result in the query
      cache. The query cache is a best-effort cache that will be flushed
      whenever tables in the query are modified. Moreover, the query cache is
      only available when a query does not have a destination table specified.
      The default value is true.
    userDefinedFunctionResources: Describes user-defined function resources
      used in the query.
    writeDisposition: [Optional] Specifies the action that occurs if the
      destination table already exists. The following values are supported:
      WRITE_TRUNCATE: If the table already exists, BigQuery overwrites the
      table data and uses the schema from the query result. WRITE_APPEND: If
      the table already exists, BigQuery appends the data to the table.
      WRITE_EMPTY: If the table already exists and contains data, a
      'duplicate' error is returned in the job result. The default value is
      WRITE_EMPTY. Each action is atomic and only occurs if BigQuery is able
      to complete the job successfully. Creation, truncation and append
      actions occur as one atomic update upon job completion.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class TableDefinitionsValue(_messages.Message):
    r"""[Optional] If querying an external data source outside of BigQuery,
    describes the data format, location and other properties of the data
    source. By defining these properties, the data source can then be queried
    as if it were a standard BigQuery table.

    Messages:
      AdditionalProperty: An additional property for a TableDefinitionsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        TableDefinitionsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a TableDefinitionsValue object.

      Fields:
        key: Name of the additional property.
        value: A ExternalDataConfiguration attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ExternalDataConfiguration', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  allowLargeResults = _messages.BooleanField(1, default=False)
  clustering = _messages.MessageField('Clustering', 2)
  connectionProperties = _messages.MessageField('ConnectionProperty', 3, repeated=True)
  continuous = _messages.BooleanField(4)
  createDisposition = _messages.StringField(5)
  createSession = _messages.BooleanField(6)
  defaultDataset = _messages.MessageField('DatasetReference', 7)
  destinationEncryptionConfiguration = _messages.MessageField('EncryptionConfiguration', 8)
  destinationTable = _messages.MessageField('TableReference', 9)
  flattenResults = _messages.BooleanField(10, default=True)
  maximumBillingTier = _messages.IntegerField(11, variant=_messages.Variant.INT32, default=1)
  maximumBytesBilled = _messages.IntegerField(12)
  parameterMode = _messages.StringField(13)
  preserveNulls = _messages.BooleanField(14)
  priority = _messages.StringField(15)
  query = _messages.StringField(16)
  queryParameters = _messages.MessageField('QueryParameter', 17, repeated=True)
  rangePartitioning = _messages.MessageField('RangePartitioning', 18)
  schemaUpdateOptions = _messages.StringField(19, repeated=True)
  tableDefinitions = _messages.MessageField('TableDefinitionsValue', 20)
  timePartitioning = _messages.MessageField('TimePartitioning', 21)
  useLegacySql = _messages.BooleanField(22, default=True)
  useQueryCache = _messages.BooleanField(23, default=True)
  userDefinedFunctionResources = _messages.MessageField('UserDefinedFunctionResource', 24, repeated=True)
  writeDisposition = _messages.StringField(25)


class JobConfigurationTableCopy(_messages.Message):
  r"""A JobConfigurationTableCopy object.

  Fields:
    createDisposition: [Optional] Specifies whether the job is allowed to
      create new tables. The following values are supported: CREATE_IF_NEEDED:
      If the table does not exist, BigQuery creates the table. CREATE_NEVER:
      The table must already exist. If it does not, a 'notFound' error is
      returned in the job result. The default value is CREATE_IF_NEEDED.
      Creation, truncation and append actions occur as one atomic update upon
      job completion.
    destinationEncryptionConfiguration: Custom encryption configuration (e.g.,
      Cloud KMS keys).
    destinationExpirationTime: [Optional] The time when the destination table
      expires. Expired tables will be deleted and their storage reclaimed.
    destinationTable: [Required] The destination table
    operationType: [Optional] Supported operation types in table copy job.
    sourceTable: [Pick one] Source table to copy.
    sourceTables: [Pick one] Source tables to copy.
    writeDisposition: [Optional] Specifies the action that occurs if the
      destination table already exists. The following values are supported:
      WRITE_TRUNCATE: If the table already exists, BigQuery overwrites the
      table data. WRITE_APPEND: If the table already exists, BigQuery appends
      the data to the table. WRITE_EMPTY: If the table already exists and
      contains data, a 'duplicate' error is returned in the job result. The
      default value is WRITE_EMPTY. Each action is atomic and only occurs if
      BigQuery is able to complete the job successfully. Creation, truncation
      and append actions occur as one atomic update upon job completion.
  """

  createDisposition = _messages.StringField(1)
  destinationEncryptionConfiguration = _messages.MessageField('EncryptionConfiguration', 2)
  destinationExpirationTime = _messages.MessageField('extra_types.JsonValue', 3)
  destinationTable = _messages.MessageField('TableReference', 4)
  operationType = _messages.StringField(5)
  sourceTable = _messages.MessageField('TableReference', 6)
  sourceTables = _messages.MessageField('TableReference', 7, repeated=True)
  writeDisposition = _messages.StringField(8)


class JobList(_messages.Message):
  r"""A JobList object.

  Messages:
    JobsValueListEntry: A JobsValueListEntry object.

  Fields:
    etag: A hash of this page of results.
    jobs: List of jobs that were requested.
    kind: The resource type of the response.
    nextPageToken: A token to request the next page of results.
  """

  class JobsValueListEntry(_messages.Message):
    r"""A JobsValueListEntry object.

    Fields:
      configuration: [Full-projection-only] Specifies the job configuration.
      errorResult: A result object that will be present only if the job has
        failed.
      id: Unique opaque ID of the job.
      jobReference: Job reference uniquely identifying the job.
      kind: The resource type.
      state: Running state of the job. When the state is DONE, errorResult can
        be checked to determine whether the job succeeded or failed.
      statistics: [Output-only] Information about the job, including starting
        time and ending time of the job.
      status: [Full-projection-only] Describes the state of the job.
      user_email: [Full-projection-only] Email address of the user who ran the
        job.
    """

    configuration = _messages.MessageField('JobConfiguration', 1)
    errorResult = _messages.MessageField('ErrorProto', 2)
    id = _messages.StringField(3)
    jobReference = _messages.MessageField('JobReference', 4)
    kind = _messages.StringField(5, default='bigquery#job')
    state = _messages.StringField(6)
    statistics = _messages.MessageField('JobStatistics', 7)
    status = _messages.MessageField('JobStatus', 8)
    user_email = _messages.StringField(9)

  etag = _messages.StringField(1)
  jobs = _messages.MessageField('JobsValueListEntry', 2, repeated=True)
  kind = _messages.StringField(3, default='bigquery#jobList')
  nextPageToken = _messages.StringField(4)


class JobReference(_messages.Message):
  r"""A JobReference object.

  Fields:
    jobId: [Required] The ID of the job. The ID must contain only letters
      (a-z, A-Z), numbers (0-9), underscores (_), or dashes (-). The maximum
      length is 1,024 characters.
    location: The geographic location of the job. See details at
      https://cloud.google.com/bigquery/docs/locations#specifying_your_locatio
      n.
    projectId: [Required] The ID of the project containing this job.
  """

  jobId = _messages.StringField(1)
  location = _messages.StringField(2)
  projectId = _messages.StringField(3)


class JobStatistics(_messages.Message):
  r"""A JobStatistics object.

  Messages:
    ReservationUsageValueListEntry: A ReservationUsageValueListEntry object.

  Fields:
    completionRatio: [TrustedTester] [Output-only] Job progress (0.0 -> 1.0)
      for LOAD and EXTRACT jobs.
    copy: [Output-only] Statistics for a copy job.
    creationTime: [Output-only] Creation time of this job, in milliseconds
      since the epoch. This field will be present on all jobs.
    dataMaskingStatistics: [Output-only] Statistics for data masking. Present
      only for query and extract jobs.
    endTime: [Output-only] End time of this job, in milliseconds since the
      epoch. This field will be present whenever a job is in the DONE state.
    extract: [Output-only] Statistics for an extract job.
    load: [Output-only] Statistics for a load job.
    numChildJobs: [Output-only] Number of child jobs executed.
    parentJobId: [Output-only] If this is a child job, the id of the parent.
    query: [Output-only] Statistics for a query job.
    quotaDeferments: [Output-only] Quotas which delayed this job's start time.
    reservationUsage: [Output-only] Job resource usage breakdown by
      reservation.
    reservation_id: [Output-only] Name of the primary reservation assigned to
      this job. Note that this could be different than reservations reported
      in the reservation usage field if parent reservations were used to
      execute this job.
    rowLevelSecurityStatistics: [Output-only] [Preview] Statistics for row-
      level security. Present only for query and extract jobs.
    scriptStatistics: [Output-only] Statistics for a child job of a script.
    sessionInfo: [Output-only] [Preview] Information of the session if this
      job is part of one.
    startTime: [Output-only] Start time of this job, in milliseconds since the
      epoch. This field will be present when the job transitions from the
      PENDING state to either RUNNING or DONE.
    totalBytesProcessed: [Output-only] [Deprecated] Use the bytes processed in
      the query statistics instead.
    totalSlotMs: [Output-only] Slot-milliseconds for the job.
    transactionInfo: [Output-only] [Alpha] Information of the multi-statement
      transaction if this job is part of one.
  """

  class ReservationUsageValueListEntry(_messages.Message):
    r"""A ReservationUsageValueListEntry object.

    Fields:
      name: [Output-only] Reservation name or "unreserved" for on-demand
        resources usage.
      slotMs: [Output-only] Slot-milliseconds the job spent in the given
        reservation.
    """

    name = _messages.StringField(1)
    slotMs = _messages.IntegerField(2)

  completionRatio = _messages.FloatField(1)
  copy = _messages.MessageField('JobStatistics5', 2)
  creationTime = _messages.IntegerField(3)
  dataMaskingStatistics = _messages.MessageField('DataMaskingStatistics', 4)
  endTime = _messages.IntegerField(5)
  extract = _messages.MessageField('JobStatistics4', 6)
  load = _messages.MessageField('JobStatistics3', 7)
  numChildJobs = _messages.IntegerField(8)
  parentJobId = _messages.StringField(9)
  query = _messages.MessageField('JobStatistics2', 10)
  quotaDeferments = _messages.StringField(11, repeated=True)
  reservationUsage = _messages.MessageField('ReservationUsageValueListEntry', 12, repeated=True)
  reservation_id = _messages.StringField(13)
  rowLevelSecurityStatistics = _messages.MessageField('RowLevelSecurityStatistics', 14)
  scriptStatistics = _messages.MessageField('ScriptStatistics', 15)
  sessionInfo = _messages.MessageField('SessionInfo', 16)
  startTime = _messages.IntegerField(17)
  totalBytesProcessed = _messages.IntegerField(18)
  totalSlotMs = _messages.IntegerField(19)
  transactionInfo = _messages.MessageField('TransactionInfo', 20)


class JobStatistics2(_messages.Message):
  r"""A JobStatistics2 object.

  Messages:
    ReservationUsageValueListEntry: A ReservationUsageValueListEntry object.

  Fields:
    biEngineStatistics: BI Engine specific Statistics. [Output only] BI Engine
      specific Statistics.
    billingTier: [Output only] Billing tier for the job.
    cacheHit: [Output only] Whether the query result was fetched from the
      query cache.
    ddlAffectedRowAccessPolicyCount: [Output only] [Preview] The number of row
      access policies affected by a DDL statement. Present only for DROP ALL
      ROW ACCESS POLICIES queries.
    ddlDestinationTable: [Output only] The DDL destination table. Present only
      for ALTER TABLE RENAME TO queries. Note that ddl_target_table is used
      just for its type information.
    ddlOperationPerformed: The DDL operation performed, possibly dependent on
      the pre-existence of the DDL target. Possible values (new values might
      be added in the future): "CREATE": The query created the DDL target.
      "SKIP": No-op. Example cases: the query is CREATE TABLE IF NOT EXISTS
      while the table already exists, or the query is DROP TABLE IF EXISTS
      while the table does not exist. "REPLACE": The query replaced the DDL
      target. Example case: the query is CREATE OR REPLACE TABLE, and the
      table already exists. "DROP": The query deleted the DDL target.
    ddlTargetDataset: [Output only] The DDL target dataset. Present only for
      CREATE/ALTER/DROP SCHEMA queries.
    ddlTargetRoutine: The DDL target routine. Present only for CREATE/DROP
      FUNCTION/PROCEDURE queries.
    ddlTargetRowAccessPolicy: [Output only] [Preview] The DDL target row
      access policy. Present only for CREATE/DROP ROW ACCESS POLICY queries.
    ddlTargetTable: [Output only] The DDL target table. Present only for
      CREATE/DROP TABLE/VIEW and DROP ALL ROW ACCESS POLICIES queries.
    dmlStats: [Output only] Detailed statistics for DML statements Present
      only for DML statements INSERT, UPDATE, DELETE or TRUNCATE.
    estimatedBytesProcessed: [Output only] The original estimate of bytes
      processed for the job.
    mlStatistics: [Output only] Statistics of a BigQuery ML training job.
    modelTraining: [Output only, Beta] Information about create model query
      job progress.
    modelTrainingCurrentIteration: [Output only, Beta] Deprecated; do not use.
    modelTrainingExpectedTotalIteration: [Output only, Beta] Deprecated; do
      not use.
    numDmlAffectedRows: [Output only] The number of rows affected by a DML
      statement. Present only for DML statements INSERT, UPDATE or DELETE.
    queryPlan: [Output only] Describes execution plan for the query.
    referencedRoutines: [Output only] Referenced routines (persistent user-
      defined functions and stored procedures) for the job.
    referencedTables: [Output only] Referenced tables for the job. Queries
      that reference more than 50 tables will not have a complete list.
    reservationUsage: [Output only] Job resource usage breakdown by
      reservation.
    schema: [Output only] The schema of the results. Present only for
      successful dry run of non-legacy SQL queries.
    searchStatistics: [Output only] Search query specific statistics.
    sparkStatistics: [Output only] Statistics of a Spark procedure job.
    statementType: The type of query statement, if valid. Possible values (new
      values might be added in the future): "SELECT": SELECT query. "INSERT":
      INSERT query; see
      https://cloud.google.com/bigquery/docs/reference/standard-sql/data-
      manipulation-language. "UPDATE": UPDATE query; see
      https://cloud.google.com/bigquery/docs/reference/standard-sql/data-
      manipulation-language. "DELETE": DELETE query; see
      https://cloud.google.com/bigquery/docs/reference/standard-sql/data-
      manipulation-language. "MERGE": MERGE query; see
      https://cloud.google.com/bigquery/docs/reference/standard-sql/data-
      manipulation-language. "ALTER_TABLE": ALTER TABLE query. "ALTER_VIEW":
      ALTER VIEW query. "ASSERT": ASSERT condition AS 'description'.
      "CREATE_FUNCTION": CREATE FUNCTION query. "CREATE_MODEL": CREATE [OR
      REPLACE] MODEL ... AS SELECT ... . "CREATE_PROCEDURE": CREATE PROCEDURE
      query. "CREATE_TABLE": CREATE [OR REPLACE] TABLE without AS SELECT.
      "CREATE_TABLE_AS_SELECT": CREATE [OR REPLACE] TABLE ... AS SELECT ... .
      "CREATE_VIEW": CREATE [OR REPLACE] VIEW ... AS SELECT ... .
      "DROP_FUNCTION" : DROP FUNCTION query. "DROP_PROCEDURE": DROP PROCEDURE
      query. "DROP_TABLE": DROP TABLE query. "DROP_VIEW": DROP VIEW query.
    timeline: [Output only] [Beta] Describes a timeline of job execution.
    totalBytesBilled: [Output only] Total bytes billed for the job.
    totalBytesProcessed: [Output only] Total bytes processed for the job.
    totalBytesProcessedAccuracy: [Output only] For dry-run jobs,
      totalBytesProcessed is an estimate and this field specifies the accuracy
      of the estimate. Possible values can be: UNKNOWN: accuracy of the
      estimate is unknown. PRECISE: estimate is precise. LOWER_BOUND: estimate
      is lower bound of what the query would cost. UPPER_BOUND: estimate is
      upper bound of what the query would cost.
    totalPartitionsProcessed: [Output only] Total number of partitions
      processed from all partitioned tables referenced in the job.
    totalSlotMs: [Output only] Slot-milliseconds for the job.
    transferredBytes: [Output-only] Total bytes transferred for cross-cloud
      queries such as Cross Cloud Transfer and CREATE TABLE AS SELECT (CTAS).
    undeclaredQueryParameters: Standard SQL only: list of undeclared query
      parameters detected during a dry run validation.
  """

  class ReservationUsageValueListEntry(_messages.Message):
    r"""A ReservationUsageValueListEntry object.

    Fields:
      name: [Output only] Reservation name or "unreserved" for on-demand
        resources usage.
      slotMs: [Output only] Slot-milliseconds the job spent in the given
        reservation.
    """

    name = _messages.StringField(1)
    slotMs = _messages.IntegerField(2)

  biEngineStatistics = _messages.MessageField('BiEngineStatistics', 1)
  billingTier = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  cacheHit = _messages.BooleanField(3)
  ddlAffectedRowAccessPolicyCount = _messages.IntegerField(4)
  ddlDestinationTable = _messages.MessageField('TableReference', 5)
  ddlOperationPerformed = _messages.StringField(6)
  ddlTargetDataset = _messages.MessageField('DatasetReference', 7)
  ddlTargetRoutine = _messages.MessageField('RoutineReference', 8)
  ddlTargetRowAccessPolicy = _messages.MessageField('RowAccessPolicyReference', 9)
  ddlTargetTable = _messages.MessageField('TableReference', 10)
  dmlStats = _messages.MessageField('DmlStatistics', 11)
  estimatedBytesProcessed = _messages.IntegerField(12)
  mlStatistics = _messages.MessageField('MlStatistics', 13)
  modelTraining = _messages.MessageField('BigQueryModelTraining', 14)
  modelTrainingCurrentIteration = _messages.IntegerField(15, variant=_messages.Variant.INT32)
  modelTrainingExpectedTotalIteration = _messages.IntegerField(16)
  numDmlAffectedRows = _messages.IntegerField(17)
  queryPlan = _messages.MessageField('ExplainQueryStage', 18, repeated=True)
  referencedRoutines = _messages.MessageField('RoutineReference', 19, repeated=True)
  referencedTables = _messages.MessageField('TableReference', 20, repeated=True)
  reservationUsage = _messages.MessageField('ReservationUsageValueListEntry', 21, repeated=True)
  schema = _messages.MessageField('TableSchema', 22)
  searchStatistics = _messages.MessageField('SearchStatistics', 23)
  sparkStatistics = _messages.MessageField('SparkStatistics', 24)
  statementType = _messages.StringField(25)
  timeline = _messages.MessageField('QueryTimelineSample', 26, repeated=True)
  totalBytesBilled = _messages.IntegerField(27)
  totalBytesProcessed = _messages.IntegerField(28)
  totalBytesProcessedAccuracy = _messages.StringField(29)
  totalPartitionsProcessed = _messages.IntegerField(30)
  totalSlotMs = _messages.IntegerField(31)
  transferredBytes = _messages.IntegerField(32)
  undeclaredQueryParameters = _messages.MessageField('QueryParameter', 33, repeated=True)


class JobStatistics3(_messages.Message):
  r"""A JobStatistics3 object.

  Fields:
    badRecords: [Output-only] The number of bad records encountered. Note that
      if the job has failed because of more bad records encountered than the
      maximum allowed in the load job configuration, then this number can be
      less than the total number of bad records present in the input data.
    inputFileBytes: [Output-only] Number of bytes of source data in a load
      job.
    inputFiles: [Output-only] Number of source files in a load job.
    outputBytes: [Output-only] Size of the loaded data in bytes. Note that
      while a load job is in the running state, this value may change.
    outputRows: [Output-only] Number of rows imported in a load job. Note that
      while an import job is in the running state, this value may change.
  """

  badRecords = _messages.IntegerField(1)
  inputFileBytes = _messages.IntegerField(2)
  inputFiles = _messages.IntegerField(3)
  outputBytes = _messages.IntegerField(4)
  outputRows = _messages.IntegerField(5)


class JobStatistics4(_messages.Message):
  r"""A JobStatistics4 object.

  Fields:
    destinationUriFileCounts: [Output-only] Number of files per destination
      URI or URI pattern specified in the extract configuration. These values
      will be in the same order as the URIs specified in the 'destinationUris'
      field.
    inputBytes: [Output-only] Number of user bytes extracted into the result.
      This is the byte count as computed by BigQuery for billing purposes.
  """

  destinationUriFileCounts = _messages.IntegerField(1, repeated=True)
  inputBytes = _messages.IntegerField(2)


class JobStatistics5(_messages.Message):
  r"""A JobStatistics5 object.

  Fields:
    copiedLogicalBytes: [Output-only] Number of logical bytes copied to the
      destination table.
    copiedRows: [Output-only] Number of rows copied to the destination table.
  """

  copiedLogicalBytes = _messages.IntegerField(1)
  copiedRows = _messages.IntegerField(2)


class JobStatus(_messages.Message):
  r"""A JobStatus object.

  Fields:
    errorResult: [Output-only] Final error result of the job. If present,
      indicates that the job has completed and was unsuccessful.
    errors: [Output-only] The first errors encountered during the running of
      the job. The final message includes the number of errors that caused the
      process to stop. Errors here do not necessarily mean that the job has
      completed or was unsuccessful.
    state: [Output-only] Running state of the job.
  """

  errorResult = _messages.MessageField('ErrorProto', 1)
  errors = _messages.MessageField('ErrorProto', 2, repeated=True)
  state = _messages.StringField(3)


@encoding.MapUnrecognizedFields('additionalProperties')
class JsonObject(_messages.Message):
  r"""Represents a single JSON object.

  Messages:
    AdditionalProperty: An additional property for a JsonObject object.

  Fields:
    additionalProperties: Additional properties of type JsonObject
  """

  class AdditionalProperty(_messages.Message):
    r"""An additional property for a JsonObject object.

    Fields:
      key: Name of the additional property.
      value: A JsonValue attribute.
    """

    key = _messages.StringField(1)
    value = _messages.MessageField('JsonValue', 2)

  additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)


class JsonOptions(_messages.Message):
  r"""A JsonOptions object.

  Fields:
    encoding: [Optional] The character encoding of the data. The supported
      values are UTF-8, UTF-16BE, UTF-16LE, UTF-32BE, and UTF-32LE. The
      default value is UTF-8.
  """

  encoding = _messages.StringField(1)


JsonValue = extra_types.JsonValue


class ListModelsResponse(_messages.Message):
  r"""A ListModelsResponse object.

  Fields:
    models: Models in the requested dataset. Only the following fields are
      populated: model_reference, model_type, creation_time,
      last_modified_time and labels.
    nextPageToken: A token to request the next page of results.
  """

  models = _messages.MessageField('Model', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListRoutinesResponse(_messages.Message):
  r"""A ListRoutinesResponse object.

  Fields:
    nextPageToken: A token to request the next page of results.
    routines: Routines in the requested dataset. Unless read_mask is set in
      the request, only the following fields are populated: etag, project_id,
      dataset_id, routine_id, routine_type, creation_time, last_modified_time,
      language, and remote_function_options.
  """

  nextPageToken = _messages.StringField(1)
  routines = _messages.MessageField('Routine', 2, repeated=True)


class ListRowAccessPoliciesResponse(_messages.Message):
  r"""Response message for the ListRowAccessPolicies method.

  Fields:
    nextPageToken: A token to request the next page of results.
    rowAccessPolicies: Row access policies on the requested table.
  """

  nextPageToken = _messages.StringField(1)
  rowAccessPolicies = _messages.MessageField('RowAccessPolicy', 2, repeated=True)


class LocationMetadata(_messages.Message):
  r"""BigQuery-specific metadata about a location. This will be set on
  google.cloud.location.Location.metadata in Cloud Location API responses.

  Fields:
    legacyLocationId: The legacy BigQuery location ID, e.g. "EU" for the
      "europe" location. This is for any API consumers that need the legacy
      "US" and "EU" locations.
  """

  legacyLocationId = _messages.StringField(1)


class MaterializedViewDefinition(_messages.Message):
  r"""A MaterializedViewDefinition object.

  Fields:
    allowNonIncrementalDefinition: [Optional] Allow non incremental
      materialized view definition. The default value is "false".
    enableRefresh: [Optional] [TrustedTester] Enable automatic refresh of the
      materialized view when the base table is updated. The default value is
      "true".
    lastRefreshTime: [Output-only] [TrustedTester] The time when this
      materialized view was last modified, in milliseconds since the epoch.
    maxStaleness: [Optional] Max staleness of data that could be returned when
      materizlized view is queried (formatted as Google SQL Interval type).
    query: [Required] A query whose result is persisted.
    refreshIntervalMs: [Optional] [TrustedTester] The maximum frequency at
      which this materialized view will be refreshed. The default value is
      "1800000" (30 minutes).
  """

  allowNonIncrementalDefinition = _messages.BooleanField(1)
  enableRefresh = _messages.BooleanField(2)
  lastRefreshTime = _messages.IntegerField(3)
  maxStaleness = _messages.BytesField(4)
  query = _messages.StringField(5)
  refreshIntervalMs = _messages.IntegerField(6)


class MlStatistics(_messages.Message):
  r"""A MlStatistics object.

  Fields:
    iterationResults: Results for all completed iterations.
    maxIterations: Maximum number of iterations specified as max_iterations in
      the 'CREATE MODEL' query. The actual number of iterations may be less
      than this number due to early stop.
  """

  iterationResults = _messages.MessageField('IterationResult', 1, repeated=True)
  maxIterations = _messages.IntegerField(2)


class Model(_messages.Message):
  r"""A Model object.

  Enums:
    ModelTypeValueValuesEnum: Output only. Type of the model resource.

  Messages:
    LabelsValue: The labels associated with this model. You can use these to
      organize and group your models. Label keys and values can be no longer
      than 63 characters, can only contain lowercase letters, numeric
      characters, underscores and dashes. International characters are
      allowed. Label values are optional. Label keys must start with a letter
      and each label in the list must have a different key.

  Fields:
    bestTrialId: The best trial_id across all training runs.
    creationTime: Output only. The time when this model was created, in
      millisecs since the epoch.
    defaultTrialId: Output only. The default trial_id to use in TVFs when the
      trial_id is not passed in. For single-objective [hyperparameter
      tuning](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-
      tuning-overview) models, this is the best trial ID. For multi-objective
      [hyperparameter tuning](/bigquery-ml/docs/reference/standard-
      sql/bigqueryml-syntax-hp-tuning-overview) models, this is the smallest
      trial ID among all Pareto optimal trials.
    description: Optional. A user-friendly description of this model.
    encryptionConfiguration: Custom encryption configuration (e.g., Cloud KMS
      keys). This shows the encryption configuration of the model data while
      stored in BigQuery storage. This field can be used with PatchModel to
      update encryption key for an already encrypted model.
    etag: Output only. A hash of this resource.
    expirationTime: Optional. The time when this model expires, in
      milliseconds since the epoch. If not present, the model will persist
      indefinitely. Expired models will be deleted and their storage
      reclaimed. The defaultTableExpirationMs property of the encapsulating
      dataset can be used to set a default expirationTime on newly created
      models.
    featureColumns: Output only. Input feature columns for the model
      inference. If the model is trained with TRANSFORM clause, these are the
      input of the TRANSFORM clause.
    friendlyName: Optional. A descriptive name for this model.
    hparamSearchSpaces: Output only. All hyperparameter search spaces in this
      model.
    hparamTrials: Output only. Trials of a [hyperparameter tuning](/bigquery-
      ml/docs/reference/standard-sql/bigqueryml-syntax-hp-tuning-overview)
      model sorted by trial_id.
    labelColumns: Output only. Label columns that were used to train this
      model. The output of the model will have a "predicted_" prefix to these
      columns.
    labels: The labels associated with this model. You can use these to
      organize and group your models. Label keys and values can be no longer
      than 63 characters, can only contain lowercase letters, numeric
      characters, underscores and dashes. International characters are
      allowed. Label values are optional. Label keys must start with a letter
      and each label in the list must have a different key.
    lastModifiedTime: Output only. The time when this model was last modified,
      in millisecs since the epoch.
    location: Output only. The geographic location where the model resides.
      This value is inherited from the dataset.
    modelReference: Required. Unique identifier for this model.
    modelType: Output only. Type of the model resource.
    optimalTrialIds: Output only. For single-objective [hyperparameter
      tuning](/bigquery-ml/docs/reference/standard-sql/bigqueryml-syntax-hp-
      tuning-overview) models, it only contains the best trial. For multi-
      objective [hyperparameter tuning](/bigquery-ml/docs/reference/standard-
      sql/bigqueryml-syntax-hp-tuning-overview) models, it contains all Pareto
      optimal trials sorted by trial_id.
    remoteModelInfo: Output only. Remote model info
    trainingRuns: Information for all training runs in increasing order of
      start_time.
    transformColumns: Output only. This field will be populated if a TRANSFORM
      clause was used to train a model. TRANSFORM clause (if used) takes
      feature_columns as input and outputs transform_columns.
      transform_columns then are used to train the model.
  """

  class ModelTypeValueValuesEnum(_messages.Enum):
    r"""Output only. Type of the model resource.

    Values:
      MODEL_TYPE_UNSPECIFIED: <no description>
      LINEAR_REGRESSION: Linear regression model.
      LOGISTIC_REGRESSION: Logistic regression based classification model.
      KMEANS: K-means clustering model.
      MATRIX_FACTORIZATION: Matrix factorization model.
      DNN_CLASSIFIER: DNN classifier model.
      TENSORFLOW: An imported TensorFlow model.
      DNN_REGRESSOR: DNN regressor model.
      XGBOOST: An imported XGBoost model.
      BOOSTED_TREE_REGRESSOR: Boosted tree regressor model.
      BOOSTED_TREE_CLASSIFIER: Boosted tree classifier model.
      ARIMA: ARIMA model.
      AUTOML_REGRESSOR: AutoML Tables regression model.
      AUTOML_CLASSIFIER: AutoML Tables classification model.
      PCA: Prinpical Component Analysis model.
      DNN_LINEAR_COMBINED_CLASSIFIER: Wide-and-deep classifier model.
      DNN_LINEAR_COMBINED_REGRESSOR: Wide-and-deep regressor model.
      AUTOENCODER: Autoencoder model.
      ARIMA_PLUS: New name for the ARIMA model.
      ARIMA_PLUS_XREG: ARIMA with external regressors.
      RANDOM_FOREST_REGRESSOR: Random forest regressor model.
      RANDOM_FOREST_CLASSIFIER: Random forest classifier model.
      TENSORFLOW_LITE: An imported TensorFlow Lite model.
      ONNX: An imported ONNX model.
    """
    MODEL_TYPE_UNSPECIFIED = 0
    LINEAR_REGRESSION = 1
    LOGISTIC_REGRESSION = 2
    KMEANS = 3
    MATRIX_FACTORIZATION = 4
    DNN_CLASSIFIER = 5
    TENSORFLOW = 6
    DNN_REGRESSOR = 7
    XGBOOST = 8
    BOOSTED_TREE_REGRESSOR = 9
    BOOSTED_TREE_CLASSIFIER = 10
    ARIMA = 11
    AUTOML_REGRESSOR = 12
    AUTOML_CLASSIFIER = 13
    PCA = 14
    DNN_LINEAR_COMBINED_CLASSIFIER = 15
    DNN_LINEAR_COMBINED_REGRESSOR = 16
    AUTOENCODER = 17
    ARIMA_PLUS = 18
    ARIMA_PLUS_XREG = 19
    RANDOM_FOREST_REGRESSOR = 20
    RANDOM_FOREST_CLASSIFIER = 21
    TENSORFLOW_LITE = 22
    ONNX = 23

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this model. You can use these to organize
    and group your models. Label keys and values can be no longer than 63
    characters, can only contain lowercase letters, numeric characters,
    underscores and dashes. International characters are allowed. Label values
    are optional. Label keys must start with a letter and each label in the
    list must have a different key.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bestTrialId = _messages.IntegerField(1)
  creationTime = _messages.IntegerField(2)
  defaultTrialId = _messages.IntegerField(3)
  description = _messages.StringField(4)
  encryptionConfiguration = _messages.MessageField('EncryptionConfiguration', 5)
  etag = _messages.StringField(6)
  expirationTime = _messages.IntegerField(7)
  featureColumns = _messages.MessageField('StandardSqlField', 8, repeated=True)
  friendlyName = _messages.StringField(9)
  hparamSearchSpaces = _messages.MessageField('HparamSearchSpaces', 10)
  hparamTrials = _messages.MessageField('HparamTuningTrial', 11, repeated=True)
  labelColumns = _messages.MessageField('StandardSqlField', 12, repeated=True)
  labels = _messages.MessageField('LabelsValue', 13)
  lastModifiedTime = _messages.IntegerField(14)
  location = _messages.StringField(15)
  modelReference = _messages.MessageField('ModelReference', 16)
  modelType = _messages.EnumField('ModelTypeValueValuesEnum', 17)
  optimalTrialIds = _messages.IntegerField(18, repeated=True)
  remoteModelInfo = _messages.MessageField('RemoteModelInfo', 19)
  trainingRuns = _messages.MessageField('TrainingRun', 20, repeated=True)
  transformColumns = _messages.MessageField('TransformColumn', 21, repeated=True)


class ModelDefinition(_messages.Message):
  r"""A ModelDefinition object.

  Messages:
    ModelOptionsValue: [Output-only, Beta] Model options used for the first
      training run. These options are immutable for subsequent training runs.
      Default values are used for any options not specified in the input
      query.

  Fields:
    modelOptions: [Output-only, Beta] Model options used for the first
      training run. These options are immutable for subsequent training runs.
      Default values are used for any options not specified in the input
      query.
    trainingRuns: [Output-only, Beta] Information about ml training runs, each
      training run comprises of multiple iterations and there may be multiple
      training runs for the model if warm start is used or if a user decides
      to continue a previously cancelled query.
  """

  class ModelOptionsValue(_messages.Message):
    r"""[Output-only, Beta] Model options used for the first training run.
    These options are immutable for subsequent training runs. Default values
    are used for any options not specified in the input query.

    Fields:
      labels: A string attribute.
      lossType: A string attribute.
      modelType: A string attribute.
    """

    labels = _messages.StringField(1, repeated=True)
    lossType = _messages.StringField(2)
    modelType = _messages.StringField(3)

  modelOptions = _messages.MessageField('ModelOptionsValue', 1)
  trainingRuns = _messages.MessageField('BqmlTrainingRun', 2, repeated=True)


class ModelReference(_messages.Message):
  r"""A ModelReference object.

  Fields:
    datasetId: Required. The ID of the dataset containing this model.
    modelId: Required. The ID of the model. The ID must contain only letters
      (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is
      1,024 characters.
    projectId: Required. The ID of the project containing this model.
  """

  datasetId = _messages.StringField(1)
  modelId = _messages.StringField(2)
  projectId = _messages.StringField(3)


class MultiClassClassificationMetrics(_messages.Message):
  r"""Evaluation metrics for multi-class classification/classifier models.

  Fields:
    aggregateClassificationMetrics: Aggregate classification metrics.
    confusionMatrixList: Confusion matrix at different thresholds.
  """

  aggregateClassificationMetrics = _messages.MessageField('AggregateClassificationMetrics', 1)
  confusionMatrixList = _messages.MessageField('ConfusionMatrix', 2, repeated=True)


class ParquetOptions(_messages.Message):
  r"""A ParquetOptions object.

  Fields:
    enableListInference: [Optional] Indicates whether to use schema inference
      specifically for Parquet LIST logical type.
    enumAsString: [Optional] Indicates whether to infer Parquet ENUM logical
      type as STRING instead of BYTES by default.
  """

  enableListInference = _messages.BooleanField(1)
  enumAsString = _messages.BooleanField(2)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  version = _messages.IntegerField(4, variant=_messages.Variant.INT32)


class PrincipalComponentInfo(_messages.Message):
  r"""Principal component infos, used only for eigen decomposition based
  models, e.g., PCA. Ordered by explained_variance in the descending order.

  Fields:
    cumulativeExplainedVarianceRatio: The explained_variance is pre-ordered in
      the descending order to compute the cumulative explained variance ratio.
    explainedVariance: Explained variance by this principal component, which
      is simply the eigenvalue.
    explainedVarianceRatio: Explained_variance over the total explained
      variance.
    principalComponentId: Id of the principal component.
  """

  cumulativeExplainedVarianceRatio = _messages.FloatField(1)
  explainedVariance = _messages.FloatField(2)
  explainedVarianceRatio = _messages.FloatField(3)
  principalComponentId = _messages.IntegerField(4)


class ProjectList(_messages.Message):
  r"""A ProjectList object.

  Messages:
    ProjectsValueListEntry: A ProjectsValueListEntry object.

  Fields:
    etag: A hash of the page of results
    kind: The type of list.
    nextPageToken: A token to request the next page of results.
    projects: Projects to which you have at least READ access.
    totalItems: The total number of projects in the list.
  """

  class ProjectsValueListEntry(_messages.Message):
    r"""A ProjectsValueListEntry object.

    Fields:
      friendlyName: A descriptive name for this project.
      id: An opaque ID of this project.
      kind: The resource type.
      numericId: The numeric ID of this project.
      projectReference: A unique reference to this project.
    """

    friendlyName = _messages.StringField(1)
    id = _messages.StringField(2)
    kind = _messages.StringField(3, default='bigquery#project')
    numericId = _messages.IntegerField(4, variant=_messages.Variant.UINT64)
    projectReference = _messages.MessageField('ProjectReference', 5)

  etag = _messages.StringField(1)
  kind = _messages.StringField(2, default='bigquery#projectList')
  nextPageToken = _messages.StringField(3)
  projects = _messages.MessageField('ProjectsValueListEntry', 4, repeated=True)
  totalItems = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class ProjectReference(_messages.Message):
  r"""A ProjectReference object.

  Fields:
    projectId: [Required] ID of the project. Can be either the numeric ID or
      the assigned ID of the project.
  """

  projectId = _messages.StringField(1)


class QueryParameter(_messages.Message):
  r"""A QueryParameter object.

  Fields:
    name: [Optional] If unset, this is a positional parameter. Otherwise,
      should be unique within a query.
    parameterType: [Required] The type of this parameter.
    parameterValue: [Required] The value of this parameter.
  """

  name = _messages.StringField(1)
  parameterType = _messages.MessageField('QueryParameterType', 2)
  parameterValue = _messages.MessageField('QueryParameterValue', 3)


class QueryParameterType(_messages.Message):
  r"""A QueryParameterType object.

  Messages:
    StructTypesValueListEntry: A StructTypesValueListEntry object.

  Fields:
    arrayType: [Optional] The type of the array's elements, if this is an
      array.
    structTypes: [Optional] The types of the fields of this struct, in order,
      if this is a struct.
    type: [Required] The top level type of this field.
  """

  class StructTypesValueListEntry(_messages.Message):
    r"""A StructTypesValueListEntry object.

    Fields:
      description: [Optional] Human-oriented description of the field.
      name: [Optional] The name of this field.
      type: [Required] The type of this field.
    """

    description = _messages.StringField(1)
    name = _messages.StringField(2)
    type = _messages.MessageField('QueryParameterType', 3)

  arrayType = _messages.MessageField('QueryParameterType', 1)
  structTypes = _messages.MessageField('StructTypesValueListEntry', 2, repeated=True)
  type = _messages.StringField(3)


class QueryParameterValue(_messages.Message):
  r"""A QueryParameterValue object.

  Messages:
    StructValuesValue: [Optional] The struct field values, in order of the
      struct type's declaration.

  Fields:
    arrayValues: [Optional] The array values, if this is an array type.
    structValues: [Optional] The struct field values, in order of the struct
      type's declaration.
    value: [Optional] The value of this value, if a simple scalar type.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class StructValuesValue(_messages.Message):
    r"""[Optional] The struct field values, in order of the struct type's
    declaration.

    Messages:
      AdditionalProperty: An additional property for a StructValuesValue
        object.

    Fields:
      additionalProperties: Additional properties of type StructValuesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a StructValuesValue object.

      Fields:
        key: Name of the additional property.
        value: A QueryParameterValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('QueryParameterValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  arrayValues = _messages.MessageField('QueryParameterValue', 1, repeated=True)
  structValues = _messages.MessageField('StructValuesValue', 2)
  value = _messages.StringField(3)


class QueryRequest(_messages.Message):
  r"""A QueryRequest object.

  Messages:
    LabelsValue: The labels associated with this job. You can use these to
      organize and group your jobs. Label keys and values can be no longer
      than 63 characters, can only contain lowercase letters, numeric
      characters, underscores and dashes. International characters are
      allowed. Label values are optional. Label keys must start with a letter
      and each label in the list must have a different key.

  Fields:
    connectionProperties: Connection properties.
    continuous: [Optional] Specifies whether the query should be executed as a
      continuous query. The default value is false.
    createSession: If true, creates a new session, where session id will be a
      server generated random id. If false, runs query with an existing
      session_id passed in ConnectionProperty, otherwise runs query in non-
      session mode.
    defaultDataset: [Optional] Specifies the default datasetId and projectId
      to assume for any unqualified table names in the query. If not set, all
      table names in the query string must be qualified in the format
      'datasetId.tableId'.
    dryRun: [Optional] If set to true, BigQuery doesn't run the job. Instead,
      if the query is valid, BigQuery returns statistics about the job such as
      how many bytes would be processed. If the query is invalid, an error
      returns. The default value is false.
    kind: The resource type of the request.
    labels: The labels associated with this job. You can use these to organize
      and group your jobs. Label keys and values can be no longer than 63
      characters, can only contain lowercase letters, numeric characters,
      underscores and dashes. International characters are allowed. Label
      values are optional. Label keys must start with a letter and each label
      in the list must have a different key.
    location: The geographic location where the job should run. See details at
      https://cloud.google.com/bigquery/docs/locations#specifying_your_locatio
      n.
    maxResults: [Optional] The maximum number of rows of data to return per
      page of results. Setting this flag to a small value such as 1000 and
      then paging through results might improve reliability when the query
      result set is large. In addition to this limit, responses are also
      limited to 10 MB. By default, there is no maximum row count, and only
      the byte limit applies.
    maximumBytesBilled: [Optional] Limits the bytes billed for this job.
      Queries that will have bytes billed beyond this limit will fail (without
      incurring a charge). If unspecified, this will be set to your project
      default.
    parameterMode: Standard SQL only. Set to POSITIONAL to use positional (?)
      query parameters or to NAMED to use named (@myparam) query parameters in
      this query.
    preserveNulls: [Deprecated] This property is deprecated.
    query: [Required] A query string, following the BigQuery query syntax, of
      the query to execute. Example: "SELECT count(f1) FROM
      [myProjectId:myDatasetId.myTableId]".
    queryParameters: Query parameters for Standard SQL queries.
    requestId: A unique user provided identifier to ensure idempotent behavior
      for queries. Note that this is different from the job_id. It has the
      following properties: 1. It is case-sensitive, limited to up to 36 ASCII
      characters. A UUID is recommended. 2. Read only queries can ignore this
      token since they are nullipotent by definition. 3. For the purposes of
      idempotency ensured by the request_id, a request is considered duplicate
      of another only if they have the same request_id and are actually
      duplicates. When determining whether a request is a duplicate of the
      previous request, all parameters in the request that may affect the
      behavior are considered. For example, query, connection_properties,
      query_parameters, use_legacy_sql are parameters that affect the result
      and are considered when determining whether a request is a duplicate,
      but properties like timeout_ms don't affect the result and are thus not
      considered. Dry run query requests are never considered duplicate of
      another request. 4. When a duplicate mutating query request is detected,
      it returns: a. the results of the mutation if it completes successfully
      within the timeout. b. the running operation if it is still in progress
      at the end of the timeout. 5. Its lifetime is limited to 15 minutes. In
      other words, if two requests are sent with the same request_id, but more
      than 15 minutes apart, idempotency is not guaranteed.
    timeoutMs: [Optional] How long to wait for the query to complete, in
      milliseconds, before the request times out and returns. Note that this
      is only a timeout for the request, not the query. If the query takes
      longer to run than the timeout value, the call returns without any
      results and with the 'jobComplete' flag set to false. You can call
      GetQueryResults() to wait for the query to complete and read the
      results. The default value is 10000 milliseconds (10 seconds).
    useLegacySql: Specifies whether to use BigQuery's legacy SQL dialect for
      this query. The default value is true. If set to false, the query will
      use BigQuery's standard SQL: https://cloud.google.com/bigquery/sql-
      reference/ When useLegacySql is set to false, the value of
      flattenResults is ignored; query will be run as if flattenResults is
      false.
    useQueryCache: [Optional] Whether to look for the result in the query
      cache. The query cache is a best-effort cache that will be flushed
      whenever tables in the query are modified. The default value is true.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this job. You can use these to organize and
    group your jobs. Label keys and values can be no longer than 63
    characters, can only contain lowercase letters, numeric characters,
    underscores and dashes. International characters are allowed. Label values
    are optional. Label keys must start with a letter and each label in the
    list must have a different key.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  connectionProperties = _messages.MessageField('ConnectionProperty', 1, repeated=True)
  continuous = _messages.BooleanField(2)
  createSession = _messages.BooleanField(3)
  defaultDataset = _messages.MessageField('DatasetReference', 4)
  dryRun = _messages.BooleanField(5)
  kind = _messages.StringField(6, default='bigquery#queryRequest')
  labels = _messages.MessageField('LabelsValue', 7)
  location = _messages.StringField(8)
  maxResults = _messages.IntegerField(9, variant=_messages.Variant.UINT32)
  maximumBytesBilled = _messages.IntegerField(10)
  parameterMode = _messages.StringField(11)
  preserveNulls = _messages.BooleanField(12)
  query = _messages.StringField(13)
  queryParameters = _messages.MessageField('QueryParameter', 14, repeated=True)
  requestId = _messages.StringField(15)
  timeoutMs = _messages.IntegerField(16, variant=_messages.Variant.UINT32)
  useLegacySql = _messages.BooleanField(17, default=True)
  useQueryCache = _messages.BooleanField(18, default=True)


class QueryResponse(_messages.Message):
  r"""A QueryResponse object.

  Fields:
    cacheHit: Whether the query result was fetched from the query cache.
    dmlStats: [Output-only] Detailed statistics for DML statements Present
      only for DML statements INSERT, UPDATE, DELETE or TRUNCATE.
    errors: [Output-only] The first errors or warnings encountered during the
      running of the job. The final message includes the number of errors that
      caused the process to stop. Errors here do not necessarily mean that the
      job has completed or was unsuccessful.
    jobComplete: Whether the query has completed or not. If rows or totalRows
      are present, this will always be true. If this is false, totalRows will
      not be available.
    jobReference: Reference to the Job that was created to run the query. This
      field will be present even if the original request timed out, in which
      case GetQueryResults can be used to read the results once the query has
      completed. Since this API only returns the first page of results,
      subsequent pages can be fetched via the same mechanism
      (GetQueryResults).
    kind: The resource type.
    numDmlAffectedRows: [Output-only] The number of rows affected by a DML
      statement. Present only for DML statements INSERT, UPDATE or DELETE.
    pageToken: A token used for paging results.
    rows: An object with as many results as can be contained within the
      maximum permitted reply size. To get any additional rows, you can call
      GetQueryResults and specify the jobReference returned above.
    schema: The schema of the results. Present only when the query completes
      successfully.
    sessionInfo: [Output-only] [Preview] Information of the session if this
      job is part of one.
    totalBytesProcessed: The total number of bytes processed for this query.
      If this query was a dry run, this is the number of bytes that would be
      processed if the query were run.
    totalRows: The total number of rows in the complete query result set,
      which can be more than the number of rows in this single page of
      results.
  """

  cacheHit = _messages.BooleanField(1)
  dmlStats = _messages.MessageField('DmlStatistics', 2)
  errors = _messages.MessageField('ErrorProto', 3, repeated=True)
  jobComplete = _messages.BooleanField(4)
  jobReference = _messages.MessageField('JobReference', 5)
  kind = _messages.StringField(6, default='bigquery#queryResponse')
  numDmlAffectedRows = _messages.IntegerField(7)
  pageToken = _messages.StringField(8)
  rows = _messages.MessageField('TableRow', 9, repeated=True)
  schema = _messages.MessageField('TableSchema', 10)
  sessionInfo = _messages.MessageField('SessionInfo', 11)
  totalBytesProcessed = _messages.IntegerField(12)
  totalRows = _messages.IntegerField(13, variant=_messages.Variant.UINT64)


class QueryTimelineSample(_messages.Message):
  r"""A QueryTimelineSample object.

  Fields:
    activeUnits: Total number of units currently being processed by workers.
      This does not correspond directly to slot usage. This is the largest
      value observed since the last sample.
    completedUnits: Total parallel units of work completed by this query.
    elapsedMs: Milliseconds elapsed since the start of query execution.
    estimatedRunnableUnits: Units of work that can be scheduled immediately.
      Providing additional slots for these units of work will speed up the
      query, provided no other query in the reservation needs additional
      slots.
    pendingUnits: Total units of work remaining for the query. This number can
      be revised (increased or decreased) while the query is running.
    totalSlotMs: Cumulative slot-ms consumed by the query.
  """

  activeUnits = _messages.IntegerField(1)
  completedUnits = _messages.IntegerField(2)
  elapsedMs = _messages.IntegerField(3)
  estimatedRunnableUnits = _messages.IntegerField(4)
  pendingUnits = _messages.IntegerField(5)
  totalSlotMs = _messages.IntegerField(6)


class RangePartitioning(_messages.Message):
  r"""A RangePartitioning object.

  Messages:
    RangeValue: [TrustedTester] [Required] Defines the ranges for range
      partitioning.

  Fields:
    field: [TrustedTester] [Required] The table is partitioned by this field.
      The field must be a top-level NULLABLE/REQUIRED field. The only
      supported type is INTEGER/INT64.
    range: [TrustedTester] [Required] Defines the ranges for range
      partitioning.
  """

  class RangeValue(_messages.Message):
    r"""[TrustedTester] [Required] Defines the ranges for range partitioning.

    Fields:
      end: [TrustedTester] [Required] The end of range partitioning,
        exclusive.
      interval: [TrustedTester] [Required] The width of each interval.
      start: [TrustedTester] [Required] The start of range partitioning,
        inclusive.
    """

    end = _messages.IntegerField(1)
    interval = _messages.IntegerField(2)
    start = _messages.IntegerField(3)

  field = _messages.StringField(1)
  range = _messages.MessageField('RangeValue', 2)


class RankingMetrics(_messages.Message):
  r"""Evaluation metrics used by weighted-ALS models specified by
  feedback_type=implicit.

  Fields:
    averageRank: Determines the goodness of a ranking by computing the
      percentile rank from the predicted confidence and dividing it by the
      original rank.
    meanAveragePrecision: Calculates a precision per user for all the items by
      ranking them and then averages all the precisions across all the users.
    meanSquaredError: Similar to the mean squared error computed in regression
      and explicit recommendation models except instead of computing the
      rating directly, the output from evaluate is computed against a
      preference which is 1 or 0 depending on if the rating exists or not.
    normalizedDiscountedCumulativeGain: A metric to determine the goodness of
      a ranking calculated from the predicted confidence by comparing it to an
      ideal rank measured by the original ratings.
  """

  averageRank = _messages.FloatField(1)
  meanAveragePrecision = _messages.FloatField(2)
  meanSquaredError = _messages.FloatField(3)
  normalizedDiscountedCumulativeGain = _messages.FloatField(4)


class RegressionMetrics(_messages.Message):
  r"""Evaluation metrics for regression and explicit feedback type matrix
  factorization models.

  Fields:
    meanAbsoluteError: Mean absolute error.
    meanSquaredError: Mean squared error.
    meanSquaredLogError: Mean squared log error.
    medianAbsoluteError: Median absolute error.
    rSquared: R^2 score. This corresponds to r2_score in ML.EVALUATE.
  """

  meanAbsoluteError = _messages.FloatField(1)
  meanSquaredError = _messages.FloatField(2)
  meanSquaredLogError = _messages.FloatField(3)
  medianAbsoluteError = _messages.FloatField(4)
  rSquared = _messages.FloatField(5)


class RemoteFunctionOptions(_messages.Message):
  r"""Options for a remote user-defined function.

  Messages:
    UserDefinedContextValue: User-defined context as a set of key/value pairs,
      which will be sent as function invocation context together with batched
      arguments in the requests to the remote service. The total number of
      bytes of keys and values must be less than 8KB.

  Fields:
    connection: Fully qualified name of the user-provided connection object
      which holds the authentication information to send requests to the
      remote service. Format: ```"projects/{projectId}/locations/{locationId}/
      connections/{connectionId}"```
    endpoint: Endpoint of the user-provided remote service, e.g.
      ```https://us-east1-my_gcf_project.cloudfunctions.net/remote_add```
    maxBatchingRows: Max number of rows in each batch sent to the remote
      service. If absent or if 0, BigQuery dynamically decides the number of
      rows in a batch.
    userDefinedContext: User-defined context as a set of key/value pairs,
      which will be sent as function invocation context together with batched
      arguments in the requests to the remote service. The total number of
      bytes of keys and values must be less than 8KB.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class UserDefinedContextValue(_messages.Message):
    r"""User-defined context as a set of key/value pairs, which will be sent
    as function invocation context together with batched arguments in the
    requests to the remote service. The total number of bytes of keys and
    values must be less than 8KB.

    Messages:
      AdditionalProperty: An additional property for a UserDefinedContextValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        UserDefinedContextValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a UserDefinedContextValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  connection = _messages.StringField(1)
  endpoint = _messages.StringField(2)
  maxBatchingRows = _messages.IntegerField(3)
  userDefinedContext = _messages.MessageField('UserDefinedContextValue', 4)


class RemoteModelInfo(_messages.Message):
  r"""Remote Model Info

  Enums:
    RemoteServiceTypeValueValuesEnum: Output only. The remote service type for
      remote model.

  Fields:
    connection: Output only. Fully qualified name of the user-provided
      connection object of the remote model. Format: ```"projects/{project_id}
      /locations/{location_id}/connections/{connection_id}"```
    endpoint: Output only. The endpoint for remote model.
    maxBatchingRows: Output only. Max number of rows in each batch sent to the
      remote service. If unset, the number of rows in each batch is set
      dynamically.
    remoteModelVersion: Output only. The model version for LLM.
    remoteServiceType: Output only. The remote service type for remote model.
  """

  class RemoteServiceTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The remote service type for remote model.

    Values:
      REMOTE_SERVICE_TYPE_UNSPECIFIED: Unspecified remote service type.
      CLOUD_AI_TRANSLATE_V3: V3 Cloud AI Translation API. See more details at
        [Cloud Translation API]
        (https://cloud.google.com/translate/docs/reference/rest).
      CLOUD_AI_VISION_V1: V1 Cloud AI Vision API See more details at [Cloud
        Vision API] (https://cloud.google.com/vision/docs/reference/rest).
      CLOUD_AI_NATURAL_LANGUAGE_V1: V1 Cloud AI Natural Language API. See more
        details at [REST Resource:
        documents](https://cloud.google.com/natural-
        language/docs/reference/rest/v1/documents).
    """
    REMOTE_SERVICE_TYPE_UNSPECIFIED = 0
    CLOUD_AI_TRANSLATE_V3 = 1
    CLOUD_AI_VISION_V1 = 2
    CLOUD_AI_NATURAL_LANGUAGE_V1 = 3

  connection = _messages.StringField(1)
  endpoint = _messages.StringField(2)
  maxBatchingRows = _messages.IntegerField(3)
  remoteModelVersion = _messages.StringField(4)
  remoteServiceType = _messages.EnumField('RemoteServiceTypeValueValuesEnum', 5)


class Routine(_messages.Message):
  r"""A user-defined function or a stored procedure.

  Enums:
    DataGovernanceTypeValueValuesEnum: Optional. Data governance specific
      option, if the value is DATA_MASKING, the function will be validated as
      masking functions.
    DeterminismLevelValueValuesEnum: Optional. The determinism level of the
      JavaScript UDF, if defined.
    LanguageValueValuesEnum: Optional. Defaults to "SQL" if
      remote_function_options field is absent, not set otherwise.
    RoutineTypeValueValuesEnum: Required. The type of routine.

  Fields:
    arguments: Optional.
    creationTime: Output only. The time when this routine was created, in
      milliseconds since the epoch.
    dataGovernanceType: Optional. Data governance specific option, if the
      value is DATA_MASKING, the function will be validated as masking
      functions.
    definitionBody: Required. The body of the routine. For functions, this is
      the expression in the AS clause. If language=SQL, it is the substring
      inside (but excluding) the parentheses. For example, for the function
      created with the following statement: `CREATE FUNCTION JoinLines(x
      string, y string) as (concat(x, "\n", y))` The definition_body is
      `concat(x, "\n", y)` (\n is not replaced with linebreak). If
      language=JAVASCRIPT, it is the evaluated string in the AS clause. For
      example, for the function created with the following statement: `CREATE
      FUNCTION f() RETURNS STRING LANGUAGE js AS 'return "\n";\n'` The
      definition_body is `return "\n";\n` Note that both \n are replaced with
      linebreaks.
    description: Optional. The description of the routine, if defined.
    determinismLevel: Optional. The determinism level of the JavaScript UDF,
      if defined.
    etag: Output only. A hash of this resource.
    importedLibraries: Optional. If language = "JAVASCRIPT", this field stores
      the path of the imported JAVASCRIPT libraries.
    language: Optional. Defaults to "SQL" if remote_function_options field is
      absent, not set otherwise.
    lastModifiedTime: Output only. The time when this routine was last
      modified, in milliseconds since the epoch.
    remoteFunctionOptions: Optional. Remote function specific options.
    returnTableType: Optional. Can be set only if routine_type =
      "TABLE_VALUED_FUNCTION". If absent, the return table type is inferred
      from definition_body at query time in each query that references this
      routine. If present, then the columns in the evaluated table result will
      be cast to match the column types specified in return table type, at
      query time.
    returnType: Optional if language = "SQL"; required otherwise. Cannot be
      set if routine_type = "TABLE_VALUED_FUNCTION". If absent, the return
      type is inferred from definition_body at query time in each query that
      references this routine. If present, then the evaluated result will be
      cast to the specified returned type at query time. For example, for the
      functions created with the following statements: * `CREATE FUNCTION
      Add(x FLOAT64, y FLOAT64) RETURNS FLOAT64 AS (x + y);` * `CREATE
      FUNCTION Increment(x FLOAT64) AS (Add(x, 1));` * `CREATE FUNCTION
      Decrement(x FLOAT64) RETURNS FLOAT64 AS (Add(x, -1));` The return_type
      is `{type_kind: "FLOAT64"}` for `Add` and `Decrement`, and is absent for
      `Increment` (inferred as FLOAT64 at query time). Suppose the function
      `Add` is replaced by `CREATE OR REPLACE FUNCTION Add(x INT64, y INT64)
      AS (x + y);` Then the inferred return type of `Increment` is
      automatically changed to INT64 at query time, while the return type of
      `Decrement` remains FLOAT64.
    routineReference: Required. Reference describing the ID of this routine.
    routineType: Required. The type of routine.
    sparkOptions: Optional. Spark specific options.
    strictMode: Optional. Can be set for procedures only. If true (default),
      the definition body will be validated in the creation and the updates of
      the procedure. For procedures with an argument of ANY TYPE, the
      definition body validtion is not supported at creation/update time, and
      thus this field must be set to false explicitly.
  """

  class DataGovernanceTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Data governance specific option, if the value is
    DATA_MASKING, the function will be validated as masking functions.

    Values:
      DATA_GOVERNANCE_TYPE_UNSPECIFIED: Unspecified data governance type.
      DATA_MASKING: The data governance type is data masking.
    """
    DATA_GOVERNANCE_TYPE_UNSPECIFIED = 0
    DATA_MASKING = 1

  class DeterminismLevelValueValuesEnum(_messages.Enum):
    r"""Optional. The determinism level of the JavaScript UDF, if defined.

    Values:
      DETERMINISM_LEVEL_UNSPECIFIED: The determinism of the UDF is
        unspecified.
      DETERMINISTIC: The UDF is deterministic, meaning that 2 function calls
        with the same inputs always produce the same result, even across 2
        query runs.
      NOT_DETERMINISTIC: The UDF is not deterministic.
    """
    DETERMINISM_LEVEL_UNSPECIFIED = 0
    DETERMINISTIC = 1
    NOT_DETERMINISTIC = 2

  class LanguageValueValuesEnum(_messages.Enum):
    r"""Optional. Defaults to "SQL" if remote_function_options field is
    absent, not set otherwise.

    Values:
      LANGUAGE_UNSPECIFIED: <no description>
      SQL: SQL language.
      JAVASCRIPT: JavaScript language.
      PYTHON: Python language.
      JAVA: Java language.
      SCALA: Scala language.
    """
    LANGUAGE_UNSPECIFIED = 0
    SQL = 1
    JAVASCRIPT = 2
    PYTHON = 3
    JAVA = 4
    SCALA = 5

  class RoutineTypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of routine.

    Values:
      ROUTINE_TYPE_UNSPECIFIED: <no description>
      SCALAR_FUNCTION: Non-built-in persistent scalar function.
      PROCEDURE: Stored procedure.
      TABLE_VALUED_FUNCTION: Non-built-in persistent TVF.
      AGGREGATE_FUNCTION: Non-built-in persistent aggregate function.
    """
    ROUTINE_TYPE_UNSPECIFIED = 0
    SCALAR_FUNCTION = 1
    PROCEDURE = 2
    TABLE_VALUED_FUNCTION = 3
    AGGREGATE_FUNCTION = 4

  arguments = _messages.MessageField('Argument', 1, repeated=True)
  creationTime = _messages.IntegerField(2)
  dataGovernanceType = _messages.EnumField('DataGovernanceTypeValueValuesEnum', 3)
  definitionBody = _messages.StringField(4)
  description = _messages.StringField(5)
  determinismLevel = _messages.EnumField('DeterminismLevelValueValuesEnum', 6)
  etag = _messages.StringField(7)
  importedLibraries = _messages.StringField(8, repeated=True)
  language = _messages.EnumField('LanguageValueValuesEnum', 9)
  lastModifiedTime = _messages.IntegerField(10)
  remoteFunctionOptions = _messages.MessageField('RemoteFunctionOptions', 11)
  returnTableType = _messages.MessageField('StandardSqlTableType', 12)
  returnType = _messages.MessageField('StandardSqlDataType', 13)
  routineReference = _messages.MessageField('RoutineReference', 14)
  routineType = _messages.EnumField('RoutineTypeValueValuesEnum', 15)
  sparkOptions = _messages.MessageField('SparkOptions', 16)
  strictMode = _messages.BooleanField(17)


class RoutineReference(_messages.Message):
  r"""A RoutineReference object.

  Fields:
    datasetId: Required. The ID of the dataset containing this routine.
    projectId: Required. The ID of the project containing this routine.
    routineId: Required. The ID of the routine. The ID must contain only
      letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum
      length is 256 characters.
  """

  datasetId = _messages.StringField(1)
  projectId = _messages.StringField(2)
  routineId = _messages.StringField(3)


class Row(_messages.Message):
  r"""A single row in the confusion matrix.

  Fields:
    actualLabel: The original label of this row.
    entries: Info describing predicted label distribution.
  """

  actualLabel = _messages.StringField(1)
  entries = _messages.MessageField('Entry', 2, repeated=True)


class RowAccessPolicy(_messages.Message):
  r"""Represents access on a subset of rows on the specified table, defined by
  its filter predicate. Access to the subset of rows is controlled by its IAM
  policy.

  Fields:
    creationTime: Output only. The time when this row access policy was
      created, in milliseconds since the epoch.
    etag: Output only. A hash of this resource.
    filterPredicate: Required. A SQL boolean expression that represents the
      rows defined by this row access policy, similar to the boolean
      expression in a WHERE clause of a SELECT query on a table. References to
      other tables, routines, and temporary functions are not supported.
      Examples: region="EU" date_field = CAST('2019-9-27' as DATE)
      nullable_field is not NULL numeric_field BETWEEN 1.0 AND 5.0
    lastModifiedTime: Output only. The time when this row access policy was
      last modified, in milliseconds since the epoch.
    rowAccessPolicyReference: Required. Reference describing the ID of this
      row access policy.
  """

  creationTime = _messages.StringField(1)
  etag = _messages.StringField(2)
  filterPredicate = _messages.StringField(3)
  lastModifiedTime = _messages.StringField(4)
  rowAccessPolicyReference = _messages.MessageField('RowAccessPolicyReference', 5)


class RowAccessPolicyReference(_messages.Message):
  r"""A RowAccessPolicyReference object.

  Fields:
    datasetId: Required. The ID of the dataset containing this row access
      policy.
    policyId: Required. The ID of the row access policy. The ID must contain
      only letters (a-z, A-Z), numbers (0-9), or underscores (_). The maximum
      length is 256 characters.
    projectId: Required. The ID of the project containing this row access
      policy.
    tableId: Required. The ID of the table containing this row access policy.
  """

  datasetId = _messages.StringField(1)
  policyId = _messages.StringField(2)
  projectId = _messages.StringField(3)
  tableId = _messages.StringField(4)


class RowLevelSecurityStatistics(_messages.Message):
  r"""A RowLevelSecurityStatistics object.

  Fields:
    rowLevelSecurityApplied: [Output-only] [Preview] Whether any accessed data
      was protected by row access policies.
  """

  rowLevelSecurityApplied = _messages.BooleanField(1)


class ScriptStackFrame(_messages.Message):
  r"""A ScriptStackFrame object.

  Fields:
    endColumn: [Output-only] One-based end column.
    endLine: [Output-only] One-based end line.
    procedureId: [Output-only] Name of the active procedure, empty if in a
      top-level script.
    startColumn: [Output-only] One-based start column.
    startLine: [Output-only] One-based start line.
    text: [Output-only] Text of the current statement/expression.
  """

  endColumn = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  endLine = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  procedureId = _messages.StringField(3)
  startColumn = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  startLine = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  text = _messages.StringField(6)


class ScriptStatistics(_messages.Message):
  r"""A ScriptStatistics object.

  Fields:
    evaluationKind: [Output-only] Whether this child job was a statement or
      expression.
    stackFrames: Stack trace showing the line/column/procedure name of each
      frame on the stack at the point where the current evaluation happened.
      The leaf frame is first, the primary script is last. Never empty.
  """

  evaluationKind = _messages.StringField(1)
  stackFrames = _messages.MessageField('ScriptStackFrame', 2, repeated=True)


class SearchStatistics(_messages.Message):
  r"""A SearchStatistics object.

  Fields:
    indexUnusedReasons: When index_usage_mode is UNUSED or PARTIALLY_USED,
      this field explains why index was not used in all or part of the search
      query. If index_usage_mode is FULLLY_USED, this field is not populated.
    indexUsageMode: Specifies index usage mode for the query.
  """

  indexUnusedReasons = _messages.MessageField('IndexUnusedReason', 1, repeated=True)
  indexUsageMode = _messages.StringField(2)


class SessionInfo(_messages.Message):
  r"""A SessionInfo object.

  Fields:
    sessionId: [Output-only] // [Preview] Id of the session.
  """

  sessionId = _messages.StringField(1)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class SnapshotDefinition(_messages.Message):
  r"""A SnapshotDefinition object.

  Fields:
    baseTableReference: [Required] Reference describing the ID of the table
      that was snapshot.
    snapshotTime: [Required] The time at which the base table was snapshot.
      This value is reported in the JSON response using RFC3339 format.
  """

  baseTableReference = _messages.MessageField('TableReference', 1)
  snapshotTime = _message_types.DateTimeField(2)


class SparkLoggingInfo(_messages.Message):
  r"""A SparkLoggingInfo object.

  Fields:
    project_id: [Output-only] Project ID used for logging
    resource_type: [Output-only] Resource type used for logging
  """

  project_id = _messages.StringField(1)
  resource_type = _messages.StringField(2)


class SparkOptions(_messages.Message):
  r"""Options for a user-defined Spark routine.

  Messages:
    PropertiesValue: Configuration properties as a set of key/value pairs,
      which will be passed on to the Spark application. For more information,
      see [Apache Spark](https://spark.apache.org/docs/latest/index.html) and
      the [procedure option
      list](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#procedure_option_list).

  Fields:
    archiveUris: Archive files to be extracted into the working directory of
      each executor. For more information about Apache Spark, see [Apache
      Spark](https://spark.apache.org/docs/latest/index.html).
    connection: Fully qualified name of the user-provided Spark connection
      object. Format: ```"projects/{project_id}/locations/{location_id}/connec
      tions/{connection_id}"```
    containerImage: Custom container image for the runtime environment.
    fileUris: Files to be placed in the working directory of each executor.
      For more information about Apache Spark, see [Apache
      Spark](https://spark.apache.org/docs/latest/index.html).
    jarUris: JARs to include on the driver and executor CLASSPATH. For more
      information about Apache Spark, see [Apache
      Spark](https://spark.apache.org/docs/latest/index.html).
    mainClass: The fully qualified name of a class in jar_uris, for example,
      com.example.wordcount. Exactly one of main_class and main_jar_uri field
      should be set for Java/Scala language type.
    mainFileUri: The main file/jar URI of the Spark application. Exactly one
      of the definition_body field and the main_file_uri field must be set for
      Python. Exactly one of main_class and main_file_uri field should be set
      for Java/Scala language type.
    properties: Configuration properties as a set of key/value pairs, which
      will be passed on to the Spark application. For more information, see
      [Apache Spark](https://spark.apache.org/docs/latest/index.html) and the
      [procedure option
      list](https://cloud.google.com/bigquery/docs/reference/standard-
      sql/data-definition-language#procedure_option_list).
    pyFileUris: Python files to be placed on the PYTHONPATH for PySpark
      application. Supported file types: `.py`, `.egg`, and `.zip`. For more
      information about Apache Spark, see [Apache
      Spark](https://spark.apache.org/docs/latest/index.html).
    runtimeVersion: Runtime version. If not specified, the default runtime
      version is used.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class PropertiesValue(_messages.Message):
    r"""Configuration properties as a set of key/value pairs, which will be
    passed on to the Spark application. For more information, see [Apache
    Spark](https://spark.apache.org/docs/latest/index.html) and the [procedure
    option list](https://cloud.google.com/bigquery/docs/reference/standard-
    sql/data-definition-language#procedure_option_list).

    Messages:
      AdditionalProperty: An additional property for a PropertiesValue object.

    Fields:
      additionalProperties: Additional properties of type PropertiesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a PropertiesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  archiveUris = _messages.StringField(1, repeated=True)
  connection = _messages.StringField(2)
  containerImage = _messages.StringField(3)
  fileUris = _messages.StringField(4, repeated=True)
  jarUris = _messages.StringField(5, repeated=True)
  mainClass = _messages.StringField(6)
  mainFileUri = _messages.StringField(7)
  properties = _messages.MessageField('PropertiesValue', 8)
  pyFileUris = _messages.StringField(9, repeated=True)
  runtimeVersion = _messages.StringField(10)


class SparkStatistics(_messages.Message):
  r"""A SparkStatistics object.

  Messages:
    EndpointsValue: [Output-only] Endpoints generated for the Spark job.

  Fields:
    endpoints: [Output-only] Endpoints generated for the Spark job.
    loggingInfo: [Output-only] Logging info is used to generate a link to
      Cloud Logging.
    sparkJobId: [Output-only] Spark job id if a Spark job is created
      successfully.
    sparkJobLocation: [Output-only] Location where the Spark job is executed.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EndpointsValue(_messages.Message):
    r"""[Output-only] Endpoints generated for the Spark job.

    Messages:
      AdditionalProperty: An additional property for a EndpointsValue object.

    Fields:
      additionalProperties: Additional properties of type EndpointsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EndpointsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  endpoints = _messages.MessageField('EndpointsValue', 1)
  loggingInfo = _messages.MessageField('SparkLoggingInfo', 2)
  sparkJobId = _messages.StringField(3)
  sparkJobLocation = _messages.StringField(4)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    AltValueValuesEnum: Data format for the response.

  Fields:
    alt: Data format for the response.
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: An opaque string that represents a user for quota purposes.
      Must not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    userIp: Deprecated. Please use quotaUser instead.
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for the response.

    Values:
      json: Responses with Content-Type of application/json
    """
    json = 0

  alt = _messages.EnumField('AltValueValuesEnum', 1, default='json')
  fields = _messages.StringField(2)
  key = _messages.StringField(3)
  oauth_token = _messages.StringField(4)
  prettyPrint = _messages.BooleanField(5, default=True)
  quotaUser = _messages.StringField(6)
  trace = _messages.StringField(7)
  userIp = _messages.StringField(8)


class StandardSqlDataType(_messages.Message):
  r"""The data type of a variable such as a function argument. Examples
  include: * INT64: `{"typeKind": "INT64"}` * ARRAY: { "typeKind": "ARRAY",
  "arrayElementType": {"typeKind": "STRING"} } * STRUCT>: { "typeKind":
  "STRUCT", "structType": { "fields": [ { "name": "x", "type": {"typeKind":
  "STRING"} }, { "name": "y", "type": { "typeKind": "ARRAY",
  "arrayElementType": {"typeKind": "DATE"} } } ] } }

  Enums:
    TypeKindValueValuesEnum: Required. The top level type of this field. Can
      be any GoogleSQL data type (e.g., "INT64", "DATE", "ARRAY").

  Fields:
    arrayElementType: The type of the array's elements, if type_kind =
      "ARRAY".
    rangeElementType: The type of the range's elements, if type_kind =
      "RANGE".
    structType: The fields of this struct, in order, if type_kind = "STRUCT".
    typeKind: Required. The top level type of this field. Can be any GoogleSQL
      data type (e.g., "INT64", "DATE", "ARRAY").
  """

  class TypeKindValueValuesEnum(_messages.Enum):
    r"""Required. The top level type of this field. Can be any GoogleSQL data
    type (e.g., "INT64", "DATE", "ARRAY").

    Values:
      TYPE_KIND_UNSPECIFIED: Invalid type.
      INT64: Encoded as a string in decimal format.
      BOOL: Encoded as a boolean "false" or "true".
      FLOAT64: Encoded as a number, or string "NaN", "Infinity" or
        "-Infinity".
      STRING: Encoded as a string value.
      BYTES: Encoded as a base64 string per RFC 4648, section 4.
      TIMESTAMP: Encoded as an RFC 3339 timestamp with mandatory "Z" time zone
        string: 1985-04-12T23:20:50.52Z
      DATE: Encoded as RFC 3339 full-date format string: 1985-04-12
      TIME: Encoded as RFC 3339 partial-time format string: 23:20:50.52
      DATETIME: Encoded as RFC 3339 full-date "T" partial-time:
        1985-04-12T23:20:50.52
      INTERVAL: Encoded as fully qualified 3 part: 0-5 15 2:30:45.6
      GEOGRAPHY: Encoded as WKT
      NUMERIC: Encoded as a decimal string.
      BIGNUMERIC: Encoded as a decimal string.
      JSON: Encoded as a string.
      ARRAY: Encoded as a list with types matching Type.array_type.
      STRUCT: Encoded as a list with fields of type Type.struct_type[i]. List
        is used because a JSON object cannot have duplicate field names.
      RANGE: Encoded as a pair with types matching range_element_type. Pairs
        must begin with "[", end with ")", and be separated by ", ".
    """
    TYPE_KIND_UNSPECIFIED = 0
    INT64 = 1
    BOOL = 2
    FLOAT64 = 3
    STRING = 4
    BYTES = 5
    TIMESTAMP = 6
    DATE = 7
    TIME = 8
    DATETIME = 9
    INTERVAL = 10
    GEOGRAPHY = 11
    NUMERIC = 12
    BIGNUMERIC = 13
    JSON = 14
    ARRAY = 15
    STRUCT = 16
    RANGE = 17

  arrayElementType = _messages.MessageField('StandardSqlDataType', 1)
  rangeElementType = _messages.MessageField('StandardSqlDataType', 2)
  structType = _messages.MessageField('StandardSqlStructType', 3)
  typeKind = _messages.EnumField('TypeKindValueValuesEnum', 4)


class StandardSqlField(_messages.Message):
  r"""A field or a column.

  Fields:
    name: Optional. The name of this field. Can be absent for struct fields.
    type: Optional. The type of this parameter. Absent if not explicitly
      specified (e.g., CREATE FUNCTION statement can omit the return type; in
      this case the output parameter does not have this "type" field).
  """

  name = _messages.StringField(1)
  type = _messages.MessageField('StandardSqlDataType', 2)


class StandardSqlStructType(_messages.Message):
  r"""A StandardSqlStructType object.

  Fields:
    fields: A StandardSqlField attribute.
  """

  fields = _messages.MessageField('StandardSqlField', 1, repeated=True)


class StandardSqlTableType(_messages.Message):
  r"""A table type

  Fields:
    columns: The columns in this table type
  """

  columns = _messages.MessageField('StandardSqlField', 1, repeated=True)


class Streamingbuffer(_messages.Message):
  r"""A Streamingbuffer object.

  Fields:
    estimatedBytes: [Output-only] A lower-bound estimate of the number of
      bytes currently in the streaming buffer.
    estimatedRows: [Output-only] A lower-bound estimate of the number of rows
      currently in the streaming buffer.
    oldestEntryTime: [Output-only] Contains the timestamp of the oldest entry
      in the streaming buffer, in milliseconds since the epoch, if the
      streaming buffer is available.
  """

  estimatedBytes = _messages.IntegerField(1, variant=_messages.Variant.UINT64)
  estimatedRows = _messages.IntegerField(2, variant=_messages.Variant.UINT64)
  oldestEntryTime = _messages.IntegerField(3, variant=_messages.Variant.UINT64)


class StringHparamSearchSpace(_messages.Message):
  r"""Search space for string and enum.

  Fields:
    candidates: Canididates for the string or enum parameter in lower case.
  """

  candidates = _messages.StringField(1, repeated=True)


class Table(_messages.Message):
  r"""A Table object.

  Messages:
    LabelsValue: The labels associated with this table. You can use these to
      organize and group your tables. Label keys and values can be no longer
      than 63 characters, can only contain lowercase letters, numeric
      characters, underscores and dashes. International characters are
      allowed. Label values are optional. Label keys must start with a letter
      and each label in the list must have a different key.

  Fields:
    biglakeConfiguration: [Optional] Specifies the configuration of a BigLake
      managed table.
    cloneDefinition: [Output-only] Clone definition.
    clustering: [Beta] Clustering specification for the table. Must be
      specified with partitioning, data in the table will be first partitioned
      and subsequently clustered.
    creationTime: [Output-only] The time when this table was created, in
      milliseconds since the epoch.
    defaultCollation: [Output-only] The default collation of the table.
    defaultRoundingMode: [Output-only] The default rounding mode of the table.
    description: [Optional] A user-friendly description of this table.
    encryptionConfiguration: Custom encryption configuration (e.g., Cloud KMS
      keys).
    etag: [Output-only] A hash of the table metadata. Used to ensure there
      were no concurrent modifications to the resource when attempting an
      update. Not guaranteed to change when the table contents or the fields
      numRows, numBytes, numLongTermBytes or lastModifiedTime change.
    expirationTime: [Optional] The time when this table expires, in
      milliseconds since the epoch. If not present, the table will persist
      indefinitely. Expired tables will be deleted and their storage
      reclaimed. The defaultTableExpirationMs property of the encapsulating
      dataset can be used to set a default expirationTime on newly created
      tables.
    externalDataConfiguration: [Optional] Describes the data format, location,
      and other properties of a table stored outside of BigQuery. By defining
      these properties, the data source can then be queried as if it were a
      standard BigQuery table.
    friendlyName: [Optional] A descriptive name for this table.
    id: [Output-only] An opaque ID uniquely identifying the table.
    kind: [Output-only] The type of the resource.
    labels: The labels associated with this table. You can use these to
      organize and group your tables. Label keys and values can be no longer
      than 63 characters, can only contain lowercase letters, numeric
      characters, underscores and dashes. International characters are
      allowed. Label values are optional. Label keys must start with a letter
      and each label in the list must have a different key.
    lastModifiedTime: [Output-only] The time when this table was last
      modified, in milliseconds since the epoch.
    location: [Output-only] The geographic location where the table resides.
      This value is inherited from the dataset.
    materializedView: [Optional] Materialized view definition.
    maxStaleness: [Optional] Max staleness of data that could be returned when
      table or materialized view is queried (formatted as Google SQL Interval
      type).
    model: [Output-only, Beta] Present iff this table represents a ML model.
      Describes the training information for the model, and it is required to
      run 'PREDICT' queries.
    numActiveLogicalBytes: [Output-only] Number of logical bytes that are less
      than 90 days old.
    numActivePhysicalBytes: [Output-only] Number of physical bytes less than
      90 days old. This data is not kept in real time, and might be delayed by
      a few seconds to a few minutes.
    numBytes: [Output-only] The size of this table in bytes, excluding any
      data in the streaming buffer.
    numLongTermBytes: [Output-only] The number of bytes in the table that are
      considered "long-term storage".
    numLongTermLogicalBytes: [Output-only] Number of logical bytes that are
      more than 90 days old.
    numLongTermPhysicalBytes: [Output-only] Number of physical bytes more than
      90 days old. This data is not kept in real time, and might be delayed by
      a few seconds to a few minutes.
    numPartitions: [Output-only] The number of partitions present in the table
      or materialized view. This data is not kept in real time, and might be
      delayed by a few seconds to a few minutes.
    numPhysicalBytes: [Output-only] [TrustedTester] The physical size of this
      table in bytes, excluding any data in the streaming buffer. This
      includes compression and storage used for time travel.
    numRows: [Output-only] The number of rows of data in this table, excluding
      any data in the streaming buffer.
    numTimeTravelPhysicalBytes: [Output-only] Number of physical bytes used by
      time travel storage (deleted or changed data). This data is not kept in
      real time, and might be delayed by a few seconds to a few minutes.
    numTotalLogicalBytes: [Output-only] Total number of logical bytes in the
      table or materialized view.
    numTotalPhysicalBytes: [Output-only] The physical size of this table in
      bytes. This also includes storage used for time travel. This data is not
      kept in real time, and might be delayed by a few seconds to a few
      minutes.
    rangePartitioning: [TrustedTester] Range partitioning specification for
      this table. Only one of timePartitioning and rangePartitioning should be
      specified.
    requirePartitionFilter: [Optional] If set to true, queries over this table
      require a partition filter that can be used for partition elimination to
      be specified.
    schema: [Optional] Describes the schema of this table.
    selfLink: [Output-only] A URL that can be used to access this resource
      again.
    snapshotDefinition: [Output-only] Snapshot definition.
    streamingBuffer: [Output-only] Contains information regarding this table's
      streaming buffer, if one is present. This field will be absent if the
      table is not being streamed to or if there is no data in the streaming
      buffer.
    tableConstraints: [Optional] The table constraints on the table.
    tableReference: [Required] Reference describing the ID of this table.
    timePartitioning: Time-based partitioning specification for this table.
      Only one of timePartitioning and rangePartitioning should be specified.
    type: [Output-only] Describes the table type. The following values are
      supported: TABLE: A normal BigQuery table. VIEW: A virtual table defined
      by a SQL query. SNAPSHOT: An immutable, read-only table that is a copy
      of another table. [TrustedTester] MATERIALIZED_VIEW: SQL query whose
      result is persisted. EXTERNAL: A table that references data stored in an
      external storage system, such as Google Cloud Storage. The default value
      is TABLE.
    view: [Optional] The view definition.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""The labels associated with this table. You can use these to organize
    and group your tables. Label keys and values can be no longer than 63
    characters, can only contain lowercase letters, numeric characters,
    underscores and dashes. International characters are allowed. Label values
    are optional. Label keys must start with a letter and each label in the
    list must have a different key.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  biglakeConfiguration = _messages.MessageField('BigLakeConfiguration', 1)
  cloneDefinition = _messages.MessageField('CloneDefinition', 2)
  clustering = _messages.MessageField('Clustering', 3)
  creationTime = _messages.IntegerField(4)
  defaultCollation = _messages.StringField(5)
  defaultRoundingMode = _messages.StringField(6)
  description = _messages.StringField(7)
  encryptionConfiguration = _messages.MessageField('EncryptionConfiguration', 8)
  etag = _messages.StringField(9)
  expirationTime = _messages.IntegerField(10)
  externalDataConfiguration = _messages.MessageField('ExternalDataConfiguration', 11)
  friendlyName = _messages.StringField(12)
  id = _messages.StringField(13)
  kind = _messages.StringField(14, default='bigquery#table')
  labels = _messages.MessageField('LabelsValue', 15)
  lastModifiedTime = _messages.IntegerField(16, variant=_messages.Variant.UINT64)
  location = _messages.StringField(17)
  materializedView = _messages.MessageField('MaterializedViewDefinition', 18)
  maxStaleness = _messages.BytesField(19)
  model = _messages.MessageField('ModelDefinition', 20)
  numActiveLogicalBytes = _messages.IntegerField(21)
  numActivePhysicalBytes = _messages.IntegerField(22)
  numBytes = _messages.IntegerField(23)
  numLongTermBytes = _messages.IntegerField(24)
  numLongTermLogicalBytes = _messages.IntegerField(25)
  numLongTermPhysicalBytes = _messages.IntegerField(26)
  numPartitions = _messages.IntegerField(27)
  numPhysicalBytes = _messages.IntegerField(28)
  numRows = _messages.IntegerField(29, variant=_messages.Variant.UINT64)
  numTimeTravelPhysicalBytes = _messages.IntegerField(30)
  numTotalLogicalBytes = _messages.IntegerField(31)
  numTotalPhysicalBytes = _messages.IntegerField(32)
  rangePartitioning = _messages.MessageField('RangePartitioning', 33)
  requirePartitionFilter = _messages.BooleanField(34, default=False)
  schema = _messages.MessageField('TableSchema', 35)
  selfLink = _messages.StringField(36)
  snapshotDefinition = _messages.MessageField('SnapshotDefinition', 37)
  streamingBuffer = _messages.MessageField('Streamingbuffer', 38)
  tableConstraints = _messages.MessageField('TableConstraints', 39)
  tableReference = _messages.MessageField('TableReference', 40)
  timePartitioning = _messages.MessageField('TimePartitioning', 41)
  type = _messages.StringField(42)
  view = _messages.MessageField('ViewDefinition', 43)


class TableCell(_messages.Message):
  r"""A TableCell object.

  Fields:
    v: A extra_types.JsonValue attribute.
  """

  v = _messages.MessageField('extra_types.JsonValue', 1)


class TableConstraints(_messages.Message):
  r"""A TableConstraints object.

  Messages:
    ForeignKeysValueListEntry: A ForeignKeysValueListEntry object.
    PrimaryKeyValue: [Optional] The primary key of the table.

  Fields:
    foreignKeys: [Optional] The foreign keys of the tables.
    primaryKey: [Optional] The primary key of the table.
  """

  class ForeignKeysValueListEntry(_messages.Message):
    r"""A ForeignKeysValueListEntry object.

    Messages:
      ColumnReferencesValueListEntry: A ColumnReferencesValueListEntry object.
      ReferencedTableValue: A ReferencedTableValue object.

    Fields:
      columnReferences: A ColumnReferencesValueListEntry attribute.
      name: A string attribute.
      referencedTable: A ReferencedTableValue attribute.
    """

    class ColumnReferencesValueListEntry(_messages.Message):
      r"""A ColumnReferencesValueListEntry object.

      Fields:
        referencedColumn: A string attribute.
        referencingColumn: A string attribute.
      """

      referencedColumn = _messages.StringField(1)
      referencingColumn = _messages.StringField(2)

    class ReferencedTableValue(_messages.Message):
      r"""A ReferencedTableValue object.

      Fields:
        datasetId: A string attribute.
        projectId: A string attribute.
        tableId: A string attribute.
      """

      datasetId = _messages.StringField(1)
      projectId = _messages.StringField(2)
      tableId = _messages.StringField(3)

    columnReferences = _messages.MessageField('ColumnReferencesValueListEntry', 1, repeated=True)
    name = _messages.StringField(2)
    referencedTable = _messages.MessageField('ReferencedTableValue', 3)

  class PrimaryKeyValue(_messages.Message):
    r"""[Optional] The primary key of the table.

    Fields:
      columns: A string attribute.
    """

    columns = _messages.StringField(1, repeated=True)

  foreignKeys = _messages.MessageField('ForeignKeysValueListEntry', 1, repeated=True)
  primaryKey = _messages.MessageField('PrimaryKeyValue', 2)


class TableDataInsertAllRequest(_messages.Message):
  r"""A TableDataInsertAllRequest object.

  Messages:
    RowsValueListEntry: A RowsValueListEntry object.

  Fields:
    ignoreUnknownValues: [Optional] Accept rows that contain values that do
      not match the schema. The unknown values are ignored. Default is false,
      which treats unknown values as errors.
    kind: The resource type of the response.
    rows: The rows to insert.
    skipInvalidRows: [Optional] Insert all valid rows of a request, even if
      invalid rows exist. The default value is false, which causes the entire
      request to fail if any invalid rows exist.
    templateSuffix: If specified, treats the destination table as a base
      template, and inserts the rows into an instance table named
      "{destination}{templateSuffix}". BigQuery will manage creation of the
      instance table, using the schema of the base template table. See
      https://cloud.google.com/bigquery/streaming-data-into-bigquery#template-
      tables for considerations when working with templates tables.
  """

  class RowsValueListEntry(_messages.Message):
    r"""A RowsValueListEntry object.

    Fields:
      insertId: [Optional] A unique ID for each row. BigQuery uses this
        property to detect duplicate insertion requests on a best-effort
        basis.
      json: [Required] A JSON object that contains a row of data. The object's
        properties and values must match the destination table's schema.
    """

    insertId = _messages.StringField(1)
    json = _messages.MessageField('JsonObject', 2)

  ignoreUnknownValues = _messages.BooleanField(1)
  kind = _messages.StringField(2, default='bigquery#tableDataInsertAllRequest')
  rows = _messages.MessageField('RowsValueListEntry', 3, repeated=True)
  skipInvalidRows = _messages.BooleanField(4)
  templateSuffix = _messages.StringField(5)


class TableDataInsertAllResponse(_messages.Message):
  r"""A TableDataInsertAllResponse object.

  Messages:
    InsertErrorsValueListEntry: A InsertErrorsValueListEntry object.

  Fields:
    insertErrors: An array of errors for rows that were not inserted.
    kind: The resource type of the response.
  """

  class InsertErrorsValueListEntry(_messages.Message):
    r"""A InsertErrorsValueListEntry object.

    Fields:
      errors: Error information for the row indicated by the index property.
      index: The index of the row that error applies to.
    """

    errors = _messages.MessageField('ErrorProto', 1, repeated=True)
    index = _messages.IntegerField(2, variant=_messages.Variant.UINT32)

  insertErrors = _messages.MessageField('InsertErrorsValueListEntry', 1, repeated=True)
  kind = _messages.StringField(2, default='bigquery#tableDataInsertAllResponse')


class TableDataList(_messages.Message):
  r"""A TableDataList object.

  Fields:
    etag: A hash of this page of results.
    kind: The resource type of the response.
    pageToken: A token used for paging results. Providing this token instead
      of the startIndex parameter can help you retrieve stable results when an
      underlying table is changing.
    rows: Rows of results.
    totalRows: The total number of rows in the complete table.
  """

  etag = _messages.StringField(1)
  kind = _messages.StringField(2, default='bigquery#tableDataList')
  pageToken = _messages.StringField(3)
  rows = _messages.MessageField('TableRow', 4, repeated=True)
  totalRows = _messages.IntegerField(5)


class TableFieldSchema(_messages.Message):
  r"""A TableFieldSchema object.

  Messages:
    CategoriesValue: [Optional] The categories attached to this field, used
      for field-level access control.
    PolicyTagsValue: A PolicyTagsValue object.

  Fields:
    categories: [Optional] The categories attached to this field, used for
      field-level access control.
    collation: Optional. Collation specification of the field. It only can be
      set on string type field.
    defaultValueExpression: Optional. A SQL expression to specify the default
      value for this field. It can only be set for top level fields (columns).
      You can use struct or array expression to specify default value for the
      entire struct or array. The valid SQL expressions are: - Literals for
      all data types, including STRUCT and ARRAY. - Following functions: -
      CURRENT_TIMESTAMP - CURRENT_TIME - CURRENT_DATE - CURRENT_DATETIME -
      GENERATE_UUID - RAND - SESSION_USER - ST_GEOGPOINT - Struct or array
      composed with the above allowed functions, for example, [CURRENT_DATE(),
      DATE '2020-01-01']
    description: [Optional] The field description. The maximum length is 1,024
      characters.
    fields: [Optional] Describes the nested schema fields if the type property
      is set to RECORD.
    maxLength: [Optional] Maximum length of values of this field for STRINGS
      or BYTES. If max_length is not specified, no maximum length constraint
      is imposed on this field. If type = "STRING", then max_length represents
      the maximum UTF-8 length of strings in this field. If type = "BYTES",
      then max_length represents the maximum number of bytes in this field. It
      is invalid to set this field if type \u2260 "STRING" and \u2260 "BYTES".
    mode: [Optional] The field mode. Possible values include NULLABLE,
      REQUIRED and REPEATED. The default value is NULLABLE.
    name: [Required] The field name. The name must contain only letters (a-z,
      A-Z), numbers (0-9), or underscores (_), and must start with a letter or
      underscore. The maximum length is 300 characters.
    policyTags: A PolicyTagsValue attribute.
    precision: [Optional] Precision (maximum number of total digits in base
      10) and scale (maximum number of digits in the fractional part in base
      10) constraints for values of this field for NUMERIC or BIGNUMERIC. It
      is invalid to set precision or scale if type \u2260 "NUMERIC" and \u2260
      "BIGNUMERIC". If precision and scale are not specified, no value range
      constraint is imposed on this field insofar as values are permitted by
      the type. Values of this NUMERIC or BIGNUMERIC field must be in this
      range when: - Precision (P) and scale (S) are specified: [-10P-S + 10-S,
      10P-S - 10-S] - Precision (P) is specified but not scale (and thus scale
      is interpreted to be equal to zero): [-10P + 1, 10P - 1]. Acceptable
      values for precision and scale if both are specified: - If type =
      "NUMERIC": 1 \u2264 precision - scale \u2264 29 and 0 \u2264 scale
      \u2264 9. - If type = "BIGNUMERIC": 1 \u2264 precision - scale \u2264 38
      and 0 \u2264 scale \u2264 38. Acceptable values for precision if only
      precision is specified but not scale (and thus scale is interpreted to
      be equal to zero): - If type = "NUMERIC": 1 \u2264 precision \u2264 29.
      - If type = "BIGNUMERIC": 1 \u2264 precision \u2264 38. If scale is
      specified but not precision, then it is invalid.
    roundingMode: Optional. Rounding Mode specification of the field. It only
      can be set on NUMERIC or BIGNUMERIC type fields.
    scale: [Optional] See documentation for precision.
    type: [Required] The field data type. Possible values include STRING,
      BYTES, INTEGER, INT64 (same as INTEGER), FLOAT, FLOAT64 (same as FLOAT),
      NUMERIC, BIGNUMERIC, BOOLEAN, BOOL (same as BOOLEAN), TIMESTAMP, DATE,
      TIME, DATETIME, INTERVAL, RECORD (where RECORD indicates that the field
      contains a nested schema) or STRUCT (same as RECORD).
  """

  class CategoriesValue(_messages.Message):
    r"""[Optional] The categories attached to this field, used for field-level
    access control.

    Fields:
      names: A list of category resource names. For example,
        "projects/1/taxonomies/2/categories/3". At most 5 categories are
        allowed.
    """

    names = _messages.StringField(1, repeated=True)

  class PolicyTagsValue(_messages.Message):
    r"""A PolicyTagsValue object.

    Fields:
      names: A list of category resource names. For example,
        "projects/1/location/eu/taxonomies/2/policyTags/3". At most 1 policy
        tag is allowed.
    """

    names = _messages.StringField(1, repeated=True)

  categories = _messages.MessageField('CategoriesValue', 1)
  collation = _messages.StringField(2)
  defaultValueExpression = _messages.StringField(3)
  description = _messages.StringField(4)
  fields = _messages.MessageField('TableFieldSchema', 5, repeated=True)
  maxLength = _messages.IntegerField(6)
  mode = _messages.StringField(7)
  name = _messages.StringField(8)
  policyTags = _messages.MessageField('PolicyTagsValue', 9)
  precision = _messages.IntegerField(10)
  roundingMode = _messages.StringField(11)
  scale = _messages.IntegerField(12)
  type = _messages.StringField(13)


class TableList(_messages.Message):
  r"""A TableList object.

  Messages:
    TablesValueListEntry: A TablesValueListEntry object.

  Fields:
    etag: A hash of this page of results.
    kind: The type of list.
    nextPageToken: A token to request the next page of results.
    tables: Tables in the requested dataset.
    totalItems: The total number of tables in the dataset.
  """

  class TablesValueListEntry(_messages.Message):
    r"""A TablesValueListEntry object.

    Messages:
      LabelsValue: The labels associated with this table. You can use these to
        organize and group your tables.
      ViewValue: Additional details for a view.

    Fields:
      clustering: [Beta] Clustering specification for this table, if
        configured.
      creationTime: The time when this table was created, in milliseconds
        since the epoch.
      expirationTime: [Optional] The time when this table expires, in
        milliseconds since the epoch. If not present, the table will persist
        indefinitely. Expired tables will be deleted and their storage
        reclaimed.
      friendlyName: The user-friendly name for this table.
      id: An opaque ID of the table
      kind: The resource type.
      labels: The labels associated with this table. You can use these to
        organize and group your tables.
      rangePartitioning: The range partitioning specification for this table,
        if configured.
      tableReference: A reference uniquely identifying the table.
      timePartitioning: The time-based partitioning specification for this
        table, if configured.
      type: The type of table. Possible values are: TABLE, VIEW.
      view: Additional details for a view.
    """

    @encoding.MapUnrecognizedFields('additionalProperties')
    class LabelsValue(_messages.Message):
      r"""The labels associated with this table. You can use these to organize
      and group your tables.

      Messages:
        AdditionalProperty: An additional property for a LabelsValue object.

      Fields:
        additionalProperties: Additional properties of type LabelsValue
      """

      class AdditionalProperty(_messages.Message):
        r"""An additional property for a LabelsValue object.

        Fields:
          key: Name of the additional property.
          value: A string attribute.
        """

        key = _messages.StringField(1)
        value = _messages.StringField(2)

      additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

    class ViewValue(_messages.Message):
      r"""Additional details for a view.

      Fields:
        useLegacySql: True if view is defined in legacy SQL dialect, false if
          in standard SQL.
      """

      useLegacySql = _messages.BooleanField(1)

    clustering = _messages.MessageField('Clustering', 1)
    creationTime = _messages.IntegerField(2)
    expirationTime = _messages.IntegerField(3)
    friendlyName = _messages.StringField(4)
    id = _messages.StringField(5)
    kind = _messages.StringField(6, default='bigquery#table')
    labels = _messages.MessageField('LabelsValue', 7)
    rangePartitioning = _messages.MessageField('RangePartitioning', 8)
    tableReference = _messages.MessageField('TableReference', 9)
    timePartitioning = _messages.MessageField('TimePartitioning', 10)
    type = _messages.StringField(11)
    view = _messages.MessageField('ViewValue', 12)

  etag = _messages.StringField(1)
  kind = _messages.StringField(2, default='bigquery#tableList')
  nextPageToken = _messages.StringField(3)
  tables = _messages.MessageField('TablesValueListEntry', 4, repeated=True)
  totalItems = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class TableReference(_messages.Message):
  r"""A TableReference object.

  Fields:
    datasetId: [Required] The ID of the dataset containing this table.
    projectId: [Required] The ID of the project containing this table.
    tableId: [Required] The ID of the table. The ID must contain only letters
      (a-z, A-Z), numbers (0-9), or underscores (_). The maximum length is
      1,024 characters.
  """

  datasetId = _messages.StringField(1)
  projectId = _messages.StringField(2)
  tableId = _messages.StringField(3)


class TableRow(_messages.Message):
  r"""A TableRow object.

  Fields:
    f: Represents a single row in the result set, consisting of one or more
      fields.
  """

  f = _messages.MessageField('TableCell', 1, repeated=True)


class TableSchema(_messages.Message):
  r"""A TableSchema object.

  Fields:
    fields: Describes the fields in a table.
  """

  fields = _messages.MessageField('TableFieldSchema', 1, repeated=True)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TimePartitioning(_messages.Message):
  r"""A TimePartitioning object.

  Fields:
    expirationMs: [Optional] Number of milliseconds for which to keep the
      storage for partitions in the table. The storage in a partition will
      have an expiration time of its partition time plus this value.
    field: [Beta] [Optional] If not set, the table is partitioned by pseudo
      column, referenced via either '_PARTITIONTIME' as TIMESTAMP type, or
      '_PARTITIONDATE' as DATE type. If field is specified, the table is
      instead partitioned by this field. The field must be a top-level
      TIMESTAMP or DATE field. Its mode must be NULLABLE or REQUIRED.
    requirePartitionFilter: A boolean attribute.
    type: [Required] The supported types are DAY, HOUR, MONTH, and YEAR, which
      will generate one partition per day, hour, month, and year,
      respectively. When the type is not specified, the default behavior is
      DAY.
  """

  expirationMs = _messages.IntegerField(1)
  field = _messages.StringField(2)
  requirePartitionFilter = _messages.BooleanField(3)
  type = _messages.StringField(4)


class TrainingOptions(_messages.Message):
  r"""Options used in model training.

  Enums:
    BoosterTypeValueValuesEnum: Booster type for boosted tree models.
    CategoryEncodingMethodValueValuesEnum: Categorical feature encoding
      method.
    ColorSpaceValueValuesEnum: Enums for color space, used for processing
      images in Object Table. See more details at
      https://www.tensorflow.org/io/tutorials/colorspace.
    DartNormalizeTypeValueValuesEnum: Type of normalization algorithm for
      boosted tree models using dart booster.
    DataFrequencyValueValuesEnum: The data frequency of a time series.
    DataSplitMethodValueValuesEnum: The data split type for training and
      evaluation, e.g. RANDOM.
    DistanceTypeValueValuesEnum: Distance type for clustering models.
    FeedbackTypeValueValuesEnum: Feedback type that specifies which algorithm
      to run for matrix factorization.
    HolidayRegionValueValuesEnum: The geographical region based on which the
      holidays are considered in time series modeling. If a valid value is
      specified, then holiday effects modeling is enabled.
    HolidayRegionsValueListEntryValuesEnum:
    HparamTuningObjectivesValueListEntryValuesEnum:
    KmeansInitializationMethodValueValuesEnum: The method used to initialize
      the centroids for kmeans algorithm.
    LearnRateStrategyValueValuesEnum: The strategy to determine learn rate for
      the current iteration.
    LossTypeValueValuesEnum: Type of loss function used during training run.
    ModelRegistryValueValuesEnum: The model registry.
    OptimizationStrategyValueValuesEnum: Optimization strategy for training
      linear regression models.
    PcaSolverValueValuesEnum: The solver for PCA.
    TreeMethodValueValuesEnum: Tree construction algorithm for boosted tree
      models.

  Messages:
    LabelClassWeightsValue: Weights associated with each label class, for
      rebalancing the training data. Only applicable for classification
      models.

  Fields:
    activationFn: Activation function of the neural nets.
    adjustStepChanges: If true, detect step changes and make data adjustment
      in the input time series.
    approxGlobalFeatureContrib: Whether to use approximate feature
      contribution method in XGBoost model explanation for global explain.
    autoArima: Whether to enable auto ARIMA or not.
    autoArimaMaxOrder: The max value of the sum of non-seasonal p and q.
    autoArimaMinOrder: The min value of the sum of non-seasonal p and q.
    autoClassWeights: Whether to calculate class weights automatically based
      on the popularity of each label.
    batchSize: Batch size for dnn models.
    boosterType: Booster type for boosted tree models.
    budgetHours: Budget in hours for AutoML training.
    calculatePValues: Whether or not p-value test should be computed for this
      model. Only available for linear and logistic regression models.
    categoryEncodingMethod: Categorical feature encoding method.
    cleanSpikesAndDips: If true, clean spikes and dips in the input time
      series.
    colorSpace: Enums for color space, used for processing images in Object
      Table. See more details at
      https://www.tensorflow.org/io/tutorials/colorspace.
    colsampleBylevel: Subsample ratio of columns for each level for boosted
      tree models.
    colsampleBynode: Subsample ratio of columns for each node(split) for
      boosted tree models.
    colsampleBytree: Subsample ratio of columns when constructing each tree
      for boosted tree models.
    dartNormalizeType: Type of normalization algorithm for boosted tree models
      using dart booster.
    dataFrequency: The data frequency of a time series.
    dataSplitColumn: The column to split data with. This column won't be used
      as a feature. 1. When data_split_method is CUSTOM, the corresponding
      column should be boolean. The rows with true value tag are eval data,
      and the false are training data. 2. When data_split_method is SEQ, the
      first DATA_SPLIT_EVAL_FRACTION rows (from smallest to largest) in the
      corresponding column are used as training data, and the rest are eval
      data. It respects the order in Orderable data types:
      https://cloud.google.com/bigquery/docs/reference/standard-sql/data-
      types#data-type-properties
    dataSplitEvalFraction: The fraction of evaluation data over the whole
      input data. The rest of data will be used as training data. The format
      should be double. Accurate to two decimal places. Default value is 0.2.
    dataSplitMethod: The data split type for training and evaluation, e.g.
      RANDOM.
    decomposeTimeSeries: If true, perform decompose time series and save the
      results.
    distanceType: Distance type for clustering models.
    dropout: Dropout probability for dnn models.
    earlyStop: Whether to stop early when the loss doesn't improve
      significantly any more (compared to min_relative_progress). Used only
      for iterative training algorithms.
    enableGlobalExplain: If true, enable global explanation during training.
    feedbackType: Feedback type that specifies which algorithm to run for
      matrix factorization.
    fitIntercept: Whether the model should include intercept during model
      training.
    hiddenUnits: Hidden units for dnn models.
    holidayRegion: The geographical region based on which the holidays are
      considered in time series modeling. If a valid value is specified, then
      holiday effects modeling is enabled.
    holidayRegions: A list of geographical regions that are used for time
      series modeling.
    horizon: The number of periods ahead that need to be forecasted.
    hparamTuningObjectives: The target evaluation metrics to optimize the
      hyperparameters for.
    includeDrift: Include drift when fitting an ARIMA model.
    initialLearnRate: Specifies the initial learning rate for the line search
      learn rate strategy.
    inputLabelColumns: Name of input label columns in training data.
    instanceWeightColumn: Name of the instance weight column for training
      data. This column isn't be used as a feature.
    integratedGradientsNumSteps: Number of integral steps for the integrated
      gradients explain method.
    itemColumn: Item column specified for matrix factorization models.
    kmeansInitializationColumn: The column used to provide the initial
      centroids for kmeans algorithm when kmeans_initialization_method is
      CUSTOM.
    kmeansInitializationMethod: The method used to initialize the centroids
      for kmeans algorithm.
    l1RegActivation: L1 regularization coefficient to activations.
    l1Regularization: L1 regularization coefficient.
    l2Regularization: L2 regularization coefficient.
    labelClassWeights: Weights associated with each label class, for
      rebalancing the training data. Only applicable for classification
      models.
    learnRate: Learning rate in training. Used only for iterative training
      algorithms.
    learnRateStrategy: The strategy to determine learn rate for the current
      iteration.
    lossType: Type of loss function used during training run.
    maxIterations: The maximum number of iterations in training. Used only for
      iterative training algorithms.
    maxParallelTrials: Maximum number of trials to run in parallel.
    maxTimeSeriesLength: The maximum number of time points in a time series
      that can be used in modeling the trend component of the time series.
      Don't use this option with the `timeSeriesLengthFraction` or
      `minTimeSeriesLength` options.
    maxTreeDepth: Maximum depth of a tree for boosted tree models.
    minRelativeProgress: When early_stop is true, stops training when accuracy
      improvement is less than 'min_relative_progress'. Used only for
      iterative training algorithms.
    minSplitLoss: Minimum split loss for boosted tree models.
    minTimeSeriesLength: The minimum number of time points in a time series
      that are used in modeling the trend component of the time series. If you
      use this option you must also set the `timeSeriesLengthFraction` option.
      This training option ensures that enough time points are available when
      you use `timeSeriesLengthFraction` in trend modeling. This is
      particularly important when forecasting multiple time series in a single
      query using `timeSeriesIdColumn`. If the total number of time points is
      less than the `minTimeSeriesLength` value, then the query uses all
      available time points.
    minTreeChildWeight: Minimum sum of instance weight needed in a child for
      boosted tree models.
    modelRegistry: The model registry.
    modelUri: Google Cloud Storage URI from which the model was imported. Only
      applicable for imported models.
    nonSeasonalOrder: A specification of the non-seasonal part of the ARIMA
      model: the three components (p, d, q) are the AR order, the degree of
      differencing, and the MA order.
    numClusters: Number of clusters for clustering models.
    numFactors: Num factors specified for matrix factorization models.
    numParallelTree: Number of parallel trees constructed during each
      iteration for boosted tree models.
    numPrincipalComponents: Number of principal components to keep in the PCA
      model. Must be <= the number of features.
    numTrials: Number of trials to run this hyperparameter tuning job.
    optimizationStrategy: Optimization strategy for training linear regression
      models.
    optimizer: Optimizer used for training the neural nets.
    pcaExplainedVarianceRatio: The minimum ratio of cumulative explained
      variance that needs to be given by the PCA model.
    pcaSolver: The solver for PCA.
    sampledShapleyNumPaths: Number of paths for the sampled Shapley explain
      method.
    scaleFeatures: If true, scale the feature values by dividing the feature
      standard deviation. Currently only apply to PCA.
    standardizeFeatures: Whether to standardize numerical features. Default to
      true.
    subsample: Subsample fraction of the training data to grow tree to prevent
      overfitting for boosted tree models.
    tfVersion: Based on the selected TF version, the corresponding docker
      image is used to train external models.
    timeSeriesDataColumn: Column to be designated as time series data for
      ARIMA model.
    timeSeriesIdColumn: The time series id column that was used during ARIMA
      model training.
    timeSeriesIdColumns: The time series id columns that were used during
      ARIMA model training.
    timeSeriesLengthFraction: The fraction of the interpolated length of the
      time series that's used to model the time series trend component. All of
      the time points of the time series are used to model the non-trend
      component. This training option accelerates modeling training without
      sacrificing much forecasting accuracy. You can use this option with
      `minTimeSeriesLength` but not with `maxTimeSeriesLength`.
    timeSeriesTimestampColumn: Column to be designated as time series
      timestamp for ARIMA model.
    treeMethod: Tree construction algorithm for boosted tree models.
    trendSmoothingWindowSize: Smoothing window size for the trend component.
      When a positive value is specified, a center moving average smoothing is
      applied on the history trend. When the smoothing window is out of the
      boundary at the beginning or the end of the trend, the first element or
      the last element is padded to fill the smoothing window before the
      average is applied.
    userColumn: User column specified for matrix factorization models.
    vertexAiModelVersionAliases: The version aliases to apply in Vertex AI
      model registry. Always overwrite if the version aliases exists in a
      existing model.
    walsAlpha: Hyperparameter for matrix factoration when implicit feedback
      type is specified.
    warmStart: Whether to train a model from the last checkpoint.
    xgboostVersion: User-selected XGBoost versions for training of XGBoost
      models.
  """

  class BoosterTypeValueValuesEnum(_messages.Enum):
    r"""Booster type for boosted tree models.

    Values:
      BOOSTER_TYPE_UNSPECIFIED: Unspecified booster type.
      GBTREE: Gbtree booster.
      DART: Dart booster.
    """
    BOOSTER_TYPE_UNSPECIFIED = 0
    GBTREE = 1
    DART = 2

  class CategoryEncodingMethodValueValuesEnum(_messages.Enum):
    r"""Categorical feature encoding method.

    Values:
      ENCODING_METHOD_UNSPECIFIED: Unspecified encoding method.
      ONE_HOT_ENCODING: Applies one-hot encoding.
      LABEL_ENCODING: Applies label encoding.
      DUMMY_ENCODING: Applies dummy encoding.
    """
    ENCODING_METHOD_UNSPECIFIED = 0
    ONE_HOT_ENCODING = 1
    LABEL_ENCODING = 2
    DUMMY_ENCODING = 3

  class ColorSpaceValueValuesEnum(_messages.Enum):
    r"""Enums for color space, used for processing images in Object Table. See
    more details at https://www.tensorflow.org/io/tutorials/colorspace.

    Values:
      COLOR_SPACE_UNSPECIFIED: Unspecified color space
      RGB: RGB
      HSV: HSV
      YIQ: YIQ
      YUV: YUV
      GRAYSCALE: GRAYSCALE
    """
    COLOR_SPACE_UNSPECIFIED = 0
    RGB = 1
    HSV = 2
    YIQ = 3
    YUV = 4
    GRAYSCALE = 5

  class DartNormalizeTypeValueValuesEnum(_messages.Enum):
    r"""Type of normalization algorithm for boosted tree models using dart
    booster.

    Values:
      DART_NORMALIZE_TYPE_UNSPECIFIED: Unspecified dart normalize type.
      TREE: New trees have the same weight of each of dropped trees.
      FOREST: New trees have the same weight of sum of dropped trees.
    """
    DART_NORMALIZE_TYPE_UNSPECIFIED = 0
    TREE = 1
    FOREST = 2

  class DataFrequencyValueValuesEnum(_messages.Enum):
    r"""The data frequency of a time series.

    Values:
      DATA_FREQUENCY_UNSPECIFIED: <no description>
      AUTO_FREQUENCY: Automatically inferred from timestamps.
      YEARLY: Yearly data.
      QUARTERLY: Quarterly data.
      MONTHLY: Monthly data.
      WEEKLY: Weekly data.
      DAILY: Daily data.
      HOURLY: Hourly data.
      PER_MINUTE: Per-minute data.
    """
    DATA_FREQUENCY_UNSPECIFIED = 0
    AUTO_FREQUENCY = 1
    YEARLY = 2
    QUARTERLY = 3
    MONTHLY = 4
    WEEKLY = 5
    DAILY = 6
    HOURLY = 7
    PER_MINUTE = 8

  class DataSplitMethodValueValuesEnum(_messages.Enum):
    r"""The data split type for training and evaluation, e.g. RANDOM.

    Values:
      DATA_SPLIT_METHOD_UNSPECIFIED: <no description>
      RANDOM: Splits data randomly.
      CUSTOM: Splits data with the user provided tags.
      SEQUENTIAL: Splits data sequentially.
      NO_SPLIT: Data split will be skipped.
      AUTO_SPLIT: Splits data automatically: Uses NO_SPLIT if the data size is
        small. Otherwise uses RANDOM.
    """
    DATA_SPLIT_METHOD_UNSPECIFIED = 0
    RANDOM = 1
    CUSTOM = 2
    SEQUENTIAL = 3
    NO_SPLIT = 4
    AUTO_SPLIT = 5

  class DistanceTypeValueValuesEnum(_messages.Enum):
    r"""Distance type for clustering models.

    Values:
      DISTANCE_TYPE_UNSPECIFIED: <no description>
      EUCLIDEAN: Eculidean distance.
      COSINE: Cosine distance.
    """
    DISTANCE_TYPE_UNSPECIFIED = 0
    EUCLIDEAN = 1
    COSINE = 2

  class FeedbackTypeValueValuesEnum(_messages.Enum):
    r"""Feedback type that specifies which algorithm to run for matrix
    factorization.

    Values:
      FEEDBACK_TYPE_UNSPECIFIED: <no description>
      IMPLICIT: Use weighted-als for implicit feedback problems.
      EXPLICIT: Use nonweighted-als for explicit feedback problems.
    """
    FEEDBACK_TYPE_UNSPECIFIED = 0
    IMPLICIT = 1
    EXPLICIT = 2

  class HolidayRegionValueValuesEnum(_messages.Enum):
    r"""The geographical region based on which the holidays are considered in
    time series modeling. If a valid value is specified, then holiday effects
    modeling is enabled.

    Values:
      HOLIDAY_REGION_UNSPECIFIED: Holiday region unspecified.
      GLOBAL: Global.
      NA: North America.
      JAPAC: Japan and Asia Pacific: Korea, Greater China, India, Australia,
        and New Zealand.
      EMEA: Europe, the Middle East and Africa.
      LAC: Latin America and the Caribbean.
      AE: United Arab Emirates
      AR: Argentina
      AT: Austria
      AU: Australia
      BE: Belgium
      BR: Brazil
      CA: Canada
      CH: Switzerland
      CL: Chile
      CN: China
      CO: Colombia
      CS: Czechoslovakia
      CZ: Czech Republic
      DE: Germany
      DK: Denmark
      DZ: Algeria
      EC: Ecuador
      EE: Estonia
      EG: Egypt
      ES: Spain
      FI: Finland
      FR: France
      GB: Great Britain (United Kingdom)
      GR: Greece
      HK: Hong Kong
      HU: Hungary
      ID: Indonesia
      IE: Ireland
      IL: Israel
      IN: India
      IR: Iran
      IT: Italy
      JP: Japan
      KR: Korea (South)
      LV: Latvia
      MA: Morocco
      MX: Mexico
      MY: Malaysia
      NG: Nigeria
      NL: Netherlands
      NO: Norway
      NZ: New Zealand
      PE: Peru
      PH: Philippines
      PK: Pakistan
      PL: Poland
      PT: Portugal
      RO: Romania
      RS: Serbia
      RU: Russian Federation
      SA: Saudi Arabia
      SE: Sweden
      SG: Singapore
      SI: Slovenia
      SK: Slovakia
      TH: Thailand
      TR: Turkey
      TW: Taiwan
      UA: Ukraine
      US: United States
      VE: Venezuela
      VN: Viet Nam
      ZA: South Africa
    """
    HOLIDAY_REGION_UNSPECIFIED = 0
    GLOBAL = 1
    NA = 2
    JAPAC = 3
    EMEA = 4
    LAC = 5
    AE = 6
    AR = 7
    AT = 8
    AU = 9
    BE = 10
    BR = 11
    CA = 12
    CH = 13
    CL = 14
    CN = 15
    CO = 16
    CS = 17
    CZ = 18
    DE = 19
    DK = 20
    DZ = 21
    EC = 22
    EE = 23
    EG = 24
    ES = 25
    FI = 26
    FR = 27
    GB = 28
    GR = 29
    HK = 30
    HU = 31
    ID = 32
    IE = 33
    IL = 34
    IN = 35
    IR = 36
    IT = 37
    JP = 38
    KR = 39
    LV = 40
    MA = 41
    MX = 42
    MY = 43
    NG = 44
    NL = 45
    NO = 46
    NZ = 47
    PE = 48
    PH = 49
    PK = 50
    PL = 51
    PT = 52
    RO = 53
    RS = 54
    RU = 55
    SA = 56
    SE = 57
    SG = 58
    SI = 59
    SK = 60
    TH = 61
    TR = 62
    TW = 63
    UA = 64
    US = 65
    VE = 66
    VN = 67
    ZA = 68

  class HolidayRegionsValueListEntryValuesEnum(_messages.Enum):
    r"""HolidayRegionsValueListEntryValuesEnum enum type.

    Values:
      HOLIDAY_REGION_UNSPECIFIED: Holiday region unspecified.
      GLOBAL: Global.
      NA: North America.
      JAPAC: Japan and Asia Pacific: Korea, Greater China, India, Australia,
        and New Zealand.
      EMEA: Europe, the Middle East and Africa.
      LAC: Latin America and the Caribbean.
      AE: United Arab Emirates
      AR: Argentina
      AT: Austria
      AU: Australia
      BE: Belgium
      BR: Brazil
      CA: Canada
      CH: Switzerland
      CL: Chile
      CN: China
      CO: Colombia
      CS: Czechoslovakia
      CZ: Czech Republic
      DE: Germany
      DK: Denmark
      DZ: Algeria
      EC: Ecuador
      EE: Estonia
      EG: Egypt
      ES: Spain
      FI: Finland
      FR: France
      GB: Great Britain (United Kingdom)
      GR: Greece
      HK: Hong Kong
      HU: Hungary
      ID: Indonesia
      IE: Ireland
      IL: Israel
      IN: India
      IR: Iran
      IT: Italy
      JP: Japan
      KR: Korea (South)
      LV: Latvia
      MA: Morocco
      MX: Mexico
      MY: Malaysia
      NG: Nigeria
      NL: Netherlands
      NO: Norway
      NZ: New Zealand
      PE: Peru
      PH: Philippines
      PK: Pakistan
      PL: Poland
      PT: Portugal
      RO: Romania
      RS: Serbia
      RU: Russian Federation
      SA: Saudi Arabia
      SE: Sweden
      SG: Singapore
      SI: Slovenia
      SK: Slovakia
      TH: Thailand
      TR: Turkey
      TW: Taiwan
      UA: Ukraine
      US: United States
      VE: Venezuela
      VN: Viet Nam
      ZA: South Africa
    """
    HOLIDAY_REGION_UNSPECIFIED = 0
    GLOBAL = 1
    NA = 2
    JAPAC = 3
    EMEA = 4
    LAC = 5
    AE = 6
    AR = 7
    AT = 8
    AU = 9
    BE = 10
    BR = 11
    CA = 12
    CH = 13
    CL = 14
    CN = 15
    CO = 16
    CS = 17
    CZ = 18
    DE = 19
    DK = 20
    DZ = 21
    EC = 22
    EE = 23
    EG = 24
    ES = 25
    FI = 26
    FR = 27
    GB = 28
    GR = 29
    HK = 30
    HU = 31
    ID = 32
    IE = 33
    IL = 34
    IN = 35
    IR = 36
    IT = 37
    JP = 38
    KR = 39
    LV = 40
    MA = 41
    MX = 42
    MY = 43
    NG = 44
    NL = 45
    NO = 46
    NZ = 47
    PE = 48
    PH = 49
    PK = 50
    PL = 51
    PT = 52
    RO = 53
    RS = 54
    RU = 55
    SA = 56
    SE = 57
    SG = 58
    SI = 59
    SK = 60
    TH = 61
    TR = 62
    TW = 63
    UA = 64
    US = 65
    VE = 66
    VN = 67
    ZA = 68

  class HparamTuningObjectivesValueListEntryValuesEnum(_messages.Enum):
    r"""HparamTuningObjectivesValueListEntryValuesEnum enum type.

    Values:
      HPARAM_TUNING_OBJECTIVE_UNSPECIFIED: Unspecified evaluation metric.
      MEAN_ABSOLUTE_ERROR: Mean absolute error. mean_absolute_error =
        AVG(ABS(label - predicted))
      MEAN_SQUARED_ERROR: Mean squared error. mean_squared_error =
        AVG(POW(label - predicted, 2))
      MEAN_SQUARED_LOG_ERROR: Mean squared log error. mean_squared_log_error =
        AVG(POW(LN(1 + label) - LN(1 + predicted), 2))
      MEDIAN_ABSOLUTE_ERROR: Mean absolute error. median_absolute_error =
        APPROX_QUANTILES(absolute_error, 2)[OFFSET(1)]
      R_SQUARED: R^2 score. This corresponds to r2_score in ML.EVALUATE.
        r_squared = 1 - SUM(squared_error)/(COUNT(label)*VAR_POP(label))
      EXPLAINED_VARIANCE: Explained variance. explained_variance = 1 -
        VAR_POP(label_error)/VAR_POP(label)
      PRECISION: Precision is the fraction of actual positive predictions that
        had positive actual labels. For multiclass this is a macro-averaged
        metric treating each class as a binary classifier.
      RECALL: Recall is the fraction of actual positive labels that were given
        a positive prediction. For multiclass this is a macro-averaged metric.
      ACCURACY: Accuracy is the fraction of predictions given the correct
        label. For multiclass this is a globally micro-averaged metric.
      F1_SCORE: The F1 score is an average of recall and precision. For
        multiclass this is a macro-averaged metric.
      LOG_LOSS: Logorithmic Loss. For multiclass this is a macro-averaged
        metric.
      ROC_AUC: Area Under an ROC Curve. For multiclass this is a macro-
        averaged metric.
      DAVIES_BOULDIN_INDEX: Davies-Bouldin Index.
      MEAN_AVERAGE_PRECISION: Mean Average Precision.
      NORMALIZED_DISCOUNTED_CUMULATIVE_GAIN: Normalized Discounted Cumulative
        Gain.
      AVERAGE_RANK: Average Rank.
    """
    HPARAM_TUNING_OBJECTIVE_UNSPECIFIED = 0
    MEAN_ABSOLUTE_ERROR = 1
    MEAN_SQUARED_ERROR = 2
    MEAN_SQUARED_LOG_ERROR = 3
    MEDIAN_ABSOLUTE_ERROR = 4
    R_SQUARED = 5
    EXPLAINED_VARIANCE = 6
    PRECISION = 7
    RECALL = 8
    ACCURACY = 9
    F1_SCORE = 10
    LOG_LOSS = 11
    ROC_AUC = 12
    DAVIES_BOULDIN_INDEX = 13
    MEAN_AVERAGE_PRECISION = 14
    NORMALIZED_DISCOUNTED_CUMULATIVE_GAIN = 15
    AVERAGE_RANK = 16

  class KmeansInitializationMethodValueValuesEnum(_messages.Enum):
    r"""The method used to initialize the centroids for kmeans algorithm.

    Values:
      KMEANS_INITIALIZATION_METHOD_UNSPECIFIED: Unspecified initialization
        method.
      RANDOM: Initializes the centroids randomly.
      CUSTOM: Initializes the centroids using data specified in
        kmeans_initialization_column.
      KMEANS_PLUS_PLUS: Initializes with kmeans++.
    """
    KMEANS_INITIALIZATION_METHOD_UNSPECIFIED = 0
    RANDOM = 1
    CUSTOM = 2
    KMEANS_PLUS_PLUS = 3

  class LearnRateStrategyValueValuesEnum(_messages.Enum):
    r"""The strategy to determine learn rate for the current iteration.

    Values:
      LEARN_RATE_STRATEGY_UNSPECIFIED: <no description>
      LINE_SEARCH: Use line search to determine learning rate.
      CONSTANT: Use a constant learning rate.
    """
    LEARN_RATE_STRATEGY_UNSPECIFIED = 0
    LINE_SEARCH = 1
    CONSTANT = 2

  class LossTypeValueValuesEnum(_messages.Enum):
    r"""Type of loss function used during training run.

    Values:
      LOSS_TYPE_UNSPECIFIED: <no description>
      MEAN_SQUARED_LOSS: Mean squared loss, used for linear regression.
      MEAN_LOG_LOSS: Mean log loss, used for logistic regression.
    """
    LOSS_TYPE_UNSPECIFIED = 0
    MEAN_SQUARED_LOSS = 1
    MEAN_LOG_LOSS = 2

  class ModelRegistryValueValuesEnum(_messages.Enum):
    r"""The model registry.

    Values:
      MODEL_REGISTRY_UNSPECIFIED: <no description>
      VERTEX_AI: Vertex AI.
    """
    MODEL_REGISTRY_UNSPECIFIED = 0
    VERTEX_AI = 1

  class OptimizationStrategyValueValuesEnum(_messages.Enum):
    r"""Optimization strategy for training linear regression models.

    Values:
      OPTIMIZATION_STRATEGY_UNSPECIFIED: <no description>
      BATCH_GRADIENT_DESCENT: Uses an iterative batch gradient descent
        algorithm.
      NORMAL_EQUATION: Uses a normal equation to solve linear regression
        problem.
    """
    OPTIMIZATION_STRATEGY_UNSPECIFIED = 0
    BATCH_GRADIENT_DESCENT = 1
    NORMAL_EQUATION = 2

  class PcaSolverValueValuesEnum(_messages.Enum):
    r"""The solver for PCA.

    Values:
      UNSPECIFIED: <no description>
      FULL: Full eigen-decoposition.
      RANDOMIZED: Randomized SVD.
      AUTO: Auto.
    """
    UNSPECIFIED = 0
    FULL = 1
    RANDOMIZED = 2
    AUTO = 3

  class TreeMethodValueValuesEnum(_messages.Enum):
    r"""Tree construction algorithm for boosted tree models.

    Values:
      TREE_METHOD_UNSPECIFIED: Unspecified tree method.
      AUTO: Use heuristic to choose the fastest method.
      EXACT: Exact greedy algorithm.
      APPROX: Approximate greedy algorithm using quantile sketch and gradient
        histogram.
      HIST: Fast histogram optimized approximate greedy algorithm.
    """
    TREE_METHOD_UNSPECIFIED = 0
    AUTO = 1
    EXACT = 2
    APPROX = 3
    HIST = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelClassWeightsValue(_messages.Message):
    r"""Weights associated with each label class, for rebalancing the training
    data. Only applicable for classification models.

    Messages:
      AdditionalProperty: An additional property for a LabelClassWeightsValue
        object.

    Fields:
      additionalProperties: Additional properties of type
        LabelClassWeightsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelClassWeightsValue object.

      Fields:
        key: Name of the additional property.
        value: A number attribute.
      """

      key = _messages.StringField(1)
      value = _messages.FloatField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  activationFn = _messages.StringField(1)
  adjustStepChanges = _messages.BooleanField(2)
  approxGlobalFeatureContrib = _messages.BooleanField(3)
  autoArima = _messages.BooleanField(4)
  autoArimaMaxOrder = _messages.IntegerField(5)
  autoArimaMinOrder = _messages.IntegerField(6)
  autoClassWeights = _messages.BooleanField(7)
  batchSize = _messages.IntegerField(8)
  boosterType = _messages.EnumField('BoosterTypeValueValuesEnum', 9)
  budgetHours = _messages.FloatField(10)
  calculatePValues = _messages.BooleanField(11)
  categoryEncodingMethod = _messages.EnumField('CategoryEncodingMethodValueValuesEnum', 12)
  cleanSpikesAndDips = _messages.BooleanField(13)
  colorSpace = _messages.EnumField('ColorSpaceValueValuesEnum', 14)
  colsampleBylevel = _messages.FloatField(15)
  colsampleBynode = _messages.FloatField(16)
  colsampleBytree = _messages.FloatField(17)
  dartNormalizeType = _messages.EnumField('DartNormalizeTypeValueValuesEnum', 18)
  dataFrequency = _messages.EnumField('DataFrequencyValueValuesEnum', 19)
  dataSplitColumn = _messages.StringField(20)
  dataSplitEvalFraction = _messages.FloatField(21)
  dataSplitMethod = _messages.EnumField('DataSplitMethodValueValuesEnum', 22)
  decomposeTimeSeries = _messages.BooleanField(23)
  distanceType = _messages.EnumField('DistanceTypeValueValuesEnum', 24)
  dropout = _messages.FloatField(25)
  earlyStop = _messages.BooleanField(26)
  enableGlobalExplain = _messages.BooleanField(27)
  feedbackType = _messages.EnumField('FeedbackTypeValueValuesEnum', 28)
  fitIntercept = _messages.BooleanField(29)
  hiddenUnits = _messages.IntegerField(30, repeated=True)
  holidayRegion = _messages.EnumField('HolidayRegionValueValuesEnum', 31)
  holidayRegions = _messages.EnumField('HolidayRegionsValueListEntryValuesEnum', 32, repeated=True)
  horizon = _messages.IntegerField(33)
  hparamTuningObjectives = _messages.EnumField('HparamTuningObjectivesValueListEntryValuesEnum', 34, repeated=True)
  includeDrift = _messages.BooleanField(35)
  initialLearnRate = _messages.FloatField(36)
  inputLabelColumns = _messages.StringField(37, repeated=True)
  instanceWeightColumn = _messages.StringField(38)
  integratedGradientsNumSteps = _messages.IntegerField(39)
  itemColumn = _messages.StringField(40)
  kmeansInitializationColumn = _messages.StringField(41)
  kmeansInitializationMethod = _messages.EnumField('KmeansInitializationMethodValueValuesEnum', 42)
  l1RegActivation = _messages.FloatField(43)
  l1Regularization = _messages.FloatField(44)
  l2Regularization = _messages.FloatField(45)
  labelClassWeights = _messages.MessageField('LabelClassWeightsValue', 46)
  learnRate = _messages.FloatField(47)
  learnRateStrategy = _messages.EnumField('LearnRateStrategyValueValuesEnum', 48)
  lossType = _messages.EnumField('LossTypeValueValuesEnum', 49)
  maxIterations = _messages.IntegerField(50)
  maxParallelTrials = _messages.IntegerField(51)
  maxTimeSeriesLength = _messages.IntegerField(52)
  maxTreeDepth = _messages.IntegerField(53)
  minRelativeProgress = _messages.FloatField(54)
  minSplitLoss = _messages.FloatField(55)
  minTimeSeriesLength = _messages.IntegerField(56)
  minTreeChildWeight = _messages.IntegerField(57)
  modelRegistry = _messages.EnumField('ModelRegistryValueValuesEnum', 58)
  modelUri = _messages.StringField(59)
  nonSeasonalOrder = _messages.MessageField('ArimaOrder', 60)
  numClusters = _messages.IntegerField(61)
  numFactors = _messages.IntegerField(62)
  numParallelTree = _messages.IntegerField(63)
  numPrincipalComponents = _messages.IntegerField(64)
  numTrials = _messages.IntegerField(65)
  optimizationStrategy = _messages.EnumField('OptimizationStrategyValueValuesEnum', 66)
  optimizer = _messages.StringField(67)
  pcaExplainedVarianceRatio = _messages.FloatField(68)
  pcaSolver = _messages.EnumField('PcaSolverValueValuesEnum', 69)
  sampledShapleyNumPaths = _messages.IntegerField(70)
  scaleFeatures = _messages.BooleanField(71)
  standardizeFeatures = _messages.BooleanField(72)
  subsample = _messages.FloatField(73)
  tfVersion = _messages.StringField(74)
  timeSeriesDataColumn = _messages.StringField(75)
  timeSeriesIdColumn = _messages.StringField(76)
  timeSeriesIdColumns = _messages.StringField(77, repeated=True)
  timeSeriesLengthFraction = _messages.FloatField(78)
  timeSeriesTimestampColumn = _messages.StringField(79)
  treeMethod = _messages.EnumField('TreeMethodValueValuesEnum', 80)
  trendSmoothingWindowSize = _messages.IntegerField(81)
  userColumn = _messages.StringField(82)
  vertexAiModelVersionAliases = _messages.StringField(83, repeated=True)
  walsAlpha = _messages.FloatField(84)
  warmStart = _messages.BooleanField(85)
  xgboostVersion = _messages.StringField(86)


class TrainingRun(_messages.Message):
  r"""Information about a single training query run for the model.

  Fields:
    classLevelGlobalExplanations: Output only. Global explanation contains the
      explanation of top features on the class level. Applies to
      classification models only.
    dataSplitResult: Output only. Data split result of the training run. Only
      set when the input data is actually split.
    evaluationMetrics: Output only. The evaluation metrics over training/eval
      data that were computed at the end of training.
    modelLevelGlobalExplanation: Output only. Global explanation contains the
      explanation of top features on the model level. Applies to both
      regression and classification models.
    results: Output only. Output of each iteration run, results.size() <=
      max_iterations.
    startTime: Output only. The start time of this training run.
    trainingOptions: Output only. Options that were used for this training
      run, includes user specified and default options that were used.
    trainingStartTime: Output only. The start time of this training run, in
      milliseconds since epoch.
    vertexAiModelId: The model id in the [Vertex AI Model
      Registry](https://cloud.google.com/vertex-ai/docs/model-
      registry/introduction) for this training run.
    vertexAiModelVersion: Output only. The model version in the [Vertex AI
      Model Registry](https://cloud.google.com/vertex-ai/docs/model-
      registry/introduction) for this training run.
  """

  classLevelGlobalExplanations = _messages.MessageField('GlobalExplanation', 1, repeated=True)
  dataSplitResult = _messages.MessageField('DataSplitResult', 2)
  evaluationMetrics = _messages.MessageField('EvaluationMetrics', 3)
  modelLevelGlobalExplanation = _messages.MessageField('GlobalExplanation', 4)
  results = _messages.MessageField('IterationResult', 5, repeated=True)
  startTime = _messages.StringField(6)
  trainingOptions = _messages.MessageField('TrainingOptions', 7)
  trainingStartTime = _messages.IntegerField(8)
  vertexAiModelId = _messages.StringField(9)
  vertexAiModelVersion = _messages.StringField(10)


class TransactionInfo(_messages.Message):
  r"""A TransactionInfo object.

  Fields:
    transactionId: [Output-only] // [Alpha] Id of the transaction.
  """

  transactionId = _messages.StringField(1)


class TransformColumn(_messages.Message):
  r"""Information about a single transform column.

  Fields:
    name: Output only. Name of the column.
    transformSql: Output only. The SQL expression used in the column
      transform.
    type: Output only. Data type of the column after the transform.
  """

  name = _messages.StringField(1)
  transformSql = _messages.StringField(2)
  type = _messages.MessageField('StandardSqlDataType', 3)


class UserDefinedFunctionResource(_messages.Message):
  r"""This is used for defining User Defined Function (UDF) resources only
  when using legacy SQL. Users of Standard SQL should leverage either DDL
  (e.g. CREATE [TEMPORARY] FUNCTION ... ) or the Routines API to define UDF
  resources. For additional information on migrating, see:
  https://cloud.google.com/bigquery/docs/reference/standard-sql/migrating-
  from-legacy-sql#differences_in_user-defined_javascript_functions

  Fields:
    inlineCode: [Pick one] An inline resource that contains code for a user-
      defined function (UDF). Providing a inline code resource is equivalent
      to providing a URI for a file containing the same code.
    resourceUri: [Pick one] A code resource to load from a Google Cloud
      Storage URI (gs://bucket/path).
  """

  inlineCode = _messages.StringField(1)
  resourceUri = _messages.StringField(2)


class ViewDefinition(_messages.Message):
  r"""A ViewDefinition object.

  Fields:
    query: [Required] A query that BigQuery executes when the view is
      referenced.
    useExplicitColumnNames: True if the column names are explicitly specified.
      For example by using the 'CREATE VIEW v(c1, c2) AS ...' syntax. Can only
      be set using BigQuery's standard SQL:
      https://cloud.google.com/bigquery/sql-reference/
    useLegacySql: Specifies whether to use BigQuery's legacy SQL for this
      view. The default value is true. If set to false, the view will use
      BigQuery's standard SQL: https://cloud.google.com/bigquery/sql-
      reference/ Queries and views that reference this view must use the same
      flag value.
    userDefinedFunctionResources: Describes user-defined function resources
      used in the query.
  """

  query = _messages.StringField(1)
  useExplicitColumnNames = _messages.BooleanField(2)
  useLegacySql = _messages.BooleanField(3)
  userDefinedFunctionResources = _messages.MessageField('UserDefinedFunctionResource', 4, repeated=True)



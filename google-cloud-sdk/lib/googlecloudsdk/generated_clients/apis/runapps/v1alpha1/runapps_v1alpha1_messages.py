"""Generated message classes for runapps version v1alpha1.

API for managing Cloud Run Integrations.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'runapps'


class Application(_messages.Message):
  r"""Message describing Application object Next tag: 9

  Messages:
    AnnotationsValue: Unstructured key value map that may be set by external
      tools to store and arbitrary metadata. They are not queryable and should
      be preserved when modifying objects. This field follows Kubernetes
      annotations' namespacing, limits, and rules. More info:
      http://kubernetes.io/docs/user-guide/annotations
    LabelsValue: Labels as key value pairs

  Fields:
    annotations: Unstructured key value map that may be set by external tools
      to store and arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. This field follows Kubernetes
      annotations' namespacing, limits, and rules. More info:
      http://kubernetes.io/docs/user-guide/annotations
    config: The application configuration. On output, both intent repo and
      application config are populated. On input, only one can be modified at
      a time.
    createTime: Output only. Create time stamp
    deleteTime: Output only. For a deleted resource, the deletion time. It is
      only populated as a response to a Delete request.
    displayName: A mutable, user-defined name for the application.
    etag: Output only. A system-generated fingerprint for this version of the
      resource. May be used to detect modification conflict during updates.
    labels: Labels as key value pairs
    name: name of resource
    reconciling: Output only. Indicates whether the resource's reconciliation
      is still in progress.
    updateTime: Output only. Update time stamp
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Unstructured key value map that may be set by external tools to store
    and arbitrary metadata. They are not queryable and should be preserved
    when modifying objects. This field follows Kubernetes annotations'
    namespacing, limits, and rules. More info: http://kubernetes.io/docs/user-
    guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  config = _messages.MessageField('Config', 2)
  createTime = _messages.StringField(3)
  deleteTime = _messages.StringField(4)
  displayName = _messages.StringField(5)
  etag = _messages.StringField(6)
  labels = _messages.MessageField('LabelsValue', 7)
  name = _messages.StringField(8)
  reconciling = _messages.BooleanField(9)
  updateTime = _messages.StringField(10)


class ApplicationStatus(_messages.Message):
  r"""Status of the application.

  Messages:
    AnnotationsValue: Unstructured key value map that may be set by external
      tools to store and arbitrary metadata. They are not queryable and should
      be preserved when modifying objects. This field follows Kubernetes
      annotations' namespacing, limits, and rules. More info:
      http://kubernetes.io/docs/user-guide/annotations
    ResourcesValue: The map of resource status where the key is the name of
      resources and the value is the resource status. Deprecated: use
      resource_statuses instead.

  Fields:
    annotations: Unstructured key value map that may be set by external tools
      to store and arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. This field follows Kubernetes
      annotations' namespacing, limits, and rules. More info:
      http://kubernetes.io/docs/user-guide/annotations
    createTime: Output only. Create time stamp
    displayName: Display name
    etag: Output only. A system-generated fingerprint for this version of the
      resource. May be used to detect modification conflict during updates.
    name: The resource name of the application status, in the following form:
      `projects/{project}/locations/{location}/applications/{application}/stat
      us`
    reconciling: Output only. Indicates whether the resource's reconciliation
      is still in progress.
    resourceStatuses: Output only. The status of the resources in this
      application.
    resources: The map of resource status where the key is the name of
      resources and the value is the resource status. Deprecated: use
      resource_statuses instead.
    updateTime: Output only. Time at which the status was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Unstructured key value map that may be set by external tools to store
    and arbitrary metadata. They are not queryable and should be preserved
    when modifying objects. This field follows Kubernetes annotations'
    namespacing, limits, and rules. More info: http://kubernetes.io/docs/user-
    guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourcesValue(_messages.Message):
    r"""The map of resource status where the key is the name of resources and
    the value is the resource status. Deprecated: use resource_statuses
    instead.

    Messages:
      AdditionalProperty: An additional property for a ResourcesValue object.

    Fields:
      additionalProperties: Additional properties of type ResourcesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourcesValue object.

      Fields:
        key: Name of the additional property.
        value: A ResourceStatus attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ResourceStatus', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  createTime = _messages.StringField(2)
  displayName = _messages.StringField(3)
  etag = _messages.StringField(4)
  name = _messages.StringField(5)
  reconciling = _messages.BooleanField(6)
  resourceStatuses = _messages.MessageField('ResourceStatus', 7, repeated=True)
  resources = _messages.MessageField('ResourcesValue', 8)
  updateTime = _messages.StringField(9)


class Binding(_messages.Message):
  r"""Binding describes the edge between the Resource it's defined in and the
  specified Resource.

  Messages:
    ConfigValue: Configuration allows the caller to provide configurations
      that are either specific to the binding or clarify *how* the binding
      works. The configuration has an associated typekit-specified JSONSchema
      (https://json-schema.org/) that defines the expected shape.

  Fields:
    config: Configuration allows the caller to provide configurations that are
      either specific to the binding or clarify *how* the binding works. The
      configuration has an associated typekit-specified JSONSchema
      (https://json-schema.org/) that defines the expected shape.
    targetRef: TargetRef describes the target resource.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConfigValue(_messages.Message):
    r"""Configuration allows the caller to provide configurations that are
    either specific to the binding or clarify *how* the binding works. The
    configuration has an associated typekit-specified JSONSchema
    (https://json-schema.org/) that defines the expected shape.

    Messages:
      AdditionalProperty: An additional property for a ConfigValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConfigValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  config = _messages.MessageField('ConfigValue', 1)
  targetRef = _messages.MessageField('ResourceRef', 2)


class BindingStatus(_messages.Message):
  r"""The binding status of a resource

  Messages:
    AnnotationsValue: Annotations of the Cloud Run service for the binded
      resource.
    EnvironmentVariablesValue: Environment variables of the Cloud Run service
      for the binded resource.

  Fields:
    annotations: Annotations of the Cloud Run service for the binded resource.
    environmentVariables: Environment variables of the Cloud Run service for
      the binded resource.
    resourceName: Name of the binded resource.
    resourceType: Type of the binded resource.
    serviceAccount: Service account email used by the Cloud Run service for
      the binded resource.
    serviceName: Name of the Cloud Run service.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Annotations of the Cloud Run service for the binded resource.

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvironmentVariablesValue(_messages.Message):
    r"""Environment variables of the Cloud Run service for the binded
    resource.

    Messages:
      AdditionalProperty: An additional property for a
        EnvironmentVariablesValue object.

    Fields:
      additionalProperties: Additional properties of type
        EnvironmentVariablesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvironmentVariablesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  environmentVariables = _messages.MessageField('EnvironmentVariablesValue', 2)
  resourceName = _messages.StringField(3)
  resourceType = _messages.StringField(4)
  serviceAccount = _messages.StringField(5)
  serviceName = _messages.StringField(6)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class CloudRunJobConfig(_messages.Message):
  r"""Message for Cloud Run job configs.

  Fields:
    bindings: Bindings to other resources.
    config: Configuration for the job.
  """

  bindings = _messages.MessageField('ServiceResourceBindingConfig', 1, repeated=True)
  config = _messages.MessageField('JobSettingsConfig', 2)


class CloudRunServiceConfig(_messages.Message):
  r"""Message for Cloud Run service configs.

  Fields:
    resources: Bindings to other resources.
  """

  resources = _messages.MessageField('ServiceResourceBindingConfig', 1, repeated=True)


class CloudSqlConfig(_messages.Message):
  r"""Message for a Cloud SQL resource.

  Fields:
    settings: Settings for the Cloud SQL instance.
    version: The database version. e.g. "MYSQL_8_0". The version must match
      one of the values at https://cloud.google.com/sql/docs/mysql/admin-
      api/rest/v1beta4/SqlDatabaseVersion.
  """

  settings = _messages.MessageField('CloudSqlSettings', 1)
  version = _messages.StringField(2)


class CloudSqlSettings(_messages.Message):
  r"""Message for settings for a CloudSql instance.

  Fields:
    activation_policy: The activation policy of the Cloud SQL instance. e.g.
      "ALWAYS".
    availability_type: The availability type of the Cloud SQL instance. e.g.
      "REGIONAL".
    disk_size: The disk size of the Cloud SQL instance, in GB. This value
      cannot be decreased on Update.
    disk_type: The type of disk for the Cloud SQL instance. e.g. "PD_SSD".
    tier: Tier of the Cloud SQL instance. e.g. "db-f1-micro".
  """

  activation_policy = _messages.StringField(1)
  availability_type = _messages.StringField(2)
  disk_size = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  disk_type = _messages.StringField(4)
  tier = _messages.StringField(5)


class Config(_messages.Message):
  r"""Message for the Application Config Next tag: 6

  Messages:
    ResourcesValue: The map of resource configs where the key is the name of
      resources and the value is the resource config.

  Fields:
    config: A byte array encapsulating the contents of the application config.
      This can be of any type of supported config (Simple Yaml, multi-file in-
      app config, etc.)
    resourceList: The list of resources defined using the type-agnostic
      Resource definitions.
    resources: The map of resource configs where the key is the name of
      resources and the value is the resource config.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResourcesValue(_messages.Message):
    r"""The map of resource configs where the key is the name of resources and
    the value is the resource config.

    Messages:
      AdditionalProperty: An additional property for a ResourcesValue object.

    Fields:
      additionalProperties: Additional properties of type ResourcesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResourcesValue object.

      Fields:
        key: Name of the additional property.
        value: A ResourceConfig attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('ResourceConfig', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  config = _messages.BytesField(1)
  resourceList = _messages.MessageField('Resource', 2, repeated=True)
  resources = _messages.MessageField('ResourcesValue', 3)


class Deployment(_messages.Message):
  r"""Message describing Deployment object Next tag: 14

  Messages:
    AnnotationsValue: Unstructured key value map that may be set by external
      tools to store and arbitrary metadata. They are not queryable and should
      be preserved when modifying objects. This field follows Kubernetes
      annotations' namespacing, limits, and rules. More info:
      http://kubernetes.io/docs/user-guide/annotations
    LabelsValue: Labels as key value pairs

  Fields:
    annotations: Unstructured key value map that may be set by external tools
      to store and arbitrary metadata. They are not queryable and should be
      preserved when modifying objects. This field follows Kubernetes
      annotations' namespacing, limits, and rules. More info:
      http://kubernetes.io/docs/user-guide/annotations
    application: Output only. The name of the parent application.
    createSelector: Optional selectors that should be applied to limit the
      scope of the deployment creation.
    createTime: Output only. Create time stamp
    deleteSelector: Optional selectors that should be applied to limit the
      scope of the deployment deletion.
    deleteTime: Output only. For a deleted resource, the deletion time. It is
      only populated as a response to a Delete request.
    dryRun: The data used for dry run. When this dry run message is set, a dry
      run deployment will be created. A dry run deployment will not deploy
      live resources. It will generate change plans that include proposed
      changes and drift changes. The results will be populated inside the dry
      run message.
    etag: Output only. A system-generated fingerprint for this version of the
      resource. May be used to detect modification conflict during updates.
    labels: Labels as key value pairs
    name: Output only. Canonical name of resource
    reconciling: Output only. Indicates whether the resource's reconciliation
      is still in progress.
    status: Output only. The status of the deployment
    updateTime: Output only. Update time stamp
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AnnotationsValue(_messages.Message):
    r"""Unstructured key value map that may be set by external tools to store
    and arbitrary metadata. They are not queryable and should be preserved
    when modifying objects. This field follows Kubernetes annotations'
    namespacing, limits, and rules. More info: http://kubernetes.io/docs/user-
    guide/annotations

    Messages:
      AdditionalProperty: An additional property for a AnnotationsValue
        object.

    Fields:
      additionalProperties: Additional properties of type AnnotationsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AnnotationsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  annotations = _messages.MessageField('AnnotationsValue', 1)
  application = _messages.StringField(2)
  createSelector = _messages.MessageField('Selector', 3)
  createTime = _messages.StringField(4)
  deleteSelector = _messages.MessageField('Selector', 5)
  deleteTime = _messages.StringField(6)
  dryRun = _messages.MessageField('DryRun', 7)
  etag = _messages.StringField(8)
  labels = _messages.MessageField('LabelsValue', 9)
  name = _messages.StringField(10)
  reconciling = _messages.BooleanField(11)
  status = _messages.MessageField('DeploymentStatus', 12)
  updateTime = _messages.StringField(13)


class DeploymentOperationMetadata(_messages.Message):
  r"""Operation metadata for Deployment.Create. Next tag: 8

  Fields:
    apiVersion: The API version which triggered this operation.
    cancelRequested: Is cancelation requested for this operation.
    createTime: The time this operation was created.
    endTime: The time this operation ended or empty if it is still active.
    resourceStatus: The ongoinging state of resources that are being deployed.
      Order is not guaranteed to be stable between multiple reads of the same
      ongoing operation.
    target: Resource path for the target resource of the operation.
    verb: The verb associated with the API method which triggered this
      operation.
  """

  apiVersion = _messages.StringField(1)
  cancelRequested = _messages.BooleanField(2)
  createTime = _messages.StringField(3)
  endTime = _messages.StringField(4)
  resourceStatus = _messages.MessageField('ResourceDeploymentStatus', 5, repeated=True)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class DeploymentStatus(_messages.Message):
  r"""Message to encapsulate the current status of the deployment.

  Enums:
    StateValueValuesEnum: The state associated with the deployment.

  Fields:
    errorMessage: The error message associated with a failed deployment state,
      if applicable.
    jobDetails: Details of each deploy job.
    resourceStatus: The state of resources that are being deployed. Order is
      not guaranteed to be stable when deployment is in progress.
    state: The state associated with the deployment.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state associated with the deployment.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      FAILED: Deployment completed with failure.
      SUCCEEDED: Deployment completed successfully.
      IN_PROGRESS: Deployment is running and has not completed.
    """
    STATE_UNSPECIFIED = 0
    FAILED = 1
    SUCCEEDED = 2
    IN_PROGRESS = 3

  errorMessage = _messages.StringField(1)
  jobDetails = _messages.MessageField('JobDetails', 2, repeated=True)
  resourceStatus = _messages.MessageField('ResourceDeploymentStatus', 3, repeated=True)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class DomainConfig(_messages.Message):
  r"""A DomainConfig object.

  Fields:
    domain: Domain name for this config.
    routes: A list of route configurations to associate with the domain. Each
      Route configuration must include a paths configuration.
  """

  domain = _messages.StringField(1)
  routes = _messages.MessageField('Route', 2, repeated=True)


class DryRun(_messages.Message):
  r"""Message describing the dry run deployment options and outputs.

  Fields:
    applicationConfig: The Application Config to run the dry run on.
    resourceChanges: Output only. Resulting changes as a result of the
      provided dry run Application Config.
    resourceDrift: Output only. Changes that were made externally since the
      previous non-dry run deployment.
    useParent: Indicates whether to use the parent application for dry run
      execution.
  """

  applicationConfig = _messages.MessageField('Config', 1)
  resourceChanges = _messages.MessageField('DryRunChanges', 2)
  resourceDrift = _messages.MessageField('DryRunChanges', 3)
  useParent = _messages.BooleanField(4)


class DryRunChanges(_messages.Message):
  r"""Message describing proposed changes from dry run.

  Fields:
    hasChanges: Whether there are changes.
    textOutput: Formatted output of the changes. Same format as the terraform
      plan output.
  """

  hasChanges = _messages.BooleanField(1)
  textOutput = _messages.StringField(2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class FirebaseHostingConfig(_messages.Message):
  r"""Message for defining firebase hosting resource.

  Fields:
    config: Hosting site configuration.
    resources: Reference to the target resources to add to the hosting site
      configuration. The resource binding's "binding-config" has no used
      fields currently.
  """

  config = _messages.MessageField('HostingSiteConfig', 1)
  resources = _messages.MessageField('ServiceResourceBindingConfig', 2, repeated=True)


class FirebaseHostingStatus(_messages.Message):
  r"""Detailed status for Firebase Hosting resource.

  Fields:
    domains: List of domains associated with the firebase hosting site.
    hostingConfig: Hosting configuration created by Serverless Stacks.
  """

  domains = _messages.StringField(1, repeated=True)
  hostingConfig = _messages.StringField(2)


class FirestoreConfig(_messages.Message):
  r"""Message for defining firestore configuration.

  Fields:
    config: Database configuration.
  """

  config = _messages.MessageField('FirestoreDatabaseConfig', 1)


class FirestoreDatabaseConfig(_messages.Message):
  r"""Message for defining firestore database configuration.

  Fields:
    location_id: LocationID is the location of the database. Available
      databases locations are listed at
      https://cloud.google.com/firestore/docs/locations.
  """

  location_id = _messages.StringField(1)


class HostingSiteConfig(_messages.Message):
  r"""Message for defining the firebase hosting site configuration.

  Fields:
    site_id: Firebase hosting site-ID.
  """

  site_id = _messages.StringField(1)


class JobComponent(_messages.Message):
  r"""Message to encapsulate component actuated by a job. JobComponent does
  not represent a GCP API resource.

  Enums:
    OperationValueValuesEnum: Operation to be performed on component.

  Fields:
    operation: Operation to be performed on component.
    typedName: TypedName is the component name and its type.
  """

  class OperationValueValuesEnum(_messages.Enum):
    r"""Operation to be performed on component.

    Values:
      COMPONENT_OPERATION_UNSPECIFIED: ComponentOperation unset.
      APPLY: Apply configuration to component.
      DESTROY: Destroy component.
    """
    COMPONENT_OPERATION_UNSPECIFIED = 0
    APPLY = 1
    DESTROY = 2

  operation = _messages.EnumField('OperationValueValuesEnum', 1)
  typedName = _messages.MessageField('TypedName', 2)


class JobDetails(_messages.Message):
  r"""Message to encapsulate the current status deployment job.

  Enums:
    StateValueValuesEnum: State of deployment job.

  Fields:
    components: Components to be actuated by the job.
    jobName: Name of deployment job. Format:
      projects/{project}/locations/{location}/builds/{build}
    jobUri: URI of deployment job within Google Cloud Console.
    state: State of deployment job.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""State of deployment job.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      FAILED: Job completed with failure.
      SUCCEEDED: Job completed successfully.
      IN_PROGRESS: Job is running and has not completed.
    """
    STATE_UNSPECIFIED = 0
    FAILED = 1
    SUCCEEDED = 2
    IN_PROGRESS = 3

  components = _messages.MessageField('JobComponent', 1, repeated=True)
  jobName = _messages.StringField(2)
  jobUri = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class JobSettingsConfig(_messages.Message):
  r"""Message for Cloud Run Job settings config. Next tag: 8

  Messages:
    EnvVarsValue: Key-value pairs to set as environment variables. Note that
      integration bindings will add/update the list of final env vars that are
      deployed to a job.

  Fields:
    args: Comma-separated arguments passed to the command run by the container
      image.
    cmd: Entrypoint for the container image.
    envVars: Key-value pairs to set as environment variables. Note that
      integration bindings will add/update the list of final env vars that are
      deployed to a job.
    image: The container image to deploy the job with.
    maxRetries: Number of times a task is allowed to restart in case of
      failure before being failed permanently. This applies per-task, not per-
      job. If set to 0, tasks will only run once and never be retried on
      failure. Default value is 3.
    parallelism: Number of tasks that may run concurrently. Must be less than
      or equal to the number of tasks. When the job is run, if this field is 0
      or unset, the maximum possible value will be used for that execution.
      Default is unset.
    taskCount: Specifies the desired number of tasks the execution should run.
      Setting to 1 means that parallelism is limited to 1 and the success of
      that task signals the success of the execution. Default value is 1.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class EnvVarsValue(_messages.Message):
    r"""Key-value pairs to set as environment variables. Note that integration
    bindings will add/update the list of final env vars that are deployed to a
    job.

    Messages:
      AdditionalProperty: An additional property for a EnvVarsValue object.

    Fields:
      additionalProperties: Additional properties of type EnvVarsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a EnvVarsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  args = _messages.StringField(1, repeated=True)
  cmd = _messages.StringField(2, repeated=True)
  envVars = _messages.MessageField('EnvVarsValue', 3)
  image = _messages.StringField(4)
  maxRetries = _messages.IntegerField(5, variant=_messages.Variant.INT32)
  parallelism = _messages.IntegerField(6, variant=_messages.Variant.INT32)
  taskCount = _messages.IntegerField(7, variant=_messages.Variant.INT32)


class ListApplicationsResponse(_messages.Message):
  r"""Message for response to listing Applications

  Fields:
    applications: The list of Application
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  applications = _messages.MessageField('Application', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListDeploymentsResponse(_messages.Message):
  r"""Message for response to listing Deployments

  Fields:
    deployments: The list of Deployment
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  deployments = _messages.MessageField('Deployment', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal, successful response of the operation. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal, successful response of the operation. If the original
    method returns no data on success, such as `Delete`, the response is
    `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: API version used to start the operation.
    createTime: The time the operation was created.
    endTime: The time the operation finished running.
    requestedCancellation: Identifies whether the user has requested
      cancellation of the operation. Operations that have successfully been
      cancelled have Operation.error value with a google.rpc.Status.code of 1,
      corresponding to `Code.CANCELLED`.
    statusMessage: Human-readable status of the operation, if any.
    target: Server-defined resource path for the target of the operation.
    verb: Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class RedisConfig(_messages.Message):
  r"""Message for Redis configs.

  Fields:
    instance: Configs for the Redis instance.
  """

  instance = _messages.MessageField('RedisInstanceConfig', 1)


class RedisInstanceConfig(_messages.Message):
  r"""Message for Redis instance configs.

  Fields:
    memory_size_gb: The redis instance memory size, in GB.
  """

  memory_size_gb = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class Resource(_messages.Message):
  r"""Resource defines a Stacks resource.

  Messages:
    ConfigValue: Configuration is the typekit-specified set of fields that
      define the resource. The configuration has an associated typekit-
      specified JSONSchema (https://json-schema.org/) that defines the
      expected shape.

  Fields:
    bindings: Bindings describe the resources that this resource references.
      For Ingress Services, this includes Components. For Components, this
      includes Backing Services. For Backing Services, this is empty.
    config: Configuration is the typekit-specified set of fields that define
      the resource. The configuration has an associated typekit-specified
      JSONSchema (https://json-schema.org/) that defines the expected shape.
    id: Resource ID describes the resource that's bound.
    subresources: Subresources is the set of subresources within this
      resource. Support for this field depends on the type of the Resource and
      is defined by the corresponding Typekit.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ConfigValue(_messages.Message):
    r"""Configuration is the typekit-specified set of fields that define the
    resource. The configuration has an associated typekit-specified JSONSchema
    (https://json-schema.org/) that defines the expected shape.

    Messages:
      AdditionalProperty: An additional property for a ConfigValue object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ConfigValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  config = _messages.MessageField('ConfigValue', 2)
  id = _messages.MessageField('ResourceID', 3)
  subresources = _messages.MessageField('Resource', 4, repeated=True)


class ResourceComponentStatus(_messages.Message):
  r"""Status for a component of a resource.

  Enums:
    StateValueValuesEnum: The state of the resource component.

  Fields:
    consoleLink: Pantheon link for the resource. This does not exist for every
      resource that makes up the SAF resource.
    diverged: Indicates that this resource component has been altered and may
      not match the expected state.
    name: The name the resource component. Usually it's the name of the GCP
      resource, which was used inside the Terraform Resource block that
      defines it. (e.g. cri-domain-cert)
    reason: The reason why this resource component to be in its state.
    selfLink: Fully qualified URL to the object represented by this resource
      component.
    state: The state of the resource component.
    type: The Terraform Resource Type of the GCP resource (e.g.
      "google_compute_managed_ssl_certificate").
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The state of the resource component.

    Values:
      STATE_UNSPECIFIED: The status of this component is unspecified.
      DEPLOYED: The component has been deployed.
      MISSING: The component is missing.
      PROVISIONING: The component has been deployed and is provisioning.
      ACTIVE: The component has been deployed and is working as intended. This
        is intended for resources that have a health indicator.
      FAILED: The component has failed and the full error message will be
        populated in the resource.
    """
    STATE_UNSPECIFIED = 0
    DEPLOYED = 1
    MISSING = 2
    PROVISIONING = 3
    ACTIVE = 4
    FAILED = 5

  consoleLink = _messages.StringField(1)
  diverged = _messages.BooleanField(2)
  name = _messages.StringField(3)
  reason = _messages.StringField(4)
  selfLink = _messages.StringField(5)
  state = _messages.EnumField('StateValueValuesEnum', 6)
  type = _messages.StringField(7)


class ResourceConfig(_messages.Message):
  r"""Message for the Resource configuration. Next tag: 11

  Fields:
    cloudsql: CloudSql configuration.
    firebase_hosting: Firebase hosting configuration.
    firestore: Firestore configuration.
    job: Cloud Run job configuration.
    latestDeployment: Output only. The deployment name for the most recent
      deployment that has been triggered for a given resource. If a resource
      was never deployed then this field will be empty.
    redis: Redis configuration.
    router: Router configuration.
    service: Cloud Run service configuration.
  """

  cloudsql = _messages.MessageField('CloudSqlConfig', 1)
  firebase_hosting = _messages.MessageField('FirebaseHostingConfig', 2)
  firestore = _messages.MessageField('FirestoreConfig', 3)
  job = _messages.MessageField('CloudRunJobConfig', 4)
  latestDeployment = _messages.StringField(5)
  redis = _messages.MessageField('RedisConfig', 6)
  router = _messages.MessageField('RouterConfig', 7)
  service = _messages.MessageField('CloudRunServiceConfig', 8)


class ResourceDeploymentStatus(_messages.Message):
  r"""Message decribing the status of a resource being deployed. Next tag: 5

  Enums:
    OperationValueValuesEnum: Operation to be performed on the resource .
    StateValueValuesEnum: Current status of the resource.

  Fields:
    errorMessage: The error details if the state is FAILED.
    name: Name of the resource.
    operation: Operation to be performed on the resource .
    state: Current status of the resource.
  """

  class OperationValueValuesEnum(_messages.Enum):
    r"""Operation to be performed on the resource .

    Values:
      OPERATION_UNSPECIFIED: Default value indicating the operation is
        unknown.
      APPLY: Apply configuration to resource.
      DESTROY: Destroy resource.
    """
    OPERATION_UNSPECIFIED = 0
    APPLY = 1
    DESTROY = 2

  class StateValueValuesEnum(_messages.Enum):
    r"""Current status of the resource.

    Values:
      STATE_UNSPECIFIED: Default value indicating the state is unknown.
      NOT_STARTED: Resource queued for deployment.
      RUNNING: Deployment in progress.
      FINISHED: Deployment completed.
      SUCCEEDED: Deployment completed successfully.
      FAILED: Deployment completed with failure.
    """
    STATE_UNSPECIFIED = 0
    NOT_STARTED = 1
    RUNNING = 2
    FINISHED = 3
    SUCCEEDED = 4
    FAILED = 5

  errorMessage = _messages.StringField(1)
  name = _messages.MessageField('TypedName', 2)
  operation = _messages.EnumField('OperationValueValuesEnum', 3)
  state = _messages.EnumField('StateValueValuesEnum', 4)


class ResourceID(_messages.Message):
  r"""ResourceID encapsulates the definition of the identity of a resource.

  Fields:
    name: Name is the name of the resource. This name must be unique within
      the type.
    type: Type is the name of the resource.
  """

  name = _messages.StringField(1)
  type = _messages.StringField(2)


class ResourceRef(_messages.Message):
  r"""ResourceRef encapsulates the definition of a reference to another
  resource.

  Fields:
    id: The ID of another resource specified in the config.
  """

  id = _messages.MessageField('ResourceID', 1)


class ResourceStatus(_messages.Message):
  r"""Status for a resource.

  Enums:
    StateValueValuesEnum: The enum state of the resource.

  Messages:
    ExtraDetailsValue: Extra details of the resource that are needed for the
      users to make use of the resources, such as IP Address of GCLB.

  Fields:
    bindingStatus: The binding status related to this resource. Deprecated:
      it's not implemented.
    consoleLink: Pantheon link for the resource. For example, the custom
      domain will link to the GCLB page.
    diverged: Indicates that a child component of this resource has been
      altered and may not match the expected state. Deprecated: it's not
      implemented.
    extraDetails: Extra details of the resource that are needed for the users
      to make use of the resources, such as IP Address of GCLB.
    firebaseHostingDetails: Details for Firebase Hosting resource. Deprecated:
      use extra_details instead.
    id: ID of the resource.
    reason: The reason why this resource is in the current state.
    resourceComponentStatuses: Repeated field with status per component
      created for this resource.
    resourceName: Name of the resource, pulled from the Application Config.
      Deprecated: use the id field instead.
    routerDetails: Detail Status of Router resource. Deprecated: use
      extra_details instead.
    state: The enum state of the resource.
    type: Type of resource. Deprecated: use the id field instead.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""The enum state of the resource.

    Values:
      STATE_UNSPECIFIED: The status of this resource is unspecified.
      ACTIVE: The resource is active.
      FAILED: Some of the components of the resource are not working.
      MISSING: The key components are missing after at least one successful
        deployment. The user could have manually removed a resource that was
        deployed.
      UPDATING: The resource is being deployed.
      NOT_READY: Some of the resource's child resources are not in ready
        state.
      NOT_DEPLOYED: The resource is currently not deployed. This could happen
        if the resource was added to the application config, but was not
        deployed yet, or the resource was undeployed.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    FAILED = 2
    MISSING = 3
    UPDATING = 4
    NOT_READY = 5
    NOT_DEPLOYED = 6

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ExtraDetailsValue(_messages.Message):
    r"""Extra details of the resource that are needed for the users to make
    use of the resources, such as IP Address of GCLB.

    Messages:
      AdditionalProperty: An additional property for a ExtraDetailsValue
        object.

    Fields:
      additionalProperties: Properties of the object.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ExtraDetailsValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  bindingStatus = _messages.MessageField('BindingStatus', 1, repeated=True)
  consoleLink = _messages.StringField(2)
  diverged = _messages.BooleanField(3)
  extraDetails = _messages.MessageField('ExtraDetailsValue', 4)
  firebaseHostingDetails = _messages.MessageField('FirebaseHostingStatus', 5)
  id = _messages.MessageField('ResourceID', 6)
  reason = _messages.StringField(7)
  resourceComponentStatuses = _messages.MessageField('ResourceComponentStatus', 8, repeated=True)
  resourceName = _messages.StringField(9)
  routerDetails = _messages.MessageField('RouterStatus', 10)
  state = _messages.EnumField('StateValueValuesEnum', 11)
  type = _messages.StringField(12)


class Route(_messages.Message):
  r"""Message for a single routeable resource within a Router.

  Fields:
    cdn: Whether to enable CDN on the route.
    paths: List of paths to be routed to this route. e.g. ["/*, /api/*"]. The
      path must fit the constraints at https://cloud.google.com/load-
      balancing/docs/url-map-concepts#pm-constraints.
    ref: Required. A reference to the resource in the config to which this is
      routing. e.g. "service/hello-service".
  """

  cdn = _messages.BooleanField(1)
  paths = _messages.StringField(2, repeated=True)
  ref = _messages.StringField(3)


class RouterConfig(_messages.Message):
  r"""Message for a Router resource.

  Fields:
    default_route: Deprecated. Use the DomainConfig instead. The default route
      config. The URL paths field is not required for this route config.
    dns_zone: Deprecated. Use the DomainConfig instead. DNSZone represents an
      existing DNS zone for the router. It's used for bring-your-own-DNSZone
      case. If empty, a new managed DNS zone shall be created.
    domain: Deprecated. Use the DomainConfig instead. Domain name to associate
      with the router.
    domains: The config for each domain.
    routes: Deprecated. Use the DomainConfig instead. A list of route
      configurations to associate with the router. Each Route configuration
      must include a paths configuration.
  """

  default_route = _messages.MessageField('Route', 1)
  dns_zone = _messages.StringField(2)
  domain = _messages.StringField(3)
  domains = _messages.MessageField('DomainConfig', 4, repeated=True)
  routes = _messages.MessageField('Route', 5, repeated=True)


class RouterStatus(_messages.Message):
  r"""Detail Status of Router resource.

  Fields:
    ipAddress: IP Address of the Google Cloud Load Balancer.
  """

  ipAddress = _messages.StringField(1)


class RunappsProjectsLocationsApplicationsCreateRequest(_messages.Message):
  r"""A RunappsProjectsLocationsApplicationsCreateRequest object.

  Fields:
    application: A Application resource to be passed as the request body.
    applicationId: Required. Id of the requesting object If auto-generating Id
      server-side, remove this field and application_id from the
      method_signature of Create RPC
    parent: Required. Value for parent.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
  """

  application = _messages.MessageField('Application', 1)
  applicationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class RunappsProjectsLocationsApplicationsDeleteRequest(_messages.Message):
  r"""A RunappsProjectsLocationsApplicationsDeleteRequest object.

  Fields:
    force: If set to true, any child deployments of this application will also
      be deleted. Followed the best practice from
      https://aip.dev/135#cascading-delete
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  force = _messages.BooleanField(1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)


class RunappsProjectsLocationsApplicationsDeploymentsCreateRequest(_messages.Message):
  r"""A RunappsProjectsLocationsApplicationsDeploymentsCreateRequest object.

  Fields:
    deployment: A Deployment resource to be passed as the request body.
    deploymentId: Required. Id of the requesting object If auto-generating Id
      server-side, remove this field and deployment_id from the
      method_signature of Create RPC
    parent: Required. Value for parent.
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    validateOnly: If true, the Create request will just do a dry run of the
      deploy instead of actuating anything.
  """

  deployment = _messages.MessageField('Deployment', 1)
  deploymentId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)
  validateOnly = _messages.BooleanField(5)


class RunappsProjectsLocationsApplicationsDeploymentsGetRequest(_messages.Message):
  r"""A RunappsProjectsLocationsApplicationsDeploymentsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class RunappsProjectsLocationsApplicationsDeploymentsListRequest(_messages.Message):
  r"""A RunappsProjectsLocationsApplicationsDeploymentsListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListDeploymentsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class RunappsProjectsLocationsApplicationsGetRequest(_messages.Message):
  r"""A RunappsProjectsLocationsApplicationsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class RunappsProjectsLocationsApplicationsGetStatusRequest(_messages.Message):
  r"""A RunappsProjectsLocationsApplicationsGetStatusRequest object.

  Fields:
    name: Required. Name of the resource.
    readMask: Field mask used for limiting the resources to query status on.
  """

  name = _messages.StringField(1, required=True)
  readMask = _messages.StringField(2)


class RunappsProjectsLocationsApplicationsListRequest(_messages.Message):
  r"""A RunappsProjectsLocationsApplicationsListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListApplicationsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class RunappsProjectsLocationsApplicationsPatchRequest(_messages.Message):
  r"""A RunappsProjectsLocationsApplicationsPatchRequest object.

  Fields:
    application: A Application resource to be passed as the request body.
    name: name of resource
    requestId: An optional request ID to identify requests. Specify a unique
      request ID so that if you must retry your request, the server will know
      to ignore the request if it has already been completed. The server will
      guarantee that for at least 60 minutes since the first request. For
      example, consider a situation where you make an initial request and the
      request times out. If you make the request again with the same request
      ID, the server can check if original operation with the same request ID
      was received, and if so, will ignore the second request. This prevents
      clients from accidentally creating duplicate commitments. The request ID
      must be a valid UUID with the exception that zero UUID is not supported
      (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the Application resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  application = _messages.MessageField('Application', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class RunappsProjectsLocationsGetRequest(_messages.Message):
  r"""A RunappsProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class RunappsProjectsLocationsListRequest(_messages.Message):
  r"""A RunappsProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class RunappsProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A RunappsProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class RunappsProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A RunappsProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class RunappsProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A RunappsProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class RunappsProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A RunappsProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class Selector(_messages.Message):
  r"""Message for selecting the resources within an application. Next tag: 3

  Fields:
    matchTypeNames: match_type_names is a list resource name + type to match.
      Use '*' or empty string for wildcard either the name or the type. E.g.
      type='service' name='' will match all services. type='*' name='default'
      will match all resources named as 'default'.
    notTypeNames: not_type_names excludes the names + types. If a type+name is
      in this list as well as match_type_names, it will not be selected.
  """

  matchTypeNames = _messages.MessageField('TypedName', 1, repeated=True)
  notTypeNames = _messages.MessageField('TypedName', 2, repeated=True)


class ServiceResourceBindingConfig(_messages.Message):
  r"""Message for a resource binding, defined in config of the source
  resource.

  Messages:
    BindingConfigValue: Any configs associated with the binding. Supported
      keys are resource type specific.

  Fields:
    binding_config: Any configs associated with the binding. Supported keys
      are resource type specific.
    ref: Reference to a target resource that is being bound. Format: "/", e.g.
      "cloudsql/sql_db"
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class BindingConfigValue(_messages.Message):
    r"""Any configs associated with the binding. Supported keys are resource
    type specific.

    Messages:
      AdditionalProperty: An additional property for a BindingConfigValue
        object.

    Fields:
      additionalProperties: Additional properties of type BindingConfigValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a BindingConfigValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  binding_config = _messages.MessageField('BindingConfigValue', 1)
  ref = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TypedName(_messages.Message):
  r"""TypedName is a resource name + its type. Next tag: 3

  Fields:
    name: The name of the resource.
    type: The type of the resource.
  """

  name = _messages.StringField(1)
  type = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    CloudSqlSettings, 'activation_policy', 'activation-policy')
encoding.AddCustomJsonFieldMapping(
    CloudSqlSettings, 'availability_type', 'availability-type')
encoding.AddCustomJsonFieldMapping(
    CloudSqlSettings, 'disk_size', 'disk-size')
encoding.AddCustomJsonFieldMapping(
    CloudSqlSettings, 'disk_type', 'disk-type')
encoding.AddCustomJsonFieldMapping(
    FirestoreDatabaseConfig, 'location_id', 'location-id')
encoding.AddCustomJsonFieldMapping(
    HostingSiteConfig, 'site_id', 'site-id')
encoding.AddCustomJsonFieldMapping(
    RedisInstanceConfig, 'memory_size_gb', 'memory-size-gb')
encoding.AddCustomJsonFieldMapping(
    ResourceConfig, 'firebase_hosting', 'firebase-hosting')
encoding.AddCustomJsonFieldMapping(
    RouterConfig, 'default_route', 'default-route')
encoding.AddCustomJsonFieldMapping(
    RouterConfig, 'dns_zone', 'dns-zone')
encoding.AddCustomJsonFieldMapping(
    ServiceResourceBindingConfig, 'binding_config', 'binding-config')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

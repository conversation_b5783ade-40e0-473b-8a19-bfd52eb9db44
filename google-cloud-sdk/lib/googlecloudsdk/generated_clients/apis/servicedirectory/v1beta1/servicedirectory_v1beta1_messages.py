"""Generated message classes for servicedirectory version v1beta1.

Service Directory is a platform for discovering, publishing, and connecting
services.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'servicedirectory'


class Asset(_messages.Message):
  r"""Asset associated with Service Directory resource.

  Fields:
    resource: Required. Schemaless uri for the component Examples :
      //compute.googleapis.com/projects/1234/zones/us-
      east1-c/instanceGroups/mig1
      //compute.googleapis.com/projects/1234/regions/us-
      east1/forwardingRules/fr1
  """

  resource = _messages.StringField(1)


class Attributes(_messages.Message):
  r"""Attributes are structured data attached to a given resource.

  Fields:
    managedRegistration: Output only. Indicates whether a GCP product or
      service manages this resource. When a resource is fully managed by
      another GCP product or system the information in Service Directory is
      read-only. The source of truth is the relevant GCP product or system
      which is managing the resource. The Service Directory resource will be
      updated or deleted as appropriate to reflect the state of the underlying
      `origin_resource`. Note: The `origin_resource` can be found in the
      endpoint(s) associated with this service.
    pscConnectionId: Optional. The Private Service Connect connection id of
      the Private Service Connect Forwarding Rule. This field should be unset
      if the service is not a Private Service Connect service.
    pscTarget: Optional. The target resource for the Private Service Connect
      service. This field should be unset if the service is not a Private
      Service Connect service. For a Private Service Connect service accessing
      managed services, this is the URI of the service producer's service
      attachment. For a Private Service Connect service accessing Google APIs,
      this will be the name of the Google API bundle. See the [Private Service
      Connect documentation](https://cloud.google.com/vpc/docs/private-
      service-connect) for more information.
  """

  managedRegistration = _messages.BooleanField(1)
  pscConnectionId = _messages.IntegerField(2, variant=_messages.Variant.UINT64)
  pscTarget = _messages.StringField(3)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    ignoreChildExemptions: A boolean attribute.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  ignoreChildExemptions = _messages.BooleanField(2)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 3)


class AuthorizationLoggingOptions(_messages.Message):
  r"""Authorization-related information used by Cloud Audit Logging.

  Enums:
    PermissionTypeValueValuesEnum: The type of the permission that was
      checked.

  Fields:
    permissionType: The type of the permission that was checked.
  """

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type of the permission that was checked.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: A read of admin (meta) data.
      ADMIN_WRITE: A write of admin (meta) data.
      DATA_READ: A read of standard data.
      DATA_WRITE: A write of standard data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 1)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    bindingId: A string attribute.
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  bindingId = _messages.StringField(1)
  condition = _messages.MessageField('Expr', 2)
  members = _messages.StringField(3, repeated=True)
  role = _messages.StringField(4)


class CloudAuditOptions(_messages.Message):
  r"""Write a Cloud Audit log

  Enums:
    LogNameValueValuesEnum: The log_name to populate in the Cloud Audit
      Record.

  Fields:
    authorizationLoggingOptions: Information used by the Cloud Audit Logging
      pipeline.
    logName: The log_name to populate in the Cloud Audit Record.
  """

  class LogNameValueValuesEnum(_messages.Enum):
    r"""The log_name to populate in the Cloud Audit Record.

    Values:
      UNSPECIFIED_LOG_NAME: Default. Should not be used.
      ADMIN_ACTIVITY: Corresponds to "cloudaudit.googleapis.com/activity"
      DATA_ACCESS: Corresponds to "cloudaudit.googleapis.com/data_access"
    """
    UNSPECIFIED_LOG_NAME = 0
    ADMIN_ACTIVITY = 1
    DATA_ACCESS = 2

  authorizationLoggingOptions = _messages.MessageField('AuthorizationLoggingOptions', 1)
  logName = _messages.EnumField('LogNameValueValuesEnum', 2)


class Condition(_messages.Message):
  r"""A condition to be met.

  Enums:
    IamValueValuesEnum: Trusted attributes supplied by the IAM system.
    OpValueValuesEnum: An operator to apply the subject with.
    SysValueValuesEnum: Trusted attributes supplied by any service that owns
      resources and uses the IAM system for access control.

  Fields:
    iam: Trusted attributes supplied by the IAM system.
    op: An operator to apply the subject with.
    svc: Trusted attributes discharged by the service.
    sys: Trusted attributes supplied by any service that owns resources and
      uses the IAM system for access control.
    values: The objects of the condition.
  """

  class IamValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by the IAM system.

    Values:
      NO_ATTR: Default non-attribute.
      AUTHORITY: Either principal or (if present) authority selector.
      ATTRIBUTION: The principal (even if an authority selector is present),
        which must only be used for attribution, not authorization.
      SECURITY_REALM: Any of the security realms in the IAMContext
        (go/security-realms). When used with IN, the condition indicates "any
        of the request's realms match one of the given values; with NOT_IN,
        "none of the realms match any of the given values". Note that a value
        can be: - 'self:campus' (i.e., clients that are in the same campus) -
        'self:metro' (i.e., clients that are in the same metro) - 'self:cloud-
        region' (i.e., allow connections from clients that are in the same
        cloud region) - 'self:prod-region' (i.e., allow connections from
        clients that are in the same prod region) - 'guardians' (i.e., allow
        connections from its guardian realms. See go/security-realms-
        glossary#guardian for more information.) - 'self' [DEPRECATED] (i.e.,
        allow connections from clients that are in the same security realm,
        which is currently but not guaranteed to be campus-sized) - a realm
        (e.g., 'campus-abc') - a realm group (e.g., 'realms-for-borg-cell-xx',
        see: go/realm-groups) A match is determined by a realm group
        membership check performed by a RealmAclRep object (go/realm-acl-
        howto). It is not permitted to grant access based on the *absence* of
        a realm, so realm conditions can only be used in a "positive" context
        (e.g., ALLOW/IN or DENY/NOT_IN).
      APPROVER: An approver (distinct from the requester) that has authorized
        this request. When used with IN, the condition indicates that one of
        the approvers associated with the request matches the specified
        principal, or is a member of the specified group. Approvers can only
        grant additional access, and are thus only used in a strictly positive
        context (e.g. ALLOW/IN or DENY/NOT_IN).
      JUSTIFICATION_TYPE: What types of justifications have been supplied with
        this request. String values should match enum names from
        security.credentials.JustificationType, e.g. "MANUAL_STRING". It is
        not permitted to grant access based on the *absence* of a
        justification, so justification conditions can only be used in a
        "positive" context (e.g., ALLOW/IN or DENY/NOT_IN). Multiple
        justifications, e.g., a Buganizer ID and a manually-entered reason,
        are normal and supported.
      CREDENTIALS_TYPE: What type of credentials have been supplied with this
        request. String values should match enum names from
        security_loas_l2.CredentialsType - currently, only
        CREDS_TYPE_EMERGENCY is supported. It is not permitted to grant access
        based on the *absence* of a credentials type, so the conditions can
        only be used in a "positive" context (e.g., ALLOW/IN or DENY/NOT_IN).
      CREDS_ASSERTION: EXPERIMENTAL -- DO NOT USE. The conditions can only be
        used in a "positive" context (e.g., ALLOW/IN or DENY/NOT_IN).
    """
    NO_ATTR = 0
    AUTHORITY = 1
    ATTRIBUTION = 2
    SECURITY_REALM = 3
    APPROVER = 4
    JUSTIFICATION_TYPE = 5
    CREDENTIALS_TYPE = 6
    CREDS_ASSERTION = 7

  class OpValueValuesEnum(_messages.Enum):
    r"""An operator to apply the subject with.

    Values:
      NO_OP: Default no-op.
      EQUALS: DEPRECATED. Use IN instead.
      NOT_EQUALS: DEPRECATED. Use NOT_IN instead.
      IN: The condition is true if the subject (or any element of it if it is
        a set) matches any of the supplied values.
      NOT_IN: The condition is true if the subject (or every element of it if
        it is a set) matches none of the supplied values.
      DISCHARGED: Subject is discharged
    """
    NO_OP = 0
    EQUALS = 1
    NOT_EQUALS = 2
    IN = 3
    NOT_IN = 4
    DISCHARGED = 5

  class SysValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by any service that owns resources and
    uses the IAM system for access control.

    Values:
      NO_ATTR: Default non-attribute type
      REGION: Region of the resource
      SERVICE: Service name
      NAME: Resource name
      IP: IP address of the caller
    """
    NO_ATTR = 0
    REGION = 1
    SERVICE = 2
    NAME = 3
    IP = 4

  iam = _messages.EnumField('IamValueValuesEnum', 1)
  op = _messages.EnumField('OpValueValuesEnum', 2)
  svc = _messages.StringField(3)
  sys = _messages.EnumField('SysValueValuesEnum', 4)
  values = _messages.StringField(5, repeated=True)


class ContactInfo(_messages.Message):
  r"""Contact information for the functionality. Used to refer to owners of
  service or workload resource for different personas.

  Fields:
    channel: Optional. Channel to use to reach the contact, eg. pager, email
    displayName: Optional. Display name of the contact.
    email: Optional. Email of the contact.
  """

  channel = _messages.StringField(1)
  displayName = _messages.StringField(2)
  email = _messages.StringField(3)


class CounterOptions(_messages.Message):
  r"""Increment a streamz counter with the specified metric and field names.
  Metric names should start with a '/', generally be lowercase-only, and end
  in "_count". Field names should not contain an initial slash. The actual
  exported metric names will have "/iam/policy" prepended. Field names
  correspond to IAM request parameters and field values are their respective
  values. Supported field names: - "authority", which is "[token]" if
  IAMContext.token is present, otherwise the value of
  IAMContext.authority_selector if present, and otherwise a representation of
  IAMContext.principal; or - "iam_principal", a representation of
  IAMContext.principal even if a token or authority selector is present; or -
  "" (empty string), resulting in a counter with no fields. Examples: counter
  { metric: "/debug_access_count" field: "iam_principal" } ==> increment
  counter /iam/policy/debug_access_count {iam_principal=[value of
  IAMContext.principal]}

  Fields:
    customFields: Custom fields.
    field: The field value to attribute.
    metric: The metric to update.
  """

  customFields = _messages.MessageField('CustomField', 1, repeated=True)
  field = _messages.StringField(2)
  metric = _messages.StringField(3)


class CustomField(_messages.Message):
  r"""Custom fields. These can be used to create a counter with arbitrary
  field/value pairs. See: go/rpcsp-custom-fields.

  Fields:
    name: Name is the field name.
    value: Value is the field value. It is important that in contrast to the
      CounterOptions.field, the value here is a constant that is not derived
      from the IAMContext.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class DataAccessOptions(_messages.Message):
  r"""Write a Data Access (Gin) log

  Enums:
    LogModeValueValuesEnum:

  Fields:
    logMode: A LogModeValueValuesEnum attribute.
  """

  class LogModeValueValuesEnum(_messages.Enum):
    r"""LogModeValueValuesEnum enum type.

    Values:
      LOG_MODE_UNSPECIFIED: Client is not required to write a partial Gin log
        immediately after the authorization check. If client chooses to write
        one and it fails, client may either fail open (allow the operation to
        continue) or fail closed (handle as a DENY outcome).
      LOG_FAIL_CLOSED: The application's operation in the context of which
        this authorization check is being made may only be performed if it is
        successfully logged to Gin. For instance, the authorization library
        may satisfy this obligation by emitting a partial log entry at
        authorization check time and only returning ALLOW to the application
        if it succeeds. If a matching Rule has this directive, but the client
        has not indicated that it will honor such requirements, then the IAM
        check will result in authorization failure by setting
        CheckPolicyResponse.success=false.
    """
    LOG_MODE_UNSPECIFIED = 0
    LOG_FAIL_CLOSED = 1

  logMode = _messages.EnumField('LogModeValueValuesEnum', 1)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class Endpoint(_messages.Message):
  r"""An individual endpoint that provides a service. The service must already
  exist to create an endpoint.

  Messages:
    MetadataValue: Optional. Metadata for the endpoint. This data can be
      consumed by service clients. Restrictions: * The entire metadata
      dictionary may contain up to 512 characters, spread accoss all key-value
      pairs. Metadata that goes beyond this limit are rejected * Valid
      metadata keys have two segments: an optional prefix and name, separated
      by a slash (/). The name segment is required and must be 63 characters
      or less, beginning and ending with an alphanumeric character
      ([a-z0-9A-Z]) with dashes (-), underscores (_), dots (.), and
      alphanumerics between. The prefix is optional. If specified, the prefix
      must be a DNS subdomain: a series of DNS labels separated by dots (.),
      not longer than 253 characters in total, followed by a slash (/).
      Metadata that fails to meet these requirements are rejected Note: This
      field is equivalent to the `annotations` field in the v1 API. They have
      the same syntax and read/write to the same location in Service
      Directory.

  Fields:
    address: Optional. An IPv4 or IPv6 address. Service Directory rejects bad
      addresses like: * `8.8.8` * `*******:53` * `test:bad:address` * `[::1]`
      * `[::1]:8080` Limited to 45 characters.
    attributes: Optional. Attributes associated with this Endpoint.
    createTime: Output only. The timestamp when the endpoint was created.
    fqdn: Optional. A fully qualified domain name address. Service Directory
      rejects special characters outside of letters, numbers, dots, and
      hyphens. FQDN is formatted as [hostname].[domain].[tld] eg:
      mail.google.com
    metadata: Optional. Metadata for the endpoint. This data can be consumed
      by service clients. Restrictions: * The entire metadata dictionary may
      contain up to 512 characters, spread accoss all key-value pairs.
      Metadata that goes beyond this limit are rejected * Valid metadata keys
      have two segments: an optional prefix and name, separated by a slash
      (/). The name segment is required and must be 63 characters or less,
      beginning and ending with an alphanumeric character ([a-z0-9A-Z]) with
      dashes (-), underscores (_), dots (.), and alphanumerics between. The
      prefix is optional. If specified, the prefix must be a DNS subdomain: a
      series of DNS labels separated by dots (.), not longer than 253
      characters in total, followed by a slash (/). Metadata that fails to
      meet these requirements are rejected Note: This field is equivalent to
      the `annotations` field in the v1 API. They have the same syntax and
      read/write to the same location in Service Directory.
    name: Immutable. The resource name for the endpoint in the format
      `projects/*/locations/*/namespaces/*/services/*/endpoints/*`.
    network: Immutable. The Google Compute Engine network (VPC) of the
      endpoint in the format `projects//locations/global/networks/*`. The
      project must be specified by project number (project id is rejected).
      Incorrectly formatted networks are rejected, but no other validation is
      performed on this field (ex. network or project existence, reachability,
      or permissions).
    port: Optional. Service Directory rejects values outside of `[0, 65535]`.
    uid: Output only. A globally unique identifier (in UUID4 format) for this
      endpoint.
    updateTime: Output only. The timestamp when the endpoint was last updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Optional. Metadata for the endpoint. This data can be consumed by
    service clients. Restrictions: * The entire metadata dictionary may
    contain up to 512 characters, spread accoss all key-value pairs. Metadata
    that goes beyond this limit are rejected * Valid metadata keys have two
    segments: an optional prefix and name, separated by a slash (/). The name
    segment is required and must be 63 characters or less, beginning and
    ending with an alphanumeric character ([a-z0-9A-Z]) with dashes (-),
    underscores (_), dots (.), and alphanumerics between. The prefix is
    optional. If specified, the prefix must be a DNS subdomain: a series of
    DNS labels separated by dots (.), not longer than 253 characters in total,
    followed by a slash (/). Metadata that fails to meet these requirements
    are rejected Note: This field is equivalent to the `annotations` field in
    the v1 API. They have the same syntax and read/write to the same location
    in Service Directory.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  address = _messages.StringField(1)
  attributes = _messages.MessageField('EndpointAttributes', 2)
  createTime = _messages.StringField(3)
  fqdn = _messages.StringField(4)
  metadata = _messages.MessageField('MetadataValue', 5)
  name = _messages.StringField(6)
  network = _messages.StringField(7)
  port = _messages.IntegerField(8, variant=_messages.Variant.INT32)
  uid = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class EndpointAttributes(_messages.Message):
  r"""Attributes associated with endpoints.

  Enums:
    KubernetesResourceTypeValueValuesEnum: Optional. Kubernetes resource-type
      associated with this endpoint

  Fields:
    gcpFleetMembership: Optional. Membership URI (scheme-less URI) for
      resources registered to Google Cloud Fleet. Currently populated only for
      kubernetes resources. Sample URI: `//gkehub.googleapis.com/projects/my-
      project/locations/global/memberships/my-membership`
    kubernetesResourceType: Optional. Kubernetes resource-type associated with
      this endpoint
    managedRegistration: Output only. Indicates whether a GCP product or
      service manages this resource. When a resource is fully managed by
      another GCP product or system the information in Service Directory is
      read-only. The source of truth is the relevant GCP product or system
      which is managing the resource. The Service Directory resource will be
      updated or deleted as appropriate to reflect the state of the underlying
      `origin_resource`.
    originResource: Optional. Reference to the underlying resource that this
      endpoint represents. This should be the full name of the resource that
      this endpoint was created from.
    region: Optional. Region of the underlying resource, or "global" for
      global resources.
    zone: Optional. GCP zone of the underlying resource. Meant to be populated
      only for zonal resources, left unset for others.
  """

  class KubernetesResourceTypeValueValuesEnum(_messages.Enum):
    r"""Optional. Kubernetes resource-type associated with this endpoint

    Values:
      KUBERNETES_RESOURCE_TYPE_UNSPECIFIED: Not a Kubernetes workload.
      KUBERNETES_RESOURCE_TYPE_CLUSTER_IP: Cluster IP service related resource
      KUBERNETES_RESOURCE_TYPE_NODE_PORT: Node port service related resource
      KUBERNETES_RESOURCE_TYPE_LOAD_BALANCER: Load balancer service related
        resource
      KUBERNETES_RESOURCE_TYPE_HEADLESS: Headless service related resource
    """
    KUBERNETES_RESOURCE_TYPE_UNSPECIFIED = 0
    KUBERNETES_RESOURCE_TYPE_CLUSTER_IP = 1
    KUBERNETES_RESOURCE_TYPE_NODE_PORT = 2
    KUBERNETES_RESOURCE_TYPE_LOAD_BALANCER = 3
    KUBERNETES_RESOURCE_TYPE_HEADLESS = 4

  gcpFleetMembership = _messages.StringField(1)
  kubernetesResourceType = _messages.EnumField('KubernetesResourceTypeValueValuesEnum', 2)
  managedRegistration = _messages.BooleanField(3)
  originResource = _messages.StringField(4)
  region = _messages.StringField(5)
  zone = _messages.StringField(6)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class GetIamPolicyRequest(_messages.Message):
  r"""Request message for `GetIamPolicy` method.

  Fields:
    options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
      `GetIamPolicy`.
  """

  options = _messages.MessageField('GetPolicyOptions', 1)


class GetPolicyOptions(_messages.Message):
  r"""Encapsulates settings provided to GetIamPolicy.

  Fields:
    requestedPolicyVersion: Optional. The maximum policy version that will be
      used to format the policy. Valid values are 0, 1, and 3. Requests
      specifying an invalid value will be rejected. Requests for policies with
      any conditional role bindings must specify version 3. Policies with no
      conditional role bindings may specify any valid value or leave the field
      unset. The policy in the response might use the policy version that you
      specified, or it might use a lower policy version. For example, if you
      specify version 3, but the policy has no conditional role bindings, the
      response uses version 1. To learn which resources support conditions in
      their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class InternalAttributes(_messages.Message):
  r"""Attributes associated with workload.

  Enums:
    ManagerTypeValueValuesEnum: Output only. The GCP resource/product
      responsible for this workload.

  Fields:
    managedRegistration: Output only. Defines if Workload is managed.
    managerType: Output only. The GCP resource/product responsible for this
      workload.
  """

  class ManagerTypeValueValuesEnum(_messages.Enum):
    r"""Output only. The GCP resource/product responsible for this workload.

    Values:
      TYPE_UNSPECIFIED: Default. Should not be used.
      GKE_HUB: Resource managed by GKE Hub.
      BACKEND_SERVICE: Resource managed by Arcus, Backend Service
    """
    TYPE_UNSPECIFIED = 0
    GKE_HUB = 1
    BACKEND_SERVICE = 2

  managedRegistration = _messages.BooleanField(1)
  managerType = _messages.EnumField('ManagerTypeValueValuesEnum', 2)


class ListEndpointsResponse(_messages.Message):
  r"""The response message for RegistrationService.ListEndpoints.

  Fields:
    endpoints: The list of endpoints.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  endpoints = _messages.MessageField('Endpoint', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListNamespacesResponse(_messages.Message):
  r"""The response message for RegistrationService.ListNamespaces.

  Fields:
    namespaces: The list of namespaces.
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
  """

  namespaces = _messages.MessageField('Namespace', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListServicesResponse(_messages.Message):
  r"""The response message for RegistrationService.ListServices.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    services: The list of services.
  """

  nextPageToken = _messages.StringField(1)
  services = _messages.MessageField('Service', 2, repeated=True)


class ListWorkloadsResponse(_messages.Message):
  r"""The response message for RegistrationService.ListWorkloads.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    workloads: The list of workloads.
  """

  nextPageToken = _messages.StringField(1)
  workloads = _messages.MessageField('Workload', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LogConfig(_messages.Message):
  r"""Specifies what kind of log the caller must write

  Fields:
    cloudAudit: Cloud audit options.
    counter: Counter options.
    dataAccess: Data access options.
  """

  cloudAudit = _messages.MessageField('CloudAuditOptions', 1)
  counter = _messages.MessageField('CounterOptions', 2)
  dataAccess = _messages.MessageField('DataAccessOptions', 3)


class Namespace(_messages.Message):
  r"""A container for services. Namespaces allow administrators to group
  services together and define permissions for a collection of services.

  Messages:
    LabelsValue: Optional. Resource labels associated with this namespace. No
      more than 64 user labels can be associated with a given resource. Label
      keys and values can be no longer than 63 characters.

  Fields:
    attributes: Optional. Attributes associated with this Namespace.
    createTime: Output only. The timestamp when the namespace was created.
    labels: Optional. Resource labels associated with this namespace. No more
      than 64 user labels can be associated with a given resource. Label keys
      and values can be no longer than 63 characters.
    name: Immutable. The resource name for the namespace in the format
      `projects/*/locations/*/namespaces/*`.
    uid: Output only. A globally unique identifier (in UUID4 format) for this
      namespace.
    updateTime: Output only. The timestamp when the namespace was last
      updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Resource labels associated with this namespace. No more than
    64 user labels can be associated with a given resource. Label keys and
    values can be no longer than 63 characters.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributes = _messages.MessageField('NamespaceAttributes', 1)
  createTime = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  uid = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class NamespaceAttributes(_messages.Message):
  r"""Attributes associated with Namespace.

  Fields:
    cloudDnsManagedZones: Output only. List of Cloud DNS ManagedZones that
      this namespace is associated with.
    managedRegistration: Output only. Indicates whether a GCP product or
      service manages this resource. When a resource is fully managed by
      another GCP product or system the information in Service Directory is
      read-only. The source of truth is the relevant GCP product or system
      which is managing the resource. The Service Directory resource will be
      updated or deleted as appropriate to reflect the state of the underlying
      `origin_resource`. Note: The `origin_resource` can be found in the
      endpoint(s) associated with service(s) associated with this namespace.
  """

  cloudDnsManagedZones = _messages.StringField(1, repeated=True)
  managedRegistration = _messages.BooleanField(2)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    rules: If more than one rule is specified, the rules are applied in the
      following manner: - All matching LOG rules are always applied. - If any
      DENY/DENY_WITH_LOG rule matches, permission is denied. Logging will be
      applied if one or more matching rule requires logging. - Otherwise, if
      any ALLOW/ALLOW_WITH_LOG rule matches, permission is granted. Logging
      will be applied if one or more matching rule requires logging. -
      Otherwise, if no rule applies, permission is denied.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  rules = _messages.MessageField('Rule', 4, repeated=True)
  version = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class ResolveServiceRequest(_messages.Message):
  r"""The request message for LookupService.ResolveService. Looks up a service
  by its name, returns the service and its endpoints.

  Fields:
    endpointFilter: Optional. The filter applied to the endpoints of the
      resolved service. General `filter` string syntax: ` ()` * `` can be
      `name`, `address`, `port`, or `metadata.` for map field * `` can be `<`,
      `>`, `<=`, `>=`, `!=`, `=`, `:`. Of which `:` means `HAS`, and is
      roughly the same as `=` * `` must be the same data type as field * ``
      can be `AND`, `OR`, `NOT` Examples of valid filters: * `metadata.owner`
      returns endpoints that have a annotation with the key `owner`, this is
      the same as `metadata:owner` * `metadata.protocol=gRPC` returns
      endpoints that have key/value `protocol=gRPC` * `address=*************`
      returns endpoints that have this address * `port>8080` returns endpoints
      that have port number larger than 8080 * `name>projects/my-
      project/locations/us-east1/namespaces/my-namespace/services/my-
      service/endpoints/endpoint-c` returns endpoints that have name that is
      alphabetically later than the string, so "endpoint-e" is returned but
      "endpoint-a" is not * `name=projects/my-project/locations/us-
      central1/namespaces/my-namespace/services/my-service/endpoints/ep-1`
      returns the endpoint that has an endpoint_id equal to `ep-1` *
      `metadata.owner!=sd AND metadata.foo=bar` returns endpoints that have
      `owner` in annotation key but value is not `sd` AND have key/value
      `foo=bar` * `doesnotexist.foo=bar` returns an empty list. Note that
      endpoint doesn't have a field called "doesnotexist". Since the filter
      does not match any endpoint, it returns no results For more information
      about filtering, see [API Filtering](https://aip.dev/160).
    maxEndpoints: Optional. The maximum number of endpoints to return.
      Defaults to 25. Maximum is 100. If a value less than one is specified,
      the Default is used. If a value greater than the Maximum is specified,
      the Maximum is used.
  """

  endpointFilter = _messages.StringField(1)
  maxEndpoints = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class ResolveServiceResponse(_messages.Message):
  r"""The response message for LookupService.ResolveService.

  Fields:
    service: A Service attribute.
  """

  service = _messages.MessageField('Service', 1)


class Rule(_messages.Message):
  r"""A rule to be applied in a Policy.

  Enums:
    ActionValueValuesEnum: Required

  Fields:
    action: Required
    conditions: Additional restrictions that must be met. All conditions must
      pass for the rule to match.
    description: Human-readable description of the rule.
    in_: If one or more 'in' clauses are specified, the rule matches if the
      PRINCIPAL/AUTHORITY_SELECTOR is in at least one of these entries.
    logConfig: The config returned to callers of CheckPolicy for any entries
      that match the LOG action.
    notIn: If one or more 'not_in' clauses are specified, the rule matches if
      the PRINCIPAL/AUTHORITY_SELECTOR is in none of the entries. The format
      for in and not_in entries can be found at in the Local IAM documentation
      (see go/local-iam#features).
    permissions: A permission is a string of form '..' (e.g.,
      'storage.buckets.list'). A value of '*' matches all permissions, and a
      verb part of '*' (e.g., 'storage.buckets.*') matches all verbs.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required

    Values:
      NO_ACTION: Default no action.
      ALLOW: Matching 'Entries' grant access.
      ALLOW_WITH_LOG: Matching 'Entries' grant access and the caller promises
        to log the request per the returned log_configs.
      DENY: Matching 'Entries' deny access.
      DENY_WITH_LOG: Matching 'Entries' deny access and the caller promises to
        log the request per the returned log_configs.
      LOG: Matching 'Entries' tell IAM.Check callers to generate logs.
    """
    NO_ACTION = 0
    ALLOW = 1
    ALLOW_WITH_LOG = 2
    DENY = 3
    DENY_WITH_LOG = 4
    LOG = 5

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  conditions = _messages.MessageField('Condition', 2, repeated=True)
  description = _messages.StringField(3)
  in_ = _messages.StringField(4, repeated=True)
  logConfig = _messages.MessageField('LogConfig', 5, repeated=True)
  notIn = _messages.StringField(6, repeated=True)
  permissions = _messages.StringField(7, repeated=True)


class Service(_messages.Message):
  r"""An individual service. A service contains a name and optional metadata.
  A service must exist before endpoints can be added to it.

  Enums:
    CriticalityValueValuesEnum: Optional. Criticality level of this service.

  Messages:
    MetadataValue: Optional. Metadata for the service. This data can be
      consumed by service clients. Restrictions: * The entire metadata
      dictionary may contain up to 2000 characters, spread accoss all key-
      value pairs. Metadata that goes beyond this limit are rejected * Valid
      metadata keys have two segments: an optional prefix and name, separated
      by a slash (/). The name segment is required and must be 63 characters
      or less, beginning and ending with an alphanumeric character
      ([a-z0-9A-Z]) with dashes (-), underscores (_), dots (.), and
      alphanumerics between. The prefix is optional. If specified, the prefix
      must be a DNS subdomain: a series of DNS labels separated by dots (.),
      not longer than 253 characters in total, followed by a slash (/).
      Metadata that fails to meet these requirements are rejected Note: This
      field is equivalent to the `annotations` field in the v1 API. They have
      the same syntax and read/write to the same location in Service
      Directory.

  Fields:
    assets: Output only. Assets that are part of this service (output only).
      Example for Google Compute Engine assets: [
      //compute.googleapis.com/projects/1234/regions/us-
      east1/forwardingRules/fr1 ]
    attributes: Optional. Attributes associated with this Service.
    createTime: Output only. The timestamp when the service was created.
    criteria: Optional. Criteria to apply to identify components belonging to
      this service. Only one criteria allowed. Eg. create service representing
      forwarding rule fr1: [ { key: FORWARDING_RULE, value:
      '//compute.googleapis.com/projects/123/zones/us-
      east1-c/forwardingRules/fr1' } ]
    criticality: Optional. Criticality level of this service.
    description: Optional. Human readable explanation of the service and what
      it does.
    displayName: Optional. User-friendly display name for service.
    endpoints: Output only. Endpoints associated with this service. Returned
      on LookupService.ResolveService. Control plane clients should use
      RegistrationService.ListEndpoints.
    environment: Optional. User-friendly string that indicates the environment
      for this service.
    hostname: Optional. Hostname. Service consumer may use for: 1) HTTP
      parameter for Host (HTTP/1.1) or Authority (HTTP/2, HTTP/3) 2) TLS SNI
      Hostname parameter (most commonly used for HTTPS) 3) TLS Hostname
      Authorization against the x509 SAN DNS entries (necessary for HTTPS)
      Example: `service.example.com` Limits: Field limited to 255 ASCII
      characters per https://www.ietf.org/rfc/rfc1035.txt
    metadata: Optional. Metadata for the service. This data can be consumed by
      service clients. Restrictions: * The entire metadata dictionary may
      contain up to 2000 characters, spread accoss all key-value pairs.
      Metadata that goes beyond this limit are rejected * Valid metadata keys
      have two segments: an optional prefix and name, separated by a slash
      (/). The name segment is required and must be 63 characters or less,
      beginning and ending with an alphanumeric character ([a-z0-9A-Z]) with
      dashes (-), underscores (_), dots (.), and alphanumerics between. The
      prefix is optional. If specified, the prefix must be a DNS subdomain: a
      series of DNS labels separated by dots (.), not longer than 253
      characters in total, followed by a slash (/). Metadata that fails to
      meet these requirements are rejected Note: This field is equivalent to
      the `annotations` field in the v1 API. They have the same syntax and
      read/write to the same location in Service Directory.
    name: Immutable. The resource name for the service in the format
      `projects/*/locations/*/namespaces/*/services/*`.
    owners: Optional. List of contacts for this service. This can include
      application engineers, architects, SRE, ops team, business owners etc.
    serviceIdentities: Optional. Authorized Service Identities. If provided,
      the consumer may use this information to determine whether the service
      provider is authorized. Examples: `spiffe_id:spiffe://example.org/my-
      service` `service_account:<EMAIL>` Limits:
      service_identities list is limited to 10 items.
    uid: Output only. A globally unique identifier (in UUID4 format) for this
      service.
    updateTime: Output only. The timestamp when the service was last updated.
      Note: endpoints being created/deleted/updated within the service are not
      considered service updates for the purpose of this timestamp.
  """

  class CriticalityValueValuesEnum(_messages.Enum):
    r"""Optional. Criticality level of this service.

    Values:
      CRITICALITY_UNSPECIFIED: Default. Resource is not supported and is not
        expected to provide any guarantees.
      MISSION_CRITICAL: The resource is mission-critical to the organization.
      HIGH: The resource may not directly affect the mission of a specific
        unit, but is of high importance to the organization.
      MEDIUM: The resource is of medium importance to the organization.
      LOW: The resource is of low importance to the organization.
    """
    CRITICALITY_UNSPECIFIED = 0
    MISSION_CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Optional. Metadata for the service. This data can be consumed by
    service clients. Restrictions: * The entire metadata dictionary may
    contain up to 2000 characters, spread accoss all key-value pairs. Metadata
    that goes beyond this limit are rejected * Valid metadata keys have two
    segments: an optional prefix and name, separated by a slash (/). The name
    segment is required and must be 63 characters or less, beginning and
    ending with an alphanumeric character ([a-z0-9A-Z]) with dashes (-),
    underscores (_), dots (.), and alphanumerics between. The prefix is
    optional. If specified, the prefix must be a DNS subdomain: a series of
    DNS labels separated by dots (.), not longer than 253 characters in total,
    followed by a slash (/). Metadata that fails to meet these requirements
    are rejected Note: This field is equivalent to the `annotations` field in
    the v1 API. They have the same syntax and read/write to the same location
    in Service Directory.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  assets = _messages.MessageField('Asset', 1, repeated=True)
  attributes = _messages.MessageField('Attributes', 2)
  createTime = _messages.StringField(3)
  criteria = _messages.MessageField('ServiceCriteria', 4, repeated=True)
  criticality = _messages.EnumField('CriticalityValueValuesEnum', 5)
  description = _messages.StringField(6)
  displayName = _messages.StringField(7)
  endpoints = _messages.MessageField('Endpoint', 8, repeated=True)
  environment = _messages.StringField(9)
  hostname = _messages.StringField(10)
  metadata = _messages.MessageField('MetadataValue', 11)
  name = _messages.StringField(12)
  owners = _messages.MessageField('ContactInfo', 13, repeated=True)
  serviceIdentities = _messages.MessageField('ServiceIdentity', 14, repeated=True)
  uid = _messages.StringField(15)
  updateTime = _messages.StringField(16)


class ServiceCriteria(_messages.Message):
  r"""Criteria to apply to identify components belonging to this service.

  Enums:
    KeyValueValuesEnum: Required. Key for criteria.

  Fields:
    key: Required. Key for criteria.
    value: Required. Criteria value to match against for the associated
      criteria key. Example: //compute.googleapis.com/projects/123/regions/us-
      west1/forwardingRules/fr1
  """

  class KeyValueValuesEnum(_messages.Enum):
    r"""Required. Key for criteria.

    Values:
      CRITERIA_KEY_UNSPECIFIED: Default. Criteria.key is unspecified.
      FORWARDING_RULE: Criteria type of Forwarding Rule. Example value:
        //compute.googleapis.com/projects/123/regions/us-
        west1/forwardingRules/fr1
      GKE_GATEWAY: Criteria type of GKE Gateway. Example value:
        //container.googleapis.com/projects/123/zones/us-
        central1-a/clusters/my-cluster/k8s/apis/gateway.networking.k8s.io/v1al
        pha2/namespaces/default/gateways/my-gateway
      APP_HUB_SERVICE: Criteria type of App Hub service. Example value:
        //servicedirectory.googleapis.com/projects/1234/locations/us-
        west1/namespaces/my-ns/services/gshoe-service
      APP_HUB_WORKLOAD: Criteria type of App Hub workload. Example value:
        //servicedirectory.googleapis.com/projects/1234/locations/us-
        west1/namespaces/my-ns/workloads/gshoe-workload
    """
    CRITERIA_KEY_UNSPECIFIED = 0
    FORWARDING_RULE = 1
    GKE_GATEWAY = 2
    APP_HUB_SERVICE = 3
    APP_HUB_WORKLOAD = 4

  key = _messages.EnumField('KeyValueValuesEnum', 1)
  value = _messages.StringField(2)


class ServiceIdentity(_messages.Message):
  r"""Specifies the Service Identity of the authorized server.

  Fields:
    serviceAccount: service_account: gcp service account, usable with ALTS.
      Example: `<EMAIL>` Limits: Limited to 320
      characters. https://tools.ietf.org/html/rfc3696
    spiffeId: spiffe_id as works with x509 certs with Subject Alternative Name
      (SAN) specified as uniformResourceIdentifier:*spiffe_id* Example:
      `spiffe://example.org/my-service` Limits: 2048 ASCII Characters
      https://github.com/spiffe/spiffe/blob/master/standards/SPIFFE-
      ID.md#23-maximum-spiffe-id-length
  """

  serviceAccount = _messages.StringField(1)
  spiffeId = _messages.StringField(2)


class ServicedirectoryProjectsLocationsGetRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class ServicedirectoryProjectsLocationsListRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    includeUnrevealedLocations: If true, the returned list will include
      locations which are not yet revealed.
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  includeUnrevealedLocations = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class ServicedirectoryProjectsLocationsNamespacesCreateRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesCreateRequest object.

  Fields:
    namespace: A Namespace resource to be passed as the request body.
    namespaceId: Required. The Resource ID must be 1-63 characters long, and
      comply with RFC1035. Specifically, the name must be 1-63 characters long
      and match the regular expression `[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?`
      which means the first character must be a lowercase letter, and all
      following characters must be a dash, lowercase letter, or digit, except
      the last character, which cannot be a dash.
    parent: Required. The resource name of the project and location the
      namespace will be created in.
  """

  namespace = _messages.MessageField('Namespace', 1)
  namespaceId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ServicedirectoryProjectsLocationsNamespacesDeleteRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesDeleteRequest object.

  Fields:
    name: Required. The name of the namespace to delete.
  """

  name = _messages.StringField(1, required=True)


class ServicedirectoryProjectsLocationsNamespacesGetIamPolicyRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesGetIamPolicyRequest object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class ServicedirectoryProjectsLocationsNamespacesGetRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesGetRequest object.

  Fields:
    name: Required. The name of the namespace to retrieve.
  """

  name = _messages.StringField(1, required=True)


class ServicedirectoryProjectsLocationsNamespacesListRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesListRequest object.

  Fields:
    filter: Optional. The filter to list results by. General `filter` string
      syntax: ` ()` * `` can be `name`, `labels.` for map field, or
      `attributes.` for attributes field * `` can be `<`, `>`, `<=`, `>=`,
      `!=`, `=`, `:`. Of which `:` means `HAS`, and is roughly the same as `=`
      * `` must be the same data type as field * `` can be `AND`, `OR`, `NOT`
      Examples of valid filters: * `labels.owner` returns namespaces that have
      a label with the key `owner`, this is the same as `labels:owner` *
      `labels.owner=sd` returns namespaces that have key/value `owner=sd` *
      `name>projects/my-project/locations/us-east1/namespaces/namespace-c`
      returns namespaces that have name that is alphabetically later than the
      string, so "namespace-e" is returned but "namespace-a" is not *
      `labels.owner!=sd AND labels.foo=bar` returns namespaces that have
      `owner` in label key but value is not `sd` AND have key/value `foo=bar`
      * `doesnotexist.foo=bar` returns an empty list. Note that namespace
      doesn't have a field called "doesnotexist". Since the filter does not
      match any namespaces, it returns no results *
      `attributes.managed_registration=true` returns namespaces that are
      managed by a GCP product or service For more information about
      filtering, see [API Filtering](https://aip.dev/160).
    orderBy: Optional. The order to list results by. General `order_by` string
      syntax: ` () (,)` * `` allows value: `name` * `` ascending or descending
      order by ``. If this is left blank, `asc` is used Note that an empty
      `order_by` string results in default order, which is order by `name` in
      ascending order.
    pageSize: Optional. The maximum number of items to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The resource name of the project and location whose
      namespaces you'd like to list.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ServicedirectoryProjectsLocationsNamespacesPatchRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesPatchRequest object.

  Fields:
    name: Immutable. The resource name for the namespace in the format
      `projects/*/locations/*/namespaces/*`.
    namespace: A Namespace resource to be passed as the request body.
    updateMask: Required. List of fields to be updated in this request.
  """

  name = _messages.StringField(1, required=True)
  namespace = _messages.MessageField('Namespace', 2)
  updateMask = _messages.StringField(3)


class ServicedirectoryProjectsLocationsNamespacesServicesCreateRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesCreateRequest
  object.

  Fields:
    parent: Required. The resource name of the namespace this service will
      belong to.
    service: A Service resource to be passed as the request body.
    serviceId: Required. The Resource ID must be 1-63 characters long, and
      comply with RFC1035. Specifically, the name must be 1-63 characters long
      and match the regular expression `[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?`
      which means the first character must be a lowercase letter, and all
      following characters must be a dash, lowercase letter, or digit, except
      the last character, which cannot be a dash.
  """

  parent = _messages.StringField(1, required=True)
  service = _messages.MessageField('Service', 2)
  serviceId = _messages.StringField(3)


class ServicedirectoryProjectsLocationsNamespacesServicesDeleteRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesDeleteRequest
  object.

  Fields:
    name: Required. The name of the service to delete.
  """

  name = _messages.StringField(1, required=True)


class ServicedirectoryProjectsLocationsNamespacesServicesEndpointsCreateRequest(_messages.Message):
  r"""A
  ServicedirectoryProjectsLocationsNamespacesServicesEndpointsCreateRequest
  object.

  Fields:
    endpoint: A Endpoint resource to be passed as the request body.
    endpointId: Required. The Resource ID must be 1-63 characters long, and
      comply with RFC1035. Specifically, the name must be 1-63 characters long
      and match the regular expression `[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?`
      which means the first character must be a lowercase letter, and all
      following characters must be a dash, lowercase letter, or digit, except
      the last character, which cannot be a dash.
    parent: Required. The resource name of the service that this endpoint
      provides.
  """

  endpoint = _messages.MessageField('Endpoint', 1)
  endpointId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class ServicedirectoryProjectsLocationsNamespacesServicesEndpointsDeleteRequest(_messages.Message):
  r"""A
  ServicedirectoryProjectsLocationsNamespacesServicesEndpointsDeleteRequest
  object.

  Fields:
    name: Required. The name of the endpoint to delete.
  """

  name = _messages.StringField(1, required=True)


class ServicedirectoryProjectsLocationsNamespacesServicesEndpointsGetRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesEndpointsGetRequest
  object.

  Fields:
    name: Required. The name of the endpoint to get.
  """

  name = _messages.StringField(1, required=True)


class ServicedirectoryProjectsLocationsNamespacesServicesEndpointsListRequest(_messages.Message):
  r"""A
  ServicedirectoryProjectsLocationsNamespacesServicesEndpointsListRequest
  object.

  Fields:
    filter: Optional. The filter to list results by. General `filter` string
      syntax: ` ()` * `` can be `name`, `address`, `port`, `metadata.` for map
      field, or `attributes.` for attributes field * `` can be `<`, `>`, `<=`,
      `>=`, `!=`, `=`, `:`. Of which `:` means `HAS`, and is roughly the same
      as `=` * `` must be the same data type as field * `` can be `AND`, `OR`,
      `NOT` Examples of valid filters: * `metadata.owner` returns endpoints
      that have a metadata with the key `owner`, this is the same as
      `metadata:owner` * `metadata.protocol=gRPC` returns endpoints that have
      key/value `protocol=gRPC` * `address=*************` returns endpoints
      that have this address * `port>8080` returns endpoints that have port
      number larger than 8080 * `name>projects/my-project/locations/us-
      east1/namespaces/my-namespace/services/my-service/endpoints/endpoint-c`
      returns endpoints that have name that is alphabetically later than the
      string, so "endpoint-e" is returned but "endpoint-a" is not *
      `metadata.owner!=sd AND metadata.foo=bar` returns endpoints that have
      `owner` in metadata key but value is not `sd` AND have key/value
      `foo=bar` * `doesnotexist.foo=bar` returns an empty list. Note that
      endpoint doesn't have a field called "doesnotexist". Since the filter
      does not match any endpoints, it returns no results *
      `attributes.kubernetes_resource_type=KUBERNETES_RESOURCE_TYPE_CLUSTER_
      IP` returns endpoints with the corresponding kubernetes_resource_type
      For more information about filtering, see [API
      Filtering](https://aip.dev/160).
    orderBy: Optional. The order to list results by. General `order_by` string
      syntax: ` () (,)` * `` allows values: `name`, `address`, `port` * ``
      ascending or descending order by ``. If this is left blank, `asc` is
      used Note that an empty `order_by` string results in default order,
      which is order by `name` in ascending order.
    pageSize: Optional. The maximum number of items to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The resource name of the service whose endpoints you'd
      like to list.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ServicedirectoryProjectsLocationsNamespacesServicesEndpointsPatchRequest(_messages.Message):
  r"""A
  ServicedirectoryProjectsLocationsNamespacesServicesEndpointsPatchRequest
  object.

  Fields:
    endpoint: A Endpoint resource to be passed as the request body.
    name: Immutable. The resource name for the endpoint in the format
      `projects/*/locations/*/namespaces/*/services/*/endpoints/*`.
    updateMask: Required. List of fields to be updated in this request.
  """

  endpoint = _messages.MessageField('Endpoint', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class ServicedirectoryProjectsLocationsNamespacesServicesGetIamPolicyRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesGetIamPolicyRequest
  object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class ServicedirectoryProjectsLocationsNamespacesServicesGetRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesGetRequest object.

  Fields:
    name: Required. The name of the service to get.
  """

  name = _messages.StringField(1, required=True)


class ServicedirectoryProjectsLocationsNamespacesServicesListRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesListRequest object.

  Fields:
    filter: Optional. The filter to list results by. General `filter` string
      syntax: ` ()` * `` can be `name` or `metadata.` for map field * `` can
      be `<`, `>`, `<=`, `>=`, `!=`, `=`, `:`. Of which `:` means `HAS`, and
      is roughly the same as `=` * `` must be the same data type as field * ``
      can be `AND`, `OR`, `NOT` Examples of valid filters: * `metadata.owner`
      returns services that have a metadata with the key `owner`, this is the
      same as `metadata:owner` * `metadata.protocol=gRPC` returns services
      that have key/value `protocol=gRPC` * `name>projects/my-
      project/locations/us-east1/namespaces/my-namespace/services/service-c`
      returns services that have name that is alphabetically later than the
      string, so "service-e" is returned but "service-a" is not *
      `metadata.owner!=sd AND metadata.foo=bar` returns services that have
      `owner` in metadata key but value is not `sd` AND have key/value
      `foo=bar` * `doesnotexist.foo=bar` returns an empty list. Note that
      service doesn't have a field called "doesnotexist". Since the filter
      does not match any services, it returns no results *
      `attributes.managed_registration=true` returns services that are managed
      by a GCP product or service For more information about filtering, see
      [API Filtering](https://aip.dev/160).
    orderBy: Optional. The order to list results by. General `order_by` string
      syntax: ` () (,)` * `` allows value: `name` * `` ascending or descending
      order by ``. If this is left blank, `asc` is used Note that an empty
      `order_by` string results in default order, which is order by `name` in
      ascending order.
    pageSize: Optional. The maximum number of items to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The resource name of the namespace whose services you'd
      like to list.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ServicedirectoryProjectsLocationsNamespacesServicesPatchRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesPatchRequest
  object.

  Fields:
    name: Immutable. The resource name for the service in the format
      `projects/*/locations/*/namespaces/*/services/*`.
    service: A Service resource to be passed as the request body.
    updateMask: Required. List of fields to be updated in this request.
  """

  name = _messages.StringField(1, required=True)
  service = _messages.MessageField('Service', 2)
  updateMask = _messages.StringField(3)


class ServicedirectoryProjectsLocationsNamespacesServicesResolveRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesResolveRequest
  object.

  Fields:
    name: Required. The name of the service to resolve.
    resolveServiceRequest: A ResolveServiceRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  resolveServiceRequest = _messages.MessageField('ResolveServiceRequest', 2)


class ServicedirectoryProjectsLocationsNamespacesServicesSetIamPolicyRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesServicesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ServicedirectoryProjectsLocationsNamespacesServicesTestIamPermissionsRequest(_messages.Message):
  r"""A
  ServicedirectoryProjectsLocationsNamespacesServicesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class ServicedirectoryProjectsLocationsNamespacesSetIamPolicyRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ServicedirectoryProjectsLocationsNamespacesTestIamPermissionsRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class ServicedirectoryProjectsLocationsNamespacesWorkloadsCreateRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesWorkloadsCreateRequest
  object.

  Fields:
    parent: Required. The resource name of the namespace this service workload
      will belong to.
    workload: A Workload resource to be passed as the request body.
    workloadId: Required. The Resource ID must be 1-63 characters long, and
      comply with [RFC 1035](https://www.ietf.org/rfc/rfc1035.txt).
      Specifically, the name must be 1-63 characters long and match the
      regular expression `[a-z](?:[-a-z0-9]{0,61}[a-z0-9])?` which means the
      first character must be a lowercase letter, and all following characters
      must be a dash, lowercase letter, or digit, except the last character,
      which cannot be a dash.
  """

  parent = _messages.StringField(1, required=True)
  workload = _messages.MessageField('Workload', 2)
  workloadId = _messages.StringField(3)


class ServicedirectoryProjectsLocationsNamespacesWorkloadsDeleteRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesWorkloadsDeleteRequest
  object.

  Enums:
    ManagerTypeValueValuesEnum: Stores extra information about what Google
      resource is directly responsible for a given Workload resource.

  Fields:
    managerType: Stores extra information about what Google resource is
      directly responsible for a given Workload resource.
    name: Required. The name of the workload to delete.
  """

  class ManagerTypeValueValuesEnum(_messages.Enum):
    r"""Stores extra information about what Google resource is directly
    responsible for a given Workload resource.

    Values:
      TYPE_UNSPECIFIED: Default. Should not be used.
      GKE_HUB: Resource managed by GKE Hub.
      BACKEND_SERVICE: Resource managed by Arcus, Backend Service
    """
    TYPE_UNSPECIFIED = 0
    GKE_HUB = 1
    BACKEND_SERVICE = 2

  managerType = _messages.EnumField('ManagerTypeValueValuesEnum', 1)
  name = _messages.StringField(2, required=True)


class ServicedirectoryProjectsLocationsNamespacesWorkloadsGetIamPolicyRequest(_messages.Message):
  r"""A
  ServicedirectoryProjectsLocationsNamespacesWorkloadsGetIamPolicyRequest
  object.

  Fields:
    getIamPolicyRequest: A GetIamPolicyRequest resource to be passed as the
      request body.
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  getIamPolicyRequest = _messages.MessageField('GetIamPolicyRequest', 1)
  resource = _messages.StringField(2, required=True)


class ServicedirectoryProjectsLocationsNamespacesWorkloadsGetRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesWorkloadsGetRequest object.

  Fields:
    name: Required. The name of the service workload to get.
  """

  name = _messages.StringField(1, required=True)


class ServicedirectoryProjectsLocationsNamespacesWorkloadsListRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesWorkloadsListRequest
  object.

  Fields:
    filter: Optional. The filter to list results by. General `filter` string
      syntax: ` ()` * `` can be any field name on the Workload proto. For
      example: `name`, `create_time`, `annotations.`, or `components` * `` can
      be `<`, `>`, `<=`, `>=`, `!=`, `=`, `:`. Of which `:` means `HAS`, and
      is roughly the same as `=` * `` must be the same data type as field * ``
      can be `AND`, `OR`, `NOT` Examples of valid filters: *
      `annotations.owner` returns workloads that have an annotation with the
      key `owner`, this is the same as `annotations:owner` *
      `components://compute.googleapis.com/projects/1234/zones/us-
      east1-c/instances/mig1\ returns workloads that contain the specified
      component. * `name>projects/my-project/locations/us-east1/namespaces/my-
      namespace/workloads/workload-c` returns workloads that have names that
      are alphabetically later than the string, so "workload-e" is returned
      but "workload-a" is not * `annotations.owner!=sd AND
      annotations.foo=bar` returns workloads that have `owner` in annotation
      key but value is not `sd` AND have key/value `foo=bar` *
      `doesnotexist.foo=bar` returns an empty list. Note that workload doesn't
      have a field called "doesnotexist". Since the filter does not match any
      workloads, it returns no results For more information about filtering,
      see [API Filtering](https://aip.dev/160).
    orderBy: Optional. The order to list results by. General `order_by` string
      syntax: ` () (,)` * `` allows values: `name`, `display_name`,
      `create_time`, `update_time` * `` ascending or descending order by ``.
      If this is left blank, `asc` is used Note that an empty `order_by`
      string results in default order, which is order by `name` in ascending
      order.
    pageSize: Optional. The maximum number of items to return.
    pageToken: Optional. The next_page_token value returned from a previous
      List request, if any.
    parent: Required. The resource name of the namespace whose service
      workloads you'd like to list.
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class ServicedirectoryProjectsLocationsNamespacesWorkloadsPatchRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesWorkloadsPatchRequest
  object.

  Fields:
    name: Immutable. The resource name for the workload in the format
      `projects/*/locations/*/namespaces/*/workloads/*`.
    updateMask: Required. List of fields to be updated in this request.
      Allowable fields: `display_name`, `annotations`. -- Internal
      integrations may update other fields
    workload: A Workload resource to be passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateMask = _messages.StringField(2)
  workload = _messages.MessageField('Workload', 3)


class ServicedirectoryProjectsLocationsNamespacesWorkloadsSetIamPolicyRequest(_messages.Message):
  r"""A
  ServicedirectoryProjectsLocationsNamespacesWorkloadsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class ServicedirectoryProjectsLocationsNamespacesWorkloadsTestIamPermissionsRequest(_messages.Message):
  r"""A ServicedirectoryProjectsLocationsNamespacesWorkloadsTestIamPermissions
  Request object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class Workload(_messages.Message):
  r"""An individual Workload. A logical collection of assets that provide the
  same functionality, with a common set of core attributes, that power
  services in Service Directory and to which policies can be applied.

  Enums:
    CriticalityValueValuesEnum: Optional. Criticality of this workload.

  Fields:
    assets: Output only. Assets that are part of this workload (output only).
      Example for Google Compute Engine assets: [
      //compute.googleapis.com/projects/1234/zones/us-
      east1-c/instanceGroups/mig1,
      //compute.googleapis.com/projects/1234/zones/us-
      east1-a/instanceGroups/mig2 ]
    createTime: Output only. The timestamp when this workload was created in
      Service Directory.
    criteria: Optional. Criteria to apply to identify assets belonging to this
      workload. Used to auto-populate the assets fields. Repeated list of
      tuples of . Multiple values are treated as OR expression, and assets
      matching any of the entries will be selected. Eg. select all resources
      of workloadType behind backend service bs1: [ { key: BACKEND_SERVICE,
      value: '//compute.googleapis.com/projects/123/zones/us-
      east1-c/backendServices/bs1' } ] Eg. select all resources of
      workloadType behind backend services in bs1 or bs2: [ { key:
      BACKEND_SERVICE, value: '//compute.googleapis.com/projects/123/zones/us-
      east1-c/backendServices/bs1' }, { key: BACKEND_SERVICE, value:
      '//compute.googleapis.com/projects/123/regions/us-
      east1/backendServices/bs2' }, ] Eg. select resources explicitly by name
      to be part of the workload: [ { key: INSTANCE_GROUP, value:
      '//compute.googleapis.com/projects/1234/zones/us-
      east1-c/instanceGroups/mig1' }, { key: INSTANCE_GROUP, value:
      '//compute.googleapis.com/projects/1234/regions/us-
      east1/instanceGroups/mig2' } ]
    criticality: Optional. Criticality of this workload.
    description: Optional. Human readable explanation of the workload and what
      it does.
    displayName: Optional. Friendly name. User modifiable.
    environment: Optional. User-friendly string that indicates the environment
      for this workload.
    internalAttributes: Optional. Internal Attributes associated with this
      workload. This field should stay GOOGLE_INTERNAL post launch.
    name: Immutable. The resource name for the workload in the format
      `projects/*/locations/*/namespaces/*/workloads/*`.
    owners: Optional. List of contacts for this workload. This can include
      application engineers, architects, SRE, ops team, business owners etc.
    uid: Output only. A globally unique identifier (in UUID4 format) for this
      workload.
    updateTime: Output only. The timestamp when the workload was last updated
      in Service Directory.
  """

  class CriticalityValueValuesEnum(_messages.Enum):
    r"""Optional. Criticality of this workload.

    Values:
      CRITICALITY_UNSPECIFIED: Default. Resource is not supported and is not
        expected to provide any guarantees.
      MISSION_CRITICAL: The resource is mission-critical to the organization.
      HIGH: The resource may not directly affect the mission of a specific
        unit, but is of high importance to the organization.
      MEDIUM: The resource is of medium importance to the organization.
      LOW: The resource is of low importance to the organization.
    """
    CRITICALITY_UNSPECIFIED = 0
    MISSION_CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4

  assets = _messages.MessageField('Asset', 1, repeated=True)
  createTime = _messages.StringField(2)
  criteria = _messages.MessageField('WorkloadCriteria', 3, repeated=True)
  criticality = _messages.EnumField('CriticalityValueValuesEnum', 4)
  description = _messages.StringField(5)
  displayName = _messages.StringField(6)
  environment = _messages.StringField(7)
  internalAttributes = _messages.MessageField('InternalAttributes', 8)
  name = _messages.StringField(9)
  owners = _messages.MessageField('ContactInfo', 10, repeated=True)
  uid = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class WorkloadCriteria(_messages.Message):
  r"""Criteria to apply to identify assets belonging to this workload. Used to
  auto-populate the assets field.

  Enums:
    KeyValueValuesEnum: Required. Key for criteria.

  Fields:
    key: Required. Key for criteria.
    value: Required. Criteria value to match against for the associated
      criteria key. Example: //compute.googleapis.com/projects/123/regions/us-
      west1/backendServices/bs1
  """

  class KeyValueValuesEnum(_messages.Enum):
    r"""Required. Key for criteria.

    Values:
      CRITERIA_KEY_UNSPECIFIED: Default. Criteria.key is unspecified.
      INSTANCE_GROUP: The criteria key is Instance Group.
      BACKEND_SERVICE: The criteria key is Backend Service.
    """
    CRITERIA_KEY_UNSPECIFIED = 0
    INSTANCE_GROUP = 1
    BACKEND_SERVICE = 2

  key = _messages.EnumField('KeyValueValuesEnum', 1)
  value = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    Rule, 'in_', 'in')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

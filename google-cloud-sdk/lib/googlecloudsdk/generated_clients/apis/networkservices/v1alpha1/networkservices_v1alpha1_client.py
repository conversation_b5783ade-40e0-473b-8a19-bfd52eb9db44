"""Generated client library for networkservices version v1alpha1."""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.py import base_api
from googlecloudsdk.generated_clients.apis.networkservices.v1alpha1 import networkservices_v1alpha1_messages as messages


class NetworkservicesV1alpha1(base_api.BaseApiClient):
  """Generated client library for service networkservices version v1alpha1."""

  MESSAGES_MODULE = messages
  BASE_URL = 'https://networkservices.googleapis.com/'
  MTLS_BASE_URL = 'https://networkservices.mtls.googleapis.com/'

  _PACKAGE = 'networkservices'
  _SCOPES = ['https://www.googleapis.com/auth/cloud-platform']
  _VERSION = 'v1alpha1'
  _CLIENT_ID = 'CLIENT_ID'
  _CLIENT_SECRET = 'CLIENT_SECRET'
  _USER_AGENT = 'google-cloud-sdk'
  _CLIENT_CLASS_NAME = 'NetworkservicesV1alpha1'
  _URL_VERSION = 'v1alpha1'
  _API_KEY = None

  def __init__(self, url='', credentials=None,
               get_credentials=True, http=None, model=None,
               log_request=False, log_response=False,
               credentials_args=None, default_global_params=None,
               additional_http_headers=None, response_encoding=None):
    """Create a new networkservices handle."""
    url = url or self.BASE_URL
    super(NetworkservicesV1alpha1, self).__init__(
        url, credentials=credentials,
        get_credentials=get_credentials, http=http, model=model,
        log_request=log_request, log_response=log_response,
        credentials_args=credentials_args,
        default_global_params=default_global_params,
        additional_http_headers=additional_http_headers,
        response_encoding=response_encoding)
    self.projects_locations_edgeCacheKeysets = self.ProjectsLocationsEdgeCacheKeysetsService(self)
    self.projects_locations_edgeCacheOrigins = self.ProjectsLocationsEdgeCacheOriginsService(self)
    self.projects_locations_edgeCacheServices = self.ProjectsLocationsEdgeCacheServicesService(self)
    self.projects_locations_endpointConfigSelectors = self.ProjectsLocationsEndpointConfigSelectorsService(self)
    self.projects_locations_endpointPolicies = self.ProjectsLocationsEndpointPoliciesService(self)
    self.projects_locations_gateways = self.ProjectsLocationsGatewaysService(self)
    self.projects_locations_global_serviceObserver = self.ProjectsLocationsGlobalServiceObserverService(self)
    self.projects_locations_global = self.ProjectsLocationsGlobalService(self)
    self.projects_locations_grpcRoutes = self.ProjectsLocationsGrpcRoutesService(self)
    self.projects_locations_httpFilters = self.ProjectsLocationsHttpFiltersService(self)
    self.projects_locations_httpRoutes = self.ProjectsLocationsHttpRoutesService(self)
    self.projects_locations_lbRouteExtensions = self.ProjectsLocationsLbRouteExtensionsService(self)
    self.projects_locations_lbServiceSteeringExtensions = self.ProjectsLocationsLbServiceSteeringExtensionsService(self)
    self.projects_locations_lbTrafficExtensions = self.ProjectsLocationsLbTrafficExtensionsService(self)
    self.projects_locations_meshes = self.ProjectsLocationsMeshesService(self)
    self.projects_locations_multicastConsumerAssociations = self.ProjectsLocationsMulticastConsumerAssociationsService(self)
    self.projects_locations_multicastDomainActivations = self.ProjectsLocationsMulticastDomainActivationsService(self)
    self.projects_locations_multicastDomains = self.ProjectsLocationsMulticastDomainsService(self)
    self.projects_locations_multicastGroupDefinitions = self.ProjectsLocationsMulticastGroupDefinitionsService(self)
    self.projects_locations_multicastGroups = self.ProjectsLocationsMulticastGroupsService(self)
    self.projects_locations_observabilityPolicies = self.ProjectsLocationsObservabilityPoliciesService(self)
    self.projects_locations_operations = self.ProjectsLocationsOperationsService(self)
    self.projects_locations_serviceBindings = self.ProjectsLocationsServiceBindingsService(self)
    self.projects_locations_serviceLbPolicies = self.ProjectsLocationsServiceLbPoliciesService(self)
    self.projects_locations_tcpRoutes = self.ProjectsLocationsTcpRoutesService(self)
    self.projects_locations_tlsRoutes = self.ProjectsLocationsTlsRoutesService(self)
    self.projects_locations_wasmActions = self.ProjectsLocationsWasmActionsService(self)
    self.projects_locations_wasmPlugins_versions = self.ProjectsLocationsWasmPluginsVersionsService(self)
    self.projects_locations_wasmPlugins = self.ProjectsLocationsWasmPluginsService(self)
    self.projects_locations = self.ProjectsLocationsService(self)
    self.projects = self.ProjectsService(self)

  class ProjectsLocationsEdgeCacheKeysetsService(base_api.BaseApiService):
    """Service class for the projects_locations_edgeCacheKeysets resource."""

    _NAME = 'projects_locations_edgeCacheKeysets'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsEdgeCacheKeysetsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new EdgeCacheKeyset in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheKeysetsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheKeysets',
        http_method='POST',
        method_id='networkservices.projects.locations.edgeCacheKeysets.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['edgeCacheKeysetId'],
        relative_path='v1alpha1/{+parent}/edgeCacheKeysets',
        request_field='edgeCacheKeyset',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheKeysetsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single EdgeCacheKeyset.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheKeysetsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheKeysets/{edgeCacheKeysetsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.edgeCacheKeysets.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheKeysetsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single EdgeCacheKeyset.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheKeysetsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EdgeCacheKeyset) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheKeysets/{edgeCacheKeysetsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.edgeCacheKeysets.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheKeysetsGetRequest',
        response_type_name='EdgeCacheKeyset',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheKeysetsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheKeysets/{edgeCacheKeysetsId}:getIamPolicy',
        http_method='GET',
        method_id='networkservices.projects.locations.edgeCacheKeysets.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheKeysetsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists EdgeCacheKeysets in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheKeysetsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEdgeCacheKeysetsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheKeysets',
        http_method='GET',
        method_id='networkservices.projects.locations.edgeCacheKeysets.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/edgeCacheKeysets',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheKeysetsListRequest',
        response_type_name='ListEdgeCacheKeysetsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single EdgeCacheKeyset.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheKeysetsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheKeysets/{edgeCacheKeysetsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.edgeCacheKeysets.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='edgeCacheKeyset',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheKeysetsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheKeysetsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheKeysets/{edgeCacheKeysetsId}:setIamPolicy',
        http_method='POST',
        method_id='networkservices.projects.locations.edgeCacheKeysets.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheKeysetsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheKeysetsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheKeysets/{edgeCacheKeysetsId}:testIamPermissions',
        http_method='POST',
        method_id='networkservices.projects.locations.edgeCacheKeysets.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheKeysetsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsEdgeCacheOriginsService(base_api.BaseApiService):
    """Service class for the projects_locations_edgeCacheOrigins resource."""

    _NAME = 'projects_locations_edgeCacheOrigins'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsEdgeCacheOriginsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new EdgeCacheOrigin in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheOriginsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheOrigins',
        http_method='POST',
        method_id='networkservices.projects.locations.edgeCacheOrigins.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['edgeCacheOriginId'],
        relative_path='v1alpha1/{+parent}/edgeCacheOrigins',
        request_field='edgeCacheOrigin',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheOriginsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single EdgeCacheOrigin.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheOriginsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheOrigins/{edgeCacheOriginsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.edgeCacheOrigins.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheOriginsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single EdgeCacheOrigin.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheOriginsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EdgeCacheOrigin) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheOrigins/{edgeCacheOriginsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.edgeCacheOrigins.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheOriginsGetRequest',
        response_type_name='EdgeCacheOrigin',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheOriginsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheOrigins/{edgeCacheOriginsId}:getIamPolicy',
        http_method='GET',
        method_id='networkservices.projects.locations.edgeCacheOrigins.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheOriginsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists EdgeCacheOrigins in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheOriginsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEdgeCacheOriginsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheOrigins',
        http_method='GET',
        method_id='networkservices.projects.locations.edgeCacheOrigins.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/edgeCacheOrigins',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheOriginsListRequest',
        response_type_name='ListEdgeCacheOriginsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single EdgeCacheOrigin.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheOriginsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheOrigins/{edgeCacheOriginsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.edgeCacheOrigins.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='edgeCacheOrigin',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheOriginsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheOriginsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheOrigins/{edgeCacheOriginsId}:setIamPolicy',
        http_method='POST',
        method_id='networkservices.projects.locations.edgeCacheOrigins.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheOriginsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheOriginsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheOrigins/{edgeCacheOriginsId}:testIamPermissions',
        http_method='POST',
        method_id='networkservices.projects.locations.edgeCacheOrigins.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheOriginsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsEdgeCacheServicesService(base_api.BaseApiService):
    """Service class for the projects_locations_edgeCacheServices resource."""

    _NAME = 'projects_locations_edgeCacheServices'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsEdgeCacheServicesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new EdgeCacheService in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheServicesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheServices',
        http_method='POST',
        method_id='networkservices.projects.locations.edgeCacheServices.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['edgeCacheServiceId'],
        relative_path='v1alpha1/{+parent}/edgeCacheServices',
        request_field='edgeCacheService',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheServicesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single EdgeCacheService.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheServicesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheServices/{edgeCacheServicesId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.edgeCacheServices.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheServicesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single EdgeCacheService.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheServicesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EdgeCacheService) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheServices/{edgeCacheServicesId}',
        http_method='GET',
        method_id='networkservices.projects.locations.edgeCacheServices.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheServicesGetRequest',
        response_type_name='EdgeCacheService',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheServicesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheServices/{edgeCacheServicesId}:getIamPolicy',
        http_method='GET',
        method_id='networkservices.projects.locations.edgeCacheServices.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheServicesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def InvalidateCache(self, request, global_params=None):
      r"""Sends a cache invalidation request.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheServicesInvalidateCacheRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (InvalidateCacheResponse) The response message.
      """
      config = self.GetMethodConfig('InvalidateCache')
      return self._RunMethod(
          config, request, global_params=global_params)

    InvalidateCache.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheServices/{edgeCacheServicesId}:invalidateCache',
        http_method='POST',
        method_id='networkservices.projects.locations.edgeCacheServices.invalidateCache',
        ordered_params=['edgeCacheService'],
        path_params=['edgeCacheService'],
        query_params=[],
        relative_path='v1alpha1/{+edgeCacheService}:invalidateCache',
        request_field='invalidateCacheRequest',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheServicesInvalidateCacheRequest',
        response_type_name='InvalidateCacheResponse',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists EdgeCacheServices in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheServicesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEdgeCacheServicesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheServices',
        http_method='GET',
        method_id='networkservices.projects.locations.edgeCacheServices.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/edgeCacheServices',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheServicesListRequest',
        response_type_name='ListEdgeCacheServicesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single EdgeCacheService.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheServicesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheServices/{edgeCacheServicesId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.edgeCacheServices.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='edgeCacheService',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheServicesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheServicesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheServices/{edgeCacheServicesId}:setIamPolicy',
        http_method='POST',
        method_id='networkservices.projects.locations.edgeCacheServices.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheServicesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkservicesProjectsLocationsEdgeCacheServicesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/edgeCacheServices/{edgeCacheServicesId}:testIamPermissions',
        http_method='POST',
        method_id='networkservices.projects.locations.edgeCacheServices.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkservicesProjectsLocationsEdgeCacheServicesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsEndpointConfigSelectorsService(base_api.BaseApiService):
    """Service class for the projects_locations_endpointConfigSelectors resource."""

    _NAME = 'projects_locations_endpointConfigSelectors'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsEndpointConfigSelectorsService, self).__init__(client)
      self._upload_configs = {
          }

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkservicesProjectsLocationsEndpointConfigSelectorsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpointConfigSelectors/{endpointConfigSelectorsId}:getIamPolicy',
        http_method='GET',
        method_id='networkservices.projects.locations.endpointConfigSelectors.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEndpointConfigSelectorsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkservicesProjectsLocationsEndpointConfigSelectorsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpointConfigSelectors/{endpointConfigSelectorsId}:setIamPolicy',
        http_method='POST',
        method_id='networkservices.projects.locations.endpointConfigSelectors.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkservicesProjectsLocationsEndpointConfigSelectorsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkservicesProjectsLocationsEndpointConfigSelectorsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpointConfigSelectors/{endpointConfigSelectorsId}:testIamPermissions',
        http_method='POST',
        method_id='networkservices.projects.locations.endpointConfigSelectors.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkservicesProjectsLocationsEndpointConfigSelectorsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsEndpointPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_endpointPolicies resource."""

    _NAME = 'projects_locations_endpointPolicies'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsEndpointPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new EndpointPolicy in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsEndpointPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpointPolicies',
        http_method='POST',
        method_id='networkservices.projects.locations.endpointPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['endpointPolicyId'],
        relative_path='v1alpha1/{+parent}/endpointPolicies',
        request_field='endpointPolicy',
        request_type_name='NetworkservicesProjectsLocationsEndpointPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single EndpointPolicy.

      Args:
        request: (NetworkservicesProjectsLocationsEndpointPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpointPolicies/{endpointPoliciesId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.endpointPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEndpointPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single EndpointPolicy.

      Args:
        request: (NetworkservicesProjectsLocationsEndpointPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (EndpointPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpointPolicies/{endpointPoliciesId}',
        http_method='GET',
        method_id='networkservices.projects.locations.endpointPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEndpointPoliciesGetRequest',
        response_type_name='EndpointPolicy',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkservicesProjectsLocationsEndpointPoliciesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpointPolicies/{endpointPoliciesId}:getIamPolicy',
        http_method='GET',
        method_id='networkservices.projects.locations.endpointPolicies.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEndpointPoliciesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists EndpointPolicies in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsEndpointPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListEndpointPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpointPolicies',
        http_method='GET',
        method_id='networkservices.projects.locations.endpointPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/endpointPolicies',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsEndpointPoliciesListRequest',
        response_type_name='ListEndpointPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single EndpointPolicy.

      Args:
        request: (NetworkservicesProjectsLocationsEndpointPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpointPolicies/{endpointPoliciesId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.endpointPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='endpointPolicy',
        request_type_name='NetworkservicesProjectsLocationsEndpointPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkservicesProjectsLocationsEndpointPoliciesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpointPolicies/{endpointPoliciesId}:setIamPolicy',
        http_method='POST',
        method_id='networkservices.projects.locations.endpointPolicies.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkservicesProjectsLocationsEndpointPoliciesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkservicesProjectsLocationsEndpointPoliciesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/endpointPolicies/{endpointPoliciesId}:testIamPermissions',
        http_method='POST',
        method_id='networkservices.projects.locations.endpointPolicies.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkservicesProjectsLocationsEndpointPoliciesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGatewaysService(base_api.BaseApiService):
    """Service class for the projects_locations_gateways resource."""

    _NAME = 'projects_locations_gateways'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsGatewaysService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Gateway in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsGatewaysCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gateways',
        http_method='POST',
        method_id='networkservices.projects.locations.gateways.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['gatewayId'],
        relative_path='v1alpha1/{+parent}/gateways',
        request_field='gateway',
        request_type_name='NetworkservicesProjectsLocationsGatewaysCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Gateway.

      Args:
        request: (NetworkservicesProjectsLocationsGatewaysDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.gateways.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGatewaysDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Gateway.

      Args:
        request: (NetworkservicesProjectsLocationsGatewaysGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Gateway) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}',
        http_method='GET',
        method_id='networkservices.projects.locations.gateways.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGatewaysGetRequest',
        response_type_name='Gateway',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkservicesProjectsLocationsGatewaysGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}:getIamPolicy',
        http_method='GET',
        method_id='networkservices.projects.locations.gateways.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGatewaysGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Gateways in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsGatewaysListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGatewaysResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gateways',
        http_method='GET',
        method_id='networkservices.projects.locations.gateways.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/gateways',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGatewaysListRequest',
        response_type_name='ListGatewaysResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Gateway.

      Args:
        request: (NetworkservicesProjectsLocationsGatewaysPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.gateways.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='gateway',
        request_type_name='NetworkservicesProjectsLocationsGatewaysPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkservicesProjectsLocationsGatewaysSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}:setIamPolicy',
        http_method='POST',
        method_id='networkservices.projects.locations.gateways.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkservicesProjectsLocationsGatewaysSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkservicesProjectsLocationsGatewaysTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/gateways/{gatewaysId}:testIamPermissions',
        http_method='POST',
        method_id='networkservices.projects.locations.gateways.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkservicesProjectsLocationsGatewaysTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGlobalServiceObserverService(base_api.BaseApiService):
    """Service class for the projects_locations_global_serviceObserver resource."""

    _NAME = 'projects_locations_global_serviceObserver'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsGlobalServiceObserverService, self).__init__(client)
      self._upload_configs = {
          }

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkservicesProjectsLocationsGlobalServiceObserverGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/global/serviceObserver:getIamPolicy',
        http_method='GET',
        method_id='networkservices.projects.locations.global.serviceObserver.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGlobalServiceObserverGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkservicesProjectsLocationsGlobalServiceObserverSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/global/serviceObserver:setIamPolicy',
        http_method='POST',
        method_id='networkservices.projects.locations.global.serviceObserver.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkservicesProjectsLocationsGlobalServiceObserverSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkservicesProjectsLocationsGlobalServiceObserverTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/global/serviceObserver:testIamPermissions',
        http_method='POST',
        method_id='networkservices.projects.locations.global.serviceObserver.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkservicesProjectsLocationsGlobalServiceObserverTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsGlobalService(base_api.BaseApiService):
    """Service class for the projects_locations_global resource."""

    _NAME = 'projects_locations_global'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsGlobalService, self).__init__(client)
      self._upload_configs = {
          }

    def GetServiceObserver(self, request, global_params=None):
      r"""GetServiceObserver gets a singleton without a parent resource.

      Args:
        request: (NetworkservicesProjectsLocationsGlobalGetServiceObserverRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceObserver) The response message.
      """
      config = self.GetMethodConfig('GetServiceObserver')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetServiceObserver.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/global/serviceObserver',
        http_method='GET',
        method_id='networkservices.projects.locations.global.getServiceObserver',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGlobalGetServiceObserverRequest',
        response_type_name='ServiceObserver',
        supports_download=False,
    )

    def UpdateServiceObserver(self, request, global_params=None):
      r"""UpdateServiceObserver updates a singleton without a parent resource.

      Args:
        request: (NetworkservicesProjectsLocationsGlobalUpdateServiceObserverRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceObserver) The response message.
      """
      config = self.GetMethodConfig('UpdateServiceObserver')
      return self._RunMethod(
          config, request, global_params=global_params)

    UpdateServiceObserver.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/global/serviceObserver',
        http_method='PATCH',
        method_id='networkservices.projects.locations.global.updateServiceObserver',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='serviceObserver',
        request_type_name='NetworkservicesProjectsLocationsGlobalUpdateServiceObserverRequest',
        response_type_name='ServiceObserver',
        supports_download=False,
    )

  class ProjectsLocationsGrpcRoutesService(base_api.BaseApiService):
    """Service class for the projects_locations_grpcRoutes resource."""

    _NAME = 'projects_locations_grpcRoutes'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsGrpcRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new GrpcRoute in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsGrpcRoutesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/grpcRoutes',
        http_method='POST',
        method_id='networkservices.projects.locations.grpcRoutes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['grpcRouteId'],
        relative_path='v1alpha1/{+parent}/grpcRoutes',
        request_field='grpcRoute',
        request_type_name='NetworkservicesProjectsLocationsGrpcRoutesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single GrpcRoute.

      Args:
        request: (NetworkservicesProjectsLocationsGrpcRoutesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/grpcRoutes/{grpcRoutesId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.grpcRoutes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGrpcRoutesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single GrpcRoute.

      Args:
        request: (NetworkservicesProjectsLocationsGrpcRoutesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (GrpcRoute) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/grpcRoutes/{grpcRoutesId}',
        http_method='GET',
        method_id='networkservices.projects.locations.grpcRoutes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGrpcRoutesGetRequest',
        response_type_name='GrpcRoute',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkservicesProjectsLocationsGrpcRoutesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/grpcRoutes/{grpcRoutesId}:getIamPolicy',
        http_method='GET',
        method_id='networkservices.projects.locations.grpcRoutes.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGrpcRoutesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists GrpcRoutes in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsGrpcRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListGrpcRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/grpcRoutes',
        http_method='GET',
        method_id='networkservices.projects.locations.grpcRoutes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/grpcRoutes',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGrpcRoutesListRequest',
        response_type_name='ListGrpcRoutesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single GrpcRoute.

      Args:
        request: (NetworkservicesProjectsLocationsGrpcRoutesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/grpcRoutes/{grpcRoutesId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.grpcRoutes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='grpcRoute',
        request_type_name='NetworkservicesProjectsLocationsGrpcRoutesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkservicesProjectsLocationsGrpcRoutesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/grpcRoutes/{grpcRoutesId}:setIamPolicy',
        http_method='POST',
        method_id='networkservices.projects.locations.grpcRoutes.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkservicesProjectsLocationsGrpcRoutesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkservicesProjectsLocationsGrpcRoutesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/grpcRoutes/{grpcRoutesId}:testIamPermissions',
        http_method='POST',
        method_id='networkservices.projects.locations.grpcRoutes.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkservicesProjectsLocationsGrpcRoutesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsHttpFiltersService(base_api.BaseApiService):
    """Service class for the projects_locations_httpFilters resource."""

    _NAME = 'projects_locations_httpFilters'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsHttpFiltersService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new HttpFilter in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsHttpFiltersCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/httpFilters',
        http_method='POST',
        method_id='networkservices.projects.locations.httpFilters.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['httpFilterId'],
        relative_path='v1alpha1/{+parent}/httpFilters',
        request_field='httpFilter',
        request_type_name='NetworkservicesProjectsLocationsHttpFiltersCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single HttpFilter.

      Args:
        request: (NetworkservicesProjectsLocationsHttpFiltersDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/httpFilters/{httpFiltersId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.httpFilters.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsHttpFiltersDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single HttpFilter.

      Args:
        request: (NetworkservicesProjectsLocationsHttpFiltersGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpFilter) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/httpFilters/{httpFiltersId}',
        http_method='GET',
        method_id='networkservices.projects.locations.httpFilters.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsHttpFiltersGetRequest',
        response_type_name='HttpFilter',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkservicesProjectsLocationsHttpFiltersGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/httpFilters/{httpFiltersId}:getIamPolicy',
        http_method='GET',
        method_id='networkservices.projects.locations.httpFilters.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsHttpFiltersGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists HttpFilters in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsHttpFiltersListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListHttpFiltersResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/httpFilters',
        http_method='GET',
        method_id='networkservices.projects.locations.httpFilters.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/httpFilters',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsHttpFiltersListRequest',
        response_type_name='ListHttpFiltersResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single HttpFilter.

      Args:
        request: (NetworkservicesProjectsLocationsHttpFiltersPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/httpFilters/{httpFiltersId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.httpFilters.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='httpFilter',
        request_type_name='NetworkservicesProjectsLocationsHttpFiltersPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkservicesProjectsLocationsHttpFiltersSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/httpFilters/{httpFiltersId}:setIamPolicy',
        http_method='POST',
        method_id='networkservices.projects.locations.httpFilters.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkservicesProjectsLocationsHttpFiltersSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkservicesProjectsLocationsHttpFiltersTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/httpFilters/{httpFiltersId}:testIamPermissions',
        http_method='POST',
        method_id='networkservices.projects.locations.httpFilters.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkservicesProjectsLocationsHttpFiltersTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsHttpRoutesService(base_api.BaseApiService):
    """Service class for the projects_locations_httpRoutes resource."""

    _NAME = 'projects_locations_httpRoutes'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsHttpRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new HttpRoute in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsHttpRoutesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/httpRoutes',
        http_method='POST',
        method_id='networkservices.projects.locations.httpRoutes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['httpRouteId'],
        relative_path='v1alpha1/{+parent}/httpRoutes',
        request_field='httpRoute',
        request_type_name='NetworkservicesProjectsLocationsHttpRoutesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single HttpRoute.

      Args:
        request: (NetworkservicesProjectsLocationsHttpRoutesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/httpRoutes/{httpRoutesId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.httpRoutes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsHttpRoutesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single HttpRoute.

      Args:
        request: (NetworkservicesProjectsLocationsHttpRoutesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (HttpRoute) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/httpRoutes/{httpRoutesId}',
        http_method='GET',
        method_id='networkservices.projects.locations.httpRoutes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsHttpRoutesGetRequest',
        response_type_name='HttpRoute',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkservicesProjectsLocationsHttpRoutesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/httpRoutes/{httpRoutesId}:getIamPolicy',
        http_method='GET',
        method_id='networkservices.projects.locations.httpRoutes.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsHttpRoutesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists HttpRoute in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsHttpRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListHttpRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/httpRoutes',
        http_method='GET',
        method_id='networkservices.projects.locations.httpRoutes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/httpRoutes',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsHttpRoutesListRequest',
        response_type_name='ListHttpRoutesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single HttpRoute.

      Args:
        request: (NetworkservicesProjectsLocationsHttpRoutesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/httpRoutes/{httpRoutesId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.httpRoutes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='httpRoute',
        request_type_name='NetworkservicesProjectsLocationsHttpRoutesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkservicesProjectsLocationsHttpRoutesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/httpRoutes/{httpRoutesId}:setIamPolicy',
        http_method='POST',
        method_id='networkservices.projects.locations.httpRoutes.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkservicesProjectsLocationsHttpRoutesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkservicesProjectsLocationsHttpRoutesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/httpRoutes/{httpRoutesId}:testIamPermissions',
        http_method='POST',
        method_id='networkservices.projects.locations.httpRoutes.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkservicesProjectsLocationsHttpRoutesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsLbRouteExtensionsService(base_api.BaseApiService):
    """Service class for the projects_locations_lbRouteExtensions resource."""

    _NAME = 'projects_locations_lbRouteExtensions'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsLbRouteExtensionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new LbRouteExtension in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsLbRouteExtensionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/lbRouteExtensions',
        http_method='POST',
        method_id='networkservices.projects.locations.lbRouteExtensions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['lbRouteExtensionId', 'requestId'],
        relative_path='v1alpha1/{+parent}/lbRouteExtensions',
        request_field='lbRouteExtension',
        request_type_name='NetworkservicesProjectsLocationsLbRouteExtensionsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single LbRouteExtension.

      Args:
        request: (NetworkservicesProjectsLocationsLbRouteExtensionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/lbRouteExtensions/{lbRouteExtensionsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.lbRouteExtensions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbRouteExtensionsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single LbRouteExtension.

      Args:
        request: (NetworkservicesProjectsLocationsLbRouteExtensionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LbRouteExtension) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/lbRouteExtensions/{lbRouteExtensionsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.lbRouteExtensions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbRouteExtensionsGetRequest',
        response_type_name='LbRouteExtension',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists LbRouteExtensions in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsLbRouteExtensionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLbRouteExtensionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/lbRouteExtensions',
        http_method='GET',
        method_id='networkservices.projects.locations.lbRouteExtensions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/lbRouteExtensions',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbRouteExtensionsListRequest',
        response_type_name='ListLbRouteExtensionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single LbRouteExtension.

      Args:
        request: (NetworkservicesProjectsLocationsLbRouteExtensionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/lbRouteExtensions/{lbRouteExtensionsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.lbRouteExtensions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='lbRouteExtension',
        request_type_name='NetworkservicesProjectsLocationsLbRouteExtensionsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsLbServiceSteeringExtensionsService(base_api.BaseApiService):
    """Service class for the projects_locations_lbServiceSteeringExtensions resource."""

    _NAME = 'projects_locations_lbServiceSteeringExtensions'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsLbServiceSteeringExtensionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new LbServiceSteeringExtension in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsLbServiceSteeringExtensionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/lbServiceSteeringExtensions',
        http_method='POST',
        method_id='networkservices.projects.locations.lbServiceSteeringExtensions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['lbServiceSteeringExtensionId', 'requestId'],
        relative_path='v1alpha1/{+parent}/lbServiceSteeringExtensions',
        request_field='lbServiceSteeringExtension',
        request_type_name='NetworkservicesProjectsLocationsLbServiceSteeringExtensionsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single LbServiceSteeringExtension.

      Args:
        request: (NetworkservicesProjectsLocationsLbServiceSteeringExtensionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/lbServiceSteeringExtensions/{lbServiceSteeringExtensionsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.lbServiceSteeringExtensions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbServiceSteeringExtensionsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single LbServiceSteeringExtension.

      Args:
        request: (NetworkservicesProjectsLocationsLbServiceSteeringExtensionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LbServiceSteeringExtension) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/lbServiceSteeringExtensions/{lbServiceSteeringExtensionsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.lbServiceSteeringExtensions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbServiceSteeringExtensionsGetRequest',
        response_type_name='LbServiceSteeringExtension',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists LbServiceSteeringExtensions in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsLbServiceSteeringExtensionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLbServiceSteeringExtensionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/lbServiceSteeringExtensions',
        http_method='GET',
        method_id='networkservices.projects.locations.lbServiceSteeringExtensions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/lbServiceSteeringExtensions',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbServiceSteeringExtensionsListRequest',
        response_type_name='ListLbServiceSteeringExtensionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single LbServiceSteeringExtension.

      Args:
        request: (NetworkservicesProjectsLocationsLbServiceSteeringExtensionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/lbServiceSteeringExtensions/{lbServiceSteeringExtensionsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.lbServiceSteeringExtensions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='lbServiceSteeringExtension',
        request_type_name='NetworkservicesProjectsLocationsLbServiceSteeringExtensionsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsLbTrafficExtensionsService(base_api.BaseApiService):
    """Service class for the projects_locations_lbTrafficExtensions resource."""

    _NAME = 'projects_locations_lbTrafficExtensions'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsLbTrafficExtensionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new LbTrafficExtension in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsLbTrafficExtensionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/lbTrafficExtensions',
        http_method='POST',
        method_id='networkservices.projects.locations.lbTrafficExtensions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['lbTrafficExtensionId', 'requestId'],
        relative_path='v1alpha1/{+parent}/lbTrafficExtensions',
        request_field='lbTrafficExtension',
        request_type_name='NetworkservicesProjectsLocationsLbTrafficExtensionsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single LbTrafficExtension.

      Args:
        request: (NetworkservicesProjectsLocationsLbTrafficExtensionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/lbTrafficExtensions/{lbTrafficExtensionsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.lbTrafficExtensions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbTrafficExtensionsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single LbTrafficExtension.

      Args:
        request: (NetworkservicesProjectsLocationsLbTrafficExtensionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (LbTrafficExtension) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/lbTrafficExtensions/{lbTrafficExtensionsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.lbTrafficExtensions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbTrafficExtensionsGetRequest',
        response_type_name='LbTrafficExtension',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists LbTrafficExtensions in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsLbTrafficExtensionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLbTrafficExtensionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/lbTrafficExtensions',
        http_method='GET',
        method_id='networkservices.projects.locations.lbTrafficExtensions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/lbTrafficExtensions',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsLbTrafficExtensionsListRequest',
        response_type_name='ListLbTrafficExtensionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single LbTrafficExtension.

      Args:
        request: (NetworkservicesProjectsLocationsLbTrafficExtensionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/lbTrafficExtensions/{lbTrafficExtensionsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.lbTrafficExtensions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='lbTrafficExtension',
        request_type_name='NetworkservicesProjectsLocationsLbTrafficExtensionsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMeshesService(base_api.BaseApiService):
    """Service class for the projects_locations_meshes resource."""

    _NAME = 'projects_locations_meshes'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsMeshesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new Mesh in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMeshesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/meshes',
        http_method='POST',
        method_id='networkservices.projects.locations.meshes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['meshId'],
        relative_path='v1alpha1/{+parent}/meshes',
        request_field='mesh',
        request_type_name='NetworkservicesProjectsLocationsMeshesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single Mesh.

      Args:
        request: (NetworkservicesProjectsLocationsMeshesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/meshes/{meshesId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.meshes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMeshesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single Mesh.

      Args:
        request: (NetworkservicesProjectsLocationsMeshesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Mesh) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/meshes/{meshesId}',
        http_method='GET',
        method_id='networkservices.projects.locations.meshes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMeshesGetRequest',
        response_type_name='Mesh',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkservicesProjectsLocationsMeshesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/meshes/{meshesId}:getIamPolicy',
        http_method='GET',
        method_id='networkservices.projects.locations.meshes.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMeshesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists Meshes in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMeshesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMeshesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/meshes',
        http_method='GET',
        method_id='networkservices.projects.locations.meshes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/meshes',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMeshesListRequest',
        response_type_name='ListMeshesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single Mesh.

      Args:
        request: (NetworkservicesProjectsLocationsMeshesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/meshes/{meshesId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.meshes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='mesh',
        request_type_name='NetworkservicesProjectsLocationsMeshesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkservicesProjectsLocationsMeshesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/meshes/{meshesId}:setIamPolicy',
        http_method='POST',
        method_id='networkservices.projects.locations.meshes.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkservicesProjectsLocationsMeshesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkservicesProjectsLocationsMeshesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/meshes/{meshesId}:testIamPermissions',
        http_method='POST',
        method_id='networkservices.projects.locations.meshes.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkservicesProjectsLocationsMeshesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsMulticastConsumerAssociationsService(base_api.BaseApiService):
    """Service class for the projects_locations_multicastConsumerAssociations resource."""

    _NAME = 'projects_locations_multicastConsumerAssociations'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsMulticastConsumerAssociationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new MulticastConsumerAssociation in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastConsumerAssociationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastConsumerAssociations',
        http_method='POST',
        method_id='networkservices.projects.locations.multicastConsumerAssociations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['multicastConsumerAssociationId', 'requestId'],
        relative_path='v1alpha1/{+parent}/multicastConsumerAssociations',
        request_field='multicastConsumerAssociation',
        request_type_name='NetworkservicesProjectsLocationsMulticastConsumerAssociationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single MulticastConsumerAssociation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastConsumerAssociationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastConsumerAssociations/{multicastConsumerAssociationsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.multicastConsumerAssociations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastConsumerAssociationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single MulticastConsumerAssociation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastConsumerAssociationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MulticastConsumerAssociation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastConsumerAssociations/{multicastConsumerAssociationsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastConsumerAssociations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastConsumerAssociationsGetRequest',
        response_type_name='MulticastConsumerAssociation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists MulticastConsumerAssociations in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastConsumerAssociationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMulticastConsumerAssociationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastConsumerAssociations',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastConsumerAssociations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/multicastConsumerAssociations',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastConsumerAssociationsListRequest',
        response_type_name='ListMulticastConsumerAssociationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single MulticastConsumerAssociation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastConsumerAssociationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastConsumerAssociations/{multicastConsumerAssociationsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.multicastConsumerAssociations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='multicastConsumerAssociation',
        request_type_name='NetworkservicesProjectsLocationsMulticastConsumerAssociationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMulticastDomainActivationsService(base_api.BaseApiService):
    """Service class for the projects_locations_multicastDomainActivations resource."""

    _NAME = 'projects_locations_multicastDomainActivations'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsMulticastDomainActivationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new MulticastDomainActivation in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainActivationsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastDomainActivations',
        http_method='POST',
        method_id='networkservices.projects.locations.multicastDomainActivations.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['multicastDomainActivationId', 'requestId'],
        relative_path='v1alpha1/{+parent}/multicastDomainActivations',
        request_field='multicastDomainActivation',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainActivationsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single MulticastDomainActivation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainActivationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastDomainActivations/{multicastDomainActivationsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.multicastDomainActivations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainActivationsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single MulticastDomainActivation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainActivationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MulticastDomainActivation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastDomainActivations/{multicastDomainActivationsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastDomainActivations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainActivationsGetRequest',
        response_type_name='MulticastDomainActivation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists MulticastDomainActivations in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainActivationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMulticastDomainActivationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastDomainActivations',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastDomainActivations.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/multicastDomainActivations',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainActivationsListRequest',
        response_type_name='ListMulticastDomainActivationsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single MulticastDomainActivation.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainActivationsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastDomainActivations/{multicastDomainActivationsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.multicastDomainActivations.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='multicastDomainActivation',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainActivationsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMulticastDomainsService(base_api.BaseApiService):
    """Service class for the projects_locations_multicastDomains resource."""

    _NAME = 'projects_locations_multicastDomains'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsMulticastDomainsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new MulticastDomain in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastDomains',
        http_method='POST',
        method_id='networkservices.projects.locations.multicastDomains.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['multicastDomainId', 'requestId'],
        relative_path='v1alpha1/{+parent}/multicastDomains',
        request_field='multicastDomain',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single MulticastDomain.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastDomains/{multicastDomainsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.multicastDomains.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single MulticastDomain.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MulticastDomain) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastDomains/{multicastDomainsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastDomains.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainsGetRequest',
        response_type_name='MulticastDomain',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists MulticastDomains in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMulticastDomainsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastDomains',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastDomains.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/multicastDomains',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainsListRequest',
        response_type_name='ListMulticastDomainsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single MulticastDomain.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastDomainsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastDomains/{multicastDomainsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.multicastDomains.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='multicastDomain',
        request_type_name='NetworkservicesProjectsLocationsMulticastDomainsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMulticastGroupDefinitionsService(base_api.BaseApiService):
    """Service class for the projects_locations_multicastGroupDefinitions resource."""

    _NAME = 'projects_locations_multicastGroupDefinitions'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsMulticastGroupDefinitionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new MulticastGroupDefinition in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupDefinitionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastGroupDefinitions',
        http_method='POST',
        method_id='networkservices.projects.locations.multicastGroupDefinitions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['multicastGroupDefinitionId', 'requestId'],
        relative_path='v1alpha1/{+parent}/multicastGroupDefinitions',
        request_field='multicastGroupDefinition',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupDefinitionsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single MulticastGroupDefinition.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupDefinitionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastGroupDefinitions/{multicastGroupDefinitionsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.multicastGroupDefinitions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupDefinitionsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single MulticastGroupDefinition.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupDefinitionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MulticastGroupDefinition) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastGroupDefinitions/{multicastGroupDefinitionsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastGroupDefinitions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupDefinitionsGetRequest',
        response_type_name='MulticastGroupDefinition',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists MulticastGroupDefinitions in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupDefinitionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMulticastGroupDefinitionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastGroupDefinitions',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastGroupDefinitions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/multicastGroupDefinitions',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupDefinitionsListRequest',
        response_type_name='ListMulticastGroupDefinitionsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single MulticastGroupDefinition.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupDefinitionsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastGroupDefinitions/{multicastGroupDefinitionsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.multicastGroupDefinitions.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='multicastGroupDefinition',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupDefinitionsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsMulticastGroupsService(base_api.BaseApiService):
    """Service class for the projects_locations_multicastGroups resource."""

    _NAME = 'projects_locations_multicastGroups'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsMulticastGroupsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new MulticastGroup in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastGroups',
        http_method='POST',
        method_id='networkservices.projects.locations.multicastGroups.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['multicastGroupId', 'requestId'],
        relative_path='v1alpha1/{+parent}/multicastGroups',
        request_field='multicastGroup',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single MulticastGroup.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastGroups/{multicastGroupsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.multicastGroups.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single MulticastGroup.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (MulticastGroup) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastGroups/{multicastGroupsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastGroups.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupsGetRequest',
        response_type_name='MulticastGroup',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists MulticastGroups in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListMulticastGroupsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastGroups',
        http_method='GET',
        method_id='networkservices.projects.locations.multicastGroups.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['filter', 'orderBy', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/multicastGroups',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupsListRequest',
        response_type_name='ListMulticastGroupsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single MulticastGroup.

      Args:
        request: (NetworkservicesProjectsLocationsMulticastGroupsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/multicastGroups/{multicastGroupsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.multicastGroups.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['requestId', 'updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='multicastGroup',
        request_type_name='NetworkservicesProjectsLocationsMulticastGroupsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsObservabilityPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_observabilityPolicies resource."""

    _NAME = 'projects_locations_observabilityPolicies'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsObservabilityPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ObservabilityPolicy in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsObservabilityPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/observabilityPolicies',
        http_method='POST',
        method_id='networkservices.projects.locations.observabilityPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['observabilityPolicyId'],
        relative_path='v1alpha1/{+parent}/observabilityPolicies',
        request_field='observabilityPolicy',
        request_type_name='NetworkservicesProjectsLocationsObservabilityPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ObservabilityPolicy.

      Args:
        request: (NetworkservicesProjectsLocationsObservabilityPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/observabilityPolicies/{observabilityPoliciesId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.observabilityPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsObservabilityPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ObservabilityPolicy.

      Args:
        request: (NetworkservicesProjectsLocationsObservabilityPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ObservabilityPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/observabilityPolicies/{observabilityPoliciesId}',
        http_method='GET',
        method_id='networkservices.projects.locations.observabilityPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsObservabilityPoliciesGetRequest',
        response_type_name='ObservabilityPolicy',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkservicesProjectsLocationsObservabilityPoliciesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/observabilityPolicies/{observabilityPoliciesId}:getIamPolicy',
        http_method='GET',
        method_id='networkservices.projects.locations.observabilityPolicies.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsObservabilityPoliciesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ObservabilityPolicies in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsObservabilityPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListObservabilityPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/observabilityPolicies',
        http_method='GET',
        method_id='networkservices.projects.locations.observabilityPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/observabilityPolicies',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsObservabilityPoliciesListRequest',
        response_type_name='ListObservabilityPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single ObservabilityPolicy.

      Args:
        request: (NetworkservicesProjectsLocationsObservabilityPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/observabilityPolicies/{observabilityPoliciesId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.observabilityPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='observabilityPolicy',
        request_type_name='NetworkservicesProjectsLocationsObservabilityPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkservicesProjectsLocationsObservabilityPoliciesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/observabilityPolicies/{observabilityPoliciesId}:setIamPolicy',
        http_method='POST',
        method_id='networkservices.projects.locations.observabilityPolicies.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkservicesProjectsLocationsObservabilityPoliciesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkservicesProjectsLocationsObservabilityPoliciesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/observabilityPolicies/{observabilityPoliciesId}:testIamPermissions',
        http_method='POST',
        method_id='networkservices.projects.locations.observabilityPolicies.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkservicesProjectsLocationsObservabilityPoliciesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsOperationsService(base_api.BaseApiService):
    """Service class for the projects_locations_operations resource."""

    _NAME = 'projects_locations_operations'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsOperationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Cancel(self, request, global_params=None):
      r"""Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.

      Args:
        request: (NetworkservicesProjectsLocationsOperationsCancelRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Cancel')
      return self._RunMethod(
          config, request, global_params=global_params)

    Cancel.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel',
        http_method='POST',
        method_id='networkservices.projects.locations.operations.cancel',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}:cancel',
        request_field='cancelOperationRequest',
        request_type_name='NetworkservicesProjectsLocationsOperationsCancelRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.

      Args:
        request: (NetworkservicesProjectsLocationsOperationsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Empty) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.operations.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsOperationsDeleteRequest',
        response_type_name='Empty',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.

      Args:
        request: (NetworkservicesProjectsLocationsOperationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.operations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsOperationsGetRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.

      Args:
        request: (NetworkservicesProjectsLocationsOperationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListOperationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/operations',
        http_method='GET',
        method_id='networkservices.projects.locations.operations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/operations',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsOperationsListRequest',
        response_type_name='ListOperationsResponse',
        supports_download=False,
    )

  class ProjectsLocationsServiceBindingsService(base_api.BaseApiService):
    """Service class for the projects_locations_serviceBindings resource."""

    _NAME = 'projects_locations_serviceBindings'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsServiceBindingsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ServiceBinding in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsServiceBindingsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serviceBindings',
        http_method='POST',
        method_id='networkservices.projects.locations.serviceBindings.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['serviceBindingId'],
        relative_path='v1alpha1/{+parent}/serviceBindings',
        request_field='serviceBinding',
        request_type_name='NetworkservicesProjectsLocationsServiceBindingsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ServiceBinding.

      Args:
        request: (NetworkservicesProjectsLocationsServiceBindingsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serviceBindings/{serviceBindingsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.serviceBindings.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsServiceBindingsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ServiceBinding.

      Args:
        request: (NetworkservicesProjectsLocationsServiceBindingsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceBinding) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serviceBindings/{serviceBindingsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.serviceBindings.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsServiceBindingsGetRequest',
        response_type_name='ServiceBinding',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkservicesProjectsLocationsServiceBindingsGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serviceBindings/{serviceBindingsId}:getIamPolicy',
        http_method='GET',
        method_id='networkservices.projects.locations.serviceBindings.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsServiceBindingsGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ServiceBinding in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsServiceBindingsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServiceBindingsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serviceBindings',
        http_method='GET',
        method_id='networkservices.projects.locations.serviceBindings.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/serviceBindings',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsServiceBindingsListRequest',
        response_type_name='ListServiceBindingsResponse',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkservicesProjectsLocationsServiceBindingsSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serviceBindings/{serviceBindingsId}:setIamPolicy',
        http_method='POST',
        method_id='networkservices.projects.locations.serviceBindings.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkservicesProjectsLocationsServiceBindingsSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkservicesProjectsLocationsServiceBindingsTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serviceBindings/{serviceBindingsId}:testIamPermissions',
        http_method='POST',
        method_id='networkservices.projects.locations.serviceBindings.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkservicesProjectsLocationsServiceBindingsTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsServiceLbPoliciesService(base_api.BaseApiService):
    """Service class for the projects_locations_serviceLbPolicies resource."""

    _NAME = 'projects_locations_serviceLbPolicies'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsServiceLbPoliciesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new ServiceLbPolicy in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsServiceLbPoliciesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies',
        http_method='POST',
        method_id='networkservices.projects.locations.serviceLbPolicies.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['serviceLbPolicyId'],
        relative_path='v1alpha1/{+parent}/serviceLbPolicies',
        request_field='serviceLbPolicy',
        request_type_name='NetworkservicesProjectsLocationsServiceLbPoliciesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single ServiceLbPolicy.

      Args:
        request: (NetworkservicesProjectsLocationsServiceLbPoliciesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies/{serviceLbPoliciesId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.serviceLbPolicies.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsServiceLbPoliciesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single ServiceLbPolicy.

      Args:
        request: (NetworkservicesProjectsLocationsServiceLbPoliciesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ServiceLbPolicy) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies/{serviceLbPoliciesId}',
        http_method='GET',
        method_id='networkservices.projects.locations.serviceLbPolicies.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsServiceLbPoliciesGetRequest',
        response_type_name='ServiceLbPolicy',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkservicesProjectsLocationsServiceLbPoliciesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies/{serviceLbPoliciesId}:getIamPolicy',
        http_method='GET',
        method_id='networkservices.projects.locations.serviceLbPolicies.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsServiceLbPoliciesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists ServiceLbPolicies in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsServiceLbPoliciesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListServiceLbPoliciesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies',
        http_method='GET',
        method_id='networkservices.projects.locations.serviceLbPolicies.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/serviceLbPolicies',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsServiceLbPoliciesListRequest',
        response_type_name='ListServiceLbPoliciesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single ServiceLbPolicy.

      Args:
        request: (NetworkservicesProjectsLocationsServiceLbPoliciesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies/{serviceLbPoliciesId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.serviceLbPolicies.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='serviceLbPolicy',
        request_type_name='NetworkservicesProjectsLocationsServiceLbPoliciesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkservicesProjectsLocationsServiceLbPoliciesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies/{serviceLbPoliciesId}:setIamPolicy',
        http_method='POST',
        method_id='networkservices.projects.locations.serviceLbPolicies.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkservicesProjectsLocationsServiceLbPoliciesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkservicesProjectsLocationsServiceLbPoliciesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/serviceLbPolicies/{serviceLbPoliciesId}:testIamPermissions',
        http_method='POST',
        method_id='networkservices.projects.locations.serviceLbPolicies.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkservicesProjectsLocationsServiceLbPoliciesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsTcpRoutesService(base_api.BaseApiService):
    """Service class for the projects_locations_tcpRoutes resource."""

    _NAME = 'projects_locations_tcpRoutes'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsTcpRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new TcpRoute in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsTcpRoutesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tcpRoutes',
        http_method='POST',
        method_id='networkservices.projects.locations.tcpRoutes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['tcpRouteId'],
        relative_path='v1alpha1/{+parent}/tcpRoutes',
        request_field='tcpRoute',
        request_type_name='NetworkservicesProjectsLocationsTcpRoutesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single TcpRoute.

      Args:
        request: (NetworkservicesProjectsLocationsTcpRoutesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tcpRoutes/{tcpRoutesId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.tcpRoutes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsTcpRoutesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single TcpRoute.

      Args:
        request: (NetworkservicesProjectsLocationsTcpRoutesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TcpRoute) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tcpRoutes/{tcpRoutesId}',
        http_method='GET',
        method_id='networkservices.projects.locations.tcpRoutes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsTcpRoutesGetRequest',
        response_type_name='TcpRoute',
        supports_download=False,
    )

    def GetIamPolicy(self, request, global_params=None):
      r"""Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.

      Args:
        request: (NetworkservicesProjectsLocationsTcpRoutesGetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('GetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    GetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tcpRoutes/{tcpRoutesId}:getIamPolicy',
        http_method='GET',
        method_id='networkservices.projects.locations.tcpRoutes.getIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=['options_requestedPolicyVersion'],
        relative_path='v1alpha1/{+resource}:getIamPolicy',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsTcpRoutesGetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TcpRoute in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsTcpRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTcpRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tcpRoutes',
        http_method='GET',
        method_id='networkservices.projects.locations.tcpRoutes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/tcpRoutes',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsTcpRoutesListRequest',
        response_type_name='ListTcpRoutesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single TcpRoute.

      Args:
        request: (NetworkservicesProjectsLocationsTcpRoutesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tcpRoutes/{tcpRoutesId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.tcpRoutes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='tcpRoute',
        request_type_name='NetworkservicesProjectsLocationsTcpRoutesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def SetIamPolicy(self, request, global_params=None):
      r"""Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.

      Args:
        request: (NetworkservicesProjectsLocationsTcpRoutesSetIamPolicyRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Policy) The response message.
      """
      config = self.GetMethodConfig('SetIamPolicy')
      return self._RunMethod(
          config, request, global_params=global_params)

    SetIamPolicy.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tcpRoutes/{tcpRoutesId}:setIamPolicy',
        http_method='POST',
        method_id='networkservices.projects.locations.tcpRoutes.setIamPolicy',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:setIamPolicy',
        request_field='setIamPolicyRequest',
        request_type_name='NetworkservicesProjectsLocationsTcpRoutesSetIamPolicyRequest',
        response_type_name='Policy',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkservicesProjectsLocationsTcpRoutesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tcpRoutes/{tcpRoutesId}:testIamPermissions',
        http_method='POST',
        method_id='networkservices.projects.locations.tcpRoutes.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkservicesProjectsLocationsTcpRoutesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsTlsRoutesService(base_api.BaseApiService):
    """Service class for the projects_locations_tlsRoutes resource."""

    _NAME = 'projects_locations_tlsRoutes'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsTlsRoutesService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new TlsRoute in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsTlsRoutesCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tlsRoutes',
        http_method='POST',
        method_id='networkservices.projects.locations.tlsRoutes.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['tlsRouteId'],
        relative_path='v1alpha1/{+parent}/tlsRoutes',
        request_field='tlsRoute',
        request_type_name='NetworkservicesProjectsLocationsTlsRoutesCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes a single TlsRoute.

      Args:
        request: (NetworkservicesProjectsLocationsTlsRoutesDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tlsRoutes/{tlsRoutesId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.tlsRoutes.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsTlsRoutesDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of a single TlsRoute.

      Args:
        request: (NetworkservicesProjectsLocationsTlsRoutesGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TlsRoute) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tlsRoutes/{tlsRoutesId}',
        http_method='GET',
        method_id='networkservices.projects.locations.tlsRoutes.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsTlsRoutesGetRequest',
        response_type_name='TlsRoute',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists TlsRoute in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsTlsRoutesListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListTlsRoutesResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tlsRoutes',
        http_method='GET',
        method_id='networkservices.projects.locations.tlsRoutes.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/tlsRoutes',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsTlsRoutesListRequest',
        response_type_name='ListTlsRoutesResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of a single TlsRoute.

      Args:
        request: (NetworkservicesProjectsLocationsTlsRoutesPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tlsRoutes/{tlsRoutesId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.tlsRoutes.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='tlsRoute',
        request_type_name='NetworkservicesProjectsLocationsTlsRoutesPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def TestIamPermissions(self, request, global_params=None):
      r"""Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may "fail open" without warning.

      Args:
        request: (NetworkservicesProjectsLocationsTlsRoutesTestIamPermissionsRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (TestIamPermissionsResponse) The response message.
      """
      config = self.GetMethodConfig('TestIamPermissions')
      return self._RunMethod(
          config, request, global_params=global_params)

    TestIamPermissions.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/tlsRoutes/{tlsRoutesId}:testIamPermissions',
        http_method='POST',
        method_id='networkservices.projects.locations.tlsRoutes.testIamPermissions',
        ordered_params=['resource'],
        path_params=['resource'],
        query_params=[],
        relative_path='v1alpha1/{+resource}:testIamPermissions',
        request_field='testIamPermissionsRequest',
        request_type_name='NetworkservicesProjectsLocationsTlsRoutesTestIamPermissionsRequest',
        response_type_name='TestIamPermissionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsWasmActionsService(base_api.BaseApiService):
    """Service class for the projects_locations_wasmActions resource."""

    _NAME = 'projects_locations_wasmActions'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsWasmActionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new `WasmAction` resource in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsWasmActionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/wasmActions',
        http_method='POST',
        method_id='networkservices.projects.locations.wasmActions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['wasmActionId'],
        relative_path='v1alpha1/{+parent}/wasmActions',
        request_field='wasmAction',
        request_type_name='NetworkservicesProjectsLocationsWasmActionsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified `WasmAction` resource.

      Args:
        request: (NetworkservicesProjectsLocationsWasmActionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/wasmActions/{wasmActionsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.wasmActions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsWasmActionsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of the specified `WasmAction` resource.

      Args:
        request: (NetworkservicesProjectsLocationsWasmActionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WasmAction) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/wasmActions/{wasmActionsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.wasmActions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsWasmActionsGetRequest',
        response_type_name='WasmAction',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `WasmAction` resources in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsWasmActionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWasmActionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/wasmActions',
        http_method='GET',
        method_id='networkservices.projects.locations.wasmActions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/wasmActions',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsWasmActionsListRequest',
        response_type_name='ListWasmActionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsWasmPluginsVersionsService(base_api.BaseApiService):
    """Service class for the projects_locations_wasmPlugins_versions resource."""

    _NAME = 'projects_locations_wasmPlugins_versions'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsWasmPluginsVersionsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new `WasmPluginVersion` resource in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsVersionsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}/versions',
        http_method='POST',
        method_id='networkservices.projects.locations.wasmPlugins.versions.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['wasmPluginVersionId'],
        relative_path='v1alpha1/{+parent}/versions',
        request_field='wasmPluginVersion',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsVersionsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified `WasmPluginVersion` resource.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsVersionsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}/versions/{versionsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.wasmPlugins.versions.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsVersionsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of the specified `WasmPluginVersion` resource.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsVersionsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WasmPluginVersion) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}/versions/{versionsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.wasmPlugins.versions.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsVersionsGetRequest',
        response_type_name='WasmPluginVersion',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `WasmPluginVersion` resources in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsVersionsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWasmPluginVersionsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}/versions',
        http_method='GET',
        method_id='networkservices.projects.locations.wasmPlugins.versions.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/versions',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsVersionsListRequest',
        response_type_name='ListWasmPluginVersionsResponse',
        supports_download=False,
    )

  class ProjectsLocationsWasmPluginsService(base_api.BaseApiService):
    """Service class for the projects_locations_wasmPlugins resource."""

    _NAME = 'projects_locations_wasmPlugins'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsWasmPluginsService, self).__init__(client)
      self._upload_configs = {
          }

    def Create(self, request, global_params=None):
      r"""Creates a new `WasmPlugin` resource in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsCreateRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Create')
      return self._RunMethod(
          config, request, global_params=global_params)

    Create.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/wasmPlugins',
        http_method='POST',
        method_id='networkservices.projects.locations.wasmPlugins.create',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['wasmPluginId'],
        relative_path='v1alpha1/{+parent}/wasmPlugins',
        request_field='wasmPlugin',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsCreateRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Delete(self, request, global_params=None):
      r"""Deletes the specified `WasmPlugin` resource.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsDeleteRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Delete')
      return self._RunMethod(
          config, request, global_params=global_params)

    Delete.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}',
        http_method='DELETE',
        method_id='networkservices.projects.locations.wasmPlugins.delete',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['force'],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsDeleteRequest',
        response_type_name='Operation',
        supports_download=False,
    )

    def Get(self, request, global_params=None):
      r"""Gets details of the specified `WasmPlugin` resource.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (WasmPlugin) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.wasmPlugins.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsGetRequest',
        response_type_name='WasmPlugin',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists `WasmPlugin` resources in a given project and location.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListWasmPluginsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/wasmPlugins',
        http_method='GET',
        method_id='networkservices.projects.locations.wasmPlugins.list',
        ordered_params=['parent'],
        path_params=['parent'],
        query_params=['pageSize', 'pageToken'],
        relative_path='v1alpha1/{+parent}/wasmPlugins',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsListRequest',
        response_type_name='ListWasmPluginsResponse',
        supports_download=False,
    )

    def Patch(self, request, global_params=None):
      r"""Updates the parameters of the specified `WasmPlugin` resource.

      Args:
        request: (NetworkservicesProjectsLocationsWasmPluginsPatchRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Operation) The response message.
      """
      config = self.GetMethodConfig('Patch')
      return self._RunMethod(
          config, request, global_params=global_params)

    Patch.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}/wasmPlugins/{wasmPluginsId}',
        http_method='PATCH',
        method_id='networkservices.projects.locations.wasmPlugins.patch',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['updateMask'],
        relative_path='v1alpha1/{+name}',
        request_field='wasmPlugin',
        request_type_name='NetworkservicesProjectsLocationsWasmPluginsPatchRequest',
        response_type_name='Operation',
        supports_download=False,
    )

  class ProjectsLocationsService(base_api.BaseApiService):
    """Service class for the projects_locations resource."""

    _NAME = 'projects_locations'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsLocationsService, self).__init__(client)
      self._upload_configs = {
          }

    def Get(self, request, global_params=None):
      r"""Gets information about a location.

      Args:
        request: (NetworkservicesProjectsLocationsGetRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (Location) The response message.
      """
      config = self.GetMethodConfig('Get')
      return self._RunMethod(
          config, request, global_params=global_params)

    Get.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations/{locationsId}',
        http_method='GET',
        method_id='networkservices.projects.locations.get',
        ordered_params=['name'],
        path_params=['name'],
        query_params=[],
        relative_path='v1alpha1/{+name}',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsGetRequest',
        response_type_name='Location',
        supports_download=False,
    )

    def List(self, request, global_params=None):
      r"""Lists information about the supported locations for this service.

      Args:
        request: (NetworkservicesProjectsLocationsListRequest) input message
        global_params: (StandardQueryParameters, default: None) global arguments
      Returns:
        (ListLocationsResponse) The response message.
      """
      config = self.GetMethodConfig('List')
      return self._RunMethod(
          config, request, global_params=global_params)

    List.method_config = lambda: base_api.ApiMethodInfo(
        flat_path='v1alpha1/projects/{projectsId}/locations',
        http_method='GET',
        method_id='networkservices.projects.locations.list',
        ordered_params=['name'],
        path_params=['name'],
        query_params=['filter', 'pageSize', 'pageToken'],
        relative_path='v1alpha1/{+name}/locations',
        request_field='',
        request_type_name='NetworkservicesProjectsLocationsListRequest',
        response_type_name='ListLocationsResponse',
        supports_download=False,
    )

  class ProjectsService(base_api.BaseApiService):
    """Service class for the projects resource."""

    _NAME = 'projects'

    def __init__(self, client):
      super(NetworkservicesV1alpha1.ProjectsService, self).__init__(client)
      self._upload_configs = {
          }

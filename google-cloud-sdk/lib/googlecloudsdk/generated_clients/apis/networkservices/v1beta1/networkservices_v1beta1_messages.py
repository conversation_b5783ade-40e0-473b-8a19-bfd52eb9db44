"""Generated message classes for networkservices version v1beta1.

"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding
from apitools.base.py import extra_types


package = 'networkservices'


class Address(_messages.Message):
  r"""Address can be either an IP address and port number, or a Unix domain
  socket name.

  Fields:
    socketAddress: Specifies an IP:Port address.
    uds: Specifies an Unix Domain Socket.
  """

  socketAddress = _messages.MessageField('AddressSocketAddress', 1)
  uds = _messages.StringField(2)


class AddressSocketAddress(_messages.Message):
  r"""Specifies an IP:Port address.

  Fields:
    address: Required. Specifies an IPV4 address. CIDR are not allowed.
    port: Required. Specifies the port.
  """

  address = _messages.StringField(1)
  port = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class AuditConfig(_messages.Message):
  r"""Specifies the audit configuration for a service. The configuration
  determines which permission types are logged, and what identities, if any,
  are exempted from logging. An AuditConfig must have one or more
  AuditLogConfigs. If there are AuditConfigs for both `allServices` and a
  specific service, the union of the two AuditConfigs is used for that
  service: the log_types specified in each AuditConfig are enabled, and the
  exempted_members in each AuditLogConfig are exempted. Example Policy with
  multiple AuditConfigs: { "audit_configs": [ { "service": "allServices",
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" }, { "log_type":
  "ADMIN_READ" } ] }, { "service": "sampleservice.googleapis.com",
  "audit_log_configs": [ { "log_type": "DATA_READ" }, { "log_type":
  "DATA_WRITE", "exempted_members": [ "user:<EMAIL>" ] } ] } ] } For
  sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
  logging. It also exempts `<EMAIL>` from DATA_READ logging, and
  `<EMAIL>` from DATA_WRITE logging.

  Fields:
    auditLogConfigs: The configuration for logging of each type of permission.
    service: Specifies a service that will be enabled for audit logging. For
      example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
      `allServices` is a special value that covers all services.
  """

  auditLogConfigs = _messages.MessageField('AuditLogConfig', 1, repeated=True)
  service = _messages.StringField(2)


class AuditLogConfig(_messages.Message):
  r"""Provides the configuration for logging a type of permissions. Example: {
  "audit_log_configs": [ { "log_type": "DATA_READ", "exempted_members": [
  "user:<EMAIL>" ] }, { "log_type": "DATA_WRITE" } ] } This enables
  'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from
  DATA_READ logging.

  Enums:
    LogTypeValueValuesEnum: The log type that this config enables.

  Fields:
    exemptedMembers: Specifies the identities that do not cause logging for
      this type of permission. Follows the same format of Binding.members.
    ignoreChildExemptions: A boolean attribute.
    logType: The log type that this config enables.
  """

  class LogTypeValueValuesEnum(_messages.Enum):
    r"""The log type that this config enables.

    Values:
      LOG_TYPE_UNSPECIFIED: Default case. Should never be this.
      ADMIN_READ: Admin reads. Example: CloudIAM getIamPolicy
      DATA_WRITE: Data writes. Example: CloudSQL Users create
      DATA_READ: Data reads. Example: CloudSQL Users list
    """
    LOG_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    DATA_WRITE = 2
    DATA_READ = 3

  exemptedMembers = _messages.StringField(1, repeated=True)
  ignoreChildExemptions = _messages.BooleanField(2)
  logType = _messages.EnumField('LogTypeValueValuesEnum', 3)


class AuthorizationLoggingOptions(_messages.Message):
  r"""Authorization-related information used by Cloud Audit Logging.

  Enums:
    PermissionTypeValueValuesEnum: The type of the permission that was
      checked.

  Fields:
    permissionType: The type of the permission that was checked.
  """

  class PermissionTypeValueValuesEnum(_messages.Enum):
    r"""The type of the permission that was checked.

    Values:
      PERMISSION_TYPE_UNSPECIFIED: Default. Should not be used.
      ADMIN_READ: A read of admin (meta) data.
      ADMIN_WRITE: A write of admin (meta) data.
      DATA_READ: A read of standard data.
      DATA_WRITE: A write of standard data.
    """
    PERMISSION_TYPE_UNSPECIFIED = 0
    ADMIN_READ = 1
    ADMIN_WRITE = 2
    DATA_READ = 3
    DATA_WRITE = 4

  permissionType = _messages.EnumField('PermissionTypeValueValuesEnum', 1)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    bindingId: A string attribute.
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  bindingId = _messages.StringField(1)
  condition = _messages.MessageField('Expr', 2)
  members = _messages.StringField(3, repeated=True)
  role = _messages.StringField(4)


class CancelOperationRequest(_messages.Message):
  r"""The request message for Operations.CancelOperation."""


class Condition(_messages.Message):
  r"""A condition to be met.

  Enums:
    IamValueValuesEnum: Trusted attributes supplied by the IAM system.
    OpValueValuesEnum: An operator to apply the subject with.
    SysValueValuesEnum: Trusted attributes supplied by any service that owns
      resources and uses the IAM system for access control.

  Fields:
    iam: Trusted attributes supplied by the IAM system.
    op: An operator to apply the subject with.
    svc: Trusted attributes discharged by the service.
    sys: Trusted attributes supplied by any service that owns resources and
      uses the IAM system for access control.
    values: The objects of the condition.
  """

  class IamValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by the IAM system.

    Values:
      NO_ATTR: Default non-attribute.
      AUTHORITY: Either principal or (if present) authority selector.
      ATTRIBUTION: The principal (even if an authority selector is present),
        which must only be used for attribution, not authorization.
      SECURITY_REALM: Any of the security realms in the IAMContext
        (go/security-realms). When used with IN, the condition indicates "any
        of the request's realms match one of the given values; with NOT_IN,
        "none of the realms match any of the given values". Note that a value
        can be: - 'self:campus' (i.e., clients that are in the same campus) -
        'self:metro' (i.e., clients that are in the same metro) - 'self:cloud-
        region' (i.e., allow connections from clients that are in the same
        cloud region) - 'self:prod-region' (i.e., allow connections from
        clients that are in the same prod region) - 'guardians' (i.e., allow
        connections from its guardian realms. See go/security-realms-
        glossary#guardian for more information.) - 'self' [DEPRECATED] (i.e.,
        allow connections from clients that are in the same security realm,
        which is currently but not guaranteed to be campus-sized) - a realm
        (e.g., 'campus-abc') - a realm group (e.g., 'realms-for-borg-cell-xx',
        see: go/realm-groups) A match is determined by a realm group
        membership check performed by a RealmAclRep object (go/realm-acl-
        howto). It is not permitted to grant access based on the *absence* of
        a realm, so realm conditions can only be used in a "positive" context
        (e.g., ALLOW/IN or DENY/NOT_IN).
      APPROVER: An approver (distinct from the requester) that has authorized
        this request. When used with IN, the condition indicates that one of
        the approvers associated with the request matches the specified
        principal, or is a member of the specified group. Approvers can only
        grant additional access, and are thus only used in a strictly positive
        context (e.g. ALLOW/IN or DENY/NOT_IN).
      JUSTIFICATION_TYPE: What types of justifications have been supplied with
        this request. String values should match enum names from
        security.credentials.JustificationType, e.g. "MANUAL_STRING". It is
        not permitted to grant access based on the *absence* of a
        justification, so justification conditions can only be used in a
        "positive" context (e.g., ALLOW/IN or DENY/NOT_IN). Multiple
        justifications, e.g., a Buganizer ID and a manually-entered reason,
        are normal and supported.
      CREDENTIALS_TYPE: What type of credentials have been supplied with this
        request. String values should match enum names from
        security_loas_l2.CredentialsType - currently, only
        CREDS_TYPE_EMERGENCY is supported. It is not permitted to grant access
        based on the *absence* of a credentials type, so the conditions can
        only be used in a "positive" context (e.g., ALLOW/IN or DENY/NOT_IN).
      CREDS_ASSERTION: EXPERIMENTAL -- DO NOT USE. The conditions can only be
        used in a "positive" context (e.g., ALLOW/IN or DENY/NOT_IN).
    """
    NO_ATTR = 0
    AUTHORITY = 1
    ATTRIBUTION = 2
    SECURITY_REALM = 3
    APPROVER = 4
    JUSTIFICATION_TYPE = 5
    CREDENTIALS_TYPE = 6
    CREDS_ASSERTION = 7

  class OpValueValuesEnum(_messages.Enum):
    r"""An operator to apply the subject with.

    Values:
      NO_OP: Default no-op.
      EQUALS: DEPRECATED. Use IN instead.
      NOT_EQUALS: DEPRECATED. Use NOT_IN instead.
      IN: The condition is true if the subject (or any element of it if it is
        a set) matches any of the supplied values.
      NOT_IN: The condition is true if the subject (or every element of it if
        it is a set) matches none of the supplied values.
      DISCHARGED: Subject is discharged
    """
    NO_OP = 0
    EQUALS = 1
    NOT_EQUALS = 2
    IN = 3
    NOT_IN = 4
    DISCHARGED = 5

  class SysValueValuesEnum(_messages.Enum):
    r"""Trusted attributes supplied by any service that owns resources and
    uses the IAM system for access control.

    Values:
      NO_ATTR: Default non-attribute type
      REGION: Region of the resource
      SERVICE: Service name
      NAME: Resource name
      IP: IP address of the caller
    """
    NO_ATTR = 0
    REGION = 1
    SERVICE = 2
    NAME = 3
    IP = 4

  iam = _messages.EnumField('IamValueValuesEnum', 1)
  op = _messages.EnumField('OpValueValuesEnum', 2)
  svc = _messages.StringField(3)
  sys = _messages.EnumField('SysValueValuesEnum', 4)
  values = _messages.StringField(5, repeated=True)


class CreateReferenceRequest(_messages.Message):
  r"""The CreateReferenceRequest request.

  Fields:
    parent: Required. The parent resource name (target_resource of this
      reference). For example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}`.
    reference: Required. The reference to be created.
    referenceId: The unique id of this resource. Must be unique within a scope
      of a target resource, but does not have to be globally unique. Reference
      ID is part of resource name of the reference. Resource name is generated
      in the following way: {parent}/references/{reference_id}. Reference ID
      field is currently required but id auto generation might be added in the
      future. It can be any arbitrary string, either GUID or any other string,
      however CLHs can use preprocess callbacks to perform a custom
      validation.
    requestId: Optional. Request ID is an idempotency ID of the request. It
      must be a valid UUID. Zero UUID (00000000-0000-0000-0000-000000000000)
      is not supported.
  """

  parent = _messages.StringField(1)
  reference = _messages.MessageField('Reference', 2)
  referenceId = _messages.StringField(3)
  requestId = _messages.StringField(4)


class DeleteReferenceRequest(_messages.Message):
  r"""The DeleteReferenceRequest request.

  Fields:
    name: Required. Full resource name of the reference, in the following
      format:
      `//{targer_service}/{target_resource}/references/{reference_id}`. For
      example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}/references/{xyz}`.
    requestId: Optional. Request ID is an idempotency ID of the request. It
      must be a valid UUID. Zero UUID (00000000-0000-0000-0000-000000000000)
      is not supported.
  """

  name = _messages.StringField(1)
  requestId = _messages.StringField(2)


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class EndpointMatcher(_messages.Message):
  r"""A definition of a matcher that selects endpoints to which the policies
  should be applied.

  Fields:
    metadataLabelMatcher: The matcher is based on node metadata presented by
      xDS clients.
  """

  metadataLabelMatcher = _messages.MessageField('MetadataLabelMatcher', 1)


class EndpointPolicy(_messages.Message):
  r"""EndpointPolicy is a resource that helps apply desired configuration on
  the endpoints that match specific criteria. For example, this resource can
  be used to apply "authentication config" an all endpoints that serve on port
  8080.

  Enums:
    TypeValueValuesEnum: Required. The type of endpoint policy. This is
      primarily used to validate the configuration.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the
      EndpointPolicy resource.

  Fields:
    authentication: Optional. [Deprecated] Use serverTlsPolicy and
      clientTlsPolicy instead.
    authorizationPolicy: Optional. This field specifies the URL of
      AuthorizationPolicy resource that applies authorization policies to the
      inbound traffic at the matched endpoints. Refer to Authorization. If
      this field is not specified, authorization is disabled(no authz checks)
      for this endpoint.
    clientTlsPolicy: Optional. A URL referring to a ClientTlsPolicy resource.
      ClientTlsPolicy can be set to specify the authentication for traffic
      from the proxy to the actual endpoints. More specifically, it is applied
      to the outgoing traffic from the proxy to the endpoint. This is
      typically used for sidecar model where the proxy identifies itself as
      endpoint to the control plane, with the connection between sidecar and
      endpoint requiring authentication. If this field is not set,
      authentication is disabled(open). Applicable only when
      EndpointPolicyType is SIDECAR_PROXY.
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    endpointMatcher: Required. A matcher that selects endpoints to which the
      policies should be applied.
    internalCaller: Optional. A flag set to identify internal controllers
      Setting this will trigger a P4SA check to validate the caller is from an
      allowlisted service's P4SA even if other optional fields are unset.
    labels: Optional. Set of label tags associated with the EndpointPolicy
      resource.
    name: Required. Name of the EndpointPolicy resource. It matches pattern
      `projects/{project}/locations/global/endpointPolicies/{endpoint_policy}`
      .
    serverTlsPolicy: Optional. A URL referring to ServerTlsPolicy resource.
      ServerTlsPolicy is used to determine the authentication policy to be
      applied to terminate the inbound traffic at the identified backends. If
      this field is not set, authentication is disabled(open) for this
      endpoint.
    targets: Optional. A list of targets this policy should apply to.
      Currently each target can only be fully qualified name of BackendService
      from the same project. e.g.:
      projects/*/backendServices/locations/global/
    trafficPortSelector: Optional. Port selector for the (matched) endpoints.
      If no port selector is provided, the matched config is applied to all
      ports.
    type: Required. The type of endpoint policy. This is primarily used to
      validate the configuration.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Required. The type of endpoint policy. This is primarily used to
    validate the configuration.

    Values:
      ENDPOINT_POLICY_TYPE_UNSPECIFIED: Default value. Must not be used.
      SIDECAR_PROXY: Represents a proxy deployed as a sidecar.
      GRPC_SERVER: Represents a proxyless gRPC backend.
    """
    ENDPOINT_POLICY_TYPE_UNSPECIFIED = 0
    SIDECAR_PROXY = 1
    GRPC_SERVER = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the EndpointPolicy
    resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  authentication = _messages.StringField(1)
  authorizationPolicy = _messages.StringField(2)
  clientTlsPolicy = _messages.StringField(3)
  createTime = _messages.StringField(4)
  description = _messages.StringField(5)
  endpointMatcher = _messages.MessageField('EndpointMatcher', 6)
  internalCaller = _messages.BooleanField(7)
  labels = _messages.MessageField('LabelsValue', 8)
  name = _messages.StringField(9)
  serverTlsPolicy = _messages.StringField(10)
  targets = _messages.StringField(11, repeated=True)
  trafficPortSelector = _messages.MessageField('TrafficPortSelector', 12)
  type = _messages.EnumField('TypeValueValuesEnum', 13)
  updateTime = _messages.StringField(14)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class Gateway(_messages.Message):
  r"""Gateway represents the configuration for a proxy, typically a load
  balancer. It captures the ip:port over which the services are exposed by the
  proxy, along with any policy configurations. Routes have reference to to
  Gateways to dictate how requests should be routed by this Gateway.

  Enums:
    IpVersionValueValuesEnum: Optional. The IP Version that will be used by
      this gateway. Valid options are IPV4 or IPV6. Default is IPV4.
    TypeValueValuesEnum: Immutable. The type of the customer managed gateway.
      This field is required. If unspecified, an error is returned.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the Gateway
      resource.

  Fields:
    addresses: Optional. Zero or one IPv4 or IPv6 address on which the Gateway
      will receive the traffic. When no address is provided, an IP from the
      subnetwork is allocated This field only applies to gateways of type
      'SECURE_WEB_GATEWAY'. Gateways of type 'OPEN_MESH' listen on 0.0.0.0 for
      IPv4 and :: for IPv6.
    authorizationPolicy: Optional. A fully-qualified AuthorizationPolicy URL
      reference. Specifies how traffic is authorized. If empty, authorization
      checks are disabled.
    certificateUrls: Optional. A fully-qualified Certificates URL reference.
      The proxy presents a Certificate (selected based on SNI) when
      establishing a TLS connection. This feature only applies to gateways of
      type 'SECURE_WEB_GATEWAY'.
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    gatewaySecurityPolicy: Optional. A fully-qualified GatewaySecurityPolicy
      URL reference. Defines how a server should apply security policy to
      inbound (VM to Proxy) initiated connections. For example:
      `projects/*/locations/*/gatewaySecurityPolicies/swg-policy`. This policy
      is specific to gateways of type 'SECURE_WEB_GATEWAY'.
    internalCaller: Optional. A flag set to identify internal controllers
      Setting this will trigger a P4SA check to validate the caller is from an
      allowlisted service's P4SA even if other optional fields are unset.
    ipVersion: Optional. The IP Version that will be used by this gateway.
      Valid options are IPV4 or IPV6. Default is IPV4.
    labels: Optional. Set of label tags associated with the Gateway resource.
    name: Required. Name of the Gateway resource. It matches pattern
      `projects/*/locations/*/gateways/`.
    network: Optional. The relative resource name identifying the VPC network
      that is using this configuration. For example:
      `projects/*/global/networks/network-1`. Currently, this field is
      specific to gateways of type 'SECURE_WEB_GATEWAY'.
    ports: Required. One or more port numbers (1-65535), on which the Gateway
      will receive traffic. The proxy binds to the specified ports. Gateways
      of type 'SECURE_WEB_GATEWAY' are limited to 1 port. Gateways of type
      'OPEN_MESH' listen on 0.0.0.0 for IPv4 and :: for IPv6 and support
      multiple ports.
    scope: Optional. Scope determines how configuration across multiple
      Gateway instances are merged. The configuration for multiple Gateway
      instances with the same scope will be merged as presented as a single
      coniguration to the proxy/load balancer. Max length 64 characters. Scope
      should start with a letter and can only have letters, numbers, hyphens.
    securityPolicy: Optional. A fully-qualified GatewaySecurityPolicy URL
      reference. Defines how a server should apply security policy to inbound
      (VM to Proxy) initiated connections. This policy is specific to gateways
      of type 'SECURE_WEB_GATEWAY'. DEPRECATED!!!! Use the
      gateway_security_policy field instead.
    selfLink: Output only. Server-defined URL of this resource
    serverTlsPolicy: Optional. A fully-qualified ServerTLSPolicy URL
      reference. Specifies how TLS traffic is terminated. If empty, TLS
      termination is disabled.
    subnetwork: Optional. The relative resource name identifying the
      subnetwork in which this SWG is allocated. For example:
      `projects/*/regions/us-central1/subnetworks/network-1` Currently, this
      field is specific to gateways of type 'SECURE_WEB_GATEWAY".
    type: Immutable. The type of the customer managed gateway. This field is
      required. If unspecified, an error is returned.
    updateTime: Output only. The timestamp when the resource was updated.
    workloadContextSelectors: Optional. Selects the workload where the gateway
      should be applied to its targets. A gateway without a
      WorkloadContextSelector should always be applied to its targets when
      there is no conflict. If there are multiple WorkloadContextSelectors
      then the policy will be applied to all targets if ANY of the
      WorkloadContextSelectors match. Therefore these selectors can be
      combined in an OR fashion. This field is used for GSM only.
  """

  class IpVersionValueValuesEnum(_messages.Enum):
    r"""Optional. The IP Version that will be used by this gateway. Valid
    options are IPV4 or IPV6. Default is IPV4.

    Values:
      IP_VERSION_UNSPECIFIED: The type when IP version is not specified.
      IPV4: The type for IP version 4.
      IPV6: The type for IP version 6.
    """
    IP_VERSION_UNSPECIFIED = 0
    IPV4 = 1
    IPV6 = 2

  class TypeValueValuesEnum(_messages.Enum):
    r"""Immutable. The type of the customer managed gateway. This field is
    required. If unspecified, an error is returned.

    Values:
      TYPE_UNSPECIFIED: The type of the customer managed gateway is
        unspecified.
      OPEN_MESH: The type of the customer managed gateway is TrafficDirector
        Open Mesh.
      SECURE_WEB_GATEWAY: The type of the customer managed gateway is
        SecureWebGateway (SWG).
    """
    TYPE_UNSPECIFIED = 0
    OPEN_MESH = 1
    SECURE_WEB_GATEWAY = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the Gateway resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  addresses = _messages.StringField(1, repeated=True)
  authorizationPolicy = _messages.StringField(2)
  certificateUrls = _messages.StringField(3, repeated=True)
  createTime = _messages.StringField(4)
  description = _messages.StringField(5)
  gatewaySecurityPolicy = _messages.StringField(6)
  internalCaller = _messages.BooleanField(7)
  ipVersion = _messages.EnumField('IpVersionValueValuesEnum', 8)
  labels = _messages.MessageField('LabelsValue', 9)
  name = _messages.StringField(10)
  network = _messages.StringField(11)
  ports = _messages.IntegerField(12, repeated=True, variant=_messages.Variant.INT32)
  scope = _messages.StringField(13)
  securityPolicy = _messages.StringField(14)
  selfLink = _messages.StringField(15)
  serverTlsPolicy = _messages.StringField(16)
  subnetwork = _messages.StringField(17)
  type = _messages.EnumField('TypeValueValuesEnum', 18)
  updateTime = _messages.StringField(19)
  workloadContextSelectors = _messages.MessageField('WorkloadContextSelector', 20, repeated=True)


class GetReferenceRequest(_messages.Message):
  r"""The GetReferenceRequest request.

  Fields:
    name: Required. Full resource name of the reference, in the following
      format:
      `//{target_service}/{target_resource}/references/{reference_id}`. For
      example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}/references/{xyz}`.
  """

  name = _messages.StringField(1)


class GrpcRoute(_messages.Message):
  r"""GrpcRoute is the resource defining how gRPC traffic routed by a Mesh or
  Gateway resource is routed.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the GrpcRoute
      resource.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    gateways: Optional. Gateways defines a list of gateways this GrpcRoute is
      attached to, as one of the routing rules to route the requests served by
      the gateway. Each gateway reference should match the pattern:
      `projects/*/locations/global/gateways/`
    hostnames: Required. Service hostnames with an optional port for which
      this route describes traffic. Format: [:] Hostname is the fully
      qualified domain name of a network host. This matches the RFC 1123
      definition of a hostname with 2 notable exceptions: - IPs are not
      allowed. - A hostname may be prefixed with a wildcard label (`*.`). The
      wildcard label must appear by itself as the first label. Hostname can be
      "precise" which is a domain name without the terminating dot of a
      network host (e.g. `foo.example.com`) or "wildcard", which is a domain
      name prefixed with a single wildcard label (e.g. `*.example.com`). Note
      that as per RFC1035 and RFC1123, a label must consist of lower case
      alphanumeric characters or '-', and must start and end with an
      alphanumeric character. No other punctuation is allowed. The routes
      associated with a Mesh or Gateway must have unique hostnames. If you
      attempt to attach multiple routes with conflicting hostnames, the
      configuration will be rejected. For example, while it is acceptable for
      routes for the hostnames `*.foo.bar.com` and `*.bar.com` to be
      associated with the same route, it is not possible to associate two
      routes both with `*.bar.com` or both with `bar.com`. If a port is
      specified, then gRPC clients must use the channel URI with the port to
      match this rule (i.e. "xds:///service:123"), otherwise they must supply
      the URI without a port (i.e. "xds:///service").
    internalCaller: Optional. A flag set to identify internal controllers
      Setting this will trigger a P4SA check to validate the caller is from an
      allowlisted service's P4SA even if other optional fields are unset.
    labels: Optional. Set of label tags associated with the GrpcRoute
      resource.
    meshes: Optional. Meshes defines a list of meshes this GrpcRoute is
      attached to, as one of the routing rules to route the requests served by
      the mesh. Each mesh reference should match the pattern:
      `projects/*/locations/global/meshes/`
    name: Required. Name of the GrpcRoute resource. It matches pattern
      `projects/*/locations/global/grpcRoutes/`
    routers: Optional. Routers define a list of routers this GrpcRoute should
      be served by. Each router reference should match the pattern:
      `projects/*/locations/global/routers/`
    rules: Required. A list of detailed rules defining how to route traffic.
      Within a single GrpcRoute, the GrpcRoute.RouteAction associated with the
      first matching GrpcRoute.RouteRule will be executed. At least one rule
      must be supplied.
    selfLink: Output only. Server-defined URL of this resource
    updateTime: Output only. The timestamp when the resource was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the GrpcRoute resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  gateways = _messages.StringField(3, repeated=True)
  hostnames = _messages.StringField(4, repeated=True)
  internalCaller = _messages.BooleanField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  meshes = _messages.StringField(7, repeated=True)
  name = _messages.StringField(8)
  routers = _messages.StringField(9, repeated=True)
  rules = _messages.MessageField('GrpcRouteRouteRule', 10, repeated=True)
  selfLink = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class GrpcRouteDestination(_messages.Message):
  r"""The destination to which traffic will be routed.

  Fields:
    serviceName: Required. The URL of a destination service to which to route
      traffic. Must refer to either a BackendService or
      ServiceDirectoryService.
    weight: Optional. Specifies the proportion of requests forwarded to the
      backend referenced by the serviceName field. This is computed as:
      weight/Sum(weights in this destination list). For non-zero values, there
      may be some epsilon from the exact proportion defined here depending on
      the precision an implementation supports. If only one serviceName is
      specified and it has a weight greater than 0, 100% of the traffic is
      forwarded to that backend. If weights are specified for any one service
      name, they need to be specified for all of them. If weights are
      unspecified for all services, then, traffic is distributed in equal
      proportions to all of them.
  """

  serviceName = _messages.StringField(1)
  weight = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GrpcRouteFaultInjectionPolicy(_messages.Message):
  r"""The specification for fault injection introduced into traffic to test
  the resiliency of clients to destination service failure. As part of fault
  injection, when clients send requests to a destination, delays can be
  introduced on a percentage of requests before sending those requests to the
  destination service. Similarly requests from clients can be aborted by for a
  percentage of requests.

  Fields:
    abort: The specification for aborting to client requests.
    delay: The specification for injecting delay to client requests.
  """

  abort = _messages.MessageField('GrpcRouteFaultInjectionPolicyAbort', 1)
  delay = _messages.MessageField('GrpcRouteFaultInjectionPolicyDelay', 2)


class GrpcRouteFaultInjectionPolicyAbort(_messages.Message):
  r"""Specification of how client requests are aborted as part of fault
  injection before being sent to a destination.

  Fields:
    httpStatus: The HTTP status code used to abort the request. The value must
      be between 200 and 599 inclusive.
    percentage: The percentage of traffic which will be aborted. The value
      must be between [0, 100]
  """

  httpStatus = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  percentage = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GrpcRouteFaultInjectionPolicyDelay(_messages.Message):
  r"""Specification of how client requests are delayed as part of fault
  injection before being sent to a destination.

  Fields:
    fixedDelay: Specify a fixed delay before forwarding the request.
    percentage: The percentage of traffic on which delay will be injected. The
      value must be between [0, 100]
  """

  fixedDelay = _messages.StringField(1)
  percentage = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class GrpcRouteHeaderMatch(_messages.Message):
  r"""A match against a collection of headers.

  Enums:
    TypeValueValuesEnum: Optional. Specifies how to match against the value of
      the header. If not specified, a default value of EXACT is used.

  Fields:
    key: Required. The key of the header.
    type: Optional. Specifies how to match against the value of the header. If
      not specified, a default value of EXACT is used.
    value: Required. The value of the header.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies how to match against the value of the header. If
    not specified, a default value of EXACT is used.

    Values:
      TYPE_UNSPECIFIED: Unspecified.
      EXACT: Will only match the exact value provided.
      REGULAR_EXPRESSION: Will match paths conforming to the prefix specified
        by value. RE2 syntax is supported.
    """
    TYPE_UNSPECIFIED = 0
    EXACT = 1
    REGULAR_EXPRESSION = 2

  key = _messages.StringField(1)
  type = _messages.EnumField('TypeValueValuesEnum', 2)
  value = _messages.StringField(3)


class GrpcRouteHeaderModifier(_messages.Message):
  r"""Specifies how to modify gRPC headers in a request or a response.

  Messages:
    AddValue: Add the headers with given map where key is the name of the
      header, value is the value of the header.
    SetValue: Completely overwrite/replace the headers with given map where
      key is the name of the header, value is the value of the header.

  Fields:
    add: Add the headers with given map where key is the name of the header,
      value is the value of the header.
    remove: Remove headers (matching by header names) specified in the list.
    set: Completely overwrite/replace the headers with given map where key is
      the name of the header, value is the value of the header.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AddValue(_messages.Message):
    r"""Add the headers with given map where key is the name of the header,
    value is the value of the header.

    Messages:
      AdditionalProperty: An additional property for a AddValue object.

    Fields:
      additionalProperties: Additional properties of type AddValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AddValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SetValue(_messages.Message):
    r"""Completely overwrite/replace the headers with given map where key is
    the name of the header, value is the value of the header.

    Messages:
      AdditionalProperty: An additional property for a SetValue object.

    Fields:
      additionalProperties: Additional properties of type SetValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SetValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  add = _messages.MessageField('AddValue', 1)
  remove = _messages.StringField(2, repeated=True)
  set = _messages.MessageField('SetValue', 3)


class GrpcRouteMethodMatch(_messages.Message):
  r"""Specifies a match against a method.

  Enums:
    TypeValueValuesEnum: Optional. Specifies how to match against the name. If
      not specified, a default value of "EXACT" is used.

  Fields:
    caseSensitive: Optional. Specifies that matches are case sensitive. The
      default value is true. case_sensitive must not be used with a type of
      REGULAR_EXPRESSION.
    grpcMethod: Required. Name of the method to match against. If unspecified,
      will match all methods.
    grpcService: Required. Name of the service to match against. If
      unspecified, will match all services.
    type: Optional. Specifies how to match against the name. If not specified,
      a default value of "EXACT" is used.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""Optional. Specifies how to match against the name. If not specified, a
    default value of "EXACT" is used.

    Values:
      TYPE_UNSPECIFIED: Unspecified.
      EXACT: Will only match the exact name provided.
      REGULAR_EXPRESSION: Will interpret grpc_method and grpc_service as
        regexes. RE2 syntax is supported.
    """
    TYPE_UNSPECIFIED = 0
    EXACT = 1
    REGULAR_EXPRESSION = 2

  caseSensitive = _messages.BooleanField(1)
  grpcMethod = _messages.StringField(2)
  grpcService = _messages.StringField(3)
  type = _messages.EnumField('TypeValueValuesEnum', 4)


class GrpcRouteRequestMirrorPolicy(_messages.Message):
  r"""Specifies the policy on how requests are mirrored to a separate mirrored
  destination service. The proxy does not wait for responses from the mirrored
  service. Prior to sending traffic to the mirrored service, the
  host/authority header is suffixed with -shadow.

  Fields:
    destination: The destination the requests will be mirrored to. The weight
      of the destination will be ignored.
  """

  destination = _messages.MessageField('GrpcRouteDestination', 1)


class GrpcRouteRetryPolicy(_messages.Message):
  r"""The specifications for retries.

  Fields:
    numRetries: Specifies the allowed number of retries. This number must be >
      0. If not specified, default to 1.
    perTryTimeout: If not specified, will use the timeout set in the
      RouteAction. If timeout is not set in the RouteAction, will use the
      largest timeout among all Backend Services associated with the route.
    retryConditions: - connect-failure: Router will retry on failures
      connecting to Backend Services, for example due to connection timeouts.
      - refused-stream: Router will retry if the backend service resets the
      stream with a REFUSED_STREAM error code. This reset type indicates that
      it is safe to retry. - cancelled: Router will retry if the gRPC status
      code in the response header is set to cancelled - deadline-exceeded:
      Router will retry if the gRPC status code in the response header is set
      to deadline-exceeded - resource-exhausted: Router will retry if the gRPC
      status code in the response header is set to resource-exhausted -
      unavailable: Router will retry if the gRPC status code in the response
      header is set to unavailable
  """

  numRetries = _messages.IntegerField(1, variant=_messages.Variant.UINT32)
  perTryTimeout = _messages.StringField(2)
  retryConditions = _messages.StringField(3, repeated=True)


class GrpcRouteRouteAction(_messages.Message):
  r"""Specifies how to route matched traffic.

  Fields:
    destinations: Optional. The destination services to which traffic should
      be forwarded. If multiple destinations are specified, traffic will be
      split between Backend Service(s) according to the weight field of these
      destinations.
    faultInjectionPolicy: Optional. The specification for fault injection
      introduced into traffic to test the resiliency of clients to destination
      service failure. As part of fault injection, when clients send requests
      to a destination, delays can be introduced on a percentage of requests
      before sending those requests to the destination service. Similarly
      requests from clients can be aborted by for a percentage of requests.
      timeout and retry_policy will be ignored by clients that are configured
      with a fault_injection_policy
    requestHeaderModifier: Optional. The specification for modifying the
      headers of a matching request prior to delivery of the request to the
      destination.
    requestMirrorPolicy: Optional. Specifies the policy on how requests
      intended for the route's destination are mirrored to a separate mirrored
      destination. The proxy will not wait for the mirrored destination to
      respond before returning the response. Prior to sending traffic to the
      mirrored service, the host / authority header is suffixed with -shadow.
    responseHeaderModifier: Optional. The specification for modifying the
      headers of a response prior to sending the response back to the client.
    retryPolicy: Optional. Specifies the retry policy associated with this
      route.
    timeout: Optional. Specifies the timeout for selected route. Timeout is
      computed from the time the request has been fully processed (i.e. end of
      stream) up until the response has been completely processed. Timeout
      includes all retries.
    urlRewrite: Optional. The specification for rewrite URL before forwarding
      requests to the destination.
  """

  destinations = _messages.MessageField('GrpcRouteDestination', 1, repeated=True)
  faultInjectionPolicy = _messages.MessageField('GrpcRouteFaultInjectionPolicy', 2)
  requestHeaderModifier = _messages.MessageField('GrpcRouteHeaderModifier', 3)
  requestMirrorPolicy = _messages.MessageField('GrpcRouteRequestMirrorPolicy', 4)
  responseHeaderModifier = _messages.MessageField('GrpcRouteHeaderModifier', 5)
  retryPolicy = _messages.MessageField('GrpcRouteRetryPolicy', 6)
  timeout = _messages.StringField(7)
  urlRewrite = _messages.MessageField('GrpcRouteURLRewrite', 8)


class GrpcRouteRouteMatch(_messages.Message):
  r"""Criteria for matching traffic. A RouteMatch will be considered to match
  when all supplied fields match.

  Fields:
    headers: Optional. Specifies a collection of headers to match.
    method: Optional. A gRPC method to match against. If this field is empty
      or omitted, will match all methods.
  """

  headers = _messages.MessageField('GrpcRouteHeaderMatch', 1, repeated=True)
  method = _messages.MessageField('GrpcRouteMethodMatch', 2)


class GrpcRouteRouteRule(_messages.Message):
  r"""Describes how to route traffic.

  Fields:
    action: Required. A detailed rule defining how to route traffic. This
      field is required.
    matches: Optional. Matches define conditions used for matching the rule
      against incoming gRPC requests. Each match is independent, i.e. this
      rule will be matched if ANY one of the matches is satisfied. If no
      matches field is specified, this rule will unconditionally match
      traffic.
  """

  action = _messages.MessageField('GrpcRouteRouteAction', 1)
  matches = _messages.MessageField('GrpcRouteRouteMatch', 2, repeated=True)


class GrpcRouteURLRewrite(_messages.Message):
  r"""The specification to modify the URL of the request, prior to forwarding
  the request to the destination.

  Fields:
    hostRewrite: Prior to forwarding the request to the selected destination,
      the requests host header is replaced by this value.
    pathPrefixRewrite: Prior to forwarding the request to the selected
      destination, the matching portion of the requests path is replaced by
      this value.
  """

  hostRewrite = _messages.StringField(1)
  pathPrefixRewrite = _messages.StringField(2)


class HttpRoute(_messages.Message):
  r"""HttpRoute is the resource defining how HTTP traffic should be routed by
  a Mesh or Gateway resource.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the HttpRoute
      resource.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    gateways: Optional. Gateways defines a list of gateways this HttpRoute is
      attached to, as one of the routing rules to route the requests served by
      the gateway. Each gateway reference should match the pattern:
      `projects/*/locations/global/gateways/`
    hostnames: Required. Hostnames define a set of hosts that should match
      against the HTTP host header to select a HttpRoute to process the
      request. Hostname is the fully qualified domain name of a network host,
      as defined by RFC 1123 with the exception that: - IPs are not allowed. -
      A hostname may be prefixed with a wildcard label (`*.`). The wildcard
      label must appear by itself as the first label. Hostname can be
      "precise" which is a domain name without the terminating dot of a
      network host (e.g. `foo.example.com`) or "wildcard", which is a domain
      name prefixed with a single wildcard label (e.g. `*.example.com`). Note
      that as per RFC1035 and RFC1123, a label must consist of lower case
      alphanumeric characters or '-', and must start and end with an
      alphanumeric character. No other punctuation is allowed. The routes
      associated with a Mesh or Gateways must have unique hostnames. If you
      attempt to attach multiple routes with conflicting hostnames, the
      configuration will be rejected. For example, while it is acceptable for
      routes for the hostnames `*.foo.bar.com` and `*.bar.com` to be
      associated with the same Mesh (or Gateways under the same scope), it is
      not possible to associate two routes both with `*.bar.com` or both with
      `bar.com`.
    internalCaller: Optional. A flag set to identify internal controllers
      Setting this will trigger a P4SA check to validate the caller is from an
      allowlisted service's P4SA even if other optional fields are unset.
    labels: Optional. Set of label tags associated with the HttpRoute
      resource.
    listenOn: The address to listen on. This can be either an IP address and
      port number, or a Unix domain socket name. When attached to a Mesh,
      defaults to 0.0.0.0:Mesh.InterceptionPort (which is 15001 by default).
      Should not be set when attached to a Gateway.
    meshes: Optional. Meshes defines a list of meshes this HttpRoute is
      attached to, as one of the routing rules to route the requests served by
      the mesh. Each mesh reference should match the pattern:
      `projects/*/locations/global/meshes/` The attached Mesh should be of a
      type SIDECAR
    name: Required. Name of the HttpRoute resource. It matches pattern
      `projects/*/locations/global/httpRoutes/http_route_name>`.
    routers: Optional. Routers define a list of routers this HttpRoute should
      be served by. Each router reference should match the pattern:
      `projects/*/locations/global/routers/` The attached Router should be of
      a type PROXY
    rules: Required. Rules that define how traffic is routed and handled.
      Rules will be matched sequentially based on the RouteMatch specified for
      the rule.
    selfLink: Output only. Server-defined URL of this resource
    updateTime: Output only. The timestamp when the resource was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the HttpRoute resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  gateways = _messages.StringField(3, repeated=True)
  hostnames = _messages.StringField(4, repeated=True)
  internalCaller = _messages.BooleanField(5)
  labels = _messages.MessageField('LabelsValue', 6)
  listenOn = _messages.MessageField('Address', 7)
  meshes = _messages.StringField(8, repeated=True)
  name = _messages.StringField(9)
  routers = _messages.StringField(10, repeated=True)
  rules = _messages.MessageField('HttpRouteRouteRule', 11, repeated=True)
  selfLink = _messages.StringField(12)
  updateTime = _messages.StringField(13)


class HttpRouteCorsPolicy(_messages.Message):
  r"""The Specification for allowing client side cross-origin requests.

  Fields:
    allowCredentials: In response to a preflight request, setting this to true
      indicates that the actual request can include user credentials. This
      translates to the Access-Control-Allow-Credentials header. Default value
      is false.
    allowHeaders: Specifies the content for Access-Control-Allow-Headers
      header.
    allowMethods: Specifies the content for Access-Control-Allow-Methods
      header.
    allowOriginRegexes: Specifies the regular expression patterns that match
      allowed origins. For regular expression grammar, please see
      https://github.com/google/re2/wiki/Syntax.
    allowOrigins: Specifies the list of origins that will be allowed to do
      CORS requests. An origin is allowed if it matches either an item in
      allow_origins or an item in allow_origin_regexes.
    disabled: If true, the CORS policy is disabled. The default value is
      false, which indicates that the CORS policy is in effect.
    exposeHeaders: Specifies the content for Access-Control-Expose-Headers
      header.
    maxAge: Specifies how long result of a preflight request can be cached in
      seconds. This translates to the Access-Control-Max-Age header.
  """

  allowCredentials = _messages.BooleanField(1)
  allowHeaders = _messages.StringField(2, repeated=True)
  allowMethods = _messages.StringField(3, repeated=True)
  allowOriginRegexes = _messages.StringField(4, repeated=True)
  allowOrigins = _messages.StringField(5, repeated=True)
  disabled = _messages.BooleanField(6)
  exposeHeaders = _messages.StringField(7, repeated=True)
  maxAge = _messages.StringField(8)


class HttpRouteDestination(_messages.Message):
  r"""Specifications of a destination to which the request should be routed
  to.

  Fields:
    serviceName: The URL of a BackendService to route traffic to.
    weight: Specifies the proportion of requests forwarded to the backend
      referenced by the serviceName field. This is computed as:
      weight/Sum(weights in this destination list). For non-zero values, there
      may be some epsilon from the exact proportion defined here depending on
      the precision an implementation supports. If only one serviceName is
      specified and it has a weight greater than 0, 100% of the traffic is
      forwarded to that backend. If weights are specified for any one service
      name, they need to be specified for all of them. If weights are
      unspecified for all services, then, traffic is distributed in equal
      proportions to all of them.
  """

  serviceName = _messages.StringField(1)
  weight = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class HttpRouteFaultInjectionPolicy(_messages.Message):
  r"""The specification for fault injection introduced into traffic to test
  the resiliency of clients to destination service failure. As part of fault
  injection, when clients send requests to a destination, delays can be
  introduced by client proxy on a percentage of requests before sending those
  requests to the destination service. Similarly requests can be aborted by
  client proxy for a percentage of requests.

  Fields:
    abort: The specification for aborting to client requests.
    delay: The specification for injecting delay to client requests.
  """

  abort = _messages.MessageField('HttpRouteFaultInjectionPolicyAbort', 1)
  delay = _messages.MessageField('HttpRouteFaultInjectionPolicyDelay', 2)


class HttpRouteFaultInjectionPolicyAbort(_messages.Message):
  r"""Specification of how client requests are aborted as part of fault
  injection before being sent to a destination.

  Fields:
    httpStatus: The HTTP status code used to abort the request. The value must
      be between 200 and 599 inclusive.
    percentage: The percentage of traffic which will be aborted. The value
      must be between [0, 100]
  """

  httpStatus = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  percentage = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class HttpRouteFaultInjectionPolicyDelay(_messages.Message):
  r"""Specification of how client requests are delayed as part of fault
  injection before being sent to a destination.

  Fields:
    fixedDelay: Specify a fixed delay before forwarding the request.
    percentage: The percentage of traffic on which delay will be injected. The
      value must be between [0, 100]
  """

  fixedDelay = _messages.StringField(1)
  percentage = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class HttpRouteHeaderMatch(_messages.Message):
  r"""Specifies how to select a route rule based on HTTP request headers.

  Fields:
    exactMatch: The value of the header should match exactly the content of
      exact_match.
    header: The name of the HTTP header to match against.
    invertMatch: If specified, the match result will be inverted before
      checking. Default value is set to false.
    prefixMatch: The value of the header must start with the contents of
      prefix_match.
    presentMatch: A header with header_name must exist. The match takes place
      whether or not the header has a value.
    rangeMatch: If specified, the rule will match if the request header value
      is within the range.
    regexMatch: The value of the header must match the regular expression
      specified in regex_match. For regular expression grammar, please see:
      https://github.com/google/re2/wiki/Syntax
    suffixMatch: The value of the header must end with the contents of
      suffix_match.
  """

  exactMatch = _messages.StringField(1)
  header = _messages.StringField(2)
  invertMatch = _messages.BooleanField(3)
  prefixMatch = _messages.StringField(4)
  presentMatch = _messages.BooleanField(5)
  rangeMatch = _messages.MessageField('HttpRouteHeaderMatchIntegerRange', 6)
  regexMatch = _messages.StringField(7)
  suffixMatch = _messages.StringField(8)


class HttpRouteHeaderMatchIntegerRange(_messages.Message):
  r"""Represents an integer value range.

  Fields:
    end: End of the range (exclusive)
    start: Start of the range (inclusive)
  """

  end = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  start = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class HttpRouteHeaderModifier(_messages.Message):
  r"""The specification for modifying HTTP header in HTTP request and HTTP
  response.

  Messages:
    AddValue: Add the headers with given map where key is the name of the
      header, value is the value of the header.
    SetValue: Completely overwrite/replace the headers with given map where
      key is the name of the header, value is the value of the header.

  Fields:
    add: Add the headers with given map where key is the name of the header,
      value is the value of the header.
    remove: Remove headers (matching by header names) specified in the list.
    set: Completely overwrite/replace the headers with given map where key is
      the name of the header, value is the value of the header.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AddValue(_messages.Message):
    r"""Add the headers with given map where key is the name of the header,
    value is the value of the header.

    Messages:
      AdditionalProperty: An additional property for a AddValue object.

    Fields:
      additionalProperties: Additional properties of type AddValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AddValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class SetValue(_messages.Message):
    r"""Completely overwrite/replace the headers with given map where key is
    the name of the header, value is the value of the header.

    Messages:
      AdditionalProperty: An additional property for a SetValue object.

    Fields:
      additionalProperties: Additional properties of type SetValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a SetValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  add = _messages.MessageField('AddValue', 1)
  remove = _messages.StringField(2, repeated=True)
  set = _messages.MessageField('SetValue', 3)


class HttpRouteQueryParameterMatch(_messages.Message):
  r"""Specifications to match a query parameter in the request.

  Fields:
    exactMatch: The value of the query parameter must exactly match the
      contents of exact_match. Only one of exact_match, regex_match, or
      present_match must be set.
    presentMatch: Specifies that the QueryParameterMatcher matches if request
      contains query parameter, irrespective of whether the parameter has a
      value or not. Only one of exact_match, regex_match, or present_match
      must be set.
    queryParameter: The name of the query parameter to match.
    regexMatch: The value of the query parameter must match the regular
      expression specified by regex_match. For regular expression grammar,
      please see https://github.com/google/re2/wiki/Syntax Only one of
      exact_match, regex_match, or present_match must be set.
  """

  exactMatch = _messages.StringField(1)
  presentMatch = _messages.BooleanField(2)
  queryParameter = _messages.StringField(3)
  regexMatch = _messages.StringField(4)


class HttpRouteRedirect(_messages.Message):
  r"""The specification for redirecting traffic.

  Enums:
    ResponseCodeValueValuesEnum: The HTTP Status code to use for the redirect.

  Fields:
    hostRedirect: The host that will be used in the redirect response instead
      of the one that was supplied in the request.
    httpsRedirect: If set to true, the URL scheme in the redirected request is
      set to https. If set to false, the URL scheme of the redirected request
      will remain the same as that of the request. The default is set to
      false.
    pathRedirect: The path that will be used in the redirect response instead
      of the one that was supplied in the request. path_redirect can not be
      supplied together with prefix_redirect. Supply one alone or neither. If
      neither is supplied, the path of the original request will be used for
      the redirect.
    portRedirect: The port that will be used in the redirected request instead
      of the one that was supplied in the request.
    prefixRewrite: Indicates that during redirection, the matched prefix (or
      path) should be swapped with this value. This option allows URLs be
      dynamically created based on the request.
    responseCode: The HTTP Status code to use for the redirect.
    stripQuery: if set to true, any accompanying query portion of the original
      URL is removed prior to redirecting the request. If set to false, the
      query portion of the original URL is retained. The default is set to
      false.
  """

  class ResponseCodeValueValuesEnum(_messages.Enum):
    r"""The HTTP Status code to use for the redirect.

    Values:
      RESPONSE_CODE_UNSPECIFIED: Default value
      MOVED_PERMANENTLY_DEFAULT: Corresponds to 301.
      FOUND: Corresponds to 302.
      SEE_OTHER: Corresponds to 303.
      TEMPORARY_REDIRECT: Corresponds to 307. In this case, the request method
        will be retained.
      PERMANENT_REDIRECT: Corresponds to 308. In this case, the request method
        will be retained.
    """
    RESPONSE_CODE_UNSPECIFIED = 0
    MOVED_PERMANENTLY_DEFAULT = 1
    FOUND = 2
    SEE_OTHER = 3
    TEMPORARY_REDIRECT = 4
    PERMANENT_REDIRECT = 5

  hostRedirect = _messages.StringField(1)
  httpsRedirect = _messages.BooleanField(2)
  pathRedirect = _messages.StringField(3)
  portRedirect = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  prefixRewrite = _messages.StringField(5)
  responseCode = _messages.EnumField('ResponseCodeValueValuesEnum', 6)
  stripQuery = _messages.BooleanField(7)


class HttpRouteRequestMirrorPolicy(_messages.Message):
  r"""Specifies the policy on how requests are shadowed to a separate mirrored
  destination service. The proxy does not wait for responses from the shadow
  service. Prior to sending traffic to the shadow service, the host/authority
  header is suffixed with -shadow.

  Fields:
    destination: The destination the requests will be mirrored to. The weight
      of the destination will be ignored.
  """

  destination = _messages.MessageField('HttpRouteDestination', 1)


class HttpRouteRetryPolicy(_messages.Message):
  r"""The specifications for retries.

  Fields:
    numRetries: Specifies the allowed number of retries. This number must be >
      0. If not specified, default to 1.
    perTryTimeout: Specifies a non-zero timeout per retry attempt.
    retryConditions: Specifies one or more conditions when this retry policy
      applies. Valid values are: 5xx: Proxy will attempt a retry if the
      destination service responds with any 5xx response code, of if the
      destination service does not respond at all, example: disconnect, reset,
      read timeout, connection failure and refused streams. gateway-error:
      Similar to 5xx, but only applies to response codes 502, 503, 504. reset:
      Proxy will attempt a retry if the destination service does not respond
      at all (disconnect/reset/read timeout) connect-failure: Proxy will retry
      on failures connecting to destination for example due to connection
      timeouts. retriable-4xx: Proxy will retry fro retriable 4xx response
      codes. Currently the only retriable error supported is 409. refused-
      stream: Proxy will retry if the destination resets the stream with a
      REFUSED_STREAM error code. This reset type indicates that it is safe to
      retry.
  """

  numRetries = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  perTryTimeout = _messages.StringField(2)
  retryConditions = _messages.StringField(3, repeated=True)


class HttpRouteRouteAction(_messages.Message):
  r"""The specifications for routing traffic and applying associated policies.

  Fields:
    corsPolicy: The specification for allowing client side cross-origin
      requests.
    destinations: The destination to which traffic should be forwarded.
    faultInjectionPolicy: The specification for fault injection introduced
      into traffic to test the resiliency of clients to backend service
      failure. As part of fault injection, when clients send requests to a
      backend service, delays can be introduced on a percentage of requests
      before sending those requests to the backend service. Similarly requests
      from clients can be aborted for a percentage of requests. timeout and
      retry_policy will be ignored by clients that are configured with a
      fault_injection_policy
    originalDestination: If true, the matched traffic will use the destination
      ip and port of the original connection (as it was not processed by
      proxy) as the destination of the request. Only one of destinations,
      redirect, original_destination can be set.
    redirect: If set, the request is directed as configured by this field.
    requestHeaderModifier: The specification for modifying the headers of a
      matching request prior to delivery of the request to the destination.
    requestMirrorPolicy: Specifies the policy on how requests intended for the
      routes destination are shadowed to a separate mirrored destination.
      Proxy will not wait for the shadow destination to respond before
      returning the response. Prior to sending traffic to the shadow service,
      the host/authority header is suffixed with -shadow.
    responseHeaderModifier: The specification for modifying the headers of a
      response prior to sending the response back to the client.
    retryPolicy: Specifies the retry policy associated with this route.
    timeout: Specifies the timeout for selected route. Timeout is computed
      from the time the request has been fully processed (i.e. end of stream)
      up until the response has been completely processed. Timeout includes
      all retries.
    urlRewrite: The specification for rewrite URL before forwarding requests
      to the destination.
  """

  corsPolicy = _messages.MessageField('HttpRouteCorsPolicy', 1)
  destinations = _messages.MessageField('HttpRouteDestination', 2, repeated=True)
  faultInjectionPolicy = _messages.MessageField('HttpRouteFaultInjectionPolicy', 3)
  originalDestination = _messages.BooleanField(4)
  redirect = _messages.MessageField('HttpRouteRedirect', 5)
  requestHeaderModifier = _messages.MessageField('HttpRouteHeaderModifier', 6)
  requestMirrorPolicy = _messages.MessageField('HttpRouteRequestMirrorPolicy', 7)
  responseHeaderModifier = _messages.MessageField('HttpRouteHeaderModifier', 8)
  retryPolicy = _messages.MessageField('HttpRouteRetryPolicy', 9)
  timeout = _messages.StringField(10)
  urlRewrite = _messages.MessageField('HttpRouteURLRewrite', 11)


class HttpRouteRouteMatch(_messages.Message):
  r"""RouteMatch defines specifications used to match requests. If multiple
  match types are set, this RouteMatch will match if ALL type of matches are
  matched.

  Fields:
    fullPathMatch: The HTTP request path value should exactly match this
      value. Only one of full_path_match, prefix_match, or regex_match should
      be used.
    headers: Specifies a list of HTTP request headers to match against. ALL of
      the supplied headers must be matched.
    ignoreCase: Specifies if prefix_match and full_path_match matches are case
      sensitive. The default value is false.
    prefixMatch: The HTTP request path value must begin with specified
      prefix_match. prefix_match must begin with a /. Only one of
      full_path_match, prefix_match, or regex_match should be used.
    queryParameters: Specifies a list of query parameters to match against.
      ALL of the query parameters must be matched.
    regexMatch: The HTTP request path value must satisfy the regular
      expression specified by regex_match after removing any query parameters
      and anchor supplied with the original URL. For regular expression
      grammar, please see https://github.com/google/re2/wiki/Syntax Only one
      of full_path_match, prefix_match, or regex_match should be used.
  """

  fullPathMatch = _messages.StringField(1)
  headers = _messages.MessageField('HttpRouteHeaderMatch', 2, repeated=True)
  ignoreCase = _messages.BooleanField(3)
  prefixMatch = _messages.StringField(4)
  queryParameters = _messages.MessageField('HttpRouteQueryParameterMatch', 5, repeated=True)
  regexMatch = _messages.StringField(6)


class HttpRouteRouteRule(_messages.Message):
  r"""Specifies how to match traffic and how to route traffic when traffic is
  matched.

  Messages:
    MetadataValue: Optional. Set of label tags associated with the RouteRule
      resource.

  Fields:
    action: The detailed rule defining how to route matched traffic.
    matches: A list of matches define conditions used for matching the rule
      against incoming HTTP requests. Each match is independent, i.e. this
      rule will be matched if ANY one of the matches is satisfied. If no
      matches field is specified, this rule will unconditionally match
      traffic. If a default rule is desired to be configured, add a rule with
      no matches specified to the end of the rules list.
    metadata: Optional. Set of label tags associated with the RouteRule
      resource.
    workloadContextSelectors: Optional. Selects the workload where the route
      rule should be applied to its targets. A route rule without a
      WorkloadContextSelector should always be applied to its targets when
      there is no conflict. If there are multiple WorkloadContextSelectors
      then the policy will be applied to all targets if ANY of the
      WorkloadContextSelectors match. Therefore these selectors can be
      combined in an OR fashion.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Optional. Set of label tags associated with the RouteRule resource.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  action = _messages.MessageField('HttpRouteRouteAction', 1)
  matches = _messages.MessageField('HttpRouteRouteMatch', 2, repeated=True)
  metadata = _messages.MessageField('MetadataValue', 3)
  workloadContextSelectors = _messages.MessageField('WorkloadContextSelector', 4, repeated=True)


class HttpRouteURLRewrite(_messages.Message):
  r"""The specification for modifying the URL of the request, prior to
  forwarding the request to the destination.

  Fields:
    hostRewrite: Prior to forwarding the request to the selected destination,
      the requests host header is replaced by this value.
    pathPrefixRewrite: Prior to forwarding the request to the selected
      destination, the matching portion of the requests path is replaced by
      this value.
  """

  hostRewrite = _messages.StringField(1)
  pathPrefixRewrite = _messages.StringField(2)


class InvalidateCacheRequest(_messages.Message):
  r"""The request used by the `InvalidateCache` method.

  Fields:
    cacheTags: A list of cache tags used to identify cached objects. Cache
      tags are specified when the response is first cached, by setting the
      `Cache-Tag` response header at the origin. By default, all objects have
      a cache tag representing the HTTP status code of the response, the MIME
      content-type, and the origin. Multiple cache tags in the same
      revalidation request are treated as Boolean `OR` - for example, `tag1 OR
      tag2 OR tag3`. If a host and path are also specified, these are treated
      as Boolean `AND` with any tags. Up to 10 tags can be specified in a
      single invalidation request.
    host: The hostname to invalidate against. You can specify an exact or
      wildcard host based on the host component. For example,
      `video.example.com` or `*.example.com`.
    path: The path to invalidate against. You can specify an exact or wildcard
      path based on the a path component. For example,
      `/videos/hls/139123.mp4` or `/manifests/*`.
  """

  cacheTags = _messages.StringField(1, repeated=True)
  host = _messages.StringField(2)
  path = _messages.StringField(3)


class InvalidateCacheResponse(_messages.Message):
  r"""The response used by the `InvalidateCache` method."""


class ListEndpointPoliciesResponse(_messages.Message):
  r"""Response returned by the ListEndpointPolicies method.

  Fields:
    endpointPolicies: List of EndpointPolicy resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
  """

  endpointPolicies = _messages.MessageField('EndpointPolicy', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListGatewaysResponse(_messages.Message):
  r"""Response returned by the ListGateways method.

  Fields:
    gateways: List of Gateway resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    unreachable: Locations that could not be reached.
  """

  gateways = _messages.MessageField('Gateway', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListGrpcRoutesResponse(_messages.Message):
  r"""Response returned by the ListGrpcRoutes method.

  Fields:
    grpcRoutes: List of GrpcRoute resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
  """

  grpcRoutes = _messages.MessageField('GrpcRoute', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListHttpRoutesResponse(_messages.Message):
  r"""Response returned by the ListHttpRoutes method.

  Fields:
    httpRoutes: List of HttpRoute resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
  """

  httpRoutes = _messages.MessageField('HttpRoute', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListLocationsResponse(_messages.Message):
  r"""The response message for Locations.ListLocations.

  Fields:
    locations: A list of locations that matches the specified filter in the
      request.
    nextPageToken: The standard List next-page token.
  """

  locations = _messages.MessageField('Location', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMeshesResponse(_messages.Message):
  r"""Response returned by the ListMeshes method.

  Fields:
    meshes: List of Mesh resources.
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
  """

  meshes = _messages.MessageField('Mesh', 1, repeated=True)
  nextPageToken = _messages.StringField(2)


class ListMulticastConsumerAssociationsResponse(_messages.Message):
  r"""Message for response to listing MulticastConsumerAssociations

  Fields:
    multicastConsumerAssociations: The list of MulticastConsumerAssociation
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  multicastConsumerAssociations = _messages.MessageField('MulticastConsumerAssociation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMulticastDomainActivationsResponse(_messages.Message):
  r"""Message for response to listing MulticastDomainActivations

  Fields:
    multicastDomainActivations: The list of MulticastDomainActivation
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  multicastDomainActivations = _messages.MessageField('MulticastDomainActivation', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMulticastDomainsResponse(_messages.Message):
  r"""Message for response to listing MulticastDomains

  Fields:
    multicastDomains: The list of MulticastDomain
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  multicastDomains = _messages.MessageField('MulticastDomain', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMulticastGroupDefinitionsResponse(_messages.Message):
  r"""Message for response to listing MulticastGroupDefinitions

  Fields:
    multicastGroupDefinitions: The list of MulticastGroupDefinition
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  multicastGroupDefinitions = _messages.MessageField('MulticastGroupDefinition', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListMulticastGroupsResponse(_messages.Message):
  r"""Message for response to listing MulticastGroups

  Fields:
    multicastGroups: The list of MulticastGroup
    nextPageToken: A token identifying a page of results the server should
      return.
    unreachable: Locations that could not be reached.
  """

  multicastGroups = _messages.MessageField('MulticastGroup', 1, repeated=True)
  nextPageToken = _messages.StringField(2)
  unreachable = _messages.StringField(3, repeated=True)


class ListOperationsResponse(_messages.Message):
  r"""The response message for Operations.ListOperations.

  Fields:
    nextPageToken: The standard List next-page token.
    operations: A list of operations that matches the specified filter in the
      request.
  """

  nextPageToken = _messages.StringField(1)
  operations = _messages.MessageField('Operation', 2, repeated=True)


class ListReferencesRequest(_messages.Message):
  r"""The ListResourceMetadataRequest request.

  Fields:
    pageSize: The maximum number of items to return. If unspecified, server
      will pick an appropriate default. Server may return fewer items than
      requested. A caller should only rely on response's next_page_token to
      determine if there are more References left to be queried.
    pageToken: The next_page_token value returned from a previous List
      request, if any.
    parent: Required. The parent resource name (target_resource of this
      reference). For example: `//targetservice.googleapis.com/projects/{my-
      project}/locations/{location}/instances/{my-instance}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3)


class ListReferencesResponse(_messages.Message):
  r"""The ListReferencesResponse response.

  Fields:
    nextPageToken: Token to retrieve the next page of results, or empty if
      there are no more results in the list.
    references: The list of references.
  """

  nextPageToken = _messages.StringField(1)
  references = _messages.MessageField('Reference', 2, repeated=True)


class ListServiceBindingsResponse(_messages.Message):
  r"""Response returned by the ListServiceBindings method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    serviceBindings: List of ServiceBinding resources.
  """

  nextPageToken = _messages.StringField(1)
  serviceBindings = _messages.MessageField('ServiceBinding', 2, repeated=True)


class ListServiceLbPoliciesResponse(_messages.Message):
  r"""Response returned by the ListServiceLbPolicies method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    serviceLbPolicies: List of ServiceLbPolicy resources.
  """

  nextPageToken = _messages.StringField(1)
  serviceLbPolicies = _messages.MessageField('ServiceLbPolicy', 2, repeated=True)


class ListTcpRoutesResponse(_messages.Message):
  r"""Response returned by the ListTcpRoutes method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    tcpRoutes: List of TcpRoute resources.
  """

  nextPageToken = _messages.StringField(1)
  tcpRoutes = _messages.MessageField('TcpRoute', 2, repeated=True)


class ListTlsRoutesResponse(_messages.Message):
  r"""Response returned by the ListTlsRoutes method.

  Fields:
    nextPageToken: If there might be more results than those appearing in this
      response, then `next_page_token` is included. To get the next set of
      results, call this method again using the value of `next_page_token` as
      `page_token`.
    tlsRoutes: List of TlsRoute resources.
  """

  nextPageToken = _messages.StringField(1)
  tlsRoutes = _messages.MessageField('TlsRoute', 2, repeated=True)


class Location(_messages.Message):
  r"""A resource that represents a Google Cloud location.

  Messages:
    LabelsValue: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    MetadataValue: Service-specific metadata. For example the available
      capacity at the given location.

  Fields:
    displayName: The friendly name for this location, typically a nearby city
      name. For example, "Tokyo".
    labels: Cross-service attributes for the location. For example
      {"cloud.googleapis.com/region": "us-east1"}
    locationId: The canonical id for this location. For example: `"us-east1"`.
    metadata: Service-specific metadata. For example the available capacity at
      the given location.
    name: Resource name for the location, which may vary between
      implementations. For example: `"projects/example-project/locations/us-
      east1"`
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Cross-service attributes for the location. For example
    {"cloud.googleapis.com/region": "us-east1"}

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata. For example the available capacity at the
    given location.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  displayName = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  locationId = _messages.StringField(3)
  metadata = _messages.MessageField('MetadataValue', 4)
  name = _messages.StringField(5)


class LogConfig(_messages.Message):
  r"""Specifies what kind of log the caller must write

  Fields:
    cloudAudit: Cloud audit options.
    counter: Counter options.
    dataAccess: Data access options.
  """

  cloudAudit = _messages.MessageField('LogConfigCloudAuditOptions', 1)
  counter = _messages.MessageField('LogConfigCounterOptions', 2)
  dataAccess = _messages.MessageField('LogConfigDataAccessOptions', 3)


class LogConfigCloudAuditOptions(_messages.Message):
  r"""Write a Cloud Audit log

  Enums:
    LogNameValueValuesEnum: The log_name to populate in the Cloud Audit
      Record.

  Fields:
    authorizationLoggingOptions: Information used by the Cloud Audit Logging
      pipeline.
    logName: The log_name to populate in the Cloud Audit Record.
  """

  class LogNameValueValuesEnum(_messages.Enum):
    r"""The log_name to populate in the Cloud Audit Record.

    Values:
      UNSPECIFIED_LOG_NAME: Default. Should not be used.
      ADMIN_ACTIVITY: Corresponds to "cloudaudit.googleapis.com/activity"
      DATA_ACCESS: Corresponds to "cloudaudit.googleapis.com/data_access"
    """
    UNSPECIFIED_LOG_NAME = 0
    ADMIN_ACTIVITY = 1
    DATA_ACCESS = 2

  authorizationLoggingOptions = _messages.MessageField('AuthorizationLoggingOptions', 1)
  logName = _messages.EnumField('LogNameValueValuesEnum', 2)


class LogConfigCounterOptions(_messages.Message):
  r"""Increment a streamz counter with the specified metric and field names.
  Metric names should start with a '/', generally be lowercase-only, and end
  in "_count". Field names should not contain an initial slash. The actual
  exported metric names will have "/iam/policy" prepended. Field names
  correspond to IAM request parameters and field values are their respective
  values. Supported field names: - "authority", which is "[token]" if
  IAMContext.token is present, otherwise the value of
  IAMContext.authority_selector if present, and otherwise a representation of
  IAMContext.principal; or - "iam_principal", a representation of
  IAMContext.principal even if a token or authority selector is present; or -
  "" (empty string), resulting in a counter with no fields. Examples: counter
  { metric: "/debug_access_count" field: "iam_principal" } ==> increment
  counter /iam/policy/debug_access_count {iam_principal=[value of
  IAMContext.principal]}

  Fields:
    customFields: Custom fields.
    field: The field value to attribute.
    metric: The metric to update.
  """

  customFields = _messages.MessageField('LogConfigCounterOptionsCustomField', 1, repeated=True)
  field = _messages.StringField(2)
  metric = _messages.StringField(3)


class LogConfigCounterOptionsCustomField(_messages.Message):
  r"""Custom fields. These can be used to create a counter with arbitrary
  field/value pairs. See: go/rpcsp-custom-fields.

  Fields:
    name: Name is the field name.
    value: Value is the field value. It is important that in contrast to the
      CounterOptions.field, the value here is a constant that is not derived
      from the IAMContext.
  """

  name = _messages.StringField(1)
  value = _messages.StringField(2)


class LogConfigDataAccessOptions(_messages.Message):
  r"""Write a Data Access (Gin) log

  Enums:
    LogModeValueValuesEnum:

  Fields:
    logMode: A LogModeValueValuesEnum attribute.
  """

  class LogModeValueValuesEnum(_messages.Enum):
    r"""LogModeValueValuesEnum enum type.

    Values:
      LOG_MODE_UNSPECIFIED: Client is not required to write a partial Gin log
        immediately after the authorization check. If client chooses to write
        one and it fails, client may either fail open (allow the operation to
        continue) or fail closed (handle as a DENY outcome).
      LOG_FAIL_CLOSED: The application's operation in the context of which
        this authorization check is being made may only be performed if it is
        successfully logged to Gin. For instance, the authorization library
        may satisfy this obligation by emitting a partial log entry at
        authorization check time and only returning ALLOW to the application
        if it succeeds. If a matching Rule has this directive, but the client
        has not indicated that it will honor such requirements, then the IAM
        check will result in authorization failure by setting
        CheckPolicyResponse.success=false.
    """
    LOG_MODE_UNSPECIFIED = 0
    LOG_FAIL_CLOSED = 1

  logMode = _messages.EnumField('LogModeValueValuesEnum', 1)


class Mesh(_messages.Message):
  r"""Mesh represents a logical configuration grouping for workload to
  workload communication within a service mesh. Routes that point to mesh
  dictate how requests are routed within this logical mesh boundary.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the Mesh
      resource.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    interceptionPort: Optional. If set to a valid TCP port (1-65535),
      instructs the SIDECAR proxy to listen on the specified port of localhost
      (127.0.0.1) address. The SIDECAR proxy will expect all traffic to be
      redirected to this port regardless of its actual ip:port destination. If
      unset, a port '15001' is used as the interception port. This is
      applicable only for sidecar proxy deployments.
    internalCaller: Optional. A flag set to identify internal controllers
      Setting this will trigger a P4SA check to validate the caller is from an
      allowlisted service's P4SA even if other optional fields are unset.
    labels: Optional. Set of label tags associated with the Mesh resource.
    name: Required. Name of the Mesh resource. It matches pattern
      `projects/*/locations/global/meshes/`.
    selfLink: Output only. Server-defined URL of this resource
    updateTime: Output only. The timestamp when the resource was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the Mesh resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  interceptionPort = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  internalCaller = _messages.BooleanField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  name = _messages.StringField(6)
  selfLink = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class MetadataLabelMatcher(_messages.Message):
  r"""The matcher that is based on node metadata presented by xDS clients.

  Enums:
    MetadataLabelMatchCriteriaValueValuesEnum: Specifies how matching should
      be done. Supported values are: MATCH_ANY: At least one of the Labels
      specified in the matcher should match the metadata presented by xDS
      client. MATCH_ALL: The metadata presented by the xDS client should
      contain all of the labels specified here. The selection is determined
      based on the best match. For example, suppose there are three
      EndpointPolicy resources P1, P2 and P3 and if P1 has a the matcher as
      MATCH_ANY , P2 has MATCH_ALL , and P3 has MATCH_ALL . If a client with
      label connects, the config from P1 will be selected. If a client with
      label connects, the config from P2 will be selected. If a client with
      label connects, the config from P3 will be selected. If there is more
      than one best match, (for example, if a config P4 with selector exists
      and if a client with label connects), an error will be thrown.

  Fields:
    metadataLabelMatchCriteria: Specifies how matching should be done.
      Supported values are: MATCH_ANY: At least one of the Labels specified in
      the matcher should match the metadata presented by xDS client.
      MATCH_ALL: The metadata presented by the xDS client should contain all
      of the labels specified here. The selection is determined based on the
      best match. For example, suppose there are three EndpointPolicy
      resources P1, P2 and P3 and if P1 has a the matcher as MATCH_ANY , P2
      has MATCH_ALL , and P3 has MATCH_ALL . If a client with label connects,
      the config from P1 will be selected. If a client with label connects,
      the config from P2 will be selected. If a client with label connects,
      the config from P3 will be selected. If there is more than one best
      match, (for example, if a config P4 with selector exists and if a client
      with label connects), an error will be thrown.
    metadataLabels: The list of label value pairs that must match labels in
      the provided metadata based on filterMatchCriteria This list can have at
      most 64 entries. The list can be empty if the match criteria is
      MATCH_ANY, to specify a wildcard match (i.e this matches any client).
  """

  class MetadataLabelMatchCriteriaValueValuesEnum(_messages.Enum):
    r"""Specifies how matching should be done. Supported values are:
    MATCH_ANY: At least one of the Labels specified in the matcher should
    match the metadata presented by xDS client. MATCH_ALL: The metadata
    presented by the xDS client should contain all of the labels specified
    here. The selection is determined based on the best match. For example,
    suppose there are three EndpointPolicy resources P1, P2 and P3 and if P1
    has a the matcher as MATCH_ANY , P2 has MATCH_ALL , and P3 has MATCH_ALL .
    If a client with label connects, the config from P1 will be selected. If a
    client with label connects, the config from P2 will be selected. If a
    client with label connects, the config from P3 will be selected. If there
    is more than one best match, (for example, if a config P4 with selector
    exists and if a client with label connects), an error will be thrown.

    Values:
      METADATA_LABEL_MATCH_CRITERIA_UNSPECIFIED: Default value. Should not be
        used.
      MATCH_ANY: At least one of the Labels specified in the matcher should
        match the metadata presented by xDS client.
      MATCH_ALL: The metadata presented by the xDS client should contain all
        of the labels specified here.
    """
    METADATA_LABEL_MATCH_CRITERIA_UNSPECIFIED = 0
    MATCH_ANY = 1
    MATCH_ALL = 2

  metadataLabelMatchCriteria = _messages.EnumField('MetadataLabelMatchCriteriaValueValuesEnum', 1)
  metadataLabels = _messages.MessageField('MetadataLabels', 2, repeated=True)


class MetadataLabels(_messages.Message):
  r"""Defines a name-pair value for a single label.

  Fields:
    labelName: Required. Label name presented as key in xDS Node Metadata.
    labelValue: Required. Label value presented as value corresponding to the
      above key, in xDS Node Metadata.
  """

  labelName = _messages.StringField(1)
  labelValue = _messages.StringField(2)


class MulticastConsumerAssociation(_messages.Message):
  r"""Message describing MulticastConsumerAssociation object

  Messages:
    LabelsValue: Labels as key value pairs

  Fields:
    createTime: Output only. [Output only] Create time stamp
    domainActivation: Reference to the domain activation in the same zone as
      the consumer association.
    labels: Labels as key value pairs
    name: name of resource
    network: Reference to the network
    updateTime: Output only. [Output only] Update time stamp
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  domainActivation = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  network = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class MulticastDomain(_messages.Message):
  r"""Message describing MulticastDomain object

  Messages:
    LabelsValue: Labels as key value pairs

  Fields:
    createTime: Output only. [Output only] Create time stamp
    labels: Labels as key value pairs
    name: name of resource
    updateTime: Output only. [Output only] Update time stamp
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  name = _messages.StringField(3)
  updateTime = _messages.StringField(4)


class MulticastDomainActivation(_messages.Message):
  r"""Message describing MulticastDomainActivation object

  Messages:
    LabelsValue: Labels as key value pairs

  Fields:
    createTime: Output only. [Output only] Create time stamp
    domain: Reference to the domain that is being activated.
    labels: Labels as key value pairs
    name: name of resource
    updateTime: Output only. [Output only] Update time stamp
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  domain = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  name = _messages.StringField(4)
  updateTime = _messages.StringField(5)


class MulticastGroup(_messages.Message):
  r"""Message describing MulticastGroup object

  Messages:
    LabelsValue: Labels as key value pairs

  Fields:
    createTime: Output only. [Output only] Create time stamp
    domainActivation: Reference to the domain activation in the same zone as
      the group.
    groupDefinition: Optional. Reference to the global group definition for
      the group.
    labels: Labels as key value pairs
    name: name of resource
    updateTime: Output only. [Output only] Update time stamp
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  domainActivation = _messages.StringField(2)
  groupDefinition = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  updateTime = _messages.StringField(6)


class MulticastGroupDefinition(_messages.Message):
  r"""Message describing MulticastGroupDefinition object

  Messages:
    LabelsValue: Labels as key value pairs

  Fields:
    createTime: Output only. [Output only] Create time stamp
    labels: Labels as key value pairs
    name: name of resource
    updateTime: Output only. [Output only] Update time stamp
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Labels as key value pairs

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  name = _messages.StringField(3)
  updateTime = _messages.StringField(4)


class NetworkservicesProjectsLocationsEdgeCacheServicesInvalidateCacheRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsEdgeCacheServicesInvalidateCacheRequest
  object.

  Fields:
    edgeCacheService: Required. The name of the EdgeCacheService resource to
      apply the invalidation request to. Must be in the format
      `projects/*/locations/global/edgeCacheServices/*`.
    invalidateCacheRequest: A InvalidateCacheRequest resource to be passed as
      the request body.
  """

  edgeCacheService = _messages.StringField(1, required=True)
  invalidateCacheRequest = _messages.MessageField('InvalidateCacheRequest', 2)


class NetworkservicesProjectsLocationsEndpointPoliciesCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEndpointPoliciesCreateRequest object.

  Fields:
    endpointPolicy: A EndpointPolicy resource to be passed as the request
      body.
    endpointPolicyId: Required. Short name of the EndpointPolicy resource to
      be created. E.g. "CustomECS".
    parent: Required. The parent resource of the EndpointPolicy. Must be in
      the format `projects/*/locations/global`.
  """

  endpointPolicy = _messages.MessageField('EndpointPolicy', 1)
  endpointPolicyId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsEndpointPoliciesDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEndpointPoliciesDeleteRequest object.

  Fields:
    name: Required. A name of the EndpointPolicy to delete. Must be in the
      format `projects/*/locations/global/endpointPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsEndpointPoliciesGetIamPolicyRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEndpointPoliciesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworkservicesProjectsLocationsEndpointPoliciesGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEndpointPoliciesGetRequest object.

  Fields:
    name: Required. A name of the EndpointPolicy to get. Must be in the format
      `projects/*/locations/global/endpointPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsEndpointPoliciesListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEndpointPoliciesListRequest object.

  Fields:
    pageSize: Maximum number of EndpointPolicies to return per call.
    pageToken: The value returned by the last `ListEndpointPoliciesResponse`
      Indicates that this is a continuation of a prior `ListEndpointPolicies`
      call, and that the system should return the next page of data.
    parent: Required. The project and location from which the EndpointPolicies
      should be listed, specified in the format `projects/*/locations/global`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsEndpointPoliciesPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEndpointPoliciesPatchRequest object.

  Fields:
    endpointPolicy: A EndpointPolicy resource to be passed as the request
      body.
    name: Required. Name of the EndpointPolicy resource. It matches pattern
      `projects/{project}/locations/global/endpointPolicies/{endpoint_policy}`
      .
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the EndpointPolicy resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  endpointPolicy = _messages.MessageField('EndpointPolicy', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsEndpointPoliciesSetIamPolicyRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsEndpointPoliciesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class NetworkservicesProjectsLocationsEndpointPoliciesTestIamPermissionsRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsEndpointPoliciesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class NetworkservicesProjectsLocationsGatewaysCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGatewaysCreateRequest object.

  Fields:
    gateway: A Gateway resource to be passed as the request body.
    gatewayId: Required. Short name of the Gateway resource to be created.
    parent: Required. The parent resource of the Gateway. Must be in the
      format `projects/*/locations/*`.
  """

  gateway = _messages.MessageField('Gateway', 1)
  gatewayId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsGatewaysDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGatewaysDeleteRequest object.

  Fields:
    name: Required. A name of the Gateway to delete. Must be in the format
      `projects/*/locations/*/gateways/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsGatewaysGetIamPolicyRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGatewaysGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworkservicesProjectsLocationsGatewaysGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGatewaysGetRequest object.

  Fields:
    name: Required. A name of the Gateway to get. Must be in the format
      `projects/*/locations/*/gateways/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsGatewaysListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGatewaysListRequest object.

  Fields:
    pageSize: Maximum number of Gateways to return per call.
    pageToken: The value returned by the last `ListGatewaysResponse` Indicates
      that this is a continuation of a prior `ListGateways` call, and that the
      system should return the next page of data.
    parent: Required. The project and location from which the Gateways should
      be listed, specified in the format `projects/*/locations/*`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsGatewaysPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGatewaysPatchRequest object.

  Fields:
    gateway: A Gateway resource to be passed as the request body.
    name: Required. Name of the Gateway resource. It matches pattern
      `projects/*/locations/*/gateways/`.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Gateway resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  gateway = _messages.MessageField('Gateway', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsGatewaysSetIamPolicyRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGatewaysSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class NetworkservicesProjectsLocationsGatewaysTestIamPermissionsRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGatewaysTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class NetworkservicesProjectsLocationsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGetRequest object.

  Fields:
    name: Resource name for the location.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsGrpcRoutesCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGrpcRoutesCreateRequest object.

  Fields:
    grpcRoute: A GrpcRoute resource to be passed as the request body.
    grpcRouteId: Required. Short name of the GrpcRoute resource to be created.
    parent: Required. The parent resource of the GrpcRoute. Must be in the
      format `projects/*/locations/global`.
  """

  grpcRoute = _messages.MessageField('GrpcRoute', 1)
  grpcRouteId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsGrpcRoutesDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGrpcRoutesDeleteRequest object.

  Fields:
    name: Required. A name of the GrpcRoute to delete. Must be in the format
      `projects/*/locations/global/grpcRoutes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsGrpcRoutesGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGrpcRoutesGetRequest object.

  Fields:
    name: Required. A name of the GrpcRoute to get. Must be in the format
      `projects/*/locations/global/grpcRoutes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsGrpcRoutesListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGrpcRoutesListRequest object.

  Fields:
    pageSize: Maximum number of GrpcRoutes to return per call.
    pageToken: The value returned by the last `ListGrpcRoutesResponse`
      Indicates that this is a continuation of a prior `ListGrpcRoutes` call,
      and that the system should return the next page of data.
    parent: Required. The project and location from which the GrpcRoutes
      should be listed, specified in the format `projects/*/locations/global`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsGrpcRoutesPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsGrpcRoutesPatchRequest object.

  Fields:
    grpcRoute: A GrpcRoute resource to be passed as the request body.
    name: Required. Name of the GrpcRoute resource. It matches pattern
      `projects/*/locations/global/grpcRoutes/`
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the GrpcRoute resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  grpcRoute = _messages.MessageField('GrpcRoute', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsHttpRoutesCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsHttpRoutesCreateRequest object.

  Fields:
    httpRoute: A HttpRoute resource to be passed as the request body.
    httpRouteId: Required. Short name of the HttpRoute resource to be created.
    parent: Required. The parent resource of the HttpRoute. Must be in the
      format `projects/*/locations/global`.
  """

  httpRoute = _messages.MessageField('HttpRoute', 1)
  httpRouteId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsHttpRoutesDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsHttpRoutesDeleteRequest object.

  Fields:
    name: Required. A name of the HttpRoute to delete. Must be in the format
      `projects/*/locations/global/httpRoutes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsHttpRoutesGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsHttpRoutesGetRequest object.

  Fields:
    name: Required. A name of the HttpRoute to get. Must be in the format
      `projects/*/locations/global/httpRoutes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsHttpRoutesListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsHttpRoutesListRequest object.

  Fields:
    pageSize: Maximum number of HttpRoutes to return per call.
    pageToken: The value returned by the last `ListHttpRoutesResponse`
      Indicates that this is a continuation of a prior `ListHttpRoutes` call,
      and that the system should return the next page of data.
    parent: Required. The project and location from which the HttpRoutes
      should be listed, specified in the format `projects/*/locations/global`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsHttpRoutesPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsHttpRoutesPatchRequest object.

  Fields:
    httpRoute: A HttpRoute resource to be passed as the request body.
    name: Required. Name of the HttpRoute resource. It matches pattern
      `projects/*/locations/global/httpRoutes/http_route_name>`.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the HttpRoute resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  httpRoute = _messages.MessageField('HttpRoute', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsListRequest object.

  Fields:
    filter: A filter to narrow down results to a preferred subset. The
      filtering language accepts strings like `"displayName=tokyo"`, and is
      documented in more detail in [AIP-160](https://google.aip.dev/160).
    includeUnrevealedLocations: If true, the returned list will include
      locations which are not yet revealed.
    name: The resource that owns the locations collection, if applicable.
    pageSize: The maximum number of results to return. If not set, the service
      selects a default.
    pageToken: A page token received from the `next_page_token` field in the
      response. Send that page token to receive the subsequent page.
  """

  filter = _messages.StringField(1)
  includeUnrevealedLocations = _messages.BooleanField(2)
  name = _messages.StringField(3, required=True)
  pageSize = _messages.IntegerField(4, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(5)


class NetworkservicesProjectsLocationsMeshesCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMeshesCreateRequest object.

  Fields:
    mesh: A Mesh resource to be passed as the request body.
    meshId: Required. Short name of the Mesh resource to be created.
    parent: Required. The parent resource of the Mesh. Must be in the format
      `projects/*/locations/global`.
  """

  mesh = _messages.MessageField('Mesh', 1)
  meshId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsMeshesDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMeshesDeleteRequest object.

  Fields:
    name: Required. A name of the Mesh to delete. Must be in the format
      `projects/*/locations/global/meshes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMeshesGetIamPolicyRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMeshesGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworkservicesProjectsLocationsMeshesGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMeshesGetRequest object.

  Fields:
    name: Required. A name of the Mesh to get. Must be in the format
      `projects/*/locations/global/meshes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMeshesListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMeshesListRequest object.

  Fields:
    pageSize: Maximum number of Meshes to return per call.
    pageToken: The value returned by the last `ListMeshesResponse` Indicates
      that this is a continuation of a prior `ListMeshes` call, and that the
      system should return the next page of data.
    parent: Required. The project and location from which the Meshes should be
      listed, specified in the format `projects/*/locations/global`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsMeshesPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMeshesPatchRequest object.

  Fields:
    mesh: A Mesh resource to be passed as the request body.
    name: Required. Name of the Mesh resource. It matches pattern
      `projects/*/locations/global/meshes/`.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the Mesh resource by the update. The fields specified in
      the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  mesh = _messages.MessageField('Mesh', 1)
  name = _messages.StringField(2, required=True)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsMeshesSetIamPolicyRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMeshesSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class NetworkservicesProjectsLocationsMeshesTestIamPermissionsRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMeshesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class NetworkservicesProjectsLocationsMulticastConsumerAssociationsCreateRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastConsumerAssociationsCreateRequest
  object.

  Fields:
    multicastConsumerAssociation: A MulticastConsumerAssociation resource to
      be passed as the request body.
    multicastConsumerAssociationId: Required. Id of the requesting object If
      auto-generating Id server-side, remove this field and
      multicast_consumer_association_id from the method_signature of Create
      RPC
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  multicastConsumerAssociation = _messages.MessageField('MulticastConsumerAssociation', 1)
  multicastConsumerAssociationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastConsumerAssociationsDeleteRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastConsumerAssociationsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkservicesProjectsLocationsMulticastConsumerAssociationsGetRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastConsumerAssociationsGetRequest
  object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMulticastConsumerAssociationsListRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastConsumerAssociationsListRequest
  object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for
      ListMulticastConsumerAssociationsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkservicesProjectsLocationsMulticastConsumerAssociationsPatchRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastConsumerAssociationsPatchRequest
  object.

  Fields:
    multicastConsumerAssociation: A MulticastConsumerAssociation resource to
      be passed as the request body.
    name: name of resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the MulticastConsumerAssociation resource by the update.
      The fields specified in the update_mask are relative to the resource,
      not the full request. A field will be overwritten if it is in the mask.
      If the user does not provide a mask then all fields will be overwritten.
  """

  multicastConsumerAssociation = _messages.MessageField('MulticastConsumerAssociation', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastDomainActivationsCreateRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastDomainActivationsCreateRequest
  object.

  Fields:
    multicastDomainActivation: A MulticastDomainActivation resource to be
      passed as the request body.
    multicastDomainActivationId: Required. Id of the requesting object If
      auto-generating Id server-side, remove this field and
      multicast_domain_activation_id from the method_signature of Create RPC
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  multicastDomainActivation = _messages.MessageField('MulticastDomainActivation', 1)
  multicastDomainActivationId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastDomainActivationsDeleteRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsMulticastDomainActivationsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkservicesProjectsLocationsMulticastDomainActivationsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainActivationsGetRequest
  object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMulticastDomainActivationsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainActivationsListRequest
  object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListMulticastDomainActivationsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkservicesProjectsLocationsMulticastDomainActivationsPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainActivationsPatchRequest
  object.

  Fields:
    multicastDomainActivation: A MulticastDomainActivation resource to be
      passed as the request body.
    name: name of resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the MulticastDomainActivation resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
  """

  multicastDomainActivation = _messages.MessageField('MulticastDomainActivation', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastDomainsCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainsCreateRequest object.

  Fields:
    multicastDomain: A MulticastDomain resource to be passed as the request
      body.
    multicastDomainId: Required. Id of the requesting object If auto-
      generating Id server-side, remove this field and multicast_domain_id
      from the method_signature of Create RPC
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  multicastDomain = _messages.MessageField('MulticastDomain', 1)
  multicastDomainId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastDomainsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainsDeleteRequest object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkservicesProjectsLocationsMulticastDomainsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMulticastDomainsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainsListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListMulticastDomainsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkservicesProjectsLocationsMulticastDomainsPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastDomainsPatchRequest object.

  Fields:
    multicastDomain: A MulticastDomain resource to be passed as the request
      body.
    name: name of resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the MulticastDomain resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  multicastDomain = _messages.MessageField('MulticastDomain', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastGroupDefinitionsCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupDefinitionsCreateRequest
  object.

  Fields:
    multicastGroupDefinition: A MulticastGroupDefinition resource to be passed
      as the request body.
    multicastGroupDefinitionId: Required. Id of the requesting object If auto-
      generating Id server-side, remove this field and
      multicast_group_definition_id from the method_signature of Create RPC
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  multicastGroupDefinition = _messages.MessageField('MulticastGroupDefinition', 1)
  multicastGroupDefinitionId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastGroupDefinitionsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupDefinitionsDeleteRequest
  object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkservicesProjectsLocationsMulticastGroupDefinitionsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupDefinitionsGetRequest
  object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMulticastGroupDefinitionsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupDefinitionsListRequest
  object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListMulticastGroupDefinitionsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkservicesProjectsLocationsMulticastGroupDefinitionsPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupDefinitionsPatchRequest
  object.

  Fields:
    multicastGroupDefinition: A MulticastGroupDefinition resource to be passed
      as the request body.
    name: name of resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the MulticastGroupDefinition resource by the update. The
      fields specified in the update_mask are relative to the resource, not
      the full request. A field will be overwritten if it is in the mask. If
      the user does not provide a mask then all fields will be overwritten.
  """

  multicastGroupDefinition = _messages.MessageField('MulticastGroupDefinition', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastGroupsCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupsCreateRequest object.

  Fields:
    multicastGroup: A MulticastGroup resource to be passed as the request
      body.
    multicastGroupId: Required. Id of the requesting object If auto-generating
      Id server-side, remove this field and multicast_group_id from the
      method_signature of Create RPC
    parent: Required. Value for parent.
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  multicastGroup = _messages.MessageField('MulticastGroup', 1)
  multicastGroupId = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  requestId = _messages.StringField(4)


class NetworkservicesProjectsLocationsMulticastGroupsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupsDeleteRequest object.

  Fields:
    name: Required. Name of the resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes after the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
  """

  name = _messages.StringField(1, required=True)
  requestId = _messages.StringField(2)


class NetworkservicesProjectsLocationsMulticastGroupsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupsGetRequest object.

  Fields:
    name: Required. Name of the resource
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsMulticastGroupsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupsListRequest object.

  Fields:
    filter: Filtering results
    orderBy: Hint for how to order the results
    pageSize: Requested page size. Server may return fewer items than
      requested. If unspecified, server will pick an appropriate default.
    pageToken: A token identifying a page of results the server should return.
    parent: Required. Parent value for ListMulticastGroupsRequest
  """

  filter = _messages.StringField(1)
  orderBy = _messages.StringField(2)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)
  parent = _messages.StringField(5, required=True)


class NetworkservicesProjectsLocationsMulticastGroupsPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsMulticastGroupsPatchRequest object.

  Fields:
    multicastGroup: A MulticastGroup resource to be passed as the request
      body.
    name: name of resource
    requestId: Optional. An optional request ID to identify requests. Specify
      a unique request ID so that if you must retry your request, the server
      will know to ignore the request if it has already been completed. The
      server will guarantee that for at least 60 minutes since the first
      request. For example, consider a situation where you make an initial
      request and the request times out. If you make the request again with
      the same request ID, the server can check if original operation with the
      same request ID was received, and if so, will ignore the second request.
      This prevents clients from accidentally creating duplicate commitments.
      The request ID must be a valid UUID with the exception that zero UUID is
      not supported (00000000-0000-0000-0000-000000000000).
    updateMask: Required. Field mask is used to specify the fields to be
      overwritten in the MulticastGroup resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  multicastGroup = _messages.MessageField('MulticastGroup', 1)
  name = _messages.StringField(2, required=True)
  requestId = _messages.StringField(3)
  updateMask = _messages.StringField(4)


class NetworkservicesProjectsLocationsOperationsCancelRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsOperationsCancelRequest object.

  Fields:
    cancelOperationRequest: A CancelOperationRequest resource to be passed as
      the request body.
    name: The name of the operation resource to be cancelled.
  """

  cancelOperationRequest = _messages.MessageField('CancelOperationRequest', 1)
  name = _messages.StringField(2, required=True)


class NetworkservicesProjectsLocationsOperationsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsOperationsDeleteRequest object.

  Fields:
    name: The name of the operation resource to be deleted.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsOperationsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsOperationsGetRequest object.

  Fields:
    name: The name of the operation resource.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsOperationsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsOperationsListRequest object.

  Fields:
    filter: The standard list filter.
    name: The name of the operation's parent resource.
    pageSize: The standard list page size.
    pageToken: The standard list page token.
  """

  filter = _messages.StringField(1)
  name = _messages.StringField(2, required=True)
  pageSize = _messages.IntegerField(3, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(4)


class NetworkservicesProjectsLocationsServiceBindingsCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceBindingsCreateRequest object.

  Fields:
    parent: Required. The parent resource of the ServiceBinding. Must be in
      the format `projects/*/locations/global`.
    serviceBinding: A ServiceBinding resource to be passed as the request
      body.
    serviceBindingId: Required. Short name of the ServiceBinding resource to
      be created.
  """

  parent = _messages.StringField(1, required=True)
  serviceBinding = _messages.MessageField('ServiceBinding', 2)
  serviceBindingId = _messages.StringField(3)


class NetworkservicesProjectsLocationsServiceBindingsDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceBindingsDeleteRequest object.

  Fields:
    name: Required. A name of the ServiceBinding to delete. Must be in the
      format `projects/*/locations/global/serviceBindings/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsServiceBindingsGetIamPolicyRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceBindingsGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworkservicesProjectsLocationsServiceBindingsGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceBindingsGetRequest object.

  Fields:
    name: Required. A name of the ServiceBinding to get. Must be in the format
      `projects/*/locations/global/serviceBindings/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsServiceBindingsListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceBindingsListRequest object.

  Fields:
    pageSize: Maximum number of ServiceBindings to return per call.
    pageToken: The value returned by the last `ListServiceBindingsResponse`
      Indicates that this is a continuation of a prior `ListRouters` call, and
      that the system should return the next page of data.
    parent: Required. The project and location from which the ServiceBindings
      should be listed, specified in the format `projects/*/locations/global`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsServiceBindingsPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceBindingsPatchRequest object.

  Fields:
    name: Required. Name of the ServiceBinding resource. It matches pattern
      `projects/*/locations/global/serviceBindings/service_binding_name`.
    serviceBinding: A ServiceBinding resource to be passed as the request
      body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the ServiceBinding resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  serviceBinding = _messages.MessageField('ServiceBinding', 2)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsServiceBindingsSetIamPolicyRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceBindingsSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class NetworkservicesProjectsLocationsServiceBindingsTestIamPermissionsRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsServiceBindingsTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class NetworkservicesProjectsLocationsServiceLbPoliciesCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceLbPoliciesCreateRequest object.

  Fields:
    parent: Required. The parent resource of the ServiceLbPolicy. Must be in
      the format `projects/{project}/locations/{location}`.
    serviceLbPolicy: A ServiceLbPolicy resource to be passed as the request
      body.
    serviceLbPolicyId: Required. Short name of the ServiceLbPolicy resource to
      be created. E.g. for resource name `projects/{project}/locations/{locati
      on}/serviceLbPolicies/{service_lb_policy_name}`. the id is value of
      {service_lb_policy_name}
  """

  parent = _messages.StringField(1, required=True)
  serviceLbPolicy = _messages.MessageField('ServiceLbPolicy', 2)
  serviceLbPolicyId = _messages.StringField(3)


class NetworkservicesProjectsLocationsServiceLbPoliciesDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceLbPoliciesDeleteRequest object.

  Fields:
    name: Required. A name of the ServiceLbPolicy to delete. Must be in the
      format `projects/{project}/locations/{location}/serviceLbPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsServiceLbPoliciesGetIamPolicyRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceLbPoliciesGetIamPolicyRequest
  object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class NetworkservicesProjectsLocationsServiceLbPoliciesGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceLbPoliciesGetRequest object.

  Fields:
    name: Required. A name of the ServiceLbPolicy to get. Must be in the
      format `projects/{project}/locations/{location}/serviceLbPolicies/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsServiceLbPoliciesListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceLbPoliciesListRequest object.

  Fields:
    pageSize: Maximum number of ServiceLbPolicies to return per call.
    pageToken: The value returned by the last `ListServiceLbPoliciesResponse`
      Indicates that this is a continuation of a prior `ListRouters` call, and
      that the system should return the next page of data.
    parent: Required. The project and location from which the
      ServiceLbPolicies should be listed, specified in the format
      `projects/{project}/locations/{location}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsServiceLbPoliciesPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceLbPoliciesPatchRequest object.

  Fields:
    name: Required. Name of the ServiceLbPolicy resource. It matches pattern `
      projects/{project}/locations/{location}/serviceLbPolicies/{service_lb_po
      licy_name}`.
    serviceLbPolicy: A ServiceLbPolicy resource to be passed as the request
      body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the ServiceLbPolicy resource by the update. The fields
      specified in the update_mask are relative to the resource, not the full
      request. A field will be overwritten if it is in the mask. If the user
      does not provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  serviceLbPolicy = _messages.MessageField('ServiceLbPolicy', 2)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsServiceLbPoliciesSetIamPolicyRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsServiceLbPoliciesSetIamPolicyRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class NetworkservicesProjectsLocationsServiceLbPoliciesTestIamPermissionsRequest(_messages.Message):
  r"""A
  NetworkservicesProjectsLocationsServiceLbPoliciesTestIamPermissionsRequest
  object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class NetworkservicesProjectsLocationsTcpRoutesCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTcpRoutesCreateRequest object.

  Fields:
    parent: Required. The parent resource of the TcpRoute. Must be in the
      format `projects/*/locations/global`.
    tcpRoute: A TcpRoute resource to be passed as the request body.
    tcpRouteId: Required. Short name of the TcpRoute resource to be created.
      E.g. TODO(Add an example).
  """

  parent = _messages.StringField(1, required=True)
  tcpRoute = _messages.MessageField('TcpRoute', 2)
  tcpRouteId = _messages.StringField(3)


class NetworkservicesProjectsLocationsTcpRoutesDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTcpRoutesDeleteRequest object.

  Fields:
    name: Required. A name of the TcpRoute to delete. Must be in the format
      `projects/*/locations/global/tcpRoutes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsTcpRoutesGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTcpRoutesGetRequest object.

  Fields:
    name: Required. A name of the TcpRoute to get. Must be in the format
      `projects/*/locations/global/tcpRoutes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsTcpRoutesListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTcpRoutesListRequest object.

  Fields:
    pageSize: Maximum number of TcpRoutes to return per call.
    pageToken: The value returned by the last `ListTcpRoutesResponse`
      Indicates that this is a continuation of a prior `ListTcpRoutes` call,
      and that the system should return the next page of data.
    parent: Required. The project and location from which the TcpRoutes should
      be listed, specified in the format `projects/*/locations/global`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsTcpRoutesPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTcpRoutesPatchRequest object.

  Fields:
    name: Required. Name of the TcpRoute resource. It matches pattern
      `projects/*/locations/global/tcpRoutes/tcp_route_name>`.
    tcpRoute: A TcpRoute resource to be passed as the request body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the TcpRoute resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  tcpRoute = _messages.MessageField('TcpRoute', 2)
  updateMask = _messages.StringField(3)


class NetworkservicesProjectsLocationsTlsRoutesCreateRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTlsRoutesCreateRequest object.

  Fields:
    parent: Required. The parent resource of the TlsRoute. Must be in the
      format `projects/*/locations/global`.
    tlsRoute: A TlsRoute resource to be passed as the request body.
    tlsRouteId: Required. Short name of the TlsRoute resource to be created.
      E.g. TODO(Add an example).
  """

  parent = _messages.StringField(1, required=True)
  tlsRoute = _messages.MessageField('TlsRoute', 2)
  tlsRouteId = _messages.StringField(3)


class NetworkservicesProjectsLocationsTlsRoutesDeleteRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTlsRoutesDeleteRequest object.

  Fields:
    name: Required. A name of the TlsRoute to delete. Must be in the format
      `projects/*/locations/global/tlsRoutes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsTlsRoutesGetRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTlsRoutesGetRequest object.

  Fields:
    name: Required. A name of the TlsRoute to get. Must be in the format
      `projects/*/locations/global/tlsRoutes/*`.
  """

  name = _messages.StringField(1, required=True)


class NetworkservicesProjectsLocationsTlsRoutesListRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTlsRoutesListRequest object.

  Fields:
    pageSize: Maximum number of TlsRoutes to return per call.
    pageToken: The value returned by the last `ListTlsRoutesResponse`
      Indicates that this is a continuation of a prior `ListTlsRoutes` call,
      and that the system should return the next page of data.
    parent: Required. The project and location from which the TlsRoutes should
      be listed, specified in the format `projects/*/locations/global`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)


class NetworkservicesProjectsLocationsTlsRoutesPatchRequest(_messages.Message):
  r"""A NetworkservicesProjectsLocationsTlsRoutesPatchRequest object.

  Fields:
    name: Required. Name of the TlsRoute resource. It matches pattern
      `projects/*/locations/global/tlsRoutes/tls_route_name>`.
    tlsRoute: A TlsRoute resource to be passed as the request body.
    updateMask: Optional. Field mask is used to specify the fields to be
      overwritten in the TlsRoute resource by the update. The fields specified
      in the update_mask are relative to the resource, not the full request. A
      field will be overwritten if it is in the mask. If the user does not
      provide a mask then all fields will be overwritten.
  """

  name = _messages.StringField(1, required=True)
  tlsRoute = _messages.MessageField('TlsRoute', 2)
  updateMask = _messages.StringField(3)


class Operation(_messages.Message):
  r"""This resource represents a long-running operation that is the result of
  a network API call.

  Messages:
    MetadataValue: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    ResponseValue: The normal response of the operation in case of success. If
      the original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

  Fields:
    done: If the value is `false`, it means the operation is still in
      progress. If `true`, the operation is completed, and either `error` or
      `response` is available.
    error: The error result of the operation in case of failure or
      cancellation.
    metadata: Service-specific metadata associated with the operation. It
      typically contains progress information and common metadata such as
      create time. Some services might not provide such metadata. Any method
      that returns a long-running operation should document the metadata type,
      if any.
    name: The server-assigned name, which is only unique within the same
      service that originally returns it. If you use the default HTTP mapping,
      the `name` should be a resource name ending with
      `operations/{unique_id}`.
    response: The normal response of the operation in case of success. If the
      original method returns no data on success, such as `Delete`, the
      response is `google.protobuf.Empty`. If the original method is standard
      `Get`/`Create`/`Update`, the response should be the resource. For other
      methods, the response should have the type `XxxResponse`, where `Xxx` is
      the original method name. For example, if the original method name is
      `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Service-specific metadata associated with the operation. It typically
    contains progress information and common metadata such as create time.
    Some services might not provide such metadata. Any method that returns a
    long-running operation should document the metadata type, if any.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  @encoding.MapUnrecognizedFields('additionalProperties')
  class ResponseValue(_messages.Message):
    r"""The normal response of the operation in case of success. If the
    original method returns no data on success, such as `Delete`, the response
    is `google.protobuf.Empty`. If the original method is standard
    `Get`/`Create`/`Update`, the response should be the resource. For other
    methods, the response should have the type `XxxResponse`, where `Xxx` is
    the original method name. For example, if the original method name is
    `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.

    Messages:
      AdditionalProperty: An additional property for a ResponseValue object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a ResponseValue object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  done = _messages.BooleanField(1)
  error = _messages.MessageField('Status', 2)
  metadata = _messages.MessageField('MetadataValue', 3)
  name = _messages.StringField(4)
  response = _messages.MessageField('ResponseValue', 5)


class OperationMetadata(_messages.Message):
  r"""Represents the metadata of the long-running operation.

  Fields:
    apiVersion: Output only. API version used to start the operation.
    createTime: Output only. The time the operation was created.
    endTime: Output only. The time the operation finished running.
    requestedCancellation: Output only. Identifies whether the user has
      requested cancellation of the operation. Operations that have
      successfully been cancelled have Operation.error value with a
      google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.
    statusMessage: Output only. Human-readable status of the operation, if
      any.
    target: Output only. Server-defined resource path for the target of the
      operation.
    verb: Output only. Name of the verb executed by the operation.
  """

  apiVersion = _messages.StringField(1)
  createTime = _messages.StringField(2)
  endTime = _messages.StringField(3)
  requestedCancellation = _messages.BooleanField(4)
  statusMessage = _messages.StringField(5)
  target = _messages.StringField(6)
  verb = _messages.StringField(7)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } **YAML example:** bindings: - members: -
  user:<EMAIL> - group:<EMAIL> - domain:google.com -
  serviceAccount:<EMAIL> role:
  roles/resourcemanager.organizationAdmin - members: - user:<EMAIL>
  role: roles/resourcemanager.organizationViewer condition: title: expirable
  access description: Does not grant access after Sep 2020 expression:
  request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA=
  version: 3 For a description of IAM and its features, see the [IAM
  documentation](https://cloud.google.com/iam/docs/).

  Fields:
    auditConfigs: Specifies cloud audit logging configuration for this policy.
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    rules: If more than one rule is specified, the rules are applied in the
      following manner: - All matching LOG rules are always applied. - If any
      DENY/DENY_WITH_LOG rule matches, permission is denied. Logging will be
      applied if one or more matching rule requires logging. - Otherwise, if
      any ALLOW/ALLOW_WITH_LOG rule matches, permission is granted. Logging
      will be applied if one or more matching rule requires logging. -
      Otherwise, if no rule applies, permission is denied.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  auditConfigs = _messages.MessageField('AuditConfig', 1, repeated=True)
  bindings = _messages.MessageField('Binding', 2, repeated=True)
  etag = _messages.BytesField(3)
  rules = _messages.MessageField('Rule', 4, repeated=True)
  version = _messages.IntegerField(5, variant=_messages.Variant.INT32)


class Reference(_messages.Message):
  r"""Represents a reference to a resource.

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    createTime: Output only. The creation time.
    details: Details of the reference type with no implied semantics.
      Cumulative size of the field must not be more than 1KiB.
    name: Output only. Relative resource name of the reference. Includes
      target resource as a parent and reference uid
      `{target_resource}/references/{reference_id}`. For example,
      `projects/{my-project}/locations/{location}/instances/{my-
      instance}/references/{xyz}`.
    sourceResource: Required. Full resource name of the resource which refers
      the target resource. For example:
      //tpu.googleapis.com/projects/myproject/nodes/mynode
    targetUniqueId: Output only. The unique_id of the target resource. Example
      1: (For arcus resource) A-1-0-2-387420123-13-913517247483640811
      unique_id format defined in go/m11n-unique-id-as-resource-id Example 2:
      (For CCFE resource) 123e4567-e89b-12d3-a456-************
    type: Required. Type of the reference. A service might impose limits on
      number of references of a specific type. Note: It's recommended to use
      CAPITALS_WITH_UNDERSCORES style for a type name.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  name = _messages.StringField(3)
  sourceResource = _messages.StringField(4)
  targetUniqueId = _messages.StringField(5)
  type = _messages.StringField(6)


class Rule(_messages.Message):
  r"""A rule to be applied in a Policy.

  Enums:
    ActionValueValuesEnum: Required

  Fields:
    action: Required
    conditions: Additional restrictions that must be met. All conditions must
      pass for the rule to match.
    description: Human-readable description of the rule.
    in_: If one or more 'in' clauses are specified, the rule matches if the
      PRINCIPAL/AUTHORITY_SELECTOR is in at least one of these entries.
    logConfig: The config returned to callers of CheckPolicy for any entries
      that match the LOG action.
    notIn: If one or more 'not_in' clauses are specified, the rule matches if
      the PRINCIPAL/AUTHORITY_SELECTOR is in none of the entries. The format
      for in and not_in entries can be found at in the Local IAM documentation
      (see go/local-iam#features).
    permissions: A permission is a string of form '..' (e.g.,
      'storage.buckets.list'). A value of '*' matches all permissions, and a
      verb part of '*' (e.g., 'storage.buckets.*') matches all verbs.
  """

  class ActionValueValuesEnum(_messages.Enum):
    r"""Required

    Values:
      NO_ACTION: Default no action.
      ALLOW: Matching 'Entries' grant access.
      ALLOW_WITH_LOG: Matching 'Entries' grant access and the caller promises
        to log the request per the returned log_configs.
      DENY: Matching 'Entries' deny access.
      DENY_WITH_LOG: Matching 'Entries' deny access and the caller promises to
        log the request per the returned log_configs.
      LOG: Matching 'Entries' tell IAM.Check callers to generate logs.
    """
    NO_ACTION = 0
    ALLOW = 1
    ALLOW_WITH_LOG = 2
    DENY = 3
    DENY_WITH_LOG = 4
    LOG = 5

  action = _messages.EnumField('ActionValueValuesEnum', 1)
  conditions = _messages.MessageField('Condition', 2, repeated=True)
  description = _messages.StringField(3)
  in_ = _messages.StringField(4, repeated=True)
  logConfig = _messages.MessageField('LogConfig', 5, repeated=True)
  notIn = _messages.StringField(6, repeated=True)
  permissions = _messages.StringField(7, repeated=True)


class ServiceBinding(_messages.Message):
  r"""ServiceBinding is the resource that defines a Service Directory Service
  to be used in a BackendService resource.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the
      ServiceBinding resource.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    endpointFilter: Optional. The endpoint filter associated with the Service
      Binding. The syntax is described in ResolveServiceRequest.
    labels: Optional. Set of label tags associated with the ServiceBinding
      resource.
    name: Required. Name of the ServiceBinding resource. It matches pattern
      `projects/*/locations/global/serviceBindings/service_binding_name`.
    service: Required. The full Service Directory Service name of the format
      projects/*/locations/*/namespaces/*/services/*
    serviceId: Output only. The unique identifier of the Service Directory
      Service against which the Service Binding resource is validated. This is
      populated when the Service Binding resource is used in another resource
      (like Backend Service). This is of the UUID4 format.
    updateTime: Output only. The timestamp when the resource was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the ServiceBinding
    resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  endpointFilter = _messages.StringField(3)
  labels = _messages.MessageField('LabelsValue', 4)
  name = _messages.StringField(5)
  service = _messages.StringField(6)
  serviceId = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class ServiceLbPolicy(_messages.Message):
  r"""ServiceLbPolicy holds global load balancing and traffic distribution
  configuration that can be applied to a BackendService.

  Enums:
    LoadBalancingAlgorithmValueValuesEnum: Optional. The type of load
      balancing algorithm to be used. The default behavior is
      WATERFALL_BY_REGION.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the
      ServiceLbPolicy resource.

  Fields:
    autoCapacityDrain: Optional. Configuration to automatically move traffic
      away for unhealthy IG/NEG for the associated Backend Service.
    createTime: Output only. The timestamp when this resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    failoverConfig: Optional. Configuration related to health based failover.
    labels: Optional. Set of label tags associated with the ServiceLbPolicy
      resource.
    loadBalancingAlgorithm: Optional. The type of load balancing algorithm to
      be used. The default behavior is WATERFALL_BY_REGION.
    name: Required. Name of the ServiceLbPolicy resource. It matches pattern `
      projects/{project}/locations/{location}/serviceLbPolicies/{service_lb_po
      licy_name}`.
    updateTime: Output only. The timestamp when this resource was last
      updated.
  """

  class LoadBalancingAlgorithmValueValuesEnum(_messages.Enum):
    r"""Optional. The type of load balancing algorithm to be used. The default
    behavior is WATERFALL_BY_REGION.

    Values:
      LOAD_BALANCING_ALGORITHM_UNSPECIFIED: The type of the loadbalancing
        algorithm is unspecified.
      SPRAY_TO_WORLD: Balance traffic across all backends across the world
        proportionally based on capacity.
      SPRAY_TO_REGION: Direct traffic to the nearest region with endpoints and
        capacity before spilling over to other regions and spread the traffic
        from each client to all the MIGs/NEGs in a region.
      WATERFALL_BY_REGION: Direct traffic to the nearest region with endpoints
        and capacity before spilling over to other regions. All MIGs/NEGs
        within a region are evenly loaded but each client might not spread the
        traffic to all the MIGs/NEGs in the region.
      WATERFALL_BY_ZONE: Attempt to keep traffic in a single zone closest to
        the client, before spilling over to other zones.
    """
    LOAD_BALANCING_ALGORITHM_UNSPECIFIED = 0
    SPRAY_TO_WORLD = 1
    SPRAY_TO_REGION = 2
    WATERFALL_BY_REGION = 3
    WATERFALL_BY_ZONE = 4

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the ServiceLbPolicy
    resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  autoCapacityDrain = _messages.MessageField('ServiceLbPolicyAutoCapacityDrain', 1)
  createTime = _messages.StringField(2)
  description = _messages.StringField(3)
  failoverConfig = _messages.MessageField('ServiceLbPolicyFailoverConfig', 4)
  labels = _messages.MessageField('LabelsValue', 5)
  loadBalancingAlgorithm = _messages.EnumField('LoadBalancingAlgorithmValueValuesEnum', 6)
  name = _messages.StringField(7)
  updateTime = _messages.StringField(8)


class ServiceLbPolicyAutoCapacityDrain(_messages.Message):
  r"""Option to specify if an unhealthy IG/NEG should be considered for global
  load balancing and traffic routing.

  Fields:
    enable: Optional. If set to 'True', an unhealthy IG/NEG will be set as
      drained. - An IG/NEG is considered unhealthy if less than 25% of the
      instances/endpoints in the IG/NEG are healthy. - This option will never
      result in draining more than 50% of the configured IGs/NEGs for the
      Backend Service.
  """

  enable = _messages.BooleanField(1)


class ServiceLbPolicyFailoverConfig(_messages.Message):
  r"""Option to specify health based failover behavior. This is not related to
  Network load balancer FailoverPolicy.

  Fields:
    failoverHealthThreshold: Optional. The percentage threshold that a load
      balancer will begin to send traffic to failover backends. If the
      percentage of endpoints in a MIG/NEG is smaller than this value, traffic
      would be sent to failover backends if possible. This field should be set
      to a value between 1 and 99. The default value is 50 for Global external
      HTTP(S) load balancer (classic) and Proxyless service mesh, and 70 for
      others.
  """

  failoverHealthThreshold = _messages.IntegerField(1, variant=_messages.Variant.INT32)


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
    updateMask: OPTIONAL: A FieldMask specifying which fields of the policy to
      modify. Only the fields in the mask will be modified. If no mask is
      provided, the following default mask is used: `paths: "bindings, etag"`
  """

  policy = _messages.MessageField('Policy', 1)
  updateMask = _messages.StringField(2)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Status(_messages.Message):
  r"""The `Status` type defines a logical error model that is suitable for
  different programming environments, including REST APIs and RPC APIs. It is
  used by [gRPC](https://github.com/grpc). Each `Status` message contains
  three pieces of data: error code, error message, and error details. You can
  find out more about this error model and how to work with it in the [API
  Design Guide](https://cloud.google.com/apis/design/errors).

  Messages:
    DetailsValueListEntry: A DetailsValueListEntry object.

  Fields:
    code: The status code, which should be an enum value of google.rpc.Code.
    details: A list of messages that carry the error details. There is a
      common set of message types for APIs to use.
    message: A developer-facing error message, which should be in English. Any
      user-facing error message should be localized and sent in the
      google.rpc.Status.details field, or localized by the client.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class DetailsValueListEntry(_messages.Message):
    r"""A DetailsValueListEntry object.

    Messages:
      AdditionalProperty: An additional property for a DetailsValueListEntry
        object.

    Fields:
      additionalProperties: Properties of the object. Contains field @type
        with type URL.
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a DetailsValueListEntry object.

      Fields:
        key: Name of the additional property.
        value: A extra_types.JsonValue attribute.
      """

      key = _messages.StringField(1)
      value = _messages.MessageField('extra_types.JsonValue', 2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  code = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  details = _messages.MessageField('DetailsValueListEntry', 2, repeated=True)
  message = _messages.StringField(3)


class TcpRoute(_messages.Message):
  r"""TcpRoute is the resource defining how TCP traffic should be routed by a
  Mesh/Gateway resource.

  Messages:
    LabelsValue: Optional. Set of label tags associated with the TcpRoute
      resource.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    gateways: Optional. Gateways defines a list of gateways this TcpRoute is
      attached to, as one of the routing rules to route the requests served by
      the gateway. Each gateway reference should match the pattern:
      `projects/*/locations/global/gateways/`
    internalCaller: Optional. A flag set to identify internal controllers
      Setting this will trigger a P4SA check to validate the caller is from an
      allowlisted service's P4SA even if other optional fields are unset.
    labels: Optional. Set of label tags associated with the TcpRoute resource.
    listenOn: The address to listen on. This can be either an IP address and
      port number, or a Unix domain socket name.
    meshes: Optional. Meshes defines a list of meshes this TcpRoute is
      attached to, as one of the routing rules to route the requests served by
      the mesh. Each mesh reference should match the pattern:
      `projects/*/locations/global/meshes/` The attached Mesh should be of a
      type SIDECAR
    name: Required. Name of the TcpRoute resource. It matches pattern
      `projects/*/locations/global/tcpRoutes/tcp_route_name>`.
    routers: Optional. Routers define a list of routers this TcpRoute should
      be served by. Each router reference should match the pattern:
      `projects/*/locations/global/routers/` The attached Router should be of
      a type PROXY
    rules: Required. Rules that define how traffic is routed and handled. At
      least one RouteRule must be supplied. If there are multiple rules then
      the action taken will be the first rule to match.
    selfLink: Output only. Server-defined URL of this resource
    updateTime: Output only. The timestamp when the resource was updated.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. Set of label tags associated with the TcpRoute resource.

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  gateways = _messages.StringField(3, repeated=True)
  internalCaller = _messages.BooleanField(4)
  labels = _messages.MessageField('LabelsValue', 5)
  listenOn = _messages.MessageField('Address', 6)
  meshes = _messages.StringField(7, repeated=True)
  name = _messages.StringField(8)
  routers = _messages.StringField(9, repeated=True)
  rules = _messages.MessageField('TcpRouteRouteRule', 10, repeated=True)
  selfLink = _messages.StringField(11)
  updateTime = _messages.StringField(12)


class TcpRouteRouteAction(_messages.Message):
  r"""The specifications for routing traffic and applying associated policies.

  Fields:
    blackholeDestination: Optional. If true, traffic will be dropped. Default
      is false. Only one of route destinations, original destination or
      blackhole destination can be set.
    destinations: Optional. The destination services to which traffic should
      be forwarded. At least one destination service is required. Only one of
      route destination or original destination can be set.
    originalDestination: Optional. If true, Router will use the destination IP
      and port of the original connection as the destination of the request.
      Default is false. Only one of route destinations or original destination
      can be set.
  """

  blackholeDestination = _messages.BooleanField(1)
  destinations = _messages.MessageField('TcpRouteRouteDestination', 2, repeated=True)
  originalDestination = _messages.BooleanField(3)


class TcpRouteRouteDestination(_messages.Message):
  r"""Describe the destination for traffic to be routed to.

  Fields:
    serviceName: Required. The URL of a BackendService to route traffic to.
    weight: Optional. Specifies the proportion of requests forwarded to the
      backend referenced by the serviceName field. This is computed as:
      weight/Sum(weights in this destination list). For non-zero values, there
      may be some epsilon from the exact proportion defined here depending on
      the precision an implementation supports. If only one serviceName is
      specified and it has a weight greater than 0, 100% of the traffic is
      forwarded to that backend. If weights are specified for any one service
      name, they need to be specified for all of them. If weights are
      unspecified for all services, then, traffic is distributed in equal
      proportions to all of them.
  """

  serviceName = _messages.StringField(1)
  weight = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class TcpRouteRouteMatch(_messages.Message):
  r"""RouteMatch defines the predicate used to match requests to a given
  action. Multiple match types are "OR"ed for evaluation. If no routeMatch
  field is specified, this rule will unconditionally match traffic.

  Fields:
    address: Required. Must be specified in the CIDR range format. A CIDR
      range consists of an IP Address and a prefix length to construct the
      subnet mask. By default, the prefix length is 32 (i.e. matches a single
      IP address). Only IPV4 addresses are supported. Examples: "********" -
      matches against this exact IP address. "10.0.0.0/8" - matches against
      any IP address within the 10.0.0.0 subnet and ************* mask.
      "0.0.0.0/0" - matches against any IP address'.
    port: Required. Specifies the destination port to match against.
  """

  address = _messages.StringField(1)
  port = _messages.StringField(2)


class TcpRouteRouteRule(_messages.Message):
  r"""Specifies how to match traffic and how to route traffic when traffic is
  matched.

  Messages:
    MetadataValue: Optional. Set of label tags associated with the RouteRule
      resource.

  Fields:
    action: Required. The detailed rule defining how to route matched traffic.
    matches: Optional. RouteMatch defines the predicate used to match requests
      to a given action. Multiple match types are "OR"ed for evaluation. If no
      routeMatch field is specified, this rule will unconditionally match
      traffic.
    metadata: Optional. Set of label tags associated with the RouteRule
      resource.
    workloadContextSelectors: Optional. Selects the workload where the route
      rule should be applied to its targets. A route rule without a
      WorkloadContextSelector should always be applied to its targets when
      there is no conflict. If there are multiple WorkloadContextSelectors
      then the policy will be applied to all targets if ANY of the
      WorkloadContextSelectors match. Therefore these selectors can be
      combined in an OR fashion.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Optional. Set of label tags associated with the RouteRule resource.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  action = _messages.MessageField('TcpRouteRouteAction', 1)
  matches = _messages.MessageField('TcpRouteRouteMatch', 2, repeated=True)
  metadata = _messages.MessageField('MetadataValue', 3)
  workloadContextSelectors = _messages.MessageField('WorkloadContextSelector', 4, repeated=True)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TlsRoute(_messages.Message):
  r"""TlsRoute defines how traffic should be routed based on SNI and other
  matching L3 attributes.

  Fields:
    createTime: Output only. The timestamp when the resource was created.
    description: Optional. A free-text description of the resource. Max length
      1024 characters.
    gateways: Optional. Gateways defines a list of gateways this TlsRoute is
      attached to, as one of the routing rules to route the requests served by
      the gateway. Each gateway reference should match the pattern:
      `projects/*/locations/global/gateways/`
    internalCaller: Optional. A flag set to identify internal controllers
      Setting this will trigger a P4SA check to validate the caller is from an
      allowlisted service's P4SA even if other optional fields are unset.
    listenOn: The address to listen on. This can be either an IP address and
      port number, or a Unix domain socket name.
    meshes: Optional. Meshes defines a list of meshes this TlsRoute is
      attached to, as one of the routing rules to route the requests served by
      the mesh. Each mesh reference should match the pattern:
      `projects/*/locations/global/meshes/` The attached Mesh should be of a
      type SIDECAR
    name: Required. Name of the TlsRoute resource. It matches pattern
      `projects/*/locations/global/tlsRoutes/tls_route_name>`.
    rules: Required. Rules that define how traffic is routed and handled. At
      least one RouteRule must be supplied. If there are multiple rules then
      the action taken will be the first rule to match.
    selfLink: Output only. Server-defined URL of this resource
    updateTime: Output only. The timestamp when the resource was updated.
  """

  createTime = _messages.StringField(1)
  description = _messages.StringField(2)
  gateways = _messages.StringField(3, repeated=True)
  internalCaller = _messages.BooleanField(4)
  listenOn = _messages.MessageField('Address', 5)
  meshes = _messages.StringField(6, repeated=True)
  name = _messages.StringField(7)
  rules = _messages.MessageField('TlsRouteRouteRule', 8, repeated=True)
  selfLink = _messages.StringField(9)
  updateTime = _messages.StringField(10)


class TlsRouteRouteAction(_messages.Message):
  r"""The specifications for routing traffic and applying associated policies.

  Fields:
    destinations: Required. The destination services to which traffic should
      be forwarded. At least one destination service is required.
    originalDestination: Optional. If true, Router will use the destination IP
      and port of the original connection as the destination of the request.
      Default is false.
  """

  destinations = _messages.MessageField('TlsRouteRouteDestination', 1, repeated=True)
  originalDestination = _messages.BooleanField(2)


class TlsRouteRouteDestination(_messages.Message):
  r"""Describe the destination for traffic to be routed to.

  Fields:
    serviceName: Required. The URL of a BackendService to route traffic to.
    weight: Optional. Specifies the proportion of requests forwareded to the
      backend referenced by the service_name field. This is computed as:
      weight/Sum(weights in destinations) Weights in all destinations does not
      need to sum up to 100.
  """

  serviceName = _messages.StringField(1)
  weight = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class TlsRouteRouteMatch(_messages.Message):
  r"""RouteMatch defines the predicate used to match requests to a given
  action. Multiple match types are "AND"ed for evaluation. If no routeMatch
  field is specified, this rule will unconditionally match traffic.

  Fields:
    alpn: Optional. ALPN (Application-Layer Protocol Negotiation) to match
      against. Examples: "http/1.1", "h2". At least one of sni_host and alpn
      is required. Up to 5 alpns across all matches can be set.
    ipAddress: Optional. Must be specified in the CIDR range format. A CIDR
      range consists of an IP Address and a prefix length to construct the
      subnet mask. By default, the prefix length is 32 (i.e. matches a single
      IP address). Only IPV4 addresses are supported. Examples: "********" -
      matches against this exact IP address. "10.0.0.0/8" - matches against
      any IP address within the 10.0.0.0 subnet and ************* mask.
      "0.0.0.0/0" - matches against any IP address.
    port: Optional. Specifies the destination port to match against.
    sniHost: Optional. SNI (server name indicator) to match against. SNI will
      be matched against all wildcard domains, i.e. `www.example.com` will be
      first matched against `www.example.com`, then `*.example.com`, then
      `*.com.` Partial wildcards are not supported, and values like
      *w.example.com are invalid. At least one of sni_host and alpn is
      required. Up to 5 sni hosts across all matches can be set.
  """

  alpn = _messages.StringField(1, repeated=True)
  ipAddress = _messages.StringField(2)
  port = _messages.StringField(3)
  sniHost = _messages.StringField(4, repeated=True)


class TlsRouteRouteRule(_messages.Message):
  r"""Specifies how to match traffic and how to route traffic when traffic is
  matched.

  Messages:
    MetadataValue: Optional. Set of label tags associated with the RouteRule
      resource.

  Fields:
    action: Required. The detailed rule defining how to route matched traffic.
    matches: Required. RouteMatch defines the predicate used to match requests
      to a given action. Multiple match types are "OR"ed for evaluation.
    metadata: Optional. Set of label tags associated with the RouteRule
      resource.
    workloadContextSelectors: Optional. Selects the workload where the route
      rule should be applied to its targets. A route rule without a
      WorkloadContextSelector should always be applied to its targets when
      there is no conflict. If there are multiple WorkloadContextSelectors
      then the policy will be applied to all targets if ANY of the
      WorkloadContextSelectors match. Therefore these selectors can be
      combined in an OR fashion.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class MetadataValue(_messages.Message):
    r"""Optional. Set of label tags associated with the RouteRule resource.

    Messages:
      AdditionalProperty: An additional property for a MetadataValue object.

    Fields:
      additionalProperties: Additional properties of type MetadataValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a MetadataValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  action = _messages.MessageField('TlsRouteRouteAction', 1)
  matches = _messages.MessageField('TlsRouteRouteMatch', 2, repeated=True)
  metadata = _messages.MessageField('MetadataValue', 3)
  workloadContextSelectors = _messages.MessageField('WorkloadContextSelector', 4, repeated=True)


class TrafficPortSelector(_messages.Message):
  r"""Specification of a port-based selector.

  Fields:
    ports: Optional. A list of ports. Can be port numbers or port range
      (example, [80-90] specifies all ports from 80 to 90, including 80 and
      90) or named ports or * to specify all ports. If the list is empty, all
      ports are selected.
  """

  ports = _messages.StringField(1, repeated=True)


class WorkloadContextSelector(_messages.Message):
  r"""Determines which workloads a policy is applicable for.

  Fields:
    metadataSelectors: Required. A map of metadata label values used to select
      workloads. If multiple MetadataSelectors are provided, all
      MetadataSelectors must match in order for the policy to be applied to
      this workload. Therefore these selectors must be combined in an AND
      fashion.
  """

  metadataSelectors = _messages.MessageField('WorkloadContextSelectorMetadataSelector', 1, repeated=True)


class WorkloadContextSelectorMetadataSelector(_messages.Message):
  r"""This message type exists as opposed to using a map to support additional
  fields in the future such as priority.

  Fields:
    key: Required. The metadata field being selected on
    value: Required. The value for this metadata field to be compared with
  """

  key = _messages.StringField(1)
  value = _messages.StringField(2)


encoding.AddCustomJsonFieldMapping(
    Rule, 'in_', 'in')
encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')

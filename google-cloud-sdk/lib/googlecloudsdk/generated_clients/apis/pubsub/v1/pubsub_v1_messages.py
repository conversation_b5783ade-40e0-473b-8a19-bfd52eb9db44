"""Generated message classes for pubsub version v1.

Provides reliable, many-to-many, asynchronous messaging between applications.
"""
# NOTE: This file is autogenerated and should not be edited by hand.

from __future__ import absolute_import

from apitools.base.protorpclite import messages as _messages
from apitools.base.py import encoding


package = 'pubsub'


class AcknowledgeRequest(_messages.Message):
  r"""Request for the Acknowledge method.

  Fields:
    ackIds: Required. The acknowledgment ID for the messages being
      acknowledged that was returned by the Pub/Sub system in the `Pull`
      response. Must not be empty.
  """

  ackIds = _messages.StringField(1, repeated=True)


class AvroConfig(_messages.Message):
  r"""Configuration for writing message data in Avro format. Message payloads
  and metadata will be written to files as an Avro binary.

  Fields:
    writeMetadata: Optional. When true, write the subscription name,
      message_id, publish_time, attributes, and ordering_key as additional
      fields in the output. The subscription name, message_id, and
      publish_time fields are put in their own fields while all other message
      properties other than data (for example, an ordering_key, if present)
      are added as entries in the attributes map.
  """

  writeMetadata = _messages.BooleanField(1)


class AwsKinesis(_messages.Message):
  r"""Ingestion settings for Amazon Kinesis Data Streams.

  Enums:
    StateValueValuesEnum: Output only. An output-only field that indicates the
      state of the Kinesis ingestion source.

  Fields:
    awsRoleArn: Required. AWS role ARN to be used for Federated Identity
      authentication with Kinesis. Check the Pub/Sub docs for how to set up
      this role and the required permissions that need to be attached to it.
    consumerArn: Required. The Kinesis consumer ARN to used for ingestion in
      Enhanced Fan-Out mode. The consumer must be already created and ready to
      be used.
    gcpServiceAccount: Required. The GCP service account to be used for
      Federated Identity authentication with Kinesis (via a
      `AssumeRoleWithWebIdentity` call for the provided role using the
      gcp_service_account for this project). The `aws_role_arn` must be set up
      with `accounts.google.com:sub` equals to this account number.
    state: Output only. An output-only field that indicates the state of the
      Kinesis ingestion source.
    streamArn: Required. The Kinesis stream ARN to ingest data from.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. An output-only field that indicates the state of the
    Kinesis ingestion source.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      ACTIVE: Ingestion is active.
      KINESIS_PERMISSION_DENIED: Permission denied encountered while consuming
        data from Kinesis. This can happen if: - The provided `aws_role_arn`
        does not exist or does not have the appropriate permissions attached.
        - The provided `aws_role_arn` is not set up properly for Identity
        Federation using `gcp_service_account`. - The Pub/Sub SA is not
        granted the `iam.serviceAccounts.getOpenIdToken` permission on
        `gcp_service_account`.
      PUBLISH_PERMISSION_DENIED: Permission denied encountered while
        publishing to the topic. This can happen due to Pub/Sub SA has not
        been granted the [appropriate publish
        permissions](https://cloud.google.com/pubsub/docs/access-
        control#pubsub.publisher)
      STREAM_NOT_FOUND: The Kinesis stream does not exist.
      CONSUMER_NOT_FOUND: The Kinesis consumer does not exist.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    KINESIS_PERMISSION_DENIED = 2
    PUBLISH_PERMISSION_DENIED = 3
    STREAM_NOT_FOUND = 4
    CONSUMER_NOT_FOUND = 5

  awsRoleArn = _messages.StringField(1)
  consumerArn = _messages.StringField(2)
  gcpServiceAccount = _messages.StringField(3)
  state = _messages.EnumField('StateValueValuesEnum', 4)
  streamArn = _messages.StringField(5)


class BigQueryConfig(_messages.Message):
  r"""Configuration for a BigQuery subscription.

  Enums:
    StateValueValuesEnum: Output only. An output-only field that indicates
      whether or not the subscription can receive messages.

  Fields:
    dropUnknownFields: Optional. When true and use_topic_schema is true, any
      fields that are a part of the topic schema that are not part of the
      BigQuery table schema are dropped when writing to BigQuery. Otherwise,
      the schemas must be kept in sync and any messages with extra fields are
      not written and remain in the subscription's backlog.
    state: Output only. An output-only field that indicates whether or not the
      subscription can receive messages.
    table: Optional. The name of the table to which to write data, of the form
      {projectId}.{datasetId}.{tableId}
    useTableSchema: Optional. When true, use the BigQuery table's schema as
      the columns to write to in BigQuery.
    useTopicSchema: Optional. When true, use the topic's schema as the columns
      to write to in BigQuery, if it exists.
    writeMetadata: Optional. When true, write the subscription name,
      message_id, publish_time, attributes, and ordering_key to additional
      columns in the table. The subscription name, message_id, and
      publish_time fields are put in their own columns while all other message
      properties (other than data) are written to a JSON object in the
      attributes column.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. An output-only field that indicates whether or not the
    subscription can receive messages.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      ACTIVE: The subscription can actively send messages to BigQuery
      PERMISSION_DENIED: Cannot write to the BigQuery table because of
        permission denied errors. This can happen if - Pub/Sub SA has not been
        granted the [appropriate BigQuery IAM
        permissions](https://cloud.google.com/pubsub/docs/create-
        subscription#assign_bigquery_service_account) -
        bigquery.googleapis.com API is not enabled for the project
        ([instructions](https://cloud.google.com/service-usage/docs/enable-
        disable))
      NOT_FOUND: Cannot write to the BigQuery table because it does not exist.
      SCHEMA_MISMATCH: Cannot write to the BigQuery table due to a schema
        mismatch.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    PERMISSION_DENIED = 2
    NOT_FOUND = 3
    SCHEMA_MISMATCH = 4

  dropUnknownFields = _messages.BooleanField(1)
  state = _messages.EnumField('StateValueValuesEnum', 2)
  table = _messages.StringField(3)
  useTableSchema = _messages.BooleanField(4)
  useTopicSchema = _messages.BooleanField(5)
  writeMetadata = _messages.BooleanField(6)


class Binding(_messages.Message):
  r"""Associates `members`, or principals, with a `role`.

  Fields:
    condition: The condition that is associated with this binding. If the
      condition evaluates to `true`, then this binding applies to the current
      request. If the condition evaluates to `false`, then this binding does
      not apply to the current request. However, a different role binding
      might grant the same role to one or more of the principals in this
      binding. To learn which resources support conditions in their IAM
      policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    members: Specifies the principals requesting access for a Google Cloud
      resource. `members` can have the following values: * `allUsers`: A
      special identifier that represents anyone who is on the internet; with
      or without a Google account. * `allAuthenticatedUsers`: A special
      identifier that represents anyone who is authenticated with a Google
      account or a service account. Does not include identities that come from
      external identity providers (IdPs) through identity federation. *
      `user:{emailid}`: An email address that represents a specific Google
      account. For example, `<EMAIL>` . *
      `serviceAccount:{emailid}`: An email address that represents a Google
      service account. For example, `my-other-
      <EMAIL>`. *
      `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`:
      An identifier for a [Kubernetes service
      account](https://cloud.google.com/kubernetes-engine/docs/how-
      to/kubernetes-service-accounts). For example, `my-
      project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
      `group:{emailid}`: An email address that represents a Google group. For
      example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
      (primary) that represents all the users of that domain. For example,
      `google.com` or `example.com`. *
      `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
      identifier) representing a user that has been recently deleted. For
      example, `<EMAIL>?uid=123456789012345678901`. If the user is
      recovered, this value reverts to `user:{emailid}` and the recovered user
      retains the role in the binding. *
      `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address
      (plus unique identifier) representing a service account that has been
      recently deleted. For example, `my-other-
      <EMAIL>?uid=123456789012345678901`. If the
      service account is undeleted, this value reverts to
      `serviceAccount:{emailid}` and the undeleted service account retains the
      role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An
      email address (plus unique identifier) representing a Google group that
      has been recently deleted. For example,
      `<EMAIL>?uid=123456789012345678901`. If the group is
      recovered, this value reverts to `group:{emailid}` and the recovered
      group retains the role in the binding.
    role: Role that is assigned to the list of `members`, or principals. For
      example, `roles/viewer`, `roles/editor`, or `roles/owner`.
  """

  condition = _messages.MessageField('Expr', 1)
  members = _messages.StringField(2, repeated=True)
  role = _messages.StringField(3)


class CloudStorageConfig(_messages.Message):
  r"""Configuration for a Cloud Storage subscription.

  Enums:
    StateValueValuesEnum: Output only. An output-only field that indicates
      whether or not the subscription can receive messages.

  Fields:
    avroConfig: Optional. If set, message data will be written to Cloud
      Storage in Avro format.
    bucket: Required. User-provided name for the Cloud Storage bucket. The
      bucket must be created by the user. The bucket name must be without any
      prefix like "gs://". See the [bucket naming requirements]
      (https://cloud.google.com/storage/docs/buckets#naming).
    filenamePrefix: Optional. User-provided prefix for Cloud Storage filename.
      See the [object naming
      requirements](https://cloud.google.com/storage/docs/objects#naming).
    filenameSuffix: Optional. User-provided suffix for Cloud Storage filename.
      See the [object naming
      requirements](https://cloud.google.com/storage/docs/objects#naming).
      Must not end in "/".
    maxBytes: Optional. The maximum bytes that can be written to a Cloud
      Storage file before a new file is created. Min 1 KB, max 10 GiB. The
      max_bytes limit may be exceeded in cases where messages are larger than
      the limit.
    maxDuration: Optional. The maximum duration that can elapse before a new
      Cloud Storage file is created. Min 1 minute, max 10 minutes, default 5
      minutes. May not exceed the subscription's acknowledgement deadline.
    state: Output only. An output-only field that indicates whether or not the
      subscription can receive messages.
    textConfig: Optional. If set, message data will be written to Cloud
      Storage in text format.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. An output-only field that indicates whether or not the
    subscription can receive messages.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      ACTIVE: The subscription can actively send messages to Cloud Storage.
      PERMISSION_DENIED: Cannot write to the Cloud Storage bucket because of
        permission denied errors.
      NOT_FOUND: Cannot write to the Cloud Storage bucket because it does not
        exist.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    PERMISSION_DENIED = 2
    NOT_FOUND = 3

  avroConfig = _messages.MessageField('AvroConfig', 1)
  bucket = _messages.StringField(2)
  filenamePrefix = _messages.StringField(3)
  filenameSuffix = _messages.StringField(4)
  maxBytes = _messages.IntegerField(5)
  maxDuration = _messages.StringField(6)
  state = _messages.EnumField('StateValueValuesEnum', 7)
  textConfig = _messages.MessageField('TextConfig', 8)


class CommitSchemaRequest(_messages.Message):
  r"""Request for CommitSchema method.

  Fields:
    schema: Required. The schema revision to commit.
  """

  schema = _messages.MessageField('Schema', 1)


class CreateSnapshotRequest(_messages.Message):
  r"""Request for the `CreateSnapshot` method.

  Messages:
    LabelsValue: Optional. See [Creating and managing
      labels](https://cloud.google.com/pubsub/docs/labels).

  Fields:
    labels: Optional. See [Creating and managing
      labels](https://cloud.google.com/pubsub/docs/labels).
    subscription: Required. The subscription whose backlog the snapshot
      retains. Specifically, the created snapshot is guaranteed to retain: (a)
      The existing backlog on the subscription. More precisely, this is
      defined as the messages in the subscription's backlog that are
      unacknowledged upon the successful completion of the `CreateSnapshot`
      request; as well as: (b) Any messages published to the subscription's
      topic following the successful completion of the CreateSnapshot request.
      Format is `projects/{project}/subscriptions/{sub}`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. See [Creating and managing
    labels](https://cloud.google.com/pubsub/docs/labels).

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  labels = _messages.MessageField('LabelsValue', 1)
  subscription = _messages.StringField(2)


class DeadLetterPolicy(_messages.Message):
  r"""Dead lettering is done on a best effort basis. The same message might be
  dead lettered multiple times. If validation on any of the fields fails at
  subscription creation/updation, the create/update subscription request will
  fail.

  Fields:
    deadLetterTopic: Optional. The name of the topic to which dead letter
      messages should be published. Format is
      `projects/{project}/topics/{topic}`.The Pub/Sub service account
      associated with the enclosing subscription's parent project (i.e.,
      service-{project_number}@gcp-sa-pubsub.iam.gserviceaccount.com) must
      have permission to Publish() to this topic. The operation will fail if
      the topic does not exist. Users should ensure that there is a
      subscription attached to this topic since messages published to a topic
      with no subscriptions are lost.
    maxDeliveryAttempts: Optional. The maximum number of delivery attempts for
      any message. The value must be between 5 and 100. The number of delivery
      attempts is defined as 1 + (the sum of number of NACKs and number of
      times the acknowledgement deadline has been exceeded for the message). A
      NACK is any call to ModifyAckDeadline with a 0 deadline. Note that
      client libraries may automatically extend ack_deadlines. This field will
      be honored on a best effort basis. If this parameter is 0, a default
      value of 5 is used.
  """

  deadLetterTopic = _messages.StringField(1)
  maxDeliveryAttempts = _messages.IntegerField(2, variant=_messages.Variant.INT32)


class DetachSubscriptionResponse(_messages.Message):
  r"""Response for the DetachSubscription method. Reserved for future use."""


class Empty(_messages.Message):
  r"""A generic empty message that you can re-use to avoid defining duplicated
  empty messages in your APIs. A typical example is to use it as the request
  or the response type of an API method. For instance: service Foo { rpc
  Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
  """



class ExpirationPolicy(_messages.Message):
  r"""A policy that specifies the conditions for resource expiration (i.e.,
  automatic resource deletion).

  Fields:
    ttl: Optional. Specifies the "time-to-live" duration for an associated
      resource. The resource expires if it is not active for a period of
      `ttl`. The definition of "activity" depends on the type of the
      associated resource. The minimum and maximum allowed values for `ttl`
      depend on the type of the associated resource, as well. If `ttl` is not
      set, the associated resource never expires.
  """

  ttl = _messages.StringField(1)


class Expr(_messages.Message):
  r"""Represents a textual expression in the Common Expression Language (CEL)
  syntax. CEL is a C-like expression language. The syntax and semantics of CEL
  are documented at https://github.com/google/cel-spec. Example (Comparison):
  title: "Summary size limit" description: "Determines if a summary is less
  than 100 chars" expression: "document.summary.size() < 100" Example
  (Equality): title: "Requestor is owner" description: "Determines if
  requestor is the document owner" expression: "document.owner ==
  request.auth.claims.email" Example (Logic): title: "Public documents"
  description: "Determine whether the document should be publicly visible"
  expression: "document.type != 'private' && document.type != 'internal'"
  Example (Data Manipulation): title: "Notification string" description:
  "Create a notification string with a timestamp." expression: "'New message
  received at ' + string(document.create_time)" The exact variables and
  functions that may be referenced within an expression are determined by the
  service that evaluates it. See the service documentation for additional
  information.

  Fields:
    description: Optional. Description of the expression. This is a longer
      text which describes the expression, e.g. when hovered over it in a UI.
    expression: Textual representation of an expression in Common Expression
      Language syntax.
    location: Optional. String indicating the location of the expression for
      error reporting, e.g. a file name and a position in the file.
    title: Optional. Title for the expression, i.e. a short string describing
      its purpose. This can be used e.g. in UIs which allow to enter the
      expression.
  """

  description = _messages.StringField(1)
  expression = _messages.StringField(2)
  location = _messages.StringField(3)
  title = _messages.StringField(4)


class IngestionDataSourceSettings(_messages.Message):
  r"""Settings for an ingestion data source on a topic.

  Fields:
    awsKinesis: Optional. Amazon Kinesis Data Streams.
  """

  awsKinesis = _messages.MessageField('AwsKinesis', 1)


class ListSchemaRevisionsResponse(_messages.Message):
  r"""Response for the `ListSchemaRevisions` method.

  Fields:
    nextPageToken: A token that can be sent as `page_token` to retrieve the
      next page. If this field is empty, there are no subsequent pages.
    schemas: The revisions of the schema.
  """

  nextPageToken = _messages.StringField(1)
  schemas = _messages.MessageField('Schema', 2, repeated=True)


class ListSchemasResponse(_messages.Message):
  r"""Response for the `ListSchemas` method.

  Fields:
    nextPageToken: If not empty, indicates that there may be more schemas that
      match the request; this value should be passed in a new
      `ListSchemasRequest`.
    schemas: The resulting schemas.
  """

  nextPageToken = _messages.StringField(1)
  schemas = _messages.MessageField('Schema', 2, repeated=True)


class ListSnapshotsResponse(_messages.Message):
  r"""Response for the `ListSnapshots` method.

  Fields:
    nextPageToken: Optional. If not empty, indicates that there may be more
      snapshot that match the request; this value should be passed in a new
      `ListSnapshotsRequest`.
    snapshots: Optional. The resulting snapshots.
  """

  nextPageToken = _messages.StringField(1)
  snapshots = _messages.MessageField('Snapshot', 2, repeated=True)


class ListSubscriptionsResponse(_messages.Message):
  r"""Response for the `ListSubscriptions` method.

  Fields:
    nextPageToken: Optional. If not empty, indicates that there may be more
      subscriptions that match the request; this value should be passed in a
      new `ListSubscriptionsRequest` to get more subscriptions.
    subscriptions: Optional. The subscriptions that match the request.
  """

  nextPageToken = _messages.StringField(1)
  subscriptions = _messages.MessageField('Subscription', 2, repeated=True)


class ListTopicSnapshotsResponse(_messages.Message):
  r"""Response for the `ListTopicSnapshots` method.

  Fields:
    nextPageToken: Optional. If not empty, indicates that there may be more
      snapshots that match the request; this value should be passed in a new
      `ListTopicSnapshotsRequest` to get more snapshots.
    snapshots: Optional. The names of the snapshots that match the request.
  """

  nextPageToken = _messages.StringField(1)
  snapshots = _messages.StringField(2, repeated=True)


class ListTopicSubscriptionsResponse(_messages.Message):
  r"""Response for the `ListTopicSubscriptions` method.

  Fields:
    nextPageToken: Optional. If not empty, indicates that there may be more
      subscriptions that match the request; this value should be passed in a
      new `ListTopicSubscriptionsRequest` to get more subscriptions.
    subscriptions: Optional. The names of subscriptions attached to the topic
      specified in the request.
  """

  nextPageToken = _messages.StringField(1)
  subscriptions = _messages.StringField(2, repeated=True)


class ListTopicsResponse(_messages.Message):
  r"""Response for the `ListTopics` method.

  Fields:
    nextPageToken: Optional. If not empty, indicates that there may be more
      topics that match the request; this value should be passed in a new
      `ListTopicsRequest`.
    topics: Optional. The resulting topics.
  """

  nextPageToken = _messages.StringField(1)
  topics = _messages.MessageField('Topic', 2, repeated=True)


class MessageStoragePolicy(_messages.Message):
  r"""A policy constraining the storage of messages published to the topic.

  Fields:
    allowedPersistenceRegions: Optional. A list of IDs of GCP regions where
      messages that are published to the topic may be persisted in storage.
      Messages published by publishers running in non-allowed GCP regions (or
      running outside of GCP altogether) will be routed for storage in one of
      the allowed regions. An empty list means that no regions are allowed,
      and is not a valid configuration.
  """

  allowedPersistenceRegions = _messages.StringField(1, repeated=True)


class ModifyAckDeadlineRequest(_messages.Message):
  r"""Request for the ModifyAckDeadline method.

  Fields:
    ackDeadlineSeconds: Required. The new ack deadline with respect to the
      time this request was sent to the Pub/Sub system. For example, if the
      value is 10, the new ack deadline will expire 10 seconds after the
      `ModifyAckDeadline` call was made. Specifying zero might immediately
      make the message available for delivery to another subscriber client.
      This typically results in an increase in the rate of message
      redeliveries (that is, duplicates). The minimum deadline you can specify
      is 0 seconds. The maximum deadline you can specify is 600 seconds (10
      minutes).
    ackIds: Required. List of acknowledgment IDs.
  """

  ackDeadlineSeconds = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  ackIds = _messages.StringField(2, repeated=True)


class ModifyPushConfigRequest(_messages.Message):
  r"""Request for the ModifyPushConfig method.

  Fields:
    pushConfig: Required. The push configuration for future deliveries. An
      empty `pushConfig` indicates that the Pub/Sub system should stop pushing
      messages from the given subscription and allow messages to be pulled and
      acknowledged - effectively pausing the subscription if `Pull` or
      `StreamingPull` is not called.
  """

  pushConfig = _messages.MessageField('PushConfig', 1)


class NoWrapper(_messages.Message):
  r"""Sets the `data` field as the HTTP body for delivery.

  Fields:
    writeMetadata: Optional. When true, writes the Pub/Sub message metadata to
      `x-goog-pubsub-:` headers of the HTTP request. Writes the Pub/Sub
      message attributes to `:` headers of the HTTP request.
  """

  writeMetadata = _messages.BooleanField(1)


class OidcToken(_messages.Message):
  r"""Contains information needed for generating an [OpenID Connect
  token](https://developers.google.com/identity/protocols/OpenIDConnect).

  Fields:
    audience: Optional. Audience to be used when generating OIDC token. The
      audience claim identifies the recipients that the JWT is intended for.
      The audience value is a single case-sensitive string. Having multiple
      values (array) for the audience field is not supported. More info about
      the OIDC JWT token audience here:
      https://tools.ietf.org/html/rfc7519#section-4.1.3 Note: if not
      specified, the Push endpoint URL will be used.
    serviceAccountEmail: Optional. [Service account
      email](https://cloud.google.com/iam/docs/service-accounts) used for
      generating the OIDC token. For more information on setting up
      authentication, see [Push
      subscriptions](https://cloud.google.com/pubsub/docs/push).
  """

  audience = _messages.StringField(1)
  serviceAccountEmail = _messages.StringField(2)


class Policy(_messages.Message):
  r"""An Identity and Access Management (IAM) policy, which specifies access
  controls for Google Cloud resources. A `Policy` is a collection of
  `bindings`. A `binding` binds one or more `members`, or principals, to a
  single `role`. Principals can be user accounts, service accounts, Google
  groups, and domains (such as G Suite). A `role` is a named list of
  permissions; each `role` can be an IAM predefined role or a user-created
  custom role. For some types of Google Cloud resources, a `binding` can also
  specify a `condition`, which is a logical expression that allows access to a
  resource only if the expression evaluates to `true`. A condition can add
  constraints based on attributes of the request, the resource, or both. To
  learn which resources support conditions in their IAM policies, see the [IAM
  documentation](https://cloud.google.com/iam/help/conditions/resource-
  policies). **JSON example:** ``` { "bindings": [ { "role":
  "roles/resourcemanager.organizationAdmin", "members": [
  "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
  "serviceAccount:<EMAIL>" ] }, { "role":
  "roles/resourcemanager.organizationViewer", "members": [
  "user:<EMAIL>" ], "condition": { "title": "expirable access",
  "description": "Does not grant access after Sep 2020", "expression":
  "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
  "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
  members: - user:<EMAIL> - group:<EMAIL> -
  domain:google.com - serviceAccount:<EMAIL>
  role: roles/resourcemanager.organizationAdmin - members: -
  user:<EMAIL> role: roles/resourcemanager.organizationViewer
  condition: title: expirable access description: Does not grant access after
  Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
  etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
  see the [IAM documentation](https://cloud.google.com/iam/docs/).

  Fields:
    bindings: Associates a list of `members`, or principals, with a `role`.
      Optionally, may specify a `condition` that determines how and when the
      `bindings` are applied. Each of the `bindings` must contain at least one
      principal. The `bindings` in a `Policy` can refer to up to 1,500
      principals; up to 250 of these principals can be Google groups. Each
      occurrence of a principal counts towards these limits. For example, if
      the `bindings` grant 50 different roles to `user:<EMAIL>`, and
      not to any other principal, then you can add another 1,450 principals to
      the `bindings` in the `Policy`.
    etag: `etag` is used for optimistic concurrency control as a way to help
      prevent simultaneous updates of a policy from overwriting each other. It
      is strongly suggested that systems make use of the `etag` in the read-
      modify-write cycle to perform policy updates in order to avoid race
      conditions: An `etag` is returned in the response to `getIamPolicy`, and
      systems are expected to put that etag in the request to `setIamPolicy`
      to ensure that their change will be applied to the same version of the
      policy. **Important:** If you use IAM Conditions, you must include the
      `etag` field whenever you call `setIamPolicy`. If you omit this field,
      then IAM allows you to overwrite a version `3` policy with a version `1`
      policy, and all of the conditions in the version `3` policy are lost.
    version: Specifies the format of the policy. Valid values are `0`, `1`,
      and `3`. Requests that specify an invalid value are rejected. Any
      operation that affects conditional role bindings must specify version
      `3`. This requirement applies to the following operations: * Getting a
      policy that includes a conditional role binding * Adding a conditional
      role binding to a policy * Changing a conditional role binding in a
      policy * Removing any role binding, with or without a condition, from a
      policy that includes conditions **Important:** If you use IAM
      Conditions, you must include the `etag` field whenever you call
      `setIamPolicy`. If you omit this field, then IAM allows you to overwrite
      a version `3` policy with a version `1` policy, and all of the
      conditions in the version `3` policy are lost. If a policy does not
      include any conditions, operations on that policy may specify any valid
      version or leave the field unset. To learn which resources support
      conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
  """

  bindings = _messages.MessageField('Binding', 1, repeated=True)
  etag = _messages.BytesField(2)
  version = _messages.IntegerField(3, variant=_messages.Variant.INT32)


class PubSubExportConfig(_messages.Message):
  r"""Configuration for a Pub/Sub export subscription.

  Enums:
    StateValueValuesEnum: Output only. An output-only field that indicates
      whether or not the subscription can receive messages.

  Fields:
    state: Output only. An output-only field that indicates whether or not the
      subscription can receive messages.
    topic: Optional. The name of the topic to which to write data, of the form
      projects/{project_id}/topics/{topic_id}
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. An output-only field that indicates whether or not the
    subscription can receive messages.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      ACTIVE: The subscription can actively send messages
      PERMISSION_DENIED: Cannot write to the destination because of permission
        denied errors.
      NOT_FOUND: Cannot write to the destination because it does not exist.
      SCHEMA_MISMATCH: Cannot write to the destination due to a schema
        mismatch.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    PERMISSION_DENIED = 2
    NOT_FOUND = 3
    SCHEMA_MISMATCH = 4

  state = _messages.EnumField('StateValueValuesEnum', 1)
  topic = _messages.StringField(2)


class PubSubLiteExportConfig(_messages.Message):
  r"""Configuration for a Pub/Sub Lite export subscription.

  Enums:
    StateValueValuesEnum: Output only. An output-only field that indicates
      whether or not the subscription can receive messages.

  Fields:
    state: Output only. An output-only field that indicates whether or not the
      subscription can receive messages.
    topic: Optional. The name of the topic to which to write data, of the form
      projects/{project_id}/locations/{location_id}/topics/{topic_id} Pushes
      occur in the same region as the Pub/Sub Lite topic. If this is different
      from the location the messages were published to, egress fees will be
      incurred.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. An output-only field that indicates whether or not the
    subscription can receive messages.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      ACTIVE: The subscription can actively send messages
      PERMISSION_DENIED: Cannot write to the destination because of permission
        denied errors.
      NOT_FOUND: Cannot write to the destination because it does not exist.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    PERMISSION_DENIED = 2
    NOT_FOUND = 3

  state = _messages.EnumField('StateValueValuesEnum', 1)
  topic = _messages.StringField(2)


class PublishRequest(_messages.Message):
  r"""Request for the Publish method.

  Fields:
    messages: Required. The messages to publish.
  """

  messages = _messages.MessageField('PubsubMessage', 1, repeated=True)


class PublishResponse(_messages.Message):
  r"""Response for the `Publish` method.

  Fields:
    messageIds: Optional. The server-assigned ID of each published message, in
      the same order as the messages in the request. IDs are guaranteed to be
      unique within the topic.
  """

  messageIds = _messages.StringField(1, repeated=True)


class PubsubMessage(_messages.Message):
  r"""A message that is published by publishers and consumed by subscribers.
  The message must contain either a non-empty data field or at least one
  attribute. Note that client libraries represent this object differently
  depending on the language. See the corresponding [client library
  documentation](https://cloud.google.com/pubsub/docs/reference/libraries) for
  more information. See [quotas and limits]
  (https://cloud.google.com/pubsub/quotas) for more information about message
  limits.

  Messages:
    AttributesValue: Optional. Attributes for this message. If this field is
      empty, the message must contain non-empty data. This can be used to
      filter messages on the subscription.

  Fields:
    attributes: Optional. Attributes for this message. If this field is empty,
      the message must contain non-empty data. This can be used to filter
      messages on the subscription.
    data: Optional. The message data field. If this field is empty, the
      message must contain at least one attribute.
    messageId: Optional. ID of this message, assigned by the server when the
      message is published. Guaranteed to be unique within the topic. This
      value may be read by a subscriber that receives a `PubsubMessage` via a
      `Pull` call or a push delivery. It must not be populated by the
      publisher in a `Publish` call.
    orderingKey: Optional. If non-empty, identifies related messages for which
      publish order should be respected. If a `Subscription` has
      `enable_message_ordering` set to `true`, messages published with the
      same non-empty `ordering_key` value will be delivered to subscribers in
      the order in which they are received by the Pub/Sub system. All
      `PubsubMessage`s published in a given `PublishRequest` must specify the
      same `ordering_key` value. For more information, see [ordering
      messages](https://cloud.google.com/pubsub/docs/ordering).
    publishTime: Optional. The time at which the message was published,
      populated by the server when it receives the `Publish` call. It must not
      be populated by the publisher in a `Publish` call.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributesValue(_messages.Message):
    r"""Optional. Attributes for this message. If this field is empty, the
    message must contain non-empty data. This can be used to filter messages
    on the subscription.

    Messages:
      AdditionalProperty: An additional property for a AttributesValue object.

    Fields:
      additionalProperties: Additional properties of type AttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributes = _messages.MessageField('AttributesValue', 1)
  data = _messages.BytesField(2)
  messageId = _messages.StringField(3)
  orderingKey = _messages.StringField(4)
  publishTime = _messages.StringField(5)


class PubsubProjectsSchemasCommitRequest(_messages.Message):
  r"""A PubsubProjectsSchemasCommitRequest object.

  Fields:
    commitSchemaRequest: A CommitSchemaRequest resource to be passed as the
      request body.
    name: Required. The name of the schema we are revising. Format is
      `projects/{project}/schemas/{schema}`.
  """

  commitSchemaRequest = _messages.MessageField('CommitSchemaRequest', 1)
  name = _messages.StringField(2, required=True)


class PubsubProjectsSchemasCreateRequest(_messages.Message):
  r"""A PubsubProjectsSchemasCreateRequest object.

  Fields:
    parent: Required. The name of the project in which to create the schema.
      Format is `projects/{project-id}`.
    schema: A Schema resource to be passed as the request body.
    schemaId: The ID to use for the schema, which will become the final
      component of the schema's resource name. See
      https://cloud.google.com/pubsub/docs/admin#resource_names for resource
      name constraints.
  """

  parent = _messages.StringField(1, required=True)
  schema = _messages.MessageField('Schema', 2)
  schemaId = _messages.StringField(3)


class PubsubProjectsSchemasDeleteRequest(_messages.Message):
  r"""A PubsubProjectsSchemasDeleteRequest object.

  Fields:
    name: Required. Name of the schema to delete. Format is
      `projects/{project}/schemas/{schema}`.
  """

  name = _messages.StringField(1, required=True)


class PubsubProjectsSchemasDeleteRevisionRequest(_messages.Message):
  r"""A PubsubProjectsSchemasDeleteRevisionRequest object.

  Fields:
    name: Required. The name of the schema revision to be deleted, with a
      revision ID explicitly included. Example: `projects/123/schemas/my-
      schema@c7cfa2a8`
    revisionId: Optional. This field is deprecated and should not be used for
      specifying the revision ID. The revision ID should be specified via the
      `name` parameter.
  """

  name = _messages.StringField(1, required=True)
  revisionId = _messages.StringField(2)


class PubsubProjectsSchemasGetIamPolicyRequest(_messages.Message):
  r"""A PubsubProjectsSchemasGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class PubsubProjectsSchemasGetRequest(_messages.Message):
  r"""A PubsubProjectsSchemasGetRequest object.

  Enums:
    ViewValueValuesEnum: The set of fields to return in the response. If not
      set, returns a Schema with all fields filled out. Set to `BASIC` to omit
      the `definition`.

  Fields:
    name: Required. The name of the schema to get. Format is
      `projects/{project}/schemas/{schema}`.
    view: The set of fields to return in the response. If not set, returns a
      Schema with all fields filled out. Set to `BASIC` to omit the
      `definition`.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The set of fields to return in the response. If not set, returns a
    Schema with all fields filled out. Set to `BASIC` to omit the
    `definition`.

    Values:
      SCHEMA_VIEW_UNSPECIFIED: The default / unset value. The API will default
        to the BASIC view.
      BASIC: Include the name and type of the schema, but not the definition.
      FULL: Include all Schema object fields.
    """
    SCHEMA_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 2)


class PubsubProjectsSchemasListRequest(_messages.Message):
  r"""A PubsubProjectsSchemasListRequest object.

  Enums:
    ViewValueValuesEnum: The set of Schema fields to return in the response.
      If not set, returns Schemas with `name` and `type`, but not
      `definition`. Set to `FULL` to retrieve all fields.

  Fields:
    pageSize: Maximum number of schemas to return.
    pageToken: The value returned by the last `ListSchemasResponse`; indicates
      that this is a continuation of a prior `ListSchemas` call, and that the
      system should return the next page of data.
    parent: Required. The name of the project in which to list schemas. Format
      is `projects/{project-id}`.
    view: The set of Schema fields to return in the response. If not set,
      returns Schemas with `name` and `type`, but not `definition`. Set to
      `FULL` to retrieve all fields.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The set of Schema fields to return in the response. If not set,
    returns Schemas with `name` and `type`, but not `definition`. Set to
    `FULL` to retrieve all fields.

    Values:
      SCHEMA_VIEW_UNSPECIFIED: The default / unset value. The API will default
        to the BASIC view.
      BASIC: Include the name and type of the schema, but not the definition.
      FULL: Include all Schema object fields.
    """
    SCHEMA_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  parent = _messages.StringField(3, required=True)
  view = _messages.EnumField('ViewValueValuesEnum', 4)


class PubsubProjectsSchemasListRevisionsRequest(_messages.Message):
  r"""A PubsubProjectsSchemasListRevisionsRequest object.

  Enums:
    ViewValueValuesEnum: The set of Schema fields to return in the response.
      If not set, returns Schemas with `name` and `type`, but not
      `definition`. Set to `FULL` to retrieve all fields.

  Fields:
    name: Required. The name of the schema to list revisions for.
    pageSize: The maximum number of revisions to return per page.
    pageToken: The page token, received from a previous ListSchemaRevisions
      call. Provide this to retrieve the subsequent page.
    view: The set of Schema fields to return in the response. If not set,
      returns Schemas with `name` and `type`, but not `definition`. Set to
      `FULL` to retrieve all fields.
  """

  class ViewValueValuesEnum(_messages.Enum):
    r"""The set of Schema fields to return in the response. If not set,
    returns Schemas with `name` and `type`, but not `definition`. Set to
    `FULL` to retrieve all fields.

    Values:
      SCHEMA_VIEW_UNSPECIFIED: The default / unset value. The API will default
        to the BASIC view.
      BASIC: Include the name and type of the schema, but not the definition.
      FULL: Include all Schema object fields.
    """
    SCHEMA_VIEW_UNSPECIFIED = 0
    BASIC = 1
    FULL = 2

  name = _messages.StringField(1, required=True)
  pageSize = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(3)
  view = _messages.EnumField('ViewValueValuesEnum', 4)


class PubsubProjectsSchemasRollbackRequest(_messages.Message):
  r"""A PubsubProjectsSchemasRollbackRequest object.

  Fields:
    name: Required. The schema being rolled back with revision id.
    rollbackSchemaRequest: A RollbackSchemaRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  rollbackSchemaRequest = _messages.MessageField('RollbackSchemaRequest', 2)


class PubsubProjectsSchemasSetIamPolicyRequest(_messages.Message):
  r"""A PubsubProjectsSchemasSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class PubsubProjectsSchemasTestIamPermissionsRequest(_messages.Message):
  r"""A PubsubProjectsSchemasTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class PubsubProjectsSchemasValidateMessageRequest(_messages.Message):
  r"""A PubsubProjectsSchemasValidateMessageRequest object.

  Fields:
    parent: Required. The name of the project in which to validate schemas.
      Format is `projects/{project-id}`.
    validateMessageRequest: A ValidateMessageRequest resource to be passed as
      the request body.
  """

  parent = _messages.StringField(1, required=True)
  validateMessageRequest = _messages.MessageField('ValidateMessageRequest', 2)


class PubsubProjectsSchemasValidateRequest(_messages.Message):
  r"""A PubsubProjectsSchemasValidateRequest object.

  Fields:
    parent: Required. The name of the project in which to validate schemas.
      Format is `projects/{project-id}`.
    validateSchemaRequest: A ValidateSchemaRequest resource to be passed as
      the request body.
  """

  parent = _messages.StringField(1, required=True)
  validateSchemaRequest = _messages.MessageField('ValidateSchemaRequest', 2)


class PubsubProjectsSnapshotsCreateRequest(_messages.Message):
  r"""A PubsubProjectsSnapshotsCreateRequest object.

  Fields:
    createSnapshotRequest: A CreateSnapshotRequest resource to be passed as
      the request body.
    name: Required. User-provided name for this snapshot. If the name is not
      provided in the request, the server will assign a random name for this
      snapshot on the same project as the subscription. Note that for REST API
      requests, you must specify a name. See the [resource name
      rules](https://cloud.google.com/pubsub/docs/admin#resource_names).
      Format is `projects/{project}/snapshots/{snap}`.
  """

  createSnapshotRequest = _messages.MessageField('CreateSnapshotRequest', 1)
  name = _messages.StringField(2, required=True)


class PubsubProjectsSnapshotsDeleteRequest(_messages.Message):
  r"""A PubsubProjectsSnapshotsDeleteRequest object.

  Fields:
    snapshot: Required. The name of the snapshot to delete. Format is
      `projects/{project}/snapshots/{snap}`.
  """

  snapshot = _messages.StringField(1, required=True)


class PubsubProjectsSnapshotsGetIamPolicyRequest(_messages.Message):
  r"""A PubsubProjectsSnapshotsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class PubsubProjectsSnapshotsGetRequest(_messages.Message):
  r"""A PubsubProjectsSnapshotsGetRequest object.

  Fields:
    snapshot: Required. The name of the snapshot to get. Format is
      `projects/{project}/snapshots/{snap}`.
  """

  snapshot = _messages.StringField(1, required=True)


class PubsubProjectsSnapshotsListRequest(_messages.Message):
  r"""A PubsubProjectsSnapshotsListRequest object.

  Fields:
    pageSize: Optional. Maximum number of snapshots to return.
    pageToken: Optional. The value returned by the last
      `ListSnapshotsResponse`; indicates that this is a continuation of a
      prior `ListSnapshots` call, and that the system should return the next
      page of data.
    project: Required. The name of the project in which to list snapshots.
      Format is `projects/{project-id}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  project = _messages.StringField(3, required=True)


class PubsubProjectsSnapshotsPatchRequest(_messages.Message):
  r"""A PubsubProjectsSnapshotsPatchRequest object.

  Fields:
    name: Optional. The name of the snapshot.
    updateSnapshotRequest: A UpdateSnapshotRequest resource to be passed as
      the request body.
  """

  name = _messages.StringField(1, required=True)
  updateSnapshotRequest = _messages.MessageField('UpdateSnapshotRequest', 2)


class PubsubProjectsSnapshotsSetIamPolicyRequest(_messages.Message):
  r"""A PubsubProjectsSnapshotsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class PubsubProjectsSnapshotsTestIamPermissionsRequest(_messages.Message):
  r"""A PubsubProjectsSnapshotsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class PubsubProjectsSubscriptionsAcknowledgeRequest(_messages.Message):
  r"""A PubsubProjectsSubscriptionsAcknowledgeRequest object.

  Fields:
    acknowledgeRequest: A AcknowledgeRequest resource to be passed as the
      request body.
    subscription: Required. The subscription whose message is being
      acknowledged. Format is `projects/{project}/subscriptions/{sub}`.
  """

  acknowledgeRequest = _messages.MessageField('AcknowledgeRequest', 1)
  subscription = _messages.StringField(2, required=True)


class PubsubProjectsSubscriptionsDeleteRequest(_messages.Message):
  r"""A PubsubProjectsSubscriptionsDeleteRequest object.

  Fields:
    subscription: Required. The subscription to delete. Format is
      `projects/{project}/subscriptions/{sub}`.
  """

  subscription = _messages.StringField(1, required=True)


class PubsubProjectsSubscriptionsDetachRequest(_messages.Message):
  r"""A PubsubProjectsSubscriptionsDetachRequest object.

  Fields:
    subscription: Required. The subscription to detach. Format is
      `projects/{project}/subscriptions/{subscription}`.
  """

  subscription = _messages.StringField(1, required=True)


class PubsubProjectsSubscriptionsGetIamPolicyRequest(_messages.Message):
  r"""A PubsubProjectsSubscriptionsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class PubsubProjectsSubscriptionsGetRequest(_messages.Message):
  r"""A PubsubProjectsSubscriptionsGetRequest object.

  Fields:
    subscription: Required. The name of the subscription to get. Format is
      `projects/{project}/subscriptions/{sub}`.
  """

  subscription = _messages.StringField(1, required=True)


class PubsubProjectsSubscriptionsListRequest(_messages.Message):
  r"""A PubsubProjectsSubscriptionsListRequest object.

  Fields:
    pageSize: Optional. Maximum number of subscriptions to return.
    pageToken: Optional. The value returned by the last
      `ListSubscriptionsResponse`; indicates that this is a continuation of a
      prior `ListSubscriptions` call, and that the system should return the
      next page of data.
    project: Required. The name of the project in which to list subscriptions.
      Format is `projects/{project-id}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  project = _messages.StringField(3, required=True)


class PubsubProjectsSubscriptionsModifyAckDeadlineRequest(_messages.Message):
  r"""A PubsubProjectsSubscriptionsModifyAckDeadlineRequest object.

  Fields:
    modifyAckDeadlineRequest: A ModifyAckDeadlineRequest resource to be passed
      as the request body.
    subscription: Required. The name of the subscription. Format is
      `projects/{project}/subscriptions/{sub}`.
  """

  modifyAckDeadlineRequest = _messages.MessageField('ModifyAckDeadlineRequest', 1)
  subscription = _messages.StringField(2, required=True)


class PubsubProjectsSubscriptionsModifyPushConfigRequest(_messages.Message):
  r"""A PubsubProjectsSubscriptionsModifyPushConfigRequest object.

  Fields:
    modifyPushConfigRequest: A ModifyPushConfigRequest resource to be passed
      as the request body.
    subscription: Required. The name of the subscription. Format is
      `projects/{project}/subscriptions/{sub}`.
  """

  modifyPushConfigRequest = _messages.MessageField('ModifyPushConfigRequest', 1)
  subscription = _messages.StringField(2, required=True)


class PubsubProjectsSubscriptionsPatchRequest(_messages.Message):
  r"""A PubsubProjectsSubscriptionsPatchRequest object.

  Fields:
    name: Required. The name of the subscription. It must have the format
      `"projects/{project}/subscriptions/{subscription}"`. `{subscription}`
      must start with a letter, and contain only letters (`[A-Za-z]`), numbers
      (`[0-9]`), dashes (`-`), underscores (`_`), periods (`.`), tildes (`~`),
      plus (`+`) or percent signs (`%`). It must be between 3 and 255
      characters in length, and it must not start with `"goog"`.
    updateSubscriptionRequest: A UpdateSubscriptionRequest resource to be
      passed as the request body.
  """

  name = _messages.StringField(1, required=True)
  updateSubscriptionRequest = _messages.MessageField('UpdateSubscriptionRequest', 2)


class PubsubProjectsSubscriptionsPullRequest(_messages.Message):
  r"""A PubsubProjectsSubscriptionsPullRequest object.

  Fields:
    pullRequest: A PullRequest resource to be passed as the request body.
    subscription: Required. The subscription from which messages should be
      pulled. Format is `projects/{project}/subscriptions/{sub}`.
  """

  pullRequest = _messages.MessageField('PullRequest', 1)
  subscription = _messages.StringField(2, required=True)


class PubsubProjectsSubscriptionsSeekRequest(_messages.Message):
  r"""A PubsubProjectsSubscriptionsSeekRequest object.

  Fields:
    seekRequest: A SeekRequest resource to be passed as the request body.
    subscription: Required. The subscription to affect.
  """

  seekRequest = _messages.MessageField('SeekRequest', 1)
  subscription = _messages.StringField(2, required=True)


class PubsubProjectsSubscriptionsSetIamPolicyRequest(_messages.Message):
  r"""A PubsubProjectsSubscriptionsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class PubsubProjectsSubscriptionsTestIamPermissionsRequest(_messages.Message):
  r"""A PubsubProjectsSubscriptionsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class PubsubProjectsTopicsDeleteRequest(_messages.Message):
  r"""A PubsubProjectsTopicsDeleteRequest object.

  Fields:
    topic: Required. Name of the topic to delete. Format is
      `projects/{project}/topics/{topic}`.
  """

  topic = _messages.StringField(1, required=True)


class PubsubProjectsTopicsGetIamPolicyRequest(_messages.Message):
  r"""A PubsubProjectsTopicsGetIamPolicyRequest object.

  Fields:
    options_requestedPolicyVersion: Optional. The maximum policy version that
      will be used to format the policy. Valid values are 0, 1, and 3.
      Requests specifying an invalid value will be rejected. Requests for
      policies with any conditional role bindings must specify version 3.
      Policies with no conditional role bindings may specify any valid value
      or leave the field unset. The policy in the response might use the
      policy version that you specified, or it might use a lower policy
      version. For example, if you specify version 3, but the policy has no
      conditional role bindings, the response uses version 1. To learn which
      resources support conditions in their IAM policies, see the [IAM
      documentation](https://cloud.google.com/iam/help/conditions/resource-
      policies).
    resource: REQUIRED: The resource for which the policy is being requested.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
  """

  options_requestedPolicyVersion = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  resource = _messages.StringField(2, required=True)


class PubsubProjectsTopicsGetRequest(_messages.Message):
  r"""A PubsubProjectsTopicsGetRequest object.

  Fields:
    topic: Required. The name of the topic to get. Format is
      `projects/{project}/topics/{topic}`.
  """

  topic = _messages.StringField(1, required=True)


class PubsubProjectsTopicsListRequest(_messages.Message):
  r"""A PubsubProjectsTopicsListRequest object.

  Fields:
    pageSize: Optional. Maximum number of topics to return.
    pageToken: Optional. The value returned by the last `ListTopicsResponse`;
      indicates that this is a continuation of a prior `ListTopics` call, and
      that the system should return the next page of data.
    project: Required. The name of the project in which to list topics. Format
      is `projects/{project-id}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  project = _messages.StringField(3, required=True)


class PubsubProjectsTopicsPatchRequest(_messages.Message):
  r"""A PubsubProjectsTopicsPatchRequest object.

  Fields:
    name: Required. The name of the topic. It must have the format
      `"projects/{project}/topics/{topic}"`. `{topic}` must start with a
      letter, and contain only letters (`[A-Za-z]`), numbers (`[0-9]`), dashes
      (`-`), underscores (`_`), periods (`.`), tildes (`~`), plus (`+`) or
      percent signs (`%`). It must be between 3 and 255 characters in length,
      and it must not start with `"goog"`.
    updateTopicRequest: A UpdateTopicRequest resource to be passed as the
      request body.
  """

  name = _messages.StringField(1, required=True)
  updateTopicRequest = _messages.MessageField('UpdateTopicRequest', 2)


class PubsubProjectsTopicsPublishRequest(_messages.Message):
  r"""A PubsubProjectsTopicsPublishRequest object.

  Fields:
    publishRequest: A PublishRequest resource to be passed as the request
      body.
    topic: Required. The messages in the request will be published on this
      topic. Format is `projects/{project}/topics/{topic}`.
  """

  publishRequest = _messages.MessageField('PublishRequest', 1)
  topic = _messages.StringField(2, required=True)


class PubsubProjectsTopicsSetIamPolicyRequest(_messages.Message):
  r"""A PubsubProjectsTopicsSetIamPolicyRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy is being specified.
      See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    setIamPolicyRequest: A SetIamPolicyRequest resource to be passed as the
      request body.
  """

  resource = _messages.StringField(1, required=True)
  setIamPolicyRequest = _messages.MessageField('SetIamPolicyRequest', 2)


class PubsubProjectsTopicsSnapshotsListRequest(_messages.Message):
  r"""A PubsubProjectsTopicsSnapshotsListRequest object.

  Fields:
    pageSize: Optional. Maximum number of snapshot names to return.
    pageToken: Optional. The value returned by the last
      `ListTopicSnapshotsResponse`; indicates that this is a continuation of a
      prior `ListTopicSnapshots` call, and that the system should return the
      next page of data.
    topic: Required. The name of the topic that snapshots are attached to.
      Format is `projects/{project}/topics/{topic}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  topic = _messages.StringField(3, required=True)


class PubsubProjectsTopicsSubscriptionsListRequest(_messages.Message):
  r"""A PubsubProjectsTopicsSubscriptionsListRequest object.

  Fields:
    pageSize: Optional. Maximum number of subscription names to return.
    pageToken: Optional. The value returned by the last
      `ListTopicSubscriptionsResponse`; indicates that this is a continuation
      of a prior `ListTopicSubscriptions` call, and that the system should
      return the next page of data.
    topic: Required. The name of the topic that subscriptions are attached to.
      Format is `projects/{project}/topics/{topic}`.
  """

  pageSize = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  pageToken = _messages.StringField(2)
  topic = _messages.StringField(3, required=True)


class PubsubProjectsTopicsTestIamPermissionsRequest(_messages.Message):
  r"""A PubsubProjectsTopicsTestIamPermissionsRequest object.

  Fields:
    resource: REQUIRED: The resource for which the policy detail is being
      requested. See [Resource
      names](https://cloud.google.com/apis/design/resource_names) for the
      appropriate value for this field.
    testIamPermissionsRequest: A TestIamPermissionsRequest resource to be
      passed as the request body.
  """

  resource = _messages.StringField(1, required=True)
  testIamPermissionsRequest = _messages.MessageField('TestIamPermissionsRequest', 2)


class PubsubWrapper(_messages.Message):
  r"""The payload to the push endpoint is in the form of the JSON
  representation of a PubsubMessage (https://cloud.google.com/pubsub/docs/refe
  rence/rpc/google.pubsub.v1#pubsubmessage).
  """



class PullRequest(_messages.Message):
  r"""Request for the `Pull` method.

  Fields:
    maxMessages: Required. The maximum number of messages to return for this
      request. Must be a positive integer. The Pub/Sub system may return fewer
      than the number specified.
    returnImmediately: Optional. If this field set to true, the system will
      respond immediately even if it there are no messages available to return
      in the `Pull` response. Otherwise, the system may wait (for a bounded
      amount of time) until at least one message is available, rather than
      returning no messages. Warning: setting this field to `true` is
      discouraged because it adversely impacts the performance of `Pull`
      operations. We recommend that users do not set this field.
  """

  maxMessages = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  returnImmediately = _messages.BooleanField(2)


class PullResponse(_messages.Message):
  r"""Response for the `Pull` method.

  Fields:
    receivedMessages: Optional. Received Pub/Sub messages. The list will be
      empty if there are no more messages available in the backlog, or if no
      messages could be returned before the request timeout. For JSON, the
      response can be entirely empty. The Pub/Sub system may return fewer than
      the `maxMessages` requested even if there are more messages available in
      the backlog.
  """

  receivedMessages = _messages.MessageField('ReceivedMessage', 1, repeated=True)


class PushConfig(_messages.Message):
  r"""Configuration for a push delivery endpoint.

  Messages:
    AttributesValue: Optional. Endpoint configuration attributes that can be
      used to control different aspects of the message delivery. The only
      currently supported attribute is `x-goog-version`, which you can use to
      change the format of the pushed message. This attribute indicates the
      version of the data expected by the endpoint. This controls the shape of
      the pushed message (i.e., its fields and metadata). If not present
      during the `CreateSubscription` call, it will default to the version of
      the Pub/Sub API used to make such call. If not present in a
      `ModifyPushConfig` call, its value will not be changed.
      `GetSubscription` calls will always return a valid version, even if the
      subscription was created without this attribute. The only supported
      values for the `x-goog-version` attribute are: * `v1beta1`: uses the
      push format defined in the v1beta1 Pub/Sub API. * `v1` or `v1beta2`:
      uses the push format defined in the v1 Pub/Sub API. For example:
      `attributes { "x-goog-version": "v1" }`

  Fields:
    attributes: Optional. Endpoint configuration attributes that can be used
      to control different aspects of the message delivery. The only currently
      supported attribute is `x-goog-version`, which you can use to change the
      format of the pushed message. This attribute indicates the version of
      the data expected by the endpoint. This controls the shape of the pushed
      message (i.e., its fields and metadata). If not present during the
      `CreateSubscription` call, it will default to the version of the Pub/Sub
      API used to make such call. If not present in a `ModifyPushConfig` call,
      its value will not be changed. `GetSubscription` calls will always
      return a valid version, even if the subscription was created without
      this attribute. The only supported values for the `x-goog-version`
      attribute are: * `v1beta1`: uses the push format defined in the v1beta1
      Pub/Sub API. * `v1` or `v1beta2`: uses the push format defined in the v1
      Pub/Sub API. For example: `attributes { "x-goog-version": "v1" }`
    noWrapper: Optional. When set, the payload to the push endpoint is not
      wrapped.
    oidcToken: Optional. If specified, Pub/Sub will generate and attach an
      OIDC JWT token as an `Authorization` header in the HTTP request for
      every pushed message.
    pubsubWrapper: Optional. When set, the payload to the push endpoint is in
      the form of the JSON representation of a PubsubMessage (https://cloud.go
      ogle.com/pubsub/docs/reference/rpc/google.pubsub.v1#pubsubmessage).
    pushEndpoint: Optional. A URL locating the endpoint to which messages
      should be pushed. For example, a Webhook endpoint might use
      `https://example.com/push`.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class AttributesValue(_messages.Message):
    r"""Optional. Endpoint configuration attributes that can be used to
    control different aspects of the message delivery. The only currently
    supported attribute is `x-goog-version`, which you can use to change the
    format of the pushed message. This attribute indicates the version of the
    data expected by the endpoint. This controls the shape of the pushed
    message (i.e., its fields and metadata). If not present during the
    `CreateSubscription` call, it will default to the version of the Pub/Sub
    API used to make such call. If not present in a `ModifyPushConfig` call,
    its value will not be changed. `GetSubscription` calls will always return
    a valid version, even if the subscription was created without this
    attribute. The only supported values for the `x-goog-version` attribute
    are: * `v1beta1`: uses the push format defined in the v1beta1 Pub/Sub API.
    * `v1` or `v1beta2`: uses the push format defined in the v1 Pub/Sub API.
    For example: `attributes { "x-goog-version": "v1" }`

    Messages:
      AdditionalProperty: An additional property for a AttributesValue object.

    Fields:
      additionalProperties: Additional properties of type AttributesValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a AttributesValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  attributes = _messages.MessageField('AttributesValue', 1)
  noWrapper = _messages.MessageField('NoWrapper', 2)
  oidcToken = _messages.MessageField('OidcToken', 3)
  pubsubWrapper = _messages.MessageField('PubsubWrapper', 4)
  pushEndpoint = _messages.StringField(5)


class ReceivedMessage(_messages.Message):
  r"""A message and its corresponding acknowledgment ID.

  Fields:
    ackId: Optional. This ID can be used to acknowledge the received message.
    deliveryAttempt: Optional. The approximate number of times that Pub/Sub
      has attempted to deliver the associated message to a subscriber. More
      precisely, this is 1 + (number of NACKs) + (number of ack_deadline
      exceeds) for this message. A NACK is any call to ModifyAckDeadline with
      a 0 deadline. An ack_deadline exceeds event is whenever a message is not
      acknowledged within ack_deadline. Note that ack_deadline is initially
      Subscription.ackDeadlineSeconds, but may get extended automatically by
      the client library. Upon the first delivery of a given message,
      `delivery_attempt` will have a value of 1. The value is calculated at
      best effort and is approximate. If a DeadLetterPolicy is not set on the
      subscription, this will be 0.
    message: Optional. The message.
  """

  ackId = _messages.StringField(1)
  deliveryAttempt = _messages.IntegerField(2, variant=_messages.Variant.INT32)
  message = _messages.MessageField('PubsubMessage', 3)


class RetryPolicy(_messages.Message):
  r"""A policy that specifies how Pub/Sub retries message delivery. Retry
  delay will be exponential based on provided minimum and maximum backoffs.
  https://en.wikipedia.org/wiki/Exponential_backoff. RetryPolicy will be
  triggered on NACKs or acknowledgement deadline exceeded events for a given
  message. Retry Policy is implemented on a best effort basis. At times, the
  delay between consecutive deliveries may not match the configuration. That
  is, delay can be more or less than configured backoff.

  Fields:
    maximumBackoff: Optional. The maximum delay between consecutive deliveries
      of a given message. Value should be between 0 and 600 seconds. Defaults
      to 600 seconds.
    minimumBackoff: Optional. The minimum delay between consecutive deliveries
      of a given message. Value should be between 0 and 600 seconds. Defaults
      to 10 seconds.
  """

  maximumBackoff = _messages.StringField(1)
  minimumBackoff = _messages.StringField(2)


class RollbackSchemaRequest(_messages.Message):
  r"""Request for the `RollbackSchema` method.

  Fields:
    revisionId: Required. The revision ID to roll back to. It must be a
      revision of the same schema. Example: c7cfa2a8
  """

  revisionId = _messages.StringField(1)


class Schema(_messages.Message):
  r"""A schema resource.

  Enums:
    TypeValueValuesEnum: The type of the schema definition.

  Fields:
    definition: The definition of the schema. This should contain a string
      representing the full definition of the schema that is a valid schema
      definition of the type specified in `type`.
    name: Required. Name of the schema. Format is
      `projects/{project}/schemas/{schema}`.
    revisionCreateTime: Output only. The timestamp that the revision was
      created.
    revisionId: Output only. Immutable. The revision ID of the schema.
    type: The type of the schema definition.
  """

  class TypeValueValuesEnum(_messages.Enum):
    r"""The type of the schema definition.

    Values:
      TYPE_UNSPECIFIED: Default value. This value is unused.
      PROTOCOL_BUFFER: A Protocol Buffer schema definition.
      AVRO: An Avro schema definition.
    """
    TYPE_UNSPECIFIED = 0
    PROTOCOL_BUFFER = 1
    AVRO = 2

  definition = _messages.StringField(1)
  name = _messages.StringField(2)
  revisionCreateTime = _messages.StringField(3)
  revisionId = _messages.StringField(4)
  type = _messages.EnumField('TypeValueValuesEnum', 5)


class SchemaSettings(_messages.Message):
  r"""Settings for validating messages published against a schema.

  Enums:
    EncodingValueValuesEnum: Optional. The encoding of messages validated
      against `schema`.

  Fields:
    encoding: Optional. The encoding of messages validated against `schema`.
    firstRevisionId: Optional. The minimum (inclusive) revision allowed for
      validating messages. If empty or not present, allow any revision to be
      validated against last_revision or any revision created before.
    lastRevisionId: Optional. The maximum (inclusive) revision allowed for
      validating messages. If empty or not present, allow any revision to be
      validated against first_revision or any revision created after.
    schema: Required. The name of the schema that messages published should be
      validated against. Format is `projects/{project}/schemas/{schema}`. The
      value of this field will be `_deleted-schema_` if the schema has been
      deleted.
  """

  class EncodingValueValuesEnum(_messages.Enum):
    r"""Optional. The encoding of messages validated against `schema`.

    Values:
      ENCODING_UNSPECIFIED: Unspecified
      JSON: JSON encoding
      BINARY: Binary encoding, as defined by the schema type. For some schema
        types, binary encoding may not be available.
    """
    ENCODING_UNSPECIFIED = 0
    JSON = 1
    BINARY = 2

  encoding = _messages.EnumField('EncodingValueValuesEnum', 1)
  firstRevisionId = _messages.StringField(2)
  lastRevisionId = _messages.StringField(3)
  schema = _messages.StringField(4)


class SeekRequest(_messages.Message):
  r"""Request for the `Seek` method.

  Fields:
    snapshot: Optional. The snapshot to seek to. The snapshot's topic must be
      the same as that of the provided subscription. Format is
      `projects/{project}/snapshots/{snap}`.
    time: Optional. The time to seek to. Messages retained in the subscription
      that were published before this time are marked as acknowledged, and
      messages retained in the subscription that were published after this
      time are marked as unacknowledged. Note that this operation affects only
      those messages retained in the subscription (configured by the
      combination of `message_retention_duration` and
      `retain_acked_messages`). For example, if `time` corresponds to a point
      before the message retention window (or to a point before the system's
      notion of the subscription creation time), only retained messages will
      be marked as unacknowledged, and already-expunged messages will not be
      restored.
  """

  snapshot = _messages.StringField(1)
  time = _messages.StringField(2)


class SeekResponse(_messages.Message):
  r"""Response for the `Seek` method (this response is empty)."""


class SetIamPolicyRequest(_messages.Message):
  r"""Request message for `SetIamPolicy` method.

  Fields:
    policy: REQUIRED: The complete policy to be applied to the `resource`. The
      size of the policy is limited to a few 10s of KB. An empty policy is a
      valid policy but certain Google Cloud services (such as Projects) might
      reject them.
  """

  policy = _messages.MessageField('Policy', 1)


class Snapshot(_messages.Message):
  r"""A snapshot resource. Snapshots are used in
  [Seek](https://cloud.google.com/pubsub/docs/replay-overview) operations,
  which allow you to manage message acknowledgments in bulk. That is, you can
  set the acknowledgment state of messages in an existing subscription to the
  state captured by a snapshot.

  Messages:
    LabelsValue: Optional. See [Creating and managing labels]
      (https://cloud.google.com/pubsub/docs/labels).

  Fields:
    expireTime: Optional. The snapshot is guaranteed to exist up until this
      time. A newly-created snapshot expires no later than 7 days from the
      time of its creation. Its exact lifetime is determined at creation by
      the existing backlog in the source subscription. Specifically, the
      lifetime of the snapshot is `7 days - (age of oldest unacked message in
      the subscription)`. For example, consider a subscription whose oldest
      unacked message is 3 days old. If a snapshot is created from this
      subscription, the snapshot -- which will always capture this 3-day-old
      backlog as long as the snapshot exists -- will expire in 4 days. The
      service will refuse to create a snapshot that would expire in less than
      1 hour after creation.
    labels: Optional. See [Creating and managing labels]
      (https://cloud.google.com/pubsub/docs/labels).
    name: Optional. The name of the snapshot.
    topic: Optional. The name of the topic from which this snapshot is
      retaining messages.
  """

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. See [Creating and managing labels]
    (https://cloud.google.com/pubsub/docs/labels).

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  expireTime = _messages.StringField(1)
  labels = _messages.MessageField('LabelsValue', 2)
  name = _messages.StringField(3)
  topic = _messages.StringField(4)


class StandardQueryParameters(_messages.Message):
  r"""Query parameters accepted by all methods.

  Enums:
    FXgafvValueValuesEnum: V1 error format.
    AltValueValuesEnum: Data format for response.

  Fields:
    f__xgafv: V1 error format.
    access_token: OAuth access token.
    alt: Data format for response.
    callback: JSONP
    fields: Selector specifying which fields to include in a partial response.
    key: API key. Your API key identifies your project and provides you with
      API access, quota, and reports. Required unless you provide an OAuth 2.0
      token.
    oauth_token: OAuth 2.0 token for the current user.
    prettyPrint: Returns response with indentations and line breaks.
    quotaUser: Available to use for quota purposes for server-side
      applications. Can be any arbitrary string assigned to a user, but should
      not exceed 40 characters.
    trace: A tracing token of the form "token:<tokenid>" to include in api
      requests.
    uploadType: Legacy upload protocol for media (e.g. "media", "multipart").
    upload_protocol: Upload protocol for media (e.g. "raw", "multipart").
  """

  class AltValueValuesEnum(_messages.Enum):
    r"""Data format for response.

    Values:
      json: Responses with Content-Type of application/json
      media: Media download with context-dependent Content-Type
      proto: Responses with Content-Type of application/x-protobuf
    """
    json = 0
    media = 1
    proto = 2

  class FXgafvValueValuesEnum(_messages.Enum):
    r"""V1 error format.

    Values:
      _1: v1 error format
      _2: v2 error format
    """
    _1 = 0
    _2 = 1

  f__xgafv = _messages.EnumField('FXgafvValueValuesEnum', 1)
  access_token = _messages.StringField(2)
  alt = _messages.EnumField('AltValueValuesEnum', 3, default='json')
  callback = _messages.StringField(4)
  fields = _messages.StringField(5)
  key = _messages.StringField(6)
  oauth_token = _messages.StringField(7)
  prettyPrint = _messages.BooleanField(8, default=True)
  quotaUser = _messages.StringField(9)
  trace = _messages.StringField(10)
  uploadType = _messages.StringField(11)
  upload_protocol = _messages.StringField(12)


class Subscription(_messages.Message):
  r"""A subscription resource. If none of `push_config`, `bigquery_config`, or
  `cloud_storage_config` is set, then the subscriber will pull and ack
  messages using API methods. At most one of these fields may be set.

  Enums:
    StateValueValuesEnum: Output only. An output-only field indicating whether
      or not the subscription can receive messages.

  Messages:
    LabelsValue: Optional. See [Creating and managing
      labels](https://cloud.google.com/pubsub/docs/labels).

  Fields:
    ackDeadlineSeconds: Optional. The approximate amount of time (on a best-
      effort basis) Pub/Sub waits for the subscriber to acknowledge receipt
      before resending the message. In the interval after the message is
      delivered and before it is acknowledged, it is considered to be
      _outstanding_. During that time period, the message will not be
      redelivered (on a best-effort basis). For pull subscriptions, this value
      is used as the initial value for the ack deadline. To override this
      value for a given message, call `ModifyAckDeadline` with the
      corresponding `ack_id` if using non-streaming pull or send the `ack_id`
      in a `StreamingModifyAckDeadlineRequest` if using streaming pull. The
      minimum custom deadline you can specify is 10 seconds. The maximum
      custom deadline you can specify is 600 seconds (10 minutes). If this
      parameter is 0, a default value of 10 seconds is used. For push
      delivery, this value is also used to set the request timeout for the
      call to the push endpoint. If the subscriber never acknowledges the
      message, the Pub/Sub system will eventually redeliver the message.
    bigqueryConfig: Optional. If delivery to BigQuery is used with this
      subscription, this field is used to configure it.
    cloudStorageConfig: Optional. If delivery to Google Cloud Storage is used
      with this subscription, this field is used to configure it.
    deadLetterPolicy: Optional. A policy that specifies the conditions for
      dead lettering messages in this subscription. If dead_letter_policy is
      not set, dead lettering is disabled. The Pub/Sub service account
      associated with this subscriptions's parent project (i.e.,
      service-{project_number}@gcp-sa-pubsub.iam.gserviceaccount.com) must
      have permission to Acknowledge() messages on this subscription.
    detached: Optional. Indicates whether the subscription is detached from
      its topic. Detached subscriptions don't receive messages from their
      topic and don't retain any backlog. `Pull` and `StreamingPull` requests
      will return FAILED_PRECONDITION. If the subscription is a push
      subscription, pushes to the endpoint will not be made.
    enableExactlyOnceDelivery: Optional. If true, Pub/Sub provides the
      following guarantees for the delivery of a message with a given value of
      `message_id` on this subscription: * The message sent to a subscriber is
      guaranteed not to be resent before the message's acknowledgement
      deadline expires. * An acknowledged message will not be resent to a
      subscriber. Note that subscribers may still receive multiple copies of a
      message when `enable_exactly_once_delivery` is true if the message was
      published multiple times by a publisher client. These copies are
      considered distinct by Pub/Sub and have distinct `message_id` values.
    enableMessageOrdering: Optional. If true, messages published with the same
      `ordering_key` in `PubsubMessage` will be delivered to the subscribers
      in the order in which they are received by the Pub/Sub system.
      Otherwise, they may be delivered in any order.
    expirationPolicy: Optional. A policy that specifies the conditions for
      this subscription's expiration. A subscription is considered active as
      long as any connected subscriber is successfully consuming messages from
      the subscription or is issuing operations on the subscription. If
      `expiration_policy` is not set, a *default policy* with `ttl` of 31 days
      will be used. The minimum allowed value for `expiration_policy.ttl` is 1
      day. If `expiration_policy` is set, but `expiration_policy.ttl` is not
      set, the subscription never expires.
    filter: Optional. An expression written in the Pub/Sub [filter
      language](https://cloud.google.com/pubsub/docs/filtering). If non-empty,
      then only `PubsubMessage`s whose `attributes` field matches the filter
      are delivered on this subscription. If empty, then no messages are
      filtered out.
    labels: Optional. See [Creating and managing
      labels](https://cloud.google.com/pubsub/docs/labels).
    messageRetentionDuration: Optional. How long to retain unacknowledged
      messages in the subscription's backlog, from the moment a message is
      published. If `retain_acked_messages` is true, then this also configures
      the retention of acknowledged messages, and thus configures how far back
      in time a `Seek` can be done. Defaults to 7 days. Cannot be more than 7
      days or less than 10 minutes.
    name: Required. The name of the subscription. It must have the format
      `"projects/{project}/subscriptions/{subscription}"`. `{subscription}`
      must start with a letter, and contain only letters (`[A-Za-z]`), numbers
      (`[0-9]`), dashes (`-`), underscores (`_`), periods (`.`), tildes (`~`),
      plus (`+`) or percent signs (`%`). It must be between 3 and 255
      characters in length, and it must not start with `"goog"`.
    pubsubExportConfig: Optional. If delivery to Pub/Sub is used with this
      subscription, this field is used to configure it.
    pubsubliteExportConfig: Optional. If delivery to Pub/Sub Lite is used with
      this subscription, this field is used to configure it.
    pushConfig: Optional. If push delivery is used with this subscription,
      this field is used to configure it.
    retainAckedMessages: Optional. Indicates whether to retain acknowledged
      messages. If true, then messages are not expunged from the
      subscription's backlog, even if they are acknowledged, until they fall
      out of the `message_retention_duration` window. This must be true if you
      would like to [`Seek` to a timestamp]
      (https://cloud.google.com/pubsub/docs/replay-overview#seek_to_a_time) in
      the past to replay previously-acknowledged messages.
    retryPolicy: Optional. A policy that specifies how Pub/Sub retries message
      delivery for this subscription. If not set, the default retry policy is
      applied. This generally implies that messages will be retried as soon as
      possible for healthy subscribers. RetryPolicy will be triggered on NACKs
      or acknowledgement deadline exceeded events for a given message.
    state: Output only. An output-only field indicating whether or not the
      subscription can receive messages.
    topic: Required. The name of the topic from which this subscription is
      receiving messages. Format is `projects/{project}/topics/{topic}`. The
      value of this field will be `_deleted-topic_` if the topic has been
      deleted.
    topicMessageRetentionDuration: Output only. Indicates the minimum duration
      for which a message is retained after it is published to the
      subscription's topic. If this field is set, messages published to the
      subscription's topic in the last `topic_message_retention_duration` are
      always available to subscribers. See the `message_retention_duration`
      field in `Topic`. This field is set only in responses from the server;
      it is ignored if it is set in any requests.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. An output-only field indicating whether or not the
    subscription can receive messages.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      ACTIVE: The subscription can actively receive messages
      RESOURCE_ERROR: The subscription cannot receive messages because of an
        error with the resource to which it pushes messages. See the more
        detailed error state in the corresponding configuration.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    RESOURCE_ERROR = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. See [Creating and managing
    labels](https://cloud.google.com/pubsub/docs/labels).

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  ackDeadlineSeconds = _messages.IntegerField(1, variant=_messages.Variant.INT32)
  bigqueryConfig = _messages.MessageField('BigQueryConfig', 2)
  cloudStorageConfig = _messages.MessageField('CloudStorageConfig', 3)
  deadLetterPolicy = _messages.MessageField('DeadLetterPolicy', 4)
  detached = _messages.BooleanField(5)
  enableExactlyOnceDelivery = _messages.BooleanField(6)
  enableMessageOrdering = _messages.BooleanField(7)
  expirationPolicy = _messages.MessageField('ExpirationPolicy', 8)
  filter = _messages.StringField(9)
  labels = _messages.MessageField('LabelsValue', 10)
  messageRetentionDuration = _messages.StringField(11)
  name = _messages.StringField(12)
  pubsubExportConfig = _messages.MessageField('PubSubExportConfig', 13)
  pubsubliteExportConfig = _messages.MessageField('PubSubLiteExportConfig', 14)
  pushConfig = _messages.MessageField('PushConfig', 15)
  retainAckedMessages = _messages.BooleanField(16)
  retryPolicy = _messages.MessageField('RetryPolicy', 17)
  state = _messages.EnumField('StateValueValuesEnum', 18)
  topic = _messages.StringField(19)
  topicMessageRetentionDuration = _messages.StringField(20)


class TestIamPermissionsRequest(_messages.Message):
  r"""Request message for `TestIamPermissions` method.

  Fields:
    permissions: The set of permissions to check for the `resource`.
      Permissions with wildcards (such as `*` or `storage.*`) are not allowed.
      For more information see [IAM
      Overview](https://cloud.google.com/iam/docs/overview#permissions).
  """

  permissions = _messages.StringField(1, repeated=True)


class TestIamPermissionsResponse(_messages.Message):
  r"""Response message for `TestIamPermissions` method.

  Fields:
    permissions: A subset of `TestPermissionsRequest.permissions` that the
      caller is allowed.
  """

  permissions = _messages.StringField(1, repeated=True)


class TextConfig(_messages.Message):
  r"""Configuration for writing message data in text format. Message payloads
  will be written to files as raw text, separated by a newline.
  """



class Topic(_messages.Message):
  r"""A topic resource.

  Enums:
    StateValueValuesEnum: Output only. An output-only field indicating the
      state of the topic.

  Messages:
    LabelsValue: Optional. See [Creating and managing labels]
      (https://cloud.google.com/pubsub/docs/labels).

  Fields:
    ingestionDataSourceSettings: Optional. Settings for managed ingestion from
      a data source into this topic.
    kmsKeyName: Optional. The resource name of the Cloud KMS CryptoKey to be
      used to protect access to messages published on this topic. The expected
      format is `projects/*/locations/*/keyRings/*/cryptoKeys/*`.
    labels: Optional. See [Creating and managing labels]
      (https://cloud.google.com/pubsub/docs/labels).
    messageRetentionDuration: Optional. Indicates the minimum duration to
      retain a message after it is published to the topic. If this field is
      set, messages published to the topic in the last
      `message_retention_duration` are always available to subscribers. For
      instance, it allows any attached subscription to [seek to a
      timestamp](https://cloud.google.com/pubsub/docs/replay-
      overview#seek_to_a_time) that is up to `message_retention_duration` in
      the past. If this field is not set, message retention is controlled by
      settings on individual subscriptions. Cannot be more than 31 days or
      less than 10 minutes.
    messageStoragePolicy: Optional. Policy constraining the set of Google
      Cloud Platform regions where messages published to the topic may be
      stored. If not present, then no constraints are in effect.
    name: Required. The name of the topic. It must have the format
      `"projects/{project}/topics/{topic}"`. `{topic}` must start with a
      letter, and contain only letters (`[A-Za-z]`), numbers (`[0-9]`), dashes
      (`-`), underscores (`_`), periods (`.`), tildes (`~`), plus (`+`) or
      percent signs (`%`). It must be between 3 and 255 characters in length,
      and it must not start with `"goog"`.
    satisfiesPzs: Optional. Reserved for future use. This field is set only in
      responses from the server; it is ignored if it is set in any requests.
    schemaSettings: Optional. Settings for validating messages published
      against a schema.
    state: Output only. An output-only field indicating the state of the
      topic.
  """

  class StateValueValuesEnum(_messages.Enum):
    r"""Output only. An output-only field indicating the state of the topic.

    Values:
      STATE_UNSPECIFIED: Default value. This value is unused.
      ACTIVE: The topic does not have any persistent errors.
      INGESTION_RESOURCE_ERROR: Ingestion from the data source has encountered
        a permanent error. See the more detailed error state in the
        corresponding ingestion source configuration.
    """
    STATE_UNSPECIFIED = 0
    ACTIVE = 1
    INGESTION_RESOURCE_ERROR = 2

  @encoding.MapUnrecognizedFields('additionalProperties')
  class LabelsValue(_messages.Message):
    r"""Optional. See [Creating and managing labels]
    (https://cloud.google.com/pubsub/docs/labels).

    Messages:
      AdditionalProperty: An additional property for a LabelsValue object.

    Fields:
      additionalProperties: Additional properties of type LabelsValue
    """

    class AdditionalProperty(_messages.Message):
      r"""An additional property for a LabelsValue object.

      Fields:
        key: Name of the additional property.
        value: A string attribute.
      """

      key = _messages.StringField(1)
      value = _messages.StringField(2)

    additionalProperties = _messages.MessageField('AdditionalProperty', 1, repeated=True)

  ingestionDataSourceSettings = _messages.MessageField('IngestionDataSourceSettings', 1)
  kmsKeyName = _messages.StringField(2)
  labels = _messages.MessageField('LabelsValue', 3)
  messageRetentionDuration = _messages.StringField(4)
  messageStoragePolicy = _messages.MessageField('MessageStoragePolicy', 5)
  name = _messages.StringField(6)
  satisfiesPzs = _messages.BooleanField(7)
  schemaSettings = _messages.MessageField('SchemaSettings', 8)
  state = _messages.EnumField('StateValueValuesEnum', 9)


class UpdateSnapshotRequest(_messages.Message):
  r"""Request for the UpdateSnapshot method.

  Fields:
    snapshot: Required. The updated snapshot object.
    updateMask: Required. Indicates which fields in the provided snapshot to
      update. Must be specified and non-empty.
  """

  snapshot = _messages.MessageField('Snapshot', 1)
  updateMask = _messages.StringField(2)


class UpdateSubscriptionRequest(_messages.Message):
  r"""Request for the UpdateSubscription method.

  Fields:
    subscription: Required. The updated subscription object.
    updateMask: Required. Indicates which fields in the provided subscription
      to update. Must be specified and non-empty.
  """

  subscription = _messages.MessageField('Subscription', 1)
  updateMask = _messages.StringField(2)


class UpdateTopicRequest(_messages.Message):
  r"""Request for the UpdateTopic method.

  Fields:
    topic: Required. The updated topic object.
    updateMask: Required. Indicates which fields in the provided topic to
      update. Must be specified and non-empty. Note that if `update_mask`
      contains "message_storage_policy" but the `message_storage_policy` is
      not set in the `topic` provided above, then the updated value is
      determined by the policy configured at the project or organization
      level.
  """

  topic = _messages.MessageField('Topic', 1)
  updateMask = _messages.StringField(2)


class ValidateMessageRequest(_messages.Message):
  r"""Request for the `ValidateMessage` method.

  Enums:
    EncodingValueValuesEnum: The encoding expected for messages

  Fields:
    encoding: The encoding expected for messages
    message: Message to validate against the provided `schema_spec`.
    name: Name of the schema against which to validate. Format is
      `projects/{project}/schemas/{schema}`.
    schema: Ad-hoc schema against which to validate
  """

  class EncodingValueValuesEnum(_messages.Enum):
    r"""The encoding expected for messages

    Values:
      ENCODING_UNSPECIFIED: Unspecified
      JSON: JSON encoding
      BINARY: Binary encoding, as defined by the schema type. For some schema
        types, binary encoding may not be available.
    """
    ENCODING_UNSPECIFIED = 0
    JSON = 1
    BINARY = 2

  encoding = _messages.EnumField('EncodingValueValuesEnum', 1)
  message = _messages.BytesField(2)
  name = _messages.StringField(3)
  schema = _messages.MessageField('Schema', 4)


class ValidateMessageResponse(_messages.Message):
  r"""Response for the `ValidateMessage` method. Empty for now."""


class ValidateSchemaRequest(_messages.Message):
  r"""Request for the `ValidateSchema` method.

  Fields:
    schema: Required. The schema object to validate.
  """

  schema = _messages.MessageField('Schema', 1)


class ValidateSchemaResponse(_messages.Message):
  r"""Response for the `ValidateSchema` method. Empty for now."""


encoding.AddCustomJsonFieldMapping(
    StandardQueryParameters, 'f__xgafv', '$.xgafv')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_1', '1')
encoding.AddCustomJsonEnumMapping(
    StandardQueryParameters.FXgafvValueValuesEnum, '_2', '2')
